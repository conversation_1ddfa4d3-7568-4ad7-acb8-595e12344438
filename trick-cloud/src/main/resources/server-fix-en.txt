errAdminRequired=Administrator privileges required
errBadColumnName=Invalid column name "{0}"
errBadHttpMethod=Unsupported Http method: {0}
errBadIP=IP format is incorrect: "{0}"
errBadReqBodyJson=JSON format error: {0}
errBinHasContainerNotToLoad=Container {1} already exists on bin {0}, container {2} cannot be placed again
errBinNoContainer=There are currently no containers on bin "{0}"
errBinNoRobot=Bin "{0}" has not configured robot position parameters
errBinNotEmpty=Bin {} must be empty and unoccupied
errBpNoChild=Component not configured child component "{0}"
errBpRunError=Component "{1}" running error: {0}
errBzError=Business exception: {0}
errBzMaterialCategoryNoStoreDistricts=Material category "{0}" Unconfigured storage area
errBzMaterialNoCategory=Item "{0}" has no category configured
errBzMissingKeyParam=Missing required parameter: "{0}"
errBzNoActiveBomForMat=Cannot find material "{0}" Activated Bill of Material
errBzNoEnoughContainerForMaterial=Insufficient empty containers for material "{0}
errBzNoEnoughEmptyContainer=Insufficient empty containers (expected 
errBzNoMaterialById=Material not found "{0}"
errBzNoMaterialCategoryById=Cannot find material category "{0}"
errBzNoMaterialContainerMaxQty=Material "{0}" has no container capacity Configured
errBzNoMaterialContainerMaxQty2=Material "{0}" is not Configured with a"valid "container capacity
errBzNoSuchOrderNameId=Cannot find order "{0 }" / "{ 1}"
errBzOutboundLineShort=Line {0}, material "{2}", inventory is missing {1} items
errCodeErr=Program error: {0}
errColumnStartGtColumnEnd=The starting column number cannot be greater than the ending column number
errComplexQueryBadOp=Unsupported operator "{0}"
errComplexQueryInMultiple=Query requires specifying multiple values
errComplexQueryMissingField1=Missing field
errComplexQueryMissingOp=Query requires operators
errComplexQueryUnknownType=Unsupported query type "{0}"
errComplexQueryValueNeedTwo=Field "{0}" requires two query values
errComplexQueryValueNotString=Field "{0}" query value is not string
errComplexQueryValueNull=Field "{0}" query value is empty
errContainerBadCurrentBin=Container {0} is currently in bin {1}, not in {2}.
errContainerLocked=Container "{0}" may be in use, please try another container (lock 
errContainerNoBin=Container "{0}" current bin is empty
errContainerNoBinMissingFromBin=The current bin of container {0} cannot be queried. You must specify a bin as the starting point
errContainerNoType=Container "{0}" has no type set
errContainerOrFromBinSpecified=Specify at least a container or starting bin
errContainerTypeNoStoreDistricts=Storage area not Configured for container type "{0}"
errDbProcessing=Processing database, please try again later
errDbRestoreNoInfo=Incorrect data file (info.txt missing)
errDepthStartGtDepthEnd=The starting depth cannot be greater than the ending depth
errDingBadConfig=DingTalk callback no code
errDingCallErr=DingTalk request failed with code 
errDingNotEnabled=DingTalk is not enabled
errDingShuNoCode=Callback no code
errDirFilesNull=Failed to list files under the directory
errDirNotExists=Directory does not exist: {0}
errDirectOutboundSubmitFail=Direct outbound commit failed. Reason: {0}
errDirectorOutboundBinNotOccupied=Bin {0} is empty. (Direct outbound)
errDirectorOutboundEmptyLayouts=The inventory details that can be shipped out are empty. (Direct outbound)
errDirectorOutboundNoOrder=Direct outbound not configure receive order
errDistrictIdsEmpty=At least one storage area must be specified
errDuplicatedKeyError=The "{1}" field value of "{0}" cannot be duplicated, duplicate value 
errEmptyEntityValue=Business Object "{0}" is null
errEmptyPassword=Password cannot be empty
errEntityNoField=Business Object "{0}" has no field "{1}"
errEntityRequestMissingIdOrQueryParam=Need to specify "id" or "query" parameter
errEntityRequestMissingQueryParam=Query requires specifying the "query" parameter
errFSNotEnabled=Feishu not enabled
errFalconBlockInputParamNotList=Component "{0}" input parameter "{1}" needs to be an array
errFalconBlockInputParamRangeError=Component "{0}" input parameter "{1}" range error
errFalconBlockOptionParamError=Component "{0}" option parameter "{1}" error
errFalconCreateTaskNoDefId=Unable to create task, no template specified
errFalconExpressionError=Expression evaluation error. Expression "{0}". Details: {1}
errFalconMissingBlockInputParam=Component "{0}" is missing input parameter "{1}"
errFalconThrowPrefix=Error: {0}
errFeiShuBadConfig=Feishu login configuration is incomplete
errFeiShuCallErr=Feishu request failed, code 
errFeiShuNoCode=Feishu callback no code
errFieldTextTooLong=Field "{0}. {1}" content length {2} exceeds maximum limit {3}
errFileNotDirectory=File is not a directory: {0}
errFileNotExists=File does not exist: {0}
errFileNotInDir=File "{0}" is not in directory "{1}"
errGwNoRobot=Robot "{0}" not configured in gateway
errHttpFail=HTTP request failed, HTTP response code 
errHttpResponseBodyEmpty=HTTP request response is empty
errInitializeScheduler=Scheduled task initialization failed!"{0}"
errInterruptedException=Execution was interrupted
errInvLayoutNoContainer=No container {0} on inventory layout
errInvShort=Insufficient inventory: {0}
errInvShort2=Insufficient inventory, material {0}, waiting to be released from stock
errLayerStartGtLayerEnd=The starting layer number cannot be greater than the ending layer number
errLockBinFailed=Failed to lock location "{0}", it has already been locked before
errMissingHttpPathParam=Missing path parameter: {0}
errMissingHttpQueryParam=Missing HTTP query parameter: {0}
errMissingIdField=Business Object {1} ({0}) lacks id field
errMissingParam=Missing parameter: {0}
errMrNoCurrentOrder=Machine "{0}" currently has no waybill
errMrNoCurrentStep=Robot "{0}" currently has no waybill steps being executed
errMrNoOrder=Waybill does not exist or has been executed "{0}"
errMrUpdateOrderBadStatus=The waybill is no longer allowed to be updated. waybill 
errNoBin=No bin "{0}" found
errNoBinById=Bin {0} not found
errNoBinRobotArgs=Missing operation parameters, bin 
errNoBpType=Component "{0}" not found
errNoConfig=Configuration not specified
errNoDistrictById=District {0} not found
errNoEmptyBinInDistrict=There are no empty bins
errNoFalconTaskDefById=Task template not found, template id 
errNoJsonNode=There is no JsonNode.
errNoMongoDB=No MongoDB is available
errNoOrderLine=Lack order line, order ID
errNoRoutePath=No reachable path found, from {0} to {1}
errNoScriptFunctionProvided=No script function specified
errNoSqlDb=SQL data source is not available
errNoSuchContainerById=Container "{0}" not found
errNoSuchContainerTypeById=Could not find container type "{0}"
errNoSuchDistrictById=Cannot find storage area "{0}"
errNoSuchEntity=Business Object "{0}" not found
errNoSuchScriptFunction=Script function "{0}" not found
errNoSuchUserById=Unable to find user. id 
errNoTaskDefByLabel=Task template not found, template name 
errNoUiDir=Cannot find ui directory: {0}
errNoUpOrderConfig=Cannot find upstream order configuration, upstream order
errNoUploadedFile=The uploaded file should be placed in the form field: "{0}"
errNoUserUsername=Unable to find user. Username 
errNoWidthTooSmall=Number width is too small
errOrderNoPushConfig=Lack reveive order config: {0}
errPasswordNotMatch=Password error
errRecoverBadExternalCallAsync=Only canceled or terminated asynchronous calls can be retried
errRefFieldNoRefEntity=Field "{0}" not configured "references Business Object
errRetryFailedRobotButOrderNotExecuting=Restart the faulty robot, but the current waybill {0} status is not in progress, but {1}.
errRobotNotFailed=Robot "{0}" is not malfunctioning
errRowStartGtRowEnd=The starting queue number cannot be greater than the ending queue number.
errScriptEntityRw=Script Business Object read and write error: {0}
errScriptExt=Script error: {0}
errScriptReturnNotString=Script function "{0}" returns value not string: {1}
errSetToStatementInvalidJavaClass=setToStatement unsupported parameter type "{0}"
errSignInNoPassword=Password required
errSimpleScriptBp=Failed to execute simple script: {0}
errSingletonEntityBadOp=Singleton Business Object "{0}" cannot call "{1}"
errSqlEmptyWhereNotAllowed=Prohibit all updates
errSqlUniqueConstraintViolation=Value is repeated. Value
errSyncKeyMultiple=Multivalued field "{0}" cannot be used as a business primary key
errSyncKeyNotUniqInCurrent=In the original Business Object list, the key is not unique: "{0}"
errSyncKeyNotUniqInNew=In the new Business Object list, the key is not unique: "{0}"
errTakeOffContainerBadBin=Required to remove container "{0}" from bin "{1}", but the system records that the container is currently in bin "{2}"
errTakeOffContainerBadContainer=To unshelf container "{1}" from bin "{0}", but the system records that the container on bin is {2}
errTakeOffContainerContainerBinAtLeastOne=Specify container or bin when take off container from port.
errTakeOffContainerNoBin=Required to unshelf container "{0}" from bin "{1}", but the system record container is not currently on any bin
errTypesNotEnoughForHeaders=Insufficient number of types
errUnbindBinContainerBadBind=Attempted to unbind bin "{0}" and container "{1}", but the current container on bin is "{2}"
errUnsupportedHttpRequestMethod=HTTP method error "{0}"
errUserDisabled=Account has been disabled
errWsNotConnected=Not connected [Websocket {0}, status 
errWsSendFail=Send failed [Websocket {0}]
errWsSendTimeout=Send interrupted [Websocket {0}]
errWwxBadConfig=WeCom login configuration is incomplete
errWwxCallErr=WeCom request failed with code 
errWwxNoCode=WeCom callback no code
errWwxNotEnabled=WeCom is not enabled
errXlsFormulaNotSupported=Excel formula not supported
Falcon_BlockGroup_Basic=Basic components
Falcon_BlockGroup_Bin=Bin
Falcon_BlockGroup_BinContainer=Container bin
Falcon_BlockGroup_Container=Container
Falcon_BlockGroup_ContainerTransport=Container transport order
Falcon_BlockGroup_Cq=ComplexQuery
Falcon_BlockGroup_CustomBlocks=Custom components
Falcon_BlockGroup_Entity=Business Objects
Falcon_BlockGroup_Inv=Inventory
Falcon_Bp_AbortTaskBp_Input_msg_description=Termination of Falcon Mission Reason
Falcon_Bp_AbortTaskBp_Input_msg_label=Error message
Falcon_Bp_AbortTaskBp_description=Terminate the Falcon mission
Falcon_Bp_AbortTaskBp_label=Terminate the task
Falcon_Bp_BindBinContainerBp_Input_binId_description=Bin ID
Falcon_Bp_BindBinContainerBp_Input_binId_label=Bin ID
Falcon_Bp_BindBinContainerBp_Input_containerId_label=Container ID
Falcon_Bp_BindBinContainerBp_Input_unlockBin_description=Need to unlock bin synchronously
Falcon_Bp_BindBinContainerBp_Input_unlockBin_label=Unlock bin
Falcon_Bp_BindBinContainerBp_Input_unlockContainer_description=Need to unlock container synchronously
Falcon_Bp_BindBinContainerBp_Input_unlockContainer_label=Unlock the container
Falcon_Bp_BindBinContainerBp_description=Bind the bin with the container
Falcon_Bp_BindBinContainerBp_label=Bind repository container
Falcon_Bp_CreateInvFromOrderBp_Input_bin_label=Bin
Falcon_Bp_CreateInvFromOrderBp_Input_container_description=Container
Falcon_Bp_CreateInvFromOrderBp_Input_container_label=Container
Falcon_Bp_CreateInvFromOrderBp_Input_copyFields_description=These fields will be copied to inventory layout.Split with "," when multiple fields.
Falcon_Bp_CreateInvFromOrderBp_Input_copyFields_label=Copy fields
Falcon_Bp_CreateInvFromOrderBp_Input_lotNoFormat_label=LotNo format
Falcon_Bp_CreateInvFromOrderBp_Input_materialFieldName_description=Please enter the field name that represents the material on the order
Falcon_Bp_CreateInvFromOrderBp_Input_materialFieldName_label=Material field
Falcon_Bp_CreateInvFromOrderBp_Input_order_description=Please enter a order object
Falcon_Bp_CreateInvFromOrderBp_Input_order_label=Order entity value
Falcon_Bp_CreateInvFromOrderBp_Input_qtyFieldName_description=Please enter the field name indicating the quantity on the order
Falcon_Bp_CreateInvFromOrderBp_Input_qtyFieldName_label=Quantity field
Falcon_Bp_CreateInvFromOrderBp_Input_state_description=There are three default inventory status: Received: received; Storing: in storage; Assigned: allocated.
Falcon_Bp_CreateInvFromOrderBp_Input_state_label=Inventory status
Falcon_Bp_CreateInvFromOrderBp_description=Create inventory information from order
Falcon_Bp_CreateInvFromOrderBp_label=Create inventory from order
Falcon_Bp_CreateInvFromOrderLinesBp_Input_bin_label=Bin
Falcon_Bp_CreateInvFromOrderLinesBp_Input_container_label=Container
Falcon_Bp_CreateInvFromOrderLinesBp_Input_copyFields_description=Multiple fields are separated by "," and these fields and corresponding data will be copied to the inventory information.
Falcon_Bp_CreateInvFromOrderLinesBp_Input_copyFields_label=Copy the following fields from a single line
Falcon_Bp_CreateInvFromOrderLinesBp_Input_materialFieldName_description=Please enter the field name that represents the material on order line.
Falcon_Bp_CreateInvFromOrderLinesBp_Input_materialFieldName_label=Material field name on order line
Falcon_Bp_CreateInvFromOrderLinesBp_Input_order_description=Please enter a order object
Falcon_Bp_CreateInvFromOrderLinesBp_Input_order_label=Order entity value
Falcon_Bp_CreateInvFromOrderLinesBp_Input_qtyFieldName_description=Please enter the field name that represents the quantity on order line.
Falcon_Bp_CreateInvFromOrderLinesBp_Input_qtyFieldName_label=Quantity field name on order line
Falcon_Bp_CreateInvFromOrderLinesBp_Input_state_description=There are three default Inventory status: Received: received; Storing: in storage; Assigned: allocated.
Falcon_Bp_CreateInvFromOrderLinesBp_Input_state_label=Inventory status
Falcon_Bp_CreateInvFromOrderLinesBp_description=Create inventory information based on single-line information
Falcon_Bp_CreateInvFromOrderLinesBp_label=Create inventory from order line
Falcon_Bp_CreateTraceContainerBp_Input_containerType_description=Please enter the container type recorded by M4.
Falcon_Bp_CreateTraceContainerBp_Input_containerType_label=Container type
Falcon_Bp_CreateTraceContainerBp_Output_containerId_description=The newly created container ID.
Falcon_Bp_CreateTraceContainerBp_Output_containerId_label=Container ID.
Falcon_Bp_CreateTraceContainerBp_description=Create a tracking container.
Falcon_Bp_CreateTraceContainerBp_label=Create a tracking container
Falcon_Bp_DelayBp_Input_timeMillis_description=Delay time (milliseconds)
Falcon_Bp_DelayBp_Input_timeMillis_label=Time (milliseconds)
Falcon_Bp_DelayBp_description=Used to delay the execution of subsequent modules, the delay depends on the input parameters
Falcon_Bp_DelayBp_label=Delay
Falcon_Bp_ExpressionBp_Input_expression_description=Expression
Falcon_Bp_ExpressionBp_Input_expression_label=Expression
Falcon_Bp_ExpressionBp_Output_expResult_description=Expresult description
Falcon_Bp_ExpressionBp_Output_expResult_label=Expresult label
Falcon_Bp_ExpressionBp_description=Calculate the result based on the input expression
Falcon_Bp_ExpressionBp_label=Evaluation of expressions
Falcon_Bp_FindEmptyContainerBp_Input_districtIds_description=Storage area ID. Please enter the storage area number recorded by M4 according to your requirements.
Falcon_Bp_FindEmptyContainerBp_Input_districtIds_label=Storage area ID
Falcon_Bp_FindEmptyContainerBp_Input_keepTrying_description=Disable: (default) only searches once; Enable: searches continuously until an empty container is found.
Falcon_Bp_FindEmptyContainerBp_Input_keepTrying_label=Retry until successful
Falcon_Bp_FindEmptyContainerBp_Input_sort_description=The sorting method of storage locations within the target warehouse area.
Falcon_Bp_FindEmptyContainerBp_Input_sort_label=Sorting
Falcon_Bp_FindEmptyContainerBp_Output_binId_description=The number of the bin where the target empty container is stored.
Falcon_Bp_FindEmptyContainerBp_Output_binId_label=Bin ID
Falcon_Bp_FindEmptyContainerBp_Output_containerId_description=The ID of the empty container found.
Falcon_Bp_FindEmptyContainerBp_Output_containerId_label=Container ID
Falcon_Bp_FindEmptyContainerBp_Output_found_description=True: Found; false: Not found.
Falcon_Bp_FindEmptyContainerBp_Output_found_label=Found empty container
Falcon_Bp_FindEmptyContainerBp_description=According to the desired rules, find empty containers from the target storage area and lock this container.
Falcon_Bp_FindEmptyContainerBp_label=Find empty container
Falcon_Bp_FindFieldValueByIdBp_Input_entityName_label=Business Object Name
Falcon_Bp_FindFieldValueByIdBp_Input_field_description=Specify the fields for the query
Falcon_Bp_FindFieldValueByIdBp_Input_field_label=Field
Falcon_Bp_FindFieldValueByIdBp_Input_id_label=Business Object ID
Falcon_Bp_FindFieldValueByIdBp_Output_found_description=Record found
Falcon_Bp_FindFieldValueByIdBp_Output_found_label=Business Object Existence
Falcon_Bp_FindFieldValueByIdBp_Output_value_description=Query field value
Falcon_Bp_FindFieldValueByIdBp_Output_value_label=Value
Falcon_Bp_FindFieldValueByIdBp_description=Find the specified field of the Business Object entity by ID
Falcon_Bp_FindFieldValueByIdBp_label=Find fields of Business Object by ID
Falcon_Bp_FindNotOccupiedBinBp_Input_cq_description=Conditions for finding empty bins
Falcon_Bp_FindNotOccupiedBinBp_Input_cq_label=Conditions
Falcon_Bp_FindNotOccupiedBinBp_Input_districtIds_label=District list
Falcon_Bp_FindNotOccupiedBinBp_Input_keepTrying_description=When it is true, repeat the search for empty bin until successful. If it is false, only search once
Falcon_Bp_FindNotOccupiedBinBp_Input_keepTrying_label=Repeated attempts lead to success
Falcon_Bp_FindNotOccupiedBinBp_Input_sort_description=Sorting rules
Falcon_Bp_FindNotOccupiedBinBp_Input_sort_label=Sorting
Falcon_Bp_FindNotOccupiedBinBp_Output_binId_description=ID of the bin found
Falcon_Bp_FindNotOccupiedBinBp_Output_binId_label=Bin ID
Falcon_Bp_FindNotOccupiedBinBp_Output_found_description=True means found, false means not found
Falcon_Bp_FindNotOccupiedBinBp_Output_found_label=Whether to find
Falcon_Bp_FindNotOccupiedBinBp_description=Look for empty bins in storage areas
Falcon_Bp_FindNotOccupiedBinBp_label=Find not occupied bin
Falcon_Bp_FindOneBp_Input_entityName_label=Entity
Falcon_Bp_FindOneBp_Input_field_label=Field
Falcon_Bp_FindOneBp_Input_value_label=Value
Falcon_Bp_FindOneBp_label=Check a single record
Falcon_Bp_FindOneEntityByIdBp_Input_entityName_description=EntityName
Falcon_Bp_FindOneEntityByIdBp_Input_entityName_label=EntityName
Falcon_Bp_FindOneEntityByIdBp_Input_id_description=Business Object entity ID
Falcon_Bp_FindOneEntityByIdBp_Input_id_label=Record ID
Falcon_Bp_FindOneEntityByIdBp_Output_ev_description=Business Object Entity Details
Falcon_Bp_FindOneEntityByIdBp_Output_ev_label=Business objects
Falcon_Bp_FindOneEntityByIdBp_Output_found_description=Whether to find
Falcon_Bp_FindOneEntityByIdBp_Output_found_label=Whether to find
Falcon_Bp_FindOneEntityByIdBp_description=Find business objects by ID
Falcon_Bp_FindOneEntityByIdBp_label=Find business objects by ID
Falcon_Bp_GetBinContainerBp_Input_binId_description=Bin ID
Falcon_Bp_GetBinContainerBp_Input_binId_label=Bin ID
Falcon_Bp_GetBinContainerBp_Output_binEmpty_description=Whether the container is empty, true is empty
Falcon_Bp_GetBinContainerBp_Output_binEmpty_label=Is the bin empty?
Falcon_Bp_GetBinContainerBp_Output_containerId_description=Container ID
Falcon_Bp_GetBinContainerBp_Output_containerId_label=Container ID
Falcon_Bp_GetBinContainerBp_description=Retrieve the container from the storage location, provided that the previous storage location is bound to the container. If it cannot be retrieved, the container number will return null
Falcon_Bp_GetBinContainerBp_label=Retrieve containers from the bin
Falcon_Bp_GetContainerBinBp_Input_containerId_description=Container ID
Falcon_Bp_GetContainerBinBp_Input_containerId_label=Container ID
Falcon_Bp_GetContainerBinBp_Output_binId_description=Bin ID
Falcon_Bp_GetContainerBinBp_Output_binId_label=Bin ID
Falcon_Bp_GetContainerBinBp_Output_found_description=Found or not, true means found
Falcon_Bp_GetContainerBinBp_Output_found_label=Whether to find
Falcon_Bp_GetContainerBinBp_description=Retrieve the bin of the container, provided that the previous bin is bound to the container. If it cannot be retrieved, the bin ID will return null
Falcon_Bp_GetContainerBinBp_label=Retrieve the bin of the container
Falcon_Bp_GetContainerInvBp_Input_containerId_description=Please enter the container ID recorded in M4.
Falcon_Bp_GetContainerInvBp_Input_containerId_label=Container ID
Falcon_Bp_GetContainerInvBp_Output_found_description=True: Found; false: Not found.
Falcon_Bp_GetContainerInvBp_Output_found_label=Whether to find
Falcon_Bp_GetContainerInvBp_Output_inv_description=Inventory details.
Falcon_Bp_GetContainerInvBp_Output_inv_label=Inventory details
Falcon_Bp_GetContainerInvBp_description=Get the corresponding inventory details by container ID.
Falcon_Bp_GetContainerInvBp_label=Get inventory details by container
Falcon_Bp_GetContainerTypeStoreDistrictsBp_Input_containerId_description=Please enter the ID of the container recorded by M4.
Falcon_Bp_GetContainerTypeStoreDistrictsBp_Input_containerId_label=Container ID.
Falcon_Bp_GetContainerTypeStoreDistrictsBp_Output_storeDistricts_description=Collection of storage area names, array.
Falcon_Bp_GetContainerTypeStoreDistrictsBp_Output_storeDistricts_label=Storage area
Falcon_Bp_GetContainerTypeStoreDistrictsBp_description=According to the container ID, query the name of the storage area where such containers can be stored.
Falcon_Bp_GetContainerTypeStoreDistrictsBp_label=Storage area for querying the type of container
Falcon_Bp_IfBp_Input_condition_description=Judgment condition
Falcon_Bp_IfBp_Input_condition_label=Conditions
Falcon_Bp_IfBp_description=Conditional judgment can only continue to execute submodules when the conditions are met
Falcon_Bp_IfBp_label=IF
Falcon_Bp_IfElseBp_Input_condition_description=Judgment condition
Falcon_Bp_IfElseBp_Input_condition_label=Conditions
Falcon_Bp_IfElseBp_description=Condition judgment, go to the submodule of true when the condition is met, go to the submodule of false when the condition is not met
Falcon_Bp_IfElseBp_label=IF ELSE
Falcon_Bp_IterateListBp_Context_index_description=Index
Falcon_Bp_IterateListBp_Context_item_description=Single data message
Falcon_Bp_IterateListBp_Input_list_description=Traversing list
Falcon_Bp_IterateListBp_Input_list_label=List
Falcon_Bp_IterateListBp_Input_parallel_description=Determine whether to traverse in parallel
Falcon_Bp_IterateListBp_Input_parallel_label=Parallel
Falcon_Bp_IterateListBp_description=Traverse arrays, support parallelism
Falcon_Bp_IterateListBp_label=Traverse an array
Falcon_Bp_KeepTryingLockBinBp_Input_binId_description=Bin ID
Falcon_Bp_KeepTryingLockBinBp_Input_binId_label=Bin
Falcon_Bp_KeepTryingLockBinBp_Input_reason_description=Reason for locking
Falcon_Bp_KeepTryingLockBinBp_Input_reason_label=Reason
Falcon_Bp_KeepTryingLockBinBp_description=Repeatedly try to lock the bin until successful
Falcon_Bp_KeepTryingLockBinBp_label=Repeatedly try to lock the bin until successful
Falcon_Bp_KeepTryingLockNotOccupiedBinBp_Input_binId_description=Bin ID
Falcon_Bp_KeepTryingLockNotOccupiedBinBp_Input_binId_label=Bin ID
Falcon_Bp_KeepTryingLockNotOccupiedBinBp_description=Wait for the bin to be empty and locked
Falcon_Bp_KeepTryingLockNotOccupiedBinBp_label=Wait for the bin to be empty and locked
Falcon_Bp_LockBinOnceBp_Input_binId_label=Bin ID
Falcon_Bp_LockBinOnceBp_Input_notFoundToAborted_description=Whether the failure requires the task to be terminated, true is yes
Falcon_Bp_LockBinOnceBp_Input_notFoundToAborted_label=Unable to find termination task
Falcon_Bp_LockBinOnceBp_Input_notFoundToFault_description=Does the failure require the task to malfunction? True means yes.
Falcon_Bp_LockBinOnceBp_Input_notFoundToFault_label=Can't find let the task malfunction
Falcon_Bp_LockBinOnceBp_Output_ok_label=Whether the lock was successful
Falcon_Bp_LockBinOnceBp_description=Attempt to lock an unlocked bin once, return true if successful, otherwise return false.
Falcon_Bp_LockBinOnceBp_label=Lock unlocked bins
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_newState_description=Expected updated state
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_newState_label=Updated status
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_orderId_description=The tracking ID of the container transport order, uniquely identified
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_orderId_label=Order ID
Falcon_Bp_MarkContainerTransportOrderDoneBp_description=Update the status of the container transport order based on the tracking ID
Falcon_Bp_MarkContainerTransportOrderDoneBp_label=Marking container transport list completed
Falcon_Bp_MoveInvByContainerBp_Input_leafContainerId_description=Please enter the number of the leaf container.
Falcon_Bp_MoveInvByContainerBp_Input_leafContainerId_label=Leaf container
Falcon_Bp_MoveInvByContainerBp_Input_toBinId_description=The number of the bin where the leaf container will be placed.
Falcon_Bp_MoveInvByContainerBp_Input_toBinId_label=End bin
Falcon_Bp_MoveInvByContainerBp_description=Move inventory by container.
Falcon_Bp_MoveInvByContainerBp_label=Move inventory by container
Falcon_Bp_PadIntStrBp_Input_numLength_description=After converting numbers to strings, if the specified length is less than the original length, the specified length is ignored; otherwise, fill it with 0
Falcon_Bp_PadIntStrBp_Input_numLength_label=Digital length
Falcon_Bp_PadIntStrBp_Input_num_description=Numbers to be converted
Falcon_Bp_PadIntStrBp_Input_num_label=Digital
Falcon_Bp_PadIntStrBp_Input_prefix_description=Converted string prefix
Falcon_Bp_PadIntStrBp_Input_prefix_label=Prefix
Falcon_Bp_PadIntStrBp_Input_suffix_description=Converted string suffix
Falcon_Bp_PadIntStrBp_Input_suffix_label=Suffix
Falcon_Bp_PadIntStrBp_Output_numStr_description=String after digital conversion
Falcon_Bp_PadIntStrBp_Output_numStr_label=String after digital conversion
Falcon_Bp_PadIntStrBp_description=According to the condition, convert the number to a string of the specified length, and fill in 0 if it is insufficient.
Falcon_Bp_PadIntStrBp_label=Convert numbers to a specified length string
Falcon_Bp_ParallelFlowBp_description=Parallel execution
Falcon_Bp_ParallelFlowBp_label=Parallel execution
Falcon_Bp_PrintBp_Input_message_description=Information to be printed
Falcon_Bp_PrintBp_Input_message_label=Message
Falcon_Bp_PrintBp_description=Print
Falcon_Bp_PrintBp_label=Print
Falcon_Bp_ReduceBinInvFromOrderBp_Input_binField_description=Please enter the field name that represents bin on order line.
Falcon_Bp_ReduceBinInvFromOrderBp_Input_binField_label=Bin field
Falcon_Bp_ReduceBinInvFromOrderBp_Input_ev_description=Please enter order
Falcon_Bp_ReduceBinInvFromOrderBp_Input_ev_label=Order entity value
Falcon_Bp_ReduceBinInvFromOrderBp_Input_materialField_description=Please enter the field name representing the material on order line
Falcon_Bp_ReduceBinInvFromOrderBp_Input_materialField_label=Material field
Falcon_Bp_ReduceBinInvFromOrderBp_Input_outboundOrderIdField_label=Outbound order ID
Falcon_Bp_ReduceBinInvFromOrderBp_Input_pickOrderIdField_label=Pick order ID
Falcon_Bp_ReduceBinInvFromOrderBp_Input_qtyField_description=Please enter the field name that represents the quantity on order line.
Falcon_Bp_ReduceBinInvFromOrderBp_Input_qtyField_label=Quantity field
Falcon_Bp_ReduceBinInvFromOrderBp_Input_subContainerIdField_label=Cell
Falcon_Bp_ReduceBinInvFromOrderBp_description=Delete inventory from bin by order
Falcon_Bp_ReduceBinInvFromOrderBp_label=Delete inventory from bin by order
Falcon_Bp_RemoveInvByContainerBp_Input_containerId_description=Please enter the container ID recorded in M4.
Falcon_Bp_RemoveInvByContainerBp_Input_containerId_label=Container ID
Falcon_Bp_RemoveInvByContainerBp_Output_count_description=Deleted inventory details rows.
Falcon_Bp_RemoveInvByContainerBp_Output_count_label=Detailed quantity
Falcon_Bp_RemoveInvByContainerBp_description=Delete the inventory details by container ID.
Falcon_Bp_RemoveInvByContainerBp_label=Delete inventory details by container
Falcon_Bp_RemoveTraceContainerBp_Input_containerId_description=Please enter the tracking container ID recorded by M4.
Falcon_Bp_RemoveTraceContainerBp_Input_containerId_label=Container ID.
Falcon_Bp_RemoveTraceContainerBp_description=Delete the tracking container.
Falcon_Bp_RemoveTraceContainerBp_label=Delete tracking container
Falcon_Bp_RepeatNumBp_Child_default_label=Component
Falcon_Bp_RepeatNumBp_Context_index_description=Serial number
Falcon_Bp_RepeatNumBp_Context_index_label=Serial number
Falcon_Bp_RepeatNumBp_Input_num_description=Number of repeated executions of subcomponents
Falcon_Bp_RepeatNumBp_Input_num_label=Number of executions
Falcon_Bp_RepeatNumBp_description=Subcomponent repeated execution
Falcon_Bp_RepeatNumBp_label=Repeated execution
Falcon_Bp_SerialFlowBp_description=Serial execution
Falcon_Bp_SerialFlowBp_label=Serial execution
Falcon_Bp_SetBinEmptyBp_Input_binId_description=Bin ID
Falcon_Bp_SetBinEmptyBp_Input_binId_label=Bin ID
Falcon_Bp_SetBinEmptyBp_Input_noContainer_description=True sets the bin to no container
Falcon_Bp_SetBinEmptyBp_Input_noContainer_label=No container
Falcon_Bp_SetBinEmptyBp_Input_notOccupied_description=True sets the bin to unoccupied
Falcon_Bp_SetBinEmptyBp_Input_notOccupied_label=Not occupied
Falcon_Bp_SetBinEmptyBp_Input_unlock_label=Unlock
Falcon_Bp_SetBinEmptyBp_Input_unlock_label_description=True to unlock the bin
Falcon_Bp_SetBinEmptyBp_description=Set bin unoccupied (deprecated)
Falcon_Bp_SetBinEmptyBp_label=Set bin unoccupied (deprecated)
Falcon_Bp_SetBinNotOccupiedBp_Input_binId_description=Bin ID
Falcon_Bp_SetBinNotOccupiedBp_Input_binId_label=Bin ID
Falcon_Bp_SetBinNotOccupiedBp_Input_unlockAfter_description=True is to set unoccupied and unlock the bin, false is to set unoccupied and not unlock the bin.
Falcon_Bp_SetBinNotOccupiedBp_Input_unlockAfter_label=Unlock
Falcon_Bp_SetBinNotOccupiedBp_description=Set bin unoccupied
Falcon_Bp_SetBinNotOccupiedBp_label=Set bin unoccupied
Falcon_Bp_SetTaskVariableBp_Input_varName_description=The name of the task variable
Falcon_Bp_SetTaskVariableBp_Input_varName_label=Variable name
Falcon_Bp_SetTaskVariableBp_Input_varValue_description=Value of task variable
Falcon_Bp_SetTaskVariableBp_Input_varValue_label=Variable value
Falcon_Bp_SetTaskVariableBp_description=Set task variables
Falcon_Bp_SetTaskVariableBp_label=Set task variables
Falcon_Bp_SimpleScriptBp_Input_scriptStr_label=Script source code
Falcon_Bp_SimpleScriptBp_Output_scriptOut_description=Return value of script execution
Falcon_Bp_SimpleScriptBp_Output_scriptOut_label=Return value
Falcon_Bp_SimpleScriptBp_label=Simple script
Falcon_Bp_SuiBp_Input_config_description=User of configuration operation
Falcon_Bp_SuiBp_Input_config_label=Configure
Falcon_Bp_SuiBp_Output_button_description=Buttons clicked by the user
Falcon_Bp_SuiBp_Output_button_label=Buttons clicked by the user
Falcon_Bp_SuiBp_Output_input_description=User input parameters
Falcon_Bp_SuiBp_Output_input_label=User input
Falcon_Bp_SuiBp_description=Waiting for user action
Falcon_Bp_SuiBp_label=User intervention
Falcon_Bp_ThrowBp_Input_condition_description=Whether to throw an exception
Falcon_Bp_ThrowBp_Input_condition_label=Conditions
Falcon_Bp_ThrowBp_Input_errMsg_description=Throw exception message
Falcon_Bp_ThrowBp_Input_errMsg_label=Abnormal message
Falcon_Bp_ThrowBp_description=Throw an exception
Falcon_Bp_ThrowBp_label=Throw an exception
Falcon_Bp_TimestampBp_Input_formatDate_description=Specify date format
Falcon_Bp_TimestampBp_Input_formatDate_label=Date format
Falcon_Bp_TimestampBp_Output_timestamp_description=Return a string in the specified date format.
Falcon_Bp_TimestampBp_Output_timestamp_label=Current timestamp
Falcon_Bp_TimestampBp_description=Return the current time string in the specified format.
Falcon_Bp_TimestampBp_label=Return to the current time
Falcon_Bp_TriggerTaskEventBp_Input_eventData_description=Event data
Falcon_Bp_TriggerTaskEventBp_Input_eventData_label=Event data
Falcon_Bp_TriggerTaskEventBp_Input_eventName_description=Name of the event
Falcon_Bp_TriggerTaskEventBp_Input_eventName_label=Event name
Falcon_Bp_TriggerTaskEventBp_description=Trigger task event
Falcon_Bp_TriggerTaskEventBp_label=Trigger task event
Falcon_Bp_TryCatchBp_Input_ignoreAbort_description=Ignore cancellation exception
Falcon_Bp_TryCatchBp_Input_ignoreAbort_label=Ignore cancellation exception
Falcon_Bp_TryCatchBp_Input_swallowError_description=Swallow the exception and do not throw it
Falcon_Bp_TryCatchBp_Input_swallowError_label=Swallow exceptions without throwing
Falcon_Bp_TryCatchBp_description=Capture exception information
Falcon_Bp_TryCatchBp_label=try-catch
Falcon_Bp_UnbindBinContainerBp_Input_binId_description=Bin ID
Falcon_Bp_UnbindBinContainerBp_Input_binId_label=Bin ID
Falcon_Bp_UnbindBinContainerBp_Input_containerId_description=Container ID
Falcon_Bp_UnbindBinContainerBp_Input_containerId_label=Container ID
Falcon_Bp_UnbindBinContainerBp_Input_unlockBin_description=Whether to unlock the bin
Falcon_Bp_UnbindBinContainerBp_Input_unlockBin_label=Unlock bin
Falcon_Bp_UnbindBinContainerBp_Input_unlockContainer_description=Whether to unlock the container
Falcon_Bp_UnbindBinContainerBp_Input_unlockContainer_label=Unlock the container
Falcon_Bp_UnbindBinContainerBp_description=Unbind the bin from the container
Falcon_Bp_UnbindBinContainerBp_label=Unbind bin container
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_description=Field names that need to be updated
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_label=Field
Falcon_Bp_UpdateContainerTransportOrderBp_Input_fv_description=Expected updated field value
Falcon_Bp_UpdateContainerTransportOrderBp_Input_fv_label=Field value
Falcon_Bp_UpdateContainerTransportOrderBp_Input_orderId_description=The tracking ID of the container handling order, uniquely identified
Falcon_Bp_UpdateContainerTransportOrderBp_Input_orderId_label=Order ID
Falcon_Bp_UpdateContainerTransportOrderBp_description=Update the value of the specified field on the container shipping order
Falcon_Bp_UpdateContainerTransportOrderBp_label=Update container handling list
Falcon_Bp_UpdateEntityFieldBp_Input_entityName_description=Entity name
Falcon_Bp_UpdateEntityFieldBp_Input_entityName_label=Entity name
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_description=Query field type
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_label=Query field type
Falcon_Bp_UpdateEntityFieldBp_Input_queryField_description=Query field
Falcon_Bp_UpdateEntityFieldBp_Input_queryField_label=Queried field
Falcon_Bp_UpdateEntityFieldBp_Input_queryValue_description=Query field value
Falcon_Bp_UpdateEntityFieldBp_Input_queryValue_label=Query value
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_description=Update the type of field
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_label=Update field type
Falcon_Bp_UpdateEntityFieldBp_Input_updateField_description=Fields to update
Falcon_Bp_UpdateEntityFieldBp_Input_updateField_label=Updated fields
Falcon_Bp_UpdateEntityFieldBp_Input_updateMany_description=Batch update
Falcon_Bp_UpdateEntityFieldBp_Input_updateMany_label=Batch update
Falcon_Bp_UpdateEntityFieldBp_Input_updateValue_description=Update value
Falcon_Bp_UpdateEntityFieldBp_Input_updateValue_label=Update value
Falcon_Bp_UpdateEntityFieldBp_description=Update entity record field by condition
Falcon_Bp_UpdateEntityFieldBp_label=Update Business Object individual fields based on conditions
Falcon_Bp_UpdateOneEntityByIdBp_Input_entityName_description=Business Object Name
Falcon_Bp_UpdateOneEntityByIdBp_Input_entityName_label=Business Object Name
Falcon_Bp_UpdateOneEntityByIdBp_Input_fieldName_description=Update field name
Falcon_Bp_UpdateOneEntityByIdBp_Input_fieldName_label=Field name
Falcon_Bp_UpdateOneEntityByIdBp_Input_fv_description=The value of the updated field
Falcon_Bp_UpdateOneEntityByIdBp_Input_fv_label=Field value
Falcon_Bp_UpdateOneEntityByIdBp_Input_id_description=Entity ID
Falcon_Bp_UpdateOneEntityByIdBp_Input_id_label=ID
Falcon_Bp_UpdateOneEntityByIdBp_description=Update Business Object entity single field based on ID
Falcon_Bp_UpdateOneEntityByIdBp_label=Update Business Object single field based on ID
Falcon_Bp_WebSocketBp_Input_eventName_description=WebSocket communication identifier
Falcon_Bp_WebSocketBp_Input_eventName_label=Send event
Falcon_Bp_WebSocketBp_Input_message_description=Send content
Falcon_Bp_WebSocketBp_Input_message_label=Send content
Falcon_Bp_WebSocketBp_description=Send Message to WebSocket Client
Falcon_Bp_WebSocketBp_label=WebSocket sends messages
ModbusDeviceNotInit=Modbus device "{0}" is not Initialized
wcs_err_TomNoUrl=Scheduling "{0}" not configured URL
wcs_err_TomNotFound=Scheduling "{0}" not configured
wcs_err_tom_BlockError=Scheduling says block execution failed, order number 
wcs_err_tom_BlockNotFound=Scheduling says block not found, order number 
wcs_err_tom_ConnectError=Connection scheduling server failed: {0}
wcs_err_tom_HttpError=Request scheduling interface Other errors: [{0}] {1}
wcs_err_tom_HttpError404=Request scheduling interface report 404
wcs_err_tom_IOError=Request scheduling, IO error: {0}, Request address: {1}
wcs_err_tom_OtherError=Request scheduling, other errors: {0}, Request address: {1}
wcs_err_tom_TomError=Dispatch error, error code 
wcs_err_tom_TomResponseEmpty=Request scheduling response body is empty
errNoDevice=No such device: {0}
Falcon_BlockGroup_PLC=PLC
Falcon_Bp_ModbusReadBp_Input_address_description=Address of Modbus
Falcon_Bp_ModbusReadBp_Input_address_label=Address
Falcon_Bp_ModbusReadBp_Input_code_description=Function code of Modbus
Falcon_Bp_ModbusReadBp_Input_code_label=Function code
Falcon_Bp_ModbusReadBp_Input_deviceName_description=The device name needs to be configured in the PLC device configuration menu first
Falcon_Bp_ModbusReadBp_Input_deviceName_label=Device name
Falcon_Bp_ModbusReadBp_Input_maxRetry_description=Maximum number of retries, default is the maximum number of retries set in the PLC device configuration menu
Falcon_Bp_ModbusReadBp_Input_maxRetry_label=Maximum number of retries
Falcon_Bp_ModbusReadBp_Input_retryDelay_description=Retry interval, default is the retry interval set in the PLC device configuration menu
Falcon_Bp_ModbusReadBp_Input_retryDelay_label=Retry wait (milliseconds)
Falcon_Bp_ModbusReadBp_Input_slaveId_description=Modbus device ID, default is 0
Falcon_Bp_ModbusReadBp_Input_slaveId_label=Slave ID
Falcon_Bp_ModbusReadBp_Output_value_description=The value of the corresponding address read out
Falcon_Bp_ModbusReadBp_Output_value_label=Value
Falcon_Bp_ModbusReadBp_description=Read the value of Modbus
Falcon_Bp_ModbusReadBp_label=Modbus read
Falcon_Bp_ModbusReadEqBp_Input_address_description=Modbus address
Falcon_Bp_ModbusReadEqBp_Input_address_label=Address
Falcon_Bp_ModbusReadEqBp_Input_code_description=Modbus Function Code
Falcon_Bp_ModbusReadEqBp_Input_code_label=Function code
Falcon_Bp_ModbusReadEqBp_Input_deviceName_description=The device name needs to be configured in the PLC device configuration menu first
Falcon_Bp_ModbusReadEqBp_Input_deviceName_label=Device name
Falcon_Bp_ModbusReadEqBp_Input_maxRetry_description=Max retry count, default to the maximum retry count set in the PLC device configuration menu
Falcon_Bp_ModbusReadEqBp_Input_maxRetry_label=Max retries
Falcon_Bp_ModbusReadEqBp_Input_readDelay_description=The interval between two readings, default is 1 second
Falcon_Bp_ModbusReadEqBp_Input_readDelay_label=Read interval
Falcon_Bp_ModbusReadEqBp_Input_retryDelay_description=The retry interval defaults to the retry interval set in the PLC device configuration menu
Falcon_Bp_ModbusReadEqBp_Input_retryDelay_label=Retry wait (milliseconds)
Falcon_Bp_ModbusReadEqBp_Input_slaveId_description=Modbus device id, default is 0
Falcon_Bp_ModbusReadEqBp_Input_slaveId_label=Slave ID
Falcon_Bp_ModbusReadEqBp_Input_targetValue_description=Target value
Falcon_Bp_ModbusReadEqBp_Input_targetValue_label=Target value
Falcon_Bp_ModbusReadEqBp_description=Read Modbus until it reaches the expected value, if not, continue reading until the maxRetry count is exceeded
Falcon_Bp_ModbusReadEqBp_label=Read Modbus until it equals
Falcon_Bp_ModbusWriteBp_Input_address_description=Modbus address
Falcon_Bp_ModbusWriteBp_Input_address_label=Address
Falcon_Bp_ModbusWriteBp_Input_code_description=Modbus Function Code
Falcon_Bp_ModbusWriteBp_Input_code_label=Function code
Falcon_Bp_ModbusWriteBp_Input_deviceName_description=The device name needs to be configured in the PLC device configuration menu first
Falcon_Bp_ModbusWriteBp_Input_deviceName_label=Device name
Falcon_Bp_ModbusWriteBp_Input_maxRetry_description=Max retry count, default to the maximum retry count set in the PLC device configuration menu
Falcon_Bp_ModbusWriteBp_Input_maxRetry_label=Max retries
Falcon_Bp_ModbusWriteBp_Input_retryDelay_description=The retry interval defaults to the retry interval set in the PLC device configuration menu
Falcon_Bp_ModbusWriteBp_Input_retryDelay_label=Retry wait (milliseconds)
Falcon_Bp_ModbusWriteBp_Input_slaveId_description=Modbus device id, default is 0
Falcon_Bp_ModbusWriteBp_Input_slaveId_label=Slave ID
Falcon_Bp_ModbusWriteBp_Input_value_description=The value written
Falcon_Bp_ModbusWriteBp_Input_value_label=Value
Falcon_Bp_ModbusWriteBp_description=Write to Modbus
Falcon_Bp_ModbusWriteBp_label=Modbus write
Falcon_Bp_S7ReadBp_Input_bitOffset_description=Bit offset
Falcon_Bp_S7ReadBp_Input_bitOffset_label=Bit offset
Falcon_Bp_S7ReadBp_Input_blockType_description=Please select the corresponding block type according to the agreement content.
Falcon_Bp_S7ReadBp_Input_blockType_label=Block type
Falcon_Bp_S7ReadBp_Input_byteOffset_description=Byte offset can be understood as address number.
Falcon_Bp_S7ReadBp_Input_byteOffset_label=Byte offset
Falcon_Bp_S7ReadBp_Input_dataType_description=Please select the corresponding data type according to the agreement content.
Falcon_Bp_S7ReadBp_Input_dataType_label=Data type
Falcon_Bp_S7ReadBp_Input_dbId_description=DB ID, please enter the corresponding value according to the agreement content.
Falcon_Bp_S7ReadBp_Input_dbId_label=dbId
Falcon_Bp_S7ReadBp_Input_deviceName_description=Please enter the name of the S7 device configured in "PLC Facility Management".
Falcon_Bp_S7ReadBp_Input_deviceName_label=Device name
Falcon_Bp_S7ReadBp_Output_value_description=Value
Falcon_Bp_S7ReadBp_Output_value_label=Value
Falcon_Bp_S7ReadBp_description=Read a data from the PLC device once through the S7 protocol and use the read value as the output parameter of this component.
Falcon_Bp_S7ReadBp_label=S7 reading
Falcon_Bp_S7ReadEqBp_Input_bitOffset_description=Bit offset
Falcon_Bp_S7ReadEqBp_Input_bitOffset_label=Bit offset
Falcon_Bp_S7ReadEqBp_Input_blockType_description=Please select the corresponding block type according to the agreement content.
Falcon_Bp_S7ReadEqBp_Input_blockType_label=blockType
Falcon_Bp_S7ReadEqBp_Input_byteOffset_description=Byte offset can be understood as address number.
Falcon_Bp_S7ReadEqBp_Input_byteOffset_label=Byte offset
Falcon_Bp_S7ReadEqBp_Input_dataType_description=Please select the corresponding data type according to the agreement content.
Falcon_Bp_S7ReadEqBp_Input_dataType_label=dataType
Falcon_Bp_S7ReadEqBp_Input_dbId_description=DB ID, please enter the corresponding value according to the agreement content.
Falcon_Bp_S7ReadEqBp_Input_dbId_label=dbId
Falcon_Bp_S7ReadEqBp_Input_deviceName_description=Please enter the name of the S7 device configured in "PLC Facility Management".
Falcon_Bp_S7ReadEqBp_Input_deviceName_label=Device name
Falcon_Bp_S7ReadEqBp_Input_value_description=Expected value
Falcon_Bp_S7ReadEqBp_Input_value_label=Value
Falcon_Bp_S7ReadEqBp_description=Read a data from the PLC device continuously through the S7 protocol until the read value matches the expected value before stopping.
Falcon_Bp_S7ReadEqBp_label=S7 reads until it equals
Falcon_Bp_S7WriteBp_Input_bitOffset_description=Bit offset
Falcon_Bp_S7WriteBp_Input_bitOffset_label=Bit offset
Falcon_Bp_S7WriteBp_Input_blockType_description=Please select the corresponding block type according to the agreement content.
Falcon_Bp_S7WriteBp_Input_blockType_label=blockType
Falcon_Bp_S7WriteBp_Input_byteOffset_description=Byte offset can be understood as address number.
Falcon_Bp_S7WriteBp_Input_byteOffset_label=Byte offset
Falcon_Bp_S7WriteBp_Input_dataType_description=Please select the corresponding data type according to the agreement content.
Falcon_Bp_S7WriteBp_Input_dataType_label=Data type
Falcon_Bp_S7WriteBp_Input_dbId_description=DB ID, please enter the corresponding value according to the agreement content.
Falcon_Bp_S7WriteBp_Input_dbId_label=dbId
Falcon_Bp_S7WriteBp_Input_deviceName_description=Please enter the name of the S7 device configured in "PLC Facility Management".
Falcon_Bp_S7WriteBp_Input_deviceName_label=Device name
Falcon_Bp_S7WriteBp_Input_value_description=Target value
Falcon_Bp_S7WriteBp_Input_value_label=Value
Falcon_Bp_S7WriteBp_description=Write a target value to the PLC device through the S7 protocol.
Falcon_Bp_S7WriteBp_label=S7 write
RbkApiNoNoSupported=API number {0} not supported
RbkClientConnectFail=Connect to RBK failed {0}
RbkClientRequestFail=Request RBK failed {0}
RobotNotExistedById=Robot "{0}" does not exist
Falcon_BlockGroup_DirectOrder=Direct transport order
Falcon_BlockGroup_Map=Map
Falcon_BlockGroup_Ndc=NDC Dispatch transport order
Falcon_BlockGroup_SeerTom=Seer Tom dispatch
Falcon_Bp_AddTomBlockBp_Input_binTask_description=binTask
Falcon_Bp_AddTomBlockBp_Input_binTask_label=binTask
Falcon_Bp_AddTomBlockBp_Input_goodsId_description=Container number
Falcon_Bp_AddTomBlockBp_Input_goodsId_label=Goods ID
Falcon_Bp_AddTomBlockBp_Input_location_description=Destination name, site name, or bin
Falcon_Bp_AddTomBlockBp_Input_location_label=Site
Falcon_Bp_AddTomBlockBp_Input_operation_description=Execution mechanism
Falcon_Bp_AddTomBlockBp_Input_operation_label=Operation
Falcon_Bp_AddTomBlockBp_Input_orderId_description=Create second-generation dispatch waybill. The "waybill number" generated by the block.
Falcon_Bp_AddTomBlockBp_Input_orderId_label=Transport order number
Falcon_Bp_AddTomBlockBp_Input_tomId_description=Scene name of "Second Generation Scheduling" on the "Robot Application" page
Falcon_Bp_AddTomBlockBp_Input_tomId_label=Dispatch ID
Falcon_Bp_AddTomBlockBp_Output_blockId_description=Block ID of the action in scheduling
Falcon_Bp_AddTomBlockBp_Output_blockId_label=Block ID
Falcon_Bp_AddTomBlockBp_description=Append action block
Falcon_Bp_AddTomBlockBp_label=Execute waybill block
Falcon_Bp_AllowNdcLoadBp_Input_orderId_label=Transport order ID
Falcon_Bp_AllowNdcLoadBp_description=Not enabled
Falcon_Bp_AllowNdcLoadBp_label=Allow NDC loading
Falcon_Bp_AllowNdcUnloadBp_Input_orderId_label=Transport order ID
Falcon_Bp_AllowNdcUnloadBp_description=Not enabled
Falcon_Bp_AllowNdcUnloadBp_label=Allow NDC unloading
Falcon_Bp_CompleteTomOrderBp_Input_orderId_description=Create second-generation dispatch waybill. The "waybill number" generated by the block.
Falcon_Bp_CompleteTomOrderBp_Input_orderId_label=Transport order number
Falcon_Bp_CompleteTomOrderBp_Input_tomId_description=Scene name of "Second Generation Scheduling" on the "Robot Application" page
Falcon_Bp_CompleteTomOrderBp_Input_tomId_label=Dispatch ID
Falcon_Bp_CompleteTomOrderBp_description=Sealing
Falcon_Bp_CompleteTomOrderBp_label=Complete dispatch waybill
Falcon_Bp_CreateNdcOrderBp_Input_endBin_description=NDC bin, Short type
Falcon_Bp_CreateNdcOrderBp_Input_endBin_label=End bin
Falcon_Bp_CreateNdcOrderBp_Input_priority_label=Priority
Falcon_Bp_CreateNdcOrderBp_Input_startBin_description=NDC bin, Short type
Falcon_Bp_CreateNdcOrderBp_Input_startBin_label=Start bin
Falcon_Bp_CreateNdcOrderBp_Output_orderId_description=NDC Transport order ID
Falcon_Bp_CreateNdcOrderBp_Output_orderId_label=Transport order ID
Falcon_Bp_CreateNdcOrderBp_description=M4 sends the start bin, end bin, and priority to NDC together; NDC automatic execution; Report to M4 after NDC retrieval, release, and task completion
Falcon_Bp_CreateNdcOrderBp_label=Create NDC dispatch transport order 
Falcon_Bp_CreateTomOrderBp_Input_group_description=Specify a robot group to assign robots belonging to this robot group to execute when selecting vehicles
Falcon_Bp_CreateTomOrderBp_Input_group_label=Specify a bot group
Falcon_Bp_CreateTomOrderBp_Input_keyGoodsId_description=For the scenario of n + 1 material container trucks, the auxiliary core is the last to pick up the goods and the first to put them down
Falcon_Bp_CreateTomOrderBp_Input_keyGoodsId_label=keyGoodsId
Falcon_Bp_CreateTomOrderBp_Input_keyRouteStr_description=Key points are used to assist in determining the dispatching robot; if not filled, the system will automatically select the appropriate robot dispatching based on the current operating situation
Falcon_Bp_CreateTomOrderBp_Input_keyRouteStr_label=Key location
Falcon_Bp_CreateTomOrderBp_Input_keyTask_description=Is "load" or "unload", if other fields are filled in, they will be automatically ignored to assist in determining the dispatching robot; if not filled in, the system will automatically select the appropriate robot dispatching based on the current operation status
Falcon_Bp_CreateTomOrderBp_Input_keyTask_label=keyTask
Falcon_Bp_CreateTomOrderBp_Input_notMarkComplete_description=Cancel the Falcon mission, do not seal the dispatch waybill
Falcon_Bp_CreateTomOrderBp_Input_notMarkComplete_label=Cancel the task, the waybill is not sealed
Falcon_Bp_CreateTomOrderBp_Input_priority_description=Waybill priority, the larger the number, the higher the order priority.
Falcon_Bp_CreateTomOrderBp_Input_priority_label=Priority
Falcon_Bp_CreateTomOrderBp_Input_tomId_description=Scene name of "Second Generation Scheduling" on the "Robot Application" page
Falcon_Bp_CreateTomOrderBp_Input_tomId_label=Dispatch ID
Falcon_Bp_CreateTomOrderBp_Input_vehicle_description=Specify the robot to assign the specified robot to execute when selecting a vehicle.
Falcon_Bp_CreateTomOrderBp_Input_vehicle_label=Specify a bot
Falcon_Bp_CreateTomOrderBp_Output_actualVehicle_description=Second-generation scheduling and allocation robot
Falcon_Bp_CreateTomOrderBp_Output_actualVehicle_label=Allocated robots
Falcon_Bp_CreateTomOrderBp_Output_orderId_description=Second generation dispatch waybill number
Falcon_Bp_CreateTomOrderBp_Output_orderId_label=Tom order ID
Falcon_Bp_CreateTomOrderBp_description=Create a second-generation dispatch waybill, namely core dispatch
Falcon_Bp_CreateTomOrderBp_label=Create Tom order
Falcon_Bp_DirectOrderExecuteBp_Input_desc_label=Description
Falcon_Bp_DirectOrderExecuteBp_Input_fillMiddleLandmarks_description=If there are other stations between the direct waybill steps, fill the intermediate station into the direct waybill
Falcon_Bp_DirectOrderExecuteBp_Input_fillMiddleLandmarks_label=Fill in the middle point
Falcon_Bp_DirectOrderExecuteBp_Input_robotId_label=Robot number
Falcon_Bp_DirectOrderExecuteBp_Input_seer3066_description=Use the 3066 command to specify the path navigation
Falcon_Bp_DirectOrderExecuteBp_Input_seer3066_label=3066 instruction enabled
Falcon_Bp_DirectOrderExecuteBp_Input_taskId_label=Task number
Falcon_Bp_DirectOrderExecuteBp_Output_orderId_description=Direct transport order ID
Falcon_Bp_DirectOrderExecuteBp_Output_orderId_label=Order number
Falcon_Bp_DirectOrderExecuteBp_description=Create "Direct transport order" and wait for its execution to complete (no duplicate creation).
Falcon_Bp_DirectOrderExecuteBp_label=Direct transport order execution
Falcon_Bp_DirectOrderMoveBp_Input_binTask_label=binTask
Falcon_Bp_DirectOrderMoveBp_Input_containerId_label=Container number
Falcon_Bp_DirectOrderMoveBp_Input_id_description=Destination name, site name, or bin
Falcon_Bp_DirectOrderMoveBp_Input_id_label=Step ID
Falcon_Bp_DirectOrderMoveBp_Input_operation_description=Execution mechanism
Falcon_Bp_DirectOrderMoveBp_Input_operation_label=Operation
Falcon_Bp_DirectOrderMoveBp_description=Direct transport order steps
Falcon_Bp_DirectOrderMoveBp_label=Direct transport order steps
Falcon_Bp_MapPointBinBp_Input_pointId_label=Site
Falcon_Bp_MapPointBinBp_Output_binId_label=Bin
Falcon_Bp_MapPointBinBp_Output_found_label=The site is equipped with a bin
Falcon_Bp_MapPointBinBp_label=Site bin
Falcon_Bp_WaitUntilNdcArriveEndBinBp_Input_orderId_label=Transport order ID
Falcon_Bp_WaitUntilNdcArriveEndBinBp_description=Not enabled
Falcon_Bp_WaitUntilNdcArriveEndBinBp_label=Waiting for NDC to reach the finish point
Falcon_Bp_WaitUntilNdcArriveStartBinBp_Input_orderId_label=Transport order ID
Falcon_Bp_WaitUntilNdcArriveStartBinBp_description=Not enabled
Falcon_Bp_WaitUntilNdcArriveStartBinBp_label=Waiting for NDC to arrive at the starting point
Falcon_Bp_WaitUntilNdcFinishBp_Input_orderId_label=Transport order ID
Falcon_Bp_WaitUntilNdcFinishBp_label=Waiting for NDC task to complete
Falcon_Bp_WaitUntilNdcLoadedBp_Input_orderId_label=Transport order ID
Falcon_Bp_WaitUntilNdcLoadedBp_label=Waiting for NDC loading completion
Falcon_Bp_WaitUntilNdcUnloadedBp_Input_orderId_label=Transport order ID
Falcon_Bp_WaitUntilNdcUnloadedBp_label=Waiting for NDC unloading to be completed
relocStatus_0=Relocation failed
relocStatus_1=Relocation successfully
relocStatus_2=Relocating
relocStatus_3=Relocation completed (need to comfirm)
relocStatus_null=Unknown
Falcon_BlockGroup_RobotSingleControl=Robot single control
Falcon_Bp_RobotSingleAngleRadianBp_Input_conversionType_description=Select the conversion type to convert the value to the desired number of degrees, or radians as the result.
Falcon_Bp_RobotSingleAngleRadianBp_Input_conversionType_label=Conversion type
Falcon_Bp_RobotSingleAngleRadianBp_Input_degrees_description=The value of degree or radian to be converted.
Falcon_Bp_RobotSingleAngleRadianBp_Input_degrees_label=Value
Falcon_Bp_RobotSingleAngleRadianBp_Output_degrees_description=The result after conversion.
Falcon_Bp_RobotSingleAngleRadianBp_Output_degrees_label=Result
Falcon_Bp_RobotSingleAngleRadianBp_description=Conversion between degrees and radians.
Falcon_Bp_RobotSingleAngleRadianBp_label=Conversion between degrees and radians.
Falcon_Bp_RobotSingleForkBp_Input_endHeight_description=The final height of the fork device after operation, default value 0.500 (unit: m).
Falcon_Bp_RobotSingleForkBp_Input_endHeight_label=End height
Falcon_Bp_RobotSingleForkBp_Input_forkDist_description=For forward-moving forks, the distance the fork moves forward is the default value of 0.00 (unit: m).
Falcon_Bp_RobotSingleForkBp_Input_forkDist_label=Stretch distance
Falcon_Bp_RobotSingleForkBp_Input_forkMidHeight_description=The height of the fork device when forklift moving, default value is 0.100 (unit: m).
Falcon_Bp_RobotSingleForkBp_Input_forkMidHeight_label=Middle height
Falcon_Bp_RobotSingleForkBp_Input_operation_description=Operable action of fork device
Falcon_Bp_RobotSingleForkBp_Input_operation_label=Operation
Falcon_Bp_RobotSingleForkBp_Input_recfile_description=When the robot performs specific operations, this file is needed for identification and comparison
Falcon_Bp_RobotSingleForkBp_Input_recfile_label=Recognize file
Falcon_Bp_RobotSingleForkBp_Input_reqId_description=Identity when the component is executing, optional
Falcon_Bp_RobotSingleForkBp_Input_reqId_label=Task ID
Falcon_Bp_RobotSingleForkBp_Input_startHeight_description=The start height of the fork device, default value is 0.100 (unit: m).
Falcon_Bp_RobotSingleForkBp_Input_startHeight_label=Start height
Falcon_Bp_RobotSingleForkBp_Input_station_description=Target site for navigation
Falcon_Bp_RobotSingleForkBp_Input_station_label=Target site
Falcon_Bp_RobotSingleForkBp_Output_rbkResult_description=Response result after execution
Falcon_Bp_RobotSingleForkBp_Output_rbkResult_label=Rbk request result
Falcon_Bp_RobotSingleForkBp_description=Operating fork device
Falcon_Bp_RobotSingleForkBp_label=Forklift
Falcon_Bp_RobotSingleJackingBp_Input_jackHeight_description=The final height after operation, defalut value is 0.01，unit is meter.
Falcon_Bp_RobotSingleJackingBp_Input_jackHeight_label=Jack height
Falcon_Bp_RobotSingleJackingBp_Input_operation_description=Operable operation of jack device
Falcon_Bp_RobotSingleJackingBp_Input_operation_label=Operation
Falcon_Bp_RobotSingleJackingBp_Input_recfile_description=When the robot performs specific operations, this file is needed for identification and comparison
Falcon_Bp_RobotSingleJackingBp_Input_recfile_label=Recognize file
Falcon_Bp_RobotSingleJackingBp_Input_reqId_description=Identity when the component is executing, optional
Falcon_Bp_RobotSingleJackingBp_Input_reqId_label=Task ID
Falcon_Bp_RobotSingleJackingBp_Input_station_description=Target site for navigation
Falcon_Bp_RobotSingleJackingBp_Input_station_label=Target site
Falcon_Bp_RobotSingleJackingBp_Input_useDownPgv_description=Using PGV that towards bottom
Falcon_Bp_RobotSingleJackingBp_Input_useDownPgv_label=Using PGV that towards bottom
Falcon_Bp_RobotSingleJackingBp_Input_usePgv_description=Using PGV that towards top
Falcon_Bp_RobotSingleJackingBp_Input_usePgv_label=Using PGV that towards top
Falcon_Bp_RobotSingleJackingBp_Output_rbkResult_description=Response result after execution
Falcon_Bp_RobotSingleJackingBp_Output_rbkResult_label=Rbk request result
Falcon_Bp_RobotSingleJackingBp_description=Operating jack device
Falcon_Bp_RobotSingleJackingBp_label=Jack
Falcon_Bp_RobotSingleNavigationBp_Input_reqId_description=Identity when the component is executing, optional
Falcon_Bp_RobotSingleNavigationBp_Input_reqId_label=Task ID
Falcon_Bp_RobotSingleNavigationBp_Input_station_description=Target site for navigation
Falcon_Bp_RobotSingleNavigationBp_Input_station_label=Target site
Falcon_Bp_RobotSingleNavigationBp_Output_rbkResult_description=Response result after execution
Falcon_Bp_RobotSingleNavigationBp_Output_rbkResult_label=Rbk request result
Falcon_Bp_RobotSingleNavigationBp_description=Navigate the robot to the target site
Falcon_Bp_RobotSingleNavigationBp_label=Path navigation
Falcon_Bp_RobotSinglePlayAudioBp_Input_audioFilename_description=Used to specify the audio file to be played by the robot
Falcon_Bp_RobotSinglePlayAudioBp_Input_audioFilename_label=Audio file name
Falcon_Bp_RobotSinglePlayAudioBp_Input_loop_description=Check it to play the audio repeatedly, otherwise robot will only play once
Falcon_Bp_RobotSinglePlayAudioBp_Input_loop_label=Loop play
Falcon_Bp_RobotSinglePlayAudioBp_Input_reqId_description=Identity when the component is executing, optional
Falcon_Bp_RobotSinglePlayAudioBp_Input_reqId_label=Task ID
Falcon_Bp_RobotSinglePlayAudioBp_Input_station_description=Target site for navigation
Falcon_Bp_RobotSinglePlayAudioBp_Input_station_label=Target site
Falcon_Bp_RobotSinglePlayAudioBp_Output_rbkResult_description=Response result after execution
Falcon_Bp_RobotSinglePlayAudioBp_Output_rbkResult_label=Rbk request result
Falcon_Bp_RobotSinglePlayAudioBp_description=Control the robot to play audio
Falcon_Bp_RobotSinglePlayAudioBp_label=Play audio
Falcon_Bp_RobotSingleRollerBp_Input_direction_description=Rolling direction of the roller device
Falcon_Bp_RobotSingleRollerBp_Input_direction_label=Direction
Falcon_Bp_RobotSingleRollerBp_Input_operation_description=Operable operation of roller device
Falcon_Bp_RobotSingleRollerBp_Input_operation_label=Operation
Falcon_Bp_RobotSingleRollerBp_Input_reqId_description=Identity when the component is executing, optional
Falcon_Bp_RobotSingleRollerBp_Input_reqId_label=Task ID
Falcon_Bp_RobotSingleRollerBp_Input_station_description=Target site for navigation
Falcon_Bp_RobotSingleRollerBp_Input_station_label=Target site
Falcon_Bp_RobotSingleRollerBp_Output_rbkResult_description=Response result after execution
Falcon_Bp_RobotSingleRollerBp_Output_rbkResult_label=Rbk request result
Falcon_Bp_RobotSingleRollerBp_description=Operating roller device
Falcon_Bp_RobotSingleRollerBp_label=Roller
Falcon_Bp_RobotSingleRotateBp_Input_angle_description=Control the angle of robot rotation
Falcon_Bp_RobotSingleRotateBp_Input_angle_label=Rotate angle (°)
Falcon_Bp_RobotSingleRotateBp_Input_mode_description=The mileage mode moves according to the mileage, and the positioning mode requires precise position. Default is the mileage mode
Falcon_Bp_RobotSingleRotateBp_Input_mode_label=Mode
Falcon_Bp_RobotSingleRotateBp_Input_reqId_description=Identity when the component is executing, optional
Falcon_Bp_RobotSingleRotateBp_Input_reqId_label=Task ID
Falcon_Bp_RobotSingleRotateBp_Input_vw_description=Control the angular velocity of robot, default value 45.0 (°/s)
Falcon_Bp_RobotSingleRotateBp_Input_vw_label=Angular velocity (°/s)
Falcon_Bp_RobotSingleRotateBp_Output_rbkResult_description=Response result after execution
Falcon_Bp_RobotSingleRotateBp_Output_rbkResult_label=Rbk request result
Falcon_Bp_RobotSingleRotateBp_description=Control the robot to rotate in place
Falcon_Bp_RobotSingleRotateBp_label=Rotate
Falcon_Bp_RobotSingleSetDOBp_Input_id_description=Specify the number of DO
Falcon_Bp_RobotSingleSetDOBp_Input_id_label=DO number
Falcon_Bp_RobotSingleSetDOBp_Input_status_description=Modify the status of the specified DO to target status.
Falcon_Bp_RobotSingleSetDOBp_Input_status_label=Target status
Falcon_Bp_RobotSingleSetDOBp_Output_rbkResult_description=Response result after execution
Falcon_Bp_RobotSingleSetDOBp_Output_rbkResult_label=Rbk request result
Falcon_Bp_RobotSingleSetDOBp_description=Used to modify the status of a specified DO
Falcon_Bp_RobotSingleSetDOBp_label=Set DO
Falcon_Bp_RobotSingleStopAudioBp_Input_reqId_description=Identity when the component is executing, optional
Falcon_Bp_RobotSingleStopAudioBp_Input_reqId_label=Task ID
Falcon_Bp_RobotSingleStopAudioBp_Input_station_description=Target site for navigation
Falcon_Bp_RobotSingleStopAudioBp_Input_station_label=Target site
Falcon_Bp_RobotSingleStopAudioBp_Output_rbkResult_description=Response result after execution
Falcon_Bp_RobotSingleStopAudioBp_Output_rbkResult_label=Rbk request result
Falcon_Bp_RobotSingleStopAudioBp_description=Control the robot to stop playing audio
Falcon_Bp_RobotSingleStopAudioBp_label=Stop playing autio
Falcon_Bp_RobotSingleTranslationBp_Input_dist_description=Used to limit the distance of robot movement
Falcon_Bp_RobotSingleTranslationBp_Input_dist_label=Linear motion distance (unit: m)
Falcon_Bp_RobotSingleTranslationBp_Input_mode_description=The mileage mode moves according to the mileage, and the positioning mode requires precise position. Default is the mileage mode
Falcon_Bp_RobotSingleTranslationBp_Input_mode_label=Mode
Falcon_Bp_RobotSingleTranslationBp_Input_reqId_description=Identity when the component is executing, optional
Falcon_Bp_RobotSingleTranslationBp_Input_reqId_label=Task ID
Falcon_Bp_RobotSingleTranslationBp_Input_vx_description=Control the speed of forward and backward movement, negative value is backward, positive value is forward, default value is 0.5 (unit: m/s)
Falcon_Bp_RobotSingleTranslationBp_Input_vx_label=Forward speed (vx, unit: m/s)
Falcon_Bp_RobotSingleTranslationBp_Input_vy_description=Control the speed in the left and right directions, only useful for omnidirectional vehicles, all other models are 0, default value is 0.0 (unit: m/s)
Falcon_Bp_RobotSingleTranslationBp_Input_vy_label=Traverse speed (vy, unit: m/s)
Falcon_Bp_RobotSingleTranslationBp_Output_rbkResult_description=Response result after execution
Falcon_Bp_RobotSingleTranslationBp_Output_rbkResult_label=Rbk request result
Falcon_Bp_RobotSingleTranslationBp_description=Controlling the robot in linear motion
Falcon_Bp_RobotSingleTranslationBp_label=Translation
Falcon_Bp_RobotSingleUserDefinedBp_Input_apiNo_description=API number, default value is 3051.
Falcon_Bp_RobotSingleUserDefinedBp_Input_apiNo_label=API number
Falcon_Bp_RobotSingleUserDefinedBp_Input_reqId_description=Identity when the component is executing, optional
Falcon_Bp_RobotSingleUserDefinedBp_Input_reqId_label=Task ID
Falcon_Bp_RobotSingleUserDefinedBp_Input_userDefinedAction_description=Customed operation details (in the form of JSON string)
Falcon_Bp_RobotSingleUserDefinedBp_Input_userDefinedAction_label=Operation
Falcon_Bp_RobotSingleUserDefinedBp_Output_rbkResult_description=Response result after execution
Falcon_Bp_RobotSingleUserDefinedBp_Output_rbkResult_label=Rbk request result
Falcon_Bp_RobotSingleUserDefinedBp_description=Control robot to execute a customed operation
Falcon_Bp_RobotSingleUserDefinedBp_label=Customed operation
Falcon_Bp_RobotSingleWaitDIBp_Input_id_description=Number of the specified DI
Falcon_Bp_RobotSingleWaitDIBp_Input_id_label=DI number
Falcon_Bp_RobotSingleWaitDIBp_Input_status_description=When the status of the DI is the expected status, it indicates that the DI is triggered.
Falcon_Bp_RobotSingleWaitDIBp_Input_status_label=Expected status.
Falcon_Bp_RobotSingleWaitDIBp_Input_timeout_description=Timeout time to wait for DI to be triggered. If not filled, wait forever.
Falcon_Bp_RobotSingleWaitDIBp_Input_timeout_label=Timeout (s)
Falcon_Bp_RobotSingleWaitDIBp_Output_rbkResult_description=Response result after execution
Falcon_Bp_RobotSingleWaitDIBp_Output_rbkResult_label=Rbk request result
Falcon_Bp_RobotSingleWaitDIBp_description=Waiting for DI to reach the expected status.
Falcon_Bp_RobotSingleWaitDIBp_label=Wait DI
errAwaitMove=Waiting for navigation to complete, but navigation failed, specific reason: {0}
errCreateStoOrderExists=The transportation order already exists
errDirectRobotOrderFailed=Direct transport order failed, ID
errFailedToFetchMapFromRobot=Failed to get map from bot {0}
errFetchChassisMode=Failed to get chassis drive type
errFetchCurrentMapFail=Failed to get the current map of the robot, robot 
errFetchMapFail=Failed to read robot map, robot
errGwNoLocalRobot=Gateway does not have robot {0}
errMove3051=Sending 3051 path navigation failed: {0}
errMove3066=Sending 3066 path navigation failed: {0}
errMoveRobotFromToStation=Robot {0} is currently in {1}, please move it back to the starting point {2} or ending point {3} and try again
errNoAnyRobot=There are no robots
errNoDefaultMap=Cannot find default map (first area, first vehicle model)
errNoReportApplyUrl=No reporting address specified
errNoRobot=No such robot "{0}"
errNoSuchBinOrLocation=Cannot find site or location named {0}
errNotRobotInTom=There is no robot {1} in scheduling {0}.
errPositionToNoSite=No stations near location ({0}, {1})
errQuery3066BadTaskId=Query result 3066, task ID does not match, expected {0}, actual {1}
errRequestControl=Request for control failed, robot
errRetryButOrderNotFailed=Retry, but the order status is not failed
errRobotAppNoControlPower=No control
errRobotAppNoScene=Scene not configured
errRobotAppNoSceneByName=No such scene: {0}
errRobotAppNoSingleScene=Scene {0} is not a QuickGo application
errRobotAppSceneDisabled=Scene {0} is disabled
errRobotHasSimpleOrderCurrent=Robot {0} is still executing transport order {1} and cannot receive new orders
errRobotMoveNoStart=Cannot find a suitable starting point for robot {0} to perform the task
errRobotNavNoId=Path navigation request error, destination parameter"id "is required.
errRobotNavNoSourceId=Path navigation request error, need starting point parameter "source_id"
errRobotNoAnyScene=There are currently no scenes
errRobotNoConnector=No connector with robot "{0}"
errRobotNoCurrentStation=Robot {0} is not currently on a site
errRobotNoPosition=Unable to obtain the location of the robot "{0}", please confirm that the robot is at point
errRobotOffline=Robot "{0}" is not online or lost contact
errRobotUpdateBusy=The robot is working and cannot be updated
errStoHikNotSupported=Simple Transport Order does not support Hikvision robots 
errStoNoRobot=Create a transportation order {1} Failed, there is no robot {0} in the gateway
errStoRetryFailedBadCurrentStatus=The current status of transportation order number {0} is not "failed" but "{1}"
errStoRetryFailedNoCurrent=The robot currently has no waybill transportation order
errTomDisconnected=Scheduling server does not exist, or connection to it failed: {0}
errTomOrderAwaitNotFound=Unable to find the dispatch waybill "{0}" after sending
errTomOrderKeyRouteMissing=At least one key location must be specified
errTomOrderNoVehicle=Transport order "{0}" has not been assigned a robot (Transport order status: {1})
errTomOrderNoVehicle2=Transport order "{0}" has not been assigned a robot
errUnsupportedFetchMap=The following connection method is not supported to obtain the map: {0}
errUnsupportedFetchMap2=Not supported for obtaining maps in this scenario
errorLocationEmpty=Location cannot be empty
errorTomOrderIdEmpty=Dispatch transport order ID cannot be empty
entity.DemoEntity.fields.componentTableField.tip=Component Table Component Table Component Table Component Table Component Table Component Table Component Table
entity.DemoEntity.fields.fileField.tip=Upload file Upload file Upload file Upload file Upload file Upload file Upload file Upload file
entity.DemoEntity.fields.floatField.tip=Input instructions Input instructions Input instructions Input instructions Input instructions Input instructions
entity.DemoEntity.fields.id.tip=Input instructions
entity.DemoEntity.fields.imageField.tip=Upload file Upload file Upload file Upload file Upload file Upload file
entity.DemoEntity.fields.stringField.tip=Input instructions
i18n_entity.AgentUser.fields.appId.label=appId
i18n_entity.AgentUser.fields.appKey.label=appKey
i18n_entity.AgentUser.fields.btDisabled.label=Disabled
i18n_entity.AgentUser.fields.createdBy.label=Created by
i18n_entity.AgentUser.fields.createdOn.label=Created time
i18n_entity.AgentUser.fields.id.label=ID
i18n_entity.AgentUser.fields.modifiedBy.label=Last modified by
i18n_entity.AgentUser.fields.modifiedOn.label=Last modified time
i18n_entity.AgentUser.fields.remark.label=Remarks
i18n_entity.AgentUser.fields.version.label=Revised version
i18n_entity.AgentUser.group=Core
i18n_entity.AgentUser.label=Agency account
i18n_entity.ApiCallTrace.fields.apiType.inlineOptionBill.items.HTTP.label=HTTP
i18n_entity.ApiCallTrace.fields.apiType.label=Interface type
i18n_entity.ApiCallTrace.fields.costTime.label=Time consuming
i18n_entity.ApiCallTrace.fields.createdBy.label=Created by
i18n_entity.ApiCallTrace.fields.createdOn.label=Created time
i18n_entity.ApiCallTrace.fields.httpMethod.inlineOptionBill.items.DELETE.label=DELETE
i18n_entity.ApiCallTrace.fields.httpMethod.inlineOptionBill.items.GET.label=GET
i18n_entity.ApiCallTrace.fields.httpMethod.inlineOptionBill.items.POST.label=POST
i18n_entity.ApiCallTrace.fields.httpMethod.inlineOptionBill.items.PUT.label=PUT
i18n_entity.ApiCallTrace.fields.httpMethod.label=HTTP methods
i18n_entity.ApiCallTrace.fields.httpPath.label=HTTP path
i18n_entity.ApiCallTrace.fields.httpUrl.label=Request URL
i18n_entity.ApiCallTrace.fields.id.label=ID
i18n_entity.ApiCallTrace.fields.modifiedBy.label=Last modified by
i18n_entity.ApiCallTrace.fields.modifiedOn.label=Last modified time
i18n_entity.ApiCallTrace.fields.reqBody.label=Request body
i18n_entity.ApiCallTrace.fields.reqBodyOn.label=Record request body
i18n_entity.ApiCallTrace.fields.reqEndOn.label=Request end on
i18n_entity.ApiCallTrace.fields.reqIp.label=Request IP
i18n_entity.ApiCallTrace.fields.reqStartOn.label=Request start on
i18n_entity.ApiCallTrace.fields.reqUser.label=Request user
i18n_entity.ApiCallTrace.fields.resBody.label=Response body
i18n_entity.ApiCallTrace.fields.resBodyOn.label=Record response body
i18n_entity.ApiCallTrace.fields.resCode.label=Response code
i18n_entity.ApiCallTrace.fields.version.label=Revised version
i18n_entity.ApiCallTrace.group=Core
i18n_entity.ApiCallTrace.label=Interface call record
i18n_entity.ApiCallTrace.pagesButtons.ListMain.buttons[0].label=Delete
i18n_entity.ApiCallTrace.pagesButtons.ListMain.buttons[1].label=Export
i18n_entity.ApiCallTrace.pagesButtons.ListMain.buttons[2].label=Configure
i18n_entity.CallEmptyContainerOrder.fields.createdBy.label=Created by
i18n_entity.CallEmptyContainerOrder.fields.createdOn.label=Created time
i18n_entity.CallEmptyContainerOrder.fields.district.label=District
i18n_entity.CallEmptyContainerOrder.fields.id.label=Order ID
i18n_entity.CallEmptyContainerOrder.fields.modifiedBy.label=Last modified by
i18n_entity.CallEmptyContainerOrder.fields.modifiedOn.label=Last modified time
i18n_entity.CallEmptyContainerOrder.fields.num.label=Number
i18n_entity.CallEmptyContainerOrder.fields.version.label=Revised version
i18n_entity.CallEmptyContainerOrder.group=Warehouse
i18n_entity.CallEmptyContainerOrder.label=Call empty container
i18n_entity.CallEmptyContainerOrder.listCard.district.prefix=District
i18n_entity.CallEmptyContainerOrder.listCard.num.prefix=Number
i18n_entity.CallEmptyContainerOrder.pagesButtons.Create.buttons[0].label=Submit
i18n_entity.ContainerTransportOrder.fields.atPort.label=At port
i18n_entity.ContainerTransportOrder.fields.container.label=Container
i18n_entity.ContainerTransportOrder.fields.createdBy.label=Created by
i18n_entity.ContainerTransportOrder.fields.createdOn.label=Created time
i18n_entity.ContainerTransportOrder.fields.doneOn.label=Completion time
i18n_entity.ContainerTransportOrder.fields.errMsg.label=Error message
i18n_entity.ContainerTransportOrder.fields.expectedRobot.label=Expected robot
i18n_entity.ContainerTransportOrder.fields.falconTaskDefId.label=Falcon task definition ID
i18n_entity.ContainerTransportOrder.fields.falconTaskDefLabel.label=Falcon task definition name
i18n_entity.ContainerTransportOrder.fields.falconTaskId.label=Falcon task record ID
i18n_entity.ContainerTransportOrder.fields.fromBin.label=From bin
i18n_entity.ContainerTransportOrder.fields.fromChannel.label=From bin
i18n_entity.ContainerTransportOrder.fields.id.label=ID
i18n_entity.ContainerTransportOrder.fields.kind.label=Type
i18n_entity.ContainerTransportOrder.fields.loaded.label=Loaded
i18n_entity.ContainerTransportOrder.fields.modifiedBy.label=Last modified by
i18n_entity.ContainerTransportOrder.fields.modifiedOn.label=Last modified time
i18n_entity.ContainerTransportOrder.fields.postProcessMark.label=Processing mark
i18n_entity.ContainerTransportOrder.fields.priority.label=Priority
i18n_entity.ContainerTransportOrder.fields.remark.label=Remarks
i18n_entity.ContainerTransportOrder.fields.robotName.label=Execution robot
i18n_entity.ContainerTransportOrder.fields.sourceOrderId.label=Related order
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Assigned.label=Dispatched
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Building.label=Not submitted
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Cancelled.label=Cancel
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Created.label=Submitted
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Done.label=Complete
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Failed.label=Failed
i18n_entity.ContainerTransportOrder.fields.status.label=Status
i18n_entity.ContainerTransportOrder.fields.toBin.label=End bin
i18n_entity.ContainerTransportOrder.fields.toChannel.label=End bin
i18n_entity.ContainerTransportOrder.fields.unloaded.label=Unloaded
i18n_entity.ContainerTransportOrder.fields.version.label=Revised version
i18n_entity.ContainerTransportOrder.group=WCS
i18n_entity.ContainerTransportOrder.label=Container transport order
i18n_entity.ContainerTransportOrder.listCard.container.prefix=Container
i18n_entity.ContainerTransportOrder.listCard.doneOn.prefix=Complete
i18n_entity.ContainerTransportOrder.listCard.falconTaskId.prefix=Falcon task
i18n_entity.ContainerTransportOrder.listCard.fromBin.suffix=--->
i18n_entity.ContainerTransportOrder.listCard.priority.prefix=Priority
i18n_entity.ContainerTransportOrder.listCard.robotName.prefix=Robot
i18n_entity.ContainerTransportOrder.listCard.sourceOrderId.prefix=Related order
i18n_entity.ContainerTransportOrder.listStats.items[0].label=Failed
i18n_entity.DemoComponent.fields.btLineNo.label=Line number
i18n_entity.DemoComponent.fields.btParentId.label=Owned order
i18n_entity.DemoComponent.fields.createdBy.label=Created by
i18n_entity.DemoComponent.fields.createdOn.label=Created time
i18n_entity.DemoComponent.fields.floatValue.label=floatValue
i18n_entity.DemoComponent.fields.id.label=ID
i18n_entity.DemoComponent.fields.modifiedBy.label=Last modified by
i18n_entity.DemoComponent.fields.modifiedOn.label=Last modified time
i18n_entity.DemoComponent.fields.referenceField.label=Reference field
i18n_entity.DemoComponent.fields.ro.label=Root tissue
i18n_entity.DemoComponent.fields.stringField.label=Text
i18n_entity.DemoComponent.fields.version.label=Version
i18n_entity.DemoComponent.group=Test
i18n_entity.DemoComponent.label=Test components
i18n_entity.DemoComponentTable.fields.btLineNo.label=Line number
i18n_entity.DemoComponentTable.fields.btParentId.label=Parent order
i18n_entity.DemoComponentTable.fields.createdBy.label=Created by
i18n_entity.DemoComponentTable.fields.createdOn.label=Created time
i18n_entity.DemoComponentTable.fields.dateField.label=dateField
i18n_entity.DemoComponentTable.fields.id.label=ID
i18n_entity.DemoComponentTable.fields.intField.label=intField
i18n_entity.DemoComponentTable.fields.modifiedBy.label=Last modified by
i18n_entity.DemoComponentTable.fields.modifiedOn.label=Last modified time
i18n_entity.DemoComponentTable.fields.referenceField.label=Reference field
i18n_entity.DemoComponentTable.fields.ro.label=Root tissue
i18n_entity.DemoComponentTable.fields.stringField.label=Text
i18n_entity.DemoComponentTable.fields.version.label=Version
i18n_entity.DemoComponentTable.group=Test
i18n_entity.DemoComponentTable.label=Test component table
i18n_entity.DemoEntity.fields.booleanField.label=Boolean
i18n_entity.DemoEntity.fields.booleanListField.label=Boolean list
i18n_entity.DemoEntity.fields.checkList2Field.inlineOptionBill.items.v1.label=Option 1
i18n_entity.DemoEntity.fields.checkList2Field.inlineOptionBill.items.v2.label=Option 2
i18n_entity.DemoEntity.fields.checkList2Field.inlineOptionBill.items.v3.label=Option 3
i18n_entity.DemoEntity.fields.checkList2Field.inlineOptionBill.items.v4.label=Option 4
i18n_entity.DemoEntity.fields.checkList2Field.label=Option list (multiple choices)
i18n_entity.DemoEntity.fields.checkListField.inlineOptionBill.items.v1.label=Option 1
i18n_entity.DemoEntity.fields.checkListField.inlineOptionBill.items.v2.label=Option 2
i18n_entity.DemoEntity.fields.checkListField.inlineOptionBill.items.v3.label=Option 3
i18n_entity.DemoEntity.fields.checkListField.inlineOptionBill.items.v4.label=Option 4
i18n_entity.DemoEntity.fields.checkListField.label=List of options (single choice)
i18n_entity.DemoEntity.fields.componentField.label=Component
i18n_entity.DemoEntity.fields.componentListField.label=Component list
i18n_entity.DemoEntity.fields.componentTableField.label=Component table
i18n_entity.DemoEntity.fields.createdBy.label=Created by
i18n_entity.DemoEntity.fields.createdOn.label=Created time
i18n_entity.DemoEntity.fields.dateField.label=Date
i18n_entity.DemoEntity.fields.dateListField.label=Date list
i18n_entity.DemoEntity.fields.dateTimeField.label=DateTime
i18n_entity.DemoEntity.fields.dateTimeListField.label=DateTime list
i18n_entity.DemoEntity.fields.fileField.label=File
i18n_entity.DemoEntity.fields.fileListField.label=File list
i18n_entity.DemoEntity.fields.floatField.label=Float
i18n_entity.DemoEntity.fields.floatListField.label=Float list
i18n_entity.DemoEntity.fields.id.label=ID
i18n_entity.DemoEntity.fields.imageField.label=Picture
i18n_entity.DemoEntity.fields.imageListField.label=Image list
i18n_entity.DemoEntity.fields.intField.label=Integer
i18n_entity.DemoEntity.fields.intListField.label=Int list
i18n_entity.DemoEntity.fields.modifiedBy.label=Last modified by
i18n_entity.DemoEntity.fields.modifiedOn.label=Last modified time
i18n_entity.DemoEntity.fields.passwordField.label=Password input
i18n_entity.DemoEntity.fields.referenceField.label=Reference
i18n_entity.DemoEntity.fields.referenceListField.label=Reference list
i18n_entity.DemoEntity.fields.ro.label=Root tissue
i18n_entity.DemoEntity.fields.selectField.inlineOptionBill.items.v1.label=Option 1
i18n_entity.DemoEntity.fields.selectField.inlineOptionBill.items.v2.label=Option 2
i18n_entity.DemoEntity.fields.selectField.inlineOptionBill.items.v3.label=Option 3
i18n_entity.DemoEntity.fields.selectField.inlineOptionBill.items.v4.label=Option 4
i18n_entity.DemoEntity.fields.selectField.label=Select input
i18n_entity.DemoEntity.fields.selectListField.inlineOptionBill.items.v1.label=Option 1
i18n_entity.DemoEntity.fields.selectListField.inlineOptionBill.items.v2.label=Option 2
i18n_entity.DemoEntity.fields.selectListField.inlineOptionBill.items.v3.label=Option 3
i18n_entity.DemoEntity.fields.selectListField.inlineOptionBill.items.v4.label=Option 4
i18n_entity.DemoEntity.fields.selectListField.label=Select input (multiple values)
i18n_entity.DemoEntity.fields.stringField.label=String
i18n_entity.DemoEntity.fields.stringListField.label=String list
i18n_entity.DemoEntity.fields.textAreaField.label=TextArea
i18n_entity.DemoEntity.fields.textAreaListField.label=TextArea list
i18n_entity.DemoEntity.fields.version.label=Version
i18n_entity.DemoEntity.group=Test
i18n_entity.DemoEntity.label=Test entity
i18n_entity.DemoEntity2.fields.booleanField.label=Boolean
i18n_entity.DemoEntity2.fields.booleanListField.label=Boolean list
i18n_entity.DemoEntity2.fields.checkList2Field.inlineOptionBill.items.v1.label=Option 1
i18n_entity.DemoEntity2.fields.checkList2Field.inlineOptionBill.items.v2.label=Option 2
i18n_entity.DemoEntity2.fields.checkList2Field.inlineOptionBill.items.v3.label=Option 3
i18n_entity.DemoEntity2.fields.checkList2Field.inlineOptionBill.items.v4.label=Option 4
i18n_entity.DemoEntity2.fields.checkList2Field.label=Option list (multiple choices)
i18n_entity.DemoEntity2.fields.checkListField.inlineOptionBill.items.v1.label=Option 1
i18n_entity.DemoEntity2.fields.checkListField.inlineOptionBill.items.v2.label=Option 2
i18n_entity.DemoEntity2.fields.checkListField.inlineOptionBill.items.v3.label=Option 3
i18n_entity.DemoEntity2.fields.checkListField.inlineOptionBill.items.v4.label=Option 4
i18n_entity.DemoEntity2.fields.checkListField.label=Option list (single choice)
i18n_entity.DemoEntity2.fields.componentField.label=Component
i18n_entity.DemoEntity2.fields.componentListField.label=Component list
i18n_entity.DemoEntity2.fields.componentTableField.label=Component table
i18n_entity.DemoEntity2.fields.createdBy.label=Created by
i18n_entity.DemoEntity2.fields.createdOn.label=Created time
i18n_entity.DemoEntity2.fields.dateField.label=Date
i18n_entity.DemoEntity2.fields.dateListField.label=Date list
i18n_entity.DemoEntity2.fields.dateTimeField.label=DateTime
i18n_entity.DemoEntity2.fields.dateTimeListField.label=DateTime list
i18n_entity.DemoEntity2.fields.fileField.label=File
i18n_entity.DemoEntity2.fields.fileListField.label=File list
i18n_entity.DemoEntity2.fields.floatField.label=Float
i18n_entity.DemoEntity2.fields.floatListField.label=Float list
i18n_entity.DemoEntity2.fields.id.label=ID
i18n_entity.DemoEntity2.fields.imageField.label=Picture
i18n_entity.DemoEntity2.fields.imageListField.label=Image list
i18n_entity.DemoEntity2.fields.intField.label=Integer
i18n_entity.DemoEntity2.fields.intListField.label=Int list
i18n_entity.DemoEntity2.fields.modifiedBy.label=Last modified by
i18n_entity.DemoEntity2.fields.modifiedOn.label=Last modified time
i18n_entity.DemoEntity2.fields.passwordField.label=Password input
i18n_entity.DemoEntity2.fields.referenceField.label=Reference
i18n_entity.DemoEntity2.fields.referenceListField.label=Reference list
i18n_entity.DemoEntity2.fields.ro.label=Root tissue
i18n_entity.DemoEntity2.fields.selectField.inlineOptionBill.items.v1.label=Option 1
i18n_entity.DemoEntity2.fields.selectField.inlineOptionBill.items.v2.label=Option 2
i18n_entity.DemoEntity2.fields.selectField.inlineOptionBill.items.v3.label=Option 3
i18n_entity.DemoEntity2.fields.selectField.inlineOptionBill.items.v4.label=Option 4
i18n_entity.DemoEntity2.fields.selectField.label=Select input
i18n_entity.DemoEntity2.fields.selectListField.inlineOptionBill.items.v1.label=Option 1
i18n_entity.DemoEntity2.fields.selectListField.inlineOptionBill.items.v2.label=Option 2
i18n_entity.DemoEntity2.fields.selectListField.inlineOptionBill.items.v3.label=Option 3
i18n_entity.DemoEntity2.fields.selectListField.inlineOptionBill.items.v4.label=Option 4
i18n_entity.DemoEntity2.fields.selectListField.label=Select input (multiple values)
i18n_entity.DemoEntity2.fields.stringField.label=String
i18n_entity.DemoEntity2.fields.stringListField.label=String list
i18n_entity.DemoEntity2.fields.textAreaField.label=TextArea
i18n_entity.DemoEntity2.fields.textAreaListField.label=TextArea list
i18n_entity.DemoEntity2.fields.version.label=Version
i18n_entity.DemoEntity2.group=Test
i18n_entity.DemoEntity2.label=Test Entity 2
i18n_entity.Department.fields.createdBy.label=Created by
i18n_entity.Department.fields.createdOn.label=Created time
i18n_entity.Department.fields.disabled.label=Disabled
i18n_entity.Department.fields.id.label=ID
i18n_entity.Department.fields.leafNode.label=Leaf department
i18n_entity.Department.fields.level.label=Level
i18n_entity.Department.fields.modifiedBy.label=Last modified by
i18n_entity.Department.fields.modifiedOn.label=Last modified time
i18n_entity.Department.fields.name.label=Name
i18n_entity.Department.fields.owner.label=Owner
i18n_entity.Department.fields.parentNode.label=Superior department
i18n_entity.Department.fields.ro.label=Root tissue
i18n_entity.Department.fields.rootNode.label=Top departments
i18n_entity.Department.fields.version.label=Version
i18n_entity.Department.group=User
i18n_entity.Department.label=Department
i18n_entity.Department.listCard.owner.prefix=Owner
i18n_entity.DirectRobotOrder.fields.createdBy.label=Created by
i18n_entity.DirectRobotOrder.fields.createdOn.label=Created time
i18n_entity.DirectRobotOrder.fields.description.label=Description
i18n_entity.DirectRobotOrder.fields.doneOn.label=Done on
i18n_entity.DirectRobotOrder.fields.errMsg.label=Error message
i18n_entity.DirectRobotOrder.fields.id.label=ID
i18n_entity.DirectRobotOrder.fields.modifiedBy.label=Last modified by
i18n_entity.DirectRobotOrder.fields.modifiedOn.label=Last modified time
i18n_entity.DirectRobotOrder.fields.moves.label=Moves
i18n_entity.DirectRobotOrder.fields.robotName.label=Robot name
i18n_entity.DirectRobotOrder.fields.seer3066.label=Specified path navigation
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Cancelled.label=Cancelled
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Created.label=Create
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Done.label=Finished
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Failed.label=Failed
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.ManualDone.label=Manual finished
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Sent.label=Allocated
i18n_entity.DirectRobotOrder.fields.status.label=Status
i18n_entity.DirectRobotOrder.fields.status.listBatchButtons.buttons[0].label=Cancel
i18n_entity.DirectRobotOrder.fields.status.listBatchButtons.buttons[1].label=Completed manually
i18n_entity.DirectRobotOrder.fields.taskId.label=Associated task
i18n_entity.DirectRobotOrder.fields.version.label=Revised version
i18n_entity.DirectRobotOrder.group=WCS
i18n_entity.DirectRobotOrder.label=Direct robot order
i18n_entity.DirectRobotOrder.listCard.createdOn.prefix=Create
i18n_entity.DirectRobotOrder.listCard.description.prefix=Description
i18n_entity.DirectRobotOrder.listCard.robotName.prefix=Robot
i18n_entity.DirectRobotOrder.listCard.taskId.prefix=Associated task
i18n_entity.DirectRobotOrder.listStats.items[0].label=Create
i18n_entity.DirectRobotOrder.listStats.items[1].label=Issued
i18n_entity.DirectRobotOrder.listStats.items[2].label=Failed
i18n_entity.EmptyContainerStoreOrder.fields.container.label=Container
i18n_entity.EmptyContainerStoreOrder.fields.containerType.label=Container type
i18n_entity.EmptyContainerStoreOrder.fields.createdBy.label=Created by
i18n_entity.EmptyContainerStoreOrder.fields.createdOn.label=Created time
i18n_entity.EmptyContainerStoreOrder.fields.id.label=ID
i18n_entity.EmptyContainerStoreOrder.fields.modifiedBy.label=Last modified by
i18n_entity.EmptyContainerStoreOrder.fields.modifiedOn.label=Last modified time
i18n_entity.EmptyContainerStoreOrder.fields.storeDistrict.label=District
i18n_entity.EmptyContainerStoreOrder.fields.version.label=Revised version
i18n_entity.EmptyContainerStoreOrder.group=Warehouse
i18n_entity.EmptyContainerStoreOrder.label=Empty container store order
i18n_entity.EmptyContainerStoreOrder.listCard.container.prefix=Container
i18n_entity.EmptyContainerStoreOrder.listCard.storeDistrict.prefix=District
i18n_entity.EmptyContainerStoreOrder.pagesButtons.ListMain.buttons[0].label=Create
i18n_entity.EmptyContainerStoreOrder.pagesButtons.ListMain.buttons[1].label=Delete
i18n_entity.EmptyContainerStoreOrder.pagesButtons.ListMain.buttons[2].label=Empty box placed
i18n_entity.EntityChangedRecord.fields.changeType.label=Modification type
i18n_entity.EntityChangedRecord.fields.createdBy.label=Created by
i18n_entity.EntityChangedRecord.fields.createdOn.label=Created time
i18n_entity.EntityChangedRecord.fields.entityFields.label=Field list
i18n_entity.EntityChangedRecord.fields.entityId.label=Entity ID
i18n_entity.EntityChangedRecord.fields.entityName.label=Entity name
i18n_entity.EntityChangedRecord.fields.id.label=ID
i18n_entity.EntityChangedRecord.fields.modifiedBy.label=Last modified by
i18n_entity.EntityChangedRecord.fields.modifiedOn.label=Last modified time
i18n_entity.EntityChangedRecord.fields.version.label=Revised version
i18n_entity.EntityChangedRecord.group=Core
i18n_entity.EntityChangedRecord.label=Entity modification record
i18n_entity.EntityComment.fields.content.label=Content
i18n_entity.EntityComment.fields.createdBy.label=Created by
i18n_entity.EntityComment.fields.createdOn.label=Created time
i18n_entity.EntityComment.fields.entityId.label=entityId
i18n_entity.EntityComment.fields.entityName.label=Entity
i18n_entity.EntityComment.fields.id.label=ID
i18n_entity.EntityComment.fields.modifiedBy.label=Last modified by
i18n_entity.EntityComment.fields.modifiedOn.label=Last modified time
i18n_entity.EntityComment.fields.ro.label=Enterprise
i18n_entity.EntityComment.fields.version.label=Version
i18n_entity.EntityComment.group=Core
i18n_entity.EntityComment.label=Entity comments
i18n_entity.EntitySyncRecord.fields.bzType.label=Business type
i18n_entity.EntitySyncRecord.fields.cost.label=Costs(ms)
i18n_entity.EntitySyncRecord.fields.createdBy.label=Created by
i18n_entity.EntitySyncRecord.fields.createdCount.label=Created count
i18n_entity.EntitySyncRecord.fields.createdOn.label=Created time
i18n_entity.EntitySyncRecord.fields.deletedCount.label=Deleted count
i18n_entity.EntitySyncRecord.fields.entityName.label=Entity name
i18n_entity.EntitySyncRecord.fields.faileReason.label=Failed reason
i18n_entity.EntitySyncRecord.fields.id.label=ID
i18n_entity.EntitySyncRecord.fields.modifiedBy.label=Last modified by
i18n_entity.EntitySyncRecord.fields.modifiedOn.label=Last modified time
i18n_entity.EntitySyncRecord.fields.oldCount.label=Old count
i18n_entity.EntitySyncRecord.fields.ro.label=Enterprise
i18n_entity.EntitySyncRecord.fields.success.label=Success
i18n_entity.EntitySyncRecord.fields.syncCount.label=Synchronized count
i18n_entity.EntitySyncRecord.fields.syncOn.label=Synchronization time
i18n_entity.EntitySyncRecord.fields.txId.label=Transaction ID
i18n_entity.EntitySyncRecord.fields.updatedCount.label=Updated count
i18n_entity.EntitySyncRecord.fields.version.label=Version
i18n_entity.EntitySyncRecord.group=Core
i18n_entity.EntitySyncRecord.label=Entity synchronization record
i18n_entity.ExternalCallRecord.fields.createdBy.label=Created by
i18n_entity.ExternalCallRecord.fields.createdOn.label=Created time
i18n_entity.ExternalCallRecord.fields.doneOn.label=Done on
i18n_entity.ExternalCallRecord.fields.failedNum.label=Failed count
i18n_entity.ExternalCallRecord.fields.failedReason.label=Failed reason
i18n_entity.ExternalCallRecord.fields.id.label=ID
i18n_entity.ExternalCallRecord.fields.modifiedBy.label=Last modified by
i18n_entity.ExternalCallRecord.fields.modifiedOn.label=Last modified time
i18n_entity.ExternalCallRecord.fields.okChecker.label=Success check method
i18n_entity.ExternalCallRecord.fields.options.label=Options
i18n_entity.ExternalCallRecord.fields.req.label=Request details
i18n_entity.ExternalCallRecord.fields.resBody.label=Response body
i18n_entity.ExternalCallRecord.fields.resCode.label=Response code
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Aborted.label=Abort
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Cancelled.label=Cancelled
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Done.label=Success
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Failed.label=Failed
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Init.label=Start
i18n_entity.ExternalCallRecord.fields.status.label=Status
i18n_entity.ExternalCallRecord.fields.url.label=URL
i18n_entity.ExternalCallRecord.fields.version.label=Revised version
i18n_entity.ExternalCallRecord.group=Core
i18n_entity.ExternalCallRecord.label=Third-party call record
i18n_entity.ExternalCallTrace.fields.createdBy.label=Created by
i18n_entity.ExternalCallTrace.fields.createdOn.label=Created time
i18n_entity.ExternalCallTrace.fields.id.label=ID
i18n_entity.ExternalCallTrace.fields.ioError.label=IO Error
i18n_entity.ExternalCallTrace.fields.ioError.view.trueText=IO error
i18n_entity.ExternalCallTrace.fields.ioErrorMsg.label=IO Error Message
i18n_entity.ExternalCallTrace.fields.method.label=HTTP methods
i18n_entity.ExternalCallTrace.fields.modifiedBy.label=Last modified by
i18n_entity.ExternalCallTrace.fields.modifiedOn.label=Last modified time
i18n_entity.ExternalCallTrace.fields.reqBody.label=Request body
i18n_entity.ExternalCallTrace.fields.reqOn.label=Request time
i18n_entity.ExternalCallTrace.fields.resBody.label=Response body
i18n_entity.ExternalCallTrace.fields.resCode.label=Response code
i18n_entity.ExternalCallTrace.fields.resOn.label=Response time
i18n_entity.ExternalCallTrace.fields.url.label=URL
i18n_entity.ExternalCallTrace.fields.version.label=Revised version
i18n_entity.ExternalCallTrace.group=Core
i18n_entity.ExternalCallTrace.label=HTTP Client Log
i18n_entity.FailureRecord.fields.createdBy.label=Created by
i18n_entity.FailureRecord.fields.createdOn.label=Created time
i18n_entity.FailureRecord.fields.desc.label=Description
i18n_entity.FailureRecord.fields.firstOn.label=First occur time
i18n_entity.FailureRecord.fields.id.label=ID
i18n_entity.FailureRecord.fields.kind.label=Category
i18n_entity.FailureRecord.fields.lastOn.label=Last occur time
i18n_entity.FailureRecord.fields.level.inlineOptionBill.items.Fatal.label=Fatal
i18n_entity.FailureRecord.fields.level.inlineOptionBill.items.Warning.label=Warning
i18n_entity.FailureRecord.fields.level.label=Level
i18n_entity.FailureRecord.fields.modifiedBy.label=Last modified by
i18n_entity.FailureRecord.fields.modifiedOn.label=Last modified time
i18n_entity.FailureRecord.fields.num.label=Count
i18n_entity.FailureRecord.fields.part.label=Object
i18n_entity.FailureRecord.fields.source.label=Source
i18n_entity.FailureRecord.fields.subKind.label=Subcategory
i18n_entity.FailureRecord.fields.version.label=Revised version
i18n_entity.FailureRecord.group=Core
i18n_entity.FailureRecord.label=Fialure record
i18n_entity.FalconBlockChildId.fields.blockId.label=blockId
i18n_entity.FalconBlockChildId.fields.childId.label=childId
i18n_entity.FalconBlockChildId.fields.contextKey.label=contextKey
i18n_entity.FalconBlockChildId.fields.createdBy.label=Created by
i18n_entity.FalconBlockChildId.fields.createdOn.label=Created time
i18n_entity.FalconBlockChildId.fields.id.label=ID
i18n_entity.FalconBlockChildId.fields.index.label=index
i18n_entity.FalconBlockChildId.fields.modifiedBy.label=Last modified by
i18n_entity.FalconBlockChildId.fields.modifiedOn.label=Last modified time
i18n_entity.FalconBlockChildId.fields.taskId.label=taskId
i18n_entity.FalconBlockChildId.fields.version.label=Revised version
i18n_entity.FalconBlockChildId.group=Falcon
i18n_entity.FalconBlockChildId.label=Falcon task child block ID
i18n_entity.FalconBlockChildId.listCard.contextKey.prefix=contextKey
i18n_entity.FalconBlockChildId.listCard.createdOn.prefix=Created time
i18n_entity.FalconBlockChildId.listCard.taskId.prefix=taskId
i18n_entity.FalconBlockRecord.fields.blockConfigId.label=blockConfigId
i18n_entity.FalconBlockRecord.fields.createdBy.label=Created by
i18n_entity.FalconBlockRecord.fields.createdOn.label=Created time
i18n_entity.FalconBlockRecord.fields.endedOn.label=endedOn
i18n_entity.FalconBlockRecord.fields.endedReason.label=endedReason
i18n_entity.FalconBlockRecord.fields.failureNum.label=Failed count
i18n_entity.FalconBlockRecord.fields.id.label=ID
i18n_entity.FalconBlockRecord.fields.internalVariables.label=internalVariables
i18n_entity.FalconBlockRecord.fields.modifiedBy.label=Last modified by
i18n_entity.FalconBlockRecord.fields.modifiedOn.label=Last Modified Time
i18n_entity.FalconBlockRecord.fields.outputParams.label=outputParams
i18n_entity.FalconBlockRecord.fields.startedOn.label=startedOn
i18n_entity.FalconBlockRecord.fields.status.label=status
i18n_entity.FalconBlockRecord.fields.taskId.label=taskId
i18n_entity.FalconBlockRecord.fields.version.label=Revised version
i18n_entity.FalconBlockRecord.group=Falcon
i18n_entity.FalconBlockRecord.label=Falcon task block ID
i18n_entity.FalconBlockRecord.listCard.blockConfigId.prefix=blockConfigId
i18n_entity.FalconBlockRecord.listCard.taskId.prefix=taskId
i18n_entity.FalconLog.fields.blockId.label=Associated components
i18n_entity.FalconLog.fields.createdBy.label=Created by
i18n_entity.FalconLog.fields.createdOn.label=Created time
i18n_entity.FalconLog.fields.id.label=ID
i18n_entity.FalconLog.fields.level.inlineOptionBill.items.Debug.label=Normal
i18n_entity.FalconLog.fields.level.inlineOptionBill.items.Error.label=Error
i18n_entity.FalconLog.fields.level.inlineOptionBill.items.Info.label=Importance
i18n_entity.FalconLog.fields.level.label=Level
i18n_entity.FalconLog.fields.message.label=Message
i18n_entity.FalconLog.fields.modifiedBy.label=Last modified by
i18n_entity.FalconLog.fields.modifiedOn.label=Last modified time
i18n_entity.FalconLog.fields.taskId.label=Associated tasks
i18n_entity.FalconLog.fields.version.label=Revised version
i18n_entity.FalconLog.group=Falcon
i18n_entity.FalconLog.label=Falcon log
i18n_entity.FalconLog.listCard.blockId.prefix=Associated components
i18n_entity.FalconLog.listCard.createdOn.prefix=Create
i18n_entity.FalconRelatedObject.fields.createdBy.label=Created by
i18n_entity.FalconRelatedObject.fields.createdOn.label=Created time
i18n_entity.FalconRelatedObject.fields.id.label=ID
i18n_entity.FalconRelatedObject.fields.modifiedBy.label=Last modified by
i18n_entity.FalconRelatedObject.fields.modifiedOn.label=Last modified time
i18n_entity.FalconRelatedObject.fields.objectArgs.label=objectArgs
i18n_entity.FalconRelatedObject.fields.objectId.label=objectId
i18n_entity.FalconRelatedObject.fields.objectType.label=objectType
i18n_entity.FalconRelatedObject.fields.taskId.label=taskId
i18n_entity.FalconRelatedObject.fields.version.label=Revised version
i18n_entity.FalconRelatedObject.group=Falcon
i18n_entity.FalconRelatedObject.label=Associated falcon task objects
i18n_entity.FalconRelatedObject.listCard.objectArgs.prefix=objectArgs
i18n_entity.FalconRelatedObject.listCard.objectId.prefix=objectId
i18n_entity.FalconTaskRecord.fields.createdBy.label=Created by
i18n_entity.FalconTaskRecord.fields.createdOn.label=Created time
i18n_entity.FalconTaskRecord.fields.defId.label=Falcon task definition ID
i18n_entity.FalconTaskRecord.fields.defLabel.label=Task Template
i18n_entity.FalconTaskRecord.fields.defVersion.label=Template version
i18n_entity.FalconTaskRecord.fields.endedOn.label=End time
i18n_entity.FalconTaskRecord.fields.endedReason.label=Error message
i18n_entity.FalconTaskRecord.fields.failureNum.label=Error count
i18n_entity.FalconTaskRecord.fields.id.label=ID
i18n_entity.FalconTaskRecord.fields.inputParams.label=Input parameters
i18n_entity.FalconTaskRecord.fields.modifiedBy.label=Last modified by
i18n_entity.FalconTaskRecord.fields.modifiedOn.label=Last modified time
i18n_entity.FalconTaskRecord.fields.paused.label=Paused
i18n_entity.FalconTaskRecord.fields.ro.label=RO
i18n_entity.FalconTaskRecord.fields.rootBlockStateId.label=Root block ID
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.100.label=Created
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.120.label=Started
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.140.label=Failure
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.160.label=Finished
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.180.label=Cancelled
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.190.label=Failed
i18n_entity.FalconTaskRecord.fields.status.label=Status
i18n_entity.FalconTaskRecord.fields.subTask.label=Subtask
i18n_entity.FalconTaskRecord.fields.topTaskId.label=Top-level task ID
i18n_entity.FalconTaskRecord.fields.variables.label=Task variables
i18n_entity.FalconTaskRecord.fields.version.label=Revised version
i18n_entity.FalconTaskRecord.group=Falcon
i18n_entity.FalconTaskRecord.label=Falcon task record
i18n_entity.FalconTaskRecord.listCard.createdOn.prefix=Create
i18n_entity.FalconTaskRecord.listCard.defLabel.prefix=Template
i18n_entity.FalconTaskRecord.listCard.defVersion.prefix=(
i18n_entity.FalconTaskRecord.listCard.defVersion.suffix=)
i18n_entity.FalconTaskRecord.listCard.endedOn.prefix=End
i18n_entity.FalconTaskRecord.listStats.items[0].label=Failure
i18n_entity.FalconTaskRecord.listStats.items[1].label=Paused
i18n_entity.FalconTaskRecord.listStats.items[2].label=Created Today
i18n_entity.FalconTaskRecord.listStats.items[3].label=Failed/Canceled today
i18n_entity.FalconTaskRecord.listStats.items[4].label=Created this week
i18n_entity.FalconTaskRecord.listStats.items[5].label=Failed/Canceled this week
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[0].label=Delete
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[1].label=Export
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[2].label=Pasue
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[3].label=Resume
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[4].label=Retry
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[5].label=Cancel
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[6].label=Delete
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[7].label=Template
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[8].label=Global control
i18n_entity.FalconTaskResource.fields.args.label=Parameter
i18n_entity.FalconTaskResource.fields.createdBy.label=Created by
i18n_entity.FalconTaskResource.fields.createdOn.label=Created time
i18n_entity.FalconTaskResource.fields.id.label=ID
i18n_entity.FalconTaskResource.fields.modifiedBy.label=Last modified by
i18n_entity.FalconTaskResource.fields.modifiedOn.label=Last modified time
i18n_entity.FalconTaskResource.fields.resId.label=Resource ID
i18n_entity.FalconTaskResource.fields.resType.label=Resource type
i18n_entity.FalconTaskResource.fields.taskId.label=Task ID
i18n_entity.FalconTaskResource.fields.version.label=Revised version
i18n_entity.FalconTaskResource.group=Falcon
i18n_entity.FalconTaskResource.label=Falcon task resources
i18n_entity.FalconTaskResource.listCard.resId.prefix=Resource ID
i18n_entity.FbAssemblyLine.fields.building.label=Building
i18n_entity.FbAssemblyLine.fields.buildingLayer.label=Floor
i18n_entity.FbAssemblyLine.fields.createdBy.label=Created by
i18n_entity.FbAssemblyLine.fields.createdOn.label=Created time
i18n_entity.FbAssemblyLine.fields.disabled.label=Disable
i18n_entity.FbAssemblyLine.fields.id.label=Order ID
i18n_entity.FbAssemblyLine.fields.modifiedBy.label=Last modified by
i18n_entity.FbAssemblyLine.fields.modifiedOn.label=Last modified time
i18n_entity.FbAssemblyLine.fields.name.label=Name
i18n_entity.FbAssemblyLine.fields.remark.label=Remarks
i18n_entity.FbAssemblyLine.fields.ro.label=Root tissue
i18n_entity.FbAssemblyLine.fields.version.label=Version
i18n_entity.FbAssemblyLine.group=MainData
i18n_entity.FbAssemblyLine.label=Assembly line
i18n_entity.FbBin.fields.assemblyLine.label=Associated assembly line
i18n_entity.FbBin.fields.boxDirection.label=Fork direction
i18n_entity.FbBin.fields.boxHeight.label=Fork lift height
i18n_entity.FbBin.fields.btDisabled.label=Disable
i18n_entity.FbBin.fields.channel.label=Aisle
i18n_entity.FbBin.fields.column.label=Column
i18n_entity.FbBin.fields.container.label=Bin container
i18n_entity.FbBin.fields.createdBy.label=Created by
i18n_entity.FbBin.fields.createdOn.label=Created time
i18n_entity.FbBin.fields.depth.label=Depth
i18n_entity.FbBin.fields.district.label=Associated district
i18n_entity.FbBin.fields.id.label=Bin ID
i18n_entity.FbBin.fields.layer.label=Layer
i18n_entity.FbBin.fields.loadStatus.inlineOptionBill.items.Empty.label=Not occupied
i18n_entity.FbBin.fields.loadStatus.inlineOptionBill.items.Occupied.label=Occupied
i18n_entity.FbBin.fields.loadStatus.inlineOptionBill.items.ToLoad.label=To be loaded
i18n_entity.FbBin.fields.loadStatus.inlineOptionBill.items.ToUnload.label=To be unload
i18n_entity.FbBin.fields.loadStatus.label=Occupation status
i18n_entity.FbBin.fields.locked.label=Lock
i18n_entity.FbBin.fields.locked.listBatchButtons.buttons[0].label=Unlock
i18n_entity.FbBin.fields.lockedBy.label=Locked reason
i18n_entity.FbBin.fields.materialCategoryLabel.label=Material category name
i18n_entity.FbBin.fields.modifiedBy.label=Last modified by
i18n_entity.FbBin.fields.modifiedOn.label=Last modified time
i18n_entity.FbBin.fields.occupied.label=Occupied
i18n_entity.FbBin.fields.occupied.view.trueText=Occupied
i18n_entity.FbBin.fields.pendingContainer.label=Container to be Load
i18n_entity.FbBin.fields.purpose.label=Purpose
i18n_entity.FbBin.fields.rack.label=Owned rack
i18n_entity.FbBin.fields.remark.label=Remarks
i18n_entity.FbBin.fields.ro.label=Root tissue
i18n_entity.FbBin.fields.robotDirection.label=Robot direction
i18n_entity.FbBin.fields.robotX.label=Robot position X
i18n_entity.FbBin.fields.robotY.label=Robot position Y
i18n_entity.FbBin.fields.row.label=Row
i18n_entity.FbBin.fields.version.label=Version
i18n_entity.FbBin.fields.warehouse.label=Owned warehouse
i18n_entity.FbBin.fields.workSite.label=Owned workstation
i18n_entity.FbBin.group=MainData
i18n_entity.FbBin.label=Bin
i18n_entity.FbBin.listCard.container.prefix=Bin container
i18n_entity.FbBin.listCard.district.prefix=Associated district
i18n_entity.FbBin.listStats.items[0].label=Locked
i18n_entity.FbBin.listStats.items[1].label=Occupied
i18n_entity.FbBin.listStats.items[2].label=To be loaded
i18n_entity.FbBin.listStats.items[3].label=To be unload
i18n_entity.FbBin.pagesButtons.ListMain.buttons[0].label=Add
i18n_entity.FbBin.pagesButtons.ListMain.buttons[1].label=Batch edit
i18n_entity.FbBin.pagesButtons.ListMain.buttons[2].label=Delete
i18n_entity.FbBin.pagesButtons.ListMain.buttons[3].label=Export
i18n_entity.FbBin.pagesButtons.ListMain.buttons[4].label=Import
i18n_entity.FbBin.pagesButtons.ListMain.buttons[5].label=Batch create bins
i18n_entity.FbContainer.fields.bin.label=Bin
i18n_entity.FbContainer.fields.btDisabled.label=Disable
i18n_entity.FbContainer.fields.createdBy.label=Created by
i18n_entity.FbContainer.fields.createdOn.label=Created time
i18n_entity.FbContainer.fields.district.label=Current district
i18n_entity.FbContainer.fields.filled.label=Occupied
i18n_entity.FbContainer.fields.fixedStoreBin.label=Specify bin
i18n_entity.FbContainer.fields.height.label=Container height
i18n_entity.FbContainer.fields.id.label=Container ID
i18n_entity.FbContainer.fields.length.label=Container length
i18n_entity.FbContainer.fields.locked.label=Lock
i18n_entity.FbContainer.fields.locked.listBatchButtons.buttons[0].label=Unlock
i18n_entity.FbContainer.fields.maxWeight.label=Container max weight
i18n_entity.FbContainer.fields.modifiedBy.label=Last modified by
i18n_entity.FbContainer.fields.modifiedOn.label=Last modified time
i18n_entity.FbContainer.fields.onRobot.label=Robot
i18n_entity.FbContainer.fields.pContainer.label=Parent container
i18n_entity.FbContainer.fields.preBin.label=Scheduled bin
i18n_entity.FbContainer.fields.qcResult.label=QC result
i18n_entity.FbContainer.fields.remark.label=Remarks
i18n_entity.FbContainer.fields.ro.label=Root tissue
i18n_entity.FbContainer.fields.state.label=Status
i18n_entity.FbContainer.fields.subNum.label=Cell qty
i18n_entity.FbContainer.fields.targetBin.label=To bin
i18n_entity.FbContainer.fields.taskType.inlineOptionBill.items.Count.label=Counting
i18n_entity.FbContainer.fields.taskType.inlineOptionBill.items.Pick.label=To be sorted
i18n_entity.FbContainer.fields.taskType.inlineOptionBill.items.Put.label=To be packed
i18n_entity.FbContainer.fields.taskType.label=Task type
i18n_entity.FbContainer.fields.type.label=Container type
i18n_entity.FbContainer.fields.version.label=Version
i18n_entity.FbContainer.fields.volume.label=Container volume
i18n_entity.FbContainer.fields.warehouse.label=Current warehouse
i18n_entity.FbContainer.fields.width.label=Container width
i18n_entity.FbContainer.fields.workStatus.inlineOptionBill.items.ToPick.label=To be picked
i18n_entity.FbContainer.fields.workStatus.inlineOptionBill.items.ToPut.label=To be loaded
i18n_entity.FbContainer.fields.workStatus.label=Work status
i18n_entity.FbContainer.group=MainData
i18n_entity.FbContainer.label=Container
i18n_entity.FbContainer.listCard.bin.prefix=Bin
i18n_entity.FbContainer.listCard.type.prefix=Type
i18n_entity.FbContainer.listStats.items[0].label=Processing (locked)
i18n_entity.FbContainer.listStats.items[1].label=Occupied
i18n_entity.FbContainer.listStats.items[2].label=Not in the bin
i18n_entity.FbContainerType.fields.btDisabled.label=Disable
i18n_entity.FbContainerType.fields.createdBy.label=Created by
i18n_entity.FbContainerType.fields.createdOn.label=Created time
i18n_entity.FbContainerType.fields.id.label=Container type ID
i18n_entity.FbContainerType.fields.mixedMaterial.label=Mixed material
i18n_entity.FbContainerType.fields.modifiedBy.label=Last modified by
i18n_entity.FbContainerType.fields.modifiedOn.label=Last modified time
i18n_entity.FbContainerType.fields.name.label=Container type name
i18n_entity.FbContainerType.fields.remark.label=Remark
i18n_entity.FbContainerType.fields.ro.label=Root tissue
i18n_entity.FbContainerType.fields.storeDistricts.label=District
i18n_entity.FbContainerType.fields.subNum.label=Cell qty
i18n_entity.FbContainerType.fields.version.label=Version
i18n_entity.FbContainerType.group=MainData
i18n_entity.FbContainerType.label=Container type
i18n_entity.FbContainerType.listCard.storeDistricts.prefix=District
i18n_entity.FbCountDiffLine.fields.actualQty.label=Actual qty
i18n_entity.FbCountDiffLine.fields.bin.label=Original bin
i18n_entity.FbCountDiffLine.fields.btLineNo.label=Line number
i18n_entity.FbCountDiffLine.fields.btMaterial.label=Material
i18n_entity.FbCountDiffLine.fields.btMaterialCategory.label=Material type ID
i18n_entity.FbCountDiffLine.fields.btMaterialCategoryName.label=Material type name
i18n_entity.FbCountDiffLine.fields.btMaterialId.label=Material ID
i18n_entity.FbCountDiffLine.fields.btMaterialImage.label=Material image
i18n_entity.FbCountDiffLine.fields.btMaterialModel.label=Material model
i18n_entity.FbCountDiffLine.fields.btMaterialName.label=Material name
i18n_entity.FbCountDiffLine.fields.btMaterialSpec.label=Material specs.
i18n_entity.FbCountDiffLine.fields.btParentId.label=Owned order
i18n_entity.FbCountDiffLine.fields.container.label=Container
i18n_entity.FbCountDiffLine.fields.createdBy.label=Created by
i18n_entity.FbCountDiffLine.fields.createdOn.label=Created time
i18n_entity.FbCountDiffLine.fields.diffQty.label=Differences qty
i18n_entity.FbCountDiffLine.fields.fix.label=Execute
i18n_entity.FbCountDiffLine.fields.id.label=ID
i18n_entity.FbCountDiffLine.fields.lotNo.label=Lot No.
i18n_entity.FbCountDiffLine.fields.modifiedBy.label=Last modified by
i18n_entity.FbCountDiffLine.fields.modifiedOn.label=Modification time
i18n_entity.FbCountDiffLine.fields.qty.label=Original quantity
i18n_entity.FbCountDiffLine.fields.qualityLevel.label=Quality grade
i18n_entity.FbCountDiffLine.fields.remark.label=Remarks
i18n_entity.FbCountDiffLine.fields.sourceInvLayout.label=Related inventory details
i18n_entity.FbCountDiffLine.fields.subContainerId.label=Cell ID
i18n_entity.FbCountDiffLine.fields.taskId.label=Count task
i18n_entity.FbCountDiffLine.fields.version.label=Modified version
i18n_entity.FbCountDiffLine.group=Warehouse
i18n_entity.FbCountDiffLine.label=Count difference list
i18n_entity.FbCountDiffLine.listCard.btMaterial.prefix=Material
i18n_entity.FbCountDiffLine.listCard.subContainerId.prefix=Cell ID
i18n_entity.FbCountFix.fields.btMaterial.label=Material
i18n_entity.FbCountFix.fields.btMaterialCategory.label=Material type ID
i18n_entity.FbCountFix.fields.btMaterialCategoryName.label=Material type name
i18n_entity.FbCountFix.fields.btMaterialId.label=Material ID
i18n_entity.FbCountFix.fields.btMaterialImage.label=Material image
i18n_entity.FbCountFix.fields.btMaterialModel.label=Material model
i18n_entity.FbCountFix.fields.btMaterialName.label=Material name
i18n_entity.FbCountFix.fields.btMaterialSpec.label=Material specs.
i18n_entity.FbCountFix.fields.container.label=Container
i18n_entity.FbCountFix.fields.createdBy.label=Created by
i18n_entity.FbCountFix.fields.createdOn.label=Created time
i18n_entity.FbCountFix.fields.id.label=ID
i18n_entity.FbCountFix.fields.lotNo.label=Lot No.
i18n_entity.FbCountFix.fields.modifiedBy.label=Last modified by
i18n_entity.FbCountFix.fields.modifiedOn.label=Modification time
i18n_entity.FbCountFix.fields.qty.label=Changes qty
i18n_entity.FbCountFix.fields.qualityLevel.label=Quality grade
i18n_entity.FbCountFix.fields.remark.label=Remarks
i18n_entity.FbCountFix.fields.subContainerId.label=Cell ID
i18n_entity.FbCountFix.fields.version.label=Modified version
i18n_entity.FbCountFix.group=Warehouse
i18n_entity.FbCountFix.label=Inventory correction record
i18n_entity.FbCountFix.listCard.container.prefix=Container
i18n_entity.FbCountFix.listCard.qty.prefix=Changes qty
i18n_entity.FbCountFix.listCard.subContainerId.prefix=Cell ID
i18n_entity.FbCountOrder.fields.bins.label=Bin list
i18n_entity.FbCountOrder.fields.btOrderState.inlineOptionBill.items.Done.label=Completed
i18n_entity.FbCountOrder.fields.btOrderState.inlineOptionBill.items.Init.label=Not submitted
i18n_entity.FbCountOrder.fields.btOrderState.inlineOptionBill.items.Submitted.label=Submitted
i18n_entity.FbCountOrder.fields.btOrderState.label=Status
i18n_entity.FbCountOrder.fields.btOrderStateReason.label=Status description
i18n_entity.FbCountOrder.fields.containers.label=Container list
i18n_entity.FbCountOrder.fields.createdBy.label=Created by
i18n_entity.FbCountOrder.fields.createdOn.label=Created time
i18n_entity.FbCountOrder.fields.diffLines.label=Count difference list
i18n_entity.FbCountOrder.fields.districts.label=Districts list
i18n_entity.FbCountOrder.fields.doneProcessed.label=Count difference has been processed
i18n_entity.FbCountOrder.fields.id.label=Order No.
i18n_entity.FbCountOrder.fields.materials.label=Material list
i18n_entity.FbCountOrder.fields.modifiedBy.label=Last modified by
i18n_entity.FbCountOrder.fields.modifiedOn.label=Modification time
i18n_entity.FbCountOrder.fields.remark.label=Remark
i18n_entity.FbCountOrder.fields.taskGenerated.label=Count task has been produced
i18n_entity.FbCountOrder.fields.taskLines.label=Task statistics line
i18n_entity.FbCountOrder.fields.version.label=Modified version
i18n_entity.FbCountOrder.group=Warehouse
i18n_entity.FbCountOrder.label=Count order
i18n_entity.FbCountOrder.states.states.Done.label=Completed
i18n_entity.FbCountOrder.states.states.Init.label=Not submitted
i18n_entity.FbCountOrder.states.states.Init.nextStates.Submitted.buttonLabel=Submit
i18n_entity.FbCountOrder.states.states.Submitted.label=Submitted
i18n_entity.FbCountOrder.states.states.Submitted.nextStates.Done.buttonLabel=Adjust count difference
i18n_entity.FbCountTask.fields.bin.label=Source bin
i18n_entity.FbCountTask.fields.btLines.label=Order line
i18n_entity.FbCountTask.fields.btOrderState.inlineOptionBill.items.Init.label=To be counted
i18n_entity.FbCountTask.fields.btOrderState.inlineOptionBill.items.Submitted.label=Counted
i18n_entity.FbCountTask.fields.btOrderState.label=Status
i18n_entity.FbCountTask.fields.btOrderStateReason.label=Status description
i18n_entity.FbCountTask.fields.container.label=Count container
i18n_entity.FbCountTask.fields.containerInOrderId.label=Container inbound order No.
i18n_entity.FbCountTask.fields.containerOutOrderId.label=Container outbound order No.
i18n_entity.FbCountTask.fields.countOrderId.label=Associated count order
i18n_entity.FbCountTask.fields.createdBy.label=Created by
i18n_entity.FbCountTask.fields.createdOn.label=Created time
i18n_entity.FbCountTask.fields.id.label=ID
i18n_entity.FbCountTask.fields.modifiedBy.label=Last modified by
i18n_entity.FbCountTask.fields.modifiedOn.label=Last modified time
i18n_entity.FbCountTask.fields.remark.label=Remark
i18n_entity.FbCountTask.fields.submittedPostProcessed.label=Completed
i18n_entity.FbCountTask.fields.version.label=Modified version
i18n_entity.FbCountTask.group=Warehouse
i18n_entity.FbCountTask.label=Count task
i18n_entity.FbCountTask.listCard.bin.prefix=Bin
i18n_entity.FbCountTask.listCard.container.prefix=Container
i18n_entity.FbCountTask.listStats.items[0].label=To be counted
i18n_entity.FbCountTask.states.states.Init.label=To be counted
i18n_entity.FbCountTask.states.states.Init.nextStates.Submitted.buttonLabel=Submit
i18n_entity.FbCountTask.states.states.Submitted.label=Counted
i18n_entity.FbCountTaskLine.fields.actualQty.label=Actual qty
i18n_entity.FbCountTaskLine.fields.amount.label=Amount
i18n_entity.FbCountTaskLine.fields.bin.label=Bin
i18n_entity.FbCountTaskLine.fields.btLineNo.label=Line No.
i18n_entity.FbCountTaskLine.fields.btMaterial.label=Material
i18n_entity.FbCountTaskLine.fields.btMaterialCategory.label=Material type ID
i18n_entity.FbCountTaskLine.fields.btMaterialCategoryName.label=Material type name
i18n_entity.FbCountTaskLine.fields.btMaterialId.label=Material ID
i18n_entity.FbCountTaskLine.fields.btMaterialImage.label=Material image
i18n_entity.FbCountTaskLine.fields.btMaterialModel.label=Material model
i18n_entity.FbCountTaskLine.fields.btMaterialName.label=Material name
i18n_entity.FbCountTaskLine.fields.btMaterialSpec.label=Material specs.
i18n_entity.FbCountTaskLine.fields.btParentId.label=Associated task
i18n_entity.FbCountTaskLine.fields.createdBy.label=Created by
i18n_entity.FbCountTaskLine.fields.createdOn.label=Created time
i18n_entity.FbCountTaskLine.fields.district.label=District
i18n_entity.FbCountTaskLine.fields.expDate.label=Valid period
i18n_entity.FbCountTaskLine.fields.id.label=ID
i18n_entity.FbCountTaskLine.fields.leafContainer.label=Container
i18n_entity.FbCountTaskLine.fields.lotNo.label=Lot No.
i18n_entity.FbCountTaskLine.fields.mfgDate.label=Production date
i18n_entity.FbCountTaskLine.fields.modifiedBy.label=Last modified by
i18n_entity.FbCountTaskLine.fields.modifiedOn.label=Last modified time
i18n_entity.FbCountTaskLine.fields.owner.label=Owner
i18n_entity.FbCountTaskLine.fields.price.label=Unit price
i18n_entity.FbCountTaskLine.fields.qty.label=Qty
i18n_entity.FbCountTaskLine.fields.qualityLevel.label=Quality grade
i18n_entity.FbCountTaskLine.fields.sourceInvLayout.label=Reference inventory details
i18n_entity.FbCountTaskLine.fields.subContainerId.label=Cell ID
i18n_entity.FbCountTaskLine.fields.topContainer.label=Outermost container
i18n_entity.FbCountTaskLine.fields.unitLabel.label=Unit name
i18n_entity.FbCountTaskLine.fields.vendor.label=Suppliers
i18n_entity.FbCountTaskLine.fields.version.label=Modified version
i18n_entity.FbCountTaskLine.fields.warehouse.label=Warehouse
i18n_entity.FbCountTaskLine.group=Warehouse
i18n_entity.FbCountTaskLine.label=Count task line
i18n_entity.FbCountTaskLine.listCard.actualQty.prefix=Actual qty
i18n_entity.FbCountTaskLine.listCard.btMaterialId.prefix=Material ID
i18n_entity.FbCountTaskLine.listCard.lotNo.prefix=Lot No.
i18n_entity.FbCountTaskLine.listCard.qty.prefix=Inventory qty
i18n_entity.FbCountTaskStatLine.fields.bin.label=Count bin
i18n_entity.FbCountTaskStatLine.fields.btLineNo.label=Line No.
i18n_entity.FbCountTaskStatLine.fields.btParentId.label=Owned order
i18n_entity.FbCountTaskStatLine.fields.container.label=Count container
i18n_entity.FbCountTaskStatLine.fields.createdBy.label=Created by
i18n_entity.FbCountTaskStatLine.fields.createdOn.label=Created time
i18n_entity.FbCountTaskStatLine.fields.id.label=ID
i18n_entity.FbCountTaskStatLine.fields.materialsNum.label=Material qty
i18n_entity.FbCountTaskStatLine.fields.modifiedBy.label=Last modified by
i18n_entity.FbCountTaskStatLine.fields.modifiedOn.label=Last modified time
i18n_entity.FbCountTaskStatLine.fields.qty.label=Total qty
i18n_entity.FbCountTaskStatLine.fields.taskId.label=Count task
i18n_entity.FbCountTaskStatLine.fields.version.label=Modified version
i18n_entity.FbCountTaskStatLine.group=Warehouse
i18n_entity.FbCountTaskStatLine.label=Count task line
i18n_entity.FbCountTaskStatLine.listCard.materialsNum.prefix=Material qty
i18n_entity.FbCountTaskStatLine.listCard.qty.prefix=Total qty
i18n_entity.FbCustomer.fields.address.label=Address
i18n_entity.FbCustomer.fields.btDisabled.label=Disable
i18n_entity.FbCustomer.fields.contact.label=Contact person
i18n_entity.FbCustomer.fields.createdBy.label=Created by
i18n_entity.FbCustomer.fields.createdOn.label=Created time
i18n_entity.FbCustomer.fields.id.label=Customer ID
i18n_entity.FbCustomer.fields.modifiedBy.label=Last modified by
i18n_entity.FbCustomer.fields.modifiedOn.label=Last modified time
i18n_entity.FbCustomer.fields.name.label=Customer name
i18n_entity.FbCustomer.fields.phone.label=Telephone
i18n_entity.FbCustomer.fields.remark.label=Remarks
i18n_entity.FbCustomer.fields.ro.label=Root tissue
i18n_entity.FbCustomer.fields.version.label=Version
i18n_entity.FbCustomer.group=MainData
i18n_entity.FbCustomer.label=Customer
i18n_entity.FbCustomer.listCard.btDisabled.formatMapping[0].replaceText=Disabled
i18n_entity.FbCustomer.listCard.contact.prefix=Contact person
i18n_entity.FbCustomer.listCard.name.prefix=Customer name
i18n_entity.FbCustomer.listCard.phone.prefix=Telephone
i18n_entity.FbDepartment.fields.btDisabled.label=Disable
i18n_entity.FbDepartment.fields.btHiLeafNode.label=Leaf node
i18n_entity.FbDepartment.fields.btHiNodeLevel.label=Layer
i18n_entity.FbDepartment.fields.btHiParentNode.label=Higher authorities
i18n_entity.FbDepartment.fields.btHiRootNode.label=High root node
i18n_entity.FbDepartment.fields.createdBy.label=Created by
i18n_entity.FbDepartment.fields.createdOn.label=Created time
i18n_entity.FbDepartment.fields.id.label=ID
i18n_entity.FbDepartment.fields.modifiedBy.label=Last modified by
i18n_entity.FbDepartment.fields.modifiedOn.label=Last modified time
i18n_entity.FbDepartment.fields.name.label=Name
i18n_entity.FbDepartment.fields.owner.label=Owner
i18n_entity.FbDepartment.fields.ro.label=Root tissue
i18n_entity.FbDepartment.fields.version.label=Version
i18n_entity.FbDepartment.group=User
i18n_entity.FbDepartment.label=Organization
i18n_entity.FbDepartment.listCard.owner.prefix=Owner
i18n_entity.FbDevTask.fields.comments.label=Comment
i18n_entity.FbDevTask.fields.createdBy.label=Created by
i18n_entity.FbDevTask.fields.createdOn.label=Created time
i18n_entity.FbDevTask.fields.description.label=Description
i18n_entity.FbDevTask.fields.devVersion.label=Version
i18n_entity.FbDevTask.fields.files.label=Annex document
i18n_entity.FbDevTask.fields.id.label=ID
i18n_entity.FbDevTask.fields.images.label=Attached image
i18n_entity.FbDevTask.fields.kind.inlineOptionBill.items.Bug.label=Defect
i18n_entity.FbDevTask.fields.kind.inlineOptionBill.items.Feature.label=Function
i18n_entity.FbDevTask.fields.kind.inlineOptionBill.items.Improvement.label=Optimize
i18n_entity.FbDevTask.fields.kind.inlineOptionBill.items.Test.label=Test
i18n_entity.FbDevTask.fields.kind.label=Type
i18n_entity.FbDevTask.fields.modifiedBy.label=Last modified by
i18n_entity.FbDevTask.fields.modifiedOn.label=Last modified time
i18n_entity.FbDevTask.fields.priority.inlineOptionBill.items.High.label=High
i18n_entity.FbDevTask.fields.priority.inlineOptionBill.items.Low.label=Low
i18n_entity.FbDevTask.fields.priority.inlineOptionBill.items.Normal.label=Normal
i18n_entity.FbDevTask.fields.priority.label=Priority
i18n_entity.FbDevTask.fields.processedBy.label=processor
i18n_entity.FbDevTask.fields.project.label=Project
i18n_entity.FbDevTask.fields.ro.label=Enterprise
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Closed.label=Closed
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Open.label=Create
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Processing.label=In progress
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Rejected.label=Refused
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Resolved.label=Resolved
i18n_entity.FbDevTask.fields.state.label=Status
i18n_entity.FbDevTask.fields.testImages.label=Test picture
i18n_entity.FbDevTask.fields.testResult.label=Test results
i18n_entity.FbDevTask.fields.title.label=Title
i18n_entity.FbDevTask.fields.version.label=Version
i18n_entity.FbDevTask.group=Dev
i18n_entity.FbDevTask.label=Collaborative tasks
i18n_entity.FbDevVersion.fields.createdBy.label=Created by
i18n_entity.FbDevVersion.fields.createdOn.label=Created time
i18n_entity.FbDevVersion.fields.displayOrder.label=Display order
i18n_entity.FbDevVersion.fields.done.label=Completed
i18n_entity.FbDevVersion.fields.id.label=ID
i18n_entity.FbDevVersion.fields.modifiedBy.label=Last modified by
i18n_entity.FbDevVersion.fields.modifiedOn.label=Last modified time
i18n_entity.FbDevVersion.fields.name.label=Name
i18n_entity.FbDevVersion.fields.planDoneOn.label=Planned completion time
i18n_entity.FbDevVersion.fields.ro.label=Enterprise
i18n_entity.FbDevVersion.fields.version.label=Version
i18n_entity.FbDevVersion.group=Dev
i18n_entity.FbDevVersion.label=Version
i18n_entity.FbDirectPutawayOrder.fields.bin.label=Putaway bin
i18n_entity.FbDirectPutawayOrder.fields.btInvProcessed.label=Inventory processed
i18n_entity.FbDirectPutawayOrder.fields.btLines.label=Order Line
i18n_entity.FbDirectPutawayOrder.fields.btOrderKind.inlineOptionBill.items.MfFinishedInbound.label=Finished goods inbound
i18n_entity.FbDirectPutawayOrder.fields.btOrderKind.inlineOptionBill.items.OtherInbound.label=Other inbound
i18n_entity.FbDirectPutawayOrder.fields.btOrderKind.inlineOptionBill.items.PurchaseInbound.label=Purchase inbound order
i18n_entity.FbDirectPutawayOrder.fields.btOrderKind.label=Type
i18n_entity.FbDirectPutawayOrder.fields.btOrderState.inlineOptionBill.items.Init.label=Not submitted
i18n_entity.FbDirectPutawayOrder.fields.btOrderState.inlineOptionBill.items.Submitted.label=Submitted
i18n_entity.FbDirectPutawayOrder.fields.btOrderState.label=Status
i18n_entity.FbDirectPutawayOrder.fields.btOrderStateReason.label=Status description
i18n_entity.FbDirectPutawayOrder.fields.container.label=Putaway container
i18n_entity.FbDirectPutawayOrder.fields.createdBy.label=Created by
i18n_entity.FbDirectPutawayOrder.fields.createdOn.label=Created time
i18n_entity.FbDirectPutawayOrder.fields.id.label=Order No.
i18n_entity.FbDirectPutawayOrder.fields.modifiedBy.label=Last modified by
i18n_entity.FbDirectPutawayOrder.fields.modifiedOn.label=Last modified time
i18n_entity.FbDirectPutawayOrder.fields.remark.label=Remarks
i18n_entity.FbDirectPutawayOrder.fields.ro.label=Root tissue
i18n_entity.FbDirectPutawayOrder.fields.version.label=Version
i18n_entity.FbDirectPutawayOrder.group=Warehouse
i18n_entity.FbDirectPutawayOrder.label=Manual putaway
i18n_entity.FbDirectPutawayOrder.listCard.bin.prefix=Putaway bin
i18n_entity.FbDirectPutawayOrder.listCard.container.prefix=Putaway container
i18n_entity.FbDirectPutawayOrder.states.states.Init.label=Not submitted
i18n_entity.FbDirectPutawayOrder.states.states.Init.nextStates.Submitted.buttonLabel=Submit
i18n_entity.FbDirectPutawayOrder.states.states.Submitted.label=Submitted
i18n_entity.FbDirectPutawayOrderLine.fields.btLineNo.label=Line number
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterial.label=Material
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialCategory.label=Material type ID
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialCategoryName.label=Material type name
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialId.label=Material ID
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialImage.label=Material image
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialModel.label=Material model
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialName.label=Material name
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialSpec.label=Material specs.
i18n_entity.FbDirectPutawayOrderLine.fields.btParentId.label=Associated putaway order
i18n_entity.FbDirectPutawayOrderLine.fields.createdBy.label=Created by
i18n_entity.FbDirectPutawayOrderLine.fields.createdOn.label=Created time
i18n_entity.FbDirectPutawayOrderLine.fields.id.label=ID
i18n_entity.FbDirectPutawayOrderLine.fields.lotNo.label=Lot No.
i18n_entity.FbDirectPutawayOrderLine.fields.modifiedBy.label=Last modified by
i18n_entity.FbDirectPutawayOrderLine.fields.modifiedOn.label=Last modified time
i18n_entity.FbDirectPutawayOrderLine.fields.price.label=Unit price
i18n_entity.FbDirectPutawayOrderLine.fields.qty.label=Putaway qty
i18n_entity.FbDirectPutawayOrderLine.fields.qualityLevel.label=Quality grade
i18n_entity.FbDirectPutawayOrderLine.fields.ro.label=Root tissue
i18n_entity.FbDirectPutawayOrderLine.fields.subContainerId.label=Cell ID
i18n_entity.FbDirectPutawayOrderLine.fields.unitLabel.label=Unit name
i18n_entity.FbDirectPutawayOrderLine.fields.version.label=Version
i18n_entity.FbDirectPutawayOrderLine.group=Warehouse
i18n_entity.FbDirectPutawayOrderLine.label=Manual putaway order line
i18n_entity.FbDirectPutawayOrderLine.listCard.btMaterialId.prefix=Material ID
i18n_entity.FbDirectPutawayOrderLine.listCard.qty.prefix=Qty
i18n_entity.FbDirectPutawayOrderLine.listCard.subContainerId.prefix=Cell ID
i18n_entity.FbDistrict.fields.btDisabled.label=Disable
i18n_entity.FbDistrict.fields.createdBy.label=Created by
i18n_entity.FbDistrict.fields.createdOn.label=Created time
i18n_entity.FbDistrict.fields.displayOrder.label=Display order
i18n_entity.FbDistrict.fields.id.label=District ID
i18n_entity.FbDistrict.fields.modifiedBy.label=Last modified by
i18n_entity.FbDistrict.fields.modifiedOn.label=Last modified time
i18n_entity.FbDistrict.fields.name.label=District name
i18n_entity.FbDistrict.fields.remark.label=Remarks
i18n_entity.FbDistrict.fields.ro.label=Root tissue
i18n_entity.FbDistrict.fields.structure.label=District structure
i18n_entity.FbDistrict.fields.version.label=Version
i18n_entity.FbDistrict.fields.warehouse.label=Owned warehouse
i18n_entity.FbDistrict.group=MainData
i18n_entity.FbDistrict.label=District
i18n_entity.FbGoodsOwner.fields.address.label=Address
i18n_entity.FbGoodsOwner.fields.btDisabled.label=Disable
i18n_entity.FbGoodsOwner.fields.contact.label=Contact person
i18n_entity.FbGoodsOwner.fields.createdBy.label=Created by
i18n_entity.FbGoodsOwner.fields.createdOn.label=Created time
i18n_entity.FbGoodsOwner.fields.id.label=ID
i18n_entity.FbGoodsOwner.fields.modifiedBy.label=Last modified by
i18n_entity.FbGoodsOwner.fields.modifiedOn.label=Last modified time
i18n_entity.FbGoodsOwner.fields.name.label=Name
i18n_entity.FbGoodsOwner.fields.phone.label=Telephone
i18n_entity.FbGoodsOwner.fields.remark.label=Remarks
i18n_entity.FbGoodsOwner.fields.ro.label=Root tissue
i18n_entity.FbGoodsOwner.fields.version.label=Version
i18n_entity.FbGoodsOwner.group=MainData
i18n_entity.FbGoodsOwner.label=Material owner
i18n_entity.FbGoodsOwner.listCard.contact.prefix=Contact person
i18n_entity.FbGoodsOwner.listCard.name.prefix=Name
i18n_entity.FbGoodsOwner.listCard.phone.prefix=Telephone
i18n_entity.FbInboundOrder.fields.asnId.label=Arrival notice No.
i18n_entity.FbInboundOrder.fields.btInvProcessed.label=Inventory processed
i18n_entity.FbInboundOrder.fields.btLines.label=Order line
i18n_entity.FbInboundOrder.fields.btOrderKind.inlineOptionBill.items.MfFinishedInbound.label=Finished goods inbound
i18n_entity.FbInboundOrder.fields.btOrderKind.inlineOptionBill.items.OtherInbound.label=Other inbound
i18n_entity.FbInboundOrder.fields.btOrderKind.inlineOptionBill.items.PurchaseInbound.label=Purchase inbound order
i18n_entity.FbInboundOrder.fields.btOrderKind.label=Type
i18n_entity.FbInboundOrder.fields.btOrderState.inlineOptionBill.items.Committed.label=Submitted
i18n_entity.FbInboundOrder.fields.btOrderState.inlineOptionBill.items.Init.label=Not submitted
i18n_entity.FbInboundOrder.fields.btOrderState.label=Status
i18n_entity.FbInboundOrder.fields.btOrderStateReason.label=Status description
i18n_entity.FbInboundOrder.fields.callContainerAll.label=Called empty container
i18n_entity.FbInboundOrder.fields.createdBy.label=Created by
i18n_entity.FbInboundOrder.fields.createdOn.label=Created time
i18n_entity.FbInboundOrder.fields.district.label=District
i18n_entity.FbInboundOrder.fields.id.label=Order No.
i18n_entity.FbInboundOrder.fields.mfgFQCOrderId.label=Finished product QC order
i18n_entity.FbInboundOrder.fields.mfgOrderId.label=Production work order
i18n_entity.FbInboundOrder.fields.modifiedBy.label=Last modified by
i18n_entity.FbInboundOrder.fields.modifiedOn.label=Last modified time
i18n_entity.FbInboundOrder.fields.planQty.label=Received qty
i18n_entity.FbInboundOrder.fields.purchaseOrderId.label=Purchase order
i18n_entity.FbInboundOrder.fields.qty.label=Inbound qty
i18n_entity.FbInboundOrder.fields.recevingOrderId.label=Receive order
i18n_entity.FbInboundOrder.fields.remark.label=Remarks
i18n_entity.FbInboundOrder.fields.ro.label=Root tissue
i18n_entity.FbInboundOrder.fields.vendor.label=Vendor
i18n_entity.FbInboundOrder.fields.version.label=Version
i18n_entity.FbInboundOrder.fields.warehouse.label=Inbound warehouse
i18n_entity.FbInboundOrder.group=Warehouse
i18n_entity.FbInboundOrder.label=Inbound Order
i18n_entity.FbInboundOrder.states.states.Committed.label=Submitted
i18n_entity.FbInboundOrder.states.states.Init.label=Not submitted
i18n_entity.FbInboundOrder.states.states.Init.nextStates.Committed.buttonLabel=Submit
i18n_entity.FbInboundOrderLine.fields.bin.label=Bin
i18n_entity.FbInboundOrderLine.fields.btLineNo.label=Line number
i18n_entity.FbInboundOrderLine.fields.btMaterial.label=Material
i18n_entity.FbInboundOrderLine.fields.btMaterialCategory.label=Material category ID
i18n_entity.FbInboundOrderLine.fields.btMaterialCategoryName.label=Material category name
i18n_entity.FbInboundOrderLine.fields.btMaterialId.label=Material ID
i18n_entity.FbInboundOrderLine.fields.btMaterialImage.label=Material image
i18n_entity.FbInboundOrderLine.fields.btMaterialModel.label=Material model
i18n_entity.FbInboundOrderLine.fields.btMaterialName.label=Material name
i18n_entity.FbInboundOrderLine.fields.btMaterialSpec.label=Material specs.
i18n_entity.FbInboundOrderLine.fields.btParentId.label=Associated receipt
i18n_entity.FbInboundOrderLine.fields.callContainerQty.label=Allocated container qty
i18n_entity.FbInboundOrderLine.fields.createdBy.label=Created by
i18n_entity.FbInboundOrderLine.fields.createdOn.label=Created time
i18n_entity.FbInboundOrderLine.fields.finishedQty.label=Finished qty
i18n_entity.FbInboundOrderLine.fields.id.label=ID
i18n_entity.FbInboundOrderLine.fields.lotNo.label=Lot No.
i18n_entity.FbInboundOrderLine.fields.materialName.label=Material name
i18n_entity.FbInboundOrderLine.fields.modifiedBy.label=Last modified by
i18n_entity.FbInboundOrderLine.fields.modifiedOn.label=Last modified time
i18n_entity.FbInboundOrderLine.fields.planQty.label=Received qty
i18n_entity.FbInboundOrderLine.fields.price.label=Unit price
i18n_entity.FbInboundOrderLine.fields.qty.label=Inbound qty
i18n_entity.FbInboundOrderLine.fields.qualityLevel.label=Quality grade
i18n_entity.FbInboundOrderLine.fields.ro.label=Root tissue
i18n_entity.FbInboundOrderLine.fields.storeQty.label=Putaway qty
i18n_entity.FbInboundOrderLine.fields.unitLabel.label=Unit name
i18n_entity.FbInboundOrderLine.fields.version.label=Version
i18n_entity.FbInboundOrderLine.group=Warehouse
i18n_entity.FbInboundOrderLine.label=Inbound order line
i18n_entity.FbInboundOrderLine.listCard.btMaterialId.prefix=Material ID
i18n_entity.FbInboundOrderLine.listCard.callContainerQty.prefix=Distribution container
i18n_entity.FbInboundOrderLine.listCard.qty.prefix=Inbound
i18n_entity.FbInboundOrderLine.listCard.storeQty.prefix=Putaway
i18n_entity.FbInvLayout.fields.amount.label=Amount
i18n_entity.FbInvLayout.fields.bin.label=Bin
i18n_entity.FbInvLayout.fields.btMaterial.label=Material
i18n_entity.FbInvLayout.fields.btMaterialCategory.label=Material category ID
i18n_entity.FbInvLayout.fields.btMaterialCategoryName.label=Material category name
i18n_entity.FbInvLayout.fields.btMaterialId.label=Material ID
i18n_entity.FbInvLayout.fields.btMaterialImage.label=Material image
i18n_entity.FbInvLayout.fields.btMaterialModel.label=Material model
i18n_entity.FbInvLayout.fields.btMaterialName.label=Material name
i18n_entity.FbInvLayout.fields.btMaterialSpec.label=Material specs.
i18n_entity.FbInvLayout.fields.column.label=Column
i18n_entity.FbInvLayout.fields.createdBy.label=Created by
i18n_entity.FbInvLayout.fields.createdOn.label=Created time
i18n_entity.FbInvLayout.fields.depth.label=Depth
i18n_entity.FbInvLayout.fields.district.label=District
i18n_entity.FbInvLayout.fields.expDate.label=Valid period
i18n_entity.FbInvLayout.fields.id.label=ID
i18n_entity.FbInvLayout.fields.inboundOn.label=Inbound time
i18n_entity.FbInvLayout.fields.inboundOrderId.label=Inbound order
i18n_entity.FbInvLayout.fields.inboundOrderLineNo.label=Inbound order line No.
i18n_entity.FbInvLayout.fields.layer.label=Layer
i18n_entity.FbInvLayout.fields.leafContainer.label=Innermost container
i18n_entity.FbInvLayout.fields.locked.label=Lock
i18n_entity.FbInvLayout.fields.locked.listBatchButtons.buttons[0].label=Unlock
i18n_entity.FbInvLayout.fields.lotNo.label=Lot No.
i18n_entity.FbInvLayout.fields.matLotNo.label=Lot No.
i18n_entity.FbInvLayout.fields.matSerialNo.label=Serial No.
i18n_entity.FbInvLayout.fields.mfgDate.label=Production date
i18n_entity.FbInvLayout.fields.modifiedBy.label=Last modified by
i18n_entity.FbInvLayout.fields.modifiedOn.label=Last modified time
i18n_entity.FbInvLayout.fields.onRobot.label=Robot
i18n_entity.FbInvLayout.fields.outboundOrderId.label=Outbound order
i18n_entity.FbInvLayout.fields.outboundOrderLineNo.label=Outbound order line No.
i18n_entity.FbInvLayout.fields.owner.label=Owner
i18n_entity.FbInvLayout.fields.price.label=Unit price
i18n_entity.FbInvLayout.fields.qty.label=Qty
i18n_entity.FbInvLayout.fields.refInv.label=Reference inventory details
i18n_entity.FbInvLayout.fields.row.label=Row
i18n_entity.FbInvLayout.fields.state.inlineOptionBill.items.Assigned.label=Allocated
i18n_entity.FbInvLayout.fields.state.inlineOptionBill.items.Received.label=Received
i18n_entity.FbInvLayout.fields.state.inlineOptionBill.items.Storing.label=In storage
i18n_entity.FbInvLayout.fields.state.label=Inventory status
i18n_entity.FbInvLayout.fields.subContainerId.label=Cell
i18n_entity.FbInvLayout.fields.topContainer.label=Outermost container
i18n_entity.FbInvLayout.fields.usedQty.label=Allocated qty
i18n_entity.FbInvLayout.fields.vendor.label=Vendor
i18n_entity.FbInvLayout.fields.version.label=Revised version
i18n_entity.FbInvLayout.fields.warehouse.label=Warehouse
i18n_entity.FbInvLayout.group=Warehouse
i18n_entity.FbInvLayout.label=Inventory details
i18n_entity.FbInvLayout.listCard.bin.prefix=Bin
i18n_entity.FbInvLayout.listCard.inboundOn.prefix=Inbound time
i18n_entity.FbInvLayout.listCard.lotNo.prefix=Lot No.
i18n_entity.FbInvLayout.listCard.qty.prefix=Qty
i18n_entity.FbInvLayout.listCard.topContainer.prefix=Container
i18n_entity.FbMaterial.fields.abc.label=ABC category
i18n_entity.FbMaterial.fields.btDisabled.label=Disable
i18n_entity.FbMaterial.fields.categoriesDesc.label=Material category description
i18n_entity.FbMaterial.fields.category1.label=Primary category
i18n_entity.FbMaterial.fields.category2.label=Sub-category
i18n_entity.FbMaterial.fields.category3.label=Tertiary category
i18n_entity.FbMaterial.fields.createdBy.label=Created by
i18n_entity.FbMaterial.fields.createdOn.label=Created time
i18n_entity.FbMaterial.fields.displayDecimals.label=Qty of decimal places
i18n_entity.FbMaterial.fields.endOn.label=Disable date
i18n_entity.FbMaterial.fields.height.label=Height
i18n_entity.FbMaterial.fields.id.label=Material ID
i18n_entity.FbMaterial.fields.image.label=Image
i18n_entity.FbMaterial.fields.leafCategory.label=Material category
i18n_entity.FbMaterial.fields.length.label=Length
i18n_entity.FbMaterial.fields.mainUnit.label=Main unit of measurement name
i18n_entity.FbMaterial.fields.mainVendor.label=Main vendor
i18n_entity.FbMaterial.fields.mixLotNo.label=Mixed lot
i18n_entity.FbMaterial.fields.mixMaterial.label=Mixed material
i18n_entity.FbMaterial.fields.model.label=Model
i18n_entity.FbMaterial.fields.modifiedBy.label=Last modified by
i18n_entity.FbMaterial.fields.modifiedOn.label=Last modified time
i18n_entity.FbMaterial.fields.name.label=Material name
i18n_entity.FbMaterial.fields.owner.label=Owner
i18n_entity.FbMaterial.fields.price.label=Unit price
i18n_entity.FbMaterial.fields.remark.label=Remarks
i18n_entity.FbMaterial.fields.ro.label=Root tissue
i18n_entity.FbMaterial.fields.spec.label=Material specs.
i18n_entity.FbMaterial.fields.startOn.label=Enable date
i18n_entity.FbMaterial.fields.syncOut.label=External import
i18n_entity.FbMaterial.fields.topCat.label=Material category name
i18n_entity.FbMaterial.fields.unit.label=Material unit
i18n_entity.FbMaterial.fields.unitLabel.label=Unit name
i18n_entity.FbMaterial.fields.version.label=Version
i18n_entity.FbMaterial.fields.volume.label=Volume
i18n_entity.FbMaterial.fields.weight.label=Weight
i18n_entity.FbMaterial.fields.width.label=Width
i18n_entity.FbMaterial.group=MainData
i18n_entity.FbMaterial.label=Material
i18n_entity.FbMaterial.listCard.leafCategory.prefix=Material category
i18n_entity.FbMaterialCategory.fields.btDisabled.label=Disable
i18n_entity.FbMaterialCategory.fields.createdBy.label=Created by
i18n_entity.FbMaterialCategory.fields.createdOn.label=Created time
i18n_entity.FbMaterialCategory.fields.id.label=Category ID
i18n_entity.FbMaterialCategory.fields.modifiedBy.label=Last modified by
i18n_entity.FbMaterialCategory.fields.modifiedOn.label=Last modified time
i18n_entity.FbMaterialCategory.fields.name.label=Name
i18n_entity.FbMaterialCategory.fields.parent.label=Superior category
i18n_entity.FbMaterialCategory.fields.remark.label=Remarks
i18n_entity.FbMaterialCategory.fields.storeDistricts.label=District
i18n_entity.FbMaterialCategory.fields.version.label=Revised version
i18n_entity.FbMaterialCategory.group=MainData
i18n_entity.FbMaterialCategory.label=Material category
i18n_entity.FbMaterialCategory.listCard.storeDistricts.prefix=District
i18n_entity.FbMaterialContainerMaxQty.fields.btDisabled.label=Disable
i18n_entity.FbMaterialContainerMaxQty.fields.btMaterial.label=Material
i18n_entity.FbMaterialContainerMaxQty.fields.containerType.label=Container type
i18n_entity.FbMaterialContainerMaxQty.fields.createdBy.label=Created by
i18n_entity.FbMaterialContainerMaxQty.fields.createdOn.label=Created time
i18n_entity.FbMaterialContainerMaxQty.fields.id.label=ID
i18n_entity.FbMaterialContainerMaxQty.fields.maxQty.label=Max qty
i18n_entity.FbMaterialContainerMaxQty.fields.modifiedBy.label=Last modified by
i18n_entity.FbMaterialContainerMaxQty.fields.modifiedOn.label=Last modified time
i18n_entity.FbMaterialContainerMaxQty.fields.version.label=Revised version
i18n_entity.FbMaterialContainerMaxQty.group=MainData
i18n_entity.FbMaterialContainerMaxQty.label=Max capacity
i18n_entity.FbMaterialContainerMaxQty.listCard.btMaterial.prefix=Material
i18n_entity.FbMaterialContainerMaxQty.listCard.maxQty.prefix=Max qty
i18n_entity.FbMaterialContainerMaxQty.listCard.maxQty.suffix=Per
i18n_entity.FbMaterialLot.fields.createdBy.label=Created by
i18n_entity.FbMaterialLot.fields.createdOn.label=Created time
i18n_entity.FbMaterialLot.fields.disabled.label=Disable
i18n_entity.FbMaterialLot.fields.id.label=Production lot number
i18n_entity.FbMaterialLot.fields.material.label=Associated material
i18n_entity.FbMaterialLot.fields.modifiedBy.label=Last modified by
i18n_entity.FbMaterialLot.fields.modifiedOn.label=Last modified time
i18n_entity.FbMaterialLot.fields.name.label=Production lot name
i18n_entity.FbMaterialLot.fields.remark.label=Remarks
i18n_entity.FbMaterialLot.fields.ro.label=Root tissue
i18n_entity.FbMaterialLot.fields.version.label=Version
i18n_entity.FbMaterialLot.group=MainData
i18n_entity.FbMaterialLot.label=Material lot
i18n_entity.FbMaterialUnit.fields.basic.label=Basic unit
i18n_entity.FbMaterialUnit.fields.createdBy.label=Created by
i18n_entity.FbMaterialUnit.fields.createdOn.label=Created time
i18n_entity.FbMaterialUnit.fields.disabled.label=Disable
i18n_entity.FbMaterialUnit.fields.id.label=Unit ID
i18n_entity.FbMaterialUnit.fields.modifiedBy.label=Last modified by
i18n_entity.FbMaterialUnit.fields.modifiedOn.label=Last modified time
i18n_entity.FbMaterialUnit.fields.name.label=Unit name
i18n_entity.FbMaterialUnit.fields.parent.label=Parent unit
i18n_entity.FbMaterialUnit.fields.ratio.label=Conversion relation
i18n_entity.FbMaterialUnit.fields.remark.label=Remarks
i18n_entity.FbMaterialUnit.fields.ro.label=Root tissue
i18n_entity.FbMaterialUnit.fields.version.label=Version
i18n_entity.FbMaterialUnit.group=MainData
i18n_entity.FbMaterialUnit.label=Material unit
i18n_entity.FbOutboundOrder.fields.btInvProcessed.label=Inventory processed
i18n_entity.FbOutboundOrder.fields.btLines.label=Order line
i18n_entity.FbOutboundOrder.fields.btOrderKind.inlineOptionBill.items.Product.label=Outbound
i18n_entity.FbOutboundOrder.fields.btOrderKind.label=Type
i18n_entity.FbOutboundOrder.fields.btOrderState.inlineOptionBill.items.Committed.label=Submitted
i18n_entity.FbOutboundOrder.fields.btOrderState.inlineOptionBill.items.Init.label=Not submitted
i18n_entity.FbOutboundOrder.fields.btOrderState.label=Status
i18n_entity.FbOutboundOrder.fields.btOrderStateReason.label=Status description
i18n_entity.FbOutboundOrder.fields.createdBy.label=Created by
i18n_entity.FbOutboundOrder.fields.createdOn.label=Created time
i18n_entity.FbOutboundOrder.fields.customer.label=Customer
i18n_entity.FbOutboundOrder.fields.district.label=Outbound district
i18n_entity.FbOutboundOrder.fields.id.label=Order No.
i18n_entity.FbOutboundOrder.fields.invAssignedAll.label=Inventory distribution completed
i18n_entity.FbOutboundOrder.fields.modifiedBy.label=Last modified by
i18n_entity.FbOutboundOrder.fields.modifiedOn.label=Last modified time
i18n_entity.FbOutboundOrder.fields.planQty.label=Planned outbound qty
i18n_entity.FbOutboundOrder.fields.priority.label=Priority
i18n_entity.FbOutboundOrder.fields.qty.label=Qty of Outbound
i18n_entity.FbOutboundOrder.fields.receiver.label=Receiving unit
i18n_entity.FbOutboundOrder.fields.remark.label=Remarks
i18n_entity.FbOutboundOrder.fields.ro.label=Root tissue
i18n_entity.FbOutboundOrder.fields.saleOrderId.label=Sales order No.
i18n_entity.FbOutboundOrder.fields.saleShipOrderId.label=Sales Order
i18n_entity.FbOutboundOrder.fields.version.label=Version
i18n_entity.FbOutboundOrder.fields.warehouse.label=Outbound warehouse
i18n_entity.FbOutboundOrder.group=Warehouse
i18n_entity.FbOutboundOrder.label=Outbound order
i18n_entity.FbOutboundOrder.listCard.priority.prefix=Priority
i18n_entity.FbOutboundOrder.states.states.Committed.label=Submitted
i18n_entity.FbOutboundOrder.states.states.Init.label=Not submitted
i18n_entity.FbOutboundOrder.states.states.Init.nextStates.Committed.buttonLabel=Submit
i18n_entity.FbOutboundOrderLine.fields.btInvRefAll.label=Total inventory
i18n_entity.FbOutboundOrderLine.fields.btInvRefMatch.label=Available stock
i18n_entity.FbOutboundOrderLine.fields.btLineNo.label=Line number
i18n_entity.FbOutboundOrderLine.fields.btMaterial.label=Material
i18n_entity.FbOutboundOrderLine.fields.btMaterialCategory.label=Material category ID
i18n_entity.FbOutboundOrderLine.fields.btMaterialCategoryName.label=Material category name
i18n_entity.FbOutboundOrderLine.fields.btMaterialId.label=Material ID
i18n_entity.FbOutboundOrderLine.fields.btMaterialImage.label=Material image
i18n_entity.FbOutboundOrderLine.fields.btMaterialModel.label=Material model
i18n_entity.FbOutboundOrderLine.fields.btMaterialName.label=Material name
i18n_entity.FbOutboundOrderLine.fields.btMaterialSpec.label=Material specs.
i18n_entity.FbOutboundOrderLine.fields.btParentId.label=Associated outbound order
i18n_entity.FbOutboundOrderLine.fields.createdBy.label=Created by
i18n_entity.FbOutboundOrderLine.fields.createdOn.label=Created time
i18n_entity.FbOutboundOrderLine.fields.finishedQty.label=Previous outbound qty
i18n_entity.FbOutboundOrderLine.fields.id.label=ID
i18n_entity.FbOutboundOrderLine.fields.invAssignedQty.label=Allocated inventory qty
i18n_entity.FbOutboundOrderLine.fields.lotNo.label=Lot No.
i18n_entity.FbOutboundOrderLine.fields.materialName.label=Material name
i18n_entity.FbOutboundOrderLine.fields.modifiedBy.label=Last modified by
i18n_entity.FbOutboundOrderLine.fields.modifiedOn.label=Last modified time
i18n_entity.FbOutboundOrderLine.fields.planQty.label=Total planned outbound
i18n_entity.FbOutboundOrderLine.fields.qty.label=Outbound qty
i18n_entity.FbOutboundOrderLine.fields.qualityLevel.label=Quality grade
i18n_entity.FbOutboundOrderLine.fields.ro.label=Root tissue
i18n_entity.FbOutboundOrderLine.fields.version.label=Version
i18n_entity.FbOutboundOrderLine.group=Warehouse
i18n_entity.FbOutboundOrderLine.label=Outbound order line
i18n_entity.FbOutboundOrderLine.listCard.btMaterialId.prefix=Material ID
i18n_entity.FbOutboundOrderLine.listCard.invAssignedQty.prefix=Allocated inventory
i18n_entity.FbOutboundOrderLine.listCard.lotNo.prefix=Lot No.
i18n_entity.FbOutboundOrderLine.listCard.qty.prefix=Number
i18n_entity.FbPkg.fields.createdBy.label=Created by
i18n_entity.FbPkg.fields.createdOn.label=Created time
i18n_entity.FbPkg.fields.disabled.label=Disable
i18n_entity.FbPkg.fields.id.label=Packaging ID
i18n_entity.FbPkg.fields.material.label=Associated material
i18n_entity.FbPkg.fields.modifiedBy.label=Last modified by
i18n_entity.FbPkg.fields.modifiedOn.label=Last modified time
i18n_entity.FbPkg.fields.name.label=Packaging name
i18n_entity.FbPkg.fields.purpose.label=Packaging purposes
i18n_entity.FbPkg.fields.qty.label=Qty of packages
i18n_entity.FbPkg.fields.remark.label=Remarks
i18n_entity.FbPkg.fields.ro.label=Root tissue
i18n_entity.FbPkg.fields.version.label=Version
i18n_entity.FbPkg.group=MainData
i18n_entity.FbPkg.label=Packaging specs.
i18n_entity.FbPkg.listCard.disabled.formatMapping[0].replaceText=Disabled
i18n_entity.FbPkg.listCard.material.prefix=Associated material
i18n_entity.FbPkg.listCard.name.prefix=Packaging name
i18n_entity.FbPkg.listCard.qty.prefix=Max qty
i18n_entity.FbPkg.listCard.qty.suffix=Per
i18n_entity.FbVendor.fields.address.label=Address
i18n_entity.FbVendor.fields.btDisabled.label=Disable
i18n_entity.FbVendor.fields.contact.label=Contact person
i18n_entity.FbVendor.fields.createdBy.label=Created by
i18n_entity.FbVendor.fields.createdOn.label=Created time
i18n_entity.FbVendor.fields.email.label=Email address
i18n_entity.FbVendor.fields.id.label=Vendor ID
i18n_entity.FbVendor.fields.level.label=Level
i18n_entity.FbVendor.fields.modifiedBy.label=Last modified by
i18n_entity.FbVendor.fields.modifiedOn.label=Last modified time
i18n_entity.FbVendor.fields.name.label=Vendor name
i18n_entity.FbVendor.fields.phone.label=Telephone
i18n_entity.FbVendor.fields.remark.label=Remarks
i18n_entity.FbVendor.fields.ro.label=Root tissue
i18n_entity.FbVendor.fields.version.label=Version
i18n_entity.FbVendor.group=MainData
i18n_entity.FbVendor.label=Vendor
i18n_entity.FbVendor.listCard.contact.prefix=Contact person
i18n_entity.FbVendor.listCard.name.prefix=Name
i18n_entity.FbVendor.listCard.phone.prefix=Telephone
i18n_entity.FbWarehouse.fields.address.label=Address
i18n_entity.FbWarehouse.fields.btDisabled.label=Disable
i18n_entity.FbWarehouse.fields.contact.label=Contact person
i18n_entity.FbWarehouse.fields.createdBy.label=Created by
i18n_entity.FbWarehouse.fields.createdOn.label=Created time
i18n_entity.FbWarehouse.fields.defaultBin.label=Default bin
i18n_entity.FbWarehouse.fields.displayOrder.label=Display order
i18n_entity.FbWarehouse.fields.id.label=Warehouse ID
i18n_entity.FbWarehouse.fields.latitude.label=Location latitude
i18n_entity.FbWarehouse.fields.longitude.label=Longitude of position
i18n_entity.FbWarehouse.fields.modifiedBy.label=Last modified by
i18n_entity.FbWarehouse.fields.modifiedOn.label=Last modified time
i18n_entity.FbWarehouse.fields.name.label=Warehouse name
i18n_entity.FbWarehouse.fields.phone.label=Telephone
i18n_entity.FbWarehouse.fields.remark.label=Remarks
i18n_entity.FbWarehouse.fields.ro.label=Root tissue
i18n_entity.FbWarehouse.fields.version.label=Version
i18n_entity.FbWarehouse.fields.volume.label=Volume
i18n_entity.FbWarehouse.group=MainData
i18n_entity.FbWarehouse.label=Warehouse
i18n_entity.FbWarehouse.listCard.btDisabled.formatMapping[0].replaceText=Disabled
i18n_entity.FbWorkPosition.fields.createdBy.label=Created by
i18n_entity.FbWorkPosition.fields.createdOn.label=Created time
i18n_entity.FbWorkPosition.fields.disabled.label=Disable
i18n_entity.FbWorkPosition.fields.id.label=Order No.
i18n_entity.FbWorkPosition.fields.modifiedBy.label=Last modified by
i18n_entity.FbWorkPosition.fields.modifiedOn.label=Last modified time
i18n_entity.FbWorkPosition.fields.name.label=Name
i18n_entity.FbWorkPosition.fields.remark.label=Remarks
i18n_entity.FbWorkPosition.fields.ro.label=Root tissue
i18n_entity.FbWorkPosition.fields.version.label=Version
i18n_entity.FbWorkPosition.group=MainData
i18n_entity.FbWorkPosition.label=Position
i18n_entity.FbWorkPosition.listCard.disabled.formatMapping[0].replaceText=Disabled
i18n_entity.FbWorkSite.fields.bin.label=Bin
i18n_entity.FbWorkSite.fields.createdBy.label=Created by
i18n_entity.FbWorkSite.fields.createdOn.label=Created time
i18n_entity.FbWorkSite.fields.disabled.label=Disable
i18n_entity.FbWorkSite.fields.id.label=ID
i18n_entity.FbWorkSite.fields.kind.label=Kind
i18n_entity.FbWorkSite.fields.line.label=Assembly line
i18n_entity.FbWorkSite.fields.modifiedBy.label=Last modified by
i18n_entity.FbWorkSite.fields.modifiedOn.label=Last modified time
i18n_entity.FbWorkSite.fields.name.label=Name
i18n_entity.FbWorkSite.fields.position.label=Position
i18n_entity.FbWorkSite.fields.remark.label=Remarks
i18n_entity.FbWorkSite.fields.ro.label=Root tissue
i18n_entity.FbWorkSite.fields.version.label=Version
i18n_entity.FbWorkSite.group=MainData
i18n_entity.FbWorkSite.label=Station
i18n_entity.FbWorkSite.listCard.bin.prefix=Bin
i18n_entity.FbWorkSite.listCard.disabled.formatMapping[0].replaceText=Disabled
i18n_entity.FbWorkSite.listCard.kind.prefix=Type
i18n_entity.FbWorkSite.listCard.name.prefix=Name
i18n_entity.FbWorkSite.listCard.position.prefix=Position
i18n_entity.HaiMockRobot.fields.battery.label=Battery level
i18n_entity.HaiMockRobot.fields.createdBy.label=Created by
i18n_entity.HaiMockRobot.fields.createdOn.label=Created time
i18n_entity.HaiMockRobot.fields.id.label=ID
i18n_entity.HaiMockRobot.fields.modifiedBy.label=Last modified by
i18n_entity.HaiMockRobot.fields.modifiedOn.label=Last modified time
i18n_entity.HaiMockRobot.fields.posX.label=Location X
i18n_entity.HaiMockRobot.fields.posY.label=Location Y
i18n_entity.HaiMockRobot.fields.version.label=Revised version
i18n_entity.HaiMockRobot.group=WCS
i18n_entity.HaiMockRobot.label=Hai simulation robot
i18n_entity.HaiMockRobot.listCard.battery.prefix=Battery level
i18n_entity.HaiMockRobot.listCard.posX.prefix=Location X
i18n_entity.HaiMockRobot.listCard.posY.prefix=Location Y
i18n_entity.HikResourcePack.fields.active.label=Main configuration
i18n_entity.HikResourcePack.fields.createdBy.label=Created by
i18n_entity.HikResourcePack.fields.createdOn.label=Created time
i18n_entity.HikResourcePack.fields.id.label=ID
i18n_entity.HikResourcePack.fields.lmap.label=Laser point cloud file "lmap"
i18n_entity.HikResourcePack.fields.modifiedBy.label=Last modified by
i18n_entity.HikResourcePack.fields.modifiedOn.label=Last modified time
i18n_entity.HikResourcePack.fields.podConfig.label=Rack configuration XML
i18n_entity.HikResourcePack.fields.remark.label=Remarks
i18n_entity.HikResourcePack.fields.version.label=Revised version
i18n_entity.HikResourcePack.group=WCS
i18n_entity.HikResourcePack.label=Hik effect package
i18n_entity.HikResourcePack.listCard.version.prefix=Version
i18n_entity.HumanUser.fields.btDisabled.label=Deactivated
i18n_entity.HumanUser.fields.company.label=Company
i18n_entity.HumanUser.fields.createdBy.label=Created by
i18n_entity.HumanUser.fields.createdOn.label=Created time
i18n_entity.HumanUser.fields.directSignInDisabled.label=Prohibit direct login
i18n_entity.HumanUser.fields.disabled.label=Disable
i18n_entity.HumanUser.fields.email.label=Mail
i18n_entity.HumanUser.fields.externalAdded.label=Externally added
i18n_entity.HumanUser.fields.externalSource.label=External sources
i18n_entity.HumanUser.fields.externalUserId.label=External user ID
i18n_entity.HumanUser.fields.id.label=ID
i18n_entity.HumanUser.fields.modifiedBy.label=Last modified by
i18n_entity.HumanUser.fields.modifiedOn.label=Last modified time
i18n_entity.HumanUser.fields.password.label=Password
i18n_entity.HumanUser.fields.phone.label=Mobile phone
i18n_entity.HumanUser.fields.ro.label=Root tissue
i18n_entity.HumanUser.fields.roAdmin.label=Enterprise administrators
i18n_entity.HumanUser.fields.roleIds.label=Role ID
i18n_entity.HumanUser.fields.truename.label=Real name
i18n_entity.HumanUser.fields.username.label=Username
i18n_entity.HumanUser.fields.version.label=Version
i18n_entity.HumanUser.group=User
i18n_entity.HumanUser.label=User
i18n_entity.HumanUser.listCard.phone.prefix=Mobile phone
i18n_entity.HumanUser.listCard.truename.prefix=Real name
i18n_entity.HumanUserSession.fields.createdBy.label=Created by
i18n_entity.HumanUserSession.fields.createdOn.label=Created time
i18n_entity.HumanUserSession.fields.expiredAt.label=Time of expiration
i18n_entity.HumanUserSession.fields.id.label=ID
i18n_entity.HumanUserSession.fields.modifiedBy.label=Last modified by
i18n_entity.HumanUserSession.fields.modifiedOn.label=Last modified time
i18n_entity.HumanUserSession.fields.ro.label=RO
i18n_entity.HumanUserSession.fields.userId.label=User ID
i18n_entity.HumanUserSession.fields.userToken.label=Token
i18n_entity.HumanUserSession.fields.version.label=Revised version
i18n_entity.HumanUserSession.group=Core
i18n_entity.HumanUserSession.label=User session
i18n_entity.IdGen.fields.createdBy.label=Created by
i18n_entity.IdGen.fields.createdOn.label=Created time
i18n_entity.IdGen.fields.flowNo.label=flow No.
i18n_entity.IdGen.fields.id.label=ID
i18n_entity.IdGen.fields.key.label=Group
i18n_entity.IdGen.fields.modifiedBy.label=Last modified by
i18n_entity.IdGen.fields.modifiedOn.label=Last modified time
i18n_entity.IdGen.fields.ro.label=Root tissue
i18n_entity.IdGen.fields.timestamp.label=Date
i18n_entity.IdGen.fields.version.label=Version
i18n_entity.IdGen.group=Core
i18n_entity.IdGen.label=Creating ID rules
i18n_entity.LePage.fields.content.label=Content
i18n_entity.LePage.fields.createdBy.label=Created by
i18n_entity.LePage.fields.createdOn.label=Created time
i18n_entity.LePage.fields.id.label=ID
i18n_entity.LePage.fields.label.label=Display name
i18n_entity.LePage.fields.modifiedBy.label=Last modified by
i18n_entity.LePage.fields.modifiedOn.label=Last modified time
i18n_entity.LePage.fields.name.label=Page name
i18n_entity.LePage.fields.version.label=Revised version
i18n_entity.LePage.group=Core
i18n_entity.LePage.label=Customized interface
i18n_entity.ListFilterCase.fields.content.label=Content
i18n_entity.ListFilterCase.fields.createdBy.label=Created by
i18n_entity.ListFilterCase.fields.createdOn.label=Created time
i18n_entity.ListFilterCase.fields.global.label=Overall situation
i18n_entity.ListFilterCase.fields.id.label=ID
i18n_entity.ListFilterCase.fields.modifiedBy.label=Last modified by
i18n_entity.ListFilterCase.fields.modifiedOn.label=Last modified time
i18n_entity.ListFilterCase.fields.owner.label=Owner
i18n_entity.ListFilterCase.fields.page.label=Page
i18n_entity.ListFilterCase.fields.ro.label=Root tissue
i18n_entity.ListFilterCase.fields.version.label=Version
i18n_entity.ListFilterCase.group=Core
i18n_entity.ListFilterCase.label=List query scheme
i18n_entity.MockSeerRobot.fields.createdBy.label=Created by
i18n_entity.MockSeerRobot.fields.createdOn.label=Created time
i18n_entity.MockSeerRobot.fields.currentStation.label=Current site
i18n_entity.MockSeerRobot.fields.id.label=ID
i18n_entity.MockSeerRobot.fields.modifiedBy.label=Last modified by
i18n_entity.MockSeerRobot.fields.modifiedOn.label=Last modified time
i18n_entity.MockSeerRobot.fields.staringStation.label=Birth site
i18n_entity.MockSeerRobot.fields.version.label=Revised version
i18n_entity.MockSeerRobot.group=WCS
i18n_entity.MockSeerRobot.label=Simulated SEER Robot
i18n_entity.MockSeerRobot.listCard.createdOn.prefix=Create
i18n_entity.MockSeerRobot.listCard.staringStation.prefix=Birth site
i18n_entity.MrRobotRuntimeRecord.fields.channel.label=Current aisle
i18n_entity.MrRobotRuntimeRecord.fields.createdBy.label=Created by
i18n_entity.MrRobotRuntimeRecord.fields.createdOn.label=Created time
i18n_entity.MrRobotRuntimeRecord.fields.ctOrders.label=Container transport order
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Charging.label=Charging
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Idle.label=Idle
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Inbound.label=Inbound processing
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Outbound.label=Outbound processing
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Parking.label=Parking
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Tasking.label=Execution of transport order
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.label=Extended task status
i18n_entity.MrRobotRuntimeRecord.fields.id.label=ID
i18n_entity.MrRobotRuntimeRecord.fields.modifiedBy.label=Last modified by
i18n_entity.MrRobotRuntimeRecord.fields.modifiedOn.label=Last modified time
i18n_entity.MrRobotRuntimeRecord.fields.port.label=Current port
i18n_entity.MrRobotRuntimeRecord.fields.rtBins.label=Real-time scheduling of cargo status
i18n_entity.MrRobotRuntimeRecord.fields.rtCmdStatus.label=Real-time scheduling execution status
i18n_entity.MrRobotRuntimeRecord.fields.rtCurrentOrder.label=Real-time scheduling of current transport orders
i18n_entity.MrRobotRuntimeRecord.fields.rtCurrentStep.label=Real-time scheduling of current transport order steps
i18n_entity.MrRobotRuntimeRecord.fields.rtOrders.label=Real-time dispatch transport order list
i18n_entity.MrRobotRuntimeRecord.fields.taskStatus.inlineOptionBill.items.Charging.label=Charging
i18n_entity.MrRobotRuntimeRecord.fields.taskStatus.inlineOptionBill.items.Idle.label=Idle
i18n_entity.MrRobotRuntimeRecord.fields.taskStatus.inlineOptionBill.items.Tasking.label=In the mission
i18n_entity.MrRobotRuntimeRecord.fields.taskStatus.label=Task status
i18n_entity.MrRobotRuntimeRecord.fields.version.label=Revised version
i18n_entity.MrRobotRuntimeRecord.group=WCS
i18n_entity.MrRobotRuntimeRecord.label=Mobile robot operation record
i18n_entity.MrRobotRuntimeRecord.listCard.ctOrders.prefix=Container transport order
i18n_entity.MrRobotRuntimeRecord.listCard.rtBins.prefix=Goods
i18n_entity.MrRobotRuntimeRecord.listCard.rtCurrentOrder.prefix=Current transport order
i18n_entity.MrRobotSystemConfig.fields.category.label=Category
i18n_entity.MrRobotSystemConfig.fields.connectionType.inlineOptionBill.items.GwWs.label=Gateway Connection Server (GW Channel)
i18n_entity.MrRobotSystemConfig.fields.connectionType.inlineOptionBill.items.GwWsLight.label=Gateway connection server (optical communication)
i18n_entity.MrRobotSystemConfig.fields.connectionType.inlineOptionBill.items.Mock.label=Simulation
i18n_entity.MrRobotSystemConfig.fields.connectionType.inlineOptionBill.items.Rbk.label=Server connection robot
i18n_entity.MrRobotSystemConfig.fields.connectionType.label=Connection type
i18n_entity.MrRobotSystemConfig.fields.createdBy.label=Created by
i18n_entity.MrRobotSystemConfig.fields.createdOn.label=Created time
i18n_entity.MrRobotSystemConfig.fields.disabled.label=Disable
i18n_entity.MrRobotSystemConfig.fields.gwAuthId.label=Gateway login ID
i18n_entity.MrRobotSystemConfig.fields.gwAuthSecret.label=Gateway login Key
i18n_entity.MrRobotSystemConfig.fields.id.label=ID
i18n_entity.MrRobotSystemConfig.fields.image.label=Picture
i18n_entity.MrRobotSystemConfig.fields.modifiedBy.label=Last modified by
i18n_entity.MrRobotSystemConfig.fields.modifiedOn.label=Last modified time
i18n_entity.MrRobotSystemConfig.fields.offDuty.label=OffDuty
i18n_entity.MrRobotSystemConfig.fields.robotHost.label=Robot host
i18n_entity.MrRobotSystemConfig.fields.scene.label=Scene
i18n_entity.MrRobotSystemConfig.fields.selfBinNum.label=Maximum cargo capacity
i18n_entity.MrRobotSystemConfig.fields.ssl.label=SSL/TSL encryption
i18n_entity.MrRobotSystemConfig.fields.tags.label=Label
i18n_entity.MrRobotSystemConfig.fields.taskMode.inlineOptionBill.items.Custom.label=Custom scheduling
i18n_entity.MrRobotSystemConfig.fields.taskMode.inlineOptionBill.items.SingleRobot.label=M4A1
i18n_entity.MrRobotSystemConfig.fields.taskMode.inlineOptionBill.items.Tom.label=Core
i18n_entity.MrRobotSystemConfig.fields.taskMode.label=Task mode
i18n_entity.MrRobotSystemConfig.fields.tomId.label=Tom ID
i18n_entity.MrRobotSystemConfig.fields.vendor.inlineOptionBill.items.Hai.label=Hai Robotics
i18n_entity.MrRobotSystemConfig.fields.vendor.inlineOptionBill.items.Hik.label=HIKROBOT
i18n_entity.MrRobotSystemConfig.fields.vendor.inlineOptionBill.items.Seer.label=SEER Robotics
i18n_entity.MrRobotSystemConfig.fields.vendor.label=Vendor
i18n_entity.MrRobotSystemConfig.fields.version.label=Revised version
i18n_entity.MrRobotSystemConfig.group=WCS
i18n_entity.MrRobotSystemConfig.label=Mobile robot system configuration
i18n_entity.MrRobotSystemConfig.listCard.scene.prefix=Scene
i18n_entity.MrRobotSystemConfig.listCard.selfBinNum.prefix=Maximum cargo
i18n_entity.OrderFlowRecord.fields.createdBy.label=Created by
i18n_entity.OrderFlowRecord.fields.createdOn.label=Created time
i18n_entity.OrderFlowRecord.fields.id.label=ID
i18n_entity.OrderFlowRecord.fields.modifiedBy.label=Last modified by
i18n_entity.OrderFlowRecord.fields.modifiedOn.label=Last modified time
i18n_entity.OrderFlowRecord.fields.pushType.label=Type
i18n_entity.OrderFlowRecord.fields.sourceOrderId.label=Source order ID
i18n_entity.OrderFlowRecord.fields.sourceOrderName.label=Source order name
i18n_entity.OrderFlowRecord.fields.targetOrderId.label=New order ID
i18n_entity.OrderFlowRecord.fields.targetOrderName.label=New order name
i18n_entity.OrderFlowRecord.fields.targetOrderType.label=New order type
i18n_entity.OrderFlowRecord.fields.txId.label=Transaction ID
i18n_entity.OrderFlowRecord.fields.version.label=Revised version
i18n_entity.OrderFlowRecord.group=Core
i18n_entity.OrderFlowRecord.label=Order flow record
i18n_entity.PickOrder.fields.allUsed.label=Whole pallet sorting
i18n_entity.PickOrder.fields.btLines.label=Order Line
i18n_entity.PickOrder.fields.btOrderKind.inlineOptionBill.items.NormalTask.label=Loading task
i18n_entity.PickOrder.fields.btOrderKind.label=Type
i18n_entity.PickOrder.fields.btOrderState.inlineOptionBill.items.Done.label=picking completed
i18n_entity.PickOrder.fields.btOrderState.inlineOptionBill.items.Todo.label=To be picked
i18n_entity.PickOrder.fields.btOrderState.label=Status
i18n_entity.PickOrder.fields.btOrderStateReason.label=Status description
i18n_entity.PickOrder.fields.container.label=Container
i18n_entity.PickOrder.fields.containerBackOrderId.label=Container return transport order number
i18n_entity.PickOrder.fields.containerOutDone.label=Container outbound transportation completed
i18n_entity.PickOrder.fields.containerOutOrderId.label=Container outbound transport order number
i18n_entity.PickOrder.fields.createdBy.label=Created by
i18n_entity.PickOrder.fields.createdOn.label=Created time
i18n_entity.PickOrder.fields.id.label=Order ID
i18n_entity.PickOrder.fields.modifiedBy.label=Last modified by
i18n_entity.PickOrder.fields.modifiedOn.label=Last modified time
i18n_entity.PickOrder.fields.sourceOrderId.label=Outbound order ID
i18n_entity.PickOrder.fields.submittedPostProcessed.label=Post-processing completed
i18n_entity.PickOrder.fields.version.label=Revised version
i18n_entity.PickOrder.group=Warehouse
i18n_entity.PickOrder.label=Picking Order
i18n_entity.PickOrder.listCard.container.prefix=Container
i18n_entity.PickOrder.listCard.sourceOrderId.prefix=Outbound Order
i18n_entity.PickOrder.states.states.Done.label=picking completed
i18n_entity.PickOrder.states.states.Todo.label=To be picked
i18n_entity.PickOrder.states.states.Todo.nextStates.Done.buttonLabel=Completed
i18n_entity.PickOrderLine.fields.btLineNo.label=Line No.
i18n_entity.PickOrderLine.fields.btMaterial.label=Material
i18n_entity.PickOrderLine.fields.btMaterialCategory.label=Material category ID
i18n_entity.PickOrderLine.fields.btMaterialCategoryName.label=Material category name
i18n_entity.PickOrderLine.fields.btMaterialId.label=Material ID
i18n_entity.PickOrderLine.fields.btMaterialImage.label=Material pictures
i18n_entity.PickOrderLine.fields.btMaterialModel.label=Material model
i18n_entity.PickOrderLine.fields.btMaterialName.label=Material name
i18n_entity.PickOrderLine.fields.btMaterialSpec.label=Material specs.
i18n_entity.PickOrderLine.fields.btParentId.label=Associated order
i18n_entity.PickOrderLine.fields.container.label=Container
i18n_entity.PickOrderLine.fields.createdBy.label=Created by
i18n_entity.PickOrderLine.fields.createdOn.label=Created time
i18n_entity.PickOrderLine.fields.id.label=ID
i18n_entity.PickOrderLine.fields.lotNo.label=Lot No.
i18n_entity.PickOrderLine.fields.modifiedBy.label=Last modified by
i18n_entity.PickOrderLine.fields.modifiedOn.label=Last modified time
i18n_entity.PickOrderLine.fields.planQty.label=Expected pick qty
i18n_entity.PickOrderLine.fields.qty.label=Actual pick qty
i18n_entity.PickOrderLine.fields.qualityLevel.label=Quality grade
i18n_entity.PickOrderLine.fields.sourceLineId.label=Source line ID
i18n_entity.PickOrderLine.fields.sourceLineNo.label=Source line No.
i18n_entity.PickOrderLine.fields.sourceOrderId.label=Source order
i18n_entity.PickOrderLine.fields.subContainerId.label=Cell ID
i18n_entity.PickOrderLine.fields.version.label=Revised version
i18n_entity.PickOrderLine.group=Warehouse
i18n_entity.PickOrderLine.label=Pick Order line
i18n_entity.PickOrderLine.listCard.btMaterialId.prefix=Material ID
i18n_entity.PickOrderLine.listCard.lotNo.prefix=Lot No.
i18n_entity.PickOrderLine.listCard.planQty.prefix=Expected picking
i18n_entity.PickOrderLine.listCard.qty.prefix=Actual picking
i18n_entity.PickOrderLine.listCard.subContainerId.prefix=Cell ID
i18n_entity.PlcDeviceConfig.fields.autoRetry.label=Automatic reconnect
i18n_entity.PlcDeviceConfig.fields.createdBy.label=Created by
i18n_entity.PlcDeviceConfig.fields.createdOn.label=Created time
i18n_entity.PlcDeviceConfig.fields.disabled.label=Deactivated
i18n_entity.PlcDeviceConfig.fields.endpoint.label=Connection string
i18n_entity.PlcDeviceConfig.fields.host.label=Host
i18n_entity.PlcDeviceConfig.fields.id.label=Name (ID)
i18n_entity.PlcDeviceConfig.fields.maxRetry.label=Maximum number of retries
i18n_entity.PlcDeviceConfig.fields.modifiedBy.label=Last modified by
i18n_entity.PlcDeviceConfig.fields.modifiedOn.label=Last modified time
i18n_entity.PlcDeviceConfig.fields.port.label=Port
i18n_entity.PlcDeviceConfig.fields.rack.label=S7 rack ID
i18n_entity.PlcDeviceConfig.fields.retryDelay.label=Retry wait (milliseconds)
i18n_entity.PlcDeviceConfig.fields.slot.label=S7 slot ID
i18n_entity.PlcDeviceConfig.fields.subType.label=Sub-type
i18n_entity.PlcDeviceConfig.fields.timeout.label=Connection timeout (milliseconds)
i18n_entity.PlcDeviceConfig.fields.type.inlineOptionBill.items.Modbus.label=Modbus
i18n_entity.PlcDeviceConfig.fields.type.inlineOptionBill.items.OPCUA.label=OPCUA
i18n_entity.PlcDeviceConfig.fields.type.inlineOptionBill.items.S7.label=S7
i18n_entity.PlcDeviceConfig.fields.type.label=Type
i18n_entity.PlcDeviceConfig.fields.version.label=Revised version
i18n_entity.PlcDeviceConfig.group=WCS
i18n_entity.PlcDeviceConfig.label=PLC equipment configuration
i18n_entity.PlcDeviceConfig.listCard.host.prefix=Host
i18n_entity.PlcDeviceConfig.listCard.port.prefix=Port
i18n_entity.PlcRwLog.fields.action.label=Operation
i18n_entity.PlcRwLog.fields.createdBy.label=Created by
i18n_entity.PlcRwLog.fields.createdOn.label=Created time
i18n_entity.PlcRwLog.fields.deviceName.label=Device name
i18n_entity.PlcRwLog.fields.deviceType.label=Device type
i18n_entity.PlcRwLog.fields.id.label=ID
i18n_entity.PlcRwLog.fields.ip.label=IP
i18n_entity.PlcRwLog.fields.modifiedBy.label=Last modified by
i18n_entity.PlcRwLog.fields.modifiedOn.label=Last modified time
i18n_entity.PlcRwLog.fields.oldValueDesc.label=Original value
i18n_entity.PlcRwLog.fields.reason.label=Reason
i18n_entity.PlcRwLog.fields.rw.inlineOptionBill.items.Read.label=Read
i18n_entity.PlcRwLog.fields.rw.inlineOptionBill.items.Write.label=Write
i18n_entity.PlcRwLog.fields.rw.label=Read and write
i18n_entity.PlcRwLog.fields.valueDesc.label=Value
i18n_entity.PlcRwLog.fields.version.label=Revised version
i18n_entity.PlcRwLog.group=WCS
i18n_entity.PlcRwLog.label=PLC read and write record
i18n_entity.PlcRwLog.listCard.oldValueDesc.prefix=Original value
i18n_entity.PlcRwLog.listCard.valueDesc.prefix=Value
i18n_entity.PutinContainerOrder.fields.btLines.label=Order Line
i18n_entity.PutinContainerOrder.fields.btOrderKind.inlineOptionBill.items.NormalTask.label=Loading task
i18n_entity.PutinContainerOrder.fields.btOrderKind.label=Type
i18n_entity.PutinContainerOrder.fields.btOrderState.inlineOptionBill.items.Done.label=Putaway completed
i18n_entity.PutinContainerOrder.fields.btOrderState.inlineOptionBill.items.Filled.label=Loading completed
i18n_entity.PutinContainerOrder.fields.btOrderState.inlineOptionBill.items.Todo.label=To be loaded
i18n_entity.PutinContainerOrder.fields.btOrderState.label=Status
i18n_entity.PutinContainerOrder.fields.btOrderStateReason.label=Status description
i18n_entity.PutinContainerOrder.fields.container.label=Container
i18n_entity.PutinContainerOrder.fields.containerBackOrderId.label=Container return transport order ID
i18n_entity.PutinContainerOrder.fields.containerOutDone.label=Container outbound transportation completed
i18n_entity.PutinContainerOrder.fields.containerOutOrderId.label=Container outbound transport order ID
i18n_entity.PutinContainerOrder.fields.createdBy.label=Created by
i18n_entity.PutinContainerOrder.fields.createdOn.label=Created time
i18n_entity.PutinContainerOrder.fields.id.label=Order ID
i18n_entity.PutinContainerOrder.fields.modifiedBy.label=Last modified by
i18n_entity.PutinContainerOrder.fields.modifiedOn.label=Last modified time
i18n_entity.PutinContainerOrder.fields.sourceOrderId.label=Inbound order ID
i18n_entity.PutinContainerOrder.fields.version.label=Revised version
i18n_entity.PutinContainerOrder.group=Warehouse
i18n_entity.PutinContainerOrder.label=Loading order
i18n_entity.PutinContainerOrder.listCard.container.prefix=Container
i18n_entity.PutinContainerOrder.states.states.Done.label=Putaway completed
i18n_entity.PutinContainerOrder.states.states.Filled.label=Loading completed
i18n_entity.PutinContainerOrder.states.states.Todo.label=To be loaded
i18n_entity.PutinContainerOrder.states.states.Todo.nextStates.Filled.buttonLabel=Completed
i18n_entity.PutinContainerOrderLine.fields.btLineNo.label=Line No.
i18n_entity.PutinContainerOrderLine.fields.btMaterial.label=Material
i18n_entity.PutinContainerOrderLine.fields.btMaterialCategory.label=Material category ID
i18n_entity.PutinContainerOrderLine.fields.btMaterialCategoryName.label=Material category name
i18n_entity.PutinContainerOrderLine.fields.btMaterialId.label=Material ID
i18n_entity.PutinContainerOrderLine.fields.btMaterialImage.label=Material pictures
i18n_entity.PutinContainerOrderLine.fields.btMaterialModel.label=Material model
i18n_entity.PutinContainerOrderLine.fields.btMaterialName.label=Material name
i18n_entity.PutinContainerOrderLine.fields.btMaterialSpec.label=Material specs.
i18n_entity.PutinContainerOrderLine.fields.btParentId.label=Associated Order
i18n_entity.PutinContainerOrderLine.fields.container.label=Container
i18n_entity.PutinContainerOrderLine.fields.createdBy.label=Created by
i18n_entity.PutinContainerOrderLine.fields.createdOn.label=Created time
i18n_entity.PutinContainerOrderLine.fields.id.label=ID
i18n_entity.PutinContainerOrderLine.fields.lotNo.label=Lot No.
i18n_entity.PutinContainerOrderLine.fields.modifiedBy.label=Last modified by
i18n_entity.PutinContainerOrderLine.fields.modifiedOn.label=Last modified time
i18n_entity.PutinContainerOrderLine.fields.planQty.label=Planned loading qty
i18n_entity.PutinContainerOrderLine.fields.price.label=Unit price
i18n_entity.PutinContainerOrderLine.fields.qty.label=Actual loading qty
i18n_entity.PutinContainerOrderLine.fields.qualityLevel.label=Quality grade
i18n_entity.PutinContainerOrderLine.fields.sourceLineId.label=Source line ID
i18n_entity.PutinContainerOrderLine.fields.sourceLineNo.label=Source line No.
i18n_entity.PutinContainerOrderLine.fields.sourceOrderId.label=Source order
i18n_entity.PutinContainerOrderLine.fields.subContainerId.label=Cell ID
i18n_entity.PutinContainerOrderLine.fields.unitLabel.label=Unit name
i18n_entity.PutinContainerOrderLine.fields.version.label=Revised version
i18n_entity.PutinContainerOrderLine.group=Warehouse
i18n_entity.PutinContainerOrderLine.label=Loading order line
i18n_entity.PutinContainerOrderLine.listCard.btMaterialId.prefix=Material ID
i18n_entity.PutinContainerOrderLine.listCard.btMaterialName.prefix=Material name
i18n_entity.PutinContainerOrderLine.listCard.lotNo.prefix=Lot No.
i18n_entity.PutinContainerOrderLine.listCard.planQty.prefix=Planned loading
i18n_entity.PutinContainerOrderLine.listCard.qty.prefix=Actual loading
i18n_entity.PutinContainerOrderLine.listCard.subContainerId.prefix=Cell ID
i18n_entity.QsInboundOrder.fields.btBzKind.inlineOptionBill.items.Normal.label=Ordinary inbound
i18n_entity.QsInboundOrder.fields.btBzKind.inlineOptionBill.items.Other.label=Other inbound
i18n_entity.QsInboundOrder.fields.btBzKind.label=Business type
i18n_entity.QsInboundOrder.fields.btLines.label=Order Line
i18n_entity.QsInboundOrder.fields.btOrderState.inlineOptionBill.items.Cancelled.label=Cancelled
i18n_entity.QsInboundOrder.fields.btOrderState.inlineOptionBill.items.Committed.label=Submitted
i18n_entity.QsInboundOrder.fields.btOrderState.inlineOptionBill.items.Init.label=Not submitted
i18n_entity.QsInboundOrder.fields.btOrderState.label=Status
i18n_entity.QsInboundOrder.fields.btOrderStateReason.label=Status description
i18n_entity.QsInboundOrder.fields.callContainerAll.label=Call empty container allocation completed
i18n_entity.QsInboundOrder.fields.createdBy.label=Created by
i18n_entity.QsInboundOrder.fields.createdOn.label=Created time
i18n_entity.QsInboundOrder.fields.id.label=Order ID
i18n_entity.QsInboundOrder.fields.modifiedBy.label=Last modified by
i18n_entity.QsInboundOrder.fields.modifiedOn.label=Last modified time
i18n_entity.QsInboundOrder.fields.otherType.label=Other types
i18n_entity.QsInboundOrder.fields.priority.label=Priority
i18n_entity.QsInboundOrder.fields.remark.label=Remarks
i18n_entity.QsInboundOrder.fields.ro.label=Root tissue
i18n_entity.QsInboundOrder.fields.version.label=Version
i18n_entity.QsInboundOrder.group=Quick Store
i18n_entity.QsInboundOrder.kinds.kinds.Normal.label=Ordinary inbound
i18n_entity.QsInboundOrder.kinds.kinds.Other.label=Other inbound
i18n_entity.QsInboundOrder.label=QS inbound order
i18n_entity.QsInboundOrder.states.states.Cancelled.label=Cancelled
i18n_entity.QsInboundOrder.states.states.Committed.label=Submitted
i18n_entity.QsInboundOrder.states.states.Init.label=Not submitted
i18n_entity.QsInboundOrder.states.states.Init.nextStates.Cancelled.buttonLabel=Cancel
i18n_entity.QsInboundOrder.states.states.Init.nextStates.Committed.buttonLabel=Submit
i18n_entity.QsInboundOrderLine.fields.btLineNo.label=Line No.
i18n_entity.QsInboundOrderLine.fields.btMaterial.label=Material
i18n_entity.QsInboundOrderLine.fields.btMaterialCategory.label=Material category ID
i18n_entity.QsInboundOrderLine.fields.btMaterialCategoryName.label=Material category name
i18n_entity.QsInboundOrderLine.fields.btMaterialId.label=Material ID
i18n_entity.QsInboundOrderLine.fields.btMaterialImage.label=Material pictures
i18n_entity.QsInboundOrderLine.fields.btMaterialModel.label=Material model
i18n_entity.QsInboundOrderLine.fields.btMaterialName.label=Material name
i18n_entity.QsInboundOrderLine.fields.btMaterialSpec.label=Material specs.
i18n_entity.QsInboundOrderLine.fields.btParentId.label=Associated outbound order
i18n_entity.QsInboundOrderLine.fields.ccQty.label=Call empty container allocation quantity
i18n_entity.QsInboundOrderLine.fields.createdBy.label=Created by
i18n_entity.QsInboundOrderLine.fields.createdOn.label=Created time
i18n_entity.QsInboundOrderLine.fields.id.label=ID
i18n_entity.QsInboundOrderLine.fields.lotNo.label=Lot No.
i18n_entity.QsInboundOrderLine.fields.modifiedBy.label=Last modified by
i18n_entity.QsInboundOrderLine.fields.modifiedOn.label=Last modified time
i18n_entity.QsInboundOrderLine.fields.priority.label=Priority
i18n_entity.QsInboundOrderLine.fields.qty.label=Inbound qty
i18n_entity.QsInboundOrderLine.fields.ro.label=Root tissue
i18n_entity.QsInboundOrderLine.fields.version.label=Version
i18n_entity.QsInboundOrderLine.group=Quick Store
i18n_entity.QsInboundOrderLine.label=QS inbound order line
i18n_entity.QsInboundOrderLine.listCard.qty.prefix=Qty
i18n_entity.QsMoveBinOrder.fields.actualToBin.label=Actual end bin
i18n_entity.QsMoveBinOrder.fields.btBzKind.inlineOptionBill.items.Normal.label=Default
i18n_entity.QsMoveBinOrder.fields.btBzKind.label=Business type
i18n_entity.QsMoveBinOrder.fields.container.label=Container
i18n_entity.QsMoveBinOrder.fields.createdBy.label=Created by
i18n_entity.QsMoveBinOrder.fields.createdOn.label=Created time
i18n_entity.QsMoveBinOrder.fields.expectToBin.label=Expect end bin
i18n_entity.QsMoveBinOrder.fields.expectToDistrict.label=Expect to district
i18n_entity.QsMoveBinOrder.fields.fromBin.label=Start bin
i18n_entity.QsMoveBinOrder.fields.id.label=ID
i18n_entity.QsMoveBinOrder.fields.modifiedBy.label=Last modified by
i18n_entity.QsMoveBinOrder.fields.modifiedOn.label=Last modified time
i18n_entity.QsMoveBinOrder.fields.oldInvLines.label=Inventory details during movement
i18n_entity.QsMoveBinOrder.fields.remark.label=Remark
i18n_entity.QsMoveBinOrder.fields.version.label=Revised version
i18n_entity.QsMoveBinOrder.group=Quick Store
i18n_entity.QsMoveBinOrder.kinds.kinds.Normal.label=Default
i18n_entity.QsMoveBinOrder.label=QS moving order
i18n_entity.QsMoveBinOrder.listCard.container.prefix=Container
i18n_entity.QsMoveBinOrder.listCard.fromBin.suffix=->
i18n_entity.QsOldInvLine.fields.amount.label=Amount
i18n_entity.QsOldInvLine.fields.btLineNo.label=Line No.
i18n_entity.QsOldInvLine.fields.btMaterial.label=Materials
i18n_entity.QsOldInvLine.fields.btMaterialCategory.label=Material category ID
i18n_entity.QsOldInvLine.fields.btMaterialCategoryName.label=Material category name
i18n_entity.QsOldInvLine.fields.btMaterialImage.label=Material pictures
i18n_entity.QsOldInvLine.fields.btMaterialModel.label=Material type
i18n_entity.QsOldInvLine.fields.btMaterialName.label=Material name
i18n_entity.QsOldInvLine.fields.btMaterialSpec.label=Material specs.
i18n_entity.QsOldInvLine.fields.btParentId.label=Associated order ID
i18n_entity.QsOldInvLine.fields.createdBy.label=Created by
i18n_entity.QsOldInvLine.fields.createdOn.label=Created time
i18n_entity.QsOldInvLine.fields.expDate.label=Valid period
i18n_entity.QsOldInvLine.fields.id.label=ID
i18n_entity.QsOldInvLine.fields.inboundOn.label=Inbound time
i18n_entity.QsOldInvLine.fields.leafContainer.label=Innermost container
i18n_entity.QsOldInvLine.fields.lotNo.label=Lot No.
i18n_entity.QsOldInvLine.fields.matLotNo.label=Material lot No.
i18n_entity.QsOldInvLine.fields.matSerialNo.label=Material serial No.
i18n_entity.QsOldInvLine.fields.mfgDate.label=Production date
i18n_entity.QsOldInvLine.fields.modifiedBy.label=Last modified by
i18n_entity.QsOldInvLine.fields.modifiedOn.label=Last modified time
i18n_entity.QsOldInvLine.fields.owner.label=Owner
i18n_entity.QsOldInvLine.fields.price.label=Unit price
i18n_entity.QsOldInvLine.fields.qty.label=Qty
i18n_entity.QsOldInvLine.fields.refInvId.label=Linked Inventory Detail ID
i18n_entity.QsOldInvLine.fields.subContainerId.label=Cell ID
i18n_entity.QsOldInvLine.fields.topContainer.label=Outermost container
i18n_entity.QsOldInvLine.fields.vendor.label=Vendor
i18n_entity.QsOldInvLine.fields.version.label=Revised version
i18n_entity.QsOldInvLine.group=Quick Store
i18n_entity.QsOldInvLine.label=QS Existing Inventory Details
i18n_entity.QsOldInvLine.listStats.items[0].label=Processing (locked)
i18n_entity.QsOutboundOrder.fields.btLines.label=Order line
i18n_entity.QsOutboundOrder.fields.btOrderKind.inlineOptionBill.items.Default.label=Default
i18n_entity.QsOutboundOrder.fields.btOrderKind.label=Type
i18n_entity.QsOutboundOrder.fields.btOrderState.inlineOptionBill.items.Committed.label=Submitted
i18n_entity.QsOutboundOrder.fields.btOrderState.inlineOptionBill.items.Init.label=Not submitted
i18n_entity.QsOutboundOrder.fields.btOrderState.label=Status
i18n_entity.QsOutboundOrder.fields.btOrderStateReason.label=Status description
i18n_entity.QsOutboundOrder.fields.createdBy.label=Created by
i18n_entity.QsOutboundOrder.fields.createdOn.label=Created time
i18n_entity.QsOutboundOrder.fields.id.label=Order ID
i18n_entity.QsOutboundOrder.fields.invAssignedAll.label=Inventory distribution completed
i18n_entity.QsOutboundOrder.fields.modifiedBy.label=Last modified by
i18n_entity.QsOutboundOrder.fields.modifiedOn.label=Last modified time
i18n_entity.QsOutboundOrder.fields.priority.label=Priority
i18n_entity.QsOutboundOrder.fields.remark.label=Remarks
i18n_entity.QsOutboundOrder.fields.ro.label=Root tissue
i18n_entity.QsOutboundOrder.fields.typePriority.label=Type priority
i18n_entity.QsOutboundOrder.fields.version.label=Version
i18n_entity.QsOutboundOrder.group=Quick Store
i18n_entity.QsOutboundOrder.label=QS outbound order
i18n_entity.QsOutboundOrder.states.states.Committed.label=Submitted
i18n_entity.QsOutboundOrder.states.states.Init.label=Not submitted
i18n_entity.QsOutboundOrder.states.states.Init.nextStates.Committed.buttonLabel=Submit
i18n_entity.QsOutboundOrderLine.fields.btLineNo.label=Line No.
i18n_entity.QsOutboundOrderLine.fields.btMaterial.label=Materials
i18n_entity.QsOutboundOrderLine.fields.btMaterialCategory.label=Material classification number
i18n_entity.QsOutboundOrderLine.fields.btMaterialCategoryName.label=Material classification name
i18n_entity.QsOutboundOrderLine.fields.btMaterialId.label=Material ID
i18n_entity.QsOutboundOrderLine.fields.btMaterialImage.label=Material pictures
i18n_entity.QsOutboundOrderLine.fields.btMaterialModel.label=Material type
i18n_entity.QsOutboundOrderLine.fields.btMaterialName.label=Material name
i18n_entity.QsOutboundOrderLine.fields.btMaterialSpec.label=Material specification
i18n_entity.QsOutboundOrderLine.fields.btParentId.label=Associated outbound order ID
i18n_entity.QsOutboundOrderLine.fields.createdBy.label=Created by
i18n_entity.QsOutboundOrderLine.fields.createdOn.label=Created time
i18n_entity.QsOutboundOrderLine.fields.id.label=ID
i18n_entity.QsOutboundOrderLine.fields.invAssignedQty.label=Allocated inventory qty
i18n_entity.QsOutboundOrderLine.fields.lotNo.label=Lot No.
i18n_entity.QsOutboundOrderLine.fields.modifiedBy.label=Last modified by
i18n_entity.QsOutboundOrderLine.fields.modifiedOn.label=Last modified time
i18n_entity.QsOutboundOrderLine.fields.priority.label=Priority
i18n_entity.QsOutboundOrderLine.fields.qty.label=Outbound qty
i18n_entity.QsOutboundOrderLine.fields.ro.label=Root tissue
i18n_entity.QsOutboundOrderLine.fields.version.label=Version
i18n_entity.QsOutboundOrderLine.group=Quick Store
i18n_entity.QsOutboundOrderLine.label=QS outbound order line
i18n_entity.QsOutboundOrderLine.listCard.qty.prefix=Outbound qty
i18n_entity.QsPickOrder.fields.btLines.label=Order line
i18n_entity.QsPickOrder.fields.btOrderKind.inlineOptionBill.items.NormalTask.label=Loading task
i18n_entity.QsPickOrder.fields.btOrderKind.label=Type
i18n_entity.QsPickOrder.fields.btOrderState.inlineOptionBill.items.Done.label=Picking completed
i18n_entity.QsPickOrder.fields.btOrderState.inlineOptionBill.items.Todo.label=To be picked
i18n_entity.QsPickOrder.fields.btOrderState.label=Status
i18n_entity.QsPickOrder.fields.btOrderStateReason.label=Status description
i18n_entity.QsPickOrder.fields.container.label=Container
i18n_entity.QsPickOrder.fields.createdBy.label=Created by
i18n_entity.QsPickOrder.fields.createdOn.label=Created time
i18n_entity.QsPickOrder.fields.id.label=Order ID
i18n_entity.QsPickOrder.fields.modifiedBy.label=Last modified by
i18n_entity.QsPickOrder.fields.modifiedOn.label=Last modified time
i18n_entity.QsPickOrder.fields.targetBin.label=Target bin
i18n_entity.QsPickOrder.fields.version.label=Revised version
i18n_entity.QsPickOrder.group=Quick Store
i18n_entity.QsPickOrder.label=QS picking order
i18n_entity.QsPickOrder.listCard.container.prefix=Container
i18n_entity.QsPickOrder.states.states.Done.label=Picking completed
i18n_entity.QsPickOrder.states.states.Todo.label=To be picked
i18n_entity.QsPickOrder.states.states.Todo.nextStates.Done.buttonLabel=Completed
i18n_entity.QsPickOrderLine.fields.btLineNo.label=Line No.
i18n_entity.QsPickOrderLine.fields.btMaterial.label=Materials
i18n_entity.QsPickOrderLine.fields.btMaterialCategory.label=Material classification number
i18n_entity.QsPickOrderLine.fields.btMaterialCategoryName.label=Material classification name
i18n_entity.QsPickOrderLine.fields.btMaterialId.label=Material ID
i18n_entity.QsPickOrderLine.fields.btMaterialImage.label=Material pictures
i18n_entity.QsPickOrderLine.fields.btMaterialModel.label=Material type
i18n_entity.QsPickOrderLine.fields.btMaterialName.label=Material name
i18n_entity.QsPickOrderLine.fields.btMaterialSpec.label=Material specification
i18n_entity.QsPickOrderLine.fields.btParentId.label=Associated order ID
i18n_entity.QsPickOrderLine.fields.createdBy.label=Created by
i18n_entity.QsPickOrderLine.fields.createdOn.label=Created time
i18n_entity.QsPickOrderLine.fields.id.label=ID
i18n_entity.QsPickOrderLine.fields.invLayoutId.label=Inventory Detail ID
i18n_entity.QsPickOrderLine.fields.lotNo.label=Lot No.
i18n_entity.QsPickOrderLine.fields.modifiedBy.label=Last modified by
i18n_entity.QsPickOrderLine.fields.modifiedOn.label=Last modified time
i18n_entity.QsPickOrderLine.fields.outboundLineId.label=Outbound order line ID
i18n_entity.QsPickOrderLine.fields.outboundLineNo.label=Outbound order line No.
i18n_entity.QsPickOrderLine.fields.outboundOrderId.label=Outbound order ID
i18n_entity.QsPickOrderLine.fields.planQty.label=Expected pick number
i18n_entity.QsPickOrderLine.fields.qty.label=Actual number of picks
i18n_entity.QsPickOrderLine.fields.subContainerId.label=Cell ID
i18n_entity.QsPickOrderLine.fields.topContainer.label=Container
i18n_entity.QsPickOrderLine.fields.version.label=Revised version
i18n_entity.QsPickOrderLine.group=Quick Store
i18n_entity.QsPickOrderLine.label=QS picking order line
i18n_entity.QsPickOrderLine.listCard.planQty.prefix=Expected qty
i18n_entity.QsPickOrderLine.listCard.qty.prefix=Actual qty
i18n_entity.QsPutOnContainerOrder.fields.bin.label=Putaway bin
i18n_entity.QsPutOnContainerOrder.fields.container.label=Container
i18n_entity.QsPutOnContainerOrder.fields.createdBy.label=Created by
i18n_entity.QsPutOnContainerOrder.fields.createdOn.label=Created time
i18n_entity.QsPutOnContainerOrder.fields.id.label=ID
i18n_entity.QsPutOnContainerOrder.fields.modifiedBy.label=Last modified by
i18n_entity.QsPutOnContainerOrder.fields.modifiedOn.label=Last modified time
i18n_entity.QsPutOnContainerOrder.fields.oldInvLines.label=Inventory details in container when put away
i18n_entity.QsPutOnContainerOrder.fields.version.label=Revised version
i18n_entity.QsPutOnContainerOrder.group=Quick Store
i18n_entity.QsPutOnContainerOrder.label=QS manual put away order
i18n_entity.QsPutOnContainerOrder.listCard.bin.prefix=Putaway bin
i18n_entity.QsPutOrder.fields.bin.label=Putaway bin
i18n_entity.QsPutOrder.fields.btLines.label=Order line
i18n_entity.QsPutOrder.fields.btOrderKind.inlineOptionBill.items.NormalTask.label=Loading task
i18n_entity.QsPutOrder.fields.btOrderKind.label=Type
i18n_entity.QsPutOrder.fields.btOrderState.inlineOptionBill.items.Done.label=Completion of loading
i18n_entity.QsPutOrder.fields.btOrderState.inlineOptionBill.items.Todo.label=To be loaded
i18n_entity.QsPutOrder.fields.btOrderState.label=Status
i18n_entity.QsPutOrder.fields.btOrderStateReason.label=Status description
i18n_entity.QsPutOrder.fields.container.label=Container
i18n_entity.QsPutOrder.fields.createdBy.label=Created by
i18n_entity.QsPutOrder.fields.createdOn.label=Created time
i18n_entity.QsPutOrder.fields.id.label=Order ID
i18n_entity.QsPutOrder.fields.modifiedBy.label=Last modified by
i18n_entity.QsPutOrder.fields.modifiedOn.label=Last modified time
i18n_entity.QsPutOrder.fields.oldInvLines.label=Original inventory details in the container
i18n_entity.QsPutOrder.fields.targetBin.label=Target bin
i18n_entity.QsPutOrder.fields.version.label=Revised version
i18n_entity.QsPutOrder.group=Quick Store
i18n_entity.QsPutOrder.label=QS loading order
i18n_entity.QsPutOrder.listCard.bin.prefix=Putaway bin
i18n_entity.QsPutOrder.listCard.container.prefix=Container
i18n_entity.QsPutOrder.states.states.Done.label=Completion of loading
i18n_entity.QsPutOrder.states.states.Todo.label=To be loaded
i18n_entity.QsPutOrder.states.states.Todo.nextStates.Done.buttonLabel=Completed
i18n_entity.QsPutOrderLine.fields.btLineNo.label=Line No.
i18n_entity.QsPutOrderLine.fields.btMaterial.label=Materials
i18n_entity.QsPutOrderLine.fields.btMaterialCategory.label=Material classification number
i18n_entity.QsPutOrderLine.fields.btMaterialCategoryName.label=Material classification name
i18n_entity.QsPutOrderLine.fields.btMaterialId.label=Material ID
i18n_entity.QsPutOrderLine.fields.btMaterialImage.label=Material pictures
i18n_entity.QsPutOrderLine.fields.btMaterialModel.label=Material type
i18n_entity.QsPutOrderLine.fields.btMaterialName.label=Material name
i18n_entity.QsPutOrderLine.fields.btMaterialSpec.label=Material specification
i18n_entity.QsPutOrderLine.fields.btParentId.label=Associated order ID
i18n_entity.QsPutOrderLine.fields.createdBy.label=Created by
i18n_entity.QsPutOrderLine.fields.createdOn.label=Created time
i18n_entity.QsPutOrderLine.fields.id.label=ID
i18n_entity.QsPutOrderLine.fields.lotNo.label=Lot No.
i18n_entity.QsPutOrderLine.fields.modifiedBy.label=Last modified by
i18n_entity.QsPutOrderLine.fields.modifiedOn.label=Last modified time
i18n_entity.QsPutOrderLine.fields.planQty.label=Expected loading qty
i18n_entity.QsPutOrderLine.fields.qty.label=Actual loading qty
i18n_entity.QsPutOrderLine.fields.subContainerId.label=Cell ID
i18n_entity.QsPutOrderLine.fields.version.label=Revised version
i18n_entity.QsPutOrderLine.group=Quick Store
i18n_entity.QsPutOrderLine.label=QS loading order line
i18n_entity.QsPutOrderLine.listCard.planQty.prefix=Expected loading
i18n_entity.QsPutOrderLine.listCard.qty.prefix=Actual loading
i18n_entity.QsTakeOffContainerOrder.fields.bin.label=Take away bin
i18n_entity.QsTakeOffContainerOrder.fields.container.label=Container
i18n_entity.QsTakeOffContainerOrder.fields.createdBy.label=Created by
i18n_entity.QsTakeOffContainerOrder.fields.createdOn.label=Created time
i18n_entity.QsTakeOffContainerOrder.fields.id.label=ID
i18n_entity.QsTakeOffContainerOrder.fields.keepInv.label=Retention inventory
i18n_entity.QsTakeOffContainerOrder.fields.modifiedBy.label=Last modified by
i18n_entity.QsTakeOffContainerOrder.fields.modifiedOn.label=Last modified time
i18n_entity.QsTakeOffContainerOrder.fields.oldInvLines.label=Inventory details in the container at the time of removal
i18n_entity.QsTakeOffContainerOrder.fields.version.label=Revised version
i18n_entity.QsTakeOffContainerOrder.group=Quick Store
i18n_entity.QsTakeOffContainerOrder.label=QS manual take away order
i18n_entity.QsTakeOffContainerOrder.listCard.bin.prefix=Take away bin
i18n_entity.ResourceLock.fields.createdBy.label=Created by
i18n_entity.ResourceLock.fields.createdOn.label=Created time
i18n_entity.ResourceLock.fields.id.label=ID
i18n_entity.ResourceLock.fields.lockOn.label=Locking time
i18n_entity.ResourceLock.fields.locked.label=Lock
i18n_entity.ResourceLock.fields.modifiedBy.label=Last modified by
i18n_entity.ResourceLock.fields.modifiedOn.label=Modification time
i18n_entity.ResourceLock.fields.owner.label=Locker
i18n_entity.ResourceLock.fields.reason.label=Lock reason
i18n_entity.ResourceLock.fields.resId.label=Resource ID
i18n_entity.ResourceLock.fields.resType.label=Resource type
i18n_entity.ResourceLock.fields.version.label=Revised version
i18n_entity.ResourceLock.group=Core
i18n_entity.ResourceLock.label=Resource lock
i18n_entity.RobotConnectedPoint.fields.bin.label=Associated bin
i18n_entity.RobotConnectedPoint.fields.channel.label=In aisle
i18n_entity.RobotConnectedPoint.fields.createdBy.label=Created by
i18n_entity.RobotConnectedPoint.fields.createdOn.label=Created time
i18n_entity.RobotConnectedPoint.fields.id.label=ID
i18n_entity.RobotConnectedPoint.fields.modifiedBy.label=Last modified by
i18n_entity.RobotConnectedPoint.fields.modifiedOn.label=Last modified time
i18n_entity.RobotConnectedPoint.fields.remark.label=Remarks
i18n_entity.RobotConnectedPoint.fields.version.label=Revised version
i18n_entity.RobotConnectedPoint.fields.x.label=Location x
i18n_entity.RobotConnectedPoint.fields.y.label=Location y
i18n_entity.RobotConnectedPoint.group=WCS
i18n_entity.RobotConnectedPoint.label=Robot communication site
i18n_entity.RobotConnectedPoint.listCard.bin.prefix=Associated bin
i18n_entity.RobotConnectedPoint.listCard.x.prefix=Location x
i18n_entity.RobotConnectedPoint.listCard.y.prefix=Location y
i18n_entity.RobustScriptExecutor.fields.args.label=Parameter
i18n_entity.RobustScriptExecutor.fields.createdBy.label=Created by
i18n_entity.RobustScriptExecutor.fields.createdOn.label=Created time
i18n_entity.RobustScriptExecutor.fields.description.label=Description
i18n_entity.RobustScriptExecutor.fields.fault.label=Failure
i18n_entity.RobustScriptExecutor.fields.faultMsg.label=Fault message
i18n_entity.RobustScriptExecutor.fields.funcName.label=Method
i18n_entity.RobustScriptExecutor.fields.id.label=ID
i18n_entity.RobustScriptExecutor.fields.modifiedBy.label=Last modified by
i18n_entity.RobustScriptExecutor.fields.modifiedOn.label=Last modified time
i18n_entity.RobustScriptExecutor.fields.version.label=Revised version
i18n_entity.RobustScriptExecutor.group=Core
i18n_entity.RobustScriptExecutor.label=Background tasks
i18n_entity.RobustScriptExecutor.pagesButtons.ListMain.buttons[0].label=Batch edit
i18n_entity.RobustScriptExecutor.pagesButtons.ListMain.buttons[1].label=Delete
i18n_entity.RobustScriptExecutor.pagesButtons.ListMain.buttons[2].label=Restore
i18n_entity.ScriptRunOnce.fields.createdBy.label=Created by
i18n_entity.ScriptRunOnce.fields.createdOn.label=Created time
i18n_entity.ScriptRunOnce.fields.id.label=ID
i18n_entity.ScriptRunOnce.fields.modifiedBy.label=Last modified by
i18n_entity.ScriptRunOnce.fields.modifiedOn.label=Modification time
i18n_entity.ScriptRunOnce.fields.output.label=Output
i18n_entity.ScriptRunOnce.fields.version.label=Revised version
i18n_entity.ScriptRunOnce.group=Core
i18n_entity.ScriptRunOnce.label=The script runs once
i18n_entity.SimpleTransportOrder.fields.createdBy.label=Created by
i18n_entity.SimpleTransportOrder.fields.createdOn.label=Created time
i18n_entity.SimpleTransportOrder.fields.currentMove.label=Current step
i18n_entity.SimpleTransportOrder.fields.doneOn.label=End time
i18n_entity.SimpleTransportOrder.fields.errorMsg.label=Error message
i18n_entity.SimpleTransportOrder.fields.id.label=ID
i18n_entity.SimpleTransportOrder.fields.modifiedBy.label=Last modified by
i18n_entity.SimpleTransportOrder.fields.modifiedOn.label=Last modified time
i18n_entity.SimpleTransportOrder.fields.moves.label=Action list
i18n_entity.SimpleTransportOrder.fields.robotName.label=Robot
i18n_entity.SimpleTransportOrder.fields.seer3066.label=Specified path navigation
i18n_entity.SimpleTransportOrder.fields.status.inlineOptionBill.items.Cancelled.label=Cancelled
i18n_entity.SimpleTransportOrder.fields.status.inlineOptionBill.items.Created.label=Created
i18n_entity.SimpleTransportOrder.fields.status.inlineOptionBill.items.Done.label=Completed
i18n_entity.SimpleTransportOrder.fields.status.inlineOptionBill.items.Failed.label=Failure
i18n_entity.SimpleTransportOrder.fields.status.label=Status
i18n_entity.SimpleTransportOrder.fields.vendor.label=Manufacturers
i18n_entity.SimpleTransportOrder.fields.version.label=Revised version
i18n_entity.SimpleTransportOrder.group=GW
i18n_entity.SimpleTransportOrder.label=Simple transport Order
i18n_entity.SimpleTransportOrder.listCard.robotName.prefix=Robot
i18n_entity.SimpleTransportOrder.listCard.vendor.prefix=Manufacturers
i18n_entity.SimpleTransportOrder.listStats.items[0].label=In implementation
i18n_entity.SimpleTransportOrder.listStats.items[1].label=Failure
i18n_entity.SocNode.fields.attention.label=Attention
i18n_entity.SocNode.fields.createdBy.label=Created by
i18n_entity.SocNode.fields.createdOn.label=Created time
i18n_entity.SocNode.fields.description.label=Explanation
i18n_entity.SocNode.fields.id.label=ID
i18n_entity.SocNode.fields.label.label=Label
i18n_entity.SocNode.fields.modifiedBy.label=Last modified by
i18n_entity.SocNode.fields.modifiedOn.label=Last modified time
i18n_entity.SocNode.fields.modifiedReason.label=Reason for update
i18n_entity.SocNode.fields.modifiedTimestamp.label=Update time
i18n_entity.SocNode.fields.value.label=Value
i18n_entity.SocNode.fields.version.label=Revised version
i18n_entity.SocNode.group=Core
i18n_entity.SocNode.label=Monitoring nodes
i18n_entity.SocNode.listCard.modifiedTimestamp.prefix=Update time:
i18n_entity.SystemKeyEvent.fields.content.label=Content
i18n_entity.SystemKeyEvent.fields.createdBy.label=Created by
i18n_entity.SystemKeyEvent.fields.createdOn.label=Created time
i18n_entity.SystemKeyEvent.fields.group.label=Module
i18n_entity.SystemKeyEvent.fields.id.label=ID
i18n_entity.SystemKeyEvent.fields.level.inlineOptionBill.items.Error.label=Error
i18n_entity.SystemKeyEvent.fields.level.inlineOptionBill.items.Info.label=Ordinary
i18n_entity.SystemKeyEvent.fields.level.inlineOptionBill.items.Warning.label=Warning
i18n_entity.SystemKeyEvent.fields.level.label=Level
i18n_entity.SystemKeyEvent.fields.modifiedBy.label=Last modified by
i18n_entity.SystemKeyEvent.fields.modifiedOn.label=Last modified time
i18n_entity.SystemKeyEvent.fields.relatedUser.label=Related users
i18n_entity.SystemKeyEvent.fields.title.label=Title
i18n_entity.SystemKeyEvent.fields.version.label=Revised version
i18n_entity.SystemKeyEvent.group=Core
i18n_entity.SystemKeyEvent.label=System critical events
i18n_entity.UserNotice.fields.actionType.label=Action type
i18n_entity.UserNotice.fields.content.label=Text
i18n_entity.UserNotice.fields.createdBy.label=Created by
i18n_entity.UserNotice.fields.createdOn.label=Created time
i18n_entity.UserNotice.fields.entityId.label=Entity ID
i18n_entity.UserNotice.fields.entityName.label=Entity name
i18n_entity.UserNotice.fields.hasContent.label=There is text
i18n_entity.UserNotice.fields.id.label=ID
i18n_entity.UserNotice.fields.modifiedBy.label=Last modified by
i18n_entity.UserNotice.fields.modifiedOn.label=Last modified time
i18n_entity.UserNotice.fields.read.label=Read
i18n_entity.UserNotice.fields.readOn.label=Time read
i18n_entity.UserNotice.fields.title.label=Title
i18n_entity.UserNotice.fields.userId.label=User ID
i18n_entity.UserNotice.fields.version.label=Revised version
i18n_entity.UserNotice.group=Core
i18n_entity.UserNotice.label=User notification
i18n_entity.UserOpLog.fields.content.label=Operation content
i18n_entity.UserOpLog.fields.createdBy.label=Created by
i18n_entity.UserOpLog.fields.createdOn.label=Created time
i18n_entity.UserOpLog.fields.id.label=ID
i18n_entity.UserOpLog.fields.level.inlineOptionBill.items.Danger.label=Danger
i18n_entity.UserOpLog.fields.level.inlineOptionBill.items.Normal.label=Normal
i18n_entity.UserOpLog.fields.level.label=Level
i18n_entity.UserOpLog.fields.modifiedBy.label=Last modified by
i18n_entity.UserOpLog.fields.modifiedOn.label=Last modified time
i18n_entity.UserOpLog.fields.operator.label=User
i18n_entity.UserOpLog.fields.page.label=Page
i18n_entity.UserOpLog.fields.version.label=Revised version
i18n_entity.UserOpLog.group=Core
i18n_entity.UserOpLog.label=User operation log
i18n_entity.UserRole.fields.createdBy.label=Created by
i18n_entity.UserRole.fields.createdOn.label=Created time
i18n_entity.UserRole.fields.defaultRole.label=Default role
i18n_entity.UserRole.fields.id.label=ID
i18n_entity.UserRole.fields.modifiedBy.label=Last modified by
i18n_entity.UserRole.fields.modifiedOn.label=Last modified time
i18n_entity.UserRole.fields.name.label=Role name
i18n_entity.UserRole.fields.pItems.label=Permission list
i18n_entity.UserRole.fields.ro.label=Root tissue
i18n_entity.UserRole.fields.version.label=Version
i18n_entity.UserRole.group=User
i18n_entity.UserRole.label=User Role
i18n_entity.UserRole.listCard.createdBy.prefix=Created by
i18n_entity.UserRole.listCard.modifiedOn.prefix=Modification time
i18n_entity.WcsMrOrder.fields.actualRobotName.label=Execution robot
i18n_entity.WcsMrOrder.fields.cancelling.label=Canceling
i18n_entity.WcsMrOrder.fields.containerId.label=Container ID
i18n_entity.WcsMrOrder.fields.createdBy.label=Created by
i18n_entity.WcsMrOrder.fields.createdOn.label=Created time
i18n_entity.WcsMrOrder.fields.currentStepIndex.label=Current step
i18n_entity.WcsMrOrder.fields.dispatchCost.label=Allocation cost
i18n_entity.WcsMrOrder.fields.doneOn.label=Completion time
i18n_entity.WcsMrOrder.fields.doneStepIndex.label=Completed steps
i18n_entity.WcsMrOrder.fields.executing.label=In implementation
i18n_entity.WcsMrOrder.fields.expectedRobotGroups.label=Specify a robot group
i18n_entity.WcsMrOrder.fields.expectedRobotNames.label=Specify a robot
i18n_entity.WcsMrOrder.fields.fault.label=Failure
i18n_entity.WcsMrOrder.fields.id.label=ID
i18n_entity.WcsMrOrder.fields.keySites.label=Key Site
i18n_entity.WcsMrOrder.fields.kind.inlineOptionBill.items.Parking.label=Park
i18n_entity.WcsMrOrder.fields.kind.label=Type
i18n_entity.WcsMrOrder.fields.loadLocationSite.label=Load Site
i18n_entity.WcsMrOrder.fields.loaded.label=Picked up
i18n_entity.WcsMrOrder.fields.materialId.label=Material ID
i18n_entity.WcsMrOrder.fields.materialKind.label=Material type
i18n_entity.WcsMrOrder.fields.modifiedBy.label=Last modified by
i18n_entity.WcsMrOrder.fields.modifiedOn.label=Modification time
i18n_entity.WcsMrOrder.fields.priority.label=Priority
i18n_entity.WcsMrOrder.fields.reqId.label=Request order ID
i18n_entity.WcsMrOrder.fields.ro.label=RO
i18n_entity.WcsMrOrder.fields.robotAllocatedOn.label=Robot allocation time
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Allocated.label=Allocated
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Building.label=Building
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Cancelled.label=Cancelled
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Cancelling.label=Canceling
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Done.label=Done
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Executing.label=Executing
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Pending.label=Pending
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.ToBeAllocated.label=Unallocated
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Withdrawn.label=Withdrawn
i18n_entity.WcsMrOrder.fields.status.label=Status
i18n_entity.WcsMrOrder.fields.stepFixed.label=Steps to fix
i18n_entity.WcsMrOrder.fields.stepNum.label=Number of task steps
i18n_entity.WcsMrOrder.fields.taskBatch.label=Task lot
i18n_entity.WcsMrOrder.fields.unloadLocationSite.label=Unload site
i18n_entity.WcsMrOrder.fields.unloaded.label=Has dropped off
i18n_entity.WcsMrOrder.fields.version.label=Revised version
i18n_entity.WcsMrOrder.fields.withdrawn.label=Reassignment
i18n_entity.WcsMrOrder.group=WCS
i18n_entity.WcsMrOrder.label=Common transport Order
i18n_entity.WcsMrOrder.listCard.actualRobotName.prefix=Execution robot
i18n_entity.WcsMrOrder.listCard.createdOn.prefix=Create
i18n_entity.WcsMrOrder.listCard.kind.prefix=Type
i18n_entity.WcsMrOrder.listStats.items[0].label=Failed
i18n_entity.WcsMrOrder.listStats.items[1].label=Unallocated
i18n_entity.WcsMrOrder.listStats.items[2].label=Allocated
i18n_entity.WcsMrOrderStep.fields.createdBy.label=Created by
i18n_entity.WcsMrOrderStep.fields.createdOn.label=Created time
i18n_entity.WcsMrOrderStep.fields.endOn.label=End execution time
i18n_entity.WcsMrOrderStep.fields.forLoad.label=Load Site
i18n_entity.WcsMrOrderStep.fields.forUnload.label=Unload site
i18n_entity.WcsMrOrderStep.fields.id.label=ID
i18n_entity.WcsMrOrderStep.fields.locationSite.label=Action site
i18n_entity.WcsMrOrderStep.fields.modifiedBy.label=Last modified by
i18n_entity.WcsMrOrderStep.fields.modifiedOn.label=Modification time
i18n_entity.WcsMrOrderStep.fields.operation.label=Opertaion
i18n_entity.WcsMrOrderStep.fields.operationArgs.label=Action parameter
i18n_entity.WcsMrOrderStep.fields.orderId.label=Transport Order ID
i18n_entity.WcsMrOrderStep.fields.startOn.label=Start execution time
i18n_entity.WcsMrOrderStep.fields.status.label=Status
i18n_entity.WcsMrOrderStep.fields.stepIndex.label=Which step?
i18n_entity.WcsMrOrderStep.fields.version.label=Revised version
i18n_entity.WcsMrOrderStep.group=WCS
i18n_entity.WcsMrOrderStep.label=Common transport order steps
i18n_entity.WcsMrOrderStep.listCard.locationSite.prefix=Site
i18n_entity.WcsMrOrderStep.listCard.operation.prefix=Opertaion
i18n_entity.WcsMrOrderStep.listCard.stepIndex.prefix=No
i18n_entity.WcsMrOrderStep.listCard.stepIndex.suffix=Step
i18n_entity.WcsRobotTaskLog.fields.args.label=Parameter
i18n_entity.WcsRobotTaskLog.fields.category.label=Category
i18n_entity.WcsRobotTaskLog.fields.code.label=Code
i18n_entity.WcsRobotTaskLog.fields.createdBy.label=Created by
i18n_entity.WcsRobotTaskLog.fields.createdOn.label=Created time
i18n_entity.WcsRobotTaskLog.fields.id.label=ID
i18n_entity.WcsRobotTaskLog.fields.level.label=Level
i18n_entity.WcsRobotTaskLog.fields.modifiedBy.label=Last modified by
i18n_entity.WcsRobotTaskLog.fields.modifiedOn.label=Last Modified Time
i18n_entity.WcsRobotTaskLog.fields.robotName.label=Robot
i18n_entity.WcsRobotTaskLog.fields.taskId.label=Task ID.
i18n_entity.WcsRobotTaskLog.fields.tcId.label=Tracking ID.
i18n_entity.WcsRobotTaskLog.fields.version.label=Revised version
i18n_entity.WcsRobotTaskLog.group=WCS
i18n_entity.WcsRobotTaskLog.label=Robot task log
i18n_entity.WcsRobotTaskLog.listCard.category.prefix=Category
i18n_entity.WcsRobotTaskLog.listCard.code.prefix=Code
i18n_entity.WcsRobotTaskLog.listCard.level.prefix=Level
i18n_entity.WcsRobotTaskLog.listCard.taskId.prefix=Task ID