
[base]
bgTaskAlarmLabel=Background tasks
bgTaskErrorMsg=Background task [{0}] failure: {1}
errEmptyStatsSubject=Statistical subjects cannot be empty
errFalconLabelEmpty=Task name cannot be empty
errFalconLabelRepeat=Duplicate task name: "{0}"
errFleetNoSuchSceneById=No such scene: {0}
errIllegalFile=Illegal documents
errNoRdsScene=Missing scheduling scene
errRbkReqResBadCode=Request for robot failed. Error code: {0}. Error message: {1}
pwdRequirementModerate=Include uppercase and lowercase English, numbers, at least 8 digits
pwdRequirementStrong=Include uppercase English, lowercase English, numbers, special characters, at least 12 digits
pwdRequirementWeak="At least one person"
reason=Reason
system=System

[base.error]
errAdminRequired=Administrator privileges required
errAggregateQuery=Aggregate query exception: {0}
errBadColumnName=Invalid column name "{0}"
errBadHttpMethod=Unsupported Http method: {0}
errBadIP=IP format is incorrect: "{0}"
errBadReqBodyJson=JSON format error: {0}
errBadRequestTimepointOrTimestamp=Cannot use timestamp and timeline for querying at the same time.
errBinBound=Storage bin is bound to another container, storage bin {0}, container {1}
errBinHasContainerNotToLoad=Container {1} already exists on storage bin {0}, container {2} cannot be placed again
errBinNoContainer=Requires a container on storage bin {0}
errBinNoRobot=Storage bin "{0}" has not configured robot position parameters
errBinNotEmpty=Storage bin {0} must be empty and unoccupied
errBpNoChild=Component not configured child component "{0}"
errBpRunError=Component "{1}" running error: {0}
errBreakLoop=Termination cycle
errBzError=Business exception: {0}
errBzMaterialCategoryNoStoreDistricts=Material category "{0}" Unconfigured storage area
errBzMaterialNoCategory=Item '{0}' has no category configured
errBzMissingKeyParam=Missing required parameter: "{0}"
errBzNoActiveBomForMat=Cannot find material "{0}" Activated Bill of Material
errBzNoEnoughContainerForMaterial=Not enough empty boxes for material '{0}'
errBzNoEnoughEmptyContainer=Insufficient empty containers (expected = {0}, only = {1})
errBzNoMaterialById=Material not found "{0}"
errBzNoMaterialCategoryById=Cannot find material category "{0}"
errBzNoMaterialContainerMaxQty=Material '{0}' has no container capacity configured
errBzNoMaterialContainerMaxQty2=Material '{0}' is not Configured with a'valid 'container capacity
errBzNoSuchOrderNameId=Cannot find documentation "{0 }" / "{ 1}"
errBzOutboundLineShort=Row {0}, material "{2}", inventory is missing {1}
errCannotCreateNewBuiltinTaskDef=Cannot create a new built-in Falcon mission
errCannotExceedStatisticTimeSpan=The statistical time span cannot exceed {0} days
errCannotExecuteNonDeviceOperation=Unable to perform non-institutional controlled operations
errCannotModifyBuiltinFromFalseToTrue=Cannot change normal Falcon mission to built-in Falcon mission, mission name = {0}
errCannotModifyBuiltinLabel=Cannot modify the task name of the built-in Falcon task, old value = {0}, new value = {1}
errCannotRemoveBuiltinTaskDef=Cannot delete built-in task template: {0}
errCodeErr=Program error: {0}
errColumnEndMustPosInt=The ending column number must be a positive integer.
errColumnStartEQColumnEnd=When columns are disabled, the starting column number must be equal to the ending column number.
errColumnStartGtColumnEnd=The starting column number cannot be greater than the ending column number
errColumnStartMustPosInt=The starting column number must be a positive integer.
errComplexQueryBadOp=Unsupported operator '{0}'
errComplexQueryInMultiple=Query requires specifying multiple values
errComplexQueryMissingField1=The query needs to specify "field 1".
errComplexQueryMissingOp=Query requires operator
errComplexQueryNotSupportField=Fields {1} | {2} of type {0} do not support the {3} operator
errComplexQueryUnknownType=Unsupported query type "{0}"
errComplexQueryValueNeedTwo=Field '{0}' requires two query values
errComplexQueryValueNotString=Field "{0}" query value is not string
errComplexQueryValueNull=Field '{0}' query value is empty
errContainerBadCurrentBin=Container {0} is currently in storage bin {1}, not in {2}.
errContainerBound=Container is bound to another storage bin, container {0}, storage bin {1}
errContainerLocked=Container '{0}' may be in use, please try another container (lock = true)
errContainerNoBin=Container "{0}" current storage bin is empty
errContainerNoBinMissingFromBin=The current storage bin of container {0} cannot be queried. You must specify a storage bin as the starting point
errContainerNoType=Container '{0}' has no type set
errContainerNotFound=Container not found, container ID = {0}
errContainerOrFromBinSpecified=Specify at least a container or starting storage bin
errContainerTypeNoStoreDistricts=Storage area not Configured for container type '{0}'
errDbProcessing=Processing database, please try again later
errDbRestoreNoInfo=Incorrect data file (missing info.txt)
errDepthEndMustPosInt=The ending depth number must be a positive integer.
errDepthStartEQDepthEnd=When depth is disabled, the starting depth number must be equal to the ending depth number.
errDepthStartGtDepthEnd=The starting depth cannot be greater than the ending depth
errDepthStartMustPosInt=The starting depth number must be a positive integer.
errDingBadConfig=DingTalk login configuration is incomplete
errDingCallErr=DingTalk request failed with code = {0}
errDingNotEnabled=DingTalk is not enabled
errDingShuNoCode=Callback no code
errDirFilesNull=Failed to list files under the directory
errDirNotExists=Directory does not exist: {0}
errDirectOutboundSubmitFail=Direct export commit failed. Reason: {0}
errDirectorOutboundBinNotOccupied=Storage bin {0} is empty. (Direct outbound)
errDirectorOutboundEmptyLayouts=The inventory details that can be shipped out are empty. (Direct shipment)
errDirectorOutboundNoOrder=Direct outbound without configured documentation
errDistrictIdsEmpty=At least one storage area must be specified
errDuplicateManilaFilterCaseName=Duplicate statistical scheme name: {0}
errDuplicatedKeyError=The "{1}" field value of "{0}" cannot be duplicated, duplicate value = {2}
errEmptyEntityValue=Business Object "{0}" is null
errEmptyPassword=Password cannot be empty
errEntityNoField=Business Object "{0}" has no field "{1}"
errEntityRequestMissingIdOrQueryParam=Need to specify "id" or "query" parameter
errEntityRequestMissingQueryParam=Query requires specifying the "query" parameter
errExceedingMaxDecimalLength=The field value {1} of Business Object {0} exceeds the maximum length allowed for the decimal part {2}.
errExceedingMaxIntegerLength=The field value {1} of Business Object {0} exceeds the maximum length allowed for the integer part {2}.
errExceedingMaxValue=Field value {1} for Business Object {0} exceeds maximum allowed value {2}
errExceedingMinValue=Field value {1} for Business Object {0} exceeds the allowed minimum value {2}
errFSNotEnabled=Feishu not enabled
errFalconBlockInputParamNotList=Component "{0}" input parameter "{1}" needs to be an array
errFalconBlockInputParamRangeError=Component '{0}' input parameter '{1}' range error
errFalconBlockOptionParamError=Component '{0}' option parameter '{1}' error
errFalconCreateTaskNoDefId=Unable to create task, no template specified
errFalconExpressionError=Expression evaluation error. Expression '{0}'. Details: {1}
errFalconMissingBlockInputParam=Component '{0}' is missing input parameter '{1}'
errFalconRecordRemoveRunning=There are running tasks that cannot be deleted. Falcon task number: {0}
errFalconThrowPrefix=Error: {0}
errFeiShuBadConfig=Feishu login configuration is incomplete
errFeiShuCallErr=Feishu request failed, code = {0}
errFeiShuNoCode=Callback no code
errFieldTextTooLong=Field '{0}. {1}' content length {2} exceeds maximum limit {3}
errFieldTypeMismatchWithSQLDataType=Field type {2} for Business Object {0} field value {1} does not match SQL data type {3}
errFileNotDirectory=File is not a directory: {0}
errFileNotExists=File does not exist: {0}
errFileNotInDir=File '{0}' is not in directory '{1}'
errGwNoRobot=Robot '{0}' not configured in gateway
errHttpFail=HTTP request failed, HTTP response code = {0}, body = {1}
errHttpResponseBodyEmpty=HTTP request response is empty
errIllegalPeriodTime=Illegal time parameter, period type {0} time value {1}
errIndexFieldDuplicated=Indexed field "{0}" is duplicated
errIndexNameDuplicated=The name of the index "{0}" is repeated
errInitializeScheduler=Scheduled task initialization failed! "{0}"
errInterruptedException=Execution was interrupted
errInvLayoutNoContainer=No container {0} on inventory detail
errInvShort=Out of stock: {0}
errInvShort2=Insufficient inventory, material {0}, pending shipment = {1}, inventory = {2}
errLayerEndMustPosInt=The ending layer number must be a positive integer.
errLayerStartEQLayerEnd=When layers are disabled, the starting layer number must be equal to the ending layer number.
errLayerStartGtLayerEnd=The starting layer number cannot be greater than the ending layer number
errLayerStartMustPosInt=The starting layer number must be a positive integer.
errLockBinFailed=Failed to lock storage bin "{0}", previously locked
errMissingHttpPathParam=Missing path parameter: {0}
errMissingHttpQueryParam=Missing HTTP query parameter: {0}
errMissingIdField=Business Object {1} ({0}) lacks id field
errMissingParam=Missing parameter: {0}
errModifyProhibition={0}
errModifyProhibitionDefaultMsg=The current selected data is not allowed to be modified.
errMrNoCurrentOrder=Machine "{0}" currently has no waybill
errMrNoCurrentStep=Robot "{0}" currently has no waybill steps being executed
errMrNoOrder=Waybill does not exist or has been executed "{0}"
errMrUpdateOrderBadStatus=The waybill is no longer allowed to be updated. waybill = "{0}", current status = {1}
errMySqlQueryListNotSupport=MySQL does not currently support querying multi-valued fields {0} | {1}.
errNoBin=No storage bin "{0}" found
errNoBinById=Cannot find storage bin "{0}"
errNoBinRobotArgs=Missing action parameters, storage bin = "{0}", action = "{1}"
errNoBpType=Component "{0}" not found
errNoConfig=Configuration not specified
errNoDeviceHost=Device, address/IP not configured
errNoDeviceType=Device type not configured
errNoDistrictById=Cannot find storage area "{0}"
errNoEmptyBinInDistrict=There are no empty storage bins
errNoFalconGlobalVariable=Global control variable {0} not found
errNoFalconTaskDefById=Task template not found, template id = {0}
errNoJsonNode=There is no JsonNode.
errNoLockById=The storage bin is not locked "{0}"
errNoMongoDB=There is no available MongoDB.
errNoOrderLine=No single line, single number = {0}
errNoRoutePath=No reachable path found, from {0} to {1}
errNoScriptFunctionProvided=No script function specified
errNoSqlDb=SQL data source is not available
errNoSuchContainerById=Container "{0}" not found
errNoSuchContainerTypeById=Could not find container type '{0}'
errNoSuchDistrictById=Cannot find storage area "{0}"
errNoSuchEntity=Business Object "{0}" not found
errNoSuchScriptFunction=Script function "{0}" not found
errNoSuchUserById=Unable to find user. id = "{0}"
errNoTaskDefByLabel=Task template not found, template name = {0}
errNoUiDir=Cannot find interface file directory: {0}
errNoUpOrderConfig=Unable to find upstream documentation configuration. Upstream documentation = {1}, downstream documentation = {0}
errNoUploadedFile=The uploaded file should be placed in the form field: "{0}"
errNoUserUsername=Unable to find user. Username = "{0}"
errNoWidthTooSmall=Number width is too small
errNonDecimal=Field value {1} for Business Object {0} is not a decimal
errNonInteger=Field value {1} for Business Object {0} is not an integer
errNumOverflow=Value {0} exceeds numeric type range {1}~ {2}
errOrderNoPushConfig=Documentation not found Pushdown configuration: {0}
errParseJsonFile=Failed to parse JSON file
errParseJsonString=Failed to parse JSON string with value: {0}
errPasswordNotMatch=Password error
errPutOnContainerMissingContainerBin=Container put away, container must be specified
errPwdErrExceedLimit=Password error exceeds the limit, please contact the administrator to reset or try again after {0} seconds
errPwdExpired=The password has expired, please contact the administrator to reset it.
errPwdLengthExceedLimit=The password length does not meet the requirements, requiring {0} to {1} bits
errPwdStrengthNotAllow=The password strength does not meet the requirements. The current password strength requirement is: {0}, and the requirement is {1}.
errRecoverBadExternalCallAsync=Only canceled or terminated asynchronous calls can be retried
errRefFieldNoRefEntity=Field '{0}' not configured 'references Business Object
errRemoveProhibition={0}
errRemoveProhibitionDefaultMsg=The current selected data is not allowed to be deleted.
errRetryFailedRobotButOrderNotExecuting=Restart the faulty robot, but the current waybill {0} status is not in progress, but {1}.
errRobotNotFailed=Robot "{0}" is faulty
errRowEndMustPosInt=The ending row number must be a positive integer.
errRowStartEQRowEnd=When rows are disabled, the starting row number must be equal to the ending row number.
errRowStartGtRowEnd=The starting queue number cannot be greater than the ending queue number.
errRowStartMustPosInt=The starting row number must be a positive integer.
errScriptBadReturnNull=Script function {0} return value exception, cannot be null
errScriptBuildError=Failed to build script source code
errScriptCloseContextError=Close context error
errScriptDisposeError=Destroy script system error
errScriptEntityRw=Script Business Object read and write error: {0}
errScriptExt=Script error: {0}
errScriptReturnNotString=Script function "{0}" returns value not string: {1}
errScriptStartError=Script startup failed
errSetToStatementInvalidJavaClass=setToStatement unsupported parameter type '{0}'
errSignInNoPassword=Password required
errSimpleScriptBp=Failed to execute simple script: {0}
errSingletonEntityBadOp=Singleton Business Object "{0}" cannot call "{1}"
errSqlEmptyWhereNotAllowed=Prohibit all updates
errSqlUniqueConstraintViolation=Values are duplicated. value = {2}, table = {0}, index = {1}.
errStartedOnLtFinishedOn=Start time must be earlier than end time
errStepCannotBeEmpty=Step cannot be empty
errSyncKeyMultiple=Multivalued field '{0}' cannot be used as business primary key
errSyncKeyNotUniqInCurrent=In the original Business Object list, the key is not unique: "{0}"
errSyncKeyNotUniqInNew=In the new Business Object list, the key is not unique: "{0}"
errTakeOffContainerBadBin=Required to remove container '{0}' from storage bin '{1}', but the system records that the container is currently in storage bin '{2}'
errTakeOffContainerBadContainer=To unshelf container '{1}' from storage bin '{0}', but the system records that the container on storage bin is {2}
errTakeOffContainerContainerBinAtLeastOne=Depot container off shelf, specify at least one container or storage bin
errTakeOffContainerNoBin=Required to remove container '{0}' from storage bin '{1}', but the system record container is not currently on any storage bin
errTooLongNum=The value {1} of field {0} is too large (too many digits), the number of digits cannot exceed {2} bits, and the integer part cannot exceed {3} bits
errTransportOrderFailed=Failed to execute dispatch transportOrder, reason for failure: {0}
errTypesNotEnoughForHeaders=Insufficient number of types
errUnbindBinContainerBadBind=Tried to unbind storage bin "{0}" and container "{1}", but the current container on storage bin is "{2}"
errUnsupportedDBType=Database type not supported: {0}
errUnsupportedDataType=Data type not supported: {0}
errUnsupportedHttpRequestMethod=HTTP method error "{0}"
errUserDisabled=Account has been disabled
errWsNotConnected=Not connected [Websocket {0}, status = {1}]
errWsSendFail=Send failed [Websocket {0}]
errWsSendTimeout=Send Timeout [Websocket {0}]
errWwxBadConfig=WeCom login configuration is incomplete
errWwxCallErr=WeCom request failed with code = {0}
errWwxNoCode=Callback no code
errWwxNotEnabled=WeCom is not enabled
errXlsFormulaNotSupported=Formulas in cells are not supported.

[base.falcon]
Falcon_BlockGroup_Basic=Basic components
Falcon_BlockGroup_Bin=Storage bin
Falcon_BlockGroup_BinContainer=Container storage bin
Falcon_BlockGroup_ConditionAndLoop=Conditions and cycles
Falcon_BlockGroup_Container=Container
Falcon_BlockGroup_ContainerTransport=Container handling list
Falcon_BlockGroup_Cq=ComplexQuery
Falcon_BlockGroup_CustomBlocks=Custom components
Falcon_BlockGroup_Entity=Business Objects
Falcon_BlockGroup_FalconRecord=Falcon mission
Falcon_BlockGroup_Inv=Stock
Falcon_Bp_AbortTaskBp_Input_msg_description=Termination of Falcon Mission Reason
Falcon_Bp_AbortTaskBp_Input_msg_label=Error message
Falcon_Bp_AbortTaskBp_description=Terminate the Falcon mission
Falcon_Bp_AbortTaskBp_label=Terminate the task
Falcon_Bp_BindBinContainerBp_Input_binId_description=Storage bin ID
Falcon_Bp_BindBinContainerBp_Input_binId_label=Storage bin number
Falcon_Bp_BindBinContainerBp_Input_checkBinStatus_description=If other containers are bound to the storage bin, an error will be reported
Falcon_Bp_BindBinContainerBp_Input_checkBinStatus_label=Check storage bin status
Falcon_Bp_BindBinContainerBp_Input_checkContainerStatus_description=If the container is bound to another storage bin, an error will be reported
Falcon_Bp_BindBinContainerBp_Input_checkContainerStatus_label=Check container status
Falcon_Bp_BindBinContainerBp_Input_containerId_description=Container number
Falcon_Bp_BindBinContainerBp_Input_containerId_label=Container number
Falcon_Bp_BindBinContainerBp_Input_unlockBin_description=Need to unlock storage bin synchronously
Falcon_Bp_BindBinContainerBp_Input_unlockBin_label=Unlock storage bin
Falcon_Bp_BindBinContainerBp_Input_unlockContainer_description=Need to unlock container synchronously
Falcon_Bp_BindBinContainerBp_Input_unlockContainer_label=Unlock the container
Falcon_Bp_BindBinContainerBp_description=Bind storage bin to container
Falcon_Bp_BindBinContainerBp_label=Bind storage bin container
Falcon_Bp_BreakBp_Input_condition_description=Exit the loop when the condition is met
Falcon_Bp_BreakBp_Input_condition_label=Conditions
Falcon_Bp_BreakBp_description=If the condition is met, an exception will be thrown to exit the loop
Falcon_Bp_BreakBp_label=Stop the loop Break
Falcon_Bp_CreateInvFromOrderBp_Input_bin_description=Storage bin
Falcon_Bp_CreateInvFromOrderBp_Input_bin_label=Storage bin
Falcon_Bp_CreateInvFromOrderBp_Input_container_description=Container
Falcon_Bp_CreateInvFromOrderBp_Input_container_label=Container
Falcon_Bp_CreateInvFromOrderBp_Input_copyFields_description=Multiple fields are separated by "," and these fields and corresponding data will be copied to the inventory information.
Falcon_Bp_CreateInvFromOrderBp_Input_copyFields_label=Copy field
Falcon_Bp_CreateInvFromOrderBp_Input_lotNoFormat_label=Storage bin numbering format
Falcon_Bp_CreateInvFromOrderBp_Input_materialFieldName_description=Please enter the field name that represents the material on the documentation
Falcon_Bp_CreateInvFromOrderBp_Input_materialFieldName_label=Material field
Falcon_Bp_CreateInvFromOrderBp_Input_order_description=Please enter a documentation object
Falcon_Bp_CreateInvFromOrderBp_Input_order_label=Documentation
Falcon_Bp_CreateInvFromOrderBp_Input_qtyFieldName_description=Please enter the field name indicating the quantity on the documentation
Falcon_Bp_CreateInvFromOrderBp_Input_qtyFieldName_label=Quantity field
Falcon_Bp_CreateInvFromOrderBp_Input_state_description=There are three default storage states: Received: received; Storing: in storage; Assigned: allocated.
Falcon_Bp_CreateInvFromOrderBp_Input_state_label=Stock status
Falcon_Bp_CreateInvFromOrderBp_description=Create inventory information from documentation
Falcon_Bp_CreateInvFromOrderBp_label=Create inventory from documentation
Falcon_Bp_CreateInvFromOrderLinesBp_Input_bin_description=Storage bin
Falcon_Bp_CreateInvFromOrderLinesBp_Input_bin_label=Storage bin
Falcon_Bp_CreateInvFromOrderLinesBp_Input_container_description=Container
Falcon_Bp_CreateInvFromOrderLinesBp_Input_container_label=Container
Falcon_Bp_CreateInvFromOrderLinesBp_Input_copyFields_description=Multiple fields are separated by "," and these fields and corresponding data will be copied to the inventory information.
Falcon_Bp_CreateInvFromOrderLinesBp_Input_copyFields_label=Copy the following fields from a single line
Falcon_Bp_CreateInvFromOrderLinesBp_Input_materialFieldName_description=Please enter the field name that represents the material on a single line.
Falcon_Bp_CreateInvFromOrderLinesBp_Input_materialFieldName_label=Material field name on a single line
Falcon_Bp_CreateInvFromOrderLinesBp_Input_order_description=Please enter a documentation object
Falcon_Bp_CreateInvFromOrderLinesBp_Input_order_label=Documentation object
Falcon_Bp_CreateInvFromOrderLinesBp_Input_qtyFieldName_description=Please enter the field name that represents the quantity on a single line.
Falcon_Bp_CreateInvFromOrderLinesBp_Input_qtyFieldName_label=Quantity field name on a single line
Falcon_Bp_CreateInvFromOrderLinesBp_Input_state_description=There are three default storage states: Received: received; Storing: in storage; Assigned: allocated.
Falcon_Bp_CreateInvFromOrderLinesBp_Input_state_label=Storage state
Falcon_Bp_CreateInvFromOrderLinesBp_description=Create inventory information based on single-line information
Falcon_Bp_CreateInvFromOrderLinesBp_label=Creating an inventory from a documentation line
Falcon_Bp_CreateTraceContainerBp_Input_containerType_description=Please enter the container type recorded by M4.
Falcon_Bp_CreateTraceContainerBp_Input_containerType_label=Container type
Falcon_Bp_CreateTraceContainerBp_Output_containerId_description=New container number.
Falcon_Bp_CreateTraceContainerBp_Output_containerId_label=Container number
Falcon_Bp_CreateTraceContainerBp_description=Create a tracking container.
Falcon_Bp_CreateTraceContainerBp_label=Create a tracking container
Falcon_Bp_DelayBp_Input_timeMillis_description=Delay time (milliseconds)
Falcon_Bp_DelayBp_Input_timeMillis_label=Time (milliseconds)
Falcon_Bp_DelayBp_description=Used to delay the execution of subsequent modules, the delay depends on the input parameters
Falcon_Bp_DelayBp_label=Delay
Falcon_Bp_ExpressionBp_Input_expression_description=Expression
Falcon_Bp_ExpressionBp_Input_expression_label=Expression
Falcon_Bp_ExpressionBp_Output_expResult_description=Result of the expression
Falcon_Bp_ExpressionBp_Output_expResult_label=Expression result
Falcon_Bp_ExpressionBp_description=Calculate the result based on the input expression
Falcon_Bp_ExpressionBp_label=Evaluation of expressions
Falcon_Bp_FindEmptyContainerBp_Input_districtIds_description=Storage area number. Please enter the storage area number recorded by M4 according to your requirements.
Falcon_Bp_FindEmptyContainerBp_Input_districtIds_label=Storage area Id
Falcon_Bp_FindEmptyContainerBp_Input_keepTrying_description=Disable: (default) only searches once; Enable: searches continuously until an empty container is found.
Falcon_Bp_FindEmptyContainerBp_Input_keepTrying_label=Retry until successful
Falcon_Bp_FindEmptyContainerBp_Input_sort_description=Sorting method for storage bins in the target library area.
Falcon_Bp_FindEmptyContainerBp_Input_sort_label=Sorting
Falcon_Bp_FindEmptyContainerBp_Output_binId_description=The number of the storage bin where the target empty container is stored.
Falcon_Bp_FindEmptyContainerBp_Output_binId_label=Storage bin number
Falcon_Bp_FindEmptyContainerBp_Output_containerId_description=The number of the empty container found.
Falcon_Bp_FindEmptyContainerBp_Output_containerId_label=Container number
Falcon_Bp_FindEmptyContainerBp_Output_found_description=True: found; false: not found.
Falcon_Bp_FindEmptyContainerBp_Output_found_label=Did you find the empty container?
Falcon_Bp_FindEmptyContainerBp_description=According to the desired rules, find empty containers from the target storage area and lock this container.
Falcon_Bp_FindEmptyContainerBp_label=Find empty container
Falcon_Bp_FindFalconRecordFieldBp_Input_fieldName_label=Read field
Falcon_Bp_FindFalconRecordFieldBp_Output_fieldValue_label=Read value
Falcon_Bp_FindFalconRecordFieldBp_label=Read task record field
Falcon_Bp_FindFieldValueByIdBp_Input_entityName_description=Business Object Name
Falcon_Bp_FindFieldValueByIdBp_Input_entityName_label=Business Object Name
Falcon_Bp_FindFieldValueByIdBp_Input_field_description=Specify the fields of the query
Falcon_Bp_FindFieldValueByIdBp_Input_field_label=Field
Falcon_Bp_FindFieldValueByIdBp_Input_id_description=Business Object entity id
Falcon_Bp_FindFieldValueByIdBp_Input_id_label=Business Object id
Falcon_Bp_FindFieldValueByIdBp_Output_found_description=Does the Business Object entity exist?
Falcon_Bp_FindFieldValueByIdBp_Output_found_label=Business Object Existence
Falcon_Bp_FindFieldValueByIdBp_Output_value_description=Query field value
Falcon_Bp_FindFieldValueByIdBp_Output_value_label=Value
Falcon_Bp_FindFieldValueByIdBp_description=Find the specified field of the Business Object entity by id
Falcon_Bp_FindFieldValueByIdBp_label=Find fields of Business Object by id
Falcon_Bp_FindNotOccupiedBinBp_Input_cq_description=Conditions for finding empty storage bins
Falcon_Bp_FindNotOccupiedBinBp_Input_cq_label=Conditions
Falcon_Bp_FindNotOccupiedBinBp_Input_districtIds_description=List of storage areas
Falcon_Bp_FindNotOccupiedBinBp_Input_districtIds_label=List of storage areas
Falcon_Bp_FindNotOccupiedBinBp_Input_keepTrying_description=When it is true, repeat the search for empty storage bin until successful. If it is false, only search once
Falcon_Bp_FindNotOccupiedBinBp_Input_keepTrying_label=Repeated attempts lead to success
Falcon_Bp_FindNotOccupiedBinBp_Input_sort_description=Sorting rules
Falcon_Bp_FindNotOccupiedBinBp_Input_sort_label=Sorting
Falcon_Bp_FindNotOccupiedBinBp_Output_binId_description=ID of the storage bin found
Falcon_Bp_FindNotOccupiedBinBp_Output_binId_label=Storage bin ID
Falcon_Bp_FindNotOccupiedBinBp_Output_found_description=True means found, false means not found
Falcon_Bp_FindNotOccupiedBinBp_Output_found_label=Whether to find
Falcon_Bp_FindNotOccupiedBinBp_description=Look for empty storage bins in storage areas
Falcon_Bp_FindNotOccupiedBinBp_label=Empty search storage bin
Falcon_Bp_FindOneBp_Input_entityName_label=Entity
Falcon_Bp_FindOneBp_Input_field_label=Field
Falcon_Bp_FindOneBp_Input_value_label=Value
Falcon_Bp_FindOneBp_label=Check a single record
Falcon_Bp_FindOneEntityByIdBp_Input_entityName_description=Business Object Name
Falcon_Bp_FindOneEntityByIdBp_Input_entityName_label=Business Object Name
Falcon_Bp_FindOneEntityByIdBp_Input_id_description=Business Object entity id
Falcon_Bp_FindOneEntityByIdBp_Input_id_label=Business Object id
Falcon_Bp_FindOneEntityByIdBp_Output_ev_description=Business Object Entity Details
Falcon_Bp_FindOneEntityByIdBp_Output_ev_label=Business Objects
Falcon_Bp_FindOneEntityByIdBp_Output_found_description=Whether to find
Falcon_Bp_FindOneEntityByIdBp_Output_found_label=Whether to find
Falcon_Bp_FindOneEntityByIdBp_description=Find Business Object Entities by ID
Falcon_Bp_FindOneEntityByIdBp_label=Find Business Objects by id
Falcon_Bp_GetBinContainerBp_Input_binId_description=Storage bin ID
Falcon_Bp_GetBinContainerBp_Input_binId_label=Storage bin number
Falcon_Bp_GetBinContainerBp_Output_binEmpty_description=Whether the container is empty, true is empty
Falcon_Bp_GetBinContainerBp_Output_binEmpty_label=Is the storage bin empty?
Falcon_Bp_GetBinContainerBp_Output_containerId_description=Container number
Falcon_Bp_GetBinContainerBp_Output_containerId_label=Container number
Falcon_Bp_GetBinContainerBp_description=Obtain the container on the storage bin, provided that the storage bin was previously bound to the container. If it cannot be obtained, the container number will return null.
Falcon_Bp_GetBinContainerBp_label=Get the container on the storage bin
Falcon_Bp_GetContainerBinBp_Input_containerId_description=Container number
Falcon_Bp_GetContainerBinBp_Input_containerId_label=Container number
Falcon_Bp_GetContainerBinBp_Output_binId_description=Storage bin number
Falcon_Bp_GetContainerBinBp_Output_binId_label=Storage bin number
Falcon_Bp_GetContainerBinBp_Output_found_description=Found or not, true means found
Falcon_Bp_GetContainerBinBp_Output_found_label=Whether to find
Falcon_Bp_GetContainerBinBp_description=Obtain the storage bin where the container is located. The premise of obtaining is that the storage bin was previously bound to the container. If it cannot be obtained, the storage bin ID returns null.
Falcon_Bp_GetContainerBinBp_label=Get the storage bin where the container is located
Falcon_Bp_GetContainerInvBp_Input_containerId_description=Please enter the container number recorded by M4
Falcon_Bp_GetContainerInvBp_Input_containerId_label=Container number
Falcon_Bp_GetContainerInvBp_Output_found_description=True: found; false: not found.
Falcon_Bp_GetContainerInvBp_Output_found_label=Whether to find
Falcon_Bp_GetContainerInvBp_Output_inv_description=Inventory list.
Falcon_Bp_GetContainerInvBp_Output_inv_label=Inventory list
Falcon_Bp_GetContainerInvBp_description=Obtain the corresponding inventory details based on the container number.
Falcon_Bp_GetContainerInvBp_label=Obtain inventory details in the container
Falcon_Bp_GetContainerTypeStoreDistrictsBp_Input_containerId_description=Please enter the number of the container recorded by M4.
Falcon_Bp_GetContainerTypeStoreDistrictsBp_Input_containerId_label=Container number
Falcon_Bp_GetContainerTypeStoreDistrictsBp_Output_storeDistricts_description=A collection of storage area names, an array.
Falcon_Bp_GetContainerTypeStoreDistrictsBp_Output_storeDistricts_label=Storage area
Falcon_Bp_GetContainerTypeStoreDistrictsBp_description=According to the container number, query the name of the storage area where such containers can be stored.
Falcon_Bp_GetContainerTypeStoreDistrictsBp_label=Storage area for querying the type of container
Falcon_Bp_IfBp_Input_condition_description=Judgment condition
Falcon_Bp_IfBp_Input_condition_label=Conditions
Falcon_Bp_IfBp_description=Conditional judgment can only continue to execute submodules when the conditions are met
Falcon_Bp_IfBp_label=If IF
Falcon_Bp_IfElseBp_Input_condition_description=Judgment condition
Falcon_Bp_IfElseBp_Input_condition_label=Conditions
Falcon_Bp_IfElseBp_description=Condition judgment, go to the submodule of true when the condition is met, go to the submodule of false when the condition is not met
Falcon_Bp_IfElseBp_label=If otherwise IF ELSE
Falcon_Bp_IterateListBp_Context_index_description=Index
Falcon_Bp_IterateListBp_Context_item_description=Single data message
Falcon_Bp_IterateListBp_Input_list_description=Traversing list
Falcon_Bp_IterateListBp_Input_list_label=List
Falcon_Bp_IterateListBp_Input_parallel_description=Determine whether to traverse in parallel
Falcon_Bp_IterateListBp_Input_parallel_label=Parallel
Falcon_Bp_IterateListBp_description=Traverse arrays, support parallelism
Falcon_Bp_IterateListBp_label=Traverse an array
Falcon_Bp_KeepTryingLockBinBp_Input_binId_description=Storage bin ID
Falcon_Bp_KeepTryingLockBinBp_Input_binId_label=Storage bin
Falcon_Bp_KeepTryingLockBinBp_Input_reason_description=Reason for locking
Falcon_Bp_KeepTryingLockBinBp_Input_reason_label=Reason
Falcon_Bp_KeepTryingLockBinBp_description=Repeatedly try to lock the storage bin until successful
Falcon_Bp_KeepTryingLockBinBp_label=Repeatedly try to lock the storage bin until successful
Falcon_Bp_KeepTryingLockNotOccupiedBinBp_Input_binId_description=Storage bin ID
Falcon_Bp_KeepTryingLockNotOccupiedBinBp_Input_binId_label=Storage bin number
Falcon_Bp_KeepTryingLockNotOccupiedBinBp_description=Wait for the storage bin to be empty and locked
Falcon_Bp_KeepTryingLockNotOccupiedBinBp_label=Wait for the storage bin to be empty and locked
Falcon_Bp_LockBinOnceBp_Input_binId_label=Storage bin number
Falcon_Bp_LockBinOnceBp_Input_notFoundToAborted_description=Whether the failure requires the task to be terminated, true is yes
Falcon_Bp_LockBinOnceBp_Input_notFoundToAborted_label=Unable to find termination task
Falcon_Bp_LockBinOnceBp_Input_notFoundToFault_description=Does the failure require the task to malfunction? True means yes.
Falcon_Bp_LockBinOnceBp_Input_notFoundToFault_label=Can't find let the task malfunction
Falcon_Bp_LockBinOnceBp_Output_ok_description=Whether the lock is successful, success is true, failure is false
Falcon_Bp_LockBinOnceBp_Output_ok_label=Whether the lock was successful
Falcon_Bp_LockBinOnceBp_description=Attempt to lock an unlocked storage bin once, return true if successful, otherwise return false.
Falcon_Bp_LockBinOnceBp_label=Lock unlocked storage bins
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_newState_Option_Cancelled=Cancel
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_newState_Option_Done=Complete
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_newState_Option_Failed=Failure
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_newState_description=Expected updated state
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_newState_label=Updated status
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_orderId_description=The tracking number of the container handling order, uniquely identified
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_orderId_label=Order number
Falcon_Bp_MarkContainerTransportOrderDoneBp_description=Update the status of the container handling order based on the order number
Falcon_Bp_MarkContainerTransportOrderDoneBp_label=Marking container handling list completed
Falcon_Bp_MoveInvByContainerBp_Input_leafContainerId_description=Please enter the number of the innermost container.
Falcon_Bp_MoveInvByContainerBp_Input_leafContainerId_label=Innermost container
Falcon_Bp_MoveInvByContainerBp_Input_state_description=Modify the inventory status to the specified inventory status
Falcon_Bp_MoveInvByContainerBp_Input_state_label=Specify stock status
Falcon_Bp_MoveInvByContainerBp_Input_toBinId_description=The number of the storage bin where the innermost container will be placed.
Falcon_Bp_MoveInvByContainerBp_Input_toBinId_label=End storage bin
Falcon_Bp_MoveInvByContainerBp_description=Bind the innermost container information to another storage bin.
Falcon_Bp_MoveInvByContainerBp_label=Move inventory by container
Falcon_Bp_PadIntStrBp_Input_numLength_description=After converting numbers to strings, if the specified length is less than the original length, the specified length is ignored; otherwise, fill it with 0
Falcon_Bp_PadIntStrBp_Input_numLength_label=Digital length
Falcon_Bp_PadIntStrBp_Input_num_description=Numbers to be converted
Falcon_Bp_PadIntStrBp_Input_num_label=Digital
Falcon_Bp_PadIntStrBp_Input_prefix_description=Converted string prefix
Falcon_Bp_PadIntStrBp_Input_prefix_label=Prefix
Falcon_Bp_PadIntStrBp_Input_suffix_description=Converted string suffix
Falcon_Bp_PadIntStrBp_Input_suffix_label=Suffix
Falcon_Bp_PadIntStrBp_Output_numStr_description=String after digital conversion
Falcon_Bp_PadIntStrBp_Output_numStr_label=String after digital conversion
Falcon_Bp_PadIntStrBp_description=According to the condition, convert the number to a string of the specified length, and fill in 0 if it is insufficient.
Falcon_Bp_PadIntStrBp_label=Convert numbers to a specified length string
Falcon_Bp_ParallelFlowBp_description=Parallel execution
Falcon_Bp_ParallelFlowBp_label=Parallel execution
Falcon_Bp_PrintBp_Input_message_description=Information to be printed
Falcon_Bp_PrintBp_Input_message_label=Message
Falcon_Bp_PrintBp_description=Print
Falcon_Bp_PrintBp_label=Print
Falcon_Bp_ReduceBinInvFromOrderBp_Input_binField_description=Please enter the field name that represents storage bin on a single line.
Falcon_Bp_ReduceBinInvFromOrderBp_Input_binField_label=Storage bin field
Falcon_Bp_ReduceBinInvFromOrderBp_Input_ev_description=Please enter documentation
Falcon_Bp_ReduceBinInvFromOrderBp_Input_ev_label=Documentation
Falcon_Bp_ReduceBinInvFromOrderBp_Input_materialField_description=Please enter the field name representing the material on a single line
Falcon_Bp_ReduceBinInvFromOrderBp_Input_materialField_label=Material field
Falcon_Bp_ReduceBinInvFromOrderBp_Input_outboundOrderIdField_label=Outbound order number
Falcon_Bp_ReduceBinInvFromOrderBp_Input_pickOrderIdField_label=Sorting order number
Falcon_Bp_ReduceBinInvFromOrderBp_Input_qtyField_description=Please enter the field name that represents the quantity on a single line.
Falcon_Bp_ReduceBinInvFromOrderBp_Input_qtyField_label=Quantity field
Falcon_Bp_ReduceBinInvFromOrderBp_Input_subContainerIdField_label=Lattice
Falcon_Bp_ReduceBinInvFromOrderBp_description=Delete inventory from storage bin by documentation
Falcon_Bp_ReduceBinInvFromOrderBp_label=Delete inventory from storage bin by documentation
Falcon_Bp_RemoveInvByContainerBp_Input_containerId_description=Please enter the container number recorded in M4.
Falcon_Bp_RemoveInvByContainerBp_Input_containerId_label=Container number
Falcon_Bp_RemoveInvByContainerBp_Output_count_description=The number of deleted inventory details.
Falcon_Bp_RemoveInvByContainerBp_Output_count_label=Detailed quantity
Falcon_Bp_RemoveInvByContainerBp_description=Delete the corresponding inventory details according to the container number.
Falcon_Bp_RemoveInvByContainerBp_label=Delete inventory details by container
Falcon_Bp_RemoveTraceContainerBp_Input_containerId_description=Please enter the tracking container number recorded by M4.
Falcon_Bp_RemoveTraceContainerBp_Input_containerId_label=Container number
Falcon_Bp_RemoveTraceContainerBp_description=Delete the tracking container.
Falcon_Bp_RemoveTraceContainerBp_label=Delete tracking container
Falcon_Bp_RepeatNumBp_Child_default_label=Component
Falcon_Bp_RepeatNumBp_Context_index_description=Serial number
Falcon_Bp_RepeatNumBp_Context_index_label=Serial number
Falcon_Bp_RepeatNumBp_Input_num_description=Number of repeated executions of subcomponents
Falcon_Bp_RepeatNumBp_Input_num_label=Number of executions
Falcon_Bp_RepeatNumBp_description=Subcomponent repeated execution
Falcon_Bp_RepeatNumBp_label=Repeated Execution For
Falcon_Bp_SerialFlowBp_description=Serial execution
Falcon_Bp_SerialFlowBp_label=Serial execution
Falcon_Bp_SetBinEmptyBp_Input_binId_description=Storage bin ID
Falcon_Bp_SetBinEmptyBp_Input_binId_label=Storage bin number
Falcon_Bp_SetBinEmptyBp_Input_noContainer_description=True sets the storage bin to no container
Falcon_Bp_SetBinEmptyBp_Input_noContainer_label=No container
Falcon_Bp_SetBinEmptyBp_Input_notOccupied_description=True sets the storage bin to unoccupied
Falcon_Bp_SetBinEmptyBp_Input_notOccupied_label=Not occupied
Falcon_Bp_SetBinEmptyBp_Input_unlock_label=Unlock
Falcon_Bp_SetBinEmptyBp_Input_unlock_label_description=True to unlock the storage bin
Falcon_Bp_SetBinEmptyBp_description=Set storage bin unoccupied (deprecated)
Falcon_Bp_SetBinEmptyBp_label=Set storage bin unoccupied (deprecated)
Falcon_Bp_SetBinNotOccupiedBp_Input_binId_description=Storage bin ID
Falcon_Bp_SetBinNotOccupiedBp_Input_binId_label=Storage bin number
Falcon_Bp_SetBinNotOccupiedBp_Input_unlockAfter_description=True is to set unoccupied and unlock the storage bin, false is to set unoccupied and not unlock the storage bin.
Falcon_Bp_SetBinNotOccupiedBp_Input_unlockAfter_label=Unlock
Falcon_Bp_SetBinNotOccupiedBp_description=Set storage bin unoccupied
Falcon_Bp_SetBinNotOccupiedBp_label=Set storage bin unoccupied
Falcon_Bp_SetBinUnLockBp_Input_binId_label=Storage bin number
Falcon_Bp_SetBinUnLockBp_Input_requireLock_description=Whether to require the storage bin to be locked. If the storage bin is not locked, abnormal termination will occur
Falcon_Bp_SetBinUnLockBp_Input_requireLock_label=The storage bin is required to be a locked storage bin
Falcon_Bp_SetBinUnLockBp_description=Unlock designated storage bin
Falcon_Bp_SetBinUnLockBp_label=Unlock storage bin
Falcon_Bp_SetGlobalVariableBp_Input_varName_description=Fill in the variable name, not the variable value
Falcon_Bp_SetGlobalVariableBp_Input_varName_label=Variable name
Falcon_Bp_SetGlobalVariableBp_Input_varValue_description=The data type should match the global variable
Falcon_Bp_SetGlobalVariableBp_Input_varValue_label=Variable value
Falcon_Bp_SetGlobalVariableBp_description=Only the value of global variables can be changed, and global variables cannot be added. When there is no specified name in the global variable, an exception will be thrown
Falcon_Bp_SetGlobalVariableBp_label=Set global variables
Falcon_Bp_SetTaskVariableBp_Input_varName_description=The name of the task variable
Falcon_Bp_SetTaskVariableBp_Input_varName_label=Variable name
Falcon_Bp_SetTaskVariableBp_Input_varValue_description=Value of task variable
Falcon_Bp_SetTaskVariableBp_Input_varValue_label=Variable value
Falcon_Bp_SetTaskVariableBp_description=Set task variables
Falcon_Bp_SetTaskVariableBp_label=Set task variables
Falcon_Bp_SimpleScriptBp_Input_scriptStr_label=Script source code
Falcon_Bp_SimpleScriptBp_Output_scriptOut_description=Return value of script execution
Falcon_Bp_SimpleScriptBp_Output_scriptOut_label=Return value
Falcon_Bp_SimpleScriptBp_label=Simple script
Falcon_Bp_SuiBp_Input_config_description=User of configuration operation
Falcon_Bp_SuiBp_Input_config_label=Configure
Falcon_Bp_SuiBp_Output_button_description=Buttons clicked by the user
Falcon_Bp_SuiBp_Output_button_label=Buttons clicked by the user
Falcon_Bp_SuiBp_Output_input_description=User input parameters
Falcon_Bp_SuiBp_Output_input_label=User input
Falcon_Bp_SuiBp_description=Waiting for user action
Falcon_Bp_SuiBp_label=User intervention
Falcon_Bp_ThrowBp_Input_condition_description=Whether to throw an exception
Falcon_Bp_ThrowBp_Input_condition_label=Conditions
Falcon_Bp_ThrowBp_Input_errMsg_description=Throw exception message
Falcon_Bp_ThrowBp_Input_errMsg_label=Abnormal message
Falcon_Bp_ThrowBp_description=Throw an exception
Falcon_Bp_ThrowBp_label=Throw an exception
Falcon_Bp_TimestampBp_Input_formatDate_description=Specify date format
Falcon_Bp_TimestampBp_Input_formatDate_label=Date format
Falcon_Bp_TimestampBp_Output_timestamp_description=Return a string in the specified date format.
Falcon_Bp_TimestampBp_Output_timestamp_label=Current timestamp
Falcon_Bp_TimestampBp_description=Return the current time string in the specified format.
Falcon_Bp_TimestampBp_label=Return to the current time
Falcon_Bp_TriggerTaskEventBp_Input_eventData_description=Event data
Falcon_Bp_TriggerTaskEventBp_Input_eventData_label=Event data
Falcon_Bp_TriggerTaskEventBp_Input_eventName_description=Name of the event
Falcon_Bp_TriggerTaskEventBp_Input_eventName_label=Event name
Falcon_Bp_TriggerTaskEventBp_description=Trigger task events
Falcon_Bp_TriggerTaskEventBp_label=Trigger task events
Falcon_Bp_TryCatchBp_Input_ignoreAbort_description=Ignore cancellation exception
Falcon_Bp_TryCatchBp_Input_ignoreAbort_label=Ignore cancellation exception
Falcon_Bp_TryCatchBp_Input_swallowError_description=Swallow the exception and do not throw it
Falcon_Bp_TryCatchBp_Input_swallowError_label=Swallow exceptions without throwing
Falcon_Bp_TryCatchBp_description=Capture exception information
Falcon_Bp_TryCatchBp_label=try-catch
Falcon_Bp_UnbindBinContainerBp_Input_binId_description=Storage bin ID
Falcon_Bp_UnbindBinContainerBp_Input_binId_label=Storage bin number
Falcon_Bp_UnbindBinContainerBp_Input_containerId_description=Container number
Falcon_Bp_UnbindBinContainerBp_Input_containerId_label=Container number
Falcon_Bp_UnbindBinContainerBp_Input_unlockBin_description=Whether to unlock the storage bin
Falcon_Bp_UnbindBinContainerBp_Input_unlockBin_label=Unlock storage bin
Falcon_Bp_UnbindBinContainerBp_Input_unlockContainer_description=Whether to unlock the container
Falcon_Bp_UnbindBinContainerBp_Input_unlockContainer_label=Unlock the container
Falcon_Bp_UnbindBinContainerBp_description=Unbind the storage bin from the container
Falcon_Bp_UnbindBinContainerBp_label=Unbind storage bin container
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_Option_fromBin=Starting storage bin
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_Option_loaded=Picked up
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_Option_robotName=Robot
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_Option_status=Status
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_Option_toBin=End storage bin
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_Option_unloaded=Has dropped off
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_description=Field names that need to be updated
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_label=Field
Falcon_Bp_UpdateContainerTransportOrderBp_Input_fv_description=Expected updated field values
Falcon_Bp_UpdateContainerTransportOrderBp_Input_fv_label=Field value
Falcon_Bp_UpdateContainerTransportOrderBp_Input_orderId_description=The tracking number of the container handling order, uniquely identified
Falcon_Bp_UpdateContainerTransportOrderBp_Input_orderId_label=Order number
Falcon_Bp_UpdateContainerTransportOrderBp_description=Update the value of the specified field on the container shipping order
Falcon_Bp_UpdateContainerTransportOrderBp_label=Update container handling list
Falcon_Bp_UpdateEntityFieldBp_Input_entityName_description=Business Object Name
Falcon_Bp_UpdateEntityFieldBp_Input_entityName_label=Business Object Name
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_Any=Any type (Any)
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_Boolean=Boolean type
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_Double=Double precision floating point type
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_JSONArray=JSON array (JSONArray)
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_JSONObject=JSON Object
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_Long=Long integer
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_String=String type (String)
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_description=The type of field to query
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_label=Query field type
Falcon_Bp_UpdateEntityFieldBp_Input_queryField_description=Field name to query
Falcon_Bp_UpdateEntityFieldBp_Input_queryField_label=Queried field
Falcon_Bp_UpdateEntityFieldBp_Input_queryValue_description=The value of the field to be queried
Falcon_Bp_UpdateEntityFieldBp_Input_queryValue_label=Query value
Falcon_Bp_UpdateEntityFieldBp_Input_setToNull_label=Set to null
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_Any=Any type (Any)
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_Boolean=Boolean type
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_Double=Double precision floating point type
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_JSONArray=JSON array (JSONArray)
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_JSONObject=JSON Object
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_Long=Long integer
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_String=String type (String)
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_description=Update the type of field
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_label=Update the type of field
Falcon_Bp_UpdateEntityFieldBp_Input_updateField_description=Fields to update
Falcon_Bp_UpdateEntityFieldBp_Input_updateField_label=Updated fields
Falcon_Bp_UpdateEntityFieldBp_Input_updateMany_description=Whether to update in batches
Falcon_Bp_UpdateEntityFieldBp_Input_updateMany_label=Batch update
Falcon_Bp_UpdateEntityFieldBp_Input_updateValue_description=Value after field update
Falcon_Bp_UpdateEntityFieldBp_Input_updateValue_label=Update value
Falcon_Bp_UpdateEntityFieldBp_description=Update Business Object individual fields based on conditions
Falcon_Bp_UpdateEntityFieldBp_label=Update Business Object individual fields based on conditions
Falcon_Bp_UpdateFalconRecordFieldBp_Input_fieldName_label=Set fields
Falcon_Bp_UpdateFalconRecordFieldBp_Output_fieldValue_label=Set value
Falcon_Bp_UpdateFalconRecordFieldBp_label=Set task record field
Falcon_Bp_UpdateOneEntityByIdBp_Input_entityName_description=Business Object Name
Falcon_Bp_UpdateOneEntityByIdBp_Input_entityName_label=Business Object Name
Falcon_Bp_UpdateOneEntityByIdBp_Input_fieldName_description=Update field name
Falcon_Bp_UpdateOneEntityByIdBp_Input_fieldName_label=Field name
Falcon_Bp_UpdateOneEntityByIdBp_Input_fv_description=The value of the updated field
Falcon_Bp_UpdateOneEntityByIdBp_Input_fv_label=Field value
Falcon_Bp_UpdateOneEntityByIdBp_Input_id_description=Entity Id
Falcon_Bp_UpdateOneEntityByIdBp_Input_id_label=Id
Falcon_Bp_UpdateOneEntityByIdBp_Input_setToNull_label=Set to null
Falcon_Bp_UpdateOneEntityByIdBp_description=Update Business Object entity single field based on id
Falcon_Bp_UpdateOneEntityByIdBp_label=Update Business Object single field based on id
Falcon_Bp_WebSocketBp_Input_eventName_description=WebSocket communication identifier
Falcon_Bp_WebSocketBp_Input_eventName_label=Send event
Falcon_Bp_WebSocketBp_Input_message_description=Send content
Falcon_Bp_WebSocketBp_Input_message_label=Send content
Falcon_Bp_WebSocketBp_description=Send Message to WebSocket Client
Falcon_Bp_WebSocketBp_label=WebSocket sends messages
Falcon_Bp_WhileBp_Input_condition_label=Conditions
Falcon_Bp_WhileBp_description=Continue to execute submodules when the conditions are met
Falcon_Bp_WhileBp_label=While loop execution
Falcon_label=Falcon mission

[wcs.base]
ModbusDeviceNotInit=Modbus device "{0}" is not Initialized
wcs_err_TomNoUrl=Scheduling '{0}' not configured URL
wcs_err_TomNotFound=Scheduling '{0}' not configured
wcs_err_tom_BlockError=Scheduling says block execution failed, order number = {0}, block = {1}, status = {2}
wcs_err_tom_BlockNotFound=Scheduling says block not found, order number = {0}, block = {1}
wcs_err_tom_Completed_No_Path=Dispatch error reported, and it has been sealed and cannot be recovered. Reason: Unable to find the path to the site
wcs_err_tom_ConnectError=Connection scheduling server failed: {0}
wcs_err_tom_HttpError=Request scheduling interface Other errors: [{0}] {1}
wcs_err_tom_HttpError404=Request scheduling interface report 404
wcs_err_tom_IOError=Request scheduling, IO error: {0}, Request address: {1}
wcs_err_tom_OtherError=Request scheduling, other errors: {0}, request address: {1}
wcs_err_tom_TomError=Dispatch error, error code = {0}, message = {1}
wcs_err_tom_TomResponseEmpty=Request scheduling response body is empty

[wcs.error]
clientDeviceExceptionConnectFail=Link failed {0}
clientDeviceExceptionDeviceDisabled=Device disabled {0}
clientDeviceExceptionDeviceNotExist=Device does not exist {0}
clientDeviceExceptionOpCancel=Thread interrupted {0}
clientDeviceExceptionOpTimeout=Operation Timeout {0}
clientDeviceExceptionReadFail=Read failed {0}
clientDeviceExceptionReqError=Request error {0}
clientDeviceExceptionWriteFail=Write failed {0}
errDeviceDisabled=Device {0} is disabled
errNoDevice=No such device: {0}

[wcs.falcon]
Falcon_BlockGroup_PLC=PLC
Falcon_Bp_ModbusReadBp_Input_address_description=Address of Modbus
Falcon_Bp_ModbusReadBp_Input_address_label=Address
Falcon_Bp_ModbusReadBp_Input_code_Option_1=0X01 Read coil status
Falcon_Bp_ModbusReadBp_Input_code_Option_2=0X02 Read input status
Falcon_Bp_ModbusReadBp_Input_code_Option_3=0X03 Read Hold Register
Falcon_Bp_ModbusReadBp_Input_code_Option_4=0X04 Read input registers
Falcon_Bp_ModbusReadBp_Input_code_description=Function code of Modbus
Falcon_Bp_ModbusReadBp_Input_code_label=Function code
Falcon_Bp_ModbusReadBp_Input_deviceName_description=The device name needs to be configured in the PLC device configuration menu first
Falcon_Bp_ModbusReadBp_Input_deviceName_label=Device name
Falcon_Bp_ModbusReadBp_Input_maxRetry_description=Maximum number of retries, default is the maximum number of retries set in the PLC device configuration menu
Falcon_Bp_ModbusReadBp_Input_maxRetry_label=Maximum number of retries
Falcon_Bp_ModbusReadBp_Input_retryDelay_description=Retry interval, default is the retry interval set in the PLC device configuration menu
Falcon_Bp_ModbusReadBp_Input_retryDelay_label=Retry wait (milliseconds)
Falcon_Bp_ModbusReadBp_Input_slaveId_description=Modbus device id, default is 0
Falcon_Bp_ModbusReadBp_Input_slaveId_label=Slave ID
Falcon_Bp_ModbusReadBp_Output_value_description=The value of the corresponding address read out
Falcon_Bp_ModbusReadBp_Output_value_label=Value
Falcon_Bp_ModbusReadBp_description=Read the value of Modbus
Falcon_Bp_ModbusReadBp_label=Modbus read
Falcon_Bp_ModbusReadEqBp_Input_address_description=Address of Modbus
Falcon_Bp_ModbusReadEqBp_Input_address_label=Address
Falcon_Bp_ModbusReadEqBp_Input_code_Option_1=0X01 Read coil status
Falcon_Bp_ModbusReadEqBp_Input_code_Option_2=0X02 Read input status
Falcon_Bp_ModbusReadEqBp_Input_code_Option_3=0X03 Read Hold Register
Falcon_Bp_ModbusReadEqBp_Input_code_Option_4=0X04 Read input registers
Falcon_Bp_ModbusReadEqBp_Input_code_description=Function code of Modbus
Falcon_Bp_ModbusReadEqBp_Input_code_label=Function code
Falcon_Bp_ModbusReadEqBp_Input_deviceName_description=The device name needs to be configured in the PLC device configuration menu first
Falcon_Bp_ModbusReadEqBp_Input_deviceName_label=Device name
Falcon_Bp_ModbusReadEqBp_Input_maxRetry_description=In the event of a read failure, the maximum number of automatic retries is set by default in the PLC device configuration menu
Falcon_Bp_ModbusReadEqBp_Input_maxRetry_label=Failure limit
Falcon_Bp_ModbusReadEqBp_Input_readDelay_description=The interval between two reads, default is 1s
Falcon_Bp_ModbusReadEqBp_Input_readDelay_label=Read interval
Falcon_Bp_ModbusReadEqBp_Input_readLimit_description=The number of times the read value is different from the expected limit, the default value is -1. Leave it blank, -1 means no limit.
Falcon_Bp_ModbusReadEqBp_Input_readLimit_label=Limit on the number of times the read value does not meet expectations
Falcon_Bp_ModbusReadEqBp_Input_retryDelay_description=In the event of a read failure, the retry interval defaults to the retry interval set in the PLC device configuration menu
Falcon_Bp_ModbusReadEqBp_Input_retryDelay_label=Failure retry wait (milliseconds)
Falcon_Bp_ModbusReadEqBp_Input_slaveId_description=Modbus device id, default is 0
Falcon_Bp_ModbusReadEqBp_Input_slaveId_label=Slave ID
Falcon_Bp_ModbusReadEqBp_Input_targetValue_description=Target value
Falcon_Bp_ModbusReadEqBp_Input_targetValue_label=Target value
Falcon_Bp_ModbusReadEqBp_description=Read Modbus until it equals the expected value, or keep reading until it exceeds the maximum number of retries
Falcon_Bp_ModbusReadEqBp_label=Modbus reads until equal to
Falcon_Bp_ModbusWriteBp_Input_address_description=Address of Modbus
Falcon_Bp_ModbusWriteBp_Input_address_label=Address
Falcon_Bp_ModbusWriteBp_Input_code_Option_5=0X05 Write single coil
Falcon_Bp_ModbusWriteBp_Input_code_Option_6=0X06 Write a single register
Falcon_Bp_ModbusWriteBp_Input_code_description=Function code of Modbus
Falcon_Bp_ModbusWriteBp_Input_code_label=Function code
Falcon_Bp_ModbusWriteBp_Input_deviceName_description=The device name needs to be configured in the PLC device configuration menu first
Falcon_Bp_ModbusWriteBp_Input_deviceName_label=Device name
Falcon_Bp_ModbusWriteBp_Input_maxRetry_description=Maximum number of retries, default is the maximum number of retries set in the PLC device configuration menu
Falcon_Bp_ModbusWriteBp_Input_maxRetry_label=Maximum number of retries
Falcon_Bp_ModbusWriteBp_Input_retryDelay_description=Retry interval, default is the retry interval set in the PLC device configuration menu
Falcon_Bp_ModbusWriteBp_Input_retryDelay_label=Retry wait (milliseconds)
Falcon_Bp_ModbusWriteBp_Input_slaveId_description=Modbus device id, default is 0
Falcon_Bp_ModbusWriteBp_Input_slaveId_label=Slave ID
Falcon_Bp_ModbusWriteBp_Input_value_description=Value written
Falcon_Bp_ModbusWriteBp_Input_value_label=Value
Falcon_Bp_ModbusWriteBp_description=Write to Modbus
Falcon_Bp_ModbusWriteBp_label=Modbus write
Falcon_Bp_S7ReadBp_Input_bitOffset_description=Bit offset
Falcon_Bp_S7ReadBp_Input_bitOffset_label=Bit offset
Falcon_Bp_S7ReadBp_Input_blockType_description=Please select the corresponding block type according to the agreement content.
Falcon_Bp_S7ReadBp_Input_blockType_label=Block type
Falcon_Bp_S7ReadBp_Input_byteOffset_description=Byte offset can be understood as address number.
Falcon_Bp_S7ReadBp_Input_byteOffset_label=Byte offset
Falcon_Bp_S7ReadBp_Input_dataType_description=Please select the corresponding data type according to the agreement content.
Falcon_Bp_S7ReadBp_Input_dataType_label=Data type
Falcon_Bp_S7ReadBp_Input_dbId_description=DB number, please enter the corresponding value according to the agreement content.
Falcon_Bp_S7ReadBp_Input_dbId_label=dbId
Falcon_Bp_S7ReadBp_Input_deviceName_description=Please enter the name of the S7 device configured in "PLC Facility Management".
Falcon_Bp_S7ReadBp_Input_deviceName_label=Device name
Falcon_Bp_S7ReadBp_Input_maxRetry_description=Maximum number of retries
Falcon_Bp_S7ReadBp_Input_maxRetry_label=Maximum number of retries
Falcon_Bp_S7ReadBp_Input_retryDelay_description=Retry interval, in milliseconds
Falcon_Bp_S7ReadBp_Input_retryDelay_label=Retry wait (milliseconds)
Falcon_Bp_S7ReadBp_Output_value_description=Value
Falcon_Bp_S7ReadBp_Output_value_label=Value
Falcon_Bp_S7ReadBp_description=Read one data of the PLC device through the S7 protocol and use the read value as the output parameter of this component.
Falcon_Bp_S7ReadBp_label=S7 read
Falcon_Bp_S7ReadEqBp_Input_bitOffset_description=Bit offset
Falcon_Bp_S7ReadEqBp_Input_bitOffset_label=Bit offset
Falcon_Bp_S7ReadEqBp_Input_blockType_description=Please select the corresponding block type according to the agreement content.
Falcon_Bp_S7ReadEqBp_Input_blockType_label=Block type
Falcon_Bp_S7ReadEqBp_Input_byteOffset_description=Byte offset can be understood as address number.
Falcon_Bp_S7ReadEqBp_Input_byteOffset_label=Byte offset
Falcon_Bp_S7ReadEqBp_Input_dataType_description=Please select the corresponding data type according to the agreement content.
Falcon_Bp_S7ReadEqBp_Input_dataType_label=Data type
Falcon_Bp_S7ReadEqBp_Input_dbId_description=DB number, please enter the corresponding value according to the agreement content.
Falcon_Bp_S7ReadEqBp_Input_dbId_label=dbId
Falcon_Bp_S7ReadEqBp_Input_deviceName_description=Please enter the name of the S7 device configured in "PLC Facility Management".
Falcon_Bp_S7ReadEqBp_Input_deviceName_label=Device name
Falcon_Bp_S7ReadEqBp_Input_maxRetry_description=Maximum number of retries
Falcon_Bp_S7ReadEqBp_Input_maxRetry_label=Maximum number of retries
Falcon_Bp_S7ReadEqBp_Input_readDelay_description=The interval between two reads, default is 1s
Falcon_Bp_S7ReadEqBp_Input_readDelay_label=Read interval
Falcon_Bp_S7ReadEqBp_Input_readLimit_description=The number of times the read value is different from the expected limit, the default value is -1. Leave it blank, -1 means no limit.
Falcon_Bp_S7ReadEqBp_Input_readLimit_label=Limit on the number of times the read value does not meet expectations
Falcon_Bp_S7ReadEqBp_Input_retryDelay_description=In the event of a read failure, the retry interval defaults to the retry interval set in the PLC device configuration menu
Falcon_Bp_S7ReadEqBp_Input_retryDelay_label=Failure retry wait (milliseconds)
Falcon_Bp_S7ReadEqBp_Input_value_description=Expected value
Falcon_Bp_S7ReadEqBp_Input_value_label=Value
Falcon_Bp_S7ReadEqBp_description=Read a data from the PLC device continuously through the S7 protocol until the read value matches the expected value before stopping.
Falcon_Bp_S7ReadEqBp_label=S7 reads until it equals
Falcon_Bp_S7WriteBp_Input_bitOffset_description=Bit offset
Falcon_Bp_S7WriteBp_Input_bitOffset_label=Bit offset
Falcon_Bp_S7WriteBp_Input_blockType_description=Please select the corresponding block type according to the agreement content.
Falcon_Bp_S7WriteBp_Input_blockType_label=Block type
Falcon_Bp_S7WriteBp_Input_byteOffset_description=Byte offset can be understood as address number.
Falcon_Bp_S7WriteBp_Input_byteOffset_label=Byte offset
Falcon_Bp_S7WriteBp_Input_dataType_description=Please select the corresponding data type according to the agreement content.
Falcon_Bp_S7WriteBp_Input_dataType_label=Data type
Falcon_Bp_S7WriteBp_Input_dbId_description=DB number, please enter the corresponding value according to the agreement content.
Falcon_Bp_S7WriteBp_Input_dbId_label=dbId
Falcon_Bp_S7WriteBp_Input_deviceName_description=Please enter the name of the S7 device configured in "PLC Facility Management".
Falcon_Bp_S7WriteBp_Input_deviceName_label=Device name
Falcon_Bp_S7WriteBp_Input_maxRetry_description=Maximum number of retries
Falcon_Bp_S7WriteBp_Input_maxRetry_label=Maximum number of retries
Falcon_Bp_S7WriteBp_Input_retryDelay_description=Retry interval, in milliseconds
Falcon_Bp_S7WriteBp_Input_retryDelay_label=Retry wait (milliseconds)
Falcon_Bp_S7WriteBp_Input_value_description=Target value
Falcon_Bp_S7WriteBp_Input_value_label=Value
Falcon_Bp_S7WriteBp_description=Write a target value to the PLC device through the S7 protocol.
Falcon_Bp_S7WriteBp_label=S7 write
errModbusReadEqNotMatch=ModbusReadEqBp read value is different from the expected number of times exceeds the limit
errS7ReadEqNotMatch=S7ReadEqBp read value is different from the expected number of times exceeds the limit

[fleet.base]
RbkApiNoNoSupported=API number {0} not supported
RbkClientConnectFail=Connect to RBK failed {0}
RbkClientRequestFail=Request RBK failed {0}
RobotAlarm=Robot alarm
RobotNotExistedById=The robot "{0}" does not exist

[fleet.falcon]
Falcon_BlockGroup_DirectOrder=Direct waybill
Falcon_BlockGroup_Map=Map
Falcon_BlockGroup_Ndc=NDC dispatch waybill
Falcon_BlockGroup_SeerTom=Second generation dispatch waybill
Falcon_BlockGroup_TransportOrder=Third generation dispatching waybill
Falcon_Bp_AddTomBlockBp_Input_binTask_description=binTask
Falcon_Bp_AddTomBlockBp_Input_binTask_label=binTask
Falcon_Bp_AddTomBlockBp_Input_goodsId_description=Container number
Falcon_Bp_AddTomBlockBp_Input_goodsId_label=Container number
Falcon_Bp_AddTomBlockBp_Input_location_description=Destination name, site name, or storage bin
Falcon_Bp_AddTomBlockBp_Input_location_label=Site
Falcon_Bp_AddTomBlockBp_Input_operation_description=Execution mechanism
Falcon_Bp_AddTomBlockBp_Input_operation_label=Action
Falcon_Bp_AddTomBlockBp_Input_orderId_description=Create second-generation dispatch waybill. The "waybill number" generated by the block.
Falcon_Bp_AddTomBlockBp_Input_orderId_label=Waybill number
Falcon_Bp_AddTomBlockBp_Input_tomId_description=Scene name of "Second Generation Scheduling" on the "Robot Application" page
Falcon_Bp_AddTomBlockBp_Input_tomId_label=Dispatch ID
Falcon_Bp_AddTomBlockBp_Output_blockId_description=Block ID of the action in scheduling
Falcon_Bp_AddTomBlockBp_Output_blockId_label=Block ID
Falcon_Bp_AddTomBlockBp_description=Append action block
Falcon_Bp_AddTomBlockBp_label=Add second-generation waybill block
Falcon_Bp_AddTransportStepBp_Input_binTask_description=binTask
Falcon_Bp_AddTransportStepBp_Input_binTask_label=binTask
Falcon_Bp_AddTransportStepBp_Input_containerDir_description=The direction of the container, unit is angle
Falcon_Bp_AddTransportStepBp_Input_containerDir_label=Container direction
Falcon_Bp_AddTransportStepBp_Input_containerId_description=Container number
Falcon_Bp_AddTransportStepBp_Input_containerId_label=Container number
Falcon_Bp_AddTransportStepBp_Input_containerTypeName_description=Name of container type
Falcon_Bp_AddTransportStepBp_Input_containerTypeName_label=Container type name
Falcon_Bp_AddTransportStepBp_Input_forLoad_description=Do you want to pick up the goods at this step?
Falcon_Bp_AddTransportStepBp_Input_forLoad_label=Pick up the goods at this step
Falcon_Bp_AddTransportStepBp_Input_forUnload_description=Whether to unload at this step
Falcon_Bp_AddTransportStepBp_Input_forUnload_label=Unload at this step
Falcon_Bp_AddTransportStepBp_Input_interruptible_description=interruptible
Falcon_Bp_AddTransportStepBp_Input_interruptible_label=interruptible
Falcon_Bp_AddTransportStepBp_Input_location_description=Point or storage bin
Falcon_Bp_AddTransportStepBp_Input_location_label=Point or storage bin
Falcon_Bp_AddTransportStepBp_Input_operationArgs_description=Action parameters, JSON format
Falcon_Bp_AddTransportStepBp_Input_operationArgs_label=Action parameter
Falcon_Bp_AddTransportStepBp_Input_operation_description=Action
Falcon_Bp_AddTransportStepBp_Input_operation_label=Action
Falcon_Bp_AddTransportStepBp_Input_orderId_description=Waybill number
Falcon_Bp_AddTransportStepBp_Input_orderId_label=Waybill number
Falcon_Bp_AddTransportStepBp_Output_robotName_description=Robot name
Falcon_Bp_AddTransportStepBp_Output_robotName_label=Robot name
Falcon_Bp_AddTransportStepBp_Output_stepId_description=Step ID
Falcon_Bp_AddTransportStepBp_Output_stepId_label=Step ID
Falcon_Bp_AddTransportStepBp_description=Steps to add third-generation waybills
Falcon_Bp_AddTransportStepBp_label=Steps to add third-generation waybills
Falcon_Bp_AllowNdcLoadBp_Input_orderId_label=Waybill number
Falcon_Bp_AllowNdcLoadBp_description=Not enabled
Falcon_Bp_AllowNdcLoadBp_label=Allow NDC loading
Falcon_Bp_AllowNdcUnloadBp_Input_orderId_label=Waybill number
Falcon_Bp_AllowNdcUnloadBp_description=Not enabled
Falcon_Bp_AllowNdcUnloadBp_label=Allow NDC to unload
Falcon_Bp_CompleteTomOrderBp_Input_orderId_description=Create second-generation dispatch waybill. The "waybill number" generated by the block.
Falcon_Bp_CompleteTomOrderBp_Input_orderId_label=Waybill number
Falcon_Bp_CompleteTomOrderBp_Input_tomId_description=Scene name of "Second Generation Scheduling" on the "Robot Application" page
Falcon_Bp_CompleteTomOrderBp_Input_tomId_label=Dispatch ID
Falcon_Bp_CompleteTomOrderBp_description=Sealing
Falcon_Bp_CompleteTomOrderBp_label=End the second-generation dispatch waybill
Falcon_Bp_CompleteTransportOrderBp_Input_orderId_description=Waybill number
Falcon_Bp_CompleteTransportOrderBp_Input_orderId_label=Waybill number
Falcon_Bp_CompleteTransportOrderBp_description=End the third generation waybill
Falcon_Bp_CompleteTransportOrderBp_label=End the third generation waybill
Falcon_Bp_CreateNdcOrderBp_Input_endBin_description=NDC storage bin, Short type
Falcon_Bp_CreateNdcOrderBp_Input_endBin_label=End point
Falcon_Bp_CreateNdcOrderBp_Input_priority_label=Priority
Falcon_Bp_CreateNdcOrderBp_Input_startBin_description=NDC storage bin, Short type
Falcon_Bp_CreateNdcOrderBp_Input_startBin_label=Starting point
Falcon_Bp_CreateNdcOrderBp_Output_orderId_description=NDC Waybill "tracking number
Falcon_Bp_CreateNdcOrderBp_Output_orderId_label=Waybill number
Falcon_Bp_CreateNdcOrderBp_description=M4 sends the starting point, endpoint, and priority to NDC together; NDC executes it automatically; NDC reports to M4 after fetching, releasing, and completing tasks
Falcon_Bp_CreateNdcOrderBp_label=Create NDC waybill
Falcon_Bp_CreateTomOrderBp_Input_group_description=Specify a robot group to assign robots belonging to this robot group to execute when selecting vehicles
Falcon_Bp_CreateTomOrderBp_Input_group_label=Specify a bot group
Falcon_Bp_CreateTomOrderBp_Input_keyGoodsId_description=For the scenario of n + 1 material container trucks, the auxiliary core is the last to pick up the goods and the first to put them down
Falcon_Bp_CreateTomOrderBp_Input_keyGoodsId_label=keyGoodsId
Falcon_Bp_CreateTomOrderBp_Input_keyRouteStr_description=Key points are used to assist in determining the dispatching robot; if not filled, the system will automatically select the appropriate robot dispatching based on the current operating situation
Falcon_Bp_CreateTomOrderBp_Input_keyRouteStr_label=Key location
Falcon_Bp_CreateTomOrderBp_Input_keyTask_description=For "load" or "unload", if other fields are filled in, they will be automatically ignored and used to assist in determining the dispatching robot; if not filled in, the system will automatically select the appropriate robot dispatching according to the current operation status
Falcon_Bp_CreateTomOrderBp_Input_keyTask_label=keyTask
Falcon_Bp_CreateTomOrderBp_Input_label_description=Specify robot tags and require the system to assign robots with specific tags to the current waybill
Falcon_Bp_CreateTomOrderBp_Input_label_label=label
Falcon_Bp_CreateTomOrderBp_Input_notMarkComplete_description=Cancel the Falcon mission, do not seal the dispatch waybill
Falcon_Bp_CreateTomOrderBp_Input_notMarkComplete_label=Cancel the task, the waybill is not sealed
Falcon_Bp_CreateTomOrderBp_Input_priority_description=Waybill priority, the larger the number, the higher the order priority.
Falcon_Bp_CreateTomOrderBp_Input_priority_label=Priority
Falcon_Bp_CreateTomOrderBp_Input_tomId_description=Scene name of "Second Generation Scheduling" on the "Robot Application Management" page
Falcon_Bp_CreateTomOrderBp_Input_tomId_label=Scene name
Falcon_Bp_CreateTomOrderBp_Input_vehicle_description=Specify robot, used to assign the specified robot to execute when selecting a car
Falcon_Bp_CreateTomOrderBp_Input_vehicle_label=Specify a bot
Falcon_Bp_CreateTomOrderBp_Output_actualVehicle_description=Robot for second-generation scheduling allocation
Falcon_Bp_CreateTomOrderBp_Output_actualVehicle_label=Allocated robots
Falcon_Bp_CreateTomOrderBp_Output_orderId_description=Second generation dispatch waybill number
Falcon_Bp_CreateTomOrderBp_Output_orderId_label=Waybill number
Falcon_Bp_CreateTomOrderBp_description=Create a second-generation dispatch waybill, namely core dispatch
Falcon_Bp_CreateTomOrderBp_label=Create a second-generation dispatch waybill
Falcon_Bp_CreateTransportOrderBp_Input_expectedRobotGroups_description=The machine group to be executed can be specified multiple times, separated by commas
Falcon_Bp_CreateTransportOrderBp_Input_expectedRobotGroups_label=Expected machine group to execute
Falcon_Bp_CreateTransportOrderBp_Input_expectedRobotNames_description=Expected execution robots, multiple can be specified, separated by commas
Falcon_Bp_CreateTransportOrderBp_Input_expectedRobotNames_label=Robot looking forward to execution
Falcon_Bp_CreateTransportOrderBp_Input_keyLocations_description=Key location
Falcon_Bp_CreateTransportOrderBp_Input_keyLocations_label=Key location
Falcon_Bp_CreateTransportOrderBp_Input_priority_description=Priority of waybill execution
Falcon_Bp_CreateTransportOrderBp_Input_priority_label=Priority
Falcon_Bp_CreateTransportOrderBp_Input_sceneName_description=Scene name for 3rd generation scheduling
Falcon_Bp_CreateTransportOrderBp_Input_sceneName_label=Scene name
Falcon_Bp_CreateTransportOrderBp_Output_orderId_description=Waybill number
Falcon_Bp_CreateTransportOrderBp_Output_orderId_label=Waybill number
Falcon_Bp_CreateTransportOrderBp_Output_robotName_description=Robot name
Falcon_Bp_CreateTransportOrderBp_Output_robotName_label=Robot name
Falcon_Bp_CreateTransportOrderBp_description=Create third-generation dispatch waybills
Falcon_Bp_CreateTransportOrderBp_label=Create third-generation dispatch waybills
Falcon_Bp_DirectOrderExecuteBp_Input_desc_label=Description
Falcon_Bp_DirectOrderExecuteBp_Input_fillMiddleLandmarks_description=If there are other stations between the direct waybill steps, fill the intermediate station into the direct waybill
Falcon_Bp_DirectOrderExecuteBp_Input_fillMiddleLandmarks_label=Fill in the middle point
Falcon_Bp_DirectOrderExecuteBp_Input_robotId_label=Robot number
Falcon_Bp_DirectOrderExecuteBp_Input_sceneName_description=Fill in the scene name of "Robot Application Management" (2.5 generation scheduling) or the scene name of "Scheduling Scenario" (3 generation scheduling)
Falcon_Bp_DirectOrderExecuteBp_Input_sceneName_label=Scene name
Falcon_Bp_DirectOrderExecuteBp_Input_seer3066_description=Use the 3066 command to specify the path navigation
Falcon_Bp_DirectOrderExecuteBp_Input_seer3066_label=3066 instruction enabled
Falcon_Bp_DirectOrderExecuteBp_Input_taskId_label=Task number
Falcon_Bp_DirectOrderExecuteBp_Input_unlockCurrentDirectSiteIds_description=The default value is false, which represents unlocking all resources locked by the robot. True represents unlocking the resources locked by the direct waybill
Falcon_Bp_DirectOrderExecuteBp_Input_unlockCurrentDirectSiteIds_label=When unlocking resources, do you only unlock the resources locked by the direct waybill?
Falcon_Bp_DirectOrderExecuteBp_Output_orderId_description=Direct Waybill "tracking number
Falcon_Bp_DirectOrderExecuteBp_Output_orderId_label=Order number
Falcon_Bp_DirectOrderExecuteBp_description=Create "Direct Waybill" and wait for its execution to complete (no duplicate creation).
Falcon_Bp_DirectOrderExecuteBp_label=Direct Waybill Execution
Falcon_Bp_DirectOrderMoveBp_Input_binTask_label=binTask
Falcon_Bp_DirectOrderMoveBp_Input_containerId_label=Container number
Falcon_Bp_DirectOrderMoveBp_Input_id_description=Destination name, site name, or storage bin
Falcon_Bp_DirectOrderMoveBp_Input_id_label=Site
Falcon_Bp_DirectOrderMoveBp_Input_operation_description=Execution mechanism
Falcon_Bp_DirectOrderMoveBp_Input_operation_label=Action
Falcon_Bp_DirectOrderMoveBp_description=Direct waybill steps
Falcon_Bp_DirectOrderMoveBp_label=Direct waybill steps
Falcon_Bp_MapPointBinBp_Input_pointId_label=Site
Falcon_Bp_MapPointBinBp_Output_binId_label=Storage bin
Falcon_Bp_MapPointBinBp_Output_found_label=The site is equipped with a storage bin
Falcon_Bp_MapPointBinBp_label=Site storage bin
Falcon_Bp_WaitUntilNdcArriveEndBinBp_Input_orderId_label=Waybill number
Falcon_Bp_WaitUntilNdcArriveEndBinBp_description=Not enabled
Falcon_Bp_WaitUntilNdcArriveEndBinBp_label=Waiting for NDC to reach the finish line
Falcon_Bp_WaitUntilNdcArriveStartBinBp_Input_orderId_label=Waybill number
Falcon_Bp_WaitUntilNdcArriveStartBinBp_description=Not enabled
Falcon_Bp_WaitUntilNdcArriveStartBinBp_label=Wait for NDC to reach the starting point
Falcon_Bp_WaitUntilNdcFinishBp_Input_orderId_label=Waybill number
Falcon_Bp_WaitUntilNdcFinishBp_label=Waiting for NDC task to complete
Falcon_Bp_WaitUntilNdcLoadedBp_Input_orderId_label=Waybill number
Falcon_Bp_WaitUntilNdcLoadedBp_label=Waiting for NDC loading to complete
Falcon_Bp_WaitUntilNdcUnloadedBp_Input_orderId_label=Waybill number
Falcon_Bp_WaitUntilNdcUnloadedBp_label=Waiting for NDC unloading to complete

[fleet.single.base]
relocStatus_0=Location failed
relocStatus_1=Correct positioning
relocStatus_2=Relocating
relocStatus_3=Positioning complete
relocStatus_null=Positioning status unknown

[fleet.single.falcon]
Falcon_BlockGroup_RobotSingleControl=Bicycle control
Falcon_Bp_RobotReadDIBp_Input_id_description=Number of the specified DI
Falcon_Bp_RobotReadDIBp_Input_id_label=DI number
Falcon_Bp_RobotReadDIBp_Input_tomId_description=Scene name of "Second Generation Scheduling" on the "Robot Application" page
Falcon_Bp_RobotReadDIBp_Input_tomId_label=Dispatch ID
Falcon_Bp_RobotReadDIBp_Input_vehicle_description=Specify a bot
Falcon_Bp_RobotReadDIBp_Input_vehicle_label=Specify a bot
Falcon_Bp_RobotReadDIBp_Output_result_label=Read result
Falcon_Bp_RobotReadDIBp_description=Read the DI of the specified robot
Falcon_Bp_RobotReadDIBp_label=Read DI
Falcon_Bp_RobotSetDOBp_Input_id_description=Specify the number of DO
Falcon_Bp_RobotSetDOBp_Input_id_label=DO number
Falcon_Bp_RobotSetDOBp_Input_status_description=Modify the state of the specified DO to this state
Falcon_Bp_RobotSetDOBp_Input_status_label=Target state
Falcon_Bp_RobotSetDOBp_Input_tomId_description=Scene name of "Second Generation Scheduling" on the "Robot Application" page
Falcon_Bp_RobotSetDOBp_Input_tomId_label=Dispatch ID
Falcon_Bp_RobotSetDOBp_Input_vehicle_description=Specify a bot
Falcon_Bp_RobotSetDOBp_Input_vehicle_label=Specify a bot
Falcon_Bp_RobotSetDOBp_Output_result_description=Response result after execution
Falcon_Bp_RobotSetDOBp_Output_result_label=Result Request result
Falcon_Bp_RobotSetDOBp_description=Used to modify the status of a specified DO
Falcon_Bp_RobotSetDOBp_label=Set DO
Falcon_Bp_RobotSingleAngleRadianBp_Input_conversionType_Option_AngleToRadian=Angular curvature
Falcon_Bp_RobotSingleAngleRadianBp_Input_conversionType_Option_RadianToAngle=Angle of curvature
Falcon_Bp_RobotSingleAngleRadianBp_Input_conversionType_description=Select the conversion type and convert the numerical value to the expected degree or radian number.
Falcon_Bp_RobotSingleAngleRadianBp_Input_conversionType_label=Conversion type
Falcon_Bp_RobotSingleAngleRadianBp_Input_degrees_description=Select the conversion type and convert the numerical value to the expected degree or radian number.
Falcon_Bp_RobotSingleAngleRadianBp_Input_degrees_label=Numerical value
Falcon_Bp_RobotSingleAngleRadianBp_Output_degrees_description=The degree or radian after conversion.
Falcon_Bp_RobotSingleAngleRadianBp_Output_degrees_label=Results
Falcon_Bp_RobotSingleAngleRadianBp_description=Conversion between angle and radian
Falcon_Bp_RobotSingleAngleRadianBp_label=Conversion of radians and angles
Falcon_Bp_RobotSingleForkBp_Input_endHeight_description=The final height of the fork adjustment, default value 0.500 (unit: m).
Falcon_Bp_RobotSingleForkBp_Input_endHeight_label=End height
Falcon_Bp_RobotSingleForkBp_Input_forkDist_description=For forward-moving forks, the distance the fork moves forward is the default value of 0.00 (unit: m).
Falcon_Bp_RobotSingleForkBp_Input_forkDist_label=Forward moving distance (forward moving fork)
Falcon_Bp_RobotSingleForkBp_Input_forkMidHeight_description=The height of the fork when walking, default value is 0.100 (unit: m).
Falcon_Bp_RobotSingleForkBp_Input_forkMidHeight_label=Walking height
Falcon_Bp_RobotSingleForkBp_Input_operation_Option_ForkForward=Fork Forward
Falcon_Bp_RobotSingleForkBp_Input_operation_Option_ForkHeight=Fork Height
Falcon_Bp_RobotSingleForkBp_Input_operation_Option_ForkLoad=ForkLoad
Falcon_Bp_RobotSingleForkBp_Input_operation_Option_ForkUnload=Fork drop off (ForkUnload)
Falcon_Bp_RobotSingleForkBp_Input_operation_description=Operable action of fork mechanism
Falcon_Bp_RobotSingleForkBp_Input_operation_label=Action
Falcon_Bp_RobotSingleForkBp_Input_recfile_description=When the robot performs specific actions, this file is needed for identification and comparison
Falcon_Bp_RobotSingleForkBp_Input_recfile_label=Identification file
Falcon_Bp_RobotSingleForkBp_Input_reqId_description=Identity when the component is executed, optional
Falcon_Bp_RobotSingleForkBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleForkBp_Input_startHeight_description=The starting height of the fork, default value is 0.100 (unit: m).
Falcon_Bp_RobotSingleForkBp_Input_startHeight_label=Starting height
Falcon_Bp_RobotSingleForkBp_Input_station_description=Target site for navigation
Falcon_Bp_RobotSingleForkBp_Input_station_label=Target site
Falcon_Bp_RobotSingleForkBp_Output_rbkResult_description=Response result after execution
Falcon_Bp_RobotSingleForkBp_Output_rbkResult_label=Rbk request result
Falcon_Bp_RobotSingleForkBp_description=Operating forklift mechanism
Falcon_Bp_RobotSingleForkBp_label=Fork/drop off
Falcon_Bp_RobotSingleGetAllStatusBp_Output_status_description=All states of the robot, data type is JsonObject.
Falcon_Bp_RobotSingleGetAllStatusBp_Output_status_label=Status
Falcon_Bp_RobotSingleGetAllStatusBp_description=Get the original report of the robot 1100 interface
Falcon_Bp_RobotSingleGetAllStatusBp_label=Get all the status of the robot
Falcon_Bp_RobotSingleGetOneStatusBp_Input_status_Option_batteryLevel=Battery level
Falcon_Bp_RobotSingleGetOneStatusBp_Input_status_Option_charging=State of charge
Falcon_Bp_RobotSingleGetOneStatusBp_Input_status_Option_currentStation=Current site
Falcon_Bp_RobotSingleGetOneStatusBp_Input_status_description=Status of the robot, e.g. battery level, current point
Falcon_Bp_RobotSingleGetOneStatusBp_Input_status_label=Status
Falcon_Bp_RobotSingleGetOneStatusBp_Output_status_description=Specified state of the robot
Falcon_Bp_RobotSingleGetOneStatusBp_Output_status_label=Status
Falcon_Bp_RobotSingleGetOneStatusBp_description=Return the specified state of the robot according to the input parameters
Falcon_Bp_RobotSingleGetOneStatusBp_label=Get the specified status of the robot
Falcon_Bp_RobotSingleJackingBp_Input_jackHeight_description=The height at which the jacking mechanism is raised, default value 0.010 (unit: m).
Falcon_Bp_RobotSingleJackingBp_Input_jackHeight_label=Jacking height
Falcon_Bp_RobotSingleJackingBp_Input_operation_Option_JackLoad=JackLoad
Falcon_Bp_RobotSingleJackingBp_Input_operation_Option_JackUnload=Put down (JackUnload)
Falcon_Bp_RobotSingleJackingBp_Input_operation_description=Operable action of jacking mechanism
Falcon_Bp_RobotSingleJackingBp_Input_operation_label=Action
Falcon_Bp_RobotSingleJackingBp_Input_recfile_description=When the robot performs specific actions, this file is needed for identification and comparison
Falcon_Bp_RobotSingleJackingBp_Input_recfile_label=Identification file
Falcon_Bp_RobotSingleJackingBp_Input_reqId_description=Identity when the component is executed, optional
Falcon_Bp_RobotSingleJackingBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleJackingBp_Input_station_description=Target site for navigation
Falcon_Bp_RobotSingleJackingBp_Input_station_label=Target site
Falcon_Bp_RobotSingleJackingBp_Input_useDownPgv_description=Use the downward PGV
Falcon_Bp_RobotSingleJackingBp_Input_useDownPgv_label=Use the downward PGV
Falcon_Bp_RobotSingleJackingBp_Input_usePgv_description=Use Upview PGV
Falcon_Bp_RobotSingleJackingBp_Input_usePgv_label=Use Upview PGV
Falcon_Bp_RobotSingleJackingBp_Output_rbkResult_description=Response result after execution
Falcon_Bp_RobotSingleJackingBp_Output_rbkResult_label=Rbk request result
Falcon_Bp_RobotSingleJackingBp_description=Operating jacking mechanism
Falcon_Bp_RobotSingleJackingBp_label=Jacking
Falcon_Bp_RobotSingleNavigationBp_Input_reqId_description=Identity when the component is executed, optional
Falcon_Bp_RobotSingleNavigationBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleNavigationBp_Input_station_description=Target site for navigation
Falcon_Bp_RobotSingleNavigationBp_Input_station_label=Target site
Falcon_Bp_RobotSingleNavigationBp_Output_rbkResult_description=Response result after execution
Falcon_Bp_RobotSingleNavigationBp_Output_rbkResult_label=Rbk request result
Falcon_Bp_RobotSingleNavigationBp_description=Navigate the robot to the target site
Falcon_Bp_RobotSingleNavigationBp_label=Path navigation
Falcon_Bp_RobotSinglePlayAudioBp_Input_audioFilename_description=Used to specify the audio file to be played by the robot
Falcon_Bp_RobotSinglePlayAudioBp_Input_audioFilename_label=Audio file name
Falcon_Bp_RobotSinglePlayAudioBp_Input_loop_description=Check Loop Play to play the audio repeatedly, otherwise it will only play once
Falcon_Bp_RobotSinglePlayAudioBp_Input_loop_label=Loop play
Falcon_Bp_RobotSinglePlayAudioBp_Input_reqId_description=Identity when the component is executed, optional
Falcon_Bp_RobotSinglePlayAudioBp_Input_reqId_label=ID
Falcon_Bp_RobotSinglePlayAudioBp_Input_station_description=Target site for navigation
Falcon_Bp_RobotSinglePlayAudioBp_Input_station_label=Target site
Falcon_Bp_RobotSinglePlayAudioBp_Output_rbkResult_description=Response result after execution
Falcon_Bp_RobotSinglePlayAudioBp_Output_rbkResult_label=Rbk request result
Falcon_Bp_RobotSinglePlayAudioBp_description=Control the robot to play audio
Falcon_Bp_RobotSinglePlayAudioBp_label=Play audio
Falcon_Bp_RobotSingleRollerBp_Input_direction_Option_back=After
Falcon_Bp_RobotSingleRollerBp_Input_direction_Option_front=Front
Falcon_Bp_RobotSingleRollerBp_Input_direction_Option_left=Left
Falcon_Bp_RobotSingleRollerBp_Input_direction_Option_right=Right
Falcon_Bp_RobotSingleRollerBp_Input_direction_description=Rolling direction of the drum mechanism
Falcon_Bp_RobotSingleRollerBp_Input_direction_label=Direction
Falcon_Bp_RobotSingleRollerBp_Input_operation_Option_RollerLoad=Roller Unload
Falcon_Bp_RobotSingleRollerBp_Input_operation_Option_RollerUnload=RollerLoad
Falcon_Bp_RobotSingleRollerBp_Input_operation_description=Operable action of drum mechanism
Falcon_Bp_RobotSingleRollerBp_Input_operation_label=Action
Falcon_Bp_RobotSingleRollerBp_Input_reqId_description=Identity when the component is executed, optional
Falcon_Bp_RobotSingleRollerBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleRollerBp_Input_station_description=Target site for navigation
Falcon_Bp_RobotSingleRollerBp_Input_station_label=Target site
Falcon_Bp_RobotSingleRollerBp_Output_rbkResult_description=Response result after execution
Falcon_Bp_RobotSingleRollerBp_Output_rbkResult_label=Rbk request result
Falcon_Bp_RobotSingleRollerBp_description=Operating drum mechanism
Falcon_Bp_RobotSingleRollerBp_label=Roller
Falcon_Bp_RobotSingleRotateBp_Input_angle_description=Control the angle of robot rotation
Falcon_Bp_RobotSingleRotateBp_Input_angle_label=Rotation angle (°)
Falcon_Bp_RobotSingleRotateBp_Input_mode_description=The mileage mode moves according to the mileage, and the positioning mode requires precise positioning. If the default is set to the mileage mode
Falcon_Bp_RobotSingleRotateBp_Input_mode_label=Mode
Falcon_Bp_RobotSingleRotateBp_Input_reqId_description=Identity when the component is executed, optional
Falcon_Bp_RobotSingleRotateBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleRotateBp_Input_vw_description=Control the speed of robot rotation, default value 45.0 (°/s)
Falcon_Bp_RobotSingleRotateBp_Input_vw_label=Rotational angular velocity (°/s)
Falcon_Bp_RobotSingleRotateBp_Output_rbkResult_description=Response result after execution
Falcon_Bp_RobotSingleRotateBp_Output_rbkResult_label=Rbk request result
Falcon_Bp_RobotSingleRotateBp_description=Control the robot to rotate in place
Falcon_Bp_RobotSingleRotateBp_label=Rotate
Falcon_Bp_RobotSingleSetDOBp_Input_id_description=Specify the number of DO
Falcon_Bp_RobotSingleSetDOBp_Input_id_label=DO number
Falcon_Bp_RobotSingleSetDOBp_Input_status_Option_status_0=0 (disabled)
Falcon_Bp_RobotSingleSetDOBp_Input_status_Option_status_1=1 (enabled)
Falcon_Bp_RobotSingleSetDOBp_Input_status_description=Modify the state of the specified DO to this state
Falcon_Bp_RobotSingleSetDOBp_Input_status_label=Target state
Falcon_Bp_RobotSingleSetDOBp_Output_rbkResult_description=Response result after execution
Falcon_Bp_RobotSingleSetDOBp_Output_rbkResult_label=Rbk request result
Falcon_Bp_RobotSingleSetDOBp_description=Used to modify the status of a specified DO
Falcon_Bp_RobotSingleSetDOBp_label=Set DO
Falcon_Bp_RobotSingleStopAudioBp_Input_reqId_description=Identity when the component is executed, optional
Falcon_Bp_RobotSingleStopAudioBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleStopAudioBp_Input_station_description=Target site for navigation
Falcon_Bp_RobotSingleStopAudioBp_Input_station_label=Target site
Falcon_Bp_RobotSingleStopAudioBp_Output_rbkResult_description=Response result after execution
Falcon_Bp_RobotSingleStopAudioBp_Output_rbkResult_label=Rbk request result
Falcon_Bp_RobotSingleStopAudioBp_description=Control the robot to stop playing audio
Falcon_Bp_RobotSingleStopAudioBp_label=Stop playing
Falcon_Bp_RobotSingleTranslationBp_Input_dist_description=Used to limit the distance of robot movement
Falcon_Bp_RobotSingleTranslationBp_Input_dist_label=Linear motion distance (unit: m)
Falcon_Bp_RobotSingleTranslationBp_Input_mode_Option_0=Mileage mode
Falcon_Bp_RobotSingleTranslationBp_Input_mode_Option_1=Positioning mode
Falcon_Bp_RobotSingleTranslationBp_Input_mode_description=The mileage mode moves according to the mileage, and the positioning mode requires precise positioning. If the default is set to the mileage mode
Falcon_Bp_RobotSingleTranslationBp_Input_mode_label=Mode
Falcon_Bp_RobotSingleTranslationBp_Input_reqId_description=Identity when the component is executed, optional
Falcon_Bp_RobotSingleTranslationBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleTranslationBp_Input_vx_description=Control the speed of forward and backward movement, negative value is backward, positive value is forward, default value is 0.5 (unit: m/s)
Falcon_Bp_RobotSingleTranslationBp_Input_vx_label=Forward speed (vx, unit: m/s)
Falcon_Bp_RobotSingleTranslationBp_Input_vy_description=Control the speed in the left and right directions, only useful for omnidirectional vehicles, all other models are 0, default value is 0.0 (unit: m/s)
Falcon_Bp_RobotSingleTranslationBp_Input_vy_label=Traverse speed (vy, unit: m/s)
Falcon_Bp_RobotSingleTranslationBp_Output_rbkResult_description=Response result after execution
Falcon_Bp_RobotSingleTranslationBp_Output_rbkResult_label=Rbk request result
Falcon_Bp_RobotSingleTranslationBp_description=Control the robot to move freely
Falcon_Bp_RobotSingleTranslationBp_label=Translation
Falcon_Bp_RobotSingleUserDefinedBp_Input_apiNo_description=Instruction number, default is 3051.
Falcon_Bp_RobotSingleUserDefinedBp_Input_apiNo_label=API number
Falcon_Bp_RobotSingleUserDefinedBp_Input_reqId_description=Identity when the component is executed, optional
Falcon_Bp_RobotSingleUserDefinedBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleUserDefinedBp_Input_userDefinedAction_description=Custom action instructions (in the form of JSON strings)
Falcon_Bp_RobotSingleUserDefinedBp_Input_userDefinedAction_label=Action
Falcon_Bp_RobotSingleUserDefinedBp_Output_rbkResult_description=Response result after execution
Falcon_Bp_RobotSingleUserDefinedBp_Output_rbkResult_label=Rbk request result
Falcon_Bp_RobotSingleUserDefinedBp_description=Let the robot perform a custom action
Falcon_Bp_RobotSingleUserDefinedBp_label=Custom actions
Falcon_Bp_RobotSingleWaitDIBp_Input_id_description=Number of the specified DI
Falcon_Bp_RobotSingleWaitDIBp_Input_id_label=DI number
Falcon_Bp_RobotSingleWaitDIBp_Input_status_Option_status_0=0 (disabled)
Falcon_Bp_RobotSingleWaitDIBp_Input_status_Option_status_1=1 (enabled)
Falcon_Bp_RobotSingleWaitDIBp_Input_status_description=When the state of DI is the expected state, it indicates that DI has been triggered.
Falcon_Bp_RobotSingleWaitDIBp_Input_status_label=Desired state
Falcon_Bp_RobotSingleWaitDIBp_Input_timeout_description=Timeout time to wait for DI to be triggered. If not filled, wait forever.
Falcon_Bp_RobotSingleWaitDIBp_Input_timeout_label=Timeout time (s)
Falcon_Bp_RobotSingleWaitDIBp_Output_rbkResult_description=Response result after execution
Falcon_Bp_RobotSingleWaitDIBp_Output_rbkResult_label=Rbk request result
Falcon_Bp_RobotSingleWaitDIBp_description=Wait for the state of DI to change to the expected state.
Falcon_Bp_RobotSingleWaitDIBp_label=Waiting for DI to trigger
Falcon_Bp_RobotWaitDIBp_Input_id_description=Number of the specified DI
Falcon_Bp_RobotWaitDIBp_Input_id_label=DI number
Falcon_Bp_RobotWaitDIBp_Input_status_description=When the state of DI is the expected state, it indicates that DI has been triggered.
Falcon_Bp_RobotWaitDIBp_Input_status_label=Desired state
Falcon_Bp_RobotWaitDIBp_Input_timeOut_description=Timeout time to wait for DI to be triggered. If not filled, wait forever.
Falcon_Bp_RobotWaitDIBp_Input_timeOut_label=Timeout time (s)
Falcon_Bp_RobotWaitDIBp_Input_tomId_description=Scene name of "Second Generation Scheduling" on the "Robot Application" page
Falcon_Bp_RobotWaitDIBp_Input_tomId_label=Dispatch ID
Falcon_Bp_RobotWaitDIBp_Input_vehicle_description=Specify a bot
Falcon_Bp_RobotWaitDIBp_Input_vehicle_label=Specify a bot
Falcon_Bp_RobotWaitDIBp_Output_result_description=Response result after execution
Falcon_Bp_RobotWaitDIBp_Output_result_label=Waiting for the results
Falcon_Bp_RobotWaitDIBp_description=Waiting for the state of DI to change to the expected state
Falcon_Bp_RobotWaitDIBp_label=Waiting for DI to trigger

[fleet.error]
T00010001=Path planning parameter validation failed
T00010002=Route planning, according to the starting point route code {0}, no route was found. Please check whether this route code exists on the current robot group {1} map
T00010003=Path planning, start or end not found, start code {0}, end code {1}, robot group {2}
T00010004=Path planning, path not found for {0}
T00010005=Path planning, robot lock acquisition timed out
T00010006=Path planning, robot {0} parking timeout, please check if parking lock resource can be applied for
T00010007=Path planning, robot {0} carries container {1}, but size information for the container configuration cannot be found
T00010010=Path planning, {0} planning succeeded
T00020010=Failed to apply for space resources online
errAddStepWhenSealed=Waybill number {0} has been sealed and cannot be added further
errAwaitMove=Waiting for navigation to complete, but navigation failed, specific reason: {0}
errChangeTrafficButOrders=Error: There are {0} unfinished waybills in the scene, and the traffic control policy cannot be modified. Please wait for the waybill to complete or cancel.
errCreateStoOrderExists=Waybill already exists = {0}
errDirectRobotOrderFailed=Direct waybill failed, waybill number = {0}
errDuplicatedSceneName=Scene name {0} already exists, please enter another scene name.
errEmptySceneName=The scene name cannot be empty. Please enter a valid scene name.
errFailedToFetchMapFromRobot=Failed to get map from bot {0}
errFetchChassisMode=Failed to get chassis drive type
errFetchCurrentMapFail=Failed to get the current map of the robot, robot = {0}, specific reason = {1}
errFetchMapFail=Read bot map failed, bot = {0}, specific reason = {1}
errFleetNoSuchSceneByName=No such scene: {0}
errGroupMapNoPointOrBin=Machine group < {0} > no point or bin {1} in map
errGwNoLocalRobot=Gateway has no robot {0}
errMotorNotFound=Failed to read motor information from model file.
errMotorParamTypeUnsupported=The {1} of the model file {0} uses an unsupported parameter type {2}, which is supported by arrayParam and comboParam.
errMotorPropNotFound=The {1} of {0} in the model file has no attribute {2}.
errMotorTypeNotFound=The {0} of the model file has no attribute classification of type {1}.
errMove3051=Failed to send 3051 path navigation: {0}
errMove3066=Failed to send 3066 path navigation: {0}
errMoveRobotFromToStation=Robot {0} is currently in {1}, please move it back to the starting point {2} or ending point {3} and try again
errMustGetRobotGroupByRobot=Group for robot {1} not found in scene {0}
errNoAnyRobot=There are no robots
errNoAreaById=Area not found, id = {0}
errNoDefaultMap=Unable to find default map (first region, first model)
errNoFirstLightScene=There are no optical communication scenarios
errNoLocalRobot=Cannot find local robot {0}
errNoOrderById=Waybill {0} not found
errNoProperty=No such attribute '{0}'
errNoRbkClient=Client communicating with robot is not initialized
errNoReportApplyUrl=No reporting address specified
errNoRobot=No such robot "{0}"
errNoRobotGroupId=Cannot find robot group {0}
errNoRobots=Robot not found: {0}
errNoSceneBasicFile=Cannot find scene base file
errNoSceneByOrderId=Cannot find the scene corresponding to waybill number {0}
errNoSuchBinOrLocation=Cannot find point or storage bin named {0}
errNotRobotInTom=There is no robot {1} in scheduling {0}.
errOnlyOneSingleAppCanBeEnabled=The system can only activate one bicycle application
errOrderNoSceneId=Waybill {0} has no corresponding scene
errPositionToNoSite=No stations near location ({0}, {1})
errQuery3066BadTaskId=Query 3066 result, task id does not match, expected {0}, actual {1}
errRbkNotOk=RBK error. Error code = {0}. Message = {1}
errRbkResErr=Rbk request failed, ID: {0}, Type: {1}, Reason: {2}
errRbkResultKindConnectFail=Rbk connection failed
errRecAndForkLoadOverHeight=The input pre-pick height {0} exceeds the maximum lift height {1} of the fork.
errRemoveAreaHasRobots=There is a robot in area {0}, which cannot be deleted or disabled
errRemoveAreaHasRunningOrders=There is a running waybill in area {0}, which cannot be deleted or disabled
errRequestControl=Request for control failed, bot = {0}
errRetryButOrderNotFailed=Retry, but the order status is not failed
errRobotAppNoControlPower=No control
errRobotAppNoScene=Scene not configured
errRobotAppNoSceneById=No such scene: {0}
errRobotAppNoSceneByName=No such scene: {0}
errRobotAppNoSingleScene=Scene {0} is not a bicycle application.
errRobotAppSceneDisabled=Scene {0} is disabled
errRobotBadGroup=Error: Robot {0} must belong to a robot group (or the robot group to which it belongs has been deleted).
errRobotExecutingTask=The robot is performing navigation tasks.
errRobotGroupDisabled=Robot group {0} is disabled
errRobotHasSimpleOrderCurrent=Robot {0} is still executing waybill {1} and cannot receive new waybills
errRobotMoveNoStart=Cannot find a suitable starting point for robot {0} to execute the task
errRobotNameDuplicated=Error: Robot name cannot be duplicated: {0}
errRobotNavNoId=Path navigation request error, destination parameter'id 'is required.
errRobotNavNoSourceId=Path navigation request error, need starting point parameter 'source_id'
errRobotNoAnyScene=There are currently no scenes
errRobotNoConnector=No connector with robot '{0}'
errRobotNoCurrentStation=Robot {0} is not currently on a site
errRobotNoPosition=Unable to obtain the location of the robot "{0}", please confirm that the robot is at point
errRobotOffline=Robot "{0}" is not online or lost contact
errRobotUpdateBusy=The robot is working and cannot be updated
errStoHikNotSupported=Bicycle waybills do not support Hik robots
errStoNoRobot=Failed to create waybill {1}, no bot {0} in gateway
errStoRetryFailedBadCurrentStatus=The current status of waybill {0} is not failed but {1}.
errStoRetryFailedNoCurrent=The robot currently has no waybill.
errTargetMotorNotFound=There is no motor named {0} in the model file.
errTomBlockStopped=Scheduling block {0}: {1} stopped, please handle manually, error message {2}
errTomDisconnected=Scheduling server does not exist, or connection to it failed: {0}
errTomOrderAwaitNotFound=Dispatch waybill "{0}" not found after sending
errTomOrderKeyRouteMissing=At least one critical location must be specified
errTomOrderNoVehicle=Waybill '{0}' not dispatched robot (Waybill status: {1})
errTomOrderNoVehicle2=Waybill "{0}" Unassigned Robot
errUnsupportedFetchMap=The following connection method is not supported to obtain the map: {0}
errUnsupportedFetchMap2=Obtaining maps in this scenario is not supported
errorLocationEmpty=Location cannot be empty
errorTomOrderIdEmpty=Dispatch waybill number cannot be empty
warningNotFindStartPoint=The current position of the robot is neither on the point nor on the line, and the distance from the point and line exceeds the allowable range {0} (m).

[stats]
StatsLabel_FalconTaskCreate=Number of Falcon Mission Records Generated
StatsLabel_FalconTaskDone=Number of completed Falcon missions recorded
StatsLabel_FalconTaskError=Abnormal number of Falcon mission records
StatsLabel_QsInboundInvQty=Inventory quantity
StatsLabel_QsInboundOrderCount=Inventory order
StatsLabel_QsOutboundInvQty=Outbound quantity
StatsLabel_QsOutboundOrderCount=Outbound order
StatsLabel_QsStatsInvSummary=Total ending stock quantity
StatsLabel_QsStatsMaterialCount=Number of inventory items at the end of the period
StatsLabel_RobotChargeDuration=Charging duration
StatsLabel_RobotChargeTimes=Number of charging
StatsLabel_RobotEmptyDuration=No-load duration
StatsLabel_RobotErrorDuration=Duration of failure
StatsLabel_RobotErrorTimes=Number of failures
StatsLabel_RobotFailureRate=Failure rate
StatsLabel_RobotIdleDuration=Idle duration
StatsLabel_RobotIdleRate=Idle rate
StatsLabel_RobotLoadDuration=Load duration
StatsLabel_RobotMileage=New mileage
StatsLabel_RobotNoLoadRate=Unloaded rate
StatsLabel_RobotOnlineDuration=Online duration
StatsLabel_RobotWorkDuration=Working hours
StatsLabel_RobotWorkTimes=Number of jobs
StatsLabel_period=Cycle
StatsLabel_subject=Subjects
StatsLabel_target=Object

[entity]
entity.DemoEntity.fields.componentTableField.tip=Component Table Component Table Component Table Component Table Component Table Component Table Component Table
entity.DemoEntity.fields.fileField.tip=Upload file Upload file Upload file Upload file Upload file Upload file Upload file Upload file
entity.DemoEntity.fields.floatField.tip=Input instructions Input instructions Input instructions Input instructions Input instructions Input instructions
entity.DemoEntity.fields.id.tip=Input instructions
entity.DemoEntity.fields.imageField.tip=Upload file Upload file Upload file Upload file Upload file Upload file
entity.DemoEntity.fields.stringField.tip=Input instructions

[entity.i18n]

[qs]
Falcon_BlockGroup_QuickStoreBc=QS container storage bin
Falcon_BlockGroup_QuickStoreBin=QS storage bin
Falcon_BlockGroup_QuickStoreContainer=QS container
Falcon_BlockGroup_QuickStoreInv=QS Inventory
Falcon_Bp_QSFindEmptyContainerBp_Input_bzDesc_description=Business description.
Falcon_Bp_QSFindEmptyContainerBp_Input_bzDesc_label=BzDesc
Falcon_Bp_QSFindEmptyContainerBp_Input_bzMark_description=Business mark.
Falcon_Bp_QSFindEmptyContainerBp_Input_bzMark_label=BzMark
Falcon_Bp_QSFindEmptyContainerBp_Input_districtIds_description=Storage area number. Please enter the storage area number recorded by M4 according to your requirements.
Falcon_Bp_QSFindEmptyContainerBp_Input_districtIds_label=Storage area Id
Falcon_Bp_QSFindEmptyContainerBp_Input_groupPriority_description=Group priority.
Falcon_Bp_QSFindEmptyContainerBp_Input_groupPriority_label=Group Priority
Falcon_Bp_QSFindEmptyContainerBp_Input_priority_description=Priority.
Falcon_Bp_QSFindEmptyContainerBp_Input_priority_label=Priority
Falcon_Bp_QSFindEmptyContainerBp_Input_sort_description=Sorting method for storage bins in the target library area.
Falcon_Bp_QSFindEmptyContainerBp_Input_sort_label=Sorting
Falcon_Bp_QSFindEmptyContainerBp_Input_timeout_description=Waiting timeout duration.
Falcon_Bp_QSFindEmptyContainerBp_Input_timeout_label=Timeout
Falcon_Bp_QSFindEmptyContainerBp_Output_binId_description=The number of the storage bin where the target empty container is stored.
Falcon_Bp_QSFindEmptyContainerBp_Output_binId_label=Storage bin number
Falcon_Bp_QSFindEmptyContainerBp_Output_containerId_description=The number of the empty container found.
Falcon_Bp_QSFindEmptyContainerBp_Output_containerId_label=Container number
Falcon_Bp_QSFindEmptyContainerBp_Output_found_description=True: found; false: not found.
Falcon_Bp_QSFindEmptyContainerBp_Output_found_label=Did you find the empty container?
Falcon_Bp_QSFindEmptyContainerBp_description=According to the desired rules, find empty containers from the target storage area and lock this container.
Falcon_Bp_QSFindEmptyContainerBp_label=Find empty container
Falcon_Bp_QsBindBinContainerBp_Input_binId_description=Storage bin ID
Falcon_Bp_QsBindBinContainerBp_Input_binId_label=Storage bin number
Falcon_Bp_QsBindBinContainerBp_Input_containerId_description=Container number
Falcon_Bp_QsBindBinContainerBp_Input_containerId_label=Container number
Falcon_Bp_QsBindBinContainerBp_description=Bind storage bin to container
Falcon_Bp_QsBindBinContainerBp_label=QS binding storage bin container
Falcon_Bp_QsCreateInvFromOrderBp_Input_bin_description=Storage bin
Falcon_Bp_QsCreateInvFromOrderBp_Input_bin_label=Storage bin
Falcon_Bp_QsCreateInvFromOrderBp_Input_container_description=Container
Falcon_Bp_QsCreateInvFromOrderBp_Input_container_label=Container
Falcon_Bp_QsCreateInvFromOrderBp_Input_copyFields_description=Multiple fields are separated by "," and these fields and corresponding data will be copied to the inventory information.
Falcon_Bp_QsCreateInvFromOrderBp_Input_copyFields_label=Copy field
Falcon_Bp_QsCreateInvFromOrderBp_Input_lotNoFormat_label=Storage bin numbering format
Falcon_Bp_QsCreateInvFromOrderBp_Input_materialFieldName_description=Please enter the field name that represents the material on the documentation
Falcon_Bp_QsCreateInvFromOrderBp_Input_materialFieldName_label=Material field
Falcon_Bp_QsCreateInvFromOrderBp_Input_order_description=Please enter a documentation object
Falcon_Bp_QsCreateInvFromOrderBp_Input_order_label=Documentation
Falcon_Bp_QsCreateInvFromOrderBp_Input_qtyFieldName_description=Please enter the field name indicating the quantity on the documentation
Falcon_Bp_QsCreateInvFromOrderBp_Input_qtyFieldName_label=Quantity field
Falcon_Bp_QsCreateInvFromOrderBp_Input_state_description=There are three default storage states: Received: received; Storing: in storage; Assigned: allocated.
Falcon_Bp_QsCreateInvFromOrderBp_Input_state_label=Stock status
Falcon_Bp_QsCreateInvFromOrderBp_description=Create inventory information from documentation
Falcon_Bp_QsCreateInvFromOrderBp_label=Create inventory from documentation
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_bin_description=Storage bin
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_bin_label=Storage bin
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_container_description=Container
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_container_label=Container
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_copyFields_description=Multiple fields are separated by "," and these fields and corresponding data will be copied to the inventory information.
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_copyFields_label=Copy the following fields from a single line
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_materialFieldName_description=Please enter the field name that represents the material on a single line.
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_materialFieldName_label=Material field name on a single line
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_order_description=Please enter a documentation object
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_order_label=Documentation object
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_qtyFieldName_description=Please enter the field name that represents the quantity on a single line.
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_qtyFieldName_label=Quantity field name on a single line
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_state_description=There are three default storage states: Received: received;Storing: in storage; Assigned: allocated.
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_state_label=Storage state
Falcon_Bp_QsCreateInvFromOrderLinesBp_description=Create inventory information based on single-line information
Falcon_Bp_QsCreateInvFromOrderLinesBp_label=Creating an inventory from a documentation line
Falcon_Bp_QsCreateTraceContainerBp_Input_containerType_description=Please enter the container type recorded by M4.
Falcon_Bp_QsCreateTraceContainerBp_Input_containerType_label=Container type
Falcon_Bp_QsCreateTraceContainerBp_Output_containerId_description=New container number.
Falcon_Bp_QsCreateTraceContainerBp_Output_containerId_label=Container number
Falcon_Bp_QsCreateTraceContainerBp_description=Create a tracking container.
Falcon_Bp_QsCreateTraceContainerBp_label=Create a tracking container
Falcon_Bp_QsFindEmptyBinBp_Input_cq_description=Conditions for finding empty storage bins
Falcon_Bp_QsFindEmptyBinBp_Input_cq_label=Conditions
Falcon_Bp_QsFindEmptyBinBp_Input_districtIds_label=List of storage areas
Falcon_Bp_QsFindEmptyBinBp_Input_keepTrying_description=When it is true, repeat the search for empty storage bin until successful. If it is false, only search once
Falcon_Bp_QsFindEmptyBinBp_Input_keepTrying_label=Repeated attempts lead to success
Falcon_Bp_QsFindEmptyBinBp_Input_sort_description=For example ["row", "column", "layer"]
Falcon_Bp_QsFindEmptyBinBp_Input_sort_label=Sorting rules
Falcon_Bp_QsFindEmptyBinBp_Output_binId_description=ID of the storage bin found
Falcon_Bp_QsFindEmptyBinBp_Output_binId_label=Storage bin ID
Falcon_Bp_QsFindEmptyBinBp_Output_found_description=True means found, false means not found
Falcon_Bp_QsFindEmptyBinBp_Output_found_label=Whether to find
Falcon_Bp_QsFindEmptyBinBp_description=Look for empty storage bins in storage areas
Falcon_Bp_QsFindEmptyBinBp_label=QS emptying storage bin
Falcon_Bp_QsGetBinContainerBp_Input_binId_description=Storage bin ID
Falcon_Bp_QsGetBinContainerBp_Input_binId_label=Storage bin number
Falcon_Bp_QsGetBinContainerBp_Output_binEmpty_description=Whether the container is empty, true is empty
Falcon_Bp_QsGetBinContainerBp_Output_binEmpty_label=Storage bin is empty
Falcon_Bp_QsGetBinContainerBp_Output_containerId_label=Container number
Falcon_Bp_QsGetBinContainerBp_description=Obtain the container on the storage bin, provided that the storage bin was previously bound to the container. If it cannot be obtained, the container number will return null.
Falcon_Bp_QsGetBinContainerBp_label=QS get containers on storage bin
Falcon_Bp_QsGetContainerBinBp_Input_containerId_label=Container number
Falcon_Bp_QsGetContainerBinBp_Output_binId_label=Storage bin number
Falcon_Bp_QsGetContainerBinBp_Output_found_description=Found or not, true means found
Falcon_Bp_QsGetContainerBinBp_Output_found_label=Find the storage bin
Falcon_Bp_QsGetContainerBinBp_description=Obtain the storage bin where the container is located. The premise of obtaining is that the storage bin was previously bound to the container. If it cannot be obtained, the storage bin ID returns null.
Falcon_Bp_QsGetContainerBinBp_label=QS gets the storage bin where the container is located
Falcon_Bp_QsGetContainerInvBp_Input_containerId_description=Please enter the container number recorded by M4
Falcon_Bp_QsGetContainerInvBp_Input_containerId_label=Container number
Falcon_Bp_QsGetContainerInvBp_Output_found_description=True: found; false: not found.
Falcon_Bp_QsGetContainerInvBp_Output_found_label=Whether to find
Falcon_Bp_QsGetContainerInvBp_Output_inv_description=Inventory list.
Falcon_Bp_QsGetContainerInvBp_Output_inv_label=Inventory list
Falcon_Bp_QsGetContainerInvBp_description=Obtain the corresponding inventory details based on the container number.
Falcon_Bp_QsGetContainerInvBp_label=Obtain inventory details in the container
Falcon_Bp_QsGetContainerTypeStoreDistrictsBp_Input_containerId_description=Please enter the number of the container recorded by M4.
Falcon_Bp_QsGetContainerTypeStoreDistrictsBp_Input_containerId_label=Container number
Falcon_Bp_QsGetContainerTypeStoreDistrictsBp_Output_storeDistricts_description=A collection of storage area names, an array.
Falcon_Bp_QsGetContainerTypeStoreDistrictsBp_Output_storeDistricts_label=Storage area
Falcon_Bp_QsGetContainerTypeStoreDistrictsBp_description=According to the container number, query the name of the storage area where such containers can be stored.
Falcon_Bp_QsGetContainerTypeStoreDistrictsBp_label=Storage area for querying the type of container
Falcon_Bp_QsKeepTryingLockBinBp_Input_binId_label=Storage bin ID
Falcon_Bp_QsKeepTryingLockBinBp_Input_bzDesc_description=bzDesc
Falcon_Bp_QsKeepTryingLockBinBp_Input_bzDesc_label=Reason for locking
Falcon_Bp_QsKeepTryingLockBinBp_label=QS repeatedly tried to lock the storage bin until successful
Falcon_Bp_QsKeepTryingLockEmptyBinBp_Input_binId_label=Storage bin ID
Falcon_Bp_QsKeepTryingLockEmptyBinBp_Input_bzDesc_description=bzDesc
Falcon_Bp_QsKeepTryingLockEmptyBinBp_Input_bzDesc_label=Reason for locking
Falcon_Bp_QsKeepTryingLockEmptyBinBp_label=QS lock after waiting storage bin becomes empty
Falcon_Bp_QsLockBinOnceBp_Input_binId_label=Storage bin ID
Falcon_Bp_QsLockBinOnceBp_Input_bzDesc_description=bzDesc
Falcon_Bp_QsLockBinOnceBp_Input_bzDesc_label=Reason for locking
Falcon_Bp_QsLockBinOnceBp_Input_notFoundToAborted_description=Whether the failure requires the task to be terminated, true is yes
Falcon_Bp_QsLockBinOnceBp_Input_notFoundToAborted_label=Unable to find termination task
Falcon_Bp_QsLockBinOnceBp_Input_notFoundToFault_description=Does the failure require the task to malfunction? True means yes.
Falcon_Bp_QsLockBinOnceBp_Input_notFoundToFault_label=Can't find let the task malfunction
Falcon_Bp_QsLockBinOnceBp_Output_ok_description=Success is true, failure is false
Falcon_Bp_QsLockBinOnceBp_Output_ok_label=Whether the lock was successful
Falcon_Bp_QsLockBinOnceBp_description=Attempt to lock an unlocked storage bin once, return true if successful, otherwise return false.
Falcon_Bp_QsLockBinOnceBp_label=QS lock unlocked storage bin
Falcon_Bp_QsMoveInvByContainerBp_Input_leafContainerId_description=Please enter the number of the innermost container.
Falcon_Bp_QsMoveInvByContainerBp_Input_leafContainerId_label=Innermost container
Falcon_Bp_QsMoveInvByContainerBp_Input_state_description=Modify the inventory status to the specified inventory status
Falcon_Bp_QsMoveInvByContainerBp_Input_state_label=Specify stock status
Falcon_Bp_QsMoveInvByContainerBp_Input_toBinId_description=The number of the storage bin where the innermost container will be placed.
Falcon_Bp_QsMoveInvByContainerBp_Input_toBinId_label=End storage bin
Falcon_Bp_QsMoveInvByContainerBp_description=Bind the innermost container information to another storage bin.
Falcon_Bp_QsMoveInvByContainerBp_label=Move inventory by container
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_binField_description=Please enter the field name that represents storage bin on a single line.
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_binField_label=Storage bin field
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_ev_description=Please enter documentation
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_ev_label=Documentation
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_materialField_description=Please enter the field name representing the material on a single line
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_materialField_label=Material field
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_outboundOrderIdField_label=Outbound order number
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_pickOrderIdField_label=Sorting order number
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_qtyField_description=Please enter the field name that represents the quantity on a single line.
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_qtyField_label=Quantity field
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_subContainerIdField_label=Lattice
Falcon_Bp_QsReduceBinInvFromOrderBp_description=Delete inventory from storage bin by documentation
Falcon_Bp_QsReduceBinInvFromOrderBp_label=Delete inventory from storage bin by documentation
Falcon_Bp_QsRemoveBzMarkBp_Input_binId_label=Storage bin number
Falcon_Bp_QsRemoveBzMarkBp_Input_requireLock_description=Whether to require the storage bin to be locked. If the storage bin is not locked, abnormal termination will occur
Falcon_Bp_QsRemoveBzMarkBp_Input_requireLock_label=The storage bin is required to be a locked storage bin
Falcon_Bp_QsRemoveBzMarkBp_description=Unlock designated storage bin
Falcon_Bp_QsRemoveBzMarkBp_label=QS unlock storage bin
Falcon_Bp_QsRemoveInvByContainerBp_Input_containerId_description=Please enter the container number recorded in M4.
Falcon_Bp_QsRemoveInvByContainerBp_Input_containerId_label=Container number
Falcon_Bp_QsRemoveInvByContainerBp_Output_count_description=The number of deleted inventory details.
Falcon_Bp_QsRemoveInvByContainerBp_Output_count_label=Detailed quantity
Falcon_Bp_QsRemoveInvByContainerBp_description=Delete the corresponding inventory details according to the container number.
Falcon_Bp_QsRemoveInvByContainerBp_label=Delete inventory details by container
Falcon_Bp_QsRemoveTraceContainerBp_Input_containerId_description=Please enter the tracking container number recorded by M4.
Falcon_Bp_QsRemoveTraceContainerBp_Input_containerId_label=Container number
Falcon_Bp_QsRemoveTraceContainerBp_description=Delete the tracking container.
Falcon_Bp_QsRemoveTraceContainerBp_label=Delete tracking container
Falcon_Bp_QsTakeOffContainerBp_Input_binId_label=Storage bin ID
Falcon_Bp_QsTakeOffContainerBp_Input_removingInv_label=Delete inventory in container
Falcon_Bp_QsTakeOffContainerBp_Input_unlockAfter_description=True is to set unoccupied and unlock the storage bin, false is to set unoccupied and not unlock the storage bin.
Falcon_Bp_QsTakeOffContainerBp_Input_unlockBin_label=Unlock
Falcon_Bp_QsTakeOffContainerBp_description=Remove containers from storage bins and mark them for business
Falcon_Bp_QsTakeOffContainerBp_label=QS empty storage bin
Falcon_Bp_QsUnbindBinContainerBp_Input_binId_description=Storage bin ID
Falcon_Bp_QsUnbindBinContainerBp_Input_binId_label=Storage bin number
Falcon_Bp_QsUnbindBinContainerBp_Input_containerId_label=Container number
Falcon_Bp_QsUnbindBinContainerBp_Input_removingInv_description=If true, delete the inventory details in the container
Falcon_Bp_QsUnbindBinContainerBp_Input_removingInv_label=Delete inventory
Falcon_Bp_QsUnbindBinContainerBp_Input_unlockBin_label=Unlock storage bin
Falcon_Bp_QsUnbindBinContainerBp_Input_unlockContainer_label=Unlock the container
Falcon_Bp_QsUnbindBinContainerBp_description=Unbind the storage bin from the container
Falcon_Bp_QsUnbindBinContainerBp_label=QS unbind storage bin container
errBinContainer1Container2=Expected container {1} on storage bin {0}, but actually has {2}
errContainerNoOnBin=Container {0} is not on storage bin {1}
errMoveContainerToBinOldContainer=To move container {0} to storage bin {1}, but there is already container {2} on storage bin
errNoBinInvByBin=Unable to find storage bin inventory, storage bin = {0}
errNoBinInvByContainer=Container inventory not found, container = {0}
errNoContainerByBin=Unable to find bound container, storage bin = {0}

[fleet.diagnosis]
FleetDiagnosis_RobotAlreadyParked=The robot has already arrived at the docking point
FleetDiagnosis_RobotAutoOrder=The robot is performing automatic waybills (such as charging and docking).
FleetDiagnosis_RobotBatteryLowButBusy=Although the battery level of the robot is low, it is executing business waybills and cannot be charged
FleetDiagnosis_RobotBusy=The robot is executing business waybills
FleetDiagnosis_RobotChargeOnly=Robot battery level is too low
FleetDiagnosis_RobotCharging=The robot is charging
FleetDiagnosis_RobotChargingNotEnoughTime=The robot has started charging, at least for a while, it cannot end immediately
FleetDiagnosis_RobotChargingNotFull=Robot is charging, but not at preset full battery level
FleetDiagnosis_RobotCmdFailed=Robot execution failure, please try again after failure
FleetDiagnosis_RobotCmdInterrupted=The robot is canceling the previous waybill, please wait a moment.
FleetDiagnosis_RobotDisabled=The robot is disabled, please enable it first.
FleetDiagnosis_RobotNoMoreBin=The robot has no more storage bins
FleetDiagnosis_RobotNotChargeNeed=The current battery level of the robot is greater than the rechargeable battery level and does not need to be charged
FleetDiagnosis_RobotNotChargeOnly=The current battery level of the robot is greater than the battery level of "must be charged" and does not require strong charging
FleetDiagnosis_RobotNotInterruptible=The robot is performing an uninterrupted task
FleetDiagnosis_RobotNotMaster=No control, please obtain control first
FleetDiagnosis_RobotOffDuty=The robot is set to not accept orders. Please set the robot to accept orders first.
FleetDiagnosis_RobotOffline=Robot disconnection
FleetDiagnosis_RobotToCharging=The robot is going to charge
FleetDiagnosis_RobotToPark=The robot is performing docking
FleetDiagnosis_RobotWaitIdleTimeoutToPark=The robot needs to be idle for a period of time before docking, and it has not timed out yet
FleetDiagnosis_TrafficNotReady=Please wait for the traffic control initialization to complete
i18n_entity.AgentUser.fields.appId.label=appId
i18n_entity.AgentUser.fields.appKey.label=appKey
i18n_entity.AgentUser.fields.btDisabled.label=Deactivated
i18n_entity.AgentUser.fields.createdBy.label=Founder
i18n_entity.AgentUser.fields.createdOn.label=Creation time
i18n_entity.AgentUser.fields.id.label=ID
i18n_entity.AgentUser.fields.modifiedBy.label=Last modifier
i18n_entity.AgentUser.fields.modifiedOn.label=Last modified time
i18n_entity.AgentUser.fields.remark.label=Remarks
i18n_entity.AgentUser.fields.version.label=Revised version
i18n_entity.AgentUser.group=Core
i18n_entity.AgentUser.label=Agency account
i18n_entity.ApiCallTrace.fields.apiType.inlineOptionBill.items.HTTP.label=HTTP
i18n_entity.ApiCallTrace.fields.apiType.label=Interface type
i18n_entity.ApiCallTrace.fields.costTime.label=Time consuming
i18n_entity.ApiCallTrace.fields.createdBy.label=Founder
i18n_entity.ApiCallTrace.fields.createdOn.label=Creation time
i18n_entity.ApiCallTrace.fields.httpMethod.inlineOptionBill.items.DELETE.label=DELETE
i18n_entity.ApiCallTrace.fields.httpMethod.inlineOptionBill.items.GET.label=GET
i18n_entity.ApiCallTrace.fields.httpMethod.inlineOptionBill.items.POST.label=POST
i18n_entity.ApiCallTrace.fields.httpMethod.inlineOptionBill.items.PUT.label=PUT
i18n_entity.ApiCallTrace.fields.httpMethod.label=HTTP methods
i18n_entity.ApiCallTrace.fields.httpPath.label=HTTP path
i18n_entity.ApiCallTrace.fields.httpUrl.label=Request URL
i18n_entity.ApiCallTrace.fields.id.label=ID
i18n_entity.ApiCallTrace.fields.modifiedBy.label=Last modifier
i18n_entity.ApiCallTrace.fields.modifiedOn.label=Modification time
i18n_entity.ApiCallTrace.fields.reqBody.label=Request body
i18n_entity.ApiCallTrace.fields.reqBodyOn.label=Request to record request body
i18n_entity.ApiCallTrace.fields.reqEndOn.label=Send response time
i18n_entity.ApiCallTrace.fields.reqIp.label=Request IP
i18n_entity.ApiCallTrace.fields.reqStartOn.label=Received request time
i18n_entity.ApiCallTrace.fields.reqUser.label=Requesting the user
i18n_entity.ApiCallTrace.fields.resBody.label=Response body
i18n_entity.ApiCallTrace.fields.resBodyOn.label=Request to record response body
i18n_entity.ApiCallTrace.fields.resCode.label=Response code
i18n_entity.ApiCallTrace.fields.version.label=Revised version
i18n_entity.ApiCallTrace.group=Core
i18n_entity.ApiCallTrace.label=Interface call record
i18n_entity.ApiCallTrace.pagesButtons.ListMain.buttons[0].label=Delete
i18n_entity.ApiCallTrace.pagesButtons.ListMain.buttons[1].label=Export
i18n_entity.ApiCallTrace.pagesButtons.ListMain.buttons[2].label=Configure
i18n_entity.BgTaskRecord.fields.args.label=Input parameters
i18n_entity.BgTaskRecord.fields.createdBy.label=Founder
i18n_entity.BgTaskRecord.fields.createdOn.label=Creation time
i18n_entity.BgTaskRecord.fields.fault.label=Mission failed
i18n_entity.BgTaskRecord.fields.faultMsg.label=Reasons for failure
i18n_entity.BgTaskRecord.fields.id.label=ID
i18n_entity.BgTaskRecord.fields.modifiedBy.label=Last modifier
i18n_entity.BgTaskRecord.fields.modifiedOn.label=Last modified time
i18n_entity.BgTaskRecord.fields.name.label=Task name
i18n_entity.BgTaskRecord.fields.paused.label=Has been suspended
i18n_entity.BgTaskRecord.fields.version.label=Revised version
i18n_entity.BgTaskRecord.group=Core
i18n_entity.BgTaskRecord.label=Background task record
i18n_entity.BgTaskRecord.pagesButtons.ListMain.buttons[0].label=Export
i18n_entity.BgTaskRecord.pagesButtons.ListMain.buttons[1].label=Continue
i18n_entity.BgTaskRecord.pagesButtons.ListMain.buttons[2].label=Suspend
i18n_entity.BgTaskRecord.pagesButtons.ListMain.buttons[3].label=Fault retry
i18n_entity.BgTaskRecord.pagesButtons.ListMain.buttons[4].label=Cancel
i18n_entity.BgTaskStepRecord.fields.createdBy.label=Founder
i18n_entity.BgTaskStepRecord.fields.createdOn.label=Creation time
i18n_entity.BgTaskStepRecord.fields.id.label=ID
i18n_entity.BgTaskStepRecord.fields.modifiedBy.label=Last modifier
i18n_entity.BgTaskStepRecord.fields.modifiedOn.label=Last modified time
i18n_entity.BgTaskStepRecord.fields.output.label=Implementation results
i18n_entity.BgTaskStepRecord.fields.taskId.label=Background Task ID
i18n_entity.BgTaskStepRecord.fields.version.label=Revised version
i18n_entity.BgTaskStepRecord.group=Core
i18n_entity.BgTaskStepRecord.label=Background task block record
i18n_entity.BgTaskStepRecord.pagesButtons.ListMain.buttons[0].label=Export
i18n_entity.CallEmptyContainerOrder.fields.createdBy.label=Founder
i18n_entity.CallEmptyContainerOrder.fields.createdOn.label=Creation time
i18n_entity.CallEmptyContainerOrder.fields.district.label=Storage area
i18n_entity.CallEmptyContainerOrder.fields.id.label=Order number
i18n_entity.CallEmptyContainerOrder.fields.modifiedBy.label=Last modifier
i18n_entity.CallEmptyContainerOrder.fields.modifiedOn.label=Last modified time
i18n_entity.CallEmptyContainerOrder.fields.num.label=Number
i18n_entity.CallEmptyContainerOrder.fields.version.label=Revised version
i18n_entity.CallEmptyContainerOrder.group=Warehouse
i18n_entity.CallEmptyContainerOrder.label=Called empty container
i18n_entity.CallEmptyContainerOrder.listCard.district.prefix=Storage area
i18n_entity.CallEmptyContainerOrder.listCard.num.prefix=Number
i18n_entity.CallEmptyContainerOrder.pagesButtons.Create.buttons[0].label=Submit
i18n_entity.ContainerTransportOrder.fields.atPort.label=At the library entrance
i18n_entity.ContainerTransportOrder.fields.container.label=Container
i18n_entity.ContainerTransportOrder.fields.createdBy.label=Founder
i18n_entity.ContainerTransportOrder.fields.createdOn.label=Creation time
i18n_entity.ContainerTransportOrder.fields.doneOn.label=Completion time
i18n_entity.ContainerTransportOrder.fields.errMsg.label=Cause of error
i18n_entity.ContainerTransportOrder.fields.expectedRobot.label=Specify a bot
i18n_entity.ContainerTransportOrder.fields.falconTaskDefId.label=Falcon Mission Template ID
i18n_entity.ContainerTransportOrder.fields.falconTaskDefLabel.label=Falcon Mission Template Name
i18n_entity.ContainerTransportOrder.fields.falconTaskId.label=Falcon mission number
i18n_entity.ContainerTransportOrder.fields.fromBin.label=Starting storage bin
i18n_entity.ContainerTransportOrder.fields.fromChannel.label=Starting point aisle
i18n_entity.ContainerTransportOrder.fields.id.label=ID
i18n_entity.ContainerTransportOrder.fields.kind.label=Type
i18n_entity.ContainerTransportOrder.fields.loaded.label=Picked up
i18n_entity.ContainerTransportOrder.fields.modifiedBy.label=Last modifier
i18n_entity.ContainerTransportOrder.fields.modifiedOn.label=Last modified time
i18n_entity.ContainerTransportOrder.fields.postProcessMark.label=Processing mark
i18n_entity.ContainerTransportOrder.fields.priority.label=Priority
i18n_entity.ContainerTransportOrder.fields.remark.label=Remarks
i18n_entity.ContainerTransportOrder.fields.robotName.label=Execution robot
i18n_entity.ContainerTransportOrder.fields.sourceOrderId.label=Related documentation
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Assigned.label=Sent a car
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Building.label=Not submitted
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Cancelled.label=Cancel
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Created.label=Submitted
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Done.label=Complete
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Failed.label=Failure
i18n_entity.ContainerTransportOrder.fields.status.label=Status
i18n_entity.ContainerTransportOrder.fields.toBin.label=End storage bin
i18n_entity.ContainerTransportOrder.fields.toChannel.label=End point aisle
i18n_entity.ContainerTransportOrder.fields.unloaded.label=Has dropped off
i18n_entity.ContainerTransportOrder.fields.version.label=Revised version
i18n_entity.ContainerTransportOrder.group=WCS
i18n_entity.ContainerTransportOrder.label=Container handling list
i18n_entity.ContainerTransportOrder.listCard.container.prefix=Container
i18n_entity.ContainerTransportOrder.listCard.doneOn.prefix=Complete
i18n_entity.ContainerTransportOrder.listCard.falconTaskId.prefix=Falcon mission
i18n_entity.ContainerTransportOrder.listCard.fromBin.suffix=--->
i18n_entity.ContainerTransportOrder.listCard.priority.prefix=Priority
i18n_entity.ContainerTransportOrder.listCard.robotName.prefix=Robot
i18n_entity.ContainerTransportOrder.listCard.sourceOrderId.prefix=Related documentation
i18n_entity.ContainerTransportOrder.listStats.items[0].label=Failure
i18n_entity.DemoComponent.fields.btLineNo.label=Line number
i18n_entity.DemoComponent.fields.btParentId.label=Owned order
i18n_entity.DemoComponent.fields.createdBy.label=Founder
i18n_entity.DemoComponent.fields.createdOn.label=Creation time
i18n_entity.DemoComponent.fields.floatValue.label=floatValue
i18n_entity.DemoComponent.fields.id.label=Number
i18n_entity.DemoComponent.fields.modifiedBy.label=Last modifier
i18n_entity.DemoComponent.fields.modifiedOn.label=Last modified time
i18n_entity.DemoComponent.fields.referenceField.label=Quote
i18n_entity.DemoComponent.fields.ro.label=Root tissue
i18n_entity.DemoComponent.fields.stringField.label=Text
i18n_entity.DemoComponent.fields.version.label=Version
i18n_entity.DemoComponent.group=Test
i18n_entity.DemoComponent.label=Test components
i18n_entity.DemoComponentTable.fields.btLineNo.label=Line number
i18n_entity.DemoComponentTable.fields.btParentId.label=Owned order
i18n_entity.DemoComponentTable.fields.createdBy.label=Founder
i18n_entity.DemoComponentTable.fields.createdOn.label=Creation time
i18n_entity.DemoComponentTable.fields.dateField.label=dateField
i18n_entity.DemoComponentTable.fields.id.label=Number
i18n_entity.DemoComponentTable.fields.intField.label=intField
i18n_entity.DemoComponentTable.fields.modifiedBy.label=Last modifier
i18n_entity.DemoComponentTable.fields.modifiedOn.label=Last modified time
i18n_entity.DemoComponentTable.fields.referenceField.label=Quote
i18n_entity.DemoComponentTable.fields.ro.label=Root tissue
i18n_entity.DemoComponentTable.fields.stringField.label=Text
i18n_entity.DemoComponentTable.fields.version.label=Version
i18n_entity.DemoComponentTable.group=Test
i18n_entity.DemoComponentTable.label=Test component table
i18n_entity.DemoEntity.fields.booleanField.label=Boolean
i18n_entity.DemoEntity.fields.booleanListField.label=Boolean multivalued
i18n_entity.DemoEntity.fields.checkList2Field.inlineOptionBill.items.v1.label=Option 1
i18n_entity.DemoEntity.fields.checkList2Field.inlineOptionBill.items.v2.label=Option 2
i18n_entity.DemoEntity.fields.checkList2Field.inlineOptionBill.items.v3.label=Option 3
i18n_entity.DemoEntity.fields.checkList2Field.inlineOptionBill.items.v4.label=Option 4
i18n_entity.DemoEntity.fields.checkList2Field.label=Option list (multiple choices)
i18n_entity.DemoEntity.fields.checkListField.inlineOptionBill.items.v1.label=Option 1
i18n_entity.DemoEntity.fields.checkListField.inlineOptionBill.items.v2.label=Option 2
i18n_entity.DemoEntity.fields.checkListField.inlineOptionBill.items.v3.label=Option 3
i18n_entity.DemoEntity.fields.checkListField.inlineOptionBill.items.v4.label=Option 4
i18n_entity.DemoEntity.fields.checkListField.label=Option list (single choice)
i18n_entity.DemoEntity.fields.componentField.label=Component
i18n_entity.DemoEntity.fields.componentListField.label=Component multivalued
i18n_entity.DemoEntity.fields.componentTableField.label=Component table
i18n_entity.DemoEntity.fields.createdBy.label=Founder
i18n_entity.DemoEntity.fields.createdOn.label=Creation time
i18n_entity.DemoEntity.fields.dateField.label=Date
i18n_entity.DemoEntity.fields.dateListField.label=Date multivalued
i18n_entity.DemoEntity.fields.dateTimeField.label=Date and time
i18n_entity.DemoEntity.fields.dateTimeListField.label=Date and time multiple values
i18n_entity.DemoEntity.fields.fileField.label=File
i18n_entity.DemoEntity.fields.fileListField.label=File multivalued
i18n_entity.DemoEntity.fields.floatField.label=Floating point number
i18n_entity.DemoEntity.fields.floatListField.label=Floating point multivalued
i18n_entity.DemoEntity.fields.id.label=Number
i18n_entity.DemoEntity.fields.imageField.label=Picture
i18n_entity.DemoEntity.fields.imageListField.label=Image multi-value
i18n_entity.DemoEntity.fields.intField.label=Integer
i18n_entity.DemoEntity.fields.intListField.label=Integer multivalued
i18n_entity.DemoEntity.fields.modifiedBy.label=Last modifier
i18n_entity.DemoEntity.fields.modifiedOn.label=Last modified time
i18n_entity.DemoEntity.fields.passwordField.label=Password input
i18n_entity.DemoEntity.fields.referenceField.label=Quote
i18n_entity.DemoEntity.fields.referenceListField.label=Reference multi-value
i18n_entity.DemoEntity.fields.ro.label=Root tissue
i18n_entity.DemoEntity.fields.selectField.inlineOptionBill.items.v1.label=Option 1
i18n_entity.DemoEntity.fields.selectField.inlineOptionBill.items.v2.label=Option 2
i18n_entity.DemoEntity.fields.selectField.inlineOptionBill.items.v3.label=Option 3
i18n_entity.DemoEntity.fields.selectField.inlineOptionBill.items.v4.label=Option 4
i18n_entity.DemoEntity.fields.selectField.label=Select input
i18n_entity.DemoEntity.fields.selectListField.inlineOptionBill.items.v1.label=Option 1
i18n_entity.DemoEntity.fields.selectListField.inlineOptionBill.items.v2.label=Option 2
i18n_entity.DemoEntity.fields.selectListField.inlineOptionBill.items.v3.label=Option 3
i18n_entity.DemoEntity.fields.selectListField.inlineOptionBill.items.v4.label=Option 4
i18n_entity.DemoEntity.fields.selectListField.label=Select input (multiple values)
i18n_entity.DemoEntity.fields.stringField.label=Text
i18n_entity.DemoEntity.fields.stringListField.label=Text multivalued
i18n_entity.DemoEntity.fields.textAreaField.label=Multiline text input
i18n_entity.DemoEntity.fields.textAreaListField.label=Multi-line text input (multiple values)
i18n_entity.DemoEntity.fields.version.label=Version
i18n_entity.DemoEntity.group=Test
i18n_entity.DemoEntity.label=Test entity
i18n_entity.DemoEntity2.fields.booleanField.label=Boolean
i18n_entity.DemoEntity2.fields.booleanListField.label=Boolean multivalued
i18n_entity.DemoEntity2.fields.checkList2Field.inlineOptionBill.items.v1.label=Option 1
i18n_entity.DemoEntity2.fields.checkList2Field.inlineOptionBill.items.v2.label=Option 2
i18n_entity.DemoEntity2.fields.checkList2Field.inlineOptionBill.items.v3.label=Option 3
i18n_entity.DemoEntity2.fields.checkList2Field.inlineOptionBill.items.v4.label=Option 4
i18n_entity.DemoEntity2.fields.checkList2Field.label=Option list (multiple choices)
i18n_entity.DemoEntity2.fields.checkListField.inlineOptionBill.items.v1.label=Option 1
i18n_entity.DemoEntity2.fields.checkListField.inlineOptionBill.items.v2.label=Option 2
i18n_entity.DemoEntity2.fields.checkListField.inlineOptionBill.items.v3.label=Option 3
i18n_entity.DemoEntity2.fields.checkListField.inlineOptionBill.items.v4.label=Option 4
i18n_entity.DemoEntity2.fields.checkListField.label=Option list (single choice)
i18n_entity.DemoEntity2.fields.componentField.label=Component
i18n_entity.DemoEntity2.fields.componentListField.label=Component multivalued
i18n_entity.DemoEntity2.fields.componentTableField.label=Component table
i18n_entity.DemoEntity2.fields.createdBy.label=Founder
i18n_entity.DemoEntity2.fields.createdOn.label=Creation time
i18n_entity.DemoEntity2.fields.dateField.label=Date
i18n_entity.DemoEntity2.fields.dateListField.label=Date multivalued
i18n_entity.DemoEntity2.fields.dateTimeField.label=Date and time
i18n_entity.DemoEntity2.fields.dateTimeListField.label=Date and time multiple values
i18n_entity.DemoEntity2.fields.fileField.label=File
i18n_entity.DemoEntity2.fields.fileListField.label=File multivalued
i18n_entity.DemoEntity2.fields.floatField.label=Floating point number
i18n_entity.DemoEntity2.fields.floatListField.label=Floating point multivalued
i18n_entity.DemoEntity2.fields.id.label=Number
i18n_entity.DemoEntity2.fields.imageField.label=Picture
i18n_entity.DemoEntity2.fields.imageListField.label=Image multi-value
i18n_entity.DemoEntity2.fields.intField.label=Integer
i18n_entity.DemoEntity2.fields.intListField.label=Integer multivalued
i18n_entity.DemoEntity2.fields.modifiedBy.label=Last modifier
i18n_entity.DemoEntity2.fields.modifiedOn.label=Last modified time
i18n_entity.DemoEntity2.fields.passwordField.label=Password input
i18n_entity.DemoEntity2.fields.referenceField.label=Quote
i18n_entity.DemoEntity2.fields.referenceListField.label=Reference multi-value
i18n_entity.DemoEntity2.fields.ro.label=Root tissue
i18n_entity.DemoEntity2.fields.selectField.inlineOptionBill.items.v1.label=Option 1
i18n_entity.DemoEntity2.fields.selectField.inlineOptionBill.items.v2.label=Option 2
i18n_entity.DemoEntity2.fields.selectField.inlineOptionBill.items.v3.label=Option 3
i18n_entity.DemoEntity2.fields.selectField.inlineOptionBill.items.v4.label=Option 4
i18n_entity.DemoEntity2.fields.selectField.label=Select input
i18n_entity.DemoEntity2.fields.selectListField.inlineOptionBill.items.v1.label=Option 1
i18n_entity.DemoEntity2.fields.selectListField.inlineOptionBill.items.v2.label=Option 2
i18n_entity.DemoEntity2.fields.selectListField.inlineOptionBill.items.v3.label=Option 3
i18n_entity.DemoEntity2.fields.selectListField.inlineOptionBill.items.v4.label=Option 4
i18n_entity.DemoEntity2.fields.selectListField.label=Select input (multiple values)
i18n_entity.DemoEntity2.fields.stringField.label=Text
i18n_entity.DemoEntity2.fields.stringListField.label=Text multivalued
i18n_entity.DemoEntity2.fields.textAreaField.label=Multiline text input
i18n_entity.DemoEntity2.fields.textAreaListField.label=Multi-line text input (multiple values)
i18n_entity.DemoEntity2.fields.version.label=Version
i18n_entity.DemoEntity2.group=Test
i18n_entity.DemoEntity2.label=Test Entity 2
i18n_entity.Department.fields.createdBy.label=Founder
i18n_entity.Department.fields.createdOn.label=Creation time
i18n_entity.Department.fields.disabled.label=Disable
i18n_entity.Department.fields.id.label=Number
i18n_entity.Department.fields.leafNode.label=Leaf department
i18n_entity.Department.fields.level.label=Hierarchy
i18n_entity.Department.fields.modifiedBy.label=Last modifier
i18n_entity.Department.fields.modifiedOn.label=Last modified time
i18n_entity.Department.fields.name.label=Name
i18n_entity.Department.fields.owner.label=Person in charge
i18n_entity.Department.fields.parentNode.label=Superior department
i18n_entity.Department.fields.ro.label=Root tissue
i18n_entity.Department.fields.rootNode.label=Top departments
i18n_entity.Department.fields.version.label=Version
i18n_entity.Department.group=User
i18n_entity.Department.label=Department
i18n_entity.Department.listCard.owner.prefix=Person in charge
i18n_entity.DirectRobotOrder.fields.createdBy.label=Founder
i18n_entity.DirectRobotOrder.fields.createdOn.label=Creation time
i18n_entity.DirectRobotOrder.fields.description.label=Description
i18n_entity.DirectRobotOrder.fields.doneOn.label=Completion time
i18n_entity.DirectRobotOrder.fields.errMsg.label=Cause of error
i18n_entity.DirectRobotOrder.fields.id.label=ID
i18n_entity.DirectRobotOrder.fields.modifiedBy.label=Last modifier
i18n_entity.DirectRobotOrder.fields.modifiedOn.label=Modification time
i18n_entity.DirectRobotOrder.fields.moves.label=Action
i18n_entity.DirectRobotOrder.fields.robotName.label=Robot name
i18n_entity.DirectRobotOrder.fields.seer3066.label=Specified path navigation
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Cancelled.label=Cancelled
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Created.label=Create
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Done.label=Completed
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Failed.label=Failure
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.ManualDone.label=Completed manually
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Sent.label=Already issued.
i18n_entity.DirectRobotOrder.fields.status.label=Status
i18n_entity.DirectRobotOrder.fields.status.listBatchButtons.buttons[0].label=Cancel
i18n_entity.DirectRobotOrder.fields.status.listBatchButtons.buttons[1].label=Completed manually
i18n_entity.DirectRobotOrder.fields.taskId.label=Belonging tasks
i18n_entity.DirectRobotOrder.fields.version.label=Revised version
i18n_entity.DirectRobotOrder.group=WCS
i18n_entity.DirectRobotOrder.label=Direct waybill
i18n_entity.DirectRobotOrder.listCard.createdOn.prefix=Create
i18n_entity.DirectRobotOrder.listCard.description.prefix=Description
i18n_entity.DirectRobotOrder.listCard.robotName.prefix=Robot
i18n_entity.DirectRobotOrder.listCard.taskId.prefix=Belonging tasks
i18n_entity.DirectRobotOrder.listStats.items[0].label=Create
i18n_entity.DirectRobotOrder.listStats.items[1].label=Issued
i18n_entity.DirectRobotOrder.listStats.items[2].label=Failure
i18n_entity.EmptyContainerStoreOrder.fields.container.label=Container
i18n_entity.EmptyContainerStoreOrder.fields.containerType.label=Container type
i18n_entity.EmptyContainerStoreOrder.fields.createdBy.label=Founder
i18n_entity.EmptyContainerStoreOrder.fields.createdOn.label=Creation time
i18n_entity.EmptyContainerStoreOrder.fields.id.label=ID
i18n_entity.EmptyContainerStoreOrder.fields.modifiedBy.label=Last modifier
i18n_entity.EmptyContainerStoreOrder.fields.modifiedOn.label=Last modified time
i18n_entity.EmptyContainerStoreOrder.fields.storeDistrict.label=Storage area
i18n_entity.EmptyContainerStoreOrder.fields.version.label=Revised version
i18n_entity.EmptyContainerStoreOrder.group=Warehouse
i18n_entity.EmptyContainerStoreOrder.label=New container put away
i18n_entity.EmptyContainerStoreOrder.listCard.container.prefix=Container
i18n_entity.EmptyContainerStoreOrder.listCard.storeDistrict.prefix=Storage area
i18n_entity.EmptyContainerStoreOrder.pagesButtons.ListMain.buttons[0].label=Create
i18n_entity.EmptyContainerStoreOrder.pagesButtons.ListMain.buttons[1].label=Delete
i18n_entity.EmptyContainerStoreOrder.pagesButtons.ListMain.buttons[2].label=Empty box
i18n_entity.EntityChangedRecord.fields.changeType.label=Modification type
i18n_entity.EntityChangedRecord.fields.createdBy.label=Founder
i18n_entity.EntityChangedRecord.fields.createdOn.label=Creation time
i18n_entity.EntityChangedRecord.fields.entityFields.label=Field list
i18n_entity.EntityChangedRecord.fields.entityId.label=Entity ID
i18n_entity.EntityChangedRecord.fields.entityName.label=Entity name
i18n_entity.EntityChangedRecord.fields.id.label=ID
i18n_entity.EntityChangedRecord.fields.modifiedBy.label=Last modifier
i18n_entity.EntityChangedRecord.fields.modifiedOn.label=Last modified time
i18n_entity.EntityChangedRecord.fields.version.label=Revised version
i18n_entity.EntityChangedRecord.group=Core
i18n_entity.EntityChangedRecord.label=Entity modification record
i18n_entity.EntityComment.fields.content.label=Content
i18n_entity.EntityComment.fields.createdBy.label=Founder
i18n_entity.EntityComment.fields.createdOn.label=Creation time
i18n_entity.EntityComment.fields.entityId.label=entityId
i18n_entity.EntityComment.fields.entityName.label=Entity
i18n_entity.EntityComment.fields.id.label=Number
i18n_entity.EntityComment.fields.modifiedBy.label=Last modifier
i18n_entity.EntityComment.fields.modifiedOn.label=Last modified time
i18n_entity.EntityComment.fields.ro.label=Enterprise
i18n_entity.EntityComment.fields.version.label=Version
i18n_entity.EntityComment.group=Core
i18n_entity.EntityComment.label=Entity comments
i18n_entity.EntitySyncRecord.fields.bzType.label=Business type
i18n_entity.EntitySyncRecord.fields.cost.label=Time consumption (milliseconds)
i18n_entity.EntitySyncRecord.fields.createdBy.label=Founder
i18n_entity.EntitySyncRecord.fields.createdCount.label=Number of new entities
i18n_entity.EntitySyncRecord.fields.createdOn.label=Creation time
i18n_entity.EntitySyncRecord.fields.deletedCount.label=Number of entities deleted
i18n_entity.EntitySyncRecord.fields.entityName.label=Entity name
i18n_entity.EntitySyncRecord.fields.faileReason.label=Reasons for failure
i18n_entity.EntitySyncRecord.fields.id.label=ID
i18n_entity.EntitySyncRecord.fields.modifiedBy.label=Last modifier
i18n_entity.EntitySyncRecord.fields.modifiedOn.label=Last modified time
i18n_entity.EntitySyncRecord.fields.oldCount.label=Number of entities before synchronization
i18n_entity.EntitySyncRecord.fields.ro.label=Enterprise
i18n_entity.EntitySyncRecord.fields.success.label=Success
i18n_entity.EntitySyncRecord.fields.syncCount.label=Number of synchronized entities
i18n_entity.EntitySyncRecord.fields.syncOn.label=Synchronization time
i18n_entity.EntitySyncRecord.fields.txId.label=Transaction ID
i18n_entity.EntitySyncRecord.fields.updatedCount.label=Update the number of entities
i18n_entity.EntitySyncRecord.fields.version.label=Version
i18n_entity.EntitySyncRecord.group=Core
i18n_entity.EntitySyncRecord.label=Entity synchronization record
i18n_entity.ExternalCallRecord.fields.createdBy.label=Founder
i18n_entity.ExternalCallRecord.fields.createdOn.label=Creation time
i18n_entity.ExternalCallRecord.fields.doneOn.label=Completion time
i18n_entity.ExternalCallRecord.fields.failedNum.label=Number of failures
i18n_entity.ExternalCallRecord.fields.failedReason.label=Reasons for failure
i18n_entity.ExternalCallRecord.fields.id.label=ID
i18n_entity.ExternalCallRecord.fields.modifiedBy.label=Last modifier
i18n_entity.ExternalCallRecord.fields.modifiedOn.label=Last modified time
i18n_entity.ExternalCallRecord.fields.okChecker.label=Success check method
i18n_entity.ExternalCallRecord.fields.options.label=Option
i18n_entity.ExternalCallRecord.fields.req.label=Request details
i18n_entity.ExternalCallRecord.fields.resBody.label=Response body
i18n_entity.ExternalCallRecord.fields.resCode.label=Response code
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Aborted.label=Has terminated
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Cancelled.label=Cancelled
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Done.label=Success
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Failed.label=Failure
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Init.label=Start
i18n_entity.ExternalCallRecord.fields.status.label=Status
i18n_entity.ExternalCallRecord.fields.url.label=URL
i18n_entity.ExternalCallRecord.fields.version.label=Revised version
i18n_entity.ExternalCallRecord.group=Core
i18n_entity.ExternalCallRecord.label=Third-party call record
i18n_entity.ExternalCallTrace.fields.createdBy.label=Founder
i18n_entity.ExternalCallTrace.fields.createdOn.label=Creation time
i18n_entity.ExternalCallTrace.fields.id.label=ID
i18n_entity.ExternalCallTrace.fields.ioError.label=Communication error
i18n_entity.ExternalCallTrace.fields.ioError.view.trueText=IO error
i18n_entity.ExternalCallTrace.fields.ioErrorMsg.label=Communication error cause
i18n_entity.ExternalCallTrace.fields.method.label=HTTP methods
i18n_entity.ExternalCallTrace.fields.modifiedBy.label=Last modifier
i18n_entity.ExternalCallTrace.fields.modifiedOn.label=Last modified time
i18n_entity.ExternalCallTrace.fields.reqBody.label=Request body
i18n_entity.ExternalCallTrace.fields.reqOn.label=Request time
i18n_entity.ExternalCallTrace.fields.resBody.label=Response body
i18n_entity.ExternalCallTrace.fields.resCode.label=Response code
i18n_entity.ExternalCallTrace.fields.resOn.label=Response time
i18n_entity.ExternalCallTrace.fields.url.label=URL
i18n_entity.ExternalCallTrace.fields.version.label=Revised version
i18n_entity.ExternalCallTrace.group=Core
i18n_entity.ExternalCallTrace.label=HTTP Client Log
i18n_entity.FailureRecord.fields.createdBy.label=Founder
i18n_entity.FailureRecord.fields.createdOn.label=Creation time
i18n_entity.FailureRecord.fields.desc.label=Description
i18n_entity.FailureRecord.fields.firstOn.label=Time of first occurrence
i18n_entity.FailureRecord.fields.id.label=ID
i18n_entity.FailureRecord.fields.kind.label=Category
i18n_entity.FailureRecord.fields.lastOn.label=Latest occurrence time
i18n_entity.FailureRecord.fields.level.inlineOptionBill.items.Error.label=Failure
i18n_entity.FailureRecord.fields.level.inlineOptionBill.items.Fatal.label=Serious
i18n_entity.FailureRecord.fields.level.inlineOptionBill.items.Warning.label=Warning
i18n_entity.FailureRecord.fields.level.label=Level
i18n_entity.FailureRecord.fields.modifiedBy.label=Last modifier
i18n_entity.FailureRecord.fields.modifiedOn.label=Last modified time
i18n_entity.FailureRecord.fields.num.label=Number of times
i18n_entity.FailureRecord.fields.part.label=Object
i18n_entity.FailureRecord.fields.source.label=Source
i18n_entity.FailureRecord.fields.subKind.label=Subcategory
i18n_entity.FailureRecord.fields.version.label=Revised version
i18n_entity.FailureRecord.group=Core
i18n_entity.FailureRecord.label=Fault record
i18n_entity.FalconBlockChildId.fields.blockId.label=blockId
i18n_entity.FalconBlockChildId.fields.childId.label=childId
i18n_entity.FalconBlockChildId.fields.contextKey.label=contextKey
i18n_entity.FalconBlockChildId.fields.createdBy.label=Founder
i18n_entity.FalconBlockChildId.fields.createdOn.label=Creation time
i18n_entity.FalconBlockChildId.fields.id.label=ID
i18n_entity.FalconBlockChildId.fields.index.label=index
i18n_entity.FalconBlockChildId.fields.modifiedBy.label=Last modifier
i18n_entity.FalconBlockChildId.fields.modifiedOn.label=Modification time
i18n_entity.FalconBlockChildId.fields.taskId.label=taskId
i18n_entity.FalconBlockChildId.fields.version.label=Revised version
i18n_entity.FalconBlockChildId.group=Falcon
i18n_entity.FalconBlockChildId.label=Falcon mission block block ID
i18n_entity.FalconBlockChildId.listCard.contextKey.prefix=contextKey
i18n_entity.FalconBlockChildId.listCard.createdOn.prefix=Create
i18n_entity.FalconBlockChildId.listCard.taskId.prefix=taskId
i18n_entity.FalconBlockRecord.fields.blockConfigId.label=blockConfigId
i18n_entity.FalconBlockRecord.fields.createdBy.label=Founder
i18n_entity.FalconBlockRecord.fields.createdOn.label=Creation time
i18n_entity.FalconBlockRecord.fields.endedOn.label=endedOn
i18n_entity.FalconBlockRecord.fields.endedReason.label=endedReason
i18n_entity.FalconBlockRecord.fields.failureNum.label=Number of failures
i18n_entity.FalconBlockRecord.fields.id.label=ID
i18n_entity.FalconBlockRecord.fields.inputParams.label=inputParams
i18n_entity.FalconBlockRecord.fields.internalVariables.label=internalVariables
i18n_entity.FalconBlockRecord.fields.modifiedBy.label=Last modifier
i18n_entity.FalconBlockRecord.fields.modifiedOn.label=Modification time
i18n_entity.FalconBlockRecord.fields.outputParams.label=outputParams
i18n_entity.FalconBlockRecord.fields.startedOn.label=startedOn
i18n_entity.FalconBlockRecord.fields.status.label=status
i18n_entity.FalconBlockRecord.fields.taskId.label=taskId
i18n_entity.FalconBlockRecord.fields.version.label=Revised version
i18n_entity.FalconBlockRecord.group=Falcon
i18n_entity.FalconBlockRecord.label=Falcon Mission Block Record
i18n_entity.FalconBlockRecord.listCard.blockConfigId.prefix=blockConfigId
i18n_entity.FalconBlockRecord.listCard.taskId.prefix=taskId
i18n_entity.FalconLog.fields.blockId.label=Associated components
i18n_entity.FalconLog.fields.createdBy.label=Founder
i18n_entity.FalconLog.fields.createdOn.label=Creation time
i18n_entity.FalconLog.fields.id.label=ID
i18n_entity.FalconLog.fields.level.inlineOptionBill.items.Debug.label=General
i18n_entity.FalconLog.fields.level.inlineOptionBill.items.Error.label=Error
i18n_entity.FalconLog.fields.level.inlineOptionBill.items.Info.label=Important
i18n_entity.FalconLog.fields.level.label=Level
i18n_entity.FalconLog.fields.message.label=Message
i18n_entity.FalconLog.fields.modifiedBy.label=Last modifier
i18n_entity.FalconLog.fields.modifiedOn.label=Modification time
i18n_entity.FalconLog.fields.taskId.label=Associated tasks
i18n_entity.FalconLog.fields.version.label=Revised version
i18n_entity.FalconLog.group=Falcon
i18n_entity.FalconLog.label=Falcon log
i18n_entity.FalconLog.listCard.blockId.prefix=Associated components
i18n_entity.FalconLog.listCard.createdOn.prefix=Create
i18n_entity.FalconRelatedObject.fields.createdBy.label=Founder
i18n_entity.FalconRelatedObject.fields.createdOn.label=Creation time
i18n_entity.FalconRelatedObject.fields.id.label=ID
i18n_entity.FalconRelatedObject.fields.modifiedBy.label=Last modifier
i18n_entity.FalconRelatedObject.fields.modifiedOn.label=Last modified time
i18n_entity.FalconRelatedObject.fields.objectArgs.label=objectArgs
i18n_entity.FalconRelatedObject.fields.objectId.label=objectId
i18n_entity.FalconRelatedObject.fields.objectType.label=objectType
i18n_entity.FalconRelatedObject.fields.taskId.label=taskId
i18n_entity.FalconRelatedObject.fields.version.label=Revised version
i18n_entity.FalconRelatedObject.group=Falcon
i18n_entity.FalconRelatedObject.label=Falcon mission related objects
i18n_entity.FalconRelatedObject.listCard.objectArgs.prefix=objectArgs
i18n_entity.FalconRelatedObject.listCard.objectId.prefix=objectId
i18n_entity.FalconTaskRecord.fields.actualRobots.label=Execution robot
i18n_entity.FalconTaskRecord.fields.createdBy.label=Founder
i18n_entity.FalconTaskRecord.fields.createdOn.label=Creation time
i18n_entity.FalconTaskRecord.fields.defId.label=Define ID
i18n_entity.FalconTaskRecord.fields.defLabel.label=Task Template
i18n_entity.FalconTaskRecord.fields.defVersion.label=Template version
i18n_entity.FalconTaskRecord.fields.endedOn.label=End time
i18n_entity.FalconTaskRecord.fields.endedReason.label=Cause of error
i18n_entity.FalconTaskRecord.fields.failureNum.label=Number of failures
i18n_entity.FalconTaskRecord.fields.id.label=ID
i18n_entity.FalconTaskRecord.fields.inputParams.label=Input parameters
i18n_entity.FalconTaskRecord.fields.modifiedBy.label=Last modifier
i18n_entity.FalconTaskRecord.fields.modifiedOn.label=Modification time
i18n_entity.FalconTaskRecord.fields.paused.label=Has been suspended
i18n_entity.FalconTaskRecord.fields.ro.label=RO
i18n_entity.FalconTaskRecord.fields.rootBlockStateId.label=Root block ID
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.100.label=Created
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.120.label=Has begun
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.140.label=Failure
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.160.label=Completed
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.180.label=Cancelled
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.190.label=Have abandoned
i18n_entity.FalconTaskRecord.fields.status.label=Status
i18n_entity.FalconTaskRecord.fields.subTask.label=Subtask
i18n_entity.FalconTaskRecord.fields.topTaskId.label=Top-level task ID
i18n_entity.FalconTaskRecord.fields.variables.label=Task variable
i18n_entity.FalconTaskRecord.fields.version.label=Revised version
i18n_entity.FalconTaskRecord.group=Falcon
i18n_entity.FalconTaskRecord.label=Falcon mission record
i18n_entity.FalconTaskRecord.listCard.createdOn.prefix=Create
i18n_entity.FalconTaskRecord.listCard.defLabel.prefix=Template
i18n_entity.FalconTaskRecord.listCard.defVersion.prefix=(
i18n_entity.FalconTaskRecord.listCard.defVersion.suffix=)
i18n_entity.FalconTaskRecord.listCard.endedOn.prefix=End
i18n_entity.FalconTaskRecord.listStats.items[0].label=Failure
i18n_entity.FalconTaskRecord.listStats.items[1].label=Has been suspended
i18n_entity.FalconTaskRecord.listStats.items[2].label=Added today
i18n_entity.FalconTaskRecord.listStats.items[3].label=Cancel if you fail today.
i18n_entity.FalconTaskRecord.listStats.items[4].label=Added this week
i18n_entity.FalconTaskRecord.listStats.items[5].label=Cancel this week
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[0].label=Delete
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[1].label=Export
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[2].label=Suspension of implementation
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[3].label=Continue to implement
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[4].label=Fault retry
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[5].label=Cancel the execution
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[6].label=Delete
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[7].label=Template
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[8].label=Global control
i18n_entity.FalconTaskResource.fields.args.label=Parameter
i18n_entity.FalconTaskResource.fields.createdBy.label=Founder
i18n_entity.FalconTaskResource.fields.createdOn.label=Creation time
i18n_entity.FalconTaskResource.fields.id.label=ID
i18n_entity.FalconTaskResource.fields.modifiedBy.label=Last modifier
i18n_entity.FalconTaskResource.fields.modifiedOn.label=Last modified time
i18n_entity.FalconTaskResource.fields.resId.label=Resource ID
i18n_entity.FalconTaskResource.fields.resType.label=Resource type
i18n_entity.FalconTaskResource.fields.taskId.label=Task number
i18n_entity.FalconTaskResource.fields.version.label=Revised version
i18n_entity.FalconTaskResource.group=Falcon
i18n_entity.FalconTaskResource.label=Falcon Mission Resources
i18n_entity.FalconTaskResource.listCard.resId.prefix=Resource ID
i18n_entity.FbAssemblyLine.fields.building.label=Building
i18n_entity.FbAssemblyLine.fields.buildingLayer.label=Floor
i18n_entity.FbAssemblyLine.fields.createdBy.label=Founder
i18n_entity.FbAssemblyLine.fields.createdOn.label=Creation time
i18n_entity.FbAssemblyLine.fields.disabled.label=Disable
i18n_entity.FbAssemblyLine.fields.id.label=Order number
i18n_entity.FbAssemblyLine.fields.modifiedBy.label=Last modifier
i18n_entity.FbAssemblyLine.fields.modifiedOn.label=Last modified time
i18n_entity.FbAssemblyLine.fields.name.label=Name
i18n_entity.FbAssemblyLine.fields.remark.label=Remarks
i18n_entity.FbAssemblyLine.fields.ro.label=Root tissue
i18n_entity.FbAssemblyLine.fields.version.label=Version
i18n_entity.FbAssemblyLine.group=MainData
i18n_entity.FbAssemblyLine.label=Production line
i18n_entity.FbBin.fields.assemblyLine.label=Affiliated production line
i18n_entity.FbBin.fields.boxDirection.label=Fork direction
i18n_entity.FbBin.fields.boxHeight.label=Fork lift height
i18n_entity.FbBin.fields.btDisabled.label=Deactivated
i18n_entity.FbBin.fields.channel.label=In aisle
i18n_entity.FbBin.fields.column.label=List
i18n_entity.FbBin.fields.container.label=Storage bin container
i18n_entity.FbBin.fields.createdBy.label=Founder
i18n_entity.FbBin.fields.createdOn.label=Creation time
i18n_entity.FbBin.fields.depth.label=Deep
i18n_entity.FbBin.fields.district.label=Owned storage area
i18n_entity.FbBin.fields.id.label=Storage bin number
i18n_entity.FbBin.fields.layer.label=Layer
i18n_entity.FbBin.fields.loadStatus.inlineOptionBill.items.Empty.label=Not occupied
i18n_entity.FbBin.fields.loadStatus.inlineOptionBill.items.Occupied.label=Occupied
i18n_entity.FbBin.fields.loadStatus.inlineOptionBill.items.ToLoad.label=Coming soon
i18n_entity.FbBin.fields.loadStatus.inlineOptionBill.items.ToUnload.label=About to be shipped away
i18n_entity.FbBin.fields.loadStatus.label=Occupation status
i18n_entity.FbBin.fields.locked.label=Lock
i18n_entity.FbBin.fields.locked.listBatchButtons.buttons[0].label=Unlock
i18n_entity.FbBin.fields.lockedBy.label=Lock reason
i18n_entity.FbBin.fields.materialCategoryLabel.label=Storage material classification name
i18n_entity.FbBin.fields.modifiedBy.label=Last modifier
i18n_entity.FbBin.fields.modifiedOn.label=Last modified time
i18n_entity.FbBin.fields.occupied.label=In stock
i18n_entity.FbBin.fields.occupied.view.trueText=In stock
i18n_entity.FbBin.fields.pendingContainer.label=Container to be transported
i18n_entity.FbBin.fields.purpose.label=Purpose
i18n_entity.FbBin.fields.rack.label=Owned shelves
i18n_entity.FbBin.fields.remark.label=Remarks
i18n_entity.FbBin.fields.ro.label=Root tissue
i18n_entity.FbBin.fields.robotDirection.label=Robot direction
i18n_entity.FbBin.fields.robotX.label=Robot position X
i18n_entity.FbBin.fields.robotY.label=Robot position Y
i18n_entity.FbBin.fields.row.label=Row
i18n_entity.FbBin.fields.version.label=Version
i18n_entity.FbBin.fields.warehouse.label=Owned warehouse
i18n_entity.FbBin.fields.workSite.label=Owned workstation
i18n_entity.FbBin.group=MainData
i18n_entity.FbBin.label=Storage bin
i18n_entity.FbBin.listCard.container.prefix=Storage bin container
i18n_entity.FbBin.listCard.district.prefix=Owned storage area
i18n_entity.FbBin.listStats.items[0].label=Processing (locked)
i18n_entity.FbBin.listStats.items[1].label=In stock
i18n_entity.FbBin.listStats.items[2].label=Coming soon
i18n_entity.FbBin.listStats.items[3].label=About to be shipped away
i18n_entity.FbBin.pagesButtons.ListMain.buttons[0].label=Added
i18n_entity.FbBin.pagesButtons.ListMain.buttons[1].label=Batch edit
i18n_entity.FbBin.pagesButtons.ListMain.buttons[2].label=Delete
i18n_entity.FbBin.pagesButtons.ListMain.buttons[3].label=Export
i18n_entity.FbBin.pagesButtons.ListMain.buttons[4].label=Import
i18n_entity.FbBin.pagesButtons.ListMain.buttons[5].label=Batch create storage bins
i18n_entity.FbBinInv.fields.amount.label=Amount
i18n_entity.FbBinInv.fields.assemblyLine.label=Affiliated production line
i18n_entity.FbBinInv.fields.bin.label=Storage bin
i18n_entity.FbBinInv.fields.binDisabled.label=Storage bin deactivated
i18n_entity.FbBinInv.fields.binFilled.label=Is the storage bin in stock?
i18n_entity.FbBinInv.fields.btBzDesc.label=Business description
i18n_entity.FbBinInv.fields.btBzMark.label=Business marking
i18n_entity.FbBinInv.fields.btMaterial.label=Materials
i18n_entity.FbBinInv.fields.btMaterialCategory.label=Material classification number
i18n_entity.FbBinInv.fields.btMaterialCategoryName.label=Material classification name
i18n_entity.FbBinInv.fields.btMaterialId.label=Material number
i18n_entity.FbBinInv.fields.btMaterialModel.label=Material type
i18n_entity.FbBinInv.fields.btMaterialName.label=Material name
i18n_entity.FbBinInv.fields.btMaterialSpec.label=Material specification
i18n_entity.FbBinInv.fields.btMaterialTopCategory.label=Material classification number
i18n_entity.FbBinInv.fields.btMaterialTopCategoryName.label=Material classification name
i18n_entity.FbBinInv.fields.btPrepare.label=btPrepare
i18n_entity.FbBinInv.fields.channel.label=Aisle
i18n_entity.FbBinInv.fields.column.label=List
i18n_entity.FbBinInv.fields.containerDisabled.label=Container deactivation
i18n_entity.FbBinInv.fields.containerFilled.label=Is the container in stock?
i18n_entity.FbBinInv.fields.containers.label=Container list
i18n_entity.FbBinInv.fields.createdBy.label=Founder
i18n_entity.FbBinInv.fields.createdOn.label=Creation time
i18n_entity.FbBinInv.fields.depth.label=Deep
i18n_entity.FbBinInv.fields.district.label=Storage area
i18n_entity.FbBinInv.fields.id.label=ID
i18n_entity.FbBinInv.fields.layer.label=Layer
i18n_entity.FbBinInv.fields.lotNo.label=Batch number
i18n_entity.FbBinInv.fields.materialCategoryLabel.label=Storage material classification name
i18n_entity.FbBinInv.fields.materialIds.label=Material on storage bin
i18n_entity.FbBinInv.fields.materialNames.label=List of material names on storage bin
i18n_entity.FbBinInv.fields.modifiedBy.label=Last modifier
i18n_entity.FbBinInv.fields.modifiedOn.label=Last modified time
i18n_entity.FbBinInv.fields.onRobot.label=The robot
i18n_entity.FbBinInv.fields.pendingContainer.label=Container to be transported
i18n_entity.FbBinInv.fields.qty.label=Number
i18n_entity.FbBinInv.fields.rack.label=Shelves
i18n_entity.FbBinInv.fields.robotBin.label=Robot storage bin
i18n_entity.FbBinInv.fields.row.label=Row
i18n_entity.FbBinInv.fields.subNum.label=Number of cells
i18n_entity.FbBinInv.fields.topContainer.label=Outermost container
i18n_entity.FbBinInv.fields.topContainerType.label=Outermost container type
i18n_entity.FbBinInv.fields.version.label=Revised version
i18n_entity.FbBinInv.fields.warehouse.label=Warehouse
i18n_entity.FbBinInv.fields.workSite.label=Workstation
i18n_entity.FbBinInv.group=Main
i18n_entity.FbBinInv.label=Storage bin inventory
i18n_entity.FbContainer.fields.bin.label=The storage bin
i18n_entity.FbContainer.fields.btDisabled.label=Deactivated
i18n_entity.FbContainer.fields.createdBy.label=Founder
i18n_entity.FbContainer.fields.createdOn.label=Creation time
i18n_entity.FbContainer.fields.district.label=Current storage area
i18n_entity.FbContainer.fields.filled.label=In stock
i18n_entity.FbContainer.fields.fixedStoreBin.label=Specify storage bin
i18n_entity.FbContainer.fields.height.label=Container height
i18n_entity.FbContainer.fields.id.label=Container number
i18n_entity.FbContainer.fields.length.label=Length of container
i18n_entity.FbContainer.fields.locked.label=Lock
i18n_entity.FbContainer.fields.locked.listBatchButtons.buttons[0].label=Unlock
i18n_entity.FbContainer.fields.maxWeight.label=Bearing capacity of container
i18n_entity.FbContainer.fields.modifiedBy.label=Last modifier
i18n_entity.FbContainer.fields.modifiedOn.label=Last modified time
i18n_entity.FbContainer.fields.onRobot.label=The robot
i18n_entity.FbContainer.fields.pContainer.label=Parent container
i18n_entity.FbContainer.fields.preBin.label=Scheduled storage bin
i18n_entity.FbContainer.fields.qcResult.label=Quality inspection results
i18n_entity.FbContainer.fields.remark.label=Remarks
i18n_entity.FbContainer.fields.ro.label=Root tissue
i18n_entity.FbContainer.fields.state.label=Status
i18n_entity.FbContainer.fields.subNum.label=Number of cells
i18n_entity.FbContainer.fields.targetBin.label=To the storage bin
i18n_entity.FbContainer.fields.taskType.inlineOptionBill.items.Count.label=Taking stock
i18n_entity.FbContainer.fields.taskType.inlineOptionBill.items.Pick.label=To be sorted
i18n_entity.FbContainer.fields.taskType.inlineOptionBill.items.Put.label=To be packed
i18n_entity.FbContainer.fields.taskType.label=Task type
i18n_entity.FbContainer.fields.type.label=Container type
i18n_entity.FbContainer.fields.version.label=Version
i18n_entity.FbContainer.fields.volume.label=Volume of container
i18n_entity.FbContainer.fields.warehouse.label=Current warehouse
i18n_entity.FbContainer.fields.width.label=Container width
i18n_entity.FbContainer.fields.workStatus.inlineOptionBill.items.ToPick.label=Pending picking
i18n_entity.FbContainer.fields.workStatus.inlineOptionBill.items.ToPut.label=Pending loading
i18n_entity.FbContainer.fields.workStatus.label=Working state
i18n_entity.FbContainer.group=MainData
i18n_entity.FbContainer.label=Container
i18n_entity.FbContainer.listCard.bin.prefix=The storage bin
i18n_entity.FbContainer.listCard.type.prefix=Type
i18n_entity.FbContainer.listStats.items[0].label=Processing (locked)
i18n_entity.FbContainer.listStats.items[1].label=In stock
i18n_entity.FbContainer.listStats.items[2].label=Not in the storage bin
i18n_entity.FbContainerType.fields.btDisabled.label=Deactivated
i18n_entity.FbContainerType.fields.createdBy.label=Founder
i18n_entity.FbContainerType.fields.createdOn.label=Creation time
i18n_entity.FbContainerType.fields.id.label=Container type encoding
i18n_entity.FbContainerType.fields.mixedMaterial.label=Material mixing
i18n_entity.FbContainerType.fields.modifiedBy.label=Last modifier
i18n_entity.FbContainerType.fields.modifiedOn.label=Last modified time
i18n_entity.FbContainerType.fields.name.label=Container type name
i18n_entity.FbContainerType.fields.remark.label=Explanation
i18n_entity.FbContainerType.fields.ro.label=Root tissue
i18n_entity.FbContainerType.fields.storeDistricts.label=Storage storage area
i18n_entity.FbContainerType.fields.subNum.label=Number of partitions
i18n_entity.FbContainerType.fields.version.label=Version
i18n_entity.FbContainerType.group=MainData
i18n_entity.FbContainerType.label=Container type
i18n_entity.FbContainerType.listCard.storeDistricts.prefix=Storage storage area
i18n_entity.FbCountDiffLine.fields.actualQty.label=Actual number
i18n_entity.FbCountDiffLine.fields.bin.label=Original storage bin
i18n_entity.FbCountDiffLine.fields.btLineNo.label=Line number
i18n_entity.FbCountDiffLine.fields.btMaterial.label=Materials
i18n_entity.FbCountDiffLine.fields.btMaterialCategory.label=Material classification number
i18n_entity.FbCountDiffLine.fields.btMaterialCategoryName.label=Material classification name
i18n_entity.FbCountDiffLine.fields.btMaterialId.label=Material number
i18n_entity.FbCountDiffLine.fields.btMaterialImage.label=Material pictures
i18n_entity.FbCountDiffLine.fields.btMaterialModel.label=Material type
i18n_entity.FbCountDiffLine.fields.btMaterialName.label=Material name
i18n_entity.FbCountDiffLine.fields.btMaterialSpec.label=Material specification
i18n_entity.FbCountDiffLine.fields.btParentId.label=Owned order
i18n_entity.FbCountDiffLine.fields.container.label=Container
i18n_entity.FbCountDiffLine.fields.createdBy.label=Founder
i18n_entity.FbCountDiffLine.fields.createdOn.label=Creation time
i18n_entity.FbCountDiffLine.fields.diffQty.label=Number of differences
i18n_entity.FbCountDiffLine.fields.fix.label=Implementation
i18n_entity.FbCountDiffLine.fields.id.label=ID
i18n_entity.FbCountDiffLine.fields.lotNo.label=Batch number
i18n_entity.FbCountDiffLine.fields.modifiedBy.label=Last modifier
i18n_entity.FbCountDiffLine.fields.modifiedOn.label=Modification time
i18n_entity.FbCountDiffLine.fields.qty.label=Original quantity
i18n_entity.FbCountDiffLine.fields.qualityLevel.label=Quality grade
i18n_entity.FbCountDiffLine.fields.remark.label=Remarks
i18n_entity.FbCountDiffLine.fields.sourceInvLayout.label=Related inventory details
i18n_entity.FbCountDiffLine.fields.subContainerId.label=Grid number
i18n_entity.FbCountDiffLine.fields.taskId.label=Inventory task
i18n_entity.FbCountDiffLine.fields.version.label=Revised version
i18n_entity.FbCountDiffLine.group=Warehouse
i18n_entity.FbCountDiffLine.label=Disk difference record line
i18n_entity.FbCountDiffLine.listCard.btMaterial.prefix=Materials
i18n_entity.FbCountDiffLine.listCard.subContainerId.prefix=Grid number
i18n_entity.FbCountFix.fields.btMaterial.label=Materials
i18n_entity.FbCountFix.fields.btMaterialCategory.label=Material classification number
i18n_entity.FbCountFix.fields.btMaterialCategoryName.label=Material classification name
i18n_entity.FbCountFix.fields.btMaterialId.label=Material number
i18n_entity.FbCountFix.fields.btMaterialImage.label=Material pictures
i18n_entity.FbCountFix.fields.btMaterialModel.label=Material type
i18n_entity.FbCountFix.fields.btMaterialName.label=Material name
i18n_entity.FbCountFix.fields.btMaterialSpec.label=Material specification
i18n_entity.FbCountFix.fields.container.label=Container
i18n_entity.FbCountFix.fields.createdBy.label=Founder
i18n_entity.FbCountFix.fields.createdOn.label=Creation time
i18n_entity.FbCountFix.fields.id.label=ID
i18n_entity.FbCountFix.fields.lotNo.label=Batch number
i18n_entity.FbCountFix.fields.modifiedBy.label=Last modifier
i18n_entity.FbCountFix.fields.modifiedOn.label=Modification time
i18n_entity.FbCountFix.fields.qty.label=Number of changes
i18n_entity.FbCountFix.fields.qualityLevel.label=Quality grade
i18n_entity.FbCountFix.fields.remark.label=Remarks
i18n_entity.FbCountFix.fields.subContainerId.label=Lattice number
i18n_entity.FbCountFix.fields.version.label=Revised version
i18n_entity.FbCountFix.group=Warehouse
i18n_entity.FbCountFix.label=Inventory correction record
i18n_entity.FbCountFix.listCard.container.prefix=Container
i18n_entity.FbCountFix.listCard.qty.prefix=Number of changes
i18n_entity.FbCountFix.listCard.subContainerId.prefix=Grid number
i18n_entity.FbCountOrder.fields.bins.label=Plan inventory storage bin list
i18n_entity.FbCountOrder.fields.btOrderState.inlineOptionBill.items.Done.label=Complete
i18n_entity.FbCountOrder.fields.btOrderState.inlineOptionBill.items.Init.label=Not submitted
i18n_entity.FbCountOrder.fields.btOrderState.inlineOptionBill.items.Submitted.label=Submitted
i18n_entity.FbCountOrder.fields.btOrderState.label=Business status
i18n_entity.FbCountOrder.fields.btOrderStateReason.label=Status description
i18n_entity.FbCountOrder.fields.containers.label=Plan inventory container list
i18n_entity.FbCountOrder.fields.createdBy.label=Founder
i18n_entity.FbCountOrder.fields.createdOn.label=Creation time
i18n_entity.FbCountOrder.fields.diffLines.label=Disk difference list
i18n_entity.FbCountOrder.fields.districts.label=List of planned inventory storage areas
i18n_entity.FbCountOrder.fields.doneProcessed.label=Disk difference has been processed
i18n_entity.FbCountOrder.fields.id.label=Order number
i18n_entity.FbCountOrder.fields.materials.label=Plan inventory material list
i18n_entity.FbCountOrder.fields.modifiedBy.label=Last modifier
i18n_entity.FbCountOrder.fields.modifiedOn.label=Modification time
i18n_entity.FbCountOrder.fields.remark.label=Explanation
i18n_entity.FbCountOrder.fields.taskGenerated.label=Inventory task generated
i18n_entity.FbCountOrder.fields.taskLines.label=Task statistics row
i18n_entity.FbCountOrder.fields.version.label=Revised version
i18n_entity.FbCountOrder.group=Warehouse
i18n_entity.FbCountOrder.label=Inventory list
i18n_entity.FbCountOrder.states.states.Done.label=Complete
i18n_entity.FbCountOrder.states.states.Init.label=Not submitted
i18n_entity.FbCountOrder.states.states.Init.nextStates.Submitted.buttonLabel=Submit
i18n_entity.FbCountOrder.states.states.Submitted.label=Submitted
i18n_entity.FbCountOrder.states.states.Submitted.nextStates.Done.buttonLabel=Execution disk difference
i18n_entity.FbCountTask.fields.bin.label=Source storage bin
i18n_entity.FbCountTask.fields.btLines.label=Single line
i18n_entity.FbCountTask.fields.btOrderState.inlineOptionBill.items.Init.label=To be counted
i18n_entity.FbCountTask.fields.btOrderState.inlineOptionBill.items.Submitted.label=Inventory taken
i18n_entity.FbCountTask.fields.btOrderState.label=Business status
i18n_entity.FbCountTask.fields.btOrderStateReason.label=Status description
i18n_entity.FbCountTask.fields.container.label=Inventory container
i18n_entity.FbCountTask.fields.containerInOrderId.label=Container inbound waybill number
i18n_entity.FbCountTask.fields.containerOutOrderId.label=Container outbound waybill number
i18n_entity.FbCountTask.fields.countOrderId.label=Owned inventory list
i18n_entity.FbCountTask.fields.createdBy.label=Founder
i18n_entity.FbCountTask.fields.createdOn.label=Creation time
i18n_entity.FbCountTask.fields.id.label=ID
i18n_entity.FbCountTask.fields.modifiedBy.label=Last modifier
i18n_entity.FbCountTask.fields.modifiedOn.label=Last modified time
i18n_entity.FbCountTask.fields.remark.label=Explanation
i18n_entity.FbCountTask.fields.submittedPostProcessed.label=Processing completed after submission
i18n_entity.FbCountTask.fields.version.label=Revised version
i18n_entity.FbCountTask.group=Warehouse
i18n_entity.FbCountTask.label=Inventory task
i18n_entity.FbCountTask.listCard.bin.prefix=Storage bin
i18n_entity.FbCountTask.listCard.container.prefix=Container
i18n_entity.FbCountTask.listStats.items[0].label=To be counted
i18n_entity.FbCountTask.states.states.Init.label=To be counted
i18n_entity.FbCountTask.states.states.Init.nextStates.Submitted.buttonLabel=Submit
i18n_entity.FbCountTask.states.states.Submitted.label=Inventory taken
i18n_entity.FbCountTaskLine.fields.actualQty.label=Actual number
i18n_entity.FbCountTaskLine.fields.amount.label=Amount
i18n_entity.FbCountTaskLine.fields.bin.label=The storage bin
i18n_entity.FbCountTaskLine.fields.btLineNo.label=Line number
i18n_entity.FbCountTaskLine.fields.btMaterial.label=Materials
i18n_entity.FbCountTaskLine.fields.btMaterialCategory.label=Material classification number
i18n_entity.FbCountTaskLine.fields.btMaterialCategoryName.label=Material classification name
i18n_entity.FbCountTaskLine.fields.btMaterialId.label=Material number
i18n_entity.FbCountTaskLine.fields.btMaterialImage.label=Material pictures
i18n_entity.FbCountTaskLine.fields.btMaterialModel.label=Material type
i18n_entity.FbCountTaskLine.fields.btMaterialName.label=Material name
i18n_entity.FbCountTaskLine.fields.btMaterialSpec.label=Material specification
i18n_entity.FbCountTaskLine.fields.btParentId.label=Belonging tasks
i18n_entity.FbCountTaskLine.fields.createdBy.label=Founder
i18n_entity.FbCountTaskLine.fields.createdOn.label=Creation time
i18n_entity.FbCountTaskLine.fields.district.label=The storage area
i18n_entity.FbCountTaskLine.fields.expDate.label=Valid period
i18n_entity.FbCountTaskLine.fields.id.label=ID
i18n_entity.FbCountTaskLine.fields.leafContainer.label=Container
i18n_entity.FbCountTaskLine.fields.lotNo.label=Batch number
i18n_entity.FbCountTaskLine.fields.mfgDate.label=Production date
i18n_entity.FbCountTaskLine.fields.modifiedBy.label=Last modifier
i18n_entity.FbCountTaskLine.fields.modifiedOn.label=Last modified time
i18n_entity.FbCountTaskLine.fields.owner.label=Shipper
i18n_entity.FbCountTaskLine.fields.price.label=Unit price
i18n_entity.FbCountTaskLine.fields.qty.label=Number
i18n_entity.FbCountTaskLine.fields.qualityLevel.label=Quality grade
i18n_entity.FbCountTaskLine.fields.sourceInvLayout.label=Reference inventory details
i18n_entity.FbCountTaskLine.fields.subContainerId.label=Lattice
i18n_entity.FbCountTaskLine.fields.topContainer.label=Outermost container
i18n_entity.FbCountTaskLine.fields.unitLabel.label=Unit name
i18n_entity.FbCountTaskLine.fields.vendor.label=Suppliers
i18n_entity.FbCountTaskLine.fields.version.label=Revised version
i18n_entity.FbCountTaskLine.fields.warehouse.label=Warehouse
i18n_entity.FbCountTaskLine.group=Warehouse
i18n_entity.FbCountTaskLine.label=Inventory task line
i18n_entity.FbCountTaskLine.listCard.actualQty.prefix=Actual number
i18n_entity.FbCountTaskLine.listCard.btMaterialId.prefix=Material number
i18n_entity.FbCountTaskLine.listCard.lotNo.prefix=Batch number
i18n_entity.FbCountTaskLine.listCard.qty.prefix=Stock quantity
i18n_entity.FbCountTaskStatLine.fields.bin.label=Inventory storage bin
i18n_entity.FbCountTaskStatLine.fields.btLineNo.label=Line number
i18n_entity.FbCountTaskStatLine.fields.btParentId.label=Owned order
i18n_entity.FbCountTaskStatLine.fields.container.label=Inventory container
i18n_entity.FbCountTaskStatLine.fields.createdBy.label=Founder
i18n_entity.FbCountTaskStatLine.fields.createdOn.label=Creation time
i18n_entity.FbCountTaskStatLine.fields.id.label=ID
i18n_entity.FbCountTaskStatLine.fields.materialsNum.label=Record quantity of material
i18n_entity.FbCountTaskStatLine.fields.modifiedBy.label=Last modifier
i18n_entity.FbCountTaskStatLine.fields.modifiedOn.label=Last modified time
i18n_entity.FbCountTaskStatLine.fields.qty.label=Total number of records
i18n_entity.FbCountTaskStatLine.fields.taskId.label=Inventory task
i18n_entity.FbCountTaskStatLine.fields.version.label=Revised version
i18n_entity.FbCountTaskStatLine.group=Warehouse
i18n_entity.FbCountTaskStatLine.label=Inventory Single Task Statistics Rows
i18n_entity.FbCountTaskStatLine.listCard.materialsNum.prefix=Number of materials
i18n_entity.FbCountTaskStatLine.listCard.qty.prefix=Total quantity
i18n_entity.FbCustomer.fields.address.label=Address
i18n_entity.FbCustomer.fields.btDisabled.label=Deactivated
i18n_entity.FbCustomer.fields.contact.label=Contact person
i18n_entity.FbCustomer.fields.createdBy.label=Founder
i18n_entity.FbCustomer.fields.createdOn.label=Creation time
i18n_entity.FbCustomer.fields.id.label=Customer code
i18n_entity.FbCustomer.fields.modifiedBy.label=Last modifier
i18n_entity.FbCustomer.fields.modifiedOn.label=Last modified time
i18n_entity.FbCustomer.fields.name.label=Customer name
i18n_entity.FbCustomer.fields.phone.label=Contact phone number
i18n_entity.FbCustomer.fields.remark.label=Remarks
i18n_entity.FbCustomer.fields.ro.label=Root tissue
i18n_entity.FbCustomer.fields.version.label=Version
i18n_entity.FbCustomer.group=MainData
i18n_entity.FbCustomer.label=Customers
i18n_entity.FbCustomer.listCard.btDisabled.formatMapping[0].replaceText=Deactivated
i18n_entity.FbCustomer.listCard.contact.prefix=Contact person
i18n_entity.FbCustomer.listCard.name.prefix=Customer name
i18n_entity.FbCustomer.listCard.phone.prefix=Telephone
i18n_entity.FbDepartment.fields.btDisabled.label=Deactivated
i18n_entity.FbDepartment.fields.btHiLeafNode.label=Leaf node
i18n_entity.FbDepartment.fields.btHiNodeLevel.label=Layer
i18n_entity.FbDepartment.fields.btHiParentNode.label=Superior
i18n_entity.FbDepartment.fields.btHiRootNode.label=Top-level node
i18n_entity.FbDepartment.fields.createdBy.label=Founder
i18n_entity.FbDepartment.fields.createdOn.label=Creation time
i18n_entity.FbDepartment.fields.id.label=Number
i18n_entity.FbDepartment.fields.modifiedBy.label=Last modifier
i18n_entity.FbDepartment.fields.modifiedOn.label=Last modified time
i18n_entity.FbDepartment.fields.name.label=Name
i18n_entity.FbDepartment.fields.owner.label=Person in charge
i18n_entity.FbDepartment.fields.ro.label=Root tissue
i18n_entity.FbDepartment.fields.version.label=Version
i18n_entity.FbDepartment.group=User
i18n_entity.FbDepartment.label=Organization
i18n_entity.FbDepartment.listCard.owner.prefix=Person in charge
i18n_entity.FbDevTask.fields.comments.label=Comment
i18n_entity.FbDevTask.fields.createdBy.label=Founder
i18n_entity.FbDevTask.fields.createdOn.label=Creation time
i18n_entity.FbDevTask.fields.description.label=Description
i18n_entity.FbDevTask.fields.devVersion.label=Iteration
i18n_entity.FbDevTask.fields.files.label=Annex document
i18n_entity.FbDevTask.fields.id.label=Number
i18n_entity.FbDevTask.fields.images.label=Attached image
i18n_entity.FbDevTask.fields.kind.inlineOptionBill.items.Bug.label=Defect
i18n_entity.FbDevTask.fields.kind.inlineOptionBill.items.Feature.label=Function
i18n_entity.FbDevTask.fields.kind.inlineOptionBill.items.Improvement.label=Optimization
i18n_entity.FbDevTask.fields.kind.inlineOptionBill.items.Test.label=Test
i18n_entity.FbDevTask.fields.kind.label=Type
i18n_entity.FbDevTask.fields.modifiedBy.label=Last modifier
i18n_entity.FbDevTask.fields.modifiedOn.label=Last modified time
i18n_entity.FbDevTask.fields.priority.inlineOptionBill.items.High.label=High
i18n_entity.FbDevTask.fields.priority.inlineOptionBill.items.Low.label=Low
i18n_entity.FbDevTask.fields.priority.inlineOptionBill.items.Normal.label=In
i18n_entity.FbDevTask.fields.priority.label=Priority
i18n_entity.FbDevTask.fields.processedBy.label=Processing person
i18n_entity.FbDevTask.fields.project.label=Project
i18n_entity.FbDevTask.fields.ro.label=Enterprise
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Closed.label=Closed
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Open.label=Create
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Processing.label=In progress
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Rejected.label=Has refused
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Resolved.label=Resolved
i18n_entity.FbDevTask.fields.state.label=Status
i18n_entity.FbDevTask.fields.testImages.label=Test image
i18n_entity.FbDevTask.fields.testResult.label=Test results
i18n_entity.FbDevTask.fields.title.label=Title
i18n_entity.FbDevTask.fields.version.label=Version
i18n_entity.FbDevTask.group=Dev
i18n_entity.FbDevTask.label=Collaborative tasks
i18n_entity.FbDevVersion.fields.createdBy.label=Founder
i18n_entity.FbDevVersion.fields.createdOn.label=Creation time
i18n_entity.FbDevVersion.fields.displayOrder.label=Display order
i18n_entity.FbDevVersion.fields.done.label=Completed
i18n_entity.FbDevVersion.fields.id.label=ID
i18n_entity.FbDevVersion.fields.modifiedBy.label=Last modifier
i18n_entity.FbDevVersion.fields.modifiedOn.label=Last modified time
i18n_entity.FbDevVersion.fields.name.label=Name
i18n_entity.FbDevVersion.fields.planDoneOn.label=Planned completion time
i18n_entity.FbDevVersion.fields.ro.label=Enterprise
i18n_entity.FbDevVersion.fields.version.label=Version
i18n_entity.FbDevVersion.group=Dev
i18n_entity.FbDevVersion.label=Iteration
i18n_entity.FbDirectPutawayOrder.fields.bin.label=Put away storage bin
i18n_entity.FbDirectPutawayOrder.fields.btInvProcessed.label=Inventory processed
i18n_entity.FbDirectPutawayOrder.fields.btLines.label=Single line
i18n_entity.FbDirectPutawayOrder.fields.btOrderKind.inlineOptionBill.items.MfFinishedInbound.label=Finished goods warehousing
i18n_entity.FbDirectPutawayOrder.fields.btOrderKind.inlineOptionBill.items.OtherInbound.label=Other storage
i18n_entity.FbDirectPutawayOrder.fields.btOrderKind.inlineOptionBill.items.PurchaseInbound.label=Procurement and warehousing
i18n_entity.FbDirectPutawayOrder.fields.btOrderKind.label=Type
i18n_entity.FbDirectPutawayOrder.fields.btOrderState.inlineOptionBill.items.Init.label=Not submitted
i18n_entity.FbDirectPutawayOrder.fields.btOrderState.inlineOptionBill.items.Submitted.label=Submitted
i18n_entity.FbDirectPutawayOrder.fields.btOrderState.label=Business status
i18n_entity.FbDirectPutawayOrder.fields.btOrderStateReason.label=Status description
i18n_entity.FbDirectPutawayOrder.fields.container.label=Put away the container
i18n_entity.FbDirectPutawayOrder.fields.createdBy.label=Founder
i18n_entity.FbDirectPutawayOrder.fields.createdOn.label=Creation time
i18n_entity.FbDirectPutawayOrder.fields.id.label=Order number
i18n_entity.FbDirectPutawayOrder.fields.modifiedBy.label=Last modifier
i18n_entity.FbDirectPutawayOrder.fields.modifiedOn.label=Last modified time
i18n_entity.FbDirectPutawayOrder.fields.remark.label=Remarks
i18n_entity.FbDirectPutawayOrder.fields.ro.label=Root tissue
i18n_entity.FbDirectPutawayOrder.fields.version.label=Version
i18n_entity.FbDirectPutawayOrder.group=Warehouse
i18n_entity.FbDirectPutawayOrder.label=Manual put away
i18n_entity.FbDirectPutawayOrder.listCard.bin.prefix=Put away storage bin
i18n_entity.FbDirectPutawayOrder.listCard.container.prefix=Put away the container
i18n_entity.FbDirectPutawayOrder.states.states.Init.label=Not submitted
i18n_entity.FbDirectPutawayOrder.states.states.Init.nextStates.Submitted.buttonLabel=Submit
i18n_entity.FbDirectPutawayOrder.states.states.Submitted.label=Submitted
i18n_entity.FbDirectPutawayOrderLine.fields.btLineNo.label=Line number
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterial.label=Materials
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialCategory.label=Material classification number
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialCategoryName.label=Material classification name
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialId.label=Material number
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialImage.label=Material pictures
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialModel.label=Material type
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialName.label=Material name
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialSpec.label=Material specification
i18n_entity.FbDirectPutawayOrderLine.fields.btParentId.label=Owned put away list
i18n_entity.FbDirectPutawayOrderLine.fields.createdBy.label=Founder
i18n_entity.FbDirectPutawayOrderLine.fields.createdOn.label=Creation time
i18n_entity.FbDirectPutawayOrderLine.fields.id.label=ID
i18n_entity.FbDirectPutawayOrderLine.fields.lotNo.label=Batch number
i18n_entity.FbDirectPutawayOrderLine.fields.modifiedBy.label=Last modifier
i18n_entity.FbDirectPutawayOrderLine.fields.modifiedOn.label=Last modified time
i18n_entity.FbDirectPutawayOrderLine.fields.price.label=Unit price
i18n_entity.FbDirectPutawayOrderLine.fields.qty.label=Quantity put away
i18n_entity.FbDirectPutawayOrderLine.fields.qualityLevel.label=Quality grade
i18n_entity.FbDirectPutawayOrderLine.fields.ro.label=Root tissue
i18n_entity.FbDirectPutawayOrderLine.fields.subContainerId.label=Grid number
i18n_entity.FbDirectPutawayOrderLine.fields.unitLabel.label=Unit name
i18n_entity.FbDirectPutawayOrderLine.fields.version.label=Version
i18n_entity.FbDirectPutawayOrderLine.group=Warehouse
i18n_entity.FbDirectPutawayOrderLine.label=Manual put away single line
i18n_entity.FbDirectPutawayOrderLine.listCard.btMaterialId.prefix=Material number
i18n_entity.FbDirectPutawayOrderLine.listCard.qty.prefix=Number
i18n_entity.FbDirectPutawayOrderLine.listCard.subContainerId.prefix=Grid number
i18n_entity.FbDistrict.fields.btDisabled.label=Deactivated
i18n_entity.FbDistrict.fields.createdBy.label=Founder
i18n_entity.FbDistrict.fields.createdOn.label=Creation time
i18n_entity.FbDistrict.fields.displayOrder.label=Display order
i18n_entity.FbDistrict.fields.id.label=Storage area number
i18n_entity.FbDistrict.fields.modifiedBy.label=Last modifier
i18n_entity.FbDistrict.fields.modifiedOn.label=Last modified time
i18n_entity.FbDistrict.fields.name.label=Storage area name
i18n_entity.FbDistrict.fields.remark.label=Remarks
i18n_entity.FbDistrict.fields.ro.label=Root tissue
i18n_entity.FbDistrict.fields.structure.label=Storage area structure
i18n_entity.FbDistrict.fields.version.label=Version
i18n_entity.FbDistrict.fields.warehouse.label=Owned warehouse
i18n_entity.FbDistrict.group=MainData
i18n_entity.FbDistrict.label=Storage area
i18n_entity.FbGoodsOwner.fields.address.label=Address
i18n_entity.FbGoodsOwner.fields.btDisabled.label=Deactivated
i18n_entity.FbGoodsOwner.fields.contact.label=Contact person
i18n_entity.FbGoodsOwner.fields.createdBy.label=Founder
i18n_entity.FbGoodsOwner.fields.createdOn.label=Creation time
i18n_entity.FbGoodsOwner.fields.id.label=Shipper code
i18n_entity.FbGoodsOwner.fields.modifiedBy.label=Last modifier
i18n_entity.FbGoodsOwner.fields.modifiedOn.label=Last modified time
i18n_entity.FbGoodsOwner.fields.name.label=Name of consignor
i18n_entity.FbGoodsOwner.fields.phone.label=Contact phone number
i18n_entity.FbGoodsOwner.fields.remark.label=Remarks
i18n_entity.FbGoodsOwner.fields.ro.label=Root tissue
i18n_entity.FbGoodsOwner.fields.version.label=Version
i18n_entity.FbGoodsOwner.group=MainData
i18n_entity.FbGoodsOwner.label=Shipper
i18n_entity.FbGoodsOwner.listCard.contact.prefix=Contact person
i18n_entity.FbGoodsOwner.listCard.name.prefix=Name of shipper
i18n_entity.FbGoodsOwner.listCard.phone.prefix=Telephone
i18n_entity.FbInboundOrder.fields.asnId.label=Arrival notification number
i18n_entity.FbInboundOrder.fields.btInvProcessed.label=Inventory processed
i18n_entity.FbInboundOrder.fields.btLines.label=Single line
i18n_entity.FbInboundOrder.fields.btOrderKind.inlineOptionBill.items.MfFinishedInbound.label=Finished goods warehousing
i18n_entity.FbInboundOrder.fields.btOrderKind.inlineOptionBill.items.OtherInbound.label=Other storage
i18n_entity.FbInboundOrder.fields.btOrderKind.inlineOptionBill.items.PurchaseInbound.label=Procurement and warehousing
i18n_entity.FbInboundOrder.fields.btOrderKind.label=Type
i18n_entity.FbInboundOrder.fields.btOrderState.inlineOptionBill.items.Committed.label=Submitted
i18n_entity.FbInboundOrder.fields.btOrderState.inlineOptionBill.items.Init.label=Not submitted
i18n_entity.FbInboundOrder.fields.btOrderState.label=Business status
i18n_entity.FbInboundOrder.fields.btOrderStateReason.label=Status description
i18n_entity.FbInboundOrder.fields.callContainerAll.label=Called empty container
i18n_entity.FbInboundOrder.fields.createdBy.label=Founder
i18n_entity.FbInboundOrder.fields.createdOn.label=Creation time
i18n_entity.FbInboundOrder.fields.district.label=Inbound storage area
i18n_entity.FbInboundOrder.fields.id.label=Order number
i18n_entity.FbInboundOrder.fields.mfgFQCOrderId.label=Finished product quality inspection number
i18n_entity.FbInboundOrder.fields.mfgOrderId.label=Production ticket number
i18n_entity.FbInboundOrder.fields.modifiedBy.label=Last modifier
i18n_entity.FbInboundOrder.fields.modifiedOn.label=Last modified time
i18n_entity.FbInboundOrder.fields.planQty.label=Quantity received
i18n_entity.FbInboundOrder.fields.purchaseOrderId.label=Purchase order number
i18n_entity.FbInboundOrder.fields.qty.label=Quantity received this time
i18n_entity.FbInboundOrder.fields.recevingOrderId.label=Receipt number
i18n_entity.FbInboundOrder.fields.remark.label=Remarks
i18n_entity.FbInboundOrder.fields.ro.label=Root tissue
i18n_entity.FbInboundOrder.fields.vendor.label=Suppliers
i18n_entity.FbInboundOrder.fields.version.label=Version
i18n_entity.FbInboundOrder.fields.warehouse.label=Inbound warehouse
i18n_entity.FbInboundOrder.group=Warehouse
i18n_entity.FbInboundOrder.label=Warehouse receipt
i18n_entity.FbInboundOrder.states.states.Committed.label=Submitted
i18n_entity.FbInboundOrder.states.states.Init.label=Not submitted
i18n_entity.FbInboundOrder.states.states.Init.nextStates.Committed.buttonLabel=Submit
i18n_entity.FbInboundOrderLine.fields.bin.label=Storage bin
i18n_entity.FbInboundOrderLine.fields.btLineNo.label=Line number
i18n_entity.FbInboundOrderLine.fields.btMaterial.label=Materials
i18n_entity.FbInboundOrderLine.fields.btMaterialCategory.label=Material classification number
i18n_entity.FbInboundOrderLine.fields.btMaterialCategoryName.label=Material classification name
i18n_entity.FbInboundOrderLine.fields.btMaterialId.label=Material number
i18n_entity.FbInboundOrderLine.fields.btMaterialImage.label=Material pictures
i18n_entity.FbInboundOrderLine.fields.btMaterialModel.label=Material type
i18n_entity.FbInboundOrderLine.fields.btMaterialName.label=Material name
i18n_entity.FbInboundOrderLine.fields.btMaterialSpec.label=Material specification
i18n_entity.FbInboundOrderLine.fields.btParentId.label=Owned receipt
i18n_entity.FbInboundOrderLine.fields.callContainerQty.label=Allocated container quantity
i18n_entity.FbInboundOrderLine.fields.createdBy.label=Founder
i18n_entity.FbInboundOrderLine.fields.createdOn.label=Creation time
i18n_entity.FbInboundOrderLine.fields.finishedQty.label=Previous inventory quantity
i18n_entity.FbInboundOrderLine.fields.id.label=ID
i18n_entity.FbInboundOrderLine.fields.lotNo.label=Batch number
i18n_entity.FbInboundOrderLine.fields.materialName.label=Material name
i18n_entity.FbInboundOrderLine.fields.modifiedBy.label=Last modifier
i18n_entity.FbInboundOrderLine.fields.modifiedOn.label=Last modified time
i18n_entity.FbInboundOrderLine.fields.planQty.label=Quantity received
i18n_entity.FbInboundOrderLine.fields.price.label=Unit price
i18n_entity.FbInboundOrderLine.fields.qty.label=Inventory quantity
i18n_entity.FbInboundOrderLine.fields.qualityLevel.label=Quality grade
i18n_entity.FbInboundOrderLine.fields.ro.label=Root tissue
i18n_entity.FbInboundOrderLine.fields.storeQty.label=Quantity put away
i18n_entity.FbInboundOrderLine.fields.unitLabel.label=Unit name
i18n_entity.FbInboundOrderLine.fields.version.label=Version
i18n_entity.FbInboundOrderLine.group=Warehouse
i18n_entity.FbInboundOrderLine.label=Warehouse single line
i18n_entity.FbInboundOrderLine.listCard.btMaterialId.prefix=Material number
i18n_entity.FbInboundOrderLine.listCard.callContainerQty.prefix=Distribution container
i18n_entity.FbInboundOrderLine.listCard.qty.prefix=Storage
i18n_entity.FbInboundOrderLine.listCard.storeQty.prefix=Put away
i18n_entity.FbInvChange.fields.btMaterial.label=Materials
i18n_entity.FbInvChange.fields.btMaterialCategory.label=Material classification number
i18n_entity.FbInvChange.fields.btMaterialCategoryName.label=Material classification name
i18n_entity.FbInvChange.fields.btMaterialId.label=Material number
i18n_entity.FbInvChange.fields.btMaterialImage.label=Material pictures
i18n_entity.FbInvChange.fields.btMaterialModel.label=Material type
i18n_entity.FbInvChange.fields.btMaterialName.label=Material name
i18n_entity.FbInvChange.fields.btMaterialSpec.label=Material specification
i18n_entity.FbInvChange.fields.container.label=Container
i18n_entity.FbInvChange.fields.createdBy.label=Founder
i18n_entity.FbInvChange.fields.createdOn.label=Creation time
i18n_entity.FbInvChange.fields.id.label=ID
i18n_entity.FbInvChange.fields.lotNo.label=Batch
i18n_entity.FbInvChange.fields.modifiedBy.label=Last modifier
i18n_entity.FbInvChange.fields.modifiedOn.label=Last modified time
i18n_entity.FbInvChange.fields.qty.label=Number
i18n_entity.FbInvChange.fields.subContainerId.label=Grid number
i18n_entity.FbInvChange.fields.version.label=Revised version
i18n_entity.FbInvChange.group=Warehouse
i18n_entity.FbInvChange.label=Inventory change
i18n_entity.FbInvLayout.fields.amount.label=Amount
i18n_entity.FbInvLayout.fields.assemblyLine.label=Affiliated production line
i18n_entity.FbInvLayout.fields.bin.label=The storage bin
i18n_entity.FbInvLayout.fields.btBzDesc.label=Business description
i18n_entity.FbInvLayout.fields.btBzMark.label=Business marking
i18n_entity.FbInvLayout.fields.btMaterial.label=Materials
i18n_entity.FbInvLayout.fields.btMaterialCategory.label=Material classification number
i18n_entity.FbInvLayout.fields.btMaterialCategoryName.label=Material classification name
i18n_entity.FbInvLayout.fields.btMaterialId.label=Material number
i18n_entity.FbInvLayout.fields.btMaterialImage.label=Material pictures
i18n_entity.FbInvLayout.fields.btMaterialModel.label=Material type
i18n_entity.FbInvLayout.fields.btMaterialName.label=Material name
i18n_entity.FbInvLayout.fields.btMaterialSpec.label=Material specification
i18n_entity.FbInvLayout.fields.btMaterialTopCategory.label=Material classification number
i18n_entity.FbInvLayout.fields.btMaterialTopCategoryName.label=Material classification name
i18n_entity.FbInvLayout.fields.btPrepare.label=btPrepare
i18n_entity.FbInvLayout.fields.channel.label=Aisle
i18n_entity.FbInvLayout.fields.column.label=List
i18n_entity.FbInvLayout.fields.createdBy.label=Founder
i18n_entity.FbInvLayout.fields.createdOn.label=Creation time
i18n_entity.FbInvLayout.fields.depth.label=Deep
i18n_entity.FbInvLayout.fields.district.label=The storage area
i18n_entity.FbInvLayout.fields.expDate.label=Valid period
i18n_entity.FbInvLayout.fields.id.label=ID
i18n_entity.FbInvLayout.fields.inboundOn.label=Storage time
i18n_entity.FbInvLayout.fields.inboundOrderId.label=Warehouse order number
i18n_entity.FbInvLayout.fields.inboundOrderLineNo.label=Warehouse order line number
i18n_entity.FbInvLayout.fields.layer.label=Layer
i18n_entity.FbInvLayout.fields.leafContainer.label=Innermost container
i18n_entity.FbInvLayout.fields.leafContainerType.label=Innermost container type
i18n_entity.FbInvLayout.fields.locked.label=Lock
i18n_entity.FbInvLayout.fields.locked.listBatchButtons.buttons[0].label=Unlock
i18n_entity.FbInvLayout.fields.lotNo.label=Batch number
i18n_entity.FbInvLayout.fields.matLotNo.label=Batch
i18n_entity.FbInvLayout.fields.matSerialNo.label=Serial number
i18n_entity.FbInvLayout.fields.mfgDate.label=Production date
i18n_entity.FbInvLayout.fields.modifiedBy.label=Last modifier
i18n_entity.FbInvLayout.fields.modifiedOn.label=Last modified time
i18n_entity.FbInvLayout.fields.onRobot.label=The robot
i18n_entity.FbInvLayout.fields.outboundOrderId.label=Outbound order number
i18n_entity.FbInvLayout.fields.outboundOrderLineNo.label=Outbound order line number
i18n_entity.FbInvLayout.fields.owner.label=Shipper
i18n_entity.FbInvLayout.fields.price.label=Unit price
i18n_entity.FbInvLayout.fields.qty.label=Number
i18n_entity.FbInvLayout.fields.rack.label=Shelves
i18n_entity.FbInvLayout.fields.refInv.label=Reference inventory details
i18n_entity.FbInvLayout.fields.robotBin.label=The storage bin of the robot
i18n_entity.FbInvLayout.fields.row.label=Row
i18n_entity.FbInvLayout.fields.state.inlineOptionBill.items.Assigned.label=Allocated
i18n_entity.FbInvLayout.fields.state.inlineOptionBill.items.Received.label=Received
i18n_entity.FbInvLayout.fields.state.inlineOptionBill.items.Storing.label=In storage
i18n_entity.FbInvLayout.fields.state.label=Stock status
i18n_entity.FbInvLayout.fields.subContainerId.label=Lattice
i18n_entity.FbInvLayout.fields.topContainer.label=Outermost container
i18n_entity.FbInvLayout.fields.topContainerType.label=Outermost container type
i18n_entity.FbInvLayout.fields.usedQty.label=Allocated quantity
i18n_entity.FbInvLayout.fields.validityDay.label=Valid period days
i18n_entity.FbInvLayout.fields.vendor.label=Suppliers
i18n_entity.FbInvLayout.fields.version.label=Revised version
i18n_entity.FbInvLayout.fields.warehouse.label=Warehouse
i18n_entity.FbInvLayout.fields.workSite.label=Workstation
i18n_entity.FbInvLayout.group=Warehouse
i18n_entity.FbInvLayout.label=Inventory details
i18n_entity.FbInvLayout.listCard.bin.prefix=Storage bin
i18n_entity.FbInvLayout.listCard.inboundOn.prefix=Storage time
i18n_entity.FbInvLayout.listCard.lotNo.prefix=Batch number
i18n_entity.FbInvLayout.listCard.qty.prefix=Number
i18n_entity.FbInvLayout.listCard.topContainer.prefix=Container
i18n_entity.FbInvSnapShot.fields.btMaterial.label=Materials
i18n_entity.FbInvSnapShot.fields.btMaterialCategory.label=Material classification number
i18n_entity.FbInvSnapShot.fields.btMaterialCategoryName.label=Material classification name
i18n_entity.FbInvSnapShot.fields.btMaterialId.label=Material number
i18n_entity.FbInvSnapShot.fields.btMaterialImage.label=Material pictures
i18n_entity.FbInvSnapShot.fields.btMaterialModel.label=Material type
i18n_entity.FbInvSnapShot.fields.btMaterialName.label=Material name
i18n_entity.FbInvSnapShot.fields.btMaterialSpec.label=Material specification
i18n_entity.FbInvSnapShot.fields.createdBy.label=Founder
i18n_entity.FbInvSnapShot.fields.createdOn.label=Creation time
i18n_entity.FbInvSnapShot.fields.id.label=ID
i18n_entity.FbInvSnapShot.fields.modifiedBy.label=Last modifier
i18n_entity.FbInvSnapShot.fields.modifiedOn.label=Last modified time
i18n_entity.FbInvSnapShot.fields.qty.label=Number
i18n_entity.FbInvSnapShot.fields.uuid.label=uuid
i18n_entity.FbInvSnapShot.fields.version.label=Revised version
i18n_entity.FbInvSnapShot.group=Warehouse
i18n_entity.FbInvSnapShot.label=Stock snapshot
i18n_entity.FbMaterial.fields.abc.label=ABC classification
i18n_entity.FbMaterial.fields.btDisabled.label=Deactivated
i18n_entity.FbMaterial.fields.categoriesDesc.label=Material classification description
i18n_entity.FbMaterial.fields.category1.label=Primary classification
i18n_entity.FbMaterial.fields.category2.label=Sub-category
i18n_entity.FbMaterial.fields.category3.label=Tertiary classification
i18n_entity.FbMaterial.fields.createdBy.label=Founder
i18n_entity.FbMaterial.fields.createdOn.label=Creation time
i18n_entity.FbMaterial.fields.displayDecimals.label=Number of decimal places
i18n_entity.FbMaterial.fields.endOn.label=Deactivation date
i18n_entity.FbMaterial.fields.height.label=Highly
i18n_entity.FbMaterial.fields.id.label=Material code
i18n_entity.FbMaterial.fields.image.label=Picture
i18n_entity.FbMaterial.fields.leafCategory.label=Material classification
i18n_entity.FbMaterial.fields.length.label=Length
i18n_entity.FbMaterial.fields.mainUnit.label=Main unit of measurement name
i18n_entity.FbMaterial.fields.mainVendor.label=Main supplier
i18n_entity.FbMaterial.fields.mixLotNo.label=Batch mixing
i18n_entity.FbMaterial.fields.mixMaterial.label=Material mixing
i18n_entity.FbMaterial.fields.model.label=Model number
i18n_entity.FbMaterial.fields.modifiedBy.label=Last modifier
i18n_entity.FbMaterial.fields.modifiedOn.label=Last modified time
i18n_entity.FbMaterial.fields.name.label=Material name
i18n_entity.FbMaterial.fields.owner.label=Shipper
i18n_entity.FbMaterial.fields.price.label=Unit price
i18n_entity.FbMaterial.fields.remark.label=Remarks
i18n_entity.FbMaterial.fields.ro.label=Root tissue
i18n_entity.FbMaterial.fields.spec.label=Material specification
i18n_entity.FbMaterial.fields.startOn.label=Enabling date
i18n_entity.FbMaterial.fields.syncOut.label=External import
i18n_entity.FbMaterial.fields.topCat.label=Inventory category name
i18n_entity.FbMaterial.fields.unit.label=Material unit
i18n_entity.FbMaterial.fields.unitLabel.label=Unit name
i18n_entity.FbMaterial.fields.version.label=Version
i18n_entity.FbMaterial.fields.volume.label=Volume
i18n_entity.FbMaterial.fields.weight.label=Weight
i18n_entity.FbMaterial.fields.width.label=Width
i18n_entity.FbMaterial.group=MainData
i18n_entity.FbMaterial.label=Materials
i18n_entity.FbMaterial.listCard.leafCategory.prefix=Material classification
i18n_entity.FbMaterialCategory.fields.btDisabled.label=Deactivated
i18n_entity.FbMaterialCategory.fields.createdBy.label=Founder
i18n_entity.FbMaterialCategory.fields.createdOn.label=Creation time
i18n_entity.FbMaterialCategory.fields.id.label=Classification number
i18n_entity.FbMaterialCategory.fields.modifiedBy.label=Last modifier
i18n_entity.FbMaterialCategory.fields.modifiedOn.label=Last modified time
i18n_entity.FbMaterialCategory.fields.name.label=Name
i18n_entity.FbMaterialCategory.fields.parent.label=Superior classification
i18n_entity.FbMaterialCategory.fields.remark.label=Remarks
i18n_entity.FbMaterialCategory.fields.storeDistricts.label=Storage storage area
i18n_entity.FbMaterialCategory.fields.version.label=Revised version
i18n_entity.FbMaterialCategory.group=MainData
i18n_entity.FbMaterialCategory.label=Material classification
i18n_entity.FbMaterialCategory.listCard.storeDistricts.prefix=Storage storage area
i18n_entity.FbMaterialContainerMaxQty.fields.btDisabled.label=Deactivated
i18n_entity.FbMaterialContainerMaxQty.fields.btMaterial.label=Materials
i18n_entity.FbMaterialContainerMaxQty.fields.containerType.label=Container type
i18n_entity.FbMaterialContainerMaxQty.fields.createdBy.label=Founder
i18n_entity.FbMaterialContainerMaxQty.fields.createdOn.label=Creation time
i18n_entity.FbMaterialContainerMaxQty.fields.id.label=ID
i18n_entity.FbMaterialContainerMaxQty.fields.maxQty.label=Put up to a few
i18n_entity.FbMaterialContainerMaxQty.fields.modifiedBy.label=Last modifier
i18n_entity.FbMaterialContainerMaxQty.fields.modifiedOn.label=Last modified time
i18n_entity.FbMaterialContainerMaxQty.fields.version.label=Revised version
i18n_entity.FbMaterialContainerMaxQty.group=MainData
i18n_entity.FbMaterialContainerMaxQty.label=Material container capacity
i18n_entity.FbMaterialContainerMaxQty.listCard.btMaterial.prefix=Storage of materials
i18n_entity.FbMaterialContainerMaxQty.listCard.maxQty.prefix=Put at most
i18n_entity.FbMaterialContainerMaxQty.listCard.maxQty.suffix=A
i18n_entity.FbMaterialLot.fields.createdBy.label=Founder
i18n_entity.FbMaterialLot.fields.createdOn.label=Creation time
i18n_entity.FbMaterialLot.fields.disabled.label=Disable
i18n_entity.FbMaterialLot.fields.id.label=Production batch number
i18n_entity.FbMaterialLot.fields.material.label=Owned materials
i18n_entity.FbMaterialLot.fields.modifiedBy.label=Last modifier
i18n_entity.FbMaterialLot.fields.modifiedOn.label=Last modified time
i18n_entity.FbMaterialLot.fields.name.label=Production batch name
i18n_entity.FbMaterialLot.fields.remark.label=Remarks
i18n_entity.FbMaterialLot.fields.ro.label=Root tissue
i18n_entity.FbMaterialLot.fields.version.label=Version
i18n_entity.FbMaterialLot.group=MainData
i18n_entity.FbMaterialLot.label=Batch of material
i18n_entity.FbMaterialUnit.fields.basic.label=Basic unit
i18n_entity.FbMaterialUnit.fields.createdBy.label=Founder
i18n_entity.FbMaterialUnit.fields.createdOn.label=Creation time
i18n_entity.FbMaterialUnit.fields.disabled.label=Disable
i18n_entity.FbMaterialUnit.fields.id.label=Unit code
i18n_entity.FbMaterialUnit.fields.modifiedBy.label=Last modifier
i18n_entity.FbMaterialUnit.fields.modifiedOn.label=Last modified time
i18n_entity.FbMaterialUnit.fields.name.label=Unit name
i18n_entity.FbMaterialUnit.fields.parent.label=Parent unit
i18n_entity.FbMaterialUnit.fields.ratio.label=Conversion relation
i18n_entity.FbMaterialUnit.fields.remark.label=Remarks
i18n_entity.FbMaterialUnit.fields.ro.label=Root tissue
i18n_entity.FbMaterialUnit.fields.version.label=Version
i18n_entity.FbMaterialUnit.group=MainData
i18n_entity.FbMaterialUnit.label=Material unit
i18n_entity.FbOutboundOrder.fields.btInvProcessed.label=Inventory processed
i18n_entity.FbOutboundOrder.fields.btLines.label=Single line
i18n_entity.FbOutboundOrder.fields.btOrderKind.inlineOptionBill.items.Product.label=Outbound goods
i18n_entity.FbOutboundOrder.fields.btOrderKind.label=Type
i18n_entity.FbOutboundOrder.fields.btOrderState.inlineOptionBill.items.Committed.label=Submitted
i18n_entity.FbOutboundOrder.fields.btOrderState.inlineOptionBill.items.Init.label=Not submitted
i18n_entity.FbOutboundOrder.fields.btOrderState.label=Business status
i18n_entity.FbOutboundOrder.fields.btOrderStateReason.label=Status description
i18n_entity.FbOutboundOrder.fields.createdBy.label=Founder
i18n_entity.FbOutboundOrder.fields.createdOn.label=Creation time
i18n_entity.FbOutboundOrder.fields.customer.label=Customers
i18n_entity.FbOutboundOrder.fields.district.label=Outbound storage area
i18n_entity.FbOutboundOrder.fields.id.label=Order number
i18n_entity.FbOutboundOrder.fields.invAssignedAll.label=Inventory distribution completed
i18n_entity.FbOutboundOrder.fields.modifiedBy.label=Last modifier
i18n_entity.FbOutboundOrder.fields.modifiedOn.label=Last modified time
i18n_entity.FbOutboundOrder.fields.planQty.label=Planned outbound quantity
i18n_entity.FbOutboundOrder.fields.priority.label=Priority
i18n_entity.FbOutboundOrder.fields.qty.label=Quantity shipped this time.
i18n_entity.FbOutboundOrder.fields.receiver.label=Receiving unit
i18n_entity.FbOutboundOrder.fields.remark.label=Remarks
i18n_entity.FbOutboundOrder.fields.ro.label=Root tissue
i18n_entity.FbOutboundOrder.fields.saleOrderId.label=Sales order number
i18n_entity.FbOutboundOrder.fields.saleShipOrderId.label=Sales invoice
i18n_entity.FbOutboundOrder.fields.version.label=Version
i18n_entity.FbOutboundOrder.fields.warehouse.label=Outbound warehouse
i18n_entity.FbOutboundOrder.group=Warehouse
i18n_entity.FbOutboundOrder.label=Delivery order
i18n_entity.FbOutboundOrder.listCard.priority.prefix=Priority
i18n_entity.FbOutboundOrder.states.states.Committed.label=Submitted
i18n_entity.FbOutboundOrder.states.states.Init.label=Not submitted
i18n_entity.FbOutboundOrder.states.states.Init.nextStates.Committed.buttonLabel=Submit
i18n_entity.FbOutboundOrderLine.fields.btInvRefAll.label=Total stock
i18n_entity.FbOutboundOrderLine.fields.btInvRefMatch.label=Available stock
i18n_entity.FbOutboundOrderLine.fields.btLineNo.label=Line number
i18n_entity.FbOutboundOrderLine.fields.btMaterial.label=Materials
i18n_entity.FbOutboundOrderLine.fields.btMaterialCategory.label=Material classification number
i18n_entity.FbOutboundOrderLine.fields.btMaterialCategoryName.label=Material classification name
i18n_entity.FbOutboundOrderLine.fields.btMaterialId.label=Material number
i18n_entity.FbOutboundOrderLine.fields.btMaterialImage.label=Material pictures
i18n_entity.FbOutboundOrderLine.fields.btMaterialModel.label=Material type
i18n_entity.FbOutboundOrderLine.fields.btMaterialName.label=Material name
i18n_entity.FbOutboundOrderLine.fields.btMaterialSpec.label=Material specification
i18n_entity.FbOutboundOrderLine.fields.btParentId.label=Owned outbound order
i18n_entity.FbOutboundOrderLine.fields.createdBy.label=Founder
i18n_entity.FbOutboundOrderLine.fields.createdOn.label=Creation time
i18n_entity.FbOutboundOrderLine.fields.finishedQty.label=Previous outbound quantity
i18n_entity.FbOutboundOrderLine.fields.id.label=ID
i18n_entity.FbOutboundOrderLine.fields.invAssignedQty.label=Allocated stock quantity
i18n_entity.FbOutboundOrderLine.fields.lotNo.label=Batch number
i18n_entity.FbOutboundOrderLine.fields.materialName.label=Material name
i18n_entity.FbOutboundOrderLine.fields.modifiedBy.label=Last modifier
i18n_entity.FbOutboundOrderLine.fields.modifiedOn.label=Last modified time
i18n_entity.FbOutboundOrderLine.fields.planQty.label=Total planned outbound
i18n_entity.FbOutboundOrderLine.fields.qty.label=Outbound quantity
i18n_entity.FbOutboundOrderLine.fields.qualityLevel.label=Quality grade
i18n_entity.FbOutboundOrderLine.fields.ro.label=Root tissue
i18n_entity.FbOutboundOrderLine.fields.version.label=Version
i18n_entity.FbOutboundOrderLine.group=Warehouse
i18n_entity.FbOutboundOrderLine.label=Outbound single line
i18n_entity.FbOutboundOrderLine.listCard.btMaterialId.prefix=Material number
i18n_entity.FbOutboundOrderLine.listCard.invAssignedQty.prefix=Allocated stock
i18n_entity.FbOutboundOrderLine.listCard.lotNo.prefix=Batch number
i18n_entity.FbOutboundOrderLine.listCard.qty.prefix=Number
i18n_entity.FbPkg.fields.createdBy.label=Founder
i18n_entity.FbPkg.fields.createdOn.label=Creation time
i18n_entity.FbPkg.fields.disabled.label=Disable
i18n_entity.FbPkg.fields.id.label=Packaging code
i18n_entity.FbPkg.fields.material.label=Owned materials
i18n_entity.FbPkg.fields.modifiedBy.label=Last modifier
i18n_entity.FbPkg.fields.modifiedOn.label=Last modified time
i18n_entity.FbPkg.fields.name.label=Packaging name
i18n_entity.FbPkg.fields.purpose.label=Packaging purposes
i18n_entity.FbPkg.fields.qty.label=Number of packages
i18n_entity.FbPkg.fields.remark.label=Remarks
i18n_entity.FbPkg.fields.ro.label=Root tissue
i18n_entity.FbPkg.fields.version.label=Version
i18n_entity.FbPkg.group=MainData
i18n_entity.FbPkg.label=Packaging specifications
i18n_entity.FbPkg.listCard.disabled.formatMapping[0].replaceText=Deactivated
i18n_entity.FbPkg.listCard.material.prefix=Owned materials
i18n_entity.FbPkg.listCard.name.prefix=Packaging name
i18n_entity.FbPkg.listCard.qty.prefix=Put at most
i18n_entity.FbPkg.listCard.qty.suffix=A
i18n_entity.FbVendor.fields.address.label=Address
i18n_entity.FbVendor.fields.btDisabled.label=Deactivated
i18n_entity.FbVendor.fields.contact.label=Contact person
i18n_entity.FbVendor.fields.createdBy.label=Founder
i18n_entity.FbVendor.fields.createdOn.label=Creation time
i18n_entity.FbVendor.fields.email.label=Email address
i18n_entity.FbVendor.fields.id.label=Supplier number
i18n_entity.FbVendor.fields.level.label=Level
i18n_entity.FbVendor.fields.modifiedBy.label=Last modifier
i18n_entity.FbVendor.fields.modifiedOn.label=Last modified time
i18n_entity.FbVendor.fields.name.label=Supplier name
i18n_entity.FbVendor.fields.phone.label=Contact phone number
i18n_entity.FbVendor.fields.remark.label=Remarks
i18n_entity.FbVendor.fields.ro.label=Root tissue
i18n_entity.FbVendor.fields.version.label=Version
i18n_entity.FbVendor.group=MainData
i18n_entity.FbVendor.label=Suppliers
i18n_entity.FbVendor.listCard.contact.prefix=Contact person
i18n_entity.FbVendor.listCard.name.prefix=Name
i18n_entity.FbVendor.listCard.phone.prefix=Telephone
i18n_entity.FbWarehouse.fields.address.label=Address
i18n_entity.FbWarehouse.fields.btDisabled.label=Deactivated
i18n_entity.FbWarehouse.fields.contact.label=Contact person
i18n_entity.FbWarehouse.fields.createdBy.label=Founder
i18n_entity.FbWarehouse.fields.createdOn.label=Creation time
i18n_entity.FbWarehouse.fields.defaultBin.label=Default storage bin
i18n_entity.FbWarehouse.fields.displayOrder.label=Display order
i18n_entity.FbWarehouse.fields.id.label=Warehouse number
i18n_entity.FbWarehouse.fields.latitude.label=Location latitude
i18n_entity.FbWarehouse.fields.longitude.label=Longitude of position
i18n_entity.FbWarehouse.fields.modifiedBy.label=Last modifier
i18n_entity.FbWarehouse.fields.modifiedOn.label=Last modified time
i18n_entity.FbWarehouse.fields.name.label=Warehouse name
i18n_entity.FbWarehouse.fields.phone.label=Contact phone number
i18n_entity.FbWarehouse.fields.remark.label=Remarks
i18n_entity.FbWarehouse.fields.ro.label=Root tissue
i18n_entity.FbWarehouse.fields.version.label=Version
i18n_entity.FbWarehouse.fields.volume.label=Volume
i18n_entity.FbWarehouse.group=MainData
i18n_entity.FbWarehouse.label=Warehouse
i18n_entity.FbWarehouse.listCard.btDisabled.formatMapping[0].replaceText=Deactivated
i18n_entity.FbWorkPosition.fields.createdBy.label=Founder
i18n_entity.FbWorkPosition.fields.createdOn.label=Creation time
i18n_entity.FbWorkPosition.fields.disabled.label=Disable
i18n_entity.FbWorkPosition.fields.id.label=Order number
i18n_entity.FbWorkPosition.fields.modifiedBy.label=Last modifier
i18n_entity.FbWorkPosition.fields.modifiedOn.label=Last modified time
i18n_entity.FbWorkPosition.fields.name.label=Name
i18n_entity.FbWorkPosition.fields.remark.label=Remarks
i18n_entity.FbWorkPosition.fields.ro.label=Root tissue
i18n_entity.FbWorkPosition.fields.version.label=Version
i18n_entity.FbWorkPosition.group=MainData
i18n_entity.FbWorkPosition.label=Position
i18n_entity.FbWorkPosition.listCard.disabled.formatMapping[0].replaceText=Deactivated
i18n_entity.FbWorkSite.fields.bin.label=Owned storage bin
i18n_entity.FbWorkSite.fields.createdBy.label=Founder
i18n_entity.FbWorkSite.fields.createdOn.label=Creation time
i18n_entity.FbWorkSite.fields.disabled.label=Disable
i18n_entity.FbWorkSite.fields.id.label=Station coding
i18n_entity.FbWorkSite.fields.kind.label=Station type
i18n_entity.FbWorkSite.fields.line.label=Affiliated production line
i18n_entity.FbWorkSite.fields.modifiedBy.label=Last modifier
i18n_entity.FbWorkSite.fields.modifiedOn.label=Last modified time
i18n_entity.FbWorkSite.fields.name.label=Station name
i18n_entity.FbWorkSite.fields.position.label=Position
i18n_entity.FbWorkSite.fields.remark.label=Remarks
i18n_entity.FbWorkSite.fields.ro.label=Root tissue
i18n_entity.FbWorkSite.fields.version.label=Version
i18n_entity.FbWorkSite.group=MainData
i18n_entity.FbWorkSite.label=Workstation
i18n_entity.FbWorkSite.listCard.bin.prefix=Owned storage bin
i18n_entity.FbWorkSite.listCard.disabled.formatMapping[0].replaceText=Deactivated
i18n_entity.FbWorkSite.listCard.kind.prefix=Station type
i18n_entity.FbWorkSite.listCard.name.prefix=Workstation name
i18n_entity.FbWorkSite.listCard.position.prefix=Position
i18n_entity.HaiMockRobot.fields.battery.label=Battery level
i18n_entity.HaiMockRobot.fields.createdBy.label=Founder
i18n_entity.HaiMockRobot.fields.createdOn.label=Creation time
i18n_entity.HaiMockRobot.fields.id.label=ID
i18n_entity.HaiMockRobot.fields.modifiedBy.label=Last modifier
i18n_entity.HaiMockRobot.fields.modifiedOn.label=Last modified time
i18n_entity.HaiMockRobot.fields.posX.label=Location X
i18n_entity.HaiMockRobot.fields.posY.label=Location Y
i18n_entity.HaiMockRobot.fields.version.label=Revised version
i18n_entity.HaiMockRobot.group=WCS
i18n_entity.HaiMockRobot.label=Hai simulation robot
i18n_entity.HaiMockRobot.listCard.battery.prefix=Battery level
i18n_entity.HaiMockRobot.listCard.posX.prefix=Location X
i18n_entity.HaiMockRobot.listCard.posY.prefix=Location Y
i18n_entity.HikResourcePack.fields.active.label=Main configuration
i18n_entity.HikResourcePack.fields.createdBy.label=Founder
i18n_entity.HikResourcePack.fields.createdOn.label=Creation time
i18n_entity.HikResourcePack.fields.id.label=ID
i18n_entity.HikResourcePack.fields.lmap.label=Laser point cloud file lmap
i18n_entity.HikResourcePack.fields.modifiedBy.label=Last modifier
i18n_entity.HikResourcePack.fields.modifiedOn.label=Last modified time
i18n_entity.HikResourcePack.fields.podConfig.label=Shelf Configuration XML
i18n_entity.HikResourcePack.fields.remark.label=Remarks
i18n_entity.HikResourcePack.fields.version.label=Revised version
i18n_entity.HikResourcePack.group=WCS
i18n_entity.HikResourcePack.label=Hik effect package
i18n_entity.HikResourcePack.listCard.version.prefix=Version
i18n_entity.HumanUser.fields.btDisabled.label=Deactivated
i18n_entity.HumanUser.fields.company.label=Company
i18n_entity.HumanUser.fields.createdBy.label=Founder
i18n_entity.HumanUser.fields.createdOn.label=Creation time
i18n_entity.HumanUser.fields.directSignInDisabled.label=Prohibit direct login
i18n_entity.HumanUser.fields.disabled.label=Disable
i18n_entity.HumanUser.fields.email.label=Mail
i18n_entity.HumanUser.fields.externalAdded.label=Externally added
i18n_entity.HumanUser.fields.externalSource.label=External sources
i18n_entity.HumanUser.fields.externalUserId.label=External user ID
i18n_entity.HumanUser.fields.id.label=Number
i18n_entity.HumanUser.fields.modifiedBy.label=Last modifier
i18n_entity.HumanUser.fields.modifiedOn.label=Last modified time
i18n_entity.HumanUser.fields.password.label=Password
i18n_entity.HumanUser.fields.phone.label=Mobile phone
i18n_entity.HumanUser.fields.pwdErrCount.label=Password error count
i18n_entity.HumanUser.fields.pwdSetOn.label=Password setting time
i18n_entity.HumanUser.fields.ro.label=Root tissue
i18n_entity.HumanUser.fields.roAdmin.label=Enterprise administrators
i18n_entity.HumanUser.fields.roleIds.label=Role
i18n_entity.HumanUser.fields.truename.label=Real name
i18n_entity.HumanUser.fields.username.label=Username
i18n_entity.HumanUser.fields.version.label=Version
i18n_entity.HumanUser.group=User
i18n_entity.HumanUser.label=User
i18n_entity.HumanUser.listCard.phone.prefix=Mobile phone
i18n_entity.HumanUser.listCard.truename.prefix=Real name
i18n_entity.HumanUserSession.fields.createdBy.label=Founder
i18n_entity.HumanUserSession.fields.createdOn.label=Creation time
i18n_entity.HumanUserSession.fields.expiredAt.label=Time of expiration
i18n_entity.HumanUserSession.fields.id.label=ID
i18n_entity.HumanUserSession.fields.modifiedBy.label=Last modifier
i18n_entity.HumanUserSession.fields.modifiedOn.label=Last modified time
i18n_entity.HumanUserSession.fields.ro.label=RO
i18n_entity.HumanUserSession.fields.userId.label=User
i18n_entity.HumanUserSession.fields.userToken.label=Token
i18n_entity.HumanUserSession.fields.version.label=Revised version
i18n_entity.HumanUserSession.group=Core
i18n_entity.HumanUserSession.label=User session
i18n_entity.IdGen.fields.createdBy.label=Founder
i18n_entity.IdGen.fields.createdOn.label=Creation time
i18n_entity.IdGen.fields.flowNo.label=Serial number
i18n_entity.IdGen.fields.id.label=Number
i18n_entity.IdGen.fields.key.label=Group
i18n_entity.IdGen.fields.modifiedBy.label=Last modifier
i18n_entity.IdGen.fields.modifiedOn.label=Last modified time
i18n_entity.IdGen.fields.ro.label=Root tissue
i18n_entity.IdGen.fields.timestamp.label=Date
i18n_entity.IdGen.fields.version.label=Version
i18n_entity.IdGen.group=Core
i18n_entity.IdGen.label=Numbering rules
i18n_entity.LePage.fields.content.label=Content
i18n_entity.LePage.fields.createdBy.label=Founder
i18n_entity.LePage.fields.createdOn.label=Creation time
i18n_entity.LePage.fields.id.label=ID
i18n_entity.LePage.fields.label.label=Display name
i18n_entity.LePage.fields.modifiedBy.label=Last modifier
i18n_entity.LePage.fields.modifiedOn.label=Last modified time
i18n_entity.LePage.fields.name.label=Page name
i18n_entity.LePage.fields.version.label=Revised version
i18n_entity.LePage.group=Core
i18n_entity.LePage.label=Customized interface
i18n_entity.ListFilterCase.fields.content.label=Content
i18n_entity.ListFilterCase.fields.createdBy.label=Founder
i18n_entity.ListFilterCase.fields.createdOn.label=Creation time
i18n_entity.ListFilterCase.fields.global.label=Overall situation
i18n_entity.ListFilterCase.fields.id.label=Number
i18n_entity.ListFilterCase.fields.modifiedBy.label=Last modifier
i18n_entity.ListFilterCase.fields.modifiedOn.label=Last modified time
i18n_entity.ListFilterCase.fields.owner.label=Owner
i18n_entity.ListFilterCase.fields.page.label=Page
i18n_entity.ListFilterCase.fields.ro.label=Root tissue
i18n_entity.ListFilterCase.fields.version.label=Version
i18n_entity.ListFilterCase.group=Core
i18n_entity.ListFilterCase.label=List query scheme
i18n_entity.MockSeerRobot.fields.createdBy.label=Founder
i18n_entity.MockSeerRobot.fields.createdOn.label=Creation time
i18n_entity.MockSeerRobot.fields.currentStation.label=Current site
i18n_entity.MockSeerRobot.fields.id.label=ID
i18n_entity.MockSeerRobot.fields.modifiedBy.label=Last modifier
i18n_entity.MockSeerRobot.fields.modifiedOn.label=Last modified time
i18n_entity.MockSeerRobot.fields.staringStation.label=Birth site
i18n_entity.MockSeerRobot.fields.version.label=Revised version
i18n_entity.MockSeerRobot.group=WCS
i18n_entity.MockSeerRobot.label=Simulated Seer Robot
i18n_entity.MockSeerRobot.listCard.createdOn.prefix=Create
i18n_entity.MockSeerRobot.listCard.staringStation.prefix=Birth site
i18n_entity.MrRobotRuntimeRecord.fields.channel.label=In aisle
i18n_entity.MrRobotRuntimeRecord.fields.createdBy.label=Founder
i18n_entity.MrRobotRuntimeRecord.fields.createdOn.label=Creation time
i18n_entity.MrRobotRuntimeRecord.fields.ctOrders.label=Container handling list
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Charging.label=Charging
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Idle.label=Idle
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Inbound.label=In storage
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Outbound.label=Out of stock
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Parking.label=To dock
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Tasking.label=Execution of waybill
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.label=Extended task status
i18n_entity.MrRobotRuntimeRecord.fields.id.label=ID
i18n_entity.MrRobotRuntimeRecord.fields.modifiedBy.label=Last modifier
i18n_entity.MrRobotRuntimeRecord.fields.modifiedOn.label=Last modified time
i18n_entity.MrRobotRuntimeRecord.fields.offDuty.label=Do not take orders
i18n_entity.MrRobotRuntimeRecord.fields.port.label=Location of the warehouse
i18n_entity.MrRobotRuntimeRecord.fields.rtBins.label=Real-time scheduling of cargo status
i18n_entity.MrRobotRuntimeRecord.fields.rtCmdStatus.label=Real-time scheduling execution status
i18n_entity.MrRobotRuntimeRecord.fields.rtCurrentOrder.label=Real-time scheduling of current waybills
i18n_entity.MrRobotRuntimeRecord.fields.rtCurrentStep.label=Real-time scheduling of current waybill steps
i18n_entity.MrRobotRuntimeRecord.fields.rtOrders.label=Real-time dispatch waybill list
i18n_entity.MrRobotRuntimeRecord.fields.taskStatus.inlineOptionBill.items.Charging.label=Charging
i18n_entity.MrRobotRuntimeRecord.fields.taskStatus.inlineOptionBill.items.Idle.label=Idle
i18n_entity.MrRobotRuntimeRecord.fields.taskStatus.inlineOptionBill.items.Tasking.label=In the mission
i18n_entity.MrRobotRuntimeRecord.fields.taskStatus.label=Task status
i18n_entity.MrRobotRuntimeRecord.fields.version.label=Revised version
i18n_entity.MrRobotRuntimeRecord.group=WCS
i18n_entity.MrRobotRuntimeRecord.label=Mobile robot operation record
i18n_entity.MrRobotRuntimeRecord.listCard.ctOrders.prefix=Container handling list
i18n_entity.MrRobotRuntimeRecord.listCard.rtBins.prefix=Goods
i18n_entity.MrRobotRuntimeRecord.listCard.rtCurrentOrder.prefix=Current waybill
i18n_entity.MrRobotSystemConfig.fields.category.label=Category
i18n_entity.MrRobotSystemConfig.fields.connectionType.inlineOptionBill.items.GwWs.label=Gateway Connection Server (GW Channel)
i18n_entity.MrRobotSystemConfig.fields.connectionType.inlineOptionBill.items.GwWsLight.label=Gateway connection server (optical communication)
i18n_entity.MrRobotSystemConfig.fields.connectionType.inlineOptionBill.items.Mock.label=Simulation
i18n_entity.MrRobotSystemConfig.fields.connectionType.inlineOptionBill.items.Rbk.label=Server connection robot
i18n_entity.MrRobotSystemConfig.fields.connectionType.label=Connection type
i18n_entity.MrRobotSystemConfig.fields.createdBy.label=Founder
i18n_entity.MrRobotSystemConfig.fields.createdOn.label=Creation time
i18n_entity.MrRobotSystemConfig.fields.disabled.label=Deactivated
i18n_entity.MrRobotSystemConfig.fields.gwAuthId.label=Gateway Login ID
i18n_entity.MrRobotSystemConfig.fields.gwAuthSecret.label=Gateway Login CDKEY
i18n_entity.MrRobotSystemConfig.fields.id.label=ID
i18n_entity.MrRobotSystemConfig.fields.image.label=Picture
i18n_entity.MrRobotSystemConfig.fields.modifiedBy.label=Last modifier
i18n_entity.MrRobotSystemConfig.fields.modifiedOn.label=Last modified time
i18n_entity.MrRobotSystemConfig.fields.offDuty.label=Do not take orders
i18n_entity.MrRobotSystemConfig.fields.robotHost.label=Robot address
i18n_entity.MrRobotSystemConfig.fields.scene.label=Scene
i18n_entity.MrRobotSystemConfig.fields.selfBinNum.label=Maximum cargo capacity
i18n_entity.MrRobotSystemConfig.fields.ssl.label=SSL/TSL encryption
i18n_entity.MrRobotSystemConfig.fields.tags.label=Label
i18n_entity.MrRobotSystemConfig.fields.taskMode.inlineOptionBill.items.Custom.label=Custom scheduling
i18n_entity.MrRobotSystemConfig.fields.taskMode.inlineOptionBill.items.SingleRobot.label=M4A1
i18n_entity.MrRobotSystemConfig.fields.taskMode.inlineOptionBill.items.Tom.label=Core
i18n_entity.MrRobotSystemConfig.fields.taskMode.label=Task mode
i18n_entity.MrRobotSystemConfig.fields.tomId.label=Seer Dispatch ID
i18n_entity.MrRobotSystemConfig.fields.vendor.inlineOptionBill.items.Hai.label=Hai
i18n_entity.MrRobotSystemConfig.fields.vendor.inlineOptionBill.items.Hik.label=Hiking
i18n_entity.MrRobotSystemConfig.fields.vendor.inlineOptionBill.items.Seer.label=Seer
i18n_entity.MrRobotSystemConfig.fields.vendor.label=Manufacturers
i18n_entity.MrRobotSystemConfig.fields.version.label=Revised version
i18n_entity.MrRobotSystemConfig.group=WCS
i18n_entity.MrRobotSystemConfig.label=Mobile robot system configuration
i18n_entity.MrRobotSystemConfig.listCard.scene.prefix=Scene
i18n_entity.MrRobotSystemConfig.listCard.selfBinNum.prefix=Maximum cargo
i18n_entity.NdcOrder.fields.allowLoad.label=Allowed to pick up
i18n_entity.NdcOrder.fields.allowUnload.label=Allow drop off
i18n_entity.NdcOrder.fields.createdBy.label=Founder
i18n_entity.NdcOrder.fields.createdOn.label=Creation time
i18n_entity.NdcOrder.fields.endBin.label=End point
i18n_entity.NdcOrder.fields.falconId.label=Falcon mission
i18n_entity.NdcOrder.fields.id.label=ID
i18n_entity.NdcOrder.fields.ikey.label=ikey
i18n_entity.NdcOrder.fields.index.label=index
i18n_entity.NdcOrder.fields.modifiedBy.label=Last modifier
i18n_entity.NdcOrder.fields.modifiedOn.label=Last modified time
i18n_entity.NdcOrder.fields.priority.label=Priority
i18n_entity.NdcOrder.fields.startBin.label=Starting point
i18n_entity.NdcOrder.fields.status.label=Status
i18n_entity.NdcOrder.fields.version.label=Version
i18n_entity.NdcOrder.group=NDC
i18n_entity.NdcOrder.label=NDC waybill
i18n_entity.OrderFlowRecord.fields.createdBy.label=Founder
i18n_entity.OrderFlowRecord.fields.createdOn.label=Creation time
i18n_entity.OrderFlowRecord.fields.id.label=ID
i18n_entity.OrderFlowRecord.fields.modifiedBy.label=Last modifier
i18n_entity.OrderFlowRecord.fields.modifiedOn.label=Last modified time
i18n_entity.OrderFlowRecord.fields.pushType.label=Type
i18n_entity.OrderFlowRecord.fields.sourceOrderId.label=Source tracking number
i18n_entity.OrderFlowRecord.fields.sourceOrderName.label=Source single name
i18n_entity.OrderFlowRecord.fields.targetOrderId.label=New tracking number
i18n_entity.OrderFlowRecord.fields.targetOrderName.label=New single name
i18n_entity.OrderFlowRecord.fields.targetOrderType.label=New single type
i18n_entity.OrderFlowRecord.fields.txId.label=Transaction ID
i18n_entity.OrderFlowRecord.fields.version.label=Revised version
i18n_entity.OrderFlowRecord.group=Core
i18n_entity.OrderFlowRecord.label=Documentation flow record
i18n_entity.PickOrder.fields.allUsed.label=Whole pallet sorting
i18n_entity.PickOrder.fields.btLines.label=Single line
i18n_entity.PickOrder.fields.btOrderKind.inlineOptionBill.items.NormalTask.label=Loading task
i18n_entity.PickOrder.fields.btOrderKind.label=Type
i18n_entity.PickOrder.fields.btOrderState.inlineOptionBill.items.Done.label=Complete sorting
i18n_entity.PickOrder.fields.btOrderState.inlineOptionBill.items.Todo.label=Waiting for sorting
i18n_entity.PickOrder.fields.btOrderState.label=Business status
i18n_entity.PickOrder.fields.btOrderStateReason.label=Status description
i18n_entity.PickOrder.fields.container.label=Container
i18n_entity.PickOrder.fields.containerBackOrderId.label=Container return waybill number
i18n_entity.PickOrder.fields.containerOutDone.label=Container outbound transportation completed
i18n_entity.PickOrder.fields.containerOutOrderId.label=Container outbound waybill number
i18n_entity.PickOrder.fields.createdBy.label=Founder
i18n_entity.PickOrder.fields.createdOn.label=Creation time
i18n_entity.PickOrder.fields.id.label=Order number
i18n_entity.PickOrder.fields.modifiedBy.label=Last modifier
i18n_entity.PickOrder.fields.modifiedOn.label=Last modified time
i18n_entity.PickOrder.fields.sourceOrderId.label=Outbound order number
i18n_entity.PickOrder.fields.submittedPostProcessed.label=Post-processing completed
i18n_entity.PickOrder.fields.version.label=Revised version
i18n_entity.PickOrder.group=Warehouse
i18n_entity.PickOrder.label=Sorting list
i18n_entity.PickOrder.listCard.container.prefix=Container
i18n_entity.PickOrder.listCard.sourceOrderId.prefix=Delivery order
i18n_entity.PickOrder.states.states.Done.label=Complete sorting
i18n_entity.PickOrder.states.states.Todo.label=Waiting for sorting
i18n_entity.PickOrder.states.states.Todo.nextStates.Done.buttonLabel=Complete
i18n_entity.PickOrderLine.fields.btLineNo.label=Line number
i18n_entity.PickOrderLine.fields.btMaterial.label=Materials
i18n_entity.PickOrderLine.fields.btMaterialCategory.label=Material classification number
i18n_entity.PickOrderLine.fields.btMaterialCategoryName.label=Material classification name
i18n_entity.PickOrderLine.fields.btMaterialId.label=Material number
i18n_entity.PickOrderLine.fields.btMaterialImage.label=Material pictures
i18n_entity.PickOrderLine.fields.btMaterialModel.label=Material type
i18n_entity.PickOrderLine.fields.btMaterialName.label=Material name
i18n_entity.PickOrderLine.fields.btMaterialSpec.label=Material specification
i18n_entity.PickOrderLine.fields.btParentId.label=Owned documentation
i18n_entity.PickOrderLine.fields.container.label=Container
i18n_entity.PickOrderLine.fields.createdBy.label=Founder
i18n_entity.PickOrderLine.fields.createdOn.label=Creation time
i18n_entity.PickOrderLine.fields.id.label=ID
i18n_entity.PickOrderLine.fields.lotNo.label=Batch number
i18n_entity.PickOrderLine.fields.modifiedBy.label=Last modifier
i18n_entity.PickOrderLine.fields.modifiedOn.label=Last modified time
i18n_entity.PickOrderLine.fields.planQty.label=Expected pick number
i18n_entity.PickOrderLine.fields.qty.label=Actual number of picks
i18n_entity.PickOrderLine.fields.qualityLevel.label=Quality grade
i18n_entity.PickOrderLine.fields.sourceLineId.label=Source single row ID
i18n_entity.PickOrderLine.fields.sourceLineNo.label=Source line number
i18n_entity.PickOrderLine.fields.sourceOrderId.label=Source documentation
i18n_entity.PickOrderLine.fields.subContainerId.label=Grid number
i18n_entity.PickOrderLine.fields.version.label=Revised version
i18n_entity.PickOrderLine.group=Warehouse
i18n_entity.PickOrderLine.label=Sorting single row
i18n_entity.PickOrderLine.listCard.btMaterialId.prefix=Material number
i18n_entity.PickOrderLine.listCard.lotNo.prefix=Batch
i18n_entity.PickOrderLine.listCard.planQty.prefix=Expected picking
i18n_entity.PickOrderLine.listCard.qty.prefix=Actual picking
i18n_entity.PickOrderLine.listCard.subContainerId.prefix=Grid number
i18n_entity.PlcDeviceConfig.fields.autoRetry.label=Automatic reconnect
i18n_entity.PlcDeviceConfig.fields.createdBy.label=Founder
i18n_entity.PlcDeviceConfig.fields.createdOn.label=Creation time
i18n_entity.PlcDeviceConfig.fields.disabled.label=Deactivated
i18n_entity.PlcDeviceConfig.fields.endpoint.label=Connection string
i18n_entity.PlcDeviceConfig.fields.host.label=Address/IP
i18n_entity.PlcDeviceConfig.fields.id.label=Name (ID)
i18n_entity.PlcDeviceConfig.fields.maxRetry.label=Maximum number of retries
i18n_entity.PlcDeviceConfig.fields.modifiedBy.label=Last modifier
i18n_entity.PlcDeviceConfig.fields.modifiedOn.label=Last modified time
i18n_entity.PlcDeviceConfig.fields.port.label=Port
i18n_entity.PlcDeviceConfig.fields.rack.label=S7 rack number
i18n_entity.PlcDeviceConfig.fields.retryDelay.label=Retry wait (milliseconds)
i18n_entity.PlcDeviceConfig.fields.slot.label=S7 slot number
i18n_entity.PlcDeviceConfig.fields.subType.label=Subtype
i18n_entity.PlcDeviceConfig.fields.timeout.label=Connection timeout (milliseconds)
i18n_entity.PlcDeviceConfig.fields.type.inlineOptionBill.items.Modbus.label=Modbus
i18n_entity.PlcDeviceConfig.fields.type.inlineOptionBill.items.OPCUA.label=OPCUA
i18n_entity.PlcDeviceConfig.fields.type.inlineOptionBill.items.S7.label=S7
i18n_entity.PlcDeviceConfig.fields.type.label=Type
i18n_entity.PlcDeviceConfig.fields.version.label=Revised version
i18n_entity.PlcDeviceConfig.group=WCS
i18n_entity.PlcDeviceConfig.label=PLC equipment configuration
i18n_entity.PlcDeviceConfig.listCard.host.prefix=Address/IP
i18n_entity.PlcDeviceConfig.listCard.port.prefix=Port
i18n_entity.PlcRwLog.fields.action.label=Operation
i18n_entity.PlcRwLog.fields.createdBy.label=Founder
i18n_entity.PlcRwLog.fields.createdOn.label=Creation time
i18n_entity.PlcRwLog.fields.deviceName.label=Device name
i18n_entity.PlcRwLog.fields.deviceType.label=Device type
i18n_entity.PlcRwLog.fields.id.label=ID
i18n_entity.PlcRwLog.fields.ip.label=IP
i18n_entity.PlcRwLog.fields.modifiedBy.label=Last modifier
i18n_entity.PlcRwLog.fields.modifiedOn.label=Last modified time
i18n_entity.PlcRwLog.fields.oldValueDesc.label=Original value
i18n_entity.PlcRwLog.fields.reason.label=Reason
i18n_entity.PlcRwLog.fields.rw.inlineOptionBill.items.Read.label=Read
i18n_entity.PlcRwLog.fields.rw.inlineOptionBill.items.Write.label=Write
i18n_entity.PlcRwLog.fields.rw.label=Read and write
i18n_entity.PlcRwLog.fields.valueDesc.label=Value
i18n_entity.PlcRwLog.fields.version.label=Revised version
i18n_entity.PlcRwLog.group=WCS
i18n_entity.PlcRwLog.label=PLC read and write record
i18n_entity.PlcRwLog.listCard.oldValueDesc.prefix=Original value
i18n_entity.PlcRwLog.listCard.valueDesc.prefix=Value
i18n_entity.PutinContainerOrder.fields.btLines.label=Single line
i18n_entity.PutinContainerOrder.fields.btOrderKind.inlineOptionBill.items.NormalTask.label=Loading task
i18n_entity.PutinContainerOrder.fields.btOrderKind.label=Type
i18n_entity.PutinContainerOrder.fields.btOrderState.inlineOptionBill.items.Done.label=Put away
i18n_entity.PutinContainerOrder.fields.btOrderState.inlineOptionBill.items.Filled.label=Loading complete
i18n_entity.PutinContainerOrder.fields.btOrderState.inlineOptionBill.items.Todo.label=Waiting to load
i18n_entity.PutinContainerOrder.fields.btOrderState.label=Business status
i18n_entity.PutinContainerOrder.fields.btOrderStateReason.label=Status description
i18n_entity.PutinContainerOrder.fields.container.label=Container
i18n_entity.PutinContainerOrder.fields.containerBackOrderId.label=Container return waybill number
i18n_entity.PutinContainerOrder.fields.containerOutDone.label=Container outbound transportation completed
i18n_entity.PutinContainerOrder.fields.containerOutOrderId.label=Container outbound waybill number
i18n_entity.PutinContainerOrder.fields.createdBy.label=Founder
i18n_entity.PutinContainerOrder.fields.createdOn.label=Creation time
i18n_entity.PutinContainerOrder.fields.id.label=Order number
i18n_entity.PutinContainerOrder.fields.modifiedBy.label=Last modifier
i18n_entity.PutinContainerOrder.fields.modifiedOn.label=Last modified time
i18n_entity.PutinContainerOrder.fields.sourceOrderId.label=Warehouse order number
i18n_entity.PutinContainerOrder.fields.version.label=Revised version
i18n_entity.PutinContainerOrder.group=Warehouse
i18n_entity.PutinContainerOrder.label=Loading note
i18n_entity.PutinContainerOrder.listCard.container.prefix=Container
i18n_entity.PutinContainerOrder.states.states.Done.label=Put away
i18n_entity.PutinContainerOrder.states.states.Filled.label=Loading complete
i18n_entity.PutinContainerOrder.states.states.Todo.label=Waiting to load
i18n_entity.PutinContainerOrder.states.states.Todo.nextStates.Filled.buttonLabel=Complete
i18n_entity.PutinContainerOrderLine.fields.btLineNo.label=Line number
i18n_entity.PutinContainerOrderLine.fields.btMaterial.label=Materials
i18n_entity.PutinContainerOrderLine.fields.btMaterialCategory.label=Material classification number
i18n_entity.PutinContainerOrderLine.fields.btMaterialCategoryName.label=Material classification name
i18n_entity.PutinContainerOrderLine.fields.btMaterialId.label=Material number
i18n_entity.PutinContainerOrderLine.fields.btMaterialImage.label=Material pictures
i18n_entity.PutinContainerOrderLine.fields.btMaterialModel.label=Material type
i18n_entity.PutinContainerOrderLine.fields.btMaterialName.label=Material name
i18n_entity.PutinContainerOrderLine.fields.btMaterialSpec.label=Material specification
i18n_entity.PutinContainerOrderLine.fields.btParentId.label=Owned documentation
i18n_entity.PutinContainerOrderLine.fields.container.label=Container
i18n_entity.PutinContainerOrderLine.fields.createdBy.label=Founder
i18n_entity.PutinContainerOrderLine.fields.createdOn.label=Creation time
i18n_entity.PutinContainerOrderLine.fields.id.label=ID
i18n_entity.PutinContainerOrderLine.fields.lotNo.label=Batch number
i18n_entity.PutinContainerOrderLine.fields.modifiedBy.label=Last modifier
i18n_entity.PutinContainerOrderLine.fields.modifiedOn.label=Last modified time
i18n_entity.PutinContainerOrderLine.fields.planQty.label=Planned load
i18n_entity.PutinContainerOrderLine.fields.price.label=Unit price
i18n_entity.PutinContainerOrderLine.fields.qty.label=Actual number of loads
i18n_entity.PutinContainerOrderLine.fields.qualityLevel.label=Quality grade
i18n_entity.PutinContainerOrderLine.fields.sourceLineId.label=Source single row ID
i18n_entity.PutinContainerOrderLine.fields.sourceLineNo.label=Source line number
i18n_entity.PutinContainerOrderLine.fields.sourceOrderId.label=Source documentation
i18n_entity.PutinContainerOrderLine.fields.subContainerId.label=Grid number
i18n_entity.PutinContainerOrderLine.fields.unitLabel.label=Unit name
i18n_entity.PutinContainerOrderLine.fields.version.label=Revised version
i18n_entity.PutinContainerOrderLine.group=Warehouse
i18n_entity.PutinContainerOrderLine.label=Loading list
i18n_entity.PutinContainerOrderLine.listCard.btMaterialId.prefix=Material number
i18n_entity.PutinContainerOrderLine.listCard.btMaterialName.prefix=Material name
i18n_entity.PutinContainerOrderLine.listCard.lotNo.prefix=Batch number
i18n_entity.PutinContainerOrderLine.listCard.planQty.prefix=Plan to load
i18n_entity.PutinContainerOrderLine.listCard.qty.prefix=Actual loading
i18n_entity.PutinContainerOrderLine.listCard.subContainerId.prefix=Grid number
i18n_entity.QsInboundOrder.fields.btBzKind.inlineOptionBill.items.Normal.label=Ordinary storage
i18n_entity.QsInboundOrder.fields.btBzKind.inlineOptionBill.items.Other.label=Other storage
i18n_entity.QsInboundOrder.fields.btBzKind.label=Business type
i18n_entity.QsInboundOrder.fields.btLines.label=Single line
i18n_entity.QsInboundOrder.fields.btOrderState.inlineOptionBill.items.Cancelled.label=Cancelled
i18n_entity.QsInboundOrder.fields.btOrderState.inlineOptionBill.items.Committed.label=Submitted
i18n_entity.QsInboundOrder.fields.btOrderState.inlineOptionBill.items.Init.label=Not submitted
i18n_entity.QsInboundOrder.fields.btOrderState.label=Business status
i18n_entity.QsInboundOrder.fields.btOrderStateReason.label=Status description
i18n_entity.QsInboundOrder.fields.callContainerAll.label=Call empty container allocation complete
i18n_entity.QsInboundOrder.fields.createdBy.label=Founder
i18n_entity.QsInboundOrder.fields.createdOn.label=Creation time
i18n_entity.QsInboundOrder.fields.id.label=Order number
i18n_entity.QsInboundOrder.fields.modifiedBy.label=Last modifier
i18n_entity.QsInboundOrder.fields.modifiedOn.label=Last modified time
i18n_entity.QsInboundOrder.fields.otherType.label=Other types
i18n_entity.QsInboundOrder.fields.priority.label=Priority
i18n_entity.QsInboundOrder.fields.remark.label=Remarks
i18n_entity.QsInboundOrder.fields.ro.label=Root tissue
i18n_entity.QsInboundOrder.fields.version.label=Version
i18n_entity.QsInboundOrder.group=Quick Store
i18n_entity.QsInboundOrder.kinds.kinds.Normal.label=Ordinary storage
i18n_entity.QsInboundOrder.kinds.kinds.Other.label=Other storage
i18n_entity.QsInboundOrder.label=QS Warehouse Form
i18n_entity.QsInboundOrder.states.states.Cancelled.label=Cancelled
i18n_entity.QsInboundOrder.states.states.Committed.label=Submitted
i18n_entity.QsInboundOrder.states.states.Init.label=Not submitted
i18n_entity.QsInboundOrder.states.states.Init.nextStates.Cancelled.buttonLabel=Cancel
i18n_entity.QsInboundOrder.states.states.Init.nextStates.Committed.buttonLabel=Submit
i18n_entity.QsInboundOrderLine.fields.btLineNo.label=Line number
i18n_entity.QsInboundOrderLine.fields.btMaterial.label=Materials
i18n_entity.QsInboundOrderLine.fields.btMaterialCategory.label=Material classification number
i18n_entity.QsInboundOrderLine.fields.btMaterialCategoryName.label=Material classification name
i18n_entity.QsInboundOrderLine.fields.btMaterialId.label=Material number
i18n_entity.QsInboundOrderLine.fields.btMaterialImage.label=Material pictures
i18n_entity.QsInboundOrderLine.fields.btMaterialModel.label=Material type
i18n_entity.QsInboundOrderLine.fields.btMaterialName.label=Material name
i18n_entity.QsInboundOrderLine.fields.btMaterialSpec.label=Material specification
i18n_entity.QsInboundOrderLine.fields.btParentId.label=Owned outbound order
i18n_entity.QsInboundOrderLine.fields.ccQty.label=Call empty container allocation quantity
i18n_entity.QsInboundOrderLine.fields.createdBy.label=Founder
i18n_entity.QsInboundOrderLine.fields.createdOn.label=Creation time
i18n_entity.QsInboundOrderLine.fields.id.label=ID
i18n_entity.QsInboundOrderLine.fields.lotNo.label=Batch number
i18n_entity.QsInboundOrderLine.fields.modifiedBy.label=Last modifier
i18n_entity.QsInboundOrderLine.fields.modifiedOn.label=Last modified time
i18n_entity.QsInboundOrderLine.fields.priority.label=Priority
i18n_entity.QsInboundOrderLine.fields.qty.label=Inventory quantity
i18n_entity.QsInboundOrderLine.fields.ro.label=Root tissue
i18n_entity.QsInboundOrderLine.fields.version.label=Version
i18n_entity.QsInboundOrderLine.group=Quick Store
i18n_entity.QsInboundOrderLine.label=QS warehouse single line
i18n_entity.QsInboundOrderLine.listCard.qty.prefix=Number
i18n_entity.QsMoveBinOrder.fields.actualToBin.label=Actual end storage bin
i18n_entity.QsMoveBinOrder.fields.btBzKind.inlineOptionBill.items.Normal.label=Default
i18n_entity.QsMoveBinOrder.fields.btBzKind.label=Business type
i18n_entity.QsMoveBinOrder.fields.container.label=Container
i18n_entity.QsMoveBinOrder.fields.createdBy.label=Founder
i18n_entity.QsMoveBinOrder.fields.createdOn.label=Creation time
i18n_entity.QsMoveBinOrder.fields.expectToBin.label=Specify endpoint storage bin
i18n_entity.QsMoveBinOrder.fields.expectToDistrict.label=Specified endpoint storage area
i18n_entity.QsMoveBinOrder.fields.fromBin.label=Starting storage bin
i18n_entity.QsMoveBinOrder.fields.id.label=ID
i18n_entity.QsMoveBinOrder.fields.modifiedBy.label=Last modifier
i18n_entity.QsMoveBinOrder.fields.modifiedOn.label=Last modified time
i18n_entity.QsMoveBinOrder.fields.oldInvLines.label=Inventory details inside the container while moving
i18n_entity.QsMoveBinOrder.fields.remark.label=Explanation
i18n_entity.QsMoveBinOrder.fields.version.label=Revised version
i18n_entity.QsMoveBinOrder.group=Quick Store
i18n_entity.QsMoveBinOrder.kinds.kinds.Normal.label=Default
i18n_entity.QsMoveBinOrder.label=QS moving order
i18n_entity.QsMoveBinOrder.listCard.container.prefix=Container
i18n_entity.QsMoveBinOrder.listCard.fromBin.suffix=->
i18n_entity.QsOldInvLine.fields.amount.label=Amount
i18n_entity.QsOldInvLine.fields.btLineNo.label=Line number
i18n_entity.QsOldInvLine.fields.btMaterial.label=Materials
i18n_entity.QsOldInvLine.fields.btMaterialCategory.label=Material classification number
i18n_entity.QsOldInvLine.fields.btMaterialCategoryName.label=Material classification name
i18n_entity.QsOldInvLine.fields.btMaterialImage.label=Material pictures
i18n_entity.QsOldInvLine.fields.btMaterialModel.label=Material type
i18n_entity.QsOldInvLine.fields.btMaterialName.label=Material name
i18n_entity.QsOldInvLine.fields.btMaterialSpec.label=Material specification
i18n_entity.QsOldInvLine.fields.btParentId.label=Owned order
i18n_entity.QsOldInvLine.fields.createdBy.label=Founder
i18n_entity.QsOldInvLine.fields.createdOn.label=Creation time
i18n_entity.QsOldInvLine.fields.expDate.label=Valid period
i18n_entity.QsOldInvLine.fields.id.label=ID
i18n_entity.QsOldInvLine.fields.inboundOn.label=Storage time
i18n_entity.QsOldInvLine.fields.leafContainer.label=Innermost container
i18n_entity.QsOldInvLine.fields.lotNo.label=Batch number
i18n_entity.QsOldInvLine.fields.matLotNo.label=Batch
i18n_entity.QsOldInvLine.fields.matSerialNo.label=Serial number
i18n_entity.QsOldInvLine.fields.mfgDate.label=Production date
i18n_entity.QsOldInvLine.fields.modifiedBy.label=Last modifier
i18n_entity.QsOldInvLine.fields.modifiedOn.label=Last modified time
i18n_entity.QsOldInvLine.fields.owner.label=Shipper
i18n_entity.QsOldInvLine.fields.price.label=Unit price
i18n_entity.QsOldInvLine.fields.qty.label=Number
i18n_entity.QsOldInvLine.fields.refInvId.label=Linked Inventory Detail ID
i18n_entity.QsOldInvLine.fields.subContainerId.label=Lattice
i18n_entity.QsOldInvLine.fields.topContainer.label=Outermost container
i18n_entity.QsOldInvLine.fields.vendor.label=Suppliers
i18n_entity.QsOldInvLine.fields.version.label=Revised version
i18n_entity.QsOldInvLine.group=Quick Store
i18n_entity.QsOldInvLine.label=QS Existing Inventory Details
i18n_entity.QsOldInvLine.listStats.items[0].label=Processing (locked)
i18n_entity.QsOutboundOrder.fields.btLines.label=Single line
i18n_entity.QsOutboundOrder.fields.btOrderKind.inlineOptionBill.items.Default.label=Default
i18n_entity.QsOutboundOrder.fields.btOrderKind.label=Type
i18n_entity.QsOutboundOrder.fields.btOrderState.inlineOptionBill.items.Committed.label=Submitted
i18n_entity.QsOutboundOrder.fields.btOrderState.inlineOptionBill.items.Init.label=Not submitted
i18n_entity.QsOutboundOrder.fields.btOrderState.label=Business status
i18n_entity.QsOutboundOrder.fields.btOrderStateReason.label=Status description
i18n_entity.QsOutboundOrder.fields.createdBy.label=Founder
i18n_entity.QsOutboundOrder.fields.createdOn.label=Creation time
i18n_entity.QsOutboundOrder.fields.id.label=Order number
i18n_entity.QsOutboundOrder.fields.invAssignedAll.label=Inventory distribution completed
i18n_entity.QsOutboundOrder.fields.modifiedBy.label=Last modifier
i18n_entity.QsOutboundOrder.fields.modifiedOn.label=Last modified time
i18n_entity.QsOutboundOrder.fields.priority.label=Priority
i18n_entity.QsOutboundOrder.fields.remark.label=Remarks
i18n_entity.QsOutboundOrder.fields.ro.label=Root tissue
i18n_entity.QsOutboundOrder.fields.typePriority.label=Type priority
i18n_entity.QsOutboundOrder.fields.version.label=Version
i18n_entity.QsOutboundOrder.group=Quick Store
i18n_entity.QsOutboundOrder.label=QS outbound form
i18n_entity.QsOutboundOrder.states.states.Committed.label=Submitted
i18n_entity.QsOutboundOrder.states.states.Init.label=Not submitted
i18n_entity.QsOutboundOrder.states.states.Init.nextStates.Committed.buttonLabel=Submit
i18n_entity.QsOutboundOrderLine.fields.btLineNo.label=Line number
i18n_entity.QsOutboundOrderLine.fields.btMaterial.label=Materials
i18n_entity.QsOutboundOrderLine.fields.btMaterialCategory.label=Material classification number
i18n_entity.QsOutboundOrderLine.fields.btMaterialCategoryName.label=Material classification name
i18n_entity.QsOutboundOrderLine.fields.btMaterialId.label=Material number
i18n_entity.QsOutboundOrderLine.fields.btMaterialImage.label=Material pictures
i18n_entity.QsOutboundOrderLine.fields.btMaterialModel.label=Material type
i18n_entity.QsOutboundOrderLine.fields.btMaterialName.label=Material name
i18n_entity.QsOutboundOrderLine.fields.btMaterialSpec.label=Material specification
i18n_entity.QsOutboundOrderLine.fields.btParentId.label=Owned outbound order
i18n_entity.QsOutboundOrderLine.fields.createdBy.label=Founder
i18n_entity.QsOutboundOrderLine.fields.createdOn.label=Creation time
i18n_entity.QsOutboundOrderLine.fields.id.label=ID
i18n_entity.QsOutboundOrderLine.fields.invAssignedQty.label=Allocated stock quantity
i18n_entity.QsOutboundOrderLine.fields.lotNo.label=Batch number
i18n_entity.QsOutboundOrderLine.fields.modifiedBy.label=Last modifier
i18n_entity.QsOutboundOrderLine.fields.modifiedOn.label=Last modified time
i18n_entity.QsOutboundOrderLine.fields.priority.label=Priority
i18n_entity.QsOutboundOrderLine.fields.qty.label=Outbound quantity
i18n_entity.QsOutboundOrderLine.fields.ro.label=Root tissue
i18n_entity.QsOutboundOrderLine.fields.version.label=Version
i18n_entity.QsOutboundOrderLine.group=Quick Store
i18n_entity.QsOutboundOrderLine.label=QS outbound single line
i18n_entity.QsOutboundOrderLine.listCard.qty.prefix=Outbound quantity
i18n_entity.QsPickOrder.fields.btLines.label=Single line
i18n_entity.QsPickOrder.fields.btOrderKind.inlineOptionBill.items.NormalTask.label=Loading task
i18n_entity.QsPickOrder.fields.btOrderKind.label=Type
i18n_entity.QsPickOrder.fields.btOrderState.inlineOptionBill.items.Done.label=Picking completed
i18n_entity.QsPickOrder.fields.btOrderState.inlineOptionBill.items.Todo.label=Pending picking
i18n_entity.QsPickOrder.fields.btOrderState.label=Business status
i18n_entity.QsPickOrder.fields.btOrderStateReason.label=Status description
i18n_entity.QsPickOrder.fields.container.label=Container
i18n_entity.QsPickOrder.fields.createdBy.label=Founder
i18n_entity.QsPickOrder.fields.createdOn.label=Creation time
i18n_entity.QsPickOrder.fields.id.label=Order number
i18n_entity.QsPickOrder.fields.modifiedBy.label=Last modifier
i18n_entity.QsPickOrder.fields.modifiedOn.label=Last modified time
i18n_entity.QsPickOrder.fields.targetBin.label=Target storage bin
i18n_entity.QsPickOrder.fields.version.label=Revised version
i18n_entity.QsPickOrder.group=Quick Store
i18n_entity.QsPickOrder.label=QS picking list
i18n_entity.QsPickOrder.listCard.container.prefix=Container
i18n_entity.QsPickOrder.states.states.Done.label=Picking completed
i18n_entity.QsPickOrder.states.states.Todo.label=Pending picking
i18n_entity.QsPickOrder.states.states.Todo.nextStates.Done.buttonLabel=Complete
i18n_entity.QsPickOrderLine.fields.btLineNo.label=Line number
i18n_entity.QsPickOrderLine.fields.btMaterial.label=Materials
i18n_entity.QsPickOrderLine.fields.btMaterialCategory.label=Material classification number
i18n_entity.QsPickOrderLine.fields.btMaterialCategoryName.label=Material classification name
i18n_entity.QsPickOrderLine.fields.btMaterialId.label=Material number
i18n_entity.QsPickOrderLine.fields.btMaterialImage.label=Material pictures
i18n_entity.QsPickOrderLine.fields.btMaterialModel.label=Material type
i18n_entity.QsPickOrderLine.fields.btMaterialName.label=Material name
i18n_entity.QsPickOrderLine.fields.btMaterialSpec.label=Material specification
i18n_entity.QsPickOrderLine.fields.btParentId.label=Owned documentation
i18n_entity.QsPickOrderLine.fields.createdBy.label=Founder
i18n_entity.QsPickOrderLine.fields.createdOn.label=Creation time
i18n_entity.QsPickOrderLine.fields.id.label=ID
i18n_entity.QsPickOrderLine.fields.invLayoutId.label=Inventory Detail ID
i18n_entity.QsPickOrderLine.fields.lotNo.label=Batch number
i18n_entity.QsPickOrderLine.fields.modifiedBy.label=Last modifier
i18n_entity.QsPickOrderLine.fields.modifiedOn.label=Last modified time
i18n_entity.QsPickOrderLine.fields.outboundLineId.label=Outbound single line ID
i18n_entity.QsPickOrderLine.fields.outboundLineNo.label=Outbound single line number
i18n_entity.QsPickOrderLine.fields.outboundOrderId.label=Outbound order number
i18n_entity.QsPickOrderLine.fields.planQty.label=Expected pick number
i18n_entity.QsPickOrderLine.fields.qty.label=Actual number of picks
i18n_entity.QsPickOrderLine.fields.subContainerId.label=Grid number
i18n_entity.QsPickOrderLine.fields.topContainer.label=Container
i18n_entity.QsPickOrderLine.fields.version.label=Revised version
i18n_entity.QsPickOrderLine.group=Quick Store
i18n_entity.QsPickOrderLine.label=QS picking list line
i18n_entity.QsPickOrderLine.listCard.planQty.prefix=Expected quantity
i18n_entity.QsPickOrderLine.listCard.qty.prefix=Actual number
i18n_entity.QsPutOnContainerOrder.fields.bin.label=Put away storage bin
i18n_entity.QsPutOnContainerOrder.fields.container.label=Container
i18n_entity.QsPutOnContainerOrder.fields.createdBy.label=Founder
i18n_entity.QsPutOnContainerOrder.fields.createdOn.label=Creation time
i18n_entity.QsPutOnContainerOrder.fields.id.label=ID
i18n_entity.QsPutOnContainerOrder.fields.modifiedBy.label=Last modifier
i18n_entity.QsPutOnContainerOrder.fields.modifiedOn.label=Last modified time
i18n_entity.QsPutOnContainerOrder.fields.oldInvLines.label=Inventory details in container when put away
i18n_entity.QsPutOnContainerOrder.fields.version.label=Revised version
i18n_entity.QsPutOnContainerOrder.group=Quick Store
i18n_entity.QsPutOnContainerOrder.label=QS manual put away order
i18n_entity.QsPutOnContainerOrder.listCard.bin.prefix=Put away storage bin
i18n_entity.QsPutOrder.fields.bin.label=Put away storage bin
i18n_entity.QsPutOrder.fields.btLines.label=Single line
i18n_entity.QsPutOrder.fields.btOrderKind.inlineOptionBill.items.NormalTask.label=Loading task
i18n_entity.QsPutOrder.fields.btOrderKind.label=Type
i18n_entity.QsPutOrder.fields.btOrderState.inlineOptionBill.items.Done.label=Completion of loading
i18n_entity.QsPutOrder.fields.btOrderState.inlineOptionBill.items.Todo.label=Pending loading
i18n_entity.QsPutOrder.fields.btOrderState.label=Business status
i18n_entity.QsPutOrder.fields.btOrderStateReason.label=Status description
i18n_entity.QsPutOrder.fields.container.label=Container
i18n_entity.QsPutOrder.fields.createdBy.label=Founder
i18n_entity.QsPutOrder.fields.createdOn.label=Creation time
i18n_entity.QsPutOrder.fields.id.label=Order number
i18n_entity.QsPutOrder.fields.modifiedBy.label=Last modifier
i18n_entity.QsPutOrder.fields.modifiedOn.label=Last modified time
i18n_entity.QsPutOrder.fields.oldInvLines.label=Original inventory details in the container
i18n_entity.QsPutOrder.fields.targetBin.label=Target storage bin
i18n_entity.QsPutOrder.fields.version.label=Revised version
i18n_entity.QsPutOrder.group=Quick Store
i18n_entity.QsPutOrder.label=QS Loading List
i18n_entity.QsPutOrder.listCard.bin.prefix=Put away storage bin
i18n_entity.QsPutOrder.listCard.container.prefix=Container
i18n_entity.QsPutOrder.states.states.Done.label=Completion of loading
i18n_entity.QsPutOrder.states.states.Todo.label=Pending loading
i18n_entity.QsPutOrder.states.states.Todo.nextStates.Done.buttonLabel=Complete
i18n_entity.QsPutOrderLine.fields.btLineNo.label=Line number
i18n_entity.QsPutOrderLine.fields.btMaterial.label=Materials
i18n_entity.QsPutOrderLine.fields.btMaterialCategory.label=Material classification number
i18n_entity.QsPutOrderLine.fields.btMaterialCategoryName.label=Material classification name
i18n_entity.QsPutOrderLine.fields.btMaterialId.label=Material number
i18n_entity.QsPutOrderLine.fields.btMaterialImage.label=Material pictures
i18n_entity.QsPutOrderLine.fields.btMaterialModel.label=Material type
i18n_entity.QsPutOrderLine.fields.btMaterialName.label=Material name
i18n_entity.QsPutOrderLine.fields.btMaterialSpec.label=Material specification
i18n_entity.QsPutOrderLine.fields.btParentId.label=Owned documentation
i18n_entity.QsPutOrderLine.fields.createdBy.label=Founder
i18n_entity.QsPutOrderLine.fields.createdOn.label=Creation time
i18n_entity.QsPutOrderLine.fields.id.label=ID
i18n_entity.QsPutOrderLine.fields.lotNo.label=Batch number
i18n_entity.QsPutOrderLine.fields.modifiedBy.label=Last modifier
i18n_entity.QsPutOrderLine.fields.modifiedOn.label=Last modified time
i18n_entity.QsPutOrderLine.fields.planQty.label=Expected loading quantity
i18n_entity.QsPutOrderLine.fields.qty.label=Actual quantity loaded
i18n_entity.QsPutOrderLine.fields.subContainerId.label=Grid number
i18n_entity.QsPutOrderLine.fields.version.label=Revised version
i18n_entity.QsPutOrderLine.group=Quick Store
i18n_entity.QsPutOrderLine.label=QS loading list
i18n_entity.QsPutOrderLine.listCard.planQty.prefix=Expected loading
i18n_entity.QsPutOrderLine.listCard.qty.prefix=Actual loading
i18n_entity.QsTakeOffContainerOrder.fields.bin.label=Off shelf storage bin
i18n_entity.QsTakeOffContainerOrder.fields.container.label=Container
i18n_entity.QsTakeOffContainerOrder.fields.createdBy.label=Founder
i18n_entity.QsTakeOffContainerOrder.fields.createdOn.label=Creation time
i18n_entity.QsTakeOffContainerOrder.fields.id.label=ID
i18n_entity.QsTakeOffContainerOrder.fields.keepInv.label=Retention stock
i18n_entity.QsTakeOffContainerOrder.fields.modifiedBy.label=Last modifier
i18n_entity.QsTakeOffContainerOrder.fields.modifiedOn.label=Last modified time
i18n_entity.QsTakeOffContainerOrder.fields.oldInvLines.label=Inventory details in the container at the time of removal
i18n_entity.QsTakeOffContainerOrder.fields.version.label=Revised version
i18n_entity.QsTakeOffContainerOrder.group=Quick Store
i18n_entity.QsTakeOffContainerOrder.label=QS manual removal order
i18n_entity.QsTakeOffContainerOrder.listCard.bin.prefix=Off shelf storage bin
i18n_entity.RaSingleBatteryRecord.fields.batteryLevel.label=Battery level
i18n_entity.RaSingleBatteryRecord.fields.batteryTemp.label=Battery temperature
i18n_entity.RaSingleBatteryRecord.fields.charging.label=Whether to charge
i18n_entity.RaSingleBatteryRecord.fields.createdBy.label=Founder
i18n_entity.RaSingleBatteryRecord.fields.createdOn.label=Creation time
i18n_entity.RaSingleBatteryRecord.fields.current.label=Current
i18n_entity.RaSingleBatteryRecord.fields.id.label=Number
i18n_entity.RaSingleBatteryRecord.fields.modifiedBy.label=Last modifier
i18n_entity.RaSingleBatteryRecord.fields.modifiedOn.label=Last modified time
i18n_entity.RaSingleBatteryRecord.fields.robotName.label=Robot name
i18n_entity.RaSingleBatteryRecord.fields.version.label=Revised version
i18n_entity.RaSingleBatteryRecord.fields.voltage.label=Voltage
i18n_entity.RaSingleBatteryRecord.group=RaSingle
i18n_entity.RaSingleBatteryRecord.label=Robot battery record
i18n_entity.RaSingleNavigateRecord.fields.cost.label=Time spent
i18n_entity.RaSingleNavigateRecord.fields.createdBy.label=Founder
i18n_entity.RaSingleNavigateRecord.fields.createdOn.label=Creation time
i18n_entity.RaSingleNavigateRecord.fields.id.label=Number
i18n_entity.RaSingleNavigateRecord.fields.modifiedBy.label=Last modifier
i18n_entity.RaSingleNavigateRecord.fields.modifiedOn.label=Last modified time
i18n_entity.RaSingleNavigateRecord.fields.robotName.label=Robot name
i18n_entity.RaSingleNavigateRecord.fields.targetId.label=Target site
i18n_entity.RaSingleNavigateRecord.fields.targetPoint.label=Target coordinate point
i18n_entity.RaSingleNavigateRecord.fields.taskId.label=Navigation ID
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.0.label=NONE
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.1.label=WAITING
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.2.label=RUNNING
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.3.label=SUSPENDED
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.4.label=COMPLETED
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.5.label=FAILED
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.6.label=CANCELED
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.label=Navigation status
i18n_entity.RaSingleNavigateRecord.fields.taskType.inlineOptionBill.items.0.label=No navigation
i18n_entity.RaSingleNavigateRecord.fields.taskType.inlineOptionBill.items.1.label=Free navigation to any point
i18n_entity.RaSingleNavigateRecord.fields.taskType.inlineOptionBill.items.100.label=Other
i18n_entity.RaSingleNavigateRecord.fields.taskType.inlineOptionBill.items.2.label=Free navigation to the site
i18n_entity.RaSingleNavigateRecord.fields.taskType.inlineOptionBill.items.3.label=Path navigation to site
i18n_entity.RaSingleNavigateRecord.fields.taskType.inlineOptionBill.items.7.label=Translational rotation
i18n_entity.RaSingleNavigateRecord.fields.taskType.label=Navigation type
i18n_entity.RaSingleNavigateRecord.fields.version.label=Revised version
i18n_entity.RaSingleNavigateRecord.group=RaSingle
i18n_entity.RaSingleNavigateRecord.label=Robot navigation record
i18n_entity.ResourceLock.fields.createdBy.label=Founder
i18n_entity.ResourceLock.fields.createdOn.label=Creation time
i18n_entity.ResourceLock.fields.id.label=ID
i18n_entity.ResourceLock.fields.lockOn.label=Locking time
i18n_entity.ResourceLock.fields.locked.label=Lock
i18n_entity.ResourceLock.fields.modifiedBy.label=Last modifier
i18n_entity.ResourceLock.fields.modifiedOn.label=Modification time
i18n_entity.ResourceLock.fields.owner.label=Locker
i18n_entity.ResourceLock.fields.reason.label=Lock reason
i18n_entity.ResourceLock.fields.resId.label=Resource ID
i18n_entity.ResourceLock.fields.resType.label=Resource type
i18n_entity.ResourceLock.fields.version.label=Revised version
i18n_entity.ResourceLock.group=Core
i18n_entity.ResourceLock.label=Resource lock
i18n_entity.RobotConnectedPoint.fields.bin.label=Associated storage bin
i18n_entity.RobotConnectedPoint.fields.channel.label=In aisle
i18n_entity.RobotConnectedPoint.fields.createdBy.label=Founder
i18n_entity.RobotConnectedPoint.fields.createdOn.label=Creation time
i18n_entity.RobotConnectedPoint.fields.id.label=ID
i18n_entity.RobotConnectedPoint.fields.modifiedBy.label=Last modifier
i18n_entity.RobotConnectedPoint.fields.modifiedOn.label=Last modified time
i18n_entity.RobotConnectedPoint.fields.remark.label=Remarks
i18n_entity.RobotConnectedPoint.fields.version.label=Revised version
i18n_entity.RobotConnectedPoint.fields.x.label=Location x
i18n_entity.RobotConnectedPoint.fields.y.label=Location y
i18n_entity.RobotConnectedPoint.group=WCS
i18n_entity.RobotConnectedPoint.label=Robot communication point
i18n_entity.RobotConnectedPoint.listCard.bin.prefix=Associated storage bin
i18n_entity.RobotConnectedPoint.listCard.x.prefix=Location x
i18n_entity.RobotConnectedPoint.listCard.y.prefix=Location y
i18n_entity.RobotPropChangeTimeline.fields.createdBy.label=Founder
i18n_entity.RobotPropChangeTimeline.fields.createdOn.label=Creation time
i18n_entity.RobotPropChangeTimeline.fields.delta.label=Amount of change
i18n_entity.RobotPropChangeTimeline.fields.duration.label=Duration (milliseconds)
i18n_entity.RobotPropChangeTimeline.fields.finishedOn.label=End time
i18n_entity.RobotPropChangeTimeline.fields.id.label=ID
i18n_entity.RobotPropChangeTimeline.fields.modifiedBy.label=Last modifier
i18n_entity.RobotPropChangeTimeline.fields.modifiedOn.label=Last modified time
i18n_entity.RobotPropChangeTimeline.fields.newValue.label=New value
i18n_entity.RobotPropChangeTimeline.fields.oldValue.label=Old value
i18n_entity.RobotPropChangeTimeline.fields.percentageBase.label=Percentage base
i18n_entity.RobotPropChangeTimeline.fields.robotName.label=Robot
i18n_entity.RobotPropChangeTimeline.fields.startedOn.label=Start time
i18n_entity.RobotPropChangeTimeline.fields.type.label=State change type
i18n_entity.RobotPropChangeTimeline.fields.version.label=Revised version
i18n_entity.RobotPropChangeTimeline.group=Stats
i18n_entity.RobotPropChangeTimeline.label=Robot state change table
i18n_entity.RobustScriptExecutor.fields.args.label=Parameter
i18n_entity.RobustScriptExecutor.fields.createdBy.label=Founder
i18n_entity.RobustScriptExecutor.fields.createdOn.label=Creation time
i18n_entity.RobustScriptExecutor.fields.description.label=Description
i18n_entity.RobustScriptExecutor.fields.fault.label=Failure
i18n_entity.RobustScriptExecutor.fields.faultMsg.label=Fault message
i18n_entity.RobustScriptExecutor.fields.funcName.label=Method
i18n_entity.RobustScriptExecutor.fields.id.label=ID
i18n_entity.RobustScriptExecutor.fields.modifiedBy.label=Last modifier
i18n_entity.RobustScriptExecutor.fields.modifiedOn.label=Last modified time
i18n_entity.RobustScriptExecutor.fields.version.label=Revised version
i18n_entity.RobustScriptExecutor.group=Core
i18n_entity.RobustScriptExecutor.label=Background tasks
i18n_entity.RobustScriptExecutor.pagesButtons.ListMain.buttons[0].label=Batch edit
i18n_entity.RobustScriptExecutor.pagesButtons.ListMain.buttons[1].label=Delete
i18n_entity.RobustScriptExecutor.pagesButtons.ListMain.buttons[2].label=Restore
i18n_entity.ScriptRunOnce.fields.createdBy.label=Founder
i18n_entity.ScriptRunOnce.fields.createdOn.label=Creation time
i18n_entity.ScriptRunOnce.fields.id.label=ID
i18n_entity.ScriptRunOnce.fields.modifiedBy.label=Last modifier
i18n_entity.ScriptRunOnce.fields.modifiedOn.label=Modification time
i18n_entity.ScriptRunOnce.fields.output.label=Output
i18n_entity.ScriptRunOnce.fields.version.label=Revised version
i18n_entity.ScriptRunOnce.group=Core
i18n_entity.ScriptRunOnce.label=The script runs once
i18n_entity.SimpleTransportOrder.fields.createdBy.label=Founder
i18n_entity.SimpleTransportOrder.fields.createdOn.label=Creation time
i18n_entity.SimpleTransportOrder.fields.currentMove.label=Current step
i18n_entity.SimpleTransportOrder.fields.doneOn.label=End time
i18n_entity.SimpleTransportOrder.fields.errorMsg.label=Error message
i18n_entity.SimpleTransportOrder.fields.id.label=ID
i18n_entity.SimpleTransportOrder.fields.modifiedBy.label=Last modifier
i18n_entity.SimpleTransportOrder.fields.modifiedOn.label=Last modified time
i18n_entity.SimpleTransportOrder.fields.moves.label=Action list
i18n_entity.SimpleTransportOrder.fields.robotName.label=Robot
i18n_entity.SimpleTransportOrder.fields.seer3066.label=Specified path navigation
i18n_entity.SimpleTransportOrder.fields.status.inlineOptionBill.items.Cancelled.label=Cancelled
i18n_entity.SimpleTransportOrder.fields.status.inlineOptionBill.items.Created.label=Created
i18n_entity.SimpleTransportOrder.fields.status.inlineOptionBill.items.Done.label=Completed
i18n_entity.SimpleTransportOrder.fields.status.inlineOptionBill.items.Failed.label=Failure
i18n_entity.SimpleTransportOrder.fields.status.label=Status
i18n_entity.SimpleTransportOrder.fields.vendor.label=Manufacturers
i18n_entity.SimpleTransportOrder.fields.version.label=Revised version
i18n_entity.SimpleTransportOrder.group=GW
i18n_entity.SimpleTransportOrder.label=Single vehicle waybill
i18n_entity.SimpleTransportOrder.listCard.robotName.prefix=Robot
i18n_entity.SimpleTransportOrder.listCard.vendor.prefix=Manufacturers
i18n_entity.SimpleTransportOrder.listStats.items[0].label=In implementation
i18n_entity.SimpleTransportOrder.listStats.items[1].label=Failure
i18n_entity.SocNode.fields.attention.label=Attention
i18n_entity.SocNode.fields.createdBy.label=Founder
i18n_entity.SocNode.fields.createdOn.label=Creation time
i18n_entity.SocNode.fields.description.label=Explanation
i18n_entity.SocNode.fields.id.label=ID
i18n_entity.SocNode.fields.label.label=Label
i18n_entity.SocNode.fields.modifiedBy.label=Last modifier
i18n_entity.SocNode.fields.modifiedOn.label=Last modified time
i18n_entity.SocNode.fields.modifiedReason.label=Reason for update
i18n_entity.SocNode.fields.modifiedTimestamp.label=Update time
i18n_entity.SocNode.fields.value.label=Value
i18n_entity.SocNode.fields.version.label=Revised version
i18n_entity.SocNode.group=Core
i18n_entity.SocNode.label=Monitoring nodes
i18n_entity.SocNode.listCard.modifiedTimestamp.prefix=Update time:
i18n_entity.StatsTimelineValueReport.fields.createdBy.label=Founder
i18n_entity.StatsTimelineValueReport.fields.createdOn.label=Creation time
i18n_entity.StatsTimelineValueReport.fields.denominator.label=Denominator
i18n_entity.StatsTimelineValueReport.fields.finishedOn.label=End time
i18n_entity.StatsTimelineValueReport.fields.id.label=ID
i18n_entity.StatsTimelineValueReport.fields.modifiedBy.label=Last modifier
i18n_entity.StatsTimelineValueReport.fields.modifiedOn.label=Last modified time
i18n_entity.StatsTimelineValueReport.fields.molecular.label=Molecular
i18n_entity.StatsTimelineValueReport.fields.period.label=Cycle
i18n_entity.StatsTimelineValueReport.fields.periodType.label=Cycle type
i18n_entity.StatsTimelineValueReport.fields.startedOn.label=Start time
i18n_entity.StatsTimelineValueReport.fields.subject.label=Theme
i18n_entity.StatsTimelineValueReport.fields.target.label=Keywords
i18n_entity.StatsTimelineValueReport.fields.value.label=Value
i18n_entity.StatsTimelineValueReport.fields.version.label=Revised version
i18n_entity.StatsTimelineValueReport.group=Stats
i18n_entity.StatsTimelineValueReport.label=Time series numerical report
i18n_entity.SystemKeyEvent.fields.content.label=Content
i18n_entity.SystemKeyEvent.fields.createdBy.label=Founder
i18n_entity.SystemKeyEvent.fields.createdOn.label=Creation time
i18n_entity.SystemKeyEvent.fields.group.label=Module
i18n_entity.SystemKeyEvent.fields.id.label=ID
i18n_entity.SystemKeyEvent.fields.level.inlineOptionBill.items.Error.label=Error
i18n_entity.SystemKeyEvent.fields.level.inlineOptionBill.items.Info.label=Ordinary
i18n_entity.SystemKeyEvent.fields.level.inlineOptionBill.items.Warning.label=Warning
i18n_entity.SystemKeyEvent.fields.level.label=Level
i18n_entity.SystemKeyEvent.fields.modifiedBy.label=Last modifier
i18n_entity.SystemKeyEvent.fields.modifiedOn.label=Last modified time
i18n_entity.SystemKeyEvent.fields.relatedUser.label=Related users
i18n_entity.SystemKeyEvent.fields.title.label=Title
i18n_entity.SystemKeyEvent.fields.version.label=Revised version
i18n_entity.SystemKeyEvent.group=Core
i18n_entity.SystemKeyEvent.label=System critical events
i18n_entity.TransportOrder.fields.actualRobotName.label=Execution robot
i18n_entity.TransportOrder.fields.containerId.label=Container number
i18n_entity.TransportOrder.fields.createdBy.label=Founder
i18n_entity.TransportOrder.fields.createdOn.label=Creation time
i18n_entity.TransportOrder.fields.currentStepIndex.label=Current step
i18n_entity.TransportOrder.fields.dispatchCost.label=Allocation cost
i18n_entity.TransportOrder.fields.doneOn.label=Completion time
i18n_entity.TransportOrder.fields.doneStepIndex.label=Completed steps
i18n_entity.TransportOrder.fields.executingTime.label=Executing time (s)
i18n_entity.TransportOrder.fields.expectedRobotGroups.label=Specify a bot group
i18n_entity.TransportOrder.fields.expectedRobotNames.label=Specify a bot
i18n_entity.TransportOrder.fields.externalId.label=External tracking number
i18n_entity.TransportOrder.fields.fault.label=Failure
i18n_entity.TransportOrder.fields.fault.view.trueText=Failure
i18n_entity.TransportOrder.fields.faultReason.label=Reason of failure
i18n_entity.TransportOrder.fields.id.label=ID
i18n_entity.TransportOrder.fields.keyLocations.label=Key point
i18n_entity.TransportOrder.fields.kind.inlineOptionBill.items.Business.label=Business
i18n_entity.TransportOrder.fields.kind.inlineOptionBill.items.Charging.label=Charge
i18n_entity.TransportOrder.fields.kind.inlineOptionBill.items.IdleAvoid.label=Avoidance
i18n_entity.TransportOrder.fields.kind.inlineOptionBill.items.Parking.label=Docked
i18n_entity.TransportOrder.fields.kind.label=Type
i18n_entity.TransportOrder.fields.loadPoint.label=Pickup point
i18n_entity.TransportOrder.fields.loaded.label=Picked up
i18n_entity.TransportOrder.fields.modifiedBy.label=Last modifier
i18n_entity.TransportOrder.fields.modifiedOn.label=Modification time
i18n_entity.TransportOrder.fields.priority.label=Priority
i18n_entity.TransportOrder.fields.processingTime.label=Processing time (s)
i18n_entity.TransportOrder.fields.ro.label=RO
i18n_entity.TransportOrder.fields.robotAllocatedOn.label=Robot allocation time
i18n_entity.TransportOrder.fields.sceneId.label=Scene
i18n_entity.TransportOrder.fields.sceneName.label=Scene name
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.Allocated.label=Has been assigned
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.Cancelled.label=Cancelled
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.Done.label=Completed
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.Executing.label=In implementation
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.Pending.label=To be implemented
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.ToBeAllocated.label=To be assigned
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.Withdrawn.label=Was withdrawn
i18n_entity.TransportOrder.fields.status.label=Status
i18n_entity.TransportOrder.fields.stepFixed.label=Steps to fix
i18n_entity.TransportOrder.fields.stepNum.label=Number of task steps
i18n_entity.TransportOrder.fields.taskBatch.label=Task batch
i18n_entity.TransportOrder.fields.unloadPoint.label=Drop off point
i18n_entity.TransportOrder.fields.unloaded.label=Has dropped off
i18n_entity.TransportOrder.fields.version.label=Revised version
i18n_entity.TransportOrder.group=Fleet
i18n_entity.TransportOrder.label=New generic waybill
i18n_entity.TransportOrder.listCard.actualRobotName.prefix=Execution robot
i18n_entity.TransportOrder.listCard.createdOn.prefix=Create
i18n_entity.TransportOrder.listCard.kind.prefix=Type
i18n_entity.TransportOrder.listStats.items[0].label=Failure
i18n_entity.TransportOrder.listStats.items[1].label=To be assigned
i18n_entity.TransportOrder.listStats.items[2].label=Has been assigned
i18n_entity.TransportOrder.listStats.items[3].label=Created today
i18n_entity.TransportOrder.listStats.items[4].label=Completed today
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[0].label=Added
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[1].label=Batch edit
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[2].label=Delete
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[3].label=Export
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[4].label=Import
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[5].label=Cancel
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[6].label=Sealing
i18n_entity.TransportStep.fields.containerDir.label=Container direction
i18n_entity.TransportStep.fields.containerId.label=Container ID
i18n_entity.TransportStep.fields.containerTypeName.label=Container type name
i18n_entity.TransportStep.fields.createdBy.label=Founder
i18n_entity.TransportStep.fields.createdOn.label=Creation time
i18n_entity.TransportStep.fields.endOn.label=End execution time
i18n_entity.TransportStep.fields.executingTime.label=Executing time (s)
i18n_entity.TransportStep.fields.forLoad.label=Pick-up point
i18n_entity.TransportStep.fields.forUnload.label=Drop off point
i18n_entity.TransportStep.fields.id.label=ID
i18n_entity.TransportStep.fields.location.label=Job position
i18n_entity.TransportStep.fields.modifiedBy.label=Last modifier
i18n_entity.TransportStep.fields.modifiedOn.label=Modification time
i18n_entity.TransportStep.fields.orderId.label=Waybill number
i18n_entity.TransportStep.fields.processingTime.label=Processing time (s)
i18n_entity.TransportStep.fields.rbkArgs.label=Action parameter
i18n_entity.TransportStep.fields.startOn.label=Start execution time
i18n_entity.TransportStep.fields.status.label=Status
i18n_entity.TransportStep.fields.stepIndex.label=Which step?
i18n_entity.TransportStep.fields.version.label=Revised version
i18n_entity.TransportStep.group=Fleet
i18n_entity.TransportStep.label=New Common Waybill Steps
i18n_entity.TransportStep.listCard.locationSite.prefix=Point
i18n_entity.TransportStep.listCard.operation.prefix=Action
i18n_entity.TransportStep.listCard.stepIndex.prefix=No
i18n_entity.TransportStep.listCard.stepIndex.suffix=Step
i18n_entity.UserNotice.fields.actionType.label=Action type
i18n_entity.UserNotice.fields.content.label=Text
i18n_entity.UserNotice.fields.createdBy.label=Founder
i18n_entity.UserNotice.fields.createdOn.label=Creation time
i18n_entity.UserNotice.fields.entityId.label=Entity ID
i18n_entity.UserNotice.fields.entityName.label=Entity name
i18n_entity.UserNotice.fields.hasContent.label=There is text
i18n_entity.UserNotice.fields.id.label=ID
i18n_entity.UserNotice.fields.modifiedBy.label=Last modifier
i18n_entity.UserNotice.fields.modifiedOn.label=Last modified time
i18n_entity.UserNotice.fields.read.label=Read
i18n_entity.UserNotice.fields.readOn.label=Time read
i18n_entity.UserNotice.fields.title.label=Title
i18n_entity.UserNotice.fields.userId.label=User
i18n_entity.UserNotice.fields.version.label=Revised version
i18n_entity.UserNotice.group=Core
i18n_entity.UserNotice.label=User notification
i18n_entity.UserOpLog.fields.content.label=Operation content
i18n_entity.UserOpLog.fields.createdBy.label=Founder
i18n_entity.UserOpLog.fields.createdOn.label=Creation time
i18n_entity.UserOpLog.fields.id.label=ID
i18n_entity.UserOpLog.fields.level.inlineOptionBill.items.Danger.label=Danger
i18n_entity.UserOpLog.fields.level.inlineOptionBill.items.Normal.label=Normal
i18n_entity.UserOpLog.fields.level.label=Level
i18n_entity.UserOpLog.fields.modifiedBy.label=Last modifier
i18n_entity.UserOpLog.fields.modifiedOn.label=Last modified time
i18n_entity.UserOpLog.fields.operator.label=User
i18n_entity.UserOpLog.fields.page.label=Page
i18n_entity.UserOpLog.fields.version.label=Revised version
i18n_entity.UserOpLog.group=Core
i18n_entity.UserOpLog.label=User operation log
i18n_entity.UserRole.fields.createdBy.label=Founder
i18n_entity.UserRole.fields.createdOn.label=Creation time
i18n_entity.UserRole.fields.defaultRole.label=Default role
i18n_entity.UserRole.fields.id.label=Number
i18n_entity.UserRole.fields.modifiedBy.label=Last modifier
i18n_entity.UserRole.fields.modifiedOn.label=Last modified time
i18n_entity.UserRole.fields.name.label=Role name
i18n_entity.UserRole.fields.pItems.label=Permission list
i18n_entity.UserRole.fields.ro.label=Root tissue
i18n_entity.UserRole.fields.version.label=Version
i18n_entity.UserRole.group=User
i18n_entity.UserRole.label=User Persona
i18n_entity.UserRole.listCard.createdBy.prefix=Founder
i18n_entity.UserRole.listCard.modifiedOn.prefix=Modification time
i18n_entity.WcsMrOrder.fields.actualRobotName.label=Execution robot
i18n_entity.WcsMrOrder.fields.cancelling.label=Canceling
i18n_entity.WcsMrOrder.fields.containerId.label=Container number
i18n_entity.WcsMrOrder.fields.createdBy.label=Founder
i18n_entity.WcsMrOrder.fields.createdOn.label=Creation time
i18n_entity.WcsMrOrder.fields.currentStepIndex.label=Current step
i18n_entity.WcsMrOrder.fields.dispatchCost.label=Allocation cost
i18n_entity.WcsMrOrder.fields.doneOn.label=Completion time
i18n_entity.WcsMrOrder.fields.doneStepIndex.label=Completed steps
i18n_entity.WcsMrOrder.fields.executing.label=In implementation
i18n_entity.WcsMrOrder.fields.expectedRobotGroups.label=Specify a bot group
i18n_entity.WcsMrOrder.fields.expectedRobotNames.label=Specify a bot
i18n_entity.WcsMrOrder.fields.fault.label=Failure
i18n_entity.WcsMrOrder.fields.id.label=ID
i18n_entity.WcsMrOrder.fields.keySites.label=Key point
i18n_entity.WcsMrOrder.fields.kind.inlineOptionBill.items.Parking.label=Docked
i18n_entity.WcsMrOrder.fields.kind.label=Type
i18n_entity.WcsMrOrder.fields.loadLocationSite.label=Pickup point
i18n_entity.WcsMrOrder.fields.loaded.label=Picked up
i18n_entity.WcsMrOrder.fields.materialId.label=Material number
i18n_entity.WcsMrOrder.fields.materialKind.label=Material type
i18n_entity.WcsMrOrder.fields.modifiedBy.label=Last modifier
i18n_entity.WcsMrOrder.fields.modifiedOn.label=Modification time
i18n_entity.WcsMrOrder.fields.priority.label=Priority
i18n_entity.WcsMrOrder.fields.reqId.label=Request number
i18n_entity.WcsMrOrder.fields.ro.label=RO
i18n_entity.WcsMrOrder.fields.robotAllocatedOn.label=Robot allocation time
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Allocated.label=Has been assigned
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Building.label=Under construction
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Cancelled.label=Cancelled
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Cancelling.label=Canceling
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Done.label=Completed
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Executing.label=In implementation
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Pending.label=To be implemented
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.ToBeAllocated.label=To be assigned
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Withdrawn.label=Was withdrawn
i18n_entity.WcsMrOrder.fields.status.label=Status
i18n_entity.WcsMrOrder.fields.stepFixed.label=Steps to fix
i18n_entity.WcsMrOrder.fields.stepNum.label=Number of task steps
i18n_entity.WcsMrOrder.fields.taskBatch.label=Task batch
i18n_entity.WcsMrOrder.fields.unloadLocationSite.label=Drop off point
i18n_entity.WcsMrOrder.fields.unloaded.label=Has dropped off
i18n_entity.WcsMrOrder.fields.version.label=Revised version
i18n_entity.WcsMrOrder.fields.withdrawn.label=Reassignment
i18n_entity.WcsMrOrder.group=WCS
i18n_entity.WcsMrOrder.label=Common waybill
i18n_entity.WcsMrOrder.listCard.actualRobotName.prefix=Execution robot
i18n_entity.WcsMrOrder.listCard.createdOn.prefix=Create
i18n_entity.WcsMrOrder.listCard.kind.prefix=Type
i18n_entity.WcsMrOrder.listStats.items[0].label=Failure
i18n_entity.WcsMrOrder.listStats.items[1].label=To be assigned
i18n_entity.WcsMrOrder.listStats.items[2].label=Has been assigned
i18n_entity.WcsMrOrderStep.fields.createdBy.label=Founder
i18n_entity.WcsMrOrderStep.fields.createdOn.label=Creation time
i18n_entity.WcsMrOrderStep.fields.endOn.label=End execution time
i18n_entity.WcsMrOrderStep.fields.forLoad.label=Pick-up point
i18n_entity.WcsMrOrderStep.fields.forUnload.label=Drop off point
i18n_entity.WcsMrOrderStep.fields.id.label=ID
i18n_entity.WcsMrOrderStep.fields.locationSite.label=Action point
i18n_entity.WcsMrOrderStep.fields.modifiedBy.label=Last modifier
i18n_entity.WcsMrOrderStep.fields.modifiedOn.label=Modification time
i18n_entity.WcsMrOrderStep.fields.operation.label=Action
i18n_entity.WcsMrOrderStep.fields.operationArgs.label=Action parameter
i18n_entity.WcsMrOrderStep.fields.orderId.label=Waybill number
i18n_entity.WcsMrOrderStep.fields.startOn.label=Start execution time
i18n_entity.WcsMrOrderStep.fields.status.label=Status
i18n_entity.WcsMrOrderStep.fields.stepIndex.label=Which step?
i18n_entity.WcsMrOrderStep.fields.version.label=Revised version
i18n_entity.WcsMrOrderStep.group=WCS
i18n_entity.WcsMrOrderStep.label=Common Waybill Steps
i18n_entity.WcsMrOrderStep.listCard.locationSite.prefix=Point
i18n_entity.WcsMrOrderStep.listCard.operation.prefix=Action
i18n_entity.WcsMrOrderStep.listCard.stepIndex.prefix=No
i18n_entity.WcsMrOrderStep.listCard.stepIndex.suffix=Step
i18n_entity.WcsRobotTaskLog.fields.args.label=Parameter
i18n_entity.WcsRobotTaskLog.fields.category.label=Category
i18n_entity.WcsRobotTaskLog.fields.code.label=Code
i18n_entity.WcsRobotTaskLog.fields.createdBy.label=Founder
i18n_entity.WcsRobotTaskLog.fields.createdOn.label=Creation time
i18n_entity.WcsRobotTaskLog.fields.id.label=ID
i18n_entity.WcsRobotTaskLog.fields.level.label=Level
i18n_entity.WcsRobotTaskLog.fields.modifiedBy.label=Last modifier
i18n_entity.WcsRobotTaskLog.fields.modifiedOn.label=Modification time
i18n_entity.WcsRobotTaskLog.fields.robotName.label=Robot
i18n_entity.WcsRobotTaskLog.fields.taskId.label=Task number
i18n_entity.WcsRobotTaskLog.fields.tcId.label=Tracking number
i18n_entity.WcsRobotTaskLog.fields.version.label=Revised version
i18n_entity.WcsRobotTaskLog.group=WCS
i18n_entity.WcsRobotTaskLog.label=Robot Task Log
i18n_entity.WcsRobotTaskLog.listCard.category.prefix=Category
i18n_entity.WcsRobotTaskLog.listCard.code.prefix=Code
i18n_entity.WcsRobotTaskLog.listCard.level.prefix=Level
i18n_entity.WcsRobotTaskLog.listCard.taskId.prefix=Mission number