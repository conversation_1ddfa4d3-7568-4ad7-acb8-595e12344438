package com.seer.trick.m4

import com.seer.trick.base.BaseModule
import com.seer.trick.base.BootHelper
import com.seer.trick.falcon.FalconModule
import java.util.*

object M4Cloud {

  fun getVersion(): String? {
    val properties = Properties()
    val stream = M4Cloud.javaClass.getResourceAsStream("/properties-from-pom.properties")
      ?: return null
    stream.use {
      properties.load(it)
    }
    return properties.getProperty("trick.version")
  }
}

fun main(args: Array<String>) {
  BootHelper.parseJsonConfigFileFromArgs0(args)

  val modules = mutableListOf(BaseModule, FalconModule, CloudModule)

  BootHelper.boot(M4Cloud.getVersion(), modules, true)
}