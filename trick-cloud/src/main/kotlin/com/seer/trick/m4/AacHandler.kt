package com.seer.trick.m4

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.getReqBody
import com.seer.trick.base.http.operator
import com.seer.trick.helper.JsonHelper
import io.javalin.http.Context

object AacHandler {

  fun registerHandlers() {
    val c = Handlers("api/aac")
    c.post("gen", ::gen, auth())
  }

  private fun gen(ctx: Context) {
    
    val req: AacGenReq = ctx.getReqBody()
    val acpStr = JsonHelper.writeValueAsString(req.acp)
    val txt = AacGen.buildLicense(req.name, req.fp, acpStr)

    val record: EntityValue = mutableMapOf(
      "name" to req.name,
      "fp" to req.fp,
      "acp" to acpStr,
      "remark" to req.remark,
      "license" to txt,
    )
    EntityRwService.createOne("M4License", record)

    ctx.res().characterEncoding = "UTF-8"
    ctx.result(txt)
  }

  data class AacGenReq(
    val name: String, // 客户名称
    val fp: String, // 机器码
    val acp: Map<String, Any?>, // 权限
    val remark: String, // 备注
  )
}