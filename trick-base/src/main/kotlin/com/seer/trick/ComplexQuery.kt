package com.seer.trick

data class ComplexQuery(
  var type: ComplexQueryType = ComplexQueryType.All,
  var or: Boolean = false,
  var not: Boolean = false,
  var operator: ComplexQueryOperator? = null,
  var field1: String? = null,
  var field2: String? = null,
  var value: Any? = null,
  var items: MutableList<ComplexQuery>? = null,
  //  val subQuery: ComplexSubQuery? = null,
) {

  override fun toString(): String = if (type == ComplexQueryType.All) {
    "(ALL)"
  } else if (type == ComplexQueryType.General) {
    val desc = (if (not) "NOT " else "") + (field1 ?: "") + " " + (operator ?: "") + " " +
      (field2 ?: if (value != null) value else "")
    // if (subQuery != null) {
    // desc += " SubQuery(${subQuery.selectColumn} ${subQuery.table} ${subQuery.where})"
    // }
    "($desc)"
  } else if (type == ComplexQueryType.Compound) {
    val desc: String = if (items.isNullOrEmpty()) {
      ""
    } else {
      items!!.joinToString(if (or) " OR " else " AND ") { obj -> obj.toString() }
    }
    "($desc)"
  } else {
    ""
  }

  companion object {

    fun mergeComplexQueries(q1: ComplexQuery, q2: ComplexQuery): ComplexQuery {
      if (q1.type == ComplexQueryType.All && q2.type == ComplexQueryType.All) return Cq.all()
      if (q1.type == ComplexQueryType.All) return q2
      if (q2.type == ComplexQueryType.All) return q1
      return Cq.and(listOf(q1, q2))
    }
  }
}

enum class ComplexQueryOperator(
  val sqlOp: String, // SQL
  val noValue: Boolean = false, // 查询运算符不需要值
  val multiple: Boolean = false, // 值必须是数组
) {
  Eq("=", false, false),
  Ne("<>", false, false),
  Gt(">", false, false),
  Gte(">=", false, false),
  Lt("<", false, false),
  Lte("<=", false, false),
  In("IN", false, true),
  Between("BETWEEN", false, true),
  Contain("LIKE", false, false),
  ContainIgnoreCase("LIKE", false, false),
  Start("LIKE", false, false),
  End("LIKE", false, false),
  Null("", true, false),
  NotNull("", true, false),
  Empty("", true, false),
  NotEmpty("", true, false),
  CurrentUser("", true, false),
  CurrentUsername("", true, false),
  ThisDay("", true, false),
  ThisWeek("", true, false),
}

enum class ComplexQueryType {
  Compound,
  General,
  All,
}

object Cq {

  fun all(): ComplexQuery = ComplexQuery(ComplexQueryType.All)

  fun and(items: List<ComplexQuery>): ComplexQuery =
    ComplexQuery(ComplexQueryType.Compound, or = false, items = items.toMutableList())

  fun and(vararg items: ComplexQuery): ComplexQuery =
    ComplexQuery(ComplexQueryType.Compound, or = false, items = items.toMutableList())

  /**
   * 防止与脚本语音的关键字冲突
   */
  fun cqAnd(items: List<ComplexQuery>): ComplexQuery =
    ComplexQuery(ComplexQueryType.Compound, or = false, items = items.toMutableList())

  // fun Cq.and(vararg items: ComplexQuery?): ComplexQuery {
  //   val cq = ComplexQuery()
  //   cq.type = ComplexQueryType.Compound
  //   cq.items = java.util.List.of(*items)
  //   cq.or = false
  //   return cq
  // }

  fun or(items: List<ComplexQuery>): ComplexQuery =
    ComplexQuery(ComplexQueryType.Compound, or = true, items = items.toMutableList())

  fun or(vararg items: ComplexQuery): ComplexQuery =
    ComplexQuery(ComplexQueryType.Compound, or = true, items = items.toMutableList())

  /**
   * 防止与脚本语音的关键字冲突
   */
  fun cqOr(items: List<ComplexQuery>): ComplexQuery =
    ComplexQuery(ComplexQueryType.Compound, or = true, items = items.toMutableList())

  // fun cqOr(vararg items: ComplexQuery?): ComplexQuery {
  //   val cq = ComplexQuery()
  //   cq.type = ComplexQueryType.Compound
  //   cq.items = java.util.List.of(*items)
  //   cq.or = true
  //   return cq
  // }

  fun match(vararg target: Pair<String, Any?>): ComplexQuery = and(target.map { (fn, fv) -> eq(fn, fv) })

  fun general(field1: String, op: ComplexQueryOperator, value: Any?): ComplexQuery =
    ComplexQuery(ComplexQueryType.General, field1 = field1, operator = op, value = value)

  fun isNull(field1: String): ComplexQuery = general(field1, ComplexQueryOperator.Null, null)
  fun isNotNull(field1: String): ComplexQuery = general(field1, ComplexQueryOperator.NotNull, null)

  fun eq(field1: String, value: Any?): ComplexQuery = general(field1, ComplexQueryOperator.Eq, value)

  fun idEq(id: String): ComplexQuery = general("id", ComplexQueryOperator.Eq, id)

  fun ne(field1: String, value: Any?): ComplexQuery = general(field1, ComplexQueryOperator.Ne, value)

  fun gt(field1: String, value: Any): ComplexQuery = general(field1, ComplexQueryOperator.Gt, value)

  fun gte(field1: String, value: Any): ComplexQuery = general(field1, ComplexQueryOperator.Gte, value)

  fun lt(field1: String, value: Any): ComplexQuery = general(field1, ComplexQueryOperator.Lt, value)

  fun lte(field1: String, value: Any): ComplexQuery = general(field1, ComplexQueryOperator.Lte, value)

  fun include(field1: String, list: List<*>): ComplexQuery = general(field1, ComplexQueryOperator.In, list)

  fun include(field1: String, vararg list: Any): ComplexQuery = general(field1, ComplexQueryOperator.In, list.toList())

  fun between(field1: String, value1: Any, value2: Any): ComplexQuery =
    general(field1, ComplexQueryOperator.Between, listOf(value1, value2))

  fun startWith(field1: String, value: String): ComplexQuery = general(field1, ComplexQueryOperator.Start, value)

  fun endWith(field1: String, value: String): ComplexQuery = general(field1, ComplexQueryOperator.End, value)

  fun containIgnoreCase(field1: String, value: String): ComplexQuery =
    general(field1, ComplexQueryOperator.ContainIgnoreCase, value)

  fun empty(field1: String): ComplexQuery = general(field1, ComplexQueryOperator.Empty, null)

  fun notEmpty(field1: String): ComplexQuery = general(field1, ComplexQueryOperator.NotEmpty, null)

  fun thisDay(field1: String): ComplexQuery = general(field1, ComplexQueryOperator.ThisDay, null)
  fun thisWeek(field1: String): ComplexQuery = general(field1, ComplexQueryOperator.ThisWeek, null)
  fun currentUser(field1: String): ComplexQuery = general(field1, ComplexQueryOperator.CurrentUser, null)
  fun currentUsername(field1: String): ComplexQuery = general(field1, ComplexQueryOperator.CurrentUsername, null)

  //    fun cqBy(like: EntityValue): ComplexQuery {
  //        val items: MutableList<ComplexQuery> = ArrayList()
  //        for ((k, v) in like) {
  //            if (v != null) items += Cq.eq(k, v)
  //        }
  //        return Cq.and(items)
  //    }
}