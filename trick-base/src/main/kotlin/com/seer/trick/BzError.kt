package com.seer.trick

/**
 * 本框架的异常基类
 */
open class TrickError(
  cause: Throwable?, val code: String, vararg val args: Any?,
) : RuntimeException(I18N.lo(code, args.toList()), cause)

/**
 * 代码 BUG，不可恢复性异常，不应该出现的异常
 */
open class FatalError(
  cause: Throwable?, code: String, vararg args: Any?,
) : TrickError(cause, code, *args) {

  constructor(code: String, vararg args: Any?) : this(null, code, *args)

}

/**
 * 普通错误
 */
open class BzError(
  cause: Throwable?, code: String, vararg args: Any?,
) : TrickError(cause, code, *args) {

  constructor(code: String, vararg args: Any?) : this(null, code, *args)

}