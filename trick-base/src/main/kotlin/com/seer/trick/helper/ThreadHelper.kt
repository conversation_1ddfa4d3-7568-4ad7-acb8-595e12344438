package com.seer.trick.helper

import com.seer.trick.BzError
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.util.concurrent.*
import java.util.concurrent.atomic.AtomicInteger

object ThreadHelper {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 新建有界线程池，主要受 core/max 控制。队列无限（MAX_INT），如果超过了，submit 阻塞（即 CallerRunsPolicy 策略）。
   * TODO 暂时 core/max 相同
   */
  fun newBoundedThreadPool(coreSize: Int = 5, maxSize: Int = 100): ThreadPoolExecutor = ThreadPoolExecutor(
    maxSize,
    maxSize,
    0L,
    TimeUnit.SECONDS,
    LinkedBlockingQueue(),
    ThreadPoolExecutor.CallerRunsPolicy(),
  )

  fun buildNamedThreadFactory(name: String?): ThreadFactory {
    val counter = AtomicInteger(0)
    return ThreadFactory { r ->
      val no = counter.incrementAndGet()
      val thread = Thread(r, String.format("%s-%d", name, no))
      thread.uncaughtExceptionHandler =
        Thread.UncaughtExceptionHandler { _, e -> logger.error("Thread - UncaughtException", e) }
      thread
    }
  }

  /**
   * 单线程长期运行的事情。worker 不能抛出异常，如果抛出异常，是代码 BUG；没必须继续运行。
   */
  fun longRunLoop(delay: Long, logger: Logger, errorMsg: String, endCondition: () -> Boolean, worker: () -> Unit) {
    while (!endCondition()) {
      try {
        worker()
      } catch (e: BzError) {
        logger.error(errorMsg + ": " + e.message)
        return
      } catch (e: Exception) {
        logger.error(errorMsg, e)
        return
      }
      // 不用捕获 Interrupted，如果中断就退出
      Thread.sleep(delay)
    }
  }
}

@Deprecated("这个方法是为了提醒使用 submit 时要捕异常。但这不是一个好的方式。submit 的方法就是要捕异常，这是一个习惯")
fun ExecutorService.submitCatch(desc: String, logger: Logger, worker: () -> Unit): Future<*> = submit {
  try {
    worker()
  } catch (e: InterruptedException) {
    logger.info("Interrupted: $desc")
  } catch (e: Throwable) {
    logger.error("$desc - Failed", e)
  }
}

fun ExecutorService.submitLongRun(
  remark: String,
  logger: Logger,
  getNextDelay: () -> Long,
  worker: () -> Unit,
): Future<*> = submit {
  while (!Thread.interrupted()) {
    try {
      worker()
    } catch (e: InterruptedException) {
      break
    } catch (e: Exception) {
      if (e is BzError) {
        logger.error("长运行任务报错。$remark。${e.message}")
      } else {
        logger.error("长运行任务报错。$remark", e)
      }
    }

    val delay = try {
      getNextDelay()
    } catch (e: Exception) {
      1000
    }

    try {
      Thread.sleep(delay)
    } catch (e: InterruptedException) {
      break
    }
  }

  logger.info("长运行任务停止。$remark")
}