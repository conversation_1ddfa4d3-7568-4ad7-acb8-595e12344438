package com.seer.trick.helper

import com.seer.trick.BzError

object IpHelper {
  
  fun ipToInt(ip: String): Int {
    // 把 IP 存在 4 个字节里，比如 127.0.0.1 -> 0x7f 0x00 0x00 0x01
    val l = ip.split(".")
    if (l.size != 4) throw BzError("errBadIP", ip)
    val il = l.map { NumHelper.anyToInt(it) ?: throw BzError("errBadIP", ip) }
    
    return (il[0] shl 24) or (il[1] shl 16) or (il[2] shl 8) or il[3]
  }
  
}