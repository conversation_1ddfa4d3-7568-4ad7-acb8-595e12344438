package com.seer.trick.helper

import org.apache.commons.lang3.StringUtils

object StringHelper {

  /**
   * 去掉空串
   */
  fun splitTrim(str: String?, sep: String): List<String> {
    if (str.isNullOrBlank()) return emptyList()
    val separator = if (StringUtils.isBlank(sep)) "," else sep
    return str.split(separator).map(String::trim).filter(StringUtils::isNotBlank)
  }
}

/**
 * 取字符串最后 N 个字符。如果字符串长度小于 N，返回全部。
 */
fun String.lastNChars(n: Int): String {
  if (this.length <= n) return this
  return this.substring(this.length - n)
}

/**
 * 用于 UUID/OID 字符串，返回最后 N 位，默认最后 8 位。
 */
fun String.shortId(n: Int = 8): String = this.lastNChars(n)

fun String.containsIgnoreCase(s: String): Boolean = this.lowercase().contains(s.lowercase())

fun String.eqIgnoreCase(other: String): Boolean = this.equals(other, ignoreCase = true)