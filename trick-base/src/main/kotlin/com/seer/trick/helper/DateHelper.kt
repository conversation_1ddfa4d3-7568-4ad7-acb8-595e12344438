package com.seer.trick.helper

import org.apache.commons.lang3.StringUtils
import org.apache.commons.lang3.time.DateFormatUtils
import org.apache.commons.lang3.time.DateUtils
import java.text.SimpleDateFormat
import java.time.*
import java.time.temporal.TemporalAdjusters
import java.util.*
import kotlin.math.round

object DateHelper {

  val sdf = SimpleDateFormat()
  val df1 = sdf.toPattern()
  val df2 = sdf.toLocalizedPattern()

  fun formatDate(d: Date?, format: String): String = if (d == null) "" else DateFormatUtils.format(d, format)

  fun anyToDate(input: Any?): Date? {
    if (input == null) return null
    if (input is Date) return input
    if (input is Instant) return Date.from(input as Instant?)
    if (input is Number) {
      val num = input.toLong()
      return if (num <= 0) null else Date(num)
    }
    if (input is String) {
      val s = input
      return if (StringUtils.isBlank(s)) null else stringToDate(s)
    }
    return null
  }

  fun stringToDate(input: String?): Date? = if (input == null) {
    null
  } else {
    try {
      DateUtils.parseDate(
        input,
        "EEE MMM dd HH:mm:ss z yyyy",
        df1, df2,
        "yyyy-MM-dd'T'HH:mm:ss.SSSXXX",
        "yyyy-MM-dd'T'HH:mm:ss.SSSZ",
        "yyyy-MM-dd'T'HH:mm:ssX",
        "yyyy-MM-dd HH:mm:ss",
        "yyyy-MM-dd HH:mm:ss.SSS",
        "yyyy-MM-dd HH:mm",
        "yyyy-MM-dd",
        "yyyy/MM/dd",
        "yyyy'年'MM'月'dd'日'",
        "HH:mm:ss",
      )
    } catch (e: Exception) {
      null
    }
  }

  fun getDayStart(d: Date): Date = DateUtils.truncate(d, Calendar.DATE)

  fun getDayEnd(d: Date): Date {
    val d2 = DateUtils.truncate(d, Calendar.DATE)
    return Date(DateUtils.addDays(d2, 1).time - 1)
  }

  fun sameHour(d1: Date, d2: Date): Boolean =
    DateUtils.truncate(d1, Calendar.HOUR) == DateUtils.truncate(d2, Calendar.HOUR)

  fun getHourStart(d: Date): Date = DateUtils.truncate(d, Calendar.HOUR)

  fun getHourEnd(d: Date): Date {
    val d2 = DateUtils.truncate(d, Calendar.HOUR)
    return Date(DateUtils.addHours(d2, 1).time - 1)
  }

  /**
   * 返回 d1 - d2 之间 每小时的开始时间
   */
  fun hoursBetween(d1: Date, d2: Date): List<Date> {
    val list = mutableListOf<Date>()
    var d = nextHourStart(d1)
    while (d <= d2) {
      list.add(d)
      d = DateUtils.addHours(d, 1)
    }
    return list
  }

  /**
   * 获取下一小时开始时间
   */
  fun nextHourStart(d: Date): Date = DateUtils.truncate(DateUtils.addHours(d, 1), Calendar.HOUR)

  fun getMondayOfWeek(d: Date): Date {
    val ld: LocalDateTime = d.toInstant()
      .atZone(ZoneId.systemDefault())
      .toLocalDate().atStartOfDay()
    // TemporalAdjusters.previous(DayOfWeek.MONDAY)  若为周一，返回的是上周，若为周二，返回的是本周
    // TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY) 返回的都是本周
    val previousMonday: LocalDateTime = ld.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
    return Date.from(previousMonday.atZone(ZoneId.systemDefault()).toInstant())
  }

  /**
   * 获取季度数
   */
  fun getQuarterOfYear(d: Date?): Int? {
    if (d == null) return null
    val calendar = Calendar.getInstance()
    calendar.time = d
    return calendar.get(Calendar.MONTH) / 3 + 1
  }

  fun parseWeek(weekStr: String?): Date? {
    // Format: "2024 W44"
    if (weekStr == null) return null

    val matchWeek = """(\d{4})\sW(\d{1,2})""".toRegex()
    val matchResult = matchWeek.matchEntire(weekStr)

    return if (matchResult != null) {
      val (year, week) = matchResult.destructured
      Calendar.getInstance().apply {
        firstDayOfWeek = Calendar.MONDAY
        minimalDaysInFirstWeek = 4 // ISO 8601 Week Definition
        set(Calendar.YEAR, year.toInt())
        set(Calendar.WEEK_OF_YEAR, week.toInt())
        set(Calendar.DAY_OF_WEEK, firstDayOfWeek)
        set(Calendar.HOUR_OF_DAY, 0)
        set(Calendar.MINUTE, 0)
        set(Calendar.SECOND, 0)
        set(Calendar.MILLISECOND, 0)
      }.time
    } else {
      null
    }
  }

  fun formatWeek(date: Date): String {
    // Format: "2024 W04"
    val calendar = Calendar.getInstance()
    calendar.time = date
    calendar.firstDayOfWeek = Calendar.MONDAY
    calendar.minimalDaysInFirstWeek = 4 // ISO 8601 Week Definition
    return String.format("%d W%02d", calendar.get(Calendar.YEAR), calendar.get(Calendar.WEEK_OF_YEAR))
  }

  fun formatQuarter(date: Date): String {
    // Format: "2024 Q1"
    val calendar = Calendar.getInstance()
    calendar.time = date
    return String.format("%d Q%d", calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) / 3 + 1)
  }

  fun parseQuarter(quarterStr: String?): Date? {
    // Format: "2024 Q1"
    if (quarterStr == null) return null

    val matchQuarter = """(\d{4})\sQ(\d)""".toRegex()
    val matchResult = matchQuarter.matchEntire(quarterStr)

    return if (matchResult != null) {
      val (year, quarter) = matchResult.destructured
      Calendar.getInstance().apply {
        set(Calendar.YEAR, year.toInt())
        set(Calendar.MONTH, (quarter.toInt() - 1) * 3) // Quarters: Q1->Jan-March, Q2->April-June, etc
        set(Calendar.DAY_OF_MONTH, 1)
        set(Calendar.HOUR_OF_DAY, 0)
        set(Calendar.MINUTE, 0)
        set(Calendar.SECOND, 0)
        set(Calendar.MILLISECOND, 0)
      }.time
    } else {
      null
    }
  }

  /**
   * 毫秒转小时，
   * 保留 5 位小数
   */
  fun millSecondToHour(d: Any?): Double? {
    if (d == null) return null
    if (d is Number) {
      val num = d.toLong()
      return round(num / 1000.0 / 60 / 60 * 1e5) / 1e5
    }
    return null
  }
}