package com.seer.trick.helper

import com.seer.trick.BzError
import org.apache.commons.lang3.StringUtils
import java.math.BigDecimal
import java.math.BigInteger
import java.nio.ByteBuffer
import kotlin.math.abs
import kotlin.math.pow
import kotlin.math.round

object NumHelper {

  /**
   * 将能转换的类型转换为 BigDecimal
   * 注意不检查 String 格式，非法格式报错
   */
  fun anyToBigDecimal(v: Any?): BigDecimal? = when (v) {
    null -> null
    is BigDecimal -> v
    is BigInteger -> v.toBigDecimal()
    is String -> if (v.isBlank()) null else BigDecimal(v)
    is Long -> BigDecimal.valueOf(v)
    is Int -> BigDecimal.valueOf(v.toLong())
    is Short -> BigDecimal.valueOf(v.toLong())
    is Byte -> BigDecimal.valueOf(v.toLong())
    is Double -> BigDecimal.valueOf(v)
    is Float -> BigDecimal.valueOf(v.toDouble())
    else -> null
  }

  fun anyToByte(v: Any?): Byte? = (v as? Number)?.toByte() ?: if (v is String) {
    if (StringUtils.isBlank(v)) null else v.toByte()
  } else {
    null
  }

  fun anyToShort(v: Any?): Short? = (v as? Number)?.toShort() ?: if (v is String) {
    if (StringUtils.isBlank(v)) null else v.toShort()
  } else {
    null
  }

  private val B_D_INT_MAX = BigDecimal.valueOf(Int.MAX_VALUE.toLong())
  private val B_D_INT_MIN = BigDecimal.valueOf(Int.MIN_VALUE.toLong())

  fun anyToInt(v: Any?, overflowCheck: Boolean = false): Int? {
    val b = anyToBigDecimal(v) ?: return null
    return if (overflowCheck && (b > B_D_INT_MAX || b < B_D_INT_MIN)) {
      throw BzError("errNumOverflow", v, Int.MIN_VALUE, Int.MAX_VALUE)
    } else {
      b.toInt()
    }
  }

  private val B_D_LONG_MAX = BigDecimal.valueOf(Long.MAX_VALUE)
  private val B_D_LONG_MIN = BigDecimal.valueOf(Long.MIN_VALUE)

  fun anyToLong(v: Any?, overflowCheck: Boolean = false): Long? {
    val b = anyToBigDecimal(v) ?: return null
    return if (overflowCheck && (b > B_D_LONG_MAX || b < B_D_LONG_MIN)) {
      throw BzError("errNumOverflow", v, Long.MIN_VALUE, Long.MAX_VALUE)
    } else {
      b.toLong()
    }
  }

  private val B_D_FLOAT_MAX = BigDecimal.valueOf(Float.MAX_VALUE.toDouble())

  // 浮点类型的最小值不是 MIN_VALUE
  private val B_D_FLOAT_MIN = BigDecimal.valueOf(-Float.MAX_VALUE.toDouble())

  fun anyToFloat(v: Any?, overflowCheck: Boolean = false): Float? {
    val b = anyToBigDecimal(v) ?: return null
    return if (overflowCheck && (b > B_D_FLOAT_MAX || b < B_D_FLOAT_MIN)) {
      throw BzError("errNumOverflow", v, Float.MIN_VALUE, Float.MAX_VALUE)
    } else {
      b.toFloat()
    }
  }

  private val B_D_DOUBLE_MAX = BigDecimal.valueOf(Double.MAX_VALUE)

  // 浮点类型的最小值不是 MIN_VALUE
  private val B_D_DOUBLE_MIN = BigDecimal.valueOf(-Double.MAX_VALUE)

  fun anyToDouble(v: Any?, overflowCheck: Boolean = false): Double? {
    val b = anyToBigDecimal(v) ?: return null
    return if (overflowCheck && (b > B_D_DOUBLE_MAX || b < B_D_DOUBLE_MIN)) {
      throw BzError("errNumOverflow", v, Double.MIN_VALUE, Double.MAX_VALUE)
    } else {
      b.toDouble()
    }
  }

  fun isNumberEqual(a: Double, b: Double): Boolean = abs(a - b) < 1e-9

  fun uByteToInt(byte: Byte): Int = (byte.toInt() and 0xFF)

  fun byteArrayToShort(bytes: ByteArray?): Short {
    val buffer = ByteBuffer.allocate(java.lang.Short.BYTES)
    buffer.put(bytes)
    buffer.rewind()
    return buffer.getShort()
  }

  fun shortToByteArray(value: Short?): ByteArray {
    val buffer = ByteBuffer.allocate(java.lang.Short.BYTES)
    buffer.putShort(value!!)
    buffer.rewind()
    return buffer.array()
  }

  fun byteArrayToInt(bytes: ByteArray?): Int {
    val buffer = ByteBuffer.allocate(Integer.BYTES)
    buffer.put(bytes)
    buffer.rewind()
    return buffer.getInt()
  }

  fun intToByteArray(value: Int?): ByteArray {
    val buffer = ByteBuffer.allocate(Integer.BYTES)
    buffer.putInt(value!!)
    buffer.rewind()
    return buffer.array()
  }

  fun byteArrayToLong(bytes: ByteArray?): Long {
    val buffer = ByteBuffer.allocate(java.lang.Long.BYTES)
    buffer.put(bytes)
    buffer.rewind()
    return buffer.getLong()
  }

  fun longToByteArray(value: Long?): ByteArray {
    val buffer = ByteBuffer.allocate(java.lang.Long.BYTES)
    buffer.putLong(value!!)
    buffer.rewind()
    return buffer.array()
  }

  fun byteArrayToFloat(bytes: ByteArray?): Float = ByteBuffer.wrap(bytes).float

  fun floatToByteArray(value: Float): ByteArray = ByteBuffer.allocate(java.lang.Float.BYTES).putFloat(value).array()

  fun byteArrayToDouble(bytes: ByteArray?): Double = ByteBuffer.wrap(bytes).double

  fun doubleToByteArray(value: Double): ByteArray = ByteBuffer.allocate(java.lang.Double.BYTES).putDouble(value).array()

  fun Double.roundToDecimal(decimals: Int): Double = round(this * 10.0.pow(decimals)) / 10.0.pow(decimals)
}