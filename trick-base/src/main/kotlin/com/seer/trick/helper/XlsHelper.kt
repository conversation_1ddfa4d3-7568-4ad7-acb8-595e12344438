package com.seer.trick.helper

import com.seer.trick.BzError
import java.io.*
import java.util.Date
import org.apache.poi.ss.usermodel.*
import org.apache.poi.xssf.streaming.SXSSFWorkbook
import org.apache.poi.xssf.usermodel.XSSFWorkbook

object XlsHelper {

  fun <T> prepareExport(
    entityLabel: String, columnLabels: List<String>, entityValues: List<T>, fill: RowBuilderFill<T>
  ): XSSFWorkbook {
    val workbook = XSSFWorkbook()
    val sheet = workbook.createSheet(entityLabel)

    // columns
    val head = sheet.createRow(0)
    for (j in columnLabels.indices) {
      head.createCell(j).setCellValue(columnLabels[j])
    }
    for (i in entityValues.indices) {
      val ev = entityValues[i]
      val row = sheet.createRow(i + 1)
      val rowBuilder = RowBuilder(row)
      fill.fill(ev, rowBuilder)
    }
    return workbook
  }

  // 修改后：
  fun workbookToFile(workbook: SXSSFWorkbook, file: File) {  // 参数类型改为 SXSSFWorkbook
    try {
      FileOutputStream(file).use { out ->
        workbook.write(out)
      }
    } finally {
      workbook.dispose()  // 清理临时文件
      workbook.close()    // 关闭资源
    }
  }

  fun importXls(ins: InputStream, process: RowParserRun) {
    ins.use { fis ->
      val workbook = XSSFWorkbook(fis)
      val sheet = workbook.getSheetAt(0)
      val rowIterator = sheet.rowIterator()
      var rowIndex = 0
      while (rowIterator.hasNext()) {
        val row = rowIterator.next()
        if (isRowBlank(row)) continue
        process.process(rowIndex, row)
        rowIndex++
      }
    }
  }

  fun toJavaValue(cell: Cell): Any? {
    return if (cell.cellType == null) null
    else when (cell.cellType!!) {
      CellType.BLANK, CellType._NONE, CellType.ERROR -> null
      CellType.BOOLEAN -> cell.booleanCellValue
      CellType.STRING -> cell.stringCellValue
      CellType.NUMERIC -> {
        if (DateUtil.isCellDateFormatted(cell)) {
          DateUtil.getJavaDate(cell.numericCellValue)
        } else {
          cell.numericCellValue
        }
      }

      CellType.FORMULA -> BzError("errXlsFormulaNotSupported")
    }
  }

  private fun isRowBlank(row: Row): Boolean {
    val ci = row.cellIterator()
    while (ci.hasNext()) {
      val cell = ci.next()
      val v = toJavaValue(cell) ?: continue
      if (v is String && v.isBlank()) continue
      return false
    }
    return true
  }

  fun rowToStringList(row: Row, headers: MutableList<String>) {
    val ci = row.cellIterator()
    while (ci.hasNext()) {
      val cell = ci.next()
      headers.add(cell.stringCellValue)
    }
  }

  fun rowToList(row: Row, row2: MutableList<Any?>) {
    val ci = row.cellIterator()
    while (ci.hasNext()) {
      val cell = ci.next()
      row2 += toJavaValue(cell)
    }
  }

}

class RowBuilder(private val row: Row) {

  private var ci = 0

  fun setAny(v: Any?) {
    when (v) {
      null -> setNull()
      is List<*> -> setNull() // TODO
      is String -> setString(v)
      is Boolean -> setBoolean(v)
      is Double -> setDouble(v)
      is Int -> setInt(v)
      is Long -> setLong(v)
      is Date -> setAsDateTime(v)
    }
  }

  fun setNull() {
    row.createCell(ci).setCellValue("")
    ci++
  }

  fun setString(str: String?) {
    var s = str ?: ""
    if (s.length > 32767) s = s.substring(0, 32765)
    row.createCell(ci).setCellValue(s)
    ci++
  }

  fun setBoolean(b: Boolean) {
    val str = if (java.lang.Boolean.TRUE == b) "Y" else "N"
    row.createCell(ci).setCellValue(str)
    ci++
  }

  fun setDouble(num: Double?) {
    row.createCell(ci).setCellValue(num ?: 0.0)
    ci++
  }

  fun setInt(i: Int?) {
    row.createCell(ci).setCellValue(i?.toDouble() ?: 0.0)
    ci++
  }

  fun setLong(i: Long?) {
    row.createCell(ci).setCellValue(i?.toDouble() ?: 0.0)
    ci++
  }

  fun setAsDateTime(d: Date?) {
    row.createCell(ci).setCellValue(DateHelper.formatDate(d, "yyyy-MM-dd HH:mm:ss"))
    ci++
  }
}

fun interface RowBuilderFill<T> {
  fun fill(ev: T, rb: RowBuilder)
}

fun interface RowParserRun {
  fun process(rowIndex: Int, row: Row): Any?
}