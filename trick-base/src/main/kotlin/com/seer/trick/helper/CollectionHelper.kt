package com.seer.trick.helper

import java.util.function.Function

object CollectionHelper {
  
  fun <T> listToMap(list: List<T>, getKey: Function<T, String>): Map<String, T> {
    val map = HashMap<String, T>()
    for (item in list) {
      val key = getKey.apply(item)
      map[key] = item
    }
    return map
  }
  
  fun <T> setItemToList(list: MutableList<T?>, index: Int, item: T?) {
    if (list.size < index + 1) {
      val times = index + 1 - list.size
      for (i in 1..times) list.add(null)
    }
    list[index] = item
  }

  /**
   * 两个 Set 有交集
   */
  fun setsHasSame(a: Set<*>, b: Set<*>): Boolean {
    for (v in a) {
      if (b.contains(v)) return true
    }
    return false
  }
  
}
