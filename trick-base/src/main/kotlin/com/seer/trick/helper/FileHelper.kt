package com.seer.trick.helper

import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream
import org.apache.commons.io.IOUtils
import java.io.*
import java.nio.charset.StandardCharsets
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.Paths
import java.nio.file.attribute.BasicFileAttributes
import java.util.Date
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream

object FileHelper {

  fun loadClasspathResourceAsString(name: String): String? {
    try {
      FileHelper::class.java.getResourceAsStream(name).use {
        return if (it == null) null else IOUtils.toString(it, StandardCharsets.UTF_8)
      }
    } catch (e: Exception) {
      return null
    }
  }

  fun loadClasspathResourceAsBytes(name: String): ByteArray? {
    try {
      FileHelper::class.java.getResourceAsStream(name).use {
        return if (it == null) null else IOUtils.toByteArray(it)
      }
    } catch (e: Exception) {
      return null
    }
  }

  fun loadClasspathResourceAsStream(name: String): InputStream? = try {
    FileHelper::class.java.getResourceAsStream(name)
  } catch (e: Exception) {
    null
  }

  fun searchFileFromWorkDirUp(filename: String): File? {
    var dir: File? = File(System.getProperty("user.dir"))
    while (dir != null) {
      val file = File(dir, filename)
      if (file.exists()) return file
      dir = file.parentFile
    }
    return null
  }

  fun searchDirectoryFromWorkDirUp(filename: String): File? {
    var dir: File? = File(System.getProperty("user.dir"))
    while (dir != null) {
      val file = File(dir, filename)
      if (file.exists() && file.isDirectory) return file
      dir = file.parentFile
    }
    return null
  }

  fun isFileInDir(file: File, dir: File): Boolean = file.canonicalPath.startsWith(dir.canonicalPath)

  fun zipDirToFile(dir: File, file: File) {
    FileOutputStream(file).use { os -> zipDirToStream(dir, os) }
  }

  fun zipFilesToFile(files: List<File>, zipFile: File) {
    FileOutputStream(zipFile).use { os ->
      ZipOutputStream(os).use { zs ->
        for (f in files) {
          val zipEntry = ZipEntry(f.name)
          zs.putNextEntry(zipEntry)
          Files.copy(f.toPath(), zs)
          zs.closeEntry()
        }
      }
    }
  }

  private fun zipDirToStream(dir: File, stream: OutputStream) {
    ZipOutputStream(stream).use { zs ->
      val pp = Paths.get(dir.absolutePath)
      Files.walk(pp).use { s ->
        s.filter { path -> !Files.isDirectory(path) }.forEach { path ->
          val zipEntry = ZipEntry(pp.relativize(path).toString())
          zs.putNextEntry(zipEntry)
          Files.copy(path, zs)
          zs.closeEntry()
        }
      }
    }
  }

  // https://stackoverflow.com/questions/47208272/android-zipinputstream-only-deflated-entries-can-have-ext-descriptor
  // https://www.baeldung.com/apache-commons-compress-project
  // TODO Expander().expand(rdsCoreSceneZipFile, rdsCoreDir)
  fun unzipFileToDir(zipFile: File, dir: File) {
    FileInputStream(zipFile).use {
      unzipStreamToDir(it, dir)
    }
  }

  fun unzipFileToDir(zipFilePath: String, dir: File) {
    unzipFileToDir(File(zipFilePath), dir)
  }

  // https://bugs.openjdk.org/browse/JDK-8054027
  // https://stackoverflow.com/questions/17326042/getnextentry-doesnt-display-folder-as-an-entry
  fun unzipStreamToDir(inputStream: InputStream, dir: File) {
    if (!dir.exists()) dir.mkdirs()
    ZipArchiveInputStream(inputStream, "UTF8", false, true).use { zis ->
      generateSequence { zis.nextEntry }.forEach { entry ->
        val newFile = Paths.get(dir.absolutePath, entry.name).normalize().toFile().canonicalFile
        if (entry.isDirectory) {
          newFile.mkdirs()
        } else {
          newFile.parentFile?.takeIf { !it.exists() }?.mkdirs()
          FileOutputStream(newFile).use { zis.copyTo(it) }
        }
      }
    }
  }

  // private val logger = LoggerFactory.getLogger(FileUtil::class.java)
  //    fun searchFileFromWorkDirUp(filename: String): File? {
  //        var dir: File? = File(System.getProperty("user.dir"))
  //        // logger.info("Search file $filename up from $dir")
  //
  //        while (dir != null) {
  //            //LOG.debug("Search config file $configFilename in $dir")
  //            val file = File(dir, filename)
  //            if (file.exists()) return file
  //            dir = file.parentFile?.parentFile
  //        }
  //        return null
  //    }
  //
  //    fun searchDirectoryFromWorkDirUp(dirName: String): File? {
  //        var dir: File? = File(System.getProperty("user.dir"))
  //        // logger.info("Search directory $dirName up from $dir")
  //
  //        while (dir != null) {
  //            //LOG.debug("Search config file $configFilename in $dir")
  //            val file = File(dir, dirName)
  //            if (file.exists() && file.isDirectory) return file
  //            dir = file.parentFile?.parentFile
  //        }
  //        return null
  //    }

  /**
   * 判断文件是否为 zip
   */
  fun isZipFile(file: File): Boolean {
    // 先检查是否是文件且可读
    if (!file.isFile || !file.canRead()) return false
    // 检查文件头
    val hasZipHeader = try {
      RandomAccessFile(file, "r").use { raf ->
        val header = raf.readInt()
        // Zip 文件头可能的魔数：
        // 0x504B0304 - 标准ZIP文件
        // 0x504B0506 - 空归档
        // 0x504B0708 - 分卷归档
        header == 0x504B0304 || header == 0x504B0506 || header == 0x504B0708
      }
    } catch (e: Exception) {
      false
    }
    // TODO 暂时只验证头部
    return hasZipHeader
  }
}

fun File.getCreationTime(): Date =
  Date.from(Files.readAttributes(this.toPath(), BasicFileAttributes::class.java).creationTime().toInstant())

fun Path.getCreationTime(): Date =
  Date.from(Files.readAttributes(this, BasicFileAttributes::class.java).creationTime().toInstant())