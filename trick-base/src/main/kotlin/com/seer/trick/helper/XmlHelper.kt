package com.seer.trick.helper

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.dataformat.xml.XmlMapper
import com.seer.trick.helper.JsonHelper.mapper

object XmlHelper {

  val xmlMapper = XmlMapper()

  fun xmlToJson(v: Any?): JsonNode? {
    if (v == null) return v
    val str = v.toString()
    return xmlMapper.readTree(str)
  }

  fun writeValueAsString(v: Any?): String {
    if (v == null) return ""
    if (v is String) return v
    return mapper.writeValueAsString(v)
  }

}