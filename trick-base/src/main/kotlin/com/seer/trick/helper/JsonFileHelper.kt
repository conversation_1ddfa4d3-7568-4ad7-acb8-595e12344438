package com.seer.trick.helper

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import org.apache.commons.io.FileUtils
import org.apache.commons.lang3.StringUtils
import java.io.File
import java.nio.charset.StandardCharsets
import java.nio.file.Files

object JsonFileHelper {
  fun writeJsonToFile(file: File, obj: Any?, pretty: Boolean = false) {
    val str: String = if (pretty) {
      JsonHelper.mapper.writerWithDefaultPrettyPrinter().writeValueAsString(obj)
    } else {
      JsonHelper.mapper.writeValueAsString(obj)
    }
    FileUtils.writeStringToFile(file, str, StandardCharsets.UTF_8)
  }

  inline fun <reified T> readJsonFromFile(file: File): T? = if (file.exists()) {
    val str: String = FileUtils.readFileToString(file, StandardCharsets.UTF_8)
    if (StringUtils.isNotBlank(str)) {
      JsonHelper.mapper.readValue(str, jacksonTypeRef())
    } else {
      null
    }
  } else {
    null
  }

  /**
   *  相较于 readJsonFromFile ，该方法的优势在于
   *  readJsonFromFile 将整个文件读取到内存，并创建一个很大的 string 对象
   *  而本方法直接从流中读取，不需要预先加载文件内容
   *  对于大的文件，该方法要更快，但对于小文件其实是差不多的，因为底层都使用的 是 fileChannel。
   *  两个方法同样读取 300 m 的 json 文件
   *  readJsonFromFile 耗时约 4s
   *  readJsonFromFileByBuffer 耗时约 2s
   *  buffered 可以不要测试下来影响忽略不计 todo 是否要加 buffer
   */
  inline fun <reified T> readJsonFromFileByBuffer(file: File): T? {
    if (!file.exists()) return null

    return Files.newInputStream(file.toPath()).buffered().use { inputStream ->
      JsonHelper.mapper.readValue(inputStream, jacksonTypeRef<T>())
    }
  }

  /**
   * 增量更新配置文件的内容，适用于复杂对象如 BaseConfig。
   * 增量更新的原因是，避免注释内容的丢失
   */
  fun writeJsonToFileIncremental(file: File, newConfig: MutableMap<String, Any>, pretty: Boolean = false) {
    val oldConfig: MutableMap<String, Any> = readJsonFromFile(file) ?: HashMap()

    // 合并旧配置和新配置
    val mergedConfig = mergeMaps(oldConfig, newConfig)

    // 写入合并后的对象
    writeJsonToFile(file, mergedConfig, pretty)
  }

  /**
   * 递归合并两个 Map 对象
   * 对于相同的 key，新值生效；旧中有的、新中没有的 key 会被保留
   */
  private fun mergeMaps(oldConfig: Map<String, Any>, newConfig: MutableMap<String, Any>): MutableMap<String, Any> {
    oldConfig.forEach { (key, oldValue) ->
      newConfig.putIfAbsent(key, oldValue)
    }
    return newConfig
  }
}