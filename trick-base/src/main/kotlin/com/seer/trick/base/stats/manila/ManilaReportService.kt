package com.seer.trick.base.stats.manila

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.ComplexQuery
import com.seer.trick.Cq
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.concurrent.BaseConcurrentCenter
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.*
import com.seer.trick.base.http.handler.ManilaStatRequest
import com.seer.trick.base.soc.SysMonitorService
import com.seer.trick.base.stats.manila.domain.ManilaReportCfg
import com.seer.trick.base.stats.manila.secondaryStat.SumStatsStrategy
import com.seer.trick.helper.DateHelper
import com.seer.trick.helper.FileHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.NumHelper
import org.apache.commons.lang3.time.DateFormatUtils
import org.apache.commons.lang3.time.DateUtils
import org.slf4j.LoggerFactory
import java.io.File
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

object ManilaReportService {
  private val logger = LoggerFactory.getLogger(javaClass)
  const val CONVERTER_DEFAULT_ALL = "defaultAll"
  const val CONVERTER_MILL_SECOND_TO_HOUR = "millSecondToHour"

  // TODO 暴露接口，用于更新 disabled，界面可以指定哪些科目需要开启/禁用统计
  // 要统计的配置 list，每个 config 对应 StatsTimelineValueReport 中的一条记录
  private val configs = mutableListOf<ManilaReportCfg>()

  // 数据处理的 map { 函数名 -> 函数}
  private val converterMap: MutableMap<String, ((v: Any?) -> Any?)?> = ConcurrentHashMap()

  // 其它包中，定制的统计代码
  val reportGenerators: MutableList<(additionalCq: ComplexQuery?) -> Unit> = mutableListOf()

  // 定时器向 worker 中插入任务
  private val scheduler = Executors.newSingleThreadScheduledExecutor()

  // 统计报表放到单线程中执行，避免占用过多资源
  // private val worker = Executors.newSingleThreadExecutor()

  // TODO 加状态标记是否在统计中，统计中的话，就不再统计了

  fun init() {
    

    loadStatsConfig()

    // 注册默认的 converters
    registerConverter(CONVERTER_DEFAULT_ALL, ::getDefaultAll)
    registerConverter(CONVERTER_MILL_SECOND_TO_HOUR, DateHelper::millSecondToHour)

    // 每 60 分钟重新生成 report 一次 60 * 60 * 1000
    scheduler.scheduleAtFixedRate(ManilaReportService::genReport, 30 * 60 * 1000, 60 * 60 * 1000, TimeUnit.MILLISECONDS)
  }

  private fun getDefaultAll(v: Any?) = "all"

  fun dispose() {
    scheduler.shutdownNow()
    // BaseConcurrentCenter.statsExecutor.shutdownNow()
  }

  fun getConfigs() = configs

  // 加载配置文件
  private fun loadStatsConfig() {
    

    // 加载 resources 目录下的统计配置
    val statsConfigStr = FileHelper.loadClasspathResourceAsString("/stats-config.json")
    if (!statsConfigStr.isNullOrBlank()) {
      val configs: List<ManilaReportCfg> = JsonHelper.mapper.readValue(statsConfigStr, jacksonTypeRef())
      logger.info("核心统计配置 ${configs.map { it.id }} ")
      configs.forEach {
        registerCfgIncludeAll(it)
      }
    } else {
      logger.info("核心统计配置为空")
    }

    // 加载项目包 config 目录下的统计配置
    val extStatsConfigFile = File(BaseCenter.baseConfig.configDir, "stats-config.json")
    if (extStatsConfigFile.exists()) {
      logger.info("扩展统计配置路径 ${extStatsConfigFile.absolutePath}")
      val extConfigs: List<ManilaReportCfg> = JsonHelper.mapper.readValue(extStatsConfigFile, jacksonTypeRef())
      extConfigs.forEach { registerCfgIncludeAll(it) }
    } else {
      // logger.info("扩展统计配置为空")
    }
  }

  private fun registerCfgIncludeAll(it: ManilaReportCfg) {
    // 加上 all 类型的统计
    if (it.includeAll) {
      val allGroupBy = it.changeGroupBy.filter { gy -> gy.alias != it.resKeyTarget }
      val allField = it.changeAggFields.filter { field -> field.alias != it.resKeyTarget }
      val allConverters = it.converters + mapOf("all" to CONVERTER_DEFAULT_ALL)
      val allCfg = it.copy(
        id = "${it.id}-all",
        resKeyTarget = "all",
        changeAggFields = allField,
        changeGroupBy = allGroupBy,
        converters = allConverters,
      )
      registerCfg(allCfg)
    }
    registerCfg(it)
  }

  /**
   * 注册统计配置
   */
  fun registerCfg(cfg: ManilaReportCfg) {
    // worker.submit { statsReport(cfg) }
    // SysMonitorService.log(
    //   
    //   "Stat",
    //   "Register",
    //   "registerCfg",
    //   "ManilaReportService 注册 ManilaReportCfg: $cfg",
    //   remove = true,
    //   instant = disableLogging(),
    // )
    // Id 不能重复，做一个提示
    configs.forEach {
      if (it.id == cfg.id) {
        SysMonitorService.log(
          "Stat",
          "Register",
          "registerCfg",
          "Id: ${cfg.id} 冲突",
          remove = true,
          level = SysMonitorService.SysMonitorLevel.Error,
          instant = disableLogging(),
        )
      }
    }
    configs.add(cfg)
  }

  fun registerConverter(converterName: String, converterFunction: ((v: Any?) -> Any?)?) {
    converterMap[converterName] = converterFunction
  }

  /**
   * 重新生成统计报表
   *
   * 供手动刷新报表
   */
  fun genReport(additionalCq: ComplexQuery? = null) {
    val delay = delay()
    configs.sortedWith(compareBy({ it.statsDateType.ordinal }, { it.statsOrder })).map {
      BaseConcurrentCenter.statsExecutor.submit {
        
        try {
          val newManilaReportCfg = if (additionalCq == null) {
            it
          } else {
            val newChangeFilter = Cq.and(listOf(it.changeFilter, additionalCq))
            it.copy(changeFilter = newChangeFilter)
          }
          stats(newManilaReportCfg)

          Thread.sleep(delay)
        } catch (e: Exception) {
          SysMonitorService.log(
            
            "Stat",
            it.id,
            it.resKeyTarget,
            "Stat fail: ${e.message}",
            instant = disableLogging(),
          )
        }
      }
    }
  }

  /**
   * 是否禁用统计
   */
  fun disabled(): Boolean = BzConfigManager.getByPath("ScStat", "disabled") as Boolean? ?: false

  /**
   * 是否禁用日志
   */
  fun disableLogging(): Boolean = BzConfigManager.getByPath("ScStat", "disableLogging") as Boolean? ?: false

  /**
   * 每个统计科目的延时，默认是 3000 毫秒，880 统计时避免卡住
   */
  fun delay(): Long = NumHelper.anyToLong(BzConfigManager.getByPath("ScStat", "delay")) ?: 3000

  /**
   * 聚合状态变化表 XxxChangeTimeline 中的数据，存到时间序列数值报表 StatsTimelineValueReport
   *
   * 统计结果覆盖更新。
   *
   * 仅支持单 value 模式，同 subject、obj 多 value 模式创建多条记录。
   *
   * TODO 目前每次都统计全量的，后续优化未根据 cfg.statsDateType 只统计增量的
   * TODO 暂定时间用 状态变化表 中的 createdOn，需要用更合理的时间点
   */
  private fun stats(cfg: ManilaReportCfg) {
    if (cfg.disabled || disabled()) return
    if (cfg.statsDateType == StatisticDateType.Hour ||
      cfg.statsDateType == StatisticDateType.Day
    ) {
      statsByRobotPropChangeTimeline(cfg)
    } else {
      statsByStatsTimelineValueReport(cfg)
    }
  }

  private fun statsByRobotPropChangeTimeline(cfg: ManilaReportCfg) {
    SysMonitorService.log("Stat", cfg.id, cfg.resKeyTarget, "Start::stat", instant = disableLogging())
    // 聚合
    val r = fetchBaseReports(cfg)
    SysMonitorService.log(
      
      "Stat",
      cfg.id,
      cfg.resKeyTarget,
      "aggregate, size: ${r.size}, first: ${r.firstOrNull()}",
      instant = disableLogging(),
    )
    // 统计结果覆盖更新
    for (ev in r) {
      val resKeyTargetConverter = if (cfg.converters.containsKey(cfg.resKeyTarget)) {
        converterMap.getOrDefault(cfg.converters[cfg.resKeyTarget], null)
      } else {
        null
      }
      val target = convertValue(resKeyTargetConverter, ev[cfg.resKeyTarget])
      val period = ev[cfg.resKeyPeriod] as String?
      val resKeyValueConverter = if (cfg.converters.containsKey(cfg.resKeyValue)) {
        converterMap.getOrDefault(cfg.converters[cfg.resKeyValue], null)
      } else {
        null
      }
      val v = convertValue(resKeyValueConverter, ev[cfg.resKeyValue])
      EntityRwService.removeMany(
        
        "StatsTimelineValueReport",
        Cq.and(
          Cq.eq("subject", cfg.reportSubject),
          Cq.eq("target", target),
          Cq.eq("periodType", cfg.statsDateType),
          Cq.eq("period", wrapPeriod(cfg, period)),
        ),
      )
      EntityRwService.createOne(
        
        "StatsTimelineValueReport",
        mutableMapOf(
          "subject" to cfg.reportSubject,
          "target" to target,
          "periodType" to cfg.statsDateType.name,
          "period" to wrapPeriod(cfg, period),
          "value" to v,
          "startedOn" to period2StartedOn(period, cfg.statsDateType),
          "finishedOn" to period2FinishedOn(period, cfg.statsDateType),
          "molecular" to ev["molecular"],
          "denominator" to ev["denominator"],
        ),
      )
    }
    SysMonitorService.log(
      
      "Stat",
      cfg.id,
      cfg.resKeyTarget,
      "Finish::stat",
      remove = true,
      instant = disableLogging(),
    )
  }

  private fun statsByStatsTimelineValueReport(cfg: ManilaReportCfg) {
    SysMonitorService.log("Stat", cfg.id, cfg.resKeyTarget, "Start::stat", instant = disableLogging())

    val baseReports = fetchBaseReports(cfg)
    if (baseReports.isEmpty()) return

    SumStatsStrategy().execute(cfg, baseReports)

    SysMonitorService.log(
      
      "Stat",
      cfg.id,
      cfg.resKeyTarget,
      "Finish::stat",
      remove = true,
      instant = disableLogging(),
    )
  }

  private fun fetchBaseReports(cfg: ManilaReportCfg): List<EntityValue> {
    val ao = AggregationOptions(fields = cfg.changeAggFields, groupBy = cfg.changeGroupBy)
    return EntityStatsService.aggregateQuery(cfg.changeEntityName, cfg.changeFilter, ao)
  }

  /**
   * 临时将时间单位转换成小时
   */
  private fun convertValue(converter: ((v: Any?) -> Any?)?, v: Any?): Any? {
    if (converter == null) return v
    return converter.invoke(v)
  }

  // TODO 临时处理：将 Hour 模式的日期由 2024-10-29 13 转换为 2024-10-29 13H
  private fun wrapPeriod(cfg: ManilaReportCfg, period: String?): String? =
    if (cfg.statsDateType == StatisticDateType.Hour && period != null) "${period}H" else period

  // 周期 -> 周期开始时间
  fun period2StartedOn(cycleStr: String?, statsDateType: StatisticDateType): Date? = if (cycleStr == null) {
    null
  } else {
    when (statsDateType) {
      StatisticDateType.Hour -> DateUtils.parseDate(cycleStr, "yyyy-MM-dd HH")
      StatisticDateType.Day -> DateUtils.parseDate(cycleStr, "yyyy-MM-dd")
      StatisticDateType.Month -> DateUtils.parseDate(cycleStr, "yyyy-MM")
      StatisticDateType.Year -> DateUtils.parseDate(cycleStr, "yyyy")
      StatisticDateType.Week -> DateHelper.parseWeek(cycleStr)
      StatisticDateType.Quarter -> DateHelper.parseQuarter(cycleStr)
    }
  }

  // 周期 -> 周期结束时间
  fun period2FinishedOn(cycleStr: String?, type: StatisticDateType): Date? {
    if (cycleStr == null) return null
    val date = period2StartedOn(cycleStr, type) ?: return null
    return toFinishedOn(type, date)
  }

  fun formatPeriod(date: Date, type: StatisticDateType): String = when (type) {
    StatisticDateType.Hour -> DateFormatUtils.format(date, "yyyy-MM-dd HH") + "H"
    StatisticDateType.Day -> DateFormatUtils.format(date, "yyyy-MM-dd")
    StatisticDateType.Week -> DateHelper.formatWeek(date)
    StatisticDateType.Month -> DateFormatUtils.format(date, "yyyy-MM")
    StatisticDateType.Quarter -> DateHelper.formatQuarter(date)
    StatisticDateType.Year -> DateFormatUtils.format(date, "yyyy")
  }

  fun date2StartedOn(date: Date, typ: StatisticDateType): Date = when (typ) {
    StatisticDateType.Hour -> DateUtils.truncate(date, Calendar.HOUR)
    StatisticDateType.Day -> DateUtils.truncate(date, Calendar.DATE)
    StatisticDateType.Week -> DateUtils.truncate(date, Calendar.DATE) // 暂时
    StatisticDateType.Month -> DateUtils.truncate(date, Calendar.MONTH)
    StatisticDateType.Quarter -> DateUtils.truncate(date, Calendar.MONTH) // 暂时
    StatisticDateType.Year -> DateUtils.truncate(date, Calendar.YEAR)
  }

  fun date2FinishedOn(date: Date, typ: StatisticDateType): Date {
    val dt = date2StartedOn(date, typ)
    return toFinishedOn(typ, dt)
  }

  private fun toFinishedOn(typ: StatisticDateType, dt: Date): Date = when (typ) {
    StatisticDateType.Hour -> DateUtils.addMilliseconds(DateUtils.addHours(dt, 1), -1)
    StatisticDateType.Day -> DateUtils.addMilliseconds(DateUtils.addDays(dt, 1), -1)
    StatisticDateType.Week -> DateUtils.addMilliseconds(DateUtils.addWeeks(dt, 1), -1)
    StatisticDateType.Month -> DateUtils.addMilliseconds(DateUtils.addMonths(dt, 1), -1)
    StatisticDateType.Quarter -> DateUtils.addMilliseconds(DateUtils.addMonths(dt, 3), -1)
    StatisticDateType.Year -> DateUtils.addMilliseconds(DateUtils.addYears(dt, 1), -1)
  }

  // 查询【时间序列数值报表】的数据
  fun fetchManilaReport(request: ManilaStatRequest): List<EntityValue> {
    val commonConditions = mutableListOf(
      Cq.eq("periodType", request.periodType),
    )
    if (request.subjects.isNotEmpty()) {
      commonConditions.add(Cq.include("subject", request.subjects))
    }
    if (request.targets.isNotEmpty()) {
      commonConditions.add(Cq.include("target", request.targets))
    }

    // 通过 “时间点（period）” 或 “时间戳（startedOn, finishedOn）” 查询，两者二选一
    val tps = request.timepointStart
    val tpe = request.timepointEnd
    val tss = request.timestampStart
    val tse = request.timestampEnd
    val byTimepoint = !tps.isNullOrBlank() || !tpe.isNullOrBlank()
    val byTimestamp = tss != null || tse != null
    if (byTimestamp && byTimepoint) {
      throw BzError("errBadRequestTimepointOrTimestamp")
    }

    val timeConditions = mutableListOf<ComplexQuery>()
    if (byTimepoint) { // 通过时间点查询
      if (!tps.isNullOrBlank() && !tpe.isNullOrBlank()) {
        // 指定了开始的时间点和结束的时间点。
        timeConditions.add(Cq.and(Cq.gte("period", tps), Cq.lte("period", tpe)))
      } else if (!tps.isNullOrBlank()) {
        // 仅指定了开始的时间点。
        timeConditions.add(Cq.gte("period", tps))
      } else if (!tpe.isNullOrBlank()) {
        // 仅指定了结束的时间点。
        timeConditions.add(Cq.lte("period", tpe))
      }
    } else { // 通过时间戳查询
      if (tss != null && tse != null) {
        // 指定了开始的时间戳和结束的时间戳。
        timeConditions.add(Cq.and(Cq.gte("startedOn", tss), Cq.lte("finishedOn", tse)))
      } else if (tss != null) {
        // 仅指定了开始的时间戳。
        timeConditions.add(Cq.gte("startedOn", tss))
      } else if (tse != null) {
        // 仅指定了结束的时间戳。
        timeConditions.add(Cq.lte("finishedOn", tse))
      }
    }

    val conditions = commonConditions + timeConditions

    return EntityRwService.findMany(
      "StatsTimelineValueReport",
      Cq.and(conditions),
      FindOptions(sort = listOf("+startedOn")),
    )
  }
}