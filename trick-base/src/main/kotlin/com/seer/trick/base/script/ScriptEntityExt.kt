package com.seer.trick.base.script

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.helper.JsonHelper
import java.util.concurrent.ConcurrentHashMap

object ScriptEntityExt {
  
  // entityName -> func
  private val beforeCreatingExtMap: MutableMap<String, String> = ConcurrentHashMap()
  
  private val afterCreatingExtMap: MutableMap<String, String> = ConcurrentHashMap()
  
  private val beforeUpdatingExtMap: MutableMap<String, String> = ConcurrentHashMap()
  private val afterUpdatingExtMap: MutableMap<String, String> = ConcurrentHashMap()
  
  private val beforeRemovingExtMap: MutableMap<String, String> = ConcurrentHashMap()
  private val afterRemovingExtMap: MutableMap<String, String> = ConcurrentHashMap()
  
  /**
   * 实体新建前
   */
  fun extBeforeCreating(entityName: String, func: String) {
    beforeCreatingExtMap[entityName] = func
  }
  
  /**
   * 实体新建后
   */
  fun extAfterCreating(entityName: String, func: String) {
    afterCreatingExtMap[entityName] = func
  }
  
  /**
   * 实体更新前
   */
  fun extBeforeUpdating(entityName: String, func: String) {
    beforeUpdatingExtMap[entityName] = func
  }
  
  /**
   * 实体更新后
   */
  fun extAfterUpdating(entityName: String, func: String) {
    afterUpdatingExtMap[entityName] = func
  }
  
  /**
   * 实体删除前
   */
  fun extBeforeRemoving(entityName: String, func: String) {
    beforeRemovingExtMap[entityName] = func
  }
  
  /**
   * 实体删除后
   */
  fun extAfterRemoving(entityName: String, func: String) {
    afterRemovingExtMap[entityName] = func
  }
  
  //
  // 以下，不是给脚本的，给 EntityServiceExtensions
  //
  
  fun clear() {
    beforeCreatingExtMap.clear()
    
    afterCreatingExtMap.clear()
    
    beforeUpdatingExtMap.clear()
    afterUpdatingExtMap.clear()
    
    beforeRemovingExtMap.clear()
    afterRemovingExtMap.clear()
  }
  
  fun beforeCreating(em: EntityMeta, evList: List<EntityValue>): List<String>? {
    val funcName = beforeCreatingExtMap[em.name] ?: return null
    if (funcName.isBlank()) return null
    
    // evList 可以被脚本修改
    val sr = ScriptCenter.execute(ScriptExeRequest(funcName, arrayOf(ScriptTraceContext(), em, evList)))
    if (!sr.isNullOrBlank()) {
      val r: ScriptEntityBeforeCreatingResult = JsonHelper.mapper.readValue(sr, jacksonTypeRef())
      if (r.error) throw BzError("errScriptExt", r.message ?: r.errorMsg)
      if (r.ids != null) return r.ids
    } else {
      return null
    }
    
    return null
  }
  
  fun afterCreating(em: EntityMeta, evList: List<EntityValue>) {
    val funcName = afterCreatingExtMap[em.name] ?: return
    
    if (funcName.isBlank()) return
    
    val sr = ScriptCenter.execute(ScriptExeRequest(funcName, arrayOf(ScriptTraceContext(), em, evList)))
    if (!sr.isNullOrBlank()) {
      val r: ScriptEntityAfterCreatingResult = JsonHelper.mapper.readValue(sr, jacksonTypeRef())
      if (r.error) throw BzError("errScriptExt", r.message ?: r.errorMsg)
    }
  }
  
  fun beforeUpdating(em: EntityMeta, ids: List<String>, update: EntityValue): Long? {
    val func = beforeUpdatingExtMap[em.name] ?: return null
    
    // update 可以被脚本修改
    val sr = ScriptCenter.execute(ScriptExeRequest(func, arrayOf(ScriptTraceContext(), em, ids, update)))
    if (!sr.isNullOrBlank()) {
      val r: ScriptEntityBeforeUpdatingResult = JsonHelper.mapper.readValue(sr, jacksonTypeRef())
      if (r.error) throw BzError("errScriptExt", r.message ?: r.errorMsg)
      if (r.changedCount != null) return r.changedCount
    }
    
    return null
  }
  
  fun afterUpdating(
    em: EntityMeta,
    ids: List<String>,
    oldValues: List<EntityValue>,
    newValues: List<EntityValue>,
  ) {
    val func = afterUpdatingExtMap[em.name] ?: return
    
    val sr = ScriptCenter.execute(ScriptExeRequest(func, arrayOf(ScriptTraceContext(), em, ids, oldValues, newValues)))
    if (!sr.isNullOrBlank()) {
      val r: ScriptEntityAfterUpdatingResult = JsonHelper.mapper.readValue(sr, jacksonTypeRef())
      if (r.error) throw BzError("errScriptExt", r.message ?: r.errorMsg)
    }
  }
  
  fun beforeRemoving(em: EntityMeta, ids: List<String>): Long? {
    val func = beforeRemovingExtMap[em.name] ?: return null
    
    val sr = ScriptCenter.execute(ScriptExeRequest(func, arrayOf(ScriptTraceContext(), em, ids)))
    if (!sr.isNullOrBlank()) {
      val r: ScriptEntityBeforeRemovingResult = JsonHelper.mapper.readValue(sr, jacksonTypeRef())
      if (r.error) throw BzError("errScriptExt", r.message ?: r.errorMsg)
      if (r.removedCount != null) return r.removedCount
    }
    
    return null
  }
  
  fun afterRemoving(em: EntityMeta, oldValues: List<EntityValue>) {
    val func = afterRemovingExtMap[em.name] ?: return
    
    val sr = ScriptCenter.execute(ScriptExeRequest(func, arrayOf(ScriptTraceContext(), em, oldValues)))
    if (!sr.isNullOrBlank()) {
      val r: ScriptEntityAfterRemovingResult = JsonHelper.mapper.readValue(sr, jacksonTypeRef())
      if (r.error) throw BzError("errScriptExt", r.message ?: r.errorMsg)
    }
  }
}

data class ScriptEntityBeforeCreatingResult(
  val error: Boolean,
  val message: String? = null,
  val errorMsg: String? = null,
  val ids: List<String>? = null,
)

data class ScriptEntityAfterCreatingResult(
  val error: Boolean,
  val message: String? = null,
  val errorMsg: String? = null,
)

data class ScriptEntityBeforeUpdatingResult(
  val error: Boolean,
  val message: String? = null,
  val errorMsg: String? = null,
  val changedCount: Long? = null,
)

data class ScriptEntityAfterUpdatingResult(
  val error: Boolean,
  val message: String? = null,
  val errorMsg: String? = null,
)

data class ScriptEntityBeforeRemovingResult(
  val error: Boolean,
  val message: String? = null,
  val errorMsg: String? = null,
  val removedCount: Long? = null,
)

data class ScriptEntityAfterRemovingResult(
  val error: Boolean,
  val message: String? = null,
  val errorMsg: String? = null,
)