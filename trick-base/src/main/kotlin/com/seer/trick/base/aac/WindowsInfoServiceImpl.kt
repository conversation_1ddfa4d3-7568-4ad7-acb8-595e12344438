package com.seer.trick.base.aac

import org.apache.commons.io.FileUtils
import org.apache.commons.lang3.StringUtils
import java.io.File
import java.nio.charset.Charset

object WindowsInfoServiceImpl : AbstractServerInfoService() {

  override fun getMotherBoardSerial(): String {
    val vbs = """
                Set objWMIService = GetObject("winmgmts:\\.\root\cimv2")
                Set colItems = objWMIService.ExecQuery _
                   ("Select * from Win32_BaseBoard")
                For Each objItem in colItems
                    Wscript.Echo objItem.SerialNumber
                    exit for  ' do the first cpu only!
                Next
        """.trim()

    val file = File.createTempFile("winMotherboardSerial", ".vbs")
    try {
      FileUtils.writeStringToFile(file, vbs, Charset.defaultCharset())

      val output = exec(arrayOf("cscript //NoLogo " + file.path))
      return StringUtils.deleteWhitespace(output)
    } finally {
      file.delete()
    }
  }
}