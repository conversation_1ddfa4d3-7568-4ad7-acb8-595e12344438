package com.seer.trick.base.http.handler

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.getReqBody
import com.seer.trick.base.http.operator
import com.seer.trick.base.log.SystemKeyEvent
import com.seer.trick.base.log.SystemKeyEventService
import com.seer.trick.base.script.*
import com.seer.trick.base.user.Operator
import io.javalin.http.Context

object ScriptHandler {
  
  fun registerHandlers() {
    val c = Handlers("api/script")
    c.post("reload", ::reload, auth())
    c.post("robust-script-executor/recover", ::recoverRobustScriptExecutor, auth())
    c.post("robust-script-executor/abort", ::abortRobustScriptExecutor, auth())
    c.get("list-buttons", ::listButtons, auth())
    c.post("call-button", ::callButton, auth())
    c.post("call-entity-ext-button", ::callEntityExtButton, auth())
  }
  
  private fun reload(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw Error403("Admin required")
    
    ScriptCenter.reload()
    
    SystemKeyEventService.record(SystemKeyEvent(group = "Base", title = "重启脚本"))
    
    ctx.status(200)
  }
  
  private fun recoverRobustScriptExecutor(ctx: Context) {
    
    val req: RecoverRobustScriptExecutor = ctx.getReqBody()
    
    val evList = EntityRwService.findMany("RobustScriptExecutor", Cq.include("id", req.ids))
    for (ev in evList) {
      ScriptRobustExecutor.recover(ev)
    }
    
    ctx.status(200)
  }
  
  private fun abortRobustScriptExecutor(ctx: Context) {
    
    val req: AbortRobustScriptExecutor = ctx.getReqBody()
    
    for (id in req.ids) {
      ScriptRobustExecutor.abort(id)
    }
    
    ctx.status(200)
  }
  
  private fun listButtons(ctx: Context) {
    ctx.json(ScriptButtonService.buttons)
  }
  
  private fun callButton(ctx: Context) {
    val req: CallButtonReq = ctx.getReqBody()
    val r = ScriptButtonService.call(req.index, req.input)
    ctx.json(r)
  }
  
  private fun callEntityExtButton(ctx: Context) {
    val op = ctx.operator()
    
    val req: CallEntityExtButtonReq = ctx.getReqBody()
    req.tc = ScriptTraceContext.from(op)
    req.op = op
    
    val r: CallEntityExtButtonResult? = ScriptCenter.execute(ScriptExeRequest(req.func, arrayOf(req)), jacksonTypeRef())
    ctx.json(r ?: CallEntityExtButtonResult(false))
  }
}

data class RecoverRobustScriptExecutor(val ids: List<String>)

data class AbortRobustScriptExecutor(val ids: List<String>)

data class CallButtonReq(val index: Int, val input: EntityValue = mutableMapOf())

data class CallEntityExtButtonReq(
  @JvmField
  var tc: ScriptTraceContext? = null,
  @JvmField
  var op: Operator? = null,
  @JvmField
  val func: String,
  @JvmField
  val entityName: String,
  @JvmField
  val selectedIds: List<String>? = null, // 列表界面
  @JvmField
  val evId: String? = null, // 详情界面
  @JvmField
  val ev: EntityValue? = null, // 编辑界面
)

data class CallEntityExtButtonResult(
  val error: Boolean = false,
  val message: String? = null,
  val modifiedEv: EntityValue? = null,
)