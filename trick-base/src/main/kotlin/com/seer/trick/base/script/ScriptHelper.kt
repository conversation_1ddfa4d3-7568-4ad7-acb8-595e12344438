package com.seer.trick.base.script

object ScriptHelper {

  /**
   * 将脚本对象转为 Java 类型
   */
  fun convertToJavaType(value: Any?): Any? {
    val res: Any?
    when (value) {
      is List<*> -> {
        res = ArrayList<Any?>()
        for (element in value) {
          res.add(convertToJavaType(element))
        }
      }
      is Map<*, *> -> {
        res = LinkedHashMap<Any, Any?>()
        for ((k, v) in value) {
          res[k!!] = convertToJavaType(v)
        }
      }
      else -> res = value
    }
    return res
  }
}