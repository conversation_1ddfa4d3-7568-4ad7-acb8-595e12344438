package com.seer.trick.base.stats

import com.seer.trick.Cq
import com.seer.trick.base.concurrent.BaseConcurrentCenter.highTimeSensitiveExecutor
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.entity.service.StatisticDateType
import com.seer.trick.base.script.ScriptCenter
import com.seer.trick.base.script.ScriptExeRequest
import com.seer.trick.base.script.ScriptTraceContext
import com.seer.trick.helper.DateHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.NumHelper
import org.apache.commons.lang3.time.DateUtils
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

object StatsService {
  
  private val logger = LoggerFactory.getLogger(javaClass)
  
  private const val EXT_PREFIX = "Ext::"
  
  val chartGroups: MutableList<ChartGroup> = CopyOnWriteArrayList()
  
  val internalCalcMap: MutableMap<String, ChartItemCalc> = ConcurrentHashMap()
  
  fun init() {
    ApiCallStatsService.init()
  }
  
  @Synchronized
  fun addGroup(g: ChartGroup) {
    if (chartGroups.any { it.id == g.id }) return
    chartGroups += g
  }
  
  @Synchronized
  fun addItem(gId: String, item: ChartItem) {
    val g = chartGroups.find { it.id == gId } ?: return
    if (g.items.any { it.id == item.id }) return
    g.items += item
  }
  
  fun calc(ids: List<String>): Map<String, String> {
    val map: MutableMap<String, String> = ConcurrentHashMap()
    val futures = ids.map { id ->
      highTimeSensitiveExecutor.submit {
        try {
          if (id.startsWith(EXT_PREFIX)) {
            val os: String? = ScriptCenter.execute(ScriptExeRequest("extStatsCalc", arrayOf(ScriptTraceContext(), id)))
            if (!os.isNullOrBlank()) map[id] = os
          } else {
            val supplier = internalCalcMap[id]
            if (supplier != null) {
              map[id] = supplier()
            }
          }
        } catch (e: InterruptedException) {
          return@submit
        } catch (e: Exception) {
          logger.error("stats calc", e)
        }
      }
    }
    
    for (f in futures) f.get()
    return map
  }
  
  fun buildShowLabelOnTop(): Map<String, Any> = mapOf("label" to mapOf("show" to true, "position" to "top"))
  
  /**
   * 构建最近 days 天的 yyyy-MM-dd 日期列表
   */
  fun buildLastDaysParams(days: Int): LastDaysParams {
    val todayStart = DateUtils.truncate(Date(), Calendar.DATE)
    
    val dates = mutableListOf<String>()
    for (d in 0 until days) {
      val dt = DateHelper.formatDate(DateUtils.addDays(todayStart, -d), "yyyy-MM-dd")
      dates += dt
    }
    
    val startInstant = DateUtils.addDays(todayStart, -(days - 1))
    val endInstant = Date(DateUtils.addDays(todayStart, 1).time - 1)
    
    return LastDaysParams(startInstant, endInstant, dates)
  }
  
  fun buildPeriodParams(start: Date, end: Date, type: StatisticDateType): LastDaysParams = when (type) {
    StatisticDateType.Hour -> buildPeriodHoursParams(start, end)
    StatisticDateType.Day -> buildPeriodDaysParams(start, end)
    StatisticDateType.Week -> buildPeriodWeeksParams(start, end)
    StatisticDateType.Month -> buildPeriodMonthsParams(start, end)
    StatisticDateType.Quarter -> buildPeriodQuartersParams(start, end)
    StatisticDateType.Year -> buildPeriodYearsParams(start, end)
  }
  
  /**
   * 构建从 start 到 end 的 yyyy-MM-dd HH'H' 小时列表
   */
  fun buildPeriodHoursParams(start: Date, end: Date): LastDaysParams {
    val dates = mutableListOf<String>()
    val startInstant = DateUtils.truncate(start, Calendar.HOUR)
    val endInstant = Date(DateUtils.addHours(end, 1).time - 1) // end 后一小时的时间
    var dt = startInstant
    while (dt <= endInstant) {
      dates += DateHelper.formatDate(dt, "yyyy-MM-dd HH'H'")
      dt = DateUtils.addHours(dt, 1)
    }
    return LastDaysParams(startInstant, endInstant, dates)
  }
  
  /**
   * 构建从 start 到 end 的 yyyy-MM-dd 日期列表
   */
  fun buildPeriodDaysParams(start: Date, end: Date): LastDaysParams {
    val dates = mutableListOf<String>()
    val startInstant = DateUtils.truncate(start, Calendar.DATE)
    val endInstant = Date(DateUtils.addDays(end, 1).time - 1) // end 这天最后一毫秒
    var dt = startInstant
    while (dt <= endInstant) {
      dates += DateHelper.formatDate(dt, "yyyy-MM-dd")
      dt = DateUtils.addDays(dt, 1)
    }
    return LastDaysParams(startInstant, endInstant, dates)
  }
  
  /**
   * 构建从 start 到 end 的 yyyy-ww 周列表
   * TODO 2025-01-01 算 2024 W53 还是算 2025 W1
   */
  fun buildPeriodWeeksParams(start: Date, end: Date): LastDaysParams {
    val dates = mutableListOf<String>()
    val endInstant = Date(DateUtils.addWeeks(end, 1).time - 1) // end 这天最后一毫秒
    var dt = start
    while (dt <= endInstant) {
      dates += DateHelper.formatWeek(dt)
      dt = DateUtils.addWeeks(dt, 1)
    }
    return LastDaysParams(start, endInstant, dates)
  }
  
  /**
   * 构建从 start 到 end 的 yyyy-MM 月份列表
   */
  fun buildPeriodMonthsParams(start: Date, end: Date): LastDaysParams {
    val dates = mutableListOf<String>()
    val startInstant = DateUtils.truncate(start, Calendar.MONTH)
    val endInstant = Date(DateUtils.addMonths(end, 1).time - 1) // end 这天最后一毫秒
    var dt = startInstant
    while (dt <= endInstant) {
      dates += DateHelper.formatDate(dt, "yyyy-MM")
      dt = DateUtils.addMonths(dt, 1)
    }
    return LastDaysParams(startInstant, endInstant, dates)
  }
  
  /**
   * 构建从 start 到 end 的 yyyy Q1 季度列表
   */
  fun buildPeriodQuartersParams(start: Date, end: Date): LastDaysParams {
    val dates = mutableListOf<String>()
    val startInstant = DateUtils.truncate(start, Calendar.MONTH)
    val endInstant = Date(DateUtils.addMonths(end, 3).time - 1) // end 这季度最后一毫秒
    var dt = startInstant
    while (dt <= endInstant) {
      dates += DateHelper.formatDate(dt, "yyyy 'Q${DateHelper.getQuarterOfYear(dt)}'")
      dt = DateUtils.addMonths(dt, 3)
    }
    return LastDaysParams(startInstant, endInstant, dates)
  }
  
  /**
   * 构建从 start 到 end 的 yyyy 年份列表
   */
  fun buildPeriodYearsParams(start: Date, end: Date): LastDaysParams {
    val dates = mutableListOf<String>()
    val startInstant = DateUtils.truncate(start, Calendar.YEAR)
    val endInstant = Date(DateUtils.addYears(end, 1).time - 1) // end 这天最后一毫秒
    var dt = startInstant
    while (dt <= endInstant) {
      dates += DateHelper.formatDate(dt, "yyyy")
      dt = DateUtils.addYears(dt, 1)
    }
    return LastDaysParams(startInstant, endInstant, dates)
  }
  
  data class CalcToday(
    val entityName: String,
    val projection: List<String>,
    val update: (ev: EntityValue, sv: EntityValue) -> Unit,
    val title: String,
    val dimensions: List<String>,
    val chartType: String,
  )
  
  fun calcToday(req: CalcToday): String {
    val todayStart = DateUtils.truncate(Date(), Calendar.DATE)
    
    val data = mutableMapOf<String, EntityValue>()
    for (d in 0 until 24) {
      val dt = DateHelper.formatDate(DateUtils.addHours(todayStart, d), "yyyy-MM-dd HH")
      val cv: EntityValue = mutableMapOf("datetime" to dt)
      for (dim in req.dimensions) cv[dim] = 0
      data[dt] = cv
    }
    
    val endInstant = Date(DateUtils.addDays(todayStart, 1).time - 1)
    
    val evList = EntityRwService.findMany(
      
      req.entityName,
      Cq.and(listOf(Cq.gte("createdOn", todayStart), Cq.lt("createdOn", endInstant))),
      FindOptions(projection = listOf("createdOn") + req.projection),
    )
    for (ev in evList) {
      val dt = DateHelper.formatDate(ev["createdOn"] as Date, "yyyy-MM-dd HH")
      val sv = data[dt] ?: continue
      
      req.update(ev, sv)
    }
    
    val option = mapOf(
      "title" to mapOf("text" to req.title, "x" to "left", "y" to "top"),
      "legend" to mapOf("x" to "right", "y" to "top"),
      "dataset" to mapOf(
        "dimensions" to listOf("datetime") + req.dimensions,
        "source" to data.values.sortedBy { it["datetime"] as String },
      ),
      "xAxis" to mapOf("type" to "category"),
      "yAxis" to mapOf("type" to "value"),
      "series" to req.dimensions.map {
        mapOf("type" to req.chartType) + buildShowLabelOnTop()
      },
    )
    return JsonHelper.mapper.writeValueAsString(option)
  }
  
  data class CalcLastNDays(
    val days: Int,
    val entityName: String,
    val projection: List<String>,
    val update: (ev: EntityValue, sv: EntityValue) -> Unit,
    val title: String,
    val dimensions: List<String>,
    val chartType: String,
  )
  
  fun calcLastNDays(req: CalcLastNDays): String {
    val todayStart = DateUtils.truncate(Date(), Calendar.DATE)
    
    val data = mutableMapOf<String, EntityValue>()
    for (d in 0 until req.days) {
      val dt = DateHelper.formatDate(DateUtils.addDays(todayStart, -d), "yyyy-MM-dd")
      val cv: EntityValue = mutableMapOf("datetime" to dt)
      for (dim in req.dimensions) cv[dim] = 0
      data[dt] = cv
    }
    
    val startInstant = DateUtils.addDays(todayStart, -(req.days - 1))
    val endInstant = Date(DateUtils.addDays(todayStart, 1).time - 1)
    
    val evList = EntityRwService.findMany(
      
      req.entityName,
      Cq.and(listOf(Cq.gte("createdOn", startInstant), Cq.lt("createdOn", endInstant))),
      FindOptions(projection = listOf("createdOn") + req.projection),
    )
    for (ev in evList) {
      val dt = DateHelper.formatDate(ev["createdOn"] as Date, "yyyy-MM-dd")
      val sv = data[dt] ?: continue
      
      req.update(ev, sv)
    }
    
    val option = mapOf(
      "title" to mapOf("text" to req.title, "x" to "left", "y" to "top"),
      "legend" to mapOf("x" to "right", "y" to "top"),
      "dataset" to mapOf(
        "dimensions" to listOf("datetime") + req.dimensions,
        "source" to data.values.sortedBy { it["datetime"] as String },
      ),
      "xAxis" to mapOf("type" to "category"),
      "yAxis" to mapOf("type" to "value"),
      "series" to req.dimensions.map {
        mapOf("type" to req.chartType) + buildShowLabelOnTop()
      },
    )
    return JsonHelper.mapper.writeValueAsString(option)
  }
  
  fun addInt(ev: EntityValue, field: String, v: Int) {
    ev[field] = v + (NumHelper.anyToInt(ev[field]) ?: 0)
  }
  
  fun addDouble(ev: EntityValue, field: String, v: Double) {
    ev[field] = v + (NumHelper.anyToDouble(ev[field]) ?: 0.0)
  }
}

data class ChartGroup(
  val id: String,
  val label: String,
  val displayOrder: Int = -1,
  val items: MutableList<ChartItem> = CopyOnWriteArrayList(),
)

data class ChartItem(val id: String = "", val label: String = "", val desc: String = "", val displayOrder: Int = -1)

typealias ChartItemCalc = () -> String

data class LastDaysParams(
  @JvmField
  val startInstant: Date,
  @JvmField
  val endInstant: Date,
  @JvmField
  val dates: List<String>,
)