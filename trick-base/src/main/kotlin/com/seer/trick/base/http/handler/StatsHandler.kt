package com.seer.trick.base.http.handler

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.I18N
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.entity.service.StatisticDateType
import com.seer.trick.base.file.FileManager
import com.seer.trick.base.http.*
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.HttpServerManager.noAuth
import com.seer.trick.base.stats.StatsService
import com.seer.trick.base.stats.manila.ManilaFilterCaseService
import com.seer.trick.base.stats.manila.ManilaReportService
import com.seer.trick.base.stats.manila.domain.ManilaFilterCase
import com.seer.trick.base.user.PermissionManager
import com.seer.trick.helper.DateHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.RowBuilder
import com.seer.trick.helper.XlsHelper
import io.javalin.http.Context
import io.javalin.websocket.WsMessageContext
import org.apache.commons.lang3.time.DateUtils
import org.apache.poi.xssf.streaming.SXSSFWorkbook
import java.util.*

object StatsHandler : WebSocketSubscriber() {
  
  fun registerHandlers() {
    WebSocketManager.subscribers += this
    
    val c = Handlers("api")
    c.get("stats/chart/groups", ::listChartGroups, auth())
    // 对外接口，查 【时间序列数值报表】
    c.post("stats/timeline", ::handleManilaStatsReports, noAuth())
    
    // 科目
    c.get("stats/subjects", ::manilaStatsSubjects, noAuth())
    // 按科目取对象
    c.post("stats/targets", ::manilaStatsTargets, auth())
    
    // 统计报表
    c.post("manila/list", ::manilaStatsList, auth())
    // 导出报表
    c.post("manila/export", ::manilaStatsExport, auth())
    // 统计图表
    c.post("manila/charts", ::manilaStatsChart, auth())
    
    // 统计方案
    // 保存
    c.post("manila/filter-case/save", ::saveManilaFilterCase, auth())
    // 删除
    c.post("manila/filter-case/remove", ::removeManilaFilterCase, auth())
    // 查询
    c.get("manila/filter-case", ::listManilaFilterCases, auth())
  }
  
  override fun onMessage(ctx: WsMessageContext, msg: WsMsg, env: WsEnv) {
    when (msg.action) {
      "Robot::StatsTimelineValueReport::Query" -> wsManilaStatsReports(ctx, msg)
      "StatsDashboard" -> {
        val ids: List<String> = msg.contentAsType() ?: return
        val r = StatsService.calc(ids)
        ctx.send(WsMsg("StatsDashboard::Reply", JsonHelper.writeValueAsString(r)))
      }
    }
  }
  
  private fun listChartGroups(ctx: Context) {
    ctx.json(StatsService.chartGroups)
  }
  
  // 处理机器人统计结果请求
  private fun handleManilaStatsReports(context: Context) {
    val req: ManilaStatRequest = context.getReqBody()
    
    // 通过服务查询并返回查询结果
    val result = ManilaReportService.fetchManilaReport(req)
    context.json(result)
  }
  
  // 统计科目
  private fun manilaStatsSubjects(ctx: Context) {
    val subjects =
      ManilaReportService.getConfigs().map {
        it.reportSubject
      }.distinct().associateWith { I18N.lo("StatsLabel_$it", defaultValue = it) }
    ctx.json(subjects)
  }
  
  // 统计对象
  private fun manilaStatsTargets(ctx: Context) {
    val req: ManilaStatsTargetReq = ctx.getReqBody()
    val filter = if (req.subject.isEmpty()) {
      Cq.all()
    } else {
      Cq.include("subject", req.subject)
    }
    val o = FindOptions(listOf("subject", "target"))
    // TODO 优化查询效率
    val evList = EntityRwService.findMany("StatsTimelineValueReport", filter, o)
    // 根据 ev.subject 汇总，key 为 subject，value 为 target list
    val map = evList.groupBy { it["subject"] as String? ?: "" }
      .mapValues { it.value.map { a -> a["target"] as String? ?: "" }.distinct() }
    ctx.json(map)
  }
  
  // 统计报表
  // [
  //     {
  //         "FalconTaskError": "1",
  //         "target": "all",
  //         "period": "2024-05-27",
  //         "FalconTaskCreate": "1"
  //     },
  //     {
  //         "RobotErrorDuration": null,
  //         "target": "AMB-02",
  //         "period": "2024-11-07",
  //         "RobotIdleDuration": null,
  //         "RobotMileage": "778.53",
  //         "RobotOnlineDuration": null
  //     }
  // ]
  private fun manilaStatsList(ctx: Context) {
    var filterCase: ManilaFilterCase = ctx.getReqBody()
    
    // TODO 临时处理，如果不传 target 赋默认值 all
    if (filterCase.targets.isEmpty()) {
      filterCase = filterCase.copy(targets = listOf("all"))
    }
    validateStatisticsQuery(filterCase)
    ctx.json(statsList(filterCase))
  }
  
  private fun statsList(filterCase: ManilaFilterCase): List<EntityValue> {
    if (filterCase.subjects.isEmpty()) throw BzError("errEmptyStatsSubject")
    
    val req = ManilaStatRequest.fromFilterCase(filterCase)
    val result = ManilaReportService.fetchManilaReport(req)
    // 补充缺失日期的数据
    val xAxis = StatsService.buildPeriodParams(
      req.getStartedOn() ?: Date(),
      req.getFinishedOn() ?: Date(),
      req.periodType,
    )
    
    val resultList = if (filterCase.subjects.size >= filterCase.targets.size) {
      mergeByTarget(result, filterCase.subjects, filterCase.targets.first(), xAxis.dates)
    } else {
      mergeBySubject(result, filterCase.subjects.first(), filterCase.targets, xAxis.dates)
    }
    return resultList
    
    // // 先根据 target 分组，然后对每组按 period 分组，然后将组内所有的 subject 合到一个对象中
    // // target||period -> { subject name -> value, target, period }
    // val r = mutableMapOf<String, EntityValue>()
    // result.forEach {
    //   val target = it["target"] as String? ?: ""
    //   val period = it["period"] as String? ?: ""
    //   val subject = it["subject"] as String? ?: ""
    //   val value = it["value"] ?: 0
    //
    //   // subject name -> value, target, period
    //   val key = "$target||$period"
    //   val m0 = r.getOrDefault(key, filterCase.subjects.associateWith { 0 }.toMutableMap()) // 默认值是 subject 为 key 的空 map
    //   m0[subject] = value
    //   m0["target"] = target
    //   m0["period"] = period
    //   r[key] = m0
    // }
    //
    // for (target in req.targets) {
    //   for (period in xAxis.dates) {
    //     val key = "$target||$period"
    //     if (!r.containsKey(key)) {
    //       val m0 = r.getOrDefault(key, filterCase.subjects.associateWith { 0 }.toMutableMap())
    //       m0["target"] = target
    //       m0["period"] = period
    //       r[key] = m0
    //     }
    //   }
    // }
    //
    // return r.values.sortedWith(
    //   compareBy<EntityValue> { it["period"] as String? }.thenBy { it["target"] as String? },
    // ).toMutableList()
  }
  
  /**
   * 多 subject，单 target
   *
   * 返回结构
   * [
   * {"target": "all", ""period": "2024-11-07", "subject1": 1, "subject2": 2, "subject3": 3},
   * {},
   * ]
   */
  private fun mergeByTarget(
    evList: List<EntityValue>,
    subjectList: List<String>,
    target: String,
    dateList: List<String>,
  ): List<EntityValue> {
    // {period -> {subject -> value}}
    val map = mutableMapOf<String, EntityValue>()
    for (ev in evList) {
      val period = ev["period"] as String? ?: ""
      val subject = ev["subject"] as String? ?: ""
      val value = ev["value"] ?: 0
      val m = map.getOrDefault(period, mutableMapOf("period" to period))
      m[subject] = value
      map[period] = m
    }
    
    // 补充缺失的日期与数据
    for (period in dateList) {
      val m = map.getOrDefault(period, mutableMapOf("period" to period))
      for (subject in subjectList) {
        if (!m.containsKey(subject)) {
          m[subject] = 0
        }
      }
      map[period] = m
    }
    // 排序
    return map.values.sortedBy { it["period"] as String? }.toList()
  }
  
  /**
   * 单 subject，多 target
   *
   * 返回结构
   * [
   * {"subject": "RobotChargeDuration", ""period": "2024-11-07", "target1": 1, "target2": 2, "target3": 3},
   * {},
   * ]
   *
   * TODO 国际化
   */
  private fun mergeBySubject(
    evList: List<EntityValue>,
    subject: String,
    targetList: List<String>,
    dateList: List<String>,
  ): List<EntityValue> {
    // {period -> {target -> value}}
    val map = mutableMapOf<String, EntityValue>()
    for (ev in evList) {
      val period = ev["period"] as String? ?: ""
      val target = ev["target"] as String? ?: ""
      val value = ev["value"] ?: 0
      val m = map.getOrDefault(period, mutableMapOf("period" to period))
      m[target] = value
      map[period] = m
    }
    
    // 补充缺失的日期与数据
    for (period in dateList) {
      val m = map.getOrDefault(period, mutableMapOf("period" to period))
      for (target in targetList) {
        if (!m.containsKey(target)) {
          m[target] = 0
        }
      }
      map[period] = m
    }
    // 排序
    return map.values.sortedBy { it["period"] as String? }.toList()
  }
  
  private fun manilaStatsExport(ctx: Context) {
    val op = ctx.operator()
    val em = BaseCenter.mustGetEntityMeta("StatsTimelineValueReport")
    
    if (!PermissionManager.pExport(em, op)) throw Error403("没有导出权：${em.label}")
    var req: ManilaFilterCase = ctx.getReqBody()
    
    // TODO 临时处理，如果不传 target 赋默认值 all
    if (req.targets.isEmpty()) {
      req = req.copy(targets = listOf("all"))
    }
    
    // 表头 targets
    // 把 req.subjects 展平放在 header 里
    // 根据 subjects、targets 选的数量，填不同的东西
    val header = mutableListOf("period")
    if (req.subjects.size >= req.targets.size) {
      header.addAll(req.subjects)
    } else {
      header.addAll(req.targets)
    }
    // 表体
    val lines = statsList(req)
    
    val sheetName =
      if (req.subjects.size >= req.targets.size) {
        req.targets.firstOrNull()
      } else {
        I18N.lo("StatsLabel_${req.subjects.firstOrNull()}", defaultValue = req.subjects.firstOrNull())
      }
    val workbook = SXSSFWorkbook(1000)
    val sheet = workbook.createSheet(sheetName)
    val head = sheet.createRow(0)
    
    // 表头
    for ((i, h) in header.withIndex()) {
      // 国际化
      head.createCell(i).setCellValue(I18N.lo("StatsLabel_$h", defaultValue = h))
    }
    // 表体
    for ((rowIndex, ev) in lines.withIndex()) {
      val row = sheet.createRow(rowIndex + 1)
      val rb = RowBuilder(row)
      for (h in header) {
        rb.setAny(ev[h])
      }
    }
    
    val file = FileManager.nextTmpFile(ext = "xlsx", prefix = "export")
    // workbook.setSheetName(0, sheetName)
    XlsHelper.workbookToFile(workbook, file)
    
    ctx.json(ExportResult(FileManager.fileToPath(file)))
  }
  
  // 统计图表
  // {
  //     "xAxis": ["a","b","c", "d"],
  //     "data": {
  //         "AMB-01": {
  //             "x": [1,3,3,5],
  //             "y": [2,3,4,5]
  //         },
  //         "AMB-02": {
  //             "x": [1,3,3,5],
  //             "y": [2,3,4,5]
  //         }
  //     }
  // }
  private fun manilaStatsChart(ctx: Context) {
    val filterCase: ManilaFilterCase = ctx.getReqBody()
    
    var req = ManilaStatRequest.fromFilterCase(filterCase)
    // TODO 临时处理，如果不传 target 赋默认值 all
    if (req.targets.isEmpty()) {
      req = req.copy(targets = listOf("all"))
    }
    
    val list = ManilaReportService.fetchManilaReport(req)
    
    // target -> { subject -> { period -> value } }
    val map = mutableMapOf<String, MutableMap<String, MutableMap<String, Any?>>>()
    // 直接向 map 里塞 target 的默认值，防止 list 没查出来的场景下，map 为空
    for (target in req.targets) {
      map[target] = filterCase.subjects.associateWith { mutableMapOf<String, Any?>() }.toMutableMap()
    }
    for (ev in list) {
      val target = ev["target"] as String? ?: ""
      val subject = ev["subject"] as String? ?: ""
      val period = ev["period"] as String? ?: ""
      val value = ev["value"]
      // subject -> { period -> value }
      val m0 = map.getOrDefault(
        target,
        filterCase.subjects.associateWith { mutableMapOf<String, Any?>() }.toMutableMap(), // 默认值是 subject 为 key 的空 map
      )
      // period -> value
      val m1 = m0.getOrDefault(subject, mutableMapOf())
      m1[period] = value
      m0[subject] = m1
      map[target] = m0
    }
    
    // fill period days
    // target -> { subject -> value list }
    val r = mutableMapOf<String, MutableMap<String, List<Any?>>>()
    
    val xAxis = StatsService.buildPeriodParams(
      req.getStartedOn() ?: Date(),
      req.getFinishedOn() ?: Date(),
      req.periodType,
    )
    for ((target, m0) in map) {
      for ((subject, m1) in m0) {
        val ll = mutableListOf<Any?>()
        for (period in xAxis.dates) {
          val value = m1[period] ?: 0
          ll.add(value)
        }
        r[target] = r.getOrDefault(target, mutableMapOf())
        r[target]!![subject] = ll
      }
    }
    
    ctx.json(mapOf("xAxis" to xAxis.dates, "data" to r))
  }
  
  // 保存统计方案
  private fun saveManilaFilterCase(ctx: Context) {
    val req: List<ManilaFilterCase> = ctx.getReqBody()
    ManilaFilterCaseService.saveCases(req)
  }
  
  // 删除统计方案
  private fun removeManilaFilterCase(ctx: Context) {
    val req: ManilaRemoveFilterCaseReq = ctx.getReqBody()
    ManilaFilterCaseService.removeCases(req.ids)
  }
  
  // 查询统计方案
  private fun listManilaFilterCases(ctx: Context) {
    val cases = ManilaFilterCaseService.listCases()
    ctx.json(cases)
  }
  
  private fun wsManilaStatsReports(ctx: WsMessageContext, msg: WsMsg) {
    val req: ManilaStatRequest = JsonHelper.mapper.readValue(msg.content!!, jacksonTypeRef())
    val res = ManilaReportService.fetchManilaReport(req)
    
    // 向客户端回复查询数据
    ctx.send(WsMsg.json("Robot::StatsTimelineValueReport::Reply", res, replyToId = msg.id))
  }
  
  // 校验开始时间结束时间是否符合条件
  private fun validateStatisticsQuery(filterCase: ManilaFilterCase) {
    val req = ManilaStatRequest.fromFilterCase(filterCase)
    val startedOn = req.getStartedOn()
    val finishedOn = req.getFinishedOn()
    if (startedOn == null || finishedOn == null) return
    if (startedOn.time > finishedOn.time) {
      throw BzError("errStartedOnLtFinishedOn")
    }
  }
}

data class ManilaStatRequest(
  val subjects: List<String>, // 统计科目列表
  val targets: List<String>, // 统计对象列表，例如机器人名称，或模块名称
  val periodType: StatisticDateType, // 周期类型
  // TODO 改到 periodStart
  val timepointStart: String?, // 查询的起始时间点，如："2024-01-01 12H"、"2024-01-01"、"2024 W1"、"2024-01"、"2024 Q1"、"2024"
  // TODO 改到 periodEnd
  val timepointEnd: String?, // 查询的终止时间点，如："2024-01-01 12H"、"2024-01-01"、"2024 W1"、"2024-01"、"2024 Q1"、"2024"
  val timestampStart: Date?, // 查询的起始时间戳，如："2024-10-11T08:00:00.000z"
  val timestampEnd: Date?, // 查询的终止时间戳
) {
  
  fun getStartedOn(): Date? {
    if (timestampStart != null) return timestampStart
    if (timepointStart.isNullOrBlank()) return null
    return convertDate(timepointStart)
  }
  
  fun getFinishedOn(): Date? {
    if (timestampEnd != null) return timestampEnd
    if (timepointEnd.isNullOrBlank()) return null
    return convertDate(timepointEnd)
  }
  
  private fun convertDate(periodTime: String?): Date? {
    try {
      return when (periodType) {
        StatisticDateType.Hour -> {
          DateUtils.parseDate(periodTime, "yyyy-MM-dd HH'H'")
        }
        
        StatisticDateType.Day -> {
          DateUtils.parseDate(periodTime, "yyyy-MM-dd")
        }
        
        StatisticDateType.Week -> {
          DateHelper.parseWeek(periodTime)
        }
        
        StatisticDateType.Month -> {
          DateUtils.parseDate(periodTime, "yyyy-MM")
        }
        
        StatisticDateType.Quarter -> {
          DateHelper.parseQuarter(periodTime)
        }
        
        StatisticDateType.Year -> {
          DateUtils.parseDate(periodTime, "yyyy")
        }
      }
    } catch (_: Exception) {
      throw BzError("errIllegalPeriodTime", periodType, periodTime)
    }
  }
  
  companion object {
    fun fromFilterCase(filterCase: ManilaFilterCase): ManilaStatRequest = ManilaStatRequest(
      subjects = filterCase.subjects,
      targets = filterCase.targets,
      periodType = filterCase.periodType,
      timepointStart = filterCase.periodStart,
      timepointEnd = filterCase.periodEnd,
      timestampStart = filterCase.timestampStart,
      timestampEnd = filterCase.timestampEnd,
    )
  }
}

// 根据科目查所有对象
data class ManilaStatsTargetReq(val subject: List<String>)

// 删统计方案 req
data class ManilaRemoveFilterCaseReq(val ids: List<String>)