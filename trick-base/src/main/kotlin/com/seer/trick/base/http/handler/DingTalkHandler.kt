package com.seer.trick.base.http.handler

import com.seer.trick.BzError
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager
import com.seer.trick.base.user.dingtalk.DingTalkOAuthManager

import io.javalin.http.Context

object DingTalkHandler {
  fun registerHandlers() {
    val c = Handlers("api/ding")
    c.get("config-ids", ::getConfigIds, HttpServerManager.noAuth())
    c.get("auth-callback", ::callbackAuth, HttpServerManager.noAuth())
  }

  private fun getConfigIds(ctx: Context) {
    val ding = DingTalkOAuthManager.readConfig()
    if (!ding.dingEnabled) throw BzError("errDingNotEnabled")
    ctx.json(mapOf("clientId" to ding.dingClientID))
  }

  private fun callbackAuth(ctx: Context) {
    val code = ctx.queryParam("code")
    if (code.isNullOrBlank()) throw BzError("errDingShuNoCode")

    
    val fs = DingTalkOAuthManager.readConfig()
    if (!fs.dingEnabled) throw BzError("errDingNotEnabled")

    val userSession = DingTalkOAuthManager.signIn(code)

    HttpServerManager.setUserSessionCookies(ctx, userSession.userId, userSession.userToken)

    ctx.redirect("/")
  }
}