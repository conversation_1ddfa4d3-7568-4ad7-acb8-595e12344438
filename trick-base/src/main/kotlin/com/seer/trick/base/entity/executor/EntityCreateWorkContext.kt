package com.seer.trick.base.entity.executor

import com.seer.trick.base.entity.EntityValue

class EntityCreateWorkContext() {
  
  val tables: MutableMap<String, MutableList<EntityValue>> = HashMap() // key is table name
  
  fun addRow(tableName: String, row: EntityValue) {
    if (row.isEmpty()) return
    val table = tables.computeIfAbsent(tableName) { _ -> ArrayList() }
    table.add(row)
  }
  
}
