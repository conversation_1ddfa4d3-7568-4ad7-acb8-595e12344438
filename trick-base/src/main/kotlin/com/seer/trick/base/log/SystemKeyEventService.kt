package com.seer.trick.base.log

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.CreateOptions
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.user.Operator
import org.slf4j.LoggerFactory

/**
 * 记录系统关键事件
 */
object SystemKeyEventService {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 不抛异常
   */
  fun record(event: SystemKeyEvent) {
    logger.info("系统关键事件：[${event.level}][${event.group}][${event.title}] ${event.content}")
    try {
      val ev: EntityValue = mutableMapOf(
        "level" to event.level.name,
        "group" to event.group,
        "title" to event.title,
        "content" to event.content,
        "relatedUser" to Operator.current()?.userId,
      )
      EntityRwService.createOne("SystemKeyEvent", ev, CreateOptions(muteExt = true))
    } catch (e: Exception) {
      logger.error("记录系统关键事件失败", e)
    }
  }

}

data class SystemKeyEvent(
  val level: SystemKeyEventLevel = SystemKeyEventLevel.Info,
  val group: String,
  val title: String,
  val content: String? = null,
)

enum class SystemKeyEventLevel {
  Info, Warning, Error
}