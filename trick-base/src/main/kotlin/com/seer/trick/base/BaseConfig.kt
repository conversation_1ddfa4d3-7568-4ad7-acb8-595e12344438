package com.seer.trick.base

import com.fasterxml.jackson.annotation.JsonIgnore
import java.io.File

data class BaseConfig(
  @JsonIgnore
  val projectDir: File = File(System.getProperty("user.dir")),
  val port: Int = 0,
  val db: DbConfig = DbConfig(),
  val noServerFileService: Boolean = false,
  @Deprecated("4.23.2 之后的版本弃用此项配置，不能用于处理业务逻辑；换用 bz.json 中的 ScSingle.singleConfig.enableScSingle 。")
  val single: Boolean = false, // 单车应用
  val insideController: Boolean = false, // 装在控制器里
  val anonymouseUser: Boolean = false, // 不需要登录
  val noScript: Boolean = false, // 不启用脚本
  val httpsConfig: HttpsConfig = HttpsConfig(),
  val disabledModules: Set<String> = HashSet(),
) {
  @JsonIgnore
  val configDir = File(projectDir, "config")

  @JsonIgnore
  val uiDir = File("ui")

  @JsonIgnore
  val uiExtDir = File(projectDir, "ui-ext")

  @JsonIgnore
  val filesDir = File(projectDir, "files")

  @JsonIgnore
  val logsDir = File(projectDir, "logs")
}

data class BootConfig(
  val port: Int = 5800,
  val db: DbConfig = DbConfig(),
  val noServerFileService: Boolean = false,
  val single: Boolean = false, // 单车应用
  val anonymouseUser: Boolean = false, // 不需要登录
  val insideController: Boolean = false, // 装在控制器里
  val noScript: Boolean = false, // 不启用脚本
  val httpsConfig: HttpsConfig = HttpsConfig(),
  val disabledModules: Set<String> = HashSet(),
)

data class DbConfig(
  val type: DbType = DbType.Derby,
  val url: String? = null,
  val host: String? = null,
  val port: Int? = null,
  val dbName: String? = null,
  val username: String? = null,
  val password: String? = null,
)

data class HttpsConfig(val enableHttps: Boolean = false, val httpsPort: Int = 5801, val password: String? = null)

enum class DbType {
  MySQL,
  Derby,
  SqlServer,
  MongoDB,
  Dameng,
}