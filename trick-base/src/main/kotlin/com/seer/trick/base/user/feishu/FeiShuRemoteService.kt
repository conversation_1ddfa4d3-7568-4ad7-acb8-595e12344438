package com.seer.trick.base.user.feishu

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonAutoDetect
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import retrofit2.Call
import retrofit2.http.*


interface FeiShuRemoteService {
  @FormUrlEncoded
  @POST("auth/v3/app_access_token/internal")
  fun getAppToken(
    @Field("app_id") appId:String,
    @Field("app_secret") appSecret:String
  ): Call<FeiShuAppTokenRes>
  @FormUrlEncoded
  @POST("authen/v1/oidc/access_token")
  fun getUserToken(
    @Field("code") code:String,
    @Field("grant_type") grantType:String,
    @Header("Authorization") authorization: String
  ): Call<FeiShuUserTokenRes>
  
  @GET("authen/v1/user_info")
  fun getUserInfo(
    @Header("Authorization") authorization: String
  ): Call<FeiShuUserInfoRes>
}

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
data class FeiShuAppTokenRes(
  @JsonAlias("code")
  var code: Int = 0,
  @JsonAlias("msg")
  var msg: String? = null,
  @JsonAlias("app_access_token")
  var appAccessToken: String? = null,
  @JsonAlias("expires_in")
  var expire: Int = 0
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
data class FeiShuUserInfoData(
  @JsonAlias("name")
  var name: String? = null, //用户姓名
  @JsonAlias("en_name")
  var enName: String? = null, //用户英文名称
  @JsonAlias("open_id")
  var openId: String? = null, //用户在应用内的唯一标识
  @JsonAlias("union_id")
  var unionId: String? = null, //用户对ISV的唯一标识，对于同一个ISV，用户在其名下所有应用的union_id相同
  @JsonAlias("user_id")
  var userId: String? = null, //用户 user_id , 需要授权
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
data class FeiShuUserInfoRes(
  @JsonAlias("code")
  var code: Int = 0,
  @JsonAlias("msg")
  var msg: String? = null,
  @JsonAlias("data")
  var data: FeiShuUserInfoData = FeiShuUserInfoData(),
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
data class FeiShuUserTokenData(
  @JsonAlias("access_token")
  var accessToken: String? = null,
  @JsonAlias("refresh_token")
  var refreshToken: String? = null,
  @JsonAlias("token_type")
  var tokenType: String? = null,
  @JsonAlias("expires_in")
  var expiresIn: Int = 0,
  @JsonAlias("refresh_expires_in")
  var refreshExpiresIn: Int = 0,
  @JsonAlias("scope")
  var scope: String? = null,
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
data class FeiShuUserTokenRes(
  @JsonAlias("code")
  var code: Int = 0,
  @JsonAlias("msg")
  var msg: String? = null,
  @JsonAlias("data")
  var data: FeiShuUserTokenData? = null,
)


