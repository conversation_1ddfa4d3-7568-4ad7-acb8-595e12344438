package com.seer.trick.base.entity.cache

import java.util.concurrent.ConcurrentHashMap

class EntityKeyCache<T> {
  
  // entity -> key
  private val cache: MutableMap<String, MutableMap<String, CachedValue<T>>> = ConcurrentHashMap()
  
  fun get(entityName: String, key: String): CachedValue<T>? {
    val c1 = cache[entityName] ?: return null
    return c1[key]
  }
  
  @Synchronized
  operator fun set(entityName: String, key: String, v: T?) {
    var c1 = cache[entityName]
    if (c1 == null) {
      c1 = ConcurrentHashMap()
      cache[entityName] = c1
    }
    c1[key] = CachedValue(v, System.currentTimeMillis())
  }
  
  fun clear() {
    cache.clear()
  }
  
  fun remove(entityName: String) {
    cache.remove(entityName)
  }

  fun cleanOld(before: Long) {
    for (c1 in cache.values) {
      val iter = c1.entries.iterator()
      while (iter.hasNext()) {
        val item = iter.next()
        if (item.value.createdOn < before) iter.remove()
      }
    }
  }
  
}
