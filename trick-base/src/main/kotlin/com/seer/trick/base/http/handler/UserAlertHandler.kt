package com.seer.trick.base.http.handler

import com.seer.trick.Cq
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.UserNoticeService
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.getReqBody
import com.seer.trick.base.http.operator
import com.seer.trick.base.sui.SuiManager
import com.seer.trick.base.sui.SuiResult
import io.javalin.http.Context

/**
 * 消息提醒
 */
object UserAlertHandler {
  
  fun registerHandlers() {
    val c = Handlers("api")
    c.get("user-alert/count", ::getCount, auth())
    c.post("user-notice/read", ::handleReadUserNotices, auth())
    
    c.get("sui/requests", ::listSuiRequests, auth())
    c.post("sui/res", ::setSuiRes, auth())
  }
  
  private fun getCount(ctx: Context) {
    val op = ctx.operator()
    
    val unreadUserNoticeNum =
      EntityRwService.count("UserNotice", Cq.and(listOf(Cq.eq("userId", op.userId), Cq.ne("read", true))))
    
    val workStation = ctx.queryParam("workStation")
    val requests = SuiManager.listMine(workStation, op)
    ctx.json(mapOf("unreadUserNoticeNum" to unreadUserNoticeNum, "suiNum" to requests.size))
  }
  
  private fun handleReadUserNotices(ctx: Context) {
    val op = ctx.operator()
    
    val req: ReadUserNotices = ctx.getReqBody()
    UserNoticeService.readUserNotices(req.noticeIds, op)
    
    ctx.status(200)
  }
  
  private fun listSuiRequests(ctx: Context) {
    val op = ctx.operator()
    val workStation = ctx.queryParam("workStation")
    val requests = SuiManager.listMine(workStation, op)
    ctx.json(requests)
  }
  
  private fun setSuiRes(ctx: Context) {
    val r: SuiResult = ctx.getReqBody()
    SuiManager.setRes(r.id, r)
  }
  
}

data class ReadUserNotices(
  val noticeIds: List<String>
)