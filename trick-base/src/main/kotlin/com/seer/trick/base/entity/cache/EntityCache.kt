package com.seer.trick.base.entity.cache

import com.seer.trick.ComplexQuery
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.FindOptions
import java.util.concurrent.Callable

interface EntityCache {

  fun init()

  fun dispose()

  fun getById(
    entityName: String,
    id: String,
    projection: List<String>?,
    loader: Callable<EntityValue>
  ): EntityValue?

  fun getByQuery(
    entityName: String,
    query: ComplexQuery,
    o: FindOptions?,
    loader: Callable<List<EntityValue>>
  ): List<EntityValue>

  fun getCount(
    entityName: String,
    query: ComplexQuery,
    loader: Callable<Long>
  ): Long

  fun clearAll()

  fun clearAll(entityName: String)

  /**
   * ids 传 null 全清掉
   */
  fun clearByIds(entityName: String, ids: List<String>?)

  fun clearByQuery(entityName: String)

  fun clearCount(entityName: String)

  fun inspect(): Any?

}