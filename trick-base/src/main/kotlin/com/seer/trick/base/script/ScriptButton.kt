package com.seer.trick.base.script

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.base.entity.EntityValue
import java.util.concurrent.CopyOnWriteArrayList

object ScriptButtonService {

  val buttons: MutableList<ScriptButton> = CopyOnWriteArrayList()

  fun setButtons(buttons: List<ScriptButton>) {
    this.buttons.clear()
    this.buttons.addAll(buttons)
  }

  fun call(index: Int, input: EntityValue): ScriptButtonResult {
    val b = buttons[index]
    return ScriptCenter.execute(ScriptExeRequest(b.func, arrayOf(input)), jacksonTypeRef()) ?: ScriptButtonResult(null)
  }
}

data class ScriptButton(
  val label: String,
  val func: String,
  val confirmText: String? = null,
  val callTimeout: Long? = null,
  val inputEntityName: String? = null,
  val inputMaxWidth: Int? = null,
)

data class ScriptButtonResult(val message: String? = null)