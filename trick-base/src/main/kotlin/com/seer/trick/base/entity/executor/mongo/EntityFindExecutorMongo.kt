package com.seer.trick.base.entity.executor.mongo

import com.seer.trick.Cq
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.FieldScale
import com.seer.trick.base.entity.FieldType
import com.seer.trick.base.entity.executor.EntityFindWorkContext
import com.seer.trick.base.entity.service.FindOptions

object EntityFindExecutorMongo {

  /**
   * 主表字段已加载，只需要加载关联表的
   */
  fun execute(ctx: EntityFindWorkContext) {
    processEntity(ctx)
  }
  
  private fun processEntity(ctx: EntityFindWorkContext) {
    for (fm in ctx.em.fields.values) {
      if (fm.type == FieldType.Component) {
        processComponentField(fm, ctx)
      }
    }
  }
  
  private fun processComponentField(fm: FieldMeta, ctx: EntityFindWorkContext) {
    val refEm = BaseCenter.mustGetRefEntityMeta(fm)
    loadRelatedTable(fm, ctx, { allRows: List<EntityValue> ->
      processEntity(EntityFindWorkContext(refEm, allRows))
    }, { myRows ->
      if (fm.scale == FieldScale.Single) {
        if (myRows.isEmpty()) null else myRows[0]
      } else {
        myRows
      }
    })
  }
  
  private fun loadRelatedTable(
    fm: FieldMeta,
    ctx: EntityFindWorkContext,
    processAllRows: ((List<EntityValue>) -> Unit)?,
    processMyRows: (List<EntityValue>) -> Any?
  ) {
    if (ctx.ids.isEmpty()) return
    val refEm = BaseCenter.mustGetRefEntityMeta(fm)
    val relatedRows = MongoExecutor.findMany(
      refEm,
      Cq.include(FieldMeta.COLUMN_OWNER, ctx.ids),
      FindOptions(sort = listOf(FieldMeta.COLUMN_ORDER))
    )
    if (processAllRows != null) processAllRows(relatedRows)
    for (ev in ctx.entityValues) {
      val myRows = relatedRows.filter { row -> row[FieldMeta.COLUMN_OWNER] == ev["id"] }
      // 添加到主业务对象
      ev[fm.name] = processMyRows(myRows)
    }
  }

}
