package com.seer.trick.base.entity.executor.sql

import com.seer.trick.BzError
import com.seer.trick.ComplexQuery
import com.seer.trick.base.DbType
import com.seer.trick.base.db.sql.SmartConnection
import com.seer.trick.base.db.sql.SqlHelper.buildOrderByClause
import com.seer.trick.base.db.sql.SqlHelper.buildSqlQuery
import com.seer.trick.base.db.sql.SqlHelper.checkColumnNameValid
import com.seer.trick.base.db.sql.SqlHelper.escapeIdentifier
import com.seer.trick.base.db.sql.SqlHelper.executePreparedUpdate
import com.seer.trick.base.db.sql.SqlHelper.objectToSetClause
import com.seer.trick.base.db.sql.SqlHelper.resultSetToEntityValues
import com.seer.trick.base.db.sql.SqlHelper.setToStatement
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.AdvancedField
import com.seer.trick.base.entity.service.AggregationOptions
import com.seer.trick.base.entity.service.ArithmeticOperator
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.helper.JsonHelper
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import java.sql.SQLException

/**
 * 处理单表的增删改查
 */
object SqlExecutor {

  private val logger = LoggerFactory.getLogger(SqlExecutor::class.java)

  fun insert(sc: SmartConnection, table: String, entityValues: List<EntityValue>): Int {
    if (entityValues.isEmpty()) return 0
    var insertNum = 0
    val chunkList = entityValues.chunked(2000)
    for (chunk in chunkList) {
      insertNum += doInsert(sc, table, chunk)
    }
    return insertNum
  }

  private fun doInsert(sc: SmartConnection, table: String, entityValues: List<EntityValue>): Int {
    // 列出所有列名
    val fieldNames: MutableSet<String> = HashSet()
    for (ev in entityValues) fieldNames.addAll(ev.keys)
    if (fieldNames.isEmpty()) return 0
    val columnNames: MutableList<String> = ArrayList(fieldNames.size)
    for (field in fieldNames) {
      checkColumnNameValid(field)
      columnNames.add(escapeIdentifier(field, sc.sqlDialect))
    }
    val placeholders: MutableList<String> = ArrayList()
    val params: MutableList<Any> = ArrayList()
    for (ev in entityValues) {
      val placeholder2: MutableList<String> = ArrayList()
      for (field in fieldNames) {
        val v = ev[field]
        if (v == null) {
          placeholder2.add("NULL")
        } else {
          placeholder2.add("?")
          params.add(v)
        }
      }
      placeholders.add(String.format("(%s)", StringUtils.join(placeholder2, ", ")))
    }
    val tableName = escapeIdentifier(table, sc.sqlDialect)
    val sql = String.format(
      "INSERT INTO %s(%s) VALUES %s",
      tableName,
      StringUtils.join(columnNames, ","),
      StringUtils.join(placeholders, ", "),
    )
    return executePreparedUpdate(sc, sql, params)
  }

  fun insert(sc: SmartConnection, table: String, entityValue: EntityValue): Int = insert(sc, table, listOf(entityValue))

  fun update(sc: SmartConnection, table: String, query: ComplexQuery, patch: EntityValue): Int {
    if (patch.isEmpty()) return 0
    val params: MutableList<Any> = ArrayList()
    val set = objectToSetClause(patch, params, sc.sqlDialect)
    val where = buildSqlQuery(query, params, sc.sqlDialect, table)
    if (StringUtils.isBlank(where)) throw BzError("errSqlEmptyWhereNotAllowed")
    val tableName = escapeIdentifier(table, sc.sqlDialect)
    val sql = "UPDATE $tableName SET $set WHERE $where"
    // logger.debug("update SQL: $sql")
    return executePreparedUpdate(sc, sql, params)
  }

  fun delete(
    
    sc: SmartConnection,
    table: String,
    query: ComplexQuery,
    deletedAllAllowed: Boolean = false,
  ): Int {
    val params: MutableList<Any> = ArrayList()
    // 先消除全删除的风险！！
    var where = buildSqlQuery(query, params, sc.sqlDialect, table)
    if (where == null) where = ""
    if (StringUtils.isBlank(where) && deletedAllAllowed) throw BzError("errSqlEmptyWhereNotAllowed")
    val tableName = escapeIdentifier(table, sc.sqlDialect)
    if (StringUtils.isNotBlank(where)) where = " WHERE $where"
    val sql = String.format("DELETE FROM %s%s", tableName, where)
    return executePreparedUpdate(sc, sql, params)
  }

  fun findMany(
    
    sc: SmartConnection,
    table: String,
    query: ComplexQuery,
    o: FindOptions? = null,
  ): List<EntityValue> {
    val select = o?.projection?.joinToString(", ") { escapeIdentifier(it, sc.sqlDialect) } ?: "*"
    val params: MutableList<Any> = ArrayList()
    val where = buildSqlQuery(query, params, sc.sqlDialect, table)
    val whereClause = if (StringUtils.isNotBlank(where)) "WHERE $where" else ""
    val orderBy = if (o?.sort != null && o.sort.isNotEmpty()) buildOrderByClause(o.sort, sc.sqlDialect) else null
    val orderByClause = if (StringUtils.isBlank(orderBy)) "" else " ORDER BY $orderBy"
    var skipLimit = ""
    if (o?.limit != null && o.limit > 0) {
      val skip = if (o.skip != null && o.skip > 0) o.skip else 0
      if (sc.sqlDialect == DbType.MySQL || sc.sqlDialect == DbType.Dameng) {
        skipLimit = " LIMIT " + o.limit + " OFFSET " + skip
      } else if (sc.sqlDialect == DbType.Derby) {
        skipLimit = String.format(" OFFSET %d ROWS FETCH FIRST %d ROWS ONLY", skip, o.limit)
      } else if (sc.sqlDialect == DbType.SqlServer) {
        // Sql Server 的 offset/fetch 必须有 order by
        val main = String.format(" OFFSET %d ROWS FETCH NEXT %d ROWS ONLY", skip, o.limit)
        skipLimit = if (StringUtils.isBlank(orderByClause)) " ORDER BY (SELECT NULL)$main" else main
      }
    }
    val tableName = escapeIdentifier(table, sc.sqlDialect)
    val sql = String.format("SELECT %s FROM %s %s%s%s", select, tableName, whereClause, orderByClause, skipLimit)
    val st = sc.prepareStatement(sql, false)
    return try {
      setToStatement(st, params)
      val rs = st.executeQuery()
      resultSetToEntityValues(table, rs)
    } finally {
      try {
        st.close()
      } catch (e: SQLException) {
        logger.error("Failed to close statement", e)
      }
    }
  }

  fun findOne(
    
    sc: SmartConnection,
    table: String,
    query: ComplexQuery,
    o: FindOptions? = null,
  ): EntityValue? {
    val o2 = o?.copy(limit = 1) ?: FindOptions(limit = 1)
    val evList = findMany(sc, table, query, o2)
    return if (evList.isNotEmpty()) evList[0] else null
  }

  fun count(sc: SmartConnection, tableName: String, query: ComplexQuery): Long {
    val params: MutableList<Any> = ArrayList()
    val where = buildSqlQuery(query, params, sc.sqlDialect, tableName)
    val whereClause = if (StringUtils.isBlank(where)) "" else " WHERE $where"
    val sql = String.format(
      "SELECT COUNT(1) AS count FROM %s%s",
      escapeIdentifier(tableName, sc.sqlDialect),
      whereClause,
    )
    val st = sc.prepareStatement(sql, false)
    return try {
      setToStatement(st, params)
      val rs = st.executeQuery()
      if (!rs.next()) throw BzError("errCodeErr", "Sql count, result has no row")
      rs.getLong(1)
    } finally {
      try {
        st.close()
      } catch (e: SQLException) {
        logger.error("关闭 statement 报错", e)
      }
    }
  }

  fun aggregateQuery(
    
    sc: SmartConnection,
    tableName: String,
    query: ComplexQuery,
    o: AggregationOptions,
  ): List<EntityValue> {
    val params: MutableList<Any> = ArrayList()
    val where = buildSqlQuery(query, params, sc.sqlDialect, tableName)
    val whereClause = if (StringUtils.isBlank(where)) "" else " WHERE $where"
    val queryContent = StringBuilder("SELECT ")
    val groupBy = StringBuilder(" GROUP BY ")
    val groupByFields = mutableListOf<String>()
    for (field in o.groupBy) {
      if (field.statisticDateType == null) {
        groupByFields.add(field.name)
      } else {
        groupByFields.add(field.alias ?: field.name)
      }
    }
    for (field in o.fields) {
      val alias = field.alias ?: field.name
      // 只做查询的字段必须参与分组
      if (field.function == null && field.rightFields.isEmpty()) {
        // 已经在分组里的普通字段就不再添加到分组，分组的时候会将分组字段添加到查询部分
        if (!groupByFields.contains(field.name)) {
          groupBy.append("${escapeIdentifier(field.name, sc.sqlDialect)}, ")
          queryContent.append("(${escapeIdentifier(field.name, sc.sqlDialect)}) AS \"$alias\", ")
        }
      } else {
        // 聚合字段或者做四则运算的字段的处理
        val arithmeticSql = buildArithmeticSql(sc.sqlDialect, field)
        // 别名在 Derby 的查询和分组部分，要么都加双引号，要么都不加。若不加则字段会自动变成大写
        // 别名在 Dameng 的查询部分，必须加双引号，否则关键字会变成大写。其他数据库无影响
        queryContent.append("($arithmeticSql) AS \"$alias\", ")
      }
    }

    val groupByClause = if (o.groupBy.isNotEmpty()) {
      for (item in o.groupBy) {
        val alias = item.alias ?: item.name
        // 普通字段的分组
        if (item.statisticDateType == null) {
          val name = escapeIdentifier(item.name, sc.sqlDialect)
          groupBy.append("$name, ")
          queryContent.append("$name AS \"$alias\", ")
        } else {
          // 日期字段分组
          val dateFormat = when (sc.sqlDialect) {
            DbType.SqlServer -> item.statisticDateType.sqlServerDateFormat
            DbType.MySQL -> item.statisticDateType.mySqlDateFormat
            DbType.Dameng -> item.statisticDateType.damengDateFormat
            DbType.Derby -> item.statisticDateType.derbyDateFormat
            else -> throw BzError("errUnsupportedDBType", sc.sqlDialect)
          }
          val content = String.format("$dateFormat AS \"$alias\"", item.name)
          val groupField = String.format("$dateFormat, ", item.name)
          groupBy.append(groupField)
          queryContent.append("$content, ")
        }
      }
      groupBy.substring(0, groupBy.length - 2)
    } else {
      ""
    }

    val queryContentClause = queryContent.substring(0, queryContent.length - 2)
    val orderBy = if (o.sort.isNotEmpty()) buildOrderByClause(o.sort, sc.sqlDialect) else ""
    val orderByClause = if (orderBy.isEmpty()) "" else " ORDER BY $orderBy"

    val sql = String.format(
      "%s FROM %s%s%s%s",
      queryContentClause,
      escapeIdentifier(tableName, sc.sqlDialect),
      whereClause,
      groupByClause,
      orderByClause,
    )
    val st = sc.prepareStatement(sql, false)
    return try {
      setToStatement(st, params)
      val rs = st.executeQuery()
      resultSetToEntityValues(tableName, rs)
    } finally {
      try {
        st.close()
      } catch (e: SQLException) {
        logger.error("Failed to close statement", e)
      }
    }
  }

  private fun buildArithmeticSql(sqlDialect: DbType, field: AdvancedField): String {
    var arithmetic = StringBuilder()
    if (field.function != null) {
      arithmetic.append("${field.function.value}(${escapeIdentifier(field.name, sqlDialect)})")
    } else {
      arithmetic.append(escapeIdentifier(field.name, sqlDialect))
    }
    if (field.operators.isNotEmpty()) {
      for ((index, operator) in field.operators.withIndex()) {
        // 运算具有优先级，该处理确保之前的加减法运算可以先执行
        if (index > 0 && (operator == ArithmeticOperator.Multiply || operator == ArithmeticOperator.Divide)) {
          arithmetic = StringBuilder("($arithmetic)")
        }
        var right = field.rightFields[index]
        if (right is Map<*, *>) right = JsonHelper.mapper.convertValue(right, AdvancedField::class.java)
        when (right) {
          is String -> arithmetic.append(" ${operator.value} ${escapeIdentifier(right, sqlDialect)}")
          is Number -> arithmetic.append(" ${operator.value} $right")
          is AdvancedField -> {
            arithmetic.append(" ${operator.value} (")
            arithmetic.append(buildArithmeticSql(sqlDialect, right))
            arithmetic.append(")")
          }
        }
      }
    }
    return arithmetic.toString()
  }
}