package com.seer.trick.base.http.handler

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.HttpServerManager.noAuth
import com.seer.trick.base.http.HttpServerManager.setUserSessionCookies
import com.seer.trick.base.http.getReqBody
import com.seer.trick.base.http.operator
import com.seer.trick.base.user.UserService
import com.seer.trick.base.user.dingtalk.DingTalkOAuthManager.readConfigs
import io.javalin.http.Context

object UserHandler {

  fun registerHandlers() {
    val c = Handlers("api")
    c.get("ping", ::ping, auth())
    c.post("sign-in", ::signIn, noAuth())
    c.get("sign-out", ::signOut, auth())
    c.post("change-password", ::changePassword, auth())
    c.get("check-third", ::checkThird, noAuth())
    c.post("user-op-log", ::recordUserOp, auth())
  }

  private fun checkThird(ctx: Context){
    val configs = readConfigs()
    ctx.json(mapOf(
      "wwxEnabled" to configs.wwxConfig.wwxEnabled,
      "feiShuEnabled" to configs.feiShuConfig.feiShuEnabled,
      "dingEnabled" to configs.dingConfig.dingEnabled
    ))
  }



  private fun ping(ctx: Context) {
    val op = ctx.operator()
    ctx.json(
      mapOf(
        "id" to op.userId,
        "username" to op.username,
        "roAdmin" to op.admin,
        "permissions" to op.permissions
      )
    )
  }

  private fun signIn(ctx: Context) {
    val req: SignInReq = ctx.getReqBody()

    

    val session = UserService.signIn(req.username, req.password)

    setUserSessionCookies(ctx, session.userId, session.userToken)

    ctx.json(SignInRes(session.userId, session.userToken))
  }

  private fun signOut(ctx: Context) {
    val op = ctx.operator()

    UserService.signOut(op.userId)

    setUserSessionCookies(ctx, null, null)

    ctx.status(200)
  }

  private fun changePassword(ctx: Context) {
    val op = ctx.operator()

    val req: ChangePasswordReq = ctx.getReqBody()

    UserService.changePassword(op, req.oldPassword, req.newPassword)

    ctx.status(200)
  }

  private fun recordUserOp(ctx: Context) {
    val op = ctx.operator()
    val req: EntityValue = ctx.getReqBody()

    req.remove("id")
    req.remove("createdOn")
    req.remove("createdBy")
    req["operator"] = op.userId

    EntityRwService.createOne("UserOpLog", req)

    ctx.status(200)
  }
}

data class SignInReq(
  val username: String,
  val password: String
)

data class SignInRes(
  val userId: String,
  val userToken: String
)

data class ChangePasswordReq(
  val oldPassword: String, val newPassword: String
)
