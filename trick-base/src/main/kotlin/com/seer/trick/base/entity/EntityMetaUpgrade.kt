package com.seer.trick.base.entity

import com.seer.trick.base.BaseCenter.baseConfig
import com.seer.trick.helper.JsonFileHelper
import org.apache.commons.io.FileUtils
import org.slf4j.LoggerFactory
import java.io.File

/**
 * 处理业务对象管理升级
 */
object EntityMetaUpgrade {
  
  private val logger = LoggerFactory.getLogger(javaClass)
  
  /**
   * 升级业务对象的管理方式
   */
  fun upgrade() {
    metaFileToDir()
    metaDirToMetaDiff()
  }
  
  // 第一个版本将业务对象的修改存储到一个大文件中
  private fun metaFileToDir() {
    val metaFile = File(baseConfig.configDir, "meta.json")
    if (!metaFile.exists()) return
    
    logger.warn("Convert meta.json to meta dir")
    
    val emList: List<EntityMeta>? = JsonFileHelper.readJsonFromFile(metaFile)
    if (!emList.isNullOrEmpty()) {
      val dir = getMetaDir()
      dir.mkdirs()
      for (em in emList) {
        val file = File(dir, em.name + ".json")
        JsonFileHelper.writeJsonToFile(file, em, true)
      }
    }
    
    metaFile.delete()
  }
  
  // 第二个版本将业务对象的修改存储到一个目录
  private fun metaDirToMetaDiff() {
    val metaDir = getMetaDir()
    if (!metaDir.exists()) return
    
    val metaFilenames = metaDir.list()
    if (metaFilenames.isNullOrEmpty()) {
      FileUtils.deleteDirectory(metaDir)
      return
    }
    
    logger.warn("Convert meta dir to meta diff")
    
    for (fn in metaFilenames) {
      val metaFile = File(metaDir, fn)
      val em: EntityMeta = JsonFileHelper.readJsonFromFile(metaFile) ?: continue
      EntityMetaManager.fixEntityMeta(em)
      EntityMetaManager.saveEntityDiff(em)
    }
    
    FileUtils.deleteDirectory(metaDir)
  }
  
  private fun getMetaDir(): File {
    val file = File(baseConfig.configDir.absolutePath, "entity")
    // file.mkdirs() 不要创建
    return file
  }
  
}