package com.seer.trick.base.db

import com.mongodb.ConnectionString
import com.mongodb.client.MongoClient
import com.mongodb.client.MongoClients
import com.mongodb.client.MongoDatabase
import com.seer.trick.FatalError
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.BaseConfig
import com.seer.trick.base.DbType
import com.seer.trick.base.db.mongo.MongoIndexManager
import com.seer.trick.base.db.sql.SmartConnection
import com.seer.trick.base.db.sql.SqlIndexManager
import com.seer.trick.base.db.sql.SqlSchemaManager
import com.seer.trick.base.log.SystemKeyEvent
import com.seer.trick.base.log.SystemKeyEventLevel
import com.seer.trick.base.log.SystemKeyEventService
import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import java.io.File
import javax.sql.DataSource

object DbManager {

  private val logger = LoggerFactory.getLogger(this::class.java)

  @Volatile
  private var initialized = false

  private var sqlDataSource: DataSource? = null
  private var sqlDbType: DbType? = null

  private var mongoClient: MongoClient? = null
  private var mongoDb: MongoDatabase? = null

  fun init(baseConfig: BaseConfig) {
    val db = baseConfig.db
    if (db.type == DbType.Derby) {
      initInternalDb(baseConfig)
    } else if (db.type == DbType.MongoDB) {
      initMongoDb(baseConfig)
    } else {
      initSqlDb(baseConfig)
    }

    sync()

    initialized = true
  }

  private fun initInternalDb(baseConfig: BaseConfig) {
    logger.info("Use internal Db")

    System.setProperty("derby.drda.startNetworkServer", "true")
    // Booting Derby
    val clazz = Class.forName("org.apache.derby.jdbc.EmbeddedDriver")
    clazz.getConstructor().newInstance()

    val dbDir = File(baseConfig.projectDir, "db")
    val config = HikariConfig()
    val url = String.format("*************************", dbDir.canonicalPath)
    logger.info("Derby, $url")
    config.jdbcUrl = url
    config.isAutoCommit = false
    sqlDataSource = HikariDataSource(config)
    sqlDbType = DbType.Derby
  }

  private fun initMongoDb(baseConfig: BaseConfig) {
    val dsConfig = baseConfig.db
    val url: String = StringUtils.firstNonBlank(dsConfig.url, "mongodb://localhost/m4")
    logger.info("Connect MongoDB $url")

    val dbName: String = StringUtils.firstNonBlank(dsConfig.dbName, "m4")

    val connectionString = ConnectionString(url)
    mongoClient = MongoClients.create(connectionString)
    mongoDb = mongoClient!!.getDatabase(dbName)
  }

  private fun initSqlDb(baseConfig: BaseConfig) {
    val dbConfig = baseConfig.db

    var url = dbConfig.url
    if (url.isNullOrBlank()) {
      if (dbConfig.type == DbType.MySQL) {
        val host = StringUtils.firstNonBlank(dbConfig.host, "127.0.0.1")
        val port = dbConfig.port ?: 3306
        val dbName = StringUtils.firstNonBlank(dbConfig.dbName, "m4")
        url = "**********************************************************************"
      } else {
        throw IllegalArgumentException("No database URL")
      }
    }

    logger.info("Init SQL db $url")

    val config = HikariConfig()
    config.jdbcUrl = url
    config.username = dbConfig.username
    config.password = dbConfig.password
    config.isAutoCommit = false
    sqlDataSource = HikariDataSource(config)
    sqlDbType = baseConfig.db.type
  }

  fun dispose() {
    logger.info("Dispose db manager")

    initialized = false
    if (sqlDataSource != null) {
      try {
        (sqlDataSource as HikariDataSource).close()
      } catch (e: Exception) {
        logger.error("Dispose db manager", e)
      }
    }
    if (mongoClient != null) {
      try {
        mongoClient!!.close()
      } catch (e: Exception) {
        logger.error("Dispose db manager", e)
      }
    }
  }

  fun mustGetMongoDb(): MongoDatabase = mongoDb ?: throw FatalError("errNoMongoDB")

  fun getSqlConnection(): SmartConnection {
    val ds = sqlDataSource ?: throw FatalError("errNoSqlDb")
    return SmartConnection(ds.connection, sqlDbType!!)
  }

  /**
   * 强制手工同步
   */
  fun syncForced() {
    sync()
    SystemKeyEventService.record(
      
      SystemKeyEvent(level = SystemKeyEventLevel.Warning, group = "Base", title = "用户强制手工更新数据库结构"),
    )
  }

  private fun sync() {
    val db = BaseCenter.baseConfig.db
    if (db.type == DbType.Derby || db.type == DbType.MySQL || db.type == DbType.SqlServer || db.type == DbType.Dameng) {
      SqlSchemaManager.sync(BaseCenter.entityMetaMap)
      SqlIndexManager.sync(BaseCenter.entityMetaMap)
    } else if (db.type == DbType.MongoDB) {
      MongoIndexManager.sync(BaseCenter.entityMetaMap)
    }
  }

  //  fun <T> useSqlConnection(worker: (sc: SmartConnection) -> T): T {

  //    return getSqlConnection().use(worker)
  //  }
  //
  //  private fun cleanConfig() {
  //    //             if (dialect == SqlDialect.MySQL) {
  //    //                 urlType = "mysql"
  //    //                 if (port.isNullOrBlank()) port = "3306"
  //    //                 query =
  //    //                     "useUnicode=true&characterEncoding=utf8&autoReconnect=true" +
  //    //                             "&sessionVariables=sql_mode=ANSI_QUOTES" +
  //    //                             "&serverTimezone=Asia/Shanghai"
  //    //             } else {
  //    //                 throw ObservableError("数据库类型不支持外部配置 $dialect")
  //    //             }
  //  }
  //
}