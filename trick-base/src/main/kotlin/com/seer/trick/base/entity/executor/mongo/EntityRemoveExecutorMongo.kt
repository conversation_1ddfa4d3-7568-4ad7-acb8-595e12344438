package com.seer.trick.base.entity.executor.mongo

import com.seer.trick.Cq
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.FieldType
import com.seer.trick.base.entity.executor.EntityRemoveWorkContext
import com.seer.trick.base.entity.service.FindOptions

object EntityRemoveExecutorMongo {
  
  // 如果带了 includingFields，用于删除关联表，不删主表
  fun execute(
    ctx: EntityRemoveWorkContext, entityMeta: EntityMeta, targetIds: List<String>,
    includingFields: List<FieldMeta>? = null
  ) {
    processEntity(ctx, entityMeta, targetIds, includingFields)

    for (entityName in ctx.removing.keys) {
      val query = ctx.removing[entityName]
      val em = BaseCenter.mustGetEntityMeta(entityName)
      if (query != null) MongoExecutor.deleteMany(em, query)
    }
  }
  
  private fun processEntity(
    ctx: EntityRemoveWorkContext, entityMeta: EntityMeta, targetIds: List<String>,
    includingFields: List<FieldMeta>? = null
  ) {
    val fields: List<FieldMeta> = if (includingFields == null) {
      ctx.removing[entityMeta.name] = Cq.include("id", targetIds)
      entityMeta.fields.values.toList()
    } else {
      includingFields
    }
    for (fd in fields) {
      if (fd.type == FieldType.Component) {
        val componentEntityDef = BaseCenter.mustGetRefEntityMeta(fd)
        val componentValues = MongoExecutor.findMany(
          componentEntityDef,
          Cq.include(FieldMeta.COLUMN_OWNER, targetIds),
          FindOptions(projection = listOf("id"))
        )
        val componentIds = componentValues.map { it["id"] as String }
        if (componentIds.isNotEmpty()) {
          val refComponentDef = BaseCenter.mustGetRefEntityMeta(fd)
          processEntity(ctx, refComponentDef, componentIds, null)
        }
      }
    }
  }
}