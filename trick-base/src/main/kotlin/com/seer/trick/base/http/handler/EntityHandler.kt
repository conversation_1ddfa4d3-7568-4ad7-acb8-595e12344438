package com.seer.trick.base.http.handler

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.*
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.*
import com.seer.trick.base.entity.clean.EntityCleanService
import com.seer.trick.base.entity.service.EntityRwScript
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.http.*
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.log.SystemKeyEvent
import com.seer.trick.base.log.SystemKeyEventService
import com.seer.trick.base.user.Operator
import com.seer.trick.base.user.PermissionManager
import com.seer.trick.helper.CollectionHelper
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper
import io.javalin.http.Context
import io.javalin.websocket.WsMessageContext
import org.apache.commons.lang3.StringUtils

object EntityHandler : WebSocketSubscriber() {
  
  fun registerHandlers() {
    WebSocketManager.subscribers += this
    
    val c = Handlers("api/entity")
    c.post("create/one", ::createOne, auth())
    c.post("create/many", ::createMany, auth())
    c.post("update/one", ::updateOne, auth())
    c.post("update/many", ::updateMany, auth())
    c.post("remove/one", ::removeOne, auth())
    c.post("remove/many", ::removeMany, auth())
    c.post("remove/all", ::removeAll, auth())
    
    c.post("save/one", ::saveOne, auth())
    
    c.post("find/one", ::findOne, auth())
    c.post("find/many", ::findMany, auth())
    c.post("find/count", ::findCount, auth())
    c.post("find/page", ::findPage, auth())
    c.post("find/batch", ::findBatch, auth())
    
    c.post("get-many-by-ids", ::getManyEntities, auth())
    c.post("get-many-digests-by-ids", ::getManyEntityDigests, auth())
    
    c.post("query-id/register", ::registerQuery, auth())
    c.get("query-id/{id}", ::getQueryById, auth())
    
    c.get("filter-cases", ::listFilterCases, auth())
    c.post("filter-cases/{entityName}", ::saveFilterCase, auth())
    c.post("filter-cases/delete/{id}", ::removeFilterCase, auth())
    
    c.post("clean-old-data", ::cleanOldData, auth())
    
    c.get("count-all", ::countAll, auth())
  }
  
  override fun onMessage(ctx: WsMessageContext, msg: WsMsg, env: WsEnv) {
    when (msg.action) {
      "EntityChanged::Query" -> onEntityChangedQuery(ctx, msg)
    }
  }
  
  private fun createOne(ctx: Context) {
    val op = ctx.operator()
    
    val req: CreateOneReq = ctx.getReqBody()
    
    val em = BaseCenter.mustGetEntityMeta(req.entityName)
    
    pCreateCheck(em, op)
    
    val entityValue = req.entityValue
    
    EntityHandlerExt.extBeforeCreate(em, listOf(entityValue))
    
    if (StringUtils.isNotBlank(op.userId)) {
      entityValue[FieldMeta.FIELD_CREATED_BY] = op.userId
    }
    val id = EntityRwService.createOne(req.entityName, entityValue)
    
    EntityHandlerExt.extAfterCreate(em, listOf(id))
    
    ctx.json(CreateOneResult(id))
  }
  
  private fun createMany(ctx: Context) {
    val op = ctx.operator()
    
    val req: CreateManyReq = ctx.getReqBody()
    
    val em = BaseCenter.mustGetEntityMeta(req.entityName)
    
    pCreateCheck(em, op)
    
    val entityValues = req.entityValues
    
    EntityHandlerExt.extBeforeCreate(em, entityValues)
    
    if (StringUtils.isNotBlank(op.userId)) {
      for (ev in entityValues) ev[FieldMeta.FIELD_CREATED_BY] = op.userId
    }
    val ids = EntityRwService.createMany(req.entityName, entityValues)
    
    EntityHandlerExt.extAfterCreate(em, ids)
    
    ctx.json(CreateManyResult(ids))
  }
  
  private fun updateOne(ctx: Context) {
    // TODO 行权限
    val op = ctx.operator()
    
    val req: UpdateReq = ctx.getReqBody()
    
    val em = BaseCenter.mustGetEntityMeta(req.entityName)
    
    pEditCheck(em, op)
    
    val update = req.update
    
    var query = if (!req.id.isNullOrBlank()) {
      Cq.idEq(req.id)
    } else {
      req.query ?: throw BzError("errEntityRequestMissingIdOrQueryParam")
    }
    query = cleanUiQuery(query)
    
    EntityHandlerExt.extBeforeUpdate(em, query, update)
    
    if (StringUtils.isNotBlank(op.userId)) {
      update[FieldMeta.FIELD_MODIFIED_BY] = op.userId
    }
    val updatedCount = EntityRwService.updateOne(req.entityName, query, update)
    
    EntityHandlerExt.extAfterUpdate(em)
    
    ctx.json(UpdateResult(updatedCount))
  }
  
  private fun updateMany(ctx: Context) {
    // TODO 行权限
    val op = ctx.operator()
    
    val req: UpdateReq = ctx.getReqBody()
    
    val em = BaseCenter.mustGetEntityMeta(req.entityName)
    
    pEditCheck(em, op)
    
    val update = req.update
    
    var query = req.query ?: throw BzError("errEntityRequestMissingQueryParam")
    query = cleanUiQuery(query)
    
    EntityHandlerExt.extBeforeUpdate(em, query, update)
    
    if (StringUtils.isNotBlank(op.userId)) {
      update[FieldMeta.FIELD_MODIFIED_BY] = op.userId
    }
    val updatedCount = EntityRwService.updateMany(req.entityName, query, update)
    
    EntityHandlerExt.extAfterUpdate(em)
    
    ctx.json(UpdateResult(updatedCount))
  }
  
  private fun removeOne(ctx: Context) {
    // TODO 行权限
    val op = ctx.operator()
    
    val req: RemoveReq = ctx.getReqBody()
    
    val em = BaseCenter.mustGetEntityMeta(req.entityName)
    
    pRemoveCheck(em, op)
    
    var query = if (!req.id.isNullOrBlank()) {
      Cq.idEq(req.id)
    } else {
      req.query ?: throw BzError("errEntityRequestMissingIdOrQueryParam")
    }
    query = cleanUiQuery(query)
    
    EntityHandlerExt.extBeforeDelete(em, query)
    
    val removedCount = EntityRwService.removeOne(req.entityName, query)
    
    EntityHandlerExt.extAfterDelete(em)
    
    ctx.json(RemoveResult(removedCount))
  }
  
  private fun removeMany(ctx: Context) {
    // TODO 行权限
    val op = ctx.operator()
    
    val req: RemoveReq = ctx.getReqBody()
    
    val em = BaseCenter.mustGetEntityMeta(req.entityName)
    
    pRemoveCheck(em, op)
    
    var query = req.query ?: throw BzError("errEntityRequestMissingQueryParam")
    query = cleanUiQuery(query)
    
    EntityHandlerExt.extBeforeDelete(em, query)
    
    val removedCount = EntityRwService.removeMany(req.entityName, query)
    
    EntityHandlerExt.extAfterDelete(em)
    
    ctx.json(RemoveResult(removedCount))
  }
  
  private fun removeAll(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw Error403("权限不够（删除全部），需要管理员")
    
    val req: RemoveReq = ctx.getReqBody()
    
    val em = BaseCenter.mustGetEntityMeta(req.entityName)
    
    EntityHandlerExt.extBeforeDelete(em, Cq.all())
    
    SystemKeyEventService.record(SystemKeyEvent(group = "Entity", title = "清空业务对象 ${em.name} ${em.label}"))
    
    val removedCount = EntityRwService.removeMany(req.entityName, Cq.all())
    
    EntityHandlerExt.extAfterDelete(em)
    
    ctx.json(RemoveResult(removedCount))
  }
  
  /**
   * create or update
   */
  private fun saveOne(ctx: Context) {
    val op = ctx.operator()
    
    val req: SaveOneReq = ctx.getReqBody()
    
    val em = BaseCenter.mustGetEntityMeta(req.entityName)
    
    pCreateCheck(em, op)
    pEditCheck(em, op)
    
    val entityValue = req.entityValue
    
    val id = EntityHelper.mustGetId(entityValue)
    if (EntityRwService.exists(em.name, Cq.idEq(id))) {
      val query = Cq.idEq(id)
      EntityHandlerExt.extBeforeUpdate(em, query, entityValue)
      
      if (StringUtils.isNotBlank(op.userId)) {
        entityValue[FieldMeta.FIELD_MODIFIED_BY] = op.userId
      }
      EntityRwService.updateOne(req.entityName, query, entityValue)
      
      EntityHandlerExt.extAfterUpdate(em)
    } else {
      EntityHandlerExt.extBeforeCreate(em, listOf(entityValue))
      
      if (StringUtils.isNotBlank(op.userId)) {
        entityValue[FieldMeta.FIELD_CREATED_BY] = op.userId
      }
      EntityRwService.createOne(req.entityName, entityValue)
      
      EntityHandlerExt.extAfterCreate(em, listOf(id))
    }
    
    ctx.status(200)
  }
  
  private fun findOne(ctx: Context) {
    val op = ctx.operator()
    
    val req: FindOneReq = ctx.getReqBody()
    val ev = doFindOne(req, op)
    ctx.json(FindOneResult(ev))
  }
  
  private fun doFindOne(req: FindOneReq, op: Operator): EntityValue? {
    val em = BaseCenter.mustGetEntityMeta(req.entityName)
    
    pReadCheck(em, op)
    
    val q0 = if (!req.id.isNullOrBlank()) {
      Cq.idEq(req.id)
    } else if (req.query != null) {
      cleanUiQuery(req.query)
    } else if (!req.fuzzy.isNullOrBlank()) {
      buildFuzzyQuery(em, req.fuzzy)
    } else {
      Cq.all()
    }
    val q = filterRows(q0, em, op)
    
    val o = checkOption(FindOptions(projection = req.projection, sort = req.sort, skip = req.skip))
    
    val ev = if (em.otherDatasource) {
      EntityRwScript.findOne(req.entityName, q, o)
    } else {
      EntityRwService.findOne(req.entityName, q, o)
    }
    
    if (ev != null) EntityHandlerExt.extRead(em, listOf(ev), op)
    return ev
  }
  
  private fun findMany(ctx: Context) {
    val op = ctx.operator()
    
    val req: FindManyReq = ctx.getReqBody()
    
    val evList = doFindMany(req, op)
    
    ctx.json(evList) // TODO 也套起来？
  }
  
  private fun doFindMany(req: FindManyReq, op: Operator): List<EntityValue> {
    val em = BaseCenter.mustGetEntityMeta(req.entityName)
    
    pReadCheck(em, op)
    
    val q0 = if (req.query != null) {
      cleanUiQuery(req.query)
    } else if (!req.fuzzy.isNullOrBlank()) {
      buildFuzzyQuery(em, req.fuzzy)
    } else {
      Cq.all()
    }
    val q = filterRows(q0, em, op)
    
    val o = checkOption(FindOptions(projection = req.projection, sort = req.sort, skip = req.skip, limit = req.limit))
    
    val evList = if (em.otherDatasource) {
      EntityRwScript.findMany(req.entityName, q, o)
    } else {
      EntityRwService.findMany(req.entityName, q, o)
    }
    
    EntityHandlerExt.extRead(em, evList, op)
    return evList
  }
  
  private fun findPage(ctx: Context) {
    val op = ctx.operator()
    
    val req: FindPageReq = ctx.getReqBody()
    
    val r = doFindPage(req, op)
    
    ctx.json(r)
  }
  
  private fun doFindPage(req: FindPageReq, op: Operator): FindPageResult {
    val em = BaseCenter.mustGetEntityMeta(req.entityName)
    
    pReadCheck(em, op)
    
    val q0 = if (req.query != null) {
      cleanUiQuery(req.query)
    } else if (!req.fuzzy.isNullOrBlank()) {
      buildFuzzyQuery(em, req.fuzzy)
    } else {
      Cq.all()
    }
    val q = filterRows(q0, em, op)
    
    val total: Long
    val evList: List<EntityValue>
    val totalPage: Long
    var pageNo: Int = req.pageNo
    var pageSize = req.pageSize
    if (pageSize <= 0) pageSize = 20
    if (em.otherDatasource) {
      val r = EntityRwScript.findPage(
        em.name,
        q,
        req.pageNo,
        pageSize,
        checkOption(FindOptions(projection = req.projection, sort = req.sort)),
      )
      total = r.total
      evList = r.page
    } else {
      total = EntityRwService.count(req.entityName, q)
      totalPage = total / pageSize + if (total % pageSize == 0L) 0 else 1
      var skip = (req.pageNo - 1) * pageSize
      // 如果存在查询条件
      if (req.query != null) {
        // 若当前页大于实际页数则跳转到第一页，且偏移量为 0
        if (req.pageNo > totalPage) {
          pageNo = 1
          skip = 0
        }
      }
      
      val o = checkOption(FindOptions(projection = req.projection, sort = req.sort, skip = skip, limit = pageSize))
      evList = EntityRwService.findMany(req.entityName, q, o)
    }
    
    EntityHandlerExt.extRead(em, evList, op)
    
    return FindPageResult(pageNo, pageSize, total, evList)
  }
  
  private fun findCount(ctx: Context) {
    val op = ctx.operator()
    
    val req: FindCountReq = ctx.getReqBody()
    
    val em = BaseCenter.mustGetEntityMeta(req.entityName)
    
    pReadCheck(em, op)
    
    val q = if (req.query != null) {
      val query = req.query
      filterRows(query, em, op)
    } else {
      Cq.all()
    }
    
    val total = EntityRwService.count(req.entityName, q)
    
    ctx.json(FindCountResult(total))
  }
  
  private fun findBatch(ctx: Context) {
    val op = ctx.operator()
    val req: List<FindBatchReq> = ctx.getReqBody()
    
    val r = req.map {
      when (it.type) {
        FindBatchType.One -> {
          doFindOne(JsonHelper.mapper.convertValue(it.req, FindOneReq::class.java), op)
        }
        
        FindBatchType.Many -> {
          doFindMany(JsonHelper.mapper.convertValue(it.req, FindManyReq::class.java), op)
        }
        
        FindBatchType.Page -> {
          doFindPage(JsonHelper.mapper.convertValue(it.req, FindPageReq::class.java), op)
        }
      }
    }
    ctx.json(r)
  }
  
  // 批量接口，输出实体，查出多种实体，按多个 id
  private fun getManyEntities(ctx: Context) {
    val op = ctx.operator()
    
    val req: GetManyEntitiesReq = ctx.getReqBody()
    
    // entity name -> entity id -> ev
    val r: MutableMap<String, Map<String, EntityValue>> = HashMap()
    for ((entityName, ids) in req.ids) {
      val em = BaseCenter.mustGetEntityMeta(entityName)
      pReadCheck(em, op)
      val evList = EntityRwService.findMany(entityName, Cq.include("id", ids))
      
      EntityHandlerExt.extRead(em, evList, op)
      
      r[entityName] = CollectionHelper.listToMap(evList, EntityHelper::mustGetId)
    }
    
    ctx.json(r)
  }
  
  // 批量接口，输出实体摘要，查出多种实体，按多个 id
  private fun getManyEntityDigests(ctx: Context) {
    val op = ctx.operator()
    
    val req: GetManyEntitiesReq = ctx.getReqBody()
    
    // entity name -> entity id -> digest
    val r: MutableMap<String, Map<String, EntityDigestValue>> = HashMap()
    for ((entityName, ids) in req.ids) {
      val em = BaseCenter.mustGetEntityMeta(entityName)
      pReadCheck(em, op)
      val digestFields = em.digest?.fields ?: listOf("id")
      val projection = ArrayList(digestFields)
      if (null == projection.find { it == "id" }) projection.add("id") // 强制包含 id
      val evList = EntityRwService.findMany(
        entityName,
        Cq.include("id", ids),
        checkOption(FindOptions(projection = projection)),
      )
      val digests: List<EntityDigestValue> = evList.map { ev ->
        val digest = digestFields.map { fn -> ev[fn] }.joinToString("|")
        EntityDigestValue(EntityHelper.mustGetId(ev), digest, ev)
      }
      r[entityName] = CollectionHelper.listToMap(digests) { d -> d.id }
    }
    
    ctx.json(r)
  }
  
  private fun pCreateCheck(em: EntityMeta, op: Operator) {
    if (!PermissionManager.pCreate(em, op)) throw Error403("没有创建权：${em.label}")
  }
  
  private fun pEditCheck(em: EntityMeta, op: Operator) {
    if (!PermissionManager.pEdit(em, op)) throw Error403("没有编辑权：${em.label}")
  }
  
  private fun pRemoveCheck(em: EntityMeta, op: Operator) {
    if (!PermissionManager.pRemove(em, op)) throw Error403("没有删除权：${em.label}")
  }
  
  private fun pReadCheck(em: EntityMeta, op: Operator) {
    if (!PermissionManager.pRead(em, op)) throw Error403("没有查看权：${em.label}")
  }
  
  /**
   * 检查 FindOptions
   *
   * 去掉 o.sort 中无效的值，如 "+", "-", ""
   */
  private fun checkOption(o: FindOptions): FindOptions =
    o.copy(sort = o.sort?.filter { it.isNotBlank() && it != "+" && it != "-" })
  
  private fun registerQuery(ctx: Context) {
    val req: RegisterQuery = ctx.getReqBody()
    
    val query = req.query
    var id = BaseCenter.queryIds.queryToId[query]
    if (id == null) {
      id = IdHelper.oidStr()
      BaseCenter.queryIds.queryToId[query] = id
      BaseCenter.queryIds.idToQuery[id] = query
    }
    
    ctx.json(mapOf("id" to id))
  }
  
  private fun getQueryById(ctx: Context) {
    val id = ctx.pathParam("id")
    
    val query = BaseCenter.queryIds.idToQuery[id]
    
    ctx.json(mapOf("query" to query))
  }
  
  private fun listFilterCases(ctx: Context) {
    val op = ctx.operator()
    
    val entityName = ctx.queryParam("entityName") ?: throw MissingQueryParamError("entityName")
    val global = ctx.queryParam("global")
    
    val qItems = mutableListOf(Cq.eq("page", entityName))
    qItems += if (global == "true") {
      Cq.eq("global", true)
    } else {
      Cq.eq("owner", op.userId)
    }
    val q = Cq.and(qItems)
    val cases = EntityRwService.findMany("ListFilterCase", q)
    
    ctx.json(cases)
  }
  
  private fun saveFilterCase(ctx: Context) {
    val op = ctx.operator()
    
    val entityName = ctx.pathParam("entityName")
    val caseEv: EntityValue = ctx.getReqBody()
    
    val case = HashMap(caseEv)
    case["page"] = entityName
    case["owner"] = op.userId
    
    val id = case["id"] as String?
    if (id.isNullOrBlank()) {
      EntityRwService.createOne("ListFilterCase", case)
    } else {
      EntityRwService.updateOne("ListFilterCase", Cq.idEq(id), case)
    }
    
    ctx.status(200)
  }
  
  private fun removeFilterCase(ctx: Context) {
    val id = ctx.pathParam("id")
    EntityRwService.removeOne("ListFilterCase", Cq.idEq(id))
    
    ctx.status(200)
  }
  
  private fun buildFuzzyQuery(em: EntityMeta, fuzzy: String): ComplexQuery {
    val items: MutableList<ComplexQuery> = mutableListOf()
    for (fm in em.fields.values) {
      if (fm.fuzzyFilter && (fm.type == FieldType.String || fm.type == FieldType.Reference)) {
        items + Cq.containIgnoreCase(fm.name, fuzzy)
      }
    }
    return if (items.isNotEmpty()) {
      return Cq.or(items)
    } else {
      Cq.containIgnoreCase("id", fuzzy)
    }
  }
  
  fun filterRows(q: ComplexQuery, em: EntityMeta, op: Operator): ComplexQuery {
    val filterQ = op.permissions?.entityRowObj?.get(em.name)
    return if (filterQ != null) {
      if (!filterQ.items.isNullOrEmpty()) {
        val q2 = filterQ.copy()
        ComplexQuery.mergeComplexQueries(q, q2)
      } else {
        q
      }
    } else {
      q
    }
  }
  
  private fun cleanOldData(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw Error403("权限不够（元数据项），需要管理员")
    
    EntityCleanService.doCleanAsync()
    
    ctx.status(200)
  }
  
  private fun countAll(ctx: Context) {
    val all = EntityRwService.countAll()
    
    ctx.json(all)
  }
  
  private fun onEntityChangedQuery(ctx: WsMessageContext, msg: WsMsg) {
    val req: EntityChangedQuery = JsonHelper.mapper.readValue(msg.content!!, jacksonTypeRef())
    val res = req.entityNames.associateWith { EntityRwService.entityLastChanged[it] }
    ctx.send(WsMsg.json("EntityChanged::Reply", res, replyToId = msg.id))
  }
  
  data class EntityChangedQuery(val entityNames: List<String>)
  
  
  /**
   * 将 CurrentUser/CurrentUsername 替换为 Eq
   */
  fun cleanUiQuery(query: ComplexQuery): ComplexQuery {
    return if (query.type == ComplexQueryType.General) {
      if (query.operator == ComplexQueryOperator.CurrentUser) {
        query.copy(operator = ComplexQueryOperator.Eq, value = Operator.current()?.userId)
      } else if (query.operator == ComplexQueryOperator.CurrentUsername) {
        query.copy(operator = ComplexQueryOperator.Eq, value = Operator.current()?.username)
      } else {
        query
      }
    } else if (query.type == ComplexQueryType.Compound) {
      val items = query.items
      if (items.isNullOrEmpty()) {
        query
      } else {
        query.copy(items = items.map { item -> cleanUiQuery(item) }.toMutableList())
      }
    } else {
      query
    }
  }
}

data class CreateOneReq(val entityName: String, val entityValue: EntityValue)

data class CreateOneResult(val id: String)

data class CreateManyReq(val entityName: String, val entityValues: List<EntityValue>)

data class CreateManyResult(val ids: List<String>)

data class UpdateReq(
  val entityName: String,
  val id: String? = null,
  val query: ComplexQuery? = null,
  val update: EntityValue,
  val limit: Int? = null,
)

data class UpdateResult(val updatedCount: Long)

data class RemoveReq(
  val entityName: String,
  val id: String? = null,
  val query: ComplexQuery? = null,
  val limit: Int? = null,
)

data class RemoveResult(val removedCount: Long)

data class FindCountReq(val entityName: String, val query: ComplexQuery? = null)

data class FindCountResult(val count: Long)

data class FindOneReq(
  val entityName: String,
  val id: String? = null,
  val query: ComplexQuery? = null,
  val fuzzy: String? = null,
  val projection: List<String>? = null,
  val sort: List<String>? = null,
  val skip: Int? = null,
)

data class FindOneResult(val entityValue: EntityValue?)

data class FindManyReq(
  val entityName: String,
  val query: ComplexQuery? = null,
  val fuzzy: String? = null,
  val projection: List<String>? = null,
  val sort: List<String>? = null,
  val skip: Int? = null,
  val limit: Int? = null,
)

data class FindPageReq(
  val entityName: String,
  val query: ComplexQuery? = null,
  val fuzzy: String? = null,
  val projection: List<String>? = null,
  val sort: List<String>? = null,
  val pageNo: Int = 1,
  val pageSize: Int = 20,
)

data class FindPageResult(val pageNo: Int, val pageSize: Int, val total: Long, val page: List<EntityValue>)

enum class FindBatchType {
  One, // 查单条
  Many, // 查多条
  Page, // 查分页
}

data class FindBatchReq(val type: FindBatchType, var req: Any)

data class FindBatchReqList(val reqList: List<FindBatchReq>)

data class GetManyEntitiesReq(val ids: Map<String, List<String>>) // entity name to ids

data class RegisterQuery(val query: String)

data class EntityDigestValue(val id: String, val digest: String, val ev: EntityValue)

data class SaveOneReq(val entityName: String, val entityValue: EntityValue)