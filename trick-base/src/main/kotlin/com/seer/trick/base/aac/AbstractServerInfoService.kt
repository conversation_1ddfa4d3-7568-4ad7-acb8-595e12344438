package com.seer.trick.base.aac

import org.apache.commons.io.IOUtils
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import oshi.SystemInfo
import oshi.hardware.HardwareAbstractionLayer
import java.nio.charset.Charset

abstract class AbstractServerInfoService {

  protected val logger: Logger = LoggerFactory.getLogger(javaClass)

  private val systemInfo = SystemInfo()
  private val hardware: HardwareAbstractionLayer = systemInfo.hardware

  // 系统名， Windows, Linux
  private val systemFamily: String = systemInfo.operatingSystem.family

  /**
   * 获取服务器信息
   *
   * 包含：CPU序列号，主板序列号，硬盘序列号。可根据需求重写
   */
  fun getServerInfo(): ServerInfo =
    ServerInfo(cpu = getCPUSerial(), motherBoard = getMotherBoardSerial(), hardDisk = getHardDiskSerial())

  /**
   * 获取服务器硬件UUID
   */
  fun getUUID(): String = hardware.computerSystem.hardwareUUID

  /**
   * 获取CPU序列号
   */
  fun getCPUSerial(): String = hardware.processor.processorIdentifier.processorID

  /**
   * 获取主板序列号
   */
  abstract fun getMotherBoardSerial(): String

  /**
   * 获取硬盘序列号
   */
  open fun getHardDiskSerial(): String {
    val disks = hardware.diskStores
    disks.sortBy { it.serial }

    return StringUtils.deleteWhitespace(disks[0].serial)
  }

  protected fun exec(commands: Array<String>): String {
    val process = if (commands.size == 1) {
      Runtime.getRuntime().exec(commands[0])
    } else {
      Runtime.getRuntime().exec(commands)
    }
    val errorString = process.errorStream?.use {
      IOUtils.toString(it, Charset.defaultCharset())
    }
    if (!errorString.isNullOrBlank()) throw RuntimeException(errorString)
    return process.inputStream.use {
      IOUtils.toString(it, Charset.defaultCharset())
    }
  }
}

data class ServerInfo(val cpu: String, val motherBoard: String, val hardDisk: String)