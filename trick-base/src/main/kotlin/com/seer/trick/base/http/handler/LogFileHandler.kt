package com.seer.trick.base.http.handler

import com.seer.trick.BzError
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.file.FileManager
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.getReqBody
import com.seer.trick.base.http.operator
import com.seer.trick.helper.DateHelper
import com.seer.trick.helper.FileHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.getCreationTime
import io.javalin.http.Context
import org.apache.commons.io.FileUtils
import org.slf4j.LoggerFactory
import java.io.File
import java.util.*
import java.util.regex.Pattern
import kotlin.time.ExperimentalTime
import kotlin.time.measureTime

/**
 * 下载日志文件
 */
object LogFileHandler {

  private val logger = LoggerFactory.getLogger(javaClass)

  fun registerHandlers() {
    val c = Handlers("api/log-files")
    c.post("download", ::download, auth())
  }

  /**
   * 下载日志文件
   */
  @OptIn(ExperimentalTime::class)
  private fun download(ctx: Context) {
    
    val req0: DownloadLogsReq = ctx.getReqBody()
    val req = req0.copy(from = DateHelper.getHourStart(req0.from), to = DateHelper.getHourEnd(req0.to))
    logger.info("download log files, req: ${JsonHelper.writeValueAsString(req)}")

    // 最多支持下载一周的日志，超过报错提示“最多只能连续取一周的日志，请修改开始或结束日期”。
    if (req.to.time - req.from.time > 7 * 24 * 60 * 60 * 1000) {
      throw BzError("errDownloadLogsTimeOverRange")
    }

    // 创建一个临时目录，将指定类型的日志都复制到临时目录中
    val tmpDir = FileManager.nextTmpDir()
    val copyCost = measureTime {
      // 系统日志：f2.log、sys-mon.log。在 /M4Demo/logs 目录
      if (req.types.contains("system")) {
        val sysCost = measureTime {
          copyLogFiles(tmpDir, "system", "^(f2).*\\.log(\\.zip)?\$", req.from, req.to)
          copyLogFiles(
            tmpDir,
            "system",
            "^(sys-mon).*\\.log(\\.zip)?\$",
            req.from,
            req.to,
            zip = true,
            zipName = "sys-mon.zip",
          )
        }
        logger.info("download log files, system cost: $sysCost")
      }

      // 调度专用日志：traffic-info.log、prevent-info.log
      if (req.types.contains("fleet")) {
        val fleetCost = measureTime {
          copyLogFiles(tmpDir, "fleet", "^(traffic-info|prevent-info).*\\.log(\\.zip)?\$", req.from, req.to)
        }
        logger.info("download log files, fleet cost: $fleetCost")
      }

      // RBK 通讯日志：rbk.log
      if (req.types.contains("rbk")) {
        val rbkCost = measureTime { copyLogFiles(tmpDir, "rbk", "^(rbk).*\\.log(\\.zip)?\$", req.from, req.to) }
        logger.info("download log files, rbk cost: $rbkCost")
      }

      // 调度回放。在 m4/M4Demo/files/fleet-op 目录
      if (req.types.contains("fleet-op")) {
        val opCost = measureTime { copyFleetOpFiles(tmpDir, req) }
        logger.info("download log files, fleet-op cost: $opCost")
      }
    }
    // tmpDir 的文件大小
    logger.info("download log files, copy cost: $copyCost, tmpDir size:${FileUtils.sizeOfDirectory(tmpDir)}")

    // 压缩临时目录 m4-logs-<start date>-<end date>.zip
    val tmpZip = File(
      FileManager.ensureTmpDir(),
      "m4-logs-${DateHelper.formatDate(req.from, "yyyyMMddHH")}-${DateHelper.formatDate(req.to, "yyyyMMddHH")}.zip",
    )
    val zipCost = measureTime { FileHelper.zipDirToFile(tmpDir, tmpZip) }
    logger.info("download log files, zip cost: $zipCost, tmpZip size:${FileUtils.sizeOf(tmpZip)}}")

    // 返回临时目录的下载路径
    ctx.json(DownloadLogsResp(FileManager.fileToPath(tmpZip)))
  }

  /**
   * 将一种类型的文件复制到指定目录
   */
  private fun copyLogFiles(
    targetDir: File,
    type: String,
    regex: String,
    from: Date,
    to: Date,
    zip: Boolean = false,
    zipName: String? = null,
  ) {
    val logsDir = BaseCenter.baseConfig.logsDir
    val pattern = Pattern.compile(regex)
    val dir = File(targetDir, type)
    dir.mkdirs()
    val files = logsDir.listFiles()?.filter { f ->
      f.isFile &&
        pattern.matcher(f.name).matches() &&
        f.getCreationTime().time >= from.time &&
        f.lastModified() <= to.time
    } ?: return
    if (zip) {
      FileHelper.zipFilesToFile(files, File(dir, zipName ?: "$type.zip"))
    } else {
      files.map { f -> f.copyTo(File(dir, f.name), true) }
    }
  }

  /**
   * 将调度回放文件复制到指定目录
   */
  private fun copyFleetOpFiles(tmpDir: File, req: DownloadLogsReq) {
    val fleetOpDir = File(BaseCenter.baseConfig.filesDir, "fleet-op")
    val targetDir = File(tmpDir, "fleet-op")
    targetDir.mkdirs()
    // 遍历 fleetOpDir 的所有一级子目录
    for (sceneDir in fleetOpDir.listFiles() ?: arrayOf()) {
      if (!sceneDir.isDirectory) continue
      val files = sceneDir.listFiles()?.filter { f ->
        f.isFile && f.getCreationTime().time >= req.from.time && f.lastModified() <= req.to.time
      } ?: return
      if (files.isNotEmpty()) FileHelper.zipFilesToFile(files, File(targetDir, sceneDir.name + ".zip"))
    }
  }

  data class DownloadLogsReq(val from: Date, val to: Date, val types: List<String>)

  data class DownloadLogsResp(val path: String)
}