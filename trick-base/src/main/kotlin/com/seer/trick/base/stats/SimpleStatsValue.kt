package com.seer.trick.base.stats

import com.seer.trick.ComplexQuery
import com.seer.trick.base.concurrent.BaseConcurrentCenter.highTimeSensitiveExecutor
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.NumHelper

/**
 * TODO entityName + query 相同的、合并计算
 */
object SimpleStatsValue {
  
  private const val CONFIG_ITEM_ID = "SimpleStatsValue"
  
  fun get(): SsvResult {
    val configStr = "" // TODO
    if (configStr.isBlank()) return SsvResult()
    
    val config: SsvConfig = JsonHelper.mapper.readValue(configStr, SsvConfig::class.java)
    val resultItems = config.items.map { ci ->
      highTimeSensitiveExecutor.submit<SsvResultItem> {
        val num = when (ci.statType) {
          SsvConfigStatType.ItemCount ->
            EntityRwService.count(ci.entityName, ci.query).toDouble()
          
          SsvConfigStatType.SumField -> {
            val evList = EntityRwService.findMany(
              ci.entityName, ci.query, FindOptions(projection = listOf(ci.field))
            )
            evList.sumOf { NumHelper.anyToDouble(it[ci.field]) ?: 0.0 }
          }
          
          SsvConfigStatType.CountFieldUnique -> {
            val evList = EntityRwService.findMany(
              ci.entityName, ci.query, FindOptions(projection = listOf(ci.field))
            )
            val set: MutableSet<String> = HashSet()
            for (ev in evList) {
              val fv = ev[ci.field] as String?
              if (!fv.isNullOrBlank()) set += fv
            }
            set.size.toDouble()
          }
        }
        SsvResultItem(
          label = ci.label, value = num, decimals = ci.decimals,
          entityName = ci.entityName, query = ci.query
        )
      }
    }.map { it.get() }
    
    return SsvResult(items = resultItems)
  }
  
}

data class SsvConfig(
  val items: List<SsvConfigItem>
)

data class SsvConfigItem(
  val label: String,
  val entityName: String,
  val query: ComplexQuery,
  val statType: SsvConfigStatType,
  val field: String = "",
  val decimals: Int = 0,
)

enum class SsvConfigStatType {
  ItemCount, SumField, CountFieldUnique
}

data class SsvResult(
  val items: List<SsvResultItem> = emptyList()
)

data class SsvResultItem(
  val label: String,
  val value: Double,
  val decimals: Int = 0,
  val entityName: String,
  val query: ComplexQuery,
)