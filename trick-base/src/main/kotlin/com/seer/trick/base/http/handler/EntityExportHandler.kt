package com.seer.trick.base.http.handler

import com.seer.trick.ComplexQuery
import com.seer.trick.Cq
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.FieldScale
import com.seer.trick.base.entity.FieldType
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.file.FileManager
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.getReqBody
import com.seer.trick.base.http.handler.EntityHandler.cleanUiQuery
import com.seer.trick.base.http.operator
import com.seer.trick.base.user.PermissionManager
import com.seer.trick.helper.RowBuilder
import com.seer.trick.helper.XlsHelper
import io.javalin.http.Context
import org.apache.poi.xssf.streaming.SXSSFWorkbook

object EntityExportHandler {
  
  fun registerHandlers() {
    val c = Handlers("api/entity")
    c.post("export", ::exportEntities, auth())
  }
  
  private fun exportEntities(ctx: Context) {
    val op = ctx.operator()
    
    val req: ExportReq = ctx.getReqBody()
    
    val em = BaseCenter.mustGetEntityMeta(req.entityName)
    val lineFm = em.fields[FieldMeta.FIELD_LINES]
    val lineEm = if (lineFm != null) BaseCenter.mustGetRefEntityMeta(lineFm) else null
    
    if (!PermissionManager.pExport(em, op)) throw Error403("没有导出权：${em.label}")
    
    var q = if (req.ids != null) {
      Cq.include("id", req.ids)
    } else if (req.query != null) {
      cleanUiQuery(req.query)
    } else {
      Cq.all()
    }
    q = EntityHandler.filterRows(q, em, op)
    
    val evList = EntityRwService.findMany(req.entityName, q, FindOptions(projection = req.fields))
    
    EntityHandlerExt.extRead(em, evList, op)
    
    val headFmList = req.fields.map { EntityHelper.mustGetFm(em, it) }
    val lineFmList = if (req.lineExported && !req.lineFields.isNullOrEmpty() && lineEm != null)
      req.lineFields.map { EntityHelper.mustGetFm(lineEm, it) }
    else null
    
    val workbook = SXSSFWorkbook(1000)  // 保留1000行在内存中
    val sheet = workbook.createSheet(em.label)
    
    // column labels
    val head = sheet.createRow(0)
    for ((i, fm) in headFmList.withIndex()) {
      head.createCell(i).setCellValue(fm.label)
    }
    if (lineFmList != null) {
      for ((i, fm) in lineFmList.withIndex()) {
        head.createCell(i + headFmList.size).setCellValue(fm.label)
      }
    }
    
    var rowIndex = 0
    for (ev in evList) {
      if (lineFmList.isNullOrEmpty()) {
        // 只有单头
        val row = sheet.createRow(++rowIndex)
        val rb = RowBuilder(row)
        for (fm in headFmList) {
          rb.setAny(fvToCellValue(ev[fm.name], fm))
        }
      } else {
        val lines = EntityHelper.getLines(ev, FieldMeta.FIELD_LINES) ?: continue // 忽略没有单行的
        if (lines.isEmpty()) continue
        for (lineEv in lines) {
          val row = sheet.createRow(++rowIndex)
          val rb = RowBuilder(row)
          for (fm in headFmList) {
            rb.setAny(fvToCellValue(ev[fm.name], fm))
          }
          for (fm in lineFmList) {
            rb.setAny(fvToCellValue(lineEv[fm.name], fm))
          }
        }
      }
      
    }
    
    val file = FileManager.nextTmpFile(ext = "xlsx", prefix = "export")
    workbook.setSheetName(0, em.label)
    XlsHelper.workbookToFile(workbook, file)
    
    ctx.json(ExportResult(FileManager.fileToPath(file)))
  }
  
  private fun fvToCellValue(fv: Any?, fm: FieldMeta): Any? {
    if (fm.scale == FieldScale.List) return null
    if (fm.type == FieldType.Component || fm.type == FieldType.Image || fm.type == FieldType.File) return null
    return fv
  }
  
}

data class ExportReq(
  val entityName: String,
  val ids: List<String>? = null,
  val query: ComplexQuery? = null,
  val fields: List<String>,
  val lineExported: Boolean = false,
  val lineFields: List<String>? = null
)

data class ExportResult(
  val path: String
)
