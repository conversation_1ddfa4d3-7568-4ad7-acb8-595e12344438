package com.seer.trick.base.http.handler

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.base.db.DbBackupManager
import com.seer.trick.base.entity.service.EntityDumpRestoreService
import com.seer.trick.base.file.FileManager
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.getReqBody
import com.seer.trick.base.http.operator
import com.seer.trick.base.log.SystemKeyEvent
import com.seer.trick.base.log.SystemKeyEventService
import com.seer.trick.helper.FileHelper
import com.seer.trick.helper.JsonHelper
import io.javalin.http.Context
import org.apache.commons.io.FileUtils
import org.slf4j.LoggerFactory
import java.io.File

object DbHandler {

  private val logger = LoggerFactory.getLogger(this::class.java)

  fun registerHandlers() {
    val c = Handlers("api/db")
    c.post("dump", ::dump, auth())
    c.post("restore", ::restore, auth())

    c.get("backup/list", ::listBackupFiles, auth())
    c.post("backup/restore", ::restoreBackupFile, auth())
    c.post("backup/remove", ::remove, auth())
    c.post("backup/now", ::backupNow, auth())
  }

  private fun dump(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw Error403("权限不够，需要管理员")

    SystemKeyEventService.record(SystemKeyEvent(group = "Base", title = "导出数据库"))

    val path = EntityDumpRestoreService.dump()
    ctx.json(mapOf("path" to path))
  }

  private fun restore(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw Error403("权限不够，需要管理员")

    val file = ctx.uploadedFile("f0") ?: throw BzError("errNoUploadedFile", "f0")

    SystemKeyEventService.record(SystemKeyEvent(group = "Base", title = "导入数据库"))

    val tmpDir = FileManager.nextTmpFile()
    try {
      FileHelper.unzipStreamToDir(file.content(), tmpDir)

      val configStr = ctx.formParam("config")
      if (configStr.isNullOrBlank()) throw BzError("errNoConfig")
      val config: DbRestoreConfig = JsonHelper.mapper.readValue(configStr, jacksonTypeRef())

      logger.info("导入数据库。操作人=${op.username}")
      EntityDumpRestoreService.restore(tmpDir, config.clear)
    } finally {
      FileUtils.deleteDirectory(tmpDir)
    }

    ctx.status(200)
  }

  private fun listBackupFiles(ctx: Context) {
    val files = DbBackupManager.listBackupFiles()
    ctx.json(mapOf("files" to files.map { it.name }))
  }

  private fun restoreBackupFile(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw Error403("权限不够，需要管理员")

    val req: RestoreBackUpFileReq = ctx.getReqBody()

    SystemKeyEventService.record(SystemKeyEvent(group = "Base", title = "从数据库备份中恢复：${req.filename}"))

    logger.info("从数据库备份中恢复：${req.filename}。操作人=${op.username}")
    DbBackupManager.restoreByFilename(req.filename)

    ctx.status(200)
  }

  private fun remove(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw Error403("权限不够，需要管理员")

    val req: RemoveBackupFileReq = ctx.getReqBody()
    logger.info("删除数据库备份文件：${req.filenames}")

    val parentDir = DbBackupManager.getBackupDir()
    for (filename in req.filenames) {
      val file = File(parentDir, filename)
      if (!FileHelper.isFileInDir(file, parentDir)) continue
      if (!file.exists()) continue
      logger.info("删除数据库备份：${file.absolutePath}")
      FileUtils.delete(file)
    }

    ctx.status(200)
  }

  private fun backupNow(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw Error403("权限不够，需要管理员")

    DbBackupManager.doBackup()

    ctx.status(200)
  }

}

data class DbRestoreConfig(
  val clear: Boolean = false
)

data class RestoreBackUpFileReq(
  val filename: String
)

data class RemoveBackupFileReq(
  val filenames: List<String>
)