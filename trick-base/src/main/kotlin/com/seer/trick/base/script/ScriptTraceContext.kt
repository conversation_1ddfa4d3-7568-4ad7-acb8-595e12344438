package com.seer.trick.base.script

import com.fasterxml.jackson.core.JacksonException
import com.seer.trick.BzError
import com.seer.trick.base.user.Operator
import com.seer.trick.base.user.PermissionFieldValue
import org.slf4j.Logger
import java.io.IOException

/**
 * 仅为了兼容脚本
 */
class ScriptTraceContext(
  val parent: ScriptTraceContext? = null,
  @JvmField
  var username: String = "",
  @JvmField
  val operatorId: String = "",
  @JvmField
  var roAdmin: Boolean = false,
  var permissions: PermissionFieldValue? = null,
  @JvmField
  val agent: Boolean = false, // 不是人是客户端
  @JvmField
  val anonymous: Boolean = false, // 匿名用户
) {
  
  @JvmField
  val id = nextId()
  
  fun trace(logger: Logger, message: String, error: Throwable? = null) {
    val msg = "[#$id] $message"
    if (error is IOException) {
      logger.trace(msg + " [${error.javaClass.name}] ${error.message}")
    } else {
      logger.trace(msg, error)
    }
  }
  
  fun debug(logger: Logger, message: String, error: Throwable? = null) {
    val msg = "[#$id] $message"
    if (error is IOException) {
      logger.debug(msg + " [${error.javaClass.name}] ${error.message}")
    } else {
      logger.debug(msg, error)
    }
  }
  
  fun info(logger: Logger, message: String, error: Throwable? = null) {
    val msg = "[#$id] $message"
    if (error is IOException) {
      logger.info(msg + " [${error.javaClass.name}] ${error.message}")
    } else {
      logger.info(msg, error)
    }
  }
  
  /**
   * 智能打印异常栈：
   * 0. forcedStackTrace 明确强制打出异常栈
   * 1. BzError 只打印错误消息，不打印异常栈
   * 2. IOException/JacksonException 只打印类型和错误消息，不打印异常栈
   * 3. 其他打印异常栈
   */
  fun warn(logger: Logger, message: String, error: Throwable? = null, forcedStackTrace: Boolean = false) {
    val msg = "[#$id] $message"
    if (forcedStackTrace) {
      logger.error(msg, error)
    } else if (error is IOException && error !is JacksonException) {
      logger.warn(msg + " [${error.javaClass.name}] ${error.message}")
    } else if (error is BzError) {
      logger.warn(msg + "${error.message}")
    } else {
      logger.warn(msg, error)
    }
  }
  
  /**
   * 智能打印异常栈：
   * 0. forcedStackTrace 明确强制打出异常栈
   * 1. BzError 只打印错误消息，不打印异常栈
   * 2. IOException/JacksonException 只打印类型和错误消息，不打印异常栈
   * 3. 其他打印异常栈
   */
  fun error(logger: Logger, message: String, error: Throwable? = null, forcedStackTrace: Boolean = false) {
    val msg = "[#$id] $message"
    if (forcedStackTrace) {
      logger.error(msg, error)
    } else if (error is IOException && error !is JacksonException) {
      logger.error(msg + " [${error.javaClass.name}] ${error.message}")
    } else if (error is BzError) {
      logger.error(msg + "${error.message}")
    } else {
      logger.error(msg, error)
    }
  }
  
  companion object {
    
    const val SYSTEM_USER_ID = "sf09dwa37kk"
    
    private const val FIRST_ID = 100000L
    
    private var idSource: Long = FIRST_ID
    
    @Synchronized
    private fun nextId(): Long {
      if (idSource == Long.MAX_VALUE) {
        idSource = FIRST_ID
      } else {
        idSource++
      }
      return idSource
    }
    
    fun from(op: Operator): ScriptTraceContext {
      return ScriptTraceContext(
        null,
        username = op.username,
        operatorId = op.userId,
        roAdmin = op.admin,
        permissions = op.permissions,
        agent = op.agent,
        anonymous = op.anonymous)
    }
  }
}