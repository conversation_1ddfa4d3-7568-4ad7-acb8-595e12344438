package com.seer.trick.base.db

import com.seer.trick.BzError
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.DbType
import com.seer.trick.base.db.mongo.MongoDumpRestoreManager
import com.seer.trick.base.entity.service.EntityRwService
import java.io.File

/**
 * 对于 SQL 数据库，实体与数据库表不是一一对应的
 */
@Deprecated("临时弃用，改用 EntityDumpRestoreService")
object DbDumpRestoreManager {
  
  @Volatile
  var processing = false
  
  /**
   * 返回文件路径
   */
  fun dump(): String {
    synchronized(this) {
      if (processing) throw BzError("errDbProcessing")
      processing = true
    }
    try {
      val dbConfig = BaseCenter.baseConfig.db
      if (dbConfig.type == DbType.MongoDB) {
        return MongoDumpRestoreManager.dump()
      } else {
        throw UnsupportedOperationException()
      }
    } finally {
      processing = false
    }
  }
  
  fun restore(dir: File, clear: Boolean) {
    synchronized(this) {
      if (processing) throw BzError("errDbProcessing")
      processing = true
    }
    try {
      val dbConfig = BaseCenter.baseConfig.db
      if (dbConfig.type == DbType.MongoDB) {
        MongoDumpRestoreManager.restore(dir, clear)
      } else {
        throw UnsupportedOperationException()
      }
      // 请缓存
      EntityRwService.cache.clearAll()
    } finally {
      processing = false
    }
  }
  
}
