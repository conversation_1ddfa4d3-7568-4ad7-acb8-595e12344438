package com.seer.trick.base.entity.executor.sql

import com.seer.trick.Cq
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.db.DbManager
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.FieldScale
import com.seer.trick.base.entity.FieldType
import com.seer.trick.base.entity.executor.EntityRemoveWorkContext
import com.seer.trick.base.entity.service.FindOptions

object EntityRemoveExecutorSql {

  // 如果带了 includingFields 用于删除关联表，不删主表
  fun execute(
    ctx: EntityRemoveWorkContext, em: EntityMeta, targetIds: List<String>,
    includingFields: List<FieldMeta>? = null
  ) {
    processEntity(ctx, em, targetIds, em.name, includingFields)
    DbManager.getSqlConnection().use { sc ->
      for (tableName in ctx.removing.keys) {
        val query = ctx.removing[tableName] ?: continue
        SqlExecutor.delete(sc, tableName, query)
      }
    }
  }

  private fun processEntity(
    ctx: EntityRemoveWorkContext, em: EntityMeta, targetIds: List<String>, tableName: String,
    includingFields: List<FieldMeta>? = null
  ) {
    val fields: List<FieldMeta>
    if (includingFields == null) {
      ctx.removing[tableName] = Cq.include("id", targetIds)
      fields = em.fields.values.toList()
    } else {
      fields = includingFields
    }
    for (fd in fields) {
      if ((fd.type == FieldType.File || fd.type == FieldType.Image) && fd.scale != FieldScale.Single) {
        val relatedTableName = fd.buildRelatedTableName(tableName)
        ctx.removing[relatedTableName] = Cq.include(FieldMeta.COLUMN_OWNER, targetIds)
      } else if (fd.type == FieldType.Reference && fd.scale != FieldScale.Single) {
        val relatedTableName = fd.buildRelatedTableName(tableName)
        ctx.removing[relatedTableName] = Cq.include(FieldMeta.COLUMN_OWNER, targetIds)
      } else if (fd.type == FieldType.Component) {
        val refComEm = BaseCenter.mustGetRefEntityMeta(fd)
        val componentValues = DbManager.getSqlConnection().use { sc ->
          SqlExecutor.findMany(
            sc, refComEm.name,
            Cq.include(FieldMeta.COLUMN_OWNER, targetIds), FindOptions(projection = listOf("id"))
          )
        }
        val componentIds = componentValues.map { it["id"] as String }
        if (componentIds.isNotEmpty()) {
          processEntity(ctx, refComEm, componentIds, refComEm.name, null)
        }
      } else if (fd.scale != FieldScale.Single) {
        val relatedTableName = fd.buildRelatedTableName(tableName)
        ctx.removing[relatedTableName] = Cq.include(FieldMeta.COLUMN_OWNER, targetIds)
      }
    }
  }
}
