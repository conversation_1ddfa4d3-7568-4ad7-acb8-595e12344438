package com.seer.trick.base.user

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.*
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.helper.JsonHelper
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * 权限管理
 */
object PermissionManager {
  
  private val logger: Logger = LoggerFactory.getLogger(javaClass)
  
  /**
   * 加载用户所有权限，及是否是管理员
   */
  fun loadUserPermissions(op: Operator): Operator {
    val user = EntityRwService.findOneById("HumanUser", op.userId)
      ?: throw BzError("errNoSuchUserById", op.userId)
    
    @Suppress("UNCHECKED_CAST")
    val roleIds = user["roleIds"] as List<String>?
    val roles = if (!roleIds.isNullOrEmpty()) {
      EntityRwService.findMany("UserRole", Cq.include("id", roleIds))
    } else {
      emptyList()
    }
    
    val defaultRoles = EntityRwService.findMany("UserRole", Cq.eq("defaultRole", true))
    val pStrList = mutableListOf<String>()
    for (role in (roles + defaultRoles)) {
      val ps = role["pItems"] as String?
      if (!ps.isNullOrBlank()) pStrList += ps
    }
    val permissions = mergePermissions(pStrList)
    
    return op.copy(
      username = user["username"] as String? ?: "",
      admin = user["roAdmin"] as Boolean? ?: false,
      permissions = permissions
    )
  }
  
  private fun mergePermissions(pStrList: List<String>): PermissionFieldValue {
    val finalP = PermissionFieldValue()
    for (pStr in pStrList) {
      if (pStr.isBlank()) continue
      try {
        mergeOnePermissions(pStr, finalP)
      } catch (e: Exception) {
        logger.error("Parse permissions", e)
      }
    }
    // 多个过滤是或的关系
    for (item in finalP.entityRowObj.values) {
      item.type = ComplexQueryType.Compound
      item.or = true
    }
    
    return finalP
  }
  
  private fun mergeOnePermissions(pStr: String, finalP: PermissionFieldValue) {
    val pObj: PermissionFieldValue = JsonHelper.mapper.readValue(pStr, jacksonTypeRef())
    for (entityName in pObj.entityRow.keys) {
      val rs = pObj.entityRow[entityName]?.trim()
      if (!rs.isNullOrBlank()) {
        val query: ComplexQuery? = if (rs.startsWith("[")) {
          // 兼容
          val queryList: List<ComplexQuery> = JsonHelper.mapper.readValue(rs, jacksonTypeRef())
          if (queryList.isNotEmpty()) {
            // for (queryValue in queryList) {
            // TODO jsonToQuery(roId, em, queryValue)
            // }
            if (queryList.size == 1) queryList[0] else Cq.and(queryList)
          } else {
            null
          }
        } else {
          JsonHelper.mapper.readValue(rs, jacksonTypeRef())
        }
        if (query != null) {
          val fq = finalP.entityRowObj.getOrPut(entityName) { Cq.or(ArrayList()) }
          if (fq.items != null) fq.items!!.add(query)
        }
      }
    }
    
    finalP.actions.addAll(pObj.actions)
    
    for (id in pObj.menu.keys) {
      finalP.menu[id] = pObj.menu[id] ?: false || finalP.menu[id] ?: false
    }
    
    // for ((entityName, byEntity) in pObj.entity) {
    //   val finalForEntity = finalP.entity.getOrPut(entityName) { HashMap() }
    //   for ((forAction, v) in byEntity) {
    //     finalForEntity[forAction] = (finalForEntity[forAction] ?: false) || v
    //   }
    // }
    
    for ((entityName, forEntity) in pObj.entityField) {
      val finalForEntity = finalP.entityField[entityName] ?: HashMap()
      for ((fieldName, forField) in forEntity) {
        val fieldP = finalForEntity[fieldName]
        if (fieldP == EntityFieldPermission.Edit || forField == EntityFieldPermission.Edit) {
          finalForEntity[fieldName] = EntityFieldPermission.Edit
        } else if (fieldP == EntityFieldPermission.Read || forField == EntityFieldPermission.Read) {
          finalForEntity[fieldName] = EntityFieldPermission.Read
        } else if (fieldP == EntityFieldPermission.Disabled || forField == EntityFieldPermission.Disabled) {
          finalForEntity[fieldName] = EntityFieldPermission.Disabled
        } else {
          finalForEntity[fieldName] = EntityFieldPermission.Read
        }
      }
    }
  }
  
  fun pRead(entityMeta: EntityMeta, op: Operator): Boolean {
    val entityName = entityMeta.name
    // 默认可读取的实体
    if (entityName == "HumanUser" ||
      entityName == "UserNotice" ||
      entityName == "ListFilterCase" ||
      entityName == "OrderFlowRecord" ||
      entityName == "EntityComment" ||
      entityName == "Department"
    ) {
      return true
    }
    return userHasEntityPermission(entityMeta.name, "Read", op)
  }
  
  fun pCreate(entityMeta: EntityMeta, op: Operator): Boolean {
    if (entityMeta.actions.createDisabled) return false
    return userHasEntityPermission(entityMeta.name, "Create", op)
  }
  
  fun pEdit(entityMeta: EntityMeta, op: Operator): Boolean {
    if (entityMeta.actions.updateDisabled) return false
    return userHasEntityPermission(entityMeta.name, "Edit", op)
  }
  
  fun pBatchEdit(entityMeta: EntityMeta, op: Operator): Boolean {
    if (entityMeta.actions.batchUpdateDisabled) return false
    return userHasEntityPermission(entityMeta.name, "BatchEdit", op)
  }
  
  fun pRemove(entityMeta: EntityMeta, op: Operator): Boolean {
    if (entityMeta.actions.removeDisabled) return false
    return userHasEntityPermission(entityMeta.name, "Remove", op)
  }
  
  fun pExport(entityMeta: EntityMeta, op: Operator): Boolean {
    if (entityMeta.actions.exportDisabled) return false
    return userHasEntityPermission(entityMeta.name, "Export", op)
  }
  
  fun userHasEntityPermission(entityName: String, action: String, op: Operator): Boolean {
    if (op.admin || op.agent) return true // agent（客户端）暂时不分权
    val ps = op.permissions ?: return false
    
    val pAction = "Entity_$entityName::$action"
    return ps.actions.contains(pAction)
  }
}