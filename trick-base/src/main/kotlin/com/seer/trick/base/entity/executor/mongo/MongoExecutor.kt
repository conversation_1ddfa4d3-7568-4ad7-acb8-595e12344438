package com.seer.trick.base.entity.executor.mongo

import com.mongodb.MongoBulkWriteException
import com.mongodb.MongoWriteException
import com.mongodb.client.AggregateIterable
import com.mongodb.client.model.Accumulators
import com.mongodb.client.model.Aggregates
import com.mongodb.client.model.BsonField
import com.mongodb.client.model.Projections
import com.seer.trick.BzError
import com.seer.trick.ComplexQuery
import com.seer.trick.base.db.DbManager
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.AdvancedField
import com.seer.trick.base.entity.service.AggFun
import com.seer.trick.base.entity.service.AggregationOptions
import com.seer.trick.base.entity.service.ArithmeticOperator
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.helper.JsonHelper
import org.bson.Document
import org.bson.conversions.Bson
import java.util.regex.Pattern

/**
 * 处理单个集合
 */
object MongoExecutor {
  /**
   * 使用业务层对象
   */
  fun findMany(
    
    entityMeta: EntityMeta,
    query: ComplexQuery,
    o: FindOptions? = null,
  ): List<EntityValue> {
    val documents = findMany(
      entityMeta,
      QueryToDocument.queryToDocument(query, entityMeta),
      QueryToDocument.projectionToDocument(o?.projection),
      QueryToDocument.sortToDocument(o?.sort),
      o?.skip,
      o?.limit,
    )
    return documents.map { doc -> MongoValueConverter.documentToEntityValue(doc) }
  }

  /**
   * 使用数据库层对象。
   */
  fun findMany(
    entityMeta: EntityMeta,
    filter: Bson,
    projection: Bson? = null,
    sort: Bson? = null,
    skip: Int? = null,
    limit: Int? = null,
  ): List<Document> {
    val db = DbManager.mustGetMongoDb()
    val collection = db.getCollection(entityMeta.name)
    val c = collection.find(filter)
    if (projection != null) c.projection(projection)
    if (sort != null) c.sort(sort)
    if (skip != null && skip > 0) c.skip(skip)
    if (limit != null && limit > 0) c.limit(limit)
    val r: MutableList<Document> = ArrayList()
    c.forEach { r.add(it) }
    return r
  }

  /**
   * 使用业务层对象
   */
  fun count(entityMeta: EntityMeta, query: ComplexQuery): Long =
    count(entityMeta, QueryToDocument.queryToDocument(query, entityMeta))

  /**
   * 使用数据库层对象。
   */
  fun count(entityMeta: EntityMeta, filter: Bson): Long {
    val db = DbManager.mustGetMongoDb()
    val collection = db.getCollection(entityMeta.name)
    return collection.countDocuments(filter)
  }

  /**
   * 使用业务层对象
   */
  fun insertManyEntityValues(entityMeta: EntityMeta, evList: List<EntityValue>) {
    val documents = evList.map { ev ->
      MongoValueConverter.entityValueToDocument(ev)
    }
    insertManyDocuments(entityMeta, documents)
  }

  /**
   * 使用数据库层对象
   */
  private fun insertManyDocuments(entityMeta: EntityMeta, documents: List<Document>) {
    val db = DbManager.mustGetMongoDb()
    val collection = db.getCollection(entityMeta.name)
    try {
      collection.insertMany(documents)
    } catch (e: Exception) {
      val e2 = checkDuplicatedKeyError(e, entityMeta)
      if (e2 != null) throw e2
      throw e
    }
  }

  /**
   * 使用业务层对象
   */
  fun updateMany(entityMeta: EntityMeta, query: ComplexQuery, update: EntityValue) {
    updateMany(
      entityMeta,
      QueryToDocument.queryToDocument(query, entityMeta),
      MongoValueConverter.entityValueToDocument(update),
    )
  }

  /**
   * 使用数据库层对象
   */
  fun updateMany(entityMeta: EntityMeta, filter: Bson, update: Bson) {
    val db = DbManager.mustGetMongoDb()
    val collection = db.getCollection(entityMeta.name)
    try {
      collection.updateMany(filter, Document(mapOf("\$set" to update, "\$inc" to mapOf(FieldMeta.FIELD_VERSION to 1))))
    } catch (e: Exception) {
      val e2 = checkDuplicatedKeyError(e, entityMeta)
      if (e2 != null) throw e2
      throw e
    }
  }

  /**
   * 使用业务层对象
   */
  fun deleteMany(entityMeta: EntityMeta, query: ComplexQuery) {
    deleteMany(entityMeta, QueryToDocument.queryToDocument(query, entityMeta))
  }

  /**
   * 使用数据库层对象。
   */
  fun deleteMany(entityMeta: EntityMeta, filter: Bson) {
    val db = DbManager.mustGetMongoDb()
    val collection = db.getCollection(entityMeta.name)
    collection.deleteMany(filter)
  }

  // 检查主键冲突
  // Bulk write operation error on server localhost:27017. Write errors: [BulkWriteError{index=0, code=11000, message='
  // E11000 duplicate key error collection: M4Demo2.FbBin index: m4_FbBinRowColumnLayerDepthDistrict
  // dup key: { row: 1, column: 1, layer: 1, depth: 1, district: "" }', details={}}].
  //
  // Bulk write operation error on server localhost:27017. Write errors: [BulkWriteError{index=0, code=11000, message='
  // E11000 duplicate key error collection: M4Demo2.FbBin index: _id_
  // dup key: { _id: "A1-01-1" }', details={}}].
  //
  // 复合唯一索引冲突时，dup key 是此索引所有字段的值
  private fun checkDuplicatedKeyError(e: Throwable, em: EntityMeta): DuplicatedKeyError? = when (e) {
    is MongoBulkWriteException -> {
      parseDuplicatedKeyError(
        e,
        em,
        ".*[\\\\u4E00-\\\\u9FA5A-Za-z0-9_-[\\s].,[{]']*E11000 duplicate key error collection: " +
          "([\\\\u4E00-\\\\u9FA5A-Za-z0-9_-]*).([\\\\u4E00-\\\\u9FA5A-Za-z0-9_-]*) index: " +
          "([\\\\u4E00-\\\\u9FA5A-Za-z0-9_-]*) dup key: [{] ([\\\\u4E00-\\\\u9FA5A-Za-z0-9_-]*): \"" +
          "([\\\\u4E00-\\\\u9FA5A-Za-z0-9_-]*)\" }[\\\\u4E00-\\\\u9FA5A-Za-z0-9_-]*[\\\\u4E00-\\\\u9FA5A-Za-z0-9_-[\\s].,[{][}][\'][\\]]]*.*",
      ) ?: parseDuplicatedCompositeIndexError(
        e,
        em,
        ".*[\\\\u4E00-\\\\u9FA5A-Za-z0-9_-[\\s].,[{]']*E11000 duplicate key error collection: " +
          "([\\\\u4E00-\\\\u9FA5A-Za-z0-9_-]*).([\\\\u4E00-\\\\u9FA5A-Za-z0-9_-]*) index: " +
          "([\\\\u4E00-\\\\u9FA5A-Za-z0-9_-]*) dup key: ([{] [\\\\u4E00-\\\\u9FA5A-Za-z0-9_-[\\s],.\"]* })[\',]*.*",
      )
    }

    is MongoWriteException -> {
      parseDuplicatedKeyError(
        e,
        em,
        (
          ".*E11000 duplicate key error collection: ([\\\\u4E00-\\\\u9FA5A-Za-z0-9_-]*)." +
            "([\\\\u4E00-\\\\u9FA5A-Za-z0-9_-]*) index: ([\\\\u4E00-\\\\u9FA5A-Za-z0-9_-]*) dup key: " +
            "[{] ([\\\\u4E00-\\\\u9FA5A-Za-z0-9_-]*): \"([\\\\u4E00-\\\\u9FA5A-Za-z0-9_-]*)\" }.*"
          ),
      )
    }

    else -> {
      null
    }
  }

  private fun parseDuplicatedKeyError(error: Throwable, em: EntityMeta, patternStr: String): DuplicatedKeyError? {
    val errMsg = error.message ?: return null
    val duplicateIndexMatcher = Pattern.compile(patternStr).matcher(errMsg)
    if (!duplicateIndexMatcher.matches()) return null

    // val index = duplicateIndexMatcher.group(3)
    var fieldName = duplicateIndexMatcher.group(4)
    if (fieldName == "_id") fieldName = "id"
    val value = duplicateIndexMatcher.group(5)
    return DuplicatedKeyError(
      entityName = em.name,
      entityLabel = em.label,
      fieldName = fieldName,
      fieldLabel = em.fields[fieldName]?.label ?: "",
      value = value,
    )
  }

  // 解析复合索引的错误字段、值
  // 先把 dup key 取出来，然后二次解析，取字段名、值
  private fun parseDuplicatedCompositeIndexError(
    error: Throwable,
    em: EntityMeta,
    patternStr: String,
  ): DuplicatedKeyError? {
    val errMsg = error.message ?: return null
    val duplicateIndexMatcher = Pattern.compile(patternStr).matcher(errMsg)
    if (!duplicateIndexMatcher.matches()) return null

    // 二次匹配拆出 json 中所有字段
    val index = duplicateIndexMatcher.group(4)

    val fieldNameList = mutableListOf<String>()
    val valueList = mutableListOf<Any>()
    val matcher = Pattern.compile(
      "([\\\\u4E00-\\\\u9FA5A-Za-z0-9_-]*)[:][\\s][\"]?([\\\\u4E00-\\\\u9FA5A-Za-z0-9_-]*)[\"]?",
    ).matcher(index)
    while (matcher.find()) {
      fieldNameList.add(matcher.group(1))
      valueList.add(matcher.group(2))
    }

    val fieldNameList2 = fieldNameList.map { if (it == "_id") "id" else it }
    val fieldName = fieldNameList2.joinToString(",")
    val fieldLabel = fieldNameList2.joinToString(",") { em.fields[ it]?.label ?: "" }
    val values = valueList.joinToString(",")
    return DuplicatedKeyError(
      entityName = em.name,
      entityLabel = em.label,
      fieldName = fieldName,
      fieldLabel = fieldLabel,
      value = values,
    )
  }

  fun aggregateQuery(
    
    entityMeta: EntityMeta,
    query: ComplexQuery,
    ao: AggregationOptions,
  ): List<EntityValue> {
    val projectionList: MutableList<Bson> = ArrayList()
    val groupMap: MutableMap<String, Any> = HashMap()
    val accumulatorMap: MutableMap<String, BsonField> = HashMap()
    for (item in ao.groupBy) {
      val alias = item.alias ?: item.name
      if (item.statisticDateType == null) {
        val name = if (item.name == "id") "_id" else item.name
        groupMap[alias] = "\$$name"
      } else {
        groupMap[alias] = Document(
          "\$dateToString",
          Document(mapOf("format" to item.statisticDateType.mongoDateFormat, "date" to "\$${item.name}")),
        )
      }
      projectionList += Projections.computed(alias, "\$_id.$alias")
    }

    for (field in ao.fields) {
      // 处理查询的字段
      addToGroupMap(field, groupMap)
      // 处理复杂组合的字段
      addToAccumulatorMap(field, accumulatorMap)
      val alias = field.alias ?: field.name
      projectionList += Document(alias, buildArithmeticBson(field))
    }

    val sortMap: MutableMap<String, Any> = HashMap()
    for (name in ao.sort) {
      val fl = name[0]
      val field = if (fl == '+' || fl == '-') name.substring(1) else name
      val order = if (fl == '-') -1 else 1
      sortMap[field] = order
    }

    val filters = QueryToDocument.queryToDocument(query, entityMeta)
    val groupDoc = if (groupMap.isNotEmpty()) Document(groupMap) else Document()
    // 去除 _id 对象
    projectionList += Projections.excludeId()
    // 创建聚合管道 先过滤再分组再排序
    val pipeline = mutableListOf(
      Aggregates.match(filters), // 过滤
      Aggregates.group(groupDoc, accumulatorMap.values.toList()),
      Aggregates.project(Projections.fields(projectionList)),
    )
    if (sortMap.isNotEmpty()) {
      pipeline += Aggregates.sort(Document(sortMap))
    }
    val db = DbManager.mustGetMongoDb()
    val collection = db.getCollection(entityMeta.name)
    val res: AggregateIterable<Document> = try {
      collection.aggregate(pipeline)
    } catch (e: Exception) {
      throw BzError("errAggregateQuery", e.message)
    }

    val entityList: MutableList<EntityValue> = ArrayList()
    res.forEach {
      entityList += MongoValueConverter.documentToEntityValue(it)
    }
    return entityList
  }

  private fun addToGroupMap(field: Any, groupMap: MutableMap<String, Any>) {
    when (field) {
      is String -> if (!groupMap.containsKey(field)) groupMap[field] = "\$$field"
      is AdvancedField -> {
        if (field.function == null) {
          val name = if (field.name == "id") "_id" else field.name
          val alias = field.alias ?: field.name
          if (!groupMap.containsKey(alias)) groupMap[alias] = "\$$name"
        }
        for (right in field.rightFields) {
          if (right is AdvancedField) {
            addToGroupMap(right, groupMap)
          }
        }
      }
    }
  }

  private fun addToAccumulatorMap(aggField: AdvancedField, accumulatorMap: MutableMap<String, BsonField>) {
    if (aggField.function != null) {
      val aggAlias = aggField.function.name + aggField.name
      if (!accumulatorMap.containsKey(aggAlias)) {
        accumulatorMap[aggAlias] = when (aggField.function) {
          AggFun.COUNT -> Accumulators.sum(aggAlias, 1)
          AggFun.SUM -> Accumulators.sum(aggAlias, "\$${aggField.name}")
          AggFun.AVG -> Accumulators.avg(aggAlias, "\$${aggField.name}")
          AggFun.MAX -> Accumulators.max(aggAlias, "\$${aggField.name}")
          AggFun.MIN -> Accumulators.min(aggAlias, "\$${aggField.name}")
        }
      }
    }
    for (right in aggField.rightFields) {
      if (right is AdvancedField) {
        addToAccumulatorMap(right, accumulatorMap)
      }
    }
  }

  private fun buildArithmeticBson(field: AdvancedField): Any {
    var leftOp: Any = if (field.function != null) {
      "\$${field.function.name + field.name}"
    } else {
      val alias = field.alias ?: field.name
      "\$_id.$alias"
    }

    if (field.operators.isNotEmpty()) {
      for ((index, operator) in field.operators.withIndex()) {
        val op = when (operator) {
          ArithmeticOperator.Add -> "\$add"
          ArithmeticOperator.Subtract -> "\$subtract"
          ArithmeticOperator.Multiply -> "\$multiply"
          ArithmeticOperator.Divide -> "\$divide"
        }
        var right = field.rightFields[index]
        // 解析 json 文件时 right 会被解析为 LinkedHashMap，所以这里需要转换一下
        if (right is Map<*, *>) right = JsonHelper.mapper.convertValue(right, AdvancedField::class.java)
        leftOp = when (right) {
          is String -> Document(op, listOf(leftOp, "\$$right"))
          is Number -> Document(op, listOf(leftOp, right))
          is AdvancedField -> Document(op, listOf(leftOp, buildArithmeticBson(right)))
          else -> {
            throw BzError("errUnsupportedDataType", right::class.simpleName)
          }
        }
      }
    }

    return leftOp
  }

  // 检查修改不可变字段
  // private fun checkModifyImmutableFieldError(e: Throwable, entityMeta: NsEntityMeta) {
  //   val err: DuplicatedKeyError?
  //   if (e is MongoWriteException) {
  //     err = parseModifyImmutableFieldError(
  //       e, entityMeta,
  //       "Performing an update on the path '([\\\\u4E00-\\\\u9FA5A-Za-z0-9_-]*)' would modify the immutable field '([\\\\u4E00-\\\\u9FA5A-Za-z0-9_-]*)'"
  //     )
  //   } else {
  //     return
  //   }
  //   throw BzError.duplicatedKey(err)
  // }

  // private fun parseModifyImmutableFieldError(
  //   error: Throwable, entityMeta: NsEntityMeta,
  //   patternStr: String
  // ): DuplicatedKeyError? {
  //   val errMsg = ObjectUtils.firstNonNull(error.message, "")
  //   val modifyImmutableFieldMatcher = getMatcher(patternStr, errMsg)
  //   if (modifyImmutableFieldMatcher.matches()) {
  //     val fieldName = modifyImmutableFieldMatcher.group(2)
  //     com.seer.meta.store.MongoManager.logger.error(
  //       "Entity field [{}:{}] is read-only and cannot be modified",
  //       entityMeta.getName(),
  //       fieldName
  //     )
  //     val e = DuplicatedKeyError()
  //     e.setCollectionName(ObjectUtils.firstNonNull(if (database != null) database.getName() else "", ""))
  //     e.setEntityName(entityMeta.getName())
  //     e.setEntityLabel(entityMeta.getLabel())
  //     e.setFieldName(fieldName)
  //     e.setFieldLabel(
  //       ListHelper.find(entityMeta.getFields()) { fm -> Objects.equals(fm.getName(), fieldName) }.getLabel()
  //     )
  //     e.setValue("")
  //     return e
  //   }
  //   return null
  // }
}

class DuplicatedKeyError(
  val entityName: String? = null,
  val entityLabel: String? = null,
  val fieldName: String? = null,
  val fieldLabel: String? = null,
  val value: String? = null,
) : BzError("errDuplicatedKeyError", entityLabel, fieldLabel, value)