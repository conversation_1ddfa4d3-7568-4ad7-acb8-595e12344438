package com.seer.trick.base.soc

import com.seer.trick.helper.DateHelper
import com.seer.trick.helper.JsonHelper
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.ConcurrentHashMap

/**
 * 系统观测
 */
object SysMonitorService {

  private val logger = LoggerFactory.getLogger("SysMon")

  private val nodeMap: MutableMap<String, SysMonitorNode> = ConcurrentHashMap()

  private val subjects: MutableSet<String> = Collections.synchronizedSet(HashSet())

  const val TIMESTAMP_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"

  fun clear() {
    nodeMap.clear()
  }

  fun list(): Collection<SysMonitorNode> = nodeMap.values

  fun log(
    subject: String, // 主题
    target: String, // 目标，如机器人名
    field: String, // 字段
    value: String, // 内容
    remove: Boolean = false,
    level: SysMonitorLevel = SysMonitorLevel.Debug,
    instant: Boolean = false, // 不记录日志文件
  ) {
    if (subject.contains("|")) throw IllegalArgumentException("Bad 'subject'")
    if (target.contains("|")) throw IllegalArgumentException("Bad 'target'")
    if (field.contains("|")) throw IllegalArgumentException("Bad 'field'")

    val now = Date()

    if (!instant) {
      val nowStr = DateHelper.formatDate(now, TIMESTAMP_FORMAT)
      val t = Thread.currentThread().name
      val log = "$nowStr|$t|${level.name}|$subject|$target|$field|$value"

      when (level) {
        SysMonitorLevel.Debug -> logger.debug(log)
        SysMonitorLevel.Info -> logger.info(log)
        SysMonitorLevel.Warn -> logger.warn(log)
        SysMonitorLevel.Error -> logger.error(log)
      }
    }

    subjects += subject

    val id = getId(subject, target, field)
    if (remove) {
      nodeMap.remove(id)
    } else {
      nodeMap[id] = SysMonitorNode(now, subject, target, field, value, level)
    }
  }

  private fun getId(subject: String, target: String, field: String) = "$subject:$target:$field"

  fun serialize(): String? = JsonHelper.mapper.writeValueAsString(nodeMap.values)

  data class SysMonitorNode(
    val timestamp: Date,
    val subject: String,
    val target: String,
    val field: String,
    val value: String,
    val level: SysMonitorLevel = SysMonitorLevel.Debug,
  )

  enum class SysMonitorLevel {
    Debug,
    Info,
    Warn,
    Error,
  }
}