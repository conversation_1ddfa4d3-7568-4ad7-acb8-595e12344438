package com.seer.trick.base.http.handler

import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.getReqBody
import io.javalin.http.Context

object EntityCommentHandler {
  
  fun registerHandlers() {
    val c = Handlers("api/entity-comment")
    c.post("create", ::createEntityComment, auth())
    c.post("update", ::updateEntityComment, auth())
    c.post("delete/{id}", ::deleteEntityComment, auth())
  }
  
  private fun createEntityComment(ctx: Context) {
    val req: EntityCommentCreateReq = ctx.getReqBody()
    
    val ev: EntityValue = mutableMapOf(
      "entityName" to req.entityName, "entityId" to req.entityId, "content" to req.content
    )
    
    EntityRwService.createOne("EntityComment", ev)
    
    // TODO
    // setImmediate(() => notifyEntityComment(req.entityName, req.entityId, ev, "评论"))
    
    ctx.status(200)
  }
  
  private fun updateEntityComment(ctx: Context) {
    val req: EntityCommentUpdateReq = ctx.getReqBody()
    
    val ev: EntityValue = mutableMapOf("content" to req.content)
    
    val old = EntityRwService.findOne("EntityComment", Cq.idEq(req.commentId))
    if (old != null) {
      EntityRwService.updateOne("EntityComment", Cq.idEq(req.commentId), ev)
      // TODO setImmediate(() => notifyEntityComment(old.entityName, old.entityId, old, "修改了评论"))
    }
    
    ctx.status(200)
  }
  
  private fun deleteEntityComment(ctx: Context) {
    val id = ctx.pathParam("id")
    
    val old = EntityRwService.findOne("EntityComment", Cq.idEq(id))
    if (old != null) {
      EntityRwService.removeOne("EntityComment", Cq.idEq(id))
      
      // TODO setImmediate(() => notifyEntityComment(old.entityName, old.entityId, old, "删除了评论"))
    }
    
    ctx.status(200)
  }
  
}

data class EntityCommentCreateReq(
  val entityName: String,
  val entityId: String,
  val content: String,
)

data class EntityCommentUpdateReq(
  val commentId: String,
  val content: String
)


