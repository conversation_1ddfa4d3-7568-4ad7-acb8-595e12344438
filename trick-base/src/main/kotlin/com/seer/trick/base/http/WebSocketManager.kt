package com.seer.trick.base.http

import com.seer.trick.BzError
import com.seer.trick.base.SysEmc
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.base.soc.SocService
import com.seer.trick.base.soc.SysMonitorService
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.getTypeMessage
import io.javalin.websocket.*
import org.eclipse.jetty.websocket.api.exceptions.WebSocketTimeoutException
import org.slf4j.LoggerFactory
import java.io.IOException
import java.util.*
import java.util.concurrent.*

object WebSocketManager {

  private val logger = LoggerFactory.getLogger(javaClass)

  private val msgExecutor = ThreadPoolExecutor(10, 100, 10L, TimeUnit.SECONDS, LinkedBlockingQueue())

  private val clients: MutableMap<String, WsClient> = ConcurrentHashMap() // session id ->

  private val sessionIdToEv: MutableMap<String, WsEnv> = ConcurrentHashMap() // session id ->

  val subscribers: MutableList<WebSocketSubscriber> = CopyOnWriteArrayList()

  val tagUiSet = setOf(WsTag.Ui)

  @Volatile
  private var connectCount = 0L

  @Volatile
  private var msgCount = 0L

  fun handleWebSocket(ws: WsConfig) {
    ws.onConnect { ctx ->
      logger.debug(
        "New connection from URL ${ctx.session.upgradeRequest.requestURI}, session=${ctx.sessionId}, " +
          "remote=${ctx.session.remoteAddress}",
      )
      clients[ctx.sessionId] = WsClient(ctx)
      for (s in subscribers) s.onConnect(ctx)

      updateConnectCount()
      updateSoc()
    }
    ws.onClose { ctx ->
      // logger.info("WebSocket 连接被关闭，session id=${ctx.sessionId}")
      onClose(ctx)
      updateSoc()
    }
    ws.onError { ctx ->
      onError(ctx)
      updateSoc()
    }

    ws.onMessage(this::onMessage)

    ws.onBinaryMessage(this::onBinaryMessage)
  }

  private fun onBinaryMessage(wsBinaryMessageContext: WsBinaryMessageContext) {
    // logger.warn("收到了二进制消息（不支持） {}", wsBinaryMessageContext)
  }

  /**
   * 非阻塞。不抛异常。
   * // TODO 仍然未限速
   */
  @Synchronized
  fun sendAllAsync(msg: WsMsg, @Suppress("UNUSED_PARAMETER") filterTags: Set<WsTag>? = null) {
    val badSessionIds = mutableSetOf<String>()
    try {
      for (client in clients.values) {
        // val env = sessionIdToEv[ctx.sessionId]
        // if (filterTags != null) {
        // if (env == null) continue
        // if (env.tags.intersect(filterTags).isEmpty()) continue // TODO
        // }

        if (!client.ctx.session.isOpen) {
          badSessionIds += client.ctx.sessionId
          continue
        }
        msgExecutor.submit { sendToClient(client, msg) }
      }
      if (badSessionIds.isNotEmpty()) {
        for (id in badSessionIds) {
          clients.remove(id)
          sessionIdToEv.remove(id)
        }
      }
    } catch (e: Exception) {
      // 并发访问异常？
      logger.error("sendAllAsync", e)
    }

    updateSoc()
  }

  private fun sendToClient(client: WsClient, msg: WsMsg) {
    if (!client.ctx.session.isOpen) {
      clients.remove(client.ctx.sessionId)
      sessionIdToEv.remove(client.ctx.sessionId)
      return
    }
    try {
      client.ctx.send(msg)
    } catch (e: IOException) {
      logger.error("Failed to send [${msg.action}] to client ${client.ctx.sessionId}. Error=${e.getTypeMessage()}")
      onSendError(client.ctx)
    } catch (e: BzError) {
      logger.error("Failed to send [${msg.action}] to client ${client.ctx.sessionId}. Error=${e.getTypeMessage()}")
      onSendError(client.ctx)
    } catch (e: Exception) {
      logger.error("Failed to send [${msg.action}] to client ${client.ctx.sessionId}. ", e)
      onSendError(client.ctx)
    }
  }

  private fun onMessage(ctx: WsMessageContext) {
    updateMsgCount()

    val msg = ctx.messageAsClass(WsMsg::class.java)

    if (msg.action == "BzPing") {
      // 实现业务 ping/pong
      msgExecutor.submit {
        ctx.send(WsMsg("BzPong", id = IdHelper.oidStr(), replyToId = msg.id))
      }
    } else if (msg.action == "SysEmc::Query") {
      msgExecutor.submit {
        ctx.send(
          WsMsg.json(
            "SysEmc::Reply",
            mapOf("enabled" to SysEmc.isSysEmc()),
            id = IdHelper.oidStr(),
            replyToId = msg.id,
          ),
        )
      }
    } else if (msg.action == "AlarmsNotice::Query") {
      // 所有告警、故障的消息
      msgExecutor.submit {
        val notice = mapOf(
          "groups" to AlarmService.listGroups(),
          "items" to AlarmService.listItems(),
        )
        ctx.send(WsMsg.json("AlarmsNotice::Reply", notice))
      }
    } else {
      val env = sessionIdToEv.getOrPut(ctx.sessionId) { WsEnv(ctx.sessionId) }

      when (msg.action) {
        "UiRegister" -> env.tags += WsTag.Ui
        "GwWsClientRegister" -> env.tags += WsTag.Gw
      }

      for (s in subscribers) {
        msgExecutor.submit {
          try {
            s.onMessage(ctx, msg, env)
          } catch (e: BzError) {
            logger.error("WebSocket on message [${msg.action}]: " + e.message)
          } catch (e: IOException) {
            // 先 info 级别
            logger.info("WebSocket on message [${msg.action}]: " + e.getTypeMessage())
          } catch (e: Exception) {
            logger.error("WebSocket on message [${msg.action}]: ", e)
          }
        }
      }
    }

    updateSoc()
  }

  @Synchronized
  fun dispose() {
    for (client in clients.values) {
      try {
        client.ctx.closeSession()
      } catch (e: Exception) {
        // ignore
      }
    }
    clients.clear()
    updateSoc()
  }

  private fun clearClient(ctx: WsContext) {
    val client = clients.remove(ctx.sessionId)
    if (client == null) {
      // logger.warn("删除 WS 客户端，没找到 session id =${ctx.sessionId}")
    } else {
      try {
        client.ctx.closeSession()
      } catch (e: Exception) {
        //
      }
    }
    sessionIdToEv.remove(ctx.sessionId)
    for (s in subscribers) s.onCloseOrError(ctx)
  }

  private fun onError(ctx: WsErrorContext) {
    if (ctx.error() !is WebSocketTimeoutException) {
      logger.debug(
        "Close client for error. ${ctx.sessionId}. " +
          "remote=${ctx.session.remoteAddress}. Error=${ctx.error()?.getTypeMessage()}",
      )
    }
    clearClient(ctx)
  }

  private fun onSendError(ctx: WsContext) {
    logger.debug("Close client for send error。 ${ctx.sessionId}. remote=${ctx.session.remoteAddress}")
    clearClient(ctx)
  }

  private fun onClose(ctx: WsCloseContext) {
    logger.debug(
      "Close client for close event. ${ctx.sessionId}. " +
        "remote=${ctx.session.remoteAddress}. reason=${ctx.reason()}",
    )
    clearClient(ctx)
  }

  private fun updateSoc() {
    SysMonitorService.log(
      subject = "Network",
      target = "WebsocketServer",
      field = "all",
      value = "client num: ${clients.size}, connection num: $connectCount, message num: $msgCount",
      instant = true,
    )

    SocService.updateNode(
      "网络",
      "WS::Server",
      "WebSocket 服务器统计",
      "客户端数量=${clients.size}。连接请求数=$connectCount。接收消息数=$msgCount",
    )
  }

  @Synchronized
  private fun updateConnectCount() {
    if (connectCount >= Long.MAX_VALUE) connectCount = 0
    ++connectCount
  }

  @Synchronized
  private fun updateMsgCount() {
    if (msgCount >= Long.MAX_VALUE) msgCount = 0
    ++msgCount
  }
}

class WsClient(val ctx: WsContext) {

  override fun toString(): String = "WsClient [${ctx.sessionId}]"
}

abstract class WebSocketSubscriber {

  open fun onConnect(ctx: WsConnectContext) {}
  open fun onCloseOrError(ctx: WsContext) {}

  /**
   * 此方法会被异步调用
   */
  open fun onMessage(ctx: WsMessageContext, msg: WsMsg, env: WsEnv) {}
}

/**
 * 标识 WS 连接的一些特性
 */
data class WsEnv(val sessionId: String, val tags: MutableSet<WsTag> = Collections.synchronizedSet(HashSet()))

enum class WsTag {
  Ui, // UI 界面
  Ext, // 扩展程序
  Gw, // Gateway
}