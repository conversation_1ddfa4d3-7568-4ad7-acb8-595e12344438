package com.seer.trick.base.entity.service.change

import com.seer.trick.base.entity.EntityHelper.findById
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.http.WebSocketManager
import com.seer.trick.base.http.WebSocketManager.tagUiSet
import com.seer.trick.base.http.WsMsg
import java.util.concurrent.CopyOnWriteArrayList

class EntityChange(val id: String, val oldValue: EntityValue?, val newValue: EntityValue?)

class EntityChangeEvent(
  val entityName: String,
  val created: Boolean = false,
  val createdList: List<EntityChange>? = null,
  val updated: Boolean = false,
  val updatedList: List<EntityChange>? = null,
  val removed: Boolean = false,
  val removedList: List<EntityChange>? = null,
)

class EntityChangeListener(
  val id: String,
  val option: EntityChangeListenerOption,
  val callback: EntityChangeListenerFunc,
)

fun interface EntityChangeListenerFunc {
  fun on(event: EntityChangeEvent)
}

class EntityChangeListenerOption(val needId: Boolean, val needOldValue: Boolean, val needNewValue: Boolean) {
  companion object {
    val NeedAll = EntityChangeListenerOption(needId = true, needOldValue = true, needNewValue = true)
    val NeedNone = EntityChangeListenerOption(needId = false, needOldValue = false, needNewValue = false)
  }
}

class EntityChangeManager(private val entityName: String) {

  private val listeners: MutableList<EntityChangeListener> = CopyOnWriteArrayList()

  @Volatile
  var finalOption = EntityChangeListenerOption.NeedNone

  fun on(listener: EntityChangeListener) {
    listeners.add(listener)
    calcFinalOption()
  }

  fun off(id: String) {
    listeners.removeIf { l: EntityChangeListener -> l.id == id }
    calcFinalOption()
  }

  private fun calcFinalOption() {
    var needId = false
    var needOldValue = false
    var needNewValue = false
    for (listener in listeners) {
      needId = needId || listener.option.needId
      needOldValue = needOldValue || listener.option.needOldValue
      needNewValue = needNewValue || listener.option.needNewValue
    }
    finalOption = EntityChangeListenerOption(needId, needOldValue, needNewValue)
  }

  fun fireCreatedOne(id: String, nv: EntityValue?) {
    fire(EntityChangeEvent(entityName, created = true, createdList = listOf(EntityChange(id, null, nv))))
  }

  fun fireCreatedMany(ids: List<String>, nvList: List<EntityValue>?) {
    val changed = ids.map { id -> EntityChange(id, null, findById(nvList, id)) }
    fire(EntityChangeEvent(entityName, created = true, createdList = changed))
  }

  fun fireUpdated(
    targetIds: List<String>,
    oldValues: List<EntityValue>?,
    newValues: List<EntityValue>?,
  ) {
    val changed = targetIds.map { id -> EntityChange(id, findById(oldValues, id), findById(newValues, id)) }
    fire(EntityChangeEvent(entityName, updated = true, updatedList = changed))
  }

  fun fireRemoved(ids: List<String>, oldValues: List<EntityValue>?) {
    val changed = ids.map { id -> EntityChange(id, findById(oldValues, id), null) }
    fire(EntityChangeEvent(entityName, removed = true, removedList = changed))
  }

  /**
   * 同步通知
   */
  private fun fire(event: EntityChangeEvent) {
    if (listeners.isEmpty()) return
    for (listener in listeners) {
      listener.callback.on(event)
    }
    // val ed = rr.mustGetEntityMeta(entityName)
    val changed = mapOf(
      "entityName" to entityName,
      "created" to event.created,
      "updated" to event.updated,
      "removed" to event.removed,
    )
    WebSocketManager.sendAllAsync(WsMsg.json("EntityChanged", changed), tagUiSet)
  }
}