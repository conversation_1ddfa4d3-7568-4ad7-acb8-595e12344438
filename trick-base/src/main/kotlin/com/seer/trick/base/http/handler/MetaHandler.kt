package com.seer.trick.base.http.handler

import com.seer.trick.BzError
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.db.DbManager
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityMetaManager
import com.seer.trick.base.file.FileManager
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.getReqBody
import com.seer.trick.base.http.operator
import com.seer.trick.helper.JsonFileHelper
import io.javalin.http.Context
import org.slf4j.LoggerFactory

object MetaHandler {

  private val logger = LoggerFactory.getLogger(this::class.java)

  fun registerHandlers() {
    val c = Handlers("api/meta")
    c.get("entities", ::getEntityMap, auth())
    c.get("entity/{entityName}", ::getEntityMeta, auth())
    c.post("entity", ::saveEntityMeta, auth())
    c.post("entities", ::saveManyEntityMeta, auth())
    c.post("remove-entities", ::removeEntityMeta, auth())

    c.get("entities/export", ::downloadEntityMeta, auth())
    c.post("reset", ::resetMeta, auth())
    c.post("reset-builtin", ::resetBuiltinEntityMeta, auth())

    c.post("sync-db-schema", ::syncDbSchema, auth())
  }

  private fun syncDbSchema(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw Error403("Admin required")

    try {
      DbManager.syncForced()
    } catch (e: Exception) {
      logger.error("同步失败", e)
      throw BzError("errCodeErr", e.message)
    }

    ctx.status(200)
  }

  private fun getEntityMap(ctx: Context) {
    ctx.json(BaseCenter.entityMetaMap)
  }

  private fun getEntityMeta(ctx: Context) {
    val entityName = ctx.pathParam("entityName")

    ctx.json(BaseCenter.mustGetEntityMeta(entityName))
  }

  private fun saveEntityMeta(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw Error403("Admin required")

    val em: EntityMeta = ctx.getReqBody()
    EntityMetaManager.saveEntityMeta(em)

    ctx.status(200)
  }

  private fun saveManyEntityMeta(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw Error403("Admin required")

    val emList: List<EntityMeta> = ctx.getReqBody()
    for (em in emList) EntityMetaManager.saveEntityMeta(em)

    ctx.status(200)
  }

  private fun removeEntityMeta(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw Error403("Admin required")

    val req: RemoveEntityMetaReq = ctx.getReqBody()
    EntityMetaManager.removeEntityMeta(req.entityNames)

    ctx.status(200)
  }


  private fun downloadEntityMeta(ctx: Context) {
    val jsonFile = FileManager.nextTmpFile("json")
    JsonFileHelper.writeJsonToFile(jsonFile, BaseCenter.entityMetaMap, true)

    ctx.json(mapOf("path" to FileManager.fileToPath(jsonFile)))
  }

  private fun resetMeta(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw Error403("Admin required")

    EntityMetaManager.resetEntityMeta()

    ctx.status(200)
  }

  /**
   * 将所有内建业务对象恢复到默认状态
   */
  private fun resetBuiltinEntityMeta(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw Error403("Admin required")

    EntityMetaManager.resetBuiltinEntityMeta()

    ctx.status(200)
  }

}

data class RemoveEntityMetaReq(
  val entityNames: List<String> = emptyList()
)