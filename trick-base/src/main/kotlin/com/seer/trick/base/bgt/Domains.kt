package com.seer.trick.base.bgt

import com.fasterxml.jackson.module.kotlin.readValue
import com.seer.trick.BzError
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.helper.JsonHelper
import java.util.concurrent.Future

/**
 * 后台任务的运行时表示
 */
class BgTaskRuntime(
  @JvmField
  val id: String,
  @JvmField
  val ev: EntityValue,
) {

  @Volatile
  var future: Future<*>? = null

  @JvmField
  val taskName = ev["name"] as String

  /**
   * 任务输入参数，静态的，只解析一次
   */
  @JvmField
  val args: Map<String, Any?>? = if (ev["args"] != null) JsonHelper.mapper.readValue(ev["args"] as String) else null

  fun throwIfAborted() {
    if (ev["aborted"] == true) throw BgTaskAbortedException()
  }

  fun suspendIfPaused() {
    while (ev["paused"] == true) {
      Thread.sleep(100)
      if (ev["aborted"] == true) throw BgTaskAbortedException()
    }
  }
}

typealias BgTaskExecutor = (tr: BgTaskRuntime) -> Unit

class BgTaskAbortedException : BzError("errCodeErr", "Task aborted")