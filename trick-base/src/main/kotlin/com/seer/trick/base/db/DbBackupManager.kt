package com.seer.trick.base.db

import com.seer.trick.BzError
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.entity.service.EntityDumpRestoreService
import com.seer.trick.base.event.EventListener
import com.seer.trick.base.file.FileManager
import com.seer.trick.helper.DateHelper
import com.seer.trick.helper.FileHelper
import com.seer.trick.helper.NumHelper
import org.apache.commons.io.FileUtils
import org.apache.commons.lang3.time.DateUtils
import org.quartz.*
import org.quartz.impl.StdSchedulerFactory
import org.slf4j.LoggerFactory
import java.io.File
import java.util.*
import java.util.Calendar

object DbBackupManager : EventListener<String> {

  private val logger = LoggerFactory.getLogger(this::class.java)

  private const val FILE_PREFIX = "db-backup-"

  // TODO Scheduler 本身是线程安全的吗？
  private val scheduler: Scheduler = StdSchedulerFactory().scheduler

  @Volatile
  private var triggerKey: TriggerKey? = null

  @Volatile
  private var lastJob: JobDetail? = null

  @Volatile
  private var lastEnabled: Boolean = false

  init {
    BzConfigManager.eventBus.listeners += this
  }

  fun init() {
    initializeScheduler()
  }

  private fun initializeScheduler() {
    val enabled = BzConfigManager.getByPathAsBoolean("ScData", "backup", "enabled")
    if (!enabled) return
    lastEnabled = true
    scheduler.scheduleJob(buildJob(), buildTrigger())
    scheduler.start()
    logger.info("定时任务初始化完成")
  }

  private fun buildJob(): JobDetail? {
    val jd = JobBuilder
      .newJob(DbBackupJob::class.java)
      .withIdentity("DbBackupJob", "baseJobGroup")
      .build()
    lastJob = jd
    return jd
  }

  /**
   * dispose 后不会再被使用
   */
  fun dispose() {
    scheduler.shutdown()
  }

  override fun onEvent(e: String) {
    val enabled = BzConfigManager.getByPathAsBoolean("ScData", "backup", "enabled")

    if (!enabled) {
      logger.info("停止定时器")
      // 停止
      val jd = lastJob
      lastEnabled = false
      if (jd != null) scheduler.deleteJob(jd.key)
    } else {
      if (!lastEnabled || triggerKey == null) {
        logger.info("重新启动定时器")
        scheduler.scheduleJob(buildJob(), buildTrigger())
        scheduler.start()
      } else {
        logger.info("重新配置定时器")
        scheduler.rescheduleJob(triggerKey, buildTrigger())
      }
      lastEnabled = true
    }
  }

  private fun buildTrigger(): CronTrigger {
    val cronStr = BzConfigManager.getByPath("ScData", "backup", "cron") as String? ?: "0 0 2 * * ?"

    val trigger = TriggerBuilder.newTrigger()
      .withIdentity("DbBackupTrigger(${cronStr})", "baseTriggerGroup")
      .startNow()
      .withSchedule(CronScheduleBuilder.cronSchedule(cronStr))
      .build()
    triggerKey = trigger.key
    return trigger
  }

  fun listBackupFiles(): List<File> {
    val dir = getBackupDir()
    return dir.listFiles()?.filter { it.name.startsWith(FILE_PREFIX) }?.toList() ?: emptyList()
  }

  fun getBackupDir(): File {
    val dirStr = BzConfigManager.getByPath("ScData", "backup", "dir") as String? ?: ""
    return if (dirStr.isNotBlank()) {
      File(dirStr)
    } else {
      File(BaseCenter.baseConfig.projectDir, "backup")
    }
  }

  fun restoreByFilename(filename: String) {
    val dir = getBackupDir()
    val zipFile = File(dir, filename)
    if (!zipFile.exists()) throw BzError("errFileNotExists", zipFile.absolutePath)
    if (FileHelper.isFileInDir(File(filename), dir))
      throw BzError("errFileNotInDir", zipFile.absolutePath, dir.absolutePath)

    val tmpDir = FileManager.nextTmpFile()
    FileHelper.unzipFileToDir(zipFile, tmpDir)
    try {
      EntityDumpRestoreService.restore(tmpDir, true)
    } finally {
      FileUtils.deleteDirectory(tmpDir)
    }
  }

  fun doBackup() {
    

    val dir = getBackupDir()

    dir.mkdirs()

    val backupZipPath = EntityDumpRestoreService.dump()
    val backupFilename = FILE_PREFIX + DateHelper.formatDate(Date(), "yyyy-MM-dd_HH_mm_ss") + ".zip"
    val backupFile = File(dir, backupFilename)
    logger.info("产生数据库备份：${backupFile.absolutePath}")
    FileUtils.moveFile(File(FileManager.getFilesDir(), backupZipPath), backupFile)

    var keepDays = NumHelper.anyToInt(BzConfigManager.getByPath("ScData", "backup", "keepDays")) ?: 30
    if (keepDays <= 0) keepDays = 1

    // 删除过期文件
    clearExpiredFiles(keepDays, dir)
  }

  private fun clearExpiredFiles(keepDays: Int, dir: File) {
    val day = DateUtils.addDays(DateUtils.truncate(Date(), Calendar.DATE), -keepDays)
    logger.info("删除过期的数据库备份，保留 $keepDays 天，目录=$dir。实际删除 $day 之前的")

    val files = listBackupFiles()
    for (file in files) {
      if (file.lastModified() >= day.time) continue
      logger.info("删除过期的数据库备份：${file.absolutePath}")
      file.delete()
    }
  }

}

class DbBackupJob : Job {

  override fun execute(jobContext: JobExecutionContext) {
    DbBackupManager.doBackup()
  }

}