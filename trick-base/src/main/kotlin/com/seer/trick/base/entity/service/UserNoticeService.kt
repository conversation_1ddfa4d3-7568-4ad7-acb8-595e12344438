package com.seer.trick.base.entity.service

import com.seer.trick.Cq
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.http.WebSocketManager
import com.seer.trick.base.http.WebSocketManager.tagUiSet
import com.seer.trick.base.http.WsMsg
import com.seer.trick.base.user.Operator
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.util.*

object UserNoticeService {
  
  private val logger: Logger = LoggerFactory.getLogger(javaClass)
  
  fun createUserNotice(req: UserNoticeReq) {
    logger.debug("Create user notice {}", req)
    
    val notices: List<EntityValue> = req.targetUsers.map { userId ->
      mutableMapOf(
        "title" to req.title,
        "hasContent" to req.hasContent,
        "content" to req.content,
        "userId" to userId,
        "actionType" to req.actionType,
        "entityName" to req.entityName,
        "entityId" to req.entityId,
        "read" to false,
      )
    }
    
    EntityRwService.createMany("UserNotice", notices)
    
    WebSocketManager.sendAllAsync(WsMsg("UserNotice", userIds = req.targetUsers), tagUiSet)
  }
  
  fun readUserNotices(noticeIds: List<String>, op: Operator) {
    EntityRwService.updateMany(
      "UserNotice",
      Cq.include("id", noticeIds),
      mutableMapOf("read" to true, "readOn" to Date()),
    )
    
    WebSocketManager.sendAllAsync(WsMsg("UserNotice", userIds = listOf(op.userId)), tagUiSet)
  }
  
  // 不能报错
  fun notifyEntityCreate(entityName: String, entityIds: List<String>, op: Operator?) {
    try {
      val username = op?.username
      val em = BaseCenter.mustGetEntityMeta(entityName)
      if (!em.userNotice.create) return
      
      // 大量创建暂不通知
      if (entityIds.size != 1) return
      
      val entityId = entityIds[0]
      val ev = EntityRwService.findOneById(entityName, entityId)
      if (ev == null) {
        logger.warn("通知实体创建，但找不到实体 '$entityId'")
        return
      }
      
      val targetUsers = listTargetUsers(em, ev, op)
      if (targetUsers.isEmpty()) return
      
      val req = UserNoticeReq(
        title = "$username 创建了 '${em.label}'",
        hasContent = false,
        content = "",
        targetUsers = targetUsers.toList(),
        actionType = "ReadOneEntity",
        entityName,
        entityId,
      )
      
      createUserNotice(req)
    } catch (e: Exception) {
      logger.error("notifyEntityCreate", e)
    }
  }
  
  // 不能报错
  fun notifyEntityUpdate(entityName: String, entityIds: List<String>, op: Operator?) {
    try {
      val username = op?.username
      val em = BaseCenter.mustGetEntityMeta(entityName)
      if (!em.userNotice.update) return
      
      // 大量创建暂不通知
      if (entityIds.size != 1) return
      
      val entityId = entityIds[0]
      val ev = EntityRwService.findOneById(entityName, entityId)
      if (ev == null) {
        logger.warn("通知实体修改，但找不到实体 '$entityId'")
        return
      }
      
      val targetUsers = listTargetUsers(em, ev, op)
      if (targetUsers.isEmpty()) return
      
      val req = UserNoticeReq(
        title = "$username 修改了 '${em.label}'",
        hasContent = false,
        content = "",
        targetUsers = targetUsers.toList(),
        actionType = "ReadOneEntity",
        entityName,
        entityId,
      )
      
      createUserNotice(req)
    } catch (e: Exception) {
      logger.error("notifyEntityUpdate", e)
    }
  }
  
  // 不能报错
  fun notifyEntityComment(
    entityName: String,
    entityId: String,
    ev: EntityValue,
    commentActionLabel: String,
    op: Operator
  ) {
    try {
      val username = op.username
      val em = BaseCenter.mustGetEntityMeta(entityName)
      if (!em.userNotice.comment) return
      
      val targetUsers = listTargetUsers(em, ev, op)
      if (targetUsers.isEmpty()) return
      
      val req = UserNoticeReq(
        title = "$username $commentActionLabel 了 '${em.label}'",
        hasContent = false,
        content = "",
        targetUsers = targetUsers.toList(),
        actionType = "ReadOneEntity",
        entityName,
        entityId,
      )
      createUserNotice(req)
    } catch (e: Exception) {
      logger.error("notifyEntityComment", e)
    }
  }
  
  private fun listTargetUsers(em: EntityMeta, ev: EntityValue, op: Operator?): Set<String> {
    if (em.userNotice.targetUserFields.isNullOrEmpty()) return emptySet()
    val userIds: MutableSet<String> = HashSet()
    for (fieldName in em.userNotice.targetUserFields) {
      val fv = ev[fieldName] ?: continue
      @Suppress("UNCHECKED_CAST")
      if (fv is List<*>) {
        userIds.addAll(fv as List<String>)
      } else {
        userIds.add(fv as String)
      }
    }
    // 不用通知自己
    if (op != null) userIds.remove(op.userId)
    return userIds
  }
}

data class UserNoticeReq(
  val title: String,
  val hasContent: Boolean = false,
  val content: String = "",
  val targetUsers: List<String>,
  val actionType: String = "None", // "None" | "OpenLink" | "ReadOneEntity"
  val entityName: String? = null,
  val entityId: String? = null,
)