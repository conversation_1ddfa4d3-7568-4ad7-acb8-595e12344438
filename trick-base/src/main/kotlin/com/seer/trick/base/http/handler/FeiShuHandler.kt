package com.seer.trick.base.http.handler

import com.seer.trick.BzError
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager
import com.seer.trick.base.user.feishu.FeiShuOAuthManager
import io.javalin.http.Context

object FeiShuHandler {
  fun registerHandlers() {
    val c = Handlers("api/feishu")
    c.get("config-ids", ::getConfigIds, HttpServerManager.noAuth())
    c.get("auth-callback", ::callbackAuth, HttpServerManager.noAuth())
  }

  private fun getConfigIds(ctx: Context) {
    val fs = FeiShuOAuthManager.readConfig()
    if (!fs.feiShuEnabled) throw BzError("errFSNotEnabled")
    ctx.json(mapOf("appId" to fs.feiShuAppId))
  }

  private fun callbackAuth(ctx: Context) {
    val code = ctx.queryParam("code")
    if (code.isNullOrBlank()) throw BzError("errFeiShuNoCode")

    
    val fs = FeiShuOAuthManager.readConfig()
    if (!fs.feiShuEnabled) throw BzError("errFSNotEnabled")

    val userSession = FeiShuOAuthManager.signIn(code)

    HttpServerManager.setUserSessionCookies(ctx, userSession.userId, userSession.userToken)

    ctx.redirect("/")
  }
}