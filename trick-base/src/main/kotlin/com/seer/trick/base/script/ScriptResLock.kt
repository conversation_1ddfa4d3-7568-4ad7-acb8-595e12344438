package com.seer.trick.base.script

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.reslock.ResLockService
import kotlin.concurrent.withLock

object ScriptResLock {
  
  fun withLock(action: () -> Unit) {
    ResLockService.resLock.withLock(action)
  }
  
  fun isLocked(tc: ScriptTraceContext, resType: String, resId: String): Boolean {
    return ResLockService.isLocked(resType, resId)
  }
  
  fun getOwner(tc: ScriptTraceContext, resType: String, resId: String): String? {
    return ResLockService.getOwner(resType, resId)
  }
  
  fun tryLockRes(tc: ScriptTraceContext, resType: String, resId: String, owner: String, reason: String): Boolean {
    return ResLockService.tryLockRes(resType, resId, owner, reason)
  }
  
  fun unlockRes(tc: ScriptTraceContext, resType: String, resId: String) {
    return ResLockService.unlockRes(resType, resId)
  }
  
  fun unlockResIfLockedBy(tc: ScriptTraceContext, resType: String, resId: String, me: String): Boolean {
    return ResLockService.unlockResIfLockedBy(resType, resId, me)
  }
  
  fun listMyRes(tc: ScriptTraceContext, me: String): List<EntityValue> {
    return ResLockService.listMyRes(me)
  }
  
}