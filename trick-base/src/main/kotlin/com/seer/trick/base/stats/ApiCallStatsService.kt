package com.seer.trick.base.stats

import com.fasterxml.jackson.annotation.JsonProperty
import com.seer.trick.Cq
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.stats.StatsService.buildShowLabelOnTop
import com.seer.trick.helper.DateHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.NumHelper
import org.apache.commons.lang3.time.DateUtils
import java.util.*

object ApiCallStatsService {

  fun init() {
    StatsService.addGroup(
      ChartGroup(
        "ApiCall", "接口调用", 1, mutableListOf(
          ChartItem("ApiCallToday", "今日接口调用统计", "", 0),
          ChartItem("ApiCallLast7Days", "最近七日接口调用统计", "", 0),
        )
      )
    )

    StatsService.internalCalcMap["ApiCallToday"] = ::calcApiCallToday
    StatsService.internalCalcMap["ApiCallLast7Days"] = ::calcApiCallLast7Days
  }

  private fun calcApiCallToday(): String {
    val todayStart = DateUtils.truncate(Date(), Calendar.DATE)

    val data = mutableMapOf<String, ApiCallValue>()
    for (d in 0 until 24) {
      val dt = DateHelper.formatDate(DateUtils.addHours(todayStart, d), "yyyy-MM-dd HH")
      data[dt] = ApiCallValue(dt)
    }

    val startInstant = todayStart
    val endInstant = Date(DateUtils.addDays(todayStart, 1).time - 1)

    val evList = EntityRwService.findMany(
      
      "ApiCallTrace",
      Cq.and(listOf(Cq.gte("createdOn", startInstant), Cq.lt("createdOn", endInstant))),
      FindOptions(projection = listOf("createdOn", "resCode"))
    )
    for (ev in evList) {
      val dt = DateHelper.formatDate(ev["createdOn"] as Date, "yyyy-MM-dd HH")
      val sv = data[dt] ?: continue
      sv.all++

      val status = NumHelper.anyToInt(ev["resCode"])
      if (!(status != null && status >= 200 && status < 300)) sv.error++
    }

    val option = mapOf(
      "title" to mapOf("text" to "今日接口调用统计", "x" to "left", "y" to "top"),
      "legend" to mapOf("x" to "right", "y" to "top"),
      "dataset" to mapOf(
        "dimensions" to listOf("datetime", "总数", "错误数"),
        "source" to data.values.sortedBy { it.datetime }
      ),
      "xAxis" to mapOf("type" to "category"),
      "yAxis" to mapOf("type" to "value"),
      "series" to listOf(
        mapOf("type" to "line") + buildShowLabelOnTop(),
        mapOf("type" to "line") + buildShowLabelOnTop(),
      )
    )
    return JsonHelper.mapper.writeValueAsString(option)
  }

  private fun calcApiCallLast7Days(): String {
    return calcApiCallLastNDays(7, "最近七日接口调用统计")
  }

  private fun calcApiCallLastNDays(days: Int, title: String): String {
    val todayStart = DateUtils.truncate(Date(), Calendar.DATE)

    val data = mutableMapOf<String, ApiCallValue>()
    for (d in 0 until days) {
      val dt = DateHelper.formatDate(DateUtils.addDays(todayStart, -d), "yyyy-MM-dd")
      data[dt] = ApiCallValue(dt)
    }

    val startInstant = DateUtils.addDays(todayStart, -(days - 1))
    val endInstant = Date(DateUtils.addDays(todayStart, 1).time - 1)

    val evList = EntityRwService.findMany(
      
      "ApiCallTrace",
      Cq.and(listOf(Cq.gte("createdOn", startInstant), Cq.lt("createdOn", endInstant))),
      FindOptions(projection = listOf("createdOn", "resCode"))
    )
    for (ev in evList) {
      val dt = DateHelper.formatDate(ev["createdOn"] as Date, "yyyy-MM-dd")
      val sv = data[dt] ?: continue
      sv.all++

      val status = NumHelper.anyToInt(ev["resCode"])
      if (!(status != null && status >= 200 && status < 300)) sv.error++
    }

    val option = mapOf(
      "title" to mapOf("text" to title, "x" to "left", "y" to "top"),
      "legend" to mapOf("x" to "right", "y" to "top"),
      "dataset" to mapOf(
        "dimensions" to listOf("datetime", "总数", "错误数"),
        "source" to data.values.sortedBy { it.datetime }
      ),
      "xAxis" to mapOf("type" to "category"),
      "yAxis" to mapOf("type" to "value"),
      "series" to listOf(
        mapOf("type" to "line") + buildShowLabelOnTop(),
        mapOf("type" to "line") + buildShowLabelOnTop(),
      )
    )
    return JsonHelper.mapper.writeValueAsString(option)
  }

}


data class ApiCallValue(
  val datetime: String,
  @JsonProperty("总数")
  var all: Int = 0,
  @JsonProperty("错误数")
  var error: Int = 0,
)