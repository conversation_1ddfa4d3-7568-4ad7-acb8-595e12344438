package com.seer.trick.base.stats.manila.domain

import com.seer.trick.ComplexQuery
import com.seer.trick.base.entity.service.AdvancedField
import com.seer.trick.base.entity.service.GroupByField
import com.seer.trick.base.entity.service.StatisticDateType
import com.seer.trick.helper.IdHelper
import java.util.Date

data class ManilaReportCfg(
  val id: String, // 唯一
  val reportSubject: String, // 统计表 - 科目。需要手动增加国际化，key 自动添加的前缀为 "StatsLabel_"，例如 "StatsLabel_xxx"
  val resKeyTarget: String, // 汇总统计结果 key - 对象，如
  val resKeyPeriod: String, // 汇总统计结果 key - 周期，如 createdOn
  val resKeyValue: String, // 汇总统计结果 key - 值，要与 changeAggFields 中的 alias 匹配，如 value
  val changeEntityName: String, // 状态变化表 - 实体名称
  val changeFilter: ComplexQuery, // 状态变化表 - 过滤条件
  val changeAggFields: List<AdvancedField>, // 状态变化表 - 聚合字段
  val changeGroupBy: List<GroupByField>, // 状态变化表 - 分组字段
  val statsDateType: StatisticDateType, // 统计时间类型
  // val converters: Map<String, ((v: Any?) -> Any?)?> = emptyMap(), // 统计结果转换器 TODO value 改为存函数名，函数单独存
  val converters: Map<String, String?> = emptyMap(), // 统计结果转换器 { key -> ManilaReportService.converters 函数名 }
  val disabled: Boolean = false, // 禁用
  val statsOrder: Int = 0, // 统计顺序
  val includeAll: Boolean = false, // 是否包含 all 类型的统计，包含则去除 resKeyTarget 的分组条件后全部统计。用于去除冗余的配置
  val type: StatType = StatType.SUM,
)

enum class StatType {
  RATIO, // 比率类
  SUM, // 累加类
}

// 统计方案
data class ManilaFilterCase(
  val id: String = IdHelper.oidStr(),
  val displayOrder: Int = 0, // 显示顺序。后端返回的 FilterCase 不保证顺序，以此字段排序，前端自行维护
  val name: String, // 方案名
  val subjects: List<String>, // 科目
  val targets: List<String>, // 对象
  val periodType: StatisticDateType, // 周期类型
  val typeAlias: String?, // 前端存配置的字段
  val periodStart: String?, // 查询的起始时间点，如："2024-01-01 12H"、"2024-01-01"、"2024 W1"、"2024-01"、"2024 Q1"、"2024"
  val periodEnd: String?, // 查询的终止时间点，如："2024-01-01 12H"、"2024-01-01"、"2024 W1"、"2024-01"、"2024 Q1"、"2024"
  val timestampStart: Date?, // 查询的起始时间戳，如："2024-10-11T08:00:00.000z"
  val timestampEnd: Date?, // 查询的终止时间戳
  val commonCase: String?, // TODO 通用方案 近 3 天、近 7 天、近 30 天、近一季度
)