package com.seer.trick.base.user

import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.helper.IdHelper
import org.apache.commons.codec.digest.DigestUtils
import org.apache.commons.lang3.RandomStringUtils
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import java.nio.charset.StandardCharsets
import java.util.*
import kotlin.math.min

object UserService {
  
  private val logger = LoggerFactory.getLogger(javaClass)
  
  private const val ANONYMOUS_USER_ROLE_ID = "__anonymous__"
  private const val SALT = "DK4(@OGE2278WGH)WE234G2132ENGNE*(DB3GEGEJ"
  
  private fun prepareAnonymousRole() {
    val anonymousRole = getAnonymousUserRole()
    if (anonymousRole != null) return
    logger.info("No anonymous role, create one")
    
    val ev: EntityValue = mutableMapOf()
    ev["id"] = ANONYMOUS_USER_ROLE_ID
    ev["name"] = "匿名用户角色"
    
    EntityRwService.createOne("UserRole", ev)
  }
  
  private fun getAnonymousUserRole(): EntityValue? =
    EntityRwService.findOne("UserRole", Cq.idEq(ANONYMOUS_USER_ROLE_ID), null)
  
  /**
   * 当用户被删除后，UserSession 可能还在，所以先检查用户在
   */
  fun checkToken(userId: String, userToken: String): CheckTokenResult? {
    
    val user = EntityRwService.findOneById("HumanUser", userId, null)
    if (user == null) {
      logger.error("Check token, user not found $userId")
      return null
    }
    
    val session =
      EntityRwService.findOne("HumanUserSession", Cq.eq("userId", userId)) ?: return null
    
    val token2 = session["userToken"] as String?
    if (!Objects.equals(userToken, token2)) return null
    val expiredAt = session["expiredAt"] as Long
    if (expiredAt < System.currentTimeMillis()) return null
    
    return CheckTokenResult(
      userId,
      userToken,
      user["username"] as String?,
      user["roAdmin"] as Boolean? ?: false,
    )
  }
  
  fun signIn(username: String, password: String): HumanUserSession {
    if (StringUtils.isBlank(password)) throw BzError("errSignInNoPassword")
    
    //    val user = if (signInExt != null) {
    //      signInExt?.invoke(username, password)
    
    val userEv = EntityRwService.findOne("HumanUser", Cq.eq("username", username))
      ?: throw BzError("errNoUserUsername", username)
    
    var pwdLastSetOn = userEv["pwdSetOn"] as Date?
    if (pwdLastSetOn == null) {
      pwdLastSetOn = Date()
      EntityRwService.updateOne("HumanUser", Cq.eq("username", username), mutableMapOf("pwdSetOn" to pwdLastSetOn))
    }
    
    // 密码失败次数限制
    val pwdErrLimit = BzConfigManager.getByPathAsInt("ScUserSecurity", "pwdErrLimit") ?: -1
    // 密码失败次数重置时间
    val pwdErrCountResetDelay = BzConfigManager.getByPathAsInt("ScUserSecurity", "pwdErrCountResetDelay") ?: 600
    // 密码有效期
    val pwdExpireDays = BzConfigManager.getByPathAsInt("ScUserSecurity", "pwdExpireDays") ?: -1
    
    // 密码有效期限制
    if (pwdExpireDays > 0) {
      val days = (Date().time - pwdLastSetOn.time) / (1000 * 60 * 60 * 24)
      if (days > pwdExpireDays) {
        throw BzError("errPwdExpired") // TODO dict
      }
    }
    
    // 密码失败次数限制
    var errCount = userEv["pwdErrCount"] as Int? ?: 0
    if (pwdErrLimit > 0) {
      val modifiedOn = userEv["modifiedOn"] as Date? ?: Date()
      if ((Date().time - modifiedOn.time) / 1000 >= pwdErrCountResetDelay) {
        // 密码失败次数过多，10 mins 后可以重试
        errCount = 0
      }
      if (errCount >= pwdErrLimit) throw BzError("errPwdErrExceedLimit", pwdErrCountResetDelay)
    }
    
    if (Objects.equals(userEv["disabled"], true)) throw BzError("errUserDisabled")
    // if (user.directSignInDisabled) throw ObservableError("禁止直接登录")
    if (!Objects.equals(hashPassword(password, SALT), userEv["password"])) {
      EntityRwService.updateOne(
        
        "HumanUser",
        Cq.eq("username", username),
        mutableMapOf("pwdErrCount" to min(errCount + 1, Int.MAX_VALUE)),
      )
      throw BzError("errPasswordNotMatch")
    }
    
    EntityRwService.updateOne("HumanUser", Cq.eq("username", username), mutableMapOf("pwdErrCount" to 0))
    return signInSuccessfully(userEv)
  }
  
  fun signInSuccessfully(user: EntityValue): HumanUserSession {
    val userId = EntityHelper.mustGetId(user)
    signOut(userId) // 先退出
    val sessionExpireMinutes = 60 * 24 * 15
    val expireAt = System.currentTimeMillis() + sessionExpireMinutes * 1000 * 60
    // logger.debug("new session $sessionExpireMinutes, $expireAt")
    val token: String = RandomStringUtils.randomAlphanumeric(24)
    val session = HumanUserSession(userId, token, expireAt)
    EntityRwService.createOne("HumanUserSession", session.ev)
    return session
  }
  
  fun signOut(userId: String) {
    EntityRwService.removeMany("HumanUserSession", Cq.eq("userId", userId))
  }
  
  fun changePassword(op: Operator, oldPassword: String, newPassword: String) {
    if (newPassword.isBlank()) throw BzError("errEmptyPassword")
    
    val user = EntityRwService.findOne("HumanUser", Cq.eq("id", op.userId))
      ?: throw BzError("errNoSuchUserById", op.userId)
    if (user["disabled"] == true) throw BzError("errUserDisabled")
    if (!Objects.equals(user["password"] as String, hashPassword(oldPassword))) throw BzError("errPasswordNotMatch")
    
    // TODO 检查密码格式
    EntityRwService.updateOne(
      "HumanUser",
      Cq.eq("id", op.userId),
      mutableMapOf("password" to newPassword),
    )
    EntityRwService.removeMany("HumanUserSession", Cq.eq("userId", op.userId))
  }
  
  fun createAdminIfNot(username: String, password: String, userId: String?) {
    if (null != EntityRwService.findOne("HumanUser", Cq.eq("roAdmin", true))) return
    logger.info("Create admin user")
    val adminUser: EntityValue = mutableMapOf(
      "id" to (userId ?: IdHelper.oidStr()),
      "username" to username,
      "password" to password,
      "roAdmin" to true,
    )
    EntityRwService.createOne("HumanUser", adminUser)
  }
  
  fun hashPassword(password: String, salt: String = SALT): String =
    DigestUtils.sha512Hex((password + salt).toByteArray(StandardCharsets.UTF_8))
}

class CheckTokenResult(val userId: String, val userToken: String, val username: String?, val roAdmin: Boolean)

class HumanUserSession(userId: String, userToken: String, expireAt: Long) {
  
  val ev: EntityValue = mutableMapOf()
  
  init {
    ev["id"] = IdHelper.oidStr()
    ev["userId"] = userId
    ev["userToken"] = userToken
    ev["expiredAt"] = expireAt
  }
  
  val userId: String
    get() = ev["userId"] as String
  
  val userToken: String
    get() = ev["userToken"] as String
}