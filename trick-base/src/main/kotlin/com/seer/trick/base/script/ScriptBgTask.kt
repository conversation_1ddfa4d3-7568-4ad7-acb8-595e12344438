package com.seer.trick.base.script

import com.seer.trick.base.MapToAnyNull
import com.seer.trick.base.bgt.BgTaskRuntime
import com.seer.trick.base.bgt.BgTaskService

/**
 * 给脚本用，执行后台任务。
 */
object ScriptBgTask {

  fun runBgTask(tc: ScriptTraceContext, taskName: String, remark: String, taskArgs: Map<String, Any?>?): BgTaskRuntime =
    BgTaskService.runBgTask(taskName, remark, taskArgs)

  fun runStepOnce(tc: ScriptTraceContext, tr: BgTaskRuntime, stepId: String, work: () -> MapToAnyNull): MapToAnyNull =
    BgTaskService.runStepOnce(tr, stepId, work)
}