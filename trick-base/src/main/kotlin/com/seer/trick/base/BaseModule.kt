package com.seer.trick.base

import com.seer.trick.I18N.lo
import com.seer.trick.base.alarm.AlarmGroup
import com.seer.trick.base.alarm.AlarmService.registerGroup
import com.seer.trick.base.bgt.BgTaskService
import com.seer.trick.base.file.clean.TmpFileCleaner
import com.seer.trick.base.http.HttpRoutes
import com.seer.trick.base.http.handler.BgTaskHandler
import com.seer.trick.base.stats.manila.ManilaFilterCaseService
import com.seer.trick.base.stats.manila.ManilaReportService

object BaseModule : AppModule() {

  override fun init() {
    registerGroup(AlarmGroup("BgTask", lo("labelBgTask"), 20))
  }

  override fun registerHttpHandlers() {
    HttpRoutes.registerHandlers()
    BgTaskHandler.registerHandlers()
  }

  override fun afterScript() {
    ManilaFilterCaseService.init()
  }

  override fun afterHttp() {
    ManilaReportService.init()
    TmpFileCleaner.init()
    BgTaskService.init()
  }

  override fun dispose() {
    ManilaReportService.dispose()
    ManilaFilterCaseService.dispose()
  }
}