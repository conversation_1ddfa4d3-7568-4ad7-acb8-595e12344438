package com.seer.trick.base.entity.service

import com.seer.trick.BzError
import com.seer.trick.ComplexQuery
import com.seer.trick.Cq
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.*
import com.seer.trick.base.entity.cache.EntityCache
import com.seer.trick.base.entity.cache.EntityLocalCache
import com.seer.trick.base.entity.executor.EntityExecutor
import com.seer.trick.base.entity.id.IdGenManager
import com.seer.trick.base.entity.service.extension.EntityServiceExtensions
import com.seer.trick.base.user.Operator
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.NumHelper
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.min

object EntityRwService {
  
  val cache: EntityCache = EntityLocalCache()
  
  // 记录业务对象最后修改时间
  val entityLastChanged: MutableMap<String, Long> = ConcurrentHashMap()
  
  /**
   * 返回 id
   */
  fun createOne(entityName: String, ev: EntityValue, o: CreateOptions? = null): String {
    val op = Operator.current()
    val em = BaseCenter.mustGetEntityMeta(entityName)
    em.checkIdExisted()
    
    if (ev.isEmpty()) throw BzError("errEmptyEntityValue", entityName)
    
    // TODO EntityValueHelper.checkEntityValue(ev, entityDef, emptyList())
    
    // 先 patch 再 clean
    
    val id = patchCreating(em, ev, o, op)
    
    EntityValueCleaner.cleanEntityValue(em, ev)
    
    val evList = listOf(ev)
    
    if (o == null || !o.muteExt) {
      val mr = EntityServiceExtensions.beforeCreating(em, evList)
      if (mr != null) return mr[0]
    }
    
    EntityExecutor.create(em, evList)
    
    cache.clearCount(entityName)
    cache.clearByQuery(entityName)
    
    clearOrderCacheByComponent(em)
    
    if (o == null || !o.muteExt) {
      val nv: EntityValue = findOne(entityName, Cq.eq("id", id))!!
      EntityServiceExtensions.afterCreating(em, listOf(nv))
    }
    
    entityLastChanged[em.name] = System.currentTimeMillis()
    
    return id
  }
  
  fun createMany(
    entityName: String,
    evList: List<EntityValue>,
    o: CreateOptions? = null,
  ): List<String> {
    val op = Operator.current()
    val em = BaseCenter.mustGetEntityMeta(entityName)
    em.checkIdExisted()
    
    if (em.scale == EntityScale.Singleton) throw BzError("errSingletonEntityBadOp", em.name, "create many")
    
    // TODO for (ev in entityValues) EntityValueHelper.checkEntityValue(ev, entityDef, emptyList())
    
    // 先 patch 再 clean
    
    val ids: MutableList<String> = ArrayList()
    for (ev in evList) {
      if (ev.isEmpty()) throw BzError("errEmptyEntityValue", entityName)
      val id = patchCreating(em, ev, o, op)
      ids.add(id)
    }
    
    EntityValueCleaner.cleanEntityValues(em, evList)
    
    if (o == null || !o.muteExt) {
      val mr = EntityServiceExtensions.beforeCreating(em, evList)
      if (mr != null) return mr
    }
    
    EntityExecutor.create(em, evList)
    
    cache.clearCount(entityName)
    cache.clearByQuery(entityName)
    clearOrderCacheByComponent(em)
    
    if (o == null || !o.muteExt) {
      val nvList: MutableList<EntityValue> = ArrayList()
      // 查询语句过长会导致 derby 数据库报错
      val chunkList = ids.chunked(10000)
      for (chunk in chunkList) {
        nvList += findMany(entityName, Cq.include("id", chunk), null)
      }
      
      EntityServiceExtensions.afterCreating(em, nvList)
    }
    
    entityLastChanged[em.name] = System.currentTimeMillis()
    
    return ids
  }
  
  private fun patchCreating(em: EntityMeta, ev: EntityValue, o: CreateOptions?, op: Operator?): String {
    val id = ensureId(em, ev, o)
    patchCreatingCoreFields(ev, o, op)
    
    patchDefaultValues(em, ev)
    
    return id
  }
  
  @Suppress("UNCHECKED_CAST")
  private fun patchDefaultValues(em: EntityMeta, ev: EntityValue) {
    for (fm in em.fields.values) {
      if (fm.type == FieldType.Component) {
        val fv = ev[fm.name] ?: continue
        val refEm = BaseCenter.mustGetRefEntityMeta(fm)
        if (fm.scale == FieldScale.Single) {
          patchDefaultValues(refEm, fv as EntityValue)
        } else {
          val items = fv as List<EntityValue>
          for (item in items) patchDefaultValues(refEm, item)
        }
      } else {
        if (ev.containsKey(fm.name)) continue
        if (fm.defaultValue != null && (fm.defaultValue !is String || fm.defaultValue.isNotBlank())) {
          ev[fm.name] = fm.defaultValue
        }
      }
    }
  }
  
  fun ensureId(em: EntityMeta, ev: EntityValue, o: CreateOptions? = null): String {
    var id: String?
    id = ev["id"] as String?
    if (id != null) id = id.trim()
    if (!id.isNullOrBlank() && (o == null || !o.resetId)) return id
    
    id = if (em.idGen != null && em.idGen.enabled) {
      IdGenManager.generateId(em.idGen)
    } else {
      IdHelper.oidStr()
    }
    ev["id"] = id
    return id
  }
  
  private fun patchCreatingCoreFields(ev: EntityValue, o: CreateOptions?, op: Operator?) {
    ev[FieldMeta.FIELD_CREATED_BY] = op?.userId
    ev[FieldMeta.FIELD_MODIFIED_BY] = op?.userId
    val now = Date()
    if (o?.keepDate == true) {
      ev[FieldMeta.FIELD_CREATED_ON] = ev[FieldMeta.FIELD_CREATED_ON] ?: now
      ev[FieldMeta.FIELD_MODIFIED_ON] = ev[FieldMeta.FIELD_MODIFIED_ON] ?: now
    } else {
      ev[FieldMeta.FIELD_CREATED_ON] = now
      ev[FieldMeta.FIELD_MODIFIED_ON] = now
    }
    ev[FieldMeta.FIELD_VERSION] = ev[FieldMeta.FIELD_VERSION] ?: 0
  }
  
  fun updateOne(
    entityName: String,
    query: ComplexQuery,
    rawUpdate: EntityValue,
    o: UpdateOptions? = null,
  ): Long {
    val o2 = if (o == null) {
      UpdateOptions(1)
    } else if (o.limit == null || o.limit > 1) {
      o.copy(limit = 1)
    } else {
      o
    }
    return updateMany(entityName, query, rawUpdate, o2)
  }
  
  fun updateMany(
    entityName: String,
    query: ComplexQuery,
    update: EntityValue,
    o: UpdateOptions? = null,
  ): Long {
    val op = Operator.current()
    val em = BaseCenter.mustGetEntityMeta(entityName)
    em.checkIdExisted()
    
    EntityValueCleaner.cleanEntityValue(em, update)
    
    EntityValueCleaner.cleanComplexQuery(em, query)
    
    patchUpdating(update, op)
    
    val targetIds = findIds(entityName, query, o?.limit)
    if (targetIds.isEmpty()) return 0
    
    // TODO EntityValueHelper.checkEntityValue(ev, entityDef, emptyList())
    
    if (o == null || !o.muteExt) {
      val mr = EntityServiceExtensions.beforeUpdating(em, targetIds, update)
      if (mr != null) return mr
    }
    
    val oldValues = findMany(entityName, Cq.include("id", targetIds), null)
    
    update[FieldMeta.FIELD_MODIFIED_ON] = Date()
    update.remove(FieldMeta.FIELD_CREATED_BY)
    update.remove(FieldMeta.FIELD_CREATED_ON)
    update.remove(FieldMeta.FIELD_VERSION)
    update.remove("id")
    
    EntityExecutor.update(em, targetIds, update)
    
    cache.clearCount(entityName)
    cache.clearByQuery(entityName)
    cache.clearByIds(entityName, targetIds)
    clearOrderCacheByComponent(em)
    
    if (o == null || !o.muteExt) {
      val newValues = findMany(entityName, Cq.include("id", targetIds), null)
      EntityServiceExtensions.afterUpdating(em, targetIds, oldValues, newValues)
    }
    
    entityLastChanged[em.name] = System.currentTimeMillis()
    
    return targetIds.size.toLong()
  }
  
  fun removeOne(entityName: String, query: ComplexQuery, o: RemoveOptions? = null): Long {
    val o2 = if (o == null) {
      RemoveOptions(1)
    } else if (o.limit != null && o.limit > 1) {
      o.copy(limit = 1)
    } else {
      o
    }
    return removeMany(entityName, query, o2)
  }
  
  fun removeMany(entityName: String, query: ComplexQuery, o: RemoveOptions? = null): Long {
    val em = BaseCenter.mustGetEntityMeta(entityName)
    
    if (em.scale == EntityScale.Singleton) throw BzError("errSingletonEntityBadOp", em.name, "remove many")
    
    EntityValueCleaner.cleanComplexQuery(em, query)
    
    val removeLimit = o?.limit?.toLong() ?: count(entityName, query) // 期望删除数
    var rest = removeLimit // 剩余要删除数
    // 大表分批删除
    while (rest > 0) {
      val d = min(1000, rest)
      
      val targetIds = findIds(entityName, query, d.toInt())
      if (targetIds.isEmpty()) return 0
      
      if (o == null || !o.muteExt) {
        val mr = EntityServiceExtensions.beforeRemoving(em, targetIds)
        if (mr != null) return mr
      }
      
      val oldValues = findMany(entityName, Cq.include("id", targetIds), null)
      
      EntityExecutor.remove(em, targetIds)
      
      cache.clearCount(entityName)
      cache.clearByQuery(entityName)
      cache.clearByIds(entityName, targetIds)
      clearOrderCacheByComponent(em)
      
      if (o == null || !o.muteExt) {
        EntityServiceExtensions.afterRemoving(em, oldValues)
      }
      
      // logger.debug("删除了 $entityName 表中 $d 条数据")
      rest -= d
    }
    
    entityLastChanged[em.name] = System.currentTimeMillis()
    
    // logger.debug("总共删了 $entityName 表中 $removeLimit 条数据")
    return removeLimit
  }
  
  fun removeAll(entityName: String) {
    val em = BaseCenter.mustGetEntityMeta(entityName)
    EntityExecutor.removeAll(em)
    
    cache.clearCount(entityName)
    cache.clearByQuery(entityName)
    cache.clearByIds(entityName, null)
    clearOrderCacheByComponent(em)
    
    entityLastChanged[em.name] = System.currentTimeMillis()
  }
  
  fun count(entityName: String, query: ComplexQuery): Long {
    val em = BaseCenter.mustGetEntityMeta(entityName)
    EntityValueCleaner.cleanComplexQuery(em, query)
    
    return cache.getCount(entityName, query) {
      EntityExecutor.count(em, query)
    }
  }
  
  fun findMany(entityName: String, query: ComplexQuery, o: FindOptions? = null): List<EntityValue> =
    cache.getByQuery(entityName, query, o) {
      findManyWithoutCache(entityName, query, o)
    }
  
  fun findIds(entityName: String, query: ComplexQuery, limit: Int? = null): List<String> = findMany(
    entityName,
    query,
    FindOptions(projection = listOf("id"), limit = limit),
  ).map(EntityHelper::mustGetId)
  
  fun findManyWithoutCache(
    entityName: String,
    query: ComplexQuery,
    o: FindOptions? = null,
  ): List<EntityValue> {
    val em = BaseCenter.mustGetEntityMeta(entityName)
    EntityValueCleaner.cleanComplexQuery(em, query)
    
    return EntityExecutor.find(em, query, o)
  }
  
  fun findOne(entityName: String, query: ComplexQuery, o: FindOptions? = null): EntityValue? {
    val many = findMany(entityName, query, o?.copy(limit = 1) ?: FindOptions(limit = 1))
    return if (many.isEmpty()) null else many[0]
  }
  
  fun findOneById(entityName: String, id: String, o: FindOptions? = null): EntityValue? =
    findOne(entityName, Cq.idEq(id), o)
  
  fun exists(entityName: String, query: ComplexQuery): Boolean =
    findOne(entityName, query, FindOptions(projection = listOf("id"))) != null
  
  private fun patchUpdating(ev: EntityValue, op: Operator?) {
    ev.remove("id")
    ev.remove("_id")
    
    // TODO 这里判断空
    
    ev.remove(FieldMeta.FIELD_VERSION)
    ev.remove(FieldMeta.FIELD_CREATED_BY)
    ev.remove(FieldMeta.FIELD_CREATED_ON)
    ev[FieldMeta.FIELD_MODIFIED_BY] = op?.userId
    ev[FieldMeta.FIELD_MODIFIED_ON] = Date()
  }
  
  fun calcSumFieldsUpdate(ev: EntityValue, em: EntityMeta): EntityValue {
    val update: EntityValue = mutableMapOf()
    
    em.fields[FieldMeta.FIELD_LINES] ?: return update
    val lines = EntityHelper.getLines(ev, FieldMeta.FIELD_LINES) ?: return update
    
    for (fm in em.fields.values) {
      if (fm.sumLineField.isBlank()) continue
      val sum = lines.sumOf { line -> NumHelper.anyToDouble(line[fm.sumLineField]) ?: 0.0 }
      update[fm.name] = sum
    }
    
    return update
  }
  
  /**
   * 直接修改单行需要把单据的缓存清掉
   */
  private fun clearOrderCacheByComponent(lineEm: EntityMeta) {
    val clearEmList: MutableSet<String> = mutableSetOf()
    for (em in BaseCenter.entityMetaMap.values) {
      for (fm in em.fields.values) {
        if (fm.type == FieldType.Component && fm.refEntity == lineEm.name) {
          clearEmList.add(em.name)
        }
      }
    }
    for (en in clearEmList) {
      cache.clearByQuery(en)
      cache.clearByIds(en, null) // TODO 暂时全清掉
    }
  }
  
  fun countAll(): Map<String, Long> {
    val countAll = mutableMapOf<String, Long>()
    for (em in BaseCenter.entityMetaMap.values) {
      if (em.disabled) continue
      val count = count(em.name, Cq.all())
      countAll[em.name] = count
    }
    return countAll
  }
}

data class CreateOptions(
  @JvmField
  val resetId: Boolean = false,
  @JvmField
  val muteExt: Boolean = false, // 不要触发扩展
  @JvmField
  val keepDate: Boolean = false, // 保持创建和修改时间
)

data class UpdateOptions(
  @JvmField
  val limit: Int? = null,
  @JvmField
  val muteExt: Boolean = false, // 不要触发扩展
)

data class RemoveOptions(
  @JvmField
  val limit: Int? = null,
  @JvmField
  val muteExt: Boolean = false, // 不要触发扩展
)

data class FindOptions(
  @JvmField
  val projection: List<String>? = null,
  @JvmField
  val sort: List<String>? = null,
  @JvmField
  val skip: Int? = null,
  @JvmField
  val limit: Int? = null,
)
// async function recordHistory(em: EntityMeta, evList: EntityValue[]) {
//   const now = new Date()
//   for (const ev of evList) {
//     const id = ev["id"]
//     ev["historyHistoryId"] = id
//     ev["historyHistoryOn"] = now
//     ev["id"] = getObjectIdHexString()
//   }
//
//   const docList: Document[] = evList.map((ev) => {
//     // TODO ev to doc
//     return ev
//   })
//   const c = getCollectionByOrg(oo.roId, em.name + "_history")
//   await c.insertMany(docList)
// }
//
// async function recordTrash(em: EntityMeta, query: ComplexQuery) {
//   const evList = await findMany(oo, em.name, query)
//   if (!evList.length) return
//   const docList: Document[] = evList.map((ev) => {
//     // TODO ev to doc
//     return ev
//   })
//   const c = getCollectionByOrg(oo.roId, em.name + "_trash")
//   await c.insertMany(docList)
// }