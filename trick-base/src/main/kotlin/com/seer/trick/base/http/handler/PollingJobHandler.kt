package com.seer.trick.base.http.handler

import com.seer.trick.base.concurrent.PollingJobManager
import com.seer.trick.base.http.WebSocketManager
import com.seer.trick.base.http.WebSocketSubscriber
import com.seer.trick.base.http.WsEnv
import com.seer.trick.base.http.WsMsg
import io.javalin.websocket.WsMessageContext

object PollingJobHandler : WebSocketSubscriber() {

  fun registerHandlers() {
    WebSocketManager.subscribers += this
  }

  override fun onMessage(ctx: WsMessageContext, msg: WsMsg, env: WsEnv) {
    if (msg.action == "PollingJob::Query") {
      ctx.send(WsMsg.json("PollingJob::Reply", PollingJobManager.list(), replyToId = msg.id))
    }
  }
}