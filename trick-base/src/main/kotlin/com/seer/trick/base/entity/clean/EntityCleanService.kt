package com.seer.trick.base.entity.clean

import com.seer.trick.Cq
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.concurrent.BaseConcurrentCenter.lowTimeSensitiveExecutor
import com.seer.trick.base.entity.DataCleanType
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.RemoveOptions
import com.seer.trick.helper.submitCatch
import org.apache.commons.lang3.time.DateUtils
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

object EntityCleanService {

  private val logger = LoggerFactory.getLogger(javaClass)

  private val cleanExecutor = Executors.newSingleThreadScheduledExecutor()

  fun init() {
    cleanExecutor.scheduleAtFixedRate(::clean, 5, 60 * 2, TimeUnit.MINUTES)
  }

  fun doCleanAsync() {
    lowTimeSensitiveExecutor.submitCatch("clean old entities", logger) {
      clean()
    }
  }

  private fun clean() {
    logger.info("Start | Clean old entities")
    for (em in BaseCenter.entityMetaMap.values) {
      if (em.disabled || !em.dateClean.enabled) continue
      try {
        if (em.dateClean.type == DataCleanType.ByDay && em.dateClean.keepDays != null && em.dateClean.keepDays > 0) {
          val oldest = DateUtils.truncate(DateUtils.addDays(Date(), -(em.dateClean.keepDays - 1)), Calendar.DATE)
          val removedCount =
            EntityRwService.removeMany(em.name, Cq.lt("createdOn", oldest), RemoveOptions(muteExt = true))
          if (removedCount > 0) {
            logger.info("清理业务对象 ${em.name}，< $oldest ，清理数量= $removedCount")
          }
        }
      } catch (e: Exception) {
        logger.error("清理业务对象失败", e)
      }
    }
    logger.info("End | Clean old entities")
  }
}