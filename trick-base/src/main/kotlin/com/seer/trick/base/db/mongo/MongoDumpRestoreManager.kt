package com.seer.trick.base.db.mongo

import com.seer.trick.base.db.DbManager
import com.seer.trick.base.file.FileManager
import com.seer.trick.base.http.WebSocketManager
import com.seer.trick.base.http.WsMsg
import com.seer.trick.helper.FileHelper
import com.seer.trick.helper.JsonHelper
import org.apache.commons.io.FileUtils
import org.apache.commons.lang3.StringUtils
import org.bson.Document
import org.bson.json.JsonMode
import org.bson.json.JsonWriterSettings
import org.slf4j.LoggerFactory
import java.io.BufferedReader
import java.io.File
import java.io.FileReader
import java.io.FileWriter
import java.nio.charset.StandardCharsets


/**
 * https://www.mongodb.com/docs/drivers/java/sync/current/fundamentals/data-formats/document-data-format-extended-json/
 */
object MongoDumpRestoreManager {
  
  private val logger = LoggerFactory.getLogger(this::class.java)
  
  fun dump(): String {
    val db = DbManager.mustGetMongoDb()
    val collectionNames = db.listCollectionNames().toList()
    
    val dir = FileManager.nextTmpFile()
    dir.mkdirs()
    val zipFile = FileManager.nextTmpFile("zip", "db-")
    for ((i, collectionName) in collectionNames.withIndex()) {
      val msg = mapOf(
        "table" to collectionName,
        "index" to i,
        "total" to collectionNames.size
      )
      logger.info("导出数据库表：$collectionName，${i + 1}/${collectionNames.size}")
      WebSocketManager.sendAllAsync(WsMsg("DbDump", JsonHelper.mapper.writeValueAsString(msg)))
      
      val file = File(dir, "$collectionName.json")
      collectionToFile(collectionName, Document(), file)
    }
    FileHelper.zipDirToFile(dir, zipFile)
    FileUtils.deleteDirectory(dir)
    return FileManager.fileToPath(zipFile)
  }
  
  fun collectionToFile(collectionName: String, filter: Document, file: File) {
    val db = DbManager.mustGetMongoDb()
    val collection = db.getCollection(collectionName)
    
    // 输出带类型信息的
    val settings = JsonWriterSettings.builder().outputMode(JsonMode.EXTENDED).build()
    
    FileWriter(file, StandardCharsets.UTF_8, false).use { writer ->
      collection.find(filter).cursor().use { cursor ->
        while (cursor.hasNext()) {
          writer.write(cursor.next().toJson(settings))
          writer.write("\n")
        }
      }
    }
  }
  
  fun restore(dir: File, clear: Boolean) {
    val db = DbManager.mustGetMongoDb()
    
    val list = dir.list()!!
    for ((i, filename) in list.withIndex()) {
      if (!filename.endsWith(".json")) continue
      val collectionName = StringUtils.substringBeforeLast(filename, ".json")
      
      val msg = mapOf(
        "table" to collectionName,
        "index" to i,
        "total" to list.size
      )
      logger.info("导入数据库表：$collectionName，${i + 1}/${list.size}")
      WebSocketManager.sendAllAsync(WsMsg("DbRestore", JsonHelper.mapper.writeValueAsString(msg)))
      
      val collection = db.getCollection(collectionName)
      if (clear) collection.drop()
      
      val num = fileToCollection(collectionName, File(dir, filename))
      logger.info("导入数据库表：$collectionName，完成，共 $num 条")
    }
  }
  
  private fun fileToCollection(collectionName: String, file: File): Int {
    val db = DbManager.mustGetMongoDb()
    val collection = db.getCollection(collectionName)
    
    val batchSize = 100
    val docList: MutableList<Document> = ArrayList(batchSize)
    var num = 0
    BufferedReader(FileReader(file, StandardCharsets.UTF_8)).use { reader ->
      var line = reader.readLine()
      while (line != null) {
        // 可以直接读 Extended JSON
        val doc = Document.parse(line)
        docList += doc
        ++num
        
        if (docList.size == batchSize) {
          collection.insertMany(docList)
          docList.clear()
        }
        
        line = reader.readLine()
      }
      if (docList.isNotEmpty()) {
        collection.insertMany(docList)
      }
    }
    return num
  }
  
}