package com.seer.trick.base.http.handler

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.base.file.FileManager
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.HttpServerManager.sendFileWithType
import com.seer.trick.helper.JsonHelper
import io.javalin.http.Context
import org.apache.commons.codec.digest.DigestUtils
import org.apache.commons.io.FilenameUtils
import java.io.File
import java.io.FileInputStream
import java.net.URLEncoder
import java.nio.charset.StandardCharsets

object FileHandler {
  
  fun registerHandlers() {
    val c = Handlers("api/files")
    c.post("upload", ::upload, auth())
    c.get("get/<path>", ::download, auth())
  }
  
  private fun upload(ctx: Context) {
    val file = ctx.uploadedFile("f0") ?: throw BzError("errNoUploadedFile", "f0")
    
    val configStr = ctx.formParam("config")
    val config: UploadConfig? = if (configStr.isNullOrBlank())
      null
    else
      JsonHelper.mapper.readValue(configStr, jacksonTypeRef())
    
    val relativePath: String = FileManager.upload(
      file.content(),
      FilenameUtils.getExtension(file.filename())
    )
    
    val md5 = if (config?.md5Compute == true) {
      FileInputStream(FileManager.pathToFile(relativePath)).use { DigestUtils.md5Hex(it) }
    } else {
      null
    }
    
    ctx.json(UploadFileInfo(file.filename(), file.size(), relativePath, md5))
  }
  
  private fun download(ctx: Context) {
    val path = ctx.pathParam("path")
    ctx.queryParam("filename")
    
    val file: File = try {
      FileManager.pathToFile(path)
    } catch (e: BzError) {
      if (e.code == "ReadFileNoRoDir") throw Error404(e.message ?: "")
      throw e
    }
    if (!file.exists()) throw Error404(path)
    
    sendFileWithType(file, ctx)
  }
  
}

data class UploadConfig(
  val md5Compute: Boolean = false
)

data class UploadFileInfo(
  val originalName: String?,
  val size: Long,
  val path: String,
  val md5: String? = null,
)
