package com.seer.trick.base.event

import org.slf4j.LoggerFactory
import java.util.concurrent.CopyOnWriteArrayList

class EventBus<T> {
  
  private val logger = LoggerFactory.getLogger(this::class.java)
  
  val listeners: MutableList<EventListener<T>> = CopyOnWriteArrayList()
  
  fun fire(e: T) {
    for (listener in listeners) {
      try {
        listener.onEvent(e)
      } catch (e: Exception) {
        logger.error("事件监听器报错", e)
      }
    }
  }
  
}

interface EventListener<T> {
  
  fun onEvent(e: T)
  
}