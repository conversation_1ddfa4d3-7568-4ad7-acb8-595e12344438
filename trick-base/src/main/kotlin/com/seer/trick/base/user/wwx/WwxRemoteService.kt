package com.seer.trick.base.user.wwx

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonAutoDetect
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import retrofit2.Call
import retrofit2.http.GET
import retrofit2.http.Query

interface WwxRemoteService {
  
  @GET("cgi-bin/gettoken")
  fun getToken(
    @Query("corpid") corpId: String,
    @Query("corpsecret") corpSecret: String
  ): Call<GetTokenRes>
  
  @GET("cgi-bin/user/getuserinfo")
  fun getUserInfo(
    @Query("access_token") accessToken: String,
    @Query("code") code: String
  ): Call<GetUserInfoRes>
  
  @GET("cgi-bin/user/get")
  fun getUserDetails(
    @Query("access_token") accessToken: String,
    @Query("userid") userId: String
  ): Call<GetUserDetailsRes>
}

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
data class GetTokenRes(
  @JsonAlias("errcode")
  var errCode: Int = 0,
  @JsonAlias("errmsg")
  var errMsg: String? = null,
  @JsonAlias("access_token")
  var accessToken: String? = null,
  @JsonAlias("expires_in")
  var expiresIn: Int = 0
)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
data class GetUserDetailsRes(
  @JsonAlias("errcode")
  var errCode: Int = 0,
  @JsonAlias("errmsg")
  var errMsg: String? = null,
  @JsonAlias("userid")
  var userId: String? = null,
  @JvmField
  var name: String? = null
)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
data class GetUserInfoRes(
  @JsonAlias("errcode")
  var errCode: Int = 0,
  @JsonAlias("errmsg")
  var errMsg: String? = null,
  @JsonAlias("UserId")
  var userId: String? = null
)
