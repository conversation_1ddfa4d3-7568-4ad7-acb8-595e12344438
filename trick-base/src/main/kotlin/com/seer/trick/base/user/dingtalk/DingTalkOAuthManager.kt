package com.seer.trick.base.user.dingtalk

import com.seer.trick.BzError
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.user.OAuthConfig
import com.seer.trick.base.user.OAuthManager
import com.seer.trick.helper.JsonHelper
import okhttp3.OkHttpClient
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import retrofit2.Retrofit
import retrofit2.converter.jackson.JacksonConverterFactory

object DingTalkOAuthManager : OAuthManager<OAuthConfig>() {
  override var platformKey = "DingTalk"
  override var logger: Logger = LoggerFactory.getLogger(DingTalkOAuthManager::class.java)
  private val apiService: DingTalkRemoteService by lazy {
    OkHttpClient.Builder().build().let {
      Retrofit.Builder()
        .baseUrl("https://api.dingtalk.com/")
        .addConverterFactory(JacksonConverterFactory.create(JsonHelper.mapper))
        .client(it).build()
    }.create(DingTalkRemoteService::class.java)
  }


  @Suppress("UNCHECKED_CAST")
  override fun readConfig(): DingTalkConfig =
    DingTalkConfig( BzConfigManager.getByPath("ScLogin", "ding")  as EntityValue? ?: HashMap())
  
  override fun fetchUserInfo(accessToken: String, code: String): UserInfo {
    val res = apiService.getUserInfo(token = accessToken)
      .execute().handleApiResponse<GetDingUserInfoRes>()?: throw BzError("errDingCallErr", 22)
    if (res.nick.isNullOrBlank() || res.unionId.isNullOrBlank()) {
      logger.error("Ding, failed to get user info $res")
      throw BzError("errDingCallErr", 22)
    }
    return UserInfo(res.unionId!!, res.nick!!)
  }

  override fun fetchAccessToken(code: String): AccessToken {
    val c = readConfig()
    if (c.dingClientID.isBlank() || c.dingClientSecret.isBlank()) throw BzError("errDingBadConfig")
    val res = apiService.getToken(GetTokenBody(clientId = c.dingClientID, clientSecret = c.dingClientSecret, code = code))
      .execute().handleApiResponse<GetDingTokenRes>()?: throw BzError("errDingCallErr", 12)

    if (res.accessToken.isNullOrBlank()) {
      logger.error("Ding, failed to get token ")
      throw BzError("errDingCallErr", 12)
    }
    return AccessToken(res.accessToken!!)
  }
}

