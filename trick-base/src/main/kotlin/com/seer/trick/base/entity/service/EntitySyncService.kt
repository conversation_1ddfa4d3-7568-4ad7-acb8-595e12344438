package com.seer.trick.base.entity.service

import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldScale
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.util.*

object EntitySyncService {

  private val logger: Logger = LoggerFactory.getLogger(javaClass)

  /**
   * update 很慢；sync update；所以采用加载，内存更新，全删，全插的方式
   */
  fun syncEntityComplete(entityName: String, req: SyncRequest) {
    // TODO 防止并发同步
    logger.info("Sync entity values completely [${entityName}][${req.bzType}][${req.txId}]")
    val start = System.currentTimeMillis()
    val record: EntityValue = mutableMapOf(
      "entityName" to entityName, "bzType" to req.bzType, "txId" to req.txId, "syncOn" to req.syncOn
    )

    try {
      doSyncEntityComplete(entityName, record, req.bzKeys, req.evList)
      record["success"] = true
    } catch (e: Exception) {
      logger.error("Sync error", e)
      record["success"] = false
      record["failedReason"] = e.message
    }
    val cost = System.currentTimeMillis() - start
    record["cost"] = cost
    logger.info("Sync done, $entityName, cost=$cost")

    EntityRwService.createOne("EntitySyncRecord", record)
  }

  private fun doSyncEntityComplete(
    entityName: String, record: EntityValue, bzKeys: List<String>?, evList: List<EntityValue>,
  ) {
    val syncCount = evList.size
    val oldCount: Long
    var updatedCount = 0L
    var createdCount = 0L
    val deletedCount: Long

    if (!bzKeys.isNullOrEmpty()) {
      // 有业务键
      val em = BaseCenter.mustGetEntityMeta(entityName)
      for (fieldName in bzKeys) {
        val fm = EntityHelper.mustGetFm(em, fieldName)
        if (fm.scale == FieldScale.List) throw BzError("errSyncKeyMultiple", fieldName)
      }
      val oldEvList = EntityRwService.findMany(entityName, Cq.all())
      logger.info("Current entity values ${oldEvList.size}")
      oldCount = oldEvList.size.toLong()

      val oldEvMap = mutableMapOf<String, EntityValue>()
      for (ev in oldEvList) {
        val key = evToKey(ev, bzKeys)
        if (oldEvMap.containsKey(key)) {
          logger.info("Key '$key' not unique in current entity values")
          throw BzError("errSyncKeyNotUniqInCurrent", key)
        }
        oldEvMap[key] = ev
      }

      val newEvMap = mutableMapOf<String, EntityValue>()
      for (ev in evList) {
        val key = evToKey(ev, bzKeys)
        if (newEvMap.containsKey(key)) {
          logger.info("Key '$key' not unique in new entity values")
          throw BzError("errSyncKeyNotUniqInNew", key)
        }
        newEvMap[key] = ev
      }

      val creatingEvList = mutableListOf<EntityValue>()

      for ((key, ev) in newEvMap) {
        val oldEv = oldEvMap.remove(key)
        if (oldEv != null) {
          // update
          updatedCount += 1
          // val id = EntityHelper.mustGetId(oldEv)
          oldEv.putAll(ev) // 内存更新
          creatingEvList.add(oldEv)
        } else {
          // create
          createdCount += 1
          creatingEvList.add(ev)
        }
      }

      deletedCount = oldEvMap.size.toLong()

      EntityRwService.removeMany(entityName, Cq.all())
      EntityRwService.createMany(entityName, creatingEvList)
    } else {
      oldCount = EntityRwService.removeMany(entityName, Cq.all())
      logger.info("Remove current entity values: $oldCount")
      deletedCount = oldCount
      val ids = EntityRwService.createMany(entityName, evList)
      createdCount = ids.size.toLong()
      logger.info("Create new entity values: $createdCount")
    }

    record["createdCount"] = createdCount
    record["updatedCount"] = updatedCount
    record["deletedCount"] = deletedCount
    record["syncCount"] = syncCount
    record["oldCount"] = oldCount
  }

  private fun evToKey(ev: EntityValue, keys: List<String>): String {
    return keys.joinToString("///") { fieldName ->
      val fv = ev[fieldName]
      if (fv is List<*>) "" else fv?.toString() ?: ""
    }
  }

}

class SyncRequest(
  val bzType: String = "",
  val txId: String = "",
  val bzKeys: List<String>? = null,
  val syncOn: Date = Date(),
  val keepId: Boolean = false,
  val evList: List<EntityValue> = emptyList()
)

