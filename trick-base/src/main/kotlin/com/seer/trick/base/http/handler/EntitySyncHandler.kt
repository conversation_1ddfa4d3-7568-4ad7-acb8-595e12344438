package com.seer.trick.base.http.handler

import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntitySyncService
import com.seer.trick.base.entity.service.SyncRequest
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.getReqBody
import com.seer.trick.base.http.operator
import com.seer.trick.base.user.PermissionManager
import io.javalin.http.Context
import java.util.*

object EntitySyncHandler {

  fun registerHandlers() {
    val c = Handlers("api/entity")
    c.post("sync", ::sync, auth())
  }

  private fun sync(ctx: Context) {
    val op = ctx.operator()

    val req: SyncReq = ctx.getReqBody()

    val em = BaseCenter.mustGetEntityMeta(req.entityName)

    if (!PermissionManager.pCreate(em, op)) throw Error403("没有创建权：${em.label}")
    if (!PermissionManager.pEdit(em, op)) throw Error403("没有编辑权：${em.label}")

    val entityValues = req.evList

    val sr = SyncRequest(req.bzType, req.txId, req.bzKeys, Date(), req.keepId, entityValues)
    EntitySyncService.syncEntityComplete(req.entityName, sr)

    ctx.status(200)
  }
}

data class SyncReq(
  val bzType: String = "",
  val txId: String = "",
  val bzKeys: List<String> = emptyList(),
  val keepId: Boolean = false,
  val entityName: String = "",
  val evList: List<EntityValue> = emptyList()
)