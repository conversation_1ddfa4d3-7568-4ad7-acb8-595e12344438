package com.seer.trick.base.script

import io.javalin.http.Context
import java.util.*
import java.util.concurrent.CopyOnWriteArrayList

object ScriptHttpServer {

  val handlers: MutableList<Handler> = CopyOnWriteArrayList()

  fun registerHandler(method: String, path: String, callback: String, auth: Boolean) {
    val fixedPath = if (!path.startsWith("/")) "/$path" else path
    handlers += Hand<PERSON>(method.uppercase(Locale.getDefault()), fixedPath, callback, auth)
  }

  // not for script
  fun handle(ctx: Context): Boolean {
    val method = ctx.method().name.uppercase(Locale.getDefault())
    val path = ctx.path()
    for (handler in handlers) {
      if (handler.method == method && path.matches(Regex(handler.path))) {
        // 因为一个 Context 不能被多个线程使用，所以这里要新创建一个
        ScriptCenter.execute(ScriptExeRequest(handler.callback, arrayOf(HandleContext(ctx))))
        return true
      }
    }
    return false
  }
}

data class Handler(val method: String, val path: String, val callback: String, val auth: Boolean = false)

class HandleContext(private val ctx: Context) {

  fun getUrl() = ctx.url()

  fun getBodyAsString() = ctx.body()

  fun getQueryParam(name: String) = ctx.queryParam(name)

  fun getHeader(name: String) = ctx.header(name)

  fun setStatus(status: Int) {
    ctx.status(status)
  }

  fun setHeader(name: String, value: String) {
    ctx.header(name, value)
  }

  fun setJson(result: Any) {
    ctx.json(result)
  }

  fun setText(result: String) {
    ctx.result(result)
  }
}