package com.seer.trick.base.user

import com.seer.trick.ComplexQuery

data class Operator(
  @JvmField
  val userId: String,
  @JvmField
  val username: String,
  @JvmField
  val admin: <PERSON>olean = false,
  @JvmField
  val anonymous: Boolean = false,
  @JvmField
  val agent: Boolean = false,
  @JvmField
  val permissions: PermissionFieldValue? = null,
) {
  
  companion object {
    
    private val operatorHolder = ThreadLocal<Operator>()
    
    fun current(): Operator? {
      return operatorHolder.get()
    }
    
    fun setCurrent(operator: Operator?) {
      operatorHolder.set(operator)
    }
    
  }
  
}


class PermissionFieldValue(
  val actions: MutableSet<String> = HashSet(),
  val menu: MutableMap<String, Boolean> = HashMap(),
  // val entity: MutableMap<String, MutableMap<String, Boolean>> = HashMap(), // entityName -> action
  val entityField: MutableMap<String, MutableMap<String, EntityFieldPermission>> = HashMap(), // entityName -> field
  val entityRow: MutableMap<String, String> = HashMap(), // entityName ->
  val entityRowObj: MutableMap<String, ComplexQuery> = HashMap(), // entityName -> // TODO skip serialization
)

enum class EntityFieldPermission {
  Disabled,
  Read,
  Edit,
}