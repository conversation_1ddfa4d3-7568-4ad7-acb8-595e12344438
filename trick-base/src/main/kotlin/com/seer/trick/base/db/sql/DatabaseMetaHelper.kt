package com.seer.trick.base.db.sql

import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import java.sql.DatabaseMetaData

/**
 * 为兼容，表名列名都小写
 */
object DatabaseMetaHelper {
  
  private val logger = LoggerFactory.getLogger(this::class.java)
  
  fun parse(sc: SmartConnection, dmd: DatabaseMetaData): Map<String, TableDm> {
    val tableNames = SqlSchemaManager.listTableNames(sc)
    // logger.info("现有表数：${tableNames.size}。表名：$tableNames")
    val catalog = StringUtils.firstNonBlank(sc.connection.catalog, sc.connection.schema)
    
    val tables: MutableMap<String, TableDm> = HashMap()
    for (tableName in tableNames) {
      val columns: MutableMap<String, ColumnDm> = HashMap()
      
      // 传数据库名，防止把其它数据库的列查出来
      val columnRs = dmd.getColumns(catalog, null, tableName, null)
      while (columnRs.next()) {
        val name = columnRs.getString("COLUMN_NAME")
        val type = columnRs.getString("TYPE_NAME").uppercase() // 类型名大写！
        val length = columnRs.getInt("COLUMN_SIZE")
        val width = columnRs.getInt("COLUMN_SIZE")
        val scale = columnRs.getInt("DECIMAL_DIGITS")
        
        columns[name] = ColumnDm(name, type, length, width, scale)
        
        //        val cc = columnRs.metaData.columnCount
        //        if (cc == 0) continue
        //        for (i in 1..cc) {
        //          val cn = columnRs.metaData.getColumnName(i)
        //          val cv = columnRs.getString(i)
        //          logger.info("$i / $cc: $cn : $cv")
        //        }
      }
      
      // 索引用小写！
      tables[tableName.lowercase()] = TableDm(tableName, columns)
    }
    
    return tables
  }
}

data class DbDm(
  val tables: Map<String, TableDm>
)

data class TableDm(
  val name: String,
  val columns: Map<String, ColumnDm>
)

data class ColumnDm(
  val name: String,
  val type: String,
  val length: Int?,
  val width: Int?,
  val scale: Int?,
)