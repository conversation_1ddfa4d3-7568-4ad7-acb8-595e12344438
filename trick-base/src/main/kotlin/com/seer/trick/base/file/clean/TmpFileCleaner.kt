package com.seer.trick.base.file.clean

import com.seer.trick.base.concurrent.PollingJobManager
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.file.FileManager
import com.seer.trick.helper.NumHelper
import org.apache.commons.lang3.time.DateUtils
import org.slf4j.LoggerFactory
import java.io.File
import java.util.*
import kotlin.system.measureTimeMillis

/**
 * 清理 tmp 目录下的文件
 *
 * 默认保存 7 天。超过 7 的清理。
 * 注意可能有子文件。只关注父文件夹的文件的时间，超过 7 天清理。如果文件夹为空，删除文件夹。
 * 临时文件是 FileManager#ensureTmpDir 目录下的所有文件。
 */
object TmpFileCleaner {

  private val logger = LoggerFactory.getLogger(javaClass)

  fun init() {
    PollingJobManager.submit(
      threadName = "TmpFileCleaner",
      remark = "Clean tmp files",
      interval = { 60 * 60 * 1000L }, // 一小时处理一次
      logger = logger,
      workerMaxTime = -1,
      stopCondition = { false },
      exceptionContinue = true,
      tags = emptySet(),
      worker = ::clean,
    )
  }

  fun clean() {
    
    try {
      val cost = measureTimeMillis {
        val day = getRemainDays() // 配置的临时文件保留天数
        val d7 = DateUtils.addDays(Date(), -1 * day)
        val d1 = DateUtils.addDays(Date(), -1)

        val tmpDir = FileManager.ensureTmpDir()
        if (!tmpDir.exists() && !tmpDir.isDirectory) return

        cleanFiles(tmpDir, d7, d1)
      }
      logger.info("clean tmp file cost $cost")
    } catch (e: InterruptedException) {
      logger.error("clean tmp file interrupted", e)
      throw e
    } catch (e: Throwable) {
      logger.error("clean tmp file error", e)
    }
  }

  /**
   * 清理指定 file 及其所有子目录
   *
   * @param d7 是配置的临时文件保留天数，不是固定 7 天。
   */
  private fun cleanFiles(file: File, d7: Date, d1: Date) {
    if (!file.exists()) return
    if (file.isFile) {
      if (file.lastModified() < d7.time) {
        file.delete()
      }
    } else if (file.isDirectory) {
      val subFiles = file.listFiles()
      if (subFiles.isNullOrEmpty()) {
        // 删除空文件夹。不能直接删当天的，可能正在往里面复制文件
        if (file.lastModified() < d1.time) file.delete()
      } else {
        for (f in subFiles) {
          cleanFiles(f, d7, d1)
        }
      }
    }
  }

  private fun getRemainDays(): Int {
    var day = NumHelper.anyToInt(BzConfigManager.getByPath("File", "tmpDay")) ?: 7
    if (day <= 0) day = 7
    return day
  }
}