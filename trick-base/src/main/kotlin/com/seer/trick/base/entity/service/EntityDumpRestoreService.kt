package com.seer.trick.base.entity.service

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.extension.EntityServiceExtensions
import com.seer.trick.base.file.FileManager
import com.seer.trick.base.http.WebSocketManager
import com.seer.trick.base.http.WsMsg
import com.seer.trick.helper.DateHelper
import com.seer.trick.helper.FileHelper
import com.seer.trick.helper.JsonFileHelper
import com.seer.trick.helper.JsonHelper
import org.apache.commons.io.FileUtils
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import java.io.BufferedReader
import java.io.File
import java.io.FileReader
import java.io.FileWriter
import java.nio.charset.StandardCharsets
import java.util.*
import kotlin.math.ceil
import kotlin.math.min


/**
 * 本质不是数据库导入导出，是数据导入导出
 */
object EntityDumpRestoreService {
  
  private val logger = LoggerFactory.getLogger(javaClass)
  
  @Volatile
  var processing = false
  
  private const val READ_PAGE_SIZE = 1000
  
  private const val INSERT_PAGE_SIZE = 1000
  
  private val skipRestoreEmNames = setOf("HumanUser", "UserRole", "HumanUserSession")
  
  /**
   * 返回文件路径
   */
  fun dump(): String {
    synchronized(this) {
      if (processing) throw BzError("errDbProcessing")
      processing = true
    }
    try {
      val dir = FileManager.nextTmpFile()
      dir.mkdirs()
      
      // 未禁用，不是组件
      val emList = BaseCenter.entityMetaMap.values.filter {
        !it.disabled && !BaseCenter.componentEmSet.contains(it.name)
      }
      val emNum = emList.size
      for ((emIndex, em) in emList.withIndex()) {
        val file = File(dir, "${em.name}.json")
        
        val total = EntityRwService.count(em.name, Cq.all())
        if (total == 0L) continue
        
        val pageNum = ceil(total.toDouble() / READ_PAGE_SIZE).toInt()
        
        FileWriter(file, StandardCharsets.UTF_8, false).use { writer ->
          var finalId = ""
          for (pageNo in 1..pageNum) {
            val limit = READ_PAGE_SIZE
            val evList = EntityRwService.findManyWithoutCache(
              em.name, if (finalId.isBlank()) Cq.all() else Cq.gt("id", finalId),
              FindOptions(limit = limit, sort = listOf("+id"))
            )
            // 一行一个业务对象
            for (ev in evList) {
              writer.write(JsonHelper.writeValueAsString(ev))
              writer.write("\n")
            }
            finalId = evList.last()["id"].toString()
            val finished = min(pageNo * READ_PAGE_SIZE.toLong(), total)
            val msg = mapOf(
              "entityName" to em.label,
              "entityIndex" to emIndex,
              "entityTotal" to emNum,
              "evTotal" to total,
              "evFinished" to finished
            )
            logger.debug("导出业务对象：${em.label}，${emIndex + 1}/${emNum} 业务对象, ${finished}/$total 条")
            WebSocketManager.sendAllAsync(WsMsg("DbDump", JsonHelper.mapper.writeValueAsString(msg)))
          }
        }
      }
      
      val dbConfig = BaseCenter.baseConfig.db
      val info = mapOf(
        "version" to "2.0",
        "createdOn" to DateHelper.formatDate(Date(), "yyyy-MM-dd'T'HH:mm:ss.SSSZ"),
        "dbType" to dbConfig.type,
        "dbName" to dbConfig.dbName
      )
      val infoFile = File(dir, "info.txt")
      JsonFileHelper.writeJsonToFile(infoFile, info, true)
      // FileUtils.write(infoFile, JsonHelper.writeValueAsString(info), StandardCharsets.UTF_8)
      
      val time = DateHelper.formatDate(Date(), "yyyyMMdd-HHmmss-SSS")
      val zipFile = File(FileManager.ensureTmpDir(), "db-${dbConfig.type}-${dbConfig.dbName}-$time.zip")
      FileHelper.zipDirToFile(dir, zipFile)
      FileUtils.deleteDirectory(dir)
      return FileManager.fileToPath(zipFile)
    } finally {
      processing = false
    }
  }
  
  fun restore(dir: File, clear: Boolean) {
    synchronized(this) {
      if (processing) throw BzError("errDbProcessing")
      processing = true
    }
    
    try {
      val infoFile = File(dir, "info.txt")
      if (!infoFile.exists()) throw BzError("errDbRestoreNoInfo")
      
      val infoStr = FileUtils.readFileToString(infoFile, StandardCharsets.UTF_8)
      logger.info("导入数据：$infoStr")
      
      if (clear) {
        // 先全删除
        val emNum = BaseCenter.entityMetaMap.size
        for ((emIndex, em) in BaseCenter.entityMetaMap.values.withIndex()) {
          if (em.disabled) continue
          if (skipRestoreEmNames.contains(em.name)) continue
          
          val removeCount = EntityRwService.removeAll(em.name)
          logger.debug("导入业务对象，删除已有：{}，{}", em.name, removeCount)
          
          val msg = mapOf(
            "type" to "remove",
            "emName" to em.name,
            "emIndex" to emIndex,
            "emNum" to emNum,
          )
          WebSocketManager.sendAllAsync(WsMsg("DbRestore", JsonHelper.mapper.writeValueAsString(msg)))
        }
      }
      
      val list = dir.list()!!
      val fileNum = list.size
      for ((fileIndex, filename) in list.withIndex()) {
        if (!filename.endsWith(".json")) continue
        val emName = StringUtils.substringBeforeLast(filename, ".json")
        if (skipRestoreEmNames.contains(emName)) continue
        val em = BaseCenter.entityMetaMap[emName] ?: continue
        // 如果是组件，跳过
        if (BaseCenter.componentEmSet.contains(emName)) continue
        
        val evList: MutableList<EntityValue> = ArrayList(INSERT_PAGE_SIZE)
        var evNum = 0
        val file = File(dir, filename)
        BufferedReader(FileReader(file, StandardCharsets.UTF_8)).use { reader ->
          var line = reader.readLine()
          while (line != null) {
            val ev: EntityValue = JsonHelper.mapper.readValue(line, jacksonTypeRef())
            evList += ev
            ++evNum
            
            // 修正 version 字段
            ev[FieldMeta.FIELD_VERSION] = ev[FieldMeta.FIELD_VERSION] ?: 0
            
            if (evList.size == INSERT_PAGE_SIZE) {
              doInsert(em, evList, fileIndex, fileNum, evNum)
            }
            
            line = reader.readLine()
          }
          if (evList.isNotEmpty()) {
            doInsert(em, evList, fileIndex, fileNum, evNum)
          }
        }
        
        EntityServiceExtensions.afterBigChange(em)
      }
      
      // 请缓存
      EntityRwService.cache.clearAll()
    } finally {
      processing = false
    }
  }
  
  // 小心：里面会 clear evList
  private fun doInsert(em: EntityMeta, evList: MutableList<EntityValue>, fileIndex: Int, fileNum: Int, evNum: Int) {
    // 直接调用 create many 会触发清缓存，就先这样
    // 会触发生命周期扩展，应该触发
    EntityRwService.createMany(em.name, evList, CreateOptions(muteExt = true, keepDate = true))
    evList.clear()
    
    logger.debug("导入业务对象 ${em.name}，${fileIndex + 1}/${fileNum} 文件，已导入 $evNum 条")
    
    val msg = mapOf(
      "type" to "insert",
      "emName" to em.name,
      "fileIndex" to fileIndex,
      "fileNum" to fileNum,
      "evNum" to evNum
    )
    WebSocketManager.sendAllAsync(WsMsg("DbRestore", JsonHelper.mapper.writeValueAsString(msg)))
  }
  
}