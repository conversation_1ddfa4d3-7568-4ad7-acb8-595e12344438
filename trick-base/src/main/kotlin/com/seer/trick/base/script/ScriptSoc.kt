package com.seer.trick.base.script

import com.fasterxml.jackson.databind.JsonNode
import com.seer.trick.helper.JsonHelper
import com.seer.trick.base.soc.SocAttention
import com.seer.trick.base.soc.SocNode
import com.seer.trick.base.soc.SocService

object ScriptSoc {
  
  fun getNode(nodeId: String): SocNode? {
    return SocService.getNode(nodeId)
  }
  
  fun updateStringNode(id: String, desc: String, value: String, attention: String) {
    updateNode(id, desc, value, attention)
  }
  
  fun updateJsonNode(id: String, desc: String, value: Any?, attention: String) {
    val jv: JsonNode? = if (value == null) null else JsonHelper.mapper.valueToTree(value)
    updateNode(id, desc, jv, attention)
  }
  
  fun updateIntNode(id: String, desc: String, value: Long, attention: String) {
    updateNode(id, desc, value, attention)
  }
  
  private fun updateNode(id: String, desc: String, value: Any?, attention: String) {
    SocService.updateNode("扩展", id, desc, value, SocAttention.valueOf(attention))
  }
  
  fun removeNode(nodeId: String) {
    SocService.removeNode(nodeId)
  }
  
}