package com.seer.trick.base.alarm

import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

/**
 * 系统告警
 */
object AlarmService {

  private val logger = LoggerFactory.getLogger(javaClass)

  // by name
  private val groups: MutableMap<String, AlarmGroup> = HashMap()

  // by key
  private val items: MutableMap<String, AlarmItemInternal> = HashMap()

  private val cleanExecutor = Executors.newSingleThreadExecutor()

  // by key
  private val responses: MutableMap<String, AlarmChoice> = ConcurrentHashMap()

  private val lock = ReentrantLock()
  private val condition = lock.newCondition()

  init {
    cleanExecutor.submit(::cleanLoop)
  }

  /**
   * 告警组必须先注册，建议在模块的 init 方法里。
   */
  @Synchronized
  fun registerGroup(g: AlarmGroup) {
    groups[g.name] = g
  }

  /**
   * 添加一个告警项。
   * 如果有相同 key 的告警项，会被合并，并增加错误计数。
   */
  @Synchronized
  fun addItem(
    item: AlarmItem,
    ttl: Long? = null, // 存活时间，单位毫秒，从创建开始计算，超时被删除。null 表示永久存活。
    callback: AlarmCallback? = null, // 后端处理回调
  ) {
    val oldItem = items[item.key]
    val item2 = if (oldItem != null) {
      item.copy(count = oldItem.item.count + item.count)
    } else {
      item
    }
    items[item.key] = AlarmItemInternal(item2, ttl, callback)
  }

  /**
   * 清除指定告警项
   */
  @Synchronized
  fun removeItem(key: String) {
    items.remove(key)
  }

  /**
   * 按组清除
   */
  @Synchronized
  fun removeAllByGroup(group: String) {
    val keys = items.values.filter { it.item.group == group }.map { it.item.key }
    for (key in keys) items.remove(key)
  }

  /**
   * 按 code 清除
   */
  @Synchronized
  fun removeAllByCode(code: String) {
    val keys = items.values.filter { it.item.code == code }.map { it.item.key }
    for (key in keys) items.remove(key)
  }

  /**
   * 按 sceneId 清除
   */
  @Synchronized
  fun removeAllBySceneId(sceneId: String) {
    val keys = items.values.filter { it.item.sceneId == sceneId }.map { it.item.key }
    for (key in keys) items.remove(key)
  }

  /**
   * 按 sceneId + code 清除
   */
  @Synchronized
  fun removeAllBySceneIdAndCode(sceneId: String, code: String) {
    val keys = items.values.filter { it.item.sceneId == sceneId && it.item.code == code }.map { it.item.key }
    for (key in keys) items.remove(key)
  }

  @Synchronized
  fun listGroups() = groups.values.toList()

  @Synchronized
  fun listItems() = items.map { it.value.item }.toList()

  /**
   * 添加告警项，并阻塞直到用户做出处理，返回用户的处理
   */
  fun request(
    ai: AlarmItem,
    ttl: Long? = null, // 存活时间
    callback: AlarmCallback? = null, // 后端处理回调
    cancelChecker: (() -> Boolean)? = null, // 取消检查，返回 true 终止阻塞等待用户
  ): Int? {
    // logger.debug("Alarm request: {}", ai)
    addItem(ai, ttl, callback)

    val res = awaitRes(ai.key, cancelChecker)
    return res?.actionIndex
  }

  private fun awaitRes(
    key: String,
    cancelChecker: (() -> Boolean)? = null, // 取消检查
  ): AlarmChoice? {
    while (true) {
      // 被删除了
      if (!items.containsKey(key)) return null

      // 被调用者取消
      if (cancelChecker?.invoke() == true) return null

      lock.withLock {
        try {
          condition.await(1, TimeUnit.SECONDS)
        } catch (e: InterruptedException) {
          return null
        }
      }

      return responses.remove(key) ?: continue
    }
  }

  /**
   * 用户做出选择：处理告警
   */
  fun makeChoice(choice: AlarmChoice) {
    val item = items[choice.key]
    if (item != null) {
      responses[choice.key] = choice
      item.callback?.invoke(choice.actionIndex, item.item.args ?: emptyList())
    }
    lock.withLock {
      condition.signalAll()
    }
  }

  /**
   * 删除过期告警
   */
  private fun cleanLoop() {
    Thread.currentThread().name = "SysAlm"
    while (true) {
      val now = System.currentTimeMillis()
      try {
        synchronized(this) {
          // 不会太多，暂时不优化性能
          val keys = items.keys.toList()
          for (key in keys) {
            val ii = items[key] ?: continue
            if (ii.ttl != null && (now - ii.item.timestamp) > ii.ttl) items.remove(key)
          }
        }
      } catch (e: Exception) {
        logger.error("Alarm clean", e)
      }
      Thread.sleep(1000)
    }
  }

  /**
   *  根据标签删除告警
   */
  fun removeByTag(tag: String) {
    val keys = items.keys.toList()
    for (key in keys) {
      val alarmItem = items[key] ?: continue
      if (alarmItem.item.tags != null && alarmItem.item.tags.contains(tag)) {
        logger.info("remove alarm '${alarmItem.item.key}' by tag '$tag'")
        items.remove(key)
      }
    }
  }
}

data class AlarmGroup(val name: String, val label: String, val displayOrder: Int)

/**
 * group 必须填已注册的组的 name。
 * actions 指定一个或多个处理操作，label 会作为按钮文本显示，用户点击按钮后执行操作。
 * 如果 uiAction 有值，则调用前端的处理逻辑。否则调用后端，args 会被传回后端。
 */
data class AlarmItem(
  val sceneId: String? = null, // 场景 ID，非必填，比如后台任务的告警
  val group: String,
  val code: String, // 错误码
  val key: String, // 必须唯一
  val level: AlarmLevel,
  val message: String,
  val count: Int = 1, // 发生次数
  val timestamp: Long = System.currentTimeMillis(),
  val args: List<Any?>? = null,
  val actions: List<AlarmAction>? = null, // 处理按钮
  val tags: Set<String>? = null, // 标签
)

class AlarmItemInternal(
  val item: AlarmItem,
  val ttl: Long? = null, // 存活时间
  val callback: AlarmCallback? = null, // 后端处理回调
)

data class AlarmAction(
  val label: String,
  val uiAction: String? = null, // 调用前端功能
  val confirmMsg: String? = null, // 界面点击这个按钮时的确认消息
)

enum class AlarmLevel {
  Info,
  Warning,
  Error,
}

/**
 * 用户做出处理后，给出用户做的是哪个处理（actionIndex）和参数。
 */
typealias AlarmCallback = (actionIndex: Int, args: List<Any?>) -> Unit

data class AlarmChoice(val key: String, val actionIndex: Int)