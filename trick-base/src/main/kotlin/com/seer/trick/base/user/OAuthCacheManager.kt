package com.seer.trick.base.user

import java.util.*

abstract class OAuthCacheManager<T : OAuthConfig>: OAuthManager<T>() {
  private val accessTokenCache: MutableMap<String, AccessTokenHolder> = mutableMapOf()
   override fun getAccessToken(code: String): String {
    val cachedToken = accessTokenCache[platformKey]
    return if (cachedToken != null && cachedToken.expiredWhen.after(Date())) {
       cachedToken.token
    }else {
      val response = fetchAccessToken(code)
      val newToken = AccessTokenHolder(response.accessToken,
        Date(System.currentTimeMillis() + response.expiresIn * 1000))
      accessTokenCache[platformKey] = newToken
      newToken.token
    }
  }
  data class AccessTokenHolder(val token: String, val expiredWhen: Date)
}
