package com.seer.trick.base.user.wwx

import com.seer.trick.BzError
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.user.OAuthCacheManager
import com.seer.trick.base.user.OAuthConfig
import com.seer.trick.helper.JsonHelper
import okhttp3.OkHttpClient
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import retrofit2.Retrofit
import retrofit2.converter.jackson.JacksonConverterFactory

object WwxOAuthManager : OAuthCacheManager<OAuthConfig>() {
  override var platformKey = "WorkWeiXin"
  override var logger: Logger = LoggerFactory.getLogger(WwxOAuthManager::class.java)
  private val apiService: WwxRemoteService by lazy {
    OkHttpClient.Builder().build().let {
      Retrofit.Builder()
        .baseUrl("https://qyapi.weixin.qq.com/")
        .addConverterFactory(JacksonConverterFactory.create(JsonHelper.mapper))
        .client(it).build()
    }.create(WwxRemoteService::class.java)
  }

  @Suppress("UNCHECKED_CAST")
  override fun readConfig(): WwxConfig =
    WwxConfig( BzConfigManager.getByPath("ScLogin", "wwx")  as EntityValue? ?: HashMap())

  override fun fetchUserInfo(accessToken: String, code: String): UserInfo {
    val res = apiService.getUserInfo(accessToken = accessToken, code = code)
      .execute().handleApiResponse<GetUserInfoRes>()?: throw BzError("errWwxCallErr", 22)

    if (res.errCode != 0) {
      logger.error("Wwx, failed to get user info ${res.errCode}, ${res.errMsg}")
      throw BzError("errWwxCallErr", 22)
    }
    val userId = res.userId
    if (userId.isNullOrBlank()) throw BzError("errWwxCallErr", 23)

    val userRes = apiService.getUserDetails(
      accessToken = accessToken,
      userId = userId
    ).execute().handleApiResponse<GetUserDetailsRes>()

    if (userRes?.errCode != 0) {
      logger.error("Wwx, failed to get user details ${userRes?.errCode}, ${userRes?.errMsg}")
      throw BzError("errWwxCallErr", 25)
    }
    return UserInfo(userId, userRes.name!!)
  }

  override fun fetchAccessToken(code: String): AccessToken {
    val c = readConfig()
    if (c.wwxCorpId.isBlank() || c.wwxCorpSecret.isBlank()) throw BzError("errWwxBadConfig")
    val res = apiService.getToken(corpId = c.wwxCorpId, corpSecret = c.wwxCorpSecret)
      .execute().handleApiResponse<GetTokenRes>()?: throw BzError("errWwxCallErr", 12)

    if (res.errCode != 0 || res.accessToken.isNullOrBlank()) {
      logger.error("Wwx, failed to get token ${res.errCode}, ${res.errMsg}")
      throw BzError("errWwxCallErr", 12)
    }
    return AccessToken(res.accessToken!!, res.expiresIn)
  }
}