package com.seer.trick.base.script

import com.seer.trick.base.http.WebSocketManager
import com.seer.trick.base.http.WsMsg
import com.seer.trick.helper.JsonHelper

object ScriptUi {
  
  /**
   * 在指定工位电脑上，打开业务对象的界面
   */
  fun openEntityViewPage(workSite: String, entityName: String, evId: String, mode: String) {
    WebSocketManager.sendAllAsync(
      WsMsg(
        "OpenEntityViewPage",
        JsonHelper.mapper.writeValueAsString(
          mapOf(
            "workSite" to workSite,
            "entityName" to entityName,
            "evId" to evId,
            "mode" to mode
          )
        )
      )
    )
  }
  
}