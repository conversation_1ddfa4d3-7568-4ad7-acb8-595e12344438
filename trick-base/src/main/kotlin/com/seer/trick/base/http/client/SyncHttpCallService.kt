package com.seer.trick.base.http.client

/**
 * 向第三方发起同步 HTTP 请求
 */
object SyncHttpCallService {

  /**
   * 发起同步调用，返回结果。允许重试。
   * 超过最大重试次数后，返回最后一次响应。
   * 如果被中断，抛异常。
   */
  fun call(
    req: HttpRequest, okChecker: HttpResultOkChecker? = null, o: CallRetryOptions? = null
  ): HttpResult {
    var retryNum = 0

    // TODO 记录 SOC；记录第三方调用日志

    while (true) {
      try {
        var res = HttpClientBase.request(req)
        res = checkAndWrapRes(res, okChecker)
        if (res.successful && (okChecker == null || res.checkRes == true)) return res

        ++retryNum
        if (o != null && retryNum < o.maxRetryNum) {
          var delay = o.retryDelay
          if (delay <= 0) delay = 3000L
          Thread.sleep(delay)
        } else {
          return res // 直接返回最后一次响应
        }
      } catch (e: InterruptedException) {
        // 应该只抛中断，其他异常不应该抛出
        throw e
      }
    }
  }

  private fun checkAndWrapRes(res: HttpResult, okChecker: HttpResultOkChecker? = null): HttpResult =
    if (okChecker == null) res else res.copy(checkRes = okChecker(res))

}