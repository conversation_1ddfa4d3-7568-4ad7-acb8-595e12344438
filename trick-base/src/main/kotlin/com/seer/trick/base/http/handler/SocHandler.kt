package com.seer.trick.base.http.handler

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.http.WebSocketManager
import com.seer.trick.base.http.WebSocketSubscriber
import com.seer.trick.base.http.WsEnv
import com.seer.trick.base.http.WsMsg
import com.seer.trick.base.soc.SocService
import com.seer.trick.base.soc.SysMonitorService
import com.seer.trick.helper.DateHelper
import com.seer.trick.helper.JsonHelper
import io.javalin.websocket.WsMessageContext
import org.apache.commons.lang3.StringUtils
import org.apache.commons.lang3.time.DateUtils
import java.io.BufferedReader
import java.io.File
import java.io.FileReader
import java.nio.charset.Charset
import java.util.*
import java.util.regex.Pattern

object SocHandler : WebSocketSubscriber() {

  fun registerHandlers() {
    WebSocketManager.subscribers += this
  }

  override fun onMessage(ctx: WsMessageContext, msg: WsMsg, env: WsEnv) {
    if (msg.action == "SocQuery") {
      ctx.send(WsMsg("SocQuery::Reply", SocService.serialize() ?: ""))
    } else if (msg.action == "SysMonQuery") {
      ctx.send(WsMsg("SysMon::Reply", SysMonitorService.serialize() ?: ""))
    } else if (msg.action == "SysMonLogQuery") {
      val req: LogQueryReq = JsonHelper.mapper.readValue(msg.content!!, jacksonTypeRef())
      logQuery(req, ctx)
    }
  }

  private fun logQuery(req: LogQueryReq, ctx: WsMessageContext) {
    val subjects = req.subject?.split(" ")?.filter(String::isNotBlank)
    val targets = req.target?.split(" ")?.filter(String::isNotBlank)
    val fields = req.field?.split(" ")?.filter(String::isNotBlank)
    val keywords = req.keyword?.split(" ")?.filter(String::isNotBlank)

    ctx.session.remote.sendPartialString("SysMonLogReply||", false)

    try {
      val files = toFiles(req.from, req.to)
      for (file in files) {
        BufferedReader(FileReader(file, Charset.forName("UTF-8"))).use { br ->
          var line = br.readLine()
          while (line != null) {
            if (filterLine(line, req.from, req.to, subjects, targets, fields, keywords)) {
              ctx.session.remote.sendPartialString(line + "\n", false)
            }
            line = br.readLine()
          }
        }
      }
    } finally {
      ctx.session.remote.sendPartialString("", true)
    }
  }

  // 按时间过滤日志
  private fun toFiles(from: Date?, to: Date?): List<File> {
    val fromDt: Date
    val toDt: Date
    // 如果都不传，今天；如果只传一个，最多允许三天内
    if (from == null && to == null) {
      fromDt = DateUtils.truncate(Date(), Calendar.DATE)
      toDt = DateUtils.addDays(fromDt, 1)
    } else if (from == null) {
      toDt = DateUtils.addDays(DateUtils.truncate(to, Calendar.DATE), 1)
      fromDt = DateUtils.addDays(to, -3)
    } else if (to == null) {
      fromDt = DateUtils.truncate(from, Calendar.DATE)
      toDt = DateUtils.addDays(from, 3)
    } else {
      fromDt = DateUtils.truncate(from, Calendar.DATE)
      toDt = DateUtils.addDays(DateUtils.truncate(to, Calendar.DATE), 1)
    }

    val today = DateUtils.truncate(Date(), Calendar.DATE)
    val todayIncluded = today >= fromDt && today < toDt

    val logsDir = BaseCenter.baseConfig.logsDir // TODO log 存放路径
    if (!logsDir.exists()) return emptyList()

    val files = logsDir.listFiles()?.sorted() ?: return emptyList()
    val pattern = Pattern.compile("sys-mon-(\\d{4}-\\d{2}-\\d{2}).*.log")
    return files.filter { file ->
      val name = file.name
      if (name == "sys-mon.log" && todayIncluded) return@filter true
      if (!name.startsWith("sys-mon")) return@filter false
      val matcher = pattern.matcher(name)
      if (!matcher.matches()) return@filter false
      val dStr = matcher.group(1)
      val d = DateHelper.anyToDate(dStr) ?: return@filter false
      val ds = DateUtils.truncate(d, Calendar.DATE)
      ds >= fromDt && ds < toDt
    }
  }

  private fun filterLine(
    line: String,
    from: Date?,
    to: Date?,
    subjects: List<String>?,
    targets: List<String>?,
    fields: List<String>?,
    keywords: List<String>?,
  ): Boolean {
    if (line.isBlank()) return false

    val parts = StringUtils.split(line, "|", 8)
    val timestamp = DateUtils.parseDate(parts[0], SysMonitorService.TIMESTAMP_FORMAT)

    if (from != null && timestamp.before(from)) return false
    if (to != null && timestamp.after(to)) return false

    val subject = parts[4]
    if (!subjects.isNullOrEmpty() && !subjects.any { subject == it }) return false

    val target = parts[5]
    if (!targets.isNullOrEmpty() && !targets.any { target == it }) return false

    val field = parts[6]
    if (!fields.isNullOrEmpty() && !fields.any { field == it }) return false

    if (!keywords.isNullOrEmpty() && !keywords.any { line.contains(it) }) return false

    return true
  }

  data class LogQueryReq(
    val subject: String? = null,
    val target: String? = null,
    val field: String? = null,
    val keyword: String? = null,
    val from: Date? = null,
    val to: Date? = null,
  )
}