package com.seer.trick.base.entity.cache

import com.seer.trick.base.entity.EntityValue
import java.util.concurrent.ConcurrentHashMap

class EntityIdKeyCache {

  // entity -> id -> key
  private val cache: MutableMap<String, MutableMap<String, MutableMap<String, CachedValue<EntityValue>>>> =
    ConcurrentHashMap()

  fun get(entityName: String, id: String, key: String): CachedValue<EntityValue>? {
    val c1 = cache[entityName] ?: return null
    val c2 = c1[id] ?: return null
    return c2[key]
  }

  @Synchronized
  operator fun set(entityName: String, id: String, key: String, v: EntityValue?) {
    var c1 = cache[entityName]
    if (c1 == null) {
      c1 = ConcurrentHashMap()
      cache[entityName] = c1
    }
    var c2 = c1[id]
    if (c2 == null) {
      c2 = ConcurrentHashMap()
      c1[entityName] = c2
    }
    c2[key] = CachedValue(v, System.currentTimeMillis())
  }

  fun clear() {
    cache.clear()
  }

  fun remove(entityName: String) {
    cache.remove(entityName)
  }

  fun clearByIds(entityName: String, ids: List<String>?) {
    if (ids == null) {
      this.remove(entityName)
    } else {
      val c1 = cache[entityName] ?: return
      for (id in ids) c1.remove(id)
    }
  }

  fun cleanOld(before: Long) {
    for (c1 in cache.values) {
      for (c2 in c1.values) {
        val iter = c2.entries.iterator()
        while (iter.hasNext()) {
          val item = iter.next()
          if (item.value.createdOn < before) iter.remove()
        }
      }
    }
  }

}
