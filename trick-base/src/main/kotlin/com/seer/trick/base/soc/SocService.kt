package com.seer.trick.base.soc

import com.seer.trick.helper.JsonHelper
import java.util.*
import java.util.concurrent.ConcurrentHashMap

object SocService {

  private val nodeMap: MutableMap<String, SocNode> = ConcurrentHashMap()

  fun clear() {
    nodeMap.clear()
  }

  fun list(): Collection<SocNode> = nodeMap.values

  fun getNode(id: String): SocNode? = nodeMap[id]

  // fun updateNode(node: SocNode) {
  //   nodeMap[node.id] = node
  // }

  fun updateNode(
    group: String,
    id: String,
    description: String,
    value: Any?,
    attention: SocAttention = SocAttention.None,
  ) {
    nodeMap[id] = SocNode(group, id, description, value, attention)
  }

  fun removeNode(id: String) {
    nodeMap.remove(id)
  }

  fun serialize(): String? = JsonHelper.mapper.writeValueAsString(nodeMap.values)
}

data class SocNode(
  val group: String,
  val id: String,
  val description: String = "",
  val value: Any?,
  val attention: SocAttention = SocAttention.None,
  val modifiedReason: String = "",
  val modifiedTimestamp: Date = Date(),
)

/**
 * 关注级别，三色灯模式
 */
enum class SocAttention {
  None,
  Green,
  Yellow,
  Red,
}