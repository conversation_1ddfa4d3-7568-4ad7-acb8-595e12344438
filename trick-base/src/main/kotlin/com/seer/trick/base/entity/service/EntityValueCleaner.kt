package com.seer.trick.base.entity.service

import com.seer.trick.BzError
import com.seer.trick.ComplexQuery
import com.seer.trick.ComplexQueryOperator
import com.seer.trick.ComplexQueryType
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.DbType
import com.seer.trick.base.entity.*
import com.seer.trick.helper.BoolHelper
import com.seer.trick.helper.DateHelper
import com.seer.trick.helper.NumHelper
import org.apache.commons.lang3.StringUtils
import java.math.BigDecimal
import java.math.RoundingMode
import java.util.*

/**
 * 根据 EntityMeta 矫正值的数据类型
 * 检查文本长度！
 * 检查数字长度
 */
object EntityValueCleaner {

  /**
   * 直接修改传入的对象。
   * 元数据中没有的字段，删除！！！
   * 会级联处理嵌套的业务对象。
   */
  fun cleanEntityValue(em: EntityMeta, ev: EntityValue) {
    // 不用 iter，js 的 Map 不支持
    val removingKeys = mutableSetOf<String>()
    for ((fn, fv) in ev.entries) {
      val fm = em.fields[fn]
      if (fm == null) {
        removingKeys += fn
      } else {
        ev[fn] = jsonToFieldValue(em, fm, fm.scale, fv)
      }
    }
    for (fn in removingKeys) ev.remove(fn)
  }

  fun cleanEntityValues(em: EntityMeta, evList: List<EntityValue>) {
    for (ev in evList) cleanEntityValue(em, ev)
  }

  fun cleanComplexQuery(em: EntityMeta, query: ComplexQuery) {
    // if (query.type == null) throw BzError("未指明查询类型")
    if (query.type == ComplexQueryType.General) {
      if (StringUtils.isBlank(query.field1)) throw BzError("errComplexQueryMissingField1")
      if (query.operator == null) throw BzError("errComplexQueryMissingOp")
      // if (!query.operator!!.noValue && query.value == null) throw BzError("ComplexQueryValueNull") TODO 需要兼容 eq("container", null)
      // if (query.operator!!.multiple) {
      //   val value = query.value
      //   if (value is List<*>? && value.isNullOrEmpty()) throw BzError("errComplexQueryInMultiple")
      // }
      val fm = EntityHelper.mustGetFm(em, query.field1!!)

      checkFieldType(fm, query)
      query.value = jsonToFieldValue(
        em,
        fm,
        if (query.operator!!.multiple) FieldScale.List else FieldScale.Single,
        query.value,
      )
    } else if (query.type == ComplexQueryType.Compound) {
      val items = query.items
      if (!items.isNullOrEmpty()) {
        for (item in items) cleanComplexQuery(em, item)
      }
    }
  }

  /**
   * 暂不支持组件、图片、对象的查询。
   * 暂不支持嵌套查询。
   * MySQL 暂不支持多值。
   *
   *
   * Eq、Ne、In、Null、NotNull 支持范围内所有的类型。
   *
   * Gt、Gte、Lt、Lte、Between 仅支持日期时间、数值、字符串。
   *
   * Contain、ContainIgnoreCase、Start、End 支持字符串、引用
   * CurrentUser、CurrentUsername 支持字符串、引用
   *
   * ThisDay、ThisWeek 仅支持日期时间
   *
   * SQL 类数据库的 Empty、NotEmpty 仅支持字符串、引用。
   * MongoDB 的 Empty、NotEmpty 支持范围内所有类型。
   *
   *
   * 比较枚举、连续的 int 时 switch-case 比 if-else 快一些，因此当判断条件较多是这里用 when
   * https://www.yuewatch.com/archives/1694522922831
   */
  private fun checkFieldType(fm: FieldMeta, query: ComplexQuery) {
    val sqlDialect = BaseCenter.baseConfig.db.type // 数据库类型
    val fmType = fm.type // 字段类型
    val fmTypeName = fmType.name // 字段类型名称
    val fmName = fm.name // 字段名称
    val fmLabel = fm.label // 字段显示名
    val operator = query.operator?.name // 操作符

    // 暂不支持组件、图片、对象的查询。
    if ((fmType == FieldType.Component || fmType == FieldType.Image || fmType == FieldType.Object) &&
      !(query.operator == ComplexQueryOperator.Null || query.operator == ComplexQueryOperator.NotNull)
    ) {
      throw BzError("errComplexQueryNotSupportField", fmTypeName, fmName, fmLabel, operator)
    }

    // SQL 类数据库暂不支持多值。
    if (sqlDialect != DbType.MongoDB && fm.scale == FieldScale.List) {
      throw BzError("errMySqlQueryListNotSupport", fmName, fmLabel)
    }

    when (query.operator) {
      // Gt、Gte、Lt、Lte、Between 仅支持日期时间、数值、字符串。
      ComplexQueryOperator.Gt, ComplexQueryOperator.Gte,
      ComplexQueryOperator.Lt, ComplexQueryOperator.Lte,
      ComplexQueryOperator.Between,
      -> {
        when (fmType) {
          FieldType.String, FieldType.Reference, FieldType.Int, FieldType.Long, FieldType.Float,
          FieldType.Date, FieldType.DateTime, FieldType.Time,
          -> {
          }

          else -> {
            throw BzError("errComplexQueryNotSupportField", fmTypeName, fmName, fmLabel, operator)
          }
        }
      }

      // Contain、ContainIgnoreCase、Start、End 支持字符串、引用
      ComplexQueryOperator.Contain, ComplexQueryOperator.ContainIgnoreCase,
      ComplexQueryOperator.Start, ComplexQueryOperator.End,
      -> {
        if (fmType != FieldType.String && fmType != FieldType.Reference) {
          throw BzError("errComplexQueryNotSupportField", fmTypeName, fmName, fmLabel, operator)
        }
      }

      // CurrentUser、CurrentUsername 支持字符串、引用
      ComplexQueryOperator.CurrentUser, ComplexQueryOperator.CurrentUsername -> {
        if (fmType != FieldType.String && fmType != FieldType.Reference) {
          throw BzError("errComplexQueryNotSupportField", fmTypeName, fmName, fmLabel, operator)
        }
      }

      // ThisDay、ThisWeek 仅支持日期时间
      ComplexQueryOperator.ThisDay, ComplexQueryOperator.ThisWeek -> {
        if (fmType != FieldType.Date && fmType != FieldType.DateTime) {
          throw BzError("errComplexQueryNotSupportField", fmTypeName, fmName, fmLabel, operator)
        }
      }

      // SQL 类数据库的 Empty、NotEmpty 仅支持字符串、引用。
      // MongoDB 的 Empty、NotEmpty 支持范围内所有类型。
      ComplexQueryOperator.Empty, ComplexQueryOperator.NotEmpty -> {
        if (sqlDialect != DbType.MongoDB) {
          if (fmType != FieldType.String && fmType != FieldType.Reference) {
            throw BzError("errComplexQueryNotSupportField", fmTypeName, fmName, fmLabel, operator)
          }
        }
      }

      // 无限制
      else -> {}
    }
  }

  private fun jsonToFieldValue(em: EntityMeta, fm: FieldMeta, scale: FieldScale, jsonFv: Any?): Any? {
    if (jsonFv == null) return null
    return if (fm.type == FieldType.String) {
      mapFieldValue(scale, jsonFv) { item ->
        if (item == null) return@mapFieldValue null
        var str = if (item is Date) {
          DateHelper.formatDate(item, "yyyy-MM-dd HH:mm:ss")
        } else {
          item.toString()
        }
        if (fm.length > 0 && str.length > fm.length) {
          if (fm.truncate) {
            str = str.substring(0, fm.length)
          } else {
            throw BzError("errFieldTextTooLong", em.label, fm.label, str.length, fm.length)
          }
        }
        str
      }
    } else if (fm.type == FieldType.Boolean) {
      mapFieldValue(scale, jsonFv) { v -> if (v != null) BoolHelper.anyToBool(v) else null } // 保持 null
    } else if (fm.type == FieldType.Int) {
      mapFieldValue(scale, jsonFv) { v -> NumHelper.anyToInt(v, true) } // 保持 null
    } else if (fm.type == FieldType.Long) {
      mapFieldValue(scale, jsonFv) { v -> NumHelper.anyToLong(v, true) } // 保持 null
    } else if (fm.type == FieldType.Float) {
      mapFieldValue(scale, jsonFv) { v -> checkDecimal(fm, NumHelper.anyToDouble(v, true)) } // 保持 null
    } else if (fm.type == FieldType.Date || fm.type == FieldType.Time || fm.type == FieldType.DateTime) {
      mapFieldValue(scale, jsonFv) { input -> DateHelper.anyToDate(input) }
    } else if (fm.type == FieldType.File || fm.type == FieldType.Image) {
      mapFieldValue(scale, jsonFv) { item ->
        if (item is Map<*, *>) {
          @Suppress("UNCHECKED_CAST")
          item as MutableMap<String, Any?>
        } else {
          null
        }
      }
    } else if (fm.type == FieldType.Component) {
      val refEntityDef = BaseCenter.mustGetRefEntityMeta(fm)
      mapFieldValue(scale, jsonFv) { item ->
        if (item is Map<*, *>) {
          @Suppress("UNCHECKED_CAST")
          cleanEntityValue(refEntityDef, item as MutableMap<String, Any?>)
          item
        } else {
          null
        }
      }
    } else if (fm.type == FieldType.Reference) {
      jsonFv
    } else {
      null
    }
  }

  private fun mapFieldValue(scale: FieldScale, fv: Any?, transform: (item: Any?) -> Any?): Any? {
    if (fv == null) return null
    return if (scale == FieldScale.List) {
      if (fv is List<*>) {
        fv.map { transform(it) }
      } else {
        null
      }
    } else {
      transform(fv)
    }
  }

  // 检查浮点数是否超过 Decimal 的长度限制。
  // MongoDB 也按此限制检查。
  // 对于 Decimal(16, 3)，整数部分不能超过 13 位，整数和小数部分加起来不能超过 16 位
  // 1234567890123456 不允许，16 位整数不行
  // 1234567890123 允许，最多 13 位整数
  // 1234567890123.123 允许，13+3=16
  // 1.1234 允许，变成 1.123
  // 1234567890123.1234 不允许，整体超过 16 位了
  private fun checkDecimal(fm: FieldMeta, fv: Double?): Double? {
    if (fv == null) return null
    // 规定的数字位数
    var numWidth = fm.numWidth
    if (numWidth <= 0) numWidth = FieldMeta.DEFAULT_DECIMAL_M

    // 规定的小数位数
    var numScale = fm.numScale
    if (numScale < 0) numScale = FieldMeta.DEFAULT_DECIMAL_D

    // 注意必须用 valueOf，不能用 BigDecimal 构造器。否则像 0.45 这种不能被精确二进制表示的，会导致 scale 变的很大
    // 测试负数 OK
    val b = BigDecimal.valueOf(fv).setScale(numScale, RoundingMode.HALF_UP)

    val precision = b.precision() // 实际数字位数
    val scale = b.scale() // 实际小数位数
    if (precision > numWidth) throw BzError("errTooLongNum", fm.name, fv, numWidth, numWidth - numScale)

    // 如果实际没有小数，整数部分也不能超过 width - scale
    if (scale == 0 && precision > (numWidth - numScale)) {
      throw BzError("errTooLongNum", fm.name, fv, numWidth, numWidth - numScale)
    }

    return fv
  }
}