package com.seer.trick.base.entity.executor.sql

import com.seer.trick.Cq
import com.seer.trick.base.db.DbManager
import com.seer.trick.base.entity.*
import com.seer.trick.base.entity.executor.EntityCreateWorkContext
import com.seer.trick.base.entity.executor.EntityRemoveWorkContext
import com.seer.trick.base.entity.executor.EntityUpdateWorkContext

object EntityUpdateExecutorSql {

  fun execute(ctx: EntityUpdateWorkContext, em: EntityMeta, targetIds: List<String>, update: EntityValue) {
    for (fd in em.fields.values) {
      if (!update.containsKey(fd.name)) continue
      if (fd.type == FieldType.File || fd.type == FieldType.Image) {
        processFileOrImageField(ctx, fd, update)
      } else if (fd.type == FieldType.Component) {
        ctx.replacingFields.add(fd)
      } else if (fd.type == FieldType.Reference) {
        processRefEntityField(ctx, fd, update)
      } else {
        processOtherField(ctx, fd, update)
      }
    }

    // 1. update main table
    if (ctx.mainTableUpdate.isNotEmpty()) {
      DbManager.getSqlConnection().use { sc ->
        SqlExecutor.update(sc, em.name, Cq.include("id", targetIds), ctx.mainTableUpdate)
      }
    }
    if (ctx.replacingFields.isNotEmpty()) {
      // 2. delete related table rows
      EntityRemoveExecutorSql.execute(
        EntityRemoveWorkContext(),
        em,
        targetIds,
        ctx.replacingFields,
      )

      // 3. insert related table rows
      val entityValues = targetIds.map { targetId ->
        val ev: EntityValue = mutableMapOf()
        ev.putAll(update)
        ev["id"] = targetId
        ev
      }
      EntityCreateExecutorSql.execute(EntityCreateWorkContext(), em, entityValues, true)
    }
  }

  @Suppress("UNCHECKED_CAST")
  private fun processFileOrImageField(ctx: EntityUpdateWorkContext, fd: FieldMeta, update: EntityValue) {
    if (fd.scale == FieldScale.Single) {
      val fv = update[fd.name] as EntityValue?
      if (fv == null) {
        if (update.containsKey(fd.name)) {
          ctx.mainTableUpdate[fd.name] = null
          ctx.mainTableUpdate[fd.buildFileNameColumnName()] = null
          ctx.mainTableUpdate[fd.buildFileSizeColumnName()] = null
          ctx.mainTableUpdate[fd.buildFileMd5ColumnName()] = null
        }
        return
      }
      ctx.mainTableUpdate[fd.name] = fv[FieldMeta.FIELD_FILE_PATH]
      ctx.mainTableUpdate[fd.buildFileNameColumnName()] = fv[FieldMeta.FIELD_FILE_NAME]
      ctx.mainTableUpdate[fd.buildFileSizeColumnName()] = fv[FieldMeta.FIELD_FILE_SIZE]
      ctx.mainTableUpdate[fd.buildFileMd5ColumnName()] = fv[FieldMeta.FIELD_FILE_MD5]
    } else {
      ctx.replacingFields.add(fd)
    }
  }

  private fun processRefEntityField(ctx: EntityUpdateWorkContext, fd: FieldMeta, update: EntityValue) {
    if (fd.scale == FieldScale.Single) {
      ctx.mainTableUpdate[fd.name] = update[fd.name]
    } else {
      ctx.replacingFields.add(fd)
    }
  }

  private fun processOtherField(ctx: EntityUpdateWorkContext, fd: FieldMeta, update: EntityValue) {
    if (fd.scale == FieldScale.Single) {
      // 简单字段
      ctx.mainTableUpdate[fd.name] = SqlValueConverter.toSqlValue(update[fd.name], fd.type)
    } else {
      ctx.replacingFields.add(fd)
    }
  }
}