package com.seer.trick.base.http

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper

/**
 * Ws 双向通讯都用这个格式。
 * 这个类不好做泛型，将 content 声明为泛型，因为从文本解析成对象时，得根据 action 不同解析不同类型的 content
 */
data class WsMsg(
  val action: String,
  val content: String? = null,
  val id: String = IdHelper.oidStr(),
  val replyToId: String = "",
  val userIds: List<String>? = null
) {
  
  inline fun <reified T> contentAsType(): T? {
    if (content.isNullOrEmpty()) return null
    return JsonHelper.mapper.readValue(content, jacksonTypeRef())
  }
  
  companion object {
    
    fun json(
      action: String,
      content: Any? = null,
      id: String = "",
      replyToId: String = "",
      userIds: List<String>? = null
    ): WsMsg {
      val contentStr = if (content != null) JsonHelper.writeValueAsString(content) else null
      return WsMsg(action, contentStr, id, replyToId, userIds)
    }
    
  }
  
}
