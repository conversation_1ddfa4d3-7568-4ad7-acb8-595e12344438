package com.seer.trick.base.failure

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.helper.DateHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.NumHelper
import org.apache.commons.lang3.time.DateUtils
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.Executors

object FailureRecorder {
  
  private val logger = LoggerFactory.getLogger(javaClass)
  
  private val executor = Executors.newSingleThreadExecutor()
  
  fun addAsync(req: FailureRecordReq) {
    executor.submit {
      add(req)
    }
  }
  
  /**
   * kind+subKind+source+desc 拼成最终的 desc。同一天同一个 desc 累加计数。
   */
  @Synchronized
  private fun add(req: FailureRecordReq) {
    val desc = "[${req.kind}][${req.subKind}][${req.source}][${req.part}] ${req.desc}"
    try {
      val old = EntityRwService.findOne(
        
        "FailureRecord",
        Cq.eq("desc", desc),
        FindOptions(sort = listOf("-firstOn")),
      )
      // 同一天的相同记录，增加错误次数；不同天的相同错误分开记录
      if (old != null && isSameDay(old)) {
        val num = 1 + (NumHelper.anyToInt(old["num"]) ?: 1)
        EntityRwService.updateOne(
          
          "FailureRecord",
          Cq.idEq(EntityHelper.mustGetId(old)),
          mutableMapOf("num" to num, "lastOn" to Date()),
        )
      } else {
        val ev: EntityValue = JsonHelper.mapper.convertValue(req, jacksonTypeRef())
        ev["desc"] = desc
        ev["level"] = req.level.name
        ev["num"] = 1
        ev["firstOn"] = Date()
        ev["lastOn"] = Date()
        
        EntityRwService.createOne("FailureRecord", ev)
      }
    } catch (e: Exception) {
      logger.error("记录故障出错", e)
    }
  }
  
  private fun isSameDay(ev: EntityValue): Boolean {
    val firstOn = DateHelper.anyToDate(ev["firstOn"]) ?: return false
    return DateUtils.isSameDay(firstOn, Date())
  }
}

data class FailureRecordReq(
  val kind: String, // 类别
  val subKind: String = "", // 子类别
  val level: FailureLevel = FailureLevel.Error, // 级别
  val source: String = "", // 来源
  val part: String = "", // 对象
  val desc: String, // 描述
)

enum class FailureLevel {
  Warning,
  Error,
  Fatal,
}