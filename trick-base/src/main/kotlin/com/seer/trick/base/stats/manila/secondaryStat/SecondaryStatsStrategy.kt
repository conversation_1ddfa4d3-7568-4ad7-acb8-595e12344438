package com.seer.trick.base.stats.manila.secondaryStat

import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.StatisticDateType
import com.seer.trick.base.stats.manila.ManilaReportService.period2FinishedOn
import com.seer.trick.base.stats.manila.ManilaReportService.period2StartedOn
import com.seer.trick.base.stats.manila.domain.ManilaReportCfg
import com.seer.trick.base.stats.manila.domain.StatType
import com.seer.trick.helper.NumHelper.roundToDecimal
import java.text.SimpleDateFormat

abstract class SecondaryStatsStrategy {
  val dateFormat = SimpleDateFormat("yyyy-MM-dd")
  abstract fun execute(cfg: ManilaReportCfg, baseReports: List<EntityValue>)

  // 保存统计结果
  fun saveStatResult(
    
    cfg: ManilaReportCfg,
    periodType: StatisticDateType,
    targetAndPeriod: String,
    totalValue: Double,
    molecular: Double? = null,
    denominator: Double? = null,
  ) {
    val split = targetAndPeriod.split("#")
    val target = split[0]
    val period = split[1]

    // 删除旧记录
    EntityRwService.removeMany(
      
      "StatsTimelineValueReport",
      Cq.and(
        Cq.eq("subject", cfg.reportSubject),
        Cq.eq("target", target),
        Cq.eq("periodType", periodType),
        Cq.eq("period", period),
      ),
    )

    // 准备要插入的数据
    val data: EntityValue = mutableMapOf(
      "subject" to cfg.reportSubject,
      "target" to target,
      "periodType" to periodType,
      "period" to period,
      "value" to totalValue.roundToDecimal(5),
      "startedOn" to period2StartedOn(period, periodType),
      "finishedOn" to period2FinishedOn(period, periodType),
    )

    // 如果是比率类型，加入 molecular 和 denominator
    if (cfg.type == StatType.RATIO && molecular != null && denominator != null) {
      data["molecular"] = molecular
      data["denominator"] = denominator
    }

    // 插入新的聚合结果
    EntityRwService.createOne("StatsTimelineValueReport", data)
  }
}