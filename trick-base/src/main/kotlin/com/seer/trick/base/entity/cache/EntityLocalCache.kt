package com.seer.trick.base.entity.cache

import com.seer.trick.ComplexQuery
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.FindOptions
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import java.util.concurrent.Callable
import java.util.concurrent.Executors
import java.util.concurrent.Future

class EntityLocalCache : EntityCache {

  private val logger = LoggerFactory.getLogger(javaClass)

  private val byIdCache = EntityIdKeyCache()
  private val byQueryCache = EntityKeyCache<List<EntityValue>>()
  private val countCache = EntityKeyCache<Long>()

  private val cleanExecutor = Executors.newSingleThreadExecutor()

  @Volatile
  private var cleanFuture: Future<*>? = null

  override fun init() {
    cleanFuture?.cancel(true)
    cleanFuture = cleanExecutor.submit(this::cleanLoop)
  }

  override fun dispose() {
    cleanFuture?.cancel(true)
    cleanFuture = null
  }

  private fun cleanLoop() {
    while (true) {
      try {
        clean()
      } catch (e: Exception) {
        logger.error("清理缓存报错", e)
      }
      Thread.sleep(1000 * 60 * 5) // 5 min
    }
  }

  private fun clean() {
    logger.info("Clean entity local cache")
    val before = System.currentTimeMillis() - 1000 * 60 * 5
    byIdCache.cleanOld(before)
    byQueryCache.cleanOld(before)
    countCache.cleanOld(before)
  }

  override fun getById(
    
    entityName: String,
    id: String,
    projection: List<String>?,
    loader: Callable<EntityValue>,
  ): EntityValue? {
    val key = if (projection != null) StringUtils.join(projection, ",") else ""
    val cv = byIdCache.get(entityName, id, key)
    val value = if (cv != null) {
      cv.value
    } else {
      val v = loader.call()
      byIdCache[entityName, id, key] = v
      v
    }
    return EntityHelper.cloneWithNull(value)
  }

  override fun getByQuery(
    
    entityName: String,
    query: ComplexQuery,
    o: FindOptions?,
    loader: Callable<List<EntityValue>>,
  ): List<EntityValue> {
    val q = ComplexQueryToKey.queryToKey(query)
    val p = o?.projection?.joinToString(",") ?: ""
    val sort = o?.sort?.joinToString(",") ?: ""
    val key = "$q|$p|$sort|${o?.skip ?: ""}|${o?.limit ?: ""}"
    val cv = byQueryCache.get(entityName, key)
    val ll = if (cv != null) {
      cv.value as List<*>
    } else {
      val v = loader.call()
      byQueryCache[entityName, key] = v
      v
    }

    return ll.map {
      @Suppress("UNCHECKED_CAST")
      EntityHelper.clone(it as EntityValue)
    }
  }

  override fun getCount(entityName: String, query: ComplexQuery, loader: Callable<Long>): Long {
    val key = ComplexQueryToKey.queryToKey(query)
    val cv = countCache.get(entityName, key)
    if (cv != null) return cv.value!!
    val v = loader.call()
    countCache[entityName, key] = v
    return v
  }

  override fun clearAll() {
    byIdCache.clear()
    byQueryCache.clear()
    countCache.clear()
  }

  override fun clearAll(entityName: String) {
    byIdCache.remove(entityName)
    byQueryCache.remove(entityName)
    countCache.remove(entityName)
  }

  override fun clearByIds(entityName: String, ids: List<String>?) {
    byIdCache.clearByIds(entityName, ids)
  }

  override fun clearByQuery(entityName: String) {
    byQueryCache.remove(entityName)
  }

  override fun clearCount(entityName: String) {
    countCache.remove(entityName)
  }

  override fun inspect(): Any = mapOf(
    "ByIdCache" to byIdCache,
    "ByQueryCache" to byQueryCache,
    "CountCache" to countCache,
  )
}