package com.seer.trick.base.http.handler

import com.seer.trick.base.bgt.BgTaskService
import com.seer.trick.base.http.*
import com.seer.trick.base.http.HttpServerManager.auth
import io.javalin.http.Context

object BgTaskHandler {

  fun registerHandlers() {
    val c = Handlers("api/bg-task")
    // 重试失败的任务
    c.post("retry", ::retryTasks, auth())
    // 终止任务
    c.post("abort", ::abortTasks, auth())
    // 暂停，继续任务
    c.post("pause-resume", ::pauseResume, auth())
  }

  fun retryTasks(ctx: Context) {
    
    val req: TaskIdsReq = ctx.getReqBody()

    BgTaskService.recoverTasks(req.ids)

    ctx.status(200)
  }

  fun abortTasks(ctx: Context) {
    
    val req: TaskIdsReq = ctx.getReqBody()
    BgTaskService.abortTasks(req.ids)

    ctx.status(200)
  }

  fun pauseResume(ctx: Context) {
    
    val req: PauseResumeReq = ctx.getReqBody()
    BgTaskService.pauseOrResumeTasks(req.ids, req.paused)

    ctx.status(200)
  }

  data class TaskIdsReq(val ids: List<String> = emptyList())

  data class PauseResumeReq(val ids: List<String> = emptyList(), val paused: Boolean)
}