package com.seer.trick.base.file

import com.seer.trick.base.BaseCenter
import com.seer.trick.helper.FileHelper
import com.seer.trick.helper.IdHelper
import org.apache.commons.io.IOUtils
import org.apache.commons.lang3.time.DateFormatUtils
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.nio.file.Paths
import java.util.*

object FileManager {

  private const val DEFAULT_UPLOAD_DIRNAME = "upload"
  private const val SECURED_UPLOAD_DIRNAME = "secured-upload"

  fun getFilesDir(): File = BaseCenter.baseConfig.filesDir

  fun isFileInFilesDir(file: File): Boolean = FileHelper.isFileInDir(file, getFilesDir())

  fun ensureFilesDir(): File {
    val filesDir = getFilesDir()
    if (!filesDir.exists()) filesDir.mkdirs()
    return filesDir
  }

  fun upload(inputStream: InputStream, extName: String): String {
    val newFilename = IdHelper.oidStr() + "." + extName
    val relativePath = Paths.get(
      DEFAULT_UPLOAD_DIRNAME,
      DateFormatUtils.format(Date(), "yyyyMMdd"),
      newFilename,
    ).toString()
    val toFile = File(getFilesDir(), relativePath)
    toFile.parentFile.mkdirs()
//    FileOutputStream(toFile).use { os -> IOUtils.copy(inputStream, os) }
    inputStream.use { input ->
      FileOutputStream(toFile).use { os ->
        IOUtils.copy(input, os)
      }
    }
    return relativePath
  }

  fun pathToFile(path: String): File {
    val file = File(getFilesDir(), path)

    // TODO 因为兼容性问题暂不检查
    // val roDir = File(getFilesDir(), roId)
    // if (!roDir.exists()) throw BzError("ReadFileNoRoDir", roId, path)
    // if (!FileUtils.directoryContains(roDir, file)) throw BzError("ReadFileOtherRo")
    return file
  }

  fun fileToPath(file: File): String {
    val dir = getFilesDir()
    return dir.toURI().relativize(file.toURI()).path
  }

  fun ensureTmpDir(): File {
    val tmpDir = File(getFilesDir(), "tmp")
    if (!tmpDir.exists()) tmpDir.mkdirs()
    return tmpDir
  }

  fun nextTmpFile(ext: String = "", prefix: String = "", suffix: String = ""): File {
    val tmpDir = ensureTmpDir()
    var filename = prefix + IdHelper.oidStr() + suffix
    if (ext.isNotBlank()) {
      filename += if (ext.startsWith(".")) ext else ".$ext"
    }
    return File(tmpDir, filename)
  }

  fun nextTmpDir(prefix: String = "", suffix: String = ""): File {
    val tmpDir = ensureTmpDir()
    val dir = File(tmpDir, prefix + IdHelper.oidStr() + suffix)
    if (!dir.exists()) dir.mkdirs()
    return dir
  }
}