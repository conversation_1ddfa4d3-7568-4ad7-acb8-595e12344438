package com.seer.trick.base.db.mongo

import com.mongodb.MongoCommandException
import com.mongodb.client.MongoCollection
import com.mongodb.client.model.IndexOptions
import com.seer.trick.base.db.DbManager
import com.seer.trick.base.db.IndexHelper
import com.seer.trick.base.entity.*
import org.bson.Document
import org.slf4j.Logger
import org.slf4j.LoggerFactory

object MongoIndexManager {

  private val logger: Logger = LoggerFactory.getLogger(this::class.java)
  private const val INDEX_PREFIX = "m4_"

  fun sync(entityMetaMap: MutableMap<String, EntityMeta>) {
    val mongoDb = DbManager.mustGetMongoDb()
    for (em in entityMetaMap.values) {
      if (em.disabled) continue
      if (em.type != EntityType.Entity) continue

      val collection = mongoDb.getCollection(em.name)
      val oldIndexMap: MutableMap<String, EntityIndexDef?> = HashMap()
      val indexes = em.indexes.toMutableList()
      val oldIndexes: List<EntityIndexDef> = try {
        queryIndexList(em.name, collection)
      } catch (e: Throwable) {
        logger.warn("查询 ${em.name} 的索引失败", e)
        emptyList()
      }

      processIndexBefore(collection, oldIndexMap, indexes, oldIndexes, em)

      for (indexDef in indexes) {
        try {
          processIndex(collection, indexDef, em.name, oldIndexMap)
        } catch (e: Throwable) {
          logger.warn("创建索引 ${em.name}:${indexDef.name} 失败", e)
        }
      }
    }
    logger.info("MongoDB 数据库索引已更新")
  }

  private fun processIndexBefore(
    
    collection: MongoCollection<Document>,
    oldIndexMap: MutableMap<String, EntityIndexDef?>,
    indexes: MutableList<EntityIndexDef>,
    oldIndexes: List<EntityIndexDef>,
    em: EntityMeta,
  ) {
    val fieldNames = em.fields.map { it.value.name }
    if (fieldNames.contains(FieldMeta.FIELD_CREATED_ON)) {
      val createdOnIndex =
        EntityIndexDef("CreatedOn", false, listOf(EntityIndexField(FieldMeta.FIELD_CREATED_ON, false)))
      IndexHelper.addIndexIfAbsent(indexes, createdOnIndex)
    }

    for (old in oldIndexes) {
      val index = indexes.find { it.name == old.name }
      if (index != null) {
        oldIndexMap[old.name] = old
      } else {
        try {
          // 删除已经不用的索引
          dropIndex(collection, em.name, old.name)
        } catch (e: MongoCommandException) {
          logger.warn("删除索引 ${em.name}:${old.name} 失败", e)
        }
      }
    }
  }

  private fun dropIndex(collection: MongoCollection<Document>, tableName: String, name: String) {
    logger.info("删除索引 $tableName:$name")
    val indexName = INDEX_PREFIX + tableName + name
    collection.dropIndex(indexName)
  }

  private fun processIndex(
    
    collection: MongoCollection<Document>,
    indexDef: EntityIndexDef,
    tableName: String,
    oldIndexMap: MutableMap<String, EntityIndexDef?>,
  ) {
    oldIndexMap[indexDef.name]?.let {
      // 索引已存在且结构严格相同则不处理
      if (it == indexDef) return
      dropIndex(collection, tableName, indexDef.name)
    }
    createIndex(collection, tableName, indexDef)
  }

  private fun queryIndexList(tableName: String, collection: MongoCollection<Document>): List<EntityIndexDef> {
    val indexDefList = mutableListOf<EntityIndexDef>()
    for (index in collection.listIndexes()) {
      val indexName = index.getString("name") ?: continue
      if (!indexName.startsWith(INDEX_PREFIX)) continue

      val unique = index["unique"] as Boolean? ?: false
      val key = index["key"] as Document
      val indexFieldList = key.keys.map {
        val desc = if (key[it] is Int) {
          (key[it] as Int) == -1
        } else {
          false
        }
        EntityIndexField(it, desc)
      }
      val entityIndexDef = EntityIndexDef(indexName.replace(INDEX_PREFIX + tableName, ""), unique, indexFieldList)
      indexDefList.add(entityIndexDef)
    }

    return indexDefList
  }

  private fun createIndex(
    
    collection: MongoCollection<Document>,
    tableName: String,
    indexDef: EntityIndexDef,
  ) {
    logger.info("创建索引 $tableName: $indexDef")
    // 当前只使用到升序、降序索引, 1 表示升序，-1 表示降序
    val field = Document()
    indexDef.fields.map {
      field[it.name] = if (it.desc) -1 else 1
    }

    val indexOptions = IndexOptions()
    val indexName = INDEX_PREFIX + tableName + indexDef.name
    indexOptions.name(indexName)
    indexOptions.unique(indexDef.unique)

    collection.createIndex(field, indexOptions)
  }
}