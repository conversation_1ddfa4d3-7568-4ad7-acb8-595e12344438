package com.seer.trick.base.http.handler

import com.seer.trick.BzError
import com.seer.trick.ComplexQuery
import com.seer.trick.Cq
import com.seer.trick.I18N
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.ProhibitionType
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.script.ScriptCenter
import com.seer.trick.base.script.ScriptExeRequest
import com.seer.trick.base.script.ScriptTraceContext
import com.seer.trick.base.user.Operator
import org.apache.commons.lang3.StringUtils

object EntityHandlerExt {
  
  @Suppress("UNUSED_PARAMETER")
  fun extBeforeCreate(em: EntityMeta, evList: List<EntityValue>) {
    //
  }
  
  @Suppress("UNUSED_PARAMETER")
  fun extAfterCreate(em: EntityMeta, ids: List<String>) {
    //
  }
  
  @Suppress("UNUSED_PARAMETER")
  fun extBeforeUpdate(em: EntityMeta, query: ComplexQuery, update: EntityValue) {
    // 校验是否允许修改
    checkModifyProhibition(em, query)
  }
  
  @Suppress("UNUSED_PARAMETER")
  fun extAfterUpdate(em: EntityMeta) {
    //
  }
  
  @Suppress("UNUSED_PARAMETER")
  fun extBeforeDelete(em: EntityMeta, query: ComplexQuery) {
    // 校验是否允许删除
    checkRemoveProhibition(em, query)
  }
  
  @Suppress("UNUSED_PARAMETER")
  fun extAfterDelete(em: EntityMeta) {
    //
  }
  
  fun extRead(em: EntityMeta, evList: List<EntityValue>, op: Operator) {
    if (em.name == "HumanUser") extHumanUserRead(em, evList, op)
  }
  
  @Suppress("UNUSED_PARAMETER")
  private fun extHumanUserRead(em: EntityMeta, evList: List<EntityValue>, op: Operator) {
    for (ev in evList) {
      ev.remove("password")
      
      val phone = ev["phone"] as String?
      if (!phone.isNullOrBlank() && !op.admin) {
        ev["phone"] = if (phone.length <= 7) {
          phone.replaceRange(0, phone.length / 2, "*".repeat(phone.length / 2))
        } else {
          phone.replaceRange(3, 7, "****")
        }
      }
    }
  }
  
  private fun checkModifyProhibition(em: EntityMeta, query: ComplexQuery) {
    if (em.modifyProhibition != null && em.modifyProhibition.enabled) {
      val errMsg =
        StringUtils.defaultIfBlank(em.modifyProhibition.alertMsg, I18N.lo("errModifyProhibitionDefaultMsg")) ?: ""
      
      if (em.modifyProhibition.type == ProhibitionType.Query && em.modifyProhibition.filter != null) {
        val r = EntityRwService.findMany(em.name, Cq.and(query, em.modifyProhibition.filter))
        if (r.isNotEmpty()) throw BzError("errModifyProhibition", errMsg)
      } else {
        val source = em.modifyProhibition.source
        val sourceLang = em.modifyProhibition.sourceLang ?: "python"
        val funcName = em.modifyProhibition.extFunction ?: ""
        checkProhibition(em, query, source, sourceLang, funcName, errMsg)
      }
    }
  }
  
  private fun checkRemoveProhibition(em: EntityMeta, query: ComplexQuery) {
    if (em.removeProhibition != null && em.removeProhibition.enabled) {
      val errMsg =
        StringUtils.defaultIfBlank(em.removeProhibition.alertMsg, I18N.lo("errRemoveProhibitionDefaultMsg")) ?: ""
      
      if (em.removeProhibition.type == ProhibitionType.Query && em.removeProhibition.filter != null) {
        val r = EntityRwService.findMany(em.name, Cq.and(query, em.removeProhibition.filter))
        if (r.isNotEmpty()) throw BzError("errRemoveProhibition", errMsg)
      } else {
        val source = em.removeProhibition.source
        val sourceLang = em.removeProhibition.sourceLang ?: "python"
        val funcName = em.removeProhibition.extFunction ?: ""
        checkProhibition(em, query, source, sourceLang, funcName, errMsg)
      }
    }
  }
  
  private fun checkProhibition(
    em: EntityMeta,
    query: ComplexQuery,
    source: String?,
    sourceLang: String,
    funcName: String,
    errMsg: String,
  ) {
    if (!source.isNullOrBlank()) {
      val ctx = ScriptCenter.createContext(sourceLang, source)
      ScriptCenter.execute(
        ctx,
        ScriptExeRequest(funcName, arrayOf(ScriptTraceContext(), em.name, query, errMsg), sourceLang),
      )
    } else {
      ScriptCenter.execute(
        ScriptExeRequest(funcName, arrayOf(ScriptTraceContext(), em.name, query, errMsg), sourceLang),
      )
    }
  }
}