package com.seer.trick.base.script

import com.fasterxml.jackson.core.type.TypeReference
import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.bgt.BgTaskService
import com.seer.trick.helper.FileHelper
import com.seer.trick.helper.JsonHelper
import org.apache.commons.lang3.StringUtils
import org.graalvm.polyglot.*
import org.slf4j.LoggerFactory
import java.io.File
import java.time.ZoneId
import java.util.*
import java.util.concurrent.CancellationException
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

object ScriptCenter {

  private val logger = LoggerFactory.getLogger(javaClass)

  private val sources: MutableList<Source> = CopyOnWriteArrayList()

  private val contexts: MutableList<Context> = Collections.synchronizedList(ArrayList())

  // 函数名 -> 是否存在
  private val existsCache: MutableMap<String, Boolean> = ConcurrentHashMap()

  @Volatile
  var mainScriptType: String = "js"
    private set

  init {
    val props = System.getProperties()
    props.setProperty("polyglotimpl.DisableClassPathIsolation", "true")
  }

  @Synchronized
  fun init() {
    if (BaseCenter.baseConfig.noScript) return

    val scriptsDir = File(BaseCenter.baseConfig.projectDir, "scripts")
    val scriptsJsDir = File(BaseCenter.baseConfig.projectDir, "scripts-js")
    val scriptsPyDir = File(BaseCenter.baseConfig.projectDir, "scripts-py")

    val finalScriptsDir: File?
    if (scriptsJsDir.exists()) {
      finalScriptsDir = scriptsJsDir
      mainScriptType = "js"
    } else if (scriptsDir.exists()) {
      finalScriptsDir = scriptsDir
      mainScriptType = "js"
    } else if (scriptsPyDir.exists()) {
      finalScriptsDir = scriptsPyDir
      mainScriptType = "python"
    } else {
      logger.info("No script dir")
      return
    }

    logger.info("Script lang: $mainScriptType, dir: $finalScriptsDir")

    try {
      buildMainSources(finalScriptsDir)
    } catch (e: Exception) {
      logger.error("Failed to build scripts", e)
      return
    }

    ScriptHttpServer.handlers.clear()

    try {
      createMainContext().use { ctx ->
        val bindings = ctx.getBindings(mainScriptType)
        if (!bindings.hasMember("boot")) {
          logger.info("Script no boot function")
        } else {
          bindings.getMember("boot").execute()
        }
      }
      logger.info("Script booted")
    } catch (e: Exception) {
      logger.error("Script boot failed", e)
      return
    }

    ScriptRobustExecutor.recoverAll()

    BgTaskService.recoverAll()
  }

  @Synchronized
  fun dispose() {
    if (BaseCenter.baseConfig.noScript) return

    logger.info("Dispose scripts")

    ScriptEntityExt.clear()

    existsCache.clear()

    // 先中断脚本线程
    ScriptThread.dispose()

    synchronized(contexts) {
      for (ctx in contexts) {
        try {
          ctx.close(true)
        } catch (e: Exception) {
          logger.error("Error on closing script context", e)
        }
      }
    }

    try {
      createMainContext().use { ctx ->
        val bindings = ctx.getBindings(mainScriptType)
        if (!bindings.hasMember("dispose")) {
          logger.info("No script dispose function")
        } else {
          bindings.getMember("dispose").execute()
        }
      }
    } catch (e: Exception) {
      logger.error("Error on calling dispose of script", e)
    }
  }

  @Synchronized
  fun reload() {
    dispose()
    init()
  }

  fun <T> execute(req: ScriptExeRequest, typeRef: TypeReference<T>): T {
    val r = execute(req)
    return if (r.isNullOrBlank()) {
      throw BzError("errScriptBadReturnNull", req.funcName) // null 表示函数不存在
    } else {
      JsonHelper.mapper.readValue(r, typeRef)
    }
  }

  /**
   * 由于上下文关闭的问题，只能返回字符串作为结果
   * 返回 null 表示函数不存在
   */
  fun execute(req: ScriptExeRequest): String? {
    val ctx = createMainContext()
    return execute(ctx, req)
  }

  /**
   * 由于上下文关闭的问题，只能返回字符串作为结果
   * 返回 null 表示函数不存在
   * 会负责关闭传入的 ctx
   */
  fun execute(ctx: Context, req: ScriptExeRequest): String? {
    if (BaseCenter.baseConfig.noScript) throw IllegalStateException("ScriptNotEnabled")
    try {
      contexts.add(ctx)

      val v = executeToValue(ctx, req)
      return if (v != null) {
        if (v.isNull) {
          null
        } else if (v.isString) {
          v.asString()
        } else {
          throw BzError("errScriptReturnNotString", req.funcName, v)
        }
      } else {
        null
      }
    } catch (e: Throwable) {
      throw tryToConvertPolyglotException(e)
    } finally {
      try {
        ctx.close()
      } catch (_: Exception) {
        // ignore
      }
      contexts.remove(ctx)
    }
  }

  // 返回 null 表示函数不存在
  private fun executeToValue(context: Context, req: ScriptExeRequest): Value? {
    try {
      val bindings = context.getBindings(StringUtils.firstNonBlank(req.scriptType, mainScriptType))
      val func = bindings.getMember(req.funcName) ?: throw BzError("errNoSuchScriptFunction", req.funcName)
      return func.execute(*req.args)
    } catch (e: Throwable) {
      throw tryToConvertPolyglotException(e, context, mainScriptType)
    }
  }

  // 返回 null 表示函数不存在
  fun exists(funcName: String): Boolean {
    if (BaseCenter.baseConfig.noScript) return false

    return existsCache.getOrPut(funcName) {
      createMainContext().use { context ->
        val bindings = context.getBindings(mainScriptType)
        bindings.hasMember(funcName)
      }
    }
  }

  private fun buildMainSources(scriptsDir: File) {
    sources.clear()

    if (mainScriptType == "js") {
      val baseSrc = FileHelper.loadClasspathResourceAsString("/script/base.js")
      sources += Source.newBuilder("js", baseSrc, "base.js").build()
    }

    for (m in BaseCenter.modules) m.loadMoreScripts(sources)

    val ext = if (mainScriptType == "python") ".py" else ".js"
    var projectScriptFiles = scriptsDir.listFiles()?.filter { it.name.endsWith(ext) }
    if (projectScriptFiles.isNullOrEmpty()) {
      logger.info("No script in dir")
      return
    }

    projectScriptFiles = projectScriptFiles.sortedBy { it.name }
    logger.info("buildSources sorted projectScriptFiles: ${projectScriptFiles.map { it.name }}")
    for (file in projectScriptFiles) {
      sources += Source.newBuilder(mainScriptType, file).build()
    }
  }

  fun createMainContext(): Context = createContext(mainScriptType, sources)

  fun createContext(scriptType: String, sourceStr: String, name: String? = null): Context {
    val source = Source.newBuilder(scriptType, sourceStr, name).build()
    return createContext(scriptType, listOf(source))
  }

  fun createContext(scriptType: String, sources: List<Source>): Context {
    if (BaseCenter.baseConfig.noScript) throw IllegalStateException("ScriptNotEnabled")
    // val port = "4242"
    // val path = IdHelper.uuidStr()
    // val hostAddress = "127.0.0.1"
    // val debugUrl = String.format(
    //   "devtools://devtools/bundled/js_app.html?ws=%s:%s/%s",
    //   hostAddress, port, path
    // )

    val context = Context.newBuilder(scriptType)
      .allowAllAccess(true)
      .allowHostAccess(HostAccess.ALL)
      // .allowExperimentalOptions(true).option("js.nashorn-compat", "true")
      // .option("inspect", port)
      // .option("inspect.Secure", "false")
      // .option("inspect.Path", path)
      // .option("inspect.Suspend", "true")
      // .option("log.file", "script.log")
      .option("engine.WarnInterpreterOnly", "false")
      .apply {
        if (scriptType == "python") {
          this.option("python.EmulateJython", "true")
          // 调用 datetime.datetime.strptime() 函数时会触发设置时区，默认 "TZ" 是空串，空串就会取默认时区 GMT，所以需显示设置
          this.environment("TZ", ZoneId.systemDefault().id)
        }
      }
      .build()

    for (source in sources) context.eval(source)

    // bindings

    val bindings = context.getBindings(scriptType)
    bindings.putMember("base", ScriptBase)
    bindings.putMember("soc", ScriptSoc)
    bindings.putMember("httpServer", ScriptHttpServer)
    bindings.putMember("thread", ScriptThread)
    bindings.putMember("entity", ScriptEntity)
    bindings.putMember("entityExt", ScriptEntityExt)
    bindings.putMember("cq", Cq)
    bindings.putMember("httpClient", ScriptHttpClient)
    bindings.putMember("resLock", ScriptResLock)
    bindings.putMember("ui", ScriptUi)
    bindings.putMember("utils", ScriptUtils)
    bindings.putMember("fileUtils", ScriptFile)
    bindings.putMember("stats", ScriptStats)
    bindings.putMember("agg", Agg)
    bindings.putMember("statType", StatType)
    bindings.putMember("bgTask", ScriptBgTask)

    // bindings.putMember("core", ScriptCore)
    // bindings.putMember("db", ScriptDb)
    // bindings.putMember("SqlQuery", SqlQuery.Companion)
    // bindings.putMember("button", ScriptButtons)
    // bindings.putMember("idHelper", IdHelper)
    // bindings.putMember("externalTaskHelper", ScriptExternalTask)

    for (m in BaseCenter.modules) m.putMoreScriptBindings(bindings)

    return context
  }

  // companion object {
  // init {
  // System.getProperty("polyglot.log.file", "truffle.log")
  // }
  // }

  fun tryToConvertPolyglotException(e: Throwable, ctx: Context? = null, scriptType: String? = "js"): Throwable {
    return if (e !is PolyglotException) {
      e
    } else if (e.isHostException) {
      e.asHostException()
    } else if (e.isCancelled) {
      CancellationException()
    } else if (e.isInterrupted) {
      InterruptedException()
    } else {
      if (scriptType == "python") {
        
        val formatException = ctx?.eval(
          "python",
          """
            def _format_exception(e):
                import traceback
                return ''.join(traceback.format_exception(e))
            _format_exception
          """.trimIndent(),
        )
        logger.info("脚本异常: ${formatException?.execute(e.guestObject)?.asString()}")
      }
      e
    }
    // TODO 如果是语法错误，简化并范围位置
  }
}

class ScriptExeRequest(val funcName: String, val args: Array<Any?> = emptyArray(), val scriptType: String? = null)

data class UnitScriptExeResult(val ok: Boolean = false, val errorMsg: String? = null)