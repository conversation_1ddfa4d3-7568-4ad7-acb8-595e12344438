package com.seer.trick.base

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.I18N
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityMetaManager
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.log.SystemKeyEvent
import com.seer.trick.base.log.SystemKeyEventService
import com.seer.trick.helper.JsonFileHelper
import com.seer.trick.helper.JsonHelper
import org.apache.commons.io.FileUtils
import org.slf4j.LoggerFactory
import java.io.File
import java.nio.charset.StandardCharsets
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

object BaseCenter {

  private val logger = LoggerFactory.getLogger(javaClass)

  @Volatile
  var version: String? = "Dev"

  val modules: MutableList<AppModule> = CopyOnWriteArrayList()

  @Volatile
  var baseConfig: BaseConfig = BaseConfig()

  val entityMetaMap: MutableMap<String, EntityMeta> = ConcurrentHashMap()

  val componentEmSet: MutableSet<String> = Collections.synchronizedSet(HashSet())

  val queryIds = QueryIdRecord()

  @Volatile
  var bzConfig: EntityValue = HashMap()

  @Volatile
  var menu: String = ""

  val httpApiCallTraceConfigMap: MutableMap<String, HttpApiCallTraceConfigItem> = ConcurrentHashMap()

  fun initConfig() {
    val menuFile = getMenuFile()
    menu = if (menuFile.exists()) {
      FileUtils.readFileToString(menuFile, StandardCharsets.UTF_8)
    } else {
      ""
    }

    bzConfig = JsonFileHelper.readJsonFromFile(getBzFile()) ?: HashMap()
    updateSingleFromBaseToBzOnStartIfNecessary()
    parseHttpApiCallTraceConfigs()
  }

  fun updateMenu(menu: String) {
    this.menu = menu

    // 格式化下
    val menuJson = JsonHelper.mapper.readTree(menu)
    JsonFileHelper.writeJsonToFile(getMenuFile(), menuJson, true)
  }

  fun updateBzConfig(field: String, fieldConfig: EntityValue) {
    bzConfig[field] = fieldConfig
    updateBzConfig(bzConfig)

    SystemKeyEventService.record(SystemKeyEvent(group = "Base", title = "修改系统配置，组=$field"))
  }

  private fun updateBzConfig(bzConfig: EntityValue) {
    this.bzConfig = bzConfig
    JsonFileHelper.writeJsonToFile(getBzFile(), bzConfig, true)

    // 重置国际化
    I18N.load()

    EntityMetaManager.loadMeta()

    parseHttpApiCallTraceConfigs()

    BzConfigManager.eventBus.fire("")
  }

  @Suppress("UNCHECKED_CAST")
  private fun parseHttpApiCallTraceConfigs() {
    try {
      this.httpApiCallTraceConfigMap.clear()

      val apiConfig = this.bzConfig["api"] as EntityValue?
      val itemsStr = apiConfig?.get("apiCallTrace") as String?

      if (!itemsStr.isNullOrBlank()) {
        val items: List<HttpApiCallTraceConfigItem> = JsonHelper.mapper.readValue(itemsStr, jacksonTypeRef())
        val map: MutableMap<String, HttpApiCallTraceConfigItem> = HashMap()
        for (item in items) {
          if (!item.trace) continue
          map[item.method + " " + item.path] = item
        }
        this.httpApiCallTraceConfigMap.putAll(map)
      }
    } catch (e: Exception) {
      logger.error("parseHttpApiCallTraceConfigs", e)
    }
  }

  /**
   * 考虑到历史遗留问题，M4 启动时，将弃用的 config.json 中单车应用的相关配置同步到没有单车相关配置的 bz.json 中。
   */
  @Suppress("UNCHECKED_CAST")
  private fun updateSingleFromBaseToBzOnStartIfNecessary() {
    val historicSingle = baseConfig.single

    // 如果 ScSingle 没有启用单车的相关配置，就更新，对应的路径为：ScSingle.singleConfig.enableScSingle
    val single = BzConfigManager.getByPath("ScSingle") as EntityValue? ?: HashMap()
    val singleConfig = single["singleConfig"] as EntityValue? ?: HashMap()
    if (singleConfig["enableScSingle"] == null) {
      logger.info("update historic single config from config.json to bz.json: set null to $historicSingle ...")
      singleConfig["enableScSingle"] = historicSingle
      single["singleConfig"] = singleConfig
      bzConfig["ScSingle"] = single
      // 更新文件，bz.json 有系统生成，不存在人为注释内容，直接替换全部内容。
      JsonFileHelper.writeJsonToFile(getBzFile(), bzConfig, true)
    }

    // 不清空 config.json 中的 single 配置也没关系。考虑到 SRC880 和 SRC3000 的性能，能省就省吧。
  }

  /**
   * 获取是否启用单车应用
   */
  fun singleEnabled(): Boolean = BzConfigManager.getByPathAsBoolean("ScSingle", "singleConfig", "enableScSingle")

  // @Suppress("UNCHECKED_CAST")
  // private fun parseSingleConfig() {
  //  val single = BzConfigManager.getByPath("ScSingle") as EntityValue? ?: HashMap()
  //  val singleConfig = single["singleConfig"] as EntityValue? ?: HashMap()
  //  // 如果 ScSingle 没有设置，则设置它
  //  if (singleConfig["enableScSingle"] == null) {
  //    singleConfig["enableScSingle"] = baseConfig.single
  //    single["singleConfig"] = singleConfig
  //    bzConfig["ScSingle"] = single
  //    updateBzConfig(bzConfig)
  //  }
  //
  //  // 如果已经启用 ScSingle 且 baseConfig 的 single 标志不同步，则同步设置
  //  // 当 baseConfig 和 ScSingle 都设置了的时候，以 ScSingle 为准
  //  if (singleConfig["enableScSingle"] != null) {
  //    baseConfig = baseConfig.copy(single = singleConfig["enableScSingle"] as Boolean)
  //    JsonFileHelper.writeJsonToFileIncremental(
  //      getBaseConfigFile(),
  //      JsonHelper.mapper.convertValue(baseConfig),
  //      pretty = true,
  //    )
  //  }
  // }

  private fun getMenuFile(): File = File(baseConfig.configDir, "menu.json")

  private fun getBzFile(): File = File(baseConfig.configDir, "bz.json")

  private fun getBaseConfigFile(): File = File(baseConfig.configDir, "config.json")
  fun mustGetEntityMeta(name: String): EntityMeta = entityMetaMap[name] ?: throw BzError("errNoSuchEntity", name)

  fun mustGetRefEntityMeta(fm: FieldMeta): EntityMeta {
    val refEntity = fm.refEntity
    if (refEntity.isNullOrBlank()) throw BzError("errRefFieldNoRefEntity", fm.name)
    return mustGetEntityMeta(refEntity)
  }
}

class QueryIdRecord(
  val queryToId: MutableMap<String, String> = ConcurrentHashMap(),
  val idToQuery: MutableMap<String, String> = ConcurrentHashMap(),
)

data class HttpApiCallTraceConfigItem(
  val method: String,
  val path: String,
  val trace: Boolean = false,
  val reqBodyOn: Boolean = false,
  val resBodyOn: Boolean = false,
)