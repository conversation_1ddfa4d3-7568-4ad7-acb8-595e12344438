package com.seer.trick.base.entity.service.extension

import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.change.EntityChange

open class EntityServiceExtension {
  
  /**
   * 返回非 null 表示拦截后续！
   */
  open fun beforeCreating(em: EntityMeta, evList: List<EntityValue>): List<String>? {
    return null
  }
  
  open fun afterCreating(em: EntityMeta, evList: List<EntityValue>) {
  }
  
  /**
   * 返回非 null 表示拦截后续！
   */
  open fun beforeUpdating(em: EntityMeta, ids: List<String>, update: EntityValue): Long? {
    return null
  }
  
  open fun afterUpdating(em: EntityMeta, changes: List<EntityChange>) {
  }
  
  /**
   * 返回非 null 表示拦截后续！
   */
  open fun beforeRemoving(em: EntityMeta, ids: List<String>): Long? {
    return null
  }
  
  open fun afterRemoving(em: EntityMeta, oldValues: List<EntityValue>) {
  }
  
  /**
   * 如数据库导入导出后
   */
  open fun afterBigChange(em: EntityMeta) {
  }
  
}
