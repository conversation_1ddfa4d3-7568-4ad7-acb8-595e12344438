package com.seer.trick.base.script

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.Cq
import com.seer.trick.I18N.lo
import com.seer.trick.base.alarm.AlarmAction
import com.seer.trick.base.alarm.AlarmItem
import com.seer.trick.base.alarm.AlarmLevel
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.base.concurrent.BaseConcurrentCenter.bgScriptExecutor
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.entity.service.RemoveOptions
import com.seer.trick.base.entity.service.extension.EntityServiceExtension
import com.seer.trick.base.entity.service.extension.EntityServiceExtensions
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.submitCatch
import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors
import java.util.concurrent.Future

object ScriptRobustExecutor : EntityServiceExtension() {
  
  private val logger = LoggerFactory.getLogger(this::class.java)
  
  private val executorMap: MutableMap<String, Future<*>> = ConcurrentHashMap()
  
  private val updateAlarmExecutor = Executors.newSingleThreadExecutor()
  
  @Volatile
  private var updateAlarmFuture: Future<*>? = null
  
  init {
    EntityServiceExtensions.addExtension("RobustScriptExecutor", this)
  }
  
  fun init() {
    updateAlarmFuture = updateAlarmExecutor.submit(::updateAlarmLoop)
  }
  
  fun dispose() {
    updateAlarmFuture?.cancel(true)
  }
  
  fun submit(description: String, funcName: String, args: Any?) {
    val argsStr = if (args == null) null else JsonHelper.writeValueAsString(args)
    val ev: EntityValue = mutableMapOf(
      "description" to description,
      "funcName" to funcName,
      "args" to argsStr,
    )
    val id = EntityRwService.createOne("RobustScriptExecutor", ev)
    executeAsync(id, funcName, argsStr)
  }
  
  private fun executeAsync(id: String, funcName: String, argsStr: String?) {
    executorMap[id] = bgScriptExecutor.submitCatch("Run bg script $funcName - $id", logger) {
      execute(id, funcName, argsStr)
      executorMap.remove(id)
    }
  }
  
  /**
   * 不抛异常
   */
  private fun execute(id: String, funcName: String, argsStr: String?) {
    try {
      val args: Map<String, Any?> = if (argsStr.isNullOrBlank()) {
        HashMap()
      } else {
        JsonHelper.mapper.readValue(argsStr, jacksonTypeRef())
      }
      ScriptCenter.execute(ScriptExeRequest(funcName, arrayOf(id, args)))
      // 删除任务，不要触发终止处理
      EntityRwService.removeOne("RobustScriptExecutor", Cq.idEq(id), RemoveOptions(muteExt = true))
    } catch (e: Throwable) {
      logger.error("执行后台任务报错", e)
      EntityRwService.updateOne(
        
        "RobustScriptExecutor",
        Cq.idEq(id),
        mutableMapOf("fault" to true, "faultMsg" to "[${e.javaClass.simpleName}] ${e.message}"),
      )
    }
  }
  
  fun runOnce(mainId: String, actionId: String, work: () -> EntityValue): EntityValue {
    logger.info("运行一次 '$mainId:$actionId'，开始")
    val id = "$mainId-$actionId"
    val old = EntityRwService.findOne("ScriptRunOnce", Cq.idEq(id))
    if (null != old) {
      val jsonStr = old["output"] as String?
      logger.info("运行一次 '$mainId:$actionId' ：但已运行过，输出：$jsonStr")
      if (jsonStr.isNullOrBlank()) return HashMap()
      return JsonHelper.mapper.readValue(jsonStr, jacksonTypeRef())
    }
    val output = work()
    val jsonStr = JsonHelper.writeValueAsString(output)
    EntityRwService.createOne("ScriptRunOnce", mutableMapOf("id" to id, "output" to jsonStr))
    return output
  }
  
  fun recoverAll() {
    val evList = EntityRwService.findMany("RobustScriptExecutor", Cq.all())
    if (evList.isEmpty()) return
    
    logger.info("恢复后台任务：${evList.size}")
    
    for (ev in evList) {
      recover(ev)
    }
  }
  
  fun recover(id: String) {
    val ev = EntityRwService.findOne("RobustScriptExecutor", Cq.idEq(id)) ?: return
    recover(ev)
  }
  
  fun recover(ev: EntityValue) {
    val id = EntityHelper.mustGetId(ev)
    val description = ev["description"] as String?
    val funcName = ev["funcName"] as String
    val argsStr = ev["args"] as String?
    logger.info("恢复后台任务：$id:$description:$funcName($argsStr)")
    
    EntityRwService.updateOne(
      "RobustScriptExecutor",
      Cq.idEq(id),
      mutableMapOf("fault" to false, "faultMsg" to ""),
    )
    
    executeAsync(id, funcName, argsStr)
  }
  
  fun abort(id: String) {
    val ev = EntityRwService.findOne("RobustScriptExecutor", Cq.idEq(id))
    val desc = if (ev != null) {
      EntityRwService.removeOne("RobustScriptExecutor", Cq.idEq(id))
      "函数=${ev["funcName"]}, 参数=${ev["args"]}, 描述=${ev["description"]}"
    } else {
      "找不到任务记录"
    }
    val e = executorMap.remove(id)
    if (e != null) {
      logger.info("取消后台任务。id=$id。$desc")
      e.cancel(true)
    } else {
      logger.warn("取消后台任务，但找不到执行器。id=$id。$desc")
    }
  }
  
  // 如果 RobustScriptExecutor，终止后台脚本
  override fun afterRemoving(em: EntityMeta, oldValues: List<EntityValue>) {
    if (em.name != "RobustScriptExecutor") return
    bgScriptExecutor.submitCatch("Abort bg script after remove", logger) {
      for (ev in oldValues) {
        abort(EntityHelper.mustGetId(ev))
      }
    }
  }
  
  private fun updateAlarmLoop() {
    while (true) {
      updateAlarm()
      Thread.sleep(500)
    }
  }
  
  private fun updateAlarm() {
    
    val evList = EntityRwService.findMany(
      
      "RobustScriptExecutor",
      Cq.eq("fault", true),
      FindOptions(projection = listOf("id", "fault", "faultMsg", "description")),
    )
    
    // 先清之前的
    AlarmService.removeAllByCode("RobustScriptExecutorFault")
    
    for (it in evList) {
      val desc = it["description"] as String? ?: ""
      val msg = it["faultMsg"] as String? ?: ""
      val id = EntityHelper.mustGetId(it)
      val ai = AlarmItem(
        group = "BgTask",
        code = "RobustScriptExecutorFault",
        key = "RobustScriptExecutorFault-$id",
        level = AlarmLevel.Error,
        message = "后台任务 <$desc> 失败。原因：$msg",
        args = listOf(id),
        actions = listOf(
          AlarmAction(lo("btnFaultRetry")),
          AlarmAction(lo("btnCancelTask")),
        ),
      )
      AlarmService.addItem(ai) { actionIndex, args ->
        if (actionIndex == 1) {
          // 故障重试
          recover(args[0] as String)
        } else if (actionIndex == 2) {
          // 取消任务
          abort(args[0] as String)
        }
      }
    }
  }
}