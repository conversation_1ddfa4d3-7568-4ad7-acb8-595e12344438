package com.seer.trick.base.entity.executor.sql

import com.seer.trick.Cq
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.db.DbManager
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.FieldScale
import com.seer.trick.base.entity.FieldType
import com.seer.trick.base.entity.executor.EntityRemoveWorkContext

object EntityRemoveAllExecutorSql {

  fun execute(ctx: EntityRemoveWorkContext, em: EntityMeta) {
    processEntity(ctx, em)

    DbManager.getSqlConnection().use { sc ->
      for (tableName in ctx.removing.keys) {
        val query = ctx.removing[tableName] ?: continue
        SqlExecutor.delete(sc, tableName, query)
      }
    }
  }

  private fun processEntity(ctx: EntityRemoveWorkContext, em: EntityMeta) {
    ctx.removing[em.name] = Cq.all()

    for (fm in em.fields.values) {
      if ((fm.type == FieldType.File || fm.type == FieldType.Image) && fm.scale != FieldScale.Single) {
        val relatedTableName = fm.buildRelatedTableName(em.name)
        ctx.removing[relatedTableName] = Cq.all()
      } else if (fm.type == FieldType.Reference && fm.scale != FieldScale.Single) {
        val relatedTableName = fm.buildRelatedTableName(em.name)
        ctx.removing[relatedTableName] = Cq.all()
      } else if (fm.type == FieldType.Component) {
        val refComEm = BaseCenter.mustGetRefEntityMeta(fm)
        processEntity(ctx, refComEm) // 级联处理
      } else if (fm.scale != FieldScale.Single) {
        val relatedTableName = fm.buildRelatedTableName(em.name)
        ctx.removing[relatedTableName] = Cq.all()
      }
    }
  }
}
