package com.seer.trick.base.ws

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.base.concurrent.BaseConcurrentCenter.wsClientExecutor
import com.seer.trick.base.concurrent.BaseConcurrentCenter.wsMonitorExecutor
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.http.WsMsg
import com.seer.trick.base.soc.SocAttention
import com.seer.trick.base.soc.SocService
import com.seer.trick.base.soc.SysMonitorService
import com.seer.trick.helper.BoolHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.getTypeMessage
import com.seer.trick.helper.submitLongRun
import okhttp3.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Future
import java.util.concurrent.TimeUnit
import java.util.concurrent.TimeoutException
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

class WsClient(
  private val url: String,
  private val onOpen: (ws: WebSocket) -> Unit,
  private val onMessage: (ws: WebSocket, msg: WsMsg) -> Unit,
) {
  
  private val logger: Logger = LoggerFactory.getLogger(javaClass)
  
  @Volatile
  private var webSocket: WebSocket? = null
  
  @Volatile
  private var status: Status = Status.Init
  
  @Volatile
  private var rebuildCount = 0L
  
  @Volatile
  private var monitorFuture: Future<*>? = null
  
  private val pendingRequestMap: MutableMap<String, WsMsg> = ConcurrentHashMap()
  private val replyMap: MutableMap<String, WsMsg> = ConcurrentHashMap()
  private val lock = ReentrantLock()
  private val condition = lock.newCondition()
  
  fun getUrl() = url
  
  fun init() {
    open()
    
    monitorFuture = wsMonitorExecutor.submitLongRun("监控 WS 客户端 $url", logger, { 1000 }) {
      if (status == Status.Init || status == Status.Failure || status == Status.Closed) {
        try {
          open()
        } catch (e: Exception) {
          logger.error("open 失败", e)
        }
      } else {
        // Ping 失败 close
        try {
          request(WsMsg("BzPing"), 3 * 1000)
        } catch (e: Throwable) {
          close(e.getTypeMessage())
        }
      }
      // TODO 处于 building 时间过长
    }
  }
  
  fun dispose() {
    monitorFuture?.cancel(true)
    status = Status.Disposed
    SocService.removeNode("WSClient:$url")
  }
  
  fun getWebSocket(): WebSocket? = webSocket
  
  @Synchronized
  fun requestNoResp(msg: WsMsg, timeout: Long = 10 * 1000) {
    val ws = webSocket ?: throw BzError("errWsNotConnected", url, null)
    
    if (status != Status.Open) throw BzError("errWsNotConnected", url, status)
    
    // 在发送之前就添加的目的是为了避免发送和响应的时序问题
    // 宇峰现场出现先收到响应再塞入 pendingRequestMap
    pendingRequestMap[msg.id] = msg
    // 加上超时处理。注意：submit 得标记返回值类型，否则会导致 future 为 null，最终导致 writeRes 为 false，并报错。
    val future = wsClientExecutor.submit<Boolean> { ws.send(JsonHelper.writeValueAsString(msg)) }
    var writeRes: Boolean? = null
    try {
      writeRes = BoolHelper.anyToBool(future.get(timeout, TimeUnit.MILLISECONDS))
      if (!writeRes) throw BzError("errWsSendFail", url)
    } catch (e: TimeoutException) {
      logger.error("requestNoResp TimeoutException" + e.getTypeMessage())
      throw BzError(e, "errWsSendTimeout", url)
    } catch (e: Throwable) {
      logger.error("requestNoResp Throwable" + e.getTypeMessage())
      throw BzError(e, "errUnexpectedException", url)
    } finally {
      // 发送没成功，从 pendingRequestMap 中移除
      if (writeRes != true) pendingRequestMap.remove(msg.id)
    }
  }
  
  @Synchronized
  fun request(msg: WsMsg, timeout: Long = 10 * 1000): WsMsg {
    requestNoResp(msg, timeout)
    
    pendingRequestMap[msg.id] = msg
    
    val logOn = BzConfigManager.getByPath("ScDev", "logWsClient") == true
    if (logOn) logger.debug("[$url][#$rebuildCount] WebSocket 发送请求响应模式的请求：$msg")
    
    val startTime = System.currentTimeMillis()
    while (status == Status.Open) {
      lock.withLock {
        val res = replyMap.remove(msg.id)
        if (res != null) return res
        val elapsed = System.currentTimeMillis() - startTime
        if (elapsed >= timeout) {
          logger.error("[#$rebuildCount] WebSocket 请求超时：$msg")
          throw BzError("errWsSendTimeout", url)
        }
//        logger.info("[#$rebuildCount] WebSocket 等待响应：$msg")
        try {
          if (!condition.await(timeout, TimeUnit.MILLISECONDS)) throw BzError("errWsSendTimeout", url)
        } catch (e: InterruptedException) {
          logger.error("request InterruptedException" + e.getTypeMessage())
          throw BzError(e, "errWsSendTimeout", url)
        } catch (e: Throwable) {
          logger.error("request Throwable" + e.getTypeMessage())
          throw BzError(e, "errUnexpectedException", url)
        }
      }
    }
    throw BzError("errWsNotConnected", url, status)
  }
  
  @Synchronized
  private fun open() {
    close()
    
    rebuildCount++
    status = Status.Building
    
    logger.info("[$url][#$rebuildCount] 初始化 WebSocket 客户端")
    updateSoc("[#$rebuildCount] 初始化")
    
    val request = Request.Builder().url(url).build()
    webSocket = client.newWebSocket(
      request,
      object : WebSocketListener() {
        override fun onOpen(webSocket: WebSocket, response: Response) {
          logger.info("[$url][#$rebuildCount] WebSocket 已连接")
          updateSoc("[#$rebuildCount] onOpen")
          status = Status.Open
          onOpen(webSocket)
        }
        
        override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
          // 如果立即连接不上（比如服务器没开）也会进入这里
          updateSoc("[#$rebuildCount] onFailure: " + t.getTypeMessage(), SocAttention.Red)
          logger.error("[#$rebuildCount] onFailure: " + t.getTypeMessage())
          status = Status.Failure
          close()
        }
        
        override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
          updateSoc("[#$rebuildCount] onClosed: code=$code, reason=$reason", SocAttention.Red)
          status = Status.Closed
          close()
        }
        
        override fun onMessage(webSocket: WebSocket, text: String) {
          val logOn = BzConfigManager.getByPath("ScDev", "logWsClient") == true
          if (logOn) logger.debug("[$url][#$rebuildCount] WebSocket 客户端收到：$text")
          
          val msg: WsMsg = JsonHelper.mapper.readValue(text, jacksonTypeRef())
          if (pendingRequestMap.containsKey(msg.replyToId)) {
            wsClientExecutor.submit {
              lock.withLock {
                replyMap[msg.replyToId] = msg
                condition.signalAll()
              }
            }
          } else {
            onMessage(webSocket, msg)
          }
        }
      },
    )
  }
  
  fun close(reason: String = "") {
    // https://datatracker.ietf.org/doc/html/rfc6455#section-7.4
    val logOn = BzConfigManager.getByPath("ScDev", "logWsClient") == true
    if (logOn) logger.debug("[$url][#$rebuildCount] close, reason=$reason")
    
    webSocket?.close(1000, reason)
    webSocket = null
  }
  
  private fun updateSoc(msg: String, a: SocAttention = SocAttention.None) {
    SysMonitorService.log(
      subject = "WsClient",
      target = "WsClient:$url",
      field = "updateSoc",
      value = "update WsClient status $msg",
    )
    
    SocService.updateNode("网络", "WSClient:$url", "WS 客户端", msg, a)
  }
  
  companion object {
    
    // val logging = HttpLoggingInterceptor()
    // logging.setLevel(HttpLoggingInterceptor.Level.BODY)
    private val client: OkHttpClient = OkHttpClient.Builder()
      // 实测 Javalin/Jetty 不会自动响应 PONG，但网上又说会
      .pingInterval(5, TimeUnit.SECONDS)
      .connectTimeout(10, TimeUnit.SECONDS)
      .readTimeout(10, TimeUnit.SECONDS)
      .writeTimeout(10, TimeUnit.SECONDS)
      .callTimeout(10, TimeUnit.SECONDS)
      .retryOnConnectionFailure(false)
      // .addInterceptor(logging)
      .build()
  }
  
  enum class Status {
    Init,
    Building,
    Open,
    Failure,
    Closed,
    Disposed,
  }
}