package com.seer.trick.base.concurrent

import com.seer.trick.helper.getTypeMessage
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.ConcurrentHashMap

/**
 * 周期运行的任务的管理
 * 暂时终止了也不会从 jobs 中移除
 */
object PollingJobManager {

  private val logger = LoggerFactory.getLogger(javaClass)

  // by thread name
  private val jobs: MutableMap<String, PollingJob> = ConcurrentHashMap()

  /**
   * 提交一个周期运行的任务
   */
  @Synchronized
  fun submit(
    threadName: String, // 必须唯一，尽量不用中文
    remark: String, // 解释
    interval: () -> Long, // 每次运行完等待时间，毫秒
    logger: Logger, // 日志
    workerMaxTime: Long, // 运行时间超过多少毫秒算作运行时间过长，-1 表示不限制
    stopCondition: () -> <PERSON><PERSON><PERSON>, // 停止条件
    exceptionContinue: Bo<PERSON><PERSON>, // 如果 worker 抛出异常，是否终止循环，true 表示不终止
    tags: Set<String>, // 标签
    worker: () -> Unit, // 尽量不要抛出异常，抛出异常默认视为终止循环
  ) {
    val oldJob = jobs.remove(threadName)
    if (oldJob != null) {
      logger.warn("Stop old job $threadName")
      oldJob.record.stop = true
      // 先不等线程退出了
      // oldJob.thread.join()
    }
    val record = PollingJobRecord(threadName, remark, workerMaxTime, tags)
    val job = PollingJob(record)
    val t = Thread {
      while (!stopCondition() && !record.stop) {
        record.error = false
        record.errMsg = null
        try {
          record.lastRunStart = System.currentTimeMillis()

          worker()

          record.runNum++
          record.lastRunEnd = System.currentTimeMillis()
          val runTime = record.lastRunEnd - record.lastRunStart
          record.runTimes.add(runTime)
          if (record.runTimes.size > 10) record.runTimes.removeAt(0)
        } catch (e: Throwable) {
          logger.error("Exception when polling job", e)

          record.error = true
          record.errMsg = e.getTypeMessage()
          record.errorNum++

          if (!exceptionContinue) {
            record.stop = true
            break
          }
        }

        // 不捕获中断
        Thread.sleep(interval())
      }

      logger.info("Job end $threadName")
      record.stop = true
    }
    t.name = threadName
    job.thread = t
    jobs[threadName] = job
    t.start()
  }

  fun list(): List<PollingJobRecord> = jobs.values.map { it.record }

  fun getJobByThreadName(threadName: String): PollingJobRecord? = jobs[threadName]?.record

  /**
   * 根据标签停止、移除后台的轮训
   */
  fun removeByTag(tag: String) {
    val jobKeys = jobs.keys.toList()
    for (jobKey in jobKeys) {
      val job = jobs[jobKey] ?: continue
      if (job.record.tags.contains(tag)) {
        logger.info("Stop and remove polling job by tag '$tag': ${job.thread.name}")
        job.record.stop = true
        jobs.remove(jobKey)
      }
    }
  }
}

/**
 * 记录一个周期运行的任务
 */
class PollingJobRecord(
  val threadName: String,
  val remark: String, // 解释
  val workerMaxTime: Long,
  val tags: Set<String> = emptySet(), // 标签
) {

  /**
   * 已停止
   */
  @Volatile
  var stop: Boolean = false

  /**
   * 开始执行时间
   */
  val startedOn: Long = System.currentTimeMillis()

  /**
   * 运行次数
   */
  @Volatile
  var runNum: Long = 0

  /**
   * 历史执行时间
   */
  val runTimes: MutableList<Long> = Collections.synchronizedList(LinkedList())

  /**
   * 最后一次运行开始时间
   */
  @Volatile
  var lastRunStart = 0L

  /**
   * 最后一次运行结束时间
   */
  @Volatile
  var lastRunEnd = 0L

  /**
   * 最后一次运行是否故障
   */
  @Volatile
  var error: Boolean = false

  /**
   * 最后一次运行故障消息
   */
  @Volatile
  var errMsg: String? = null

  /**
   * 故障次数
   */
  @Volatile
  var errorNum: Long = 0
}

/**
 * 持有一个周期运行的任务
 */
class PollingJob(val record: PollingJobRecord) {
  lateinit var thread: Thread
}