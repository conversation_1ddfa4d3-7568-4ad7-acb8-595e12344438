package com.seer.trick.base.script

import com.seer.trick.base.file.FileManager
import org.apache.commons.io.FileUtils
import java.io.File

object ScriptFile {

  fun strToFile(pathStr: String): File {
    return File(pathStr)
  }

  fun joinDirFile(dir: String, file: String): File {
    return File(dir, file)
  }

  fun moveFile(srcFile: File, dstFile: File) {
    FileUtils.moveFile(srcFile, dstFile)
  }

  fun moveDirectory(srcDir: File, dstDir: File) {
    FileUtils.moveDirectory(srcDir, dstDir)
  }

  fun removeFile(dir: File) {
    FileUtils.delete(dir)
  }

  fun removeDirectory(dir: File) {
    FileUtils.deleteDirectory(dir)
  }

  fun ensureFilesDir(): File {
    return FileManager.getFilesDir()
  }

  fun pathToFile(path: String): File {
    return FileManager.pathToFile(path)
  }

  fun fileToPath(file: File): String {
    return FileManager.fileToPath(file)
  }

  fun ensureTmpDir(): File {
    return FileManager.ensureTmpDir()
  }

  fun nextTmpFile(ext: String, prefix: String , suffix: String ): File {
    return FileManager.nextTmpFile(ext, prefix, suffix)
  }

}