package com.seer.trick.base.db.sql

import com.microsoft.sqlserver.jdbc.SQLServerException
import com.seer.trick.BzError
import com.seer.trick.ComplexQuery
import com.seer.trick.ComplexQueryOperator
import com.seer.trick.ComplexQueryType
import com.seer.trick.base.DbType
import com.seer.trick.base.entity.EntityIndexDef
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.executor.mongo.QueryToDocument
import com.seer.trick.helper.DateHelper
import org.apache.commons.lang3.StringUtils
import org.apache.commons.lang3.time.DateUtils
import org.slf4j.LoggerFactory
import java.sql.*
import java.sql.Date
import java.time.Instant
import java.util.*
import java.util.regex.Pattern

object SqlHelper {
  
  private val logger = LoggerFactory.getLogger(javaClass)
  
  fun escapeIdentifier(identifier: String, sqlDialect: DbType): String = if (sqlDialect == DbType.MySQL ||
    sqlDialect == DbType.SqlServer ||
    sqlDialect == DbType.Derby ||
    sqlDialect == DbType.Dameng
  ) {
    String.format("\"%s\"", identifier)
  } else {
    identifier
  }
  
  private fun isColumnNameValid(column: String): Boolean =
    StringUtils.isNotBlank(column) && column.matches("^[a-zA-Z0-9_]+$".toRegex())
  
  fun checkColumnNameValid(column: String) {
    if (!isColumnNameValid(column)) throw BzError("errBadColumnName", column)
  }
  
  fun setToStatement(st: PreparedStatement, params: List<Any>) {
    val paramsLength = params.size
    for (index in 0 until paramsLength) {
      val pi = index + 1
      val value = params[index]
      if (value is String) {
        st.setString(pi, value)
      } else if (value is Int) {
        st.setInt(pi, value)
      } else if (value is Long) {
        st.setLong(pi, value)
      } else if (value is Float) {
        st.setFloat(pi, value)
      } else if (value is Double) {
        st.setDouble(pi, value)
      } else if (value is java.util.Date) {
        st.setTimestamp(pi, Timestamp(value.time)) // TODO 可能有问题，SQL 的日期和实时间列
      } else if (value is Instant) {
        st.setTimestamp(pi, Timestamp(value.toEpochMilli()))
      } else if (value is Boolean) {
        st.setBoolean(pi, value)
      } else {
        throw BzError("errSetToStatementInvalidJavaClass", value.javaClass)
      }
    }
  }
  
  fun objectToSetClause(obj: Map<String, Any?>, params: MutableList<Any>, sqlDialect: DbType): String {
    val columns: MutableList<String?> = ArrayList()
    for (field in obj.keys) {
      checkColumnNameValid(field)
      val column = escapeIdentifier(field, sqlDialect)
      val value = obj[field]
      if (value == null) {
        columns.add(String.format("%s = NULL", column))
      } else {
        params.add(value)
        columns.add(String.format("%s = ?", column))
      }
    }
    val ver = escapeIdentifier("version", sqlDialect)
    return StringUtils.join(columns, ", ") + ", $ver = $ver + 1"
  }
  
  fun buildSqlQuery(
    query: ComplexQuery,
    params: MutableList<Any>,
    sqlDialect: DbType,
    tableName: String,
  ): String? {
    return if (query.type == ComplexQueryType.All) {
      null
    } else if (query.type == ComplexQueryType.Compound) {
      val sqlParts: MutableList<String> = ArrayList()
      if (!query.items.isNullOrEmpty()) {
        for (item in query.items!!) {
          val sqlPart = buildSqlQuery(item, params, sqlDialect, tableName)
          if (sqlPart != null) sqlParts.add(sqlPart)
        }
      }
      if (sqlParts.isEmpty()) return null
      val base: String = if (query.or) {
        String.format("(%s)", StringUtils.join(sqlParts, " OR "))
      } else {
        StringUtils.join(sqlParts, " AND ")
      }
      if (query.not) {
        String.format("NOT (%s)", base)
      } else {
        base
      }
    } else {
      if (StringUtils.isBlank(query.field1)) throw BzError("errComplexQueryMissingField1")
      
      if (query.operator == null) throw BzError("errComplexQueryMissingOp")
      
      val eColumn = escapeIdentifier(query.field1!!, sqlDialect)
      
      val tableDef = SqlSchemaManager.tableDefMap[tableName] ?: throw BzError("errCodeErr", "No table $tableName")
      val columnDef = tableDef.columns[query.field1]
      val base: String = if (query.operator == ComplexQueryOperator.Eq && query.value == false) {
        // 特殊处理 == false
        "($eColumn IS NULL OR $eColumn = 0)"
      } else if (query.operator == ComplexQueryOperator.Eq &&
        query.value is java.util.Date
      ) {
        val queryDate = query.value as java.util.Date
        val rightDate = if (columnDef?.type.equals("DATE")) {
          QueryToDocument.adjustDate(queryDate, days = 1)
        } else {
          QueryToDocument.adjustDate(queryDate, seconds = 1)
        }
        params.add(queryDate)
        params.add(rightDate)
        "($eColumn >= ? AND $eColumn < ?)"
      } else if (query.operator == ComplexQueryOperator.Ne && query.value == true) {
        // 特殊处理 != true
        "($eColumn IS NULL OR $eColumn = 0)"
      } else if (query.operator == ComplexQueryOperator.Ne && query.value is Date) {
        val queryDate = query.value as java.util.Date
        val rightDate = QueryToDocument.adjustDate(queryDate, seconds = 1)
        params.add(queryDate)
        params.add(rightDate)
        "($eColumn < ? OR $eColumn >= ?)"
      } else if (query.operator == ComplexQueryOperator.CurrentUser) {
        throw IllegalArgumentException("Op 'CurrentUser' no supported in handlers")
      } else if (query.operator == ComplexQueryOperator.CurrentUsername) {
        throw IllegalArgumentException("Op 'CurrentUsername' no supported in handlers")
      } else if (query.operator == ComplexQueryOperator.ThisDay) {
        val todayStart = DateHelper.getDayStart(java.util.Date())
        val todayEnd = DateHelper.getDayEnd(java.util.Date())
        
        params.add(todayStart)
        params.add(todayEnd)
        
        "($eColumn >= ? AND $eColumn <= ?)"
      } else if (query.operator == ComplexQueryOperator.ThisWeek) {
        val thisMonday = DateHelper.getMondayOfWeek(java.util.Date())
        val nextMonday = DateUtils.addDays(thisMonday, 7)
        
        params.add(thisMonday)
        params.add(nextMonday)
        
        "($eColumn >= ? AND $eColumn < ?)"
      } else if (query.operator == ComplexQueryOperator.Null) {
        String.format("%s IS NULL", eColumn)
      } else if (query.operator == ComplexQueryOperator.NotNull) {
        String.format("%s IS NOT NULL", eColumn)
      } else if (query.operator == ComplexQueryOperator.Empty) {
        String.format("(%s IS NULL OR %s = '')", eColumn, eColumn)
      } else if (query.operator == ComplexQueryOperator.NotEmpty) {
        String.format("(%s IS NOT NULL AND %s <> '')", eColumn, eColumn)
      } else {
        val sqlOperator = query.operator!!.sqlOp
        // if (query.subQuery != null) {
        //   val subQuery = buildSubQuery(query.subQuery, params, sqlDialect)
        //   "$eColumn $sqlOperator $subQuery"
        // } else
        if (StringUtils.isNotBlank(query.field2)) {
          String.format(
            "%s %s %s",
            eColumn,
            sqlOperator,
            escapeIdentifier(query.field2!!, sqlDialect),
          )
        } else if (query.value != null) {
          if (query.operator == ComplexQueryOperator.Eq &&
            sqlDialect == DbType.SqlServer &&
            columnDef?.type == "TEXT"
          ) {
            // 特殊处理 SQL Server == TEXT，只能用 LIKE
            params.add(query.value!!)
            "$eColumn LIKE ?"
          } else if (query.operator == ComplexQueryOperator.Ne &&
            sqlDialect == DbType.SqlServer &&
            columnDef?.type == "TEXT"
          ) {
            // 特殊处理 SQL Server != TEXT，只能用 LIKE
            params.add(query.value!!)
            "$eColumn NOT LIKE ?"
          } else if (query.operator == ComplexQueryOperator.In &&
            sqlDialect == DbType.SqlServer &&
            columnDef?.type == "TEXT"
          ) {
            // 特殊处理 SQL Server IN TEXT，只能用 LIKE
            val queryValue = query.value
            val sqlParts = mutableListOf<String>()
            if (queryValue is List<*>) {
              for (item in queryValue) {
                if (item == null) continue
                params.add(item)
                sqlParts += "$eColumn LIKE ?"
              }
              if (sqlParts.isNotEmpty()) sqlParts.joinToString(" OR ") else " 1 <> 1"
            } else {
              " 1 <> 1"
            }
          } else if (query.operator == ComplexQueryOperator.In) {
            val inClause = buildInClause(query.value!!, params) ?: return " 1 <> 1"
            String.format("%s IN %s", eColumn, inClause)
          } else if (query.operator == ComplexQueryOperator.Between) {
            if (query.value == null) throw BzError("errComplexQueryValueNull", "")
            val list = query.value as List<*>
            if (list.size != 2) throw BzError("errComplexQueryValueNeedTwo", "")
            params.add(list[0]!!)
            params.add(list[1]!!)
            "$eColumn BETWEEN ? and ?"
          } else if (query.operator == ComplexQueryOperator.Start) {
            params.add(query.value.toString() + "%")
            String.format("%s LIKE ?", eColumn)
          } else if (query.operator == ComplexQueryOperator.End) {
            params.add("%" + query.value)
            String.format("%s LIKE ?", eColumn)
          } else if (query.operator == ComplexQueryOperator.Contain) {
            params.add("%" + query.value + "%")
            String.format("%s LIKE ?", eColumn)
          } else if (query.operator == ComplexQueryOperator.ContainIgnoreCase) {
            params.add("%" + query.value + "%")
            String.format("UPPER(%s) LIKE UPPER(?)", eColumn)
          } else {
            if (Objects.equals(query.value, true)) {
              params.add(1)
            } else if (Objects.equals(query.value, false)) {
              params.add(0)
            } else {
              params.add(query.value!!)
            }
            String.format("%s %s ?", eColumn, sqlOperator)
          }
        } else {
          return null
        }
      }
      if (query.not) String.format("NOT (%s)", base) else base
    }
  }
  
  // 如果条件无效，返回 null
  private fun buildInClause(comparedValue: Any, params: MutableList<Any>): String? {
    val placeholders: MutableList<String?> = ArrayList()
    if (comparedValue is List<*>) {
      for (item in comparedValue) {
        if (item == null) continue
        params.add(item)
        placeholders.add("?")
      }
    }
    return if (placeholders.isEmpty()) null else String.format("(%s)", StringUtils.join(placeholders, ", "))
  }
  
  // WmsMaterial: The statement was aborted because it would have caused a duplicate key value in a unique or primary key constraint or unique index identified by 'WmsMaterial_ID' defined on 'WmsMaterial'.
  // identified by 'WmsMaterial_ID' defined on 'WmsMaterial'.
  // \u4e00-\u9fa5\w 兼容中文英文索引名
  private val derbyUniqueErrorPattern = Pattern.compile("identified by '([\\u4e00-\\u9fa5\\w]+)' defined on '(\\w+)'")
  
  // Duplicate entry 'test1' for key 'controlledagv.ID'
  // \u4e00-\u9fa5\w 兼容中文英文索引名
  private val mysqlUniqueErrorPattern =
    Pattern.compile("Duplicate entry '([\\w_-]+)' for key '((\\w+).)?([\\u4e00-\\u9fa5\\w]+)'")
  
  // 不能在具有唯一索引“m4_AgentUserindex_app”的对象“dbo.AgentUser”中插入重复键的行。重复键值为 (app2)。
  // \u4e00-\u9fa5\w 兼容中文英文索引名
  private val sqlServerUniqueErrorPatternCN = Pattern.compile(
    "不能在具有唯一索引“([\\u4e00-\\u9fa5\\w]+)”的对象“((\\w+).)?(\\w+)”中" +
      "插入重复键的行。重复键值为 \\((\\w+)\\)。",
  )
  
  // Cannot insert duplicate key row in object 'dbo.AgentUser' with unique index 'm4_AgentUserindex_app'. The duplicate key value is (John).
  // \u4e00-\u9fa5\w 兼容中文英文索引名
  private val sqlServerUniqueErrorPatternEN = Pattern.compile(
    "Cannot insert duplicate key row in " +
      "object '((\\w+).)?(\\w+)' with unique index '([\\u4e00-\\u9fa5\\w]+)'. The duplicate key value is \\((\\w+)\\).",
  )
  
  fun parseUniqueConstraintViolation(
    e: Exception,
    dialect: DbType,
    sql: String,
    params: List<Any>,
  ): SqlUniqueConstraintViolation {
    val message: String? = e.message
    var tableName = ""
    var indexName = ""
    var content: String? = null
    if (message == null) {
      return SqlUniqueConstraintViolation("", "", null, e)
    }
    if (dialect == DbType.Derby) {
      val m = derbyUniqueErrorPattern.matcher(message)
      if (!m.find()) return SqlUniqueConstraintViolation("", "", null, e)
      val tableIndex = m.group(1)
      tableName = m.group(2)
      indexName = tableIndex.substring(tableName.length + 1)
    } else if (dialect == DbType.MySQL) {
      val m = mysqlUniqueErrorPattern.matcher(message)
      if (!m.find()) return SqlUniqueConstraintViolation("", "", null, e)
      content = m.group(1)
      tableName = m.group(3) // 注意可能变小写
      indexName = m.group(4)
    } else if (dialect == DbType.SqlServer) {
      var m = sqlServerUniqueErrorPatternCN.matcher(message)
      if (!m.find()) {
        m = sqlServerUniqueErrorPatternEN.matcher(message)
        if (!m.find()) return SqlUniqueConstraintViolation("", "", null, e)
        indexName = m.group(4)
        tableName = m.group(3)
        content = m.group(5)
      } else {
        indexName = m.group(1)
        tableName = m.group(4)
        content = m.group(5)
      }
    }
    if (content.isNullOrBlank()) {
      content = "SQL: $sql. Params: $params"
    }
    return SqlUniqueConstraintViolation(tableName, indexName, content, e)
  }
  
  fun buildOrderByClause(sort: List<String>, sqlDialect: DbType): String {
    val orders: MutableList<String?> = ArrayList()
    for (item in sort) {
      val fl = item[0]
      val field = if (fl == '+' || fl == '-') item.substring(1) else item
      val order = if (fl == '-') "DESC" else "ASC"
      checkColumnNameValid(field)
      val column = escapeIdentifier(field, sqlDialect)
      orders.add("$column $order")
    }
    return StringUtils.join(orders, ", ")
  }
  
  fun resultSetToEntityValues(tableName: String, rs: ResultSet): List<EntityValue> {
    val evList: MutableList<EntityValue> = ArrayList()
    
    val md: ResultSetMetaData = rs.metaData
    val columnsCount = md.columnCount
    while (rs.next()) {
      val ev: EntityValue = mutableMapOf()
      evList.add(ev)
      for (i in 1..columnsCount) {
        val colName = md.getColumnName(i)
        var v: Any? = null
        val ct = md.getColumnType(i)
        if (ct == Types.NULL) {
          v = null
        } else if (ct == Types.BIT || ct == Types.TINYINT || ct == Types.SMALLINT || ct == Types.INTEGER) {
          v = rs.getInt(i)
        } else if (ct == Types.BIGINT) {
          v = rs.getLong(i)
        } else if (ct == Types.FLOAT ||
          ct == Types.REAL ||
          ct == Types.DOUBLE ||
          ct == Types.NUMERIC ||
          ct == Types.DECIMAL
        ) {
          v = rs.getDouble(i)
        } else if (ct == Types.DATE) {
          val d: Date? = rs.getDate(i)
          v = if (d != null) java.util.Date(d.time) else null
        } else if (ct == Types.TIME) {
          val d: Time? = rs.getTime(i)
          v = if (d != null) java.util.Date(d.time) else null
        } else if (ct == Types.TIMESTAMP) {
          val d: Timestamp? = rs.getTimestamp(i)
          v = if (d != null) java.util.Date(d.time) else null
        } else if (ct == Types.BOOLEAN) {
          v = rs.getBoolean(i)
        } else if (ct == Types.VARCHAR ||
          ct == Types.CHAR ||
          ct == Types.CLOB ||
          ct == Types.NCLOB ||
          ct == Types.LONGVARCHAR ||
          ct == Types.NVARCHAR
        ) {
          v = rs.getString(i)
        } else {
          logger.error("未知 SQL 数据类型 $tableName:$colName = $ct")
        }
        // 先调用 get 再调用 null
        ev[colName] = if (rs.wasNull()) null else v
      }
    }
    return evList
  }
  
  fun executeSql(sc: SmartConnection, sql: String) {
    val st = sc.createStatement()
    try {
      st.execute(sql)
    } finally {
      try {
        st.close()
      } catch (e: SQLException) {
        logger.error("关闭 Statement 报错", e)
      }
    }
  }
  
  fun executePreparedUpdate(sc: SmartConnection, sql: String, params: List<Any>): Int {
    val st: PreparedStatement = sc.prepareStatement(sql, true)
    return try {
      setToStatement(st, params)
      st.executeUpdate()
    } catch (e: SQLIntegrityConstraintViolationException) {
      throw parseUniqueConstraintViolation(e, sc.sqlDialect, sql, params)
    } catch (e: SQLServerException) {
      throw parseUniqueConstraintViolation(e, sc.sqlDialect, sql, params)
    } finally {
      try {
        st.close()
      } catch (e: SQLException) {
        logger.error("关闭 Statement 报错", e)
      }
    }
  } // private fun buildSubQuery(subQuery: ComplexSubQuery, params: MutableList<Any>, sqlDialect: DbType): String {
  //    val column = escapeIdentifier(subQuery.selectColumn, sqlDialect)
  //    val tableName = escapeIdentifier(subQuery.table, sqlDialect)
  //    val where = buildSqlQuery(subQuery.where, params, sqlDialect) ?: throw ObservableError("Sub query has no WHERE")
  //    return "(SELECT $column FROM $tableName WHERE $where)"
  //  }
  
  /**
   * 检查索引是否有效
   */
  fun checkIndexValid(dbType: DbType, em: EntityMeta, index: EntityIndexDef): Boolean {
    var len = 0
    for (field in index.fields) {
      val fieldMeta = em.fields[field.name]
      if (fieldMeta == null) {
        logger.error("Field ${field.name} for index ${index.name} does not exist")
        return false
      }
      len += fieldMeta.length
    }
    val maxIndexLen = maxIndexLen(dbType)
    // TODO 暂时按 UTF-8 一个字符 4 字节来计算
    if (maxIndexLen < len * 4) {
      logger.error(
        "Total length of index ${index.name} fields $len for table ${em.name} exceeds limit $maxIndexLen",
      )
      return false
    }
    return true
  }
  
  /**
   * 索引长度的最大限制
   */
  fun maxIndexLen(dbType: DbType): Int = when (dbType) {
    DbType.MySQL -> 3072
    DbType.Dameng -> 3072
    DbType.Derby -> 2000 // derby 限制的总长为 32767，但其实超过一定限制后是无意义的
    DbType.SqlServer -> 1700
    else -> 3072 // 其他类型暂时默认给 3072 限制
  }
}