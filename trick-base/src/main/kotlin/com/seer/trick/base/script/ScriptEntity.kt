package com.seer.trick.base.script

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.ComplexQuery
import com.seer.trick.Cq
import com.seer.trick.base.MapToAnyNull
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.*
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.NumHelper

object ScriptEntity {
  
  fun createOne(tc: ScriptTraceContext, entityName: String, evJson: EntityValue, o: MapToAnyNull? = null): String {
    val o2: CreateOptions = if (o != null) JsonHelper.mapper.convertValue(o, jacksonTypeRef()) else CreateOptions()
    return EntityRwService.createOne(entityName, evJson, o2)
  }
  
  fun createMany(
    tc: ScriptTraceContext,
    entityName: String,
    evJsonList: List<EntityValue>,
    o: MapToAnyNull? = null,
  ): List<String> {
    val o2: CreateOptions = if (o != null) JsonHelper.mapper.convertValue(o, jacksonTypeRef()) else CreateOptions()
    return EntityRwService.createMany(entityName, evJsonList, o2)
  }
  
  fun updateOne(
    tc: ScriptTraceContext,
    entityName: String,
    queryJson: ComplexQuery,
    updateJson: EntityValue,
    o: MapToAnyNull? = null,
  ): Long {
    val o2: UpdateOptions = if (o != null) JsonHelper.mapper.convertValue(o, jacksonTypeRef()) else UpdateOptions(1)
    return EntityRwService.updateOne(entityName, queryJson, updateJson, o2)
  }
  
  fun updateOneById(
    tc: ScriptTraceContext,
    entityName: String,
    id: String,
    updateJson: EntityValue,
    o: MapToAnyNull? = null,
  ): Long {
    val o2: UpdateOptions = if (o != null) JsonHelper.mapper.convertValue(o, jacksonTypeRef()) else UpdateOptions()
    return EntityRwService.updateOne(entityName, Cq.idEq(id), updateJson, o2)
  }
  
  fun updateMany(
    tc: ScriptTraceContext,
    entityName: String,
    queryJson: ComplexQuery,
    updateJson: EntityValue,
    o: MapToAnyNull? = null,
  ): Long {
    val o2: UpdateOptions = if (o != null) JsonHelper.mapper.convertValue(o, jacksonTypeRef()) else UpdateOptions()
    return EntityRwService.updateMany(entityName, queryJson, updateJson, o2)
  }
  
  fun removeOne(tc: ScriptTraceContext, entityName: String, queryJson: ComplexQuery, o: MapToAnyNull? = null): Long {
    val o2: RemoveOptions = if (o != null) JsonHelper.mapper.convertValue(o, jacksonTypeRef()) else RemoveOptions()
    return EntityRwService.removeOne(entityName, queryJson, o2)
  }
  
  fun removeMany(tc: ScriptTraceContext, entityName: String, queryJson: ComplexQuery, o: MapToAnyNull? = null): Long {
    val o2: RemoveOptions = if (o != null) JsonHelper.mapper.convertValue(o, jacksonTypeRef()) else RemoveOptions()
    return EntityRwService.removeMany(entityName, queryJson, o2)
  }
  
  fun count(tc: ScriptTraceContext, entityName: String, queryJson: ComplexQuery): Long =
    EntityRwService.count(entityName, queryJson)
  
  fun findMany(
    tc: ScriptTraceContext,
    entityName: String,
    queryJson: ComplexQuery,
    o: MapToAnyNull? = null,
  ): List<EntityValue> {
    val o2: FindOptions = if (o != null) JsonHelper.mapper.convertValue(o, jacksonTypeRef()) else FindOptions()
    return EntityRwService.findMany(entityName, queryJson, o2)
  }
  
  fun findOne(tc: ScriptTraceContext, entityName: String, queryJson: ComplexQuery, o: MapToAnyNull? = null): EntityValue? {
    val o2: FindOptions = if (o != null) JsonHelper.mapper.convertValue(o, jacksonTypeRef()) else FindOptions()
    return EntityRwService.findOne(entityName, queryJson, o2)
  }
  
  fun findOneById(tc: ScriptTraceContext, entityName: String, id: String, o: MapToAnyNull? = null): EntityValue? {
    val o2: FindOptions = if (o != null) JsonHelper.mapper.convertValue(o, jacksonTypeRef()) else FindOptions()
    return EntityRwService.findOneById(entityName, id, o2)
  }
  
  fun exists(tc: ScriptTraceContext, entityName: String, queryJson: ComplexQuery): Boolean =
    EntityRwService.exists(entityName, queryJson)
  
  fun buildFindOptions(projection: List<String>?, sort: List<String>?, skip: Int?, limit: Int?): MapToAnyNull {
    val o = FindOptions(projection = projection, sort = sort, skip = skip, limit = limit)
    val o2: MapToAnyNull = JsonHelper.mapper.convertValue(o, jacksonTypeRef())
    return o2
  }
  
  fun clearCacheAll() {
    
    EntityRwService.cache.clearAll()
  }
  
  fun clearCacheByEntity(entityName: String) {
    
    EntityRwService.cache.clearAll(entityName)
  }
  
  fun sortByStringField(evList: List<EntityValue>, fn: String) {
    evList.sortedBy { it[fn] as String? }
  }
  
  fun sortByIntField(evList: List<EntityValue>, fn: String) {
    evList.sortedBy { NumHelper.anyToInt(it[fn]) }
  }
  
  fun sortByDoubleField(evList: List<EntityValue>, fn: String) {
    evList.sortedBy { NumHelper.anyToDouble(it[fn]) }
  }
  
  fun max(tc: ScriptTraceContext, entityName: String, fieldName: String, query: ComplexQuery): Double? =
    aggregateQueryBase(entityName, fieldName, AggFun.MAX, query)
  
  fun min(tc: ScriptTraceContext, entityName: String, fieldName: String, query: ComplexQuery): Double? =
    aggregateQueryBase(entityName, fieldName, AggFun.MIN, query)
  
  fun sum(tc: ScriptTraceContext, entityName: String, fieldName: String, query: ComplexQuery): Double? =
    aggregateQueryBase(entityName, fieldName, AggFun.SUM, query)
  
  fun avg(tc: ScriptTraceContext, entityName: String, fieldName: String, query: ComplexQuery): Double? =
    aggregateQueryBase(entityName, fieldName, AggFun.AVG, query)
  
  private fun aggregateQueryBase(
    entityName: String,
    fieldName: String,
    function: AggFun,
    query: ComplexQuery,
  ): Double? {
    val ao = AggregationOptions(
      fields = listOf(
        AdvancedField(
          function = function,
          name = fieldName,
          alias = fieldName,
        ),
      ),
    )
    val entityValues = EntityStatsService.aggregateQuery(entityName, query, ao)
    
    return if (entityValues.isNotEmpty()) {
      NumHelper.anyToDouble(entityValues[0][fieldName])
    } else {
      null
    }
  }
}