package com.seer.trick.base

import com.seer.trick.base.http.WebSocketManager
import com.seer.trick.base.http.WsMsg
import com.seer.trick.base.log.SystemKeyEvent
import com.seer.trick.base.log.SystemKeyEventLevel
import com.seer.trick.base.log.SystemKeyEventService
import com.seer.trick.helper.JsonHelper
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 系统暂停
 */
object SysEmc {

  private val logger = LoggerFactory.getLogger(this::class.java)

  @Volatile
  private var emc: Boolean = false

  private var emcOn: Date? = null

  private val callbacks: MutableList<SysEmcCallback> = CopyOnWriteArrayList()

  fun isSysEmc() = emc

  @Synchronized
  fun enableSysEmc() {
    // 不检查当前已急停，重复操作
    logger.info("系统进入急停状态")
    val oldEmc = emc
    emc = true
    emcOn = Date()
    SystemKeyEventService.record(
      
      SystemKeyEvent(SystemKeyEventLevel.Warning, group = "Base", "系统进入急停状态，之前急停=$oldEmc"),
    )

    callbackAll(true)
  }

  @Synchronized
  fun disableSysEmc() {
    // 不检查当前已急停，重复操作
    logger.info("系统退出急停状态")
    val oldEmc = emc
    emc = false
    emcOn = null
    SystemKeyEventService.record(
      
      SystemKeyEvent(SystemKeyEventLevel.Warning, group = "Base", "系统退出急停状态，之前急停=$oldEmc"),
    )

    callbackAll(false)
  }

  fun addCallback(cb: SysEmcCallback) {
    WebSocketManager.sendAllAsync(WsMsg("SysEmc", JsonHelper.writeValueAsString(mapOf("enabled" to emc))))

    if (callbacks.contains(cb)) {
      logger.error("SysEmc callback already existed!", IllegalArgumentException("$cb"))
      return
    }
    callbacks.add(cb)
  }

  fun removeCallback(cb: SysEmcCallback) {
    callbacks.remove(cb)
  }

  private fun callbackAll(enabled: Boolean) {
    for (cb in callbacks) {
      try {
        cb.invoke(enabled)
      } catch (e: Exception) {
        logger.error("系统急停状态回调异常", e)
      }
    }
  }
}

typealias SysEmcCallback = (enabled: Boolean) -> Unit