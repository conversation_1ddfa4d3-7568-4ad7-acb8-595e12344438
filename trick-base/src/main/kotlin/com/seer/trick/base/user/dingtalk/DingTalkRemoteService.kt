package com.seer.trick.base.user.dingtalk


import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonAutoDetect
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import retrofit2.Call
import retrofit2.http.*

interface DingTalkRemoteService {
  @POST("v1.0/oauth2/userAccessToken")
  fun getToken(
    @Body getTokenBody: GetTokenBody
  ): Call<GetDingTokenRes>

  @GET("v1.0/contact/users/{unionId}")
  fun getUserInfo(
    @Header("x-acs-dingtalk-access-token") token: String,
    @Path("unionId") unionId: String = "me"
  ): Call<GetDingUserInfoRes>

}
data class GetTokenBody(
  var clientId: String = "",
  var clientSecret: String = "",
  var code: String = "",
  var grantType: String = "authorization_code"
)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
data class GetDingTokenRes(
  @JsonAlias("corpId")
  var corpId: String? = null,
  @JsonAlias("refreshToken")
  var refreshToken: String? = null,
  @JsonAlias("accessToken")
  var accessToken: String? = null,
  @JsonAlias("expireIn")
  var expireIn: Int = 0,
  @JsonAlias("code")
  var code: String? = null,
  @JsonAlias("message")
  var message: String? = null,
)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
data class GetDingUserInfoRes(
  @JsonAlias("nick")
  var nick: String? = null,
  @JsonAlias("mobile")
  var mobile: String? = null,
  @JsonAlias("openId")
  var openId: String? = null,
  @JsonAlias("unionId")
  var unionId: String? = null,
)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
data class DingUserInfoData(
  @JsonAlias("userid")
  var userId: String? = null, //用户姓名
  @JsonAlias("unionid")
  var unionId: String? = null, //用户在当前开放应用所属企业的唯一标识。
  @JsonAlias("name")
  var name: String? = null, //用户在应用内的唯一标识
)

