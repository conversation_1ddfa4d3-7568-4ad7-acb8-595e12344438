package com.seer.trick.base.db.sql

import com.seer.trick.base.DbType
import org.slf4j.LoggerFactory
import java.sql.Connection
import java.sql.PreparedStatement
import java.sql.SQLException
import java.sql.Statement
import java.util.function.Function

class SmartConnection(
  val connection: Connection,
  val sqlDialect: DbType
) {
  
  private val logger = LoggerFactory.getLogger(this::class.java)
  
  @Volatile
  private var written = false
  
  fun <T> use(worker: Function<SmartConnection, T>): T {
    return try {
      val r = worker.apply(this)
      tryCommit()
      r
    } finally {
      close()
    }
  }
  
  fun setAutoCommit(ac: Boolean) {
    connection.autoCommit = ac
  }
  
  fun tryCommit() {
    try {
      if (connection.autoCommit) return
      if (!written) return
      connection.commit()
    } catch (e: SQLException) {
      logger.error("Error to commit tx", e)
      try {
        connection.rollback()
      } catch (ex: SQLException) {
        logger.error("Error to rollback tx", e)
      }
      throw e
    }
  }
  
  fun close() {
    connection.close()
  }
  
  fun createStatement(): Statement {
    return connection.createStatement()
  }
  
  fun prepareStatement(sql: String?, forWrite: Boolean): PreparedStatement {
    if (forWrite) written = true
    return connection.prepareStatement(sql)
  }
  
}
