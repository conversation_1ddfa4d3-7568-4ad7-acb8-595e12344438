package com.seer.trick.base.entity.executor.sql

import com.seer.trick.Cq
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.db.DbManager
import com.seer.trick.base.entity.*
import com.seer.trick.base.entity.executor.EntityFindWorkContext
import com.seer.trick.base.entity.service.FindOptions

object EntityFindExecutorSql {

  fun execute(ctx: EntityFindWorkContext) {
    processEntity(ctx, ctx.em.name)
  }

  private fun processEntity(ctx: EntityFindWorkContext, tableName: String) {
    for (fm in ctx.em.fields.values) {
      if (fm.type == FieldType.File || fm.type == FieldType.Image) {
        processFileOrImageField(ctx, fm, tableName)
      } else if (fm.type == FieldType.Reference) {
        processRefEntityField(ctx, fm, tableName)
      } else if (fm.type == FieldType.Component) {
        processComponentField(ctx, fm)
      } else {
        processOtherField(ctx, fm, tableName)
      }
    }
  }

  private fun processFileOrImageField(ctx: EntityFindWorkContext, fm: FieldMeta, mainTableName: String) {
    if (fm.scale == FieldScale.Single) {
      // 路径字段和列同名
      for (ev in ctx.entityValues) {
        val fv: EntityValue = mutableMapOf()
        fv[FieldMeta.FIELD_FILE_PATH] = ev[fm.name]
        fv[FieldMeta.FIELD_FILE_SIZE] = ev[fm.buildFileSizeColumnName()]
        fv[FieldMeta.FIELD_FILE_NAME] = ev[fm.buildFileNameColumnName()]
        fv[FieldMeta.FIELD_FILE_MD5] = ev[fm.buildFileMd5ColumnName()]
        ev[fm.name] = fv
      }
    } else {
      loadRelatedTable(ctx, fm, fm.buildRelatedTableName(mainTableName), null) { myRows ->
        myRows.map { row ->
          val newRow: EntityValue = mutableMapOf()
          newRow[FieldMeta.FIELD_FILE_PATH] = row[FieldMeta.COLUMN_FILE_PATH]
          newRow[FieldMeta.FIELD_FILE_NAME] = row[FieldMeta.COLUMN_FILE_NAME]
          newRow[FieldMeta.FIELD_FILE_SIZE] = row[FieldMeta.COLUMN_FILE_SIZE]
          newRow[FieldMeta.FIELD_FILE_MD5] = row[FieldMeta.COLUMN_FILE_MD5]
          newRow
        }
      }
    }
  }

  private fun processRefEntityField(ctx: EntityFindWorkContext, fm: FieldMeta, mainTableName: String) {
    // 如果是单值，字段和列一一映射
    if (fm.scale == FieldScale.Single) {
      for (ev in ctx.entityValues) {
        ev[fm.name] = ev[fm.name]
      }
    } else {
      loadRelatedTable(ctx, fm, fm.buildRelatedTableName(mainTableName), null) { myRows ->
        myRows.map { row -> row[FieldMeta.COLUMN_REF_ID] }
      }
    }
  }

  private fun processComponentField(ctx: EntityFindWorkContext, fd: FieldMeta) {
    val refComEm = BaseCenter.mustGetRefEntityMeta(fd)
    loadRelatedTable(ctx, fd, refComEm.name, { allRows ->
      val ctx2 = EntityFindWorkContext(refComEm, allRows)
      processEntity(ctx2, refComEm.name)
    }) { myRows -> if (fd.scale == FieldScale.Single) (if (myRows.isEmpty()) null else myRows[0]) else myRows }
  }

  private fun processOtherField(ctx: EntityFindWorkContext, fm: FieldMeta, mainTableName: String) {
    if (fm.scale == FieldScale.Single) {
      for (ev in ctx.entityValues) {
        ev[fm.name] = SqlValueConverter.fromSqlValue(ev[fm.name], fm.type)
      }
    } else {
      loadRelatedTable(ctx, fm, fm.buildRelatedTableName(mainTableName), null) { myRows ->
        myRows.map { row -> SqlValueConverter.fromSqlValue(row[fm.name], fm.type) }
      }
    }
  }

  private fun loadRelatedTable(
    ctx: EntityFindWorkContext, fm: FieldMeta, relatedTableName: String,
    processAllRows: ((List<EntityValue>) -> Unit)?, processMyRows: (List<EntityValue>) -> Any?
  ) {
    if (ctx.ids.isEmpty()) return
    val relatedRows = DbManager.getSqlConnection().use { sc ->
      SqlExecutor.findMany(
        sc, relatedTableName,
        Cq.include(FieldMeta.COLUMN_OWNER, ctx.ids), FindOptions(sort = listOf(FieldMeta.COLUMN_ORDER))
      )
    }
    if (processAllRows != null) processAllRows(relatedRows)
    for (ev in ctx.entityValues) {
      val evId = EntityHelper.mustGetId(ev)
      val myRows = relatedRows.filter { row -> row[FieldMeta.COLUMN_OWNER] == evId }
      ev[fm.name] = processMyRows(myRows)
    }
  }
}