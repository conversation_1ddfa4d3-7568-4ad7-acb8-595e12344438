package com.seer.trick.base.http.handler

import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.operator
import com.seer.trick.base.stats.SimpleStatsValue
import io.javalin.http.Context

object SimpleStatsValueHandler {
  
  fun registerHandlers() {
    val c = Handlers("api")
    c.post("stats/simple-value", ::simpleValue, auth())
  }
  
  private fun simpleValue(ctx: Context) {
    
    val r = SimpleStatsValue.get()
    ctx.json(r)
  }
  
}