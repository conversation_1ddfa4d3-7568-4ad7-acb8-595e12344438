package com.seer.trick.base.entity.executor.sql

import com.seer.trick.base.entity.FieldType
import com.seer.trick.helper.DateHelper
import com.seer.trick.helper.JsonHelper

object SqlValueConverter {

  fun toSqlValue(fv: Any?, fieldType: FieldType): Any? {
    return if (fieldType == FieldType.Boolean) {
      // null 写 0！
      if (true == fv) 1 else 0
    } else if (fv == null) {
      return null
    } else {
      if (fieldType == FieldType.Date) {
        DateHelper.formatDate(DateHelper.anyToDate(fv), "yyyy-MM-dd")
      } else if (fieldType == FieldType.DateTime) {
        DateHelper.formatDate(
          DateHelper.anyToDate(fv), "yyyy-MM-dd HH:mm:ss"
        )
      } else if (fieldType == FieldType.Time) {
        DateHelper.formatDate(DateHelper.anyToDate(fv), "HH:mm:ss")
      } else if (fieldType == FieldType.Object) {
        JsonHelper.writeValueAsString(fv)
      } else {
        fv
      }
    }
  }

  fun fromSqlValue(v: Any?, fieldType: FieldType): Any? {
    if (v == null) return null
    return if (fieldType == FieldType.Boolean) {
      v != 0
    } else if (fieldType == FieldType.Date || fieldType == FieldType.DateTime || fieldType == FieldType.Time) {
      DateHelper.anyToDate(v)
    } else if (fieldType == FieldType.Object) {
      JsonHelper.mapper.readTree(v as String?)
    } else {
      v
    }
  }

}