package com.seer.trick.base.aac

import org.apache.commons.lang3.StringUtils

object LinuxInfoServiceImpl : AbstractServerInfoService() {

  override fun getMotherBoardSerial(): String {
    val output = exec(
      arrayOf(
        "sudo",
        "/bin/bash",
        "-c",
        "dmidecode | grep 'Serial Number' | awk -F ':' '{print $2}' | head -n 1",
      ),
    )
    return StringUtils.deleteWhitespace(output)
  }
}