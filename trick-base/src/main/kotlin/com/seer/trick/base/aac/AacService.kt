package com.seer.trick.base.aac

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.helper.DateHelper
import com.seer.trick.helper.JsonHelper
import org.apache.commons.io.FileUtils
import org.slf4j.LoggerFactory
import java.io.File
import java.nio.charset.StandardCharsets
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.Date

object AacService {

  private val logger = LoggerFactory.getLogger(javaClass)

  const val LICENSE_FILENAME = "m4.license"

  @Volatile
  var license: String = ""

  @Volatile
  var licenseError: String = ""

  @Volatile
  var acpMap: Map<String, Any?> = emptyMap()

  fun checkLicense(licenseFile: File): Boolean {
    val fp = getFp()
    license = fp
    licenseError = checkLicenseError(licenseFile, fp) ?: ""
    return if (licenseError.isBlank()) {
      true
    } else {
      logger.error(
        "$licenseError\n===================================\n$fp\n===================================",
      )
      false
    }
  }

  private fun checkLicenseError(licenseFile: File, fp: String): String? {
    if (!licenseFile.exists()) return "No license. 无许可证文件。"
    val actualLicense = FileUtils.readFileToString(licenseFile, StandardCharsets.UTF_8)
    if (actualLicense.isBlank()) return "Empty license. 许可证为空。"

    val licenseLine = actualLicense.split("\n")
    if (licenseLine.size < 4) return "Bad license. 许可证文件格式错误。(L)"

    if (licenseLine[0] != fp) return "Bad license, footprint not match. 许可证文件格式错误，指纹不匹配。"

    val expectedLicense = AacNative.bl(fp, licenseLine[1], licenseLine[3])
    if (expectedLicense != licenseLine[2]) return "Bad license content. 许可证内容不匹配。(M)"

    val licenseLines = actualLicense.split("\n")
    if (licenseLines.size < 3) return "Bad license content. 许可证内容不匹配。(21)"
    val configLine = licenseLines[3]
    if (configLine.isBlank()) return "Bad license content. 许可证内容不匹配。(22)"

    val acpMap: Map<String, Any?> = try {
      JsonHelper.mapper.readValue(configLine, jacksonTypeRef())
    } catch (e: Exception) {
      return "Bad license content. 许可证内容不匹配。(23)"
    }

    this.acpMap = acpMap

    val expiredOnStr = acpMap["expiredOn"]
    val expiredOn = DateHelper.anyToDate(expiredOnStr)?.toInstant()?.truncatedTo(ChronoUnit.DAYS)
    if (expiredOn != null) {
      val today = Instant.now().truncatedTo(ChronoUnit.DAYS)
      if (expiredOn.isBefore(today)) return "License expired. 许可证已过期。(E)"
    }

    return null
  }

  private fun getFp(): String {
    val serverInfo = getServerInfo()
    val serverInfoString = "${serverInfo.cpu}|${serverInfo.motherBoard}"
    return AacNative.ffp(serverInfoString)
  }

  private fun getServerInfo(): ServerInfo {
    val osName = System.getProperty("os.name").lowercase()
    return when {
      osName.contains("win") -> {
        WindowsInfoServiceImpl
      }

      osName.contains("linux") -> {
        LinuxInfoServiceImpl
      }

      osName.contains("mac") -> {
        MacInfoServiceImpl
      }

      else -> throw RuntimeException("Unsupported OS $osName")
    }.getServerInfo()
  }
}

data class Acp(
  val expiredOn: Date?, // 过期时间
  val modules: Set<String>?, // 启用的模块 Falcon, Store, Fleet, Go
  val maxUsers: Int?, // 最大用户数
  // -- fleet
  val maxScenes: Int?, // 最多支持多少个场景
  val maxRobots: Int?, // 最多支持多少个机器人
)