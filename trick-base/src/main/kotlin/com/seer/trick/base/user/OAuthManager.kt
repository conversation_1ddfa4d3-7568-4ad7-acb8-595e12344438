package com.seer.trick.base.user

import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.user.dingtalk.DingTalkConfig
import com.seer.trick.base.user.dingtalk.DingTalkOAuthManager
import com.seer.trick.base.user.feishu.FeiShuConfig
import com.seer.trick.base.user.feishu.FeiShuOAuthManager
import com.seer.trick.base.user.wwx.WwxConfig
import com.seer.trick.base.user.wwx.WwxOAuthManager
import com.seer.trick.helper.IdHelper
import org.slf4j.Logger
import retrofit2.Response
import java.util.*

abstract class OAuthManager<T : OAuthConfig> {
  abstract var platformKey: String
  abstract var logger: Logger
  abstract fun readConfig(): T

  open fun getAccessToken(code: String): String {
    val response = fetchAccessToken(code)
    return response.accessToken
  }

  fun signIn(code: String): HumanUserSession {
    val accessToken = getAccessToken(code)
    val userInfo = fetchUserInfo(accessToken, code)
    return generateUserIfNotExisted(userInfo)
  }

  abstract fun fetchAccessToken(code: String): AccessToken

  abstract fun fetchUserInfo(accessToken: String, code: String): UserInfo

  private fun generateUserIfNotExisted(userInfo: UserInfo): HumanUserSession {
    val existingUser = EntityRwService.findOne("HumanUser", Cq.eq("externalUserId", userInfo.userId))
    return existingUser?.let {
      UserService.signInSuccessfully(it)
    } ?: createNewUserEntity(userInfo).let {
      UserService.signInSuccessfully(it)
    }
  }

  private fun createNewUserEntity(userInfo: UserInfo): EntityValue {
    val newEv: EntityValue = mutableMapOf(
      "id" to IdHelper.oidStr(),
      "username" to userInfo.userName,
      "createdOn" to Date(),
      "externalAdded" to true,
      "externalSource" to platformKey,
      "externalUserId" to userInfo.userId,
      "directSignInDisabled" to true
    )
    EntityRwService.createOne("HumanUser", newEv)
    return newEv
  }


  //解决某些情况下，请求失败，body 中没有响应体，错误的响应体存储在 errorBody 中
  fun <T> Response<T>.handleApiResponse(): T? = if (isSuccessful) {
    body()
  } else {
    errorBody()?.string()?.let {
      logger.error("API call failed: $it")
    }
    null
  }

   fun readConfigs(): Configs {
    val wwxConfig = WwxOAuthManager.readConfig()
    val feiShuConfig = FeiShuOAuthManager.readConfig()
    val dingConfig = DingTalkOAuthManager.readConfig()
    return Configs(wwxConfig, feiShuConfig, dingConfig)
  }

  data class AccessToken(
    val accessToken: String,
    val expiresIn: Int = 0
  )

  data class UserInfo(
    val userId: String,
    val userName: String
  )
  data class Configs(
    val wwxConfig: WwxConfig,
    val feiShuConfig: FeiShuConfig,
    val dingConfig: DingTalkConfig
  )
}

interface OAuthConfig
