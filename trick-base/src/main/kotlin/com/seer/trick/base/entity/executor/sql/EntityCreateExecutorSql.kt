package com.seer.trick.base.entity.executor.sql

import com.seer.trick.base.BaseCenter
import com.seer.trick.base.db.DbManager
import com.seer.trick.base.entity.*
import com.seer.trick.base.entity.executor.EntityCreateWorkContext
import com.seer.trick.helper.IdHelper
import org.apache.commons.lang3.StringUtils

// 一个实体可能对应多张表的多个行
object EntityCreateExecutorSql {

  // onlyRelatedTable: 只插入关联表，不插入自己
  fun execute(
    ctx: EntityCreateWorkContext, entityMeta: EntityMeta, entityValues: List<EntityValue>,
    onlyRelatedTable: Boolean = false
  ) {
    processEntity(ctx, entityMeta, entityValues, entityMeta.name)
    if (onlyRelatedTable) ctx.tables.remove(entityMeta.name)
    DbManager.getSqlConnection().use { sc ->
      for (tableName in ctx.tables.keys) {
        val rows = ctx.tables[tableName]
        if (rows.isNullOrEmpty()) continue
        for (row in rows) {
          // 确保一定有 id
          if (!row.containsKey("id")) row["id"] = IdHelper.oidStr()
        }
        SqlExecutor.insert(sc, tableName, rows)
      }
      null
    }
  }

  private fun processEntity(
    ctx: EntityCreateWorkContext, em: EntityMeta, entityValues: List<EntityValue>, mainTable: String
  ) {
    for (entityValue in entityValues) {
      val id = EntityHelper.mustGetId(entityValue)
      val mainRow: EntityValue = mutableMapOf()
      for (fm in em.fields.values) {
        if (!entityValue.containsKey(fm.name)) {
          if (fm.type == FieldType.Boolean && fm.scale == FieldScale.Single) {
            entityValue[fm.name] = false // 单值布尔强制给 false，只插入这样
          } else {
            continue
          }
        }
        if (fm.type == FieldType.File || fm.type == FieldType.Image) {
          processFileOrImageField(ctx, fm, entityValue, mainRow, mainTable, id)
        } else if (fm.type == FieldType.Reference) {
          processRefEntity(ctx, fm, entityValue, mainRow, mainTable, id)
        } else if (fm.type == FieldType.Component) {
          processComponentField(ctx, fm, entityValue[fm.name], id)
        } else {
          processOtherField(ctx, fm, entityValue[fm.name], mainRow, mainTable, id)
        }
      }
      if (em.type == EntityType.Component) {
        mainRow["id"] = entityValue["id"]
        mainRow[FieldMeta.COLUMN_OWNER] = entityValue[FieldMeta.COLUMN_OWNER]
        mainRow[FieldMeta.COLUMN_ORDER] = entityValue[FieldMeta.COLUMN_ORDER]
      }
      ctx.addRow(mainTable, mainRow)
    }
  }

  @Suppress("UNCHECKED_CAST")
  private fun processFileOrImageField(
    ctx: EntityCreateWorkContext, fd: FieldMeta, entityValue: EntityValue,
    mainRow: EntityValue, mainTable: String, owner: String?
  ) {
    if (fd.scale == FieldScale.Single) {
      val fv = entityValue[fd.name] as EntityValue? ?: return
      mainRow[fd.name] = fv[FieldMeta.FIELD_FILE_PATH]
      mainRow[fd.buildFileSizeColumnName()] = fv[FieldMeta.FIELD_FILE_SIZE]
      mainRow[fd.buildFileNameColumnName()] = fv[FieldMeta.FIELD_FILE_NAME]
      mainRow[fd.buildFileMd5ColumnName()] = fv[FieldMeta.FIELD_FILE_MD5]
    } else {
      val fvList = entityValue[fd.name] as List<EntityValue>? ?: return
      val relatedTableName = fd.buildRelatedTableName(mainTable)
      for (i in fvList.indices) {
        val fv = fvList[i]
        val fileRow: EntityValue = mutableMapOf()
        fileRow[FieldMeta.COLUMN_OWNER] = owner
        fileRow[FieldMeta.COLUMN_ORDER] = i + 1
        fileRow[FieldMeta.COLUMN_FILE_PATH] = fv[FieldMeta.FIELD_FILE_PATH]
        fileRow[FieldMeta.COLUMN_FILE_NAME] = fv[FieldMeta.FIELD_FILE_NAME]
        fileRow[FieldMeta.COLUMN_FILE_SIZE] = fv[FieldMeta.FIELD_FILE_SIZE]
        fileRow[FieldMeta.COLUMN_FILE_MD5] = fv[FieldMeta.FIELD_FILE_MD5]
        ctx.addRow(relatedTableName, fileRow)
      }
    }
  }

  @Suppress("UNCHECKED_CAST")
  private fun processRefEntity(
    ctx: EntityCreateWorkContext, fd: FieldMeta, entityValue: EntityValue,
    mainRow: EntityValue, mainTable: String, owner: String?
  ) {
    if (fd.scale == FieldScale.Single) {
      mainRow[fd.name] = entityValue[fd.name]
    } else {
      val fvList = entityValue[fd.name] as List<String>? ?: return
      val relatedTableName = fd.buildRelatedTableName(mainTable)
      for (i in fvList.indices) {
        val fv = fvList[i]
        val fileRow: EntityValue = mutableMapOf()
        fileRow[FieldMeta.COLUMN_OWNER] = owner
        fileRow[FieldMeta.COLUMN_ORDER] = i + 1
        fileRow[FieldMeta.COLUMN_REF_ID] = fv
        ctx.addRow(relatedTableName, fileRow)
      }
    }
  }

  @Suppress("UNCHECKED_CAST")
  private fun processComponentField(ctx: EntityCreateWorkContext, fd: FieldMeta, fv: Any?, owner: String?) {
    if (fv == null) return
    val refComEm = BaseCenter.mustGetRefEntityMeta(fd)
    val fvList: List<EntityValue> = if (fd.scale == FieldScale.Single) {
      listOf(fv as EntityValue)
    } else {
      fv as List<EntityValue>
    }
    for (i in fvList.indices) {
      val v = fvList[i]
      var comId = v["id"] as String?
      if (StringUtils.isBlank(comId)) comId = IdHelper.oidStr()
      v["id"] = comId
      v[FieldMeta.COLUMN_OWNER] = owner
      v[FieldMeta.COLUMN_ORDER] = i + 1
      v[FieldMeta.FIELD_VERSION] = 0
    }
    processEntity(ctx, refComEm, fvList, refComEm.name) // 用子业务对象名！
  }

  private fun processOtherField(
    ctx: EntityCreateWorkContext, fd: FieldMeta, fv: Any?,
    mainRow: EntityValue, mainTable: String, owner: String?
  ) {
    if (fd.scale == FieldScale.Single) {
      mainRow[fd.name] = SqlValueConverter.toSqlValue(fv, fd.type)
    } else {
      if (fv == null) return
      val fvList = fv as List<*>
      val sqlValues = fvList.map { SqlValueConverter.toSqlValue(it, fd.type) }
      val relatedTableName = fd.buildRelatedTableName(mainTable)
      for (i in sqlValues.indices) {
        val v = sqlValues[i]
        val row: EntityValue = mutableMapOf()
        row[FieldMeta.COLUMN_OWNER] = owner
        row[FieldMeta.COLUMN_ORDER] = i + 1
        row[fd.name] = v
        ctx.addRow(relatedTableName, row)
      }
    }
  }
}
