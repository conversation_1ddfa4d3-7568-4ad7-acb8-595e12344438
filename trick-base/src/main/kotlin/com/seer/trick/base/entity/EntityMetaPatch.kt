/**
 * 不通过数组位置管理前后顺序。如果要维护顺序，通过 displayOrder 字段。
 */
package com.seer.trick.base.entity

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.helper.JsonHelper
import kotlin.reflect.KClass
import kotlin.reflect.KProperty1
import kotlin.reflect.full.declaredMemberProperties

/**
 * 比较业务对象的差异，产生和应用修改记录。
 */
object EntityMetaPatch {
  
  private val entityMetaNotSimpleFields =
    mutableSetOf(EntityMeta::fields, EntityMeta::indexes, EntityMeta::pagesButtons)
  
  private val fieldMetaNotSimpleFields = mutableSetOf(FieldMeta::view)
  
  fun diff(oldEm: EntityMeta, newEm: EntityMeta): EntityMetaDiff? {
    // 分三类：简单字段、整个比较的对象字段、详细比较
    
    // 整个比较，依赖 data class 的 equals，但可能
    // 1. 出现 String? "" != null 的情况
    // 2. 整个对象是 null vs 默认空对象的情况
    // 整个比较危险 val orderConfig: OrderConfig? = null,
    
    // 处理简单字段和整个比较字段
    val simplePropertyChanges = diffSimple(oldEm, newEm, EntityMeta::class, entityMetaNotSimpleFields)
    
    val newFields: MutableList<FieldMeta> = ArrayList()
    val removedFields: MutableList<String> = ArrayList()
    val changedFields: MutableList<FieldMetaChange> = ArrayList()
    
    val newIndexes: MutableList<EntityIndexDef> = ArrayList()
    val removedIndexes: MutableList<String> = ArrayList()
    val changedIndexes: MutableList<EntityIndexChange> = ArrayList()
    
    val pageButtonsChanges: MutableList<PageButtonsChange> = ArrayList()
    
    // 处理 pagesButtons
    if (oldEm.pagesButtons != newEm.pagesButtons) {
      val pageKeys = HashSet(oldEm.pagesButtons.keys + newEm.pagesButtons.keys)
      for (pageKey in pageKeys) {
        val oldValue = oldEm.pagesButtons[pageKey]
        val newValue = newEm.pagesButtons[pageKey]
        if (oldValue != newValue) {
          pageButtonsChanges += PageButtonsChange(pageKey, oldValue, newValue)
        }
      }
    }
    
    // 处理 fields
    if (oldEm.fields != newEm.fields) {
      val fieldNames = HashSet(oldEm.fields.keys + newEm.fields.keys)
      for (fieldName in fieldNames) {
        val oldFm = oldEm.fields[fieldName]
        val newFm = newEm.fields[fieldName]
        if (oldFm == null && newFm != null) {
          newFields += newFm
        } else if (oldFm != null && newFm == null) {
          removedFields += fieldName
        } else if (oldFm != null && newFm != null) {
          val c = diff(oldFm, newFm)
          if (c != null) changedFields += c
        }
      }
    }
    
    // 处理 indexes
    if (oldEm.indexes != newEm.indexes) {
      for (oldIndex in oldEm.indexes) {
        val newIndex = newEm.indexes.find { it.hasSameFields(oldIndex) }
        if (newIndex == null) removedIndexes += oldIndex.name
        else if (newIndex != oldIndex) changedIndexes += EntityIndexChange(oldIndex.name, oldIndex, newIndex)
      }
      for (newIndex in newEm.indexes) {
        val oldIndex = oldEm.indexes.find { it.hasSameFields(newIndex) }
        if (oldIndex == null) newIndexes += newIndex
      }
    }
    
    if (simplePropertyChanges.isEmpty()
      && newFields.isEmpty() && removedFields.isEmpty() && changedFields.isEmpty()
      && newIndexes.isEmpty() && removedIndexes.isEmpty() && changedIndexes.isEmpty()
      && pageButtonsChanges.isEmpty()
    ) return null
    
    return EntityMetaDiff(
      oldEm.name, changed = EntityMetaChange(
        simplePropertyChanges,
        newFields,
        removedFields,
        changedFields,
        newIndexes,
        removedIndexes,
        changedIndexes,
        pageButtonsChanges
      )
    )
  }
  
  // 比较两个字段
  private fun diff(oldFm: FieldMeta, newFm: FieldMeta): FieldMetaChange? {
    if (oldFm == newFm) return null
    
    // 处理简单字段和整个比较字段
    val simplePropertyChanges = diffSimple(oldFm, newFm, FieldMeta::class, fieldMetaNotSimpleFields)
    val viewChanges = if (oldFm.view != newFm.view) {
      diffSimple(oldFm.view, newFm.view, FieldView::class, emptySet())
    } else {
      emptyList()
    }
    if (simplePropertyChanges.isEmpty() && viewChanges.isEmpty()) return null
    
    return FieldMetaChange(oldFm.name, simplePropertyChanges, viewChanges)
  }
  
  // 处理简单字段和整个比较字段
  private fun <T : Any> diffSimple(
    oldOne: T, newOne: T, kClass: KClass<T>, skipFields: Set<KProperty1<T, Any?>>
  ): List<SimplePropertyChange> {
    val simplePropertyChanges: MutableList<SimplePropertyChange> = ArrayList()
    for (p in kClass.declaredMemberProperties) {
      if (p in skipFields) continue
      
      val pt = p.returnType.classifier
      val equals = if (pt == String::class) {
        stringEquals(p.get(oldOne) as String?, p.get(newOne) as String?)
      } else if (pt == Boolean::class) {
        booleanEquals(p.get(oldOne) as Boolean?, p.get(newOne) as Boolean?)
      } else {
        p.get(oldOne) == p.get(newOne)
      }
      if (!equals) {
        simplePropertyChanges += SimplePropertyChange(p.name, p.get(oldOne), p.get(newOne))
      }
    }
    return simplePropertyChanges
  }
  
  // blank 等效
  private fun stringEquals(v1: String?, v2: String?): Boolean {
    val v1f = if (v1.isNullOrBlank()) null else v1
    val v2f = if (v2.isNullOrBlank()) null else v2
    return v1f == v2f
  }
  
  // null/false 等效
  private fun booleanEquals(v1: Boolean?, v2: Boolean?): Boolean {
    val v1f = v1 ?: false
    val v2f = v2 ?: false
    return v1f == v2f
  }
  
  /**
   * 对内建业务对象 em 施加 diff
   */
  fun apply(builtinEm: EntityMeta, diff: EntityMetaDiff?): EntityMeta? {
    if (diff == null) return builtinEm
    if (diff.removedEm != null) return null
    val c = diff.changed ?: return builtinEm
    
    // 先处理复杂的
    val em = builtinEm
    if (c.newFields.isNotEmpty()) {
      for (fm in c.newFields) em.fields[fm.name] = fm
    }
    if (c.removedFields.isNotEmpty()) {
      for (fn in c.removedFields) em.fields.remove(fn)
    }
    if (c.changedFields.isNotEmpty()) {
      for (fmc in c.changedFields) {
        apply(fmc, em.fields)
      }
    }
    
    if (c.newIndexes.isNotEmpty()) {
      for (idx in c.newIndexes) em.indexes += idx
    }
    if (c.removedIndexes.isNotEmpty()) {
      for (idxName in c.removedIndexes) em.indexes.removeIf { it.name == idxName }
    }
    if (c.changedIndexes.isNotEmpty()) {
      for (idxCh in c.changedIndexes) {
        apply(idxCh, em.indexes)
      }
    }
    
    if (c.pageButtonsChanges.isNotEmpty()) {
      for (pc in c.pageButtonsChanges) {
        if (pc.newValue == null) em.pagesButtons.remove(pc.pageKey)
        else em.pagesButtons[pc.pageKey] = pc.newValue
      }
    }
    
    val emMap: MutableMap<String, Any?> = JsonHelper.mapper.convertValue(em, jacksonTypeRef())
    if (c.simplePropertyChanges.isNotEmpty()) {
      for (p in c.simplePropertyChanges) {
        emMap[p.name] = p.newValue
      }
    }
    
    return JsonHelper.mapper.convertValue(emMap, jacksonTypeRef())
  }
  
  private fun apply(fmc: FieldMetaChange, fieldMap: MutableMap<String, FieldMeta>) {
    var fm = fieldMap[fmc.fieldName] ?: return
    
    if (fmc.viewChanges.isNotEmpty()) {
      val viewMap: MutableMap<String, Any?> = JsonHelper.mapper.convertValue(fm.view, jacksonTypeRef())
      for (cc in fmc.viewChanges) viewMap[cc.name] = cc.newValue
      fm.view = JsonHelper.mapper.convertValue(viewMap, jacksonTypeRef())
    }
    
    if (fmc.simplePropertyChanges.isNotEmpty()) {
      val fmMap: MutableMap<String, Any?> = JsonHelper.mapper.convertValue(fm, jacksonTypeRef())
      for (cc in fmc.simplePropertyChanges) fmMap[cc.name] = cc.newValue
      fm = JsonHelper.mapper.convertValue(fmMap, jacksonTypeRef())
    }
    
    fieldMap[fm.name] = fm
  }
  
  private fun apply(idxCh: EntityIndexChange, indexes: MutableList<EntityIndexDef>) {
    val idx = indexes.indexOfFirst { it.name == idxCh.name }
    if (idx < 0) return
    indexes[idx] = idxCh.newValue
  }
  
}

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class EntityMetaDiff(
  val entityName: String,
  val addedEm: EntityMeta? = null,
  val removedEm: String? = null,
  val changed: EntityMetaChange? = null,
)

data class EntityMetaChange(
  val simplePropertyChanges: List<SimplePropertyChange>,
  //
  val newFields: List<FieldMeta>,
  val removedFields: List<String>,
  val changedFields: List<FieldMetaChange>,
  //
  val newIndexes: List<EntityIndexDef>,
  val removedIndexes: List<String>,
  val changedIndexes: List<EntityIndexChange>,
  //
  val pageButtonsChanges: List<PageButtonsChange>,
)

data class SimplePropertyChange(
  val name: String,
  val oldValue: Any?,
  val newValue: Any?,
)

data class FieldMetaChange(
  val fieldName: String,
  val simplePropertyChanges: List<SimplePropertyChange>,
  val viewChanges: List<SimplePropertyChange>,
)

data class EntityIndexChange(
  val name: String,
  val oldValue: EntityIndexDef,
  val newValue: EntityIndexDef,
)

data class PageButtonsChange(
  val pageKey: String,
  val oldValue: PageButtons?,
  val newValue: PageButtons?,
)