package com.seer.trick.base.script

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.base.http.client.*
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.XmlHelper
import org.slf4j.LoggerFactory

object ScriptHttpClient {
  
  private val logger = LoggerFactory.getLogger(this::class.java)
  
  fun request(tc: ScriptTraceContext, reqStr: String): HttpResult {
    val req: HttpRequest = JsonHelper.mapper.readValue(reqStr, jacksonTypeRef())
    val res = HttpClientBase.request(req)
    return res
  }
  
  fun syncCall(tc: ScriptTraceContext, reqStr: String, okChecker: HttpResultOkChecker?, oStr: String?): HttpResult {
    val req: HttpRequest = JsonHelper.mapper.readValue(reqStr, jacksonTypeRef())
    val o: CallRetryOptions? = if (oStr.isNullOrBlank()) null else JsonHelper.mapper.readValue(oStr, jacksonTypeRef())
    return SyncHttpCallService.call(req, okChecker, o)
  }
  
  fun asyncCallback(tc: ScriptTraceContext, reqStr: String, okChecker: String?, oStr: String?): String {
    val req: HttpRequest = JsonHelper.mapper.readValue(reqStr, jacksonTypeRef())
    val o: CallRetryOptions? = if (oStr.isNullOrBlank()) null else JsonHelper.mapper.readValue(oStr, jacksonTypeRef())
    return AsyncHttpCallService.submit(req, okChecker, o)
  }
  
  // 已有
  @Suppress("MemberVisibilityCanBePrivate")
  fun requestJson(method: String, url: String, reqBody: Any?, headers: Map<String, String>?): HttpResult {
    val req = HttpRequest(
      url = url,
      contentType = HttpContentType.Json,
      method = HttpClientBase.strToHttpMethod(method),
      reqBody = JsonHelper.writeValueAsString(reqBody),
      headers = headers
    )
    return HttpClientBase.request(req)
  }
  
  /**
   * 反复轮训直到成功。使用此方法的好处：不用自己写循环。优化日志，开始结束时打印日志。中间如果一致是相同结果，不重复打印日志。
   * purpose 是目的，写好了方便查错。
   * 后面会与 SOC 结合。
   */
  fun requestJsonUntil(
    tc: ScriptTraceContext,
    method: String, url: String, reqBody: Any?, headers: Map<String, String>?, purpose: String,
    delay: Long, check: (HttpResult) -> Boolean,
  ): HttpResult {
    logger.info("HTTP 请求。目的=$purpose")
    var first = true
    while (true) {
      val r = requestJson(method, url, reqBody, headers)
      if (check(r)) {
        logger.info("HTTP 请求通过。目的=$purpose 响应码=${r.code} 响应正文=${r.bodyString}")
        return r
      }
      if (first) {
        logger.debug("HTTP 请求未通过。目的=$purpose 响应码=${r.code} 响应正文=${r.bodyString}")
      }
      first = false
      Thread.sleep(delay)
    }
  }
  
  @Deprecated(
    "弃用", ReplaceWith(
      "requestXml(method, url, reqBody, headers, auth)",
      "com.seer.trick.base.script.ScriptHttpClient.requestXml"
    )
  )
  fun requestWebService(
    method: String, url: String, reqBody: Any?, headers: Map<String, String>?, auth: Map<String, String>?
  ): HttpResult {
    return requestXml(method, url, reqBody, headers, auth)
  }
  
  fun requestXml(
    method: String, url: String, reqBody: Any?, headers: Map<String, String>?, auth: Map<String, String>?
  ): HttpResult {
    val req = HttpRequest(
      url = url,
      contentType = HttpContentType.Xml,
      method = HttpClientBase.strToHttpMethod(method),
      reqBody = XmlHelper.writeValueAsString(reqBody),
      headers = headers,
      basicAuth = auth,
    )
    return HttpClientBase.request(req)
  }
  
}