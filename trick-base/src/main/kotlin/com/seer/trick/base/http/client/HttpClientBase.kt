package com.seer.trick.base.http.client

import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.http.client.HttpMethod.*
import okhttp3.OkHttpClient
import org.slf4j.LoggerFactory
import java.io.IOException
import java.util.*

/**
 * 基础封装。方便调用。追踪调用。
 */
object HttpClientBase {

  private val logger = LoggerFactory.getLogger(this::class.java)

  // val interceptor = HttpLoggingInterceptor { msg -> logger.debug(msg) }
  // interceptor.level = HttpLoggingInterceptor.Level.BODY
  private val httpClient = OkHttpClient.Builder()
    // .addInterceptor(interceptor)
    .build()

  /**
   * 封装请求响应对象，方便调用。
   * TODO 此方法应该保证只抛中断异常，不抛其他异常
   */
  fun request(req: HttpRequest): HttpResult {
    val request = req.toOkRequest()

    val traceId = if (req.trace) startTrace(req) else null

    val res = try {
      val response = httpClient.newCall(request).execute()

      val resBody = response.body?.string()

      HttpResult(response.isSuccessful, false, null, response.code, resBody, null, null)
    } catch (e: IOException) {
      HttpResult(successful = false, ioError = true, ioErrorMsg = e.message, code = 0, bodyString = null, null, null)
    }

    if (!traceId.isNullOrBlank()) endTrace(traceId, res)

    return res
  }

  // 不抛错
  private fun startTrace(req: HttpRequest): String? {
    try {
      val trace: EntityValue = mutableMapOf(
        "url" to req.url,
        "method" to req.method.name,
        "reqBody" to if (req.traceReqBody) req.reqBody else null,
        "reqOn" to req.reqOn,
      )
      return EntityRwService.createOne("ExternalCallTrace", trace) // TODO rename
    } catch (e: InterruptedException) {
      throw e
    } catch (e: Exception) {
      logger.error("startTrace", e)
      return null
    }
  }

  private fun endTrace(id: String, res: HttpResult) {
    try {
      val update: EntityValue = mutableMapOf(
        "ioError" to res.ioError,
        "ioErrorMsg" to res.ioErrorMsg,
        "resBody" to res.bodyString,
        "resCode" to res.code,
        "resOn" to Date()
      )
      EntityRwService.updateOne("ExternalCallTrace", Cq.idEq(id), update) // TODO rename
    } catch (e: InterruptedException) {
      throw e
    } catch (e: Exception) {
      logger.error("startTrace", e)
    }
  }

  fun strToHttpMethod(str: String): HttpMethod {
    val lStr = str.lowercase()
    return when (lStr) {
      "get" -> Get
      "post" -> Post
      "put" -> Put
      "delete" -> Delete
      else -> throw BzError("errBadHttpMethod", str)
    }
  }

}