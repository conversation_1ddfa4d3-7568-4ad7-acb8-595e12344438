package com.seer.trick.base.stats.manila

import com.seer.trick.BzError
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.stats.manila.domain.ManilaFilterCase
import com.seer.trick.helper.JsonFileHelper
import java.io.File
import java.util.concurrent.ConcurrentHashMap

/**
 * Manila 统计方案
 *
 * 在内存里存一份，增删改查时，先走内存后持久化到文件
 */
object ManilaFilterCaseService {

  private val caseMap = ConcurrentHashMap<String, ManilaFilterCase>()

  fun init() {
    val dir = BaseCenter.baseConfig.configDir
    val filterCaseFile = File(dir, "time-stats.json")
    if (filterCaseFile.exists()) {
      val list = JsonFileHelper.readJsonFromFile<List<ManilaFilterCase>>(filterCaseFile)
      list?.forEach { caseMap[it.id] = it }
    }
  }

  fun dispose() {
    caseMap.clear()
  }

  fun listCases(): List<ManilaFilterCase> = caseMap.values.toList()

  @Synchronized
  fun saveCases(filterCases: List<ManilaFilterCase>) {
    // 校验同名方案
    val nameToIdMap = caseMap.values.associate { it.name to it.id } // filter case name -> filter case id
    for (filterCase in filterCases) {
      val exist = nameToIdMap[filterCase.name]
      if (exist != null && exist != filterCase.id) {
        throw BzError("errDuplicateManilaFilterCaseName", filterCase.name)
      }
    }

    // 持久化方案
    for (filterCase in filterCases) {
      caseMap[filterCase.id] = filterCase
    }
    persist()
  }

  @Synchronized
  fun removeCases(ids: List<String>) {
    for (id in ids) {
      caseMap.remove(id)
    }
    persist()
  }

  // 保证持久化的文件是最新的即可，中间顺序颠倒无影响
  private fun persist() {
    val dir = BaseCenter.baseConfig.configDir
    val filterCaseFile = File(dir, "time-stats.json")
    JsonFileHelper.writeJsonToFile(filterCaseFile, caseMap.values.toList(), true)
  }
}