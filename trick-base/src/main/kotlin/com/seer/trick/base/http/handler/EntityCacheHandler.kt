package com.seer.trick.base.http.handler

import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import io.javalin.http.Context

object EntityCacheHandler {
  
  fun registerHandlers() {
    val c = Handlers("api/entity-cache")
    c.post("clear-all", ::clearAll, auth())
    c.post("clear-entity/{entityName}", ::clearEntityAll, auth())
    c.get("inspect", ::inspect, auth())
  }
  
  private fun clearAll(ctx: Context) {
    EntityRwService.cache.clearAll()
    ctx.status(200)
  }
  
  private fun clearEntityAll(ctx: Context) {
    val entityName = ctx.pathParam("entityName")
    EntityRwService.cache.clearAll(entityName)
  }
  
  private fun inspect(ctx: Context) {
    val r = EntityRwService.cache.inspect() ?: throw UnsupportedOperationException()
    ctx.json(r)
  }
  
}
