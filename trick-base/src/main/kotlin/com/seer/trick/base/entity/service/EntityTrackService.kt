package com.seer.trick.base.entity.service

import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.change.EntityChange
import java.util.*

object EntityTrackService {
  
  fun trackUpdate(em: EntityMeta, changes: List<EntityChange>) {
    if (!em.trackChange) return
    
    val records: MutableList<EntityValue> = ArrayList()
    
    for (change in changes) {
      val fields: MutableList<String> = ArrayList()
      for (fieldName in em.fields.keys) {
        if (notTrackChangeField(fieldName)) continue
        val f1 = change.oldValue?.get(fieldName)
        val f2 = change.newValue?.get(fieldName)
        if (!Objects.equals(f1, f2)) fields += fieldName
      }
      
      if (fields.isEmpty()) continue
      
      records += mutableMapOf(
        "entityName" to em.name,
        "changeType" to "Update",
        "entityId" to change.id,
        "entityFields" to fields.joinToString(", ")
      )
    }
    
    if (records.isNotEmpty()) EntityRwService.createMany("EntityChangedRecord", records)
  }
  
  private fun notTrackChangeField(fieldName: String): Boolean {
    return fieldName == FieldMeta.FIELD_VERSION ||
        fieldName == FieldMeta.FIELD_CREATED_ON || fieldName == FieldMeta.FIELD_CREATED_BY
        || fieldName == FieldMeta.FIELD_MODIFIED_ON || fieldName == FieldMeta.FIELD_MODIFIED_BY
  }
  
  
}