package com.seer.trick.base.db.sql

import com.seer.trick.base.DbType
import com.seer.trick.base.db.DbManager
import com.seer.trick.base.entity.*
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import java.sql.ResultSet
import java.util.concurrent.ConcurrentHashMap

/**
 * 让表结构与实体结构一致。
 * 不会删除表、列：删除表、列需谨慎，不要自动。
 * 修改表结构不需要事务，自动提交。
 *
 *
 * Derby 表名列名要保持大小写，要放在双引号内。注意像主键定义等各种地方都需要。
 */
object SqlSchemaManager {

  private val logger = LoggerFactory.getLogger(this::class.java)

  val tableDefMap: MutableMap<String, TableDef> = ConcurrentHashMap()

  @Synchronized
  fun sync(entityMetaMap: Map<String, EntityMeta>) {
    DbManager.getSqlConnection().use { sc: SmartConnection ->
      sc.setAutoCommit(true)

      val dbDm = DatabaseMetaHelper.parse(sc, DbManager.getSqlConnection().connection.metaData)

      tableDefMap.clear()
      toTableDef(entityMetaMap, sc.sqlDialect)
      fix(sc.sqlDialect)

      for (table in tableDefMap.values) {
        analyseTable(sc, table, dbDm)
      }
    }

    logger.info("SQL schema updated")
  }

  private fun analyseTable(sc: SmartConnection, tableDef: TableDef, dbDm: Map<String, TableDm>) {
    val tableDm = dbDm[tableDef.name.lowercase()] // 索引用小写！
    if (tableDm == null) {
      // 新建表
      createTable(sc, tableDef)
    } else {
      // 修改表（如果需要）
      alterTable(sc, tableDef, tableDm)
    }
  }

  fun createTable(sc: SmartConnection, tableDef: TableDef) {
    logger.info("添加表 {}", tableDef.name)
    val columnDefs: MutableList<String?> = ArrayList()
    for (cd in tableDef.columns.values) {
      SqlHelper.checkColumnNameValid(cd.name)
      columnDefs.add(toColumnDll(cd, tableDef.dialect))
    }
    val eTableName = SqlHelper.escapeIdentifier(tableDef.name, sc.sqlDialect)
    val columns = StringUtils.join(columnDefs, ", ")
    val primaryKey = if (tableDef.columns.containsKey("id")) {
      if (sc.sqlDialect == DbType.Derby) {
        ", PRIMARY KEY (\"id\")"
      } else {
        ", CONSTRAINT PK_${tableDef.name} PRIMARY KEY (\"id\")"
      }
    } else {
      ""
    }
    val sql = "CREATE TABLE $eTableName ($columns$primaryKey)"
    logger.debug("添加表 SQL : {}", sql)
    SqlHelper.executeSql(sc, sql)
  }

  private fun alterTable(sc: SmartConnection, tableDef: TableDef, tableDm: TableDm) {
    for (colDef in tableDef.columns.values) {
      try {
        val columnDm = tableDm.columns[colDef.name]
        if (columnDm == null) {
          addColumn(sc, tableDef, colDef)
        } else {
          alterColumn(sc, tableDef, colDef, columnDm)
        }
      } catch (e: Exception) {
        logger.error("维护列报错：" + e.message)
      }
    }
  }

  private fun addColumn(sc: SmartConnection, tableDef: TableDef, cd: ColumnDef) {
    logger.info("添加列 {}:{}", tableDef.name, cd.name)
    val eTableName = SqlHelper.escapeIdentifier(tableDef.name, sc.sqlDialect)
    val sql = String.format("ALTER TABLE %s ADD %s", eTableName, toColumnDll(cd, tableDef.dialect))
    logger.debug("添加列 SQL {}", sql)
    SqlHelper.executeSql(sc, sql)
  }

  private fun alterColumn(sc: SmartConnection, tableDef: TableDef, cd: ColumnDef, columnDm: ColumnDm) {
    // 类型名都大写！
    var diff = ""
    var newType = cd.type
    var oldType = columnDm.type
    // 同名合并
    if (newType == "INTEGER") newType = "INT"
    if (oldType == "INTEGER") oldType = "INT"

    if (newType != oldType) diff += "类型从 $oldType 改为 $newType。"
    if ((newType == "VARCHAR" || newType == "CHAR" || newType == "CLOB") && cd.length != columnDm.length) {
      diff += "长度从 ${columnDm.length} 改为 ${cd.length}。"
    }
    if (newType == "DECIMAL") {
      if (cd.width != columnDm.width) diff += "数字宽度从 ${columnDm.width} 改为 ${cd.width}。"
      if (cd.scale != columnDm.scale) diff += "数字精度从 ${columnDm.scale} 改为 ${cd.scale}。"
    }

    if (diff.isBlank()) return

    logger.info("修改列 {}:{}。差异：{}", tableDef.name, cd.name, diff)

    val eTableName = SqlHelper.escapeIdentifier(tableDef.name, sc.sqlDialect)
    val eColumnName = SqlHelper.escapeIdentifier(cd.name, sc.sqlDialect)

    // 仅支持文本类、同类型的长度扩大
    if ((newType == "VARCHAR" || newType == "CLOB" || newType == "CHAR") &&
      newType == oldType &&
      cd.length != null &&
      columnDm.length != null &&
      cd.length > columnDm.length
    ) {
      val sql = if (sc.sqlDialect == DbType.MySQL) {
        String.format(
          "ALTER TABLE %s CHANGE COLUMN %s %s",
          eTableName,
          eColumnName,
          toColumnDll(cd, tableDef.dialect),
        )
      } else if (sc.sqlDialect == DbType.Derby) {
        String.format(
          "ALTER TABLE %s ALTER COLUMN %s SET DATA TYPE %s",
          eTableName,
          eColumnName,
          getSqlTypeStr(cd),
        )
      } else if (sc.sqlDialect == DbType.SqlServer) {
        String.format("ALTER TABLE %s ALTER COLUMN %s", eTableName, toColumnDll(cd, tableDef.dialect))
      } else if (sc.sqlDialect == DbType.Dameng) {
        String.format("ALTER TABLE %s MODIFY %s", eTableName, toColumnDll(cd, tableDef.dialect))
      } else {
        return
      }
      logger.debug("修改列 SQL {}", sql)
      SqlHelper.executeSql(sc, sql)
    } else {
      // 先 drop 原列
      SqlHelper.executeSql(sc, "ALTER TABLE $eTableName DROP COLUMN $eColumnName")
      addColumn(sc, tableDef, cd)
    }
  }

  fun listTableNames(sc: SmartConnection): List<String> {
    val md = sc.connection.metaData
    // 不同的数据库对 catalog / schema 是否支持不同。因此不得不加强以下限制，取非空的。
    val schema = StringUtils.firstNonBlank(sc.connection.catalog, sc.connection.schema)

    val tableRs = if (sc.sqlDialect == DbType.MySQL) {
      md.getTables(schema, schema, null, arrayOf("TABLE"))
    } else if (sc.sqlDialect == DbType.Derby || sc.sqlDialect == DbType.SqlServer) {
      md.getTables(null, null, null, arrayOf("TABLE"))
    } else {
      md.getTables(null, null, null, arrayOf("TABLE"))
    }

    val tableNames: MutableList<String> = ArrayList()
    while (tableRs.next()) {
      val tn = tableRs.getString("TABLE_NAME")
      tableNames += tn
    }

    return tableNames
  }

  fun isTableExisted(sc: SmartConnection, tableName: String?): Boolean {
    val md = sc.connection.metaData
    // 不同的数据库对 catalog / schema 是否支持不同。因此不得不加强以下限制，取非空的。
    val schema = StringUtils.firstNonBlank(sc.connection.catalog, sc.connection.schema)
    val rs: ResultSet = if (sc.sqlDialect == DbType.MySQL) {
      md.getTables(schema, schema, tableName, null)
    } else if (sc.sqlDialect == DbType.Derby) {
      md.getTables(null, null, tableName, null)
    } else if (sc.sqlDialect == DbType.SqlServer) {
      md.getTables(null, null, tableName, null)
    } else {
      return true
    }
    return rs.next()
  }

  /**
   * 将实体定义转换为表定义。
   * 一个实体可能对应多个表。
   * - 单值文件/图片：对应两列，一列取字段名，存路径；一列取字段名+Size，存大小。
   * - 多值文件/图片表：owner/order，路径列，大小列，共四列
   * - 单值引用单实体：一列取字段名
   * - 单值引用多实体：加一列存引用哪个实体，字段名+RefEntity
   * - 多值引用表：owner/order，上面两列，共四列
   * - 组件表：组件ID、owner/order、组件的字段
   * - 单值其他：单列
   * - 多值其他：owner/order，字段名，三列
   */
  private fun toTableDef(entityMetaMap: Map<String, EntityMeta>, dialect: DbType) {
    for (em in entityMetaMap.values) {
      if (em.disabled) continue

      if (!em.fields.containsKey("id")) {
        logger.error("实体 ${em.name} 没有 id 字段")
        continue
      }

      emToTableDef(em, em.name, dialect)
    }
  }

  private fun emToTableDef(em: EntityMeta, tableName: String, dialect: DbType) {
    val tableDef = TableDef(dialect, tableName, idColumns = listOf("id"))

    for (fm in em.fields.values) {
      if (fm.type == FieldType.File || fm.type == FieldType.Image) {
        processFileOrImage(fm, tableName, tableDef, dialect)
      } else if (fm.type == FieldType.Reference) {
        processRefEntity(fm, tableName, tableDef, dialect)
      } else if (fm.type == FieldType.Component) {
        val colDef = ColumnDef(fm.name, "CHAR", 100)
        tableDef.columns[colDef.name] = colDef
      } else {
        if (fm.sqlType == FieldSqlType.None) continue
        if (fm.scale == FieldScale.List) {
          buildListTable(listOf(fm), fm.buildRelatedTableName(tableName), dialect)
        } else {
          val colDef = fmToColumnDef(fm, dialect)
          tableDef.columns[colDef.name] = colDef
        }
      }
    }

    if (tableDef.columns.isNotEmpty()) {
      tableDefMap[tableName] = tableDef
    }
  }

  private fun processFileOrImage(fm: FieldMeta, mainTableName: String, tableDef: TableDef, dialect: DbType) {
    if (fm.scale == FieldScale.List) {
      // 文件需要三列，存路径、名称和大小
      val path = FieldMeta(FieldMeta.COLUMN_FILE_PATH, sqlType = FieldSqlType.Varchar, length = 255)
      val name = FieldMeta(FieldMeta.COLUMN_FILE_NAME, sqlType = FieldSqlType.Varchar, length = 255)
      val size = FieldMeta(FieldMeta.COLUMN_FILE_SIZE, sqlType = FieldSqlType.Int, numWidth = 15)
      val md5 = FieldMeta(FieldMeta.COLUMN_FILE_MD5, sqlType = FieldSqlType.Varchar, length = 50)
      buildListTable(listOf(path, name, size, md5), fm.buildRelatedTableName(mainTableName), dialect)
    } else {
      // 不需要关联表
      tableDef.addColumns(
        fmToColumnDef(FieldMeta(fm.name, sqlType = FieldSqlType.Varchar, length = 255), dialect),
        fmToColumnDef(FieldMeta(fm.buildFileNameColumnName(), sqlType = FieldSqlType.Varchar, length = 255), dialect),
        fmToColumnDef(FieldMeta(fm.buildFileSizeColumnName(), sqlType = FieldSqlType.Int, numWidth = 15), dialect),
        fmToColumnDef(FieldMeta(fm.buildFileMd5ColumnName(), sqlType = FieldSqlType.Varchar, length = 50), dialect),
      )
    }
  }

  private fun processRefEntity(fm: FieldMeta, mainTableName: String, tableDef: TableDef, dialect: DbType) {
    if (fm.scale == FieldScale.List) {
      buildListTable(
        listOf(FieldMeta(FieldMeta.COLUMN_REF_ID, sqlType = FieldSqlType.Varchar, length = 100)),
        fm.buildRelatedTableName(mainTableName),
        dialect,
      )
    } else {
      // 引用外键列
      tableDef.addColumns(
        fmToColumnDef(FieldMeta(fm.name, sqlType = FieldSqlType.Varchar, length = FieldMeta.ID_LENGTH), dialect),
      )
    }
  }

  private fun buildListTable(fmList: List<FieldMeta>, tableName: String, dialect: DbType) {
    val tableDef = TableDef(dialect, tableName)

    // 附加两列：父引用、索引
    val fields: MutableList<FieldMeta> = ArrayList()
    fields.add(buildFmOwner())
    fields.add(buildFmOrder())
    fields.addAll(fmList)

    for (fm in fields) {
      tableDef.addColumns(fmToColumnDef(fm, dialect))
    }

    tableDefMap[tableName] = tableDef
  }

  //  private fun processComponent(fm: FieldMeta, tables: MutableList<TableDef>, dialect: DbType) {
  //    val refEm = BaseCenter.mustGetRefEntityMeta(fm)
  //    // 还是要有一个 ID 列，否则如果组件有子表
  //    val columns: MutableList<ColumnDef> = ArrayList()
  //    if (!refEm.fields.contains("id")) {
  //      columns += fmToColumnDef(FieldMeta("id", sqlType = FieldSqlType.Varchar, length = FieldMeta.ID_LENGTH), dialect)
  //    }
  //    if (!refEm.fields.contains(FieldMeta.COLUMN_OWNER)) {
  //      columns += fmToColumnDef(buildFmOwner(), dialect)
  //    }
  //    if (!refEm.fields.contains(FieldMeta.COLUMN_ORDER)) {
  //      columns += fmToColumnDef(buildFmOrder(), dialect)
  //    }
  //
  //    // 用组件业务对象名！
  //    emToTableDef(refEm, refEm.name, tables, columns, dialect)
  //  }

  private fun buildFmOwner(): FieldMeta =
    FieldMeta(FieldMeta.COLUMN_OWNER, sqlType = FieldSqlType.Varchar, length = FieldMeta.ID_LENGTH)

  private fun buildFmOrder(): FieldMeta = FieldMeta(FieldMeta.COLUMN_ORDER, sqlType = FieldSqlType.Int, numWidth = 12)

  //  fun getHistoryIdColumnDef(): ColumnDef {
  //    return ColumnDef("historyId", FieldSqlType.Varchar, width = FieldDef.ID_LENGTH)
  //  }

  /**
   * 带 length 的：VARCHAR、CHAR、CLOB
   * 带 width/scale 的：DECIMAL
   */
  private fun fmToColumnDef(fm: FieldMeta, dialect: DbType): ColumnDef {
    val sqlType = fm.sqlType

    if (sqlType == FieldSqlType.Char) {
      var length = fm.length
      if (length <= 0) length = 100
      return ColumnDef(fm.name, "CHAR", length)
    } else if (sqlType == FieldSqlType.Varchar) {
      var length = fm.length
      if (length <= 0) length = 100
      return if (dialect == DbType.MySQL) {
        // 纸面最大 65535
        if (length <= 10000) {
          ColumnDef(fm.name, "VARCHAR", length)
        } else {
          ColumnDef(fm.name, "LONGTEXT", null)
        }
      } else if (dialect == DbType.Derby) {
        // 纸面最大 32672
        if (length <= 10000) {
          ColumnDef(fm.name, "VARCHAR", length)
        } else {
          ColumnDef(fm.name, "CLOB", length)
        }
      } else if (dialect == DbType.SqlServer) {
        if (length <= 4000) {
          ColumnDef(fm.name, "VARCHAR", length)
        } else {
          ColumnDef(fm.name, "TEXT", null)
        }
      } else if (dialect == DbType.Dameng) {
        /*
          达梦数据库的 VARCHAR 最大长度可设置到 32767，但实际大小需要根据`USING LONG ROW` 存储选项（DM8 无此选项，应该是不指定）决定
          1. 若不指定则插入 VARCHAR 数据类型的实际最大存储长度由数据库页面大小以及字符集决定
          2. 若指定则最大长度为 32767
          默认页大小配置为 8k，若字符集为 UTF8，实测最大长度为 3875
         */
        if (length <= 3875) {
          ColumnDef(fm.name, "VARCHAR", length)
        } else {
          ColumnDef(fm.name, "LONGVARCHAR", null)
        }
      } else {
        throw IllegalStateException()
      }
    } else if (sqlType == FieldSqlType.Int) {
      return if (dialect == DbType.MySQL || dialect == DbType.Dameng) {
        ColumnDef(fm.name, "INTEGER", null)
      } else if (dialect == DbType.Derby) {
        ColumnDef(fm.name, "INTEGER", null)
      } else if (dialect == DbType.SqlServer) {
        ColumnDef(fm.name, "INT", null)
      } else {
        throw IllegalStateException()
      }
    } else if (sqlType == FieldSqlType.BigInt) {
      return ColumnDef(fm.name, "BIGINT", null)
    } else if (sqlType == FieldSqlType.Decimal) {
      var width = fm.numWidth
      if (width <= 0) width = FieldMeta.DEFAULT_DECIMAL_M
      var scale = fm.numScale
      if (scale < 0) scale = FieldMeta.DEFAULT_DECIMAL_D
      return ColumnDef(fm.name, "DECIMAL", width = width, scale = scale)
    } else if (sqlType == FieldSqlType.DateTime) {
      return if (dialect == DbType.SqlServer) {
        ColumnDef(fm.name, "DATETIME")
      } else {
        ColumnDef(fm.name, "TIMESTAMP")
      }
    } else if (sqlType == FieldSqlType.Date) {
      return ColumnDef(fm.name, "DATE")
    } else if (sqlType == FieldSqlType.Time) {
      return ColumnDef(fm.name, "TIME")
    } else {
      throw IllegalStateException("Bad sql type: $sqlType, name=${fm.name}")
    }
  }

  private fun getSqlTypeStr(cd: ColumnDef): String = if (cd.length != null) {
    "${cd.type}(${cd.length})"
  } else if (cd.width != null) {
    "${cd.type}(${cd.width}, ${cd.scale})"
  } else {
    cd.type
  }

  private fun toColumnDll(cd: ColumnDef, dialect: DbType): String {
    val sqlTypeStr = getSqlTypeStr(cd)
    val notNullPart = if ("id" == cd.name) " NOT NULL DEFAULT ''" else ""
    // val defaultValue = toDefaultValue()
    // val defaultValuePart = if (defaultValue == null) "" else String.format(" DEFAULT %s", defaultValue)
    val columnName = SqlHelper.escapeIdentifier(cd.name, dialect)
    return "$columnName $sqlTypeStr$notNullPart"
  }

  /**
   * 进一步修复。
   */
  private fun fix(dialect: DbType) {
    for (table in tableDefMap.values) {
      // 检查必须有 id
      if (table.columns.containsKey("id")) continue
      table.columns["id"] = ColumnDef("id", type = "VARCHAR", length = FieldMeta.ID_LENGTH)
    }
    if (dialect == DbType.MySQL) {
      // MySQL 行不能超过 65535，超过把 VARCHAR 改成 LONGTEXT
      for (table in tableDefMap.values) {
        val varcharColumns = table.columns.values.filter { it.type == "VARCHAR" || it.type == "CHAR" }
          .sortedBy { it.length }
        var len = 0
        for (col in varcharColumns) {
          val cLen = (col.length ?: 0) * 4 // 字符 -> 字节
          if (len + cLen >= 65535) {
            // logger.debug("将列从 VARCHAR 改为 LONGTEXT: ${table.name}.${col.name}")
            // length 必须清掉
            table.columns[col.name] = col.copy(type = "LONGTEXT", length = null)
          }
          len += cLen
        }
      }
    } else if (dialect == DbType.SqlServer) {
      for (table in tableDefMap.values) {
        val varcharColumns = table.columns.values.filter { it.type == "VARCHAR" || it.type == "CHAR" }
          .sortedBy { it.length }
        var len = 0
        for (col in varcharColumns) {
          val cLen = col.length ?: 0
          // 单行不能超过的字节长度 8060（包括 7 字节的内部开销）
          if (len + cLen > 8053) {
            table.columns[col.name] = col.copy(type = "TEXT", length = null)
          }
          len += cLen
        }
      }
    }
  }

  //  private fun toDefaultValue(): Any? {
  //    if (sqlType == FieldSqlType.Char || sqlType == FieldSqlType.Varchar) {
  //      return if (defaultValue == null) null else String.format("'%s'", defaultValue)
  //    } else if (sqlType == FieldSqlType.Int || sqlType == FieldSqlType.BigInt || sqlType == FieldSqlType.Decimal) {
  //      return if (defaultValue == null) {
  //        null
  //      } else if ("true" == defaultValue) {
  //        1
  //      } else if ("false" == defaultValue) {
  //        0
  //      } else {
  //        java.lang.Double.valueOf(defaultValue)
  //      }
  //    } else if (sqlType == FieldSqlType.DateTime || sqlType == FieldSqlType.Date || sqlType == FieldSqlType.Time) {
  //      return if (StringUtils.isBlank(defaultValue)) {
  //        null
  //      } else {
  //        String.format("'%s'", defaultValue)
  //      }
  //    }
  //    return null
  //  }
}

data class TableDef(
  val dialect: DbType,
  val name: String,
  val columns: MutableMap<String, ColumnDef> = ConcurrentHashMap(),
  val idColumns: List<String>? = null,
) {

  fun addColumns(vararg newColumns: ColumnDef) {
    for (c in newColumns) columns[c.name] = c
  }
}

data class ColumnDef(
  val name: String,
  val type: String,
  val length: Int? = null,
  val width: Int? = null,
  val scale: Int? = null,
  //  val notNull: Boolean = false,
  //  val defaultValue: String? = null
)