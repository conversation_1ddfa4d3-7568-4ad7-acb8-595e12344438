package com.seer.trick.base.http.handler

import com.seer.trick.Cq
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.getReqBody
import com.seer.trick.base.soc.SysMonitorService
import com.seer.trick.base.stats.manila.ManilaReportService
import io.javalin.http.Context
import java.util.*

object StatsManilaHandler {
  fun registerHandlers() {
    val c = Handlers("api")

    // 开始统计全量数据（与定时器调用函数不同）
    c.post("stats/gen-total", StatsManilaHandler::manilaStatsGenTotal, auth())
    // 重新生成当前周期的报表（同定时器调用的函数）
    c.post("stats/gen-incremental", StatsManilaHandler::manilaStatsGenIncremental, auth())
  }

  // 针对所有科目进行全量统计：基于二次统计机制，对记录的所有时间段的所有记录进行统计，并覆盖原来的记录。
  private fun manilaStatsGenTotal(ctx: Context) {
    SysMonitorService.log(
      "Stat",
      "-",
      "-",
      "Gen total report",
      ManilaReportService.disableLogging(),
    )

    // 基于现有的配置进行全量统计，但是不包括出库单和入库单。
    ManilaReportService.genReport()

    // 针对出库单和入库单相关的统计。
    // QsStatsService.genReportQuickStore()
    ManilaReportService.reportGenerators.forEach { it(null) }
  }

  // 增量统计：基于二次统计机制，统计所选时间段内（可能是当天，也可能是指定的任意时间段）的所有数据，必要时覆盖原来的记录。
  private fun manilaStatsGenIncremental(ctx: Context) {
    val req: GenIncrementalReq = ctx.getReqBody()
    SysMonitorService.log(
      
      "Stat",
      "-",
      "-",
      "Gen incremental report, req=$req",
      ManilaReportService.disableLogging(),
    )
    val additionalCq = if (req.start != null && req.end != null) {
      Cq.between("finishedOn", req.start, req.end)
    } else if (req.start != null) {
      Cq.gte("finishedOn", req.start)
    } else if (req.end != null) {
      Cq.lte("finishedOn", req.end)
    } else {
      null
    }

    // 生成时间序列表数据
    ManilaReportService.genReport(additionalCq)

    // 针对出库单和入库单相关的统计。
    // QsStatsService.genReportQuickStore(additionalCq)
    ManilaReportService.reportGenerators.forEach { it(additionalCq) }
  }
}

data class GenIncrementalReq(val start: Date?, val end: Date?)