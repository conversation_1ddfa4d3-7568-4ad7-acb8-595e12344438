package com.seer.trick.base.bgt

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.I18N.lo
import com.seer.trick.base.MapToAnyNull
import com.seer.trick.base.alarm.AlarmAction
import com.seer.trick.base.alarm.AlarmItem
import com.seer.trick.base.alarm.AlarmLevel
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.base.concurrent.BaseConcurrentCenter
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.entity.service.RemoveOptions
import com.seer.trick.base.entity.service.extension.EntityServiceExtension
import com.seer.trick.base.entity.service.extension.EntityServiceExtensions
import com.seer.trick.base.script.ScriptCenter
import com.seer.trick.base.script.ScriptExeRequest
import com.seer.trick.base.script.ScriptTraceContext
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.ThreadHelper.newBoundedThreadPool
import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors
import java.util.concurrent.Future

/**
 * 后台任务主服务。
 * 完成、终止的任务会被删除。
 * 暂停，只是标记。代码里主动检查这个标志位，调用 suspendIfPaused()。
 * 终止，只是标记。代码里主动检查这个标志位，调用 throwIfAborted()。
 * TODO 优先级要做吗？
 */
object BgTaskService : EntityServiceExtension() {
  
  private val logger = LoggerFactory.getLogger(javaClass)
  
  /**
   * 注册内部处理函数。
   */
  val taskInternalExecutors: MutableMap<String, BgTaskExecutor> = ConcurrentHashMap()
  
  /**
   * 执行中的任务（不含故障的）：任务 ID->
   */
  private val executingTasks: MutableMap<String, BgTaskRuntime> = ConcurrentHashMap()
  
  private val updateAlarmExecutor = Executors.newSingleThreadExecutor()
  
  @Volatile
  private var updateAlarmFuture: Future<*>? = null
  
  // 需要公平？
  private val executor = newBoundedThreadPool(3, 30)
  
  init {
    EntityServiceExtensions.addExtension("BgTaskRecord", this)
  }
  
  fun init() {
    updateAlarmFuture = updateAlarmExecutor.submit(::updateAlarmLoop)
  }
  
  fun dispose() {
    updateAlarmFuture?.cancel(true)
  }
  
  /**
   * 后台（异步）运行指定后台任务。
   * taskName 后台任务名字。先找内部处理器，否则找脚本函数名（没有报错）。
   * taskArgs 任务参数必须能被序列化。
   * remark 要仔细构造。列表界面、故障时都会显示！
   * TODO 多个同名后台任务会同时执行，由业务代码控制是否合适
   */
  fun runBgTask(taskName: String, remark: String, taskArgs: MapToAnyNull?): BgTaskRuntime {
    val argsStr = if (taskArgs == null) null else JsonHelper.writeValueAsString(taskArgs)
    var ev: EntityValue = mutableMapOf(
      "name" to taskName,
      "remark" to remark,
      "args" to argsStr,
    )
    val id = EntityRwService.createOne("BgTaskRecord", ev)
    
    ev = EntityRwService.findOne("BgTaskRecord", Cq.idEq(id))
      ?: throw BzError("errCodeErr", "No bg task: $id")
    
    return executeAsync(ev)
  }
  
  @Synchronized
  private fun executeAsync(ev: EntityValue): BgTaskRuntime {
    val taskId = EntityHelper.mustGetId(ev)
    // 不能重复提交
    val exist = executingTasks[taskId]
    if (exist != null) {
      logger.info("BgTask executing, return executeAsync: $taskId")
      return exist
    }
    
    val tr = BgTaskRuntime(taskId, ev)
    
    executingTasks[taskId] = tr
    
    tr.future = executor.submit {
      execute(tr)
    }
    
    return tr
  }
  
  /**
   * 实际执行任务。不抛异常
   */
  private fun execute(tr: BgTaskRuntime) {
    try {
      val exe = taskInternalExecutors[tr.taskName]
      if (exe != null) {
        exe(tr)
      } else {
        ScriptCenter.execute(ScriptExeRequest(tr.taskName, arrayOf(ScriptTraceContext(), tr)))
      }
      
      // 成功执行结束
      executingTasks.remove(tr.id)
      // 删除任务，不要触发终止处理
      EntityRwService.removeOne("BgTaskRecord", Cq.idEq(tr.id), RemoveOptions(muteExt = true))
      removeStepsAsync(tr.id)
    } catch (_: BgTaskAbortedException) {
      logger.info("Bg task execution catch BgTaskAbortedException: ${tr.taskName}:${tr.id}")
    } catch (e: Exception) {
      logger.error("Failed to execute bg task: ${tr.taskName}:${tr.id}", e)
      
      // 任务故障
      executingTasks.remove(tr.id)
      EntityRwService.updateOne(
        
        "BgTaskRecord",
        Cq.idEq(tr.id),
        mutableMapOf("fault" to true, "faultMsg" to "[${e.javaClass.simpleName}] ${e.message}"),
      )
    }
  }
  
  /**
   * 恢复所有因系统异常终止导致中断的后台任务
   *
   * TODO 重启脚本后，是否会导致出库分配库存也重新运行？
   *
   * M4 重启后，后台任务的重试也用这个函数
   */
  fun recoverAll() {
    
    
    // 把之前故障的都清掉，自动重新执行
    EntityRwService.updateMany(
      
      "BgTaskRecord",
      Cq.all(),
      mutableMapOf("fault" to false, "faultMsg" to ""),
    )
    
    // updateMany 与 findMany 中间可能被塞入了新的 BgTaskRecord，这些新的后台任务状态是执行中。通过 executeAsync 防止被重复执行
    val evList = EntityRwService.findMany("BgTaskRecord", Cq.all())
    if (evList.isEmpty()) return
    
    logger.info("Recover bg tasks: ${evList.size}")
    
    for (ev in evList) {
      logger.info("Recover bg task: ${ev["name"]}:${ev["id"]}")
      executeAsync(ev)
    }
  }
  
  /**
   * 恢复指定后台任务
   */
  fun recoverTasks(taskIds: List<String>) {
    for (taskId in taskIds) {
      EntityRwService.updateOne("BgTaskRecord", Cq.idEq(taskId), mutableMapOf("fault" to false, "faultMsg" to ""))
      
      // 暂时忽略不存在的
      val ev = EntityRwService.findOne("BgTaskRecord", Cq.idEq(taskId)) ?: continue
      logger.info("Recover bg task: ${ev["name"]}:${ev["id"]}")
      
      executeAsync(ev)
    }
  }
  
  /**
   * 终止任务
   */
  fun abortTasks(taskIds: List<String>) {
    logger.error("Abort bg tasks: $taskIds")
    
    EntityRwService.removeMany("BgTaskRecord", Cq.include("id", taskIds))
    
    // 后续靠 afterRemoving() 处理
  }
  
  /**
   * 如果删除 BgTaskRecord，终止后台脚本
   */
  override fun afterRemoving(em: EntityMeta, oldValues: List<EntityValue>) {
    if (em.name != "BgTaskRecord") return
    
    // 因为没有阻塞操作，直接同步调用
    for (ev in oldValues) {
      val id = EntityHelper.mustGetId(ev)
      val tr = executingTasks.remove(id) ?: continue
      tr.ev["aborted"] = true
      // 不中断线程
      
      // 删除关联步骤
      removeStepsAsync(tr.id)
    }
  }
  
  /**
   * 运行后台任务中的一步。
   * stepId 是这一步的 ID，在一个任务中必须唯一。
   * 必须返回一个 Map，必须能被持久化。
   * 最多运行一次。如果已运行，直接返回上一次运行结果。
   */
  fun runStepOnce(tr: BgTaskRuntime, stepId: String, work: () -> MapToAnyNull): MapToAnyNull {
    tr.throwIfAborted()
    tr.suspendIfPaused()
    
    logger.info("Run bg task step '${tr.taskName}:${tr.id}:$stepId', start")
    
    val id = "${tr.id}-$stepId"
    val old = EntityRwService.findOne("BgTaskStepRecord", Cq.idEq(id))
    if (null != old) {
      val jsonStr = old["output"] as String?
      logger.info("Bg task step '${tr.taskName}:${tr.id}:$stepId' already done, output=$jsonStr")
      if (jsonStr.isNullOrBlank()) return HashMap()
      return JsonHelper.mapper.readValue(jsonStr, jacksonTypeRef())
    }
    
    tr.throwIfAborted()
    tr.suspendIfPaused()
    
    val output = work()
    
    logger.info("Run bg task step '${tr.taskName}:${tr.id}:$stepId', end")
    val jsonStr = JsonHelper.writeValueAsString(output)
    EntityRwService.createOne(
      
      "BgTaskStepRecord",
      mutableMapOf("id" to id, "taskId" to tr.id, "output" to jsonStr),
    )
    
    // 放这里，先持久化完再扔异常，保证步骤是原子的
    tr.throwIfAborted()
    tr.suspendIfPaused()
    
    return output
  }
  
  fun pauseOrResumeTasks(ids: List<String>, paused: Boolean) {
    EntityRwService.updateMany("BgTaskRecord", Cq.include("id", ids), mutableMapOf("paused" to paused))
    for (id in ids) {
      val tr = executingTasks[id] ?: continue
      tr.ev["paused"] = paused
    }
  }
  
  /**
   * 删除与任务关联的步骤
   */
  private fun removeStepsAsync(taskId: String) {
    BaseConcurrentCenter.lowTimeSensitiveExecutor.submit {
      EntityRwService.removeMany("BgTaskStepRecord", Cq.eq("taskId", taskId))
    }
  }
  
  private fun updateAlarmLoop() {
    while (true) {
      updateAlarm()
      Thread.sleep(500)
    }
  }
  
  private fun updateAlarm() {
    
    val evList = EntityRwService.findMany(
      
      "BgTaskRecord",
      Cq.eq("fault", true),
      FindOptions(projection = listOf("id", "fault", "faultMsg", "remark")),
    )
    
    // 先清之前的
    AlarmService.removeAllByCode("BgTaskFault")
    
    for (it in evList) {
      val remark = it["remark"] as String? ?: ""
      val faultMsg = it["faultMsg"] as String? ?: ""
      val id = EntityHelper.mustGetId(it)
      val ai = AlarmItem(
        group = "BgTask",
        code = "BgTaskFault",
        key = "BgTaskFault-$id",
        level = AlarmLevel.Error,
        message = lo("bgTaskErrorMsg", listOf(remark, faultMsg)),
        args = listOf(id),
        actions = listOf(
          AlarmAction(lo("btnFaultRetry")),
          AlarmAction(lo("btnCancelTask")),
        ),
      )
      AlarmService.addItem(ai) { actionIndex, args ->
        if (actionIndex == 1) {
          // 故障重试
          recoverTasks(listOf(args[0] as String))
        } else if (actionIndex == 2) {
          // 取消任务
          abortTasks(listOf(args[0] as String))
        }
      }
    }
  }
}