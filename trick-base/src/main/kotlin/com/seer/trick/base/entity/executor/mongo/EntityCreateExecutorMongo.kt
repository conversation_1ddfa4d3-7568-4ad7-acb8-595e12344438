package com.seer.trick.base.entity.executor.mongo

import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.*
import com.seer.trick.base.entity.executor.EntityCreateWorkContext
import com.seer.trick.helper.IdHelper
import org.apache.commons.lang3.StringUtils

object EntityCreateExecutorMongo {

  // onlyRelatedTable: 只插入关联表，不插入自己
  fun execute(
    ctx: EntityCreateWorkContext,
    em: EntityMeta,
    entityValues: List<EntityValue>,
    onlyRelatedTable: Boolean = false,
  ) {
    processEntity(ctx, em, entityValues)
    if (onlyRelatedTable) ctx.tables.remove(em.name)
    for (entityName in ctx.tables.keys) {
      val rows = ctx.tables[entityName]
      if (rows.isNullOrEmpty()) continue
      val ed = BaseCenter.mustGetEntityMeta(entityName)
      MongoExecutor.insertManyEntityValues(ed, rows)
    }
  }

  private fun processEntity(ctx: EntityCreateWorkContext, em: EntityMeta, entityValues: List<EntityValue>) {
    em.checkIdExisted()

    for (entityValue in entityValues) {
      val id = EntityHelper.mustGetId(entityValue)
      val mainRow: EntityValue = mutableMapOf()
      for (fd in em.fields.values) {
        if (!entityValue.containsKey(fd.name)) continue
        if (fd.type == FieldType.Component) {
          processComponentField(fd, entityValue[fd.name], id, ctx)
          if (fd.scale == FieldScale.Single) {
            mainRow[fd.name] = if (entityValue[fd.name] != null) 1 else null
          } else {
            val updateList = entityValue[fd.name] as List<*>?
            mainRow[fd.name] = if (!updateList.isNullOrEmpty()) {
              updateList.size
            } else {
              null
            }
          }
        } else {
          mainRow[fd.name] = entityValue[fd.name]
        }
      }

      // TODO 怎么体现组件表
      if (em.type == EntityType.Component) {
        mainRow["id"] = entityValue["id"]
        mainRow[FieldMeta.COLUMN_OWNER] = entityValue[FieldMeta.COLUMN_OWNER]
        mainRow[FieldMeta.COLUMN_ORDER] = entityValue[FieldMeta.COLUMN_ORDER]
      }
      ctx.addRow(em.name, mainRow)
    }
  }

  @Suppress("UNCHECKED_CAST")
  private fun processComponentField(fd: FieldMeta, fv: Any?, owner: String?, ctx: EntityCreateWorkContext) {
    if (fv == null || fv is Unit) return
    val componentEntityDef = BaseCenter.mustGetRefEntityMeta(fd)
    val fvList: List<EntityValue> = if (fd.scale == FieldScale.Single) {
      listOf(fv as EntityValue)
    } else {
      fv as List<EntityValue>
    }
    for (i in fvList.indices) {
      val v = fvList[i]
      var comId = v["id"] as String?
      if (StringUtils.isBlank(comId)) comId = IdHelper.oidStr()
      v["id"] = comId
      v[FieldMeta.COLUMN_OWNER] = owner
      v[FieldMeta.COLUMN_ORDER] = i + 1
      v[FieldMeta.FIELD_VERSION] = 0
    }
    processEntity(ctx, componentEntityDef, fvList)
  }
}