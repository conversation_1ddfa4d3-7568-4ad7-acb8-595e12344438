package com.seer.trick.base.concurrent

import com.seer.trick.helper.ThreadHelper.newBoundedThreadPool
import java.util.concurrent.Executors
import java.util.concurrent.Future

/**
 * 因为线程是最昂贵的资源，因此集中管理
 */
object BaseConcurrentCenter {

  /**
   * 时效性要求高的通用执行器，主要是工作线程多
   */
  val highTimeSensitiveExecutor = newBoundedThreadPool(5, 500)

  /**
   * 时效性要求不高的通用执行器，主要是工作线程少
   */
  val lowTimeSensitiveExecutor = newBoundedThreadPool(3, 10)

  /**
   * 用于异步 HTTP 回调
   */
  val httpAsyncCallExecutor = newBoundedThreadPool(3, 50)

  /**
   * 用于并行上传
   */
  val uploadExecutor = newBoundedThreadPool(2, 10)

  /**
   * 用于执行后台脚本
   */
  val bgScriptExecutor = newBoundedThreadPool(3, 30)

  /**
   * 用于 WS 客户端请求响应的线程
   */
  val wsClientExecutor = newBoundedThreadPool(4, 100)

  /**
   * WS 监控线程
   */
  val wsMonitorExecutor = Executors.newSingleThreadExecutor()

  /**
   * 故障恢复用
   */
  val failureRecoveryExecutor = newBoundedThreadPool(2, 30)

  // 专用线程
  private val adhocLoopExecutor = Executors.newCachedThreadPool()

  /**
   * 获得一个专用的持续使用的线程
   */
  fun getAdhocLoopThead(purpose: String, worker: () -> Unit): Future<*> = adhocLoopExecutor.submit(worker)

  /**
   * 统计线程
   */
  val statsExecutor = Executors.newSingleThreadExecutor()
}