package com.seer.trick.base.entity

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.I18N.lo
import com.seer.trick.base.BaseCenter.baseConfig
import com.seer.trick.base.BaseCenter.componentEmSet
import com.seer.trick.base.BaseCenter.entityMetaMap
import com.seer.trick.base.DbType
import com.seer.trick.base.db.sql.SqlHelper
import com.seer.trick.base.log.SystemKeyEvent
import com.seer.trick.base.log.SystemKeyEventService
import com.seer.trick.helper.FileHelper
import com.seer.trick.helper.JsonFileHelper
import com.seer.trick.helper.JsonHelper
import org.apache.commons.io.FileUtils
import org.slf4j.LoggerFactory
import java.io.File

object EntityMetaManager {

  private val logger = LoggerFactory.getLogger(javaClass)

  private val builtinEntityMap = loadBuiltinEntityMap()

  @Synchronized
  fun loadMeta() {
    val builtinEntityMap = loadBuiltinEntityMap()

    // 升级
    EntityMetaUpgrade.upgrade()

    val entityDiffMap = loadEntityDiffMap()
    entityMetaMap.clear()
    for (em in builtinEntityMap.values) {
      val em2 = EntityMetaPatch.apply(em, entityDiffMap[em.name])
      if (em2 != null) entityMetaMap[em2.name] = em2
    }
    for (diff in entityDiffMap.values) {
      if (diff.addedEm != null) entityMetaMap[diff.addedEm.name] = diff.addedEm
    }

    for (em in entityMetaMap.values) patchEntityMeta(em)

    listComponentEntities()
  }

  // 会 fixEntityMeta + patchEntityMeta
  private fun loadBuiltinEntityMap(): Map<String, EntityMeta> {
    val metaStr = FileHelper.loadClasspathResourceAsString("/entities.json")
    if (metaStr.isNullOrBlank()) throw RuntimeException("No default meta file")
    val emList: List<EntityMeta> = JsonHelper.mapper.readValue(metaStr, jacksonTypeRef())
    val builtinEmList = parseI18nEntities(emList)

    val map: MutableMap<String, EntityMeta> = HashMap()
    for (em in builtinEmList) {
      em.builtin = true
      fixEntityMeta(em)
      patchEntityMeta(em)
      map[em.name] = em
    }
    return map
  }

  private fun loadEntityDiffMap(): Map<String, EntityMetaDiff> {
    val metaDir = getMetaChangeDir()
    val diffMap: MutableMap<String, EntityMetaDiff> = HashMap()

    if (metaDir.exists()) {
      val metaFilenames = metaDir.list()
      if (!metaFilenames.isNullOrEmpty()) {
        for (fn in metaFilenames) {
          val metaFile = File(metaDir, fn)
          val diff: EntityMetaDiff = JsonFileHelper.readJsonFromFile(metaFile) ?: continue
          diffMap[diff.entityName] = diff
        }
      }
    }

    logger.info("Entity changed num: ${diffMap.size}")

    return diffMap
  }

  private fun parseI18nEntities(entities: List<EntityMeta>): List<EntityMeta> {
    for (entity in entities) {
      entity.label = loIfNotBlank(entity.label)
      entity.group = loIfNotBlank(entity.group)
      for ((_, pageButtons) in entity.pagesButtons) {
        pageButtons.buttons.forEach { it.label = loIfNotBlank(it.label) }
      }
      //      entity.digest?.formatJs?.let {
      //        entity.digest.formatJs = lo(it)
      //      }
      entity.listCard.lines.forEach {
        it.forEach { ii ->
          run {
            ii.prefix = loIfNotBlank(ii.prefix)
            ii.suffix = loIfNotBlank(ii.suffix)
            ii.formatMapping.forEach { tt ->
              run {
                tt.replaceText = loIfNotBlank(tt.replaceText)
              }
            }
          }
        }
      }
      entity.states?.states?.let { states ->
        states.values.forEach {
          it.label = loIfNotBlank(it.label)
          it.nextStates.forEach { ii -> ii.buttonLabel = loIfNotBlank(ii.buttonLabel) }
        }
      }
      entity.kinds?.kinds?.let { kinds ->
        kinds.values.forEach {
          it.label = loIfNotBlank(it.label)
        }
      }
      entity.listStats?.items?.forEach { it.label = loIfNotBlank(it.label) }

      for ((_, fm) in entity.fields) {
        fm.label = loIfNotBlank(fm.label)
        fm.tip?.let { fm.tip = loIfNotBlank(it) }
        fm.inlineOptionBill?.items?.forEach { it.label = loIfNotBlank(it.label) }
        fm.view.trueText?.let { fm.view.trueText = loIfNotBlank(it) }
        fm.view.falseText?.let { fm.view.falseText = loIfNotBlank(it) }
        fm.listBatchButtons?.buttons?.forEach { it.label = loIfNotBlank(it.label) }
      }
    }

    return entities
  }

  private fun loIfNotBlank(s: String): String = if (s.isNotBlank()) lo(s) else ""

  private fun listComponentEntities() {
    componentEmSet.clear()
    for (em in entityMetaMap.values) {
      for (fm in em.fields.values) {
        if (fm.type == FieldType.Component && !fm.refEntity.isNullOrBlank()) componentEmSet.add(fm.refEntity)
      }
    }
  }

  private fun patchEntityMeta(em: EntityMeta) {
    if (!em.fields.containsKey(FieldMeta.FIELD_ID)) {
      em.fields[FieldMeta.FIELD_ID] = FieldMeta(
        FieldMeta.FIELD_ID,
        "ID",
        view = FieldView(displayOrder = -19),
        fuzzyFilter = true,
        sqlType = FieldSqlType.Varchar,
        length = 50,
      )
    }
    if (!em.fields.containsKey(FieldMeta.FIELD_VERSION)) {
      em.fields[FieldMeta.FIELD_VERSION] = FieldMeta(
        FieldMeta.FIELD_VERSION,
        "修订版本",
        type = FieldType.Int,
        view = FieldView(displayOrder = -1),
        sqlType = FieldSqlType.BigInt,
        defaultValue = 0,
      )
    }
    if (!em.fields.containsKey(FieldMeta.FIELD_CREATED_BY)) {
      em.fields[FieldMeta.FIELD_CREATED_BY] = FieldMeta(
        FieldMeta.FIELD_CREATED_BY,
        "创建人",
        type = FieldType.Reference,
        refEntity = "HumanUser",
        view = FieldView(displayOrder = 991),
        sqlType = FieldSqlType.Varchar,
        length = 50,
      )
    }
    if (!em.fields.containsKey(FieldMeta.FIELD_MODIFIED_BY)) {
      em.fields[FieldMeta.FIELD_MODIFIED_BY] = FieldMeta(
        FieldMeta.FIELD_MODIFIED_BY,
        "最后修改人",
        type = FieldType.Reference,
        refEntity = "HumanUser",
        view = FieldView(displayOrder = 992),
        sqlType = FieldSqlType.Varchar,
        length = 50,
      )
    }
    if (!em.fields.containsKey(FieldMeta.FIELD_CREATED_ON)) {
      em.fields[FieldMeta.FIELD_CREATED_ON] = FieldMeta(
        FieldMeta.FIELD_CREATED_ON,
        "创建时间",
        type = FieldType.DateTime,
        view = FieldView(displayOrder = 993),
        sqlType = FieldSqlType.DateTime,
      )
    }
    if (!em.fields.containsKey(FieldMeta.FIELD_MODIFIED_ON)) {
      em.fields[FieldMeta.FIELD_MODIFIED_ON] = FieldMeta(
        FieldMeta.FIELD_MODIFIED_ON,
        "修改时间",
        type = FieldType.DateTime,
        view = FieldView(displayOrder = 993),
        sqlType = FieldSqlType.DateTime,
      )
    }
  }

  /* 检查索引：
   1. 名称不能相同。
   2. 不同索引的字段必须不同，并且是忽略顺序的不同。举例子，index_1 用字段 id, name，index_2 用字段 name, id 是不允许的。
   */
  @Synchronized
  fun saveEntityMeta(em: EntityMeta) {
    patchEntityMeta(em)

    val uniqueSet = HashSet<String>()
    val uniqueField = HashSet<String>()
    for (index in em.indexes) {
      if (!uniqueSet.add(index.name)) throw BzError("errIndexNameDuplicated", index.name)
      val fieldSet = index.fields.map { it.name }.sorted().toString()
      if (!uniqueField.add(fieldSet)) throw BzError("errIndexFieldDuplicated", fieldSet)
      if (baseConfig.db.type != DbType.MongoDB && !SqlHelper.checkIndexValid(baseConfig.db.type, em, index)) {
        throw BzError("errIndexFieldLenTooLong", index.name, SqlHelper.maxIndexLen(baseConfig.db.type))
      }
    }

    entityMetaMap[em.name] = em

    saveEntityDiff(em)

    SystemKeyEventService.record(SystemKeyEvent(group = "Base", title = "修改业务对象 ${em.label} (${em.name})"))
  }

  fun saveEntityDiff(em: EntityMeta) {
    val builtinEm = builtinEntityMap[em.name]
    val diff = if (builtinEm != null) {
      EntityMetaPatch.diff(builtinEm, em)
    } else {
      EntityMetaDiff(em.name, addedEm = em)
    }

    val file = File(getMetaChangeDir(), toDiffFile(em.name))
    if (diff != null) {
      JsonFileHelper.writeJsonToFile(file, diff, true)
    } else {
      // 要删除！
      file.delete()
    }
  }

  @Synchronized
  fun removeEntityMeta(entityNames: List<String>) {
    for (entityName in entityNames) {
      entityMetaMap.remove(entityName)

      val file = File(getMetaChangeDir(), toDiffFile(entityName))
      if (builtinEntityMap.containsKey(entityName)) {
        val diff = EntityMetaDiff(entityName, removedEm = entityName)
        JsonFileHelper.writeJsonToFile(file, diff, true)
      } else {
        // 要删除！
        file.delete()
      }
    }

    SystemKeyEventService.record(SystemKeyEvent(group = "Base", title = "删除业务对象 $entityNames"))
  }

  fun resetEntityMeta() {
    val dir = getMetaChangeDir()
    FileUtils.deleteDirectory(dir)

    loadMeta()

    SystemKeyEventService.record(SystemKeyEvent(group = "Base", title = "重置了所有业务对象"))
  }

  /**
   * 将所有内建业务对象恢复到默认状态
   */
  fun resetBuiltinEntityMeta() {
    val dir = getMetaChangeDir()
    for (em in builtinEntityMap.values) {
      val file = File(dir, toDiffFile(em.name))
      file.delete()
    }

    loadMeta()

    SystemKeyEventService.record(SystemKeyEvent(group = "Base", title = "重置了所有内建业务对象"))
  }

  fun fixEntityMeta(em: EntityMeta) {
    // 单据状态 -> 业务状态
    if (em.states == null && em.orderConfig != null && em.orderConfig.enabled && em.orderConfig.states.isNotEmpty()) {
      val states = mutableMapOf<String, BzState>()
      for (state in em.orderConfig.states) {
        states[state.id] = state
      }
      em.states = BzStatesConfig(true, states = states)
    }
    // IdGenRule 流水号长度 0 -> 4
    if (em.idGen != null && em.idGen.flowNoWidth == 0) {
      em.idGen.flowNoWidth = 4
    }
  }

  private fun getMetaChangeDir(): File {
    val file = File(baseConfig.configDir.absolutePath, "entity-change-v1")
    file.mkdirs()
    return file
  }

  private fun toDiffFile(en: String): String = "$en.diff.json"
}