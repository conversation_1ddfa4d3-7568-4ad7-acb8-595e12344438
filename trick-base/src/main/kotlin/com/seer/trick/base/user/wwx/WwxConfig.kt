package com.seer.trick.base.user.wwx

import com.seer.trick.base.user.OAuthConfig
import com.seer.trick.base.entity.EntityValue

class WwxConfig(ev: EntityValue): OAuthConfig {
  val wwxEnabled: Boolean = ev["wwxEnabled"] as Boolean? ?: false
  val wwxCorpId: String = ev["wwxCorpId"] as String? ?: ""
  val wwxCorpSecret: String = ev["wwxCorpSecret"] as String? ?: ""
  val wwxAgent: String = ev["wwxAgent"] as String? ?: ""
}
