package com.seer.trick.base.http.handler

import com.seer.trick.base.alarm.AlarmChoice
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.getReqBody
import com.seer.trick.base.http.operator
import io.javalin.http.Context

object AlarmHandler {

  fun registerHandlers() {
    val c = Handlers("api/alarm")
    c.post("choice", ::makeChoice, auth())
  }

  private fun makeChoice(ctx: Context) {
    val r: AlarmChoice = ctx.getReqBody()
    AlarmService.makeChoice(r)
    ctx.status(200)
  }
}