package com.seer.trick.base.http.handler

import com.seer.trick.I18N
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.BaseCenter.singleEnabled
import com.seer.trick.base.SysEmc
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.file.FileManager
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.HttpServerManager.noAuth
import com.seer.trick.base.http.HttpServerManager.sendFileWithType
import com.seer.trick.base.http.HttpServerManager.sendStream
import com.seer.trick.base.http.getReqBody
import com.seer.trick.base.http.operator
import com.seer.trick.base.script.ScriptHttpServer
import com.seer.trick.helper.FileHelper
import io.javalin.http.Context
import java.io.File

object BaseHandler {

  fun registerHandlers() {
    val c = Handlers("api")
    c.get("base", ::getBase, noAuth())
    c.get("logo", ::getLogo, noAuth())

    c.get("i18n-ui", ::getI18NUiStr, noAuth())
    c.post("reload-i18n", ::reloadI18N, auth())

    // 登录后调用
    c.get("init", ::getInit, auth())

    c.get("menu", ::getMenu, noAuth())
    c.post("menu", ::updateMenu, auth())

    c.get("bz-config/{field}", ::getBzConfigItem, auth())
    c.post("bz-config/{field}", ::setBzConfigItem, auth())

    c.get("http-api", ::listHttpApi, auth())

    c.post("sys-emc", ::setSysEmc, auth())
  }

  @Suppress("UNCHECKED_CAST")
  private fun getBase(ctx: Context) {
    val basic = BaseCenter.bzConfig["basic"] as EntityValue?
    val uiExtFiles: List<String> = BaseCenter.baseConfig.uiExtDir.list()?.toList() ?: emptyList()

    ctx.json(
      mapOf(
        "single" to singleEnabled(),
        "name" to (basic?.get("name") as String? ?: ""),
        "serviceVersionName" to BaseCenter.version,
        "uiExtFiles" to uiExtFiles,
        "projectDir" to BaseCenter.baseConfig.projectDir.absolutePath,
      ),
    )
  }

  private fun getLogo(ctx: Context) {
    ctx.status(200)
    val file = getLogoFile()
    if (file != null) {
      sendFileWithType(file, ctx)
    } else {
      FileHelper::class.java.getResourceAsStream("/logo.png")!!.use {
        sendStream(it, ctx, "logo.png")
      }
    }
  }

  @Suppress("UNCHECKED_CAST")
  private fun getLogoFile(): File? {
    val basic = BaseCenter.bzConfig["basic"] as EntityValue? ?: return null
    val logo = basic["logo"] as EntityValue? ?: return null
    val logoPath = logo["path"] as String? ?: return null

    val file: File = FileManager.pathToFile(logoPath)
    if (!file.exists()) return null
    return file
  }

  private fun getInit(ctx: Context) {
    ctx.json(
      mapOf(
        "menu" to BaseCenter.menu,
        "emMap" to BaseCenter.entityMetaMap,
        "bz" to BaseCenter.bzConfig,
      ),
    )
  }

  private fun getMenu(ctx: Context) {
    ctx.result(BaseCenter.menu)
  }

  private fun updateMenu(ctx: Context) {
    val op = ctx.operator()

    if (!op.admin) throw Error403("权限不够（元数据项），需要管理员")

    val menu = ctx.body()
    BaseCenter.updateMenu(menu)

    ctx.status(200)
  }

  private fun getBzConfigItem(ctx: Context) {
    //
    // if (!op.admin) throw Error403("权限不够（元数据项），需要管理员")

    val field = ctx.pathParam("field")
    val fv = BaseCenter.bzConfig[field] ?: emptyMap<String, Any>()
    ctx.json(fv)
  }

  private fun setBzConfigItem(ctx: Context) {
    val op = ctx.operator()

    if (!op.admin) throw Error403("权限不够（元数据项），需要管理员")

    val field = ctx.pathParam("field")
    val req: EntityValue = ctx.getReqBody()

    BaseCenter.updateBzConfig(field, req)

    ctx.status(200)
  }

  private fun listHttpApi(ctx: Context) {
    val mappings = HttpServerManager.listMappings().map {
      HttpRequestMappingDigest(it.method.name, it.path)
    } + ScriptHttpServer.handlers.map {
      HttpRequestMappingDigest(it.method, it.path)
    }
    ctx.json(mappings)
  }

  private fun getI18NUiStr(ctx: Context) {
    ctx.result(I18N.mustGetExtraUiDictStr())
  }

  private fun reloadI18N(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw Error403("权限不够，需要管理员")

    I18N.load()
  }

  private fun setSysEmc(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw Error403("权限不够，需要管理员")

    val req: SetSysEmcReq = ctx.getReqBody()
    if (req.enabled) {
      SysEmc.enableSysEmc()
    } else {
      SysEmc.disableSysEmc()
    }

    ctx.status(200)
  }

  data class SetSysEmcReq(val enabled: Boolean)
}

data class HttpRequestMappingDigest(val method: String, val path: String)