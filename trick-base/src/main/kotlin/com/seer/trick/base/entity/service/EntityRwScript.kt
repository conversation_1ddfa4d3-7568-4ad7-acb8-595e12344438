package com.seer.trick.base.entity.service

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.ComplexQuery
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.script.ScriptCenter
import com.seer.trick.base.script.ScriptExeRequest
import com.seer.trick.base.script.ScriptTraceContext
import com.seer.trick.helper.JsonHelper

object EntityRwScript {
  
  fun findOne(entityName: String, query: ComplexQuery, o: FindOptions? = null): EntityValue? {
    val r: ScriptFindOneResult = ScriptCenter.execute(
      ScriptExeRequest(
        "entityFindOne",
        arrayOf(ScriptTraceContext(), entityName, JsonHelper.mapper.writeValueAsString(query), o),
      ),
      jacksonTypeRef(),
    ) ?: throw BzError("errScriptEntityRw", "No return value")
    if (r.ok) {
      return r.entityValue
    } else {
      throw BzError("errScriptEntityRw", r.errorMsg)
    }
  }
  
  fun findMany(entityName: String, query: ComplexQuery, o: FindOptions? = null): List<EntityValue> {
    val r: ScriptFindManyResult = ScriptCenter.execute(
      ScriptExeRequest(
        "entityFindMany",
        arrayOf(ScriptTraceContext(), entityName, JsonHelper.mapper.writeValueAsString(query), o),
      ),
      jacksonTypeRef(),
    ) ?: throw BzError("errScriptEntityRw", "No return value")
    if (r.ok) {
      return r.entityValues
    } else {
      throw BzError("errScriptEntityRw", r.errorMsg)
    }
  }
  
  fun findPage(
    
    entityName: String,
    query: ComplexQuery,
    pageNo: Int,
    pageSize: Int,
    o: FindOptions? = null,
  ): ScriptFindPageResult {
    val r: ScriptFindPageResult = ScriptCenter.execute(
      ScriptExeRequest(
        "entityFindPage",
        arrayOf(ScriptTraceContext(), entityName, JsonHelper.mapper.writeValueAsString(query), pageNo, pageSize, o),
      ),
      jacksonTypeRef(),
    ) ?: throw BzError("errScriptEntityRw", "No return value")
    if (r.ok) {
      return r
    } else {
      throw BzError("errScriptEntityRw", r.errorMsg)
    }
  }
}

data class ScriptFindOneResult(
  val ok: Boolean = false,
  val errorMsg: String? = null,
  val entityValue: EntityValue? = null,
)

data class ScriptFindManyResult(
  val ok: Boolean = false,
  val errorMsg: String? = null,
  val entityValues: List<EntityValue>,
)

data class ScriptFindPageResult(
  val ok: Boolean = false,
  val errorMsg: String? = null,
  val pageNo: Int = 1,
  val pageSize: Int = 20,
  val total: Long = 0,
  val page: List<EntityValue>,
)