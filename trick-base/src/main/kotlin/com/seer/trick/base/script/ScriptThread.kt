package com.seer.trick.base.script

import com.seer.trick.BzError
import org.slf4j.LoggerFactory
import java.util.concurrent.CancellationException
import java.util.concurrent.CopyOnWriteArrayList

object ScriptThread {

  private val logger = LoggerFactory.getLogger(this::class.java)

  val group = ThreadGroup("Script")

  private val threads: MutableList<Thread> = CopyOnWriteArrayList()

  @Synchronized
  fun dispose() {
    logger.info("Dispose threads of scripts. Num=${threads.size}")
    for (t in threads) {
      t.interrupt()
    }
    threads.clear()
  }

  /**
   * 因为 ScriptCenter.execute 必须在新线程，所以只能传函数名，不能传函数。
   */
  fun createThread(name: String, worker: String): Thread {
    val t = Thread(group) {
      try {
        ScriptCenter.execute(ScriptExeRequest(worker))
      } catch (e: Throwable) {
        if (e is InterruptedException || e is CancellationException) {
          logger.error("Script thread '$name': function '$worker' interrupted")
        } else if (e is BzError) {
          logger.error("Script thread  '$name' function '$worker', bz error: ${e.message}")
        } else {
          logger.error("Script thread  '$name' function '$worker', other error: ${e.javaClass}", e)
        }
      } finally {
        threads.remove(Thread.currentThread())
      }
    }
    threads += t
    t.name = name
    t.start()
    return t
  }

  fun sleep(millis: Long) {
    Thread.sleep(millis)
  }

  fun interrupted(): Boolean = Thread.interrupted()
}