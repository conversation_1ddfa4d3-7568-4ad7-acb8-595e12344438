package com.seer.trick.base.script

import com.seer.trick.helper.*
import org.apache.commons.lang3.StringUtils
import java.util.*

object ScriptUtils {
  
  fun isNullOrBlank(str: String?): Boolean {
    return str.isNullOrBlank()
  }
  
  fun substringAfter(str: String, sep: String): String {
    return StringUtils.substringAfter(str, sep)
  }
  
  fun substringBefore(str: String, sep: String): String {
    return StringUtils.substringBefore(str, sep)
  }
  
  fun splitTrim(str: String?, sep: String) = StringHelper.splitTrim(str, sep)
  
  fun anyToInt(v: Any?) = NumHelper.anyToInt(v)
  
  fun anyToLong(v: Any?) = NumHelper.anyToLong(v)
  
  fun anyToFloat(v: Any?) = NumHelper.anyToFloat(v)
  
  fun anyToDouble(v: Any?) = NumHelper.anyToLong(v)
  
  fun anyToBool(a: Any?) = BoolHelper.anyToBool(a)
  
  fun anyToDate(input: Any?) = DateHelper.anyToDate(input)
  
  fun formatDate(d: Date?, format: String) = DateHelper.formatDate(d, format)
  
  fun uuidStr() = IdHelper.uuidStr()
  
  fun oidStr() = IdHelper.oidStr()
  
}