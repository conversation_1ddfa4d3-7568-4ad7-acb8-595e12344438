package com.seer.trick.base.http.handler

import com.seer.trick.BzError
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.noAuth
import com.seer.trick.base.http.HttpServerManager.setUserSessionCookies
import com.seer.trick.base.user.wwx.WwxOAuthManager
import io.javalin.http.Context

object WwxHandler {
  
  fun registerHandlers() {
    val c = Handlers("api/wwx")
    c.get("config-ids", ::getConfigIds, noAuth())
    c.get("auth-callback", ::callbackAuth, noAuth())
  }
  
  private fun getConfigIds(ctx: Context) {
    val wwx = WwxOAuthManager.readConfig()
    if (!wwx.wwxEnabled) throw BzError("errWwxNotEnabled")
    ctx.json(mapOf("corpId" to wwx.wwxCorpId, "agentId" to wwx.wwxAgent))
  }

  private fun callbackAuth(ctx: Context) {
    val code = ctx.queryParam("code")
    if (code.isNullOrBlank()) throw BzError("errWwxNoCode")
    
    
    val wwx = WwxOAuthManager.readConfig()
    if (!wwx.wwxEnabled) throw BzError("errWwxNotEnabled")
    
    val userSession = WwxOAuthManager.signIn(code)
    
    setUserSessionCookies(ctx, userSession.userId, userSession.userToken)
    
    ctx.redirect("/")
  }
  
}