package com.seer.trick.base.entity.service

import com.seer.trick.BzError
import com.seer.trick.ComplexQuery
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.executor.EntityExecutor

/**
 * 统计相关
 */
object EntityStatsService {

  /**
   * 聚合查询，支持 SUM、AVG、MAX、MIN、COUNT 聚合函数
   */
  fun aggregateQuery(
    
    entityName: String,
    query: ComplexQuery,
    o: AggregationOptions,
  ): List<EntityValue> {
    val em = BaseCenter.mustGetEntityMeta(entityName)
    EntityValueCleaner.cleanComplexQuery(em, query)
    return EntityExecutor.aggregateQuery(em, query, o)
  }
}

/**
 * TODO 先不考虑子查询
 */
data class AggregationOptions(
  @JvmField
  val fields: List<AdvancedField>, // 支持普通字段查询、聚合操作、四则运算
  @JvmField
  val sort: List<String> = emptyList(), // 排序字段
  @JvmField
  val groupBy: List<GroupByField> = emptyList(), // 分组字段
)

enum class ArithmeticOperator(val value: String) {
  Add("+"),
  Subtract("-"),
  Multiply("*"),
  Divide("/"),
}

/**
 * 支持简单查询、聚合操作、四则运算（便于统计xx率，不支持数值类型作为左操作数）
 *
 */
data class AdvancedField(
  @JvmField
  val function: AggFun? = null, // 使用的聚合函数
  @JvmField
  val name: String, // 字段名
  @JvmField
  val alias: String? = null, // 字段别名，统一取别名，别名不填则取字段名
) {
  // 可以是数值类型、字段名、AdvancedField
  val rightFields: MutableList<Any> = mutableListOf()
  val operators: MutableList<ArithmeticOperator> = mutableListOf() // 运算符

  fun add(right: Any): AdvancedField {
    if (right !is Number && right !is String && right !is AdvancedField) {
      throw BzError("errUnsupportedDataType", right::class.simpleName)
    }
    this.rightFields += right
    this.operators += ArithmeticOperator.Add
    return this
  }

  fun subtract(right: Any): AdvancedField {
    if (right !is Number && right !is String && right !is AdvancedField) {
      throw BzError("errUnsupportedDataType", right::class.simpleName)
    }
    this.rightFields += right
    this.operators += ArithmeticOperator.Subtract
    return this
  }

  fun multiply(right: Any): AdvancedField {
    if (right !is Number && right !is String && right !is AdvancedField) {
      throw BzError("errUnsupportedDataType", right::class.simpleName)
    }
    this.rightFields += right
    this.operators += ArithmeticOperator.Multiply

    return this
  }

  fun divide(right: Any): AdvancedField {
    if (right !is Number && right !is String && right !is AdvancedField) {
      throw BzError("errUnsupportedDataType", right::class.simpleName)
    }
    this.rightFields += right
    this.operators += ArithmeticOperator.Divide
    return this
  }
}

/**
 * 支持多种时间类型进行统计
 * 统一取别名，别名不填则取字段名
 */
data class GroupByField(
  @JvmField
  val name: String, // 字段名
  @JvmField
  val alias: String? = null, // 统一取别名，别名不填则取字段名
  @JvmField
  val statisticDateType: StatisticDateType?, // 时间类型的字段才需要填写
)

// TODO 优化写法
enum class StatisticDateType(
  val sqlServerDateFormat: String,
  val mySqlDateFormat: String,
  val derbyDateFormat: String,
  val damengDateFormat: String,
  val mongoDateFormat: String,
) {
  Hour(
    "FORMAT(%s, 'yyyy-MM-dd HH')",
    "DATE_FORMAT(%s, '%%Y-%%m-%%d %%H')",
    "SUBSTR(CAST(\"%s\" AS VARCHAR(30)), 1, 13)",
    "TO_CHAR(%s, 'yyyy-MM-dd HH24')",
    "%Y-%m-%d %H",
  ),
  Day(
    "FORMAT(%s, 'yyyy-MM-dd')",
    "DATE_FORMAT(%s, '%%Y-%%m-%%d')",
    "SUBSTR(CAST(\"%s\" AS VARCHAR(30)), 1, 10)",
    "TO_CHAR(%s, 'yyyy-MM-dd')",
    "%Y-%m-%d",
  ),

  Week(
    "",
    "",
    "",
    "",
    "",
  ),
  Month(
    "FORMAT(%s, 'yyyy-MM')",
    "DATE_FORMAT(%s, '%%Y-%%m')",
    "SUBSTR(CAST(\"%s\" AS VARCHAR(30)), 1, 7)",
    "TO_CHAR(%s, 'yyyy-MM')",
    "%Y-%m",
  ),

  Quarter(
    "",
    "",
    "",
    "",
    "",
  ),
  Year(
    "FORMAT(%s, 'yyyy')",
    "DATE_FORMAT(%s, '%%Y')",
    "SUBSTR(CAST(\"%s\" AS VARCHAR(30)), 1, 4)",
    "TO_CHAR(%s, 'yyyy')",
    "%Y",
  ),
}

enum class AggFun(val value: String) {
  COUNT("COUNT"),
  SUM("SUM"),
  AVG("AVG"),
  MAX("MAX"),
  MIN("MIN"),
}