package com.seer.trick.base.db.sql

import com.seer.trick.base.DbType
import com.seer.trick.base.db.DbManager
import com.seer.trick.base.db.IndexHelper
import com.seer.trick.base.db.sql.SqlHelper.escapeIdentifier
import com.seer.trick.base.db.sql.SqlHelper.executeSql
import com.seer.trick.base.entity.*
import com.seer.trick.helper.getTypeMessage
import org.slf4j.LoggerFactory
import java.sql.SQLException

object SqlIndexManager {

  private val logger = LoggerFactory.getLogger(javaClass)
  
  private const val INDEX_PREFIX = "m4_"

  fun sync(entityMetaMap: Map<String, EntityMeta>) {
    DbManager.getSqlConnection().use { sc: SmartConnection ->
      sc.setAutoCommit(true)
      for (em in entityMetaMap.values) {
        if (em.disabled) continue
        if (em.type != EntityType.Entity) continue
        val oldIndexes: List<EntityIndexDef> = try {
          queryIndexList(em.name, sc)
        } catch (e: Throwable) {
          logger.error("Failed to query indexes for entity ${em.name}", e)
          emptyList()
        }
        val indexes = em.indexes.toMutableList()
        val oldIndexMap: MutableMap<String, EntityIndexDef?> = HashMap()

        processIndexBefore(sc, oldIndexMap, indexes, oldIndexes, em)

        for (indexDef in indexes) {
          try {
            if (!SqlHelper.checkIndexValid(sc.sqlDialect, em, indexDef)) {
              continue
            }
            processIndex(sc, indexDef, em.name, oldIndexMap)
          } catch (e: SQLException) {
            logger.error("Failed to create index ${em.name}:${indexDef.name}: " + e.getTypeMessage())
          } catch (e: Throwable) {
            logger.error("Failed to create index ${em.name}:${indexDef.name}", e)
          }
        }
      }
    }
    logger.info("SQL indexes updated")
  }

  private fun processIndexBefore(
    
    sc: SmartConnection,
    oldIndexMap: MutableMap<String, EntityIndexDef?>,
    indexes: MutableList<EntityIndexDef>,
    oldIndexes: List<EntityIndexDef>,
    em: EntityMeta,
  ) {
    val fieldNames = em.fields.map { it.value.name }
    // 如果没有内置索引且存在该字段，则强制建一个
    if (fieldNames.contains(FieldMeta.FIELD_ID)) {
      val idIndex = EntityIndexDef("ID", true, listOf(EntityIndexField(FieldMeta.FIELD_ID, true)))
      IndexHelper.addIndexIfAbsent(indexes, idIndex)
    }
    if (fieldNames.contains(FieldMeta.FIELD_CREATED_ON)) {
      val createdOnIndex =
        EntityIndexDef("CreatedOn", false, listOf(EntityIndexField(FieldMeta.FIELD_CREATED_ON, false)))
      IndexHelper.addIndexIfAbsent(indexes, createdOnIndex)
    }

    for (old in oldIndexes) {
      val index = indexes.find { it.name == old.name }
      if (index != null) {
        oldIndexMap[old.name] = old
      } else {
        // 删除已经不用的索引
        try {
          dropIndex(sc, em.name, old.name)
        } catch (e: SQLException) {
          logger.error("Failed to delete the index ${em.name}:${old.name}: " + e.getTypeMessage())
        } catch (e: Throwable) {
          logger.error("Failed to delete the index ${em.name}:${old.name}", e)
        }
      }
    }
  }

  private fun queryIndexList(tableName: String, sc: SmartConnection): List<EntityIndexDef> {
    val indexMap = mutableMapOf<String, EntityIndexDef>()
    val connection = sc.connection
    val metaData = connection.metaData
    // unique 为 true 则会过滤出唯一索引，为 false 不进行过滤
    val indexSet = metaData.getIndexInfo(sc.connection.catalog, sc.connection.schema, tableName, false, false)

    while (indexSet.next()) {
      var indexName = indexSet.getString("INDEX_NAME") ?: continue
      if (!indexName.startsWith(INDEX_PREFIX)) continue
      indexName = indexName.replace(INDEX_PREFIX + tableName, "")

      val fieldList = if (indexMap.containsKey(indexName)) {
        indexMap[indexName]!!.fields
      } else {
        // Derby 返回是布尔值
        val unique = indexSet.getString("NON_UNIQUE") == "0" || !indexSet.getBoolean("NON_UNIQUE")
        indexMap[indexName] = EntityIndexDef(indexName, unique, mutableListOf())
        indexMap[indexName]!!.fields
      }

      (fieldList as MutableList).add(
        EntityIndexField(
          indexSet.getString("COLUMN_NAME"),
          indexSet.getString("ASC_OR_DESC") == "D",
        ),
      )
    }

    return indexMap.map { it.value }
  }

  private fun processIndex(
    
    sc: SmartConnection,
    indexDef: EntityIndexDef,
    tableName: String,
    oldIndexMap: MutableMap<String, EntityIndexDef?>,
  ) {
    oldIndexMap[indexDef.name]?.let {
      // 索引已存在且结构严格相同则不处理
      if (it == indexDef) return
      dropIndex(sc, tableName, it.name)
    }
    createIndex(sc, tableName, indexDef)
  }

  private fun dropIndex(sc: SmartConnection, tableName: String, rawIndexName: String) {
    logger.info("Delete index $tableName:$rawIndexName")
    val indexName = INDEX_PREFIX + tableName + rawIndexName
    val sql: String = if (sc.sqlDialect == DbType.MySQL || sc.sqlDialect == DbType.SqlServer) {
      val eTableName = escapeIdentifier(tableName, sc.sqlDialect)
      val eIndexName = escapeIdentifier(indexName, sc.sqlDialect)
      "DROP INDEX $eIndexName ON $eTableName"
    } else if (sc.sqlDialect == DbType.Derby) {
      val eIndexName = escapeIdentifier(indexName, sc.sqlDialect)
      "DROP INDEX $eIndexName"
    } else {
      return
    }
    executeSql(sc, sql)
  }

  private fun createIndex(sc: SmartConnection, tableName: String, indexDef: EntityIndexDef) {
    val indexName = INDEX_PREFIX + tableName + indexDef.name

    logger.info("Create index $tableName: $indexDef")
    val unique = if (indexDef.unique) "UNIQUE " else ""
    val fields = indexDef.fields.joinToString(", ") { it: EntityIndexField ->
      escapeIdentifier(it.name, sc.sqlDialect) + if (it.desc) " DESC" else " ASC"
    }
    val eTableName = escapeIdentifier(tableName, sc.sqlDialect)
    val eIndexName = escapeIdentifier(indexName, sc.sqlDialect)
    val sql = "CREATE ${unique}INDEX $eIndexName ON $eTableName($fields)"
    executeSql(sc, sql)
  }
}