package com.seer.trick.base.entity

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.seer.trick.BzError
import com.seer.trick.ComplexQuery
import org.apache.commons.lang3.StringUtils

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class EntityMeta(
  val name: String = "",
  var label: String = "", // i18n
  var group: String = "",
  var builtin: Boolean = false,
  val disabled: Boolean = false,
  val otherDatasource: Boolean = false,
  val type: EntityType = EntityType.Entity,
  val fields: MutableMap<String, FieldMeta> = HashMap(),
  val indexes: MutableList<EntityIndexDef> = ArrayList(),
  val idGen: IdGenRule? = null,
  val digest: EntityDigestMeta? = null,
  val scale: EntityScale = EntityScale.Instances,
  val disabledFilter: Boolean = false,
  val sort: String = "",
  val commentEnabled: Boolean = false,
  val trash: Boolean = false,
  val history: Boolean = false,
  val trackChange: Boolean = false,
  val userNotice: EntityChangedUserNotice = EntityChangedUserNotice(),
  val onChangeJs: String? = null,
  val actions: EntityPageActions = EntityPageActions(),
  val listTableStyleExt: String? = null,
  val detailsPageName: String? = null, // 替换详情页
  val viewOpenMode: String? = null,
  val opIndex: Boolean? = null, // 首页是操纵（新增）界面
  val listStats: EntityListStats? = null,
  val listCard: EntityListCard = EntityListCard(),
  val orderConfig: OrderConfig? = null,
  val pagesButtons: MutableMap<String, PageButtons> = HashMap(),
  val codeParse: CodeParse = CodeParse(),
  val dateClean: DataClean = DataClean(),
  val kinds: BzKindsConfig? = null,
  var states: BzStatesConfig? = null,
  val editTip: String? = null, // 编辑页面的提示
  val indexTip: String? = null, // 首页提示
  val quickInput: QuickInput? = null, // 快速录入，null 或者 enabled 为 false 则是未启用
  val notMenu: Boolean = false, // 是否作为菜单项；内部业务对象不会被作为菜单项显示在配置菜单的界面上。
  val menuIcon: String? = null, // 菜单图标，存图标的存放路径。
  val menuColor: String? = null, // 菜单颜色，或者说是菜单图标的颜色。
  val modifyProhibition: ModifyProhibition? = null, // 禁止修改的条件
  val removeProhibition: RemoveProhibition? = null, // 禁止删除的条件
) {

  fun checkIdExisted() {
    if (!fields.containsKey("id")) throw BzError("errMissingIdField", name, label)
  }
}

enum class EntityType {
  Entity,
  Component,
  Extension,
  Abstract,
}

data class EntityDigestMeta(val fields: List<String>? = null, val formatJs: String? = null)

enum class EntityScale {
  Instances,
  Hierarchical,
  Singleton, // 注意大小写改变了
}

data class IdGenRule(val enabled: Boolean = false, val fixedPrefix: String? = null, var flowNoWidth: Int = 4)

data class EntityChangedUserNotice(
  val create: Boolean = false,
  val update: Boolean = false,
  val delete: Boolean = false,
  val comment: Boolean = false,
  val targetUserFields: List<String>? = null, // 从实体的字段确定要通知的用户
)

data class EntityPageActions(
  val createDisabled: Boolean = false,
  val updateDisabled: Boolean = false,
  val batchUpdateDisabled: Boolean = false,
  var removeDisabled: Boolean = true, // 默认不让删除，避免现场 xjb 操作，把数据全删了破坏掉关键数据。
  val exportDisabled: Boolean = false,
  val importDisabled: Boolean = false,
)

data class EntityListStats(val items: List<EntityListStatsItem> = ArrayList())

data class EntityListStatsItem(
  val disabled: Boolean = false,
  var label: String = "", // i18n
  val type: String = "",
  val filter: ComplexQuery? = null,
  val sumField: String? = null,
  val scriptStr: String? = null,
  val valueColor: String? = null,
)

data class EntityListCard(
  val imageFieldName: String? = null,
  // val stateFieldName: String? = null,
  val lines: List<List<ListCardLinePart>> = ArrayList(),
)

data class ListCardLinePart(
  val type: ListCardLinePartType = ListCardLinePartType.Simple,
  val fieldName: String = "",
  var prefix: String = "", // i18n
  var suffix: String = "", // i18n
  val prefixPaddingRight: Double? = null,
  val suffixPaddingLeft: Double? = null,
  val marginRight: Double? = null,
  val textColor: String = "",
  val bgColor: String = "",
  val tag: Boolean = false,
  val tagStyle: String = "",
  val cssStr: String = "",
  val className: String = "",
  val replaceText: String = "", // i18n
  val alignRight: Boolean = false,
  val hideIfBlankFalse: Boolean = false,
  val formatMapping: List<PartFormatMapping> = ArrayList(),
)

enum class ListCardLinePartType {
  Simple,
  Conditional,
}

data class PartFormatMapping(
  val operator: PartFormatMappingOp = PartFormatMappingOp.Eq,
  val value: String = "",
  val textColor: String = "",
  val bgColor: String = "",
  val tag: Boolean = false,
  val tagStyle: String = "",
  val notShow: Boolean = false,
  val cssStr: String = "",
  val className: String = "",
  var replaceText: String = "", // i18n
  val alignRight: Boolean = false,
)

enum class PartFormatMappingOp {
  Eq,
  Other,
}

data class OrderConfig(
  val enabled: Boolean = false,
  val states: List<BzState> = emptyList(),
  //
  val pushOrderStates: List<String> = emptyList(), // 上游单据允许下推时的状态
  val pushOrders: List<PushOrderConfigItem> = emptyList(),
  //
  val upOrderConfig: UpOrderConfig = UpOrderConfig(),
  //
  // val lineEntityName: String = "",
  //
  val thisQtyFields: List<String> = emptyList(), // 本次发生数量字段列表
  val occurredQtyField: String = "", // 已发生数量字段
  val planQtyField: String = "", // 期望数量字段
  //
  val toCreateInv: Boolean = false, // 创建库存
  val orderStatesToCreateInv: List<String> = emptyList(), // 创建库存时的单据状态
  val toCreateInvState: String? = null, // 库存状态
  //
  val toAssignInv: Boolean = false, // 分配库存
  val outboundInvAssignState: String = "", // 进行库存分配时出库单的状态
  val outboundInvStates: List<String> = emptyList(), // 能够用于出库的库存状态
  //
  val invRef: Boolean = false, // 是否显示参考库存
  val invRefWarehouseHeadField: String = "", // 库存筛选，仓库单头字段
  val invRefWarehouseLineField: String = "", // 库存筛选，仓库单行字段
  val invRefDistrictHeadField: String = "", // 库存筛选，库区单头字段
  val invRefDistrictLineField: String = "", // 库存筛选，库区单行字段
)

data class BzStatesConfig(val enabled: Boolean = false, val states: Map<String, BzState> = emptyMap())

data class BzState(
  val id: String = "",
  var label: String = "",
  val displayOrder: Int? = null,
  val entityEditable: Boolean = false,
  val finalState: Boolean = false,
  val color: String = "",
  val nextStates: List<NextState> = emptyList(),
)

data class NextState(
  val id: String = "",
  var buttonLabel: String = "", // i18n
  val buttonKind: String = "",
  val reasonRequired: Boolean = false,
)

data class PushOrderConfigItem(
  val downOrderName: String = "", // 下游单据实体
  val downOrderKind: String = "", // 下游单据类型
  val downOrderState: String = "", // 下推后的单据类型
  val headFieldMapping: List<OrderFieldMapping> = emptyList(), // 单头字段映射
  val lineFieldMapping: List<OrderFieldMapping> = emptyList(), // 单行字段映射
  val downOrderBackStates: List<String> = emptyList(), // 下游单据反写时的状态
  val lineWriteBackMapping: List<WriteBackFieldMapping> = emptyList(), // 反写映射
)

data class UpOrderConfig(val items: List<UpOrderConfigItem> = emptyList())

data class UpOrderConfigItem(
  val upOrderName: String = "", // 上游单据实体
  val upOrderStates: List<String> = emptyList(), // 能拉取的上游单据状态
  val headFieldMapping: List<OrderFieldMapping> = emptyList(), // 单头字段映射
  val lineFieldMapping: List<OrderFieldMapping> = emptyList(), // 单行字段映射
  val writeBackStates: List<String> = emptyList(), // 下游单据反写时的状态
  val lineWriteBackMapping: List<WriteBackFieldMapping> = emptyList(), // 反写映射
)

data class OrderFieldMapping(val sourceField: String = "", val targetField: String = "")

data class WriteBackFieldMapping(val downField: String = "", val upField: String = "")

data class EntityIndexDef(
  val name: String = "",
  val unique: Boolean = false,
  val fields: List<EntityIndexField> = ArrayList(),
) {
  fun describe(): String = (
    (if (unique) "UNIQUE " else "") + fields.joinToString(" ") {
      it.name + if (it.desc) " DESC" else " ASC"
    }
    )

  /**
   * 具有相同字段即认为相同，不管顺序
   */
  fun hasSameFields(other: EntityIndexDef): Boolean {
    val f1 = fields.map { it.name }.sorted()
    val f2 = other.fields.map { it.name }.sorted()
    return f1 == f2
  }
}

data class EntityIndexField(val name: String = "", val desc: Boolean = false)

data class FieldMeta(
  val name: String = "",
  var label: String = "", // i18n
  val type: FieldType = FieldType.String,
  val scale: FieldScale = FieldScale.Single,
  val inlineOptionBill: InlineOptionBill? = null,
  val refEntity: String? = null,
  val disabled: Boolean = false,
  var tip: String? = null, // i18n
  val inputRequired: Boolean = false,
  val fuzzyFilter: Boolean = false,
  val defaultValue: Any? = null,
  val decimals: Int? = null, // 显示小数位数
  val refField: String = "",
  val refFieldField: String = "",
  val copiable: Boolean = false,
  val sumLineField: String = "",
  val computed: String = "",
  // dynamicEnabled?: FieldDynamicEnabled
  val sqlType: FieldSqlType = FieldSqlType.None,
  val length: Int = 0,
  val truncate: Boolean = false,
  val numWidth: Int = 0,
  val numScale: Int = 0, // 存储小数位数
  var view: FieldView = FieldView(),
  val refFilter: ComplexQuery? = null,
  val fileMD5: Boolean = false,
  val listItemNumMin: Int? = null,
  val listItemNumMax: Int? = null,
  val onChangeExt: String? = null,
  val listBatchButtons: ListBatchButtons? = null,
) {

  fun buildFileSizeColumnName(): String = name + "Size"

  fun buildFileNameColumnName(): String = name + "Name"

  fun buildFileMd5ColumnName(): String = name + "MD5"

  // 用于 SQL
  fun buildRelatedTableName(mainTableName: String): String = mainTableName + StringUtils.capitalize(name)

  companion object {
    const val ID_LENGTH = 120

    const val FIELD_ID = "id"
    const val FIELD_VERSION = "version"
    const val FIELD_CREATED_ON = "createdOn"
    const val FIELD_MODIFIED_ON = "modifiedOn"
    const val FIELD_CREATED_BY = "createdBy"
    const val FIELD_MODIFIED_BY = "modifiedBy"
    const val FIELD_FILE_PATH = "path"
    const val FIELD_FILE_SIZE = "size"
    const val FIELD_FILE_NAME = "name"
    const val FIELD_FILE_MD5 = "md5"

    const val FIELD_DISABLED = "btDisabled"

    const val FIELD_PREPARE = "btPrepare"
    const val FIELD_BZ_MARK = "btBzMark"
    const val FIELD_BZ_DESC = "btBzDesc"

    const val FIELD_PARENT_ID = "btParentId"
    const val FIELD_LINE_NO = "btLineNo"
    const val FIELD_LINES = "btLines"

    const val FIELD_SOURCE_ORDER_NAME = "btSourceOrderName"
    const val FIELD_SOURCE_ORDER_ID = "btSourceOrderId"
    const val FIELD_SOURCE_LINE_NO = "btSourceLineNo"

    const val FIELD_SUB_CONTAINER_ID = "subContainerId"

    const val FIELD_LEAF_CONTAINER = "leafContainer"
    const val FIELD_TOP_CONTAINER = "topContainer"
    const val FIELD_TOP_CONTAINER_TYPE = "topContainerType"
    const val FIELD_LEAF_CONTAINER_TYPE = "leafContainerType"

    const val FIELD_BZ_KIND = "btBzKind"

    const val FIELD_ORDER_STATE = "btOrderState"
    const val FIELD_ORDER_STATE_REASON = "btOrderStateReason"

    const val FIELD_HI_LEVEL = "btHiNodeLevel"
    const val FIELD_HI_LEAF_NODE = "btHiLeafNode"
    const val FIELD_HI_ROOT_NODE = "btHiRootNode"
    const val FIELD_HI_PARENT_NODE = "btHiParentNode"

    const val FIELD_MATERIAL = "btMaterial"
    const val FIELD_MATERIAL_ID = "btMaterialId"
    const val FIELD_MATERIAL_NAME = "btMaterialName"
    const val FIELD_MATERIAL_MODEL = "btMaterialModel"
    const val FIELD_MATERIAL_SPEC = "btMaterialSpec"
    const val FIELD_MATERIAL_CATEGORY = "btMaterialCategory"
    const val FIELD_MATERIAL_CATEGORY_NAME = "btMaterialCategoryName"
    const val FIELD_MATERIAL_IMAGE = "btMaterialImage"
    const val FIELD_MATERIAL_TOP_CATEGORY = "btMaterialTopCategory"
    const val FIELD_MATERIAL_TOP_CATEGORY_NAME = "btMaterialTopCategoryName"

    const val FIELD_INV_PROCESSED = "btInvProcessed"

    // public static final String FIELD_REF_ID = "refId"
    // public static final String FIELD_REF_ENTITY = "refEntity"
    const val COLUMN_REF_ID = "_refId"

    // public static final String COLUMN_REF_ENTITY = "_refEntityName"
    const val COLUMN_FILE_PATH = "_filePath"
    const val COLUMN_FILE_SIZE = "_fileSize"
    const val COLUMN_FILE_NAME = "_fileName"
    const val COLUMN_FILE_MD5 = "_fileMD5"
    const val COLUMN_OWNER = FIELD_PARENT_ID
    const val COLUMN_ORDER = FIELD_LINE_NO

    val MaterialFields = listOf(
      FIELD_MATERIAL,
      FIELD_MATERIAL_ID,
      FIELD_MATERIAL_NAME,
      FIELD_MATERIAL_MODEL,
      FIELD_MATERIAL_SPEC,
      FIELD_MATERIAL_CATEGORY,
      FIELD_MATERIAL_CATEGORY_NAME,
      FIELD_MATERIAL_IMAGE,
    )

    const val DEFAULT_DECIMAL_M = 13
    const val DEFAULT_DECIMAL_D = 2
  }
}

enum class FieldType {
  String,
  Boolean,
  Int,
  Long,
  Float,
  Date,
  Time,
  DateTime,
  Reference,
  Component,
  File,
  Image,
  Object,
}

enum class FieldScale {
  Single,
  List,
}

enum class FieldSqlType {
  None,
  Char,
  Varchar,
  Int,
  BigInt,
  Decimal,
  Date,
  Time,
  DateTime,
}

data class InlineOptionBill(val enabled: Boolean = false, val items: List<OptionItem> = emptyList())

data class OptionItem(
  val value: String = "",
  var label: String = "", // i18n
  val color: String = "",
)

data class FieldView(
  val input: String? = "",
  val specialInput: String? = "",
  val specialInputArgs: String? = null,
  val scan: Boolean? = null,
  val read: String = "",
  val create: String = "",
  val update: String = "",
  val displayOrder: Int = 0,
  val block: Boolean = false,
  val lineColumnWidth: Int = 0,
  val listTableDisabled: Boolean = false,
  val listTableColumnWidth: Int = 0,
  val listTableColumnAlign: String? = "",
  val timestampPrecision: String? = null,
  val asTag: Boolean? = null,
  val sortAllowed: Boolean? = null,
  var trueText: String? = null, // i18n
  val trueIcon: String? = null,
  val trueStyle: String? = null,
  var falseText: String? = null, // i18n
  val falseIcon: String? = null,
  val falseStyle: String? = null,
)

data class PageButtons(val extEnabled: Boolean = false, val buttons: List<PageButton> = emptyList())

data class PageButton(
  val type: String = "",
  var label: String = "", // i18n
  val kind: String = "",
  val builtinId: String = "",
  val extId: String? = null,
  val confirmMsg: String? = null, // i18n
  val scriptFunc: String? = null,
  val scriptFuncBefore: String? = null,
  val scriptFuncAfter: String? = null,
  val callTimeout: Long? = null,
  val falconTask: String? = null,
  val falconTaskBefore: String? = null,
  val falconTaskAfter: String? = null,
  val uiSource: String? = null,
  val uiSourceBefore: String? = null,
  val uiSourceAfter: String? = null,
  val preSep: Boolean? = null,
  val enabledCondition: PageButtonEnabledCondition? = null,
  val icon: String? = null // 界面按钮的图标，存放图标的路径。
)

data class PageButtonEnabledCondition(val enabled: Boolean = false, val cq: ComplexQuery? = null)

data class CodeParse(
  val enabled: Boolean = false,
  val rules: List<CodeParseRule> = emptyList(),
  val addRow: Boolean = false,
  val extJs: String? = null,
)

data class CodeParseRule(val demoCode: String = "", val regex: String = "", val mapFields: List<CpField> = emptyList())

data class CpField(val demoText: String = "", val field: String = "", val order: Int? = 0)

data class DataClean(
  val enabled: Boolean = false,
  val type: DataCleanType = DataCleanType.ByDay,
  val keepDays: Int? = null,
)

enum class DataCleanType {
  ByDay,
}

data class ListBatchButtons(val buttons: List<ListBatchButton> = emptyList())

data class ListBatchButton(
  var label: String = "", // i18n
  val value: Any? = null,
  val kind: String? = null,
  val displayOrder: Int = 0,
)

data class BzKindsConfig(var enabled: Boolean = false, var kinds: Map<String, BzKind> = emptyMap())

data class BzKind(
  var name: String = "",
  var label: String = "",
  val color: String? = null,
  val displayOrder: Int = 0,
  val fields: Map<String, BzKindField> = emptyMap(),
)

data class BzKindField(
  val disabled: Boolean? = null,
  val inputRequired: Boolean? = null,
  val refFilter: ComplexQuery? = null,
)

data class QuickInput(
  val enabled: Boolean = false, // 是否启用
  val autoCommit: Boolean = false, // 是否自动提交，用于在最后一个录入项后回车，直接触发提交
  val items: List<QuickInputItem> = emptyList(), // 快速录入输入框列表
)

data class QuickInputItem(
  val type: QuickInputType = QuickInputType.Mapping, // 配置的类型，默认为 “直接映射字段”
  val mappingField: String? = null, // 映射字段，当 type 为 Mapping 时，必填
  val rules: List<CodeParseRule>? = emptyList(), // 条码解析规则，当 type 为 Parsing 时，必填
  val value: String? = null, // 输入框值
  val label: String = "", // 输入框标签
  val remain: Boolean = false, // 是否保持
  val required: Boolean = false, // 是否必填
  val disabled: Boolean = false, // 是否禁用 录入项
  val order: Int = 0, // 排序
  val enabledScan: Boolean, // 是否启动扫码
  val extJs: String? = null, // 扩展代码
)

enum class QuickInputType {
  Mapping, // 直接映射字段
  Parsing, // 解析录入
  ExtScript, // 扩展代码解析
}

enum class ProhibitionType {
  Query,
  Script,
}

data class ModifyProhibition(
  val enabled: Boolean = false, // 是否启用
  val type: ProhibitionType = ProhibitionType.Query,
  val filter: ComplexQuery? = null, // 禁止修改的筛选条件
  val extFunction: String? = null, // 脚本函数名
  val sourceLang: String? = null, // python, js
  val source: String? = null, // 源代码
  val alertMsg: String? = null, // 确认消息。被禁止修改时抛出
)

data class RemoveProhibition(
  val enabled: Boolean = false, // 是否启用
  val type: ProhibitionType = ProhibitionType.Query,
  val filter: ComplexQuery? = null, // 禁止删除的筛选条件
  val extFunction: String? = null, // 脚本函数名
  val sourceLang: String? = null, // python, js
  val source: String? = null, // 源代码
  val alertMsg: String? = null, // 确认消息。被禁止删除时抛出
)