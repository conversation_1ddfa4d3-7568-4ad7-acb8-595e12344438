package com.seer.trick.base.config

import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.event.EventBus
import com.seer.trick.helper.NumHelper
import org.apache.commons.lang3.math.NumberUtils

object BzConfigManager {

  val eventBus = EventBus<String>()

  fun getByPathAsBoolean(vararg path: String): Boolean {
    return getByPath(*path) as Boolean? ?: false
  }

  fun getByPathAsInt(vararg path: String): Int? {
    return NumHelper.anyToInt(getByPath(*path))
  }

  fun getByPathAsLong(vararg path: String): Long? {
    return NumHelper.anyToLong(getByPath(*path))
  }

  fun getByPathAsString(vararg path: String): String? {
    return getByPath(*path) as String?
  }

  @Suppress("UNCHECKED_CAST")
  fun getByPath(vararg path: String): Any? {
    if (path.isEmpty()) {
      return BaseCenter.bzConfig
    } else if (path.size == 1) {
      return BaseCenter.bzConfig[path[0]]
    } else {
      var ev = BaseCenter.bzConfig[path[0]]
      for (i in 1 until path.size) {
        if (ev == null) return null
        val key = path[i]
        if (NumberUtils.isDigits(key)) {
          // 数组处理
          val index = key.toInt()
          ev = ev as List<*>
          ev = ev.getOrNull(index)
        } else {
          ev = ev as EntityValue
          ev = ev[key]
        }
      }
      return ev
    }
  }

}