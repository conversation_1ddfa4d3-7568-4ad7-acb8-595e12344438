package com.seer.trick.base.http.handler

import com.seer.trick.base.http.*
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.client.AsyncHttpCallService
import io.javalin.http.Context

object ExtCallHttpHandler {

  fun registerHandlers() {
    val c = Handlers("api/external-call")
    c.post("cancel", ::cancelAsyncHttp, auth())
    c.post("retry", ::retryAsyncHttp, auth())

  }

  private fun cancelAsyncHttp(ctx: Context) {
    

    val req: ExtCallHttpHandlerCancelReq = ctx.getReqBody()

    for (recordId in req.asyncRecordIds) {
      AsyncHttpCallService.cancel(recordId)
    }

    ctx.status(200)
  }

  private fun retryAsyncHttp(ctx: Context) {
    

    val req: ExtCallHttpHandlerRetryReq = ctx.getReqBody()

    for (recordId in req.asyncRecordIds) {
      AsyncHttpCallService.retry(recordId)
    }

    ctx.status(200)
  }


  data class ExtCallHttpHandlerCancelReq(
    val asyncRecordIds: List<String>
  )

  data class ExtCallHttpHandlerRetryReq(
    val asyncRecordIds: List<String>
  )
}


