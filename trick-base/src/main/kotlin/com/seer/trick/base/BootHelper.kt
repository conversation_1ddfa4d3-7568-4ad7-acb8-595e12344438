package com.seer.trick.base

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.I18N
import com.seer.trick.base.aac.AacService
import com.seer.trick.base.db.DbBackupManager
import com.seer.trick.base.db.DbManager
import com.seer.trick.base.entity.EntityMetaManager
import com.seer.trick.base.entity.clean.EntityCleanService
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.http.HttpServerManager
import com.seer.trick.base.http.WebSocketManager
import com.seer.trick.base.http.client.AsyncHttpCallService
import com.seer.trick.base.log.SystemKeyEvent
import com.seer.trick.base.log.SystemKeyEventService
import com.seer.trick.base.script.ScriptCenter
import com.seer.trick.base.script.ScriptRobustExecutor
import com.seer.trick.base.soc.SysMonitorService
import com.seer.trick.base.stats.StatsService
import com.seer.trick.base.user.UserService
import com.seer.trick.helper.JsonHelper
import org.apache.commons.io.FileUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.io.File
import java.nio.charset.StandardCharsets
import kotlin.system.exitProcess

object BootHelper {
  
  private var logger: Logger? = null
  
  init {
    println("========================\nM4 booter\n==================")
    
    Thread.setDefaultUncaughtExceptionHandler { _: Thread, e: Throwable? ->
      logger?.error("Global uncaught exception", e)
    }
    
    Runtime.getRuntime().addShutdownHook(Thread(::dispose))
  }
  
  fun parseJsonConfigFileFromArgs0(args: Array<String>): BaseConfig {
    if (args.isEmpty()) {
      println("Missing args")
      exitProcess(-1)
    }
    
    val projectDir = File(args[0])
    if (!projectDir.exists()) {
      println("Project directory not existed")
      exitProcess(-1)
    }
    
    val configFile = File(projectDir, "config/config.json")
    val bootConfig: BootConfig = if (configFile.exists()) {
      JsonHelper.mapper.readValue(
        FileUtils.readFileToString(configFile, StandardCharsets.UTF_8),
        jacksonTypeRef(),
      )
    } else {
      BootConfig()
    }
    
    System.setProperty("logPath", projectDir.absolutePath)
    logger = LoggerFactory.getLogger(javaClass)
    
    BaseCenter.baseConfig = BaseConfig(
      projectDir,
      port = bootConfig.port,
      db = bootConfig.db,
      noServerFileService = bootConfig.noServerFileService,
      single = bootConfig.single,
      anonymouseUser = bootConfig.anonymouseUser,
      insideController = bootConfig.insideController,
      noScript = bootConfig.noScript,
      httpsConfig = bootConfig.httpsConfig,
      disabledModules = bootConfig.disabledModules,
    )
    
    SysMonitorService.log(
      subject = "Sys",
      target = "Sys",
      field = "boot args",
      value = JsonHelper.writeValueAsString(BaseCenter.baseConfig),
    )
    SysMonitorService.log(
      subject = "Sys",
      target = "Sys",
      field = "boot args",
      value = "Work directory - ${projectDir.absolutePath}",
    )
    
    return BaseCenter.baseConfig
  }
  
  /**
   * 启动系统。
   * 加同步防止启动和销毁并发。
   */
  @Synchronized
  fun boot(version: String?, modules: List<AppModule>, noAac: Boolean = false) {
    
    
    BaseCenter.version = version
    SysMonitorService.log(
      
      subject = "Sys",
      target = "Sys",
      field = "boot",
      value = "M4 starting, version=$version **********",
    )
    
    BaseCenter.initConfig()
    
    if (!(noAac || insideController())) {
      val licenseFile = File(BaseCenter.baseConfig.projectDir, AacService.LICENSE_FILENAME)
      if (!AacService.checkLicense(licenseFile)) {
        return
      }
      logger?.info("License OK")
    }
    
    BaseCenter.modules.addAll(modules)
    
    I18N.load()
    
    for (m in modules) {
      m.init()
      m.registerHttpHandlers()
      m.registerStatsChart()
    }
    
    EntityMetaManager.loadMeta()
    
    EntityRwService.cache.init()
    
    for (m in modules) {
      m.registerMoreBp() // 这个位置不太对 TODO
    }
    
    // 启动数据库
    logger?.info("Boot database...")
    DbManager.init(BaseCenter.baseConfig)
    logger?.info("Database ready")
    
    UserService.createAdminIfNot(userId = "__admin__", username = "admin", password = "admin")
    
    for (m in modules) {
      m.afterDb()
    }
    
    for (m in modules) {
      m.beforeScript()
    }
    
    ScriptCenter.init()
    
    ScriptRobustExecutor.init()
    
    for (m in modules) {
      m.afterScript()
    }
    
    EntityCleanService.init()
    
    HttpServerManager.startHttpServer(BaseCenter.baseConfig.port)
    
    StatsService.init()
    
    for (m in modules) {
      m.afterHttp()
    }
    
    DbBackupManager.init()
    
    AsyncHttpCallService.init()
    
    if (BaseCenter.baseConfig.anonymouseUser) {
      logger?.info("Anonymouse user enabled!!!")
    }
    
    SysMonitorService.log(subject = "Sys", target = "Sys", field = "boot", value = "M4 up <<<<<<<<")
    SystemKeyEventService.record(SystemKeyEvent(group = "Base", title = "系统启动完成"))
  }
  
  private fun insideController(): Boolean {
    if (!BaseCenter.baseConfig.insideController) return false
    return (
      File("/opt/data/rbk/lib/libfoundation.so").exists() ||
        File("/usr/local/SeerRobotics/rbk/lib/libfoundation.so").exists()
      ) &&
      (
        File("/opt/data/rbk/lib/libchasis.so").exists() ||
          File("/usr/local/SeerRobotics/rbk/lib/libchasis.so").exists()
        )
  }
  
  /**
   * 系统销毁。
   * 加同步防止启动和销毁并发。
   */
  @Synchronized
  private fun dispose() {
    println("==================\nM4 shutdown\n==================")
    
    logger?.info("M4 shutdown ========================================================================")
    
    
    SysMonitorService.log(subject = "Sys", target = "Sys", field = "shutdown", value = "M4 shutdown <<<<<<<<<")
    
    SystemKeyEventService.record(SystemKeyEvent(group = "Base", title = "进程收到关闭信号"))
    
    WebSocketManager.dispose()
    
    HttpServerManager.stop()
    
    // QuartzExecutor.endQuartz()
    for (m in BaseCenter.modules) {
      try {
        m.dispose()
      } catch (e: Exception) {
        logger?.error("Error on disposing module '${m.javaClass.simpleName}'", e)
      }
    }
    
    ScriptCenter.dispose()
    
    ScriptRobustExecutor.dispose()
    
    EntityRwService.cache.dispose()
    
    DbBackupManager.dispose()
    
    DbManager.dispose()
    
    logger?.info("All disposed!!!")
  }
}