package com.seer.trick.base.net.tcp

import com.seer.trick.BzError
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.helper.getTypeMessage
import okio.IOException
import org.slf4j.LoggerFactory
import java.io.InputStream
import java.net.InetSocketAddress
import java.net.Socket
import java.nio.ByteBuffer

/**
 * 只支持请求响应模型。
 * 单工串行：发送请求、接受响应，在收到响应前不会发下一个请求。
 * T 是消息的类型
 */
class ReqResTcpClient<T : Any>(
  private val host: String,
  private val port: Int,
  private val msgEncoder: MsgEncoder<T>,
  private val msgDecoder: MsgDecoder<T>,
  // private val ssl: Boolean,
  logPrefix: String,
  private val readTimeout: Int = 0, // 读取超时，0 表示不超时，单位毫秒
) {

  private val logger = LoggerFactory.getLogger(javaClass)

  private val loggerHead: String = "$logPrefix$host:$port "

  private var socket: Socket? = null

  @Volatile
  private var disposed = false

  fun dispose() {
    logger.info(loggerHead + "关闭 Socket")

    disposed = true

    try {
      socket?.close()
    } catch (_: Exception) {
      // ignore
    }
  }

  /**
   * 阻塞式请求响应
   */
  @Synchronized
  fun request(
    
    reqMsg: T,
    connectTimeout: Int = CONNECT_TIMEOUT,
    connectMaxTry: Int = CONNECT_MAX_TRY,
    connectRetryDelay: Long = CONNECT_RETRY_DELAY,
    reqMaxTry: Int = 1,
    reqRetryDelay: Long = 1000,
    resTimeout: Long = -1, // TODO
  ): T {
    var doReqMaxTry = reqMaxTry
    if (doReqMaxTry <= 0) doReqMaxTry = 1

    var lastException: IOException? = null

    for (r in 1..doReqMaxTry) {
      try {
        if (disposed) {
          logger.error(loggerHead + "disposed")
          break
        }
        return doRequest(reqMsg, connectTimeout, connectMaxTry, connectRetryDelay)
          ?: throw BzError("errCodeErr", loggerHead + "disposed")
      } catch (e: IOException) {
        // 只捕获 IOException
        logger.error(
          loggerHead + "请求响应失败，重试 $r/$doReqMaxTry。" +
            "读超时时间：$readTimeout,连接超时时间：$connectTimeout 会关闭原来的 Socket 新建。具体原因" + e.getTypeMessage(),
        )

        lastException = e
        try {
          socket?.close()
        } catch (e: Exception) {
          //
        }
        Thread.sleep(reqRetryDelay)
      }
    }
    throw IOException("Failed to request", lastException)
  }

  /**
   * 阻塞式请求响应。
   * 只有 disposed 会返回 null
   */
  @Synchronized
  private fun doRequest(
    
    reqMsg: T,
    connectTimeout: Int = CONNECT_TIMEOUT,
    connectMaxTry: Int = CONNECT_MAX_TRY,
    connectRetryDelay: Long = CONNECT_RETRY_DELAY,
    resTimeout: Long = -1, // TODO
  ): T? {
    val socket = ensureSocket(connectTimeout, connectMaxTry, connectRetryDelay)

    val reqBuf = msgEncoder.encode(reqMsg)
    write(reqBuf, socket)

    val inputStream = socket.getInputStream()

    // 在这个对象里控制重复读
    while (!disposed) {
      val resMsg = msgDecoder.decode(inputStream)
      if (resMsg != null) return resMsg
    }
    return null
  }

  @Synchronized
  private fun write(buf: ByteBuffer, socket: Socket): Socket {
    val log = BzConfigManager.getByPath("ScDev", "logTcpWrite") == true
    if (log) logger.debug(loggerHead + "写 TCP 开始")

    if (buf.hasRemaining()) {
      val outputStream = socket.getOutputStream()
      outputStream.write(buf.array(), buf.position() + buf.arrayOffset(), buf.remaining())
      outputStream.flush()
    }
    buf.position(buf.limit())

    if (log) logger.debug(loggerHead + "写 TCP 结束")

    return socket
  }

  @Synchronized
  private fun ensureSocket(
    
    connectTimeout: Int,
    connectMaxTry: Int,
    connectRetryDelay: Long,
  ): Socket {
    var socket = socket
    if (socket != null && socket.isConnected && !socket.isClosed) return socket

    var doMaxTry = connectMaxTry
    if (doMaxTry <= 0) doMaxTry = Int.MAX_VALUE

    var lastException: IOException? = null

    for (i in 1..doMaxTry) {
      if (disposed) throw BzError("errCodeErr", loggerHead + "disposed")
      socket = Socket()
      socket.soTimeout = readTimeout
      // 阻塞式连接，带超时
      try {
        socket.connect(InetSocketAddress(host, port), connectTimeout)
        this.socket = socket
        logger.info(loggerHead + "连接成功")
        return socket
      } catch (e: IOException) {
        logger.error(loggerHead + "连接失败，重试 $i/$doMaxTry。" + e.getTypeMessage())
        lastException = e
        Thread.sleep(connectRetryDelay)
      }
    }

    throw IOException("Tcp connection failed for $host:$port", lastException)
  }

  companion object {
    private const val CONNECT_TIMEOUT: Int = 3 * 1000
    private const val CONNECT_MAX_TRY: Int = 3
    private const val CONNECT_RETRY_DELAY: Long = 1000
  }
}

interface MsgEncoder<T> {
  fun encode(msg: T): ByteBuffer
}

interface MsgDecoder<T> {
  /**
   * 返回 null 表示未成功解析到消息，需要外部继续读
   */
  fun decode(inputStream: InputStream): T?
}