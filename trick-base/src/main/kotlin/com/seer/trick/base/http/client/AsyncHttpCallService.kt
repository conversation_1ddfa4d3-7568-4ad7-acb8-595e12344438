package com.seer.trick.base.http.client

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.base.concurrent.BaseConcurrentCenter.httpAsyncCallExecutor
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.script.ScriptCenter
import com.seer.trick.base.script.ScriptExeRequest
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.NumHelper
import com.seer.trick.helper.submitCatch
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.ConcurrentHashMap

/**
 * 向第三方发起异步 HTTP 请求
 */
object AsyncHttpCallService {

  private val logger = LoggerFactory.getLogger(this::class.java)

  val internalOkChecker: MutableMap<String, HttpResultOkChecker> = ConcurrentHashMap()

  fun init() {
    val evList = EntityRwService.findMany("ExternalCallRecord", Cq.include("status", listOf("Init", "Failed")))
    if (evList.isEmpty()) return

    logger.info("Unfinished async http call num: " + evList.size)

    for (ev in evList) {
      reload(ev)
    }
  }

  private fun reload(ev: EntityValue) {
    
    try {
      val req: HttpRequest = JsonHelper.mapper.readValue(ev["req"] as String, jacksonTypeRef())
      val okChecker = ev["okChecker"] as String
      val oStr = ev["o"] as String?
      val o: CallRetryOptions? = if (oStr.isNullOrBlank()) null else JsonHelper.mapper.readValue(oStr, jacksonTypeRef())
      val failedNum = NumHelper.anyToInt(ev["failedNum"]) ?: 0

      // 异步调用
      doCallAsync(req, okChecker, o, EntityHelper.mustGetId(ev), failedNum)
    } catch (e: Exception) {
      logger.error("Reload sync http call", e)
    }
  }

  /**
   * 发起第三方调用。
   * 返回记录 ID。
   */
  fun submit(req: HttpRequest, okChecker: String? = null, o: CallRetryOptions? = null): String {
    val record: EntityValue = mutableMapOf(
      "status" to "Init",
      "url" to req.url,
      "req" to JsonHelper.writeValueAsString(req),
      "okChecker" to okChecker,
      "options" to JsonHelper.writeValueAsString(o),
      "failedNum" to 0,
    )

    val id = EntityRwService.createOne("ExternalCallRecord", record)

    doCallAsync(req, okChecker, o, id, 0)

    return id
  }

  private fun doCallAsync(
    
    req: HttpRequest,
    okChecker: String? = null,
    o: CallRetryOptions? = null,
    recordId: String,
    initFailedNum: Int,
  ) {
    httpAsyncCallExecutor.submitCatch("Http async call", logger) {
      doCall(req, okChecker, o, recordId, initFailedNum)
    }
  }

  // 不要直接用，给 doCallAsync
  private fun doCall(
    
    req: HttpRequest,
    okChecker: String? = null,
    o: CallRetryOptions? = null,
    recordId: String,
    initFailedNum: Int,
  ) {
    var failedNum = initFailedNum

    while (true) {
      val latestStatus = loadLatestState(recordId)
      if (latestStatus == null || latestStatus == "Cancelled") {
        // logger.info("最新状态为 $latestStatus，取消异步第三方调用重试：$recordId")
        return
      }

      try {
        val res = HttpClientBase.request(req)

        // TODO check 不 ok，记录 checkMsg
        if (checkResult(okChecker, res)) {
          updateRecordSuccess(recordId, res)
          return
        }

        ++failedNum
        updateRecordFailed(
          
          recordId,
          FailedParams(failedNum, "请求或检查未通过 HttpResult=${JsonHelper.mapper.writeValueAsString(res)}"),
          res,
        )

        if (o != null && failedNum < o.maxRetryNum) { // 注意这里是失败次数不是重试次数
          var delay = o.retryDelay
          if (delay <= 0) delay = 3000L
          Thread.sleep(delay)
        } else {
          updateRecordAborted(recordId)
          return
        }
      } catch (e: IllegalArgumentException) {
        ++failedNum
        updateRecordFailed(recordId, FailedParams(failedNum, "参数异常 ${e.message}"), null)
        return
      } catch (_: InterruptedException) {
        // 不记录，保持上一次。一般是由于关闭程序导致的，不能停止
        return
      } catch (e: Throwable) {
        ++failedNum
        updateRecordFailed(recordId, FailedParams(failedNum, "其它异常 ${e.message}"), null)
        return
      }
    }
  }

  /**
   * 检查结果是否 OK。默认 2xx 都 OK。
   * 脚本函数：传入一个参数 HttpResult，返回 JSON 字符串 CheckResultResult
   */
  private fun checkResult(okChecker: String? = null, res: HttpResult): Boolean {
    if (!res.successful) return false // 不是 200 一定错

    if (okChecker.isNullOrBlank()) return true // 不定制

    // 先看内部
    val ic = internalOkChecker[okChecker]
    if (ic != null) return ic(res)

    // 调用脚本
    val cr: CheckResultResult? = ScriptCenter.execute(ScriptExeRequest(okChecker, arrayOf(res)), jacksonTypeRef())
    return cr?.ok == true
  }

  private fun updateRecordSuccess(recordId: String, res: HttpResult) {
    val update: EntityValue = mutableMapOf(
      "status" to "Done",
      "doneOn" to Date(),
      "resCode" to res.code,
      "resBody" to res.bodyString,
    )
    EntityRwService.updateOne("ExternalCallRecord", Cq.idEq(recordId), update)
  }

  private fun updateRecordFailed(recordId: String, fp: FailedParams, res: HttpResult?) {
    val latestStatus = loadLatestState(recordId)
    if (latestStatus == null || latestStatus == "Cancelled") {
      // logger.info("失败，但最新状态为 $latestStatus，取消异步第三方调用重试：$recordId")
      return
    }

    val update: EntityValue = mutableMapOf(
      "status" to "Failed",
      "failedNum" to fp.failedNum,
      "failedReason" to fp.failedReason,
    )
    if (res != null) {
      update["resCode"] = res.code
      update["resBody"] = res.bodyString
    }
    EntityRwService.updateOne("ExternalCallRecord", Cq.idEq(recordId), update)
  }

  private fun updateRecordAborted(recordId: String) {
    val update: EntityValue = mutableMapOf(
      "status" to "Aborted",
      "doneOn" to Date(),
    )
    EntityRwService.updateOne("ExternalCallRecord", Cq.idEq(recordId), update)
  }

  private fun loadLatestState(recordId: String): String? = EntityRwService.findOne(
    
    "ExternalCallRecord",
    Cq.idEq(recordId),
    FindOptions(projection = listOf("status")),
  )?.get("status") as String?

  /**
   * 用户主动取消
   */
  fun cancel(recordId: String) {
    logger.info("Cancel async http call $recordId")
    val update: EntityValue = mutableMapOf(
      "status" to "Cancelled",
      "doneOn" to Date(),
    )
    EntityRwService.updateOne("ExternalCallRecord", Cq.idEq(recordId), update)
  }

  /**
   * 用户要求重试。只能重试取消、终止的
   */
  fun retry(recordId: String) {
    logger.info("Retry async http call $recordId")
    val latestStatus = loadLatestState(recordId) ?: return

    if (!(latestStatus == "Cancelled" || latestStatus == "Aborted")) throw BzError("errRecoverBadExternalCallAsync")

    val update: EntityValue = mutableMapOf(
      "status" to "Init",
      "doneOn" to null,
      "failedNum" to 0,
      "resCode" to null,
      "resBody" to null,
    )
    EntityRwService.updateOne("ExternalCallRecord", Cq.idEq(recordId), update)

    val ev = EntityRwService.findOne("ExternalCallRecord", Cq.idEq(recordId)) ?: return
    reload(ev)
  }
}

data class CheckResultResult(val ok: Boolean = false)

data class FailedParams(val failedNum: Int, val failedReason: String = "")