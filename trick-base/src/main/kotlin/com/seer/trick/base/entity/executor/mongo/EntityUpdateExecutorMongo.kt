package com.seer.trick.base.entity.executor.mongo

import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldScale
import com.seer.trick.base.entity.FieldType
import com.seer.trick.base.entity.executor.EntityCreateWorkContext
import com.seer.trick.base.entity.executor.EntityRemoveWorkContext
import com.seer.trick.base.entity.executor.EntityUpdateWorkContext

object EntityUpdateExecutorMongo {

  fun execute(ctx: EntityUpdateWorkContext, entityMeta: EntityMeta, targetIds: List<String>, update: EntityValue) {
    for (fd in entityMeta.fields.values) {
      if (!update.containsKey(fd.name)) continue
      if (fd.type == FieldType.Component) {
        if (fd.scale == FieldScale.Single) {
          ctx.mainTableUpdate[fd.name] = if (update[fd.name] != null) 1 else null
        } else {
          val updateList = update[fd.name] as List<*>?
          ctx.mainTableUpdate[fd.name] = if (!updateList.isNullOrEmpty()) {
            updateList.size
          } else {
            null
          }
        }
        ctx.replacingFields.add(fd)
      } else {
        ctx.mainTableUpdate[fd.name] = update[fd.name]
      }
    }

    // 1. update main table
    if (ctx.mainTableUpdate.isNotEmpty()) {
      MongoExecutor.updateMany(entityMeta, Cq.include("id", targetIds), ctx.mainTableUpdate)
    }

    if (ctx.replacingFields.isNotEmpty()) {
      // 2. delete related table rows
      EntityRemoveExecutorMongo.execute(EntityRemoveWorkContext(), entityMeta, targetIds, ctx.replacingFields)

      // 3. insert related table rows
      val entityValues = targetIds.map { targetId ->
        val ev: EntityValue = mutableMapOf()
        ev.putAll(update)
        ev["id"] = targetId
        ev
      }
      EntityCreateExecutorMongo.execute(EntityCreateWorkContext(), entityMeta, entityValues, true)
    }
  }
}