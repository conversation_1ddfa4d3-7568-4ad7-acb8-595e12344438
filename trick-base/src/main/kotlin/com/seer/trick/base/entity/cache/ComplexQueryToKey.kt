package com.seer.trick.base.entity.cache

import com.seer.trick.ComplexQuery
import com.seer.trick.ComplexQueryOperator
import com.seer.trick.ComplexQueryType
import org.apache.commons.lang3.time.DateUtils
import java.util.*

object ComplexQueryToKey {
  
  fun queryToKey(query: ComplexQuery): String {
    return when (query.type) {
      ComplexQueryType.All -> "(ALL)"
      ComplexQueryType.Compound -> {
        val desc: String = if (query.items.isNullOrEmpty()) {
          ""
        } else {
          query.items!!.joinToString(if (query.or) " OR " else " AND ") { item -> queryToKey(item) }
        }
        "($desc)"
      }
      
      ComplexQueryType.General -> {
        val value: Any? = if (!query.field2.isNullOrBlank()) {
          query.field2
        } else if (query.operator == ComplexQueryOperator.CurrentUser) {
          throw IllegalArgumentException("Op 'CurrentUser' no supported in handlers")
        } else if (query.operator == ComplexQueryOperator.CurrentUsername) {
          throw IllegalArgumentException("Op 'CurrentUsername' no supported in handlers")
        } else if (query.operator == ComplexQueryOperator.ThisWeek) {
          DateUtils.truncate(Date(), Calendar.DATE) // 今天
        } else {
          query.value ?: ""
        }
        val desc = (if (query.not) "NOT " else "") + (query.field1 ?: "") + " " + (query.operator ?: "") + " " + value
        "($desc)"
      }
    }
  }
  
}
