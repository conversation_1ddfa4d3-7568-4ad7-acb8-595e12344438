package com.seer.trick.base.reslock

import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

object ResLockService {
  
  private val logger = LoggerFactory.getLogger(this::class.java)
  
  val resLock = ReentrantLock()
  
  fun isLocked(resType: String, resId: String): Boolean {
    return resLock.withLock {
      val ev = EntityRwService.findOne(
        "ResourceLock", Cq.and(listOf(Cq.eq("resType", resType), Cq.eq("resId", resId)))
      )
      ev?.get("locked") as Boolean? ?: false
    }
  }
  
  fun getOwner(resType: String, resId: String): String? {
    return resLock.withLock {
      val ev = EntityRwService.findOne(
        "ResourceLock", Cq.and(listOf(Cq.eq("resType", resType), Cq.eq("resId", resId)))
      )
      ev?.get("owner") as String?
    }
  }
  
  /**
   * 允许重复锁定
   */
  fun tryLockRes(resType: String, resId: String, owner: String, reason: String): Boolean {
    return resLock.withLock {
      val ev = EntityRwService.findOne(
        "ResourceLock", Cq.and(listOf(Cq.eq("resType", resType), Cq.eq("resId", resId)))
      )
      if (ev == null) {
        logger.info("尝试锁定资源成功 $resType:$resId by $owner，原因=$reason")
        EntityRwService.createOne(
          "ResourceLock", mutableMapOf(
            "resType" to resType,
            "resId" to resId,
            "locked" to true,
            "owner" to owner,
            "reason" to reason,
            "lockedOn" to Date()
          )
        )
        true
      } else {
        if (ev["locked"] == true) {
          return ev["owner"] == owner
        } else {
          logger.info("尝试锁定资源成功 $resType:$resId by $owner，原因=$reason")
          EntityRwService.updateOne(
            "ResourceLock", Cq.idEq(EntityHelper.mustGetId(ev)), mutableMapOf(
              "resType" to resType,
              "resId" to resId,
              "locked" to true,
              "owner" to owner,
              "reason" to reason,
              "lockedOn" to Date()
            )
          )
          true
        }
      }
    }
  }
  
  fun unlockRes(resType: String, resId: String) {
    resLock.withLock {
      val ev = EntityRwService.findOne(
        "ResourceLock", Cq.and(listOf(Cq.eq("resType", resType), Cq.eq("resId", resId)))
      )
      if (ev != null) {
        if (ev["locked"] == true) {
          val owner = ev["owner"]
          logger.info("释放锁 $resType:$resId，当前锁定者 '$owner'")
          updateToUnlock(resType, resId)
        } else {
          logger.info("释放锁 $resType:$resId，但未锁")
        }
      } else {
        logger.info("释放锁 $resType:$resId，没有锁定记录")
      }
    }
  }
  
  fun unlockResIfLockedBy(resType: String, resId: String, me: String): Boolean {
    return resLock.withLock {
      val ev = EntityRwService.findOne(
        "ResourceLock", Cq.and(listOf(Cq.eq("resType", resType), Cq.eq("resId", resId)))
      )
      if (ev != null) {
        if (ev["locked"] == true) {
          val owner = ev["owner"]
          logger.info("尝试释放锁 $resType:$resId，当前锁定者 '$owner'，我 '$me'")
          if (owner == me) {
            updateToUnlock(resType, resId)
            true
          } else {
            false // 不是我锁的
          }
        } else {
          logger.info("释放锁 $resType:$resId，但未锁")
          true
        }
      } else {
        logger.info("释放锁 $resType:$resId，没有锁定记录")
        true
      }
    }
  }
  
  fun listMyRes(me: String): List<EntityValue> {
    return EntityRwService.findMany(
      "ResourceLock", Cq.and(listOf(Cq.eq("owner", me), Cq.eq("locked", true)))
    )
  }
  
  private fun updateToUnlock(resType: String, resId: String) {
    EntityRwService.updateOne(
      
      "ResourceLock",
      Cq.and(listOf(Cq.eq("resType", resType), Cq.eq("resId", resId))),
      mutableMapOf(
        "locked" to false,
        "owner" to "",
        "reason" to "",
        "lockedOn" to null
      )
    )
  }
  
}