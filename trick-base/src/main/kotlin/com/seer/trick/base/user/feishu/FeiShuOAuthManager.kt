package com.seer.trick.base.user.feishu

import com.seer.trick.BzError
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.user.OAuthCacheManager
import com.seer.trick.helper.JsonHelper
import okhttp3.OkHttpClient
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import retrofit2.Retrofit
import retrofit2.converter.jackson.JacksonConverterFactory


object FeiShuOAuthManager : OAuthCacheManager<FeiShuConfig>() {
  override var platformKey = "FeiShu"
  override var logger: Logger = LoggerFactory.getLogger(FeiShuOAuthManager::class.java)
  private val apiService: FeiShuRemoteService by lazy {
    Retrofit.Builder()
      .baseUrl("https://open.feishu.cn/open-apis/")
      .addConverterFactory(JacksonConverterFactory.create(JsonHelper.mapper))
      .client(OkHttpClient.Builder().build())
      .build()
      .create(FeiShuRemoteService::class.java)
  }

  @Suppress("UNCHECKED_CAST")
  override fun readConfig(): FeiShuConfig =
    FeiShuConfig( BzConfigManager.getByPath("ScLogin", "feishu")  as EntityValue? ?: HashMap())


  @Synchronized
  override fun fetchAccessToken(code: String): AccessToken {
    val c = readConfig()
    if (c.feiShuAppId.isBlank() || c.feiShuAppSecret.isBlank()) throw BzError("errFeiShuBadConfig")

    val res = apiService.getAppToken(appId = c.feiShuAppId, appSecret = c.feiShuAppSecret)
      .execute().handleApiResponse<FeiShuAppTokenRes>()?: throw BzError("errFeiShuCallErr", 12)


    if (res.code != 0 || res.appAccessToken.isNullOrBlank()) {
      logger.error("feishu, failed to get app token ${res.code}, ${res.msg}")
      throw BzError("errFeiShuCallErr", 12)
    }
    return AccessToken(res.appAccessToken!!, res.expire)
  }

  @Synchronized
  override fun fetchUserInfo(accessToken: String, code: String): UserInfo {
    val userTokenResponseBody = apiService.getUserToken(code = code, grantType = "authorization_code", authorization = "Bearer $accessToken")
      .execute().handleApiResponse<FeiShuUserTokenRes>()?: throw BzError("errFeiShuCallErr", 22)

    if (userTokenResponseBody.code != 0) {
      logger.error("FeiShu, failed to get user token ${userTokenResponseBody.code}, ${userTokenResponseBody.msg}")
      throw BzError("errFeiShuCallErr", 22)
    }

    val userInfoResponseBody = apiService.getUserInfo(authorization = "Bearer ${userTokenResponseBody.data!!.accessToken!!}")
      .execute().handleApiResponse<FeiShuUserInfoRes>() ?: throw BzError("errFeiShuCallErr", 25)

    if (userInfoResponseBody.code != 0) {
      logger.error("FeiShu, failed to get user info ${userInfoResponseBody.code}, ${userInfoResponseBody.msg}")
      throw BzError("errFeiShuCallErr", 25)
    }

    return UserInfo(userInfoResponseBody.data.userId!!, userInfoResponseBody.data.name!!)
  }
}


