package com.seer.trick.base.entity.id

import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.IdGenRule
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.helper.DateHelper
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import java.util.*

object IdGenManager {
  
  private val logger = LoggerFactory.getLogger(this::class.java)
  
  @Synchronized
  fun generateId(rule: IdGenRule): String {
    while (true) {
      try {
        return doGenerateId(rule)
      } catch (_: IdConcurrentError) {
        // ignore
      }
    }
  }
  
  private fun doGenerateId(rule: IdGenRule): String {
    val todayStr = DateHelper.formatDate(Date(), "yyyyMMdd")
    var no = 1
    val fixedPrefix = StringUtils.defaultString(rule.fixedPrefix, "")
    val flowNoWidth = if (rule.flowNoWidth <= 0) 4 else rule.flowNoWidth
    val uniqueKey = fixedPrefix + flowNoWidth
    val record = EntityRwService.findOne("IdGen", Cq.eq("key", uniqueKey), null)
    if (record != null) {
      no = record["flowNo"] as Int
      if (todayStr != record["timestamp"]) {
        no = 1
      } else {
        no++
      }
      val id = EntityHelper.mustGetId(record)
      val update: EntityValue = mutableMapOf("timestamp" to todayStr, "flowNo" to no)
      val q = Cq.and(listOf(Cq.idEq(id), Cq.eq("version", record["version"])))
      if (1L != EntityRwService.updateMany("IdGen", q, update)) {
        logger.error("Gen id, concurrent conflict")
        throw IdConcurrentError()
      }
    } else {
      val ev: EntityValue = mutableMapOf("key" to uniqueKey, "timestamp" to todayStr, "flowNo" to no)
      EntityRwService.createOne("IdGen", ev, null)
      // TODO key 作为唯一键，防止重复插入
    }
    return toId(fixedPrefix, todayStr, flowNoWidth, no)
  }
  
  private fun toId(prefix: String, todayStr: String, width: Int, no: Int): String {
    return String.format("%s%s-%s", prefix, todayStr, StringUtils.leftPad(no.toString(), width, "0"))
  }
  
}
