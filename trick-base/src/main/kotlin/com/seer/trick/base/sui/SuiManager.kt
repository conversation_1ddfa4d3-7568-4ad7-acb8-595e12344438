package com.seer.trick.base.sui

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.OptionItem
import com.seer.trick.base.http.WebSocketManager
import com.seer.trick.base.http.WsMsg
import com.seer.trick.base.user.Operator
import com.seer.trick.helper.IdHelper
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

/**
 * 简单用户介入 SimpleUserInvolved
 */
object SuiManager {
  
  private val reqMap: MutableMap<String, SuiRequest> = ConcurrentHashMap()
  private val resMap: MutableMap<String, SuiResult> = ConcurrentHashMap()
  
  private val lock = ReentrantLock()
  private val condition = lock.newCondition()
  
  /**
   * 请求用户接入
   */
  fun sui(req: SuiRequest): SuiResult {
    WebSocketManager.sendAllAsync(WsMsg(action = "SuiRequest"))
    
    while (true) {
      reqMap[req.id] = req
      
      while (true) {
        lock.withLock {
          try {
            condition.await()
          } catch (e: InterruptedException) {
            throw e
          }
        }
        
        return resMap.remove(req.id) ?: continue
      }
    }
  }
  
  fun setRes(id: String, res: SuiResult) {
    reqMap.remove(id)
    lock.withLock {
      resMap[id] = res
      condition.signalAll()
    }
  }
  
  fun listMine(workStation: String?, op: Operator): List<SuiRequest> {
    return reqMap.values.filter {
      if (it.targetType == SuiTargetType.Users) {
        return@filter it.targetUsernames.isNullOrEmpty() || it.targetUsernames.contains(op.username)
      } else if (it.targetType == SuiTargetType.WorkStations) {
        return@filter it.targetWorkStations.isNullOrEmpty() ||
            !workStation.isNullOrBlank() && it.targetWorkStations.contains(workStation)
      }
      true
    }.toList()
  }
  
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class SuiRequest(
  val message: String,
  val fields: List<SuiField>? = null,
  val buttons: List<SuiButton>? = null,
  val targetType: SuiTargetType = SuiTargetType.All,
  val targetUsernames: List<String>? = null,
  val targetWorkStations: List<String>? = null,
  val id: String = IdHelper.oidStr(),
  val createdOn: Date = Date()
)

data class SuiField(
  val name: String,
  val label: String,
  val type: SuiFieldType,
  val required: Boolean = false,
  val options: List<OptionItem> = emptyList()
)

enum class SuiFieldType {
  
  Text, Boolean, Select, Number
  
}

data class SuiButton(
  val label: String,
  val kind: String = "",
  val uiButton: Boolean = false,
  val refEntity: String? = null,
  val refEntityId: String? = null,
)

enum class SuiTargetType {
  
  Users, WorkStations, All
  
}

data class SuiResult(
  val button: Int,
  val input: EntityValue,
  val id: String,
)