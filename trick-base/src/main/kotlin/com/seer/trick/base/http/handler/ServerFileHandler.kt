package com.seer.trick.base.http.handler

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.concurrent.BaseConcurrentCenter.uploadExecutor
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.http.*
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.HttpServerManager.sendStream
import com.seer.trick.helper.*
import io.javalin.http.Context
import java.io.*
import java.nio.file.Files
import java.nio.file.attribute.BasicFileAttributes
import org.apache.commons.io.FileUtils
import org.apache.commons.io.IOUtils
import org.slf4j.LoggerFactory


/**
 * 服务器文件管理
 */
object ServerFileHandler {

  private val logger = LoggerFactory.getLogger(this::class.java)

  fun registerHandlers() {
    val c = Handlers("api/server-files")

    c.post("ls", ::listFiles, auth())

    c.post("mkdir", ::mkdir, auth())
    c.post("mv", ::mv, auth())
    c.post("rename", ::rename, auth())
    c.post("rm", ::rm, auth())

    c.post("upload", ::upload, auth())
    c.get("download", ::download, auth())

    c.post("zip", ::zipDirectory, auth())
    c.post("unzip", ::unzip, auth())
  }

  private fun listFiles(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw BzError("errAdminRequired")

    val req: ListServerFilesReq = ctx.getReqBody()

    val files = listFiles(req.dirPath)
    ctx.json(files)
  }

  @Suppress("UNCHECKED_CAST")
  private fun listFiles(dirPath: String): List<FileDigest> {
    val dir = File(dirPath)
    if (!dir.exists()) throw BzError("errDirNotExists", dirPath)
    if (!dir.isDirectory) throw BzError("errFileNotDirectory", dirPath)
    val files = dir.listFiles() ?: throw BzError("errDirFilesNull", dirPath)

    val (fullDownloadMode, whitelistDir) = loadServerFileConfig()

    return files.filter { !it.isHidden }.mapNotNull {
      try {
        val attr: BasicFileAttributes = Files.readAttributes(it.toPath(), BasicFileAttributes::class.java)
        FileDigest(
          it.name, it.length(), it.isDirectory,
          attr.creationTime().toMillis(), attr.lastModifiedTime().toMillis(),
          downloadAllowed = if (fullDownloadMode) {
            true
          } else if (FileHelper.isFileInDir(it, whitelistDir)) {
            true
          } else {
            false
          }
        )
      } catch (_: Exception) {
        null // 因为各种原因拿不到就算了
      }
    }
  }

  private fun mkdir(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw BzError("errAdminRequired")

    val req: SfMkdirReq = ctx.getReqBody()
    mkdir(req.parent, req.dirname)

    ctx.status(200)
  }

  private fun mkdir(parent: String, dirname: String): Boolean {
    val dir = File(parent, dirname)
    return dir.mkdirs()
  }

  private fun mv(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw BzError("errAdminRequired")

    val req: SfMvReq = ctx.getReqBody()
    mv(req.parent, req.filenames, req.targetDirPath)

    ctx.status(200)
  }

  private fun mv(parent: String, filenames: List<String>, targetDirPath: String) {
    val targetDir = File(targetDirPath)
    if (!targetDir.exists()) throw BzError("errDirNotExists", targetDirPath)
    for (filename in filenames) {
      val file = File(parent, filename)
      logger.info("移动文件 ${file.canonicalPath} 到 $targetDirPath")
      if (!file.exists()) continue
      if (file.isDirectory) FileUtils.moveDirectory(file, targetDir)
      else FileUtils.moveFile(file, targetDir)
    }
  }

  private fun rename(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw BzError("errAdminRequired")

    val req: SfRenameReq = ctx.getReqBody()
    rename(req.parent, req.oldFilename, req.newFileName)

    ctx.status(200)
  }

  private fun rename(parent: String, oldFilename: String, newFileName: String) {
    val file1 = File(parent, oldFilename)
    val file2 = File(parent, newFileName)
    logger.info("重命名文件 ${file1.canonicalPath} 到 $newFileName")
    if (!file1.exists()) return
    if (file1.isDirectory) FileUtils.moveFile(file1, file2)
    else FileUtils.moveDirectory(file1, file2)
  }

  private fun rm(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw BzError("errAdminRequired")

    val req: SfRmReq = ctx.getReqBody()
    rm(req.parent, req.filenames)

    ctx.status(200)
  }

  private fun rm(parent: String, filenames: List<String>) {
    for (filename in filenames) {
      val file = File(parent, filename)
      logger.info("删除文件 ${file.canonicalPath}")
      if (!file.exists()) continue
      if (file.isDirectory) FileUtils.deleteDirectory(file)
      else FileUtils.delete(file)
    }
  }

  private fun unzip(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw BzError("errAdminRequired")

    val req: SfUnzipReq = ctx.getReqBody()
    unzip(req.parent, req.filename)

    ctx.status(200)
  }

  private fun unzip(parent: String, filename: String) {
    val file = File(parent, filename)
    if (!file.exists()) throw BzError("errFileNotExists", file.canonicalPath)
    val dir = File(parent, file.nameWithoutExtension)
    FileHelper.unzipFileToDir(file, dir)
  }

  private fun zipDirectory(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw BzError("errAdminRequired")

    val req: SfZipReq = ctx.getReqBody()
    zipDirectory(req.parent, req.dirname)

    ctx.status(200)
  }

  private fun zipDirectory(parent: String, dirname: String): File {
    val dir = File(parent, dirname)
    if (!dir.exists()) throw BzError("errDirNotExists", dir.canonicalPath)

    val file = File(parent, "$dirname.zip")
    FileHelper.zipDirToFile(dir, file)

    return file
  }

  private fun download(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw BzError("errAdminRequired")

    val parent = ctx.queryParam("parent") ?: throw BzError("errCodeErr", "Missing query param 'parent'")
    val filenamesStr = ctx.queryParam("filenames") ?: throw BzError("errCodeErr", "Missing query param 'filenames'")
    val filenames = StringHelper.splitTrim(filenamesStr, ";")
    val ff = download(parent, filenames)
    // ctx.status(200)
    ctx.header("Content-Disposition", "attachment; filename=\"${ff.name}\"")

    FileInputStream(ff).use {
      sendStream(it, ctx, ff.name)
    }
  }

  private fun download(parent: String, filenames: List<String>): File {
    whiteListCheck(parent, filenames)
    val ff = if (filenames.size == 1) {
      val filename = filenames[0]
      val file = File(parent, filename)
      if (!file.exists()) throw BzError("errFileNotExists", file.canonicalPath)
      if (file.isDirectory) {
        zipDirectory(parent, filename)
      } else {
        file
      }
    } else {
      val zipDirname = IdHelper.oidStr()
      val zipDir = File(parent, zipDirname)
      zipDir.mkdirs()
      for (filename in filenames) {
        val file1 = File(parent, filename)
        if (!file1.exists()) continue
        val file2 = File(zipDir, filename)
        if (file1.isDirectory) {
          FileUtils.copyDirectory(file1, file2)
        } else {
          FileUtils.copyFile(file1, file2)
        }
      }
      val zipFile = File(parent, "$zipDirname.zip")
      FileHelper.zipDirToFile(zipDir, zipFile)
      FileUtils.deleteDirectory(zipDir)
      zipFile
    }

    return ff
  }

  /**
   * 白名单校验
   */
  @Suppress("UNCHECKED_CAST")
  private fun whiteListCheck(parent: String, filenames: List<String>) {
    val (fullDownloadMode, whitelistDir) = loadServerFileConfig()
    if (fullDownloadMode) return // 全盘下载提前返回
    for (filename in filenames) {
      val file = File(parent, filename)
      if (!FileHelper.isFileInDir(file, whitelistDir)) {
        throw BzError("errFileNotExists", "Access denied: $parent is outside of whitelist")
      }
    }
  }

  @Suppress("UNCHECKED_CAST")
  private fun loadServerFileConfig(): Pair<Boolean, File> {
    val config = BzConfigManager.getByPath("File", "serverFiles") as? EntityValue ?: emptyMap()
    val fullDownloadMode = config["fullDownloadMode"] as? Boolean ?: false
    val whitelistPath = (config["downloadWhitelist"] as? String)?.takeIf { it.isNotBlank() }
      ?: BaseCenter.baseConfig.projectDir.toString()

    return fullDownloadMode to File(whitelistPath)
  }

  private fun upload(ctx: Context) {
    val op = ctx.operator()
    if (!op.admin) throw BzError("errAdminRequired")

    val reqStr = ctx.formParam("req")
    if (reqStr.isNullOrBlank()) throw BzError("errCodeErr", "Missing form field 'req'")
    val req: SfUploadReq = JsonHelper.mapper.readValue(reqStr, jacksonTypeRef())

    // 并行上传
    (0 until req.num).map { i ->
      val file = ctx.uploadedFile("f$i") ?: throw BzError("errCodeErr", "No form field: f$i")
      logger.info("上传文件 ${file.filename()} 到 ${req.parent}，大小=${file.size()}")
      uploadExecutor.submitCatch("Upload", logger) {
        val target = File(req.parent, file.filename())
        FileOutputStream(target).use { IOUtils.copy(file.content(), it) }
      }
    }.map { it.get() }

    ctx.json(200)
  }

}

data class FileDigest(
  val fileName: String,
  val size: Long,
  val dir: Boolean = false,
  val createdOn: Long,
  val modifiedOn: Long,
  val downloadAllowed: Boolean = true,
)

data class ListServerFilesReq(
  val dirPath: String,
)

data class SfMkdirReq(
  val parent: String,
  val dirname: String
)

data class SfMvReq(
  val parent: String,
  val filenames: List<String>,
  val targetDirPath: String
)

data class SfRenameReq(
  val parent: String,
  val oldFilename: String,
  val newFileName: String
)

data class SfRmReq(
  val parent: String,
  val filenames: List<String>
)

data class SfUnzipReq(
  val parent: String,
  val filename: String
)

data class SfZipReq(
  val parent: String,
  val dirname: String
)

data class SfUploadReq(
  val parent: String,
  val num: Int, // 文件个数
)