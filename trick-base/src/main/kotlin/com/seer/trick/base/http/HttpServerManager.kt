package com.seer.trick.base.http

import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.http.handler.Error401
import com.seer.trick.base.http.handler.Error403
import com.seer.trick.base.http.handler.Error404
import com.seer.trick.base.script.ScriptHttpServer
import com.seer.trick.base.user.AgentSecurity
import com.seer.trick.base.user.Operator
import com.seer.trick.base.user.PermissionManager
import com.seer.trick.base.user.UserService
import com.seer.trick.helper.FileHelper
import com.seer.trick.helper.JsonHelper
import io.javalin.Javalin
import io.javalin.community.ssl.SSLPlugin
import io.javalin.community.ssl.TLSConfig
import io.javalin.config.JavalinConfig
import io.javalin.http.Context
import io.javalin.http.Cookie
import io.javalin.http.Handler
import io.javalin.http.HandlerType
import io.javalin.json.JavalinJackson
import org.apache.commons.io.FileUtils
import org.apache.commons.io.IOUtils
import org.apache.tika.Tika
import org.eclipse.jetty.server.ForwardedRequestCustomizer
import org.slf4j.LoggerFactory
import java.io.File
import java.io.InputStream
import java.time.Duration
import java.time.temporal.ChronoUnit
import java.util.*
import java.util.concurrent.CopyOnWriteArrayList

object HttpServerManager {

  private const val COOKIE_USER_ID = "xzz-qyq"
  private const val COOKIE_USER_TOKEN = "xzz-qyx"

  private val logger = LoggerFactory.getLogger(javaClass)

  private val app: Javalin = Javalin.create { jc ->
    JavalinJackson(JsonHelper.mapper)
    jc.http.maxRequestSize = 1024 * 1024 * 500
    jc.showJavalinBanner = false
    BaseCenter.baseConfig.configDir
    registerHttps(jc)
    jc.plugins.enableCors { cors ->
      cors.add { it.anyHost() }
    }
    jc.jetty.httpConfigurationConfig { c ->
      c.idleTimeout = 15000
      c.addCustomizer(ForwardedRequestCustomizer())
    }
    jc.jetty.wsFactoryConfig { c ->
      c.maxFrameSize = 1024 * 1024 * 500 // 500M
      c.maxTextMessageSize = 1024 * 1024 * 500 // 500M
      c.idleTimeout = Duration.of(15, ChronoUnit.SECONDS)
    }
  }.ws("/wsm", WebSocketManager::handleWebSocket)

  private val mappings: MutableList<HttpRequestMapping> = CopyOnWriteArrayList()

  fun startHttpServer(port: Int) {
    registerHandleAll()
    app.start(port)
    logger.info("HTTP server started on $port")
  }

  fun stop() {
    logger.info("Stop HTTP server")
    app.stop()
  }

  fun handle(vararg reqMappings: HttpRequestMapping) {
    mappings.addAll(reqMappings)
    for (h in reqMappings) {
      app.addHandler(h.method, h.path, enhanceHandler(h))
    }
  }

  fun listMappings() = mappings

  private fun registerHandleAll() {
    app.addHandler(HandlerType.GET, "*", HttpServerManager::handleAll)
    app.addHandler(HandlerType.POST, "*", HttpServerManager::handleAll)
    app.addHandler(HandlerType.PUT, "*", HttpServerManager::handleAll)
    app.addHandler(HandlerType.DELETE, "*", HttpServerManager::handleAll)
  }

  private fun handleAll(ctx: Context) {
    val path = ctx.path()
    // logger.debug("path=$path")
    if (path == "" || path == "/") {
      sendStaticFile(BaseCenter.baseConfig.uiDir, "index.html", ctx)
    } else if (path.endsWith(".html") || path.endsWith(".htm")) {
      sendStaticFile(BaseCenter.baseConfig.uiDir, path, ctx)
    } else if (path.startsWith("/assets/")) {
      sendStaticFile(BaseCenter.baseConfig.uiDir, path, ctx)
    } else if (path.startsWith("/ui-ext/")) {
      sendStaticFile(BaseCenter.baseConfig.uiExtDir, path.substringAfter("/ui-ext/"), ctx)
    } else {
      if (!ScriptHttpServer.handle(ctx)) {
        ctx.status(404)
      }
    }
  }

  private fun sendStaticFile(dir: File?, path: String, ctx: Context) {
    if (dir == null || !dir.exists()) throw BzError("errNoUiDir", dir)
    val file = File(dir, path)
    if (FileHelper.isFileInDir(dir, file)) throw Error403("文件位置超出范围")
    if (!file.exists()) {
      ctx.status(404)
      return
    }

    sendFileWithType(file, ctx)
  }

  /**
   * 发文件
   * ctx.outputStream() 是带压缩的；ctx.res().outputStream() 是原始的
   * 不要关输出流
   */
  fun sendFileWithType(file: File, ctx: Context) {
    val tika = Tika()
    val mt = tika.detect(file)
    ctx.contentType(mt)
    val res = ctx.res()
    res.setContentLength(file.length().toInt())
    FileUtils.copyFile(file, res.outputStream)
  }

  /**
   * ctx.outputStream() 是带压缩的；ctx.res().outputStream() 是原始的
   * 不要关输出流
   */
  fun sendStream(inputStream: InputStream, ctx: Context, filename: String?) {
    if (!filename.isNullOrBlank()) {
      val tika = Tika()
      val mt = tika.detect(filename)
      ctx.contentType(mt)
    }
    val res = ctx.res()
    IOUtils.copy(inputStream, res.outputStream)
  }

  private fun enhanceHandler(reqHandler: HttpRequestMapping): Handler = Handler { ctx ->
    val method = ctx.method().name
    val path = ctx.endpointHandlerPath()
    val reqStartOn = Date()
    val config = BaseCenter.httpApiCallTraceConfigMap["$method $path"]

    var trace: EntityValue? = null
    if (config?.trace == true) {
      trace = mutableMapOf(
        "apiType" to "HTTP",
        "httpMethod" to method,
        "httpPath" to path,
        "httpUrl" to ctx.path(),
        "reqStartOn" to reqStartOn,
        "reqBodyOn" to config.reqBodyOn,
        "resBodyOn" to config.resBodyOn,
        "reqIp" to ctx.ip(),
      )
      if (config.reqBodyOn) trace["reqBody"] = ctx.body()
    }

    try {
      val op = authenticate(ctx)
      if (trace != null) trace["reqUser"] = op?.userId

      if (reqHandler.meta.auth) {
        if (op == null) throw Error401()
        // TODO permissions
        // val p = mapping.meta.permission
        // if (!user.admin && !p.isNullOrBlank()) {
        //   val noP = !HumanUserService.userHasPermission(user, p)
        //   if (noP) throw Error403(mapping.meta.permission)
        // }
      }

      reqHandler.handler(ctx)
    } catch (_: Error401) {
      ctx.status(401)
    } catch (e: Error404) {
      ctx.status(404)
      ctx.result(e.message ?: "")
    } catch (e: Error403) {
      ctx.status(403)
      ctx.json(mapOf("permission" to e.message))
      // } catch (e: Error404) {
      //   ctx.status(404)
    } catch (e: BzError) {
      ctx.status(400)
      // val msg = I18N.lo("zh", e.code, *e.args) // TODO
      // logger.info("BzError: {}", msg, e.cause)
      // val msg = "${e.code} ${e.args.joinToString(", ")}"
      val r: MutableMap<String, Any?> = HashMap()
      r["code"] = e.code
      r["message"] = e.message
      r["args"] = e.args
      ctx.json(r)
    } catch (e: Error) {
      ctx.status(500)
      logger.error("catch all at http server", e)
    } catch (e: Throwable) {
      ctx.status(500)
      logger.error("catch all at http server", e)
    }

    if (trace != null) {
      val end = Date()
      val cost = end.time - reqStartOn.time
      trace["reqEndOn"] = end
      trace["costTime"] = cost
      trace["resCode"] = ctx.status().code
      if (config?.resBodyOn == true) {
        trace["resBody"] = ctx.result()
      }

      EntityRwService.createOne("ApiCallTrace", trace)
    }
  }

  /**
   * 依次检查：用户 id/token 登录、Agent 登录、匿名登录
   */
  private fun authenticate(ctx: Context): Operator? {
    var op = authUser(ctx) ?: authAgent(ctx)
    if (op == null && BaseCenter.baseConfig.anonymouseUser) {
      op = Operator(userId = "Anonymouse", username = "Anonymouse", admin = true, anonymous = true)
    }

    if (op != null) {
      ctx.attribute("operator", op)
      Operator.setCurrent(op)
    }

    return op
  }

  /**
   * 用户 id/token 登录
   */
  private fun authUser(ctx: Context): Operator? {
    var userId = ctx.cookie(getPortedCookieName(COOKIE_USER_ID, ctx.port()))
    var userToken = ctx.cookie(getPortedCookieName(COOKIE_USER_TOKEN, ctx.port()))

    if (userId.isNullOrBlank() || userToken.isNullOrBlank()) {
      userId = ctx.header("x-$COOKIE_USER_ID")
      userToken = ctx.header("x-$COOKIE_USER_TOKEN")
    }

    if (userId.isNullOrBlank() || userToken.isNullOrBlank()) return null

    val us = UserService.checkToken(userId, userToken) ?: return null
    val op = Operator(userId = us.userId, username = us.username ?: "", admin = us.roAdmin)
    return PermissionManager.loadUserPermissions(op)
  }

  /**
   * agent 登录
   */
  private fun authAgent(ctx: Context): Operator? {
    val appId = ctx.header("xyy-app-id")
    val appKey = ctx.header("xyy-app-key")

    if (!appId.isNullOrBlank() && !appKey.isNullOrBlank()) {
      if (AgentSecurity.authAgent(appId, appKey)) {
        return Operator(userId = appId, username = appId, agent = true)
      }
    }

    return null
  }

  /**
   * 注册 https 插件，并设置一些配置
   */
  private fun registerHttps(jc: JavalinConfig) {
    if (BaseCenter.baseConfig.httpsConfig.enableHttps) {
      val plugin = SSLPlugin { conf ->
        conf.pemFromPath(
          "${BaseCenter.baseConfig.configDir}\\cert.pem",
          "${BaseCenter.baseConfig.configDir}\\key.pem",
          BaseCenter.baseConfig.httpsConfig.password,
        )
        conf.insecure = true
        conf.secure = true
        conf.http2 = false
        conf.insecurePort = BaseCenter.baseConfig.port
        conf.securePort = BaseCenter.baseConfig.httpsConfig.httpsPort
        conf.sniHostCheck = false
        conf.tlsConfig = TLSConfig.INTERMEDIATE
      }
      jc.plugins.register(plugin)
    }
  }

  fun auth() = ReqMeta(true)

  fun noAuth() = ReqMeta(false)

  fun permit(permission: String) = ReqMeta(true, permission)

  private fun getPortedCookieName(name: String, port: Int): String = "$name-$port"

  private fun setSessionCookie(ctx: Context, name: String, value: String?) {
    if (value != null) {
      ctx.cookie(Cookie(name, value, secure = false, isHttpOnly = true, path = "/"))
      // ctx.res().addHeader("Set-Cookie", "$name=$value; httpOnly; path=/;")
    } else {
      ctx.removeCookie(name)
    }
  }

  fun queryParamsMapToSimpleMap(map: Map<String, List<String>>): MutableMap<String, String> {
    val result: MutableMap<String, String> = HashMap()
    for (key in map.keys) {
      val list = map[key] ?: continue
      result[key] = if (list.isNotEmpty()) list.first() else ""
    }
    return result
  }

  fun getReqLang(ctx: Context): String {
    val lang = ctx.header("X-Req-Lang")
    return if (lang.isNullOrBlank()) "zh" else lang
  }

  // null to remove
  fun setUserSessionCookies(ctx: Context, id: String?, token: String?) {
    setSessionCookie(ctx, getPortedCookieName(COOKIE_USER_ID, ctx.port()), id)
    setSessionCookie(ctx, getPortedCookieName(COOKIE_USER_TOKEN, ctx.port()), token)
  }
}

class HttpRequestMapping(
  var method: HandlerType,
  var path: String,
  var handler: (ctx: Context) -> Unit,
  var meta: ReqMeta = ReqMeta(),
)

data class ReqMeta(
  var auth: Boolean = true,
  var permission: String? = null,
  var test: Boolean = false,
  var page: Boolean = false,
  var reqBodyDemo: List<String>? = null,
)

class Handlers(pathPrefix: String = "") {

  private val pathPrefix: String =
    if (pathPrefix.endsWith("/")) pathPrefix.substring(0, pathPrefix.length - 1) else pathPrefix

  fun get(path: String, handler: (ctx: Context) -> Unit, meta: ReqMeta = ReqMeta()) {
    HttpServerManager.handle(HttpRequestMapping(HandlerType.GET, "$pathPrefix/$path", handler, meta))
  }

  fun post(path: String, handler: (ctx: Context) -> Unit, meta: ReqMeta = ReqMeta()) {
    HttpServerManager.handle(HttpRequestMapping(HandlerType.POST, "$pathPrefix/$path", handler, meta))
  }

  fun put(path: String, handler: (ctx: Context) -> Unit, meta: ReqMeta = ReqMeta()) {
    HttpServerManager.handle(HttpRequestMapping(HandlerType.PUT, "$pathPrefix/$path", handler, meta))
  }

  fun delete(path: String, handler: (ctx: Context) -> Unit, meta: ReqMeta = ReqMeta()) {
    HttpServerManager.handle(HttpRequestMapping(HandlerType.DELETE, "$pathPrefix/$path", handler, meta))
  }
}

inline fun <reified T> Context.getReqBody(): T {
  try {
    return JsonHelper.mapper.readValue(body(), jacksonTypeRef())
  } catch (e: JsonProcessingException) {
    throw BzError("errBadReqBodyJson", e.message ?: "") // TODO 更精简的报错信息
  }
}

inline fun <reified T> Context.getReqBodyOrNull(): T? {
  val b = body()
  if (b.isBlank()) return null
  try {
    return JsonHelper.mapper.readValue(b, jacksonTypeRef())
  } catch (e: JsonProcessingException) {
    throw BzError("errBadReqBodyJson", e.message ?: "") // TODO 更精简的报错信息
  }
}

fun Context.operator(): Operator = this.attribute<Operator>("operator") ?: throw Error401()