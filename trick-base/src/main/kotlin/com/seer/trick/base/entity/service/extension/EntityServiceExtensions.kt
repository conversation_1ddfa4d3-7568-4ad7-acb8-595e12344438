package com.seer.trick.base.entity.service.extension

import com.seer.trick.BzError
import com.seer.trick.I18N.lo
import com.seer.trick.base.concurrent.BaseConcurrentCenter.lowTimeSensitiveExecutor
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityTrackService
import com.seer.trick.base.entity.service.UserNoticeService
import com.seer.trick.base.entity.service.change.EntityChange
import com.seer.trick.base.script.ScriptEntityExt
import com.seer.trick.base.user.Operator
import com.seer.trick.base.user.UserService
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

object EntityServiceExtensions {
  
  private val logger = LoggerFactory.getLogger(javaClass)
  
  private val allExtensions: MutableList<EntityServiceExtension> = CopyOnWriteArrayList()
  private val entityExtensions: MutableMap<String, MutableList<EntityServiceExtension>> = ConcurrentHashMap()
  
  @Synchronized
  fun addExtension(ext: EntityServiceExtension) {
    if (allExtensions.contains(ext)) {
      logger.error("重复添加业务对象扩展 $ext")
      return
    }
    
    allExtensions.add(ext)
  }
  
  @Synchronized
  fun addExtension(entityName: String, ext: EntityServiceExtension) {
    var list = entityExtensions[entityName]
    if (list == null) {
      list = CopyOnWriteArrayList()
      entityExtensions[entityName] = list
    }
    if (list.contains(ext)) {
      logger.error("重复添加业务对象扩展 $entityName $ext")
      return
    }
    list.add(ext)
  }
  
  fun beforeCreating(em: EntityMeta, evList: List<EntityValue>): List<String>? {
    if (em.name == "HumanUser") extHumanUserCreateOrUpdate(em, evList)
    
    val list = entityExtensions[em.name]
    if (list != null) {
      for (m in list) {
        val ids = m.beforeCreating(em, evList)
        if (ids != null) return ids
      }
    }
    for (m in allExtensions) {
      val ids = m.beforeCreating(em, evList)
      if (ids != null) return ids
    }
    
    val ids = ScriptEntityExt.beforeCreating(em, evList)
    if (ids != null) return ids
    
    return null
  }
  
  /**
   * 确保 evList 是从数据库里查出来的最新的值
   */
  fun afterCreating(em: EntityMeta, evList: List<EntityValue>) {
    val op = Operator.current()
    val list = entityExtensions[em.name]
    if (list != null) {
      for (m in list) {
        m.afterCreating(em, evList)
      }
    }
    for (m in allExtensions) {
      m.afterCreating(em, evList)
    }
    
    ScriptEntityExt.afterCreating(em, evList)
    
    if (em.userNotice.create) {
      lowTimeSensitiveExecutor.submit {
        try {
          UserNoticeService.notifyEntityCreate(em.name, evList.map(EntityHelper::mustGetId), op)
        } catch (_: Exception) {
          logger.error("Run entity ext for ${em.name}: afterCreating")
        }
      }
    }
  }
  
  fun beforeUpdating(em: EntityMeta, ids: List<String>, update: EntityValue): Long? {
    if (em.name == "HumanUser") extHumanUserCreateOrUpdate(em, listOf(update))
    
    val list = entityExtensions[em.name]
    if (list != null) {
      for (m in list) {
        val changedCount = m.beforeUpdating(em, ids, update)
        if (changedCount != null) return changedCount
      }
    }
    for (m in allExtensions) {
      val changedCount = m.beforeUpdating(em, ids, update)
      if (changedCount != null) return changedCount
    }
    
    val changedCount = ScriptEntityExt.beforeUpdating(em, ids, update)
    if (changedCount != null) return changedCount
    
    return null
  }
  
  fun afterUpdating(em: EntityMeta, ids: List<String>, oldValues: List<EntityValue>, newValues: List<EntityValue>) {
    val op = Operator.current()
    val changes = ids.map { id ->
      EntityChange(id, EntityHelper.findById(oldValues, id), EntityHelper.findById(newValues, id))
    }
    
    EntityTrackService.trackUpdate(em, changes)
    
    val list = entityExtensions[em.name]
    if (list != null) {
      for (m in list) {
        m.afterUpdating(em, changes)
      }
    }
    for (m in allExtensions) {
      m.afterUpdating(em, changes)
    }
    
    ScriptEntityExt.afterUpdating(em, ids, oldValues, newValues)
    
    if (em.userNotice.update) {
      lowTimeSensitiveExecutor.submit {
        try {
          UserNoticeService.notifyEntityUpdate(em.name, ids, op)
        } catch (e: Exception) {
          logger.error("Run entity ext for ${em.name}: afterUpdating", e)
        }
      }
    }
  }
  
  fun beforeRemoving(em: EntityMeta, ids: List<String>): Long? {
    val list = entityExtensions[em.name]
    if (list != null) {
      for (m in list) {
        val r = m.beforeRemoving(em, ids)
        if (r != null) return r
      }
    }
    
    for (m in allExtensions) {
      val r = m.beforeRemoving(em, ids)
      if (r != null) return r
    }
    
    val removedCount = ScriptEntityExt.beforeRemoving(em, ids)
    if (removedCount != null) return removedCount
    
    return null
  }
  
  fun afterRemoving(em: EntityMeta, oldValues: List<EntityValue>) {
    val list = entityExtensions[em.name]
    if (list != null) {
      for (m in list) {
        m.afterRemoving(em, oldValues)
      }
    }
    
    for (m in allExtensions) {
      m.afterRemoving(em, oldValues)
    }
    
    ScriptEntityExt.afterRemoving(em, oldValues)
    
    // TODO 没有删除通知
    // lowTimeSensitiveExecutor.submitCatch("Run entity ext for ${em.name}:afterRemoving", logger) {
    // }
  }
  
  fun afterBigChange(em: EntityMeta) {
    val list = entityExtensions[em.name]
    if (list != null) {
      for (m in list) {
        m.afterBigChange(em)
      }
    }
    for (m in allExtensions) {
      m.afterBigChange(em)
    }
    
    // TODO ScriptEntityExt.afterBigChange(em)
  }
  
  @Suppress("UNUSED_PARAMETER")
  private fun extHumanUserCreateOrUpdate(em: EntityMeta, evList: List<EntityValue>) {
    for (ev in evList) {
      var password = ev["password"] as String? ?: ""
      password = password.trim()
      if (password.isNotBlank()) {
        checkBeforeChangeUserPassword(password)
        ev["password"] = UserService.hashPassword(password)
        ev["pwdSetOn"] = Date()
        ev["pwdErrCount"] = 0
      } else {
        ev.remove("password")
      }
    }
  }
  
  // 校验密码强度
  private fun checkBeforeChangeUserPassword(password: String) {
    val pwdMinLen = BzConfigManager.getByPathAsLong("ScUserSecurity", "pwdMinLen") ?: 1
    val pwdMaxLen = BzConfigManager.getByPathAsLong("ScUserSecurity", "pwdMaxLen") ?: 20
    if (password.length !in pwdMinLen..pwdMaxLen) {
      throw BzError("errPwdLengthExceedLimit", pwdMinLen, pwdMaxLen)
    }
    
    // Strong, Moderate, Weak
    val pwdStrengthLevel = BzConfigManager.getByPathAsString("ScUserSecurity", "pwdStrengthLevel") ?: "Weak"
    val (pattern, requirement) = when (pwdStrengthLevel) {
      "Strong" -> Pair("^(?=.*\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[^\\da-zA-Z\\s]).{12,}\$", lo("pwdRequirementStrong"))
      "Moderate" -> Pair("^(?=.*[a-zA-Z])(?=.*\\d).{8,}\$", lo("pwdRequirementModerate"))
      else -> Pair("^.{1,}\$", lo("pwdRequirementWeak"))
    }
    val regex = pattern.toRegex()
    if (!regex.matches(password)) {
      throw BzError("errPwdStrengthNotAllow", pwdStrengthLevel, requirement)
    }
  }
}