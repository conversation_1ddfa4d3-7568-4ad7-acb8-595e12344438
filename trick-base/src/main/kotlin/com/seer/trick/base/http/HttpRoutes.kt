package com.seer.trick.base.http

import com.seer.trick.base.BaseCenter
import com.seer.trick.base.http.handler.*

object HttpRoutes {

  fun registerHandlers() {
    BaseHandler.registerHandlers()

    if (!BaseCenter.baseConfig.noServerFileService) {
      ServerFileHandler.registerHandlers()
    }

    FileHandler.registerHandlers()
    LogFileHandler.registerHandlers()

    MetaHandler.registerHandlers()

    UserHandler.registerHandlers()

    WwxHandler.registerHandlers()
    FeiShuHandler.registerHandlers()
    DingTalkHandler.registerHandlers()

    EntityHandler.registerHandlers()
    EntityExportHandler.registerHandlers()
    EntityImportHandler.registerHandlers()
    EntitySyncHandler.registerHandlers()
    EntityCacheHandler.registerHandlers()
    ExtCallHttpHandler.registerHandlers()
    EntityCommentHandler.registerHandlers()

    UserAlertHandler.registerHandlers()

    ScriptHandler.registerHandlers()

    SimpleStatsValueHandler.registerHandlers()

    DbHandler.registerHandlers()

    StatsHandler.registerHandlers()
    StatsManilaHandler.registerHandlers()
    SocHandler.registerHandlers()

    AlarmHandler.registerHandlers()

    PollingJobHandler.registerHandlers()
  }
}