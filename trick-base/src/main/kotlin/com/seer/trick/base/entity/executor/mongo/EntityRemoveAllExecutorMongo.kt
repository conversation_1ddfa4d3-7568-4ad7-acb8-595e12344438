package com.seer.trick.base.entity.executor.mongo

import com.seer.trick.Cq
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.FieldType
import com.seer.trick.base.entity.executor.EntityRemoveWorkContext

object EntityRemoveAllExecutorMongo {

  fun execute(ctx: EntityRemoveWorkContext, entityMeta: EntityMeta) {
    processEntity(ctx, entityMeta)

    for (entityName in ctx.removing.keys) {
      val query = ctx.removing[entityName]
      val em = BaseCenter.mustGetEntityMeta(entityName)
      if (query != null) MongoExecutor.deleteMany(em, query)
    }
  }

  private fun processEntity(ctx: EntityRemoveWorkContext, em: EntityMeta) {
    ctx.removing[em.name] = Cq.all()
    for (fm in em.fields.values) {
      if (fm.type == FieldType.Component) {
        val comEm = BaseCenter.mustGetRefEntityMeta(fm)
        processEntity(ctx, comEm)
      }
    }
  }

}