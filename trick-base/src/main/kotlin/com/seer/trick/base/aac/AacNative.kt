package com.seer.trick.base.aac

import org.apache.commons.codec.digest.DigestUtils

class AacNative {

  companion object {

    private const val FP_FP =
      "901511E7F629B352403EDC2B5AF322152" +
        "32371D3F6364A5C02D8A99B98C33D" +
        "33BF59F0523EB68A60B9F0A79F02AB37C605D66" +
        "A349B4F6B86A21C28CF0CEA176CF"

    fun ffp(fp: String): String = DigestUtils.sha512Hex(fp + FP_FP)

    fun bl(f1: String, f2: String, f3: String): String = DigestUtils.sha512Hex(f1 + FP_FP + f2 + FP_FP + f3)
  }
}