package com.seer.trick.base.script

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.ComplexQuery
import com.seer.trick.base.MapToAnyNull
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.*
import com.seer.trick.base.stats.ChartGroup
import com.seer.trick.base.stats.LastDaysParams
import com.seer.trick.base.stats.StatsService
import com.seer.trick.base.stats.manila.ManilaReportService
import com.seer.trick.base.stats.manila.domain.ManilaReportCfg
import com.seer.trick.helper.JsonHelper

object ScriptStats {
  
  fun addStatsGroup(gStr: String) {
    val g = JsonHelper.mapper.readValue(gStr, ChartGroup::class.java)
    StatsService.addGroup(g)
  }
  
  fun buildLastDaysParams(days: Int): LastDaysParams = StatsService.buildLastDaysParams(days)
  
  fun aggregateQuery(
    tc: ScriptTraceContext,
    entityName: String,
    query: ComplexQuery,
    ao: AggregationOptions,
  ): List<EntityValue> = EntityStatsService.aggregateQuery(entityName, query, ao)
  
  fun buildAggOptions(
    advancedFields: List<AdvancedField>,
    sort: List<String>,
    groupBy: List<GroupByField>,
  ): AggregationOptions = AggregationOptions(advancedFields, sort, groupBy)
  
  fun buildQueryField(functionName: AggFun? = null, name: String, alias: String): AdvancedField =
    AdvancedField(functionName, name, alias)
  
  fun buildGroupByField(name: String, alias: String, statType: StatisticDateType?): GroupByField =
    GroupByField(name, alias, statType)
  
  /**
   * 注册一个统计科目
   */
  fun registerCfg(tc: ScriptTraceContext, cfg: MapToAnyNull) {
    val newCfg = cfg.toMutableMap()
    val converters = newCfg["converters"]
    if (converters is Map<*, *>) {
      converters.forEach { (_, funcName) ->
        funcName?.let {
          val func = fun(v: Any?): Any? = ScriptCenter.execute(ScriptExeRequest(it as String, arrayOf(v)))
          // 注册转换器
          ManilaReportService.registerConverter(funcName as String, func)
        }
      }
    }
    val config: ManilaReportCfg = JsonHelper.mapper.convertValue(newCfg, jacksonTypeRef())
    ManilaReportService.registerCfg(config)
  }
}

object Agg {
  
  fun count(): AggFun = AggFun.COUNT
  
  fun max(): AggFun = AggFun.MAX
  
  fun min(): AggFun = AggFun.MIN
  
  fun sum(): AggFun = AggFun.SUM
  
  fun avg(): AggFun = AggFun.AVG
}

object StatType {
  fun hour(): StatisticDateType = StatisticDateType.Hour
  
  fun day(): StatisticDateType = StatisticDateType.Day
  
  fun month(): StatisticDateType = StatisticDateType.Month
  
  fun year(): StatisticDateType = StatisticDateType.Year
}