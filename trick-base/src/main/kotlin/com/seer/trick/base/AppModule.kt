package com.seer.trick.base

import org.graalvm.polyglot.Source
import org.graalvm.polyglot.Value

abstract class AppModule {

  /**
   * 在读完配置、字典后调用
   */
  open fun init() {
  }

  open fun registerHttpHandlers() {
  }

  open fun registerStatsChart() {}

  open fun afterDb() {
  }

  open fun registerMoreBp() {
  }

  open fun beforeScript() {
  }

  open fun afterScript() {
  }

  open fun afterHttp() {
  }

  open fun loadMoreScripts(sources: MutableList<Source>) {
  }

  open fun putMoreScriptBindings(bindings: Value) {
  }

  open fun dispose() {
  }
}