package com.seer.trick.base.entity.executor

import com.seer.trick.ComplexQuery
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.DbType
import com.seer.trick.base.db.DbManager
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.executor.mongo.*
import com.seer.trick.base.entity.executor.sql.*
import com.seer.trick.base.entity.service.AggregationOptions
import com.seer.trick.base.entity.service.FindOptions

/**
 * 执行实体粒度的增删改查，可能涉及多个底层的表/集合。
 * 根据数据库是 MongoDB 还是 SQL 类数据库，分流到两种实现
 * 实体跟表/集合是一对多关系，字段类型跟数据库类型也需要转换
 */
object EntityExecutor {

  // onlyRelatedTable: 只插入关联表，不插入自己
  fun create(entityMeta: EntityMeta, entityValues: List<EntityValue>) {
    if (BaseCenter.baseConfig.db.type == DbType.MongoDB) {
      EntityCreateExecutorMongo.execute(EntityCreateWorkContext(), entityMeta, entityValues, false)
    } else {
      EntityCreateExecutorSql.execute(EntityCreateWorkContext(), entityMeta, entityValues, false)
    }
  }

  fun update(entityMeta: EntityMeta, targetIds: List<String>, update: EntityValue) {
    if (BaseCenter.baseConfig.db.type == DbType.MongoDB) {
      EntityUpdateExecutorMongo.execute(EntityUpdateWorkContext(), entityMeta, targetIds, update)
    } else {
      EntityUpdateExecutorSql.execute(EntityUpdateWorkContext(), entityMeta, targetIds, update)
    }
  }

  fun remove(entityMeta: EntityMeta, targetIds: List<String>) {
    if (BaseCenter.baseConfig.db.type == DbType.MongoDB) {
      EntityRemoveExecutorMongo.execute(EntityRemoveWorkContext(), entityMeta, targetIds, null)
    } else {
      EntityRemoveExecutorSql.execute(EntityRemoveWorkContext(), entityMeta, targetIds, null)
    }
  }

  fun removeAll(entityMeta: EntityMeta) {
    if (BaseCenter.baseConfig.db.type == DbType.MongoDB) {
      EntityRemoveAllExecutorMongo.execute(EntityRemoveWorkContext(), entityMeta)
    } else {
      EntityRemoveAllExecutorSql.execute(EntityRemoveWorkContext(), entityMeta)
    }
  }

  fun count(entityMeta: EntityMeta, query: ComplexQuery): Long =
    if (BaseCenter.baseConfig.db.type == DbType.MongoDB) {
      MongoExecutor.count(entityMeta, query)
    } else {
      DbManager.getSqlConnection().use { sc -> SqlExecutor.count(sc, entityMeta.name, query) }
    }

  fun find(entityMeta: EntityMeta, query: ComplexQuery, o: FindOptions? = null): List<EntityValue> {
    val entityValues: List<EntityValue>
    if (BaseCenter.baseConfig.db.type == DbType.MongoDB) {
      entityValues = MongoExecutor.findMany(entityMeta, query, o)
      EntityFindExecutorMongo.execute(EntityFindWorkContext(entityMeta, entityValues))
    } else {
      val o2 = fixProjection(o)
      entityValues = DbManager.getSqlConnection().use { sc -> SqlExecutor.findMany(sc, entityMeta.name, query, o2) }
      EntityFindExecutorSql.execute(EntityFindWorkContext(entityMeta, entityValues))
    }
    return entityValues
  }

  /**
   * 必须包含 id，否则 mustGetId 会报错
   */
  private fun fixProjection(o: FindOptions?): FindOptions? {
    if (o == null) return null
    if (o.projection.isNullOrEmpty()) return o
    if (!o.projection.contains("id")) {
      val ps = o.projection.toMutableList()
      ps += "id"
      return o.copy(projection = ps)
    }
    return o
  }

  /**
   * 聚合查询，支持 SUM、AVG、MAX、MIN、COUNT 聚合函数
   *
   * 示例：
   val ao = AggregationOptions(
   aggregationFields = listOf(AggregationField("qty", "qtyCount", AggregationFunction.COUNT)),
   sort = listOf(SortField("qtyCount", true)),
   queryFields = listOf("createdBy"),
   groupBy = listOf(GroupByField("createdOn", "newCreatedOn", StatisticDateType.Day))
   )
   val aggregateQuery = aggregateQuery(entityName, query, ao)
   println(aggregateQuery)
   等同于：
   SELECT COUNT(`qty`) AS qtyCount, DATE(createdOn) AS newCreatedOn, createdBy FROM FbInvLayout GROUP BY DATE(createdOn), createdBy ORDER BY qtyCount ASC
   *
   */
  fun aggregateQuery(
    
    entityMeta: EntityMeta,
    query: ComplexQuery,
    ao: AggregationOptions,
  ): List<EntityValue> = if (BaseCenter.baseConfig.db.type == DbType.MongoDB) {
    MongoExecutor.aggregateQuery(entityMeta, query, ao)
  } else {
    DbManager.getSqlConnection().use { sc -> SqlExecutor.aggregateQuery(sc, entityMeta.name, query, ao) }
  }
}