package com.seer.trick.base.http.handler

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.*
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.operator
import com.seer.trick.base.user.PermissionManager
import com.seer.trick.helper.DateHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.NumHelper
import com.seer.trick.helper.XlsHelper
import io.javalin.http.Context
import org.slf4j.Logger
import org.slf4j.LoggerFactory

object EntityImportHandler {

  private val logger: Logger = LoggerFactory.getLogger(javaClass)

  fun registerHandlers() {
    val c = Handlers("api/entity")
    c.post("import", ::importXlsx, auth())
  }

  private fun importXlsx(ctx: Context) {
    val op = ctx.operator()

    val file = ctx.uploadedFile("f0") ?: throw BzError("errNoUploadedFile", "f0")

    val configStr = ctx.formParam("config")
    if (configStr.isNullOrBlank()) throw BzError("errNoConfig")
    val config: ImportExcelConfig = JsonHelper.mapper.readValue(configStr, jacksonTypeRef())

    val em = BaseCenter.mustGetEntityMeta(config.entityName)
    val lineFm = em.fields[FieldMeta.FIELD_LINES]
    val lineEm = if (lineFm != null) BaseCenter.mustGetRefEntityMeta(lineFm) else null

    if (!PermissionManager.pCreate(em, op)) throw Error403("没有创建权：${em.label}")
    if (!PermissionManager.pEdit(em, op)) throw Error403("没有编辑权：${em.label}")

    val tableHeader: MutableList<String> = ArrayList()
    val columnMappings: MutableList<ImportMapping?> = ArrayList()
    val entityList: MutableList<EntityValue> = ArrayList()
    val entityMap: MutableMap<String, EntityValue> = HashMap()

    val headFieldNames = mutableSetOf<String>()
    val lineFieldNames = mutableSetOf<String>()

    XlsHelper.importXls(file.content()) { rowIndex, row ->
      if (rowIndex == 0) {
        val ci = row.cellIterator()
        while (ci.hasNext()) {
          val cell = ci.next()
          val label = cell.stringCellValue
          tableHeader += label
          var fm = lineEm?.fields?.values?.find { it.label == label }
          if (fm != null) {
            columnMappings += ImportMapping(fm, true)
            lineFieldNames += fm.name
            continue
          }
          fm = em.fields.values.find { it.label == label }
          if (fm != null) {
            columnMappings += ImportMapping(fm, false)
            headFieldNames += fm.name
            continue
          }
          columnMappings += null
        }
        logger.debug("Excel 导入业务对象，表格头 {}", tableHeader)
      } else if (rowIndex > 0) {
        val headEv: EntityValue = HashMap()
        val lineEv: EntityValue = HashMap()

        val ci = row.cellIterator()
        while (ci.hasNext()) {
          val cell = ci.next()

          val mapping = columnMappings.getOrNull(cell.columnIndex) ?: continue
          val fm = mapping.fm
          if (fm.scale != FieldScale.Single) continue
          val v = parseCellValue(fm, XlsHelper.toJavaValue(cell))
          if (mapping.line) {
            lineEv[fm.name] = v
          } else {
            headEv[fm.name] = v
          }
        }

        // 带 id 和不带 id 的
        val evId = headEv["id"] as String?
        if (evId.isNullOrBlank()) {
          // 如果不带 id 且有 btLines 字段,则是单据,数据塞到单行中
          if (lineFm != null) {
            headEv[FieldMeta.FIELD_LINES] = if (lineFm.scale == FieldScale.Single) lineEv else mutableListOf(lineEv)
          } else {
            headEv.putAll(lineEv)
          }
          entityList += headEv
        } else {
          val oldEv = entityMap[evId]
          if (oldEv != null) {
            val lines: MutableList<EntityValue> = EntityHelper.getLines(oldEv, FieldMeta.FIELD_LINES)?.toMutableList()
              ?: ArrayList()
            lines += lineEv
            oldEv[FieldMeta.FIELD_LINES] = lines
          } else {
            headEv[FieldMeta.FIELD_LINES] = if (lineFm?.scale == FieldScale.Single) lineEv else mutableListOf(lineEv)
            entityList += headEv
            entityMap[evId] = headEv
          }
        }
      }
    }

    // TODO 检查行是否有效
    logger.info("导入 Excel，实体 '${config.entityName}' 数量=${entityList.size}，带 ID 数量=${entityMap.size}")
    if (entityList.isNotEmpty()) {
      // 采用删除重新插入的方式
      val oldIds = entityMap.keys.toList()
      if (oldIds.isNotEmpty()) {
        EntityRwService.removeMany(config.entityName, Cq.include("id", oldIds))
      }
      EntityRwService.createMany(config.entityName, entityList)
    }

    ctx.status(200)
  }

  private fun parseCellValue(fm: FieldMeta, v: Any?): Any? {
    return when (fm.type) {
      FieldType.String, FieldType.Reference -> {
        // 虽然 Excel 里已设置文本类型，但 cell 仍是 NUMERIC
        val str = v?.toString()
        // 是否是数字，是的话去掉小数点，有些物料尾号就是 .0
        val pattern = "^-?\\d+(\\.\\d+)?$".toRegex()
        if (str != null && pattern.matches(str) && str.endsWith(".0")) str.substring(0, str.length - 2) else str
      }

      FieldType.Boolean -> v == 1 || v == "Y"
      FieldType.Int -> NumHelper.anyToInt(v)
      FieldType.Long -> NumHelper.anyToInt(v)
      FieldType.Float -> NumHelper.anyToDouble(v)
      FieldType.Date, FieldType.Time, FieldType.DateTime -> DateHelper.anyToDate(v)
      else -> null
    }
  }
}

data class ImportExcelConfig(
  val entityName: String = "",
)

data class ImportMapping(
  val fm: FieldMeta,
  val line: Boolean,
)