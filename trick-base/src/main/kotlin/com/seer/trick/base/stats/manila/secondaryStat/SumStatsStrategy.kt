package com.seer.trick.base.stats.manila.secondaryStat

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.StatisticDateType
import com.seer.trick.base.stats.manila.ManilaReportService.formatPeriod
import com.seer.trick.base.stats.manila.domain.ManilaReportCfg
import java.util.*

class SumStatsStrategy : SecondaryStatsStrategy() {
  override fun execute(cfg: ManilaReportCfg, baseReports: List<EntityValue>) {
    val result = mutableMapOf<StatisticDateType, MutableMap<String, Double>>()
    baseReports.forEach { record ->
      val target = record["target"] as String
      val date = dateFormat.parse(record["finishedOn"] as String)
      val value = record["value"].toString().toDouble()
      aggregateByPeriod(result, target, date, value, StatisticDateType.Week)
      aggregateByPeriod(result, target, date, value, StatisticDateType.Month)
      aggregateByPeriod(result, target, date, value, StatisticDateType.Quarter)
      aggregateByPeriod(result, target, date, value, StatisticDateType.Year)
    }
    result.forEach { (periodType, data) ->
      data.forEach { (targetAndPeriod, totalValue) ->
        saveStatResult(cfg, periodType, targetAndPeriod, totalValue)
      }
    }
  }

  private fun aggregateByPeriod(
    result: MutableMap<StatisticDateType, MutableMap<String, Double>>,
    target: String,
    date: Date,
    value: Double,
    periodType: StatisticDateType,
  ) {
    val periodKey = "$target#${formatPeriod(date, periodType)}"
    result.computeIfAbsent(periodType) { mutableMapOf() }
      .merge(periodKey, value, Double::plus)
  }
}