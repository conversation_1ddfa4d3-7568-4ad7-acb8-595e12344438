package com.seer.trick.base.entity.executor.mongo

import com.seer.trick.base.entity.EntityValue
import org.bson.Document

object MongoValueConverter {

//  fun documentToEntityValue(doc: Document): EntityValue {
//    val ev: EntityValue = mutableMapOf()
//    for (field in doc.keys) {
//      val fv = doc[field]
//      val fn = if ("_id" == field) "id" else field
//      ev[fn] = fv
//    }
//    return ev
//  }

  fun documentToEntityValue(doc: Document): EntityValue {
    val ev: EntityValue = mutableMapOf()
    for (field in doc.keys) {
      val value = doc[field]
      val newFieldName = if (field == "_id") "id" else field

      when (value) {
        is Document -> {
          // 递归处理嵌套文档
          ev[newFieldName] = documentToEntityValue(value)
        }

        is List<*> -> {
          // 处理 List 中的 Document
          ev[newFieldName] = value.map { item ->
            if (item is Document) documentToEntityValue(item) else item
          }
        }

        else -> {
          ev[newFieldName] = value
        }
      }
    }
    return ev
  }

  fun entityValueToDocument(ev: EntityValue): Document {
    val doc = Document()
    for (fn in ev.keys) {
      val fv = ev[fn]
      val field = if ("id" == fn) "_id" else fn
      doc[field] = fv
    }
    return doc
  }
}