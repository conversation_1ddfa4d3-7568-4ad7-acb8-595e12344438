package com.seer.trick.base.entity.executor.mongo

import com.mongodb.client.model.Filters
import com.seer.trick.*
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.FieldScale
import com.seer.trick.base.entity.FieldType
import com.seer.trick.helper.DateHelper
import org.apache.commons.lang3.time.DateUtils
import org.bson.Document
import org.bson.conversions.Bson
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.Date
import java.util.regex.Pattern

object QueryToDocument {

  fun queryToDocument(query: ComplexQuery, entityMeta: EntityMeta): Bson =
    queryToDocumentOrNull(query, entityMeta) ?: Document()

  private fun queryToDocumentOrNull(query: ComplexQuery, entityMeta: EntityMeta): Bson? =
    when (query.type) {
      ComplexQueryType.All -> {
        null
      }

      ComplexQueryType.General -> {
        generalQueryToDocument(query, entityMeta)
      }

      ComplexQueryType.Compound -> {
        compoundQueryToDocument(query, entityMeta)
      }
    }

  private fun generalQueryToDocument(query: ComplexQuery, em: EntityMeta): Bson {
    val mongoField = if ("id" == query.field1) "_id" else query.field1
    if (mongoField.isNullOrEmpty()) throw BzError("errComplexQueryMissingField1")

    if (query.operator == null) throw BzError("errComplexQueryMissingOp")

    var value = query.value
    if (value != null && value.javaClass.isEnum) {
      value = value.toString()
    }

    val fm = EntityHelper.mustGetFm(em, query.field1!!)

    return if (query.operator == ComplexQueryOperator.Eq) {
      // Eq true：当且仅当为 true
      // Eq false: 包括 false, null, undefined
      // 不能检查 null，因为可能就是等于 null
      if (fm.type == FieldType.Boolean && value == false) {
        Filters.ne(mongoField, true)
      } else if (fm.type == FieldType.Date && value is Date) {
        Filters.and(
          Filters.gte(mongoField, value),
          Filters.lt(mongoField, adjustDate(value, days = 1)),
        )
      } else if (fm.type == FieldType.DateTime && value is Date) {
        Filters.and(
          Filters.gte(mongoField, value),
          Filters.lt(mongoField, adjustDate(value, seconds = 1)),
        )
      } else {
        Filters.eq(mongoField, value)
      }
    } else if (query.operator == ComplexQueryOperator.Ne) {
      // Ne true：不为 true，包括 false, null, undefined
      // Ne false: 当且仅当为 true
      // 不能检查 null，因为可能就是不等于 null
      if (fm.type == FieldType.Boolean && value == false) {
        Filters.eq(mongoField, true)
      } else if (fm.type == FieldType.Date && value is Date) {
        Filters.or(
          Filters.gte(mongoField, adjustDate(value, days = 1)),
          Filters.lt(mongoField, value),
        )
      } else if (fm.type == FieldType.DateTime && value is Date) {
        Filters.or(
          Filters.gt(mongoField, adjustDate(value, seconds = 1)),
          Filters.lte(mongoField, value),
        )
      } else {
        Filters.ne(mongoField, value)
      }
    } else if (query.operator == ComplexQueryOperator.Gt) {
      if (value == null) throw BzError("errComplexQueryValueNull", mongoField)
      Filters.gt(mongoField, value)
    } else if (query.operator == ComplexQueryOperator.Gte) {
      if (value == null) throw BzError("errComplexQueryValueNull", mongoField)
      Filters.gte(mongoField, value)
    } else if (query.operator == ComplexQueryOperator.Lt) {
      if (value == null) throw BzError("errComplexQueryValueNull", mongoField)
      Filters.lt(mongoField, value)
    } else if (query.operator == ComplexQueryOperator.Lte) {
      if (value == null) throw BzError("errComplexQueryValueNull", mongoField)
      Filters.lte(mongoField, value)
    } else if (query.operator == ComplexQueryOperator.In) {
      if (value == null) throw BzError("errComplexQueryValueNull", mongoField)
      Filters.`in`(mongoField, value as List<*>)
    } else if (query.operator == ComplexQueryOperator.Between) {
      if (value == null) throw BzError("errComplexQueryValueNull", mongoField)
      val list = value as List<*>
      if (list.size != 2) throw BzError("errComplexQueryValueNeedTwo", mongoField)
      Filters.and(Filters.gte(mongoField, list[0]!!), Filters.lte(mongoField, list[1]!!))
    } else if (query.operator == ComplexQueryOperator.Contain) {
      if (value == null) throw BzError("errComplexQueryValueNull", mongoField)
      if (value !is String) throw BzError("errComplexQueryValueNotString", mongoField)
      Filters.regex(mongoField, Pattern.quote(value as String?))
    } else if (query.operator == ComplexQueryOperator.ContainIgnoreCase) {
      if (value == null) throw BzError("errComplexQueryValueNull", mongoField)
      if (value !is String) throw BzError("errComplexQueryValueNotString", mongoField)
      Filters.regex(mongoField, Pattern.quote(value as String?), "i")
    } else if (query.operator == ComplexQueryOperator.Start) {
      if (value == null) throw BzError("errComplexQueryValueNull", mongoField)
      if (value !is String) throw BzError("errComplexQueryValueNotString", mongoField)
      Filters.regex(mongoField, "^" + Pattern.quote(value as String?))
    } else if (query.operator == ComplexQueryOperator.End) {
      if (value == null) throw BzError("errComplexQueryValueNull", mongoField)
      if (value !is String) throw BzError("errComplexQueryValueNotString", mongoField)
      Filters.regex(mongoField, Pattern.quote(value as String?) + "$")
    } else if (query.operator == ComplexQueryOperator.Null) {
      Filters.eq<Any?>(mongoField, null)
    } else if (query.operator == ComplexQueryOperator.NotNull) {
      Filters.ne<Any?>(mongoField, null)
    } else if (query.operator == ComplexQueryOperator.Empty) {
      // 如果字段不存在或为 null，则 $ne: null 都为 false
      if (fm.scale == FieldScale.List) {
        Filters.or(Filters.eq(mongoField, null), Filters.size(mongoField, 0))
      } else if (fm.type == FieldType.String || fm.type == FieldType.Reference) {
        Filters.or(Filters.eq(mongoField, null), Filters.eq(mongoField, ""))
      } else {
        Filters.eq(mongoField, null)
      }
    } else if (query.operator == ComplexQueryOperator.NotEmpty) {
      if (fm.scale == FieldScale.List) {
        Filters.and(Filters.not(Filters.size(mongoField, 0)), Filters.ne(mongoField, null))
      } else if (fm.type == FieldType.String || fm.type == FieldType.Reference) {
        Filters.and(Filters.ne(mongoField, null), Filters.ne(mongoField, ""))
      } else {
        Filters.ne(mongoField, null)
      }
    } else if (query.operator == ComplexQueryOperator.CurrentUser) {
      throw IllegalArgumentException("Op 'CurrentUser' no supported in handlers")
    } else if (query.operator == ComplexQueryOperator.CurrentUsername) {
      throw IllegalArgumentException("Op 'CurrentUsername' no supported in handlers")
    } else if (query.operator == ComplexQueryOperator.ThisDay) {
      val todayStart = DateHelper.getDayStart(Date())
      val todayEnd = DateHelper.getDayEnd(Date())
      return Filters.and(Filters.gte(mongoField, todayStart), Filters.lte(mongoField, todayEnd))
    } else if (query.operator == ComplexQueryOperator.ThisWeek) {
      val thisMonday = DateHelper.getMondayOfWeek(Date())
      val nextMonday = DateUtils.addDays(thisMonday, 7)
      return Filters.and(Filters.gte(mongoField, thisMonday), Filters.lt(mongoField, nextMonday))
    } else {
      throw BzError("errComplexQueryBadOp", query.operator)
    }
  }

  // 定义泛用的日期修改函数，包括加/减天数和小时数
  fun adjustDate(date: Date, days: Long = 0, seconds: Long = 0): Date {
    // 将 Date 转换为 LocalDateTime
    val localDateTime = date.toLocalDateTime()

    // 调整日期，使用内置的 plusDays/plusHours 或 minusDays/minusHours
    val adjustedDateTime = localDateTime
      .plusDays(days)
      .plusSeconds(seconds)

    // 返回调整后的 Date
    return adjustedDateTime.toDate()
  }

  // 扩展函数，将 java.util.Date 转成 LocalDateTime
  fun Date.toLocalDateTime(): LocalDateTime = this.toInstant()
    .atZone(ZoneId.systemDefault())
    .toLocalDateTime()

  // 扩展函数，将 LocalDateTime 转回 java.util.Date
  fun LocalDateTime.toDate(): Date = Date.from(this.atZone(ZoneId.systemDefault()).toInstant())

  private fun compoundQueryToDocument(query: ComplexQuery, entityMeta: EntityMeta): Bson? {
    if (query.items.isNullOrEmpty()) return null
    val documents: List<Bson> = query.items!!.mapNotNull { queryToDocumentOrNull(it, entityMeta) }
    if (documents.isEmpty()) return null
    return if (query.or) {
      Filters.or(documents)
    } else {
      Filters.and(documents)
    }
  }

  fun projectionToDocument(projection: List<String>?): Document {
    val doc = Document()
    if (projection.isNullOrEmpty()) return doc
    for (p in projection) {
      val sign = if (p.startsWith("-")) -1 else 1
      var field = if (p.startsWith("-") || p.startsWith("+")) p.substring(1) else p
      if ("id" == field) field = "_id"
      doc[field] = sign
    }
    return doc
  }

  fun sortToDocument(sort: List<String>?): Document {
    val doc = Document()
    if (sort.isNullOrEmpty()) return doc
    for (s in sort) {
      val sign = if (s.startsWith("-")) -1 else 1
      var field = if (s.startsWith("-") || s.startsWith("+")) s.substring(1) else s
      if (field.isBlank()) continue
      if ("id" == field) field = "_id"
      doc[field] = sign
    }
    return doc
  }
}