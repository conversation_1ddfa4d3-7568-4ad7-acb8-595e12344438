package com.seer.trick.base.entity.service.change

import java.util.concurrent.ConcurrentHashMap

object EntityChangeManagers {
  
  private val managers: MutableMap<String, EntityChangeManager> = ConcurrentHashMap()
  
  @Synchronized
  fun ensureManager(entityName: String): EntityChangeManager {
    // TODO check entity name
    var m = managers[entityName]
    if (m != null) return m
    m = EntityChangeManager(entityName)
    managers[entityName] = m
    return m
  }
  
}
