package com.seer.trick.base.entity

import com.seer.trick.BzError

object EntityHelper {

  fun mustGetId(ev: EntityValue): String {
    return ev["id"] as String
  }

  fun mustGetLineNo(ev: EntityValue): Int {
    return ev[FieldMeta.FIELD_LINE_NO] as Int
  }

  @Suppress("UNCHECKED_CAST")
  fun getLines(ev: EntityValue, fieldName: String): List<EntityValue>? {
    val fieldValue = ev[fieldName]

    return if (fieldValue == null) {
      null
    } else {
      val list = mutableListOf<EntityValue>()
      if (fieldValue is List<*>) {
        // 多值的组件类型
        list.addAll(fieldValue as List<EntityValue>)
      } else {
        // 单值的组件类型
        list.add(fieldValue as EntityValue)
      }
      list
    }
  }

  fun mustGetLines(ev: EntityValue, fieldName: String): List<EntityValue> {
    val lines = getLines(ev, fieldName)
    if (lines.isNullOrEmpty()) throw BzError("errNoOrderLine", ev["id"])
    return lines
  }

  @Suppress("UNCHECKED_CAST")
  fun getMutableLines(ev: EntityValue, fieldName: String): MutableList<EntityValue>? {
    return ev[fieldName] as MutableList<EntityValue>?
  }

  fun findById(evList: List<EntityValue>?, id: String): EntityValue? {
    return evList?.find { id == mustGetId(it) }
  }

  fun mustGetFm(em: EntityMeta, fn: String): FieldMeta {
    return em.fields[fn] ?: throw BzError("errEntityNoField", em.name, fn)
  }

  fun mustGetLineFm(em: EntityMeta): FieldMeta {
    return em.fields[FieldMeta.FIELD_LINES] ?: throw BzError("errEntityNoField", em.name, FieldMeta.FIELD_LINES)
  }

  fun mustGetLineEntityName(em: EntityMeta): String {
    val fm = em.fields[FieldMeta.FIELD_LINES] ?: throw BzError("errEntityNoField", em.name, FieldMeta.FIELD_LINES)
    val refEntity = fm.refEntity
    if (refEntity.isNullOrBlank()) throw BzError("errRefFieldNoRefEntity", fm.name)
    return refEntity
  }

  @Suppress("UNCHECKED_CAST")
  fun clone(ev: EntityValue): EntityValue {
    val ev2: EntityValue = HashMap(ev.size)
    for ((k, v) in ev) {
      if (v is List<*>) {
        ev2[k] = v.map {
          if (it is Map<*, *>) clone(it as EntityValue) else it
        }
      } else if (v is Map<*, *>) {
        val vv = v as EntityValue
        ev2[k] = clone(vv)
      } else {
        ev2[k] = v
      }
    }
    return ev2
  }

  fun cloneWithNull(ev: EntityValue?): EntityValue? {
    if (ev == null) return null
    return clone(ev)
  }
}