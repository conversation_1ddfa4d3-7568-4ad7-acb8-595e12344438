package com.seer.trick.base.http.client

import okhttp3.Credentials
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import java.util.*


data class HttpRequest(
  val url: String,
  val method: HttpMethod = HttpMethod.Get,
  val contentType: HttpContentType = HttpContentType.Json,
  val reqBody: String? = null,
  val headers: Map<String, String>? = null,
  val basicAuth: Map<String, String>? = null, // Basic Auth 协议……
  val trace: Boolean = false, // 是否记录请求
  val traceReqBody: Boolean = false, // 是否记录请求正文
  val traceResBody: Boolean = false, // 是否记录响应正文 FIXME 未生效
  val reqOn: Date = Date()
) {

  fun toOkRequest(): Request {
    val body = if (reqBody == null) ByteArray(0).toRequestBody()
    else when (contentType) {
      HttpContentType.Json -> reqBody.toRequestBody(JSON)
      HttpContentType.Xml -> reqBody.toRequestBody(XML)
      HttpContentType.Plain -> reqBody.toRequestBody(PLAIN)
    }

    val requestBuilder = Request.Builder().url(url)

    when (method) {
      HttpMethod.Get -> requestBuilder.get()
      HttpMethod.Post -> requestBuilder.post(body)
      HttpMethod.Put -> requestBuilder.put(body)
      HttpMethod.Delete -> requestBuilder.delete(body)
    }

    if (!basicAuth.isNullOrEmpty()) {
      requestBuilder.header(
        "Authorization",
        Credentials.basic(basicAuth["Username"] ?: "", basicAuth["Password"] ?: "")
      )
    }
    if (!headers.isNullOrEmpty()) {
      for ((key, value) in headers) requestBuilder.header(key, value)
    }

    val request = requestBuilder.build()
    return request
  }

  companion object {

    private val JSON = "application/json; charset=utf-8".toMediaType()
    private val XML = "application/xml".toMediaType()
    private val PLAIN = "text/plain".toMediaType()

  }

}

enum class HttpMethod {
  Get, Post, Put, Delete
}

enum class HttpContentType {
  Json, Xml, Plain
}

data class HttpResult(
  @JvmField
  val successful: Boolean = false,
  @JvmField
  val ioError: Boolean = false,
  @JvmField
  val ioErrorMsg: String? = null,
  @JvmField
  val code: Int = 0,
  @JvmField
  val bodyString: String? = null,
  @JvmField
  val checkRes: Boolean? = null, // 校验结果。后端会自动补上，不要手动赋值
  @JvmField
  var checkMsg: String? = null,
)

typealias HttpResultOkChecker = (HttpResult) -> Boolean

data class CallRetryOptions(
  val maxRetryNum: Int = 10,
  val retryDelay: Long = 3000L
)