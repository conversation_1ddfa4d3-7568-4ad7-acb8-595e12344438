package com.seer.trick.base.script

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.base.MapToAnyNull
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.UserNoticeReq
import com.seer.trick.base.entity.service.UserNoticeService
import com.seer.trick.base.failure.FailureRecordReq
import com.seer.trick.base.failure.FailureRecorder
import com.seer.trick.base.soc.SocAttention
import com.seer.trick.base.soc.SocService
import com.seer.trick.base.user.Operator
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.XmlHelper
import org.apache.commons.codec.digest.DigestUtils
import org.graalvm.polyglot.Value
import org.quartz.CronExpression
import org.slf4j.LoggerFactory
import java.nio.charset.Charset
import java.security.MessageDigest
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock
import kotlin.coroutines.cancellation.CancellationException

object ScriptBase {
  
  private val logger = LoggerFactory.getLogger(this::class.java)
  
  private val lockMap: MutableMap<String, ReentrantLock> = ConcurrentHashMap()
  
  private val globalMap: MutableMap<String, Any?> = Collections.synchronizedMap(HashMap())
  
  fun give(a: Value) {
    println(a)
  }
  
  fun testMap(): Map<String, Any?> = mapOf("a" to "A", "1" to "A1")
  
  fun traceContext() : ScriptTraceContext =
    Operator.current()?.let { ScriptTraceContext.from(it) } ?: ScriptTraceContext()
  
  fun logDebug(tc: ScriptTraceContext, message: String) {
    logger.debug(message)
  }
  
  fun logInfo(tc: ScriptTraceContext, message: String) {
    logger.info(message)
  }
  
  fun logError(tc: ScriptTraceContext, message: String, v: Value?) {
    if (v == null) {
      logger.error(message)
    } else {
      // val jsError = toJsError(v)
      // if (jsError != null) {
      //   logger.error(message, jsError.exception)
      //   return
      // }
      
      val throwable = toThrowable(v)
      if (throwable != null) {
        logger.error(message, throwable)
        return
      }
      
      logger.error(message + "\n$v")
    }
  }
  
  // import com.oracle.truffle.js.runtime.builtins.JSErrorObject
  // private fun toJsError(v: Value): JSErrorObject? {
  //   return try {
  //     v.`as`(JSErrorObject::class.java)
  //   } catch (e: Exception) {
  //     null
  //   }
  // }
  
  private fun toThrowable(v: Value): Throwable? = try {
    v.`as`(Throwable::class.java)
  } catch (e: Exception) {
    null
  }
  
  fun throwBzError(code: String, args: Array<Any?>): Unit = throw BzError(code, *args)
  
  fun parseJsonString(str: String): EntityValue? = JsonHelper.mapper.readValue(str, jacksonTypeRef<EntityValue>())
  
  /**
   * TODO 为什么
   */
  fun jsonToString(jo: Any?): String? = if (jo == null) null else JsonHelper.writeValueAsString(jo)
  
  fun xmlToJsonStr(xo: Any?): String? = if (xo == null) null else XmlHelper.xmlToJson(xo).toString()
  
  fun now(): Date = Date()
  
  fun keepTrying(tag: String, description: String, delay: Long, worker: () -> Any) {
    var counter = 1
    while (!Thread.interrupted()) {
      try {
        worker()
        SocService.removeNode("KeepTrying:$tag")
        return
      } catch (e: Exception) {
        SocService.updateNode(
          "任务",
          "KeepTrying:$tag",
          "重试直到成功:$tag",
          "$counter|${e.javaClass.name}: ${e.message}|$description",
          SocAttention.Red,
        )
        counter++
        Thread.sleep(delay)
      }
    }
  }
  
  fun robustExecute(tc: ScriptTraceContext, description: String, funcName: String, args: Any?) {
    ScriptRobustExecutor.submit(description, funcName, args)
  }
  
  fun strToMd5(tc: ScriptTraceContext, data: String): String? = DigestUtils.md5Hex(data)
  
  fun runOnce(tc: ScriptTraceContext, mainId: String, actionId: String, work: () -> EntityValue): EntityValue =
    ScriptRobustExecutor.runOnce(mainId, actionId, work)
  
  fun withLock(lockName: String, action: () -> Unit) {
//    val lock = synchronized(this) {
//      var lock = lockMap[lockName]
//      if (lock == null) {
//        lock = ReentrantLock()
//        lockMap[lockName] = lock
//      }
//      lock
//    }
    val lock = getWithLock(lockName, false)
    lock.withLock(action)
  }
  
  fun scheduledAtFixedDelay(name: String, delay: Long, work: (counter: Long) -> Unit) {
    var forkTaskManageCounter = 0L
    
    while (!Thread.interrupted()) {
      try {
        ++forkTaskManageCounter
        work(forkTaskManageCounter)
      } catch (e: Throwable) {
        val e2 = ScriptCenter.tryToConvertPolyglotException(e)
        if (e2 is CancellationException || e2 is InterruptedException) return
        if (e2 is BzError) {
          logger.error("scheduledAtFixedDelay, $name: ${e2.message}")
        } else {
          logger.error("scheduledAtFixedDelay, $name", e2)
        }
      } finally {
        try {
          Thread.sleep(delay)
        } catch (e: InterruptedException) {
          return
        }
      }
    }
  }
  
  fun scheduledByCron(name: String, cronExpression: String, work: (counter: Long) -> Unit) {
    var forkTaskManageCounter = 0L
    val cron = CronExpression(cronExpression)
    
    while (!Thread.interrupted()) {
      try {
        val now = Date()
        val nextExecution = cron.getNextValidTimeAfter(now)
        val delay = nextExecution.time - now.time
        
        if (delay > 0) {
          Thread.sleep(delay)
        }
        
        ++forkTaskManageCounter
        work(forkTaskManageCounter)
      } catch (e: Throwable) {
        val e2 = ScriptCenter.tryToConvertPolyglotException(e)
        if (e2 is CancellationException || e2 is InterruptedException) return
        if (e2 is BzError) {
          logger.error("scheduledByCron, $name: ${e2.message}")
        } else {
          logger.error("scheduledByCron, $name", e2)
        }
      }
    }
  }
  
  fun assignJavaMapToJsMap(jsMap: MutableMap<String, Any?>, javaMap: Map<String, Any?>) {
    for ((k, v) in javaMap) jsMap[k] = v
  }
  
  fun setScriptButtons(buttonsStr: String) {
    val buttons: List<ScriptButton> = JsonHelper.mapper.readValue(buttonsStr, jacksonTypeRef())
    ScriptButtonService.setButtons(buttons)
  }
  
  fun getGlobalValue(key: String): Any? = globalMap[key]
  
  fun setGlobalValue(key: String, value: Any?) {
    if (value == null) {
      globalMap.remove(key)
    } else {
      globalMap[key] = ScriptHelper.convertToJavaType(value)
    }
  }
  
  fun recordFailure(tc: ScriptTraceContext, failure: MapToAnyNull) {
    val f: FailureRecordReq = JsonHelper.mapper.convertValue(failure, jacksonTypeRef())
    FailureRecorder.addAsync(f)
  }
  
  fun createUserNotice(tc: ScriptTraceContext, req: MapToAnyNull) {
    val req2: UserNoticeReq = JsonHelper.mapper.convertValue(req, jacksonTypeRef())
    UserNoticeService.createUserNotice(req2)
  }
  
  fun createSHA256Tags(tc: ScriptTraceContext, content: String, charset: String): String? {
    // 使用 SHA-256 算法生成哈希值
    val messageDigest = MessageDigest.getInstance("SHA-256")
    val hashBytes = messageDigest.digest(content.toByteArray(Charset.forName(charset)))
    // 将哈希结果进行 Base64 编码
    return Base64.getEncoder().encodeToString(hashBytes)
  }
  
  fun withFairnessLock(lockName: String, fair: Boolean, action: () -> Unit) {
    val lock = getWithLock(lockName, fair)
    lock.withLock(action)
  }
  
  /**
   * @param lockName 锁名称，用同一个 lockMap,所以同一个锁不能既是公平又是非公平，脚本不能混着用
   * @param fair true 公平锁 false 非公平
   * TODO 基于优先级的公平锁，优先级高的优先执行，自定义队列先按优先级、再根据时间（PriorityBlockingQueue），头部先执行，取消任务移除未执行的排队任务
   */
  private fun getWithLock(lockName: String, fair: Boolean): ReentrantLock {
    val lock = synchronized(this) {
      var lock = lockMap[lockName]
      if (lock == null) {
        lock = ReentrantLock(fair)
        lockMap[lockName] = lock
      } else if (lock.isFair != fair) { // 修改锁类型
        lock = ReentrantLock(fair)
        lockMap[lockName] = lock
      }
      lock
    }
    return lock
  }
}