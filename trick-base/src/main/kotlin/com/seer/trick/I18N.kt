package com.seer.trick

import com.seer.trick.base.BaseCenter
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.helper.FileHelper
import com.seer.trick.helper.JsonHelper
import org.apache.commons.io.FileUtils
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import java.io.File
import java.nio.charset.StandardCharsets
import java.text.MessageFormat
import java.util.concurrent.ConcurrentHashMap

object I18N {

  private val logger = LoggerFactory.getLogger(this::class.java)

  private val dict: MutableMap<String, String> = ConcurrentHashMap()

  private val extraUiDictStr: MutableMap<String, String> = ConcurrentHashMap()

  fun lo(key: String, args: List<Any?>? = null, defaultValue: String? = null): String {
    val template = dict[key]
    if (template.isNullOrBlank()) {
      return defaultValue ?: ("[$key]" + (if (args.isNullOrEmpty()) "" else (" $args")))
    }
    if (args.isNullOrEmpty()) return template
    return MessageFormat.format(template, *args.toTypedArray())
  }

  fun load() {
    val lang = lang()

    this.dict.clear()

    // 服务端默认
    var dictStr = FileHelper.loadClasspathResourceAsString("/server-base-$lang.txt")
    var dict = parseDictStr(dictStr)
    this.dict.putAll(dict)

    // 服务端校对
    dictStr = FileHelper.loadClasspathResourceAsString("/server-fix-$lang.txt")
    dict = parseDictStr(dictStr)
    this.dict.putAll(dict)

    // 服务端用户校对
    val serverDict = File(BaseCenter.baseConfig.configDir, "server-fix-$lang.txt")
    if (serverDict.exists()) {
      logger.info("加载到服务器字典文件 server-fix-$lang.txt")
      val dictStr2 = FileUtils.readFileToString(serverDict, StandardCharsets.UTF_8)
      val dict2 = parseDictStr(dictStr2)
      this.dict.putAll(dict2)
    }

    // 界面用户较多
    val uiDict = File(BaseCenter.baseConfig.configDir, "ui-fix-$lang.txt")
    if (uiDict.exists()) {
      logger.info("加载到界面字典文件 ui-fix-$lang.txt")
      val dictStr2 = FileUtils.readFileToString(uiDict, StandardCharsets.UTF_8)
      val dict2 = parseDictStr(dictStr2)
      extraUiDictStr[lang] = JsonHelper.writeValueAsString(dict2)
    }
  }

  private fun parseDictStr(dictStr: String?): Map<String, String> {
    val dict = mutableMapOf<String, String>()
    if (dictStr.isNullOrBlank()) return dict
    val lines = dictStr.lines()
    for (line in lines) {
      if (line.startsWith("[") || line.startsWith("#")) continue
      val pairs = StringUtils.split(line, "=", 2)
      if (pairs.size != 2) continue
      val key = pairs[0].trim()
      val value = pairs[1].trim()
      if (key.isBlank() || value.isBlank()) continue
      dict[key] = value
    }
    return dict
  }

  /**
   * 获取当前使用的语言
   */
  fun lang(): String {
    val lang = BzConfigManager.getByPath("basic", "lang") as String?
    return if (lang.isNullOrBlank()) "zh" else lang
  }

  /**
   * 获取定制的前端字典
   */
  fun mustGetExtraUiDictStr(): String = extraUiDictStr[lang()] ?: ""
}