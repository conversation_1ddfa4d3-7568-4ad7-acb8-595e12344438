{"FbAdvancedShippingNotice": {"name": "FbAdvancedShippingNotice", "label": "到货通知单", "group": "Purchase", "builtin": true, "disabled": true, "type": "Entity", "fields": {"remark": {"name": "remark", "label": "备注", "type": "String", "scale": "Single", "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "vendor": {"name": "vendor", "label": "供应商", "type": "Reference", "scale": "Single", "refEntity": "FbVendor", "inputRequired": true, "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 14, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 23, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 21, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "单号", "type": "String", "scale": "Single", "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 200, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 22, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 25, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 20, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btLines": {"name": "btLines", "label": "单行", "type": "Component", "scale": "List", "refEntity": "FbAdvancedShippingNoticeLine", "copiable": true, "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"input": "ComponentTable", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 24, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btOrderState": {"name": "btOrderState", "label": "状态", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "Init", "label": "未提交", "color": "#888"}, {"value": "Committed", "label": "已提交", "color": "#EFAF46"}, {"value": "Finished", "label": "已完成", "color": "#2DD92E"}]}, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btOrderStateReason": {"name": "btOrderStateReason", "label": "状态说明", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 4, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btOrderKind": {"name": "btOrderKind", "label": "类型", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "Purchase", "label": "采购入库"}]}, "inputRequired": true, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "toWarehouse": {"name": "toWarehouse", "label": "入库仓库", "type": "Reference", "scale": "Single", "refEntity": "FbWarehouse", "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "expectedArrivedOn": {"name": "expectedArrivedOn", "label": "约定到货日期", "type": "Date", "scale": "Single", "inputRequired": true, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "transportBy": {"name": "transportBy", "label": "物流联系人", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "transportByPhone": {"name": "transportByPhone", "label": "物流联系方式", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "transportRemark": {"name": "transportRemark", "label": "物流备注", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "purchaseOrderId": {"name": "purchaseOrderId", "label": "关联采购订单号", "type": "Reference", "scale": "Single", "refEntity": "FbPurchaseOrder", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "qty": {"name": "qty", "label": "本次到货数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sumLineField": "qty", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "purchaseQty": {"name": "purchaseQty", "label": "采购数量", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 15, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "otherArrivedQty": {"name": "otherArrivedQty", "label": "已确认交期数量", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "Hidden", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 16, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "yieldQty": {"name": "yieldQty", "label": "让步接收数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sumLineField": "yieldQty", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 18, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "rejectQty": {"name": "rejectQty", "label": "拒收数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sumLineField": "rejectQty", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 19, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "receivedQty": {"name": "receivedQty", "label": "合格数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sumLineField": "receivedQty", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 17, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"enabled": true, "fixedPrefix": "ASN", "flowNoWidth": 5}, "digest": {"fields": []}, "scale": "Instances", "commentEnabled": true, "trackChange": true, "userNotice": {"create": true, "update": true, "comment": true, "targetUserFields": ["modifiedBy", "created<PERSON>y"]}, "actions": {}, "listStats": {"items": []}, "listCard": {"stateFieldName": "btOrderState", "lines": [[{"type": "Simple", "fieldName": "id", "formatMapping": []}], [{"type": "Simple", "fieldName": "vendor", "formatMapping": []}, {"type": "Simple", "fieldName": "expectedArrivedOn", "prefix": "约定到货日期 ", "formatMapping": []}], [{"type": "Simple", "fieldName": "qty", "prefix": "本次到货数量: ", "formatMapping": []}, {"type": "Simple", "fieldName": "receivedQty", "prefix": "合格数量: ", "formatMapping": []}]]}, "orderConfig": {"enabled": true, "kinds": [{"name": "Purchase", "label": "采购入库"}], "states": [{"id": "Init", "label": "未提交", "entityEditable": true, "color": "#888", "nextStates": [{"id": "Committed", "buttonLabel": "提交", "buttonKind": "primary"}]}, {"id": "Committed", "label": "已提交", "color": "#EFAF46", "nextStates": [{"id": "Finished", "buttonLabel": "完成", "buttonKind": "success"}]}, {"id": "Finished", "label": "已完成", "finalState": true, "color": "#2DD92E", "nextStates": []}], "pushOrderStates": ["Approved", "Committed"], "pushOrders": [{"downOrderName": "FbReceivingOrder", "downOrderKind": "Purchase", "downOrderState": "Init", "headFieldMapping": [{"sourceField": "vendor", "targetField": "vendor"}, {"sourceField": "id", "targetField": "asnId"}, {"sourceField": "purchaseOrderId", "targetField": "purchaseOrderId"}, {"sourceField": "vendor", "targetField": "vendor"}, {"sourceField": "toWarehouse", "targetField": "toWarehouse"}, {"sourceField": "qty", "targetField": "expectedQty"}], "lineFieldMapping": [{"sourceField": "qty", "targetField": "expectedQty"}, {"sourceField": "price", "targetField": "price"}], "downOrderBackStates": ["Commited", "Committed"], "lineWriteBackMapping": [{"downField": "goodQty", "upField": "receivedQty"}, {"downField": "yieldQty", "upField": "yieldQty"}, {"downField": "badQty", "upField": "rejectQty"}]}], "upOrderConfig": {"items": []}, "thisQtyFields": ["qty"], "occurredQtyField": "otherArrivedQty", "planQtyField": "purchaseQty", "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "FbAdvancedShippingNoticeLine": {"name": "FbAdvancedShippingNoticeLine", "label": "到货通知单行", "group": "Purchase", "builtin": true, "disabled": true, "type": "Entity", "fields": {"modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "disabled": true, "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 24, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "disabled": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 20, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 15, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 22, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "price": {"name": "price", "label": "采购价格", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "disabled": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 23, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "disabled": true, "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 19, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "qty": {"name": "qty", "label": "本次到货数量", "type": "Float", "scale": "Single", "inputRequired": true, "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "disabled": true, "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 21, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btParentId": {"name": "btParentId", "label": "所属通知单", "type": "Reference", "scale": "Single", "refEntity": "FbAdvancedShippingNotice", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btLineNo": {"name": "btLineNo", "label": "行号", "type": "Int", "scale": "Single", "copiable": true, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterial": {"name": "btMaterial", "label": "物料", "type": "Reference", "scale": "Single", "refEntity": "FbMaterial", "inputRequired": true, "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 240, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialName": {"name": "btMaterialName", "label": "物料名称", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "name", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialModel": {"name": "btMaterialModel", "label": "物料型号", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "model", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialSpec": {"name": "btMaterialSpec", "label": "物料规格", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "spec", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialCategory": {"name": "btMaterialCategory", "label": "物料分类编号", "type": "String", "scale": "Single", "disabled": true, "refField": "btMaterial", "refFieldField": "leafCategory.id", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialImage": {"name": "btMaterialImage", "label": "物料图片", "type": "Image", "scale": "Single", "refField": "btMaterial", "refFieldField": "image", "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialId": {"name": "btMaterialId", "label": "物料编号", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "id", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialCategoryName": {"name": "btMaterialCategoryName", "label": "物料分类名称", "type": "String", "scale": "Single", "disabled": true, "refField": "btMaterial", "refFieldField": "leafCategory.name", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "purchaseQty": {"name": "purchaseQty", "label": "采购数量", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "otherArrivedQty": {"name": "otherArrivedQty", "label": "已确认交期数量", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "Hidden", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 14, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "yieldQty": {"name": "yieldQty", "label": "让步接收数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 17, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "rejectQty": {"name": "rejectQty", "label": "拒收数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 18, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "receivedQty": {"name": "receivedQty", "label": "合格数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 16, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 0}, "digest": {"fields": []}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"imageFieldName": "btMaterialImage", "lines": [[{"type": "Simple", "fieldName": "btMaterialName", "formatMapping": []}], [{"type": "Simple", "fieldName": "btMaterialId", "suffix": " |", "formatMapping": []}, {"type": "Simple", "fieldName": "btMaterialModel", "suffix": " |", "formatMapping": []}, {"type": "Simple", "fieldName": "btMaterialSpec", "formatMapping": []}], [{"type": "Simple", "fieldName": "qty", "prefix": "数量: ", "formatMapping": []}]]}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "FbBrand": {"name": "FbBrand", "label": "品牌", "group": "MainData", "builtin": true, "disabled": true, "type": "Entity", "fields": {"createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "Date", "scale": "Single", "decimals": 0, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "logo": {"name": "logo", "label": "LOGO", "type": "Image", "scale": "Single", "inputRequired": true, "decimals": 0, "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "name": {"name": "name", "label": "名称", "type": "String", "scale": "Single", "inputRequired": true, "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "单号", "type": "String", "scale": "Single", "inputRequired": true, "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "remark": {"name": "remark", "label": "备注", "type": "String", "scale": "Single", "inputRequired": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "disabled": {"name": "disabled", "label": "禁用", "type": "Boolean", "scale": "Single", "inputRequired": true, "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "Date", "scale": "Single", "decimals": 0, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 0}, "digest": {"fields": ["name"]}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": []}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "FbDock": {"name": "FbDock", "label": "月台", "group": "MainData", "builtin": true, "disabled": true, "type": "Entity", "fields": {"name": {"name": "name", "label": "月台名称", "type": "String", "scale": "Single", "inputRequired": true, "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "type": {"name": "type", "label": "类型", "type": "String", "scale": "Single", "inputRequired": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "state": {"name": "state", "label": "状态", "type": "String", "scale": "Single", "inputRequired": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "disabled": {"name": "disabled", "label": "禁用", "type": "Boolean", "scale": "Single", "inputRequired": true, "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "月台编码", "type": "String", "scale": "Single", "inputRequired": true, "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "Date", "scale": "Single", "decimals": 0, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "zdmc11": {"name": "zdmc11", "label": "用途", "type": "String", "scale": "Single", "inputRequired": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "remark": {"name": "remark", "label": "备注", "type": "String", "scale": "Single", "inputRequired": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "Date", "scale": "Single", "decimals": 0, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 0}, "digest": {"fields": ["name"]}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": []}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "FbMfgFQCOrder": {"name": "FbMfgFQCOrder", "label": "成品质检单", "group": "MES", "builtin": true, "disabled": true, "type": "Entity", "fields": {"remark": {"name": "remark", "label": "备注", "type": "String", "scale": "Single", "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 16, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 14, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "单号", "type": "String", "scale": "Single", "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 200, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 15, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 18, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btLines": {"name": "btLines", "label": "单行", "type": "Component", "scale": "List", "refEntity": "FbMfgFQCOrderLine", "copiable": true, "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"input": "ComponentTable", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 17, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btOrderState": {"name": "btOrderState", "label": "状态", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "Init", "label": "未提交", "color": "#888"}, {"value": "Committed", "label": "已提交", "color": "#EFAF46"}, {"value": "Finished", "label": "已完成", "color": "#2DD92E"}]}, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btOrderStateReason": {"name": "btOrderStateReason", "label": "状态说明", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 4, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btOrderKind": {"name": "btOrderKind", "label": "类型", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "General", "label": "一般成品质检"}]}, "inputRequired": true, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "goodQty": {"name": "goodQty", "label": "合格数量", "type": "Float", "scale": "Single", "decimals": 3, "sumLineField": "goodQty", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "badQty": {"name": "badQty", "label": "不合格数量", "type": "Float", "scale": "Single", "decimals": 3, "sumLineField": "badQty", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "expectedQty": {"name": "expectedQty", "label": "计划生产数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sumLineField": "expectedQty", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "mfgOrderId": {"name": "mfgOrderId", "label": "生产工单号", "type": "Reference", "scale": "Single", "refEntity": "FbMfgOrder", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "checkOn": {"name": "checkOn", "label": "质检日期", "type": "Date", "scale": "Single", "inputRequired": true, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "inboundQty": {"name": "inboundQty", "label": "入库完成数量", "type": "Float", "scale": "Single", "decimals": 3, "sumLineField": "inboundQty", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"enabled": true, "fixedPrefix": "FQC", "flowNoWidth": 5}, "digest": {"fields": []}, "scale": "Instances", "commentEnabled": true, "trackChange": true, "userNotice": {"create": true, "update": true, "comment": true, "targetUserFields": ["modifiedBy", "created<PERSON>y"]}, "actions": {}, "listStats": {"items": []}, "listCard": {"stateFieldName": "btOrderState", "lines": [[{"type": "Simple", "fieldName": "id", "formatMapping": []}], [{"type": "Simple", "fieldName": "expectedQty", "prefix": "应收数量: ", "formatMapping": []}, {"type": "Simple", "fieldName": "goodQty", "prefix": "合格:", "formatMapping": []}, {"type": "Simple", "fieldName": "badQty", "prefix": "不合格:", "formatMapping": []}]]}, "orderConfig": {"enabled": true, "kinds": [{"name": "General", "label": "一般成品质检"}], "states": [{"id": "Init", "label": "未提交", "entityEditable": true, "color": "#888", "nextStates": [{"id": "Committed", "buttonLabel": "提交", "buttonKind": "primary"}]}, {"id": "Committed", "label": "已提交", "color": "#EFAF46", "nextStates": [{"id": "Finished", "buttonLabel": "完成", "buttonKind": "success"}]}, {"id": "Finished", "label": "已完成", "finalState": true, "color": "#2DD92E", "nextStates": []}], "pushOrderStates": ["Approved", "Committed"], "pushOrders": [{"downOrderName": "FbInboundOrder", "downOrderKind": "MfFinishedInbound", "downOrderState": "Init", "headFieldMapping": [{"sourceField": "id", "targetField": "mfgFQCOrderId"}, {"sourceField": "mfgOrderId", "targetField": "mfgOrderId"}], "lineFieldMapping": [], "downOrderBackStates": ["Committed"], "lineWriteBackMapping": [{"downField": "qty", "upField": "inboundQty"}]}], "upOrderConfig": {"items": []}, "thisQtyFields": ["goodQty", "yieldQty", "badQty"], "planQtyField": "expectedQty", "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "FbMfgFQCOrderLine": {"name": "FbMfgFQCOrderLine", "label": "成品质检单行", "group": "MES", "builtin": true, "disabled": true, "type": "Entity", "fields": {"modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "disabled": true, "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 22, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "disabled": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 18, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 19, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "disabled": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 21, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "disabled": true, "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 17, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "disabled": true, "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 20, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btParentId": {"name": "btParentId", "label": "所属质检单", "type": "Reference", "scale": "Single", "refEntity": "FbMfgFQCOrder", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btLineNo": {"name": "btLineNo", "label": "行号", "type": "Int", "scale": "Single", "copiable": true, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterial": {"name": "btMaterial", "label": "物料", "type": "Reference", "scale": "Single", "refEntity": "FbMaterial", "inputRequired": true, "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 240, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialName": {"name": "btMaterialName", "label": "物料名称", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "name", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialModel": {"name": "btMaterialModel", "label": "物料型号", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "model", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialSpec": {"name": "btMaterialSpec", "label": "物料规格", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "spec", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialCategory": {"name": "btMaterialCategory", "label": "物料分类编号", "type": "String", "scale": "Single", "disabled": true, "refField": "btMaterial", "refFieldField": "leafCategory.id", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialImage": {"name": "btMaterialImage", "label": "物料图片", "type": "Image", "scale": "Single", "refField": "btMaterial", "refFieldField": "image", "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialId": {"name": "btMaterialId", "label": "物料编号", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "id", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialCategoryName": {"name": "btMaterialCategoryName", "label": "物料分类名称", "type": "String", "scale": "Single", "disabled": true, "refField": "btMaterial", "refFieldField": "leafCategory.name", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "goodQty": {"name": "goodQty", "label": "合格数量", "type": "Float", "scale": "Single", "inputRequired": true, "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "badQty": {"name": "badQty", "label": "不合格数量", "type": "Float", "scale": "Single", "inputRequired": true, "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 14, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "expectedQty": {"name": "expectedQty", "label": "计划生产数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "inboundQty": {"name": "inboundQty", "label": "入库完成数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 15, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "badReason": {"name": "badReason", "label": "不合格备注", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 16, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 0}, "digest": {"fields": []}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"imageFieldName": "btMaterialImage", "lines": [[{"type": "Simple", "fieldName": "btMaterialName", "formatMapping": []}], [{"type": "Simple", "fieldName": "btMaterialId", "suffix": " |", "formatMapping": []}, {"type": "Simple", "fieldName": "btMaterialModel", "suffix": " |", "formatMapping": []}, {"type": "Simple", "fieldName": "btMaterialSpec", "formatMapping": []}], [{"type": "Simple", "fieldName": "goodQty", "prefix": "合格:", "formatMapping": []}, {"type": "Simple", "fieldName": "badQty", "prefix": "不合格:", "formatMapping": []}]]}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "FbMfgOrder": {"name": "FbMfgOrder", "label": "生产工单", "group": "MES", "builtin": true, "disabled": true, "type": "Entity", "fields": {"remark": {"name": "remark", "label": "备注", "type": "String", "scale": "Single", "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 15, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "单号", "type": "String", "scale": "Single", "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 200, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 14, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btLines": {"name": "btLines", "label": "单行", "type": "Component", "scale": "List", "refEntity": "FbMfgOrderLine", "copiable": true, "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"input": "ComponentTable", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 16, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btOrderState": {"name": "btOrderState", "label": "状态", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "Init", "label": "未提交", "color": "#888"}, {"value": "Committed", "label": "已提交", "color": "#EFAF46"}, {"value": "Approved", "label": "已审核", "color": "#1D9DD6"}, {"value": "Rejected", "label": "已退回", "color": "E776BF"}, {"value": "Finished", "label": "已完成", "color": "#2DD92E"}, {"value": "Aborted", "label": "已作废", "color": "#E776BF"}]}, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btOrderStateReason": {"name": "btOrderStateReason", "label": "状态说明", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 4, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btOrderKind": {"name": "btOrderKind", "label": "类型", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "Product", "label": "一般生产"}]}, "inputRequired": true, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "qty": {"name": "qty", "label": "计划生产总数", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sumLineField": "qty", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "customer": {"name": "customer", "label": "客户", "type": "Reference", "scale": "Single", "refEntity": "FbCustomer", "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "finishedQty": {"name": "finishedQty", "label": "已完工数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sumLineField": "finishedQty", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "inboundQty": {"name": "inboundQty", "label": "已入库数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sumLineField": "inboundQty", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"enabled": true, "fixedPrefix": "MO", "flowNoWidth": 5}, "digest": {"fields": []}, "scale": "Instances", "commentEnabled": true, "trackChange": true, "userNotice": {"create": true, "update": true, "comment": true, "targetUserFields": ["modifiedBy", "created<PERSON>y", "requirementUser"]}, "actions": {}, "listStats": {"items": []}, "listCard": {"stateFieldName": "btOrderState", "lines": [[{"type": "Simple", "fieldName": "id", "formatMapping": []}], [{"type": "Simple", "fieldName": "btOrderKind", "formatMapping": []}, {"type": "Conditional", "fieldName": "created<PERSON>y", "formatMapping": [{"operator": "Other", "alignRight": true}]}, {"type": "Simple", "fieldName": "createdOn", "formatMapping": []}], [{"type": "Simple", "fieldName": "vendor", "formatMapping": []}], [{"type": "Simple", "fieldName": "qty", "prefix": "计划生产数量: ", "formatMapping": []}, {"type": "Simple", "fieldName": "finishedQty", "prefix": "已完工数量: ", "formatMapping": []}]]}, "orderConfig": {"enabled": true, "kinds": [{"name": "Product", "label": "一般生产"}], "states": [{"id": "Init", "label": "未提交", "entityEditable": true, "color": "#888", "nextStates": [{"id": "Committed", "buttonLabel": "提交", "buttonKind": "primary"}]}, {"id": "Committed", "label": "已提交", "color": "#EFAF46", "nextStates": [{"id": "Approved", "buttonLabel": "审核通过", "buttonKind": "success"}, {"id": "Rejected", "buttonLabel": "审核不通过", "buttonKind": "warning", "reasonRequired": true}, {"id": "Aborted", "buttonLabel": "作废", "buttonKind": "error", "reasonRequired": true}]}, {"id": "Approved", "label": "已审核", "color": "#1D9DD6", "nextStates": [{"id": "Finished", "buttonLabel": "完成"}]}, {"id": "Rejected", "label": "已退回", "entityEditable": true, "color": "E776BF", "nextStates": [{"id": "Committed", "buttonLabel": "提交"}]}, {"id": "Finished", "label": "已完成", "finalState": true, "color": "#2DD92E", "nextStates": []}, {"id": "Aborted", "label": "已作废", "finalState": true, "color": "#E776BF", "nextStates": []}], "pushOrderStates": ["Approved"], "pushOrders": [{"downOrderName": "FbMfgFQCOrder", "downOrderKind": "General", "downOrderState": "Committed", "headFieldMapping": [{"sourceField": "inboundQty", "targetField": "inboundQty"}, {"sourceField": "id", "targetField": "mfgOrderId"}], "lineFieldMapping": [{"sourceField": "qty", "targetField": "expectedQty"}], "downOrderBackStates": ["Committed"], "lineWriteBackMapping": [{"downField": "goodQty", "upField": "finishedQty"}]}], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "FbMfgOrderLine": {"name": "FbMfgOrderLine", "label": "生产工单行", "group": "MES", "builtin": true, "disabled": true, "type": "Entity", "fields": {"modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "disabled": true, "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 20, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "disabled": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 16, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 18, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "disabled": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 19, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "disabled": true, "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 15, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "qty": {"name": "qty", "label": "计划生产数量", "type": "Float", "scale": "Single", "inputRequired": true, "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "disabled": true, "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 17, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btParentId": {"name": "btParentId", "label": "所属订单", "type": "Reference", "scale": "Single", "refEntity": "FbMfgOrder", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btLineNo": {"name": "btLineNo", "label": "行号", "type": "Int", "scale": "Single", "copiable": true, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterial": {"name": "btMaterial", "label": "物料", "type": "Reference", "scale": "Single", "refEntity": "FbMaterial", "inputRequired": true, "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 240, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialName": {"name": "btMaterialName", "label": "物料名称", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "name", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialModel": {"name": "btMaterialModel", "label": "物料型号", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "model", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialSpec": {"name": "btMaterialSpec", "label": "物料规格", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "spec", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialCategory": {"name": "btMaterialCategory", "label": "物料分类编号", "type": "String", "scale": "Single", "disabled": true, "refField": "btMaterial", "refFieldField": "leafCategory.id", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialImage": {"name": "btMaterialImage", "label": "物料图片", "type": "Image", "scale": "Single", "refField": "btMaterial", "refFieldField": "image", "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialId": {"name": "btMaterialId", "label": "物料编号", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "id", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialCategoryName": {"name": "btMaterialCategoryName", "label": "物料分类名称", "type": "String", "scale": "Single", "disabled": true, "refField": "btMaterial", "refFieldField": "leafCategory.name", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "finishedQty": {"name": "finishedQty", "label": "已完工数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "inboundQty": {"name": "inboundQty", "label": "已入库数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 14, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 0}, "digest": {"fields": []}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"imageFieldName": "btMaterialImage", "lines": [[{"type": "Simple", "fieldName": "btMaterialName", "formatMapping": []}], [{"type": "Simple", "fieldName": "btMaterialId", "suffix": " |", "formatMapping": []}, {"type": "Simple", "fieldName": "btMaterialModel", "suffix": " |", "formatMapping": []}, {"type": "Simple", "fieldName": "btMaterialSpec", "formatMapping": []}], [{"type": "Simple", "fieldName": "qty", "prefix": "计划生产数量: ", "formatMapping": []}, {"type": "Simple", "fieldName": "finishedQty", "prefix": "已完工数量: ", "formatMapping": []}]]}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "FbProduct": {"name": "FbProduct", "label": "商品", "group": "MainData", "builtin": true, "disabled": true, "type": "Entity", "fields": {"name": {"name": "name", "label": "商品名称", "type": "String", "scale": "Single", "inputRequired": true, "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "category": {"name": "category", "label": "商品分类", "type": "Reference", "scale": "Single", "refEntity": "FbProductCategory", "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "商品编号", "type": "String", "scale": "Single", "inputRequired": true, "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 2, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 8, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 10, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btDisabled": {"name": "btDisabled", "label": "停用", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "image": {"name": "image", "label": "商品主图片", "type": "Image", "scale": "Single", "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "productModelEnabled": {"name": "productModelEnabled", "label": "启用型号管理", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 4}, "digest": {"fields": ["name"]}, "scale": "Instances", "disabledFilter": true, "sort": "id", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"imageFieldName": "image", "lines": [[{"type": "Simple", "fieldName": "name", "formatMapping": []}], [{"type": "Simple", "fieldName": "id", "formatMapping": []}, {"type": "Conditional", "fieldName": "btDisabled", "formatMapping": [{"operator": "Eq", "value": "true", "className": "rui-ev-card-part-tag-disabled", "replaceText": "已停用"}, {"operator": "Other", "notShow": true}]}]]}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "FbProductCategory": {"name": "FbProductCategory", "label": "商品分类", "group": "MainData", "builtin": true, "disabled": true, "type": "Entity", "fields": {"name": {"name": "name", "label": "名称", "type": "String", "scale": "Single", "inputRequired": true, "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "parent": {"name": "parent", "label": "上级分类", "type": "Reference", "scale": "Single", "refEntity": "FbProductCategory", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "分类编号", "type": "String", "scale": "Single", "inputRequired": true, "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 9, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 10, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btDisabled": {"name": "btDisabled", "label": "停用", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "remark": {"name": "remark", "label": "备注", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 4}, "digest": {"fields": ["name"]}, "scale": "Instances", "disabledFilter": true, "sort": "id", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": [[{"type": "Simple", "fieldName": "name", "formatMapping": []}], [{"type": "Simple", "fieldName": "id", "formatMapping": []}, {"type": "Conditional", "fieldName": "btDisabled", "formatMapping": [{"operator": "Eq", "value": "true", "className": "rui-ev-card-part-tag-disabled", "replaceText": "已停用"}, {"operator": "Other", "notShow": true}]}]]}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "FbProductModel": {"name": "FbProductModel", "label": "商品型号", "group": "MainData", "builtin": true, "disabled": true, "type": "Entity", "fields": {"name": {"name": "name", "label": "产品型号名称", "type": "String", "scale": "Single", "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "product": {"name": "product", "label": "所属商品", "type": "Reference", "scale": "Single", "refEntity": "FbProduct", "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "material": {"name": "material", "label": "对应物料", "type": "Reference", "scale": "Single", "refEntity": "FbMaterial", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "spec": {"name": "spec", "label": "规格描述", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "产品型号编号", "type": "String", "scale": "Single", "inputRequired": true, "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 2, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 8, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 10, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btDisabled": {"name": "btDisabled", "label": "停用", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 4}, "digest": {"fields": ["name"]}, "scale": "Instances", "disabledFilter": true, "sort": "id", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"imageFieldName": "image", "lines": [[{"type": "Simple", "fieldName": "name", "formatMapping": []}], [{"type": "Simple", "fieldName": "id", "formatMapping": []}, {"type": "Simple", "fieldName": "product", "formatMapping": []}, {"type": "Conditional", "fieldName": "btDisabled", "formatMapping": [{"operator": "Eq", "value": "true", "className": "rui-ev-card-part-tag-disabled", "replaceText": "已停用"}, {"operator": "Other", "notShow": true}]}]]}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "FbProductSimpleBom": {"name": "FbProductSimpleBom", "label": "产品物料清单", "group": "MainData", "builtin": true, "disabled": true, "type": "Entity", "fields": {"finalMaterial": {"name": "finalMaterial", "label": "产出物料", "type": "Reference", "scale": "Single", "refEntity": "FbMaterial", "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "finalProductModel": {"name": "finalProductModel", "label": "产生商品型号", "type": "Reference", "scale": "Single", "refEntity": "FbProductModel", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "asWip": {"name": "asWip", "label": "在制品", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "asFinal": {"name": "asFinal", "label": "成品", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "enabled": {"name": "enabled", "label": "已启用", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "enabledOn": {"name": "enabledOn", "label": "启用日期", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "disabledOn": {"name": "disabledOn", "label": "停用日期", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 4, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 6, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 10, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btLines": {"name": "btLines", "label": "单行", "type": "Component", "scale": "List", "refEntity": "FbProductSimpleBomLine", "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"input": "ComponentTable", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 14, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"enabled": true, "fixedPrefix": "BOM-", "flowNoWidth": 3}, "digest": {"fields": []}, "scale": "Instances", "sort": "-id", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": [[{"type": "Simple", "fieldName": "finalMaterial", "formatMapping": []}], [{"type": "Conditional", "fieldName": "enabled", "formatMapping": [{"operator": "Eq", "value": "true", "replaceText": "已启用"}, {"operator": "Other", "notShow": true}]}]]}, "orderConfig": {"enabled": true, "kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "FbProductSimpleBomLine": {"name": "FbProductSimpleBomLine", "label": "产品物料清单行", "group": "MainData", "builtin": true, "disabled": true, "type": "Entity", "fields": {"material": {"name": "material", "label": "物料", "type": "Reference", "scale": "Single", "refEntity": "FbMaterial", "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "qty": {"name": "qty", "label": "数量", "type": "Float", "scale": "Single", "inputRequired": true, "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 4, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 5, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 7, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btParentId": {"name": "btParentId", "label": "所属订单", "type": "Reference", "scale": "Single", "refEntity": "FbProductSimpleBom", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btLineNo": {"name": "btLineNo", "label": "行号", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 4}, "digest": {"fields": []}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": [[{"type": "Simple", "fieldName": "material", "formatMapping": []}, {"type": "Simple", "fieldName": "qty", "prefix": "x ", "formatMapping": []}]]}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "FbPurchaseOrder": {"name": "FbPurchaseOrder", "label": "采购订单", "group": "Purchase", "builtin": true, "disabled": true, "type": "Entity", "fields": {"remark": {"name": "remark", "label": "备注", "type": "String", "scale": "Single", "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "vendor": {"name": "vendor", "label": "供应商", "type": "Reference", "scale": "Single", "refEntity": "FbVendor", "inputRequired": true, "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 14, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 24, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 21, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "单号", "type": "String", "scale": "Single", "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 200, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 23, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 22, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 20, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btLines": {"name": "btLines", "label": "单行", "type": "Component", "scale": "List", "refEntity": "FbPurchaseOrderLine", "copiable": true, "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"input": "ComponentTable", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 25, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btOrderState": {"name": "btOrderState", "label": "状态", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "Init", "label": "未提交", "color": "#888"}, {"value": "Committed", "label": "已提交", "color": "#EFAF46"}, {"value": "Approved", "label": "已审核", "color": "#1D9DD6"}, {"value": "Rejected", "label": "已退回", "color": "E776BF"}, {"value": "Finished", "label": "已完成", "color": "#2DD92E"}, {"value": "Aborted", "label": "已作废", "color": "#E776BF"}]}, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btOrderStateReason": {"name": "btOrderStateReason", "label": "状态说明", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 4, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btOrderKind": {"name": "btOrderKind", "label": "类型", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "Goods", "label": "商品采购"}, {"value": "RawMaterials", "label": "原料采购"}, {"value": "Office", "label": "办公用品采购"}, {"value": "Fa", "label": "固定资产采购"}]}, "inputRequired": true, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "requirementDepartment": {"name": "requirementDepartment", "label": "需求部门", "type": "Reference", "scale": "Single", "refEntity": "FbDepartment", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "requirementUser": {"name": "requirementUser", "label": "需求人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "requirementRemark": {"name": "requirementRemark", "label": "需求备注", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "toWarehouse": {"name": "toWarehouse", "label": "入库仓库", "type": "Reference", "scale": "Single", "refEntity": "FbWarehouse", "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "expectedArrivedOn": {"name": "expectedArrivedOn", "label": "期望到货日期", "type": "Date", "scale": "Single", "inputRequired": true, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "lastArrivedOn": {"name": "lastArrivedOn", "label": "最晚到货日期", "type": "Date", "scale": "Single", "inputRequired": true, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "amount": {"name": "amount", "label": "采购金额", "type": "Float", "scale": "Single", "decimals": 3, "sumLineField": "amount", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 15, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "qty": {"name": "qty", "label": "采购数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sumLineField": "qty", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "asnQty": {"name": "asn<PERSON><PERSON>", "label": "已确认交期数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sumLineField": "asn<PERSON><PERSON>", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 16, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "yieldQty": {"name": "yieldQty", "label": "让步接收数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sumLineField": "yieldQty", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 18, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "rejectQty": {"name": "rejectQty", "label": "拒收数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sumLineField": "rejectQty", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 19, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "receivedQty": {"name": "receivedQty", "label": "合格数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sumLineField": "receivedQty", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 17, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"enabled": true, "fixedPrefix": "PO", "flowNoWidth": 5}, "digest": {"fields": []}, "scale": "Instances", "commentEnabled": true, "trackChange": true, "userNotice": {"create": true, "update": true, "comment": true, "targetUserFields": ["modifiedBy", "created<PERSON>y", "requirementUser"]}, "actions": {}, "listStats": {"items": []}, "listCard": {"stateFieldName": "btOrderState", "lines": [[{"type": "Simple", "fieldName": "id", "formatMapping": []}], [{"type": "Simple", "fieldName": "btOrderKind", "formatMapping": []}, {"type": "Conditional", "fieldName": "created<PERSON>y", "formatMapping": [{"operator": "Other", "alignRight": true}]}, {"type": "Simple", "fieldName": "createdOn", "formatMapping": []}], [{"type": "Simple", "fieldName": "vendor", "formatMapping": []}, {"type": "Conditional", "fieldName": "amount", "prefix": "¥ ", "formatMapping": [{"operator": "Other", "alignRight": true}]}], [{"type": "Simple", "fieldName": "qty", "prefix": "采购数量: ", "formatMapping": []}, {"type": "Simple", "fieldName": "asn<PERSON><PERSON>", "prefix": "已确认交货数量: ", "formatMapping": []}], [{"type": "Simple", "fieldName": "receivedQty", "prefix": "实收数量: ", "formatMapping": []}, {"type": "Simple", "fieldName": "yieldQty", "prefix": "让步数量: ", "formatMapping": []}, {"type": "Simple", "fieldName": "rejectQty", "prefix": "拒收数量: ", "formatMapping": []}]]}, "orderConfig": {"enabled": true, "kinds": [{"name": "Goods", "label": "商品采购"}, {"name": "RawMaterials", "label": "原料采购"}, {"name": "Office", "label": "办公用品采购"}, {"name": "Fa", "label": "固定资产采购"}], "states": [{"id": "Init", "label": "未提交", "entityEditable": true, "color": "#888", "nextStates": [{"id": "Committed", "buttonLabel": "提交", "buttonKind": "primary"}]}, {"id": "Committed", "label": "已提交", "color": "#EFAF46", "nextStates": [{"id": "Approved", "buttonLabel": "审核通过", "buttonKind": "success"}, {"id": "Rejected", "buttonLabel": "审核不通过", "buttonKind": "warning", "reasonRequired": true}, {"id": "Aborted", "buttonLabel": "作废", "buttonKind": "error", "reasonRequired": true}]}, {"id": "Approved", "label": "已审核", "color": "#1D9DD6", "nextStates": [{"id": "Finished", "buttonLabel": "完成"}]}, {"id": "Rejected", "label": "已退回", "entityEditable": true, "color": "E776BF", "nextStates": [{"id": "Committed", "buttonLabel": "提交"}]}, {"id": "Finished", "label": "已完成", "finalState": true, "color": "#2DD92E", "nextStates": []}, {"id": "Aborted", "label": "已作废", "finalState": true, "color": "#E776BF", "nextStates": []}], "pushOrderStates": ["Approved"], "pushOrders": [{"downOrderName": "FbAdvancedShippingNotice", "downOrderKind": "Purchase", "downOrderState": "Init", "headFieldMapping": [{"sourceField": "vendor", "targetField": "vendor"}, {"sourceField": "expectedArrivedOn", "targetField": "expectedArrivedOn"}, {"sourceField": "toWarehouse", "targetField": "toWarehouse"}, {"sourceField": "id", "targetField": "purchaseOrderId"}, {"sourceField": "qty", "targetField": "purchaseQty"}], "lineFieldMapping": [{"sourceField": "price", "targetField": "price"}, {"sourceField": "qty", "targetField": "purchaseQty"}], "downOrderBackStates": ["Committed"], "lineWriteBackMapping": [{"downField": "qty", "upField": "asn<PERSON><PERSON>"}, {"downField": "receivedQty", "upField": "receivedQty"}, {"downField": "yieldQty", "upField": "yieldQty"}, {"downField": "rejectQty", "upField": "rejectQty"}]}], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "FbPurchaseOrderLine": {"name": "FbPurchaseOrderLine", "label": "采购订单行", "group": "Purchase", "builtin": true, "disabled": true, "type": "Entity", "fields": {"modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "disabled": true, "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 24, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "amount": {"name": "amount", "label": "采购金额", "type": "Float", "scale": "Single", "decimals": 3, "computed": "(ev.price || 0) * (ev.qty || 0)", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "disabled": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 20, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 14, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 22, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "price": {"name": "price", "label": "采购价格", "type": "Float", "scale": "Single", "inputRequired": true, "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "disabled": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 23, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "disabled": true, "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 19, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "qty": {"name": "qty", "label": "采购数量", "type": "Float", "scale": "Single", "inputRequired": true, "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "disabled": true, "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 21, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btParentId": {"name": "btParentId", "label": "所属订单", "type": "Reference", "scale": "Single", "refEntity": "FbPurchaseOrder", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btLineNo": {"name": "btLineNo", "label": "行号", "type": "Int", "scale": "Single", "copiable": true, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterial": {"name": "btMaterial", "label": "物料", "type": "Reference", "scale": "Single", "refEntity": "FbMaterial", "inputRequired": true, "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 240, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialName": {"name": "btMaterialName", "label": "物料名称", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "name", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialModel": {"name": "btMaterialModel", "label": "物料型号", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "model", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialSpec": {"name": "btMaterialSpec", "label": "物料规格", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "spec", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialCategory": {"name": "btMaterialCategory", "label": "物料分类编号", "type": "String", "scale": "Single", "disabled": true, "refField": "btMaterial", "refFieldField": "leafCategory.id", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialImage": {"name": "btMaterialImage", "label": "物料图片", "type": "Image", "scale": "Single", "refField": "btMaterial", "refFieldField": "image", "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialId": {"name": "btMaterialId", "label": "物料编号", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "id", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialCategoryName": {"name": "btMaterialCategoryName", "label": "物料分类名称", "type": "String", "scale": "Single", "disabled": true, "refField": "btMaterial", "refFieldField": "leafCategory.name", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "asnQty": {"name": "asn<PERSON><PERSON>", "label": "已确定交期数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 15, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "yieldQty": {"name": "yieldQty", "label": "让步接收数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 17, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "rejectQty": {"name": "rejectQty", "label": "拒收数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 18, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "receivedQty": {"name": "receivedQty", "label": "合格数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 16, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 0}, "digest": {"fields": []}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"imageFieldName": "btMaterialImage", "lines": [[{"type": "Simple", "fieldName": "btMaterialName", "formatMapping": []}], [{"type": "Simple", "fieldName": "btMaterialId", "suffix": " |", "formatMapping": []}, {"type": "Simple", "fieldName": "btMaterialModel", "suffix": " |", "formatMapping": []}, {"type": "Simple", "fieldName": "btMaterialSpec", "formatMapping": []}], [{"type": "Simple", "fieldName": "qty", "prefix": "数量: ", "formatMapping": []}, {"type": "Simple", "fieldName": "price", "prefix": "价格: ", "formatMapping": []}, {"type": "Simple", "fieldName": "amount", "prefix": "金额: ", "formatMapping": []}]]}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "FbRack": {"name": "FbRack", "label": "货架", "group": "MainData", "builtin": true, "type": "Entity", "fields": {"ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "disabled": {"name": "disabled", "label": "禁用", "type": "Boolean", "scale": "Single", "inputRequired": true, "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "name": {"name": "name", "label": "名称", "type": "String", "scale": "Single", "inputRequired": true, "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "remark": {"name": "remark", "label": "备注", "type": "String", "scale": "Single", "inputRequired": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "warehouse": {"name": "warehouse", "label": "所属仓库", "type": "Reference", "scale": "Single", "refEntity": "FbWarehouse", "inputRequired": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "编号", "type": "String", "scale": "Single", "inputRequired": true, "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "Date", "scale": "Single", "decimals": 0, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "Date", "scale": "Single", "decimals": 0, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "district": {"name": "district", "label": "所属库区", "type": "Reference", "scale": "Single", "refEntity": "FbDistrict", "inputRequired": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 0}, "digest": {"fields": ["name"]}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": []}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "FbRackChannel": {"name": "FbRackChannel", "label": "巷道", "group": "MainData", "builtin": true, "disabled": true, "type": "Entity", "fields": {"disabled": {"name": "disabled", "label": "禁用", "type": "Boolean", "scale": "Single", "inputRequired": true, "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "巷道编号", "type": "String", "scale": "Single", "inputRequired": true, "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "district": {"name": "district", "label": "所属库区", "type": "Reference", "scale": "Single", "refEntity": "FbDistrict", "inputRequired": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "warehouse": {"name": "warehouse", "label": "所属仓库", "type": "Reference", "scale": "Single", "refEntity": "FbWarehouse", "inputRequired": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "Date", "scale": "Single", "decimals": 0, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "name": {"name": "name", "label": "巷道名称", "type": "String", "scale": "Single", "inputRequired": true, "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "remark": {"name": "remark", "label": "备注", "type": "String", "scale": "Single", "inputRequired": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "Date", "scale": "Single", "decimals": 0, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 0}, "digest": {"fields": ["name"]}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": []}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "FbReceivingOrder": {"name": "FbReceivingOrder", "label": "收货单", "group": "Purchase", "builtin": true, "disabled": true, "type": "Entity", "fields": {"remark": {"name": "remark", "label": "备注", "type": "String", "scale": "Single", "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "vendor": {"name": "vendor", "label": "供应商", "type": "Reference", "scale": "Single", "refEntity": "FbVendor", "inputRequired": true, "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 15, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 23, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 21, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "单号", "type": "String", "scale": "Single", "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 200, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 22, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 25, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 20, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btLines": {"name": "btLines", "label": "单行", "type": "Component", "scale": "List", "refEntity": "FbReceivingOrderLine", "copiable": true, "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"input": "ComponentTable", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 24, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btOrderState": {"name": "btOrderState", "label": "状态", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "Init", "label": "未提交", "color": "#888"}, {"value": "Committed", "label": "已提交", "color": "#EFAF46"}, {"value": "Finished", "label": "已完成", "color": "#2DD92E"}]}, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btOrderStateReason": {"name": "btOrderStateReason", "label": "状态说明", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 4, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btOrderKind": {"name": "btOrderKind", "label": "类型", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "Purchase", "label": "采购入库"}]}, "inputRequired": true, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "toWarehouse": {"name": "toWarehouse", "label": "入库仓库", "type": "Reference", "scale": "Single", "refEntity": "FbWarehouse", "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "transportBy": {"name": "transportBy", "label": "物流联系人", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "transportByPhone": {"name": "transportByPhone", "label": "物流联系方式", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "transportRemark": {"name": "transportRemark", "label": "物流备注", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "purchaseOrderId": {"name": "purchaseOrderId", "label": "采购订单号", "type": "Reference", "scale": "Single", "refEntity": "FbPurchaseOrder", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "asnId": {"name": "asnId", "label": "到货通知号", "type": "Reference", "scale": "Single", "refEntity": "FbAdvancedShippingNotice", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "arrivedOn": {"name": "arrivedOn", "label": "实际到货日期", "type": "Date", "scale": "Single", "inputRequired": true, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "goodQty": {"name": "goodQty", "label": "合格数量", "type": "Float", "scale": "Single", "decimals": 3, "sumLineField": "goodQty", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 16, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "yieldQty": {"name": "yieldQty", "label": "让步接收数量", "type": "Float", "scale": "Single", "decimals": 3, "sumLineField": "yieldQty", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 17, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "badQty": {"name": "badQty", "label": "拒收数量", "type": "Float", "scale": "Single", "decimals": 3, "sumLineField": "badQty", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 18, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "expectedQty": {"name": "expectedQty", "label": "应收数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sumLineField": "expectedQty", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 14, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "receivedQty": {"name": "receivedQty", "label": "实收数量", "type": "Float", "scale": "Single", "decimals": 3, "sumLineField": "receivedQty", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 19, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"enabled": true, "fixedPrefix": "RCO", "flowNoWidth": 5}, "digest": {"fields": []}, "scale": "Instances", "commentEnabled": true, "trackChange": true, "userNotice": {"create": true, "update": true, "comment": true, "targetUserFields": ["modifiedBy", "created<PERSON>y"]}, "actions": {}, "listStats": {"items": []}, "listCard": {"stateFieldName": "btOrderState", "lines": [[{"type": "Simple", "fieldName": "id", "formatMapping": []}], [{"type": "Simple", "fieldName": "expectedQty", "prefix": "应收数量: ", "formatMapping": []}, {"type": "Simple", "fieldName": "goodQty", "prefix": "合格数量: ", "formatMapping": []}], [{"type": "Simple", "fieldName": "yieldQty", "prefix": "让步数量: ", "formatMapping": []}, {"type": "Simple", "fieldName": "badQty", "prefix": "拒收数量: ", "formatMapping": []}]]}, "orderConfig": {"enabled": true, "kinds": [{"name": "Purchase", "label": "采购入库"}], "states": [{"id": "Init", "label": "未提交", "entityEditable": true, "color": "#888", "nextStates": [{"id": "Committed", "buttonLabel": "提交", "buttonKind": "primary"}]}, {"id": "Committed", "label": "已提交", "color": "#EFAF46", "nextStates": [{"id": "Finished", "buttonLabel": "完成", "buttonKind": "success"}]}, {"id": "Finished", "label": "已完成", "finalState": true, "color": "#2DD92E", "nextStates": []}], "pushOrderStates": ["Approved", "Committed"], "pushOrders": [{"downOrderName": "FbInboundOrder", "downOrderKind": "PurchaseInbound", "downOrderState": "Init", "headFieldMapping": [{"sourceField": "remark", "targetField": "remark"}, {"sourceField": "vendor", "targetField": "vendor"}, {"sourceField": "purchaseOrderId", "targetField": "purchaseOrderId"}, {"sourceField": "asnId", "targetField": "asnId"}, {"sourceField": "id", "targetField": "recevingOrderId"}, {"sourceField": "toWarehouse", "targetField": "warehouse"}], "lineFieldMapping": [{"sourceField": "receivedQty", "targetField": "qty"}], "downOrderBackStates": ["Committed"], "lineWriteBackMapping": []}], "upOrderConfig": {"items": []}, "thisQtyFields": ["goodQty", "yieldQty", "badQty"], "planQtyField": "expectedQty", "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "FbReceivingOrderLine": {"name": "FbReceivingOrderLine", "label": "收货单行", "group": "Purchase", "builtin": true, "disabled": true, "type": "Entity", "fields": {"modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "disabled": true, "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 23, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "disabled": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 19, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 20, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "disabled": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 22, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "disabled": true, "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 18, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "disabled": true, "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 21, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btParentId": {"name": "btParentId", "label": "所属收货单", "type": "Reference", "scale": "Single", "refEntity": "FbAdvancedShippingNotice", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btLineNo": {"name": "btLineNo", "label": "行号", "type": "Int", "scale": "Single", "copiable": true, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterial": {"name": "btMaterial", "label": "物料", "type": "Reference", "scale": "Single", "refEntity": "FbMaterial", "inputRequired": true, "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 240, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialName": {"name": "btMaterialName", "label": "物料名称", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "name", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialModel": {"name": "btMaterialModel", "label": "物料型号", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "model", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialSpec": {"name": "btMaterialSpec", "label": "物料规格", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "spec", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialCategory": {"name": "btMaterialCategory", "label": "物料分类编号", "type": "String", "scale": "Single", "disabled": true, "refField": "btMaterial", "refFieldField": "leafCategory.id", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialImage": {"name": "btMaterialImage", "label": "物料图片", "type": "Image", "scale": "Single", "refField": "btMaterial", "refFieldField": "image", "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialId": {"name": "btMaterialId", "label": "物料编号", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "id", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialCategoryName": {"name": "btMaterialCategoryName", "label": "物料分类名称", "type": "String", "scale": "Single", "disabled": true, "refField": "btMaterial", "refFieldField": "leafCategory.name", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "goodQty": {"name": "goodQty", "label": "合格数量", "type": "Float", "scale": "Single", "inputRequired": true, "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 14, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "yieldQty": {"name": "yieldQty", "label": "让步接收数量", "type": "Float", "scale": "Single", "inputRequired": true, "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 15, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "badQty": {"name": "badQty", "label": "拒收数量", "type": "Float", "scale": "Single", "inputRequired": true, "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 16, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "expectedQty": {"name": "expectedQty", "label": "应收数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "receivedQty": {"name": "receivedQty", "label": "实收数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "computed": "(ev.goodQty || 0) + (ev.yieldQty || 0)", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 17, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "price": {"name": "price", "label": "采购价格", "type": "Float", "scale": "Single", "inputRequired": true, "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 0}, "digest": {"fields": []}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"imageFieldName": "btMaterialImage", "lines": [[{"type": "Simple", "fieldName": "btMaterialName", "formatMapping": []}], [{"type": "Simple", "fieldName": "btMaterialId", "suffix": " |", "formatMapping": []}, {"type": "Simple", "fieldName": "btMaterialModel", "suffix": " |", "formatMapping": []}, {"type": "Simple", "fieldName": "btMaterialSpec", "formatMapping": []}], [{"type": "Simple", "fieldName": "goodQty", "prefix": "合格数量: ", "formatMapping": []}, {"type": "Simple", "fieldName": "yieldQty", "prefix": "让步数量: ", "formatMapping": []}, {"type": "Simple", "fieldName": "badQty", "prefix": "拒收数量: ", "formatMapping": []}]]}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "FbSaleOrder": {"name": "FbSaleOrder", "label": "销售订单", "group": "Sale", "builtin": true, "disabled": true, "type": "Entity", "fields": {"remark": {"name": "remark", "label": "备注", "type": "String", "scale": "Single", "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 19, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 16, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "单号", "type": "String", "scale": "Single", "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 200, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 18, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 17, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 15, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btLines": {"name": "btLines", "label": "单行", "type": "Component", "scale": "List", "refEntity": "FbSaleOrderLine", "copiable": true, "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"input": "ComponentTable", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 20, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btOrderState": {"name": "btOrderState", "label": "状态", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "Init", "label": "未提交", "color": "#888"}, {"value": "Committed", "label": "已提交", "color": "#EFAF46"}, {"value": "Approved", "label": "已审核", "color": "#1D9DD6"}, {"value": "Rejected", "label": "已退回", "color": "E776BF"}, {"value": "Finished", "label": "已完成", "color": "#2DD92E"}, {"value": "Aborted", "label": "已作废", "color": "#E776BF"}]}, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btOrderStateReason": {"name": "btOrderStateReason", "label": "状态说明", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 4, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btOrderKind": {"name": "btOrderKind", "label": "类型", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "Product", "label": "商品销售"}]}, "inputRequired": true, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "expectedArrivedOn": {"name": "expectedArrivedOn", "label": "期望到货日期", "type": "Date", "scale": "Single", "inputRequired": true, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "lastArrivedOn": {"name": "lastArrivedOn", "label": "最晚到货日期", "type": "Date", "scale": "Single", "inputRequired": true, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "amount": {"name": "amount", "label": "销售金额", "type": "Float", "scale": "Single", "decimals": 3, "sumLineField": "amount", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "qty": {"name": "qty", "label": "销售数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sumLineField": "qty", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "customer": {"name": "customer", "label": "客户", "type": "Reference", "scale": "Single", "refEntity": "FbCustomer", "inputRequired": true, "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "fromWarehouse": {"name": "fromWarehouse", "label": "出库仓库", "type": "Reference", "scale": "Single", "refEntity": "FbWarehouse", "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "confirmedShipQty": {"name": "confirmedShip<PERSON><PERSON>", "label": "已确认发货数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sumLineField": "confirmedShip<PERSON><PERSON>", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "outboundQty": {"name": "outboundQty", "label": "已出库数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sumLineField": "outboundQty", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 14, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"enabled": true, "fixedPrefix": "SO", "flowNoWidth": 5}, "digest": {"fields": []}, "scale": "Instances", "commentEnabled": true, "trackChange": true, "userNotice": {"create": true, "update": true, "comment": true, "targetUserFields": ["modifiedBy", "created<PERSON>y", "requirementUser"]}, "actions": {}, "listStats": {"items": []}, "listCard": {"stateFieldName": "btOrderState", "lines": [[{"type": "Simple", "fieldName": "id", "formatMapping": []}], [{"type": "Simple", "fieldName": "btOrderKind", "formatMapping": []}, {"type": "Conditional", "fieldName": "created<PERSON>y", "formatMapping": [{"operator": "Other", "alignRight": true}]}, {"type": "Simple", "fieldName": "createdOn", "formatMapping": []}], [{"type": "Simple", "fieldName": "customer", "formatMapping": []}, {"type": "Conditional", "fieldName": "amount", "prefix": "¥ ", "formatMapping": [{"operator": "Other", "alignRight": true}]}], [{"type": "Simple", "fieldName": "qty", "prefix": "采购数量: ", "formatMapping": []}, {"type": "Simple", "fieldName": "confirmedShip<PERSON><PERSON>", "prefix": "已确认发货数量: ", "formatMapping": []}], [{"type": "Simple", "fieldName": "outboundQty", "prefix": "已出库数量: ", "formatMapping": []}]]}, "orderConfig": {"enabled": true, "kinds": [{"name": "Product", "label": "商品销售"}], "states": [{"id": "Init", "label": "未提交", "entityEditable": true, "color": "#888", "nextStates": [{"id": "Committed", "buttonLabel": "提交", "buttonKind": "primary"}]}, {"id": "Committed", "label": "已提交", "color": "#EFAF46", "nextStates": [{"id": "Approved", "buttonLabel": "审核通过", "buttonKind": "success"}, {"id": "Rejected", "buttonLabel": "审核不通过", "buttonKind": "warning", "reasonRequired": true}, {"id": "Aborted", "buttonLabel": "作废", "buttonKind": "error", "reasonRequired": true}]}, {"id": "Approved", "label": "已审核", "color": "#1D9DD6", "nextStates": [{"id": "Finished", "buttonLabel": "完成"}]}, {"id": "Rejected", "label": "已退回", "entityEditable": true, "color": "E776BF", "nextStates": [{"id": "Committed", "buttonLabel": "提交"}]}, {"id": "Finished", "label": "已完成", "finalState": true, "color": "#2DD92E", "nextStates": []}, {"id": "Aborted", "label": "已作废", "finalState": true, "color": "#E776BF", "nextStates": []}], "pushOrderStates": ["Approved"], "pushOrders": [{"downOrderName": "FbSaleShipOrder", "downOrderKind": "Product", "downOrderState": "Init", "headFieldMapping": [{"sourceField": "qty", "targetField": "saleQty"}, {"sourceField": "customer", "targetField": "customer"}, {"sourceField": "fromWarehouse", "targetField": "fromWarehouse"}, {"sourceField": "outboundQty", "targetField": "outboundQty"}, {"sourceField": "id", "targetField": "shipOrderId"}], "lineFieldMapping": [{"sourceField": "price", "targetField": "price"}, {"sourceField": "qty", "targetField": "saleQty"}], "downOrderBackStates": ["Committed"], "lineWriteBackMapping": [{"downField": "outboundQty", "upField": "outboundQty"}, {"downField": "qty", "upField": "confirmedShip<PERSON><PERSON>"}]}], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "FbSaleOrderLine": {"name": "FbSaleOrderLine", "label": "销售订单行", "group": "Sale", "builtin": true, "disabled": true, "type": "Entity", "fields": {"modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "disabled": true, "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 22, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "amount": {"name": "amount", "label": "销售金额", "type": "Float", "scale": "Single", "decimals": 3, "computed": "(ev.price || 0) * (ev.qty || 0)", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "disabled": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 18, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 14, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 20, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "price": {"name": "price", "label": "售价", "type": "Float", "scale": "Single", "inputRequired": true, "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "disabled": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 21, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "disabled": true, "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 17, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "qty": {"name": "qty", "label": "销售数量", "type": "Float", "scale": "Single", "inputRequired": true, "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "disabled": true, "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 19, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btParentId": {"name": "btParentId", "label": "所属订单", "type": "Reference", "scale": "Single", "refEntity": "FbSaleOrder", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btLineNo": {"name": "btLineNo", "label": "行号", "type": "Int", "scale": "Single", "copiable": true, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterial": {"name": "btMaterial", "label": "物料", "type": "Reference", "scale": "Single", "refEntity": "FbMaterial", "inputRequired": true, "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 240, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialName": {"name": "btMaterialName", "label": "物料名称", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "name", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialModel": {"name": "btMaterialModel", "label": "物料型号", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "model", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialSpec": {"name": "btMaterialSpec", "label": "物料规格", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "spec", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialCategory": {"name": "btMaterialCategory", "label": "物料分类编号", "type": "String", "scale": "Single", "disabled": true, "refField": "btMaterial", "refFieldField": "leafCategory.id", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialImage": {"name": "btMaterialImage", "label": "物料图片", "type": "Image", "scale": "Single", "refField": "btMaterial", "refFieldField": "image", "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialId": {"name": "btMaterialId", "label": "物料编号", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "id", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialCategoryName": {"name": "btMaterialCategoryName", "label": "物料分类名称", "type": "String", "scale": "Single", "disabled": true, "refField": "btMaterial", "refFieldField": "leafCategory.name", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "confirmedShipQty": {"name": "confirmedShip<PERSON><PERSON>", "label": "已确定发货数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 15, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "outboundQty": {"name": "outboundQty", "label": "已出库数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 16, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 0}, "digest": {"fields": []}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"imageFieldName": "btMaterialImage", "lines": [[{"type": "Simple", "fieldName": "btMaterialName", "formatMapping": []}], [{"type": "Simple", "fieldName": "btMaterialId", "suffix": " |", "formatMapping": []}, {"type": "Simple", "fieldName": "btMaterialModel", "suffix": " |", "formatMapping": []}, {"type": "Simple", "fieldName": "btMaterialSpec", "formatMapping": []}], [{"type": "Simple", "fieldName": "qty", "prefix": "数量: ", "formatMapping": []}, {"type": "Simple", "fieldName": "price", "prefix": "价格: ", "formatMapping": []}, {"type": "Simple", "fieldName": "amount", "prefix": "金额: ", "formatMapping": []}]]}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "FbSaleShipOrder": {"name": "FbSaleShipOrder", "label": "销售发货单", "group": "Sale", "builtin": true, "disabled": true, "type": "Entity", "fields": {"remark": {"name": "remark", "label": "备注", "type": "String", "scale": "Single", "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 20, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 18, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "单号", "type": "String", "scale": "Single", "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 200, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 19, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 22, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 17, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btLines": {"name": "btLines", "label": "单行", "type": "Component", "scale": "List", "refEntity": "FbSaleShipOrderLine", "copiable": true, "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"input": "ComponentTable", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 21, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btOrderState": {"name": "btOrderState", "label": "状态", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "Init", "label": "未提交", "color": "#888"}, {"value": "Committed", "label": "已提交", "color": "#EFAF46"}, {"value": "Finished", "label": "已完成", "color": "#2DD92E"}]}, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btOrderStateReason": {"name": "btOrderStateReason", "label": "状态说明", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 4, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btOrderKind": {"name": "btOrderKind", "label": "类型", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "Product", "label": "商品销售"}]}, "inputRequired": true, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "transportBy": {"name": "transportBy", "label": "物流联系人", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "transportByPhone": {"name": "transportByPhone", "label": "物流联系方式", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "transportRemark": {"name": "transportRemark", "label": "物流备注", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "qty": {"name": "qty", "label": "本次发货数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sumLineField": "qty", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "otherArrivedQty": {"name": "otherArrivedQty", "label": "已确认发货数量", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "Hidden", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 15, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "shipOrderId": {"name": "shipOrderId", "label": "关联销售订单号", "type": "Reference", "scale": "Single", "refEntity": "FbSaleOrder", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "customer": {"name": "customer", "label": "客户", "type": "Reference", "scale": "Single", "refEntity": "FbCustomer", "inputRequired": true, "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "fromWarehouse": {"name": "fromWarehouse", "label": "出库仓库", "type": "Reference", "scale": "Single", "refEntity": "FbWarehouse", "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "saleQty": {"name": "saleQty", "label": "销售总数", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 14, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "outboundQty": {"name": "outboundQty", "label": "已出库数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sumLineField": "outboundQty", "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 16, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"enabled": true, "fixedPrefix": "SSO", "flowNoWidth": 5}, "digest": {"fields": []}, "scale": "Instances", "commentEnabled": true, "trackChange": true, "userNotice": {"create": true, "update": true, "comment": true, "targetUserFields": ["modifiedBy", "created<PERSON>y"]}, "actions": {}, "listStats": {"items": []}, "listCard": {"stateFieldName": "btOrderState", "lines": [[{"type": "Simple", "fieldName": "id", "formatMapping": []}], [{"type": "Simple", "fieldName": "customer", "formatMapping": []}], [{"type": "Simple", "fieldName": "qty", "prefix": "本次发货数量: ", "formatMapping": []}, {"type": "Simple", "fieldName": "outboundQty", "prefix": "已出库数量: ", "formatMapping": []}]]}, "orderConfig": {"enabled": true, "kinds": [{"name": "Product", "label": "商品销售"}], "states": [{"id": "Init", "label": "未提交", "entityEditable": true, "color": "#888", "nextStates": [{"id": "Committed", "buttonLabel": "提交", "buttonKind": "primary"}]}, {"id": "Committed", "label": "已提交", "color": "#EFAF46", "nextStates": [{"id": "Finished", "buttonLabel": "完成", "buttonKind": "success"}]}, {"id": "Finished", "label": "已完成", "finalState": true, "color": "#2DD92E", "nextStates": []}], "pushOrderStates": ["Approved", "Committed"], "pushOrders": [{"downOrderName": "FbOutboundOrder", "downOrderKind": "Product", "downOrderState": "Init", "headFieldMapping": [{"sourceField": "remark", "targetField": "remark"}, {"sourceField": "qty", "targetField": "qty"}, {"sourceField": "customer", "targetField": "customer"}, {"sourceField": "shipOrderId", "targetField": "saleOrderId"}, {"sourceField": "id", "targetField": "saleShipOrderId"}, {"sourceField": "fromWarehouse", "targetField": "warehouse"}], "lineFieldMapping": [{"sourceField": "qty", "targetField": "planQty"}], "downOrderBackStates": ["Committed"], "lineWriteBackMapping": [{"downField": "qty", "upField": "outboundQty"}]}], "upOrderConfig": {"items": []}, "thisQtyFields": ["qty"], "occurredQtyField": "otherArrivedQty", "planQtyField": "saleQty", "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "FbSaleShipOrderLine": {"name": "FbSaleShipOrderLine", "label": "销售发货单行", "group": "Sale", "builtin": true, "disabled": true, "type": "Entity", "fields": {"modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "disabled": true, "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 22, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "disabled": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 18, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 15, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 20, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "price": {"name": "price", "label": "售价", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "disabled": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 21, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "disabled": true, "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 17, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "qty": {"name": "qty", "label": "本次发货数量", "type": "Float", "scale": "Single", "inputRequired": true, "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "disabled": true, "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 19, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btParentId": {"name": "btParentId", "label": "所属发货单", "type": "Reference", "scale": "Single", "refEntity": "FbSaleShipOrder", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btLineNo": {"name": "btLineNo", "label": "行号", "type": "Int", "scale": "Single", "copiable": true, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterial": {"name": "btMaterial", "label": "物料", "type": "Reference", "scale": "Single", "refEntity": "FbMaterial", "inputRequired": true, "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 240, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialName": {"name": "btMaterialName", "label": "物料名称", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "name", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialModel": {"name": "btMaterialModel", "label": "物料型号", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "model", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialSpec": {"name": "btMaterialSpec", "label": "物料规格", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "spec", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialCategory": {"name": "btMaterialCategory", "label": "物料分类编号", "type": "String", "scale": "Single", "disabled": true, "refField": "btMaterial", "refFieldField": "leafCategory.id", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialImage": {"name": "btMaterialImage", "label": "物料图片", "type": "Image", "scale": "Single", "refField": "btMaterial", "refFieldField": "image", "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialId": {"name": "btMaterialId", "label": "物料编号", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "id", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btMaterialCategoryName": {"name": "btMaterialCategoryName", "label": "物料分类名称", "type": "String", "scale": "Single", "disabled": true, "refField": "btMaterial", "refFieldField": "leafCategory.name", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "otherArrivedQty": {"name": "otherArrivedQty", "label": "已确认发货数量", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "Hidden", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 14, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "saleQty": {"name": "saleQty", "label": "销售总量", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "outboundQty": {"name": "outboundQty", "label": "已出库数量", "type": "Float", "scale": "Single", "decimals": 3, "copiable": true, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 16, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 0}, "digest": {"fields": []}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"imageFieldName": "btMaterialImage", "lines": [[{"type": "Simple", "fieldName": "btMaterialName", "formatMapping": []}], [{"type": "Simple", "fieldName": "btMaterialId", "suffix": " |", "formatMapping": []}, {"type": "Simple", "fieldName": "btMaterialModel", "suffix": " |", "formatMapping": []}, {"type": "Simple", "fieldName": "btMaterialSpec", "formatMapping": []}], [{"type": "Simple", "fieldName": "qty", "prefix": "数量: ", "formatMapping": []}]]}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "FbTransporter": {"name": "FbTransporter", "label": "承运商", "group": "MainData", "builtin": true, "disabled": true, "type": "Entity", "fields": {"createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "Date", "scale": "Single", "decimals": 0, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "address": {"name": "address", "label": "地址", "type": "String", "scale": "Single", "inputRequired": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "phone": {"name": "phone", "label": "联系电话", "type": "String", "scale": "Single", "inputRequired": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "remark": {"name": "remark", "label": "备注", "type": "String", "scale": "Single", "inputRequired": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "disabled": {"name": "disabled", "label": "禁用", "type": "Boolean", "scale": "Single", "inputRequired": true, "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "承运商编码", "type": "String", "scale": "Single", "inputRequired": true, "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "name": {"name": "name", "label": "承运商名称", "type": "String", "scale": "Single", "inputRequired": true, "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "Date", "scale": "Single", "decimals": 0, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 14, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "contact": {"name": "contact", "label": "联系人", "type": "String", "scale": "Single", "inputRequired": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "kind": {"name": "kind", "label": "承运商类型", "type": "String", "scale": "Single", "inputRequired": true, "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 0}, "digest": {"fields": ["name"]}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": []}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "MfProduct": {"name": "MfProduct", "label": "产品", "group": "MES", "builtin": true, "disabled": true, "type": "Entity", "fields": {"name": {"name": "name", "label": "产品名称", "type": "String", "scale": "Single", "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "material": {"name": "material", "label": "对应物料", "type": "Reference", "scale": "Single", "refEntity": "FbMaterial", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "disabled": {"name": "disabled", "label": "禁用", "type": "Boolean", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "产品编号", "type": "String", "scale": "Single", "inputRequired": true, "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 9, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 11, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "remark": {"name": "remark", "label": "备注", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btDisabled": {"name": "btDisabled", "label": "停用", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 4}, "digest": {"fields": ["name"]}, "scale": "Instances", "disabledFilter": true, "sort": "+id", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": []}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "OrderFlowRecord": {"name": "OrderFlowRecord", "label": "单据流转记录", "group": "Core", "builtin": true, "builtinCore": true, "type": "Entity", "fields": {"txId": {"name": "txId", "label": "事务ID", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "pushType": {"name": "pushType", "label": "类型", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "sourceOrderName": {"name": "sourceOrderName", "label": "源单名", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "sourceOrderId": {"name": "sourceOrderId", "label": "源单号", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "targetOrderName": {"name": "targetOrderName", "label": "新单名", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "targetOrderType": {"name": "targetOrderType", "label": "新单类型", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "targetOrderId": {"name": "targetOrderId", "label": "新单号", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 3, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 12, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 13, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 4}, "digest": {"fields": []}, "scale": "Instances", "sort": "-createdOn", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": []}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "SuDeliveryOpItem": {"name": "SuDeliveryOpItem", "label": "交付项", "group": "SOP", "builtin": true, "disabled": true, "type": "Entity", "fields": {"specModel": {"name": "specModel", "label": "规格型号", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 160, "timestampPrecision": "Second"}}, "makeOrderNotMakeQty": {"name": "makeOrderNotMakeQty", "label": "生产订单未完工量", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 23, "lineColumnWidth": 0, "listTableColumnWidth": 75, "timestampPrecision": "Second"}}, "planCategory": {"name": "planCategory", "label": "计划属性", "type": "String", "scale": "Single", "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 60, "timestampPrecision": "Second"}}, "saleDepartment": {"name": "saleDepartment", "label": "销售部门", "type": "String", "scale": "Single", "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "deliveryBy": {"name": "deliveryBy", "label": "交付经理", "type": "String", "scale": "Single", "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 80, "timestampPrecision": "Second"}}, "prjNo": {"name": "prjNo", "label": "项目编号", "type": "String", "scale": "Single", "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 28, "lineColumnWidth": 0, "listTableColumnWidth": 150, "timestampPrecision": "Second"}}, "extMakeQty": {"name": "extMakeQty", "label": "委外订单子件量", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 19, "lineColumnWidth": 0, "listTableColumnWidth": 75, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "Date", "scale": "Single", "decimals": 0, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 34, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "orderType": {"name": "orderType", "label": "需求类型", "type": "String", "scale": "Single", "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 80, "timestampPrecision": "Second"}}, "supplyQty": {"name": "supplyQty", "label": "供应合计", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 26, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "makeQty": {"name": "makeQty", "label": "生产订单子件量", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 18, "lineColumnWidth": 0, "listTableColumnWidth": 75, "timestampPrecision": "Second"}}, "preQty": {"name": "preQty", "label": "提前启动量", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 16, "lineColumnWidth": 0, "listTableColumnWidth": 75, "timestampPrecision": "Second"}}, "balance": {"name": "balance", "label": "现存量", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 25, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 35, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "materialId": {"name": "materialId", "label": "存货编码", "type": "String", "scale": "Single", "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "purchaseShipQty": {"name": "purchaseShipQty", "label": "请购未执行量", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 21, "lineColumnWidth": 0, "listTableColumnWidth": 75, "timestampPrecision": "Second"}}, "saleQty": {"name": "saleQty", "label": "销售订单量", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 17, "lineColumnWidth": 0, "listTableColumnWidth": 75, "timestampPrecision": "Second"}}, "materialName": {"name": "materialName", "label": "存货名称", "type": "String", "scale": "Single", "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 160, "timestampPrecision": "Second"}}, "orderId": {"name": "orderId", "label": "订单号", "type": "String", "scale": "Single", "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 200, "timestampPrecision": "Second"}}, "configRemark": {"name": "configRemark", "label": "配置说明", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"input": "TextArea", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "demandQty": {"name": "demandQty", "label": "需求合计", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 20, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "extOrderNotMakeQty": {"name": "extOrderNotMakeQty", "label": "委外订单未完工量", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 24, "lineColumnWidth": 0, "listTableColumnWidth": 75, "timestampPrecision": "Second"}}, "prjName": {"name": "prjName", "label": "项目名称", "type": "String", "scale": "Single", "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 29, "lineColumnWidth": 0, "listTableColumnWidth": 200, "timestampPrecision": "Second"}}, "sourceId": {"name": "sourceId", "label": "行号", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 36, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "orderDate": {"name": "orderDate", "label": "订单日期", "type": "Date", "scale": "Single", "decimals": 0, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "notShipQty": {"name": "notShipQty", "label": "未发货数量", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 15, "lineColumnWidth": 0, "listTableColumnWidth": 75, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 30, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "businessMan": {"name": "businessMan", "label": "业务员", "type": "String", "scale": "Single", "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 80, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "Date", "scale": "Single", "decimals": 0, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 31, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "编号", "type": "String", "scale": "Single", "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 32, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "purchaseNotMakeQty": {"name": "purchaseNotMakeQty", "label": "采购单未完工量", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 22, "lineColumnWidth": 0, "listTableColumnWidth": 75, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 33, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "qty": {"name": "qty", "label": "数量", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 14, "lineColumnWidth": 0, "listTableColumnWidth": 75, "timestampPrecision": "Second"}}, "short": {"name": "short", "label": "供应不足", "type": "Boolean", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 27, "lineColumnWidth": 0, "listTableColumnWidth": 50, "timestampPrecision": "Second"}}, "customer": {"name": "customer", "label": "客户简称", "type": "String", "scale": "Single", "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 200, "timestampPrecision": "Second"}}, "planShipDate": {"name": "planShipDate", "label": "预发货日期", "type": "Date", "scale": "Single", "decimals": 0, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 37, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 0}, "digest": {"fields": []}, "scale": "Instances", "sort": "-orderDate,orderId", "commentEnabled": true, "userNotice": {"targetUserFields": []}, "actions": {}, "listTableStyleExt": "    console.log(\"ext: page: \", page)\n    const r = { rows: {}, cells: {} };\n    let lastId = \"\";\n    const colors = [\"#EFF9FB\", \"#FBFBEF\"];\n    let rowColorIndex = 0;\n    for (const row of page) {\n        const id = row[\"id\"]\n\n        r.cells[id] = { \"demandQty\": { style: { fontSize: \"24px\" } }, \"supplyQty\": { style: { fontSize: \"24px\" } } }\n\n        if (row[\"demandQty\"] > row[\"supplyQty\"]) {\n            r.cells[id][\"supplyQty\"].style.background = \"yellow\"\n        }\n\n        if (row[\"businessMan\"] === \"黎璐\") {\n            // row[\"businessMan\"] = \"黎璐！\"\n            // r.cells[id][\"businessMan\"] = { style: { color: \"red\", border: \"2px solid #000\" } }\n        }\n\n        const orderId = row[\"orderId\"]\n        if (lastId !== orderId) {\n            rowColorIndex = (rowColorIndex + 1) % colors.length;\n            lastId = orderId\n        }\n\n        r.rows[id] = { style: { background: colors[rowColorIndex] } }\n    }\n    return r;", "listStats": {"items": []}, "listCard": {"lines": [[{"type": "Simple", "fieldName": "orderId", "formatMapping": []}], [{"type": "Simple", "fieldName": "materialName", "formatMapping": []}, {"type": "Simple", "fieldName": "materialId", "formatMapping": []}, {"type": "Conditional", "fieldName": "qty", "prefix": "x", "formatMapping": [{"operator": "Other", "alignRight": true}]}], [{"type": "Simple", "fieldName": "businessMan", "formatMapping": []}, {"type": "Simple", "fieldName": "orderDate", "formatMapping": []}]]}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}, "FbMaterialContainerVolume": {"name": "FbMaterialContainerVolume", "label": "容器容量", "group": "MainData", "builtin": true, "type": "Entity", "fields": {"containerType": {"name": "containerType", "label": "容器类型", "type": "Reference", "scale": "Single", "refEntity": "FbContainerType", "inputRequired": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "remark": {"name": "remark", "label": "备注", "type": "String", "scale": "Single", "inputRequired": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "Date", "scale": "Single", "decimals": 0, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "material": {"name": "material", "label": "物料", "type": "Reference", "scale": "Single", "refEntity": "FbMaterial", "inputRequired": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "qty": {"name": "qty", "label": "最大数量", "type": "Float", "scale": "Single", "inputRequired": true, "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "单号", "type": "String", "scale": "Single", "inputRequired": true, "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "Date", "scale": "Single", "decimals": 0, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 0}, "digest": {"fields": ["id"]}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": []}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}}}