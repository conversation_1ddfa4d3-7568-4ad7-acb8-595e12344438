### 初始化
POST http://localhost:5800/api/hai/test
content-type: appliction/json

{
  "robotId": "1501",
  "msgType": 0
}

### 移动
POST http://localhost:5800/api/hai/test
content-type: appliction/json

{
  "robotId": "1501",
  "msgType": 1,
  "targetPosition" : {
    "x" : 11.66,
    "y" : 7.16,
    "theta" : 1.57
  }
}


### 取货到货叉上
POST http://localhost:5800/api/hai/test
content-type: appliction/json
#     地面二维码   货叉旋转角度于与机器人朝向没关系,根据地面二维码来判断
#     Y -1.57
#     ｜ *  *
#     ｜ **  *
#     ｜* *   *
#     ｜ ** * * *
#     0----------- X 3.14
#     1.57
#    注意:如果货叉上有货,继续取货指令,会继续执行取货,导致问题

{
  "robotId": "1501",
  "msgType": 2,
  "targetPosition" : { // 当取货放货的时候,theta为货叉的旋转角度
    "x" : 11.66,
    "y" : 7.16,//4.79,//7.16,
    "theta" : 3.14
  },
  "opType":2,
  "targetHeight":1200,//高度
  "binType":0//货箱类型 0:扫码识别，1:不用扫码识别
   // "BinModel":"CUSTOM1-SIZE1.2x1.2x1.2",  //料箱模型，默认不用配置，箱子和机器人不符合才需要配置,配错可能会导致取货失败
}

### 货叉放货到背篓上
POST http://localhost:5800/api/hai/test
content-type: appliction/json

{
    "robotId": "1501",
    "msgType": 3,
    "opType":3,
    "srcTray":{
       "id":0,  // 货叉编号 0
        "type":0  // 货叉 0，背篓 1, 定制背篓 2
   },
     "dstTray":{
       "id":0, //背篓编号 0
       "type": 1
   }

 }


### 从背篓上取货到货叉上
POST http://localhost:5800/api/hai/test
content-type: appliction/json

{
    "robotId": "1501",
    "msgType": 3,
    "opType":3,
    "srcTray":{
       "id":0,
        "type":1  // 货叉 0，背篓 1, 定制背篓 2
   },
     "dstTray":{
       "id":0, 
       "type": 0
   }
 }

### 从货叉取货到料架上
POST http://localhost:5800/api/hai/test
content-type: appliction/json

{
  "robotId": "1501",
  "msgType": 2,
  "targetPosition" : {
    "x" : 11.66,
    "y" : 7.16,//4.79,//7.16,
    "theta" : 3.14
  },
  "opType":0,
  "targetHeight":1200,//高度
  "binType":0 //货箱类型 0:扫码识别，1:不用扫码识别
}


### 从货叉取货到料架上
POST http://localhost:5800/api/hai/test
content-type: appliction/json

{
    "robotId": "1501",
    "msgType": 4, //4暂停 5恢复
    // 0 机器人执行完成当前任务后停止，后续任务的任务不执行，不发恢复不执行
    "stopType": 1 // 1 机器人移动或者旋转的实际发送,直接停止，恢复后继续执行
}


### 测试任务
POST http://localhost:5800/api/hai/test-task
content-type: appliction/json

{
  "robotId": "1",
  "from" : { "x": 0.0, "y": 3.0, "direction": 0 },
  "steps": [
    {
      "operation": "Move",
      "position": { "x": 3.0, "y": 0.0, "direction": 0 }
    },
    {
      "operation": "Move",
      "position": { "x": 4.0, "y": 3.0, "direction": 0 }
    }
  ]
}