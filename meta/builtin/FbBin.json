{"name": "Fb<PERSON><PERSON>", "label": "库位", "group": "MainData", "builtin": true, "type": "Entity", "fields": {"assemblyLine": {"name": "assemblyLine", "label": "所属产线", "type": "Reference", "scale": "Single", "refEntity": "FbAssemblyLine", "disabled": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 15, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "district": {"name": "district", "label": "所属库区", "type": "Reference", "scale": "Single", "refEntity": "FbDistrict", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "column": {"name": "column", "label": "列", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 17, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 43, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "id": {"name": "id", "label": "库位编号", "type": "String", "scale": "Single", "inputRequired": true, "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": -100, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "container": {"name": "container", "label": "库位上容器", "type": "Reference", "scale": "Single", "refEntity": "FbContainer", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 45, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "layer": {"name": "layer", "label": "层", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 18, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "row": {"name": "row", "label": "排", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 16, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "warehouse": {"name": "warehouse", "label": "所属仓库", "type": "Reference", "scale": "Single", "refEntity": "FbWarehouse", "disabled": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "rack": {"name": "rack", "label": "所属货架", "type": "Reference", "scale": "Single", "refEntity": "FbRack", "disabled": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 14, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 28, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 27, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "workSite": {"name": "workSite", "label": "所属工位", "type": "Reference", "scale": "Single", "refEntity": "FbWorkSite", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 20, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "channel": {"name": "channel", "label": "所在巷道", "type": "String", "scale": "Single", "refEntity": "FbRackChannel", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 100, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 42, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 44, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "occupied": {"name": "occupied", "label": "有货", "type": "Boolean", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "trueText": "有货", "trueStyle": "{\"backgroundColor\": \"#86B4F2\", \"padding\": \"2px 4px\", \"borderRadius\": \"4px\"}"}, "listBatchButtons": {"buttons": []}}, "depth": {"name": "depth", "label": "深", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 19, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "remark": {"name": "remark", "label": "备注", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "robotX": {"name": "robotX", "label": "机器人位置 X", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 22, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "robotY": {"name": "robotY", "label": "机器人位置 Y", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 23, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "boxHeight": {"name": "boxHeight", "label": "货叉抬升高度", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 24, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "boxDirection": {"name": "boxDirection", "label": "货叉方向", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 25, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "robotDirection": {"name": "robotDirection", "label": "机器人方向", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 26, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "materialCategoryLabel": {"name": "materialCategoryLabel", "label": "存储物料分类名称", "type": "String", "scale": "Single", "disabled": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 100, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 21, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btDisabled": {"name": "btDisabled", "label": "停用", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "locked": {"name": "locked", "label": "锁定", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "trueIcon": "lock", "trueStyle": "{\"color\": \"#FFB942\"}"}, "listBatchButtons": {"buttons": [{"label": "解锁", "kind": "warning", "displayOrder": 0}]}}, "lockedBy": {"name": "locked<PERSON>y", "label": "锁定原因", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "loadStatus": {"name": "loadStatus", "label": "占用状态", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "Empty", "label": "未占用", "color": "#b3b3b3"}, {"value": "Occupied", "label": "已占用", "color": "#009ce0"}, {"value": "ToLoad", "label": "即将运来", "color": "#fcc400"}, {"value": "ToUnload", "label": "即将运走", "color": "#fa28ff"}]}, "defaultValue": "Empty", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "asTag": true}, "listBatchButtons": {"buttons": []}}, "purpose": {"name": "purpose", "label": "用途", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "pendingContainer": {"name": "pending<PERSON><PERSON><PERSON>", "label": "要被运来的容器", "type": "Reference", "scale": "Single", "refEntity": "FbContainer", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}}, "indexes": [], "idGen": {"flowNoWidth": 0}, "digest": {"fields": ["id", "btDisabled", "occupied", "locked"], "formatJs": "return ev.id + (ev.btDisabled ? \" | 停用\": \"\") + (ev.occupied ? \" | 有货\": \"\") +  (ev.locked ? \" | 处理中\": \"\")"}, "scale": "Instances", "disabledFilter": true, "sort": "id", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": [{"label": "处理中（锁定）", "type": "Count", "filter": {"type": "Compound", "items": [{"type": "General", "operator": "Eq", "field1": "locked", "value": true}]}, "valueColor": "#fcc400"}, {"label": "有货", "type": "Count", "filter": {"type": "Compound", "items": [{"type": "General", "operator": "Eq", "field1": "occupied", "value": true}]}, "valueColor": "#16a5a5"}, {"label": "即将运来", "type": "Count", "filter": {"type": "Compound", "items": [{"type": "General", "operator": "Eq", "field1": "loadStatus", "value": "ToLoad"}]}, "valueColor": "#fe9200"}, {"label": "即将运走", "type": "Count", "filter": {"type": "Compound", "items": [{"type": "General", "operator": "Eq", "field1": "loadStatus", "value": "ToUnload"}]}, "valueColor": "#fe9200"}]}, "listCard": {"lines": [[{"type": "Simple", "fieldName": "id", "formatMapping": []}, {"type": "Simple", "fieldName": "btDisabled", "tag": true, "replaceText": "已停用", "alignRight": true, "hideIfBlankFalse": true, "formatMapping": []}], [{"type": "Simple", "fieldName": "district", "prefix": "所属库区", "prefixPaddingRight": 8, "hideIfBlankFalse": true, "formatMapping": []}], [{"type": "Simple", "fieldName": "container", "prefix": "库位上容器", "prefixPaddingRight": 8, "hideIfBlankFalse": true, "formatMapping": []}], [{"type": "Simple", "fieldName": "loadStatus", "marginRight": 8, "tag": true, "hideIfBlankFalse": true, "formatMapping": []}, {"type": "Simple", "fieldName": "occupied", "marginRight": 8, "tag": true, "tagStyle": "for-error", "replaceText": "有货", "hideIfBlankFalse": true, "formatMapping": []}, {"type": "Simple", "fieldName": "locked", "tag": true, "tagStyle": "for-error", "replaceText": "已锁定", "hideIfBlankFalse": true, "formatMapping": []}]]}, "orderConfig": {"states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"extEnabled": true, "buttons": [{"type": "Builtin", "label": "新增", "kind": "primary", "builtinId": "Create"}, {"type": "Builtin", "label": "批量编辑", "builtinId": "BatchEdit"}, {"type": "Builtin", "label": "删除", "kind": "warning", "builtinId": "RemoveSelected"}, {"type": "Builtin", "label": "导出", "builtinId": "Export"}, {"type": "Builtin", "label": "导入", "builtinId": "Import"}, {"type": "Special", "label": "批量创建库位", "extId": "BatchCreateBin"}]}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}, "dateClean": {"type": "ByDay"}, "kinds": {"kinds": {}}, "states": {"states": {}}, "menuColor": "#0ACFB3", "menuIcon": "cubes", "quickInput": {"items": []}}