{"name": "FbDevTask", "label": "协同任务", "group": "<PERSON>", "builtin": true, "type": "Entity", "fields": {"ro": {"name": "ro", "label": "企业", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 15, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "comments": {"name": "comments", "label": "评论", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 10000, "numWidth": 0, "numScale": 0, "view": {"specialInput": "FbDevTaskComments", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 14, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "images": {"name": "images", "label": "附件图片", "type": "Image", "scale": "List", "decimals": 0, "copiable": true, "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 10, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "files": {"name": "files", "label": "附件文件", "type": "File", "scale": "List", "decimals": 0, "copiable": true, "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 11, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "id": {"name": "id", "label": "编号", "type": "String", "scale": "Single", "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": -100, "lineColumnWidth": 0, "listTableColumnWidth": 140, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 32, "lineColumnWidth": 0, "listTableColumnWidth": 100, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "testResult": {"name": "testResult", "label": "测试结果", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 3000, "numWidth": 0, "numScale": 0, "view": {"input": "TextArea", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 12, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "devVersion": {"name": "devVersion", "label": "迭代", "type": "Reference", "scale": "Single", "refEntity": "FbDevVersion", "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 80, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 33, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 16, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "description": {"name": "description", "label": "描述", "type": "String", "scale": "Single", "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 10000, "numWidth": 0, "numScale": 0, "view": {"input": "TextArea", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 9, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "processedBy": {"name": "processedBy", "label": "处理人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 100, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "project": {"name": "project", "label": "项目", "type": "String", "scale": "Single", "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "title": {"name": "title", "label": "标题", "type": "String", "scale": "Single", "fuzzyFilter": true, "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "block": true, "lineColumnWidth": 0, "listTableColumnWidth": 400, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 30, "lineColumnWidth": 0, "listTableColumnWidth": 100, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "testImages": {"name": "testImages", "label": "测试图片", "type": "Image", "scale": "List", "decimals": 0, "copiable": true, "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 13, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "state": {"name": "state", "label": "状态", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "Open", "label": "新建"}, {"value": "Processing", "label": "进行中"}, {"value": "Resolved", "label": "已解决"}, {"value": "Closed", "label": "已关闭"}, {"value": "Rejected", "label": "已拒绝"}]}, "defaultValue": "Open", "decimals": 0, "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 20, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 80, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 31, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "kind": {"name": "kind", "label": "类型", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "Feature", "label": "功能"}, {"value": "Bug", "label": "缺陷"}, {"value": "Improvement", "label": "优化"}, {"value": "Test", "label": "测试"}]}, "defaultValue": "Feature", "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 20, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 80, "listTableColumnAlign": "center", "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "priority": {"name": "priority", "label": "优先级", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "High", "label": "高"}, {"value": "Normal", "label": "中"}, {"value": "Low", "label": "低"}]}, "defaultValue": "Normal", "copiable": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 60, "listTableColumnAlign": "center", "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}}, "indexes": [], "idGen": {"enabled": true, "fixedPrefix": "TK-", "flowNoWidth": 3}, "digest": {"fields": ["id"]}, "scale": "Instances", "sort": "-createdOn", "commentEnabled": true, "trackChange": true, "userNotice": {"create": true, "update": true, "delete": true, "comment": true, "targetUserFields": ["modifiedBy", "processedBy", "created<PERSON>y"]}, "actions": {}, "listTableStyleExt": "const r = { rows: {}, cells: {} };\nconst stateColors = {\n    \"Open\": \"#D8D8D8\",\n    \"Processing\": \"#8646DB\",\n    \"Resolved\": \"#4ABCFF\",\n    \"Closed\": \"#26C51C\",\n    \"Rejected\": \"#FF894A\",\n}\nfor (const row of page) {\n    const id = row[\"id\"]\n\n    r.cells[id] = { state: { style: { background: stateColors[row[\"state\"]], color: \"#fff\"} } }\n}\nreturn r;", "listStats": {"items": []}, "listCard": {"lines": [[{"type": "Simple", "fieldName": "title", "formatMapping": []}], [{"type": "Simple", "fieldName": "state", "formatMapping": []}]]}, "orderConfig": {"states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}, "dateClean": {"type": "ByDay"}, "kinds": {"kinds": {}}, "states": {"states": {}}, "menuColor": "#0ACFB3", "menuIcon": "list-check", "notMenu": true, "quickInput": {"items": []}}