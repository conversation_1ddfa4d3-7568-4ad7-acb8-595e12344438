{"name": "TransportStep", "label": "新通用运单步骤", "group": "Fleet", "builtin": true, "type": "Entity", "fields": {"orderId": {"name": "orderId", "label": "运单单号", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 100, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 200, "timestampPrecision": "Second", "listTableColumnAlign": "left"}, "listBatchButtons": {"buttons": []}}, "stepIndex": {"name": "stepIndex", "label": "第几步", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 80, "timestampPrecision": "Second", "listTableColumnAlign": "right"}, "listBatchButtons": {"buttons": []}}, "status": {"name": "status", "label": "状态", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 20, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "listTableColumnAlign": "center"}, "listBatchButtons": {"buttons": []}}, "forLoad": {"name": "forLoad", "label": "在此步取货", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 100, "timestampPrecision": "Second", "listTableColumnAlign": "center"}, "listBatchButtons": {"buttons": []}, "copiable": true}, "forUnload": {"name": "forUnload", "label": "在此步放货", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 100, "timestampPrecision": "Second", "listTableColumnAlign": "center"}, "listBatchButtons": {"buttons": []}, "copiable": true}, "startOn": {"name": "startOn", "label": "开始执行时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 160, "timestampPrecision": "Second", "listTableColumnAlign": "center"}, "listBatchButtons": {"buttons": []}}, "endOn": {"name": "endOn", "label": "结束执行时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 160, "timestampPrecision": "Second", "listTableColumnAlign": "center"}, "listBatchButtons": {"buttons": []}}, "id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 220, "timestampPrecision": "Second", "listTableColumnAlign": "left"}, "listBatchButtons": {"buttons": []}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 16, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "listTableColumnAlign": "left"}, "listBatchButtons": {"buttons": []}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 18, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "listTableColumnAlign": "left"}, "listBatchButtons": {"buttons": []}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 17, "lineColumnWidth": 0, "listTableColumnWidth": 160, "timestampPrecision": "Second", "listTableColumnAlign": "center"}, "listBatchButtons": {"buttons": []}}, "modifiedOn": {"name": "modifiedOn", "label": "修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 19, "lineColumnWidth": 0, "listTableColumnWidth": 160, "timestampPrecision": "Second", "listTableColumnAlign": "center"}, "listBatchButtons": {"buttons": []}}, "location": {"name": "location", "label": "作业位置", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "listTableColumnAlign": "left"}, "listBatchButtons": {"buttons": []}, "copiable": true}, "executingTime": {"name": "executingTime", "label": "执行耗时（秒）", "type": "Float", "scale": "Single", "sqlType": "Decimal", "length": 0, "numWidth": 10, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 14, "lineColumnWidth": 0, "listTableColumnWidth": 150, "timestampPrecision": "Second", "listTableColumnAlign": "right"}, "listBatchButtons": {"buttons": []}, "decimals": 3}, "processingTime": {"name": "processingTime", "label": "处理耗时（秒）", "type": "Float", "scale": "Single", "sqlType": "Decimal", "length": 0, "numWidth": 10, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 150, "timestampPrecision": "Second", "listTableColumnAlign": "right"}, "listBatchButtons": {"buttons": []}, "decimals": 3}, "rbkArgs": {"name": "rbkArgs", "label": "动作参数", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 10000, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 200, "listTableColumnAlign": "left", "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}, "copiable": true}, "withdrawOrderAllowed": {"name": "withdrawOrderAllowed", "label": "允许重分派", "type": "Boolean", "scale": "Single", "copiable": true, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 100, "listTableColumnAlign": "center", "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "nextStepSameOrder": {"name": "nextStepSameOrder", "label": "下一个步骤必须是同一运单的", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 15, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}}, "indexes": [], "idGen": {"flowNoWidth": 4}, "digest": {"fields": []}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": [[{"type": "Simple", "fieldName": "orderId", "formatMapping": []}, {"type": "Simple", "fieldName": "status", "tag": true, "tagStyle": "for-created", "alignRight": true, "hideIfBlankFalse": true, "formatMapping": []}], [{"type": "Simple", "fieldName": "stepIndex", "prefix": "第", "suffix": "步", "prefixPaddingRight": 8, "suffixPaddingLeft": 8, "hideIfBlankFalse": true, "formatMapping": []}], [{"type": "Simple", "fieldName": "locationSite", "prefix": "点位", "prefixPaddingRight": 8, "marginRight": 8, "formatMapping": []}, {"type": "Simple", "fieldName": "forLoad", "tag": true, "tagStyle": "for-going", "replaceText": "取货点", "hideIfBlankFalse": true, "formatMapping": []}, {"type": "Simple", "fieldName": "forUnload", "tag": true, "tagStyle": "for-success", "replaceText": "是放货点", "hideIfBlankFalse": true, "formatMapping": []}], [{"type": "Simple", "fieldName": "operation", "prefix": "动作", "prefixPaddingRight": 8, "hideIfBlankFalse": true, "formatMapping": []}], [{"type": "Simple", "fieldName": "modifiedOn", "formatMapping": []}]]}, "orderConfig": {"states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}, "dateClean": {"type": "ByDay"}, "kinds": {"kinds": {}}, "states": {"states": {}}, "menuColor": "#1A7BF2", "menuIcon": "code-compare", "quickInput": {"items": []}, "modifyProhibition": {"type": "Query"}, "removeProhibition": {"type": "Query"}, "sort": "-createdOn"}