{"name": "PickOrderLine", "label": "分拣单行", "group": "Warehouse", "builtin": true, "type": "Entity", "fields": {"btParentId": {"name": "btParentId", "label": "所属单据", "type": "Reference", "scale": "Single", "refEntity": "PutinContainerOrder", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btLineNo": {"name": "btLineNo", "label": "行号", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": -100, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 35, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 37, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 36, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 38, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "container": {"name": "container", "label": "容器", "type": "Reference", "scale": "Single", "refEntity": "FbContainer", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 15, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "subContainerId": {"name": "subContainerId", "label": "格号", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 16, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "planQty": {"name": "planQty", "label": "期望拣出数", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 17, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "qty": {"name": "qty", "label": "实际拣出数", "type": "Float", "scale": "Single", "inputRequired": true, "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 18, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btMaterial": {"name": "btMaterial", "label": "物料", "type": "Reference", "scale": "Single", "refEntity": "FbMaterial", "inputRequired": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 240, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btMaterialName": {"name": "btMaterialName", "label": "物料名称", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "name", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "qualityLevel": {"name": "qualityLevel", "label": "质量等级", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "lotNo": {"name": "lotNo", "label": "批次号", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 14, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btMaterialId": {"name": "btMaterialId", "label": "物料编号", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "id", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btMaterialModel": {"name": "btMaterialModel", "label": "物料型号", "type": "String", "scale": "Single", "disabled": true, "refField": "btMaterial", "refFieldField": "model", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btMaterialSpec": {"name": "btMaterialSpec", "label": "物料规格", "type": "String", "scale": "Single", "disabled": true, "refField": "btMaterial", "refFieldField": "spec", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btMaterialCategory": {"name": "btMaterialCategory", "label": "物料分类编号", "type": "String", "scale": "Single", "disabled": true, "refField": "btMaterial", "refFieldField": "leafCategory.id", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btMaterialCategoryName": {"name": "btMaterialCategoryName", "label": "物料分类名称", "type": "String", "scale": "Single", "disabled": true, "refField": "btMaterial", "refFieldField": "leafCategory.name", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btMaterialImage": {"name": "btMaterialImage", "label": "物料图片", "type": "Image", "scale": "Single", "disabled": true, "refField": "btMaterial", "refFieldField": "image", "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "sourceOrderId": {"name": "sourceOrderId", "label": "来源单据", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Editable", "displayOrder": 19, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "sourceLineId": {"name": "sourceLineId", "label": "来源单行 ID", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 20, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "sourceLineNo": {"name": "sourceLineNo", "label": "来源行号", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 21, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}}, "indexes": [], "idGen": {"flowNoWidth": 4}, "digest": {"fields": []}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": [[{"type": "Simple", "fieldName": "btMaterialName", "formatMapping": []}], [{"type": "Simple", "fieldName": "btMaterialId", "prefix": "物料编号", "prefixPaddingRight": 8, "hideIfBlankFalse": true, "formatMapping": []}], [{"type": "Simple", "fieldName": "lotNo", "prefix": "批次", "prefixPaddingRight": 8, "hideIfBlankFalse": true, "formatMapping": []}], [{"type": "Simple", "fieldName": "planQty", "prefix": "期望拣出", "prefixPaddingRight": 8, "hideIfBlankFalse": true, "formatMapping": []}, {"type": "Simple", "fieldName": "qty", "prefix": "实际拣出", "prefixPaddingRight": 8, "alignRight": true, "hideIfBlankFalse": true, "formatMapping": []}], [{"type": "Simple", "fieldName": "subContainerId", "prefix": "格号", "prefixPaddingRight": 8, "hideIfBlankFalse": true, "formatMapping": []}]]}, "orderConfig": {"states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}, "dateClean": {"type": "ByDay"}, "kinds": {"kinds": {}}, "states": {"states": {}}, "menuColor": "#FC8E0E", "menuIcon": "inbox-out", "notMenu": true, "quickInput": {"items": []}}