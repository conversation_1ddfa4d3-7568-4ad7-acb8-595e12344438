{"name": "QsOldInvLine", "label": "QS 现有库存明细", "group": "Quick Store", "builtin": true, "type": "Entity", "fields": {"modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 43, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "qty": {"name": "qty", "label": "数量", "type": "Float", "scale": "Single", "inputRequired": true, "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 14, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 40, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 42, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": -100, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 41, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "leafContainer": {"name": "leafContainer", "label": "最内层容器", "type": "Reference", "scale": "Single", "refEntity": "FbContainer", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 16, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "inboundOn": {"name": "inboundOn", "label": "入库时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 18, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "matLotNo": {"name": "matLotNo", "label": "批次", "type": "String", "scale": "Single", "disabled": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 100, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 19, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "matSerialNo": {"name": "matSerialNo", "label": "序列号", "type": "String", "scale": "Single", "disabled": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 100, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 20, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "price": {"name": "price", "label": "单价", "type": "Float", "scale": "Single", "disabled": true, "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "amount": {"name": "amount", "label": "金额", "type": "Float", "scale": "Single", "disabled": true, "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 21, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "vendor": {"name": "vendor", "label": "供应商", "type": "Reference", "scale": "Single", "refEntity": "FbVendor", "disabled": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 22, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "owner": {"name": "owner", "label": "货主", "type": "Reference", "scale": "Single", "refEntity": "FbGoodsOwner", "disabled": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 23, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "expDate": {"name": "expDate", "label": "有效期", "type": "Date", "scale": "Single", "disabled": true, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 24, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "mfgDate": {"name": "mfgDate", "label": "生产日期", "type": "Date", "scale": "Single", "disabled": true, "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 25, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btMaterialName": {"name": "btMaterialName", "label": "物料名称", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "name", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 100, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 300, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btMaterialModel": {"name": "btMaterialModel", "label": "物料型号", "type": "String", "scale": "Single", "disabled": true, "refField": "btMaterial", "refFieldField": "model", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 100, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btMaterialSpec": {"name": "btMaterialSpec", "label": "物料规格", "type": "String", "scale": "Single", "disabled": true, "refField": "btMaterial", "refFieldField": "spec", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 100, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btMaterialCategory": {"name": "btMaterialCategory", "label": "物料分类编号", "type": "String", "scale": "Single", "disabled": true, "refField": "btMaterial", "refFieldField": "leafCategory.id", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 100, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btMaterialImage": {"name": "btMaterialImage", "label": "物料图片", "type": "Image", "scale": "Single", "disabled": true, "refField": "btMaterial", "refFieldField": "image", "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btMaterialCategoryName": {"name": "btMaterialCategoryName", "label": "物料分类名称", "type": "String", "scale": "Single", "disabled": true, "refField": "btMaterial", "refFieldField": "leafCategory.name", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 100, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "subContainerId": {"name": "subContainerId", "label": "格子", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 17, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btMaterial": {"name": "btMaterial", "label": "物料", "type": "Reference", "scale": "Single", "refEntity": "FbMaterial", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "lotNo": {"name": "lotNo", "label": "批次号", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 100, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btParentId": {"name": "btParentId", "label": "所属订单", "type": "Reference", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btLineNo": {"name": "btLineNo", "label": "行号", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "topContainer": {"name": "topContainer", "label": "最外层容器", "type": "Reference", "scale": "Single", "refEntity": "FbContainer", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 15, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "refInvId": {"name": "refInvId", "label": "关联库存明细 ID", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 26, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}}, "indexes": [], "idGen": {"flowNoWidth": 4}, "digest": {"fields": []}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": [{"label": "处理中（锁定）", "type": "Count", "filter": {"type": "Compound", "items": [{"type": "General", "operator": "Eq", "field1": "locked", "value": true}]}, "valueColor": "#fcc400"}]}, "listCard": {"imageFieldName": "btMaterialImage", "lines": [[{"type": "Simple", "fieldName": "btMaterial", "formatMapping": []}, {"type": "Simple", "fieldName": "qty", "alignRight": true, "formatMapping": [{"operator": "Other", "alignRight": true}]}], [{"type": "Simple", "fieldName": "btMaterialName", "formatMapping": []}]]}, "orderConfig": {"states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}, "dateClean": {"type": "ByDay"}, "kinds": {"kinds": {}}, "states": {"states": {}}, "menuColor": "#3CC62F", "menuIcon": "boxes-stacked", "quickInput": {"items": []}}