{"name": "QsInboundOrder", "label": "QS 入库单", "group": "Quick Store", "builtin": true, "type": "Entity", "fields": {"createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 26, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 27, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "remark": {"name": "remark", "label": "备注", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "id": {"name": "id", "label": "单号", "type": "String", "scale": "Single", "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": -100, "lineColumnWidth": 0, "listTableColumnWidth": 200, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 25, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 28, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btLines": {"name": "btLines", "label": "单行", "type": "Component", "scale": "List", "refEntity": "QsInboundOrderLine", "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"input": "ComponentTable", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 10, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "priority": {"name": "priority", "label": "优先级", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "callContainerAll": {"name": "callContainerAll", "label": "叫空容器分配完成", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "otherType": {"name": "otherType", "label": "其他类型", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btOrderState": {"name": "btOrderState", "label": "业务状态", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "Init", "label": "未提交", "color": "#009ce0"}, {"value": "Committed", "label": "已提交", "color": "#4AC774"}, {"value": "Cancelled", "label": "已取消", "color": "#999999"}]}, "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": -80, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "asTag": true}, "listBatchButtons": {"buttons": []}}, "btOrderStateReason": {"name": "btOrderStateReason", "label": "状态说明", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 500, "truncate": true, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 4, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btBzKind": {"name": "btBzKind", "label": "业务类型", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "Normal", "label": "普通入库", "color": "#009ce0"}, {"value": "Other", "label": "其他入库", "color": "#fcc400"}]}, "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": -90, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "asTag": true}, "listBatchButtons": {"buttons": []}}}, "indexes": [], "idGen": {"enabled": true, "fixedPrefix": "OO", "flowNoWidth": 5}, "digest": {"fields": []}, "scale": "Instances", "sort": "-id", "trackChange": true, "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": [[{"type": "Simple", "fieldName": "id", "formatMapping": []}, {"type": "Simple", "fieldName": "btOrderState", "tag": true, "alignRight": true, "formatMapping": []}], [{"type": "Simple", "fieldName": "btBzKind", "tag": true, "formatMapping": []}, {"type": "Simple", "fieldName": "created<PERSON>y", "alignRight": true, "formatMapping": [{"operator": "Other", "alignRight": true}]}, {"type": "Simple", "fieldName": "createdOn", "formatMapping": []}]]}, "orderConfig": {"enabled": true, "states": [{"id": "Init", "label": "i18n_entity.QsInboundOrder.orderConfig.states.Init.label", "entityEditable": true, "color": "#777", "nextStates": [{"id": "Committed", "buttonLabel": "i18n_entity.QsInboundOrder.orderConfig.states.Init.nextStates.Committed.buttonLabel", "buttonKind": "primary"}]}, {"id": "Committed", "label": "i18n_entity.QsInboundOrder.orderConfig.states.Committed.label", "finalState": true, "color": "#4AC774", "nextStates": []}], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": ["qty"], "occurredQtyField": "finishedQty", "planQtyField": "planQty", "orderStatesToCreateInv": [], "toAssignInv": true, "outboundInvAssignState": "Committed", "outboundInvStates": ["Storing"], "invRef": true, "invRefWarehouseHeadField": "warehouse", "invRefDistrictHeadField": "district"}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}, "dateClean": {"type": "ByDay"}, "kinds": {"enabled": true, "kinds": {"Normal": {"name": "Normal", "label": "普通入库", "color": "#009ce0", "displayOrder": 0, "fields": {"id": {}, "btBzKind": {"inputRequired": true}, "btOrderState": {}, "btOrderStateReason": {}, "otherType": {"disabled": true}, "priority": {}, "remark": {}, "callContainerAll": {}, "createdBy": {}, "createdOn": {}, "modifiedBy": {}, "version": {}, "modifiedOn": {}, "btLines": {}, "ro": {}}}, "Other": {"name": "Other", "label": "其他入库", "color": "#fcc400", "displayOrder": 0, "fields": {"id": {}, "btBzKind": {"inputRequired": true}, "btOrderState": {}, "btOrderStateReason": {}, "otherType": {}, "priority": {}, "remark": {"inputRequired": true}, "callContainerAll": {}, "createdBy": {}, "createdOn": {}, "modifiedBy": {}, "version": {}, "modifiedOn": {}, "btLines": {}, "ro": {}}}}}, "states": {"enabled": true, "states": {"Init": {"id": "Init", "label": "未提交", "entityEditable": true, "color": "#009ce0", "nextStates": [{"id": "Committed", "buttonLabel": "提交", "buttonKind": "primary"}, {"id": "Cancelled", "buttonLabel": "取消"}]}, "Committed": {"id": "Committed", "label": "已提交", "finalState": true, "color": "#4AC774", "nextStates": []}, "Cancelled": {"id": "Cancelled", "label": "已取消", "finalState": true, "color": "#999999", "nextStates": []}}}, "menuColor": "#3CC62F", "menuIcon": "ramp-loading", "quickInput": {"items": []}}