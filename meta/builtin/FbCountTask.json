{"name": "FbCountTask", "label": "盘点任务", "group": "Warehouse", "builtin": true, "type": "Entity", "fields": {"remark": {"name": "remark", "label": "说明", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "bin": {"name": "bin", "label": "来源库位", "type": "Reference", "scale": "Single", "refEntity": "Fb<PERSON><PERSON>", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "container": {"name": "container", "label": "盘点容器", "type": "Reference", "scale": "Single", "refEntity": "FbContainer", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btLines": {"name": "btLines", "label": "单行", "type": "Component", "scale": "List", "refEntity": "FbCountTaskLine", "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 12, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "countOrderId": {"name": "countOrderId", "label": "所属盘点单", "type": "Reference", "scale": "Single", "refEntity": "FbCountOrder", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": -100, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 160, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 26, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 28, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 27, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 29, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "containerOutOrderId": {"name": "containerOutOrderId", "label": "容器出库运单号", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 100, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 9, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "containerInOrderId": {"name": "containerInOrderId", "label": "容器入库运单号", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 100, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 10, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "submittedPostProcessed": {"name": "submittedPostProcessed", "label": "提交后处理完成", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btOrderState": {"name": "btOrderState", "label": "业务状态", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "Init", "label": "待盘点"}, {"value": "Submitted", "label": "已盘点"}]}, "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": -80, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "asTag": true}, "listBatchButtons": {"buttons": []}}, "btOrderStateReason": {"name": "btOrderStateReason", "label": "状态说明", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 500, "truncate": true, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 3, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}}, "indexes": [], "idGen": {"enabled": true, "fixedPrefix": "CT-", "flowNoWidth": 6}, "digest": {"fields": []}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": [{"label": "待盘点", "type": "Count", "filter": {"type": "Compound", "items": [{"type": "General", "operator": "Eq", "field1": "btOrderState", "value": "Init"}]}, "valueColor": "#e27300"}]}, "listCard": {"lines": [[{"type": "Simple", "fieldName": "id", "formatMapping": []}, {"type": "Simple", "fieldName": "btOrderState", "tag": true, "alignRight": true, "hideIfBlankFalse": true, "formatMapping": []}], [{"type": "Simple", "fieldName": "container", "prefix": "容器", "prefixPaddingRight": 8, "hideIfBlankFalse": true, "formatMapping": []}], [{"type": "Simple", "fieldName": "bin", "prefix": "库位", "prefixPaddingRight": 8, "hideIfBlankFalse": true, "formatMapping": []}], [{"type": "Simple", "fieldName": "modifiedOn", "alignRight": true, "formatMapping": []}]]}, "orderConfig": {"enabled": true, "states": [{"id": "Init", "label": "i18n_entity.FbCountTask.orderConfig.states.Init.label", "entityEditable": true, "nextStates": [{"id": "Submitted", "buttonLabel": "i18n_entity.FbCountTask.orderConfig.states.Init.nextStates.Submitted.buttonLabel", "buttonKind": "primary"}]}, {"id": "Submitted", "label": "i18n_entity.FbCountTask.orderConfig.states.Submitted.label", "finalState": true, "nextStates": []}], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}, "dateClean": {"type": "ByDay"}, "kinds": {"kinds": {}}, "states": {"enabled": true, "states": {"Init": {"id": "Init", "label": "待盘点", "entityEditable": true, "nextStates": [{"id": "Submitted", "buttonLabel": "提交", "buttonKind": "primary"}]}, "Submitted": {"id": "Submitted", "label": "已盘点", "finalState": true, "nextStates": []}}}, "menuColor": "#3CC62F", "menuIcon": "box-check", "quickInput": {"items": []}}