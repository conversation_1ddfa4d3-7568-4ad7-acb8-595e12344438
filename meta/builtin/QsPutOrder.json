{"name": "QsPutOrder", "label": "QS 装货单", "group": "Quick Store", "builtin": true, "type": "Entity", "fields": {"container": {"name": "container", "label": "容器", "type": "Reference", "scale": "Single", "refEntity": "FbContainer", "inputRequired": true, "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btLines": {"name": "btLines", "label": "单行", "type": "Component", "scale": "List", "refEntity": "QsPutOrderLine", "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"input": "ComponentTable", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 9, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btOrderKind": {"name": "btOrderKind", "label": "类型", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "NormalTask", "label": "装货任务"}]}, "disabled": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 100, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "id": {"name": "id", "label": "单号", "type": "String", "scale": "Single", "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": -100, "lineColumnWidth": 0, "listTableColumnWidth": 200, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 24, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 26, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 25, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 27, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "bin": {"name": "bin", "label": "上架库位", "type": "Reference", "scale": "Single", "refEntity": "Fb<PERSON><PERSON>", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "oldInvLines": {"name": "oldInvLines", "label": "容器内原有库存明细", "type": "Component", "scale": "List", "refEntity": "QsOldInvLine", "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "targetBin": {"name": "targetBin", "label": "目标库位", "type": "Reference", "scale": "Single", "refEntity": "Fb<PERSON><PERSON>", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btOrderState": {"name": "btOrderState", "label": "业务状态", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "Todo", "label": "待装货", "color": "#e37e2a"}, {"value": "Done", "label": "装货完成", "color": "#159215"}]}, "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": -80, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "asTag": true}, "listBatchButtons": {"buttons": []}}, "btOrderStateReason": {"name": "btOrderStateReason", "label": "状态说明", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 500, "truncate": true, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 3, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}}, "indexes": [], "idGen": {"enabled": true, "fixedPrefix": "PTO-", "flowNoWidth": 4}, "digest": {"fields": ["id"]}, "scale": "Instances", "trackChange": true, "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": [[{"type": "Simple", "fieldName": "id", "formatMapping": []}, {"type": "Simple", "fieldName": "btOrderState", "tag": true, "alignRight": true, "hideIfBlankFalse": true, "formatMapping": []}], [{"type": "Simple", "fieldName": "container", "prefix": "容器", "prefixPaddingRight": 8, "marginRight": 8, "formatMapping": []}], [{"type": "Simple", "fieldName": "bin", "prefix": "上架库位", "prefixPaddingRight": 8, "formatMapping": []}], [{"type": "Simple", "fieldName": "created<PERSON>y", "marginRight": 8, "formatMapping": []}, {"type": "Simple", "fieldName": "createdOn", "formatMapping": []}]]}, "orderConfig": {"enabled": true, "states": [{"id": "Todo", "label": "i18n_entity.QsPutOrder.orderConfig.states.Todo.label", "entityEditable": true, "color": "#e37e2a", "nextStates": [{"id": "Done", "buttonLabel": "i18n_entity.QsPutOrder.orderConfig.states.Todo.nextStates.Done.buttonLabel", "buttonKind": "primary"}]}, {"id": "Done", "label": "i18n_entity.QsPutOrder.orderConfig.states.Done.label", "finalState": true, "color": "#159215", "nextStates": []}], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}, "dateClean": {"type": "ByDay"}, "kinds": {"kinds": {}}, "states": {"enabled": true, "states": {"Todo": {"id": "Todo", "label": "待装货", "entityEditable": true, "color": "#e37e2a", "nextStates": [{"id": "Done", "buttonLabel": "完成", "buttonKind": "primary"}]}, "Done": {"id": "Done", "label": "装货完成", "finalState": true, "color": "#159215", "nextStates": []}}}, "menuColor": "#FC8E0E", "menuIcon": "inbox-in", "quickInput": {"items": []}}