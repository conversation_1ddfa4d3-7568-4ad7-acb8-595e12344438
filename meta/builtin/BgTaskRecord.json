{"name": "BgTaskRecord", "label": "后台任务记录", "group": "Core", "builtin": true, "type": "Entity", "fields": {"id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": -100, "lineColumnWidth": 0, "listTableColumnWidth": 160}, "listBatchButtons": {"buttons": []}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 21, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 23, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 22, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 24, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "defaultValue": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "paused": {"name": "paused", "label": "已暂停", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "name": {"name": "name", "label": "后台任务名", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "fault": {"name": "fault", "label": "任务失败", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "args": {"name": "args", "label": "输入参数", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 5000, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "faultMsg": {"name": "faultMsg", "label": "失败原因", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 500, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}}, "indexes": [], "idGen": {"flowNoWidth": 4}, "digest": {"fields": []}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": []}, "orderConfig": {"states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"extEnabled": true, "buttons": [{"type": "Builtin", "label": "导出", "kind": "normal", "builtinId": "Export", "enabledCondition": {}}, {"type": "Special", "label": "继续", "kind": "primary", "extId": "BgTaskResume", "confirmMsg": "确定要继续执行吗", "preSep": true, "enabledCondition": {}}, {"type": "Special", "label": "暂停", "kind": "primary", "extId": "BgTaskPause", "enabledCondition": {}}, {"type": "Special", "label": "故障重试", "kind": "success", "extId": "BgTaskRetry", "enabledCondition": {}}, {"type": "Special", "label": "取消", "kind": "error", "extId": "BgTaskCancel", "enabledCondition": {}}]}, "ListItem": {"buttons": []}, "View": {"extEnabled": true, "buttons": []}, "Create": {"buttons": []}, "Edit": {"extEnabled": true, "buttons": []}}, "codeParse": {"rules": []}, "dateClean": {"type": "ByDay"}, "kinds": {"kinds": {}}, "states": {"states": {}}, "quickInput": {"items": []}, "modifyProhibition": {"type": "Query", "filter": {"type": "Compound", "items": []}}, "removeProhibition": {"type": "<PERSON><PERSON><PERSON>", "filter": {"type": "Compound", "items": []}, "sourceLang": "python"}}