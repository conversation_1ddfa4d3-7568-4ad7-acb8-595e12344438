{"name": "QsOutboundOrder", "label": "QS 出库单", "group": "Quick Store", "builtin": true, "type": "Entity", "fields": {"createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 26, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 27, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "remark": {"name": "remark", "label": "备注", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "id": {"name": "id", "label": "单号", "type": "String", "scale": "Single", "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": -100, "lineColumnWidth": 0, "listTableColumnWidth": 200, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 25, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 28, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btLines": {"name": "btLines", "label": "单行", "type": "Component", "scale": "List", "refEntity": "QsOutboundOrderLine", "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"input": "ComponentTable", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 10, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btOrderKind": {"name": "btOrderKind", "label": "类型", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "<PERSON><PERSON><PERSON>", "label": "默认"}]}, "inputRequired": true, "defaultValue": "<PERSON><PERSON><PERSON>", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 100, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "priority": {"name": "priority", "label": "优先级", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "invAssignedAll": {"name": "invAssignedAll", "label": "库存分配完成", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "typePriority": {"name": "typePriority", "label": "类型优先级", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "btOrderState": {"name": "btOrderState", "label": "业务状态", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "Init", "label": "未提交", "color": "#777"}, {"value": "Committed", "label": "已提交", "color": "#4AC774"}]}, "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "Hidden", "displayOrder": -80, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "asTag": true}, "listBatchButtons": {"buttons": []}}, "btOrderStateReason": {"name": "btOrderStateReason", "label": "状态说明", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 500, "truncate": true, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 3, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}}, "indexes": [], "idGen": {"enabled": true, "fixedPrefix": "OO", "flowNoWidth": 5}, "digest": {"fields": []}, "scale": "Instances", "sort": "-id", "trackChange": true, "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": [[{"type": "Simple", "fieldName": "id", "formatMapping": []}, {"type": "Simple", "fieldName": "btOrderState", "tag": true, "alignRight": true, "hideIfBlankFalse": true, "formatMapping": []}], [{"type": "Simple", "fieldName": "btOrderKind", "marginRight": 8, "tag": true, "hideIfBlankFalse": true, "formatMapping": []}, {"type": "Conditional", "fieldName": "created<PERSON>y", "formatMapping": [{"operator": "Other", "alignRight": true}]}, {"type": "Simple", "fieldName": "createdOn", "formatMapping": []}]]}, "orderConfig": {"enabled": true, "states": [{"id": "Init", "label": "i18n_entity.QsOutboundOrder.orderConfig.states.Init.label", "entityEditable": true, "color": "#777", "nextStates": [{"id": "Committed", "buttonLabel": "i18n_entity.QsOutboundOrder.orderConfig.states.Init.nextStates.Committed.buttonLabel", "buttonKind": "primary"}]}, {"id": "Committed", "label": "i18n_entity.QsOutboundOrder.orderConfig.states.Committed.label", "finalState": true, "color": "#4AC774", "nextStates": []}], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": ["qty"], "occurredQtyField": "finishedQty", "planQtyField": "planQty", "orderStatesToCreateInv": [], "toAssignInv": true, "outboundInvAssignState": "Committed", "outboundInvStates": ["Storing"], "invRef": true, "invRefWarehouseHeadField": "warehouse", "invRefDistrictHeadField": "district"}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}, "dateClean": {"type": "ByDay"}, "kinds": {"kinds": {}}, "states": {"enabled": true, "states": {"Init": {"id": "Init", "label": "未提交", "entityEditable": true, "color": "#777", "nextStates": [{"id": "Committed", "buttonLabel": "提交", "buttonKind": "primary"}]}, "Committed": {"id": "Committed", "label": "已提交", "finalState": true, "color": "#4AC774", "nextStates": []}}}, "menuColor": "#FC8E0E", "menuIcon": "boxes-packing", "quickInput": {"items": []}}