{"name": "FbBinInv", "label": "库位库存", "group": "Core", "builtin": true, "type": "Entity", "fields": {"id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": -100, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 160}, "listBatchButtons": {"buttons": []}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 56, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 58, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 57, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 59, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "btMaterial": {"name": "btMaterial", "label": "物料", "type": "Reference", "scale": "Single", "refEntity": "FbMaterial", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 300, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "btMaterialId": {"name": "btMaterialId", "label": "物料编号", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "id", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "btMaterialName": {"name": "btMaterialName", "label": "物料名称", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "name", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "btMaterialModel": {"name": "btMaterialModel", "label": "物料型号", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "model", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "btMaterialSpec": {"name": "btMaterialSpec", "label": "物料规格", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "spec", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "btMaterialCategory": {"name": "btMaterialCategory", "label": "物料分类编号", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "leafCategory.id", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "btMaterialCategoryName": {"name": "btMaterialCategoryName", "label": "物料分类名称", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "leafCategory.name", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "btMaterialTopCategory": {"name": "btMaterialTopCategory", "label": "物料一级分类编号", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "leafCategory.id", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "btMaterialTopCategoryName": {"name": "btMaterialTopCategoryName", "label": "物料一级分类名称", "type": "String", "scale": "Single", "refField": "btMaterial", "refFieldField": "leafCategory.name", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "lotNo": {"name": "lotNo", "label": "批次号", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "qty": {"name": "qty", "label": "数量", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "amount": {"name": "amount", "label": "金额", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 14, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "topContainer": {"name": "topContainer", "label": "最外层容器", "type": "Reference", "scale": "Single", "refEntity": "FbContainer", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 15, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "topContainerType": {"name": "topContainerType", "label": "最外层容器类型", "type": "Reference", "scale": "Single", "refEntity": "FbContainerType", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 16, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "warehouse": {"name": "warehouse", "label": "仓库", "type": "Reference", "scale": "Single", "refEntity": "FbWarehouse", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 17, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "district": {"name": "district", "label": "库区", "type": "Reference", "scale": "Single", "refEntity": "FbDistrict", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 18, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "bin": {"name": "bin", "label": "库位", "type": "Reference", "scale": "Single", "refEntity": "Fb<PERSON><PERSON>", "refField": "bin", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 19, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "defaultValue": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "binDisabled": {"name": "binDisabled", "label": "库位停用", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 10, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 20, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "row": {"name": "row", "label": "排", "type": "Int", "scale": "Single", "refField": "bin", "sqlType": "Int", "length": 10, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 21, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "column": {"name": "column", "label": "列", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 10, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 22, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "layer": {"name": "layer", "label": "层", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 10, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 23, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "depth": {"name": "depth", "label": "深", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 10, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 24, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "rack": {"name": "rack", "label": "货架", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 25, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "channel": {"name": "channel", "label": "巷道", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 26, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "workSite": {"name": "workSite", "label": "工位", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 27, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "assemblyLine": {"name": "assemblyLine", "label": "所属产线", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 28, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "onRobot": {"name": "onRobot", "label": "所在机器人", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 29, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "robotBin": {"name": "<PERSON><PERSON><PERSON>", "label": "所在机器人库位", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 30, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "pendingContainer": {"name": "pending<PERSON><PERSON><PERSON>", "label": "要被运来的容器", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 31, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "binFilled": {"name": "binFilled", "label": "库位是否有货", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 10, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 32, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "containerFilled": {"name": "containerFilled", "label": "容器是否有货", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 10, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 33, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "subNum": {"name": "subNum", "label": "格数", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 10, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 34, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "containerDisabled": {"name": "containerDisabled", "label": "容器停用", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 10, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 35, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "materialIds": {"name": "materialIds", "label": "库位上的物料", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 10000, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 36, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "materialNames": {"name": "materialNames", "label": "库位上物料名称列表", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 10000, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 37, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "containers": {"name": "containers", "label": "容器列表", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 10000, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 38, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "materialCategoryLabel": {"name": "materialCategoryLabel", "label": "存储物料分类名称", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 100, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 39, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "btBzDesc": {"name": "btBzDesc", "label": "业务描述", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 42, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "btPrepare": {"name": "btPrepare", "label": "btPrepare", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 40, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "btBzMark": {"name": "btBzMark", "label": "业务标记", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 41, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}}, "indexes": [{"name": "bin", "fields": [{"name": "bin"}]}, {"name": "topContainer", "fields": [{"name": "topContainer"}]}], "idGen": {"flowNoWidth": 4}, "digest": {"fields": []}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {"createDisabled": true, "updateDisabled": true, "batchUpdateDisabled": true, "removeDisabled": true, "importDisabled": true}, "listStats": {"items": []}, "listCard": {"lines": []}, "orderConfig": {"states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}, "dateClean": {"type": "ByDay"}, "kinds": {"kinds": {}}, "states": {"states": {}}, "quickInput": {"items": []}, "modifyProhibition": {"type": "Query"}, "removeProhibition": {"type": "Query"}}