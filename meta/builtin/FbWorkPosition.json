{"name": "FbWorkPosition", "label": "岗位", "group": "MainData", "builtin": true, "type": "Entity", "fields": {"id": {"name": "id", "label": "单号", "type": "String", "scale": "Single", "inputRequired": true, "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": -100, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 23, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "version": {"name": "version", "label": "版本", "type": "Int", "scale": "Single", "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "disabled": {"name": "disabled", "label": "禁用", "type": "Boolean", "scale": "Single", "inputRequired": true, "decimals": 0, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "decimals": 0, "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 21, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "name": {"name": "name", "label": "名称", "type": "String", "scale": "Single", "inputRequired": true, "fuzzyFilter": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 100, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 20, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 22, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "ro": {"name": "ro", "label": "根组织", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "remark": {"name": "remark", "label": "备注", "type": "String", "scale": "Single", "inputRequired": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 600, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}}, "indexes": [], "idGen": {"flowNoWidth": 0}, "digest": {"fields": ["name"]}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": [[{"type": "Simple", "fieldName": "id", "formatMapping": []}, {"type": "Simple", "fieldName": "disabled", "tag": true, "replaceText": "已停用", "alignRight": true, "hideIfBlankFalse": true, "formatMapping": [{"operator": "Eq", "value": "true", "className": "rui-ev-card-part-tag-disabled", "replaceText": "已停用", "alignRight": true}, {"operator": "Other", "notShow": true}]}], [{"type": "Simple", "fieldName": "name", "hideIfBlankFalse": true, "formatMapping": []}]]}, "orderConfig": {"states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}, "dateClean": {"type": "ByDay"}, "kinds": {"kinds": {}}, "states": {"states": {}}, "menuColor": "#1A7BF2", "menuIcon": "user-helmet-safety", "quickInput": {"items": []}}