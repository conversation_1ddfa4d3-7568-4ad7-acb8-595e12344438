{"name": "TransportOrder", "label": "新通用运单", "group": "Fleet", "builtin": true, "type": "Entity", "fields": {"status": {"name": "status", "label": "状态", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "ToBeAllocated", "label": "待分派", "color": "#fcc400"}, {"value": "Allocated", "label": "已分派", "color": "#16a5a5"}, {"value": "Executing", "label": "执行中", "color": "#009ce0"}, {"value": "Pending", "label": "待执行", "color": "#e27300"}, {"value": "Withdrawn", "label": "被撤回", "color": "#fa28ff"}, {"value": "Done", "label": "已完成", "color": "#68bc00"}, {"value": "Cancelled", "label": "已取消", "color": "#808080"}]}, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "asTag": true}, "listBatchButtons": {"buttons": []}}, "priority": {"name": "priority", "label": "优先级", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}, "copiable": true}, "expectedRobotNames": {"name": "expectedRobotNames", "label": "指定机器人", "type": "String", "scale": "List", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 28, "lineColumnWidth": 0, "listTableColumnWidth": 140, "timestampPrecision": "Second", "listTableColumnAlign": "left"}, "listBatchButtons": {"buttons": []}, "copiable": true}, "expectedRobotGroups": {"name": "expectedRobotGroups", "label": "指定机器人组", "type": "String", "scale": "List", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 29, "lineColumnWidth": 0, "listTableColumnWidth": 140, "timestampPrecision": "Second", "listTableColumnAlign": "left"}, "listBatchButtons": {"buttons": []}, "copiable": true}, "containerId": {"name": "containerId", "label": "容器", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}, "copiable": true}, "taskBatch": {"name": "taskBatch", "label": "任务批次", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 31, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "listTableDisabled": true}, "listBatchButtons": {"buttons": []}}, "stepNum": {"name": "<PERSON><PERSON><PERSON>", "label": "任务步数", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 25, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "listTableColumnAlign": "right"}, "listBatchButtons": {"buttons": []}}, "stepFixed": {"name": "stepFixed", "label": "已封口", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 15, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "listTableColumnAlign": "center"}, "listBatchButtons": {"buttons": []}, "copiable": true}, "currentStepIndex": {"name": "currentStepIndex", "label": "当前步骤", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "doneStepIndex": {"name": "doneStepIndex", "label": "已完成步骤", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 32, "lineColumnWidth": 0, "listTableColumnWidth": 100, "timestampPrecision": "Second", "listTableColumnAlign": "right", "listTableDisabled": true}, "listBatchButtons": {"buttons": []}}, "actualRobotName": {"name": "actualRobotName", "label": "执行机器人", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 140, "timestampPrecision": "Second", "listTableColumnAlign": "left"}, "listBatchButtons": {"buttons": []}}, "robotAllocatedOn": {"name": "robotAllocatedOn", "label": "机器人分配时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 23, "lineColumnWidth": 0, "listTableColumnWidth": 160, "timestampPrecision": "Second", "listTableColumnAlign": "center"}, "listBatchButtons": {"buttons": []}}, "doneOn": {"name": "doneOn", "label": "完成时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 16, "lineColumnWidth": 0, "listTableColumnWidth": 160, "timestampPrecision": "Second", "listTableColumnAlign": "center"}, "listBatchButtons": {"buttons": []}}, "fault": {"name": "fault", "label": "故障", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "trueStyle": "{\"color\": \"red\"}", "trueText": "故障"}, "listBatchButtons": {"buttons": []}}, "loaded": {"name": "loaded", "label": "已取货", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 19, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "listTableColumnAlign": "center"}, "listBatchButtons": {"buttons": []}}, "unloaded": {"name": "unloaded", "label": "已放货", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 20, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "listTableColumnAlign": "center"}, "listBatchButtons": {"buttons": []}}, "ro": {"name": "ro", "label": "RO", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "id": {"name": "id", "label": "单号", "type": "String", "scale": "Single", "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 200, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 24, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 39, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "listTableColumnAlign": "left", "listTableDisabled": true}, "listBatchButtons": {"buttons": []}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 41, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second", "listTableColumnAlign": "left"}, "listBatchButtons": {"buttons": []}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 40, "lineColumnWidth": 0, "listTableColumnWidth": 160, "timestampPrecision": "Second", "listTableColumnAlign": "center"}, "listBatchButtons": {"buttons": []}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 42, "lineColumnWidth": 0, "listTableColumnWidth": 160, "timestampPrecision": "Second", "listTableColumnAlign": "center"}, "listBatchButtons": {"buttons": []}}, "kind": {"name": "kind", "label": "类型", "type": "String", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "Business", "label": "业务"}, {"value": "Parking", "label": "停靠"}, {"value": "Charging", "label": "充电"}, {"value": "IdleAvoid", "label": "推空闲车"}]}, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "listTableColumnAlign": "center"}, "listBatchButtons": {"buttons": []}}, "dispatchCost": {"name": "dispatchCost", "label": "分派成本", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 33, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "listTableColumnAlign": "right", "listTableDisabled": true}, "listBatchButtons": {"buttons": []}}, "externalId": {"name": "externalId", "label": "外部单号", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 14, "lineColumnWidth": 0, "listTableColumnWidth": 200, "timestampPrecision": "Second", "listTableColumnAlign": "left"}, "listBatchButtons": {"buttons": []}}, "keyLocations": {"name": "keyLocations", "label": "关键点位", "type": "String", "scale": "List", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 30, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second", "listTableColumnAlign": "left"}, "listBatchButtons": {"buttons": []}, "copiable": true}, "loadPoint": {"name": "loadPoint", "label": "取货点位", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 21, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "listTableColumnAlign": "left"}, "listBatchButtons": {"buttons": []}}, "unloadPoint": {"name": "unloadPoint", "label": "放货点位", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 22, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "listTableColumnAlign": "left"}, "listBatchButtons": {"buttons": []}}, "sceneId": {"name": "sceneId", "label": "场景 ID", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 27, "lineColumnWidth": 0, "listTableColumnWidth": 220, "listTableColumnAlign": "left", "listTableDisabled": true}, "listBatchButtons": {"buttons": []}, "copiable": true}, "executingTime": {"name": "executingTime", "label": "执行耗时（秒）", "type": "Float", "scale": "Single", "sqlType": "Decimal", "length": 0, "numWidth": 10, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 17, "lineColumnWidth": 0, "listTableColumnWidth": 150, "timestampPrecision": "Second", "listTableColumnAlign": "right"}, "listBatchButtons": {"buttons": []}}, "processingTime": {"name": "processingTime", "label": "处理耗时（秒）", "type": "Float", "scale": "Single", "sqlType": "Decimal", "length": 0, "numWidth": 10, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 18, "lineColumnWidth": 0, "listTableColumnWidth": 150, "timestampPrecision": "Second", "listTableColumnAlign": "right"}, "listBatchButtons": {"buttons": []}}, "sceneName": {"name": "scene<PERSON><PERSON>", "label": "场景名", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 26, "lineColumnWidth": 0, "listTableColumnWidth": 160, "listTableColumnAlign": "left"}, "listBatchButtons": {"buttons": []}, "copiable": true}, "faultReason": {"name": "faultReason", "label": "故障原因", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 1000, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 300, "listTableColumnAlign": "left", "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "oldRobots": {"name": "oldRobots", "label": "历史分配机器人", "type": "String", "scale": "List", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 200, "listTableColumnAlign": "left", "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "unloadDuration": {"name": "unloadDuration", "label": "放货时长（s）", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 37, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "loadDuration": {"name": "loadDuration", "label": "取货时长（s）", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 36, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "waitExecuteDuration": {"name": "waitExecuteDuration", "label": "执行等待时长（s）", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 38, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "faultDuration": {"name": "faultDuration", "label": "故障时长（s）", "type": "Float", "scale": "Single", "decimals": 3, "sqlType": "Decimal", "length": 0, "numWidth": 16, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 35, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "failureNum": {"name": "failureNum", "label": "故障次数", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 34, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "containerDir": {"name": "containerDir", "label": "容器方向", "type": "Float", "scale": "Single", "sqlType": "Decimal", "length": 0, "numWidth": 0, "numScale": 3, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}}, "containerTypeName": {"name": "containerTypeName", "label": "容器类型名称", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0}, "listBatchButtons": {"buttons": []}, "copiable": true}}, "indexes": [], "idGen": {"enabled": true, "fixedPrefix": "TO", "flowNoWidth": 6}, "digest": {"fields": []}, "scale": "Instances", "sort": "-createdOn", "userNotice": {"targetUserFields": []}, "actions": {"updateDisabled": true, "batchUpdateDisabled": true, "removeDisabled": true}, "listStats": {"items": [{"label": "故障", "type": "Count", "filter": {"type": "Compound", "items": [{"type": "General", "operator": "Eq", "field1": "fault", "value": true}]}, "valueColor": "#f44e3b"}, {"label": "待分派", "type": "Count", "filter": {"type": "Compound", "items": [{"type": "General", "operator": "Eq", "field1": "status", "value": "ToBeAllocated"}]}, "valueColor": "#0062b1"}, {"label": "未封口", "type": "Count", "filter": {"type": "Compound", "items": [{"type": "General", "operator": "Ne", "field1": "stepFixed", "value": true}]}, "valueColor": "#68bc00"}, {"label": "今日创建", "type": "Count", "filter": {"type": "Compound", "items": [{"type": "General", "operator": "ThisDay", "field1": "createdOn"}]}, "valueColor": "#68bc00"}, {"label": "今日完成", "type": "Count", "filter": {"type": "Compound", "items": [{"type": "General", "operator": "ThisDay", "field1": "doneOn"}]}, "valueColor": "#009ce0"}]}, "listCard": {"lines": [[{"type": "Simple", "fieldName": "id", "formatMapping": []}, {"type": "Simple", "fieldName": "status", "tag": true, "alignRight": true, "hideIfBlankFalse": true, "formatMapping": []}], [{"type": "Simple", "fieldName": "actualRobotName", "prefix": "执行机器人", "prefixPaddingRight": 8, "formatMapping": []}], [{"type": "Simple", "fieldName": "kind", "prefix": "类型", "prefixPaddingRight": 8, "hideIfBlankFalse": true, "formatMapping": []}], [{"type": "Simple", "fieldName": "loaded", "tag": true, "tagStyle": "for-going", "replaceText": "已取货", "hideIfBlankFalse": true, "formatMapping": []}, {"type": "Simple", "fieldName": "unloaded", "tag": true, "tagStyle": "for-success", "replaceText": "已放货", "hideIfBlankFalse": true, "formatMapping": []}, {"type": "Simple", "fieldName": "fault", "tag": true, "tagStyle": "for-error", "replaceText": "故障", "hideIfBlankFalse": true, "formatMapping": []}], [{"type": "Simple", "fieldName": "createdOn", "prefix": "创建", "prefixPaddingRight": 8, "formatMapping": []}]]}, "orderConfig": {"states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"extEnabled": true, "buttons": [{"type": "Special", "label": "新增", "kind": "primary", "builtinId": "Create", "extId": "CreateTransportOrder", "enabledCondition": {}, "icon": "fas,plus"}, {"type": "Builtin", "label": "批量编辑", "builtinId": "BatchEdit"}, {"type": "Builtin", "label": "删除", "kind": "warning", "builtinId": "RemoveSelected"}, {"type": "Builtin", "label": "导出", "builtinId": "Export"}, {"type": "Ext", "label": "取消", "kind": "warning", "confirmMsg": "确定要取消所选运单么？", "uiSource": "const {\n    getHttpClient,\n    toast,\n    cqEq,\n} = __jf();\n\n// 校验数据源。前端先筛一波，减少后端负载。\nconst checkedOrderIds = []\nctx.evList.forEach((order) => {\n    // 需要非终态的运单\n    if ([\"Cancelled\", \"Done\"].indexOf(order[\"status\"]) == -1) checkedOrderIds.push(order[\"id\"])\n})\nif (checkedOrderIds.length == 0) {\n    toast.toastError(\"请勾选至少一个非终态的运单后，再点击 ‘取消’ 按钮！\")\n    return null\n}\n\n// 将所选的新通用运单标记为取消状态。\nconst orderQty = checkedOrderIds.length\nif (orderQty > 50)\n    console.log(`批量撤销大量运单，qty=${orderQty}，不打印相关日志。`)\n\nconst r = await getHttpClient().post(\"fleet/orders/cancel-batch\", { orderIds: checkedOrderIds })\n\n// 此处不用校验 HTTP 状态码，getHttpClient() 已经拦截异常的状态码了。\n\n// 取消一条运单的响应文本的内容并不多，可以打印以便于排查问题，但是批量撤销大量运单时，就不打印日志了。\nif (orderQty <= 50) console.log(r)\n\ntoast.toastSuccess(\"成功\")\nreturn null", "preSep": true, "enabledCondition": {}, "icon": "fas,circle-stop"}, {"type": "Ext", "label": "封口", "kind": "warning", "confirmMsg": "确定要将所选运单封口么？", "uiSource": "const {\n    getHttpClient,\n    toast,\n    cqEq,\n} = __jf();\n\n// 校验数据源。前端先筛一波，减少后端负载。\nconst checkedOrderIds = []\nctx.evList.forEach((order) => {\n    // 需要非终态的未封口的运单\n    if ([\"Cancelled\", \"Done\"].indexOf(order[\"status\"]) == -1 && !order[\"stepFixed\"]) checkedOrderIds.push(order[\"id\"])\n})\nif (checkedOrderIds.length == 0) {\n    toast.toastError(\"请勾选至少一个非终态的未封口的运单后，再点击 ‘封口’ 按钮！\")\n    return null\n}\n\n// 将所选的新通用运单标记为 封口 。\nconst orderQty = checkedOrderIds.length\nif (orderQty > 50)\n    console.log(`批量封口大量运单，qty=${orderQty}，不打印相关日志。`)\n\nconst r = await getHttpClient().post(\"fleet/orders/complete-order-batch\", { orderIds: checkedOrderIds })\n\n// 此处不用校验 HTTP 状态码，getHttpClient() 已经拦截异常的状态码了。\n\n// 取消一条运单的响应文本的内容并不多，可以打印以便于排查问题，但是批量撤销大量运单时，就不打印日志了。\nif (orderQty <= 50) console.log(r)\n\ntoast.toastSuccess(\"成功\")\nreturn null", "preSep": true, "enabledCondition": {}, "icon": "fas,check-double"}, {"type": "Ext", "label": "故障重试", "kind": "warning", "confirmMsg": "确定要重试所需的故障的运单吗？", "uiSource": "const {\n    getHttpClient,\n    toast,\n    cqEq,\n} = __jf();\n\n// 校验数据源。前端先筛一波，减少后端负载。\nconst checkedOrderIds = []\nctx.evList.forEach((order) => {\n    // 需要非终态的故障的运单\n    if (order[\"fault\"] == true && [\"Cancelled\", \"Done\"].indexOf(order[\"status\"]) == -1) checkedOrderIds.push(order[\"id\"])\n})\nif (checkedOrderIds.length == 0) {\n    toast.toastError(\"请勾选至少一个非终态的故障状态的运单后，再点击 ‘故障重试’ 按钮！\")\n    return null\n}\n\n// 重试所选的故障状态的运单。\nconst orderQty = checkedOrderIds.length\nif (orderQty > 50)\n    console.log(`批量重试大量故障的运单，qty=${orderQty}，不打印相关日志。`)\n\nconst r = await getHttpClient().post(\"fleet/orders/retry-failed-orders\", { orderIds: checkedOrderIds })\n\n// 此处不用校验 HTTP 状态码，getHttpClient() 已经拦截异常的状态码了。\n\n// 响应文本的内容并不多，可以打印以便于排查问题，但是批量重试大量故障的运单时，就不打印日志了。\nif (orderQty <= 50) console.log(r)\n\ntoast.toastSuccess(\"成功\")\nreturn null", "preSep": true, "enabledCondition": {}, "icon": "fas,hammer"}, {"type": "Ext", "label": "修改优先级", "kind": "warning", "uiSource": "const {\n    getHttpClient,\n    toast,\n    cqEq,\n    popupStore\n} = __jf();\n\n// 校验数据源。前端先筛一波，减少后端负载。\nconst checkedOrderIds = []\nctx.evList.forEach((order) => {\n    // 需要非终态的故障的运单\n    // if (order[\"fault\"] == true && [\"Cancelled\", \"Done\"].indexOf(order[\"status\"]) == -1)\n    checkedOrderIds.push(order[\"id\"])\n})\nif (checkedOrderIds.length == 0) {\n    toast.toastError(\"请选择目标运单\")\n    return null\n}\n\n// 重试所选的故障状态的运单。\nconst orderQty = checkedOrderIds.length\nif (orderQty > 50)\n    console.log(`批量重试大量故障的运单，qty=${orderQty}，不打印相关日志。`)\n\nlet value = await popupStore.input('优先级', '输入优先级')\nconsole.log(value)\nif (value === null) return\nvalue = parseInt(value)\nif(Number.isNaN(value)) {\n    return\n}\n\nconst r = await getHttpClient().post(\"fleet/orders/update-priority-batch\", {priority: value,  orderIds: checkedOrderIds })\n\n// 此处不用校验 HTTP 状态码，getHttpClient() 已经拦截异常的状态码了。\n\n// 响应文本的内容并不多，可以打印以便于排查问题，但是批量重试大量故障的运单时，就不打印日志了。\nif (orderQty <= 50) console.log(r)\n\ntoast.toastSuccess(\"成功\")\nreturn null", "preSep": true, "enabledCondition": {}, "icon": "fas,arrow-up-9-1"}, {"type": "Special", "label": "导出快照", "extId": "ExportTransportOrderSnapshoot", "preSep": true, "enabledCondition": {}, "icon": "fas,diagram-next"}]}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}, "dateClean": {"type": "ByDay"}, "kinds": {"kinds": {}}, "states": {"states": {}}, "menuColor": "#1A7BF2", "menuIcon": "code-compare", "quickInput": {"disabled": true, "items": []}, "modifyProhibition": {"type": "Query"}, "removeProhibition": {"type": "Query"}, "detailsPageName": "TransportOrderView", "viewOpenMode": "Dialog"}