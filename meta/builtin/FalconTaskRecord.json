{"name": "FalconTaskRecord", "label": "猎鹰任务记录", "group": "Falcon", "builtin": true, "type": "Entity", "fields": {"defId": {"name": "defId", "label": "定义 ID", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 2, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "defLabel": {"name": "def<PERSON><PERSON><PERSON>", "label": "任务模版", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "defVersion": {"name": "defVersion", "label": "模版版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "rootBlockStateId": {"name": "rootBlockStateId", "label": "根块ID", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "status": {"name": "status", "label": "状态", "type": "Int", "scale": "Single", "inlineOptionBill": {"enabled": true, "items": [{"value": "100", "label": "已创建", "color": "#009ce0"}, {"value": "120", "label": "已开始", "color": "#a4dd00"}, {"value": "140", "label": "故障", "color": "#e27300"}, {"value": "160", "label": "已完成", "color": "#68bc00"}, {"value": "180", "label": "已取消", "color": "#fa28ff"}, {"value": "190", "label": "已放弃", "color": "#f44e3b"}]}, "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second", "asTag": true}, "listBatchButtons": {"buttons": []}}, "actualRobots": {"name": "actualRobots", "label": "执行机器人", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "paused": {"name": "paused", "label": "已暂停", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "endedReason": {"name": "endedReason", "label": "错误原因", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 1000, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 14, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "inputParams": {"name": "inputParams", "label": "输入参数", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 10000, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 16, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "variables": {"name": "variables", "label": "任务变量", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 10000, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 17, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "ro": {"name": "ro", "label": "RO", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 180, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 18, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 20, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 19, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "modifiedOn": {"name": "modifiedOn", "label": "修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 21, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "topTaskId": {"name": "topTaskId", "label": "顶层任务 ID", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "subTask": {"name": "subTask", "label": "子任务", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "endedOn": {"name": "endedOn", "label": "结束时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}, "failureNum": {"name": "failureNum", "label": "故障次数", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 15, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}, "listBatchButtons": {"buttons": []}}}, "indexes": [{"name": "status_createdOn", "fields": [{"name": "status", "desc": true}, {"name": "createdOn", "desc": true}]}, {"name": "topTaskId_status", "fields": [{"name": "topTaskId", "desc": true}, {"name": "status", "desc": true}]}], "idGen": {"enabled": true, "fixedPrefix": "FT", "flowNoWidth": 6}, "digest": {"fields": []}, "scale": "Instances", "sort": "-createdOn", "userNotice": {"targetUserFields": []}, "actions": {"createDisabled": true, "updateDisabled": true, "batchUpdateDisabled": true}, "detailsPageName": "FalconTaskView", "listStats": {"items": [{"label": "故障", "type": "Count", "filter": {"type": "Compound", "items": [{"type": "General", "operator": "Eq", "field1": "status", "value": "140"}]}, "valueColor": "#d33115"}, {"label": "已暂停", "type": "Count", "filter": {"type": "Compound", "items": [{"type": "General", "operator": "Eq", "field1": "paused", "value": true}]}, "valueColor": "#fcc400"}, {"label": "今日新增", "type": "Count", "filter": {"type": "Compound", "items": [{"type": "General", "operator": "ThisDay", "field1": "createdOn"}]}, "valueColor": "#b0bc00"}, {"label": "今日失败取消", "type": "Count", "filter": {"type": "Compound", "items": [{"type": "General", "operator": "ThisDay", "field1": "createdOn"}, {"type": "General", "operator": "In", "field1": "status", "value": ["180", "190"]}]}, "valueColor": "#d33115"}, {"label": "本周新增", "type": "Count", "filter": {"type": "Compound", "items": [{"type": "General", "operator": "ThisWeek", "field1": "createdOn"}]}, "valueColor": "#b0bc00"}, {"label": "本周失败取消", "type": "Count", "filter": {"type": "Compound", "items": [{"type": "General", "operator": "ThisWeek", "field1": "createdOn"}, {"type": "General", "operator": "In", "field1": "status", "value": ["180", "190"]}]}, "valueColor": "#d33115"}]}, "listCard": {"lines": [[{"type": "Simple", "fieldName": "id", "formatMapping": []}, {"type": "Simple", "fieldName": "status", "tag": true, "alignRight": true, "formatMapping": []}], [{"type": "Simple", "fieldName": "def<PERSON><PERSON><PERSON>", "prefix": "模板", "prefixPaddingRight": 8, "marginRight": 8, "formatMapping": []}, {"type": "Simple", "fieldName": "defVersion", "prefix": "(", "suffix": ")", "formatMapping": []}], [{"type": "Simple", "fieldName": "paused", "tag": true, "tagStyle": "for-error", "replaceText": "已暂停", "hideIfBlankFalse": true, "formatMapping": []}], [{"type": "Simple", "fieldName": "createdOn", "prefix": "创建", "prefixPaddingRight": 8, "hideIfBlankFalse": true, "formatMapping": []}], [{"type": "Simple", "fieldName": "endedOn", "prefix": "结束", "prefixPaddingRight": 8, "hideIfBlankFalse": true, "formatMapping": []}]]}, "orderConfig": {"enabled": true, "states": [], "pushOrderStates": [], "pushOrders": [], "upOrderConfig": {"items": []}, "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"extEnabled": true, "buttons": [{"type": "Builtin", "label": "删除", "builtinId": "Remove"}, {"type": "Builtin", "label": "导出", "builtinId": "Export"}, {"type": "Special", "label": "暂停执行", "kind": "warning", "extId": "FalconPause", "enabledCondition": {}, "icon": "fas,circle-pause"}, {"type": "Special", "label": "继续执行", "extId": "FalconResume", "enabledCondition": {}, "icon": "fas,circle-play"}, {"type": "Special", "label": "故障重试", "kind": "warning", "extId": "FalconRecoverError", "enabledCondition": {}, "icon": "fas,hammer"}, {"type": "Special", "label": "取消执行", "kind": "warning", "extId": "FalconCancel", "enabledCondition": {}, "icon": "fas,circle-stop"}, {"type": "Builtin", "label": "删除", "kind": "warning", "builtinId": "RemoveSelected"}, {"type": "Special", "label": "模版", "extId": "FalconToDef", "preSep": true}, {"type": "Special", "label": "全局控制", "extId": "FalconToGlobal"}]}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}, "codeParse": {"rules": []}, "dateClean": {"type": "ByDay"}, "kinds": {"kinds": {}}, "states": {"states": {}}, "menuColor": "#1A7BF2", "menuIcon": "file-pen", "quickInput": {"items": []}}