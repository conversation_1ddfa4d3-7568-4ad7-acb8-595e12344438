const fs = require('fs')
const path = require("path")

// -------------------------------------------------------------------
// 注意：
// - 同时有 new、removed、changed 的配置，需要先 remove，后 new，再 change。new 时需要删除原有的 key，防止重复执行脚本导致的重复添加。
// -------------------------------------------------------------------

const prj = process.argv[2]
console.log("prj: " + prj)

const debug = false

const prjPath = path.join(prj, "config", "entity-change-v1")

if (!fs.existsSync(prjPath)) return

const builtinEmMap = {}

// 读取内建实体
let builtinPath = path.join("meta", "builtin")
if (debug) builtinPath = path.join("builtin")
const builtinDir = fs.readdirSync(builtinPath)

for (const fn of builtinDir) {
  if (!fn.endsWith(".json")) continue
  const file = path.join(builtinPath, fn)
  const emStr = fs.readFileSync(file, {encoding: "utf-8"})
  const em = JSON.parse(emStr)
  builtinEmMap[em.name] = em
}

// 应用变更
for (const fn of fs.readdirSync(prjPath)) {
  if (!fn.endsWith(".diff.json")) continue
  const file = path.join(prjPath, fn)
  const diffStr = fs.readFileSync(file, {encoding: "utf-8"}) // EntityMetaDiff
  const diff = JSON.parse(diffStr)

  apply(diff)
}

// 更新 meta/builtin/xx.json
for (const fn of builtinDir) {
  fs.unlinkSync(path.join(builtinPath, fn))
}
for (const entityName in builtinEmMap) {
  const em = builtinEmMap[entityName]
  cleanObj(em)
  persist(em)
}

// 更新 resources/entities.json
let resourcesPath = path.join("trick-m4", "src", "main", "resources", "entities.json")
if (debug) resourcesPath = path.join("..", "trick-m4", "src", "main", "resources", "entities.json") // debug 模式
persistEntities();

// ----------------------------------------------------------
// 下面是一些封装的函数
// ----------------------------------------------------------

// 应用 - 总入口
// diff - EntityMetaDiff
function apply(diff) {
  const entityName = diff.entityName

  if (diff.removedEm)
    removeFromMap(builtinEmMap, diff.removedEm)
  if (diff.addedEm && diff.addedEm.builtin)
    addToMap(builtinEmMap, entityName, diff.addedEm)
  if (diff.changed)
    applyEmChange(entityName, diff.changed)
}

// 应用变更实体
// ec - EntityMetaChange
function applyEmChange(entityName, ec) {
  const em = builtinEmMap[entityName]
  if (!em) return

  // simple property
  applySimplePropertyChange(em, ec.simplePropertyChanges)

  // fields
  applyFields(em, ec)

  // index
  applyIndex(em, ec);

  // page button
  const pagesButtons = em.pagesButtons
  for (const btn of ec.pageButtonsChanges) {
    pagesButtons[btn.pageKey] = btn.newValue
  }
}

// 应用基础属性变更
function applySimplePropertyChange(o, changes) {
  for (const c of changes) {
    o[c.name] = c.newValue
  }
}

// 应用字段变更
function applyFields(em, ec) {
  const fields = em.fields
  for (const fn of ec.removedFields)
    removeFromMap(fields, fn)
  for (const fm of ec.newFields)
    addToMap(fields, fm.name, fm)
  for (const fc of ec.changedFields) {
    // simple property
    applySimplePropertyChange(fields[fc.fieldName], fc.simplePropertyChanges)
    // view
    applySimplePropertyChange(fields[fc.fieldName].view, fc.viewChanges)
  }
}

// 应用索引变更
function applyIndex(em, ec) {
  const indexes = em.indexes
  for (const idxRemoved of ec.removedIndexes)
    removeFromList(indexes, (it) => it.name === idxRemoved)
  for (const idx of ec.newIndexes)
    addToList(indexes, (it) => it.name === idx.name, idx)
  for (const idxChange of ec.changedIndexes) {
    em.indexes = em.indexes.map(it => {
      if (it.name === idxChange.name)
        return idxChange.newValue
      else
        return it
    })
    // console.log(em.indexes)
  }
}

function removeFromMap(m, key) {
  delete m[key]
}

function addToMap(m, key, nv) {
  removeFromMap(m, key)  // 先删除，防止重复加
  m[key] = nv
}

function removeFromList(arr, condition) {
  const i = arr.findIndex(it => condition(it))
  if (i !== -1)
    arr.splice(i, 1)
}

function addToList(arr, condition, nv) {
  removeFromList(arr, condition)  // 先删除，防止重复加
  arr.push(nv)
}

// 清理无效行
function cleanObj(em) {
  if (Array.isArray(em)) {
    for (const item of em) cleanObj(item)
  } else if (typeof em === "object") {
    for (const key of Object.keys(em)) {
      const value = em[key]
      if (value === null || value === false || value === "") delete em[key]
      cleanObj(em[key])
    }
  }
}

// 持久化 meta/builtin/xx.json
function persist(em) {
  const builtinEntityPath = path.join(builtinPath, em.name + ".json")
  fs.writeFileSync(builtinEntityPath, JSON.stringify(em, null, 2), {encoding: "utf-8"})
}

// 持久化 resources/entities.json
function persistEntities() {
  const builtinEmList = Object.values(builtinEmMap)
  builtinEmList.sort((a, b) => a.name.localeCompare(b.name))
  const emMapStr = JSON.stringify(builtinEmList, null, 2)
  fs.writeFileSync(resourcesPath, emMapStr, {encoding: "utf-8"})
}
