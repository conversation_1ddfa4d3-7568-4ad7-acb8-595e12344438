const fs = require('fs')
const path = require("path")

const prj = process.argv[2]
console.log("prj: " + prj)

const dir = path.join(prj, "config", "entity")

if (!fs.existsSync(dir)) return

const builtinEmMap = {}

// 读 builtin 文件夹的

const builtinFilename = fs.readdirSync("meta/builtin")

for (const fn of builtinFilename) {
    if (!fn.endsWith(".json")) continue

    const file = path.join("meta", "builtin", fn)
    const jsonStr = fs.readFileSync(file, {encoding: "utf-8"})
    const em = JSON.parse(jsonStr)
    if (!em.builtin) continue

    cleanObj(em)

    builtinEmMap[em.name] = em
}

// 读项目包下的

const metaFilenames = fs.readdirSync(dir)

for (const fn of metaFilenames) {
    if (!fn.endsWith(".json")) continue

    const file = path.join(dir, fn)
    const jsonStr = fs.readFileSync(file, {encoding: "utf-8"})
    const em = JSON.parse(jsonStr)
    if (!em.builtin) continue

    cleanObj(em)

    builtinEmMap[em.name] = em

    const emStr = JSON.stringify(em, null, 2)
    const file2 = path.join("meta", "builtin", em.name + ".json")
    fs.writeFileSync(file2, emStr, {encoding: "utf-8"})
}

const file = path.join("trick-m4", "src", "main", "resources", "entities.json")
const builtinEmList = Object.values(builtinEmMap)
builtinEmList.sort((a, b) => a.name.localeCompare(b.name))

const emMapStr = JSON.stringify(builtinEmList, null, 2)
fs.writeFileSync(file, emMapStr, {encoding: "utf-8"})


function cleanObj(o) {
    if (Array.isArray(o)) {
        for (const item of o) cleanObj(item)
    } else if (typeof o === "object") {
        for (const key of Object.keys(o)) {
            const value = o[key]
            if (value === null || value === false || value === "") delete o[key]
            cleanObj(o[key])
        }
    }
}

// 更深度的清理，能去掉 10%
// function cleanValue(o) {
//   if (Array.isArray(o)) {
//       if (!o.length) return null
//       const na = o.map(item => cleanValue(item))
//       const na2 = na.filter(item => !!item)
//       return na2.length ? na : null // 如果元素全是空，当数组为空；否则有一个非空元素，就得原顺序保留
//   } else if (typeof o === "object") {
//       const no = {}
//       for (const key of Object.keys(o)) {
//           const value = o[key]
//           if (value === null || value === false || value === "") continue
//           const nv = cleanValue(value)
//           if (nv !== null) no[key] = nv
//       }
//       return Object.keys(no).length ? no : null
//   } else if (o === null || o === false || o === "") {
//       return null
//   } else {
//       return o
//   }
// }