const fs = require('fs')
const path = require("path")

const metaStr = fs.readFileSync("meta.json", {encoding: "utf-8"})
const emMap = JSON.parse(metaStr)

const builtinEmMap = {}

for (const name of Object.keys(emMap)) {
    const em = emMap[name]
    if (!em.builtin) continue
    cleanObj(em)

    builtinEmMap[em.name] = em

    const emStr = JSON.stringify(em, null, 2)
    const file = path.join("meta", "builtin", em.name + ".json")
    fs.writeFileSync(file, emStr, {encoding: "utf-8"})
}

const builtinEmList = Object.values(builtinEmMap)
builtinEmList.sort((a, b) => a.name.localeCompare(b.name))
const file = path.join("trick-m4", "src", "main", "resources", "entities.json")
const emMapStr = JSON.stringify(builtinEmList, null, 2)
fs.writeFileSync(file, emMapStr, {encoding: "utf-8"})


function cleanObj(o) {
    if (Array.isArray(o)) {
        for (const item of o) cleanObj(item)
    } else if (typeof o === "object") {
        for (const key of Object.keys(o)) {
            const value = o[key]
            if (value === null || value === false || value === "") delete o[key]
            cleanObj(o[key])
        }
    }
}

// 更深度的清理，能去掉 10%
// function cleanValue(o) {
//   if (Array.isArray(o)) {
//       if (!o.length) return null
//       const na = o.map(item => cleanValue(item))
//       const na2 = na.filter(item => !!item)
//       return na2.length ? na : null // 如果元素全是空，当数组为空；否则有一个非空元素，就得原顺序保留
//   } else if (typeof o === "object") {
//       const no = {}
//       for (const key of Object.keys(o)) {
//           const value = o[key]
//           if (value === null || value === false || value === "") continue
//           const nv = cleanValue(value)
//           if (nv !== null) no[key] = nv
//       }
//       return Object.keys(no).length ? no : null
//   } else if (o === null || o === false || o === "") {
//       return null
//   } else {
//       return o
//   }
// }