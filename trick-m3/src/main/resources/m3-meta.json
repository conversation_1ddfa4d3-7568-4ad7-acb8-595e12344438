{"M3Project": {"name": "M3Project", "label": "项目", "fields": {"id": {"name": "id", "label": "ID", "type": "String", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 100}, "name": {"name": "name", "label": "项目名称", "type": "String", "length": 100}, "disabled": {"name": "disabled", "label": "停用", "type": "Boolean"}, "remark": {"name": "remark", "label": "备注", "type": "String", "length": 250}, "gwAuthId": {"name": "gwAuthId", "label": "身份 ID（多协议网关）", "type": "String"}, "gwAuthSecret": {"name": "gwAuthSecret", "label": "身份秘钥（多协议网关）", "type": "String"}, "logo": {"name": "logo", "label": "LOGO", "type": "Image"}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "refEntity": "HumanUser"}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "sqlType": "DateTime"}}}, "M3SeerRobot": {"name": "M3SeerRobot", "label": "仙工机器人", "fields": {"id": {"name": "id", "label": "ID", "type": "String", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 100}, "projectId": {"name": "projectId", "label": "所属项目", "type": "Reference", "refEntity": "M3Project"}, "name": {"name": "name", "label": "机器人名", "type": "String", "length": 100}, "connectionType": {"name": "connectionType", "label": "连接类型", "type": "String", "length": 100}, "host": {"name": "host", "label": "地址（IP）", "type": "String", "length": 50}, "disabled": {"name": "disabled", "label": "停用", "type": "Boolean"}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "refEntity": "HumanUser"}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "sqlType": "DateTime"}, "remark": {"name": "remark", "label": "备注", "type": "String", "length": 250}, "imageType": {"name": "imageType", "label": "图片类型", "type": "String", "length": 250}, "builtinImage": {"name": "builtinImage", "label": "内建图片", "type": "String", "length": 250}, "uploadedImage": {"name": "uploadedImage", "label": "上传图片", "type": "Image", "length": 250}}}, "M3ModbusDevice": {"name": "M3ModbusDevice", "label": "Modbus 设备", "fields": {"id": {"name": "id", "label": "ID", "type": "String", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 100}, "projectId": {"name": "projectId", "label": "所属项目", "type": "Reference", "refEntity": "M3Project"}, "disabled": {"name": "disabled", "label": "停用", "type": "Boolean"}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "refEntity": "HumanUser"}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "sqlType": "DateTime"}, "name": {"name": "name", "label": "设备名", "type": "String", "length": 100}, "ip": {"name": "ip", "label": "IP", "type": "String", "length": 100}, "port": {"name": "port", "label": "端口", "type": "Int"}, "lightVariable": {"name": "lightVariable", "label": "三色灯地址", "type": "Int"}, "variables": {"name": "variables", "label": "变量", "type": "String", "length": 1000}, "image": {"name": "image", "label": "图片", "type": "Image", "length": 250}}}, "M3MaintenanceRule": {"name": "M3MaintenanceRule", "label": "维保规则", "fields": {"id": {"name": "id", "label": "ID", "type": "String", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 100}, "projectId": {"name": "projectId", "label": "所属项目", "type": "Reference", "refEntity": "M3Project"}, "disabled": {"name": "disabled", "label": "停用", "type": "Boolean"}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "refEntity": "HumanUser"}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "sqlType": "DateTime"}, "title": {"name": "title", "label": "规则名", "type": "String", "length": 100}, "targetType": {"name": "targetType", "label": "对象类型", "type": "String", "length": 100}, "subType": {"name": "subType", "label": "子类型", "type": "String", "length": 100}, "targetId": {"name": "targetId", "label": "对象 ID", "type": "String", "length": 100}, "content": {"name": "content", "label": "规则详情", "type": "String", "length": 1000}, "lastValue": {"name": "lastValue", "label": "上次触发值", "type": "Float"}}}, "M3MaintenanceTask": {"name": "M3MaintenanceTask", "label": "维保任务", "fields": {"id": {"name": "id", "label": "ID", "type": "String", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 100}, "projectId": {"name": "projectId", "label": "所属项目", "type": "Reference", "refEntity": "M3Project"}, "disabled": {"name": "disabled", "label": "停用", "type": "Boolean"}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "refEntity": "HumanUser"}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "sqlType": "DateTime"}, "rule": {"name": "rule", "label": "规则", "type": "Reference"}, "title": {"name": "title", "label": "规则名", "type": "String", "length": 100}, "desc": {"name": "desc", "label": "描述", "type": "String", "length": 200}, "targetType": {"name": "targetType", "label": "对象类型", "type": "String", "length": 100}, "subType": {"name": "subType", "label": "子类型", "type": "String", "length": 100}, "targetId": {"name": "targetId", "label": "对象 ID", "type": "String", "length": 100}, "state": {"name": "state", "label": "状态", "type": "String", "length": 100}, "expectedDate": {"name": "expectedDate", "label": "建议维保时间", "type": "DateTime"}, "actualDate": {"name": "actualDate", "label": "处理时间", "type": "DateTime"}, "remark": {"name": "remark", "label": "处理备注", "type": "String", "length": 100}, "progress": {"name": "progress", "label": "进度", "type": "Int"}}}}