<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="INFO">
    <Properties>
        <Property name="LOG_PATTERN">
            %d{yyyy-MM-dd HH:mm:ss.SSS} %highlight{${LOG_LEVEL_PATTERN:-%5p}}{FATAL=red, ERROR=red, WARN=yellow,
            INFO=green, DEBUG=green, TRACE=green} --- [%15.15t] %style{%-40.40c{1.}}{cyan} : %m%n%ex
        </Property>
    </Properties>

    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${LOG_PATTERN}"/>
        </Console>

        <RollingFile name="RollingFile" fileName="logs/m3.log" filePattern="logs/m3-%d{yyyy-MM-dd}-%i.log">
            <PatternLayout>
                <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n</Pattern>
                <charset>UTF-8</charset>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="100">
                <Delete basePath="" maxDepth="2">
                    <IfFileName glob="*.log"/>
                    <IfLastModified age="7d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
    </Appenders>

    <Loggers>
        <Root level="info">
            <!--            <AppenderRef ref="Console"/>-->
            <AppenderRef ref="RollingFile"/>
        </Root>
        <Logger name="com.seer" level="debug" additivity="false">
            <!--            <AppenderRef ref="Console"/>-->
            <AppenderRef ref="RollingFile"/>
        </Logger>
    </Loggers>
</Configuration>