<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.seer.trick</groupId>
  <artifactId>trick-server</artifactId>
  <packaging>pom</packaging>
  <version>${revision}</version>

  <properties>
    <revision>5.5.2</revision>
    <trick.version>${revision}</trick.version>

    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

    <kotlin.version>1.8.21</kotlin.version>

    <maven.compiler.source>17</maven.compiler.source>
    <maven.compiler.target>17</maven.compiler.target>

    <okhttp.version>4.11.0</okhttp.version>
    <graalvm.version>23.1.1</graalvm.version>
    <dameng.version>8.1.3.62</dameng.version>
  </properties>

  <modules>
    <module>trick-base</module>
    <module>trick-falcon</module>
    <module>trick-wcs</module>
    <module>trick-robot</module>
    <module>quick-fleet</module>
    <module>quick-store</module>
    <module>trick-m4</module>
    <module>trick-cloud</module>
  </modules>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-stdlib-jdk8</artifactId>
        <version>${kotlin.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>3.12.0</version>
      </dependency>

      <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>2.16.1</version>
      </dependency>

      <dependency>
        <groupId>commons-codec</groupId>
        <artifactId>commons-codec</artifactId>
        <version>1.15</version>
      </dependency>

      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-compress</artifactId>
        <version>1.26.1</version>
      </dependency>

      <dependency>
        <groupId>io.javalin</groupId>
        <artifactId>javalin</artifactId>
        <version>5.6.3</version>
      </dependency>
      <dependency>
        <groupId>io.javalin.community.ssl</groupId>
        <artifactId>ssl-plugin</artifactId>
        <version>5.6.3</version>
      </dependency>

      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp</artifactId>
        <version>${okhttp.version}</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>logging-interceptor</artifactId>
        <version>${okhttp.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-slf4j2-impl</artifactId>
        <version>2.20.0</version>
      </dependency>

      <dependency>
        <groupId>com.digitalpetri.modbus</groupId>
        <artifactId>modbus-master-tcp</artifactId>
        <version>1.2.0</version>
      </dependency>
      <dependency>
        <groupId>com.digitalpetri.modbus</groupId>
        <artifactId>modbus-slave-tcp</artifactId>
        <version>1.2.0</version>
      </dependency>

      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-yaml</artifactId>
        <version>2.14.2</version>
      </dependency>

      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-all</artifactId>
        <version>4.1.92.Final</version>
      </dependency>

      <dependency>
        <groupId>org.mvel</groupId>
        <artifactId>mvel2</artifactId>
        <version>2.4.7.Final</version>
      </dependency>

      <dependency>
        <groupId>com.seer.trick</groupId>
        <artifactId>trick-base</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.seer.trick</groupId>
        <artifactId>trick-falcon</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.seer.trick</groupId>
        <artifactId>trick-wcs</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.seer.trick</groupId>
        <artifactId>trick-robot</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.seer.trick</groupId>
        <artifactId>quick-fleet</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.seer.trick</groupId>
        <artifactId>trick-bz</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.seer.trick</groupId>
        <artifactId>quick-store</artifactId>
        <version>${revision}</version>
      </dependency>
      <dependency>
        <groupId>com.seer.trick</groupId>
        <artifactId>quick-store-d</artifactId>
        <version>${revision}</version>
      </dependency>

      <dependency>
        <groupId>com.dameng</groupId>
        <artifactId>DmJdbcDriver18</artifactId>
        <version>${dameng.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

</project>
