
----

任务应该可以建模为由两点间移动构成（搬运或空跑都是）

----

移动机器人最基本的原语：做一个动作。包括：移动到一个点、旋转、顶升升降、叉齿升降、滚筒动、播放音乐、触发 DO、读容器编码。

任务的粒度：一次取放是一个任务？取货本身是一个任务？把一批库存（多个箱子）出库是一个任务？

任务调度的本质：面对一些要做的事，让哪个机器人做什么。例如，当料箱机器人取完一次货后，接下来它可以取放货，或再取一次货。

取货放货差异巨大：取货涉及选择哪个机器人取。但卸货，只要卸哪个货确定，哪个机器人执行就确定了。

把一个货物的取放当做一个任务。有什么例外。有取了不放、放了不取的情况吗？取了就一定会放？

有非取放货任务。如单纯让机器人移动到某个地方。然后控制机械臂，或让传感器读码。

就地播放音乐也是一个机器人动作。

一个任务就是让某个特定机器人做一系列动作，移动到一个点，取货、移动到另一个点，放货。每个动作前会有前置活动。如放货前要先等滚筒线就绪。即，任务是动作序列。

一个机器人能接多个任务（动作序列），这些任务可能需要串行，也可能可以交替执行。比如料箱机器人收到两个取放货任务。可以安排先做两次取货再放货。串行的任务换个视角看，也可以看做机器人一次只能做一个任务。

机器人任务的第一个动作一般是取货。哪些情况下取货前还有前置动作。比如先移动到一个点。

在一个任务下达时还不知道任务的完整动作列表。比如取货时还不知道卸货地点。

特别的，在任务开始时不知道取什么货、到哪里取。机器人纯先移动。但至少货物类型要知道，否则多车型时无法选车。

一个任务内至多有一组取货放动作。但确实存在取放、取放两次是一个原子操作的。比如要求必须同一台车（一次能拿两个货），一次取两个货卸两个货。或者一台车取放货后必须同一台车把另一个货拿走（上料完下料）。

到达卸货点，发现无法用，再更换卸货地点。

选定车（开始动作序列）、释放车（结束动作序列）要不要单独存在。

或者说，有没有可能需要先纯选车。先选车再决定第一步是什么。例如，搬栈板。有堆高和搬运车。如果选到堆高车，则取二层以上的，如果是搬运车，取第一层的。

料箱机器人的自动拼单和人工拼单。人工拼单，人工决定这一组箱子由一个车运。

序列、运单、步，三级结构？

序列表示这个车在完成序列内已有动作后，阻止这个车处理其他序列。料箱车可以同时执行多个动作序列。但是，如何实现，料箱车先到库位放 3 个箱子，然后必须先把库口的空箱子搬回来。

# 典型疑难场景

## 1 非搬运性任务

如去一个库位读取货物编号。



## 2 根据货物类型选车

叫料，根据货物从料箱库还是栈板库出，选料箱车还是堆高车。



## 3 根据货物位置选车

叫料，根据货物从一层货架还是其他层选车，一层货架优先选搬运车，其他位置只能选堆高车。



## 4 经过中间点运输

去指定起点取货，终点未知。取货后先去中间点，扫码，与 ERP 通讯，下发终点。

或者去终点放货前，需要先与库口通讯，如 Modbus 允许放行后才能放货。



## 5 关键设备与暂存区

如打包机、烘干机。如果设备空闲，直接送设备上，否则先送旁边暂存区。等设备空闲再把暂存区的送设备。



## 关键资源分支

如果电梯空闲，送二楼，否则先送一楼缓冲区。



## 7 任务序列

任务序列指的是，一台机器人，在完成当前已知所有任务后，不能接其他序列的新任务。而是要等这个序列任务追加直到序列结束。

例如，给 A 机器人发去工位上料，取完后，才能下发机器人再下料。



## 8 单车并行执行

一台设备的不同机构具有并行执行能力。如边走边升降叉、边走边启动滚筒。接近终点 3 米时开始播放音乐。



## 9 多车并行执行

派辆车到相邻 A、B 两个库位，先到的车等待，两车都到达后，A 车向 B 车卸货（滚筒）。



## 10 密集平库

一台车进去出来后，另一台车才能进去。但细节时，另一台车最好在足够近的地方等，而不是为了等前车，在很远的地方就地不动。



## 10.1 穿行密集平库

当一块密集库空时，可以穿行。



## 11 排队取货

多台叉车排队到库口取立库吐出来的货物。

难点一是平滑排队。二是任务中断，排队中执行高优先级任务。

三是顺序问题。WMS 通知接下来会吐栈板 A、B、C，分配车 1、2、3，但 2 车先到。所以先排上队再绑定车和货物。



## 12 并行预定终点

基本任务是，从指定起点运输货物到某个区域的一个空库位。那么在货物最终往库位上放之前，需要库区有空库位。

几种串行的方式：

1. 直接派空车去起点取货，然后运行到库区外，找空库位。如果库区满且迟迟没有新的空库位，机器人长时间被占用，浪费。
2. 先尝试找到一个空库位。找到前，不派车。问题是，机器人取货可能很久（比如取货的地方很远），造成这个空库位周转率下降。
3. 起点很远，所以先派车去起点。取到货时再尝试找空库位。同样，如果迟迟没有空库位，机器人在起点处等待，浪费。

并行的方式，如：

起点很远，所以先派车去起点。取到货即往终点走，同时，并发的找空库位，如果先找到，机器人直接运进去。如果机器人到库区外了，还没找到，则继续等。



## 13 多负载机器人

对于料箱或同时能背多个货的复合机器人。

如果产线叫料需要送 A、B、C 三箱货，机器人能一次拿三个，则机器人先去库里取三个，一起运到产线。

进一步规定，A、B 在库里距离较近，比如同一排，而 C 较远，则先运 A、B，再运 C。

进一步规定，如果现在产线产生以下叫料：工位 1 叫料、工位 2 叫料、工位 1 叫料、工位 3 叫料、工位 1 叫料，则应合并处理工位 1 的叫料请求，去库里把工位 1 的叫料全拿出来，送到产线，再处理其他工位。



## 13.1 转向成本&无法转向

料箱机器人进入巷道里，取了第 2 列、第 3 列的货，此时第 1 列、第 10 列又有任务，机器人该取第 10 列的，因为是顺向，取第 1 列要倒车。



## 13.2 以最少的水平移动卸货

料箱机器人带着 4 个货，如果其中 2 个是工位 1 需要的，则把这两个一起卸下来。避免卸一个，去其他工位，再回来卸另一个的情况。



## 13.3 以最少水平移动卸货 - 找库位

料箱机器人带 4 个货，到库口，第一列有 3 个空库位，不选，第二列有 4 个空库位，放第二列。



## 13.4 复合机器人多容器规格

复合机器人身上有两组库位，放大小料盒，能放 N 个和 M 个。



## 13.5 顺向取货

巷道只能从列小向列大的方向移动，



## 14 料箱机器人 N+1

料箱机器人背后能带 N 个箱子，抓斗里也要带一个。因此再进一步取货下，需要先把抓斗里的放下。



## 15 条件等待

要求，顶升机器人去工位放货后，就地等待 5 秒，如果 5 秒内有上一箱空箱下料，则优先拉走此空箱。



## 16 电梯放货机器人不进电梯

电梯内设四个库位。机器人不跨电梯。从一楼送二三四楼。到达一楼后，放货。等待四个库位放满或最长等待 3 分钟，电梯关门。如果电梯内放了去 1 楼的货，下面有去 2 楼的货，先送到缓冲区。

如果有多电梯更复杂一些。



## 17 多负载机器人取放交替

简单料箱车拼单（波次），一个波次要么先批量执行取货、再批量执行放货。

先改成可以交替。例如料箱机器人能背 4 个货，先取货 4 个，去 A 工位，放 2 个满料，从 A 工位取空箱 2 个，去 B 岗位放 2 个满料，取 1 个空箱，还能再拉一个，恰好 A 工位又有一个空箱出来，去取。



## 17.1 先放后取

料箱车，能拉 4 个，带 2 个，去工位，需要先取走两个，把库口空出来，再放下 2 个。



## 18 动态波次任务

料箱机器人收到多个任务，其中去某排第 1、2、10 列取货化为一个波次，但取完 1 列后，收到新任务取 3 列，则挤出第 10 列，先取第 3 列。



## 19 动态派单

库区内每个库位上的货物指定一个工位。当某个工位叫料时，把库位内这个库位的货都运过去，才能运一个工位的。地牛，一次只能运一托。如果叫料时，库区内有 4 托，但可能：

1. 变少。运完第一托发现，只有 2 托了，因为 1 托被人工拉走。
2. 变多。运完第一托，发现还有 4 托，因为又补了 1 托，要求补的也要运走。



## 20 任务抢占

顶升车收到任务，去较远工位取货，途中，收到离当前位置更近的另一工位取货请求，则扔回前任务，改执行新任务。



## 20.1 暂不回库

A 工位叫料，拣货后，有余料。此时，B 工位叫料，且物料匹配，则先不回库，先直接去 B 工位。



## 21 料箱机器人出库需要每次放货与库口交互

根据出库需求生成搬运任务。起点终点明确。

去多个起点批量搬运。

去终点。

获取库位放行信号，放一个箱子。重复直至箱子放完。



## 多车先进后出区域

一个库区，先进后出。

取货时，最外面货是 1，里面一个是 2，分配 A 取 1、B 取 2，则必须要求 A 先到，如果 B 先到，无法取到里面的货。

即，到库区取货时，无法保证哪台车先到库区。

先通过计数器判断库区有余料。然后派车到库区前，车到达库区前后实际分配（最外面）。

取放货也是串行的。假如有取货任务，派机器人 A，在取到前，产生了放货任务，应直接调度机器人 A 不从库区取货，而是直接从产生货的地方取，不用经过库区。

或者，如果取货只能从库区取。且放货机器人先到，放货机器人可以不往里面放，而是放最外面，方便取货机器人直接拿走。



## 更换取放货库位

如人机混合作业场景。

取货，到达后发现库位空，重新分配新库位。

放货，达到后发现库位满，重新分配新库位。



## 24 多负载机器人严格顺序送料

每个出库单是给一个产线各工序的上料需求。必须按出库单行顺序上料。

首先，每行出库单行可能对应多个箱子。例如第 1 行 2 个，第 2 行 1 个，第 3 行 2 个。

如果机器人一趟能拉掉，则放货顺序要按上面的箱子顺序。

如果有多台机器人，要确保第一批箱子的机器人先到。



## 25 光通讯



# 任务的取消与失败

## 任务的取消

例 1：

1. 开始：此时取消，什么也不处理
2. 锁定库存和容器：此时取消，需要解锁
3. 去起点取货：执行回库逆流程
4. 解绑容器库位：
5. 寻找终点空库位并锁定：释放终点库位，货物执行回库逆流程
6. 去终点放货：
7. 绑定新容器库位库存

## 任务的异常

例 1：

1. 开始
2. 锁定库存和容器
3. 去起点取货
4. 解绑容器库位
5. 寻找终点空库位并锁定
6. 去终点放货
7. 绑定新容器库位库存

异常及处理：

- 在取货途中机器人故障，解锁起点，任务失败。
- 在取货时或取货后途中故障，容器人工上架。

## 多负载机器人的异常

如料箱车。分货物异常和机器人异常。如果货物异常，不能影响同机器人身上的其他货和任务。如果机器人异常，则多个任务失败。

# 关键设计

运单：货物（容器）、关键点、取货点、放货点、机器人。

在取货前、放货前，放货后可能经过数个关键点，如 关键点 A - 取货 - 关键点 B - 放货 - 关键点 C。

对于料箱车，可能出现，去 A 点（比如库口）、取取取、去 B 点（出库口）、放放放。即要取放三个箱子，但取之前都要先经过库口。取完也先经过库口。此时车是一个整体。

如果收到到同一个点的放货请求，应合并成先去这个点，再放货。

料箱机器人取放货位置可能出现：起点靠近，终点靠近，起点靠近、终点不靠近，起点不靠近、终点靠近。

对下发任务的一方，不管这个任务会被哪个机器人、会不会被拼单。我只要求去 A、去 B 取货、去 C 、去 D 放货。虽然，这里面有顺路性。另一个视角，也可以看成是，派车去 A ，开始拼单，取 B、C、D，派车去 E，开始拼单，放 F、G、H。在 A 之前关闭拼单，在 A 过后开始拼单？对料箱车，有拼单模式和不拼单模式？

发单的部分是否需要关心会不会被拼单？发单只管发，不在乎会被拼单 vs 发单时指定要拼单。

机器人的拼单和人的拼车有什么区别？

下发任务块状态：暂不可执行，比如还未放行，只是用来预测后期行为。



# 设备任务调度

设备任务调度系统包括两部分：中央管理系统和位于设备内的执行系统。

设备任务调度主要包括：

- 多个任务在设备间的分派。例如有 3 个出库任务、2 个入库任务，2 台机器人，让哪个机器人执行哪个任务。甚至可能让一个机器人暂时空闲，因为车型不匹配、电量低或预测即将有更适合的任务。
- 一个设备内多个任务的处理。例如料箱机器人一次可以带 4 个箱子，可以发给它 6 个任务，设备决定先执行哪些任务（1 个到 4 个），因为比较近。还包括任务的并发处理，例如边行走边抬叉，边行走边播放音乐。
- 任务池动态增减。包括一个设备内任务执行顺序的重排序。甚至已下发到设备的未执行任务的回收和重新分配。
- 任务执行管理。一个任务可能包含多个环节。如取货、放货。在环节前后可能需要前置、后置处理。如等待信号、等待其他任务的某个环节。
- 任务同步。任务间或任务内环节间具有依赖关系。例如，必须先执行完 A 任务再执行 B 任务。或按照 A 任务的第一阶段、B 任务的第一阶段、A 任务的第二阶段、B 任务的第二阶段执行。一个机器人可以并发、交替执行任务。多个机器人可以相互协调。



# 一、概述

两个关键概念：控制对象和指令。

WCS 的控制对象称为“设备”（Device）。其中一类特殊的是机器人（Robot）。机器人中一类特殊的是移动机器人（Mobile Robot）。移动机器人有很多类型，如顶升车、滚筒车、叉车、料箱车、复合机器人。

对设备的操作称为指令（Action）。

指令是对设备最小粒度的控制命令。不会进一步拆分指令为子指令、步等。

## 多指令

最简单的情况下，每次只产生一条，发给设备执行。

但现实中往往需要产生发送大量指令。根据设备的智能程度，有时设备可以接收大量指令，自行决定如何智能执行。但如果设备不具备这个能力，就需要 WCS 决定这个指令该由哪个设备执行、是否能执行、什么时候执行。

首先，WCS 要具备选择设备的能力。WCS 能接受多个（大量）指令，WCS 决定由哪个设备执行。

其次，在 WCS 上如果一个设备有多个待执行指令。可以智能决定这些指令是否可以执行、是否并行执行、执行先后顺序、什么时候执行等。细化说明：

1. 并行能力。如果设备具有接收并行指令的能力/需求（举例：移动、抬叉、放音乐），WCS 可以发送并行指令。
2. 重排指令的能力。收到“取 A、放 A、取 B 放 B ”的指令，按“取 B 、取 A 、放 A 、放 B”执行。
3. 智能决定是否能执行（先后顺序）。例如如果一次只能取一个容器。虽然收到了“取 A、取 B、放 A、放 B”的指令，但在“取 B”前，必须先“放 A”。
4. 遵循指令依赖关系。如收到 A、B、C 三条指令，其中 C 必须待 A 执行后才能执行，A、B 无约束。
5. 指令序列（Sequence）串行执行。如 CPU 类似，必须按指定的指令顺序执行，不能重排，且中间不能插入其他指令。


> 关于目前 RDS CORE 的运单、块、封口设计。目前 CORE 的封口的设计至少能保证机器人在做完块后不会接其他运单。比如如果机器人取完货，放货块还没发。如果没有封口机制，机器人接其他单、取货就碰撞了。其次从语义上，未封口表示虽然做完了已有的块，但可能还有后续块，机器人要等着。比如先移动到某个点，等着，等一系列其他任务完成后，才能生成和执行下一个块。
>
> 更本质的看。创建运单 1、取  A、放 A、运单 1 封口、创建运单 2、取 B、放 B、运单 2 封口。也可以发成：创建运单 1、取 A、放 A、取 B、放 B、运单 1 封口。这里关键是决策好下面该执行什么。按照这种思路，一个机器人运行过程中只要一个运单足矣。
>
> 在业务层面，一定是有运单或任务的概念的。在业务层面，一次运输包括几步一般是确定的；规则是，符合实际情况。比如我们一般把一次取、一次放看做一个任务。
>
> 换个角度描述：当机器人取完货后，在收到放货指令前，不能接其他指令，不能是因为没有封口，是因为它此时已经不具备接其他类型任务的指令了。
>
> 封口是运单/任务视角。但或许以机器人视角更清楚：是否释放机器人接其他“单”。机器人和任务，什么时候该控制机器人，什么时候该控制任务；信息该记录在机器人上还是任务上。


## 指令的重排和抢占

以一些边界例子说明。

顶升车（一次只能带一个容器），做“到 A 点等待，在 A 点取货，去 B 点等待，去 C 点卸货”的任务。

1. 收到并执行完“到 A1 点等待”，此时如果收到另一个任务，“到 A2 点等待”，应该继续等待。
2. 收到并执行完“到 A1 点等待”，此时如果收到另一个任务，“到 A2 点等待”，应该继续等待。但此时如果又收到了第二个任务的第二步，“在 A2 点取货”，应该中断第一个任务，先执行第二个任务。
3. 但如果第一个任务已执行到在 B 点等待，此时收到第二个任务，哪怕收到了第二个任务全部步骤，也不能执行，因为已经超过执行能力（带着货）。


但对于料箱车（一次能带多个容器），如果第一个任务已执行到在 B 点等待，此时收到第二个任务，从能力上说车是有能力执行第二个任务的（如果还能放容器在身上），此时该做什么取决于第一个任务等待时间和第二个任务会不会更快完成。


类似的，对于顶升车，空车取货指令在取货前可以被更优指令抢占，但顶升车放货途中不能被抢占。

## 全局一次只处理一条指令（贪心）

在不考虑并行的情况下。假设此时有两台料箱车，一次能载 4 个料箱，目前两车全空。有 10 个要运输的箱子，即先有 10 个取货指令。

对两车计算取 10 个箱子的成本（20 条数据），两车取分别取最小值先执行（A 车取箱子 a）。一车取完后再决定下一次做哪个指令。在取货过程中可能有新指令发出或原指令已被执行完。可能出现，车开始搬第一个箱子过程中才出现一个让车搬临近箱子的指令。所以不要过早决策。

贪心策略可能不会导致全局最优解，一般项目问题不大（感受不到）。

### 预留任务

直观上可能的问题：料箱机器人。两台车可执行任务。排序出指令成本。指令 a 由 A 车执行。第二个低成本 b 的指令由 B 车执行。但 b 与 a 近，应该预留给 A 车执行。

因此，分配一台车后，如果这台车有空余能力，应顺便查看一定成本阈值内的其他任务，预留，本次不分配。

### 来回走

例如，料箱车，需要在当期位置左右各取 2 箱，可能出现先到左边近的，再到右边近的，再到左边远的，再到右边远的 —— 来回走的情况。

可能时间最优线路是左边近的、左边远的、右边近的、右边远的。

但人感知的最优路线就是走直线，从左边远的、左边近的、右边近的、右边远的。

考虑到转向问题更复杂。

## 记录每轮分派

机器人和指令的分配一轮轮进行。记录过程，方便调试和解释。

## 执行条件

以料箱机器人为例：

1. 取满了就不能再取了，至少先执行一个放的指令。
2. 要是抓斗本身带着箱子，做任何取、放指令前，都必须先做放抓斗箱子的指令。

## 执行顺序

一类任务有严格的顺序，必须按顺序执行。如按出库单逐行安排运输。



# 移动机器人的指令处理

移动机器人指令（Mobile Robot Action）。

指令执行状态：未执行、执行中、执行结束。

结束类型：成功、失败、取消。



# 移动机器人

机器人状态
禁用 disabled
任务状态：空闲 idle，任务中 working，待执行 pending 等待下一步任务
暂停 paused
故障中 error

# WCS 连接机器人

WCS 控制机器人：

- WCS TCP Client -> 机器人
- WCS Http Client -> GWS Http Server | TCP Client -> 机器人
- WCS WebSocket Client -> GWS WebSocket Server | TCP Client -> 机器人
- WCS WebSocket Server <- GWS WebSocket Client | TCP Client -> 机器人
- WCS TCP Server <- GWS TCP Client | TCP Client -> 机器人