package com.seer.wcs.stats

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.stats.ChartGroup
import com.seer.trick.base.stats.ChartItem
import com.seer.trick.base.stats.StatsService

object WcsStatsService {
  
  fun register() {
    StatsService.addGroup(
      ChartGroup(
        "ContainerTransportOrder", "容器搬运单", 2, mutableListOf(
          ChartItem("ContainerTransportOrderToday", "今日容器搬运单统计", "", 0),
          ChartItem("ContainerTransportOrderLast7Days", "最近七日容器搬运单统计", "", 0),
          ChartItem("ContainerTransportOrderLast30Days", "最近三十日容器搬运单统计", "", 0),
        )
      )
    )
    
    StatsService.internalCalcMap["ContainerTransportOrderToday"] = {
      StatsService.calcToday(
        StatsService.CalcToday(
          entityName = "ContainerTransportOrder",
          projection = listOf("status"),
          update = ::updateContainerTransportOrder,
          title = "今日容器搬运单统计",
          dimensions = listOf("创建数", "完成数", "异常数"),
          chartType = "line",
        )
      )
    }
    StatsService.internalCalcMap["ContainerTransportOrderLast7Days"] = {
      StatsService.calcLastNDays(
        StatsService.CalcLastNDays(
          days = 7,
          entityName = "ContainerTransportOrder",
          projection = listOf("status"),
          update = ::updateContainerTransportOrder,
          title = "最近七日容器搬运单统计",
          dimensions = listOf("创建数", "完成数", "异常数"),
          chartType = "bar",
        )
      )
    }
    StatsService.internalCalcMap["ContainerTransportOrderLast30Days"] = {
      StatsService.calcLastNDays(
        StatsService.CalcLastNDays(
          days = 30,
          entityName = "ContainerTransportOrder",
          projection = listOf("status"),
          update = ::updateContainerTransportOrder,
          title = "最近三十日容器搬运单统计",
          dimensions = listOf("创建数", "完成数", "异常数"),
          chartType = "bar",
        )
      )
    }
    
    StatsService.addGroup(
      ChartGroup(
        "DirectRobotOrder", "直接运单", 1, mutableListOf(
          ChartItem("DirectRobotOrderToday", "今日直接运单统计", "", 0),
          ChartItem("DirectRobotOrderLast7Days", "最近七日直接运单统计", "", 0),
          ChartItem("DirectRobotOrderLast30Days", "最近三十日直接运单统计", "", 0),
        )
      )
    )
    
    StatsService.internalCalcMap["DirectRobotOrderToday"] = {
      StatsService.calcToday(
        StatsService.CalcToday(
          entityName = "DirectRobotOrder",
          projection = listOf("status"),
          update = ::updateDirectOrder,
          title = "今日直接运单统计",
          dimensions = listOf("创建数", "完成数", "异常数"),
          chartType = "line",
        )
      )
    }
    StatsService.internalCalcMap["DirectRobotOrderLast7Days"] = {
      StatsService.calcLastNDays(
        StatsService.CalcLastNDays(
          days = 7,
          entityName = "DirectRobotOrder",
          projection = listOf("status"),
          update = ::updateDirectOrder,
          title = "最近七日直接运单统计",
          dimensions = listOf("创建数", "完成数", "异常数"),
          chartType = "bar",
        )
      )
    }
    StatsService.internalCalcMap["DirectRobotOrderLast30Days"] = {
      StatsService.calcLastNDays(
        StatsService.CalcLastNDays(
          days = 30,
          entityName = "DirectRobotOrder",
          projection = listOf("status"),
          update = ::updateDirectOrder,
          title = "最近三十日直接运单统计",
          dimensions = listOf("创建数", "完成数", "异常数"),
          chartType = "bar",
        )
      )
    }
  }
  
  private fun updateContainerTransportOrder(ev: EntityValue, sv: EntityValue) {
    StatsService.addInt(sv, "创建数", 1)
    
    val status = ev["status"]
    if (status == "Done") StatsService.addInt(sv, "完成数", 1)
    else if (status == "Cancelled" || status == "Failed") StatsService.addInt(sv, "异常数", 1)
  }
  
  private fun updateDirectOrder(ev: EntityValue, sv: EntityValue) {
    StatsService.addInt(sv, "创建数", 1)
    
    val status = ev["status"]
    if (status == "Done") StatsService.addInt(sv, "完成数", 1)
    else if (status == "Cancelled") StatsService.addInt(sv, "异常数", 1)
  }
  
}