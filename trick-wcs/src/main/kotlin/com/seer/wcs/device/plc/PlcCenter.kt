package com.seer.wcs.device.plc

import com.github.xingshuangs.iot.protocol.s7.service.S7PLCServer
import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.I18N

import com.seer.trick.base.concurrent.BaseConcurrentCenter.highTimeSensitiveExecutor
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.change.EntityChange
import com.seer.trick.base.entity.service.extension.EntityServiceExtension
import com.seer.trick.base.entity.service.extension.EntityServiceExtensions
import com.seer.trick.base.event.EventListener
import com.seer.trick.helper.NumHelper
import com.seer.trick.helper.submitCatch
import com.seer.wcs.device.plc.modbus.ModbusClient
import com.seer.wcs.device.plc.modbus.ModbusConfig
import com.seer.wcs.device.plc.s7.S7Client
import com.seer.wcs.device.plc.s7.S7Config
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap

object PlcCenter : EntityServiceExtension(), EventListener<String> {

  private val logger = LoggerFactory.getLogger(this::class.java)

  private val modbusClients: MutableMap<String, ModbusClient> = ConcurrentHashMap()

  private val s7Clients: MutableMap<String, S7Client> = ConcurrentHashMap()

  private var s7MockServer: S7PLCServer? = null

  init {
    BzConfigManager.eventBus.listeners += this
    EntityServiceExtensions.addExtension("PlcDeviceConfig", this)
  }

  fun init() {
    highTimeSensitiveExecutor.submitCatch("Init s7 mock", logger) { initS7MockServer() }

    val evList = EntityRwService.findMany("PlcDeviceConfig", Cq.all())
    logger.info("Plc devices: ${evList.size}")

    createDevices(evList)

    PlcPanelService.init()
  }

  private fun initS7MockServer() {
    val mock = BzConfigManager.getByPath("ScDev", "s7mock") == true
    if (mock) {
      logger.info("启动 S7 模拟 Server")
      s7MockServer = S7PLCServer()
      s7MockServer?.addDBArea(2, 3, 4)
      s7MockServer?.start()
    } else {
      disposeS7MockServer()
    }
  }

  private fun disposeS7MockServer() {
    if (s7MockServer == null) {
      return
    }
    s7MockServer?.stop()
    s7MockServer = null
  }

  fun dispose() {
    for (client in modbusClients.values) {
      client.dispose("DisposeFromPlcCenter")
    }

    for (c in s7Clients.values) {
      c.dispose("DisposeFromPlcCenter")
    }

    disposeS7MockServer()
  }

  override fun onEvent(e: String) {
    // BzConfig 配置改变
    
    logger.debug("PlcCenter onEvent [$e]")
    initS7MockServer()
  }

  fun getModbusClient(name: String): ModbusClient? = modbusClients[name]

  fun mustGetModbusClient(name: String): ModbusClient = modbusClients[name] ?: throw BzError("errNoDevice", name)

  fun getS7Client(name: String): S7Client? = s7Clients[name]

  fun mustGetS7Client(name: String): S7Client = s7Clients[name] ?: throw BzError("errNoDevice", name)

  fun existModbusClient(name: String): Boolean = modbusClients.containsKey(name)

  fun existS7Client(name: String): Boolean = s7Clients.containsKey(name)

  override fun beforeCreating(em: EntityMeta, evList: List<EntityValue>): List<String>? {
    if (em.name != "PlcDeviceConfig") return super.beforeCreating(em, evList)
    evList.map { pCheck(it, true, true) } // 新增时，固定检查 type、host

    return null
  }

  override fun beforeUpdating(em: EntityMeta, ids: List<String>, update: EntityValue): Long? {
    if (em.name != "PlcDeviceConfig") return super.beforeUpdating(em, ids, update)

    // 前面取 ids 已经查过一遍了，所以这里查的缓存，基本不消耗性能
    val evList = EntityRwService.findMany("PlcDeviceConfig", Cq.include("id", ids))
    var checkType = false
    var checkHost = false
    for (ev in evList) {
      if (!ev.containsKey("type") || StringUtils.isBlank(ev["type"] as String?)) checkType = true
      if (!ev.containsKey("host") || StringUtils.isBlank(ev["host"] as String?)) checkHost = true
    }

    pCheck(update, checkType, checkHost)

    return null
  }

  /**
   * 校验 type、host 必填
   * 创建/单条更新时，前端是全量传过来，因此这里一定有值。checkType，checkHost 均传 true
   * 批量更新时，需要先把原本的值查出来，如果原本的值里不包含 type、host，那么 update 中必须包含 type、host。checkType、checkHost 取决于数据库中已存在的值
   *
   * 需要报错：
   * - 要求 type、host 必填时，没传 type、host
   * - 传了 type、host，但值为空
   */
  private fun pCheck(update: EntityValue, checkType: Boolean, checkHost: Boolean) {
    val type = update["type"] as String?
    if (checkType && type.isNullOrBlank() || update.containsKey("type") && type.isNullOrBlank()) {
      logger.error("设备类型未配置")
      throw BzError("errNoDeviceType", I18N.lo("errNoDeviceType"))
    }

    val host = update["host"] as String?
    if (checkHost && host.isNullOrBlank() || update.containsKey("host") && host.isNullOrBlank()) {
      logger.error("设备 地址/IP 未配置")
      throw BzError("errNoDeviceHost", I18N.lo("errNoDeviceHost"))
    }
  }

  override fun afterCreating(em: EntityMeta, evList: List<EntityValue>) {
    if (em.name != "PlcDeviceConfig") return
    highTimeSensitiveExecutor.submitCatch("after create plc device", logger) {
      createDevices(evList)
    }
  }

  override fun afterUpdating(em: EntityMeta, changes: List<EntityChange>) {
    if (em.name != "PlcDeviceConfig") return
    for (c in changes) {
      val ev = c.newValue ?: continue
      val id = EntityHelper.mustGetId(ev)
      val type = ev["type"] as String?
      if (type.isNullOrBlank()) {
        logger.error("设备 '$id' 类型未配置")
        continue
      }
      when (type) {
        "Modbus" -> updateModbus(ev)
        "S7" -> updateS7(ev)
      }
    }
  }

  override fun afterRemoving(em: EntityMeta, oldValues: List<EntityValue>) {
    if (em.name != "PlcDeviceConfig") return
    for (ev in oldValues) {
      val id = EntityHelper.mustGetId(ev)
      val type = ev["type"] as String?
      if (type.isNullOrBlank()) {
        logger.error("设备 '$id' 类型未配置")
        continue
      }
      when (type) {
        "Modbus" -> removeModbus(id)
        "S7" -> removeS7(id)
      }
    }
  }

  private fun createDevices(evList: List<EntityValue>) {
    for (ev in evList) {
      try {
        val id = EntityHelper.mustGetId(ev)

        // FIXME 禁用的设备也初始化，这样重启后《PLC 面版》中的状态是已禁用，不是无此设备
        // val disabled = ev["disabled"] as Boolean?
        // if (disabled == true) continue

        val type = ev["type"] as String?
        if (type.isNullOrBlank()) {
          logger.error("设备 '$id' 类型未配置")
          continue
        }
        when (type) {
          "Modbus" -> createModbus(ev)
          "S7" -> createS7(ev)
          // "OPCUA" -> createOpcUa(ev)
        }
      } catch (e: BzError) {
        logger.error("创建设备报错：" + e.message)
      } catch (e: Exception) {
        logger.error("创建设备报错", e)
      }
    }
  }

  private fun createModbus(ev: EntityValue) {
    val id = EntityHelper.mustGetId(ev)
    val config = toModbusConfig(ev)
    logger.info("创建 Modbus 客户端 '$id', $config")
    val client = ModbusClient(config)
    modbusClients[id] = client

    // TODO 这里真的要异步吗
    highTimeSensitiveExecutor.submitCatch("init modbus", logger) {
      client.init(true)
    }
  }

  private fun createS7(ev: EntityValue) {
    val id = EntityHelper.mustGetId(ev)
    val config = toS7Config(ev)
    logger.info("创建 S7 客户端 '$id', $config")
    val client = S7Client(config)
    s7Clients[id] = client

    highTimeSensitiveExecutor.submitCatch("init s7", logger) {
      client.init(true)
    }
  }

  private fun updateModbus(ev: EntityValue) {
    val id = EntityHelper.mustGetId(ev)
    s7Clients.remove(id)?.let {
      logger.warn("更新 Modbus 设备，但此设备是 S7 类型，尝试销毁")
      it.destroy()
    }

    val client = modbusClients[id]
    if (client == null) {
      logger.warn("更新 Modbus 设备，但此设备不存在 '$id'，尝试重建")

      createModbus(ev)
    } else {
      logger.warn("更新 Modbus 设备")
      val config = toModbusConfig(ev)

      highTimeSensitiveExecutor.submitCatch("update modbus", logger) {
        client.updateConfig(config)
      }
    }
  }

  private fun updateS7(ev: EntityValue) {
    val id = EntityHelper.mustGetId(ev)
    modbusClients.remove(id)?.let {
      logger.warn("更新设备类型 $id，从 Modbus 变为 S7")
      it.destroy()
    }

    val client = s7Clients[id]
    if (client == null) {
      logger.warn("更新 S7 设备，但此设备不存在 '$id'，尝试重建")

      createS7(ev)
    } else {
      logger.warn("更新 S7 设备")
      val config = toS7Config(ev)

      highTimeSensitiveExecutor.submitCatch("update s7", logger) {
        client.updateConfig(config)
      }
    }
  }

  private fun toModbusConfig(ev: EntityValue): ModbusConfig {
    val id = EntityHelper.mustGetId(ev)
    val config = ModbusConfig(
      name = id,
      disabled = ev["disabled"] as Boolean? ?: false,
      autoRetry = ev["autoRetry"] as Boolean? ?: false,
      timeout = ev["timeout"] as Int? ?: 15000,
      maxRetry = NumHelper.anyToInt(ev["maxRetry"]) ?: -1, // 作为一组默认配置
      retryDelay = NumHelper.anyToLong(ev["retryDelay"]) ?: 3000,
      host = ev["host"] as String? ?: "",
      port = NumHelper.anyToInt(ev["port"]) ?: 502,
    )
    if (config.host.isBlank()) {
      throw BzError("errCodeErr", "HostRequired")
    }
    return config
  }

  private fun toS7Config(ev: EntityValue): S7Config {
    val id = EntityHelper.mustGetId(ev)
    val cfg = S7Config(
      name = id, disabled = ev["disabled"] as Boolean? ?: false,
      timeout = ev["timeout"] as Int? ?: 6000,
      maxRetry = NumHelper.anyToInt(ev["maxRetry"]) ?: -1, // 作为一组默认配置
      retryDelay = NumHelper.anyToLong(ev["retryDelay"]) ?: 3000,
      autoRetry = ev["autoRetry"] as Boolean? ?: false,
      plcType = StringUtils.defaultIfBlank(ev["subType"] as String?, "S1200") ?: "S1200",
      host = ev["host"] as String? ?: "",
      port = NumHelper.anyToInt(ev["port"]) ?: 102,
      rack = NumHelper.anyToInt(ev["rack"]) ?: 0,
      slot = NumHelper.anyToInt(ev["slot"]) ?: 2,
    )
    if (cfg.host.isBlank()) {
      throw BzError("errCodeErr", "HostRequired")
    }
    return cfg
  }

  private fun removeModbus(id: String) {
    val client = modbusClients[id]
    if (client == null) {
      logger.error("更新 Modbus 设备，但此设备不存在 '$id'")
      return
    }

    modbusClients.remove(id)

    highTimeSensitiveExecutor.submitCatch("destroy modbus $id", logger) {
      client.destroy()
    }
  }

  private fun removeS7(id: String) {
    val client = s7Clients[id]
    if (client == null) {
      logger.error("更新 S7 设备，但此设备不存在 '$id'")
      return
    }

    s7Clients.remove(id)

    highTimeSensitiveExecutor.submitCatch("destroy s7 $id", logger) {
      client.destroy()
    }
  }
}