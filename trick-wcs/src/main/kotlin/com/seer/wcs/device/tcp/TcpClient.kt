package com.seer.wcs.device.tcp

import com.seer.trick.BzError

import com.seer.trick.base.config.BzConfigManager
import io.netty.bootstrap.Bootstrap
import io.netty.buffer.ByteBuf
import io.netty.channel.*
import io.netty.channel.nio.NioEventLoopGroup
import io.netty.channel.socket.SocketChannel
import io.netty.channel.socket.nio.NioSocketChannel
import io.netty.handler.ssl.SslContextBuilder
import io.netty.handler.ssl.SslHandler
import org.slf4j.LoggerFactory
import java.net.InetSocketAddress
import java.net.SocketException

/**
 * T 是消息的类型
 */
class TcpClient<T : Any>(
  private val host: String,
  private val port: Int,
  private val schema: FixedHeadFrameSchema<T>,
  private val ssl: Boolean,
  private val onMessage: MsgCallback<T>,
  private val onRemoved: CtxRemovedCallback? = null,
) {

  private val logger = LoggerFactory.getLogger(this::class.java)

  private val loggerHead: String = "$host:$port "

  private var nettyClient: NettyClient? = null

  @Volatile
  private var disposed = false

  fun dispose() {
    logger.info(loggerHead + "Dispose tcp client")
    disposed = true

    val c = nettyClient
    nettyClient = null
    if (c != null) {
      try {
        c.close()
      } catch (e: Exception) {
        // ignore
      }
    }
  }

  /**
   * 可能耗时
   */
  fun getNettyClient(): NettyClient {
    var c = nettyClient
    if (c == null) {
      c = newNettyClient()
      disposed = false
      nettyClient = c
    }
    return c
  }

  /**
   * 可能耗时
   */
  private fun newNettyClient(): NettyClient {
    logger.info(loggerHead + "New Netty TCP client")

    val sslCtx = if (ssl) {
      // TODO 这里还是不对，用不了
      SslContextBuilder.forClient() // 注意是 for client
        .keyManager(ssc.key(), "", ssc.cert())
        .build()
    } else {
      null
    }

    val handler = InboundHandler(onMessage, this::onError, onRemoved)
    val group: EventLoopGroup = NioEventLoopGroup() // TODO 放外面？
    val b = Bootstrap()
    b.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 10000)
    b.group(group) //
      .channel(NioSocketChannel::class.java) //
      .remoteAddress(InetSocketAddress(host, port)) //
      // .handler(LoggingHandler(LogLevel.INFO, ByteBufFormat.HEX_DUMP))
      .handler(object : ChannelInitializer<SocketChannel>() {
        override fun initChannel(ch: SocketChannel) {
          val pipeline: ChannelPipeline = ch.pipeline()
          if (sslCtx != null) pipeline.addLast(SslHandler(sslCtx.newEngine(ch.alloc())))
          pipeline.addLast(FixedHeadFrameDecoder(schema))
          pipeline.addLast(handler)
        }
      })
    return try {
      val f: ChannelFuture = b.connect().sync()
      logger.info(loggerHead + "TCP client connected")
      NettyClient("$host:$port", group, f)
    } catch (e: Throwable) {
      if (e is SocketException) {
        logger.error(loggerHead + "Failed to connect tcp, " + e.message)
      } else {
        logger.error(loggerHead + "Failed to connect tcp, ", e)
      }
      try {
        group.shutdownGracefully().sync() // 关闭 group 否则会泄露
        logger.info(loggerHead + "Done to show down")
      } catch (e: Exception) {
        logger.error(loggerHead + "Shutdown group", e)
      }
      throw e
    }
  }

  private fun onError(e: Throwable) {
    if (e is BzError) {
      logger.error(loggerHead + "onError called: ${e.message}")
    } else {
      logger.error(loggerHead + "onError called", e)
    }
    dispose()
  }

  @Synchronized
  fun write(buf: ByteBuf) {
    val log = BzConfigManager.getByPath("ScDev", "logTcpWrite") == true
    val client = getNettyClient()
    if (log) logger.debug("TcpClient write called")
    client.write(buf)
    if (log) logger.debug("TcpClient write end")
  }
}