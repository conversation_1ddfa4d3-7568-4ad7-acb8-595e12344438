package com.seer.wcs.device.plc.modbus

import com.digitalpetri.modbus.master.ModbusTcpMaster
import com.digitalpetri.modbus.master.ModbusTcpMasterConfig
import com.digitalpetri.modbus.requests.*
import com.digitalpetri.modbus.responses.*

import com.seer.trick.base.failure.FailureRecordReq
import com.seer.trick.base.failure.FailureRecorder
import com.seer.trick.helper.getTypeMessage
import com.seer.wcs.device.*
import com.seer.wcs.device.plc.PlcCenter
import com.seer.wcs.device.plc.PlcRwLogService
import io.netty.buffer.ByteBuf
import io.netty.buffer.Unpooled
import java.io.IOException
import java.time.Duration
import java.time.temporal.ChronoUnit
import java.util.BitSet
import java.util.concurrent.*

/**
 *
 */
class ModbusClient(config: ModbusConfig) : ClientDevice<ModbusConfig>(config, "Plc", "Modbus") {

  private val readTimeout = 10L

  private var modbusClient: ModbusTcpMaster? = null

  /**
   * 所有异常包成 DeviceException
   */
  override fun doConnect() {
    try {
      val cfg = ModbusTcpMasterConfig.Builder(config.host)
        .setTimeout(Duration.of(config.timeout.toLong(), ChronoUnit.MILLIS)) // TODO 什么超时 测试一下
        .setMaxReconnectDelaySeconds(0) // TODO 做什么用
        .setPort(config.port)
        .build()
      val nc = ModbusTcpMaster(cfg)
      nc.connect()?.get()
      modbusClient = nc
    } catch (e: ExecutionException) {
      val e2 = e.cause ?: e
      if (e2 !is IOException) {
        logger.error(loggerHead() + "连接失败", e2)
      }
      throw ClientDeviceException(ClientDeviceExceptionKind.ConnectFail, loggerHead() + e2.getTypeMessage(), e2)
    } catch (e: CancellationException) {
      throw ClientDeviceException(ClientDeviceExceptionKind.ConnectFail, loggerHead() + "CancellationException", e)
    } catch (e: InterruptedException) {
      throw ClientDeviceException(ClientDeviceExceptionKind.ConnectFail, loggerHead() + "InterruptedException", e)
    } catch (e: Exception) {
      logger.error(loggerHead() + "连接失败", e)
      throw ClientDeviceException(ClientDeviceExceptionKind.ConnectFail, loggerHead() + "UnknownException", e)
    }
  }

  public override fun ensureDeviceExist(): Boolean {
    if (!PlcCenter.existModbusClient(config.name)) {
      FailureRecorder.addAsync(
        
        FailureRecordReq(kind = "Plc", subKind = "Modbus", part = config.name, desc = "设备不存在"),
      )
      throw ClientDeviceException(ClientDeviceExceptionKind.DeviceNotExist, config.name, null)
    }

    return true
  }

  override fun doDispose() {
    val c = modbusClient
    modbusClient = null
    if (c != null) {
      try {
        c.disconnect().get() // 同步！
      } catch (e: ExecutionException) {
        logger.error(loggerHead() + "销毁时报错", e.cause ?: e)
      } catch (e: Exception) {
        logger.error(loggerHead() + "销毁时报错", e)
      }
    }
  }

  private fun mustGetClient(): ModbusTcpMaster {
    ensureDeviceEnabled()

    return modbusClient ?: throw ClientDeviceException(
      ClientDeviceExceptionKind.ConnectFail,
      "${loggerHead()} No Modbus Client",
      null,
    )
  }

  /**
   * 不管是 bit/boolean 还是 word，都以 int 返回
   * 自动重试；耗时
   * 抛出 DeviceException
   */
  fun readRetry(req: ModbusReadReq): List<Int> = retry(req.maxRetry, req.retryDelay) {
    readOnce(req)
  }

  fun readUtilEq(
    
    req: ModbusReadReq,
    targetValue: Int,
    readDelay: Long = 1000L,
    readLimit: Int = -1, // 读到的值不等于预期值的次数限制
  ): Boolean {
    var retryCount = 0
    while (readLimit <= 0 || retryCount < readLimit) {
      val values = readRetry(req)
      val v = values.firstOrNull()
      if (v == targetValue) return true
      Thread.sleep(readDelay)
      retryCount++
    }
    return false
  }

  /**
   * 只读一次，不推荐直接用。
   * 建议用 readRetry，并将 maxRetry 置为 1
   */
  fun readOnce(req: ModbusReadReq): List<Int> {
    val values = read(req)

    PlcRwLogService.logRead(
      
      config.name,
      "0x${codeToHex2(req.code)}: ${req.address}+${req.qty}",
      values.joinToString(", "),
      "Modbus",
      config.host,
    )

    return values
  }

  @Synchronized
  fun read(req: ModbusReadReq): List<Int> {
    val client = mustGetClient()

    try {
      when (req.code) {
        0x01 -> {
          val rr = ReadCoilsRequest(req.address, req.qty)
          val res = client.sendRequest<ReadCoilsResponse>(rr, req.slaveId)
          return parseBits(res.get(readTimeout, TimeUnit.SECONDS).coilStatus, req.qty)
        }

        0x02 -> {
          val rr = ReadDiscreteInputsRequest(req.address, req.qty)
          val res = client.sendRequest<ReadDiscreteInputsResponse>(rr, req.slaveId)
          return parseBits(res.get(readTimeout, TimeUnit.SECONDS).inputStatus, req.qty)
        }

        0x03 -> {
          val rr = ReadHoldingRegistersRequest(req.address, req.qty)
          val res = client.sendRequest<ReadHoldingRegistersResponse>(rr, req.slaveId)
          return parseWords(res.get(readTimeout, TimeUnit.SECONDS).registers, req.qty)
        }

        0x04 -> {
          val rr = ReadInputRegistersRequest(req.address, req.qty)
          val res = client.sendRequest<ReadInputRegistersResponse>(rr, req.slaveId)
          return parseWords(res.get(readTimeout, TimeUnit.SECONDS).registers, req.qty)
        }

        else -> throw ClientDeviceException(ClientDeviceExceptionKind.ReqError, "${loggerHead()} Bad code ${req.code}")
      }
    } catch (e: ExecutionException) {
      val e2 = e.cause ?: e
      logger.error(loggerHead() + "读取失败 $req", e2)
      throw ClientDeviceException(ClientDeviceExceptionKind.ReadFail, req.toString() + e2.getTypeMessage(), e2)
    } catch (e: CancellationException) {
      throw ClientDeviceException(ClientDeviceExceptionKind.OpCancel, req.toString() + e.getTypeMessage(), null)
    } catch (e: InterruptedException) {
      throw ClientDeviceException(ClientDeviceExceptionKind.OpCancel, req.toString() + e.getTypeMessage(), null)
    } catch (e: TimeoutException) {
      throw ClientDeviceException(ClientDeviceExceptionKind.OpTimeout, req.toString() + e.getTypeMessage(), null)
    }
  }

  /**
   * 小心中断和取消
   * 自动重试；耗时
   * 抛出 DeviceException
   */
  fun writeRetry(req: ModbusWriteReq, values: List<Int>): Int =
    retry(req.maxRetry, req.retryDelay) {
      writeOnce(req, values)
    }

  fun writeOnce(req: ModbusWriteReq, values: List<Int>): Int {
    val v = write(req, values)
    // TODO 写成功了记录还是之前就记录
    PlcRwLogService.logWrite(
      
      config.name,
      "0x${codeToHex2(req.code)}: ${req.address}",
      values.joinToString(", "),
      "",
      "Modbus",
      config.host,
    )
    return v
  }

  @Synchronized
  private fun write(req: ModbusWriteReq, values: List<Int>): Int {
    val client = mustGetClient()

    try {
      return when (req.code) {
        0x05 -> {
          val rr = WriteSingleCoilRequest(req.address, values[0] != 0)
          val res = client.sendRequest<WriteSingleCoilResponse>(rr, req.slaveId)
          res.get(5, TimeUnit.SECONDS).value
        }

        0x06 -> {
          val rr = WriteSingleRegisterRequest(req.address, values[0])
          val res = client.sendRequest<WriteSingleRegisterResponse>(rr, req.slaveId)
          res.get(5, TimeUnit.SECONDS).value
        }

        0x0f -> {
          val bitset = BitSet(values.size)
          for (i in values.indices) bitset.set(i, values[i])
          val rr = WriteMultipleCoilsRequest(req.address, values.size, bitset.toByteArray())
          val res = client.sendRequest<WriteMultipleCoilsResponse>(rr, req.slaveId)
          res.get(5, TimeUnit.SECONDS).quantity
        }

        0x10 -> {
          val buf = Unpooled.buffer(values.size * 2)
          for (v in values) {
            buf.writeShort(v)
          }
          val rr = WriteMultipleRegistersRequest(req.address, values.size, buf)
          val res = client.sendRequest<WriteMultipleRegistersResponse>(rr, req.slaveId)
          res.get(5, TimeUnit.SECONDS).quantity
        }

        else -> throw ClientDeviceException(ClientDeviceExceptionKind.ReqError, "Bad code ${req.code}")
      }
    } catch (e: ExecutionException) {
      val e2 = e.cause ?: e
      logger.error(loggerHead() + "写入失败", e2)
      throw ClientDeviceException(ClientDeviceExceptionKind.WriteFail, e2.message ?: "", e2)
    } catch (e: CancellationException) {
      throw ClientDeviceException(ClientDeviceExceptionKind.OpCancel, "CancellationException", null)
    } catch (e: InterruptedException) {
      throw ClientDeviceException(ClientDeviceExceptionKind.OpCancel, "InterruptedException", null)
    } catch (e: TimeoutException) {
      throw ClientDeviceException(ClientDeviceExceptionKind.OpTimeout, "TimeoutException", null)
    }
  }

  private fun parseBits(buf: ByteBuf, qty: Int): List<Int> {
    val bits: MutableList<Int> = ArrayList(qty)
    while (buf.isReadable) {
      val byte = buf.readByte().toInt()
      for (i in 0..7) {
        bits += (byte shr i) and 1
        if (bits.size >= qty) return bits
      }
    }
    buf.release()
    return bits
  }

  private fun parseWords(buf: ByteBuf, qty: Int): List<Int> {
    val words: MutableList<Int> = ArrayList(qty)
    for (i in 1..qty) {
      val w = buf.readShort()
      words.add(w.toInt())
    }
    buf.release()
    return words
  }

  private fun codeToHex2(code: Int): String = code.toString(16).padStart(2, '0')
}

data class ModbusConfig(
  override val name: String,
  override val disabled: Boolean = false,
  override val autoRetry: Boolean = false,
  override val timeout: Int,
  override val maxRetry: Int = -1,
  override val retryDelay: Long = 3000,
  val host: String,
  val port: Int,
) : ClientDeviceConfig

data class ModbusReadReq(
  val code: Int,
  val address: Int,
  val qty: Int,
  val slaveId: Int = 0,
  val maxRetry: Int? = null, // = -1,
  val retryDelay: Long? = null, // = 1000,
)

data class ModbusWriteReq(
  val code: Int,
  val address: Int,
  val slaveId: Int = 0,
  val maxRetry: Int? = null, // = -1,
  val retryDelay: Long? = null, // = 1000,
)