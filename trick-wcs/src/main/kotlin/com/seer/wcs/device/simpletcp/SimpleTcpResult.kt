package com.seer.wcs.device.simpletcp

/**
 * 表示 TCP 请求结果的数据类。
 * 包含请求结果的类型、服务器 IP 地址、端口号、请求的十六进制字符串表示、响应的十六进制字符串表示以及错误信息。
 * 用于与 TCP 服务器进行通信后，返回请求的处理结果状态和相关数据。
 */
data class SimpleTcpResult(
  @JvmField
  val kind: SimpleTcpResultKind, // 请求结果的类型。
  @JvmField
  val ip: String? = null, // 服务器 IP 地址。
  @JvmField
  val port: Int? = null, // 服务器端口号。
  @JvmField
  val reqHexString: String, // 请求的十六进制字符串表示。
  @JvmField
  val resHexString: String? = null, // 响应的十六进制字符串表示。
  @JvmField
  val errMsg: String? = null, // 错误信息。
) {
  constructor(kind: SimpleTcpResultKind) : this(kind = kind, reqHexString = "")
}

enum class SimpleTcpResultKind {
  Ok, // 请求成功。
  GetTcpNameError, // 获取 TCP 名称错误。
  ConnectFail, // 连接失败。
  WriteError, // 写错误。
  Disposed, // 已被销毁。
  Timeout, // 超时。
  Interrupted, // 被中断。
}
