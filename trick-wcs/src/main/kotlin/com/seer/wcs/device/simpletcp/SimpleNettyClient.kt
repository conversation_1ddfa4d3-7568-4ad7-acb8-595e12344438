package com.seer.wcs.device.simpletcp

import com.seer.trick.base.soc.SocService
import io.netty.buffer.ByteBufUtil
import io.netty.buffer.Unpooled
import io.netty.channel.ChannelFuture
import io.netty.channel.EventLoopGroup
import java.util.concurrent.TimeUnit

/**
 * 用于与服务器进行简单的 TCP 通信，通过给定的名称、事件循环组和通道未来进行初始化。
 */
class SimpleNettyClient(
  private val name: String,
  private val group: EventLoopGroup,
  private val channelFuture: ChannelFuture,
) {
  /**
   * 向服务器写入数据的方法。
   */
  fun write(byteArray: ByteArray) {
    val bf = Unpooled.wrappedBuffer(byteArray)
    val bfStr = ByteBufUtil.prettyHexDump(bf)
    SocService.updateNode("Simple Tcp", "WriteTcp:$name", "写 Simple TCP:$name", "开始。请求=$bfStr")
    val r = channelFuture.channel().writeAndFlush(bf).await(5000, TimeUnit.MILLISECONDS)
    SocService.updateNode("Simple Tcp", "WriteTcp:$name", "写 Simple TCP:$name", "结束。请求=$bfStr，响应=$r")
  }

  /**
   * 关闭客户端连接的方法。
   */
  fun close() {
    try {
      channelFuture.channel().close()
      channelFuture.channel().closeFuture().sync()
    } catch (e: InterruptedException) {
      // ignore
    } finally {
      try {
        group.shutdownGracefully()
      } catch (e: InterruptedException) {
        // ignore
      }
    }
  }
}
