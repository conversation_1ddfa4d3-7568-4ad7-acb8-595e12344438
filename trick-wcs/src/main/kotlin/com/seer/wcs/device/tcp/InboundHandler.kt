package com.seer.wcs.device.tcp

import com.seer.trick.BzError

import io.netty.channel.ChannelHandlerContext
import io.netty.channel.SimpleChannelInboundHandler
import org.slf4j.LoggerFactory
import java.net.InetSocketAddress

class InboundHandler<T : Any>(
  private val onMessage: MsgCallback<T>,
  private val onError: (e: Throwable) -> Unit,
  private val onRemoved: CtxRemovedCallback? = null,
) : SimpleChannelInboundHandler<Any>() {
  private val logger = LoggerFactory.getLogger(this::class.java)

  override fun channelRead0(channelHandlerContext: ChannelHandlerContext, msg: Any) {
    @Suppress("UNCHECKED_CAST")
    onMessage.callback(channelHandlerContext, msg as T)
  }

  override fun handlerRemoved(ctx: ChannelHandlerContext?) {
    

    if (ctx != null) {
      val address = ctx.channel().remoteAddress() as InetSocketAddress
      logger.debug("M4 TCP client disconnected, client ip=${address.address}, client port=${address.port}}")

      if (onRemoved != null) {
        onRemoved.callback(ctx)
        return
      }
    }

    onError(BzError("errCodeErr", "NettyHandlerRemoved"))
  }

  override fun exceptionCaught(ctx: ChannelHandlerContext, cause: Throwable) {
    try {
      ctx.close()
    } catch (e: Exception) {
      //
    }
    onError(cause)
  }
}