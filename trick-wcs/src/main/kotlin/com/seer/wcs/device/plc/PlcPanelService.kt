package com.seer.wcs.device.plc

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.github.xingshuangs.iot.common.enums.EDataType
import com.seer.trick.BzError

import com.seer.trick.base.BaseCenter
import com.seer.trick.base.concurrent.BaseConcurrentCenter.highTimeSensitiveExecutor
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.submitCatch
import com.seer.wcs.device.plc.modbus.ModbusReadReq
import com.seer.wcs.device.plc.modbus.ModbusWriteReq
import com.seer.wcs.device.plc.s7.S7ReadReq
import com.seer.wcs.device.plc.s7.S7WriteReq
import org.apache.commons.io.FileUtils
import org.slf4j.LoggerFactory
import java.io.File
import java.nio.charset.StandardCharsets
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.Executors
import java.util.concurrent.Future
import java.util.concurrent.TimeUnit

object PlcPanelService {
  
  private val logger = LoggerFactory.getLogger(this::class.java)
  
  @Volatile
  var configStr = "{}"
  
  @Volatile
  var config = PlcPanelConfigs()
  
  val value: MutableList<PanelValue> = CopyOnWriteArrayList() // panel -> field -> points
  
  private val executor = Executors.newScheduledThreadPool(10)
  
  @Volatile
  private var futures: List<Future<*>> = emptyList()
  
  @Synchronized
  fun init() {
    loadConfig()
    
    for (f in futures) {
      f.cancel(true)
    }
    
    // 准备值
    value.clear()
    for (pc in config.panels) {
      val panelValue: PanelValue = CopyOnWriteArrayList()
      value.add(panelValue)
      for (field in pc.fields) {
        val fieldValue: FieldValue = CopyOnWriteArrayList()
        panelValue.add(fieldValue)
        for (point in field.points) {
          fieldValue.add(null)
        }
      }
    }
    
    val futures: MutableList<Future<*>> = ArrayList()
    for ((pi, pc) in config.panels.withIndex()) {
      if (pc.disabled) continue
      
      for ((fi, field) in pc.fields.withIndex()) {
        if (field.disabled) continue
        
        var delay = field.refreshDelay ?: 1000
        if (delay <= 0) delay = 1000
        
        futures += executor.scheduleWithFixedDelay({
          
          val fieldValue = value.getOrNull(pi)?.getOrNull(fi) ?: return@scheduleWithFixedDelay
          
          for ((pti, point) in field.points.withIndex()) {
            if (field.deviceName.isBlank()) {
              fieldValue[pti] = PointValue(true, null, "No device name")
            } else if (field.deviceType == "Modbus") {
              try {
                val client = PlcCenter.mustGetModbusClient(field.deviceName)
                val req = point.toModbusReadReq().copy(maxRetry = 1) // 仅使用 readRetry 的故障处理，限制重试次数为 1
                val values = client.readRetry(req)
                fieldValue[pti] = PointValue(false, values)
              } catch (e: Exception) {
                fieldValue[pti] = PointValue(true, null, e.message)
              }
            } else if (field.deviceType == "S7") {
              try {
                val client = PlcCenter.mustGetS7Client(field.deviceName)
                val req = point.toS7ReadReq().copy(maxRetry = 1) // 仅使用 readRetry 的故障处理，限制重试次数为 1
                val values = client.readRetry(req)
                fieldValue[pti] = PointValue(false, listOf(values))
              } catch (e: Exception) {
                fieldValue[pti] = PointValue(true, null, e.message)
              }
            }
          }
        }, 3 * 1000, delay, TimeUnit.MILLISECONDS)
      }
    }
    this.futures = futures
  }
  
  private fun loadConfig() {
    val file = getPanelConfigFile()
    if (file.exists()) {
      configStr = FileUtils.readFileToString(file, StandardCharsets.UTF_8)
      config = JsonHelper.mapper.readValue(configStr, jacksonTypeRef())
    } else {
      configStr = "{}"
      config = PlcPanelConfigs()
    }
  }
  
  fun update(configStr: String) {
    logger.info("更新 PLC 面板配置")
    val file = getPanelConfigFile()
    FileUtils.write(file, configStr, StandardCharsets.UTF_8)
    highTimeSensitiveExecutor.submitCatch("update plc panel", logger, ::init)
  }
  
  private fun getPanelConfigFile(): File = File(BaseCenter.baseConfig.configDir, "plc-panel.json")
  
  fun callButton(pi: Int, bi: Int) {
    val button = config.panels.getOrNull(pi)?.buttons?.getOrNull(bi) ?: return
    if (button.deviceName.isBlank() || button.deviceType.isBlank()) return
    
    // val plcConfig = EntityRwService.findOneById("PlcDeviceConfig", button.deviceName)
    //   ?: throw BzError("errNoDevice", button.deviceName)
    // if (plcConfig["disabled"] == true) throw BzError("errDeviceDisabled", button.deviceName)
    
    for (cmd in button.commands) {
      if (button.deviceType == "Modbus") {
        val client = PlcCenter.mustGetModbusClient(button.deviceName)
        client.ensureDeviceExist()
        client.ensureDeviceEnabled()
        client.writeOnce(cmd.toModbusWriteReq(), cmd.values)
      } else if (button.deviceType == "S7") {
        val client = PlcCenter.mustGetS7Client(button.deviceName)
        client.ensureDeviceExist()
        client.ensureDeviceEnabled()
        client.writeOnce(cmd.toS7WriteReq())
      }
    }
  }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class PlcPanelConfigs(val panels: List<PlcPanelConfig> = emptyList())

data class PlcPanelConfig(
  // val label: String,
  val disabled: Boolean = false,
  // displayOrder?: number
  // bgColor?: string
  val fields: List<PlcPanelField>,
  val buttons: List<PlcPanelButton>,
)

data class PlcPanelField(
  // label: string
  val deviceName: String = "",
  val deviceType: String = "",
  val disabled: Boolean = false,
  // asSwitch?: boolean
  // optionBill?: InlineOptionBill
  val refreshDelay: Long? = null,
  // valueJs?: string
  val points: List<PlcPoint>,
)

data class PlcPoint(
  // Modbus
  val code: Int? = null, // modbus
  val address: Int? = null, // modbus
  val qty: Int = 1, // modbus
  val slaveId: Int = 0, // modbus
  
  // S7
  val blockType: String = "DB", // DB, Q, I, M, V
  val dataType: EDataType? = null,
  val dbId: Int = 0,
  val byteOffset: Int = 0,
  val bitOffset: Int = 0,
) {
  
  fun toModbusReadReq(): ModbusReadReq {
    if (code == null) throw BzError("errPlcPoint", "未配置 Modbus 功能码")
    if (address == null) throw BzError("errPlcPoint", "未配置 Modbus 地址")
    return ModbusReadReq(code, address, qty, slaveId)
  }
  
  fun toS7ReadReq(): S7ReadReq {
    if (dataType == null) throw BzError("errPlcPoint", "未配置 S7 数据类型")
    return S7ReadReq(blockType, dataType, dbId, byteOffset, bitOffset)
  }
}

data class PlcPanelButton(
  //   label: string
  //   disabled?: boolean
  val deviceName: String = "",
  val deviceType: String = "",
  //   kind?: "primary" | "primary-light" | "success" | "warning" | "error"
  //   confirmText?: string
  val commands: List<PlcCommand>,
)

data class PlcCommand(
  // Modbus
  val code: Int? = null, // modbus
  val address: Int? = null, // modbus
  val values: List<Int>,
  val slaveId: Int = 0, // modbus
  
  // S7
  val blockType: String = "DB", // DB, Q, I, M, V
  val dataType: EDataType? = null,
  val dbId: Int = 0,
  val byteOffset: Int = 0,
  val bitOffset: Int = 0,
  val s7Value: Any = Object(),
) {
  
  fun toModbusWriteReq(): ModbusWriteReq {
    if (code == null) throw BzError("errPlcPoint", "未配置 Modbus 功能码")
    if (address == null) throw BzError("errPlcPoint", "未配置 Modbus 地址")
    return ModbusWriteReq(code, address, slaveId)
  }
  
  fun toS7WriteReq(): S7WriteReq {
    if (dataType == null) throw BzError("errPlcPoint", "未配置 S7 数据类型")
    return S7WriteReq(
      blockType,
      dataType,
      dbId,
      byteOffset,
      bitOffset,
      value = s7Value,
    )
  }
}

data class PointValue(val error: Boolean = false, val values: List<Any?>? = null, val errMsg: String? = null)

typealias FieldValue = MutableList<PointValue?>

typealias PanelValue = MutableList<FieldValue>