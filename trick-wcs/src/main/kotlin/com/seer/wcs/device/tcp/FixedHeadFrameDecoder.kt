package com.seer.wcs.device.tcp

import com.seer.trick.base.config.BzConfigManager
import io.netty.buffer.ByteBuf
import io.netty.buffer.ByteBufUtil
import io.netty.channel.ChannelHandlerContext
import io.netty.handler.codec.ByteToMessageDecoder
import org.slf4j.LoggerFactory

class FixedHeadFrameSchema<T : Any>(
  val start: ByteArray,
  val headLength: Int, // 算 start
  val getBodyLength: (head: ByteBuf) -> Int, // head 含有 start
  // NOTE: 需要手动 release head、body
  val parse: (head: ByteBuf, body: ByteBuf) -> T // head 含有 start
)

class FixedHeadFrameDecoder<T : Any>(private val schema: FixedHeadFrameSchema<T>) : ByteToMessageDecoder() {
  
  private val logger = LoggerFactory.getLogger(this::class.java)
  
  private val startLength = schema.start.size
  
  @Volatile
  private var started = false
  
  @Volatile
  private var headOk = false
  
  @Volatile
  private var head: ByteBuf? = null
  
  @Volatile
  private var bodyLength = -1
  
  override fun decode(ctx: ChannelHandlerContext, buf: ByteBuf, out: MutableList<Any>) {
    val dump = BzConfigManager.getByPath("ScDev", "fixedHeadFrameDecoderDump") == true
    if (dump) logger.debug(ByteBufUtil.prettyHexDump(buf))
    
    if (!started) {
      while (buf.readableBytes() > startLength) {
        if (isStart(buf)) {
          started = true
          // buf.readerIndex(buf.readerIndex() + startLength)
          break
        } else {
          buf.readerIndex(buf.readerIndex() + 1)
        }
        
      }
    }
    if (!started) {
      if (dump) logger.warn("err start")
      return
    }
    
    if (!headOk) {
      if (buf.readableBytes() >= schema.headLength) {
        head = buf.readBytes(schema.headLength)
        bodyLength = schema.getBodyLength(head!!)
        headOk = true
      }
    }
    if (!headOk) {
      if (dump) logger.warn("head not ok")
      return
    }
    
    if (buf.readableBytes() < bodyLength) {
      if (dump) logger.warn("lack body")
      return
    }
    val body = buf.readBytes(bodyLength)
    
    val frame = schema.parse(head!!, body)
    out.add(frame as Any)
    
    started = false
    headOk = false
    head = null
    bodyLength = -1
  }
  
  private fun isStart(buf: ByteBuf): Boolean {
    for (i in 0 until startLength) {
      if (buf.getByte(buf.readerIndex() + i) != schema.start[i]) return false
    }
    return true
  }
  
}