package com.seer.wcs.device.simpletcp

import io.netty.buffer.ByteBuf
import io.netty.channel.ChannelHandlerContext
import io.netty.handler.codec.ByteToMessageDecoder

/**
 * 简单数据解码器类。该类继承自 ByteToMessageDecoder。
 */
class SimpleDataDecoder : ByteToMessageDecoder() {
  /**
   * 解码，用于将输入的 ByteBuf 数据解码为字节数组。
   */
  override fun decode(ctx: ChannelHandlerContext, inBuf: ByteBuf, out: MutableList<Any>) {
    val byteArray = ByteArray(inBuf.readableBytes())
    inBuf.readBytes(byteArray)
    out.add(byteArray)
  }
}
