package com.seer.wcs.device.simpletcp

import com.seer.trick.BzError
import com.seer.trick.base.config.BzConfigManager
import java.util.concurrent.ConcurrentHashMap

object SimpleTcpCenter {
  // 存储简单 TCP 客户端的 map
  private val simpleTcpClients: MutableMap<String, SimpleTcpClient> = ConcurrentHashMap()

  /**
   * 获取指定名称的简单 TCP 客户端。如果不存在则创建一个新的客户端并添加到 map 中。
   */
  fun mustGetSimpleTcpClient(name: String): SimpleTcpClient {
    val serverHost = BzConfigManager.getByPath("ScWcs", "tcp", name, "host")
    val serverPort = BzConfigManager.getByPath("ScWcs", "tcp", name, "port")
    if (simpleTcpClients[name] == null) {
      simpleTcpClients[name] = SimpleTcpClient(serverHost.toString(), serverPort as Int)
    }
    return simpleTcpClients[name] ?: throw BzError("errNoTcp", name)
  }
}
