package com.seer.wcs.device.plc.s7

import com.github.xingshuangs.iot.common.enums.EDataType
import com.github.xingshuangs.iot.exceptions.S7CommException
import com.github.xingshuangs.iot.protocol.s7.enums.EPlcType
import com.github.xingshuangs.iot.protocol.s7.service.S7PLC

import com.seer.trick.base.failure.FailureRecordReq
import com.seer.trick.base.failure.FailureRecorder
import com.seer.trick.helper.NumHelper
import com.seer.wcs.device.*
import com.seer.wcs.device.plc.PlcCenter
import com.seer.wcs.device.plc.PlcRwLogService

class S7Client(config: S7Config) : ClientDevice<S7Config>(config, "Plc", "S7") {

  private var s7Client: S7PLC? = null

  override fun doConnect() {
    try {
      val c = S7PLC(EPlcType.valueOf(config.plcType), config.host, config.port, config.rack, config.slot)
      s7Client = c
    } catch (e: Exception) {
      logger.error(loggerHead() + "连接失败", e)
      throw ClientDeviceException(ClientDeviceExceptionKind.ConnectFail, "UnknownException", e)
    }
  }

  public override fun ensureDeviceExist(): Boolean {
    if (!PlcCenter.existS7Client(config.name)) {
      FailureRecorder.addAsync(
        
        FailureRecordReq(kind = "Plc", subKind = "S7", part = config.name, desc = "设备不存在"),
      )
      throw ClientDeviceException(ClientDeviceExceptionKind.DeviceNotExist, config.name, null)
    }

    return true
  }

  override fun doDispose() {
    try {
      s7Client?.close()
      s7Client = null
    } catch (e: Exception) {
      logger.error(loggerHead() + "销毁时报错", e)
    }
  }

  private fun mustGetClient(): S7PLC {
    ensureDeviceEnabled()

    return s7Client ?: throw ClientDeviceException(ClientDeviceExceptionKind.ConnectFail, "No S7 Client", null)
  }

  fun readRetry(req: S7ReadReq): Any? = retry(req.maxRetry, req.retryDelay) {
    readOnce(req)
  }

  fun readUntilEq(
    
    req: S7ReadReq,
    targetValue: Any,
    readDelay: Long = 1000L,
    readLimit: Int = -1, // 读到的值不等于预期值的次数限制
  ): Boolean {
    var retryCount = 0
    while (readLimit <= 0 || retryCount < readLimit) {
      val value = readRetry(req) ?: ""
      if (value.toString() == targetValue.toString()) return true
      Thread.sleep(readDelay)
      retryCount++
    }
    return false
  }

  fun readOnce(req: S7ReadReq): Any? {
    try {
      val r = read(req)
      PlcRwLogService.logRead(
        
        config.name,
        "Read ${req.blockType}.${req.dbId}.${req.byteOffset}.${req.bitOffset}",
        "$r (${req.dataType})",
        "S7",
        config.host,
      )
      status = ClientDeviceConnectStatus.Connected
      return r
    } catch (e: ClientDeviceException) {
      throw e
    } catch (e: S7CommException) {
      throw ClientDeviceException(ClientDeviceExceptionKind.ConnectFail, req.toString() + e.message, e)
    } catch (e: Exception) {
      throw ClientDeviceException(ClientDeviceExceptionKind.ReadFail, req.toString() + e.message, e)
    }
  }

  private fun read(req: S7ReadReq): Any? {
    when (req.dataType) {
      EDataType.BOOL -> {
        return mustGetClient().readBoolean("${req.blockType}${req.dbId}.${req.byteOffset}.${req.bitOffset}")
      }

      EDataType.BYTE -> {
        return mustGetClient().readByte(req.blockType + req.dbId + "." + req.byteOffset)
      }

      EDataType.INT16 -> {
        return mustGetClient().readInt16(req.blockType + req.dbId + "." + req.byteOffset)
      }

      EDataType.UINT16 -> {
        return mustGetClient().readUInt16("${req.blockType}${req.dbId}.${req.byteOffset}")
      }

      EDataType.INT32 -> {
        return mustGetClient().readInt32("${req.blockType}${req.dbId}.${req.byteOffset}")
      }

      EDataType.UINT32 -> {
        return mustGetClient().readUInt32("${req.blockType}${req.dbId}.${req.byteOffset}")
      }

      EDataType.FLOAT32 -> {
        return mustGetClient().readFloat32(req.blockType + req.dbId + "." + req.byteOffset)
      }

      EDataType.FLOAT64 -> {
        return mustGetClient().readFloat64(req.blockType + req.dbId + "." + req.byteOffset)
      }

      EDataType.STRING -> {
        return mustGetClient().readString(req.blockType + req.dbId + "." + req.byteOffset)
      }

      else -> {
        throw ClientDeviceException(ClientDeviceExceptionKind.ReqError, "Bad data type ${req.dataType}")
      }
    }
  }

  fun writeRetry(req: S7WriteReq) = retry(req.maxRetry, req.retryDelay) {
    writeOnce(req)
    logger.debug("写 S7 成功，请求=$req，值=${req.value}")
  }

  fun writeOnce(req: S7WriteReq) {
    try {
      when (req.dataType) {
        EDataType.BOOL -> {
          mustGetClient().writeBoolean(
            "${req.blockType}${req.dbId}.${req.byteOffset}.${req.bitOffset}",
            req.value as Boolean,
          )
        }

        EDataType.BYTE -> {
          mustGetClient().writeByte(
            req.blockType + req.dbId + "." + req.byteOffset,
            NumHelper.anyToByte(req.value) ?: 0,
          )
        }

        EDataType.INT16 -> {
          mustGetClient().writeInt16(
            req.blockType + req.dbId + "." + req.byteOffset,
            NumHelper.anyToShort(req.value) ?: 0,
          )
        }

        EDataType.UINT16 -> {
          mustGetClient().writeUInt16(
            "${req.blockType}${req.dbId}.${req.byteOffset}",
            NumHelper.anyToInt(req.value) ?: 0,
          )
        }

        EDataType.INT32 -> {
          mustGetClient().writeInt32(
            "${req.blockType}${req.dbId}.${req.byteOffset}",
            NumHelper.anyToInt(req.value) ?: 0,
          )
        }

        EDataType.UINT32 -> {
          mustGetClient().writeUInt32(
            "${req.blockType}${req.dbId}.${req.byteOffset}",
            NumHelper.anyToLong(req.value) ?: 0,
          )
        }

        EDataType.FLOAT32 -> {
          mustGetClient().writeFloat32(
            req.blockType + req.dbId + "." + req.byteOffset,
            NumHelper.anyToFloat(req.value) ?: 0F,
          )
        }

        EDataType.FLOAT64 -> {
          mustGetClient().writeFloat64(
            req.blockType + req.dbId + "." + req.byteOffset,
            NumHelper.anyToDouble(req.value) ?: 0.0,
          )
        }

        EDataType.STRING -> {
          mustGetClient().writeString(req.blockType + req.dbId + "." + req.byteOffset, req.value as String)
        }

        else -> {
          throw ClientDeviceException(ClientDeviceExceptionKind.ReqError, "Bad data type ${req.dataType}")
        }
      }
      PlcRwLogService.logWrite(
        
        config.name,
        "Write ${req.blockType}.${req.dbId}.${req.byteOffset}.${req.bitOffset}",
        "${req.value} (${req.dataType})",
        "",
        "S7",
        config.host,
      )
      status = ClientDeviceConnectStatus.Connected
    } catch (e: S7CommException) {
      throw ClientDeviceException(ClientDeviceExceptionKind.ConnectFail, req.toString() + e.message, e)
    } catch (e: Throwable) {
      throw ClientDeviceException(ClientDeviceExceptionKind.WriteFail, req.toString() + e.message, e)
    }
  }
}

data class S7Config(
  override val name: String,
  override val disabled: Boolean,
  override val autoRetry: Boolean,
  override val timeout: Int,
  override val maxRetry: Int = -1,
  override val retryDelay: Long = 3000,
  val plcType: String = "S1200",
  val host: String,
  val port: Int,
  val rack: Int,
  val slot: Int,
) : ClientDeviceConfig

data class S7ReadReq(
  val blockType: String = "DB", // DB, Q, I, M, V
  val dataType: EDataType,
  val dbId: Int,
  val byteOffset: Int = 0,
  val bitOffset: Int = 0,
  // val length: String,
  val maxRetry: Int? = null,
  val retryDelay: Long? = null,
)

data class S7WriteReq(
  val blockType: String, // DB, Q, I, M, V
  val dataType: EDataType,
  val dbId: Int,
  val byteOffset: Int = 0,
  val bitOffset: Int = 0,
  // val length: String,
  val maxRetry: Int? = null,
  val retryDelay: Long? = null,
  val value: Any = Object(),
)