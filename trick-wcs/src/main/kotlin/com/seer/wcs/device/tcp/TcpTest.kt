package com.seer.wcs.device.tcp

object TcpTest {

  // private val logger = LoggerFactory.getLogger(this::class.java)
  //
  // val start = byteArrayOf(0x10, 0x20)
  // val schema = FixedHeadFrameSchema(
  //   start,
  //   8,
  //   { buf -> buf.getInt(4) }) { head, body ->
  //   val apiNo = head.readInt()
  //   val msg = body.toString(StandardCharsets.UTF_8)
  //   Frame(apiNo, msg)
  // }
  // val port = 9765
  //
  // fun test() {
  //   TcpServer("测试用", port, schema, false) { ctx: ChannelHandlerContext, msg: Frame ->
  //     logger.info("Server: $msg")
  //     ctx.write(buildReqBytes(1, "HelloGot"))
  //     ctx.flush()
  //   }
  //
  //   val client = TcpClient("127.0.0.1", port, schema, false) { _, msg ->
  //     logger.info("Client: $msg")
  //   }
  //   for (i in 0..9) {
  //     client.write(buildReqBytes(i + 1, "Hello-" + i.toString().padStart(i, '0')))
  //     logger.info("Client written")
  //     Thread.sleep(1000)
  //   }
  // }
  //
  // fun buildReqBytes(apiNo: Int, msg: String): CompositeByteBuf {
  //   val bodyBytes = msg.toByteArray(StandardCharsets.UTF_8)
  //   val bodyBuf: ByteBuf = Unpooled.copiedBuffer(bodyBytes)
  //
  //   val headBuf: ByteBuf = Unpooled.buffer(RbkDecoder.HEAD_SIZE)
  //   headBuf.writeBytes(start)
  //   headBuf.writeInt(apiNo)
  //   headBuf.writeInt(bodyBytes.size)
  //
  //   val reqBuf: CompositeByteBuf = Unpooled.compositeBuffer()
  //   reqBuf.addComponent(true, headBuf)
  //   reqBuf.addComponent(true, bodyBuf)
  //   return reqBuf
  // }

  data class Frame(
    val apiNo: Int,
    val msg: String
  )
  
}
