package com.seer.wcs.device

import com.seer.trick.BzError

import com.seer.trick.base.SysEmc.isSysEmc
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.failure.FailureRecordReq
import com.seer.trick.base.failure.FailureRecorder
import com.seer.trick.base.soc.SocAttention
import com.seer.trick.base.soc.SocService
import com.seer.trick.helper.getTypeMessage
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

/**
 * 基本的连接、断开功能；连接状态管理
 * 更新配置功能
 * 管理自动连接
 * 提供操作（读写）自动重试
 * 在 destroy 前可以反复 init/dispose
 */
abstract class ClientDevice<CT : ClientDeviceConfig>(
  @Volatile protected var config: CT,
  private val type: String,
  private val subType: String,
) {

  protected val logger: Logger = LoggerFactory.getLogger(this::class.java)

  protected fun loggerHead() = "${config.name}："

  @Volatile
  var status: ClientDeviceConnectStatus = ClientDeviceConnectStatus.Init
    protected set
  private var initRetryCount = 1
  private var initLastError: String? = null

  // 自动重连计数器
  private var reconnectCount = 0

  private val connectingTimer = Executors.newSingleThreadScheduledExecutor()

  init {
    connectingTimer.scheduleWithFixedDelay(::retryConnect, 1000, 1000, TimeUnit.MILLISECONDS)
  }

  /**
   * reconnectCount % 30 == 0 时，强制重连（forced = true），否则只进行故障重连
   */
  private fun retryConnect() {
    reconnectCount++

    if (reconnectCount % 30 == 0) {
      init(true) // 强制重连
    } else {
      init(false)
    }

    reconnectCount %= 30
  }

  /**
   * 销毁后不能再使用此实例
   */
  fun destroy() {
    logger.info(loggerHead() + "Destroy start")

    connectingTimer.shutdownNow()

    dispose("Destroy")

    removeSocConnectRecord()

    logger.debug(loggerHead() + "Destroy finished")
  }

  /**
   * 耗时
   * 不抛异常
   */
  @Synchronized
  fun init(forced: Boolean = false) {
    // 不强制重连 且 状态是连接中/已连接，就 return
    if (!forced && (status == ClientDeviceConnectStatus.Connecting || status == ClientDeviceConnectStatus.Connected)) {
      return
    }

    // 被禁用
    if (isDisabled()) {
      if (shouldLog()) logger.debug(loggerHead() + "设备禁用，断开连接")
      dispose("Disabled", 0)
      status = ClientDeviceConnectStatus.Disabled
      updateSocConnectRecord()
      return
    }

    // 被删除的话，应该关闭 connectingTimer，init() 就不在执行了。这里也无需处理

    // 如果已连接，则先断开原本的连接
    if (status == ClientDeviceConnectStatus.Connected) {
      dispose("ForcedInit")
    }

    status = ClientDeviceConnectStatus.Connecting
    initLastError = null
    updateSocConnectRecord()
    try {
      if (shouldLog()) logger.debug(loggerHead() + "开始链接")
      doConnect()
      logger.debug(loggerHead() + "已连接")

      // S7Client 用的库在首次读写时才建立 Socket（调用 getAvailableSocket() ）
      // com.github.xingshuangs.iot.net.client.TcpClientBasic.write(byte[], int, int, int)
      // com.github.xingshuangs.iot.net.client.TcpClientBasic.read(byte[], int, int, int, int, boolean)
      if (subType != "S7") status = ClientDeviceConnectStatus.Connected

      initRetryCount = 1
      initLastError = null
      updateSocConnectRecord()
      return
    } catch (e: ClientDeviceException) {
      if (shouldLog()) logger.debug(loggerHead() + "Connect failed, e.kind=${e.kind}, e.message=${e.message}")
      status = ClientDeviceConnectStatus.ConnectFail
      initLastError = e.message
      updateSocConnectRecord()
      ++initRetryCount

      FailureRecorder.addAsync(
        
        FailureRecordReq(
          kind = type,
          subKind = subType,
          part = config.name,
          desc = "连接失败。类型=${e.kind}。详情=${e.message}",
        ),
      )
    }
  }

  /**
   * 所有异常包成 DeviceException
   */
  protected abstract fun doConnect()

  /**
   * 确保设备在 PlcCenter 中是否存在
   */
  protected abstract fun ensureDeviceExist(): Boolean

  fun ensureDeviceEnabled() {
    if (isDisabled()) {
      FailureRecorder.addAsync(
        
        FailureRecordReq(kind = type, subKind = subType, part = config.name, desc = "设备被禁用"),
      )
      throw ClientDeviceException(ClientDeviceExceptionKind.DeviceDisabled, config.name)
    }
  }

  // 设备被禁用
  private fun isDisabled(): Boolean = config.disabled

  /**
   * 同步清理
   * TODO 会不会卡在销毁上；销毁超时
   *
   * 如果要销毁此 client，应调用 destroy
   */
  @Synchronized
  fun dispose(reason: String, retryCount: Int? = null) {
    if (retryCount == null || retryCount % 60 == 1 || shouldLog()) {
      logger.debug(loggerHead() + "开始 dispose 清理。原因=$reason")
    }

    if (status == ClientDeviceConnectStatus.Disabled || status == ClientDeviceConnectStatus.Disposing) return

    status = ClientDeviceConnectStatus.Disposing
    updateSocConnectRecord()

    doDispose()

    status = ClientDeviceConnectStatus.Disposed
    updateSocConnectRecord()
    // logger.debug(loggerHead() + "dispose 已清理。$reason")
  }

  // 打印日志配置
  private fun shouldLog() = BzConfigManager.getByPath("ScDev", "logDeviceClient") == true

  protected abstract fun doDispose()

  /**
   * 耗时
   */
  @Synchronized
  fun updateConfig(config: CT) {
    dispose("更新配置")
    this.config = config
    init(true)
  }

  protected fun <RT> retry(maxRetry: Int?, retryDelay: Long?, op: () -> RT): RT {
    ensureConnected(maxRetry, retryDelay)

    var retryCount = 1
    while (!Thread.interrupted()) {
      ensureDeviceExist()
      // op() 里一定调用 mustGetClient(), 里面已经调用了 ensureDeviceEnabled()，此处不重复调用

      try {
        if (!isSysEmc()) {
          // 系统软急停时，暂停重试
          return op()
        }
      } catch (e: Exception) {
        FailureRecorder.addAsync(
          
          FailureRecordReq(
            kind = type,
            subKind = subType,
            part = config.name,
            desc = "retry 中失败。原因=${e.getTypeMessage()}",
          ),
        )
        dispose("retry 中失败，第 $retryCount 次。原因=${e.getTypeMessage()}", retryCount) // TODO 异步？
        retryCount++
        if (e is ClientDeviceException &&
          (e.kind == ClientDeviceExceptionKind.OpCancel || e.kind == ClientDeviceExceptionKind.DeviceDisabled)
        ) {
          throw e
        }
        if (getMaxRetry(maxRetry) > 0 && retryCount > getMaxRetry(maxRetry)) throw e
      } finally {
        updateSocConnectRecord()
      }
      try {
        Thread.sleep(getRetryDelay(retryDelay))
      } catch (e: InterruptedException) {
        FailureRecorder.addAsync(
          
          FailureRecordReq(kind = type, subKind = subType, part = config.name, desc = "sleep 时，线程被打断"),
        )
        throw ClientDeviceException(ClientDeviceExceptionKind.OpCancel, "${loggerHead()} InterruptedException")
      }
    }
    FailureRecorder.addAsync(
      
      FailureRecordReq(kind = type, subKind = subType, part = config.name, desc = "retry 时，线程被打断"),
    )
    throw ClientDeviceException(ClientDeviceExceptionKind.OpCancel, "${loggerHead()} Interrupted")
  }

  private fun ensureConnected(maxRetry: Int?, retryDelay: Long?) {
    var c = 1
    while (!Thread.interrupted()) {
      try {
        if (status == ClientDeviceConnectStatus.Connected && subType != "S7") break
        if ((status == ClientDeviceConnectStatus.Connected || status == ClientDeviceConnectStatus.Connecting) &&
          subType == "S7"
        ) {
          break
        }
        ensureDeviceExist()
        ensureDeviceEnabled()

        Thread.sleep(100)
        c++
        if (getMaxRetry(maxRetry) > 0 && c > getMaxRetry(maxRetry) * getRetryDelay(retryDelay) / 100) {
          throw ClientDeviceException(ClientDeviceExceptionKind.ConnectFail, config.name)
        }
      } catch (e: InterruptedException) {
        FailureRecorder.addAsync(
          
          FailureRecordReq(kind = type, subKind = subType, part = config.name, desc = "被打断"),
        )
        throw ClientDeviceException(
          ClientDeviceExceptionKind.OpCancel,
          "${loggerHead()} InterruptedConnectException",
          e,
        )
      }
    }
  }

  private fun getMaxRetry(reqMaxRetry: Int?): Int = reqMaxRetry ?: config.maxRetry

  private fun getRetryDelay(retryDelay: Long?): Long = retryDelay ?: config.retryDelay

  private fun updateSocConnectRecord() {
    val r = ClientDeviceConnectRecord(status, initRetryCount, initLastError)
    val attention = when (status) {
      ClientDeviceConnectStatus.ConnectFail, ClientDeviceConnectStatus.Disposed -> SocAttention.Red
      ClientDeviceConnectStatus.Connected -> SocAttention.Green
      else -> SocAttention.Yellow
    }
    SocService.updateNode(
      "设备",
      "Device:$type:$subType:Connect:${config.name}",
      "设备连接状态:$type:$subType:${config.name}",
      r,
      attention,
    )
  }

  private fun removeSocConnectRecord() {
    SocService.removeNode("Device:$type:$subType:Connect:${config.name}")
  }
}

interface ClientDeviceConfig {
  val name: String
  val disabled: Boolean
  val autoRetry: Boolean
  val timeout: Int
  val maxRetry: Int
  val retryDelay: Long
}

enum class ClientDeviceConnectStatus {
  Init,
  Connecting,
  Connected,
  ConnectFail,
  Disabled,
  Disposing,
  Disposed,
}

data class ClientDeviceConnectRecord(
  val status: ClientDeviceConnectStatus,
  val retryCount: Int = 1,
  val lastError: String? = null,
)

class ClientDeviceException(val kind: ClientDeviceExceptionKind, message: String, cause: Throwable? = null) :
  BzError(cause, "clientDeviceException$kind", message)

enum class ClientDeviceExceptionKind {
  ConnectFail,
  ReqError,
  ReadFail,
  WriteFail,
  OpCancel,
  OpTimeout,
  DeviceNotExist,
  DeviceDisabled,
}