package com.seer.wcs.device.tcp


import io.netty.bootstrap.ServerBootstrap
import io.netty.channel.ChannelFuture
import io.netty.channel.ChannelInitializer
import io.netty.channel.ChannelOption
import io.netty.channel.ChannelPipeline
import io.netty.channel.nio.NioEventLoopGroup
import io.netty.channel.socket.SocketChannel
import io.netty.channel.socket.nio.NioServerSocketChannel
import io.netty.handler.ssl.SslContextBuilder
import io.netty.handler.ssl.util.SelfSignedCertificate
import org.slf4j.LoggerFactory
import java.net.InetSocketAddress

val ssc = SelfSignedCertificate()

class TcpServer<T : Any>(
  private val purpose: String,
  private val port: Int,
  private val schema: FixedHeadFrameSchema<T>,
  ssl: Boolean,
  onMessage: MsgCallback<T>,
  onRemoved: CtxRemovedCallback? = null,
) {

  private val logger = LoggerFactory.getLogger(this::class.java)

  private var future: ChannelFuture? = null
  private var group: NioEventLoopGroup = NioEventLoopGroup()

  init {
    val sslCtx = if (ssl) {
      SslContextBuilder.forServer(ssc.certificate(), ssc.privateKey()).build()
    } else {
      null
    }

    try {
      val b = ServerBootstrap()
      b.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 10000)
      b.group(group)
        .channel(NioServerSocketChannel::class.java)
        // .handler(LoggingHandler(LogLevel.INFO, ByteBufFormat.HEX_DUMP))
        .localAddress(InetSocketAddress(port))
        .childHandler(object : ChannelInitializer<SocketChannel>() {
          override fun initChannel(ch: SocketChannel) {
            val pipeline: ChannelPipeline = ch.pipeline()
            if (sslCtx != null) pipeline.addLast(sslCtx.newHandler(ch.alloc()))
            pipeline.addLast(FixedHeadFrameDecoder(schema))
            pipeline.addLast(ServerInboundHandler(onMessage, ::onError, onRemoved))
          }
        })
      future = b.bind().sync() // 因为打开本地端口，应该很快
      logger.info("TCP server [$purpose] ready, port=$port")
    } finally {
      // group.shutdownGracefully().sync()
    }
  }

  private fun onError(e: Throwable) {
    logger.error("TCP server [$purpose]:$port error", e)
  }

  fun dispose() {
    logger.info("Dispose TCP server [$purpose]:$port")
    try {
      val channel = future?.channel()
      if (channel != null) {
        channel.close()
        channel.closeFuture().sync()
      }
    } catch (e: Exception) {
      logger.error("close future", e)
    }
    try {
      group.shutdownGracefully().sync()
    } catch (e: Exception) {
      logger.error("close group", e)
    }
  }
}