package com.seer.wcs.device.plc


import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors

object PlcRwLogService {
  
  private val logger = LoggerFactory.getLogger(this::class.java)
  
  private val readCache: MutableMap<String, String> = ConcurrentHashMap()
  
  private val executor = Executors.newSingleThreadExecutor()
  
  fun logRead(
    
    deviceName: String,
    action: String,
    value: String,
    deviceType: String,
    ip: String
  ) = executor.submit {
    val key = "$deviceName-$action"
    val oldValue = readCache[key]
    if (oldValue != value) {
      readCache[key] = value
      val ev: EntityValue = mutableMapOf(
        "deviceName" to deviceName,
        "rw" to "Read",
        "deviceName" to deviceName,
        "action" to action,
        "valueDesc" to value,
        "oldValueDesc" to oldValue,
        "deviceType" to deviceType,
        "ip" to ip,
      )
      logger.info("PLC 读值变化。$ev")
      EntityRwService.createOne("PlcRwLog", ev)
    }
  }
  
  /**
   * 异步串行记录
   */
  fun logWrite(
    
    deviceName: String,
    action: String,
    value: String,
    reason: String,
    deviceType: String,
    ip: String
  ) = executor.submit {
    val ev: EntityValue = mutableMapOf(
      "deviceName" to deviceName,
      "rw" to "Write",
      "deviceName" to deviceName,
      "action" to action,
      "valueDesc" to value,
      "reason" to reason,
      "deviceType" to deviceType,
      "ip" to ip,
    )
    logger.info("PLC 写。$ev")
    EntityRwService.createOne("PlcRwLog", ev)
  }
  
}