package com.seer.wcs.device.tcp

import com.seer.trick.base.soc.SocService
import io.netty.buffer.ByteBuf
import io.netty.buffer.ByteBufUtil
import io.netty.channel.ChannelFuture
import io.netty.channel.EventLoopGroup
import java.net.SocketAddress
import java.util.concurrent.TimeUnit

class NettyClient(
  private val name: String,
  private val group: EventLoopGroup,
  private val channelFuture: ChannelFuture
) {
  /**
   * 不加同步，外部加同步
   */
  fun write(bf: ByteBuf) {
    val bfStr = ByteBufUtil.prettyHexDump(bf)
    SocService.updateNode("网络", "WriteTcp:$name", "写 TCP:$name", "开始。请求=$bfStr")
    val r = channelFuture.channel().writeAndFlush(bf).await(5000, TimeUnit.MILLISECONDS)

    SocService.updateNode("网络", "WriteTcp:$name", "写 TCP:$name", "结束。请求=$bfStr，响应=$r")
  }
  fun getLocalAddress(): SocketAddress? {
    return channelFuture.channel().localAddress()
  }

  fun close() {
    try {
      channelFuture.channel().close()
      channelFuture.channel().closeFuture().sync()
    } catch (e: InterruptedException) {
      // ignore
    } finally {
      try {
        group.shutdownGracefully()
      } catch (e: InterruptedException) {
        // ignore
      }
    }
  }
}