package com.seer.wcs.device.plc

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.github.xingshuangs.iot.common.enums.EDataType

import com.seer.trick.base.concurrent.BaseConcurrentCenter.highTimeSensitiveExecutor
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.change.EntityChange
import com.seer.trick.base.entity.service.extension.EntityServiceExtension
import com.seer.trick.base.entity.service.extension.EntityServiceExtensions
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.submitCatch
import com.seer.wcs.device.ClientDeviceConnectStatus
import com.seer.wcs.device.plc.modbus.ModbusReadReq
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

object PlcWatcher : EntityServiceExtension() {
  
  private val logger = LoggerFactory.getLogger(this::class.java)
  
  private const val configItemId = "PlcDevicePanels"
  
  private val timer = Executors.newSingleThreadScheduledExecutor()
  
  @Volatile
  private var configs: List<PlcDevicePanelConfig> = emptyList()
  
  val valueList: MutableList<PlcDevicePanelValue> = CopyOnWriteArrayList()
  
  @Volatile
  private var updating = false
  
  init {
    EntityServiceExtensions.addExtension("ConfigItem", this)
  }
  
  fun init() {
    
    timer.scheduleAtFixedRate(PlcWatcher::updateValue, 3000, 1000, TimeUnit.MILLISECONDS)
    
    val configStr = null // TODO
    reset(configStr)
  }
  
  override fun afterCreating(em: EntityMeta, evList: List<EntityValue>) {
    if (em.name != "ConfigItem") return
    for (ev in evList) {
      val id = EntityHelper.mustGetId(ev)
      if (id != configItemId) continue
      reset(ev["value"] as String?)
    }
  }
  
  override fun afterUpdating(em: EntityMeta, changes: List<EntityChange>) {
    if (em.name != "ConfigItem") return
    for (c in changes) {
      val ev = c.newValue ?: continue
      val id = EntityHelper.mustGetId(ev)
      if (id != configItemId) continue
      reset(ev["value"] as String?)
    }
  }
  
  override fun afterRemoving(em: EntityMeta, oldValues: List<EntityValue>) {
    if (em.name != "ConfigItem") return
    for (ev in oldValues) {
      val id = EntityHelper.mustGetId(ev)
      if (id != configItemId) continue
      reset("")
    }
  }
  
  private fun reset(str: String?) {
    configs = if (str.isNullOrBlank()) {
      emptyList()
    } else {
      try {
        JsonHelper.mapper.readValue(str, jacksonTypeRef())
      } catch (e: Exception) {
        logger.error("Bad PlcDevicePanels configs")
        emptyList()
      }
    }
    
    valueList.clear()
    for ((i, c) in configs.withIndex()) {
      valueList += PlcDevicePanelValue(i, PlcDevicePanelOnline.Unknown, c.watchPoints.map { PlcWatchPointValue() })
    }
  }
  
  @Synchronized
  private fun updateValue() {
    
    if (updating) return
    updating = true
    try {
      val configs = configs
      if (configs.isEmpty()) return
      
      configs.mapIndexed { i, c ->
        highTimeSensitiveExecutor.submitCatch("plc watcher update value", logger) {
          if (c.deviceName.isBlank()) {
            valueList[i] = PlcDevicePanelValue(i, PlcDevicePanelOnline.NoDevice)
            return@submitCatch
          }
          when (c.deviceType) {
            "Modbus" -> updateModbus(i, c)
            else -> valueList[i] = PlcDevicePanelValue(i, PlcDevicePanelOnline.NoDevice)
          }
        }
      }.map { f ->
        try {
          f.get()
        } catch (e: Exception) {
          logger.error("unexpected error", e)
        }
      }
    } finally {
      updating = false
    }
  }
  
  private fun updateModbus(i: Int, c: PlcDevicePanelConfig) {
    val device = PlcCenter.getModbusClient(c.deviceName)
    if (device == null) {
      valueList[i] = PlcDevicePanelValue(i, PlcDevicePanelOnline.NoDevice)
      return
    }
    val online = PlcDevicePanelOnline.valueOf(device.status.name)
    // 因为是同一个设备，一个读失败，全失败
    if (device.status != ClientDeviceConnectStatus.Connected) {
      valueList[i] = PlcDevicePanelValue(i, online, emptyList())
    } else {
      try {
        val wpValues = c.watchPoints.map { vp ->
          val getter = vp.getter
          
          val rawValue = device.readRetry(
            
            ModbusReadReq(
              code = getter.modbusCode,
              address = getter.modbusAddress,
              qty = getter.qty,
              slaveId = getter.salveId,
              maxRetry = 1
            )
          )
          val value: Any? = if (getter.qty == 1) rawValue.firstOrNull() else rawValue
          PlcWatchPointValue(value, null)
        }
        valueList[i] = PlcDevicePanelValue(i, online, wpValues)
      } catch (e: Exception) {
        valueList[i] = PlcDevicePanelValue(i, online, emptyList())
      }
    }
  }
  
  // TODO S7
}

data class PlcDevicePanelConfig(
  val label: String = "",
  val displayOrder: Int = 0,
  val disabled: Boolean = false,
  val deviceType: String = "",
  val deviceName: String = "",
  val watchPoints: List<PlcWatchPointConfig> = emptyList()
)

data class PlcWatchPointConfig(
  val label: String = "",
  val disabled: Boolean = false,
  val getter: PlcWatchPointGetter = PlcWatchPointGetter(),
  // val s7Getter: S7PlcWatchPointValue = S7PlcWatchPointValue(),
  val attentionExpression: String = "",
  val remark: String = ""
)

data class PlcWatchPointGetter(
  val salveId: Int = 0,
  val modbusCode: Int = 1,
  val modbusAddress: Int = 0,
  val qty: Int = 1,
  val toASCII: Boolean = false
)

data class S7PlcWatchPointValue(
  val blockType: String = "DB",
  val dataType: EDataType = EDataType.BYTE,
  val dbId: Int = 1,
  val byteOffset: Int = 0,
  val bitOffset: Int = 0,
)

data class PlcDevicePanelValue(
  val index: Int = 0,
  val online: PlcDevicePanelOnline = PlcDevicePanelOnline.Unknown,
  val watchPoints: List<PlcWatchPointValue> = emptyList()
)

enum class PlcDevicePanelOnline {
  Unknown, NoDevice,
  Init, Connecting, Connected, ConnectFail, Disabled, Disposing, Disposed
}

data class PlcWatchPointValue(
  val value: Any? = null,
  val errorMsg: String? = null,
  val timestamp: Date = Date()
)