package com.seer.wcs.device.simpletcp


import com.seer.trick.base.soc.SocAttention
import com.seer.trick.base.soc.SocService
import com.seer.trick.helper.getTypeMessage
import com.seer.wcs.device.tcp.InboundHandler
import io.netty.bootstrap.Bootstrap
import io.netty.channel.*
import io.netty.channel.nio.NioEventLoopGroup
import io.netty.channel.socket.SocketChannel
import io.netty.channel.socket.nio.NioSocketChannel
import org.slf4j.LoggerFactory
import java.io.IOException
import java.net.InetSocketAddress
import java.util.concurrent.TimeUnit
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

/**
 * 简单 TCP 客户端类。
 * 用于与指定主机和端口的服务器进行 TCP 通信，包含连接、发送请求、接收响应以及资源管理等功能。
 * 通过给定的主机地址和端口号进行初始化，内部使用 Netty 实现与服务器的通信。
 */
class SimpleTcpClient(
  private val host: String,
  private val port: Int,
) {
  private val logger = LoggerFactory.getLogger(this::class.java)
  private val loggerHead: String = "$host:$port "
  private var resMessage: String? = null
  private var nettyClient: SimpleNettyClient? = null

  @Volatile
  private var disposed = false

  @Volatile
  private var resetting = true

  private val lock = ReentrantLock()
  private val condition = lock.newCondition()

  /**
   * 销毁客户端资源。
   */
  fun dispose() {
    logger.info(loggerHead + "Dispose tcp simple client")
    disposed = true
    val c = nettyClient
    nettyClient = null
    if (c != null) {
      try {
        c.close()
      } catch (e: Exception) {
        // ignore
      }
    }
  }

  /**
   * 重置客户端。
   */
  @Synchronized
  private fun reset(reason: String) {
    resMessage = null
    if (resetting || nettyClient == null) {
      updateSocState("已重置，原因=$reason")
      return
    }
    updateSocState("开始重置，原因=$reason")
    resetting = true
    val c = nettyClient
    nettyClient = null
    if (c != null) {
      try {
        c.close()
      } catch (e: Exception) {
        // ignore
      }
    }
    updateSocState("已重置，原因=$reason")
  }

  /**
   * 获取 Netty 客户端实例。如果不存在则创建一个新的。
   */
  private fun getNettyClient(): SimpleNettyClient {
    var c = nettyClient
    if (c == null) {
      c = newNettyClient()
      resetting = false
      nettyClient = c
    }
    return c
  }

  /**
   * 创建新的 Netty 客户端实例。
   */
  private fun newNettyClient(): SimpleNettyClient {
    updateSocState("构建中")
    val handler = InboundHandler(this::onMessage, this::onError)
    val group: EventLoopGroup = NioEventLoopGroup()
    val b = Bootstrap()
    b.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 10000)
    b
      .group(group)
      .channel(NioSocketChannel::class.java)
      .remoteAddress(InetSocketAddress(host, port))
      .handler(
        object : ChannelInitializer<SocketChannel>() {
          override fun initChannel(ch: SocketChannel) {
            val pipeline: ChannelPipeline = ch.pipeline()
            pipeline.addLast(SimpleDataDecoder())
            pipeline.addLast(handler)
          }
        },
      )
    return try {
      val f: ChannelFuture = b.connect().sync()
      updateSocState("已连接")
      SimpleNettyClient("$host:$port", group, f)
    } catch (e: Throwable) {
      updateSocState("连接失败：" + e.getTypeMessage(), SocAttention.Red)
      try {
        group.shutdownGracefully().sync()
      } catch (e: Exception) {
        // ignore
      }
      throw e
    }
  }

  /**
   * 处理接收到的消息的回调方法。
   */
  fun onMessage(ctx: ChannelHandlerContext, msg: ByteArray) {
    val hexString = msg.joinToString("") { "%02X".format(it) }
    resMessage = hexString
    lock.withLock {
      condition.signalAll()
    }
  }

  /**
   * 处理错误的回调方法。
   */
  private fun onError(e: Throwable) {
    logger.error(loggerHead + "onError called", e)
    dispose()
  }

  /**
   * 发送请求并等待响应。
   */
  fun request(reqHexString: String, timeout: Long = 10 * 1000): SimpleTcpResult {
    val res = doRequest(reqHexString, timeout)
    val kind = res.kind
    if (kind != SimpleTcpResultKind.Ok) {
      reset("请求失败，销毁 Simple Tcp 端口客户端，错误类型=$kind")
    }
    return res
  }

  /**
   * 将十六进制字符串转换为字节数组。
   */
  private fun hexStringToByteArray(hexString: String): ByteArray {
    val cleanedHexString = hexString.replace("\\s".toRegex(), "")
    val len = cleanedHexString.length
    val data = ByteArray(len / 2)
    for (i in 0 until len step 2) {
      val hex = cleanedHexString.substring(i, i + 2)
      data[i / 2] = hex.toInt(16).toByte()
    }
    return data
  }

  /**
   * 执行请求并返回结果。
   */
  private fun doRequest(reqHexString: String, timeout: Long = 10 * 1000): SimpleTcpResult {
    val byteArray = hexStringToByteArray(reqHexString)

    // logger.debug("请求开始 $reqHexString")
    updateSocState("请求开始 报文: $reqHexString")

    lock.withLock {
      // logger.debug("等客户端 $reqHexString")
      updateSocState("等客户端 $reqHexString")

      val client =
        try {
          getNettyClient()
        } catch (e: Throwable) {
          return handleException(e, "等客户端失败", reqHexString)
        }
      updateSocState("客户端就绪，准备写 #$reqHexString")
      try {
        client.write(byteArray)
      } catch (e: InterruptedException) {
        // logger.error(loggerHead + "写超时")
        return handleWriteException("写失败，超时", reqHexString)
      } catch (e: Throwable) {
        if (e is IOException) {
          logger.error(loggerHead + "写 SimpleTcp IO 报错，报文=$reqHexString, 错误=${e.getTypeMessage()}")
        } else {
          logger.error(loggerHead + "写 SimpleTcp 报错，报文=$reqHexString", e)
        }
        return handleWriteException("写报错 #${e.getTypeMessage()}", reqHexString)
      }

      // logger.debug("写完成 #$reqHexString")
      updateSocState("写完成 #$reqHexString")

      // 等结果
      return waitForResult(timeout, reqHexString)
    }
  }

  /**
   * 处理异常情况，返回请求结果。
   */
  private fun handleException(e: Throwable, state: String, reqHexString: String): SimpleTcpResult {
    updateSocState("$state：${e.getTypeMessage()}", SocAttention.Red)
    return if (e is InterruptedException) {
      SimpleTcpResult(SimpleTcpResultKind.Interrupted, host, port, reqHexString, null)
    } else {
      SimpleTcpResult(SimpleTcpResultKind.ConnectFail, host, port, reqHexString, errMsg = e.message)
    }
  }

  /**
   * 处理写操作异常情况，返回请求结果。
   */
  private fun handleWriteException(state: String, reqHexString: String): SimpleTcpResult {
    updateSocState(state, SocAttention.Red)
    return SimpleTcpResult(SimpleTcpResultKind.WriteError, host, port, reqHexString, errMsg = "写超时")
  }

  /**
   * 等待结果并返回请求结果。
   */
  private fun waitForResult(timeout: Long, reqHexString: String): SimpleTcpResult {
    while (!resetting) {
      updateSocState("等结果 #$reqHexString")
      try {
        if (!condition.await(timeout, TimeUnit.MILLISECONDS)) {
          updateSocState("等结果，超时", SocAttention.Red)
          return SimpleTcpResult(SimpleTcpResultKind.Timeout, host, port, reqHexString, errMsg = "Timeout")
        }
      } catch (e: InterruptedException) {
        updateSocState("等结果，被中断", SocAttention.Red)
        return SimpleTcpResult(SimpleTcpResultKind.Interrupted, host, port, reqHexString, errMsg = "Timeout")
      }

      updateSocState("检查结果 #$reqHexString")
      resMessage?.let {
        val resHexString = it
        val hexString = resMessage
        resMessage = null
        updateSocState("收到结果 #$hexString")
        return SimpleTcpResult(SimpleTcpResultKind.Ok, host, port, reqHexString, resHexString)
      }
    }
    updateSocState("已销毁")
    return SimpleTcpResult(SimpleTcpResultKind.Disposed, host, port, reqHexString)
  }

  /**
   * 记录系统监控。
   */
  private fun updateSocState(state: String, attention: SocAttention = SocAttention.None) {
    if (disposed) return
    SocService.updateNode(
      "Simple Tcp",
      "SimpleTcpClient:$host:$port",
      "Simple Tcp 连接::$host:$port",
      state,
      attention,
    )
  }
}
