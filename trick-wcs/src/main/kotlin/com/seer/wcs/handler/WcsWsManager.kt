package com.seer.wcs.handler

import com.seer.trick.base.http.WebSocketSubscriber
import com.seer.trick.base.http.WsEnv
import com.seer.trick.base.http.WsMsg
import com.seer.wcs.device.plc.PlcPanelService
import io.javalin.websocket.WsMessageContext

object WcsWsManager : WebSocketSubscriber() {

  override fun onMessage(ctx: WsMessageContext, msg: WsMsg, env: WsEnv) {
    when (msg.action) {
      "WCS::Plc::Panel::Value" -> onFetchPlcPanelValue(ctx, msg)
    }
  }

  private fun onFetchPlcPanelValue(ctx: WsMessageContext, msg: WsMsg) {
    ctx.send(WsMsg.json("WCS::Plc::Panel::Value::Reply", PlcPanelService.value, replyToId = msg.id))
  }
}