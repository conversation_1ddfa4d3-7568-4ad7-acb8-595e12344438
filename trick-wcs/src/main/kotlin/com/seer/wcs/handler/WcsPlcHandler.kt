package com.seer.wcs.handler

import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.getReqBody
import com.seer.trick.base.http.operator
import com.seer.wcs.device.plc.PlcCenter
import com.seer.wcs.device.plc.PlcPanelService
import com.seer.wcs.device.plc.modbus.ModbusReadReq
import com.seer.wcs.device.plc.modbus.ModbusWriteReq
import io.javalin.http.Context

object WcsPlcHandler {
  
  fun registerHandlers() {
    val c = Handlers("api/wcs/plc")
    c.post("reconnect", ::reconnect, auth())
    c.post("modbus/read", ::readModbus, auth())
    c.post("modbus/write", ::writeModbus, auth())
    
    c.get("panel-config", ::getPanelConfig, auth())
    c.post("panel-config", ::updatePanelConfig, auth())
    
    c.post("panel/button", ::callPanelButton, auth())
  }
  
  private fun reconnect(ctx: Context) {
    val req: DeviceIdsReq = ctx.getReqBody()
    
    req.ids.map { name ->
      PlcCenter.getModbusClient(name)?.init(true)
        ?: PlcCenter.getS7Client(name)?.init(true)
    }
  }
  
  private fun readModbus(ctx: Context) {
    
    
    val req: HandleModbusReadReq = ctx.getReqBody()
    val client = PlcCenter.mustGetModbusClient(req.deviceName)
    val r = req.commands.map { c ->
      val rr = ModbusReadReq(c.code, c.address, c.qty, c.slaveId, req.maxRetry, req.retryDelay)
      client.readRetry(rr)
    }
    
    ctx.json(r)
  }
  
  private fun writeModbus(ctx: Context) {
    
    
    val req: HandleModbusWriteReq = ctx.getReqBody()
    val client = PlcCenter.mustGetModbusClient(req.deviceName)
    req.commands.map { c ->
      val wr = ModbusWriteReq(c.code, c.address, c.slaveId, req.maxRetry, req.retryDelay)
      client.writeRetry(wr, c.values)
    }
    
    ctx.status(200)
  }
  
  private fun getPanelConfig(ctx: Context) {
    ctx.result(PlcPanelService.configStr)
  }
  
  private fun updatePanelConfig(ctx: Context) {
    
    val req = ctx.body()
    
    PlcPanelService.update(req)
    
    ctx.status(200)
  }
  
  private fun callPanelButton(ctx: Context) {
    
    val req: CallPanelButtonReq = ctx.getReqBody()
    
    PlcPanelService.callButton(req.pi, req.bi)
    
    ctx.status(200)
  }
}

data class DeviceIdsReq(val ids: List<String>)

data class HandleModbusReadReq(
  val deviceName: String,
  val maxRetry: Int = -1,
  val retryDelay: Long = 1000,
  val commands: List<ModbusReadCommand>,
)

data class ModbusReadCommand(val code: Int, val address: Int, val qty: Int, val slaveId: Int = 0)

data class HandleModbusWriteReq(
  val deviceName: String,
  val maxRetry: Int = -1,
  val retryDelay: Long = 1000,
  val commands: List<ModbusWriteCommand>,
)

data class ModbusWriteCommand(val code: Int, val address: Int, val slaveId: Int = 0, val values: List<Int>)

data class CallPanelButtonReq(val pi: Int, val bi: Int)