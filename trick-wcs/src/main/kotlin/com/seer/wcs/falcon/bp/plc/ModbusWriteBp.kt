package com.seer.wcs.falcon.bp.plc


import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*
import com.seer.wcs.device.ClientDeviceException
import com.seer.wcs.device.ClientDeviceExceptionKind
import com.seer.wcs.device.plc.PlcCenter
import com.seer.wcs.device.plc.modbus.ModbusWriteReq
import kotlin.math.max

class ModbusWriteBp : AbstractBp() {
  
  override fun process() {
    val deviceName = mustGetBlockInputParam("deviceName") as String
    val code = mustGetBlockInputParamAsLong("code").toInt()
    val address = mustGetBlockInputParamAsLong("address").toInt()
    val value = mustGetBlockInputParamAsLong("value").toInt()
    val slaveId = getBlockInputParamAsLong("slaveId")?.toInt() ?: 0
    val maxRetry = getBlockInputParamAsLong("maxRetry")?.toInt()
    val retryDelay = getBlockInputParamAsLong("retryDelay")
    
    try {
      val client = PlcCenter.mustGetModbusClient(deviceName)
      client.writeRetry(
        ModbusWriteReq(code, address, slaveId, maxRetry = maxRetry, retryDelay = retryDelay), listOf(value)
      )
    } catch (e: ClientDeviceException) {
      if (e.kind == ClientDeviceExceptionKind.OpCancel) throw InterruptedException(e.message + e.cause?.message)
      else throw e
    }
  }
  
  companion object {
    
    val def = BlockDef(
      ModbusWriteBp::class.simpleName!!,
      color = "#7293d8",
      inputParams = listOf(
        BlockInputParamDef("deviceName", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.PlcDevice)),
        BlockInputParamDef(
          "code", BlockParamType.Long, true,
          options = listOf(
            BlockInputParamOption(0x05.toString(), "0x05"),
            BlockInputParamOption(0x06.toString(), "0x06"),
          ),
        ),
        BlockInputParamDef("address", BlockParamType.Long, true),
        BlockInputParamDef("value", BlockParamType.Long, true),
        BlockInputParamDef("slaveId", BlockParamType.Long, false),
        BlockInputParamDef("maxRetry", BlockParamType.Long, false),
        BlockInputParamDef("retryDelay", BlockParamType.Long, false),
      )
    )
  }
  
}