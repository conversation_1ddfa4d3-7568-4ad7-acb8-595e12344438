package com.seer.wcs.falcon.bp.plc

import com.github.xingshuangs.iot.common.enums.EDataType
import com.seer.trick.BzError

import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*
import com.seer.wcs.device.ClientDeviceException
import com.seer.wcs.device.ClientDeviceExceptionKind
import com.seer.wcs.device.plc.s7.S7ReadReq
import com.seer.wcs.device.plc.PlcCenter

class S7ReadEqBp : AbstractBp() {
  
  override fun process() {
    val deviceName = mustGetBlockInputParam("deviceName") as String
    val blockType = mustGetBlockInputParam("blockType") as String
    val dataType = mustGetBlockInputParam("dataType") as String
    val dbId = mustGetBlockInputParamAsLong("dbId")
    val byteOffset = mustGetBlockInputParamAsLong("byteOffset")
    val bitOffset = getBlockInputParamAsLong("bitOffset") ?: 0
    val v = getBlockInputParam("value") ?: ""
    val readDelay = getBlockInputParamAsLong("readDelay") // 读值与预期不同的重读时间间隔
    val readLimit = getBlockInputParamAsLong("readLimit") // 读值与预期不同的重读次数限制
    val maxRetry = getBlockInputParamAsLong("maxRetry")?.toInt() // 失败重试次数限制
    val retryDelay = getBlockInputParamAsLong("retryDelay") // 失败重试时间间隔
    
    val req = S7ReadReq(
      blockType,
      EDataType.valueOf(dataType),
      dbId.toInt(),
      byteOffset.toInt(),
      bitOffset.toInt(),
      maxRetry,
      retryDelay
    )
    try {
      val client = PlcCenter.mustGetS7Client(deviceName)
      val r = client.readUntilEq(req, targetValue = v, readDelay ?: 1000L, readLimit?.toInt() ?: -1)
      if (!r) throw BzError("errS7ReadEqNotMatch", "")
    } catch (e: ClientDeviceException) {
      if (e.kind == ClientDeviceExceptionKind.OpCancel) throw InterruptedException(e.message + e.cause?.message)
      else throw e
    }
  }
  
  companion object {
    val def = BlockDef(
      S7ReadEqBp::class.simpleName!!,
      color = "#00ccff",
      inputParams = listOf(
        BlockInputParamDef("deviceName", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.PlcDevice)),
        BlockInputParamDef(
          "blockType", BlockParamType.String, true, defaultValue = "DB", options = listOf(
            BlockInputParamOption("DB", "DB"),
            BlockInputParamOption("Q", "Q"),
            BlockInputParamOption("I", "I"),
            BlockInputParamOption("M", "M"),
            BlockInputParamOption("V", "V"),
          )
        ),
        BlockInputParamDef(
          "dataType", BlockParamType.String, true, options = listOf(
            BlockInputParamOption("BOOL", "BOOL"),
            BlockInputParamOption("BYTE", "BYTE"),
            BlockInputParamOption("INT16", "INT16"),
            BlockInputParamOption("UINT16", "UINT16"),
            BlockInputParamOption("INT32", "INT32"),
            BlockInputParamOption("UINT32", "UINT32"),
            BlockInputParamOption("FLOAT32", "FLOAT32"),
            BlockInputParamOption("FLOAT64", "FLOAT64"),
            BlockInputParamOption("STRING", "STRING"),
          )
        ),
        BlockInputParamDef("dbId", BlockParamType.Long, true),
        BlockInputParamDef("byteOffset", BlockParamType.Long, true),
        BlockInputParamDef("bitOffset", BlockParamType.Long, defaultValue = 0),
        BlockInputParamDef("value", BlockParamType.Any, true),
        BlockInputParamDef("readLimit", BlockParamType.Long, false),
        BlockInputParamDef("readDelay", BlockParamType.Long, false),
        BlockInputParamDef("maxRetry", BlockParamType.Long, false),
        BlockInputParamDef("retryDelay", BlockParamType.Long, false),
      ),
      outputParams = listOf()
    )
  }
}