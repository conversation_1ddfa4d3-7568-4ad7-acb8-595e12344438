package com.seer.wcs.falcon.bp.plc


import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*
import com.seer.wcs.device.ClientDeviceException
import com.seer.wcs.device.ClientDeviceExceptionKind
import com.seer.wcs.device.plc.PlcCenter
import com.seer.wcs.device.plc.modbus.ModbusReadReq

class ModbusReadBp : AbstractBp() {
  
  override fun process() {
    val deviceName = mustGetBlockInputParam("deviceName") as String
    val code = mustGetBlockInputParamAsLong("code").toInt()
    val address = mustGetBlockInputParamAsLong("address").toInt()
    val slaveId = getBlockInputParamAsLong("slaveId")?.toInt() ?: 0
    val maxRetry = getBlockInputParamAsLong("maxRetry")?.toInt()
    val retryDelay = getBlockInputParamAsLong("retryDelay")
    
    try {
      val client = PlcCenter.mustGetModbusClient(deviceName)
      val values =
        client.readRetry(ModbusReadReq(code, address, 1, slaveId, maxRetry = maxRetry, retryDelay = retryDelay))
      val v = values.firstOrNull()
      
      setBlockOutputParams(mapOf("value" to v))
    } catch (e: ClientDeviceException) {
      if (e.kind == ClientDeviceExceptionKind.OpCancel) throw InterruptedException(e.message + e.cause?.message)
      else throw e
    }
  }
  
  companion object {
    
    val def = BlockDef(
      ModbusReadBp::class.simpleName!!,
      color = "#7293d8",
      inputParams = listOf(
        BlockInputParamDef("deviceName", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.PlcDevice)),
        BlockInputParamDef(
          "code", BlockParamType.Long, true,
          options = listOf(
            BlockInputParamOption(0x01.toString(), "0x01"),
            BlockInputParamOption(0x02.toString(), "0x02"),
            BlockInputParamOption(0x03.toString(), "0x03"),
            BlockInputParamOption(0x04.toString(), "0x04"),
          ),
        ),
        BlockInputParamDef("address", BlockParamType.Long, true),
        BlockInputParamDef("slaveId", BlockParamType.Long, false),
        BlockInputParamDef("maxRetry", BlockParamType.Long, false),
        BlockInputParamDef("retryDelay", BlockParamType.Long, false),
      ),
      outputParams = listOf(
        BlockOutputParamDef("value", BlockParamType.Long)
      )
    )
  }
  
}