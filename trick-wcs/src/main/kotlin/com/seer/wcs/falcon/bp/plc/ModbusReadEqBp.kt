package com.seer.wcs.falcon.bp.plc

import com.seer.trick.BzError

import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*
import com.seer.wcs.device.ClientDeviceException
import com.seer.wcs.device.ClientDeviceExceptionKind
import com.seer.wcs.device.plc.PlcCenter
import com.seer.wcs.device.plc.modbus.ModbusReadReq

class ModbusReadEqBp : AbstractBp() {
  
  override fun process() {
    val deviceName = mustGetBlockInputParam("deviceName") as String
    val code = mustGetBlockInputParamAsLong("code").toInt()
    val address = mustGetBlockInputParamAsLong("address").toInt()
    val slaveId = getBlockInputParamAsLong("slaveId")?.toInt() ?: 0
    val targetValue = mustGetBlockInputParamAsLong("targetValue").toInt()
    val readDelay = getBlockInputParamAsLong("readDelay") // 读值与预期不同的重读时间间隔
    val readLimit = getBlockInputParamAsLong("readLimit") // 读值与预期不同的重读次数限制
    val maxRetry = getBlockInputParamAsLong("maxRetry")?.toInt() // 失败重试次数限制
    val retryDelay = getBlockInputParamAsLong("retryDelay") // 失败重试时间间隔
    
    try {
      val client = PlcCenter.mustGetModbusClient(deviceName)
      val r = client.readUtilEq(
        ModbusReadReq(code, address, 1, slaveId, maxRetry = maxRetry, retryDelay = retryDelay),
        targetValue, readDelay ?: 1000L, readLimit?.toInt() ?: -1
      )
      if (!r) throw BzError("errModbusReadEqNotMatch", "errModbusReadEqNotMatch")
    } catch (e: ClientDeviceException) {
      if (e.kind == ClientDeviceExceptionKind.OpCancel) throw InterruptedException(e.message + e.cause?.message)
      else throw e
    }
  }
  
  companion object {
    
    val def = BlockDef(
      ModbusReadEqBp::class.simpleName!!,
      color = "#7293d8",
      inputParams = listOf(
        BlockInputParamDef("deviceName", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.PlcDevice)),
        BlockInputParamDef(
          "code", BlockParamType.Long, true,
          options = listOf(
            BlockInputParamOption(0x01.toString(), "0x01"),
            BlockInputParamOption(0x02.toString(), "0x02"),
            BlockInputParamOption(0x03.toString(), "0x03"),
            BlockInputParamOption(0x04.toString(), "0x04"),
          ),
        ),
        BlockInputParamDef("address", BlockParamType.Long, true, true),
        BlockInputParamDef("slaveId", BlockParamType.Long, false),
        BlockInputParamDef("targetValue", BlockParamType.Long, true),
        BlockInputParamDef("readLimit", BlockParamType.Long, false),
        BlockInputParamDef("readDelay", BlockParamType.Long, false),
        BlockInputParamDef("maxRetry", BlockParamType.Long, false),
        BlockInputParamDef("retryDelay", BlockParamType.Long, false),
      ),
    )
  }
  
}