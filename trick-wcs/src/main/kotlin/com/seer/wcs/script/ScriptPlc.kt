package com.seer.wcs.script

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.base.MapToAnyNull
import com.seer.trick.base.script.ScriptTraceContext
import com.seer.trick.helper.JsonHelper
import com.seer.wcs.device.plc.PlcCenter
import com.seer.wcs.device.plc.modbus.ModbusReadReq
import com.seer.wcs.device.plc.modbus.ModbusWriteReq
import com.seer.wcs.device.plc.s7.S7ReadReq
import com.seer.wcs.device.plc.s7.S7WriteReq

object ScriptPlc {

  fun modbusRead(tc: ScriptTraceContext, deviceName: String, reqMap: MapToAnyNull): List<Int> {
    val client = PlcCenter.mustGetModbusClient(deviceName)
    val req: ModbusReadReq = JsonHelper.mapper.convertValue(reqMap, jacksonTypeRef())
    return client.readRetry(req)
  }

  /**
   * 重复读，直到等于某个值；无限尝试
   */
  fun modbusReadUtilEq(
    tc: ScriptTraceContext,
    deviceName: String,
    reqMap: MapToAnyNull,
    targetValue: Int,
    readDelay: Long,
  ) {
    val client = PlcCenter.mustGetModbusClient(deviceName)
    val req: ModbusReadReq = JsonHelper.mapper.convertValue(reqMap, jacksonTypeRef())
    client.readUtilEq(req, targetValue, readDelay)
  }

  /**
   * 重复读，直到等于某个值；最多尝试指定次数；如果成功返回 true，如果超过最大次数，返回 false
   */
  fun modbusReadUtilEqMaxRetry(
    tc: ScriptTraceContext,
    deviceName: String,
    reqMap: MapToAnyNull,
    targetValue: Int,
    readDelay: Long,
    maxRetry: Int,
  ): Boolean {
    val client = PlcCenter.mustGetModbusClient(deviceName)
    val req: ModbusReadReq = JsonHelper.mapper.convertValue(reqMap, jacksonTypeRef())
    return client.readUtilEq(req, targetValue, readDelay, maxRetry)
  }

  fun modbusWrite(tc: ScriptTraceContext, deviceName: String, reqMap: MapToAnyNull, values: List<Int>) {
    val client = PlcCenter.mustGetModbusClient(deviceName)
    val req: ModbusWriteReq = JsonHelper.mapper.convertValue(reqMap, jacksonTypeRef())
    client.writeRetry(req, values)
  }

  fun s7Read(tc: ScriptTraceContext, deviceName: String, reqMap: MapToAnyNull): Any? {
    val client = PlcCenter.mustGetS7Client(deviceName)
    val req: S7ReadReq = JsonHelper.mapper.convertValue(reqMap, jacksonTypeRef())
    return client.readRetry(req)
  }

  fun s7ReadUntilEq(
    tc: ScriptTraceContext,
    deviceName: String,
    reqMap: MapToAnyNull,
    targetValue: Any,
    readDelay: Long,
  ) {
    val client = PlcCenter.mustGetS7Client(deviceName)
    val req: S7ReadReq = JsonHelper.mapper.convertValue(reqMap, jacksonTypeRef())
    client.readUntilEq(req, targetValue, readDelay)
  }

  fun s7Write(tc: ScriptTraceContext, deviceName: String, reqMap: MapToAnyNull) {
    val client = PlcCenter.mustGetS7Client(deviceName)
    val req: S7WriteReq = JsonHelper.mapper.convertValue(reqMap, jacksonTypeRef())
    client.writeRetry(req)
  }
}