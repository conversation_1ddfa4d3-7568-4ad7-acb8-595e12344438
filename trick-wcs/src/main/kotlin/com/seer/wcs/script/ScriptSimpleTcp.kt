package com.seer.wcs.script


import com.seer.wcs.device.simpletcp.SimpleTcpCenter
import com.seer.wcs.device.simpletcp.SimpleTcpResult
import com.seer.wcs.device.simpletcp.SimpleTcpResultKind

object ScriptSimpleTcp {
  fun doTcpRequest(tcpServerName: String, reqHexString: String, timeout: Long = 10 * 1000): SimpleTcpResult =
    try {
      val simpleTcpClient = SimpleTcpCenter.mustGetSimpleTcpClient(tcpServerName)
      simpleTcpClient.request(reqHexString, timeout)
    } catch (e: Exception) {
      SimpleTcpResult(SimpleTcpResultKind.GetTcpNameError)
    }
}
