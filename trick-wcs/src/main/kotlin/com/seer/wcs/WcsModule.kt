package com.seer.wcs


import com.seer.trick.base.AppModule
import com.seer.trick.base.http.WebSocketManager
import com.seer.trick.falcon.FalconCenter
import com.seer.trick.falcon.domain.BlockDefGroup
import com.seer.wcs.device.plc.PlcCenter
import com.seer.wcs.device.plc.PlcWatcher
import com.seer.wcs.falcon.bp.plc.*
import com.seer.wcs.handler.WcsPlcHandler
import com.seer.wcs.handler.WcsWsManager
import com.seer.wcs.script.ScriptPlc
import com.seer.wcs.script.ScriptSimpleTcp
import com.seer.wcs.stats.WcsStatsService
import org.graalvm.polyglot.Value

object WcsModule : AppModule() {

  override fun registerHttpHandlers() {
    WcsPlcHandler.registerHandlers()
    WebSocketManager.subscribers += WcsWsManager
  }

  override fun beforeScript() {
    PlcCenter.init()
  }

  override fun registerMoreBp() {
    val plcBlocks = listOf(
      FalconCenter.registerBp(ModbusReadBp::class, ModbusReadBp.def),
      FalconCenter.registerBp(ModbusReadEqBp::class, ModbusReadEqBp.def),
      FalconCenter.registerBp(ModbusWriteBp::class, ModbusWriteBp.def),
      FalconCenter.registerBp(S7ReadBp::class, S7ReadBp.def),
      FalconCenter.registerBp(S7ReadEqBp::class, S7ReadEqBp.def),
      FalconCenter.registerBp(S7WriteBp::class, S7WriteBp.def),
    )
    plcBlocks.forEach { it.color = "rgba(0,102,255,0.1)" }
    FalconCenter.registerBpGroup(BlockDefGroup("PLC", "", 37, false, plcBlocks))
  }

  override fun afterHttp() {
    PlcWatcher.init()
  }

  override fun dispose() {
    PlcCenter.dispose()
  }

  override fun putMoreScriptBindings(bindings: Value) {
    bindings.putMember("plc", ScriptPlc)
    bindings.putMember("simpleTcp", ScriptSimpleTcp)
  }

  override fun registerStatsChart() {
    WcsStatsService.register()
  }
}