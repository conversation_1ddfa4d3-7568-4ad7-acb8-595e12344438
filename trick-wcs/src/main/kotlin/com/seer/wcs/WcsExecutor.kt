package com.seer.wcs

import org.slf4j.Logger
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.Future

object WcsExecutor {

  /**
   * submit 一件事始终占用一个线程的任务
   */
  private val wcsSinglePurposeExecutor: ExecutorService = Executors.newCachedThreadPool()

  fun submitLongRun(remark: String, logger: Logger, delay: Long, worker: () -> Unit): Future<*> {
    return wcsSinglePurposeExecutor.submit {
      while (!Thread.interrupted()) {
        try {
          worker()
        } catch (e: InterruptedException) {
          break
        } catch (e: Exception) {
          logger.error("长运行任务报错。$remark", e)
        }

        try {
          Thread.sleep(delay)
        } catch (e: InterruptedException) {
          break
        }
      }
      logger.info("长运行任务停止。$remark")
    }
  }

}