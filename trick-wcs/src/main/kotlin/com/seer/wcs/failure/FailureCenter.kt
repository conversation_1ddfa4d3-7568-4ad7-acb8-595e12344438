package com.seer.wcs.failure

import java.util.*

class FailureCenter(
  private val listener: FailureEventListener?
) {

  val robotDisconnectedMap: MutableMap<String, RobotDisconnected> = Collections.synchronizedMap(HashMap())
  val robotErrorMap: MutableMap<String, RobotError> = Collections.synchronizedMap(HashMap())

  val modbusDisconnectedMap: MutableMap<String, ModbusDisconnected> = Collections.synchronizedMap(HashMap())
  val modbusErrorMap: MutableMap<String, ModbusError> = Collections.synchronizedMap(HashMap())

  fun list(): FailureAll {
    return FailureAll(robotDisconnectedMap, robotErrorMap, modbusDisconnectedMap, modbusErrorMap)
  }

  fun setRobotDisconnected(robotId: String, robotName: String, disconnected: Boolean, message: String) {
    if (disconnected) {
      if (!robotDisconnectedMap.containsKey(robotId)) {
        robotDisconnectedMap[robotId] = RobotDisconnected(robotId, robotName, message)
        fire(FailureEvent(FailureEventType.RobotDisconnected, robotId))
      }
    } else {
      if (robotDisconnectedMap.containsKey(robotId)) {
        robotDisconnectedMap.remove(robotId)
        fire(FailureEvent(FailureEventType.RobotDisconnected, robotId))
      }
    }
  }

  fun setRobotError(robotId: String, robotName: String, errors: List<RobotErrorReport>?) {
    if (!errors.isNullOrEmpty()) {
      val old = robotErrorMap[robotId]
      robotErrorMap[robotId] = RobotError(robotId, robotName, errors)
      if (!Objects.deepEquals(old, errors)) {
        fire(FailureEvent(FailureEventType.RobotError, robotId))
      }
    } else {
      if (robotErrorMap.containsKey(robotId)) {
        robotErrorMap.remove(robotId)
        fire(FailureEvent(FailureEventType.RobotError, robotId))
      }
    }
  }

  fun resetRobot(robotId: String) {
    robotDisconnectedMap.remove(robotId)
    robotErrorMap.remove(robotId)
  }

  fun setModbusDisconnected(modbusId: String, ip: String, port: Int, disconnected: Boolean, message: String) {
    if (disconnected) {
      if (!modbusDisconnectedMap.containsKey(modbusId)) {
        modbusDisconnectedMap[modbusId] = ModbusDisconnected(modbusId, ip, port, message)
        fire(FailureEvent(FailureEventType.ModbusDisconnected, modbusId))
      }
    } else {
      if (modbusDisconnectedMap.containsKey(modbusId)) {
        modbusDisconnectedMap.remove(modbusId)
        fire(FailureEvent(FailureEventType.ModbusDisconnected, modbusId))
      }
    }
  }

  fun setModbusError(modbusId: String, errors: List<ModbusErrorReport>?) {
    if (!errors.isNullOrEmpty()) {
      val old = modbusErrorMap[modbusId]
      modbusErrorMap[modbusId] = ModbusError(modbusId, errors)
      if (!Objects.deepEquals(old, errors)) {
        fire(FailureEvent(FailureEventType.ModbusError, modbusId))
      }
    } else {
      if (modbusErrorMap.containsKey(modbusId)) {
        modbusErrorMap.remove(modbusId)
        fire(FailureEvent(FailureEventType.ModbusError, modbusId))
      }
    }
  }

  fun resetModbus(deviceId: String) {
    modbusDisconnectedMap.remove(deviceId)
    modbusErrorMap.remove(deviceId)
  }

  private fun fire(e: FailureEvent) {
    // logger.debug("Fire failure event, {}", e)
    val l = listener
    if (l != null) l(e)
  }

}

data class RobotDisconnected(
  val robotId: String,
  val robotName: String,
  val message: String,
  val on: Date = Date()
)

data class ModbusDisconnected(
  val modbusId: String,
  val ip: String,
  val port: Int,
  val message: String,
  val on: Date = Date()
)

data class RobotError(
  val robotId: String,
  val robotName: String,
  val errors: List<RobotErrorReport>
)

data class RobotErrorReport(
  val robotId: String,
  val robotName: String,
  val code: Int,
  val level: String,
  val message: String,
  val times: Int,
  val on: Date,
)

data class ModbusErrorReport(
  val modbusId: String,
  val ip: String,
  val port: Int,
  val level: String,
  val message: String,
  val variableAddress: Int,
  val variableValue: Int,
  val on: Date = Date(),
)

data class ModbusError(
  val modbusId: String,
  val errors: List<ModbusErrorReport>
)

data class FailureAll(
  val robotDisconnectedMap: Map<String, RobotDisconnected>,
  val robotErrorMap: Map<String, RobotError>,
  val modbusDisconnectedMap: Map<String, ModbusDisconnected>,
  val modbusErrorMap: Map<String, ModbusError>
) {

  fun count(): Int {
    return this.robotDisconnectedMap.size +
        this.modbusDisconnectedMap.size +
        this.robotErrorMap.values.sumOf { it.errors.size } +
        this.modbusErrorMap.values.sumOf { it.errors.size }
  }

}

data class FailureEvent(
  val type: FailureEventType, val deviceId: String
)

enum class FailureEventType {
  RobotDisconnected, RobotError, ModbusDisconnected, ModbusError
}

typealias FailureEventListener = (e: FailureEvent) -> Unit