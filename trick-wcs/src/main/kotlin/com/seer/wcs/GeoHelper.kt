package com.seer.wcs

import kotlin.math.pow
import kotlin.math.sqrt

object GeoHelper {
  
  fun euclideanDistance(x1: Double, y1: Double, x2: Double, y2: Double): Double {
    return sqrt((x1 - x2).pow(2.0) + (y1 - y2).pow(2.0))
  }
  
  /**
   * 注意！x,y 是矩形左上角，y 轴向上
   */
  fun isPointInRect(px: Double, py: Double, rx: Double, ry: Double, rw: Double, rh: Double): Boolean {
    return !(px < rx || px > rx + rw || py < ry - rh || py > ry)
  }
  
}