package com.seer.trick.fleet.seer

import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.event.EventListener
import com.seer.trick.helper.NumHelper.anyToInt
import com.seer.trick.helper.StringHelper.splitTrim
import org.slf4j.LoggerFactory

/**
 * 记录与 RBK 请求响应报文。
 * 不用于诊断信道问题。
 */
object RbkLog : EventListener<String> {

  private val logger = LoggerFactory.getLogger("RbkLog")

  @Volatile
  var ignoredRbkApiNoList: Set<Int> = setOf(1100)
    private set

  init {
    BzConfigManager.eventBus.listeners += this
  }

  fun init() {
    reloadIgnoredRbkApiNoList("init")
    // TODO 如果还未配置过，最好将默认的 1100 会写到 bz-config.json 文件中，否则界面上的控制会有歧义:
    //  1. 用户未修改过配置，默认会不打印关于 1100 的记录。
    //  2. 用户已修改配置，且期望打印所有 rbk api 的记录。
  }

  /**
   * 系统初始化的时候加载一次，车队管理的配置变更时修改一次。
   */
  private fun reloadIgnoredRbkApiNoList(remark: String) {
    val ignoredRbkApiNo = BzConfigManager.getByPathAsString("ScWcs", "robotCtrl", "ignoredRbkApiNo")
      ?: return // 还未人为设置过相关指令编号，默认忽略 1100 即可。

    ignoredRbkApiNoList = if (ignoredRbkApiNo.isEmpty()) {
      // 还未人为设置过相关指令编号，默认忽略 1100 即可。
      emptySet() // 置空
    } else {
      // 直接赋值。如果比较新值旧值是否相同，都得 toString, 并没有多大的优化。
      splitTrim(ignoredRbkApiNo, ",").mapNotNull { anyToInt(it) }.toSet()
    }
    logger.info("RbkLog config updated: ignoredRbkApiNoList=$ignoredRbkApiNoList, remark=$remark")
  }

  override fun onEvent(e: String) {
    reloadIgnoredRbkApiNoList("config changed, e=$e")
  }

  fun log(robotName: String, apiNo: Int, msg: String) {
    if (apiNo in ignoredRbkApiNoList) return
    logger.info("[$robotName|$apiNo]$msg")
  }
}