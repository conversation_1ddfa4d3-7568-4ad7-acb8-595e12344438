package com.seer.trick.fleet.traffic.distributed.prevent

import com.seer.trick.fleet.traffic.distributed.helper.LogParseHelper
import com.seer.trick.fleet.traffic.distributed.lock.move.SpaceLockCalculate
import com.seer.trick.fleet.traffic.distributed.map.MapService
import com.seer.trick.fleet.traffic.distributed.plan.model.PathAction
import com.seer.trick.fleet.traffic.distributed.plan.model.PathType
import org.slf4j.LoggerFactory
import kotlin.math.min

class PreventSearch {

  private val logger = LoggerFactory.getLogger(javaClass)

  companion object {
    private const val MAX_SIZE = 40
  }

  fun calculate(prevent: PreventRequestInfo, preRotate: Boolean): PreventRequestInfo {
    buildTable(prevent)
    // 初始化冲突节点
    initConflict(prevent)
    // 优先级的处理
    sortPriority(prevent)
    // 对冲突做处理
    processConflict(prevent, preRotate)
//    processSpace(prevent)
    return prevent
  }

  // 对于运行中的机器人进行排序
  private fun sortPriority(prevent: PreventRequestInfo) {
    val runningRobots = prevent.runningRobots
    val robots = runningRobots.filter { it.priority > 0 }.sortedBy { it.priority }.toMutableList()
    val newRobots = runningRobots.filter { it.priority <= 0 }.sortedBy { it.restPath.size }.toMutableList()
    var index = 1
    for (robot in robots) {
      robot.priority = index++
    }
    for (robot in newRobots) {
      robot.priority = index++
      robots.add(robot)
    }
    val robotMap = robots.associateBy { it.robotName }
    val conflicts = prevent.conflict
    for (conflict in conflicts) {
      // 相向冲突 考虑其实位置，或部分已在冲突中的情况
      val robotA = robotMap[conflict.robotA] ?: continue
      val robotB = robotMap[conflict.robotB] ?: continue

      when (conflict.type) {
        // 已经在冲突中的乙方优先级高
        ConflictType.OPPOSE -> {
          val aiA = robotA.allocateIndex
          val aiB = robotB.allocateIndex
          val pathActionA = robotA.reserveTable[conflict.indexA.first()] ?: continue
          val pathActionB = robotB.reserveTable[conflict.indexB.first()] ?: continue
          // A 在 冲突外， B 在冲突中 ， A 的优先级小于 B
          if ((aiA < pathActionA.index && aiB >= pathActionB.index && robotA.priority < robotB.priority) ||
            (aiA >= pathActionA.index && aiB < pathActionB.index && robotA.priority > robotB.priority)
          ) {
            val priority = robotA.priority
            robotA.priority = robotB.priority
            robotB.priority = priority
          }
        }
        // 跟随考虑谁先谁后
        ConflictType.FLOW -> {
          // 需要优先级交换
          if ((conflict.indexA.first() < conflict.indexB.first() && robotA.priority > robotB.priority) ||
            (conflict.indexA.first() > conflict.indexB.first() && robotA.priority < robotB.priority)
          ) {
            val priority = robotA.priority
            robotA.priority = robotB.priority
            robotB.priority = priority
          }
        }

        else -> {}
      }
    }
    // 使用新数据
    prevent.runningRobots.removeAll(prevent.runningRobots)
    prevent.runningRobots.addAll(robotMap.values.sortedBy { it.priority })
    logger.info("priority is ${LogParseHelper.priorityLog(prevent)}")
    // 对每一个机器人，构建其锁闭信息
    prevent.runningRobots.forEach {
      buildSpaceLock(it)
    }
  }

  private fun buildSpaceLock(robotInfo: PreventRobotInfo) {
    robotInfo.reserveTable.values.forEach {
      buildPathSpaceLock(it, robotInfo.sceneId, robotInfo.groupName)
    }
  }

  private fun buildPathSpaceLock(action: PathAction, sceneId: String, groupName: String) {
    // 取所有的路径
    if (action.runLock == null && pointNotInAvoidArea(action, sceneId, action.mapName)) {
      action.startLock = SpaceLockCalculate
        .robotAndContainerStaticSpaceLock(
          robotName = action.robotName,
          sceneId = sceneId,
          groupName = groupName,
          robotHeading = action.robotInHeading,
          point = action.start,
          mapName = action.mapName,
          container = action.containerName,
          containerHeading = action.containerInHeading,
        )
      action.runLock = SpaceLockCalculate.moveSpaceLock(action)
      action.targetLock = SpaceLockCalculate
        .robotAndContainerStaticSpaceLock(
          robotName = action.robotName,
          sceneId = sceneId,
          groupName = groupName,
          robotHeading = action.robotOutHeading,
          point = action.target,
          mapName = action.mapName,
          container = action.containerName,
          containerHeading = action.containerOutHeading,
        )
    }
  }

  private fun pointNotInAvoidArea(action: PathAction, sceneId: String, mapName: String): Boolean = !(
    MapService.findPointInAvoidArea(sceneId, mapName, action.start.pointName) &&
      MapService.findPointInAvoidArea(sceneId, mapName, action.target.pointName)
    )

  // 处理冲突
  private fun processConflict(prevent: PreventRequestInfo, preRotate: Boolean): PreventRequestInfo {
    val runningRobots = prevent.runningRobots
    val conflict = prevent.conflict
    val robotMap = runningRobots.associateBy { it.robotName }
    for (i in 0 until runningRobots.size) {
      val robot = runningRobots[i]
      for (j in 0 until i) {
        // todo 每一个机器人和 高优先级的机器人做比较
      }
      var conflictNodes =
        conflict.filter { it.robotA == robot.robotName || it.robotB == robot.robotName }.toMutableList()

      // 对于解死锁后的情况，仅处理冲突的情况
      if (conflictNodes.size > 1) {
        conflictNodes.map {
          if (it.type == ConflictType.FLOW) {
            conflict.remove(it)
          }
        }
        conflictNodes = conflictNodes.filter { it.type == ConflictType.OPPOSE }.toMutableList()
      }
      // 对于其它车的冲突记录
      val conflictIndex: MutableMap<Int, MutableMap<String, Int>> = mutableMapOf()
      for (conflictNode in conflictNodes) {
        // todo 冲突处理
        conflict.remove(conflictNode)
        val robotB = robotMap[selectOtherRobot(conflictNode, robot.robotName)]!!
        val result = processTwoRobotConflict(conflictNode, robot, robotB, preRotate)
        if (result.isNotEmpty()) {
          for (j in 0 until result.size step 2) {
            val (robotAName, indexA) = result[j] // 走
            val (robotBName, indexB) = result[j + 1] // 等
            // 处于冲突中，将其冲突的删除，并将所有相关的冲突节点删除
            if (indexA == -1) {
              // 如果一方已经到达终点了，则不需要做处理
              if ((robot.restPath.last().stop() && robot.restPath.last().index == robot.allocateIndex) ||
                (robotB.restPath.last().stop() && robotB.restPath.last().index == robotB.allocateIndex)
              ) {
                break
              }
              // 将其加入到对方的死锁列表中
              if (robotBName == robot.robotName) {
                robotB.deadlocks.add(robotBName)
                robot.deadlocks.add(robotAName)
              } else {
                robot.deadlocks.add(robotBName)
                robotB.deadlocks.add(robotAName)
              }
              break
            }
            if (robotBName == robot.robotName) {
              val mutableMap = robot.disableTable.getOrDefault(indexB, mutableMapOf())
              mutableMap[robotAName] = indexA
              robot.disableTable[indexB] = mutableMap
            } else {
              val mutableMap = conflictIndex.getOrDefault(indexA, mutableMapOf())
              mutableMap[robotBName] = indexB
              conflictIndex[indexA] = mutableMap
            }
          }
        }
      }
      // 构建冲突
      for ((k, ci) in conflictIndex) {
        for ((robotName, index) in ci) {
          val robotB = robotMap[robotName] ?: continue
          val mutableMap = robotB.disableTable.getOrDefault(index, mutableMapOf())
          mutableMap[robot.robotName] = k
          robotB.disableTable[index] = mutableMap
        }
      }
    }
    logger.debug("conflict disable table ${LogParseHelper.disableTableLog(prevent)}")
    return prevent
  }

  // 处理两个机器人之间的冲突
  private fun processTwoRobotConflict(
    conflictNode: ConflictNode,
    robotA: PreventRobotInfo,
    robotB: PreventRobotInfo,
    preRotate: Boolean,
  ): MutableList<Pair<String, Int>> {
    logger.debug(
      "process conflict ${robotA.robotName} allocate index ${robotA.allocateIndex} | " +
        "${robotB.robotName} allocate index ${robotB.allocateIndex} | conflict node $conflictNode",
    )
    val robotAFirstIndex =
      if (robotA.robotName == conflictNode.robotA) conflictNode.indexA.first() else conflictNode.indexB.first()
    val robotBFirstIndex =
      if (robotB.robotName == conflictNode.robotB) conflictNode.indexB.first() else conflictNode.indexA.first()
    val startConflictIndexA = robotA.reserveTable[robotAFirstIndex]?.index
    val startConflictIndexB = robotB.reserveTable[robotBFirstIndex]?.index
    if (startConflictIndexA == null || startConflictIndexB == null) {
      logger.error(
        "start ${robotA.robotName} conflict index is $startConflictIndexA or" +
          " start ${robotB.robotName} conflict index is $startConflictIndexB",
      )
      return mutableListOf()
    }
    if (startConflictIndexA <= robotA.allocateIndex && startConflictIndexB <= robotB.allocateIndex) {
      // 冲突已成， 交给解死锁处理
      if (conflictNode.type == ConflictType.FLOW) {
        logger.error("flow conflict can not be resolved!! !!")
        return mutableListOf()
      }
      // -1 表示都冲突
      return mutableListOf(robotA.robotName to -1, robotB.robotName to -1)
    } else if (startConflictIndexA <= robotA.allocateIndex) {
      // A 在冲突中
      return oneInConflict(conflictNode, robotA, robotB, preRotate)
    } else if (startConflictIndexB <= robotB.allocateIndex) {
      // B 在冲突中
      return oneInConflict(conflictNode, robotB, robotA, preRotate)
    }
    // 跟随还不在冲突中，这个时候不好判断谁先谁后进入冲突范围，待后面进入后再计算
    if (conflictNode.type == ConflictType.FLOW) {
      robotA.flowRobots.add(robotB.robotName)
      robotB.flowRobots.add(robotA.robotName)
      return mutableListOf()
    }
    // 都不在冲突中
    return if (calculateCost(conflictNode, robotA, robotB) == robotA.robotName) {
      oneInConflict(conflictNode, robotA, robotB, preRotate)
    } else {
      oneInConflict(conflictNode, robotB, robotA, preRotate)
    }
  }

  fun calculateCost(conflictNode: ConflictNode, robotA: PreventRobotInfo, robotB: PreventRobotInfo): String {
    // 优先级
    val priorityA = robotA.priority
    val priorityB = robotB.priority
    val robotAFirstIndex =
      if (robotA.robotName == conflictNode.robotA) conflictNode.indexA.first() else conflictNode.indexB.first()
    val robotBFirstIndex =
      if (robotB.robotName == conflictNode.robotB) conflictNode.indexB.first() else conflictNode.indexA.first()
    val distanceA = getDistance(robotA.reserveTable, robotAFirstIndex)
    val distanceB = getDistance(robotB.reserveTable, robotBFirstIndex)
    return if ((priorityA * 0.5 + distanceA * 0.2) <=
      (priorityB * 0.5 + distanceB * 0.2)
    ) {
      robotA.robotName
    } else {
      robotB.robotName
    }
  }

  private fun getDistance(reserveTable: MutableMap<Int, PathAction>, index: Int): Double = reserveTable.map {
    val dis = if (it.key < index) {
      if (it.value.isRotate()) {
        1.0
      } else {
        it.value.target.distance(it.value.start)
      }
    } else {
      0.0
    }
    dis
  }.sum()

  private fun oneInConflict(
    conflictNode: ConflictNode,
    robotA: PreventRobotInfo,
    robotB: PreventRobotInfo,
    preRotate: Boolean,
  ): MutableList<Pair<String, Int>> {
    val result: MutableList<Pair<String, Int>> = mutableListOf()
    val tableA = robotA.reserveTable
    val tableB = robotB.reserveTable
    val indexAs = if (robotA.robotName == conflictNode.robotA) conflictNode.indexA else conflictNode.indexB
    val indexBs = if (robotA.robotName == conflictNode.robotA) conflictNode.indexB else conflictNode.indexA
    if (conflictNode.type == ConflictType.FLOW) {
      val differ = indexAs.first() - indexBs.first()
      for (i in indexAs) {
        val pathA = tableA[i]
        if (pathA != null && pathA.isRotate()) {
          var j = 1
          while ((i - differ - j) >= 0 &&
            pathA.runLock?.checkCollision(tableB[i - differ - j]?.runLock!!) == true
          ) {
            j++
          }
          if ((i - differ - j) < 0) {
            logger.error("${robotA.robotName} and ${robotB.robotName} in flow conflict !!")
            return mutableListOf(robotA.robotName to -1, robotB.robotName to -1)
          }
          if (j > 1 && (i - differ - j) >= 0) {
            result.add(robotA.robotName to i)
            val action = tableB[i - differ - j]
            val bj = if (preRotate &&
              action != null &&
              (i - differ - j) > 0 &&
              (action.type == PathType.ROTATE || action.type == PathType.TURNING)
            ) {
              i - differ - j - 1
            } else {
              i - differ - j
            }
            result.add(robotB.robotName to bj)
          }
        }
      }
      return result
    }

    val pathA = tableA[indexAs.last()]
    var indexB = indexBs.first()
    while (indexB >= 0 && pathA?.runLock?.checkCollision(tableB[indexB]?.targetLock!!) == true) {
      indexB--
    }
    if (indexB < 0 || tableB[indexB]?.index!! < robotB.allocateIndex) {
      if (indexB == -1 &&
        robotB.allocateIndex == -1L &&
        pathA?.runLock?.checkCollision(robotB.reserveSpaceLock) != true
      ) {
        return mutableListOf(robotA.robotName to indexAs.last(), robotB.robotName to 0)
      }
      logger.error("${robotA.robotName} and ${robotB.robotName} in oppose conflict !!")
      return mutableListOf(robotA.robotName to -1, robotB.robotName to -1)
    }
    if (preRotate &&
      (tableB[indexB]?.type == PathType.ROTATE || tableB[indexB]?.type == PathType.TURNING) &&
      indexB > 0
    ) {
      indexB--
    }
    return mutableListOf(robotA.robotName to indexAs.last(), robotB.robotName to indexB)
  }

  // 选择机器人编码
  private fun selectOtherRobot(conflictNode: ConflictNode, robotName: String): String =
    if (conflictNode.robotA == robotName) conflictNode.robotB else conflictNode.robotA

  /**
   * 构建预分配表
   */
  private fun buildTable(prevent: PreventRequestInfo): PreventRequestInfo {
    val runningRobots = prevent.runningRobots
    logger.info("process buildReserveTable! ${runningRobots.size}")
    for (robot in runningRobots) {
      val reserveTable: MutableMap<Int, PathAction> = mutableMapOf()
      val restPath = robot.restPath
      for (i in 0 until min(restPath.size, MAX_SIZE)) {
        reserveTable[i] = restPath[i]
      }
      robot.reserveTable = reserveTable
      robot.disableTable.clear()
      robot.flowRobots.clear()
      robot.deadlocks.clear()
    }
    logger.info("prevent index is ${LogParseHelper.reserveTableLog(prevent)}")
    return prevent
  }

  private fun oneReserveTableLog(reserveTable: MutableMap<Int, PathAction>): MutableMap<Int, String> {
    val t: MutableMap<Int, String> = mutableMapOf()
    for (table in reserveTable) {
      t[table.key] = table.value.start.pointName + "-" + table.value.target.pointName
    }
    return t
  }

  // 初始化冲突列表
  private fun initConflict(prevent: PreventRequestInfo): PreventRequestInfo {
    logger.debug("process init conflict!")
    val robots = prevent.runningRobots
    for (i in 0 until robots.size) {
      val robotA = robots[i]
      for (j in i + 1 until robots.size) {
        val robotB = robots[j]
        prevent.conflict.addAll(checkTwoRobotConflict(robotA, robotB))
      }
    }
    return prevent
  }

  // 检查两个机器人之间的冲突
  private fun checkTwoRobotConflict(robotA: PreventRobotInfo, robotB: PreventRobotInfo): List<ConflictNode> {
    val conflict: MutableList<ConflictNode> = mutableListOf()
    val tableA = robotA.reserveTable
    val tableB = robotB.reserveTable
    var min = -1 // 冲突最小的索引
    var minB = 0
    for (ai in 0 until tableA.size) {
      if (ai <= min) continue
      val pathActionA = tableA[ai]
      for (bi in minB until tableB.size) {
        val pathActionB = tableB[bi]
        // 检测是否存在冲突
        if (pathActionA?.target?.pointName.equals(pathActionB?.target?.pointName)) {
          var conflictNode =
            buildConflictArea(robotA.robotName, tableA, robotB.robotName, tableB, ai, bi, robotA.mapName)
          var back = false
          // 解死锁折返情况
          if (conflictNode.type == ConflictType.FLOW) {
            // 检查是否存在折返
            val cIndexB = checkNextConflict(tableB, pathActionA, conflictNode.indexB.last())
            if (cIndexB != null) {
              conflictNode =
                buildConflictArea(robotA.robotName, tableA, robotB.robotName, tableB, ai, cIndexB, robotA.mapName)
              back = true
            } else {
              val cIndexA = checkNextConflict(tableA, pathActionB, conflictNode.indexA.last())
              if (cIndexA != null) {
                conflictNode =
                  buildConflictArea(robotB.robotName, tableB, robotA.robotName, tableA, bi, cIndexA, robotA.mapName)
                back = true
              }
            }
          }
          if (conflictNode.conflictPoint.size <= 1 && !back) {
            break
          }
          conflict.add(conflictNode)
          min = conflictNode.indexA.last()
          minB = conflictNode.indexB.last() + 1
          logger.debug(
            "${robotA.robotName} table ${oneReserveTableLog(tableA)} | " +
              "${robotB.robotName} table ${oneReserveTableLog(tableB)}| init conflictNode is $conflictNode",
          )
          break
        }
      }
    }
    return conflict
  }

  /**
   * 检查下一次的冲突
   * */
  private fun checkNextConflict(table: MutableMap<Int, PathAction>, pathAction: PathAction?, min: Int): Int? {
    for (i in min until table.size) {
      val pathActionNext = table[i]
      if (pathAction?.target?.pointName.equals(pathActionNext?.target?.pointName)) {
        return i
      }
    }
    return null
  }

  // 构建冲突的区域
  private fun buildConflictArea(
    robotA: String,
    tableA: MutableMap<Int, PathAction>,
    robotB: String,
    tableB: MutableMap<Int, PathAction>,
    ai: Int,
    bi: Int,
    mapName: String,
  ): ConflictNode {
    val maxA = tableA.size - 1
    val maxB = tableB.size - 1
    // 冲突存在两种情况，一种是 A、B 同向移动，另一种是 A、B 逆向移动
    val points: MutableList<String> =
      mutableListOf(tableA[ai]?.target?.pointName!!)
    val indexA = mutableListOf(ai)
    val indexB = mutableListOf(bi)
    if (ai < maxA &&
      bi < maxB &&
      (
        (
          tableA[ai + 1]?.isRotate() != true &&
            tableA[ai + 1]?.target?.pointName.equals(tableB[bi + 1]?.target?.pointName)
          ) ||
          (
            tableA[ai + 1]?.isRotate() == true &&
              tableA[ai + 2]?.target?.pointName.equals(tableB[bi + 1]?.target?.pointName)
            ) ||
          (
            tableB[bi + 1]?.isRotate() == true &&
              tableB[bi + 2]?.target?.pointName.equals(tableA[ai + 1]?.target?.pointName)
            ) ||
          tableA[ai + 1]?.isRotate() == true &&
          tableB[bi + 1]?.isRotate() == true &&
          tableB[bi + 2]?.target?.pointName.equals(tableA[ai + 2]?.target?.pointName)
        )
    ) {
      // 表示同向移动
      var a = ai + 1
      var b = bi + 1
      if (a <= maxA && tableA[a]?.isRotate() == true) {
        indexA.add(a++)
      }
      if (b <= maxB && tableB[b]?.isRotate() == true) {
        indexB.add(b++)
      }
      while (a <= maxA && b <= maxB && tableA[a]?.target?.pointName.equals(tableB[b]?.target?.pointName)) {
        points.add(tableA[a]?.target?.pointName!!)
        indexA.add(a++)
        indexB.add(b++)
      }
      if (a <= maxA && tableA[a]?.isRotate() == true) indexA.add(a)
      if (b <= maxB && tableB[b]?.isRotate() == true) indexB.add(b)
      return ConflictNode(
        robotA = robotA,
        robotB = robotB,
        conflictPoint = points,
        indexA = indexA,
        indexB = indexB,
        mapName = mapName,
        type = ConflictType.FLOW,
      )
    }

    // 若逆向移动， 也有可能十字路口
    var a = ai + 1
    if (a <= maxA && tableA[a]?.isRotate() == true) indexA.add(a++)
    if (bi + 1 <= maxB && tableB[bi + 1]?.isRotate() == true) indexB.add(0, bi + 1)
    var b = bi - 1
    if (b >= 0 && tableB[b]?.isRotate() == true) indexB.add(b--)
    while (a <= maxA && b >= 0 && tableA[a]?.target?.pointName.equals(tableB[b]?.target?.pointName)) {
      points.add(tableA[a]?.target?.pointName!!)
      indexA.add(a++)
      indexB.add(b--)
      if (a <= maxA && tableA[a]?.isRotate() == true) indexA.add(a++)
      if (b >= 0 && tableB[b]?.isRotate() == true) indexB.add(b--)
    }
    if (b + 1 <= maxB &&
      tableB[b + 1]?.isRotate() == true &&
      !points.contains(tableB[b + 1]?.target?.pointName) &&
      !(a <= maxA && tableA[a]?.target?.pointName.equals(tableB[b + 1]?.target?.pointName))
    ) {
      indexB.removeLast()
    }
    return ConflictNode(
      robotA = robotA,
      robotB = robotB,
      conflictPoint = points,
      indexA = indexA,
      indexB = indexB.reversed(),
      mapName = mapName,
      type = ConflictType.OPPOSE,
    )
  }
}