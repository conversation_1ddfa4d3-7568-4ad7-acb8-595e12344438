package com.seer.trick.fleet.order

import com.seer.trick.BzError
import com.seer.trick.fleet.diagnosis.RobotOrderPrediction
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.order.DispatchOrderService.estimateOrderCost
import com.seer.trick.fleet.service.MapService.NOT_ACHIEVABLE_COST
import com.seer.trick.fleet.service.RobotBinService
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.RobotService
import com.seer.trick.fleet.service.SceneRuntime
import org.slf4j.LoggerFactory
import java.util.*

/**
 * 兼容将来，支持带业务单避让、停靠、充电
 */
class DispatchOrderWorker(private val sr: SceneRuntime) {

  private val logger = LoggerFactory.getLogger(javaClass)

  private var allocations: List<PreAllocation> = emptyList()

  // 本次不再检查运单集
  private val checkedOrders: MutableSet<String> = HashSet()

  /**
   * 非阻塞，相当于发了一次请求。触发一次全局派单。提交给派单线程然后立即返回。不能报错。
   * 更新场景时，阻塞，不能派新的。
   *
   * 派单过程
   * 1. 过滤掉不能接任何单的机器人（包括自动单），如不接单、断连的机器人
   * 2. 电量过低的机器人
   *   1.1 如果正在充电，继续
   *   1.2 如果正在去充电点，继续
   *   1.3 如果正在做业务单，继续，不能打断
   *   1.4 如果正在停靠，打断停靠，尝试生成充电运单
   *   1.5 尝试生成充电运单
   * 3. 派业务单
   *  3.1 可能需要打断停靠点，可打断的充电单
   * 4. 可充电的
   *   4.1 如果正在充电，继续
   *   4.2 如果正在去充电点，继续
   *   4.3 如果正在做业务单，继续，不能打断
   *   4.4 如果正在停靠，打断停靠，尝试生成充电运单
   *   4.5 尝试生成充电运单
   * 5. 无运单、可停靠的，停靠
   * 6. 虽然过低但无法充电（充电桩不够），不能接业务单，但能停靠
   * 7. 停靠、避让过程中，电量低到建议、强制充电电量，且有充电桩的，可以取消停靠、避让，去充电
   */
  fun dispatch() {
    decideStartPoints()

    // 先安排强充
    ChargingService.checkForceCharging(sr)

    val dispatchProfile = DispatchProfile(sr.dispatchProfile.loop + 1)
    sr.dispatchProfile = dispatchProfile
    dispatchProfile.timeCost = System.currentTimeMillis()

    // 潜在的可以被派单或重新派单的运单
    var orders = cleanAndReturnMayBeAssigned()
    orders = orders.filter { RobotOrderPrediction.checkOrderBeAllocatedOrAllocatedAgain(it) == null }
    dispatchProfile.ordersPass = orders.size

    // 潜在的可以被分派或重新分派的机器人
    var robots = sr.listRobots()
    robots = robots.filter {
      RobotService.isRobotOkToAcceptOrder(it) && RobotOrderPrediction.checkRobotAvailableForNewBzOrders(it) == null
    }
    dispatchProfile.workableRobotsNum = robots.size // 可工作的机器人数量

    // 先标记没有可用机器人，等后续实际分派的了的，移除标记
    for (order in orders) {
      order.allocationReject = RejectReason("NoEnoughRobot")
    }

    if (orders.isNotEmpty() && robots.isNotEmpty()) {
      // 列出所有潜在的分派可能
      allocations = sr.matchingAlgorithm.match(sr, orders, robots)
      dispatchProfile.preNum = allocations.size

      if (allocations.isNotEmpty()) {
        for (alloc in allocations) {
          tryAllocate(alloc)
        }
      }
    }

    dispatchProfile.timeCost = System.currentTimeMillis() - dispatchProfile.timeCost
    sr.dispatchProfile = dispatchProfile

    // 先检查充电，再检查停靠！
    // 注意要在前面派单时检查是否需要强冲（不派单）
    ChargingService.tryCharging(sr)

    // 再尝试停靠
    ParkingService.tryToPark(sr)
  }

  /**
   * 设置每个机器人用于规划的当前位置 startPointNameForDispatching
   * TODO 不要用 pointName，用 pointCache
   */
  private fun decideStartPoints() {
    for (rr in sr.robots.values) {
      val stand = rr.selfReport?.stand
      rr.startPointNameForDispatching = if (stand != null) {
        if (!stand.pointName.isNullOrBlank()) {
          stand.pointName
        } else if (!stand.pathPositions.isNullOrEmpty()) {
          stand.pathPositions[0].fromPointName
        } else {
          null
        }
      } else {
        null
      }
    }
  }

  /**
   * 标记没有后续步骤、封口的运单，完成。
   * 清理已完成的运单。
   * 然后返回所有剩余运单，潜在可分派的。
   */
  private fun cleanAndReturnMayBeAssigned(): List<OrderRuntime> = sr.withOrderLock {
    val orders = sr.orders.values.toList()
    if (orders.isEmpty()) return@withOrderLock orders

    orders.filter { or ->
      // 删除已经终态的
      if (or.order.status.finalStatus) {
        sr.orders.remove(or.id)
        return@filter false
      }

      // 故障的不分派
      if (or.order.fault) return@filter false

      if (or.order.noMoreStep) {
        val rr = or.order.actualRobotName?.let { sr.robots[it] }
        logger.info("Mark order done for no more step, order=${or.id}, robot=$rr")
        OrderDoneService.markOrderDone(sr, or, rr)
        return@filter false
      }

      // 如果运单不能被撤回或步骤不能被撤回，都不用考虑再分派了
      or.withdrawOrderAllowed && or.withdrawStepAllowed
    }
  }

  private fun tryAllocate(alloc: PreAllocation) {
    if (checkedOrders.contains(alloc.orderId)) return

    // 要被分的单子
    val or1 = sr.orders[alloc.orderId] ?: return
    val order1 = or1.order

    // 再次检查运单状态
    if (RobotOrderPrediction.checkOrderBeAllocatedOrAllocatedAgain(or1) != null) return

    val rr1 = sr.robots[alloc.robotName] ?: return

    // 再次检查机器人可接单性
    if (RobotOrderPrediction.checkRobotAvailableForNewBzOrders(rr1) != null) return

    if (alloc.robotName == order1.actualRobotName) {
      // 分配未改变
      checkedOrders += alloc.orderId
      return
    }

    val actualRobotName = order1.actualRobotName
    if (!actualRobotName.isNullOrEmpty()) {
      val rr2 = sr.robots[actualRobotName] ?: return
      // 检查是否没有本质的改变，需要计算前一分派当前的成本
      val cost = try {
        val result = estimateOrderCost(or1, rr2)
        if (result == NOT_ACHIEVABLE_COST) return else result
      } catch (_: BzError) {
        return
      }
      // val oldAlloc = allocations.find { it.orderId == order.id && it.robotName == order.actualRobotName }
      if (cost - alloc.cost < sr.config.decideWithdrawnMinCost()) {
        // 没有本质改变
        checkedOrders += alloc.orderId
        return
      }
    }

    // 有空库位（抛出掉卸货），不用挤走别的单了；否则挤走一个单子；否则没有位置了
    val targetBin = findBestBin(rr1, alloc) ?: return

    // rr1 是否需要撤单，rr1wOr 是 rr1 要撤的但
    var rr1wOr: OrderRuntime? = null
    if (!targetBin.orderId.isNullOrBlank()) {
      // 挤走一个单子
      rr1wOr = rr1.orders[targetBin.orderId]
      if (rr1wOr == null) {
        logger.error("Robot $rr1 has order for bin $targetBin")
        return
      }
    }

    // 要分派的运单要从这个机器人撤单
    var rr2: RobotRuntime? = null
    if (!order1.actualRobotName.isNullOrBlank()) {
      rr2 = sr.robots[order1.actualRobotName]
      if (rr2 == null) {
        logger.error("Actual robot ${order1.actualRobotName} of order $or1 is not found")
        return
      }
    }

    doAllocate(alloc, rr1, or1, rr1wOr, rr2, targetBin)
  }

  /**
   * 实际尝试分派。把所有的撤单、分派放在一起。
   * TODO 锁内进行。锁有点大。
   */
  private fun doAllocate(
    alloc: PreAllocation,
    rr1: RobotRuntime, // 分派给这个机器人
    or1: OrderRuntime, // 分派这个运单
    rr1wOr: OrderRuntime?, // 从机器人 1 撤一个运单
    rr2: RobotRuntime?, // 从运单当前机器人 2 中撤这个运单
    targetBin: RobotBin,
  ) = sr.withOrderLock {
    // 是否要取消自动单
    val autoOr = rr1.autoOrder?.let { sr.orders[it.orderId] }

    var desc = "To assign order ${alloc.orderId} to robot $rr1, targetBin=${targetBin.index}."
    if (autoOr != null) {
      desc += " Cancel auto order ${autoOr.id}(${autoOr.order.status}) from robot $rr1."
    }
    if (rr1wOr != null) {
      desc += " Withdraw or reset bz order ${rr1wOr.id}(${rr1wOr.order.status}) from robot $rr1."
    }
    if (rr2 != null) {
      desc += " Withdraw or reset order ${alloc.orderId}(${or1.order.status}) from current robot $rr2."
    }

    // 先再次检查
    if (!RobotService.isRobotOkToAcceptOrder(rr1) ||
      RobotOrderPrediction.checkRobotAvailableForNewBzOrders(rr1) != null
    ) {
      return@withOrderLock
    }

    if (rr1wOr != null) {
      if (!(rr1wOr.withdrawOrderAllowed && rr1wOr.withdrawStepAllowed)) return@withOrderLock
    }
    if (rr2 != null) {
      if (!RobotService.isRobotOkToAcceptOrder(rr2)) return@withOrderLock
      if (!(or1.withdrawOrderAllowed && or1.withdrawStepAllowed)) return@withOrderLock
    }

    logger.info(desc)

    // 开始先撤单
    if (autoOr != null) {
      // 直接取消自动单
      OrderCancelService.cancelOrder(sr, autoOr.id)
    }

    // 从 rr1 挤走单子
    if (rr1wOr != null) {
      withdrawOrResetOrder(rr1, rr1wOr, desc)
      ++sr.dispatchProfile.withdrawnNum
      checkedOrders += rr1wOr.id
    }

    // 把 or1 从 rr2 抢过来
    if (rr2 != null) {
      withdrawOrResetOrder(rr2, or1, desc)
      ++sr.dispatchProfile.withdrawnNum
    }

    // 不改机器人 cmdStatus，执行步骤时才改
    val newOrder = or1.order.copy(
      status = OrderStatus.Allocated,
      actualRobotName = rr1.robotName,
      oldRobots = OrderService.getOldRobotsContainsActualRobot(or1.order),
      robotAllocatedOn = Date(),
      dispatchCost = alloc.cost,
    )
    OrderService.updateAndPersistOrder(or1, newOrder, "Allocate to robot $rr1")

    rr1.orders[or1.id] = or1 // 把运单给机器人的运单池
    rr1.bins[targetBin.index] =
      RobotBin(targetBin.index, RobotBinStatus.Reserved, orderId = or1.id, containerId = or1.order.containerId)
    // 清理充电时间
    rr1.chargingOrderDoneOn = null

    RobotService.persistRobotRuntime(rr1)

    sr.dispatchProfile.allocationNum++
    or1.allocationReject = null
    checkedOrders += or1.id
  }

  /**
   * 判断能不能接单，能的话找个合适的库位
   */
  private fun findBestBin(rr: RobotRuntime, alloc: PreAllocation): RobotBin? {
    val emptyBinNum = rr.bins.count { it.status == RobotBinStatus.Empty }
    if (emptyBinNum > 0) {
      // 已卸货的运单数量
      val unloadedOrderNum = rr.orders.values.count { or ->
        // 注意只考虑业务单
        or.order.kind == OrderKind.Business && rr.bins.find { it.orderId == or.id } == null
      }
      // 已卸货后运单还在，库位变空，但仍不能用
      if (emptyBinNum > unloadedOrderNum) {
        // 随便一个
        return rr.bins.find { it.status == RobotBinStatus.Empty }
      }
    }

    return findReservedWorstOrderBin(rr, alloc)
  }

  /**
   * 找能被挤走的最差的运单；能被挤走的运单一定是未载货的。
   * 前面要检查机器人不能处于故障、取消、打断等状态。
   * 不能挤走优先级更高的。
   */
  private fun findReservedWorstOrderBin(rr: RobotRuntime, refAlloc: PreAllocation): RobotBin? {
    var worst: PreAllocation? = null
    var worstBin: RobotBin? = null

    for (bin in rr.bins) {
      if (bin.status != RobotBinStatus.Reserved) continue
      if (bin.orderId == null) {
        // logger.error("$rr:${bin.index} orderId:${bin.orderId} ")
        continue
      }

      // 找到要被分的运单，计算这个运单现在实际与机器人的成本
      val or = rr.orders[bin.orderId] ?: continue

      val executingStep = rr.executingStep?.getStep()
      // 如果当前库位对应的运单步骤正在执行且是不可重分派的，则选择其他库位
      if (executingStep != null && executingStep.orderId == bin.orderId && !executingStep.withdrawOrderAllowed) {
        continue
      }

      // 判断对应运单是否可以被二分
      if (RobotOrderPrediction.checkOrderBeAllocatedOrAllocatedAgain(or) != null) continue

      val cost = try {
        val result = estimateOrderCost(or, rr)
        if (result == NOT_ACHIEVABLE_COST) continue else result
      } catch (_: BzError) {
        continue
      }

      // 不挤走应该优先执行的或同等的
      if (DispatchOrderService.compareOrders(
          sr,
          refAlloc.priority,
          refAlloc.cost,
          refAlloc.createdOn,
          or.order.priority,
          cost,
          or.order.createdOn,
        ) >= 0
      ) {
        continue
      }

      val oldAlloc = PreAllocation(
        robotName = rr.robotName,
        orderId = bin.orderId,
        priority = or.order.priority,
        cost = cost,
        createdOn = or.order.createdOn,
      )

      if (worst == null ||
        DispatchOrderService.compareOrders(
          sr,
          oldAlloc.priority,
          oldAlloc.cost,
          oldAlloc.createdOn,
          worst.priority,
          worst.cost,
          worst.createdOn,
        ) < 0
      ) {
        worst = oldAlloc
        worstBin = bin
      }
    }

    if (worst == null) return null

    // 如果比最差的还差，不能换
    if (DispatchOrderService.compareOrders(
        sr,
        refAlloc.priority,
        refAlloc.cost,
        refAlloc.createdOn,
        worst.priority,
        worst.cost,
        worst.createdOn,
      ) >= 0
    ) {
      return null
    }

    // 成本虽然小，但差异不大，也不改变
    if (worst.priority == refAlloc.priority &&
      worst.cost - refAlloc.cost <= sr.config.decideWithdrawnMinCost()
    ) {
      return null
    }

    return worstBin
  }

  /**
   * 撤回正在执行的运单或重置未执行的运单。
   * 自动单不会被撤回：直接取消。
   */
  private fun withdrawOrResetOrder(rr: RobotRuntime, or: OrderRuntime, reason: String) {
    val sec = rr.executingStep
    if (sec != null && sec.orderId == or.id) {
      // 正在执行
      withdrawOrder(sec)
    } else {
      // 未在执行
      resetOrder(rr, or, reason)
    }
  }

  /**
   * 撤回运单
   */
  private fun withdrawOrder(sec: StepExecuteContext) {
    StepExecuteService.cancelExecuting(sec, "Withdraw order by order dispatch")

    val or = sec.or
    val rr = sec.rr

    // 运单
    val newOrder = or.order.copy(
      status = OrderStatus.ToBeAllocated,
      withdrawnCount = or.order.withdrawnCount + 1,
      actualRobotName = null,
      oldRobots = OrderService.getOldRobotsContainsActualRobot(or.order),
      robotAllocatedOn = null,
    )
    OrderService.updateAndPersistOrder(
      or,
      newOrder,
      "Withdraw executing order",
    )

    // 机器人
    rr.orders.remove(sec.orderId)
    RobotBinService.cancelBin(or, rr)
    RobotService.persistRobotRuntime(rr)
  }

  /**
   * 恢复（当前未在执行的）运单到待分配状态，已执行的步骤重新执行。
   */
  private fun resetOrder(rr: RobotRuntime, or: OrderRuntime, reason: String) = rr.sr.withOrderLock {
    logger.info("Reset Order $or. Reason=$reason. Robot=$rr.")

    or.coolingFrom = System.currentTimeMillis()

    // 运单
    val newOrder = or.order.copy(
      status = OrderStatus.ToBeAllocated,
      withdrawnCount = or.order.withdrawnCount + 1,
      actualRobotName = null,
      oldRobots = OrderService.getOldRobotsContainsActualRobot(or.order),
      robotAllocatedOn = null,
      //
      currentStepIndex = -1,
      doneStepIndex = -1,
    )
    OrderService.updateAndPersistOrder(or, newOrder, "Withdraw not executing order")

    // 步骤
    for (step in or.steps) {
      if (step.status == StepStatus.Done) {
        OrderService.updateAndPersistStep(
          or,
          step.copy(status = StepStatus.Executable),
          "Reset order, reset done step",
        )
      }
    }

    // 机器人
    rr.orders.remove(or.id)

    // 如果运单与机器人关联，但机器人储位上没有这个运单，表示运单已卸货
    // 但不管是要被分还是被挤走的运单，应该都不会处于已卸货的状态
    val bin = rr.bins.find { it.orderId == or.id }
    if (bin != null) rr.bins[bin.index] = RobotBin(bin.index)
    RobotService.persistRobotRuntime(rr)
  }
}