package com.seer.trick.fleet.traffic.distributed.context.domain

import java.util.concurrent.locks.*

data class RobotContext(
  val robotName: String, // 机器人编码

  val groupName: String, // 机器人组名称

  var mapName: String, // 地图名称

  val sceneId: String, // 场景 id

  var robotMotionType: RobotMotionType = RobotMotionType.ADVANCE, // 机器人行驶方式

  var robotChassisType: ChassisType = ChassisType.DIFF, // 机器人底盘类型

  var robotSalverNotRotate: Boolean = false, // 机器人货架是否可以旋转

) {

  val lock: Lock = ReentrantLock()

  /**
   * 机器人交控状态
   * */
  @Volatile
  var state = TrafficStatus.IDLE

  /**
   * 机器人基础信息
   * */
  var baseDomain = BaseDomain()

  /**
   * 机器人请求信息
   * */
  var request = RequestDomain()

  /**
   * 路径规划和执行信息
   * */
  var plan = PlanDomain()

  /**
   * 死锁记录
   * */
  var deadLock = DeadLockDomain()

  /**
   *  停止的时间
   * */
  @Volatile
  var pauseTime: Long = System.currentTimeMillis()
}