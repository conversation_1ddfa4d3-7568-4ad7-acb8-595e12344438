package com.seer.trick.fleet

import com.seer.trick.fleet.order.OrderRuntime
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.SceneRuntime
import org.slf4j.LoggerFactory

class FleetLog(val subject: FleetLoggerSubject) {

  private val logger = LoggerFactory.getLogger("Fleet")

  fun trace(sr: SceneRuntime, rr: RobotRuntime?, or: OrderRuntime?, msg: String) {
    logger.trace("{}|{}|{}|{}|{}", subject, sr.basic.name, rr?.robotName, or?.id, msg)
  }

  fun info(sr: SceneRuntime, rr: RobotRuntime?, or: OrderRuntime?, msg: String) {
    logger.info("{}|{}|{}|{}|{}", subject, sr.basic.name, rr?.robotName, or?.id, msg)
  }

  fun warn(sr: SceneRuntime, rr: RobotRuntime?, or: OrderRuntime?, msg: String) {
    logger.warn("{}|{}|{}|{}|{}", subject, sr.basic.name, rr?.robotName, or?.id, msg)
  }

  fun error(sr: SceneRuntime, rr: RobotRuntime?, or: OrderRuntime?, msg: String, e: Throwable? = null) {
    logger.error("{}|{}|{}|{}|{}", subject, sr.basic.name, rr?.robotName, or?.id, msg, e)
  }
}

enum class FleetLoggerSubject {
  TP2,
}