package com.seer.trick.fleet.traffic.distributed.deadlock.model

import com.seer.trick.fleet.traffic.distributed.block.BlockItem
import com.seer.trick.fleet.traffic.distributed.context.domain.RobotContext

/**
 *  解死锁消息管理
 * */
class DeadLockMessage(val robotName: String, val mapName: String, val context: RobotContext) {

  /**
   *  检查是否匹配
   * */
  var state: Boolean = false

  var blocks: MutableList<BlockItem> = mutableListOf()

  /**
   * 空闲车障碍物
   * */
  var idleRobots: MutableList<String> = mutableListOf()

  /**
   * 固定障碍物
   * */
  var fixedBlocks: MutableList<BlockItem> = mutableListOf()

  /**
   * 最小环
   * */
  var linkRobots: MutableList<String> = mutableListOf()

  /**
   * 邻域信息
   * */
  var neighborDomains: MutableMap<String, NeighborDomain> = mutableMapOf()

  /**
   * 阻挡物集合， todo 后期使用
   * */
  var blockMap: MutableMap<String, MutableList<String>> = mutableMapOf()

  /**
   * 需要解的车
   * */
  var calRobot: String? = null
}

class RobotNode(
  val pre: RobotNode?, // 前节点
  val robotName: String, // 当前节点
)