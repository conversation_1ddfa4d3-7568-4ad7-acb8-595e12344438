package com.seer.trick.fleet.traffic.distributed.lock.graph

import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper
import kotlin.math.abs
import kotlin.math.sqrt

class VeLine(val v1: Vector, val v2: Vector) {

  // 点在线上
  fun pointOnLine(v: Vector): Boolean {
    return AngleHelper.sameAngleInOneDegree(AngleHelper.getAngle(v1, v2), AngleHelper.getAngle(v1, v)) ||
      AngleHelper.sameAngleInOneDegree(AngleHelper.getAngle(v2, v1), AngleHelper.getAngle(v2, v))
//    return (v1.angleTo(v2) == v1.angleTo(v)
//        || v2.angleTo(v1) == v2.angleTo(v))
  }

  // 点在线内
  fun pointInLine(v: Vector): Boolean = pointOnLine(v) &&
    (v1.distanceTo(v) + v2.distanceTo(v)) == v1.distanceTo(v2)

  // 点投影到线上的距离
  fun vectorProjectToLine(v: Vector): Double {
    // 判断点在线上  or 线外
    val AP = Vector(v.x, v.y).subtract(v1)
    val AB = v2.subtract(v1)
    val cosA = AP.dot(AB) / (AP.getMagnitude() * AB.getMagnitude())
    if (cosA < 0) return v1.distanceTo(v)
    val BP = Vector(v.x, v.y).subtract(v2)
    val BA = v1.subtract(v2)
    val cosB = BP.dot(BA) / (BP.getMagnitude() * BA.getMagnitude())
    if (cosB < 0) return v2.distanceTo(v)
    // 投影在线段内
    val A = v2.y - v1.y
    val B = v1.x - v2.x
    val C = v2.x * v1.y - v1.x * v2.y
    return abs(A * v.x + B * v.y + C) / sqrt(A * A + B * B)
  }

  // 点投影到线段上的点
  fun projectPointOnLine(v: Vector): Vector {
    val ab = v2.subtract(v1)
    val ap = v.subtract(v1)

    val dotProduct = ap.dot(ab)
    val abLengthSq = ab.x * ab.x + ab.y * ab.y

    if (abLengthSq == 0.0) return v1 // 线段退化成一个点

    var t = dotProduct / abLengthSq
    t = maxOf(0.0, minOf(1.0, t)) // 限制 t 在 [0, 1] 范围内

    return Vector(v1.x + t * ab.x, v1.y + t * ab.y)
  }

  // 线段相交
  fun lineIntersect(l: VeLine): Boolean {
    val ac = v1.subtract(l.v1)
    val ab = v1.subtract(v2)
    val ad = v1.subtract(l.v2)
    val acb = ac.cross(ab)
    val adb = ad.cross(ab)
    if (acb * adb >= 0) return false

    val ca = l.v1.subtract(v1)
    val cb = l.v1.subtract(l.v2)
    val cd = l.v1.subtract(v2)
    val cad = ca.cross(cd)
    val cbd = cb.cross(cd)
    return cad * cbd < 0
  }
}