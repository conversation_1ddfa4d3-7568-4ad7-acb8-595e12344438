package com.seer.trick.fleet.seer

/**
 * 场景实体
 */
data class RdsCoreScene(
  val desc: String? = "",                                       // 描述
  val robotGroup: List<RdsCoreRobotGroup> = emptyList(),       // 机器人组
  val labels: List<RdsCoreLabel>? = null,                       // 标签
  val areas: List<RdsCoreAreas> = emptyList(),                 // 区域
  val blockGroup: List<Any>? = emptyList(),                    // 高级组关系
  val lifts: List<Any>? = emptyList(),                         // 电梯
  val doors: List<Any>? = emptyList(),                         // 门
  val binMonitors: List<Any>? = emptyList(),                   // 光电库位
  val binAreas: List<Any>? = emptyList(),                      // 库区
  val terminals: List<Any>? = emptyList(),                      // 设备（plc）
  val terminalGroups: List<Any>? = emptyList(),                 // 设备组（风淋门）
)

/**
 * 机器人组实体
 */
data class RdsCoreRobotGroup(
  val name: String = "",                              // 机器人组名称
  val robot: List<RdsCoreRobot>? = emptyList(),       // 机器人的集合
  val property: List<SmapMapProperty>? = emptyList(),  // 机器人组属性
  val desc: String? = "",                              // 机器人组描述
)

/**
 * 机器人实体
 */
data class RdsCoreRobot(
  val id: String = "",                                // 机器人id
  val property: List<SmapMapProperty>? = emptyList(),  // 机器人属性
)

/**
 * 机器人标签
 */
data class RdsCoreLabel(
  val name: String = "",                             // 标签名称
  val robotIds: List<String>? = null,         // 属于此标签下的机器人
  val property: List<SmapMapProperty>? = null,
  val desc: String? = "",
)

// 区域
data class RdsCoreAreas(
  val name: String = "",                            // 区域名称
  val pos: SmapPos? = SmapPos(0.0, 0.0, 0.0),
  val maps: List<AreaMaps> = emptyList(),          // 区域下地图集合
  val logicalMap: LogicalMap,                       // 场景元素
  val backgroundImage: Any?,
)

// rdscore 区域下的地图
data class AreaMaps(
  val robotId: String = "",                         // 机器人 id
  val mapName: String = "",                         // smap 地图名称
  val md5: String = "",                             // 地图的 md5
)

// 区域下的元素
data class LogicalMap(
  val advancedPoints: List<AdvancedPoints>? = emptyList(),   // 点
  val advancedCurves: List<AdvancedCurves>? = emptyList(),   // 线
  val advancedBlocks: List<AdvancedBlock>? = emptyList(),    // 高级区域
  val binLocationsList: List<BinLocationList>? = emptyList(),    // 库位
  val wallsList: List<Any>? = emptyList(),// 忽略
  val modelsList: List<Any>? = emptyList(),
  val windowsList: List<Any>? = emptyList(),
  val externalDeviceList: List<Any>? = emptyList(),
  val workAreaList: List<Any>? = emptyList(),
  val advancedAreaList: List<AdvancedArea>? = emptyList(),
)

data class AdvancedPoints(
  val className: String = "",                           // 点位类型
  val instanceName: String = "",                        // 名称
  val pos: SmapPos = SmapPos(0.0, 0.0, 0.0),     // 坐标
  val dir: Double? = null,                              // 方向
  val property: List<SmapMapProperty>? = null,          // 属性
  val ignoreDir: Boolean? = null,
  val desc: String = "",                                 // 描述
)

data class AdvancedCurves(
  val className: String = "",
  val instanceName: String = "",  // 线路 例：LM1-LM2 LM1 到 LM2 的方向
  val startPos: AdvancedCurvePoint, // 线路起点
  val endPos: AdvancedCurvePoint,// 线路终点
  val controlPos1: SmapPos? = null, // 高阶曲线控制点
  val controlPos2: SmapPos? = null,
  val controlPos3: SmapPos? = null,
  val controlPos4: SmapPos? = null,
  val property: List<SmapMapProperty>? = emptyList(),
  val desc: String? = "",
  val devices: List<Any>? = emptyList(),
)

data class AdvancedCurvePoint(
  val className: String? = "",
  val instanceName: String = "",
  val pos: SmapPos = SmapPos(0.0, 0.0, 0.0),
  val dir: Double? = null,                              // 方向
  val property: List<SmapMapProperty>? = null,          // 属性
  val ignoreDir: Boolean? = null,
  val desc: String = "",                                 // 描述
)

// core 场景高级区域
data class AdvancedBlock(
  val className: String = "",                // 类型
  val instanceName: String = "",             // 名称
  val posGroup: List<SmapPos> = emptyList(), // 方向
  val dir: Double,                           // 旋转
  val property: List<SmapMapProperty>? = emptyList(),
  val desc: String = "",
  val devices: List<Any>? = emptyList(),
)

data class AdvancedArea(
  val className: String,
  val instanceName: String,
  val posGroup: List<SmapPos>,
  val dir: Double,
  val desc: String,
  val property: List<SmapMapProperty>? = null,
  val attribute: SmapMapAttribute? = null,
)

data class BinLocationList(
  val binLocationList: List<BinLocation>? = emptyList(),
)

data class BinLocation(
  val className: String = "",
  val instanceName: String = "",
  val groupName: String? = "",
  val pointName: String = "",
  val pos: SmapPos = SmapPos(0.0, 0.0, 0.0),
  val property: List<SmapMapProperty>? = emptyList(),
  val desc: String? = "",
)



