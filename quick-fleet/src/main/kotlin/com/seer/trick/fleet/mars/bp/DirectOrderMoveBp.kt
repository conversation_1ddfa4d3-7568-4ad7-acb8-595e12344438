package com.seer.trick.fleet.mars.bp

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.falcon.domain.ParamObjectType
import com.seer.trick.helper.JsonHelper

class DirectOrderMoveBp : AbstractBp() {

  override fun process() {
    val location = mustGetBlockInputParam("id")
    val binTask = getBlockInputParam("binTask") as String?
    val operation = getBlockInputParam("operation") as String?
    val containerId = getBlockInputParam("containerId") as String?

    val move: EntityValue = mutableMapOf(
      "id" to location,
    )
    if (!binTask.isNullOrBlank()) move["binTask"] = binTask
    if (!operation.isNullOrBlank()) move["operation"] = operation
    if (!containerId.isNullOrBlank()) move["goodsId"] = containerId

    setBlockInternalVariables(mutableMapOf("move" to JsonHelper.writeValueAsString(move)))
  }

  companion object {

    val def = BlockDef(
      DirectOrderMoveBp::class.simpleName!!,
      color = "#83E164",
      inputParams = listOf(
        BlockInputParamDef(
          "id",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.BinId, ParamObjectType.SiteId),
        ),
        BlockInputParamDef("binTask", BlockParamType.String, false),
        BlockInputParamDef("operation", BlockParamType.String, false),
        BlockInputParamDef(
          "containerId",
          BlockParamType.String,
          false,
          objectTypes = listOf(ParamObjectType.ContainerId),
        ),
      ),
    )
  }
}