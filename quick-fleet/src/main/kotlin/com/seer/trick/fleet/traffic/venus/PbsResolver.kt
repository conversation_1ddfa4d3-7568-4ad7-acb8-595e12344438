package com.seer.trick.fleet.traffic.venus

import com.seer.trick.fleet.FleetLogger
import com.seer.trick.fleet.domain.Pose2D
import com.seer.trick.fleet.service.RobotLoadCollisionService
import com.seer.trick.fleet.service.SceneRuntime
import com.seer.trick.fleet.traffic.TrafficTaskStatus
import java.util.concurrent.ConcurrentLinkedDeque
import kotlin.math.max

/**
 * PBS (Priority Based Search) 高层求解器。只能被用一次。
 * 与现有 CBS 实现大体保持接口一致，。
 */
class PbsResolver(sr: SceneRuntime, requests: Map<String, RobotPlanRequest>, o: PathFindingOption) :
  BaseHighResolver(sr, requests, o) {

  private val logModule = "HighRvPBS"

  /**
   * 线程池：用于 (1) 同一节点内并行 buildChild，(2) 未来可扩展为跨节点并行。
   * 使用 CPU 核数大小的固定线程池即可；无需手动释放线程。
   */
  private val childPool: java.util.concurrent.ExecutorService =
    java.util.concurrent.Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors())

  // 全局自增节点编号，保证 PBS 节点 id 唯一
  private var nextNodeId: Int = 1

  // initPath 约束
  private val initPathConstraints: Map<String, List<RobotConstraint>> = ResConflictManager.buildInitPathConstraints(
    requests,
  )

  // 全局起点/旋转避让约束（静态约束，不含 leaveTime 上限）
  private lateinit var startAvoidConstraintsAll: List<RobotConstraint>

  /** ---------------- 内部数据结构 ---------------- */

  private data class PbsNode(
    val id: Int,
    val parentId: Int,
    val priorityGraph: PriorityGraph,
    val robotCtxs: Map<String, RobotPlanContext>, // 各机器人状态
    val cost: Double, // sum of cost
    val conflictNum: Int,
    val conflicts: List<Triple<String, String, RobotConstraint>>, // 冲突列表，元素为 (lowPriority, highPriority, constraint)
    val depth: Int,
    val planRobot: List<String>,
  )

  /**
   * 有向优先级图：若 edge low -> high 存在，则 high 优先级高于 low。
   */
  private class PriorityGraph {
    /**
     * 邻接表形式存储优先级边：`low -> {high1, high2, ...}`
     * 表示 high 优先级高于 low。
     * 仅存有向边，缺失的节点在逻辑上视作独立节点（与任何人无关系）。
     */
    private val edges: MutableMap<String, MutableSet<String>> = HashMap()

    /**
     * 深拷贝当前图，便于在 PBS 分支中修改而不影响父节点。
     */
    fun copy(): PriorityGraph {
      val g = PriorityGraph()
      edges.forEach { (k, v) -> g.edges[k] = v.toMutableSet() }
      return g
    }

    /**
     * 向图中加入一条优先级边 `low -> high`。
     * 若添加后会产生环（即违背拓扑关系），则返回 `false` 并拒绝添加。
     * @return `true` 表示成功添加；`false` 表示成环被拒绝。
     */
    fun addEdge(low: String, high: String): Boolean {
      // 添加前检测是否引入环（若已存在 high ⇒ ... ⇒ low 路径，则 low→high 会成环）
      if (connected(high, low)) return false
      edges.computeIfAbsent(low) { mutableSetOf() }.add(high)
      edges.computeIfAbsent(high) { mutableSetOf() } // 确保 high 也在图中
      return true
    }

    /**
     * 判断图中是否存在 从 `from` 到 `to` 的可达路径。用于环检测。
     */
    private fun connected(from: String, to: String): Boolean {
      if (from == to) return true
      val vis = HashSet<String>()
      val stack = ArrayDeque<String>()
      stack.add(from)
      while (stack.isNotEmpty()) {
        val n = stack.removeLast()
        if (n == to) return true
        edges[n]?.forEach { if (vis.add(it)) stack.add(it) }
      }
      return false
    }

    /**
     * 获取所有在优先级图中高于 `a` 的机器人集合（可直接或间接到达 `a` 的节点）。
     * 用 BFS 向出边方向遍历。
     */
    fun higherThanAgents(a: String): List<String> {
      val agents = mutableListOf<String>()
      // BFS 获取所有比 a 优先的节点
      val vis = HashSet<String>()
      val queue = ArrayDeque<String>()
      edges[a]?.forEach { queue.add(it) }
      while (queue.isNotEmpty()) {
        val n = queue.removeFirst()
        if (vis.add(n)) {
          agents += n
          edges[n]?.forEach { queue.add(it) }
        }
      }
      return agents
    }

    /**
     * 将图中所有边展开成 `(low, high)` 列表，方便日志/结果输出。
     */
    fun edgePairs(): List<Pair<String, String>> {
      val list = mutableListOf<Pair<String, String>>()
      for ((k, vs) in edges) vs.forEach { list += k to it }
      return list
    }
  }

  /** ---------------- 求解入口 ---------------- */

  override fun doResolve(): PlanResult {
    val root = buildRootNode() ?: return PlanResult(
      ok = false,
      reason = "No init solution",
      solutions = emptyMap(),
      giveWayGoals = emptyMap(),
      timeCost = 0,
    )

    // 空闲机器人避让预处理
    resolveIdleRobot(root)

    // -------- DFS 搜索 --------
    val (best, expanded) = dfsSearch(root)

    val elapsed = System.currentTimeMillis() - startOn
    childPool.shutdown() // Resolver 仅用一次，及时释放线程池

    val pr = best?.let { toPlanResult(it, expanded) } ?: return PlanResult(
      ok = false,
      reason = "No solution",
      solutions = emptyMap(),
      giveWayGoals = emptyMap(),
      timeCost = elapsed,
    )

    return if (pr.ok) pr.copy(priorityEdges = best.priorityGraph.edgePairs()) else pr
  }

  /**
   * 深度优先搜索 PBS 解。
   * 返回 <最优节点, 展开节点数, 是否降级求解> 三元组。
   */
  private fun dfsSearch(root: PbsNode): Pair<PbsNode?, Int> {
    val stack: ConcurrentLinkedDeque<PbsNode> = ConcurrentLinkedDeque<PbsNode>().apply { addLast(root) }
    var best: PbsNode? = null
    var expanded = 0
    val failRobots = mutableListOf<String>()

    while (stack.isNotEmpty()) {
      if (System.currentTimeMillis() - startOn > getResolveTimeout()) {
        FleetLogger.warn(module = logModule, subject = "Timeout", sr = sr, robotName = "-", msg = mapOf())
        break
      }
      val node = stack.pollLast() ?: continue
      expanded++
      logMe(
        "Expand id=${'$'}{node.id} depth=${'$'}{node.depth} cost=${'$'}{node.cost} " +
          "conf#=${'$'}{node.conflictNum} conflicts=${'$'}{node.conflicts}",
      )

      // 找到无冲突节点即返回
      if (node.conflicts.isEmpty()) {
        best = node
        break
      }

      // ---- 并行生成子节点 ----
      val childFutures = node.conflicts.map { (low, high, constrain) ->
        childPool.submit<PbsNode?> {
          if (node.robotCtxs[low]?.request?.trafficTask == null) {
            ensureGiveWayOrderForConstraint(node.robotCtxs[low]!!, low)
            return@submit null
          }
          buildChild(node, low, high, constrain)
        }
      }
      val children = childFutures.mapNotNull { it.get() }

      if (children.isEmpty() && produceDelayNodes(node, stack, failRobots)) {
        continue // 生成了 delay 节点，继续 DFS
      }

      // 冲突数 / 成本 升序，后进先出确保 DFS 先访问更优节点
      children
        .sortedWith(compareBy<PbsNode> { it.conflictNum }.thenBy { it.cost })
        .asReversed()
        .forEach(stack::addLast)

      // 若栈空，尝试降级方案
      if (stack.isEmpty()) {
        FleetLogger.warn(module = logModule, subject = "solve downgraded", sr = sr, robotName = "-", msg = mapOf())
        logMe("solve downgraded")
        buildFallbackNode(root, failRobots)?.let(stack::addLast)
      }
    }

    return Pair(best, expanded)
  }

  private fun resolveIdleRobot(root: PbsNode) {
    // 获取所有有任务的机器人路径
    val taskPaths = root.robotCtxs
      .filter { it.value.request.trafficTask != null }
      .values
      .flatMap { it.solution?.path ?: emptyList() }

    // 检查无任务机器人是否在有任务机器人的路径上
    val idleRobots = root.robotCtxs
      .filter {
        it.value.request.trafficTask == null || it.value.request.trafficTask?.status == TrafficTaskStatus.Success
      }
      .map { it.key }

    //    var needWaitIdle = false
    val idleAvoidRobot = mutableListOf<String>()
    for (idleRobot in idleRobots) {
      val idleRobotCtx = root.robotCtxs[idleRobot]!!
      val idleRobotState = idleRobotCtx.request.robotInfo.stand // 无任务机器人当前状态

      // 检查是否在有任务机器人的路径上
      val conflict = taskPaths.any { pathState ->
        pathState.toPosition.pointName == idleRobotState.pointName
      }

      if (conflict) {
        //        needWaitIdle = true
        idleAvoidRobot.add(idleRobot)
        ensureGiveWayOrderForConstraint(idleRobotCtx, idleRobot)
      }
    }
  }

  /** ---------------- 节点生成 ---------------- */

  /** 构造根节点 */
  private fun buildRootNode(): PbsNode? {
    val robotCtxs = HashMap<String, RobotPlanContext>()
    // 预先构造所有 RobotPlanContext，保证后续可安全访问 request/stand 等字段
    for ((name, req) in requests) {
      robotCtxs[name] = RobotPlanContext(
        name,
        req,
        req.trafficTask != null,
        reserveTargetPoint[name],
        null,
      )
    }
    val constraints: MutableMap<String, List<RobotConstraint>> = HashMap(initPathConstraints)

    var costSum = 0.0
    var makespan = 0L

    // 构造根优先级图，根据 option 决定是否注入初始优先级
    val pgRoot = PriorityGraph()
    for ((low, high) in o.initialPriorityEdges) pgRoot.addEdge(low, high)

    // ---- 预生成起点避让静态约束，后续复用 ----
    startAvoidConstraintsAll = buildStaticStartAvoidConstraints(robotCtxs)

    // 排序机器人，使初始优先级高的先规划
    val order: List<String> = if (o.initialPriorityEdges.isEmpty()) {
      requests.keys.toList()
    } else {
      topoSortByEdges(requests.keys, o.initialPriorityEdges)
    }

    val planRobot = mutableListOf<String>()
    for (robot in order) {
      val req = requests[robot]!!
      val ctx = robotCtxs[robot]!!

      if (req.trafficTask == null) continue

      planRobot.add(robot)
      // 生成额外约束
      // 仅对当前机器人低优先级的已规划机器人施加约束，避免无关机器人路径过早锁死
      val higherSetForRobot = pgRoot.higherThanAgents(robot).filter { robotCtxs[it]?.solution != null }

      val extraConstraints = buildRootExtraConstraints(robotCtxs, higherSetForRobot, robot)

      val lowConstraints = (constraints[robot] ?: emptyList()) + extraConstraints +
        (initPathConstraints[robot] ?: emptyList())

      val low = TargetManyLowResolver(
        sr, robot, req,
        sr.mapCache.areaById[ctx.request.robotInfo.stand.areaId]!!.groupedMaps[
          sr.mustGetRobot(ctx.robotName)
            .mustGetGroup().id,
        ]!!,
        o.collisionModels,
        ctx.reservePointName,
        friendlyId, 0L,
        lowConstraints, emptyMap(), o,
      )
      val rs = low.search("PBS Root")
      if (!rs.ok) {
        return null
      }
      robotCtxs[robot] = ctx.copy(solution = rs)

      costSum += rs.cost

      val lastEnd = rs.path.lastOrNull()?.timeEnd ?: 0L
      makespan = max(makespan, lastEnd)
    }

    val (conflictNum, earliest) = ResConflictManager.getConflictNumAndEarliest(robotCtxs, o)
    val conflictPairs: List<Triple<String, String, RobotConstraint>> =
      earliest?.map { Triple(it.key, it.value.masterRobotName, it.value) } ?: emptyList()

    val n = PbsNode(
      id = 0,
      parentId = -1,
      priorityGraph = pgRoot,
      robotCtxs = robotCtxs,
      cost = costSum,
      conflicts = conflictPairs,
      depth = 0,
      conflictNum = conflictNum,
      planRobot = planRobot,
    )
    logPbsNode(n)
    return n
  }

  private fun buildChild(parent: PbsNode, low: String, high: String, constraint: RobotConstraint): PbsNode? {
    // 构造新的优先级图
    val pg = parent.priorityGraph.copy()
    if (!pg.addEdge(low, high)) return null // 成环，剪枝

    // 复制上下文
    val robotCtxs = HashMap(parent.robotCtxs)

    // 根据优先级图，将所有 high 的路径转换为空间/点/时间约束，强加给 low
    val highAgents = pg.higherThanAgents(low)
    val constraintsForLow = mutableListOf<RobotConstraint>()
    // 先加入 initPath 约束
    constraintsForLow += initPathConstraints[low] ?: emptyList()
    constraintsForLow += constraint
    val extraConstraints = buildRootExtraConstraints(robotCtxs, highAgents, low)
    constraintsForLow += extraConstraints
    val lowCtx = robotCtxs[low]!!

    val lowResolver = TargetManyLowResolver(
      sr,
      low,
      lowCtx.request,
      sr.mapCache.areaById[lowCtx.request.robotInfo.stand.areaId]!!
        .groupedMaps[sr.mustGetRobot(lowCtx.robotName).mustGetGroup().id]!!,
      o.collisionModels,
      lowCtx.reservePointName,
      friendlyId,
      (parent.id + 1).toLong(),
      constraintsForLow,
      robotCtxs,
      o,
    )
    val rs = lowResolver.search("PBS replan $low -> let $high")
    if (!rs.ok) {
      logMe(
        "plan fail: ${'$'}{rs.reason}",
      )
      return null
    }

    robotCtxs[low] = lowCtx.copy(solution = rs)

    // 更新 cost & makespan
    var cost = 0.0
    var makespan = 0L
    for (ctx in robotCtxs.values) {
      ctx.solution?.let {
        cost += it.cost
        val lastEnd = it.path.lastOrNull()?.timeEnd ?: 0L
        makespan = max(makespan, lastEnd)
      }
    }

    val (conflictNum, earliest) = ResConflictManager.getConflictNumAndEarliest(robotCtxs, o)
    val conflictPairs: List<Triple<String, String, RobotConstraint>> =
      earliest?.map { Triple(it.key, it.value.masterRobotName, it.value) } ?: emptyList()

    if (earliest?.containsKey(low) == true && earliest.containsKey(high) == true) {
      FleetLogger.error(
        module = logModule,
        subject = "HighSearchResultConflict",
        sr = sr,
        robotName = low,
        msg = mapOf(
          "low" to low,
          "high" to high,
          "earliest" to earliest,
          "initPath" to robotCtxs[low]!!.request.initPath,
          "path" to robotCtxs[low]!!.solution!!.path,
        ),
      )
    }
    val n = PbsNode(
      id = nextNodeId++,
      parentId = parent.id,
      priorityGraph = pg,
      robotCtxs = robotCtxs,
      cost = cost,
      conflictNum = conflictNum,
      conflicts = conflictPairs,
      depth = parent.depth + 1,
      planRobot = listOf(low),
    )
    logPbsNode(n)
    return n
  }

  private fun logPbsNode(n: PbsNode) {
    if (sr.getDevConfigAsBool("logHigh") != true) return
    val solDesc = n.robotCtxs.values.joinToString(separator = "\n") {
      "  ${it.robotName}: stand=${it.request.robotInfo.stand}, task=${it.request.trafficTask}, " +
        "reservePointName=${it.reservePointName}\n    " +
        "sol=${it.solution}"
    }
    val pgDesc =
      n.priorityGraph.edgePairs().joinToString(separator = "\n") { "  ${'$'}{it.first} -> ${'$'}{it.second}" }
    nodeLogs += VenusHelper.logTimestamp() +
      " 产生PBS节点 | ID=${n.id} | 父节点=${n.parentId} |" +
      " 规划机器人=${n.planRobot} | 冲突数=${n.conflictNum} | cost=${n.cost} ｜冲突=${n.conflicts}  " +
      "| 成本=${n.cost} | 深度=${n.depth}\n" +
      "优先级图:\n$pgDesc\n" +
      "解:\n$solDesc"
  }

  private fun toPlanResult(node: PbsNode, expanded: Int): PlanResult {
    val solutions = mutableMapOf<String, RobotSolution>()
    for (ctx in node.robotCtxs.values) {
      ctx.solution?.let { solutions[ctx.robotName] = it }
    }
    // 打印路径日志
    solutions.forEach { (robot, sol) ->
      FleetLogger.trace(
        module = logModule,
        subject = "HighSearchResultPath",
        sr = sr,
        robotName = robot,
        msg = mapOf(
          "reservePointName" to node.robotCtxs[robot]!!.reservePointName,
          "initPath" to node.robotCtxs[robot]!!.request.initPath,
          "path" to sol.path,
        ),
      )
    }
    return PlanResult(
      ok = true,
      solutions = solutions,
      giveWayGoals = emptyMap(),
      timeCost = System.currentTimeMillis() - startOn,
      highNodeExpanded = expanded.toLong(),
      lowNodeExpanded = 0L,
    )
  }

  /**
   * 当搜索栈为空且存在无法规划的机器人时，为这些失败机器人构造“保持原地 / 执行 initPath”的降级方案，
   * 生成新的 PBS 节点。如无可行降级则返回 null。
   */
  private fun buildFallbackNode(root: PbsNode, failRobots: Collection<String>): PbsNode? {
    // Step-1: 若没有失败机器人则无需降级
    if (failRobots.isEmpty()) return null

    // 检查是否仍存在其他“活跃”机器人。
    // 只有在系统中仍有任务活跃（trafficTask ≠ null）的情况下，降级才有意义。
    val activeCtx = root.robotCtxs.filterValues { it.request.trafficTask != null }
    // 若活跃机器人全部属于失败集合，则搜索无法继续，直接返回 null。
    if (activeCtx.keys.all { it in failRobots }) return null

    // 克隆当前 robotCtxs，后续仅修改失败机器人。
    val ctx = root.robotCtxs.toMutableMap()

    // 为每个失败机器人构造保持原地 / 执行 initPath的解决方案：
    //   1) 若 initPath 不为空，则将其包装为一条合法的 RobotSolution，成本视为 0；
    //   2) 若 initPath 为空，则 newSol 为 null，表示忽略移动；
    //   3) moved 置为 true，防止后续重复规划。
    failRobots
      .asSequence()
      .mapNotNull { name -> activeCtx[name]?.let { name to it } }
      .forEach { (name, old) ->
        val newSol = old.request.initPath
          ?.takeIf { it.isNotEmpty() }
          ?.let { path ->
            val fixedPath = buildList<State> {
              addAll(path.dropLast(1))
              add(path.last().copy(timeEnd = -1)) // 结束后保持停止状态
            }
            RobotSolution(robotName = name, path = fixedPath, ok = true, cost = 0.0)
          }

        // 用更新后的 solution & moved 标记覆盖原上下文
        ctx[name] = old.copy(solution = newSol, moved = true)
      }
    // 重新计算总体成本，并检测最新冲突，以便正确排序 PBS 节点
    val newCost = ctx.values.sumOf { it.solution?.cost ?: 0.0 }
    val (_, earliest) = ResConflictManager.getConflictNumAndEarliest(ctx, o)
    val conflictPairs = earliest?.map { Triple(it.key, it.value.masterRobotName, it.value) } ?: emptyList()

    return root.copy(
      robotCtxs = ctx,
      cost = newCost,
      conflicts = conflictPairs,
    )
  }

  /**
   * 构造根节点中低优先级机器人需遵守的额外约束：包含高优先级路径占位 & 起点避让
   */
  private fun buildRootExtraConstraints(
    robotCtxs: Map<String, RobotPlanContext>,
    higherSet: List<String>,
    currentRobot: String,
  ): List<RobotConstraint> {
    val list = mutableListOf<RobotConstraint>()
    // 1) 高优先级路径占位
    for (high in higherSet) {
      val path = robotCtxs[high]?.solution?.path
      if (path != null) {
        list += ResConflictManager.pathToConstraints(high, path, o)
      } else if (robotCtxs[high]?.request?.trafficTask == null) {
        robotCtxs[high]?.request?.let {
          list += RobotConstraint(
            high,
            source = "StationaryPointSpatial",
            type = ResConstraintType.Spatial,
            timeStart = 0, // s1b.timeStart,
            timeEnd = -1, // s1b.timeEnd,
            pointName = it.robotInfo.stand.pointName,
            shapes = PolygonsWithBBox(
              RobotLoadCollisionService.buildCollisionShape(
                o.collisionModels[high]!!,
                Pose2D(
                  it.robotInfo.stand.x,
                  it.robotInfo.stand.y,
                  it.robotInfo.stand.theta,
                ),
                it.robotInfo.loadTheta,
              ),
            ),
            spatialDesc = "high Stationary spatial by point $it",
          )
        }
      } else {
        continue
      }
    }

    // 加入预计算的起点避让约束（排除 currentRobot 本身）
    list += startAvoidConstraintsAll.filter { it.masterRobotName != currentRobot }
    // initPath 约束在调用处合并
    return list
  }

  /**
   * 预生成所有机器人的起点/旋转避让约束，timeEnd 使用 Long.MAX_VALUE，后续复用。
   */
  private fun buildStaticStartAvoidConstraints(robotCtxs: Map<String, RobotPlanContext>): List<RobotConstraint> {
    // 直接复用 BaseHighResolver 的工具方法，但不过滤 currentRobot；leaveTime 给无限大以表示静态占位
    val constraints = generateStartPointAvoidanceConstraints(
      robotContexts = robotCtxs,
      options = o,
      source = "PBSStartRootStatic",
    ) ?: emptyList()
    return constraints
  }

  /**
   * 当两个子分支都失败时，尝试延迟高优先级或低优先级机器人，生成新的 PBS 节点。
   *
   * @return true 如果至少生成了一个延迟节点，false 如果没有生成任何延迟节点
   */
  private fun produceDelayNodes(
    node: PbsNode,
    stack: java.util.concurrent.ConcurrentLinkedDeque<PbsNode>,
    failRobots: MutableList<String>,
  ): Boolean {
    // 两子分支都失败——依次为 high 与 low 机器人各构造一个“起点等待 delayMs”节点
    val (low, high, _) = node.conflicts.first()
    val delayTime = 10L

    fun shiftPath(path: List<State>, delta: Long): List<State> =
      path.map { s -> s.copy(timeStart = s.timeStart + delta, timeEnd = s.timeEnd + delta) }

    var producedAny = false

    for (delayRobot in listOf(high, low)) {
      val ctx = node.robotCtxs[delayRobot] ?: continue
      if (ctx.delayed) continue // 已经延迟过
      val sol = ctx.solution ?: continue
      if (sol.path.isEmpty()) continue

      // 当两个冲突机器人任意一方无任务时，直接返回 false，不做延迟处理
      if (node.robotCtxs[low]?.request?.trafficTask == null ||
        node.robotCtxs[high]?.request?.trafficTask == null
      ) {
        return false
      }

      // 若机器人有 initPath，只允许在 initPath 结束之后延迟
      val initEnd = ctx.request.initPath?.lastOrNull()?.timeEnd ?: 0L

      val (initpath, rest) = sol.path.partition { it.timeEnd <= initEnd }

      val newPath = buildList<State> {
        if (initpath.isNotEmpty()) {
          // 延长 initpath 最后一个状态的停留时间
          addAll(initpath.dropLast(1))
          val lastPrefix = initpath.last()
          add(lastPrefix.copy(timeEnd = lastPrefix.timeEnd + delayTime))
          if (rest.isNotEmpty()) addAll(shiftPath(rest, delayTime))
        } else {
          // 无 initpath，直接对第一个状态延时
          val first = sol.path.first()
          add(first.copy(timeEnd = first.timeEnd + delayTime))
          addAll(shiftPath(sol.path.drop(1), delayTime))
        }
      }

      val newSol = sol.copy(path = newPath)
      val newCtxMap = node.robotCtxs.toMutableMap()
      newCtxMap[delayRobot] = ctx.copy(solution = newSol, delayed = true)

      // 重新计算冲突与成本、makespan
      val newCost = newCtxMap.values.sumOf { it.solution?.cost ?: 0.0 }
      val (confNum, earliest) = ResConflictManager.getConflictNumAndEarliest(newCtxMap, o)
      val conflictPairs = earliest?.map { Triple(it.key, it.value.masterRobotName, it.value) } ?: emptyList()

      val delayNode = PbsNode(
        id = nextNodeId++,
        parentId = node.id,
        robotCtxs = newCtxMap,
        planRobot = listOf(delayRobot),
        cost = newCost,
        conflictNum = confNum,
        conflicts = conflictPairs,
        depth = node.depth + 1,
        priorityGraph = node.priorityGraph,
      )

      stack.addLast(delayNode)
      producedAny = true
    }

    if (!producedAny) {
      // 若两个机器人都已延迟过或无法延迟，则记录失败
      failRobots.add(low)
      failRobots.add(high)
    }
    return producedAny
  }

  /**
   * 根据 low->high 边做拓扑排序，返回高优先级在前的列表
   */
  private fun topoSortByEdges(nodes: Collection<String>, edges: List<Pair<String, String>>): List<String> {
    val adj = mutableMapOf<String, MutableSet<String>>()
    val indeg = mutableMapOf<String, Int>()
    nodes.forEach { indeg[it] = 0 }
    for ((low, high) in edges) {
      if (nodes.contains(low) && nodes.contains(high)) {
        adj.computeIfAbsent(low) { mutableSetOf() }.add(high)
        indeg[high] = indeg.getOrDefault(high, 0) + 1
      }
    }
    val q = ArrayDeque<String>()
    indeg.filter { it.value == 0 }.keys.forEach { q.add(it) }
    val res = mutableListOf<String>()
    while (q.isNotEmpty()) {
      val n = q.removeFirst()
      res += n
      adj[n]?.forEach { v ->
        indeg[v] = indeg[v]!! - 1
        if (indeg[v] == 0) q.add(v)
      }
    }
    // append remaining (cycle) or not connected
    for (n in nodes) if (!res.contains(n)) res += n
    // res 目前低优先级在前（出边节点），需反转使高优先级在前
    return res.asReversed()
  }

  /**
   * 判断在初始优先级图中，high 是否比 low 优先（即存在 low->...->high 的有向路径）
   */
  private fun isHigher(low: String, high: String): Boolean {
    if (low == high) return false
    // 建邻接表 low -> high
    val adj = mutableMapOf<String, MutableSet<String>>()
    for ((l, h) in o.initialPriorityEdges) {
      adj.computeIfAbsent(l) { mutableSetOf() }.add(h)
    }
    // DFS
    val stack = ArrayDeque<String>()
    val visited = mutableSetOf<String>()
    stack.add(low)
    while (stack.isNotEmpty()) {
      val cur = stack.removeLast()
      if (!visited.add(cur)) continue
      val nexts = adj[cur] ?: emptySet()
      if (nexts.contains(high)) return true
      for (n in nexts) stack.add(n)
    }
    return false
  }
}