package com.seer.trick.fleet.traffic.distributed.helper

import com.seer.trick.fleet.traffic.distributed.map.LineType
import com.seer.trick.fleet.traffic.distributed.plan.model.PathAction
import com.seer.trick.fleet.traffic.distributed.plan.model.PathType
import org.slf4j.LoggerFactory
import java.util.*

/**
 *  路径的辅助类
 * */
object PathInfoHelper {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 简单的将两段路径拼接
   * */
  fun joinPath(firstPath: MutableList<PathAction>, secondPath: MutableList<PathAction>): MutableList<PathAction> {
    var first = firstPath.last()
    if (first.type == PathType.STOP && secondPath.isEmpty()) {
      return firstPath
    }
    if (first.type == PathType.STOP) {
      firstPath.removeLast()
      first = firstPath.last()
    }
    val second = secondPath.first()
    val path: MutableList<PathAction> = LinkedList()
    path.addAll(firstPath)
    if (first.target.pointName == second.start.pointName &&
      AngleHelper.sameAngleInFiveDegree(first.robotOutHeading, second.robotInHeading)
    ) {
      path.addAll(secondPath)
      return path
    }
    if (first.target.pointName == second.start.pointName &&
      AngleHelper.sameAngleInFiveDegree(first.robotOutHeading, second.robotInHeading)
    ) {
      val pathAction = first.copy(
        index = first.index + 1,
        type = PathType.ROTATE,
        start = first.target,
        lineName = first.lineName,
        robotInHeading = first.robotOutHeading,
        robotOutHeading = second.robotInHeading,
        containerInHeading = first.containerOutHeading,
        containerOutHeading = second.containerInHeading,
      )
      path.add(pathAction)
      path.addAll(secondPath)
      return path
    }
    throw RuntimeException("Path concatenation failure")
  }

  /**
   * 将两段路径拼接
   *
   * */
  fun joinPath(
    fistPath: MutableList<PathAction>,
    secondPath: MutableList<PathAction>,
    siteIndex: Long,
  ): MutableList<PathAction> {
    if (fistPath.size <= siteIndex) {
      throw RuntimeException("Path concatenation failure")
    }
    val path: MutableList<PathAction> = LinkedList()
    var index = siteIndex
    if (siteIndex < 0) {
      for (p in secondPath) {
        p.index = ++index
        path.add(p)
      }
      return path
    }
    val actions = fistPath.subList(0, (siteIndex + 1).toInt())
    path.addAll(actions)
    val first = actions.last()
    val second = secondPath.first()
    if (first.target.pointName == second.start.pointName &&
      AngleHelper.sameAngleInFiveDegree(first.robotOutHeading, second.robotInHeading)
    ) {
      for (p in secondPath) {
        p.index = ++index
        path.add(p)
      }
      return path
    }
    if (first.target.pointName == second.start.pointName &&
      !AngleHelper.sameAngleInFiveDegree(first.robotOutHeading, second.robotInHeading)
    ) {
      val pathAction = first.copy(
        index = ++index,
        type = PathType.ROTATE,
        start = first.target,
        robotInHeading = first.robotOutHeading,
        robotOutHeading = second.robotInHeading,
        containerInHeading = first.containerOutHeading,
        containerOutHeading = second.containerInHeading,
      )
      path.add(pathAction)
      for (p in secondPath) {
        p.index = ++index
        path.add(p)
      }
      return path
    }
    throw RuntimeException("Path concatenation failure")
  }

  fun typeConvert(type: LineType): PathType = when (type) {
    LineType.STRAIGHT -> PathType.STRAIGHT
    LineType.THREE_BEZIER -> PathType.ARC
    LineType.LUMINANCE_CURVE -> PathType.CURVE
    else -> PathType.STRAIGHT
  }
}