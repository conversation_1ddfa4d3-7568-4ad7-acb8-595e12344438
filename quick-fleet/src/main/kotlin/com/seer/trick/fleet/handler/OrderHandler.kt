package com.seer.trick.fleet.handler

import RobotOrderDiagnostics.checkOrderKeyLocationsValidate
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.http.*
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.handler.MissingQueryParamError
import com.seer.trick.falcon.task.FalconTaskService
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.mock.service.MockService
import com.seer.trick.fleet.order.*
import com.seer.trick.fleet.service.RobotService
import com.seer.trick.fleet.service.SceneService
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.NumHelper
import com.seer.trick.helper.containsIgnoreCase
import io.javalin.http.Context
import io.javalin.websocket.WsMessageContext

/**
 * 运单相关 HTTP 和 WS 接口
 */
object OrderHandler : WebSocketSubscriber() {

  fun registerHandlers() {
    WebSocketManager.subscribers += this

    val c = Handlers("api/fleet/orders")
    c.post("create", ::createOrder, auth())
    c.post("cancel", ::cancelOrder, auth())
    c.post("cancel-batch", ::cancelOrderBatch, auth())
    c.post("cancel-all", ::cancelAllOrders, auth())
    c.post("reset-robot", ::resetRobot, auth())
    c.post("reset-all-robots", ::resetAllRobots, auth())
    c.post("update-priority-batch", ::updatePriorityBatch, auth())
    c.post("retry-failed-robot", ::retryFailedRobot, auth())
    c.post("retry-failed-orders", ::retryFailedOrders, auth())
    c.post("add-steps", ::addSteps, auth())
    c.post("update-steps", ::updateSteps, auth())
    c.post("delete-steps", ::deleteSteps, auth())
    c.post("update-key-locations", ::updateKeyLocations, auth())
    c.post("complete-order", ::completeOrder, auth())
    c.post("complete-order-batch", ::completeOrderBatch, auth())

    c.post("simple-test", ::createSimpleTest, auth())

    c.get("query-order-detail", ::queryOrderDetail, auth())

    c.post("random-orders", ::randomOrders, auth())

    c.get("random-order-config", ::getRandomOrderConfig, auth())
    c.post("random-order-config", ::saveRandomOrderConfig, auth())
    c.post("export-order-snapshot", ::exportOrderSnapshot, auth()) // 导出运单快照
    c.post("import-order-replay", ::startOrderReplay, auth()) // 运单重放
    c.post("cancel-order-replay", ::stopOrderReplay, auth()) // 取消运单重放
  }

  override fun onMessage(ctx: WsMessageContext, msg: WsMsg, env: WsEnv) {
    when (msg.action) {
      "Fleet::OrderDetail::Query" -> onQueryOrderDetail(ctx, msg) // 查询运单详情
    }
  }

  private fun createOrder(ctx: Context) {
    val req: CreateOrderReq = ctx.getReqBody()

    val sr = SceneService.mustGetSceneById(req.sceneId)

    val orderId = OrderService.generateOrderId()

    val or = toOrderRuntime(req, orderId)
    // TODO 挪到 createOrders() 中处理。
    if (!checkOrderKeyLocationsValidate(or).passed) throw BzError("errKeyLocationNotReachable")
    OrderService.createOrders(sr, listOf(or))

    ctx.json(CreateOrderRes(orderId = orderId, externalId = req.externalId))
  }

  fun toOrderRuntime(req: CreateOrderReq, orderId: String): OrderRuntime {
    // 先检查一下是否已禁用
    val sr = SceneService.mustGetSceneById(req.sceneId)
    if (req.expectedRobotNames.isNullOrEmpty()) {
      // 若指定了机器人组则检查是否被禁用了
      if (!req.expectedRobotGroups.isNullOrEmpty()) {
        val groups = sr.listRobotGroups()
        val allGroupDisabled = req.expectedRobotGroups.all { gName ->
          // 找不到机器人组，则等效为禁用
          groups.find { it.name == gName }?.disabled ?: true
        }
        // 机器人不存在且机器人组被禁用
        if (allGroupDisabled) {
          throw BzError("errRobotGroupDisabled", req.expectedRobotGroups.toMutableList().joinToString(","))
        }
      }
    } else {
      val allRobotDisabled = req.expectedRobotNames.all { sr.mustGetRobot(it).disabled() }
      // 不管有没有指定机器人组，一旦指定机器人，且都被禁用了则报错，因为指定了机器人，匹配失败了就不会判断机器人组了
      if (allRobotDisabled) throw BzError("errRobotDisabled", req.expectedRobotNames.toMutableList().joinToString(","))
    }

    // 检查一下已封口但步骤为空
    if (req.stepFixed && req.steps.isEmpty()) throw BzError("errSubmitEmptyStepWhenSealed")

    val steps = req.steps.mapIndexed { index, step ->
      step.toStep(orderId, index)
    }
    val order = TransportOrder(
      id = orderId,
      externalId = req.externalId,
      status = req.status,
      priority = req.priority,
      expectedRobotNames = req.expectedRobotNames,
      expectedRobotGroups = req.expectedRobotGroups,
      containerId = req.containerId,
      containerDir = req.containerDir,
      containerTypeName = req.containerTypeName,
      keyLocations = req.keyLocations,
      stepFixed = req.stepFixed,
      stepNum = steps.size,
      sceneId = req.sceneId,
    )

    return OrderRuntime(order, steps)
  }

  /**
   * 搬运任务
   */
  data class CreateOrderReq(
    val sceneId: String,
    val externalId: String? = null, // 请求 ID
    val status: OrderStatus = OrderStatus.ToBeAllocated, // 状态
    val priority: Int = 0, // 优先级
    val expectedRobotNames: List<String>? = null, // 期望以下机器人执行
    val expectedRobotGroups: List<String>? = null, // 期望以下机器人组执行
    val containerId: String? = null, // 搬运的容器编号
    val containerTypeName: String? = null, // 容器类型名称
    val containerDir: Double? = null, // 容器方向（弧度）
    val keyLocations: List<String>? = null, // 关键点位
    val stepFixed: Boolean = false, // 不再追加新的步骤
    val steps: List<CreateStepReq> = emptyList(),
  )

  /**
   * 搬运任务中的一步
   */
  data class CreateStepReq(
    val status: StepStatus = StepStatus.Executable,
    val location: String, // 作业位置
    val rbkArgs: Map<String, Any?>? = null, // 动作参数
    val forLoad: Boolean = false, // 在此步取货
    val forUnload: Boolean = false, // 在此步卸货
    val withdrawOrderAllowed: Boolean = false, // 是否可以重分派
    val nextStepSameOrder: Boolean = false, // 强制要求下一步不能切换运单，还是做本单
  ) {

    fun toStep(orderId: String, index: Int): TransportStep = TransportStep(
      id = "$orderId-Step$index",
      orderId = orderId,
      stepIndex = index,
      status = status,
      location = location,
      rbkArgs = rbkArgs?.let { JsonHelper.writeValueAsString(it) },
      forLoad = forLoad,
      forUnload = forUnload,
      withdrawOrderAllowed = withdrawOrderAllowed,
      nextStepSameOrder = nextStepSameOrder,
    )
  }

  data class CreateOrderRes(val orderId: String, val externalId: String?)

  private fun resetRobot(ctx: Context) {
    val req: ResetRobotReq = ctx.getReqBody()

    val sr = SceneService.mustGetSceneById(req.sceneId)
    val rr = sr.mustGetRobot(req.robotName)

    RobotService.resetRobot(sr, rr)

    // 清理仿真机器人的背篓、货架
    MockService.resetRobotContainers(rr.robotName)

    ctx.status(200)
  }

  data class ResetRobotReq(val sceneId: String, val robotName: String)

  private fun resetAllRobots(ctx: Context) {
    val req: ResetAllRobotReq = ctx.getReqBody()

    val sr = SceneService.mustGetSceneById(req.sceneId)
    val robots = sr.listRobots()
    robots.forEach {
      RobotService.resetRobot(sr, it)

      // 清理仿真机器人的背篓、货架
      MockService.resetRobotContainers(it.robotName)
    }

    ctx.status(200)
  }

  data class ResetAllRobotReq(val sceneId: String)

  /**
   * 批量调整非终态运单的优先级
   */
  private fun updatePriorityBatch(ctx: Context) {
    val req: UpdatePriorityBatchReq = ctx.getReqBody()

    req.orderIds.forEach { orderId ->
      val sceneId = OrderService.mustGetSceneIdByOrderId(orderId)
      val sr = SceneService.mustGetSceneById(sceneId)
      OrderService.updatePriority(sr, orderId, req.priority)
    }
  }

  data class UpdatePriorityBatchReq(val orderIds: List<String>, val priority: Int)

  private fun createSimpleTest(ctx: Context) {
    val req: SimpleTest = ctx.getReqBody()

    val sr = SceneService.mustGetSceneById(req.sceneId)
    val sceneLiteral = "${req.sceneId} (${sr.basic.name})"

    val orderId = OrderService.generateOrderId()

    // Robot-1@Container-type-1:container-1$PP2%AP1:*Load->AP2:Unload^90#3

    var robotName = ""
    var priority = 0
    var reqStr = req.path

    // 解析运单优先级
    if (reqStr.contains("#")) {
      val pStr = reqStr.substringAfterLast("#").trim()
      priority = try {
        // 这里可能会报错，接一下。例如 pStr = "a123" 时。
        NumHelper.anyToInt(pStr) ?: 0
      } catch (e: Exception) {
        throw BzError("errSimpleTestBadPriority", pStr)
      }
      reqStr = reqStr.substringBeforeLast("#")
    }

    // 解析机器人名称
    if (reqStr.contains("@")) {
      robotName = reqStr.substringBefore("@").trim()
      if (robotName.isNotBlank() && !sr.robots.keys.contains(robotName)) {
        throw BzError("errSimpleTestBadRobotName", sceneLiteral, robotName)
      }
      reqStr = reqStr.substringAfter("@").trim()
    }

    // 解析 容器类型名称 和 容器编号
    var containerTypeName: String? = null
    var containerId: String? = null
    if (reqStr.contains("$")) {
      // 容器类型:容器编号
      val containerInfo = reqStr.substringBefore("$")
      containerTypeName = containerInfo.substringBefore(":").trim()
      // 非空的容器类型的名称，必须是系统中已存在的容器。
      if (containerTypeName.isNotBlank() && sr.containerTypes.values.none { it.name == containerTypeName }) {
        throw BzError("errSimpleTestBadContainerTypeName", sceneLiteral, containerTypeName)
      }
      containerId = containerInfo.substringAfter(":").trim()
      reqStr = reqStr.substringAfter("$")
    }

    // 解析出生点，仅对仿真机器人有效。在 createOrders() 之前，再将仿真机器人移动到 initialPos
    var initialPos: String? = null
    if (reqStr.contains("%")) {
      initialPos = reqStr.substringBefore("%").trim()
      if (initialPos.isNotBlank()) {
        if (!sr.mustGetRobot(robotName).config.simulated) {
          throw BzError("errSimpleTestInitialPosOnlyForRealRobot", robotName, initialPos)
        }

        // TODO: 出生点上不能有其他机器人，也不能已经被分派给其他机器人

        // initialPos 必须在当前场景中，但是不校验 initialPos 与运单步骤的 location 的可达性。
        val pointIds = sr.mapCache.areaById.values.flatMap { it.mergedMap.pointIdMap.keys }.map { it.toString() }
        val pointNames = sr.mapCache.pointNames
        val binNames = sr.mapCache.binNames
        if (!pointIds.contains(initialPos) && !pointNames.contains(initialPos) && !binNames.contains(initialPos)) {
          throw BzError("errSimpleTestBadInitialPos", sceneLiteral, initialPos)
        }
      }
      reqStr = reqStr.substringAfter("%")
    }

    // 解析运单步骤和每个步骤的相关参数。
    var containerDir: Double? = null // 放货角度
    // splitTrim() 会把空字符串过滤掉空字符串，为了提供更明确的提示信息，这里就不用此方法了；并且为了回显原文，也不能 trim()；后面处理 ":" 和 "^" 时也是如此。
    val steps = reqStr.split("->").mapIndexed { i, ss ->
      // ss 的完整格式是 点位名称:动作描述^放货角度
      val stepNo = i + 1
      val parts = ss.split(":") // 不能 trim(), 因为要回显原文。
      if (parts.size != 2) throw BzError("errSimpleTestBadStepStruct", stepNo, ss)

      // 解析当前步骤的 location，基于场景的合法性与可达性，在 createOrders() 中校验。
      val location = parts[0]
      if (location.isBlank()) throw BzError("errSimpleTestBadStepLocation", stepNo)

      // 解析当前步骤的动作名称，和放货角度。
      val opPart = parts[1]
      if (opPart.isBlank()) throw BzError("errSimpleTestBadStepAction", stepNo)
      val opParts = opPart.split("^") // 不能 trim(), 因为要回显原文。
      // 动作名称
      val op = opParts[0]
      if (op.isBlank()) throw BzError("errSimpleTestBadStepAction", stepNo)
      // 放货角度
      if (opParts.size > 1) {
        val degree = try {
          NumHelper.anyToDouble(opParts[1])
        } catch (e: Exception) {
          null
        } ?: throw BzError("errSimpleTestBadStepContainerDir", stepNo, opParts[1])
        // 角度转弧度
        containerDir = degree * (Math.PI / 180)
      }

      val rbkArgs = JsonHelper.writeValueAsString(
        if (op.contains("*")) {
          mapOf("binTask" to op.replace("*", ""))
        } else {
          mapOf("operation" to op)
        },
      )
      TransportStep(
        id = IdHelper.oidStr(), orderId = orderId, stepIndex = i,
        status = StepStatus.Executable,
        location = location,
        rbkArgs = rbkArgs,
        forLoad = op.containsIgnoreCase("load") && !op.containsIgnoreCase("unload"), // 包含 load 或者 *load
        forUnload = op.containsIgnoreCase("unload"), // 包含 unload 或者 *unload
        withdrawOrderAllowed = true,
      )
    }

    // 一次简单发单，运单中带 "load" 和 "unload" 动作描述的步骤必须成对出现，且最多只能有一组。
    val forLoadSize = steps.filter { it.forLoad }.size
    val forUnloadSize = steps.filter { it.forUnload }.size
    val loadUnloadStepsValid = forLoadSize == forUnloadSize && forLoadSize in listOf(0, 1)
    if (!loadUnloadStepsValid) throw BzError("errSimpleTestLoadUnloadStepsInvalid")

    val order = TransportOrder(
      id = orderId,
      status = OrderStatus.ToBeAllocated,
      containerId = containerId?.ifEmpty { null },
      expectedRobotNames = if (robotName.isBlank()) null else listOf(robotName),
      priority = priority,
      keyLocations = listOf(steps[0].location).distinct(),
      stepNum = steps.size,
      stepFixed = true,
      sceneId = req.sceneId,
      containerTypeName = containerTypeName,
      containerDir = containerDir,
    )
    val or = OrderRuntime(order, steps)
    // TODO 挪到 createOrders() 中处理。
    if (!checkOrderKeyLocationsValidate(or).passed) throw BzError("errKeyLocationNotReachable")

    // 创建运单前，尝试将对应的仿真机器人移动到指定的出生点上
    if (!initialPos.isNullOrBlank()) {
      // 之前已经校验过，存在非空的 initialPos 时，一定会指定仿真机器人的。
      // TODO：如果机器人正在执行运单就报错，否则机器人无法立即执行这条新建的运单，将其移动到 initialPos 也无意义。
      //  1. 如果有未完成的业务单，并且一定要撤销这些业务运单的话，直接重置此机器人即可。
      //  2. 都是未完成的自动单，可以直接重置此机器人。
      //  3. 还需要将此机器人置为不接单，否则会修改机器人位置后，可能会为其分配自动但，但其实自动单的影响也不大。

      // 移动仿真机器人到 initialPos
      MockService.setRobotInitPoint(robotName, initialPos, null, null, null)
    }

    OrderService.createOrders(sr, listOf(or))

    ctx.json(mapOf("orderId" to orderId))
  }

  data class SimpleTest(val sceneId: String, val path: String)

  private fun cancelOrder(ctx: Context) {
    val req: CancelOrderReq = ctx.getReqBody()

    val sr = SceneService.mustGetSceneById(req.sceneId)
    OrderCancelService.cancelOrder(sr, req.orderId)

    ctx.status(200)
  }

  private fun cancelOrderBatch(ctx: Context) {
    val req: OrderIdsReq = ctx.getReqBody()

    req.orderIds.forEach { orderId ->
      val sceneId = OrderService.mustGetSceneIdByOrderId(orderId)
      val sr = SceneService.mustGetSceneById(sceneId)
      OrderCancelService.cancelOrder(sr, orderId)
    }

    ctx.status(200)
  }

  private fun cancelAllOrders(ctx: Context) {
    val req: SceneIdReq = ctx.getReqBody()

    val sr = SceneService.mustGetSceneById(req.sceneId)
    OrderCancelService.cancelAllOrders(sr)

    ctx.status(200)
  }

  data class CancelOrderReq(val sceneId: String, val orderId: String)

  data class OrderIdsReq(val orderIds: List<String>)

  private fun retryFailedRobot(ctx: Context) {
    val req: RetryFailedRobotReq = ctx.getReqBody()

    val sr = SceneService.mustGetSceneById(req.sceneId)
    val rr = sr.mustGetRobot(req.robotName)

    OrderFaultService.retryByRobot(rr)

    ctx.status(200)
  }

  data class RetryFailedRobotReq(val sceneId: String, val robotName: String)

  /**
   * 故障重试：尝试继续执行故障的运单。
   */
  private fun retryFailedOrders(ctx: Context) {
    val req: OrderIdsReq = ctx.getReqBody()

    req.orderIds.forEach { orderId ->
      val sceneId = OrderService.mustGetSceneIdByOrderId(orderId)
      val sr = SceneService.mustGetSceneById(sceneId)
      OrderFaultService.retryByOrder(sr, orderId)
      // 查询有没有故障状态且内部变量包含该运单的任务，存在则故障重试猎鹰任务
      val taskId = EntityRwService.findOne(
        "FalconBlockRecord",
        Cq.and(listOf(Cq.eq("status", 250), Cq.containIgnoreCase("internalVariables", orderId))),
      )?.get("taskId")
      if (taskId != null) {
        FalconTaskService.recoveryErrorRun(taskId as String)
      }
    }

    ctx.status(200)
  }

  private fun addSteps(ctx: Context) {
    val req: AddStepsReq = ctx.getReqBody()
    val sceneId = OrderService.mustGetSceneIdByOrderId(req.orderId)
    val sr = SceneService.mustGetSceneById(sceneId)
    val or = sr.mustGetOrderById(req.orderId)
    if (or.order.kind != OrderKind.Business) {
      throw BzError("errAddStepsButNotBzOrder", req.orderId)
    }
    sr.withOrderLock {
      var stepIndex = or.steps.size
      val steps = req.steps.map { it.toStep(req.orderId, stepIndex++) }
      OrderService.addSteps(sr, req.orderId, steps, false)
    }
    ctx.status(200)
  }

  /**
   * 更新未执行的运单步骤。
   * 不能通过接口修改运单步骤的这些字段（在前端约束吧）：
   *    id orderId stepIndex processingTime executingTime createdOn startOn endOn
   * 其他字段的可修改性待讨论。
   */
  private fun updateSteps(ctx: Context) {
    val req: UpdateStepsReq = ctx.getReqBody()
    val sceneId = OrderService.mustGetSceneIdByOrderId(req.orderId)
    val sr = SceneService.mustGetSceneById(sceneId)
    val or = sr.mustGetOrderById(req.orderId)

    OrderService.updateSteps(sr, or, req.steps)

    ctx.status(200)
  }

  /**
   * TODO 改成 ID 索引
   */
  data class UpdateStepsReq(val orderId: String, val steps: List<UpdateStepReq>)

  /**
   * 删除未执行的运单步骤
   */
  private fun deleteSteps(ctx: Context) {
    val req: DeleteStepsReq = ctx.getReqBody()
    val sceneId = OrderService.mustGetSceneIdByOrderId(req.orderId)
    val sr = SceneService.mustGetSceneById(sceneId)
    val or = sr.mustGetOrderById(req.orderId)
    OrderService.removeSteps(sr, or, req.stepIds)
    ctx.status(200)
  }

  data class DeleteStepsReq(val orderId: String, val stepIds: List<String>)

  /**
   * 修改运单的 keyLocations
   */
  private fun updateKeyLocations(ctx: Context) {
    val req: UpdateKeyLocationsReq = ctx.getReqBody()

    val sceneId = OrderService.mustGetSceneIdByOrderId(req.orderId)
    val sr = SceneService.mustGetSceneById(sceneId)
    val or = sr.mustGetOrderById(req.orderId)
    val newOrder = or.order.copy(keyLocations = req.keyLocations)
    OrderService.updateAndPersistOrder(or, newOrder, "Update key location from handler")

    ctx.status(200)
  }

  data class UpdateKeyLocationsReq(val orderId: String, val keyLocations: List<String>)

  private fun completeOrder(ctx: Context) {
    val req: OrderIdReq = ctx.getReqBody()
    val sceneId = OrderService.mustGetSceneIdByOrderId(req.orderId)
    val sr = SceneService.mustGetSceneById(sceneId)
    val or = sr.mustGetOrderById(req.orderId)
    sr.withOrderLock {
      OrderService.updateAndPersistOrder(or, or.order.copy(stepFixed = true), "Set step fixed by handler")
    }

    ctx.status(200)
  }

  private fun completeOrderBatch(ctx: Context) {
    val req: OrderIdsReq = ctx.getReqBody()

    req.orderIds.forEach { orderId ->
      val sceneId = OrderService.mustGetSceneIdByOrderId(orderId)
      val sr = SceneService.mustGetSceneById(sceneId)
      val or = sr.mustGetOrderById(orderId)
      OrderService.updateAndPersistOrder(or, or.order.copy(stepFixed = true), "Set step fixed by handler")
    }

    ctx.status(200)
  }

  data class AddStepsReq(val orderId: String, val steps: List<CreateStepReq>)

  data class OrderIdReq(val orderId: String)

  /**
   * WS 查询运单详情
   */
  private fun onQueryOrderDetail(ctx: WsMessageContext, msg: WsMsg) {
    val req: OrderReq = JsonHelper.mapper.readValue(msg.content!!, jacksonTypeRef())
    val orderDetail = mustGetOrderDetail(req.orderId)
    ctx.send(WsMsg.json("Fleet::OrderDetail::Reply", orderDetail, replyToId = msg.id))
  }

  data class OrderReq(val orderId: String)

  /**
   * 查询运单详情
   */
  private fun queryOrderDetail(ctx: Context) {
    val orderId = ctx.queryParam("orderId") ?: throw MissingQueryParamError("orderId")
    ctx.json(mustGetOrderDetail(orderId))
  }

  private fun mustGetOrderDetail(orderId: String): EntityValue {
    val sceneId = OrderService.mustGetSceneIdByOrderId(orderId)
    val sr = SceneService.mustGetSceneById(sceneId)
    val order = EntityRwService.findOneById("TransportOrder", orderId) ?: throw BzError("errNoOrderById", orderId)
    val steps = EntityRwService.findMany(
      "TransportStep",
      Cq.eq("orderId", orderId),
      FindOptions(sort = listOf("stepIndex")),
    )
    order["steps"] = steps
    sr.orders[orderId]?.let {
      order["allocationReject"] = it.allocationReject
      order["executionReject"] = it.executionReject
    }
    return order
  }

  private fun randomOrders(ctx: Context) {
    val req: RandomOrderReq = ctx.getReqBody()
    if (req.on) {
      RandomOrderService.addCfg(req.sceneId, req.keepOrderNum)
    } else {
      RandomOrderService.removeCfg(req.sceneId)
    }
    ctx.status(200)
  }

  data class RandomOrderReq(val sceneId: String, val on: Boolean, val keepOrderNum: Int = 5)

  private fun getRandomOrderConfig(ctx: Context) {
    val sceneId = ctx.queryParam("sceneId") ?: throw MissingQueryParamError("sceneId")
    val config = RandomCreateOrderService.getConfig(sceneId) ?: RandomOrderConfig()
    ctx.json(config)
  }

  private fun saveRandomOrderConfig(ctx: Context) {
    val req: RandomOrderConfigReq = ctx.getReqBody()
    RandomCreateOrderService.save(req.sceneId, req.config)
    ctx.status(200)
  }

  data class RandomOrderConfigReq(val sceneId: String, val config: RandomOrderConfig)

  /**
   * 导出运单快照
   */
  private fun exportOrderSnapshot(ctx: Context) {
    val req: ExportOrderReq = ctx.getReqBody()
    val r = OrderReplayService.exportOrdersSnapshot(req)
    ctx.json(r)
  }

  /**
   * 运单重放
   */
  private fun startOrderReplay(ctx: Context) {
    val uploaded = ctx.uploadedFile("f0") ?: throw BzError("errNoUploadedFile", "f0")
    val os: OrdersSnapshot = JsonHelper.mapper.readValue(uploaded.content(), jacksonTypeRef())

    val configStr = ctx.formParam("config") ?: throw BzError("errMissingJson")
    val config: OrderReplayReq = JsonHelper.mapper.readValue(configStr, jacksonTypeRef())

    OrderReplayService.startOrderReplay(os, config)

    ctx.status(200)
  }

  /**
   * 取消运单重做
   */
  private fun stopOrderReplay(ctx: Context) {
    val req: SceneIdReq = ctx.getReqBody()
    OrderReplayService.stopOrderReplay(req.sceneId)

    ctx.status(200)
  }
}