package com.seer.trick.fleet.traffic.distributed.plan.model

import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper
import com.seer.trick.fleet.traffic.distributed.map.Line
import com.seer.trick.fleet.traffic.distributed.map.Point

data class PointData(
  val point: Point,
  val line: Line,
  val g: Double, // 路径的代价
  val h: Double, // 启发函数
  val t: Double, // 旋转代价
  val r: Double, // 倒车、横移代价
  val s: Double, // 停车代价
  val robotHeading: Int,
  val loadingHeading: Int,
  var parent: PointData?,
) {
  // 机器人离开角度
  var robotOutHeading: Int = AngleHelper.ERROR_ANGLE

  // 货架离开角度
  var loadingOutHeading: Int = AngleHelper.ERROR_ANGLE

  fun f(): Double = g * 0.8 + h + t + r + s

  fun key(): String = point.pointName + "-" + line.end.pointName + "-" + robotHeading + "-" + loadingHeading
}

enum class PathType {
  STRAIGHT, // 直线
  ARC, // 表示三阶贝塞尔曲线
  CURVE, // 表示高阶曲线
  SECTOR, // 转动小角度
  ROTATE, // 车旋转
  TURNING, // 带载转
  STOP, // 停止
}

data class Restraint(
  val rePlan: Boolean,
  var forbiddenPoints: MutableList<String> = mutableListOf(),
  var forbiddenLine: MutableList<String> = mutableListOf(),
  var extraCost: MutableMap<String, Int> = mutableMapOf(),
  val cantRotatePoint: MutableList<String> = mutableListOf(),
)