package com.seer.trick.fleet.order

import com.seer.trick.BzError
import com.seer.trick.ComplexQuery
import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.file.FileManager
import com.seer.trick.base.http.handler.MissingQueryParamError
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.order.OrderService.generateOrderId
import com.seer.trick.fleet.service.SceneRuntime
import com.seer.trick.fleet.service.SceneService
import com.seer.trick.helper.JsonHelper
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService

object OrderReplayService {

  private val logger = LoggerFactory.getLogger(javaClass)

  private val runtimes: MutableMap<String, SceneReplayRuntime> = ConcurrentHashMap() // by scene id

  fun dispose() {
    val sceneIds = runtimes.keys.toList()
    for (sceneId in sceneIds) {
      stopOrderReplay(sceneId)
    }
  }

  /**
   * 导出运单快照 返回 json 文件
   */
  fun exportOrdersSnapshot(req: ExportOrderReq): ExportOrderSnapshotResult {
    val orders = if (req.query != null) {
      EntityRwService.findMany(
        
        "TransportOrder",
        Cq.and(Cq.eq("kind", OrderKind.Business.name), req.query),
      )
    } else if (req.ids != null) {
      EntityRwService.findMany(
        "TransportOrder",
        Cq.and(Cq.eq("kind", OrderKind.Business.name), Cq.include("id", req.ids)),
      )
    } else {
      throw MissingQueryParamError("query or ids")
    }

    if (orders.isEmpty()) throw BzError("errNoOrders")

    val orderIds = orders.map(EntityHelper::mustGetId)
    val steps = EntityRwService.findMany("TransportStep", Cq.include("orderId", orderIds))

    val os = OrdersSnapshot(req.remark, orders.size, orders, steps)

    val file = FileManager.nextTmpFile("orders.json").apply {
      writeText(JsonHelper.writeValueAsString(os))
    }
    val path = FileManager.fileToPath(file)

    return ExportOrderSnapshotResult(path, orders.size)
  }

  /**
   * 开始运单重放
   */
  @Synchronized
  fun startOrderReplay(os: OrdersSnapshot, config: OrderReplayReq) {
    if (os.orders.isEmpty()) throw BzError("errNoOrders")

    val oldRt = runtimes[config.sceneId]
    if (oldRt != null) {
      stopOrderReplay(config.sceneId)
    }

    val sr = SceneService.mustGetSceneById(config.sceneId)

    val rt = SceneReplayRuntime(os, config)
    runtimes[config.sceneId] = rt
    rt.worker.submit { bgReplay(sr, rt) }
  }

  /**
   * 停止运单重放
   */
  @Synchronized
  fun stopOrderReplay(sceneId: String) {
    val rt = runtimes.remove(sceneId) ?: return // 未开始
    if (rt.stop) return // 已停止

    rt.stop = true
  }

  /**
   * 后台回放
   */
  private fun bgReplay(sr: SceneRuntime, rt: SceneReplayRuntime) {
    val steps: Map<String, List<TransportStep>> = rt.os.steps.map(OrderService::evToStep).groupBy { it.orderId }

    val orders = rt.os.orders.map(OrderService::evToOrder).sortedBy { it.createdOn }

    try {
      if (orders.isEmpty()) return

      // 开始发单的时间
      val startedOn = System.currentTimeMillis()
      // 最早运单的时间
      var minCreatedOn = 0L

      for (order in orders) {
        if (rt.stop) return

        // 状态过滤
        if (!rt.config.filterStatuses.isNullOrEmpty() && !rt.config.filterStatuses.contains(order.status)) continue

        // 类型过滤
        if (order.kind != OrderKind.Business) continue

        var steps = steps[order.id] ?: continue
        steps = steps.sortedBy { it.stepIndex }

        if (minCreatedOn == 0L) minCreatedOn = order.createdOn.time

        var sleep = order.createdOn.time - minCreatedOn - (System.currentTimeMillis() - startedOn)
        if (sleep < 0) sleep = 0
        Thread.sleep(sleep)

        if (rt.stop) return // 创建之前再次判断是否停止重放
        val or = createOrderRuntime(sr, order, steps, rt.config.keepAllocation)
        OrderService.createOrders(sr, listOf(or))
      }
    } finally {
      rt.stop = true
    }
  }

  /**
   * 拷贝清理数据
   */
  private fun createOrderRuntime(
    sr: SceneRuntime,
    order: TransportOrder,
    steps: List<TransportStep>,
    keepAllocation: Boolean,
  ): OrderRuntime {
    val orderId = generateOrderId()
    val steps = steps.mapIndexed { index, step ->
      step.copy(
        id = "$orderId-Step$index",
        orderId = orderId,
        status = StepStatus.Executable,
        startOn = null,
        endOn = null,
        processingTime = 0.0,
        executingTime = 0.0,
      )
    }

    val expectedRobotNames = if (keepAllocation && order.actualRobotName != null) {
      listOf(order.actualRobotName)
    } else {
      order.expectedRobotNames
    }

    val order = order.copy(
      id = orderId,
      sceneId = sr.sceneId,
      sceneName = sr.basic.name,
      status = OrderStatus.ToBeAllocated,
      expectedRobotNames = expectedRobotNames,
      fault = false,
      faultReason = null,
      currentStepIndex = -1,
      doneStepIndex = -1,
      actualRobotName = null,
      oldRobots = null,
      robotAllocatedOn = null,
      loadPoint = null,
      unloadPoint = null,
      loaded = false,
      unloaded = false,
      doneOn = null,
      dispatchCost = null,
      withdrawnCount = 0,
      processingTime = null,
      executingTime = null,
      failureNum = 0,
      faultDuration = null,
      loadDuration = null,
      unloadDuration = null,
      waitExecuteDuration = null,
    )

    return OrderRuntime(order, steps)
  }

  /**
   * 获取场景的运单重放状态
   */
  fun getReplayStatus(sceneId: String): Boolean = runtimes[sceneId]?.stop ?: true
}

data class ExportOrderReq(val ids: List<String>? = null, val query: ComplexQuery? = null, val remark: String = "")

data class OrdersSnapshot(
  val remark: String,
  val ordersNum: Int,
  val orders: List<EntityValue>,
  val steps: List<EntityValue>,
  val createdOn: Date = Date(),
)

data class ExportOrderSnapshotResult(val path: String, val ordersNum: Int)

/**
 * 重放的选项入参
 */
data class OrderReplayReq(
  val sceneId: String,
  val filterStatuses: List<OrderStatus>? = null, // 状态筛选 null 表示所有状态
  val keepAllocation: Boolean, // 将运单的期望机器人指定为实际机器人 true 为指定
)

class SceneReplayRuntime(val os: OrdersSnapshot, val config: OrderReplayReq) {

  val worker: ScheduledExecutorService = Executors.newSingleThreadScheduledExecutor()

  @Volatile
  var stop = false
}