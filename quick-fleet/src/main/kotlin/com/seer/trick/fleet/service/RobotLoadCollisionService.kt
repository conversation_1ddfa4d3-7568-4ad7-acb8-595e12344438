package com.seer.trick.fleet.service

import com.seer.trick.fleet.domain.GeoHelper
import com.seer.trick.fleet.domain.GeoHelper.translate
import com.seer.trick.fleet.domain.Point2D
import com.seer.trick.fleet.domain.Polygon
import com.seer.trick.fleet.domain.Pose2D
import com.seer.trick.fleet.domain.RobotCollisionModel
import com.seer.trick.fleet.domain.RobotGroup
import com.seer.trick.fleet.domain.RobotLoadCollisionModel
import com.seer.trick.fleet.domain.RobotLoadRelation
import com.seer.trick.fleet.domain.RobotSelfReportMain
import com.seer.trick.fleet.domain.SceneContainerType
import com.seer.trick.fleet.traffic.venus.cache.RobotShapeCache
import kotlin.Boolean
import kotlin.math.abs
import kotlin.math.cos
import kotlin.math.max
import kotlin.math.min
import kotlin.math.sin

/**
 * 机器人与货物碰撞管理
 */
object RobotLoadCollisionService {

  // ---- constants ----
  private const val DIM_TOLERANCE = 0.05 // 尺寸容差（米）

  // ---- internal data types ----
  private data class ChassisInfo(val width: Double, val head: Double, val tail: Double)
  private data class Dimensions(val length: Double, val width: Double)

  /**
   * 根据机器人当前状态和配置，生成机器人与货物的碰撞模型
   * 如果求不出抛异常，不要返回 null
   */
  fun buildCollisionModel(rr: RobotRuntime, main: RobotSelfReportMain): RobotLoadCollisionModel {
    // 机器人底盘信息、安全距离从机器人中取
    //
    // 货物信息：根据 rr.selfReport?.main?.loadRelations 估计容器类型、相关尺寸位置角度
    // 可以只考虑 loadRelations[0]
    // offsetX 可以取容器类型的配置 groupCenterDistances 或根据 loadRelation 计算

    val group = rr.mustGetGroup()
    val chassis = parseRobotChassisInfo(group.collisionModel)
    val (robotWidth, robotHead, robotTail) = chassis
    val safeDistHead = group.safeDistHead
    val safeDistTail = group.safeDistTail
    val safeDistLeft = group.safeDistLeft
    val safeDistRight = group.safeDistRight

    val loadRelations = main.loadRelations ?: emptyList()
    val hasOversizeLoad = loadRelations.isNotEmpty() && group.containerOversize

    // 没有货物 或 货物尺寸在底盘范围内
    if (!hasOversizeLoad) {
      return RobotLoadCollisionModel(
        robotWidth = robotWidth,
        robotHead = robotHead,
        robotTail = robotTail,
        safeDistHead = safeDistHead,
        safeDistTail = safeDistTail,
        safeDistLeft = safeDistLeft,
        safeDistRight = safeDistRight,
        loaded = false,
        containerTypeName = null,
      )
    }

    val loadRelation = loadRelations.first() // 已经确定存在
    val containerInfo = parseContainerInfo(rr, loadRelation, group)

    return RobotLoadCollisionModel(
      robotWidth = robotWidth,
      robotHead = robotHead,
      robotTail = robotTail,
      safeDistHead = safeDistHead,
      safeDistTail = safeDistTail,
      safeDistLeft = safeDistLeft,
      safeDistRight = safeDistRight,
      loaded = true,
      containerTypeName = containerInfo.containerTypeName,
      loadRotatable = containerInfo.loadRotatable,
      loadOuterWidth = containerInfo.loadOuterWidth,
      loadOuterLength = containerInfo.loadOuterLength,
      loadInitTheta = containerInfo.loadInitTheta,
      offsetX = containerInfo.offsetX,
    )
  }

  /**
   * 解析机器人底盘信息
   */
  private fun parseRobotChassisInfo(collisionModel: RobotCollisionModel): ChassisInfo {
    val points = collisionModel.bound.points
    val yValues = points.map { it.y }
    val xValues = points.map { it.x }

    val width = yValues.max() - yValues.min()
    val head = xValues.max()
    val tail = abs(xValues.min())
    return ChassisInfo(width, head, tail)
  }

  /**
   * 解析容器信息
   */
  private fun parseContainerInfo(rr: RobotRuntime, loadRelation: RobotLoadRelation, group: RobotGroup): ContainerInfo {
    val dims = calculateContainerDimensions(loadRelation.points)
    // todo 找不到匹配的容器应该告警
    val matchedType = findMatchingContainerType(rr, dims)

    val offsetX = matchedType?.groupCenterDistances?.get(group.id) ?: 0.0
    val loadRotatable = !group.salverNotRotate
    val loadInitTheta = loadRelation.direction

    return ContainerInfo(
      containerTypeName = matchedType?.name,
      loadRotatable = loadRotatable,
      loadOuterWidth = matchedType?.outerWidth ?: dims.width,
      loadOuterLength = matchedType?.outerLength ?: dims.length,
      loadInitTheta = loadInitTheta,
      offsetX = offsetX,
    )
  }

  /**
   * 根据点位计算容器尺寸
   */
  private fun calculateContainerDimensions(points: List<Point2D>): Dimensions {
    /*
     * 上报的 4 个点按矩形顺序排列，相邻两点连线即为矩形边。
     * 只需取 p0→p1、p1→p2 两段即可得到两条邻边长度。
     * 如果点数不足 3，返回零尺寸以示错误。
     */
    if (points.size < 3) return Dimensions(0.0, 0.0)

    val side1 = GeoHelper.euclideanDistance(points[0], points[1])
    val side2 = GeoHelper.euclideanDistance(points[1], points[2])
    val length = max(side1, side2)
    val width = min(side1, side2)
    return Dimensions(length = length, width = width)
  }

  /**
   * 根据计算出的尺寸匹配系统配置的容器类型。
   * 由于 length/width 可能对调，需要同时比较两种排列。
   */
  private fun findMatchingContainerType(rr: RobotRuntime, dims: Dimensions): SceneContainerType? =
    rr.sr.containerTypes.values.firstOrNull { t ->
      (abs(t.outerLength - dims.length) <= DIM_TOLERANCE && abs(t.outerWidth - dims.width) <= DIM_TOLERANCE) ||
        (abs(t.outerLength - dims.width) <= DIM_TOLERANCE && abs(t.outerWidth - dims.length) <= DIM_TOLERANCE)
    }

  /**
   * 容器信息数据类
   */
  private data class ContainerInfo(
    val containerTypeName: String?,
    val loadRotatable: Boolean,
    val loadOuterWidth: Double,
    val loadOuterLength: Double,
    val loadInitTheta: Double,
    val offsetX: Double,
  )

  /**
   * 根据机器人当前位置、朝向和货物朝向（可选），计算机器人和货物的碰撞位姿
   */
  fun buildCollisionShape(
    model: RobotLoadCollisionModel, // 碰撞模型
    robotPose: Pose2D, // 机器人当前位置朝向（世界坐标系，弧度）
    loadTheta: Double? = null, // 货物当前朝向（世界坐标系，弧度），null 表示没有货物或不指定朝向，保持初始朝向
  ): List<Polygon> {
    // 底盘形状，已考虑安全距离
    val robotWidth: Double = model.robotWidth + model.safeDistLeft + model.safeDistRight
    val robotHead: Double = model.robotHead + model.safeDistHead
    val robotTail: Double = model.robotTail + model.safeDistTail

    val robotCollisionShape = translate(
      RobotShapeCache.getRotated(robotWidth, robotHead, robotTail, robotPose.theta),
      robotPose.x,
      robotPose.y,
    )

    // 若无货物直接返回
    if (!model.loaded) return listOf(robotCollisionShape)

    // 货物矩形，考虑安全距离
    val loadWidth: Double = model.loadOuterWidth + model.safeDistLeft + model.safeDistRight
    val loadHalfLength = model.loadOuterLength / 2.0
    val loadHead: Double = loadHalfLength + model.safeDistHead
    val loadTail: Double = loadHalfLength + model.safeDistTail

    // 货物朝向
    val loadTheta = loadTheta ?: model.loadInitTheta

    // 货物世界坐标
    val loadCx = robotPose.x + model.offsetX * cos(robotPose.theta)
    val loadCy = robotPose.y + model.offsetX * sin(robotPose.theta)

    val loadShape = translate(
      RobotShapeCache.getRotated(loadWidth, loadHead, loadTail, loadTheta),
      loadCx,
      loadCy,
    )

    return listOf(robotCollisionShape, loadShape)
  }
}