package com.seer.trick.fleet.service

import com.seer.trick.fleet.mock.service.MockService
import com.seer.trick.fleet.order.RandomOrderService
import com.seer.trick.fleet.seer.RbkLog
import com.seer.trick.fleet.seer.SeerRbkTcpServer
import com.seer.trick.fleet.traffic.distributed.service.DistributedTrafficService
import org.slf4j.LoggerFactory

/**
 * 三代调度主服务。目前主要用于初始化和销毁调度系统。
 */
object FleetService {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 初始化调度。只能被调用一次。
   */
  fun init() {
    logger.info("Start | Init fleet3")
    // 按需初始化 RBK TCP 服务，全局，所有场景共用
    SeerRbkTcpServer.init()

    // 初始化仿真
    MockService.init()

    // 初始化打印 RBK 请求日志的相关功能，所有场景共用，且要在初始化场景和机器人之前处理。
    RbkLog.init()

    // 初始化所有场景
    SceneService.init()

    // 随机发单
    RandomOrderService.init()

    // 先放这，交管的初始化是对于定时任务的初始化，仅一次，不涉及场景
    DistributedTrafficService.init()
    logger.info("Done | Init fleet3")
  }

  /**
   * 销毁调度。只能被调用一次，在系统关闭时。
   */
  fun dispose() {
    logger.info("Start | Dispose fleet3")

    // 先放这，交管的销毁，对于定时任务的销毁，不涉及场景
    DistributedTrafficService.dispose()

    RandomOrderService.dispose()

    SceneService.dispose()

    // 销毁仿真
    MockService.dispose()

    SeerRbkTcpServer.dispose()
    logger.info("Done | Dispose fleet3")
  }
}