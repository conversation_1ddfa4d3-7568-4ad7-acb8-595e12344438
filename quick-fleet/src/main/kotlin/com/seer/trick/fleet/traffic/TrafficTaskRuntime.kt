package com.seer.trick.fleet.traffic

import com.seer.trick.fleet.FleetLogger
import com.seer.trick.fleet.domain.TrafficTaskSource
import com.seer.trick.fleet.domain.TrafficTaskTarget
import com.seer.trick.fleet.service.MoveActionRuntime
import com.seer.trick.fleet.service.RobotRuntime
import java.util.*
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 表示一个机器人需要交管规划执行的任务。只支持单个区域！
 * 封装信息主要包括： 1、场景和机器人相关；2、起点相关；3、目标相关
 * 为支持未来需求，每个任务可以携带多个目标点。但交管可以暂时只支持单个目标，即只取第一个目标。
 * 此对象不需要持久化。只需要内存表示。
 * 可变对象，会被修改。因此直接建模为一个 Runtime。
 */
class TrafficTaskRuntime(
  val id: String, // 任务 ID
  //
  val sceneId: String, // 场景 ID
  val robotName: String, // 机器人编码
  val robotGroupName: String, // 机器人组名称
  val robotGroupId: Int,
  //
  val orderId: String, // 订单号
  val stepIndex: Int, // 步骤 index
  val stepId: String, // 步骤号
  val secId: String, // StepExecuteContext ID
  //
  val areaId: Int, // 单区域交管任务
  val source: TrafficTaskSource, // 规划的起点
  val target: TrafficTaskTarget, // 目标
  val initiator: Int, // 1 StepExecutor 2 Traffic
) {

  val createdOn: Date = Date()

  @Volatile
  var status: TrafficTaskStatus = TrafficTaskStatus.Created

  @Volatile
  var msg: String? = null

  /**
   * 动作历史
   */
  val moveActions: MutableList<MoveActionRuntime> = CopyOnWriteArrayList()

  /**
   * 更新任务状态。封装这个方法是为了打日志。TODO 但可能这不一定对。可能打更业务化的日志更好。
   */
  fun updateStatus(rr: RobotRuntime, status: TrafficTaskStatus, msg: String?) = rr.sr.withOrderLock {
    FleetLogger.info(
      module = "Traffic",
      subject = "UpdateTaskStatus",
      sr = rr.sr,
      robotName = rr.robotName,
      msg = mapOf(
        "taskId" to id,
        "oldStatus" to this.status,
        "newStatus" to status,
        "msg" to msg,
      ),
    )

    this.status = status
    this.msg = msg
  }

  override fun toString(): String = "TrafficTaskRuntime(id='$id', sceneId='$sceneId', " +
    "robotName='$robotName', robotGroupName='$ robotGroupName', robotGroupId=$robotGroupId, " +
    "orderId='$orderId', stepIndex=$stepIndex, stepId='$stepId', " +
    "source=$source, target=$target, source=$initiator, createdOn=$createdOn, status=$status, msg=$msg)"

  companion object {
    const val SOURCE_STEP_EXECUTOR = 1
    const val SOURCE_TRAFFIC = 2
  }
}

enum class TrafficTaskStatus(val finalStatus: Boolean) {
  Created(false),
  TrafficAccepted(false), // 表示交管初步判断 OK，并不是表示实际路径规划完成
  Success(true),
  Failed(true), // 失败时终态 TODO 去掉
  Cancelled(true),
}