package com.seer.trick.fleet.seer

import com.seer.trick.BzError
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.service.SceneFileService
import com.seer.trick.fleet.service.SceneRuntime
import com.seer.trick.helper.JsonFileHelper
import org.apache.commons.io.FileUtils
import java.io.File
import java.nio.charset.StandardCharsets

/**
 * 导入 M4 场景
 */
object M4SceneImportConverter {

  /**
   * 导入 M4 场景。采用整体拷贝目录的方式。需要一些修复。
   */
  fun importScene(sr: SceneRuntime, tmpDir: File) {
    // 目标场景目录
    val sceneDir = SceneFileService.getSceneDir(sr.sceneId)

    // basic 文件不变
    val basicFile = File(tmpDir, "scene-basic.json")
    val sceneBasic: SceneBasic = JsonFileHelper.readJsonFromFile(basicFile)
      ?: throw BzError("errCodeErr", "Imported scene without basic file")
    basicFile.delete()
    FileUtils.copyFile(File(sceneDir, "scene-basic.json"), basicFile)

    // 修复 areas.json 中的带有场景 ID 的链接，粗暴：直接替换字符串
    val areasFile = File(tmpDir, "areas.json")
    if (areasFile.exists()) {
      var areasStr = FileUtils.readFileToString(areasFile, StandardCharsets.UTF_8)
      areasStr = areasStr.replace(sceneBasic.id, sr.sceneId)
      FileUtils.writeStringToFile(areasFile, areasStr, StandardCharsets.UTF_8)
    }

    // 整体清空目标场景目录
    FileUtils.deleteDirectory(sceneDir)

    // 整体拷贝进去
    FileUtils.copyDirectory(tmpDir, sceneDir)
  }
}