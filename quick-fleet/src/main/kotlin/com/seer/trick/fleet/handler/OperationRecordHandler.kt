package com.seer.trick.fleet.handler

import com.seer.trick.BzError
import com.seer.trick.base.file.FileManager
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.HttpServerManager.sendFileWithType
import com.seer.trick.base.http.handler.Error404
import com.seer.trick.base.http.handler.MissingQueryParamError
import com.seer.trick.fleet.service.OperationRecordService
import com.seer.trick.fleet.service.SceneService
import com.seer.trick.helper.NumHelper
import io.javalin.http.Context
import java.io.FileInputStream

object OperationRecordHandler {
  
  fun registerHandlers() {
    val c = Handlers("api/fleet/operation-record")
    
    c.get("files", ::listFiles, auth())
    c.get("file-content", ::getFileContent, auth())
    c.get("download-file", ::download, auth())
    c.get("slice", ::slice, auth())
  }
  
  private fun listFiles(ctx: Context) {
    val sceneId = ctx.queryParam("sceneId") ?: throw MissingQueryParamError("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)
    var files = sr.opRecorder.listFiles()
    files = files.sortedDescending() // 降序
    ctx.json(files)
  }
  
  private fun getFileContent(ctx: Context) {
    val sceneId = ctx.queryParam("sceneId") ?: throw MissingQueryParamError("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)
    val filename = ctx.queryParam("file") ?: throw MissingQueryParamError("file")
    val file = sr.opRecorder.getFile(filename)
    ctx.contentType("text/plain; charset=utf-8")
    if (!file.exists()) {
      ctx.result("")
    } else if (!FileManager.isFileInFilesDir(file)) {
      throw BzError("errBzError", "Bad file access")
    } else {
      // result 负责关流
      ctx.result(FileInputStream(file))
    }
  }
  
  private fun download(ctx: Context) {
    val sceneId = ctx.queryParam("sceneId") ?: throw MissingQueryParamError("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)
    val filename = ctx.queryParam("file") ?: throw MissingQueryParamError("file")
    val file = sr.opRecorder.getFile(filename)
    
    sendFileWithType(file, ctx)
  }
  
  private fun slice(ctx: Context) {
    val sceneId = ctx.queryParam("sceneId") ?: throw MissingQueryParamError("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)
    val filename = ctx.queryParam("file") ?: throw MissingQueryParamError("file")
    
    val startStr = ctx.queryParam("start") ?: throw MissingQueryParamError("start")
    val endStr = ctx.queryParam("end") ?: throw MissingQueryParamError("end")
    
    val start = NumHelper.anyToLong(startStr) ?: throw MissingQueryParamError("start")
    val end = NumHelper.anyToLong(endStr) ?: throw MissingQueryParamError("end")
    
    val file = sr.opRecorder.sliceToTmpFile(filename, start, end)
      ?: throw Error404("File not found")
    
    sendFileWithType(file, ctx)
  }
}