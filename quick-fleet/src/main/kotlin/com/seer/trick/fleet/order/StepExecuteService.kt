package com.seer.trick.fleet.order

import com.seer.trick.BzError
import com.seer.trick.I18N
import com.seer.trick.base.LifetimeStatus
import com.seer.trick.base.alarm.AlarmItem
import com.seer.trick.base.alarm.AlarmLevel
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.fleet.FleetLogger
import com.seer.trick.fleet.adapter.RobotRbkAdapter
import com.seer.trick.fleet.device.lift.LiftDispatcher
import com.seer.trick.fleet.device.lift.RobotUsingStatus
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.map.MapPointCache
import com.seer.trick.fleet.order.OrderService.countLoadDuration
import com.seer.trick.fleet.order.OrderService.countUnloadDuration
import com.seer.trick.fleet.service.RobotBinService
import com.seer.trick.fleet.service.RobotMoveService
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.RobotService
import com.seer.trick.fleet.service.RobotService.persistRobotRuntime
import com.seer.trick.fleet.traffic.TrafficService
import com.seer.trick.fleet.traffic.TrafficTaskRuntime
import com.seer.trick.fleet.traffic.TrafficTaskStatus
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.getTypeMessage
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.Future

/**
 * 负责控制机器人执行一个运单步骤。
 */
object StepExecuteService {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 机器人正在执行且不能被打断
   * TODO 管不管运单
   */
  fun isExecutingAndWithdrawStepNotAllowed(rr: RobotRuntime): Boolean {
    val sec = rr.executingStep ?: return false
    if (sec.withdrawn) return false
    return !sec.or.withdrawStepAllowed
  }

  /**
   * 在机器人自己的执行线程里执行。
   * OrderProcessingError 统一在这里捕获。
   */
  fun executeStep(rr: RobotRuntime, sec: StepExecuteContext): Future<*> = rr.executingStepExecutor.submit {
    try {
      doExecuteStep(sec)
    } catch (e: OrderProcessingError) {
      // 先判断再打日志
      if (sec.withdrawn) {
        logger.info("executeStep, got OrderProcessingError, and sec.withdrawn is true, try to cancel nav task again")
        // 唯一还尝试做一遍的是取消机器人路径导航
        RobotMoveService.tryToCancelNavTask(rr)
        return@submit
      }
      logger.error("OrderProcessingError[${e.code}], $sec " + e.message)

      if (e.code == OpErrCode.RobotDisposed && !sec.withdrawn) {
        // 如果是关机、销毁机器人等导致执行中断
        cancelExecuting(sec, "[${e.code}] ${e.message}")
      } else {
        afterStepFailed(sec, "[${e.code}] ${e.message}")
      }
      return@submit
    } catch (e: Exception) {
      logger.error("Executing step failed, $sec, unexpected error", e)
      afterStepFailed(sec, "Unexpected error: " + e.getTypeMessage())
      return@submit
    }

    if (sec.withdrawn) return@submit

    afterStepOk(sec)

    // 执行完成，触发新的派发
    DispatchOrderService.dispatchOrders(rr.sr)
  }

  /**
   * 执行一次
   */
  private fun doExecuteStep(sec: StepExecuteContext) {
    val rr = sec.rr
    // 起点
    val start = standToSource(rr) ?: throw OrderProcessingError(OpErrCode.NoStart, "No start")

    val step = sec.getStep()

    // 终点，TODO 要在这里就转换吗？
    if (rr.sr.mapCache.binNames.contains(step.location)) {
      // 如果是库位的话需要校验库位是否停用
      rr.sr.mapCache.areaById.values.forEach {
        if (it.mergedMap.binNameMap[step.location] != null &&
          it.mergedMap.binNameMap[step.location]?.bin?.disabled == true
        ) {
          throw BzError("errNoBins", step.location)
        }
      }
    }
    val endPointCache = rr.sr.mapCache.mustGetPointCacheByGroupAndLoc(rr, step.location)
    if (endPointCache.point.disabled) throw BzError("errNoPointOrDisabled", endPointCache.point.name)
    val endPointAreaId = endPointCache.areaId

    return if (endPointAreaId == start.areaId) {
      // 执行单个区域内的步骤
      executeSingleAreaStep(rr, sec, endPointCache)
    } else {
      // 执行跨区步骤
      executeCrossAreasStep(rr, sec, start, endPointCache)
    }
  }

  /**
   * 执行单个区域的步骤。
   */
  private fun executeSingleAreaStep(rr: RobotRuntime, sec: StepExecuteContext, endPointCache: MapPointCache) {
    executeTrafficTask(rr, sec, endPointCache, true)
  }

  /**
   * 执行跨区步骤。
   */
  private fun executeCrossAreasStep(
    rr: RobotRuntime,
    sec: StepExecuteContext,
    start: TrafficTaskSource,
    endPointCache: MapPointCache,
  ) {
    val startAreaCache = rr.sr.mapCache.mustGetAreaCacheById(start.areaId)
    val endAreaCache = rr.sr.mapCache.mustGetAreaCacheById(endPointCache.areaId)
    logger.debug("Robot {} cross areas, from {} to {}", rr, startAreaCache.schema.name, endAreaCache.schema.name)

    val startPoint = StringUtils.firstNonBlank(start.pointName, start.pathPositions?.firstOrNull()?.toPointName)!!

    // 选电梯
    val selectedLift = LiftDispatcher.findBestLift(
      rr,
      start.areaId,
      endPointCache.areaId,
      startPoint,
      endPointCache.point.name,
    )

    if (sec.withdrawn) throw OrderProcessingError(OpErrCode.Interrupted, "Withdrawn after selecting lift")
    RobotService.throwIfDisabledOrDisposed(rr)
    RobotService.suspendUntilRobotOkToExecuting(rr, "After lift selected")

    if (selectedLift == null) {
      // 大概率是暂时没有可用电梯，未必需要人工干预。
      val errCode = "errCrossAreaLiftsAvailableNone"
      val errParams = listOf(
        rr.robotName,
        sec.toString(),
        "${startAreaCache.schema.name}::${start.pointName}",
        "${endAreaCache.schema.name}::${endPointCache.point.name}",
      )

      rr.stepReject = RejectReason(errCode, errParams)

      val ai = AlarmItem(
        sceneId = rr.sr.sceneId,
        group = "Fleet",
        code = "ExecCrossAreaStepFailed",
        key = "ExecCrossAreaStepFailed-${sec.stepId}",
        level = AlarmLevel.Error,
        message = I18N.lo(errCode, errParams),
        tags = setOf(rr.tag, rr.sr.tag),
      )
      AlarmService.addItem(ai, ttl = 5000)

      throw OrderProcessingError(OpErrCode.NoLift, "No lift")
    }

    val lr = selectedLift.lift
    logger.info("Select lift $lr, sec=$sec, robot=$rr")

    val fromAreaRgMapCache = startAreaCache.groupedMaps[rr.config.groupId]!!
    val toAreaRgMapCache = endAreaCache.groupedMaps[rr.config.groupId]!!

    // TODO 先随机选一条进出的路径
    val enterPathKey = selectedLift.fromFloor.switchMapPoints.find { it.pointName == selectedLift.smPointName }!!
      .enterPaths.first().pathKey
    val enterPathCache = fromAreaRgMapCache.pathKeyMap[enterPathKey]!!
    val preEnterPointCache = fromAreaRgMapCache.pointNameMap[enterPathCache.path.fromPointName]!!
    val fromSmPointCache = fromAreaRgMapCache.pointNameMap[selectedLift.smPointName]!!

    val exitPathKey = selectedLift.toFloor.switchMapPoints.find { it.pointName == selectedLift.smPointName }!!
      .exitPaths.first().pathKey
    val exitPathCache = toAreaRgMapCache.pathKeyMap[exitPathKey]!!
    val postExitPointCache = toAreaRgMapCache.pointNameMap[exitPathCache.path.toPointName]!!
    // val toSmPointCache = toAreaRobotGroupMapCache.pointNameMap[selectedLift.smPointName]!!

    // TODO 中间出错可能需要更精细化的处理
    // TODO 记录进度，主要是已经在电梯内了

    // 第一个交管任务，去前置点
    // TODO 已经到了，不用再发任务
    executeTrafficTask(rr, sec, preEnterPointCache)

    logger.info("Arrived at lift door of start floor, lift=$lr, sec=$sec, robot=$rr")

    if (sec.withdrawn) throw OrderProcessingError(OpErrCode.Interrupted, "Withdrawn after robot at the start door")

    LiftDispatcher.use(lr, rr) {
      // TODO selected.or.withdrawStepAllowed selected.or.withdrawOrderAllowed

      if (sec.withdrawn) throw OrderProcessingError(OpErrCode.Interrupted, "Withdrawn after occupying lift")

      // 呼叫电梯，去起始楼层，开门
      lr.adapter.gotoOpenDoor(rr, selectedLift.fromFloor.index, "Robot to enter the lift")

      logger.info("Door open at start floor, lift=$lr, sec=$sec, robot=$rr")

      if (sec.withdrawn) throw OrderProcessingError(OpErrCode.Interrupted, "Withdrawn after door open at start floor")

      // 使用电梯状态
      lr.usingStatus = RobotUsingStatus.Entering

      // 进电梯
      executeTrafficTask(rr, sec, fromSmPointCache)

      logger.info("Robot in lift, lift=$lr, sec=$sec, robot=$rr")

      // 使用电梯状态
      lr.usingStatus = RobotUsingStatus.Inside

      if (sec.withdrawn) throw OrderProcessingError(OpErrCode.Interrupted, "Withdrawn after robot in lift")

      // 关门
      lr.adapter.closeDoor(rr, "After robot entering")

      logger.info("Door closed at start floor, lift=$lr, sec=$sec, robot=$rr")

      if (sec.withdrawn) throw OrderProcessingError(OpErrCode.Interrupted, "Withdrawn after door close at start floor")

      // 呼叫电梯，去目标楼层，开门
      lr.adapter.gotoOpenDoor(rr, selectedLift.toFloor.index, "Robot to target")

      logger.info("Lift move and open door at target floor, lift=$lr, sec=$sec, robot=$rr")

      if (sec.withdrawn) throw OrderProcessingError(OpErrCode.Interrupted, "Withdrawn after lift open target door")
      RobotService.throwIfDisabledOrDisposed(rr)
      RobotService.suspendUntilRobotOkToExecuting(rr, "After door open")

      // 切地图
      val switched = RobotRbkAdapter.switchMapAndAwait(rr, toAreaRgMapCache.mapName, selectedLift.smPointName) {
        // TODO 坐电梯，运单和步骤应该不允许撤回，只有运单状态的取消
        sec.withdrawn || sec.or.order.status == OrderStatus.Cancelled
      }

      if (!switched) throw OrderProcessingError(OpErrCode.SwitchMapFailed, "Switch map failed")

      logger.info("Robot map switched, lift=$lr, sec=$sec, robot=$rr")

      if (sec.withdrawn) throw OrderProcessingError(OpErrCode.Interrupted, "Withdrawn after map switched")

      // 等待机器人到目标区域
      if (!awaitRobotSwitchMapDone(sec, rr, selectedLift.toFloor.areaId)) {
        throw OrderProcessingError(OpErrCode.SwitchMapFailed, "Switch map failed, map not match")
      }

      if (sec.withdrawn) throw OrderProcessingError(OpErrCode.Interrupted, "Withdrawn after map switched")

      // 切换交管机器人地图
      rr.sr.trafficService.switchMap(rr)

      if (sec.withdrawn) throw OrderProcessingError(OpErrCode.Interrupted, "Withdrawn after switch traffic map")

      // 使用电梯状态
      lr.usingStatus = RobotUsingStatus.Exiting

      // 出电梯
      executeTrafficTask(rr, sec, postExitPointCache)

      logger.info("Robot leave lift, lift=$lr, sec=$sec, robot=$rr")

      // 使用电梯状态
      lr.usingStatus = null

      if (sec.withdrawn) throw OrderProcessingError(OpErrCode.Interrupted, "Withdrawn after robot leave lift")

      // 关门
      lr.adapter.closeDoor(rr, "After robot exiting")

      logger.info("Lift door closed at target floor, lift=$lr, sec=$sec, robot=$rr")
    }

    if (sec.withdrawn) throw OrderProcessingError(OpErrCode.Interrupted, "Withdrawn after leaving and closing lift")

    // 去目标点
    return executeTrafficTask(rr, sec, endPointCache, true)
  }

  /**
   * 一定是同区域的。起点从当前机器人上报位置估算。
   * 在产生和处理 tt 前有问题，抛异常。
   */
  private fun executeTrafficTask(
    rr: RobotRuntime,
    sec: StepExecuteContext,
    endPointCache: MapPointCache,
    finalTask: Boolean = false, // 步骤的最后一步
  ) {
    // 规划前确保没有路径导航
    cancelNavTaskIfAny(rr)

    RobotService.throwIfDisabledOrDisposed(rr)
    RobotService.suspendUntilRobotOkToExecuting(rr, "Before executing tt")

    // 每次执行时从当前位置开始规划
    val start = standToSource(rr) ?: throw OrderProcessingError(OpErrCode.NoStart, "No start")
    // TODO 校验确保与终点是同一区域

    val group = rr.mustGetGroup()
    val step = sec.getStep()
    val containerTypeName = sec.or.order.containerTypeName

    // 当前任务是否带载：已取货、未放货
    val loaded = sec.or.order.loaded && !sec.or.order.unloaded
    val containerOversize = rr.mustGetGroup().containerOversize
    val loadRelations = rr.selfReport?.main?.loadRelations

    val containerTheta = if (containerOversize && loaded && !loadRelations.isNullOrEmpty()) {
      loadRelations[0].direction
    } else {
      null
    }
    // 任务目标
    val endPoint = endPointCache.point
    var ttt = TrafficTaskTarget(
      areaId = start.areaId,
      areaName = start.areaName,
      x = endPoint.x,
      y = endPoint.y,
      pointName = endPoint.name,
      containerId = if (containerOversize && loaded) containerTypeName else null,
      containerStartAngle = containerTheta,
      rbkArgs = JsonHelper.mapper.writeValueAsString(mapOf("operation" to "Wait")),
    )
    if (finalTask) {
      ttt = ttt.copy(
        rbkArgs = step.rbkArgs,
        containerTargetAngle = if (containerOversize && step.forUnload) sec.getUnloadContainerDir() else null,
      )
    }

    val taskId = IdHelper.oidStr()

    val tt = TrafficTaskRuntime(
      id = taskId,
      //
      sceneId = rr.sr.sceneId,
      robotName = rr.robotName,
      robotGroupId = rr.config.groupId,
      robotGroupName = group.name,
      //
      orderId = step.orderId,
      stepIndex = step.stepIndex,
      stepId = step.id,
      secId = sec.exeId,
      //
      areaId = ttt.areaId,
      source = start,
      target = ttt,
      initiator = TrafficTaskRuntime.SOURCE_STEP_EXECUTOR,
    )

    // 设置待处理交管任务
    TrafficService.setPendingTrafficTask(rr, tt)

    // 开始处理执行。也会抛 OrderProcessingError
    RobotMoveService.move(sec, tt)

    // move 执行完，不一定终态：比如如果系统关闭了

    when (tt.status) {
      TrafficTaskStatus.Success -> return
      TrafficTaskStatus.Failed -> throw OrderProcessingError(OpErrCode.TrafficTaskExecuteFailed, tt.msg)
      TrafficTaskStatus.Cancelled -> throw OrderProcessingError(OpErrCode.Interrupted, tt.msg)
      else -> throw OrderProcessingError(OpErrCode.RobotDisposed, "Unexpected traffic status: ${tt.status}")
    }
  }

  /**
   * 执行 tt 前，确保机器人没有正在执行的路径导航，有就取消。
   * 支持尝试取消，不敢保证一定能取消。
   */
  private fun cancelNavTaskIfAny(rr: RobotRuntime) {
    if (!RobotService.isNavTaskGoing(rr)) return

    if (!RobotService.isControlCommandAllowed(rr)) return // 没法处理

    logger.warn("Cancel nav task before planning traffic task")
    try {
      RobotRbkAdapter.cancelNavTask(rr)
    } catch (e: Exception) {
      logger.error("Cancel nav task", e)
    }
  }

  private fun standToSource(rr: RobotRuntime): TrafficTaskSource? {
    // 这些数据拿不到，先返回 null，等下一个周期再处理 selected
    val selfReport = rr.selfReport ?: run {
      rr.stepReject = RejectReason("fetchError", listOf("Failed get robot self report"))
      return null
    }
    val main = selfReport.main ?: run {
      rr.stepReject = RejectReason("fetchError", listOf("Failed get robot main position"))
      return null
    }
    main.x ?: run {
      rr.stepReject = RejectReason("fetchError", listOf("Failed get robot x"))
      return null
    }
    main.y ?: run {
      rr.stepReject = RejectReason("fetchError", listOf("Failed get robot y"))
      return null
    }
    val stand = selfReport.stand ?: run {
      rr.stepReject = RejectReason("standError", listOf("Failed get robot stand"))
      return null
    }
    val areaId = stand.areaId
    val areaCache = rr.sr.mapCache.areaById[areaId] ?: run {
      rr.stepReject = RejectReason("areaIdError", listOf("No area by id $areaId"))
      return null
    }
    main.direction ?: run {
      rr.stepReject = RejectReason("fetchError", listOf("Failed get robot direction"))
      return null
    }

    val startPointName = stand.pointName
    if (startPointName.isNullOrBlank()) {
      return TrafficTaskSource(
        areaId = areaId,
        areaName = areaCache.schema.name,
        x = stand.x,
        y = stand.y,
        theta = stand.theta,
        type = PositionType.Path,
        pathPositions = stand.pathPositions,
      )
    } else {
      // 在点上
      return TrafficTaskSource(
        areaId = areaId,
        areaName = areaCache.schema.name,
        x = stand.x,
        y = stand.y,
        theta = stand.theta,
        type = PositionType.Point,
        pointName = startPointName,
      )
    }
  }

  /**
   * 切地图完成，根据上报的区域（当前地图）和加载状态
   */
  private fun awaitRobotSwitchMapDone(sec: StepExecuteContext, rr: RobotRuntime, areaId: Int): Boolean {
    while (rr.ltStatus != LifetimeStatus.Disposed && !sec.withdrawn) {
      val stand = rr.selfReport?.stand
      val status = rr.selfReport?.rawReport?.get("loadmap_status")
      if (stand?.areaId == areaId && status == 1) return true
      Thread.sleep(500)
    }
    return false
  }

  /**
   * 步骤成功结束。
   */
  fun afterStepOk(sec: StepExecuteContext) = sec.rr.sr.withOrderLock {
    val rr = sec.rr
    val or = sec.or
    val step = sec.getStep()

    val targetBin = rr.bins.find { it.orderId == sec.orderId }

    FleetLogger.info(
      module = "ExecuteStep",
      subject = "StepDoneOk",
      sr = rr.sr,
      robotName = rr.robotName,
      msg = mapOf(
        "orderId" to sec.orderId,
        "stepIndex" to sec.stepIndex,
        "stepId" to sec.stepId,
        "targetBinIndex" to targetBin?.index, // 库位目前的情况也全部输出
        "targetBinPlusOne" to targetBin?.isPlusOne(rr), // 库位是否是货叉
        "targetBinStatus" to targetBin?.status,
      ),
    )

    // 步骤
    val endOn = Date()
    val newStep = step.copy(
      status = StepStatus.Done,
      endOn = endOn,
      processingTime = timeCostOrZero(endOn, step.createdOn),
      executingTime = timeCostOrZero(endOn, step.startOn),
    )
    OrderService.updateAndPersistStep(or, newStep, "Mark step done")

    // 运单
    val newOrder = or.order.copy(
      doneStepIndex = sec.stepIndex,
      // allowModifyRobot = false, // 执行完任何一个步骤就不允许修改机器人了
      status = OrderStatus.Pending,
      loadDuration = countLoadDuration(or),
      unloadDuration = countUnloadDuration(or),
    )
    OrderService.updateAndPersistOrder(or, newOrder, "Mark order pending after step done")
    // 执行完步骤后，允许重分步骤
    or.withdrawStepAllowed = true

    // 机器人
    rr.executingStep = null
    rr.idleFrom = Date()

    if (step.nextStepSameOrder) {
      logger.info("Robot $rr must execute same order ${or.id} step after step ${step.stepIndex}")
      rr.selectSameOrderId = or.id
    } else {
      if (!rr.selectSameOrderId.isNullOrBlank()) {
        logger.info(
          "Clear robot $rr selectSameOrderId=${rr.selectSameOrderId} after step ${step.orderId}:${step.stepIndex}",
        )
        rr.selectSameOrderId = null
      }
    }
    persistRobotRuntime(rr)

    // 处理库位。如果处理失败，标记机器人故障。
    // TODO 可能抛异常。如何处理
    RobotBinService.afterStepDone(rr, sec)

    rr.sr.trafficService.afterLoadOrUnload(rr, sec)
  }

  /**
   * 步骤失败。
   * 如果有 sec，未撤回，去取消其执行。
   */
  fun afterStepFailed(sec: StepExecuteContext, msg: String) = sec.rr.sr.withOrderLock {
    val rr = sec.rr
    val or = sec.or

    FleetLogger.info(
      module = "ExecuteStep",
      subject = "StepDoneFail",
      sr = rr.sr,
      robotName = rr.robotName,
      msg = mapOf(
        "orderId" to sec.orderId,
        "stepIndex" to sec.stepIndex,
        "stepId" to sec.stepId,
      ),
    )

    if (!sec.withdrawn) {
      cancelExecuting(sec, "Cancel executing for step failed: $msg")
    }

    // 运单执行过程中的所有错误，到这里标记
    OrderFaultService.markOrderAndRobotFault(rr.sr, or, sec, msg)
  }

  /**
   * 取消正在执行的运单步骤，
   * 撤回 StepExecuteContext，清空 rr.executingStep
   * 取消 TrafficTask，清空 rr.pendingTrafficTask
   * 取消所有 MoveAction，清空 actions
   * 机器人变为 idle 状态。
   * 运单步骤变为 executable 状态
   * --------
   * 不管运单状态，不处理库位
   */
  fun cancelExecuting(sec: StepExecuteContext, reason: String) {
    val rr = sec.rr

    FleetLogger.info(
      module = "ExecuteStep",
      subject = "CancelExecuting",
      sr = rr.sr,
      robotName = rr.robotName,
      msg = mapOf(
        "reason" to reason,
        "executing" to sec.digest(),
        "trafficTask" to rr.pendingTrafficTask?.id,
        "movesSize" to rr.moveActions.size,
      ),
    )

    sec.or.coolingFrom = System.currentTimeMillis()
    sec.rr.coolingFrom = System.currentTimeMillis()

    rr.sr.withOrderLock {
      if (rr.executingStep != null && rr.executingStep != sec) {
        logger.error("Sec changed, current=${rr.executingStep}, expected=$sec")
        return@withOrderLock
      }

      // StepExecuteContext
      sec.withdrawn = true
      rr.executingStep = null

      // TrafficTask
      val trafficTask = rr.pendingTrafficTask
      trafficTask?.updateStatus(rr, TrafficTaskStatus.Cancelled, "Clean up after interrupted or cancelled")
      rr.pendingTrafficTask = null

      // 取消所有动作
      RobotMoveService.clearAllActions(rr)

      // 机器人
      rr.idleFrom = Date()
      persistRobotRuntime(rr)

      // 重置步骤重分派状态
      sec.or.withdrawStepAllowed = true

      // 步骤 TODO 步骤的其他清理
      OrderService.updateAndPersistStep(sec.or, sec.getStep().copy(status = StepStatus.Executable), reason)
    }

    // 是否取消机器人的执行
    if (!rr.sr.config.notCancelRobot) {
      RobotMoveService.tryToCancelNavTask(rr)
      Thread.sleep(1000) // 先等处理
    }

    // 交管
    // 先强制获取下状态
    RobotService.fetchStateNow(rr)
    // TODO 这里还是有风险取消别人的任务
    rr.sr.trafficService.afterTrafficTaskCancelled(rr)
  }

  /**
   * 计算运单步骤的耗时。单位是秒。如果 startOn = null 时，返回值是 0.0，否则就是具体的时间差。
   */
  private fun timeCostOrZero(endOn: Date, startOn: Date? = null): Double =
    if (startOn == null) 0.0 else (endOn.time - startOn.time) / 1000.0
}