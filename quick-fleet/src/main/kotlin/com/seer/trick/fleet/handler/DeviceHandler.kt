package com.seer.trick.fleet.handler

import com.seer.trick.base.http.*
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.fleet.device.door.DoorService
import com.seer.trick.fleet.device.lift.LiftService
import com.seer.trick.fleet.device.lift.UpdateLiftRecord
import com.seer.trick.fleet.service.SceneService
import io.javalin.http.Context

object DeviceHandler : WebSocketSubscriber() {

  fun registerHandlers() {
    WebSocketManager.subscribers += this

    // 改配置门 和 电梯 的配置信息的相关的接口在 SceneHandler.kt 里面

    val c = Handlers("api/fleet/devices/")

    // 门
    // 修改仿真门的一些状态：在线状态、故障状态等。
    // 门的控制逻辑相对简单，可以同时开、关多扇门。
    c.post("doors/open-batch", ::openDoor, auth()) // 批量开门
    c.post("doors/close-batch", ::closeDoor, auth()) // 批量关门

    // 电梯
    c.post("lifts/update/records", ::updateLiftsRecords, auth()) // 批量更新仿真电梯的状态
    c.post("lifts/goto", ::gotoAndOpenDoor, auth()) // 呼叫电梯
    c.post("lifts/close-door", ::closeLiftDoor, auth()) // 控制电梯关门
  }

  private fun openDoor(ctx: Context) {
    val req: CtrlDoorReq = ctx.getReqBody()
    val sr = SceneService.mustGetSceneById(req.sceneId)

    for (id in req.ids) {
      DoorService.openDoor(sr, id, req.remark)
    }
  }

  private fun closeDoor(ctx: Context) {
    val req: CtrlDoorReq = ctx.getReqBody()
    val sr = SceneService.mustGetSceneById(req.sceneId)

    for (id in req.ids) {
      DoorService.closeDoor(sr, id, req.remark)
    }
  }

  private fun updateLiftsRecords(ctx: Context) {
    val req: UpdateLiftsReq = ctx.getReqBody()
    val sr = SceneService.mustGetSceneById(req.sceneId)

    LiftService.updateMockLiftRecords(sr, req.ids, req.update)

    ctx.status(200)
  }

  private fun gotoAndOpenDoor(ctx: Context) {
    val req: GotoAndOpenDoorReq = ctx.getReqBody()
    val sr = SceneService.mustGetSceneById(req.sceneId)
    val lr = LiftService.mustGetLiftById(sr, req.liftId)
    LiftService.gotoAndOpenDoor(sr, lr, req.targetFloor, "(http) ${req.remark}")
  }

  private fun closeLiftDoor(ctx: Context) {
    val req: CloseDoorReq = ctx.getReqBody()
    val sr = SceneService.mustGetSceneById(req.sceneId)
    val lr = LiftService.mustGetLiftById(sr, req.liftId)
    LiftService.closeDoor(sr, lr, "(http) ${req.remark}")
  }
}

/**
 * 前端请求开关门时，remark 的值为 "from ui"。
 */
data class CtrlDoorReq(val sceneId: String, val ids: Set<Int>, val remark: String)

data class UpdateLiftsReq(val sceneId: String, val ids: Set<Int>, val update: UpdateLiftRecord)

data class GotoAndOpenDoorReq(val sceneId: String, val liftId: Int, val targetFloor: Int, val remark: String = "")

data class CloseDoorReq(val sceneId: String, val liftId: Int, val remark: String = "")