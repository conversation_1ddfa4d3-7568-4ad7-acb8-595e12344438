package com.seer.trick.fleet.diagnosis

import CheckRobotOrderReq
import DiagnosisReport
import com.seer.trick.fleet.domain.RejectReason
import com.seer.trick.fleet.service.MapService
import com.seer.trick.fleet.service.SceneRuntime

/**
 * 调度诊断
 */
object FleetDiagnosisService {

  /**
   * 检查机器人处理运单的一些情况
   */
  fun checkRobotOrder(sr: SceneRuntime, req: CheckRobotOrderReq): RejectReason? {
    val rr = sr.mustGetRobot(req.robotName!!)
    return when (req.subject) {
      // 机器人能否接单
      "RobotOrder" -> RobotOrderPrediction.checkRobotAvailableForBzOrders(rr)
      // 机器人能否接新单
      "RobotNewOrder" -> RobotOrderPrediction.checkRobotAvailableForNewBzOrders(rr)
      // 机器人是否需要停靠
      "RobotParking" -> {
        val fromPoint = MapService.findBestStartPointNameForPlan(rr)
        RobotOrderPrediction.checkRobotShouldParking(rr, fromPoint)
      }
      // 机器人现在是否需要强制充电
      "RobotMustCharging" -> {
        val fromPoint = MapService.findBestStartPointNameForPlan(rr)
        RobotOrderPrediction.checkRobotMustCharging(rr, fromPoint)
      }
      // 机器人现在是否可以充电
      "RobotMayCharging" -> {
        val fromPoint = MapService.findBestStartPointNameForPlan(rr)
        RobotOrderPrediction.checkRobotMayCharging(rr, fromPoint)
      }
      // TODO 机器人能否接这个单
      // TODO 机器人能否到达这个点位
      // TODO 机器人为什么不动
      else -> throw IllegalArgumentException("Unknown subject: ${req.subject}")
    }
  }

  /**
   * 检查机器人处理运单的一些情况
   */
  fun checkRobotOrderAll(sr: SceneRuntime, req: CheckRobotOrderReq): DiagnosisReport = when (req.subject) {
    // 机器人能否接单
    "RobotOrder" -> RobotOrderPrediction.diagnoseRobotAvailableForBzOrders(sr, req)
    // 机器人能否接新单
    "RobotNewOrder" -> RobotOrderPrediction.diagnoseRobotAvailableForNewBzOrders(sr, req)
    // 机器人是否需要停靠
    "RobotParking" -> RobotOrderPrediction.diagnoseRobotShouldParking(sr, req)
    // 机器人现在是否需要强制充电
    "RobotMustCharging" -> RobotOrderPrediction.diagnoseRobotMustCharging(sr, req)
    // 机器人现在是否可以充电
    "RobotMayCharging" -> RobotOrderPrediction.diagnoseRobotMayCharging(sr, req)
    // 机器人能否接这个单
    "RobotCanAcceptThisOrders" -> RobotOrderPrediction.diagnoseRobotCanAcceptThisOrders(sr, req)
    // 机器人能否到达这个点位
    "RobotCanReachPoint" -> RobotOrderPrediction.diagnoseRobotCanReachPoint(sr, req)
    // 机器人为什么不动
    "RobotRemainsStationary" -> RobotOrderPrediction.diagnoseRobotRemainsStationary(sr, req)
    // 运单为啥没有机器人接单
    "OrderNoRobotMatch" -> RobotOrderPrediction.diagnoseOrderNoRobotMatch(sr, req)
    // 运单为啥不继续执行
    "OrderNoContinueExecuting" -> RobotOrderPrediction.diagnoseOrderNoContinueExecuting(sr, req)
    else -> throw IllegalArgumentException("Unknown subject: ${req.subject}")
  }

  /**
   * 获取所有支持的诊断项列表
   */
  fun getDiagnosisList(): Map<String, List<String>> = mapOf(
    "RobotOrder" to listOf("robotName"),
    "RobotNewOrder" to listOf("robotName"),
    "RobotParking" to listOf("robotName"),
    "RobotMustCharging" to listOf("robotName"),
    "RobotMayCharging" to listOf("robotName"),
    "RobotCanAcceptThisOrders" to listOf("robotName", "orderId"),
    "RobotCanReachPoint" to listOf("robotName", "pointName"),
    "RobotRemainsStationary" to listOf("robotName"),
    "OrderNoRobotMatch" to listOf("orderId"),
    "OrderNoContinueExecuting" to listOf("robotName"),
  )
}