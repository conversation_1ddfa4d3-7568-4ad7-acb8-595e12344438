package com.seer.trick.fleet.device.lift.adapter

import com.seer.trick.fleet.device.lift.LiftAdapterReport
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.SceneRuntime

/**
 * 电梯适配器。与梯控通讯，监控状态，下发指令。
 */
abstract class LiftAdapter {

  open fun init(sr: SceneRuntime) {
  }

  /**
   * 不抛异常
   */
  open fun dispose() {
  }

  open fun report(): LiftAdapterReport = LiftAdapterReport()

  /**
   * 到指定楼层并开门
   */
  open fun gotoOpenDoor(rr: RobotRuntime?, targetFloor: Int, remark: String) {
  }

  /**
   * 关门。如果有多个门，关闭所有门
   */
  open fun closeDoor(rr: RobotRuntime?, remark: String) {
  }
}