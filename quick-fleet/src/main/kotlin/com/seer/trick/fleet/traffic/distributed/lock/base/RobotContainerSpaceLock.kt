package com.seer.trick.fleet.traffic.distributed.lock.base

import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper
import com.seer.trick.fleet.traffic.distributed.helper.LockHelper
import com.seer.trick.fleet.traffic.distributed.lock.LockType
import com.seer.trick.fleet.traffic.distributed.lock.graph.*
import com.seer.trick.fleet.traffic.distributed.lock.model.*
import com.seer.trick.fleet.traffic.distributed.map.PosType
import com.seer.trick.fleet.traffic.distributed.map.Position
import com.seer.trick.fleet.traffic.distributed.service.DistributedTrafficService
import com.seer.trick.fleet.traffic.distributed.service.model.ContainerModel
import org.slf4j.LoggerFactory
import java.util.concurrent.*
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.pow
import kotlin.math.sqrt

/**
 *  在机器人身上的货架锁闭模型
 * */
object RobotContainerSpaceLock {

  private val logger = LoggerFactory.getLogger(javaClass)

  private val containerModelMap: MutableMap<String, ContainerModel> = ConcurrentHashMap()

  // key: container + robotGroup , value: cell
  private val rotateCellMap: MutableMap<String, Cell> = ConcurrentHashMap()

  private val staticCellMap: MutableMap<String, Cell> = ConcurrentHashMap()

  /**
   *  新增一个货架模型
   * */
  fun addContainerModel(containerModel: ContainerModel) {
    if (containerModel.height < 0.001 || containerModel.width < 0.001) {
      logger.error("add container type ${containerModel.typeName} model is empty!")
      return
    }
    containerModelMap[typeKey(containerModel.sceneId, containerModel.typeName)] = containerModel
  }

  private fun typeKey(sceneId: String, containerType: String): String = "$sceneId-$containerType"

  /**
   *  修改货架模型
   * */
  fun updateContainerModel(containerModel: ContainerModel) {
    if (containerModel.height < 0.001 || containerModel.width < 0.001) {
      logger.error("update container type ${containerModel.typeName} model is empty!")
      return
    }
    val type = typeKey(containerModel.sceneId, containerModel.typeName)
    val model = containerModelMap[type]
    containerModelMap[type] = containerModel
    model?.groupCenterDistance?.forEach { (t, _) ->
      rotateCellMap.remove(getKey(model.sceneId, model.typeName, t))
      staticCellMap.remove(getKey(model.sceneId, model.typeName, t))
    }
  }

  /**
   *  移除容器模型
   * */
  fun removeContainerModel(sceneId: String, containerType: String) {
    val model = containerModelMap.remove(typeKey(sceneId, containerType))
    model?.groupCenterDistance?.forEach { (t, _) ->
      rotateCellMap.remove(getKey(model.sceneId, model.typeName, t))
      staticCellMap.remove(getKey(model.sceneId, model.typeName, t))
    }
  }

  fun queryContainerModelNarrowDir(sceneId: String, containerType: String): Int =
    containerModelMap[typeKey(sceneId, containerType)]?.narrowDir ?: 0

  /**
   *  key = containerType + robotGroup
   * */
  private fun getKey(sceneId: String, containerType: String, robotGroup: String): String =
    "$sceneId-$containerType-$robotGroup"

  /**
   *  通过货架类型和机器人组名查询货架锁闭单元格
   * */
  private fun queryCellByContainerAndGroup(sceneId: String, container: String, robotGroup: String): Cell? {
    val key = getKey(sceneId, container, robotGroup)
    if (staticCellMap.containsKey(key)) {
      return staticCellMap[key]?.copy()
    }
    logger.debug("build container cell. key=$key")
    buildContainerCell(sceneId, container, robotGroup)
    return staticCellMap[key]?.copy()
  }

  /**
   *  检查机器人在货架下是否可旋转
   * */
  fun checkRobotInContainerCanRotate(sceneId: String, robotGroup: String, container: String): Boolean {
    val containerModel = containerModelMap[typeKey(sceneId, container)]
      ?: throw RuntimeException("container type $container not found!")
//    val distance = containerModel.groupCenterDistance[robotGroup] ?: 0.0

    val radius = 0.5 * sqrt(containerModel.length.pow(2.0) + containerModel.width.pow(2.0))
    val groupModel = StaticRobotSpaceLock.queryRobotModelByGroupName(sceneId, robotGroup)
    if (groupModel == null) {
      logger.error("robot group $robotGroup model is null!")
      return false
    }
    return radius > groupModel.radius
  }

  /**
   *  通过货架类型和机器人组名查询货架旋转锁闭单元格
   * */
  private fun queryRotateCellByContainerAndGroup(sceneId: String, container: String, robotGroup: String): Cell? {
    val key = getKey(sceneId, container, robotGroup)
    if (rotateCellMap.containsKey(key)) {
      return rotateCellMap[key]?.copy()
    }
    logger.debug("build container rotate cell. key=$key")
    buildContainerCell(sceneId, container, robotGroup)
    return rotateCellMap[key]?.copy()
  }

  /**
   *  构建容器基础锁闭单元
   * */
  private fun buildContainerCell(sceneId: String, container: String, robotGroup: String) {
    val key = getKey(sceneId, container, robotGroup)
    val containerModel =
      containerModelMap[typeKey(sceneId, container)] ?: throw RuntimeException("container type $container not found!")
    val distance = containerModel.groupCenterDistance[robotGroup] ?: 0.0
    val groupModel = StaticRobotSpaceLock.queryRobotModelByGroupName(sceneId, robotGroup)
    if (groupModel == null) {
      logger.error("robot group $robotGroup model is null !!")
      return
    }
    val safeDistance = max(groupModel.lengthSafeDistance, groupModel.widthSafeDistance)
    staticCellMap[key] = staticCell(containerModel, distance, safeDistance)
    rotateCellMap[key] = rotateCell(containerModel, distance, safeDistance)
  }

  /**
   *  构建旋转容器在机器人身上的基础锁闭单元
   * */
  private fun rotateCell(containerModel: ContainerModel, distance: Double, safeDistance: Double): Cell {
    val radius = containerModel.radius + abs(distance) + abs(safeDistance)
    val shape = Circle(GraphType.CIRCLE, radius, Vector(0.0, 0.0))
    val layer = Layer(id = 0, top = containerModel.height, bottom = 0.0, shape = shape)
    return Cell(layers = mutableListOf(layer), sPosition = Position(0.0, 0.0, "", PosType.UNDEFINE))
  }

  /**
   *  构建静态容器在机器人身上的基础锁闭单元
   * */
  private fun staticCell(containerModel: ContainerModel, distance: Double, safeDistance: Double): Cell {
    val points = mutableListOf<Vector>()
    points += Vector(-0.5 * containerModel.length + distance - safeDistance, 0.5 * containerModel.width + safeDistance)
    points += Vector(0.5 * containerModel.length + distance + safeDistance, 0.5 * containerModel.width + safeDistance)
    points += Vector(0.5 * containerModel.length + distance + safeDistance, -0.5 * containerModel.width - safeDistance)
    points += Vector(-0.5 * containerModel.length + distance - safeDistance, -0.5 * containerModel.width - safeDistance)
    val shape = Rectangle(type = GraphType.RECTANGLE, points = points, box = BoundingBox(points))
    val layer = Layer(id = 0, top = containerModel.height, bottom = 0.0, shape = shape)
    return Cell(layers = mutableListOf(layer), sPosition = Position(0.0, 0.0, "", PosType.UNDEFINE))
  }

  /**
   *  构建容器锁闭
   * */
  fun buildContainerSpaceLock(
    robotName: String,
    sceneId: String,
    groupName: String,
    point: Position,
    containerHeading: Int,
    mapName: String,
    container: String,
  ): SpaceLock {
    if (containerHeading == AngleHelper.ERROR_ANGLE) {
      logger.error("containerHeading is error, containerHeading=$containerHeading")
      DistributedTrafficService.trafficAlarm(
        sceneId = sceneId,
        robotName = robotName,
        code = "T00020002",
        args = listOf(robotName, container),
      )
      throw Exception("buildContainerSpaceLock, containerHeading is error")
    }
    var containerName = container
    if (container.isEmpty()) {
      val first = containerModelMap.keys.first()
      containerName = first.split("-")[0]
    }
    val cell = queryCellByContainerAndGroup(sceneId, containerName, groupName)
    if (cell == null) {
      logger.error("spaceLock is null, container=$container, robotName=$robotName, groupName=$groupName")
      DistributedTrafficService.trafficAlarm(
        sceneId = sceneId,
        robotName = robotName,
        code = "T00020003",
        args = listOf(robotName, container, groupName),
      )
      throw Exception("buildContainerSpaceLock, spaceLock is null")
    }
    // 角度判断
    if (!AngleHelper.sameAngleInFiveDegree(containerHeading, 0)) {
      LockHelper.lockModelRotation(cell, containerHeading)
    }
    LockHelper.lockModelTranslation(cell, point)
    cell.sPosition = point
    return SpaceLock(
      groupName = groupName,
      type = LockType.ROBOT,
      name = robotName,
      cells = mutableListOf(cell),
      mapName = mapName,
    )
  }

  /**
   *  构建容器旋转锁闭
   * */
  fun buildContainerRotateSpaceLock(
    robotName: String,
    sceneId: String,
    groupName: String,
    point: Position,
    mapName: String,
    container: String,
  ): SpaceLock {
    var containerName = container
    if (container.isEmpty()) {
      val first = containerModelMap.keys.first()
      containerName = first.split("-")[0]
    }
    val cell = queryRotateCellByContainerAndGroup(sceneId, containerName, groupName)
    if (cell == null) {
      logger.error("spaceLock is null, container=$container, robotName=$robotName, groupName=$groupName")
      DistributedTrafficService.trafficAlarm(
        sceneId = sceneId,
        robotName = robotName,
        code = "T00020003",
        args = listOf(robotName, container, groupName),
      )
      throw Exception("buildRotateSpaceLock, spaceLock is null")
    }
    LockHelper.lockModelTranslation(cell, point)
    cell.sPosition = point
    return SpaceLock(
      groupName = groupName,
      type = LockType.ROBOT,
      name = robotName,
      cells = mutableListOf(cell),
      mapName = mapName,
    )
  }
}