package com.seer.trick.fleet.mars

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.script.ScriptCenter
import com.seer.trick.base.script.ScriptExeRequest
import com.seer.trick.falcon.FalconEventBus
import com.seer.trick.falcon.FalconEventListener
import com.seer.trick.falcon.TaskEvent
import com.seer.trick.falcon.domain.CreateTaskReq
import com.seer.trick.falcon.task.FalconTaskDefService
import com.seer.trick.falcon.task.FalconTaskService
import com.seer.trick.fleet.service.RobotService
import com.seer.trick.fleet.service.SceneRuntime
import com.seer.trick.robot.light.CpnOrderToFalconRes
import com.seer.trick.robot.rachel.MrRobotInfoAll
import com.seer.wcs.GeoHelper
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.Executors
import java.util.concurrent.Future

/**
 * Fleet 3 临时版光通讯派单
 */
class MarsDispatcher(private val sceneRuntime: SceneRuntime, private val marsService: MarsService) :
  FalconEventListener {

  private val logger = LoggerFactory.getLogger(javaClass)

  private val dispatchExecutor = Executors.newSingleThreadExecutor()

  @Volatile
  private var future: Future<*>? = null

  @Volatile
  private var disposed = false

  fun init() {
    FalconEventBus.listeners += this

    future = dispatchExecutor.submit {
      while (!disposed) {
        val config = sceneRuntime.config

        // val cpnEnabled = BzConfigManager.getByPath("ScWcs", "cpn", "enabled") == true
        val cpnEnabled = config.cpnEnabled
        // var delay = NumHelper.anyToLong(BzConfigManager.getByPath("ScWcs", "cpn", "dispatchPeriod")) ?: 3000
        var delay = config.cpnDispatchPeriod ?: 0L
        if (delay <= 0) delay = 1000
        if (cpnEnabled) {
          try {
            dispatch()
          } catch (e: Exception) {
            logger.error("光通讯派单失败", e)
          }
        }
        Thread.sleep(delay)
      }
    }
  }

  fun dispose() {
    disposed = true
    future?.cancel(false)
    FalconEventBus.listeners -= this
  }

  // TODO 暂时只考虑了单负载车
  private fun dispatch() {
    // 列出所有未派车的容器搬运单
    // 排序：优先级、创建时间
    val orders = EntityRwService.findMany(
      "ContainerTransportOrder",
      Cq.eq("status", "Created"),
      FindOptions(sort = listOf("-priority", "createdOn")),
    )

    for (order in orders) {
      val orderId = EntityHelper.mustGetId(order)

      val robot = tryToAssignRobot(order) ?: continue

      var taskParams = mapOf(
        "orderId" to orderId,
        "orderKind" to order["kind"],
        "container" to order["container"],
        "robotName" to robot.id,
        "robotCurrentSite" to robot.selfReport?.main?.currentSite,
        "fromBin" to order["fromBin"] as String?,
        "toBin" to order["toBin"] as String?,
      )

      // 处理的猎鹰任务，在运单上指定或由脚本决定
      var falconTaskDefLabel = order["falconTaskDefLabel"] as String?
      if (falconTaskDefLabel.isNullOrBlank()) {
        val otfRes: CpnOrderToFalconRes? = ScriptCenter.execute(
          ScriptExeRequest("cpnOrderToFalcon", arrayOf(order)),
          jacksonTypeRef(),
        )
        falconTaskDefLabel = otfRes?.falconTaskDefLabel
        if (falconTaskDefLabel.isNullOrBlank()) {
          EntityRwService.updateOne(
            "ContainerTransportOrder",
            Cq.idEq(orderId),
            mutableMapOf("status" to "Failed", "errMsg" to "未匹配到处理此运单的猎鹰任务模板"),
          )
          continue
        }

        // 脚本返回额外参数
        val eip = otfRes?.extraInputParams
        if (!eip.isNullOrEmpty()) taskParams = taskParams + eip
      }

      val taskDef = FalconTaskDefService.getTaskDefByLabel(falconTaskDefLabel)
      if (taskDef == null) {
        EntityRwService.updateOne(
          "ContainerTransportOrder",
          Cq.idEq(orderId),
          mutableMapOf("status" to "Failed", "errMsg" to "找不到猎鹰任务：$falconTaskDefLabel"),
        )
        continue
      }

      val tr = FalconTaskService.createTopTask(CreateTaskReq(taskDef.id, taskParams))

      logger.info("分配容器搬运单 '$orderId' 到机器人 '${robot.id}'。 $order")

      // 搬运单状态
      EntityRwService.updateOne(
        "ContainerTransportOrder",
        Cq.idEq(orderId),
        mutableMapOf(
          "status" to "Assigned",
          "errMsg" to "",
          "robotName" to robot.id,
          "falconTaskId" to tr.taskId,
          "falconTaskDefId" to taskDef.id,
          "falconTaskDefLabel" to taskDef.label,
        ),
      )

      // 机器人状态
      EntityRwService.updateOne(
        "MrRobotRuntimeRecord",
        Cq.idEq(robot.id),
        mutableMapOf("taskStatus" to "Tasking", "ctOrders" to listOf(orderId)),
      )

      // 执行猎鹰任务
      FalconTaskService.runTaskAsync(tr)
    }

    // TODO 充电
  }

  // 找一个机器人
  private fun tryToAssignRobot(order: EntityValue): MrRobotInfoAll? {
    val idleRobots = listIdleRobots()
    if (idleRobots.isEmpty()) return null

    // 如果指定了机器人，看指定机器人能否可用
    val expectedRobot = order["expectedRobot"] as String?
    if (!expectedRobot.isNullOrBlank()) {
      val idleRobot = idleRobots.find { it.id == expectedRobot } ?: return null
      return idleRobot
    }

    // 距离最近的接单
    val firstBin = StringUtils.firstNonBlank(order["fromBin"] as String?, order["toBin"] as String?)
    if (!firstBin.isNullOrBlank()) {
      return findClosestRobot(firstBin, idleRobots)
    }

    return idleRobots.firstOrNull()
  }

  /**
   * 未禁用、可接单、在线、空闲（taskStatus）的机器人
   * TODO 电量过低的也不接单
   */
  private fun listIdleRobots(): List<MrRobotInfoAll> {
    val all: MutableList<MrRobotInfoAll> = mutableListOf()
    for (rr in marsService.listRobots()) {
      if (rr.config.disabled || rr.offDuty || !RobotService.isOnline(rr)) continue
      val ra = marsService.getRobotInfoAll(rr)
      val taskStatus = ra.runtimeRecord["taskStatus"] as String? ?: "Idle"
      if (taskStatus != "Idle") continue
      all += ra
    }
    return all
  }

  // 距离某个站点最近的机器人，最简单的直线距离估测
  private fun findClosestRobot(firstBin: String, robots: List<MrRobotInfoAll>): MrRobotInfoAll = robots.minBy {
    val main = it.selfReport?.main
    val robotX = main?.x
    val robotY = main?.y
    if (robotX != null && robotY != null) {
      val firstPoint = marsService.getPointByLoc(it.id, firstBin)
      if (firstPoint != null) {
        GeoHelper.euclideanDistance(robotX, robotY, firstPoint.x, firstPoint.y)
      } else {
        Double.MAX_VALUE
      }
    } else {
      Double.MAX_VALUE
    }
  }

  /**
   * 机器人从任务中变为空闲；清空运单
   */
  fun releaseRobotOrder(robotId: String) {
    val rrr = EntityRwService.findOneById("MrRobotRuntimeRecord", robotId)
    if (rrr == null) {
      logger.error("释放机器人运单状态，未找到运行记录。机器人=$robotId。")
      return
    }
    val taskStatus = rrr["taskStatus"]
    val ctOrders = rrr["ctOrders"]
    logger.info("释放机器人运单状态。机器人=$robotId，当前状态=$taskStatus，当前运单=$ctOrders")

    EntityRwService.updateOne(
      "MrRobotRuntimeRecord",
      Cq.idEq(robotId),
      mutableMapOf("taskStatus" to "Idle", "ctOrders" to null),
    )
  }

  override fun onFalconEvent(event: TaskEvent) {
    // 兼容多负载车的场景
    val orders = EntityRwService.findMany(
      "ContainerTransportOrder",
      Cq.eq("falconTaskId", event.taskId),
    )
    for (order in orders) {
      val orderId = EntityHelper.mustGetId(order)
      val robotName = order["robotName"] as String?
      if (event.event == "TaskSuccess") {
        // 猎鹰任务成功
        if (order["status"] == "Done") {
          logger.error(
            "猎鹰任务完成，但容器搬运单已经是完成的。猎鹰任务=${event.taskId}，容器搬运单=$orderId，机器人=$robotName",
          )
          return
        }
        logger.info(
          "猎鹰任务完成，处理容器搬运单，机器人释放。猎鹰任务=${event.taskId}，容器搬运单=$orderId，机器人=$robotName，" +
            "运单详情=$order",
        )
        EntityRwService.updateOne(
          "ContainerTransportOrder",
          Cq.idEq(orderId),
          mutableMapOf("status" to "Done", "doneOn" to Date()),
        )
        if (!robotName.isNullOrBlank()) releaseRobotOrder(robotName)
      } else if (event.event == "TaskFail") {
        // 猎鹰任务故障，这边也跟着故障吧
        val errMsg = event.taskEv["endedReason"]
        logger.warn(
          "猎鹰任务故障，处理容器搬运单。故障原因=$errMsg，猎鹰任务=${event.taskId}，容器搬运单=$orderId，机器人=$robotName，" +
            "运单详情=$order",
        )
        EntityRwService.updateOne(
          "ContainerTransportOrder",
          Cq.idEq(orderId),
          mutableMapOf("status" to "Failed", "errMsg" to "猎鹰任务故障：$errMsg"),
        )
      } else if (event.event == "TaskFailByOrderFault") {
        // 运单故障导致猎鹰任务故障，但此时猎鹰任务不能被标记为故障状态，故而新增一个事件名
        // 将关联的 CTO 标记为失败状态，逻辑参考 TaskFail 处
        val counts = EntityRwService.updateOne(
          "ContainerTransportOrder",
          Cq.and(listOf(Cq.idEq(orderId), Cq.include("status", listOf("Building", "Created", "Assigned", "Failed")))),
          mutableMapOf("status" to "Failed", "errMsg" to "关联的猎鹰任务创建的调度运单故障了"),
        )
        logger.error(
          "猎鹰任务故障，处理容器搬运单。故障原因=猎鹰任务创建的三代调度运单故障, 猎鹰任务=${event.taskId}，" + // todo 日志中补充调度运单 ID
            "容器搬运单=$orderId，机器人=$robotName，运单详情=$order，更新的记录数=$counts",
        )
      } else if (event.event == "TaskAborted" || event.event == "TaskCancelled") {
        // TODO 猎鹰任务的终态失败，但不意味着运单也必须终态失败
        val opt = if (event.event == "TaskAborted") "终止" else "取消"
        val counts = EntityRwService.updateOne(
          "ContainerTransportOrder",
          Cq.and(listOf(Cq.idEq(orderId), Cq.include("status", listOf("Building", "Created", "Assigned", "Failed")))),
          mutableMapOf("status" to "Cancelled", "errMsg" to "关联的猎鹰任务被$opt"),
        )
        logger.warn(
          "猎鹰任务取消或放弃（${event.event}），处理容器搬运单。猎鹰任务=${event.taskId}，容器搬运单=$orderId，" +
            "机器人=$robotName，运单详情=$order，取消 $counts 条容器搬运单",
        )
      } else if (event.event == "TaskRecover") {
        // 猎鹰任务故障重试了，将关联的 Failed 状态的 CTO 置为 Created（已提交） 或 Assigned（已派车）状态

        val taskId = event.taskId
        // 如果 CTO 已经有执行机器人了，则将其重置为 Created 状态
        val expectedStatus = if (robotName.isNullOrBlank()) "Created" else "Assigned"
        val counts = EntityRwService.updateOne(
          "ContainerTransportOrder",
          Cq.and(listOf(Cq.eq("falconTaskId", taskId), Cq.eq("status", "Failed"))), // 仅处理 Failed 状态的。
          mutableMapOf("status" to expectedStatus, "errMsg" to ""),
        )
        logger.info(
          "用户重试故障任务 taskId=$taskId 后，更新关联的 Failed 的容器搬运单状态为 $expectedStatus，更新的记录数=$counts"
        )
      }
    }
  }
}

data class CpnOrderToFalconRes(val falconTaskDefLabel: String, val extraInputParams: EntityValue?)