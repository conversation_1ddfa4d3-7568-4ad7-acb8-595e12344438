package com.seer.trick.fleet.seer

import com.seer.trick.base.file.FileManager
import com.seer.trick.fleet.domain.EnvPointCloud
import com.seer.trick.fleet.domain.GeoHelper
import java.awt.image.BufferedImage
import java.awt.image.WritableRaster
import javax.imageio.ImageIO
import javax.imageio.ImageWriteParam
import javax.imageio.ImageWriter
import javax.imageio.stream.ImageOutputStream
import kotlin.math.ceil
import kotlin.math.floor
import kotlin.math.round

/**
 * 环境点云的处理。
 */
object EnvPointCloudHelper {

  /**
   * 产生环境点云图片和相关信息。如果点云为空，返回 null。
   */
  fun parseSmap(normalPosList: Collection<SmapPos>): EnvPointCloud? = if (normalPosList.isEmpty()) {
    null
  } else {
    val maxPx = 4096

    val bound = GeoHelper.calcBound2D(normalPosList, { it.x }, { it.y })
    val widthInM = bound.maxX - bound.minX
    val heightInM = bound.maxY - bound.minY

    val pxByM = if (widthInM > heightInM) {
      maxPx / widthInM
    } else {
      maxPx / heightInM
    }

    val widthInPx = ceil(widthInM * pxByM).toInt()
    val heightInPx = ceil(heightInM * pxByM).toInt()

    val originXInPx = floor(bound.minX * pxByM).toInt()
    val originYInPx = floor(-bound.maxY * pxByM).toInt()

    // 创建支持透明背景的图像类型 TYPE_INT_ARGB
    val image = BufferedImage(widthInPx, heightInPx, BufferedImage.TYPE_INT_ARGB)
    val raster: WritableRaster = image.raster

    // 彩色像素值：黑色【前景色】
    val black = intArrayOf(0, 0, 0, 255)

    // 透明背景：透明像素值
    val transparent = intArrayOf(0, 0, 0, 0)

    // 创建用于存储需要绘制的像素坐标的集合
    val pixels = mutableSetOf<Pair<Int, Int>>()

    normalPosList.forEach { p ->
      val x = round(p.x * pxByM).toInt() - originXInPx
      val y = round(-p.y * pxByM).toInt() - originYInPx

      if (x in 0 until widthInPx && y in 0 until heightInPx) {
        pixels.add(Pair(x, y))
      }
    }

    // 初始化整个图像为透明背景
    for (y in 0 until heightInPx) {
      for (x in 0 until widthInPx) {
        raster.setPixel(x, y, transparent) // 把所有像素初始化为透明
      }
    }

    // 将黑色像素填充到点云位置处
    pixels.forEach { (x, y) ->
      raster.setPixel(x, y, black) // 仅在点云位置设置为黑色
    }

    val imageFile = FileManager.nextTmpFile("png", prefix = "pc-env-")

    val writer: ImageWriter = ImageIO.getImageWritersByFormatName("png").next()
    val ios: ImageOutputStream = ImageIO.createImageOutputStream(imageFile)
    writer.output = ios

    val writeParam: ImageWriteParam = writer.defaultWriteParam

    if (writeParam.canWriteCompressed()) {
      writeParam.compressionMode = ImageWriteParam.MODE_EXPLICIT
      writeParam.compressionQuality = 0.5f
    }

    writer.write(null, javax.imageio.IIOImage(image, null, null), writeParam)

    writer.dispose()
    ios.close()

    EnvPointCloud(
      FileManager.fileToPath(imageFile),
      width = widthInM,
      height = heightInM,
      imageWidth = widthInPx,
      imageHeight = heightInPx,
      scale = pxByM,
      imageOriginX = originXInPx,
      imageOriginY = originYInPx,
    )
  }
}