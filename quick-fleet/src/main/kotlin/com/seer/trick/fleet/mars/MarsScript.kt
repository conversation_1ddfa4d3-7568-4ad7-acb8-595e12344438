package com.seer.trick.fleet.mars

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.base.MapToAnyNull
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.script.ScriptRobustExecutor
import com.seer.trick.fleet.mars.MarsCenter.getSceneByNameOrFirstMarsScene
import com.seer.trick.fleet.service.RobotService
import com.seer.trick.helper.JsonHelper
import com.seer.trick.robot.rachel.MrRobotInfoAll
import org.slf4j.LoggerFactory

/**
 * Fleet 3 临时版，扩展脚本
 */
object MarsScript {

  private val logger = LoggerFactory.getLogger(javaClass)

  fun isRobotOnline(robotName: String, sceneName: String? = null): Boolean {
    val sr = getSceneByNameOrFirstMarsScene(sceneName)
    return RobotService.isOnline(sr.mustGetRobot(robotName))
  }

  /**
   * 未禁用、可接单、在线的机器人
   */
  fun listWorkableRobots(sceneName: String? = null): List<MrRobotInfoAll> {
    val sr = getSceneByNameOrFirstMarsScene(sceneName)

    val all: MutableList<MrRobotInfoAll> = mutableListOf()
    for (rr in sr.listRobots()) {
      if (rr.config.disabled || rr.offDuty || !RobotService.isOnline(rr)) continue
      val ra = sr.marsService.getRobotInfoAll(rr)
      all += ra
    }
    return all
  }

  /**
   * 未禁用、可接单、在线、空闲（扩展任务状态）的机器人
   */
  fun listExtIdleRobots(sceneName: String? = null): List<MrRobotInfoAll> {
    val sr = getSceneByNameOrFirstMarsScene(sceneName)

    val all: MutableList<MrRobotInfoAll> = mutableListOf()
    for (rr in sr.listRobots()) {
      if (rr.config.disabled || rr.offDuty || !RobotService.isOnline(rr)) continue
      val ra = sr.marsService.getRobotInfoAll(rr)
      if (ra.runtimeRecord["extTaskStatus"] != "Idle") continue
      all += ra
    }
    return all
  }

  fun getCost(robotName: String, startSiteId: String, endSiteId: String, sceneName: String? = null): Double {
    val sr = getSceneByNameOrFirstMarsScene(sceneName)
    return sr.marsService.calcCost(robotName, startSiteId, endSiteId)
  }

  fun mustGetRobotInfoAll(robotName: String, sceneName: String? = null): MrRobotInfoAll {
    val sr = getSceneByNameOrFirstMarsScene(sceneName)
    val rr = sr.mustGetRobot(robotName)

    return sr.marsService.getRobotInfoAll(rr)
  }

  fun findClosestRobotConnectedPoint(robotName: String, sceneName: String? = null): EntityValue? {
    val sr = getSceneByNameOrFirstMarsScene(sceneName)

    return sr.marsService.findClosestRobotConnectedPoint(robotName)
  }

  fun buildSeerMoves(
    robotId: String,
    rawSteps: String,
    sceneName: String? = null,
  ): List<EntityValue> {
    val sr = getSceneByNameOrFirstMarsScene(sceneName)

    val steps: List<EntityValue> = JsonHelper.mapper.readValue(rawSteps, jacksonTypeRef())
    return sr.marsService.scheduler.buildMoves(robotId, steps)
  }

  /**
   * 采用 RunOnce 的方式，创建直接运单，并等待其完成。
   * 注意如果 3066，moves 要填充好路径
   */
  fun awaitRunOnceDirectRobotOrder(
    robotName: String,
    mainId: String,
    action: String,
    seer3066: Boolean,
    moves: List<MapToAnyNull>,
  ): String {
    logger.info(
      "采用 RunOnce 的方式，创建直接运单，并等待其完成。" +
        "机器人=$robotName, mainId=$mainId, action=$action, 3066=$seer3066，moves=$moves",
    )
    val r = ScriptRobustExecutor.runOnce(mainId, action) {
      val orderId = createDirectRobotOrder(robotName, mainId, action, seer3066, moves)
      mutableMapOf("orderId" to orderId)
    }
    val orderId = r["orderId"] as String
    awaitDirectRobotOrder(robotName, orderId)
    return orderId
  }

  private fun createDirectRobotOrder(
    robotName: String,
    taskId: String,
    description: String,
    seer3066: Boolean,
    moves: List<MapToAnyNull>,
  ): String {
    val movesStr = JsonHelper.mapper.writeValueAsString(moves)
    val ev: EntityValue = mutableMapOf(
      "taskId" to taskId, // 运单所属的任务编号
      "robotName" to robotName,
      "status" to "Created",
      "moves" to movesStr,
      "description" to description,
      "seer3066" to seer3066,
    )
    val orderId = EntityRwService.createOne("DirectRobotOrder", ev)

    logger.info("创建直接运单完成，'$orderId'，机器人=$robotName")

    // TODO 要不要
    //   soc.updateStringNode(`Robot::CurrentOrder::${robotName}`, `机器人::当前运单::${robotName}`,
    //     `${orderId}:${description}`, "None")

    return orderId
  }

  private fun awaitDirectRobotOrder(robotName: String, orderId: String) {
    try {
      // 等待运单完成
      while (true) {
        val order = EntityRwService.findOneById("DirectRobotOrder", orderId)
          ?: throw BzError("errDirectRobotOrderNonexistent", orderId)
        val status = order["status"] as String
        if (status == "Done") {
          logger.info("直接运单，完成，'$orderId'，机器人=$robotName")
          break
        } else if (status == "Failed") {
          logger.info("直接运单，失败，'$orderId'，机器人=$robotName")
          throw BzError("errDirectRobotOrderFailed", orderId)
        }
        Thread.sleep(500)
      }
    } finally {
      // soc.removeNode(`Robot::CurrentOrder::${robotName}`)
    }
  }

  fun unlockByRobot(robotId: String, sceneName: String? = null) {
    val sr = getSceneByNameOrFirstMarsScene(sceneName)
    sr.marsService.scheduleService.unlockByRobot(robotId)
  }

  fun unlockBySiteIds(robotId: String, siteIds: List<String>, sceneName: String? = null) {
    val sr = getSceneByNameOrFirstMarsScene(sceneName)
    sr.marsService.scheduleService.unlockBySiteIds(robotId, siteIds)
  }

  fun tryLockOneSiteByName(
    @Suppress("UNUSED_PARAMETER") 
    robotId: String,
    siteIds: List<String>,
    sceneName: String? = null,
  ): String? {
    val sr = getSceneByNameOrFirstMarsScene(sceneName)

    return sr.marsService.scheduleService.tryLockOneSiteBySiteId(robotId, siteIds)
  }

  /**
   * 重置所有扩展机器人，终止所有后台任务，清楚所有地图资源锁
   */
  fun resetExtRobots(sceneName: String? = null) {
    logger.info("重置所有扩展机器人")
    val sr = getSceneByNameOrFirstMarsScene(sceneName)

    // 先令所有机器人停止接单
    // val robotConfigs = EntityRwService.findMany("MrRobotSystemConfig", Cq.all())
    for (rr in sr.listRobots()) RobotService.updateOffDuty(rr, true)

    // 放弃后台任务
    val evList = EntityRwService.findMany("RobustScriptExecutor", Cq.all())
    logger.info("未完成后台任务数：${evList.size}")
    for (ev in evList) {
      ScriptRobustExecutor.abort(EntityHelper.mustGetId(ev))
    }

    // 重启脚本
    // ScriptCenter.reload()
    // logger.info("resetExtRobots 脚本已重启")

    // 机器人状态空闲
    EntityRwService.updateMany(
      "MrRobotRuntimeRecord",
      Cq.all(),
      mutableMapOf("extTaskStatus" to "Idle", "ctOrders" to null, "taskStatus" to "Idle"),
    )

    // 释放资源
    sr.marsService.scheduleService.unlockAll()

    // 释放申请
    sr.marsService.scheduler.reset()

    // 恢复之前的接单设置
    //    for (rcEv in robotConfigs) {
    //      EntityRwService.updateOne(
    //        "MrRobotSystemConfig", Cq.idEq(EntityHelper.mustGetId(rcEv)), mutableMapOf("offDuty" to rcEv["offDuty"])
    //      )
    //    }
  }

  /**
   * 脚本路径规划一次的方法
   */
  fun tryBuildSeerMoves(
    robotId: String,
    rawSteps: String,
    sceneName: String? = null,
  ): List<EntityValue> {
    val sr = getSceneByNameOrFirstMarsScene(sceneName)

    val steps: List<EntityValue> = JsonHelper.mapper.readValue(rawSteps, jacksonTypeRef())
    return sr.marsService.scheduler.tryBuildMoves(robotId, steps)
  }
}