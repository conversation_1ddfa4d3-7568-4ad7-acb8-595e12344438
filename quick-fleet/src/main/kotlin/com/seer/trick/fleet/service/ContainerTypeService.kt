package com.seer.trick.fleet.service

import com.seer.trick.fleet.domain.SceneContainerType
import com.seer.trick.helper.JsonFileHelper
import org.slf4j.LoggerFactory
import java.io.File

/**
 * 容器类型管理。
 */
object ContainerTypeService {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 加载并初始化一个场景下的容器类型
   */
  fun init(sr: SceneRuntime) = sr.withOrderLock {
    val containerTypes: List<SceneContainerType> =
      JsonFileHelper.readJsonFromFile(getContainerTypeFile(sr.sceneId)) ?: emptyList()
    sr.containerTypes = containerTypes.associateBy { it.id }
  }

  /**
   * 添加容器类型。
   */
  fun add(sr: SceneRuntime, req: SceneContainerType): SceneContainerType {
    val containerType = sr.withOrderLock {
      val containerType = req.copy(id = (sr.containerTypes.values.maxOfOrNull { it.id } ?: 0) + 1)
      val containerTypes = sr.containerTypes.toMutableMap()
      containerTypes[containerType.id] = containerType
      sr.containerTypes = containerTypes
      persist(sr, containerTypes)
      containerType
    }

    logger.info("Create container type of scene $sr: $containerType")
    FleetEventService.fire(
      FleetEvent(
        name = "ContainerType::Create",
        sceneId = sr.sceneId,
        extra = mapOf("containerTypeId" to containerType.id),
      ),
    )

    return containerType
  }

  /**
   * 删除容器类型。
   */
  fun remove(sr: SceneRuntime, containerTypeIds: List<Int>) {
    val containerTypes = sr.containerTypes.toMutableMap()
    val containerNames = containerTypeIds.mapNotNull { containerTypes[it]?.name }
    sr.withOrderLock {
      for (id in containerTypeIds) containerTypes.remove(id)
      sr.containerTypes = containerTypes
      persist(sr, containerTypes)
    }

    logger.info("Removed container types from scene ${sr.sceneId}: $containerTypeIds")

    // 通过 id 来删除容器
    FleetEventService.fire(
      FleetEvent(
        name = "ContainerType::RemoveById",
        sceneId = sr.sceneId,
        extra = mapOf("containerTypeIds" to containerTypeIds),
      ),
    )
    // 通过 名称 来删除容器， 交管目前用的这个 误伤 ！！！
    FleetEventService.fire(
      FleetEvent(
        name = "ContainerType::RemoveByName",
        sceneId = sr.sceneId,
        extra = mapOf(
          "containerNames" to containerNames,
        ),
      ),
    )
  }

  /**
   * 修改容器类型。
   */
  fun update(sr: SceneRuntime, req: SceneContainerType) {
    sr.withOrderLock {
      val containerTypes = sr.containerTypes.toMutableMap()
      containerTypes[req.id] = req
      persist(sr, containerTypes)
    }

    logger.info("Updated container type ${req.id} of scene ${sr.sceneId}")
    FleetEventService.fire(
      FleetEvent(
        name = "ContainerType::Update",
        sceneId = sr.sceneId,
        extra = mapOf("containerTypeId" to req.id),
      ),
    )
  }

  private fun persist(sr: SceneRuntime, containerTypes: Map<Int, SceneContainerType>) {
    sr.containerTypes = containerTypes
    JsonFileHelper.writeJsonToFile(getContainerTypeFile(sr.sceneId), containerTypes.values, true)
  }

  private fun getContainerTypeFile(sceneId: String): File {
    val dir = SceneFileService.getSceneDir(sceneId)
    return File(dir, "container-types.json")
  }
}