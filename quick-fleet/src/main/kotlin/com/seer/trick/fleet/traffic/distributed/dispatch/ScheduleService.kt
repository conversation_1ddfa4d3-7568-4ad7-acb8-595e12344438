package com.seer.trick.fleet.traffic.distributed.dispatch

import com.seer.trick.fleet.traffic.distributed.context.domain.*
import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper
import com.seer.trick.fleet.traffic.distributed.lock.LockService
import com.seer.trick.fleet.traffic.distributed.lock.LockType
import com.seer.trick.fleet.traffic.distributed.lock.domain.LockRequest
import com.seer.trick.fleet.traffic.distributed.lock.graph.VeLine
import com.seer.trick.fleet.traffic.distributed.lock.graph.Vector
import com.seer.trick.fleet.traffic.distributed.lock.move.SpaceLockCalculate
import com.seer.trick.fleet.traffic.distributed.map.LineType
import com.seer.trick.fleet.traffic.distributed.map.MapService
import com.seer.trick.fleet.traffic.distributed.map.Position
import com.seer.trick.fleet.traffic.distributed.plan.model.PathAction
import com.seer.trick.fleet.traffic.distributed.service.DistributedTrafficService
import io.javalin.util.NamedThreadFactory
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.*

object ScheduleService {

  private val logger = LoggerFactory.getLogger(javaClass)

  private val robotPool = ThreadPoolExecutor(
    20,
    40,
    60L,
    TimeUnit.MILLISECONDS,
    LinkedBlockingQueue<Runnable>(1024),
    NamedThreadFactory("traffic"),
  )

  private val futures: MutableMap<String, Future<*>> = ConcurrentHashMap()

  fun schedule(robots: List<RobotContext>) {
    // 统一释放锁闭
    for (robot in robots) {
      if (checkRobotStatus(robot)) continue

      releaseTailSpaces(robot)
      if (futures.containsKey(robot.robotName)) {
        val future = futures[robot.robotName]!!
        if (!future.isDone) {
          continue
        }
      }
      val future = robotPool.submit(runAsy(robot))
      futures[robot.robotName] = future
    }
  }

  private fun runAsy(robot: RobotContext): Runnable {
    return Runnable {
      val lock = robot.lock.tryLock(100, TimeUnit.MILLISECONDS)
      if (!lock) {
        logger.error("${robot.robotName} lock failed")
        return@Runnable
      }
      try {
        ScheduleProcessor.scheduleTask(robot)
      } catch (e: Exception) {
        logger.error("schedule error $e")
      } finally {
        robot.lock.unlock()
      }
    }
  }

  private fun checkRobotStatus(robot: RobotContext): Boolean {
    // 交控状态
    if (robot.state != TrafficStatus.RUNNING) {
      // 如机器人交管状态为 停车，则进入处理停车状态
      if (robot.state == TrafficStatus.STOP) {
        processRobotStopStatus(robot)
      }
      return true
    }
    // 机器人本身状态
    return robot.baseDomain.status != RobotStatus.WORK
  }

  private fun processRobotStopStatus(context: RobotContext) {
    // 检测机器人是否在自由导航区域内
    val curPoint = context.baseDomain.curPoint
    if (curPoint != null && MapService.findXYInAvoidArea(context.sceneId, context.mapName, curPoint.x, curPoint.y)) {
      val key: String =
        LockType.ROBOT.toString() + "-><-" + context.groupName + "-><-" + context.robotName
      LockService.removeSpaceLockByKey(key, context.sceneId, context.mapName)
      context.state = TrafficStatus.IDLE
      return
    }
    // 构建停车锁闭并进行申请
    val staticSpaceLock = SpaceLockCalculate.robotStaticSpaceLock(
      context.robotName,
      context.sceneId,
      context.groupName,
      context.baseDomain.robotHeading,
      context.baseDomain.curPoint,
      context.mapName,
    )
    val handle = LockService.lockHandle(
      LockRequest(
        id = context.robotName,
        mapName = context.mapName,
        sceneId = context.sceneId,
        spaces = mutableListOf(staticSpaceLock!!),
      ),
    )
    if (handle.success) {
      context.state = TrafficStatus.IDLE
    } else {
      val blockName = if (handle.blocks.isNotEmpty()) {
        handle.blocks.first().code
      } else {
        null
      }
      DistributedTrafficService.trafficAlarm(
        sceneId = context.sceneId,
        robotName = context.robotName,
        code = "*********",
        args = listOf(context.robotName, blockName ?: ""),
        buttons = listOf("btnResetSpaceLock"),
      )
      logger.error("${context.robotName} apply space lock failed, block by $blockName")
    }
  }

  /**
   *  释放机器人路径上的尾部空间资源
   * */
  private fun releaseTailSpaces(robot: RobotContext) {
    val plan = robot.plan
    val reservePath = plan.reservePath
    val index = plan.index
    val curPoint = robot.baseDomain.curPoint
    val newReservePath: MutableList<PathAction> = LinkedList()
    for (pathAction in reservePath) {
      if (pathAction.index == index) {
        // 临时处理下 旋转 释放快问题
        if (pathAction.isRotate()) {
          if (AngleHelper.sameAngleInFiveDegree(pathAction.robotOutHeading, robot.baseDomain.robotHeading) &&
            AngleHelper.sameAngleInFiveDegree(pathAction.containerOutHeading, robot.baseDomain.containerHeading)
          ) {
            pathAction.runLock = pathAction.targetLock
          } else {
            newReservePath.add(pathAction)
          }
          continue
        }

        newReservePath.add(updateAction(curPoint, pathAction, robot))
      } else if (pathAction.index > index) {
        newReservePath.add(pathAction)
      } else {
        pathAction.clearLock()
      }
    }
    plan.reservePath = newReservePath
  }

  private fun updateAction(curPoint: Position?, pathAction: PathAction, robot: RobotContext): PathAction {
    val start = pathAction.start
    if ((pathAction.isMove() || pathAction.stop()) && curPoint != null) {
      val line = MapService.findLineByName(robot.sceneId, robot.mapName, robot.groupName, pathAction.lineName)
      val pointOnLine = if (line.type == LineType.STRAIGHT) {
        VeLine(
          Vector(pathAction.start.x, pathAction.start.y),
          Vector(pathAction.target.x, pathAction.target.y),
        ).projectPointOnLine(Vector(curPoint.x, curPoint.y))
      } else {
        var min = Double.MAX_VALUE
        var minPos = line.tracePoses[0]
        for (pos in line.tracePoses) {
          if (curPoint.getDistance(pos.x, pos.y) < min) {
            min = curPoint.getDistance(pos.x, pos.y)
            minPos = pos
          }
        }
        Vector(minPos.x, minPos.y)
      }
      val action = pathAction.copy(
        start = Position(x = pointOnLine.x, y = pointOnLine.y, pointName = start.pointName, posType = start.posType),
        robotInHeading = robot.baseDomain.robotHeading,
        containerInHeading = robot.baseDomain.containerHeading,
      )
      action.startLock = null
      action.runLock = null
      return action
    }
    return pathAction
  }
}