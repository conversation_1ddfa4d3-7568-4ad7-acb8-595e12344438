package com.seer.trick.fleet.traffic.distributed.service.model

import com.seer.trick.fleet.traffic.distributed.map.Point
import com.seer.trick.fleet.traffic.distributed.map.Site


class TaskContext(val robotName: String,// 机器人编码
                  val orderId: String,// 订单号
                  val stepId: String,// 步骤号
                  var mapName: String, // 地图编码
                  var groupName: String,// 机器人组编码
                  val start: Site,// 起始点 || 不可为空
                  val target: Point, // 目标点|| 不可为空，若后期需要，可改为坐标
                  val startAngle: Int, // 机器人当前角度
                  val status: TaskStatus // 任务状态
) {

  // 步骤类型
  var type: String? = null

  // 货架编码
  var containerName: String? = null

  // 货架角度
  var containerStartAngle: Int? = null

  // 终点货架角度
  var containerTargetAngle: Int? = null

  // 任务取消完成停靠的点
  var cancelPoint: Site? = null

  override fun toString(): String {
    return "TaskContext(robotName=$robotName, orderId=$orderId, stepId=$stepId, mapName=$mapName, groupName=${groupName}, " +
      "start=${start}, target=$target, startAngle=$startAngle, status=$status, containerName=$containerName, " +
      "containerStartAngle=$containerStartAngle, containerTargetAngle=$containerTargetAngle, cancelPoint=$cancelPoint)"
  }
}


enum class TaskStatus {
  INIT, RUNNING, CANCELED, FAILED, FINISHED;
}