package com.seer.trick.fleet.handler

import com.seer.trick.BzError
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.file.FileManager
import com.seer.trick.base.http.*
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.fleet.domain.RbkModel
import com.seer.trick.fleet.mock.*
import com.seer.trick.fleet.mock.service.MockService
import com.seer.trick.fleet.seer.SeerRbkTcpServer
import com.seer.trick.fleet.service.SceneService
import com.seer.trick.helper.JsonFileHelper
import com.seer.trick.helper.JsonHelper
import io.javalin.http.Context
import io.javalin.websocket.WsMessageContext

/**
 * 仿真相关 HTTP 和 WS 接口
 */
object MockHandler : WebSocketSubscriber() {

  fun registerHandlers() {
    WebSocketManager.subscribers += this

    val c = Handlers("api/mock")

    c.get("config", ::getMockConfig, auth()) // 获取仿真配置
    c.post("config/actions-costs", ::updateActionsCosts, auth()) // 修改动作耗时
    c.post("config/battery", ::updateBatteryConfig, auth()) // 修改电池()配置
    c.post("config/export", ::exportConfig, auth()) // 导出仿真配置
    c.post("config/import", ::importConfig, auth()) // 导入仿真配置

    c.post("pause", ::pauseOrResumeMock, auth()) // 暂停/恢复仿真总体
    c.post("reset", ::resetMock, auth()) // 一键重置仿真
    c.post("speed", ::updateMockSpeedFactor, auth()) // 设置仿真倍率

    c.get("robots/model", ::getMockRobotsModel, auth()) // 获取仿真机器人的模型文件
    c.post("robots/create", ::addRobots, auth()) // 添加多个仿真机器人
    c.post("robots/rename", ::renameRobots, auth()) // 重命名多个仿真机器人
    c.post("robots/update/configs", ::updateRobotsConfigs, auth()) // 批量更新仿真机器人配置
    c.post("robots/update/model", ::updateRobotsModel, auth()) // 批量更新仿真机器人的模型文件
    c.post("robots/update/records", ::updateRobotsRecords, auth()) // 批量更新仿真机器人状态
    c.post("robots/update/maps", ::updateRobotsMaps, auth()) // 批量更新仿真机器人地图
    c.post("robots/update/alarms", ::updateRobotsAlarms, auth()) // 批量更新仿真机器人告警
    c.post("robots/remove", ::removeRobots, auth()) // 批量删除仿真机器人
    c.post("robots/init-points", ::changeRobotInitPointsRandomly, auth()) // 随机分派机器人出生点位
    c.post("robots/set-init-points", ::setRobotInitPoint, auth()) // 根据机器人名指定机器人出生点位
    c.post("robots/snapshot/export", ::exportRobotsSnapshots, auth()) // 导出仿真机器人状态，带未完成的路径导航
    c.post("robots/snapshot/import", ::importRobotsSnapshots, auth()) // 导入仿真机器人状态，带未完成的路径导航
    c.post("robots/emc", ::setRobotEmc, auth()) // 设置机器人急停
    c.post("robots/soft-emc", ::setRobotSoftEmc, auth()) // 设置机器人软急停
    c.post("robots/create-from-scene", ::createFromScene, auth()) // 根据名字创建一组仿真机器人

    c.post("robots/reconnect", ::reconnect, auth()) // 重置机器人连接

    c.post("robot-nav-task/by-robots", ::prcRobotsNavTasksByRobots, auth()) // 按机器人，暂停/恢复/取消指定机器人路径导航任务

    c.post("server/request", ::coffeeTcpServerMockRequest, auth())

    c.post("robot/set-currentTask-failed", ::setCurrentTaskFailed, auth()) // 使当前任务失败
    c.post("robot/set-currentTask-cancelled", ::setCurrentTaskCancelled, auth()) // 使当前任务取消
  }

  override fun onMessage(ctx: WsMessageContext, msg: WsMsg, env: WsEnv) {
    when (msg.action) {
      "Mock::RealtimeFrame::Query" -> onRealtimeFrame(ctx, msg.id)
    }
  }

  private fun getMockConfig(ctx: Context) {
    ctx.json(MockService.config)
  }

  private fun updateActionsCosts(ctx: Context) {
    val req: MockActionsCosts = ctx.getReqBody()
    MockService.updateActionsCosts(req)

    ctx.status(200)
  }

  private fun updateMockSpeedFactor(ctx: Context) {
    val req: MockSpeed = ctx.getReqBody()
    MockService.updateSpeedFactor(req.speed)
  }

  private fun pauseOrResumeMock(ctx: Context) {
    val req: MockPauseReq = ctx.getReqBody()
    MockService.pauseOrResume(req.paused)

    ctx.status(200)
  }

  private fun updateBatteryConfig(ctx: Context) {
    val req: MockBattery = ctx.getReqBody()
    MockService.updateBatteryConfig(req)
  }

  private fun resetMock(ctx: Context) {
    MockService.reset()

    ctx.status(200)
  }

  private fun addRobots(ctx: Context) {
    val configs: List<MockSeerRobotConfig> = ctx.getReqBody()
    val ids = MockService.addMockRobots(configs)

    ctx.json(ids)
  }

  private fun renameRobots(ctx: Context) {
    val names: Map<String, String> = ctx.getReqBody() // id -> new name
    val ids = MockService.updateMockRobotsNames(names)

    ctx.json(ids)
  }

  private fun updateRobotsConfigs(ctx: Context) {
    val req: UpdateRobotsReq<EntityValue> = ctx.getReqBody()

    MockService.updateMockRobotsConfigs(req.ids, req.update)

    ctx.status(200)
  }

  private fun getMockRobotsModel(ctx: Context) {
    val req: UpdateRobotsReq<Any> = ctx.getReqBody()
    ctx.json(MockService.getMockRobotsModel(req.ids))
  }

  private fun updateRobotsModel(ctx: Context) {
    val req: UpdateRobotsReq<RbkModel> = ctx.getReqBody()

    MockService.updateMockRobotsModel(req.ids, req.update)

    ctx.status(200)
  }

  private fun updateRobotsRecords(ctx: Context) {
    val req: UpdateRobotsReq<EntityValue> = ctx.getReqBody()

    MockService.updateMockRobotsRecords(req.ids, req.update)

    ctx.status(200)
  }

  private fun updateRobotsMaps(ctx: Context) {
    val req: UpdateRobotsReq<List<MockSeerRobotMap>> = ctx.getReqBody()

    MockService.updateMockRobotsMaps(req.ids, req.update)

    ctx.status(200)
  }

  private fun updateRobotsAlarms(ctx: Context) {
    val req: UpdateRobotsReq<List<MockSeerRobotAlarm>> = ctx.getReqBody()

    MockService.updateMockRobotsAlarms(req.ids, req.update)

    ctx.status(200)
  }

  private fun removeRobots(ctx: Context) {
    val req: RobotsIdsReq = ctx.getReqBody()

    MockService.removeMockRobots(req.ids)

    ctx.status(200)
  }

  private fun changeRobotInitPointsRandomly(ctx: Context) {
    val req: RobotsIdsReq = ctx.getReqBody()

    MockService.changeRobotInitPointsRandomly(req.ids)

    ctx.status(200)
  }

  private fun setRobotInitPoint(ctx: Context) {
    val req: SetRobotInitPointReq = ctx.getReqBody()

    MockService.setRobotInitPoint(req.robotName, req.pointName, req.x, req.y, req.angle)
    ctx.status(200)
  }

  // 仿真机器人重连
  private fun reconnect(ctx: Context) {
    val req: RobotsIdsReq = ctx.getReqBody()

    MockService.reconnect(req.ids)

    ctx.status(200)
  }

  private fun prcRobotsNavTasksByRobots(ctx: Context) {
    val req: RobotNavTaskActionReq = ctx.getReqBody()

    MockService.prcRobotsNavTasksByRobots(req.ids, req.action)

    ctx.status(200)
  }

  private fun exportConfig(ctx: Context) {
    val file = FileManager.nextTmpFile("json", prefix = "mock-config-")
    JsonFileHelper.writeJsonToFile(file, MockService.config, true)
    val path = FileManager.fileToPath(file)

    ctx.json(ExportResult(path, file.name))
  }

  private fun importConfig(ctx: Context) {
    val req: ImportReq = ctx.getReqBody()

    val file = FileManager.pathToFile(req.path)
    val config: MockConfig = JsonFileHelper.readJsonFromFile(file) ?: throw BzError("errParseJsonFile")

    MockService.updateConfig(config)

    file.delete()

    ctx.status(200)
  }

  private fun exportRobotsSnapshots(ctx: Context) {
    val file = FileManager.nextTmpFile("json", prefix = "mock-robots-")
    JsonFileHelper.writeJsonToFile(file, MockService.getMockRobotsSnapshots(), true)
    val path = FileManager.fileToPath(file)

    ctx.json(ExportResult(path, file.name))
  }

  private fun importRobotsSnapshots(ctx: Context) {
    val req: ImportReq = ctx.getReqBody()

    val file = FileManager.pathToFile(req.path)
    val ss: List<MockSeerRobotsSnapshot> = JsonFileHelper.readJsonFromFile(file) ?: throw BzError("errParseJsonFile")

    MockService.restoreRobotsSnapshots(ss)

    file.delete()

    ctx.status(200)
  }

  private fun onRealtimeFrame(ctx: WsMessageContext, id: String) {
    ctx.send(WsMsg.json("Mock::RealtimeFrame::Reply", MockService.getRealtimeFrame(), replyToId = id))
  }

  private fun coffeeTcpServerMockRequest(ctx: Context) {
    val req: CoffeeTcpServerMockRequest = ctx.getReqBody()
    val r = SeerRbkTcpServer.request(req.robotName, req.apiNo, JsonHelper.writeValueAsString(req.req))
    ctx.result(r)
  }

  private fun setCurrentTaskFailed(ctx: Context) {
    val req: RobotsIdsReq = ctx.getReqBody()
    MockService.setCurrentTaskFailed(req.ids)
    ctx.status(200)
  }

  private fun setCurrentTaskCancelled(ctx: Context) {
    val req: RobotsIdsReq = ctx.getReqBody()
    MockService.setCurrentTaskCancelled(req.ids)
    ctx.status(200)
  }

  private fun setRobotEmc(ctx: Context) {
    val req: RobotEmcReq = ctx.getReqBody()

    MockService.setEmc(req.ids, req.emc)

    ctx.status(200)
  }

  private fun setRobotSoftEmc(ctx: Context) {
    val req: RobotSoftEmcReq = ctx.getReqBody()

    MockService.setSoftEmc(req.ids, req.softEmc)

    ctx.status(200)
  }

  private fun createFromScene(ctx: Context) {
    val req: CreateFromSceneReq = ctx.getReqBody()

    val sr = SceneService.mustGetSceneById(req.sceneId)
    MockService.initSceneMockRobots(sr, req.robotNames)
  }

  data class CreateFromSceneReq(val sceneId: String, val robotNames: List<String>)
}

data class MockSpeed(val speed: Double)

data class MockPauseReq(val paused: Boolean = false)

data class RobotsIdsReq(val ids: List<String>)

data class SetRobotInitPointReq(
  val robotName: String,
  val pointName: String?,
  val x: Double?,
  val y: Double?,
  val angle: Double?,
)

data class UpdateRobotsReq<UT>(val ids: List<String>, val update: UT)

data class ImportReq(val path: String)

data class ExportResult(val path: String, val filename: String)

data class RobotNavTaskActionReq(val ids: List<String>, val action: RobotNavTaskAction)

data class CoffeeTcpServerMockRequest(val robotName: String, val apiNo: Int, val req: Any)

data class RobotEmcReq(val ids: List<String>, val emc: Boolean)

data class RobotSoftEmcReq(val ids: List<String>, val softEmc: Boolean)

data class RobotManualReq(val ids: List<String>, val manual: Boolean)