package com.seer.trick.fleet.domain

import com.seer.trick.fleet.device.door.SceneDoor
import com.seer.trick.fleet.device.lift.SceneLift
import com.seer.trick.fleet.service.MapService.NOT_ACHIEVABLE_COST
import java.util.Date

/**
 * 场景主状态
 */
enum class SceneStatus {
  Disabled, // 停用
  Initializing, // 初始化中
  Initialized, // 已初始化
  Disposed, // 已销毁
}

/**
 * 场景基本信息。
 */
data class SceneBasic(
  val id: String, // 唯一 ID
  val name: String = "", // 人可见的名字
  val disabled: Boolean = false,
  val version: Long = 0,
  val displayOrder: Int = 0, // 显示顺序
  val lastModifiedOn: Date = Date(),
  // val ext: Map<String, Any?>? = null, // 扩展，用于记录我们不用，但额外记录的，比如从其他系统导入的场景
)

/**
 * 场景的主要信息。
 */
data class SceneDigest(
  val id: String, // 唯一 ID
  val name: String, // 人可见的名字
  val disabled: Boolean = false,
  val version: Long = 0,
  val displayOrder: Int = 0, // 显示顺序
  val lastModifiedOn: Date = Date(),
)

/**
 * 表示场景中一个机器人组。
 */
data class RobotGroup(
  val id: Int = 0,
  val name: String = "",
  val disabled: Boolean = false, // 停用
  val displayOrder: Int = 0, // 显示顺序，给界面用
  val modelType: String? = null, // 机器人型号 jack 顶升，trans-fork 搬运叉车，height-fork 堆高叉车，picking 料箱
  val icon: String? = null, // 图标
  val remark: String = "", // 备注
  val salverNotRotate: Boolean = false, // 机器人组的托盘是否支持旋转
  val motionModel: MotionModel = MotionModel.Differential, // 运动模型
  val motionDirection: MotionDirection = MotionDirection.Unidirectional, // 运动类型
  val containerOversize: Boolean = false, // 容器尺寸超过机器人底盘
  val collisionModel: RobotCollisionModel = RobotCollisionModel(), // 机器人组的碰撞模型
  val robotModel: RobotModelRecord? = null, // 机器人模型
  //
  val safeDistHead: Double = 0.0, // 安全距离（前）
  val safeDistTail: Double = 0.0, // 安全距离（后）
  val safeDistLeft: Double = 0.0, // 安全距离（左）
  val safeDistRight: Double = 0.0, // 安全距离（右）
  //
  val maxSpeed: Double? = null, // 最大速度（空载）
  val maxBackSpeed: Double? = null, // 最大后退速度（空载）
  val maxRotSpeed: Double? = null, // 最大旋转速度（空载）
  val loadedMaxSpeed: Double? = null, // 最大速度（载货后）
  val loadedMaxBackSpeed: Double? = null, // 最大后退速度（载货后）
  val loadedMaxRotSpeed: Double? = null, // 最大旋转速度（载货后）
)

/**
 * 运动模型。默认差速。
 */
enum class MotionModel {
  Differential, // 差速
  Steer, // 单舵轮
  Omni, // 麦轮
}

/**
 * 运动方向。默认双向。
 */
enum class MotionDirection {
  Unidirectional, // 单向车
  Bidirectional, // 双向车
  Omnidirectional, // 全向车
}

/**
 * 机器人标签。考虑到未来可能要给标签属性，建模为一个类。
 */
data class RobotTag(
  val name: String = "", // 名字是标识
)

/**
 * 表示组内一个机器人的配置。
 */
data class SceneRobot(
  val robotName: String = "", // 机器人名称
  val disabled: Boolean = false, // 停用
  val remark: String = "", // 备注
  val groupId: Int = 0, // 所属组 ID
  val tags: List<String> = emptyList(), // 标签
  val vendor: RobotVendor = RobotVendor.Seer,
  val selfBinNum: Int = 1, // 机器人库位数；一次能载几个货
  val image: Any? = null, // 图片
  val connectionType: RobotConnectionType? = null, // 机器人和调度的连接方式
  val robotIp: String? = null, // 机器人 IP
  val robotPortStart: Int? = null, // 机器人起始端口
  val ssl: Boolean? = null, // 海柔：连接使用 SSL 加密
  val gwAuthId: String = "", // gw 网关用户ID
  val gwAuthSecret: String = "", // gw 网关登录秘钥
  val simulated: Boolean = false, // 是否为仿真
  val plusOne: Boolean = false, // 是否支持 N + 1
)

/**
 * 厂商
 */
enum class RobotVendor {
  Seer,
  Hai,
  Hik,
}

/**
 * 机器人和调度的连接方式
 */
enum class RobotConnectionType {
  FleetToRobot,
  RobotToFleet,
  GwWs, // GW 上报
}

/**
 * 表示场景中一个区域。
 */
data class SceneArea(
  val id: Int = 0, // 唯一 ID，在整个场景内唯一。主要用于编辑，业务不用。
  val name: String = "", // 区域名称，接口和界面上显示名称。
  val disabled: Boolean = false, // 停用
  val displayOrder: Int = 0, // 显示顺序，给界面用
  val remark: String = "", // 备注
  val mergedMap: SceneAreaMap = SceneAreaMap(), // 合并区域中多个机器人组的地图后的地图
  val gmMap: Map<Int, SceneAreaMap> = emptyMap(), // 每个机器人组对应的地图
  val groupsMap: Map<Int, RobotAreaMapRecord> = emptyMap(), // 每个机器人组对应的地图文件记录
  val ui: UiConfig? = null, // UI 配置，包含区域的文本、图片
)

/**
 * 一个区域的地图。
 */
data class SceneAreaMap(
  val bound: Rect = Rect(), // 区域界限，能容纳这个区域上所有元素的矩形
  val points: List<MapPoint> = emptyList(), // 点位
  val paths: List<MapPath> = emptyList(), // 路径
  val zones: List<MapZone> = emptyList(), // 区块
  // val binPoints: List<SceneBinPoint> = emptyList(), // 库位点
  val bins: List<SceneBin> = emptyList(), // 库位
  val restrictedLines: List<MapRestrictedLine> = emptyList(), // 禁行线
  //
  val sourceMaps: List<String>? = null, // 如果此地图从某个源地图生成，则记录源地图的名称
  val envPointCloud: EnvPointCloud? = null, // 环境点云
  val svgMapFile: String? = null,
)

/**
 * 场景中的点位。
 */
data class MapPoint(
  val id: Long = 0, // 唯一 ID，在整个场景内唯一。主要用于编辑，业务不用。
  val name: String = "", // 点位名称，接口和界面上显示名称。
  val label: String? = null, // 点位别名，现在主要用于对应 smap 里点位 label
  val type: String = "", // 类型，如 LocationMark
  val x: Double = 0.0,
  val y: Double = 0.0,
  val direction: Double? = null,
  val disabled: Boolean = false, // 停用
  val remark: String = "", // 备注
  val parkAllowed: Boolean = false, // 允许停靠 TODO 运行时设置？
  val chargeAllowed: Boolean = false, // 允许充电 TODO 运行时设置？
  val giveWayNotAllowed: Boolean = false, // 不允许避让
  val rotateNotAllowed: Boolean = false, // 不允许旋转
  val spin: Boolean = false, // 对应 smap 随动 spin
  val containerRotateAllowed: Boolean = false, // 换向点：允许货架旋转
  val linkedPathAsJointResource: Boolean = false, // 资源申请时，相连路径要一起申请
  val unloadContainerDir: Double? = null, // 放料架时的料架方向
  val m4labelsStr: String? = null, // 给界面用，将被记录到 smap 的点位的 m4define 属性中，最终会处理成 m4labels，不支持根据机器人分组设置。
  val m4labels: Set<String> = emptySet(), // 在元素的标签的集合，解析 smap 时，再将 m4labelsStr 转换成 m4labels，随机发单会用到。
)

/**
 * 场景中的路径。
 */
data class MapPath(
  val id: Long = 0, // 唯一 ID，在整个场景内唯一。主要用于编辑，业务不用。
  val fromPointId: Long = 0, // 起点
  val fromPointName: String = "",
  val toPointId: Long = 0, // 终点
  val toPointName: String = "",
  val disabled: Boolean = false, // 停用
  val moveDirection: MoveDirection = MoveDirection.Dual, // 正走倒走限制，null 表示不限制
  val remark: String = "", // 备注
  val curveType: PathCurveType = PathCurveType.Other, // 曲线类型
  val degree: Long = 0, // 形状参数：度数。控制点中具有全局影响的点的数量。增加度数会增加曲线连续性。
  val knots: List<Double> = emptyList(), // 形状参数：节数组。个数等于控制点数 + 度数 + 1。
  val controls: List<NurbsControlPoint> = emptyList(), // 形状参数： 控制点数组：包含起点和终点。每个控制点由 x, y 和权重组成。
  val actualLength: Double = Double.MAX_VALUE, // 路径实际长度，默认无限大 —— 算不出来就不用用这条路
  val costFactor: Double = 1.0, // 成本因子，乘以 actualLength
  val middlePoint: CurvePoint2D, // 路径的中点
  val bezierPaths: List<BezierPath>? = null, // 贝塞尔曲线列表
  val tracePoints: List<CurvePoint2D> = emptyList(), // 轨迹点，路径上的 101 个点（包括首尾）。注意中点不一定在这个列表里。
  val rotateNotAllowed: Boolean = false, // 不允许旋转
  val giveWayNotAllowed: Boolean = false, // 解死锁不可通过
  val containerDir: Double? = null, // 进入路径时容器方向要求
  val containerShortSideAhead: Boolean = false, // 容器短边必须朝前
  val loadPass: PathLoadPass = PathLoadPass.NoLimit, // 载货通行限制
  val m4labelsStr: String? = null, // 给界面用，将被记录到 smap 的点位的 m4define 属性中，最终会处理成 m4labels，不支持根据机器人分组设置。
  val m4labels: Set<String> = emptySet(), // 在元素的标签的集合，解析 smap 时，再将 m4labelsStr 转换成 m4labels，随机发单会用到。
  val reversed: Boolean = false, // 是否需要反转路径，只在编辑地图时使用
) {

  val key = getKey(fromPointName, toPointName)

  companion object {

    /**
     * 路径的唯一键
     */
    fun getKey(fromPointName: String, toPointName: String) = "$fromPointName->$toPointName"
  }
}

data class BezierPath(
  val degree: Long = 0, // 形状参数：度数。控制点中具有全局影响的点的数量。增加度数会增加曲线连续性。
  val controls: List<NurbsControlPoint> = emptyList(), // 形状参数： 控制点数组：包含起点和终点。每个控制点由 x, y 和权重组成。
)

/**
 * 路径曲线类型
 */
enum class PathCurveType {
  Straight, // 直线
  Bezier3, // 三阶贝塞尔
  NURBS6, // NURBS 6 个控制点
  DegenerateBezier, // 高级三阶贝塞尔
  Other, // 其他，目前应该是只有圆弧
}

/**
 * 机器人在路径上的移动方向限制
 */
enum class MoveDirection {
  Forward, // 正走
  Backward, // 倒走
  Dual, // 都可以
}

/**
 * 路径载货通行限制
 */
enum class PathLoadPass {
  NoLimit, // 不限制
  NoLoadOnly, // 仅空载通行
  LoadOnly, // 仅载货通过
}

/**
 * 场景中的区块。
 */
data class MapZone(
  val id: Long = 0, // 唯一 ID，在整个场景内唯一。主要用于编辑，业务不用。
  val name: String = "", // 区块名称。一般自动生成，也可以手工指定。接口和界面上显示名称。
  val disabled: Boolean = false, // 停用
  val type: String? = null, // 类型
  val remark: String = "", // 备注
  // val shape: ZoneShape = ZoneShape.Rect, // 区块形状
  // val rect: Rect = Rect(), // 用于描述矩形区块
  val polygon: List<Point2D>, // 用于描述多边形区块
  val direction: Double? = -1.57, // smap 的区域一定是由朝向的。 对应 smap 的区域的 dir 属性，默认值是 -1.57 rad。
  val fontSize: Int? = null, // 字体大小
  val borderColor: String? = null, // 区块边框颜色
  val fillColor: String? = null, // 区块填充的颜色
)

/**
 * 区块形状
 */
enum class ZoneShape {
  Rect, // 矩形
  Polygon, // 多边形
}

/**
 * 禁行线。一根直线，机器人禁止穿越、旋转碰撞。
 */
data class MapRestrictedLine(
  val id: Long = 0, // 唯一 ID，在整个场景内唯一。主要用于编辑，业务不用。
  val disabled: Boolean = false, // 停用
  val remark: String = "", // 备注
  val p1: Point2D, // 直线点 1
  val p2: Point2D, // 直线点 2
)

// /**
//  * 库位点。放置货物容器的位置（和范围）。
//  */
// data class SceneBinPoint(
//   val id: Long = 0, // 唯一 ID，在整个场景内唯一。主要用于编辑，业务不用。
//   val disabled: Boolean = false, // 停用
//   val x: Double = 0.0,
//   val y: Double = 0.0,
//   val layers: Int = 1, // 层数
//   val polygon: List<Point2D>, // 描述货物放置的范围
// )

/**
 * 场景里的一个库位。
 */
data class SceneBin(
  val id: Long = 0, // 唯一 ID，在整个场景内唯一。主要用于编辑，业务不用。
  val name: String = "", // 库位名称
  val x: Double = 0.0, // 库位坐标 TODO 怎么定义库位坐标，中心点？
  val y: Double = 0.0,
  val layerNo: Int = 1, // 层号，从 1 开始
  val disabled: Boolean = false, // 停用
  // val binPoint: Long = 0, // 库位所在点
  val workPointId: Long? = null, // 取放货关联点位。core 存在未绑定的库位
  val workPointName: String? = null, // 关联点位
  val remark: String = "", // 备注
  val unloadContainerDir: Double? = null, // 放料架时的料架方向
  val binTaskMap: Map<String, String>? = null, // binTask 的键 -> 序列化后的 binTask 的值。
  val m4labelsStr: String? = null, // 给界面用，将被记录到 smap 的点位的 m4define 属性中，最终会处理成 m4labels，不支持根据机器人分组设置。
  val m4labels: Set<String> = emptySet(), // 在元素的标签的集合，解析 smap 时，再将 m4labelsStr 转换成 m4labels，随机发单会用到。
)

/**
 * 一个区域的环境点云
 */
data class EnvPointCloud(
  val imagePath: String = "", // 图片路径，用于显示。
  // val pointsFilePath: String = "", // 点云文件路径，用于计算
  val width: Double = 0.0, // 现场宽度，单位米
  val height: Double = 0.0,
  val imageWidth: Int = 0, // 图片宽度，单位像素
  val imageHeight: Int = 0,
  val scale: Double = 0.0, // 每米用图片多少像素表示
  val imageOriginX: Int = 0, // 原点在图片中的位置，单位像素
  val imageOriginY: Int = 0,
)

/**
 * UI 配置
 */
data class UiConfig(val texts: List<Text>? = null, val images: List<Picture>? = null)

/**
 * 一个区域的文本
 */
data class Text(
  val id: String = "",
  val color: String = "",
  val fontSize: Int = 0,
  val fontWeight: String = "",
  val lineHeight: Int = 0,
  val shadowBlur: Int = 0,
  val shadowColor: String = "",
  val shadowVisible: Boolean = false,
  val shadowXOffset: Int = 0,
  val shadowYOffset: Int = 0,
  val specialPosition: String = "",
  val text: String = "",
  val textAlign: String = "",
  val x: Double = 0.0,
  val y: Double = 0.0,
  val stackOnMap: String = "",
  val remark: String = "",
)

/**
 * 一个区域的图片
 */
data class Picture(
  val id: String = "",
  val src: String = "", // 图片路径
  val name: String = "",
  val width: Double = 0.0, // 现场宽度，单位米
  val height: Double = 0.0,
  val opacity: Int = 0, // 透明度
  val x: Double = 0.0,
  val y: Double = 0.0,
  val stackOnMap: String = "",
  val remark: String = "",
)

/**
 * 表示机器人的一个区域的一张地图。
 */
data class RobotAreaMapRecord(
  val mapName: String = "", // 用户、接口使用的地图名称。
  val mapFile: String = "", // 地图在磁盘存放文件路径。
  val mapMd5: String = "", // MD5
  val remark: String = "", // 自定义备注
)

data class RobotMapNameMd5(
  val mapName: String = "", // 用户、接口使用的地图名称。
  val mapMd5: String = "", // MD5
  val current: Boolean = false, // 是否是机器人当前使用的地图
)

/**
 * 定制元素属性。如果 robots 有值，取之；否则取 groups 的值；否则取 defaultValue。
 */
data class OverrideProp<T : Any>(
  val defaultValue: T,
  val groups: Map<Int, T>, // 机器人组的定制
  val robots: Map<String, T>, // 机器人定制
)

/**
 * 定制点位属性
 */
data class OverrideMapPoint(
  val disabled: OverrideProp<Boolean>?,
  val parkAllowed: OverrideProp<Boolean>?,
  val chargeAllowed: OverrideProp<Boolean>?,
)

/**
 * 一个容器类型
 * 容器坐标系原点在容器中心
 */
data class SceneContainerType(
  val id: Int = 0, // 唯一 ID，在整个场景内唯一。主要用于编辑，业务不用。
  val name: String = "", // 类型名称
  val disabled: Boolean = false, // 停用
  val displayOrder: Int = 0, // 显示顺序，给界面用
  val remark: String = "", // 备注
  val imagePath: String? = null, // 图片路径，用于显示
  val height: Double = 0.0, // 货架高度（1 层的高度）
  val radius: Double = 0.0, // 旋转半径
  val legHeight: Double = 0.0, // 货架腿高度（0 层的高度）
  val groupCenterDistances: Map<Int, Double> = emptyMap(), // 货架中心点与机器人组模型的中心点的距离
  val outerWidth: Double = 0.0, // 容器外宽度（y 方向边的长度）
  val outerLength: Double = 0.0, // 容器外长度（x 方向边的长度）
  val width: Double = 0.0, // 腿外宽度
  val length: Double = 0.0, // 腿外长度
  val legWidth: Double = 0.0, // 腿形状宽度
  val legLength: Double = 0.0, // 腿形状长度
  // 废弃
  val polygon: Polygon = Polygon(), // 外轮廓多边形，必填
  val legs: List<Circle> = emptyList(), // 货架腿
)

/**
 * 表示一个最短路
 */
data class ShortestPath(
  val found: Boolean = false,
  val points: List<String> = emptyList(),
  val reason: String = "",
  val weight: Double = NOT_ACHIEVABLE_COST,
)

/**
 * 场景元素的 id 计数器
 */
class IdsCounter {

  var areaId = 0L
  var pointId = 0L
  var pathId = 0L
  var zoneId = 0L
  var binId = 0L
  var restrictedLineId = 0L
}

/**
 * RBK 机器人模型文件
 */
data class RobotModelRecord(
  val path: String = "", // 存放路径
  val md5: String = "", // 文件 MD5
  val remark: String = "", // 自定义备注
)

enum class DispatchMethod {
  KM,
  Greedy,
}

enum class TrafficMethod {
  Distributed,
  Venus,
}

/**
 * 场景的配置
 * 注意，配置目前只能有一层，不能嵌套。chargingConfig 当成一个整体。
 */
data class SceneConfig(
  //
  // ----- 简单配置：立即生效
  //
  val robotStateFetchDelay: Long = 500, // 机器人状态获取间隔，单位毫秒
  val robotStateFailureNumToAutoConnect: Int = 10, // 连续几次读取失败，自动重连。读取一个机器人的状态，如果连续失败次数超过这个值，将断开当前连接，重新连接。
  val robotStateErrorToAutoReconnectMax: Int = 5, // 自动重连最多几次。连续读取失败出发自动重连，如果连续重连 N 次都失败，将放弃自动重连，界面将提示人工介入，手工重连。
  val noOpLog: Boolean = false, // 关闭运行记录。系统默认将记录机器人的运行数据，以便回放。
  val opLogKeepDays: Int = 5, // 运行记录保留天数。超过后自动清理更早的运行记录。
  val noRbkMsgLog: Boolean = false, // 不记录与机器人的报文。系统默认将记录与机器人的报文，以便调试。
  // 派单
  val dispatchMethod: DispatchMethod = DispatchMethod.Greedy, // 派单策略，不需要复杂处理
  val withdrawnMinCost: Double? = null, // 重新分派运单成本差异阈值
  val robotPlanPausedOnSelfReportErrorOrFatal: Boolean = true, // 机器人上报 Error 或 Fatal 错误时，暂停机器人派单。
  val notCancelRobot: Boolean = false, // 取消正在执行的运单步骤时，不向机器人发取消
  val parkingCollisionCheckMode: CollisionCheckMode = CollisionCheckMode.None, // 停靠充电碰撞检测模式
  val parkingPaused: Boolean = false, // 暂停停靠
  val noReallocation: Boolean = false, // 不重新分配运单
  val dispatchSort: List<String> = emptyList(), // 派单排序
  val dispatchAcceptableTimeout: Long = 10 * 60, // 最大允许的派单等待时机，超过后将优先处理，单位秒
  // 充电
  val chargingPaused: Boolean = false, // 暂停充电
  val chargingConfig: SceneChargingConfig = SceneChargingConfig(), // 充电配置
  // 临时光通讯
  val cpnEnabled: Boolean = false, // 是否启用光通讯，目前这个是立即生效的！
  val cpnDispatchPeriod: Long? = null, // 光通讯派单周期
  // 交管公共
  val trafficPlanPaused: Boolean = false, // 暂停交管规划
  val trafficPlanPausedOnFailedPlan: Boolean = false, // 规划失败暂停继续规划
  val closePointDistance: Double = 0.2, // 判断机器人在点位上，允许的最大距离。单位米。判断机器人是否在一个点位上，计算机器人和点位的距离，如果在此距离内，认为机器人在点位上。
  val closePathDistance: Double = 0.2, // 判断机器人在路径上，允许的最大距离。单位米。判断机器人是否在一条路径上，机器人机器人和路径的距离，如果在此距离内，认为机器人在路径上。
  val trafficPlanIntervalMs: Long = 5_000, // Venus 交通规划重规划间隔（毫秒）。默认 5 秒。
  val trafficDevConfigStr: String? = null, // 交管高级配置
  // Venus 的配置
  val cbsHighW: Double = 1.2,
  val cbsLowW: Double = 1.2,
  // Distributed 的配置
  val linkMinDistance: Double = 1.0, // 死锁环推最小距离
  val linkMaxDistance: Double = 200.0, // 死锁环推最大距离
  val reverseDeadlock: Boolean = true, // 倒车解死锁
  val enablePrevent: Boolean = true, // 是否开启死锁预防
  val preRotate: Boolean = false, // 前置圆同时申请
  //
  // ----- 复杂配置：生效需要处理
  //
  val trafficMethod: TrafficMethod = TrafficMethod.Distributed, // 交管策略
  // 其他
  val selectConfig: String? = null, // 批量选择元素的配置
) {

  /**
   * 撤单最小成本差异
   */
  fun decideWithdrawnMinCost(): Double {
    var cost = withdrawnMinCost ?: 3.0
    if (cost < 0.0) cost = 3.0
    return cost
  }

  /**
   * 获取派单排序：先比较优先级（优先级大的先处理）、再比较成本（成本小的先处理）、最后比较创建时间（时间早的先处理）。
   */
  fun decideDispatchSort(): List<String> {
    var s = dispatchSort
    if (s.isEmpty()) s = listOf("Priority", "Cost", "CreatedOn")
    return s
  }
}

enum class CollisionCheckMode {
  None, // 无（默认）
  TargetPoint, // 仅考虑最终位置
  TargetPath, // 考虑最终位置和进入路径
}

/**
 * 场景的充电配置
 */
data class SceneChargingConfig(
  val defaultConfig: BasicChargingConfig = BasicChargingConfig(),
  val groupedConfigs: Map<Int, BasicChargingConfig> = emptyMap(), // 分机器人组的配置
)

/**
 * 基础充电配置
 */
data class BasicChargingConfig(
  // 强充电量。低于 chargeOnly 后，机器人不接普通运单，直接去充电，至少充到 chargeOk 后，再解除强充状态。
  @JvmField
  val chargeOnly: Double = 0.1,
  // 可充电量。低于 chargeNeed 后，机器人会生成充电任务，充电时可接普通运单
  @JvmField
  val chargeNeed: Double = 0.3,
  // OK 电量。机器人低电量强充，至少充到此电量后才能接单；非强充，充到此电量前也能接单。大于此电量后，机器人可接普通运单，执行完后去停靠点，不去充电点。
  @JvmField
  val chargeOk: Double = 0.6,
  // 满电量。高于 chargeFull 后，机器人生成停靠运单离开充电点
  @JvmField
  val chargeFull: Double = 0.9,
  // 最小充电时间，单位：秒
  @JvmField
  val minChargingTime: Int = 10,
  // 生成充电任务前的空闲时间，单位：秒
  @JvmField
  val idleTime: Int = 5, // TODO 目前没用
)

/**
 * 场景结构 = 机器人组、机器人、区域
 */
data class SceneStructure(
  val robotGroups: List<RobotGroup> = emptyList(), // 机器人组
  val robotTags: List<RobotTag> = emptyList(), // 机器人标签
  val robots: List<SceneRobot> = emptyList(), // 组内机器人
  val areas: List<SceneArea> = emptyList(), // 地图区域
  val containerTypes: List<SceneContainerType> = emptyList(), // 容器类型
  val doors: List<SceneDoor> = emptyList(), // 门的配置
  val lifts: List<SceneLift> = emptyList(), // 电梯的配置
)

/**
 * 聚合场景信息
 */
data class SceneSchema(val id: String, val basic: SceneBasic, val config: SceneConfig, val structure: SceneStructure)