package com.seer.trick.fleet.traffic.distributed.helper

import com.seer.trick.fleet.traffic.distributed.map.PosType
import com.seer.trick.fleet.traffic.distributed.map.Position
import com.seer.trick.fleet.traffic.distributed.map.Site
import kotlin.math.sqrt

/**
 *  三阶贝塞尔曲线计算
 * */
object BezierCurveHelper {

  // 计算贝塞尔曲线上的一个点
  private fun calculatePoint(
    t: Double,
    start: Position,
    c1: Site,
    c2: Site,
    end: Position,
    lineName: String,
  ): Position {
    val u = 1 - t
    val tt = t * t
    val uu = u * u
    val uuu = uu * u
    val ttt = tt * t
    var x: Double = uuu * start.x // 初始点的影响
    x += 3 * uu * t * c1.x // 控制点1的影响
    x += 3 * u * tt * c2.x // 控制点2的影响
    x += ttt * end.x // 结束点的影响
    var y: Double = uuu * start.y
    y += 3 * uu * t * c1.y
    y += 3 * u * tt * c2.y
    y += ttt * end.y
    return Position(x = x, y = y, pointName = lineName, posType = PosType.LINE)
  }

  // 使用直线近似法计算贝塞尔曲线的长度
  fun calculateLength(segment: Int, start: Position, c1: Site, c2: Site, end: Position, lineName: String): Double {
    var length = 0.0
    val deltaT = 1.0 / segment
    var p1 = start
    for (i in 1 until segment - 1) {
      val t = i * deltaT
      val p2 = calculatePoint(t, start, c1, c2, end, lineName)

      // 使用勾股定理计算两点之间的距离
      val dx = p2.x - p1.x
      val dy = p2.y - p1.y
      p1 = p2
      length += sqrt(dx * dx + dy * dy)
    }
    return length
  }

  // 均匀采样贝塞尔曲线
  fun sampleUniformly(
    segment: Int,
    start: Position,
    c1: Site,
    c2: Site,
    end: Position,
    lineName: String,
  ): MutableList<Position> {
    var sites: MutableList<Position> = mutableListOf()
    sites.add(start)
    val deltaT = 1.0 / segment
    for (i in 1 until segment - 1) {
      val t = i * deltaT
      val p2 = calculatePoint(t, start, c1, c2, end, lineName)
      sites.add(p2)
    }
    sites.add(end)
    return sites
  }
}