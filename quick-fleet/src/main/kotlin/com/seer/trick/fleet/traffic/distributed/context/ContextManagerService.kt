package com.seer.trick.fleet.traffic.distributed.context

import com.seer.trick.fleet.domain.MotionDirection
import com.seer.trick.fleet.domain.MotionModel
import com.seer.trick.fleet.domain.PositionType
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.traffic.distributed.context.domain.*
import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper
import com.seer.trick.fleet.traffic.distributed.helper.LogParseHelper
import com.seer.trick.fleet.traffic.distributed.lock.base.StaticRobotSpaceLock
import com.seer.trick.fleet.traffic.distributed.lock.graph.BoundingBox
import com.seer.trick.fleet.traffic.distributed.lock.graph.VeLine
import com.seer.trick.fleet.traffic.distributed.lock.graph.Vector
import com.seer.trick.fleet.traffic.distributed.map.*
import com.seer.trick.fleet.traffic.distributed.plan.PlanResponse
import com.seer.trick.fleet.traffic.distributed.plan.RequestInfo
import com.seer.trick.fleet.traffic.distributed.plan.model.PathAction
import com.seer.trick.fleet.traffic.distributed.service.DistributedTrafficService
import com.seer.trick.fleet.traffic.distributed.service.model.RobotStatusReport
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.*
import kotlin.math.abs

/**
 * 机器人上下文管理服务
 * */
object ContextManagerService {

  private val logger = LoggerFactory.getLogger(javaClass)

  private val robotContexts: MutableMap<String, RobotContext> = ConcurrentHashMap()

  private const val OFFSET_RANGE = 0.005

  // 更新机器人的上下文信息
  fun updateRobotContext(request: RequestInfo): RobotContext? {
    val robotName = request.robotName
    val context = queryRobotContext(robotName)
    // 获取当前机器人基本信息 or todo 抛出异常
    val robotData = DistributedTrafficService.queryRobotInfo(context) ?: return context
    if (!context.lock.tryLock(100, TimeUnit.MILLISECONDS)) {
      logger.error("$robotName can not get lock for robot context ")
      return null
    }
    try {
      updateRobotContext(robotData, context)
      buildRequest(request, context)
    } finally {
      context.lock.unlock()
    }
    // 机器人起点是否在点上
    if (request.startType == PositionType.Point && request.start != request.target) {
      val point = MapService.findPointByName(context.sceneId, request.mapName, request.groupName, request.start)
      if (point.getDistance(request.startX, request.startY) > OFFSET_RANGE * 20) {
        // 判断在哪条路线上
        val priorityLine: PriorityQueue<Pair<Line, Double>> = PriorityQueue { l1, l2 ->
          l1.second.compareTo(l2.second)
        }
        point.toLines.forEach {
          if (it.type == LineType.STRAIGHT) {
            priorityLine.offer(
              Pair(
                it,
                VeLine(Vector(it.start.x, it.start.y), Vector(it.end.x, it.end.y))
                  .vectorProjectToLine(Vector(request.startX, request.startY)),
              ),
            )
          } else {
            val closePose = it.getClosePose(request.startX, request.startY)
            priorityLine.offer(Pair(it, closePose.getDistance(request.startX, request.startY)))
          }
        }
        point.fromLines.forEach {
          if (it.type == LineType.STRAIGHT) {
            priorityLine.offer(
              Pair(
                it,
                VeLine(Vector(it.start.x, it.start.y), Vector(it.end.x, it.end.y))
                  .vectorProjectToLine(Vector(request.startX, request.startY)),
              ),
            )
          } else {
            val closePose = it.getClosePose(request.startX, request.startY)
            priorityLine.offer(Pair(it, closePose.getDistance(request.startX, request.startY)))
          }
        }
        if (priorityLine.isNotEmpty()) {
          request.startType = PositionType.Path
          request.start = priorityLine.peek().first.lineName
          logger.info(
            "$robotName start line is ${request.start} |(${request.startX}, ${request.startY})| ${request.startAngle}",
          )
        } else {
          logger.error("can not find $robotName start line")
          // todo 要不要结束此次路径规划呢
        }
      }
    }
    request.targetAngle = request.targetAngle ?: MapService.findPointByName(
      context.sceneId,
      request.mapName,
      request.groupName,
      request.target,
    ).direction
    return context
  }

  /**
   *  查询机器人上下文信息
   * */
  fun queryRobotContext(robotName: String): RobotContext =
    robotContexts[robotName] ?: throw RuntimeException("can not find $robotName context")

  private fun buildRequest(request: RequestInfo, context: RobotContext) {
    val requestDomain = context.request
    requestDomain.updateOrder(request.orderId, request.stepId)
    requestDomain.target = request.target
    requestDomain.loadTargetAngle = request.containerTargetAngle
    context.baseDomain.containerType = request.containerType
  }

  private fun updateRobotContext(robotStatusReport: RobotStatusReport, context: RobotContext) {
    // todo 目前先按照这样处理
    if (context.mapName != robotStatusReport.mapName) {
      logger.error(
        "${context.robotName} current map ${context.mapName} is not same with report ${robotStatusReport.mapName}",
      )
      return
    }
    val baseDomain = context.baseDomain
    baseDomain.status = robotStatusReport.status
    val position = baseDomain.curPoint
    if (position != null &&
      (
        abs(position.x - robotStatusReport.x) > OFFSET_RANGE ||
          abs(position.y - robotStatusReport.y) > OFFSET_RANGE ||
          !AngleHelper.sameAngleInFiveDegree(baseDomain.robotHeading, robotStatusReport.robotAngle) ||
          !AngleHelper.sameAngleInFiveDegree(baseDomain.containerHeading, robotStatusReport.containerAngle)
        )
    ) {
      context.pauseTime = System.currentTimeMillis()
    }

    if (!robotStatusReport.pointName.isNullOrBlank()) {
      baseDomain.curPoint =
        Position(robotStatusReport.x, robotStatusReport.y, robotStatusReport.pointName, PosType.POINT)
    } else {
      var curPoint = baseDomain.curPoint
      if (robotStatusReport.index > -1) {
        val action = context.plan.queryActionByIndex(robotStatusReport.index)
        if (action != null) {
          curPoint = action.start
        }
      }
      if (curPoint != null) {
        baseDomain.curPoint = Position(robotStatusReport.x, robotStatusReport.y, curPoint.pointName, PosType.POINT)
      } else {
        // 根据 x,y 进行定位
        val box = BoundingBox(
          robotStatusReport.x - 5,
          robotStatusReport.y + 5,
          robotStatusReport.x + 5,
          robotStatusReport.y - 5,
        )
        val point = PositionService.findPerhapsPosition(
          Site(robotStatusReport.x, robotStatusReport.y),
          box,
          context.sceneId,
          context.mapName,
        )
        if (point != null) {
          baseDomain.curPoint = Position(robotStatusReport.x, robotStatusReport.y, point.pointName, PosType.POINT)
        } else {
          val line = PositionService.findPerhapsLine(
            Vector(robotStatusReport.x, robotStatusReport.y),
            box,
            robotStatusReport.robotAngle,
            context.sceneId,
            context.mapName,
          )
          if (line != null) {
            val p = if (line.start.getDistance(robotStatusReport.x, robotStatusReport.y) >
              line.end.getDistance(robotStatusReport.x, robotStatusReport.y)
            ) {
              line.end
            } else {
              line.start
            }
            baseDomain.curPoint = Position(robotStatusReport.x, robotStatusReport.y, p.pointName, PosType.POINT)
          }
        }
      }
    }
    baseDomain.robotHeading = robotStatusReport.robotAngle
    baseDomain.containerHeading = robotStatusReport.containerAngle
  }

  // 路径更新
  fun updatePath(robotName: String, response: PlanResponse): Boolean {
    val context = queryRobotContext(robotName)
    val lock = context.lock.tryLock(100, TimeUnit.MILLISECONDS)
    if (!lock) {
      logger.error("$robotName can not get lock for robot context")
      return false
    }
    try {
      val plan = context.plan
      plan.clear()
      plan.path = response.path
      context.deadLock.linkSuccessPoint = null
      logger.info(
        "$robotName path update current index ${context.plan.index}, " +
          "plan path ${LogParseHelper.parsePathLog(response.path)}",
      )
    } finally {
      context.lock.unlock()
    }
    return true
  }

  // 更新路径
  fun updatePath(robotName: String, path: MutableList<PathAction>): Boolean {
    val robotContext = queryRobotContext(robotName)
    val lock = robotContext.lock.tryLock(100, TimeUnit.MILLISECONDS)
    if (!lock) {
      logger.error("$robotName can not get lock for robot context ")
      return false
    }
    try {
      val plan = robotContext.plan
      plan.prevent = null
      plan.preventTime = 0
      plan.areas.clear()
      plan.path = path
      logger.info("$robotName | plan path ${LogParseHelper.parsePathLog(path)}")
    } finally {
      robotContext.lock.unlock()
    }
    return true
  }

  // 在机器人上线的时候调用，对机器人上下文进行初始化
  fun init(
    robotName: String,
    groupName: String,
    mapName: String,
    salverNotRotate: Boolean,
    motionDirection: MotionDirection,
    motionModel: MotionModel,
    rr: RobotRuntime,
  ): RobotContext {
    val robotContext = RobotContext(
      robotName = robotName,
      groupName = groupName,
      mapName = mapName,
      sceneId = rr.sr.sceneId,
      robotSalverNotRotate = salverNotRotate,
      robotMotionType = when (motionDirection) {
        MotionDirection.Unidirectional -> RobotMotionType.ADVANCE
        MotionDirection.Bidirectional -> RobotMotionType.BOTH
        MotionDirection.Omnidirectional -> RobotMotionType.OMNI
      },
      robotChassisType = when (motionModel) {
        MotionModel.Differential -> ChassisType.DIFF
        MotionModel.Steer -> ChassisType.STEER
        MotionModel.Omni -> ChassisType.OMNI
      },
    )
    robotContexts[robotName] = robotContext
    val robotStatusReport = DistributedTrafficService.queryRobotStatusReport(robotName, rr)
    if (!robotContext.lock.tryLock(100, TimeUnit.MILLISECONDS)) {
      logger.error("$robotName can not get lock for robot context ")
      return robotContext
    }
    try {
      updateRobotContext(robotStatusReport, robotContext)
    } finally {
      robotContext.lock.unlock()
    }

    robotContext.baseDomain.length = queryRobotLength(robotContext.sceneId, groupName)
    return robotContext
  }

  private fun queryRobotLength(sceneId: String, groupName: String): Double {
    val groupType = StaticRobotSpaceLock.queryRobotModelByGroupName(sceneId, groupName)
    var min: Double = 0.0
    var max: Double = 0.0
    groupType?.points?.forEach {
      if (it.x > max) max = it.x
      if (it.x < min) min = it.x
    }
    return if ((max - min) == 0.0) 2.0 else max - min
  }

  // 对机器人上下文进行销毁，在机器人下线的时候调用，清理上下文和锁闭资源
  fun destroy(robotCode: String): RobotContext? = robotContexts.remove(robotCode)

  // 更新机器人上下文信息
  fun updateRobotContext(robotStatusReport: RobotStatusReport): RobotContext? {
    val robotName = robotStatusReport.robotName
    val context = queryRobotContext(robotName)
    val lock = context.lock.tryLock(100, TimeUnit.MILLISECONDS)
    if (!lock) {
      logger.error("$robotName can not get lock for robot context")
      return null
    }
    try {
      updateRobotContext(robotStatusReport, context)
      // todo 目前只有正常工作时更新索引
      if (context.baseDomain.status == RobotStatus.WORK) {
        if (robotStatusReport.index > -1 && robotStatusReport.index < context.plan.allocateIndex) {
          context.plan.index = robotStatusReport.index + 1
        } else {
          context.plan.index = robotStatusReport.index
        }
      }
    } finally {
      context.lock.unlock()
    }
    return context
  }

  /**
   *  切换地图
   * */
  fun switchRobotMap(robotName: String, mapName: String): Boolean {
    val context = queryRobotContext(robotName)
    val lock = context.lock.tryLock(100, TimeUnit.MILLISECONDS)
    if (!lock) {
      logger.error("$robotName can not get lock for robot context")
      return false
    }
    try {
      context.mapName = mapName
    } finally {
      context.lock.unlock()
    }
    return true
  }

  // 任务取消，将数据和状态进行更新
  fun canceled(robotName: String, cancelPoint: Site?) {
    // todo 暂时先不处理
    finished(robotName)
  }

  fun finished(robotName: String) {
    val context = queryRobotContext(robotName)
    context.state = TrafficStatus.STOP
    val lock = context.lock.tryLock(100, TimeUnit.MILLISECONDS)
    if (!lock) {
      logger.error("$robotName can not get lock for robot context")
      return
    }
    try {
      context.plan.clear()
    } finally {
      context.lock.unlock()
    }
  }

  fun failed(robotName: String) {
    // todo 暂时先不处理
    finished(robotName)
  }

  fun resetRobot(robotName: String) {
    // todo 暂时先不处理
    try {
      finished(robotName)
    } catch (e: RuntimeException) {
      logger.error("$robotName reset robot error, robot not enter in traffic system")
    }
  }

  fun queryAllRobotContext(): MutableList<RobotContext> = robotContexts.values.toMutableList()

  /**
   *  更新机器人托盘旋转与行驶方向及运动模型
   * */
  fun updateRobotGroupBasic(
    sceneId: String,
    groupName: String,
    salverNotRotate: Boolean,
    motionDirection: MotionDirection,
    motionModel: MotionModel,
  ) {
    logger.info("update robot group basic $sceneId $groupName $salverNotRotate $motionDirection")
    val contexts = robotContexts.values.filter { it.sceneId == sceneId && it.groupName == groupName }
    contexts.forEach {
      it.robotSalverNotRotate = salverNotRotate
      it.robotMotionType = when (motionDirection) {
        MotionDirection.Unidirectional -> RobotMotionType.ADVANCE
        MotionDirection.Bidirectional -> RobotMotionType.OMNI
        MotionDirection.Omnidirectional -> RobotMotionType.OMNI
      }
      it.robotChassisType = when (motionModel) {
        MotionModel.Differential -> ChassisType.DIFF
        MotionModel.Steer -> ChassisType.STEER
        MotionModel.Omni -> ChassisType.OMNI
      }
    }
  }
}