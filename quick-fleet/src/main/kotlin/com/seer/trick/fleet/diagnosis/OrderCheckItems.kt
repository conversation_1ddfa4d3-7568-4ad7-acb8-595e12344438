package com.seer.trick.fleet.diagnosis

import CheckItem
import CheckReq
import CheckResult
import com.seer.trick.BzError
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.order.DispatchOrderService
import com.seer.trick.fleet.order.OrderRuntime
import com.seer.trick.fleet.order.StepSelectService
import com.seer.trick.fleet.service.MapService.NOT_ACHIEVABLE_COST
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.SceneService
import fail
import pass

object OrderCheckItems {

  /**
   * 校验运单参数
   * 1. keyLocations 是否为空
   * 2. stepNum 是否等于 steps.size 匹配
   * 3. doneStepIndex 是否在范围内
   * 4. currentStepIndex 是否在范围内
   */
  object OrderParametersValidate : CheckItem {
    override val name = "OrderParametersValidate"
    override fun check(checkReq: CheckReq): CheckResult {
      val or = checkReq.or
      if (or == null) return fail("OrderIsNull")
      val order = checkReq.or.order
      if (order.status != OrderStatus.ToBeAllocated) return pass()
      // 参数校验逻辑
      val result = when {
        order.noLoad && (order.loaded || order.unloaded) -> fail("ErrorLoad")
        order.keyLocations.isNullOrEmpty() -> fail("KeyLocationsNull")
        order.stepNum != or.steps.size -> fail("ErrorStepSize")
        order.currentStepIndex < order.doneStepIndex -> fail("ErrorCurrentStepIndex")
        order.currentStepIndex >= or.steps.size -> fail("ErrorCurrentStepIndex")
        or.steps.isEmpty() && (order.currentStepIndex != -1 || order.doneStepIndex != -1) ->
          fail("ErrorOrderStepIndex")

        or.steps.any { it.stepIndex >= order.stepNum } -> fail("ErrorStepIndex")
        else -> pass()
      }
      return result
    }
  }

  /**
   * 运单的关键路径是有效点位
   *
   * or.order.keyLocations 为空 -> 有效
   * 任意区域内至少有一个 keyLocation 就认为是有效的。不管是不是可达站点
   */
  object OrderKeyLocationsValidate : CheckItem {
    override val name = "OrderKeyLocationsReachability"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.or == null) return fail("OrderIsNull")
      val locations = checkReq.or.order.keyLocations
      if (locations.isNullOrEmpty()) return pass()

      val sr = SceneService.mustGetSceneById(checkReq.or.order.sceneId)
      val pointIds = sr.mapCache.areaById.values.flatMap { it.mergedMap.pointIdMap.keys }.map { it.toString() }
      val pointNames = sr.mapCache.pointNames
      val binNames = sr.mapCache.binNames
      for (loc in locations) {
        if (pointIds.contains(loc) || pointNames.contains(loc) || binNames.contains(loc)) return pass()
      }
      return fail("ErrKeyLocationInvalidate")
    }
  }

  /**
   * 该运单的关键路径是否可达
   */
  object OrderReachability : CheckItem {
    override val name = "OrderReachability"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.or == null) return fail("OrderIsNull")
      if (checkReq.or.order.status != OrderStatus.ToBeAllocated) return pass()
      val sr = SceneService.mustGetSceneById(checkReq.or.order.sceneId)
      val robots = sr.listRobots()
      val expectedRobots = checkReq.or.order.expectedRobotNames
      val expectedGroups = checkReq.or.order.expectedRobotGroups
      val result = when {
        expectedRobots?.isNotEmpty() == true -> validateExpectedRobotsReachability(
          expectedRobots,
          robots,
          checkReq.or,
        )

        expectedGroups?.isNotEmpty() == true -> validateExpectedGroupsReachability(
          expectedGroups,
          robots,
          checkReq.or,
        )

        else -> validateGlobalReachability(robots, checkReq.or)
      }
      return result
    }

    /**
     * 有期望的机器人时，校验可达性
     */
    private fun validateExpectedRobotsReachability(
      expected: List<String>,
      robots: List<RobotRuntime>,
      order: OrderRuntime,
    ): CheckResult {
      val availableRobots = robots.filter { it.robotName in expected }
      return if (availableRobots.isEmpty()) {
        fail(
          "errNoRobots",
          listOf(expected),
        )
      } else if (!availableRobots.any { rr ->
          try {
            // 存在与 validateGlobalReachability() 类似的问题，处理方式也相同。
            val result = DispatchOrderService.estimateOrderCost(order, rr)
            result != NOT_ACHIEVABLE_COST
          } catch (_: BzError) {
            false
          } catch (_: IllegalStateException) {
            false
          }
        }
      ) {
        fail(
          "RobotsUnreachable",
          listOf(expected, order.order.keyLocations?.firstOrNull()),
        )
      } else {
        pass()
      }
    }

    /**
     * 有期望的机器人组时校验可达性
     */
    private fun validateExpectedGroupsReachability(
      expected: List<String>,
      robots: List<RobotRuntime>,
      order: OrderRuntime,
    ): CheckResult {
      val existingGroups = robots.map { it.mustGetGroup().name }.toSet()
      val missingGroups = expected - existingGroups
      if (missingGroups.isNotEmpty()) return fail("MissingGroups", missingGroups)

      val groupRobots = robots.filter { it.mustGetGroup().name in expected }
      // 机器人组里可能有机器人是不可达运单的关键位置（比如一张地图中有多个互相不连通的区域），所以存在一个可达就可以了
      return if (!groupRobots.any { rr ->
          try {
            // 存在与 validateGlobalReachability() 类似的问题，处理方式也相同。
            val result = DispatchOrderService.estimateOrderCost(order, rr)
            result != NOT_ACHIEVABLE_COST
          } catch (_: BzError) {
            false
          } catch (_: IllegalStateException) {
            false
          }
        }
      ) {
        fail(
          "RobotGroupUnreachable",
          listOf(
            expected,
            order.order.keyLocations?.firstOrNull(),
          ),
        )
      } else {
        pass()
      }
    }

    /**
     * 校验全局机器人可达性
     */
    private fun validateGlobalReachability(robots: List<RobotRuntime>, order: OrderRuntime): CheckResult =
      if (!robots.any { rr ->
          try {
            // 多种原因会导致 rr.startPointNameForDispatching = null，
            // 从而导致 estimateOrderCost() 抛异常 IllegalStateException，但是此异常未被捕获，
            // 进而导致 DispatchOrderWorker::dispatch() 报错，无法派单。
            //
            // 导致 rr.startPointNameForDispatching = null 原因有：
            //  1. 机器人已禁用；
            //  2. 机器人离线；
            //  3. 机器人在线，但是无法稳定获取机器人的信息（1100）；
            //  4. 机器人在线，但是机器人既不在点上，也远离任何一条线路。
            // 情况复杂，直接捕获异常 IllegalStateException 吧。
            val result = DispatchOrderService.estimateOrderCost(order, rr)
            result != NOT_ACHIEVABLE_COST
          } catch (_: BzError) {
            false
          } catch (_: IllegalStateException) {
            false
          }
        }
      ) {
        fail("RobotsAllUnreachable", listOf(order.order.keyLocations?.firstOrNull()))
      } else {
        pass()
      }
  }

  /**
   * 校验机器人状态
   */
  object ValidateRobotsStatus : CheckItem {
    override val name = "validateRobotsStatus"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.or == null) return fail("OrderIsNull")
      /**
       * 对于待分配的运单，校验机器人的状态
       */
      val sr = SceneService.mustGetSceneById(checkReq.or.order.sceneId)
      val robots = sr.listRobots()
      val order = checkReq.or.order
      val expectedRobots = order.expectedRobotNames
      val expectedGroups = order.expectedRobotGroups
      if (order.status != OrderStatus.ToBeAllocated) return pass()

      return when {
        expectedRobots?.isNotEmpty() == true -> validateExpectedRobotsStatus(expectedRobots, robots)
        expectedGroups?.isNotEmpty() == true -> validateExpectedGroupsStatus(expectedGroups, robots)
        else -> validateGlobalStatus(robots)
      }
    }

    /**
     * 有期望的机器人时，校验机器人状态
     */
    private fun validateExpectedRobotsStatus(expected: List<String>, robots: List<RobotRuntime>): CheckResult {
      val availableRobots = robots.filter { it.robotName in expected }
      val reasonsMap = mutableMapOf<String, String>()
      if (availableRobots.all { it.orderReject != null }) {
        availableRobots.forEach { rr ->
          reasonsMap[rr.robotName] = rr.orderReject?.code ?: "Unknown"
        }
      }
      return if (reasonsMap.isNotEmpty()) {
        fail("AllRobotUnDispatchable", listOf(reasonsMap.toString()))
      } else {
        pass()
      }
    }

    /**
     * 有期望的机器人组时，校验机器人状态
     */
    private fun validateExpectedGroupsStatus(expected: List<String>, robots: List<RobotRuntime>): CheckResult {
      val groupRobots = robots.filter { it.mustGetGroup().name in expected }
      val reasonsMap = mutableMapOf<String, String>()
      if (groupRobots.all { it.orderReject != null }) {
        groupRobots.forEach { rr ->
          reasonsMap[rr.robotName] = rr.orderReject?.code ?: "Unknown"
        }
      }
      return if (reasonsMap.isNotEmpty()) {
        fail("AllRobotUnDispatchable", listOf(reasonsMap))
      } else {
        pass()
      }
    }

    /**
     * 无期望的机器人组时，校验全局机器人状态
     */
    private fun validateGlobalStatus(robots: List<RobotRuntime>): CheckResult {
      val reasonsMap = mutableMapOf<String, String>()
      if (robots.all { it.orderReject != null }) {
        robots.forEach { rr ->
          reasonsMap[rr.robotName] = rr.orderReject?.code ?: "Unknown"
        }
      }
      return if (reasonsMap.isNotEmpty()) {
        fail("AllRobotUnDispatchable", listOf(reasonsMap.toString()))
      } else {
        pass()
      }
    }
  }

  /**
   * 校验运单是否是业务单
   */
  object OrderNotBusiness : CheckItem {
    override val name = "OrderNotBusiness"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.or == null) return fail("OrderIsNull")
      val order = checkReq.or.order
      if (order.kind != OrderKind.Business) return fail("OrderNotBusiness", listOf(order.kind.name))
      return pass()
    }
  }

  /**
   * 运单状态不是待分派
   */
  object OrderNotToBeAllocated : CheckItem {
    override val name = "OrderNotToBeAllocated"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.or == null) return fail("OrderIsNull")
      val order = checkReq.or.order
      if (order.status != OrderStatus.ToBeAllocated) return fail("OrderNotToBeAllocated", listOf(order.kind.name))
      return pass()
    }
  }

  /**
   * 期待的机器人、组都正在执行其他单子
   */
  object ExpectedRobotExecutingOtherOrder : CheckItem {
    override val name = "ExpectedRobotExecutingOtherOrder"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.or == null) return fail("OrderIsNull")
      /**
       * 对于待分配的运单，校验机器人的状态
       */
      val sr = SceneService.mustGetSceneById(checkReq.or.order.sceneId)
      val order = checkReq.or.order
      if (order.status != OrderStatus.ToBeAllocated) return pass()

      if (!order.expectedRobotNames.isNullOrEmpty()) {
        // 如果指定了机器人
        val anyIdle = order.expectedRobotNames.any { sr.robots[it]?.getExecuteStatus() == RobotExecuteStatus.Idle }
        return if (anyIdle) pass() else fail("ExpectedRobotExecutingOtherOrder")
      }

      if (!order.expectedRobotGroups.isNullOrEmpty()) {
        // 如果指定了机器人组
        val anyIdle = sr.listRobots().any {
          order.expectedRobotGroups.contains(it.mustGetGroup().name) && it.getExecuteStatus() == RobotExecuteStatus.Idle
        }
        return if (anyIdle) pass() else fail("ExpectedRobotExecutingOtherOrder")
      }

      val anyIdle = sr.listRobots().any { it.getExecuteStatus() == RobotExecuteStatus.Idle }
      return if (anyIdle) pass() else fail("ExpectedRobotExecutingOtherOrder")
    }
  }

  /**
   * 可接单的机器人不可达
   */
  object OnDutyRobotsUnreachable : CheckItem {
    override val name = "OnDutyRobotsUnreachable"
    override fun check(checkReq: CheckReq): CheckResult {
      val sceneId = checkReq.or?.order?.sceneId ?: return fail("OrderIsNull")
      val sr = SceneService.mustGetSceneById(sceneId)
      // 可接单并且非停用的机器人
      val robots = sr.listRobots().filter { !it.offDuty && !it.disabled() }
      if (robots.isEmpty()) return fail("NoAvailableRobots")
      for (rr in robots) {
        try {
          val result = DispatchOrderService.estimateOrderCost(checkReq.or, rr)
          if (result == NOT_ACHIEVABLE_COST) continue else return pass()
        } catch (_: BzError) {
          continue
          // 存在与 validateGlobalReachability() 类似的问题，处理方式也相同。
        } catch (_: IllegalStateException) {
          continue
        }
      }
      return fail("OnDutyRobotsUnreachable", listOf(robots.joinToString(",") { it.robotName }))
    }
  }

  /**
   * 运单没有下一步但未封口
   */
  object OrderNoNextStep : CheckItem {
    override val name = "OrderNoNextStep"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.or == null) return fail("OrderIsNull")
      val order = checkReq.or.order
      if (order.doneStepIndex + 1 == order.stepNum) return fail("OrderNoNextStep")
      return pass()
    }
  }

  /**
   * 运单下一步不可达
   */
  object OrderStepCannotAchievable : CheckItem {
    override val name = "OrderStepCannotAchievable"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.or == null) return fail("OrderIsNull")
      if (checkReq.rr == null) return fail("RobotIsNull")
      if (checkReq.fromPointName.isNullOrBlank()) return fail("FromPointNullOrBlank")
      val order = checkReq.or.order
      if (order.status != OrderStatus.Allocated && order.status != OrderStatus.Pending) return pass()
      // 运单为 Allocated 或者 Pending 状态时，校验下一步是否可达
      if (order.doneStepIndex + 1 < order.stepNum) {
        val step = checkReq.or.steps[order.doneStepIndex + 1]
        try {
          val cost = StepSelectService.estimateStepCost(checkReq.rr, checkReq.fromPointName, step)
          if (cost == NOT_ACHIEVABLE_COST) {
            // 不可达
            return fail("OrderStepCannotAchievable", listOf(checkReq.fromPointName, step.location))
          }
        } catch (e: BzError) {
          // 点位不存在
          return fail(e.code, listOf(if (e.args.isNotEmpty())e.args[0] else null))
        }
      }
      return pass()
    }
  }

  /**
   * 校验运单是否是故障单
   */
  object OrderFault : CheckItem {
    override val name = "OrderFault"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.or == null) return fail("OrderIsNull")
      val order = checkReq.or.order
      if (order.fault) return fail("OrderFault")
      return pass()
    }
  }

  /**
   * 校验运单状态，不为 Cancelled
   */
  object OrderStatusCheck : CheckItem {
    override val name = "OrderNotWithdrawnOrCancelled"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.or == null) return fail("OrderIsNull")
      val order = checkReq.or.order
      if (order.status == OrderStatus.Cancelled) {
        return fail("OrderNotWithdrawnOrCancelled", listOf(checkReq.or.order.status.name))
      }
      return pass()
    }
  }

  /**
   * 判读运单是否已分派
   */
  object OrderIsAllocated : CheckItem {
    override val name = "OrderIsAllocated"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.or == null) return fail("OrderIsNull")
      val order = checkReq.or.order
      if (order.status == OrderStatus.ToBeAllocated) return fail("OrderNotAllocated")
      return pass()
    }
  }

  /**
   * 判断运单是否已取货,运单已载货，不能二分；即使后面卸了也不行
   */
  object OrderLoaded : CheckItem {
    override val name = "OrderLoaded"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.or == null) return fail("OrderIsNull")
      val order = checkReq.or.order
      if (order.loaded) return fail("OrderLoaded")
      return pass()
    }
  }

  /**
   * 第一个动作已经做完，就不允许再重分配了
   */
  object OrderFirstStepDone : CheckItem {
    override val name = "FirstStepDone"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.or == null) return fail("OrderIsNull")
      val order = checkReq.or.order
      if ((order.status == OrderStatus.Executing || order.status == OrderStatus.Pending) &&
        order.doneStepIndex > -1
      ) {
        return fail("FirstStepDone")
      }
      return pass()
    }
  }

  /**
   * 已经分派给机器人执行的运单,并且设置了禁止运单重分配，则不改变分派。
   */
  object NotReallocation : CheckItem {
    override val name = "NotReallocation"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.or == null) return fail("OrderIsNull")
      val order = checkReq.or.order
      val sr = SceneService.mustGetSceneById(checkReq.or.order.sceneId)
      if (!order.actualRobotName.isNullOrBlank() && sr.config.noReallocation) return fail("NotReallocation")
      return pass()
    }
  }
}