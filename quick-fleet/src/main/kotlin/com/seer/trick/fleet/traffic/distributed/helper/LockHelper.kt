package com.seer.trick.fleet.traffic.distributed.helper

import com.seer.trick.fleet.traffic.distributed.lock.graph.*
import com.seer.trick.fleet.traffic.distributed.lock.model.*
import com.seer.trick.fleet.traffic.distributed.map.Position
import org.slf4j.LoggerFactory

/**
 *  锁闭模型辅助类
 * */
object LockHelper {

  private val logger = LoggerFactory.getLogger(javaClass)

  // 锁闭模型旋转
  fun lockModelRotation(cell: Cell, heading: Int) {
    val angle = AngleHelper.degreesToRadians(heading)
    for (layer in cell.layers) {
      val shape = layer.shape
      when (shape.type) {
        GraphType.RECTANGLE -> {
          val rectangle = shape as Rectangle
          val points: MutableList<Vector> = mutableListOf()
          for (i in 0 until rectangle.points.size) {
            val point = rectangle.points[i]
            points += point.rotate(angle)
          }
          layer.shape = Rectangle(type = rectangle.type, points = points, box = BoundingBox(points))
        }

        GraphType.POLYGON -> {
          val polygon = shape as Polygon
          val points: MutableList<Vector> = mutableListOf()
          for (i in 0 until polygon.points.size) {
            val point = polygon.points[i]
            points += point.rotate(angle)
          }
          layer.shape = Polygon(
            type = polygon.type,
            points = points,
            concave = polygon.concave,
            subPolygons = polygon.subPolygons,
            box = BoundingBox(points),
          )
        }

        GraphType.SECTOR -> {}
        else -> {}
      }
    }
    cell.updateBoundingBox()
  }

  fun lockModelTranslation(cell: Cell, point: Position) {
    for (layer in cell.layers) {
      val shape = layer.shape
      when (shape.type) {
        GraphType.RECTANGLE -> {
          val rectangle = shape as Rectangle
          val points: MutableList<Vector> = mutableListOf()
          for (i in 0 until rectangle.points.size) {
            points += rectangle.points[i].add(Vector(point.x, point.y))
          }
          layer.shape = Rectangle(type = rectangle.type, points = points, box = BoundingBox(points))
        }

        GraphType.POLYGON -> {
          val polygon = shape as Polygon
          val points: MutableList<Vector> = mutableListOf()
          for (i in 0 until polygon.points.size) {
            points += polygon.points[i].add(Vector(point.x, point.y))
          }
          layer.shape = Polygon(
            type = polygon.type,
            points = points,
            concave = polygon.concave,
            subPolygons = polygon.subPolygons,
            box = BoundingBox(points),
          )
        }

        GraphType.CIRCLE -> {
          val circle = shape as Circle
          val center = circle.center.add(Vector(point.x, point.y))
          layer.shape = Circle(type = circle.type, radius = circle.radius, center = center)
        }

        GraphType.SECTOR -> {}
      }
    }
    cell.updateBoundingBox()
  }

  fun moveSpaceLock(startLock: SpaceLock, endLock: SpaceLock): SpaceLock {
    val s = startLock.getPoints()[0]
    val t = endLock.getPoints()[0]
    if (s.x == t.x && s.y == t.y) {
      return endLock
    }
    // 初始的角度，用s -> t 方向的角度
    var angle = AngleHelper.getAngle(s, t) // Vector(s.x, s.y).angleTo(Vector(t.x, t.y))
    val sl = startLock.cells[0].layers
    val el = endLock.cells[0].layers
    if (sl.size != el.size) {
      logger.error("startLock and endLock must have the same layers")
      return endLock
    }
    val layers: MutableList<Layer> = mutableListOf()
//    val spaceLocks: MutableList<SpaceLock> = mutableListOf()
    for (i in 0 until sl.size) {
      val ss = sl[i].shape
      val es = el[i].shape
      val edgeCost: MutableMap<Vector, MutableMap<Vector, Int>> = mutableMapOf()
      val vectors: MutableList<Vector> = mutableListOf()
      val sp = queryPoints(ss)
      vectors.addAll(sp)
      vectors.addAll(queryPoints(es))
      for (vector in vectors) {
        val vm: MutableMap<Vector, Int> = mutableMapOf()
        for (v in vectors) {
          if (v == vector) continue

          vm[v] = AngleHelper.getAngle(vector, v) // vector.angleTo(v)
        }
        edgeCost[vector] = vm
      }
      val points: MutableList<Vector> = mutableListOf()
      var min: Int? = null
      var tangle: Int? = null
      for (j in 0 until sp.size) {
        val to = AngleHelper.getAngle(sp[j], sp[(j + 1) % sp.size]) // sp[i].angleTo(sp[(i + 1) % sp.size])
        val d = AngleHelper.vectorAngle(to, angle)
        if (min == null) {
          min = d
          tangle = to
          points.add(sp[j])
          points.add(sp[(j + 1) % sp.size])
        } else if (d < min) {
          min = d
          tangle = to
          points.clear()
          points.add(sp[j])
          points.add(sp[(j + 1) % sp.size])
        }
      }
      var index = 0
      while (index < 100) {
        angle = tangle ?: 0
        min = null
        val last = points.last()
        val mutableMap = edgeCost[last]
        if (mutableMap != null) {
          var minPoint: Vector? = null
          for (map in mutableMap) {
//            if (points.contains(map.key)) continue
            val d = AngleHelper.vectorAngle(map.value, angle)
            if (min == null) {
              min = d
              tangle = map.value
              minPoint = map.key
            } else if (!AngleHelper.sameAngleInOneDegree(d, min) && d < min) {
              min = d
              tangle = map.value
              minPoint = map.key
            } else if (AngleHelper.sameAngleInOneDegree(d, min)) {
              if (minPoint != null && last.distanceTo(minPoint) < last.distanceTo(map.key)) {
                minPoint = map.key
              }
            }
          }
          if (points.contains(minPoint)) {
            break
          } else {
            if (points.size == 2 && VeLine(points[0], points[1]).pointOnLine(minPoint!!)) {
              points.removeAt(1)
              points.add(minPoint)
            } else {
              points.add(minPoint!!)
            }
          }
        } else {
          break
        }
        index++
      }
      if (index == 100) {
        logger.error("build move space lock error ${startLock.cells[0].layers[0].shape}")
        return endLock
      }
      val polygon = Polygon(
        type = GraphType.POLYGON,
        points = points,
        concave = false,
        subPolygons = mutableListOf(),
        box = BoundingBox(points),
      )
      layers.add(Layer(id = i, top = sl[i].top, bottom = sl[i].bottom, shape = polygon))
    }
    val cell = Cell(layers = layers, sPosition = s)
    cell.tPosition = t
    return SpaceLock(
      groupName = startLock.groupName,
      type = startLock.type,
      name = startLock.name,
      cells = mutableListOf(cell),
      mapName = startLock.mapName,
    )
  }

  private fun queryPoints(shape: Shape): MutableList<Vector> {
    val points: MutableList<Vector> = mutableListOf()
    if (shape.type == GraphType.RECTANGLE) {
      val rectangle = shape as Rectangle
      points.addAll(rectangle.points)
    } else if (shape.type == GraphType.POLYGON) {
      val polygon = shape as Polygon
      points.addAll(polygon.points)
    }
    return points
  }

  fun lockSpacesUnion(spaces: MutableList<SpaceLock>): SpaceLock {
    if (spaces.isEmpty()) {
      throw Exception("lock spaces is empty")
    }
    val spaceLock = spaces[0].copy()
    for (i in 1 until spaces.size) {
      spaceLock.union(spaces[i])
    }
    return spaceLock
  }
}