package com.seer.trick.fleet.mock.service

import com.seer.trick.fleet.domain.GeoHelper
import com.seer.trick.fleet.mock.MockSeerRobotRuntime
import com.seer.trick.fleet.mock.MockSmapHelper
import com.seer.trick.fleet.mock.service.MockService.DEFAULT_MAX_ROT_SPEED
import com.seer.trick.fleet.mock.service.MockService.DEFAULT_MAX_SPEED
import com.seer.trick.fleet.mock.service.MockService.MOCK_FPS
import kotlin.math.PI
import kotlin.math.ceil

object MockGeoHelper {

  /**
   * 计算旋转参数
   * @param fromRad 当前角度。归一弧度制 [0, 2π]
   * @param toRad 目标角度。归一弧度制 [0, 2π]
   * @param dir 旋转方向，-1 表示顺时针，1 表示逆时针，null 表示自动判断
   */
  fun calculateRotationParams(fromRad: Double, toRad: Double, dir: Int? = null): RotationParams {
    val from = GeoHelper.normalizeRadian(fromRad)
    val to = GeoHelper.normalizeRadian(toRad)

    val ccw = GeoHelper.normalizeRadian(to - from) // 逆时针
    val cw = GeoHelper.normalizeRadian(from - to) // 顺时针

    return when {
      dir == 1 -> RotationParams(ccw, 1)
      dir == -1 -> RotationParams(cw, -1)
      cw <= ccw -> RotationParams(cw, -1)
      else -> RotationParams(ccw, 1)
    }
  }

  /**
   * 角度转换成弧度
   * [-π, π]
   */
  fun angleToRadians(degrees: Double): Double = degrees * (PI / 180.0)

  /**
   * 仿真：获取路径的 goodsDir
   */
  fun getPathGoodsDir(mr: MockSeerRobotRuntime, move: Step3066): Double? = MockSmapHelper.getPathByFromAndTo(
    mr.robotId,
    move.sourceId,
    move.id,
  )?.path?.property?.firstOrNull { it.key == "goodsDir" }?.doubleValue

  fun maxVelocity(maxVelocity: Double?): Double =
    maxVelocity ?: MockService.config.actionsCosts.maxVelocity ?: DEFAULT_MAX_SPEED

  /**
   * 计算步数，确保步数至少为 2
   *
   * 每秒 50 步，每步 20 ms
   */
  fun calculateSteps(distance: Double, maxVelocity: Double?): Int {
    val velocity = maxVelocity(maxVelocity)
    val steps = ceil(distance / (velocity * MockService.mockSpeedFactor) * MOCK_FPS).toInt()
    return maxOf(steps, 2) // 确保至少有 2 步
  }

  /**
   * 每帧旋转的弧度 rad
   *
   * 每秒 50 步，每步 20 ms
   */
  fun getStepRad(maxRotateV: Double?): Double = maxRotateV(maxRotateV) * MockService.mockSpeedFactor / MOCK_FPS

  /**
   * 最大旋转速度 rad/s
   */
  fun maxRotateV(maxRotateV: Double?): Double =
    maxRotateV ?: MockService.config.actionsCosts.maxRotateVelocity ?: DEFAULT_MAX_ROT_SPEED
}

data class RotationParams(val rotationDiff: Double, val direction: Int)