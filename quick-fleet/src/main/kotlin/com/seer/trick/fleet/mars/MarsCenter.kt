package com.seer.trick.fleet.mars

import com.seer.trick.BzError
import com.seer.trick.fleet.service.SceneService

object MarsCenter {
  
  fun init() {
    MarsReportApplyManager.init()
  }
  
  /**
   * 获取指定场景，否则尝试获取第一个光通讯场景
   */
  fun getSceneOrFirstMarsScene(sceneId: String? = null) = if (sceneId.isNullOrBlank()) {
    SceneService.listScenes().find { !it.basic.disabled && it.config.cpnEnabled }
      ?: throw BzError("errNoFirstLightScene")
  } else {
    SceneService.mustGetSceneById(sceneId)
  }
  
  /**
   * 根据场景名称获取指定场景，否则尝试获取第一个光通讯场景
   */
  fun getSceneByNameOrFirstMarsScene(sceneName: String? = null) = if (sceneName.isNullOrBlank()) {
    SceneService.listScenes().find { !it.basic.disabled && it.config.cpnEnabled }
      ?: throw BzError("errNoFirstLightScene")
  } else {
    SceneService.mustGetSceneByName(sceneName)
  }
}