package com.seer.trick.fleet.mock.tcp

import com.seer.trick.base.soc.*
import com.seer.trick.fleet.mock.MockSeerRobotRuntime
import com.seer.trick.fleet.mock.service.MockService
import com.seer.trick.fleet.seer.RobotRegister
import com.seer.trick.helper.JsonHelper
import com.seer.trick.robot.vendor.seer.rbk.*
import com.seer.wcs.device.tcp.TcpClient
import io.netty.channel.ChannelHandlerContext
import java.net.InetSocketAddress
import java.util.concurrent.*

/**
 * CoffeeTCP 客户端处理器类，负责管理和处理 TCP 连接及相关操作
 * 继承自AbstractTcpConnection，提供了一套用于 TCP 客户端的实现
 * TODO 放到 mock 下
 * @param mr MockSeerRobotRuntime的实例，用于访问和操作机器人运行时环境
 */
class CoffeeTcpClientHandler(override var mr: MockSeerRobotRuntime) : AbstractTcpConnection(mr) {
  
  @Volatile
  private var client: TcpClient<RbkFrame>? = null
  
  private val logHead: String =
    "[${mr.config.name}][${mr.config.id}]${mr.config.fleetTcpServerIp}:${mr.config.getFleetTcpServerPortOrDefault()} "
  
  // 存放接受到消息的队列
  private val receiveQueue: BlockingQueue<RbkFrame> = LinkedBlockingQueue()
  
  // 存放发送消息的队列
  private val sendQueue: BlockingQueue<RbkFrame> = LinkedBlockingQueue()
  
  // 处理收到的消息
  private val processor = Executors.newFixedThreadPool(2)
  
  // 处理要发出的消息
  private val sender = Executors.newScheduledThreadPool(2)
  
  private val scheduler: ScheduledExecutorService = Executors.newScheduledThreadPool(3)
  private var worker: List<Future<*>> = mutableListOf()
  // private var pongMsgQueue: BlockingQueue<RbkFrame> = LinkedBlockingQueue()
  // private val pingLock = Any()
  // private var lastPingTime: Long = 0
  
  companion object {
    private const val PING_AND_PONG_API_NO = 0x1235
    private const val REGISTER_API_NO = 0x206c
    private const val REGISTER_RESPONSE_API_NO = 0x1234
    private const val PONG_TIMEOUT = 5000L
  }
  
  override fun logAndUpdateState(message: String, attention: SocAttention, noLog: Boolean) {
    if (!noLog) {
      when (attention) {
        SocAttention.Red -> logger.error("$logHead $message")
        SocAttention.Yellow -> logger.warn("$logHead $message")
        SocAttention.Green -> logger.info("$logHead $message")
        else -> logger.debug("$logHead $message")
      }
    }
    
    SysMonitorService.log(
      subject = "Coffee",
      target = mr.config.name,
      field = "logAndUpdateState",
      value = message,
      instant = true,
    )
    SocService.updateNode(
      "Mock Robot",
      "Mock Robot Client: $logHead",
      "Mock Robot Client:$logHead",
      message,
      attention,
    )
  }
  
  override fun init() {
    getClient()
    worker += scheduler.scheduleWithFixedDelay(::process, 1500, 500, TimeUnit.MILLISECONDS)
    worker += scheduler.scheduleWithFixedDelay(::cleanWorker, 3000, 5000, TimeUnit.MILLISECONDS)
    worker += scheduler.scheduleWithFixedDelay(::send, 1500, 500, TimeUnit.MILLISECONDS)
    worker += scheduler.scheduleWithFixedDelay(::pingServer, 1700, 10000, TimeUnit.MILLISECONDS)
  }
  
  /**
   * 销毁 tcp 连接
   */
  override fun dispose() {
    disconnect()
    receiveQueue.clear()
    for (f in worker) {
      if (!f.isDone && !f.isCancelled) {
        f.cancel(true)
      }
    }
    processor.shutdownNow()
    sender.shutdownNow()
    scheduler.shutdownNow()
  }
  
  override fun disconnect() {
    client?.let {
      it.dispose()
      client = null
    }
  }
  
  @Synchronized
  override fun reset() {
    disconnect()
    getClient()
  }
  
  /**
   * 处理 client 收到的消息
   */
  override fun onMessage(ctx: ChannelHandlerContext, frame: RbkFrame) {
    try {
      // 机器人停用的话忽略消息
      if (MockService.paused || mr.config.disabled) return
      when (frame.apiNo) {
        REGISTER_RESPONSE_API_NO -> {} // 注册响应，不需要特殊处理
        PING_AND_PONG_API_NO -> {} // 回复的 pong 消息
        else -> { // 其他消息
          val address = ctx.channel().remoteAddress() as InetSocketAddress
          receiveQueue.put(RbkFrame(frame.flowNo, frame.apiNo, frame.bodyStr, address.toString()))
        }
      }
    } catch (e: Throwable) {
      logAndUpdateState("onMessage error: ${e.message}", SocAttention.Red) // soc 会被刷掉，主要用来打印日志记录
      dispose()
    }
  }
  
  private fun onRemoved(ctx: ChannelHandlerContext) {
    logger.info("Client removed")
    disconnect()
  }
  
  /**
   * 获取 tcp 客户端，根据传入的调度 ip，与调度端口
   */
  @Synchronized
  private fun getClient(): TcpClient<RbkFrame> {
    var client = client
    if (client != null) return client
    client = TcpClient(
      mr.config.fleetTcpServerIp!!,
      mr.config.getFleetTcpServerPortOrDefault(),
      RbkTcp.schema,
      false,
      ::onMessage,
      ::onRemoved,
    )
    this.client = client
    // 这里是新创建的 client，需要注册到 server
    registerToServer(client)
    return client
  }
  
  /**
   * 从接受请求的队列中取出消息，并处理消息
   */
  private fun process() {
    if (MockService.paused || mr.config.disabled) return
    
    try {
      while (receiveQueue.isNotEmpty()) {
        worker += processor.submit { doProcess(receiveQueue.take()) } // 当前 future 完成后，从 worker 中移除
      }
    } catch (e: Exception) { // TODO 这里 try-catch 没太大必要，不太可能失败，doProcess 失败可能性更大
      logAndUpdateState("process error ${e.message}", SocAttention.Red)
    }
  }
  
  /**
   * 真正处理接受到的消息，处理完成之后，放入发送队列中
   */
  private fun doProcess(f: RbkFrame) {
    // TODO 如果处理失败，要把消息放到一个队列里，重新发
    if (MockService.paused || mr.config.disabled) {
      receiveQueue.put(f)
    }
    // TODO 失败后的处理，要把消息塞回 requestQueue 重新处理，并且去重，已经执行过的，不要重复执行
    val r = handleMessage(f)
    val respApiNo = 10000 + f.apiNo
    sendQueue.put(RbkFrame(f.flowNo, respApiNo, r, f.address))
  }
  
  /**
   * 发送消息给 TCP Server
   *
   * 从 sendQueue 中取出消息，发送给 TCP Server。如果发送失败，或者已经暂停了，则塞回 sendQueue，重新发。
   * ping、register 不用重新发。
   */
  private fun send() {
    if (MockService.paused || mr.config.disabled) return
    while (sendQueue.isNotEmpty()) { // TODO 每次塞一个还是塞全部
      worker += sender.submit { doSend() } // 当前 future 完成后，从 worker 中移除
    }
  }
  
  /**
   * 执行发送操作的方法
   * 本方法负责从发送队列中取出待发送的数据，并根据当前的状态决定是否进行发送
   * 如果发送过程中遇到异常，会将数据重新放入队列，并断开与客户端的连接
   */
  private fun doSend() {
    // 从发送队列中取出待发送的数据
    val f = sendQueue.take()
    // 检查模拟服务是否暂停或者客户端是否为空，如果是，则将数据放回队列并返回
    if (MockService.paused || mr.config.disabled || client == null) {
      sendQueue.put(f)
      return
    }
    try {
      // 尝试获取客户端并发送数据
      getClient().write(RbkEncoder.buildReqBytes(f.apiNo, f.bodyStr, f.flowNo))
    } catch (e: Throwable) {
      // 发送失败时将数据重新放入队列，断开与客户端的连接，并记录错误状态
      sendQueue.put(f)
      disconnect()
      logAndUpdateState("doSend error ${e.message}", SocAttention.Red)
    }
  }
  
  /**
   * 发送心跳信号以检查服务器状态
   * 此函数用于定期向服务器发送心跳信号，以确认服务器是否正常运行
   * 它使用 RbkEncoder 构建请求数据，并通过客户端写入数据
   * 如果操作成功，则更新状态，否则记录错误并断开连接
   */
  private fun pingServer() {
    try {
      // 构建心跳请求数据
      val buf = RbkEncoder.buildReqBytes(PING_AND_PONG_API_NO, "ping", nextFlowNo())
      // 向服务器写入请求数据
      getClient().write(buf)
      // 更新状态并记录日志
      logAndUpdateState("ping success", noLog = true)
    } catch (e: Throwable) {
      // 记录心跳错误并断开连接
      logger.error("Ping error: ${e.message}", e)
      disconnect()
    }
  }
  
  // private fun waitForPong(): Boolean {
  //   val endTime = lastPingTime + PONG_TIMEOUT
  //   while (System.currentTimeMillis() < endTime) {
  //     if (pongMsgQueue.poll() != null) {
  //       return true
  //     }
  //     Thread.sleep(100) // 避免过度消耗 CPU
  //   }
  //   return false
  // }
  
  /**
   * 向服务器注册机器人信息
   * 此函数在机器人启动时调用，向服务器注册机器人的配置和网络信息
   * 它首先获取本地地址，然后构建注册请求数据，并通过客户端写入数据
   * 如果操作成功，则机器人成功注册，否则记录错误并断开连接
   *
   * @param tc TraceContext，用于追踪和调试的上下文
   */
  private fun registerToServer(client: TcpClient<RbkFrame>) {
    try {
      // 获取客户端的本地地址
      val localAddress = client.getNettyClient().getLocalAddress() as InetSocketAddress
      // 构建机器人信息的JSON字符串
      val body = JsonHelper.writeValueAsString(
        RobotRegister(mr.config.name, localAddress.hostString, localAddress.port),
      )
      // 构建注册请求数据
      val buf = RbkEncoder.buildReqBytes(REGISTER_API_NO, body, nextFlowNo())
      // 向服务器写入注册请求数据
      this.client?.write(buf)
    } catch (e: Throwable) {
      // 记录注册错误并断开连接
      logger.error("Error registering to server: ${e.message}", e)
      disconnect()
    }
  }
  
  /**
   * 清理 worker 中已完成的 future
   */
  private fun cleanWorker() {
    worker = worker.filter { !it.isDone && !it.isCancelled }
  }
}