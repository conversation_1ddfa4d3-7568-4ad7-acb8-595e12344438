package com.seer.trick.fleet.handler

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.base.MapToAnyNull
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.http.*
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.HttpServerManager.noAuth
import com.seer.trick.base.http.handler.MissingQueryParamError
import com.seer.trick.fleet.adapter.RobotRbkAdapter
import com.seer.trick.fleet.domain.RobotMapNameMd5
import com.seer.trick.fleet.service.*
import com.seer.trick.fleet.traffic.TrafficTaskStatus
import com.seer.trick.fleet.traffic.distributed.service.DistributedTrafficService
import com.seer.trick.helper.BoolHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.robot.RobotAppManager
import io.javalin.http.Context
import io.javalin.websocket.WsMessageContext
import org.slf4j.LoggerFactory

/**
 * 机器人相关 HTTP 和 WS 接口
 */
object RobotHandler : WebSocketSubscriber() {

  private val logger = LoggerFactory.getLogger(javaClass)

  fun registerHandlers() {
    WebSocketManager.subscribers += this

    val c = Handlers("api/fleet/robots")
    c.post("reconnect", ::reconnect, auth())
    c.post("off-duty", ::updateOffDuty, auth())
    c.get("all-all", ::queryReports, auth())
    c.get("all-legacy", ::listAllLegacy, noAuth())

    c.post("master", ::changeMaster, auth())

    // 透传，通过 HTTP 直接给机器人发送指令，便于调试 API，不对用户开放 ！！！
    c.post("request-directly", ::requestDirectly, auth())

    c.get("query-point", ::queryPoint, auth()) // 获取机器人当前点位
    c.get("query-group", ::queryGroup, auth())
    c.get("traffic-accepted", ::trafficAccepted, auth())
    c.post("plan-path", ::trafficPlanPath, auth())
    c.get("query-forward", ::queryForward, auth()) // 查询机器人是否在正走

    c.get("{sceneId}/maps/{robotName}", ::listRobotMaps, auth()) // 获取机器人当前所有地图
    c.post("{sceneId}/pull-maps/{check}", ::pullMapsFromRobot, auth())

    c.post("{sceneId}/clear-alarm", ::clearAlarm, auth()) // 清除机器人自身错误
    c.post("{sceneId}/set-soft-emc", ::setSoftEmc, auth()) // 设置机器人的软急停状态。
    c.post("{sceneId}/disabled", ::updateDisabled, auth())

    c.post("{sceneId}/update-robot-traffic", ::updateTrafficRobot, auth())
  }

  override fun onMessage(ctx: WsMessageContext, msg: WsMsg, env: WsEnv) {
    when (msg.action) {
      "Fleet3::AllRobotsUiReports::Query" -> onAllRobotsUiReports(ctx, msg)
    }
  }

  private fun onAllRobotsUiReports(ctx: WsMessageContext, msg: WsMsg) {
    val req: SceneIdReq = JsonHelper.mapper.readValue(msg.content!!, jacksonTypeRef())
    val sr = SceneService.mustGetSceneById(req.sceneId)
    val reports = RobotService.listRobotUiReports(sr)
    ctx.send(WsMsg.json("Fleet3::AllRobotsUiReports::Reply", reports, replyToId = msg.id))
  }

  private fun reconnect(ctx: Context) {
    val req: ReconnectReq = ctx.getReqBody()

    val sr = SceneService.mustGetSceneById(req.sceneId)
    val robots = sr.listRobots()

    for (rr in robots) {
      if (!req.robotNames.isNullOrEmpty() && !req.robotNames.contains(rr.robotName)) continue
      // 清自动重连次数
      rr.fetchFailureToAutoReconnectCounter = 0
      // 很快，所以串行即可
      RobotService.reconnect(rr, "from ui")
    }

    ctx.status(200)
  }

  data class ReconnectReq(val sceneId: String, val robotNames: List<String>? = null)

  private fun updateOffDuty(ctx: Context) {
    val req: UpdateOffDutyReq = ctx.getReqBody()
    val sr = SceneService.mustGetSceneById(req.sceneId)

    for (robotName in req.robotNames) {
      val rr = sr.mustGetRobot(robotName)
      RobotService.updateOffDuty(rr, req.offDuty)
    }

    ctx.status(200)
  }

  /**
   *  机器人在交管上下线
   */
  private fun updateTrafficRobot(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)

    val req: RobotNamesReq = ctx.getReqBody()
    for (robotName in req.robotNames) {
      val rr = sr.mustGetRobot(robotName)
      RobotService.updateTrafficRobot(rr)
    }
    ctx.status(200)
  }

  private fun queryReports(ctx: Context) {
    val sceneName = ctx.queryParam("sceneName")
    val sceneId = ctx.queryParam("sceneId")
    if (sceneName.isNullOrBlank() && sceneId.isNullOrBlank()) {
      throw BzError("errMissingHttpQueryParam", "sceneName|sceneId")
    }
    val sr = if (!sceneId.isNullOrBlank()) {
      SceneService.mustGetSceneById(sceneId)
    } else {
      SceneService.mustGetSceneByName(sceneName!!)
    }

    // TODO 暂不支持字段过滤
    val listRobotUiReports = RobotService.listRobotUiReports(sr)
    ctx.json(listRobotUiReports)
  }

  data class UpdateOffDutyReq(val sceneId: String, val robotNames: List<String>, val offDuty: Boolean = false)

  // 取 2 代调度，2.5 代，3 代的机器人信息，把共有的部分返回
  private fun listAllLegacy(ctx: Context) {
    val robots: MutableMap<String, RobotLegacy> = mutableMapOf()

    for (scene in RobotAppManager.scenes.values) {
      scene.tom?.listTomIdleRobots()?.forEach { r ->
        robots[r.id] = RobotLegacy(r.id, r.online, r.rbkReport)
      }
      scene.rachel?.listAllAll()?.forEach { r ->
        robots[r.id] = RobotLegacy(r.id, r.online, r.selfReport?.rawReport)
      }
    }

    for (sr in SceneService.listScenes()) {
      sr.listRobots().forEach { rr ->
        robots[rr.robotName] = RobotLegacy(rr.robotName, RobotService.isOnline(rr), rr.selfReport?.rawReport)
      }
    }

    ctx.json(robots)
  }

  data class RobotLegacy(val robotName: String, val online: Boolean, val rawReport: EntityValue?)

  private fun queryPoint(ctx: Context) {
    val rr = mustGetRobotRuntime(ctx)
    val point = RobotPoint(
      rr.selfReport?.main?.currentPoint,
      rr.selfReport?.main?.direction,
      rr.selfReport?.main?.x,
      rr.selfReport?.main?.y,
    )
    ctx.json(point)
  }

  data class RobotPoint(val pointName: String?, val direction: Double?, val x: Double?, val y: Double?)

  private fun queryGroup(ctx: Context) {
    val rr = mustGetRobotRuntime(ctx)
    ctx.json(rr.mustGetGroup())
  }

  private fun trafficAccepted(ctx: Context) {
    val rr = mustGetRobotRuntime(ctx)
    ctx.json(rr.pendingTrafficTask?.status == TrafficTaskStatus.TrafficAccepted)
  }

  private fun mustGetRobotRuntime(ctx: Context): RobotRuntime {
    val sceneId = ctx.queryParam("sceneId") ?: throw MissingQueryParamError("sceneId")
    val robotName = ctx.queryParam("robotName") ?: throw MissingQueryParamError("robotName")
    val sr = SceneService.mustGetSceneById(sceneId)
    return sr.robots[robotName] ?: throw BzError("errNoRobot")
  }

  data class TrafficPlanReq(
    val sceneId: String, // 场景 id
    val robotName: String, // 机器人名称
    val startPoint: String, // 起点名称
    val startX: Double, // 起点 x 坐标
    val startY: Double, // 起点 y 坐标
    val direction: Double, // 起点方向, 弧度
    val targetPoint: String, // 目标点名称
    val targetDir: Double?, // 目标点方向, 弧度
    val containerName: String? = null, // 容器名称
    val containerStartDirection: Double? = null, // 容器起点方向, 弧度
    val containerTargetDirection: Double? = null, // 容器目标点方向, 弧度
  )

  private fun trafficPlanPath(ctx: Context) {
    val req: TrafficPlanReq = ctx.getReqBody()
    val sr = SceneService.mustGetSceneById(req.sceneId)
    val rr = sr.mustGetRobot(req.robotName)
    val mapName = rr.selfReport?.stand?.areaId ?: throw BzError("areaIdError")
    val groupName = rr.mustGetGroup().name
    val path = DistributedTrafficService.planPath(
      robotName = req.robotName,
      groupName = groupName,
      mapName = mapName.toString(),
      sceneId = req.sceneId,
      startX = req.startX,
      startY = req.startY,
      startPoint = req.startPoint,
      direction = req.direction,
      targetPoint = req.targetPoint,
      targetDir = req.targetDir,
      containerName = req.containerName,
      containerStartDirection = req.containerStartDirection,
      containerTargetDirection = req.containerTargetDirection,
    )
    ctx.json(path)
  }

  /**
   * 查询机器人是否在正走
   * true：正走
   * false：倒走
   */
  private fun queryForward(ctx: Context) {
    val rr = mustGetRobotRuntime(ctx)
    val vx = rr.selfReport?.main?.velocity ?: 0.0
    ctx.json(vx >= 0.0)
  }

  data class ChangeMasterReq(val sceneId: String, val robotNames: List<String>, val on: Boolean = false)

  /**
   * 申请释放控制权
   */
  private fun changeMaster(ctx: Context) {
    val req: ChangeMasterReq = ctx.getReqBody()

    logger.info("Change robot master: $req")

    val sr = SceneService.mustGetSceneById(req.sceneId)

    // TODO 并行？
    for (robotName in req.robotNames) {
      val rr = sr.mustGetRobot(robotName)
      if (req.on) {
        RobotRbkAdapter.setRobotMaster(rr)
      } else {
        RobotRbkAdapter.unsetRobotMaster(rr)
      }
    }

    ctx.status(200)
  }

  data class RequestDirectlyReq(
    val sceneId: String,
    val robotName: String,
    val apiNo: Int,
    val body: MapToAnyNull,
    val timeout: Long? = null,
  )

  private fun requestDirectly(ctx: Context) {
    val req: RequestDirectlyReq = ctx.getReqBody()
    val sr = SceneService.mustGetSceneById(req.sceneId)
    val rr = sr.mustGetRobot(req.robotName)

    val fixedTimeout = req.timeout?.let { if (it < 3000) 5000 else it } ?: 5000
    val res = RobotRbkAdapter.request(rr, req.apiNo, JsonHelper.writeValueAsString(req.body), fixedTimeout)

    ctx.json(res)
  }

  private fun listRobotMaps(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val robotName = ctx.pathParam("robotName")

    val sr = SceneService.mustGetSceneById(sceneId)
    val rr = sr.mustGetRobot(robotName)

    val list = RobotService.listRobotMaps(rr)
    ctx.json(list)
  }

  /**
   * 从机器人拉取地图
   */
  private fun pullMapsFromRobot(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val check = BoolHelper.anyToBool(ctx.pathParam("check"))
    val sr = SceneService.mustGetSceneById(sceneId)

    val req: PullMapsFromRobotReq = ctx.getReqBody()

    val (noArcPath, errors) = SceneRobotSyncService.pullMapsFromRobot(sr, req.robotName, req.newMapByArea, check)
    ctx.json(mapOf("noArcPath" to noArcPath, "errors" to errors.joinToString("<br/>")))
  }

  data class PullMapsFromRobotReq(val robotName: String, val newMapByArea: Map<Int, RobotMapNameMd5>)

  /**
   * 清除机器人自身告警
   */
  private fun clearAlarm(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)
    val req: RobotNamesReq = ctx.getReqBody()
    for (robotName in req.robotNames) {
      val rr = sr.mustGetRobot(robotName)
      // 获取控制权
      RobotRbkAdapter.requestControl(rr)
      // 清除错误
      RobotRbkAdapter.clearAlarm(rr)
    }
  }

  data class RobotNamesReq(val robotNames: List<String>)

  /**
   * 设置机器人的软急停状态
   */
  private fun setSoftEmc(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)
    val req: SetSoftEmcReq = ctx.getReqBody()
    for (robotName in req.robotNames) {
      val rr = sr.mustGetRobot(robotName)
      RobotService.setSoftEmc(sr, rr, req.enable) // 通过 service 间接访问 adapter 吧。
    }

    ctx.status(200)
  }

  data class SetSoftEmcReq(val robotNames: List<String>, val enable: Boolean)

  private fun updateDisabled(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)
    val req: UpdateDisabledReq = ctx.getReqBody()

    RobotService.updateDisabled(sr, req.robotNames, req.disabled)

    ctx.status(200)
  }

  data class UpdateDisabledReq(val robotNames: List<String>, val disabled: Boolean)
}