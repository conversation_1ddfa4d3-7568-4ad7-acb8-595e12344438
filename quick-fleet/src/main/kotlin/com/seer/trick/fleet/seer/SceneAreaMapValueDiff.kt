package com.seer.trick.fleet.seer

import com.seer.trick.fleet.domain.MapPath
import com.seer.trick.fleet.domain.MapPoint
import com.seer.trick.fleet.domain.MapZone
import com.seer.trick.fleet.domain.SceneAreaMap
import com.seer.trick.fleet.domain.SceneBin

/**
 * 比较两个区域地图。m2 相对于 m1 的变化。 TODO 是否需要与 SceneAreaMapDiff 合并？
 * 元素值层面的比较
 */
class SceneAreaMapValueDiff(m1: SceneAreaMap, m2: SceneAreaMap) {

  /**
   * 新添加的点位
   */
  val addedPoints = mutableListOf<MapPoint>()

  /**
   * 删除的点位
   */
  val removedPoints = mutableListOf<MapPoint>()

  /**
   * 修改了的点位
   */
  val changedPoints = mutableListOf<MapPoint>()

  /**
   * 新添加的路径
   */
  val addedPaths = mutableListOf<MapPath>()

  /**
   * 删除的路径
   */
  val removedPaths = mutableListOf<MapPath>()

  /**
   * 修改了的路径
   */
  val changedPaths = mutableListOf<MapPath>()

  /**
   * 新添加的库位
   */
  val addedBins = mutableListOf<SceneBin>()

  /**
   * 删除的库位
   */
  val removedBins = mutableListOf<SceneBin>()

  /**
   * 修改了的库位
   */
  val changedBins = mutableListOf<SceneBin>()

  /**
   * 新添加的区块
   */
  val addedZones = mutableListOf<MapZone>()

  /**
   * 删除的区块
   */
  val removedZones = mutableListOf<MapZone>()

  /**
   * 修改了的区块
   */
  val changedZones = mutableListOf<MapZone>()

  private val pointMap1 = m1.points.associateBy { it.name }
  private val pointMap2 = m2.points.associateBy { it.name }

  private val pathMap1 = m1.paths.associateBy { it.key }
  private val pathMap2 = m2.paths.associateBy { it.key }

  private val binMap1 = m1.bins.associateBy { it.name }
  private val binMap2 = m2.bins.associateBy { it.name }

  private val zoneMap1 = m1.zones.associateBy { it.name }
  private val zoneMap2 = m2.zones.associateBy { it.name }

  init {
    diffPoints()
    diffPaths()
    diffBins()
    diffZones()
  }

  /**
   * 两个地图是否有变化
   */
  fun isAnyChanged() = addedPoints.isNotEmpty() ||
    removedPoints.isNotEmpty() ||
    changedPoints.isNotEmpty() ||
    addedPaths.isNotEmpty() ||
    removedPaths.isNotEmpty() ||
    changedPaths.isNotEmpty() ||
    addedBins.isNotEmpty() ||
    removedBins.isNotEmpty() ||
    changedBins.isNotEmpty() ||
    addedZones.isNotEmpty() ||
    removedZones.isNotEmpty() ||
    changedZones.isNotEmpty()

  /**
   * 比较点位差异
   */
  private fun diffPoints() {
    for (p2 in pointMap2.values) {
      val p1 = pointMap1[p2.name]
      if (p1 == null) {
        addedPoints += p2
      } else {
        // id、disabled 不写入 smap，所以不参与比较
        if (p1.copy(id = 0, disabled = false) != p2.copy(id = 0, disabled = false)) changedPoints += p2
      }
    }
    for (p1 in pointMap1.values) {
      val p2 = pointMap2[p1.name]
      if (p2 == null) removedPoints += p1
    }
  }

  /**
   * 比较路径差异
   */
  private fun diffPaths() {
    for (p2 in pathMap2.values) {
      val p1 = pathMap1[p2.key]
      if (p1 == null) {
        addedPaths += p2
      } else {
        // id、disabled 不写入 smap，所以不参与比较
        if (p1.copy(id = 0, disabled = false) != p2.copy(id = 0, disabled = false)) changedPaths += p2
      }
    }
    for (p1 in pathMap1.values) {
      val p2 = pathMap2[p1.key]
      if (p2 == null) removedPaths += p1
    }
  }

  /**
   * 比较库位差异
   */
  private fun diffBins() {
    for (b2 in binMap2.values) {
      val b1 = binMap1[b2.name]
      if (b1 == null) {
        addedBins += b2
      } else {
        // id、disabled 不写入 smap，所以不参与比较
        if (b1.copy(id = 0, disabled = false) != b2.copy(id = 0, disabled = false)) changedBins += b2
      }
    }
    for (b1 in binMap1.values) {
      val b2 = binMap2[b1.name]
      if (b2 == null) removedBins += b1
    }
  }

  private fun diffZones() {
    for (z2 in zoneMap2.values) {
      val z1 = zoneMap1[z2.name]
      if (z1 == null) {
        addedZones += z2
      } else {
        // id、disabled 不写入 smap，所以不参与比较
        if (z1.copy(id = 0, disabled = false) != z2.copy(id = 0, disabled = false)) changedZones += z2
      }
    }

    for (z1 in zoneMap1.values) {
      val z2 = zoneMap2[z1.name]
      if (z2 == null) removedZones += z1
    }
  }
}