package com.seer.trick.fleet.device.lift

import com.seer.trick.fleet.device.lift.adapter.LiftAdapter
import com.seer.trick.fleet.device.lift.adapter.LiftAdapterMock
import java.util.Date

class LiftRuntime(
  @Volatile
  var config: SceneLift,
) {

  @Volatile
  var adapter: LiftAdapter = LiftAdapterMock(config)

  /**
   * 设备是否在线
   */
  @Volatile
  var online: Boolean = false

  /**
   * 最后一次状态更新时间
   */
  @Volatile
  var lastStatusUpdateTime: Date = Date()

  /**
   * 设备是否故障
   */
  @Volatile
  var fault: Boolean = false

  /**
   * 设备故障信息
   */
  @Volatile
  var faultMsg: String = ""

  /**
   * 自动模式
   */
  @Volatile
  var autoMode: Boolean = true

  /**
   * 当前楼层，可能为空
   */
  @Volatile
  var currentFloor: String? = null

  /**
   * 目标楼层，可能为空
   */
  @Volatile
  var targetFloor: String? = null

  /**
   * 电梯的门的状态
   */
  @Volatile
  var doors: List<LiftDoorStatus> = emptyList()

  /**
   * 电梯里是否有人
   */
  @Volatile
  var people: Boolean = false

  /**
   * 使用电梯的机器人的名字
   */
  @Volatile
  var usingRobotName: String? = null

  /**
   * 机器人使用电梯的状态
   */
  @Volatile
  var usingStatus: RobotUsingStatus? = null

  fun toUiReport() = LiftUiReport(
    name = config.name,
    online = online,
    autoMode = autoMode,
    currentFloor = currentFloor,
    targetFloor = targetFloor,
    doors = doors,
    people = people,
    fault = fault,
    faultMsg = faultMsg,
    lastStatusUpdateTime = lastStatusUpdateTime,
    disabled = config.disabled,
  )

  override fun toString(): String = config.name
}