package com.seer.trick.fleet.order

import com.seer.trick.BzError
import com.seer.trick.fleet.algorithm.GreedyMatchingAlgorithm
import com.seer.trick.fleet.algorithm.KMMatchingAlgorithm
import com.seer.trick.fleet.algorithm.MatchingAlgorithm
import com.seer.trick.fleet.domain.DispatchMethod
import com.seer.trick.fleet.service.MapService
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.SceneRuntime
import org.slf4j.LoggerFactory
import java.util.Date

/**
 * 派单管理。将运单分给机器人。
 * 触发时机：1、定时的分派；2、一些特定的事件触发立即分派（如有新单创建）
 */
object DispatchOrderService {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 执行一次派单
   */
  fun dispatchOrders(sr: SceneRuntime) {
    sr.dispatchExecutor.submit {
      try {
        if (!sr.isEnabledInitializedNotEmc()) return@submit
        sr.matchingAlgorithm = updateMatchingAlgorithm(sr)
        val worker = DispatchOrderWorker(sr)
        worker.dispatch()
      } catch (e: Exception) {
        logger.error("dispatch orders", e)
      }
    }
  }

  /**
   * 更新匹配算法。配置变了才更新。
   */
  fun updateMatchingAlgorithm(sr: SceneRuntime): MatchingAlgorithm =
    if (sr.config.dispatchMethod == DispatchMethod.KM) {
      if (sr.matchingAlgorithm is KMMatchingAlgorithm) sr.matchingAlgorithm else KMMatchingAlgorithm()
    } else {
      if (sr.matchingAlgorithm is GreedyMatchingAlgorithm) sr.matchingAlgorithm else GreedyMatchingAlgorithm()
    }

  /**
   * 估测机器人执行运单的成本。
   * 大于等于 0 为成本值,不可达返回 -1 。如没有给出目标位置，无法估测成本,抛异常。
   */
  fun estimateOrderCost(or: OrderRuntime, rr: RobotRuntime): Double {
    // 对于不能二次分派的运单，此时不应该以机器人当前的位置来进行计算，而应该以运单的最后位置来进行计算。
    // 但机器人可能是多储位的，虽然当前运单不能被更换机器人，但不代表着不能接其他的运单。
    // 而这里的 cost 决定多储位机器是否拼单，如果第二个单取货点是在第一个单取货点附近，此时计算的应该机器人当前的位置
    // 而假如第二个单在第一个单终点附近，此时计算的应该是机器人终点的位置。亦或者说什么时候以当前正在执行的步骤位置计算？
    // 首先这个步骤必须是不可打断的，才以当前执行的步骤位置进行计算
    // 背篓满的情况。不参与计算不考虑
    // 背篓有空的情况，按照根据当前步骤以及当前运单是否可打断进行判断，假如不可打断，就以步骤终点进行计算，假如可打断，以机器人当前位置进行计算 (顺风车应该怎么办呢？)
    // 背篓被预定的情况下，运单是可以被更改的，那其实应该得按照机器人的当前位置来判断

    // 运单要求机器人去哪
    // 优先用 keyLocations
    var toLocation = or.order.keyLocations?.firstOrNull()
    if (toLocation.isNullOrBlank() && or.steps.isNotEmpty()) toLocation = or.steps[0].location
    if (toLocation.isNullOrBlank()) throw BzError("errNoKeyLocations")

    // 不应该发生
    val fromPoint =
      rr.startPointNameForDispatching
        ?: throw IllegalStateException("can not get startPointNameForDispatching for robot ${rr.robotName}")

    return MapService.getShortestPathCostOfCrossAreas(rr, fromPoint, toLocation)
  }

  /**
   * 排前面的优先处理。
   */
  fun compareOrders(
    sr: SceneRuntime,
    priority1: Int,
    cost1: Double,
    createdOn1: Date,
    priority2: Int,
    cost2: Double,
    createdOn2: Date,
  ): Int {
    val sort = sr.config.decideDispatchSort()
    for (factor in sort) {
      if (factor == "Priority") {
        val o = priority2.compareTo(priority1) // 优先级大的排前面
        if (o != 0) return o
      } else if (factor == "Cost") {
        val o = cost1.compareTo(cost2)
        if (o != 0) return o
      } else if (factor == "CreatedOn") {
        val o = createdOn1.time.compareTo(createdOn2.time)
        if (o != 0) return o
      } else if (factor == "AcceptableTimeout") {
        // 超过派单等待时间的部分排序
        val dispatchAcceptableTimeout = sr.config.dispatchAcceptableTimeout
        var t1 = System.currentTimeMillis() - createdOn1.time - dispatchAcceptableTimeout * 1000
        if (t1 < 0) t1 = 0
        var t2 = System.currentTimeMillis() - createdOn2.time - dispatchAcceptableTimeout * 1000
        if (t2 < 0) t2 = 0

        val o = t2.compareTo(t1) // 等待时间长的排前面
        if (o != 0) return o
      }
    }
    return 0
  }
}

data class DispatchProfile(
  val loop: Long = 0,
  var ordersPass: Int = 0, // 潜在可分派
  var workableRobotsNum: Int = 0, // 潜在可接单
  var preNum: Int = 0, // 预分配组合
  var withdrawnNum: Int = 0, // 撤单数量
  var allocationNum: Int = 0, // 最终成功分派数
  var timeCost: Long = 0, // 总耗时
)