package com.seer.trick.fleet.traffic.distributed.deadlock.helper

import com.seer.trick.fleet.traffic.distributed.context.domain.RobotContext
import com.seer.trick.fleet.traffic.distributed.context.domain.RobotMotionType
import com.seer.trick.fleet.traffic.distributed.deadlock.model.NeighborDomain
import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper
import com.seer.trick.fleet.traffic.distributed.helper.LogParseHelper
import com.seer.trick.fleet.traffic.distributed.lock.base.RobotContainerSpaceLock
import com.seer.trick.fleet.traffic.distributed.map.*
import com.seer.trick.fleet.traffic.distributed.plan.model.PathAction
import com.seer.trick.fleet.traffic.distributed.plan.model.PathType
import org.slf4j.LoggerFactory

/**
 *  邻域搜索管理
 * */
object NeighborSearchService {

  private val logger = LoggerFactory.getLogger(javaClass)

  fun process(context: RobotContext, map: MutableMap<String, NeighborDomain>): NeighborDomain {
    if (map.containsKey(context.robotName)) return map[context.robotName]!!
    val neighbors = findNeighbors(context)
    map[context.robotName] = neighbors
    logger.info("${context.robotName} neighbors: ${LogParseHelper.parseNeighborLog(neighbors)}")
    return neighbors
  }

  private fun findNeighbors(context: RobotContext): NeighborDomain = if (checkRobotInPoint(context)) {
    findNeighborInPoint(context)
  } else {
    findNeighborInLine(context)
  }

  private fun checkRobotInPoint(context: RobotContext): Boolean {
    val curPoint = context.baseDomain.curPoint
    if (curPoint != null) {
      val point =
        MapService.findPointByName(context.sceneId, context.mapName, context.groupName, curPoint.pointName)
      if (point.distance(curPoint) < 0.1) {
        return true
      }
    }
    return false
  }

  /**
   *  在线上
   *  弧线不支持在线上解死锁
   * */
  private fun findNeighborInLine(context: RobotContext): NeighborDomain {
    // 定位所在的线 todo 起始位置
    val action =
      context.plan.queryCurrentAction() ?: firstActionInLine(context)
        ?: throw RuntimeException("${context.robotName} current action is null")
    val lineName = action.lineName
    logger.info("${context.robotName} find neighbor in line $lineName")
    val line = MapService.findLineByName(context.sceneId, action.mapName, action.groupName, lineName)
    if (line.type == LineType.THREE_BEZIER || line.type == LineType.LUMINANCE_CURVE) {
      return NeighborDomain(context.robotName)
    }
    val curPoint = context.baseDomain.curPoint ?: return NeighborDomain(context.robotName)
    // 获取当前角度
    val robotHeading = context.baseDomain.robotHeading
    val neighborDomain = NeighborDomain(context.robotName)
    // 前进
    if (line.robotCanPass(context.baseDomain.isLoading(), robotHeading, context.robotMotionType) == true &&
      DeadLockHelper.canPass(context, line)
    ) {
      val pathAction = PathAction(
        robotName = context.robotName,
        index = 0,
        type = action.type,
        mapName = action.mapName,
        groupName = context.groupName,
        targetX = line.end.x,
        targetY = line.end.y,
        start = line.start.copy(curPoint.x, curPoint.y),
        target = line.end,
        lineName = lineName,
        robotInHeading = robotHeading,
        robotOutHeading = robotHeading,
        containerName = action.containerName,
        containerInHeading = action.containerInHeading,
        containerOutHeading = action.containerOutHeading,
      )
      if (PathCollisionHelper.pathCollision(context.sceneId, pathAction, mutableListOf())) {
        neighborDomain.cantPass.add(line.end.pointName)
      } else {
        neighborDomain.canPass.add(pathAction)
        // 看下一个点是否可以旋转
        val rotate = PathAction(
          robotName = context.robotName,
          index = 0,
          type = PathType.ROTATE,
          mapName = action.mapName,
          groupName = context.groupName,
          targetX = line.end.x,
          targetY = line.end.y,
          start = line.end,
          target = line.end,
          lineName = lineName,
          robotInHeading = robotHeading,
          robotOutHeading = AngleHelper.processAngle(robotHeading + AngleHelper.RIGHT_ANGLE),
          containerName = action.containerName,
          containerInHeading = action.robotInHeading,
          containerOutHeading = action.robotOutHeading,
        )
        if (PathCollisionHelper.pathCollision(context.sceneId, rotate, mutableListOf())) {
          neighborDomain.cantRotate.add(line.end.pointName)
        }
      }
    }
    // 后退
    val revLine = MapService.findLineByPoints(
      context.sceneId,
      action.mapName,
      action.groupName,
      line.end.pointName,
      line.start.pointName,
    )
    if (revLine != null &&
      line.rotate &&
      revLine.robotCanPass(
        context.baseDomain.isLoading(),
        AngleHelper.processAngle(robotHeading + AngleHelper.DOWN_ANGLE),
        context.robotMotionType,
      ) &&
      DeadLockHelper.canPass(context, revLine) &&
      revLine.end.avoid
    ) {
      val point = MapService.findPointByName(context.sceneId, action.mapName, action.groupName, curPoint.pointName)
      val rotate = PathAction(
        robotName = context.robotName,
        index = 0,
        type = PathType.ROTATE,
        mapName = action.mapName,
        groupName = context.groupName,
        targetX = curPoint.x,
        targetY = curPoint.y,
        start = curPoint,
        target = point,
        lineName = revLine.lineName,
        robotInHeading = robotHeading,
        robotOutHeading = AngleHelper.processAngle(robotHeading + AngleHelper.DOWN_ANGLE),
        containerName = action.containerName,
        containerInHeading = action.containerInHeading,
        containerOutHeading = action.containerOutHeading,
      )
      if (!PathCollisionHelper.pathCollision(context.sceneId, rotate, mutableListOf())) {
        neighborDomain.canRotate.add(rotate)
        val pathAction = PathAction(
          robotName = context.robotName,
          index = 0,
          type = PathType.STRAIGHT,
          mapName = action.mapName,
          groupName = context.groupName,
          targetX = revLine.end.x,
          targetY = revLine.end.y,
          start = revLine.start.copy(curPoint.x, curPoint.y),
          target = revLine.end,
          lineName = revLine.lineName,
          robotInHeading = AngleHelper.processAngle(robotHeading + AngleHelper.DOWN_ANGLE),
          robotOutHeading = AngleHelper.processAngle(robotHeading + AngleHelper.DOWN_ANGLE),
          containerName = action.containerName,
          containerInHeading = action.robotInHeading,
          containerOutHeading = action.robotOutHeading,
        )
        if (PathCollisionHelper.pathCollision(context.sceneId, pathAction, mutableListOf())) {
          neighborDomain.cantPass.add(revLine.end.pointName)
        } else {
          neighborDomain.canPass.add(pathAction)
          // 看下一个点是否可以旋转
          val rotate0 = PathAction(
            robotName = context.robotName,
            index = 0,
            type = PathType.ROTATE,
            mapName = action.mapName,
            groupName = context.groupName,
            targetX = revLine.end.x,
            targetY = revLine.end.y,
            start = revLine.end,
            target = revLine.end,
            lineName = revLine.lineName,
            robotInHeading = AngleHelper.processAngle(robotHeading + AngleHelper.DOWN_ANGLE),
            robotOutHeading = AngleHelper.processAngle(robotHeading + AngleHelper.RIGHT_ANGLE),
            containerName = action.containerName,
            containerInHeading = action.robotInHeading,
            containerOutHeading = action.robotOutHeading,
          )
          if (PathCollisionHelper.pathCollision(context.sceneId, rotate0, mutableListOf())) {
            neighborDomain.cantRotate.add(revLine.end.pointName)
          }
        }
      }
    }
    // 倒退
    if (line.start.avoid) {
      val back = PathAction(
        robotName = context.robotName,
        index = 0,
        type = action.type,
        mapName = action.mapName,
        groupName = context.groupName,
        targetX = line.start.x,
        targetY = line.start.y,
        start = line.end.copy(curPoint.x, curPoint.y),
        target = line.start,
        lineName = lineName,
        robotInHeading = robotHeading,
        robotOutHeading = robotHeading,
        containerName = action.containerName,
        containerInHeading = action.containerInHeading,
        containerOutHeading = action.containerOutHeading,
      )
      if (!PathCollisionHelper.pathCollision(context.sceneId, back, mutableListOf())) {
        neighborDomain.backPass = back
        // 看下一个点是否可以旋转
        val rotate = PathAction(
          robotName = context.robotName,
          index = 0,
          type = PathType.ROTATE,
          mapName = action.mapName,
          groupName = context.groupName,
          targetX = line.start.x,
          targetY = line.start.y,
          start = line.start,
          target = line.start,
          lineName = lineName,
          robotInHeading = robotHeading,
          robotOutHeading = AngleHelper.processAngle(robotHeading + AngleHelper.RIGHT_ANGLE),
          containerName = action.containerName,
          containerInHeading = action.containerInHeading,
          containerOutHeading = action.containerOutHeading,
        )
        if (PathCollisionHelper.pathCollision(context.sceneId, rotate, mutableListOf())) {
          neighborDomain.cantRotate.add(line.start.pointName)
        }
      }
    }
    return neighborDomain
  }

  private fun firstActionInLine(context: RobotContext): PathAction? {
    val plan = context.plan
    if (plan.index == -1L && plan.allocateIndex == -1L) {
      return plan.path.first()
    }
    return null
  }

  /**
   *  在点上
   * */
  private fun findNeighborInPoint(context: RobotContext): NeighborDomain {
    val curPosition = context.baseDomain.curPoint ?: throw RuntimeException("current point is null")
    logger.info("${context.robotName} find neighbor in point ${curPosition.pointName}")
    val robotHeading = context.baseDomain.robotHeading
    val containerHeading = context.baseDomain.containerHeading
    val loading = context.baseDomain.isLoading()
    val dynamic = context.robotSalverNotRotate
    val containerName = context.baseDomain.containerType
    val neighborDomain = NeighborDomain(context.robotName)
    val curPoint =
      MapService.findPointByName(context.sceneId, context.mapName, context.groupName, curPosition.pointName)

    val narrowDir = if (containerName != null) {
      RobotContainerSpaceLock
        .queryContainerModelNarrowDir(sceneId = context.sceneId, containerName)
    } else {
      0
    }

    val lines = curPoint.toLines
    for (line in lines) {
      // 判断是否可以通行
      if (!DeadLockHelper.canPass(context, line)) {
        continue
      }
      if (context.robotMotionType != RobotMotionType.OMNI && DeadLockHelper.pointNotRotate(line, robotHeading)) {
        continue
      }
      logger.debug("find neighbor line ${line.lineName}")
      // 检测倒车
      if (line.start.avoid && AngleHelper.opposeAngle(robotHeading, line.enterDir)) {
        val backAction = PathAction(
          robotName = context.robotName,
          index = 0,
          type = coverLineType(line),
          mapName = context.mapName,
          groupName = context.groupName,
          targetX = line.end.x,
          targetY = line.end.y,
          start = line.start,
          target = line.end,
          lineName = line.lineName,
          robotInHeading = robotHeading,
          robotOutHeading = robotHeading,
          containerName = context.baseDomain.containerType,
          containerInHeading = containerHeading,
          containerOutHeading = containerHeading,
        )
        if (PathCollisionHelper.pathCollision(context.sceneId, backAction, mutableListOf())) {
          neighborDomain.cantPass.add(line.end.pointName)
        } else {
          neighborDomain.backPass = backAction
          val rotate = PathAction(
            robotName = context.robotName,
            index = 0,
            type = PathType.ROTATE,
            mapName = context.mapName,
            groupName = context.groupName,
            targetX = line.end.x,
            targetY = line.end.y,
            start = line.end,
            target = line.end,
            lineName = line.lineName,
            robotInHeading = robotHeading,
            robotOutHeading = AngleHelper.processAngle(robotHeading + AngleHelper.RIGHT_ANGLE),
            containerName = context.baseDomain.containerType,
            containerInHeading = containerHeading,
            containerOutHeading = containerHeading,
          )
          if (PathCollisionHelper.pathCollision(context.sceneId, rotate, mutableListOf())) {
            neighborDomain.cantRotate.add(line.end.pointName)
          }
        }
      }
      // 旋转 or 直行
      var robotHeading0 = robotHeading
      var containerHeading0 = containerHeading
      if (!line.robotCanPass(context.baseDomain.isLoading(), robotHeading, context.robotMotionType)) {
        if (neighborDomain.cantRotate.contains(curPoint.pointName)) continue
        val headings = line.findHeadings(context.robotMotionType)
        if (headings.isEmpty()) {
          continue
        }
        robotHeading0 = headings[0]
        if (loading && dynamic) {
          containerHeading0 = AngleHelper.processAngle(containerHeading + robotHeading0 - robotHeading)
        } else if (loading &&
          line.start.type == PointType.ROTATE &&
          line.narrowPath &&
          !AngleHelper.sameAngleInFiveDegree(
            containerHeading0,
            AngleHelper.processAngle(line.enterDir + narrowDir),
          ) &&
          !AngleHelper.sameAngleInFiveDegree(
            AngleHelper.processAngle(containerHeading0 + AngleHelper.DOWN_ANGLE),
            AngleHelper.processAngle(line.outDir + narrowDir),
          )
        ) {
          containerHeading0 = AngleHelper.processAngle(line.enterDir + narrowDir)
        } else if (loading &&
          line.start.type == PointType.ROTATE &&
          line.containerDir != null &&
          !AngleHelper.sameAngleInFiveDegree(containerHeading0, line.containerDir)
        ) {
          containerHeading0 = line.containerDir
        }

        if (!AngleHelper.sameAngleInFiveDegree(containerHeading, containerHeading0)) {
          val containerRotate = PathAction(
            robotName = context.robotName,
            index = 0,
            type = PathType.TURNING,
            mapName = context.mapName,
            groupName = context.groupName,
            targetX = curPoint.x,
            targetY = curPoint.y,
            start = curPoint,
            target = curPoint,
            lineName = line.lineName,
            robotInHeading = robotHeading,
            robotOutHeading = robotHeading0,
            containerName = context.baseDomain.containerType,
            containerInHeading = containerHeading,
            containerOutHeading = containerHeading0,
          )
          if ((!line.start.rotate && !AngleHelper.sameAngleInFiveDegree(robotHeading, robotHeading0)) ||
            PathCollisionHelper.pathCollision(context.sceneId, containerRotate, mutableListOf())
          ) {
            continue
          }
          neighborDomain.canRotate.add(containerRotate)
        }

        if (!AngleHelper.sameAngleInFiveDegree(robotHeading, robotHeading0) &&
          neighborDomain.canRotate.size <= 0
        ) {
          val rotate = PathAction(
            robotName = context.robotName,
            index = 0,
            type = PathType.ROTATE,
            mapName = context.mapName,
            groupName = context.groupName,
            targetX = curPoint.x,
            targetY = curPoint.y,
            start = curPoint,
            target = curPoint,
            lineName = line.lineName,
            robotInHeading = robotHeading,
            robotOutHeading = robotHeading0,
            containerName = context.baseDomain.containerType,
            containerInHeading = containerHeading,
            containerOutHeading = containerHeading0,
          )
          if (!line.start.rotate || PathCollisionHelper.pathCollision(context.sceneId, rotate, mutableListOf())) {
            neighborDomain.cantRotate.add(curPoint.pointName)
            continue
          }
          neighborDomain.canRotate.add(rotate)
        }
      }
      val pathAction = PathAction(
        robotName = context.robotName,
        index = 0,
        type = coverLineType(line),
        mapName = context.mapName,
        groupName = context.groupName,
        targetX = line.end.x,
        targetY = line.end.y,
        start = line.start,
        target = line.end,
        lineName = line.lineName,
        robotInHeading = robotHeading0,
        robotOutHeading = AngleHelper.processAngle(robotHeading0 + line.angleDeviation()),
        containerName = context.baseDomain.containerType,
        containerInHeading = containerHeading0,
        containerOutHeading = if (loading) {
          AngleHelper.processAngle(containerHeading0 + line.angleDeviation())
        } else {
          containerHeading0
        },
      )
      if (PathCollisionHelper.pathCollision(context.sceneId, pathAction, mutableListOf())) {
        neighborDomain.cantPass.add(line.end.pointName)
      } else {
        neighborDomain.canPass.add(pathAction)
        val nextRotate = PathAction(
          robotName = context.robotName,
          index = 0,
          type = PathType.ROTATE,
          mapName = context.mapName,
          groupName = context.groupName,
          targetX = line.end.x,
          targetY = line.end.y,
          start = line.end,
          target = line.end,
          lineName = line.lineName,
          robotInHeading = robotHeading0,
          robotOutHeading = AngleHelper.processAngle(robotHeading + AngleHelper.RIGHT_ANGLE),
          containerName = context.baseDomain.containerType,
          containerInHeading = containerHeading0,
          containerOutHeading = containerHeading0,
        )
        if (PathCollisionHelper.pathCollision(context.sceneId, nextRotate, mutableListOf())) {
          neighborDomain.cantRotate.add(line.end.pointName)
        }
      }
    }
    return neighborDomain
  }

  private fun coverLineType(line: Line): PathType = if (line.type == LineType.STRAIGHT) {
    PathType.STRAIGHT
  } else if (line.type == LineType.THREE_BEZIER) {
    PathType.ARC
  } else if (line.type == LineType.LUMINANCE_CURVE) {
    PathType.CURVE
  } else {
    PathType.STRAIGHT
  }
}