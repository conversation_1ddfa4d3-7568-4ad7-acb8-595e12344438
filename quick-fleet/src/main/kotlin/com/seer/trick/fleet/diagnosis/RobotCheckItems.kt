package com.seer.trick.fleet.diagnosis

import CheckItem
import CheckReq
import CheckResult
import com.seer.trick.BzError
import com.seer.trick.base.concurrent.PollingJobManager
import com.seer.trick.fleet.domain.OrderKind
import com.seer.trick.fleet.domain.RobotAlarmLevel
import com.seer.trick.fleet.domain.RobotAutoOrderType
import com.seer.trick.fleet.domain.RobotBinStatus
import com.seer.trick.fleet.domain.TrafficMethod
import com.seer.trick.fleet.order.*
import com.seer.trick.fleet.service.MapService
import com.seer.trick.fleet.service.MapService.NOT_ACHIEVABLE_COST
import com.seer.trick.fleet.service.RobotService
import com.seer.trick.fleet.traffic.venus.VenusTrafficService
import fail
import pass

object RobotCheckItems {

  /**
   * 机器人暂停停靠
   */
  object RobotParkingPaused : CheckItem {
    override val name = "RobotParkingPaused"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      return if (!checkReq.rr.sr.config.parkingPaused) {
        pass()
      } else {
        fail("RobotParkingPaused")
      }
    }
  }

  /**
   * 机器人是否禁用
   */
  object RobotDisabled : CheckItem {
    override val name = "RobotDisabled"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      return if (!checkReq.rr.config.disabled) {
        pass()
      } else {
        fail("RobotDisabled")
      }
    }
  }

  /**
   * 机器人是否在线
   */
  object RobotOffline : CheckItem {
    override val name = "RobotOffline"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      return if (RobotService.isOnline(checkReq.rr)) pass() else fail("RobotOffline")
    }
  }

  /**
   * 机器人是否停止接单
   */
  object RobotOffDuty : CheckItem {
    override val name = "RobotOffDuty"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      return if (!checkReq.rr.offDuty) pass() else fail("RobotOffDuty")
    }
  }

  /**
   * 机器人是否有控制权
   */
  object RobotNotMaster : CheckItem {
    override val name = "RobotNotMaster"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      return if (checkReq.rr.selfReport?.main?.controlledByFleet == true) pass() else fail("RobotNotMaster")
    }
  }

  /**
   * 机器人是否交管初始化完成
   */
  object TrafficNotReady : CheckItem {
    override val name = "TrafficNotReady"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      return if (checkReq.rr.trafficReady) pass() else fail("TrafficNotReady")
    }
  }

  /**
   * 机器人是否故障
   */
  object RobotFailed : CheckItem {
    override val name = "RobotFailed"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      val alarms = checkReq.rr.selfReport?.main?.alarms
      val alarm = alarms?.find { it.level == RobotAlarmLevel.Fatal || it.level == RobotAlarmLevel.Error }

      // 当开启了机器人上报 Error 或 Fatal 错误时，暂停机器人派单
      if (checkReq.rr.sr.config.robotPlanPausedOnSelfReportErrorOrFatal && alarm != null) {
        return fail("RobotSelfFailed", listOf(alarm.message))
      }

      val sec = checkReq.rr.executingStep
      if (sec != null && sec.fault) {
        return fail("RobotFailed", listOf(sec.faultMsg ?: ""))
      }
      return pass()
    }
  }

  /**
   * 判断机器人是否有可用的停靠点
   */
  object RobotParkPointAvailable : CheckItem {
    override val name = "RobotParkPointAvailable"
    override fun check(checkReq: CheckReq): CheckResult {
      val rr = checkReq.rr
      if (rr == null) return fail("RobotIsNull")
      if (checkReq.fromPointName.isNullOrBlank()) return fail("FromPointNullOrBlank")
      // 仅诊断的时候需要判断
      if (!checkReq.fullDiagnosis) return pass()

      // 判断整个场景有没有可用的停靠点
      val parkAndChargeResourceHelper = ParkAndChargeResourceHelper(rr.sr)
      val availableParkPoints = parkAndChargeResourceHelper.listAvailableParkingPoints(rr)
      if (availableParkPoints.isEmpty()) {
        fail("RobotNoAvailableParkPoint")
      }

      // 判断有没有当前机器人可达的停靠点
      val costs = mutableListOf<Double>()
      for (location in availableParkPoints) {
        val cost = try {
          val result = MapService.getShortestPathCostOfCrossAreas(rr, checkReq.fromPointName, location)
          if (result == NOT_ACHIEVABLE_COST) continue else result
        } catch (_: BzError) {
          continue
        }
        costs += cost
      }
      if (costs.isEmpty()) return fail("RobotNoAvailableParkPoint")

      val rejectList = mutableListOf<String>()
      for (it in availableParkPoints) {
        val reason = parkAndChargeResourceHelper.checkParkAndChargeCollision(rr, it)
        if (reason != null) {
          rejectList.add(reason)
          continue
        } else {
          return pass()
        }
      }
      return fail("RobotNoAvailableParkPointByCollision", rejectList)
    }
  }

  /**
   * 判断机器人是否有可用的充电点
   */
  object RobotChargingPointAvailable : CheckItem {
    override val name = "RobotChargingPointAvailable"
    override fun check(checkReq: CheckReq): CheckResult {
      val rr = checkReq.rr
      if (rr == null) return fail("RobotIsNull")
      if (checkReq.fromPointName.isNullOrBlank()) return fail(("FromPointNullOrBlank"))
      // 仅诊断的时候需要判断
      if (!checkReq.fullDiagnosis) return pass()

      // 判断有没有可用的
      val parkAndChargeResourceHelper = ParkAndChargeResourceHelper(rr.sr)
      val availableChargingPoints = parkAndChargeResourceHelper.listAvailableChargingPoints(rr)
      if (availableChargingPoints.isEmpty()) {
        fail("RobotNoAvailableChargingPoint")
      }

      // 判断有没有当前机器人可达的充电点
      val costs = mutableListOf<Double>()
      for (location in availableChargingPoints) {
        val cost = try {
          val result = MapService.getShortestPathCostOfCrossAreas(rr, checkReq.fromPointName, location)
          if (result == NOT_ACHIEVABLE_COST) continue else result
        } catch (_: BzError) {
          continue
        }
        costs += cost
      }
      if (costs.isEmpty()) fail("RobotNoAvailableChargingPoint")

      val rejectList = mutableListOf<String>()
      for (it in availableChargingPoints) {
        val reason = parkAndChargeResourceHelper.checkParkAndChargeCollision(rr, it)
        if (reason != null) {
          rejectList.add(reason)
          continue
        } else {
          return pass()
        }
      }
      return fail("RobotNoAvailableChargingPointByCollision", rejectList)
    }
  }

  /**
   * 机器人是否正在充电,且未充电足够时间。避免刚充电就去接单
   */
  object RobotChargingNotEnoughTime : CheckItem {
    override val name = "RobotChargingNotEnoughTime"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      return if (!ChargingService.justCharging(checkReq.rr)) {
        pass()
      } else {
        fail("RobotChargingNotEnoughTime")
      }
    }
  }

  /**
   * 充电但没充到 FULL
   * 如果没充到 FULL 则校验不通过
   */
  object RobotChargingNotFull : CheckItem {
    override val name = "RobotChargingNotFull"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      return if (ChargingService.isChargingRobust(checkReq.rr) && !ChargingService.canStopCharge(checkReq.rr)) {
        fail("RobotChargingNotFull")
      } else {
        pass()
      }
    }
  }

  /**
   * 机器人电量是否高于 chargeOnly
   * 如果低于则校验不通过
   */
  object RobotBatteryExceedChargeOnly : CheckItem {
    override val name = "RobotBatteryExceedChargeOnly"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      val cc = ChargingService.getConfigOfRobot(checkReq.rr)
      val battery = ChargingService.getBatteryOrZero(checkReq.rr)

      return if (battery >= cc.chargeOnly) {
        pass()
      } else {
        fail("RobotBatteryExceedChargeOnly", listOf("battery: ${battery * 100}%,chargeOnly: ${cc.chargeOnly * 100}%"))
      }
    }
  }

  object RobotForceCharging : CheckItem {
    override val name = "RobotForceCharging"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      return if (checkReq.rr.forcedCharging) {
        fail("RobotForceCharging")
      } else {
        pass()
      }
    }
  }

  /**
   *  正在执行自动单，包括充电、停靠
   *  如果正在执行则校验不通过
   */
  object RobotAutoOrder : CheckItem {
    override val name = "RobotAutoOrder"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      val autoOrder = checkReq.rr.autoOrder

      return if (autoOrder == null) {
        pass()
      } else {
        fail("RobotAutoOrder")
      }
    }
  }

  /**
   * 校验机器人是否已经在停靠点了
   * 如果已经在则校验不通过，无需停靠
   */
  object RobotAlreadyParked : CheckItem {
    override val name = "RobotAlreadyParked"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      val rr = checkReq.rr
      val mapCache = rr.sr.mapCache
      val pc = mapCache.getPointCacheByStand(rr)
      val parkAllowed = if (pc != null) mapCache.isParkAllowed(pc) else false

      return if (parkAllowed) {
        fail("RobotAlreadyParked")
      } else {
        pass()
      }
    }
  }

  /**
   * 需要空闲一段时间才去停靠
   */
  object RobotWaitIdleTimeoutToPark : CheckItem {
    override val name = "RobotWaitIdleTimeoutToPark"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      val idleFrom = checkReq.rr.idleFrom
      if (!(idleFrom != null && System.currentTimeMillis() - idleFrom.time > 1000 * 5)) {
        return fail("RobotWaitIdleTimeoutToPark")
      }
      return pass()
    }
  }

  /**
   * 校验机器人是否有多余的储位
   * 如果没有多余的储位则校验不通过
   */
  object RobotNoMoreBin : CheckItem {
    override val name = "RobotNoMoreBin"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      for (it in checkReq.rr.bins) {
        if (it.status == RobotBinStatus.Empty) {
          return pass()
        }
        if (it.status == RobotBinStatus.Reserved) {
          // 对于状态为预的运单，需要判断这个运单是否可以重分派
          val or = checkReq.rr.sr.orders[it.orderId] ?: continue
          if (RobotOrderPrediction.checkOrderBeAllocatedOrAllocatedAgain(or) == null) {
            // 储位上状态为预的运单可以重分派
            return pass()
          }
        }
      }
      return fail("RobotNoMoreBin")
    }
  }

  /**
   * 机器人是否正在执行业务单
   * 如果在执行业务单则校验不通过
   */
  object RobotBusy : CheckItem {
    override val name = "RobotBusy"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      val hasBzOrder = checkReq.rr.orders.isNotEmpty() &&
        checkReq.rr.orders.any { it.value.order.kind == OrderKind.Business }

      return if (!hasBzOrder) {
        pass()
      } else {
        fail("RobotBusy")
      }
    }
  }

  /**
   * 机器人电量是否低于 chargeOnly,如果低于则需要强充
   * 大于 chargeOnly 则校验不通过
   */
  object RobotBatteryBelowChargeOnly : CheckItem {
    override val name = "RobotBatteryBelowChargeOnly"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      val cc = ChargingService.getConfigOfRobot(checkReq.rr)
      val battery = ChargingService.getBatteryOrZero(checkReq.rr)

      return if (!ChargingService.forceCharge(checkReq.rr)) {
        fail("RobotBatteryBelowChargeOnly", listOf("battery: ${battery * 100}%,chargeOnly: ${cc.chargeOnly * 100}%"))
      } else {
        pass() // 注意：这里是通过，因为必须充电
      }
    }
  }

  /**
   * 机器人电量大于 chargeNeed，如果大于则不会生成充电任务
   * 大于 chargeNeed 则校验不通过
   */
  object RobotBatteryBelowChargeNeed : CheckItem {
    override val name = "RobotBatteryBelowChargeNeed"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      val cc = ChargingService.getConfigOfRobot(checkReq.rr)
      val battery = ChargingService.getBatteryOrZero(checkReq.rr)

      return if (!ChargingService.needCharge(checkReq.rr)) {
        // 电量大于可以充电的电量
        fail("RobotBatteryBelowChargeNeed", listOf("battery: ${battery * 100}%,chargeOnly: ${cc.chargeNeed * 100}%"))
      } else {
        pass() // 通过表示可以充电
      }
    }
  }

  /**
   * 机器人是否禁用自动充电
   */
  object RobotChargingPaused : CheckItem {
    override val name = "RobotChargingPaused"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      if (checkReq.rr.sr.config.chargingPaused) {
        return fail("RobotChargingPaused")
      }
      return pass()
    }
  }

  /**
   * 校验机器人是否正在去充电，如果正在去充电则校验不通过
   */
  object RobotToCharging : CheckItem {
    override val name = "RobotToCharging"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      if (checkReq.rr.autoOrder?.type == RobotAutoOrderType.Charging) {
        return fail("RobotToCharging")
      }
      return pass()
    }
  }

  /**
   * 机器人是否正在充电,如果正在充电则校验不通过
   */
  object RobotCharging : CheckItem {
    override val name = "RobotCharging"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      if (ChargingService.isChargingRobust(checkReq.rr)) {
        return fail("RobotCharging")
      }
      return pass()
    }
  }

  /**
   * 机器人被阻挡
   */
  object RobotBlocked : CheckItem {
    override val name = "RobotBlocked"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      val selfReport = checkReq.rr.selfReport
      return if (selfReport?.main?.blocked == true) {
        fail("RobotBlocked")
      } else {
        pass()
      }
    }
  }

  /**
   * 路径规划是否暂停
   */
  object TrafficPlanPaused : CheckItem {
    override val name = "TrafficPlanPaused"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      val sr = checkReq.rr.sr
      sr.config.trafficPlanPaused
      return if (sr.config.trafficPlanPaused) {
        fail("TrafficPlanPaused")
      } else {
        pass()
      }
    }
  }

  /**
   * 校验机器人是否被其他机器人阻挡，导致获取不到资源
   */
  object RobotBlockedByTraffic : CheckItem {
    override val name = "RobotBlockedByTraffic"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")

      val tm = checkReq.rr.sr.trafficService.showTrafficResourceMessage(checkReq.rr.robotName)
      val blockedBy = tm?.blockedMessage?.robotBlocked
      return if (!blockedBy.isNullOrEmpty()) {
        fail("RobotBlockedByTraffic", blockedBy)
      } else {
        pass()
      }
    }
  }

  /**
   * 校验机器人是否能够到达某个点位
   */
  object RobotCanReachPoint : CheckItem {
    override val name = "RobotCanReachPoint"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      if (checkReq.pointName.isNullOrEmpty()) return fail("PointIsNull")
      if (checkReq.fromPointName.isNullOrBlank()) return fail("FromPointNullOrBlank")
      try {
        val result = MapService.getShortestPathCostOfCrossAreas(checkReq.rr, checkReq.fromPointName, checkReq.pointName)
        if (result == NOT_ACHIEVABLE_COST) return fail("RobotCanReachPoint")
      } catch (_: BzError) {
        return fail("RobotCanReachPoint")
      }
      return pass()
    }
  }

  /**
   * 机器人是否执行这个运单是否可达
   */
  object RobotCanReachOrder : CheckItem {
    override val name = "RobotCanReachOrder"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      if (checkReq.or == null) return fail("OrderIsNull")
      try {
        val result = DispatchOrderService.estimateOrderCost(checkReq.or, checkReq.rr)
        if (result == NOT_ACHIEVABLE_COST) return fail("RobotCanReachOrder")
      } catch (_: BzError) {
        return fail("RobotCanReachOrder")
      }
      return pass()
    }
  }

  /**
   * 检测机器人是否没有运单
   */
  object RobotNoOrder : CheckItem {
    override val name = "RobotNoOrder"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      return if (checkReq.rr.orders.isEmpty()) {
        fail("RobotNoOrder")
      } else {
        pass()
      }
    }
  }

  /**
   * 机器人的运单是否都故障
   */
  object RobotOrdersFault : CheckItem {
    override val name = "OrderFault"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      if (checkReq.rr.orders.isEmpty()) return fail("RobotNoOrder")
      if (checkReq.rr.orders.all { it.value.order.fault }) return fail("RobotsOrderFault")
      return pass()
    }
  }

  /**
   * 判断这个机器人是否运单期望的机器人
   */
  object RobotIsOrderExpected : CheckItem {
    override val name = "RobotIsOrderExpected"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      if (checkReq.or == null) return fail("OrderIsNull")
      val expectedRobotNames = checkReq.or.order.expectedRobotNames
      val expectedRobotGroups = checkReq.or.order.expectedRobotGroups
      if (expectedRobotNames == null && expectedRobotGroups == null) return pass()
      if (expectedRobotNames?.isNotEmpty() == true &&
        !expectedRobotNames.contains(checkReq.rr.robotName)
      ) {
        return fail("RobotNotOrderExpectedRobotNames", listOf(expectedRobotNames))
      }
      if (expectedRobotGroups?.isNotEmpty() == true &&
        !expectedRobotGroups.contains(checkReq.rr.mustGetGroup().name)
      ) {
        return fail("RobotNotOrderExpectedRobotGroups", listOf(expectedRobotGroups))
      }

      return pass()
    }
  }

  /**
   * 判断后台任务的状态(机器人)
   */
  object BackgroundJob : CheckItem {
    override val name = "BackgroundJob"
    override fun check(checkReq: CheckReq): CheckResult {
      if (checkReq.rr == null) return fail("RobotIsNull")
      val rr = checkReq.rr
      val sr = checkReq.rr.sr
      val badThreadNames: List<String?> = listOf(
        OrderService.getDpThreadName(sr),
        OrderService.getHkThreadName(sr),
        StepSelectService.getThreadName(rr),
        RobotService.getThreadName(rr),
        if (sr.config.trafficMethod == TrafficMethod.Venus) VenusTrafficService.getThreadName(sr) else null,
      ).filter {
        if (it == null) return@filter false
        val job = PollingJobManager.getJobByThreadName(it)
        job == null || job.stop
      }
      if (badThreadNames.isNotEmpty()) {
        return fail("BackgroundJobFailed", listOf(badThreadNames.joinToString(", ")))
      }
      return pass()
    }
  }

  /**
   * 当机器人要跨区域时，检查是否有可用的电梯
   */
  object LiftAvailableWhenCrossArea : CheckItem {
    override val name = "LiftAvailableWhenCrossArea"

    override fun check(checkReq: CheckReq): CheckResult {
      val rr = checkReq.rr ?: return fail("RobotIsNull")
      val stepReject = rr.stepReject ?: return pass()
      val values = stepReject.params ?: emptyList()
      return when (val code = stepReject.code) {
        "errCrossAreaLiftsAvailableNone" -> fail(code.substringAfter("errCrossArea"), values)
        else -> pass() // 不是和电梯相关的异常，pass
      }
    }
  }
}