package com.seer.trick.fleet.domain

/**
 * 比较两个区域地图。m2 相对于 m1 的变化。
 * 停用 -> 启用相当于新增
 * 启用 -> 停用相当于删除
 * 点位还是按 name 做唯一标识吧；路径按 key；库位按 name。
 * 点位、路径等是否变化/没变，只考虑部分字段；从业务视角，主要是派单和交管视角。
 */
class SceneAreaMapDiff(m1: SceneAreaMap, m2: SceneAreaMap) {

  /**
   * 新添加的点位
   */
  val addedPoints = mutableListOf<MapPoint>()

  /**
   * 删除的点位
   */
  val removedPoints = mutableListOf<MapPoint>()

  /**
   * 修改了的点位
   */
  val changedPoints = mutableListOf<MapPoint>()

  /**
   * 新添加的路径
   */
  val addedPaths = mutableListOf<MapPath>()

  /**
   * 删除的路径
   */
  val removedPaths = mutableListOf<MapPath>()

  /**
   * 修改了的路径
   */
  val changedPaths = mutableListOf<MapPath>()

  /**
   * 新添加的库位
   */
  val addedBins = mutableListOf<SceneBin>()

  /**
   * 删除的库位
   */
  val removedBins = mutableListOf<SceneBin>()

  /**
   * 修改了的库位
   */
  val changedBins = mutableListOf<SceneBin>()

  private val pointMap1 = m1.points.filter { !it.disabled }.associateBy { it.name }
  private val pointMap2 = m2.points.filter { !it.disabled }.associateBy { it.name }

  private val pathMap1 = m1.paths.filter { !it.disabled }.associateBy { it.key }
  private val pathMap2 = m2.paths.filter { !it.disabled }.associateBy { it.key }

  private val binMap1 = m1.bins.filter { !it.disabled }.associateBy { it.name }
  private val binMap2 = m2.bins.filter { !it.disabled }.associateBy { it.name }

  init {
    diffPoints()
    diffPaths()
    diffBins()
  }

  /**
   * 两个地图是否有变化
   */
  fun isAnyChanged() = addedPoints.isNotEmpty() ||
    removedPoints.isNotEmpty() ||
    changedPoints.isNotEmpty() ||
    addedPaths.isNotEmpty() ||
    removedPaths.isNotEmpty() ||
    changedPaths.isNotEmpty() ||
    addedBins.isNotEmpty() ||
    removedBins.isNotEmpty() ||
    changedBins.isNotEmpty()

  /**
   * 比较点位差异
   */
  private fun diffPoints() {
    for (p2 in pointMap2.values) {
      val p1 = pointMap1[p2.name]
      if (p1 == null) {
        addedPoints += p2
      } else {
        if (!isSamePoint(p1, p2)) changedPoints += p2
      }
    }
    for (p1 in pointMap1.values) {
      val p2 = pointMap2[p1.name]
      if (p2 == null) removedPoints += p1
    }
  }

  /**
   * 点位：业务层面是否相等
   */
  private fun isSamePoint(p1: MapPoint, p2: MapPoint): Boolean {
    if (p1.x != p2.x) return false
    if (p1.y != p2.y) return false
    if (p1.direction != p2.direction) return false
    if (p1.parkAllowed != p2.parkAllowed) return false
    if (p1.chargeAllowed != p2.chargeAllowed) return false
    if (p1.name != p2.name) return false
    if (p1.type != p2.type) return false

    return true
  }

  /**
   * 比较路径差异
   */
  private fun diffPaths() {
    for (p2 in pathMap2.values) {
      val p1 = pathMap1[p2.key]
      if (p1 == null) {
        addedPaths += p2
      } else {
        if (!isSamePath(p1, p2)) changedPaths += p2
      }
    }
    for (p1 in pathMap1.values) {
      val p2 = pathMap2[p1.key]
      if (p2 == null) removedPaths += p1
    }
  }

  /**
   * 路径：业务层面是否相等
   */
  private fun isSamePath(p1: MapPath, p2: MapPath): Boolean {
    if (p1.degree != p2.degree) return false
    if (p1.actualLength != p2.actualLength) return false
    if (p1.costFactor != p2.costFactor) return false
    if (p1.moveDirection != p2.moveDirection) return false
    if (p1.curveType != p2.curveType) return false
    if (p1.knots != p2.knots) return false
    if (p1.controls != p2.controls) return false

    return true
  }

  /**
   * 比较库位差异
   */
  private fun diffBins() {
    for (b2 in binMap2.values) {
      val b1 = binMap1[b2.name]
      if (b1 == null) {
        addedBins += b2
      } else {
        if (!isSameBin(b1, b2)) changedBins += b2
      }
    }
    for (b1 in binMap1.values) {
      val b2 = pathMap2[b1.name]
      if (b2 == null) removedBins += b1
    }
  }

  /**
   * 库位：业务层面是否相等
   */
  private fun isSameBin(b1: SceneBin, b2: SceneBin): Boolean {
    if (b1.layerNo != b2.layerNo) return false
    if (b1.workPointName != b2.workPointName) return false

    return true
  }
}