package com.seer.trick.fleet.service

import com.seer.trick.BzError
import com.seer.trick.base.MapToAnyNull
import com.seer.trick.fleet.domain.BasicChargingConfig
import com.seer.trick.fleet.domain.RejectReason
import com.seer.trick.fleet.order.ChargingService
import com.seer.trick.fleet.order.OrderRuntime

/**
 * 调度暴露给脚本的接口
 */
@Suppress("unused")
object FleetScriptApi {

  /**
   * 添加事件监听器，注意 id 必须唯一
   */
  fun onEvent(eventName: String, listener: FleetEventScriptListener) {
    FleetEventService.on(eventName, listener)
  }

  /**
   * 移除事件监听器，根据 id
   */
  fun offEvent(eventName: String, id: String) {
    FleetEventService.off(eventName, id)
  }

  /**
   * 用机器人地图计算两点之间的最短路的成本
   */
  fun getCost(robotName: String, fromLocation: String, toLocation: String, sceneName: String): Double {
    val sr = SceneService.mustGetSceneByName(sceneName)
    val rr = sr.mustGetRobot(robotName)
    return MapService.getShortestPathOfCrossAreas(rr, fromLocation, toLocation, emptySet()).weight
  }

  /**
   * 用机器人地图，计算机器人位置到的目标点的最短路的成本
   */
  fun getCost(robotName: String, toLocation: String, sceneName: String): Double {
    val sr = SceneService.mustGetSceneByName(sceneName)
    val rr = sr.mustGetRobot(robotName)
    val fromPointName = MapService.findBestStartPointNameForPlan(rr) ?: throw BzError("errNoFromPointName")
    return MapService.getShortestPathCostOfCrossAreas(rr, fromPointName, toLocation)
  }

  // 旅行商问题，传入多个点位，计算机器人依次前往各个点位最近的点位列表，支持限定第一个去的点位
  fun getTspPath(robotName: String, locations: List<String>, sceneName: String, firstLocation: String?): List<String> {
    val sr = SceneService.mustGetSceneByName(sceneName)
    val rr = sr.mustGetRobot(robotName)
    return MapService.getTspPath(rr, locations, firstLocation)
  }

  fun listRobotNames(sceneName: String): List<String> {
    val sr = SceneService.mustGetSceneByName(sceneName)
    return sr.robots.keys.toList()
  }

  fun listRobotRuntimes(sceneName: String): List<RobotRuntime> {
    val sr = SceneService.mustGetSceneByName(sceneName)
    return sr.robots.values.toList()
  }

  fun mustGetRobotRawReport(sceneName: String, robotName: String): MapToAnyNull {
    val sr = SceneService.mustGetSceneByName(sceneName)
    val rr = sr.mustGetRobot(robotName)
    return rr.selfReport?.rawReport ?: emptyMap()
  }

  fun mustGetOrder(sceneName: String, orderId: String): OrderRuntime {
    val sr = SceneService.mustGetSceneByName(sceneName)
    return sr.mustGetOrderById(orderId)
  }

  fun mustGetOrderAllocationReject(sceneName: String, orderId: String): RejectReason? {
    val sr = SceneService.mustGetSceneByName(sceneName)
    val order = sr.mustGetOrderById(orderId)
    order.allocationReject
    order.executionReject
    return order.allocationReject
  }

  fun mustGetOrderExecutionReject(sceneName: String, orderId: String): RejectReason? {
    val sr = SceneService.mustGetSceneByName(sceneName)
    val order = sr.mustGetOrderById(orderId)
    order.executionReject
    return order.executionReject
  }

  fun updateOffDuty(sceneName: String, robotName: String, offDuty: Boolean) {
    val sr = SceneService.mustGetSceneByName(sceneName)
    val rr = sr.mustGetRobot(robotName)
    RobotService.updateOffDuty(rr, offDuty)
  }

  /**
   * 获取指定机器人的充电配置
   */
  fun getChargeConfig(rr: RobotRuntime): BasicChargingConfig = ChargingService.getConfigOfRobot(rr)

  /**
   * 获取指定机器人当前电量
   */
  fun getBatteryOrZero(rr: RobotRuntime): Double = ChargingService.getBatteryOrZero(rr)

  /**
   * 机器人充电中
   */
  fun isChargingRobust(rr: RobotRuntime): Boolean = ChargingService.isChargingRobust(rr)
}