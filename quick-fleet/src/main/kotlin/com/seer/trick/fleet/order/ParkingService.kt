package com.seer.trick.fleet.order

import com.seer.trick.BzError
import com.seer.trick.fleet.diagnosis.RobotOrderPrediction
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.service.MapService
import com.seer.trick.fleet.service.MapService.NOT_ACHIEVABLE_COST
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.RobotService
import com.seer.trick.fleet.service.SceneRuntime
import org.slf4j.LoggerFactory
import java.util.*

/**
 * 机器人停靠。
 * TODO 明示：应该停靠单没有停靠点
 */
object ParkingService {
  
  private val logger = LoggerFactory.getLogger(javaClass)
  
  fun tryToPark(sr: SceneRuntime) {
    if (sr.config.parkingPaused) return
    // 检测去停靠的机器人的点位是否存在冲突的机器人，存在则取消停靠单
    checkConflictingRobot(sr)
    
    // 需要停靠的机器人
    val toParkRobots = sr.listRobots()
      .filter { RobotOrderPrediction.checkRobotShouldParking(it, it.startPointNameForDispatching) == null }
      .mapNotNull {
        val bestStart = it.startPointNameForDispatching
        if (bestStart != null) {
          RobotWithBestStart(it, bestStart)
        } else {
          null
        }
      }
    
    // TODO 两个问题：1、导致跨楼层停靠；2、用合并后的地图，具体机器人组可能没有这个停靠位置
    // 按 groupId 分组处理，支持跨区域停靠
    val groupRobots = toParkRobots.groupBy { it.rr.config.groupId }
    val helper = ParkAndChargeResourceHelper(sr)
    for ((_, robots) in groupRobots) {
      val costs = mutableListOf<ParkingCost>()
      for (r in robots) {
        val parkPoints = helper.listAvailableParkingPoints(r.rr)
        if (parkPoints.isEmpty()) continue
        for (location in parkPoints) {
          val fromPoint = r.rr.startPointNameForDispatching ?: continue
          val cost = try {
            val result = MapService.getShortestPathCostOfCrossAreas(r.rr, fromPoint, location)
            if (result == NOT_ACHIEVABLE_COST) continue else result
          } catch (_: BzError) {
            continue
          }
          val mustCharging = RobotOrderPrediction.checkRobotMustCharging(r.rr, fromPoint)
          val chargingPoints = helper.listAvailableChargingPoints(r.rr)
          // 需要强充但没有充电点则优先去停靠
          val priority = if (mustCharging == null && chargingPoints.isEmpty()) {
            0
          } else {
            1
          }
          costs += ParkingCost(r.rr, r.bestStartPointName, location, cost, priority)
        }
      }
      costs.sortWith(compareBy({ it.priority }, { it.cost }))
      val assigned = mutableMapOf<String, String>() // robot -> parking point
      for (c in costs) {
        // 不用同一个对象的原因是，每一次重新计算，避免同一轮碰撞检测出问题。
        // 由于目前每次分配完成后都直接创建已分派运单，故已分配的点位都在包含在占用的点位中
        if (ParkAndChargeResourceHelper(sr).checkParkAndChargeCollision(c.rr, c.parkingPointName) != null) continue
        // 检查停靠点是否已被分配
        if (assigned.containsValue(c.parkingPointName)) continue
        if (assigned.contains(c.rr.robotName)) continue
        
        logger.info("Have the robot ${c.rr.robotName} docked at ${c.parkingPointName}")
        doParkingOrder(sr, c.rr, c.parkingPointName)
        assigned[c.rr.robotName] = c.parkingPointName
      }
    }
  }
  
  /**
   * 为了应对机器人可能出现的偶发断联、长时间无法连接或初次启动时机器人连接顺序不固定等异常情况：
   * 可能会出现某些机器人尚未连接时，其他机器人已被分配到了停靠点，但这个停靠点可能正是尚未连接的某个机器人当前所在的位置。
   * 当前的解决措施仅针对最终成功连接的机器人：如果某个机器人成功连接，则将取消去其当前位置的停靠任务。
   */
  private fun checkConflictingRobot(sr: SceneRuntime) {
    // 获取所有机器人列表一次，避免多次调用
    val allRobots = sr.listRobots()
    
    // 筛选出所有正在执行停车自动订单的机器人
    val checkParkingRobots = allRobots.filter {
      it.autoOrder?.type == RobotAutoOrderType.Parking && it.isExecuting()
    }
    
    // 遍历每一个正在停车的机器人
    for (parkingRobot in checkParkingRobots) {
      val targetPoint = parkingRobot.autoOrder?.pointName
      if (targetPoint != null) {
        // 查找是否有其他机器人当前位于目标停车点
        allRobots.firstOrNull {
          it.selfReport?.main?.currentPoint == targetPoint && it.robotName != parkingRobot.robotName
        } ?: continue
        //  没有冲突的机器人，无需取消订单；如果找到冲突的机器人，尝试取消其自动订单
        parkingRobot.autoOrder?.orderId?.let { orderId ->
          val withdrawingOrder = parkingRobot.orders[orderId]
          if (withdrawingOrder != null) {
            // 取消订单
            OrderCancelService.cancelOrder(sr, withdrawingOrder.id)
          } else {
            // 记录错误日志
            logger.error(
              "$orderId could not be found in the list of Order for robot ${parkingRobot.robotName}",
            )
          }
        }
      }
    }
  }
  
  // TODO 优化
  // private fun listIdleParkingPoints(): List<String> {
  //   // 已使用的、即将使用的点
  //   val usedOrUsingPoints = mutableSetOf<String>()
  //
  //   for (rr in orderService.scene.robots.values) {
  //     rr.selfReport?.stand?.pointName?.let { usedOrUsingPoints += it }
  //     rr.autoOrder?.pointName?.let { usedOrUsingPoints += it }
  //   }
  //
  //   return orderService.scene.mapService.listPoints().values
  //     .filter { isParkingPoint(it.point) } // 过滤停靠点
  //     .filter { !usedOrUsingPoints.contains(it.point.name) }
  //     .map { it.point.name }
  // }
  
  fun doParkingOrder(sr: SceneRuntime, rr: RobotRuntime, parkingPointName: String) {
    val orderId = OrderService.generateOrderId() + "P"
    
    logger.info("Have the robot ${rr.robotName} Parking at $parkingPointName, Parking Order = $orderId")
    
    val step = TransportStep(
      id = orderId + "Step0",
      orderId = orderId,
      status = StepStatus.Executable,
      withdrawOrderAllowed = true,
      location = parkingPointName,
    )
    
    val order = TransportOrder(
      kind = OrderKind.Parking,
      id = orderId,
      status = OrderStatus.Allocated, // 直接已分派
      stepFixed = true,
      stepNum = 1,
      keyLocations = listOf(parkingPointName),
      expectedRobotNames = listOf(rr.robotName),
      actualRobotName = rr.robotName,
      robotAllocatedOn = Date(),
      sceneId = sr.sceneId,
    )
    val steps = listOf(step)
    
    startParking(sr, rr, OrderRuntime(order, steps), parkingPointName)
  }
  
  /**
   * 开始停靠单。停靠不修改库位。
   */
  private fun startParking(
    sr: SceneRuntime,
    rr: RobotRuntime,
    or: OrderRuntime,
    parkingPointName: String,
  ) {
    logger.info("The robot $rr starts parking, the target point = $parkingPointName, the Order ${or.order}")
    OrderService.createOrders(sr, listOf(or))
    
    rr.orders[or.id] = or
    
    // 清理充电时间
    rr.chargingOrderDoneOn = null
    
    rr.autoOrder = RobotAutoOrder(RobotAutoOrderType.Parking, or.id, parkingPointName)
    
    RobotService.persistRobotRuntime(rr)
  }
}

data class RobotWithBestStart(
  val rr: RobotRuntime,
  val bestStartPointName: String, // point name
)

data class ParkingCost(
  val rr: RobotRuntime,
  val bestStartPointName: String,
  val parkingPointName: String,
  val cost: Double,
  val priority: Int = 0,
)