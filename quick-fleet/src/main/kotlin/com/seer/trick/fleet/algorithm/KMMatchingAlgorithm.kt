package com.seer.trick.fleet.algorithm

import com.seer.trick.BzError
import com.seer.trick.fleet.domain.PreAllocation
import com.seer.trick.fleet.domain.RejectReason
import com.seer.trick.fleet.order.*
import com.seer.trick.fleet.service.MapService.NOT_ACHIEVABLE_COST
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.SceneRuntime
import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap

class KMMatchingAlgorithm : MatchingAlgorithm {

  private val logger = LoggerFactory.getLogger(javaClass)

  // 保存上次分配的结果，用于计算变更
  private val previousAllocations = ConcurrentHashMap<String, String>()

  var totalCost: Double = 0.0 // 本次分配的成本合

  val additionalCostForChangingRobot = 5 // 假设这是更换机器人的额外成本

  override fun match(
    sr: SceneRuntime,
    orders: List<OrderRuntime>,
    robots: List<RobotRuntime>,
  ): List<PreAllocation> {
    // 如果没有订单或机器人，返回空列表
    if (orders.isEmpty() || robots.isEmpty()) return emptyList()

    // 移除不存在的订单的历史分配
    cleanUpPreviousAllocations(orders.map { it.id })

    // 构建成本矩阵
    val (costMatrix, robotIndexMap) = buildCostMatrix(orders, robots)

    // 如果只有一个订单，直接分配最优机器人
    if (orders.size == 1) return allocateSingleOrder(orders, robots, costMatrix, robotIndexMap)

    // 使用 Kuhn-Munkres 算法计算匹配
    val result = KuhnMunkresImpl.solve(costMatrix)
    // 构建最终的分配结果
    return buildAllocations(result, orders, robots, costMatrix, robotIndexMap)
  }

  private fun cleanUpPreviousAllocations(orderIds: List<String>) {
    // 移除历史分配中不存在于当前订单列表的订单
    previousAllocations.keys.retainAll(orderIds.toSet())
  }

  private fun buildCostMatrix(
    orders: List<OrderRuntime>,
    robots: List<RobotRuntime>,
  ): Pair<Array<IntArray>, Map<Int, Int>> {
    val totalCapacity = robots.sumOf { OrderService.calculateCanLoadCount(it) }

    val costMatrix = Array(orders.size) { IntArray(totalCapacity) { -1 } }
    // 记录库位索引与机器人索引映射快照，binIndex -> robotIndex
    val robotIndexMap: MutableMap<Int, Int> = HashMap()

    // 为每个机器人和订单组合计算成本
    for ((robotIndex, rr) in robots.withIndex()) {
      val offset = robots.take(robotIndex).sumOf { OrderService.calculateCanLoadCount(it) }
      repeat(OrderService.calculateCanLoadCount(rr)) { binIndex ->
        // 每次都重新计算非有货库位数量，这些数量增多时，会导致数组越界
        if (offset + binIndex >= totalCapacity) return@repeat

        robotIndexMap[offset + binIndex] = robotIndex

        for ((orderIndex, orderRuntime) in orders.withIndex()) {
          // 增加 estimateCost 的验证逻辑
          val cost = try {
            val result = DispatchOrderService.estimateOrderCost(orderRuntime, rr).toInt()
            if (result == NOT_ACHIEVABLE_COST.toInt()) {
              // 如果计算不出 cost ，则表示不连通，标记为 -1
              costMatrix[orderIndex][offset + binIndex] = -1
              continue // 跳过此分配
            } else {
              result
            }
          } catch (_: BzError) {
            // 如果点位不存在 ，则表示不连通，标记为 -1
            costMatrix[orderIndex][offset + binIndex] = -1
            continue // 跳过此分配
          }

          var adjustedCost = cost

          // 增加额外成本逻辑：如果正在执行且满的机器人要接其他的运单
          if (!rr.orders.contains(orderRuntime.order.id) &&
            rr.orders.size == rr.bins.size
          ) {
            adjustedCost += additionalCostForChangingRobot
          }

          // 增加额外成本逻辑：如果正在执行的运单要换成其他机器人接单
          if (orderRuntime.order.actualRobotName != null && orderRuntime.order.actualRobotName != rr.robotName) {
            adjustedCost += additionalCostForChangingRobot
          }

          val order = orderRuntime.order

          // 机器人没有指定机器人也没有指定机器人组时，是根据关键位置来匹配机器人
          if (order.expectedRobotNames.isNullOrEmpty()) {
            // 如果当前运单没有指定机器人，但指定机器人组，而机器人不在运单指定机器人组列表中，则设置为不连通
            if (!order.expectedRobotGroups.isNullOrEmpty() &&
              !order.expectedRobotGroups.contains(rr.mustGetGroup().name)
            ) {
              adjustedCost = -1
            }
          } else {
            // 如果当前运单指定机器人，则忽略指定机器人组，若机器人不在指定的机器人中，则设置为不连通
            if (!order.expectedRobotNames.contains(rr.robotName)) {
              adjustedCost = -1
            }
          }

          // 设置最终成本到 costMatrix
          costMatrix[orderIndex][offset + binIndex] = adjustedCost
        }
      }
    }
    return Pair(costMatrix, robotIndexMap)
  }

  private fun allocateSingleOrder(
    orders: List<OrderRuntime>,
    robots: List<RobotRuntime>,
    costMatrix: Array<IntArray>,
    robotIndexMap: Map<Int, Int>,
  ): List<PreAllocation> {
    val order = orders[0].order

    // 收集有效机器人（只包含成本不是 -1 的机器人）
    val validIndices = costMatrix[0].indices.filter { costMatrix[0][it] != -1 }
    if (validIndices.isEmpty()) {
      orders[0].allocationReject = RejectReason("NoEnoughRobot", listOf("No reachable robot"))
      return emptyList() // 若无可分配机器人，返回空列表
    }

    // 找到成本最小的可达机器人
    val bestRobotIndex = validIndices.minByOrNull { costMatrix[0][it] } ?: return emptyList()
    val minCost = costMatrix[0][bestRobotIndex]

    // 通过列索引获取实际机器人的索引
    // val robotIndex = getRobotIndexFromColumnIndex(robots, bestRobotIndex)
    val robotIndex = robotIndexMap[bestRobotIndex] ?: return emptyList()

    // 创建单一的分配
    val robot = robots[robotIndex]
    return listOf(
      PreAllocation(
        robotName = robot.robotName,
        orderId = order.id,
        priority = order.priority,
        cost = minCost.toDouble(),
        createdOn = order.createdOn,
      ),
    )
  }

  private fun buildAllocations(
    result: IntArray,
    orders: List<OrderRuntime>,
    robots: List<RobotRuntime>,
    costMatrix: Array<IntArray>,
    robotIndexMap: Map<Int, Int>,
  ): List<PreAllocation> {
    val allocations = mutableListOf<PreAllocation>()
    for ((orderIndex, columnIndex) in result.withIndex()) {
      if (columnIndex != -1 && orderIndex < orders.size && columnIndex < costMatrix[0].size) {
        val cost = costMatrix[orderIndex][columnIndex]
        // 跳过不连通的分配
        if (cost == -1) continue

        val order = orders[orderIndex].order
        // 并发时，该计算方式就会有问题，等于 -1 的时候，说明库位已经发生变化了，根据 columnIndex 获取不到正确的机器人索引，此次分配已错误
        // val robotIndex = getRobotIndexFromColumnIndex(robots, columnIndex)
        val robotIndex = robotIndexMap[columnIndex]
        if (robotIndex != null) {
          val robot = robots[robotIndex]
          // 这里使用的是真实成本，而不是带有额外成本的成本矩阵值
          val realCost = try {
            val result = DispatchOrderService.estimateOrderCost(orders[orderIndex], robot)
            if (result == NOT_ACHIEVABLE_COST) continue else result
          } catch (_: BzError) {
            continue
          }
          allocations.add(
            PreAllocation(
              robotName = robot.robotName,
              orderId = order.id,
              priority = order.priority,
              cost = realCost, // 使用真实成本
              createdOn = order.createdOn,
            ),
          )
        } else {
          // 这里不应该发生，除非 columnIndex 计算错误
          logger.error("Error to get robotIndex from robotIndexMap $robotIndexMap by columnIndex $columnIndex")
          return emptyList()
        }
      }
    }
    val sortedWith = allocations.sortedWith(
      compareByDescending<PreAllocation> { it.priority }.thenBy { it.cost }
        .thenBy { it.createdOn },
    )
//    printAllocations(sortedWith)
    updatePreviousAllocations(sortedWith)
    return sortedWith
  }

  // 更新历史数据
  private fun updatePreviousAllocations(allocations: List<PreAllocation>) {
//    logger.info("allocations size: ${allocations.size}")
//    logger.info("previousAllocations size: ${previousAllocations.size}")
    allocations.count { previousAllocations[it.orderId] != it.robotName }
    previousAllocations.run {
      clear()
      putAll(allocations.associateBy({ it.orderId }, { it.robotName }))
    }
//    logger.info("Number of changed allocations: $changedAllocationsCount")
  }

  fun printAllocations(allocations: List<PreAllocation>) {
    allocations.forEach { allocation ->
      logger.info("RobotName: ${allocation.robotName}, Cost: ${allocation.cost},orderId:${allocation.orderId}")
    }
    logger.info("Pre Total Cost: $totalCost")
    totalCost = allocations.sumOf { it.cost }
    logger.info("Total Cost: $totalCost")
  }

  // 辅助函数，通过列索引获取相应的机器人索引
  private fun getRobotIndexFromColumnIndex(robots: List<RobotRuntime>, columnIndex: Int): Int {
    var count = 0
    robots.forEachIndexed { index, rr ->
      val canLoadCount = OrderService.calculateCanLoadCount(rr)
      if (columnIndex < count + canLoadCount) return index
      count += canLoadCount
    }
    return -1
  }
}

// 扩展函数，打印二维数组的真实值
fun Array<IntArray>.print() {
  println("cost:")
  this.forEach { row ->
    println(row.joinToString(", "))
  }
}

// 扩展函数，打印一维数组的真实值
fun IntArray.print() {
  println("result:")
  println(this.joinToString(", "))
}