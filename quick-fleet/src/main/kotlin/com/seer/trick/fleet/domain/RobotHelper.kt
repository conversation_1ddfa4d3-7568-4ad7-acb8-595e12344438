package com.seer.trick.fleet.domain

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.I18N
import com.seer.trick.I18N.lo
import com.seer.trick.base.alarm.AlarmAction
import com.seer.trick.base.alarm.AlarmItem
import com.seer.trick.base.alarm.AlarmLevel
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.fleet.seer.SmapPos
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.NumHelper
import org.apache.commons.lang3.math.NumberUtils
import org.slf4j.LoggerFactory
import java.util.*

/**
 * 处理机器人相关对象
 */
object RobotHelper {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 将各品牌机器人的自报数据转换为 RobotSelfReportMain
   */
  fun rawToMain(vendor: RobotVendor, rawReport: EntityValue?, rr: RobotRuntime): RobotSelfReportMain? {
    if (rawReport == null) return null

    return when (vendor) {
      RobotVendor.Seer -> {
        val rawMain = rawToMainSeer(rawReport)
        // 判断机器人当前地图的 MD5 与机器人组地图的 MD5 是否一致，不一致则增加告警提示
        // 为了兼容光通讯，暂时放在这里产生告警
        val currentMapSame = compareCurrentMapMd5(rr, rawMain)
        if (!currentMapSame) {
          val ai = AlarmItem(
            sceneId = rr.sr.sceneId,
            group = "Fleet",
            code = "SceneRobotMapNotMatch",
            key = "SceneRobotMapNotMatch-${rr.robotName}",
            level = AlarmLevel.Error,
            message = lo("errRobotMapInconsistentWithScene", listOf(rr.robotName)),
            args = listOf(rr.sr.sceneId, rr.robotName),
            actions = listOf(AlarmAction(lo("btnPushMap"), uiAction = "FleetSceneSync")),
            tags = setOf(rr.tag, rr.sr.tag),
          )
          AlarmService.addItem(ai, ttl = 3000)
        }
        rawMain.copy(currentMapNotMatched = !currentMapSame)
      }

      else -> null
    }
  }

  /**
   * 判断机器人的地图与机器人组内的同名地图的 MD5 是否相同
   */
  private fun compareCurrentMapMd5(rr: RobotRuntime, reportMain: RobotSelfReportMain): Boolean {
    val schema = rr.sr.mapCache.getAreaCacheByStand(rr)?.schema
    // 同个区域中的不同分组的机器人，使用的地图名称可以是相同的，但两份地图的 MD5 值可能是不同的，不能只根据地图名称找记录。
    // 优化：先获取机器人所在的分组的 id，即 groupId，然后根据 groupId 找记录
    val record = schema?.groupsMap?.get(rr.config.groupId)
    if (record != null) {
      return reportMain.currentMapMd5 == record.mapMd5 && reportMain.currentMap == record.mapName
    }
    return false
  }

  private fun rawToMainSeer(rawReport: EntityValue): RobotSelfReportMain {
    val currentStation = rawReport["current_station"] as String? ?: ""
    val x = NumHelper.anyToDouble(rawReport["x"])
    val y = NumHelper.anyToDouble(rawReport["y"])
    val angle = NumHelper.anyToDouble(rawReport["angle"])

    val alarms: MutableList<RobotAlarm> = ArrayList()
    parseSeerAlarm(rawReport["fatals"], RobotAlarmLevel.Fatal, alarms)
    parseSeerAlarm(rawReport["errors"], RobotAlarmLevel.Error, alarms)
    parseSeerAlarm(rawReport["warnings"], RobotAlarmLevel.Warning, alarms)
    parseSeerAlarm(rawReport["notices"], RobotAlarmLevel.Info, alarms)

    val relocStatus = NumHelper.anyToInt(rawReport["reloc_status"])
    val relocStatusLabel = lo("relocStatus_$relocStatus")

    @Suppress("UNCHECKED_CAST")
    val currentLock = rawReport["current_lock"] as Map<String, Any?>?
    val currentLockNickName = currentLock?.get("nick_name") as String?
    val locked = currentLock?.get("locked") as Boolean? // TODO 这个属性是啥意思，答：ture 表示机器人已经被抢占，结合 nick_name 可以知道是哪个用户抢占的
    val currentMap = rawReport["current_map"] as String?
    // 去掉后缀
    // val currentMapMain = if (currentMap == null) null
    // else if (currentMap.endsWith(".smap")) StringUtils.substringBeforeLast(currentMap, ".smap")
    // else if (currentMap.endsWith(".SMAP")) StringUtils.substringBeforeLast(currentMap, ".SMAP")
    // else currentMap
    // val currentArea = if (!currentMapMain.isNullOrBlank()) mm.getAreaByAnyName(currentMapMain)
    // else null

    // val xStr = x?.toString() ?: "?"
    // val yStr = y?.toString() ?: "?"

    return RobotSelfReportMain(
      battery = NumHelper.anyToDouble(rawReport["battery_level"]),
      x = x,
      y = y,
      direction = angle,
      currentPoint = currentStation,
      // currentLocationDesc = "[$currentStation] ($xStr, $yStr)",
      blocked = rawReport["blocked"] as Boolean?,
      blockedReason = NumHelper.anyToInt(rawReport["block_reason"]),
      charging = rawReport["charging"] as Boolean?,
      currentMap = addMapExt(currentMap),
      currentMapMd5 = rawReport["current_map_md5"] as String?,
      emergency = rawReport["emergency"] as Boolean?,
      softEmc = rawReport["soft_emc"] as Boolean?,
      todayOdo = NumHelper.anyToDouble(rawReport["today_odo"]), // 今日累计行驶里程, 单位 m
      confidence = NumHelper.anyToDouble(rawReport["confidence"]), // 机器人的定位置信度, 范围 [0, 1]
      relocStatus = convertRelocStatus(relocStatus),
      relocStatusLabel = relocStatusLabel,
      controller = currentLockNickName,
      alarms = alarms,
      isControlled = locked,
      loadRelations = parseSeerLoadRelations(rawReport["goods_region"]),
      velocity = NumHelper.anyToDouble(rawReport["vx"]),
      rotateVelocity = NumHelper.anyToDouble(rawReport["w"]),
      timestamp = Date(),
      moveStatusInfo = rawReport["move_status_info"] as String?, // 机器人的运动状态信息
      bins = parseSeerContainers(rawReport["containers"]),
    )
  }

  private fun addMapExt(currentMap: String?): String? {
    if (currentMap.isNullOrBlank() || currentMap.endsWith(".smap")) return currentMap
    return "$currentMap.smap"
  }

  private fun convertRelocStatus(relocStatus: Int?): RelocStatus? = when (relocStatus) {
    0 -> {
      RelocStatus.Init
    }

    1 -> {
      RelocStatus.Success
    }

    2 -> {
      RelocStatus.Relocing
    }

    3 -> {
      RelocStatus.Loading
    }

    else -> {
      null
    }
  }

  /**
   * 解析机器人碰撞模型
   * 机器人底盘模型目前有三种：矩形、圆、多边形。将圆和多边形转换为包裹的矩形。
   *
   */
  fun parseChassisPolygon(robotModelRawEv: EntityValue): Polygon? {
    // 获取 deviceTypes 列表，使用安全类型转换
    val deviceTypes = robotModelRawEv["deviceTypes"] as? List<*> ?: return null

    // 找到 name 为 "chassis" 的设备并获取其 devices 列表
    val chassisDevices = deviceTypes
      .mapNotNull { it as? Map<*, *> }
      .firstOrNull { it["name"] == "chassis" }
      ?.get("devices") as? List<*> ?: return null

    // 在 devices 中找到 name 为 "chassis" 的设备并解析其参数
    chassisDevices
      .mapNotNull { it as? Map<*, *> }
      .firstOrNull { it["name"] == "chassis" }
      ?.let { chassisDevice ->
        val deviceParams = chassisDevice["deviceParams"] as? List<*> ?: return null

        // 查找 key 为 "shape" 的参数并解析
        deviceParams
          .mapNotNull { it as? Map<*, *> }
          .firstOrNull { it["key"] == "shape" }
          ?.let { shapeParam ->
            val comboParam = shapeParam["comboParam"] as? Map<*, *> ?: return null
            val type = comboParam["childKey"] as? String ?: return null
            val params = comboParam["childParams"] as? List<*> ?: return null
            return parseShape(type, params)
          }
      }

    return null
  }

  // 获取地盘模型
  private fun parseShape(type: String, params: List<*>): Polygon? {
    // 找到与 type 匹配的参数
    val parseParams = params
      .mapNotNull { it as? Map<*, *> }
      .firstOrNull { it["key"] == type }
      ?.get("params") as? List<*> ?: return null

    // TODO RobotCollisionModel 没给 shapes 字段

    return when (type) {
      "rectangle" -> { // 矩形
        // 车体的最大外宽度。
        val width = parseParams(parseParams, "width") as? Double ?: return null
        // 机器人坐标原点（旋转中心）到达车头的距离。
        val head = parseParams(parseParams, "head") as? Double ?: return null
        // tail 是 x 轴负方向的长度 机器人坐标原点（旋转中心）到达车尾的距离。
        val tail = parseParams(parseParams, "tail") as? Double ?: return null
        // 因为 tail 是 x 轴负方向的长度 但取值是正数，因此计算中心点 x 轴 (head - tail)/2
        // 此时参考坐标系是机器人
        val cx = (head - tail) / 2
        Polygon.fromRectCenterWidthHeight(cx, 0.0, head + tail, width)
      }

      "circle" -> { // 圆
        val radius = parseParams(parseParams, "radius") as? Double ?: return null
        Polygon.fromRectCenterWidthHeight(0.0, 0.0, radius * 2, radius * 2)
      }

      "polygon" -> { // 多边形
        val polygon = parseParams(parseParams, "polygon") as? String ?: return null
        val posList: List<SmapPos> = JsonHelper.mapper.readValue(polygon, jacksonTypeRef())
        val points = posList.map { Point2D(it.x, it.y) }
        Polygon(points)
      }

      else -> null
    }
  }

  /**
   * TODO 周春雷 含义不明
   * 获取底盘参数的值 name 匹配 params 参数集合中的 key 如果 name == key，从此对象拿值
   * @param params: 所有属性参数的集合
   * @param name: 参数名
   * 目前解析底盘参数如果是矩形、圆形从 doubleValue 字段取值，多边形从 stringValue 取值
   */
  private fun parseParams(params: List<*>, name: String): Any? {
    for (param in params) {
      val p = param as Map<*, *>
      if (p["key"] == name) {
        return p["doubleValue"] ?: p["stringValue"]
      }
    }
    return null
  }

  /**
   * 解析机器人载货情况。货物坐标是地图坐标系。
   */
  private fun parseSeerLoadRelations(rawJsonNode: Any?): List<RobotLoadRelation>? {
    // TODO goods_region
    val goodsRegion: GoodsRegion = JsonHelper.mapper.convertValue(rawJsonNode, jacksonTypeRef())
      ?: return null

    if (goodsRegion.points.isNullOrEmpty() || goodsRegion.points.size != 4) return null

    // 转换点位为 Point2D 列表
    val points = goodsRegion.points.map { Point2D(it.x, it.y) }
    // 计算方向
    val direction = (points[0] - points[1]).dir()

    return listOf(
      RobotLoadRelation(points = points, direction = direction),
    )
  }

  private fun parseSeerContainers(rawJsonNode: Any?): List<RobotSelfBin>? {
    if (rawJsonNode == null) return null
    val rawContainers = rawJsonNode as List<*>
    val containers: MutableList<RobotSelfBin> = ArrayList()
    for (c in rawContainers) {
      @Suppress("UNCHECKED_CAST")
      val container = c as Map<String, Any>
      val containerName = container["container_name"] as String
      val desc = container["desc"] as String? ?: ""
      val containerId = container["goods_id"] as String? ?: ""
      val occupied = container["has_goods"] as Boolean? ?: false
      val rbkBinId = NumHelper.anyToInt(containerName) ?: continue
      containers.add(
        RobotSelfBin(
          rbkBinId = rbkBinId,
          desc = desc,
          containerId = containerId,
          occupied = occupied,
        ),
      )
    }
    return containers
  }

  private fun parseSeerAlarm(node: Any?, level: RobotAlarmLevel, alarms: MutableList<RobotAlarm>) {
    try {
      if (node == null) return
      val errors = node as List<*>
      for (e in errors) {
        @Suppress("UNCHECKED_CAST")
        val eo = e as Map<String, Any?>
        val desc = eo["desc"] as String?
        val times = eo["times"] as Int?
        // 调度报错 code 直接事一个字段
        val code = eo.keys.find { NumberUtils.isDigits(it) } ?: (eo["code"] as Int?)?.toString()
        if (!desc.isNullOrBlank() && code != null) {
          alarms += RobotAlarm(level, code, desc, times ?: 1)
        }
      }
    } catch (e: Exception) {
      logger.error("解析仙工机器人报告中的告警", e)
    }
  }
}