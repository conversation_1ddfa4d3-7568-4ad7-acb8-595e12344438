package com.seer.trick.fleet.traffic.dev

import org.slf4j.LoggerFactory
import java.util.*
import kotlin.math.abs
import kotlin.math.ceil

/**
 * 有界最优，Focal Search A*
 * f, g, h 单位是时间，表示时间成本
 */
class FSA(
  private val robotName: String,
  private val highNodeId: Long,
  private val w: Double,
  private val mapDimX: Int,
  private val mapDimY: Int, // y 向下为正
  private val obstacles: Set<Int>,
  private val moveUnitCost: Double = 1.0, // 移动的单位成本，每米
  private val rotateUnitCost: Double = 0.0, // 旋转的单位成本，每 90 度
  private val isValidNeighbor: ((fromState: State, newState: State) -> Boolean)? = null,
  private val focalStateHeuristic2: ((s: State, g: Double) -> Double)? = null,
  private val focalTransitionHeuristic2: (
    (s1a: State, s1b: State, gScoreS1a: Double, gScoreS1b: Double) -> Double
  )? = null,
) {

  private val logger = LoggerFactory.getLogger(this::class.java)

  /**
   * 单目标搜索
   */
  fun search(
    targetIndex: Int,
    timeOffset: Long, // 起始时刻
    startState: State,
    goalState: State,
    goalStopTime: Int,
    lastGoalConstraint: Long = -1, // 如果目标位置被约束了，lastGoalConstraint 是被约束的最后一个时刻
  ): TargetOnePlanResult {
    val startOn = System.currentTimeMillis()
    var expandedCount = 0L

    val openSet = PriorityQueue<Node> { n1, n2 ->
      // 排序：lowest fScore，highest gScore
      if (n1.f != n2.f) {
        n1.f.compareTo(n2.f)
      } else {
        n2.g.compareTo(n1.g)
      }
    }

    val focalSet = PriorityQueue<Node> { n1, n2 ->
      // Sort order (see "Improved Solvers for Bounded-Suboptimal Multi-Agent Path Finding" by Cohen et. al.)
      // 1. lowest focalHeuristic
      // 2. lowest fScore
      // 3. highest gScore
      if (n1.focalHeuristic != n2.focalHeuristic) {
        n1.focalHeuristic.compareTo(n2.focalHeuristic)
      } else if (n1.f != n2.f) {
        n1.f.compareTo(n2.f)
      } else {
        n2.g.compareTo(n1.g)
      }
    }

    val closedSet = mutableSetOf<Node>()

    // 待展开，未展开（不在 close 里），那一定在 open 里吧，所有不需要这个
    val processedNodes = mutableSetOf<Node>()

    val startNode = Node(
      startState.copy(timeStart = timeOffset, timeEnd = timeOffset, timeNum = 1), // 时间从 timeOffset
      null,
      f = admissibleHeuristic(startState, goalState),
      g = 0.0,
      focalHeuristic = 0.0,
    )

    openSet += startNode
    focalSet += startNode
    processedNodes += startNode

    var fBest = startNode.f

    while (openSet.isNotEmpty()) {
      // update focal list
      focalSet.clear()
      val top = openSet.peek()
      val bound = top.f * w // top.f 值肯定在增大
      for (node in openSet) {
        if (node.f < bound) focalSet += node
      }

      // TODO 有这一步，可以不要上一步？
      val fBestOld = fBest
      fBest = openSet.peek().f
      if (fBest > fBestOld) {
        for (node in openSet) {
          // 之前不在 focalSet，本轮新加入 focalSet 的节点
          if (node.f > fBestOld * w && node.f <= fBest * w) focalSet += node
          if (node.f > fBest * w) break
        }
      }

      val current = focalSet.poll()
      ++expandedCount
      // logger.debug("[H=$highNodeId][R=$robotName] Low, #${expandedCount}, state=${current.state} (${current.g})")
      if (expandedCount > mapDimX * mapDimY) {
        logger.error("展开过多，是 BUG？")
        return TargetOnePlanResult(
          robotName,
          false,
          "展开过多",
          planCost = System.currentTimeMillis() - startOn,
          fromState = startState,
          toState = goalState,
        )
      }
      // M_env.onExpandNode(current.state, current.fScore, current.gScore);

      if (current.state.isSameLocation(goalState) &&
        current.state.timeEnd > lastGoalConstraint // 达到时间在最后一次目标点被约束的时刻后
      ) {
        logger.debug("[H=$highNodeId][R=$robotName][$targetIndex] Low, target one, find solution: ${current.g}")

        // 到达后等待一段时间
        val lastState = current.state

        val path = mutableListOf(lastState)
        var currNode = current.parent
        while (currNode != null) {
          path += currNode.state
          currNode = currNode.parent
        }
        path.reverse()

        val goalStopTime2 = if (goalStopTime <= 0) 1 else goalStopTime

        // 最后追加一个原地等待的，模拟动作时间
        val actionState = lastState.copy(
          timeStart = lastState.timeEnd + 1,
          timeEnd = lastState.timeEnd + goalStopTime2,
          timeNum = goalStopTime2.toLong(),
        )
        path += actionState

        return TargetOnePlanResult(
          robotName,
          true,
          cost = current.g + goalStopTime2,
          minF = current.f + goalStopTime2,
          planCost = System.currentTimeMillis() - startOn,
          expandedCount = expandedCount,
          timeNum = actionState.timeEnd,
          timeStart = timeOffset,
          timeEnd = actionState.timeEnd,
          fromState = startState,
          toState = goalState,
          path = path,
        )
      }

      openSet -= current
      processedNodes -= current // 加入 close 就不用在 processedStates
      closedSet += current

      // traverse neighbors
      val neighbors = getNeighbors(targetIndex, current.state, goalState, goalStopTime)
      for (neighbor in neighbors) {
        // 只要经过这个位置，不管时间、朝向
        if (closedSet.find { it.state.isSameLocation(neighbor) } != null) continue

        // 实际成本按时间算，timeNum 可以看出本次移动时间的向上取整
        val g = current.g + neighbor.timeNum

        val oldNode = processedNodes.find { it.state.isSameLocation(neighbor) }
        if (oldNode == null) {
          val f = g + admissibleHeuristic(neighbor, goalState)
          val focalHeuristic = current.focalHeuristic +
            focalStateHeuristic(neighbor, g) +
            focalTransitionHeuristic(current.state, neighbor, current.g, g)
          val node = Node(neighbor, current, f = f, focalHeuristic = focalHeuristic, g = g)
          openSet += node
          processedNodes += node
          if (f <= fBest * w) focalSet += node
        } else {
          // We found this node before with a better path
          if (g >= oldNode.g) continue
          val gOld = oldNode.g
          val fOld = oldNode.f
          // update f and g
          oldNode.g = g
          oldNode.f += g - gOld

          // 肯定已在 openSet，重新添加引发排序
          openSet -= oldNode
          openSet += oldNode

          if (oldNode.f <= fBest * w && fOld > fBest * w) {
            focalSet += oldNode
          }
        }
      }
    }

    return TargetOnePlanResult(
      robotName,
      false,
      "找不到路径",
      planCost = System.currentTimeMillis() - startOn,
      expandedCount = expandedCount,
      fromState = startState,
      toState = goalState,
    )
  }

  /**
   * 多目标搜索
   */
  fun search(startState: State, goalStates: List<State>, goalStopTime: Int): TargetManyPlanResult {
    val startOn = System.currentTimeMillis()

    var fromState = startState
    var ok = true
    var reason: String? = null
    val steps = mutableListOf<TargetOnePlanResult>()
    val path = mutableListOf<State>()

    var expandedCount = 0L
    var cost = 0.0
    var minF = 0.0
    var timeNum = 0L

    for ((ti, toState) in goalStates.withIndex()) {
      val sr = search(ti, timeNum, fromState, toState, goalStopTime, -1)
      expandedCount += sr.expandedCount

      if (!sr.ok) {
        ok = false
        reason = sr.reason
        break
      }

      steps += sr
      path.addAll(sr.path)

      cost += sr.cost
      minF += sr.minF
      timeNum += sr.timeNum

      fromState = toState
    }

    return TargetManyPlanResult(
      robotName,
      ok,
      reason,
      cost = cost,
      minF = minF,
      expandedCount = expandedCount,
      planCost = System.currentTimeMillis() - startOn,
      timeNum = timeNum, timeStart = 0, timeEnd = timeNum,
      steps = steps, path = path,
    )
  }

  // 两点之间的直线路径
  private fun admissibleHeuristic(from: Location, target: Location): Double =
    (abs(from.x - target.x) + abs(from.y - target.y)).toDouble() / moveUnitCost
  // GeoHelper.euclideanDistance(from.x.toDouble(), from.y.toDouble(), target.x.toDouble(), target.y.toDouble())

  // 故意 inadmissible 的启发式
  private fun focalStateHeuristic(toState: State, toStateG: Double): Double =
    focalStateHeuristic2?.invoke(toState, toStateG) ?: toStateG

  private fun focalTransitionHeuristic(fromState: State, toState: State, fromStateG: Double, toStateG: Double): Double =
    focalTransitionHeuristic2?.invoke(fromState, toState, fromStateG, toStateG) ?: (toStateG - fromStateG)

  private fun stateToIndex(x: Int, y: Int) = x + y * mapDimX

  private fun getNeighbors(targetIndex: Int, fromState: State, goalState: State, goalStopTime: Int): List<State> {
    val neighbors = mutableListOf<State>()
    addValidNeighbor(targetIndex, neighbors, fromState, goalState, goalStopTime, 0, 0, fromState.head) // waiting
    addValidNeighbor(targetIndex, neighbors, fromState, goalState, goalStopTime, 1, 0, 0)
    addValidNeighbor(targetIndex, neighbors, fromState, goalState, goalStopTime, -1, 0, 180)
    addValidNeighbor(targetIndex, neighbors, fromState, goalState, goalStopTime, 0, 1, 90)
    addValidNeighbor(targetIndex, neighbors, fromState, goalState, goalStopTime, 0, -1, 270)
    return neighbors
  }

  // toHead 目标车头朝向
  private fun addValidNeighbor(
    targetIndex: Int,
    neighbors: MutableList<State>,
    fromState: State,
    goalState: State,
    goalStopTime: Int,
    dx: Int,
    dy: Int,
    toHead: Int,
  ) {
    val x = fromState.x + dx
    val y = fromState.y + dy

    if (x < 0 || x >= mapDimX || y < 0 || y >= mapDimY || obstacles.contains(stateToIndex(x, y))) return

    // 需要转的角度，初始，-270 ~ +270
    var dHead = abs(toHead - fromState.head)
    // 270 改成 90
    if (dHead > 180) dHead = 90
    dHead /= 90

    // 耗时，也作为 g 的增量
    // 假设 dx/dy 1 是 1 米
    var timeNum = ceil(abs(dx + dy).toDouble() / moveUnitCost + dHead / rotateUnitCost).toLong()
    if (timeNum < 1) timeNum = 1 // 原地等待

    val newState = State(
      x = x,
      y = y,
      head = toHead,
      timeStart = fromState.timeEnd + 1,
      timeEnd = fromState.timeEnd + timeNum,
      timeNum = timeNum,
    )

    // 最后一步要等待
    val testState = if (goalState.isSameLocation(newState)) {
      newState.copy(timeEnd = newState.timeEnd + goalStopTime, timeNum = newState.timeNum + goalStopTime)
    } else {
      newState
    }

    if (isValidNeighbor != null && !isValidNeighbor.invoke(fromState, testState)) {
      // logger.debug("[H=$highNodeId][R=$robotName][$targetIndex] Bad neighbor: $newState <- $fromState")
      return
    }
    neighbors += newState
  }

  data class Node(
    val state: State,
    val parent: Node?,
    var f: Double,
    val focalHeuristic: Double,
    var g: Double, // 到这个节点的实际成本
  )

  /**
   * 单目标
   */
  data class TargetOnePlanResult(
    val robotName: String,
    val ok: Boolean = true,
    val reason: String? = null,
    val cost: Double = 0.0,
    val minF: Double = 0.0,
    val expandedCount: Long = 0,
    val planCost: Long = 0, // 毫秒
    val timeNum: Long = 0,
    val timeStart: Long = -1,
    val timeEnd: Long = -1,
    val fromState: State,
    val toState: State,
    val path: List<State> = emptyList(),
  )

  /**
   * 多目标
   */
  data class TargetManyPlanResult(
    val robotName: String,
    val ok: Boolean = true,
    val reason: String? = null,
    val cost: Double = 0.0,
    val minF: Double = 0.0,
    val expandedCount: Long = 0,
    val planCost: Long = 0, // 毫秒
    val timeNum: Long = 0,
    val timeStart: Long = -1,
    val timeEnd: Long = -1,
    val steps: List<TargetOnePlanResult> = emptyList(),
    val path: List<State> = emptyList(), // 总路径
  )
}