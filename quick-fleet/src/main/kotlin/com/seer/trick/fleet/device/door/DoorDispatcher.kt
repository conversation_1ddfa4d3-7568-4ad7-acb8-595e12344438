package com.seer.trick.fleet.device.door

import com.seer.trick.fleet.domain.MapPath
import com.seer.trick.fleet.service.MoveActionRuntime
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.SceneRuntime
import com.seer.trick.fleet.traffic.TrafficTaskRuntime
import com.seer.trick.fleet.traffic.TrafficTaskStatus
import org.slf4j.LoggerFactory
import java.util.*

/**
 * 门调度。
 * 发开门需求。经过门后发关门需求。
 * 取消需求。
 */
object DoorDispatcher {
  
  private val logger = LoggerFactory.getLogger(javaClass)
  
  /**
   * 下发动作前看看是否需要确保开门或提前开门。
   * 处理器注意监控动作和交管任务被取消。
   * 此方法不能抛异常，如果有意外，返回 false。自己负责产生告警信息。
   */
  fun beforeSendingMoveActionToRobot(
    rr: RobotRuntime,
    ma: MoveActionRuntime,
    tt: TrafficTaskRuntime,
  ): <PERSON><PERSON><PERSON> {
    try {
      val extra = ma.req.extra
      val doorId = extra["doorId"] as Int? ?: return true
      
      val dr = rr.sr.doors[doorId] ?: return true
      
      // 提前开门的路径
      val preOpenDoorPath = extra["preOpenDoorPath"] as String?
      
      // 确保开门的路径
      val ensureDoorOpenPath = extra["ensureDoorOpenPath"] as String?
      
      val mainStatus = dr.adapter.report().mainStatus
      
      // 先判断需要强制开门
      if (!ensureDoorOpenPath.isNullOrBlank()) {
        // 在强制开门的路径上了
        // 不管门是否开，强制发一次开门
        val dd = OpenDoorDemand(
          robotName = rr.robotName,
          fromPath = ensureDoorOpenPath,
          underPath = ensureDoorOpenPath,
          pre = false,
          tt = tt,
        )
        addOpenDemand(dr, dd)
        
        logger.info("Before sending action to robot, ensure door open [$dr], $dd")
        openOrCloseDoorAsNeeded(rr.sr)
        
        // 已开，能下发，否则不能下发
        return mainStatus == DoorMainStatus.Opened
      }
      
      // 发提前开门
      if (!preOpenDoorPath.isNullOrBlank() && !ensureDoorOpenPath.isNullOrBlank()) {
        // 在提前开门的路径上了
        val dd = OpenDoorDemand(
          robotName = rr.robotName,
          fromPath = preOpenDoorPath,
          underPath = ensureDoorOpenPath,
          pre = true,
          tt = tt,
        )
        addOpenDemand(dr, dd)
        logger.info("Before sending action to robot, pre-open door [$dr], $dd")
        openOrCloseDoorAsNeeded(rr.sr)
        
        return true
      }
      
      return true
    } catch (e: Exception) {
      logger.error("beforeSendingMoveAction", e)
      return false
    }
  }
  
  /**
   * 如果没有相同命令则添加并返回 true，否则返回 false
   */
  private fun addOpenDemand(dr: DoorRuntime, dd: OpenDoorDemand): Boolean {
    // 按机器人查询是否有开门需求
    val old = dr.openDemands.find { it.robotName == dd.robotName }
    if (old != null) {
      // 把提前开门指令替换成强制开门指令
      if (!old.pre && dd.pre) {
        dr.openDemands.remove(old)
        dr.openDemands.add(dd)
      }
      return false
    } else {
      dr.openDemands.add(dd)
      return true
    }
  }
  
  /**
   * 机器人执行完一个动作，尝试关门
   */
  fun afterMoveActionDone(rr: RobotRuntime, ma: MoveActionRuntime) {
    val underPath = ma.req.extra["ensureDoorOpenPath"] as String?
    // 已走完门上的路径才去关门
    if (underPath.isNullOrBlank() || underPath != MapPath.getKey(ma.req.fromPointName, ma.req.toPointName)) return
    afterRobotLeaveUnderPath(rr.sr, rr, underPath)
  }
  
  /**
   * 当机器人离开门下路径，删除需求，尝试关门
   */
  private fun afterRobotLeaveUnderPath(sr: SceneRuntime, rr: RobotRuntime, underPath: String) {
    sr.withOrderLock {
      // 这个路径上可能有多个门
      val doors = sr.doors.values.filter { it.config.controlledPathKeys.contains(underPath) }
      for (dr in doors) {
        logger.info(
          "Robot ${rr.robotName} leave under path $underPath, remove open demands for door [$dr]",
        )
        // 按机器人名和路径删除开门需求
        dr.openDemands.removeIf { it.robotName == rr.robotName && it.underPath == underPath }
      }
    }
    
    openOrCloseDoorAsNeeded(sr)
  }
  
  /**
   * 如果机器人要经过门下的路径，确保门是开的。
   * 可能路径上有多个门。
   */
  fun awaitDoorOpenIfNeed(sr: SceneRuntime, nextPath: String) {
    val allDoors = sr.doors.values.toList()
    if (allDoors.isEmpty()) return
    
    while (true) {
      // TODO 处理抛异常
      var allOpen = true
      for (dr in allDoors) {
        if (!dr.config.controlledPathKeys.contains(nextPath)) continue
        val report = dr.adapter.report()
        // TODO 故障告警
        if (report.mainStatus != DoorMainStatus.Opened) {
          dr.adapter.openDoor("await open")
          allOpen = false
        }
      }
      if (allOpen) return
    }
  }
  
  /**
   * 检查是否需要开门或关门。
   * 不抛异常。
   */
  fun openOrCloseDoorAsNeeded(sr: SceneRuntime) {
    try {
      for (dr in sr.doors.values) {
        // 去掉已取消、已完成的交管任务的开门请求
        sr.withOrderLock {
          dr.openDemands.removeIf {
            it.tt.status == TrafficTaskStatus.Cancelled ||
              it.tt.status == TrafficTaskStatus.Success
          }
        }
        
        if (dr.openDemands.isNotEmpty()) {
          try {
            dr.adapter.openDoor("With open demands")
          } catch (e: Exception) {
            logger.error("Failed to open door $dr", e)
          }
        } else {
          try {
            // 仅在已开、正在开的情况下关
            if (dr.status == DoorMainStatus.Opened || dr.status == DoorMainStatus.Opening) {
              dr.adapter.closeDoor("No more demands")
            }
          } catch (e: Exception) {
            logger.error("Failed to close door $dr", e)
          }
        }
      }
    } catch (e: Exception) {
      logger.error("doOpenDoorAsNeeded", e)
    }
  }
  
  /**
   * 预处理交管下发给 MoveService 的动作。
   */
  fun prepareMoveActions(rr: RobotRuntime, moves: List<MoveActionRuntime>) {
    if (moves.isEmpty()) return // 容错
    val doors = rr.sr.doors.values.toList()
    if (doors.isEmpty()) return
    
    // 未来要走的路径，用于判断提前开门
    val futurePaths = buildFuturePath(rr, moves)
    
    for (move in moves) {
      if (move.req.fromPointName == move.req.toPointName) continue // 原地
      
      val extra = move.req.extra
      val pathKey = MapPath.getKey(move.req.fromPointName, move.req.toPointName)
      for (dr in doors) {
        if (pathKey in dr.config.controlledPathKeys) {
          // 这个 move 经过门，必须确保开门
          extra["doorId"] = dr.config.id
          extra["ensureDoorOpenPath"] = pathKey
          logger.info(
            "Prepare MoveAction: ensure door open [${dr.config.id}][${dr.config.name}], under path=$pathKey," +
              " move=${move.descId()}",
          )
        }
        for ((underPath, prePaths) in dr.preOpenPathPaths) {
          if (underPath != pathKey && underPath in futurePaths && pathKey in prePaths) {
            // 未来路径（underPath）上有门，当前路径是提前开门路径
            extra["doorId"] = dr.config.id
            extra["preOpenDoorPath"] = pathKey
            extra["ensureDoorOpenPath"] = underPath
            logger.info(
              "Prepare MoveAction: pre-open door [${dr.config.id}][${dr.config.name}], under path=$underPath, " +
                "pre path=$pathKey, move=${move.descId()}",
            )
          }
        }
      }
    }
  }
  
  /**
   * moves 是交管下发的路径。从这些路径开始，返回机器人未来预计要走的路。
   */
  private fun buildFuturePath(rr: RobotRuntime, moves: List<MoveActionRuntime>): List<String> {
    val futurePaths = mutableListOf<String>()
    
    // TODO 更好的获取未来路径的方法
    val untraveledPointNames =
      rr.sr.trafficService.showTrafficResourceMessage(rr.robotName)?.pathResource?.unTravelPointNames
    
    // 下发的路径的第一段的起点
    val moveStart = moves[0].req.fromPointName // 注意这里是 fromPointName
    if (!untraveledPointNames.isNullOrEmpty()) {
      var fromPointName = moveStart
      // 有一部分 untraveledPointNames 已下发了，但机器人还未执行
      val firstIndex = untraveledPointNames.indexOf(fromPointName)
      if (firstIndex >= 0) {
        for (pi in firstIndex + 1 until untraveledPointNames.size) {
          val p = untraveledPointNames[pi]
          if (p == fromPointName) continue
          futurePaths.add(MapPath.getKey(fromPointName, p))
          fromPointName = p
        }
      }
    }
    return futurePaths
  }
}

/**
 * 开门需求
 */
data class OpenDoorDemand(
  val robotName: String,
  val fromPath: String, // 从哪个路径发起的
  val underPath: String, // 要经过门下哪个路径
  val pre: Boolean, // 是否是提前开门
  val tt: TrafficTaskRuntime, // 记交管任务，用来判断开门是否可以取消
  val timestamp: Date = Date(),
)