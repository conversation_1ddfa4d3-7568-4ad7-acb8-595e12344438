package com.seer.trick.fleet.device.lift.adapter

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.I18N
import com.seer.trick.base.alarm.AlarmItem
import com.seer.trick.base.alarm.AlarmLevel
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.base.script.ScriptCenter
import com.seer.trick.base.script.ScriptExeRequest
import com.seer.trick.base.script.ScriptTraceContext
import com.seer.trick.fleet.device.lift.LiftAdapterReport
import com.seer.trick.fleet.device.lift.SceneLift
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.SceneRuntime
import org.slf4j.LoggerFactory

class LiftAdapterScript(val config: SceneLift) : LiftAdapter() {

  private val logger = LoggerFactory.getLogger(javaClass)

  override fun init(sr: SceneRuntime) {
    logger.info("Init lift ${config.id}:${config.name}")
  }

  override fun dispose() {
    // TODO 是否需要释放的相关资源？
    logger.info("Dispose lift ${config.id}:${config.name}")
  }

  override fun report(): LiftAdapterReport {
    val funName = config.reportFunName ?: "scriptLiftAdapterReport"
    if (ScriptCenter.exists(funName)) {
      return ScriptCenter.execute(ScriptExeRequest(funName, arrayOf()), jacksonTypeRef())
    }

    logger.error(I18N.lo("errNoReportLiftFunc", listOf(config.id, config.name, funName)))
    val ai = AlarmItem(
      group = "Fleet",
      code = "ReportLiftFailed",
      key = "ReportLiftFailed-${config.name}",
      level = AlarmLevel.Error,
      message = I18N.lo("errNoReportLiftFunc", listOf(config.id, config.name, funName)),
    )
    AlarmService.addItem(ai, 5000L)

    return super.report().copy(
      fault = true,
      faultMsg = I18N.lo("errNoReportLiftFunc", listOf(config.id, config.name, funName)),
    )
  }

  override fun gotoOpenDoor(rr: RobotRuntime?, targetFloor: Int, remark: String) {
    val funName = config.openFunName ?: "scriptLiftAdapterOpenDoor"
    if (ScriptCenter.exists(funName)) {
      logger.info("Open door of lift ${config.id}:${config.name}")
      ScriptCenter.execute(ScriptExeRequest(funName, arrayOf(targetFloor, remark)))
    } else {
      logger.error(I18N.lo("errNoOpenLiftFunc", listOf(config.id, config.name, funName)))
      throw BzError("errNoOpenLiftFunc", config.id, config.name, funName)
    }
  }

  override fun closeDoor(rr: RobotRuntime?, remark: String) {
    val funName = config.closeFunName ?: "scriptLiftAdapterCloseDoor"
    if (ScriptCenter.exists(funName)) {
      logger.info("Close door of lift ${config.id}:${config.name}")
      ScriptCenter.execute(ScriptExeRequest(funName, arrayOf(remark)))
    } else {
      logger.error(I18N.lo("errNoCloseLiftFunc", listOf(config.id, config.name, funName)))
      throw BzError("errNoCloseLiftFunc", config.id, config.name, funName)
    }
  }
}