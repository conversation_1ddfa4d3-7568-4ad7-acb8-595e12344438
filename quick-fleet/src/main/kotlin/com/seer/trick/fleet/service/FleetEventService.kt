package com.seer.trick.fleet.service

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.base.script.ScriptCenter
import com.seer.trick.base.script.ScriptExeRequest
import com.seer.trick.base.script.UnitScriptExeResult
import com.seer.trick.helper.ThreadHelper
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 调度事件中心
 * 事件机制；事件列表：https://seer-group.feishu.cn/wiki/Ps3Nwn1xciUibekexSfcCX35nQb
 */
object FleetEventService {
  
  private val logger = LoggerFactory.getLogger(javaClass)
  
  // 内部事件监听器和脚本的放在一起混排
  // 本身的线程安全问题由集合本身解决：靠线程安全集合
  private val listeners: MutableMap<String, MutableList<FleetEventListener>> = ConcurrentHashMap()
  
  // 事件的异步执行器
  private val eventAsyncExecutor = ThreadHelper.newBoundedThreadPool(2, 20)
  
  /**
   * 触发事件
   * TODO 同步触发应该没有时序问题。但异步触发，如果执行器是一个大池子，根据线程调度，可能后发生的事件先出发。
   * 最简单的坚决办法是单线程，但可能阻塞（不同事件）。更好的办法的一个每种实现单个线程，但可能增加很多线程，因为事件很多。
   */
  fun fire(event: FleetEvent) {
    // TODO 日志
    val listeners = listeners[event.name] ?: return
    
    // TODO 线程安全遍历
    for (listener in listeners) {
      if (listener.async) {
        eventAsyncExecutor.submit {
          try {
            process(event, listener)
          } catch (e: Exception) {
            // TODO 改善异常日志。异步失败了是否要界面通知，比如回调第三方的。
            logger.error("Uncaught error, event=${event.name}, listener=${listener.id}", e)
          }
        }
      } else {
        process(event, listener)
      }
    }
  }
  
  // 可能是内部监听器 或 脚本执行
  private fun process(event: FleetEvent, listener: FleetEventListener) {
    if (listener is FleetEventInternalListener) {
      listener.process(event)
    } else if (listener is FleetEventScriptListener) {
      val r: UnitScriptExeResult = ScriptCenter.execute(
        ScriptExeRequest(listener.scriptFunc, args = arrayOf(event)),
        jacksonTypeRef(),
      )
      if (!r.ok) throw BzError("errBzError", r.errorMsg)
    }
  }
  
  /**
   * 注册监听器。
   * 监听器的 id 不能重复：检测的原因是防止重复添加监听器
   * 加主锁的原因：防止重复添加
   * TODO 只监听特定场景的？
   */
  fun on(eventName: String, listener: FleetEventListener) {
    val listeners = listeners.getOrPut(eventName) { CopyOnWriteArrayList() }
    if (listeners.any { it.id == listener.id }) {
      throw BzError("errCodeErr", "Old listener with same id '${listener.id}'")
    }
    listeners.add(listener)
    listeners.sortBy { it.order }
  }
  
  /**
   * 注销监听器。
   * 忽略删除不存在的监听器的问题。
   * 不需要加主锁：应该没有并发问题
   */
  fun off(eventName: String, id: String) {
    val list = listeners[eventName] ?: return
    list.removeIf { it.id == id }
  }
}

/**
 * 调度事件
 */
data class FleetEvent(
  val name: String, // 事件名
  val sceneId: String, // 所属场景
  val robotName: String? = null, // 关联机器人，可能为空
  val timestamp: Date = Date(), // 发生事件
  val extra: Any? = null // 额外信息，只能放简单类型或简单类型的组合，不要放不可序列化的对象
)

/**
 * 事件处理器
 */
interface FleetEventListener {
  
  val id: String // 事件处理器唯一标识
  val order: Int // 事件处理顺序，越小越优先
  val async: Boolean // 是否异步处理
  
}

/**
 * 内部事件处理器，标准行为
 */
class FleetEventInternalListener(
  override val id: String,
  override val order: Int,
  override val async: Boolean,
  val process: (event: FleetEvent) -> Unit,
) : FleetEventListener

/**
 * 脚本事件处理器，用于定制
 */
class FleetEventScriptListener(
  override val id: String,
  override val order: Int,
  override val async: Boolean,
  val scriptFunc: String, // 脚本函数名
) : FleetEventListener