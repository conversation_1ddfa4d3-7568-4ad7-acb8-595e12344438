package com.seer.trick.fleet.algorithm

import com.seer.trick.BzError
import com.seer.trick.fleet.diagnosis.RobotOrderPrediction
import com.seer.trick.fleet.domain.PreAllocation
import com.seer.trick.fleet.order.DispatchOrderService
import com.seer.trick.fleet.order.OrderRuntime
import com.seer.trick.fleet.service.MapService.NOT_ACHIEVABLE_COST
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.SceneRuntime
import com.seer.trick.helper.ThreadHelper.newBoundedThreadPool
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.Future

/**
 * 贪心匹配。
 * 先按优先级，再按成本（距离），最后按创建时间排序。无其他额外加权。
 */
class GreedyMatchingAlgorithm : MatchingAlgorithm {

  private val logger = LoggerFactory.getLogger(javaClass)

  override fun match(
    sr: SceneRuntime,
    orders: List<OrderRuntime>,
    robots: List<RobotRuntime>,
  ): List<PreAllocation> {
    val allocations: MutableList<PreAllocation> = Collections.synchronizedList(ArrayList()) // TODO 实现，有序数组
    val futures: MutableList<Future<*>> = ArrayList()

    for (or in orders) {
      val order = or.order

      for (rr in robots) {
        // 机器人没有指定机器人也没有指定机器人组时，是根据关键位置来匹配机器人
        if (order.expectedRobotNames.isNullOrEmpty()) {
          // 若没有指定机器人，而指定了机器人组则检查机器人组是否匹配
          if (!order.expectedRobotGroups.isNullOrEmpty() &&
            !order.expectedRobotGroups.contains(rr.mustGetGroup().name)
          ) {
            continue
          }
        } else {
          // 若指定机器人则忽略指定机器人组，只根据指定机器人判断是否匹配
          if (!order.expectedRobotNames.contains(rr.robotName)) continue
        }

        futures += dispatchCalcExecutor.submit {
          // 异步执行，再测一次
          if (RobotOrderPrediction.checkRobotAvailableForNewBzOrders(rr) != null) return@submit

          val cost = try {
            val result = DispatchOrderService.estimateOrderCost(or, rr)
            if (result == NOT_ACHIEVABLE_COST) return@submit else result
          } catch (_: BzError) {
            return@submit
          }
          if (cost >= 0 && cost != Double.MAX_VALUE) {
            allocations += PreAllocation(
              robotName = rr.robotName,
              orderId = order.id,
              priority = order.priority,
              cost = cost,
              createdOn = order.createdOn,
            )
          }
        }
      }
    }

    for (f in futures) {
      try {
        f.get()
      } catch (e: Exception) {
        logger.error("build allocation", e)
      }
    }

    // 先处理的放前面
    return allocations.sortedWith { a1, a2 ->
      DispatchOrderService.compareOrders(sr, a1.priority, a1.cost, a1.createdOn, a2.priority, a2.cost, a2.createdOn)
    }
  }

  companion object {
    /**
     * 计算派单成本
     */
    val dispatchCalcExecutor = newBoundedThreadPool(1, 5)
  }
}