package com.seer.trick.fleet.mock

import com.seer.trick.BzError
import com.seer.trick.base.concurrent.PollingJobManager
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.mock.service.MockMoveProcessor
import com.seer.trick.fleet.mock.service.MockService
import com.seer.trick.fleet.mock.tcp.ConnectionManager
import com.seer.trick.fleet.seer.PathHelper
import com.seer.trick.fleet.seer.SmapFile
import com.seer.trick.fleet.service.SceneFileService
import org.slf4j.LoggerFactory
import org.tinyspline.BSpline
import java.util.concurrent.*

/**
 * 表示一个仿真机器人
 *
 * TODO 优化：增加 ltStatus，管理当前仿真机器人 Runtime 的状态
 */
class MockSeerRobotRuntime(
  val robotId: String, // 机器人在仿真系统中的唯一 ID，注意不是机器人名，机器人名是可以改的。
  config: MockSeerRobotConfig,
  record: MockSeerRobotRecord?,
) {
  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 机器人的配置信息
   */
  @Volatile
  var config = MockSeerRobotConfig()
    private set

  /**
   * 机器人的模型
   */
  @Volatile
  var model: RbkModel? = null
    private set

  /**
   * 机器人的运行记录
   */
  @Volatile
  var record = MockSeerRobotRecord()
    private set

  /**
   * 机器人的移动配置，执行 3066 时才有
   */
  @Volatile
  var moveConfig: MoveConfig? = null

  /**
   * 机器人当前的速度，执行 3066 时才有
   */
  @Volatile
  var currentSpeed: CurrentSpeed = CurrentSpeed()

  /**
   * 仿真机器人标签
   */
  val tag: String = "MockRobot-$robotId"

  /**
   * Smap 缓存，键是文件名
   */
  private val smapCache: MutableMap<String, SmapFile> = ConcurrentHashMap()

  /**
   * 执行路径导航任务单独的线程
   */
  var taskExecutor: ExecutorService = Executors.newSingleThreadExecutor()
  var future: Future<*>? = null

  // 运行中、失败、取消、失败等的任务状态列表
  val tasks: MutableMap<String, MoveTaskRuntime> = ConcurrentHashMap()

  /**
   * 当前正在执行的路径导航任务
   */
  @Volatile
  var currentTask: MoveTaskRuntime? = null

  // 急停，对应实车的物理急停
  @Volatile
  var emc = false

  /**
   * 软急停
   *  对于仿真机器人来说，其实没必要区分 物理急停 和 软急停，
   *  但是为了保证仿真效果，以及统一数据格式和内容，仿真机器人也加上软急停状态。
   *  TODO 软急停其实没必要持久化，但是先持久化吧。
   */
  @Volatile
  var softEmc = false

  // 被阻挡
  @Volatile
  var blocked = false

  @Volatile
  var movePaused = false
  val lock = Object()

  private var connectionManager: ConnectionManager? = null

  // val msgProcessor = MessageProcessor(this)

  // MoveProcessor 相关

  @Volatile
  var moveOrCanceledFinished: Boolean = true

  @Volatile
  var moveTaskList = LinkedBlockingQueue<List<MoveTaskRuntime>>()

  val splineMap: MutableMap<String, Pair<PathHelper, BSpline>> = mutableMapOf()

  val curvePoint2DMap: MutableMap<String, List<CurvePoint2D>> = mutableMapOf()

  init {
    // 兼容老版本地图路径带有场景目录
    val maps = config.maps.map { it.copy(mapFile = SceneFileService.subpath(config.sceneId, it.mapFile)) }
    this.config = config.copy(maps = maps)
    if (record != null) this.record = record
    // 加载地图
    reloadSmapCache()
    reloadModelFile()
    connectionManager = ConnectionManager(this)

    // 根据站点，初始化 x, y 坐标
    if (record != null && !record.point.isNullOrBlank()) {
      val pointHelper = MockSmapHelper.getPointHelper(config.id, record.point)
      if (pointHelper != null) {
        this.record = record.copy(x = pointHelper.point.pos.x, y = pointHelper.point.pos.y)
      }
    }

    // 3066 导航单独线程
    PollingJobManager.submit(
      threadName = getMoveThreadName(this),
      remark = "mock robot move for: ${this.config.name}",
      interval = { 200 },
      logger = logger,
      workerMaxTime = 1500L,
      stopCondition = { this.config.disabled },
      exceptionContinue = true,
      tags = setOf(this.tag),
      worker = { MockMoveProcessor.process3066Move(this) },
    )
  }

  fun getMoveThreadName(rr: MockSeerRobotRuntime): String = "MockRobotMove-${rr.config.name}"

  fun dispose() {
    // 删除相关后台任务
    PollingJobManager.removeByTag(tag)
    connectionManager?.dispose()
    taskExecutor.shutdown()
    future?.cancel(true)
  }

  /**
   * 更新配置
   *
   * 配置变化后，自动重置链接，不情况现有的指令、任务，可以继续执行
   */
  fun setConfig(config: MockSeerRobotConfig) {
    try {
      val shouldResetConnections = hasTcpConfigChanged(config)
      this.config = config
      if (shouldResetConnections) {
        connectionManager?.resetConnections()
      }
    } catch (e: Exception) {
      throw BzError("errSetConfig", "更新配置失败:${e.cause?.message}", e.cause)
    }
  }

  // TODO 封装一个 setRecord 函数，只更新一部分属性。因为在外面 copy record 后，可能会有并发问题
  fun setRecord(record: MockSeerRobotRecord) {
    this.record = record
    synchronized(lock) {
      this.emc = record.emc
      this.softEmc = record.softEmc
      this.blocked = record.blocked
      lock.notifyAll()
    }
    // MockStore.updateMockRobotRecordAsync(record)
  }

  /**
   * cas 保证更新的正确性，避免数据丢失或不一致
   */
  fun compareAndSetRecord(expect: MockSeerRobotRecord, update: MockSeerRobotRecord): Boolean = synchronized(this) {
    if (record === expect) {
      record = update
      true
    } else {
      false
    }
  }

  /**
   * 与 tcp 相关的配置是否变化，如果变化才 reset 链接
   */
  private fun hasTcpConfigChanged(config: MockSeerRobotConfig): Boolean =
    config.fleetTcpServerIp != this.config.fleetTcpServerIp ||
      config.fleetTcpServerPort != this.config.fleetTcpServerPort ||
      config.robotTcpServerPortStart != this.config.robotTcpServerPortStart

  /**
   * 暂停移动
   */
  fun pause() {
    synchronized(lock) {
      movePaused = true
    }
  }

  /**
   * 继续移动
   */
  fun resume() {
    synchronized(lock) {
      movePaused = false
      lock.notifyAll()
    }
  }

  // 重连服务器
  fun reconnect() {
    connectionManager?.resetConnections(false)
  }

  fun disconnect() {
    connectionManager?.disconnect()
  }

  fun reloadSmapCache() {
    smapCache.clear()
    config.maps.forEach { map ->
      map.toSmapFile(config.sceneId)?.let { smapCache[map.mapName] = it }
    }
    registerMap()
  }

  fun registerMap() {
    record.map?.takeIf { it.isNotBlank() }?.let { mapName ->
      smapCache[mapName]?.let { MockSmapHelper.loadMap(it, this) }
    }
  }

  fun toMockRobotRealtimeFrame(): MockRobotRealtimeFrame = MockRobotRealtimeFrame(
    config,
    record,
    // 过滤，仅展示待执行或者执行中的任务
    if (currentTask?.status == MoveTaskStatus.Init || currentTask?.status == MoveTaskStatus.Running) {
      currentTask
    } else {
      null
    },
  )

  fun getCurrentSmapFile(): SmapFile? = record.map?.takeIf { it.isNotBlank() }?.let { smapCache[it] }

  /**
   * 删除当前地图缓存
   */
  fun clearCurrentMapCache() = smapCache.remove(record.map)

  /**
   * 重新加载当前地图缓存
   */
  fun reloadCurrentMapCache() {
    val map = config.maps.find { it.mapName == record.map }
    map?.toSmapFile(config.sceneId)?.let {
      smapCache[map.mapName] = it
      MockSmapHelper.loadMap(it, this)
    }
  }

  /**
   * 更新当前任务
   */
  fun updateCurrentTask(taskRuntime: MoveTaskRuntime) {
    currentTask = taskRuntime
    tasks[taskRuntime.taskId] = taskRuntime
  }

  /**
   * 设置当前任务故障
   */
  fun setCurrentTaskFailed() {
    MockMoveProcessor.triggerTaskError(this, MoveTaskStatus.Failing)
  }

  /**
   * 设置当前任务取消
   */
  fun setCurrentTaskCancelled() {
    MockMoveProcessor.triggerTaskError(this, MoveTaskStatus.Cancelling)
  }

  /**
   * 抢占控制权
   */
  fun takeController(nickName: String) {
    record.currentLock.nickName = nickName
    record.currentLock.locked = true
    record = record.copy(master = nickName)
  }

  /**
   * 更新 tasks 中所有不处于完成状态的任务
   */
  fun updateTasksStatus(statusUpdater: (MoveTaskRuntime) -> MoveTaskStatus) {
    tasks.forEach { (taskId, taskRuntime) ->
      taskRuntime.takeIf {
        !(it.status.isFinal())
      }?.let {
        logger.debug("更新任务 $taskId 状态: ${it.status} -> ${statusUpdater(it)}")
        tasks[taskId] = it.copy(status = statusUpdater(it))
      }
    }
  }

  /**
   * 加载本地的机器人模型文件
   */
  private fun reloadModelFile() {
    this.model = MockStore.loadMockRobotModel(config.id)
  }

  fun getMockRobotModel(): RbkModel {
    if (this.model == null) reloadModelFile()
    return this.model!!
  }

  /**
   * 更新机器人的模型文件（内存+本地文件）
   */
  fun updateAndSaveMockRobotModel(newModel: RbkModel) {
    // TODO 模型文件变更后再更新

    // 更新内存
    this.model = newModel

    // 更新本地文件
    MockStore.saveMockRobotModel(config.id, newModel)
  }

  /**
   * 最大旋转速度
   */
  fun maxAngularVelocity(): Double? = moveConfig?.let { Math.toRadians(it.maxRotSpeed!!) * MockService.mockSpeedFactor }
}