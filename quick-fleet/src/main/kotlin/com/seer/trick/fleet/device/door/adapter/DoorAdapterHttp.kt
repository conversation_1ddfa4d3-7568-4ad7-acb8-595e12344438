package com.seer.trick.fleet.device.door.adapter

class DoorAdapterHttp : DoorAdapter()

// // 存储门状态的缓存
// private val doorStateCache = ConcurrentHashMap<String, DoorState>()
//
// // 基础配置，优先从 DeviceConfig.extraOptions 中获取
// private val baseUrl = config.extraOptions["baseUrl"] as? String ?: "http://${config.ip}:${config.port}"
// private val authToken = config.extraOptions["authToken"] as? String
// private val username = config.extraOptions["username"] as? String
// private val password = config.extraOptions["password"] as? String
// private val requestTimeout = (config.extraOptions["requestTimeout"] as? Number)?.toLong() ?: 5000L
// private val traceHttp = config.extraOptions["traceHttp"] as? Boolean ?: true
//
// // 默认的 HTTP 路径配置
// private val defaultApiPaths = mapOf(
//   "open" to "/api/door/open",
//   "close" to "/api/door/close",
//   "status" to "/api/door/status",
//   "ping" to "/ping",
// )
//
// /**
//  * 初始化设备
//  */
// fun initialize() {
//   FleetLogger.info(
//     module = "DeviceHandler",
//     subject = "initialize",
//     sr = runtime.sr,
//     robotName = null,
//     msg = mapOf(
//       "deviceName" to config.deviceName,
//       "deviceType" to config.deviceType,
//     ),
//   )
//
//   if (config.protocol != DeviceProtocolType.HTTP) {
//     throw RuntimeException("Unsupported protocol types: ${config.protocol}, must be HTTP")
//   }
//
//   try {
//     // 验证连接
//     FleetLogger.error(
//       module = "DeviceHandler",
//       subject = if (isConnected()) "connectFail" else "initializeSuccess",
//       sr = runtime.sr,
//       robotName = null,
//       msg = mapOf(
//         "deviceName" to config.deviceName,
//         "deviceType" to config.deviceType,
//       ),
//     )
//   } catch (e: Exception) {
//     FleetLogger.error(
//       module = "DeviceHandler",
//       subject = "initializeFail",
//       sr = runtime.sr,
//       robotName = null,
//       msg = mapOf(
//         "deviceName" to config.deviceName,
//         "deviceType" to config.deviceType,
//       ),
//       e = e,
//     )
//     throw e
//   }
// }
//
// /**
//  * 执行开门操作
//  */
// fun executeOpenDoor(context: DeviceActionContext): DeviceResponse {
//   try {
//     FleetLogger.info(
//       module = "DeviceHandler",
//       subject = "executeOpenDoor",
//       sr = runtime.sr,
//       robotName = context.robotId,
//       msg = mapOf(
//         "deviceName" to config.deviceName,
//         "deviceType" to config.deviceType,
//         "action" to context.getActionName(),
//         "context" to context,
//       ),
//     )
//
//     val doorStatus = getDoorStatus()
//
//     // 获取开门动作配置
//     val actionConfig = getActionConfig("open")
//       ?: return DeviceResponse.failed("Open action configuration not found")
//
//     // 发送开门命令
//     val response = executeHttpRequest("open", "{}")
//
//     if (!response.successful) {
//       return DeviceResponse.failed("Open door command failed: HTTP ${response.code}")
//     }
//
//     // 更新门状态缓存
//     updateDoorState(DoorDeviceStatus.OPENING)
//
//     // 如果需要等待开门完成
//     if (context.shouldWaitForCompletion()) {
//       // 等待门关闭到位
//       val timeout = context.request.config.timeout
//       val startTime = System.currentTimeMillis()
//
//       while (System.currentTimeMillis() - startTime < timeout) {
//         val status = getDoorStatus()
//         if (status == DoorDeviceStatus.OPENED) {
//           // 关门成功
//           return DeviceResponse.success("The door is open.")
//         }
//
//         if (status == DoorDeviceStatus.ERROR) {
//           return DeviceResponse.failed("Error during door opening")
//         }
//
//         TimeUnit.MILLISECONDS.sleep(500) // 等待500ms再检查
//       }
//
//       return DeviceResponse.failed("Open the door timeout", retryable = true)
//     }
//
//     // 如果是持续性操作，返回正在执行状态，避免被自动清理
//     if (actionConfig.isContinuous) {
//       return DeviceResponse(DeviceResponseStatus.RUNNING, "Continuous door opening")
//     }
//
//     return DeviceResponse.success("Door open command sent.")
//   } catch (e: Exception) {
//     FleetLogger.error(
//       module = "DeviceHandler",
//       subject = "executeOpenDoorFail",
//       sr = runtime.sr,
//       robotName = context.robotId,
//       msg = mapOf(
//         "deviceName" to config.deviceName,
//         "deviceType" to config.deviceType,
//         "action" to context.getActionName(),
//         "context" to context,
//       ),
//       e = e,
//     )
//     context.metadata["lastSuccess"] = false
//     return DeviceResponse.failed("Abnormal door-opening operation: ${e.message}")
//   }
// }
//
// /**
//  * 执行关门操作
//  */
// fun executeCloseDoor(context: DeviceActionContext): DeviceResponse {
//   try {
//     FleetLogger.info(
//       module = "DeviceHandler",
//       subject = "executeCloseDoor",
//       sr = runtime.sr,
//       robotName = context.robotId,
//       msg = mapOf(
//         "deviceName" to config.deviceName,
//         "deviceType" to config.deviceType,
//         "action" to context.getActionName(),
//         "context" to context,
//       ),
//     )
//     val doorStatus = getDoorStatus()
//
//     // 如果门已经关闭了，直接返回成功
//     if (doorStatus == DoorDeviceStatus.CLOSED) {
//       return DeviceResponse.success("The door is closed")
//     }
//
//     // 获取关门动作配置
//     val actionConfig = getActionConfig("close")
//       ?: return DeviceResponse.failed("Close action configuration not found")
//
//     // 发送关门命令
//     val response = executeHttpRequest("close", "{}")
//
//     if (!response.successful) {
//       return DeviceResponse.failed("Failed to send the close command: HTTP ${response.code}")
//     }
//
//     // 更新门状态
//     updateDoorState(DoorDeviceStatus.CLOSING)
//
//     // 如果需要等待关门完成
//     if (context.shouldWaitForCompletion()) {
//       // 等待门关闭到位
//       val timeout = context.request.config.timeout
//       val startTime = System.currentTimeMillis()
//
//       while (System.currentTimeMillis() - startTime < timeout) {
//         val status = getDoorStatus()
//         if (status == DoorDeviceStatus.CLOSED) {
//           // 关门成功
//           return DeviceResponse.success("The door is closed.")
//         }
//
//         if (status == DoorDeviceStatus.ERROR) {
//           return DeviceResponse.failed("Error during door closure")
//         }
//
//         TimeUnit.MILLISECONDS.sleep(500) // 等待500ms再检查
//       }
//
//       return DeviceResponse.failed("Closing timeout", retryable = true)
//     }
//
//     return DeviceResponse.running("The door is closing.")
//   } catch (e: Exception) {
//     FleetLogger.error(
//       module = "DeviceHandler",
//       subject = "executeCloseDoorFail",
//       sr = runtime.sr,
//       robotName = context.robotId,
//       msg = mapOf(
//         "deviceName" to config.deviceName,
//         "deviceType" to config.deviceType,
//         "action" to context.getActionName(),
//         "context" to context,
//       ),
//       e = e,
//     )
//     return DeviceResponse.failed("Abnormal door closing operation: ${e.message}")
//   }
// }
//
// /**
//  * 获取门的当前状态
//  */
// fun getDoorStatus(): DoorDeviceStatus {
//   
//   try {
//     // 发送状态查询请求
//     val response = executeHttpRequest("status")
//
//     if (!response.successful) {
//       FleetLogger.error(
//         module = "DeviceHandler",
//         subject = "getDoorStatusFail",
//         sr = runtime.sr,
//         robotName = null,
//         msg = mapOf(
//           "deviceName" to config.deviceName,
//           "deviceType" to config.deviceType,
//           "response" to response,
//         ),
//       )
//       return DoorDeviceStatus.ERROR
//     }
//
//     // 解析状态响应
//     // 这里假设API返回JSON格式，包含status字段：{"status": "OPENED"/"CLOSED"/"OPENING"/"CLOSING"/"ERROR"}
//     val body = response.bodyString
//     // 校验响应和解析逻辑应在这里实现
//
//     // todo 这里是示例返回，实际应该根据响应内容解析
//     return DoorDeviceStatus.OPENED
//   } catch (e: Exception) {
//     FleetLogger.error(
//       module = "DeviceHandler",
//       subject = "getDoorStatusFail",
//       sr = runtime.sr,
//       robotName = null,
//       msg = mapOf(
//         "deviceName" to config.deviceName,
//         "deviceType" to config.deviceType,
//       ),
//       e = e,
//     )
//     return DoorDeviceStatus.ERROR
//   }
// }
//
// /**
//  * 检查设备是否在线
//  */
// fun isConnected(): Boolean = try {
//   val response = executeHttpRequest("ping")
//   response.successful
// } catch (e: Exception) {
//   FleetLogger.error(
//     module = "DeviceHandler",
//     subject = "HttpConnectedFail",
//     sr = runtime.sr,
//     robotName = null,
//     msg = mapOf(
//       "deviceName" to config.deviceName,
//       "deviceType" to config.deviceType,
//     ),
//     e = e,
//   )
//   false
// }
//
// /**
//  * 获取设备错误信息
//  */
// fun getErrorDetails(): String? = if (getDoorStatus() == DoorDeviceStatus.ERROR) {
//   "Door device HTTP communication is abnormal, please check device connection and status"
// } else {
//   null
// }
//
// /**
//  * 门的当前状态
//  */
// private data class DoorState(val status: DoorDeviceStatus, val timestamp: Long = System.currentTimeMillis())
//
// /**
//  * 获取指定动作的配置
//  */
// private fun getActionConfig(actionName: String): ActionConfig? = config.actionConfigs.find { it.name == actionName }
//
// /**
//  * 获取HTTP请求路径
//  */
// private fun getHttpPath(actionName: String): String {
//   val actionConfig = getActionConfig(actionName)
//   return actionConfig?.httpPath ?: defaultApiPaths[actionName] ?: "/api/door/$actionName"
// }
//
// /**
//  * 获取HTTP请求方法
//  */
// private fun getHttpMethod(actionName: String): HttpMethod {
//   val actionConfig = getActionConfig(actionName)
//   val methodStr = actionConfig?.httpMethod
//     ?: when (actionName) {
//       "status", "ping" -> "GET"
//       else -> "POST"
//     }
//   return HttpClientBase.strToHttpMethod(methodStr)
// }
//
// /**
//  * 构建HTTP请求头
//  */
// private fun buildHeaders(): Map<String, String> {
//   val headers = mutableMapOf<String, String>()
//   headers["Content-Type"] = "application/json"
//
//   // 添加认证头
//   if (authToken != null) {
//     headers["Authorization"] = "Bearer $authToken"
//   } else if (username != null && password != null) {
//     val auth = "$username:$password"
//     val encodedAuth = java.util.Base64.getEncoder().encodeToString(auth.toByteArray())
//     headers["Authorization"] = "Basic $encodedAuth"
//   }
//
//   return headers
// }
//
// /**
//  * 更新门状态缓存
//  */
// private fun updateDoorState(status: DoorDeviceStatus) {
//   doorStateCache[config.deviceName] = DoorState(status, System.currentTimeMillis())
// }
//
// /**
//  * 执行HTTP请求
//  * @param actionName 操作名称
//  * @param body 请求体，默认为null
//  * @return HTTP响应结果
//  */
// private fun executeHttpRequest(actionName: String, body: String? = null): HttpResult {
//   val httpPath = getHttpPath(actionName)
//   val httpMethod = getHttpMethod(actionName)
//   val headers = buildHeaders()
//
//   val httpRequest = HttpRequest(
//     url = "$baseUrl$httpPath",
//     method = httpMethod,
//     contentType = HttpContentType.Json,
//     reqBody = body,
//     headers = headers,
//     trace = traceHttp,
//   )
//
//   return HttpClientBase.request(httpRequest)
// }