package com.seer.trick.fleet.traffic.distributed.service.model

import com.seer.trick.fleet.traffic.distributed.context.domain.RobotStatus
import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper

/**
 * 机器人信息
 * */
class RobotStatusReport(

  val robotName: String, // 机器人编码

  val mapName: String, // 地图编码

  val groupName: String, // 机器人组编码

  val index: Long = -1, // 执行的路径索引

  val x: Double = 0.0,

  val y: Double = 0.0,

  val pointName: String? = null,

  val lineName: String? = null,

  val status: RobotStatus = RobotStatus.IDLE, // 机器人状态

  val containerName: String? = null, // 带载容器编码

  val containerType: String? = null, // 带载类型

  val robotAngle: Int = AngleHelper.ERROR_ANGLE, // 机器人车头朝向

  val containerAngle: Int = AngleHelper.ERROR_ANGLE, // 容器朝向
)

class PathResult(val success: <PERSON><PERSON><PERSON>, val pathAndDirs: List<PathAndDir>, val message: String? = null)

class PathAndDir(
  val fromPointName: String,
  val toPointName: String,
  val index: Long,
  val robotEnterTheta: Int,
  val robotExitTheta: Int,
  val loadEnterTheta: Int?,
  val loadExitTheta: Int?,
)