package com.seer.trick.fleet.falcon

import com.seer.trick.falcon.task.FalconTaskService
import com.seer.trick.fleet.domain.OrderStatus
import com.seer.trick.fleet.order.OrderCancelService
import com.seer.trick.fleet.service.SceneService
import org.slf4j.LoggerFactory

object FleetOrderResourceCleaner {

  private val logger = LoggerFactory.getLogger(javaClass)

  fun init() {
    FalconTaskService.addResCleaner("TransportOrder", ::cleanTransportOrder)
  }

  private fun cleanTransportOrder(
    taskId: String,
    resType: String,
    resId: String,
    args: Map<String, Any?>?,
  ) {
    if (args == null) {
      logger.error("Start cleaning up the waybill，but args is empty")
      return
    }
    val sceneName = args["sceneName"] as String
    val orderId = args["orderId"] as String
    logger.info("Start cleaning up the waybill，sceneName=$sceneName，orderId=$orderId")

    val sr = SceneService.mustGetSceneByName(sceneName)
    val order = sr.mustGetOrderById(orderId).order
    if (order.status != OrderStatus.Done) {
      OrderCancelService.cancelOrder(sr, orderId)
    }
  }
}