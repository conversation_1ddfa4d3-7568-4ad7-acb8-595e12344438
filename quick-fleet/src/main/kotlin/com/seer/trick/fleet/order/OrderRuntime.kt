package com.seer.trick.fleet.order

import com.seer.trick.fleet.domain.OrderRuntimeRecord
import com.seer.trick.fleet.domain.RejectReason
import com.seer.trick.fleet.domain.StepStatus
import com.seer.trick.fleet.domain.TransportOrder
import com.seer.trick.fleet.domain.TransportStep
import java.util.Date

/**
 * 一个运单和步骤的运行时表示。
 */
class OrderRuntime(order: TransportOrder, steps: List<TransportStep>) {

  val id = order.id

  @Volatile
  var order: TransportOrder = order
    private set

  @Volatile
  var steps: List<TransportStep> = steps
    private set

  /**
   * 此运单无法被分派的原因
   */
  @Volatile
  var allocationReject: RejectReason? = null

  /**
   * 此运单无法被执行的原因
   */
  @Volatile
  var executionReject: RejectReason? = null

  /**
   * 是否允许运单重新分派，默认允许重分派。运单一旦不允许后（变为 false），不能再为 true。
   */
  @Volatile
  var withdrawOrderAllowed: Boolean = true

  /**
   * 是否允许步骤可重新分派，默认允许重分派
   */
  @Volatile
  var withdrawStepAllowed: Boolean = true

  /**
   * 运单被撤回、重置的时刻，进入一段时间的冷静期
   */
  @Volatile
  var coolingFrom: Long? = null

  /**
   * 运单是否正在被执行
   */
  fun isExecuting() = getCurrentStep()?.status == StepStatus.Executing

  /**
   * 运单是否在撤单后的冷静期内，期内执行
   */
  fun isCooling(): Boolean {
    val c = coolingFrom ?: return false
    return System.currentTimeMillis() - c < 1000 * 2 // 2s
  }

  /**
   * 更新运单，但不持久化
   */
  fun updateOrder(o: TransportOrder) {
    order = o
  }

  /**
   * 获取当前正在执行的运单步骤
   */
  fun getCurrentStep(): TransportStep? {
    val index = order.currentStepIndex
    if (index < 0 || index >= steps.size) return null
    return steps[order.currentStepIndex]
  }

  /**
   * 追加几个步骤，不持久化
   */
  fun addSteps(newSteps: List<TransportStep>) {
    val steps = steps.toMutableList()
    steps.addAll(newSteps)
    this.steps = steps
  }

  /**
   * 替换所有运单步骤，不持久化
   */
  fun replaceSteps(steps: List<TransportStep>) {
    this.steps = steps
  }

  /**
   * 根据 index 更新运单步骤，不持久化
   * 目前只有 updateAndPersistStep 能调用
   */
  fun updateStep(step: TransportStep): TransportStep {
    val steps = steps.toMutableList()
    steps[step.stepIndex] = step
    this.steps = steps
    return step
  }

  fun toRecord(): OrderRuntimeRecord = OrderRuntimeRecord(
    orderId = id,
    status = order.status,
    allocationReject = allocationReject,
    executionReject = executionReject,
  )

  fun toAll() = mapOf("order" to order, "steps" to steps)

  override fun toString(): String = "$id (${order.status})"

  companion object {

    /**
     * 计算 order 的耗时，单位：秒。
     *
     * @param doneOn 运单结束时间
     * @param startOn 运单的创建时间（createdOn），或者分派机器人的时间（robotAllocatedOn）
     *
     * @return startOn = null 时，返回值是 0.0，否则就是具体的时间差，单位是秒。
     */
    fun timeCostOrZero(doneOn: Date, startOn: Date? = null): Double {
      if (startOn == null) return 0.0
      return (doneOn.time - startOn.time) / 1000.0
    }
  }
}