package com.seer.trick.fleet.traffic.distributed.helper

import com.seer.trick.fleet.traffic.distributed.lock.graph.BoundingBox
import com.seer.trick.fleet.traffic.distributed.lock.graph.Polygon
import com.seer.trick.fleet.traffic.distributed.lock.graph.Vector
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min
import kotlin.math.sqrt

/**
 *  图的辅助类
 * */
object GraphHelper {

  /**
   * SAT 定理
   * */
  fun satTheorem(shapeA: List<Vector>, shapeB: List<Vector>): <PERSON><PERSON><PERSON> {
    // 矩形 检测两边就可以了 todo 如果后期遇到不规则四边形 再说
    val normals = if (shapeA.size == 4) 2 else shapeA.size
    for (i in 0 until normals) {
      val s1 = shapeA[i]
      val s2 = shapeA[(i + 1) % shapeA.size]
      val v = Vector(s2.y - s1.y, s1.x - s2.x)
      var minA = v.dot(s1)
      var maxA = minA
      for (sa in shapeA) {
        val dot = v.dot(sa)
        if (dot < minA) {
          minA = dot
        } else if (dot > maxA) {
          maxA = dot
        }
      }
      var minB = v.dot(shapeB[0])
      var maxB = minB
      for (j in 1 until shapeB.size) {
        val sb = shapeB[j]
        val dot = v.dot(sb)
        if (dot < minB) {
          minB = dot
        } else if (dot > maxB) {
          maxB = dot
        }
      }
      // 不相交
      if (maxA < minB || maxB < minA) return true
    }
    return false
  }

  /**
   * 多边形的每条边向外平移指定距离（d）
   */
  fun inflate(ps: MutableList<Vector>, d: Double): MutableList<Vector> {
    val points: MutableList<Vector> = mutableListOf()
    val delta = if (area(ps) < 0) -d else d
    val normals = buildNormals(ps)
    var pre = ps.size - 1
    for (index in ps.indices) {
      val cos = dotProduct(normals[index], normals[pre])
      val q = delta / (cos + 1)
      points.add(
        Vector(
          (ps[index].x + q * (normals[index].x + normals[pre].x)),
          (ps[index].y + q * (normals[index].y + normals[pre].y)),
        ),
      )
      pre = index
    }
    return points
  }

  // 计算由一系列点构成的闭合路径所围成的面积
  private fun area(ps: List<Vector>): Double {
    // https://en.wikipedia.org/wiki/Shoelace_formula
    var sum = 0.0
    if (ps.size < 3) {
      return 0.0
    }
    var p1 = ps[ps.size - 1]
    for (i in 0 until ps.size) {
      val p2 = ps[i]
      sum += (p2.y + p1.y) * (p1.x - p2.x)
      p1 = p2
    }
    return sum * 0.5
  }

  // 点乘
  private fun dotProduct(p1: Vector, p2: Vector): Double = p1.x * p2.x + p1.y * p2.y

  // 构建多边形的单位法向量
  private fun buildNormals(points: List<Vector>): List<Vector> {
    val normals: MutableList<Vector> = mutableListOf()
    for (i in 0 until points.size - 1) {
      normals.add(getUnitNormal(points[i], points[(i + 1)]))
    }
    normals.add(getUnitNormal(points[points.size - 1], points[0]))
    return normals
  }

  // 计算两个点构成的向量的单位法向量
  private fun getUnitNormal(p1: Vector, p2: Vector): Vector {
    val vx = p2.x - p1.x
    val vy = p2.y - p1.y
    if (vx == 0.0 && vy == 0.0) {
      return Vector(0.0, 0.0)
    }
    val f = 1.0 / sqrt(vx * vx + vy * vy)
    return Vector(vy * f, -vx * f)
  }

  /**
   * 判断点 (x, y) 是否在多边形内部（包括边界）
   */
  fun pointInPolygon(x: Double, y: Double, shape: Polygon, box: BoundingBox): Boolean {
    if (!box.onRegion(x, y)) return false
    val ps = shape.points.size
    for (i in 0 until ps) {
      val s1 = shape.points[i]
      val s2 = shape.points[(i + 1) % ps]
      if (pointOnSegment(x, y, s1, s2)) return true
    }
    var count: Int = 0
    for (i in 0 until ps) {
      val s1 = shape.points[i]
      val s2 = shape.points[(i + 1) % ps]
      // 跳过水平边（已在边界检查中处理）
      if (abs(s1.y - s2.y) < 1e-10) continue
      // 检查射线是否穿过边的非水平部分（下闭上开规则）
      // if (y1 <= y < y2) or (y2 <= y < y1):
      if (s1.y <= y && y < s2.y || s2.y <= y && y < s1.y) {
        val intersect = (s2.x - s1.x) * (y - s1.y) / (s2.y - s1.y) + s1.x
        if (intersect > x) count += 1
      }
    }
    return count % 2 == 1
  }

  private fun pointOnSegment(x: Double, y: Double, s1: Vector, s2: Vector, tol: Double = 1e-10): Boolean {
    val crossProduct = (x - s1.x) * (s2.y - s1.y) - (y - s1.y) * (s2.x - s1.x)
    if (abs(crossProduct) > tol) return false
    // # 检查点是否在线段包围盒内
    return min(s1.x, s2.x) - tol <= x &&
      x <= max(s1.x, s2.x) + tol &&
      min(s1.y, s2.y) - tol <= y &&
      y <= max(s1.y, s2.y) + tol
  }
}