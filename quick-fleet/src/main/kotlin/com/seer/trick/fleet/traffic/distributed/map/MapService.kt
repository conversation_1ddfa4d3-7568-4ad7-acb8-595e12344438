package com.seer.trick.fleet.traffic.distributed.map

import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper
import com.seer.trick.fleet.traffic.distributed.helper.GraphHelper
import com.seer.trick.fleet.traffic.distributed.lock.LockService
import com.seer.trick.fleet.traffic.distributed.lock.LockType
import com.seer.trick.fleet.traffic.distributed.lock.graph.*
import com.seer.trick.fleet.traffic.distributed.lock.graph.Polygon
import com.seer.trick.fleet.traffic.distributed.lock.model.*
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.abs

/**
 * 交管地图管理服务
 * */

object MapService {

  // 地图集合
  private val map = ConcurrentHashMap<String, MapArea>()

  // 组集合 key: mapCode-groupName, value: map
  private val groupMap = ConcurrentHashMap<String, MapArea>()

  private const val BLOCK_AREA_NAME = "BlockArea"

  private const val AVOID_AREA_NAME = "Avoid"

  // 自由导航区集合 key mapName, value: AvoidArea 集合
  val avoidAreas = ConcurrentHashMap<String, MutableList<AvoidArea>>()

  private fun updateMap(m: Map<String, MapArea>) {
    map.clear()
    m.forEach(map::put)
  }

  // 通过点编码查询点信息
  fun findPointByName(sceneId: String, mapName: String, groupName: String, pointName: String): Point {
    val groupKey = groupMapKey(sceneId, mapName, groupName)
    val groupMap =
      groupMap[groupKey] ?: throw RuntimeException("mapName: $mapName, groupName: $groupName not found")
    return groupMap.points[pointName]
      ?: throw RuntimeException("mapName: $mapName, groupName: $groupName, pointName: $pointName not found")
  }

  fun findLineByName(sceneId: String, mapName: String, groupName: String, lineName: String): Line {
    val groupKey = groupMapKey(sceneId, mapName, groupName)
    val groupMap = groupMap[groupKey] ?: throw RuntimeException("mapName: $mapName, groupName: $groupName not found")
    return groupMap.lines[lineName]
      ?: throw RuntimeException("mapName: $mapName, groupName: $groupName, lineName: $lineName not found")
  }

  fun findLineByPoints(
    sceneId: String,
    mapName: String,
    groupName: String,
    startPointName: String,
    endPointName: String,
  ): Line? {
    val pointByName = findPointByName(sceneId, mapName, groupName, startPointName)
    return pointByName.toLines.find { line -> line.end.pointName == endPointName }
  }

  fun findGroupMap(sceneId: String, mapName: String, groupName: String): MapArea =
    groupMap[groupMapKey(sceneId, mapName, groupName)]
      ?: throw RuntimeException("mapName: $mapName, groupName: $groupName not found")

  /**
   *  检查点是否在自由导航区域内
   * */
  fun findPointInAvoidArea(sceneId: String, mapName: String, pointName: String): Boolean {
    val areas = avoidAreas[LockService.sceneMapKey(sceneId, mapName)]
    areas?.forEach { area ->
      if (area.pointNames.contains(pointName)) {
        return true
      }
    }
    return false
  }

  /**
   *  检查坐标是否在自由导航区域内
   * */
  fun findXYInAvoidArea(sceneId: String, mapName: String, x: Double, y: Double): Boolean {
    val areas = avoidAreas[LockService.sceneMapKey(sceneId, mapName)]
    areas?.forEach { area ->
      if (GraphHelper.pointInPolygon(x, y, area.shape.cells[0].layers[0].shape as Polygon, area.shape.box)) {
        return true
      }
    }
    return false
  }

  /**
   *  初始化场景地图信息
   * */
  fun initMap(sceneId: String, areas: List<SceneArea>) {
    val maps = buildSceneArea(sceneId, areas)
    this.updateMap(maps)
    LockService.initQuadTree(maps)
  }

  private fun buildSceneArea(sceneId: String, areas: List<SceneArea>): MutableMap<String, MapArea> {
    val maps = HashMap<String, MapArea>()

    // 互斥区集合 key mapName, value: blockArea 集合
    val blockAreas = ConcurrentHashMap<String, MutableList<BlockArea>>()

    for (area in areas) {
      val areaMap = area.mergedMap
      val bound = Bound(
        areaMap.bound.cx - areaMap.bound.width / 2,
        areaMap.bound.cx + areaMap.bound.width / 2,
        areaMap.bound.cy - areaMap.bound.height / 2,
        areaMap.bound.cy + areaMap.bound.height / 2,
      )

      val result = convertFunction(schema = areaMap)
      val mapArea = MapArea(area.name, result.first, result.second, bound)
      maps[LockService.sceneMapKey(sceneId, area.id.toString())] = mapArea

      blockAreas[LockService.sceneMapKey(sceneId, area.id.toString())] =
        initBlockAreas(areaMap.zones, area.id.toString())

      avoidAreas[LockService.sceneMapKey(sceneId, area.id.toString())] =
        initAvoidAreas(areaMap.zones, area.id.toString(), mapArea.points.values.toList())
    }
    LockService.initBlockArea(blockAreas)
    return maps
  }

  private fun initAvoidAreas(zones: List<MapZone>, mapName: String, points: List<Point>): MutableList<AvoidArea> {
    val avoidArea = mutableListOf<AvoidArea>()
    zones.filter { it.type == AVOID_AREA_NAME && !it.disabled }.map { it ->
      val shape = buildAreaShape(it.polygon, it.name, mapName)
      val pointNames = mutableListOf<String>()
      points.filter { point ->
        GraphHelper.pointInPolygon(point.x, point.y, shape.cells[0].layers[0].shape as Polygon, shape.box)
      }.map { p -> pointNames.add(p.name) }
      avoidArea.add(
        AvoidArea(
          name = it.name,
          disabled = it.disabled,
          shape = shape,
          pointNames = pointNames,
        ),
      )
    }
    return avoidArea
  }

  /**
   *  更新场景地图信息
   * */
  fun updateMap(sceneId: String, areas: List<SceneArea>) {
    val maps = buildSceneArea(sceneId, areas)
    this.updateMap(maps)
    maps.forEach { (sceneMapKey, area) -> LockService.updateQuadTree(sceneMapKey, area) }
  }

  private fun convertFunction(schema: SceneAreaMap): Pair<MutableMap<String, Point>, MutableMap<String, Line>> {
    // 构建点的集合
    val points: MutableMap<String, Point> = mutableMapOf()
    schema.points.forEach {
      if (it.disabled) return@forEach
      points[it.name] = Point(
        name = it.name,
        type = if (it.containerRotateAllowed) PointType.ROTATE else processPointType(it.type),
        x = it.x,
        y = it.y,
        direction = if (it.direction == null) null else AngleHelper.convertAngle(it.direction),
        avoid = !it.giveWayNotAllowed,
        rotate = !it.rotateNotAllowed,
        junction = it.linkedPathAsJointResource,
      )
    }

    // 构建路线的集合
    val lines: MutableMap<String, Line> = mutableMapOf()
    schema.paths.map { path ->
      if (path.disabled) {
        return@map
      }
      // 线路的起点或者终点被禁用之后，这条线路也是不可用的。
      val start = points[path.fromPointName] ?: return@map
      val end = points[path.toPointName] ?: return@map
      when (path.curveType) {
        PathCurveType.Straight -> {
          val line = Line(
            lineName = path.key,
            type = LineType.STRAIGHT,
            start = start,
            end = end,
            rotate = !path.rotateNotAllowed,
            passThrough = !path.giveWayNotAllowed,
            length = path.actualLength * (if (path.costFactor > 1.0) path.costFactor else 1.0),
            driveDirection = path.moveDirection,
            enterDir = AngleHelper.convertAngle(path.tracePoints.first().tangent),
            outDir = AngleHelper.convertAngle(path.tracePoints.last().tangent),
            narrowPath = path.containerShortSideAhead,
            containerDir = if (path.containerDir == null) null else AngleHelper.convertAngle(path.containerDir),
            loadStatus = convertLoadState(path.loadPass),
          )
          start.toLines += line
          end.fromLines += line
          lines[path.key] = line
        }

        PathCurveType.Bezier3 -> {
          val controls: MutableList<Site> = mutableListOf()
          for (i in 1 until path.controls.size - 1) {
            controls.add(convert(path.controls[i]))
          }
          // 看看是不是可以降为直线处理
          val type = if (isStraight(path)) LineType.STRAIGHT else LineType.THREE_BEZIER
          val line = Line(
            lineName = path.key,
            type = type,
            start = start,
            end = end,
            rotate = !path.rotateNotAllowed,
            driveDirection = path.moveDirection,
            passThrough = !path.giveWayNotAllowed,
            length = path.actualLength * (if (path.costFactor > 1.0) path.costFactor else 1.0),
            enterDir = AngleHelper.convertAngle(path.tracePoints.first().tangent),
            outDir = AngleHelper.convertAngle(path.tracePoints.last().tangent),
            controls = controls,
            tracePoses = findTracePoses(path.tracePoints),
            narrowPath = path.containerShortSideAhead,
            containerDir = if (path.containerDir == null) null else AngleHelper.convertAngle(path.containerDir),
            loadStatus = convertLoadState(path.loadPass),
          )
          start.toLines += line
          end.fromLines += line
          lines[path.key] = line
        }

        PathCurveType.NURBS6, PathCurveType.DegenerateBezier, PathCurveType.Other -> {
          //  高阶降维的可能性检测
          val type = if (isStraight(path)) LineType.STRAIGHT else LineType.LUMINANCE_CURVE
          val line = Line(
            lineName = path.key,
            type = type,
            start = start,
            end = end,
            rotate = !path.rotateNotAllowed,
            driveDirection = path.moveDirection,
            passThrough = !path.giveWayNotAllowed,
            length = path.actualLength * (if (path.costFactor > 1.0) path.costFactor else 1.0),
            enterDir = AngleHelper.convertAngle(path.tracePoints.first().tangent),
            outDir = AngleHelper.convertAngle(path.tracePoints.last().tangent),
            controls = path.controls.map { convert(it) }.toMutableList(),
            tracePoses = findTracePoses(path.tracePoints),
            narrowPath = path.containerShortSideAhead,
            containerDir = if (path.containerDir == null) null else AngleHelper.convertAngle(path.containerDir),
            loadStatus = convertLoadState(path.loadPass),
          )
          start.toLines += line
          end.fromLines += line
          lines[path.key] = line
        }
      }
    }
    return Pair(points, lines)
  }

  private fun convertLoadState(loadPass: PathLoadPass): LoadStatus = when (loadPass) {
    PathLoadPass.NoLimit -> LoadStatus.ANY
    PathLoadPass.NoLoadOnly -> LoadStatus.EMPTY
    PathLoadPass.LoadOnly -> LoadStatus.LOADED
  }

  private fun findTracePoses(tracePoints: List<CurvePoint2D>): MutableList<Pose> {
    var dis = 0.0
    val poses = mutableListOf<Pose>()
    poses.add(convertPose(tracePoints.first()))
    for (i in 1 until tracePoints.size - 1) {
      dis += abs(tracePoints[i].x - tracePoints[i - 1].x) + abs(tracePoints[i].y - tracePoints[i - 1].y)
      if (dis > 0.2) {
        poses.add(convertPose(tracePoints[i]))
        dis = 0.0
      }
    }
    poses.add(convertPose(tracePoints.last()))
    return poses
  }

  private fun isStraight(path: MapPath): Boolean {
    val tracePoints = path.tracePoints
    // 可能存在中点正好起终点连线上
    val sPoint = tracePoints[tracePoints.size / 4]
    val ePoint = tracePoints[tracePoints.size * 3 / 4]
    val veLine =
      VeLine(Vector(tracePoints.first().x, tracePoints.first().y), Vector(tracePoints.last().x, tracePoints.last().y))
    return veLine.pointOnLine(Vector(path.middlePoint.x, path.middlePoint.y)) &&
      veLine.pointOnLine(Vector(sPoint.x, sPoint.y)) &&
      veLine.pointOnLine(Vector(ePoint.x, ePoint.y))
  }

  private fun convertPose(it: CurvePoint2D): Pose = Pose(it.x, it.y, AngleHelper.convertAngle(it.tangent))

  // 初始化互斥区
  private fun initBlockAreas(zones: List<MapZone>, mapName: String): MutableList<BlockArea> {
    val blockAreas = mutableListOf<BlockArea>()
    zones.filter { it.type == BLOCK_AREA_NAME }.map {
      blockAreas.add(
        BlockArea(
          name = it.name,
          disabled = it.disabled,
          shape = buildAreaShape(it.polygon, it.name, mapName),
          robotNum = 1,
        ),
      )
    }
    return blockAreas
  }

  private fun buildAreaShape(polygon: List<Point2D>, name: String, mapName: String): SpaceLock {
    val points = polygon.map { Vector(it.x, it.y) }
    val shape = Polygon(
      type = GraphType.POLYGON,
      points = points,
      concave = false,
      subPolygons = mutableListOf(),
      box = BoundingBox(points),
    )

    val cell = Cell(
      layers = mutableListOf(Layer(id = 0, top = 2.5, bottom = 0.0, shape = shape)),
      sPosition = Position(x = 0.0, y = 0.0, pointName = "", posType = PosType.UNDEFINE),
    )

    return SpaceLock(
      groupName = BLOCK_AREA_NAME,
      type = LockType.AREA,
      name = name,
      cells = mutableListOf(cell),
      mapName = mapName,
    )
  }

  fun updateGroupMap(sceneId: String, groupName: String, mapName: String, schema: SceneAreaMap) {
    val key = groupMapKey(sceneId, mapName, groupName)
    val bound = Bound(
      schema.bound.cx - schema.bound.width / 2,
      schema.bound.cx + schema.bound.width / 2,
      schema.bound.cy - schema.bound.height / 2,
      schema.bound.cy + schema.bound.height / 2,
    )
    val result = convertFunction(schema = schema)
    val mapArea = MapArea(mapName, result.first, result.second, bound)
    groupMap[key] = mapArea
  }

  /**
   *  组地图唯一标识
   * */
  private fun groupMapKey(sceneId: String, mapName: String, groupName: String): String = "$sceneId-$mapName-$groupName"

  private fun convert(p: NurbsControlPoint): Site = Site(p.x, p.y)

  private fun processPointType(type: String): PointType {
    //     "LocationMark" to "LM",
    //     "ActionPoint" to "AP",
    //     "ParkPoint" to "PP",
    //     "ChargePoint" to "CP",
    //     "TransferLocation" to "TL",
    //     "WorkingLocation" to "WL",
    //     "SwitchMap" to "SM", 设备点
    //     "HomeRegion" to "HR"
    // todo 目前先这样分类，后期做详细设计
    if (type == "ActionPoint" || type == "LocationMark" || type == "ParkPoint") {
      return PointType.COMMON
    } else if (type == "ChargePoint") {
      return PointType.CHARGE
    } else if (type == "WorkingLocation") {
      return PointType.STATION
    }
    return PointType.UNDEFINE
  }
}