package com.seer.trick.fleet.traffic.distributed.service

import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.service.*
import com.seer.trick.fleet.traffic.TrafficService
import com.seer.trick.fleet.traffic.TrafficTaskRuntime
import com.seer.trick.fleet.traffic.distributed.block.BlockService
import com.seer.trick.fleet.traffic.distributed.context.ContextManagerService
import com.seer.trick.fleet.traffic.distributed.context.domain.RobotMotionType
import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper
import com.seer.trick.fleet.traffic.distributed.lock.LockService
import com.seer.trick.fleet.traffic.distributed.lock.LockType
import com.seer.trick.fleet.traffic.distributed.lock.base.RobotContainerSpaceLock
import com.seer.trick.fleet.traffic.distributed.lock.base.StaticRobotSpaceLock
import com.seer.trick.fleet.traffic.distributed.lock.domain.LockRequest
import com.seer.trick.fleet.traffic.distributed.lock.graph.Vector
import com.seer.trick.fleet.traffic.distributed.lock.move.SpaceLockCalculate
import com.seer.trick.fleet.traffic.distributed.map.*
import com.seer.trick.fleet.traffic.distributed.map.MapService
import com.seer.trick.fleet.traffic.distributed.plan.*
import com.seer.trick.fleet.traffic.distributed.service.model.GroupModel
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper
import org.apache.commons.lang3.StringUtils
import kotlin.math.max

/**
 *  定义和交管外部交互的接口服务
 * */
class DistributedInteractionService(override val sr: SceneRuntime) : TrafficService() {

  private val eventId = IdHelper.oidStr()

  override fun init() {
    super.init()
    logger.info("init distributed traffic")

    // 更新机器人组信息
    FleetEventService.on(
      "RobotGroup::Update",
      FleetEventInternalListener(eventId, 100, true, ::onUpdateGroup),
    )
    // 添加容器信息
    FleetEventService.on(
      "ContainerType::Create",
      FleetEventInternalListener(eventId, 100, true, ::onCreateContainerType),
    )
    // 更新容器信息
    FleetEventService.on(
      "ContainerType::Update",
      FleetEventInternalListener(eventId, 100, true, ::onUpdateContainerType),
    )
    // 删除容器信息
    FleetEventService.on(
      "ContainerType::RemoveByName",
      FleetEventInternalListener(eventId, 100, true, ::onRemoveContainerType),
    )
  }

  private fun onUpdateContainerType(event: FleetEvent) {
    if (event.sceneId != sr.sceneId) return
    val sr = SceneService.mustGetSceneById(event.sceneId)
    val node = JsonHelper.writeToTree(event.extra)
    val containerTypeId = node?.get("containerTypeId")?.asInt() ?: return
    val containerType = sr.containerTypes[containerTypeId] ?: return
    this.updateContainerType(containerType)
  }

  private fun onRemoveContainerType(event: FleetEvent) {
    if (event.sceneId != sr.sceneId) return
    val node = JsonHelper.writeToTree(event.extra)
    val containerNames = node?.get("containerNames")?.asIterable() ?: return
    for (containerName in containerNames) {
      val name = containerName.asText() ?: continue
      this.removeContainerType(name)
    }
  }

  private fun onCreateContainerType(event: FleetEvent) {
    if (event.sceneId != sr.sceneId) return
    val sr = SceneService.mustGetSceneById(event.sceneId)
    val node = JsonHelper.writeToTree(event.extra)
    val containerTypeId = node?.get("containerTypeId")?.asInt() ?: return
    val containerType = sr.containerTypes[containerTypeId] ?: return
    this.createContainerType(containerType)
  }

  private fun onUpdateGroup(event: FleetEvent) {
    if (event.sceneId != sr.sceneId) return
    val sr = SceneService.mustGetSceneById(event.sceneId)
    val node = JsonHelper.writeToTree(event.extra)
    val groupId = node?.get("groupId")?.asInt() ?: return
    val robotGroup = sr.robotGroups[groupId] ?: return
    ContextManagerService.updateRobotGroupBasic(
      event.sceneId,
      robotGroup.name,
      robotGroup.salverNotRotate,
      robotGroup.motionDirection,
      robotGroup.motionModel,
    )
    updateGroupModel(robotGroup)
  }

  /**
   *  交管场景初始化
   * */
  @Volatile
  var initialized = false
    private set

  /**
   * 销毁服务。销毁后此实例不能再被重用。
   */
  override fun dispose() {
    logger.info("dispose traffic ${sr.sceneId} service")
    initialized = false
    super.dispose()
    FleetEventService.off("RobotGroup::Update", eventId)
    FleetEventService.off("ContainerType::Create", eventId)
    FleetEventService.off("ContainerType::Update", eventId)
    FleetEventService.off("ContainerType::Remove", eventId)
  }

  override fun initTrafficSchema() {
    logger.info("init traffic $sr schema")
    val areas = sr.areas
    MapService.initMap(sr.sceneId, areas)
    trafficSchema()
    initialized = true
  }

  /**
   *  更新交管场景信息
   *  1、地图信息
   *  2、机器人模型信息
   */
  override fun updateTrafficSchema() {
    logger.info("update traffic $sr schema")

    val areas = sr.areas
    MapService.updateMap(sr.sceneId, areas)
    trafficSchema()
  }

  private fun trafficSchema() {
    val groups = sr.robotGroups.values.toList()
    val cache = sr.mapCache
    for (group in groups) {
      for (area in cache.areaById.values) {
        for (gm in area.groupedMaps) {
          if (gm.key != group.id) continue
          // 更新组的一个区域的地图
          MapService.updateGroupMap(sr.sceneId, group.name, area.schema.id.toString(), gm.value.areaMap)
        }
      }
      val bound = group.collisionModel.bound

      StaticRobotSpaceLock.addRobotGroupModel(
        GroupModel(
          sceneId = sr.sceneId,
          groupName = group.name,
          type = RobotMotionType.ADVANCE,
          points = bound.points.map { Vector(it.x, it.y) }.toMutableList(),
          lengthSafeDistance = max(group.safeDistHead, group.safeDistTail), // 车头前后最大安全距离，以车的方向考虑
          widthSafeDistance = max(group.safeDistLeft, group.safeDistRight), // 车头两侧最大安全距离
          radius = 0.0,
        ),
      )
    }
    val containerTypes = sr.containerTypes.values.toList()
    DistributedTrafficService.addContainerTypes(containerTypes, groups, sr.sceneId)
  }

  /**
   *  更新交管地图信息
   *  1、展示地图
   *  2、各组地图
   *  todo 要不要拆开来单个更新 ？？
   * */
  fun updateTrafficMap(
    areas: List<SceneArea>,
    mapNames: Map<String, String>,
    groupsMap: Map<String, SceneAreaMap>,
  ): Boolean {
    MapService.updateMap(sr.sceneId, areas)
    for (map in groupsMap) {
      MapService.updateGroupMap(sr.sceneId, map.key, mapNames[map.key]!!, map.value)
    }
    return true
  }

  override fun showTrafficResourceMessage(robotName: String): RobotShowTrafficMessage? =
    DistributedTrafficService.showTrafficResourceMessageByRobotName(sr.sceneId, robotName)

  override fun showTrafficResourceMessage(): Map<String, RobotShowTrafficMessage> =
    DistributedTrafficService.buildTrafficResourceMessage(sr.sceneId)

  /**
   * 机器人组模型更新
   * */
  private fun updateGroupModel(group: RobotGroup) {
    logger.info("${group.name} update model ${group.collisionModel.bound}")
    val polygon = group.collisionModel.bound
    val groupModel = GroupModel(
      sceneId = sr.sceneId,
      groupName = group.name,
      type = RobotMotionType.ADVANCE,
      points = polygon.points.map { Vector(it.x, it.y) }.toMutableList(),
      lengthSafeDistance = max(group.safeDistHead, group.safeDistTail), // 车头前后最大安全距离，以车的方向考虑
      widthSafeDistance = max(group.safeDistLeft, group.safeDistRight), // 车头两侧最大安全距离
      radius = 0.0,
    )
    StaticRobotSpaceLock.updateGroupModel(groupModel)
  }

  override fun plan(task: TrafficTaskRuntime): PlanResult {
    val plan = task.target
    // TODO 暂时未考虑容器
    val request = RequestInfo(
      robotName = task.robotName,
      groupName = task.robotGroupName,
      //
      orderId = task.orderId,
      stepId = task.stepId,
      stepIndex = task.stepIndex,
      //
      sceneId = sr.sceneId,
      mapName = plan.areaId.toString(),
      startX = task.source.x,
      startY = task.source.y,
      startType = task.source.type,
      start = StringUtils.firstNonBlank(task.source.pointName, task.source.pathPositions?.firstOrNull()?.pathKey) ?: "",
      target = plan.pointName,
      startAngle = AngleHelper.convertAngle(task.source.theta),
      containerType = plan.containerId,
      containerStartAngle = if (plan.containerId != null && plan.containerStartAngle == null) {
        AngleHelper.ERROR_ANGLE
      } else {
        AngleHelper.convertAngle(plan.containerStartAngle)
      },
      containerTargetAngle = if (plan.containerId != null && plan.containerTargetAngle == null) {
        AngleHelper.ERROR_ANGLE
      } else {
        AngleHelper.convertAngle(plan.containerTargetAngle)
      },
      targetAngle = if (plan.theta == null) null else AngleHelper.convertAngle(plan.theta),
      targetCombinedStop = true,
      task = task,
    )
    // 发送请求
    logger.info(
      "${request.robotName} plan request| ${request.orderId}-${request.stepId}| " +
        "${request.mapName}-${request.groupName}| ${request.startType}-${request.start}, ${request.target}| " +
        "${request.startX}, ${request.startY}, ${request.startAngle}, ${request.targetAngle}| " +
        "${request.containerType}, ${request.containerStartAngle}, ${request.containerTargetAngle}",
    )

    val responseInfo = PlanPathService.plan(request)
    return PlanResult(
      success = responseInfo.status == ResStatus.SUCCESS,
      code = responseInfo.code,
      args = responseInfo.args,
    )
  }

  /**
   * 任务取消完成, 是否保证机器人已经停止
   * 如何柔性的进行任务取消
   * */
  override fun afterTrafficTaskCancelled(rr: RobotRuntime) {
    // 记录请求信息
    logger.info("${rr.robotName} current step ${rr.executingStep} canceled")

    // 任务取消流程，todo 取消后，找一个不在主干道上且可以停靠的点，发送给机器人，若提供了停靠点，则使用该停靠点，否则使用机器人当前位置
    ContextManagerService.canceled(rr.robotName, null)
    // 锁闭信息处理
    // todo 对接下来的点进行处理
    robotDoneMoves[rr.robotName] = -1
  }

  override fun afterMoveActionFailed(rr: RobotRuntime, orderId: String?) {
    logger.info("${rr.robotName} executing $orderId step failed")
    ContextManagerService.failed(rr.robotName)
    robotDoneMoves[rr.robotName] = -1
  }

  override fun afterTrafficTaskDone(rr: RobotRuntime): Boolean {
    // 记录请求信息
    logger.info("${rr.robotName} step finished")
    // 任务结束流程
    ContextManagerService.finished(rr.robotName)
    // todo 锁闭问题处理
    // 锁闭信息处理
    robotDoneMoves[rr.robotName] = -1
    return true
  }

  override fun resetByRobot(rr: RobotRuntime) {
    // 重置机器人: 1、重置机器人任务; 2、重置机器人的资源信息
    logger.info("${rr.robotName} reset by robot")
    ContextManagerService.resetRobot(rr.robotName)
  }

  override fun resetAll() {
    sr.robots.values.forEach {
      resetByRobot(it)
    }
  }

  /**
   * 机器人第一次出现在场景里
   * 通过事件触发来调用，可能存在的问题：
   * 1、事件触发未达到此；
   * 2、机器人没有位置信息导致失败；
   * 3、机器人没有位置信息和模型信息导致失败
   * 4、机器人上线位置和其他机器人发生碰撞，导致失败
   * 失败后 todo
   */
  override fun robotEnterTraffic(rr: RobotRuntime) {
    logger.info(
      "${rr.robotName} enter current scene ${sr.sceneId} in ${rr.selfReport?.stand?.pointName} " +
        "| ${rr.selfReport?.stand?.theta} | ${rr.selfReport?.stand?.areaId}",
    )
    if (rr.selfReport?.stand == null) {
      logger.error("${rr.robotName} enter but stand is null")
      return
    }
    if (rr.trafficReady) {
      logger.warn("${rr.robotName} enter but trafficReady is true")
      return
    }
    if (rr.selfReport?.stand?.areaId == null) {
      logger.warn("${rr.robotName} enter but areaId is null")
      return
    }
    if (!initialized) {
      logger.warn("${rr.robotName} enter but traffic not initialized")
    }

    try {
      val group = rr.mustGetGroup()
      val mapName = rr.selfReport?.stand?.areaId.toString()
      val context =
        ContextManagerService.init(
          rr.robotName,
          group.name,
          mapName,
          group.salverNotRotate,
          group.motionDirection,
          group.motionModel,
          rr,
        )
      // 申请上线锁闭信息, 这个时候机器人不一定能够申请到锁闭锁闭信息
      val spaceLock = SpaceLockCalculate.robotStaticSpaceLock(
        context.robotName,
        context.sceneId,
        context.groupName,
        context.baseDomain.robotHeading,
        context.baseDomain.curPoint,
        context.mapName,
      )
      if (spaceLock == null) {
        logger.error("${context.robotName} apply space lock failed, current point is ${context.baseDomain.curPoint}")
        return
      } else {
        val lockHandle = LockService.lockHandle(
          LockRequest(
            id = context.robotName,
            mapName = context.mapName,
            sceneId = context.sceneId,
            spaces = mutableListOf(spaceLock),
          ),
        )
        if (lockHandle.success) {
          rr.trafficReady = true
          return
        }
        val blockName = if (lockHandle.blocks.isNotEmpty()) {
          lockHandle.blocks.first().code
        } else {
          null
        }
        DistributedTrafficService.trafficAlarm(
          sceneId = context.sceneId,
          robotName = context.robotName,
          code = "*********",
          args = listOf(context.robotName, blockName ?: ""),
          buttons = listOf("btnResetSpaceLock"),
        )

        logger.error("${context.robotName} apply space lock failed, block by $blockName")
      }
    } catch (e: Exception) {
      logger.error("${rr.robotName} enter traffic failed! $e")
    }
  }

  /**
   * 机器人离开场景
   * 不再需要在系统中占有资源信息
   */
  override fun robotExitTraffic(rr: RobotRuntime) {
    logger.info("${rr.robotName} exit current scene")
    try {
      val context = ContextManagerService.destroy(rr.robotName) ?: return
      val key = LockType.ROBOT.toString() + "-><-" + context.groupName + "-><-" + context.robotName
      LockService.removeSpaceLockByKey(key, context.sceneId, context.mapName)
      BlockService.removeBlockByItemCode(context.robotName)
      rr.trafficReady = false
    } catch (e: Exception) {
      logger.error("${rr.robotName} exit distributed is failed!")
    }
  }

  /**
   *  机器人顶升容器接口
   *  direction: 容器方向，多个容器传最大的那一个容器的方向
   * */
  override fun robotLoadContainer(
    rr: RobotRuntime,
    containerName: String?,
    containerType: String,
    direction: Double,
  ): Boolean {
    logger.info("${rr.robotName} load container $containerName, type $containerType, direction $direction")
    // 构建合体锁闭信息
    try {
      val context = ContextManagerService.queryRobotContext(robotName = rr.robotName)
      val selfReport = DistributedTrafficService.queryRobotStatusReport(rr.robotName, rr)
      val spaceLock = SpaceLockCalculate.robotAndContainerStaticSpaceLock(
        robotName = context.robotName,
        sceneId = context.sceneId,
        groupName = context.groupName,
        robotHeading = selfReport.robotAngle,
        point = if (selfReport.pointName != null) {
          Position(
            x = selfReport.x,
            y = selfReport.y,
            pointName = selfReport.pointName,
            posType = PosType.POINT,
          )
        } else {
          context.baseDomain.curPoint!!
        },
        mapName = context.mapName,
        container = containerType,
        containerHeading = AngleHelper.convertAngle(direction),
      )
      val lockHandle = LockService.lockHandle(
        LockRequest(
          id = context.robotName,
          mapName = context.mapName,
          sceneId = context.sceneId,
          spaces = mutableListOf(spaceLock),
        ),
      )
      return lockHandle.success
    } catch (e: Exception) {
      logger.error("${rr.robotName} load container failed! $e")
    }
    return false
  }

  /**
   *  机器人放下容器接口,不考虑容器锁闭信息
   * */
  override fun robotUnloadContainer(rr: RobotRuntime): Boolean {
    logger.info("${rr.robotName} unload container")
    // 构建合体锁闭信息
    try {
      val context = ContextManagerService.queryRobotContext(robotName = rr.robotName)
      val selfReport = DistributedTrafficService.queryRobotStatusReport(rr.robotName, rr)
      val spaceLock = SpaceLockCalculate.robotAndContainerStaticSpaceLock(
        robotName = context.robotName,
        sceneId = context.sceneId,
        groupName = context.groupName,
        robotHeading = selfReport.robotAngle,
        point = if (selfReport.pointName != null) {
          Position(
            x = selfReport.x,
            y = selfReport.y,
            pointName = selfReport.pointName,
            posType = PosType.POINT,
          )
        } else {
          context.baseDomain.curPoint!!
        },
        mapName = context.mapName,
        container = null,
      )
      // 清空容器
      context.baseDomain.containerType = null
      val lockHandle = LockService.lockHandle(
        LockRequest(
          id = context.robotName,
          mapName = context.mapName,
          sceneId = context.sceneId,
          spaces = mutableListOf(spaceLock),
        ),
      )
      return lockHandle.success
    } catch (e: Exception) {
      logger.error("${rr.robotName} unload container failed! $e")
    }
    return false
  }

  /**
   *  切地图接口
   */
  override fun switchMap(rr: RobotRuntime): Boolean {
    // 先删除之前的锁闭信息
    logger.info("switch robot ${rr.robotName} map to ${rr.selfReport?.stand?.areaId}")
    try {
      val context = ContextManagerService.queryRobotContext(rr.robotName)
      val key = LockType.ROBOT.toString() + "-><-" + context.groupName + "-><-" + context.robotName
      LockService.removeSpaceLockByKey(key, sr.sceneId, context.mapName)
      // 切地图
      val newMapName = rr.selfReport?.stand?.areaId.toString()
      if (newMapName == context.mapName) {
        logger.error("${context.robotName} switch map but map not change")
        return false
      }
      if (ContextManagerService.switchRobotMap(rr.robotName, newMapName)) {
        // 申请新锁闭信息
        val spaceLock = SpaceLockCalculate.robotStaticSpaceLock(
          context.robotName,
          sr.sceneId,
          context.groupName,
          context.baseDomain.robotHeading,
          context.baseDomain.curPoint,
          context.mapName,
        )
        if (spaceLock == null) {
          logger.error("${context.robotName} apply space lock failed")
          return false
        }
        val lockHandle = LockService.lockHandle(
          LockRequest(
            id = context.robotName,
            mapName = context.mapName,
            sceneId = context.sceneId,
            spaces = mutableListOf(spaceLock),
          ),
        )
        return lockHandle.success
      }
    } catch (e: Exception) {
      logger.error("${rr.robotName} switch distributed map is failed! $e")
      return false
    }
    return false
  }

  /**
   *  申请在交管的空间资源
   * */
  override fun requestSpaceResource(
    owner: String,
    mapName: String,
    spaceResource: List<Polygon>,
  ): Boolean {
    logger.info("request space resource for $owner | $mapName | $spaceResource")
    try {
      val points = mutableListOf<List<Vector>>()
      spaceResource.forEach { it ->
        points.add(it.points.map { Vector(it.x, it.y) }.toList())
      }
      return DistributedTrafficService.requestSpaceLock(owner, mapName, sr.sceneId, points)
    } catch (e: Exception) {
      return false
    }
  }

  /**
   *  释放在交管申请的空间资源
   * */
  override fun releaseSpaceResource(owner: String, mapName: String): Boolean {
    logger.info("release space resource for $owner | $mapName")
    // todo 存在 owner 先前不在系统中，后在交管上线导致删除构建的 key 无效的情况
    val groupName = try {
      ContextManagerService.queryRobotContext(owner).groupName
    } catch (e: Exception) {
      LockType.EXTERNAL.toString()
    }

    val key = LockType.EXTERNAL.toString() + "-><-" + groupName + "-><-" + owner
    return LockService.removeSpaceLockByKey(key, sr.sceneId, mapName)
  }

  /**
   * 机器人的碰撞模型改变
   */
  override fun onRobotCollisionModelChanged(rr: RobotRuntime, model: RobotCollisionModel) {
    // do nothing
  }

  /**
   * 机器人身上货物的形状发生变化。包括取货完成、放货完成。
   * TODO 货架入场，是机器人放下货架或者货架第一次出现在地图里；货架出场，是机器人取货架到自己身上，或货架离开地图。
   */
  override fun onRobotLoadRelationChanged(rr: RobotRuntime, load: List<RobotLoadRelation>?) {
    // do nothing
  }

  private fun createContainerType(containerType: SceneContainerType) {
    logger.info("create container type ${containerType.name}")

    val groups = sr.robotGroups.values.toList()
    DistributedTrafficService.addContainerTypes(listOf(containerType), groups, sr.sceneId)
  }

  private fun updateContainerType(containerType: SceneContainerType) {
    logger.info("update container type ${containerType.name}")
    val groups = sr.robotGroups.values.toList()
    DistributedTrafficService.updateContainerTypes(containerType, groups, sr.sceneId)
  }

  private fun removeContainerType(containerName: String) {
    logger.info("remove container type $containerName")
    RobotContainerSpaceLock.removeContainerModel(sr.sceneId, containerName)
  }
}