package com.seer.trick.fleet.diagnosis

import CheckRobotOrderReq
import DiagnosisReport
import RobotOrderDiagnostics
import com.seer.trick.BzError
import com.seer.trick.fleet.domain.RejectReason
import com.seer.trick.fleet.order.OrderRuntime
import com.seer.trick.fleet.service.MapService
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.SceneRuntime

object RobotOrderPrediction {

  /**
   * 初步评估机器人是否能接单（业务单）。
   */
  fun checkRobotAvailableForBzOrders(rr: RobotRuntime): RejectReason? {
    val reason = RobotOrderDiagnostics.canAcceptBzOrders(rr, false).firstRejection
    rr.orderReject = reason
    return reason
  }

  /**
   * 初步评估机器人能否接新单。
   */
  fun checkRobotAvailableForNewBzOrders(rr: RobotRuntime): RejectReason? {
    val reason = RobotOrderDiagnostics.canAcceptNewBzOrders(rr, false).firstRejection
    rr.orderReject = reason
    return reason
  }

  /**
   * 机器人是否应该去停靠点。
   */
  fun checkRobotShouldParking(rr: RobotRuntime, fromPoint: String?): RejectReason? {
    val reason = RobotOrderDiagnostics.shouldPark(rr, fromPoint, false).firstRejection
    return reason
  }

  /**
   * 机器人是否必须充电。
   */
  fun checkRobotMustCharging(rr: RobotRuntime, fromPoint: String?): RejectReason? {
    val reason = RobotOrderDiagnostics.mustCharge(rr, fromPoint, false).firstRejection
    return reason
  }

  /**
   * 机器人是否可以充电。
   */
  fun checkRobotMayCharging(rr: RobotRuntime, fromPoint: String?): RejectReason? {
    val reason = RobotOrderDiagnostics.mayCharge(rr, fromPoint, false).firstRejection
    return reason
  }

  /**
   * 判断运单是否能够执行步骤
   */
  fun checkOrderExecutableStep(or: OrderRuntime): RejectReason? {
    val reason = RobotOrderDiagnostics.checkOrderExecutableStep(or, false).firstRejection
    or.executionReject = reason
    return reason
  }

  /**
   *  能被分或二分的单子只能处于：ToBeAllocated、Allocated、Executing、Pending
   *  且 Executing、Pending 要检查是否可打断
   *  副作用：设置 OrderRuntime.allocationReject
   */
  fun checkOrderBeAllocatedOrAllocatedAgain(or: OrderRuntime): RejectReason? {
    val reason = RobotOrderDiagnostics.checkOrderBeAllocatedOrAllocatedAgain(or, false).firstRejection
    or.allocationReject = reason
    return reason
  }

  /**
   * 校验机器人能否执行步骤
   */
  fun checkRobotExecutingStep(rr: RobotRuntime): RejectReason? {
    val reason = RobotOrderDiagnostics.canExecuteStep(rr, false).firstRejection
    rr.stepReject = reason
    return reason
  }

  /**
   * 判断运单是否可达
   */
  fun checkOrderReachable(or: OrderRuntime): RejectReason? {
    val reason = RobotOrderDiagnostics.checkOrderReachable(or, false).firstRejection
    or.allocationReject = reason
    return reason
  }

  /**
   * 判断运单是否有可用的机器人
   */
  fun checkOrderRobotAvailable(or: OrderRuntime): RejectReason? {
    val reason = RobotOrderDiagnostics.checkRobotAvailableForOrder(or, false).firstRejection
    or.allocationReject = reason
    return reason
  }

  /**
   * 完整诊断机器人是否能接单（业务单）。
   */
  fun diagnoseRobotAvailableForBzOrders(sr: SceneRuntime, req: CheckRobotOrderReq): DiagnosisReport {
    val rr = sr.robots[req.robotName]
    val result = RobotOrderDiagnostics.canAcceptBzOrders(rr, true)
    return result.report!!
  }

  /**
   * 完整诊断机器人能否接新单。
   */
  fun diagnoseRobotAvailableForNewBzOrders(sr: SceneRuntime, req: CheckRobotOrderReq): DiagnosisReport {
    val rr = sr.robots[req.robotName]
    val result = RobotOrderDiagnostics.canAcceptNewBzOrders(rr, true)
    return result.report!!
  }

  fun diagnoseRobotCanAcceptThisOrders(sr: SceneRuntime, req: CheckRobotOrderReq): DiagnosisReport {
    val rr = sr.robots[req.robotName]
    val or = sr.orders[req.orderId]
    val result = RobotOrderDiagnostics.canAcceptThisOrders(rr, or, true)
    return result.report!!
  }

  /**
   * 完整诊断机器人是否应该去停靠点。
   */
  fun diagnoseRobotShouldParking(sr: SceneRuntime, req: CheckRobotOrderReq): DiagnosisReport {
    val rr = sr.robots[req.robotName]
    val fromPoint = if (rr != null) MapService.findBestStartPointNameForPlan(rr) else null
    val result = RobotOrderDiagnostics.shouldPark(rr, fromPoint, true)
    return result.report!!
  }

  /**
   * 完整诊断机器人是否必须充电。
   */
  fun diagnoseRobotMustCharging(sr: SceneRuntime, req: CheckRobotOrderReq): DiagnosisReport {
    val rr = sr.robots[req.robotName]
    val fromPoint = if (rr != null) MapService.findBestStartPointNameForPlan(rr) else null
    val result = RobotOrderDiagnostics.mustCharge(rr, fromPoint, true)
    return result.report!!
  }

  /**
   * 完整诊断机器人是否可以充电。
   */
  fun diagnoseRobotMayCharging(sr: SceneRuntime, req: CheckRobotOrderReq): DiagnosisReport {
    val rr = sr.robots[req.robotName]
    val fromPoint = if (rr != null) MapService.findBestStartPointNameForPlan(rr) else null
    val result = RobotOrderDiagnostics.mayCharge(rr, fromPoint, true)
    return result.report!!
  }

  /**
   * 完整诊断机器人是否可以到达指定点位
   */
  fun diagnoseRobotCanReachPoint(sr: SceneRuntime, req: CheckRobotOrderReq): DiagnosisReport {
    val rr = sr.robots[req.robotName]
    val toPoint = req.pointName
    val fromPoint = if (rr != null) MapService.findBestStartPointNameForPlan(rr) else null
    val result = RobotOrderDiagnostics.robotCanReachPoint(rr, toPoint, true, fromPoint)
    return result.report!!
  }

  /**
   * 判断机器人为什么不动
   */
  fun diagnoseRobotRemainsStationary(sr: SceneRuntime, req: CheckRobotOrderReq): DiagnosisReport {
    val rr = sr.robots[req.robotName]
    val result = RobotOrderDiagnostics.checkRobotRemainsStationary(rr, true)
    return result.report!!
  }

  /**
   * 运单为啥没有机器人接单
   */
  fun diagnoseOrderNoRobotMatch(sr: SceneRuntime, req: CheckRobotOrderReq): DiagnosisReport {
    val or = sr.orders[req.orderId]
    val result = RobotOrderDiagnostics.checkOrderNoRobotMatch(or, true)
    return result.report!!
  }

  /**
   * 运单为啥不继续执行
   */
  fun diagnoseOrderNoContinueExecuting(sr: SceneRuntime, req: CheckRobotOrderReq): DiagnosisReport {
    val or = sr.orders[req.orderId]
    val rr = sr.listRobots().find { or?.order?.actualRobotName == it.robotName }
    val fromPoint = if (rr != null) MapService.findBestStartPointNameForPlan(rr) else null
    val result = RobotOrderDiagnostics.checkOrderNoContinueExecuting(or, rr, fromPoint, true)
    return result.report!!
  }
}

class RobotNotExistException : BzError("RobotNotExist")

class OrderNotExistException : BzError("OrderNotExist")
class NotPointException : BzError("PointNotExist")