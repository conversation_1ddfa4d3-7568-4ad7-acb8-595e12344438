package com.seer.trick.fleet.traffic


import com.seer.trick.fleet.domain.PlanResult
import com.seer.trick.fleet.domain.RobotShowTrafficMessage
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.SceneRuntime

/**
 * 假的 TrafficService
 */
class DummyTrafficService(override val sr: SceneRuntime) : TrafficService() {

  override fun plan(task: TrafficTaskRuntime): PlanResult = PlanResult(false)

  override fun afterTrafficTaskDone(rr: RobotRuntime): Boolean = true

  override fun afterTrafficTaskCancelled(rr: RobotRuntime) {
    // do nothing
  }

  override fun afterMoveActionFailed(rr: RobotRuntime, orderId: String?) {
    // do nothing
  }

  override fun resetByRobot(rr: RobotRuntime) {
    // do nothing
  }

  override fun resetAll() {
    // do nothing
  }

  override fun showTrafficResourceMessage(): Map<String, RobotShowTrafficMessage> = emptyMap()
}