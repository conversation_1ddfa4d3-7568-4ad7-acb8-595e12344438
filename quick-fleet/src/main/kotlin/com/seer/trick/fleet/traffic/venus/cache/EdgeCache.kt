package com.seer.trick.fleet.traffic.venus.cache

import com.seer.trick.fleet.domain.AABB
import com.seer.trick.fleet.domain.Point2D
import kotlin.math.max
import kotlin.math.min

/**
 * Edge 缓存的伴生对象，提供全局访问点
 */
object EdgeCacheProvider {
  private val cache = mutableMapOf<Pair<Point2D, Point2D>, AABB>()

  /**
   * 获取或计算 Edge 的 bbox
   *
   * @param p1 边的起始点
   * @param p2 边的结束点
   * @return 边的 bbox
   */
  fun getOrCompute(p1: Point2D, p2: Point2D): AABB {
    val key = Pair(p1, p2)
    return cache.getOrPut(key) {
      AABB(min(p1.x, p2.x), min(p1.y, p2.y), max(p1.x, p2.x), max(p1.y, p2.y))
    }
  }

  /**
   * 清空缓存
   */
  fun clear() {
    cache.clear()
  }

  /**
   * 缓存大小
   */
  fun size(): Int = cache.size
}
