package com.seer.trick.fleet.mock.service

import com.seer.trick.fleet.mock.MockSeerRobotAlarm
import com.seer.trick.fleet.mock.MockSeerRobotRecord
import com.seer.trick.fleet.mock.MockSeerRobotRuntime
import kotlin.random.Random

object MockAlarmProcessor {

  /**
   * CAS 保证更新的正确性，避免数据丢失或不一致 TODO record 的所有更新都用 CAS
   *
   */
  private fun updateAlarm(mr: MockSeerRobotRuntime, update: MockSeerRobotRecord.() -> MockSeerRobotRecord) {
    while (true) {
      val oldRecord = mr.record
      val newRecord = oldRecord.update()
      val updatedRecord = if (newRecord.alarms !== oldRecord.alarms && newRecord.alarms != oldRecord.alarms) {
        newRecord.copy(alarms = mergeAlarms(oldRecord.alarms, newRecord.alarms))
      } else {
        newRecord
      }
      if (mr.compareAndSetRecord(oldRecord, updatedRecord)) break
      // 简单的退避策略，避免活锁
      Thread.sleep(Random.nextLong(1, 10))
    }
  }

  /**
   * 如果 newAlarms 中没有某个告警，但 oldAlarms 中有，那么这个告警应该被移除。
   * 如果 newAlarms 中有某个告警，且 oldAlarms 中也有，那么应该用 newAlarms 中的版本覆盖 oldAlarms 中的版本。
   * 如果 newAlarms 中有新的告警，应该添加到结果中。
   *
   */
  private fun mergeAlarms(
    oldAlarms: List<MockSeerRobotAlarm>,
    newAlarms: List<MockSeerRobotAlarm>,
  ): List<MockSeerRobotAlarm> {
    val newAlarmMap = newAlarms.associateBy { it.code }
    return oldAlarms
      .filter { oldAlarm -> newAlarmMap.containsKey(oldAlarm.code) }
      .map { oldAlarm -> newAlarmMap[oldAlarm.code] ?: oldAlarm }
      .plus(newAlarms.filter { newAlarm -> !oldAlarms.any { it.code == newAlarm.code } })
  }

  /**
   * 更新告警
   */
  fun updateAlarms(mr: MockSeerRobotRuntime, update: (List<MockSeerRobotAlarm>) -> List<MockSeerRobotAlarm>) {
    updateAlarm(mr) { copy(alarms = update(alarms)) }
  }

  /**
   * 添加告警
   * @param alarm 告警
   */
  fun addOrUpdateAlarm(mr: MockSeerRobotRuntime, alarm: MockSeerRobotAlarm) {
    updateAlarms(mr) { it + alarm }
  }

  /**
   * 移除告警
   * @param code 告警的 code
   */
  fun removeAlarm(mr: MockSeerRobotRuntime, code: String) {
    updateAlarms(mr) { it.filterNot { alarm -> alarm.code == code } }
  }

  /**
   * 移除所有告警
   */
  fun removeAllAlarm(mr: MockSeerRobotRuntime) {
    updateAlarms(mr) { emptyList() }
  }
}