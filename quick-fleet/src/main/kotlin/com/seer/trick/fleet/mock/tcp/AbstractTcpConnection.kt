package com.seer.trick.fleet.mock.tcp


import com.seer.trick.base.soc.SocAttention
import com.seer.trick.fleet.mock.MockSeerRobotAlarm
import com.seer.trick.fleet.mock.MockSeerRobotRuntime
import com.seer.trick.fleet.mock.service.MockAlarmProcessor
import com.seer.trick.fleet.mock.service.MockMessageProcessor
import com.seer.trick.helper.JsonHelper
import com.seer.trick.robot.vendor.seer.rbk.RbkFrame
import io.netty.channel.ChannelHandlerContext
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * TODO 移到 mock
 */
abstract class AbstractTcpConnection(open var mr: MockSeerRobotRuntime) {

  private var flowNoCounter = 0

  protected val logger: Logger = LoggerFactory.getLogger(javaClass)

  abstract fun logAndUpdateState(message: String, attention: SocAttention = SocAttention.None, noLog: Boolean = false)

  abstract fun init()

  abstract fun dispose()

  abstract fun disconnect()

  abstract fun reset()

  abstract fun onMessage(ctx: ChannelHandlerContext, frame: RbkFrame)

  protected fun handleMessage(f: RbkFrame): String {
    // TODO 增加日志：收到的报文、消息，处理时间，相应内容。并且需要用开关来控制

    // 校验控制权
    val failed = MockMessageProcessor.ensureCurrentLock(mr, f.apiNo, f.address)
    if (failed != null) return JsonHelper.writeValueAsString(failed)

    return when (f.apiNo) {
      1000 -> MockMessageProcessor.handle1000(mr, f)
      1020 -> MockMessageProcessor.handle1020(mr, f)
      1060 -> MockMessageProcessor.handle1060(mr, f)
      1100 -> MockMessageProcessor.handle1100(mr, f)
      1110 -> MockMessageProcessor.handle1110(mr, f)
      1300 -> MockMessageProcessor.handle1300(mr, f)
      1302 -> MockMessageProcessor.handle1302(mr, f)
      1400 -> MockMessageProcessor.handle1400(mr, f)
      1500 -> MockMessageProcessor.handle1500(mr, f)
      2010 -> MockMessageProcessor.handle2010(mr, f)
      2022 -> MockMessageProcessor.handle2022(mr, f)
      2025 -> MockMessageProcessor.handle2025(mr, f)
      3001 -> MockMessageProcessor.handle3001(mr, f)
      3002 -> MockMessageProcessor.handle3002(mr, f)
      3003 -> MockMessageProcessor.handle3003(mr, f)
      3051 -> MockMessageProcessor.handle3051(mr, f)
      3066 -> MockMessageProcessor.handle3066(mr, f)
      4005 -> MockMessageProcessor.handle4005(mr, f)
      4006 -> MockMessageProcessor.handle4006(mr, f)
      4009 -> MockMessageProcessor.handle4009(mr, f)
      4010 -> MockMessageProcessor.handle4010(mr, f)
      4011 -> MockMessageProcessor.handle4011(mr, f)
      4012 -> MockMessageProcessor.handle4012(mr, f)
      4200 -> MockMessageProcessor.handle4200(mr, f)
      6004 -> MockMessageProcessor.handle6004(mr, f)
      6080 -> MockMessageProcessor.handle6080(mr, f)
      6801 -> MockMessageProcessor.handle6801(mr, f)
      6802 -> MockMessageProcessor.handle6802(mr, f)
      6803 -> MockMessageProcessor.handle6803(mr, f)
      6804 -> MockMessageProcessor.handle6804(mr, f)
      else -> {
        MockAlarmProcessor.addOrUpdateAlarm(
          mr,
          MockSeerRobotAlarm("Error", "52502", "Dispatching system command error"),
        )
        return ""
      }
    }
  }

  /**
   * 避免重复发送相同的 flowNo
   */
  @Synchronized
  protected fun nextFlowNo(): Int {
    flowNoCounter = (flowNoCounter + 1) % 512
    return flowNoCounter
  }
}