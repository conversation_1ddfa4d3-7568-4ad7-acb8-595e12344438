package com.seer.trick.fleet.traffic.distributed.deadlock.helper

import com.seer.trick.fleet.domain.PositionType
import com.seer.trick.fleet.traffic.distributed.context.domain.RobotContext
import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper
import com.seer.trick.fleet.traffic.distributed.helper.PathInfoHelper
import com.seer.trick.fleet.traffic.distributed.map.Point
import com.seer.trick.fleet.traffic.distributed.plan.PlanPathService
import com.seer.trick.fleet.traffic.distributed.plan.ResStatus
import com.seer.trick.fleet.traffic.distributed.plan.model.PathAction
import com.seer.trick.fleet.traffic.distributed.plan.model.Restraint
import org.slf4j.LoggerFactory
import java.util.*

/**
 * 解死锁到规划新路径的一个中间辅助类
 * */
object DeadLockPathPlanHelper {

  private val logger = LoggerFactory.getLogger(javaClass)

  fun toMiddlePath(
    context: RobotContext,
    middle: Point,
    action: PathAction,
    cantRotate: MutableList<String>,
    forbiddenPoint: MutableList<String>,
  ): MutableList<PathAction>? {
    // 准备数据
    val restraint = Restraint(
      rePlan = true,
      forbiddenPoints = forbiddenPoint,
      forbiddenLine = mutableListOf(),
      extraCost = mutableMapOf(),
      cantRotatePoint = cantRotate,
    )
    val request = context.request.request?.copy(
      start = action.target.pointName,
      target = middle.pointName,
      startAngle = action.robotOutHeading,
      targetAngle = null,
      startType = PositionType.Point,
      containerStartAngle = action.containerOutHeading,
      containerTargetAngle = AngleHelper.ERROR_ANGLE,
      targetCombinedStop = false,
    ) // todo 不用考虑中间过程货架最终角度
    val plan = PlanPathService.rePlanPath(request!!, restraint)
    if (plan.status == ResStatus.SUCCESS) {
      return PathInfoHelper.joinPath(Collections.singletonList(action), plan.path)
    }
    return null
  }

  fun toTargetPath(context: RobotContext, middlePath: MutableList<PathAction>): MutableList<PathAction>? {
    val action = middlePath.last()
    val restraint = Restraint(
      rePlan = true,
      forbiddenPoints = mutableListOf(),
      forbiddenLine = mutableListOf(),
      extraCost = mutableMapOf(),
      cantRotatePoint = mutableListOf(),
    )
    val request = context.request.request?.copy(
      start = action.target.pointName,
      startType = PositionType.Point,
      target = context.request.target!!,
      startAngle = action.robotOutHeading,
      containerStartAngle = action.containerOutHeading,
    )
    val plan = PlanPathService.rePlanPath(request!!, restraint)
    if (plan.status == ResStatus.SUCCESS) {
      return PathInfoHelper.joinPath(middlePath, plan.path)
    }
    return null
  }

  fun toTargetPath(
    context: RobotContext,
    cantRotate: MutableList<String>,
    forbiddenPoint: MutableList<String>,
    action: PathAction?,
  ): MutableList<PathAction>? {
    val newAction = action ?: context.plan.queryCurrentAction() ?: throw IllegalStateException("current action is null")
    val restraint = Restraint(
      rePlan = true,
      forbiddenPoints = forbiddenPoint,
      forbiddenLine = mutableListOf(),
      extraCost = mutableMapOf(),
      cantRotatePoint = cantRotate,
    )
    val request = context.request.request?.copy(
      start = newAction.target.pointName,
      startType = PositionType.Point,
      target = context.request.target!!,
      startAngle = newAction.robotOutHeading,
      containerStartAngle = newAction.containerOutHeading,
    )
    val plan = PlanPathService.rePlanPath(request!!, restraint)
    if (plan.status == ResStatus.SUCCESS) {
      var path = plan.path
      if (action != null) {
        path = PathInfoHelper.joinPath(Collections.singletonList(action), path)
      }
      return path
    }
    return null
  }
}