package com.seer.trick.fleet.mars

import com.seer.trick.BzError
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.soc.SocService
import com.seer.trick.base.soc.SysMonitorService
import com.seer.trick.fleet.service.SceneRuntime
import com.seer.trick.helper.getTypeMessage
import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.max

/**
 * Fleet 3 临时版交管
 */
class MarsScheduler(private val sr: SceneRuntime, private val marsService: MarsService) {

  private val logger = LoggerFactory.getLogger(javaClass)

  val pendingSiteIds: MutableMap<String, List<String>> = ConcurrentHashMap()

  /**
   * 先规划出一条途径各个 steps 的路径
   */
  fun buildMoves(robotName: String, steps: List<EntityValue>): List<EntityValue> {
    val rr = sr.mustGetRobot(robotName)
    val startSite = rr.selfReport?.main?.currentPoint

    if (startSite.isNullOrBlank()) throw BzError("errRobotNoCurrentStation", robotName)
    return fillMiddleLandmarks(robotName, startSite, steps)
  }

  /**
   * 将粗步骤，如：从当前点到 A，再到 B 取货，再到 C 放货，再到 D
   * 寻路，填充中间点
   * 锁定资源
   */
  private fun fillMiddleLandmarks(
    robotId: String,
    startSite: String,
    steps: List<EntityValue>,
  ): MutableList<EntityValue> {
    logger.debug("寻找中间点，机器人=$robotId，起点=$startSite，步骤=$steps")
    val siteIds: List<String> = steps.map {
      val siteIdOrBinId = it["id"] as String? // 可能是 Bin 也可能是站点
      val name = marsService.getPointByLoc(robotId, siteIdOrBinId!!)?.name
        ?: throw BzError("errNoSuchBinOrLocation", siteIdOrBinId)
      name
    }

    marsService.scheduleService.checkConnectivity(robotId, startSite, siteIds)

    val paths = awaitFindPath(robotId, startSite, siteIds)
    return stepAssembly(robotId, startSite, steps, siteIds, paths)

//    val moves: MutableList<EntityValue> = ArrayList()
//    var fromSite = startSite
//    for ((si, toSiteId) in siteIds.withIndex()) {
//      if (fromSite != toSiteId) {
//        val sites = paths[si].map { marsService.getSiteBySiteIdOrBinId(robotId, it)!! }
//        // 去掉最后点，只加中间点
//        for (i in 1 until sites.size - 1) {
//          fromSite = sites[i - 1].name
//          // TODO 之前遇到的 SELF_POSITION 的问题
//          moves += mutableMapOf("id" to sites[i].name, "source_id" to fromSite)
//        }
//        fromSite = sites[max(sites.size - 2, 0)].name // 倒数第二个点
//      }
//      // 最后一个指令，如果需要取放货
//      val step = steps[si]
//      step["source_id"] = fromSite
//      moves += step
//      fromSite = toSiteId
//    }
//    return moves
  }

  private fun stepAssembly(
    robotId: String,
    startSite: String,
    steps: List<EntityValue>,
    siteIds: List<String>,
    paths: List<List<String>>,
  ): MutableList<EntityValue> {
    val moves: MutableList<EntityValue> = ArrayList()
    var fromSite = startSite
    for ((si, toSiteId) in siteIds.withIndex()) {
      if (fromSite != toSiteId) {
        val sites = paths[si].map { marsService.getPointByLoc(robotId, it)!! }
        // 去掉最后点，只加中间点
        for (i in 1 until sites.size - 1) {
          fromSite = sites[i - 1].name
          // TODO 之前遇到的 SELF_POSITION 的问题
          moves += mutableMapOf("id" to sites[i].name, "source_id" to fromSite)
        }
        fromSite = sites[max(sites.size - 2, 0)].name // 倒数第二个点
      }
      // 最后一个指令，如果需要取放货
      val step = steps[si]
      step["source_id"] = fromSite
      moves += step
      fromSite = toSiteId
    }

    return moves
  }

  private fun awaitFindPath(
    robotId: String,
    startSite: String,
    siteIds: List<String>,
  ): List<List<String>> {
    try {
      while (true) {
        SysMonitorService.log(
          subject = "Light",
          target = robotId,
          field = "awaitFindPath",
          value = "Start, from: $startSite, to: $siteIds",
        )

        pendingSiteIds[robotId] = siteIds
        val paths = marsService.scheduleService.plan(robotId, startSite, siteIds)
        if (paths != null) {
          SysMonitorService.log(
            subject = "Light",
            target = robotId,
            field = "awaitFindPath",
            value = "Found, from: $startSite, to: $siteIds, paths: $paths",
            remove = true,
          )
          SocService.removeNode("AwaitPathFind:$robotId")
          pendingSiteIds.remove(robotId)
          return paths
        } else {
          SysMonitorService.log(
            subject = "Light",
            target = robotId,
            field = "awaitFindPath",
            value = "Not found, from: $startSite, to: $siteIds",
            level = SysMonitorService.SysMonitorLevel.Warn,
          )

          SocService.updateNode(
            "机器人",
            "AwaitPathFind:$robotId",
            "机器人寻路:$robotId",
            "找不到可用路径，起点=$startSite，中间点=$siteIds",
          )
        }
        Thread.sleep(2000)
      }
    } catch (e: InterruptedException) {
      SysMonitorService.log(
        subject = "Light",
        target = robotId,
        field = "awaitFindPath",
        value = "Interrupted, from: $startSite, to: $siteIds",
        level = SysMonitorService.SysMonitorLevel.Error,
      )
      throw e
    } catch (e: Exception) {
      SysMonitorService.log(
        subject = "Light",
        target = robotId,
        field = "awaitFindPath",
        value = "Error, ${e.getTypeMessage()}, from: $startSite, to: $siteIds",
        level = SysMonitorService.SysMonitorLevel.Error,
      )
      throw e
    }
  }

  fun reset() {
    pendingSiteIds.clear()
    for (rr in marsService.listRobots()) SocService.removeNode("AwaitPathFind:${rr.robotName}")
  }

  /**
   * 路径规划尝试规划一次
   */
  fun tryBuildMoves(robotName: String, steps: List<EntityValue>): List<EntityValue> {
    val rr = sr.mustGetRobot(robotName)
    val startSite = rr.selfReport?.main?.currentPoint

    if (startSite.isNullOrBlank()) throw BzError("errRobotNoCurrentStation", robotName)

    logger.debug("尝试寻找中间点，机器人=$robotName，起点=$startSite，步骤=$steps")
    val siteIds: List<String> = steps.map {
      val siteIdOrBinId = it["id"] as String? // 可能是 Bin 也可能是站点
      val name = marsService.getPointByLoc(robotName, siteIdOrBinId!!)?.name
        ?: throw BzError("errNoSuchBinOrLocation", siteIdOrBinId)
      name
    }
    val paths = marsService.scheduleService.plan(robotName, startSite, siteIds)
    if (paths.isNullOrEmpty()) return emptyList()
    return stepAssembly(robotName, startSite, steps, siteIds, paths)
  }
}