package com.seer.trick.fleet.mars

import com.seer.trick.BzError
import com.seer.trick.fleet.service.SceneRuntime
import com.seer.trick.helper.CollectionHelper
import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

/**
 * Fleet 3 临时版交管（底层）
 */
class MarsScheduleService(private val sceneRuntime: SceneRuntime, private val marsService: MarsService) {
  
  private val logger = LoggerFactory.getLogger(javaClass)
  
  // 机器人因当前位置占用的点位
  // private val robotLocationMap: MutableMap<String, SceneLocation> = ConcurrentHashMap() // robot -> loc
  
  // 机器人因交通管制申请的点位
  private val lockedSiteIdsMap: MutableMap<String, String> = ConcurrentHashMap() // site -> robot
  
  private val parkingSiteIdsMap: MutableMap<String, String> = ConcurrentHashMap() // site -> orderId
  
  private val planLock = ReentrantLock()
  
  // /**
  //  * 获取当前拥有站点的机器人
  //  */
  // fun getOwnerBySiteId(siteId: String): String? =
  //   lockedSiteIdsMap[siteId] ?: robotLocationMap.entries.find { e -> e.value.site?.id == siteId }?.key
  
  // /**
  //  * 更新机器人位置占用的点位
  //  */
  // fun updateRobotLocation(robotId: String, loc: SceneLocation?) {
  //   if (loc != null) {
  //     robotLocationMap[robotId] = loc
  //   } else {
  //     robotLocationMap.remove(robotId)
  //   }
  // }
  
  // fun updateParkingSiteId(orderId: String, siteId: String?) {
  //   if (siteId != null) {
  //     parkingSiteIdsMap[orderId] = siteId
  //   } else {
  //     parkingSiteIdsMap.remove(orderId)
  //   }
  // }
  
  // fun checkParkingBySiteId(siteId: String): String? = parkingSiteIdsMap.entries.find { e -> e.value == siteId }?.key
  
  // TODO 光通讯项目不用
  // fun checkParkingOrder() {
  //   // 移除已经结束获取被取消的停靠订单所预定的停靠站点
  //   parkingSiteIdsMap.forEach { (orderId, _) ->
  //     rachel.orderService.orders[orderId] ?: updateParkingSiteId(orderId, null)
  //   }
  // }
  
  /**
   * TODO 光通讯项目不用
   * 规划从起点到步骤终点的路径。如果不联通，直接失败。否则会阻塞反复寻路。
   */
  // fun schedule(rr: MrRobotRuntime, step: MrStep, startSite: String): ScheduleResult {
  //   val endSite = step.location.site!!
  //
  //   if (startSite == endSite) return ScheduleResult(ScheduleResultKind.NoMove)
  //
  //   // 先不考虑任何锁的情况下寻路
  //   val sp = rachel.sceneRuntime.map.getShortestPath(startSite, endSite)
  //   if (sp?.found != true) return ScheduleResult(ScheduleResultKind.NoPath)
  //
  //   SocService.updateNode(
  //     "机器人",
  //     "Robot:MrSchedule:${rr.id}",
  //     "机器人路径规划:${rr.id}",
  //     "起点 $startSite，终点 $endSite",
  //     SocAttention.Red,
  //   )
  //   try {
  //     while (true) {
  //       val nextSites = doSchedule(rr, startSite, endSite)
  //       if (nextSites != null) return ScheduleResult(ScheduleResultKind.Success, nextSites)
  //       try {
  //         Thread.sleep(1000)
  //       } catch (e: InterruptedException) {
  //         return ScheduleResult(ScheduleResultKind.Interrupted)
  //       }
  //     }
  //   } finally {
  //     SocService.removeNode("Robot:MrSchedule:${rr.id}")
  //   }
  // }
  
  // 去掉占用的情况下，寻路；最多返回下面三个站点
  // private fun doSchedule(rr: MrRobotRuntime, startSite: String, endSite: String): List<String>? {
  //   planLock.withLock {
  //     // 去掉已锁定资源，重新尝试构造地图
  //     val lockedSiteIds = listLockedSiteIds(rr.id)
  //     val alg = SceneMapAlg(rachel.sceneRuntime.map.sceneMapRuntime.sceneMap, lockedSiteIds) // 去掉已锁定的资源
  //     // val ma = ModelMapAlg(mm, null)
  //
  //     val sp = alg.getShortestPath(startSite, endSite)
  //     if (!sp.found) return null
  //
  //     // 只锁前面三个点
  //     val nextSites = if (sp.points.size <= 4) {
  //       sp.points.subList(1, sp.points.size)
  //     } else {
  //       sp.points.subList(1, 4)
  //     }
  //
  //     val newLockedSiteIds = mutableSetOf<String>()
  //     for (p in nextSites) {
  //       if (!lockedSiteIds.contains(p)) newLockedSiteIds.add(p)
  //     }
  //     logger.info("机器人 '${rr.id}' 锁定资源 ${newLockedSiteIds.joinToString(", ")}")
  //
  //     for (siteId in newLockedSiteIds) {
  //       lockedSiteIdsMap[siteId] = rr.id
  //     }
  //
  //     // 把锁定站点所在特区内的点都加入申请资源列表
  //     for (zoneIndex in rachel.sceneRuntime.map.sceneMapRuntime.zoneIdToIndexMap.values) {
  //       if (zoneIndex.zone.type != "BlockArea") continue
  //       val zoneSiteIds = zoneIndex.siteIds
  //       if (!CollectionHelper.setsHasSame(zoneSiteIds, newLockedSiteIds)) continue
  //       for (siteId in zoneSiteIds) {
  //         lockedSiteIdsMap[siteId] = rr.id
  //       }
  //     }
  //
  //     return nextSites
  //   }
  // }
  
  /**
   * 检查连通性
   * TODO 不用计算最短路
   */
  fun checkConnectivity(robotName: String, startSite: String, siteIds: List<String>) {
    var fromId = startSite
    for (siteId in siteIds) {
      if (fromId == siteId) continue
      val sp = marsService.getShortestPath(robotName, fromId, siteId)
      if (!sp.found) throw BzError("errNoRoutePath", fromId, siteId)
      fromId = siteId
    }
  }
  
  /**
   * 给老版本和光通讯使用
   * 调用前先检查连通性
   * 规划成功，锁定资源
   */
  fun plan(robotId: String, startSite: String, siteIds: List<String>): List<List<String>>? {
    planLock.withLock {
      val lockedSiteIds = listLockedSiteIds(robotId)
      var fromId = startSite
      val paths: MutableList<List<String>> = ArrayList(siteIds.size)
      val newLockedSiteIds = mutableSetOf<String>()
      for (siteId in siteIds) {
        if (fromId == siteId) {
          paths += emptyList<String>()
        } else {
          val sp = marsService.getShortestPath(robotId, fromId, siteId, lockedSiteIds)
          if (!sp.found) return null
          paths += sp.points
          for (p in sp.points) {
            if (!lockedSiteIds.contains(p)) newLockedSiteIds.add(p)
          }
          
          fromId = siteId
        }
      }
      
      logger.info("机器人 '$robotId' 锁定资源 ${newLockedSiteIds.joinToString(", ")}")
      
      for (siteId in newLockedSiteIds) {
        lockedSiteIdsMap[siteId] = robotId
      }
      for (zones in marsService.listBlockZones()) {
        val zoneSiteIds = zones.pointNames
        if (!CollectionHelper.setsHasSame(zoneSiteIds, newLockedSiteIds)) continue
        for (siteId in zoneSiteIds) {
          lockedSiteIdsMap[siteId] = robotId
        }
        logger.info("机器人 '$robotId' 锁定互斥区资源 ${zones.zone.id}:${zoneSiteIds.joinToString(", ")}")
      }
      
      return paths
    }
  }
  
  fun listMySiteIds(robotId: String): List<String> = lockedSiteIdsMap.keys.filter { lockedSiteIdsMap[it] == robotId }
  
  fun unlockByRobot(robotId: String) {
    planLock.withLock {
      val siteIds = listMySiteIds(robotId)
      logger.info("解锁机器人 '$robotId' 的点位：${siteIds.joinToString(", ")}")
      for (siteId in siteIds) lockedSiteIdsMap.remove(siteId)
    }
  }

  /**
   * 按传入的 SiteIds 进行解锁，涉及互斥区时也会解锁互斥区的内容
   */
  fun unlockBySiteIds(robotId: String, siteIds: List<String>) {
    planLock.withLock {
      // Step 1: 初始化要解锁的点位集合
      val lockedSiteIds = siteIds.toMutableSet()

      // Step 2: 遍历所有互斥区资源，将相关点位加入到解锁集合中
      marsService.listBlockZones().forEach { zone ->
        if (zone.pointNames.any { it in siteIds }) {
          lockedSiteIds.addAll(zone.pointNames)
        }
      }

      // Step 3: 解锁的点位
      val idsToUnlock = lockedSiteIds.toList()
      logger.info("解锁机器人 '$robotId' 的点位：${idsToUnlock.joinToString(", ")}")
      for (siteId in idsToUnlock) {
        if (lockedSiteIdsMap[siteId] == robotId) {
          lockedSiteIdsMap.remove(siteId)
        }
      }
    }
  }

  fun unlockAll() {
    logger.info("解锁所有地图资源")
    lockedSiteIdsMap.clear()
  }
  
  /**
   * 列出锁定的资源
   */
  private fun listLockedSiteIds(robotId: String): Set<String> {
    val lockedSiteIds: MutableSet<String> = HashSet()
    // 停靠的位置
    for (rr in sceneRuntime.listRobots()) {
      if (rr.robotName == robotId) continue
      val pointName = rr.selfReport?.stand?.pointName ?: continue
      
      lockedSiteIds += pointName
      
      // 停靠在互斥区
      for (zone in marsService.listBlockZones()) {
        if (!zone.pointNames.contains(pointName)) continue
        lockedSiteIds.addAll(zone.pointNames)
      }
    }
    
    for ((siteId, rId) in lockedSiteIdsMap) {
      if (rId == robotId) continue
      lockedSiteIds.add(siteId)
    }
    return lockedSiteIds
  }
  
  /**
   * 传入一组 site id，返回机器人能锁定的第一个 site id
   */
  fun tryLockOneSiteBySiteId(robotId: String, siteIds: List<String>): String? {
    for (siteId in siteIds) {
      val rId = getRobotIdByLocation(siteId)
      if (rId == robotId) {
        return siteId
      } else if (rId != null) {
        continue // 被别的机器人占用
      }
      
      val lockedRobot = lockedSiteIdsMap[siteId]
      if (lockedRobot == null || lockedRobot == robotId) return siteId
    }
    return null
  }
  
  // 获取在当前站点的机器人 ID
  private fun getRobotIdByLocation(siteId: String): String? {
    for (rr in sceneRuntime.listRobots()) {
      val stand = rr.selfReport?.stand ?: continue
      if (stand.pointName == siteId) return rr.robotName
    }
    return null
  }
}

enum class ScheduleResultKind {
  NoMove, // 已到最终目标点，不需要移动
  NoPath, // 找不到路径
  Success,
  Interrupted,
}

data class ScheduleResult(
  val kind: ScheduleResultKind,
  val nextSites: List<String> = emptyList(), // 接下来要去的一系列点
)