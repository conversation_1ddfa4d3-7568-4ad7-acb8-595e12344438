package com.seer.trick.fleet.falcon.bp

import com.seer.trick.BzError

import com.seer.trick.falcon.domain.*
import com.seer.trick.fleet.handler.OrderHandler
import com.seer.trick.fleet.order.OrderFaultService
import com.seer.trick.fleet.order.OrderService
import com.seer.trick.fleet.service.SceneService

/**
 * 添加步骤，并等待步骤完成
 */
class AddStepAndWaitCompleteBp : AbstractWaitBp() {

  override fun process() {
    val orderId = mustGetBlockInputParam("orderId") as String

    val sceneId = OrderService.mustGetSceneIdByOrderId(orderId)
    val sr = SceneService.mustGetSceneById(sceneId)
    val or = sr.mustGetOrderById(orderId)

    if (or.order.stepFixed) throw BzError("errAddStepWhenSealed", orderId)

    @Suppress("UNCHECKED_CAST")
    val operationArgs = getBlockInputParam("operationArgs") as Map<String, Any?>?
    val binTask = getBlockInputParam("binTask") as String?
    val operation = getBlockInputParam("operation") as String?
    val location = mustGetBlockInputParam("location") as String
    val withdrawOrderAllowed = getBlockInputParam("withdrawOrderAllowed") as Boolean? ?: false
    val nextStepSameOrder = getBlockInputParamAsBool("nextStepSameOrder")
    // TODO 支持脚本参数 空串问题是否需要在下发前检查
    val rbkArgsMap: MutableMap<String, Any?> = if (!operation.isNullOrBlank()) {
      mutableMapOf("operation" to operation)
    } else {
      emptyMap<String, Any?>().toMutableMap()
    }
    if (!binTask.isNullOrBlank() && !operation.isNullOrBlank()) throw BzError("errAddStepTooManyOp", orderId)
    if (!binTask.isNullOrBlank() && !sr.mapCache.binNames.contains(location)) {
      throw BzError("errAddStepLocationInvalid", orderId)
    }
    if (!binTask.isNullOrBlank()) rbkArgsMap["binTask"] = binTask
    if (!operationArgs.isNullOrEmpty()) rbkArgsMap.putAll(operationArgs)
    // 容器编号
    // val containerId = getBlockInputParam("containerId") as String?
    // val containerTypeName = getBlockInputParam("containerTypeName") as String?
    // val containerDir = getBlockInputParam("containerDir") as Double?

    val stepReq = OrderHandler.CreateStepReq(
      location = location,
      rbkArgs = rbkArgsMap,
      forLoad = getBlockInputParamAsBool("forLoad"),
      forUnload = getBlockInputParamAsBool("forUnload"),
      withdrawOrderAllowed = withdrawOrderAllowed,
      nextStepSameOrder = nextStepSameOrder,
      // containerId = if (!containerId.isNullOrBlank()) containerId else null,
      // containerTypeName = containerTypeName,
      // containerDir = if (containerDir != null) GeoHelper.normalizeRadian(Math.toRadians(containerDir)) else null,
    )
    val stepId = sr.withOrderLock {
      val stepId = getInternalVariable("$orderId-stepId")
      // 故障的步骤一定是当前的步骤？
      val currentStep = or.steps.find { it.id == stepId && it.stepIndex == or.order.currentStepIndex }
      // 若已存在则是故障重试
      if (currentStep != null) {
        OrderFaultService.retryByOrder(sr, orderId)
        stepId as String
      } else {
        val stepIndex = or.steps.size
        val step = stepReq.toStep(orderId, stepIndex)
        OrderService.addSteps(sr, orderId, listOf(step), false)
        step.id
      }
    }
    // 设置内部变量
    getOrSetBlockInternalVariable("$orderId-stepId") { stepId }

    // 等待运单步骤完成
    waitStepComplete(sr, or, stepId)

    val robotName = or.order.actualRobotName
    if (!robotName.isNullOrBlank()) addActualVehicle(robotName)
    setBlockOutputParams(mapOf("stepId" to stepId, "robotName" to robotName))
    // 添加猎鹰任务相关业务对象
    addRelatedObject("TransportStep", stepId, null)
  }

  companion object {
    val def = BlockDef(
      AddStepAndWaitCompleteBp::class.simpleName!!,
      color = "#C7DCA7",
      inputParams = listOf(
        BlockInputParamDef("orderId", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.OrderId)),
        BlockInputParamDef(
          "location",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.BinId, ParamObjectType.SiteId),
        ),
        BlockInputParamDef("binTask", BlockParamType.String, false),
        BlockInputParamDef("operation", BlockParamType.String, false),
        BlockInputParamDef("operationArgs", BlockParamType.JSONObject, false),
        BlockInputParamDef("forLoad", BlockParamType.Boolean, false, defaultValue = false),
        BlockInputParamDef("forUnload", BlockParamType.Boolean, false, defaultValue = false),
        BlockInputParamDef("withdrawOrderAllowed", BlockParamType.Boolean, false, defaultValue = false),
        BlockInputParamDef("nextStepSameOrder", type = BlockParamType.Boolean, false, defaultValue = false),
        // BlockInputParamDef("containerId", BlockParamType.String, false),
        // BlockInputParamDef("containerTypeName", BlockParamType.String, false),
        // BlockInputParamDef("containerDir", BlockParamType.Double, false),
      ),
      outputParams = listOf(
        BlockOutputParamDef("stepId", BlockParamType.String),
        BlockOutputParamDef("robotName", BlockParamType.String, objectTypes = listOf(ParamObjectType.RobotName)),
      ),
    )
  }
}