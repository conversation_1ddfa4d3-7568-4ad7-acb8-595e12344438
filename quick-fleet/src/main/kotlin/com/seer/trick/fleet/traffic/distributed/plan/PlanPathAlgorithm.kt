package com.seer.trick.fleet.traffic.distributed.plan

import com.seer.trick.fleet.traffic.distributed.context.domain.RobotContext
import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper
import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper.RIGHT_ANGLE
import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper.SECTOR_VALUE
import com.seer.trick.fleet.traffic.distributed.helper.PathInfoHelper
import com.seer.trick.fleet.traffic.distributed.map.*
import com.seer.trick.fleet.traffic.distributed.plan.model.*
import java.util.*

abstract class PlanPathAlgorithm(request: PlanRequest) {

  val context: RobotContext
  val start: Point
  val target: Point
  val cantRotatePoint: List<String>
  val mapName: String
  val groupName: String
  private val targetAngle: Int?
  private val startAngle: Int
  private val loadCode: String?
  private val isLoading: Boolean
  private val followUp: Boolean
  private val forbiddenPoint: List<String>
  private val forbiddenLine: List<String>
  private val extraCost: Map<String, Int>
  private val loadingStartHeading: Int
  private val loadingTargetHeading: Int
  private val narrowDir: Int
  private val targetCombinedStop: Boolean

  val defaultCost = 0.0
  val noRotatePoints = mutableListOf<String>()

  init {
    context = request.context
    start = request.start
    target = request.target
    startAngle = request.startAngle
    targetAngle = request.targetAngle
    forbiddenPoint = request.forbiddenPoints
    forbiddenLine = request.forbiddenLine
    extraCost = request.extraCost
    cantRotatePoint = request.cantRotatePoint
    loadingStartHeading = request.loadStartAngle
    loadingTargetHeading = request.loadTargetAngle
    isLoading = request.loadName != null
    loadCode = request.loadName
    followUp = request.robotAndRackNotRotateRelative
    mapName = request.mapName
    groupName = request.context.groupName
    narrowDir = request.narrowDir
    targetCombinedStop = request.targetCombinedStop
  }

  fun execute(): PlanResponse {
    if (start == target) {
      return samePointExecute()
    }

    // 路径要不要改为mm,为整数
    val lines = start.toLines
    val openList = OpenList()
    val closeList = ArrayList<String>()

    if (!start.rotate) {
      noRotatePoints.add(start.pointName)
    }
    // 构建初始
    for (line in lines) {
      // 对每一条路径的约束做处理
      if (!line.robotCanPass(isLoading)) continue
      if (forbiddenLine.contains(line.lineName) || forbiddenPoint.contains(line.end.pointName)) continue
      if (!(
          line.end.type == PointType.COMMON ||
            line.end.type == PointType.ROTATE ||
            line.end.pointName == target.pointName
          )
      ) {
        continue
      }
      val data = processPointData(start, line, startAngle, isLoading, loadingStartHeading, null)

      openList.addOpenList(data)
    }
    while (openList.isNotEmpty()) {
      val pointData = openList.pop()
      closeList.add(pointData.key())
      // 判断是否到达终点
      if (arrayTarget(pointData)) {
        return buildPath(pointData)
      }
      val end = pointData.line.end
      if (!end.rotate && !noRotatePoints.contains(end.pointName)) {
        noRotatePoints.add(end.pointName)
      }

      for (l in end.toLines) {
        if (!l.robotCanPass(isLoading)) continue
        if (forbiddenLine.contains(l.lineName) || forbiddenPoint.contains(l.end.pointName)) continue
        if (l.end.pointName == pointData.point.pointName) continue
        if (!(
            l.end.type == PointType.COMMON ||
              l.end.type == PointType.ROTATE ||
              l.end.pointName == target.pointName
            )
        ) {
          continue
        }
        val data =
          processPointData(end, l, pointData.robotOutHeading, isLoading, pointData.loadingOutHeading, pointData)
        for (d in data) {
          if (!closeList.contains(d.key())) {
            openList.add(d)
          }
        }
      }
    }

    return PlanResponse(
      status = ResStatus.FAILED,
      code = "T00010004",
      args = mutableListOf(context.robotName, start.pointName, target.pointName, noRotatePoints.toString()),
      path = mutableListOf(),
    )
  }

  private fun samePointExecute(): PlanResponse {
    // 表示孤点, 如果为孤点，用自己的 pointName 来表示线段名称，防错处理
    val lineName = if (target.toLines.isEmpty()) {
      target.pointName
    } else {
      target.toLines[0].lineName
    }
    val path = mutableListOf<PathAction>()
    var index: Long = 0
    var type = PathType.STRAIGHT
    if (targetAngle != null && !AngleHelper.sameAngleInFiveDegree(startAngle, targetAngle)) {
      type = PathType.ROTATE
    }
    if (!AngleHelper.sameAngleInFiveDegree(loadingTargetHeading, AngleHelper.ERROR_ANGLE) &&
      !AngleHelper.sameAngleInFiveDegree(loadingStartHeading, loadingTargetHeading)
    ) {
      if (target.type != PointType.ROTATE) {
        return PlanResponse(
          status = ResStatus.FAILED,
          code = "T00010004",
          args = mutableListOf(context.robotName, start.pointName, target.pointName, noRotatePoints.toString()),
          path = mutableListOf(),
        )
      }
      type = PathType.TURNING
    }
    if (type != PathType.STRAIGHT) {
      type = if (targetCombinedStop) {
        PathType.STOP
      } else if (type == PathType.ROTATE && !AngleHelper.needRotate(startAngle, targetAngle!!)) {
        PathType.SECTOR
      } else {
        type
      }
      path.add(
        PathAction(
          robotName = context.robotName,
          index = index++,
          type = type,
          mapName = mapName,
          groupName = groupName,
          targetX = target.x,
          targetY = target.y,
          start = target,
          target = target,
          lineName = lineName,
          robotInHeading = startAngle,
          robotOutHeading = targetAngle ?: startAngle,
          containerName = loadCode,
          containerInHeading = loadingStartHeading,
          containerOutHeading = if (type == PathType.TURNING) loadingTargetHeading else loadingStartHeading,
        ),
      )
    }
    if (path.isEmpty() || !targetCombinedStop) {
      path.add(
        PathAction(
          robotName = context.robotName,
          index = index,
          type = PathType.STOP,
          mapName = mapName,
          groupName = groupName,
          targetX = target.x,
          targetY = target.y,
          start = target,
          target = target,
          lineName = lineName,
          robotInHeading = targetAngle ?: startAngle,
          robotOutHeading = targetAngle ?: startAngle,
          containerName = loadCode,
          containerInHeading = if (type == PathType.TURNING) loadingTargetHeading else loadingStartHeading,
          containerOutHeading = if (type == PathType.TURNING) loadingTargetHeading else loadingStartHeading,
        ),
      )
    }
    return PlanResponse(
      status = ResStatus.SUCCESS,
      code = "*********",
      args = mutableListOf(context.robotName),
      path = path,
    )
  }

  private fun arrayTarget(pointData: PointData): Boolean {
    // 检测是否达到终点
    if (pointData.line.end.pointName == target.pointName) {
      if (!isLoading ||
        AngleHelper.ERROR_ANGLE == loadingTargetHeading ||
        AngleHelper.sameAngleInFiveDegree(
          pointData.loadingOutHeading,
          loadingTargetHeading,
        )
      ) {
        return true
      }
    }
    if (pointData.point.pointName == target.pointName) {
      if (!isLoading ||
        AngleHelper.ERROR_ANGLE == loadingTargetHeading ||
        AngleHelper.sameAngleInFiveDegree(
          pointData.loadingHeading,
          loadingTargetHeading,
        )
      ) {
        return true
      }
    }
    return false
  }

  private fun buildPath(pointData: PointData): PlanResponse {
    val list = LinkedList<PointData>()
    val cost = pointData.f()
    var pd: PointData? = pointData
    while (pd != null) {
      list.addFirst(pd)
      pd = pd.parent
    }
    val path = LinkedList<PathAction>()
    var lastPointData: PointData? = null
    var index: Long = 0
    // 开始构建路径
    for (pointPath in list) {
      if (lastPointData != null) {
        // 判断是否需要旋转
        var type = PathType.STRAIGHT
        if (!AngleHelper.sameAngleInFiveDegree(lastPointData.robotOutHeading, pointPath.robotHeading)) {
          if (AngleHelper.needRotate(lastPointData.robotOutHeading, pointPath.robotHeading)) {
            type = PathType.ROTATE
          } else {
            type = PathType.SECTOR
          }
        }
        if (isLoading &&
          !AngleHelper.sameAngleInFiveDegree(lastPointData.loadingOutHeading, pointPath.loadingHeading)
        ) {
          // 需要货架换向
          type = PathType.TURNING
        }
        if (type != PathType.STRAIGHT) {
          val pathAction = PathAction(
            robotName = context.robotName,
            index = index,
            type = type,
            mapName = mapName,
            groupName = groupName,
            targetX = pointPath.point.x,
            targetY = pointPath.point.y,
            start = pointPath.point,
            target = pointPath.point,
            lineName = lastPointData.line.lineName,
            robotInHeading = lastPointData.robotOutHeading,
            robotOutHeading = pointPath.robotHeading, loadCode,
            containerInHeading = lastPointData.loadingOutHeading,
            containerOutHeading = pointPath.loadingHeading,
          )
          path.add(pathAction)
          index++
        }
      } else {
        // 和初始角度做比较
        var type = PathType.STRAIGHT
        if (!AngleHelper.sameAngleInFiveDegree(startAngle, pointPath.robotHeading)) {
          if (AngleHelper.needRotate(startAngle, pointPath.robotHeading)) {
            type = PathType.ROTATE
          } else {
            type = PathType.SECTOR
          }
        }
        if (isLoading && !AngleHelper.sameAngleInFiveDegree(loadingStartHeading, pointPath.loadingHeading)) {
          // 需要货架换向
          type = PathType.TURNING
        }
        if (type != PathType.STRAIGHT) {
          val pathAction = PathAction(
            robotName = context.robotName,
            index = index,
            type = type,
            mapName = mapName,
            groupName = groupName,
            targetX = start.x,
            targetY = start.y,
            start = start,
            target = start,
            lineName = pointPath.line.lineName,
            robotInHeading = startAngle,
            robotOutHeading = pointPath.robotHeading,
            containerName = loadCode,
            containerInHeading = loadingStartHeading,
            containerOutHeading = pointPath.loadingHeading,
          )
          path.add(pathAction)
          index++
        }
      }
      if (pointPath.point == target) {
        break
      }
      // 构建 pathAction
      val pathAction = PathAction(
        robotName = context.robotName,
        index = index,
        type = PathInfoHelper.typeConvert(pointPath.line.type),
        mapName = mapName,
        groupName = groupName,
        targetX = pointPath.line.end.x,
        targetY = pointPath.line.end.y,
        start = pointPath.point,
        target = pointPath.line.end,
        lineName = pointPath.line.lineName,
        robotInHeading = pointPath.robotHeading,
        robotOutHeading = pointPath.robotOutHeading,
        containerName = loadCode,
        containerInHeading = pointPath.loadingHeading,
        containerOutHeading = pointPath.loadingOutHeading,
      )
      path.add(pathAction)
      index++
      lastPointData = pointPath
    }

    if (targetCombinedStop) {
      val action = path.removeLast()
      if (targetAngle != null && !AngleHelper.sameAngleInFiveDegree(action.robotOutHeading, targetAngle)) {
        path.add(action.copy(type = PathType.STOP, robotOutHeading = targetAngle))
      } else {
        path.add(action.copy(type = PathType.STOP))
      }
    } else { // 构建一个停下来的
      val action = path.last()
      if (targetAngle != null && !AngleHelper.sameAngleInFiveDegree(action.robotOutHeading, targetAngle)) {
        path.add(
          PathAction(
            robotName = context.robotName,
            index = index++,
            type = if (AngleHelper.needRotate(action.robotOutHeading, targetAngle)) {
              PathType.ROTATE
            } else {
              PathType.SECTOR
            },
            mapName = mapName,
            groupName = groupName,
            targetX = target.x,
            targetY = target.y,
            start = target,
            target = target,
            lineName = action.lineName,
            robotInHeading = action.robotOutHeading,
            robotOutHeading = targetAngle,
            containerName = loadCode,
            containerInHeading = action.containerOutHeading,
            containerOutHeading = action.containerOutHeading,
          ),
        )
      }
      val pathAction = PathAction(
        robotName = context.robotName,
        index = index,
        type = PathType.STOP,
        mapName = mapName,
        groupName = groupName,
        targetX = target.x,
        targetY = target.y,
        start = target,
        target = target,
        lineName = action.lineName,
        robotInHeading = targetAngle ?: action.robotOutHeading,
        robotOutHeading = targetAngle ?: action.robotOutHeading,
        containerName = loadCode,
        containerInHeading = action.containerOutHeading,
        containerOutHeading = action.containerOutHeading,
      )
      path.add(pathAction)
    }
    return PlanResponse(
      status = ResStatus.SUCCESS,
      code = "*********",
      args = mutableListOf(context.robotName),
      path = path,
      cost = cost,
    )
  }

  private fun processPointData(
    start: Point,
    line: Line,
    robotHeading: Int,
    loading: Boolean,
    loadingHeading: Int,
    pointData: PointData?,
  ): List<PointData> {
    val robotHeadings = findRobotHeadings(start, line, robotHeading)
    val loadingHeadings =
      if (loading) findLoadingHeadings(loadingHeading, robotHeading, robotHeadings, line) else ArrayList()
    val pointDatas = ArrayList<PointData>()
    // 判断机器人是否带载
    if (robotHeadings.isEmpty()) return pointDatas
    for (rHeading in robotHeadings) {
      val pg = pointData?.g ?: 0.0
      val pt = pointData?.t ?: 0.0
      val pr = pointData?.r ?: 0.0
      val ps = pointData?.s ?: 0.0
      val g = line.length + pg
      val h = line.end.distance(target)
      var t = pt + costT(rHeading, robotHeading)
      val r = pr + costR(rHeading, line)
      val s = ps + costS(pointData, line)
      val robotOutAngle = AngleHelper.processAngle(rHeading + line.angleDeviation())
      if (!loading || loadingHeadings.isEmpty()) {
        val pd = PointData(start, line, g, h, t, r, s, rHeading, loadingHeading, pointData)
        pd.robotOutHeading = robotOutAngle
        pointDatas.add(pd)
        continue
      }
      for (lHeading in loadingHeadings) {
        // 判断货架是否会进行换向 or 随动时的情况
        if (followUp) {
          if (AngleHelper.sameAngleInFiveDegree(
              lHeading,
              AngleHelper.processAngle(loadingHeading - robotHeading + rHeading),
            )
          ) {
            t += if (lHeading == loadingHeading) 0.0 else 1.5
            val pd = PointData(start, line, g, h, t, r, s, rHeading, lHeading, pointData)
            pd.loadingOutHeading = AngleHelper.processAngle(lHeading + line.angleDeviation())
            pd.robotOutHeading = robotOutAngle
            pointDatas.add(pd)
          }
          continue
        }
        val lc = containerRotateCost(lHeading, loadingHeading)
        val pd = PointData(start, line, g, h, t + lc, r, s, rHeading, lHeading, pointData)
        pd.loadingOutHeading = AngleHelper.processAngle(lHeading + line.angleDeviation())
        pd.robotOutHeading = robotOutAngle
        pointDatas.add(pd)
      }
    }
    return pointDatas
  }

  private fun containerRotateCost(lHeading: Int, loadingHeading: Int): Double {
    if (AngleHelper.sameAngleInFiveDegree(lHeading, loadingHeading)) return 0.0
    var tn = AngleHelper.vectorAngle(lHeading, loadingHeading) / RIGHT_ANGLE
    if (AngleHelper.vectorAngle(lHeading, loadingHeading) % RIGHT_ANGLE > SECTOR_VALUE) {
      tn += 1
    }
    1.5 * tn
    return 1.5 * tn
  }

  // 停车代价
  abstract fun costS(pointData: PointData?, line: Line): Double

  // 倒车、横移代价
  abstract fun costR(rHeading: Int, line: Line): Double

  // 旋转代价
  abstract fun costT(rHeading: Int, robotHeading: Int): Double

  fun processStop(pointData: PointData?, line: Line): Boolean {
    if (pointData == null) return true
    val line0 = pointData.line
    return AngleHelper.sameAngleInFiveDegree(line0.outDir, line.enterDir)
  }

  abstract fun findRobotHeadings(start: Point, line: Line, robotHeading: Int): List<Int>

  private fun findLoadingHeadings(
    loadingHeading: Int,
    robotHeading: Int,
    robotHeadings: List<Int>,
    line: Line,
  ): List<Int> {
    val loadingHeadings = ArrayList<Int>()
    // 如果 车头方向没有，则就不会有货架的方向
    if (robotHeadings.isEmpty()) return loadingHeadings
    // 随动情况
    if (followUp) {
      val relateAngle = loadingHeading - robotHeading
      for (heading in robotHeadings) {
        loadingHeadings.add(AngleHelper.processAngle(heading + relateAngle))
      }
      return loadingHeadings
    }

    if (line.start.type != PointType.ROTATE) {
      return listOf(loadingHeading)
    }
    if (line.containerDir != null) {
      loadingHeadings.add(line.containerDir)
      return loadingHeadings
    }
    if (line.narrowPath) {
      loadingHeadings.add(AngleHelper.processAngle(line.enterDir + narrowDir))
      loadingHeadings.add(AngleHelper.processAngle(line.enterDir + AngleHelper.DOWN_ANGLE + narrowDir))
      return loadingHeadings
    }
    loadingHeadings.add(line.enterDir)
    loadingHeadings.add(AngleHelper.processAngle(line.enterDir + RIGHT_ANGLE))
    loadingHeadings.add(AngleHelper.processAngle(line.enterDir + AngleHelper.DOWN_ANGLE))
    loadingHeadings.add(AngleHelper.processAngle(line.enterDir - RIGHT_ANGLE))
    if (!AngleHelper.sameFixAngle(loadingTargetHeading, line.enterDir)) {
      loadingHeadings.add(loadingTargetHeading)
    }
    return loadingHeadings
  }
}