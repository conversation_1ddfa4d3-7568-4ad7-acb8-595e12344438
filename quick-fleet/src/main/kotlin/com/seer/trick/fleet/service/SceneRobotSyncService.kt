package com.seer.trick.fleet.service

import com.seer.trick.BzError
import com.seer.trick.I18N
import com.seer.trick.fleet.FleetConcurrentCenter.syncSceneToRobotsExecutor
import com.seer.trick.fleet.FleetLogger
import com.seer.trick.fleet.domain.RobotMapNameMd5
import com.seer.trick.fleet.service.SceneRobotSyncService.PushMapRobotContext.Companion.PUSH_ROBOT_MAP_RESULT_FAIL
import com.seer.trick.fleet.service.SceneRobotSyncService.PushMapRobotContext.Companion.PUSH_ROBOT_MAP_RESULT_OK
import com.seer.trick.fleet.service.SceneRobotSyncService.PushMapRobotContext.Companion.PUSH_ROBOT_MAP_RESULT_PUSHING
import com.seer.trick.helper.DateHelper
import com.seer.trick.helper.IdHelper
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.*

/**
 * 负责服务器场景与机器人的同步。
 * 一次最多一个推送。当前有未完成的推送，还要继续推送，报错。
 */
object SceneRobotSyncService {

  private val logger = LoggerFactory.getLogger(javaClass)

  private val pushMapsCtxMap: MutableMap<String, PushMapsContext> = HashMap()

  private val pushToMapFutures: MutableList<Future<*>> = CopyOnWriteArrayList()

  /**
   * 每个场景一个，在锁里访问不需要本身线程安全。
   */
  private val checkMapsCtxMap: MutableMap<String, RobotsMapsCheckContext> = HashMap()

  private val checkFutures: MutableList<Future<*>> = CopyOnWriteArrayList()

  /**
   * 异步推送任务，一个场景一个任务
   */
  private val asyncPushTaskMap: MutableMap<String, AsyncPushTask> = ConcurrentHashMap()

  /**
   * 异步任务执行器
   */
  private val asyncTaskExecutor: ExecutorService = Executors.newSingleThreadExecutor()

  @Volatile
  private var asyncTaskFuture: Future<*>? = null

  fun init() {
    logger.info("Initialize the task of asynchronously pushing map")
    asyncTaskFuture = asyncTaskExecutor.submit {
      while (!Thread.interrupted()) {
        for (sceneId in asyncPushTaskMap.keys.toList()) {
          val sr = SceneService.mustGetSceneById(sceneId)
          val task = asyncPushTaskMap[sceneId] ?: continue
          if (task.status != PUSH_ROBOT_MAP_RESULT_PUSHING) continue
          // 未开始的推送任务
          val waitPushContexts = task.contexts.filter { it.doneOn == null }
          // 更新异步任务状态，不管是否有失败，都是任务完成
          if (waitPushContexts.isEmpty()) {
            task.status = PUSH_ROBOT_MAP_RESULT_OK
            task.doneOn = Date()
            continue
          }
          for (rc in waitPushContexts) {
            if (rc.status != PUSH_ROBOT_MAP_RESULT_PUSHING) continue
            val rr = sr.robots[rc.robotName] ?: continue
            // 选择合适的时期再进行推送
            if (!checkRobotStatus(rr)) continue
            task.robotPushTaskFutures[rc.robotName] = syncSceneToRobotsExecutor.submit {
              try {
                RobotService.pushMapToRobot(rr)
                rc.status = PUSH_ROBOT_MAP_RESULT_OK
              } catch (e: Exception) {
                logger.error("Failed to push maps to robot ${rc.robotName}", e)
                rc.status = PUSH_ROBOT_MAP_RESULT_FAIL
              } finally {
                // 失败或者成功，推送都已经完成了
                rr.pushingMap = false
                rc.doneOn = Date()
                rc.cost = System.currentTimeMillis() - rc.startedOn.time
              }
            }
          }
        }
        Thread.sleep(1000)
      }
    }
  }

  fun dispose() {
    logger.info("Dispose the task of asynchronously pushing map")
    asyncTaskFuture?.cancel(true)
  }

  /**
   * 一个场景一次只能有一个推送。如果上一个推送未完成，报错！
   */
  @Synchronized
  fun pushToRobots(sceneId: String, robotNames: List<String>) {
    val sr = SceneService.mustGetSceneById(sceneId)

    var pushCtx = pushMapsCtxMap[sceneId]
    if (pushCtx != null && !pushCtx.done) throw BzError("errSceneMapsPushing")

    logger.info("Push maps to robots in scene $sceneId: $robotNames")

    pushCtx = PushMapsContext()
    pushMapsCtxMap[sceneId] = pushCtx

    pushToMapFutures.clear()

    for (robotName in robotNames) {
      val rr = sr.robots[robotName] ?: continue

      val robotCtx = PushMapRobotContext(robotName)
      pushCtx.robots[robotName] = robotCtx

      // 异步、并行推送给机器人
      val future = syncSceneToRobotsExecutor.submit {
        try {
          RobotService.pushMapToRobot(rr)
          robotCtx.status = PUSH_ROBOT_MAP_RESULT_OK
        } catch (e: Exception) {
          logger.error("Failed to push maps to robot $robotName", e)
          robotCtx.status = PUSH_ROBOT_MAP_RESULT_FAIL
        }
        robotCtx.doneOn = Date()
        robotCtx.cost = System.currentTimeMillis() - robotCtx.startedOn.time
      }
      pushToMapFutures += future
    }

    syncSceneToRobotsExecutor.submit {
      for (f in pushToMapFutures) {
        try {
          f.get()
        } catch (e: Exception) {
          //
        }
      }
      logger.info("Push maps to robots done in scene ${pushCtx.txId}")

      pushCtx.done = true
      pushCtx.doneOn = Date()
      pushToMapFutures.clear()
    }
  }

  fun queryPushMapsProgress(sceneId: String): PushMapsContext? = pushMapsCtxMap[sceneId]

  fun queryCheckMapsProgress(sceneId: String): RobotsMapsCheckContext? = checkMapsCtxMap[sceneId]

  /**
   * 检查场景地图和机器人地图的匹配情况
   */
  @Synchronized
  fun checkMaps(sceneId: String) {
    val sr = SceneService.mustGetSceneById(sceneId)

    var checkCtx = checkMapsCtxMap[sceneId]
    if (checkCtx != null && !checkCtx.done) return // 正在检查，不要重复检查

    checkCtx = RobotsMapsCheckContext()
    checkMapsCtxMap[sceneId] = checkCtx

    // group id -> map name -> md5
    val maps = mutableMapOf<Int, MutableMap<String, String>>()
    for (area in sr.areas) {
      if (area.disabled) continue
      for ((gid, g) in area.groupsMap) {
        maps.getOrPut(gid) { HashMap() }[g.mapName] = g.mapMd5
      }
    }

    checkFutures.clear()

    for (rr in sr.robots.values) {
      val groupMaps = maps[rr.config.groupId]

      if (RobotService.isOnline(rr)) {
        // 在线
        val robotCheckCtx = RobotMapsCheckResult(
          rr.robotName,
          rr.config.groupId,
          online = true,
          // RobotService.isMaster(rr) 判断控制权的结果不严谨，但是又还有其他地方在用，目前先不改动 isMaster()
          // 只需要判断此机器人控制权的所有者即可。
          master = rr.selfReport?.main?.controller == RobotService.NICKNAME,
          allMatch = false,
        )

        checkCtx.results[rr.robotName] = robotCheckCtx

        val future = syncSceneToRobotsExecutor.submit {
          try {
            val robotMaps = RobotService.listRobotMaps(rr)
            val robotMapsStatuses = mutableMapOf<String, RobotMapStatus>()

            var allMatch = true
            if (groupMaps != null) {
              for ((mapName, md5) in groupMaps) {
                val robotMap = robotMaps.find { it.mapName == mapName }
                if (robotMap == null || robotMap.mapMd5 != md5) allMatch = false
                robotMapsStatuses[mapName] = RobotMapStatus(mapName, robotMap != null, md5, robotMap?.mapMd5)
              }
            } else {
              allMatch = false
            }

            robotCheckCtx.allMatch = allMatch
            robotCheckCtx.checking = false
            robotCheckCtx.maps = robotMapsStatuses
          } catch (e: Exception) {
            robotCheckCtx.checking = false
            robotCheckCtx.error = true
          }
        }

        checkFutures += future
      } else {
        // 不在线
        checkCtx.results[rr.robotName] = RobotMapsCheckResult(
          rr.robotName,
          rr.config.groupId,
          online = false,
          master = false,
          allMatch = false,
          checking = false,
        )
      }
    }

    // 等完成
    syncSceneToRobotsExecutor.submit {
      for (f in checkFutures) {
        try {
          f.get()
        } catch (e: Exception) {
          //
        }
      }
      logger.debug("All robot maps checked for scene $sceneId")
      checkCtx.done = true
      checkCtx.doneOn = Date()

      checkFutures.clear()
    }
  }

  /**
   * 拉取机器人地图到场景
   */
  fun pullMapsFromRobot(
    sr: SceneRuntime,
    robotName: String,
    newMapByArea: Map<Int, RobotMapNameMd5>,
    check: Boolean,
  ): Pair<Boolean, List<String>> {
    FleetLogger.info(
      module = "Scene",
      subject = "PullMapFromRobot",
      sr = sr,
      robotName = robotName,
      msg = mapOf("newMap" to newMapByArea),
    )
    val rr = sr.mustGetRobot(robotName)
    // 遍历每个要修改的区域
    for ((areaId, mm) in newMapByArea) {
      val area = sr.areas.find { a -> !a.disabled && a.id == areaId } ?: continue
      // 先拉取地图
      val (success, record) = RobotService.loadSaveRobotMapFile(rr, mm.mapName, check)
      if (!success) return Pair(false, emptyList())

      // 更新到这个区域
      val newArea = area.copy(groupsMap = area.groupsMap + (rr.config.groupId to record!!))
      val checkContext = CheckAreaContext(mutableListOf())
      SceneAreaService.updateAreaConfig(sr, newArea, checkContext)
      if (checkContext.errors.isNotEmpty()) return Pair(true, checkContext.errors)
    }
    return Pair(true, emptyList())
  }

  /**
   * 一次推送的上下文
   */
  data class PushMapsContext(
    val txId: String = IdHelper.oidStr(),
    val robots: MutableMap<String, PushMapRobotContext> = HashMap(),
    val startedOn: Date = Date(),
    @Volatile
    var done: Boolean = false,
    @Volatile
    var doneOn: Date? = null,
  )

  /**
   * 一个机器人的推送记录
   */
  data class PushMapRobotContext(
    val robotName: String,
    val startedOn: Date = Date(),
    @Volatile
    var status: Int = PUSH_ROBOT_MAP_RESULT_PUSHING,
    @Volatile
    var doneOn: Date? = null,
    @Volatile
    var cost: Long? = null,
  ) {

    companion object {
      const val PUSH_ROBOT_MAP_RESULT_PUSHING = 10
      const val PUSH_ROBOT_MAP_RESULT_OK = 100
      const val PUSH_ROBOT_MAP_RESULT_FAIL = 200
    }
  }

  /**
   * 一次检查场景中地图和机器人地图的匹配关系
   */
  data class RobotsMapsCheckContext(
    val txId: String = IdHelper.oidStr(),
    val startedOn: Date = Date(),
    // 检查完后放进来
    val results: MutableMap<String, RobotMapsCheckResult> = ConcurrentHashMap(),
    @Volatile
    var done: Boolean = false,
    @Volatile
    var doneOn: Date? = null,
  )

  /**
   * 检查机器人地图
   */
  data class RobotMapsCheckResult(
    val robotName: String,
    val groupId: Int,
    val online: Boolean,
    val master: Boolean,
    @Volatile
    var allMatch: Boolean = false, // 所有地图都匹配
    @Volatile
    var checking: Boolean = true,
    @Volatile
    var error: Boolean = false, // 检查过程中出错
    @Volatile
    var maps: Map<String, RobotMapStatus> = emptyMap(),
  )

  /**
   * 检查机器人地图结果，单个地图
   */
  data class RobotMapStatus(
    val mapName: String,
    // 在机器人上有没有
    val robotFound: Boolean,
    val m4Md5: String,
    val robotMd5: String?,
  )

  /**
   * 取消某场景的异步任务
   */
  fun cancelPushTaskAsync(sceneId: String) {
    logger.info("Cancels an asynchronous task for scene $sceneId")
    val task = asyncPushTaskMap.remove(sceneId) ?: return
    for (future in task.robotPushTaskFutures.values) {
      future.cancel(true)
    }
  }

  /**
   * 查询异步任务
   */
  fun queryAsyncPushTask(sceneId: String): AsyncPushTask? = asyncPushTaskMap[sceneId]

  /**
   * 异步推送地图给机器人
   */
  fun pushToRobotsAsync(sceneId: String, robotNames: List<String>) {
    // 每个场景里存在一个长时间的异步任务，找机器人合适的时期进行推送，该任务只可取消、有且只有一个任务。点击推送时生成该异步任务，不持久化
    logger.info("Async push maps to robots in scene $sceneId: $robotNames")
    // 暂时一个场景只允许一个异步任务
    val oldPushTask = asyncPushTaskMap[sceneId]
    if (oldPushTask != null && oldPushTask.status == PUSH_ROBOT_MAP_RESULT_PUSHING) throw BzError("errSceneMapsPushing")
    val asyncTask = AsyncPushTask()
    for (robotName in robotNames) {
      val robotCtx = PushMapRobotContext(robotName)
      asyncTask.contexts.add(robotCtx)
    }
    asyncPushTaskMap[sceneId] = asyncTask
  }

  data class AsyncPushTask(
    val robotPushTaskFutures: MutableMap<String, Future<*>> = ConcurrentHashMap(), // 机器人名称 -> 任务
    val contexts: MutableList<PushMapRobotContext> = CopyOnWriteArrayList(), // 所有机器人的上下文
    val startedOn: Date = Date(),
    @Volatile
    var doneOn: Date? = null,
    @Volatile
    var status: Int = PUSH_ROBOT_MAP_RESULT_PUSHING,
  ) {

    fun ok(): Boolean = status == PUSH_ROBOT_MAP_RESULT_OK

    fun desc(): String = if (status == PUSH_ROBOT_MAP_RESULT_PUSHING) {
      // 推送中，提示：系统当前正在后台进行地图推送。开始于 {0}。预计推送 {1} 个机器人：{2}。还有 {3} 个正在推送中：{4}。
      val pushingRobots = contexts.filter { it.status == PUSH_ROBOT_MAP_RESULT_PUSHING }.map { it.robotName }
      I18N.lo(
        "asyncPushTaskPushingMsg",
        listOf(
          DateHelper.formatDate(startedOn, "yyyy-MM-dd HH:mm:ss"),
          contexts.size,
          if (contexts.isNotEmpty()) I18N.lo("colonMsg", listOf(contexts.joinToString(",") { it.robotName })) else "",
          pushingRobots.size,
          if (pushingRobots.isNotEmpty()) I18N.lo("colonMsg", listOf(pushingRobots.joinToString(","))) else "",
        ),
      )
    } else {
      // 已推送完成，提示：系统已完成后台地图推送。开始于 {0}。完成于 {1}。共推送 {2} 个机器人：{3}。成功 {4} 个：{5}。失败 {6} 个：{7}。
      val (successContexts, failContexts) = contexts.partition { it.status == PUSH_ROBOT_MAP_RESULT_OK }
      val successRobots = successContexts.map { it.robotName }
      val failRobots = failContexts.map { it.robotName }
      I18N.lo(
        "asyncPushTaskOkMsg",
        listOf(
          DateHelper.formatDate(startedOn, "yyyy-MM-dd HH:mm:ss"),
          DateHelper.formatDate(doneOn, "yyyy-MM-dd HH:mm:ss"),
          contexts.size,
          if (contexts.isNotEmpty()) I18N.lo("colonMsg", listOf(contexts.joinToString(",") { it.robotName })) else "",
          successRobots.size,
          if (successRobots.isNotEmpty()) I18N.lo("colonMsg", listOf(successRobots.joinToString(","))) else "",
          failRobots.size,
          if (failRobots.isNotEmpty()) I18N.lo("colonMsg", listOf(failRobots.joinToString(","))) else "",
        ),
      )
    }
  }

  /**
   * 检查机器人当前状态是否合适推送
   */
  private fun checkRobotStatus(rr: RobotRuntime): Boolean {
    // 推送中则不再推送
    if (rr.pushingMap) return false
    // 地图不一致目前会将 moveActions 清空，TODO 后续可能会做更精细的判断
    if (rr.moveActions.isEmpty()) {
      rr.pushingMap = true
      return true
    }
    return false
  }

  fun removeAsyncPushTask(sceneId: String) = asyncPushTaskMap.remove(sceneId)
}