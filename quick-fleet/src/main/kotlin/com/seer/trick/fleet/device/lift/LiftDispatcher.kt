package com.seer.trick.fleet.device.lift

import com.seer.trick.fleet.service.MapService
import com.seer.trick.fleet.service.RobotRuntime

/**
 * 电梯的调度。
 * TODO 支持需要多次乘梯的
 */
object LiftDispatcher {

  /**
   * 根据起始结束区域，选择合适的电梯
   */
  fun findBestLift(
    rr: RobotRuntime,
    fromAreaId: Int,
    toAreaId: Int,
    fromPointName: String,
    toPointName: String,
  ): SelectedLift? {
    val sr = rr.sr

    // 机器人所有 SM 点
    val smPointNames = sr.mapCache.listSwitchMapPointsByGroup(rr.config.groupId).map { it.point.name }

    val selectedLifts = sr.lifts.values.filter { lift ->
      lift.online && !lift.fault && lift.autoMode && !lift.config.disabled
    }.map { lift ->
      val fromFloor = lift.config.floors.find { floor -> floor.areaId == fromAreaId } ?: return@map null
      val toFloor = lift.config.floors.find { floor -> floor.areaId == toAreaId } ?: return@map null

      // 机器人地图上有没有这个 SM 点
      if (fromFloor.switchMapPoints.none { smp -> smp.pointName in smPointNames }) return@map null
      if (toFloor.switchMapPoints.none { smp -> smp.pointName in smPointNames }) return@map null

      SelectedLift(lift, fromFloor, toFloor, "")
    }

    // TODO 先随机选一个
    val selectedLift = selectedLifts.randomOrNull() ?: return null

    // 选择合适的 SM 点

    // 起始和结束楼层 SM 交集
    val allSmPointNames = selectedLift.fromFloor.switchMapPoints.map { it.pointName }.toSet()
      .intersect(selectedLift.toFloor.switchMapPoints.map { it.pointName }.toSet())

    for (smp in allSmPointNames) {
      var sp = MapService.getShortestPathOfArea(rr, selectedLift.fromFloor.areaId, fromPointName, smp)
      if (!sp.found) continue
      sp = MapService.getShortestPathOfArea(rr, selectedLift.toFloor.areaId, smp, toPointName)
      if (!sp.found) continue

      return selectedLift.copy(smPointName = smp)
    }
    return null
  }

  /**
   * 确保电梯一次只被一个机器人使用
   */
  fun <T> use(lr: LiftRuntime, rr: RobotRuntime, work: () -> T): T = synchronized(lr) {
    try {
      // 正在使用电梯的机器人
      lr.usingRobotName = rr.robotName
      lr.usingStatus = null

      rr.usingLiftId = lr.config.id

      val r = work()

      // 用完了
      lr.usingRobotName = null
      lr.usingStatus = null

      rr.usingLiftId = null

      r
    } finally {
      // 目前 usingStatus 三个状态，都不能关门，可能危险
      if (lr.usingStatus == null) {
        // TODO 读门的状态，没关再关
        lr.adapter.closeDoor(null, "Ensure closing door after using")
      }
    }
  }
}

/**
 * 选中的电梯和根据起始结束区域选中的楼层
 */
data class SelectedLift(
  val lift: LiftRuntime,
  val fromFloor: LiftFloorConfig,
  val toFloor: LiftFloorConfig,
  val smPointName: String,
)