package com.seer.trick.fleet.order

import com.seer.trick.fleet.FleetLogger
import com.seer.trick.fleet.domain.OrderStatus
import com.seer.trick.fleet.domain.RobotAutoOrderType
import com.seer.trick.fleet.domain.RobotBin
import com.seer.trick.fleet.domain.RobotBinStatus
import com.seer.trick.fleet.order.OrderRuntime.Companion.timeCostOrZero
import com.seer.trick.fleet.order.OrderService.countLoadDuration
import com.seer.trick.fleet.order.OrderService.countUnloadDuration
import com.seer.trick.fleet.order.OrderService.updateAndPersistOrder
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.RobotService
import com.seer.trick.fleet.service.SceneRuntime
import org.slf4j.LoggerFactory
import java.util.*

/**
 * 处理运单的完成。单独放在一个类里解决 OrderService 过大的问题。
 */
object OrderDoneService {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 完成运单。
   * 目前仅在派单开头掉，判断运单完成，则执行此方法。步骤完成后不要自己调用此方法。
   * rr 为空：尚未执行
   */
  fun markOrderDone(sr: SceneRuntime, or: OrderRuntime, rr: RobotRuntime?) = sr.withOrderLock {
    // 处理机器人完成
    if (rr != null) processRobotDone(or, rr)

    sr.orders.remove(or.id)

    // 计算耗时，此时运单是正常结束的，不用考虑计算 step 的耗时。
    val doneOn = Date()
    val newOrder = or.order.copy(
      status = OrderStatus.Done,
      doneOn = doneOn,
      processingTime = timeCostOrZero(doneOn, or.order.createdOn),
      executingTime = timeCostOrZero(doneOn, or.order.robotAllocatedOn),
      loadDuration = countLoadDuration(or),
      unloadDuration = countUnloadDuration(or),
    )
    updateAndPersistOrder(or, newOrder, "Mark order done")

    FleetLogger.info(
      module = "Order",
      subject = "OrderDone",
      sr = sr,
      robotName = rr?.robotName,
      msg = mapOf("orderId" to or.id),
    )
  }

  /**
   * 运单完成后，处理机器人完成
   */
  private fun processRobotDone(or: OrderRuntime, rr: RobotRuntime) {
    // 更新预测的机器人位置，避免机器人上报位置延迟或者断联导致的异常
    RobotService.updateExpectedOccupiedPointByOrderCurrent(rr, or)

    // 先移除机器人自己的任务列表
    rr.orders.remove(or.id)

    if (rr.autoOrder?.orderId == or.id) {
      // 如果是充电运单，记录机器人到达充电点的时间
      if (rr.autoOrder?.type == RobotAutoOrderType.Charging) {
        rr.chargingOrderDoneOn = Date()
        FleetLogger.info(
          module = "Order",
          subject = "StartCharging",
          sr = rr.sr,
          robotName = rr.robotName,
          msg = mapOf("orderId" to or.id),
        )
      }

      // 移除自动单
      rr.autoOrder = null
    }

    // 如果到了最后了，库位没用，取消占用
    val binIndex = rr.bins.indexOfFirst { it.orderId == or.id }
    if (binIndex >= 0) {
      // logger.info("Transport order is done, empty robot bin [$binIndex]. Order=${or.orderId}. Robot=$rr.")
      val oldBin = rr.bins[binIndex]
      if (oldBin.status == RobotBinStatus.Reserved) {
        rr.bins[binIndex] = RobotBin(binIndex, status = RobotBinStatus.Empty, orderId = null)
      }
    }

    if (rr.selectSameOrderId == or.id) {
      logger.info("Clear robot $rr selectSameOrderId=${rr.selectSameOrderId} after order done")
      rr.selectSameOrderId = null
    }

    RobotService.persistRobotRuntime(rr)
  }
}