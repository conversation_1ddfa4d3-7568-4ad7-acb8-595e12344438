package com.seer.trick.fleet.traffic.distributed.plan

import com.seer.trick.fleet.domain.MoveDirection
import com.seer.trick.fleet.traffic.distributed.context.domain.ChassisType
import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper
import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper.RIGHT_ANGLE
import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper.SECTOR_VALUE
import com.seer.trick.fleet.traffic.distributed.map.Line
import com.seer.trick.fleet.traffic.distributed.map.Point
import com.seer.trick.fleet.traffic.distributed.plan.model.PointData

/**
 *  可前进、后退的运动模型
 * */
class BothPlanPathAlgorithm(val request: PlanRequest) : PlanPathAlgorithm(request) {

  override fun findRobotHeadings(start: Point, line: Line, robotHeading: Int): List<Int> {
    val robotHeadings = ArrayList<Int>()
    // 判断这条边是否允许此车型通过， 对于点不可旋转时计算
    if (!start.rotate || cantRotatePoint.contains(start.pointName)) {
      // 判断是如何行驶，前进 or 后退
      if (line.driveDirection == MoveDirection.Dual &&
        (
          AngleHelper.sameAngleInFiveDegree(robotHeading, line.enterDir) ||
            AngleHelper.opposeAngle(robotHeading, line.enterDir)
          )
      ) {
        robotHeadings.add(robotHeading)
      }
      if (line.driveDirection == MoveDirection.Forward &&
        AngleHelper.sameAngleInFiveDegree(robotHeading, line.enterDir)
      ) {
        robotHeadings.add(robotHeading)
      }
      if (line.driveDirection == MoveDirection.Backward && AngleHelper.opposeAngle(robotHeading, line.enterDir)) {
        robotHeadings.add(robotHeading)
      }
      return robotHeadings
    }

    // 线上可以行驶的方向
    if (line.driveDirection == MoveDirection.Dual) {
      robotHeadings.add(line.enterDir)
      robotHeadings.add(AngleHelper.processAngle(line.enterDir + AngleHelper.DOWN_ANGLE))
    } else {
      if (line.driveDirection == MoveDirection.Forward) robotHeadings.add(line.enterDir)
      if (line.driveDirection == MoveDirection.Backward) {
        robotHeadings.add(AngleHelper.processAngle(line.enterDir + AngleHelper.DOWN_ANGLE))
      }
    }

    return robotHeadings
  }

  override fun costS(pointData: PointData?, line: Line): Double = if (processStop(pointData, line)) 0.0 else 1.5

  override fun costR(rHeading: Int, line: Line): Double =
    if (AngleHelper.sameAngleInFiveDegree(rHeading, line.enterDir)) {
      0.0
    } else {
      if (context.robotChassisType == ChassisType.STEER) {
        0.1 * line.length
      } else {
        0.001 * line.length
      }
    }

  override fun costT(rHeading: Int, robotHeading: Int): Double =
    if (AngleHelper.sameAngleInFiveDegree(rHeading, robotHeading)) {
      0.0
    } else {
      var tn = AngleHelper.vectorAngle(rHeading, robotHeading) / RIGHT_ANGLE
      if (AngleHelper.vectorAngle(rHeading, robotHeading) % RIGHT_ANGLE > SECTOR_VALUE) {
        tn += 1
      }
      1.5 * tn
    }
}