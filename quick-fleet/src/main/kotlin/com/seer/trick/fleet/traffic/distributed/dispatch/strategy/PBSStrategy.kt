package com.seer.trick.fleet.traffic.distributed.dispatch.strategy

import com.seer.trick.fleet.traffic.distributed.context.domain.RobotContext
import com.seer.trick.fleet.traffic.distributed.dispatch.ScheduleRequest
import org.slf4j.LoggerFactory

object PBSStrategy : Strategy {

  private val logger = LoggerFactory.getLogger(javaClass)
  override fun check(context: RobotContext): Boolean = false

  override fun handle(request: ScheduleRequest) {
    logger.debug("PBS")
  }
}