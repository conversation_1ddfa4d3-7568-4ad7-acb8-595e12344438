package com.seer.trick.fleet.traffic.distributed.dispatch

import com.seer.trick.fleet.traffic.distributed.block.BlockItem
import com.seer.trick.fleet.traffic.distributed.context.domain.RobotContext
import com.seer.trick.fleet.traffic.distributed.plan.model.PathAction
import java.util.*

/**
 * 执行时构建的请求信息
 * */
data class ScheduleRequest(

  val robotName: String, // 机器人编码

  val context: RobotContext, // 机器人上下文

  val allocatedIndex: Long, // 已经分配的索引

  val currentIndex: Long, // 当前索引

  val limitLength: Int, // 限制申请的长度

  val reservePath: List<PathAction> = LinkedList(), // 已经持有的路径

  var requestPath: MutableList<PathAction> = LinkedList(), // 需要的申请路径

  val remainPath: MutableList<PathAction> = LinkedList(), // 未申请的的路径

  var specialAreas: MutableList<SpecialArea> = mutableListOf(), // 特殊区域

  var blockRobot: MutableList<String> = mutableListOf(), // 预阻挡的机器人

  var version: String? = null,
)

/**
 *  执行 构建的响应信息
 * */
data class ScheduleResponse(

  val robotName: String, // 机器人信息

  val resultPath: MutableList<PathAction> = LinkedList(), // 响应的路径

  var blocks: MutableList<BlockItem> = mutableListOf(), // 阻挡信息
)

data class SpecialArea(
  val type: AreaType = AreaType.UNDEFINE,
  val startIndex: Long,
  val targetIndex: Long,
  val points: List<String>,
)

enum class AreaType {
  CHANNEL, // 单通道类型
  LIMIT, // 限车类型
  OPTICAL, // 光通讯类型
  AVOID, // 自由导航类型
  UNDEFINE,
}