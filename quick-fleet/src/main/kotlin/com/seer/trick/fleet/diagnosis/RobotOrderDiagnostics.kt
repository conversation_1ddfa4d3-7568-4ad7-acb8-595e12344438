import com.seer.trick.fleet.diagnosis.OrderCheckItems
import com.seer.trick.fleet.diagnosis.RobotCheckItems
import com.seer.trick.fleet.order.OrderRuntime
import com.seer.trick.fleet.service.RobotRuntime

/**
 * 机器人诊断系统
 */
object RobotOrderDiagnostics {
  // 机器人允许接单基础检查组
  private val checkRobotAvailableForAnyOrdersGroup = CheckGroup("基础检查").apply {
    // 机器人是否禁用
    addCheck(RobotCheckItems.RobotDisabled)
    // 机器人是否停止接单
    addCheck(RobotCheckItems.RobotOffDuty)
    // 机器人是否在线
    addCheck(RobotCheckItems.RobotOffline)
    // 机器人是否有控制权
    addCheck(RobotCheckItems.RobotNotMaster)
    // 交管初始化是否完成
    addCheck(RobotCheckItems.TrafficNotReady)
    // 机器人是否故障
    addCheck(RobotCheckItems.RobotFailed)
    // 机器人是否受控
    // TODO addCheck(RobotCheckItems.RobotCompositiveControlledFailed)
  }

  // 机器人运行接业务单检查组
  private val canAcceptBzOrdersGroup = CheckGroup("业务单检查").apply {
    // 基础检查
    include(checkRobotAvailableForAnyOrdersGroup)
    // 检查充电时间
    addCheck(RobotCheckItems.RobotChargingNotEnoughTime)
    // 检查电量是否大于chargeOnly
    addCheck(RobotCheckItems.RobotBatteryExceedChargeOnly)
    // 检查机器人是否在强冲
    addCheck(RobotCheckItems.RobotForceCharging)
    // 后台任务是否挂掉
    addCheck(RobotCheckItems.BackgroundJob)
  }

  // 机器人能否接新单检查组
  private val canAcceptNewBzOrdersGroup = CheckGroup("新单检查").apply {
    // 包含业务单检查组（包括其包含的基础检查组）
    include(canAcceptBzOrdersGroup)
    // 没有更多库位了
    addCheck(RobotCheckItems.RobotNoMoreBin)
  }

  // 机器人能否停靠检查组
  private val shouldParkGroup = CheckGroup("停靠检查").apply {
    // 先包含基础检查
    include(checkRobotAvailableForAnyOrdersGroup)
    // 是否暂停停靠
    addCheck(RobotCheckItems.RobotParkingPaused)
    // 机器人是否在执行业务单
    addCheck(RobotCheckItems.RobotBusy)
    // 正在执行自动单，包括充电、停靠
    addCheck(RobotCheckItems.RobotAutoOrder)
    // 已经在停靠点了
    addCheck(RobotCheckItems.RobotAlreadyParked)
    // 需要空闲一段时间才去停靠
    addCheck(RobotCheckItems.RobotWaitIdleTimeoutToPark)
    // 开始充了，至少充一段时间，不能立即结束
    addCheck(RobotCheckItems.RobotChargingNotEnoughTime)
    // 充电但没充到 FULL
    addCheck(RobotCheckItems.RobotChargingNotFull)
    // 是否有可用的停靠点
    addCheck(RobotCheckItems.RobotParkPointAvailable)
  }

  /**
   * 检查机器人是否能充电，不管是强充还是建议
   */
  private val checkRobotAnyCharging = CheckGroup("可以充电检查").apply {
    include(checkRobotAvailableForAnyOrdersGroup)
    // 是否禁用自动充电
    addCheck(RobotCheckItems.RobotChargingPaused)
    // 正在去充电
    addCheck(RobotCheckItems.RobotToCharging)
    // 机器人在执行业务单，不能充电
    addCheck(RobotCheckItems.RobotBusy)
    // 开始充了，至少充一段时间，不能立即结束
    addCheck(RobotCheckItems.RobotChargingNotEnoughTime)
    // 正在充电
    addCheck(RobotCheckItems.RobotCharging)
    // 是否有可用的充电点
    addCheck(RobotCheckItems.RobotChargingPointAvailable)
  }

  // 机器人必须充电检查组
  private val mustChargeGroup = CheckGroup("必须充电检查").apply {
    include(checkRobotAnyCharging)
    // 电量大于必须充电的电量
    addCheck(RobotCheckItems.RobotBatteryBelowChargeOnly)
  }

  // 机器人可以充电检查组
  private val mayChargeGroup = CheckGroup("可以充电检查").apply {
    include(checkRobotAnyCharging)
    // 判断机器人电量是否低于 chargeNeed
    addCheck(RobotCheckItems.RobotBatteryBelowChargeNeed)
  }

  /**
   * 机器人是否能够到达指定点位
   */
  private val robotCanReachPointGroup = CheckGroup("机器人能否到达指定点位").apply {
    addCheck(RobotCheckItems.RobotCanReachPoint)
  }

  /**
   * 检查运单是否可被执行,将要执行运单步骤
   * 副作用：设置 executionReject
   */
  private val checkOrderExecutableStepGroup = CheckGroup("运单以及步骤能被执行检查").apply {
    // 基础检查
    addCheck(OrderCheckItems.OrderFault)
    addCheck(OrderCheckItems.OrderStatusCheck)
  }

  // 运单参数校验
  private val checkOrderValidateGroup = CheckGroup("运单参数校验").apply {
    addCheck(OrderCheckItems.OrderParametersValidate)
  }

  private val checkOrderKeyLocationsValidate = CheckGroup("运单关键路径有效").apply {
    addCheck(OrderCheckItems.OrderKeyLocationsValidate)
  }

  /**
   * 校验运单是否可达
   */
  private val checkOrderReachableGroup = CheckGroup("运单是否可达").apply {
    addCheck(OrderCheckItems.OrderReachability)
  }

  /**
   * 校验运单是否有可用的机器人
   */
  private val checkRobotAvailableForOrderGroup = CheckGroup("运单是否有可用的机器人").apply {
    addCheck(OrderCheckItems.ValidateRobotsStatus)
  }

  /**
   * 运单能被分派或重分派检查
   */
  private val checkOrderBeAllocatedOrAllocatedAgainGroup = CheckGroup("运单能被分派或重分派检查").apply {
    // 运单参数校验
    include(checkOrderValidateGroup)
    // 运单是否可达
    include(checkOrderReachableGroup)
    // 校验这个运单期望的机器人是否可用
    include(checkRobotAvailableForOrderGroup)
    // 运单是否是业务运单
    addCheck(OrderCheckItems.OrderNotBusiness)
    // 运单是否故障
    addCheck(OrderCheckItems.OrderFault)
    // 运单是否被撤回或者被取消
    addCheck(OrderCheckItems.OrderStatusCheck)
    // 运单是否已取货
    addCheck(OrderCheckItems.OrderLoaded)
    // 运单的第一个步骤是否完成
    // addCheck(OrderCheckItems.OrderFirstStepDone)
    // 运单已经被分配且，当前场景是否禁止运单的重分配
    addCheck(OrderCheckItems.NotReallocation)
  }

  /**
   * 运单为啥没有机器人接单
   */
  private val checkOrderNoRobotMatch = CheckGroup("运单为啥没有机器人接单").apply {
    include(checkOrderBeAllocatedOrAllocatedAgainGroup)
    // 状态不是待分派的单子都不能被接单
    addCheck(OrderCheckItems.OrderNotToBeAllocated)
    // 期待的机器人正在接其他单子
    addCheck(OrderCheckItems.ExpectedRobotExecutingOtherOrder)
    // 可接单的机器人不可达。检查运单是否可达时，只是检查任意机器人可达，并没有检查所有机器人可达，所以需要检查可接单的机器人是否可达
    addCheck(OrderCheckItems.OnDutyRobotsUnreachable)
  }

  private val checkRobotExecutingStepGroup = CheckGroup("评估机器人是否能执行一个步骤").apply {
    // 基础检查
    include(checkRobotAvailableForAnyOrdersGroup)
    // 机器人是否上报故障
    addCheck(RobotCheckItems.RobotFailed)
    // 后台任务是否挂掉
    addCheck(RobotCheckItems.BackgroundJob)
  }

  /**
   * 检查机器人为什么保持不动
   */
  private val checkRobotRemainsStationary = CheckGroup("检查机器人为什么保持不动").apply {
    // 检测机器人是否没接单
    addCheck(RobotCheckItems.RobotNoOrder)
    // 检测运单是否故障
    addCheck(RobotCheckItems.RobotOrdersFault)
    // 基础检查
    include(checkRobotAvailableForAnyOrdersGroup)
    // 机器人是否被阻挡
    addCheck(RobotCheckItems.RobotBlocked)
    // 路径规划是否暂停
    addCheck(RobotCheckItems.TrafficPlanPaused)
    // 机器人是否获取不到资源被其他机器人占了
    addCheck(RobotCheckItems.RobotBlockedByTraffic)
    // 执行跨区域运单时，是否有可用的电梯。
    addCheck(RobotCheckItems.LiftAvailableWhenCrossArea)
    // 后台任务是否挂掉
    addCheck(RobotCheckItems.BackgroundJob)
  }

  /**
   * 运单为啥不继续执行
   */
  private val checkOrderNoContinueExecuting = CheckGroup("运单为啥不继续执行").apply {
    // 校验运单已分派
    addCheck(OrderCheckItems.OrderIsAllocated)
    // 检测运单是否故障
    addCheck(OrderCheckItems.OrderFault)
    // 运单无下一步，且未封口
    addCheck(OrderCheckItems.OrderNoNextStep)
    // 机器人不可达下一步的位置
    addCheck(OrderCheckItems.OrderStepCannotAchievable)
    // 机器人为啥不动
    include(checkRobotRemainsStationary)
  }

  /**
   * 机器人能否接对应单检查
   */
  private val canAcceptThisOrdersGroup = CheckGroup("机器人能否接对应单检查").apply {
    addCheck(RobotCheckItems.RobotIsOrderExpected)
    addCheck(RobotCheckItems.RobotCanReachOrder)
    include(canAcceptNewBzOrdersGroup)
    include(checkOrderValidateGroup)
    // 运单是否是业务运单
    addCheck(OrderCheckItems.OrderNotBusiness)
    // 运单是否故障
    addCheck(OrderCheckItems.OrderFault)
    // 运单是否被撤回或者被取消
    addCheck(OrderCheckItems.OrderStatusCheck)
    // 运单是否已取货
    addCheck(OrderCheckItems.OrderLoaded)
    // 运单的第一个步骤是否完成
    addCheck(OrderCheckItems.OrderFirstStepDone)
    // 运单已经被分配且，当前场景是否禁止运单的重分配
    addCheck(OrderCheckItems.NotReallocation)
  }

  // 基础检查

  // ==== 统一的诊断API ====

  /**
   * 机器人是否能接业务单
   */
  fun canAcceptBzOrders(rr: RobotRuntime?, fullDiagnosis: Boolean = false): DiagnosisResponse =
    canAcceptBzOrdersGroup.check(CheckReq(rr = rr, fullDiagnosis = fullDiagnosis))

  /**
   * 机器人是否能接新业务单
   */
  fun canAcceptNewBzOrders(rr: RobotRuntime?, fullDiagnosis: Boolean = false): DiagnosisResponse =
    canAcceptNewBzOrdersGroup.check(CheckReq(rr = rr, fullDiagnosis = fullDiagnosis))

  /**
   * 判断机器人能否接这个单
   */
  fun canAcceptThisOrders(rr: RobotRuntime?, or: OrderRuntime?, fullDiagnosis: Boolean = false): DiagnosisResponse =
    canAcceptThisOrdersGroup.check(CheckReq(rr = rr, or = or, fullDiagnosis = fullDiagnosis))

  /**
   * 机器人是否应该停靠
   */
  fun shouldPark(rr: RobotRuntime?, currentPoint: String?, fullDiagnosis: Boolean = false): DiagnosisResponse =
    shouldParkGroup.check(CheckReq(rr = rr, fromPointName = currentPoint, fullDiagnosis = fullDiagnosis))

  /**
   * 机器人是否必须充电
   */
  fun mustCharge(rr: RobotRuntime?, currentPoint: String?, fullDiagnosis: Boolean = false): DiagnosisResponse =
    mustChargeGroup.check(CheckReq(rr = rr, fromPointName = currentPoint, fullDiagnosis = fullDiagnosis))

  /**
   * 机器人是否可以充电
   */
  fun mayCharge(rr: RobotRuntime?, currentPoint: String?, fullDiagnosis: Boolean = false): DiagnosisResponse =
    mayChargeGroup.check(CheckReq(rr = rr, fromPointName = currentPoint, fullDiagnosis = fullDiagnosis))

  /**
   * 机器人是否可以到达指定点位
   */
  fun robotCanReachPoint(
    rr: RobotRuntime?,
    pointName: String?,
    fullDiagnosis: Boolean = false,
    fromPointName: String?,
  ): DiagnosisResponse = robotCanReachPointGroup.check(
    CheckReq(
      rr = rr,
      pointName = pointName,
      fullDiagnosis = fullDiagnosis,
      fromPointName = fromPointName,
    ),
  )

  /**
   * 校验运单是否合法
   */
  fun checkOrderValidate(or: OrderRuntime?, fullDiagnosis: Boolean = false): DiagnosisResponse =
    checkOrderValidateGroup.check(CheckReq(or = or, fullDiagnosis = fullDiagnosis))

  fun checkOrderKeyLocationsValidate(or: OrderRuntime?, fullDiagnosis: Boolean = false): DiagnosisResponse =
    checkOrderKeyLocationsValidate.check(CheckReq(or = or, fullDiagnosis = fullDiagnosis))

  /**
   * 机器人是否能接业务单
   */
  fun checkOrderBeAllocatedOrAllocatedAgain(or: OrderRuntime?, fullDiagnosis: Boolean = false): DiagnosisResponse =
    checkOrderBeAllocatedOrAllocatedAgainGroup.check(CheckReq(or = or, fullDiagnosis = fullDiagnosis))

  /**
   * 机器人是否可以执行步骤
   */
  fun canExecuteStep(rr: RobotRuntime?, fullDiagnosis: Boolean = false): DiagnosisResponse =
    checkRobotExecutingStepGroup.check(CheckReq(rr = rr, fullDiagnosis = fullDiagnosis))

  /**
   * 校验运单是否允许执行步骤
   */
  fun checkOrderExecutableStep(or: OrderRuntime?, fullDiagnosis: Boolean = false): DiagnosisResponse =
    checkOrderExecutableStepGroup.check(CheckReq(or = or, fullDiagnosis = fullDiagnosis))

  /**
   * 校验运单是否可达
   */
  fun checkOrderReachable(or: OrderRuntime?, fullDiagnosis: Boolean = false): DiagnosisResponse =
    checkOrderReachableGroup.check(CheckReq(or = or, fullDiagnosis = fullDiagnosis))

  /**
   * 校验运单是否有可用的机器人
   */
  fun checkRobotAvailableForOrder(or: OrderRuntime?, fullDiagnosis: Boolean = false): DiagnosisResponse =
    checkRobotAvailableForOrderGroup.check(CheckReq(or = or, fullDiagnosis = fullDiagnosis))

  /**
   * 校验机器人为啥不动
   */
  fun checkRobotRemainsStationary(rr: RobotRuntime?, fullDiagnosis: Boolean = false): DiagnosisResponse =
    checkRobotRemainsStationary.check(CheckReq(rr = rr, fullDiagnosis = fullDiagnosis))

  /**
   * 校验运单为啥没有机器人接单
   */
  fun checkOrderNoRobotMatch(or: OrderRuntime?, fullDiagnosis: Boolean = false): DiagnosisResponse =
    checkOrderNoRobotMatch.check(CheckReq(or = or, fullDiagnosis = fullDiagnosis))

  /**
   * 校验运单为啥不继续执行
   */
  fun checkOrderNoContinueExecuting(
    or: OrderRuntime?,
    rr: RobotRuntime?,
    currentPoint: String?,
    fullDiagnosis: Boolean = false,
  ): DiagnosisResponse = checkOrderNoContinueExecuting.check(
    CheckReq(rr = rr, or = or, fromPointName = currentPoint, fullDiagnosis = fullDiagnosis),
  )
}