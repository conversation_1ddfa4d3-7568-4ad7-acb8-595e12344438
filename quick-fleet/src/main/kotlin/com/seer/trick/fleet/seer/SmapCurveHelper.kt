package com.seer.trick.fleet.seer

import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.seer.SmapCurveHelper.transferQuinticToCubicSpline
import com.seer.trick.robot.map.CurveType
import org.apache.commons.math3.linear.*
import org.tinyspline.BSpline
import kotlin.math.*

/**
 * Smap 曲线辅助类
 */
object SmapCurveHelper {

  /**
   * 创建一个B样条曲线
   *
   * B样条曲线是一种常用于曲线拟合的数学模型，它由一组控制点和一个定义曲线形状的节点（knot）序列组成
   * 该函数允许用户创建一个具有指定控制点数量、度数和维度的B样条曲线
   * 如果提供了节点序列，则使用提供的节点序列；否则，默认生成一个等距节点序列
   *
   * @param controlPointNumber 控制点的数量，决定了样条曲线的复杂度
   * @param degree 样条曲线的阶数，影响曲线的平滑度
   * @param dimension 样条曲线的维度，决定了曲线是二维、三维还是更高维度
   * @param knot 可选参数，样条曲线的节点序列，如果未提供，则默认生成
   * @param controlPoints 样条曲线的控制点坐标列表，定义了曲线的基本形状
   * @return 返回创建的BSpline对象
   */
  fun createBSpline(
    controlPointNumber: Long,
    degree: Long,
    dimension: Int,
    knot: List<Double>? = null,
    controlPoints: List<Double>,
  ): BSpline = BSpline(controlPointNumber, dimension.toLong(), degree).apply {
    this.controlPoints = controlPoints
    if (knot != null) this.knots = knot
  }

  /**
   * 创建形状为线段的 b 样条曲线
   * 其控制点为线段两端点，阶数为 1，维度为 2
   * @param path 路线
   */
  private fun createStraightPathBSpline(path: SmapAdvancedCurve): BSpline {
    val controlPoints = listOf(
      path.startPos.pos.x,
      path.startPos.pos.y,
      path.endPos.pos.x,
      path.endPos.pos.y,
    )
    return createBSpline(2, 1, 2, controlPoints = controlPoints)
  }

  /**
   * 将贝塞尔曲线转换成 b 样条曲线
   * 在 rbk 中其控制点数量为 2，之后可以改成任意控制点数，阶数为控制点数 - 1，维度为 2
   * @param path 路线
   */
  private fun createBezierPathBSpline(path: SmapAdvancedCurve): BSpline {
    val controlPoints = buildList {
      add(path.startPos.pos.x)
      add(path.startPos.pos.y)
      path.controlPos1?.let {
        add(it.x)
        add(it.y)
      }
      path.controlPos2?.let {
        add(it.x)
        add(it.y)
      }
      path.controlPos3?.let {
        add(it.x)
        add(it.y)
      }
      path.controlPos4?.let {
        add(it.x)
        add(it.y)
      }
      add(path.endPos.pos.x)
      add(path.endPos.pos.y)
    }
    return createBSpline(4, 3, 2, controlPoints = controlPoints)
  }

  /**
   * 将 BSpline 转换成贝塞尔曲线
   */
  fun transferBSplineToBezier(spline: BSpline): List<BezierPath>? = when {
    spline.isLinearOrCubicBezier() -> handleLinearOrCubicBezier(spline)
    spline.isQuinticBezier() -> {
      // 对五阶贝塞尔进行降阶，变成三阶贝塞尔
      val cubicSegments = transferQuinticToCubicSpline(spline, 5, 20)
      cubicSegments.flatMap { transferBSplineToBezier(it) ?: emptyList() }
    }

    spline.isNurbsBSpline() -> handleNurbsToBezier(spline)
    else -> null
  }

  /**
   * 将 NURBS 曲线转换成贝塞尔曲线
   */
  private fun handleNurbsToBezier(spline: BSpline): List<BezierPath> {
    val beziers = spline.toBeziers()
    val controlPoints = beziers.controlPoints
    val offset = (spline.order * spline.dimension).toInt()
    return buildList {
      for (n in 0 until (controlPoints.size / offset)) {
        val controls = buildList {
          for (i in 0 until offset / 3) {
            add(
              NurbsControlPoint(
                x = controlPoints[n * offset + i * 3] / controlPoints[n * offset + i * 3 + 2],
                controlPoints[n * offset + i * 3 + 1] / controlPoints[n * offset + i * 3 + 2],
                weight = 0.0,
              ),
            )
          }
        }
        add(BezierPath(degree = beziers.degree, controls = controls))
      }
    }
  }

  /**
   * 处理直线或者三次贝塞尔
   */
  private fun handleLinearOrCubicBezier(spline: BSpline): List<BezierPath> {
    val beziers = spline.toBeziers()
    val controlPoints = beziers.controlPoints
    val offset = (spline.order * spline.dimension).toInt()

    return buildList {
      for (n in 0 until (controlPoints.size / offset)) {
        val controls = buildList {
          for (i in 0 until offset / 2) {
            add(
              NurbsControlPoint(
                x = controlPoints[n * offset + i * 2],
                controlPoints[n * offset + i * 2 + 1],
                weight = 0.0,
              ),
            )
          }
        }
        add(BezierPath(degree = beziers.degree, controls = controls))
      }
    }
  }

  /**
   * 高级三阶贝塞尔曲线，实际上是五阶贝塞尔，为了保证平滑，让首尾的曲率为 0 ，运行编辑的还是四个控制点，其中第二个、第五个控制点是自动生成的
   * 相关 coding：https://seer-group.coding.net/p/robokit/requirements/issues/100/detail
   * 实现上是通过让五阶贝塞尔曲线的部分控制点重合（即 ( P_1 = P_2 ) 和 ( P_3 = P_4 )）
   */
  private fun createDegenerateBezierPathBSpline(path: SmapAdvancedCurve): BSpline {
    val controlPoints = buildList {
      add(path.startPos.pos.x)
      add(path.startPos.pos.y)
      path.controlPos1?.let {
        add(it.x)
        add(it.y)
      } // P_2 = P_1
      path.controlPos1?.let {
        add(it.x)
        add(it.y)
      }
      path.controlPos2?.let {
        add(it.x)
        add(it.y)
      }
      path.controlPos2?.let {
        add(it.x)
        add(it.y)
      } // P_4 = P_3
      add(path.endPos.pos.x)
      add(path.endPos.pos.y)
    }
    return createBSpline(6, 5, 2, controlPoints = controlPoints)
  }

  /**
   * 将 nurbs 曲线转换成齐次坐标，用更高维度的 b 样条曲线表示
   * 控制点数量为 6，之后可以改成任意控制点数。其阶数为 2，维度为 3
   * @param path 路线
   */
  private fun createNurbsPathBSpline(path: SmapAdvancedCurve): BSpline {
    val controlPoints = buildList {
      val fromZ = path.startPos.pos.z ?: 10.0
      add(path.startPos.pos.x * fromZ)
      add(path.startPos.pos.y * fromZ)
      add(fromZ)
      listOf(path.controlPos1, path.controlPos2, path.controlPos3, path.controlPos4).forEach { controlPos ->
        controlPos?.let {
          val z = controlPos.z ?: 10.0
          add(controlPos.x * z)
          add(controlPos.y * z)
          add(z)
        }
      }
      val toZ = path.endPos.pos.z ?: 10.0
      add(path.endPos.pos.x * toZ)
      add(path.endPos.pos.y * toZ)
      add(toZ)
    }
    return createBSpline(
      6,
      2,
      3,
      listOf(0.0, 0.0, 0.0, 0.25, 0.5, 0.75, 1.0, 1.0, 1.0),
      controlPoints = controlPoints,
    )
  }

  /**
   * 将路径转换为样条曲线
   *
   * 此函数根据给定路径的类型，将其转换为相应类型的 B 样条曲线
   * 如果路径类型不支持，则返回null
   *
   * TODO 如果解析不了，统一按直线处理
   *
   * @param path SmapAdvancedCurve 类型的路径对象，包含路径信息
   * @return 根据路径类型返回相应的 BSpline 对象，如果路径类型不支持则返回 null
   */
  fun transferPathToSpline(path: SmapAdvancedCurve): BSpline = when (path.className) {
    CurveType.StraightPath.name -> createStraightPathBSpline(path)
    CurveType.BezierPath.name -> createBezierPathBSpline(path)
    CurveType.NURBS6.name -> createNurbsPathBSpline(path)
    CurveType.DegenerateBezier.name -> createDegenerateBezierPathBSpline(path)
    else -> createStraightPathBSpline(path)
  }

  /**
   * 将场景路径转换为 b 样条
   */
  fun mapPathToSpline(mapPath: MapPath): BSpline {
    val line = BSpline(mapPath.controls.size.toLong(), 2L, mapPath.degree)

    if (mapPath.controls.isNotEmpty()) {
      val l0 = mutableListOf<Double>()
      if (mapPath.curveType == PathCurveType.NURBS6) {
        for (c in mapPath.controls) {
          val z = if (c.weight == 0.0) 10.0 else c.weight // TODO 可能有 bug，如果 weight 就是 0，不是 null，需要区分
          l0 += c.x
          l0 += c.y
          l0 += z
        }
      } else {
        for (c in mapPath.controls) {
          l0 += c.x
          l0 += c.y
        }
      }

      line.controlPoints = l0
    }
    line.knots = mapPath.knots
    return line
  }

  /**
   * 计算样条曲线的长度
   * 根据样条曲线的类型，计算其长度
   * 如果样条曲线类型不支持，则返回0.0
   * @param spline 样条曲线对象
   * @param path 路线
   */
  fun getLength(spline: BSpline, path: SmapAdvancedCurve): Double = when (path.className) {
    CurveType.NURBS6.name -> getLengthForNURBS(spline)
    else -> spline.chordLengths().arcLength()
  }.roundLengthPrecision()

  /**
   * 计算样条曲线上的长度
   * 根据样条曲线的类型，计算其长度
   * @param spline 样条曲线对象
   */
  private fun getLengthForNURBS(spline: BSpline): Double {
    val points = spline.sample(100)
    val x = DoubleArray(100) { points[it * 3] / points[it * 3 + 2] }
    val y = DoubleArray(100) { points[it * 3 + 1] / points[it * 3 + 2] }
    return (1 until 100).sumOf { i ->
      val dx = x[i] - x[i - 1]
      val dy = y[i] - y[i - 1]
      sqrt(dx * dx + dy * dy)
    }
  }

  /**
   * 根据样条曲线的类型，计算其上的一系列点位的坐标和角度
   *
   * 如果样条曲线类型不支持，则返回空列表
   * @param spline 样条曲线对象
   * @param steps 要计算的点位的数量，可以看成事每个点位的步长
   * @param pathClassName 路线类型
   */
  fun getPathPositionAndAngle(
    spline: BSpline,
    steps: Int,
    pathClassName: String,
    currentIndex: Double = 0.0,
    endIndex: Double = 1.0,
  ): List<CurvePoint2D> {
    var tEvalList = spline.equidistantKnotSeq(steps.toLong())
    tEvalList = tEvalList.filter { it in currentIndex..endIndex }
    val positionList = spline.evalAll(tEvalList)
    return when (pathClassName) {
      CurveType.NURBS6.name -> calculatePositionAndAngleForNURBS(positionList, tEvalList)
      else -> calculatePositionAndAngleForBezierAndStraight(spline, positionList, tEvalList)
    }
  }

  /**
   * 计算 nurbs 曲线上一系列点位的坐标和角度
   * 会传入连个点位列表，以两点之间的斜率，近似作为该点位曲线的切线斜率
   * @param positionList 样条曲线上的点的坐标列表
   * @param tEvalList 每个点位对应曲线的 t，t 取值为 0-1
   */
  private fun calculatePositionAndAngleForNURBS(
    positionList: List<Double>,
    tEvalList: List<Double>,
  ): List<CurvePoint2D> = buildList {
    val lastIndex = positionList.size / 3 - 1
    for (i in 0..lastIndex) {
      val tangent = when (i) {
        0 -> calculateAngleForNURBS(positionList, 0, 1)
        lastIndex -> calculateAngleForNURBS(positionList, lastIndex - 1, lastIndex)
        else -> calculateAngleForNURBS(positionList, i - 1, i + 1)
      }
      val percentage = tEvalList[i / 2]
      val normal = normalizeAngle(tangent + PI / 2)
      add(
        CurvePoint2D(
          (positionList[i * 3] / positionList[i * 3 + 2]).roundLengthPrecision(),
          (positionList[i * 3 + 1] / positionList[i * 3 + 2]).roundLengthPrecision(),
          percentage,
          tangent.roundRadianPrecision(),
          normal.roundRadianPrecision(),
        ),
      )
    }
  }

  /**
   * 计算给定两点在NURBS（Non-Uniform Rational B-Spline）曲线中的角度
   *
   * @param positionList 包含所有点位置和权重的列表，格式为[x, y, w]的重复序列
   * @param index1 第一个点在列表中的索引
   * @param index2 第二个点在列表中的索引
   * @return 返回两点间的角度，单位为弧度
   */
  private fun calculateAngleForNURBS(positionList: List<Double>, index1: Int, index2: Int): Double {
    // 从列表中提取第一个点的x、y和权重值
    val (x1, y1, w1) = positionList.slice(index1 * 3 until index1 * 3 + 3)
    // 从列表中提取第二个点的x、y和权重值
    val (x2, y2, w2) = positionList.slice(index2 * 3 until index2 * 3 + 3)

    // 将点的坐标除以其权重值，以在二维空间中获得正确的坐标位置
    val p1 = Point2D(x1 / w1, y1 / w1)
    val p2 = Point2D(x2 / w2, y2 / w2)

    // 计算两点在x轴和y轴上的差异
    val dx = p2.x - p1.x
    val dy = p2.y - p1.y

    // 使用atan2函数计算两点间的角度，返回值为弧度单位
    return atan2(dy, dx)
  }

  /**
   * 将角度规范化到-π到π的范围内
   *
   * @param angle 需要规范化的角度，单位为弧度
   * @return 返回规范化的角度，单位为弧度
   */
  private fun normalizeAngle(angle: Double): Double {
    var result = angle
    // 如果角度大于π，则减去2π，使其落入-π到π的范围内
    while (result > PI) result -= 2 * PI
    // 如果角度小于等于-π，则加上2π，使其落入-π到π的范围内
    while (result <= -PI) result += 2 * PI
    return result
  }

  /**
   * 计算 bezier 曲线与直线上一系列点位的坐标和角度
   */
  private fun calculatePositionAndAngleForBezierAndStraight(
    spline: BSpline,
    positionList: List<Double>,
    tEvalList: List<Double>,
  ): List<CurvePoint2D> {
    val der = spline.derive().evalAll(tEvalList)
    return buildList {
      for (i in positionList.indices step 2) {
        val tangent = calculateAngleForBezierAndStraight(i, der)
        val percentage = tEvalList[i / 2]
        val normal = normalizeAngle(tangent + PI / 2)
        add(
          CurvePoint2D(
            positionList[i].roundLengthPrecision(),
            positionList[i + 1].roundLengthPrecision(),
            percentage,
            tangent.roundRadianPrecision(),
            normal.roundRadianPrecision(),
          ),
        )
      }
    }
  }

  /**
   * 计算 bezier 曲线与直线对应点的斜率
   */
  private fun calculateAngleForBezierAndStraight(i: Int, der: List<Double>): Double = atan2(der[i + 1], der[i])

  /**
   * 计算 Nurbs 曲线对应点的斜率
   */
  private fun calculateAngleForNURBS(i: Int, positionList: List<Double>, nextPositionList: List<Double>): Double {
    val w1 = positionList[i + 2]
    val x1 = positionList[i] / w1
    val y1 = positionList[i + 1] / w1
    val w2 = nextPositionList[i + 2]
    val x2 = nextPositionList[i] / w2
    val y2 = nextPositionList[i + 1] / w2
    return atan2(y2 - y1, x2 - x1)
  }

  /**
   * 将五阶贝塞尔曲线转换为多个三阶贝塞尔曲线段
   * @param quinticSpline 输入的五阶贝塞尔曲线
   * @param numSegments 分割段数（默认分5段）
   * @param numSamples 每段采样点数（默认20）
   */
  fun transferQuinticToCubicSpline(
    quinticSpline: BSpline,
    splitCount: Int = 5,
    samplePerSegment: Int = 20,
  ): List<BSpline> = quinticSpline.splitToCubicSegments(splitCount, samplePerSegment)

  /**
   * BSpline扩展：将五阶曲线分割并转换为三次贝塞尔曲线集合
   */
  private fun BSpline.splitToCubicSegments(segmentCount: Int, samplePerSegment: Int): List<BSpline> {
    require(degree == 5L) { "仅支持五阶贝塞尔曲线转换" }

    return this.splitToSubSplines(segmentCount).map { subSpline ->
      subSpline.fitToCubicBezier(samplePerSegment)
    }
  }

  /**
   * 将曲线分割为多个子段
   */
  private fun BSpline.splitToSubSplines(segmentCount: Int): List<BSpline> {
    val step = 1.0 / segmentCount
    return List(segmentCount) { index ->
      val start = index * step
      val end = (index + 1) * step
      this.subSpline(start, end)
    }
  }

  /**
   * 用最小二乘法拟合三次贝塞尔曲线
   */
  private fun BSpline.fitToCubicBezier(sampleCount: Int): BSpline {
    val samples = this.sample(sampleCount.toLong())
    val sValues = List(sampleCount) { it.toDouble() / (sampleCount - 1) }

    val (p0x, p0y) = samples.firstTwo()
    val (p3x, p3y) = samples.lastTwo()

    val (aMatrix, bVectorX, bVectorY) = buildLeastSquareSystem(
      samples,
      sValues,
      p0x,
      p0y,
      p3x,
      p3y,
    )

    val solver = QRDecomposition(Array2DRowRealMatrix(aMatrix)).solver

    // 修正向量转换
    val solutionX = solver.solve(ArrayRealVector(bVectorX)).toArray()
    val solutionY = solver.solve(ArrayRealVector(bVectorY)).toArray()

    val p1x = solutionX[0]
    val p2x = solutionX[1]
    val p1y = solutionY[0]
    val p2y = solutionY[1]

    return createCubicSpline(
      listOf(p0x, p0y, p1x, p1y, p2x, p2y, p3x, p3y),
    )
  }

  /**
   * 构建最小二乘方程组
   */
  private fun buildLeastSquareSystem(
    samples: List<Double>,
    sValues: List<Double>,
    p0x: Double,
    p0y: Double,
    p3x: Double,
    p3y: Double,
  ): Triple<Array<DoubleArray>, DoubleArray, DoubleArray> {
    val aMatrix = Array(sValues.size) { DoubleArray(2) }
    val bX = DoubleArray(sValues.size)
    val bY = DoubleArray(sValues.size)

    sValues.forEachIndexed { i, s ->
      val (x, y) = samples.getPairAt(i)
      val coeffP1 = 3 * s * (1 - s).pow(2)
      val coeffP2 = 3 * s.pow(2) * (1 - s)

      aMatrix[i][0] = coeffP1
      aMatrix[i][1] = coeffP2

      val term0 = (1 - s).pow(3) * p0x + s.pow(3) * p3x
      val term1 = (1 - s).pow(3) * p0y + s.pow(3) * p3y

      bX[i] = x - term0
      bY[i] = y - term1
    }

    return Triple(aMatrix, bX, bY)
  }

  /**
   * 创建三阶贝塞尔曲线
   */
  private fun createCubicSpline(ctrlPoints: List<Double>): BSpline = BSpline(4, 2, 3, BSpline.Type.Beziers).apply {
    controlPoints = ctrlPoints
  }

  /****************** 扩展函数 ******************/
  private fun List<Double>.firstTwo() = Pair(this[0], this[1])
  private fun List<Double>.lastTwo() = Pair(this[lastIndex - 1], this[lastIndex])
  private fun List<Double>.getPairAt(index: Int) = Pair(this[index * 2], this[index * 2 + 1])

  private fun BSpline.isLinearOrCubicBezier() = dimension == 2L && degree <= 3
  private fun BSpline.isQuinticBezier() = dimension == 2L && degree == 5L
  private fun BSpline.isNurbsBSpline() = dimension == 3L
}

fun main() {
  // 1. 创建五阶贝塞尔曲线
  val quintic = BSpline(6, 2, 5, BSpline.Type.Beziers).apply {
    controlPoints = listOf(
      -56.72, -0.705,
      -58.735, -0.681,
      -59.198, -0.176,
      -59.198, 0.847,
      -60.198, 1.2,
      -62.0, 1.3,
//      -63.0, 1.5
    )
  }

  val cubicSegments = transferQuinticToCubicSpline(quintic, 5, 20)

// 2. 转换为5个三次贝塞尔段
//  val cubicSegments = quintic.toCubicSegments(numSegments = 5)

// 3. 转换为业务对象
  cubicSegments.map {
    SmapCurveHelper.transferBSplineToBezier(it)?.firstOrNull()
  }
  quintic.sample(100)
  cubicSegments.flatMap { it.sample(100) ?: emptyList() }
  println("")
}