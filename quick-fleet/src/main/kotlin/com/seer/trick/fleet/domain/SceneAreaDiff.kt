package com.seer.trick.fleet.domain

/**
 * 比较 a2 区域相比与 a1 区域的差异。
 * 注意：a1 和 a2 都必须是未停用的
 */
class SceneAreaDiff(a1: SceneArea, a2: SceneArea) {

  /**
   * 合并地图的差异
   */
  val mergedMapDiff = SceneAreaMapDiff(a1.mergedMap, a2.mergedMap)

  /**
   * 新增的组的地图：原来没有这个组的地图，现在有了
   */
  val addedGroupsMaps = mutableMapOf<Int, SceneAreaMap>()

  /**
   * 删除了的组的地图：原来有这个组的地图，现在没有了
   */
  val removedGroupsMaps = mutableMapOf<Int, SceneAreaMap>()

  /**
   * 修改了的组的地图
   */
  val changedGroupsMaps = mutableMapOf<Int, SceneAreaMapDiff>()

  init {
    // TODO
    // for ((groupId, map2) in a2.groupsMap) {
    //   if (!a1.groupsMap.containsKey(groupId)) {
    //     addedGroupsMaps[groupId] = map2
    //   }
    // }
  }
}