package com.seer.trick.fleet.seer

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.event.EventListener
import com.seer.trick.base.soc.SysMonitorService
import com.seer.trick.helper.JsonHelper
import com.seer.trick.robot.vendor.seer.rbk.RbkEncoder
import com.seer.trick.robot.vendor.seer.rbk.RbkFrame
import com.seer.trick.robot.vendor.seer.rbk.RbkTcp
import com.seer.wcs.device.tcp.TcpServer
import io.netty.channel.ChannelHandlerContext
import org.slf4j.LoggerFactory
import java.io.IOException
import java.util.concurrent.*
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

/**
 * 仙工机器人，主动连调度，提供 Tcp 服务。维护与服务建立建立的机器人。
 * TODO 是否需要分场景配置
 */
object SeerRbkTcpServer : EventListener<String> {

  private val logger = LoggerFactory.getLogger(javaClass)

  @Volatile
  private var serv: TcpServer<RbkFrame>? = null

  // 机器人流水号 robotName -> flowNo。只允许通过 nextFlowNo() 获取，不允许直接读。
  private val robotFlowNoCounter: MutableMap<String, Int> = ConcurrentHashMap()

  private val executor = Executors.newScheduledThreadPool(1)
  private val futures: MutableList<ScheduledFuture<*>> = CopyOnWriteArrayList()

  // 不同机器人的同 flowNo 可能会重复，因此需要根据机器人拆分为二维 Map
  private val responseMap: MutableMap<String, MutableMap<Int, RbkFrame>> = ConcurrentHashMap()

  private val lock = ReentrantLock()
  private val condition = lock.newCondition()

  @Volatile
  private var disposed = false

  private val robots: MutableMap<String, SeerRobotConnection> = ConcurrentHashMap() // by robot name

  @Volatile
  private var configOn: Boolean? = null

  @Volatile
  private var configPort: Int? = null

  private val eventExecutor: ExecutorService = Executors.newSingleThreadExecutor()

  init {
    BzConfigManager.eventBus.listeners += this
  }

  override fun onEvent(e: String) {
    // 异步处理，否则保存 “系统配置” 时，响应时间太长了。
    eventExecutor.submit { reset() }
  }

  /**
   * 应该很快完成
   */
  @Synchronized
  fun reset() {
    SysMonitorService.log(
      subject = "Fleet",
      target = "SeerRbkTcpServer",
      field = "status",
      value = "Reset",
      level = SysMonitorService.SysMonitorLevel.Info,
    )

    dispose()
    init()
  }

  @Synchronized
  fun init() {
    val on = !BzConfigManager.getByPathAsBoolean("ScWcs", "tcpServer", "disabled")
    var port = BzConfigManager.getByPathAsInt("ScWcs", "tcpServer", "port")
    if (port == null || port <= 0) port = 5820

    SysMonitorService.log(
      subject = "Fleet",
      target = "SeerRbkTcpServer",
      field = "status",
      value = "Init, on=$on, port=$port, (old on=$configOn, old port=$configPort)",
      level = SysMonitorService.SysMonitorLevel.Info,
    )

    disposed = false

    configOn = on
    configPort = port

    if (!on) return // reset 时，一定先调了 dispose，所以不存在不改配置不重初始化 serv 的场景。

    serv = TcpServer(
      "Fleet TCP Server",
      port,
      RbkTcp.schema,
      false,
      SeerRbkTcpServer::onServerMessage,
      SeerRbkTcpServer::onRemoved,
    )

    SysMonitorService.log(
      subject = "Fleet",
      target = "SeerRbkTcpServer",
      field = "status",
      value = "Up, port=$port",
      level = SysMonitorService.SysMonitorLevel.Info,
    )

    futures += executor.scheduleAtFixedRate(SeerRbkTcpServer::ping, 0, 5, TimeUnit.SECONDS)
  }

  // TODO RBK 断开与 M4 的链接
  fun dispose() {
    SysMonitorService.log(
      subject = "Fleet",
      target = "SeerRbkTcpServer",
      field = "status",
      value = "Dispose, port=$configPort",
      level = SysMonitorService.SysMonitorLevel.Info,
    )

    disposed = true
    robots.clear()
    try {
      serv?.dispose()
      serv = null
      futures.forEach { it.cancel(true) }
    } catch (e: Exception) {
      logger.error("dispose Fleet TCP Server failed", e)
    }
  }

  private fun onServerMessage(ctx: ChannelHandlerContext, f: RbkFrame) {
    // if (f.apiNo != 0x1235) logger.debug("received message: ${JsonHelper.writeValueAsString(f)}")
    when (f.apiNo) {
      // 建立链接后的上报，收到请求
      0x206c -> onRegister(ctx, f) // 8003（0x206c）是 rbk 请求连接的 apiNo，不能改变
      0x1235 -> pong(ctx) // 仿真使用，rbk 不需要
      0x3944 -> {} // 响应 0x1234 的心跳包 忽略
      0x3945 -> {} // 响应 0x1235 的心跳包 忽略
      // 路径导航
      else -> receiveResponse(ctx, f.flowNo, f)
    }
  }

  private fun onRemoved(ctx: ChannelHandlerContext) {
    val robotName = getRobotNameByCtx(ctx) ?: return
    robots.remove(robotName)

    SysMonitorService.log(
      subject = "Fleet",
      target = "SeerRbkTcpServer",
      field = "robot",
      value = "Removed, name=$robotName",
      level = SysMonitorService.SysMonitorLevel.Info,
    )
  }

  private fun receiveResponse(ctx: ChannelHandlerContext, flowNo: Int, f: RbkFrame) {
    val robotName = getRobotNameByCtx(ctx)
      ?: throw BzError("receiveResponse robot not register, flowNo: $flowNo, f: ${JsonHelper.writeValueAsString(f)}")
    val m = responseMap[robotName] ?: mutableMapOf()
    // // 测试时，打印重复的 flowNo
    // val exist = m[flowNo]
    // if (exist != null) {
    //   .warn(
    //     logger,
    //     "receive duplicate flowNo: $flowNo, f: ${f.toJsonString()}, exist: ${exist.toJsonString()}",
    //   )
    // }
    m[flowNo] = f
    responseMap[robotName] = m
    lock.withLock {
      condition.signalAll()
    }
  }

  // private fun RbkFrame.toJsonString(): String = JsonHelper.writeValueAsString(this)

  private fun getRobotNameByCtx(ctx: ChannelHandlerContext): String? = robots.values.firstOrNull { it.ctx == ctx }?.name

  /**
   * 向指定机器人发送一个 API 请求。
   */
  fun request(robotName: String, apiNo: Int, reqStr: String, timeout: Long = 15 * 1000): String {
    val robot = robots[robotName] ?: throw BzError("errNoRobot", robotName)
    val flowNo = nextFlowNo(robotName)
    val ctx = robot.ctx
    // val address = ctx.channel().remoteAddress() as InetSocketAddress
    // val cIp = address.address.hostAddress
    // val cPort = address.port
    lock.withLock {
      ctx.writeAndFlush(RbkEncoder.buildReqBytes(apiNo, reqStr, flowNo))
      val startTimeMillis = System.currentTimeMillis()
      while (!disposed) {
        // 等待
        if (System.currentTimeMillis() - startTimeMillis > timeout) {
          throw IOException("Request timeout. API no=$apiNo")
        }
        if (!condition.await(timeout, TimeUnit.MILLISECONDS)) {
          throw IOException("Request timeout. API no=$apiNo")
        }

        // 找到消息内容
        if (responseMap.containsKey(robotName)) {
          val m = responseMap[robotName] ?: continue
          val resp = m.remove(flowNo) ?: continue
          // 校验 reqApiNo 与 respApiNo 是否匹配
          if (resp.apiNo != apiNo + 10000) {
            throw BzError("sendRequestToRobot, apiNo not match, reqApiNo: $apiNo, respApiNo: ${resp.apiNo}")
          }
          return resp.bodyStr
        }
      }
    }
    throw BzError("errCodeErr", "No connection")
  }

  private fun ping() {
    for (rr in robots.values) {
      // 清理掉已经关闭的链接，放到 onRemoved 中处理
      // if (rr.ctx.isRemoved) {
      //   robots.remove(rr.robot.name)
      //   rr.ctx.close()
      //   logger.info("remove disconnected robot: ${rr.robot.name}, rr.ctx.id = ${rr.ctx.hashCode()}")
      //   continue
      // }

      // ping(rr.ctx) // TODO 是否要 ping，再想一下
    }
  }

  private fun ping(ctx: ChannelHandlerContext, robotName: String) {
    ctx.writeAndFlush(RbkEncoder.buildReqBytes(0x1234, "", nextFlowNo(robotName)))
  }

  private fun pong(ctx: ChannelHandlerContext) {
    // 需要判断当前链接是否在 map 中，如果不在就不发 pong,等待重新被注册
    val robotName = getRobotNameByCtx(ctx)
    if (robotName != null) {
      ctx.writeAndFlush(RbkEncoder.buildReqBytes(0x1235, "", nextFlowNo(robotName)))
    } else {
      logger.warn("Received ping from unregistered connection")
    }
  }

  private fun onRegister(ctx: ChannelHandlerContext, frame: RbkFrame) {
    if (frame.bodyStr.isBlank()) {
      throw BzError("body is blank in onRegister frame")
    }

    val robot: RobotRegister = JsonHelper.mapper.readValue(frame.bodyStr, jacksonTypeRef())

    SysMonitorService.log(
      subject = "Fleet",
      target = "SeerRbkTcpServer",
      field = "robot",
      value = "Register, name=${robot.name}, ${JsonHelper.writeValueAsString(frame)}, ctx.id = ${ctx.hashCode()}",
      level = SysMonitorService.SysMonitorLevel.Info,
    )

    val conn = SeerRobotConnection(robot.name, ctx)
    robots[robot.name] = conn
    logger.info("Robot ${robot.name} register, ctx.id=${ctx.hashCode()}")
    // 必须发送响应报文，才能正式通信，rbk 不校验内容，内容可以任意
    ping(ctx, robot.name)
  }

  @Synchronized
  private fun nextFlowNo(robotName: String): Int {
    val no = (robotFlowNoCounter.getOrDefault(robotName, 0) + 1) % 512
    robotFlowNoCounter[robotName] = no
    return no
  }

  /**
   * 主动重置机器人连接
   */
  fun resetRobot(robotName: String, reason: String) {
    val conn = robots.remove(robotName)

    val oldExists = conn != null
    SysMonitorService.log(
      subject = "Fleet",
      target = robotName,
      field = "SeerRbkTcpServer::reset",
      value = "Reset, name=$robotName, reason=$reason, old exists=$oldExists",
      level = SysMonitorService.SysMonitorLevel.Info,
      instant = true, // TODO 不能一直刷
    )

    if (conn == null) return

    // TODO 这么做对吗？
    try {
      conn.ctx.close()
    } catch (e: Exception) {
      logger.error("Reset robot", e)
    }
  }
}

/**
 * 表示机器人对服务器的一个连接
 */
class SeerRobotConnection(val name: String, val ctx: ChannelHandlerContext)

data class RobotRegister(val name: String = "", val ip: String? = null, val port: Int? = null)