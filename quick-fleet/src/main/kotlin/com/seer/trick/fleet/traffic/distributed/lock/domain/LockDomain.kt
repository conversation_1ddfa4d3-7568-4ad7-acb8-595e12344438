package com.seer.trick.fleet.traffic.distributed.lock.domain

import com.seer.trick.fleet.traffic.distributed.block.BlockItem
import com.seer.trick.fleet.traffic.distributed.lock.model.SpaceLock

data class LockRequest(
  val id: String,
  val mapName: String,
  val sceneId: String,
  val spaces: MutableList<SpaceLock>,
  var tryNumber: Int = 3,
  var continueapplicatons: MutableList<Int> = mutableListOf(),
)

data class LockResponse(
  val id: String,
  val success: Boolean,
  val spaces: MutableList<SpaceLock>,
  var blocks: MutableList<BlockItem> = mutableListOf(),
)