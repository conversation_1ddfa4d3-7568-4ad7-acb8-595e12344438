package com.seer.trick.fleet.traffic.distributed.deadlock

import com.seer.trick.fleet.traffic.distributed.block.BlockItem
import com.seer.trick.fleet.traffic.distributed.deadlock.model.DeadLockMessage
import com.seer.trick.fleet.traffic.distributed.lock.LockType

interface DeadLockProcessor {

  fun check(robotName: String, block: MutableList<BlockItem>): DeadLockMessage

  fun handle(message: DeadLockMessage): Boolean

  fun findRobot(blocks: MutableList<BlockItem>): MutableList<BlockItem> {
    if (blocks.isEmpty()) return mutableListOf()

    return blocks.filter { it.type == LockType.ROBOT || it.type == LockType.ROBOT_WAIT }.toMutableList()
  }

  fun findContainer(blocks: MutableList<BlockItem>): MutableList<BlockItem> {
    if (blocks.isEmpty()) return mutableListOf()
    return blocks.filter { it.type == LockType.RACK }.toMutableList()
  }
}