package com.seer.trick.fleet.seer

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.base.file.FileManager
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.service.SceneFileService
import com.seer.trick.helper.FileHelper
import com.seer.trick.helper.JsonFileHelper
import com.seer.trick.helper.JsonHelper
import org.apache.commons.io.FileUtils
import java.io.File
import kotlin.random.Random

/**
 * RDS CORE 场景与 M4 场景互转。暂时保留
 * RDS CORE 场景转 M4 场景 用 RdsCoreSceneToM4SceneConverter
 * // TODO 没人用了？
 */
object RdsCoreSceneService {

  /**
   * RDS CORE 场景文件 -> M4 场景
   * 一个场景一次只能转换一个 RDS CORE 场景文件
   * TODO smap 高级区域和场景区域高级组目前放到一起的
   */
  @Synchronized
  fun parseRdsCoreSceneFile(sceneId: String, rdsCoreSceneZipFile: File): SceneStructure {
    val rdsCoreDir = SceneFileService.getSceneRdsCoreDir(sceneId)
    rdsCoreDir.mkdirs()
    FileUtils.cleanDirectory(rdsCoreDir) // 清空！

    FileHelper.unzipFileToDir(rdsCoreSceneZipFile, rdsCoreDir)

    val rdsCoreSceneFile = File(rdsCoreDir, "rds.scene")
    // TODO 建个实体类
    val node: JsonNode = JsonFileHelper.readJsonFromFile(rdsCoreSceneFile) ?: throw BzError("errNoRdsScene")

    // 标签
    val tagMap: MutableMap<String, MutableSet<String>> = mutableMapOf()
    val robotTags = transformTags(node["labels"]?.asIterable(), tagMap)

    // 机器人组
    val robotIndexMap: MutableMap<String, Int> = mutableMapOf()
    val (robotGroup, robots) = transformRobotGroup(node["robotGroup"], tagMap, robotIndexMap)

    // 区域
    val areas = transformAreas(node["areas"], robotIndexMap) ?: throw BzError("errNoRdsScene")

    return SceneStructure(
      robotGroups = robotGroup,
      robotTags = robotTags ?: emptyList(),
      robots = robots,
      areas = areas,
    )
  }

  /**
   * M4 场景 -> RDS CORE 场景文件
   * TODO
   * 1. 机器人属性 目前给的是默认值（当前地图地图、ip、是否真车、vendor、GW_url、chargeNeed、chargeOnly、chargedFull、chargedOk、
   * dir、endPosition、initialArea、initialPosition、maxDirtyWater、minCleanWater、modelName、percentage）
   * 2. 机器人组属性 目前给的默认值（robotType类型，expansion、extention、isPooling）
   * 3. 站点、线路、库位 来自哪张地图 原先有默认不修改，没有默认添加当前组下所有地图的
   * 4. 站点类型没有 className ignoreDir dir
   * 5. M4 线路 degree == 3 的 转成高阶贝塞尔曲线 >3 的转成 NURBS-6  线路圆弧转直线
   * 6. M4 机器人地图导出 TODO
   *
   */
  fun toRdsCoreSceneFile(sceneId: String, ss: SceneStructure): File {
    val rdsCoreSceneFile = FileManager.nextTmpFile("zip")
    // TODO
    val coreDir = SceneFileService.getSceneRdsCoreDir(sceneId)
    //  TEST
//    val coreDir = File("C:\\work\\workspace\\RDSWork\\.data\\rds\\scene")
    var rcs = RdsCoreScene()
    if (coreDir.exists()) {
      val rdsScene = coreDir.listFiles()?.find { it.name == "rds.scene" }
      if (rdsScene != null) {
        rcs = JsonFileHelper.readJsonFromFile(rdsScene) ?: throw BzError("errNoRdsScene")
      }
    }
    // 标签
    val countCoreTags: MutableList<RdsCoreLabel> = mutableListOf()
    val coreTags = transformM4ToCoreTag(ss.robots, rcs.labels)
    countCoreTags.addAll(coreTags)
    val tMap = coreTags.map { it.name }

    ss.robotTags.forEach {
      if (!tMap.contains(it.name)) {
        countCoreTags.add(RdsCoreLabel(name = it.name))
      }
    }

    // 机器人组
    val coreGroups = transformM4ToCoreGroup(ss.robotGroups, ss.robots, rcs.robotGroup)

    // 区域
    val coreArea = transformM4ToCoreArea(ss, rcs.areas)

    val copy = rcs.copy(robotGroup = coreGroups, labels = countCoreTags, areas = coreArea)
    var pathToFile: File? = null
    try {
      pathToFile = FileManager.pathToFile("tmp-core")
      if (!pathToFile.exists()) {
        pathToFile.mkdirs()
      }
//      val om = ObjectMapper()
//      om.setSerializationInclusion(JsonInclude.Include.NON_NULL)
//      val en:EntityValue = om.readValue(copy, jacksonTypeRef())
      JsonFileHelper.writeJsonToFile(File(pathToFile, "rds.scene"), copy, false)
      if (coreDir.exists()) {
        FileUtils.copyDirectory(coreDir, pathToFile) {
          it.name != "rds.scene"
        }
      }

      JsonHelper.mapper

      // M4 机器人地图 TODO
      // "rds.scene"
      FileHelper.zipDirToFile(pathToFile, rdsCoreSceneFile)
    } finally {
      if (pathToFile != null) {
        FileUtils.deleteDirectory(pathToFile)
      }
    }

    return rdsCoreSceneFile
  }

  /**
   * RDS CORE 标签转 RobotTag
   *  labels
   * tagMap key：机器热id value: 标签集合
   * labels core场景标签元素
   * return core 转 M4 标签集合
   *
   */
  private fun transformTags(
    labels: Iterable<JsonNode>?,
    tagMap: MutableMap<String, MutableSet<String>>,
  ): List<RobotTag>? {
    if (labels == null) return null
    return labels.let {
      // 将 JsonNode 转换为 RdsCoreLabels 列表
      val rdsLabels: List<RdsCoreLabel> =
        JsonHelper.mapper.readValue(JsonHelper.writeValueAsString(it), jacksonTypeRef())

      // 更新 tagMap 和创建 RobotTag 列表
      rdsLabels.map { rdsLabel ->
        rdsLabel.robotIds?.forEach { r ->
          (
            if (tagMap[r] == null) {
              tagMap[r] = mutableSetOf(rdsLabel.name)
            } else {
              tagMap[r]?.add(rdsLabel.name)
            }
            )
        } // 更新 tagMap
        RobotTag(rdsLabel.name) // 创建 RobotTag
      }.toMutableList()
    } ?: mutableListOf() // 如果 labels 为 null，返回一个空的 MutableList
  }

  // rdscore 机器人组转 RobotGroup
  /**
   * tagMap key 机器人id value 机器人标签
   * robotIndexMap：key: 机器人id value 机器人组 id
   * */
  private fun transformRobotGroup(
    jsonNode: JsonNode?,
    tagMap: MutableMap<String, MutableSet<String>>,
    robotIndexMap: MutableMap<String, Int>,
  ): Pair<List<RobotGroup>, List<SceneRobot>> {
    if (jsonNode == null) return Pair(emptyList(), emptyList())
    val robotGroups: MutableList<RobotGroup> = mutableListOf()
    val robots: MutableList<SceneRobot> = mutableListOf()

    val rcr: List<RdsCoreRobotGroup> = JsonHelper.mapper.convertValue(jsonNode, jacksonTypeRef())
    rcr.forEachIndexed { i, v ->
      v.robot?.forEach { r ->
        run {
          val tm = tagMap[r.id] ?: emptyList()
          val ip = r.property?.find { it.key == "ip" }?.stringValue ?: ""
          robots.add(SceneRobot(robotName = r.id, groupId = i, tags = tm.toMutableList(), robotIp = ip))
          robotIndexMap[r.id] = i
        }
      }
      robotGroups.add(RobotGroup(id = i, name = v.name))
    }
    return Pair(robotGroups, robots)
  }

  /**
   * rdscore 区域转 m4 区域
   * */
  private fun transformAreas(areas: JsonNode?, robotIndexMap: MutableMap<String, Int>): List<SceneArea>? {
    if (areas == null) return null
    val rca: List<RdsCoreAreas> = JsonHelper.mapper.readValue(JsonHelper.writeValueAsString(areas), jacksonTypeRef())
    val areas: MutableList<SceneArea> = mutableListOf() // 地图区域
    var indexPoints: Long = 0
    var indexPaths: Long = 0
    var indexZones: Long = 0
    var indexBins: Long = 0
    rca.forEachIndexed { index, rdsCoreAreas ->
      // 每个机器人组在每个区域的地图
      val maps = rdsCoreAreas.maps
      val groupsMap: MutableMap<Int, RobotAreaMapRecord> = mutableMapOf() // 每个机器人组对应的地图
      maps?.forEach { it ->
        run {
          val i = robotIndexMap[it.robotId]!!
          if (groupsMap[i] == null) {
            groupsMap[i] = RobotAreaMapRecord(mapName = it.mapName, mapMd5 = it.md5) // TODO 机器人地图路径
          }
        }
      }
      indexPoints += if (index == 0) 0 else (rca[index - 1].logicalMap.advancedBlocks?.size ?: 0)
      val pointMap: MutableMap<String, Long> = mutableMapOf()
      // 点位
      val points = transformPoints(rdsCoreAreas.logicalMap.advancedPoints, indexPoints, pointMap)

      indexPaths += if (index == 0) 0 else (rca[index - 1].logicalMap.advancedCurves?.size ?: 0)
      // 线路
      val paths = transformPath(rdsCoreAreas.logicalMap.advancedCurves, indexPaths, pointMap)

      indexZones += if (index == 0) {
        0
      } else {
        (
          rca[index - 1].logicalMap.advancedBlocks?.size
            ?: 0
          ) + (rca[index - 1].logicalMap.advancedAreaList?.size ?: 0)
      }
      // 区域
      val zones =
        transformZone(rdsCoreAreas.logicalMap.advancedBlocks, rdsCoreAreas.logicalMap.advancedAreaList, indexZones)

      // 库位
      if (index == 0) {
        indexBins = 0
      } else {
        rca[index - 1].logicalMap.binLocationsList?.forEach {
          if (!it.binLocationList.isNullOrEmpty()) {
            indexBins += it.binLocationList.size
          }
        }
      }
//      val binPoints: MutableSet<SceneBinPoint> = mutableSetOf()
      val bins: MutableList<SceneBin> = mutableListOf()
//      transformBins(rdsCoreAreas.logicalMap.binLocationsList, indexBins, binPoints, bins)
      val pMap = points.associateBy { it.name }

      transformBins(rdsCoreAreas.logicalMap.binLocationsList, indexBins, bins, pMap)

//      val sceneAreaMap = SceneAreaMap(points = points, paths = paths, zones = zones, binPoints = binPoints.toMutableList(), bins = bins)
      val sceneAreaMap = SceneAreaMap(points = points, paths = paths, zones = zones, bins = bins)

      areas.add(
        SceneArea(
          id = index,
          name = rdsCoreAreas.name,
          displayOrder = index,
          mergedMap = sceneAreaMap,
          groupsMap = groupsMap,
        ),
      )
    }
//    var pointIdCounter: Long = 0
//    var pathIdCounter: Long = 0
//    var zoneIdCounter: Long = 0
//    areas.forEach { it ->
//      pointIdCounter += it.mergedMap.points.size
//      pathIdCounter += it.mergedMap.paths.size
//      zoneIdCounter += it.mergedMap.zones.size
//    }
//    return SceneMapSet(areas, areaIdCounter = areas.size.toLong(), pointIdCounter = pointIdCounter, pathIdCounter = pathIdCounter, zoneIdCounter = zoneIdCounter)
    return areas
  }

  private fun transformPoints(
    advancedPoints: List<AdvancedPoints>?,
    indexPoints: Long,
    pointMap: MutableMap<String, Long>,
  ): List<MapPoint> {
    if (advancedPoints.isNullOrEmpty()) return emptyList()
    return advancedPoints.mapIndexed { index, advancedPoints ->
      val id = index + indexPoints + 1
      pointMap[advancedPoints.instanceName] = id
      MapPoint(
        id,
        name = advancedPoints.instanceName,
        x = advancedPoints.pos.x,
        y = advancedPoints.pos.y,
      )
    }
  }

  // core 场景线路转 spline
  /** indexPath 线路 indexPath 增长
   * pointMap 点位 id key 站点 value id
   * return 转换后的线路
   * */
  private fun transformPath(
    advancedCurves: List<AdvancedCurves>?,
    indexPath: Long,
    pointMap: MutableMap<String, Long>,
  ): List<MapPath> {
    if (advancedCurves.isNullOrEmpty()) return emptyList()
    val pathMap: MutableMap<String, MapPath> = mutableMapOf()
    advancedCurves.forEachIndexed { index, advancedCurves ->
      val fromPointName = advancedCurves.startPos.instanceName
      val toPointName = advancedCurves.endPos.instanceName
      // TODO 这里不对
      if (pathMap[fromPointName] != null || pathMap[toPointName] != null) {
        pathMap[fromPointName] ?: pathMap[toPointName]!!
        // pathMap[scenePath.fromPointName] = scenePath.copy(dual = true)
        return@forEachIndexed
      }
      var id = indexPath + index + 1

      // 线路转 spline
      val cp1 = if ("ArcPath" == advancedCurves.className) { // 圆弧转直线
        null
      } else {
        advancedCurves.controlPos1
      }
      val smapAdvancedCurve = SmapAdvancedCurve(
        className = advancedCurves.className,
        instanceName = advancedCurves.instanceName,
        startPos = SmapAdvancedCurveEndpoint(
          instanceName = advancedCurves.startPos.instanceName,
          pos = advancedCurves.startPos.pos,
        ),
        endPos = SmapAdvancedCurveEndpoint(
          instanceName = advancedCurves.endPos.instanceName,
          pos = advancedCurves.endPos.pos,
        ),
        controlPos1 = cp1,
        controlPos2 = advancedCurves.controlPos2,
        controlPos3 = advancedCurves.controlPos3,
        controlPos4 = advancedCurves.controlPos4,
      )

      val spline = SmapCurveHelper.transferPathToSpline(smapAdvancedCurve) ?: return@forEachIndexed

      val direction = advancedCurves.property?.firstOrNull { it.key == "direction" }?.int32Value
      val fromPointId = pointMap[advancedCurves.startPos.instanceName]!!
      val toPointId = pointMap[advancedCurves.endPos.instanceName]!!
      val remark = advancedCurves.desc
//      val length = sqrt((advancedCurves.startPos.pos.x - advancedCurves.endPos.pos.x).pow(2.0)
//        + (advancedCurves.startPos.pos.y - advancedCurves.endPos.pos.y).pow(2.0))
      val length = spline.chordLengths().arcLength()
      val tracePoints = SmapCurveHelper.getPathPositionAndAngle(spline, 10, smapAdvancedCurve.className)
      // 顺序:起点、控制点、终点 TODO
      val controls: MutableList<NurbsControlPoint> = mutableListOf(
        NurbsControlPoint(
          x = advancedCurves.startPos.pos.x,
          y = advancedCurves.startPos.pos.y,
          weight = advancedCurves.startPos.pos.z
            ?: 0.0,
        ),
      )
      listOf(
        advancedCurves.controlPos1,
        advancedCurves.controlPos2,
        advancedCurves.controlPos3,
        advancedCurves.controlPos4,
      ).forEach { controlPos ->
        controlPos?.let {
          controls.add(NurbsControlPoint(x = it.x, y = it.y, weight = it.z ?: 0.0))
        }
      }
      controls.add(
        NurbsControlPoint(
          x = advancedCurves.endPos.pos.x,
          y = advancedCurves.endPos.pos.y,
          weight = advancedCurves.endPos.pos.z
            ?: 0.0,
        ),
      )
      val scenePath = MapPath(
        id = id,
        fromPointId = fromPointId,
        fromPointName = fromPointName,
        toPointId = toPointId,
        toPointName = toPointName,
        moveDirection = when (direction) {
          0 -> MoveDirection.Forward
          1 -> MoveDirection.Backward
          else -> MoveDirection.Dual
        },
        remark = SmapHelper.base64ToString(remark) ?: "", // 原文是 base64 的需要转成 utf8 的。remark ?: "",
        actualLength = length,
        controls = controls,
        middlePoint = tracePoints[tracePoints.size / 2],
        degree = spline.degree,
        tracePoints = tracePoints,
      )

      pathMap[fromPointName] = scenePath
    }
    return pathMap.map { it.value }
  }

  // core 高级区域转 m4
  /*
   * advancedBlocks core 的高级组
   * advancedAreaList smap（高级区域） 清洁区域和区域描述
   *
   * */
  private fun transformZone(
    advancedBlocks: List<AdvancedBlock>?,
    advancedAreaList: List<AdvancedArea>?,
    indexZone: Long,
  ): MutableList<MapZone> {
    // 矩形旋转转化就有问题  矩形也是多边形
    val zone: MutableList<MapZone> = mutableListOf()
    if (!advancedBlocks.isNullOrEmpty()) {
      advancedBlocks.forEachIndexed { index, advancedBlock ->
        val id = index + 1 + indexZone
        val polygon = advancedBlock.posGroup.map { Point2D(it.x, it.y) }
        zone.add(
          MapZone(
            id = id,
            name = advancedBlock.instanceName,
            // shape = ZoneShape.Polygon,
            remark = advancedBlock.desc,
            polygon = polygon,
          ),
        )
      }
    }
    if (!advancedAreaList.isNullOrEmpty()) {
      advancedAreaList.forEachIndexed { index, advancedArea ->
        val id = index + 1 + indexZone + advancedBlocks!!.size
        val polygon = advancedArea.posGroup.map { Point2D(it.x, it.y) }
        zone.add(
          MapZone(
            id = id,
            name = advancedArea.instanceName,
            // shape = ZoneShape.Polygon,
            remark = advancedArea.desc,
            polygon = polygon,
          ),
        )
      }
    }
    return zone
  }

  /**
   * core 库位转
   * */
  private fun transformBins(
    binLocationsList: List<BinLocationList>?,
    indexBins: Long,
    bins: MutableList<SceneBin>,
    pointMap: Map<String, MapPoint>,
  ) {
    // TODO 层数 0 初始是 0  缺少库区
//    val m: MutableMap<String, SceneBinPoint> = mutableMapOf()
//    val binList: MutableList<BinLocation> = mutableListOf()
//    binLocationsList?.forEach {
//      if (!it.binLocationList.isNullOrEmpty()) {
//        binList.addAll(it.binLocationList)
//      }
//    }
    // 将所有的 BinLocation 扁平化为一个列表
    val binList = binLocationsList
      ?.flatMap { it.binLocationList.orEmpty() }
      ?: emptyList()

    binList.forEachIndexed { index, binLocation ->
      val id = index + indexBins + 1
      val pm = pointMap[binLocation.pointName]
      bins.add(
        SceneBin(
          id = id,
          name = binLocation.instanceName,
          layerNo = 0,
          workPointId = pm?.id,
          remark = binLocation.desc
            ?: "",
        ),
      )
    }
  }

//  private fun transformBins(binLocationsList: List<BinLocationList>?, indexBins: Long, binPoints: MutableSet<SceneBinPoint>, bins: MutableList<SceneBin>) {
//    // TODO 层数 0 初始是 0  缺少库区
//    val m: MutableMap<String, SceneBinPoint> = mutableMapOf()
// //    val binList: MutableList<BinLocation> = mutableListOf()
// //    binLocationsList?.forEach {
// //      if (!it.binLocationList.isNullOrEmpty()) {
// //        binList.addAll(it.binLocationList)
// //      }
// //    }
//    // 将所有的 BinLocation 扁平化为一个列表
//    val binList = binLocationsList
//      ?.flatMap { it.binLocationList.orEmpty() }
//      ?: emptyList()
//
//    binList.forEachIndexed { index, binLocation ->
//      val id = index + indexBins + 1
//      // sceneBinPoint id 不连续保证唯一
//      val sceneBinPoint = m[binLocation.pointName]
//        ?: SceneBinPoint(id = id, layers = 0, x = binLocation.pos.x, y = binLocation.pos.y, polygon = emptyList())
//      bins.add(SceneBin(id = id, name = binLocation.instanceName, layerNo = 0, pointId = sceneBinPoint.id, remark = binLocation.desc
//        ?: ""))
//      binPoints.add(sceneBinPoint)
//    }
//
//  }

  /*
   * 机器人标签：M4 转 core
   */
  private fun transformM4ToCoreTag(robots: List<SceneRobot>, labels: List<RdsCoreLabel>?): List<RdsCoreLabel> {
    // tag -> robots
    val resMap = mutableMapOf<String, MutableSet<String>>()

    for (r in robots) {
      for (tag in r.tags) {
        resMap.getOrPut(tag) { HashSet() }.add(r.robotName)
      }
    }

    // 将 resMap 转换为 RdsCoreLabels 列表
    return resMap.map { (key, robotNames) ->
      val label = labels?.find { it.name == key } ?: RdsCoreLabel(name = key)
      label.copy(robotIds = robotNames.toList())
    }
  }

  /*
   * M4 机器人组转 core
   * 1. 机器人来说：如果core本身就有只检验ip是否改变，当前地图不校验 如果没有默认 目前没记录当前地图给空 其他属性给默认值
   * 2. 机器人组区分不了 类型 默认不选择类型 分叉车和其他类型
   * */
  private fun transformM4ToCoreGroup(
    robotGroups: List<RobotGroup>,
    robots: List<SceneRobot>,
    coreGroups: List<RdsCoreRobotGroup>,
  ): List<RdsCoreRobotGroup> {
    val coreGroupMap = coreGroups.associateBy(RdsCoreRobotGroup::name)
    val res: MutableList<RdsCoreRobotGroup> = mutableListOf()
    for (rg in robotGroups) {
      if (coreGroupMap.keys.contains(rg.name) && !coreGroupMap[rg.name]!!.robot.isNullOrEmpty()) {
        val coreRobots = coreGroupMap[rg.name]!!.robot!!.associateBy { it.id }
        val groupRobots = mutableListOf<RdsCoreRobot>()
        for (r in robots) {
          if (r.groupId != rg.id) continue
          groupRobots += if (coreRobots.keys.contains(r.robotName)) {
            val property = coreRobots[r.robotName]!!.property!!
            val filter = property.filter { it.key != "ip" }.toMutableList()
            filter.add(
              SmapMapProperty(
                key = "ip",
                type = "string",
                value = "",
                tag = "",
                stringValue = r.robotIp ?: "",
              ),
            )
            RdsCoreRobot(id = r.robotName, property = filter)
          } else {
            RdsCoreRobot(id = r.robotName, property = getDefaultRobotProperty(ip = r.robotIp))
          }
        }
        res.add(RdsCoreRobotGroup(name = rg.name, robot = groupRobots, property = coreGroupMap[rg.name]!!.property))
      } else {
        val groupRobots = mutableListOf<RdsCoreRobot>()
        for (r in robots) {
          if (r.groupId != rg.id) continue
          groupRobots += RdsCoreRobot(id = r.robotName, property = getDefaultRobotProperty(ip = r.robotIp))
        }
        res.add(RdsCoreRobotGroup(name = rg.name, robot = groupRobots, property = getDefaultGroupProperty()))
      }
    }
    return res.toMutableList()
  }

  private fun transformM4ToCoreArea(ss: SceneStructure, areas: List<RdsCoreAreas>): List<RdsCoreAreas> {
    val coreScene: MutableList<RdsCoreAreas> = mutableListOf()
    val areasMap = areas.associateBy { it.name }
    for (area in ss.areas) {
      val groupRobots = ss.robotGroups.associate { it.id to ss.robots.filter { r -> r.groupId == it.id } }
      // 区域地图
      val maps = makeCoreMaps(groupRobots, area.groupsMap)
      // 站点
      val corePoints = areasMap[area.name]?.logicalMap?.advancedPoints?.associateBy { it.instanceName } ?: mapOf()

      val strSource = makeDefaultSource(groupRobots, area.groupsMap)
      val points = makeCorePoints(area.mergedMap.points, corePoints, strSource)

      val corePaths = areasMap[area.name]?.logicalMap?.advancedCurves?.associateBy { it.startPos.instanceName }
        ?: mapOf()
      val paths = makeCorePath(area.mergedMap.paths, corePaths, strSource)

      val coreBlocks = areasMap[area.name]?.logicalMap?.advancedBlocks?.associateBy { it.instanceName } ?: mapOf()
      val coreSmapBlocks = areasMap[area.name]?.logicalMap?.advancedAreaList?.associateBy { it.instanceName } ?: mapOf()
      // smap  高级区域
      val areaLists: MutableList<AdvancedArea> = mutableListOf()
      // 场景的高级区域
      val blocks = makeCoreBlocks(area.mergedMap.zones, coreBlocks, coreSmapBlocks, areaLists)

      val coreBins = areasMap[area.name]?.logicalMap?.binLocationsList?.flatMap { it.binLocationList.orEmpty() }
        ?.associateBy { it.instanceName }
        ?: mapOf()

//      val m4Points = area.mergedMap.binPoints.associateBy { it.id }
      val pMap = area.mergedMap.points.associateBy { it.id }
      val bins = makeCoreBin(area.mergedMap.bins, coreBins, strSource, pMap)
      val lm = LogicalMap(
        advancedPoints = points, advancedCurves = paths, advancedBlocks = blocks, binLocationsList = bins,
        advancedAreaList = areaLists,
        externalDeviceList = areasMap[area.name]?.logicalMap?.externalDeviceList
          ?: emptyList(),
        modelsList = areasMap[area.name]?.logicalMap?.modelsList
          ?: emptyList(),
        windowsList = areasMap[area.name]?.logicalMap?.windowsList ?: emptyList(),
        wallsList = areasMap[area.name]?.logicalMap?.wallsList
          ?: emptyList(),
        workAreaList = areasMap[area.name]?.logicalMap?.workAreaList ?: emptyList(),
      )
      coreScene.add(
        RdsCoreAreas(
          name = area.name,
          maps = maps,
          logicalMap = lm,
          backgroundImage = areasMap[area.name]?.backgroundImage,
        ),
      )
    }
    return coreScene
  }

  // M4 库位转换成 rdscore 的
  private fun makeCoreBin(
    bins: List<SceneBin>,
    coreBins: Map<String, BinLocation>,
    strSource: String,
    pMap: Map<Long, MapPoint>,
  ): List<BinLocationList> {
    val resBins: MutableMap<String, MutableList<BinLocation>> = mutableMapOf()
    for (b in bins) {
      val x = pMap[b.workPointId]?.x ?: 0.0
      val y = pMap[b.workPointId]?.y ?: 0.0
      // 没有就用站点的
      val binLocation = coreBins[b.name]
        ?: BinLocation(
          className = "GeneralLocation",
          instanceName = b.name,
          pos = SmapPos(x, y, 0.0),
          property = getDefaultBinProperty(x, y, strSource),
        )
      val copy = binLocation.copy(desc = b.remark)
      val key = if (copy.groupName.isNullOrBlank()) {
        "M4Bin"
      } else {
        copy.groupName
      }
      if (resBins[key] == null) {
        resBins[key] = mutableListOf(copy)
      } else {
        resBins[key]?.add(copy)
      }
    }

    return resBins.values.toList().map { BinLocationList(it.toList()) }
  }
//  private fun makeCoreBin(bins: List<SceneBin>, coreBins: Map<String, BinLocation>, m4Points: Map<Long, SceneBinPoint>, strSource: String): List<BinLocationList> {
//    val resBins: MutableMap<String, MutableList<BinLocation>> = mutableMapOf()
//    for (b in bins) {
//      val x = m4Points[b.pointId]!!.x
//      val y = m4Points[b.pointId]!!.y
//      val binLocation = coreBins[b.name]
//        ?: BinLocation(className = "GeneralLocation", instanceName = b.name, property = getDefaultBinProperty(x, y, strSource))
//      val copy = binLocation.copy(desc = b.remark, pos = SmapPos(x, y, 0.0))
//      val key = if (copy.groupName.isNullOrBlank()) {
//        "M4Bin"
//      } else {
//        copy.groupName
//      }
//      if (resBins[key] == null) {
//        resBins[key] = mutableListOf(copy)
//      } else {
//        resBins[key]?.add(copy)
//      }
//
//    }
//
//    return resBins.values.toList().map { BinLocationList(it.toList()) }
//  }

  // m4 高级区域转 rdscore 场景
  private fun makeCoreBlocks(
    zones: List<MapZone>,
    coreBlocks: Map<String, AdvancedBlock>,
    coreSmapBlocks: Map<String, AdvancedArea>,
    areaLists: MutableList<AdvancedArea>,
  ): List<AdvancedBlock> {
    val block: MutableList<AdvancedBlock> = mutableListOf()
    for ((index, z) in zones.withIndex()) {
      if (coreSmapBlocks.containsKey(z.name)) {
        coreSmapBlocks[z.name]?.let { areaLists.add(it) }
        continue
      } else if (coreSmapBlocks.containsKey(z.name)) {
        val posGroup = z.polygon.map { SmapPos(it.x, it.y, 0.0) }
        coreBlocks[z.name]?.let { block.add(it.copy(posGroup = posGroup)) }
      } else {
        // TODO 矩形暂时用左下角的点开始计算
        val posGroup =
          //   if (z.shape == ZoneShape.Rect) {
          //   listOf(
          //     SmapPos(z.rect.x, z.rect.y, 0.0),
          //     SmapPos(z.rect.x + z.rect.width, z.rect.y, 0.0),
          //     SmapPos(z.rect.x + z.rect.width, z.rect.y + z.rect.height, 0.0),
          //     SmapPos(z.rect.x, z.rect.y + z.rect.height, 0.0),
          //   )
          // } else {
          z.polygon.map { SmapPos(it.x, it.y, 0.0) }
        // }
        block.add(
          AdvancedBlock(
            className = "MutexRegion",
            instanceName = "M4-MutexRegion$index",
            posGroup = posGroup,
            dir = 0.0,
            desc = z.remark,
          ),
        )
      }
    }
    return block
  }

  private fun makeDefaultSource(list: Map<Int, List<SceneRobot>>, groupsMap: Map<Int, RobotAreaMapRecord>): String {
    var source = ""
    for (k in groupsMap.keys) {
      val sceneRobots = list[k] ?: continue
      sceneRobots.forEach { source += it.robotName + ":" + groupsMap[k]!!.mapName + "," }
    }
    if (source.isBlank()) return source
    return source.substring(0, source.length - 1)
  }

  // 将 m4 的机器人组和地图转为 core 的 maps
  private fun makeCoreMaps(
    robotGroups: Map<Int, List<SceneRobot>>,
    groupsMap: Map<Int, RobotAreaMapRecord>,
  ): List<AreaMaps> {
    val maps: MutableList<AreaMaps> = mutableListOf()
    for (k in groupsMap.keys) {
      val sceneRobots = robotGroups[k] ?: continue
      maps.addAll(
        sceneRobots.map {
          AreaMaps(
            robotId = it.robotName,
            mapName = groupsMap[k]!!.mapName,
            md5 = groupsMap[k]!!.mapMd5,
          )
        },
      )
    }
    return maps
  }

  // 将 m4 点转为 core的点  新增的默认点 默认点位类型是 ActionPoint
  private fun makeCorePoints(
    points: List<MapPoint>,
    corePoints: Map<String, AdvancedPoints>,
    strSource: String,
  ): List<AdvancedPoints> {
    val transformPoint: MutableList<AdvancedPoints> = mutableListOf()
    for (point in points) {
      val ap = AdvancedPoints(
        className = "ActionPoint",
        instanceName = point.name,
        pos = SmapPos(point.x, point.y, 0.0),
        ignoreDir = true,
        dir = 0.0,
        desc = point.remark,
        property = getDefaultPointProperty(strSource),
      )
      if (corePoints.containsKey(point.name)) {
        corePoints[point.name]?.let {
          transformPoint.add(
            ap.copy(
              pos = it.pos,
              className = it.className,
              ignoreDir = it.ignoreDir,
              dir = it.dir,
              property = it.property,
            ),
          )
        }
        continue
      }
      transformPoint.add(ap)
    }
    return transformPoint
  }

  // M4 线路转 core
  /*
   * 线路类型保持和原先一致，M4 新增线路 TODO
   * */
  private fun makeCorePath(
    paths: List<MapPath>,
    corePaths: Map<String, AdvancedCurves>,
    strSource: String,
  ): List<AdvancedCurves> {
    val resPath: MutableMap<String, AdvancedCurves> = mutableMapOf()
    for (p in paths) {
      if (corePaths[p.fromPointName] != null) {
        corePaths[p.fromPointName]?.let { resPath[p.fromPointName] = it }
      } else {
        // TODO 优化写法
        val startPos = AdvancedCurvePoint(
          className = "ActionPoint",
          instanceName = p.fromPointName,
          SmapPos(p.controls[0].x, p.controls[0].y, p.controls[0].weight),
          dir = 0.0,
          property = emptyList(),
          ignoreDir = false,
        ) // 线路起点
        val endPos = AdvancedCurvePoint(
          className = "ActionPoint",
          instanceName = p.toPointName,
          SmapPos(
            p.controls[p.controls.size - 1].x,
            p.controls[p.controls.size - 1].y,
            p.controls[p.controls.size - 1].weight,
          ),
          property = emptyList(),
          ignoreDir = false,
        ) // 线路终点
        val ac = if (p.degree == 3L) {
          val controlPos1 = SmapPos(p.controls[1].x, p.controls[1].y, p.controls[1].weight) // 高阶曲线控制点
          val controlPos2 = SmapPos(p.controls[2].x, p.controls[2].y, p.controls[2].weight)
          AdvancedCurves(
            className = "BezierPath",
            instanceName = p.fromPointName + "-" + p.toPointName,
            startPos = startPos,
            endPos = endPos,
            controlPos1 = controlPos1,
            controlPos2 = controlPos2,
            property = getDefaultPathProperty(strSource),
          )
        } else if (p.degree > 3L) {
          val part = (p.controls.size - 2) / 4
          val controlPos1 = SmapPos(p.controls[part].x, p.controls[part].y, p.controls[part].weight) // 高阶曲线控制点
          val controlPos2 = SmapPos(p.controls[part * 1].x, p.controls[part * 1].y, p.controls[part * 1].weight)
          val controlPos3 =
            SmapPos(p.controls[part * 2].x, p.controls[part * 2].y, p.controls[part * 2].weight) // 高阶曲线控制点
          val controlPos4 = SmapPos(p.controls[part * 3].x, p.controls[part * 3].y, p.controls[part * 3].weight)
          AdvancedCurves(
            className = "NURBS6",
            instanceName = p.fromPointName + "-" + p.toPointName,
            startPos = startPos,
            endPos = endPos,
            property = getDefaultPathProperty(strSource),
            controlPos1 = controlPos1,
            controlPos2 = controlPos2,
            controlPos3 = controlPos3,
            controlPos4 = controlPos4,
          )
        } else {
          AdvancedCurves(
            className = "StraightPath",
            instanceName = p.fromPointName + "-" + p.toPointName,
            startPos = startPos,
            endPos = endPos,
            property = getDefaultPathProperty(strSource),
          )
        }
        resPath[p.fromPointName] = ac
      }
      // TODO
      // if (p.dual && corePaths[p.toPointName] != null) {
      //   corePaths[p.toPointName]?.let { resPath[p.toPointName] = it }
      // } else if (p.dual) {
      //   resPath[p.toPointName] = resPath[p.fromPointName]!!.copy(
      //     instanceName = p.toPointName + "-" + p.fromPointName,
      //     startPos = resPath[p.fromPointName]!!.endPos,
      //     endPos = resPath[p.fromPointName]!!.startPos
      //   )
      // }
    }
    return resPath.map { it.value }
  }

  /*
   * 获取机器人组默认属性
   * */
  private fun getDefaultPointProperty(source: String): List<SmapMapProperty> = listOf(
    SmapMapProperty(key = "bindRobotMap", type = "string", value = "", tag = "", stringValue = source),
    SmapMapProperty(key = "label", type = "string", value = "", tag = "", stringValue = ""),
  )

  /*
   * 获取机器人组默认属性
   * */
  private fun getDefaultGroupProperty(): List<SmapMapProperty> = listOf(
    SmapMapProperty(key = "expansion", type = "double", value = "", tag = "", doubleValue = 0.0),
    SmapMapProperty(key = "extention", type = "double", value = "", tag = "", doubleValue = 0.0),
    SmapMapProperty(key = "isPooling", type = "bool", value = "", tag = "", boolValue = false),
  )

  /*
   * 获取机器人默认属性
   * */
  private fun getDefaultRobotProperty(ip: String?): List<SmapMapProperty> = listOf(
    SmapMapProperty(key = "current_map", type = "string", value = "", tag = "", stringValue = ""),
    SmapMapProperty(key = "ip", type = "string", value = "", tag = "", stringValue = ip ?: ""),
    SmapMapProperty(key = "is_simulation", type = "bool", value = "", tag = "", boolValue = true),
    SmapMapProperty(key = "color", type = "string", value = "", tag = "", stringValue = generateRandomColor()),
    SmapMapProperty(key = "vendor", type = "string", value = "", tag = "", stringValue = ""),
    SmapMapProperty(key = "GW_url", type = "string", value = "", tag = "", stringValue = ""),
    SmapMapProperty(key = "chargeNeed", type = "int32", value = "", tag = "", int32Value = -1),
    SmapMapProperty(key = "chargeOnly", type = "int32", value = "", tag = "", int32Value = -1),
    SmapMapProperty(key = "chargedFull", type = "int32", value = "", tag = "", int32Value = 90),
    SmapMapProperty(key = "chargedOk", type = "int32", value = "", tag = "", int32Value = 50),
    SmapMapProperty(key = "dir", type = "double", value = "", tag = "", doubleValue = 0.0),
    SmapMapProperty(key = "endPosition", type = "string", value = "", tag = "", stringValue = ""),
    SmapMapProperty(key = "initialArea", type = "string", value = "", tag = "", stringValue = ""),
    SmapMapProperty(key = "initialPosition", type = "string", value = "", tag = "", stringValue = ""),
    SmapMapProperty(key = "maxDirtyWater", type = "int32", value = "", tag = "", int32Value = 20),
    SmapMapProperty(key = "minCleanWater", type = "int32", value = "", tag = "", int32Value = 20),
    SmapMapProperty(key = "modelName", type = "string", value = "", tag = "", stringValue = ""),
    SmapMapProperty(key = "percentage", type = "double", value = "", tag = "", doubleValue = 0.0),
  )

  /*
   * 获取库位默认属性
   * */
  private fun getDefaultBinProperty(x: Double, y: Double, strSource: String): List<SmapMapProperty> = listOf(
    SmapMapProperty(key = "width", type = "double", value = "", tag = "", doubleValue = 0.3),
    SmapMapProperty(key = "height", type = "double", value = "", tag = "", doubleValue = 0.3),
    SmapMapProperty(
      key = "points",
      type = "json",
      value = "",
      tag = "",
      stringValue = "[{\"x\":${x - 0.15},\"y\":${y - 0.15}},{\"x\":${x + 0.15},\"y\":${y - 0.15}}," +
        "{\"x\":${x + 0.15},\"y\":${y + 0.15}},{\"x\":${x - 0.15},\"y\":${y + 0.15}}]",
    ),
    SmapMapProperty(key = "bindRobotMap", type = "string", value = "", tag = "", stringValue = strSource),
    SmapMapProperty(
      key = "3DProperty",
      type = "json",
      value = "",
      tag = "",
      stringValue = "{\"baseGround\":0,\"binHeight\":0.5,\"binLength\":0.8,\"binType\":\"Shelf\",\"binWidth\":0.8,\"shelfHeight\":0.75}",
    ),
  )

  // 获取线路的默认属性
  private fun getDefaultPathProperty(strSource: String): List<SmapMapProperty> = listOf(
    SmapMapProperty(key = "bindRobotMap", type = "string", value = "", tag = "", stringValue = strSource),
  )

  private fun generateRandomColor(): String {
    // 生成随机的 RGB 分量
    val red = Random.nextInt(0, 256)
    val green = Random.nextInt(0, 256)
    val blue = Random.nextInt(0, 256)

    // 将 RGB 分量转换为十六进制颜色代码
    return String.format("#%02X%02X%02X", red, green, blue)
  }

//  @JvmStatic
//  fun main(args: Array<String>) {
//    val parseRdsCoreSceneFile = parseRdsCoreSceneFile(File("C:\\work\\workspace\\RDSWork\\.data\\rds\\scene"))
//    val toRdsCoreSceneFile = toRdsCoreSceneFile(parseRdsCoreSceneFile)
//    println(11111)
//
//  }
}