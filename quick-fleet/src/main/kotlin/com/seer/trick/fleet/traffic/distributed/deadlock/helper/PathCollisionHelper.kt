package com.seer.trick.fleet.traffic.distributed.deadlock.helper

import com.seer.trick.fleet.traffic.distributed.context.domain.RobotContext
import com.seer.trick.fleet.traffic.distributed.lock.LockService
import com.seer.trick.fleet.traffic.distributed.lock.LockType
import com.seer.trick.fleet.traffic.distributed.lock.model.SpaceLock
import com.seer.trick.fleet.traffic.distributed.lock.move.SpaceLockCalculate
import com.seer.trick.fleet.traffic.distributed.map.Point
import com.seer.trick.fleet.traffic.distributed.plan.model.PathAction
import org.slf4j.LoggerFactory

/**
 *  解死锁新路径碰撞检测辅助类
 * */
object PathCollisionHelper {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 检测机器人在某一个码点上，是否会和别的机器人发生碰撞
   * */
  fun staticPathCollision(robot: RobotContext, point: Point, spaceLocks: MutableList<SpaceLock>): <PERSON><PERSON>an {
    // 构建锁闭信息
    val spaceLock0 = SpaceLockCalculate
      .robotStaticSpaceLock(robot.robotName, robot.sceneId, robot.groupName, 0, point, robot.mapName)
    if (spaceLock0 != null) {
      val blocks = checkCollision(spaceLock0, robot.sceneId, robot.mapName, spaceLocks)
      if (blocks.isNotEmpty()) {
        logger.warn("${robot.robotName} in ${point.name} collision, space lock info：${blocks.map { it.getKey() }}")
        return true
      }
    }
    val spaceLock9000 = SpaceLockCalculate
      .robotStaticSpaceLock(robot.robotName, robot.sceneId, robot.groupName, 9000, point, robot.mapName)
    if (spaceLock9000 != null) {
      val blocks = checkCollision(spaceLock9000, robot.sceneId, robot.mapName, spaceLocks)
      if (blocks.isNotEmpty()) {
        logger.warn("${robot.robotName} in ${point.name} collision, space lock info：${blocks.map { it.getKey() }}")
        return true
      }
    }
    return false
  }

  /**
   *  检测一段距离的锁闭是否会和别的机器人发生碰撞
   * */
  fun pathCollision(sceneId: String, action: PathAction, spaceLocks: MutableList<SpaceLock>): Boolean {
    val spaceLock = SpaceLockCalculate.moveSpaceLock(action)
    val blocks = checkCollision(spaceLock, sceneId, action.mapName, spaceLocks)
    return blocks.isNotEmpty()
  }

  /**
   *  检测一段距离的锁闭发生碰撞的机器人信息
   * */
  fun pathCollisionRobot(sceneId: String, action: PathAction?): String? {
    if (action == null) return null
    val spaceLock = SpaceLockCalculate.moveSpaceLock(action)
    val collisions = checkCollision(spaceLock, sceneId, action.mapName, mutableListOf())
    for (collision in collisions) {
      if (collision.type == LockType.ROBOT) {
        return collision.name
      }
    }
    return null
  }

  private fun checkCollision(
    spaceLock: SpaceLock,
    sceneId: String,
    mapName: String,
    spaceLocks: MutableList<SpaceLock>,
  ): MutableList<SpaceLock> {
    val blocks = checkCollision(spaceLock, sceneId, mapName)
    if (blocks.isNotEmpty()) {
      return blocks
    }
    if (spaceLocks.isNotEmpty()) {
      for (space in spaceLocks) {
        if (space.checkCollision(spaceLock)) {
          blocks.add(space)
        }
      }
    }
    return blocks
  }

  private fun checkCollision(spaceLock: SpaceLock, sceneId: String, mapName: String): MutableList<SpaceLock> {
    val locks = LockService.queryByBounding(spaceLock.box, sceneId, mapName)
    // 初次检测，帅选需要真正做碰撞检测的
    val block: MutableList<SpaceLock> = mutableListOf()
    for (l in locks) {
      if (spaceLock.checkCollision(l)) {
        block.add(l)
      }
    }
    return block
  }
}