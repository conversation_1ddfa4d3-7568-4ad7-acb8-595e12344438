package com.seer.trick.fleet.seer

import com.seer.trick.BzError
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.service.RobotGroupService
import com.seer.trick.fleet.service.SceneFileService
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonFileHelper
import com.seer.trick.helper.JsonHelper
import org.apache.commons.compress.archivers.examples.Expander
import org.apache.commons.io.FileUtils
import java.io.File
import java.nio.file.Paths

/**
 * RDS CORE 场景转 M4 场景
 */
class RdsCoreSceneToM4SceneConverter(private val sceneId: String, rdsCoreSceneZipFile: File) {

  /**
   * 解压后的目录
   */
  private val rdsCoreDir: File = SceneFileService.getSceneRdsCoreDir(sceneId)

  private val m4MapsDir = SceneFileService.getSceneRobotMapsDir(sceneId)

  private val rdsCoreScene: RdsCoreScene

  /**
   * M4 标签列表
   */
  private val robotTags: MutableList<RobotTag> = ArrayList()

  /**
   * 机器人的标签列表 robot name -> tags（标签名）
   */
  private val robotNameToTags: MutableMap<String, MutableSet<String>> = HashMap()

  /**
   * 机器人名到机器人组 ID
   */
  private val robotNameToGroupId: MutableMap<String, Int> = HashMap()

  /**
   * 机器人组。group id -> group
   */
  private val robotGroups: MutableMap<Int, RobotGroup> = HashMap()

  /**
   * 机器人
   */
  private val robots: MutableList<SceneRobot> = ArrayList()

  /**
   * 地图区域
   */
  private val areas: MutableList<SceneArea> = ArrayList()

  init {
    rdsCoreDir.mkdirs()
    FileUtils.cleanDirectory(rdsCoreDir) // 清空！

    Expander().expand(rdsCoreSceneZipFile, rdsCoreDir)
    // FileHelper.unzipFileToDir(rdsCoreSceneZipFile, rdsCoreDir)

    val rdsCoreSceneFile = File(rdsCoreDir, "rds.scene")

    rdsCoreScene = JsonFileHelper.readJsonFromFile(rdsCoreSceneFile) ?: throw BzError("errNoRdsScene")

    processRobotTags()
    processRobotGroups()
    processAreas()
  }

  /**
   * 产生 M4 场景
   */
  fun build(): SceneStructure {
    val ss = SceneStructure(
      robotGroups = robotGroups.values.toList(),
      robots = robots,
      robotTags = robotTags,
      areas = areas,
    )
    return ss
  }

  /**
   * 处理机器人标签
   */
  private fun processRobotTags() {
    val rdsCoreLabels = rdsCoreScene.labels ?: return
    for (rdsCoreLabel in rdsCoreLabels) {
      robotTags += RobotTag(rdsCoreLabel.name)
      if (rdsCoreLabel.robotIds != null) {
        for (robotName in rdsCoreLabel.robotIds) {
          // 机器人的标签
          robotNameToTags.getOrPut(robotName) { mutableSetOf() }.add(rdsCoreLabel.name)
        }
      }
    }
  }

  /**
   * 处理机器人组
   */
  private fun processRobotGroups() {
    val rdsCoreRobotGroups = rdsCoreScene.robotGroup
    rdsCoreRobotGroups.forEachIndexed { gi, rdsCoreRobotGroup ->
      // 让组 index 做组 id
      if (!rdsCoreRobotGroup.robot.isNullOrEmpty()) {
        for (r in rdsCoreRobotGroup.robot) {
          val robotName = r.id
          val tags = robotNameToTags[robotName] ?: emptySet()
          val ip = r.property?.find { it.key == "ip" }?.stringValue ?: ""
          robots.add(
            SceneRobot(
              robotName = robotName,
              groupId = gi,
              connectionType = RobotConnectionType.FleetToRobot,
              tags = tags.toMutableList(),
              robotIp = ip,
            ),
          )
          robotNameToGroupId[robotName] = gi
        }
      }

      // 取第一个机器人的底盘形状作为该组的碰撞模型
      var collisionModel: RobotCollisionModel = rdsCoreRobotGroup.robot?.firstOrNull()?.let { parseCollisionModel(it) }
        ?: RobotGroupService.defaultCollisionModel
      // 为空，取默认
      if (collisionModel.isEmpty()) collisionModel = RobotGroupService.defaultCollisionModel

      robotGroups[gi] = RobotGroup(id = gi, name = rdsCoreRobotGroup.name, collisionModel = collisionModel)
    }
  }

  /**
   * 获取机器人碰撞模型
   * 取组内的第一个机器人的底盘形状作为该组的碰撞模型
   */
  private fun parseCollisionModel(rdsCoreRobot: RdsCoreRobot): RobotCollisionModel? {
    val modelFile = Paths.get(rdsCoreDir.absolutePath, "robots", rdsCoreRobot.id, "models", "robot.model").toFile()
    if (!modelFile.exists()) return null

    val jn = JsonHelper.mapper.readTree(modelFile)

    val params = jn["deviceTypes"]?.asIterable() // deviceTypes 是 JSON 对象数组
      ?.find { it["name"]?.asText() == "chassis" }
      ?.get("devices")?.asIterable() // devices 是 JSON 对象数组
      ?.find { it["name"]?.asText() == "chassis" }
      ?.get("deviceParams")?.asIterable() // deviceParams 是 JSON 对象数组
      ?.find { it["key"]?.asText() == "shape" }
      ?.get("comboParam")
      ?.get("childParams")?.asIterable() // childParams 是 JSON 对象数组
      // TODO 这里只解析矩形底盘，还有其他形状的底盘，如圆形
      ?.find { it["key"]?.asText() == "rectangle" }
      ?.get("params")?.asIterable() // params 是 JSON 对象数组
      ?: return null

    val width = params.find { it["key"]?.asText() == "width" }?.get("doubleValue")?.asDouble() ?: return null
    val head = params.find { it["key"]?.asText() == "head" }?.get("doubleValue")?.asDouble() ?: return null
    val tail = params.find { it["key"]?.asText() == "tail" }?.get("doubleValue")?.asDouble() ?: return null

    val polygon = Polygon(
      points = listOf(
        Point2D(x = -tail, y = width / 2),
        Point2D(x = head, y = width / 2),
        Point2D(x = head, y = -width / 2),
        Point2D(x = -tail, y = -width / 2),
      ),
    )
    return RobotCollisionModel(shapes = listOf(RobotCollisionShape(polygon = polygon)), bound = polygon)
  }

  /**
   * 处理区域
   */
  private fun processAreas() {
    val rdsCoreAreas = rdsCoreScene.areas
    rdsCoreAreas.forEachIndexed { ai, rdsCoreArea ->
      val rdsCoreMaps = rdsCoreArea.maps // CORE 的地图是每个机器人一份
      val groupsMap: MutableMap<Int, RobotAreaMapRecord> = mutableMapOf() // 每个机器人组对应的地图

      for (rdsCoreMap in rdsCoreMaps) {
        val robotName = rdsCoreMap.robotId
        val groupId = robotNameToGroupId[robotName] ?: continue

        // 一个机器人组下的机器人只取一次
        if (groupsMap.containsKey(groupId)) continue

        // smap 解压后的路径
        val smapFile = Paths.get(rdsCoreDir.absolutePath, "robots", robotName, "maps", rdsCoreMap.mapName).toFile()
        if (!smapFile.exists()) continue

        // 防止出现同名覆盖，重新生成地图文件名
        val mapName = "${IdHelper.oidStr()}.smap"
        val smapNewFile = File(m4MapsDir, mapName)
        // 拷贝到 M4 机器人地图目录下
        FileUtils.copyFile(smapFile, smapNewFile)
        val smapPath = SceneFileService.fileToPath(sceneId, smapNewFile)

        groupsMap[groupId] = RobotAreaMapRecord(
          mapName = rdsCoreMap.mapName,
          mapMd5 = rdsCoreMap.md5,
          mapFile = smapPath,
        )
      }

      areas.add(
        SceneArea(
          id = ai,
          name = rdsCoreArea.name,
          groupsMap = groupsMap,
        ),
      )
    }
  }
}