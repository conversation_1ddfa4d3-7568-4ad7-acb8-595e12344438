package com.seer.trick.fleet.map

import com.seer.trick.fleet.domain.SceneArea
import com.seer.trick.fleet.seer.SmapHelper

/**
 * 运行时，表示一个区域。
 * 不可变对象。
 */
class SceneAreaCache(val sceneId: String, val schema: SceneArea) {

  /**
   * 各个机器人组合并后的区域地图
   */
  val mergedMap = AreaMapCache(schema.id, schema.mergedMap, "")

  /**
   * 各个机器人组自己的区域地图
   */
  val groupedMaps: MutableMap<Int, AreaMapCache> = HashMap()

  init {
    // 对每个机器人组，加载 smap，并转换为区域地图，构建 AreaMapRuntime
    for ((gId, gm) in schema.groupsMap) {
      var areaMap = schema.gmMap[gId] ?: continue
      areaMap = SmapHelper.patchSmapByScript(sceneId, schema.id, schema.name, gId, gm.mapName, areaMap)
      groupedMaps[gId] = AreaMapCache(schema.id, areaMap, gm.mapName)
    }
  }
}