package com.seer.trick.fleet.traffic.distributed.lock.model

import com.seer.trick.fleet.traffic.distributed.lock.graph.Shape

/**
 *  层 的概念
 * */
class Layer(
  val id: Int, // 第几层
  val top: Double, // 层上 高度
  val bottom: Double, // 层底 高度
  var shape: Shape, // 形状
) {

  fun copy(): Layer {
    val s = shape.copy()
    return Layer(id, top, bottom, s)
  }

  override fun toString(): String = "Layer(id=$id, top=$top, bottom=$bottom, shape=$shape)"
}