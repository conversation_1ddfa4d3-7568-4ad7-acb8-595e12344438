package com.seer.trick.fleet.algorithm

import java.util.*

object KuhnMunkresImpl {
  // 定义 "无效匹配" 的标记值
  const val INFINITY = Double.MAX_VALUE

  /**
   * 使用 Kuhn-Munkres (匈牙利) 算法解决指派问题。
   *
   * @param costMatrix 指派问题的成本矩阵，其中 costMatrix[i][j]表示将工人 i 分配到工作 j 的成本。
   * @return 一个数组，其中第 i 个元素是分配给第 i 个工人的工作的索引。
   */
  fun solve(costMatrix: Array<IntArray>): IntArray {
    val n = costMatrix.size // 工人数
    val m = costMatrix[0].size // 工作数
    val size = maxOf(n, m) // 扩展为方阵的大小

    // 扩展成本矩阵（初始化为 INFINITY，用实际值覆盖）
    val extendedCostMatrix = Array(size) { i ->
      DoubleArray(size) { j ->
        if (i < n && j < m && costMatrix[i][j] != -1) {
          costMatrix[i][j].toDouble()
        } else {
          INFINITY // 标记不连通
        }
      }
    }

    // 双向潜在值（列和行分别的潜在值）
    val u = DoubleArray(size) // 行潜在值
    val v = DoubleArray(size) // 列潜在值
    val p = IntArray(size) { -1 } // 匹配结果记录，p[j] 表示第 j 列匹配的有序行索引

    // 遍历每个行节点，寻找对应的最优匹配
    for (i in 0 until size) {
      // 如果原始矩阵的行全为不可连通（-1），跳过该行处理
      if (i < n && costMatrix[i].all { it == -1 }) continue

      val links = IntArray(size) { -1 } // 链接记录，每个列节点指向的行节点
      val mins = DoubleArray(size) { INFINITY } // 最小潜在值
      val visited = BooleanArray(size) // 访问记录，标记列节点是否已访问

      var markedI = i
      var markedJ = -1

      while (true) {
        var j = -1

        // 寻找增广路径：找到最小潜在值路径
        for (j1 in 0 until size) {
          if (!visited[j1]) {
            // 当前潜在值计算
            val cur = extendedCostMatrix[markedI][j1] - u[markedI] - v[j1]
            if (cur < mins[j1]) {
              mins[j1] = cur
              links[j1] = markedJ // 记录前继
            }
            // 找到最小潜在值的列节点
            if (j == -1 || mins[j1] < mins[j]) {
              j = j1
            }
          }
        }

        // 处理 delta：如果 delta == INFINITY，说明已无可行路径
        val delta = mins[j]
        if (delta == INFINITY) break // 无法匹配，退出循环

        // 更新潜在值
        for (j1 in 0 until size) {
          if (visited[j1]) {
            u[p[j1]] += delta
            v[j1] -= delta
          } else {
            mins[j1] -= delta
          }
        }
        u[i] += delta

        visited[j] = true // 标记当前列节点为已访问
        markedJ = j
        markedI = p[j]

        // 如果未匹配到新的行节点，跳出循环（找到路径终点）
        if (markedI == -1) break
      }

      // 更新增广路径上的匹配关系
      while (markedJ != -1 && links[markedJ] != -1) {
        p[markedJ] = p[links[markedJ]]
        markedJ = links[markedJ]
      }
      if (markedJ != -1) {
        p[markedJ] = i
      }
    }

    // 提取最终结果，转换为原始矩阵大小
    val result = IntArray(n) { -1 }
    for (j in 0 until m) {
      if (p[j] != -1 && p[j] < n && extendedCostMatrix[p[j]][j] != INFINITY) {
        result[p[j]] = j
      }
    }
    return result
  }
}

fun main() {
  val algorithm = KuhnMunkresImpl

  // 创建一个500x500的成本矩阵

  // 耗时
  val start = System.currentTimeMillis()
  // 创建一个成本矩阵
  val lsize = 100
  val rsize = 100
  val costs = Array(lsize) { IntArray(rsize) }
  val rand = Random()
  for (i in 0 until lsize) {
    for (j in 0 until rsize) {
      costs[i][j] = 1 + rand.nextInt(100)
    }
  }
  costs.print()

//  val costMatrix = arrayOf(
//    intArrayOf(4, 1, -1),
//    intArrayOf(2, 0, 3),
//    intArrayOf(-1, 6, 4),
//  )
// //  val costMatrix = arrayOf(
// //    intArrayOf(-1, 2, -1),
// //    intArrayOf(-1, -1, -1),
// //  )
  val result = algorithm.solve(costs)
  result.print()
  val end = System.currentTimeMillis()
  println("耗时: ${end - start}")
}