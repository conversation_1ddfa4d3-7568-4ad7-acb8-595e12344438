package com.seer.trick.fleet.traffic.venus

import com.seer.trick.fleet.domain.GeoHelper
import com.seer.trick.fleet.domain.GeoHelper.generateRotateSteps
import com.seer.trick.fleet.domain.GeoHelper.translate
import com.seer.trick.fleet.domain.MapPath
import com.seer.trick.fleet.domain.MoveDirection
import com.seer.trick.fleet.domain.Polygon
import com.seer.trick.fleet.domain.Pose2D
import com.seer.trick.fleet.domain.RobotLoadCollisionModel
import com.seer.trick.fleet.domain.RobotStand
import com.seer.trick.fleet.domain.normalizeRadian
import com.seer.trick.fleet.domain.reverseAzimuth
import com.seer.trick.fleet.service.RobotLoadCollisionService
import com.seer.trick.fleet.traffic.venus.cache.RobotShapeCache
import kotlin.math.min

object ResConflictManager {

  /**
   * 返回整个高层节点所有机器人的冲突情况：冲突数和最早的约束。返回的 map 有两个元素。每个表示参与约束的一个机器人。
   * 注意两个机器人之间目前可能有多于一个约束，但是先返回最早的。
   */
  fun getConflictNumAndEarliest(
    robots: Map<String, RobotPlanContext>,
    o: PathFindingOption,
  ): Pair<Int, Map<String, RobotConstraint>?> {
    val conflictPairs = mutableSetOf<Pair<String, String>>() // 去重后的冲突机器人对
    var constraints: Map<String, RobotConstraint>? = null
    var earliestTime = Long.MAX_VALUE // 出现冲突的最早时间

    // 只检查动的机器人
    val movedRobotNames = robots.filter { it.value.moved }.keys.toList()

    for (r1i in movedRobotNames.indices) {
      val robotName1 = movedRobotNames[r1i]
      val robotCtx1 = robots[robotName1] ?: continue
      val sol1 = robotCtx1.solution ?: continue

      val remainingMovedRobots = movedRobotNames.subList(r1i + 1, movedRobotNames.size)

      for (s1i in 0 until sol1.path.size) {
        val s1a = if (s1i == 0) null else sol1.path[s1i - 1] // 上一个状态
        val s1b = sol1.path[s1i] // 当前状态

//        if (o.window!= null && !s1b.isWithinWindow(o.window)) break

        val r = getConflictNumAndEarliest(
          robotName1,
          s1a,
          s1b,
          robots,
          remainingMovedRobots,
          o,
          conflictPairs,
        )

        if (r.second < earliestTime && r.third != null) {
          constraints = r.third
          earliestTime = r.second
        }
      }
    }
    return Pair(conflictPairs.size, constraints)
  }

  /**
   * 只处理单个机器人的单个状态。
   * 返回冲突数和最早的约束。返回的 map 有两个元素。每个表示参与约束的一个机器人。
   * 注意两个机器人之间目前可能有多于一个约束，但是先返回最早的。
   */
  fun getConflictNumAndEarliest(
    robotName1: String, // robotName1 是本轮发生移动的机器人
    s1a: State?,
    s1b: State,
    robots: Map<String, RobotPlanContext>,
    remainingMovedRobots: List<String>, // 检查任务冲突时，只与这些机器人查找冲突
    o: PathFindingOption,
    conflictPairs: MutableSet<Pair<String, String>>? = null, // 可选去重集合
  ): Triple<Int, Long, Map<String, RobotConstraint>?> {
    var conflictNum = 0
    var constraints: Map<String, RobotConstraint>? = null
    var earliestTime = Long.MAX_VALUE // 出现冲突的最早时间

    // 与有任务机器人的冲突
    for (robotName2 in remainingMovedRobots) {
      if (robotName1 == robotName2) continue

      val robotCtx2 = robots[robotName2] ?: continue
      val sol2 = robotCtx2.solution ?: continue

      for (s2i in 0 until sol2.path.size) {
        val s2a = if (s2i == 0) null else sol2.path[s2i - 1] // 上一个状态
        val s2b = sol2.path[s2i] // 当前状态
        val minTime = min(s1b.timeStart, s2b.timeStart)

        val cs = getConstraints(robotName1, robotName2, s1a, s1b, s2a, s2b, robots, o)
        if (!cs.isNullOrEmpty()) {
          if (conflictPairs != null) {
            val pair = if (robotName1 < robotName2) robotName1 to robotName2 else robotName2 to robotName1
            if (conflictPairs.add(pair)) ++conflictNum
          } else {
            ++conflictNum
          }
          if (minTime < earliestTime) {
            earliestTime = minTime
            constraints = cs
          }
          continue
        }
      }
    }

    // 判断是否与其他机器人的最终停靠位置冲突
    for ((robotName2, ctx) in robots) {
      if (robotName1 == robotName2) continue

      val cs = getStationaryConstraints(
        robotName1,
        s1b,
        robotName2,
        ctx.solution?.path?.last(),
        ctx.request.robotInfo.stand,
        o,
      )
      if (!cs.isNullOrEmpty()) {
        if (conflictPairs != null) {
          val pair = if (robotName1 < robotName2) robotName1 to robotName2 else robotName2 to robotName1
          if (conflictPairs.add(pair)) ++conflictNum
        } else {
          ++conflictNum
        }
        if (s1b.timeStart < earliestTime) {
          earliestTime = s1b.timeStart
          constraints = cs
        }
      }
    }

    return Triple(conflictNum, earliestTime, constraints)
  }

  /**
   * 计算指定机器人新状态的新增冲突
   */
  fun countConflicts(
    robotName1: String,
    s1a: State?,
    s1b: State,
    robots: Map<String, RobotPlanContext>,
    o: PathFindingOption,
  ): Int {
    val r = getConflictNumAndEarliest(robotName1, s1a, s1b, robots, robots.keys.toList(), o)
    return r.first
  }

  /**
   * 状态转移是否违背约束。0: 有效
   */
  fun isValidNeighbor(
    robotName: String,
    fromState: State?,
    toState: State,
    robotConstraints: List<RobotConstraint>,
    o: PathFindingOption,
  ): RobotConstraint? {
    for (c in robotConstraints) {
      val r = c.isValidNeighbor(fromState, toState, o)
      if (r != null) return r
    }
    return null
  }

  /**
   * 将给机器人添加新约束
   */
  fun addConstraints(
    allRobotsConstraints: Map<String, List<RobotConstraint>>,
    toRobotName: String,
    newConstraint: RobotConstraint,
  ): Map<String, List<RobotConstraint>> {
    val oldRobotConstraints = allRobotsConstraints[toRobotName]
    val newRobotConstraints = if (oldRobotConstraints.isNullOrEmpty()) {
      listOf(newConstraint)
    } else {
      oldRobotConstraints + newConstraint
    }

    val newAllRobotsConstraints: MutableMap<String, List<RobotConstraint>> = HashMap(allRobotsConstraints)
    newAllRobotsConstraints[toRobotName] = newRobotConstraints

    return newAllRobotsConstraints
  }

  // 一个机器人的状态与另一个机器人的状态的冲突
  private fun getConstraints(
    robotName1: String,
    robotName2: String,
    s1a: State?,
    s1b: State,
    s2a: State?,
    s2b: State,
    robots: Map<String, RobotPlanContext>,
    o: PathFindingOption,
  ): Map<String, RobotConstraint>? {
    if (robotName1 == robotName2 || !s1b.isTimeOverlay(s2b)) return null

//    // 去往同一位置
//    if (s1b.isSameLocation(s2b)) {
//      if (s1b.toPosition.type == RoutePositionType.Point && s2b.toPosition.type == RoutePositionType.Point) {
//        // 去往同一点位
//        return mapOf(
//          robotName1 to RobotConstraint(
//            robotName2,
//            source = "Point/Point",
//            s2b.timeStart,
//            s2b.timeEnd,
//            type = ResConstraintType.Point,
//            pointName = s2b.toPosition.pointName,
//          ),
//          robotName2 to RobotConstraint(
//            robotName1,
//            source = "Point/Point",
//            s1b.timeStart,
//            s1b.timeEnd,
//            type = ResConstraintType.Point,
//            pointName = s1b.toPosition.pointName,
//          ),
//        )
//      } else if (s1b.toPosition.type == RoutePositionType.Path && s2b.toPosition.type == RoutePositionType.Path) {
//        // 使用同一路径
//        return mapOf(
//          robotName1 to RobotConstraint(
//            robotName2,
//            source = "Path/Path",
//            s2b.timeStart,
//            s2b.timeEnd,
//            type = ResConstraintType.Path,
//            pathKey = s2b.toPosition.pathKey,
//          ),
//          robotName2 to RobotConstraint(
//            robotName1,
//            source = "Path/Path",
//            s1b.timeStart,
//            s1b.timeEnd,
//            type = ResConstraintType.Path,
//            pathKey = s1b.toPosition.pathKey,
//          ),
//        )
//      } else {
//        // 后续按空间检查
//      }
//    }
//
//    // 使用相同路径
//    if (s1b.type == StateType.Move && s2b.type == StateType.Move) {
//      if (s1b.byPathKey != null && s1b.byPathKey == s2b.byPathKey) {
//        // 占用同一路径
//        return mapOf(
//          robotName1 to RobotConstraint(
//            robotName2,
//            source = "Path/Path-2",
//            s2b.timeStart,
//            s2b.timeEnd,
//            type = ResConstraintType.Path,
//            pathKey = s2b.byPathKey,
//          ),
//          robotName2 to RobotConstraint(
//            robotName1,
//            source = "Path/Path-2",
//            s1b.timeStart,
//            s1b.timeEnd,
//            type = ResConstraintType.Path,
//            pathKey = s1b.byPathKey,
//          ),
//        )
//      } else if (s1a != null && s2a != null && s1b.isSameLocation(s2a) && s2b.isSameLocation(s1a)) {
//        // 经过一个位置，交换位置 TODO 暂不考虑 s1a, s2a 是路径上的点
//        return mapOf(
//          robotName1 to RobotConstraint(
//            robotName2,
//            source = "Path/Path-3",
//            s2a.timeStart,
//            s2b.timeEnd,
//            type = ResConstraintType.Path,
//            pathKey = s1b.byPathKey, // 不要用 s2b
//          ),
//          robotName2 to RobotConstraint(
//            robotName1,
//            source = "Path/Path-3",
//            s1a.timeStart,
//            s1b.timeEnd,
//            type = ResConstraintType.Path,
//            pathKey = s2b.byPathKey, // 不要用 s1b
//          ),
//        )
//      }
//    }

    // TODO 一个 Move + 一个 Wait

    if (s1b.spatialIntersecting(s2b)) {
      // 对于碰撞检测添加的冲突，应该是对方自己的路径吧
      return mapOf(
        robotName1 to RobotConstraint(
          robotName2,
          source = "Spatial-1",
          s2b.timeStart,
          s2b.timeEnd,
          type = ResConstraintType.Spatial,
          shapes = s2b.shapes,
          spatialDesc = descSpatial(s2b),
        ),
        robotName2 to RobotConstraint(
          robotName1,
          source = "Spatial-1",
          s1b.timeStart,
          s1b.timeEnd,
          type = ResConstraintType.Spatial,
          shapes = s1b.shapes,
          spatialDesc = descSpatial(s1b),
        ),
      )
    }

    return null
  }

  // 一个机器人的状态与另一个机器人的停靠的冲突
  private fun getStationaryConstraints(
    robotName1: String,
    s1b: State,
    robotName2: String,
    finalState: State?,
    stand: RobotStand,
    o: PathFindingOption,
  ): Map<String, RobotConstraint>? {
    if (robotName1 == robotName2) return null
    // 先检查与移动了的机器人的最终停靠位置是否冲突
    if (finalState != null) {
      if (!finalState.isTimeOverlay(s1b)) return null

      // // 去往同一位置
      // if (s1b.toPosition.type == RoutePositionType.Point &&
      //   finalState.toPosition.pointName != null &&
      //   s1b.toPosition.pointName == finalState.toPosition.pointName
      // ) {
      //   // 去往同一点位
      //   return mapOf(
      //     robotName1 to RobotConstraint(
      //       robotName2,
      //       source = "FinalPoint",
      //       type = ResConstraintType.Point,
      //       timeStart = finalState.timeStart,
      //       timeEnd = finalState.timeEnd,
      //       pointName = s1b.toPosition.pointName,
      //     ),
      //   )
      // } else {
      //   // 后续按空间检查
      // }

      // 在计算一个机器人的状态与另一个机器人的停靠的冲突，没有去除不在同一区域的情况
      val robotCmr = finalState.shapes
      if (robotCmr != null &&
        s1b.spatialIntersecting(
          robotCmr,
        )
      ) {
        if (!finalState.toPosition.pointName.isNullOrBlank()) {
          return mapOf(
            robotName1 to RobotConstraint(
              robotName2,
              source = "FinalSpatial",
              type = ResConstraintType.Spatial,
              timeStart = finalState.timeStart,
              timeEnd = finalState.timeEnd,
              pointName = finalState.toPosition.pointName,
              shapes = robotCmr,
              spatialDesc = "Stationary spatial by point ${finalState.toPosition.pointName}",
            ),
          )
        } // 暂不考虑目标点不是点位的情况
      }
    } else {
      // 下面是原地不动的机器人

      val robotCmr = stand.collisionShape
      // // 去往同一位置
      // if (s1b.toPosition.type == RoutePositionType.Point &&
      //   stand.pointName != null &&
      //   s1b.toPosition.pointName == stand.pointName
      // ) {
      //   // 去往同一点位
      //   return mapOf(
      //     robotName1 to RobotConstraint(
      //       robotName2,
      //       source = "StationaryPoint",
      //       type = ResConstraintType.Point,
      //       timeStart = 0, // s1b.timeStart,
      //       timeEnd = -1, // s1b.timeEnd,
      //       pointName = s1b.toPosition.pointName,
      //     ),
      //     robotName2 to RobotConstraint(
      //       robotName1,
      //       source = "StationaryPoint",
      //       type = ResConstraintType.Point,
      //       timeStart = 0, // s1b.timeStart,
      //       timeEnd = -1, // s1b.timeEnd,
      //       pointName = s1b.toPosition.pointName,
      //     ),
      //   )
      // } else {
      //   // 后续按空间检查
      // }

      // 碰撞检测
      if (robotCmr != null && s1b.spatialIntersecting(robotCmr)) {
        if (!stand.pointName.isNullOrBlank()) {
          return mapOf(
            robotName1 to RobotConstraint(
              robotName2,
              source = "StationaryPointSpatial",
              type = ResConstraintType.Spatial,
              timeStart = 0, // s1b.timeStart,
              timeEnd = -1, // s1b.timeEnd,
              pointName = stand.pointName,
              shapes = PolygonsWithBBox(listOf(robotCmr)),
              spatialDesc = "Stationary spatial by point ${stand.pointName}",
            ),
            robotName2 to RobotConstraint(
              robotName1,
              source = "StationaryPointSpatial",
              type = ResConstraintType.Spatial,
              timeStart = 0, // s1b.timeStart,
              timeEnd = -1, // s1b.timeEnd,
              pointName = stand.pointName,
              shapes = PolygonsWithBBox(listOf(robotCmr)),
              spatialDesc = "Stationary spatial by point ${stand.pointName}",
            ),
          )
        } else if (!stand.pathPositions.isNullOrEmpty()) {
          for (cp in stand.pathPositions) {
            return mapOf(
              robotName1 to RobotConstraint(
                robotName2,
                source = "StationaryPathSpatial",
                type = ResConstraintType.Spatial,
                timeStart = 0,
                timeEnd = -1,
                pathKey = cp.pathKey,
                shapes = PolygonsWithBBox(listOf(robotCmr)), // TODO 扩展到整个路径？
                spatialDesc = "Stationary spatial by path ${cp.pathKey}",
              ),
              robotName2 to RobotConstraint(
                robotName1,
                source = "StationaryPathSpatial",
                type = ResConstraintType.Spatial,
                timeStart = 0,
                timeEnd = -1,
                pathKey = cp.pathKey,
                shapes = PolygonsWithBBox(listOf(robotCmr)), // TODO 扩展到整个路径）
                spatialDesc = "Stationary spatial by path ${cp.pathKey}",
              ),
            )
          }
        }
      }
    }

    return null
  }

  /**
   * 将某机器人已确定的路径转换为约束，用于 PBS：
   * 这里采取保守做法，将每个状态的占用空间转换为 Spatial 约束，
   * 时间区间 [timeStart, timeEnd]，避免其他低优先级机器人占用。
   * 可根据需要优化（如只转换前若干步、仅关键点等）。
   */
  fun pathToConstraints(robotName: String, path: List<State>, o: PathFindingOption): List<RobotConstraint> {
    val list = mutableListOf<RobotConstraint>()
//    val newPath = if(o.window == null){
//      path
//    }else{
//      path.trimToWindow(o.window)
//    }

    for (s in path) {
      if (s.shapes?.polys.isNullOrEmpty()) continue
      list += RobotConstraint(
        masterRobotName = robotName,
        source = "PBSHigh",
        timeStart = s.timeStart,
        timeEnd = s.timeEnd,
        type = ResConstraintType.Spatial,
        shapes = s.shapes,
        spatialDesc = "PBS high path segment,${s.toPosition.pointName}",
      )
    }
    return list
  }

  /**
   * 从 initPath 生成初始约束。将每个机器人已下发/锁定的 initPath 段转换为其他机器人的初始约束。
   */
  fun buildInitPathConstraints(requests: Map<String, RobotPlanRequest>): Map<String, List<RobotConstraint>> {
    val allConstraints = mutableMapOf<String, MutableList<RobotConstraint>>()

    for ((robotName, _) in requests) {
      allConstraints[robotName] = mutableListOf()
    }

    for ((masterName, robotReq) in requests) {
      val initPath = robotReq.initPath
      if (initPath.isNullOrEmpty()) continue

      for (state in initPath) {
        val (tStart, tEnd) = state.timeStart to state.timeEnd

        val c = RobotConstraint(
          masterName,
          source = "InitPath",
          timeStart = tStart,
          timeEnd = tEnd,
          type = ResConstraintType.InitPath, // 使用 InitPath 约束类型
          shapes = state.shapes,
          spatialDesc = descSpatial(state),
        )
        // 只加到其他机器人
        for ((otherName, _) in requests) {
          if (otherName != masterName) {
            allConstraints[otherName]?.add(c)
          }
        }
      }
    }

    return allConstraints.mapValues { it.value.toList() }
  }

  fun descSpatial(s: State): String = if (s.type == StateType.Move) {
    "Spatial by move ${s.byPathKey}"
  } else {
    "Spatial by point ${s.toPosition.pointName}"
  }

  /**
   * 机器人在点位上等待所需的空间
   */
  fun buildVertexSpace(robotCollisionModel: Polygon, x: Double, y: Double, theta: Double): List<Polygon> =
    listOf(translate(RobotShapeCache.getRotated(robotCollisionModel, theta), x, y))

  /**
   * 机器人从起始点位移动带目标点位所需的空间
   */
  fun buildEdgeSpace(
    robotCollisionModel: RobotLoadCollisionModel,
    fromX: Double,
    fromY: Double,
    robotStartTheta: Double,
    robotEnterTheta: Double,
    path: MapPath,
    minor: Boolean,
    moveDirection: MoveDirection,
    loadStartTheta: Double? = null,
  ): List<Polygon> {
    val shapes = mutableListOf<Polygon>()
    // 构建旋转资源

    val minTracePoint = path.tracePoints.minBy {
      GeoHelper.euclideanDistance(it.x, it.y, fromX, fromY)
    }
    val rotateSteps = generateRotateSteps(
      robotStartTheta.normalizeRadian(),
      if (moveDirection == MoveDirection.Forward) {
        minTracePoint.tangent.normalizeRadian()
      } else {
        minTracePoint.tangent.reverseAzimuth().normalizeRadian()
      },
      Math.toRadians(10.0),
      minor,
    )

    for (a in rotateSteps) {
      shapes += RobotLoadCollisionService.buildCollisionShape(
        robotCollisionModel,
        Pose2D(
          minTracePoint.x - 0.0,
          minTracePoint.y - 0.0,
          a,
        ),
        if (loadStartTheta != null) {
          loadStartTheta + a - robotStartTheta
        } else {
          loadStartTheta
        },
      )
    }
    path.tracePoints.indexOf(minTracePoint)
    val indexOf = path.tracePoints.indexOf(minTracePoint)
    // 路径空间，每 10 个点
    // 不能直接按照路径的点点弧度，因为正走倒走的路径模型是不一样的
    val tangentOffset = when (moveDirection) {
      MoveDirection.Backward -> Math.PI
      else -> 0.0
    }

    val totalPoints = path.tracePoints.size
    // 自适应采样：目标采样点数 ≈ 5% 总点数，限制在 [3, 25]，并向上取整保持 ≤ sampleCnt
    val desiredSamples = (totalPoints * 15 + 99) / 100 // 等价于 ceil(totalPoints * 0.15)
    val sampleCnt = desiredSamples.coerceIn(3, 15)
    var step = (totalPoints + sampleCnt - 1) / sampleCnt // ceil(totalPoints / sampleCnt)
    if (step < 1) step = 1
    var pi = 0
    while (pi < path.tracePoints.size) {
      if (pi < indexOf) {
        pi += step
        continue
      }
      val p = path.tracePoints[pi]
      shapes += RobotLoadCollisionService.buildCollisionShape(
        robotCollisionModel,
        Pose2D(
          p.x,
          p.y,
          p.tangent + tangentOffset,
        ),
        if (loadStartTheta != null) {
          loadStartTheta + p.tangent + tangentOffset - robotStartTheta
        } else {
          loadStartTheta
        },
      )
      pi += step
    }

    // 最后一个
    val last = path.tracePoints.last()

    shapes += RobotLoadCollisionService.buildCollisionShape(
      robotCollisionModel,
      Pose2D(
        last.x,
        last.y,
        last.tangent + tangentOffset,
      ),
      if (loadStartTheta != null) {
        loadStartTheta + last.tangent + tangentOffset - robotStartTheta
      } else {
        loadStartTheta
      },
    )
    return shapes
  }

  // /**
  //  * 对于不动的机器人，将其当前位置变成静止约束
  //  */
  // fun buildStationaryConstraints(
  //   rr: RobotRuntime,
  //   noTopologyCheck: Boolean,
  //   noSpatialCheck: Boolean,
  // ): List<RobotConstraint>? {
  //   val stand = rr.selfReport?.stand ?: return null
  //
  //   val stationaryConstraints: MutableList<RobotConstraint> = mutableListOf()
  //
  //   if (!noTopologyCheck) {
  //     if (!stand.pointName.isNullOrBlank()) {
  //       stationaryConstraints += RobotConstraint(
  //         rr.robotName,
  //         timeStart = 0,
  //         timeEnd = -1,
  //         type = ResConstraintType.Point,
  //         pointName = stand.pointName,
  //       )
  //     } else if (!stand.closePaths.isNullOrEmpty()) {
  //       for (cp in stand.closePaths) {
  //         stationaryConstraints += RobotConstraint(
  //           rr.robotName,
  //           timeStart = 0,
  //           timeEnd = -1,
  //           type = ResConstraintType.Path,
  //           pathKey = cp.pathKey,
  //         )
  //       }
  //     }
  //   }
  //   val robotCmr = rr.selfReport?.stand?.collisionShape
  //   if (!noSpatialCheck && robotCmr != null) {
  //     if (!stand.pointName.isNullOrBlank()) {
  //       stationaryConstraints += RobotConstraint(
  //         rr.robotName,
  //         timeStart = 0,
  //         timeEnd = -1,
  //         type = ResConstraintType.Spatial,
  //         pointName = stand.pointName,
  //         shapes = listOf(robotCmr),
  //         spatialDesc = "Stationary spatial by point ${stand.pointName}",
  //       )
  //     } else if (!stand.closePaths.isNullOrEmpty()) {
  //       for (cp in stand.closePaths) {
  //         stationaryConstraints += RobotConstraint(
  //           rr.robotName,
  //           timeStart = 0,
  //           timeEnd = -1,
  //           type = ResConstraintType.Spatial,
  //           pathKey = cp.pathKey,
  //           shapes = listOf(robotCmr), // TODO 扩展到整个路径？
  //           spatialDesc = "Stationary spatial by path ${cp.pathKey}",
  //         )
  //       }
  //     }
  //   }
  //
  //   return stationaryConstraints
  // }
}

data class RobotConstraint(
  val masterRobotName: String, // 哪个机器人施加了这个约束
  val source: String, // 哪个规则产生了这个约束
  override val timeStart: Long, // 约束开始时间
  override val timeEnd: Long, // 约束结束时间
  val type: ResConstraintType,
  val pointName: String? = null, // 约束不能占用点
  val pathKey: String? = null, // 约束不能占用路径
  val shapes: PolygonsWithBBox? = null, // 约束不能占用空间
  val spatialDesc: String? = null, // 对空间的描述，便于 DEBUG
) : TimeSteps {

  /**
   * 移动是否有效。0 表示有效，>0 表示无效。
   */
  fun isValidNeighbor(fromState: State?, toState: State, o: PathFindingOption): RobotConstraint? {
    // 对于 InitPath 不仅不能重叠还不能在左侧
    val type = type
    if (type != ResConstraintType.InitPath &&
      !toState.isTimeOverlay(this)
    ) {
      return null
    }
    when (type) {
      ResConstraintType.Point -> {
        if (toState.toPosition.type == RoutePositionType.Point && toState.toPosition.pointName == pointName) {
          return this
        }
      }

      ResConstraintType.Path -> {
        if (toState.type == StateType.Move) {
          if (toState.byPathKey == pathKey) return this
        }
      }

      ResConstraintType.Spatial -> {
        if (shapes?.polys.isNullOrEmpty() || toState.shapes?.polys.isNullOrEmpty()) {
          println("ResConstraintType.Spatial has no shapes")
        }
        if (!shapes?.polys.isNullOrEmpty() && toState.spatialIntersecting(shapes)) {
          return this
        }
      }

      ResConstraintType.InitPath -> {
        // 如果是 InitPath 约束，必须确保开始时间在 initPath 完成后
        if (!shapes?.polys.isNullOrEmpty() &&
          (toState.timeStart <= timeEnd) &&
          toState.spatialIntersecting(shapes)
        ) { // 关键：必须在 initPath 完成后才能开始
          return this
        }
      }
    }
    return null
  }

  override fun toString(): String = "[$type][$source]" +
    "by[$masterRobotName][$timeStart:$timeEnd]${(pointName ?: "")}|${(pathKey ?: "")}|${(spatialDesc ?: "")}"
}

/**
 * 资源限制类型
 */
enum class ResConstraintType {
  Point,
  Path,
  Spatial,
  InitPath,
// 新增：表示必须等待 initPath 完成
}