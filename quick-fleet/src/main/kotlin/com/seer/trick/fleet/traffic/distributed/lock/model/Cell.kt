package com.seer.trick.fleet.traffic.distributed.lock.model

import com.fasterxml.jackson.annotation.JsonIgnore
import com.seer.trick.fleet.traffic.distributed.lock.graph.BoundingBox
import com.seer.trick.fleet.traffic.distributed.map.Position

class Cell(
  val layers: MutableList<Layer>, // 层信息
  @JsonIgnore
  var sPosition: Position, //   起始位置信息
) {

  // 外包围盒
  var box: BoundingBox = BoundingBox()

  // 位置信息
  @JsonIgnore
  var tPosition: Position? = null

  init {
    buildBoundingBox()
  }

  fun checkCollision(cell: Cell): Boolean {
    if (box.intersect(cell.box)) {
      // 计算两个cell的交集, 相同的层进行比较
      val min = layers.size.coerceAtMost(cell.layers.size)
      for (i in 0 until min) {
        val layer = layers[i]
        val cellLayer = cell.layers[i]
        if (layer.shape.intersection(cellLayer.shape)) {
          return true
        }
      }
    }
    return false
  }

  private fun buildBoundingBox() {
    for (l in layers) {
      box.updateBoundingBox(l.shape.getBoundingBox())
    }
  }

  fun updateBoundingBox() {
    box = BoundingBox()
    for (l in layers) {
      box.updateBoundingBox(l.shape.getBoundingBox())
    }
  }

  fun copy(): Cell {
    val cell = Cell(layers.map { it.copy() }.toMutableList(), sPosition)
    cell.tPosition = tPosition
    cell.box = box
    return cell
  }

  override fun toString(): String = "Cell(box=$box, sp=$sPosition, tp=$tPosition, layers=$layers)"
}