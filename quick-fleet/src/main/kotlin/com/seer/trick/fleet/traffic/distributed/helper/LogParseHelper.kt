package com.seer.trick.fleet.traffic.distributed.helper

import com.seer.trick.fleet.traffic.distributed.deadlock.model.NeighborDomain
import com.seer.trick.fleet.traffic.distributed.plan.model.PathAction
import com.seer.trick.fleet.traffic.distributed.prevent.PreventRequestInfo
import com.seer.trick.helper.JsonHelper

object LogParseHelper {

  fun parsePathLog(path: MutableList<PathAction>): String {
    val builder = StringBuilder()
    for (p in path) {
      builder.append(p.index).append("|").append(p.type.name).append("|")
        .append(p.start.pointName).append("(").append(p.start.x).append(",").append(p.start.y).append(")")
        .append("--")
        .append(p.target.pointName).append("(").append(p.target.x).append(",").append(p.target.y).append(")")
        .append("|r:").append(p.robotInHeading).append("|").append(p.robotOutHeading)
        .append("|c:").append(p.containerInHeading).append("|").append(p.containerOutHeading)
      builder.append(" -> ")
    }
    return builder.toString()
  }

  fun parseNeighborLog(neighbor: NeighborDomain): String {
    val builder = StringBuilder()
    builder.append(neighbor.robot).append("|canRotate{")
    neighbor.canRotate.forEach { r ->
      builder.append(r.type.name).append("|")
        .append(r.start.pointName).append("(").append(r.start.x).append(",").append(r.start.y).append(")|")
        .append(r.robotInHeading).append("|").append(r.robotOutHeading)
    }
    builder.append("} | canPass{")
    neighbor.canPass.forEach { p ->
      builder.append(p.type.name).append("|")
        .append(p.start.pointName).append("(").append(p.start.x).append(",").append(p.start.y).append(") to ")
        .append(p.target.pointName).append("(").append(p.target.x).append(",").append(p.target.y)
        .append(")|").append(p.robotInHeading).append("|").append(p.robotOutHeading).append(" | ")
    }
    builder.append("} | cantPass{").append(neighbor.cantPass).append("} | cantRotate{").append(neighbor.cantRotate)
      .append("} | backPath{")
    if (neighbor.backPass != null) {
      builder.append(neighbor.backPass!!.type.name).append("|")
        .append(neighbor.backPass!!.start.pointName).append("(").append(neighbor.backPass!!.start.x).append(",")
        .append(neighbor.backPass!!.start.y).append(") to ")
        .append(neighbor.backPass!!.target.pointName).append("(").append(neighbor.backPass!!.target.x)
        .append(",").append(neighbor.backPass!!.target.y).append(")|").append(neighbor.backPass!!.robotInHeading)
        .append("|").append(neighbor.backPass!!.robotOutHeading)
    }
    builder.append("} ")
    return builder.toString()
  }

  fun disableTableLog(prevent: PreventRequestInfo): String {
    val table: MutableMap<String, String> = mutableMapOf()
    prevent.runningRobots.forEach {
      if (it.disableTable.isNotEmpty()) {
        table[it.robotName] = JsonHelper.writeValueAsString(it.disableTable)
      }
    }
    return JsonHelper.writeValueAsString(table)
  }

  fun priorityLog(prevent: PreventRequestInfo): String {
    val map: MutableMap<String, Int> = mutableMapOf()
    prevent.runningRobots.forEach {
      map[it.robotName] = it.priority
    }
    return JsonHelper.writeValueAsString(map)
  }

  fun reserveTableLog(prevent: PreventRequestInfo): String {
    val map: MutableMap<String, MutableMap<Int, String>> = mutableMapOf()
    prevent.runningRobots.forEach {
      val reserveTable = it.reserveTable
      if (reserveTable.isEmpty()) return@forEach
      val t: MutableMap<Int, String> = mutableMapOf()
      for (table in reserveTable) {
        t[table.key] = table.value.start.pointName + "-" + table.value.target.pointName
      }
      map[it.robotName] = t
    }
    return JsonHelper.writeValueAsString(map)
  }
}