package com.seer.trick.fleet.traffic.distributed.lock.model

import com.fasterxml.jackson.annotation.JsonIgnore
import com.seer.trick.fleet.traffic.distributed.lock.LockType
import com.seer.trick.fleet.traffic.distributed.lock.graph.BoundingBox
import com.seer.trick.fleet.traffic.distributed.map.Position

/**
 * 三维锁闭
 * */
class SpaceLock(
  val groupName: String,
  val type: LockType,
  val name: String,
  val cells: MutableList<Cell>,
  val mapName: String,
) {

  // 区域
  var box: BoundingBox = BoundingBox()

  init {
    for (cell in cells) {
      cell.updateBoundingBox()
      box.updateBoundingBox(cell.box)
    }
  }

  // 不需要考虑碰撞检测的锁闭
  @JsonIgnore
  var ignoreCollisionSpaceLocks: MutableList<String> = mutableListOf()

  /**
   * 做碰撞检测
   * */
  fun checkCollision(lock: SpaceLock): Boolean {
    // 忽略自身
    if (lock.getKey() == getKey() || lock.getSubKey() == getSubKey()) {
      return false
    }
    // 忽略碰撞检测的锁闭
    if (ignoreCollisionSpaceLocks.contains(lock.getKey())) {
      return false
    }
    if (box.intersect(lock.box)) {
      for (cell in cells) {
        for (lockCell in lock.cells) {
          if (cell.checkCollision(lockCell)) {
            return true
          }
        }
      }
    }
    return false
  }

  /**
   *  获取点位信息
   * */
  fun getPoints(): MutableList<Position> {
    val points = mutableListOf<Position>()
    cells.forEach {
      points.add(it.sPosition)
      val tPosition = it.tPosition
      if (tPosition != null) {
        points.add(tPosition)
      }
    }
    return points
  }

  /**
   *  添加锁闭单元
   * */
  fun addCellLock(cell: Cell): SpaceLock {
    cells.add(cell)
    box.updateBoundingBox(cell.box)
    return this
  }

  /**
   * 创建锁闭信息
   * */
  fun createLock(cell: Cell) {
    clear()
    cells.add(cell)
    box = cell.box.copy()
  }

  fun clear() {
    cells.clear()
    ignoreCollisionSpaceLocks.clear()
  }

  fun getKey(): String = "${type.name}-><-$groupName-><-$name"

  fun getSubKey(): String = "$groupName-><-$name"

  fun copy(): SpaceLock {
    val lock = SpaceLock(
      groupName = groupName,
      type = type,
      name = name,
      cells = cells.map { it.copy() }.toMutableList(),
      mapName = mapName,
    )
    lock.ignoreCollisionSpaceLocks = ignoreCollisionSpaceLocks
    return lock
  }

  /**
   *  合并锁闭
   * */
  fun union(spaceLock: SpaceLock) {
    for (c in spaceLock.cells) {
      addCellLock(c)
    }
  }

  fun updateBoundingBox() {
    box = BoundingBox()
    for (cell in cells) {
      cell.updateBoundingBox()
      box.updateBoundingBox(cell.box)
    }
  }
}