package com.seer.trick.fleet.seer

import com.fasterxml.jackson.databind.node.ArrayNode
import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.fleet.domain.MapPath
import com.seer.trick.fleet.domain.MapPoint
import com.seer.trick.fleet.domain.MoveDirection
import com.seer.trick.fleet.domain.SceneBin
import com.seer.trick.fleet.seer.SmapHelper.valueToBase64Str
import com.seer.trick.helper.JsonHelper
import org.slf4j.LoggerFactory

/**
 * 根据 diff 对 old 进行增删改
 */
class SmapOutputHelper(private val old: ObjectNode, val diff: SceneAreaMapValueDiff, points: List<MapPoint>) {
  private val logger = LoggerFactory.getLogger(SmapOutputHelper::class.java)
  private val pointMap: Map<String, MapPoint> = points.associateBy { it.name }

  /**
   * 实际上可能并没有改动任何元素的属性，返回 null，否则会导致没有修改 smap 的元素属性是，smap 的 md5 也变了。
   *  详见 updateAreaMergedMap() 调用此方法处的说明。
   */
  fun merge(): ObjectNode? {
    mergePoints()
    mergePaths()
    mergeBins()
    mergeZones()
    return old
  }

  /**
   * 更新点位
   */
  private fun mergePoints() {
    if (diff.removedPoints.isEmpty() && diff.changedPoints.isEmpty() && diff.addedPoints.isEmpty()) return
    if (old.get("advancedPointList") == null) {
      if (diff.addedPoints.isNotEmpty()) old.replace("advancedPointList", addSmapPoints())
      return
    }
    // 添加点位
    val oldJnPoints = if (diff.addedPoints.isNotEmpty()) {
      val nodes = addSmapPoints().addAll(old["advancedPointList"] as ArrayNode)
      old.replace("advancedPointList", nodes)
      nodes
    } else {
      old["advancedPointList"]
    }

    // 删除点位
    val jnPoints = if (diff.removedPoints.isNotEmpty()) {
      val pointNames = diff.removedPoints.map { it.name }
      val nodes = JsonHelper.mapper.createArrayNode()
      for (point in oldJnPoints) {
        if (!pointNames.contains((point["instanceName"]?.asText()))) nodes.add(point)
      }
      old.replace("advancedPointList", nodes)
      nodes.asSequence()
    } else {
      oldJnPoints.asSequence()
    }

    // 更新点位
    val updatePointMap = diff.changedPoints.associateBy { it.name }
    for (jnPoint in jnPoints) {
      val onPoint = jnPoint as ObjectNode
      val name = onPoint["instanceName"]?.asText()
      if (name.isNullOrBlank()) continue
      val point = updatePointMap[name] ?: continue

      // ID：SMAP 的元素没有 id
      // 名称：暂不处理，否则还得处理关联的 path 和 bin

      // 更新点位的坐标值 x, y
      val onPos = onPoint["pos"] as ObjectNode
      onPos.put("x", point.x)
      onPos.put("y", point.y)

      // 更新点位的朝向
      onPoint.put("ignoreDir", point.direction == null)
      onPoint.put("dir", point.direction) // 弧度值

      // 宽度、高度：smap 的 point 没有这俩属性

      // 更新备注，对应 desc，但是需要转换成 base64 再赋值给 desc，smap 未显示其明文。
      onPoint.put("desc", valueToBase64Str(point.remark))

      // 停用：smap 的 point 没有这个属性。

      // 更新点位的属性
      val pros = onPoint["property"] as ArrayNode
      // 更新属性-随动
      updateProperty(pros, "spin", "bool", point.spin)

      // 更新属性-m4define
      updatePointM4define(pros, point)
    }
  }

  private fun addSmapPoints(): ArrayNode {
    val pointsNode = JsonHelper.mapper.createArrayNode()
    for (point in diff.addedPoints) {
      val pointNode = JsonHelper.mapper.createObjectNode()
      pointNode.put("instanceName", point.name)
      pointNode.put("className", point.type)
      val pos = JsonHelper.mapper.createObjectNode()
      pos.put("x", point.x)
      pos.put("y", point.y)
      pointNode.replace("pos", pos)
      if (point.direction == null) {
        pointNode.put("ignoreDir", true)
      }
      pointNode.put("dir", point.direction)
      pointNode.put("desc", valueToBase64Str(point.remark))
      val property = JsonHelper.mapper.createArrayNode()
      if (!point.label.isNullOrEmpty()) {
        updateProperty(property, "label", "string", point.label)
      }
      if (point.spin) {
        updateProperty(property, "spin", "bool", true)
      }
      if (point.parkAllowed) {
        updateProperty(property, "M4define", "bool", true)
      }
      updatePointM4define(property, point)
      if (property.size() != 0) pointNode.replace("property", property)
      pointsNode.add(pointNode)
    }
    return pointsNode
  }

  /**
   * 更新路径
   */
  private fun mergePaths() {
    if (diff.removedPaths.isEmpty() && diff.addedPaths.isEmpty() && diff.changedPaths.isEmpty()) return
    if (old.get("advancedCurveList") == null) {
      if (diff.addedPaths.isNotEmpty()) {
        old.replace("advancedCurveList", addSmapPaths())
      }
      return
    }
    // 添加路径
    val oldJnPaths = if (diff.addedPaths.isNotEmpty()) {
      val nodes = addSmapPaths().addAll(old["advancedCurveList"] as ArrayNode)
      old.replace("advancedCurveList", nodes)
      nodes
    } else {
      old["advancedCurveList"]
    }

    // 先删除路径
    val jnPaths = if (diff.removedPaths.isNotEmpty()) {
      val pathKeys = diff.removedPaths.map { it.key }
      val nodes = JsonHelper.mapper.createArrayNode()
      for (path in oldJnPaths) {
        if (!pathKeys.contains(path["instanceName"]?.asText()?.replace("-", "->"))) {
          nodes.add(path)
        }
      }
      old.replace("advancedCurveList", nodes)
      nodes.asSequence()
    } else {
      oldJnPaths.asSequence()
    }

    // 更新路径
    val updatePathMap = diff.changedPaths.associateBy { it.key }
    for (jnPath in jnPaths) {
      val pathNode = jnPath as ObjectNode
      val pathKey = pathNode["instanceName"]?.asText()?.replace("-", "->")
      if (pathKey.isNullOrEmpty()) continue
      // 起点、终点坐标可能发生了改变
      val start = pathNode["startPos"] as ObjectNode
      val end = pathNode["endPos"] as ObjectNode
      val fromPoint = pointMap[start["instanceName"].asText()]
      val toPoint = pointMap[end["instanceName"].asText()]

      // 如果线路的点位改变了，则及时更新相关的线路。
      if (fromPoint != null || toPoint != null) {
        val spDiff = getPointDiffAndUpdate(start, fromPoint)
        val epDiff = getPointDiffAndUpdate(end, toPoint)

        // 处理各种曲线的控制点-平移
        //  StraightPath 有 0 个控制点；
        //  BezierPath 和 DegenerateBezier 都是 2 个控制点
        //  NURBS6 有 4 个控制点
        //  ArcPath（圆弧） 有 1 个控制点
        when (pathNode["className"]?.asText()) {
          "BezierPath", "DegenerateBezier" -> {
            // TODO 待优化：能调整控制点了，但是效果不理想。
            updateControlPos("controlPos1", pathNode, spDiff) // 第一个控制点基于 spDiff 平移
            updateControlPos("controlPos2", pathNode, epDiff) // 第二个控制点基于 epDiff 平移
          }

          else -> {
            // TODO: 待实现
            //  1. 弧线（ArcPath）：1 个控制点，很少用，必要时先用 shop 处理吧。
            //  2. NURBS6：4 个控制点，如何调整。
            // 直线（StraightPath）没有控制点；其他未提及的线路类型，不处理，以后有新增的线型再说。
          }
        }
      }

      // 以下是路径属性发生改变
      val path = updatePathMap[pathKey] ?: continue // 当前路径没有需要更新的元素

      val pros = pathNode["property"] as ArrayNode

      // 更新备注，对应 desc，但是需要转换成 base64 再赋值给 desc，smap 未显示其明文。
      pathNode.put("desc", valueToBase64Str(path.remark))

      // 更新属性-正走倒走
      val smapPathDir = smapPathDir(path.moveDirection)
      updateProperty(pros, "direction", "int", smapPathDir)

      // 更新属性-成本因子
      updateProperty(pros, "length", "double", path.costFactor)

      // 更新属性-m4define
      updatePathM4define(pros, path)

      // 更新属性-进入路径时容器方向要求
      if (path.containerDir != null) {
        updateProperty(pros, "goodsDir", "double", path.containerDir)
      }
    }
  }

  private fun addSmapPaths(): ArrayNode {
    val pathsNode = JsonHelper.mapper.createArrayNode()
    for (path in diff.addedPaths) {
      val pathNode = JsonHelper.mapper.createObjectNode()
      pathNode.put(
        "className",
        SceneCurveHelper.toSmapCurveType(path.curveType, path.bezierPaths?.get(0)?.controls?.size ?: 0),
      )
      pathNode.put("instanceName", toSmapPathKey(path.key))
      val from = pointMap[path.fromPointName] ?: continue
      val to = pointMap[path.toPointName] ?: continue

      // 控制点
      val controls = path.controls.toMutableList()
      val fromPoint = controls.removeAt(0)
      val toPoint = controls.removeAt(controls.size - 1)

      val startNode = JsonHelper.mapper.createObjectNode()
      val startPos = JsonHelper.mapper.createObjectNode()
      startPos.put("x", from.x)
      startPos.put("y", from.y)
      if (fromPoint.weight > 0) {
        startPos.put("z", fromPoint.weight)
      }
      startNode.replace("pos", startPos)
      startNode.put("instanceName", from.name)

      val endNode = JsonHelper.mapper.createObjectNode()
      val endPos = JsonHelper.mapper.createObjectNode()
      endPos.put("x", to.x)
      endPos.put("y", to.y)
      if (toPoint.weight > 0) {
        endPos.put("z", toPoint.weight)
      }
      endNode.replace("pos", endPos)
      endNode.put("instanceName", to.name)
      pathNode.replace("startPos", startNode)
      pathNode.replace("endPos", endNode)

      // 控制点转换
      for ((index, control) in controls.withIndex()) {
        val controlNode = JsonHelper.mapper.createObjectNode()
        controlNode.put("x", control.x)
        controlNode.put("y", control.y)
        if (control.weight != 0.0) {
          controlNode.put("z", control.weight)
        }
        pathNode.replace("controlPos${index + 1}", controlNode)
      }
      val property = JsonHelper.mapper.createArrayNode()
      updatePathM4define(property, path)
      pathNode.put("desc", valueToBase64Str(path.remark))
      updateProperty(property, "direction", "int", smapPathDir(path.moveDirection))
      updateProperty(property, "length", "double", path.costFactor)
      // 调度默认给 movestyle，值 0 表示路径导航
      updateProperty(property, "movestyle", "int", 0)
      if (path.containerDir != null) {
        updateProperty(property, "goodsDir", "double", path.containerDir)
      }
      pathNode.replace("property", property)
      pathsNode.add(pathNode)
    }
    return pathsNode
  }

  private fun smapPathDir(moveDirection: MoveDirection): Int = when (moveDirection) {
    MoveDirection.Forward -> 0
    MoveDirection.Backward -> 1
    else -> 2
  }

  private fun toSmapPathKey(key: String): String = key.replace("->", "-")

  /**
   * 更新库位
   */
  private fun mergeBins() {
    if (diff.removedBins.isEmpty() && diff.addedBins.isEmpty() && diff.changedBins.isEmpty()) return
    if (old.get("binLocationsList") == null) {
      if (diff.addedBins.isNotEmpty()) old.replace("binLocationsList", addSmapBins())
      return
    }
    // 添加库位
    val oldJnBins = if (diff.addedBins.isNotEmpty()) {
      val nodes = addSmapBins().addAll(old["binLocationsList"] as ArrayNode)
      old.replace("binLocationsList", nodes)
      nodes
    } else {
      old["binLocationsList"]
    }

    for (jnBin in oldJnBins) {
      val oldBinsNode = jnBin["binLocationList"] as ArrayNode
      // 删除库位
      val binsNode = if (diff.removedBins.isNotEmpty()) {
        val binNames = diff.removedBins.map { it.name }
        val nodes = JsonHelper.mapper.createArrayNode()
        for (bin in oldBinsNode) {
          if (!binNames.contains(bin["instanceName"]?.asText())) nodes.add(bin)
        }
        (jnBin as ObjectNode).replace("binLocationList", nodes)
        nodes.asSequence()
      } else {
        oldBinsNode.asSequence()
      }

      // 更新
      val updateBinMap = diff.changedBins.associateBy { it.name }
      for (binNode in binsNode) {
        // smap 里面的库位可能没有关联点位，但是在导入、拉取机器人地图时，已经筛过了。
        val binName = binNode["instanceName"]?.asText() ?: continue
        val newBin = updateBinMap[binName] ?: continue // 跳过不需要更新属性的库位。

        // 修改库自身的坐标。
        val pos = binNode["pos"] as ObjectNode? // 库位一定是有坐标的！
        pos?.put("x", newBin.x)
        pos?.put("y", newBin.y)
        // 目前不写 z 也没关系。smap 的最底层库位是没有 pos.z 属性的, pos.z = SceneBin.layerNo - 1
        if (newBin.layerNo > 1) pos?.put("z", newBin.layerNo - 1)

        // 修改库位的关联点位
        val relatedPointName = binNode["pointName"]?.asText() ?: continue
        if (!newBin.workPointName.isNullOrEmpty() && relatedPointName != newBin.workPointName) {
          (binNode as ObjectNode).put("pointName", newBin.workPointName)
        }

        // 修改 binTask
        val props = binNode["property"] as ArrayNode
        val binTasks = newBin.binTaskMap?.map { it ->
          val binTaskValue: Map<String, Any> = JsonHelper.mapper.readValue(it.value, jacksonTypeRef())
          mapOf(Pair(it.key, binTaskValue))
        } ?: emptyList()
        updateProperty(props, "binTask", "jsonArray", binTasks)
        updateBinM4define(props, newBin)
      }
    }
  }

  private fun addSmapBins(): ArrayNode {
    val binLocationsNode = JsonHelper.mapper.createArrayNode()
    val groupBins = diff.addedBins.groupBy { it.workPointName }
    for ((pointName, bins) in groupBins) {
      val binLocationNode = JsonHelper.mapper.createObjectNode()
      val binLocationList = JsonHelper.mapper.createArrayNode()
      for (bin in bins) {
        val binNode = JsonHelper.mapper.createObjectNode()
        binNode.put("instanceName", bin.name)
        binNode.put("pointName", pointName)

        val pos = JsonHelper.mapper.createObjectNode()
        pos.put("x", bin.x)
        pos.put("y", bin.y)
        if (bin.layerNo > 1) pos?.put("z", bin.layerNo - 1)
        binNode.replace("pos", pos)

        val property = JsonHelper.mapper.createArrayNode()
        val binTasks = bin.binTaskMap?.map { it ->
          val binTaskValue: Map<String, Any> = JsonHelper.mapper.readValue(it.value, jacksonTypeRef())
          mapOf(Pair(it.key, binTaskValue))
        } ?: emptyList()
        updateProperty(property, "binTask", "jsonArray", binTasks)
        updateBinM4define(property, bin)
        binNode.replace("property", property)
        binLocationList.add(binNode)
      }
      binLocationNode.replace("binLocationList", binLocationList)
      binLocationsNode.add(binLocationNode)
    }
    return binLocationsNode
  }

  private fun mergeZones() {
    if (diff.removedZones.isEmpty() && diff.addedZones.isEmpty() && diff.changedZones.isEmpty()) return
    if (old.get("advancedAreaList") == null) {
      if (diff.addedZones.isNotEmpty()) old.replace("advancedAreaList", addSmapZones())
      return
    }

    // 添加区块
    val oldJnZones = if (diff.addedZones.isNotEmpty()) {
      val nodes = addSmapZones().addAll(old["advancedAreaList"] as ArrayNode)
      old.replace("advancedAreaList", nodes)
      nodes
    } else {
      old["advancedAreaList"]
    }

    val jnZones = if (diff.removedZones.isNotEmpty()) {
      val zoneNames = diff.removedZones.map { it.name }
      val nodes = JsonHelper.mapper.createArrayNode()
      for (zone in oldJnZones) {
        val className = zone["className"]?.asText()
        val zoneName = zone["instanceName"]?.asText()
        val mapZoneName = className + "_" + zoneName
        if (!zoneNames.contains(mapZoneName)) nodes.add(zone)
      }
      old.replace("advancedAreaList", nodes)
      nodes.asSequence()
    } else {
      oldJnZones.asSequence()
    }

    val updateZoneMap = diff.changedZones.associateBy { it.name }
    for (jnZone in jnZones) {
      // 目前新调度只处理了 smap 的描述区域。
      // TODO：以后有必要的时候再针对不同类型的区域做对应的处理。
      // if (jnZone["className"]?.asText() != "AreaDescription") continue

      val className = jnZone["className"]?.asText() ?: continue
      val zoneName = jnZone["instanceName"]?.asText() ?: continue
      val mapZoneName = className + "_" + zoneName
      val newZone = updateZoneMap[mapZoneName] ?: continue // 跳过不需要更新属性的区块。
      val onZone = jnZone as ObjectNode

      // 更新备注，对应 desc
      onZone.put("desc", valueToBase64Str(newZone.remark))

      // todo，先不做。
      //  smap 是通过 dir 和 4 个顶点坐标来描述区域的几何属性的，前端处理好之后直接将这个两类，共 5 个数据传过来就行。
      // 更新朝向，smap 记录的是弧度值
      onZone.put("dir", newZone.direction)
      // 更新区域的 4 个顶点的坐标
      // smap 一定是用矩形表示区块的。
      // 更新表示区块范围的矩形的顶点坐标。
      //  1. smap 没有记录区块的坐标，而是将区块的中心坐标结合矩形的长度宽度转换成区块的四个顶点坐标了
      //  2. 四个点依次表示：（当矩形朝向世界坐标系的 x 正方向时）右上、右下、左下、左上。
      (jnZone["posGroup"] as ArrayNode).forEachIndexed { idx, jnPos ->
        val onPos = jnPos as ObjectNode
        onPos.put("x", newZone.polygon[idx].x)
        onPos.put("y", newZone.polygon[idx].y)
      }

      // 更新区块的背景颜色和边框颜色。
      val attr = jnZone["attribute"] as ObjectNode
      if (newZone.borderColor != null) {
        attr.put("colorPen", colorToLong(newZone.borderColor)) // 更新边框颜色
      }
      if (newZone.fillColor != null) {
        attr.put("colorBrush", colorToLong(newZone.fillColor)) // 更新填充颜色
      }

      // 更新区块的属性
      val props = jnZone["property"] as ArrayNode
      // 更新属性-字号。
      if (newZone.fontSize != null) {
        updateProperty(props, "TextFontSize", "int", newZone.fontSize)
      }

      // 更新 m4define
      // updateM4defineValue(props, mapOf("disabled" to newZone.disabled))
    }
  }

  private fun addSmapZones(): ArrayNode {
    val zonesNode = JsonHelper.mapper.createArrayNode()
    for (zone in diff.addedZones) {
      val zoneNode = JsonHelper.mapper.createObjectNode()
      zoneNode.put("instanceName", zone.name)
      zoneNode.put("className", zone.type)
      zoneNode.put("dir", zone.direction)

      val posGroup = JsonHelper.mapper.createArrayNode()
      for (point in zone.polygon) {
        val pos = JsonHelper.mapper.createObjectNode()
        pos.put("x", point.x)
        pos.put("y", point.y)
        posGroup.add(pos)
      }
      zoneNode.replace("posGroup", posGroup)

      val property = JsonHelper.mapper.createArrayNode()
      if (zone.fontSize != null) {
        updateProperty(property, "TextFontSize", "int", zone.fontSize)
      }
      // updateM4defineValue(property, mapOf("disabled" to zone.disabled))
      zoneNode.replace("property", property)

      val attribute = JsonHelper.mapper.createObjectNode()
      if (zone.borderColor != null) {
        attribute.put("colorPen", colorToLong(zone.borderColor))
      }
      if (zone.fillColor != null) {
        attribute.put("colorFill", colorToLong(zone.fillColor))
      }
      zoneNode.replace("attribute", attribute)

      zonesNode.add(zoneNode)
    }
    return zonesNode
  }

  /**
   * 将 16 进制或者 RGBA 转为数值类型
   */
  private fun colorToLong(color: String): Long = if (color.startsWith("rgba")) rgbaToLong(color) else hexToLong(color)

  /**
   * RGBA 转数值类型，不解析透明度
   */
  private fun rgbaToLong(rgba: String): Long {
    val values = rgba.replace("rgba(", "")
      .replace(")", "")
      .split(",")
      .map { it.trim() }
    val r = values[0].toInt()
    val g = values[1].toInt()
    val b = values[2].toInt()
    return (r.toLong() shl 16) or (g.toLong() shl 8) or b.toLong()
  }

  /**
   * 16 进制转为数值类型，不解析透明度
   */
  private fun hexToLong(hex: String): Long {
    val cleanHex = hex.replace("#", "").replace("0x", "")
    // 截取最后 6 位（RRGGBB），忽略前 2 位（AA）
    val rgbHex = if (cleanHex.length == 8) cleanHex.substring(2) else cleanHex
    return rgbHex.toLong(16)
  }

  /**
   * 修改地图元素的 property 的指定属性的详情
   *
   *  key: 待处理的属性的名称。
   *  type: 待处理的属性的数据类型； int, double, bool, jsonObject, jsonArray, string
   *  value: 待处理的属性的新值。
   */
  private fun updateProperty(properties: ArrayNode, key: String, type: String, value: Any) {
    val prop = properties.find { it.get("key")?.asText() == key } as ObjectNode?
    if (prop != null) {
      putTypeValue(prop, type, value)
    } else {
      val newProp = JsonHelper.mapper.createObjectNode()
      newProp.put("key", key)
      newProp.put("type", if (type.contains("json")) "json" else type)
      putTypeValue(newProp, type, value)
      properties.add(newProp)
    }
  }

  /**
   * 更新指定属性的值，同时更新 utf8 和 base64 两种编码格式的值。
   */
  private fun putTypeValue(property: ObjectNode, type: String, value: Any) {
    // 写入 Base64 编码的属性值
    val valueStr =
      when (type) {
        "jsonObject" -> {
          // shop 解析 path 的 M4define 的 base64 编码的文本，即 property.value 的值。
          // shop 解析 point 的 M4define 的非 base64 编码的文本，即 property.stringValue 的值。
          JsonHelper.writeValueAsString(value as Map<*, *>)
        }

        "jsonArray" -> JsonHelper.writeValueAsString(value as List<*>) // 处理 binTask
        else -> value.toString()
      }
    property.put("value", valueToBase64Str(valueStr))

    // 写入属性值
    when (type) {
      "string" -> property.put("stringValue", value as String) // samp 没有这个类型
      "jsonObject" -> property.put("stringValue", JsonHelper.writeValueAsString(value as Map<*, *>))
      "jsonArray" -> property.put("stringValue", JsonHelper.writeValueAsString(value as List<*>))
      "int" -> property.put("int32Value", value as Int)
      "double" -> property.put("doubleValue", value as Double)
      "bool" -> property.put("boolValue", value as Boolean)
      else -> throw BzError("Undefined value type of smap element property")
    }
  }

  /**
   * 更新 M4define 的属性值。
   */
  private fun updateM4defineValue(properties: ArrayNode, updates: Map<String, Any>) {
    val m4defineValue = JsonHelper.mapper.createObjectNode()
    updates.forEach {
      when (it.value) {
        is Boolean -> m4defineValue.put(it.key, it.value as Boolean)
        is Int -> m4defineValue.put(it.key, it.value as Int)
        is Double -> m4defineValue.put(it.key, it.value as Double)
        is String -> m4defineValue.put(it.key, it.value as String)
        else -> {
          // TODO 暂不支持更复杂类型
          logger.error("unsupported type ${it.value::class.simpleName}, value ${it.value}")
        }
      }
    }
    updateProperty(properties, "M4define", "jsonObject", updates)
  }

  /**
   * 更新路径的自定义属性
   */
  private fun updatePathM4define(property: ArrayNode, path: MapPath) = updateM4defineValue(
    property,
    mapOf(
      "rotateNotAllowed" to path.rotateNotAllowed, // 不允许旋转
      "giveWayNotAllowed" to path.giveWayNotAllowed, // 解死锁不可通过
      "containerShortSideAhead" to path.containerShortSideAhead, // 容器短边必须朝前
      "m4labelsStr" to (path.m4labelsStr ?: ""), // m4 定义的元素标签的集合
      "loadPass" to path.loadPass.name,
    ),
  )

  /**
   * 更新点位的自定义属性
   */
  private fun updatePointM4define(property: ArrayNode, point: MapPoint) {
    val m4define = mutableMapOf<String, Any>(
      "parkAllowed" to point.parkAllowed, // 允许停靠
      "chargeAllowed" to point.chargeAllowed, // 允许充电
      "giveWayNotAllowed" to point.giveWayNotAllowed, // 不允许避让
      "rotateNotAllowed" to point.rotateNotAllowed, // 不允许旋转
      "containerRotateAllowed" to point.containerRotateAllowed, // 换向点
      "linkedPathAsJointResource" to point.linkedPathAsJointResource,
      "m4labelsStr" to (point.m4labelsStr ?: ""), // m4 定义的元素标签的集合
    )
    if (point.unloadContainerDir != null) m4define["unloadContainerDir"] = point.unloadContainerDir

    updateM4defineValue(property, m4define)
  }

  /**
   * 更新库位的自定义属性
   */
  private fun updateBinM4define(props: ArrayNode, bin: SceneBin) {
    val m4define = mutableMapOf<String, Any>(
      "m4labelsStr" to (bin.m4labelsStr ?: ""), // m4 定义的元素标签的集合
      "remark" to bin.remark, // 库位没有 desc 字段，其备注信息只能记录在这里，否则通过 shop 地图后，存在其他节点的备注信息会被清空。
    )
    if (bin.unloadContainerDir != null) m4define["unloadContainerDir"] = bin.unloadContainerDir
    updateM4defineValue(props, m4define)
  }

  private fun getPointDiffAndUpdate(oldPoint: ObjectNode, newPoint: MapPoint?): Map<String, Double> {
    if (newPoint == null) return mapOf("x" to 0.0, "y" to 0.0)

    // 先取差值
    val pos = oldPoint["pos"] as ObjectNode
    val diff = mapOf(
      "x" to newPoint.x - pos["x"].asDouble(),
      "y" to newPoint.y - pos["y"].asDouble(),
    )

    // 再赋值
    pos.put("x", newPoint.x)
    pos.put("y", newPoint.y)

    return diff
  }

  private fun updateControlPos(controlPosName: String, path: ObjectNode, diff: Map<String, Double>) {
    val controlPos = path[controlPosName] as ObjectNode
    // diff = 新值 - 旧值；则这里计算时就该是 旧值 + diff。
    controlPos.put("x", controlPos["x"].asDouble() + diff["x"]!!)
    controlPos.put("y", controlPos["y"].asDouble() + diff["y"]!!)
  }
}