package com.seer.trick.fleet.traffic.distributed.map

import com.seer.trick.fleet.domain.MoveDirection
import com.seer.trick.fleet.traffic.distributed.context.domain.RobotMotionType
import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper
import java.util.*

/**
 * 线路信息
 * */
class Line(
  val lineName: String, // 线编码
  val type: LineType, // 线的类型
  val start: Point, // 起始点
  val end: Point, // 终止点
  var length: Double, // 线的长度
  val passThrough: Boolean = true, // 解死锁是否可穿行
  val rotate: Boolean = true, // 是否可以旋转
  val driveDirection: MoveDirection, // todo 限制行驶方向
  val enterDir: Int, // 进入角度
  val outDir: Int, // 离开角度
  val narrowPath: Boolean = false, // 是否为窄边行走
  val containerDir: Int? = null,
  val loadStatus: LoadStatus = LoadStatus.ANY, // 路线带载状态

  val tracePoses: MutableList<Pose> = mutableListOf(),

  val controls: MutableList<Site> = LinkedList(), // 控制点
) {

  fun angleDeviation(): Int = outDir - enterDir

  // 判断机器人以当前角度是否可以通过
  fun robotCanPass(load: Boolean, angle: Int, moveType: RobotMotionType): Boolean {
    if (!robotCanPass(load)) return false
    val headings = findHeadings(moveType)
    if (headings.isEmpty()) return false
    for (heading in headings) {
      if (AngleHelper.sameAngleInFiveDegree(angle, heading)) return true
    }
    return false
  }

  fun robotCanPass(load: Boolean): Boolean = when (loadStatus) {
    LoadStatus.EMPTY -> !load
    LoadStatus.LOADED -> load
    LoadStatus.ANY -> true
  }

  fun findHeadings(moveType: RobotMotionType): MutableList<Int> {
    val headings: MutableList<Int> = mutableListOf()
    if (driveDirection == MoveDirection.Dual) {
      if (moveType == RobotMotionType.ADVANCE) {
        headings.add(enterDir)
      } else if (moveType == RobotMotionType.BOTH) {
        headings.add(enterDir)
        headings.add(AngleHelper.processAngle(enterDir + AngleHelper.DOWN_ANGLE))
      } else if (moveType == RobotMotionType.OMNI) {
        headings.add(enterDir)
        headings.add(AngleHelper.processAngle(enterDir + AngleHelper.DOWN_ANGLE))
        headings.add(AngleHelper.processAngle(enterDir + AngleHelper.RIGHT_ANGLE))
        headings.add(AngleHelper.processAngle(enterDir - AngleHelper.DOWN_ANGLE))
      }
      return headings
    }
    if (driveDirection == MoveDirection.Forward) {
      headings.add(enterDir)
      return headings
    }
    if (driveDirection == MoveDirection.Backward) {
      headings.add(AngleHelper.processAngle(enterDir + AngleHelper.DOWN_ANGLE))
      return headings
    }
//    if (driveDirection.contains(DriveDirection.Shift)) {
//      if (moveType == RobotMotionType.OMNI) {
//        headings.add(AngleHelper.processAngle(enterDir + AngleHelper.RIGHT_ANGLE))
//        headings.add(AngleHelper.processAngle(enterDir - AngleHelper.DOWN_ANGLE))
//      }
//      return headings
//    }
    return headings
  }

  fun getClosePose(x: Double, y: Double): Pose {
    if (type == LineType.STRAIGHT) {
      return Pose(x, y, enterDir)
    }
    var minPose = tracePoses[0]
    var minDis = tracePoses[0].getDistance(x, y)
    for (i in 1 until tracePoses.size) {
      val pose = tracePoses[i]
      val dis = pose.getDistance(x, y)
      if (dis < minDis) {
        minPose = pose
        minDis = dis
      }
    }
    return minPose
  }
}

/**
 * 线的类型
 * */
enum class LineType {
  // 直线
  STRAIGHT,

  // 三阶贝塞尔曲线
  THREE_BEZIER,

  // 高阶曲线
  LUMINANCE_CURVE,

  // 其他
  UNDEFINE,
}

/**
 * 路线带载状态
 * */
enum class LoadStatus {
  EMPTY, // 仅空载
  LOADED, // 仅带载
  ANY, // 任意
}