package com.seer.trick.fleet.traffic.distributed.map

import com.seer.trick.fleet.traffic.distributed.lock.model.SpaceLock
import java.util.*

// 地图区域
data class MapArea(
  val mapName: String, // 名称。 对于交管，区域名称唯一
  val points: MutableMap<String, Point>, // 点集合
  val lines: MutableMap<String, Line>, // 线集合
  var bound: Bound, // 大小
)

// 互斥区
data class BlockArea(
  val name: String, // 区块名称。一般自动生成，也可以手工指定。接口和界面上显示名称。
  val disabled: Boolean, // 停用
  val shape: SpaceLock, // 锁闭模型
  val robotNum: Int, // 区域支持的车的数量
  val robots: MutableList<String> = Collections.synchronizedList(mutableListOf()), // 区域内的机器人
)

// 自由导航区
data class AvoidArea(
  val name: String, // 区块名称。一般自动生成，也可以手工指定。接口和界面上显示名称。
  val disabled: Boolean, // 停用
  val shape: SpaceLock, // 锁闭模型
  val pointNames: List<String>, // 区域内的点
)