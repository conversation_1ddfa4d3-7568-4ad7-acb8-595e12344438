package com.seer.trick.fleet.order

import com.seer.trick.Cq
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.fleet.FleetLogger
import com.seer.trick.fleet.domain.OrderStatus
import com.seer.trick.fleet.order.OrderRuntime.Companion.timeCostOrZero
import com.seer.trick.fleet.order.OrderService.countFaultDuration
import com.seer.trick.fleet.order.OrderService.countLoadDuration
import com.seer.trick.fleet.order.OrderService.countUnloadDuration
import com.seer.trick.fleet.order.OrderService.updateAndPersistOrder
import com.seer.trick.fleet.service.RobotBinService
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.RobotService
import com.seer.trick.fleet.service.SceneRuntime
import org.slf4j.LoggerFactory
import java.util.*

/**
 * 处理运单的取消。单独放在一个类里解决 OrderService 过大的问题。
 */
object OrderCancelService {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 取消通用运单。包括自动单。
   * 如果运单正在被执行，先把运单取消、机器人中断，等待后续处理。
   * 在运单锁里执行。
   */
  fun cancelOrder(sr: SceneRuntime, orderId: String) = sr.withOrderLock {
    val or = sr.orders.remove(orderId)
    if (or == null) {
      updateDbOrderToCancelled(sr, orderId)
      return@withOrderLock
    }

    val order = or.order
    val status = order.status
    val currentStep = or.getCurrentStep()
    val rr = order.actualRobotName?.let { sr.robots[it] }

    FleetLogger.info(
      module = "Order",
      subject = "CancelOrderLive",
      sr = sr,
      robotName = rr?.robotName,
      msg = mapOf(
        "orderId" to orderId,
        "orderStatus" to status,
        "currentStepIndex" to order.currentStepIndex,
        "currentStepStatus" to currentStep?.status,
        "cmdStatus" to rr?.getExecuteStatus(),
        "robotBins" to rr?.bins,
      ),
    )
    // todo order.fault == true 要不要特殊处理

    val doneOn = Date()

    val newOrder = or.order.copy(
      fault = false,
      faultReason = null,
      faultDuration = countFaultDuration(or),
      loadDuration = countLoadDuration(or),
      unloadDuration = countUnloadDuration(or),
      status = OrderStatus.Cancelled,
      doneOn = doneOn,
      processingTime = timeCostOrZero(doneOn, order.createdOn),
      executingTime = timeCostOrZero(doneOn, order.robotAllocatedOn),
    )
    updateAndPersistOrder(or, newOrder, "Cancel order")

    // 处理关联机器人
    if (rr != null) {
      cancelRobot(or, rr)
    }

    DispatchOrderService.dispatchOrders(sr)
  }

  /**
   * 取消关联的机器人
   * 取消一个已经故障的机器人的运单时，将储位改为取消，正常清理机器人故障以及运单
   */
  private fun cancelRobot(or: OrderRuntime, rr: RobotRuntime) = rr.sr.withOrderLock {
    val sec = rr.executingStep
    if (sec != null && sec.orderId == or.id) {
      StepExecuteService.cancelExecuting(sec, "User cancel order")
    }

    if (rr.selectSameOrderId == or.id) {
      logger.info("Clear robot $rr 'selectSameOrderId' for order '${or.id}' cancelled")
      rr.selectSameOrderId = null
    }

    rr.orders.remove(or.id)
    RobotBinService.cancelBin(or, rr)
    // 是自动单
    if (or.id == rr.autoOrder?.orderId) rr.autoOrder = null

    RobotService.persistRobotRuntime(rr)
  }

  /**
   * 如果运单不是活跃运单，一般不会出现
   */
  private fun updateDbOrderToCancelled(sr: SceneRuntime, orderId: String) {
    FleetLogger.info(
      module = "Order",
      subject = "CancelOrderNotLive",
      sr = sr,
      robotName = null,
      msg = mapOf(
        "orderId" to orderId,
      ),
    )

    // 正常情况下，此时的 order 已经处于终态了，不需要再计算耗时，不处理更新此功能前的历史记录的耗时。
    // 直接在运单列表中修改运单状态为终态，不会触发计算耗时的逻辑。
    EntityRwService.updateOne(
      "TransportOrder",
      Cq.cqAnd(
        listOf(
          Cq.idEq(orderId),
          // 不应该取消已经是终态（Done、Cancelled）的运单
          Cq.ne("status", OrderStatus.Done.name),
          Cq.ne("status", OrderStatus.Cancelled.name),
        ),
      ),
      mutableMapOf("status" to OrderStatus.Cancelled.name, "fault" to false, "doneOn" to Date()),
    )
  }

  /**
   * 取消全部未完成的运单。包括自动单。
   */
  fun cancelAllOrders(sr: SceneRuntime) {
    val ids = sr.orders.keys.toList() // 先不管数据库里的
    for (id in ids) {
      try {
        cancelOrder(sr, id)
      } catch (e: Exception) {
        logger.error("Failed to cancel transport order: $id", e)
      }
    }
  }
}