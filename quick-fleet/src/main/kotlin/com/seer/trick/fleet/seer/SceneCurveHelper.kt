package com.seer.trick.fleet.seer

import com.seer.trick.fleet.domain.MapPath
import com.seer.trick.fleet.domain.MapPoint
import com.seer.trick.fleet.domain.PathCurveType
import com.seer.trick.fleet.seer.SmapCurveHelper.createBSpline
import org.tinyspline.BSpline

/**
 * 场景曲线辅助类
 */
object SceneCurveHelper {

  /**
   * 将场景路径转换为样条曲线
   */
  fun transferPathToSpline(path: MapPath, fromPoint: MapPoint, toPoint: MapPoint): BSpline = when (path.curveType) {
    PathCurveType.Straight -> createStraightPathBSpline(fromPoint, toPoint)
    PathCurveType.Bezier3 -> createBezierPathBSpline(path)
    PathCurveType.NURBS6 -> createNurbsPathBSpline(path)
    PathCurveType.DegenerateBezier -> createDegenerateBezierPathBSpline(path)
    PathCurveType.Other -> {
      // 兼容老版本，若 bezierPaths 的控制点数量为 2，则是圆弧，TODO bezierPaths 应该不会为 null？
      if (path.bezierPaths?.get(0)?.controls?.size == 2) {
        createStraightPathBSpline(fromPoint, toPoint)
      } else {
        createDegenerateBezierPathBSpline(path)
      }
    }
  }

  /**
   * 高级三阶贝塞尔曲线
   */
  private fun createDegenerateBezierPathBSpline(path: MapPath): BSpline {
    val controlPoints = buildList {
      for ((index, control) in path.controls.withIndex()) {
        add(control.x)
        add(control.y)
        // 非起点且非终点则重复添加
        if (index != 0 && index != path.controls.size - 1) {
          add(control.x)
          add(control.y)
        }
      }
    }
    return createBSpline(6, 5, 2, controlPoints = controlPoints)
  }

  /**
   * nurbs 曲线
   */
  private fun createNurbsPathBSpline(path: MapPath): BSpline {
    val controlPoints = buildList {
      path.controls.forEach { control ->
        add(control.x * control.weight)
        add(control.y * control.weight)
        add(control.weight)
      }
    }
    return createBSpline(
      6,
      2,
      3,
      listOf(0.0, 0.0, 0.0, 0.25, 0.5, 0.75, 1.0, 1.0, 1.0),
      controlPoints = controlPoints,
    )
  }

  /**
   * 贝塞尔曲线
   */
  private fun createBezierPathBSpline(path: MapPath): BSpline {
    val controlPoints = buildList {
      path.controls.forEach { control ->
        add(control.x)
        add(control.y)
      }
    }
    return createBSpline(4, 3, 2, controlPoints = controlPoints)
  }

  /**
   * 创建形状为线段的 b 样条曲线
   */
  private fun createStraightPathBSpline(fromPoint: MapPoint, toPoint: MapPoint): BSpline {
    val controlPoints = listOf(
      fromPoint.x,
      fromPoint.y,
      toPoint.x,
      toPoint.y,
    )
    return createBSpline(2, 1, 2, controlPoints = controlPoints)
  }

  /**
   * 场景的路径曲线类型转 smap 的曲线类型
   * @param curveType 场景的路径曲线类型
   * @param controlsNum bezierPaths 中的控制点数量
   * @return smap 的曲线类型
   */
  fun toSmapCurveType(curveType: PathCurveType, controlsNum: Int): String = when (curveType) {
    PathCurveType.Straight -> "StraightPath"
    PathCurveType.Bezier3 -> "BezierPath"
    PathCurveType.NURBS6 -> "NURBS6"
    PathCurveType.DegenerateBezier -> "DegenerateBezier"
    PathCurveType.Other -> {
      // 兼容老版本，后续可以直接转为圆弧
      if (controlsNum != 2) {
        "DegenerateBezier"
      } else {
        "ArcPath"
      }
    }
  }
}