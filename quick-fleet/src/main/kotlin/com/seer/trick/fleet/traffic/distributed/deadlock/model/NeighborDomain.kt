package com.seer.trick.fleet.traffic.distributed.deadlock.model

import com.seer.trick.fleet.traffic.distributed.plan.model.PathAction

/**
 *  邻域管理
 * */
class NeighborDomain(val robot: String) {

  /**
   *  旋转，目前先只考虑最大圆的旋转，不考虑圆弧，故只有一个
   * */
  val canRotate: MutableList<PathAction> = mutableListOf()

  /**
   * 可行方向
   * */
  val canPass: MutableList<PathAction> = mutableListOf()

  /**
   * 不可行点集合
   * */
  val cantPass: MutableList<String> = mutableListOf()

  /**
   * 不可旋转点集合
   * */
  val cantRotate: MutableList<String> = mutableListOf()

  /**
   * 倒车路线，配合 配置是否可倒车解死锁使用
   * */
  var backPass: PathAction? = null

}