package com.seer.trick.fleet.device.lift

import java.util.Date

/**
 * 电梯的配置
 * 一个电梯可能有多个 SM 点。SM 点必须有一个方向。
 * 一个电梯可能有多个门。而且每一层门数可能不一样。比如电梯有前后两个门。但在一楼开两个门，二楼只开一个门。
 * 电梯门分内外门。但尽量忽略。
 * 一个电梯在一层可能有多条进出路径。
 */
data class SceneLift(
  val id: Int,
  val name: String = "",
  val disabled: Boolean = false, // 是否禁用。
  //
  val mock: Boolean = false, // 是否是仿真模式
  val adapterType: LiftAdapterType = LiftAdapterType.Mock,
  //
  val doorNum: Int = 1, // 电梯有几个门
  val floorNum: Int = 0, // 有几层楼
  val floors: List<LiftFloorConfig> = emptyList(), // 分楼层配置
  //
  // 一些耗时，可用于仿真电梯，精确的数据也可用于调度逻辑
  val openDoorCost: Long = 3000, // 电梯开门耗时
  val closeDoorCost: Long = 3000, // 电梯关门耗时，应该和开门耗时一样。
  val changeFloorCost: Long = 5000, // 电梯到达目标楼层的耗时。TODO：精致些可以处理成上、下一个楼层的耗时，但是目前的数据结构不支持，目前非刚需。

  val host: String? = null,
  val port: Int? = null,

  val reportFunName: String? = null, // 脚本获取门状态的方法名
  val openFunName: String? = null, // 脚本开门的方法名
  val closeFunName: String? = null, // 脚本关门的方法名

  val keepCalling: Boolean = true, // 是否在进入/离开电梯时一直下发开门信号，默认一直下发
)

enum class LiftAdapterType {
  JinBo,
  Script,
  Mock,
}

/**
 * 电梯分楼层配置
 */
data class LiftFloorConfig(
  val index: Int = 0,
  val name: String = "", // 给用户看到的楼层的名称
  val code: String = "", // 给梯控发的楼层的编码
  val areaId: Int = -1, // 所属区域
  val switchMapPoints: List<LiftSwitchMapPoint> = emptyList(), // 切地图点
)

/**
 * 电梯里切地图的点
 */
data class LiftSwitchMapPoint(
  val pointName: String = "", // 点位名称
  val enterPaths: List<LiftPath> = emptyList(), // 进入电梯的路径
  val exitPaths: List<LiftPath> = emptyList(), // 退出电梯的路径
)

/**
 * 进出电梯的路径
 */
data class LiftPath(
  val pathKey: String = "", // 路径 key
  val doorNo: Int = 0, // 经过电梯的门的编号，从 1 开始
)

/**
 * 电梯适配器报告
 */
data class LiftAdapterReport(
  val online: Boolean = false, // 是否在线
  val fault: Boolean = false, // 是否故障
  val faultMsg: String = "", // 故障信息。可能会有多条故障信息。
  val autoMode: Boolean = true, // 自动模式，非手动模式，可以被调度使用
  val currentFloor: Int? = null, // 当前楼层，可能为空
  val targetFloor: Int? = null, // 目标楼层，可能为空
  val doors: List<LiftDoorStatus> = emptyList(), // 电梯的门的状态
  val people: Boolean = false, // 电梯里是否有人
  val timestamp: Date = Date(),
)

/**
 * 电梯门状态
 */
enum class LiftDoorStatus {
  Unknown, // 门状态未知
  Closed, // 门已关闭
  Closing, // 门正在关闭
  Opening, // 门正在打开
  Opened, // 门已打开
}

/**
 * 需要展示在界面上的电梯的详情
 */
data class LiftUiReport(
  val name: String, // 电梯名称
  val online: Boolean, // 是否在线
  val autoMode: Boolean,
  val currentFloor: String? = null,
  val targetFloor: String? = null,
  val doors: List<LiftDoorStatus>,
  val people: Boolean = false,
  val fault: Boolean, // 是否故障：
  val faultMsg: String, // 故障信息。可能会有多条故障信息。
  val lastStatusUpdateTime: Date, // 最后一次状态更新的时间
  val disabled: Boolean, // 是否禁用

) {
  fun toStringExceptTime() =
    "online: $online, disabled: $disabled, autoMode: $autoMode, currentFloor: $currentFloor, " +
      "targetFloor: $targetFloor, doors: $doors, people: $people, fault: $fault, faultMsg: $faultMsg, " +
      "disabled: $disabled"
}

data class MockLiftRecord(
  val id: Int,
  val name: String, // 电梯名称
  val online: Boolean = true, // 是否在线
  val autoMode: Boolean = true,
  val currentFloor: String? = null,
  val targetFloor: String? = null,
  val doors: List<LiftDoorStatus> = emptyList(),
  val people: Boolean = false,
  val fault: Boolean = false, // 是否故障：
  val faultMsg: String = "", // 故障信息。可能会有多条故障信息。
  val lastStatusUpdateTime: Date = Date(), // 最后一次状态更新的时间
)

data class UpdateLiftRecord(
  val online: Boolean? = null, // 是否在线
  val autoMode: Boolean? = null,
  val currentFloor: String? = null,
  val targetFloor: String? = null,
  val doors: List<LiftDoorStatus> = emptyList(),
  val people: Boolean? = null,
  val fault: Boolean? = null, // 是否故障：
  val faultMsg: String? = null, // 故障信息。可能会有多条故障信息。
) {

  /**
   * 修改仿真电梯的运行记录时，方便获取仅需要修改的属性和对应的期望值。
   */
  fun toStringNotNullAndNotEmpty(): String? {
    val updates: MutableList<String> = mutableListOf()
    if (online != null) updates.add("online=$online")
    if (autoMode != null) updates.add("autoMode=$autoMode")
    if (currentFloor != null) updates.add("currentFloor=$currentFloor")
    if (targetFloor != null) updates.add("targetFloor=$targetFloor")
    if (doors.isNotEmpty()) updates.add("doors=$doors")
    if (people != null) updates.add("people=$people")
    if (fault != null) updates.add("fault=$fault")
    if (faultMsg != null) updates.add("faultMsg=$faultMsg")

    return if (updates.isNotEmpty()) updates.joinToString(", ") else null
  }
}

/**
 * 机器人使用电梯的状态
 */
enum class RobotUsingStatus {
  Entering,
  Inside,
  Exiting,
}