package com.seer.trick.fleet.traffic.distributed.plan

import com.seer.trick.fleet.domain.MoveDirection
import com.seer.trick.fleet.domain.PositionType
import com.seer.trick.fleet.traffic.distributed.context.ContextManagerService
import com.seer.trick.fleet.traffic.distributed.context.domain.*
import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper
import com.seer.trick.fleet.traffic.distributed.helper.PathInfoHelper
import com.seer.trick.fleet.traffic.distributed.lock.base.RobotContainerSpaceLock
import com.seer.trick.fleet.traffic.distributed.map.*
import com.seer.trick.fleet.traffic.distributed.plan.model.*
import org.slf4j.LoggerFactory

/**
 *  路径规划服务
 * */
object PlanPathService {

  private val logger = LoggerFactory.getLogger(javaClass)

  fun plan(request: RequestInfo): ResponseInfo {
    try {
      // 先更新机器人的任务信息
      val context = ContextManagerService.updateRobotContext(request)
        ?: return ResponseInfo(ResStatus.FAILED, code = "*********", args = mutableListOf())
      // 添加容错次数，避免死循环
      var faultToleranceNumber = 0
      while (context.state == TrafficStatus.STOP && faultToleranceNumber < 10) {
        logger.warn("waiting for robot ${context.robotName} status from stop to idle")
        Thread.sleep(100)
        faultToleranceNumber++
      }
      if (context.state == TrafficStatus.STOP && faultToleranceNumber == 10) {
        return ResponseInfo(ResStatus.FAILED, code = "*********", args = mutableListOf(context.robotName))
      }
      // 1对请求信息进行校验
      val response = planPath(request, null)
      val robotContext = ContextManagerService.queryRobotContext(request.robotName)
      // 对路径规划的成功 or 失败进行处理
      robotContext.request.request = request
      if (response.status == ResStatus.FAILED) {
        // 路径规划失败
        context.state = TrafficStatus.IDLE
        return ResponseInfo(ResStatus.FAILED, code = response.code, args = response.args)
      }
      // 对路径进行更新
      val update = ContextManagerService.updatePath(robotContext.robotName, response)
      if (!update) {
        context.state = TrafficStatus.IDLE
        return ResponseInfo(ResStatus.FAILED, code = "*********", args = mutableListOf())
      }
      robotContext.state = TrafficStatus.RUNNING

      return ResponseInfo(ResStatus.SUCCESS, code = response.code, args = response.args)
    } catch (e: Exception) {
      logger.error("${request.robotName} | plan path error", e)
      return ResponseInfo(ResStatus.FAILED, code = "*********", args = mutableListOf(request.robotName))
    }
  }

  private fun planPath(request: RequestInfo, restraint: Restraint?): PlanResponse {
    if (!valid(request)) {
      return PlanResponse(ResStatus.FAILED, code = "*********", args = mutableListOf(), path = mutableListOf())
    }
    var startInfo: StartPositionAndPreActions? = null
    var startAngle = request.startAngle

    // 路径规划找起点
    val startPoint = if (request.startType == PositionType.Path) {
      val line = try {
        MapService.findLineByName(request.sceneId, request.mapName, request.groupName, request.start)
      } catch (e: RuntimeException) {
        logger.error(
          "${request.robotName}| line ${request.start} is not exist in ${request.mapName} and ${request.groupName}",
        )
        return PlanResponse(
          status = ResStatus.FAILED,
          code = "*********",
          args = mutableListOf(request.start, request.groupName),
          path = mutableListOf(),
        )
      }
      startInfo = startPositionAndPreActions(line, request)
      startAngle = startInfo.angle
      startInfo.line.end
    } else {
      try {
        MapService.findPointByName(request.sceneId, request.mapName, request.groupName, request.start)
      } catch (e: RuntimeException) {
        logger.error(
          "${request.robotName} | start point ${request.start} is not in ${request.mapName} and ${request.groupName}",
        )
        return PlanResponse(
          status = ResStatus.FAILED,
          code = "T00010003",
          args = mutableListOf(request.start, request.groupName, request.groupName),
          path = mutableListOf(),
        )
      }
    }
    // 路径规划的终点
    val targetPoint = try {
      MapService.findPointByName(request.sceneId, request.mapName, request.groupName, request.target)
    } catch (e: RuntimeException) {
      logger.error(
        "${request.robotName} | end point ${request.target} is not in ${request.mapName} and ${request.groupName}",
      )
      return PlanResponse(
        status = ResStatus.FAILED,
        code = "T00010003",
        args = mutableListOf(request.target, request.groupName, request.mapName),
        path = mutableListOf(),
      )
    }
    val context = ContextManagerService.queryRobotContext(request.robotName)
    var narrowDir = 0
    val rotateFollow = try {
      if (request.containerType != null) {
        narrowDir =
          RobotContainerSpaceLock.queryContainerModelNarrowDir(sceneId = context.sceneId, request.containerType)
        context.robotSalverNotRotate ||
          !RobotContainerSpaceLock.checkRobotInContainerCanRotate(
            context.sceneId,
            request.groupName,
            request.containerType,
          )
      } else {
        context.robotSalverNotRotate
      }
    } catch (e: Exception) {
      return PlanResponse(
        status = ResStatus.FAILED,
        code = "T00010007",
        args = mutableListOf(request.robotName, request.containerType!!),
        path = mutableListOf(),
      )
    }

    val planRequest = PlanRequest(
      context = context,
      orderId = request.orderId,
      stepId = request.stepId,
      mapName = request.mapName,
      sceneId = request.sceneId,
      start = startPoint,
      target = targetPoint,
      startAngle = startAngle,
      loadName = request.containerType,
      loadStartAngle = request.containerStartAngle,
      loadTargetAngle = request.containerTargetAngle,
      robotAndRackNotRotateRelative = rotateFollow,
      narrowDir = narrowDir,
      targetAngle = request.targetAngle,
      targetCombinedStop = request.targetCombinedStop,
    )

    // todo 临时，后期设计调整
    if (restraint == null) {
      planRequest.context.state = TrafficStatus.PLANING
    } else {
      planRequest.forbiddenLine = restraint.forbiddenLine
      planRequest.forbiddenPoints = restraint.forbiddenPoints
      planRequest.extraCost = restraint.extraCost
      planRequest.cantRotatePoint = restraint.cantRotatePoint
    }

    // todo 4获取系统中障碍物信息、不可通行点、边信息

    // 根据机器人的地盘类型，来进行判断
    val response = when (planRequest.context.robotMotionType) {
      RobotMotionType.ADVANCE -> AdvancePlanPathAlgorithm(planRequest).execute()
      RobotMotionType.BOTH -> BothPlanPathAlgorithm(planRequest).execute()
      RobotMotionType.OMNI -> OmniPlanPathAlgorithm(planRequest).execute()
      // 按照仅前行的处理
    }
    if (response.status == ResStatus.FAILED || request.startType == PositionType.Point) {
      if (response.status != ResStatus.FAILED) {
        response.path = processStartPointOffset(response.path, request, context)
      }
      return response
    }
    // 对路径结果进行处理
    response.path =
      processPath(response.path, startInfo!!, planRequest, request.startX, request.startY, request.startAngle)
    return response
  }

  private fun processStartPointOffset(
    path: MutableList<PathAction>,
    request: RequestInfo,
    context: RobotContext,
  ): MutableList<PathAction> {
    if (path.isNotEmpty()) {
      val first = path.first()
      val curPoint = context.baseDomain.curPoint
      if (first.isMove() && curPoint != null && request.start == curPoint.pointName && request.targetCombinedStop) {
        val action = path.removeAt(0)
        val pathAction =
          action.copy(start = Position(request.startX, request.startY, action.start.pointName, action.start.posType))
        path.add(0, pathAction)
      }
    }
    return path
  }

  private fun headingNotNeedRotate(headings: MutableList<Int>, startAngle: Int): Boolean {
    for (heading in headings) {
      if (!AngleHelper.needRotate(heading, startAngle)) {
        return true
      }
    }
    return false
  }

  private fun processPath(
    path: MutableList<PathAction>,
    startInfo: StartPositionAndPreActions,
    request: PlanRequest,
    startX: Double,
    startY: Double,
    startAngle: Int,
  ): MutableList<PathAction> {
    if (path.first().isRotate()) {
      val secondPath = path[1]
      val line = startInfo.line
      if (secondPath.target.pointName == line.start.pointName) {
        val rotateAction = path.removeAt(0)
        val action = path.removeAt(0)
        val lineByName =
          MapService.findLineByName(request.sceneId, request.mapName, request.context.groupName, action.lineName)
        val headings = lineByName.findHeadings(request.context.robotMotionType)
        if (headingNotNeedRotate(headings, startAngle)) {
          val copy = action.copy(
            start = Position(x = startX, y = startY, pointName = action.start.pointName, posType = PosType.POINT),
            robotInHeading = startAngle,
          )
          val joinPath = PathInfoHelper.joinPath(mutableListOf(copy), path)
          joinPath.forEachIndexed { index, pathAction ->
            pathAction.index = index.toLong()
          }
          return joinPath
        } else if (lineByName.rotate) {
          val rotate = rotateAction.copy(
            start = rotateAction.target.copy(startX, startY),
            target = rotateAction.target.copy(startX, startY),
            robotInHeading = startAngle,
          )
          val copy = action.copy(
            start = Position(x = startX, y = startY, pointName = lineByName.start.pointName, posType = PosType.POINT),
            robotInHeading = rotate.robotOutHeading,
            target = lineByName.end,
            targetX = lineByName.end.x,
            targetY = lineByName.end.y,
            lineName = lineByName.lineName,
          )
          val joinPath = PathInfoHelper.joinPath(mutableListOf(rotate, copy), path)
          joinPath.forEachIndexed { index, pathAction ->
            pathAction.index = index.toLong()
          }
          return joinPath
        }
        logger.error("robot not rotate in line ${lineByName.lineName}")
        return mutableListOf()
      } else {
        val joinPath = PathInfoHelper.joinPath(startInfo.actions, path)
        joinPath.forEachIndexed { index, pathAction ->
          pathAction.index = index.toLong()
        }
        return joinPath
      }
    } else {
      val firstPath = path[0]
      val line = startInfo.line
      if (firstPath.target.pointName == line.start.pointName) {
        val action = path.removeAt(0)
        val copy = action.copy(
          start = Position(x = startX, y = startY, pointName = action.start.pointName, posType = PosType.POINT),
          robotInHeading = startAngle,
        )
        val joinPath = PathInfoHelper.joinPath(mutableListOf(copy), path)
        joinPath.forEachIndexed { index, pathAction ->
          pathAction.index = index.toLong()
        }
        return joinPath
      } else {
        val joinPath = PathInfoHelper.joinPath(startInfo.actions, path)
        joinPath.forEachIndexed { index, pathAction ->
          pathAction.index = index.toLong()
        }
        return joinPath
      }
    }
  }

  private fun startPositionAndPreActions(line: Line, request: RequestInfo): StartPositionAndPreActions {
    val mapName = request.mapName
    val context = ContextManagerService.queryRobotContext(request.robotName)
    val revLine = MapService.findLineByPoints(
      request.sceneId,
      mapName,
      context.groupName,
      line.end.pointName,
      line.start.pointName,
    )
    var needRotate = false
    var startAngle = 0
    val actions = mutableListOf<PathAction>()
    var startLine = line
    // 没有反向线时，线的终点就是起始点
    val angle = if (revLine == null) {
      when (context.robotMotionType) {
        RobotMotionType.ADVANCE -> {
          when (line.type) {
            LineType.STRAIGHT -> {
              if (robotNeedRotate(request.startAngle, line.enterDir, line.driveDirection)) {
                needRotate = true
                startAngle = if (line.driveDirection == MoveDirection.Backward) {
                  AngleHelper.processAngle(AngleHelper.DOWN_ANGLE + line.enterDir)
                } else {
                  line.enterDir
                }
              }
              if (line.driveDirection == MoveDirection.Backward) {
                AngleHelper.processAngle(AngleHelper.DOWN_ANGLE + line.outDir)
              } else {
                line.outDir
              }
            }
            else -> {
              val pose = line.getClosePose(request.startX, request.startY)
              if (robotNeedRotate(request.startAngle, pose.theta, line.driveDirection)) {
                needRotate = true
                startAngle = if (line.driveDirection == MoveDirection.Backward) {
                  AngleHelper.processAngle(AngleHelper.DOWN_ANGLE + pose.theta)
                } else {
                  pose.theta
                }
              }
              if (line.driveDirection == MoveDirection.Backward) {
                AngleHelper.processAngle(AngleHelper.DOWN_ANGLE + line.outDir)
              } else {
                line.outDir
              }
            }
          }
        }

        RobotMotionType.BOTH -> {
          when (line.type) {
            LineType.STRAIGHT -> request.startAngle
            else -> {
              val pose = line.getClosePose(request.startX, request.startY)
              if (AngleHelper.vectorAngle(pose.theta, request.startAngle) <=
                AngleHelper.vectorAngle(
                  AngleHelper.processAngle(pose.theta + AngleHelper.DOWN_ANGLE),
                  request.startAngle,
                )
              ) {
                if (!AngleHelper.sameAngleInFiveDegree(pose.theta, request.startAngle)) {
                  needRotate = true
                  startAngle = pose.theta
                }
                line.outDir
              } else {
                if (!AngleHelper.sameAngleInFiveDegree(
                    AngleHelper.processAngle(pose.theta + AngleHelper.DOWN_ANGLE),
                    request.startAngle,
                  )
                ) {
                  needRotate = true
                  startAngle = AngleHelper.processAngle(pose.theta + AngleHelper.DOWN_ANGLE)
                }
                AngleHelper.processAngle(line.outDir + AngleHelper.DOWN_ANGLE)
              }
            }
          }
        }
        // todo
        RobotMotionType.OMNI -> {
          request.startAngle
        }
      }
    } else {
      when (context.robotMotionType) {
        // todo
        RobotMotionType.OMNI -> {
          request.startAngle
        }

        else -> {
          when (line.type) {
            LineType.STRAIGHT -> {
              if (AngleHelper.vectorAngle(line.enterDir, request.startAngle) <=
                AngleHelper.vectorAngle(revLine.enterDir, request.startAngle)
              ) {
                if (!AngleHelper.sameAngleInFiveDegree(line.enterDir, request.startAngle)) {
                  needRotate = true
                  startAngle = line.enterDir
                }
                line.outDir
              } else {
                if (!AngleHelper.sameAngleInFiveDegree(revLine.enterDir, request.startAngle)) {
                  needRotate = true
                  startAngle = revLine.enterDir
                }
                startLine = revLine
                startLine.outDir
              }
            }

            else -> {
              val pose = line.getClosePose(request.startX, request.startY)
              val rPose = revLine.getClosePose(request.startX, request.startY)
              if (AngleHelper.vectorAngle(pose.theta, request.startAngle) <=
                AngleHelper.vectorAngle(rPose.theta, request.startAngle)
              ) {
                if (!AngleHelper.sameAngleInFiveDegree(pose.theta, request.startAngle)) {
                  needRotate = true
                  startAngle = pose.theta
                }
                line.outDir
              } else {
                if (!AngleHelper.sameAngleInFiveDegree(rPose.theta, request.startAngle)) {
                  needRotate = true
                  startAngle = rPose.theta
                }
                startLine = revLine
                revLine.outDir
              }
            }
          }
        }
      }
    }
    if (needRotate) {
      actions.add(
        PathAction(
          robotName = request.robotName,
          index = 0,
          type = if (AngleHelper.needRotate(request.startAngle, startAngle)) PathType.ROTATE else PathType.SECTOR,
          mapName = mapName,
          groupName = context.groupName,
          targetX = request.startX,
          targetY = request.startY,
          start = Position(
            x = request.startX,
            y = request.startY,
            pointName = startLine.start.pointName,
            posType = PosType.POINT,
          ),
          target = startLine.start.copy(request.startX, request.startY),
          lineName = startLine.lineName,
          robotInHeading = request.startAngle,
          robotOutHeading = startAngle,
          containerName = request.containerType,
          containerInHeading = request.containerStartAngle,
          containerOutHeading = request.containerStartAngle,
        ),
      )
      actions.add(
        PathAction(
          robotName = request.robotName,
          index = 1,
          type = PathInfoHelper.typeConvert(startLine.type),
          mapName = mapName,
          groupName = context.groupName,
          targetX = startLine.end.x,
          targetY = startLine.end.y,
          start = Position(
            x = request.startX,
            y = request.startY,
            pointName = startLine.start.pointName,
            posType = PosType.POINT,
          ),
          target = startLine.end,
          lineName = startLine.lineName,
          robotInHeading = startAngle,
          robotOutHeading = angle,
          containerName = request.containerType,
          containerInHeading = request.containerStartAngle,
          containerOutHeading = request.containerStartAngle,
        ),
      )
    } else {
      actions.add(
        PathAction(
          robotName = request.robotName,
          index = 0,
          type = PathInfoHelper.typeConvert(startLine.type),
          mapName = mapName,
          groupName = context.groupName,
          targetX = startLine.end.x,
          targetY = startLine.end.y,
          start = Position(
            x = request.startX,
            y = request.startY,
            pointName = startLine.start.pointName,
            posType = PosType.POINT,
          ),
          target = startLine.end,
          lineName = startLine.lineName,
          robotInHeading = request.startAngle,
          robotOutHeading = angle,
          containerName = request.containerType,
          containerInHeading = request.containerStartAngle,
          containerOutHeading = request.containerStartAngle,
        ),
      )
    }
    return StartPositionAndPreActions(
      line = startLine,
      angle = angle,
      actions = actions,
    )
  }

  private fun robotNeedRotate(startDir: Int, enterDir: Int, driveDir: MoveDirection): Boolean = !(
    AngleHelper.sameAngleInFiveDegree(startDir, enterDir) ||
      (
        driveDir == MoveDirection.Backward &&
          AngleHelper.sameAngleInFiveDegree(
            startDir,
            AngleHelper.processAngle(enterDir + AngleHelper.DOWN_ANGLE),
          )
        )
    )

  fun rePlanPath(request: RequestInfo, restraint: Restraint): PlanResponse = planPath(request, restraint)

  /**
   *  路径规划外部调用接口
   * */
  fun planPath(request: RequestInfo): PlanResponse = planPath(request, null)

  private fun valid(request: RequestInfo): Boolean {
    // 对请求信息进行校验

    return true
  }
}

class PlanRequest(
  val context: RobotContext, // 机器人上下文信息
  val orderId: String, // 订单号
  val stepId: String, // 步骤号
  val mapName: String, // 地图编码
  val sceneId: String, // 场景编码
  val start: Point, // 起始点 || 不可为空
  val target: Point, // 目标点|| 不可为空，若后期需要，可改为坐标
  val startAngle: Int, // 机器人当前角度
  var targetAngle: Int? = null, // 车头角度约束
  val loadName: String?, // 货架编码
  val loadStartAngle: Int, // 货架角度
  val loadTargetAngle: Int, // 终点货架角度
  val robotAndRackNotRotateRelative: Boolean, // 随动 - true, or false
  val narrowDir: Int, // 窄边的偏差角度
  val targetCombinedStop: Boolean,
  var forbiddenPoints: MutableList<String> = mutableListOf(), // 不可通行点集合
  var forbiddenLine: MutableList<String> = mutableListOf(),
  var extraCost: MutableMap<String, Int> = mutableMapOf(),
  var cantRotatePoint: MutableList<String> = mutableListOf(),
)

data class StartPositionAndPreActions(val line: Line, val angle: Int, val actions: MutableList<PathAction>)