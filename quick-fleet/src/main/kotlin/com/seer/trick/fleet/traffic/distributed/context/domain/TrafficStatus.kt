package com.seer.trick.fleet.traffic.distributed.context.domain

/**
 * 机器人交控状态
 * */
enum class TrafficStatus {

  IDLE, // 空闲状态，在初始化完成后
  PLANING, // 规划状态，在进行路径规划过程中，处于规划状态
  RUNNING, // 运行状态，任务规划完成，或在任务执行过程中，处于运行状态
  STOP, // 停止状态，在任务执行过程中，任务取消完成后，状态处于停止状态， 任务执行完成后，状态处于完成状态
  DEADLOCK, // 死锁状态，在任务执行过程中，任务被阻塞后，进入解死锁流程时的状态
}

enum class RobotStatus {

  IDLE, // 空闲
  WORK, // 工作
  LOCK, // 锁定
  INTERRUPT, // 打断
  ERROR, // 错误
  OFFLINE, // 不在线
}

/**
 *  机器人的运动类型
 *  1、前进
 *  2、前进、后退
 *  3、全向
 * */
enum class RobotMotionType {
  ADVANCE, // 单向
  BOTH, // 双向
  OMNI, // 全向
}

/**
 * 机器人的地盘类型
 * */
enum class ChassisType {
  DIFF, // 差速
  STEER, // 舵轮
  OMNI, // 麦轮
}