package com.seer.trick.fleet.domain

import com.fasterxml.jackson.annotation.JsonProperty
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.fleet.order.StepExecuteDigest
import com.seer.trick.fleet.service.MoveActionRuntime
import com.seer.trick.fleet.service.NavTaskRuntime
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.RobotService.NICKNAME
import com.seer.trick.fleet.traffic.TrafficTaskRuntime
import java.util.*

/**
 * 机器人自身上报的信息
 */
data class RobotSelfReport(
  @JvmField
  val error: Boolean = false, // 是否有错，如机器人离线
  @JvmField
  val errorMsg: String? = null, // 错误原因
  @JvmField
  val timestamp: Date = Date(), // 获取机器人报告的时间
  @JvmField
  val main: RobotSelfReportMain? = null, // 主要字段
  @JvmField
  val stand: RobotStand? = null, // 机器人当前位置
  @JvmField
  val rawReport: EntityValue? = null, // 原始报文
)

/**
 * 机器人自身上报的信息，主要、通用字段
 */
data class RobotSelfReportMain(
  @JvmField
  val battery: Double? = null, // 电池电量 0..1
  @JvmField
  val x: Double? = null, // 机器人位置 x
  @JvmField
  val y: Double? = null, // 机器人位置 y
  @JvmField
  val direction: Double? = null, // 机器人车头朝向（弧度）
  @JvmField
  val currentMap: String? = null, // 当前地图名
  @JvmField
  val currentMapMd5: String? = null, // 当前地图 MD5
  @JvmField
  val currentMapNotMatched: Boolean? = false, // 与机器人所在组的同名地图的 MD5 是否相等
  @JvmField
  val currentPoint: String? = null, // 当前站点名称
  @JvmField
  val blocked: Boolean? = null, // 是否被阻挡（障碍物、其他机器人、含义复杂……）
  @JvmField
  val blockedReason: Int? = null, // 被阻挡的原因,
  // 0 = Ultrasonic (超声), 1 = Laser (激光), 2 = Fallingdown (防跌落传感器), 3 = Collision (碰撞传感器),
  // 4 = Infrared (红外传感器), 5 = Lock（锁车开关），6 = 动态障碍物， 7 = 虚拟激光点，8 = 3D 相机
  @JvmField
  val charging: Boolean? = null, // 充电中
  @JvmField
  val emergency: Boolean? = null, // 急停按下
  @JvmField
  val softEmc: Boolean? = null, // 软急停按下
  @JvmField
  val relocStatus: RelocStatus? = null, // 重定位状态
  @JvmField
  val relocStatusLabel: String? = null, // 重定位状态描述
  @JvmField
  val confidence: Double? = null, // 定位置信度 0..1
  @JvmField
  val todayOdo: Double? = null, // 今日里程
  @JvmField
  val isControlled: Boolean? = null, // 机器人控制权是否被抢占，可能是被本系统抢占也可能是被人工或者其他系统抢占
  @JvmField
  val controller: String? = null, // 控制权所有者名称
  @JvmField
  val controlledByFleet: Boolean = isControlled == true && controller == NICKNAME, // 受调度控制
  @JvmField
  val noControlByFleet: Boolean = !controlledByFleet, // 不受调度控制
  @JvmField
  val controlledByFleetOrFree: Boolean = controller == NICKNAME || isControlled == false, // 受调度控制或自由
  @JvmField
  val alarms: List<RobotAlarm>? = null, // 告警
  @JvmField
  val loadRelations: List<RobotLoadRelation>? = null, // 机器人载货情况
  @JvmField
  val velocity: Double? = null, // 机器人在机器人坐标系的 x 方向的直线速度, 单位 m/s，正值是正走，负值则倒走
  @JvmField
  val rotateVelocity: Double? = null, // 机器人角速度
  @JvmField
  val timestamp: Date? = null, // 获取机器人位置的时间
  @JvmField
  val moveStatusInfo: String? = null, // 机器人导航信息
  @JvmField
  val bins: List<RobotSelfBin>? = null, // 机器人上的容器信息
)

/**
 * 机器人告警
 */
data class RobotAlarm(
  @JvmField
  val level: RobotAlarmLevel = RobotAlarmLevel.Info,
  @JvmField
  val code: String? = null,
  @JvmField
  val message: String = "",
  @JvmField
  val times: Int? = null,
  @JvmField
  val timestamp: Date = Date(),
)

/**
 * 机器人告警级别
 */
enum class RobotAlarmLevel {
  Info,
  Warning,
  Error,
  Fatal,
}

/**
 * 机器人自身配置参数
 */
data class RobotSelfConfig(
  @JvmField
  val maxSpeed: Double? = null, // 最大速度（空载）
  @JvmField
  val maxBackSpeed: Double? = null, // 最大后退速度（空载）
  @JvmField
  val maxRotSpeed: Double? = null, // 最大旋转速度（空载）
  @JvmField
  val loadedMaxSpeed: Double? = null, // 最大速度（载货后）
  @JvmField
  val loadedMaxBackSpeed: Double? = null, // 最大后退速度（载货后）
  @JvmField
  val loadedMaxRotSpeed: Double? = null, // 最大旋转速度（载货后）
)

/**
 * 一个机器人的信息，给前端用，汇总所有信息
 */
@Suppress("unused")
class RobotUiReport(
  @JvmField
  val robotName: String,
  @JvmField
  val offDuty: Boolean,
  @JvmField
  val config: SceneRobot,
  @JvmField
  val groupId: Int,
  @JvmField
  val groupName: String,
  @JvmField
  val online: Boolean,
  @JvmField
  val selfReport: RobotSelfReport?,
  @JvmField
  val lastFetchError: String?,
  @JvmField
  val stopAutoConnect: Boolean, // 停止自动重连
  @JvmField
  val collisionModel: Polygon, // 机器人碰撞模型（机器人坐标系）
  @JvmField
  val orderRecord: RobotOrderUiReport,
  @JvmField
  val isMaster: Boolean, // 调度是否能控
  @JvmField
  val usingLiftId: Int?,
)

/**
 * RobotRuntime 中与运单处理相关的字段。并且变成序列化友好的形式。给前端用。
 */
data class RobotOrderUiReport(
  val offDuty: Boolean,
  val cmdStatus: RobotExecuteStatus,
  val faultMsg: String?,
  val orders: List<String>,
  val withBzOrder: Boolean,
  val autoOrder: String?,
  val bins: List<RobotBin>,
  val idleFrom: Date?,
  val chargingOrderDoneOn: Date?,
  val reportingChargingOn: Date?,
  val executingSelected: StepExecuteDigest?,
  val orderReject: RejectReason?,
  val stepReject: RejectReason?,

  // 交管规划信息
  val trafficReady: Boolean?,
  val pendingTrafficTask: TrafficTaskRuntime?,
  val robotBlockedBy: List<String>?, // 阻挡当前机器人的其他机器人列表
  val containerBlockedBy: List<String>?, // 阻挡当前机器人的其他货架列表
  val travelledPointNames: List<String>?, // 已经经过的点位名称列表
  val unTravelPointNames: List<String>?, // 未经过的点位名称列表
  val spaceResources: List<SpaceResource>?, // 资源占用情况
  val recentReq3066s: List<NavTaskRuntime>, // 最近 3 条 3066 指令

  val moveActions: List<MoveActionRuntime>,
  val extra: Any? = null,
)

/**
 * 机器人当前位置。结合地图估测配置。
 * TODO TV 加货架角度
 */
data class RobotStand(
  val x: Double, // 位置坐标 x
  val y: Double, // 位置坐标 y
  val theta: Double, // 朝向
  val areaId: Int, // 所在区域 id
  val areaName: String? = null, // 所在区域名
  val mapName: String, // 当前地图
  val pointName: String? = null, // 当前点位名称，可能为空，如果不在点位上
  val pathPositions: List<PathPosition>? = null, // 距离机器人点位最近的路径集
  val velocity: Double? = null, // 直线速度
  val rotateVelocity: Double? = null, // 旋转角速度
  val timestamp: Date = Date(), // 获取机器人位置的时间
  val collisionShape: Polygon? = null, // 碰撞框，注意是世界坐标系！！TODO ，去掉
) {
  override fun toString(): String = "$pointName"
}

/**
 * 一个机器人“碰撞模型”
 */
data class RobotCollisionModel(
  val shapes: List<RobotCollisionShape> = emptyList(), // 一组形状
  val bound: Polygon = Polygon(), // 包括所有形状的多边形
  // TODO 膨胀
) {

  /**
   * 形状为空、点集为空，面积为空
   */
  fun isEmpty() = shapes.isEmpty() || bound.isEmpty() || shapes.first().polygon.isEmpty()
}

/**
 * 机器人碰撞模型的基础形状
 */
data class RobotCollisionShape(
  val type: RobotCollisionShapeType = RobotCollisionShapeType.Rect, // 形状类型，暂时只支持矩形
  val polygon: Polygon = Polygon(), // 形状参数
)

/**
 * 机器人碰撞模型的基础形状的类型
 */
enum class RobotCollisionShapeType {
  Rect, // 矩形
}

/**
 * 机器人身上一个货物的尺寸形状和位置等信息。坐标系是地图坐标。
 */
data class RobotLoadRelation(
  val type: String? = null, // 货物类型
  val id: String? = null, // 货物 ID，一般为容器编号
  val points: List<Point2D> = emptyList(), // 形状
  val direction: Double = 0.0, // 方向
)

/**
 * 机器人执行运单的状态
 */
enum class RobotExecuteStatus {
  Idle,
  Moving, // 有当前运单，rename Executing
  Failed,
}

/**
 * 机器人身上库位状态
 */
data class RobotBin(
  val index: Int,
  val status: RobotBinStatus = RobotBinStatus.Empty,
  val orderId: String? = null, // 与此库位关联的运单
  val containerId: String? = null, // 与此库位关联的容器编号
) {
  /**
   * 跟 RBK 交互使用的库位 id
   */
  fun rbkBinId(rr: RobotRuntime) = if (isPlusOne(rr)) 999 else index

  /**
   * 所在的背篓层，第一层返回 1
   */
  fun binNo(rr: RobotRuntime): Int = if (isPlusOne(rr)) 999 else index + 1

  /**
   * 是否是 N + 1 的库位
   */
  fun isPlusOne(rr: RobotRuntime): Boolean = rr.config.plusOne && index == rr.bins.size - 1
}

/**
 * 机器人身上库位状态
 */
enum class RobotBinStatus {
  Empty, // 空
  Reserved, // 占用，即将放货到此库位
  Filled, // 有货
  Cancelled, // 机器人载货后运单被取消，货物需要人工处理
}

enum class RelocStatus {
  Init, // 重定位初始化中
  Success, // 重定位成功
  Relocing, // 正在重定位
  Loading, // 地图载入中
}

/**
 * 表示路径上的一个位置
 */
data class PathPosition(
  val pathId: Long, // 所在路径 id
  val pathKey: String, // 所在路径 key
  val pathPercentage: Double, // 在路径上的位置
  val pathDistance: Double, // 机器人档位位置距离此路径的距离
  val fromPointName: String,
  val toPointName: String,
) {
  override fun toString(): String = "$pathKey+$pathPercentage%"
}

/**
 * 机器人上报的库位
 */
data class RobotSelfBin(
  val rbkBinId: Int, // 源数据是 rbk 接口返回的字符串 container_name，从 ‘0’ 开始，支持 N+1 则货叉索引是 999
  val containerId: String = "",
  val desc: String = "",
  val occupied: Boolean = false,
)

data class CurrentLockResult(
  val locked: Boolean, // 当前控制权是否被抢占
  @JsonProperty("nick_name") val nickName: String?, // 控制权所有者昵称信息
)

/**
 * 机器人与货物的碰撞模型
 */
data class RobotLoadCollisionModel(
  // 机器人底盘模型
  val robotWidth: Double,
  val robotHead: Double,
  val robotTail: Double,
  // 安全距离配置
  val safeDistHead: Double, // 安全距离（前）
  val safeDistTail: Double, // 安全距离（后）
  val safeDistLeft: Double, // 安全距离（左）
  val safeDistRight: Double, // 安全距离（右）
  // 货物
  val loaded: Boolean, // 是否有货,并且货物尺寸大于机器人
  val containerTypeName: String?, // 容器类型名称
  val loadRotatable: Boolean = false, // 货物是否能旋转（具有旋转顶升机构）
  val loadOuterWidth: Double = 0.0, // 货物外宽度（y 方向边的长度）
  val loadOuterLength: Double = 0.0, // 货物外长度（x 方向边的长度）
  val loadInitTheta: Double = 0.0, // 货物初始角度（相对于机器人坐标系）
  val offsetX: Double = 0.0, // 货物中心距离机器人中心的偏移（x 方向，即车头方向）
)