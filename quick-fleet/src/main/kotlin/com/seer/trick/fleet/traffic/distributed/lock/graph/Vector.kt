package com.seer.trick.fleet.traffic.distributed.lock.graph


import com.fasterxml.jackson.annotation.JsonIgnore
import kotlin.math.*
/**
 * 向量
 * */
class Vector(val x: Double, val y: Double) {

  /**
   * 向量的模， 计算距离
   */
  @JsonIgnore
  fun getMagnitude(): Double {
    return sqrt(x * x + y * y)
  }

  /**
   * 是否为零向量
   */
  fun zeroVector(): <PERSON>olean {
    return x == 0.0 && y == 0.0
  }

  /**
   * 归一化
   * */
  fun normalize(): Vector {
    if (getMagnitude() == 0.0) {
      return Vector(0.0, 0.0)
    }
    return Vector(x / getMagnitude(), y / getMagnitude())
  }

  /**
   *  向量加法
   * */
  fun add(vector: Vector): Vector {
    return Vector(x + vector.x, y + vector.y)
  }

  /**
   *  向量减法
   * */
  fun subtract(vector: Vector): Vector {
    return Vector(x - vector.x, y - vector.y)
  }

  /**
   * 向量乘法，即向量放
   * */
  fun multiply(scalar: Double): Vector {
    return Vector(x * scalar, y * scalar)
  }

  /**
   *  向量除法，即向量缩
   * */
  fun divide(scalar: Double): Vector {
    if (scalar == 0.0) {
      throw IllegalArgumentException("Cannot divide by zero")
    }
    return Vector(x / scalar, y / scalar)
  }

  /**
   * 计算点积， 垂直为 0
   * */
  fun dot(vector: Vector): Double {
    return x * vector.x + y * vector.y
  }

  /**
   * 计算叉乘， 平行为 0
   * */
  fun cross(vector: Vector): Double {
    return x * vector.y - y * vector.x
  }

  /**
   * 法向量
   * */
  fun normal(v: Vector): Vector {
    return Vector(v.y - y, x - v.x)
  }

  /**
   * 计算投影
   * */
  fun projection(vector: Vector): Vector {
    return vector.multiply(this.dot(vector) / vector.getMagnitude() .pow(2))
  }

  // 计算夹角
  fun angleTo(vector: Vector): Double {
//    return atan2((y - vector.y), (x - vector.x))
    return atan2((vector.y - y), (vector.x - x))

  }

  // 旋转
  fun rotate(angle: Double): Vector {
    val cos = cos(angle)
    val sin = sin(angle)
    return Vector(x * cos - y * sin, x * sin + y * cos)
  }

  /**
   * 计算距离
   * */
  fun distanceTo(vector: Vector): Double {
    return sqrt((x - vector.x) .pow(2) + (y - vector.y) .pow(2))
  }

  fun copy(): Vector {
    return Vector(x, y)
  }

  override fun toString(): String {
    return "Vector(x=$x, y=$y)"
  }


}