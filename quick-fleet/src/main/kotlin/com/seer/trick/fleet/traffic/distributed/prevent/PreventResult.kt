package com.seer.trick.fleet.traffic.distributed.prevent

import com.seer.trick.fleet.traffic.distributed.plan.model.PathAction
import com.seer.trick.fleet.traffic.distributed.plan.model.PathType
import org.slf4j.LoggerFactory

/**
 *  结果处理
 * */
class PreventResult {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   *  结果计算，机器人可以申请到的路径
   * */
  fun processRequest(prevent: PreventRequestInfo): MutableMap<String, PreventResponse> {
    val result = mutableMapOf<String, PreventResponse>()
//    logger.debug(
//      "process result |reserve table is ${LogParseHelper.reserveTableLog(prevent)} | disable table is ${
//        LogParseHelper.disableTableLog(
//          prevent,
//        )
//      }",
//    )
    for (robot in prevent.runningRobots) {
      val reserveTable = robot.reserveTable
      val disableTable = robot.disableTable
      if (reserveTable.isEmpty()) {
        result[robot.robotName] =
          PreventResponse(
            index = -1, // -1 表示不限制
            reserveTable = mutableMapOf(),
            disableTable = mutableMapOf(),
            orderNo = robot.orderNo,
            stepNo = robot.stepNo,
            version = prevent.version,
            blockRobot = mutableListOf(),
            priority = robot.priority,
            flowRobots = mutableListOf(),
            deadlocks = robot.deadlocks,
          )
        continue
      }
      val min = disableTable.keys.minOrNull()
      if (min == null) {
        result[robot.robotName] =
          PreventResponse(
            index = -1, // -1 表示不限制
            reserveTable = reserveTable,
            disableTable = disableTable,
            orderNo = robot.orderNo,
            stepNo = robot.stepNo,
            version = prevent.version,
            blockRobot = mutableListOf(),
            priority = robot.priority,
            flowRobots = robot.flowRobots,
            deadlocks = robot.deadlocks,
          )
        continue
      }
      val map = disableTable[min]
      logger.info(
        "${robot.robotName} in ${
          if (robot.allocateIndex == -1L && min == 0) {
            reserveTable[min]?.start?.pointName
          } else {
            reserveTable[min]?.target?.pointName
          }
        } point wait ${map?.keys}",
      )
      result[robot.robotName] = PreventResponse(
        index = min,
        reserveTable = reserveTable,
        disableTable = disableTable,
        orderNo = robot.orderNo,
        stepNo = robot.stepNo,
        version = prevent.version,
        blockRobot = map?.keys?.toMutableList() ?: mutableListOf(),
        priority = robot.priority,
        flowRobots = robot.flowRobots,
        deadlocks = robot.deadlocks,
      )
    }
    checkRing(result)
    return result
  }

  // 环检测破坏
  private fun checkRing(result: MutableMap<String, PreventResponse>) {
    result.forEach { (robot, response) ->
      val blockRobot = response.blockRobot
      val queue: MutableList<String> = mutableListOf()
      queue.addAll(blockRobot)
      val close: MutableList<String> = mutableListOf()
      while (queue.isNotEmpty()) {
        val first = queue.removeFirst()
        close.add(first)
        val blockRobot1 = result[first]?.blockRobot
        if (blockRobot1 != null && blockRobot1.contains(robot)) {
          logger.debug("$robot in block robots $blockRobot1")
          result[first]?.blockRobot?.remove(robot)
          val disableTable = result[first]?.disableTable
          if (disableTable != null) {
            for (table in disableTable) {
              table.value.remove(robot)
            }
          }
          break
        } else if (blockRobot1 != null) {
          queue.addAll(blockRobot1.filter { !close.contains(it) })
        }
      }
    }
  }

  /**
   *  对于计算的跟随情况，未达到冲突范围的后处理
   * */
  fun processFlow(prevent: PreventRequestInfo, preRotate: Boolean): MutableMap<String, PreventResponse> {
    val runningRobots = prevent.runningRobots
    val robotMap =
      runningRobots.filter { it.flowRobots.isNotEmpty() }.sortedBy { it.priority }.associateBy { it.robotName }
    if (robotMap.isNotEmpty()) {
      for ((robotName, robot) in robotMap) {
        val flowRobots = robot.flowRobots
        robot.flowRobots.clear()
        for (flowRobot in flowRobots) {
          // 检测两机器人的冲突范围
          val robotB = robotMap[flowRobot] ?: continue
          if (robotB.priority < robot.priority ||
            robotB.flowRobots.isEmpty() ||
            !robotB.flowRobots.contains(robotName)
          ) {
            continue
          }
          robotB.flowRobots.remove(robotName)
          val conflictNode = checkFlowConflict(robot, robotB) ?: continue
          processConflict(conflictNode, robot, robotB, preRotate)
        }
      }
    }
    return processRequest(prevent)
  }

  private fun processConflict(
    conflictNode: ConflictNode,
    robotA: PreventRobotInfo,
    robotB: PreventRobotInfo,
    preRotate: Boolean,
  ) {
    val indexAs = if (robotA.robotName == conflictNode.robotA) conflictNode.indexA else conflictNode.indexB
    val indexBs = if (robotA.robotName == conflictNode.robotA) conflictNode.indexB else conflictNode.indexA
    val startConflictIndexA = robotA.reserveTable[indexAs.first()]?.index
    val startConflictIndexB = robotB.reserveTable[indexBs.first()]?.index

    if (startConflictIndexA == null || startConflictIndexB == null) {
      logger.error(
        "start ${robotA.robotName} conflict index is $startConflictIndexA or" +
          " start ${robotB.robotName} conflict index is $startConflictIndexB",
      )
      return
    }
    if (startConflictIndexA <= robotA.allocateIndex) {
      // A 在冲突中
      processConflictResult(conflictNode, robotA, robotB, preRotate)
    } else if (startConflictIndexB <= robotB.allocateIndex) {
      // B 在冲突中
      processConflictResult(conflictNode, robotB, robotA, preRotate)
    }
    // 跟随还不在冲突中，这个时候不好判断谁先谁后进入冲突范围，待后面进入后再计算
    if (conflictNode.type == ConflictType.FLOW) {
      robotA.flowRobots.add(robotB.robotName)
      robotB.flowRobots.add(robotA.robotName)
    }
  }

  private fun processConflictResult(
    conflictNode: ConflictNode,
    robotA: PreventRobotInfo,
    robotB: PreventRobotInfo,
    preRotate: Boolean,
  ) {
    val result = oneInConflict(conflictNode, robotA, robotB, preRotate)
    if (result.isNotEmpty()) {
      for (j in 0 until result.size step 2) {
        val (robotAName, indexA) = result[j] // 走
        val indexB = result[j + 1].second // 等
        // 处于冲突中，将其冲突的删除，并将所有相关的冲突节点删除
        if (indexA == -1) {
          return
        }
        val mutableMap = robotB.disableTable.getOrDefault(indexB, mutableMapOf())
        mutableMap[robotAName] = indexA
        robotB.disableTable[indexB] = mutableMap
      }
    }
  }

  private fun oneInConflict(
    conflictNode: ConflictNode,
    robotA: PreventRobotInfo,
    robotB: PreventRobotInfo,
    preRotate: Boolean,
  ): MutableList<Pair<String, Int>> {
    val result: MutableList<Pair<String, Int>> = mutableListOf()
    val tableA = robotA.reserveTable
    val tableB = robotB.reserveTable
    val indexAs = if (robotA.robotName == conflictNode.robotA) conflictNode.indexA else conflictNode.indexB
    val indexBs = if (robotA.robotName == conflictNode.robotA) conflictNode.indexB else conflictNode.indexA
    val differ = indexAs.first() - indexBs.first()
    for (i in indexAs) {
      val pathA = tableA[i]
      if (pathA != null && pathA.isRotate()) {
        var j = 1
        while ((i - differ - j) >= 0 &&
          pathA.runLock?.checkCollision(tableB[i - differ - j]?.runLock!!) == true
        ) {
          j++
        }
        if ((i - differ - j) < 0) {
          logger.error("${robotA.robotName} and ${robotB.robotName} in flow conflict !!")
          return mutableListOf(robotA.robotName to -1, robotB.robotName to -1)
        }
        if (j > 1 && (i - differ - j) >= 0) {
          result.add(robotA.robotName to i)
          val action = tableB[i - differ - j]
          val bj = if (preRotate &&
            action != null &&
            (i - differ - j) > 0 &&
            (action.type == PathType.ROTATE || action.type == PathType.TURNING)
          ) {
            i - differ - j - 1
          } else {
            i - differ - j
          }
          result.add(robotB.robotName to bj)
          result.add(robotB.robotName to i - differ - j)
        }
      }
    }
    return result
  }

  private fun checkFlowConflict(robotA: PreventRobotInfo, robotB: PreventRobotInfo): ConflictNode? {
    val tableA = robotA.reserveTable
    val tableB = robotB.reserveTable
    for (ai in tableA.keys.min() until tableA.size) {
      val pathActionA = tableA[ai]
      if (pathActionA?.isRotate() == true) continue
      for (bi in tableB.keys.min() until tableB.size) {
        val pathActionB = tableB[bi]
        if (pathActionB?.isRotate() == true) continue
        // 检测是否存在冲突
        if (pathActionA?.target?.pointName.equals(pathActionB?.target?.pointName)) {
          return buildConflictArea(robotA.robotName, tableA, robotB.robotName, tableB, ai, bi, robotA.mapName)
        }
      }
    }
    return null
  }

  private fun buildConflictArea(
    robotA: String,
    tableA: MutableMap<Int, PathAction>,
    robotB: String,
    tableB: MutableMap<Int, PathAction>,
    ai: Int,
    bi: Int,
    mapName: String,
  ): ConflictNode? {
    val maxA = tableA.size - 1
    val maxB = tableB.size - 1
    // 冲突存在两种情况，一种是 A、B 同向移动，另一种是 A、B 逆向移动
    val points: MutableList<String> =
      mutableListOf(tableA[ai]?.target?.pointName!!)
    val indexA = mutableListOf(ai)
    val indexB = mutableListOf(bi)
    if (ai < maxA &&
      bi < maxB &&
      (
        tableA[ai + 1]?.target?.pointName.equals(tableB[bi + 1]?.target?.pointName) ||
          (
            tableA[ai + 1]?.isRotate() == true &&
              tableA[ai + 2]?.target?.pointName.equals(tableB[bi + 1]?.target?.pointName)
            ) ||
          tableB[bi + 1]?.isRotate() == true &&
          tableB[bi + 2]?.target?.pointName.equals(tableA[ai + 1]?.target?.pointName)
        )
    ) {
      // 表示同向移动
      var a = ai + 1
      var b = bi + 1
      if (a <= maxA && tableA[a]?.isRotate() == true) {
        indexA.add(a++)
      }
      if (b <= maxB && tableB[b]?.isRotate() == true) {
        indexB.add(b++)
      }
      while (a <= maxA && b <= maxB && tableA[a]?.target?.pointName.equals(tableB[b]?.target?.pointName)) {
        points.add(tableA[a]?.target?.pointName!!)
        indexA.add(a++)
        indexB.add(b++)
      }
      if (a <= maxA && tableA[a]?.isRotate() == true) indexA.add(a)
      if (b <= maxB && tableB[b]?.isRotate() == true) indexB.add(b)
      return ConflictNode(
        robotA = robotA,
        robotB = robotB,
        conflictPoint = points,
        indexA = indexA,
        indexB = indexB,
        mapName = mapName,
        type = ConflictType.FLOW,
      )
    }
    return null
  }
}