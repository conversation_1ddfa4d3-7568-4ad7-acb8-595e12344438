package com.seer.trick.fleet.traffic.distributed.lock.graph

class BoundingBox {

  var left: Double = Double.NaN
  var top: Double = Double.NaN
  var right: Double = Double.NaN
  var bottom: Double = Double.NaN

  constructor()
  constructor(points: List<Vector>) {
    this.buildOutBoundingBox(points)
  }

  constructor(left: Double, top: Double, right: Double, bottom: Double) {
    this.left = left
    this.top = top
    this.right = right
    this.bottom = bottom
  }

  // 和当前区域是否相交
  fun intersect(box: BoundingBox): Boolean =
    !(left > box.right || right < box.left || top < box.bottom || bottom > box.top)

  fun intersect(x: Double, y: Double): Boolean = x in left..right && y in top..bottom

  // 在当前区域内
  fun onRegion(box: BoundingBox): Boolean = left < box.left && right > box.right && top > box.top && bottom < box.bottom

  fun onRegion(x: Double, y: Double): Boolean = x > left && right > x && top > y && bottom < y

  fun sameBounding(box: BoundingBox): Boolean =
    left == box.left && right == box.right && top == box.top && bottom == box.bottom

  fun buildOutBoundingBox(points: List<Vector>) {
    init()
    for (site in points) {
      val x = site.x
      val y = site.y
      if (x < left || Double.NaN.equals(left)) {
        left = x
      }
      if (x > right || Double.NaN.equals(right)) {
        right = x
      }
      if (y < bottom || Double.NaN.equals(bottom)) {
        bottom = y
      }
      if (y > top || Double.NaN.equals(top)) {
        top = y
      }
    }
  }

  private fun init() {
    left = Double.NaN
    top = Double.NaN
    right = Double.NaN
    bottom = Double.NaN
  }

  fun updateBoundingBox(box: BoundingBox) {
    if (left > box.left || Double.NaN.equals(left)) {
      left = box.left
    }
    if (top < box.top || Double.NaN.equals(top)) {
      top = box.top
    }
    if (right < box.right || Double.NaN.equals(right)) {
      right = box.right
    }
    if (bottom > box.bottom || Double.NaN.equals(bottom)) {
      bottom = box.bottom
    }
  }

  fun copy(): BoundingBox = BoundingBox(left, top, right, bottom)

  override fun toString(): String = "BoundingBox(left=$left, top=$top, right=$right, bottom=$bottom)"
  // 在当前区域内
//  fun onRegion(boundingBox: BoundingBox): Boolean {
//    val nleft = if (boundingBox.left > left) boundingBox.left else left
//    val nright = if (boundingBox.right < right) boundingBox.right else right
//    val ntop = if (boundingBox.top > top) boundingBox.top else top
//    val nbottom = if (boundingBox.bottom < bottom) boundingBox.bottom else bottom
//    return nleft < nright && ntop < nbottom
// //    return left <= region.right && right >= region.left && top <= region.bottom && bottom >= region.top;
//  }
//
//  // 在当前区域边界上
//  fun onRegionBorder(boundingBox: BoundingBox): Boolean {
//    return true
//  }
//
//  // 在当前区域外
//  fun outRegion(boundingBox: BoundingBox): Boolean {
//    return true
//  }
}