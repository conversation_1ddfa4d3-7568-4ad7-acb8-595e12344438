package com.seer.trick.fleet.order

import com.fasterxml.jackson.annotation.*
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.fleet.domain.MapPath
import com.seer.trick.fleet.domain.TransportStep
import com.seer.trick.fleet.domain.normalizeRadian
import com.seer.trick.fleet.domain.reverseAzimuth
import com.seer.trick.fleet.seer.RotationDirection
import com.seer.trick.fleet.service.*
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.containsIgnoreCase
import kotlin.math.PI
import kotlin.math.abs

object MoveActionsTransferRbk {

  private const val ANGLE_TOLERANCE = 0.02

  /**
   * 转换 rbk 的请求方式
   * 一个 MoveActionRuntime 可能对应多个路径导航请求
   * 在方法内部直接建立 MoveActionRuntime 与路径导航的关联关系
   */
  fun buildNavTasks(rr: RobotRuntime, moveActions: List<MoveActionRuntime>): List<NavTaskRuntime> {
    val resultRuntimes = mutableListOf<NavTaskRuntime>()

    moveActions.forEach { moveAction ->
      val context = createMoveActionContext(rr, moveAction)
      // 根据条件调用不同的处理方法
      when {
        // 判断是否是最后一条指令
        isFinal(context) -> resultRuntimes.addAll(handleFinal(context))
        // 判断是否旋转
        isRotation(context) -> resultRuntimes.addAll(handleRotation(context))
        // 判断是否异常
        else -> handleMovement(context)?.let { resultRuntimes.addAll(it) }
      }
    }

    return resultRuntimes
  }

  /**
   * 处理最后一条指令
   */
  private fun handleFinal(context: MoveActionContext): Collection<NavTaskRuntime> {
    val req3066List = createFinalReq(context)
    return addReq3066ToMoveAction(context, *req3066List.toTypedArray())
  }

  /**
   * 构建最终一段的 rbk 3066 指令
   */
  private fun createFinalReq(ctx0: MoveActionContext): List<Req3066> {
    // 额外处理：机器人需要转的角度 = 最终机器人角度 MoveActionReq.robotExitTheta - 路径终点角度
    val path = getPathByContext(ctx0)
    val tangent = path?.tracePoints?.last()?.tangent
    val ctx = if (tangent == null) {
      ctx0
    } else {
      // 如果是倒走，就取路径终点反方位角
      val dir = if (getMoveMethod(ctx0) == "forward") tangent.normalizeRadian() else tangent.reverseAzimuth()
      // 要用 0-2π 来算，避免 3.14 与 -3.14 之间转了 360 度
      // 从路径终点弧度 dir 转到指令终点弧度 robotExitTheta
      ctx0.copy(
        amrToTheta = calcRotateTheta(ctx0.move.rotationDirection, dir, ctx0.move.robotExitTheta.normalizeRadian()),
      )
    }

    val executingSelected = ctx.rr.executingStep ?: return emptyList()
    val step = executingSelected.getStep()

    val reqList = mutableListOf<Req3066>() // 最终要执行的 3066 指令 list
    val rbkArgsMap: MutableMap<String, Any?>? = step.rbkArgs?.let { JsonHelper.mapper.readValue(it, jacksonTypeRef()) }
    val rotDir = getRotDir(ctx)

    // 路径导航 + 取货。
    if (ctx.sourceId != "SELF_POSITION" || step.forLoad) { // 兼容原地取货
      reqList.add(buildMoveAndLoad3066(ctx, step, rbkArgsMap))
    }

    if (ctx.containerToTheta != null && ctx.rr.canRotateShelf()) {
      // 要转料架的：交管下发的 MoveActionReq 中容器的角度不为 null，且是能转料架的
      reqList += createJackPyRotation(ctx).copy(
        taskId = IdHelper.oidStr(),
        sourceId = "SELF_POSITION",
        id = "SELF_POSITION",
        startRotDir = rotDir,
        reachAngle = PI,
      )
    } else if (abs(ctx.amrToTheta) > ANGLE_TOLERANCE) {
      // 只转底盘的
      reqList += createGyByOdometerRotation(ctx).copy(
        taskId = IdHelper.oidStr(),
        sourceId = "SELF_POSITION",
        id = "SELF_POSITION",
        startRotDir = rotDir,
        reachAngle = PI,
      )
    }

    // 追加放货
    if (step.forUnload) {
      reqList.add(buildUnload3066(ctx, step, rbkArgsMap))
      // 加一个顶升盘归零动作
      if (ctx.containerToTheta != null && ctx.rr.canRotateShelf()) { // 只要是能控制料架角度的，交管保证 containerToTheta 不为空
        reqList.add(buildZero3066(ctx))
      }
    }

    // 兼容原地动作
    if (reqList.isEmpty()) {
      reqList.add(buildSelfPosition3066(ctx, step, rbkArgsMap))
    }

    return reqList
  }

  /**
   *  处理旋转动作，采用新的指令 coding: https://seer-group.coding.net/p/order_issue_pool/assignments/issues/11970/detail
   *  {
   *  "operation": "Script",
   *  "script_args": {
   *      "robot_rotate_angle": 90, //机器人旋转角度
   *      "shelf_rotate_angle": 180, //托盘旋转角度
   *      "robot_rotate_direction": -1, //旋转方向：-1 顺时针 0 就近走劣弧 1 逆时针， 缺省时默认为0
   *      "shelf_rotate_direction": 1,  //旋转方向：-1 顺时针 0 就近走劣弧 1 逆时针， 缺省时默认为0
   *      "spin": true  //是否随动
   *  },
   *  "script_name": "jack.py"
   * }
   */
  private fun handleRotation(context: MoveActionContext): Collection<NavTaskRuntime> = addReq3066ToMoveAction(
    context,
    if (context.rr.canRotateShelf()) createJackPyRotation(context) else createGyByOdometerRotation(context),
  )

  private fun createJackPyRotation(context: MoveActionContext): Req3066 {
    val scriptArgs = mutableMapOf<String, Any>(
      "robot_rotate_angle" to context.move.robotExitTheta.normalizeRadian() * (180 / Math.PI),
      "robot_rotate_direction" to 0,
    )
    if (context.move.loadExitTheta == null) {
      // 没有容器时，走之前机器人旋转
      return createGyByOdometerRotation(context)
    } else if (isCombinedRotationSame(context)) {
      scriptArgs["spin"] = false
    } else {
      scriptArgs["spin"] = true
      scriptArgs["shelf_rotate_angle"] = context.move.loadExitTheta.normalizeRadian() * (180 / Math.PI)
      scriptArgs["shelf_rotate_direction"] = 0
    }

    return Req3066(
      sourceId = context.sourceId,
      id = context.id,
      taskId = IdHelper.oidStr(),
      // 旋转弧度，注意是基于机器人当前弧度，去旋转 moveAngle 弧度
      operation = "Script",
      scriptName = "jack.py",
      scriptArgs = scriptArgs,
    )
  }

  /**
   *  检测是否为旋转动作，机器人原地旋转或托盘原地旋转或一起原地旋转
   * */
  private fun isRotation(context: MoveActionContext): Boolean = context.sourceId == "SELF_POSITION" &&
    (
      abs(context.amrToTheta) > ANGLE_TOLERANCE ||
        (context.containerToTheta != null && abs(context.containerToTheta) > ANGLE_TOLERANCE)
      )

  /**
   *  检测是否为最后一个动作
   * */
  private fun isFinal(context: MoveActionContext): Boolean = context.move.finalAction

  /**
   * 处理移动动作
   */
  private fun handleMovement(context: MoveActionContext): List<NavTaskRuntime>? {
    val req3066 = createMovementReq(context) ?: return null
    applySpinParameter(req3066, context.spin)
    return addReq3066ToMoveAction(context, req3066)
  }

  private fun createMoveActionContext(rr: RobotRuntime, moveAction: MoveActionRuntime): MoveActionContext {
    // 如果是多储位机器人，需要将库位索引附加上去
    val bin = rr.bins.firstOrNull { it.orderId == moveAction.req.orderId }

    // 只要机器人是多储位的，无论场景机器人是不是只使用了一个储位，也需指定库位放货，否则与多储位机器人的校验内容不一致
    val rbkBinId = if (bin != null && rr.autoOrder == null && !rr.selfReport?.main?.bins.isNullOrEmpty()) {
      bin.rbkBinId(rr).toString()
    } else {
      null
    }

    val move = moveAction.req
    return MoveActionContext(
      rr = rr,
      moveAction = moveAction,
      move = move,
      spin = calculateSpin(move, rr),
      amrToTheta = calculateAmrRotateTheta(move),
      containerToTheta = calculateContainerRotateTheta(move),
      sourceId = getSourceAndDestinationIds(move).first, // TODO 是不是重复调用了
      id = getSourceAndDestinationIds(move).second,
      rbkBinId = rbkBinId,
    )
  }

  /**
   * 计算旋转参数(spin)的值
   */
  private fun calculateSpin(move: MoveActionReq, rr: RobotRuntime? = null): Boolean? = rr?.let {
    rr.sr.mapCache.getPointCacheByStand(rr)?.point?.spin
  }

  /**
   * 应用spin参数到Req3066上
   */
  private fun applySpinParameter(req3066: Req3066, spin: Boolean?) {
    req3066.spin = spin
  }

  /**
   * 将一个或多个 Req3066 添加到 MoveActionRuntime 并记录最近几条 Req3066
   */
  private fun addReq3066ToMoveAction(context: MoveActionContext, vararg reqs: Req3066): List<NavTaskRuntime> =
    reqs.map { req ->
      context.moveAction.addNavTask(req).also {
        RobotService.addRecentNavTasks(context.rr, it)
      }
    }

  /**
   *  处理组合情况：机器人原地旋转 + 货架旋转 且旋转不同步
   */
  private fun isCombinedRotationDiff(context: MoveActionContext): Boolean = context.sourceId == "SELF_POSITION" &&
    abs(context.amrToTheta) > ANGLE_TOLERANCE &&
    context.containerToTheta != null &&
    abs(context.containerToTheta) > ANGLE_TOLERANCE &&
    abs(context.amrToTheta - context.containerToTheta) > ANGLE_TOLERANCE

  /**
   *  处理组合情况：机器人原地旋转 + 货架旋转 且旋转同步
   */
  private fun isCombinedRotationSame(context: MoveActionContext): Boolean = context.sourceId == "SELF_POSITION" &&
    abs(context.amrToTheta) > ANGLE_TOLERANCE &&
    context.containerToTheta != null &&
    abs(context.containerToTheta) > ANGLE_TOLERANCE &&
    abs(context.amrToTheta - context.containerToTheta) <= ANGLE_TOLERANCE

  /**
   * 机器人原地旋转，货架不转/机器人原地旋转，货架跟着转
   */
  private fun isAmrRotation(context: MoveActionContext): Boolean =
    context.sourceId == "SELF_POSITION" && abs(context.amrToTheta) > ANGLE_TOLERANCE

  /**
   * 机器人原地不动，货架旋转
   */
  private fun isContainerRotation(context: MoveActionContext): Boolean = context.sourceId == "SELF_POSITION" &&
    context.containerToTheta != null &&
    abs(context.containerToTheta) > ANGLE_TOLERANCE

  /**
   * 创建一个机器人旋转操作的请求
   */
  private fun createGyByOdometerRotation(context: MoveActionContext) = Req3066(
    sourceId = context.sourceId,
    id = context.id,
    taskId = IdHelper.oidStr(),
    // 旋转弧度，注意是基于机器人当前弧度，去旋转 moveAngle 弧度
    moveAngle = abs(context.amrToTheta),
    speedW = if (context.amrToTheta < 0.0) -1570.0 else 1570.0, // 这里指定最大速度，实际速度受机器人限制
    skillName = "GoByOdometer", // 旋转时为固定值
    locMode = 1, // 定位模式，1 表示激光定位，0 或者其他 表示按里程定位。 默认值为 0
  )

  /**
   * 创建一个容器旋转操作的请求
   */
  private fun createContainerRotationReq(context: MoveActionContext) = Req3066(
    sourceId = context.sourceId,
    id = context.id,
    taskId = IdHelper.oidStr(),
    // 旋转弧度，注意是基于货架当前弧度，去旋转 increaseSpinAngle 弧度
    increaseSpinAngle = context.containerToTheta,
    skillName = "GoByOdometer", // 旋转时为固定值
  )

  /**
   * 创建一个移动操作的请求
   */
  private fun createMovementReq(context: MoveActionContext): Req3066? {
    val method = getMoveMethod(context)
    val rotDir = getRotDir(context)

    // 获取当前正在执行的步骤以及步骤的 location
    val executingSelected = context.rr.executingStep ?: return null
    val step = executingSelected.getStep()
    val location = step.location

    // location 转换为 pointName
    val pointName = context.rr.sr.mapCache.getPointCacheByGroupAndLoc(context.rr, location)?.point?.name ?: return null

    // 路径任务为 前置点->目标点时，或者机器人本身就在目标点的时候。目标点->目标点 的 final 动作
    // val rbkArgsMap: Map<String, Any?>? = if (ifEndPreAction(context, pointName) || context.move.final) {
    //   step.rbkArgs?.let { JsonHelper.mapper.readValue(it, jacksonTypeRef()) }
    // } else {
    //   null
    // }
    // val dispatcherArgs = createDispatcherArgs(step)
    val containerId = executingSelected.or.order.containerId

    return Req3066(
      sourceId = context.sourceId,
      id = if (context.id != "SELF_POSITION" && context.move.finalAction && context.id == pointName) {
        location
      } else {
        context.id
      },
      // 对于前置点->目标点的 moveAction，或者 final 为 true 的 moveAction，containerId 都要传
      containerId = if (ifEndPreAction(context, pointName) || context.move.finalAction) containerId else null,
      // 取放货时可以通过索引指定背篓
      // 只有料箱车（多储位机器人）在执行取、放货动作时，才需要给 rbk 传 binIndex；中间的步骤不处理 binIndex 。
      //  TODO：以后如果要将料箱车的取放货动作进一步拆解的话，再额外考虑：
      //   取货 = 外部取货 + 内部放货；
      //   放货 = 内部取货 + 外部放货。
      //   1. 内部取货：让料箱车从指定的背篓取货，需要告知 binIndex；
      //   2. 内部放货：让料箱车将容器放到指定背篓上，需要告知 binIndex；
      //   3. 外部取货：让料箱车将外部库位上的货物放到货叉上，不需要告知 binIndex；
      //   4. 外部放货：让料箱车将货叉上的货物放到指定的外部库位上，不需要告知 binIndex；
      // binIndex = context.rbkBinId,
      taskId = IdHelper.oidStr(),
      // TODO 为什么要区别处理？
      reachAngle = Math.PI,
      method = method,
      startRotDir = rotDir,
//      goodsDir = if (context.move.loadEnterTheta != null && context.move.loadExitTheta == context.move.loadEnterTheta) {
//        GeoHelper.normalizeRadian(context.move.loadEnterTheta)
//      } else {
//        null
//      },
      // dispatcherArgs = dispatcherArgs,
      // rbkArgs = rbkArgsMap?.toMutableMap(),
    )
  }

  /**
   * 路径导航 + 取货的 rbk 3066 指令
   *
   * step.forUnload 时，过滤 rbkArgs
   */
  private fun buildMoveAndLoad3066(
    ctx: MoveActionContext,
    step: TransportStep,
    rbkArgsMap: MutableMap<String, Any?>?,
  ): Req3066 {
    val rbkArgs = if (step.forUnload) null else rbkArgsMap?.toMutableMap()
    val binTask = rbkArgs?.get("binTask")?.toString()
    val operation = rbkArgs?.get("operation")?.toString().let {
      // 此方法目前仅被 createFinalReq() 调用，就算创建运单时没有指定 binTask 和 operation，则也得将其当做 Wait 处理。
      if (it.isNullOrBlank() && binTask.isNullOrBlank()) "Wait" else it
    }
    // 最后一步一定是带动作的。Wait 也是 RBK 到点后的默认动作，一定要将有效的 containerId 传给机器人。
    //  因为有些定制的业务场景中，需要单独将 goodsId 传给机器人，例如宇峰项目叉车校验货架码。
    val containerId = ctx.rr.executingStep?.or?.order?.containerId
    var binIndex: String? = null
    if (!operation.isNullOrBlank() || !binTask.isNullOrBlank()) {
      // TODO：
      //  对动作描述进行更细致的判断，提高指令的准确性：哪种车型，在执行哪个动作时，有哪些必要的参数以及期望的值。
      //  下发给指令中，不能同时存在 binTask 和 operation，否则 binTask 无效，现在创建运单时处理吧。
      if (binTask?.containsIgnoreCase("Load") == true || operation?.containsIgnoreCase("Load") == true) {
        // 料箱车要执行 取货 动作时，才将有效的 binIndex 传给它；
        // 注意：N+1 的情况下，得将数值最大的 binIndex 处理成 999。
        ctx.rbkBinId?.let { binIndex = if (it == ctx.rr.bins.size.toString() && ctx.rr.config.plusOne) "999" else it }
      }
    }

    return Req3066(
      sourceId = ctx.sourceId,
      id = ctx.id,
      taskId = IdHelper.oidStr(),
      reachAngle = PI,
      method = getMoveMethod(ctx),
      dispatcherArgs = createDispatcherArgs(step),
      rbkArgs = rbkArgs,
      containerId = containerId,
      binIndex = binIndex,
    )
  }

  /**
   * 卸货 rbk 3066 指令
   */
  private fun buildUnload3066(
    ctx: MoveActionContext,
    step: TransportStep,
    rbkArgsMap: MutableMap<String, Any?>?,
  ): Req3066 {
    val rbkArgs = rbkArgsMap?.toMutableMap()
    val binTask = rbkArgs?.get("binTask")?.toString()
    val operation = rbkArgs?.get("operation")?.toString().let {
      // 此方法目前仅被 createFinalReq() 调用，就算创建运单时没有指定 binTask 和 operation，则也得将其当做 Wait 处理。
      if (it.isNullOrBlank() && binTask.isNullOrBlank()) "Wait" else it
    }
    // 原因参考 buildMoveAndLoad3066() 的 val containerId 的备注、
    val containerId = ctx.rr.executingStep?.or?.order?.containerId
    var binIndex: String? = null
    if (!operation.isNullOrBlank() || !binTask.isNullOrBlank()) {
      // TODO：
      //  对动作描述进行更细致的判断，提高指令的准确性：哪种车型，在执行哪个动作时，有哪些必要的参数以及期望的值。
      //  下发给指令中，不能同时存在 binTask 和 operation，否则 binTask 无效，现在创建运单时处理吧。
      if (binTask?.containsIgnoreCase("Unload") == true || operation?.containsIgnoreCase("Unload") == true) {
        // 料箱车要执行 放货 动作时，才将有效的 binIndex 传给它；考虑到部分项目不会用到 containerId，所以还是得传 binIndex 。
        // 注意：N+1 的情况下，得将数值最大的 binIndex 处理成 999。
        ctx.rbkBinId?.let { binIndex = if (it == ctx.rr.bins.size.toString() && ctx.rr.config.plusOne) "999" else it }
      }
    }

    return Req3066(
      sourceId = "SELF_POSITION",
      id = "SELF_POSITION",
      taskId = IdHelper.oidStr(),
      reachAngle = PI,
      dispatcherArgs = createDispatcherArgs(step),
      rbkArgs = rbkArgs,
      containerId = containerId,
      binIndex = binIndex,
    )
  }

  /**
   * 顶升盘归零的 rbk 3066 指令
   *
   * 注意：“零” 是指底盘当前角度
   */
  private fun buildZero3066(ctx: MoveActionContext): Req3066 = Req3066(
    sourceId = "SELF_POSITION",
    id = "SELF_POSITION",
    taskId = IdHelper.oidStr(),
    reachAngle = PI,
    operation = "Script",
    scriptName = "jack.py",
    scriptArgs = mapOf(
      "shelf_rotate_angle" to ctx.move.robotExitTheta.normalizeRadian() * 180 / PI,
      "shelf_rotate_direction" to 0,
      "spin" to true,
    ),
  )

  private fun buildSelfPosition3066(
    ctx: MoveActionContext,
    step: TransportStep,
    rbkArgsMap: MutableMap<String, Any?>?,
  ): Req3066 {
    val rbkArgs = if (step.forUnload) null else rbkArgsMap?.toMutableMap()
    val binTask = rbkArgs?.get("binTask")?.toString()
    val operation = rbkArgs?.get("operation")?.toString().let {
      // 此方法目前仅被 createFinalReq() 调用，就算创建运单时没有指定 binTask 和 operation，则也得将其当做 Wait 处理。
      if (it.isNullOrBlank() && binTask.isNullOrBlank()) "Wait" else it
    }
    val containerId = ctx.rr.executingStep?.or?.order?.containerId
    var binIndex: String? = null
    if (!operation.isNullOrBlank() || !binTask.isNullOrBlank()) {
      // TODO：
      //  对动作描述进行更细致的判断，提高指令的准确性：哪种车型，在执行哪个动作时，有哪些必要的参数以及期望的值。
      //  下发给指令中，不能同时存在 binTask 和 operation，否则 binTask 无效，现在创建运单时处理吧。
      if (binTask?.containsIgnoreCase("Load") == true || operation?.containsIgnoreCase("Load") == true) {
        // 料箱车要执行 取货 动作时，才将有效的 binIndex 传给它；
        // 注意：N+1 的情况下，得将数值最大的 binIndex 处理成 999。
        ctx.rbkBinId?.let { binIndex = if (it == ctx.rr.bins.size.toString() && ctx.rr.config.plusOne) "999" else it }
      }
    }

    return Req3066(
      sourceId = ctx.sourceId,
      id = ctx.id, // 要么两个都是 SELF_POSITION，要么 sourceId、id 不是相同的点位
      taskId = IdHelper.oidStr(),
      dispatcherArgs = createDispatcherArgs(step),
      rbkArgs = rbkArgs,
      containerId = containerId,
      binIndex = binIndex,
    )
  }

  private fun getRotDir(ctx: MoveActionContext): Int = when (ctx.move.rotationDirection) {
    RotationDirection.CLOCKWISE -> -1
    RotationDirection.COUNTERCLOCKWISE -> 1
    else -> 0
  }

  private fun getMoveMethod(context: MoveActionContext): String? {
    val path = getPathByContext(context)
    val tangent = path?.tracePoints?.firstOrNull()?.tangent

    val method = getMovementMethod(tangent, context.move.robotEnterTheta)
    return method
  }

  private fun getPathByContext(context: MoveActionContext): MapPath? {
    val mapCache = context.rr.sr.mapCache.getAreaMapCacheByStandAndGroup(context.rr)
    return mapCache?.pathKeyMap?.get(MapPath.getKey(context.sourceId, context.id))?.path
  }

  /**
   * 判断是不是最后目标点的前置动作
   */
  private fun ifEndPreAction(context: MoveActionContext, endPointName: String) =
    context.move.fromPointName != context.move.toPointName &&
      context.move.toPointName == endPointName

  /**
   * 计算以当前机器人朝向为基准要旋转的弧度
   */
  private fun calculateAmrRotateTheta(move: MoveActionReq): Double =
    calcRotateTheta(move.rotationDirection, move.robotEnterTheta, move.robotExitTheta)

  /**
   * 计算 fromTheta 到 toTheta 的旋转弧度。
   * 注意：传入的两个角度范围要一样，都传 [-π,π] 或 [0,2π]。
   */
  private fun calcRotateTheta(dir: RotationDirection?, fromTheta: Double, toTheta: Double): Double {
    // 计算旋转角度，顺时针为负值，逆时针为正值
    return when (dir) {
      RotationDirection.CLOCKWISE -> {
        // 顺时针旋转：从目标朝向减去当前朝向并归一化角度为负值范围
        var deltaTheta = toTheta - fromTheta
        if (deltaTheta > 0) {
          deltaTheta -= 2 * Math.PI // 顺时针旋转，保证角度为负值
        }
        deltaTheta
      }

      RotationDirection.COUNTERCLOCKWISE -> {
        // 逆时针旋转：从目标朝向减去当前朝向并归一化角度为正值范围
        var deltaTheta = toTheta - fromTheta
        if (deltaTheta < 0) {
          deltaTheta += 2 * Math.PI // 逆时针旋转，保证角度为正值
        }
        deltaTheta
      }

      else -> {
        val moveTheta = calculateMoveTheta(fromTheta, toTheta)
        moveTheta
      }
    }
  }

  /**
   * 计算以当前货架朝向为基准要旋转的弧度
   */
  private fun calculateContainerRotateTheta(move: MoveActionReq): Double? {
    // 计算旋转角度，顺时针为负值，逆时针为正值
    return if (move.loadExitTheta == null || move.loadEnterTheta == null) {
      // 容器角度可能为空
      null
    } else if (move.rotationDirection == RotationDirection.CLOCKWISE) {
      // 顺时针旋转：从目标朝向减去当前朝向并归一化角度为负值范围
      var deltaTheta = move.loadExitTheta - move.loadEnterTheta
      if (deltaTheta > 0) {
        deltaTheta -= 2 * Math.PI // 顺时针旋转，保证角度为负值
      }
      deltaTheta
    } else if (move.rotationDirection == RotationDirection.COUNTERCLOCKWISE) {
      // 逆时针旋转：从目标朝向减去当前朝向并归一化角度为正值范围
      var deltaTheta = move.loadExitTheta - move.loadEnterTheta
      if (deltaTheta < 0) {
        deltaTheta += 2 * Math.PI // 逆时针旋转，保证角度为正值
      }
      deltaTheta
    } else {
      val moveTheta = calculateMoveTheta(move.loadEnterTheta, move.loadExitTheta)
      // logger.info("moveTheta: $moveTheta")
      moveTheta
    }
  }

  /**
   * 计算移动的角度，顺时针为 - ，逆时针为 +。取劣弧
   *
   * 优化：
   *  逆时针旋转角度 a = 终点 - 起点
   *  顺时针旋转角度 b = 起点 - 终点
   *  取 a、b 中较小的
   */
  private fun calculateMoveTheta(fromTheta: Double, toTheta: Double): Double {
    val from = fromTheta.normalizeRadian()
    val to = toTheta.normalizeRadian()
    val a = (to - from).normalizeRadian() // 逆时针
    val b = (from - to).normalizeRadian() // 顺时针
    return if (a <= b) a else -b
  }

  /**
   * 根据轨迹的角度计算前进方式
   *
   * diff.normalizeRadian() 在[π/2, 3π/2] 之间倒走， [0, π/2), (3π/2, 2π] 之间正走
   */
  private fun getMovementMethod(tangent: Double?, robotEnterTheta: Double): String? = tangent?.let {
    val diff = (robotEnterTheta - it).normalizeRadian() // [0, 2π]
    if (diff >= PI / 2 && diff <= 3 * PI / 2) {
      "backward"
    } else {
      "forward"
    }
  }

  /**
   * 获取 Source 和 Destination 的 ID
   */
  private fun getSourceAndDestinationIds(move: MoveActionReq): Pair<String, String> =
    if (move.fromPointName == move.toPointName) {
      "SELF_POSITION" to "SELF_POSITION"
    } else {
      move.fromPointName to move.toPointName
    }

  /**
   * 调度的一些参数
   */
  private fun createDispatcherArgs(step: TransportStep): Map<String, String> {
    val dispatcherArgs = mutableMapOf(
      "orgLoc" to step.location,
    )
    return dispatcherArgs
  }

  data class MoveActionContext(
    val rr: RobotRuntime,
    val moveAction: MoveActionRuntime,
    val move: MoveActionReq,
    val spin: Boolean?,
    val amrToTheta: Double,
    val containerToTheta: Double?,
    val sourceId: String,
    val id: String,
    val rbkBinId: String?,
  )
}

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Req3066(
  @JsonProperty("source_id")
  val sourceId: String, // 起始点，原地任务时为 SELF_POSITION
  @JsonProperty("id")
  val id: String, // 目标点，原地任务时为 SELF_POSITION
  @JsonProperty("task_id")
  val taskId: String, // 唯一id
  @JsonProperty("reach_angle")
  val reachAngle: Double? = null, // 到点精度。π 时经过这些点时无需调整车头方向到点位朝向上, -1 时需要将车头朝向变换到点位的朝向上
  @JsonProperty("method")
  val method: String? = null, // 正走或者倒走，forward 为正走，backward 为倒走
  @JsonProperty("move_angle")
  val moveAngle: Double? = null, // 旋转弧度，注意是基于机器人当前弧度，去旋转 moveAngle 弧度
  @JsonProperty("speed_w")
  val speedW: Double? = null, // 旋转的角速度,正数为顺时针，负数为逆时针
  @JsonProperty("skill_name")
  val skillName: String? = null, // 旋转时为固定值 GoByOdometer
  @JsonProperty("increase_spin_angle")
  val increaseSpinAngle: Double? = null, // 托盘旋转的弧度，正数则逆时针旋转，为负数顺时针旋转
  @JsonProperty("goods_dir")
  val goodsDir: Double? = null, // 货物朝向，单位角度
  @JsonProperty("loc_mode")
  val locMode: Int? = null, // 定位模式，1 表示激光定位，0 或者其他 表示按里程定位。 对 rbk 来说默认值为 0
  @JsonProperty("spin")
  var spin: Boolean? = null, // 是否随动
  @JsonProperty("start_rot_dir")
  val startRotDir: Int? = 0, // 旋转方向，-1: 顺时针转 0: 朝近的方向旋转 1: 逆时针转，默认为 0
  val percentage: Double? = null, // 路线百分比
  @JsonProperty("goodsId")
  val containerId: String? = null, // 货物id
  @JsonProperty("#containerName")
  val binIndex: String? = null, // 容器 index，从 0 开始，N+1 index 是 999
  val dispatcherArgs: Map<String, String>? = null,
  @JsonIgnore
  val rbkArgs: MutableMap<String, Any?>? = mutableMapOf(),
  @JsonProperty("operation")
  val operation: String? = null, // 操作
  @JsonProperty("script_name")
  val scriptName: String? = null, // 脚本名称
  @JsonProperty("script_args")
  val scriptArgs: Map<String, Any>? = null, // 脚本参数
) {
  // 用于添加一些动态参数，比如动作的参数
  @JsonAnyGetter
  fun anyGetter(): Map<String, Any?>? = rbkArgs
}