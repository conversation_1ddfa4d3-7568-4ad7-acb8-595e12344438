package com.seer.trick.fleet.mock.tcp

import com.seer.trick.fleet.mock.MockSeerRobotAlarm
import com.seer.trick.fleet.mock.MockSeerRobotRuntime
import com.seer.trick.fleet.mock.service.MockAlarmProcessor
import org.slf4j.LoggerFactory
import java.util.concurrent.*

/**
 * 管理仿真机器人连接的类，使用线程池来处理连接的创建和销毁。
 * TODO 移到 mock 下
 */
class ConnectionManager(var mr: MockSeerRobotRuntime) {
  private val logger = LoggerFactory.getLogger(javaClass)

  private val executor: ExecutorService = Executors.newCachedThreadPool() // 创建客户端的线程是短时间的操作，又不需要保持并发线程

  private var coffeeRbkTcpClient: CoffeeTcpClientHandler? = null
  private var coffeeRbkTcpService: MutableList<CoffeeTcpServerHandler> = mutableListOf()

  init {
    initializeHandlers() // 初始化连接处理器
  }

  /**
   * 重置连接，可以选择性地销毁连接。
   *
   * @param dispose 是否仅销毁连接，默认为false。
   */
  fun resetConnections(dispose: Boolean = false) {
    executeConnectionTasks(dispose)
  }

  /**
   * 销毁所有连接和线程池。
   */
  fun dispose() {
    resetConnections(dispose = true)
    executor.shutdown()
    executor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS)
  }

  /**
   * 异步执行连接相关任务，以避免阻塞。
   *
   * @param dispose 是否仅进行销毁操作。
   */
  private fun executeConnectionTasks(dispose: Boolean) {
    val tasks = mutableListOf<Future<*>>()
    tasks.add(
      executor.submit {
        coffeeRbkTcpClient?.dispose()
      },
    )
    coffeeRbkTcpService.forEach { server ->
      tasks.add(
        executor.submit {
          server.dispose()
        },
      )
    }
    tasks.forEach { it.get() } // 确保连接都被销毁
    if (!dispose) initializeHandlers()
  }

  fun disconnect() {
    coffeeRbkTcpClient?.disconnect()

    coffeeRbkTcpService.forEach { server ->
      server.disconnect()
    }
  }

  /**
   * 初始化连接处理器。
   */
  private fun initializeHandlers() {
    val tasks = mutableListOf<Future<*>>()

    mr.config.fleetTcpServerIp.takeIf { !it.isNullOrEmpty() }?.let {
      tasks.add(
        executor.submit {
          logger.info("Initializing CoffeeTcpClientHandler...")
          coffeeRbkTcpClient = CoffeeTcpClientHandler(mr).apply {
            init()
          }
        },
      )
    }

    mr.config.robotTcpServerPortStart?.let { portStart ->
      val portIncrements = listOf(0, 1, 2, 3, 6)
      coffeeRbkTcpService.clear()
      portIncrements.map { increment ->
        tasks.add(
          executor.submit {
            logger.info("Initializing CoffeeTcpServerHandler for port ${portStart + increment}...")
            coffeeRbkTcpService.add(
              CoffeeTcpServerHandler(portStart + increment, mr).apply {
                init()
              },
            )
          },
        )
      }
    }

    try {
      // 等待所有初始化任务完成
      tasks.forEach { it.get() }
    } catch (e: Exception) {
      MockAlarmProcessor.addOrUpdateAlarm(
        mr,
        MockSeerRobotAlarm("Error", "52500", "disconnect from dispatching system"),
      )
    }
  }
}