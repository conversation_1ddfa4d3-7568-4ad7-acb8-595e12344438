package com.seer.trick.fleet.traffic.distributed.context.domain

import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper
import com.seer.trick.fleet.traffic.distributed.plan.RequestInfo

/**
 *  记录机器人请求信息
 * */
class RequestDomain {

  var orderNo: String? = null // 请求的单号

  var stepNo: String? = null // 请求的步骤号

  var target: String? = null // 目标点|| 不可为空，若后期需要，可改为坐标

  var type: String? = null // 步骤类型

  var loadTargetAngle: Int = AngleHelper.ERROR_ANGLE // 终点货架角度

  var request: RequestInfo? = null

  var oldOrderNo: String? = null // 上一个订单号

  var oldStepNo: String? = null // 上一个步骤号

  fun updateOrder(order: String, step: String) {
    if (this.orderNo != order) {
      this.oldOrderNo = this.orderNo
      this.orderNo = order
    }
    if (this.stepNo != step) {
      this.oldStepNo = this.stepNo
      this.stepNo = step
    }
  }
}