package com.seer.trick.fleet.order

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.base.script.ScriptCenter
import com.seer.trick.base.script.ScriptExeRequest
import com.seer.trick.fleet.diagnosis.RobotOrderPrediction
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.service.MapService
import com.seer.trick.fleet.service.MapService.NOT_ACHIEVABLE_COST
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.RobotService
import com.seer.trick.fleet.service.SceneRuntime
import org.slf4j.LoggerFactory
import java.util.*

/**
 * 机器人充电管理
 * - 非充电中的机器人可以一直接单，直到电量低于 chargeOnly。
 * - 电量低于 chargeOnly 的机器人必须充电，在电量充到 chargeOk 之前，不能接业务运单。
 * - 机器人电量低于 chargeNeed 后，闲时充电。
 * - 机器人电量高于 chargeFull 后，安排停靠运单，离开充电点。
 *
 * TODO 明示：应该充电没有充电点
 */
object ChargingService {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 检查是否为强充状态；分派需要强充的机器人；可能需要打算停靠
   * 清除强充标记
   */
  fun checkForceCharging(sr: SceneRuntime) {
    if (sr.config.chargingPaused) return

    for (rr in sr.robots.values) {
      // 检查强充但电量已经充到 OK 状态的机器人，为其解除强充
      if (rr.forcedCharging && getBatteryOrZero(rr) >= getConfigOfRobot(rr).chargeOk) {
        rr.forcedCharging = false
      }
    }

    // 列出必须强充的机器人
    val mustChargingRobots = sr.robots.values.filter {
      val fromPoint = it.startPointNameForDispatching ?: return@filter false // 有起始位置才认为能强充

      val mustCharging = RobotOrderPrediction.checkRobotMustCharging(it, fromPoint) == null
      if (mustCharging) {
        // 标记为强充
        it.forcedCharging = true
      }

      mustCharging
    }
    if (mustChargingRobots.isEmpty()) return

    tryToAssign(sr, mustChargingRobots)
  }

  /**
   * 让没有运单的可充电的机器人充电
   */
  fun tryCharging(sr: SceneRuntime) {
    if (sr.config.chargingPaused) return

    // 列出可充电的机器人
    val mayChargingRobots = sr.robots.values.filter {
      RobotOrderPrediction.checkRobotMayCharging(it, it.startPointNameForDispatching) == null
    }
    if (mayChargingRobots.isEmpty()) return

    tryToAssign(sr, mayChargingRobots)
  }

  /**
   * 生成充电运单
   */
  private fun tryToAssign(sr: SceneRuntime, robots: List<RobotRuntime>) {
    if (sr.config.chargingPaused) return

    // 筛选出一种特殊情况：在充点电，但没在充电
    val (inChargingPointRobots, otherPointRobots) = robots.partition { robotInChargingPoint(it) }
    // 将所有闲置的充电点分配给未在充电点的机器人
    // TODO 分配时就检测碰撞
    val allocations = allocateChargePoints(sr, otherPointRobots)
    if (allocations.isNotEmpty()) {
      logger.info("$sr charging allocations = ${allocations.size}")
      // allocations.parallelStream().forEach { createChargeOrder(it) }
      allocations.forEach {
        // 每次都重新计算已占用点位，保证分配的充电点不会碰撞后再下发
        if (ParkAndChargeResourceHelper(sr).checkParkAndChargeCollision(it.rr, it.pointName) == null) {
          createChargeOrder(sr, it)
        }
      }
    }

    // 在充电点未充电的机器人尝试重新充电。放最后执行，避免离开充电点后，充电点被其他机器人抢占
    inChargingPointRobots.forEach {
      logger.info("$sr robot ${it.config.robotName} retry to charging")
      val cc = getConfigOfRobot(it)
      // 先去充电前置点再回到充电点
      retryCharging(
        sr,
        ChargePointsAllocation(
          it,
          it.selfReport?.main?.currentPoint!!,
          0.0,
          (it.selfReport?.main?.battery ?: 0.0) < cc.chargeOnly,
        ),
      )
    }
  }

  /**
   * 在充电点失败的机器人尝试重新充电 TODO 充电失败次数限制
   */
  private fun retryCharging(sr: SceneRuntime, allocation: ChargePointsAllocation) {
    val orderId = OrderService.generateOrderId() + "C"
    // 获取充电点的前置点
    // 先借用这个 loc 方法
    val pc = sr.mapCache.getPointCacheByGroupAndLoc(allocation.rr, allocation.pointName) ?: return
    val prePoint = pc.backwardPaths.firstOrNull()?.fromPointName ?: return
    logger.info(
      "Let the robot ${allocation.rr.robotName} go to the prepoint $prePoint first and" +
        " then go to ${allocation.pointName} to charge, charging Order $orderId",
    )
    val step0 = TransportStep(
      id = orderId + "Step0",
      orderId = orderId,
      status = StepStatus.Executable,
      withdrawOrderAllowed = true,
      location = prePoint,
    )
    val step1 = TransportStep(
      id = orderId + "Step1",
      stepIndex = 1,
      orderId = orderId,
      status = StepStatus.Executable,
      withdrawOrderAllowed = true,
      location = allocation.pointName,
    )

    val order = TransportOrder(
      kind = OrderKind.Charging,
      id = orderId,
      status = OrderStatus.Allocated,
      stepFixed = true,
      stepNum = 2,
      expectedRobotNames = listOf(allocation.rr.robotName),
      actualRobotName = allocation.rr.robotName,
      robotAllocatedOn = Date(),
      sceneId = sr.sceneId,
    )
    val steps = listOf(step0, step1)

    // 里面改 autoOrder
    startCharge(sr, allocation.rr, OrderRuntime(order, steps), allocation.pointName)
  }

  // 当前机器人在充电点
  private fun robotInChargingPoint(rr: RobotRuntime): Boolean {
    val currentPoint = rr.sr.mapCache.getPointCacheByStand(rr) ?: return false
    return rr.sr.mapCache.isChargeAllowed(currentPoint)
  }

  // 充电点是否可用
  // TODO 部分充电点上没有机器人也不能用，比如互斥的充电点、充电点与停靠点几乎重合等。这些暂时通过碰撞检测来处理
  // private fun listIdleChargePoints(sr: SceneRuntime): List<MapPoint> {
  //   val usedOrUsingPoints = mutableSetOf<String>()
  //
  //   for (rr in sr.listRobots()) {
  //     rr.selfReport?.stand?.pointName?.let { usedOrUsingPoints.add(it) }
  //     rr.autoOrder?.pointName?.let { usedOrUsingPoints.add(it) }
  //     // TODO 去掉业务单的终点，如业务单指定去充电点的场景。
  //   }
  //
  //   val cache = sr.mapCache
  //   return cache.pointNames.values
  //     .filter { isChargePoint(it.point) }
  //     .filter { !usedOrUsingPoints.contains(it.point.name) }
  //     .map { it.point }
  // }

  // 分配充电点
  // 暂时随机分配，按照花费从小到大排序，并且认为所有充电桩都可以给所有机器人充电
  // 避免给同一个机器人分配多次
  // 返回过滤后的分配情况，外部可直接执行
  private fun allocateChargePoints(sr: SceneRuntime, robots: List<RobotRuntime>): List<ChargePointsAllocation> {
    val r = mutableListOf<ChargePointsAllocation>()
    // TODO 碰撞检测
    // 按机器人组 ID 分组，支持跨区域充电，比如：一楼区域充电点满了，若二楼有可用充电点，可以去二楼的充电点充电
    val groupRobots = robots.groupBy { it.config.groupId }
    val helper = ParkAndChargeResourceHelper(sr)
    for ((_, rrs) in groupRobots) {
      for (rr in rrs) {
        val cc = getConfigOfRobot(rr)
        val chargingPoints = helper.listAvailableChargingPoints(rr)
        for (cp in chargingPoints) {
          val fromPoint = rr.startPointNameForDispatching ?: continue
          // 计算路径花费
          val length = try {
            val result = MapService.getShortestPathCostOfCrossAreas(rr, fromPoint, cp)
            if (result == NOT_ACHIEVABLE_COST) continue else result
          } catch (_: BzError) {
            continue
          }
          r.add(
            ChargePointsAllocation(
              rr,
              cp,
              length,
              (rr.selfReport?.main?.battery ?: 0.0) < cc.chargeOnly, // 机器人电量大于 chargeOnly 才能打断
            ),
          )
        }
      }
    }
    // 筛选出可执行的充电分配
    // 记录已经分配的机器人、充电点，避免重复分配
    val allocatedRobots = mutableSetOf<String>() // 已经分配过的机器人
    val allocatedChargePoints = mutableSetOf<String>() // 已经分配过的充电点
    // 先排个序
    if (ScriptCenter.exists("extSortChargingAllocations")) {
      // 比如根据机器人当前电量排序
      // 注意：修改原本的值，不要返回新 list
      ScriptCenter.execute(ScriptExeRequest("extSortChargingAllocations", arrayOf(r)))
    } else {
      r.sortBy { it.length }
    }

    val r2 = mutableListOf<ChargePointsAllocation>()
    for (e in r) {
      if (allocatedRobots.contains(e.rr.robotName) || allocatedChargePoints.contains(e.pointName)) {
        continue
      } else {
        allocatedRobots.add(e.rr.robotName)
        allocatedChargePoints.add(e.pointName)
        r2.add(e)
      }
    }

    return r2
  }

  // 创建充电运单
  private fun createChargeOrder(sr: SceneRuntime, allocation: ChargePointsAllocation) {
    val rr = allocation.rr
    val oldAutoOrder = rr.autoOrder
    if (oldAutoOrder != null) {
      if (oldAutoOrder.type == RobotAutoOrderType.Charging) {
        logger.error("机器人 ${rr.robotName} 已经有充电运单 ${oldAutoOrder.orderId} 了，不重复创建")
      } else {
        // 先撤销
        OrderCancelService.cancelOrder(sr, oldAutoOrder.orderId)
      }
      // 均暂不继续分配
      return
    }

    val orderId = OrderService.generateOrderId() + "C"

    logger.info("让机器人 ${allocation.rr.robotName} 去 ${allocation.pointName} 充电，充电运单 $orderId")

    val step = TransportStep(
      id = orderId + "Step0",
      orderId = orderId,
      status = StepStatus.Executable,
      withdrawOrderAllowed = true,
      location = allocation.pointName,
    )

    val order = TransportOrder(
      kind = OrderKind.Charging,
      id = orderId,
      status = OrderStatus.Allocated,
      stepFixed = true,
      stepNum = 1,
      expectedRobotNames = listOf(allocation.rr.robotName),
      actualRobotName = allocation.rr.robotName,
      keyLocations = listOf(allocation.pointName),
      robotAllocatedOn = Date(),
      sceneId = sr.sceneId,
    )
    val steps = listOf(step)

    // 里面改 autoOrder
    startCharge(sr, allocation.rr, OrderRuntime(order, steps), allocation.pointName)
  }

  /**
   * 开始充电运单。
   */
  private fun startCharge(
    sr: SceneRuntime,
    rr: RobotRuntime,
    or: OrderRuntime,
    chargingPointName: String,
  ) {
    logger.info("机器人 ${rr.robotName} 开始去充电，充电点=$chargingPointName，运单=${or.order} ")
    OrderService.createOrders(sr, listOf(or))

    rr.orders[or.id] = or

    rr.autoOrder = RobotAutoOrder(RobotAutoOrderType.Charging, or.id, pointName = chargingPointName)
    rr.chargingOrderDoneOn = null

    RobotService.persistRobotRuntime(rr)
  }

  /**
   * 先取机器人所在组的，否则取默认的
   */
  fun getConfigOfRobot(rr: RobotRuntime): BasicChargingConfig {
    val scc = rr.sr.config.chargingConfig
    return scc.groupedConfigs[rr.config.groupId] ?: scc.defaultConfig
  }

  /**
   * 返回机器人当前电量，如果没有返回 0.0
   */
  fun getBatteryOrZero(rr: RobotRuntime): Double = rr.selfReport?.main?.battery ?: 0.0

  /**
   * 机器人是否刚开始充电。从充电运单结束开始算，一段时间内，不更改派单。
   */
  fun justCharging(rr: RobotRuntime): Boolean {
    val cc = getConfigOfRobot(rr)

    val chargingStartOn = rr.chargingOrderDoneOn ?: return false

    return (System.currentTimeMillis() - chargingStartOn.time < cc.minChargingTime * 1000)
  }

  /**
   * 机器人正在充电
   */
  fun isReportingChargingNow(rr: RobotRuntime) = rr.selfReport?.main?.charging == true

  /**
   * 机器人在过去 N 秒内正在充电就算正在充电
   * 防止上报的充电状态波动
   */
  fun isChargingRobust(rr: RobotRuntime): Boolean {
    val on = rr.reportingChargingOn ?: return false
    return (System.currentTimeMillis() - on.time < 1000 * 10)
  }

  /**
   * 机器人是否要强充
   */
  fun forceCharge(rr: RobotRuntime): Boolean {
    val cc = getConfigOfRobot(rr)
    if (ScriptCenter.exists("extForceCharge")) {
      val r: Boolean = ScriptCenter.execute(
        ScriptExeRequest("extForceCharge", arrayOf(rr, cc)),
        jacksonTypeRef(),
      )
      return r
    } else {
      return getBatteryOrZero(rr) < cc.chargeOnly
    }
  }

  /**
   * 机器人是否要充电
   */
  fun needCharge(rr: RobotRuntime): Boolean {
    val cc = getConfigOfRobot(rr)
    if (ScriptCenter.exists("extNeedCharge")) {
      val r: Boolean = ScriptCenter.execute(
        ScriptExeRequest("extNeedCharge", arrayOf(rr, cc)),
        jacksonTypeRef(),
      )
      return r
    } else {
      return getBatteryOrZero(rr) < cc.chargeNeed
    }
  }

  /**
   * 机器人充电充足，可以停靠
   */
  fun canStopCharge(rr: RobotRuntime): Boolean {
    val cc = getConfigOfRobot(rr)
    if (ScriptCenter.exists("canStopCharge")) {
      val r: Boolean = ScriptCenter.execute(
        ScriptExeRequest("canStopCharge", arrayOf(rr, cc)),
        jacksonTypeRef(),
      )
      return r
    } else {
      return getBatteryOrZero(rr) >= cc.chargeFull
    }
  }
}

data class ChargePointsAllocation(
  // val configId: Int, // 充电策略 ID
  @JvmField
  val rr: RobotRuntime, // 机器人
  @JvmField
  val pointName: String, // 充电点
  @JvmField
  val length: Double, // 路径
  @JvmField
  val lowBattery: Boolean = false, // 是否属于强充状态的低电量，否才可以打断
  // 优先级
)