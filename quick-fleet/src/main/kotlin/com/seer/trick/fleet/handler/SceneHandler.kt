package com.seer.trick.fleet.handler

import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.*
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.file.FileManager
import com.seer.trick.base.http.*
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.HttpServerManager.noAuth
import com.seer.trick.base.http.HttpServerManager.sendFileWithType
import com.seer.trick.base.http.handler.*
import com.seer.trick.fleet.device.door.DoorService
import com.seer.trick.fleet.device.door.SceneDoor
import com.seer.trick.fleet.device.lift.LiftService
import com.seer.trick.fleet.device.lift.SceneLift
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.mock.service.MockService
import com.seer.trick.fleet.order.OrderReplayService
import com.seer.trick.fleet.order.RandomOrderService
import com.seer.trick.fleet.seer.SceneAreaMapValueDiff
import com.seer.trick.fleet.seer.SmapHelper
import com.seer.trick.fleet.seer.SmapToRobotMapConverter
import com.seer.trick.fleet.service.*
import com.seer.trick.fleet.service.SceneService
import com.seer.trick.fleet.stats.TrafficConditionsService
import com.seer.trick.helper.*
import io.javalin.http.Context
import io.javalin.websocket.WsMessageContext
import org.apache.commons.codec.digest.DigestUtils
import org.apache.commons.io.*
import org.slf4j.LoggerFactory
import java.io.File
import java.io.FileInputStream
import java.nio.charset.StandardCharsets
import kotlin.collections.component1
import kotlin.collections.component2
import kotlin.collections.set
import kotlin.system.measureTimeMillis

/**
 * 场景相关 HTTP 和 WS 接口
 */
object SceneHandler : WebSocketSubscriber() {

  private val logger = LoggerFactory.getLogger(javaClass)

  fun registerHandlers() {
    WebSocketManager.subscribers += this

    val c = Handlers("api/fleet/scenes")

    c.get("list", ::listScenes, auth()) // 列出场景
    c.get("schema/{id}", ::getSceneSchema) // 获取单个场景
    c.post("create", ::createScene, auth()) // 创建新场景
    c.post("remove", ::removeScenes, auth()) // 删除多个场景
    c.get("{sceneId}/ready-status", ::readyStatus, auth()) // 场景就绪情况
    c.post("{sceneId}/disable", ::disableScene, auth()) // 是否禁用场景
    c.post("{sceneId}/export-m4-scene", ::exportM4Scene, auth()) // 导出 M4 的场景
    c.post("{sceneId}/import-m4-scene", ::importM4Scene, auth()) // 导入 M4 的场景

    c.post("{sceneId}/import-rds-core-scene/{check}", ::importRdsCoreScene, auth()) // 导入 RDS CORE 场景文件

    c.post("{sceneId}/mock", ::mockScene, auth()) // 一键仿真
    c.post("{sceneId}/cancel-mock", ::cancelMockScene, auth()) // 一键取消仿真

    c.post("{sceneId}/basic/patch", ::patchSceneBasic, auth()) // 部分更新场景基础

    // 配置
    c.get("{sceneId}/config", ::getSceneConfig, auth()) // 获取场景配置
    c.post("{sceneId}/config/patch", ::patchSceneConfig, auth()) // 部分更新场景配置

    // 机器人组
    c.post("{sceneId}/robot-group/create", ::createRobotGroup, auth())
    c.post("{sceneId}/robot-group/remove", ::removeRobotGroups, auth())
    c.post("{sceneId}/robot-group/{groupId}/update", ::updateRobotGroup, auth())

    // 机器人
    c.post("{sceneId}/robot/create", ::createRobot, auth()) // 添加一个机器人
    c.post("{sceneId}/robot/create/batch", ::createRobotBatch, auth()) // 添加多个机器人
    c.post("{sceneId}/robot/remove", ::removeRobots, auth()) // 删除多个机器人
    c.post("{sceneId}/robot/update/config", ::updateRobotConfig, auth()) // 更新一个机器人配置
    c.post("{sceneId}/robot/update/config/batch", ::updateRobotConfigBatch, auth()) // 批量更新机器人配置

    // 区域
    c.post("{sceneId}/area/create/{check}", ::createArea, auth()) // 添加一个区域
    c.post("{sceneId}/area/remove", ::removeAreas, auth()) // 删除多个区域
    c.post("{sceneId}/area/update/config/{check}", ::updateAreaConfig, auth()) // 更新一个区域配置
    c.post("{sceneId}/area/disable/batch", ::disableAreasBatch, auth()) // 批量启用停用区域

    // 容器类型
    c.get("{sceneId}/container-type", ::listContainerTypes, noAuth()) // 列出场景中所有容器类型
    c.post("{sceneId}/container-type/create", ::createContainerType, noAuth()) // 创建一个容器类型
    c.post("{sceneId}/container-type/remove", ::removeContainerTypes, noAuth()) // 删除多个容器类型
    c.post("{sceneId}/container-type/update/config", ::updateContainerType, noAuth()) // 更新一个容器类型

    // 门
    c.get("{sceneId}/door", ::listDoors, noAuth()) // 获取场景中所有门的配置
    c.post("{sceneId}/door/create", ::createDoor, auth()) // 添加一扇门
    c.post("{sceneId}/door/remove", ::removeDoors, auth()) // 删除多扇门
    c.post("{sceneId}/door/update/config", ::updateDoorConfig, auth()) // 修改一扇门的配置

    // 电梯
    c.get("{sceneId}/lift", ::listLifts, noAuth()) // 获取场景中所有电梯的配置
    c.post("{sceneId}/lift/create", ::createLift, auth()) // 添加一个电梯
    c.post("{sceneId}/lift/remove", ::removeLifts, auth()) // 删除多个电梯
    c.post("{sceneId}/lift/update/config", ::updateLiftConfig, auth()) // 修改一个电梯的配置

    // 地图
    c.get("{sceneId}/area-map/{areaId}", ::listAreaMaps, noAuth()) // 获取场景中一个区域的所有地图
    c.post("{sceneId}/area-map/update-area-map", ::updateAreaMap, auth()) // 更新一个区域地图，可指定更新合并后的地图或机器人组地图
    c.post("{sceneId}/upload-robot-map", ::uploadRobotMap, auth()) // 上传一个机器人的地图
    c.post("parse-smap", ::parseSmap, auth()) // 上传 smap 文件并解析
    c.post("{sceneId}/load-smap", ::loadSmap, auth()) // 加载指定 smap 文件并返回

    c.get("{sceneId}/absolute-path", ::getSceneAbsolutePath) // 获取场景的绝对路径

    c.post("{sceneId}/calc-move-cost", ::getCost) // 计算机器人到达某点的花费

    c.get("{sceneId}/list-points-bins", ::listPointsBins) // 列出场景中的点位以及库位

    c.post("{sceneId}/upload", ::uploadFile, auth()) // 上传场景资源文件
    c.get("{sceneId}/download/<path>", ::downloadFile, auth()) // 下载场景资源文件
  }

  override fun onMessage(ctx: WsMessageContext, msg: WsMsg, env: WsEnv) {
    when (msg.action) {
      "Fleet::Scene::Query" -> onSceneAllQuery(ctx, msg) // 调度开发界面综合数据
    }
  }

  private fun createArea(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)

    val req: SceneArea = ctx.getReqBody()

    val check = BoolHelper.anyToBool(ctx.pathParam("check"))
    if (check && SceneHelper.existArcPath(sceneId, req.groupsMap)) {
      ctx.json(mapOf("noArcPath" to false))
      return
    }
    val checkContext = CheckAreaContext(mutableListOf())
    SceneAreaService.create(sr, req, checkContext)

    ctx.json(mapOf("noArcPath" to true, "errors" to checkContext.errors.joinToString("<br/>")))
  }

  data class DeleteAreasReq(val ids: List<Int>)

  private fun removeAreas(ctx: Context) {
    val req: DeleteAreasReq = ctx.getReqBody()

    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)

    SceneAreaService.remove(sr, req.ids)

    ctx.status(200)
  }

  private fun updateAreaConfig(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)

    val req: SceneArea = ctx.getReqBody()

    val check = BoolHelper.anyToBool(ctx.pathParam("check"))
    if (check && SceneHelper.existArcPath(sceneId, req.groupsMap)) {
      ctx.json(mapOf("noArcPath" to false))
      return
    }
    val checkContext = CheckAreaContext(mutableListOf())
    SceneAreaService.updateAreaConfig(sr, req, checkContext)
    ctx.json(mapOf("noArcPath" to true, "errors" to checkContext.errors.joinToString("<br/>")))
  }

  /**
   *  批量启用停用区域
   */
  private fun disableAreasBatch(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)

    val req: SceneAreaService.DisableAreas = ctx.getReqBody()

    SceneAreaService.disableAreas(sr, req)
    ctx.status(200)
  }

  private fun listScenes(ctx: Context) {
    ctx.json(SceneService.listSceneDigests())
  }

  private fun getSceneSchema(ctx: Context) {
    val id = ctx.pathParam("id")
    val schema = SceneService.mustGetSceneById(id).getSchema()
    // 前端不通过此接口使用 gmMap，数据量大，所以过滤掉
    val areas = schema.structure.areas.map { it.copy(gmMap = emptyMap()) }
    val structure = schema.structure.copy(areas = areas)
    ctx.json(schema.copy(structure = structure))
  }

  private fun createScene(ctx: Context) {
    val req: CreateSceneReq = ctx.getReqBody()
    val id = SceneService.createScene(req.name)
    ctx.json(mapOf("id" to id))
  }

  data class CreateSceneReq(val name: String)

  private fun removeScenes(ctx: Context) {
    val req: IdsReq = ctx.getReqBody()

    SceneService.removeScenes(req.ids)

    ctx.status(200)
  }

  data class IdsReq(val ids: List<String>)

  private fun readyStatus(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)
    ctx.json(sr.status == SceneStatus.Initialized)
  }

  private fun disableScene(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val req: DisableReq = ctx.getReqBody()

    SceneService.setSceneDisabled(sceneId, req.disabled)

    ctx.status(200)
  }

  data class DisableReq(val disabled: Boolean)

  private fun importRdsCoreScene(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)
    val totalTime = measureTimeMillis {
      val uploaded = ctx.uploadedFile("f0") ?: throw BzError("errNoUploadedFile", "f0")

      val tmpFile = FileManager.nextTmpFile("zip")

      // 测量文件复制耗时
      FileUtils.copyInputStreamToFile(uploaded.content(), tmpFile)
      val check = BoolHelper.anyToBool(ctx.pathParam("check"))
      val (noArcPath, errors) = RdsCoreService.importRdsCoreScene(sr, tmpFile, check)
      if (!noArcPath) {
        ctx.json(mapOf("noArcPath" to false))
      } else {
        ctx.json(mapOf("noArcPath" to true, "errors" to errors.joinToString("<br/>")))
      }
    }
    logger.info("Import Rds Core scene cost $totalTime ms")
  }

  /**
   * 列出场景中一个区域的所有地图
   */
  private fun listAreaMaps(ctx: Context) {
    val areaId = NumHelper.anyToInt(ctx.pathParam("areaId")) ?: throw BzError("errAreaIdCannotBeEmpty")
    val sceneId = ctx.pathParam("sceneId")

    val sr = SceneService.mustGetSceneById(sceneId)
    val areaCache = sr.mapCache.areaById[areaId]
    val mergedMap = areaCache?.mergedMap?.areaMap
    val groupMaps = areaCache?.groupedMaps?.map { (id, cache) -> GroupMap(id, cache.areaMap) } ?: emptyList()

    ctx.json(ListAreaMapsResp(mergedMap, groupMaps, areaCache?.schema?.ui))
  }

  data class ListAreaMapsResp(val mergedMap: SceneAreaMap?, val groupMaps: List<GroupMap>, val ui: Any? = null)

  data class GroupMap(val id: Int, val map: SceneAreaMap)

  /**
   * 更新一个区域地图
   */
  private fun updateAreaMap(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val req: UpdateAreaMapReq = ctx.getReqBody()
    val sr = SceneService.mustGetSceneById(sceneId)
    val areaCache = sr.mapCache.areaById[req.areaId] ?: throw BzError("errAreaNotFoundById", req.areaId)
    // 检查 MD5 是否发生改变
    var changedMD5 = false
    for ((gId, gm1) in req.groupMaps) {
      val gm2 = areaCache.groupedMaps[gId]?.areaMap
      if (gm2 == null) {
        changedMD5 = true
        break
      }
      val diff = SceneAreaMapValueDiff(gm1, gm2)
      if (diff.isAnyChanged()) {
        changedMD5 = true
        break
      }
    }

    SceneAreaService.updateAreaMap(sr, areaCache, req.mergedMap, req.groupMaps, req.ui)
    ctx.json(mapOf("changedMD5" to changedMD5))
  }

  data class UpdateAreaMapReq(
    val areaId: Int,
    val mergedMap: SceneAreaMap, // 前端必传
    val groupMaps: Map<Int, SceneAreaMap>, // 前端必传
    val ui: UiConfig? = null,
  )

  private fun uploadRobotMap(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val uploaded = ctx.uploadedFile("f0") ?: throw BzError("errNoUploadedFile", "f0")

    val mapsDir = SceneFileService.getSceneRobotMapsDir(sceneId)
    mapsDir.mkdirs()

    // TODO 验证是地图文件

    // 先写入临时文件
    val tmpFile = FileManager.nextTmpFile()
    FileUtils.copyInputStreamToFile(uploaded.content(), tmpFile)

    val filename = IdHelper.oidStr() + uploaded.extension()
    val targetFile = File(mapsDir, filename)
    if (FileHelper.isZipFile(tmpFile)) {
      // 3D 地图解析
      try {
        val tmpZipFile = FileManager.nextTmpFile()
        FileHelper.unzipFileToDir(tmpFile, tmpZipFile)
        val smapFile = File(tmpZipFile, "0.smap")
        val infoFile = File(tmpZipFile, "info.json")
        val mapName = JsonHelper.mapper.readTree(infoFile)?.get("zipName")?.asText()!!
        val smap = JsonHelper.mapper.readTree(smapFile)
        (smap["header"] as ObjectNode).put("mapName", mapName)
        JsonHelper.mapper.writeValue(targetFile, smap)
      } catch (e: Exception) {
        logger.error("Invalid map file: ${e.message}")
        throw BzError("errInvalidMapFile")
      }
    } else {
      FileUtils.moveFile(tmpFile, targetFile)
    }

    val path = SceneFileService.fileToPath(sceneId, targetFile)
    val md5 = FileInputStream(targetFile).use { DigestUtils.md5Hex(it) }

    ctx.json(mapOf("path" to path, "md5" to md5))
  }

  private fun parseSmap(ctx: Context) {
    val file = ctx.uploadedFile("f0") ?: throw BzError("errNoUploadedFile", "f0")
    val str = IOUtils.toString(file.content(), StandardCharsets.UTF_8)

    val smap = SmapHelper.strToSmap(str)
    val map = SmapToRobotMapConverter().add(smap, "").build()
    ctx.json(map)
  }

  private fun loadSmap(ctx: Context) {
    val req: LoadSmapReq = ctx.getReqBody()
    val sceneId = ctx.pathParam("sceneId")
    val file = SceneFileService.pathToFile(sceneId, req.path)
    if (file.exists()) {
      val str = FileUtils.readFileToString(file, StandardCharsets.UTF_8)
      val smap = SmapHelper.strToSmap(str)
      val map = SmapToRobotMapConverter().add(smap, "").build()
      ctx.json(map)
    } else {
      ctx.json(SceneAreaMap())
    }

    ctx.status(200)
  }

  // 导出 M4 场景
  private fun exportM4Scene(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")

    val zipFile = SceneService.exportM4Scene(sceneId)
    ctx.json(mapOf("path" to FileManager.fileToPath(zipFile)))
  }

  // 导入 M4 场景
  private fun importM4Scene(ctx: Context) {
    val uploaded = ctx.uploadedFile("f0") ?: throw BzError("errNoUploadedFile", "f0")
    val sceneId = ctx.pathParam("sceneId")
    val tmpDir = FileManager.nextTmpFile()
    try {
      uploaded.content().use { inputStream ->
        FileHelper.unzipStreamToDir(inputStream, tmpDir)
      }

      SceneService.importM4Scene(sceneId, tmpDir)
    } finally {
      FileUtils.deleteDirectory(tmpDir)
    }

    ctx.status(200)
  }

  data class LoadSmapReq(val path: String)

  private fun mockScene(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")

    val sr = SceneService.mustGetSceneById(sceneId)
    MockService.mockScene(sr, SceneService.listScenes())

    ctx.status(200)
  }

  /**
   * 取消一个场景中全部机器人的仿真
   */
  private fun cancelMockScene(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")

    val sr = SceneService.mustGetSceneById(sceneId)
    MockService.cancelMockScene(sr)

    ctx.status(200)
  }

  private fun onSceneAllQuery(ctx: WsMessageContext, msg: WsMsg) {
    val req: DevQueryReq = JsonHelper.mapper.readValue(msg.content!!, jacksonTypeRef())

    val sr = SceneService.mustGetSceneById(req.sceneId)

    val all: EntityValue = mutableMapOf(
      "status" to sr.status,
    )

    if (req.excluded?.contains("robots") != true) {
      val robots = RobotService.listRobotUiReports(sr, req.withRawReport)
      all["robots"] = robots
    }

    val cqEqSceneId = Cq.eq("sceneId", req.sceneId)
    val goingQuery = Cq.and(
      Cq.ne("status", OrderStatus.Cancelled.name),
      Cq.ne("status", OrderStatus.Done.name),
      cqEqSceneId,
    )
    val faultQuery = Cq.and(
      Cq.ne("status", OrderStatus.Cancelled.name),
      Cq.ne("status", OrderStatus.Done.name),
      Cq.eq("fault", true),
      cqEqSceneId,
    )

    if (req.excluded?.contains("orders") != true) {
      val orders = EntityRwService.findMany(
        "TransportOrder",
        when (req.orderQueryType) {
          OrderQueryType.AllOrders -> {
            cqEqSceneId
          }

          OrderQueryType.NoFinishedOrders -> {
            goingQuery
          }

          OrderQueryType.FaultOrders -> {
            faultQuery
          }
        },
        FindOptions(limit = 50, sort = listOf("-createdOn")),
      )
      all["orders"] = orders
    }

    val goingCount = EntityRwService.count("TransportOrder", goingQuery)
    all["goingCount"] = goingCount

    val faultCount = EntityRwService.count("TransportOrder", faultQuery)

    all["faultCount"] = faultCount

    if (req.excluded?.contains("ordersRecords") != true) {
      // 运行时状态
      val ordersRecords = sr.listOrders().map { it.toRecord() }.associateBy { it.orderId }
      all["ordersRecords"] = ordersRecords
    }

    // 派单
    if (req.excluded?.contains("dispatch") != true) {
      all["dispatchProfile"] = sr.dispatchProfile
    }

    // 交管
    if (req.excluded?.contains("traffic") != true) {
      all["trafficDebug"] = sr.trafficService.debugData()
    }

    // 配置
    if (req.excluded?.contains("sceneConfig") != true) {
      all["sceneConfig"] = sr.config
    }

    // 路况
    if (req.excluded?.contains("trafficConditions") != true) {
      all["trafficConditions"] = TrafficConditionsService.getPathTrafficConditions()
    }

    // 随机发单
    if (req.excluded?.contains("randomOrders") != true) {
      val keepOrderNum = RandomOrderService.getCfg(req.sceneId)
      all["keepOrderNum"] = keepOrderNum
      all["randomOrdersOn"] = keepOrderNum != null
    }

    // 运单重放
    if (req.excluded?.contains("replayOrders") != true) {
      val stop = OrderReplayService.getReplayStatus(req.sceneId)
      all["replayOrdersStop"] = stop
    }

    // 所有门的实时状态
    if (req.excluded?.contains("doors") != true) {
      all["doors"] = DoorService.listDoorUiReports(sr)
    }

    // 所有电梯的实时状态
    if (req.excluded?.contains("lifts") != true) {
      all["lifts"] = LiftService.listLiftUiReports(sr)
    }

    ctx.send(WsMsg.json("Fleet::Scene::Reply", all, replyToId = msg.id))
  }

  data class DevQueryReq(
    val sceneId: String,
    val excluded: Set<String>? = null,
    val withRawReport: Boolean = false, // 要不要机器人原始报文
    val orderQueryType: OrderQueryType = OrderQueryType.AllOrders,
  )

  enum class OrderQueryType {
    AllOrders,
    NoFinishedOrders,
    FaultOrders,
  }

  private fun patchSceneBasic(ctx: Context) {
    val id = ctx.pathParam("sceneId")
    val req: EntityValue = ctx.getReqBody()

    val sr = SceneService.mustGetSceneById(id)
    var basic = sr.basic
    if (req.containsKey("name")) {
      val name = req["name"] as String?
      if (name.isNullOrBlank()) throw BzError("errSceneNameCannotBeBlank")
      basic = basic.copy(name = name)
    }
    if (req.containsKey("displayOrder")) {
      val displayOrder = req["displayOrder"] as Int?
      if (displayOrder != null) {
        basic = basic.copy(displayOrder = displayOrder)
      }
    }

    SceneService.replaceSceneBasic(id, basic)

    ctx.status(200)
  }

  private fun getSceneConfig(ctx: Context) {
    val id = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(id)
    ctx.json(sr.config)
  }

  private fun patchSceneConfig(ctx: Context) {
    val id = ctx.pathParam("sceneId")
    val req: EntityValue = ctx.getReqBody()
    val sr = SceneService.mustGetSceneById(id)
    SceneConfigService.patchSceneConfig(sr, req)

    ctx.status(200)
  }

  private fun createRobotGroup(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)

    val req: RobotGroup = ctx.getReqBody()
    val group = RobotGroupService.create(sr, req)
    ctx.json(group)
  }

  data class RemoveRobotGroupsReq(val ids: List<Int>)

  private fun removeRobotGroups(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)

    val req: RemoveRobotGroupsReq = ctx.getReqBody()
    RobotGroupService.remove(sr, req.ids)

    ctx.status(200)
  }

  private fun updateRobotGroup(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)

    val groupId: Int = ctx.pathParam("groupId").toInt()
    val req: RobotGroup = ctx.getReqBody()
    RobotGroupService.update(sr, groupId, req)

    ctx.status(200)
  }

  // 添加一个机器人
  private fun createRobot(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)

    val req: SceneRobot = ctx.getReqBody()

    RobotService.create(sr, req)

    ctx.status(200)
  }

  // 添加多个机器人
  private fun createRobotBatch(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)

    val req: List<SceneRobot> = ctx.getReqBody()

    if (req.isNotEmpty()) {
      for (robot in req) {
        RobotService.create(sr, robot)
      }
    }

    ctx.status(200)
  }

  data class DeleteRobotsReq(val robotNames: List<String>)

  // 删除多个机器人
  private fun removeRobots(ctx: Context) {
    val req: DeleteRobotsReq = ctx.getReqBody()

    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)

    RobotService.remove(sr, req.robotNames)

    ctx.status(200)
  }

  // 更新一个机器人配置
  private fun updateRobotConfig(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)

    val req: SceneRobot = ctx.getReqBody()
    RobotService.updateRobotConfig(sr, req)

    ctx.status(200)
  }

  // 批量更新机器人配置，如果使用 SceneRobot，其默认值会导致操作异常。
  data class SceneRobotDefaultNull(
    val disabled: Boolean? = null, // 停用
    val remark: String? = null, // 备注
    val groupId: Int? = null, // 所属组 ID
    val tags: List<String>? = null, // 标签
    val vendor: RobotVendor? = null,
    val selfBinNum: Int? = null, // 机器人库位数；一次能载几个货
    val image: Any? = null, // 图片
    val connectionType: RobotConnectionType? = null, // 机器人和调度的连接方式
    val robotIp: String? = null, // 机器人 IP
    val robotPortStart: Int? = null, // 机器人起始端口
    val ssl: Boolean? = null, // 海柔：连接使用 SSL 加密
    val gwAuthId: String? = null, // gw 网关用户ID
    val gwAuthSecret: String? = null, // gw 网关登录秘钥
    val simulated: Boolean? = null, // 是否为仿真
    val plusOne: Boolean? = null, // 是否支持 N + 1
  )

  data class UpdateRobotConfigBatchReq(val robotNames: List<String>, val config: SceneRobotDefaultNull)

  // 批量更新机器人配置
  private fun updateRobotConfigBatch(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)
    val req: UpdateRobotConfigBatchReq = ctx.getReqBody()

    val config = req.config
    for (robotName in req.robotNames) {
      val oldConfig = sr.mustGetRobot(robotName).config
      val newConfig = oldConfig.copy(
        disabled = config.disabled ?: oldConfig.disabled, // 停用，
        groupId = config.groupId ?: oldConfig.groupId, // 机器人分组
        remark = config.remark ?: oldConfig.remark, // 备注，界面上目前未展示
        tags = config.tags ?: oldConfig.tags, // 标签，界面上目前未展示
        vendor = config.vendor ?: oldConfig.vendor, // 厂商
        selfBinNum = config.selfBinNum ?: oldConfig.selfBinNum, // 最大载货数
        plusOne = config.plusOne ?: oldConfig.plusOne, // N+1
        image = config.image ?: oldConfig.image, // 机器人图片
        connectionType = config.connectionType ?: oldConfig.connectionType, // 连接类型
        robotPortStart = config.robotPortStart ?: oldConfig.robotPortStart, // 机器人起始端口
        simulated = config.simulated ?: oldConfig.simulated, // 仿真
        ssl = config.ssl ?: oldConfig.ssl, // 仅对海柔机器人有效
        gwAuthId = config.gwAuthId ?: oldConfig.gwAuthId, // gw 网关用户ID，界面上目前未展示
        gwAuthSecret = config.gwAuthSecret ?: oldConfig.gwAuthSecret, // gw 网关登录秘钥，界面上目前未展示
        // robotIp = config.robotIp ?: oldConfig.robotIp, // 机器人 ip 地址。实车的 IP 地址应该都是唯一的，所以不支持批量修改。
      )
      RobotService.updateRobotConfig(sr, newConfig)
    }

    ctx.status(200)
  }

  /**
   * 获取场景的绝对路径
   */
  private fun getSceneAbsolutePath(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sceneDir = SceneFileService.getSceneDir(sceneId)
    ctx.json(sceneDir.absolutePath)
  }

  private fun listContainerTypes(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)
    ctx.json(sr.containerTypes)
  }

  private fun createContainerType(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)

    val req: SceneContainerType = ctx.getReqBody()
    val containerType = ContainerTypeService.add(sr, req)
    ctx.json(containerType)
  }

  data class DeleteContainerTypesReq(val ids: List<Int>)

  private fun removeContainerTypes(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)

    val req: DeleteContainerTypesReq = ctx.getReqBody()
    ContainerTypeService.remove(sr, req.ids)

    ctx.status(200)
  }

  private fun updateContainerType(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)
    val req: SceneContainerType = ctx.getReqBody()
    ContainerTypeService.update(sr, req)

    ctx.status(200)
  }

  private fun listDoors(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)
    val doorConfigMap: MutableMap<Int, SceneDoor> = mutableMapOf()
    sr.doors.map { doorConfigMap.put(it.value.config.id, it.value.config) }
    ctx.json(doorConfigMap)
  }

  private fun createDoor(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)

    val req: SceneDoor = ctx.getReqBody()
    val door = DoorService.add(sr, req)
    ctx.json(door)
  }

  data class RemoveDoorsReq(val ids: List<Int>)

  private fun removeDoors(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)
    val req: RemoveDoorsReq = ctx.getReqBody()

    DoorService.remove(sr, req.ids)
  }

  private fun updateDoorConfig(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)
    val req: SceneDoor = ctx.getReqBody()
    DoorService.update(sr, req)
  }

  private fun listLifts(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)
    val liftConfigMap: MutableMap<Int, SceneLift> = mutableMapOf()
    sr.lifts.map { liftConfigMap.put(it.value.config.id, it.value.config) }
    ctx.json(liftConfigMap)
  }

  private fun createLift(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)
    val req: SceneLift = ctx.getReqBody()
    val lift = LiftService.add(sr, req)
    ctx.json(lift)
  }

  data class RemoveLiftsReq(val ids: List<Int>)

  private fun removeLifts(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)
    val req: RemoveLiftsReq = ctx.getReqBody()
    LiftService.remove(sr, req.ids)
  }

  private fun updateLiftConfig(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)
    val req: SceneLift = ctx.getReqBody()
    LiftService.update(sr, req)
  }

  private fun getCost(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)
    val req: CalcMoveCostReq = ctx.getReqBody()
    val rr = sr.mustGetRobot(req.robotName)
    val fromPoint = MapService.findBestStartPointNameForPlan(rr)
    if (fromPoint == null) {
      throw BzError("errNoFromPointName")
    } else {
      ctx.json(MapService.getShortestPathCostOfCrossAreas(rr, fromPoint, req.toLocation))
    }
  }

  data class CalcMoveCostReq(val robotName: String, val toLocation: String)

  /**
   * 列出点位以及库位
   */
  private fun listPointsBins(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)
    val res: MutableList<String> = ArrayList()
    res.addAll(sr.mapCache.pointNames)
    res.addAll(sr.mapCache.binNames)
    ctx.json(res)
  }

  /**
   * 上传场景资源文件
   */
  private fun uploadFile(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val f0 = ctx.uploadedFile("f0") ?: throw BzError("errNoUploadedFile", "f0")

    val configStr = ctx.formParam("config")
    val config: UploadConfig? = if (configStr.isNullOrBlank()) {
      null
    } else {
      JsonHelper.mapper.readValue(configStr, jacksonTypeRef())
    }

    val imageDir = SceneFileService.getSceneImageDir(sceneId)
    val extension = FilenameUtils.getExtension(f0.filename())
    val fileName = IdHelper.oidStr() + if (extension.isNullOrBlank()) "" else ".$extension"
    val file = File(imageDir, fileName)
    FileUtils.copyInputStreamToFile(f0.content(), file)

    val md5 = if (config?.md5Compute == true) {
      FileInputStream(file).use { DigestUtils.md5Hex(it) }
    } else {
      null
    }

    ctx.json(UploadFileInfo(f0.filename(), f0.size(), SceneFileService.fileToPath(sceneId, file), md5))
  }

  /**
   * 下载场景资源文件
   */
  private fun downloadFile(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val path = ctx.pathParam("path")

    // 校验文件路径合法性
    val imageDir = SceneFileService.getSceneImageDir(sceneId)
    val file = imageDir.resolve(path).normalize()
    if (!FileHelper.isFileInDir(file, imageDir) || file.isDirectory) { // 暂时不让下载目录
      throw Error404(I18N.lo("errIllegalPath", listOf(path)))
    }

    // 检查文件是否存在
    if (!file.exists()) {
      throw Error404(I18N.lo("errFileNotFound", listOf(path)))
    }

    ctx.header("Content-Disposition", "attachment; filename=\"${file.name}\"")
    sendFileWithType(file, ctx)
  }
}

data class SceneIdReq(val sceneId: String)