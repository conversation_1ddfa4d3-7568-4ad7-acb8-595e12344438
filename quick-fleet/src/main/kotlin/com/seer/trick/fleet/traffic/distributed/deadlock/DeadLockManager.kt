package com.seer.trick.fleet.traffic.distributed.deadlock

import com.seer.trick.fleet.traffic.distributed.block.BlockService
import com.seer.trick.fleet.traffic.distributed.context.ContextManagerService
import com.seer.trick.fleet.traffic.distributed.context.domain.TrafficStatus
import com.seer.trick.fleet.traffic.distributed.deadlock.helper.*
import com.seer.trick.fleet.traffic.distributed.deadlock.idle.IdleDeadLockProcessor
import com.seer.trick.fleet.traffic.distributed.deadlock.link.LinkDeadLockProcessor
import com.seer.trick.fleet.traffic.distributed.deadlock.model.DeadLockMessage
import com.seer.trick.fleet.traffic.distributed.deadlock.replan.ReplanDeadLockProcessor
import com.seer.trick.fleet.traffic.distributed.service.DistributedTrafficService
import io.javalin.util.NamedThreadFactory
import org.slf4j.LoggerFactory
import java.util.concurrent.*

/**
 * 死锁模块的入口
 *  1、检测死锁，死锁分类
 *  2、处理死锁
 * */
object DeadLockManager {

  private val logger = LoggerFactory.getLogger(javaClass)

  // 定义一个线程池
  private val deadLockPool = ThreadPoolExecutor(
    2,
    20,
    60L,
    TimeUnit.MILLISECONDS,
    LinkedBlockingQueue<Runnable>(128),
    NamedThreadFactory("deadlock"),
  )

  /**
   *  检测死锁
   *  每次只处理一种类型的死锁
   * */
  fun checkDeadLock(robot: String) {
    // 获取死锁信息
    val blockItems = BlockService.findBlock(robot)
    if (blockItems.isEmpty()) return
    val context = ContextManagerService.queryRobotContext(robot)
//    if (System.currentTimeMillis() - context.pauseTime <= 30 * 1000) return
    // 检测属于哪一种死锁类型
    val idleMessage = IdleDeadLockProcessor.check(robot, blockItems)
    if (idleMessage.state) {
      logger.info("$robot| check block item is idle robot ${idleMessage.idleRobots}")
      deadLockPool.submit(handleDeadLock(idleMessage, IdleDeadLockProcessor))
      return
    }
    val linkMessage = LinkDeadLockProcessor.check(robot, blockItems)
    if (linkMessage.state) {
      logger.info("$robot| check block item in link robot ${linkMessage.linkRobots}")
      DistributedTrafficService.trafficAlarm(
        sceneId = context.sceneId,
        robotName = context.robotName,
        code = "*********",
        args = listOf(linkMessage.linkRobots.toString()),
        error = false,
      )
      deadLockPool.submit(handleDeadLock(linkMessage, LinkDeadLockProcessor))
      return
    }
    val replanMessage = ReplanDeadLockProcessor.check(robot, blockItems)
    if (replanMessage.state) {
      logger.info("$robot| check block item is fix robot ${replanMessage.fixedBlocks}")
      deadLockPool.submit(handleDeadLock(replanMessage, ReplanDeadLockProcessor))
    }
  }

  // 处理死锁
  private fun handleDeadLock(message: DeadLockMessage, processor: DeadLockProcessor): Runnable {
    return Runnable {
      val context = message.context
      val lock = context.lock.tryLock(100, TimeUnit.MILLISECONDS)
      if (!lock) {
        logger.error("${message.robotName}| try lock robot context failed")
        return@Runnable
      }
      try {
        context.state = TrafficStatus.DEADLOCK
        val handle = if (processor.handle(message)) "success" else "failed"
        logger.info("${message.robotName}| ${processor.javaClass.simpleName}  handle dead lock $handle")
      } catch (e: Exception) {
        logger.error("${message.robotName}| ${processor.javaClass.simpleName} handle dead lock failed $e")
      } finally {
        context.state = TrafficStatus.RUNNING
        logger.info("${context.robotName}| unlock")
        context.lock.unlock()
      }
    }
  }
}