package com.seer.trick.fleet.device.lift.adapter

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.I18N
import com.seer.trick.base.concurrent.PollingJobManager
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.fleet.device.lift.LiftAdapterReport
import com.seer.trick.fleet.device.lift.LiftDoorStatus
import com.seer.trick.fleet.device.lift.SceneLift
import com.seer.trick.fleet.device.lift.adapter.vendor.jinbo.JinBoTcpClient
import com.seer.trick.fleet.domain.SceneStatus
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.SceneRuntime
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.NumHelper
import com.seer.trick.helper.getTypeMessage
import org.slf4j.LoggerFactory
import java.util.*

/**
 * <AUTHOR>
 * @date 2025/5/27 16:10
 * 金博电梯梯控请求电梯开门时，只需要下发指定楼层参数即可；关门不需要参数。
 */
class LiftAdapterJinBo(val config: SceneLift) : LiftAdapter() {

  private val logger = LoggerFactory.getLogger(javaClass)

  @Volatile
  private var tcpClient: JinBoTcpClient? = null // TODO 重连机制

  @Volatile
  private lateinit var report: LiftAdapterReport

  // 金博电梯不会上报目标楼层，可以不用这个变量。
  @Volatile
  private var curTargetFloor: Int? = null // 记录本次请求楼层

  /**
   * 打印通讯记录，方便排查问题。
   * 日志量会很大，默认不开启。
   * TODO：不重启也可设置打印或者不打印日志。
   */
  @Volatile
  private var showPkgLog: Boolean = BzConfigManager.getByPathAsBoolean("ScWcs", "lifts", "showPkgLog")

  /**
   * LiftFloorConfig.index -> LiftFloorConfig.name.toInt()
   */
  private val floors: MutableList<JinBoFloor> = mutableListOf()

  /**
   * 适配器的故障信息
   */
  private val adapterFaultMsgSet: MutableSet<String> = mutableSetOf()

  override fun init(sr: SceneRuntime) {
    logger.info("init lift ${config.id}:${config.name}")

    // 校验楼层编码（LiftFloorConfig.code），校验失败只会导致无法呼叫电梯，但是还是可以正常获取电梯信息的。
    checkFloorCode()

    // 初始化每个电梯门的状态。
    report = LiftAdapterReport(doors = (0..config.doorNum).map { LiftDoorStatus.Closed })

    // 最后尝试构建 client 。
    tryBuildClient()

    logger.info("init lift ${config.id}:${config.name} finished, $report")

    PollingJobManager.submit(
      threadName = "FtLift-${config.id}-${config.name}-query",
      remark = "Fetching lift state of ${config.name}",
      interval = { 1000L },
      logger = logger,
      workerMaxTime = 30 * 1000,
      stopCondition = { sr.status == SceneStatus.Disposed || config.disabled },
      exceptionContinue = true,
      tags = setOf(sr.tag),
      worker = { fetchJinBoState() },
    )
  }

  private fun checkFloorCode() {
    for (floor in config.floors) {
      // 之前已经通过 checkJinBoFloorName() 校验过了，这里一定不会报错。
      val parsedCode = parseFloorCode(floor.code)
      if (parsedCode == null) {
        // 再次修改电梯配置后，如果相关参数都正常，则这些异常信息就消失了。
        adapterFaultMsgSet.add(I18N.lo("errLiftJinBoFloorCodeMustInt", listOf(config.name, floor.index + 1)))
      } else {
        floors.add(JinBoFloor(index = floor.index, name = floor.name, parsedCode = parsedCode, areaId = floor.areaId))
      }
    }

    logger.debug("checkFloorCode: $floors")
  }

  private fun parseFloorCode(floorCodeStr: String): Int? {
    return try {
      NumHelper.anyToInt(floorCodeStr) // "1ad" 报错。
    } catch (e: Exception) {
      null
    }
  }

  /**
   * 校验网络参数成功之后，才构建 client
   *    不构建 client，此电梯一直是离线状态，调度系统将无法选中此电梯。
   */
  private fun tryBuildClient() {
    // 校验网络参数，校验失败则禁用此电梯。前端有校验了。
    if (!config.host.isNullOrEmpty() && config.port != null) {
      tcpClient = JinBoTcpClient(config.name, config.host, config.port)
      return
    }

    if (config.host.isNullOrEmpty()) {
      adapterFaultMsgSet.add(I18N.lo("errLiftJinBoBadHost", listOf(config.host)))
    }
    if (config.port == null) {
      adapterFaultMsgSet.add(I18N.lo("errLiftJinBoBadPort", listOf("null")))
    }
  }

  override fun dispose() {
    logger.info("try to dispose lift ${config.id}:${config.name}")
    curTargetFloor = null
    tcpClient?.dispose()
  }

  /**
   * 校验 client，这样处理主要是为了更好的交互效果，界面可以提供更明确的异常信息。
   */
  private fun ensureTcpClient(faultMsgList: MutableList<String>): JinBoTcpClient? {
    if (tcpClient == null) {
      faultMsgList.add(I18N.lo("errLiftJinBoNoClient"))
      faultMsgList.addAll(adapterFaultMsgSet)
      // 未创建 client，无法获取电梯信息，电梯离线，且故障。
      report = report.copy(
        timestamp = Date(), online = false, fault = true, faultMsg = faultMsgList.joinToString("; "),
      )
      return null
    }
    return tcpClient
  }

  /**
   * 解析梯控的响应内容
   */
  private fun parseResponse(apiNo: JinBoLiftApi, resStr: String, faultMsgList: MutableList<String>) {

    if (showPkgLog) logger.debug("解析梯控的响应：raw: $resStr")

    val res: JinBoLiftRes = if (resStr.isBlank()) {
      // 梯控返回的数据是空的，交互成功，电梯在线，但故障。
      faultMsgList.add(I18N.lo("errLiftJinBoReplyEmptyData"))
      faultMsgList.addAll(adapterFaultMsgSet)
      report = report.copy(
        timestamp = Date(), online = true, fault = true, faultMsg = faultMsgList.joinToString("; "),
      )
      return
    } else {
      JsonHelper.mapper.readValue(resStr, jacksonTypeRef())
    }

    // 先判断 resCode 和 resMsg
    val resCode = res.code.let {
      if (it.isEmpty())
        faultMsgList.add(I18N.lo("errLiftJinBoResCodeEmpty")) // 没有获取到响应码
      try {
        NumHelper.anyToInt(res.code) ?: -1
      } catch (e: Exception) {
        faultMsgList.add(I18N.lo("errLiftJinBoResCodeMustInt", listOf(it))) // 获取到响应码，无法转换成整数。
        -1
      }
    }

    // 请求失败时，记录响应内容
    if (resCode != 0) faultMsgList.add(res.msg.ifEmpty { I18N.lo("errLiftJinBoNoFailedMsg") })

    // 根据 apiNo 处理响应的具体内容。
    when (apiNo) {
      JinBoLiftApi.QUERY_STATUS -> parseLiftStatus(res, faultMsgList)
      else -> {
        // 呼叫电梯 和 控制电梯关门后，除了 resCode 和 resMsg 之外，没有需要校验的内容。所以 do nothing 。
        // 以后有需要的话，再实现 parseCtrlResponse() 。
      }
    }

    // 组合请求过程中的故障信息和 adapterFaultMsg，并判断门是否故障。
    faultMsgList.addAll(adapterFaultMsgSet)
    val fault = resCode != 0 || faultMsgList.isNotEmpty()
    report = report.copy(
      timestamp = Date(), online = true, fault = fault, faultMsg = faultMsgList.joinToString("; "),
      targetFloor = curTargetFloor,
    )

    if (showPkgLog) logger.debug("report: {}", report)
  }

  /**
   * 解析电梯状态：当前楼层、电梯门状态。
   */
  private fun parseLiftStatus(response: JinBoLiftRes, faultMsgList: MutableList<String>) {
    // 获取电梯所在楼层
    val currentFloor = response.currentFloor.let {
      if (it == null) {
        faultMsgList.add(I18N.lo("errLiftJinBoNoCurrentFloorAndSetTo999")) // 没有获取到电梯所在楼层，取默认值 999
      }
      try {
        NumHelper.anyToInt(it) ?: 999
      } catch (e: Exception) {
        faultMsgList.add(I18N.lo("errLiftJinBoCurrentFloorMustInt")) // 获取到异常的楼层信息，无法转换成整数。
        999
      }
    }

    // 转换成调度能识别的楼层信息。
    val currentFloorIndex = floors.firstOrNull { it.parsedCode == currentFloor }?.index

    // 获取电梯门的状态
    val doorStatus = response.doorStatus.let {
      when (it) {
        JinBoLiftDoorStatus.OPEN -> LiftDoorStatus.Opened
        JinBoLiftDoorStatus.CLOSE -> LiftDoorStatus.Closed
        JinBoLiftDoorStatus.OPENING -> LiftDoorStatus.Opening
        JinBoLiftDoorStatus.CLOSING -> LiftDoorStatus.Closing
        else -> {
          // 电梯门故障（JinBoLiftDoorStatus.ERROR），或者没有获取到电梯门的状态时，标记电梯故障，并将电梯门置为 Unknown 状态。
          if (it == JinBoLiftDoorStatus.ERROR) faultMsgList.add(I18N.lo("errLiftJinBoDoorError")) // 电梯门故障
          else faultMsgList.add(I18N.lo("errLiftJinBoNoDoorStatus")) // 没有获取到电梯门的状态
          LiftDoorStatus.Unknown
        }
      }
    }

    // 并及时更新门的 “当前楼层” 和 “电梯门状态” 。
    report = report.copy(currentFloor = currentFloorIndex, doors = report.doors.map { doorStatus })
  }

  /**
   * 给梯控发送请求，并解析、校验梯控的响应。
   */
  private fun requestWitCheck(api: JinBoLiftApi, reqStr: String) {
    val faultMsgList: MutableList<String> = mutableListOf()

    val client = ensureTcpClient(faultMsgList) ?: return

    val resStr = try {
      // request() 最终还是会抛异常的
      client.request(api.apiNo, reqStr)
    } catch (e: Exception) {
      faultMsgList.add(I18N.lo("errLiftJinBoNoRequestFailed", listOf(e.getTypeMessage())))
      faultMsgList.addAll(adapterFaultMsgSet)
      // 请求失败，电梯离线，且故障。
      report = report.copy(
        timestamp = Date(), online = false, fault = true, faultMsg = faultMsgList.joinToString("; "),
      )
      return
    }

    parseResponse(api, resStr, faultMsgList)
  }

  override fun report(): LiftAdapterReport {
    fetchJinBoState()
    return report
  }

  private fun fetchJinBoState() {
    requestWitCheck(JinBoLiftApi.QUERY_STATUS, "")
  }

  /**
   * 阻塞式方法，直到到达目标楼层并开门
   */
  override fun gotoOpenDoor(rr: RobotRuntime?, targetFloorIndex: Int, remark: String) {
    // 呼叫电梯前先关一次电梯门
    logger.debug("goto 呼叫电梯前先关门，targetFloorIndex=$targetFloorIndex, remark=$remark")
    controlLift(JinBoLiftApi.CLOSE_DOOR)

    // todo 待优化
    val targetFloor = floors.firstOrNull { it.index == targetFloorIndex }.let {
      if (it == null) {
        logger.error("find code of floor=${config.name} index=${targetFloorIndex} failed，please check lift config")
        // 不能直接返回、报错吧
        // 呼叫电梯 "{0}" 失败：其 "区域楼层 {1}" 的 "楼层编码" 不正确，请重新配置。
        throw BzError("errJinBoLiftNoFloorCode", config.name, targetFloorIndex + 1)
      }
      it
    }
    val targetFloorCode = targetFloor.parsedCode
    curTargetFloor = targetFloorIndex

    // 直到到达目标楼层并开门 TODO 是否会阻塞 ？？？
    // todo 得增加超时机制，否则万一，就死等了。
    logger.debug(
      "goto 呼叫电梯，targetFloorIndex=$targetFloorIndex, remark=$remark, targetFloorCode=$targetFloorCode"
    )
    while (!Thread.interrupted()) {
      controlLift(JinBoLiftApi.OPEN_DOOR, targetFloorCode)

      if (remark.startsWith("(http) ")) {
        // 表示是通过接口呼叫电梯的，只呼叫一次, 并更新调度记录的目标楼层。
        // TODO 换用更合理的方式。
        report = report.copy(targetFloor = targetFloorIndex)
        return
      }

      // TODO 金博梯控目前不会提供多个不同的门的状态
      if (report.currentFloor == targetFloorIndex && report.doors.any { it == LiftDoorStatus.Opened }) {
        logger.debug("电梯 ${config.id}:${config.name} 在第 ${targetFloorIndex + 1} 层已打开")
        break
      }
      Thread.sleep(1000)
    }

    // TODO 优化：不要用 job
    // 机器人在进入/离开电梯时保持请求信号；手动开关门不下发周期任务
    logger.debug("goto 电梯到达机器人所在楼层，持续呼叫电梯")
    if (config.keepCalling && rr != null) {
      PollingJobManager.submit(
        threadName = "FtLift-${config.id}-${config.name}-${rr.robotName}-calling",
        remark = "robot ${rr.robotName} is calling lift ${config.name}",
        interval = { 1000L },
        logger = logger,
        workerMaxTime = 30 * 1000,
        stopCondition = {
          rr.sr.status == SceneStatus.Disposed || curTargetFloor != targetFloorIndex || config.disabled
        },
        exceptionContinue = true,
        tags = setOf(rr.sr.tag),
        worker = { controlLift(JinBoLiftApi.OPEN_DOOR, targetFloorCode) },
      )
    }
  }

  override fun closeDoor(rr: RobotRuntime?, remark: String) {
    controlLift(JinBoLiftApi.CLOSE_DOOR)
    curTargetFloor = null
  }

  private fun controlLift(apiNo: JinBoLiftApi, targetFloor: Int = -1) {
    // TODO 用 apiNo 作为判断条件更合理
    //  另外，基于之前 srd 适配金博梯控的过程，这里可能会有问题。调试完成后删除此行注释。
    val reqStr = if (targetFloor != -1) {
      val req = JinBoLiftReq(targetFloor.toString())
      JsonHelper.mapper.writeValueAsString(req)
    } else {
      ""
    }

    requestWitCheck(apiNo, reqStr)
  }

  /**
   * 开环控制-呼叫电梯到指定楼层，仅呼叫一次。
   * 供前端的按钮调用。
   */
  fun gotoOnce(targetFloorIndex: Int) {

  }
}

data class JinBoLiftReq(val destFloor: String)

/**
 * code: 0 表示没有错误
 */
data class JinBoLiftRes(
  val currentFloor: String?,
  val doorStatus: JinBoLiftDoorStatus?,
  val code: String,
  val msg: String,
)

data class JinBoFloor(
  val index: Int = 0,
  val name: String = "", // 给用户看到的楼层的名称
  val parsedCode: Int, // 给梯控发的楼层的编码 code.toInt()
  val areaId: Int = -1, // 所属区域
)

enum class JinBoLiftDoorStatus {
  OPEN,
  OPENING,
  CLOSE,
  CLOSING,
  ERROR,
}

enum class JinBoLiftApi(val apiNo: Int) {
  OPEN_DOOR(1000),
  CLOSE_DOOR(1001),
  QUERY_STATUS(2000),
}