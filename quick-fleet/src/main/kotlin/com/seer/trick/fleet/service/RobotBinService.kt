package com.seer.trick.fleet.service

import com.seer.trick.I18N.lo
import com.seer.trick.base.alarm.AlarmAction
import com.seer.trick.base.alarm.AlarmItem
import com.seer.trick.base.alarm.AlarmLevel
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.fleet.adapter.RobotRbkAdapter
import com.seer.trick.fleet.domain.OrderKind
import com.seer.trick.fleet.domain.RobotBin
import com.seer.trick.fleet.domain.RobotBinStatus
import com.seer.trick.fleet.domain.RobotSelfBin
import com.seer.trick.fleet.order.*
import org.slf4j.LoggerFactory

/**
 * 处理机器人身上的库位。
 * 目前要求一个运单最多一次取货、一次放货！不能有多次取货、放货。
 */
object RobotBinService {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 如果机器人是 N+1，且货叉 999 库位已有货，则返回这个库位
   */
  fun findFilledBin999(rr: RobotRuntime): RobotBin? {
    val bin999 = rr.bins.firstOrNull { it.isPlusOne(rr) && it.status == RobotBinStatus.Filled }
    return bin999
  }

  /**
   * 对于多负载机器人，优先往低位的背篓上放货，并且，最后再放货叉。bins 的顺序即反映这个顺序
   */
  fun findBetterBinBeforeStepExecuting(rr: RobotRuntime, selected: SelectedOrderStep) {
    if (rr.bins.size <= 1) return
    rr.sr.withOrderLock {
      if (!selected.getStep().forLoad) return@withOrderLock
      // 原定的库位
      val orderBin = rr.bins.firstOrNull { it.orderId == selected.or.id } ?: return@withOrderLock // 不可能出现
      // 低位空库位
      val firstEmptyBin = rr.bins
        .firstOrNull { it.status == RobotBinStatus.Empty || it.status == RobotBinStatus.Reserved }
        ?: return@withOrderLock // 也不可能出现
      if (orderBin.index != firstEmptyBin.index) {
        swapRobotBins(rr, orderBin.index, firstEmptyBin.index)
      }
    }
  }

  /**
   * 交换机器人的背篓
   * 需要在外部加锁 sr.withOrderLock
   */
  private fun swapRobotBins(rr: RobotRuntime, i: Int, j: Int) {
    val t = rr.bins[i].copy(index = j)
    rr.bins[i] = rr.bins[j].copy(index = i)
    rr.bins[j] = t
  }

  /**
   * 取货，单未指定容器，自动生成一个
   * 取货，但机器人身上的目标位置有货，告警
   * 放货，但机器人身上目标位置无货，或货不对，告警
   */
  fun checkBinBeforeStepExecuting(rr: RobotRuntime, selected: SelectedOrderStep): Boolean {
    val orderBin = rr.bins.find { it.orderId == selected.or.id } ?: return true

    val rbkBins = rr.selfReport?.main?.bins ?: return true
    val rbkBin = rbkBins.find { it.rbkBinId == orderBin.rbkBinId(rr) } ?: return true

    if (selected.getStep().forLoad) {
      // 多储位机器人则检查容器是否存在，不存在则自动生成一个
      if (rbkBins.isNotEmpty() && selected.or.order.containerId.isNullOrEmpty()) {
        val containerId = "container-${selected.or.id}"
        val newOrder = selected.or.order.copy(containerId = containerId)
        OrderService.updateAndPersistOrder(selected.or, newOrder, "Generate random container for multi-bin robot")
      }

      // 取货时，检查 rbk 库位是否已经有货了，有货则不能取货放到库位
      if (rbkBin.occupied) {
        alarmForLoadingToFilledRobotBin(rr, orderBin, selected)
        return false
      }
    } else if (selected.getStep().forUnload) {
      // 放货时，检查 rbk 库位是否有货，无货则不能放货
      if (!rbkBin.occupied) {
        alarmToUnloadingButRobotBinEmpty(rr, orderBin, selected)
        return false
      }
      // 放货时货不对板：1.重新绑定 rbk bin 2.取消运单
      if (rbkBin.containerId != orderBin.containerId) {
        alarmToUnloadingBadContainer(rr, orderBin, rbkBin, selected)
        return false
      }
    }
    return true
  }

  /**
   * 将要取货，但预定要放的机器人库位上有货
   */
  private fun alarmForLoadingToFilledRobotBin(rr: RobotRuntime, orderBin: RobotBin, selected: SelectedOrderStep) {
    val message = if (orderBin.isPlusOne(rr)) {
      lo("errRbkBinOccupiedPlusOne", listOf(rr.robotName))
    } else {
      lo("errRbkBinOccupied", listOf(rr.robotName, orderBin.binNo(rr), orderBin.index))
    }

    val ai = AlarmItem(
      sceneId = rr.sr.sceneId,
      group = "Fleet",
      code = "SceneRobotLoadFail",
      key = "SceneRobotLoadFail-${rr.robotName}",
      level = AlarmLevel.Error,
      message = message,
      args = listOf(rr.sr.sceneId, selected.or.id),
      actions = listOfNotNull(
        AlarmAction(lo("btnManualContinue")),
        AlarmAction(
          lo("btnCancelOrder"),
          // rbk 有货 m4 无货，需要确认提示用户先取下背篓的容器
          confirmMsg = lo("btnManualContinueMsg", listOf(rr.robotName, orderBin.binNo(rr), orderBin.index)),
        ),
      ),
      tags = setOf(rr.tag, rr.sr.tag),
    )
    AlarmService.addItem(ai) { actionIndex, args ->
      AlarmService.removeItem(ai.key)
      // TODO 判断已不能恢复，比如运单已取消
      val sr = rr.sr
      // 执行哪一步都需要清空 rbk bin
      RobotRbkAdapter.emptyRobotRbkBin(rr, orderBin)
      RobotService.fetchStateNow(rr)
      when (actionIndex) {
        0 -> {
          logger.debug("clear rbk bin index=${orderBin.index}")
        }

        1 -> {
          logger.debug("clear rbk bin index=${orderBin.index} and cancel order")
          OrderCancelService.cancelOrder(sr, selected.or.id)
        }
      }
    }
  }

  /**
   * 卸货，但机器人库位上没货
   */
  private fun alarmToUnloadingButRobotBinEmpty(rr: RobotRuntime, orderBin: RobotBin, selected: SelectedOrderStep) {
    val message = if (orderBin.isPlusOne(rr)) {
      lo("errRbkBinIsEmptyPlusOne", listOf(rr.robotName, orderBin.containerId))
    } else {
      lo("errRbkBinIsEmpty", listOf(rr.robotName, orderBin.binNo(rr), orderBin.index, orderBin.containerId))
    }
    val ai = AlarmItem(
      sceneId = rr.sr.sceneId,
      group = "Fleet",
      code = "SceneRobotUnloadFail",
      key = "SceneRobotUnloadFail-${rr.robotName}",
      level = AlarmLevel.Error,
      message = message,
      args = listOf(),
      actions = listOfNotNull(
        AlarmAction(lo("btnManualContinue")),
        AlarmAction(lo("btnCancelOrder")),
      ),
      tags = setOf(rr.tag, rr.sr.tag),
    )
    AlarmService.addItem(ai) { actionIndex, _ ->
      AlarmService.removeItem(ai.key)
      // TODO 判断已不能恢复，比如运单已取消
      RobotService.fetchStateNow(rr)
      when (actionIndex) {
        0 -> {
          logger.debug("bind rbk bin index=${orderBin.index} container id=${orderBin.containerId}")
          RobotRbkAdapter.fillRobotRbkBin(rr, orderBin, orderBin.containerId ?: "", "manual bind rbk bin")
        }

        1 -> {
          logger.debug("cancel order: rbk bin index=${orderBin.index} is empty")
          OrderCancelService.cancelOrder(rr.sr, selected.or.id)
          afterManualClearingBin(rr, orderBin)
        }
      }
    }
  }

  private fun alarmToUnloadingBadContainer(
    rr: RobotRuntime,
    orderBin: RobotBin,
    rbkBin: RobotSelfBin,
    selected: SelectedOrderStep,
  ) {
    val message = if (orderBin.isPlusOne(rr)) {
      lo("errRbkBinContainerIdPlusOne", listOf(rr.robotName, orderBin.containerId, rbkBin.containerId))
    } else {
      lo(
        "errRbkBinContainerId",
        listOf(rr.robotName, orderBin.binNo(rr), orderBin.index, orderBin.containerId, rbkBin.containerId),
      )
    }

    val ai = AlarmItem(
      sceneId = rr.sr.sceneId,
      group = "Fleet",
      code = "SceneRobotUnloadFail",
      key = "SceneRobotUnloadFail-${rr.robotName}",
      level = AlarmLevel.Error,
      message = message,
      args = listOf(),
      actions = listOf(
        AlarmAction(lo("btnManualContinue")),
        AlarmAction(lo("btnCancelOrder")),
      ),
      tags = setOf(rr.tag, rr.sr.tag),
    )
    AlarmService.addItem(ai) { actionIndex, _ ->
      AlarmService.removeItem(ai.key)
      // TODO 判断已不能恢复，比如运单已取消
      RobotService.fetchStateNow(rr)
      when (actionIndex) {
        0 -> {
          logger.debug("bind rbk bin index=${orderBin.index} container id=${orderBin.containerId}")
          RobotRbkAdapter.fillRobotRbkBin(rr, orderBin, orderBin.containerId ?: "", "manual rebind rbk bin")
        }

        1 -> {
          logger.debug(
            "cancel order: bin index=${orderBin.index} container id=${orderBin.containerId} != rbk" +
              " bin container id=${rbkBin.containerId}",
          )
          OrderCancelService.cancelOrder(rr.sr, selected.or.id)
        }
      }
    }
  }

  /**
   * 当运单步骤成功。
   * 外部加锁。外部处理持久化。
   * 处理失败抛异常。
   * 如果此步骤用于装货，标记库位有货。标记运单已取货。
   * 如果此步骤用于卸货，标记库位空，解除与运单的关联。标记运单已放货。
   */
  fun afterStepDone(rr: RobotRuntime, sec: StepExecuteContext) {
    val or = sec.or
    // 非业务单不处理库位
    if (or.order.kind != OrderKind.Business) return

    if (sec.withdrawn) return

    val orderId = or.id
    val step = sec.getStep()

    val binIndex = rr.bins.indexOfFirst { it.orderId == orderId }

    if (step.forLoad) {
      // 步骤步骤，且是装货步骤
      if (binIndex >= 0) {
        // TODO check empty
        val containerId = sec.or.order.containerId
        if ((rr.config.plusOne || rr.config.selfBinNum > 1) && containerId.isNullOrEmpty()) {
          throw RobotBinError(
            "Step Done and loaded, but no container id. Order=$orderId, " +
              "selfBinNum=${rr.config.selfBinNum}, N+1=${rr.config.plusOne}",
          )
        }
        rr.bins[binIndex] = RobotBin(
          binIndex,
          status = RobotBinStatus.Filled,
          orderId = orderId,
          containerId = sec.or.order.containerId,
        )
        val newOrder = or.order.copy(loaded = true, loadPoint = step.location)
        OrderService.updateAndPersistOrder(or, newOrder, "Mark loaded after step done")
      } else {
        // 不应该发生
        throw RobotBinError("Step done and loaded, but no target bin. Order=$orderId")
      }
    } else if (step.forUnload) {
      // 步骤步骤，且是卸货步骤
      if (binIndex >= 0) {
        // TODO check filled
        rr.bins[binIndex] = RobotBin(binIndex, status = RobotBinStatus.Empty, orderId = null, containerId = null)
        val newOrder = or.order.copy(unloaded = true, unloadPoint = step.location)
        OrderService.updateAndPersistOrder(or, newOrder, "Mark unloaded after step done")
      } else {
        // 不应该发生
        throw RobotBinError("Step done and unloaded, but no target bin. Order=$orderId")
      }
    }
  }

  /**
   * 库位异常，需要人工处理：人工取下货物
   */
  private fun alarmToManualClearingBin(rr: RobotRuntime, bin: RobotBin) {
    val message = if (bin.isPlusOne(rr)) {
      lo("errCancelOrderWhenPlusOneLoadButNoUnload", listOf(rr.robotName))
    } else {
      lo("errCancelOrderWhenLoadButNoUnload", listOf(rr.robotName, bin.binNo(rr), bin.index))
    }

    val ai = AlarmItem(
      sceneId = rr.sr.sceneId,
      group = "Fleet",
      code = "FleetOrderBinFault",
      key = "FleetOrderBinFault-${rr.robotName}-bin-${bin.index}",
      level = AlarmLevel.Error,
      message = message,
      args = listOf(),
      actions = listOfNotNull(AlarmAction(lo("btnManualFinishedTask"))),
      tags = setOf(rr.tag, rr.sr.tag),
    )

    AlarmService.addItem(ai) { _, args ->
      AlarmService.removeItem(ai.key)
      afterManualClearingBin(rr, bin)
    }
  }

  /**
   * 已取货未放货时运单被取消时的人工完成
   */
  private fun afterManualClearingBin(rr: RobotRuntime, bin: RobotBin) {
    val sr = rr.sr
    sr.withOrderLock {
      rr.bins[bin.index] = RobotBin(bin.index)
      RobotService.persistRobotRuntime(rr)
    }
    // “卸货，但机器人库位上没货”时，也会将背篓置空，确保机器人背篓状态符合调度预期。
    val resStr = RobotRbkAdapter.emptyRobotRbkBin(rr, bin)
    logger.info("manual finish robot ${rr.robotName}, unbind box index=${bin.index}, response = $resStr")
  }

  /**
   * 检查机器人上报的库位数量，如果小于系统内配置的机器人库位数，告警
   */
  fun checkReportBinNum(rr: RobotRuntime) {
    val main = rr.selfReport?.main ?: return
    val containers = main.bins ?: return
    val reportNum = containers.size
    // 只有料箱机器人才有数据，其他机器人不做处理
    if (reportNum == 0) return

    // 背篓的编号必须是从 0 开始的连续整数，必须校验，否则料箱车可能无法从调度系统期望的背篓中取放货。关键的导航参数是 "#containerName"
    val rbkBinIds = containers.map { it.rbkBinId }.sorted()
    val expectedSortedIds = rbkBinIds.mapIndexed { index, id -> if (id == 999) 999 else index }
    if (rbkBinIds.toString() != expectedSortedIds.toString()) {
      val ai = AlarmItem(
        sceneId = rr.sr.sceneId,
        group = "Fleet",
        code = "SceneRobotBinNumNotMatch",
        key = "SceneRobotBinNumNotMatch-${rr.robotName}",
        level = AlarmLevel.Error,
        message = lo(
          "errRobotBinNumBadOrder",
          listOf(rr.robotName, rbkBinIds.joinToString(", ")),
        ), // 机器人 "{0}" 启用的背篓的编号不正确，请按要求配置。
        tags = setOf(rr.tag, rr.sr.tag),
      )
      AlarmService.addItem(ai, ttl = 1000)
      return // 以免告警信息被覆盖了
    }

    val configNum = rr.config.selfBinNum

    // 只有配置数量大于实际数量的时候需要告警，允许配置数量小于实际数量（但是小于实际的时候不能开启 N+1）
    if (reportNum < configNum) {
      val ai = AlarmItem(
        sceneId = rr.sr.sceneId,
        group = "Fleet",
        code = "SceneRobotBinNumNotMatch",
        key = "SceneRobotBinNumNotMatch-${rr.robotName}",
        level = AlarmLevel.Error,
        message = lo("errRobotBinNum", listOf(rr.robotName, rr.bins.size, reportNum)),
        tags = setOf(rr.tag, rr.sr.tag),
      )
      AlarmService.addItem(ai, ttl = 1000)
    } else if (reportNum == configNum) {
      // 相等但是实际有 N+1 但是 M4 没有，告警
      if (containers.any { it.rbkBinId == 999 } && !rr.config.plusOne) {
        val ai = AlarmItem(
          sceneId = rr.sr.sceneId,
          group = "Fleet",
          code = "SceneRobotBinNumNotMatch",
          key = "SceneRobotBinNumNotMatch-${rr.robotName}",
          level = AlarmLevel.Error,
          message = lo("errRobotBinPlusOneNotConfig", listOf(rr.robotName)),
          tags = setOf(rr.tag, rr.sr.tag),
        )
        AlarmService.addItem(ai, ttl = 1000)
      }
    } else if (rr.config.plusOne) {
      // 系统配置数量小于实际，不能配置 N+1
      val ai = AlarmItem(
        sceneId = rr.sr.sceneId,
        group = "Fleet",
        code = "SceneRobotBinNumNotMatch",
        key = "SceneRobotBinNumNotMatch-${rr.robotName}",
        level = AlarmLevel.Error,
        message = lo("errRobotBinPlusOneShouldNotConfig", listOf(rr.robotName, rr.bins.size, reportNum)),
        tags = setOf(rr.tag, rr.sr.tag),
      )
      AlarmService.addItem(ai, ttl = 1000)
    }

    // 如果系统配置了 N+1 但实际读到的没有
    if (rr.config.plusOne && containers.none { it.rbkBinId == 999 }) {
      val ai = AlarmItem(
        sceneId = rr.sr.sceneId,
        group = "Fleet",
        code = "SceneRobotBinPlusOneNotMatch",
        key = "SceneRobotBinPlusOneNotMatch-${rr.robotName}",
        level = AlarmLevel.Error,
        message = lo("errRobotBinPlusOne", listOf(rr.robotName, true, false)),
        tags = setOf(rr.tag, rr.sr.tag),
      )
      AlarmService.addItem(ai, ttl = 1000)
    }
  }

  /**
   * 取消、撤回运单（运单与机器人断开联系），清理机器人库位：如果已载货，标记异常，否则重置
   */
  fun cancelBin(or: OrderRuntime, rr: RobotRuntime) = rr.sr.withOrderLock {
    val order = or.order
    val bin = rr.bins.find { it.orderId == or.id } ?: return@withOrderLock

    logger.debug("Cancel order {}, bin current: {}", or.id, bin)

    if (order.loaded) {
      rr.bins[bin.index] = bin.copy(status = RobotBinStatus.Cancelled)
    } else {
      // 空
      rr.bins[bin.index] = RobotBin(bin.index)
    }
  }

  /**
   * 提示清理库位
   */
  fun canceledBinsToAlarms(sr: SceneRuntime) {
    val robots = sr.listRobots().filter { !it.disabled() }

    // 添加运单故障告警
    for (rr in robots) {
      for (bin in rr.bins) {
        if (bin.status == RobotBinStatus.Cancelled) {
          alarmToManualClearingBin(rr, bin)
        }
      }
    }
  }
}

/**
 * 处理库位遇到问题
 */
class RobotBinError(message: String) : RuntimeException(message)