package com.seer.trick.fleet.service

import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.base.SysEmc
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.base.concurrent.BaseConcurrentCenter.highTimeSensitiveExecutor
import com.seer.trick.base.concurrent.BaseConcurrentCenter.lowTimeSensitiveExecutor
import com.seer.trick.base.concurrent.PollingJobManager
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.file.FileManager
import com.seer.trick.fleet.adapter.RobotRbkAdapter
import com.seer.trick.fleet.device.door.DoorService
import com.seer.trick.fleet.device.lift.LiftService
import com.seer.trick.fleet.domain.SceneBasic
import com.seer.trick.fleet.domain.SceneDigest
import com.seer.trick.fleet.domain.SceneStatus
import com.seer.trick.fleet.mock.service.MockContainersProcessor
import com.seer.trick.fleet.mock.service.MockService
import com.seer.trick.fleet.order.OrderCancelService
import com.seer.trick.fleet.order.OrderReplayService
import com.seer.trick.fleet.order.OrderService
import com.seer.trick.fleet.order.RandomCreateOrderService
import com.seer.trick.fleet.order.RandomOrderService
import com.seer.trick.fleet.seer.M4SceneImportConverter
import com.seer.trick.helper.FileHelper
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.getTypeMessage
import org.apache.commons.io.FileUtils
import org.slf4j.LoggerFactory
import java.io.File
import java.nio.file.Path
import java.nio.file.Paths
import java.util.concurrent.ConcurrentHashMap

/**
 * 管理场景。
 */
object SceneService {

  private val logger = LoggerFactory.getLogger(javaClass)

  // 场景运行时。by scene id, 包括禁用的场景
  private val scenes: MutableMap<String, SceneRuntime> = ConcurrentHashMap()

  /**
   * 初始化所有场景（包括停用的）。在启动时调用。
   */
  fun init() {
    SceneRobotSyncService.init()

    DoorService.init()

    LiftService.init()

    val ids = SceneFileService.listScenesIds()
    logger.info("Fleet3 scenes num: ${ids.size}")
    // 串行初始化所有场景
    for (id in ids) {
      initScene(loadSceneRuntime(id))
    }

    // 系统急停
    SysEmc.addCallback(::onSysEmc)
  }

  /**
   * 初始化单个场景（包括停用的）。只能被调用一次。在启动时、新建场景、启用场景时。场景修改不要调用此方法。
   * 目前整体比较快。
   */
  private fun initScene(sr: SceneRuntime) {
    if (sr.basic.disabled) return

    logger.info("Start | Init scene: $sr")
    sr.withOrderLock {
      sr.status = SceneStatus.Initializing
      // 配置
      SceneConfigService.init(sr)

      // 区域
      SceneAreaService.init(sr)

      // 容器类型
      ContainerTypeService.init(sr)

      // 机器人组
      RobotGroupService.init(sr)

      // 缓存
      sr.mapCache.update()

      // 门
      DoorService.init(sr)

      // 电梯
      LiftService.init(sr)

      // 先初始化运单服务再初始化机器人。因为恢复机器人状态时需要读未完成运单。
      // 运单，比较快
      OrderService.init(sr)

      // 初始化机器人，比较快
      RobotService.init(sr)

      // 兼容 5.1.1 之前创建的仿真机器人，修复其 selfBins
      MockContainersProcessor.fixSelfBins(sr)

      // 记录
      sr.opRecorder.init()

      // 临时版光通讯
      sr.marsService.init()

      // 随机发单
      RandomCreateOrderService.init(sr.sceneId)

      // 清理场景的无效数据
      clearInvalidData(sr)

      sr.status = SceneStatus.Initialized
    }
    logger.info("Done | Init scene: $sr")

    // 目前 setEmc 只是处理路径导航；启动时如果就急停了，不调用 setEmc 更好
    // highTimeSensitiveExecutor.submit {
    //   // 如果启动起来系统就急停了，尝试暂停路径导航
    //   // 如果非急停，不要处理
    //   if (SysEmc.isSysEmc()) {
    //     setEmc(sr, true)
    //   }
    // }

    // 异步清理一下废弃的地图文件
    lowTimeSensitiveExecutor.submit {
      clearExpiredMapFiles(sr.sceneId)
    }
  }

  /**
   * 从文件中加载场景运行时
   */
  private fun loadSceneRuntime(sceneId: String): SceneRuntime {
    val basic = SceneFileService.loadSceneBasic(sceneId) ?: SceneBasic(sceneId)
    val sr = SceneRuntime(basic)
    scenes[sceneId] = sr
    return sr
  }

  /**
   * 销毁所有场景。
   */
  fun dispose() {
    DoorService.dispose()

    SceneRobotSyncService.dispose()

    val srs = scenes.values.toList()
    for (sr in srs) {
      disposeScene(sr)
      scenes.remove(sr.sceneId)
    }
  }

  /**
   * 销毁一个场景。不要报错！
   * 销毁后此实例不能再被使用。
   */
  private fun disposeScene(sr: SceneRuntime) {
    if (sr.status == SceneStatus.Disposed || sr.basic.disabled) return

    logger.info("Start | Dispose scene: $sr")
    sr.withOrderLock {
      sr.status = SceneStatus.Disposed

      // 停用相关后台轮训任务
      PollingJobManager.removeByTag(sr.tag)

      sr.marsService.dispose()

      sr.opRecorder.dispose()

      RobotService.dispose(sr)

      OrderService.dispose(sr)

      OrderReplayService.dispose()

      SysEmc.removeCallback(::onSysEmc)

      // 按标签删除场景告警
      AlarmService.removeByTag(sr.tag)

      // 清除场景告警
      AlarmService.removeAllBySceneId(sr.sceneId)
    }
    logger.info("Done | Dispose scene: $sr")
  }

  /**
   * 列出所有调度场景。包括停用的。返回副本。
   */
  fun listScenes() = scenes.values.toList()

  /**
   * 列出所有场景，只给出关键信息。包括停用的。
   */
  fun listSceneDigests(): List<SceneDigest> = scenes.values.map {
    SceneDigest(
      id = it.basic.id,
      name = it.basic.name,
      disabled = it.basic.disabled,
      version = it.basic.version,
      displayOrder = it.basic.displayOrder,
      lastModifiedOn = it.basic.lastModifiedOn,
    )
  }

  /**
   * 根据场景 ID 获取场景
   */
  fun mustGetSceneById(id: String): SceneRuntime = scenes[id] ?: throw BzError("errFleetNoSuchSceneById", id)

  /**
   * 根据场景 ID 获取场景
   */
  fun getSceneById(id: String) = scenes[id]

  /**
   * 根据场景名获取场景
   */
  fun getSceneByName(name: String): SceneRuntime? = scenes.values.firstOrNull { it.basic.name == name }

  /**
   * 根据场景名获取场景
   */
  fun mustGetSceneByName(name: String) = getSceneByName(name) ?: throw BzError("errFleetNoSuchSceneByName", name)

  /**
   * 获取场景的停用状态
   */
  fun disabled(sr: SceneRuntime) = sr.basic.disabled

  /**
   * 创建新场景。传入场景名称。
   * 加锁。
   */
  @Synchronized
  fun createScene(name: String): String {
    if (name.isBlank()) throw BzError("errSceneNameCannotBeBlank")
    val id = IdHelper.oidStr()
    val basic = SceneBasic(id, name)

    // 场景名不能重复
    if (scenes.values.any { it.basic.name == name }) throw BzError("errDuplicatedSceneName", basic.name)

    logger.info("Create scene: $basic")

    SceneFileService.saveSceneBasic(basic)
    initScene(loadSceneRuntime(id))

    return id
  }

  /**
   * 删除多个场景。
   * 加锁。
   */
  @Synchronized
  fun removeScenes(ids: List<String>) {
    for (id in ids) {
      val sr = scenes[id] ?: continue
      logger.info("Remove scenes: $sr")

      // 取消随机发单
      RandomOrderService.removeCfg(id)

      // 取消新通用运单
      OrderCancelService.cancelAllOrders(sr)

      // 删除后台任务轮训
      PollingJobManager.removeByTag(sr.tag)

      // 取消仿真
      MockService.cancelMockScene(sr)

      // 删除场景
      disposeScene(sr)
      // 等以上取消完成再移除，否则机器人下线时会报 “无此场景”
      scenes.remove(id)

      // 删除该场景下的所有运单
      EntityRwService.removeMany("TransportOrder", Cq.include("sceneId", sr.sceneId))

      // TODO 删除数据库中其他数据

      SceneFileService.removeSceneFiles(id)
    }
  }

  /**
   * 修改场景基础配置 SceneBasic
   * 加锁。
   */
  @Synchronized
  fun replaceSceneBasic(id: String, basic: SceneBasic) {
    val sr = mustGetSceneById(id)

    logger.info("Update scene basic: $id $basic")
    sr.basic = basic
    SceneFileService.saveSceneBasic(basic)
  }

  /**
   * 停用/启用场景
   * TODO 用什么锁合适
   */
  @Synchronized
  fun setSceneDisabled(sceneId: String, disabled: Boolean) {
    val sr = mustGetSceneById(sceneId)
    if (sr.basic.disabled == disabled) return

    if (disabled) {
      // 取消仿真，删除与场景内所有机器人同名的仿真机器人
      val robotNames = sr.listRobots().map { it.robotName }
      val removeIds = MockService.config.robots.filter { robotNames.contains(it.name) }.map { it.id }
      if (removeIds.isNotEmpty()) MockService.removeMockRobots(removeIds)
      disposeScene(sr)
      // 最后再修改 basic 状态,因为销毁场景时会判断 basic.disable
      sr.basic = sr.basic.copy(disabled = disabled)
      SceneFileService.saveSceneBasic(sr.basic)
    } else {
      // 启用场景需要先保存
      sr.basic = sr.basic.copy(disabled = disabled)
      SceneFileService.saveSceneBasic(sr.basic)
      val srNew = loadSceneRuntime(sceneId)
      initScene(srNew)
      // 异步启动仿真
      asyncMock(srNew)
    }
  }

  /**
   * 导出 M4 场景。返回打包好的 M4 场景包。
   */
  fun exportM4Scene(sceneId: String): File {
    // 先清理一下废弃的地图文件
    clearExpiredMapFiles(sceneId)

    val sceneDir = SceneFileService.getSceneDir(sceneId)
    val tmpDir = FileManager.nextTmpFile()
    val zipFile = FileManager.nextTmpFile(suffix = ".zip")
    try {
      FileUtils.copyDirectory(sceneDir, tmpDir) { file -> file.name != "rds-core-scene" }
      FileHelper.zipDirToFile(tmpDir, zipFile) // Zip the directory into the file
    } finally {
      FileUtils.deleteDirectory(tmpDir)
    }

    return zipFile
  }

  /**
   * 导入 M4 场景
   * 采用销毁、导入、初始化的方式
   */
  fun importM4Scene(sceneId: String, tmpDir: File) {
    val sr = mustGetSceneById(sceneId)

    logger.info("Import M4 scene $sceneId | Start")

    disposeScene(sr)

    M4SceneImportConverter.importScene(sr, tmpDir)

    logger.info("Import M4 scene $sceneId | Done")

    val srNew = loadSceneRuntime(sceneId)

    initScene(srNew)

    asyncMock(srNew)
  }

  /**
   *  异步执行一次一键仿真
   */
  fun asyncMock(sr: SceneRuntime) {
    highTimeSensitiveExecutor.submit {
      try {
        while (true) {
          if (sr.status == SceneStatus.Initialized) {
            MockService.initOrRemoveMockByRobotState(sr)
            break
          }
          Thread.sleep(500)
        }
      } catch (e: Exception) {
        logger.error("Failed mock ", e)
      }
    }
  }

  /**
   * 当系统急停改变
   */
  private fun onSysEmc(enabled: Boolean) {
    for (sr in scenes.values) {
      setEmc(sr, enabled)
    }
  }

  /**
   * 设置急停状态
   *
   * SysEmc 的 callBack 需要传入相同的 callback，所以这里要封装一个 set 方法
   */
  private fun setEmc(sr: SceneRuntime, emc: Boolean) {
    if (emc) {
      for (rr in sr.robots.values) {
        try {
          if (RobotService.isControlCommandAllowed(rr)) {
            RobotRbkAdapter.pauseNavTask(rr)
          }
        } catch (e: Exception) {
          logger.error("Failed to pause robot ${rr.robotName} nav task," + e.getTypeMessage())
        }
      }
    } else {
      for (rr in sr.robots.values) {
        try {
          if (RobotService.isControlCommandAllowed(rr)) {
            RobotRbkAdapter.resumeNavTask(rr)
          }
        } catch (e: Exception) {
          logger.error("Failed to resume robot ${rr.robotName} nav task," + e.getTypeMessage())
        }
      }
    }
  }

  /**
   * 清理场景中废弃的地图文件
   */
  @Synchronized
  fun clearExpiredMapFiles(sceneId: String) {
    val mapsDir = SceneFileService.getSceneRobotMapsDir(sceneId)
    val mapFiles = mapsDir.listFiles()
    if (mapFiles.isNullOrEmpty()) return

    // 仿真机器人的地图 TODO 仿真机器人的地图不应该导出，一键仿真时仿真是直接使用组地图的
    val mockMaps = mutableListOf<Path>()
    for (rc in MockService.config.robots) {
      if (rc.sceneId != sceneId) continue
      for (map in rc.maps) {
        mockMaps.add(Paths.get(map.mapFile))
      }
    }

    // 场景机器人组的地图
    val groupMaps = mutableListOf<Path>()
    val sr = mustGetSceneById(sceneId)
    sr.areas.forEach { area ->
      area.groupsMap.forEach { gm ->
        groupMaps.add(Paths.get(gm.value.mapFile))
      }
    }

    for (dir in mapFiles) {
      if (dir.isFile) {
        if (Paths.get(SceneFileService.fileToPath(sceneId, dir)) !in groupMaps) dir.delete()
      } else {
        val mockFiles = dir.listFiles()
        if (mockFiles.isNullOrEmpty()) {
          dir.delete()
          continue
        }
        for (mf in mockFiles) {
          if (mf.isFile) {
            if (Paths.get(SceneFileService.fileToPath(sceneId, mf)) !in mockMaps) mf.delete()
          } else {
            mf.delete()
          }
        }
      }
    }
  }

  /**
   * 清理无效数据
   */
  private fun clearInvalidData(sr: SceneRuntime) {
    sr.areas = sr.areas.map { area ->
      val gmMap = area.gmMap.filter { sr.robotGroups.keys.contains(it.key) }
      val groupsMap = area.groupsMap.filter { sr.robotGroups.keys.contains(it.key) }
      area.copy(gmMap = gmMap, groupsMap = groupsMap)
    }
  }
}