package com.seer.trick.fleet.traffic.venus

import com.seer.trick.fleet.domain.Pose2D
import com.seer.trick.fleet.domain.RobotLoadCollisionModel
import com.seer.trick.fleet.domain.roundRadianPrecision
import com.seer.trick.fleet.service.RobotLoadCollisionService
import com.seer.trick.fleet.service.SceneRuntime
import com.seer.trick.fleet.service.SceneService
import com.seer.trick.fleet.traffic.TrafficTaskStatus
import java.util.concurrent.ConcurrentHashMap

/**
 * 路径规划
 */
object PlanService {

  /**
   * 上一轮规划请求
   */
  // ---- per area caches ----
  private val lastRequestsMap = ConcurrentHashMap<Int, Map<String, RobotPlanRequest>>()
  private val lastPlanTimeMap = ConcurrentHashMap<Int, Long>()
  private val lastPriorityEdgesMap = ConcurrentHashMap<Int, List<Pair<String, String>>>()
  private val lastPlanOkMap = ConcurrentHashMap<Int, Boolean>()

  // 从场景配置读取重规划间隔，默认 5秒
  private const val DEFAULT_REPLAN_INTERVAL_MS = 5_000L

  /**
   * 进行一次规划。分区域求解。
   */
  fun plan(sr: SceneRuntime, executions: Map<String, VenusRobotExecutionContext>): Map<Int, PlanResult> {
    // 请求都已经求解成功，无需重复求解
    // 其实还是要重规划的！TODO 为什么，找更优？条件变了？
    // 仅按照固定间隔触发重规划

    // 按区域收集请求
    val requests = collectRequests(sr, executions)

    val now = System.currentTimeMillis()

    // 分区域求解，暂时串行
    return requests.mapValues { ar ->
      val areaId = ar.key
      val interval = sr.config.trafficPlanIntervalMs.takeIf { it > 0 } ?: DEFAULT_REPLAN_INTERVAL_MS
      val lastTime = lastPlanTimeMap[areaId] ?: 0L
      if (now - lastTime < interval) return emptyMap()

      val result = doPlan(sr, ar.value, areaId)

      lastPlanOkMap[areaId] = result.ok
      if (result.ok) {
        lastPlanTimeMap[areaId] = System.currentTimeMillis()
        lastPriorityEdgesMap[areaId] = result.priorityEdges
        lastRequestsMap[areaId] = ar.value
      }

      result
    }
  }

  /**
   * 请求都已经求解成功
   */
  private fun allResolved(
    requests: Map<String, RobotPlanRequest>,
    executions: Map<String, VenusRobotExecutionContext>,
  ): Boolean {
    for (req in requests.values) {
      if (req.trafficTask == null) continue
      // 新任务
      val result = executions[req.robotInfo.robotName] ?: return false
      // 之前任务不同
      if (result.trafficTaskId != req.trafficTask.id) return false
    }
    return true
  }

  private fun doPlan(sr: SceneRuntime, requests: Map<String, RobotPlanRequest>, areaId: Int): PlanResult {
    val config = sr.config

    val robotCollisionModels: Map<String, RobotLoadCollisionModel> = requests.mapValues {
      it.value.robotInfo.collisionModel
    }

    val initialEdges = filterReusableEdges(requests, areaId)

    val lastOk = lastPlanOkMap[areaId] ?: false
    val initialEdgesFinal = if (!lastOk) {
      emptyList<Pair<String, String>>()
    } else {
      initialEdges
    }

    // TODO 为了能重放，规划时不要依赖 config/robotGroups/collisionModel 当前值，都通过请求传下去

    val option = PathFindingOption(
      highW = config.cbsHighW,
      lowW = config.cbsLowW,
      collisionModels = robotCollisionModels,
      initialPriorityEdges = initialEdgesFinal,
      enableNarrowPath = sr.config.enableNarrowPath ?: true, // 从场景配置中读取窄边通行开关
    )

    val pr = if (sr.getDevConfig("resolver") == "PBS") {
      val resolver = PbsResolver(sr, requests, option)
      resolver.resolve()
    } else {
      val resolver = CbsResolver(sr, requests, option)
      resolver.resolve()
    }

    return pr
  }

  /**
   * 分区域收集规划请求。包括没有任务的机器人。如果一个区域所有机器人都没有任务，不包含。
   */
  private fun collectRequests(
    sr: SceneRuntime,
    executions: Map<String, VenusRobotExecutionContext>, // 用于计算初始路径
  ): Map<Int, Map<String, RobotPlanRequest>> {
    val requests: MutableMap<Int, MutableMap<String, RobotPlanRequest>> = mutableMapOf()

    for (rr in sr.robots.values) {
      if (rr.disabled()) continue

      val stand = rr.selfReport?.stand ?: continue
      val main = rr.selfReport?.main ?: continue

      // 仅 TrafficAccepted 的交管任务需要被规划
      var tt = rr.pendingTrafficTask
      if (tt != null && tt.status != TrafficTaskStatus.TrafficAccepted) tt = null

      // TODO cm 是不是这么取
      val cm = RobotLoadCollisionService.buildCollisionModel(rr, main)

      // 初始货物方向
      val loadTheta = main.loadRelations?.get(0)?.direction

      val ec = executions[rr.robotName]
      val initPath = if (ec != null && ec.path.isNotEmpty() && ec.releasedPathIndex >= 0) {
        toInitPath(ec, cm)
      } else {
        null
      }
      val selfConfig = rr.selfConfig

      // 获取容器类型和窄边方向信息
      val containerType = cm.containerTypeName
      val narrowDir = if (containerType != null) {
        VenusNarrowPathHelper.queryContainerModelNarrowDir(sr.sceneId, containerType)
      } else {
        0
      }

      val req = RobotPlanRequest(
        RobotInfo(
          robotName = rr.robotName,
          stand = stand,
          groupId = rr.mustGetGroup().id,
          loadTheta = loadTheta,
          collisionModel = cm,
          moveSpeed = selfConfig?.maxSpeed ?: 1.0,
          rotateSpeed = selfConfig?.maxRotSpeed ?: Math.toRadians(45.0).roundRadianPrecision(),
          containerType = containerType,
          narrowDir = narrowDir,
        ),
        tt,
        initPath,
      )

      requests.getOrPut(stand.areaId) { mutableMapOf() }[rr.robotName] = req
    }

    // 过滤掉没有任务的区域
    return requests.filter { area -> area.value.any { it.value.trafficTask != null } }
  }

  /**
   * 在开始规划时，机器人可能已有一些已下发的路径正在执行
   * TODO 除了 initPath，还有一个方案是从已下发的路径结束的地方开始规划。
   */
  private fun toInitPath(ec: VenusRobotExecutionContext, cm: RobotLoadCollisionModel): List<State> {
    // TODO OrderProcessingError
    if (ec.donePathIndex > ec.releasedPathIndex) throw IllegalStateException("done > released")

    // 已下发，未执行的状态，作为机器人的初始状态
    val doneState = ec.path[ec.donePathIndex - ec.pathIndexOffset]

    val initPath = mutableListOf<State>()

    for (pi in ec.donePathIndex..ec.releasedPathIndex) {
      val state = ec.path[pi - ec.pathIndexOffset]
      // if (state.type == StateType.Goal) throw BzError("errCodeErr", "不能下发 Goal 节点")
      // 将状态的时间平移到最开始
      if (pi == ec.donePathIndex) {
        initPath += state.copy(
          timeStart = state.timeStart - doneState.timeStart,
          timeEnd = state.timeEnd - doneState.timeEnd,
          released = true,
        )
      } else {
        initPath += state.copy(
          timeStart = state.timeStart - doneState.timeEnd,
          timeEnd = if (state.timeEnd > 0) {
            state.timeEnd - doneState.timeEnd
          } else {
            state.timeEnd
          },
          released = true,
        )
      }
    }

    if (initPath.isNotEmpty()) {
      initPath[0] = initPath[0].copy(
        type = StateType.Start,
        shapes = PolygonsWithBBox(
          RobotLoadCollisionService.buildCollisionShape(
            cm,
            Pose2D(doneState.toPosition.x, doneState.toPosition.y, doneState.toEndHead!!),
            null,
          ),
        ),
      )
    }

    return initPath
  }

  /**
   * TODO 只对 PBS 生效？
   * 复用上一轮规划成功时生成的优先级边：
   * 复用之前的优先级的目的是，为了避免前后两次优先级不同，导致机器人有多余的避让
   * 1. `lastPriorityEdges` 记录了 PBS 在上一次成功规划时输出的 low→high 边集合；
   * 2. 本轮中，只有当 low / high 两个机器人都仍在执行同一任务时，即任务没有变化时，才说明这条边仍然有效，
   *    否则任务变化可能打破原有先后关系，不应强制沿用；
   * 3. 返回通过上述校验的边集合作为 `initialPriorityEdges`，帮助收敛搜索。
   *
   * 若上一次规划失败 (`lastPriorityEdges` 为空) 或 `lastRequests` 为空，则直接返回空集合。
   */
  private fun filterReusableEdges(current: Map<String, RobotPlanRequest>, areaId: Int): List<Pair<String, String>> {
    val prevEdges = lastPriorityEdgesMap[areaId] ?: emptyList()
    val prevReqs = lastRequestsMap[areaId] ?: return emptyList()

    val reusable = mutableListOf<Pair<String, String>>()

    for ((low, high) in prevEdges) {
      val lowCur = current[low] ?: continue
      val highCur = current[high] ?: continue
      val lowPrev = prevReqs[low]
      val highPrev = prevReqs[high]
      if (lowPrev != null && highPrev != null) {
        val lowSame = (lowPrev.trafficTask?.id == lowCur.trafficTask?.id)
        val highSame = (highPrev.trafficTask?.id == highCur.trafficTask?.id)
        if (lowSame && highSame) {
          reusable += low to high
        }
      }
    }
    return reusable
  }

  /**
   * 读取慢案例文件并执行规划。
   * @param fileName 位于 cases 目录下的文件名，如 "case-xxx.json"
   */
  fun planCase(sceneName: String, fileName: String): PlanResult? {
    val sr = SceneService.getSceneByName(sceneName) ?: return null
    val requests = VenusLogService.readSlowCase(fileName) ?: return null

    // 提取首个非空 areaId，若全部为空则默认为 0
    val areaId = requests.values.firstNotNullOfOrNull { it.robotInfo.stand.areaId } ?: 0

    return doPlan(sr, requests, areaId)
  }
}