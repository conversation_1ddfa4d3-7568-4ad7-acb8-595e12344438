package com.seer.trick.fleet.seer

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.base.file.FileManager
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.seer.SmapHelper.base64ToString
import com.seer.trick.fleet.seer.SmapHelper.getM4labels
import com.seer.trick.helper.JsonHelper
import org.apache.commons.io.FileUtils
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.tinyspline.BSpline
import java.nio.charset.StandardCharsets
import java.util.Base64
import kotlin.math.round

/**
 * 仙工 smap 转换为通用机器人地图（一个区域）
 * 允许添加多张 smap。按规则叠加。
 */
class SmapToRobotMapConverter {

  private val logger: Logger = LoggerFactory.getLogger(javaClass)

  private val idsCounter = IdsCounter() // ids 计数器

  private val pointsMap: MutableMap<String, MapPoint> = HashMap() // by point name

  // 与一个点位关联的库位最小最大名
  private val binsOnPoints: MutableMap<String, Array<String?>> = HashMap() // point name -> bin names

  private val pathsMap: MutableMap<String, MapPath> = HashMap() // by path key

  private val zones: MutableList<MapZone> = ArrayList()

  private val binsMap: MutableMap<String, SceneBin> = HashMap() // by bin name

  private val sourceMaps: MutableList<String> = ArrayList()

  private val envPoints: MutableSet<SmapPos> = HashSet() // 环境激光点云，判断是否是同一个点用 equals 方法

  private val restrictedLineMap: MutableMap<SmapMapLine, MapRestrictedLine> =
    HashMap() // 禁行线，SmapMapLine.toString() -> MapRestrictedLine

  private var minX: Double = Double.MAX_VALUE
  private var maxX: Double = -Double.MAX_VALUE
  private var minY: Double = Double.MAX_VALUE
  private var maxY: Double = -Double.MAX_VALUE

  private var width: Double = 0.0
  private var height: Double = 0.0

  private val errMsg: MutableList<String> = ArrayList() // 记录转换过程中的错误

  private val curveSplitType: CurveSplitType = CurveSplitType.Distance

  private val svgLengthScale = 1000

  /**
   * 添加一张 smap。
   */
  fun add(smap: Smap, smapName: String): SmapToRobotMapConverter {
    sourceMaps += smapName

    // 处理各地图元素
    processPoints(smap)
    processPaths(smap)
    processZones(smap)
    processBins(smap)
    processRestrictedLines(smap)
    processEnv(smap)

    return this
  }

  /**
   * 产生一个区域的机器人地图
   */
  fun build(): SceneAreaMap {
    if (errMsg.size > 0) logger.error(errMsg.joinToString("\n"))

    minX = minX.roundLengthPrecision()
    maxX = maxX.roundLengthPrecision()
    minY = minY.roundLengthPrecision()
    maxY = maxY.roundLengthPrecision()

    val cx = ((minX + maxX) / 2).roundLengthPrecision()
    val cy = ((minY + maxY) / 2).roundLengthPrecision()
    width = (maxX - minX).roundLengthPrecision()
    height = (maxY - minY).roundLengthPrecision()

    return SceneAreaMap(
      bound = Rect(cx = cx, cy = cy, width = width, height = height),
      points = pointsMap.values.toList(),
      paths = pathsMap.values.toList(),
      zones = zones,
      bins = binsMap.values.toList(),
      restrictedLines = restrictedLineMap.values.toList(),
      sourceMaps = sourceMaps,
      envPointCloud = EnvPointCloudHelper.parseSmap(envPoints),
      svgMapFile = buildSvgMap(),
    )
  }

  /**
   * 处理点位。点位的合并策略是，同名的取第一个。
   */
  private fun processPoints(smap: Smap) {
    if (smap.advancedPointList.isNullOrEmpty()) return

    for (raw in smap.advancedPointList) {
      if (pointsMap.containsKey(raw.instanceName)) continue
      val label = raw.property?.firstOrNull { it.key == "label" }?.stringValue ?: ""
      val spin = raw.property?.firstOrNull { it.key == "spin" }?.boolValue ?: false
      val m4define =
        raw.property?.firstOrNull { it.key == "M4define" }?.stringValue?.let { JsonHelper.mapper.readTree(it) }
      val point = MapPoint(
        id = ++idsCounter.pointId,
        name = raw.instanceName,
        label = label,
        type = raw.className,
        x = raw.pos.x,
        y = raw.pos.y,
        // 部分情况下，SMAP 中朝向为 0 度的点位（raw），其 ignoreDir 和 dir 属性都为 null，
        // 所以当 ignoreDir == null，并且 dir == null 时，direction 的值应该为 0.0 。
        direction = if (raw.ignoreDir == true) null else (raw.dir ?: 0.0),
        remark = base64ToString(raw.desc) ?: "", // 原文是 base64 的需要转成 utf8 的。
        spin = spin,
        parkAllowed = m4define?.get("parkAllowed")?.asBoolean() ?: false,
        chargeAllowed = m4define?.get("chargeAllowed")?.asBoolean() ?: false,
        giveWayNotAllowed = m4define?.get("giveWayNotAllowed")?.asBoolean() ?: false,
        containerRotateAllowed = m4define?.get("containerRotateAllowed")?.asBoolean() ?: false,
        linkedPathAsJointResource = m4define?.get("linkedPathAsJointResource")?.asBoolean() ?: false,
        unloadContainerDir = m4define?.get("unloadContainerDir")?.asDouble(),
        rotateNotAllowed = m4define?.get("rotateNotAllowed")?.asBoolean() ?: false,
        m4labelsStr = if (m4define?.has("m4labelsStr") == true) m4define.get("m4labelsStr").asText() else null,
        m4labels = getM4labels(m4define),
      )
      pointsMap[point.name] = point

      updateMinMaxX(point.x)
      updateMinMaxY(point.y)
    }
  }

  /**
   * 处理路径。路径的合并策略是，相同起点终点的，取第一个。
   */
  private fun processPaths(smap: Smap) {
    if (smap.advancedCurveList.isNullOrEmpty()) return

    for (ac in smap.advancedCurveList) {
      val fromPointName = ac.startPos.instanceName
      val toPointName = ac.endPos.instanceName
      val key = MapPath.getKey(fromPointName, toPointName)

      if (pathsMap.containsKey(key)) continue

      val fromPoint = pointsMap[fromPointName]
      val toPoint = pointsMap[toPointName]

      if (fromPoint == null) {
        errMsg += "路径 $key 找不到起始点 $fromPointName"
        continue
      }
      if (toPoint == null) {
        errMsg += "路径 $key 找不到终点 $toPointName"
        continue
      }
      // 线路起始点替换成存在的点给前端绘图用
      val newAc = ac.copy(
        startPos = SmapAdvancedCurveEndpoint(
          instanceName = fromPoint.name,
          pos = SmapPos(x = fromPoint.x, y = fromPoint.y, z = ac.startPos.pos.z),
        ),
        endPos = SmapAdvancedCurveEndpoint(
          instanceName = toPoint.name,
          pos = SmapPos(x = toPoint.x, y = toPoint.y, z = ac.endPos.pos.z),
        ),
      )
      val controls = buildControls(fromPoint, toPoint, ac)

      // length 属性不是线路长度，是权重，没有的话默认给 1.0
      val length = ac.property.firstOrNull { it.key == "length" }?.doubleValue ?: 1.0 // 这个属性可能不赋值
      val direction = ac.property.firstOrNull { it.key == "direction" }?.int32Value
      val goodsDir = ac.property.firstOrNull { it.key == "goodsDir" }?.doubleValue?.let {
        GeoHelper.normalizeRadian(Math.toRadians(it))
      }
      // m4 的配置
      val m4define =
        ac.property.firstOrNull { it.key == "M4define" }?.stringValue?.let { JsonHelper.mapper.readTree(it) }
      val loadPass = m4define?.get("loadPass")?.asText()
      val bSpline = SmapCurveHelper.transferPathToSpline(newAc)
      val beziers = SmapCurveHelper.transferBSplineToBezier(bSpline)
      val actualLength = SmapCurveHelper.getLength(bSpline, newAc)

      val (tracePoints, middlePoint) = generateTracePoints(newAc, bSpline, actualLength)

      // 曲线类型。先按 smap 设定的解析。后续根据实际是什么曲线来决定。比如，虽然是 BezierPath，但实际可能是直线
      val curveType = when (ac.className) {
        "StraightPath" -> PathCurveType.Straight
        "BezierPath" -> PathCurveType.Bezier3
        "NURBS6" -> PathCurveType.NURBS6
        "DegenerateBezier" -> PathCurveType.DegenerateBezier
        else -> PathCurveType.Other
      }

      val path = MapPath(
        id = ++idsCounter.pathId,
        fromPointId = fromPoint.id,
        fromPointName = fromPoint.name,
        toPointId = toPoint.id,
        toPointName = toPoint.name,
        moveDirection = when (direction) {
          0 -> MoveDirection.Forward
          1 -> MoveDirection.Backward
          else -> MoveDirection.Dual
        },
        curveType = curveType,
        degree = bSpline.degree,
        knots = bSpline.knots,
        controls = controls,
        actualLength = actualLength,
        costFactor = length,
        middlePoint = middlePoint,
        bezierPaths = beziers,
        tracePoints = tracePoints,
        containerDir = goodsDir,
        rotateNotAllowed = m4define?.get("rotateNotAllowed")?.asBoolean() ?: false,
        giveWayNotAllowed = m4define?.get("giveWayNotAllowed")?.asBoolean() ?: false,
        containerShortSideAhead = m4define?.get("containerShortSideAhead")?.asBoolean() ?: false,
        loadPass = if (loadPass != null) PathLoadPass.valueOf(loadPass) else PathLoadPass.NoLimit,
        m4labelsStr = m4define?.get("m4labelsStr")?.asText(),
        m4labels = getM4labels(m4define),
        remark = base64ToString(ac.desc) ?: "",
      )
      pathsMap[key] = path

      tracePoints.forEach { updateBounds(it.x, it.y) }
    }
  }

  private fun buildControls(fromPoint: MapPoint, toPoint: MapPoint, ac: SmapAdvancedCurve): List<NurbsControlPoint> =
    buildList {
      add(NurbsControlPoint(x = fromPoint.x, y = fromPoint.y, weight = ac.startPos.pos.z ?: 0.0))
      listOf(ac.controlPos1, ac.controlPos2, ac.controlPos3, ac.controlPos4).forEach { controlPos ->
        controlPos?.let {
          add(NurbsControlPoint(x = it.x, y = it.y, weight = it.z ?: 0.0))
        }
      }
      add(NurbsControlPoint(x = toPoint.x, y = toPoint.y, weight = ac.endPos.pos.z ?: 0.0))
    }

  private fun generateTracePoints(
    ac: SmapAdvancedCurve,
    spline: BSpline,
    length: Double,
  ): Pair<List<CurvePoint2D>, CurvePoint2D> {
    val tracePoints = when (curveSplitType) {
      // 包含起点终点在内 101 个点，即路径中 99 个点
      CurveSplitType.Num -> SmapCurveHelper.getPathPositionAndAngle(spline, 101, ac.className)
      // 改为等间距。每 0.1m 一个。未来可配置
      // actualLength / 0.1 是为了计算 0.1 长的段有多少个
      // + 3 是因为至少要保证有起点、中点、终点，有中点前端才能显示方向
      CurveSplitType.Distance -> {
        val num = (length / 0.1 + 3).toInt()
        SmapCurveHelper.getPathPositionAndAngle(spline, num, ac.className)
      }
    }

    if (tracePoints.isEmpty()) {
      throw BzError("errBzError", "Calculation of trace points failed, please check the map")
    }

    val middlePoint = tracePoints[tracePoints.size / 2]
    return Pair(tracePoints, middlePoint)
  }

  /**
   * 处理区块（高级区域）。不做合并。
   */
  private fun processZones(smap: Smap) {
    if (smap.advancedAreaList.isNullOrEmpty()) return

    for (a in smap.advancedAreaList) {
      val points: MutableList<Point2D> = ArrayList()
      for (p in a.posGroup) {
        points += Point2D(x = p.x, y = p.y)

        updateMinMaxX(p.x)
        updateMinMaxY(p.y)
      }

      val desc = a.desc
      val remark = try {
        if (desc.isBlank()) desc else String(Base64.getDecoder().decode(desc), Charsets.UTF_8)
      } catch (err: Exception) {
        // 如果解析失败，则将解析前的原文本记录到 remark 中。
        logger.error("process desc of SmapAdvancedArea failed, raw data is $a, set raw desc=$desc to remark")
        desc
      }

      zones += MapZone(
        id = ++idsCounter.zoneId,
        name = a.className + "_" + a.instanceName,
        type = a.className,
        remark = remark,
        // shape = ZoneShape.Polygon,
        // rect = Rect(),
        polygon = points,
        direction = a.dir,
        fontSize = a.property?.firstOrNull { it.key == "TextFontSize" }?.int32Value,
        borderColor = if (a.attribute?.colorPen != null) longToRgba(a.attribute.colorPen, false) else null,
        fillColor = if (a.attribute?.colorBrush != null) longToRgba(a.attribute.colorBrush, true) else null,
      )
    }
  }

  /**
   * 处理库位。合并策略，同名的取第一个。
   */
  private fun processBins(smap: Smap) {
    if (smap.binLocationsList.isNullOrEmpty()) return

    // 第一层是库区，第二层是库区下的库位
    for (list in smap.binLocationsList) {
      for (raw in list.binLocationList) {
        if (binsMap.containsKey(raw.instanceName)) continue
        val workPoint = pointsMap[raw.pointName]

        val binTaskMap: MutableMap<String, String> = mutableMapOf()
        val binTaskStr = raw.property?.firstOrNull { it.key == "binTask" }?.stringValue
        // stringValue 可能会为空字符串。
        if (!binTaskStr.isNullOrEmpty()) {
          JsonHelper.mapper.readValue<List<Map<String, Any>>>(binTaskStr, jacksonTypeRef())
            .forEach { binTaskList ->
              binTaskList.map { binTask ->
                // 1. 将 binTask 的内容（即 value）的类型从 Map 改为 Any，以免解析 smap 时报错。
                // 2. 暂时不使用 binTask 的内容，将其转换为字符串存储。
                // 3. 以后需要使用 binTask 的内容时，在将其反序列化，如果反序列化失败，则将此 binTask 的内容置为 emptyMap() 。
                // 4. 经操作确认：正常情况下，Roboshop 保存 binTask 时，会校验 binTask 内容，至少能保证内容不会是 null、或者 ""，
                //    但可能会出现 []、["a", "b"] 这种异常值。以防万一，还是优化一下。
                binTaskMap.put(binTask.key, JsonHelper.writeValueAsString(binTask.value))
              }
            }
        }
        // m4 的配置
        val m4define =
          raw.property?.firstOrNull { it.key == "M4define" }?.stringValue?.let { JsonHelper.mapper.readTree(it) }

        binsMap[raw.instanceName] = SceneBin(
          id = ++idsCounter.binId,
          name = raw.instanceName,
          x = raw.pos.x,
          y = raw.pos.y,
          layerNo = (raw.pos.z ?: 0).toInt() + 1, // 层数
          workPointId = workPoint?.id,
          workPointName = workPoint?.name,
          binTaskMap = binTaskMap,
          remark = m4define?.get("remark")?.asText() ?: "",
          unloadContainerDir = m4define?.get("unloadContainerDir")?.asDouble(),
          m4labelsStr = m4define?.get("m4labelsStr")?.asText(),
          m4labels = getM4labels(m4define),
        )

        val pointName = workPoint?.name
        if (!pointName.isNullOrBlank()) {
          val bins = binsOnPoints.getOrPut(pointName) { arrayOf(null, null) }
          if (bins[0] == null || raw.instanceName < bins[0]!!) bins[0] = raw.instanceName
          if (bins[1] == null || raw.instanceName > bins[1]!!) bins[1] = raw.instanceName
        }
      }
    }
  }

  /**
   * 处理禁行线。
   */
  private fun processRestrictedLines(smap: Smap) {
    if (smap.advancedLineList.isNullOrEmpty()) return

    for (raw in smap.advancedLineList) {
      // 起点和终点的坐标都相同则跳过
      if (restrictedLineMap.containsKey(raw.line)) continue
      restrictedLineMap[raw.line] = MapRestrictedLine(
        id = ++idsCounter.restrictedLineId,
        p1 = Point2D(x = raw.line.startPos.x, y = raw.line.startPos.y),
        p2 = Point2D(x = raw.line.endPos.x, y = raw.line.endPos.y),
      )

      updateMinMaxX(raw.line.startPos.x)
      updateMinMaxX(raw.line.endPos.x)

      updateMinMaxY(raw.line.startPos.y)
      updateMinMaxY(raw.line.endPos.y)
    }
  }

  /**
   * 处理激光点云。添加所有 smap 地图不重复的点云。
   */
  private fun processEnv(smap: Smap) {
    envPoints.addAll(smap.normalPosList)

    for (p in smap.normalPosList) {
      updateMinMaxX(p.x)
      updateMinMaxY(p.y)
    }
  }

  private fun updateMinMaxX(x: Double) {
    if (x < minX) minX = x
    if (x > maxX) maxX = x
  }

  private fun updateMinMaxY(y: Double) {
    if (y < minY) minY = y
    if (y > maxY) maxY = y
  }

  /**
   * 更新最小和最大坐标
   */
  private fun updateBounds(x: Double, y: Double) {
    minX = minOf(minX, x)
    maxX = maxOf(maxX, x)
    minY = minOf(minY, y)
    maxY = maxOf(maxY, y)
  }

  // 返回文件 path
  private fun buildSvgMap(): String {
    val minX = round(minX * svgLengthScale).toLong()
    val minY = round(-maxY * svgLengthScale).toLong() // 小心，翻转 y 轴
    val width = round(width * svgLengthScale).toLong()
    val height = round(height * svgLengthScale).toLong()

    val axisLen = round(5.0 * svgLengthScale).toLong()
    val axisArrowW = round(.33 * svgLengthScale).toLong()

    val svgStr = """
    <svg viewBox="$minX $minY $width $height" xmlns="http://www.w3.org/2000/svg">
      <g>
        <circle cx="0" cy="0" r="200" class="origin" style="fill: red;" />
        <path class="x-axis"
          d="M0,0 L$axisLen,0 M$axisLen,0 L${axisLen * .8},$axisArrowW M$axisLen,0 L${axisLen * .8},${-axisArrowW}"
          style="fill: none; stroke: #f22; stroke-width: 100;" />
        <path class="y-axis"
          d="M0,0 L0,-$axisLen M0,-$axisLen L$axisArrowW,-${axisLen * .8} M0,-$axisLen L${-axisArrowW},-${axisLen * .8}"
          style="fill: none; stroke: #13D313; stroke-width: 100;" />
      </g>
      <g>
        ${buildSvgPaths()}
      </g>
      <g>
        ${buildSvgPoints()}
      </g>
    </svg>
    """.trimIndent()

    val svgFile = FileManager.nextTmpFile("svg", prefix = "area-map-")
    FileUtils.writeStringToFile(svgFile, svgStr, StandardCharsets.UTF_8)

    return FileManager.fileToPath(svgFile)
  }

  private fun buildSvgPoints(): String = pointsMap.values.joinToString("\n") { point ->
    val x = round(point.x * svgLengthScale).toLong()
    val y = round(-point.y * svgLengthScale).toLong()
    val transform = "translate($x, $y)"
    val label = StringUtils.firstNonBlank(point.name, "(${point.x}, ${point.y})")
    val boundW = 200
    val borderRadius = boundW / 2

    val bins = binsOnPoints[point.name]
    val binStr = if (bins == null || bins[0] == null && bins[1] == null) {
      null
    } else if (bins[0] != null && bins[1] == null) {
      bins[0]
    } else if (bins[0] == null && bins[1] != null) {
      bins[1]
    } else if (bins[0] != bins[1]) {
      "${bins[0]} ~ ${bins[1]}"
    } else {
      bins[0]
    }

    val binDesc = if (binStr != null) {
      """<tspan x="0" dy="180" text-anchor="middle" style="font-size: 140px" fill="#2b702b">$binStr</tspan>"""
    } else {
      ""
    }

    """
      <g class="map-point" data-point-id="${point.id}" data-point-label="${point.name}" transform="$transform">
        <rect class="point-main" rx="$borderRadius" ry="$borderRadius"
          width="$boundW" height="$boundW" x="${-boundW / 2}" y="${-boundW / 2}"
          fill="#6d90ec" />
        <text class="point-label" x="0" y="240">
          <tspan x="0" text-anchor="middle" style="font-size: 170px" fill="#333">$label</tspan>$binDesc
        </text>
      </g>
    """.trimIndent()
  }

  private fun buildSvgPaths(): String {
    return pathsMap.values.joinToString("\n") { path ->
      // 显示方向
      val daStart = round(0.02 * svgLengthScale).toLong()
      val daShortSite = round(0.05 * svgLengthScale).toLong()
      val daLongSite = round(0.12 * svgLengthScale).toLong()

      // 双向线只画 fromPointName < toPointName 的那个
      val reversed = pathsMap[MapPath.getKey(path.toPointName, path.fromPointName)]
      if (reversed != null && path.fromPointName > path.toPointName) return@joinToString ""

      val directionData = if (reversed != null) {
        "M$daStart,${-daShortSite} L$daStart,$daShortSite L$daLongSite,0 L$daStart,${-daShortSite}" +
          "M${-daStart},${-daShortSite} L${-daStart},$daShortSite L${-daLongSite}, 0 L${-daStart},${-daShortSite}"
      } else {
        "M$daStart,${-daShortSite} L$daStart,$daShortSite L$daLongSite,0 L$daStart,${-daShortSite}"
      }

      val middleDirDeg = round(-(path.middlePoint.tangent) * 180 / Math.PI)
      val middleX = round(path.middlePoint.x * svgLengthScale).toLong()
      val middleY = round(-path.middlePoint.y * svgLengthScale).toLong()
      val directionTransform = "translate($middleX, $middleY) rotate($middleDirDeg)"

      val d = "M" +
        path.tracePoints.joinToString(" L") {
          "${round(it.x * svgLengthScale).toLong()},${round(-it.y * svgLengthScale).toLong()}"
        }

      """
      <g class="map-path" data-path-id="${path.id}" data-path="${path.fromPointName}-${path.toPointName}">
        <path class="path-main" d="$d"
          style="fill: none; stroke: #666; stroke-opacity: 0.75; stroke-width: 50; stroke-linecap: round;" />
        <path class="path-direction" d="$directionData" transform="$directionTransform"
          style="fill: #555; fill-opacity: 0.8;" />
       </g>
      """.trimIndent()
    }
  }

  /**
   * 数值类型转 RGBA
   */
  private fun longToRgba(color: Long, isFill: Boolean): String {
    val colorLong = color.toInt()
    val red = ((colorLong shr 16) and 0xFF) // 红色 (0-255)
    val green = ((colorLong shr 8) and 0xFF) // 绿色 (0-255)
    val blue = (colorLong and 0xFF) // 蓝色 (0-255)
    // smap 的颜色是无透明度的，填充的颜色默认给 0.1，非填充给 0.9
    return if (isFill) {
      "rgba($red, $green, $blue, 0.1)"
    } else {
      "rgba($red, $green, $blue, 0.9)"
    }
  }
}

enum class CurveSplitType {
  Num,
  Distance,
}