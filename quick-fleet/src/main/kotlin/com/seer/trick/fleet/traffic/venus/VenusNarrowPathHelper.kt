package com.seer.trick.fleet.traffic.venus

import com.seer.trick.fleet.domain.MapPath
import com.seer.trick.fleet.helper.AngleHelper
import com.seer.trick.fleet.map.AreaMapCache
import org.slf4j.LoggerFactory

/**
 * Venus系统窄边通行辅助类
 * 参考distributed系统的窄边通行实现，为Venus系统提供相同的功能
 */
object VenusNarrowPathHelper {
  
  private val logger = LoggerFactory.getLogger(javaClass)
  
  /**
   * 容器模型信息，用于窄边通行计算
   */
  data class ContainerModel(
    val sceneId: String,
    val typeName: String,
    val length: Double,
    val width: Double,
    val height: Double,
    val radius: Double,
    val narrowDir: Int, // 窄边方向
  )
  
  /**
   * 容器模型缓存
   */
  private val containerModelMap: MutableMap<String, ContainerModel> = mutableMapOf()
  
  /**
   * 添加容器模型
   */
  fun addContainerModel(model: ContainerModel) {
    if (model.height < 0.001 || model.width < 0.001) {
      logger.error("add container type ${model.typeName} model is empty!")
      return
    }
    containerModelMap[typeKey(model.sceneId, model.typeName)] = model
  }
  
  /**
   * 查询容器模型的窄边方向
   */
  fun queryContainerModelNarrowDir(sceneId: String, containerType: String): Int =
    containerModelMap[typeKey(sceneId, containerType)]?.narrowDir ?: 0
  
  /**
   * 检查路径是否为窄边通行路径
   */
  fun isNarrowPath(path: MapPath): Boolean {
    return path.containerShortSideAhead
  }
  
  /**
   * 获取路径的容器方向
   */
  fun getPathContainerDir(path: MapPath): Int? {
    return if (path.containerDir == null) null else AngleHelper.convertAngle(path.containerDir)
  }
  
  /**
   * 计算窄边通行时的容器朝向
   * 参考distributed系统的实现逻辑
   */
  fun calculateNarrowPathContainerHeadings(
    pathEnterDir: Int,
    narrowDir: Int,
    containerDir: Int?
  ): List<Int> {
    val headings = mutableListOf<Int>()
    
    // 如果路径指定了容器方向，直接使用
    if (containerDir != null) {
      headings.add(containerDir)
      return headings
    }
    
    // 窄边通行的容器朝向计算
    headings.add(AngleHelper.processAngle(pathEnterDir + narrowDir))
    headings.add(AngleHelper.processAngle(pathEnterDir + AngleHelper.DOWN_ANGLE + narrowDir))
    
    return headings
  }
  
  /**
   * 检查机器人在容器下是否可以旋转
   * 参考distributed系统的逻辑
   */
  fun checkRobotInContainerCanRotate(
    sceneId: String, 
    robotGroupRadius: Double, 
    containerType: String
  ): Boolean {
    val containerModel = containerModelMap[typeKey(sceneId, containerType)]
      ?: return false
    
    val containerRadius = 0.5 * kotlin.math.sqrt(
      containerModel.length * containerModel.length + 
      containerModel.width * containerModel.width
    )
    
    return containerRadius > robotGroupRadius
  }
  
  /**
   * 计算容器的窄边方向
   * 如果长度大于宽度，窄边方向为0，否则为90度
   */
  fun calculateNarrowDir(length: Double, width: Double): Int {
    return if (length > width) 0 else AngleHelper.RIGHT_ANGLE
  }
  
  /**
   * 验证窄边通行的约束条件
   */
  fun validateNarrowPathConstraints(
    robotName: String,
    containerType: String?,
    path: MapPath,
    robotHeading: Int,
    containerHeading: Int
  ): Boolean {
    if (containerType == null || !isNarrowPath(path)) {
      return true
    }
    
    val narrowDir = queryContainerModelNarrowDir("", containerType)
    val pathEnterDir = AngleHelper.convertAngle(path.tracePoints.first().tangent)
    val expectedHeadings = calculateNarrowPathContainerHeadings(pathEnterDir, narrowDir, getPathContainerDir(path))
    
    // 检查容器朝向是否符合窄边通行要求
    return expectedHeadings.any { expectedHeading ->
      AngleHelper.sameAngleInFiveDegree(containerHeading, expectedHeading)
    }
  }
  
  /**
   * 生成类型键
   */
  private fun typeKey(sceneId: String, containerType: String): String = "$sceneId-$containerType"
  
  /**
   * 清理容器模型缓存
   */
  fun clearContainerModels() {
    containerModelMap.clear()
  }
  
  /**
   * 获取所有容器模型
   */
  fun getAllContainerModels(): Map<String, ContainerModel> = containerModelMap.toMap()
}
