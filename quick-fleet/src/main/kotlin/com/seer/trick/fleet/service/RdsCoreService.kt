package com.seer.trick.fleet.service

import com.seer.trick.fleet.domain.SceneHelper
import com.seer.trick.fleet.seer.RdsCoreSceneToM4SceneConverter
import java.io.File

/**
 * 处理一些与 CORE 的兼容性
 */
object RdsCoreService {

  /**
   * 导入 RDS CORE 场景。
   * 导入前需要确定为哪个场景导入的（需要 scene id），因为有些资源要存储。
   */
  fun importRdsCoreScene(
    sr: SceneRuntime,
    rdsCoreSceneFile: File,
    check: Boolean = false,
  ): Pair<Boolean, List<String>> {
    val structure = RdsCoreSceneToM4SceneConverter(sr.sceneId, rdsCoreSceneFile).build()

    // 检查一下区域地图是否存在圆弧的路径
    if (check) {
      for (area in structure.areas) {
        if (SceneHelper.existArcPath(sr.sceneId, area.groupsMap)) return Pair(false, emptyList())
      }
    }
    // 先检查场景是否空闲
    sr.throwIfSceneWithOrders()

    // TODO 前后改场景状态
    // sr.status == SceneStatus.Initializing

    RobotGroupService.replaceAllGroups(sr, structure.robotGroups)

    // RobotTagService.replace(sr, structure.robotTags)

    RobotService.replaceAllRobots(sr, structure.robots)

    val checkContext = CheckAreaContext(mutableListOf())
    SceneAreaService.replaceAllAreas(sr, structure.areas, checkContext)

    return Pair(true, checkContext.errors)
  }
}