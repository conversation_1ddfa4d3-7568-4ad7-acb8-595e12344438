package com.seer.trick.fleet.mock

import com.seer.trick.fleet.domain.GeoHelper
import com.seer.trick.fleet.mock.service.MockAlarmProcessor
import com.seer.trick.fleet.seer.*
import org.jgrapht.Graph
import org.jgrapht.alg.shortestpath.DijkstraShortestPath
import org.jgrapht.graph.DefaultWeightedEdge
import org.jgrapht.graph.builder.GraphTypeBuilder
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.util.concurrent.ConcurrentHashMap

object MockSmapHelper {

  /**
   * TODO helper 里尽量不放状态，只放方法，这里可以放到 mock 里
   */
  private val md5ToSmapMapUtil: MutableMap<String, SmapMapUtil> = ConcurrentHashMap()

  private val mockSeerRobotIdToSmapMapUtil: MutableMap<String, SmapMapUtil> = ConcurrentHashMap()

  /**
   * 在初始化机器人的时候需要这样处理
   * 1.从文件或直接复制读取地图
   * 2.加载地图，并进行解析，调用 loadMap 方法，返回地图的 md5
   * 3.将 md5 存储到机器人 record 的 map 中
   * 加载地图，每个地图一份，以 md5 区分, 返回 md5
   */
  fun loadMap(sf: SmapFile, mr: MockSeerRobotRuntime) {
    try {
      val smapHelper = md5ToSmapMapUtil.computeIfAbsent(sf.md5) {
        buildSmapMapUtil(sf.content)
      }
      mockSeerRobotIdToSmapMapUtil[mr.robotId] = smapHelper
    } catch (e: Exception) {
      // 加载地图失败告警
      MockAlarmProcessor.addOrUpdateAlarm(mr, MockSeerRobotAlarm("Fatal", "50101", "map load error"))
    }
  }

  /**
   * 加载 map 之后，构建 SmapMapUtil，其中包含一些常用的 map
   */
  private fun buildSmapMapUtil(smap: Smap): SmapMapUtil {
    val pointHelper = smap.advancedPointList?.associateBy({ it.instanceName }, { point ->
      PointHelper(
        point = point,
        forwardPaths = smap.advancedCurveList?.filter { it.startPos.instanceName == point.instanceName } ?: emptyList(),
        backwardPaths = smap.advancedCurveList?.filter { it.endPos.instanceName == point.instanceName } ?: emptyList(),
      )
    }) ?: emptyMap()

    val pathHelper = smap.advancedCurveList?.associateBy({ it.instanceName }, { curve ->
      val spline = SmapCurveHelper.transferPathToSpline(curve)
      val pathPositionAndAngleList = spline.let {
        SmapCurveHelper.getPathPositionAndAngle(it, 100, curve.className)
      }
      PathHelper(
        curve,
        listOf(pointHelper[curve.startPos.instanceName]!!, pointHelper[curve.endPos.instanceName]!!),
        pathPositionAndAngleList,
      )
    }) ?: emptyMap()

    val zoneHelper = smap.advancedAreaList?.associateBy({ it.instanceName }, { area ->
      ZoneHelper(area)
    }) ?: emptyMap()

    val binHelper = smap.binLocationsList?.flatMap { it.binLocationList }?.associateBy({ it.instanceName }, { bin ->
      BinHelper(bin, pointHelper[bin.pointName])
    }) ?: emptyMap()

    return SmapMapUtil(smap, pointHelper, pathHelper, zoneHelper, binHelper)
  }

  /**
   * 根据机器人的 id 获取 SmapMapUtil
   */
  private fun getSmapMapUtilByMockSeerRobotId(robotId: String): SmapMapUtil? = mockSeerRobotIdToSmapMapUtil[robotId]

  /**
   * 根据地图的 Md5 获取 SmapMapUtil
   */
  private fun getSmapMapUtilByMd5(md5: String): SmapMapUtil = md5ToSmapMapUtil[md5]!!

  /**
   * 库位/站点名 -> PointHelper
   */
  fun getPointHelper(robotId: String, name: String): PointHelper? {
    val smapMapUtil = getSmapMapUtilByMockSeerRobotId(robotId)
    return smapMapUtil?.pointNameMap?.get(name) ?: smapMapUtil?.binNameMap?.get(name)?.pointHelper
  }

  /**
   * 一条线路的起终点，获取对应的 PointHelper
   */
  fun getPathByFromAndTo(robotId: String, from: String, to: String): PathHelper? {
    val smapMapUtil = getSmapMapUtilByMockSeerRobotId(robotId)
    val fromPointHelper = getPointHelper(robotId, from)
    val toPointHelper = getPointHelper(robotId, to)
    return fromPointHelper?.forwardPaths?.firstOrNull { path ->
      path.instanceName == fromPointHelper.point.instanceName + "-" + toPointHelper?.point?.instanceName
    }?.let { smapMapUtil!!.pathNameMap[it.instanceName] }
  }

  fun smapToByteBuffer(smap: Smap): ByteBuffer {
    val pointCloudSize = smap.normalPosList.size
    val pcBuffer = ByteBuffer.allocate(pointCloudSize * 2 * 8)
    pcBuffer.order(ByteOrder.BIG_ENDIAN)
    smap.normalPosList.forEach { p ->
      pcBuffer.putDouble(p.x)
      pcBuffer.putDouble(p.y)
    }
    pcBuffer.flip()
    return pcBuffer
  }

  /**
   * 找到起点与终点的最短路径
   */
  fun findShortestPath(robotId: String, from: String, to: String): List<String>? {
    val smapMapUtil = getSmapMapUtilByMockSeerRobotId(robotId)
    val graph = buildGraph(smapMapUtil!!)
    val dijkstraAlg = DijkstraShortestPath(graph)
    val path = dijkstraAlg.getPath(from, to) ?: return null
    return path.vertexList
  }

  /**
   * 获取距离机器人最近的站点
   */
  fun getClosestPoint(mr: MockSeerRobotRuntime, close: Double = 1.0): String? {
    var min = Double.MAX_VALUE
    var minPoint: String? = null
    val smapMapUtil = getSmapMapUtilByMockSeerRobotId(mr.robotId)
    smapMapUtil!!.pointNameMap.forEach { (_, value) ->
      val d = GeoHelper.euclideanDistance(value.point.pos.x, value.point.pos.y, mr.record.x, mr.record.y)
      if (d < min && d < close) {
        min = d
        minPoint = value.point.instanceName
      }
    }
    return minPoint
  }

  /**
   * 获取机器人距离最近的线以及线上点的索引,
   */
  fun getClosestPathList(mr: MockSeerRobotRuntime, close: Double = 0.5): List<ClosestPath> {
    val smapMapUtil = getSmapMapUtilByMockSeerRobotId(mr.robotId)
    val closestPathMap: MutableMap<String, ClosestPath> = mutableMapOf()

    smapMapUtil!!.pathNameMap.forEach { (_, value) ->
      value.points.forEachIndexed { index, e ->
        val d = GeoHelper.euclideanDistance(e.x, e.y, mr.record.x, mr.record.y)
        if (d < close) {
          val existingClosestPath = closestPathMap[value.path.instanceName]
          if (existingClosestPath == null || d < existingClosestPath.distance) {
            closestPathMap[value.path.instanceName] = ClosestPath(pathHelper = value, index = index, distance = d)
          }
        }
      }
    }
    return closestPathMap.values.toList()
  }

  /**
   * 获取机器人附近线以及线上点的索引,附近指的是距离在阈值内
   */
  fun getClosePathList(mr: MockSeerRobotRuntime, close: Double = 0.5): List<ClosestPath> {
    val smapMapUtil = getSmapMapUtilByMockSeerRobotId(mr.robotId)
    val closestPathMap: MutableMap<String, ClosestPath> = mutableMapOf()

    smapMapUtil!!.pathNameMap.forEach { (_, value) ->
      value.points.forEachIndexed { index, e ->
        // d 是机器人到路径的最短距离
        val d = GeoHelper.euclideanDistance(e.x, e.y, mr.record.x, mr.record.y)
        val exist = closestPathMap[value.path.instanceName]
        if (d < close && (exist == null || d < exist.distance)) {
          closestPathMap[value.path.instanceName] = ClosestPath(pathHelper = value, index = index, distance = d)
        }
      }
    }
    return closestPathMap.values.toList()
  }

  /**
   * 构建图，用于寻路
   */
  private fun buildGraph(smapMapUtil: SmapMapUtil): Graph<String, DefaultWeightedEdge>? {
    val graph = GraphTypeBuilder
      .directed<String, DefaultWeightedEdge>()
      .allowingMultipleEdges(false)
      .allowingSelfLoops(false)
      .edgeClass(DefaultWeightedEdge::class.java)
      .weighted(true)
      .buildGraph()
    smapMapUtil.pointNameMap.keys.forEach { graph.addVertex(it) }
    smapMapUtil.pathNameMap.values.forEach {
      val from = it.path.startPos.instanceName
      val to = it.path.endPos.instanceName
      val length = GeoHelper.euclideanDistance(
        it.path.startPos.pos.x,
        it.path.startPos.pos.y,
        it.path.endPos.pos.x,
        it.path.endPos.pos.y,
      )
      val edge = graph.addEdge(from, to)
      if (edge != null) {
        graph.setEdgeWeight(edge, length)
      }
    }
    return graph
  }
}