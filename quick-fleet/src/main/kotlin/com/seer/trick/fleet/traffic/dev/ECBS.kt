package com.seer.trick.fleet.traffic.dev

import com.seer.trick.BzError
import com.seer.trick.fleet.FleetConcurrentCenter.trafficParallelExecutor
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ExecutionException
import kotlin.math.max

/**
 *  Enhanced Conflict-Based-Search (ECBS)
 *  寻找多个机器人的无冲突路径，每个机器人单个起点终点
 */
class ECBS(
  private val w: Double,
  private val mapDimX: Int,
  private val mapDimY: Int, // y 向下为正
  private val obstacles: Set<Int>,
  private val tasks: Map<String, RobotTask>, // robot ->
) {

  private val logger = LoggerFactory.getLogger(this::class.java)

  private val startOn = System.currentTimeMillis()

  @Volatile
  private var hlNodeId = 0L

  @Volatile
  private var highLevelNodeExpanded = 0L

  @Volatile
  private var lowLevelNodeExpanded = 0L

  fun search(): MapfResult {
    val rootNode = buildRootHLNode() ?: return MapfResult(
      false,
      emptyMap(),
      timeCost = System.currentTimeMillis() - startOn,
      // highLevelNodeExpanded = highLevelNodeExpanded,
      // lowLevelNodeExpanded = lowLevelNodeExpanded,
    )

    val open = PriorityQueue<HighLevelNode> { o1, o2 ->
      // if (cost != n.cost)
      o1.cost.compareTo(o2.cost)
      // return id > n.id;
    }
    val focal = PriorityQueue<HighLevelNode> { o1, o2 ->
      if (o1.focalHeuristic != o2.focalHeuristic) {
        o1.focalHeuristic.compareTo(o2.focalHeuristic)
      } else {
        o1.cost.compareTo(o2.cost)
      }
    }

    open.offer(rootNode)
    focal.offer(rootNode)

    var bestCost = rootNode.cost

    while (open.isNotEmpty()) {
      if (System.currentTimeMillis() - startOn > 5000) throw BzError("errBzError", "No solution found within 5s")

      val oldBestCost = bestCost
      bestCost = open.peek().cost
      if (bestCost > oldBestCost) {
        for (iter in open) {
          if (iter.cost > oldBestCost * w && iter.cost <= bestCost * w) {
            focal.offer(iter)
          }
          // if (val > bestCost * m_w) {
          //   break;
          // }
        }
      }

      val p = focal.poll()
      open.remove(p)
      logger.info("[H=${p.id}] Focal 取出, cost=${p.cost}")
      // m_env.onExpandHighLevelNode(P.cost);
      ++highLevelNodeExpanded

      val constraints = getFirstConflictConstraints(p.solution) ?: return MapfResult(
        true,
        p.solution.mapValues {
          TargetManyPlanResult(
            it.value.robotName,
            it.value.ok,
            it.value.reason,
            it.value.cost,
            it.value.steps.map { s ->
              TargetOnePlanResult(
                s.robotName,
                s.ok,
                s.reason,
                s.cost,
                // s.fromState,
                // s.toState,
                s.path,
                s,
              )
            },
            it.value.path,
            it.value,
          )
        },
        timeCost = System.currentTimeMillis() - startOn,
        // highLevelNodeExpanded = highLevelNodeExpanded,
        // lowLevelNodeExpanded = lowLevelNodeExpanded,
      )

      logger.info("[H=${p.id}] 新约束：" + constraints.map { it.key } + " || $constraints")

      // TODO 防止高层节点重复检查？

      constraints.map { (robotName, c) ->
        trafficParallelExecutor.submit<HighLevelNode?> {
          return@submit buildChildHLNode(p, robotName, c)
        }
      }.forEach {
        val childNode = try {
          it.get()
        } catch (e: ExecutionException) {
          throw e.cause ?: e
        } ?: return@forEach
        open.offer(childNode)
        if (childNode.cost <= bestCost * w) {
          focal.offer(childNode)
        }
      }
    }

    return MapfResult(
      false,
      emptyMap(),
      timeCost = System.currentTimeMillis() - startOn,
      // highLevelNodeExpanded = highLevelNodeExpanded,
      // lowLevelNodeExpanded = lowLevelNodeExpanded,
    )
  }

  private fun buildRootHLNode(): HighLevelNode? {
    val solution: MutableMap<String, FSA.TargetManyPlanResult> = ConcurrentHashMap()

    val futures = tasks.map { (robotName, task) ->
      trafficParallelExecutor.submit<FSA.TargetManyPlanResult> {
        val constraints = Constraints(robotName)

        val aStar = FSA(
          robotName, hlNodeId, w, mapDimX, mapDimY, obstacles,
          moveUnitCost = 1.0, rotateUnitCost = 1.0,
          isValidNeighbor = { from, to ->
            stateValid(to, constraints) && transitionValid(from, to, constraints) // 初始约束为空
          },
          focalStateHeuristic2 = { s, _ ->
            focalStateHeuristic(s, robotName, solution)
          },
          focalTransitionHeuristic2 = { s1a, s1b, _, _ ->
            focalTransitionHeuristic(s1a, s1b, robotName, solution)
          },
        )
        // 先每个智能体从起点到终点各自搜路？如果搜不到，一定没解。
        logger.debug("Init Search for $robotName")

        return@submit aStar.search(
          State.fromCell(task.fromState),
          task.toStates.map { State.fromCell(it) },
          task.stopTimes,
        )
      }
    }

    var cost = 0.0
    var lb = 0.0

    for (future in futures) {
      val rs = try {
        future.get()
      } catch (e: ExecutionException) {
        throw e.cause ?: e
      }
      lowLevelNodeExpanded += rs.expandedCount
      if (!rs.ok) {
        logger.error("Robot ${rs.robotName} no init solution")
        return null
      }
      solution[rs.robotName] = rs
      cost += rs.cost
      lb += rs.minF
    }

    val focalHeuristic = focalHeuristic(solution)

    // 确保每个机器人都有一个空约束
    val constraints: MutableMap<String, Constraints> = HashMap()
    for (robotName in tasks.keys) {
      constraints[robotName] = Constraints(robotName)
    }

    return HighLevelNode(
      id = hlNodeId,
      parentId = -1,
      solution = solution,
      constraints = constraints,
      cost = cost,
      lb = lb,
      focalHeuristic = focalHeuristic,
    )
  }

  private fun addConstraints(
    parent: HighLevelNode,
    robotName: String,
    newConstraints: Constraints,
  ): HashMap<String, Constraints> {
    val childConstraints = HashMap(parent.constraints) // 继承父节点的约束
    val oldRobotConstraints = childConstraints[robotName]!!
    if (oldRobotConstraints.contain(newConstraints)) {
      logger.error("Constraints already exist for robot $robotName: $newConstraints -> $oldRobotConstraints")
      throw BzError("errCode", "Constraints already exist for robot $robotName")
    }
    val vertexConstraints = oldRobotConstraints.vertexConstraints + newConstraints.vertexConstraints
    val edgeConstraints = oldRobotConstraints.edgeConstraints + newConstraints.edgeConstraints
    val newRobotConstraints = oldRobotConstraints.copy(
      vertexConstraints = vertexConstraints,
      edgeConstraints = edgeConstraints,
    )
    childConstraints[robotName] = newRobotConstraints
    return childConstraints
  }

  private fun buildChildHLNode(parent: HighLevelNode, robotName: String, newConstraints: Constraints): HighLevelNode? {
    // robotName 施加约束的机器人
    val childConstraints = addConstraints(parent, robotName, newConstraints)
    val robotConstraints = childConstraints[robotName]!!

    val solution = HashMap(parent.solution)

    var cost = parent.cost - solution[robotName]!!.cost
    var lb = parent.lb - solution[robotName]!!.minF

    val childNodeId = synchronized(this) {
      ++hlNodeId
    }

    val aStar = FSA(
      robotName, childNodeId, w, mapDimX, mapDimY, obstacles,
      moveUnitCost = 1.0, rotateUnitCost = 1.0,
      isValidNeighbor = { from, to ->
        stateValid(to, robotConstraints) && transitionValid(from, to, robotConstraints)
      },
      focalStateHeuristic2 = { s, _ ->
        focalStateHeuristic(s, robotName, solution) // 这里是子节点的 solution，不能是父节点的
      },
      focalTransitionHeuristic2 = { s1a, s1b, _, _ ->
        focalTransitionHeuristic(s1a, s1b, robotName, solution) // 这里是子节点的 solution，不能是父节点的
      },
    )
    val task = tasks[robotName]!!
    val rs = aStar.search(
      State.fromCell(task.fromState),
      task.toStates.map {
        State.fromCell(it)
      },
      task.stopTimes,
    )
    lowLevelNodeExpanded += rs.expandedCount

    // TODO 会不 OK 嘛，会
    if (!rs.ok) {
      return null
    }

    solution[robotName] = rs

    cost += rs.cost
    lb += rs.minF
    val focalHeuristic = focalHeuristic(solution)

    logger.info("产生新的高层节点 $childNodeId，父节点=${parent.id}，机器人=$robotName，cost=$cost")
    return HighLevelNode(
      id = childNodeId,
      parentId = parent.id,
      constraints = childConstraints,
      solution = solution,
      cost = cost,
      lb = lb,
      focalHeuristic = focalHeuristic,
    )
  }

  // 返回的 map 有两个元素。每个表示参与约束的一个机器人
  private fun getFirstConflictConstraints(solution: Map<String, FSA.TargetManyPlanResult>): Map<String, Constraints>? {
    val constraints = mutableMapOf<String, Constraints>()

    val robotNames = solution.keys.toList()

    for (r1i in 0 until robotNames.size - 1) {
      val robotName1 = robotNames[r1i]
      val sol1 = solution[robotName1]!!
      for (r2i in r1i + 1 until robotNames.size) {
        val robotName2 = robotNames[r2i]
        val sol2 = solution[robotName2]!!

        // 顶点约束
        // 遍历两个机器人的每个状态，如果位置相同且时间交叠
        for (s1 in sol1.path) {
          for (s2 in sol2.path) {
            if (s1.isSameLocation(s2) && s1.isTimeOverlay(s2)) {
              val c1 = Constraints(
                robotName1,
                vertexConstraints = setOf(VertexConstraint(s1.x, s1.y, s2.timeStart, s2.timeEnd)),
              )
              // 注意用另一个机器人的时间
              constraints[robotName1] = c1

              val c2 = Constraints(
                robotName2,
                vertexConstraints = setOf(VertexConstraint(s2.x, s2.y, s1.timeStart, s1.timeEnd)),
              )
              // 注意用另一个机器人的时间
              constraints[robotName2] = c2

              return constraints
            }
          }
        }

        // 边约束
        // 遍历两个机器人的所有移动 i -> i+1
        // 时间范围取占起点的开始时间到终点的结束时间
        for (s1i in 0 until sol1.path.size - 1) {
          val s1a = sol1.path[s1i]
          val s1b = sol1.path[s1i + 1]
          if (s1a.isSameLocation(s1b)) continue
          for (s2i in 0 until sol2.path.size - 1) {
            val s2a = sol2.path[s2i]
            val s2b = sol2.path[s2i + 1]
            if (s2a.isSameLocation(s2b)) continue

            if (s1a.isSameLocation(s2b) && s1b.isSameLocation(s2a) && s1b.isTimeOverlay(s2b)) {
              val c1 = Constraints(
                robotName1,
                edgeConstraints = setOf(EdgeConstraint(s1a.x, s1a.y, s1b.x, s1b.y, s2a.timeStart, s2b.timeEnd)),
              )
              // 注意用另一个机器人的时间
              constraints[robotName1] = c1

              val c2 = Constraints(
                robotName2,
                edgeConstraints = setOf(EdgeConstraint(s2a.x, s2a.y, s2b.x, s2b.y, s1a.timeStart, s1b.timeEnd)),
              )
              // 注意用另一个机器人的时间
              constraints[robotName2] = c2

              return constraints
            }
          }
        }
      }
    }

    return null
  }

  // 一个智能体的。在边界内，不是障碍物，不与约束冲突。
  private fun stateValid(toState: State, constraints: Constraints): Boolean {
    val con = constraints.vertexConstraints
    if (!(
        toState.x in 0 until mapDimX &&
          toState.y >= 0 &&
          toState.y < mapDimY &&
          !obstacles.contains(toState.y * mapDimX + toState.x)
        )
    ) {
      return false
    }
    val c = con.find {
      toState.isSameLocation(it) && toState.isTimeOverlay(it)
    }
    return c == null
    // logger.debug("Vertex constraint violated ${constraints.robotName}: $toState x $c")
  }

  private fun transitionValid(fromState: State, toState: State, constraints: Constraints): Boolean {
    if (fromState.isSameLocation(toState)) return true
    val con = constraints.edgeConstraints
    return !con.any {
      // 起点、终点位置相同
      it.x1 == fromState.x &&
        it.y1 == fromState.y &&
        it.x2 == toState.x &&
        it.y2 == toState.y &&
        State.isTimeOverlay(fromState.timeStart, toState.timeEnd, it.timeStart, it.timeEnd)
    }
  }

  // 如果目标位置被约束了，等约束结束后，机器人才能到达目标
  private fun constraintsToLastGoalConstraint(constraints: Constraints, goal: State): Long {
    var lastGoalConstraint = -1L
    for (vc in constraints.vertexConstraints) {
      if (vc.x == goal.x && vc.y == goal.y) {
        lastGoalConstraint = max(lastGoalConstraint, vc.timeEnd)
      }
    }
    return lastGoalConstraint
  }

  // Count all conflicts
  private fun focalHeuristic(solution: MutableMap<String, FSA.TargetManyPlanResult>): Double {
    var numConflicts = 0
    var tMax = 0
    for (sol in solution.values) {
      tMax = max(tMax, sol.path.size - 1)
    }
    val robotNames = solution.keys.toList()
    for (t in 0 until tMax) {
      // check drive-drive vertex collisions
      for (i in 0 until robotNames.size) {
        val state1 = getState(robotNames[i], solution, t.toLong())
        for (j in i + 1 until robotNames.size) {
          val state2 = getState(robotNames[j], solution, t.toLong())
          if (state1.x == state2.x && state1.y == state2.y) {
            ++numConflicts
          }
        }
      }

      // drive-drive edge (swap)
      for (i in 0 until robotNames.size) {
        val state1a = getState(robotNames[i], solution, t.toLong())
        val state1b = getState(robotNames[i], solution, t.toLong() + 1)
        for (j in i + 1 until robotNames.size) {
          val state2a = getState(robotNames[j], solution, t.toLong())
          val state2b = getState(robotNames[j], solution, t.toLong() + 1)
          if (state1a.x == state2b.x && state1a.y == state2b.y && state1b.x == state2a.x && state1b.y == state2a.y) {
            ++numConflicts
          }
        }
      }
    }
    return numConflicts.toDouble()
  }

  private fun getState(robotName: String, solution: Map<String, FSA.TargetManyPlanResult>, t: Long): State {
    val path = solution[robotName]!!.path
    return (path.find { it.timeStart >= t && it.timeEnd <= t } ?: path.last())
  }

  // 相交的
  private fun getState(
    robotName: String,
    solution: Map<String, FSA.TargetManyPlanResult>,
    tStart: Long,
    tEnd: Long,
  ): State {
    val path = solution[robotName]!!.path
    return (path.find { !(it.timeEnd < tStart || it.timeStart > tEnd) } ?: path.last())
  }

  // low-level
  private fun focalStateHeuristic(
    s: State,
    fromRobot: String,
    solution: Map<String, FSA.TargetManyPlanResult>,
  ): Double {
    var numConflicts = 0
    for (robotName in solution.keys) {
      if (robotName != fromRobot && solution[robotName] != null) {
        val state2 = getState(robotName, solution, s.timeStart, s.timeEnd)
        if (s.x == state2.x && s.y == state2.y) ++numConflicts
      }
    }
    return numConflicts.toDouble()
  }

  // low-level
  private fun focalTransitionHeuristic(
    s1a: State,
    s1b: State,
    fromRobot: String,
    solution: Map<String, FSA.TargetManyPlanResult>,
  ): Double {
    var numConflicts = 0
    for (robotName in solution.keys) {
      if (robotName != fromRobot && solution[robotName] != null) {
        val s2a = getState(robotName, solution, s1a.timeStart, s1a.timeEnd)
        val s2b = getState(robotName, solution, s1b.timeStart, s1b.timeEnd)
        if (s1a.x == s2b.x && s1a.y == s2b.y && s1b.x == s2a.x && s1b.y == s2a.y) ++numConflicts
      }
    }
    return numConflicts.toDouble()
  }

  class HighLevelNode(
    val id: Long, // 节点 ID
    val parentId: Long,
    val solution: Map<String, FSA.TargetManyPlanResult>, // robot name ->
    val constraints: Map<String, Constraints>, // robot name ->
    val cost: Double,
    val lb: Double,
    val focalHeuristic: Double,
  )

  data class VertexConstraint(
    override val x: Int,
    override val y: Int,
    override val timeStart: Long,
    override val timeEnd: Long,
  ) : TimeSteps,
    Location

  data class EdgeConstraint(
    val x1: Int,
    val y1: Int,
    val x2: Int,
    val y2: Int,
    override val timeStart: Long,
    override val timeEnd: Long,
  ) : TimeSteps

  data class Constraints(
    val robotName: String,
    val vertexConstraints: Set<VertexConstraint> = emptySet(),
    val edgeConstraints: Set<EdgeConstraint> = emptySet(),
  ) {

    /**
     * 判断两组约束有交集
     */
    fun overlap(other: Constraints): Boolean {
      for (vc in vertexConstraints) {
        if (other.vertexConstraints.contains(vc)) return true
      }
      for (ec in edgeConstraints) {
        if (other.edgeConstraints.contains(ec)) return true
      }
      return false
    }

    fun contain(other: Constraints): Boolean {
      if (other.vertexConstraints.isEmpty() && other.edgeConstraints.isEmpty()) return true

      for (vc in other.vertexConstraints) {
        if (!vertexConstraints.contains(vc)) return false
      }
      for (ec in other.edgeConstraints) {
        if (!edgeConstraints.contains(ec)) return false
      }
      return true
    }
  }
}