package com.seer.trick.fleet.traffic.distributed.deadlock.link

import com.seer.trick.fleet.traffic.distributed.context.ContextManagerService
import com.seer.trick.fleet.traffic.distributed.context.domain.RobotContext
import com.seer.trick.fleet.traffic.distributed.deadlock.helper.*
import com.seer.trick.fleet.traffic.distributed.deadlock.model.DeadLockMessage
import com.seer.trick.fleet.traffic.distributed.deadlock.model.NeighborDomain
import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper
import com.seer.trick.fleet.traffic.distributed.helper.PathInfoHelper
import com.seer.trick.fleet.traffic.distributed.lock.model.SpaceLock
import com.seer.trick.fleet.traffic.distributed.map.*
import com.seer.trick.fleet.traffic.distributed.plan.model.PathAction
import com.seer.trick.fleet.traffic.distributed.plan.model.PathType
import com.seer.trick.fleet.traffic.distributed.service.DistributedTrafficService
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.*

/**
 * 选择避让点解死锁算子
 * */
object AvoidDeadLockHandler {

  private val logger = LoggerFactory.getLogger(javaClass)

  fun process(message: DeadLockMessage): Map<String, Boolean> {
    var calRobot = message.calRobot ?: return mapOf()
    val linkRobot = message.linkRobots.filter { it != calRobot }.toMutableList()
    val blockMap = message.blockMap
    val list = blockMap[calRobot]
    val result = mutableListOf<String>()
    var success = mutableMapOf<String, Boolean>()
    if (!list.isNullOrEmpty()) {
      while (list.isNotEmpty()) {
        calRobot = list.removeLast()
        val context = ContextManagerService.queryRobotContext(calRobot)
        if (!context.lock.tryLock(100, TimeUnit.MILLISECONDS)) {
          logger.error("$calRobot try lock robot context failed")
          continue
        }
        try {
          success += calRobot to processOneRobot(context, message, linkRobot, result)
        } finally {
          context.lock.unlock()
        }
      }
    } else {
      val context = ContextManagerService.queryRobotContext(calRobot)
      if (!context.lock.tryLock(100, TimeUnit.MILLISECONDS)) {
        logger.error("$calRobot try lock robot context failed")
        return mapOf(calRobot to false)
      }
      try {
        success += calRobot to processOneRobot(context, message, linkRobot, result)
      } finally {
        context.lock.unlock()
      }
    }
    return success
  }

  private fun processOneRobot(
    context: RobotContext,
    message: DeadLockMessage,
    linkRobot: MutableList<String>,
    result: MutableList<String>,
  ): Boolean {
    logger.debug("process dead lock robot is ${context.robotName}")
    if (context.plan.path.last().index == context.plan.allocateIndex) {
      logger.debug("process dead lock robot allocate index is ${context.plan.allocateIndex}")
      return false
    }
    // 仅计算一台车 one time
    val neighborDomain = NeighborSearchService.process(context, message.neighborDomains)
    if (neighborDomain.canPass.isEmpty() && neighborDomain.backPass == null) {
      logger.error("${context.robotName} find can pass neighbor failed, no feasible solution was found")
      return false
    }
    val avoid = findAvoidMessage(context, neighborDomain, linkRobot, result)
    if (avoid == null) {
      logger.error("${context.robotName} find avoid point failed, no feasible solution was found")
      return false
    }
    result.add(avoid.second.pointName)
    logger.info("${context.robotName} find avoid point ${avoid.second}")
    val middlePath = if (avoid.first.target.pointName == avoid.second.pointName) {
      Collections.singletonList(avoid.first)
    } else {
      DeadLockPathPlanHelper
        .toMiddlePath(context, avoid.second, avoid.first, neighborDomain.cantRotate, neighborDomain.cantPass)
    }
    if (middlePath != null) {
      val targetPath = DeadLockPathPlanHelper.toTargetPath(context, middlePath)
      if (targetPath != null) {
        // 更新路径
        var robotHeading = context.baseDomain.robotHeading
        var containerHeading = context.baseDomain.containerHeading
        var lineName = avoid.first.lineName
        val currentAction = context.plan.queryCurrentAction()
        // todo 对于旋转临时处理
        if (currentAction != null && !currentAction.isRotate()) {
          robotHeading = currentAction.robotOutHeading
          containerHeading = currentAction.containerOutHeading
          lineName = currentAction.lineName
        }
        val first = targetPath.first()
        if (AngleHelper.sameAngleInFiveDegree(robotHeading, first.robotInHeading) &&
          AngleHelper.sameAngleInFiveDegree(containerHeading, first.containerInHeading)
        ) {
          val path = PathInfoHelper.joinPath(context.plan.path, targetPath, context.plan.allocateIndex)
          ContextManagerService.updatePath(context.robotName, path)
        } else {
          val canRotate = neighborDomain.canRotate
          if (canRotate.isEmpty()) throw RuntimeException("${context.robotName} path fail")
          val r = if (context.baseDomain.isLoading() &&
            !AngleHelper.sameAngleInFiveDegree(containerHeading, first.containerInHeading)
          ) {
            canRotate.find { it.type == PathType.TURNING } ?: throw RuntimeException("${context.robotName} path fail")
          } else {
            canRotate.find { it.type == PathType.ROTATE } ?: throw RuntimeException("${context.robotName} path fail")
          }
          val rotate = r.copy(
            lineName = lineName,
            robotInHeading = robotHeading,
            robotOutHeading = first.robotInHeading,
            containerInHeading = containerHeading,
            containerOutHeading = first.containerInHeading,
          )
          targetPath.add(0, rotate)
          // 更新路径
          val path = PathInfoHelper.joinPath(context.plan.path, targetPath, context.plan.allocateIndex)
          ContextManagerService.updatePath(context.robotName, path)
        }
        return true
      }
    }
    return false
  }

  /**
   *  寻找可避让的点
   *  优先 canPass， 里面 和 当前路线方向一致的
   *  其次 backPass
   * */
  private fun findAvoidMessage(
    context: RobotContext,
    neighborDomain: NeighborDomain,
    linkRobot: MutableList<String>,
    result: MutableList<String>,
  ): Pair<PathAction, Point>? {
    val canPass = neighborDomain.canPass
    val backPass = neighborDomain.backPass
    val cantRotate = neighborDomain.cantRotate
    val config = DistributedTrafficService.findSceneBySceneId(context.sceneId).config
    val unreachablePoints = context.deadLock.unreachablePoints
    if (canPass.isNotEmpty()) {
      val nextAction = context.plan.queryNextAction()
      val otherNearPathPoint: List<String> = linkRobot.filter { it != context.robotName }
        .flatMap {
          val restPath = ContextManagerService.queryRobotContext(it).plan.restPath()
          val pathPoint: MutableList<String> = mutableListOf()
          restPath.forEachIndexed { index, pathAction ->
            if (index <= 10) pathPoint.add(pathAction.target.pointName)
          }
          pathPoint
        }
      // 和当前路线一直 || 不在其他路线上
      canPass.sortWith(
        compareBy({
          nextAction == null || nextAction.target.pointName != it.target.pointName
        }, {
          !it.target.avoid
        }, {
          otherNearPathPoint.contains(it.target.pointName)
        }, {
          it.type == PathType.ARC || it.type == PathType.CURVE
        }),
      )

      for (action in canPass) {
        if (unreachablePoints.contains(action.target.pointName)) continue
        val avoidPoint = findAvoidPoint(context, action, cantRotate, linkRobot, result)
        if (avoidPoint != null) {
          return Pair(action, avoidPoint)
        }
        unreachablePoints.add(action.target.pointName)
      }
    }
    if (config.reverseDeadlock && backPass != null && !unreachablePoints.contains(backPass.target.pointName)) {
      logger.debug("${context.robotName} find back pass $backPass")
      val avoidPoint = findAvoidPoint(context, backPass, cantRotate, linkRobot, result)
      if (avoidPoint != null) {
        return Pair(backPass, avoidPoint)
      }
      unreachablePoints.add(backPass.target.pointName)
    }

    if (context.deadLock.failCount > 0 && context.deadLock.failCount % 10 == 0) {
      logger.error("too many fail count. clean up and start again")
      context.deadLock.unreachablePoints.clear()
    }
    return null
  }

  private fun findAvoidPoint(
    context: RobotContext,
    action: PathAction,
    cantRotate: MutableList<String>,
    linkRobot: MutableList<String>,
    result: MutableList<String>,
  ): Point? {
    logger.debug("${context.robotName} first action is ${action.target.pointName}")
    // 寻找可以避让的点
    val robotName = context.robotName
    val start = action.start.pointName
    val end = action.target.pointName
    val closePoint: MutableList<String> = mutableListOf(start)
    val robotHeading = action.robotOutHeading
    val openQueue: PriorityQueue<AvoidPointDistance> = PriorityQueue { p1, p2 ->
      p1.cost.compareTo(p2.cost)
    }

    val pair = context.deadLock.linkSuccessPoint
    val extraDis = if (pair != null && pair.first == end) {
      logger.info("${context.robotName} find link success point $pair")
      2.0 * pair.second
    } else {
      0.0
    }

    val endPoint = MapService.findPointByName(context.sceneId, context.mapName, context.groupName, end)

    val link: MutableList<RobotContext> = mutableListOf()
    for (robot in linkRobot) {
      if (robot == robotName) continue
      link.add(ContextManagerService.queryRobotContext(robot))
    }
    val restPath = context.plan.restPath()
    if (restPath.isEmpty()) {
      logger.warn("${context.robotName} rest path is empty")
      return null
    }
    val pathPoints = restPath.map { it.target.pointName }.toList()
    val targetPoint = restPath.last().target

    val spaceLocks = buildLinkRobotSpaceLock(link)
    var firstCantRotate = false
    if (cantRotate.contains(end)) {
      firstCantRotate = true
    }
    val paths = buildLinkRobotPath(link)
    closePoint.addAll(otherRobotPointName(link))
    val trafficConfig = DistributedTrafficService.findSceneBySceneId(context.sceneId).config
    // 检测当前避让位置是否可避让
    if (endPoint.distance(action.start) > trafficConfig.linkMinDistance + extraDis &&
      endPoint.distance(action.start) <= trafficConfig.linkMaxDistance &&
      endPoint.avoid &&
      !onOtherRobotPath(endPoint, paths) &&
      !checkCollision(context, endPoint, spaceLocks)
    ) {
      context.deadLock.linkSuccessPoint = Pair(end, if (end == pair?.first) pair.second + 1 else 1)
      return endPoint
    }
    openQueue.offer(AvoidPointDistance(endPoint, action.target.distance(action.start), endPoint.distance(targetPoint)))
    while (openQueue.isNotEmpty()) {
      val avoidPointDistance = openQueue.poll()
      val point = avoidPointDistance.point
      val oldDistance = avoidPointDistance.distance

      closePoint.add(point.pointName)
      var lines = point.toLines
      if (firstCantRotate) {
        lines = lines.filter { line ->
          line.robotCanPass(context.baseDomain.isLoading(), robotHeading, context.robotMotionType) ||
            line.robotCanPass(
              context.baseDomain.isLoading(),
              AngleHelper.processAngle(robotHeading + AngleHelper.DOWN_ANGLE),
              context.robotMotionType,
            )
        }.toMutableList()
        firstCantRotate = false
      }
      for (line in lines) {
        val distance = oldDistance + line.end.distance(line.start) // todo 临时计算距离
        if (distance > trafficConfig.linkMaxDistance) {
          logger.warn("${context.robotName} distance is too long, current point is ${line.start.pointName}")
          continue
        }
        // 检测这个点是否为避让点
        if (closePoint.contains(line.end.pointName)) {
          continue
        }
        if (!DeadLockHelper.canPass(context, line)) {
          continue
        }
        // todo 一些特殊的地方不可去
        if (distance < trafficConfig.linkMinDistance + extraDis ||
          !line.end.avoid ||
          onOtherRobotPath(line.end, paths) ||
          checkCollision(context, line.end, spaceLocks) ||
          result.contains(line.end.pointName)
        ) {
          // 优先原有路线
          if (pathPoints.contains(line.end.pointName)) {
            logger.debug("${context.robotName} can avoid point: ${line.end.pointName}")
            openQueue.offer(AvoidPointDistance(line.end, distance, (distance + line.end.distance(targetPoint)) * 0.6))
          } else {
            openQueue.offer(AvoidPointDistance(line.end, distance, distance + line.end.distance(targetPoint)))
          }
          continue
        }
        context.deadLock.linkSuccessPoint = Pair(end, if (end == pair?.first) pair.second + 1 else 1)
        return line.end
      }
    }
    logger.debug("$robotName can not find avoid point")
    return null
  }

  /**
   *  其他机器人的位置
   * */
  private fun otherRobotPointName(link: MutableList<RobotContext>): List<String> {
    val points = mutableListOf<String>()
    link.forEach {
      val point = it.baseDomain.curPoint
      if (point != null && point.posType == PosType.POINT) {
        points.add(point.pointName)
      }
    }
    return points
  }

  private fun onOtherRobotPath(point: Point, link: MutableMap<String, MutableList<String>>): Boolean {
    for (path in link) {
      if (path.value.contains(point.pointName)) {
        return true
      }
    }
    return false
  }

  private fun buildLinkRobotPath(link: MutableList<RobotContext>): MutableMap<String, MutableList<String>> {
    val linkPath: MutableMap<String, MutableList<String>> = mutableMapOf()
    for (robotContext in link) {
      val path = robotContext.plan.restPath().mapNotNull { action ->
        action.target.pointName
      }.distinct().toMutableList()
      linkPath[robotContext.robotName] = path
    }
    return linkPath
  }

  private fun buildLinkRobotSpaceLock(link: MutableList<RobotContext>): MutableMap<String, MutableList<SpaceLock>> {
    val linkSpace: MutableMap<String, MutableList<SpaceLock>> = mutableMapOf()
    for (robotContext in link) {
      val spaceLock = robotContext.plan.restPath().mapNotNull { action -> action.runLock }.toMutableList()
      linkSpace[robotContext.robotName] = spaceLock
    }
    return linkSpace
  }

  /**
   * 检测碰撞
   * */
  private fun checkCollision(
    context: RobotContext,
    point: Point,
    spaceLockMap: MutableMap<String, MutableList<SpaceLock>>,
  ): Boolean {
    val spaceLocks = spaceLockMap.values.flatten().toMutableList()
    return PathCollisionHelper.staticPathCollision(context, point, spaceLocks)
  }

  data class AvoidPointDistance(val point: Point, val distance: Double, val cost: Double)
}