package com.seer.trick.fleet.domain

import com.fasterxml.jackson.annotation.JsonProperty
import com.seer.trick.fleet.seer.SmapPos


/**
 * 机器人载入的地图以及储存的地图
 */
data class RbkAllMaps(
  @JsonProperty("current_map")
  val currentMap: String = "",
  @JsonProperty("current_map_md5")
  val currentMapMd5: String = "",
  @JsonProperty("map_files_info")
  val mapFilesInfo: List<RbkMapInfo> = emptyList(),
  val maps: List<String> = emptyList(),
  @JsonProperty("ret_code")
  val retCode: Int = 0,
)

/**
 * 机器人的地图
 */
data class RbkMapInfo(
  val modified: String = "",
  val name: String = "",
  val size: Int = 0,
)

/**
 * 机器人地图的 md5
 */
data class RbkMapMd5(
  @JsonProperty("map_info")
  val mapInfo: List<RbkMapMd5Detail> = emptyList(),
  @JsonProperty("ret_code")
  val retCode: Int = 0,
  @JsonProperty("err_msg")
  val errMsg: String = ""
)

/**
 * 机器人地图的 md5
 */
data class RbkMapMd5Detail(
  val md5: String = "",
  val name: String = "",
)

/**
 * 机器人模型文件
 */
data class RbkModel(
  val model: String = "",
  val deviceTypes: List<Any> = emptyList()
)

/**
 * 货物
 */
data class GoodsRegion(
  val name: String = "",
  @JsonProperty("point")
  val points: List<SmapPos>? = null,
)
