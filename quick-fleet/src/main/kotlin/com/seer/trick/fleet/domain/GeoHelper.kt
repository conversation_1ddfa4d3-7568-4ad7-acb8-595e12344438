package com.seer.trick.fleet.domain

import com.seer.trick.BzError
import com.seer.trick.fleet.domain.normalizeRadian
import kotlin.math.PI
import kotlin.math.abs
import kotlin.math.atan2
import kotlin.math.cos
import kotlin.math.floor
import kotlin.math.max
import kotlin.math.min
import kotlin.math.pow
import kotlin.math.round
import kotlin.math.sin
import kotlin.math.sqrt

object GeoHelper {

  /**
   * 给定一组点，返回 X, Y 最大最小值。如果 arr 为空报错。
   */
  fun <T> calcBound2D(arr: Collection<T>, getX: (e: T) -> Double, getY: (e: T) -> Double): Bound2D {
    if (arr.isEmpty()) throw BzError("errCodeErr", "Points is empty")

    var minX: Double = Double.MAX_VALUE
    var maxX: Double = -Double.MAX_VALUE
    var minY: Double = Double.MAX_VALUE
    var maxY: Double = -Double.MAX_VALUE

    for (e in arr) {
      val x = getX(e)
      val y = getY(e)
      if (x < minX) minX = x
      if (x > maxX) maxX = x
      if (y < minY) minY = y
      if (y > maxY) maxY = y
    }

    return Bound2D(
      minX.roundLengthPrecision(),
      maxX.roundLengthPrecision(),
      minY.roundLengthPrecision(),
      maxY.roundLengthPrecision(),
    )
  }

  /**
   * 计算两个点的欧氏距离
   */
  fun euclideanDistance(x1: Double, y1: Double, x2: Double, y2: Double): Double =
    sqrt((x1 - x2).pow(2.0) + (y1 - y2).pow(2.0)).roundLengthPrecision()

  /**
   * 计算两个点的欧氏距离
   */
  fun euclideanDistance(p1: Point2D, p2: Point2D): Double =
    sqrt((p1.x - p2.x).pow(2.0) + (p1.y - p2.y).pow(2.0)).roundLengthPrecision()

  /**
   * 求直线中点
   */
  fun middlePoint(p1: Point2D, p2: Point2D): Point2D = Point2D(
    ((p1.x + p2.x) / 2).roundLengthPrecision(),
    ((p1.y + p2.y) / 2).roundLengthPrecision(),
  )

  /**
   * 求直线中点
   */
  fun middlePoint(p1x: Double, p1y: Double, p2x: Double, p2y: Double): Point2D = Point2D(
    ((p1x + p2x) / 2).roundLengthPrecision(),
    ((p1y + p2y) / 2).roundLengthPrecision(),
  )

  /**
   * 判断两个带旋转的矩形是否相交
   */
  @Deprecated("旋转这个含义不明确")
  fun isRotatedRectsIntersecting(r1: Rect, r2: Rect): Boolean {
    val aabb1 = getRectAABB(r1)
    val aabb2 = getRectAABB(r2)
    return !(
      aabb1.cx - aabb1.width / 2 > aabb2.cx + aabb2.width / 2 ||
        aabb1.cx + aabb1.width / 2 < aabb2.cx - aabb2.width / 2 ||
        aabb1.cy - aabb1.height / 2 > aabb2.cy + aabb2.height / 2 ||
        aabb1.cy + aabb1.height / 2 < aabb2.cy - aabb2.height / 2
      )
  }

  /**
   * 计算矩形的四个顶点
   */
  fun getRectVertices(r: Rect): List<Point2D> {
    val cos = cos(r.theta)
    val sin = sin(r.theta)
    val halfWidth = r.width / 2
    val halfHeight = r.height / 2

    return listOf(
      Point2D(
        (r.cx + halfWidth * cos - halfHeight * sin).roundLengthPrecision(),
        (r.cy + halfWidth * sin + halfHeight * cos).roundLengthPrecision(),
      ),
      Point2D(
        (r.cx - halfWidth * cos - halfHeight * sin).roundLengthPrecision(),
        (r.cy + halfWidth * sin + halfHeight * cos).roundLengthPrecision(),
      ),
      Point2D(
        (r.cx - halfWidth * cos + halfHeight * sin).roundLengthPrecision(),
        (r.cy - halfWidth * sin - halfHeight * cos).roundLengthPrecision(),
      ),
      Point2D(
        (r.cx + halfWidth * cos + halfHeight * sin).roundLengthPrecision(),
        (r.cy + halfWidth * sin - halfHeight * cos).roundLengthPrecision(),
      ),
    )
  }

  /**
   * 计算矩形的AABB
   */
  private fun getRectAABB(r: Rect): Rect {
    val vertices = getRectVertices(r)
    var minX = vertices[0].x
    var maxX = vertices[0].x
    var minY = vertices[0].y
    var maxY = vertices[0].y

    for (i in 1..3) {
      if (vertices[i].x < minX) minX = vertices[i].x
      if (vertices[i].x > maxX) maxX = vertices[i].x
      if (vertices[i].y < minY) minY = vertices[i].y
      if (vertices[i].y > maxY) maxY = vertices[i].y
    }

    return Rect(
      cx = (minX + maxX) / 2,
      cy = (minY + maxY) / 2,
      width = maxX - minX,
      height = maxY - minY,
      theta = 0.0,
    )
  }

  /**
   * 四个点求矩形，矩形可以已被旋转
   */
  fun pointsToRect(points: List<Point2D>): Rect = pointsToRect(points[0], points[1], points[2], points[3])

  fun pointsToOBB(points: List<Point2D>): OBB {
    val unitVectorsWidth = (points[1] - points[0]).unit()
    val unitVectorsHeight = (points[2] - points[1]).unit()

    val halfWidth = (points[1] - points[0]).mod() / 2
    val halfHeight = (points[2] - points[1]).mod() / 2
    val center = Point2D(
      points.map { it.x }.average(),
      points.map { it.y }.average(),
    )
    return OBB(
      center = center,
      unitVectorsWidth = unitVectorsWidth,
      unitVectorsHeight = unitVectorsHeight,
      halfWidth = halfWidth,
      halfHeight = halfHeight,
    )
  }

  fun obbToRect(obb: OBB): Rect {
    // 计算宽度和高度
    val width = obb.halfWidth * 2
    val height = obb.halfHeight * 2

    // 计算旋转弧度（假设第一个单位向量是宽度方向）
    val theta = atan2(obb.unitVectorsWidth.y, obb.unitVectorsWidth.x)
    // 计算左下角坐标
    val bottomLeftVector = obb.unitVectorsWidth * (-obb.halfWidth) + obb.unitVectorsHeight * (-obb.halfHeight)
    val x = obb.center.x + bottomLeftVector.x
    val y = obb.center.y + bottomLeftVector.y
    return Rect(x, y, width, height, theta)
  }

  /**
   * 四个点求矩形，矩形可以已被旋转
   */
  fun pointsToRect(p1: Point2D, p2: Point2D, p3: Point2D, p4: Point2D): Rect {
    // 计算中心坐标
    val centerX: Double = (p1.x + p2.x + p3.x + p4.x) / 4
    val centerY: Double = (p1.y + p2.y + p3.y + p4.y) / 4

    // 计算边长
    val side1Length: Double = euclideanDistance(p1, p2)
    val side2Length: Double = euclideanDistance(p2, p3)

    // 计算旋转弧度
    val angle = atan2(p2.y - p1.y, p2.x - p1.x)

    return Rect(
      cx = centerX,
      cy = centerY,
      width = side1Length,
      height = side2Length,
      theta = angle,
    )
  }

  /**
   * 两个多边形是否相交
   */
  fun isPolygonsIntersecting(r1: Polygon, r2: Polygon): Boolean {
    // 定义多边形：首先，你需要定义两个多边形。多边形可以用一组顶点来表示，每个顶点由其x和y坐标组成。
    // 计算多边形的边界框：边界框是一个矩形，它包围了整个多边形。对于每个多边形，你可以计算其边界框的左上角和右下角坐标。
    // 检查边界框是否相交：如果两个多边形的边界框不相交，那么这两个多边形也不相交。你可以通过比较边界框的坐标来判断它们是否相交。
    // 检查多边形顶点是否在另一个多边形内部：如果两个多边形的边界框相交，那么你需要进一步检查多边形顶点是否在另一个多边形内部。这可以通过射线法或点在多边形内的算法来实现。
    // 检查多边形边是否相交：如果两个多边形的边界框相交，并且没有多边形顶点在另一个多边形内部，那么你需要检查多边形边是否相交。这可以通过计算几何中的线段相交算法来实现。

    if (r1.points.isEmpty() || r2.points.isEmpty()) return false

    // Check if any vertex of polygon1 is inside polygon2
    for (p in r1.points) {
      if (isPointInPolygon(r2, p.x, p.y)) return true
    }

    // Check if any vertex of polygon2 is inside polygon1
    for (p in r2.points) {
      if (isPointInPolygon(r1, p.x, p.y)) return true
    }

    // Check if any edge of polygon1 intersects with any edge of polygon2
    for (i in 0 until r1.points.size) {
      for (j in 0 until r2.points.size) {
        if (isLinesIntersect(
            r1.points[i],
            r1.points[(i + 1) % r1.points.size],
            r2.points[j],
            r2.points[(j + 1) % r2.points.size],
          )
        ) {
          return true
        }
      }
    }

    return false
  }

  /**
   * 新的实现，先暂时 venus 使用
   */
  fun isPolygonsIntersectingByVenus(r1: Polygon, r2: Polygon): Boolean {
    // 整体 AABB 剪枝
    // 先用多边形预计算好的轴对齐包围盒（bbox）做快速排除。
    // 若 bbox 不相交，则两个多边形肯定不相交

    if (!r1.bbox.intersects(r2.bbox)) return false

    // 顶点包含
    // 若整体 bbox 相交，再检查顶点是否落在对方多边形内部。
    if (r1.points.any { isPointInPolygon(r2, it.x, it.y) }) return true
    if (r2.points.any { isPointInPolygon(r1, it.x, it.y) }) return true

    // 边-边相交检测
    // 逐条边遍历前再次使用 bbox 进行层级剪枝：
    //  • e1.bbox 与 r2.bbox 不交 → e1 不可能与 r2 任意边相交，整条边跳过。
    //  • e1.bbox 与 e2.bbox 不交 → 这两条边不可能相交，边对跳过。
    for (e1 in r1.edges) {
      if (!e1.bbox.intersects(r2.bbox)) continue // 整条边快速排除
      for (e2 in r2.edges) {
        if (!e1.bbox.intersects(e2.bbox)) continue // 边对快速排除
        if (isLinesIntersect(e1.p1, e1.p2, e2.p1, e2.p2)) return true
      }
    }
    return false
  }

  /**
   * 一个多边形与一个线段是否相交，传入的是线段的两个端点
   */
  fun isPolygonsIntersectWithLine(r1: Polygon, l1p1: Point2D, l1p2: Point2D): Boolean {
    val points = r1.points
    if (points.isEmpty()) return false

    // 判断点是否在多边形内部
    if (isPointInPolygon(r1, l1p1.x, l1p1.y) || isPointInPolygon(r1, l1p2.x, l1p2.y)) return true
    for (i in 0 until r1.points.size) {
      if (isLinesIntersect(
          r1.points[i],
          r1.points[(i + 1) % r1.points.size],
          l1p1,
          l1p2,
        )
      ) {
        return true
      }
    }
    return false
  }

  /**
   * 平移
   */
  fun translate(p: Polygon, dx: Double, dy: Double): Polygon = Polygon(
    p.points.map { pt ->
      Point2D(
        (pt.x + dx).roundLengthPrecision(),
        (pt.y + dy).roundLengthPrecision(),
      )
    },
    p.type,
    // 平移处理，减少重新构建 aabb，因为平移对于 aabb 的变化是线性的，通过 dx，dy 可以直接得到
    translateAabb(p.bbox, dx, dy),
  )

  /**
   * 旋转
   */
  fun rotate(p: Polygon, cx: Double, cy: Double, r: Double): Polygon {
    val cosR = cos(r)
    val sinR = sin(r)
    val rotatedPoints = ArrayList<Point2D>(p.points.size)
    for (pt in p.points) {
      val dx = pt.x - cx
      val dy = pt.y - cy
      val nx = dx * cosR - dy * sinR + cx
      val ny = dx * sinR + dy * cosR + cy
      rotatedPoints.add(Point2D(nx.roundLengthPrecision(), ny.roundLengthPrecision()))
    }
    return Polygon(rotatedPoints, p.type)
  }

  /**
   * 一个点绕另一个点旋转
   */
  fun rotatePoint(p: Point2D, cx: Double, cy: Double, angle: Double): Point2D = Point2D(
    ((p.x - cx) * cos(angle) - (p.y - cy) * sin(angle) + cx).roundLengthPrecision(),
    ((p.x - cx) * sin(angle) + (p.y - cy) * cos(angle) + cy).roundLengthPrecision(),
  )

  /**
   * 平移后旋转
   */
  fun translateThenRotate(p: Polygon, dx: Double, dy: Double, cx: Double, cy: Double, angle: Double): Polygon =
    rotate(translate(p, dx, dy), cx, cy, angle)

  /**
   * 点是否在多边形内。测试通过。
   */
  fun isPointInPolygon(polygon: Polygon, x: Double, y: Double): Boolean {
    if (polygon.points.isEmpty()) return false
    var inside = false
    for (i in 0 until polygon.points.size) {
      val j = if (i == polygon.points.size - 1) 0 else i + 1
      val p1 = polygon.points[i]
      val p2 = polygon.points[j]
      // 除 0 产生 Double.Infinity，< 是可以工作的
      val intersect = (p1.y > y) != (p2.y > y) && x < (p2.x - p1.x) * (y - p1.y) / (p2.y - p1.y) + p1.x
      if (intersect) inside = !inside
    }
    return inside
  }

  /**
   * 两个线段是否相交
   */
  fun isLinesIntersect(l1p1: Point2D, l1p2: Point2D, l2p1: Point2D, l2p2: Point2D): Boolean {
    val (x1, y1) = l1p1
    val (x2, y2) = l1p2
    val (x3, y3) = l2p1
    val (x4, y4) = l2p2

    // val denominator = (y4 - y3) * (x2 - x1) - (x4 - x3) * (y2 - y1)
    // if (denominator == 0.0) {
    //   return false // Lines are parallel
    // }
    //
    // val ua = ((x4 - x3) * (y1 - y3) - (y4 - y3) * (x1 - x3)) / denominator
    // val ub = ((x2 - x1) * (y1 - y3) - (y2 - y1) * (x1 - x3)) / denominator
    //
    // return ua >= 0.0 && ua <= 1.0 && ub >= 0.0 && ub <= 1.0

    val d = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)
    if (d == 0.0) return false

    val xi = ((x3 - x4) * (x1 * y2 - y1 * x2) - (x1 - x2) * (x3 * y4 - y3 * x4)) / d
    val yi = ((y3 - y4) * (x1 * y2 - y1 * x2) - (y1 - y2) * (x3 * y4 - y3 * x4)) / d

    // 容差处理（避免浮点精度问题）
    val eps = 1e-9
    fun inside(v: Double, a: Double, b: Double): Boolean = v + eps >= min(a, b) && v - eps <= max(a, b)
    
    return !(!inside(xi, x1, x2) || !inside(xi, x3, x4) || !inside(yi, y1, y2) || !inside(yi, y3, y4))
  }

  /**
   * 将弧度归一到 [0, 2π]
   */
  fun normalizeRadian(radians: Double): Double {
    val twoPi = 2 * Math.PI
    var a = radians % twoPi
    if (a < 0) a += twoPi
    return a.roundRadianPrecision()
  }

  /**
   * 将弧度归一到 [0, 2π]，保留小数版本
   */
  fun normalizeRadianNoRound(radians: Double): Double {
    val twoPi = 2 * Math.PI
    var a = radians % twoPi
    if (a < 0) a += twoPi
    return a
  }

  /**
   * 将角度规范化到 [-π, π]
   */
  fun symmetricRadian(radians: Double): Double {
    var r = this.normalizeRadian(radians)
    if (r > PI) r -= 2 * PI
    return r
  }

  /**
   * 两个弧度的差异是否为锐角
   */
  fun isAcuteRadian(a: Double, b: Double): Boolean {
    val d = abs(normalizeRadian(a) - normalizeRadian(b)) // (-2pi ~ +2pi) -> [0, 2pi)
    // [0, 90°) || (270°, 360°)
    return (0 <= d && d < PI * 0.5) || (PI * 1.5 < d && d < PI * 2.0)
  }

  /**
   * 从弧度 1 旋转到弧度 2 的方向和弧度差。方向：1 逆时针，-1 顺时针。弧度差取正值。
   * minor == true 表示小角度旋转（劣弧）；minor == false 表示大角度旋转（优弧）。
   */
  fun getRotateDirection(a1: Double, a2: Double, minor: Boolean): Pair<Int, Double> {
    val a1n = normalizeRadian(a1)
    val a2n = normalizeRadian(a2)
    var delta = abs(a2n - a1n)
    var dir = if (a2n >= a1n) {
      if (delta <= PI) 1 else -1
    } else {
      if (delta <= PI) -1 else 1
    }

    delta = if (delta <= PI) delta else 2 * PI - delta

    if (!minor) {
      dir = -dir
      delta = 2 * PI - delta
    }
    return Pair(dir, delta.roundRadianPrecision())
  }

  /**
   * 从起始弧度 a1 到结束弧度 a2，以最小角度旋转，每 step 弧度产生一个弧度，返回一个数组，首尾是起始和结束弧度。
   * 如果 a1==a2，则返回一个元素的数组。
   * 如果 a1 和 a2 夹角小于 step，返回 [a1, a2]
   * minor == true 表示小角度旋转（劣弧）；minor == false 表示大角度旋转（优弧）。
   */
//  fun generateRotateSteps(a1: Double, a2: Double, step: Double, minor: Boolean): List<Double> {
//    // 起点旋转所需空间，从起点停止弧度，到进入线路弧度，每 N 度一个多边形
//    val fromTheta = normalizeRadian(a1)
//    val toTheta = normalizeRadian(a2)
//
//    if (fromTheta.radianEquals(toTheta))
//      return listOf(fromTheta)
//
//    val (rotateDir, rotateDelta) = getRotateDirection(fromTheta, toTheta, minor)
//
//    val dt = abs(step) // 防止 step 传负数
//    val rotateNum = floor(rotateDelta / dt).toInt() // 旋转几次
//
//    if (rotateNum == 0) return listOf(fromTheta, toTheta)
//
//    val angles: MutableList<Double> = ArrayList(rotateNum + 1)
//
//    for (i in 0 until rotateNum) {
//      val t = (fromTheta + dt * i * rotateDir).normalizeRadian().roundRadianPrecision()
//      angles += t
//    }
//
//    if (!angles.last().radianEquals(toTheta)) angles += toTheta
//
//    return angles
//  }

  /**
   * 从起始弧度 a1 到结束弧度 a2，以最小角度旋转，每 step 弧度产生一个弧度，返回一个数组，首尾是起始和结束弧度。
   * 如果 a1==a2，则返回一个元素的数组。
   * 如果 a1 和 a2 夹角小于 step，返回 [a1, a2]
   * minor == true 表示小角度旋转（劣弧）；minor == false 表示大角度旋转（优弧）。
   * 高性能版本：返回 DoubleArray，避免 List<T> 与单元素 listOf(...) 分配。
   */
  fun generateRotateSteps(a1: Double, a2: Double, step: Double, minor: Boolean): DoubleArray {
    val fromTheta = normalizeRadian(a1)
    val toTheta = normalizeRadian(a2)

    if (fromTheta.radianEquals(toTheta)) return doubleArrayOf(fromTheta)

    val (rotateDir, rotateDelta) = getRotateDirection(fromTheta, toTheta, minor)

    val dt = abs(step)
    val rotateNum = floor(rotateDelta / dt).toInt()

    if (rotateNum == 0) return doubleArrayOf(fromTheta, toTheta)

    val arr = DoubleArray(rotateNum + 1)
    var idx = 0
    while (idx < rotateNum) {
      val t = (fromTheta + dt * idx * rotateDir).normalizeRadian().roundRadianPrecision()
      arr[idx] = t
      idx++
    }

    // Ensure last equals toTheta
    if (!arr.last().radianEquals(toTheta)) {
      return arr + toTheta
    }
    return arr
  }

  /**
   * 多边形的每条边向外平移指定距离（d）TODO
   */
  fun inflate(polygon: Polygon, d: Double): Polygon {
    val points: MutableList<Point2D> = mutableListOf()
    val delta = if (area(polygon) < 0) -d else d
    val ps = polygon.points
    val normals = buildNormals(ps)
    var pre = ps.size - 1
    for (index in ps.indices) {
      val cos = dotProduct(normals[index], normals[pre])
      val q = delta / (cos + 1)
      points.add(
        Point2D(
          (ps[index].x + q * (normals[index].x + normals[pre].x)).roundLengthPrecision(),
          (ps[index].y + q * (normals[index].y + normals[pre].y)).roundLengthPrecision(),
        ),
      )
      pre = index
    }
    return Polygon(points = points, type = polygon.type)
  }

  // 计算由一系列点构成的闭合路径所围成的面积
  private fun area(polygon: Polygon): Double {
    // https://en.wikipedia.org/wiki/Shoelace_formula
    var sum = 0.0
    if (polygon.points.size < 3) {
      return 0.0
    }
    var p1 = polygon.points[polygon.points.size - 1]
    for (i in 0 until polygon.points.size) {
      val p2 = polygon.points[i]
      sum += (p2.y + p1.y) * (p1.x - p2.x)
      p1 = p2
    }
    return sum * 0.5
  }

  // 点乘
  private fun dotProduct(p1: Point2D, p2: Point2D): Double = p1.x * p2.x + p1.y * p2.y

  // 构建多边形的单位法向量
  private fun buildNormals(points: List<Point2D>): List<Point2D> {
    val normals: MutableList<Point2D> = mutableListOf()
    for (i in 0 until points.size - 1) {
      normals.add(getUnitNormal(points[i], points[(i + 1)]))
    }
    normals.add(getUnitNormal(points[points.size - 1], points[0]))
    return normals
  }

  // 计算两个点构成的向量的单位法向量
  private fun getUnitNormal(p1: Point2D, p2: Point2D): Point2D {
    val vx = p2.x - p1.x
    val vy = p2.y - p1.y
    if (vx == 0.0 && vy == 0.0) {
      return Point2D(0.0, 0.0)
    }
    val f = 1.0 / sqrt(vx * vx + vy * vy)
    return Point2D(vy * f, -vx * f)
  }

  /**
   * 勾股定理计算斜边长
   */
  fun pythagorean(a: Double, b: Double): Double = sqrt(a * a + b * b)

  /**
   * 计算多边形面积
   */
  fun calculatePolygonArea(p: Polygon): Double {
    val n = p.points.size
    if (n == 0) return 0.0

    var area = 0.0
    for (i in 0 until n) {
      val j = (i + 1) % n
      area += p.points[i].x * p.points[j].y - p.points[j].x * p.points[i].y
    }
    return abs(area) / 2
  }

  /**
   * 仅平移包围盒。
   * 当后续仅做粗略碰撞剪枝而无需点坐标时，可避免复制大规模点集。
   */
  fun translateAabb(b: AABB, dx: Double, dy: Double): AABB = AABB(
    (b.minX + dx).roundLengthPrecision(),
    (b.minY + dy).roundLengthPrecision(),
    (b.maxX + dx).roundLengthPrecision(),
    (b.maxY + dy).roundLengthPrecision(),
  )

  /**
   * 从多边形快速获取平移后的包围盒
   */
  fun translateBboxOfPolygon(p: Polygon, dx: Double, dy: Double): AABB = translateAabb(p.bbox, dx, dy)
}

/**
 * 四舍五入到足够的长度精度。毫米后 2 位
 */
fun Double.roundLengthPrecision(): Double = round(this * 1e5) / 1e5

/**
 * 判断两个长度是否相等，只考虑足够精度
 */
fun Double.lengthEquals(b: Double): Boolean = abs(this - b) < 1e-5

/**
 * 四舍五入到足够的弧度精度。保留三位小数，约 0.057° 。
 */
fun Double.roundRadianPrecision(): Double = round(this * 1e3) / 1e3

/**
 * 保留两位小数。
 */
fun Double.roundTwoDigital(): Double = round(this * 1e2) / 1e2

/**
 * 判断两个弧度是否相等，只考虑足够精度
 */
fun Double.radianEquals(b: Double): Boolean = abs(this - b) < 1e-3

/**
 * 将弧度归一到 [0, 2π]
 */
fun Double.normalizeRadian(): Double = GeoHelper.normalizeRadian(this)

/**
 * 计算反方位（向）角：π + theta。此方法会负责先把 theta 归一。
 */
fun Double.reverseAzimuth(): Double = (PI + this.normalizeRadian()).normalizeRadian()

/**
 * 将角度规范化到 [-π, π]
 */
fun Double.symmetricRadian(): Double = GeoHelper.symmetricRadian(this)