package com.seer.trick.fleet.traffic.venus

import com.seer.trick.base.file.FileManager
import com.seer.trick.helper.DateHelper
import com.seer.trick.helper.JsonFileHelper
import org.apache.commons.io.FileUtils
import java.io.File
import java.nio.charset.StandardCharsets
import java.nio.file.Paths
import java.util.*
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.Executors
import kotlin.concurrent.timer

object VenusLogService {

  // 单线程写日志线程池，避免与主线程竞争 I/O，保证写入顺序。
  private val logExecutor = Executors.newSingleThreadExecutor { r ->
    Thread(r, "venus-log-writer").apply { isDaemon = true }
  }

  val highLogs: MutableList<HighLog> = CopyOnWriteArrayList()

  val lowLogs: MutableList<LowLog> = CopyOnWriteArrayList()

  // 改成 init {
  fun start() {
    // TODO 定时器机制修改
    timer(period = 3600_000) {
      clearLogs()
    }
  }

  private fun clearLogs() {
    val logRoot = Paths.get(
      FileManager.getFilesDir().absolutePath,
      "traffic",
      "nodes",
    ).toFile()

    val oneHourAgo = System.currentTimeMillis() - 3600_000

    logRoot.listFiles()?.forEach { dir ->
      if (dir.isDirectory && dir.lastModified() < oneHourAgo) {
        dir.deleteRecursively()
      }
    }
  }

  /**
   * 求解慢判定的时间阈值（毫秒）。
   * 高层求解耗时超过该阈值则会把本次请求序列化到文件，方便后续复现 / 调试。
   */
  @Volatile
  var CASE_TIME_THRESHOLD_MS: Long = 2000L

  /**
   * 求解慢判定的展开节点阈值。高层展开节点超过该值也会落盘记录。
   */
  const val CASE_EXPANDED_THRESHOLD: Long = 150L

  fun logHigh(resolver: BaseHighResolver) {
    logExecutor.submit {
      // 异步写文件
      val friendlyId = resolver.friendlyId
      val id = resolver.id
      val content = resolver.nodeLogs.joinToString("\n")

      val logDir = getLogDir(friendlyId)
      val logFile = File(logDir, "high-nodes-$id.log")
      FileUtils.write(logFile, content, StandardCharsets.UTF_8)

      val path = FileManager.fileToPath(logFile)
      highLogs += HighLog(id, path)

      synchronized(this) {
        if (highLogs.size > 100) highLogs.removeAt(0)
      }
    }
  }

  fun logLow(resolver: TargetOneLowResolver, success: Boolean, time: Long?) {
    logExecutor.submit {
      val friendlyId = resolver.highResolverFriendlyId
      val highNodeId = resolver.highNodeId
      val robotName = resolver.robotName
      val id = resolver.id
      val nodeLogs = resolver.nodeLogs.joinToString("\n")
      val logDir = getLogDir(friendlyId)
      val timestamp = System.currentTimeMillis()
      val logFile = File(
        logDir,
        "low-nodes-H$highNodeId-$timestamp-$robotName-$success-$time.log",
      )
      FileUtils.write(logFile, nodeLogs, StandardCharsets.UTF_8)

      val path = FileManager.fileToPath(logFile)
      lowLogs += LowLog(id, path)

      synchronized(this) {
        if (lowLogs.size > 100) lowLogs.removeAt(0)
      }
    }
  }
  fun recordSlowCase(
    id: String,
    requests: Map<String, RobotPlanRequest>,
    elapsedMs: Long,
    expanded: Long,
    hr: PlanResult,
  ) {
    logExecutor.submit {
      // 检测是否存在路径振荡（A->B->A）
      val hasOscillation = hr.solutions.values.any { sol ->
        detectOscillation(sol.path)
      }
//    if (elapsedMs < CASE_TIME_THRESHOLD_MS && expanded < CASE_EXPANDED_THRESHOLD && !hasOscillation) return
      val oscFlag = if (hasOscillation) "-osc" else ""
      val file = File(getCaseDir(), "case-$id-t${elapsedMs}ms-${hr.ok}-${hr.reason}$oscFlag.json")
      JsonFileHelper.writeJsonToFile(file, requests)
    }
  }

  /**
   * 简单检测路径中是否存在 A->B->A 往返走动。
   */
  private fun detectOscillation(path: List<State>): Boolean {
    if (path.size < 3) return false
    for (i in 0 until path.size - 2) {
      val p0 = path[i].toPosition.pointName
      val p2 = path[i + 2].toPosition.pointName
      if (p0 != null && p0 == p2) return true
    }
    return false
  }

  /**
   * 读取慢案例（requests），便于回放。
   * @param fileName 文件名，例如 "case-xxx.json"
   * @return 反序列化后的请求 map
   */
  fun readSlowCase(fileName: String): Map<String, RobotPlanRequest>? {
    val file = File(getCaseLoadDir(), fileName)
    if (!file.exists()) return null
    return JsonFileHelper.readJsonFromFile(file)
  }
  private fun getLogDir(subDir: String): File {
    val dir = Paths.get(
      FileManager.getFilesDir().absolutePath,
      "traffic",
      "nodes",
      DateHelper.formatDate(Date(), "yyyy-MM-dd"),
      subDir,
    )
    val dirFile = dir.toFile()
    dirFile.mkdirs()
    return dirFile
  }

  private fun getCaseDir(): File {
    val dir = Paths.get(
      FileManager.getFilesDir().absolutePath,
      "traffic",
      "cases",
    ).toFile()
    dir.mkdirs()
    return dir
  }
  private fun getCaseLoadDir(): File {
    val dir = Paths.get(
      FileManager.getFilesDir().absolutePath,
      "traffic",
      "cases备份",
    ).toFile()
    dir.mkdirs()
    return dir
  }

  data class HighLog(val id: String, val path: String, val timestamp: Date = Date())

  data class LowLog(val id: String, val path: String, val timestamp: Date = Date())

  /**
   * 停止日志线程池，测试或应用关闭时调用。
   */
  fun stop() {
    logExecutor.shutdown()
  }
}