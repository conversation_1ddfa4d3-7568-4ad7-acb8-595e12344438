package com.seer.trick.fleet.traffic.venus

import com.seer.trick.fleet.FleetLogger
import com.seer.trick.fleet.domain.GeoHelper
import com.seer.trick.fleet.domain.MoveDirection
import com.seer.trick.fleet.domain.OrderKind
import com.seer.trick.fleet.domain.OrderStatus
import com.seer.trick.fleet.domain.Polygon
import com.seer.trick.fleet.domain.Pose2D
import com.seer.trick.fleet.domain.StepStatus
import com.seer.trick.fleet.domain.TransportOrder
import com.seer.trick.fleet.domain.TransportStep
import com.seer.trick.fleet.domain.normalizeRadian
import com.seer.trick.fleet.domain.reverseAzimuth
import com.seer.trick.fleet.map.AreaMapCache
import com.seer.trick.fleet.map.SceneMapAlg
import com.seer.trick.fleet.order.OrderRuntime
import com.seer.trick.fleet.order.OrderService
import com.seer.trick.fleet.order.ParkAndChargeResourceHelper
import com.seer.trick.fleet.order.ParkingCost
import com.seer.trick.fleet.service.MapService
import com.seer.trick.fleet.service.RobotLoadCollisionService
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.SceneRuntime
import com.seer.trick.fleet.traffic.TrafficTaskStatus
import com.seer.trick.fleet.traffic.venus.cache.DijkstraCache
import com.seer.trick.helper.IdHelper
import java.util.Date
import kotlin.math.max
import kotlin.random.Random

/**
 * 高层求解器公共基类（ECBS / PBS 等）。
 * 负责
 *  1. 公共字段、日志与性能统计
 *  2. 统一 resolve() 流程：开始日志 -> 调用子类搜索 -> 记录慢案例 -> 落盘节点日志
 *  3. 提供 logMe() 工具便于子类记录调试信息
 *
 * 子类仅需关注具体高层搜索算法，实现抽象成员。
 */
abstract class BaseHighResolver(
  protected val sr: SceneRuntime,
  protected val requests: Map<String, RobotPlanRequest>,
  protected val o: PathFindingOption,
) {
  /**
   * 是否启用起点优先
   */
  protected val prioritizeStart = true

  val logModuleHighResolver = "HighRv"
  val reserveTargetPoint: MutableMap<String, String> = mutableMapOf()

  /** 唯一 ID */
  val id: String = IdHelper.oidStr()

  /** 起始时间 */
  protected val startOn: Long = System.currentTimeMillis()

  /**
   * 已展开的高层节点计数
   */
  @Volatile
  var highNodeExpanded = 0L

  // 默认起点离开时间（未规划机器人），10 个时间步。时间单位与 path.timeEnd 相同。
  val defaultLeaveTime: Long = 10

  /**
   * 已展开的低层节点计数
   */
  @Volatile
  var lowNodeExpanded = 0L

  /** 友好 ID（用于日志目录） */
  var friendlyId: String = run {
    val time = com.seer.trick.helper.DateHelper.formatDate(Date(startOn), "HH-mm-ss")
//    val robots = requests.keys.sorted().joinToString("_")
    id.takeLast(6).let { "${time}_$it" }
  }

  /** 统一的节点日志 */
  val nodeLogs: MutableList<String> = mutableListOf()

  val mainAr: AreaMapCache

  // ---------------- 可达点位缓存 ----------------
  private data class ReachKey(val robotName: String, val type: Type) {
    enum class Type { PARK, CHARGE, LM }
  }

  // 生命周期限定在本次高层规划期间；重入时实例会重新创建
  private val reachablePointCache = mutableMapOf<ReachKey, List<Pair<String, Double>>>()

  /**
   * 获取机器人从当前起点可达的目标点集合，结果按路径成本升序排列并缓存。
   */
  private fun getReachablePoints(
    req: RobotPlanRequest,
    startPoint: String,
    candidates: Collection<String>,
    algSupplier: () -> SceneMapAlg,
    type: ReachKey.Type,
  ): List<Pair<String, Double>> {
    val key = ReachKey(req.robotInfo.robotName, type)
    // 如果已有缓存直接返回
    reachablePointCache[key]?.let { return it }

    val alg = algSupplier()

    val computed = candidates
      .mapNotNull { p ->
        val sp = alg.getShortestPath(startPoint, p)
        if (sp.found) p to sp.weight else null
      }
      .sortedBy { it.second }

    reachablePointCache[key] = computed
    return computed
  }

  init {
    val tt = requests.values.find { it.trafficTask != null }!!.trafficTask!!

    mainAr = sr.mapCache.areaById[tt.target.areaId]!!.groupedMaps[tt.robotGroupId]!!
    requests.values.forEach { plan ->
      plan.trafficTask?.target?.pointName?.let { goal ->
        val t0 = System.currentTimeMillis()
        DijkstraCache.precomputeAllPoints(sr.mustGetRobot(plan.robotInfo.robotName), goal)
        println(
          "Precompute heuristic finished in " + (System.currentTimeMillis() - t0) +
            "ms | robot=${plan.robotInfo.robotName} goal=$goal",
        )
      }
    }
    if (o.enableReservePath) createReserveTargetPoint()
  }

  /** 快速时间戳日志 */
  protected fun logMe(msg: String) {
    if (sr.getDevConfigAsBool("logHigh") != true) return
    nodeLogs += VenusHelper.logTimestamp() + " " + msg
  }

  /** 统一入口。子类请勿重写 */
  fun resolve(): PlanResult {
    logMe("Start: $requests")
    val startHighOn = System.currentTimeMillis()
    val hr = doResolve()
    val highElapsed = System.currentTimeMillis() - startHighOn
    logMe("Result: $hr | elapsed=$highElapsed ms")

    FleetLogger.trace(
      module = logModuleHighResolver,
      subject = "HighSearchResult",
      sr = sr,
      robotName = "-",
      msg = mapOf(
        "id" to id,
        "success" to hr.ok,
        "elapsedMs" to highElapsed,
        "expanded" to highNodeExpanded,
        "msg" to hr.reason,
      ),
    )

    VenusLogService.recordSlowCase(id, requests, highElapsed, highNodeExpanded, hr)

    // 输出节点日志文件
    if (sr.getDevConfigAsBool("logHigh") == true) VenusLogService.logHigh(this)

    return hr
  }

  fun createReserveTargetPoint() {
    Random(System.currentTimeMillis())
    val helper = ParkAndChargeResourceHelper(sr)

    // 获取有任务的机器人并按组分组
    val groupRobots = requests
      .filter { it.value.trafficTask != null && !isParkOrCharge(it.value.trafficTask!!.target.pointName) }
      .map { it.value }
      .groupBy { it.robotInfo.groupId }

    // 为每个组分配停靠点和充电点
    groupRobots.forEach { (groupId, robots) ->
      val mapCache = sr.mapCache
      val maps = mapCache.getAllAreaMapsOfGroup(groupId) // 机器人组的所有区域的地图
      var algInstance: SceneMapAlg? = null
      val algSupplier = {
        algInstance ?: SceneMapAlg(maps, emptySet()).also { algInstance = it }
      }
      val costs = robots.flatMap { r ->
        val targetPointName = r.trafficTask?.target?.pointName!!
        val rr = sr.mustGetRobot(r.robotInfo.robotName)
        val availablePoints = helper.listAvailableParkingPoints(rr)
        val reachableParksWithCost =
          getReachablePoints(r, targetPointName, availablePoints, algSupplier, ReachKey.Type.PARK)
        reachableParksWithCost.map { (loc, c) ->
          ParkingCost(rr, targetPointName, loc, c, 0)
        }
      }

      // 按优先级和成本排序
      costs.sortedWith(compareBy({ it.priority }, { it.cost })).forEach { cost ->
        if (checkCollisionAndAssign(cost, helper)) {
          reserveTargetPoint[cost.rr.robotName] = cost.parkingPointName
        }
      }
    }

    // 将未分配机器人按组分组，统一构建 SceneMapAlg，避免重复构图
    val unassignedRequests = requests
      .filter {
        val tt = it.value.trafficTask
        tt != null && !isParkOrCharge(tt.target.pointName) && !reserveTargetPoint.contains(it.value.robotInfo.robotName)
      }
      .map { it.value }

    if (unassignedRequests.isEmpty()) return

    val allTargets = requests.values
      .mapNotNull { it.trafficTask?.target?.pointName }
      .toSet() + reserveTargetPoint.values
    // 已被占用的目标集合，随着分配实时扩充，避免重复分配
    val reservedTargets = allTargets.toMutableSet()

    val unassignedRobotsByGroup = unassignedRequests
      .groupBy { it.robotInfo.groupId }

    unassignedRobotsByGroup.forEach { (groupId, robots) ->
      val mapCache = sr.mapCache
      val maps = mapCache.getAllAreaMapsOfGroup(groupId)
      var algInstance: SceneMapAlg? = null
      val algSupplier = {
        algInstance ?: SceneMapAlg(maps, emptySet()).also { algInstance = it }
      }

      for (req in robots) {
        val startPoint = req.trafficTask?.target?.pointName ?: continue
        val candidateLms = sr.mapCache.areaById[req.robotInfo.stand.areaId]
          ?.groupedMaps
          ?.get(groupId)
          ?.pointNameMap
          ?.filter { (name, _) -> name.startsWith("LM") && !reservedTargets.contains(name) }
          ?.keys
          ?: emptySet()

        if (candidateLms.isEmpty()) continue

        // 选择距离阈值之外(>=5m)且距离最短的 LM 点，避免选择过近的点
        val reachableLms = getReachablePoints(req, startPoint, candidateLms, algSupplier, ReachKey.Type.LM)
        val distThreshold = 5.0 // meters
        val lmWithCost = reachableLms
          .filter { it.second >= distThreshold }
          .minByOrNull { it.second }

        lmWithCost?.first?.let { pointName ->
          reserveTargetPoint[req.robotInfo.robotName] = pointName
          reservedTargets += pointName
        }
      }
    }
  }

  private fun checkCollisionAndAssign(cost: ParkingCost, helper: ParkAndChargeResourceHelper): Boolean {
    // 检查碰撞
    if (helper.checkParkAndChargeCollision(cost.rr, cost.parkingPointName) != null) return false

    // 检查是否已被分配
    if (reserveTargetPoint.containsValue(cost.parkingPointName)) return false
    if (reserveTargetPoint.containsKey(cost.rr.robotName)) return false

    return true
  }

  /**
   * 检测施加约束后的搜路结果，确实遵循了该约束。如果还是违反了，BUG！
   * TODO 这里只比较了最早的冲突
   */
  private fun assertConstraint(
    earliestConstraints: Map<String, RobotConstraint>?, // 目前最早的冲突
    toRobotName: String, // 给这个机器人施加约束
    childAllConstraints: Map<String, List<RobotConstraint>>,
    rs: RobotSolution,
  ): Boolean {
    val newRobotConstraint = earliestConstraints?.get(toRobotName) ?: return true
    val robotConstraints2 = childAllConstraints[toRobotName]!!
    // TODO 这里用 == 判断对吗
    val old = robotConstraints2.find { it == newRobotConstraint }
    if (old != null) {
      FleetLogger.error(
        module = logModuleHighResolver,
        subject = "BadLowSearch",
        sr = sr,
        robotName = toRobotName,
        msg = mapOf("newRobotConstraint" to newRobotConstraint, "path" to rs),
      )
      return false
    }
    return true
  }

  /**
   * 让路。进入该方法的前提是，发现冲突发生在静止机器人或机器人最后目标点停靠上
   */
//  private fun buildChildGiveWayNodes(
//    parent: HighNode,
//    newConstraint: RobotConstraint,
//  ): List<HighNode> {
//    val masterRobotName = newConstraint.masterRobotName // 需要让路的机器人
//
//    val oldRobotCtx = parent.robots[masterRobotName]!!
//
//    val childNodes = mutableListOf<HighNode>()
//
//    val fromPointName = if (!oldRobotCtx.giveWayPointName.isNullOrBlank()) {
//      // 之前规划了让路点，继续让；先只考虑在点上
//      oldRobotCtx.giveWayPointName
//    } else if (oldRobotCtx.moved) {
//      // 机器人有任务，从任务的终点开始
//      oldRobotCtx.solution!!.path.last().toPosition.pointName!!
//    } else {
//      // 从机器人当前点
//      val stand = oldRobotCtx.request.stand
//      // 先只考虑在点上
//      stand.pointName!!
//    }
//

  /**
   * TODO 注释
   */
  protected fun generateStartPointAvoidanceConstraints(
    robotContexts: Map<String, RobotPlanContext>,
    options: PathFindingOption,
    source: String,
  ): List<RobotConstraint>? {
    if (!prioritizeStart) return null
    val constraints = mutableListOf<RobotConstraint>()
    // 预计算每个机器人起点 shape，供旋转冲突检测复用
    val standShapes: Map<String, List<Polygon>> = robotContexts.mapValues { (_, ctx) ->
      RobotLoadCollisionService.buildCollisionShape(
        options.collisionModels[ctx.robotName]!!,
        Pose2D(
          ctx.request.robotInfo.stand.x,
          ctx.request.robotInfo.stand.y,
          ctx.request.robotInfo.stand.theta,
        ),
        ctx.request.robotInfo.loadTheta,
      )
    }

    // ---- 第 1 轮：判断 each robot 是否需旋转并生成旋转圈 ----
    data class RotateInfo(val need: Boolean, val shapes: List<Polygon>)

    val rotateInfoMap: MutableMap<String, RotateInfo> = mutableMapOf()
    for ((robot, ctx) in robotContexts) {
      val need = needRotate(ctx)
      val shapes = if (need) buildRotateShapes(robot, ctx, options) else emptyList()
      rotateInfoMap[robot] = RotateInfo(need, shapes)
    }

    // ---- 第 2 轮：检测旋转圈与起点 shape 以及旋转圈之间的交叠 ----
    val bannedRotate = mutableSetOf<String>()

    // 2.1 与其他起点 shape 相交
    for ((robot, info) in rotateInfoMap) {
      if (!info.need) continue
      val collideStand = robotContexts.any { (other, _) ->
        other != robot &&
          info.shapes.any { rs ->
            standShapes[other]!!.any { ss -> GeoHelper.isPolygonsIntersectingByVenus(rs, ss) }
          }
      }
      if (collideStand) bannedRotate += robot
    }

    // 2.2 旋转圈之间相交
    val robotsList = rotateInfoMap.keys.toList()
    for (i in 0 until robotsList.size) {
      val r1 = robotsList[i]
      val info1 = rotateInfoMap[r1] ?: continue
      if (!info1.need) continue
      for (j in i + 1 until robotsList.size) {
        val r2 = robotsList[j]
        val info2 = rotateInfoMap[r2] ?: continue
        if (!info2.need) continue
        val intersects = info1.shapes.any { s1 ->
          info2.shapes.any { s2 -> GeoHelper.isPolygonsIntersectingByVenus(s1, s2) }
        }
        if (intersects) {
          bannedRotate += r1
          bannedRotate += r2
        }
      }
    }

    // 2.3 旋转圈与其他机器人的 initpath shape 相交
    for ((robot, info) in rotateInfoMap) {
      if (!info.need) continue
      val collideInitPath = robotContexts.any { (other, _) ->
        other != robot &&
          info.shapes.any { rs ->
            robotContexts[other]?.request?.initPath?.any { ss ->
              ss.spatialIntersecting(rs)
            } ?: false
          }
      }
      if (collideInitPath) bannedRotate += robot
    }

    // ---- 第 3 轮：生成约束 ----
    for ((robot, ctx) in robotContexts) {
//      if (robot == currentRobot) continue // 不约束当前机器人自己的起点
      if (ctx.request.trafficTask == null) continue // 不动的机器人
      sr.mustGetRobot(ctx.robotName)

      val rr = sr.mustGetRobot(ctx.robotName)

      val standPt = if (!ctx.request.robotInfo.stand.pointName.isNullOrEmpty()) {
        ctx.request.robotInfo.stand.pointName
      } else {
        MapService.findBestStartPointNameForPlan(rr)
      } ?: continue
      val path = ctx.solution?.path
      val leaveTime = calculateLeaveTime(path)

      // 1) 起点占位约束
      val standShapeList = standShapes[robot]!!
      constraints += RobotConstraint(
        robot,
        source = source,
        type = ResConstraintType.Spatial,
        timeStart = 0,
        timeEnd = leaveTime,
        shapes = PolygonsWithBBox(standShapeList),
        spatialDesc = "Start spatial by point $standPt",
      )

      // 2) 旋转占位约束（如需且未被禁用）
      val info = rotateInfoMap[robot]!!
      if (info.need && robot !in bannedRotate) {
        constraints += RobotConstraint(
          robot,
          source = source,
          type = ResConstraintType.Spatial,
          timeStart = 0,
          timeEnd = leaveTime,
          shapes = PolygonsWithBBox(info.shapes),
          spatialDesc = "Rotate spatial by point $standPt",
        )
      }
    }

    return constraints
  }

  private fun needRotate(ctx: RobotPlanContext): Boolean {
    val areaMapCache =
      sr.mapCache.areaById[ctx.request.robotInfo.stand.areaId]!!.groupedMaps[
        sr.mustGetRobot(
          ctx.robotName,
        ).mustGetGroup().id,
      ]!!
    // 在点上，遍历前向路径
    val rr = sr.mustGetRobot(ctx.robotName)
    val startPoint = if (!ctx.request.robotInfo.stand.pointName.isNullOrEmpty()) {
      ctx.request.robotInfo.stand.pointName
    } else {
      MapService.findBestStartPointNameForPlan(rr)
    } ?: return false

    val pr = areaMapCache.pointNameMap[startPoint]
    var shouldRotate = true
    if (pr != null) {
      val robotTheta = ctx.request.robotInfo.stand.theta.normalizeRadian()
      val paths = pr.forwardPaths
      pathLoop@ for (fp in paths) {
        val forwardTheta = fp.tracePoints.first().tangent.normalizeRadian()
        val backwardTheta = fp.tracePoints.last().tangent.reverseAzimuth()

        fun angleClose(a: Double, b: Double, tol: Double = 0.01): Boolean {
          val diff = kotlin.math.abs(a - b)
          val circ = 2 * Math.PI
          val d = if (diff > Math.PI) circ - diff else diff
          return d < tol
        }

        when (fp.moveDirection) {
          MoveDirection.Forward -> {
            if (angleClose(forwardTheta, robotTheta)) {
              shouldRotate = false
              break@pathLoop
            }
          }

          MoveDirection.Backward -> {
            if (angleClose(backwardTheta, robotTheta)) {
              shouldRotate = false
              break@pathLoop
            }
          }

          MoveDirection.Dual -> {
            if (angleClose(forwardTheta, robotTheta) || angleClose(backwardTheta, robotTheta)) {
              shouldRotate = false
              break@pathLoop
            }
          }
        }
      }
    }
    return shouldRotate
  }

  private fun buildRotateShapes(robot: String, ctx: RobotPlanContext, options: PathFindingOption): List<Polygon> {
    // todo 更精细的旋转资源控制
    // 0~360° 每 10° 一步，共 36 个角度
    val rotateSteps = (0 until 36).map { Math.toRadians(it * 10.0) }
    // buildCollisionShape 返回 List<Polygon>，此处需要扁平化为单层 List<Polygon>
    return rotateSteps.flatMap { a ->
      RobotLoadCollisionService.buildCollisionShape(
        options.collisionModels[robot]!!,
        Pose2D(
          ctx.request.robotInfo.stand.x,
          ctx.request.robotInfo.stand.y,
          a,
        ),
        a,
      )
    }
  }

  /**
   * 计算机器人离开起点的时间
   */
  protected fun calculateLeaveTime(path: List<State>?): Long {
    if (path == null) return defaultLeaveTime

    var t = 0L
    for (st in path) {
      if (st.toPosition.pointName != path.first().toPosition.pointName) {
        t = st.timeEnd
        break
      }
    }
    return max(defaultLeaveTime, t)
  } //
//    val fromPoint = mainAr.pointNameMap[fromPointName]!!
//
//    // 遍历所有前向路径
//    for (path in fromPoint.forwardPaths) {
//      val toPoint = mainAr.pointNameMap[path.toPointName]!!.point
//
//      // 避免来回走
//      if (parent.giveWayPoints.contains(toPoint.name)) continue
//
//      val finalGoal = RoutePosition(
//        type = RoutePositionType.Point,
//        pointName = toPoint.name,
//        x = toPoint.x,
//        y = toPoint.y,
//      )
//
//      val n = buildChildGiveWayNode(parent, newConstraint, finalGoal)
//      if (n != null) childNodes += n
//    }
//
//    return childNodes
//  }

//  private fun buildChildGiveWayNode(
//    parent: HighNode,
//    newConstraint: RobotConstraint,
//    finalGoal: RoutePosition,
//  ): HighNode? {
//    // 放前面，每次尝试构造都 +1
//    val childNodeId = synchronized(this) {
//      ++highNodeIdCounter
//    }
//
//    val masterRobotName = newConstraint.masterRobotName // 需要让路的机器人
//
//    FleetLogger.debug(
//      module = logModuleHighResolver,
//      subject = "BuildGiveWayChild",
//      sr = sr,
//      robotName = masterRobotName,
//      mapOf("childNodeId" to childNodeId, "parentId" to parent.id, "newConstraint" to newConstraint),
//    )
//
//    val oldRobotCtx = parent.robots[masterRobotName]!!
//
//    val robotCtx = oldRobotCtx.copy(
//      moved = true,
//      giveWayPointName = finalGoal.pointName,
//      solution = null,
//    )
//
//    val newRobotContexts = mutableMapOf<String, RobotPlanContext>()
//    newRobotContexts.putAll(parent.robots)
//    newRobotContexts[masterRobotName] = robotCtx
//
//    val lowResolver = TargetManyMapfLowResolver(
//      sr,
//      masterRobotName,
//      robotCtx.request,
//      mainAr,
//      o.robotCollisionModels,
//      robotCtx.reservePointName,
//      friendlyId,
//      childNodeId,
//      parent.allConstraints[masterRobotName]!!,
//      newRobotContexts,
//      o,
//    )
//    val rs = lowResolver.search("Build giving way -> ${finalGoal.pointName}")
//    lowNodeExpanded += rs.expandedCount
//
//    // 会不 OK 嘛，会
//    if (!rs.ok) {
//      FleetLogger.debug(
//        module = logModuleHighResolver,
//        subject = "GiveWayChildNodeNoSolution",
//        sr = sr,
//        robotName = masterRobotName,
//        msg = emptyMap(),
//      )
//      return null
//    }
//
//    val robotNewCtx = robotCtx.copy(solution = rs)
//    newRobotContexts[masterRobotName] = robotNewCtx
//
//    val (conflictNum, constraints) = ResConflictManager.getConflictNumAndEarliest(newRobotContexts, o)
//
//    val cost = parent.cost + rs.cost - (oldRobotCtx.solution?.cost ?: 0.0)
//
//    val giveWayPoints = HashSet(parent.giveWayPoints)
//    giveWayPoints += finalGoal.pointName
//
//    val n = HighNode(
//      id = childNodeId,
//      parentId = parent.id,
//      newConstraintForRobotName = masterRobotName,
//      newConstraint = newConstraint,
//      allConstraints = parent.allConstraints,
//      robots = newRobotContexts,
//      conflictNum = conflictNum,
//      conflictConstraints = constraints,
//      cost = cost,
//      planCost = rs.planCost,
//      lowExpanded = rs.expandedCount,
//      giveWayPoints = giveWayPoints,
//    )
//    logHighNode(n)
//    return n
//  }

//  private fun buildChildGiveWayNodes(
//    parent: HighNode,
//    newConstraint: RobotConstraint,
//  ): List<HighNode> {
//    val masterRobotName = newConstraint.masterRobotName // 需要让路的机器人
//
//    val oldRobotCtx = parent.robots[masterRobotName]!!
//
//    val childNodes = mutableListOf<HighNode>()
//
//    val fromPointName = if (!oldRobotCtx.giveWayPointName.isNullOrBlank()) {
//      // 之前规划了让路点，继续让；先只考虑在点上
//      oldRobotCtx.giveWayPointName
//    } else if (oldRobotCtx.moved) {
//      // 机器人有任务，从任务的终点开始
//      oldRobotCtx.solution!!.path.last().toPosition.pointName!!
//    } else {
//      // 从机器人当前点
//      val stand = oldRobotCtx.request.stand
//      // 先只考虑在点上
//      stand.pointName!!
//    }
//

  /**
   * 如果约束的结束时间 <0，表示目标机器人当前没有任务，需要生成让路运单。
   * 该函数负责检查机器人当前运单列表并在合适时调用 OrderService.createIdleAvoidOrder。
   */
  fun ensureGiveWayOrderForConstraint(robotCtx: RobotPlanContext, robotName: String): PlanResult? {
    val rr = sr.mustGetRobot(robotName)
    if (rr.orders.isNotEmpty()) return null // 已有任务不用创建
    val trafficTask = robotCtx.request.trafficTask
    if (trafficTask != null && trafficTask.status == TrafficTaskStatus.TrafficAccepted) return null

    val idlePointName = findIdlePointForRobot(rr, robotCtx) ?: return null

    val orderId = OrderService.generateOrderId() + "A"
    val steps = mutableListOf(
      TransportStep(
        id = IdHelper.oidStr(),
        orderId = orderId,
        stepIndex = 0,
        status = StepStatus.Executable,
        location = idlePointName,
        withdrawOrderAllowed = true,
      ),
    )

    val order = TransportOrder(
      id = orderId,
      status = OrderStatus.Allocated,
      keyLocations = listOf(steps[0].location),
      stepNum = 1,
      expectedRobotNames = listOf(robotName),
      actualRobotName = robotName,
      kind = OrderKind.IdleAvoid,
      stepFixed = true,
      sceneId = sr.sceneId,
    )

    OrderService.createIdleAvoidOrder(sr, robotName, OrderRuntime(order, steps), idlePointName)
    return PlanResult(
      ok = false,
      reason = "wait idle avoid,$idlePointName",
      solutions = emptyMap(),
      giveWayGoals = emptyMap(),
      timeCost = 0,
    )
  }

  /**
   * 为给定机器人选择一个空闲避让点：优先返回 PP 开头的停靠点，其次 LM 开头的 LocationMark 点。
   * 如果均未找到则返回 null。
   */
  private fun findIdlePointForRobot(rr: RobotRuntime, robotCtx: RobotPlanContext): String? {
    // 收集其他机器人任务目标点
    val busyTargets = requests.values
      .mapNotNull { it.trafficTask?.target?.pointName }
      .toSet()

    val startPoint = if (!robotCtx.request.robotInfo.stand.pointName.isNullOrEmpty()) {
      robotCtx.request.robotInfo.stand.pointName
    } else {
      MapService.findBestStartPointNameForPlan(rr)
    } ?: return null
    val mapCache = rr.sr.mapCache
    val maps = mapCache.getAllAreaMapsOfGroup(rr.config.groupId)
    var algInstance: SceneMapAlg? = null
    val algSupplier = {
      algInstance ?: SceneMapAlg(maps, emptySet()).also { algInstance = it }
    }
    val helper = ParkAndChargeResourceHelper(sr)
    val random = Random(System.currentTimeMillis())

    // ---- 优先选择停靠点 ----
    val parkCandidates = helper.listAvailableParkingPoints(rr)
    val reachableParks =
      getReachablePoints(robotCtx.request, startPoint, parkCandidates, algSupplier, ReachKey.Type.PARK)
    val parkPoint = reachableParks.firstOrNull { !busyTargets.contains(it.first) }?.first
    if (parkPoint != null) return parkPoint

    // ---- 其次选择充电点 ----
    val chargeCandidates = helper.listAvailableChargingPoints(rr)
    val reachableCharges =
      getReachablePoints(robotCtx.request, startPoint, chargeCandidates, algSupplier, ReachKey.Type.CHARGE)
    val chargePoint = reachableCharges.firstOrNull { !busyTargets.contains(it.first) }?.first
    if (chargePoint != null) return chargePoint

    // ---- 最后选择 LM 点 ----
    val candidateLms = mapCache.areaById[robotCtx.request.robotInfo.stand.areaId]
      ?.groupedMaps
      ?.get(rr.config.groupId)
      ?.pointNameMap
      ?.filter { (name, _) -> name.startsWith("LM") && !busyTargets.contains(name) }
      ?.keys
      ?: emptySet()

    val reachableLms = getReachablePoints(robotCtx.request, startPoint, candidateLms, algSupplier, ReachKey.Type.LM)
    if (reachableLms.isNotEmpty()) {
      val top10 = reachableLms.take(10).map { it.first }
      return top10.random(random)
    }
    return null
  }

  /**
   * 记录高层节点日志
   */
  open fun logHighNode(n: HighNode) {
    val solDesc = n.robots.values.joinToString("；") {
      "${it.robotName}: stand=${it.request.robotInfo.stand}, task=${it.request.trafficTask}," +
        " reservePointName=${it.reservePointName}, " +
        "sol=${it.solution}"
    }

    nodeLogs += VenusHelper.logTimestamp() + " 产生高层节点 | ID=${n.id} | 父节点=${n.parentId}" +
      " | 新解冲突数=${n.conflictNum} | 新解成本=${n.cost}" +
      " | 耗时=${n.planCost}ms | 展开底层节点=${n.lowExpanded}" +
      " | 施加新约束=${n.newConstraint} | 施加给机器人=${n.newConstraintForRobotName} " +
      " | 解=$solDesc"
  }

  /** 子类实现具体搜索逻辑 */
  protected abstract fun doResolve(): PlanResult

  private fun isParkOrCharge(pointName: String?): Boolean {
    if (pointName == null) return false
    return pointName.startsWith("PP") || pointName.startsWith("CP")
  }

  /**
   * 求解超时时间，毫秒
   */
  protected fun getResolveTimeout(): Long {
    var t = sr.getDevConfigAsLong("resolveTimeout") ?: 10_000
    if (t <= 0) t = 10_000
    return t
  }

  // 用任务目标作为起点的包装类
  data class RobotWithTarget(val rr: RobotRuntime, val targetPointName: String)
}