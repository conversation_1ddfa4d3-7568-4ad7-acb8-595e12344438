package com.seer.trick.fleet.seer

import com.fasterxml.jackson.annotation.*
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectReader
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.base.script.ScriptCenter
import com.seer.trick.base.script.ScriptExeRequest
import com.seer.trick.fleet.domain.*
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.StringHelper
import org.apache.commons.codec.digest.DigestUtils
import java.util.*

/**
 * 对 smap 的直接操作
 */
object SmapHelper {

  // 预先创建 ObjectReader 以提高性能
  private val smapReader: ObjectReader = JsonHelper.mapper.readerFor(Smap::class.java)

  /**
   * 字符串转 Smap 对象。目前直接映射，不需要额外处理。
   */
  fun strToSmap(str: String): Smap = smapReader.readValue(str)

  /**
   * Smap 字符串转 Smap 对象，并计算 MD5。
   */
  fun strToSmapMd5(str: String): Pair<Smap, String> =
    Pair(JsonHelper.mapper.readValue(str, Smap::class.java), DigestUtils.md5Hex(str))

  fun smapToScene(smap: Smap, smapName: String): SceneAreaMap = SmapToRobotMapConverter().add(smap, smapName).build()

  /**
   * 解析完 smap 后，通过脚本修改地图
   */
  fun patchSmapByScript(
    sceneId: String,
    areaId: Int,
    areaName: String,
    groupId: Int,
    mapName: String,
    areaMap: SceneAreaMap,
  ): SceneAreaMap {
    if (!ScriptCenter.exists("patchSmap")) return areaMap
    val ctx = mapOf(
      "sceneId" to sceneId,
      "areaId" to areaId,
      "areaName" to areaName,
      "groupId" to groupId,
      "mapName" to mapName,
    )
    // 地图通过 JSON 字符串传递
    val mapStr = JsonHelper.mapper.writeValueAsString(areaMap)
    val r: SceneAreaMap? = ScriptCenter.execute(ScriptExeRequest("patchSmap", arrayOf(ctx, mapStr)), jacksonTypeRef())
    return r ?: areaMap
  }

  fun base64ToString(base64Str: String?): String? {
    if (base64Str == null) return null
    if (base64Str.isEmpty()) return ""
    return String(Base64.getDecoder().decode(base64Str), Charsets.UTF_8)
  }

  fun base64ToBoolean(base64Str: String): Boolean = base64Str == "dHJ1ZQ=="

  fun valueToBase64Str(value: String): String = Base64.getEncoder().encodeToString(value.toByteArray())

  /**
   * 将 m4define 的 m4labelsStr 转换成 m4labels
   */
  fun getM4labels(m4define: JsonNode?): Set<String> {
    if (m4define?.has("m4labelsStr") != true) return emptySet()
    val m4labelStr = m4define.get("m4labelsStr").asText()
    return StringHelper.splitTrim(m4labelStr, ",").toSet()
  }
}

data class SmapFile(val name: String, val md5: String, val content: Smap)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Smap(
  val header: SmapMapHeader,
  val normalPosList: List<SmapPos> = emptyList(),
  val advancedPointList: List<SmapAdvancedPoint>? = null,
  val advancedLineList: List<SmapAdvancedLine>? = null,
  val advancedCurveList: List<SmapAdvancedCurve>? = null,
  val advancedAreaList: List<SmapAdvancedArea>? = null,
  val binLocationsList: List<SmapBinLocations>? = null,
  val rssiPosList: List<SmapPos>? = null,
  val reflectorPosList: List<SmapReflectorPos>? = null,
  val userData: List<SmapMapProperty>? = null,
) {
  fun getAllPoints(): List<SmapAdvancedPoint> =
    getParkPoints() + getChargePoints() + getActionPoints() + getLocationMarks() + getSwitchMapPoints()

  fun getParkPoints(): List<SmapAdvancedPoint> = advancedPointList?.filter {
    it.className == "ParkPoint"
  } ?: emptyList()

  fun getChargePoints(): List<SmapAdvancedPoint> = advancedPointList?.filter {
    it.className == "ChargePoint"
  } ?: emptyList()

  fun getActionPoints(): List<SmapAdvancedPoint> = advancedPointList?.filter {
    it.className == "ActionPoint"
  } ?: emptyList()

  fun getLocationMarks(): List<SmapAdvancedPoint> = advancedPointList?.filter {
    it.className == "LocationMark"
  } ?: emptyList()

  fun getSwitchMapPoints(): List<SmapAdvancedPoint> = advancedPointList?.filter {
    it.className == "SwitchMap"
  } ?: emptyList()

  fun getAllBins(): List<SmapBinLocation> {
    val bins = mutableListOf<SmapBinLocation>()
    binLocationsList?.forEach { binList ->
      binList.binLocationList.forEach {
        bins.add(it)
      }
    }
    return bins
  }
}

@JsonInclude(JsonInclude.Include.NON_NULL)
data class SmapMapHeader(
  val mapType: String = "2D-Map",

  /**
   * 该地图名称不带后缀名
   */
  val mapName: String,
  val minPos: SmapPos,
  val maxPos: SmapPos,
  val resolution: Double = 0.02,
  val version: String = "1.0.6",
)

data class SmapPos(
  val x: Double = 0.0,
  val y: Double = 0.0,
  val z: Double? = null, // NURBS6 使用
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class SmapAdvancedPoint(
  val className: String = "",
  val instanceName: String = "",
  val pos: SmapPos = SmapPos(0.0, 0.0),
  val dir: Double? = null,
  val ignoreDir: Boolean? = null, // 是否忽略点位朝向
  val attribute: SmapMapAttribute? = null,
  val desc: String? = null, // 这是 base64 编码格式的字符串
  val property: List<SmapMapProperty>? = null,
)

data class SmapAdvancedLine(
  val className: String,
  val instanceName: String? = null, // 部分 smap 的 FeatureLine 没有 instanceName 属性，即使有也都是字符串 "0"。
  val line: SmapMapLine,
  val attribute: SmapMapAttribute? = null,
  val property: List<SmapMapProperty>? = null,
)

data class SmapMapLine(val startPos: SmapPos, val endPos: SmapPos)

data class SmapMapAttribute(
  val description: String? = null,
  val colorPen: Long? = null,
  val colorBrush: Long? = null,
  val colorFont: Long? = null,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class SmapAdvancedCurve(
  val className: String,
  val instanceName: String,
  val startPos: SmapAdvancedCurveEndpoint,
  val endPos: SmapAdvancedCurveEndpoint,
  val controlPos1: SmapPos?,
  val controlPos2: SmapPos?,
  val controlPos3: SmapPos?,
  val controlPos4: SmapPos?,
  val property: List<SmapMapProperty> = emptyList(),
  val devices: List<SmapDevice>? = null,
  val attribute: SmapMapAttribute? = null,
  val desc: String? = null,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class SmapAdvancedCurveEndpoint(val instanceName: String = "", val pos: SmapPos = SmapPos(0.0, 0.0))

@JsonInclude(JsonInclude.Include.NON_NULL)
class SmapMapProperty(
  val key: String,
  val type: String = "",
  var value: String? = null, // 早期 protobuf 兼容，现在不用
  @JsonAlias("string_value")
  val stringValue: String? = null, // rbk上传时是snake_case
  val boolValue: Boolean? = null,
  val int32Value: Int? = null,
  val uint32Value: Long? = null,
  val int64Value: Long? = null,
  val uint64Value: Long? = null,
  val floatValue: Float? = null,
  val doubleValue: Double? = null,
  val bytesValue: ByteArray? = null,
  val tag: String? = "",
)

/**
 * TODO 为什么 smap 里库位是类似两级数组？
 */
data class SmapBinLocations(val binLocationList: List<SmapBinLocation> = emptyList())

data class SmapBinLocation(
  val className: String,
  val instanceName: String,
  val pointName: String?,
  val pos: SmapPos,
  val property: List<SmapMapProperty>?,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class SmapAdvancedArea(
  val className: String,
  val instanceName: String,
  val posGroup: List<SmapPos>,
  val dir: Double,
  val desc: String = "",
  val property: List<SmapMapProperty>? = null,
  val attribute: SmapMapAttribute? = null,
  val devices: List<SmapDevice>? = null,
)

data class SmapReflectorPos(val type: String, val width: Double, val x: Double, val y: Double)

data class SmapDevice(
  val modelName: String,
  val laserDevices: List<SmapLaserDevice>,
  val ultrasonicDist: List<Double>,
  val fallingdownDist: List<Double>,
)

data class SmapLaserDevice(val id: Long, val laserMarginPos: List<SmapPos>)

data class SmapMapUtil(
  val smap: Smap, // 地图本身
  val pointNameMap: Map<String, PointHelper>, // point name -> point helper
  //
  val pathNameMap: Map<String, PathHelper>, // path name -> path helper
  //
  val zoneNameMap: Map<String, ZoneHelper>, // zone name -> zone helper

  //
  val binNameMap: Map<String, BinHelper>, // bin name -> bin

)

data class PointHelper(
  val point: SmapAdvancedPoint,
  val forwardPaths: List<SmapAdvancedCurve>, // 从这个点位出发的所有路径
  val backwardPaths: List<SmapAdvancedCurve>, // 到从这个点位的所有路径
  val zones: List<SmapAdvancedArea>? = null, // 这个点位所在的所有区块
)

data class PathHelper(
  val path: SmapAdvancedCurve,
  val pointHelpers: List<PointHelper>, // 路径上的等间距的点
  val points: List<CurvePoint2D>, // 路径上的等间距的点,包括 x，y 坐标与斜率
)

data class ZoneHelper(
  val zone: SmapAdvancedArea,
  val points: List<SmapAdvancedPoint>? = null, // 这个区块中的所有点位
  val paths: List<SmapAdvancedCurve>? = null, // 与区块相交所有路径
)

data class BinHelper(
  val bin: SmapBinLocation,
  val pointHelper: PointHelper?, // 路径上的等间距的点
)

data class ClosestPath(val pathHelper: PathHelper, val index: Int, val distance: Double)