package com.seer.trick.fleet.traffic.distributed.plan.model

import com.fasterxml.jackson.annotation.JsonIgnore
import com.seer.trick.fleet.traffic.distributed.lock.model.SpaceLock
import com.seer.trick.fleet.traffic.distributed.map.*
import kotlin.math.abs

/**
 *  路径规划封装后的每一段路径
 * */
data class PathAction(
  val robotName: String, // 机器人名称
  var index: Long, // 索引
  val type: PathType, // 路径类型
  val mapName: String, // 地图编码
  val groupName: String, // 组编码
  val targetX: Double, // 终点 x坐标
  val targetY: Double, // 终点 y坐标
  val start: Position, // 起点
  val target: Point, // 结束点
  val lineName: String, // 路线编码
  val robotInHeading: Int, // 车进入方向
  val robotOutHeading: Int, // 车离开方向
  val containerName: String?, // 带载物编码
  val containerInHeading: Int, // 带载进入方向
  val containerOutHeading: Int, // 带载离开方向
) {

  var c1: Site? = null
  var c2: Site? = null

  @JsonIgnore
  var startLock: SpaceLock? = null

  @JsonIgnore
  @Volatile
  var targetLock: SpaceLock? = null

  @JsonIgnore
  @Volatile
  var runLock: SpaceLock? = null

  fun clearLock() {
    startLock = null
    targetLock = null
    runLock = null
  }

  fun equals(pathAction: PathAction): Boolean = pathAction.robotName == robotName &&
    pathAction.type == type &&
    pathAction.mapName == mapName &&
    pathAction.targetX == targetX &&
    pathAction.targetY == targetY &&
    pathAction.lineName == lineName &&
    pathAction.robotInHeading == robotInHeading &&
    pathAction.robotOutHeading == robotOutHeading &&
    pathAction.containerName == containerName &&
    pathAction.containerInHeading == containerInHeading &&
    pathAction.containerOutHeading == containerOutHeading

  fun isRotate(): Boolean = type == PathType.ROTATE || type == PathType.TURNING

  fun isMove(): Boolean = type == PathType.STRAIGHT || type == PathType.ARC || type == PathType.CURVE

  fun stop(): Boolean = type == PathType.STOP

  fun distance(): Double = abs(targetX - start.x) + abs(targetY - start.y)
}