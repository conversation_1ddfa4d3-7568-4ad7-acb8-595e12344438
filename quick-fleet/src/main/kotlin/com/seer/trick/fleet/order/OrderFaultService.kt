package com.seer.trick.fleet.order

import com.seer.trick.BzError
import com.seer.trick.fleet.FleetLogger
import com.seer.trick.fleet.adapter.RobotRbkAdapter
import com.seer.trick.fleet.domain.OrderStatus
import com.seer.trick.fleet.order.OrderService.countFaultDuration
import com.seer.trick.fleet.order.OrderService.countLoadDuration
import com.seer.trick.fleet.order.OrderService.countUnloadDuration
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.RobotService
import com.seer.trick.fleet.service.SceneRuntime
import org.slf4j.LoggerFactory

/**
 * 处理运单的故障或故障恢复。单独放在一个类里解决 OrderService 过大的问题。
 */
object OrderFaultService {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 让运单和机器人故障。sec 为空表示运单尚未被执行。
   */
  fun markOrderAndRobotFault(
    sr: SceneRuntime,
    or: OrderRuntime,
    sec: StepExecuteContext?,
    msg: String,
  ) = sr.withOrderLock {
    logger.info("Mark order $or fault: $msg, sec=$sec")

    val newOrder = or.order.copy(
      fault = true,
      faultReason = msg,
      failureNum = or.order.failureNum + 1,
      loadDuration = countLoadDuration(or),
      unloadDuration = countUnloadDuration(or),
    )
    OrderService.updateAndPersistOrder(or, newOrder, "Mark order failed: $msg")

    if (sec != null) {
      sec.fault = true
      sec.faultMsg = msg
    }
  }

  /**
   * 从运单发起故障重试
   */
  fun retryByOrder(sr: SceneRuntime, orderId: String) {
    val or = sr.mustGetOrderById(orderId)
    val order = or.order
    val robotName = or.order.actualRobotName

    if (!order.fault) {
      FleetLogger.error(
        module = "Order",
        subject = "RetryFaultOrder",
        sr = sr,
        robotName = robotName,
        msg = mapOf("msg" to "Retry fault order, but order no fault", "order" to order),
      )
      return
    }

    val rr = if (!robotName.isNullOrBlank()) {
      sr.mustGetRobot(robotName)
    } else {
      null
    }

    FleetLogger.info(
      module = "OrderFault",
      subject = "RetryByOrder",
      sr = sr,
      robotName = robotName,
      msg = mapOf("orderId" to orderId, "robot" to robotName),
    )

    clearOrderFault(sr, or)

    if (rr != null) {
      RobotRbkAdapter.requestControl(rr)
      clearRobotFault(rr)
    }
  }

  /**
   * 从机器人发起故障重试
   */
  fun retryByRobot(rr: RobotRuntime) {
    // 两种情况，机器人没有当前执行的步骤，但有运单故障；如系统重启后
    val sec = rr.executingStep
    val or = if (sec != null && sec.fault) {
      sec.or
    } else {
      rr.orders.values.find { it.order.fault }
    }
    if (or == null) throw BzError("errRobotNotFailed", rr.robotName)

    FleetLogger.info(
      module = "OrderFault",
      subject = "RetryByRobot",
      sr = rr.sr,
      robotName = rr.robotName,
      msg = mapOf("order" to or.id),
    )

    clearOrderFault(rr.sr, or)

    RobotRbkAdapter.requestControl(rr)
    clearRobotFault(rr)
  }

  private fun clearOrderFault(sr: SceneRuntime, or: OrderRuntime) = sr.withOrderLock {
    // !!
    or.withdrawStepAllowed = true

    val newOrder = or.order.copy(
      fault = false,
      faultReason = null,
      faultDuration = countFaultDuration(or),
      status = if (or.order.status == OrderStatus.Executing) OrderStatus.Pending else or.order.status,
    )
    OrderService.updateAndPersistOrder(or, newOrder, "Retry failed order")
  }

  private fun clearRobotFault(rr: RobotRuntime) {
    if (RobotService.isControlCommandAllowed(rr)) {
      // 自动清一下机器人自身的错误，锁外
      RobotRbkAdapter.clearAlarm(rr)
    }
  }
}