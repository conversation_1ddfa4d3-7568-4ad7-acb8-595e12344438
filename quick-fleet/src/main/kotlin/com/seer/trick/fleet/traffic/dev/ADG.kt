package com.seer.trick.fleet.traffic.dev

/**
 * 机器人 1 要从 A 到 C，然后从 C 到 D；到达 C 的时刻是 t。
 * 机器人 2 要从 B 到 C，到达 C 的时刻是 t + M，即比机器人 1 晚。
 * 那么一定要机器人 1 先到 D（即确保已离开 C），机器人 2 才能开始从 B 到 C 的移动，比较保险。
 * 否则机器人 1 一边离开 C，机器人 2 同时去 C，如果速度不同步，2 可能撞 1。
 */
class ADG(cbsResult: MapfResult) {

  /**
   * 每个节点的前序节点
   */
  val nodes = mutableMapOf<String, MutableList<String>>()

  init {
    if (cbsResult.ok) {
      for (rr in cbsResult.plans.values) {
        val path = rr.path
        for (i in 0 until path.size - 2) {
          // 前一段路径
          val n1key = toNodeKey(rr.robotName, i)
          // 后一段路径
          val n2key = toNodeKey(rr.robotName, i + 1)
          nodes.getOrPut(n2key) { ArrayList() }.add(n1key)
        }
      }
      for (rr1 in cbsResult.plans.values) {
        val path1 = rr1.path
        for (i in 0 until path1.size - 1) {
          for (rr2 in cbsResult.plans.values) {
            val path2 = rr2.path
            if (rr1.robotName == rr2.robotName) continue
            for (j in 0 until path2.size - 1) {
              // s == g
              val s = path1[i]
              val g = path2[j]
              if (s.x == g.x && s.y == g.y && s.timeEnd <= g.timeEnd) {
                val n1key = toNodeKey(rr1.robotName, i + 1)
                val n2key = toNodeKey(rr2.robotName, j)
                nodes.getOrPut(n2key) { ArrayList() }.add(n1key)
              }
            }
          }
        }
      }
    }
  }

  // ADG 的一个顶点的唯一键。
  private fun toNodeKey(r: String, pathIndex: Int) = "$r#$pathIndex"
}