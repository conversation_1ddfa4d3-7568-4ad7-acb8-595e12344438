package com.seer.trick.fleet.traffic.distributed.lock.graph

import com.seer.trick.fleet.traffic.distributed.helper.GraphHelper

/**
 * 多边形
 * */
class Polygon(
  override var type: GraphType,
  val points: List<Vector>, // 具有的顶点数量
  val concave: Boolean, // 判断是否为凹多边形
  val subPolygons: List<Polygon>, // 每一个 小 polygon 为凸多边形
  var box: BoundingBox, // // 外轮廓，即外接矩形
) : Shape {

  override fun intersectionRectangle(rect: Rectangle): Boolean {
    // 先和外接矩形相交
    if (!rect.box.intersect(box)) {
      return false
    }
    // 判断是否为 凹 多边形
    if (concave) {
      // 遍历子多边形，判断是否相交
      for (polygon in subPolygons) {
        if (polygon.intersectionRectangle(rect)) {
          return true
        }
      }
      return false
    }
    // 为凸多边形，判断是否相交
    return polygonCheckCollision(points, rect.points)
  }

  override fun intersectionCircle(circle: Circle): Boolean {
    val center = circle.center
    val radius = circle.radius
    if (!circle.getBoundingBox().intersect(box)) {
      return false
    }
    if (concave) {
      for (polygon in subPolygons) {
        if (polygon.intersectionCircle(circle)) {
          return true
        }
      }
    }
    for (i in points.indices) {
      val p1 = points[i]
      val p2 = points[(i + 1) % points.size]
      if (radius >= VeLine(Vector(p1.x, p1.y), Vector(p2.x, p2.y))
          .vectorProjectToLine(Vector(center.x, center.y))
      ) {
        return true
      }
    }
    return this.box.onRegion(circle.center.x, circle.center.y)
  }

  override fun intersectionPolygon(polygon: Polygon): Boolean {
    if (!polygon.box.intersect(box)) {
      return false
    }
    if (concave) {
      for (subPolygon in subPolygons) {
        if (polygon.intersectionPolygon(subPolygon)) {
          return true
        }
      }
    }
    if (polygon.concave && !concave) {
      for (subPolygon in polygon.subPolygons) {
        if (intersectionPolygon(subPolygon)) {
          return true
        }
      }
    }
    return polygonCheckCollision(points, polygon.points)
  }

  override fun intersectionSector(sector: Sector): Boolean {
    // todo 待后期需要再实现
    return false
  }

  override fun getBoundingBox(): BoundingBox = box

  override fun copy(): Shape {
    val ps: MutableList<Vector> = mutableListOf()
    for (point in this.points) {
      ps.add(point.copy())
    }
    val sps: MutableList<Polygon> = mutableListOf()
    if (concave) {
      for (polygon in this.subPolygons) {
        sps.add(polygon.copy() as Polygon)
      }
    }
    return Polygon(type = GraphType.POLYGON, points = ps, concave = concave, subPolygons = sps, box = BoundingBox(ps))
  }

  /**
   * 凸多边形碰撞检测
   * 基于 SAT 定理
   * */
  private fun polygonCheckCollision(shapeA: List<Vector>, shapeB: List<Vector>): Boolean {
    if (shapeA.size < 2 || shapeB.size < 2) {
      return false
    }
    return !GraphHelper.satTheorem(shapeA, shapeB) &&
      !GraphHelper.satTheorem(shapeB, shapeA)
  }
}