package com.seer.trick.fleet.device.door

import com.seer.trick.fleet.device.door.adapter.DoorAdapter
import com.seer.trick.fleet.device.door.adapter.DoorAdapterMock
import java.util.*
import java.util.concurrent.CopyOnWriteArrayList

class DoorRuntime(
  @Volatile
  var config: SceneDoor,
) {

  @Volatile
  var adapter: DoorAdapter = DoorAdapterMock(config)

  /**
   * 设备是否在线
   */
  @Volatile
  var online: Boolean = false

  /**
   * 最后一次状态更新时间
   */
  @Volatile
  var lastStatusUpdateTime: Date = Date()

  /**
   * 设备是否故障
   */
  @Volatile
  var fault: Boolean = false

  /**
   * 设备故障信息
   */
  @Volatile
  var faultMsg: String = ""

  /**
   * 门的状态
   */
  @Volatile
  var status: DoorMainStatus = DoorMainStatus.Unknown

  /**
   * key 是此门下的路径。value 是此门前后的一些路径，通过这些路径并以 key 路径为目标时，提前开门
   */
  @Volatile
  var preOpenPathPaths: Map<String, Set<String>> = emptyMap()

  /**
   * 开门需求
   */
  val openDemands: MutableList<OpenDoorDemand> = CopyOnWriteArrayList()

  fun toUiReport(): DoorUiReport = DoorUiReport(
    online = online,
    fault = fault,
    faultMsg = faultMsg,
    lastStatusUpdateTime = lastStatusUpdateTime,
    status = status,
    disabled = config.disabled,
    openDoorRobotNames = openDemands.map { it.robotName },
  )

  override fun toString(): String = "${config.name}:$status"
}