package com.seer.trick.fleet.traffic.venus

import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.seer.RotationDirection
import com.seer.trick.fleet.traffic.TrafficTaskRuntime
import java.util.*

/**
 * 机器人规划请求
 */
data class RobotPlanRequest(
  val robotInfo: RobotInfo,
  val trafficTask: TrafficTaskRuntime?, // null 表示没有任务
  val initPath: List<State>?,
)

/**
 * 规划用的机器人信息
 */
data class RobotInfo(
  val robotName: String,
  val stand: RobotStand,
  val groupId: Int = 0, // 所属组 ID
  val loadTheta: Double? = null, // 容器的朝向
  val moveSpeed: Double = 1.0, // 1 m/s
  val rotateSpeed: Double = Math.toRadians(45.0).roundRadianPrecision(), // 45°/s
  val collisionModel: RobotLoadCollisionModel,
)

/**
 * 机器人规划上下文。机器人分两种，有任务的和无任务的。
 */
data class RobotPlanContext(
  val robotName: String,
  val request: RobotPlanRequest,
  val moved: Boolean, // 动还是不动，原地等待
  val reservePointName: String?, // 避让点。一次规划最多一个避让。 TODO
  val solution: RobotSolution?, // 需要移动的机器人移动方案  TODO
  val delayed: Boolean = false, // 是否已做过延迟启动 TODO
)

interface TimeSteps {
  val timeStart: Long // 开始去这个位置的第一个时间步
  val timeEnd: Long // 到达这个位置的时刻
}

/**
 * 允许跨多个时间步
 */
data class State(
  var pathIndex: Int,
  val type: StateType,
  val toPosition: RoutePosition,
  // toEndHead 最终车头朝向；对于起点，为机器人初始的朝向；对于最终点，一般指定一个方向；
  // 对于中间点，一般为路径最后一个点的方向或相反方向（取决于正走倒走）
  // TODO toEndHead 不能为 null
  val toEndHead: Double? = null,
  val byPathKey: String? = null, // 到达这个状态经过的路径
  val robotEnterTheta: Double, // 机器人进入线路的弧度，根据正走倒走可能不同
  val robotExitTheta: Double, // 机器人离开线路的弧度，根据正走倒走可能不同
  val loadEnterTheta: Double? = null, // 货物进入的弧度
  val loadExitTheta: Double? = null, // 货物退出的弧度
  val moveDirection: MoveDirection, // 机器人移动方向
  val rotateDirection: RotationDirection, // 旋转方向: 0 不限制，1 逆时针，-1 顺时针
  override val timeStart: Long = 0, // 开始去这个位置的第一个时间步
  override val timeEnd: Long = 0, // 到达这个位置的时刻
  val timeNum: Long = 0,
  val released: Boolean = false, // 已下发，不能改
  val reserve: Boolean = false, // 预留
  val shapes: PolygonsWithBBox? = null, // 处于这个状态以及到达这个状态需要的空间资源，如果不需要碰撞检测 null
) : TimeSteps {

  fun isSameLocation(o: State) = toPosition.isSameLocation(o.toPosition)

  fun isSameLocation(p: RoutePosition) = toPosition.isSameLocation(p)

  fun isSameLocation(p: MapPoint) = toPosition.isSameLocation(p)

  fun isTimeOverlay(o: TimeSteps): Boolean = isTimeOverlay(timeStart, timeEnd, o.timeStart, o.timeEnd)

  /**
   * 空间资源交叠
   */
  fun spatialIntersecting(o: State): Boolean {
    if (shapes == null || o.shapes == null) return false

    // quick reject by comparing overall bounding boxes
    val bbox1 = shapes.bbox
    val bbox2 = o.shapes.bbox
    if (!bbox1.intersects(bbox2)) return false

    for (s1 in shapes.polys) {
      for (s2 in o.shapes.polys) {
        if (GeoHelper.isPolygonsIntersectingByVenus(s1, s2)) return true
      }
    }
    return false
  }

  fun spatialIntersecting(p: PolygonsWithBBox?): Boolean {
    if (shapes == null || p == null) return false
    // quick reject by comparing overall bounding boxes
    val bbox1 = shapes.bbox
    val bbox2 = p.bbox
    if (!bbox1.intersects(bbox2)) return false
    for (s1 in shapes.polys) {
      for (s2 in p.polys) {
        if (GeoHelper.isPolygonsIntersectingByVenus(s1, s2)) return true
      }
    }
    return false
  }

  fun spatialIntersecting(p: Polygon): Boolean {
    if (shapes == null) return false

    for (s1 in shapes.polys) {
      if (GeoHelper.isPolygonsIntersectingByVenus(s1, p)) return true
    }
    return false
  }

  fun spatialIntersecting(o: List<Polygon>): Boolean {
    if (shapes == null || o.isEmpty()) return false

    val bbox1 = shapes.bbox
    val bbox2 = o.combinedAabb()
    if (!bbox1.intersects(bbox2)) return false

    for (s1 in shapes.polys) {
      for (s2 in o) {
        if (GeoHelper.isPolygonsIntersectingByVenus(s1, s2)) return true
      }
    }
    return false
  }

  override fun toString(): String =
    "$pathIndex|$type|$timeStart:$timeEnd|@$toPosition|$moveDirection|$rotateDirection｜$reserve|${shapes?.polys?.size}"

  /**
   * 用于哈希快速索引：位置 + 结束时间。
   */
  fun stateTimeKey(): String = "$toPosition|$timeStart｜$timeEnd|$moveDirection|$rotateDirection"

  companion object {

    /**
     * 两个时间段是否交叠，必须 >= 0
     */
    fun isTimeOverlay(start1: Long, end1: Long, start2: Long, end2: Long): Boolean {
      if (start1 < 0) throw IllegalArgumentException("start1<0")
      if (start2 < 0) throw IllegalArgumentException("start2<0")
      return if (end1 < 0 && end2 < 0) {
        true
      } else if (end1 < 0) {
        start1 <= end2
      } else if (end2 < 0) {
        start2 <= end1
      } else {
        start1 <= end2 && start2 <= end1
      }
    }
  }
}

/**
 * order 用于启发式函数中的排序
 */
enum class StateType(val order: Int) {
  Start(-1), // 起点
  Wait(0), // 原地等待，不动不旋转
  Move(1), // 移动（也会带旋转）
  Goal(-1), // 终点
  // Rotate, // 原地旋转
}

/**
 * 包含多边形列表及其整体包围盒，避免重复计算
 */
data class PolygonsWithBBox(val polys: List<Polygon>) {
  val bbox: AABB = run {
    var minX = Double.POSITIVE_INFINITY
    var minY = Double.POSITIVE_INFINITY
    var maxX = Double.NEGATIVE_INFINITY
    var maxY = Double.NEGATIVE_INFINITY

    for (poly in polys) {
      val bbox = poly.bbox
      if (bbox.minX < minX) minX = bbox.minX
      if (bbox.minY < minY) minY = bbox.minY
      if (bbox.maxX > maxX) maxX = bbox.maxX
      if (bbox.maxY > maxY) maxY = bbox.maxY
    }

    AABB(minX, minY, maxX, maxY)
  }
}

/**
 * 机器人在移动路径上的位置。
 */
data class RoutePosition(
  val type: RoutePositionType = RoutePositionType.Point,
  val pointName: String? = null,
  val pathKey: String? = null,
  val p: Double = 0.0,
  val pathStartPointName: String? = null,
  val pathEndPointName: String? = null,
  val x: Double,
  val y: Double,
  val reserved: Boolean = false,
) {
  fun isSameLocation(o: RoutePosition) = isSameLocation(o.x, o.y)

  fun isSameLocation(p: MapPoint) = isSameLocation(p.x, p.y)

  private fun isSameLocation(px: Double, py: Double) = px.lengthEquals(x) && py.lengthEquals(y)

  /**
   * 欧式距离
   */
  fun euclideanDistance(o: RoutePosition): Double = GeoHelper.euclideanDistance(x, y, o.x, o.y)

  override fun toString(): String = if (type == RoutePositionType.Point) {
    pointName!!
  } else {
    "$pathKey+$p%"
  }

  companion object {

    fun from(ttt: TrafficTaskTarget) = RoutePosition(
      type = RoutePositionType.Point,
      pointName = ttt.pointName,
      x = ttt.x,
      y = ttt.y,
    )
  }
}

enum class RoutePositionType {
  Point, // 在点上
  Path, // 在路径上
}

data class RobotSolution(
  val robotName: String,
  val ok: Boolean = true,
  val reason: String? = null,
  var path: List<State> = emptyList(), // 包括起点和每一个目标点。目标点标识类型标识为 Goal。
  val cost: Double = 0.0, //
  // val minF: Double = 0.0, //
  val expandedCount: Long = 0,
  val generatedCount: Long = 0,
  val planCost: Long = 0, // 毫秒
  val timeNum: Long = 0,
  val timeStart: Long = 0,
  val timeEnd: Long = 0,
  // val fromState: State,
  // val toState: State,
  val solutionOn: Date = Date(), // 产生解的时间
)

/**
 * 低层节点
 */
data class LowNode(
  val goalIndex: Int,
  val index: Int,
  val state: State,
  val parent: LowNode?,
  val f: Double, // 单位：时间
  val focalHeuristic: Double, // 单位：冲突数量 TODO 这算个 f 还是 h 值
  val g: Double, // 到这个节点的实际成本，单位：时间
  var inOpen: Boolean = false, // 是否在 open 列表中（懒删除）
) {

  // 缓存 toString 结果，构造时就计算，后续直接返回

  override fun toString(): String =
    "${parent?.index}->$index|$state|g=$g|h=${(f - g).roundTwoDigital()}|f=$f|fh=$focalHeuristic"
}

/**
 * 高层节点
 */
class HighNode(
  val id: Long, // 节点 ID
  val parentId: Long,
  val robots: Map<String, RobotPlanContext>,
  val newConstraintForRobotName: String?, // 这个节点相对于父节点，给此机器人施加约束，null 是根节点
  val newConstraint: RobotConstraint?, // 构建这个节点时施加的约束，null 是根节点
  val allConstraints: Map<String, List<RobotConstraint>>, // 这个节点搜路用的约束 robot name ->
  val conflictNum: Int, // 这个节点搜路后统计冲突数
  // TODO 考虑 RobotConstraint 变数组？如果一次发现多个相关冲突
  val conflictConstraints: Map<String, RobotConstraint>?, // 这个节点搜路产生的时间最早的冲突
  val cost: Double, // 总的移动城堡
  val planCost: Long, // 求解耗时
  val lowExpanded: Long, // 低层节点展开数量
  val giveWayPoints: Set<String> = emptySet(), // 让路经过的节点
)

data class PlanResult(
  val ok: Boolean,
  val reason: String? = null,
  val solutions: Map<String, RobotSolution>,
  val giveWayGoals: Map<String, String>,
  val timeCost: Long,
  val highNodeExpanded: Long = 0,
  val lowNodeExpanded: Long = 0,
  val lastNode: HighNode? = null, // 当失败时最后一个检查的节点
  val priorityEdges: List<Pair<String, String>> = emptyList(), // PBS 产生的优先级边 low->high
  val timestamp: Date = Date(),
)

data class PathFindingOption(
  val highW: Double,
  val lowW: Double,
  val maxWaitingTimesOnState: Long = 200L, // 在同一位置最大等待步数。要大于 goalStopTimes，否则就搜不到解了。可能要等其他机器人让出一条路，要等很久
  val waitingOnStateMinSec: Long = 3L, // 每次（每个状态）等待的时间步长；减少搜索状态数
  val goalStopTimes: Long = 15L, // 在终点额外等待时间步数（近似秒）
  val collisionModels: Map<String, RobotLoadCollisionModel>,
  val window: Long? = 20, // 全局窗口限制，如果为 null 则没有窗口限制
  val initialPriorityEdges: List<Pair<String, String>> = emptyList(), // PBS 初始优先级
  val enableReservePath: Boolean = true,
  val apGiveWayAllowed: Boolean = false, // ap 点是否允许让路，为 true 就允许，为 false 就不允许
)

/**
 * 判断状态是否在窗口范围内
 */
fun State.isWithinWindow(windowLimit: Long): Boolean = timeStart < windowLimit + 1

/**
 * 裁剪状态到窗口范围内
 */
fun State.trimToWindow(windowLimit: Long): State {
  if (timeEnd > windowLimit + 1) {
    return copy(timeEnd = windowLimit + 1)
  }
  return this
}

/**
 * 裁剪路径到窗口范围内
 */
fun List<State>.trimToWindow(windowLimit: Long?): List<State> {
  if (windowLimit == null) return this
  val result = mutableListOf<State>()
  var nextPathIndex = 0
  for (state in this) {
    if (!state.isWithinWindow(windowLimit)) break
    result.add(state.trimToWindow(windowLimit).copy(pathIndex = nextPathIndex++))
  }
  return result
}

data class AdgNodeDependencies(
  val topology: MutableSet<String> = HashSet(),
  val spatial: MutableSet<String> = HashSet(),
)

/**
 * 一个区域的 ADG 数据
 */
data class AdgArea(
  val areaId: Int,
  /**
   * ADG 依赖关系
   * robot name ->
   */
  @Volatile
  var adgNodes: Map<String, AdgNodeDependencies> = emptyMap(),
  /**
   * 已完成的 ADG 依赖
   */
  val finishedAdgNodes: MutableSet<String> = Collections.synchronizedSet(HashSet()),
)

/**
 * 一个机器人的执行上下文。封装规划后路径执行情况。
 */
class VenusRobotExecutionContext(
  val robotName: String,
  val areaId: Int,
  val trafficTaskId: String,
  val trafficTask: TrafficTaskRuntime,
  val path: List<State>,
  val done: Boolean = false, // TODO 金楷添加未使用
) {

  /**
   * offset 之前是上次已下发给机器人的路径
   */
  @Volatile
  var pathIndexOffset = 0

  /**
   * 已释放可下发给机器人的路径索引
   */
  @Volatile
  var committedPathIndex = 0

  /**
   * 已下发给机器人的路径索引
   */
  @Volatile
  var releasedPathIndex = 0

  /**
   * 机器人已上报完成的路径的索引。第一个节点是起点，直接释放
   */
  @Volatile
  var donePathIndex = 0

  /**
   * 到终点的路径是否已经下发
   */
  @Volatile
  var goalEdgeReleased = false

  @Volatile
  var goalEdgeReleasedIndex = 0
}

data class VenusRobotPlanResult(
  val robotName: String,
  val trafficTaskId: String,
  val request: RobotPlanRequest,
  val ok: Boolean,
  val reason: String,
  val path: List<State>,
  val resultOn: Date = Date(),
)

// 计算一组多边形的整体包围盒
private fun List<Polygon>.combinedAabb(): AABB {
  val minX = this.minOf { it.bbox.minX }
  val minY = this.minOf { it.bbox.minY }
  val maxX = this.maxOf { it.bbox.maxX }
  val maxY = this.maxOf { it.bbox.maxY }
  return AABB(minX, minY, maxX, maxY)
}