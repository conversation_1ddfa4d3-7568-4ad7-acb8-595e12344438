/**
 * 除非特殊说明，均使用右手坐标系。
 * 距离单位为米。方向单位为弧度 [0, 2π）。
 */
package com.seer.trick.fleet.domain

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.seer.trick.fleet.traffic.venus.cache.EdgeCacheProvider
import kotlin.math.atan2

/**
 * 二维点。浮点数。
 */
data class Point2D(val x: Double = 0.0, val y: Double = 0.0) {

  operator fun minus(other: Point2D): Point2D = Point2D(x - other.x, y - other.y)

  operator fun plus(other: Point2D): Point2D = Point2D(x + other.x, y + other.y)

  // 重载 `/` 运算符，使 Point2D 支持与 Double 的除法
  operator fun div(scalar: Double): Point2D = Point2D(x / scalar, y / scalar)

  /**
   * 计算点积
   */
  fun dot(other: Point2D): Double = x * other.x + y * other.y

  /**
   * 计算向量的方向角度（弧度制）
   * @return 方向角（弧度）
   */
  fun dir(): Double = atan2(y, x)

  /**
   * 计算叉积
   */
  fun cross(other: Point2D): Double = x * other.y - y * other.x

  /**
   * 正交或者法向量
   */
  fun orthogonal(): Point2D = Point2D(-y, x)

  /**
   * 计算这个点到原点的距离，即这个点/向量的模。
   *
   * @return 这个点到原点的距离。
   */
  fun mod(): Double = kotlin.math.hypot(x, y)

  /**
   * 计算单位向量
   */
  fun unit(): Point2D {
    val mod = mod()
    return Point2D(x / mod, y / mod)
  }

  operator fun plusAssign(rhs: Point2D) {
    this += rhs
  }

  override fun toString(): String = "($x,$y)"
}

/**
 * 将Point2D类型别名为Vector2D，用于二维向量的表示
 */
typealias Vector2D = Point2D

/**
 * 定义Double类型与Vector2D类型的乘法运算
 * 这里使用operator函数是为了使Double类型可以使用乘法运算符来直接与Vector2D类型相乘
 * 参数rhs：Vector2D操作数
 */
operator fun Double.times(rhs: Vector2D): Vector2D {
  return Vector2D(this * rhs.x, this * rhs.y) // 分别将Double类型与Vector2D的x和y分量相乘，生成新的Vector2D
}

/**
 * 定义Vector2D类型与Double类型的乘法运算
 * 这里使用operator函数是为了重载乘法运算符，使得Vector2D类型可以直接被Double类型乘以
 * 参数rhs：Double操作数
 */
operator fun Vector2D.times(rhs: Double): Vector2D {
  return rhs * this // 调用Double类型的乘法运算符实现，保持运算符的交换律特性
}

/**
 * 旋转函数，用于将二维向量按照指定的弧度数进行旋转
 * @param vec：需要旋转的二维向量
 * @param angleRad：旋转的弧度值
 */
fun rotate(vec: Vector2D, angleRad: Double): Vector2D {
  val c = kotlin.math.cos(angleRad) // 计算旋转弧度的余弦值
  val s = kotlin.math.sin(angleRad) // 计算旋转弧度的正弦值
  return Vector2D(vec.x * c - vec.y * s, vec.x * s + vec.y * c) // 根据旋转矩阵计算旋转后的向量
}

/**
 * 二维点和弧度
 */
data class Pose2D(val x: Double = 0.0, val y: Double = 0.0, val theta: Double = 0.0)

/**
 * 曲线上的一个点，及其参数
 */
data class CurvePoint2D(
  val x: Double = 0.0,
  val y: Double = 0.0,
  val percentage: Double, // 位于曲线的位置，百分比位置
  val tangent: Double = 0.0, // 切线方向 [-π, π]
  val normal: Double = 0.0, // 法线方向 [-π, π]
)

/**
 * 轴对齐包围盒（AABB）。用于快速碰撞剪枝。
 */
data class AABB(val minX: Double, val minY: Double, val maxX: Double, val maxY: Double) {
  fun intersects(o: AABB): Boolean = !(maxX < o.minX || o.maxX < minX || maxY < o.minY || o.maxY < minY)
}

data class Edge(val p1: Point2D, val p2: Point2D) {
  val bbox: AABB
    get() = EdgeCacheProvider.getOrCompute(p1, p2)
}

@JsonIgnoreProperties("edges", "bbox")
data class Polygon(
  val points: List<Point2D> = emptyList(),
  val type: PolygonShape = PolygonShape.Other,
  private val precomputedBbox: AABB? = null,
) {

  // 如果调用方已给出 bbox，则直接使用，避免再次遍历 points
  val bbox: AABB by lazy(LazyThreadSafetyMode.NONE) {
    precomputedBbox ?: run {
      var minX = Double.POSITIVE_INFINITY
      var maxX = Double.NEGATIVE_INFINITY
      var minY = Double.POSITIVE_INFINITY
      var maxY = Double.NEGATIVE_INFINITY
      for (p in points) {
        val x = p.x
        val y = p.y
        if (x < minX) minX = x
        if (x > maxX) maxX = x
        if (y < minY) minY = y
        if (y > maxY) maxY = y
      }
      AABB(minX, minY, maxX, maxY)
    }
  }

  val edges: List<Edge> by lazy(LazyThreadSafetyMode.NONE) {
    val size = points.size
    (0 until size).map { i -> Edge(points[i], points[(i + 1) % size]) }
  }

  /**
   * 点集为空，或面积为零
   */
  fun isEmpty() = points.isEmpty() || GeoHelper.calculatePolygonArea(this) == 0.0

  companion object {

    fun fromRectCenterWidthHeight(cx: Double, cy: Double, w: Double, height: Double): Polygon = Polygon(
      listOf(
        Point2D(cx - w / 2, cy + height / 2),
        Point2D(cx + w / 2, cy + height / 2),
        Point2D(cx + w / 2, cy - height / 2),
        Point2D(cx - w / 2, cy - height / 2),
      ),
      PolygonShape.Rect,
    )
  }
}

/**
 * 特殊形状的多边形
 */
enum class PolygonShape {
  Other,
  Rect,
}

/**
 * 矩形。
 */
data class Rect(
  val cx: Double = 0.0, // 中心点
  val cy: Double = 0.0, // 中心点
  val width: Double = 0.0,
  val height: Double = 0.0,
  val theta: Double = 0.0, // 绕中心
)

/**
 * 以 OBB 描述矩形
 */
data class OBB(
  val unitVectorsWidth: Vector2D, // 宽方向上的单位向量
  val unitVectorsHeight: Vector2D, // 长方向上的单位向量
  val halfWidth: Double = 0.0, // 宽度的一半
  val halfHeight: Double = 0.0, // 长度的一半
  val center: Point2D, // 中心点
)

/**
 * Nurbs 曲线控制点
 */
data class NurbsControlPoint(val x: Double = 0.0, val y: Double = 0.0, val weight: Double = 0.0)

/**
 * x,y 坐标最大最小值
 */
data class Bound2D(val minX: Double, val maxX: Double, val minY: Double, val maxY: Double) {

  fun toRect(): Rect {
    val cx = (minX + maxX) / 2
    val cy = (minY + maxY) / 2
    val width = maxX - minX
    val height = maxY - minY
    return Rect(cx, cy, width, height)
  }
}

/**
 * 圆
 */
data class Circle(
  val cx: Double = 0.0, // 中心点
  val cy: Double = 0.0, // 中心点
  val radius: Double = 0.0,
)