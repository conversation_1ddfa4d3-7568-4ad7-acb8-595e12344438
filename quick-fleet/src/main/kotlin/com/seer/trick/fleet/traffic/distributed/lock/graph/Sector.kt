package com.seer.trick.fleet.traffic.distributed.lock.graph

/**
 * 扇形
 *  todo 待后期需要实现
 * */

class Sector(override var type: GraphType) : Shape {

  override fun intersectionRectangle(rect: Rectangle): <PERSON><PERSON>an = false

  override fun intersectionCircle(circle: Circle): <PERSON><PERSON><PERSON> = false

  override fun intersectionPolygon(polygon: Polygon): <PERSON><PERSON>an = false

  override fun intersectionSector(sector: Sector): <PERSON><PERSON>an = false

  override fun getBoundingBox(): BoundingBox = BoundingBox(0.0, 0.0, 0.0, 0.0)

  override fun copy(): Shape {
    TODO("Not yet implemented")
  }
}