package com.seer.trick.fleet.device.lift.adapter.vendor.jinbo

import com.seer.trick.base.net.tcp.MsgDecoder
import com.seer.trick.base.net.tcp.MsgEncoder
import java.io.EOFException
import java.io.InputStream
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.charset.StandardCharsets

/**
 * <AUTHOR>
 * @date 2025/5/27 15:16
 */
class JinBoTcpMsg(val flowNo: Int, val apiNo: Int, val body: String)

class JinBoTcpMsgDecoder : MsgDecoder<JinBoTcpMsg> {
  private var started = false

  private val headArray: ByteArray = ByteArray(JinBoTcpClient.HEAD_SIZE - 1)
  private var headLimit = 0

  private var headOk = false

  private var flowNo: Byte = 0
  private var apiNo: Short = 0
  private var bodySize: Int = 0
  private var bodyArray: ByteArray = ByteArray(1024) // 持有最大
  private var bodyLimit = 0

  override fun decode(inputStream: InputStream): JinBoTcpMsg? {
    if (!started) {
      val next = inputStream.read()
      if (next == -1) throw EOFException("Read start " + JinBoTcpClient.START_MARK)
      if (next.toByte() == JinBoTcpClient.START_MARK) {
        started = true
      } else {
        return null
      }
    }

    // 一定 started
    if (!headOk) {
      val headSizeInc = inputStream.read(headArray, headLimit, headArray.size - headLimit)
      if (headSizeInc == -1) throw EOFException("Read head")
      headLimit += headSizeInc
      if (headLimit == JinBoTcpClient.HEAD_SIZE - 1) {
        headOk = true
        val headBuf = ByteBuffer.wrap(headArray)
        headBuf.order(ByteOrder.BIG_ENDIAN)
        flowNo = headBuf.get()
        apiNo = headBuf.getShort()
        bodySize = headBuf.getInt()

        if (bodySize > bodyArray.size) {
          bodyArray = ByteArray(bodySize)
        }
      } else {
        return null
      }
    }

    // 一定 headOk
    val bodySizeInc = inputStream.read(bodyArray, bodyLimit, bodySize - bodyLimit)
    if (bodySizeInc == -1) throw EOFException("Read body")
    bodyLimit += bodySizeInc
    return if (bodyLimit == bodySize) {
      val body = String(bodyArray, 0, bodySize, StandardCharsets.UTF_8)
      val msg = JinBoTcpMsg(flowNo.toInt(), apiNo.toInt(), body)
      reset()
      msg
    } else {
      null
    }
  }

  private fun reset() {
    started = false
    headLimit = 0
    headOk = false
    bodyLimit = 0
    bodySize = 0
    flowNo = 0
    apiNo = 0
  }
}

class JinBoTcpMsgEncoder : MsgEncoder<JinBoTcpMsg> {
  override fun encode(msg: JinBoTcpMsg): ByteBuffer {
    val bodyBytes = msg.body.toByteArray(StandardCharsets.UTF_8)

    val buf = ByteBuffer.allocate(JinBoTcpClient.HEAD_SIZE + bodyBytes.size)
    buf.order(ByteOrder.BIG_ENDIAN)

    buf.put(JinBoTcpClient.START_MARK) // 报头
    buf.put(msg.flowNo.toByte()) // 流水号
    buf.putShort(msg.apiNo.toShort()) // 指令编号
    buf.putInt(bodyBytes.size) // 数据区长度

    buf.put(bodyBytes)

    buf.flip()
    return buf
  }
}