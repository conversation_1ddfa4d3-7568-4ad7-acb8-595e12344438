import com.seer.trick.fleet.domain.RejectReason
import com.seer.trick.fleet.order.OrderRuntime
import com.seer.trick.fleet.service.RobotRuntime

/**
 * 检查结果 - 内部使用的简单结果
 */
data class CheckResult(val success: Boolean, val code: String? = null, val messages: List<Any?> = emptyList())

/**
 * 诊断响应 - 统一的外部API返回类型
 */
data class DiagnosisResponse(
  val passed: Boolean, // 是否通过
  val firstRejection: RejectReason? = null,

  val report: DiagnosisReport? = null, // 完整报告（可选）
)

/**
 * 诊断报告项
 */
data class DiagnosisItem(
  val itemName: String, // 检查项目名称
  val code: String? = null, // 检查错误代码
  val passed: Boolean, // 是否通过
  val params: List<Any?>? = null,
)

/**
 * 诊断报告
 */
data class DiagnosisReport(
  val checkList: List<DiagnosisItem>, // 所有检查项
  val pass: Boolean, // 整体是否通过
)

/**
 * 检查项接口
 */
interface CheckItem {
  val name: String // 检查项名称

  /**
   * TODO 增加 tc?
   */
  fun check(checkReq: CheckReq): CheckResult
}

class CheckGroup(private val name: String) {
  private val items = mutableListOf<CheckItem>()

  fun addCheck(checkItem: CheckItem) = apply { items.add(checkItem) }
  fun include(group: CheckGroup) = apply { items.addAll(group.items) }

  fun check(checkReq: CheckReq): DiagnosisResponse {
    // 快速失败模式（调度场景）
    if (!checkReq.fullDiagnosis) {
      return quickCheck(checkReq)
    }

    // 完整诊断模式
    val report = diagnose(checkReq)
    val firstFailure = report.checkList.firstOrNull { !it.passed }

    return DiagnosisResponse(
      passed = report.pass,
      firstRejection = firstFailure?.let { RejectReason(it.code!!, it.params ?: emptyList()) },
      report = report,
    )
  }

  /**
   * 快速失败短路模式
   */
  private fun quickCheck(checkReq: CheckReq): DiagnosisResponse {
    for (item in items) {
      val result = item.check(checkReq)
      if (!result.success) {
        return DiagnosisResponse(
          passed = false,
          firstRejection = RejectReason(result.code!!, result.messages),
        )
      }
    }
    return DiagnosisResponse(passed = true)
  }

  /**
   * 完整诊断模式
   */
  private fun diagnose(checkReq: CheckReq): DiagnosisReport {
    val results = mutableListOf<DiagnosisItem>()
    var allPassed = true

    for (item in items) {
      val result = item.check(checkReq)
      if (result.success) {
        results.add(DiagnosisItem(item.name, result.code, true))
      } else {
        allPassed = false
        results.add(DiagnosisItem(item.name, result.code, false, result.messages))
        // 注意：诊断模式不触发副作用，仅收集结果
        // item.onFail(robot, result.code, result.messages)
      }
    }

    return DiagnosisReport(results, allPassed)
  }
}

/**
 * 检查请求
 */
data class CheckReq(
  val rr: RobotRuntime? = null,
  val or: OrderRuntime? = null,
  val fromPointName: String? = null, // 起始位置，一般是机器人当前点位
  val pointName: String? = null,
  val fullDiagnosis: Boolean = false,
)

/**
 * 辅助函数 - 创建通过结果
 */
fun pass(): CheckResult = CheckResult(true)

/**
 * 辅助函数 - 创建失败结果
 */
fun fail(code: String, messages: List<Any?> = emptyList()): CheckResult = CheckResult(false, code, messages)

data class CheckRobotOrderReq(
  val subject: String,
  val robotName: String? = null,
  val orderId: String? = null,
  val pointName: String? = null,
)