package com.seer.trick.fleet.stats

import com.seer.trick.fleet.domain.RobotExecuteStatus
import com.seer.trick.fleet.service.*
import com.seer.trick.fleet.traffic.TrafficTaskStatus
import org.slf4j.LoggerFactory
import java.util.concurrent.*
import java.util.concurrent.atomic.AtomicReference
import kotlin.math.*

object TrafficConditionsService {
  // 日志记录器
  private val logger = LoggerFactory.getLogger(javaClass)

  // 线程池配置
  private val recordExecutor = Executors.newSingleThreadExecutor()
  private val cleanupScheduler = Executors.newScheduledThreadPool(1)

  // 常量配置
  private const val STUCK_SPEED_THRESHOLD = 0.05 // 静止判定阈值(m/s)
  private const val SPEED_SAMPLE_EXPIRATION_MS = 10_000L // 速度样本有效期10秒
  private const val CONGESTION_DECAY_RATE = 0.05 // 拥堵指数衰减系数
  private const val MAX_SPEED_SAMPLES = 10 // 最大速度样本数
  private const val IDEAL_SPEED_M_PER_S = 1.5 // 理想通行速度(m/s)
  private const val PATH_OCCUPATION_TIMEOUT_MS = 10_000L // 路径占用超时时间 10 秒

  // 全局状态管理
  private val trafficStats = AtomicReference(TrafficStats())
  private val robotCurrentNodes = ConcurrentHashMap<String, Set<String>>()

  init {
    // 初始化周期性清理任务
    cleanupScheduler.scheduleAtFixedRate({
      try {
        cleanupInactivePaths()
      } catch (e: Exception) {
        logger.error("周期性清理失败", e)
      }
    }, 5, 2, TimeUnit.SECONDS)
  }

  /**
   * 异步处理机器人运行报告
   */
  fun onReport(rr: RobotRuntime): Future<*> = recordExecutor.submit {
    synchronized(this) { processReport(rr) }
  }

  /**
   * 机器人报告主处理流程
   */
  private fun processReport(rr: RobotRuntime) {
    val robotName = rr.robotName
    val previousNodes = getPreviousNodes(robotName)

    if (!shouldProcessReport(rr)) {
      handleInvalidReport(robotName, previousNodes)
      return
    }

    val currentNodes = determineCurrentNodes(rr)
    updateNodeStatuses(rr, currentNodes)
    updatePathPassage(rr)
    robotCurrentNodes[robotName] = currentNodes
  }

  /**
   * 获取机器人关联的历史节点
   */
  private fun getPreviousNodes(robotName: String): Set<String> = trafficStats.get().nodes.values
    .filter {
      it.activeStucks.containsKey(robotName) ||
        it.speedSamples.any { it.robotName == robotName }
    }
    .map { it.id }.toSet()

  /**
   * 处理无效报告的数据清理
   */
  private fun handleInvalidReport(robotName: String, previousNodes: Set<String>) {
    previousNodes.forEach { node ->
      trafficStats.get().nodes[node]?.let { nodeStat ->
        nodeStat.activeStucks.remove(robotName)
        nodeStat.speedSamples.removeAll { it.robotName == robotName }
      }
    }
  }

  /**
   * 根据机器人位置确定当前节点
   */
  private fun determineCurrentNodes(rr: RobotRuntime): MutableSet<String> {
    val currentNodes = mutableSetOf<String>()
    val closetPoints = MapService.findClosetPointsForTrafficConditions(rr)
    currentNodes.addAll(closetPoints.orEmpty())
    rr.selfReport?.stand?.pointName?.let(currentNodes::add)

//    val tm = rr.sr.trafficService.showTrafficResourceMessage(rr.robotName)
//    val unTravel = tm?.pathResource?.unTravelPointNames
//    val travelled = tm?.pathResource?.travelledPointNames
//
//    if (travelled?.isNotEmpty() == true) {
//      // 添加最后一个已行驶点
//      currentNodes.add(travelled[travelled.size - 1])
//    }
//    if (unTravel?.isNotEmpty() == true) {
//      // 添加最后一个已行驶点到第一个未行驶点的路径
//      unTravel[0].let { currentNodes.add(it) }
//    }

    return currentNodes
  }

  /**
   * 更新节点状态（拥堵检测和速度跟踪）
   */
  private fun updateNodeStatuses(rr: RobotRuntime, currentNodes: Set<String>) {
    val robotName = rr.robotName
    val speed = rr.selfReport?.main?.velocity ?: return

    // 清理已离开节点的数据
    getLeftNodes(robotName, currentNodes).forEach { node ->
      trafficStats.get().nodes[node]?.let { nodeStat ->
        nodeStat.activeStucks.remove(robotName)
        nodeStat.speedSamples.removeAll { it.robotName == robotName }
      }
    }

    // 更新当前节点状态
    currentNodes.forEach { node ->
      updateNodeStuckStatus(robotName, node, speed)
      updateNodeSpeed(robotName, node, speed)
    }
  }

  /**
   * 判断是否处理该报告
   */
  private fun shouldProcessReport(rr: RobotRuntime): Boolean = !rr.disabled() &&
    !rr.offDuty &&
    RobotService.isOnline(rr) &&
    (
      rr.pendingTrafficTask?.status == TrafficTaskStatus.TrafficAccepted ||
        rr.getExecuteStatus() == RobotExecuteStatus.Failed
      )

  /**
   * 获取机器人离开的节点集合
   */
  private fun getLeftNodes(robotName: String, currentNodes: Set<String>): Set<String> =
    robotCurrentNodes[robotName]?.let { previous ->
      previous - currentNodes
    } ?: emptySet()

  /**
   * 更新节点拥堵状态
   */
  private fun updateNodeStuckStatus(robotName: String, nodeId: String, speed: Double) {
    val stats = trafficStats.get()
    val pointStat = stats.nodes.getOrPut(nodeId) { PointStat(nodeId) }

    if (speed < STUCK_SPEED_THRESHOLD) {
      pointStat.activeStucks.putIfAbsent(robotName, System.currentTimeMillis())
    } else {
      pointStat.activeStucks.remove(robotName)
    }
  }

  /**
   * 更新节点速度样本
   */
  private fun updateNodeSpeed(robotName: String, nodeId: String, speed: Double) {
    val stats = trafficStats.get()
    val pointStat = stats.nodes.getOrPut(nodeId) { PointStat(nodeId) }
    val samples = pointStat.speedSamples

    samples.add(SpeedPoint(System.currentTimeMillis(), speed, robotName))

    // 维护样本数量限制
    while (samples.size > MAX_SPEED_SAMPLES) {
      samples.poll()
    }

    // 清理过期样本
    samples.removeIf { System.currentTimeMillis() - it.timestamp > SPEED_SAMPLE_EXPIRATION_MS }
  }

  /**
   * 更新路径占用信息
   */
  private fun updatePathPassage(rr: RobotRuntime) {
    val main = rr.selfReport?.main ?: return
    val closePaths = MapService.getClosePaths(rr, main) ?: return

    // 占用新路径
    closePaths.forEach { path ->
      occupyPath(path.pathKey, rr.robotName)
    }
  }

  /**
   * 占用指定路径
   */
  private fun occupyPath(pathKey: String, robotName: String) {
    val stats = trafficStats.get()
    val (nodeA, nodeB) = pathKey.split("->")

    stats.paths.computeIfAbsent(pathKey) {
      PathStat(nodeA, nodeB)
    }.apply {
      occupant = robotName
      occupiedSince = System.currentTimeMillis()
    }
  }

  /**
   * 计算路径拥堵等级 [0-4]
   */
  fun calculateCongestionLevel(pathKey: String): Int {
    val stats = trafficStats.get()
    val pathStat = stats.paths[pathKey] ?: return 0

    val nodeACongestion = calculateNodeCongestion(stats, pathStat.nodeA)
    val nodeBCongestion = calculateNodeCongestion(stats, pathStat.nodeB)

    val congestion = max(nodeACongestion, nodeBCongestion)

    // 应用占用时间加成
//    if (pathStat.occupiedSince != null &&
//      (nodeACongestion > STUCK_SPEED_THRESHOLD || nodeBCongestion > STUCK_SPEED_THRESHOLD)
//    ) {
//
//      val occupationTime = System.currentTimeMillis() - pathStat.occupiedSince!!
//      val extraWeight = min(occupationTime / 60_000.0, 5.0)
//      congestion = min(congestion + extraWeight * 0.1, 1.0)
//    }

    return when {
      congestion < 0.1 -> 0
      congestion < 0.2 -> 1
      congestion < 0.6 -> 2
      congestion < 0.8 -> 3
      else -> 4
    }
  }

  /**
   * 计算节点拥堵指数 [0.0-1.0]
   */
  private fun calculateNodeCongestion(stats: TrafficStats, nodeId: String): Double {
    val nodeStat = stats.nodes[nodeId] ?: return 0.0
    val now = System.currentTimeMillis()

    val smoothedSpeed = calculateSmoothedSpeed(nodeStat, now)

    return if (smoothedSpeed < STUCK_SPEED_THRESHOLD) {
      // 计算活跃拥堵持续时间
      val activeWeightedDuration = nodeStat.activeStucks.values.sumOf { startTime ->
        val durationSec = (now - startTime) / 1000.0
        durationSec + 2
      }.coerceIn(0.0..10.0)

      activeWeightedDuration / 10.0
    } else {
      max(0.0, (1.0 - (smoothedSpeed / IDEAL_SPEED_M_PER_S)) * 0.2)
    }
  }

  /**
   * 计算指数加权平均速度
   */
  private fun calculateSmoothedSpeed(pointStat: PointStat, now: Long): Double {
    var totalWeight = 0.0
    var weightedSum = 0.0

    pointStat.speedSamples.forEach { point ->
      val ageSec = (now - point.timestamp) / 1000.0
      val weight = exp(-CONGESTION_DECAY_RATE * ageSec)

      weightedSum += abs(point.speed) * weight
      totalWeight += weight
    }

    return if (totalWeight == 0.0) 0.0 else weightedSum / totalWeight
  }

  /**
   * 获取全量路径交通状况
   */
  fun getPathTrafficConditions(): Map<String, Int> {
    cleanupInactivePaths()
    val filterValues = trafficStats.get().paths.mapValues { (pathKey, _) ->
      calculateCongestionLevel(pathKey)
    }.filterValues { it != 0 }
    return filterValues
  }

  /**
   * 清理不活跃路径
   */
  private fun cleanupInactivePaths() {
    val stats = trafficStats.get()
    val now = System.currentTimeMillis()

    stats.paths.values.forEach { pathStat ->
      // 清理超时占用
      if (pathStat.occupiedSince != null &&
        now - pathStat.occupiedSince!! > PATH_OCCUPATION_TIMEOUT_MS
      ) {
        pathStat.occupiedSince = null
      }

      // 移除完全空闲的路径
      if (pathStat.occupiedSince == null) {
        val nodeACongestion = calculateNodeCongestion(stats, pathStat.nodeA)
        val nodeBCongestion = calculateNodeCongestion(stats, pathStat.nodeB)

        if (nodeACongestion < 0.01 && nodeBCongestion < 0.01) {
          stats.paths.remove(pathStat.key)
        }
      }
    }

    // 替换统计对象
    trafficStats.compareAndSet(stats, stats.copy())
  }

  // 数据结构定义
  data class SpeedPoint(val timestamp: Long, val speed: Double, val robotName: String? = null)

  data class TrafficStats(
    val nodes: ConcurrentHashMap<String, PointStat> = ConcurrentHashMap(),
    val paths: ConcurrentHashMap<String, PathStat> = ConcurrentHashMap(),
  )

  // 将 LinkedList 改为 ConcurrentLinkedQueue
  data class PointStat(
    val id: String,
    val activeStucks: ConcurrentHashMap<String, Long> = ConcurrentHashMap(),
    val speedSamples: ConcurrentLinkedQueue<SpeedPoint> = ConcurrentLinkedQueue(),
  )

  data class PathStat(
    val nodeA: String,
    val nodeB: String,
    var occupant: String? = null,
    var occupiedSince: Long? = null,
    val key: String = "$nodeA->$nodeB",
  )
}