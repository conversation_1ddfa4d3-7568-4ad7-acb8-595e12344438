package com.seer.trick.fleet.falcon.bp

import com.seer.trick.BzError
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockOutputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.falcon.domain.ParamObjectType
import com.seer.trick.fleet.domain.GeoHelper
import com.seer.trick.fleet.domain.SceneStatus
import com.seer.trick.fleet.domain.TransportOrder
import com.seer.trick.fleet.order.OrderRuntime
import com.seer.trick.fleet.order.OrderService
import com.seer.trick.fleet.service.SceneService
import com.seer.trick.helper.StringHelper

class CreateTransportOrderBp : AbstractBp() {

  override fun process() {
    val sceneName = getBlockInputParam("sceneName") as String?
    val priority = getBlockInputParamAsLong("priority") ?: 0
    val expectedRobotNames = getBlockInputParam("expectedRobotNames") as String?
    val expectedRobotGroups = getBlockInputParam("expectedRobotGroups") as String?
    val keyLocations = mustGetBlockInputParam("keyLocations") as String

    val sr = if (!sceneName.isNullOrBlank()) {
      SceneService.mustGetSceneByName(sceneName)
    } else {
      SceneService.listScenes().firstOrNull { it.status == SceneStatus.Initialized }
        ?: throw BzError("errNoEnabledScene")
    }
    val orderId = OrderService.generateOrderId()
    val robotNames = StringHelper.splitTrim(expectedRobotNames, ",")
    val groups = StringHelper.splitTrim(expectedRobotGroups, ",")
    val keyLocs = StringHelper.splitTrim(keyLocations, ",")

    // 检查一下关键位置是否存在
    for (loc in keyLocs) {
      if (!(sr.mapCache.pointNames.contains(loc) || sr.mapCache.binNames.contains(loc))) {
        throw BzError("errNoSuchBinOrLocation", loc)
      }
    }

    // 容器相关
    val containerId = getBlockInputParam("containerId") as String?
    val containerTypeName = getBlockInputParam("containerTypeName") as String?
    val containerDir = getBlockInputParam("containerDir") as Double?

    val order = TransportOrder(
      id = orderId,
      priority = priority.toInt(), // 优先级
      externalId = taskRuntime.taskId,
      containerId = if (!containerId.isNullOrBlank()) containerId else null,
      containerTypeName = containerTypeName,
      containerDir = if (containerDir != null) GeoHelper.normalizeRadian(Math.toRadians(containerDir)) else null,
      expectedRobotNames = robotNames,
      expectedRobotGroups = groups,
      keyLocations = keyLocs,
      sceneId = sr.sceneId,
    )
    OrderService.createOrders(sr, listOf(OrderRuntime(order, emptyList())))

    addResource("TransportOrder", orderId, mapOf("orderId" to orderId, "sceneName" to sr.basic.name))
    setBlockOutputParams(mapOf("orderId" to orderId))
    // 添加猎鹰任务相关业务对象
    addRelatedObject("TransportOrder", orderId, sceneName)
  }

  companion object {

    val def = BlockDef(
      CreateTransportOrderBp::class.simpleName!!,
      color = "#C7DCA7",
      // TODO 待添加的参数：externalId、containerId、taskBatch
      inputParams = listOf(
        BlockInputParamDef("sceneName", BlockParamType.String, false),
        BlockInputParamDef("priority", BlockParamType.Long, false),
        BlockInputParamDef("expectedRobotNames", BlockParamType.String, false),
        BlockInputParamDef("expectedRobotGroups", BlockParamType.String, false),
        BlockInputParamDef("keyLocations", BlockParamType.String, true),
        BlockInputParamDef("containerId", BlockParamType.String, false),
        BlockInputParamDef("containerTypeName", BlockParamType.String, false),
        BlockInputParamDef("containerDir", BlockParamType.Double, false),
      ),
      outputParams = listOf(
        BlockOutputParamDef("orderId", BlockParamType.String, objectTypes = listOf(ParamObjectType.OrderId)),
      ),
    )
  }
}