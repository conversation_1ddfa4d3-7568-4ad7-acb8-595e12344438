package com.seer.trick.fleet.map

import com.seer.trick.fleet.domain.MapPoint
import com.seer.trick.fleet.domain.ShortestPath
import org.jgrapht.Graph
import org.jgrapht.alg.shortestpath.DijkstraShortestPath
import org.jgrapht.graph.DefaultWeightedEdge
import org.jgrapht.graph.builder.GraphTypeBuilder

/**
 * 对地图算法的封装。
 * 兼容用，后续会移除。
 */
class SceneMapAlg(areaMaps: Collection<AreaMapCache>, excludedPointNames: Set<String>? = null) {

  private val graph: Graph<String, DefaultWeightedEdge>

  val g: DijkstraShortestPath<String, DefaultWeightedEdge>

  init {
    graph = buildEmptyGraph()

    for (ar in areaMaps) {
      val pointMap: MutableMap<String, MapPoint> = HashMap()
      for (point in ar.areaMap.points) {
        if (excludedPointNames != null && excludedPointNames.contains(point.name)) continue
        graph.addVertex(point.name)
        pointMap[point.name] = point
      }

      for (path in ar.areaMap.paths) {
        if (excludedPointNames != null &&
          (excludedPointNames.contains(path.fromPointName) || excludedPointNames.contains(path.toPointName))
        ) {
          continue
        }
        val e = graph.addEdge(path.fromPointName, path.toPointName)
        if (e != null) graph.setEdgeWeight(e, path.actualLength)
      }
    }

    g = DijkstraShortestPath(graph)
  }

  fun getShortestPath(from: String, end: String): ShortestPath {
    if (!graph.containsVertex(from)) return ShortestPath(false, reason = "No start point: $from")
    if (!graph.containsVertex(end)) return ShortestPath(false, reason = "No end point: $end")
    val p = g.getPath(from, end) ?: return ShortestPath(false)
    return ShortestPath(true, p.vertexList, "", p.weight)
  }

  private fun buildEmptyGraph(): Graph<String, DefaultWeightedEdge> = GraphTypeBuilder
    .directed<String, DefaultWeightedEdge>()
    .allowingMultipleEdges(false)
    .allowingSelfLoops(false)
    .edgeClass(DefaultWeightedEdge::class.java)
    .weighted(true).buildGraph()
}