package com.seer.trick.fleet.device.door.adapter

import com.seer.trick.BzError
import com.seer.trick.base.concurrent.BaseConcurrentCenter
import com.seer.trick.fleet.device.door.*
import com.seer.trick.helper.submitCatch
import com.seer.wcs.device.ClientDeviceConnectStatus
import com.seer.wcs.device.plc.PlcCenter
import com.seer.wcs.device.plc.modbus.ModbusClient
import com.seer.wcs.device.plc.modbus.ModbusReadReq
import com.seer.wcs.device.plc.modbus.ModbusWriteReq
import org.slf4j.LoggerFactory

/**
 * 暂时只支持 Modbus
 */
class DoorAdapterPlc(private val config: SceneDoor) : DoorAdapter() {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 定时查询并缓存？
   */
  override fun report(): DoorAdapterReport {
    val client = getModbusClient()
    val online = client.status == ClientDeviceConnectStatus.Connected

    // 检查故障状态
    // 从对应的地址读取到的表示故障信息的值，记录到 faultMsg 中，之后再考虑是否要解析，以及如何解析。
    //  门是个相对简单的设备，门控也无法提供丰富的故障信息，最多可能就是“是否有故障”。
    //  故障分两大类：
    //    1 门控上报的故障信息；
    //    2 出了 1 之外的其他故障信息，例如连接失败。
    val faultMessage = getFaultMessage()
    val inFault = faultMessage != null
    val faultMsg = faultMessage?.desc ?: ""

    // 读取门状态
    val mainStatus = getDoorStatus()

    return DoorAdapterReport(
      online = online,
      fault = inFault,
      faultMsg = faultMsg,
      mainStatus = mainStatus,
    )
  }

  override fun openDoor(remark: String) {
    logger.info("try to open door ${config.name}: '$remark'")

    // 检查开门前是否需要复位关门信号
    if (config.needCloseReset) {
      controlDoor(config.plcCloseDoor, "Door ${config.name} Missing 'plcCloseDoor'", reset = true)
    }

    // 开门
    controlDoor(config.plcOpenDoor, "Door ${config.name} Missing 'plcOpenDoor'")
  }

  override fun closeDoor(remark: String) {
    super.closeDoor(remark)
    logger.info("try to close door ${config.name}: '$remark'")

    // 关门前复位开门信号
    if (config.needOpenReset) {
      controlDoor(config.plcOpenDoor, "Door ${config.name} Missing 'plcOpenDoor'", reset = true)
    }

    // 关门
    if (config.plcCloseDoor != null) controlDoor(config.plcCloseDoor, "Door ${config.name} Missing 'plcCloseDoor'")

    // 复位关门信号
    if (config.needCloseReset && config.closeResetInterval > 0) {
      try {
        Thread.sleep(config.closeResetInterval)
        BaseConcurrentCenter.highTimeSensitiveExecutor.submitCatch("${config.name} close reset", logger) {
          controlDoor(config.plcCloseDoor, "Door ${config.name} Missing 'plcCloseDoor'", reset = true)
        }
      } catch (e: InterruptedException) {
        logger.error("closeDoor reset sleep interrupted", e)
      }
    }
  }

  private fun getModbusClient(): ModbusClient {
    if (config.plcName.isNullOrBlank()) throw BzError("errCodeError", "Missing 'plcName'")
    return PlcCenter.getModbusClient(config.plcName)
      ?: throw BzError("errCodeError", "No such modbus client: ${config.plcName}")
  }

  /**
   * 获取门控状态
   */
  private fun getDoorStatus(): DoorMainStatus {
    // 简化一下，PLC 只需要反馈明确的开门到位信号即可，如果读到的值不是期望值，统一判定门的状态是 Closed
    // 如果能提供多个状态，之后再考虑。
    return if (doorIsOpened()) DoorMainStatus.Opened else DoorMainStatus.Closed
  }

  private fun getFaultMessage(): FaultMessage? {
    val faultOp = config.plcFaultStatus ?: return null
    val client = getModbusClient()
    val result = client.read(
      ModbusReadReq(code = faultOp.code, address = faultOp.address, qty = 1, slaveId = faultOp.slaveId),
    )
    // todo 优先展示门控反馈的原始故障信息。
    val rv = result.getOrNull(0) ?: throw BzError("errDoorReadFaultMsgButNull", config.name)
    return faultOp.faults.firstOrNull { f -> f.value == rv }
      ?: FaultMessage(rv, "Undefined fault msg ...")
  }

  private fun doorIsOpened(): Boolean {
    val openedOp = config.plcOpenedStatus ?: throw BzError("errCodeError", "Missing 'plcOpenedStatus'")
    val result = readDoorStatusResult(openedOp)
    return result == openedOp.value
  }

  private fun readDoorStatusResult(statusOp: PlcReadCommand): Int {
    val client = getModbusClient()
    val result = client.read(
      ModbusReadReq(code = statusOp.code, address = statusOp.address, qty = 1, slaveId = statusOp.slaveId),
    )
    return result.getOrNull(0) ?: throw BzError("errDoorReadStatusButNull", config.name)
  }

  private fun controlDoor(op: PlcWriteCommand?, remark: String, reset: Boolean = false) {
    if (op == null) throw BzError("errCodeError", remark)
    val value = if (reset) 0 else op.value
    val client = getModbusClient()
    client.writeOnce(
      ModbusWriteReq(code = op.code, address = op.address, slaveId = op.slaveId),
      listOf(value),
    )
  }
}