package com.seer.trick.fleet.service

import com.seer.trick.BzError
import com.seer.trick.I18N.lo
import com.seer.trick.base.alarm.AlarmItem
import com.seer.trick.base.alarm.AlarmLevel
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.map.MapPointCache
import com.seer.trick.fleet.map.SceneMapAlg
import org.slf4j.LoggerFactory
import java.util.*

/**
 * 地图服务。
 * TODO 清理不用的方法。优化实现。
 */
object MapService {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 不可达的 cost 表示
   */
  const val NOT_ACHIEVABLE_COST = -1.0

  /**
   * 根据机器人上报信息估测机器人位置。
   * 注意，无地图、区域信息算无效位置，返回 null。
   */
  fun estimateStand(rr: RobotRuntime, main: RobotSelfReportMain?): RobotStand? {
    if (main == null) return null

    if (main.x == null || main.y == null) return null

    val cache = rr.sr.mapCache

    val mapName = main.currentMap
    // 没有地图算无效位置
    if (mapName.isNullOrBlank()) return null

    val areaId = rr.sr.mapCache.groupMaps[rr.config.groupId]?.areaByMapName?.get(mapName)
    val areaName = areaId?.let { cache.areaById[it]?.schema?.name }
    // 没有区域算无效位置
    if (areaId == null || areaName == null) return null

    val closePaths = getClosePaths(rr, main)

    val cm = RobotService.getCollisionModel(rr)
    val collisionShape = GeoHelper.translateThenRotate(cm, main.x, main.y, main.x, main.y, main.direction ?: 0.0)

    return RobotStand(
      x = main.x, y = main.y, theta = main.direction ?: 0.0, mapName = mapName,
      areaId = areaId, areaName = areaName,
      pointName = main.currentPoint,
      pathPositions = closePaths,
      velocity = main.velocity, rotateVelocity = main.rotateVelocity,
      timestamp = main.timestamp ?: Date(),
      collisionShape = collisionShape,
    )
  }

  /**
   * 返回以路径规划为目的的，机器人的最佳起始点位。
   * TODO 后面尽量直接用 Stand，机器人不需要一定有最佳起点，可以支持四种情况：在点上、在线上，在点附近、在线附近
   */
  fun findBestStartPointNameForPlan(rr: RobotRuntime): String? {
    val main = rr.selfReport?.main ?: run {
      val ai = AlarmItem(
        sceneId = rr.sr.sceneId,
        group = "Fleet",
        code = "RobotFetchError",
        key = "RobotFetchError-${rr.robotName}",
        message = lo("warningRobotFetchError", listOf(rr.robotName)),
        level = AlarmLevel.Error,
        args = listOf(rr.sr.sceneId, rr.robotName),
        tags = setOf(rr.tag, rr.sr.tag),
      )
      AlarmService.addItem(ai, ttl = 5000)
      return null
    }

    // TODO 实际上不能以 currentPoint 为准，应该以 x、y 为准
    // 在点位上,
    if (!main.currentPoint.isNullOrBlank()) return main.currentPoint

    // 不在点位上
    if (main.x == null || main.y == null) return null

    // 机器人当前区域 & 机器人所在机器人组的地图
    val gac = rr.sr.mapCache.getAreaMapCacheByStandAndGroup(rr)
    if (gac == null) {
      val ai = AlarmItem(
        sceneId = rr.sr.sceneId,
        group = "Fleet",
        code = "RobotAreaMapNotFound",
        key = "RobotAreaMapNotFound-${rr.robotName}",
        message = lo("warningRobotAreaMapNotFound", listOf(main.currentMap, rr.config.groupId)),
        level = AlarmLevel.Error,
        args = listOf(rr.sr.sceneId, rr.robotName),
        tags = setOf(rr.tag, rr.sr.tag),
      )
      AlarmService.addItem(ai, ttl = 5000)
      return null
    }

    var cp = getClosestPoint(gac.pointNameMap.values, rr, main.x, main.y)
    if (cp != null) return cp

    val closePaths = getClosePaths(rr, main)
    if (!closePaths.isNullOrEmpty()) {
      val closePath = closePaths[0]
      val path = gac.pathKeyMap[closePath.pathKey]?.path
      if (path != null) {
        cp = if (closePath.pathPercentage > 0.5) path.toPointName else path.fromPointName
        return cp
      }
    }

    val ai = AlarmItem(
      sceneId = rr.sr.sceneId,
      group = "Fleet",
      code = "RobotNoStartPoint",
      key = "RobotNoStartPoint-${rr.robotName}",
      message = lo("warningNotFindStartPoint", listOf(rr.sr.config.closePointDistance)),
      level = AlarmLevel.Error,
      args = listOf(rr.sr.sceneId, rr.robotName),
      tags = setOf(rr.tag, rr.sr.tag),
    )
    AlarmService.addItem(ai, ttl = 5000)

    return null
  }

  /**
   * 计算最短路，排除锁定的点位。
   * 使用机器人组的地图。可以跨区域。
   */
  fun getShortestPathOfCrossAreas(
    rr: RobotRuntime,
    startPointName: String,
    endPointName: String,
    lockedPointNames: Set<String> = emptySet(),
  ): ShortestPath {
    val mapCache = rr.sr.mapCache
    val maps = mapCache.getAllAreaMapsOfGroup(rr.config.groupId) // 机器人组的所有区域的地图
    val alg = SceneMapAlg(maps, lockedPointNames) // 去掉已锁定的资源
    return alg.getShortestPath(startPointName, endPointName)
  }

  /**
   * 计算指定机器人从 fromLocation 点位到 toLocation 位置的成本。
   * 使用机器人组的地图。可以跨区域。
   * 大于等于 0 表示可达；不可达返回 -1 。如没有给出目标位置，无法估测成本 则抛异常。
   */
  fun getShortestPathCostOfCrossAreas(rr: RobotRuntime, fromLocation: String, toLocation: String): Double {
    val mapCache = rr.sr.mapCache

    val fromPointName = mapCache.getPointCacheByGroupAndLoc(rr, fromLocation)?.point?.name
    if (fromPointName.isNullOrBlank()) throw BzError("errNoFromPointName", fromLocation)

    val toPointName = mapCache.getPointCacheByGroupAndLoc(rr, toLocation)?.point?.name
    if (toPointName.isNullOrBlank()) throw BzError("errNoToPointName", toLocation)

    val sp = getShortestPathOfCrossAreas(rr, fromPointName, toPointName, emptySet())
    return if (sp.found) {
      sp.weight
    } else {
      // 不可达
      return NOT_ACHIEVABLE_COST
    }
  }

  /**
   * 计算最短路，排除锁定的点位。
   * 使用机器人组的地图。单区域。
   */
  fun getShortestPathOfArea(
    rr: RobotRuntime,
    areaId: Int,
    startPointName: String,
    endPointName: String,
    lockedPointNames: Set<String> = emptySet(),
  ): ShortestPath {
    val mapCache = rr.sr.mapCache
    val map = mapCache.areaById[areaId]?.groupedMaps?.get(rr.config.groupId) ?: return ShortestPath(false)
    val alg = SceneMapAlg(listOf(map), lockedPointNames) // 去掉已锁定的资源
    return alg.getShortestPath(startPointName, endPointName)
  }

  /**
   * 旅行商问题，传入多个点位，计算机器人依次前往各个点位最优路径顺序
   * @param locations 需要访问的站点ID列表
   * @param firstLocation 可选的第一个访问的站点ID
   * @return 优化后的站点访问顺序列表,如果返回为空列表代表计算失败
   */
  fun getTspPath(rr: RobotRuntime, locations: List<String>, firstLocation: String?): List<String> {
    // 处理边界情况
    if (locations.isEmpty()) return emptyList()
    if (locations.size == 1) return locations

    // 验证所有站点ID的有效性（必须转换为有效点位名称）
    val mapCache = rr.sr.mapCache
    val locationToPointNames = locations.associateWith { mapCache.getPointCacheByGroupAndLoc(rr, it)?.point?.name }
      .filterValues { it != null }
      .mapValues { it.value!! } // 过滤后安全转换

    val invalidLocations = locations.minus(locationToPointNames.keys)
    if (invalidLocations.isNotEmpty()) {
      logger.error("Invalid locations found: ${invalidLocations.joinToString(", ")}")
      return emptyList()
    }

    // 确定起点
    val startPointName = findBestStartPointNameForPlan(rr)
    if (startPointName == null) {
      logger.error("Unable to determine current position for robot: ${rr.robotName}")
      return emptyList()
    }

    // 处理指定的 firstLocation（如果存在）
    var firstPointName: String? = null
    if (firstLocation != null) {
      firstPointName = locationToPointNames[firstLocation]
      if (firstPointName == null) {
        logger.error("Invalid firstLocation: $firstLocation")
        return emptyList()
      }

      // 验证起点到 firstLocation 的路径是否存在
      val initialPath = getShortestPathOfCrossAreas(rr, startPointName, firstPointName, emptySet())
      if (!initialPath.found) {
        logger.error("No path from start to specified first location: $startPointName -> $firstPointName")
        return emptyList()
      }
    }

    // 初始化路径规划
    val visited = mutableSetOf<String>()
    val result = mutableListOf<String>()
    var currentPointName = startPointName

    // 将 firstLocation 添加到路径（如果存在）
    if (firstLocation != null && firstPointName != null) {
      result.add(firstLocation)
      visited.add(firstPointName)
      currentPointName = firstPointName
    }

    // 贪心算法：每次选择距离最近且可达的点位
    while (visited.size < locationToPointNames.size) {
      var minDistance = Double.MAX_VALUE
      var nextLocation: String? = null
      var nextPointName: String? = null

      // 找出最近的未访问点
      for ((location, pointName) in locationToPointNames) {
        if (pointName in visited) continue

        val path = getShortestPathOfCrossAreas(rr, currentPointName!!, pointName, emptySet())
        if (path.found && path.weight < minDistance) {
          minDistance = path.weight
          nextLocation = location
          nextPointName = pointName
        }
      }

      // 如果找不到下一个可达的点，返回空列表
      if (nextLocation == null || nextPointName == null) {
        val unreachable = locationToPointNames.entries
          .filter { (_, pointName) -> pointName !in visited }.mapIndexed { _, it -> it.key }
          .joinToString(", ")
        logger.error("Cannot find path from current point: $currentPointName to unvisited points: $unreachable")
        return emptyList()
      }

      // 更新路径和状态
      result.add(nextLocation)
      visited.add(nextPointName)
      currentPointName = nextPointName
    }

    logger.debug("TSP path planning successful: ${result.joinToString(" -> ")}")
    return result
  }

  private fun getClosestPoint(points: Collection<MapPointCache>, rr: RobotRuntime, x: Double, y: Double): String? {
    var min = Double.MAX_VALUE
    var minPoint: String? = null

    var cd = rr.sr.config.closePointDistance
    if (cd < 0) cd = .2

    for (pr in points) {
      val point = pr.point
      val d = GeoHelper.euclideanDistance(x, y, point.x, point.y)
      if (d < min && d <= cd) {
        min = d
        minPoint = point.name
      }
    }

    return minPoint
  }

  private fun getClosePoints(points: Collection<MapPointCache>, rr: RobotRuntime, x: Double, y: Double): List<String> {
    var cd = rr.sr.config.closePointDistance
    if (cd < 0) cd = .2
    val closePoints = mutableListOf<String>()
    for (pr in points) {
      val point = pr.point
      val d = GeoHelper.euclideanDistance(x, y, point.x, point.y)
      if (d <= cd) {
        closePoints.add(point.name)
      }
    }
    return closePoints
  }

  fun findClosetPointsForTrafficConditions(rr: RobotRuntime): List<String>? {
    val main = rr.selfReport?.main ?: return null
    if (main.x == null || main.y == null) return null
    val gac = rr.sr.mapCache.getAreaMapCacheByStandAndGroup(rr)
    if (gac == null) {
      val ai = AlarmItem(
        sceneId = rr.sr.sceneId,
        group = "Fleet",
        code = "RobotAreaMapNotFound",
        key = "RobotAreaMapNotFound-${rr.robotName}",
        message = lo("warningRobotAreaMapNotFound", listOf(main.currentMap, rr.config.groupId)),
        level = AlarmLevel.Error,
        args = listOf(rr.sr.sceneId, rr.robotName),
        tags = setOf(rr.tag, rr.sr.tag),
      )
      AlarmService.addItem(ai, ttl = 5000)
      return null
    }
    return getClosePoints(gac.pointNameMap.values, rr, main.x, main.y)
  }

  /**
   * 根据机器人当前位置计算最近的路径。用机器人自己的路径计算才准。可能有多个路径符合要求，返回的路径按距离从小到大排序。
   */
  fun getClosePaths(rr: RobotRuntime, main: RobotSelfReportMain): List<PathPosition>? {
    if (main.x == null || main.y == null) return null

    var cd = rr.sr.config.closePathDistance
    if (cd < 0) cd = 0.2

    val mapCache = rr.sr.mapCache
    // 机器人组在当前区域的地图
    val gac = mapCache.getAreaMapCacheByStandAndGroup(rr) ?: return null

    // 距离机器人最近的路径集
    val closePaths: MutableList<PathPosition> = ArrayList()
    for (pathCache in gac.pathKeyMap.values) {
      val path = pathCache.path
      var minDistance = Double.MAX_VALUE
      var minDistancePoint: CurvePoint2D? = null

      for (point in path.tracePoints) {
        val distance = GeoHelper.euclideanDistance(point.x, point.y, main.x, main.y)
        // 距离 <= cd 米，则认为点位在该路径上，并取距离最近的点位
        if (distance <= cd && distance < minDistance) {
          minDistance = distance
          minDistancePoint = point
          // 若是小于跳过的值则认为已经找到了最短距离，不再计算后续点
          if (distance < 1e-5) break
        }
      }

      if (minDistancePoint != null) {
        closePaths +=
          PathPosition(
            path.id,
            path.key,
            minDistancePoint.percentage,
            minDistance,
            path.fromPointName,
            path.toPointName,
          )
      }
    }
    closePaths.sortBy { it.pathDistance }
    return closePaths
  }
}