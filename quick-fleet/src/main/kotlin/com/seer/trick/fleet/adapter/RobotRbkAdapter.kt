package com.seer.trick.fleet.adapter

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.base.BaseCenter
import com.seer.trick.fleet.FleetLogger
import com.seer.trick.fleet.domain.CurrentLockResult
import com.seer.trick.fleet.domain.NavTaskResult
import com.seer.trick.fleet.domain.RobotBin
import com.seer.trick.fleet.domain.RobotConnectionType
import com.seer.trick.fleet.order.Req3066
import com.seer.trick.fleet.seer.RbkLog
import com.seer.trick.fleet.seer.SeerRbkTcpServer
import com.seer.trick.fleet.service.*
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonFileHelper
import com.seer.trick.helper.JsonHelper
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import java.io.File

/**
 * 场景机器人与 rbk 的适配器
 */
object RobotRbkAdapter {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 向机器人发请求。
   * 对于有 ret_code 字段的，尽量用 requestWithCodeCheck
   */
  fun request(rr: RobotRuntime, apiNo: Int, reqStr: String, timeout: Long = 5 * 1000): String {
    val noRbkMsgLog = rr.sr.config.noRbkMsgLog
    if (!noRbkMsgLog) RbkLog.log(rr.robotName, apiNo, "Request: $reqStr")
    val resStr = if (rr.config.simulated) {
      // 是仿真则忽略连接方式，直接采用 “机器人连接调度” 模式
      SeerRbkTcpServer.request(rr.robotName, apiNo, reqStr, timeout)
    } else {
      if (rr.config.connectionType == RobotConnectionType.FleetToRobot) {
        val rbkClient = rr.rbkClient ?: throw BzError("errCodeErr", "Robot $rr no rbk client")
        rbkClient.request(apiNo, reqStr)
      } else {
        SeerRbkTcpServer.request(rr.robotName, apiNo, reqStr, timeout)
      }
    }
    if (!noRbkMsgLog) RbkLog.log(rr.robotName, apiNo, "Response: $resStr")
    return resStr
  }

  /**
   * 向机器人发请求。如果 ret_code 不为 null 且不为 0 报错。
   */
  fun requestWithCodeCheck(
    rr: RobotRuntime,
    apiNo: Int,
    reqStr: String,
    timeout: Long = 5 * 1000,
  ): String {
    val resStr = request(rr, apiNo, reqStr, timeout)
    val n = JsonHelper.mapper.readTree(resStr)
    val resCode = n["ret_code"]?.asInt()
    val errMsg = n["err_msg"]?.asText()
    if (resCode != null && resCode != 0) throw BzError("errRbkReqResBadCode", resCode, errMsg)
    return resStr
  }

  /**
   * 与 RBK 通讯，标记多负载机器人某个库位有货
   */
  fun fillRobotRbkBin(
    rr: RobotRuntime,
    bin: RobotBin,
    containerId: String,
    desc: String = "",
  ): String {
    val req = mapOf(
      "goods_id" to containerId,
      "container_name" to bin.rbkBinId(rr).toString(),
      "desc" to desc,
    )
    val reqStr = JsonHelper.mapper.writeValueAsString(req)
    return request(rr, 6804, reqStr)
  }

  /**
   * 与 RBK 通讯，标记多负载机器人某个库位无货、为空
   */
  fun emptyRobotRbkBin(rr: RobotRuntime, bin: RobotBin): String {
    val req = mapOf("container_name" to bin.rbkBinId(rr).toString())
    val reqStr = JsonHelper.mapper.writeValueAsString(req)
    return request(rr, 6802, reqStr)
  }

  /**
   * 与 RBK 通讯，标记多负载机器人所有库位无货、为空
   */
  fun emptyAllRobotRbkBin(rr: RobotRuntime): String = requestWithCodeCheck(rr, 6803, "")

  /**
   * 与 RBK 通讯，标记单负载机器人为未载货状态
   * 但是如果目标机器人的载货状态受传感器状态控制，则此接口会无效。
   */
  fun setRobotToUnloadedStatus(rr: RobotRuntime): String = requestWithCodeCheck(rr, 6080, "")

  /**
   * 3066 指令导航
   */
  fun go3066(rr: RobotRuntime, moveList: List<Req3066>, timeout: Long = 10 * 1000): String {
    val reqStr = JsonHelper.mapper.writeValueAsString(mapOf("move_task_list" to moveList))
    FleetLogger.debug(
      module = "Robot",
      subject = "Send3066",
      sr = rr.sr,
      robotName = rr.robotName,
      msg = mapOf("reqStr" to reqStr),
    )
    return requestWithCodeCheck(rr, 3066, reqStr, timeout)
  }

  /**
   * 请求控制权。抛 BzError，但也可能抛其他异常。
   */
  fun requestControl(rr: RobotRuntime): String {
    val reqStr = JsonHelper.mapper.writeValueAsString(mapOf("nick_name" to RobotService.NICKNAME))
    return requestWithCodeCheck(rr, 4005, reqStr)
  }

  /**
   * 查询当前控制权所有者
   */
  fun queryControlCurrentLock(rr: RobotRuntime): CurrentLockResult {
    val resStr = requestWithCodeCheck(rr, 1060, "")
    val currentLockResult: CurrentLockResult = JsonHelper.mapper.readValue(resStr, jacksonTypeRef())
    return currentLockResult
  }

  /**
   * 查询路径导航 TODO 最佳 API 是
   * https://support.seer-group.com/d/1674676595492704257.html
   */
  fun query3066(rr: RobotRuntime, taskId: String): NavTaskResult? {
    val reqStr = JsonHelper.mapper.writeValueAsString(mapOf("task_ids" to listOf(taskId)))
    val resStr = requestWithCodeCheck(rr, 1110, reqStr)
    val n = JsonHelper.mapper.readTree(resStr)
    val taskStatusNode = n?.get("task_status_package")?.get("task_status_list")?.asIterable()?.first() ?: return null
    val status = taskStatusNode["status"]?.asInt() ?: 0
    val rTaskId = taskStatusNode["task_id"]?.asText() ?: ""
    return NavTaskResult(status, rTaskId)
  }

  /**
   * 查询导航结果
   */
  fun query3066List(rr: RobotRuntime, taskIds: List<String>): List<NavTaskResult>? {
    val reqStr = JsonHelper.mapper.writeValueAsString(mapOf("task_ids" to taskIds))
    val resStr = requestWithCodeCheck(rr, 1110, reqStr)
    val n = JsonHelper.mapper.readTree(resStr)
    val taskStatusNodes = n?.get("task_status_package")?.get("task_status_list") ?: return null
    val resultList = mutableListOf<NavTaskResult>()
    taskStatusNodes.forEach {
      val status = it["status"]?.asInt() ?: 0
      val rTaskId = it["task_id"]?.asText() ?: ""
      resultList.add(NavTaskResult(status, rTaskId))
    }
    return resultList
  }

  /**
   * 暂停导航
   */
  fun pauseNavTask(rr: RobotRuntime): String = requestWithCodeCheck(rr, 3001, "")

  /**
   * 继续导航
   */
  fun resumeNavTask(rr: RobotRuntime): String = requestWithCodeCheck(rr, 3002, "")

  /**
   * 取消路径导航 TODO 最佳 API 是
   */
  fun cancelNavTask(rr: RobotRuntime): String = requestWithCodeCheck(rr, 3003, "")

  /**
   * 申请对机器人的控制权
   */
  fun setRobotMaster(rr: RobotRuntime) {
    FleetLogger.info(
      module = "Robot",
      subject = "SetRobotMaster",
      sr = rr.sr,
      robotName = rr.robotName,
      msg = mapOf(),
    )
    val req = mapOf("nick_name" to RobotService.NICKNAME)
    val reqStr = JsonHelper.mapper.writeValueAsString(req)

    requestWithCodeCheck(rr, 4005, reqStr)
  }

  /**
   * 释放对机器人的控制权
   */
  fun unsetRobotMaster(rr: RobotRuntime) {
    FleetLogger.info(
      module = "Robot",
      subject = "UnsetRobotMaster",
      sr = rr.sr,
      robotName = rr.robotName,
      msg = mapOf(),
    )

    requestWithCodeCheck(rr, 4006, "")
  }

  /**
   * 4009 清除机器人告警信息（仅清除 error 级别的告警）
   */
  fun clearAlarm(rr: RobotRuntime) {
    requestWithCodeCheck(rr, 4009, "")
  }

  /**
   * 删除地图
   *
   * RBK 指令 4012
   * https://seer-group.feishu.cn/wiki/TsgowkwkUifHZtkXJyUcBC17n9f
   */
  fun deleteRobotMap(rr: RobotRuntime, mapName: String) {
    requestWithCodeCheck(rr, 4012, JsonHelper.mapper.writeValueAsString(mapOf("map_name" to mapName)))
    // TODO 故障处理
  }

  /**
   * 上传模型文件到机器人
   *
   * RBK 指令 4200
   * https://seer-group.feishu.cn/wiki/MPzKwfCQvizVS7kV9b3cvadlnUg
   */
  fun pushRobotModelToRobot(rr: RobotRuntime) {
    val group = rr.mustGetGroup()
    val model = group.robotModel ?: return

    val modelFile = File(BaseCenter.baseConfig.filesDir, model.path)
    val robotModel: Map<String, Any?> =
      JsonFileHelper.readJsonFromFile(modelFile) ?: throw BzError("errEmptyModelFile", "模型文件解析为空")
    requestWithCodeCheck(rr, 4200, JsonHelper.mapper.writeValueAsString(robotModel))
    // TODO 处理上传失败
  }

  /**
   * 切地图，返回 3066 taskId
   */
  fun switchMap(rr: RobotRuntime, map: String, smPointName: String): String {
    var pMap = map
    if (pMap.endsWith(".smap")) pMap = StringUtils.substringBeforeLast(pMap, ".smap")
    val taskId = IdHelper.oidStr()
    val req = Req3066(
      sourceId = "SELF_POSITION",
      id = "SELF_POSITION",
      taskId = taskId,
      operation = "Script",
      scriptName = "syspy/switchMap.py", // smapName.smap -> smapName
      scriptArgs = mapOf("map" to pMap, "switchPoint" to smPointName),
    )

    logger.info("Make robot [$rr] to switch map, map=$map, sm=$smPointName, taskId=$taskId, 3066=$taskId")
    go3066(rr, listOf(req), 120 * 1000)

    return taskId
  }

  /**
   * 切地图并等完成。如果 3066 成功，返回 true，否则返回 false。
   */
  fun switchMapAndAwait(
    rr: RobotRuntime,
    map: String,
    smPointName: String,
    isCancelled: () -> Boolean,
  ): Boolean {
    val taskId = switchMap(rr, map, smPointName)
    while (!isCancelled()) {
      val r = query3066(rr, taskId)
      // 终态就结束
      if (r?.isFinalStatus == true) {
        logger.info("Switch map 3066 done, taskId=$taskId, result=${r.status}")
        return r.status == NavTaskResult.NT_COMPLETED
      }
      Thread.sleep(1000)
    }
    return false
  }

  /**
   * 触发或者清除机器人的软急停状态。apiNo = 6004
   *
   * @param enable false: 触发软急停状态；true: 清除软急停状态。
   */
  fun setSoftEmc(rr: RobotRuntime, enable: Boolean) {
    // 高危操作，记录日志。
    FleetLogger.info(
      module = "Robot",
      subject = "setSoftEmc",
      sr = rr.sr,
      robotName = rr.robotName,
      msg = mapOf("reset" to enable),
    )
    val softEmcReq = mapOf("status" to enable)
    requestWithCodeCheck(rr, 6004, JsonHelper.mapper.writeValueAsString(softEmcReq))
  }

  /**
   * 查询机器人移动参数配置
   */
  fun queryRobotMoveConfig(rr: RobotRuntime): String {
    val req = mapOf(
      "param" to mapOf(
        "MoveFactory" to listOf(
          "MaxSpeed",
          "MaxBackSpeed",
          "MaxRot",
          "Load_MaxSpeed",
          "Load_MaxBackSpeed",
          "Load_MaxRot",
        ),
      ),
    )
    return requestWithCodeCheck(rr, 1400, JsonHelper.mapper.writeValueAsString(req))
  }
}