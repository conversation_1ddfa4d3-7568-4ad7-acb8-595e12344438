package com.seer.trick.fleet.order

import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.RobotService
import com.seer.trick.fleet.service.SceneRuntime

/**
 * TODO 这个辅助类计算耗时吗？
 */
class ParkAndChargeResourceHelper(private val sr: SceneRuntime) {

  /**
   * 所有被占用或者预定的充点电、停靠点的列表，支持停靠或充电的普通点也包括在内
   * value 为一个对象，包含占据/预定点位的机器人名，类型，以及运单 id
   * 类型有正在充电、预抢占充电、预停靠、机器人当前位置、业务目标点
   * pointName -> OccupiedInfo
   */
  private val allOccupiedPoints: MutableMap<String, OccupiedInfo> = HashMap()

  init {
    initialize()
  }

  /**
   * 根据机器人当前的位置、机器人运单的目标点，机器人的业务类型
   * 计算出可用的空闲的停靠点、充点电，并计算出所有运单当前步骤的目标点
   * 还计算出非空闲，被占用或者预占用的停靠点或充点电的点位列表，用于后续的业务控制
   * 停靠点的筛选规则
   * 1.是停靠点或者具有运行停靠属性的点
   * 2.该点位未被其他机器人或者运单预定，即不为其他机器人的目标停靠/充电点，也不是某个业务订单当前步骤的目标点
   * 3.当前点位上没有其他机器人 (可能出现一个极端情况是，机器人断联然后调度任务这个点没有机器人导致让其他机器人去这里停靠)
   * 充点电的筛选规则与停靠点一致
   * 当前被预定或占据的点包括：
   * 1.机器人的当前位置
   * 2.业务运单目标位置
   * 3.停靠或充电目标位置
   * 还存储预定或占据该点的类型，包括正在充电、预抢占充电、预停靠、机器人当前位置、业务目标点
   * 便于后续进行业务流程控制，譬如高优先级充电订单在没有可用充电点的情况下，挤走低优先级正在充电的机器人。
   *
   * 为了避免断联或者机器人上报延时导致的计算资源异常，给机器人增加 anticipatePointNames 字段
   * 当步骤完成时或者运单完成时，更新该字段，当机器人正在上报状态后，覆盖该字段，相应的还有一个问题，不可能无限制的信任这个字段
   * 假如机器人一直断联，并且被别人推走了呢？此时 todo anticipatePointNames 的异常处理机制
   */
  private fun initialize() {
    // 计算当前占用的点位，包括机器人的当前位置，停靠任务预定的点位，充电任务预定的点位
    val usedOrPreOccupiedPoints = calculateOccupiedPoints()
    // 获取所有运单当前步骤的目标点
    val orderTargets = calculateOrderTargets()
    // 生成所有被占据/预定的点位
    allOccupiedPoints.putAll(mergeOccupiedPoints(usedOrPreOccupiedPoints, orderTargets))
  }

  /**
   * 计算当前被占用或预占的点位
   * 包括机器人当前上报的位置、机器人预测的位置、机器人预定的停靠位置、机器人预定的充电位置
   */
  private fun calculateOccupiedPoints(): Map<String, OccupiedInfo> = sr.listRobots()
    .flatMap { rr ->
      listOfNotNull(
        rr.expectedOccupiedPointName?.takeIf { it.isNotEmpty() },
        rr.selfReport?.stand?.pointName?.takeIf { it.isNotEmpty() },
        rr.autoOrder?.pointName,
      ).map { it to OccupiedInfo(robotName = rr.robotName, occupiedKind = getOccupiedKind(rr)) }
    }.toMap()

  /**
   * 计算正在执行运单当前步骤的目标点，假如运单处于待执行状态，就是下一个步骤的位置
   */
  private fun calculateOrderTargets(): Map<String, OrderTarget> =
    sr.listOrders().filter { it.isExecutingOrder() }.mapNotNull { or ->
      or.getCurrentOrWillStep()?.let { step ->
        sr.mapCache.getPointCacheByLoc(sr, step.location)?.let { pc ->
          pc.point.name to OrderTarget(
            orderId = or.order.id,
            kind = or.order.kind,
            currentTargetPoint = pc.point.name,
            occupiedByRobot = or.order.actualRobotName!!,
          )
        }
      }
    }.toMap()

  /**
   * 合并已占用点和运单目标点
   */
  private fun mergeOccupiedPoints(
    usedOrPreOccupiedPoints: Map<String, OccupiedInfo>,
    orderTargets: Map<String, OrderTarget>,
  ): Map<String, OccupiedInfo> = usedOrPreOccupiedPoints + orderTargets.mapValues { (_, target) ->
    OccupiedInfo(
      robotName = target.occupiedByRobot,
      occupiedKind = getOccupiedKindByOrderKind(target.kind),
      orderId = target.orderId,
    )
  }

  /**
   * 检查当前机器人在某个站点是否会与已占据/已预定的机器人发生碰撞
   * 调用这个函数前需要自行检查当前机器人当对应位置是否可达，避免不可达以及跨楼层的情况
   * 碰撞检测时的方向是按照点位的方向
   * todo 跨楼层的处理
   * @param rr 机器人
   * @param pointName 目标站点名称
   * @return 返回失败的原因，如果是因为碰撞则返回碰撞的点位名称，如果是因为点位不存在则返回对应点位不存在
   */
  @Synchronized
  fun checkParkAndChargeCollision(rr: RobotRuntime, pointName: String): String? {
    val robotPoint = rr.sr.mapCache.getPointCacheByRobotAndName(rr, pointName) ?: return "$pointName Not Exist"
    val robotCm = RobotService.getCollisionModel(rr)
    // 这里需要排除掉对应的区域
    val areaIdByPointName = getAreaIdByPointName(rr, pointName)
    // 在这里实现具体的碰撞检测逻辑
    // 检查机器人从当前位置移动到指定点位是否会与其他已占用的点发生碰撞
    // 如果会发生碰撞,返回true,否则返回false

    for ((occupiedPointName, occupiedInfo) in allOccupiedPoints) {
      // 有些老运单是旧的机器人执行的，运单还存在，但机器人已经被移除场景了，occupiedRobot 就为 null 了
      val occupiedRr = sr.robots[occupiedInfo.robotName] ?: continue
      if (occupiedRr.robotName == rr.robotName) continue
      // 这种情况发生一般是地图不一致
      val occupiedPoint = occupiedRr.sr.mapCache.getPointCacheByRobotAndName(occupiedRr, occupiedPointName) ?: continue
      val occupiedCm = RobotService.getCollisionModel(occupiedRr)
      // 获取已占用点的区域ID
      val areaIdByOccupiedPoint = getAreaIdByPointName(occupiedRr, occupiedPointName)
      // 如果目标点和已占用点不在同一个区域，则跳过该点的碰撞检测
      if (areaIdByPointName != areaIdByOccupiedPoint) {
        continue // 仅跳过当前点的碰撞检测，继续检查其他点
      }
      // 判断是否需要考虑到点角度
      val (allocatePoint, newOccupiedPoint) =
        if (sr.config.parkingCollisionCheckMode == CollisionCheckMode.None) {
          Pair(robotPoint.point.copy(direction = null), occupiedPoint.point.copy(direction = null))
        } else {
          Pair(robotPoint.point, occupiedPoint.point)
        }
      // 如果在同一区域，执行碰撞检测
      if (checkCollision(allocatePoint, robotCm, newOccupiedPoint, occupiedCm)) return occupiedPoint.point.name
    }
    return null
  }

  /**
   * 检测膨胀，判断两个机器人是否会发生碰撞
   * TODO 考虑旋转
   */
  private fun checkCollision(
    robotPoint: MapPoint,
    robotModel: Polygon,
    occupiedPoint: MapPoint,
    occupiedModel: Polygon,
  ): Boolean {
    val resRect = GeoHelper.translateThenRotate(
      robotModel,
      robotPoint.x,
      robotPoint.y,
      robotPoint.x,
      robotPoint.y,
      robotPoint.direction ?: 0.0,
    )
    val occupiedPointRect = GeoHelper.translateThenRotate(
      occupiedModel,
      occupiedPoint.x,
      occupiedPoint.y,
      occupiedPoint.x,
      occupiedPoint.y,
      occupiedPoint.direction ?: 0.0,
    )
    return GeoHelper.isPolygonsIntersecting(resRect, occupiedPointRect)
  }

  private fun getOccupiedKind(rr: RobotRuntime): OccupiedKind = if (rr.selfReport?.main?.charging == true) {
    OccupiedKind.Charging
  } else {
    OccupiedKind.Stand
  }

  /**
   * 根据运单的 kind 获取对应的占据站点的 kind
   */
  private fun getOccupiedKindByOrderKind(kind: OrderKind?): OccupiedKind = when (kind) {
    OrderKind.Parking -> OccupiedKind.PrePark
    OrderKind.Charging -> OccupiedKind.PreCharge
    OrderKind.Business, OrderKind.IdleAvoid -> OccupiedKind.Business
    else -> OccupiedKind.Stand
  }

  /**
   * 获取运行中的 order 除了 Withdrawn 状态
   */
  private fun OrderRuntime.isExecutingOrder(): Boolean = order.status == OrderStatus.Allocated ||
    order.status == OrderStatus.Executing ||
    order.status == OrderStatus.Pending

  /**
   * 获取机器人当前运行的步骤，对于已运行完但还没有执行下一个步骤时，返回下一个步骤
   */
  private fun OrderRuntime.getCurrentOrWillStep(): TransportStep? = when (order.status) {
    OrderStatus.ToBeAllocated, OrderStatus.Allocated -> steps.getOrNull(0)
    OrderStatus.Executing -> steps.getOrNull(order.currentStepIndex)
    OrderStatus.Pending -> steps.getOrNull(
      if (order.currentStepIndex == steps.size - 1) {
        order.currentStepIndex
      } else {
        order.currentStepIndex + 1
      },
    )

    else -> null
  }

  /**
   * 找到这个点位对应的区域是什么
   */
  private fun getAreaIdByPointName(rr: RobotRuntime, pointName: String): Int? {
    val cache = rr.sr.mapCache
    for (ar in cache.areaById.values) {
      val gm = ar.groupedMaps[rr.config.groupId] ?: continue
      val point = gm.pointNameMap[pointName]?.point
      if (point != null) return gm.areaId
    }
    return null
  }

  /**
   * 列出有效的停靠点
   */
  fun listAvailableParkingPoints(rr: RobotRuntime): List<String> {
    val parkingPoints = sr.mapCache.listParkAllowedPointsByRobot(rr).map { it.point.name }
    return parkingPoints.filterNot { allOccupiedPoints.containsKey(it) }
  }

  /**
   * 列出有效的充电点
   */
  fun listAvailableChargingPoints(rr: RobotRuntime): List<String> {
    val chargingPoints = sr.mapCache.listChargeAllowedPointsByRobot(rr).map { it.point.name }
    return chargingPoints.filterNot { allOccupiedPoints.containsKey(it) }
  }
}

/**
 * 运单目标点
 * @param orderId 运单id
 * @param kind 运单类型
 * @param currentTargetPoint 当前运单的目标点，当前步骤的
 * @param occupiedByRobot 占据机器人名
 */
data class OrderTarget(
  val orderId: String,
  val kind: OrderKind,
  val currentTargetPoint: String,
  val occupiedByRobot: String,
)

/**
 * 点位占据的信息
 * @param robotName 机器人名
 * @param occupiedKind 占据类型
 * @param orderId 占据的订单id，如果不是运单的目标点占用，则为null
 */
data class OccupiedInfo(
  val robotName: String, // 机器人名
  val occupiedKind: OccupiedKind,
  val orderId: String? = null,
)

enum class OccupiedKind {
  Charging, // 正在充电
  PreCharge, // 预抢占充电
  PrePark, // 预停靠
  Stand, // 机器人当前位置
  Business, // 业务目标点
}