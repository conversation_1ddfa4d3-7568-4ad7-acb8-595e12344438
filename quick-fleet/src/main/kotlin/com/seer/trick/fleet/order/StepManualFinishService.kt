package com.seer.trick.fleet.order

import com.seer.trick.BzError
import com.seer.trick.I18N.lo
import com.seer.trick.fleet.service.SceneRuntime

object StepManualFinishService {

  /**
   * 人工完成步骤
   */
  fun manualFinishingStep(sr: SceneRuntime, or: OrderRuntime) {
    val robotName = or.order.actualRobotName

    if (!robotName.isNullOrBlank()) {
      val rr = sr.robots[robotName] ?: throw BzError("errManualFinishStepsButNoRobot", or.id, robotName)

      val sec = rr.executingStep ?: throw BzError("errManualFinishStepNoExecutingSelected", robotName, or.id)

      // 直接用一套逻辑
      StepExecuteService.afterStepOk(sec)
    }
  }

  /**
   * 运单步骤失败后的告警信息
   */
  fun buildStepErrMsg(sr: SceneRuntime, or: OrderRuntime): String {
    val orderId = or.id
    // 运单 XXX 第 N 步故障。
    var errMsg = lo("errOrderStepFailed", listOf(or.order.actualRobotName, orderId, or.order.currentStepIndex + 1))

    // 故障原因
    val faultReason = or.order.faultReason
    errMsg += if (faultReason != null) lo("errFaultReason", listOf(faultReason)) else lo("errFaultReasonUnknown")

    // 推荐的操作
    errMsg += lo("errFaultOptions")

    // 容器号
    val containerId = or.order.containerId
    if (!containerId.isNullOrBlank()) {
      errMsg += lo("errFaultContainer", listOf(containerId))
    }

    // 人工完成的提示
    buildManualFinishTip(sr, orderId)?.let { errMsg += it }
    return errMsg
  }

  /**
   * 人工完成步骤的提示
   */
  private fun buildManualFinishTip(sr: SceneRuntime, orderId: String): String? {
    val or = sr.mustGetOrderById(orderId)
    val robotName = or.order.actualRobotName ?: return null
    val rr = sr.robots[robotName] ?: return null
    val step = or.getCurrentStep() ?: return null

    // 单负载的机器人，不需要提示背篓位置
    if (rr.bins.size <= 1) {
      return if (step.forLoad) {
        lo("manualLoadTip", listOf(step.location))
      } else if (step.forUnload) {
        lo("manualUnloadTip", listOf(step.location))
      } else {
        null
      }
    }

    val bin = rr.bins.firstOrNull { it.orderId == orderId } ?: return null // 机器人身上的背篓
    return if (step.forLoad) {
      if (bin.isPlusOne(rr)) {
        lo("manualLoadTip1", listOf(step.location))
      } else {
        lo("manualLoadTip2", listOf(step.location, bin.binNo(rr), bin.index))
      }
    } else if (step.forUnload) {
      if (bin.isPlusOne(rr)) {
        lo("manualUnloadTip1", listOf(step.location))
      } else {
        lo("manualUnloadTip2", listOf(bin.binNo(rr), bin.index, step.location))
      }
    } else {
      null
    }
  }

  /**
   * 是否允许人工完成
   */
  fun allowManualFinish(sr: SceneRuntime, or: OrderRuntime): Boolean =
    or.isExecuting() && !robotLoadingContainerOversize(sr, or)

  /**
   * 货物比机器人大时，故障后不提供人工完成功能
   *
   * 步骤故障，正常是提供人工完成的，人工把货物放机器人身上。
   * 但对于货物比机器人大的情况（比如顶升车顶料架），人工把货放上，但碰撞模型可能出错，所以禁止这么干。
   */
  private fun robotLoadingContainerOversize(sr: SceneRuntime, or: OrderRuntime): Boolean {
    // // 不是取货失败的，可以人工完成
    // if (or.getCurrentStep()?.forLoad == false) return false
    val robotName = or.order.actualRobotName
    if (robotName.isNullOrBlank()) return false
    val rr = sr.robots[robotName] ?: return false
    return rr.mustGetGroup().containerOversize
  }
}