package com.seer.trick.fleet.traffic.distributed.service

import com.seer.trick.I18N
import com.seer.trick.base.alarm.AlarmAction
import com.seer.trick.base.alarm.AlarmItem
import com.seer.trick.base.alarm.AlarmLevel
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.order.OrderRuntime
import com.seer.trick.fleet.order.OrderService
import com.seer.trick.fleet.service.*
import com.seer.trick.fleet.traffic.distributed.block.BlockItem
import com.seer.trick.fleet.traffic.distributed.block.BlockService
import com.seer.trick.fleet.traffic.distributed.context.ContextManagerService
import com.seer.trick.fleet.traffic.distributed.context.domain.RobotContext
import com.seer.trick.fleet.traffic.distributed.context.domain.RobotStatus
import com.seer.trick.fleet.traffic.distributed.context.domain.TrafficStatus
import com.seer.trick.fleet.traffic.distributed.dispatch.ScheduleManagerService
import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper
import com.seer.trick.fleet.traffic.distributed.lock.LockService
import com.seer.trick.fleet.traffic.distributed.lock.LockType
import com.seer.trick.fleet.traffic.distributed.lock.base.RobotContainerSpaceLock
import com.seer.trick.fleet.traffic.distributed.lock.domain.LockRequest
import com.seer.trick.fleet.traffic.distributed.lock.graph.*
import com.seer.trick.fleet.traffic.distributed.lock.graph.Circle
import com.seer.trick.fleet.traffic.distributed.lock.graph.Polygon
import com.seer.trick.fleet.traffic.distributed.lock.model.Cell
import com.seer.trick.fleet.traffic.distributed.lock.model.Layer
import com.seer.trick.fleet.traffic.distributed.lock.model.SpaceLock
import com.seer.trick.fleet.traffic.distributed.map.Point
import com.seer.trick.fleet.traffic.distributed.map.PosType
import com.seer.trick.fleet.traffic.distributed.map.Position
import com.seer.trick.fleet.traffic.distributed.plan.PlanPathService
import com.seer.trick.fleet.traffic.distributed.plan.RequestInfo
import com.seer.trick.fleet.traffic.distributed.plan.ResStatus
import com.seer.trick.fleet.traffic.distributed.plan.model.PathAction
import com.seer.trick.fleet.traffic.distributed.prevent.PreventManager
import com.seer.trick.fleet.traffic.distributed.service.model.ContainerModel
import com.seer.trick.fleet.traffic.distributed.service.model.PathAndDir
import com.seer.trick.fleet.traffic.distributed.service.model.PathResult
import com.seer.trick.fleet.traffic.distributed.service.model.RobotStatusReport
import com.seer.trick.helper.IdHelper
import io.javalin.util.NamedThreadFactory
import org.slf4j.LoggerFactory
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit

/**
 *  交管服务 总入口
 *
 * */
object DistributedTrafficService {

  private val logger = LoggerFactory.getLogger(javaClass)

  private val trafficThreadPool = ThreadPoolExecutor(
    8,
    32,
    60L,
    TimeUnit.MILLISECONDS,
    LinkedBlockingQueue(516),
    NamedThreadFactory("external-traffic"),
  )

  fun init() {
    ScheduleManagerService.start()
    PreventManager.start()
  }

  /**
   *  查询场景信息
   *  调用外部的接口都从这里获取，为了今后若有修改，可同一处理
   * */
  fun findSceneBySceneId(sceneId: String): SceneRuntime = SceneService.mustGetSceneById(sceneId)

  /**
   *  交管告警
   * */
  fun trafficAlarm(
    sceneId: String,
    robotName: String,
    code: String,
    args: List<String>,
    buttons: List<String> = listOf(),
    error: Boolean = true,
  ) {
    var actions: List<AlarmAction>? = null
    if (buttons.isNotEmpty()) {
      actions = buttons.map {
        AlarmAction(label = I18N.lo(it))
      }
    }
    val ai = AlarmItem(
      sceneId = sceneId,
      group = "Fleet",
      code = code,
      key = "$code-$robotName",
      level = if (error) AlarmLevel.Error else AlarmLevel.Warning,
      message = I18N.lo(code, args),
      args = listOf(sceneId, robotName),
      actions = actions,
    )
    AlarmService.addItem(ai, ttl = 3000) { actionIndex, itemArgs ->
      if (ai.code == "T00020010") {
        val sceneId = itemArgs[0] as String
        val scene = this.findSceneBySceneId(sceneId)
        val rr = scene.mustGetRobot(itemArgs[1] as String)
        scene.trafficService.resetByRobot(rr)
      }
    }
  }

  /**
   * 提供外部调用路径规划接口，仅支持点到点的规划
   * */
  fun planPath(
    robotName: String,
    groupName: String,
    mapName: String,
    sceneId: String,
    startX: Double,
    startY: Double,
    startPoint: String,
    direction: Double,
    targetPoint: String,
    targetDir: Double?,
    containerName: String?,
    containerStartDirection: Double?,
    containerTargetDirection: Double?,
  ): PathResult {
    val response = PlanPathService.planPath(
      RequestInfo(
        robotName = robotName,
        orderId = "default",
        stepId = "default",
        stepIndex = 0,
        sceneId = sceneId,
        mapName = mapName,
        groupName = groupName,
        startX = startX,
        startY = startY,
        start = startPoint,
        target = targetPoint,
        startType = PositionType.Point,
        startAngle = AngleHelper.convertAngle(direction),
        targetAngle = if (targetDir == null) null else AngleHelper.convertAngle(targetDir),
        containerType = containerName,
        containerStartAngle = if (containerName == null) {
          AngleHelper.ERROR_ANGLE
        } else {
          AngleHelper.convertAngle(
            containerStartDirection,
          )
        },
        containerTargetAngle = if (containerName == null) {
          AngleHelper.ERROR_ANGLE
        } else {
          AngleHelper.convertAngle(
            containerTargetDirection,
          )
        },
      ),
    )
    val pathAndDir = response.path.map {
      PathAndDir(
        fromPointName = it.start.pointName,
        toPointName = it.target.pointName,
        index = it.index,
        robotEnterTheta = it.robotInHeading,
        robotExitTheta = it.robotOutHeading,
        loadEnterTheta = if (containerName != null) {
          it.containerInHeading
        } else {
          null
        },
        loadExitTheta = if (containerName != null) {
          it.containerOutHeading
        } else {
          null
        },
      )
    }
    return PathResult(
      success = response.status == ResStatus.SUCCESS,
      pathAndDirs = pathAndDir,
      message = I18N.lo(response.code, response.args),
    )
  }

  // 查询单个机器人的状态信息
  fun queryRobotInfo(context: RobotContext): RobotStatusReport? {
    val sr = this.findSceneBySceneId(context.sceneId)
    val robotRuntime = sr.robots[context.robotName] ?: return null // 系统中不存在此机器人信息
    return queryRobotStatusReport(context.robotName, robotRuntime)
  }

  /**
   * 查询所有机器人的状态信息
   */
  fun queryAllRobotInfo(): List<RobotStatusReport> {
    val robotStatus: MutableList<RobotStatusReport> = mutableListOf()
    val contexts = ContextManagerService.queryAllRobotContext()
    if (contexts.isEmpty()) return emptyList()

    val contextMap: MutableMap<String, MutableList<RobotContext>> = mutableMapOf()
    contexts.forEach {
      val robotContexts = contextMap[it.sceneId] ?: mutableListOf()
      robotContexts.add(it)
      contextMap[it.sceneId] = robotContexts
    }

    for ((sceneId, robotContexts) in contextMap) {
      val sr = this.findSceneBySceneId(sceneId)
      for (context in robotContexts) {
        val robotRuntime = sr.robots[context.robotName]
        if (robotRuntime == null) {
          logger.error("${context.robotName} robot runtime is null")
          continue
        }
        robotStatus.add(queryRobotStatusReport(context.robotName, robotRuntime))
      }
    }
    return robotStatus
  }

  // 查询机器人上报数据 todo 期望这些数据的封装由上层来做，这边是通过接口直接取
  fun queryRobotStatusReport(robotName: String, rr: RobotRuntime): RobotStatusReport {
    var robotStatus = RobotStatus.IDLE
    val group = rr.mustGetGroup()
    if (group.disabled) {
      logger.error("$robotName| robot group status is disabled true")
      robotStatus = RobotStatus.OFFLINE // 停用默认不在线
    }
    // 是否在线
    if (!RobotService.isOnline(rr)) {
      logger.error("$robotName| robot is offline")
      robotStatus = RobotStatus.OFFLINE
    }
    // 是否有控制权
    if (rr.selfReport?.main?.controlledByFleet != true) {
      logger.error("$robotName| robot is no controller")
      robotStatus = RobotStatus.OFFLINE // 停用默认不在线
    }
    // 在充电中
//    if (rr.selfReport?.main?.charging == true) {
//      logger.info("$robotName| robot is charging")
//      robotStatus = RobotStatus.LOCK
//    }
    if (robotStatus == RobotStatus.IDLE) {
      robotStatus = adapterStatus(rr.getExecuteStatus())
    }

    val stand = rr.selfReport?.stand
    if (stand?.areaId == null) {
      logger.error("$robotName run time stand is null or area id is null")
      return RobotStatusReport(
        robotName = robotName,
        groupName = group.name,
        mapName = "",
        robotAngle = AngleHelper.ERROR_ANGLE,
        status = RobotStatus.ERROR,
      )
    }
    val containerAngle = if (rr.selfReport?.main?.loadRelations != null) {
      adapterAngle(rr.selfReport?.main?.loadRelations?.get(0)?.direction)
    } else {
      AngleHelper.ERROR_ANGLE
    }
    val robotReport = RobotStatusReport(
      robotName = robotName,
      mapName = stand.areaId.toString(),
      groupName = group.name,
      index = rr.sr.trafficService.queryRobotIndex(robotName), // todo 待实现
      x = stand.x,
      y = stand.y,
      pointName = stand.pointName,
      robotAngle = adapterAngle(stand.theta),
      containerAngle = containerAngle,
      status = robotStatus, // todo 待实现
    )
    logger.info(
      "$robotName self report| ${robotReport.index}| ${robotReport.pointName} (${robotReport.x}, ${robotReport.y}) ," +
        "r:${robotReport.robotAngle} | c:${robotReport.containerAngle} | ${robotReport.mapName}, " +
        "${robotReport.groupName}| ${robotReport.status}",
    )
    return robotReport
  }

  // 角度转换
  private fun adapterAngle(direction: Double?): Int {
    if (direction == null) {
      return AngleHelper.ERROR_ANGLE
    }
    return AngleHelper.convertAngle(direction)
  }

  /**
   *  页面展示信息
   *  1、路径信息
   *  2、锁闭信息
   *  3、阻挡信息
   * */
  fun buildTrafficResourceMessage(sceneId: String): Map<String, RobotShowTrafficMessage> {
    val allContext = ContextManagerService.queryAllRobotContext().filter { it.sceneId == sceneId }
    val pathMap: MutableMap<String, PathResource> = mutableMapOf()
    allContext.forEach { context ->
      if (context.mapName.isEmpty()) return@forEach
      pathMap[context.robotName] = buildTrafficPath(context)
    }

    val allSpaces = LockService.querySceneSpaceLock(sceneId)
    val spaceMap: MutableMap<String, List<SpaceResource>> = mutableMapOf()
    allSpaces.values.forEach { spaceList ->
      spaceList.forEach { space ->
        val key = space.getKey()
        val split = key.split("-><-")
        val type = split[0]
        if (type == LockType.ROBOT.name || type == LockType.EXTERNAL.name) {
          // 构建展示信息
          spaceMap[split[2]] = buildTrafficSpace(space)
        }
      }
    }

    val blocks = BlockService.findAllBlocks()
    val blockMap: MutableMap<String, BlockedMessage> = mutableMapOf()
    blocks.forEach { (name, block) ->
      blockMap[name] = buildTrafficBlock(block)
    }

    val robotsShow: MutableMap<String, RobotShowTrafficMessage> = mutableMapOf()

    pathMap.forEach { (name, path) ->
      val resources = spaceMap.remove(name)
      robotsShow[name] = RobotShowTrafficMessage(
        pathResource = path,
        spaceResources = resources ?: listOf(),
        blockedMessage = blockMap[name],
      )
    }
    if (spaceMap.isNotEmpty()) {
      spaceMap.forEach { (name, resource) ->
        robotsShow[name] = RobotShowTrafficMessage(
          pathResource = PathResource(listOf(), listOf()),
          spaceResources = resource,
          blockedMessage = blockMap[name],
        )
      }
    }
    return robotsShow
  }

  /**
   *  发送请求，构建空闲移动运单
   * */
  fun sendMove(robot: String, point: Point) {
    // 创建订单
    try {
      val robotContext = ContextManagerService.queryRobotContext(robot)
      val sr = findSceneBySceneId(robotContext.sceneId)

      // 提前检查机器人是否有运单
      val rr = sr.mustGetRobot(robot)
      if (rr.orders.isNotEmpty()) {
        return
      }

      val orderId = OrderService.generateOrderId() + "A"
      val steps = mutableListOf(
        TransportStep(
          id = IdHelper.oidStr(),
          orderId = orderId,
          stepIndex = 0,
          status = StepStatus.Executable,
          location = point.pointName,
          withdrawOrderAllowed = true,
        ),
      )
      val order = TransportOrder(
        id = orderId,
        status = OrderStatus.Allocated,
        keyLocations = listOf(steps[0].location),
        stepNum = 1,
        expectedRobotNames = listOf(robot),
        actualRobotName = robot,
        kind = OrderKind.IdleAvoid,
        stepFixed = true,
        sceneId = sr.sceneId,
      )

      logger.info("$robot create idle avoid order, orderId: $orderId, target point is ${point.pointName}")
      trafficThreadPool.submit {
        OrderService.createIdleAvoidOrder(sr, robot, OrderRuntime(order, steps), point.pointName)
      }
    } catch (e: Exception) {
      logger.error("send move is failed $e")
    }
  }

  /**
   *  路径下发接口
   * */
  fun pushPath(context: RobotContext, paths: MutableList<PathAction>): Boolean {
    val sr = findSceneBySceneId(context.sceneId)
    val rr = sr.robots[context.robotName]
    if (rr == null) {
      logger.error("${context.robotName} can not found in scene")
      return false
    }
    val req2 = context.request.request!!
    val robotMoves: MutableList<MoveActionRuntime> = mutableListOf()
    // 构建下发路径
    paths.forEach {
      robotMoves.add(
        MoveActionRuntime(
          req2.task?.id!!,
          MoveActionReq(
            fromPointName = it.start.pointName,
            toPointName = it.target.pointName,
            index = it.index,
            robotEnterTheta = AngleHelper.degreesToRadians(it.robotInHeading),
            robotExitTheta = AngleHelper.degreesToRadians(it.robotOutHeading),
            loadEnterTheta = if (context.baseDomain.isLoading()) {
              AngleHelper.degreesToRadians(it.containerInHeading)
            } else { // todo 应该为 null
              null
            },
            loadExitTheta = if (context.baseDomain.isLoading()) {
              AngleHelper.degreesToRadians(it.containerOutHeading)
            } else { // todo 应该为 null
              null
            },
            finalAction = it.stop(),
            orderId = req2.orderId,
            stepId = req2.stepId,
            stepIndex = req2.stepIndex,
            trafficMethod = TrafficMethod.Distributed,
          ),
        ),
      )
    }
    if (context.state != TrafficStatus.RUNNING || context.baseDomain.status != RobotStatus.WORK) {
      logger.error("${context.robotName} is state is ${context.state} or base state is ${context.baseDomain.status}")
      return false
    }
    logger.info("${context.robotName} |push path: $robotMoves") // todo 记录具体信息
    trafficThreadPool.submit {
      RobotMoveService.appendActions(rr, req2.task!!, robotMoves)
    }

    return true
  }

  private fun convert(type: GraphType): SpaceResourceType = when (type) {
    GraphType.RECTANGLE -> SpaceResourceType.Rect
    GraphType.CIRCLE -> SpaceResourceType.Circle
    GraphType.POLYGON -> SpaceResourceType.Polygon
    else -> SpaceResourceType.Rect
  }

  // 状态转换
  private fun adapterStatus(status: RobotExecuteStatus): RobotStatus = when (status) {
    RobotExecuteStatus.Idle -> RobotStatus.IDLE
    RobotExecuteStatus.Moving -> RobotStatus.WORK
    RobotExecuteStatus.Failed -> RobotStatus.ERROR
  }

  fun dispose() {
    ScheduleManagerService.stop()
    PreventManager.stop()
  }

  fun addContainerTypes(containerTypes: List<SceneContainerType>, groups: List<RobotGroup>, sceneId: String) {
    val groupMap = groups.associateBy { it.id }
    for (containerType in containerTypes) {
      val expLegLength = containerType.length - containerType.legLength
      val expLegWidth = containerType.width - containerType.legWidth
      val groupCenterDistance = mutableMapOf<String, Double>()
      containerType.groupCenterDistances.forEach { (t, u) ->
        groupMap[t]?.let {
          groupCenterDistance[it.name] = u
        }
      }
      val model = ContainerModel(
        sceneId = sceneId,
        typeName = containerType.name,
        length = containerType.outerLength,
        width = containerType.outerWidth,
        height = containerType.height,
        radius = containerType.radius,
        legHeight = containerType.legHeight,
        expLegLength = expLegLength,
        expLegWidth = expLegWidth,
        narrowDir = if (containerType.outerLength > containerType.outerWidth) 0 else AngleHelper.RIGHT_ANGLE,
        groupCenterDistance = groupCenterDistance,
      )
      RobotContainerSpaceLock.addContainerModel(model)
    }
  }

  private fun calculateNarrowDir(points: MutableList<Vector>): Int {
    if (points.size == 4) {
      if ((points[1].distanceTo(points[2]) - points[0].distanceTo(points[1])) > 0.01) {
        return AngleHelper.RIGHT_ANGLE
      }
    }
    return 0
  }

  fun updateContainerTypes(containerType: SceneContainerType, groups: List<RobotGroup>, sceneId: String) {
    val groupMap = groups.associateBy { it.id }
    val expLegLength = containerType.length - containerType.legLength
    val expLegWidth = containerType.width - containerType.legWidth
    val groupCenterDistance = mutableMapOf<String, Double>()
    containerType.groupCenterDistances.forEach { (t, u) ->
      groupMap[t]?.let {
        groupCenterDistance[it.name] = u
      }
    }
    val model = ContainerModel(
      sceneId = sceneId,
      typeName = containerType.name,
      length = containerType.outerLength,
      width = containerType.outerWidth,
      height = containerType.height,
      radius = containerType.radius,
      legHeight = containerType.legHeight,
      expLegLength = expLegLength,
      expLegWidth = expLegWidth,
      narrowDir = if (containerType.outerLength > containerType.outerWidth) 0 else AngleHelper.RIGHT_ANGLE,
      groupCenterDistance = groupCenterDistance,
    )
    RobotContainerSpaceLock.updateContainerModel(model)
  }

  /**
   *  构建单个机器人展示数据
   * */
  fun showTrafficResourceMessageByRobotName(sceneId: String, robotName: String): RobotShowTrafficMessage? {
    try {
      val context = ContextManagerService.queryRobotContext(robotName)
      val pathResource = buildTrafficPath(context)

      val key: String = LockType.ROBOT.toString() + "-><-" + context.groupName + "-><-" + context.robotName
      val spaceLock = LockService.querySpaceLockByKey(key, sceneId, context.mapName)
      val spaceResources = spaceLock?.let { buildTrafficSpace(it) }

      val ek: String = LockType.EXTERNAL.toString() + "-><-" + context.groupName + "-><-" + context.robotName
      val externalLock = LockService.querySpaceLockByKey(ek, sceneId, context.mapName)
      val externalResources = externalLock?.let { buildTrafficSpace(it) }

      val spaces = mutableListOf<SpaceResource>()
      if (spaceResources != null && externalResources != null) {
        spaces.addAll(spaceResources)
        spaces.addAll(externalResources)
      } else if (spaceResources == null && externalResources != null) {
        spaces.addAll(externalResources)
      } else if (spaceResources != null) {
        spaces.addAll(spaceResources)
      }

      val blockItems = BlockService.findBlock(robotName)
      val blockedMessage = buildTrafficBlock(blockItems)

      return RobotShowTrafficMessage(pathResource, spaces, blockedMessage)
    } catch (e: Exception) {
      logger.error("showTrafficResourceMessageByRobotName error $e")
      return null
    }
  }

  private fun buildTrafficPath(context: RobotContext): PathResource {
    val path = context.plan.path
    return if (path.isEmpty()) {
      PathResource(listOf(), listOf())
    } else {
      val index = context.plan.index
      val travelledPointNames: MutableList<String> = mutableListOf()
      val unTravelPointNames: MutableList<String> = mutableListOf()
      travelledPointNames.add(path[0].start.pointName)
      for (action in path) {
        if (action.index < index) {
          if (action.isRotate()) continue
          if (travelledPointNames.isEmpty()) {
            travelledPointNames.add(action.start.pointName)
          }
          travelledPointNames.add(action.target.pointName)
        } else {
          if (action.isRotate()) continue
          if (unTravelPointNames.isEmpty()) {
            unTravelPointNames.add(action.start.pointName)
          }
          unTravelPointNames.add(action.target.pointName)
        }
      }
      PathResource(travelledPointNames, unTravelPointNames)
    }
  }

  private fun buildTrafficSpace(spaceLock: SpaceLock): List<SpaceResource> {
    val shapes = mutableListOf<SpaceResource>()
    spaceLock.cells.forEach { cell ->
      cell.layers.forEach { layer ->
        when (layer.shape.type) {
          GraphType.RECTANGLE -> {
            val rect = layer.shape as Rectangle
            val point2Ds: MutableList<Point2D> = mutableListOf()
            rect.points.forEach { p ->
              point2Ds.add(Point2D(p.x, p.y))
            }
            shapes.add(SpaceResource(type = convert(GraphType.RECTANGLE), points = point2Ds))
          }

          GraphType.CIRCLE -> {
            val circle = layer.shape as Circle
            shapes.add(
              SpaceResource(
                type = convert(GraphType.CIRCLE),
                points = listOf(Point2D(circle.center.x, circle.center.y)),
                radius = circle.radius,
              ),
            )
          }

          GraphType.POLYGON -> {
            val polygon = layer.shape as Polygon
            val point2Ds: MutableList<Point2D> = mutableListOf()
            polygon.points.forEach { p ->
              point2Ds.add(Point2D(p.x, p.y))
            }
            shapes.add(SpaceResource(type = convert(GraphType.POLYGON), points = point2Ds))
          }

          GraphType.SECTOR -> {}
        }
      }
    }
    return shapes
  }

  private fun buildTrafficBlock(blockItems: MutableList<BlockItem>): BlockedMessage {
    val rBlock: MutableList<String> = mutableListOf()
    val oBlock: MutableList<String> = mutableListOf()
    for (b in blockItems) {
      if (b.type == LockType.ROBOT) {
        rBlock.add(b.code)
      } else {
        oBlock.add(b.code)
      }
    }
    return BlockedMessage(rBlock, oBlock)
  }

  fun requestSpaceLock(owner: String, mapName: String, sceneId: String, points: MutableList<List<Vector>>): Boolean {
    if (points.isEmpty()) {
      return true
    }

    val groupName = try {
      ContextManagerService.queryRobotContext(owner).groupName
    } catch (e: Exception) {
      null
    }

    val cells = mutableListOf<Cell>()
    points.forEach { point ->
      val p = Polygon(
        type = GraphType.POLYGON,
        points = point,
        concave = false,
        subPolygons = mutableListOf(),
        box = BoundingBox(point),
      )
      val layer = Layer(0, 1.0, 0.0, p)
      cells.add(Cell(mutableListOf(layer), Position(0.0, 0.0, "", PosType.UNDEFINE)))
    }
    val spaceLock = SpaceLock(
      groupName = groupName ?: LockType.EXTERNAL.toString(),
      type = LockType.EXTERNAL,
      name = owner,
      cells = cells,
      mapName = mapName,
    )

    return LockService.lockHandle(
      LockRequest(
        id = owner,
        mapName = mapName,
        sceneId = sceneId,
        spaces = mutableListOf(spaceLock),
      ),
    ).success
  }
}