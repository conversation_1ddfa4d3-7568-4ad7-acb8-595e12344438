package com.seer.trick.fleet.traffic

import com.seer.trick.BzError
import com.seer.trick.I18N
import com.seer.trick.base.SysEmc
import com.seer.trick.base.alarm.AlarmAction
import com.seer.trick.base.alarm.AlarmItem
import com.seer.trick.base.alarm.AlarmLevel
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.fleet.FleetLogger
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.order.StepExecuteContext
import com.seer.trick.fleet.service.*
import com.seer.trick.helper.IdHelper
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap

/**
 * 每个实例只用于一个场景
 * 路径规划和交通管制。
 */
abstract class TrafficService {
  
  protected val logger: Logger = LoggerFactory.getLogger(javaClass)
  
  protected val logModuleTraffic = "Traffic"
  
  abstract val sr: SceneRuntime
  
  @Volatile
  protected var disposed = false
  
  // 机器人执行完成的步骤
  // fixme 放到 distributed 实现里 todo 迁移到 robotTrafficRuntime？
  protected val robotDoneMoves: MutableMap<String, Long> = ConcurrentHashMap()
  
  // TODO 后面改成用场景前缀的
  private val eventId = IdHelper.oidStr()
  
  /**
   * 初始化服务
   */
  open fun init() {
    FleetLogger.info(
      module = logModuleTraffic,
      subject = "InitTraffic",
      sr = sr,
      robotName = null,
      msg = emptyMap(),
    )
    
    // 监听机器人上线、下线
    FleetEventService.on("Robot::Online", FleetEventInternalListener(eventId, 100, false, ::onRobotOnline))
    FleetEventService.on("Robot::Offline", FleetEventInternalListener(eventId, 100, true, ::onRobotOffline))
    FleetEventService.on("Robot::SelfReport", FleetEventInternalListener(eventId, 100, false, ::onRobotReport))
    FleetEventService.on("Robot::Dispose", FleetEventInternalListener(eventId, 100, false, ::onRobotDispose))
  }
  
  private fun onRobotOnline(event: FleetEvent) {
    // 小心检查场景！！！
    if (event.sceneId != sr.sceneId) return
    
    val sr = SceneService.mustGetSceneById(event.sceneId)
    val rr = sr.robots[event.robotName!!] ?: return
    
    if (!rr.trafficReady) robotEnterTraffic(rr)
  }
  
  private fun onRobotOffline(event: FleetEvent) {
    // 小心检查场景！！！
    if (event.sceneId != sr.sceneId) return
    
    // 机器人离线不要下线机器人
    // val sr = SceneService.mustGetSceneById(event.sceneId)
    // val rr = sr.robots[event.robotName!!] ?: return
    // robotExitTraffic(rr)
  }
  
  private fun onRobotReport(event: FleetEvent) {
    // 小心检查场景！！！
    if (event.sceneId != sr.sceneId) return
    
    val sr = SceneService.mustGetSceneById(event.sceneId)
    val rr = sr.robots[event.robotName!!] ?: return
    
    // 为了上线时候交管还没开始处理，所以这里额外检测一次：如果机器人在线但交管未初始化，就强制再调用一次进场
    if (!rr.trafficReady) robotEnterTraffic(rr)
  }
  
  private fun onRobotDispose(event: FleetEvent) {
    // 小心检查场景！！！
    if (event.sceneId != sr.sceneId) return
    
    val sr = SceneService.mustGetSceneById(event.sceneId)
    val rr = sr.robots[event.robotName!!] ?: return
    robotExitTraffic(rr)
  }
  
  /**
   * 销毁服务。销毁后此实例不能再被重用。
   */
  open fun dispose() {
    FleetLogger.info(
      module = logModuleTraffic,
      subject = "DisposeTraffic",
      sr = sr,
      robotName = null,
      msg = emptyMap(),
    )
    
    disposed = true
    
    FleetEventService.off("Robot::Online", eventId)
    FleetEventService.off("Robot::Offline", eventId)
    FleetEventService.off("Robot::SelfReport", eventId)
    FleetEventService.off("Robot::Dispose", eventId)
    // 下线所有机器人
    sr.robots.values.forEach { robot ->
      this.robotExitTraffic(robot)
    }
  }
  
  /**
   * 规划一轮移动，返回是否规划成功
   * 运单步骤开始时，调用此接口，返回路径规划是否成功
   * TODO 可达性检测
   */
  open fun plan(task: TrafficTaskRuntime): PlanResult {
    FleetLogger.info(
      module = logModuleTraffic,
      subject = "NewPlanRequest",
      sr = sr,
      robotName = task.robotName,
      msg = mapOf("plan" to task.toString()),
    )
    
    return PlanResult(true)
  }
  
  /**
   * 一次移动完成。一般释放本次移动占用的资源。
   * todo 需要迁移？
   * 目前只被 RobotMoveService.markActionDone 调用。
   */
  open fun afterMoveActionDone(rr: RobotRuntime, move: MoveActionReq) {
    robotDoneMoves[rr.robotName] = move.index
  }
  
  /**
   * 正在执行中的步骤出现失败，调用此接口
   * TODO 删除 目前不会调用
   */
  abstract fun afterMoveActionFailed(rr: RobotRuntime, orderId: String?)
  
  /**
   * 正在执行的 TrafficTask 成功
   * 目前只被 RobotMoveService 调用，当 TrafficTask 完成时。
   */
  abstract fun afterTrafficTaskDone(rr: RobotRuntime): Boolean
  
  /**
   * 取消当前正在执行的 TrafficTask
   * 目前只被 StepExecuteService.cleanupExecuting 调用。
   * TODO 这个方法危险，没提供取消的是什么。
   */
  abstract fun afterTrafficTaskCancelled(rr: RobotRuntime)
  
  /**
   * 重置交管层的机器人信息
   * 重置机器人位置信息、空间资源信息、交管任务信息，目前仅在界面上点击重置时触发
   */
  abstract fun resetByRobot(rr: RobotRuntime)
  
  /**
   * 释放所有机器人所有资源
   */
  open fun resetAll() {
    // do nothing
  }
  
  /**
   * 机器人上线的时候会调用该方法，代表着进入交管层
   * 关键是机器人是否占用资源。如果占用资源，需要交管处理，就还是 enter 状态。
   *
   */
  open fun robotEnterTraffic(rr: RobotRuntime) {
    // do nothing
  }
  
  /**
   * 机器人下线或者被销毁时会触发该方法
   * 机器人离开交管层、清理机器人上下文资源等，由不同的交管具体实现
   */
  open fun robotExitTraffic(rr: RobotRuntime) {
    // do nothing
  }
  
  /**
   * 机器人的碰撞模型改变
   */
  open fun onRobotCollisionModelChanged(rr: RobotRuntime, model: RobotCollisionModel) {
    // do nothing
  }
  
  /**
   * 机器人身上货物的形状发生变化。包括取货完成、放货完成。
   * TODO 货架入场，是机器人放下货架或者货架第一次出现在地图里；货架出场，是机器人取货架到自己身上，或货架离开地图。
   */
  open fun onRobotLoadRelationChanged(rr: RobotRuntime, load: List<RobotLoadRelation>?) {
    // do nothing
  }
  
  /**
   *  查询机器人的索引
   *  todo 放在这里合适 ？
   * */
  fun queryRobotIndex(robotName: String): Long = robotDoneMoves[robotName] ?: -1
  
  /**
   * 初始化场景时
   */
  open fun initTrafficSchema() {
    updateTrafficSchema()
  }
  
  /**
   * 更新场景时
   */
  open fun updateTrafficSchema() {
  }
  
  /**
   *  获取单个机器人展示信息
   */
  open fun showTrafficResourceMessage(robotName: String): RobotShowTrafficMessage? =
    showTrafficResourceMessage()[robotName]
  
  /**
   *  获取所有机器人展示信息
   */
  abstract fun showTrafficResourceMessage(): Map<String, RobotShowTrafficMessage>
  
  /**
   *  机器人顶升容器接口
   * */
  open fun robotLoadContainer(
    rr: RobotRuntime,
    containerName: String? = null,
    containerType: String,
    direction: Double,
  ): Boolean = true
  
  /**
   *  机器人放下容器接口,不考虑容器锁闭信息
   * */
  open fun robotUnloadContainer(rr: RobotRuntime): Boolean = true
  
  /**
   *  机器人切换地图接口
   * */
  open fun switchMap(rr: RobotRuntime): Boolean = true
  
  /**
   *  申请在交管的空间资源
   * */
  open fun requestSpaceResource(
    owner: String,
    mapName: String,
    spaceResource: List<Polygon>,
  ): Boolean = true
  
  /**
   *  释放在交管申请的空间资源
   * */
  open fun releaseSpaceResource(owner: String, mapName: String): Boolean = true
  
  open fun debugData(): Any? = null
  
  open fun robotDebugData(robotName: String): Any? = null
  
  /**
   * 因为 TrafficService 还未改造为 object，所以这些方法放在这里
   */
  companion object {
    
    private val logger: Logger = LoggerFactory.getLogger(TrafficService::class.java)
    
    /**
     * 周期性的将任务下发给交管执行
     */
    fun submitTaskToTraffic(sr: SceneRuntime) {
      val config = sr.config
      if (sr.status == SceneStatus.Initialized && !config.trafficPlanPaused && !SysEmc.isSysEmc()) {
        
        try {
          doSubmitTaskToTraffic(sr)
          processOfflineRobots(sr)
        } catch (e: BzError) {
          // 没有必要继续循环了
          logger.error("BUG: planTaskExecutorLoop " + e.message)
          return
        } catch (e: Exception) {
          // 没有必要继续循环了
          logger.error("planTaskExecutorLoop", e)
          return
        }
      }
    }
    
    /**
     *  处理离线的机器人，检查是否有机器人离线，如果离线，则需要释放离线机器人在交管内的资源
     * */
    private fun processOfflineRobots(sr: SceneRuntime) {
      // 检查机器人是否在线
      val offlineRobots = sr.robots.values.filter { !RobotService.isOnline(it) }
      if (offlineRobots.isEmpty()) {
        return
      }
      val show = sr.trafficService.showTrafficResourceMessage()
      offlineRobots.forEach {
        if (show.containsKey(it.robotName)) {
          val ai = AlarmItem(
            sceneId = it.sr.sceneId,
            group = "Fleet",
            code = "FleetTrafficRobot",
            key = "FleetTrafficRobot-${it.robotName}",
            level = AlarmLevel.Error,
            message = I18N.lo("T00020011", listOf(it.robotName)),
            args = listOf(it.robotName),
            actions = listOf(AlarmAction(I18N.lo("btnReleaseSpaceLock"))),
          )
          
          AlarmService.addItem(ai, ttl = 3000) { actionIndex, args ->
            val rr = sr.mustGetRobot(args[0] as String)
            sr.trafficService.robotExitTraffic(rr)
          }
        }
      }
    }
    
    /**
     * 获取所有机器人未处理的任务，并依次提交给交管
     * 抛异常是 BUG
     */
    private fun doSubmitTaskToTraffic(sr: SceneRuntime) {
      // TODO 批量提交给交管
      for (rr in sr.robots.values) {
        if (!(RobotService.isRobotOkToAcceptOrder(rr) && rr.trafficReady)) continue
        
        val task = rr.pendingTrafficTask ?: continue
        if (task.status != TrafficTaskStatus.Created) continue
        
        // 为任务规划路径
        // TODO 多目标支持
        val planResult = sr.trafficService.plan(task)
        
        sr.withOrderLock {
          // 在锁内检查任务状态
          if (task.status == TrafficTaskStatus.Created) {
            if (planResult.success) {
              task.updateStatus(rr, TrafficTaskStatus.TrafficAccepted, "")
            } else {
              planResult.code?.let {
                task.updateStatus(
                  rr,
                  TrafficTaskStatus.Failed,
                  I18N.lo(planResult.code, planResult.args),
                )
              }
            }
          }
        }
      }
    }
    
    /**
     * 设置机器人下一个待规划任务。
     * 此方法可以被 StepExecuteService/TrafficService 调用，即从步骤执行或交管层设置任务。后者即交管层自己规划出来的任务。
     * 一个机器人一次只能有一个待规划任务。否则报错。
     */
    fun setPendingTrafficTask(rr: RobotRuntime, task: TrafficTaskRuntime) = rr.sr.withOrderLock {
      // 这里需要再检查一下步骤是否正常，不然会出现步骤被取消，但这里没控制，继续 pending trafficTask 导致 trafficTask 未被取消
      if (rr.executingStep == null) {
        throw OrderProcessingError(
          OpErrCode.PendingTrafficTaskError,
          "Robot not executing step, robot=$rr, new task=$task",
        )
      }
      if (task.status != TrafficTaskStatus.Created) {
        throw OrderProcessingError(
          OpErrCode.PendingTrafficTaskError,
          "New task status not Created, robot=$rr, new task=$task",
        )
      }
      
      rr.pendingTrafficTask = task
      
      FleetLogger.info(
        module = "Traffic",
        subject = "SetRobotTrafficTask",
        sr = rr.sr,
        robotName = rr.robotName,
        msg = mapOf("task" to task),
      )
    }
  }
  
  /**
   * 取放货后通知交管。
   * 小心按目前的调用链，此方法是在在锁里调用的！
   * TODO 目前有实际意义吗
   */
  fun afterLoadOrUnload(rr: RobotRuntime, sec: StepExecuteContext) {
    if (!rr.mustGetGroup().containerOversize) return
    
    if (sec.getStep().forLoad) {
      val loadRelation = rr.selfReport?.main?.loadRelations?.get(0)
      // 只有上报了类型或者
      if (loadRelation != null) {
        rr.sr.trafficService.robotLoadContainer(
          rr,
          containerType = loadRelation.type ?: sec.or.order.containerTypeName ?: "",
          direction = loadRelation.direction,
        )
      } else {
        logger.warn("No load report after loaded step, robot=$rr")
      }
    } else if (sec.getStep().forUnload) {
      rr.sr.trafficService.robotUnloadContainer(rr)
    }
  }
}