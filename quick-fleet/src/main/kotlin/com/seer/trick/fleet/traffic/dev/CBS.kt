package com.seer.trick.fleet.traffic.dev

import org.slf4j.LoggerFactory

/**
 *  Conflict-Based-Search (CBS)
 *  寻找多个机器人的无冲突路径，每个机器人单个起点终点
 */
class CBS(
  private val w: Double,
  private val mapDimX: Int,
  private val mapDimY: Int, // y 向下为正
  private val obstacles: Set<Int>,
) {

  private val logger = LoggerFactory.getLogger(this::class.java)

  // fun search(
  //   fromStates: Map<String, FSA.State>, // 初始状态，robot name ->
  //   toStates: Map<String, FSA.State>, // 目标状态，robot name ->
  // ): CbsResult {
  //   val startOn = System.currentTimeMillis()
  //
  //   var highLevelNodeExpanded = 0L
  //   var lowLevelNodeExpanded = 0L
  //
  //   val start = HighLevelNode(id = 0)
  //
  //   val futures = fromStates.keys.map { robotName ->
  //     DefaultExecutor.bgCacheExecutor.submit<FSA.PlanResult> {
  //       val fromState = fromStates[robotName]!!
  //       val constraints = Constraints()
  //       start.constraints[robotName] = constraints // 确保每个机器人都有一个空约束
  //
  //       val aStar = FSA(robotName, 0, w, mapDimX, mapDimY, obstacles, isValidNeighbor = { from, to ->
  //         stateValid(to, constraints) && transitionValid(from, to, constraints) // 初始约束为空
  //       })
  //       // 先每个智能体从起点到终点各自搜路？如果搜不到，一定没解。
  //       logger.debug("Init Search for $robotName")
  //       val solution = aStar.search(
  //         fromState,
  //         toStates[robotName]!!,
  //         constraintsToLastGoalConstraint(constraints, toStates[robotName]!!),
  //       )
  //       return@submit solution
  //     }
  //   }
  //
  //   for (future in futures) {
  //     val solution = future.get()
  //     lowLevelNodeExpanded += solution.expandedCount
  //     if (!solution.ok) {
  //       logger.error("Robot ${solution.robotName} no init solution")
  //       return CbsResult(
  //         false,
  //         emptyMap(),
  //         timeCost = System.currentTimeMillis() - startOn,
  //         highLevelNodeExpanded = highLevelNodeExpanded,
  //         lowLevelNodeExpanded = lowLevelNodeExpanded,
  //       )
  //     }
  //     // 这样的话，后续高层节点的 cost 一定不断增长
  //     start.solution[solution.robotName] = solution
  //     start.cost += solution.cost
  //   }
  //
  //   val open = PriorityQueue<HighLevelNode> { o1, o2 ->
  //     // if (cost != n.cost)
  //     o1.cost.compareTo(o2.cost)
  //     // return id > n.id;
  //   }
  //   open.offer(start)
  //
  //   var id = 1L // 高层节点编号计数器
  //
  //   while (open.isNotEmpty()) {
  //     val p = open.poll()
  //     logger.info("Check high node: ${p.id}, cost=${p.cost}")
  //     // m_env.onExpandHighLevelNode(P.cost);
  //     ++highLevelNodeExpanded
  //
  //     val conflict =
  //       getFirstConflict(p.solution)
  //         ?: return CbsResult(
  //           true,
  //           p.solution,
  //           timeCost = System.currentTimeMillis() - startOn,
  //           highLevelNodeExpanded = highLevelNodeExpanded,
  //           lowLevelNodeExpanded = lowLevelNodeExpanded,
  //         )
  //
  //     // create additional nodes to resolve conflict
  //     val constraints = createConstraintsFromConflict(conflict)
  //
  //     // TODO 防止高层节点重复检查？
  //
  //     val futures2 = constraints.map { (robotName, c) ->
  //       DefaultExecutor.bgCacheExecutor.submit {
  //         // robotName 施加约束的机器人
  //         val childConstraints = HashMap(p.constraints) // 继承父节点的约束
  //         val childNode = HighLevelNode(
  //           id = ++id,
  //           constraints = childConstraints,
  //           solution = HashMap(p.solution),
  //           cost = p.cost,
  //         )
  //         childConstraints[robotName]!!.add(c)
  //         // 先减后加，重新计算机器人 robotName 的成本
  //         childNode.cost -= childNode.solution[robotName]!!.cost
  //
  //         val robotConstraints = childNode.constraints[robotName]!!
  //         val aStar = FSA(robotName, id, w, mapDimX, mapDimY, obstacles, isValidNeighbor = { from, to ->
  //           stateValid(to, robotConstraints) && transitionValid(from, to, robotConstraints)
  //         })
  //         logger.info("[$id] Search for robot $robotName")
  //         val solution = aStar.search(
  //           fromStates[robotName]!!,
  //           toStates[robotName]!!,
  //           constraintsToLastGoalConstraint(robotConstraints, toStates[robotName]!!),
  //         )
  //         lowLevelNodeExpanded += solution.expandedCount
  //         if (solution.ok) {
  //           childNode.solution[robotName] = solution
  //           childNode.cost += childNode.solution[robotName]!!.cost
  //           open.offer(childNode)
  //         } else {
  //           // TODO
  //         }
  //       }
  //     }
  //     for (f in futures2) {
  //       f.get()
  //     }
  //   }
  //   return CbsResult(
  //     false,
  //     emptyMap(),
  //     timeCost = System.currentTimeMillis() - startOn,
  //     highLevelNodeExpanded = highLevelNodeExpanded,
  //     lowLevelNodeExpanded = lowLevelNodeExpanded,
  //   )
  // }
  //
  // // TODO 二次检查实现
  // private fun getFirstConflict(solution: Map<String, FSA.PlanResult>): Conflict? {
  //   var tMax = 0L
  //   for (sol in solution.values) {
  //     tMax = max(tMax, sol.timeNum)
  //   }
  //
  //   val robotNames = solution.keys.toList()
  //
  //   for (t in 0..tMax) {
  //     // check drive-drive vertex collisions
  //     for (i in robotNames.indices) {
  //       val robotName1 = robotNames[i]
  //       val state1 = solution[robotName1]?.path?.find { t >= it.timeStart && t <= it.timeEnd } ?: continue
  //       for (j in i + 1 until robotNames.size) {
  //         val robotName2 = robotNames[j]
  //         val state2 = solution[robotName2]?.path?.find { t >= it.timeStart && t <= it.timeEnd } ?: continue
  //         if (state1.isSameLocation(state2)) {
  //           return Conflict(
  //             time = t,
  //             agent1 = robotName1,
  //             agent2 = robotName2,
  //             type = ConflictType.Vertex,
  //             x1 = state1.x,
  //             y1 = state1.y,
  //             x2 = 0,
  //             y2 = 0,
  //           )
  //         }
  //       }
  //     }
  //
  //     // check drive-drive edge (swap)
  //     for (i in robotNames.indices) {
  //       val robotName1 = robotNames[i]
  //       val sol1 = solution[robotName1]!!
  //       val state1aIndex = sol1.path.indexOfFirst { it.timeStart >= t }
  //       if (state1aIndex < 0) continue
  //       val state1a = sol1.path[state1aIndex]
  //       val state1b = sol1.path.getOrNull(state1aIndex + 1) ?: continue
  //
  //       for (j in i + 1 until robotNames.size) {
  //         val robotName2 = robotNames[j]
  //         val sol2 = solution[robotName2]!!
  //         val state2aIndex = sol2.path.indexOfFirst { it.timeStart >= t }
  //         if (state2aIndex < 0) continue
  //         val state2a = sol2.path[state1aIndex]
  //         val state2b = sol2.path.getOrNull(state1aIndex + 1) ?: continue
  //
  //         // 1a x 2b, 1b x 2a
  //         if (state1a.x == state2b.x && state1a.y == state2b.y && state1b.x == state2a.x && state1b.y == state2a.y) {
  //           return Conflict(
  //             time = t,
  //             agent1 = robotName1,
  //             agent2 = robotName2,
  //             type = ConflictType.Edge,
  //             x1 = state1a.x,
  //             y1 = state1a.y,
  //             x2 = state1b.x,
  //             y2 = state1b.y,
  //           )
  //         }
  //       }
  //     }
  //   }
  //
  //   return null
  // }
  //
  // // 返回 robot name->
  // // TODO 二次检查实现
  // private fun createConstraintsFromConflict(conflict: Conflict): Map<String, Constraints> {
  //   val constraints = mutableMapOf<String, Constraints>()
  //   if (conflict.type == ConflictType.Vertex) {
  //     val c1 = Constraints()
  //     c1.vertexConstraints += VertexConstraint(conflict.time, conflict.x1, conflict.y1)
  //     constraints[conflict.agent1] = c1
  //     constraints[conflict.agent2] = c1
  //   } else if (conflict.type == ConflictType.Edge) {
  //     val c1 = Constraints()
  //     c1.edgeConstraints += EdgeConstraint(conflict.time, conflict.x1, conflict.y1, conflict.x2, conflict.y2)
  //     constraints[conflict.agent1] = c1
  //     val c2 = Constraints()
  //     c2.edgeConstraints += EdgeConstraint(conflict.time, conflict.x2, conflict.y2, conflict.x1, conflict.y1)
  //     constraints[conflict.agent2] = c2
  //   }
  //   return constraints
  // }
  //
  // // 一个智能体的。在边界内，不是障碍物，不与约束冲突。
  // // TODO 二次检查实现
  // private fun stateValid(state: FSA.State, constraints: Constraints): Boolean {
  //   val con = constraints.vertexConstraints
  //   return state.x in 0 until mapDimX &&
  //       state.y >= 0 &&
  //       state.y < mapDimY &&
  //       !obstacles.contains(state.y * mapDimX + state.x) &&
  //       (con.find { it.x == state.x && it.y == state.y && it.time == state.time } == null)
  // }
  //
  // // TODO 二次检查实现
  // private fun transitionValid(s1: FSA.State, s2: FSA.State, constraints: Constraints): Boolean {
  //   val con = constraints.edgeConstraints
  //   return con.find { it.time == s1.time && it.x1 == s1.x && it.y1 == s1.y && it.x2 == s2.x && it.y2 == s2.y } == null
  // }
  //
  // private fun constraintsToLastGoalConstraint(constraints: Constraints, goal: FSA.State): Long {
  //   var lastGoalConstraint = -1L
  //   for (vc in constraints.vertexConstraints) {
  //     if (vc.x == goal.x && vc.y == goal.y) {
  //       lastGoalConstraint = max(lastGoalConstraint, vc.time)
  //     }
  //   }
  //   return lastGoalConstraint
  // }
  //
  // class HighLevelNode(
  //   val id: Long, // 节点 ID
  //   var solution: MutableMap<String, FSA.PlanResult> = ConcurrentHashMap(), // robot name ->
  //   val constraints: MutableMap<String, Constraints> = ConcurrentHashMap(), // robot name ->
  //   @Volatile
  //   var cost: Double = 0.0,
  // )
  //
  // // todo 改成基于非网格的
  // class VertexConstraint(val time: Long, val x: Int, val y: Int)
  //
  // // todo 改成基于非网格的
  // class EdgeConstraint(val time: Long, val x1: Int, val y1: Int, val x2: Int, val y2: Int)
  //
  // class Constraints(
  //   val vertexConstraints: MutableSet<VertexConstraint> = HashSet(),
  //   val edgeConstraints: MutableSet<EdgeConstraint> = HashSet(),
  // ) {
  //
  //   fun add(other: Constraints) {
  //     vertexConstraints.addAll(other.vertexConstraints)
  //     edgeConstraints.addAll(other.edgeConstraints)
  //   }
  //
  //   /**
  //    * 判断两组约束有交集
  //    */
  //   fun overlap(other: Constraints): Boolean {
  //     for (vc in vertexConstraints) {
  //       if (other.vertexConstraints.contains(vc)) return true
  //     }
  //     for (ec in edgeConstraints) {
  //       if (other.edgeConstraints.contains(ec)) return true
  //     }
  //     return false
  //   }
  // }
  //
  // enum class ConflictType {
  //   Vertex,
  //   Edge,
  // }
  //
  // class Conflict(
  //   val time: Long,
  //   val agent1: String,
  //   val agent2: String,
  //   val type: ConflictType,
  //   val x1: Int,
  //   val y1: Int,
  //   val x2: Int,
  //   val y2: Int,
  // )
  //
  // data class CbsResult(
  //   val ok: Boolean,
  //   val robotPlans: Map<String, FSA.PlanResult>,
  //   val timeCost: Long,
  //   val highLevelNodeExpanded: Long,
  //   val lowLevelNodeExpanded: Long = 0,
  // )
}