package com.seer.trick.fleet.mars

import com.seer.trick.Cq
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.falcon.task.FalconTaskService
import org.slf4j.LoggerFactory

object DirectOrderResourceCleaner {

  private val logger = LoggerFactory.getLogger(this::class.java)

  fun init() {
    FalconTaskService.addResCleaner("DirectRobotOrder", ::cleanDirectOrder)
  }

  @Suppress("UNUSED_PARAMETER")
  private fun cleanDirectOrder(
    taskId: String,
    resType: String,
    resId: String,
    args: Map<String, Any?>?,
  ) {
    if (args == null) {
      logger.error("清理直接运单，args 为空")
      return
    }
    val orderId = args["orderId"] as String
    logger.info("清理直接运单，运单号=$orderId")
    val order = EntityRwService.findOneById("DirectRobotOrder", orderId) ?: return
    val status = order["status"] as String
    if (status != "Cancelled" && status != "Done") {
      EntityRwService.updateOne("DirectRobotOrder", Cq.idEq(orderId), mutableMapOf("status" to "Cancelled"))
    }
  }
}