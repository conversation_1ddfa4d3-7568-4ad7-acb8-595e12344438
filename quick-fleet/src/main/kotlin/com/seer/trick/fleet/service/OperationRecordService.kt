package com.seer.trick.fleet.service

import com.fasterxml.jackson.annotation.JsonInclude
import com.seer.trick.base.concurrent.PollingJobManager
import com.seer.trick.base.file.FileManager
import com.seer.trick.fleet.device.door.DoorMainStatus
import com.seer.trick.fleet.device.lift.LiftDoorStatus
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.order.StepExecuteDigest
import com.seer.trick.helper.*
import org.apache.commons.io.FileUtils
import org.apache.commons.lang3.StringUtils
import org.apache.commons.lang3.time.DateUtils
import org.slf4j.LoggerFactory
import java.io.*
import java.nio.charset.StandardCharsets
import java.util.*
import java.util.concurrent.Executors
import java.util.concurrent.locks.ReentrantLock

/**
 * 车队运行记录
 * 分场景记录。每天一个文件夹。每小时一个文件。
 */
class OperationRecordService(private val sr: SceneRuntime) {

  private val logger = LoggerFactory.getLogger(javaClass)

  private val logExecutor = Executors.newSingleThreadExecutor()

  @Volatile
  private var writer: Writer? = null

  private var forDay: String = ""
  private var forHour: Int = -1

  private var recordCounter = 0

  private val lock = ReentrantLock()

  fun init() {
    PollingJobManager.submit(
      threadName = "FtOp-${sr.no}",
      remark = "Recording operations",
      interval = { 1000L },
      logger = logger,
      workerMaxTime = 1500L,
      stopCondition = { sr.status == SceneStatus.Disposed },
      exceptionContinue = true,
      tags = setOf(sr.tag),
      worker = { loop() },
    )
  }

  fun dispose() {
    close()
  }

  /**
   * 记录和清理
   */
  private fun loop() {
    try {
      logRobotStates()

      recordCounter = (recordCounter + 1) % (60 * 30)
      if (recordCounter == 1) cleanOld()
    } catch (e: Exception) {
      logger.error("Operation record loop", e)
    }
  }

  /**
   * 记录机器人状态
   */
  private fun logRobotStates() {
    if (sr.config.noOpLog) return

    if (sr.status != SceneStatus.Initialized) return

    var logs = ""

    val trafficService = sr.trafficService
    val trafficInfo = trafficService.showTrafficResourceMessage()

    for (rr in sr.listRobots()) {
      val selfReport = rr.selfReport ?: continue
      val main = selfReport.main ?: continue

      val robotTraffic = trafficInfo[rr.robotName]

      val cmp = RobotService.getCollisionModel(rr)
      val cm = RobotCollisionModel(listOf(RobotCollisionShape(polygon = cmp)), cmp)

      val ro = RobotOperation(
        g = rr.config.groupId,
        x = main.x,
        y = main.y,
        d = main.direction,
        collisionModel = cm,
        loadRelations = main.loadRelations,
        map = main.currentMap,
        travelled = robotTraffic?.pathResource?.travelledPointNames,
        unTravelled = robotTraffic?.pathResource?.unTravelPointNames,
        spaces = robotTraffic?.spaceResources,
        blockedMsg = robotTraffic?.blockedMessage,
        orders = rr.orders.keys.toList(),
        exeStep = rr.executingStep?.digest(),
        bins = rr.bins.joinToString(",") { "${it.index}:${it.status}:${it.orderId}:${it.containerId}" },
        isAlarm = !main.alarms.isNullOrEmpty(),
        noControlByFleet = main.noControlByFleet,
        isEmergency = main.emergency,
        isSoftEmc = main.softEmc,
        isBlocked = main.blocked,
        offDuty = rr.offDuty,
      )

      logs += buildLogHead(selfReport.timestamp.time, "RO3", rr.robotName) + JsonHelper.writeValueAsString(ro) + "\n"
    }

    for (lift in sr.lifts.values) {
      val liftOp = LiftOperation(
        online = lift.online,
        autoMode = lift.autoMode,
        currentFloor = lift.currentFloor,
        targetFloor = lift.targetFloor,
        doors = lift.doors,
        people = lift.people,
        fault = lift.fault,
        faultMsg = lift.faultMsg,
      )
      logs += buildLogHead(lift.lastStatusUpdateTime.time, "LO1", lift.config.name) +
        JsonHelper.writeValueAsString(liftOp) + "\n"
    }

    for (door in sr.doors.values) {
      val doorOp = DoorOperation(
        online = door.online,
        status = door.status,
        fault = door.fault,
        faultMsg = door.faultMsg,
        openDoorRobotNames = door.openDemands.map { it.robotName },
      )
      logs += buildLogHead(door.lastStatusUpdateTime.time, "DO1", door.config.name) +
        JsonHelper.writeValueAsString(doorOp) + "\n"
    }

    if (logs.isNotBlank()) log(logs)

    lock.lock()
    try {
      writer?.flush()
    } catch (e: IOException) {
      logger.error("flush writer error " + e.getTypeMessage())
    } finally {
      lock.unlock()
    }
  }

  /**
   * 注意自己最后加换行！
   */
  private fun log(content: String) = logExecutor.submit {
    lock.lock()
    try {
      val now = Calendar.getInstance()
      val day = DateHelper.formatDate(now.time, "yyyyMMdd")
      val hour = now.get(Calendar.HOUR_OF_DAY)

      if (writer == null || forDay != day || forHour != hour) {
        close()
        val file = getFile(getSceneOpLogDir(), day, hour)
        file.parentFile.mkdirs()

        val newWrite = BufferedWriter(FileWriter(file, StandardCharsets.UTF_8, true))
        writer = newWrite
        forDay = day
        forHour = hour
      }

      writer?.write(content)
      writer?.flush()
    } catch (e: Exception) {
      logger.error("log: " + e.getTypeMessage())
    } finally {
      lock.unlock()
    }
  }

  /**
   * 列出所有文件
   */
  fun listFiles(): List<String> {
    val sceneDir = getSceneOpLogDir()
    return sceneDir.listFiles()?.filter { !it.isDirectory }?.map { it.name } ?: emptyList()
  }

  /**
   * 关闭当前文件
   */
  private fun close() {
    lock.lock()
    try {
      writer?.close()
    } catch (e: IOException) {
      logger.error("close: " + e.getTypeMessage())
    } finally {
      lock.unlock()
    }
  }

  @JsonInclude(JsonInclude.Include.NON_NULL)
  data class RobotOperation(
    val g: Int, // 所在组
    val x: Double?,
    val y: Double?,
    val d: Double?,
    val collisionModel: RobotCollisionModel? = null, // 机器人的碰撞模型，可能为空
    val loadRelations: List<RobotLoadRelation>? = null, // 机器人载货情况
    val map: String?, // 当前地图
    val travelled: List<String>?, // 已经走过的路径
    val unTravelled: List<String>?, // 未来将要走的路径
    val spaces: List<SpaceResource>?, // 空间资源信息
    val blockedMsg: BlockedMessage?, // 阻挡信息
    val orders: List<String>?, // 运单列表
    val exeStep: StepExecuteDigest?, // 当前正在执行的步骤
    val bins: String = "", // 库位
    val isAlarm: Boolean = false, // 是否告警
    val noControlByFleet: Boolean? = null, // 无控制权
    val isEmergency: Boolean? = null, // 是否急停
    val isSoftEmc: Boolean? = null, // 是否软急停
    val isBlocked: Boolean? = null, // 是否阻挡
    val offDuty: Boolean = false, // 不接单
  )

  @JsonInclude(JsonInclude.Include.NON_NULL)
  data class LiftOperation(
    val online: Boolean, // 是否在线
    val autoMode: Boolean,
    val currentFloor: String? = null,
    val targetFloor: String? = null,
    val doors: List<LiftDoorStatus>,
    val people: Boolean = false,
    val fault: Boolean, // 是否故障：
    val faultMsg: String, // 故障信息。可能会有多条故障信息。
  )

  @JsonInclude(JsonInclude.Include.NON_NULL)
  data class DoorOperation(
    val online: Boolean, // 是否在线
    val status: DoorMainStatus, // 开门状态。关键信息放到前面，看日志方便些。
    val fault: Boolean, // 是否故障：
    val faultMsg: String, // 故障信息。可能会有多条故障信息。
    val openDoorRobotNames: List<String>, // 开门机器人列表
  )

  /**
   * 删除旧的
   */
  private fun cleanOld() {
    var keepDays = sr.config.opLogKeepDays
    if (keepDays <= 0) keepDays = 5 // 默认 5 天

    val oldFilename = DateHelper.formatDate(DateUtils.addDays(Date(), -keepDays), "yyyyMMdd") + "-00"

    val sceneDir = getSceneOpLogDir()
    if (!sceneDir.exists()) return

    val files = sceneDir.listFiles()?.filter { it.isFile } ?: return
    for (file in files) {
      if (file.name >= oldFilename) continue
      logger.debug("Remove fleet op record：${file.name}")
      try {
        file.delete()
      } catch (e: IOException) {
        logger.error("Delete file ${file.absolutePath}: " + e.getTypeMessage())
      }
    }
  }

  fun sliceToTmpFile(filename: String, start: Long, end: Long): File? {
    val srcFile = getFile(filename)
    if (!srcFile.exists()) return null

    // TODO 性能优化

    val lines = FileUtils.readFileToString(srcFile, StandardCharsets.UTF_8).split("\n")
      .filter { line ->
        val timestampStr = StringUtils.substringBefore(line, ":") ?: return@filter false
        val timestamp = NumHelper.anyToLong(timestampStr) ?: return@filter false
        timestamp in start..end
      }

    val tmpFile = FileManager.nextTmpFile(ext = "log")
    FileUtils.write(tmpFile, lines.joinToString("\n"), StandardCharsets.UTF_8)

    return tmpFile
  }

  /**
   * 日志文件，每个小时一个，文件名是 yyyyMMdd-HH
   */
  private fun getFile(sceneDir: File, day: String, hour: Int): File =
    File(sceneDir, "$day-${hour.toString().padStart(2, '0')}.log")

  /**
   * 根据场景和文件名获取。限制访问范围。
   */
  fun getFile(filename: String): File {
    val dir = getSceneOpLogDir()
    val f = File(dir, filename)
    if (!FileHelper.isFileInDir(f, dir)) throw IllegalArgumentException("Bad access")
    return f
  }

  /**
   * 场景的记录目录
   */
  private fun getSceneOpLogDir(): File = File(getDir(), sr.sceneId)

  /**
   * 记录目录
   */
  private fun getDir(): File = File(FileManager.getFilesDir(), "fleet-op")

  private fun buildLogHead(timestamp: Long, topic: String, robotName: String?) = "$timestamp:$topic:$robotName|"
}