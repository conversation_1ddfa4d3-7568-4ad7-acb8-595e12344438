package com.seer.trick.fleet.traffic.distributed.deadlock.idle

import com.seer.trick.fleet.traffic.distributed.block.BlockItem
import com.seer.trick.fleet.traffic.distributed.context.ContextManagerService
import com.seer.trick.fleet.traffic.distributed.context.domain.RobotContext
import com.seer.trick.fleet.traffic.distributed.context.domain.RobotMotionType
import com.seer.trick.fleet.traffic.distributed.context.domain.RobotStatus
import com.seer.trick.fleet.traffic.distributed.deadlock.DeadLockProcessor
import com.seer.trick.fleet.traffic.distributed.deadlock.helper.DeadLockHelper
import com.seer.trick.fleet.traffic.distributed.deadlock.helper.PathCollisionHelper
import com.seer.trick.fleet.traffic.distributed.deadlock.model.DeadLockMessage
import com.seer.trick.fleet.traffic.distributed.map.*
import com.seer.trick.fleet.traffic.distributed.service.DistributedTrafficService
import org.slf4j.LoggerFactory
import java.util.*

/**
 *  空闲车死锁处理
 * */
object IdleDeadLockProcessor : DeadLockProcessor {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 检测障碍物是否为空闲车
   * */
  override fun check(robotName: String, block: MutableList<BlockItem>): DeadLockMessage {
    val context = ContextManagerService.queryRobotContext(robotName)
    val message =
      DeadLockMessage(robotName = robotName, mapName = context.mapName, context = context)
    val items = this.findRobot(block)

    if (items.isEmpty()) {
      logger.info("$robotName |block is not robot")
      return message
    }
    if (context.deadLock.idleTime > 0 && System.currentTimeMillis() - context.deadLock.idleTime < 2 * 1000) {
      logger.info("$robotName |dead lock idle time is not over")
      return message
    }
    val idle: MutableList<String> = mutableListOf()
    // 检测障碍物是否为空闲车
    items.forEach {
      val queryRobotContext = ContextManagerService.queryRobotContext(it.code)
      // 查询机器人状态
      if (queryRobotContext.baseDomain.status == RobotStatus.IDLE) {
        idle.add(it.code)
      }
    }

    if (idle.isEmpty()) return message

    message.state = true
    message.idleRobots = idle
    return message
  }

  override fun handle(message: DeadLockMessage): Boolean {
    // 不同算子进行计算，找到一个合理的解
    if (message.idleRobots.size <= 0) return true // 没有空闲车可以去推
    val context = message.context
    if (context.deadLock.idleTime > 0 && System.currentTimeMillis() - context.deadLock.idleTime < 2 * 1000) {
      logger.info("${context.robotName} |dead lock idle time is not over")
      return false
    }
    context.deadLock.idleTime = System.currentTimeMillis()
    val lockPoints: MutableList<String> = mutableListOf()
    for (idle in message.idleRobots) {
      // 寻找空闲车避让的点
      val point = findAvoidPoint(idle, context, lockPoints)
      // 调用接口去执行避让
      if (point != null) {
        lockPoints.add(point.pointName)
        DistributedTrafficService.sendMove(idle, point)
      }
    }
    return true
  }

  // 寻找空闲避让点
  private fun findAvoidPoint(idle: String, context: RobotContext, lockPoints: MutableList<String>): Point? {
    // 获取空闲车当前的位置信息
    val idleContext = ContextManagerService.queryRobotContext(idle)
    val curPosition = idleContext.baseDomain.curPoint
    if (curPosition == null) {
      logger.error("$idle robot not found current position")
      return null
    }
    val curPoint =
      MapService.findPointByName(
        idleContext.sceneId,
        idleContext.mapName,
        idleContext.groupName,
        curPosition.pointName,
      )
    val robotHeading = idleContext.baseDomain.robotHeading
    val plan = context.plan
    // todo 获取机器人当前位置的信息
    val action = if (plan.queryCurrentAction() == null) plan.path.first() else plan.queryCurrentAction()
    if (action == null) {
      logger.error("${context.robotName} robot not found current action")
      return null
    }
    val startLine = MapService
      .findLineByName(context.sceneId, context.mapName, context.groupName, action.lineName)

    val closePoints: MutableList<String> =
      mutableListOf(curPoint.pointName, startLine.start.pointName, startLine.end.pointName)
    closePoints.addAll(lockPoints)
    val lines = curPoint.toLines
    val spaceLocks = plan.restPath().mapNotNull { it.runLock }.toMutableList()
    val queue: Queue<Line> = LinkedList()
    val rest: MutableList<String> = plan.restPath().map { it.target.pointName }.distinct().toMutableList()
    queue.addAll(lines)
    while (queue.isNotEmpty()) {
      val line = queue.poll()
      if (closePoints.contains(line.end.pointName)) {
        continue
      }
      // 是否符合条件
      if (!DeadLockHelper.canPass(idleContext, line)) {
        continue
      }
      if (context.robotMotionType != RobotMotionType.OMNI && DeadLockHelper.pointNotRotate(line, robotHeading)) {
        continue
      }
      // 检查终点是否在路线上
      if (rest.contains(line.end.pointName)) {
        closePoints.add(line.end.pointName)
        val intoLines = line.end.toLines
        for (l in intoLines) {
          if (!closePoints.contains(l.end.pointName)) {
            queue.add(l)
          }
        }
        continue
      }
      if (!line.end.avoid) {
        closePoints.add(line.end.pointName)
        val intoLines = line.end.toLines
        for (l in intoLines) {
          if (!closePoints.contains(l.end.pointName)) {
            queue.add(l)
          }
        }
        continue
      }
      // 检查是否会发生碰撞
      if (!PathCollisionHelper.staticPathCollision(idleContext, line.end, spaceLocks)) {
        return line.end
      }
      closePoints.add(line.end.pointName)
      val intoLines = line.end.toLines
      for (l in intoLines) {
        if (!closePoints.contains(l.end.pointName)) {
          queue.add(l)
        }
      }
    }

    logger.info("$idle not find avoid point, find collision point to resolver dead lock")
    // todo 这个时候，任选一点，让车动起来
    val queue0: Queue<Line> = LinkedList()
    queue0.addAll(
      startLine.start.toLines.filter { line ->
        !closePoints.contains(line.end.pointName)
      }.toMutableList(),
    )
    queue0.addAll(startLine.end.toLines.filter { line -> !closePoints.contains(line.end.pointName) }.toMutableList())
    while (queue0.isNotEmpty()) {
      val line = queue0.poll()
      if (closePoints.contains(line.end.pointName)) {
        continue
      }
      // 是否符合条件
      if (!DeadLockHelper.canPass(idleContext, line)) {
        continue
      }
      if (!line.end.avoid) {
        closePoints.add(line.end.pointName)
        val intoLines = line.end.toLines
        for (l in intoLines) {
          if (!closePoints.contains(l.end.pointName)) {
            queue0.add(l)
          }
        }
        continue
      }
      return line.end
    }
    logger.warn("$idle  not find avoid point")
    return null
  }
}