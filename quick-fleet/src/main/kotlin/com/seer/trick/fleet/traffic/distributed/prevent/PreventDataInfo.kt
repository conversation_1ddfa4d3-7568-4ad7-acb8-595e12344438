package com.seer.trick.fleet.traffic.distributed.prevent

import com.seer.trick.fleet.traffic.distributed.context.domain.RobotMotionType
import com.seer.trick.fleet.traffic.distributed.lock.model.SpaceLock
import com.seer.trick.fleet.traffic.distributed.map.Point
import com.seer.trick.fleet.traffic.distributed.plan.model.PathAction

data class PreventRequestInfo(
  val idleRobots: MutableList<PreventRobotInfo>, // 空闲机器人集合
  val blockRobots: MutableList<PreventRobotInfo>, // 故障机器人集合
  val deadLockRobots: MutableList<PreventRobotInfo>, // 死锁机器人集合
  val runningRobots: MutableList<PreventRobotInfo>, // 正在运行机器人集合
  val time: Long,
  val version: String, // 版本
) {
  var calculateType: CalculateType = CalculateType.NEW
  val conflict: MutableList<ConflictNode> = mutableListOf()
}

// 结果数据
data class PreventResponse(
  val index: Int, // 可执行到的索引
  val reserveTable: MutableMap<Int, PathAction>,
  val disableTable: MutableMap<Int, MutableMap<String, Int>>,
  val orderNo: String?,
  val stepNo: String?,
  val version: String,
  val blockRobot: MutableList<String>,
  val priority: Int,
  val flowRobots: MutableList<String>,
  val deadlocks: MutableList<String>,
)

enum class CalculateType {
  NEW,
  NEXT_WINDOW,
}

data class PreventRobotInfo(
  val robotName: String, // 机器人 id
  val mapName: String, // 地图 id
  val sceneId: String, // 场景 id
  val groupName: String, // 机器人类型
  val robotMotionType: RobotMotionType, // 机器人运动类型
  val robotHeading: Int, // 机器人朝向
  val point: Point, // 机器人位置
  val orderNo: String?, // 任务号
  val stepNo: String?, // 步骤号
  val restPath: List<PathAction>, // 机器人剩余路径
  var index: Long,
  val allocateIndex: Long, // 机器人已分配的最远索引
  val reserveSpaceLock: SpaceLock,
  var time: Long,
  var priority: Int, // 优先级, 从 1 开始排序， 0 表示没有优先级，需要计算
  val flowRobots: MutableList<String>, // 跟随，但还没到，先记录，后续到了再判断谁先谁后
  val deadlocks: MutableList<String>, // 和自己已经发生死锁的机器人

  var reserveTable: MutableMap<Int, PathAction>, // 预约表
  var disableTable: MutableMap<Int, MutableMap<String, Int>>, // 禁用表

)

// 冲突节点
data class ConflictNode(
  val robotA: String, // 机器人 id
  val robotB: String, // 机器人 id
  val conflictPoint: List<String>,
  val indexA: List<Int>,
  val indexB: List<Int>,
  val mapName: String, // 地图 id
  val type: ConflictType, // 冲突类型
)

enum class ConflictType {
  POINT,
  EDGE,
  FLOW,
  OPPOSE,
}