package com.seer.trick.fleet.traffic.distributed.plan.model

import java.util.*
import kotlin.Comparator
import kotlin.collections.HashMap
import kotlin.collections.List

class OpenList {

  private val compare = Comparator.comparingDouble(PointData::f)

  private var openList = PriorityQueue<PointData>(64, compare)

  private var openMap = HashMap<String, PointData>()

  fun addOpenList(list: List<PointData>) {
    for (data in list) {
      add(data)
    }
  }

  fun add(pointData: PointData) {
    val key = pointData.key()
    val data = openMap[key]
    if (data != null && data.f() > pointData.f()) {
      openList.remove(openMap[key])
      openList.offer(pointData)
      openMap[key] = pointData
    } else if (data == null) {
      // 将其加入到列表中
      openList.offer(pointData)
      openMap[pointData.key()] = pointData
    }
  }

  fun pop(): PointData {
    val pointData = openList.poll()
    openMap.remove(pointData.key())
    return pointData
  }

  fun isNotEmpty(): Boolean = !openList.isEmpty()
}