package com.seer.trick.fleet.mock.service

import com.seer.trick.BzError
import com.seer.trick.I18N
import com.seer.trick.fleet.domain.GeoHelper
import com.seer.trick.fleet.domain.Point2D
import com.seer.trick.fleet.domain.SceneContainerType
import com.seer.trick.fleet.mock.*
import com.seer.trick.fleet.mock.service.MockService.robotRuntimeMapByRobotId
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.SceneRuntime
import com.seer.trick.fleet.service.SceneService
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.NumHelper
import com.seer.trick.helper.containsIgnoreCase
import com.seer.trick.helper.getTypeMessage
import org.slf4j.LoggerFactory
import kotlin.math.cos
import kotlin.math.sin

/**
 * 仿真机器人负载 Service，维护货物姿态 goodsRegion、背篓 selfBins
 */
object MockContainersProcessor {
  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 设置货物姿态
   */
  fun setGoodsRegion(mr: MockSeerRobotRuntime, goodsId: String? = null, containerTheta: Double? = null) {
    // 如果机器人有 goodsRegion，则直接转。否则，加载机器人上当前运单的容器类型
    val goodsRegion =
      rebuildGoodsRegion(containerTheta, mr, goodsId) ?: buildGoodsRegionByOrder(mr, goodsId, containerTheta) ?: return

    // 先尝试根据运单，获取容器类型。不打印获取容器类型为 null 的日志，因为料箱车、转运车不关心容器类型是常态
    mr.setRecord(mr.record.copy(goodsRegion = goodsRegion))
  }

  /**
   * 根据 mr.records.goodsRegions.oriPoints 重现构建料架形状
   */
  private fun rebuildGoodsRegion(containerTheta: Double?, mr: MockSeerRobotRuntime, goodsId: String?): GoodsRegion? {
    val goodsName = goodsId ?: mr.record.goodsRegion?.name ?: ""

    val oriPoints = mr.record.goodsRegion?.oriPoints
    if (oriPoints?.isNotEmpty() != true) return null

    val theta = GeoHelper.normalizeRadian(containerTheta ?: mr.record.goodsRegion?.theta ?: mr.record.theta)
    // 根据上次缓存的加载点位
    val robotWorldPos = Point2D(mr.record.x, mr.record.y)
    val centerOffset = mr.record.goodsRegion?.centerOffset ?: Point2D(0.0, 0.0)
    val points = calculateShelfPoints(
      robotPosi = robotWorldPos,
      shelfRad = theta,
      oriPoints = oriPoints,
      centerOffset = centerOffset,
    )

    return GoodsRegion(
      name = goodsName,
      oriPoints = oriPoints,
      points = points,
      theta = theta,
      centerOffset = centerOffset,
    )
  }

  /**
   * 根据 SceneRuntime 里机器人当前运单构建料架形状
   */
  private fun buildGoodsRegionByOrder(
    mr: MockSeerRobotRuntime,
    goodsId: String?,
    containerTheta: Double? = null,
  ): GoodsRegion? {
    // 多负载的机器人才不加载料架模型
    if (mr.record.selfBins.isNotEmpty() && mr.record.selfBins.size > 1) return null

    val sr = SceneService.listScenes().firstOrNull { it.robots.containsKey(mr.config.name) } ?: run {
      logger.error("fail get scenes which contains robot ${mr.config.name}")
      return null
    }
    val rr = sr.robots[mr.config.name] ?: return null

    val group = rr.mustGetGroup()
    if (!group.containerOversize) return null

    // 根据运单加载对应的料架点位
    val containerType = getContainerTypeByOrder(sr, rr, mr) ?: return null

    // 假设偏移量沿货架局部坐标系x轴
    val centerOffset = Point2D(containerType.groupCenterDistances[group.id] ?: 0.0, 0.0)
    // 获取货架当前状态（这里需要根据业务逻辑获取实际方向）

    val robotWorldPos = Point2D(mr.record.x, mr.record.y)
    val oriPoints = getContainerPoints(containerType)

    val theta = GeoHelper.normalizeRadian(containerTheta ?: mr.record.goodsRegion?.theta ?: mr.record.theta)

    // 计算变换后的顶点
    val points = calculateShelfPoints(
      robotPosi = robotWorldPos,
      shelfRad = theta,
      oriPoints = oriPoints,
      centerOffset = centerOffset,
    )

    return GoodsRegion(
      name = goodsId ?: mr.record.goodsRegion?.name ?: "",
      oriPoints = oriPoints,
      points = points,
      theta = theta,
      centerOffset = centerOffset,
    )
  }

  /**
   * 在调度场景中，根据机器人执行的运单，获取运单上指定的容器类型。
   * 如果机器人执行的运单没有指定容器类型的，则认为不用加载容器的模型，返回 null
   */
  private fun getContainerTypeByOrder(
    sr: SceneRuntime,
    rr: RobotRuntime,
    mr: MockSeerRobotRuntime,
  ): SceneContainerType? {
    val typeNames = rr.orders.values.mapNotNull { it.order.containerTypeName }.filter { it.isNotBlank() }
    val types = sr.containerTypes.values.filter { it.name in typeNames }
    if (types.isEmpty()) return null

    return types.random()
  }

  private fun getContainerPoints(containerType: SceneContainerType) =
    if (containerType.outerWidth != 0.0 && containerType.outerLength != 0.0) {
      listOf(
        Point2D(containerType.outerLength / 2, containerType.outerWidth / 2),
        Point2D(-containerType.outerLength / 2, containerType.outerWidth / 2),
        Point2D(-containerType.outerLength / 2, -containerType.outerWidth / 2),
        Point2D(containerType.outerLength / 2, -containerType.outerWidth / 2),
      )
    } else {
      sortPointsInRbkOrder(containerType.polygon.points)
    }

  /**
   * 按照 RBK 料架点位重新排序
   *
   * M4 场景中容器类型配置的顺序是交管画料架的顺序，与 RBK 的不一致。故从 M4 场景中取容器类型后，需要转换一下点位顺序。
   * RBK 的点位顺序是：依次画第一、二、三、四象限的点
   */
  private fun sortPointsInRbkOrder(points: List<Point2D>): List<Point2D> = points.sortedBy { it.quadrant() }

  /**
   * 计算点位所在象限
   * 坐标轴的特殊处理：将坐标轴上的点，归到其逆时针方向的象限里，避免 (0,1) 与 (1,0) 被划分到同一个象限。(0,0) 返回 1
   */
  private fun Point2D.quadrant(): Int {
    // 计算 Point2D 的象限
    val quadrant = when {
      x > 0 && y >= 0 -> 1 // 第一象限，+x 轴
      x <= 0 && y > 0 -> 2 // 第二象限，+y 轴
      x < 0 && y <= 0 -> 3 // 第三象限，-x 轴
      x >= 0 && y < 0 -> 4 // 第四象限，-y 轴
      else -> 1
    }
    return quadrant
  }

  /**
   * 计算货架朝向
   */
  private fun calculateShelfPoints(
    robotPosi: Point2D, // 机器人当前位置
    shelfRad: Double, // 货架当前朝向（弧度）
    oriPoints: List<Point2D>, // 货架原始多边形顶点（中心坐标系）
    centerOffset: Point2D = Point2D(0.0, 0.0), // 货架中心相对于机器人的偏移量
  ): List<Point2D> {
    // 1. 计算货架中心的世界坐标
    val shelfCenter = robotPosi + rotatePoint(centerOffset, shelfRad)

    // 2. 准备旋转参数
    val cosTheta = cos(shelfRad)
    val sinTheta = sin(shelfRad)

    // 3. 对每个顶点进行坐标变换
    return oriPoints.map { localPoint ->
      // 应用旋转变换（绕货架中心）
      val rotated = Point2D(
        x = localPoint.x * cosTheta - localPoint.y * sinTheta,
        y = localPoint.x * sinTheta + localPoint.y * cosTheta,
      )

      // 平移到世界坐标系
      rotated + shelfCenter
    }
  }

  private fun rotatePoint(p: Point2D, rad: Double): Point2D {
    val cosTheta = cos(rad)
    val sinTheta = sin(rad)

    return Point2D(
      x = p.x * cosTheta - p.y * sinTheta,
      y = p.x * sinTheta + p.y * cosTheta,
    )
  }

  /**
   * 移除货物姿态
   */
  fun removeGoodsRegion(mr: MockSeerRobotRuntime) {
    mr.setRecord(mr.record.copy(goodsRegion = null))
    MockStore.updateMockRobotRecordAsync(mr.record)
  }

  /**
   * 检查机器人是否可以装载指定容器
   */
  fun checkSelfBinsBeforeMove(mr: MockSeerRobotRuntime, move: Step3066) {
    // 多储位机器人判断
    if (mr.record.selfBins.isNotEmpty()) {
      // 货叉库位
      val armBin = mr.record.selfBins.last()
      when (getMoveType(move)) {
        MoveType.Load -> {
          val t = armBin.containerId
          // 多储位机器人判断：货叉不为空时，去取货
          if (mr.record.keepInArm == true && t.isNotBlank()) throw BzError("errArmNotEmptyForLoad")
          logger.debug("Mock ${mr.robotId}:${mr.config.name} 3066 load container [${move.goodsId}]")
        }

        MoveType.Unload -> {
          val t = armBin.containerId
          if (mr.record.keepInArm == true) {
            if (t.isNotBlank()) {
              if (move.binIndex != armBin.binIndex) {
                // 卸背篓上的货物，但货叉有货
                throw BzError("errArmNotEmptyForUnload")
              } else if (move.goodsId != armBin.containerId) {
                // 卸货叉上的货物，但准备卸的货物与货叉的货物不一致
                throw BzError("errArmContainerForUnload")
              }
            } else {
              // 卸货叉上的货，但货叉为空
              if (move.binIndex == armBin.binIndex) throw BzError("errArmEmptyForUnload")
            }
          }

          logger.debug("Mock ${mr.robotId}:${mr.config.name} 3066 unload container [${move.goodsId}]")
          // 调度判断是多储位机器人时，一般都会传货物编号
        }

        MoveType.Other -> {}
      }
    }
  }

  /**
   * 3066 指令完成后，处理机器人负载：货物形状、背篓
   */
  fun postProcessRobotLoads(mr: MockSeerRobotRuntime, move: Step3066) {
    when (getMoveType(move)) {
      MoveType.Load -> {
        // 取货要更新货物姿态的时候
        setGoodsRegion(mr, move.goodsId)
        bindContainerToSelfBin(mr, move)
      }

      MoveType.Unload -> {
        // 放货时移除货物
        // logger.info("${mr.config.name} remove containers")
        removeGoodsRegion(mr)
        clearSelfBinByContainerId(mr, move.goodsId ?: "")
      }

      MoveType.Other -> {}
    }
    logger.debug("after postProcessRobotLoads mr.record=${JsonHelper.writeValueAsString(mr.record)}")
  }

  /**
   * 绑定容器到机器人背篓
   *
   * 如果 Step3066 指定了 binIndex，则放到指定的背篓里；如果没指定，则从下往上选一个
   */
  private fun bindContainerToSelfBin(mr: MockSeerRobotRuntime, move: Step3066) {
    if (mr.record.selfBins.isEmpty()) return

    val containerId = move.goodsId ?: ""
    val binIndex = move.binIndex
    synchronized(mr.lock) {
      // 防止 3066 超时重发，导致的重复绑定
      if (containerId.isNotBlank() && mr.record.selfBins.any { it.containerId == containerId }) {
        MockAlarmProcessor.addOrUpdateAlarm(
          mr,
          MockSeerRobotAlarm("Error", "999000", "$containerId is loaded, but rbk receive load again"),
        )
        return
      }
      // 防止指定位置的背篓被占用了
      if (binIndex != null && mr.record.selfBins.any { it.binIndex == binIndex && it.containerId.isNotBlank() }) {
        MockAlarmProcessor.addOrUpdateAlarm(
          mr,
          MockSeerRobotAlarm(
            "Error",
            "999001",
            "load targetBin is not empty, newContainerId = $containerId, selfBinIndex = $binIndex",
          ),
        )
        // TODO 要不要 return ？
      }

      val firstEmptyBin =
        if (binIndex != null) {
          mr.record.selfBins.firstOrNull { it.binIndex == binIndex && it.containerId.isBlank() }
        } else {
          mr.record.selfBins.sortedBy { it.binIndex }.firstOrNull { it.containerId.isBlank() && !it.hasGoods }
        }
      if (firstEmptyBin == null) {
        val binMsg = if (binIndex != "999") {
          I18N.lo("robotArmMsg")
        } else {
          I18N.lo("binNoMsg", listOf(NumHelper.anyToInt(binIndex)!! + 1, NumHelper.anyToInt(binIndex)))
        }
        throw BzError("errLoadContainerToSelfBinFail", containerId, binMsg)
      } else {
        bindContainerToSelfBin(mr, BindContainerSelfBinReq(containerId, firstEmptyBin.binIndex, ""))
      }
      // MockStore.updateMockRobotRecordAsync(mr.record)
    }
  }

  /**
   * 绑定容器到机器人背篓
   */
  fun bindContainerToSelfBin(mr: MockSeerRobotRuntime, req: BindContainerSelfBinReq) {
    if (mr.record.selfBins.isEmpty()) return
    synchronized(mr.lock) {
      val selfBins = mr.record.selfBins.map {
        if (it.binIndex == req.binIndex) {
          it.copy(desc = req.desc, containerId = req.containerId, hasGoods = true)
        } else {
          it
        }
      }
      logger.debug("bindContainerToRandomSelfBin: ${req.containerId} -> ${req.binIndex}")
      mr.setRecord(mr.record.copy(selfBins = selfBins))
    }
    MockStore.updateMockRobotRecordAsync(mr.record)
  }

  /**
   * 根据容器号 containerId 清空机器人背篓
   */
  fun clearSelfBinByContainerId(mr: MockSeerRobotRuntime, containerId: String) {
    if (mr.record.selfBins.isEmpty()) return
    synchronized(mr.lock) {
      val selfBins = mr.record.selfBins.map {
        if (it.containerId == containerId) {
          it.copy(desc = "", containerId = "", hasGoods = false)
        } else {
          it
        }
      }
      logger.debug("clearSelfBinByContainerId: $containerId")
      mr.setRecord(mr.record.copy(selfBins = selfBins))
    }
    MockStore.updateMockRobotRecordAsync(mr.record)
  }

  /**
   * 根据 binIndex 清空机器人指定背篓
   */
  fun clearSelfBin(mr: MockSeerRobotRuntime, req: ClearSelfBinReq) {
    if (mr.record.selfBins.isEmpty()) return
    synchronized(mr.lock) {
      val selfBins = mr.record.selfBins.map {
        if (it.binIndex == req.binIndex) {
          it.copy(desc = "", containerId = "", hasGoods = false)
        } else {
          it
        }
      }
      logger.debug("clearSelfBin: ${req.binIndex}")
      mr.setRecord(mr.record.copy(selfBins = selfBins))
    }
    MockStore.updateMockRobotRecordAsync(mr.record)
  }

  /**
   * 清空机器人所有背篓
   */
  fun clearAllSelfBins(mr: MockSeerRobotRuntime) {
    if (mr.record.selfBins.isEmpty()) return
    synchronized(mr.lock) {
      mr.setRecord(mr.record.copy(selfBins = MockSeerRobotConfig.buildEmptySelfBins(mr.config)))
    }
    MockStore.updateMockRobotRecordAsync(mr.record)
  }

  /**
   * 修复仿真机器人的背篓信息
   *
   * 在 5.1.1 之前发布的版本中，仿真机器人时没有 selfBin 的，
   * 5.1.1+ 的版本使用之前版本创建的仿真机器人时，会导致机器人执行运单报错。
   *
   * RobotService.init() 之后，OrderService.init() 之前调用。
   */
  fun fixSelfBins(sr: SceneRuntime) = sr.withOrderLock {
    robotRuntimeMapByRobotId.values.forEach { mrr ->
      try {
        if (mrr.record.selfBins.isNotEmpty()) return@forEach // 不需要修正

        // selfBins 一定得是非空的，需要 fix
        val binNum = mrr.config.containerNum
          ?: sr.robots.values.firstOrNull { it.robotName == mrr.config.name }?.config?.selfBinNum
        if (binNum == null) {
          logger.info("fix selfBins of ${mrr.robotId} failed，RobotRuntime=null, and containerNum=null")
          return@forEach // 场景中此机器人已经被删除了，且仿真配置中也没有此机器人的背篓的配置。其他措施：重新保存一下此机器人的配置。
        }

        clearAllSelfBins(mrr) // 初始化机器人的背篓。
      } catch (e: Exception) {
        logger.error("fix selfBins of ${mrr.robotId} failed，${e.getTypeMessage()}", e)
      }
    }
  }

  /**
   * 判断 Step3066 的类型：取货、放货、其它
   *
   * operation、binTask 包含 unload 就是放货；
   * 取货同理，但要排除 unload 情况
   */
  private fun getMoveType(move: Step3066): MoveType =
    if (move.operation?.containsIgnoreCase("unload") == true || move.binTask?.containsIgnoreCase("unload") == true) {
      MoveType.Unload
    } else if (move.operation?.containsIgnoreCase("load") == true || move.binTask?.containsIgnoreCase("load") == true) {
      MoveType.Load
    } else {
      MoveType.Other
    }

  enum class MoveType {
    Load,
    Unload,
    Other,
  }
}