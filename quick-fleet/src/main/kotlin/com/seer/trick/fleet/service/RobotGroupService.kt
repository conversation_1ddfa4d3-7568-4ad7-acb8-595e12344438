package com.seer.trick.fleet.service

import com.seer.trick.BzError
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.mock.service.MockService
import com.seer.trick.helper.JsonFileHelper
import org.slf4j.LoggerFactory
import java.io.File

/**
 * 机器人组管理。
 */
object RobotGroupService {

  private val logger = LoggerFactory.getLogger(javaClass)

  val defaultCollisionPolygon = Polygon(
    listOf(
      Point2D(-0.5, 0.4),
      Point2D(0.5, 0.4),
      Point2D(0.5, -0.4),
      Point2D(-0.5, -0.4),
    ),
  )

  val defaultCollisionModel =
    RobotCollisionModel(listOf(RobotCollisionShape(polygon = defaultCollisionPolygon)), bound = defaultCollisionPolygon)

  /**
   * 加载并初始化一个场景下的机器人组
   */
  fun init(sr: SceneRuntime): List<RobotGroup> = sr.withOrderLock {
    var groups: List<RobotGroup> = JsonFileHelper.readJsonFromFile(getRobotGroupFile(sr.sceneId)) ?: emptyList()
    groups = groups.map { g ->
      // 容错：添加默认碰撞模型
      g.copy(collisionModel = ensureCollisionModel(g.collisionModel))
    }
    sr.robotGroups = groups.associateBy { it.id }
    return@withOrderLock groups
  }

  /**
   * 根据机器人获取机器人组
   */
  fun mustGetRobotGroupByRobot(sr: SceneRuntime, robotName: String): RobotGroup {
    val rr = sr.mustGetRobot(robotName)
    return sr.mustGetRobotGroupById(rr.config.groupId)
  }

  /**
   * 新建机器人组。
   */
  fun create(sr: SceneRuntime, req: RobotGroup): RobotGroup {
    checkCollisionModel(req.collisionModel)

    val group = sr.withOrderLock {
      val group = req.copy(id = (sr.robotGroups.values.maxOfOrNull { it.id } ?: 0) + 1)
      val groups = sr.robotGroups + mapOf(group.id to group)
      sr.robotGroups = groups
      persist(sr, groups)
      group
    }

    logger.info("Create robot group of scene $sr: $req")
    FleetEventService.fire(
      FleetEvent(
        name = "RobotGroup::Create",
        sceneId = sr.sceneId,
        extra = mapOf("groupId" to group.id),
      ),
    )

    return group
  }

  /**
   * 删除机器人组。
   */
  fun remove(sr: SceneRuntime, groupIds: List<Int>) {
    sr.withOrderLock {
      val groups = sr.robotGroups.toMutableMap()
      for (id in groupIds) {
        val robots = sr.robots.values.filter { it.config.groupId == id }
        RobotService.remove(sr, robots.map { it.robotName })
        groups.remove(id)
      }
      sr.robotGroups = groups
      persist(sr, groups)
    }

    logger.info("Remove robot groups of scene $sr: $groupIds")
    FleetEventService.fire(
      FleetEvent(
        name = "RobotGroup::Remove",
        sceneId = sr.sceneId,
        extra = mapOf("groupIds" to groupIds),
      ),
    )
  }

  /**
   * 修改机器人组。
   */
  fun update(sr: SceneRuntime, groupId: Int, req: RobotGroup) {
    val oldGroup = sr.mustGetRobotGroupById(groupId)
    val newGroup = req.copy(id = groupId)
    checkCollisionModel(newGroup.collisionModel)

    sr.withOrderLock {
      val groups = sr.robotGroups.toMutableMap()
      groups[groupId] = newGroup
      persist(sr, groups)
      // 等待上面机器人组更新完成，再更新机器人
      if (!oldGroup.disabled && newGroup.disabled || oldGroup.disabled && !newGroup.disabled) {
        // 启停整组机器人
        RobotService.updateDisabledByGroup(sr, groupId, newGroup.disabled)
      }
    }

    logger.info("Update robot group of scene $sr: $newGroup")

    // 删除或创建仿真
    MockService.initOrRemoveMockByRobotState(sr)

    FleetEventService.fire(
      FleetEvent(
        name = "RobotGroup::Update",
        sceneId = sr.sceneId,
        extra = mapOf("groupId" to groupId),
      ),
    )

    // 重置一下交管机器人的信息，更新碰撞模型
    sr.listRobots().filter { it.config.groupId == groupId }.forEach {
      if (it.pendingTrafficTask == null) {
        sr.trafficService.resetByRobot(it)
      }
    }
  }

  /**
   * 完全替换场景下的所有机器人组。
   */
  fun replaceAllGroups(sr: SceneRuntime, groups: List<RobotGroup>) {
    val groups2 = groups.map { it.copy(collisionModel = ensureCollisionModel(it.collisionModel)) }

    sr.withOrderLock {
      persist(sr, groups2.associateBy { it.id })
    }

    logger.info("Replace all robot groups of scene $sr")
    FleetEventService.fire(
      FleetEvent(name = "RobotGroup::Replace", sceneId = sr.sceneId),
    )
  }

  // 只负责持久化
  private fun persist(sr: SceneRuntime, groups: Map<Int, RobotGroup>) {
    sr.robotGroups = groups
    JsonFileHelper.writeJsonToFile(getRobotGroupFile(sr.sceneId), groups.values, true)
  }

  private fun getRobotGroupFile(sceneId: String): File {
    val dir = SceneFileService.getSceneDir(sceneId)
    return File(dir, "robot-group.json")
  }

  /**
   * 如果碰撞模型为空，报错
   */
  private fun checkCollisionModel(collisionModel: RobotCollisionModel) {
    if (collisionModel.isEmpty()) throw BzError("errCollisionModelEmpty")
  }

  /**
   * 判断碰撞模型是否为空，如果为空，返回默认的。
   */
  private fun ensureCollisionModel(collisionModel: RobotCollisionModel): RobotCollisionModel =
    if (collisionModel.isEmpty()) {
      defaultCollisionModel
    } else {
      collisionModel
    }
}