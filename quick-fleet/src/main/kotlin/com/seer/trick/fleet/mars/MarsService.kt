package com.seer.trick.fleet.mars

import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.map.MapZoneCache
import com.seer.trick.fleet.service.MapService
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.RobotService
import com.seer.trick.fleet.service.SceneRuntime
import com.seer.trick.helper.NumHelper
import com.seer.trick.robot.map.MrSite
import com.seer.trick.robot.rachel.MrRobotInfoAll

class MarsService(private val sr: SceneRuntime) {

  private val dispatcher = MarsDispatcher(sr, this)

  val scheduler = MarsScheduler(sr, this)

  val scheduleService = MarsScheduleService(sr, this)

  fun init() {
    dispatcher.init()
  }

  fun dispose() {
    dispatcher.dispose()
  }

  /**
   * 按点位名称或库位名称返回点位
   */
  fun getPointByLoc(robotName: String, loc: String): MapPoint? {
    val rr = sr.mustGetRobot(robotName)

    return rr.sr.mapCache.getPointCacheByGroupAndLoc(rr, loc)?.point
  }

  /**
   * MapPoint 转 MrSite
   */
  fun mapPointToMrSite(point: MapPoint): MrSite =
    MrSite(id = point.name, type = point.type, x = point.x, y = point.y, direction = point.direction)

  /**
   * 列出机器人
   */
  fun listRobots(): List<RobotRuntime> = sr.listRobots()

  /**
   * 根据 RobotRuntime 产生 MrRobotInfoAll
   *
   */
  fun getRobotInfoAll(rr: RobotRuntime): MrRobotInfoAll {
    val robotName = rr.robotName // robotName 同 robotId
    // RobotRuntime 没有 runtimeRecord，即内存里没有，需要从数据库读
    val runtimeRecord = EntityRwService.findOne("MrRobotRuntimeRecord", Cq.idEq(robotName)) ?: HashMap(0)

    val selectedStep = rr.executingStep // 机器人当前执行的 step
    val targetPoint = if (selectedStep == null) {
      null // 机器人当前 step 的目标点位
    } else {
      rr.sr.mapCache.getPointCacheByGroupAndLoc(rr, selectedStep.getStep().location)?.point?.name
    }

    return MrRobotInfoAll(
      robotName,
      systemConfig = MarsDomainConverter.convertRobotConfig(rr).ev,
      runtimeRecord = runtimeRecord,
      online = RobotService.isOnline(rr),
      rr.selfReport?.let { MarsDomainConverter.convertSelfReport(it) },
      targetSite = targetPoint,
      lockedSiteIds = scheduleService.listMySiteIds(robotName),
      pendingSiteIds = scheduler.pendingSiteIds[robotName] ?: listOf(),
    )
  }

  /**
   * 寻找最短路，去掉已锁定的资源
   * 注意用机器人（组）自己的地图搜路！
   */
  fun getShortestPath(
    robotName: String,
    fromSiteId: String,
    toSiteId: String,
    lockedSiteIds: Set<String> = emptySet(),
  ): ShortestPath {
    val rr = sr.mustGetRobot(robotName)
    val fromPointName = rr.sr.mapCache.mustGetPointCacheByGroupAndLoc(rr, fromSiteId).point.name
    val toPointName = rr.sr.mapCache.mustGetPointCacheByGroupAndLoc(rr, toSiteId).point.name
    return MapService.getShortestPathOfCrossAreas(rr, fromPointName, toPointName, lockedSiteIds)
  }

  /**
   * 计算路径成本。注意 startSiteId / endSiteId 可能是库位名或点位名。
   */
  fun calcCost(robotName: String, startSiteId: String, endSiteId: String): Double {
    val shortestPath = getShortestPath(robotName, startSiteId, endSiteId)
    return shortestPath.weight
  }

  /**
   * 列出互斥区。注意这里使用合并后的地图 mergedMap
   */
  fun listBlockZones(): List<MapZoneCache> {
    val cache = sr.mapCache
    return cache.areaById.values.map { it.mergedMap.zoneIdMap.values }
      .flatten().filter { it.zone.type == "BlockArea" }
  }

  // 判断线路是否与多边形相交
  private fun pathIntersectPolygon(
    fromPoint: MapPoint,
    toPoint: MapPoint,
    tracePoints: List<CurvePoint2D>,
    polygon: Polygon,
  ): Boolean {
    // 起点或终点在多边形边上或内部 就认为这个线路就被这个多边形包裹
    if (GeoHelper.isPointInPolygon(polygon, fromPoint.x, fromPoint.y) ||
      GeoHelper.isPointInPolygon(polygon, toPoint.x, toPoint.y)
    ) {
      return true
    }
    // 轨迹点分割成线路判断是否相交
    for (i in 0 until tracePoints.size - 1) {
      val fromPD = Point2D(tracePoints[i].x, tracePoints[i].y)
      val toPD = Point2D(tracePoints[i + 1].x, tracePoints[i + 1].y)
      for (j in 0 until polygon.points.size) {
        if (
          GeoHelper.isLinesIntersect(polygon.points[j], polygon.points[(j + 1) % polygon.points.size], fromPD, toPD)
        ) {
          return true
        }
      }
    }
    return false
  }

  /**
   * 寻找离机器人最近的光通讯点
   */
  fun findClosestRobotConnectedPoint(robotName: String): EntityValue? {
    val rr = sr.mustGetRobot(robotName)
    val main = rr.selfReport?.main ?: return null
    val rx = main.x ?: return null
    val ry = main.y ?: return null

    val points = EntityRwService.findMany("RobotConnectedPoint", Cq.all())
    var point: EntityValue? = null
    var minD = Double.MAX_VALUE
    for (p in points) {
      val x = NumHelper.anyToDouble(p["x"]) ?: continue
      val y = NumHelper.anyToDouble(p["y"]) ?: continue
      val d = GeoHelper.euclideanDistance(rx, ry, x, y)
      if (d <= 1 && d < minD) { // <= 1m
        point = p
        minD = d
      }
    }
    return point
  }
}