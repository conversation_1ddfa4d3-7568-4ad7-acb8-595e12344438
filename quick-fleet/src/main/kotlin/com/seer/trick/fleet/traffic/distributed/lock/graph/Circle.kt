package com.seer.trick.fleet.traffic.distributed.lock.graph

import kotlin.math.pow
import kotlin.math.sqrt

/**
 *  圆形
 * */
class Circle(override var type: GraphType, val radius: Double, val center: Vector) : Shape {

  val box: BoundingBox =
    BoundingBox(center.x - radius, center.y + radius, center.x + radius, center.y - radius)

  override fun intersectionRectangle(rect: Rectangle): Boolean {
    val c = center
    if (!rect.box.intersect(getBoundingBox())) {
      return false
    }
    // 检测到矩形最近的边
    val points = rect.points
    var minX = points[0].x
    var minY = points[0].y
    var maxX = points[0].x
    var maxY = points[0].y
    for (i in points.indices) {
      val p = points[i]
      if (p.x < minX) {
        minX = p.x
      }
      if (p.x > maxX) {
        maxX = p.x
      }
      if (p.y < minY) {
        minY = p.y
      }
      if (p.y > maxY) {
        maxY = p.y
      }
      val p1 = points[(i + 1) % points.size]
      val distance = VeLine(Vector(p.x, p.y), Vector(p1.x, p1.y)).vectorProjectToLine(Vector(c.x, c.y))
      if (distance <= radius) {
        return true
      }
    }
    return c.x - radius >= minX && c.x + radius <= maxX && c.y - radius >= minY && c.y + radius <= maxY
  }

  override fun intersectionCircle(circle: Circle): Boolean {
    val c = center
    val vc = circle.center
    // 圆心之间的距离 大于 两半径之和 不会相交
    return sqrt((c.x - vc.x).pow(2) + (c.y - vc.y).pow(2)) <= (radius + circle.radius)
  }

  override fun intersectionPolygon(polygon: Polygon): Boolean = polygon.intersectionCircle(this)

  override fun intersectionSector(sector: Sector): Boolean {
    // todo 待后期需要实现
    return false
  }

  override fun getBoundingBox(): BoundingBox = box

  override fun copy(): Shape {
    val c = center.copy()
    return Circle(type, radius, c)
  }
}