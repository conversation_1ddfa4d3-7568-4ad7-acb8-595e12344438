package com.seer.trick.fleet.service

import com.seer.trick.base.LifetimeStatus
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.order.OrderRuntime
import com.seer.trick.fleet.order.StepExecuteContext
import com.seer.trick.fleet.traffic.TrafficTaskRuntime
import com.seer.trick.robot.vendor.seer.rbk.RbkClient
import java.util.*
import java.util.concurrent.*

/**
 * 一个机器人的运行时状态。只包含状态和读写方法。业务代码在 RobotService 李。
 */
class RobotRuntime(
  /**
   * 所属场景
   */
  val sr: SceneRuntime,
  /**
   * 机器人的配置。可修改。但不能修改机器人名称。
   */
  val config: SceneRobot,
) {

  /**
   * 机器人名，方便访问，不会改
   */
  @JvmField
  val robotName = config.robotName

  /**
   * Job 的标签
   */
  val tag = "Robot-" + config.robotName

  /**
   * 表示此实例已销毁，不能再被使用。如果机器人时被停用的，则直接是销毁状态。
   */
  @Volatile
  var ltStatus = LifetimeStatus.Created

  /**
   * 如果是调度连兼容的模式，用这个客户端
   */
  @Volatile
  var rbkClient: RbkClient? = null

  /**
   * 不接单。只能被 RobotService.updateOffDuty 修改。
   */
  @Volatile
  var offDuty = false

  /**
   * 充电运单结束时间。此时机器人可能还没实际开始充电。
   * 这个字段有两个作用：1、防止充电运单结束，但机器人还未上报正在充电。2、防止机器人频发开始结束充电
   */
  @Volatile
  @JvmField
  var chargingOrderDoneOn: Date? = null

  /**
   * 最新一次机器人上报正在充电的时间
   */
  @Volatile
  var reportingChargingOn: Date? = null

  /**
   * 强制充电
   *
   * 机器人当前电量 selfReport.main.batteryLevel 低于第一个匹配到的充电配置的 chargeOnly 后，进入强充状态
   */
  @Volatile
  var forcedCharging = false

  /**
   * 表示正在读取状态
   */
  @Volatile
  var fetching = false

  /**
   * 机器人当前状态
   */
  @Volatile
  var selfReport: RobotSelfReport? = null

  /**
   * 机器人的机身配置
   */
  @Volatile
  var selfConfig: RobotSelfConfig? = null

  /**
   * 读取机器人状态的计数器
   */
  @Volatile
  var fetchCounter = 0

  /**
   * 获取状态失败的次数
   */
  @Volatile
  var fetchFailureNum: Int = 0

  /**
   * 最新一次获取机器人状态异常
   */
  @Volatile
  var lastFetchError: Throwable? = null

  /**
   * 连接失败重连次数
   */
  @Volatile
  var fetchFailureToAutoReconnectCounter = 0

  /**
   * 缓存读取到的机器人模型文件
   */
  // @Volatile
  // var cacheModelEv: EntityValue? = null

  /**
   * 从模型文件中读取到的底盘多边形
   */
  // @Volatile
  // var modelChassis: Polygon? = null

  /**
   * 分派给机器人的运单列表
   */
  val orders: MutableMap<String, OrderRuntime> = ConcurrentHashMap()

  /**
   * 机器人身上的库位状态
   */
  val bins: MutableList<RobotBin> = Collections.synchronizedList(ArrayList())

  /**
   * 当前正在执行的运单步骤。故障了这个字段不删。
   */
  @Volatile
  var executingStep: StepExecuteContext? = null

  /**
   * 专用于执行步骤
   */
  val executingStepExecutor: ExecutorService = Executors.newSingleThreadExecutor()

  /**
   * 机器人正在执行的自动任务
   */
  @Volatile
  var autoOrder: RobotAutoOrder? = null

  /**
   * 机器人不能接单原因
   */
  @Volatile
  var orderReject: RejectReason? = null

  /**
   * 不能执行步骤的原因
   */
  @Volatile
  var stepReject: RejectReason? = null

  /**
   * 机器人从什么时候开始空闲，自系统启动以来
   */
  @Volatile
  var idleFrom: Date? = Date()

  /**
   * 期望机器人当前位置
   * 为什么要这个？是为了避免机器人位置未能及时更新或者机器人突然断联，导致的业务异常
   * 比如前一个停靠任务任务完成，运单被删除，但这个时候机器人位置还没更新，导致下一轮计算停靠资源的时候，认为这个停靠点是可用的
   * 当机器人有上报的位置时，将那个上报位置赋值给这个位置
   */
  @Volatile
  @JvmField
  var expectedOccupiedPointName: String? = null

  /**
   * 交管层是否已经准备好让这个机器人执行规划，由交管层在准备好后设置为 true。
   */
  @Volatile
  var trafficReady = false

  /**
   * 待交管处理的任务。只能被 TrafficService#setPendingTrafficTask 修改。
   * 任务结束将此字段置为 null：任务完成或取消
   */
  @Volatile
  var pendingTrafficTask: TrafficTaskRuntime? = null

  /**
   * 需要机器人执行的移动动作
   */
  val moveActions: MutableList<MoveActionRuntime> = CopyOnWriteArrayList()

  /**
   * 用于记录最近 3 条路径导航任务的队列
   */
  val recentNavTasks = ConcurrentLinkedQueue<NavTaskRuntime>()

  /**
   * 是否正在处理最终的取放货指令
   */
  @Volatile
  var doingKeyAction = false

  /**
   * 机器人是否在导航中，导航结束重新置为 false
   */
  @Volatile
  var navigating: Boolean = false

  /**
   * 机器人是否在推送地图
   */
  @Volatile
  var pushingMap: Boolean = false

  /**
   * 是否发生当前地图不匹配异常
   */
  @Volatile
  var currentMapNotMatchedError: Boolean = false

  /**
   * 机器人当前位置，用于派单，只能被派单赋值、使用
   */
  var startPointNameForDispatching: String? = null

  /**
   * 取消执行，进入一段时间的冷静期
   */
  @Volatile
  var coolingFrom: Long? = null

  /**
   * 接下来必须接相同运单的步骤
   */
  @Volatile
  var selectSameOrderId: String? = null

  /**
   * 正在使用的电梯
   */
  @Volatile
  var usingLiftId: Int? = null

  /**
   * 返回机器人所在组
   */
  fun mustGetGroup(): RobotGroup = sr.mustGetRobotGroupById(config.groupId)

  /**
   * 机器人是否停用，机器人组停用时机器人也被停用
   */
  fun disabled(): Boolean = config.disabled || mustGetGroup().disabled

  /**
   * 机器人执行状态
   * TODO 没有 sec 但有 order 故障！
   */
  fun getExecuteStatus(): RobotExecuteStatus {
    val sec = executingStep
    return if (sec != null) {
      if (sec.fault) {
        RobotExecuteStatus.Failed
      } else if (sec.withdrawn) {
        RobotExecuteStatus.Idle
      } else {
        RobotExecuteStatus.Moving
      }
    } else {
      if (orders.values.any { it.order.fault }) {
        RobotExecuteStatus.Failed
      } else {
        RobotExecuteStatus.Idle
      }
    }
  }

  /**
   * 是否正在执行
   */
  fun isExecuting(): Boolean = getExecuteStatus() == RobotExecuteStatus.Moving

  /**
   * 机器人是否在撤单后的冷静期内，期内执行
   */
  fun isCooling(): Boolean {
    val c = coolingFrom ?: return false
    return System.currentTimeMillis() - c < 1000 * 2 // 2s
  }

  /**
   * 能独立转料架 顶升机构允许转
   */
  fun canRotateShelf(): Boolean {
    val group = mustGetGroup()
    return !group.salverNotRotate
  }

  override fun toString(): String = robotName
}