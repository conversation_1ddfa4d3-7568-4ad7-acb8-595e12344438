package com.seer.trick.fleet.traffic.venus.cache

import com.seer.trick.fleet.map.SceneMapCache
import com.seer.trick.fleet.service.MapService
import com.seer.trick.fleet.service.RobotRuntime
import java.util.HashMap
import java.util.LinkedHashMap
import java.util.PriorityQueue
import java.util.concurrent.ConcurrentHashMap
import kotlin.collections.iterator
import kotlin.math.abs
import kotlin.math.atan2

/**
 * 迪杰斯特拉启发式距离缓存。
 *
 * 以目标点 ➜ 起点为键，最短路径成本，忽略朝向差异。
 */
object DijkstraCache {
  // todo 实现带方向的
  // todo 复用计算过程

  /**
   * 多层缓存：
   *   goalPoint -> fromPoint -> headBucket -> cost
   */
  // 无并发需求，使用 LinkedHashMap 保持插入/排序顺序
  private val cache = LinkedHashMap<String, LinkedHashMap<String, LinkedHashMap<Int, Double>>>()

  // 缓存最短路径点列 key=(from,to)
  private val pathCache = ConcurrentHashMap<Pair<String, String>, List<String>>()
  private val ignoreOrientation = true
  private val cacheHeuristic = true

  /**
   * 将角度划分到 8 个 45° 等分桶 (0‥7)。
   */
  private fun headBucket(deg: Double): Int {
    return (((deg + 45) / 90.0).toInt()) and 3 // 保证在 0‥7 范围
  }

  /**
   * 查询从 [fromPoint] 到 [goalPoint] 的启发式距离。
   * TODO 不会跨区域
   * 如果缓存未命中，则调用 [com.seer.trick.fleet.service.MapService.getShortestPathCostOfCrossAreas] 计算后写入缓存。
   *
   * @param rr           机器人运行时，用于获取地图及分组信息
   * @param fromPoint    起始点位名称
   * @param goalPoint    目标点位名称
   * @param fromHead     起始朝向（弧度）
   * @return 启发式距离，如果不可达返回 null
   */
  fun getOrCompute(rr: RobotRuntime, fromPoint: String, goalPoint: String, fromHead: Double): Double? {
    if (!cacheHeuristic) return null
    // 如果该目标还未预计算，先做一次区域级反向 Dijkstra
    if (!cache.containsKey(goalPoint)) {
      precomputeAreaShortest(rr, goalPoint)
    }
    val headDeg = Math.toDegrees(fromHead).let { if (it < 0) it + 360 else it }
    val bucket = headBucket(headDeg)

    val goalMap = cache.getOrPut(goalPoint) { LinkedHashMap() }
    val fromMap = goalMap.getOrPut(fromPoint) { LinkedHashMap() }

    // 使用桶中心角度 (0,90,180,270) 代表该朝向，保证同一桶的旋转成本一致，且避免多次计算
    fromMap[bucket]?.let { return it }

    val sp = MapService.getShortestPathOfCrossAreas(rr, fromPoint, goalPoint)
    if (!sp.found) return null
    val baseCost = sp.weight
    val mapCache = rr.sr.mapCache

    // 一次性计算并写入 8 个朝向桶，避免重复计算
    for (b in 0 until 4) {
      val bucketCenterDeg = b * 90.0
      val rotCost = if (ignoreOrientation) {
        0.0
      } else {
        calcRotationCostForPath(rr, mapCache, sp.points, bucketCenterDeg)
      }
      fromMap[b] = baseCost + rotCost
    }
    // 返回当前桶的值
    return fromMap[bucket]
  }

  /**
   * 获取起点到终点的最短路径点位序列（忽略朝向与锁点）。
   * 若缓存未命中，则调用 [MapService.getShortestPathOfArea] 计算并缓存。
   * 不可达返回 null。
   */
  fun getPathOrCompute(rr: RobotRuntime, areaId: Int, fromPoint: String, goalPoint: String): List<String>? {
    val key = Pair(fromPoint, goalPoint)
    pathCache[key]?.let { return it }

    val sp = MapService.getShortestPathOfArea(rr, areaId, fromPoint, goalPoint)
    if (!sp.found || sp.points.isEmpty()) return null

    pathCache[key] = sp.points
    return sp.points
  }

  /**
   * 针对 goalPoint 所在区域一次性反向跑 Dijkstra，得到所有点的基础最短路成本并写入 cache。
   * 仅计算路径长度（忽略朝向），四个 90° 桶共用同一成本。
   */
  private fun precomputeAreaShortest(rr: RobotRuntime, goalPoint: String) {
    // 找出包含 goalPoint 的 AreaMapCache 及组地图
    val areaCache = rr.sr.mapCache.areaById.values.firstOrNull { ac ->
      ac.groupedMaps[rr.config.groupId]?.pointNameMap?.containsKey(goalPoint) == true
    } ?: return
    val gm = areaCache.mergedMap

    // 若已缓存直接返回
    if (cache.containsKey(goalPoint)) return

    val goalMap = cache.getOrPut(goalPoint) { LinkedHashMap() }

    // priority queue of (pointName, cost)
    val dist = HashMap<String, Double>()
    val pq = PriorityQueue<Pair<String, Double>>(compareBy { it.second })
    pq.add(goalPoint to 0.0)

    while (pq.isNotEmpty()) {
      val (curName, curCost) = pq.poll()
      if (dist.containsKey(curName)) continue
      dist[curName] = curCost

      val pc = gm.pointNameMap[curName] ?: continue
      for (path in pc.backwardPaths) {
        if (path.disabled) continue
        val nextName = path.fromPointName
        val baseLenRaw = path.actualLength
        val baseLen = if (baseLenRaw == Double.MAX_VALUE || baseLenRaw <= 0) 1.0 else baseLenRaw
        val edgeCost = baseLen
        val nCost = curCost + edgeCost
        if (nCost < (dist[nextName] ?: Double.MAX_VALUE)) {
          pq.add(nextName to nCost)
        }
      }
    }

    // 写入 cache
    for ((from, cost) in dist) {
      val fromMap = goalMap.getOrPut(from) { LinkedHashMap() }
      for (b in 0 until 4) fromMap[b] = cost
    }
  }

  /**
   * 预计算所有点到 [goalPoint] 的最短成本（包含 8 个朝向桶）。
   * 通常在高层求解器初始化时调用。
   */
  fun precomputeAllPoints(rr: RobotRuntime, goalPoint: String) {
    precomputeAreaShortest(rr, goalPoint)
    sortCache()
  }

  /**
   * 对 cache 第一层(goal)和第二层(from)按字典序排序，方便调试
   */
  private fun sortCache() {
    val sortedFirst = cache.toSortedMap()
    cache.clear()
    for ((goal, fromMap) in sortedFirst) {
      // 第二层排序
      val sortedSecond = fromMap.toSortedMap()
      val newSecond = LinkedHashMap<String, LinkedHashMap<Int, Double>>()
      for ((from, bucketMap) in sortedSecond) {
        @Suppress("UNCHECKED_CAST")
        newSecond[from] = bucketMap as LinkedHashMap<Int, Double>
      }
      cache[goal] = newSecond
    }
  }

  /**
   * 计算包含初始朝向旋转成本的最短路径成本。
   * 旋转成本以 90° = 1 的单位计入。
   * 如果无法找到路径或点位无效，返回 null。
   */
  fun calcRotationCostForPath(
    rr: RobotRuntime,
    mapCache: SceneMapCache,
    pointNames: List<String>,
    initialHeadDeg: Double,
  ): Double {
    if (pointNames.size < 2) return 0.0

    // 获取坐标序列
    fun getXY(name: String): Pair<Double, Double>? {
      val point = mapCache.getPointCacheByRobotAndName(rr, name)
      return Pair(point!!.point.x, point.point.y)
    }

    var prevHead = initialHeadDeg
    var rotCost = 0.0

    for (i in 0 until pointNames.size - 1) {
      val (x1, y1) = getXY(pointNames[i]) ?: return 0.0
      val (x2, y2) = getXY(pointNames[i + 1]) ?: return 0.0

      var segHead = Math.toDegrees(atan2(y2 - y1, x2 - x1))
      if (segHead < 0) segHead += 360.0

      var diff = abs(prevHead - segHead)
      if (diff > 180) diff = 360 - diff

      rotCost += diff / 45.0
      prevHead = segHead
    }

    return rotCost
  }
}