package com.seer.trick.fleet.traffic.dev

data class MapfTestReq(
  val w: Double,
  val mapDimX: Int,
  val mapDimY: Int, // y 向下为正
  val obstacles: Set<Int>,
  val tasks: Map<String, RobotTask>, // robot name ->
  val goalStops: Int = 0,
  val planScriptFunc: String? = null, // 脚本函数
  val adgScriptFunc: String? = null, // 脚本函数
)
data class VenusLoadTaskReq(
  val sceneName: String,
  val name: String,
)

data class RobotTask(
  val name: String,
  val fromState: Cell,
  val toStates: List<Cell>,
  val stopTimes: Int = 1, // 停多久
)

data class Cell(override val x: Int, override val y: Int) : Location

interface Location {
  val x: Int
  val y: Int
}

interface TimeSteps {
  val timeStart: Long // 开始去这个位置的第一个时间步
  val timeEnd: Long // 到达这个位置的时刻
}

/**
 * 允许跨多个时间步
 * 车头方向，
 * 0，x 正，向右
 * 90，y 正，向下
 * 180，x 负，向左
 * 270，y 负，向上
 */
data class State(
  override val x: Int,
  override val y: Int,
  val head: Int,
  override val timeStart: Long, // 开始去这个位置的第一个时间步
  override val timeEnd: Long, // 到达这个位置的时刻
  val timeNum: Long,
) : Location,
  TimeSteps {

  fun isSameLocation(o: Location): Boolean = x == o.x && y == o.y

  fun isTimeOverlay(o: TimeSteps): Boolean = isTimeOverlay(timeStart, timeEnd, o.timeStart, o.timeEnd)

  companion object {
    fun fromCell(c: Cell): State = State(c.x, c.y, 0, 0, 0, 0)
    fun isTimeOverlay(start1: Long, end1: Long, start2: Long, end2: Long): Boolean = start1 <= end2 && start2 <= end1
  }
}

data class MapfResult(val ok: Boolean, val plans: Map<String, TargetManyPlanResult>, val timeCost: Long)

/**
 * 单目标
 */
data class TargetOnePlanResult(
  val robotName: String,
  val ok: Boolean = true,
  val reason: String? = null,
  val cost: Double = 0.0,
  // val fromState: State,
  // val toState: State,
  val path: List<State>,
  val extra: Any? = null,
)

/**
 * 多目标
 */
data class TargetManyPlanResult(
  val robotName: String,
  val ok: Boolean = true,
  val reason: String? = null,
  val cost: Double = 0.0,
  val steps: List<TargetOnePlanResult>,
  val path: List<State>, // 总路径
  val extra: Any? = null,
)