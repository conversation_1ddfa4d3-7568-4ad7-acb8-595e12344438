package com.seer.trick.fleet.traffic.distributed.plan

import com.seer.trick.fleet.domain.PositionType
import com.seer.trick.fleet.traffic.TrafficTaskRuntime
import com.seer.trick.fleet.traffic.distributed.plan.model.PathAction
import java.util.*

data class RequestInfo(
  val robotName: String, // 机器人编码
  val orderId: String, // 订单号
  val stepId: String, // 步骤号
  val stepIndex: Int, // 步骤索引
  val sceneId: String, // 场景编码
  val mapName: String, // 地图编码
  val groupName: String, // 组编码
  val startX: Double, // 起点 x 坐标
  val startY: Double, // 起点 y 坐标
  var start: String, // 起始点
  var startType: PositionType, // 起点类型
  val target: String, // 目标点
  val startAngle: Int, // 机器人当前角度
  var targetAngle: Int?, // 终点机器人方向角度
  val containerType: String?, // 货架编码
  val containerStartAngle: Int, // 货架角度
  val containerTargetAngle: Int, // 终点货架角度
  val targetCombinedStop: Boolean = false, // 终点路线合并到停车
  val task: TrafficTaskRuntime? = null,
)

data class ResponseInfo(
  val status: ResStatus, // 结果状态
  val code: String, // 编码
  val args: MutableList<String>,

)

enum class ResStatus {
  SUCCESS, // 成功
  FAILED, // 失败
}

class PlanResponse(
  var status: ResStatus = ResStatus.SUCCESS, // 状态
  val code: String, // 编码
  val args: MutableList<String>, // 参数
  var path: MutableList<PathAction> = LinkedList(), // 路径
  val cost: Double = 0.0,
)