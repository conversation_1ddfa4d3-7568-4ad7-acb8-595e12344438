package com.seer.trick.fleet.traffic.distributed.plan

import com.seer.trick.fleet.domain.MoveDirection
import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper
import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper.RIGHT_ANGLE
import com.seer.trick.fleet.traffic.distributed.map.Line
import com.seer.trick.fleet.traffic.distributed.map.Point
import com.seer.trick.fleet.traffic.distributed.plan.model.PointData

/**
 *  全向车运动模型
 * */
class OmniPlanPathAlgorithm(val request: PlanRequest) : PlanPathAlgorithm(request) {

  override fun findRobotHeadings(start: Point, line: Line, robotHeading: Int): List<Int> {
    val robotHeadings = ArrayList<Int>()
    // 判断这条边是否允许此车型通过， 起点不可旋转时计算
    if (!start.rotate || cantRotatePoint.contains(start.pointName)) {
      // 判断是如何行驶，前进 or 后退
      if (line.driveDirection == MoveDirection.Dual) {
        robotHeadings.add(robotHeading)
      }
      if (line.driveDirection == MoveDirection.Forward &&
        AngleHelper.sameAngleInFiveDegree(robotHeading, line.enterDir)
      ) {
        robotHeadings.add(robotHeading)
      }
      if (line.driveDirection == MoveDirection.Backward && AngleHelper.opposeAngle(robotHeading, line.enterDir)) {
        robotHeadings.add(robotHeading)
      }
      // TODO 横移
//      if (line.driveDirection != null &&
//        line.driveDirection.contains(DriveDirection.Shift) &&
//        AngleHelper.rightAngle(robotHeading, line.enterDir)
//      ) {
//        robotHeadings.add(robotHeading)
//      }

      return robotHeadings
    }

    // 线上可以行驶的方向,
    if (line.driveDirection == MoveDirection.Dual) {
      robotHeadings.add(line.enterDir)
      // 线上倒行的方式
      robotHeadings.add(AngleHelper.processAngle(line.enterDir + AngleHelper.DOWN_ANGLE))
      // 线上横行的方式
      robotHeadings.add(AngleHelper.processAngle(line.enterDir + RIGHT_ANGLE))
      robotHeadings.add(AngleHelper.processAngle(line.enterDir - RIGHT_ANGLE))
    } else {
      if (line.driveDirection == MoveDirection.Forward) {
        robotHeadings.add(line.enterDir)
      }
      if (line.driveDirection == MoveDirection.Backward) {
        robotHeadings.add(AngleHelper.processAngle(line.enterDir + AngleHelper.DOWN_ANGLE))
      }
      // TODO 横移，系统暂不支持
//      if (line.driveDirection.contains(DriveDirection.Shift)) {
//        robotHeadings.add(AngleHelper.processAngle(line.enterDir + RIGHT_ANGLE))
//        robotHeadings.add(AngleHelper.processAngle(line.enterDir - RIGHT_ANGLE))
//      }
    }
    return robotHeadings
  }

  override fun costS(pointData: PointData?, line: Line): Double = if (processStop(pointData, line)) 0.0 else 1.5

  override fun costR(rHeading: Int, line: Line): Double =
    if (AngleHelper.sameAngleInFiveDegree(rHeading, line.enterDir)) 0.0 else 0.5 * line.length

  override fun costT(rHeading: Int, robotHeading: Int): Double =
    if (AngleHelper.sameAngleInFiveDegree(rHeading, robotHeading)) {
      0.0
    } else {
      val vectorAngle = AngleHelper.vectorAngle(rHeading, robotHeading)
      val i = vectorAngle / RIGHT_ANGLE
      if (i == 0) 2.0 * 0.5 else i * 2.0
    }
}