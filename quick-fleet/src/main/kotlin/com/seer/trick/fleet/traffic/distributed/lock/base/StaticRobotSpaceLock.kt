package com.seer.trick.fleet.traffic.distributed.lock.base

import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper
import com.seer.trick.fleet.traffic.distributed.helper.GraphHelper
import com.seer.trick.fleet.traffic.distributed.helper.LockHelper
import com.seer.trick.fleet.traffic.distributed.lock.LockType
import com.seer.trick.fleet.traffic.distributed.lock.graph.*
import com.seer.trick.fleet.traffic.distributed.lock.model.*
import com.seer.trick.fleet.traffic.distributed.map.PosType
import com.seer.trick.fleet.traffic.distributed.map.Position
import com.seer.trick.fleet.traffic.distributed.service.DistributedTrafficService
import com.seer.trick.fleet.traffic.distributed.service.model.GroupModel
import org.slf4j.LoggerFactory
import java.util.concurrent.*
import kotlin.math.*

/**
 * 机器人锁闭模型
 * 采用懒加载的模式
 * */
object StaticRobotSpaceLock {

  private val logger = LoggerFactory.getLogger(javaClass)

  private var groupModelMap: MutableMap<String, GroupModel> = ConcurrentHashMap()

  private var robotStaticLockMap: MutableMap<String, Cell> = ConcurrentHashMap()

  private var robotRotateLockMap: MutableMap<String, Cell> = ConcurrentHashMap()

  /**
   * 新增组的模型信息
   * */
  fun addRobotGroupModel(groupModel: GroupModel) {
    if (groupModel.points.size < 4) {
      logger.error(
        "addRobotGroupModel, groupModel points is null, sceneId=${groupModel.sceneId} groupName=${groupModel.groupName}",
      )
      return
    }
    groupModelMap.putIfAbsent(getKey(groupModel.sceneId, groupModel.groupName), groupModel)
  }

  fun getKey(sceneId: String, groupName: String): String = "$sceneId-$groupName"

  /**
   *  修改组的模型信息
   *  注意 : 在调用更新前，机器人的任务必须完成，调用模型成功后才可进行下一次的路径规划
   * */
  fun updateGroupModel(groupModel: GroupModel): Boolean {
    val key = getKey(groupModel.sceneId, groupModel.groupName)
    val group = groupModelMap[key]
    groupModelMap[key] = groupModel
    if (group != null) {
      val oldKey = getKey(group.sceneId, group.groupName)
      robotRotateLockMap.remove(oldKey)
      robotStaticLockMap.remove(oldKey)
    }
    return true
  }

  /**
   *  删除组的模型信息
   * */
  fun removeRobotGroupModel(sceneId: String, groupName: String) {
    val key = getKey(sceneId, groupName)
    groupModelMap.remove(key)
    robotRotateLockMap.remove(key)
    robotStaticLockMap.remove(key)
  }

  fun queryRobotModelByGroupName(sceneId: String, groupName: String): GroupModel? {
    val key = getKey(sceneId, groupName)
    if (groupModelMap.containsKey(key)) {
      return groupModelMap[key]
    }
    return null
  }

  private fun queryCellByGroupName(key: String): Cell? {
    if (robotStaticLockMap.containsKey(key)) {
      return robotStaticLockMap[key]?.copy()
    }
    // 初始化锁闭模型
    logger.info("buildCell, key=$key")
    val group = groupModelMap[key] ?: return null
    buildCellModule(group)
    val cell = robotStaticLockMap[key] ?: return null
    return cell.copy()
  }

  private fun buildCellModule(group: GroupModel) {
    // 检测是否点是否大于等于 4
    var points = group.points
    if (points.size < 4) {
      logger.error("${group.groupName} module polygon points less 4")
    }
    if (points.size == 4) {
      points[0] = Vector(points[0].x - group.lengthSafeDistance, points[0].y + group.widthSafeDistance)
      points[1] = Vector(points[1].x + group.lengthSafeDistance, points[1].y + group.widthSafeDistance)
      points[2] = Vector(points[2].x + group.lengthSafeDistance, points[2].y - group.widthSafeDistance)
      points[3] = Vector(points[3].x - group.lengthSafeDistance, points[3].y - group.widthSafeDistance)
    } else {
      val distance = max(group.lengthSafeDistance, group.widthSafeDistance)
      points = GraphHelper.inflate(points, distance)
    }
    var radius = 0.0
    for (i in 0 until points.size) {
      val p = points[i]
      val distance = sqrt(p.x.pow(2.0) + p.y.pow(2.0))
      radius = max(radius, distance)
    }
    // todo 不用管膨胀情况，由外层处理，给到的就是处理后的模型
    robotStaticLockMap[getKey(group.sceneId, group.groupName)] = staticSpaceLock(points, 0.25)
    robotRotateLockMap[getKey(group.sceneId, group.groupName)] = rotateSpaceLock(radius, 0.25)
  }

  private fun staticSpaceLock(points: MutableList<Vector>, height: Double): Cell {
    val shape = when (points.size) {
      4 -> Rectangle(type = GraphType.RECTANGLE, points = points, box = BoundingBox(points))
      else -> Polygon(
        type = GraphType.POLYGON,
        points = points,
        concave = false,
        subPolygons = mutableListOf(),
        box = BoundingBox(points),
      )
    }
    val layer = Layer(0, height, 0.0, shape)
    return Cell(mutableListOf(layer), Position(0.0, 0.0, "", PosType.UNDEFINE))
  }

  private fun rotateSpaceLock(radius: Double, height: Double): Cell {
    val circle = Circle(type = GraphType.CIRCLE, radius = radius, Vector(0.0, 0.0))
    val layer = Layer(0, height, 0.0, circle)
    return Cell(mutableListOf(layer), Position(0.0, 0.0, "", PosType.UNDEFINE))
  }

  private fun queryRotateCellByGroupName(key: String): Cell? {
    if (robotRotateLockMap.containsKey(key)) {
      return robotRotateLockMap[key]?.copy()
    }
    // 初始化锁闭模型
    logger.info("buildSpaceLock, key=$key")
    val group = groupModelMap[key] ?: return null
    buildCellModule(group)
    val cell = robotRotateLockMap[key] ?: return null
    return cell.copy()
  }

  // 构建在码点上机器人锁闭信息
  fun buildStaticSpaceLock(
    robotName: String,
    sceneId: String,
    groupName: String,
    point: Position,
    heading: Int,
    mapName: String,
  ): SpaceLock {
    val cell = queryCellByGroupName(getKey(sceneId, groupName))
    if (cell == null) {
      logger.error("buildStaticSpaceLock, spaceLock is null, robotName=$robotName, groupName=$groupName")
      DistributedTrafficService.trafficAlarm(
        sceneId = sceneId,
        robotName = robotName,
        code = "T00020001",
        args = listOf(robotName, groupName),
      )
      throw Exception("buildStaticSpaceLock, spaceLock is null")
    }
    // 角度判断
    if (!AngleHelper.sameAngleInFiveDegree(heading, 0)) {
      LockHelper.lockModelRotation(cell, heading)
    }
    LockHelper.lockModelTranslation(cell, point)
    cell.sPosition = point
    return SpaceLock(
      groupName = groupName,
      type = LockType.ROBOT,
      name = robotName,
      cells = mutableListOf(cell),
      mapName = mapName,
    )
  }

  // 构建在码点上旋转机器人锁闭信息
  fun buildRotateSpaceLock(
    robotName: String,
    sceneId: String,
    groupName: String,
    point: Position,
    mapName: String,
  ): SpaceLock {
    val cell = queryRotateCellByGroupName(getKey(sceneId, groupName))
    if (cell == null) {
      logger.error("buildRotateSpaceLock, spaceLock is null, robotName=$robotName, groupName=$groupName")
      DistributedTrafficService.trafficAlarm(
        sceneId = sceneId,
        robotName = robotName,
        code = "T00020001",
        args = listOf(robotName, groupName),
      )
      throw Exception("buildRotateSpaceLock, spaceLock is null")
    }
    LockHelper.lockModelTranslation(cell, point)
    cell.sPosition = point
    return SpaceLock(
      groupName = groupName,
      type = LockType.ROBOT,
      name = robotName,
      cells = mutableListOf(cell),
      mapName = mapName,
    )
  }
}