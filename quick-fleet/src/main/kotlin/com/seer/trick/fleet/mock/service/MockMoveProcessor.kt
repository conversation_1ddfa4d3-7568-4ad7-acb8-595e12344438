package com.seer.trick.fleet.mock.service

import com.seer.trick.BzError
import com.seer.trick.fleet.domain.CurvePoint2D
import com.seer.trick.fleet.domain.GeoHelper
import com.seer.trick.fleet.domain.normalizeRadian
import com.seer.trick.fleet.domain.reverseAzimuth
import com.seer.trick.fleet.mock.*
import com.seer.trick.fleet.mock.service.MockMoveProcessor.handleCurrentPosition
import com.seer.trick.fleet.mock.service.MockRotationProcessor.rotateContainerTo
import com.seer.trick.fleet.mock.service.MockRotationProcessor.rotateRobotBy
import com.seer.trick.fleet.mock.service.MockRotationProcessor.rotateRobotTo
import com.seer.trick.fleet.seer.*
import com.seer.trick.fleet.service.SceneService
import com.seer.trick.helper.BoolHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.NumHelper
import com.seer.trick.helper.getTypeMessage
import org.slf4j.LoggerFactory
import org.tinyspline.BSpline
import java.util.concurrent.TimeUnit
import kotlin.math.*

object MockMoveProcessor {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 执行 3066 的移动逻辑
   */
  fun process3066Move(mr: MockSeerRobotRuntime, ifTaskComplete: Boolean = true) { // TODO ifTaskComplete 默认值改为 false
    try {
      val movesRuntime = mr.moveTaskList.poll(500, TimeUnit.MILLISECONDS)
      if (movesRuntime == null) {
        // 没有任务了，清空移动速度
        mr.currentSpeed = CurrentSpeed()
        return
      }
      val moves = movesRuntime.map { it.step3066 }
      // 检验告警码
      if (mockAlarmStop(mr, moves)) return // moveTaskList 清空了

      try {
        for ((index, moveRuntime) in movesRuntime.withIndex()) {
          // 设置当前任务运行中
          val move = moveRuntime.step3066

          logger.info("${mr.config.name} start run 3066: ${JsonHelper.writeValueAsString(move)}")
          checkTask(mr, move.taskId)
          // TODO update status 封装一个函数
          val taskRuntime = moveRuntime.copy(status = MoveTaskStatus.Running)
          mr.updateCurrentTask(taskRuntime)

          // sourceId 为 SELF_POSITION 时是原地任务,原地任务执行动作即可
          // 机器人移动位置
          if (move.sourceId != "SELF_POSITION") {
            // 线路终点
            val targetPoint = getPointHelper(mr, move.id) ?: break // TODO 为啥 break
            // 线路起点
            val sourcePoint = getPointHelper(mr, move.sourceId) ?: break // TODO 为啥 break
            // 认为只有第一次的时候才需要检测！连续下发的行走过程中一定在线路上

            val handleCurrentPosition = HandleCurrentPositionInfo(mr, sourcePoint, targetPoint, move, 0.0, index)
            val moveStatus = handleCurrentPosition(mr, handleCurrentPosition)
            when (moveStatus) {
              NavProcessingLogic.STOP -> break
              NavProcessingLogic.CONTINUE, NavProcessingLogic.FINISHED -> {} // 无需操作
            }

            if (moveStatus == NavProcessingLogic.CONTINUE) {
              val pathKey = getPathKey(sourcePoint.point.instanceName, targetPoint.point.instanceName)
              mr.splineMap[pathKey]?.let {
                moveAlongPath(mr, move, it.first, it.second, handleCurrentPosition.cureIndex)
              }
            }
            updateRobotPoint(mr, targetPoint)
          }

          // 动作 + 结束
          simulateAction(mr, move)
          mockCharge(mr, move)

          if (ifTaskComplete) {
            updateTaskCompletion(mr)
          }
        }
      } catch (e: TaskException) {
        handleExpectedTaskError(mr, e)
      } catch (e: Exception) {
        handleUnexpectedTaskError(mr, e)
      } finally {
        // TODO 打印一点日志
      }
    } catch (e: InterruptedException) {
      logger.info("运行被中断，${e.getTypeMessage()}")
    } finally {
      mr.moveOrCanceledFinished = true
    }
  }

  /**
   * 模拟充电
   *
   * TODO 优化：到达充电点 2s 后再变为充电中，并增加电量。与 com.seer.trick.fleet.mock.MockService.mockBatteryConsumption 合并
   */
  private fun mockCharge(mr: MockSeerRobotRuntime, move: Step3066) {
    val sceneId = mr.config.sceneId
    SceneService.mustGetSceneById(sceneId)
    if (move.id.startsWith("CP")) {
      val record = mr.record.copy(charging = true)
      mr.setRecord(record)
    }
  }

  /**
   * 执行 3051 的移动逻辑
   *
   * TODO 挪到 process3066 前面
   */
  fun process3051Move(move: Step3051, mr: MockSeerRobotRuntime) {
    try {
      val targetPointHelper = getPointHelper(mr, move.id) ?: return
      val sourcePoint = getCurrentPoint(mr, 2.0)
      if (sourcePoint == null) {
        MockAlarmProcessor.addOrUpdateAlarm(mr, MockSeerRobotAlarm("Error", "52201", "robot out of path"))
        return
      }

      // 判断机器人是不是已经在终点
      if (isSamePosition(mr, targetPointHelper.point)) {
        updateTaskCompletion(mr)
        updateRobotPoint(mr, targetPointHelper)
        return
      }

      val taskList: List<String> =
        MockSmapHelper.findShortestPath(mr.robotId, sourcePoint, targetPointHelper.point.instanceName) ?: run {
          logger.debug("仿真机器人 ${mr.config.name} 无法在地图上找到到达目标点：${move.id} 的路径")
          MockAlarmProcessor.addOrUpdateAlarm(
            mr,
            MockSeerRobotAlarm("Error", "52700", "can not find a feasible path"),
          )
          return
        }
      val moves: MutableList<MoveTaskRuntime> = mutableListOf()
      taskList.forEachIndexed { index, id ->
        val reachAngle = if (index == taskList.size - 1) -1.0 else Math.PI
        val taskMove = Step3066(
          id = id,
          taskId = move.taskId,
          sourceId = if (index == 0) id else taskList[index - 1],
          reachAngle = reachAngle,
        )
        moves.add(MoveTaskRuntime(step3066 = taskMove, taskId = taskMove.taskId, status = MoveTaskStatus.Init))
      }

      mr.moveTaskList.put(moves)
      while (mr.moveTaskList.isNotEmpty()) {
        process3066Move(mr, false)
      }
      updateTaskCompletion(mr)
      updateRobotPoint(mr, targetPointHelper)
    } catch (e: TaskException) {
      handleExpectedTaskError(mr, e)
    } catch (e: Exception) {
      handleUnexpectedTaskError(mr, e)
    }
  }

  /**
   * 处理机器人当前位置不同所造成一系列情况
   * 包括机器人位于起点、起点位于终点、机器人不在起点也不在终点即偏离路线
   * 偏离路线又分为下发的任务为一条路线，下发的任务为一个点
   * @param tc TraceContext
   * @param handleCurrentPosition 包含属性有，机器人、源id、目标id、移动任务、占线路的百分比
   */
  private fun handleCurrentPosition(
    mr: MockSeerRobotRuntime,
    handleCurrentPositionInfo: HandleCurrentPositionInfo,
  ): NavProcessingLogic = when {
    // 当前位置是目标点
    isSamePosition(handleCurrentPositionInfo.mr, handleCurrentPositionInfo.targetPoint.point) -> {
      updateRobotPoint(mr, handleCurrentPositionInfo.targetPoint)
      updateTaskCompletion(mr)
      handleCurrentPositionInfo.cureIndex = 1.0 // 机器人已经在终点了
      NavProcessingLogic.FINISHED // 机器人已经在终点了，结束本次循环
    }

    // 当前位置是起点
    isSamePosition(handleCurrentPositionInfo.mr, handleCurrentPositionInfo.sourcePoint.point) -> {
      updateRobotPoint(mr, handleCurrentPositionInfo.sourcePoint)
      NavProcessingLogic.CONTINUE // 继续这次任务
    }

    // 当前位置在线路上
    isOnThePath(mr, handleCurrentPositionInfo) -> {
      logger.info("mr ${handleCurrentPositionInfo.mr.config.name} is on the path")
      NavProcessingLogic.CONTINUE
    }

    // 当前位置偏离路线
    else -> {
      // TODO 仿真日志单独放
      // TODO 仿真日志结构 机器人名 + 事件/函数名 + 描述
      logger.info("mr ${handleCurrentPositionInfo.mr.config.name} is deviation the path")
      handleDeviation(mr, handleCurrentPositionInfo)
    }
  }

  /**
   * 机器人偏移路线时的处理逻辑
   *
   * TODO 重新树立一下逻辑和写法
   */
  private fun handleDeviation(
    mr: MockSeerRobotRuntime,
    handleCurrentPositionInfo: HandleCurrentPositionInfo,
  ): NavProcessingLogic {
    val closestPathList = getClosePath(mr)
    return closestPathList.firstOrNull { e ->
      e.pathHelper.path.endPos.instanceName == handleCurrentPositionInfo.targetPoint.point.instanceName ||
        e.pathHelper.path.startPos.instanceName == handleCurrentPositionInfo.sourcePoint.point.instanceName ||
        e.pathHelper.path.startPos.instanceName == handleCurrentPositionInfo.targetPoint.point.instanceName ||
        e.pathHelper.path.endPos.instanceName == handleCurrentPositionInfo.sourcePoint.point.instanceName
    }?.let {
      // 如果当前位置在目标路径附近路径上，则移动到目标点位上，离起点近就去起点，终点近就去终点
      logger.info("mr ${handleCurrentPositionInfo.mr.config.name} is close to path")
      // 分别计算到起点和终点的距离
      val sourcePoint = MockSmapHelper.getPointHelper(
        mr.robotId,
        handleCurrentPositionInfo.sourcePoint.point.instanceName,
      )
      val sourceDis = hypot(sourcePoint!!.point.pos.x - mr.record.x, sourcePoint.point.pos.y - mr.record.y)
      val targetPoint = MockSmapHelper.getPointHelper(
        mr.robotId,
        handleCurrentPositionInfo.targetPoint.point.instanceName,
      )
      val targetDis = hypot(targetPoint!!.point.pos.x - mr.record.x, targetPoint.point.pos.y - mr.record.y)
      if (sourceDis < targetDis) {
        handleCurrentPositionInfo.cureIndex = 0.0
        logger.info(
          "mr ${mr.config.name} moving towards path start ${sourcePoint.point.instanceName}, distance=$sourceDis",
        )
        moveToClosestPath(
          handleCurrentPositionInfo.move,
          handleCurrentPositionInfo.mr,
          sourcePoint.point.pos.x,
          sourcePoint.point.pos.y,
          it.pathHelper,
        )
      } else {
        handleCurrentPositionInfo.cureIndex = 1.0
        logger.info(
          "mr ${mr.config.name} moving towards path end ${targetPoint.point.instanceName}, distance=$targetDis",
        )
        moveToClosestPath(
          handleCurrentPositionInfo.move,
          handleCurrentPositionInfo.mr,
          targetPoint.point.pos.x,
          targetPoint.point.pos.y,
          it.pathHelper,
        )
      }
      NavProcessingLogic.CONTINUE
    } ?: run {
      logger.info("mr ${handleCurrentPositionInfo.mr.config.name} out of path")
      MockAlarmProcessor.addOrUpdateAlarm(
        handleCurrentPositionInfo.mr,
        MockSeerRobotAlarm("Error", "52201", "robot out of path"),
      )
      NavProcessingLogic.STOP
    }
  }

  /**
   * 移动机器人到最近的路径点
   *
   * @param tc TraceContext
   * @param mr MockSeerRobotRuntime
   * @param targetX 目标点的 x 坐标
   * @param targetY 目标点的 y 坐标
   */
  private fun moveToClosestPath(
    move: Step3066,
    mr: MockSeerRobotRuntime,
    targetX: Double,
    targetY: Double,
    pathHelper: PathHelper,
  ) {
    // 计算目标弧度
    val targetRad = GeoHelper.normalizeRadian(atan2(targetY - mr.record.y, targetX - mr.record.x))
    val t0 = GeoHelper.normalizeRadian(targetRad + PI)
    val robotTheta = GeoHelper.normalizeRadian(mr.record.theta)
    // 挑一个角度小的转
    if (GeoHelper.normalizeRadian(targetRad - robotTheta) <= GeoHelper.normalizeRadian(t0 - robotTheta)) {
      rotateRobotTo(mr, move, targetRad)
    } else {
      rotateRobotTo(mr, move, t0)
    }

    val isMovingForward = getMoveMethod(move, pathHelper)
    val maxSpeed = if (isMovingForward) mr.moveConfig?.maxSpeed!! else mr.moveConfig?.maxBackSpeed!!

    // 按照 vx, vy 的速度行走
    while (!isSamePosition(mr, targetX, targetY, 0.05)) { // 使用更小的阈值
      logger.debug("Move to closest path, x: ${mr.record.x}, y: ${mr.record.y}, targetX: $targetX, targetY: $targetY")
      val distanceToTarget = hypot(targetX - mr.record.x, targetY - mr.record.y)
      val currentAngle = atan2(targetY - mr.record.y, targetX - mr.record.x) // 重新计算当前角度

      // 动态调整步长，确保不会走过 TODO 0.05 还是 0.02
      val stepSize = min(maxSpeed * MockService.mockSpeedFactor * 0.05, distanceToTarget)
      val x = mr.record.x + stepSize * cos(currentAngle)
      val y = mr.record.y + stepSize * sin(currentAngle)

      // 如果距离非常接近，直接设置为目标点，避免走过头
      if (distanceToTarget < 0.05) {
        mr.setRecord(mr.record.copy(x = targetX, y = targetY))
        break
      } else {
        mr.setRecord(mr.record.copy(x = x, y = y))
      }
      Thread.sleep(50) // 减少每次移动间隔，增加精度
    }
  }

  fun addSplineMap(mr: MockSeerRobotRuntime, key: String, value1: PathHelper, value2: BSpline) {
    mr.splineMap[key] = Pair(value1, value2)
  }

  fun addCurvePoint2DMap(mr: MockSeerRobotRuntime, key: String, value: List<CurvePoint2D>) {
    mr.curvePoint2DMap[key] = value
  }

  /**
   * 判断两个点是否在同一位置，允许一定地偏离阈值
   *
   * @param x1 第一个点的 x 坐标
   * @param y1 第一个点的 y 坐标
   * @param x2 第二个点的 x 坐标
   * @param y2 第二个点的 y 坐标
   * @param threshold 允许的偏离阈值
   * @return 如果两点之间的距离小于或等于阈值，返回 true，否则返回 false
   */
  private fun isSamePosition(x1: Double, y1: Double, x2: Double, y2: Double, threshold: Double = 0.01): Boolean {
    val distance = sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1))
    return distance <= threshold
  }

  private fun isSamePosition(mr: MockSeerRobotRuntime, point: SmapAdvancedPoint, threshold: Double = 0.01): Boolean =
    isSamePosition(mr.record.x, mr.record.y, point.pos.x, point.pos.y, threshold)

  private fun isSamePosition(mr: MockSeerRobotRuntime, x2: Double, y2: Double, threshold: Double = 0.01): Boolean =
    isSamePosition(mr.record.x, mr.record.y, x2, y2, threshold)

  /**
   * 根据故障码仿真机器人故障
   */
  private fun mockAlarmStop(mr: MockSeerRobotRuntime, moves: List<Step3066>): Boolean {
    if (mr.record.alarms.any { it.code == "0000" }) {
      logger.info("动作被取消动作被取消")

      moves.first().let {
        mr.currentTask?.let { task ->
          val taskRuntime = task.copy(status = MoveTaskStatus.Cancelled)
          mr.updateCurrentTask(taskRuntime)
        }
      }
      mr.moveTaskList.clear()
      return true
    } else if (mr.record.alarms.any { it.code == "1111" }) {
      logger.info("动作故障")
      moves.first().let {
        mr.currentTask?.let { task ->
          val taskRuntime = task.copy(status = MoveTaskStatus.Failed)
          mr.updateCurrentTask(taskRuntime)
        }
      }
      mr.moveTaskList.clear()
      return true
    }
    return false
  }

  /**
   * 判断机器人是否在下发路径的路线上，并更新位于的索引
   */
  private fun isOnThePath(
    mr: MockSeerRobotRuntime,
    handleCurrentPositionInfo: HandleCurrentPositionInfo,
  ): Boolean = if (handleCurrentPositionInfo.index != 0) {
    // 行走的路程中一定在路径上
    true
  } else {
    getClosePath(mr)
      .firstOrNull {
        it.pathHelper.path.instanceName ==
          handleCurrentPositionInfo.sourcePoint.point.instanceName + "-" +
          handleCurrentPositionInfo.targetPoint.point.instanceName
      }
      ?.takeIf { it.distance < 0.3 }
      ?.let { closestPath ->
        handleCurrentPositionInfo.cureIndex = closestPath.index.toDouble() / (closestPath.pathHelper.points.size - 1)
        true
      } ?: false
  }

  /**
   * 获取距离机器人最近的点
   */
  fun getCurrentPoint(mr: MockSeerRobotRuntime, close: Double = 1.0): String? =
    MockSmapHelper.getClosestPoint(mr, close)

  fun putMoveTaskList(mr: MockSeerRobotRuntime, moves: List<MoveTaskRuntime>) {
    mr.moveTaskList.put(moves)
  }

  /**
   * 更新机器人站点
   */
  private fun updateRobotPoint(mr: MockSeerRobotRuntime, pointHelper: PointHelper) {
    // mr.record.apply { // 不会是 null，为啥要 apply
    mr.setRecord(
      mr.record.copy(
        point = pointHelper.point.instanceName,
      ),
    )
    // }
    MockStore.updateMockRobotRecordAsync(mr.record)
  }

  /**
   * 库位/站点 -> pointHelper
   */
  fun getPointHelper(mr: MockSeerRobotRuntime, pointId: String): PointHelper? =
    MockSmapHelper.getPointHelper(mr.robotId, pointId).also {
      if (it == null) {
        logger.debug("仿真机器人 ${mr.config.name} 无法在地图上找到目标点：$pointId")
        MockAlarmProcessor.addOrUpdateAlarm(mr, MockSeerRobotAlarm("Error", "52701", "can not find target id"))
      }
    }

  /**
   * 预期以外的一些异常，执行路径导航失败处理 Error
   */
  private fun handleUnexpectedTaskError(mr: MockSeerRobotRuntime, e: Exception) {
    logger.error("Unexpected error during task execution", e)
    // 当前任务设置为故障
    mr.currentTask?.let { task ->
      val taskRuntime = task.copy(status = MoveTaskStatus.Failed)
      mr.updateCurrentTask(taskRuntime)
    }
    // 未执行的其他任务设置为取消还是故障呢？
    mr.updateTasksStatus { MoveTaskStatus.Failed }
  }

  /**
   * 路径导航预期内的异常，目前仅包括主动设置动作失败，主动设置取消
   * TODO 是否要在这里清理机器人点位
   */
  private fun handleExpectedTaskError(mr: MockSeerRobotRuntime, e: TaskException) {
    logger.info("Expected error during task execution", e)
    mr.currentTask?.let {
      val taskRuntime = when (e.kind) {
        TaskExceptionKind.Cancelled -> {
          mr.updateTasksStatus { MoveTaskStatus.Cancelled }
          it.copy(status = MoveTaskStatus.Cancelled)
        }

        TaskExceptionKind.Failed -> {
          mr.updateTasksStatus { MoveTaskStatus.Failed }
          it.copy(status = MoveTaskStatus.Failed)
        }
      }
      mr.updateCurrentTask(taskRuntime)
    }
  }

  /**
   * 获取机器人当前最近的线路，通过遍历站点与当前位置的 x，y 进行比较
   */
  private fun getClosestPath(mr: MockSeerRobotRuntime, close: Double = 2.0): List<ClosestPath> =
    MockSmapHelper.getClosestPathList(mr, close).also {
      if (it.isEmpty()) {
        // TODO 记录告警
        throw BzError(
          "errRobotAwayPath",
          "机器人名 ${mr.config.name} 偏离路线",
        )
      }
    }

  /**
   * 获取机器人附近的路线
   */
  private fun getClosePath(mr: MockSeerRobotRuntime, close: Double = 1.0): List<ClosestPath> =
    MockSmapHelper.getClosePathList(mr, close).also {
      if (it.isEmpty()) {
        // TODO 记录告警
        throw BzError(
          "errRobotAwayPath",
          "机器人名 ${mr.config.name} 偏离路线",
        )
      }
    }

  /**
   * 任务完成
   */
  private fun updateTaskCompletion(mr: MockSeerRobotRuntime) {
    mr.currentTask?.let { task ->
      val taskRuntime = task.copy(status = MoveTaskStatus.Completed)
      mr.updateCurrentTask(taskRuntime)
    }
  }

  /**
   * 根据一条线路的起点与终点名，获取这条线路的 pathHelper
   */
  fun getPathHelper(mr: MockSeerRobotRuntime, from: String, to: String): PathHelper? =
    MockSmapHelper.getPathByFromAndTo(mr.robotId, from, to).also {
      if (it == null) {
        logger.debug("仿真机器人 ${mr.config.name} 无法找到从 $from 到 $to 的路径")
      }
    }

  fun getPathKey(from: String, to: String): String = "$from-$to"

  /**
   * 模拟沿着线路行走
   */
  private fun moveAlongPath(
    mr: MockSeerRobotRuntime,
    move: Step3066,
    pathHelper: PathHelper,
    spline: BSpline,
    currentIndex: Double = 0.0,
    endIndex: Double = 1.0,
  ) {
    // 判断正走还是倒走
    val isMovingForward = getMoveMethod(move, pathHelper)
    val distance = SmapCurveHelper.getLength(spline, pathHelper.path)
    val steps =
      calculateSteps(distance, if (isMovingForward) mr.moveConfig?.maxSpeed!! else mr.moveConfig?.maxBackSpeed!!)

    // 起点或者终点在线路上
    val curvePoint2D = if (currentIndex != 0.0 || endIndex != 1.0) {
      SmapCurveHelper.getPathPositionAndAngle(spline, steps, pathHelper.path.className, currentIndex, endIndex)
    } else {
      mr.curvePoint2DMap[pathHelper.path.instanceName] ?: SmapCurveHelper.getPathPositionAndAngle(
        spline,
        steps,
        pathHelper.path.className,
        currentIndex,
      )
    }
    // 根据正走还是倒走的不同，其目标角度不同
    val forward = getMoveMethod(move, pathHelper)

    // 获取并计算切线角度 (考虑正反向)
    val tangentRad = getTangentRad(curvePoint2D.first().tangent, forward)

    // 模拟 goodsDir
    // simulateGoodsDirRotation(mr, move)

    // 执行旋转 (正走或倒走)
    rotateRobotTo(mr, move, tangentRad)

    // TODO 仿真：机器人离开起点到什么时候上报的就没有 point 了？
    curvePoint2D.forEachIndexed { index, it ->
      // 任务中途可能被取消或暂停
      checkTask(mr, move.taskId)
      if (!(curvePoint2D.size == 2 && index == 0)) {
        Thread.sleep(20)
      }
      updateRobotPosition(mr, it, forward, move)
    }
  }

  /**
   * 计算步数，确保步数至少为 2
   *
   * 每秒 50 步，每步 20 ms
   */
  fun calculateSteps(distance: Double, velocity: Double): Int {
    val steps = ceil(distance / (velocity * MockService.mockSpeedFactor) * 1000 / 20).toInt()
    return maxOf(steps, 2) // 确保至少有 2 步
  }

  /**
   * 获取机器人当前的运动方向，正走或倒走
   */
  fun getMoveMethod(move: Step3066, pathHelper: PathHelper): Boolean = when (move.method) {
    "forward" -> true // 正走
    "backward" -> false // 倒走
    null -> { // 无指定时，根据路径的朝向判断
      val direction = pathHelper.path.property.firstOrNull { e -> e.key == "direction" }
      direction?.int32Value == 0 // 0 表示正走，1 表示倒走
    }

    else -> true // 默认正走，处理未知情况
  }

  /**
   * 根据正走或倒走，调整切线角度
   */
  private fun getTangentRad(tangent: Double, forward: Boolean): Double = if (forward) {
    tangent.normalizeRadian() // 正走，直接使用切线
  } else {
    tangent.reverseAzimuth() // 倒走，切线方向偏转 180 度 TODO 用 GeoHelper
  }

  private fun logCancellation(mr: MockSeerRobotRuntime, taskId: String) {
    logger.debug("仿真机器人 ${mr.config.name} 处理移动过程中被取消 taskId=$taskId")
  }

  /**
   * 更新机器人的位置与朝向
   *
   * 这段代码很容器踩坑，这里需要计算底盘旋转的度数 amrMoveTheta，然后把 amrMoveTheta 加到容器上
   */
  private fun updateRobotPosition(
    mr: MockSeerRobotRuntime,
    positionAndAngle: CurvePoint2D,
    isMovingForward: Boolean,
    move: Step3066,
  ) {
    val speed = if (isMovingForward) mr.moveConfig?.maxSpeed!! else -mr.moveConfig?.maxBackSpeed!!
    // 更新机器人移动速度
    mr.currentSpeed = mr.currentSpeed.copy(speed = speed * MockService.mockSpeedFactor)

    val theta = getTangentRad(positionAndAngle.tangent, isMovingForward)

    // 底盘旋转弧度
    val delta = GeoHelper.normalizeRadian(theta - mr.record.theta)

    mr.setRecord(
      // 未到达点位时，rbk 上报的当前点位都是空
      mr.record.copy(
        x = positionAndAngle.x,
        y = positionAndAngle.y,
        point = "",
        theta = theta,
      ),
    )
    val h = MockRotationProcessor.holdContainerDir(mr, move)
    MockRotationProcessor.updateContainerDirAndPosition(mr, delta, h)
    MockStore.updateMockRobotRecordAsync(mr.record)
  }

  /**
   * 触发任务异常，包括取消与故障
   */
  fun triggerTaskError(
    mr: MockSeerRobotRuntime,
    targetStatus: MoveTaskStatus,
    clearQueue: Boolean = true,
  ) {
    synchronized(mr.lock) {
      mr.moveOrCanceledFinished = false
      mr.currentTask?.let {
        mr.updateCurrentTask(it.copy(status = targetStatus))
      }
      mr.updateTasksStatus { targetStatus }

      if (clearQueue) mr.moveTaskList.clear()
    }

    while (!mr.moveOrCanceledFinished) {
      Thread.sleep(200)
    }
    mr.moveOrCanceledFinished = true
  }

  /**
   * 模拟动作
   */
  private fun simulateAction(mr: MockSeerRobotRuntime, move: Step3066) {
    // 仿真容器原地旋转的指令
    if (move.increaseSpinAngle != null) {
      // TODO 貌似永远调用不到，暂时不改了
      val toTheta = (mr.record.theta + move.increaseSpinAngle) % (2 * Math.PI)
      rotateContainerTo(mr, move, toTheta)
    }
    // 老的仿真机器人原地旋转的指令
    if (move.moveAngle != null && move.speedW != null) mockGoByOdometer(mr, move, move.moveAngle, move.speedW)

    // 新的机器人旋转脚本
    if (move.operation == "Script" && move.scriptName == "jack.py") mockJackPy(move, mr)

    val pointName = mr.record.point

    // 最后的路径需要转向，以最终点的角度为准
    if (move.reachAngle < 0 && pointName != null) {
      getPointHelper(mr, pointName)?.point?.let {
        if (it.ignoreDir != true) {
          rotateRobotTo(mr, move, it.dir ?: 0.0)
        }
      }
    }

    // 切换地图
    if (move.operation == "Script" && move.scriptName == "syspy/switchMap.py") {
      if (move.scriptArgs != null) {
        val mapName = move.scriptArgs["map"] as String
        val switchPoint = move.scriptArgs["switchPoint"] as String
        val mapNameWithSuffix = "$mapName.smap" // 切换地图时，发送的是不带 ".smap" 后缀的地图名称。
        MockService.initOneRobotByPoint(robotId = mr.robotId, mapNameWithSuffix, switchPoint)
      } else {
        logger.debug("Switch map script without args: $move")
      }
    }

    // 动作 + 结束
    val operation = move.operation ?: move.binTask
    logger.debug("Simulating action $operation for ${mr.config.name}")

    operation?.let {
      val mockActionCost = MockService.config.actionsCosts.actions[it]?.cost
        ?: MockService.config.actionsCosts.defaultCost
      Thread.sleep((mockActionCost * 1000 / MockService.mockSpeedFactor).toLong())

      // 取放货时更新货物姿态、背篓
      MockContainersProcessor.postProcessRobotLoads(mr, move)
    }
  }

  private fun mockGoByOdometer(
    mr: MockSeerRobotRuntime,
    move: Step3066,
    theta: Double,
    speedW: Double,
  ) {
    logger.debug("GoByOdometer: ${JsonHelper.writeValueAsString(move)}, theta=$theta, speedW=$speedW")
    val dir = if (speedW > 0) 1 else -1
    rotateRobotBy(mr, move, theta, dir)
  }

  private fun mockJackPy(move: Step3066, mr: MockSeerRobotRuntime) {
    // https://seer-group.coding.net/p/order_issue_pool/assignments/issues/11970/detail
    val robotTargetAngle = NumHelper.anyToDouble(move.scriptArgs?.get("robot_rotate_angle")) // [0, 360] 绝对角度，不是差值
    val shelfTargetAngle = NumHelper.anyToDouble(move.scriptArgs?.get("shelf_rotate_angle"))
    val robotRotateDirection = NumHelper.anyToInt(move.scriptArgs?.get("robot_rotate_direction"))
    val shelfRotateDirection = NumHelper.anyToInt(move.scriptArgs?.get("shelf_rotate_direction"))
    val spin = BoolHelper.anyToBool(move.scriptArgs?.get("spin")) // 为 null 是默认为 false

    // 转底盘
    if (robotTargetAngle != null) {
      val toTheta = GeoHelper.normalizeRadian(MockGeoHelper.angleToRadians(robotTargetAngle))
      // 机器人转，托盘不单独转，是否同步转看 spin 参数
      rotateRobotTo(mr, move, toTheta, robotRotateDirection, spin)
    }

    // 转料架
    if (shelfTargetAngle != null && spin) {
      // 托盘转机器人不转
      val toTheta = GeoHelper.normalizeRadian(MockGeoHelper.angleToRadians(shelfTargetAngle))
      rotateContainerTo(mr, move, toTheta, shelfRotateDirection)
    }
  }

  /**
   * 判断导航任务是否取消或暂停或者故障
   * TODO 直接抛出异常是否恰当？是否可以 return 枚举？
   */
  fun checkTask(mr: MockSeerRobotRuntime, taskId: String) {
    synchronized(mr.lock) {
      if (mr.tasks[taskId]?.status == MoveTaskStatus.Cancelled ||
        mr.tasks[taskId]?.status == MoveTaskStatus.Cancelling
      ) {
        throw TaskException(TaskExceptionKind.Cancelled, "Task Canceled:$taskId") // TODO 要抛异常吗？而且打日志了
      }
      if (mr.tasks[taskId]?.status == MoveTaskStatus.Failed || mr.tasks[taskId]?.status == MoveTaskStatus.Failing) {
        throw TaskException(TaskExceptionKind.Failed, "Task Failed:$taskId") // TODO 要抛异常吗？而且打日志了
      }
      if (mr.emc && mr.config.cancelAfterEmc) {
        // TODO 外面对 TaskException 有特殊处理，这里要不改为 TaskException
        throw BzError("errTaskCanceledAfterEmc", taskId)
      }
      if (mr.movePaused || (mr.emc && !mr.config.cancelAfterEmc) || mr.softEmc || mr.blocked) {
        // 要放到 synchronized 里，防止不是 owner
        mr.lock.wait()
      }
    }
  }
}

// TODO 重命名
enum class NavProcessingLogic {
  FINISHED,
  STOP,
  CONTINUE,
}

// TODO 重命名
data class HandleCurrentPositionInfo(
  var mr: MockSeerRobotRuntime,
  var sourcePoint: PointHelper,
  var targetPoint: PointHelper,
  var move: Step3066,
  var cureIndex: Double,
  var index: Int,
)