# Venus系统窄边通行功能实现

## 概述

本文档描述了在Venus系统中实现窄边通行功能的详细过程。窄边通行是一种控制容器以窄边向前方向进入路径的配置，需配合换向点使用。该功能参考了distributed系统的实现方法。

## 核心概念

### 窄边通行 (Narrow Path)
- **定义**: 控制容器以窄边向前方向进入路径的配置
- **用途**: 在狭窄通道中确保容器以最小宽度通过，提高通行效率
- **配合**: 需要与换向点配合使用

### 关键参数
- **narrowDir**: 窄边方向偏差角度，根据容器尺寸计算
- **narrowPath**: 路径标记，表示该路径需要窄边通行
- **containerDir**: 容器在该路径上的指定方向

## 实现架构

### 1. 核心组件

#### VenusNarrowPathHelper
- **位置**: `VenusNarrowPathHelper.kt`
- **功能**: 窄边通行的核心辅助类
- **主要方法**:
  - `addContainerModel()`: 添加容器模型
  - `queryContainerModelNarrowDir()`: 查询容器窄边方向
  - `isNarrowPath()`: 检查路径是否为窄边通行路径
  - `calculateNarrowPathContainerHeadings()`: 计算窄边通行时的容器朝向
  - `validateNarrowPathConstraints()`: 验证窄边通行约束

#### 数据结构扩展
- **RobotInfo**: 添加了`containerType`和`narrowDir`字段
- **PathFindingOption**: 添加了`enableNarrowPath`开关
- **SceneConfig**: 添加了`enableNarrowPath`配置项

### 2. 集成点

#### 路径规划算法 (LowResolver)
- **位置**: `LowResolver.kt`
- **修改**: 在`addNeighborByRotation`方法中集成窄边通行逻辑
- **功能**: 
  - 检测窄边通行路径
  - 计算容器的特定朝向
  - 验证窄边通行约束

#### 规划服务 (PlanService)
- **位置**: `PlanService.kt`
- **修改**: 在`collectRequests`方法中设置容器类型和窄边方向
- **功能**: 传递窄边通行相关参数

#### 交通服务 (VenusTrafficService)
- **位置**: `VenusTrafficService.kt`
- **修改**: 
  - 初始化容器模型信息
  - 监听容器类型变更事件
  - 清理资源

## 实现细节

### 1. 容器模型管理

```kotlin
data class ContainerModel(
  val sceneId: String,
  val typeName: String,
  val length: Double,
  val width: Double,
  val height: Double,
  val radius: Double,
  val narrowDir: Int, // 窄边方向
)
```

### 2. 窄边方向计算

```kotlin
fun calculateNarrowDir(length: Double, width: Double): Int {
  return if (length > width) 0 else AngleHelper.RIGHT_ANGLE
}
```

### 3. 容器朝向计算

```kotlin
fun calculateNarrowPathContainerHeadings(
  pathEnterDir: Int,
  narrowDir: Int,
  containerDir: Int?
): List<Int> {
  val headings = mutableListOf<Int>()
  
  if (containerDir != null) {
    headings.add(containerDir)
    return headings
  }
  
  headings.add(AngleHelper.processAngle(pathEnterDir + narrowDir))
  headings.add(AngleHelper.processAngle(pathEnterDir + AngleHelper.DOWN_ANGLE + narrowDir))
  
  return headings
}
```

### 4. 路径规划集成

在路径规划过程中，当检测到窄边通行路径时：
1. 获取容器类型和窄边方向
2. 计算期望的容器朝向
3. 选择最接近当前朝向的期望朝向
4. 验证窄边通行约束

## 配置说明

### 场景配置
在`SceneConfig`中添加了`enableNarrowPath`配置项：
```kotlin
val enableNarrowPath: Boolean? = null // 是否启用窄边通行功能，null表示使用默认值true
```

### 路径配置
路径需要设置`containerShortSideAhead = true`来标记为窄边通行路径。

## 测试

### 单元测试
- **位置**: `VenusNarrowPathTest.kt`
- **覆盖**: 
  - 容器模型管理
  - 窄边方向计算
  - 路径检测
  - 容器朝向计算
  - 约束验证

### 测试用例
1. 容器模型的添加和查询
2. 窄边方向的正确计算
3. 窄边通行路径的识别
4. 容器朝向的计算
5. 机器人旋转能力检查

## 使用方法

### 1. 启用功能
在场景配置中设置：
```json
{
  "enableNarrowPath": true
}
```

### 2. 配置路径
在地图编辑器中，将需要窄边通行的路径设置为：
```json
{
  "containerShortSideAhead": true
}
```

### 3. 配置容器类型
确保容器类型配置了正确的尺寸信息，系统会自动计算窄边方向。

## 注意事项

1. **兼容性**: 该功能与distributed系统的窄边通行功能保持兼容
2. **性能**: 窄边通行计算会增加路径规划的复杂度，但影响较小
3. **配置**: 需要正确配置容器类型和路径标记
4. **调试**: 可以通过日志查看窄边通行的计算过程

## 未来改进

1. **优化算法**: 进一步优化窄边通行的计算效率
2. **可视化**: 在界面上显示窄边通行的状态
3. **更多约束**: 支持更复杂的窄边通行约束条件
4. **动态调整**: 支持运行时动态调整窄边通行参数
