package com.seer.trick.fleet.device.lift.adapter

import com.seer.trick.fleet.device.lift.*
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.SceneRuntime
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.Executors
import java.util.concurrent.Future

class LiftAdapterMock(val config: SceneLift) : LiftAdapter() {

  private val logger = LoggerFactory.getLogger(javaClass)

  @Volatile
  var record = MockLiftRecord(id = config.id, name = config.name)
    private set

  private val executor = Executors.newSingleThreadExecutor()

  @Volatile
  private var openF: Future<*>? = null

  @Volatile
  private var closeF: Future<*>? = null

  override fun init(sr: SceneRuntime) {
    logger.info("Init lift: $config")
    super.init(sr)
    // todo：仿真电梯的 record 是否要持久化
    // 仿真电梯，即使有多个门，也当一个门处理，同时开，同时关。

    // 初始化一些运行数据。
    updateRecord(
      UpdateLiftRecord(
        currentFloor = record.currentFloor ?: "0",
        doors = (0..config.doorNum).map { LiftDoorStatus.Closed }, // 将所有电梯的门初始化为 Closed 状态。
      ),
    )
  }

  override fun report(): LiftAdapterReport = LiftAdapterReport(
    online = record.online,
    fault = record.fault,
    faultMsg = record.faultMsg,
    autoMode = record.autoMode,
    currentFloor = record.currentFloor?.toInt(),
    targetFloor = record.targetFloor?.toInt(),
    doors = record.doors,
    people = record.people,
    timestamp = Date(),
  )

  override fun gotoOpenDoor(rr: RobotRuntime?, targetFloor: Int, remark: String) {
    synchronized(this) {
      logger.info(
        "Mock lift [${config.id}][${config.name}]: ask me to gotoOpenDoor, targetFloor=$targetFloor, remark=$remark",
      )
      closeF?.cancel(true)
      closeF = null

      if (openF != null) return
      openF = executor.submit {
        try {
          // 收到请求，更新电梯状态。
          // 如果电梯在期望的楼层，则将电梯门置为 Opening，否则置为 Closed .
          val doorStatus =
            if (record.currentFloor == targetFloor.toString()) LiftDoorStatus.Opening else LiftDoorStatus.Closed
          updateRecord(
            UpdateLiftRecord(
              targetFloor = targetFloor.toString(),
              doors = record.doors.map { doorStatus }, // 强改，且将两扇门当一扇门处理。
            ),
          )

          // 必要时，上下楼，到达之后 opening
          if (record.currentFloor != targetFloor.toString()) {
            Thread.sleep(config.changeFloorCost)
            logger.info("Mock lift [${config.id}][${config.name}]: gotoOpenDoor, reached target floor .")
            updateRecord(
              UpdateLiftRecord(
                currentFloor = targetFloor.toString(), // 电梯到达目标楼层。
                targetFloor = targetFloor.toString(), // 再写一次吧，万一期间被人为修改了呢。
                doors = record.doors.map { LiftDoorStatus.Opening }, // 强改，且将两扇门当一扇门处理。
              ),
            )
          }

          // 一段时间后，电梯在目标楼层开门到位
          Thread.sleep(config.openDoorCost)
          logger.info("Mock lift [${config.id}][${config.name}]: gotoOpenDoor, reached and door Opened .")
          updateRecord(
            UpdateLiftRecord(
              currentFloor = targetFloor.toString(), // 电梯到达目标楼层。
              targetFloor = targetFloor.toString(), // 再写一次吧，万一期间被人为修改了呢。
              doors = record.doors.map { LiftDoorStatus.Opened }, // 开门到位了
            ),
          )
        } catch (e: Exception) {
          logger.error("Mock lift [${config.id}][${config.name}]: gotoOpenDoor failed", e)
          return@submit
        }
      }
    }
  }

  override fun closeDoor(rr: RobotRuntime?, remark: String) {
    synchronized(this) {
      logger.info("Mock lift [${config.id}][${config.name}]: ask me to close door, remark=$remark")
      openF?.cancel(true)
      openF = null

      if (closeF != null) return
      closeF = executor.submit {
        logger.info("Mock lift [${config.id}][${config.name}]: set door to Closing")
        updateRecord(
          UpdateLiftRecord(doors = record.doors.map { LiftDoorStatus.Closing }),
        )

        try {
          Thread.sleep(config.closeDoorCost)
        } catch (_: Exception) {
          return@submit
        }

        updateRecord(
          UpdateLiftRecord(doors = record.doors.map { LiftDoorStatus.Closed }),
        )
        logger.info("Mock lift [${config.id}][${config.name}]: Closed")
      }
    }
  }

  fun updateRecord(update: UpdateLiftRecord) {
    logger.info("update mock lift[${config.id}:${config.name}] record: ${update.toStringNotNullAndNotEmpty()}")

    record = record.copy(
      online = update.online ?: record.online,
      autoMode = update.autoMode ?: record.autoMode,
      currentFloor = update.currentFloor ?: record.currentFloor,
      targetFloor = update.targetFloor ?: record.targetFloor,
      doors = update.doors.ifEmpty { record.doors },
      people = update.people ?: record.people,
      fault = update.fault ?: record.fault,
      faultMsg = update.faultMsg ?: record.faultMsg,
      lastStatusUpdateTime = Date(),
    )
  }
}