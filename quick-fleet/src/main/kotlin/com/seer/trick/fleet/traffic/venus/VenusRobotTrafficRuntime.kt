package com.seer.trick.fleet.traffic.venus

import com.seer.trick.fleet.traffic.TrafficTaskRuntime

/**
 * 一个机器人在 Venus 交管中的运行时表示，封装当前任务、规划请求、规划结果、执行情况等。
 */
class VenusRobotTrafficRuntime(val robotName: String) {
  /**
   * 正在规划的任务
   */
  @Volatile
  var trafficTask: TrafficTaskRuntime? = null

  /**
   * 规划结果
   */
  @Volatile
  var resolvingStatus: ResolvingStatus = ResolvingStatus.None

  @Volatile
  var failedReason: String? = null

  @Volatile
  var failedOn: Long? = null

  @Volatile
  var donePathIndex = 0

  override fun toString(): String = robotName
}

enum class ResolvingStatus {
  None,
  Resolving,
  Resolved,
  NotResolved,
}