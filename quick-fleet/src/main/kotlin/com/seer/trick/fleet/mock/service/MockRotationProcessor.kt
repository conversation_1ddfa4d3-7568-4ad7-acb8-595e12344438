package com.seer.trick.fleet.mock.service

import com.seer.trick.fleet.domain.GeoHelper
import com.seer.trick.fleet.mock.MockSeerRobotRuntime
import com.seer.trick.fleet.mock.service.MockGeoHelper.angleToRadians
import com.seer.trick.fleet.mock.service.MockGeoHelper.getPathGoodsDir
import com.seer.trick.fleet.mock.service.MockGeoHelper.getStepRad
import com.seer.trick.fleet.mock.service.MockMoveProcessor.checkTask
import com.seer.trick.fleet.mock.service.MockService.DEFAULT_MAX_ROT_SPEED
import com.seer.trick.fleet.mock.service.MockService.MOCK_FPS2
import kotlin.math.min

/**
 * 仿真机器人的角度是 -π ~ π
 */
object MockRotationProcessor {
  /**
   * 指定旋转角度，比如逆时针旋转 3π，不能归一化
   */
  fun rotateRobotBy(
    mr: MockSeerRobotRuntime,
    move: Step3066,
    delta: Double, // 传入要旋转的弧度，不带方向的
    dir: Int, // 旋转方向
    h: Boolean? = null, // 保持托盘的世界角度不变
  ) {
    val fromRad = GeoHelper.normalizeRadian(mr.record.theta)
    val h0 = h == true || holdContainerDir(mr, move)
    rotateBy(mr, move, fromRad = fromRad, delta, dir) { t, inc ->
      // 旋转底盘
      mr.setRecord(mr.record.copy(theta = t))
      // 旋转托盘
      updateContainerDirAndPosition(mr, inc, h0)
    }
  }

  /**
   * 仿真机器人的旋转
   * 注意托盘可能会和机器人同步旋转
   * @param toRad 目标弧度 [0, 2π]
   * @param move 移动指令
   * @param mr 机器人运行时
   */
  fun rotateRobotTo(
    mr: MockSeerRobotRuntime,
    move: Step3066,
    toRad: Double,
    dir: Int? = null, // 旋转方向
    h: Boolean? = null, // 保持托盘的世界角度不变
  ) {
    val maxRotateVelocity = mr.maxAngularVelocity()!!
    val currentTheta = GeoHelper.normalizeRadian(mr.record.theta)
    val rotationParams = MockGeoHelper.calculateRotationParams(currentTheta, toRad, dir)
    // 更新机器人旋转速度
    mr.currentSpeed =
      mr.currentSpeed.copy(rotSpeed = if (rotationParams.direction > 0) maxRotateVelocity else -maxRotateVelocity)

    val fromRad = GeoHelper.normalizeRadian(mr.record.theta)
    val h0 = h == true || holdContainerDir(mr, move)
    rotateTo(mr, move, fromRad, rotationParams) { t, inc ->
      // 旋转底盘
      mr.setRecord(mr.record.copy(theta = t))
      // 旋转托盘
      updateContainerDirAndPosition(mr, inc, h0)
    }
  }

  /**
   * 仿真托盘的旋转
   * @param mr 机器人运行时
   * @param move 移动指令
   * @param toRad 目标弧度 [0, 2π]
   *
   */
  fun rotateContainerTo(mr: MockSeerRobotRuntime, move: Step3066, toRad: Double, dir: Int? = null) {
    mr.record.goodsRegion?.let {
      val fromRad = GeoHelper.normalizeRadian(mr.record.goodsRegion?.theta ?: mr.record.theta)
      val rotationParams = MockGeoHelper.calculateRotationParams(fromRad, toRad, dir)
      rotateTo(mr, move, fromRad, rotationParams) { toTheta, _ ->
        MockContainersProcessor.setGoodsRegion(mr, containerTheta = toTheta)
      }
    }
  }

  /**
   * 根据 goodsDir 仿真托盘的旋转
   * @param move 移动指令
   * @param mr 机器人运行时
   */
  fun simulateGoodsDirRotation(mr: MockSeerRobotRuntime, move: Step3066) {
    mr.record.goodsRegion?.let {
      val goodsDir = move.goodsDir ?: getPathGoodsDir(mr, move) ?: return

      val toTheta = GeoHelper.normalizeRadian(angleToRadians(goodsDir))
      rotateContainerTo(mr, move, toTheta)
    }
  }

  /**
   * move 指令需要保持料架朝向
   *
   * 兼容：老机制的 move.spin，路径属性 goodsDir。
   * move.spin == true 或者 goodsDir != null，都认为要保持料架世界角度，即 holdContainerDir = true
   */
  fun holdContainerDir(mr: MockSeerRobotRuntime, move: Step3066): Boolean {
    val goodsDir = move.goodsDir ?: getPathGoodsDir(mr, move)
    return move.spin == true || goodsDir != null
  }

  /**
   * 更新容器的朝向和位置
   *
   * 如果要转托盘，就转到 toTheta，否则就转到当前机器人的角度
   *
   * @param delta 带方向的、旋转的弧度（rotate by）
   * @param holdContainerDir 保持托盘的世界角度不变
   */
  fun updateContainerDirAndPosition(mr: MockSeerRobotRuntime, delta: Double, holdContainerDir: Boolean) {
    if (mr.record.goodsRegion == null) return
    if (holdContainerDir) {
      MockContainersProcessor.setGoodsRegion(mr)
    } else {
      val containerTheta = GeoHelper.normalizeRadian((mr.record.goodsRegion?.theta ?: mr.record.theta) + delta)
      MockContainersProcessor.setGoodsRegion(mr, containerTheta = containerTheta)
    }
  }

  /**
   * 旋转通用逻辑
   * @param mr 机器人运行时
   * @param move 移动指令
   * @param fromRad 当前角度，弧度制 [0, 2π]
   * @param toRad 目标角度，弧度制 [0, 2π]
   * @param dir 旋转方向
   * @param rotationStep 旋转回调，输入参数：当前角度、变化角度
   */
  private fun rotateTo(
    mr: MockSeerRobotRuntime,
    move: Step3066,
    fromRad: Double,
    rotationParams: RotationParams,
    rotationStep: (Double, Double) -> Unit, // 当前角度，变量量
  ) {
    var t = fromRad
    val maxAngularVelocity = mr.maxAngularVelocity()!!
    val timeRequired = rotationParams.rotationDiff / maxAngularVelocity
    // val timeStep = 0.05 // 50ms per step
    val timeStep = 1.0 / MOCK_FPS2 // 50ms per step

    var elapsedTime = 0.0
    // 模拟旋转过程
    while (elapsedTime < timeRequired) {
      checkTask(mr, move.taskId)
      // 计算剩余时间和当前步的旋转时间
      val remainingTime = timeRequired - elapsedTime
      val currentStep = minOf(remainingTime, timeStep)

      // 当前步的旋转角度
      val stepAngle = rotationParams.direction * maxAngularVelocity * currentStep
      // TODO 根据 currentStep 算角度，而不是 timeStep
      // TODO md，就算 v * 时间，时间也应该取 currentStep，而不是 timeStep，不然最后一步转的有误差
      t = GeoHelper.normalizeRadian(t + stepAngle)
      rotationStep(t, stepAngle)
      // 更新时间
      elapsedTime += currentStep
      Thread.sleep((timeStep * 1000).toLong())
    }
  }

  /**
   * 旋转指定的角度
   */
  private fun rotateBy(
    mr: MockSeerRobotRuntime,
    move: Step3066,
    fromRad: Double,
    delta: Double, // 旋转弧度，不带方向
    dir: Int, // 旋转方向
    rotationStep: (Double, Double) -> Unit, // 当前角度，变化量
  ) {
    var cur = fromRad // 当前弧度
    var rest = delta // 剩余要旋转的弧度
    val stepRad = getStepRad(Math.toRadians(mr.moveConfig?.maxRotSpeed ?: DEFAULT_MAX_ROT_SPEED)) // 每帧的弧度

    while (rest > 0) {
      checkTask(mr, move.taskId)
      val step = min(rest, stepRad)
      // 注意：要保留这里小数，如果忽略了精度，导致最后少转了一点
      cur = GeoHelper.normalizeRadianNoRound(cur + step * dir)
      rest -= step

      rotationStep(cur, step)
      Thread.sleep(20)
    }
  }
}