package com.seer.trick.fleet.falcon.bp

import com.seer.trick.BzError
import com.seer.trick.I18N.lo

import com.seer.trick.base.alarm.AlarmAction
import com.seer.trick.base.alarm.AlarmItem
import com.seer.trick.base.alarm.AlarmLevel
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.falcon.FalconEventBus
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.TaskCancelledError
import com.seer.trick.falcon.task.TaskStatus
import com.seer.trick.fleet.domain.OrderStatus
import com.seer.trick.fleet.domain.StepStatus
import com.seer.trick.fleet.order.OrderCancelService
import com.seer.trick.fleet.order.OrderFaultService
import com.seer.trick.fleet.order.OrderRuntime
import com.seer.trick.fleet.order.StepManualFinishService
import com.seer.trick.fleet.service.SceneRuntime

abstract class AbstractWaitBp : AbstractBp() {

  /**
   * 等待运单步骤完成
   */
  fun waitStepComplete(sr: SceneRuntime, or: OrderRuntime, stepId: String) {
    var previousRobotName: String? = null
    val orderId = or.id

    while (true) {
      val step = or.steps.find { it.id == stepId }
        ?: throw BzError("errCodeError", "No transport step, id=$stepId")

      // 成功，跳过 TODO 取消
      if (step.status == StepStatus.Done || step.status == StepStatus.Skipped) break

      if (or.order.status == OrderStatus.Cancelled) {
        logger.info("TransportOrder cancelled: $orderId")
        // 运单被取消时，抛出异常取消整个猎鹰任务
        throw TaskCancelledError()
      }

      // 故障则告警并阻塞到用户选择
      if (or.order.fault) {
        alarmAndWait(sr, or)

        // 用户已作出选择，或者运单的故障状态已消失，通过猎鹰任务事件，及时清除关联的 CTO 的失败状态。
        or.order.externalId?.let {
          if (it.isNotBlank()) FalconEventBus.fire(it, "TaskRecover") // 故障重试
        }
      }

      val actualRobotName = or.order.actualRobotName
      // 运单步骤可能被多个机器人执行过或者一个猎鹰任务里创建多个运单，所以要把所有机器人记录下来
      if (previousRobotName != actualRobotName) {
        addActualVehicle(actualRobotName, previousRobotName)
      }
      previousRobotName = actualRobotName

      Thread.sleep(1000)
    }
  }

  private fun alarmAndWait(sr: SceneRuntime, or: OrderRuntime) {
    val orderId = or.id
    logger.info("TransportOrder $orderId failed: ${or.order.faultReason}")
    val taskEv = EntityRwService.findOneById("FalconTaskRecord", taskRuntime.taskId, null)

    // 构造运单报错
    val manualFinishAllowed = StepManualFinishService.allowManualFinish(sr, or)
    val ai = AlarmItem(
      sceneId = sr.sceneId,
      group = "Falcon",
      code = "FalconFleetOrderFault",
      key = "FalconFleetOrderFault-$orderId", // 要唯一
      level = AlarmLevel.Error,
      message = lo(
        "errFalconFailed",
        listOf(taskEv?.get("defLabel"), taskRuntime.taskId, StepManualFinishService.buildStepErrMsg(sr, or)),
      ),
      args = listOf(taskRuntime.taskId, sr.sceneId, orderId),
      actions = listOfNotNull(
        AlarmAction(lo("btnViewDetail"), uiAction = "FalconViewTask"),
        AlarmAction(lo("btnFaultRetry")),
        AlarmAction(lo("btnCancelTask")),
        if (manualFinishAllowed) AlarmAction(lo("btnManualFinishedTask")) else null,
      ),
    )

    or.order.externalId?.let {
      if (it.isNotBlank()) FalconEventBus.fire(it, "TaskFailByOrderFault")
    }

    AlarmService.request(ai, null, { actionIndex, args ->
      AlarmService.removeItem(ai.key)
      when (actionIndex) {
        1 -> OrderFaultService.retryByOrder(sr, orderId)
        2 -> OrderCancelService.cancelOrder(sr, orderId)
        3 -> StepManualFinishService.manualFinishingStep(sr, or)
      }
    }, {
      // 如果运单不再故障，删除告警，停止等待用户处理
      // logger.debug("order check: {}", or.order.fault)
      if (!or.order.fault) {
        AlarmService.removeItem(ai.key)
      }

      // 如果运单关联的猎鹰任务，已经是终态了，则删除告警，停止等待用户处理
      val topTask = taskRuntime.getTopTask()
      if (topTask.status in listOf(TaskStatus.Cancelled, TaskStatus.Aborted)) {
        logger.error(
          "FalconTask Aborted or Cancelled，remove AlarmItem，orderId=$orderId, " +
            "taskId=${taskRuntime.taskId}, topTaskId=${topTask.taskId}, alarmMsg=${ai.message}",
        )
        AlarmService.removeItem(ai.key)
        // 此处抛这个异常，都可以确保此运单的告警信息一定会被消除。
        topTask.throwIfCancelledOrAborted()
      }

      !or.order.fault
    })
  }
}