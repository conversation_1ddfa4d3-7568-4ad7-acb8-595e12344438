package com.seer.trick.fleet.domain

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.helper.JsonHelper
import java.util.*

/**
 * 移动机器人“通用运单”
 */
data class TransportOrder(
  val id: String = "", // 单号，一般由系统负责生成
  val sceneId: String = "", // 场景 ID
  val sceneName: String = "", // 场景名称
  val status: OrderStatus = OrderStatus.ToBeAllocated, // 单据状态
  val externalId: String? = null, // 外部单号，目前只用于记录猎鹰任务的 ID
  val containerId: String? = null, // 搬运的容器编号
  val containerTypeName: String? = null, // 容器类型名称
  val containerDir: Double? = null, // 放置容器的方向（弧度）
  val taskBatch: String? = null, // 对于多负载机器人期望一起取放的任务
  val priority: Int = 0, // 优先级
  val expectedRobotNames: List<String>? = null, // 期望以下机器人执行
  val expectedRobotGroups: List<String>? = null, // 期望以下机器人组执行，注意是机器人组名
  val keyLocations: List<String>? = null, // 关键位置
  val createdOn: Date = Date(),
  val kind: OrderKind = OrderKind.Business,
  val noLoad: Boolean = false, // 此运单不涉及装卸货
  val fault: Boolean = false, // 运单执行出现故障
  val faultReason: String? = null, // 故障原因
  val stepNum: Int = 0, // 运单由几个步骤构成
  val stepFixed: Boolean = false, // 所有步骤是否都已创建好，不会再追加了
  val currentStepIndex: Int = -1, // 当前正在执行/执行完的运单步
  val doneStepIndex: Int = -1, // 已完成执行的运单步  TODO 有用吗？
  val actualRobotName: String? = null, // 实际执行机器人
  val oldRobots: List<String>? = null, // 历史分配机器人，只有业务单才会有 oldRobots
  val robotAllocatedOn: Date? = null, // 分派机器人的时间
  // val allowModifyRobot: Boolean = true, // 单独有这个属性的原因是，针对直接运单的场景  TODO
  val loadPoint: String? = null, // 取货点位
  val unloadPoint: String? = null, // 放货点位
  val loaded: Boolean = false, // 是否已完成取货 // TODO 要不要增加 loading / unloading
  val unloaded: Boolean = false, // 是否已完成放货
  val dispatchCost: Double? = null, // 派单成本 TODO
  val withdrawnCount: Int = 0, // 被二分的次数
  // val steps: List<TransportStep> = emptyList(),
  val doneOn: Date? = null, // 运单完成时间
  val processingTime: Double? = null, // 运单处理时间，单位秒。原始数据不做精度处理。
  val executingTime: Double? = null, // 运单执行时间，单位秒。原始数据不做精度处理。
  val failureNum: Int = 0, // 故障次数
  val faultDuration: Double? = null, // 故障时长。单位秒，从故障开始到故障结束的时间，多次发生故障则累加时间。
  val loadDuration: Double? = null, // 取货时长。单位秒，取货步骤从开始到结束的时间
  val unloadDuration: Double? = null, // 放货时长。单位秒，放货步骤从开始到结束的时间
  val waitExecuteDuration: Double? = null, // 执行等待时长。单位秒，从创建到下发给机器人第一个运单指令的时间
) {

  /**
   * 不会再有后续步骤了：已封口，都已执行完
   */
  val noMoreStep = stepFixed && (stepNum == 0 || stepNum > 0 && doneStepIndex == stepNum - 1)
}

/**
 * 运单状态
 */
enum class OrderStatus(
  val notExecuting: Boolean, // 未在执行，也未结束
  val finalStatus: Boolean,
) {
  // Building,
  ToBeAllocated(true, false), // 待分派给机器人
  Allocated(true, false), // 已分派给机器人
  Pending(true, false), // 已执行了至少一个步骤，但当前没有步骤被执行
  Executing(false, false), // 正在执行，即运单的某个步骤正在被机器人执行！
  Done(false, true), // 运单完成
  Cancelled(false, true), // 运单取消完成
}

/**
 * 移动机器人业务类型
 */
enum class OrderKind(val auto: Boolean) {
  Business(false), // 正常业务单
  Parking(true), // 停靠单
  Charging(true), // 充电单
  IdleAvoid(true), // 避让单
}

/**
 * 通用运单步骤（单步）
 */
data class TransportStep(
  val id: String = "", // 步骤 ID
  val orderId: String = "", // 所属运单
  val stepIndex: Int = 0, // 运单的第几个步骤 0 based
  val status: StepStatus = StepStatus.Executable, // 步骤状态
  val withdrawOrderAllowed: Boolean = false, // 是否允许运单重分派
  val location: String = "", // 作业点位或库位
  val percentage: Double? = null, // 路径百分比
  val rbkArgs: String? = null, // 动作参数
  val forLoad: Boolean = false, // 在此步取货
  val forUnload: Boolean = false, // 在此步卸货
  val createdOn: Date = Date(), // 创建时间
  val startOn: Date? = null, // 开始时间 TODO
  val endOn: Date? = null, // 结束时间
  // val containerId: String? = null, // 容器id
  // val containerTypeName: String? = null, // 容器类型名称
  // val containerDir: Double? = null, // 容器方向（弧度）
  // val needWaiting: Boolean = false  // 是否需要等待 TODO
  val processingTime: Double = 0.0, // 运单步骤处理时间，单位秒。原始数据不做精度处理。
  val executingTime: Double = 0.0, // 运单步骤执行时间，单位秒。原始数据不做精度处理。
  val nextStepSameOrder: Boolean = false, // 强制要求下一步不能切换运单，还是做本单
) {

  /**
   * 获得 binTask 或 operation
   */
  fun parseBinTaskOrOperation(): String? {
    val rbkArgs: Map<String, Any?> = rbkArgs?.let { JsonHelper.mapper.readValue(it, jacksonTypeRef()) } ?: return null
    val binTask = rbkArgs["binTask"] as? String?
    if (!binTask.isNullOrBlank()) return binTask
    val operation = rbkArgs["operation"] as? String?
    return operation
  }
}

/**
 * 运单步骤状态
 * 不设取消状态。步骤不能单独被取消。
 */
enum class StepStatus(
  val finished: Boolean, // 是否终态
) {
  // Building,
  Executable(false), // 可执行
  Executing(false), // 正在被执行
  Skipped(true), // TODO
  Done(true), // 完成（成功）
}

/**
 * 将运单分给机器人的一次尝试
 */
data class PreAllocation(
  val robotName: String,
  val orderId: String,
  val priority: Int, // 优先级，数字越大优先级越高
  val cost: Double, // 成本，单位是米！
  val createdOn: Date,
)

/**
 * 机器人自动任务
 */
data class RobotAutoOrder(
  val type: RobotAutoOrderType,
  val orderId: String,
  val pointName: String? = null, // 停靠点、充电点
)

/**
 * 机器人自动任务类型
 */
enum class RobotAutoOrderType {
  Parking,
  Charging,
  GiveWay, // 让路
}

/**
 * 拒绝原因。
 * params 要能被序列化，不能传复杂的对象。
 */
data class RejectReason(val code: String, val params: List<Any?>? = null) {

  override fun toString(): String = "$code|${params ?: ""}"
}

/**
 * 运单运行时记录
 */
data class OrderRuntimeRecord(
  val orderId: String,
  val status: OrderStatus,
  val allocationReject: RejectReason? = null,
  val executionReject: RejectReason? = null,
  val createdOn: Date = Date(),
)

data class NavTaskResult(val status: Int = 0, val taskId: String = "") {
  /**
   * 是否是终态
   */
  val isFinalStatus: Boolean get() = status == 4 || status == 5 || status == 6

  companion object {
    const val NT_WAITING = 1 // 目前不可能出现该状态
    const val NT_RUNNING = 2
    const val NT_SUSPENDED = 3
    const val NT_COMPLETED = 4
    const val NT_FAILED = 5
    const val NT_CANCELLED = 6
    const val NT_NOT_FOUND = 404
  }
}