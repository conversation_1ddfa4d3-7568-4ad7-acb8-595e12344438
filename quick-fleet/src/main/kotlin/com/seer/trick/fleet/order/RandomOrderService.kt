package com.seer.trick.fleet.order


import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.map.SceneMapAlg
import com.seer.trick.fleet.service.SceneService
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper
import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.Future
import kotlin.random.Random

/**
 * 随机发单 Service
 *
 * 暂时不用系统监控或者日志看随机发单状态，有人关心的话再加
 */
object RandomOrderService {

  private val logger = LoggerFactory.getLogger(javaClass)

  private val executor: ExecutorService = Executors.newSingleThreadExecutor()
  private var future: Future<*>? = null

  // 随机发单的配置 { sceneId -> keepOrderNum }
  // 没开随机发单的场景不放到 cfg 中
  private val cfgMap: MutableMap<String, Int> = ConcurrentHashMap()

  @Volatile
  private var disposed: Boolean = false

  fun init() {
    disposed = false
    future = executor.submit { loop() }
    logger.info("KeepOrderService init")
  }

  /**
   * 关闭随机发单
   *
   * 注意：要在 dispose SceneRuntime 前调用
   */
  fun dispose() {
    disposed = true
    future?.cancel(true)
    logger.info("KeepOrderService dispose")
  }

  fun addCfg(sceneId: String, keepOrderNum: Int) {
    cfgMap[sceneId] = keepOrderNum
  }

  fun removeCfg(sceneId: String) = cfgMap.remove(sceneId)

  fun getCfg(sceneId: String): Int? = cfgMap[sceneId]

  private fun loop() {
    while (!disposed) {
      
      try {
        for (cfg in cfgMap) {
          createRandomOrder(cfg.key, cfg.value)
        }
        Thread.sleep(100)
      } catch (e: InterruptedException) {
        break
      } catch (e: Throwable) {
        logger.error("Error in KeepOrderService loop", e)
      }
    }
  }

  private fun createRandomOrder(sceneId: String, keepOrderNum: Int) {
    val sr = SceneService.mustGetSceneById(sceneId)

    if (sr.status != SceneStatus.Initialized) return
    if (sr.basic.disabled) return

    // 自动单
    if (sr.orders.values.filter { it.order.kind == OrderKind.Business }.size >= keepOrderNum) return

    // 随机选一个未停用的机器人，取其对应的机器人组
    val rr = sr.listRobots().filter { !it.disabled() }.randomOrNull() ?: return
    val gId = rr.config.groupId
    val gName = sr.robotGroups[gId]?.name ?: return

    // 随机选择一个未停用的区域，有机器人组的
    val area = sr.mapCache.areaById.values.filter { !it.schema.disabled && it.groupedMaps.contains(gId) }
      .randomOrNull() ?: return
    val gmc = area.groupedMaps[gId] ?: return

    // 在机器人组的地图里随机选 2 个点位
    val points = gmc.pointNameMap.values.filter {
      !it.point.disabled &&
        (it.point.type == "ActionPoint" || it.point.type == "LocationMark")
    }
    val fromPoint = points[Random.nextInt(0, points.size)].point.name
    val toPoint = points[Random.nextInt(0, points.size)].point.name

    // 检查这两个点位可达
    sr.mapCache.groupMaps[gId] ?: return
    val alg = SceneMapAlg(listOf(gmc), emptySet())
    if (!alg.getShortestPath(fromPoint, toPoint).found) return

    // 随机单号、容器号
    val oId = OrderService.generateOrderId()
    val cId = IdHelper.oidStr()

    // 创建运单
    // interruptible wait -> load -> interruptible wait -> unload
    val steps = listOf(
      TransportStep(
        id = IdHelper.oidStr(),
        orderId = oId,
        stepIndex = 0,
        status = StepStatus.Executable,
        location = fromPoint,
        withdrawOrderAllowed = true,
      ),
      TransportStep(
        id = IdHelper.oidStr(),
        orderId = oId,
        stepIndex = 1,
        status = StepStatus.Executable,
        location = fromPoint,
        forLoad = true,
        rbkArgs = JsonHelper.writeValueAsString(mutableMapOf("operation" to "Load")),
      ),
      TransportStep(
        id = IdHelper.oidStr(),
        orderId = oId,
        stepIndex = 2,
        status = StepStatus.Executable,
        location = toPoint,
        withdrawOrderAllowed = true,
      ),
      TransportStep(
        id = IdHelper.oidStr(),
        orderId = oId,
        stepIndex = 3,
        status = StepStatus.Executable,
        location = toPoint,
        forUnload = true,
        rbkArgs = JsonHelper.writeValueAsString(mutableMapOf("operation" to "Unload")),
      ),
    )
    val order = TransportOrder(
      id = oId,
      status = OrderStatus.ToBeAllocated,
      containerId = cId,
      expectedRobotGroups = listOf(gName),
      keyLocations = listOf(fromPoint),
      stepNum = steps.size,
      stepFixed = true,
      sceneId = sceneId,
    )
    OrderService.createOrders(sr, listOf(OrderRuntime(order, steps)))
  }
}