package com.seer.trick.fleet.traffic.distributed.lock.tree

import com.seer.trick.fleet.traffic.distributed.lock.LockType
import com.seer.trick.fleet.traffic.distributed.lock.graph.BoundingBox
import com.seer.trick.fleet.traffic.distributed.lock.model.SpaceLock
import com.seer.trick.fleet.traffic.distributed.map.Line
import com.seer.trick.fleet.traffic.distributed.map.Point
import java.util.concurrent.ConcurrentHashMap

/**
 * 四叉树节点
 * */
class QuadTreeNode(val deep: Int, val mapCode: String) {

  /**
   *       +
   *    0  +  1
   *  ++++++++++++
   *    2  +  3
   *       +
   * 四叉树的四个方位
   */
  val leftTop: Int = 0
  val rightTop: Int = 1
  val leftBottom: Int = 2
  val rightBottom: Int = 3

  // 节点方位信息
  private var orientaion: Int? = null

  var collision: MutableMap<String, SpaceLock> = ConcurrentHashMap()

  private var childElementIndex: MutableMap<String, Int> = ConcurrentHashMap()

  var child: MutableMap<Int, QuadTreeNode> = mutableMapOf()

  var parent: QuadTreeNode? = null

  var boundingBox: BoundingBox? = null

  var treePoint: MutableList<Point> = mutableListOf()

  var treeEdge: MutableList<Line> = mutableListOf()

  constructor(
    deep: Int,
    mapCode: String,
    parent: QuadTreeNode?,
    boundingBox: BoundingBox,
    orientation: Int,
  ) : this(deep, mapCode) {
    this.parent = parent
    this.boundingBox = boundingBox
    this.orientaion = orientation
  }

  // 插入元素信息到节点中
  fun insert(spaceLock: SpaceLock): Boolean {
    // 添加元素信息
    collision[spaceLock.getKey()] = spaceLock
    if (orientaion != null) {
      parent?.addChild(spaceLock.getKey(), orientaion!!)
    }
    return true
  }

  // 删除元素信息
  fun remove(key: String): Boolean {
    if (collision.containsKey(key)) {
      collision.remove(key)
      parent?.removeChild(key)
      return true
    }
    if (childElementIndex.containsKey(key)) {
      for (child in child) {
        if (child.value.remove(key)) {
          return true
        }
      }
    }
    return false
  }

  // 查询元素信息
  fun query(key: String): SpaceLock? {
    // 查询元素信息
    if (collision.containsKey(key)) {
      return collision[key]!!
    }
    // 查询子节点信息
    if (childElementIndex.containsKey(key)) {
      val orientation = childElementIndex[key]!!
      return child[orientation]?.query(key)
    }
    return null
  }

  fun findChildRobotSpaceLock(): MutableMap<String, SpaceLock> {
    val spaceMap: MutableMap<String, SpaceLock> = mutableMapOf()
    spaceMap.putAll(collision.filter { it.value.type == LockType.ROBOT })
    if (child.isEmpty()) {
      return spaceMap
    }
    for (c in child) {
      spaceMap.putAll(c.value.findChildRobotSpaceLock())
    }
    return spaceMap
  }

  fun findAllChildSpaceLock(): MutableList<SpaceLock> {
    val spaceMap: MutableList<SpaceLock> = mutableListOf()
    spaceMap.addAll(collision.values)
    if (child.isEmpty()) {
      return spaceMap
    }
    for (c in child) {
      spaceMap.addAll(c.value.findAllChildSpaceLock())
    }
    return spaceMap
  }

  fun findParentRobotSpaceLock(): MutableMap<String, SpaceLock> {
    val spaceMap: MutableMap<String, SpaceLock> = mutableMapOf()
    spaceMap.putAll(collision.filter { it.value.type == LockType.ROBOT })
    if (parent != null) {
      spaceMap.putAll(parent!!.findParentRobotSpaceLock())
    }
    return spaceMap
  }

  fun findAllParentSpaceLock(): MutableList<SpaceLock> {
    val spaceMap: MutableList<SpaceLock> = mutableListOf()
    spaceMap.addAll(collision.values)
    if (parent != null) {
      spaceMap.addAll(parent!!.findAllParentSpaceLock())
    }
    return spaceMap
  }

  // 添加子元数信息
  private fun addChild(elementKey: String, orientation: Int) {
    childElementIndex[elementKey] = orientation
    if (orientaion != null) {
      parent?.addChild(elementKey, orientaion!!)
    }
  }

  // 删除子元素信息
  private fun removeChild(elementKey: String) {
    childElementIndex.remove(elementKey)
    parent?.removeChild(elementKey)
  }

  fun queryTreePoints(): MutableList<Point> {
    val points: MutableList<Point> = mutableListOf()
    if (treePoint.isNotEmpty()) {
      points.addAll(treePoint)
    }
    if (child.isEmpty()) {
      return points
    }
    for (c in child) {
      points.addAll(c.value.queryTreePoints())
    }
    return points
  }

  fun queryTreeLines(): MutableList<Line> {
    val lines: MutableList<Line> = mutableListOf()
    if (treeEdge.isNotEmpty()) {
      lines.addAll(treeEdge)
    }
    if (child.isEmpty()) {
      return lines
    }
    for (c in child) {
      lines.addAll(c.value.queryTreeLines())
    }
    return lines
  }
}