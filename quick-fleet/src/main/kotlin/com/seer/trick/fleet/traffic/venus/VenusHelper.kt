package com.seer.trick.fleet.traffic.venus

import com.seer.trick.fleet.domain.MapPoint
import com.seer.trick.fleet.domain.MoveDirection
import com.seer.trick.fleet.domain.PathPosition
import com.seer.trick.fleet.domain.Pose2D
import com.seer.trick.fleet.domain.RobotStand
import com.seer.trick.fleet.map.AreaMapCache
import com.seer.trick.fleet.seer.RotationDirection
import com.seer.trick.fleet.service.RobotLoadCollisionService
import com.seer.trick.helper.DateHelper.formatDate
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.util.*

object VenusHelper {
  val logger: Logger = LoggerFactory.getLogger(javaClass)

  /**
   * 规划请求 -> 机器人规划的起始状态。可能有多个，如果位于多条线路上。
   */
  fun robotStandToStartStates(
    robotName: String,
    ar: AreaMapCache,
    request: RobotPlanRequest,
    o: PathFindingOption,
  ): List<State> = robotStandToStartPositions(robotName, ar, request.robotInfo.stand).map {
    val stand = request.robotInfo.stand
    State(
      0,
      StateType.Start,
      it,
      toEndHead = stand.theta,
      robotEnterTheta = stand.theta, // 只是赋下值，实际没用
      robotExitTheta = stand.theta, // 只是赋下值，实际没用
      loadEnterTheta = request.robotInfo.loadTheta,
      loadExitTheta = request.robotInfo.loadTheta,
      moveDirection = MoveDirection.Dual, // 只是赋下值，实际没用
      rotateDirection = RotationDirection.EITHER, // 只是赋下值，实际没用
      shapes = PolygonsWithBBox(
        RobotLoadCollisionService.buildCollisionShape(
          o.collisionModels[robotName]!!,
          Pose2D(
            stand.x,
            stand.y,
            stand.theta,
          ),
        ),
      ),
    )
  }

  /**
   * 机器人起始位置 -> 机器人规划的起始位置。可能有多个，如果位于多条线路上。
   * 如果在点上，就不考虑在线上。
   */
  private fun robotStandToStartPositions(robotName: String, ar: AreaMapCache, stand: RobotStand): List<RoutePosition> =
    when {
      !stand.pointName.isNullOrBlank() ->
        listOf(
          RoutePosition(
            type = RoutePositionType.Point,
            pointName = stand.pointName,
            x = stand.x,
            y = stand.y,
          ),
        )

      !stand.pathPositions.isNullOrEmpty() -> {
        // 1. 按无序端点对分组
        val groupedPaths = stand.pathPositions.groupBy { pp ->
          val (a, b) = listOf(pp.fromPointName, pp.toPointName).sorted()
          "${a}_$b"
        }

        // 2. 获取每组中 pathPercentage 最小的 PathPosition
        val minPaths = groupedPaths.values.map { list ->
          list.maxByOrNull { it.pathPercentage }!!
        }

        // 3. 按阈值分层过滤
        val thresholds = listOf(0.1, 0.3, 1.0)
        val filteredPaths = thresholds.firstNotNullOfOrNull { threshold ->
          val candidates = minPaths.filter { it.pathDistance < threshold }
          candidates.ifEmpty { null }
        } ?: minPaths // 如果所有阈值都没匹配到，就用全部数据

        // 4. 转换为 RoutePosition
        // 对于双向路，如何选择呢？
        filteredPaths.map { pp ->
          val pr = ar.pathKeyMap[pp.pathKey]
            ?: throw IllegalStateException("PathKey ${pp.pathKey} not found in cache")
          RoutePosition(
            type = RoutePositionType.Path,
            pathKey = pp.pathKey,
            p = pp.pathPercentage,
            pathStartPointName = pr.path.fromPointName,
            pathEndPointName = pr.path.toPointName,
            x = stand.x,
            y = stand.y,
          )
        }
      }

      else -> throw IllegalStateException("Robot $robotName not on points or paths")
    }

  /**
   * 从一组 PathPosition 中，按阈值逐层筛选出最优的一个
   */
  private fun selectBestPathPosition(list: List<PathPosition>, thresholds: List<Double>): PathPosition? {
    for (threshold in thresholds) {
      val candidates = list.filter { it.pathDistance < threshold }
      if (candidates.isNotEmpty()) {
        return candidates.minByOrNull { it.pathPercentage }
      }
    }
    return null
  }

  /**
   * 将移动到目标点的状态，转换为在目标点工作的状态：给工作用的时间片。
   */
  fun moveToGoalToWorkingGoal(robotName: String, toState: State, o: PathFindingOption, p: MapPoint) = toState.copy(
    pathIndex = toState.pathIndex + 1,
    type = StateType.Goal,
    robotEnterTheta = toState.robotExitTheta, // 目标点最终初始进入角度，应该是上次离开线路的角度
    robotExitTheta = p.direction ?: toState.robotExitTheta, // 目标点最终朝向是站点配置的朝向
    byPathKey = null, // 静止不移动
    timeStart = toState.timeEnd + 1,
    timeEnd = toState.timeEnd + 10 + o.goalStopTimes,
    timeNum = 10,
    shapes = PolygonsWithBBox(
      RobotLoadCollisionService.buildCollisionShape(
        o.collisionModels[robotName]!!,
        Pose2D(
          toState.toPosition.x,
          toState.toPosition.y,
          toState.toEndHead!!,
        ),
      ),
    ),
  )

  /**
   * 将移动到目标点的状态，转换为在目标点停靠的状态
   */
  fun moveToGoalToParkingGoal(robotName: String, toState: State, o: PathFindingOption) = toState.copy(
    pathIndex = toState.pathIndex + 1,
    type = StateType.Goal,
    byPathKey = null, // 静止不移动
    timeStart = toState.timeEnd + 1,
    timeEnd = -1,
    timeNum = Long.MAX_VALUE,
    shapes = PolygonsWithBBox(
      RobotLoadCollisionService.buildCollisionShape(
        o.collisionModels[robotName]!!,
        Pose2D(
          toState.toPosition.x,
          toState.toPosition.y,
          toState.toEndHead!!,
        ),
      ),
    ),
  )

  fun logTimestamp(): String = formatDate(Date(), "dd HH:mm:ss.SSS")
}