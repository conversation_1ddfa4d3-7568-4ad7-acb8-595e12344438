package com.seer.trick.fleet.device.door.adapter

import com.seer.trick.fleet.device.door.DoorAdapterReport
import com.seer.trick.fleet.device.door.DoorMainStatus
import com.seer.trick.fleet.device.door.SceneDoor
import org.slf4j.LoggerFactory
import java.util.concurrent.Executors
import java.util.concurrent.Future

/**
 * 到模拟门的适配器
 */
class DoorAdapterMock(private val config: SceneDoor) : DoorAdapter() {

  private val logger = LoggerFactory.getLogger(javaClass)

  @Volatile
  var status = DoorMainStatus.Closed

  private val executor = Executors.newSingleThreadExecutor()

  @Volatile
  private var openF: Future<*>? = null

  @Volatile
  private var closeF: Future<*>? = null

  override fun init() {
    super.init()
    logger.info("Mock door [${config.id}][${config.name}]: stand by")
  }

  override fun report(): DoorAdapterReport =
    DoorAdapterReport(online = true, fault = false, faultMsg = "", mainStatus = status)

  override fun openDoor(remark: String) {
    synchronized(this) {
      // 会导致刷日志
      // logger.debug("Mock door [${config.id}][${config.name}]: ask me to open, current status: $status")
      closeF?.cancel(true)
      closeF = null

      if (openF != null) return
      openF = executor.submit {
        status = DoorMainStatus.Opening
        try {
          Thread.sleep(3000)
        } catch (_: Exception) {
          return@submit
        }
        status = DoorMainStatus.Opened
        logger.info("Mock door [${config.id}][${config.name}]: Opened")
      }
    }
  }

  override fun closeDoor(remark: String) {
    synchronized(this) {
      logger.info(
        "Mock door [${config.id}][${config.name}]: ask me to close, current status: $status, remark=$remark",
      )
      openF?.cancel(true)
      openF = null

      if (closeF != null) return
      closeF = executor.submit {
        status = DoorMainStatus.Closing
        try {
          Thread.sleep(3000)
        } catch (_: Exception) {
          return@submit
        }
        status = DoorMainStatus.Closed
        logger.info("Mock door [${config.id}][${config.name}]: Closed")
      }
    }
  }
}