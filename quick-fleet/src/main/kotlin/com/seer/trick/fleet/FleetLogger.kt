package com.seer.trick.fleet

import com.seer.trick.base.MapToAnyNull
import com.seer.trick.fleet.service.SceneRuntime
import org.slf4j.LoggerFactory

/**
 * 调度专用日志。结构化的输出日志。
 * 其中 msg 必须以结构化的方式给出。
 */
object FleetLogger {

  private val logger = LoggerFactory.getLogger("Fleet")

  fun trace(
    module: String,
    subject: String,
    sr: SceneRuntime,
    robotName: String?,
    msg: MapToAnyNull,
  ) {
    logger.debug("[$module][$subject][${sr.basic.name}][$robotName]$msg")
  }

  fun debug(
    module: String,
    subject: String,
    sr: SceneRuntime,
    robotName: String?,
    msg: MapToAnyNull,
  ) {
    logger.debug("[$module][$subject][${sr.basic.name}][$robotName]$msg")
  }

  fun info(module: String, subject: String, sr: SceneRuntime, robotName: String?, msg: MapToAnyNull) {
    logger.info("[$module][$subject][${sr.basic.name}][$robotName]$msg")
  }

  fun warn(
    module: String,
    subject: String,
    sr: SceneRuntime,
    robotName: String?,
    msg: MapToAnyNull,
    e: Throwable? = null,
  ) {
    logger.warn("[$module][$subject][${sr.basic.name}][$robotName]$msg", e)
  }

  fun error(
    module: String,
    subject: String,
    sr: SceneRuntime,
    robotName: String?,
    msg: MapToAnyNull,
    e: Throwable? = null,
  ) {
    logger.error("[$module][$subject][${sr.basic.name}][$robotName]$msg", e)
  }
}