package com.seer.trick.fleet.traffic.distributed.service.model

import com.seer.trick.fleet.traffic.distributed.context.domain.RobotMotionType
import com.seer.trick.fleet.traffic.distributed.lock.graph.*

/**
 * 机器人类型信息
 *   目前简单这样处理
 * */
data class RobotTypeInfo(

  // 机器人类型 code
  var typeCode: String,

  // 机器人类型名称
  var typeName: String,

  // 地盘类型 TODO 临时这样写
  var type: RobotMotionType = RobotMotionType.ADVANCE,

  // 是否随动
  var isDynamic: Boolean,

  // 机器人长度
  var length: Double,

  // 机器人宽度
  var width: Double,

  // 机器人高度
  var height: Double,

  // 旋转半径
  var radius: Double,

  // 偏移几何中心x的距离
  var offerDistanceX: Double,

  // 偏移几何中心y的距离
  var offerDistanceY: Double,

  // 车头前后安全距离，以车的方向考虑
  var lengthSafeDistance: Double,

  // 车头两侧安全距离
  var widthSafeDistance: Double,
)

/**
 *  机器人组类型信息
 * */
data class GroupModel(
  val sceneId: String, // 场景名称
  val groupName: String, // 组类型名称
  val type: RobotMotionType, // 组内机器人运动模型
  val points: MutableList<Vector>, // 外轮廓点
  var radius: Double, // 最大旋转半径
  val lengthSafeDistance: Double = 0.0, // 车头前后安全距离，以车的方向考虑
  val widthSafeDistance: Double = 0.0, // 车头两侧安全距离
  var isDynamic: Boolean = true, // 是否随动
)

/**
 *  容器模型信息
 * */
data class ContainerModel(
  val sceneId: String, // 场景名称
  val typeName: String, // 货架类型
  val length: Double, // 货架长度
  val width: Double, // 货架宽度
  val height: Double, // 货架高度
  val radius: Double, // 旋转半径
  val expLegLength: Double, // 货架除腿长度
  val expLegWidth: Double, // 货架除腿宽度
  val legHeight: Double, // 货架腿高度
  val narrowDir: Int, // 窄边方向
  val groupCenterDistance: Map<String, Double> = mutableMapOf(), // 货架中心点与机器人组模型的中心点的距离
)

// data class ContainerModel(
//  val sceneId: String, // 场景名称
//  val typeName: String, // 货架类型
//  val polygon: Polygon, // 外轮廓多边形
//  val height: Double, // 货架高度 （1 层的高度）
//  val radius: Double, // 旋转半径
//  val narrowDir: Int, // 窄边方向
//  val legHeight: Double, // 货架腿高度（0层的高度）
//  val legs: List<Circle> = mutableListOf(), // 货架腿相对中心的位置
//  val groupCenterDistance: Map<String, Double> = mutableMapOf(), // 货架中心点与机器人组模型的中心点的距离
// )