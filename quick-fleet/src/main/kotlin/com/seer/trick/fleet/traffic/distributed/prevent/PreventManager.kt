package com.seer.trick.fleet.traffic.distributed.prevent

import com.seer.trick.fleet.domain.SceneConfig
import com.seer.trick.fleet.traffic.distributed.block.BlockItem
import com.seer.trick.fleet.traffic.distributed.context.ContextManagerService
import com.seer.trick.fleet.traffic.distributed.context.domain.RobotContext
import com.seer.trick.fleet.traffic.distributed.context.domain.TrafficStatus
import com.seer.trick.fleet.traffic.distributed.deadlock.idle.IdleDeadLockProcessor
import com.seer.trick.fleet.traffic.distributed.lock.LockService
import com.seer.trick.fleet.traffic.distributed.lock.LockType
import com.seer.trick.fleet.traffic.distributed.map.MapService
import com.seer.trick.fleet.traffic.distributed.plan.model.PathAction
import com.seer.trick.fleet.traffic.distributed.service.DistributedTrafficService
import io.javalin.util.NamedThreadFactory
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.*

object PreventManager {

  private val logger = LoggerFactory.getLogger(javaClass)

  // 定义一个线程池
  private val schedulePool = ScheduledThreadPoolExecutor(1, NamedThreadFactory("prevent"))

  private const val RUNNING = 0
  private const val IDLE = 1
  private const val DEADLOCK = 2
  private const val ERROR = 3
  private const val MIN_SIZE = 15

  fun start() {
    // 启动线程池
    schedulePool.scheduleWithFixedDelay(this::doSchedule, 1000, 200, TimeUnit.MILLISECONDS)
  }

  fun stop() {
    schedulePool.shutdown()
  }

  private fun doSchedule() {
    try {
      val robots = ContextManagerService.queryAllRobotContext()
      val robotsMap = robots.groupBy { it.sceneId } // 按照场景id进行分组
      for ((sceneId, contexts) in robotsMap) {
        val config = DistributedTrafficService.findSceneBySceneId(sceneId).config
        if (!config.enablePrevent) {
          continue
        }
        // todo 按照 mapName 分组
        runTask(contexts, config)
      }
    } catch (e: Exception) {
      logger.error("prevent error $e")
    }
  }

  private fun runTask(contexts: List<RobotContext>, config: SceneConfig) {
    try {
// 获取所有机器人上下文
      val prevent = PreventRequestInfo(
        idleRobots = mutableListOf(),
        blockRobots = mutableListOf(),
        runningRobots = mutableListOf(),
        deadLockRobots = mutableListOf(),
        time = System.currentTimeMillis(),
        version = System.currentTimeMillis().toString(),
      )
      // 遍历机器人
      contexts.forEach { context ->
        // 目前只考虑 运行中的机器人， 空闲、故障等一系列机器人不在计算的范围内， 故需要保证终点没有机器人或者 没有任务的
        when (context.state) {
          TrafficStatus.RUNNING -> buildRobotData(context, prevent, RUNNING)
          TrafficStatus.IDLE, TrafficStatus.STOP -> buildRobotData(context, prevent, IDLE)
          TrafficStatus.DEADLOCK -> buildRobotData(context, prevent, DEADLOCK)
          else -> buildRobotData(context, prevent, ERROR)
        }
      }
      if (!noCalculate(prevent)) {
        val response = PreventResult().processFlow(prevent, config.preRotate)
        processIdleRobotOnTheWay(prevent)
        contexts.forEach {
          val r = response[it.robotName]
          it.plan.prevent = r
        }
        return
      }
      // 调用 prevent 计算
      val result = PreventSearch().calculate(prevent, config.preRotate)
      val response = PreventResult().processRequest(result)
      processIdleRobotOnTheWay(result)
      // 更新各自的机器人信息
      contexts.forEach {
        val r = response[it.robotName]
        it.plan.prevent = r
        if (r == null && it.state == TrafficStatus.RUNNING) {
          it.plan.preventTime = System.currentTimeMillis()
        }
      }
    } catch (e: Exception) {
      logger.error("prevent error $e")
    }
    logger.info("calculate is end!!")
  }

  private fun noCalculate(prevent: PreventRequestInfo): Boolean {
    // 检查是否需要计算
    val runningRobots = prevent.runningRobots
    if (runningRobots.size == 0) {
      return false
    }
    // 检查是否需要计算
    for (robot in runningRobots) {
      if ((robot.reserveTable.isEmpty() && System.currentTimeMillis() - robot.time > 4 * 1000L) ||
        checkReserveTableSize(robot)
      ) {
        return true
      }
    }

    updateTable(runningRobots)
    return false
  }

  // 检查剩余预约表的长度，若小于 MIN_SIZE，则需要再计算更新长度
  private fun checkReserveTableSize(robot: PreventRobotInfo): Boolean {
    // 没有 || 已经计算到终点了的 不需要再计算了
    if (robot.reserveTable.isEmpty() || robot.reserveTable.values.last().stop()) return false
    // 获取当前的索引
    val index = robot.index
    return robot.reserveTable.filter { it.value.index >= index }.size < MIN_SIZE
  }

  private fun updateTable(runningRobots: MutableList<PreventRobotInfo>) {
    val recordIndex: MutableMap<String, Int> = mutableMapOf()
    for (robot in runningRobots) {
      val robotCode = robot.robotName
      val index = if (robot.index == -1L) 0 else robot.index
      val reserveTable = robot.reserveTable
      if (reserveTable.isEmpty()) continue
      var key = -1
      for (table in reserveTable) {
        if (index == table.value.index) {
          key = table.key
          continue
        }
        if (key != -1) {
          recordIndex[robotCode] = key
          break
        }
      }
    }
    if (recordIndex.isEmpty()) return

    val min = recordIndex.values.minOf { it }
    for (robot in runningRobots) {
      val robotCode = robot.robotName
      val reserveTable = robot.reserveTable
      val reserve: MutableMap<Int, PathAction> = mutableMapOf()
      val rIndex = recordIndex[robotCode] ?: continue
      var key = rIndex - min
      for (table in reserveTable) {
        if (table.key >= rIndex) {
          reserve[key] = table.value
          ++key
        }
      }
      robot.reserveTable = reserve
    }
    for (robot in runningRobots) {
      val robotCode = robot.robotName
      val disableTable = robot.disableTable
      val disable: MutableMap<Int, MutableMap<String, Int>> = mutableMapOf()
      val rIndex = recordIndex[robotCode] ?: continue
      for (table in disableTable) {
        val key = table.key
        if (key - rIndex >= 0) {
          val value = table.value
          val newValue: MutableMap<String, Int> = mutableMapOf()
          for (r in value) {
            val k = r.key
            val v = r.value
            if (recordIndex[k] == null) continue

            if (v - recordIndex[k]!! >= 0) {
              newValue[k] = v - min
            }
          }
          if (newValue.isNotEmpty()) {
            disable[key - min] = newValue
          }
        }
      }
      robot.disableTable = disable
    }
  }

  // 构建机器人基础数据，并根据机器人状态进行分类
  private fun buildRobotData(context: RobotContext, prevent: PreventRequestInfo, flag: Int) {
    val key: String = LockType.ROBOT.toString() + "-><-" + context.groupName + "-><-" + context.robotName
    val spaceLock = LockService.querySpaceLockByKey(key, context.sceneId, context.mapName)
    if (spaceLock == null) {
      logger.error("spaceLock is null $key")
      return
    }
    val curPosition = context.baseDomain.curPoint
    if (curPosition?.pointName == null || curPosition.pointName.isEmpty()) {
      logger.error("${context.robotName} curPosition is null $curPosition")
      return
    }
    val curPoint =
      MapService.findPointByName(context.sceneId, context.mapName, context.groupName, curPosition.pointName)
    val preventRobotInfo = PreventRobotInfo(
      robotName = context.robotName,
      sceneId = context.sceneId,
      mapName = context.mapName,
      groupName = context.groupName,
      robotMotionType = context.robotMotionType,
      robotHeading = context.baseDomain.robotHeading,
      point = curPoint,
      orderNo = context.request.orderNo,
      stepNo = context.request.stepNo,
      restPath = context.plan.restPath(),
      index = context.plan.index,
      allocateIndex = context.plan.allocateIndex,
      reserveSpaceLock = spaceLock,
      reserveTable = if (context.plan.prevent != null) context.plan.prevent!!.reserveTable else mutableMapOf(),
      disableTable = if (context.plan.prevent != null) context.plan.prevent!!.disableTable else mutableMapOf(),
      time = context.plan.preventTime,
      priority = if (context.plan.prevent != null) context.plan.prevent!!.priority else 0,
      flowRobots = if (context.plan.prevent != null) context.plan.prevent!!.flowRobots else mutableListOf(),
      deadlocks = if (context.plan.prevent != null) context.plan.prevent!!.deadlocks else mutableListOf(),
    )
    if (flag == RUNNING &&
      context.plan.prevent != null &&
      (
        !context.plan.prevent!!.orderNo.equals(context.request.orderNo) ||
          context.plan.prevent!!.stepNo != context.request.stepNo
        )
    ) {
      preventRobotInfo.reserveTable = mutableMapOf()
      preventRobotInfo.disableTable = mutableMapOf()
    }
    when (flag) {
      RUNNING -> prevent.runningRobots.add(preventRobotInfo)
      IDLE -> prevent.idleRobots.add(preventRobotInfo)
      DEADLOCK -> prevent.deadLockRobots.add(preventRobotInfo)
      ERROR -> prevent.blockRobots.add(preventRobotInfo)
    }
  }

  /**
   *  计算空闲车的位置和直行车之间的距离，提前将空闲车推离开阻挡得路线上
   * */
  private fun processIdleRobotOnTheWay(prevent: PreventRequestInfo) {
    val idleRobots = prevent.idleRobots
    val idleMap: MutableMap<String, PreventRobotInfo> = mutableMapOf()
    idleRobots.forEach { idleMap[it.point.pointName] = it }
    prevent.runningRobots.forEach { robot ->
      var distance = 0.0
      robot.restPath.forEach {
        if (it.target.pointName in idleMap.keys) {
          distance += it.target.distance(it.start)
          if (distance > 20) return

          // 先检查机器人是否空闲
          val prf = idleMap[it.target.pointName] ?: return
          val message = IdleDeadLockProcessor.check(
            robot.robotName,
            Collections.singletonList(
              BlockItem(
                code = prf.robotName,
                type = LockType.ROBOT_WAIT,
                point = it.target,
                version = prevent.version,
              ),
            ),
          )
          if (message.state) {
            IdleDeadLockProcessor.handle(message)
          }
        }
      }
    }
  }
}