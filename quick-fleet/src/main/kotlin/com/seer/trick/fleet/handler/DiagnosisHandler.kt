package com.seer.trick.fleet.handler

import CheckRobotOrderReq
import com.seer.trick.I18N
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.getReqBody
import com.seer.trick.fleet.diagnosis.FleetDiagnosisService
import com.seer.trick.fleet.service.SceneService
import io.javalin.http.Context

/**
 * 诊断
 */
object DiagnosisHandler {

  fun registerHandlers() {
    val c = Handlers("api/fleet/diagnosis")
    c.get("{sceneId}/diagnosis-list", ::getDiagnosisList, auth())
    c.post("{sceneId}/robot-order", ::checkRobotOrder, auth())
    c.post("{sceneId}/robot-order-all", ::checkRobotOrderAll, auth())
  }

  /**
   * 获取所有的诊断 subject 列表
   * 并返回该诊断项需要传入哪些内容
   */
  private fun getDiagnosisList(ctx: Context) {
    val diagnosisList = FleetDiagnosisService.getDiagnosisList()
    ctx.json(diagnosisList)
  }

  private fun checkRobotOrder(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)

    val req: CheckRobotOrderReq = ctx.getReqBody()

    val reason = FleetDiagnosisService.checkRobotOrder(sr, req)

    val label = reason?.let { I18N.lo("FleetDiagnosis_" + it.code) }

    ctx.json(mapOf("pass" to (reason == null), "code" to reason?.code, "reason" to label))
  }

  private fun checkRobotOrderAll(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)

    val req: CheckRobotOrderReq = ctx.getReqBody()

    val result = FleetDiagnosisService.checkRobotOrderAll(sr, req)

    val checksResult = result.checkList.map { item ->
      CheckOneResult(
        item = I18N.lo("FleetCheckItem_${item.itemName}"),
        code = item.code,
        passed = item.passed,
        reason = if (!item.passed) {
          I18N.lo("FleetDiagnosis_${item.code}", item.params)
        } else {
          null
        },
      )
    }

    val r = CheckAllResult(result.pass, checksResult)

    ctx.json(r)
  }

  data class CheckAllResult(val pass: Boolean, val checkList: List<CheckOneResult>)

  data class CheckOneResult(val item: String, val code: String?, val passed: Boolean, val reason: String?)
}