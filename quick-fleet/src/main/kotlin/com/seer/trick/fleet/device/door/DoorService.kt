package com.seer.trick.fleet.device.door

import com.seer.trick.BzError
import com.seer.trick.I18N
import com.seer.trick.base.alarm.AlarmItem
import com.seer.trick.base.alarm.AlarmLevel
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.base.concurrent.PollingJobManager
import com.seer.trick.fleet.FleetLogger
import com.seer.trick.fleet.device.door.adapter.DoorAdapterHttp
import com.seer.trick.fleet.device.door.adapter.DoorAdapterMock
import com.seer.trick.fleet.device.door.adapter.DoorAdapterPlc
import com.seer.trick.fleet.device.door.adapter.DoorAdapterScript
import com.seer.trick.fleet.domain.MapPath
import com.seer.trick.fleet.domain.SceneStatus
import com.seer.trick.fleet.map.AreaMapCache
import com.seer.trick.fleet.service.*
import com.seer.trick.helper.JsonFileHelper
import com.seer.trick.helper.getTypeMessage
import org.slf4j.LoggerFactory
import java.io.File

/**
 * 门的基础服务
 */
object DoorService {
  
  private val logger = LoggerFactory.getLogger(javaClass)
  
  fun init() {
    //
  }
  
  fun dispose() {
    //
  }
  
  /**
   * 加载并初始化一个场景下的门
   */
  fun init(sr: SceneRuntime) = sr.withOrderLock {
    val doorConfigs: List<SceneDoor> = JsonFileHelper.readJsonFromFile(getDoorsFile(sr.sceneId)) ?: emptyList()
    sr.doors.clear()
    
    val doors = doorConfigs.map {
      val dr = DoorRuntime(it)
      init(sr, dr)
      dr
    }.associateBy { it.config.id }
    
    sr.doors.putAll(doors)
    
    PollingJobManager.submit(
      threadName = "FtDoor-${sr.no}",
      remark = "Fetching doors states for scene $sr",
      interval = { 1000L },
      logger = logger,
      workerMaxTime = 30 * 1000,
      stopCondition = { sr.status == SceneStatus.Disposed },
      exceptionContinue = true,
      tags = setOf(sr.tag),
      worker = { mainLoop(sr) },
    )
  }
  
  fun getDoorTag(sr: SceneRuntime): String = "FtDoor-${sr.no}"
  
  private fun init(sr: SceneRuntime, dr: DoorRuntime) {
    if (dr.config.disabled) return
    
    // 选择适配器类型
    if (dr.config.mock) {
      dr.adapter = DoorAdapterMock(dr.config)
    } else {
      when (dr.config.adapterType) {
        DoorAdapterType.Plc -> dr.adapter = DoorAdapterPlc(dr.config)
        DoorAdapterType.Http -> dr.adapter = DoorAdapterHttp()
        DoorAdapterType.Script -> dr.adapter = DoorAdapterScript()
        DoorAdapterType.Mock -> dr.adapter = DoorAdapterMock(dr.config)
      }
    }
    
    // 适配器初始化
    dr.adapter.init()
    
    buildPreOpenPathPaths(sr, dr)
  }
  
  /**
   * 添加门
   */
  fun add(sr: SceneRuntime, req: SceneDoor): SceneDoor {
    val door = sr.withOrderLock {
      // 自动门名称唯一
      if (sr.doors.values.any { it.config.name == req.name }) {
        throw BzError("errNameConflict", req.name) // 添加失败，已经存在同名的自动门了。
      }
      
      val door = req.copy(id = (sr.doors.values.maxOfOrNull { it.config.id } ?: 0) + 1)
      val dr = DoorRuntime(door)
      sr.doors[door.id] = dr
      init(sr, dr)
      
      val doorConfigs = sr.doors.values.map { it.config }.toMutableList()
      doorConfigs += door
      persist(sr, doorConfigs)
      door
    }
    
    logger.info("Create door of scene $sr: $door")
    
    FleetEventService.fire(
      
      FleetEvent(
        name = "Door::Create",
        sceneId = sr.sceneId,
        extra = mapOf("doorId" to door.id),
      ),
    )
    
    return door
  }
  
  /**
   * 删除门
   */
  fun remove(sr: SceneRuntime, doorIds: List<Int>) {
    sr.withOrderLock {
      for (id in doorIds) {
        // TODO
        //   1.不能删除正在被使用的门。
        //   2.如何判断门正在被使用？
        
        val dr = sr.doors.remove(id)
        if (dr != null) dispose(dr)
      }
      
      val doorConfigs = sr.doors.values.map { it.config }
      persist(sr, doorConfigs)
    }
    
    logger.info("Removed doors from scene ${sr.sceneId}: $doorIds")
    
    FleetEventService.fire(
      
      FleetEvent(
        name = "Door::Remove",
        sceneId = sr.sceneId,
        extra = mapOf("doorIds" to doorIds),
      ),
    )
  }
  
  /**
   * 不抛异常
   */
  private fun dispose(dr: DoorRuntime) {
    dr.adapter.dispose()
  }
  
  /**
   * 修改门。
   */
  fun update(sr: SceneRuntime, door: SceneDoor) {
    sr.withOrderLock {
      // TODO 不能修改正在被使用的门
      
      val dr = getDoorById(sr, door.id) ?: return@withOrderLock
      dispose(dr)
      dr.config = door
      init(sr, dr)
      
      val doorConfigs = sr.doors.values.map { it.config }
      persist(sr, doorConfigs)
    }
    
    logger.info("Updated door ${door.id} of scene ${sr.sceneId}")
    
    FleetEventService.fire(
      
      FleetEvent(
        name = "Door::Update",
        sceneId = sr.sceneId,
        extra = mapOf("doorId" to door.id),
      ),
    )
  }
  
  private fun persist(sr: SceneRuntime, doors: List<SceneDoor>) {
    JsonFileHelper.writeJsonToFile(getDoorsFile(sr.sceneId), doors, true)
  }
  
  private fun getDoorsFile(sceneId: String): File {
    val dir = SceneFileService.getSceneDir(sceneId)
    return File(dir, "doors.json")
  }
  
  /**
   * key 是此门下的路径。value 是此门前后的一些路径，通过这些路径并以 key 路径为目标时，提前开门
   */
  private fun buildPreOpenPathPaths(sr: SceneRuntime, dr: DoorRuntime) {
    val pps: MutableMap<String, MutableSet<String>> = HashMap()
    
    val areaR = sr.mapCache.areaById[dr.config.areaId] ?: run {
      logger.error("No area by id ${dr.config.areaId}")
      return
    }
    
    for (pathKey in dr.config.controlledPathKeys) {
      val pathR = areaR.mergedMap.pathKeyMap[pathKey] ?: continue
      val allPaths = mutableSetOf<String>()
      listPathsToPoint(pathR.path.fromPointName, dr.config.openPreDist, areaR.mergedMap, allPaths)
      allPaths.remove(pathKey)
      pps[pathKey] = allPaths
    }
    
    dr.preOpenPathPaths = pps
  }
  
  /**
   * 列出到这个点的路径在 dist 内的所有路径
   */
  private fun listPathsToPoint(pointName: String, dist: Double, areaR: AreaMapCache, allPaths: MutableSet<String>) {
    val paths = areaR.pointNameMap[pointName]?.backwardPaths ?: return
    for (path in paths) {
      val reversedKey = MapPath.getKey(path.toPointName, path.fromPointName)
      if (allPaths.contains(reversedKey) || allPaths.contains(path.key)) continue // 防止循环
      allPaths.add(path.key)
      val d = dist - path.actualLength
      if (d > 0) listPathsToPoint(path.fromPointName, d, areaR, allPaths)
    }
  }
  
  /**
   * 获取门的展示信息
   */
  fun listDoorUiReports(sr: SceneRuntime): Map<String, DoorUiReport> {
    val result: MutableMap<String, DoorUiReport> = mutableMapOf()
    sr.doors.values.map {
      result.put(it.config.name, it.toUiReport())
    }
    return result
  }
  
  /**
   * 定时任务
   */
  private fun mainLoop(sr: SceneRuntime) {
    if (sr.basic.disabled) return
    
    // 上报状态
    reportAll(sr)
    // 持续发开门
    keepOpen(sr)
    // 监控急停
    stopRobotsIfDoorException(sr)
  }
  
  /**
   * 持续发开门
   */
  private fun keepOpen(sr: SceneRuntime) {
    DoorDispatcher.openOrCloseDoorAsNeeded(sr)
  }
  
  /**
   * 所有门上报状态
   */
  private fun reportAll(sr: SceneRuntime) {
    for (dr in sr.doors.values) {
      fetchReports(sr, dr)
    }
  }
  
  /**
   * 获取门的运行状态并更新到内存中。
   */
  private fun fetchReports(sr: SceneRuntime, dr: DoorRuntime) {
    if (dr.config.disabled) return
    try {
      // TODO 不要用 uiReport
      val old = dr.toUiReport()
      val report = dr.adapter.report()
      dr.online = report.online
      dr.status = report.mainStatus
      dr.fault = report.fault
      dr.faultMsg = report.faultMsg
      dr.lastStatusUpdateTime = report.timestamp
      
      // 状态变更时，打印日志
      val new = dr.toUiReport()
      if (old.toStringExceptTime() != new.toStringExceptTime()) {
        FleetLogger.info(
          module = "FetchDoorReport",
          subject = "Changed",
          sr = sr,
          robotName = null,
          msg = mapOf("name" to dr.config.name, "old" to old, "new" to new),
        )
      }
    } catch (e: Exception) {
      // TODO 之后有需要时，再对不同的异常做针对性的处理。
      //  1.由于网络问题导致的问题。
      //  2.并发访问 plc 设备时，也可能会出现问题。
      //  3.更新门的运行状态为故障，online=false
      dr.online = false // 一定么？
      dr.fault = true
      dr.faultMsg = e.getTypeMessage() // todo 是否会存在多条错误。
      // 此时不修改 status 和 lastStatusUpdateTime
      val ai = AlarmItem(
        sceneId = sr.sceneId,
        group = "Fleet",
        code = "FetchDoorReportFailed",
        key = "FetchDoorReportFailed-${dr.config.name}",
        level = AlarmLevel.Error,
        message = I18N.lo("errFetchDoorReportFailed", listOf(dr.config.name, e.getTypeMessage())),
        tags = setOf(sr.tag),
      )
      AlarmService.addItem(ai, ttl = 5000)
    }
  }
  
  /**
   * 启用、禁用门的仿真模式。
   */
  fun changeAllDoorsSimulated(sr: SceneRuntime, simulated: Boolean) {
    sr.withOrderLock {
      // TODO 不能修改正在被使用的门
      val doorConfigs = sr.doors.values.map { dr ->
        
        // 启用仿真时，忽略已经是仿真适配器的门
        // 取消仿真时，忽略适配器类型就是 DoorAdapterType.Mock 的门。
        if ((simulated && dr.adapter is DoorAdapterMock) ||
          (!simulated && dr.config.adapterType == DoorAdapterType.Mock)
        ) {
          return@map dr.config
        }
        
        // 先 dispose
        dispose(dr)
        
        // 修改内存的配置
        val config = dr.config.copy(mock = simulated)
        dr.config = config
        
        // 重新初始化
        init(sr, dr)
        
        FleetEventService.fire(
          
          FleetEvent(
            name = "Door::Update",
            sceneId = sr.sceneId,
            extra = mapOf("doorId" to dr.config.id, "msg" to "mock = $simulated"),
          ),
        )
        
        config
      }
      
      persist(sr, doorConfigs)
    }
    logger.info("Change simulated of all doors for $sr")
  }
  
  fun getDoorById(sr: SceneRuntime, doorId: Int) = sr.doors[doorId]
  
  fun mustGetDoorById(sr: SceneRuntime, doorId: Int) = sr.doors[doorId]
    ?: throw BzError("errDoorNotFoundById", sr.basic.name, doorId)
  
  /**
   * 控制开门
   */
  fun openDoor(sr: SceneRuntime, doorId: Int, remark: String) = sr.withOrderLock {
    // TODO 有哪些不能开门的情况，并且要提供必要的提示的？
    mustGetDoorById(sr, doorId).adapter.openDoor(remark)
  }
  
  /**
   * 控制关门
   */
  fun closeDoor(sr: SceneRuntime, doorId: Int, remark: String) = sr.withOrderLock {
    // TODO 有哪些不能开门的情况，并且要提供必要的提示的？
    mustGetDoorById(sr, doorId).adapter.closeDoor(remark)
  }
  
  /**
   * 如果机器人已经踏上开门路径，但门出意外（失联、停用、非打开），机器人急停；
   * 后续靠“释放急停”机制恢复执行
   */
  private fun stopRobotsIfDoorException(sr: SceneRuntime) {
    for (dr in sr.doors.values) {
      // 门开了，就没事
      if (dr.status == DoorMainStatus.Opened && dr.online && !dr.fault) continue
      
      // 没有强制开门的需求
      val demands = dr.openDemands.filter { !it.pre }
      if (demands.isEmpty()) continue
      
      // 可能会让机器人多次软急停，先不管
      for (dd in demands) {
        val rr = sr.mustGetRobot(dd.robotName)
        val pps = rr.selfReport?.stand?.pathPositions ?: continue
        // 机器人在门下路径上，并且已离开起点
        // 通过百分比的判断方式不太好，但先这样
        if (pps.any { it.pathKey == dd.underPath && it.pathPercentage > .2 && it.pathPercentage < .8 }) {
          logger.error(
            "Halt robot [$rr] for door [$dr] exception, under path=${dd.underPath}, main status=${dr.status}, " +
              "online=${dr.online}, fault=${dr.fault}, fault msg=${dr.faultMsg}",
          )
          RobotService.setSoftEmc(sr, rr, true)
        }
      }
    }
  }
}