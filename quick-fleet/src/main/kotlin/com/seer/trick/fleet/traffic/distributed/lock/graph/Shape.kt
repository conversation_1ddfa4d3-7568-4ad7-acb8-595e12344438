package com.seer.trick.fleet.traffic.distributed.lock.graph

/**
 * 图形 接口
 * */
interface Shape {

  var type: GraphType

  // 定义和图形相交的检测方法
  fun intersection(shape: Shape): Boolean = when (shape.type) {
    GraphType.RECTANGLE -> {
      intersectionRectangle(shape as Rectangle)
    }

    GraphType.CIRCLE -> {
      intersectionCircle(shape as Circle)
    }

    GraphType.POLYGON -> {
      intersectionPolygon(shape as Polygon)
    }

    GraphType.SECTOR -> {
      intersectionSector(shape as Sector)
    }
  }

  // 和矩形相交的检测方法
  fun intersectionRectangle(rect: Rectangle): <PERSON><PERSON><PERSON>

  // 和圆相交的检测方法
  fun intersectionCircle(circle: Circle): Bo<PERSON>an

  // 和多边形相交的检测方法
  fun intersectionPolygon(polygon: Polygon): Bo<PERSON>an

  // 和扇形相交的检测方法
  fun intersectionSector(sector: Sector): Boolean

  fun getBoundingBox(): BoundingBox

//  fun updateBoundingBox()
  fun copy(): Shape
}