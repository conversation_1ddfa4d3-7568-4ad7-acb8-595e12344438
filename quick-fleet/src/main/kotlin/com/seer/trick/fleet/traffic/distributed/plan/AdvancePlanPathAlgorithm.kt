package com.seer.trick.fleet.traffic.distributed.plan

import com.seer.trick.fleet.domain.MoveDirection
import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper
import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper.RIGHT_ANGLE
import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper.SECTOR_VALUE
import com.seer.trick.fleet.traffic.distributed.map.Line
import com.seer.trick.fleet.traffic.distributed.map.Point
import com.seer.trick.fleet.traffic.distributed.plan.model.PointData

/**
 *  仅可往前走的运动模型
 * */
class AdvancePlanPathAlgorithm(val request: PlanRequest) : PlanPathAlgorithm(request) {

  override fun findRobotHeadings(start: Point, line: Line, robotHeading: Int): List<Int> {
    val robotHeadings = ArrayList<Int>()
    // 判断这条边是否允许此车型通过, 点不可旋转时
    if (!start.rotate || cantRotatePoint.contains(start.pointName)) {
      // 判断路线的方向是否允许当前车头方向通过
      if (line.driveDirection == MoveDirection.Dual &&
        (
          AngleHelper.sameAngleInFiveDegree(robotHeading, line.enterDir) ||
            AngleHelper.opposeAngle(robotHeading, line.enterDir)
          )
      ) {
        robotHeadings.add(robotHeading)
      }
      if (line.driveDirection == MoveDirection.Forward &&
        AngleHelper.sameAngleInFiveDegree(robotHeading, line.enterDir)
      ) {
        robotHeadings.add(robotHeading)
      }
      if (line.driveDirection == MoveDirection.Backward && AngleHelper.opposeAngle(robotHeading, line.enterDir)) {
        robotHeadings.add(robotHeading)
      }
      return robotHeadings
    }

    // 线上可以行驶的方向
    if (line.driveDirection == MoveDirection.Dual) {
      robotHeadings.add(line.enterDir)
    } else {
      if (line.driveDirection == MoveDirection.Forward) {
        robotHeadings.add(line.enterDir)
      }
      if (line.driveDirection == MoveDirection.Backward) {
        robotHeadings.add(AngleHelper.processAngle(line.enterDir + AngleHelper.DOWN_ANGLE))
      }
    }

    return robotHeadings
  }

  override fun costS(pointData: PointData?, line: Line): Double = defaultCost

  override fun costR(rHeading: Int, line: Line): Double = defaultCost

  override fun costT(rHeading: Int, robotHeading: Int): Double =
    if (AngleHelper.sameAngleInFiveDegree(rHeading, robotHeading)) {
      0.0
    } else {
      var tn = AngleHelper.vectorAngle(rHeading, robotHeading) / RIGHT_ANGLE
      if (AngleHelper.vectorAngle(rHeading, robotHeading) % RIGHT_ANGLE > SECTOR_VALUE) {
        tn += 1
      }
      1.5 * tn
    }
}