package com.seer.trick.fleet.traffic.distributed.lock.graph

import com.seer.trick.fleet.traffic.distributed.helper.GraphHelper

/**
 *  矩形
 * */
class Rectangle(override var type: GraphType, val points: List<Vector>, val box: BoundingBox) : Shape {

  override fun intersectionRectangle(rect: Rectangle): Bo<PERSON>an {
    if (!rect.box.intersect(box)) return false
    return rectangleCheckCollision(points, rect.points)
  }

  override fun intersectionCircle(circle: Circle): Boolean = circle.intersectionRectangle(this)

  override fun intersectionPolygon(polygon: Polygon): Boolean = polygon.intersectionRectangle(this)

  override fun intersectionSector(sector: Sector): Boolean {
    // todo 扇形与矩形相交
    return false
  }

  override fun getBoundingBox(): BoundingBox = box

  override fun copy(): Shape {
    val ps: MutableList<Vector> = mutableListOf()
    for (p in this.points) {
      ps.add(p.copy())
    }
    return Rectangle(type = type, points = ps, box = box)
  }

  /**
   * 矩形碰撞检测
   * 基于 SAT 定理
   * */
  private fun rectangleCheckCollision(shapeA: List<Vector>, shapeB: List<Vector>): Boolean {
    if (shapeA.size <= 2 || shapeB.size <= 2) {
      return false
    }
    return !GraphHelper.satTheorem(shapeA, shapeB) &&
      !GraphHelper.satTheorem(shapeB, shapeA)
  }
}