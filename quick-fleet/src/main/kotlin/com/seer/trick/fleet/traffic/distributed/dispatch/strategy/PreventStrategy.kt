package com.seer.trick.fleet.traffic.distributed.dispatch.strategy

import com.seer.trick.fleet.traffic.distributed.context.domain.RobotContext
import com.seer.trick.fleet.traffic.distributed.dispatch.ScheduleRequest
import com.seer.trick.fleet.traffic.distributed.helper.LogParseHelper
import com.seer.trick.fleet.traffic.distributed.service.DistributedTrafficService
import org.slf4j.LoggerFactory
import java.util.LinkedList

object PreventStrategy : Strategy {

  private val logger = LoggerFactory.getLogger(javaClass)
  override fun check(context: RobotContext): Boolean =
    DistributedTrafficService.findSceneBySceneId(context.sceneId).config.enablePrevent

  override fun handle(request: ScheduleRequest) {
    val context = request.context
    val allocatedIndex = context.plan.allocateIndex
    val prevent = context.plan.prevent
    var preventIndex: Long? = null
    if (prevent != null &&
      prevent.orderNo == context.request.orderNo &&
      prevent.stepNo == context.request.stepNo &&
      prevent.index != -1
    ) {
      val pathAction = prevent.reserveTable[prevent.index]
      if (pathAction == null || pathAction.index < allocatedIndex) {
        logger.warn(
          "${request.robotName} prevent index is ${pathAction?.index} | allocatedIndex index is $allocatedIndex",
        )
        return
      }
      preventIndex = pathAction.index
    }

    if (preventIndex == null) {
      logger.warn("${request.robotName} prevent index is null")
      return
    }

    if (prevent?.deadlocks?.isNotEmpty() == true) {
      logger.debug("${request.robotName} deadlocks is ${prevent.deadlocks}")
      request.blockRobot.addAll(prevent.deadlocks)
    }
    request.version = prevent?.version

    if (request.requestPath.isEmpty()) {
      logger.error("${request.robotName} request path is empty")
      return
    }

    val lastAction = request.requestPath.last()
    if (lastAction.index <= preventIndex) {
      return
    }

    if (request.allocatedIndex == -1L && preventIndex == 0L) {
      logger.debug("${request.robotName} allocatedIndex is -1 and preventIndex is 0")
      request.requestPath = LinkedList()
      if (prevent?.blockRobot?.isNotEmpty() == true) {
        request.blockRobot.addAll(prevent.blockRobot)
      }
      return
    }
    // 截取到 preventIndex 位置
    val requestPath = request.requestPath.filter { it.index <= preventIndex }.toMutableList()
    if (prevent?.blockRobot?.isNotEmpty() == true) {
      request.blockRobot.addAll(prevent.blockRobot)
    }

    logger.debug(
      "${request.robotName} prevent index is $preventIndex request path is " +
        "${LogParseHelper.parsePathLog(requestPath)}",
    )
    request.requestPath = requestPath
  }
}