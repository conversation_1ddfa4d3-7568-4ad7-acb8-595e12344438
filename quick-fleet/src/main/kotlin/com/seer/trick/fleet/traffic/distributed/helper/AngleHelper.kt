package com.seer.trick.fleet.traffic.distributed.helper

import com.seer.trick.fleet.traffic.distributed.lock.graph.Vector
import com.seer.trick.fleet.traffic.distributed.map.Position
import com.seer.trick.fleet.traffic.distributed.map.Site
import kotlin.math.*

/**
 * 角度 约定为 0 ~ 36000
 * 进入调度系统时，角度若没有，则为默认错误角度 ERROR_ANGLE，在计算时不进行计算
 * */
object AngleHelper {

  // 表示没有角度或者错误角度
  const val ERROR_ANGLE: Int = 88888

  private const val MAX_ANGLE: Int = 36000

  const val DOWN_ANGLE: Int = 18000

  const val RIGHT_ANGLE: Int = 9000

  private const val FIVE_THRESHOLD_VALUE: Int = 500 // 默认在 5° 范围内不算旋转

  private const val ONE_THRESHOLD_VALUE: Int = 10 // 在 0.1° 范围内

  const val SECTOR_VALUE: Int = 4500

  // 根据两点获取角度
  fun getAngle(s: Site, e: Site): Int = getAngle(s.x, s.y, e.x, e.y)

  fun getAngle(s: Position, e: Position): Int = getAngle(s.x, s.y, e.x, e.y)

  fun getAngle(s: Vector, e: Vector): Int = getAngle(s.x, s.y, e.x, e.y)

  fun getAngle(x1: Double, y1: Double, x2: Double, y2: Double): Int {
    val theta: Double = atan2((y2 - y1), (x2 - x1))
    var angle = (theta * (18000 / Math.PI)).toInt()
    angle = if (angle < 0) MAX_ANGLE + angle else angle
    return processAngle(angle)
  }

  // 处理 角度信息
  fun processAngle(angle: Int): Int {
    if (angle < 0) {
      return angle + MAX_ANGLE
    }
    return angle % MAX_ANGLE
  }

  fun sameFixAngle(x: Int, offerAngle: Int): Boolean =
    sameAngleInFiveDegree(x, processAngle(offerAngle + RIGHT_ANGLE)) ||
      sameAngleInFiveDegree(x, processAngle(offerAngle + DOWN_ANGLE)) ||
      sameAngleInFiveDegree(x, processAngle(offerAngle + DOWN_ANGLE + RIGHT_ANGLE)) ||
      sameAngleInFiveDegree(x, offerAngle)

  fun convertAngle(theta: Double?): Int {
    if (theta == null) {
      return ERROR_ANGLE
    }
    var angle = (theta * (18000 / Math.PI)).toInt()
    angle = if (angle < 0) MAX_ANGLE + angle else angle
    return processAngle(angle)
  }

  fun degreesToRadians(degrees: Int?): Double {
    if (degrees == null) {
      return ERROR_ANGLE.toDouble()
    }
    val theta = if (degrees > DOWN_ANGLE) degrees - MAX_ANGLE else degrees

    return theta * (Math.PI / 18000)
  }

  fun sameAngleInFiveDegree(x1: Int, x2: Int): Boolean {
    val vectorAngle = vectorAngle(x1, x2)
    return vectorAngle <= FIVE_THRESHOLD_VALUE
//    if (x1 > DOWN_ANGLE + RIGHT_ANGLE && x2 < RIGHT_ANGLE) {
//      return (MAX_ANGLE - x1) + x2 <= THRESHOLD_VALUE
//    } else if (x2 > DOWN_ANGLE + RIGHT_ANGLE && x1 < RIGHT_ANGLE) {
//      return (MAX_ANGLE - x2) + x1 <= THRESHOLD_VALUE
//    }
//    return abs(x2 - x1) <= THRESHOLD_VALUE
  }

  fun sameAngleInOneDegree(x1: Int, x2: Int): Boolean {
    val vectorAngle = vectorAngle(x1, x2)
    return vectorAngle <= ONE_THRESHOLD_VALUE
  }

  fun needRotate(x1: Int, x2: Int): Boolean {
    val vectorAngle = vectorAngle(x1, x2)
    return vectorAngle > SECTOR_VALUE
  }

  fun vectorAngle(x1: Int, x2: Int): Int {
    if (abs(x2 - x1) >= DOWN_ANGLE) {
      return MAX_ANGLE - max(x1, x2) + min(x1, x2)
    }
    return abs(x2 - x1)
//
//    if (x1 >= DOWN_ANGLE + RIGHT_ANGLE && x2 < RIGHT_ANGLE) {
//      return (MAX_ANGLE - x1) + x2
//    } else if (x2 >= DOWN_ANGLE + RIGHT_ANGLE && x1 < RIGHT_ANGLE) {
//      return (MAX_ANGLE - x2) + x1
//    }
//    return abs(x2 - x1)
  }

  fun opposeAngle(x1: Int, x2: Int): Boolean = abs(vectorAngle(x1, x2) - DOWN_ANGLE) <= FIVE_THRESHOLD_VALUE

  fun rightAngle(x1: Int, x2: Int): Boolean = abs(abs(x2 - x1) - RIGHT_ANGLE) <= FIVE_THRESHOLD_VALUE ||
    abs(abs(x2 - x1) - (DOWN_ANGLE + RIGHT_ANGLE)) <= FIVE_THRESHOLD_VALUE
}