package com.seer.trick.fleet.service

import com.seer.trick.fleet.device.door.DoorDispatcher
import com.seer.trick.fleet.order.StepExecuteContext
import com.seer.trick.fleet.traffic.TrafficTaskRuntime

/**
 * MoveAction 的前后处理器。
 * 目前支持下发前处理、执行完成后处理。
 */
object RobotMoveProcessor {

  /**
   * 给机器人发送动作前的预处理。标记 releasePreprocessOk。
   * 处理器注意监控动作和交管任务被取消。
   * 尽量不抛异常。
   */
  fun beforeSendingMoveActionToRobot(sec: StepExecuteContext, tt: TrafficTaskRuntime) {
    val rr = sec.rr
    for (ma in rr.moveActions) {
      // 注意这里有 sent 的
      // 跳过已处理的
      if (ma.releasePreprocessOk) continue

      // 门调度处理，如果处理不成功，不能下发，暂时退出
      val ok = DoorDispatcher.beforeSendingMoveActionToRobot(rr, ma, tt)
      if (!ok) break

      // 可以下发
      ma.releasePreprocessOk = true
    }
  }

  /**
   * 动作执行完后的后处理：
   * 1. 关门
   */
  fun afterMoveActionDone(rr: RobotRuntime, ma: MoveActionRuntime) {
    // 门调度处理
    DoorDispatcher.afterMoveActionDone(rr, ma)
  }
}