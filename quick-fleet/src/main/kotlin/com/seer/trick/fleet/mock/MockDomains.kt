package com.seer.trick.fleet.mock

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.seer.trick.BzError
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.fleet.domain.Point2D
import com.seer.trick.fleet.mock.service.Step3066
import com.seer.trick.fleet.seer.*
import com.seer.trick.fleet.service.SceneFileService
import com.seer.trick.helper.NumHelper
import java.nio.charset.StandardCharsets
import java.util.Date

/**
 * 仿真配置。序列化到文件存储。
 */
data class MockConfig(
  val actionsCosts: MockActionsCosts = MockActionsCosts(), // 机器人动作耗时配置
  val battery: MockBattery = MockBattery(), // 仿真充电/耗电配置
  var robots: List<MockSeerRobotConfig> = mutableListOf(), // 仿真机器人的配置
)

/**
 * 仿真充电/耗电配置。
 */
data class MockBattery(
  val enabled: Boolean = false, // 仿真充电/耗电
  val chargePeriod: Long = 3 * 1000, // 充电时，电量上升 1% 的时间间隔，单位 ms
  val consumePeriod: Long = 10 * 1000, // 耗电时，电量下降 1% 的时间间隔，单位 ms
)

/**
 * 机器人动作耗时配置。序列化到文件存储。
 */
data class MockActionsCosts(
  val maxVelocity: Double? = null, // 最大前进速度
  val maxBackVelocity: Double? = null, // 最大后退速度
  val maxRotateVelocity: Double? = null, // 最大旋转速度 单位为 度/秒

  val defaultCost: Double = 0.0, // 默认耗时，单位秒
  val actions: Map<String, MockActionCost> = emptyMap(), // 分动作的配置
)

/**
 * 单个动作的耗时配置。
 */
data class MockActionCost(
  val name: String = "", // operation / binTask
  val cost: Double = 0.0, // 默认耗时，单位秒
)

/**
 * 一个仿真机器人的配置。序列化到文件存储。
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class MockSeerRobotConfig(
  val id: String = "", // 机器人 ID
  val sceneId: String = "", // 场景 ID
  val name: String = "", // 机器人名称
  val disabled: Boolean = false, // 停用
  val remark: String = "",
  val fleetTcpServerIp: String? = null, // 调度 ip
  val fleetTcpServerPort: Int? = null, // 调度 端口
  val robotTcpServerPortStart: Int? = null, // 机器人启用 RBK TCP 协议，做服务器
  // 仿真配置
  val initMap: String? = null, // 出生地图
  val initPoint: String? = null, // 出生点位
  val initPositionX: Double? = null, // 出生位置坐标 X
  val initPositionY: Double? = null, // 出生位置坐标 Y
  val initTheta: Double = 0.0, // 出生朝向
  val initBattery: Double? = 1.0, // 出生电量，默认为 1.0。可为 null 兼容历史数据
  // 运动
  val actionMockType: ActionMockType? = null, // 动作仿真模式
  // 其他
  val cancelAfterEmc: Boolean = true, // 急停后取消机器人任务
  val setMasterAfterEmc: Boolean = true, // 急停后 rbk 获取控制权
  val unsetMasterAfterUnClearEmc: Boolean = false, // 解除急停后，rbk 释放控制权
  val ip: String = "", // 机器人 IP，显示用
  val containerNum: Int? = null, // 背篓数量，null 即不启用，多储位机器人时启用，值应该大于 1
  val keepInArm: Boolean? = null, // 料箱机器人 N+1 模式，在货叉中背一个走
  // 地图
  val maps: List<MockSeerRobotMap> = emptyList(), // 地图列表
) {

  fun getFleetTcpServerPortOrDefault(): Int = fleetTcpServerPort ?: 5820

  fun toRecord(): MockSeerRobotRecord = MockSeerRobotRecord(
    id = id,
    name = name,
    map = initMap,
    point = initPoint,
    x = initPositionX ?: 0.0,
    y = initPositionY ?: 0.0,
    theta = initTheta,
    battery = initBattery,
    charging = false,
    emc = false,
    blocked = false,
    alarms = emptyList(),
    currentLock = CurrentLock(),
    selfBins = buildEmptySelfBins(this),
    keepInArm = keepInArm,
  )

  companion object {
    fun buildEmptySelfBins(cfg: MockSeerRobotConfig): MutableList<MockRbkSelfBin> {
      val containers = mutableListOf<MockRbkSelfBin>()
      // TODO 单负载的机器人不初始化 selfBins 是否合理
      // 一般开启了 N + 1 则数量应该大于 1 的
      if (cfg.containerNum != null && cfg.containerNum > 1) {
        for (i in 0 until cfg.containerNum) {
          containers.add(MockRbkSelfBin(i.toString(), "", "", false))
        }
        if (cfg.keepInArm == true) {
          containers.removeLast()
          containers.add(MockRbkSelfBin(999.toString(), "", "", false))
        }
      }
      return containers
    }
  }
}

/**
 * 动作仿真类型。目前仅支持简单仿真。
 */
enum class ActionMockType {
  Simple,
}

/**
 * 一个仿真机器人的运行时状态记录。序列化到文件存储。
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class MockSeerRobotRecord(
  val id: String = "", // 机器人 ID
  val name: String = "", // 机器人名称
  val map: String? = null, // 当前地图
  val point: String? = null, // 当前点位
  val x: Double = 0.0, // 当前位置坐标 X
  val y: Double = 0.0, // 当前位置坐标 Y
  val theta: Double = 0.0, // 当前朝向 0-2π
  val battery: Double? = null, // 当前电量 0-1
  val charging: Boolean = false, // 充电中标记
  val emc: Boolean = false, // 急停标志
  val softEmc: Boolean = false, // 软急停。
  val blocked: Boolean = false, // 被阻挡标志
  val alarms: List<MockSeerRobotAlarm> = emptyList(), // 告警列表
  val currentLock: CurrentLock = CurrentLock(), // 控制权
  val master: String? = null, // 控制者
  val goodsRegion: GoodsRegion? = null, // 货物区域
  val selfBins: List<MockRbkSelfBin> = emptyList(), // 机器人背篓，只对多负载机器人有效
  val keepInArm: Boolean? = null, // 料箱机器人 N+1 模式，在货叉中背一个走
  val loadmapStatus: Int = 0, // 切换地图后地图加载的状态，0 为载入失败，1 为载入成功，2 为正在载入
) {
  fun fatals(): List<EntityValue> = listAlarms("fatal")

  fun errors(): List<EntityValue> = listAlarms("error")

  fun warnings(): List<EntityValue> = listAlarms("warn")

  fun notices(): List<EntityValue> = listAlarms("notice")

  private fun listAlarms(t: String): List<EntityValue> = alarms.filter { it.level.lowercase().contains(t) }
    .map {
      mutableMapOf("code" to NumHelper.anyToInt(it.code), "desc" to it.message, "times" to 1, "timestamp" to it.time)
    }
}

data class CurrentLock(
  var locked: Boolean = false,
  var nickName: String? = null,
  var address: String? = null, // 机器人控制权所有者的地址：IP + Port
)

/**
 * 表示仿真机器人的一张地图。
 */
data class MockSeerRobotMap(
  val mapName: String = "", // 用户、接口使用的地图名称。
  val mapFile: String = "", // 地图在磁盘存放文件路径。
  val mapMd5: String = "", // MD5
  val remark: String = "", // 自定义备注。
) {
  fun toSmap(sceneId: String): Smap? {
    val f = SceneFileService.pathToFile(sceneId, mapFile)
    if (f.exists()) {
      val mapStr = f.readText(StandardCharsets.UTF_8)
      return SmapHelper.strToSmap(mapStr)
    }
    return null
  }

  fun toSmapFile(sceneId: String): SmapFile? {
    val f = SceneFileService.pathToFile(sceneId, mapFile)
    if (f.exists()) {
      val mapStr = f.readText(StandardCharsets.UTF_8)
      val (smap, md5) = SmapHelper.strToSmapMd5(mapStr)
      return SmapFile(mapName, md5, smap)
    }
    return null
  }
}

/**
 * 表示一个机器人的告警。
 */
data class MockSeerRobotAlarm(
  val level: String = "", // 告警级别
  val code: String = "", // 告警代码
  val message: String = "", // 告警信息
  val time: Date = Date(), // 首次告警时间
)

/**
 * 表示机器人的一个路径导航任务，即通过 3051、3066 下发的路径导航。
 * 对应业务对象。
 */
data class MockSeerRobotNavTask(
  val id: String = "", // 任务 ID
  val robotName: String = "", // 机器人名
  val status: Int? = null, // 状态
)

/**
 * 对机器人路径导航任务的处理动作
 */
enum class RobotNavTaskAction {
  Pause,
  Resume,
  Cancel,
}

data class MockSeerRobotsSnapshot(
  val record: MockSeerRobotRecord,
  // TODO 导航任务
)

/**
 * 仿真实时数据
 */
data class MockRealtimeFrame(
  val paused: Boolean, // 是否暂停
  val mockSpeedFactor: Double, // 仿真倍率
  val battery: Boolean, // 是否仿真耗电、充电
  val robots: List<MockRobotRealtimeFrame>, // 机器人
)

/**
 * 一个机器人的实时数据，包括配置。
 */
data class MockRobotRealtimeFrame(
  val config: MockSeerRobotConfig,
  val record: MockSeerRobotRecord?,
  var currentTask: MoveTaskRuntime? = null, // 当前路径导航任务
)

/**
 * 批量查询地图的 md5
 */
data class MockMapNamesReq(
  /**
   * 必须带文件后缀名
   */
  @JsonProperty("map_names")
  val mapNames: List<String>,
)

/**
 * 下载指定地图
 */
data class MockMapNameReq(

  /**
   * rbk 不需要文件后缀名，与 rbk 保持一致，不用后缀名
   */
  @JsonProperty("map_name")
  val mapName: String,
)

data class MoveTaskRuntime(val taskId: String, val step3066: Step3066, var status: MoveTaskStatus)

enum class MoveTaskStatus(val code: Int, val desc: String) {
  Init(0, "初始化"),
  Waiting(1, "等待中"), // 未模拟
  Running(2, "运行中"),
  Completed(4, "完成"),
  Failed(5, "失败"),
  Failing(55, "失败处理中"),
  Cancelled(6, "取消"),
  Cancelling(66, "取消处理中"),
  NotFound(404, "未找到"),
  ;

  fun isFinal(): Boolean = this in setOf(Completed, Failed, Cancelled)

  companion object {
    fun fromCode(code: Int): MoveTaskStatus = values().find { it.code == code } ?: Init
  }
}
enum class TaskType(val code: Int, val desc: String) {
  None(0, "没有导航"),
  FreeToAny(1, "自由导航到任意点"),
  FreeToPoint(2, "自由导航到站点"),
  PathToPoint(3, "路径导航到站点"),
  Translation(7, "平动转动"),
  Other(100, "其他"),
}

class TaskException(val kind: TaskExceptionKind, message: String, cause: Throwable? = null) :
  BzError(cause, "TaskException$kind", message)

enum class TaskExceptionKind {
  Failed,
  Cancelled,
}

data class GoodsRegion(
  val name: String = "",
  @JsonIgnore
  val oriPoints: List<Point2D>? = null, // 原始的容器点
  @JsonProperty("point")
  val points: List<Point2D>? = null, // 旋转偏移后的容器点
  @JsonIgnore
  val theta: Double, // 货物朝向，目前 rbk 还没加 [0, 2π]
  @JsonIgnore
  val centerOffset: Point2D = Point2D(0.0, 0.0), // 到机器人中心点的偏移
)

/**
 * 仿真 RBK 的容器
 *
 * 结构：
 * {
 *     "container_name": "1",
 *     "desc": "first good",
 *     "goods_id": "good1",
 *     "has_goods": true
 * }
 *
 * binIndex 的数字是 0，1，2，3 …… 999（货叉）
 */
data class MockRbkSelfBin(
  @JsonProperty("container_name")
  val binIndex: String = "",
  val desc: String = "",
  @JsonProperty("goods_id")
  val containerId: String = "",
  @JsonProperty("has_goods")
  val hasGoods: Boolean = false,
)

/**
 * 移动速度
 *
 * TODO 仿真目前暂未使用载货速度
 */
data class MoveConfig(
  val maxSpeed: Double?, // 空载最大前进速度
  val maxBackSpeed: Double?, // 空载最大后退速度
  val maxRotSpeed: Double?, // 空载最大旋转速度 单位为 度/秒
  val loadedMaxSpeed: Double?, // 载货最大前进速度
  val loadedMaxBackSpeed: Double?, // 载货最大后退速度
  val loadedMaxRotSpeed: Double?, // 载货最大旋转速度 单位为 度/秒
)

/**
 * 当前移动速度
 */
data class CurrentSpeed(
  val speed: Double = 0.0, // 直线速度，m/s
  val rotSpeed: Double = 0.0, // 旋转速度，rad/s
)