package com.seer.trick.fleet.service

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.*
import com.seer.trick.I18N.lo
import com.seer.trick.base.LifetimeStatus
import com.seer.trick.base.SysEmc
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.base.concurrent.BaseConcurrentCenter
import com.seer.trick.base.concurrent.PollingJobManager
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.failure.FailureLevel
import com.seer.trick.base.failure.FailureRecordReq
import com.seer.trick.base.failure.FailureRecorder
import com.seer.trick.fleet.FleetLogger
import com.seer.trick.fleet.adapter.RobotRbkAdapter
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.mock.service.MockService
import com.seer.trick.fleet.order.*
import com.seer.trick.fleet.order.OrderRuntime.Companion.timeCostOrZero
import com.seer.trick.fleet.order.OrderService.countFaultDuration
import com.seer.trick.fleet.order.OrderService.updateAndPersistOrder
import com.seer.trick.fleet.seer.SeerRbkTcpServer
import com.seer.trick.fleet.seer.SmapHelper
import com.seer.trick.fleet.stats.TrafficConditionsService
import com.seer.trick.helper.*
import com.seer.trick.robot.vendor.seer.rbk.RbkClient
import org.apache.commons.codec.digest.DigestUtils
import org.apache.commons.io.FileUtils
import org.apache.commons.lang3.StringUtils
import org.apache.commons.lang3.exception.ExceptionUtils
import org.slf4j.LoggerFactory
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.nio.charset.StandardCharsets
import java.util.*

/**
 * 机器人管理。东西很多。未来考虑拆分。目前包括：
 * 机器人配置管理、状态管理、库位管理、与 RBK 对应的功能、修改配置。
 *
 * 机器人更新配置，目前暂时采用 dispose 后重新 init 的方法。要求机器人更新配置时，机器人无运单。
 */
object RobotService {

  private val logger = LoggerFactory.getLogger(javaClass)

  const val NICKNAME = "M4QuickFleet"

  /**
   * 从配置文件中加载所有机器人。初始化 RobotRuntime。
   * 无阻塞操作。
   * 删除未配置机器人组的机器人并持久化！
   */
  fun init(sr: SceneRuntime) {
    sr.robots.clear()

    val robotConfigs: List<SceneRobot> = JsonFileHelper.readJsonFromFile(getRobotsFile(sr.sceneId)) ?: emptyList()

    val checkedConfigs = mutableListOf<SceneRobot>()
    for (rc in robotConfigs) {
      if (sr.listRobotGroups().none { it.id == rc.groupId }) {
        // 如果机器人的分组不存在，则删除此机器人，稍后持久化。
        FleetLogger.info(
          "InitRobot",
          "RemoveBadRobot",
          sr,
          rc.robotName,
          mapOf(
            "groupId" to rc.groupId,
            "msg" to "No related RobotGroup was found, remove this robot.",
          ),
        )
        continue
      }
      checkedConfigs.add(rc)
      initRobot(sr, rc)
    }

    if (checkedConfigs.size != robotConfigs.size) {
      // 存在被过滤掉的机器人，持久化校验后的机器人配置
      JsonFileHelper.writeJsonToFile(getRobotsFile(sr.sceneId), checkedConfigs, true)
    }
  }

  /**
   * 从机器人配置初始机器人。负责构造 RobotRuntime 并添加到机器人列表。初始化机器人。
   * 不耗时，暂时全部放到 orderLock 里。
   */
  private fun initRobot(sr: SceneRuntime, rc: SceneRobot) = sr.withOrderLock {
    val oldRr = sr.robots[rc.robotName]
    if (oldRr != null) {
      disposeRobot(sr, oldRr)
    }

    logger.info("Start | Init robot $rc")

    val rr = RobotRuntime(sr, rc)
    sr.robots[rc.robotName] = rr

    if (rr.disabled()) {
      // 直接销毁状态
      rr.ltStatus = LifetimeStatus.Disposed
      return@withOrderLock
    }

    // 机器人运单运行时
    restore(rr)

    initRbkClientIfNeed(rr)

    // 开始轮训机器人状态
    PollingJobManager.submit(
      threadName = getThreadName(rr),
      remark = "Fetching robot state: ${rr.robotName}",
      interval = {
        val config = rr.sr.config
        var interval = config.robotStateFetchDelay
        if (interval <= 200) interval = 200
        interval
      },
      logger = logger,
      workerMaxTime = 1500L,
      stopCondition = { sr.status == SceneStatus.Disposed || rr.ltStatus == LifetimeStatus.Disposed },
      exceptionContinue = true,
      tags = setOf(rr.sr.tag, rr.tag),
      worker = { fetchState(rr) },
    )

    StepSelectService.initRobot(rr)

    rr.ltStatus = LifetimeStatus.Initialized

    logger.info("Done | Init robot $rr")

    FleetEventService.fire(FleetEvent(name = "Robot::Init", sceneId = sr.sceneId, robotName = rr.robotName))
  }

  /**
   * 获取轮训机器人状态的 threadName
   */
  fun getThreadName(rr: RobotRuntime): String = "RobotState-${rr.sr.no}-${rr.robotName}"

  // 按需初始化 Rbk 客户端。可以被多次调用。
  // TODO 加什么锁合适
  private fun initRbkClientIfNeed(rr: RobotRuntime) = synchronized(rr) {
    val config = rr.config
    if (config.simulated || config.connectionType != RobotConnectionType.FleetToRobot) {
      return
    }

    val robotIp = config.robotIp
    if (robotIp.isNullOrBlank()) {
      logger.error("未配置机器人 $rr IP 地址")
      return
    }
    // 此步不耗时
    rr.rbkClient = RbkClient(rr.robotName, robotIp, config.robotPortStart ?: 19204)
  }

  /**
   * 重置/重连 Rbk 客户端。销毁重建。
   * TODO 加什么锁合适
   */
  fun resetRbkClient(rr: RobotRuntime) = synchronized(rr) {
    rr.rbkClient?.dispose()
    rr.rbkClient = null
    initRbkClientIfNeed(rr)
  }

  /**
   * 销毁一个场景下的所有机器人。
   */
  fun dispose(sr: SceneRuntime) = sr.withOrderLock {
    for (rr in sr.robots.values) disposeRobot(sr, rr)
    sr.robots.clear()
  }

  /**
   * 销毁单个机器人。不要报错！销毁后此实例不能再被使用。
   * 只能在场景销毁、机器人被删除、停用时调用。
   * 不负责从机器人列表中删除！
   */
  private fun disposeRobot(sr: SceneRuntime, rr: RobotRuntime) = sr.withOrderLock {
    // 销毁前先释放控制权
    asyncReleaseRobotMaster(rr)
    if (rr.ltStatus == LifetimeStatus.Disposed) return@withOrderLock
    rr.ltStatus = LifetimeStatus.Disposed

    val sec = rr.executingStep
    if (sec != null) StepExecuteService.cancelExecuting(sec, "Dispose robot")

    // TODO
    //  停用机器人后，没有同步重置机器人本体的载货状态；
    //  但是再次启用此机器人后，会给此机器人重新构造 RobotBins，这会导致双端数据不一致，并且目前只有在料箱车接单后，才会展示相关的告警信息。

    // 删除机器人相关的后台任务
    PollingJobManager.removeByTag(rr.tag)

    rr.rbkClient?.dispose()
    rr.rbkClient = null

    FleetEventService.fire(
      FleetEvent(name = "Robot::Dispose", sceneId = sr.sceneId, robotName = rr.robotName),
    )
  }

  /**
   * 从数据库中读取机器人处理运单的状态。如果没有记录，自动创建一个，方便用户使用。
   */
  private fun restore(rr: RobotRuntime) = rr.sr.withOrderLock {
    // 先重置库位
    resetBins(rr)

    val ev = EntityRwService.findOne("MrRobotRuntimeRecord", Cq.idEq(rr.robotName))
    if (ev == null) {
      // 确保有此对象
      EntityRwService.createOne(
        "MrRobotRuntimeRecord",
        mutableMapOf(
          "id" to rr.robotName,
          "rtBins" to JsonHelper.writeValueAsString(rr.bins),
        ),
      )
      return@withOrderLock
    }

    // selectSameOrderId
    rr.selectSameOrderId = ev["selectSameOrderId"] as String?

    // 是否接单
    rr.offDuty = BoolHelper.anyToBool(ev["offDuty"])

    // 运单列表
    val orderIdsStr = ev["rtOrders"] as String?
    if (!orderIdsStr.isNullOrBlank()) {
      val orderIds = orderIdsStr.split(",")
      for (orderId in orderIds) {
        val order = rr.sr.orders[orderId]
        if (order == null) continue // 一般是自动单，自动单不会被恢复
        rr.orders[orderId] = order
      }
    }

    // 库位状态
    val binsStr = ev["rtBins"] as String?
    if (!binsStr.isNullOrBlank()) {
      // 可能出现持久化内容与库位数不一致的情况
      val bins: List<RobotBin> = JsonHelper.mapper.readValue(binsStr, jacksonTypeRef())
      // 如果之前库位多，直接丢弃
      for (binIndex in 0 until minOf(bins.size, rr.bins.size)) {
        rr.bins[binIndex] = bins[binIndex]
      }
    }

    // 占用库位但运单不在了
    for (bin in rr.bins) {
      if (bin.status == RobotBinStatus.Reserved && !bin.orderId.isNullOrBlank()) {
        if (!rr.orders.contains(bin.orderId)) {
          logger.error("Reserved bin ${bin.index} of robot $rr has no order ${bin.orderId}")
          rr.bins[bin.index] = RobotBin(bin.index)
        }
      }
    }
  }

  /**
   * 替换全部机器人配置。
   */
  fun replaceAllRobots(sr: SceneRuntime, robots: List<SceneRobot>) {
    sr.withOrderLock {
      // 先销毁之前的
      dispose(sr)

      persistConfigs(sr.sceneId, robots)
    }

    // 重新初始化整个场景的
    init(sr)
  }

  /**
   * 重连
   * TODO 要不要主动清理 selfReport
   */
  fun reconnect(rr: RobotRuntime, reason: String) {
    FleetLogger.info(
      module = "Robot",
      subject = "Reconnect",
      sr = rr.sr,
      robotName = rr.robotName,
      mapOf(
        "reason" to reason,
        "autoConnectCounter" to rr.fetchFailureToAutoReconnectCounter,
      ),
    )

    when (rr.config.connectionType) {
      RobotConnectionType.FleetToRobot -> resetRbkClient(rr)
      else -> SeerRbkTcpServer.resetRobot(rr.robotName, reason)
    }
  }

  /**
   * 添加一个机器人
   */
  fun create(sr: SceneRuntime, robotConfig: SceneRobot) {
    sr.withOrderLock {
      if (sr.robots.contains(robotConfig.robotName)) throw BzError("errRobotNameDuplicated", robotConfig.robotName)
      if (!sr.robotGroups.containsKey(robotConfig.groupId)) throw BzError("errRobotBadGroup", robotConfig.robotName)

      ensureIpv4IfFleetToRobot(sr, robotConfig)

      val robotConfigs = sr.robots.values.map { it.config } + robotConfig
      persistConfigs(sr.sceneId, robotConfigs)
    }

    logger.info("Create robot $robotConfig of scene $sr")

    initRobot(sr, robotConfig)

    if (robotConfig.simulated &&
      !MockService.config.robots.map { it.name }.contains(robotConfig.robotName) &&
      SceneAreaService.listMapsByRobotGroupId(sr, robotConfig.groupId).isNotEmpty()
    ) {
      // 初始化仿真机器人，只初始化有地图的
      MockService.initSceneMockRobots(sr, listOf(robotConfig.robotName))
    }
  }

  /**
   * 校验机器人的 ip 地址是否重复，格式是否正确。
   */
  private fun ensureIpv4IfFleetToRobot(sr: SceneRuntime, robotConfig: SceneRobot) {
    // 机器人的连接方式不是 FleetToRobot 时，机器人的 IP 地址可忽略，此时也不校验，因为：
    //  需要将连接方式修改会 FleetToRobot 之后，IP 地址才有效，会被再次校验。
    if (robotConfig.connectionType != RobotConnectionType.FleetToRobot) return

    // 调度连机器人，必须给机器人配置有效的 IP 地址。
    val robotIp = robotConfig.robotIp
    if (robotIp.isNullOrBlank()) {
      throw BzError("errRobotIpMissing", robotConfig.robotName, robotIp)
    }

    // 调度连机器人时（FleetToRobot），同一个场景中，机器人的 IP 地址必须是唯一的
    val ipConflictRobots = sr.robots.values.filter {
      !it.config.robotIp.isNullOrBlank() &&
        // ip 地址非空
        it.config.robotIp == robotConfig.robotIp &&
        // ip 地址相同
        it.robotName != robotConfig.robotName // 更新机器人配置时，得排除正在被修改的机器人。
    }.map { it.robotName }
    if (ipConflictRobots.isNotEmpty()) {
      throw BzError("errRobotIpConflict", robotConfig.robotName, robotConfig.robotIp, ipConflictRobots)
    }
  }

  /**
   * 删除一些机器人
   */
  fun remove(sr: SceneRuntime, robotNames: List<String>) {
    logger.info("Remove robots $robotNames of scene $sr")
    val robots = robotNames.mapNotNull { sr.robots[it] }
    for (rr in robots) {
      // 先不接单
      rr.offDuty = true
      // 重置机器人
      try {
        resetRobot(sr, rr)
      } catch (e: BzError) {
        if (e.code == "errRbkReqResBadCode") {
          // resetRobot() 会清空机器人的载货信息，但是会由于缺少机器人控制权等原因导致故障。
          // TODO 直接抛出此异常会导致前端的交互体验不好，暂时先忽略这个报错吧。
          logger.error("remove robot=${rr.robotName} but request rbk failed .", e)
        } else {
          throw e
        }
      }
    }

    sr.withOrderLock {
      val robotConfigs = sr.robots.values.map { it.config }.toMutableList()
      // TODO：直接删除机器人可能会有不良的影响，例如运单故障。后期优化。
      robotConfigs.removeAll { robotNames.contains(it.robotName) }
      persistConfigs(sr.sceneId, robotConfigs)
    }

    for (robotName in robotNames) {
      val rr = sr.robots[robotName] ?: continue
      disposeRobot(sr, rr)
      sr.robots.remove(robotName)
    }

    // 删除仿真
    MockService.removeMockByName(robotNames)
  }

  /**
   * 更新一个机器人配置。目前采用销毁重建的方式。
   */
  fun updateRobotConfig(sr: SceneRuntime, req: SceneRobot) {
    val robotGroup = sr.robotGroups[req.groupId] ?: throw BzError("errRobotBadGroup", req.robotName)
    // 机器人组已停用了，应该不能再编辑组内的机器人了？
    if (robotGroup.disabled) throw BzError("errRobotGroupDisabled", robotGroup.name)

    ensureIpv4IfFleetToRobot(sr, req)

    val rr = sr.mustGetRobot(req.robotName)

    // 持久化配置
    sr.withOrderLock {
      // 仅当机器人空闲时可以执行，否则报错
      throwIfRobotWithOrders(rr)

      val robotConfigs = sr.robots.values.map {
        if (it.config.robotName == req.robotName) {
          req
        } else {
          it.config
        }
      }
      persistConfigs(sr.sceneId, robotConfigs)
      logger.info("Update robot config $req of scene $sr")
    }
    // 异步释放控制权
    if (req.disabled) asyncReleaseRobotMaster(rr)

    // 包在 withOrderLock 里，防止此时被派新单
    val oldRr = sr.robots[req.robotName]
    if (oldRr != null) disposeRobot(sr, oldRr)
    sr.robots.remove(req.robotName)

    initRobot(sr, req)

    // 删除或新建仿真机器人
    MockService.initOrRemoveMockByRobotState(sr)
  }

  /**
   * 将一个场景下的所有机器人的仿真属性更新。
   * TODO 自动创建仿真机器人
   */
  fun changeAllRobotsSimulated(sr: SceneRuntime, simulated: Boolean) {
    sr.withOrderLock {
      sr.throwIfSceneWithOrders()

      val robotConfigs = sr.robots.values.map { rr ->
        val config = rr.config.copy(simulated = simulated)
        config
      }
      // 持久化
      persistConfigs(sr.sceneId, robotConfigs)
      logger.info("Change simulated of all robots for $sr")

      dispose(sr)
    }

    init(sr)
  }

  /**
   * 启用停用机器人。
   */
  fun updateDisabled(sr: SceneRuntime, robotNames: List<String>, disabled: Boolean) {
    logger.info("Update robot disabled: $robotNames -> $disabled")
    val robots = robotNames.mapNotNull { sr.robots[it] }

    // 先取消所有执行、运单，重置机器人、释放控制权
    if (disabled) {
      for (rr in robots) {
        rr.offDuty = true
        resetRobot(sr, rr)
        asyncReleaseRobotMaster(rr)
      }
    }

    sr.withOrderLock {
      val robotConfigs = sr.robots.values.map {
        if (robotNames.contains(it.robotName)) {
          it.config.copy(disabled = disabled)
        } else {
          it.config
        }
      }
      persistConfigs(sr.sceneId, robotConfigs)
    }

    for (robotName in robotNames) {
      val rr = sr.robots[robotName] ?: continue
      disposeRobot(sr, rr)
      initRobot(sr, rr.config.copy(disabled = disabled))
    }
  }

  /**
   * 启用停用整组机器人。采用销毁重建的方式。
   */
  fun updateDisabledByGroup(sr: SceneRuntime, groupId: Int, disabled: Boolean) {
    val robotNames = sr.robots.values.filter { it.config.groupId == groupId }.map { it.robotName }
    updateDisabled(sr, robotNames, disabled)
  }

  private val fetchReqBody = JsonHelper.mapper.writeValueAsString(mapOf("return_laser" to false))

  /**
   * 外部主动强制获取一次机器人状态，本次必須等待获取到结果，要么获取到要么获取不到
   * 获取到之后就是机器人的真实位置，获取不到即机器人离线，等机器人重新连接上之后重走上线的逻辑
   */
  fun fetchStateNow(rr: RobotRuntime) {
    fetchState(rr, true)
  }

  /**
   * 读取一次机器人状态。
   * 只获取状态、更新状态，不要干别的！！
   * 不抛异常
   */
  private fun fetchState(rr: RobotRuntime, forceFetch: Boolean = false) {
    if (rr.config.connectionType == RobotConnectionType.GwWs) return

    // 机器人停用、销毁、正在获取且 forceFetch 为 false 的时候提前返回
    if (rr.disabled() || rr.ltStatus != LifetimeStatus.Initialized || (rr.fetching && !forceFetch)) return

    // 重连次数过多，暂停自动重连，需要界面上人工重连
    if (rr.fetchFailureToAutoReconnectCounter > rr.sr.config.robotStateErrorToAutoReconnectMax) return

    rr.fetching = true
    rr.fetchCounter = (rr.fetchCounter + 1) % 3600 // 读取此时

    val selfReport = try {
      // tryReloadRobotModel(rr)
      doFetchState(rr)
    } catch (e: Throwable) {
      rr.lastFetchError = e
      if (e is IOException) {
        logger.warn("Fetch state of robot ${rr.robotName} failed: ${e.getTypeMessage()}")
      } else if (e is BzError) {
        logger.error("Fetch state of robot ${rr.robotName} failed: ${e.getTypeMessage()}")
      } else {
        logger.error("Fetch state of robot ${rr.robotName} failed: ", e)
      }
      RobotSelfReport(true, e.getTypeMessage())
    } finally {
      rr.fetching = false
    }

    updateSelfReport(rr, selfReport)

    if (!selfReport.error) {
      rr.fetchFailureNum = 0
      rr.fetchFailureToAutoReconnectCounter = 0

      // 获取机器人参数配置，第一次请求 1400 一次，后面每 100 次 fetchState 请求一次
      if (rr.fetchCounter % 100 == 0) fetchRobotConfig(rr)
    } else {
      // 连续读取失败次数
      rr.fetchFailureNum++
      if (rr.fetchFailureNum >= rr.sr.config.robotStateFailureNumToAutoConnect) {
        // 自动重连
        rr.fetchFailureNum = 0
        // 自动重连次数
        rr.fetchFailureToAutoReconnectCounter++
        if (rr.fetchFailureToAutoReconnectCounter > rr.sr.config.robotStateErrorToAutoReconnectMax) {
          FleetLogger.error(
            module = "Robot",
            subject = "StopAutoReconnect",
            sr = rr.sr,
            robotName = rr.robotName,
            msg = mapOf("max" to rr.sr.config.robotStateErrorToAutoReconnectMax),
          )
        } else {
          Thread.sleep(1000)
          reconnect(rr, "Failed too many times")
        }
      }
    }
  }

  /**
   * 获取机器人参数配置
   */
  private fun fetchRobotConfig(rr: RobotRuntime) {
    try {
      val res = RobotRbkAdapter.queryRobotMoveConfig(rr)
      val moveConfigNode = JsonHelper.mapper.readTree(res)
      val moveFactory = moveConfigNode.get("MoveFactory")
      rr.selfConfig = RobotSelfConfig(
        maxSpeed = moveFactory?.get("MaxSpeed")?.get("value")?.asDouble(),
        maxBackSpeed = moveFactory?.get("MaxBackSpeed")?.get("value")?.asDouble(),
        maxRotSpeed = moveFactory?.get("MaxRot")?.get("value")?.asDouble(),
        loadedMaxSpeed = moveFactory?.get("Load_MaxSpeed")?.get("value")?.asDouble(),
        loadedMaxBackSpeed = moveFactory?.get("Load_MaxBackSpeed")?.get("value")?.asDouble(),
        loadedMaxRotSpeed = moveFactory?.get("Load_MaxRot")?.get("value")?.asDouble(),
      )
    } catch (e: Exception) {
      logger.error("Failed to fetch robot config", e)
    }
  }

  /**
   * 根据连接方式实际读取机器人状态。
   * 只能被 fetchState 方法调用！
   * 不捕获任何异常，在外层捕获。异常除了通讯，还可能在解析过程中出现。
   */
  private fun doFetchState(rr: RobotRuntime): RobotSelfReport {
    val resStr = RobotRbkAdapter.request(rr, 1100, fetchReqBody, 5 * 1000)

    val ev: EntityValue = if (resStr.isBlank()) {
      HashMap(0)
    } else {
      JsonHelper.mapper.readValue(resStr, jacksonTypeRef())
    }

    val main = RobotHelper.rawToMain(RobotVendor.Seer, ev, rr)

    val stand = MapService.estimateStand(rr, main)

    return RobotSelfReport(
      false,
      null,
      main = main,
      stand = stand,
      rawReport = ev,
    )
  }

  /**
   * 更新机器人的自身报告。此方法只能被两个地方调用：
   * com.seer.trick.fleet.mars.MarsReportApplyManager.onReportApply
   * com.seer.trick.fleet.service.RobotService.fetchState
   */
  fun updateSelfReport(rr: RobotRuntime, selfReport: RobotSelfReport) {
    val sr = rr.sr
    val previousReport = rr.selfReport

    // 及时更新！
    rr.selfReport = selfReport

    if (!selfReport.error) {
      val main = selfReport.main
      if (main != null) {
        // 最新一次记录的充电时间
        if (main.charging == true) rr.reportingChargingOn = Date()

        rr.expectedOccupiedPointName = rr.selfReport?.stand?.pointName
      }
    }

    FleetEventService.fire(
      FleetEvent(name = "Robot::SelfReport", sceneId = sr.sceneId, robotName = rr.robotName),
    )

    // 记录路况
    TrafficConditionsService.onReport(rr)

    if ((previousReport == null || previousReport.error) && !selfReport.error) {
      // 机器人上线
      FleetLogger.info(
        module = "Robot",
        subject = "RobotOnline",
        sr = sr,
        robotName = rr.robotName,
        msg = mapOf(),
      )

      FleetEventService.fire(
        FleetEvent(name = "Robot::Online", sceneId = sr.sceneId, robotName = rr.robotName),
      )
    } else if (previousReport != null && !previousReport.error && selfReport.error) {
      // 机器人掉线
      FleetLogger.info(
        module = "Robot",
        subject = "RobotOffline",
        sr = sr,
        robotName = rr.robotName,
        msg = mapOf(),
      )

      FleetEventService.fire(
        FleetEvent(name = "Robot::Offline", sceneId = sr.sceneId, robotName = rr.robotName),
      )
    }

    // 如果系统没有配置了 N+1 但实际读到的有，允许这种情况，不需要告警
    RobotBinService.checkReportBinNum(rr)

    tryToSetRobotMater(rr)

    recordRobotFailures(rr)

    tryToRecoverByEmcReleased(rr, previousReport?.main, selfReport.main)
  }

  /**
   * 如果机器人没有获取控制权，并且当前是接单，未急停状态，系统未急停时，尝试获取控制权，不抛中断以外的异常
   */
  private fun tryToSetRobotMater(rr: RobotRuntime) {
    if (!rr.sr.isEnabledInitializedNotEmc()) return

    val main = rr.selfReport?.main ?: return // 离线不考虑

    if (rr.disabled() ||
      rr.ltStatus != LifetimeStatus.Initialized ||
      rr.offDuty ||
      isEmcOrSoftEmc(rr) ||
      main.controlledByFleet ||
      main.isControlled == true
    ) {
      return
    }

    try {
      RobotRbkAdapter.setRobotMaster(rr)
    } catch (e: Exception) {
      FleetLogger.error(
        module = "Robot",
        subject = "SetRobotMaster",
        sr = rr.sr,
        robotName = rr.robotName,
        msg = mapOf("error" to (e.message ?: e.getTypeMessage())),
      )
    }
  }

  /**
   * 记录与机器人相关的故障信息
   * 1. 机器人自身上报的告警信息：rr.selfReport?.main?.alarms
   * 2. 调用 RBK API 时，机器人反馈的告警信息：RobotRbkAdapter.requestWithCodeCheck()
   *    TODO: 但是这项就不是在这里记录了。
   * 3. 调度判定的机器人的异常状态，例如：离线、综合受控状态等。
   *    TODO: 调度判定的部分机器人异常状态，也受机器人自身的告警信息影响，待细化。
   */
  private fun recordRobotFailures(rr: RobotRuntime) {
    // 机器人离线了
    if (!isOnline(rr)) {
      addRobotFailureRecord(FailureLevel.Error, rr.robotName, "Robot offline")
    }

    // 记录机器人上报的故障信息
    recordRobotAlarms(rr)
  }

  /**
   * 记录机器人上报的告警信息
   */
  private fun recordRobotAlarms(rr: RobotRuntime) {
    val alarms = rr.selfReport?.main?.alarms ?: return
    val errAlarms = alarms.filter { it.level != RobotAlarmLevel.Info }
    if (errAlarms.isEmpty()) return
    val timestampRegex = """\[\d{4}-\d{2}-\d{2}T[^]]*]""".toRegex()
    for (a in errAlarms) {
      val level = when (a.level) {
        RobotAlarmLevel.Fatal -> FailureLevel.Fatal
        RobotAlarmLevel.Error -> FailureLevel.Error
        RobotAlarmLevel.Warning -> FailureLevel.Warning
        else -> continue
      }
      val desc = "[${a.code}] ${a.message.replaceFirst(timestampRegex, "")}"
      addRobotFailureRecord(level, rr.robotName, desc, "RobotSelf")
    }
  }

  private fun addRobotFailureRecord(level: FailureLevel, robotName: String, desc: String, source: String = "Fleet") {
    val recordReq = FailureRecordReq(
      kind = "Robot",
      subKind = "RobotAlarm",
      source = source,
      level = level,
      part = robotName,
      desc = desc,
    )
    FailureRecorder.addAsync(recordReq)
  }

  /**
   * 急停释放后尝试恢复故障的机器人
   */
  private fun tryToRecoverByEmcReleased(
    rr: RobotRuntime,
    previousMain: RobotSelfReportMain?,
    main: RobotSelfReportMain?,
  ) {
    if (!(previousMain != null && main != null && previousMain.emergency == true && main.emergency != true)) return
    if (rr.orders.values.none { it.order.fault }) return

    // 不要理解执行
    BaseConcurrentCenter.highTimeSensitiveExecutor.submit {
      Thread.sleep(3000)

      val faultOr = rr.orders.values.find { it.order.fault } ?: return@submit
      logger.info("Try to recover robot $rr for EMC released, fault order=$faultOr")
      OrderFaultService.retryByRobot(rr)
    }
  }

  /**
   * 返回所有机器人的前端信息汇总
   */
  fun listRobotUiReports(sr: SceneRuntime, withRawReport: Boolean = false): Map<String, RobotUiReport> {
    val robots = sr.listRobots()

    return robots.associate {
      val rr = it
      val group = rr.mustGetGroup()

      Pair(
        it.robotName,
        RobotUiReport(
          robotName = it.robotName,
          offDuty = rr.offDuty,
          config = rr.config,
          groupId = rr.config.groupId,
          groupName = group.name,
          online = isOnline(rr),
          selfReport = rr.selfReport?.let { r ->
            r.copy(rawReport = if (withRawReport) r.rawReport else null)
          },
          lastFetchError = rr.lastFetchError?.let { e -> ExceptionUtils.getStackTrace(e) },
          stopAutoConnect = rr.fetchFailureToAutoReconnectCounter > rr.sr.config.robotStateErrorToAutoReconnectMax,
          collisionModel = getCollisionModel(rr),
          orderRecord = toOrderUi(rr),
          isMaster = rr.selfReport?.main?.controlledByFleet == true, // TODO 去掉
          usingLiftId = rr.usingLiftId,
        ),
      )
    }
  }

  /**
   * 组装机器人运单方面的信息，给前端
   */
  fun toOrderUi(rr: RobotRuntime): RobotOrderUiReport {
    val tm = rr.sr.trafficService.showTrafficResourceMessage(rr.robotName)

    val faultMsg = StringUtils.firstNonBlank(
      rr.executingStep?.faultMsg,
      rr.orders.values.find { it.order.fault }?.order?.faultReason,
    )
    return RobotOrderUiReport(
      offDuty = rr.offDuty,
      cmdStatus = rr.getExecuteStatus(),
      faultMsg = faultMsg,
      orders = rr.orders.values.map { it.id },
      withBzOrder = rr.orders.values.any { it.order.kind == OrderKind.Business },
      autoOrder = rr.autoOrder?.orderId,
      bins = rr.bins,
      idleFrom = rr.idleFrom,
      chargingOrderDoneOn = rr.chargingOrderDoneOn,
      reportingChargingOn = rr.reportingChargingOn,
      executingSelected = rr.executingStep?.digest(),
      orderReject = rr.orderReject,
      stepReject = rr.stepReject,
      trafficReady = rr.trafficReady,
      pendingTrafficTask = rr.pendingTrafficTask,
      recentReq3066s = rr.recentNavTasks.toList(),
      robotBlockedBy = tm?.blockedMessage?.robotBlocked,
      containerBlockedBy = tm?.blockedMessage?.containerBlocked,
      unTravelPointNames = tm?.pathResource?.unTravelPointNames,
      travelledPointNames = tm?.pathResource?.travelledPointNames,
      spaceResources = tm?.spaceResources,
      moveActions = rr.moveActions.toList(),
      extra = rr.sr.trafficService.robotDebugData(rr.robotName),
    )
  }

  // 只负责持久化
  private fun persistConfigs(sceneId: String, configs: List<SceneRobot>) {
    JsonFileHelper.writeJsonToFile(getRobotsFile(sceneId), configs, true)
  }

  private fun getRobotsFile(sceneId: String): File {
    val dir = SceneFileService.getSceneDir(sceneId)
    return File(dir, "robots.json")
  }

  /**
   * 持久化 rr 到数据库
   */
  fun persistRobotRuntime(rr: RobotRuntime) {
    val ev = robotRuntimeToEv(rr)
    val id = EntityHelper.mustGetId(ev)
    if (null == EntityRwService.findOne("MrRobotRuntimeRecord", Cq.idEq(id))) {
      EntityRwService.createOne("MrRobotRuntimeRecord", ev)
    } else {
      EntityRwService.updateOne("MrRobotRuntimeRecord", Cq.idEq(id), ev)
    }
  }

  private fun robotRuntimeToEv(rr: RobotRuntime): EntityValue = mutableMapOf(
    "id" to rr.robotName,
    "offDuty" to rr.offDuty,
    "rtOrders" to rr.orders.keys.joinToString(","),
    "rtBins" to JsonHelper.writeValueAsString(rr.bins),
  )

  /**
   * 重置所有库位。清空。根据机器人最新配置，初始化所有库位状态为空。
   * 在运单锁里执行。
   */
  private fun resetBins(rr: RobotRuntime) = rr.sr.withOrderLock {
    rr.bins.clear()
    for (i in 1..rr.config.selfBinNum) {
      rr.bins.add(RobotBin(i - 1))
    }
  }

  /**
   * 机器人是否在线，还要看最后一次获取状态的时间点
   */
  fun isOnline(rr: RobotRuntime): Boolean {
    val report = rr.selfReport
    if (report == null || report.error) return false
    val reportOn = report.timestamp
    return (System.currentTimeMillis() - reportOn.time) <= 6 * 1000
  }

  // 下载机器人模型文件 1500
  // 列出机器人所有的地图 1300
  // 查指定地图的 md5 值 1302
  // 下载指定地图 4011
  // 上传地图 4010
  // 推送模型文件 4200
  // 删除机器人地图 4012

  /**
   * 读取此机器人的模型文件，将其存储到场景下。文件名随机命名，扩展名 .model。
   *
   * RBK 指令 1500
   * https://seer-group.feishu.cn/wiki/Ld9rwbVciilVbRkN42UcE6p1npe
   */
  fun loadSaveRbkRobotModelFile(rr: RobotRuntime): RobotModelRecord {
    val sr = rr.sr
    val dir = SceneFileService.getSceneRobotModelsDir(sr.sceneId)

    // 1500 故意用 request
    val rbkModelStr = RobotRbkAdapter.request(rr, 1500, "")
    val rbkModel: RbkModel = JsonHelper.mapper.readValue(rbkModelStr, jacksonTypeRef())

    val targetFileName = "${IdHelper.uuidStr()}.model"
    val targetFile = File(dir, targetFileName)
    JsonFileHelper.writeJsonToFile(targetFile, rbkModel)

    val md5 = FileInputStream(targetFile).use { DigestUtils.md5Hex(it) }

    // val record = RobotModelRecord(FileManager.fileToPath(targetFile), md5)
    // updateConfig(config, group.copy(robotModel = record))
    // return record
    return RobotModelRecord(SceneFileService.fileToPath(sr.sceneId, targetFile), md5)
  }

  /**
   * 列出此机器人的所有地图。地图名和 MD5。
   *
   * RBK 指令 1300
   * 查机器人所有的地图
   * https://seer-group.feishu.cn/wiki/Eu5pwBAnrihjWYkE9kLc6FPln0b
   *
   * RBK 指令 1302
   * 查地图的 MD5 值
   * https://seer-group.feishu.cn/wiki/ITvMwXIqZipLsIkpwGBcFhUvnce
   */
  fun listRobotMaps(rr: RobotRuntime): List<RobotMapNameMd5> {
    // 拿机器人的所有的地图
    val robotMapsStr = RobotRbkAdapter.requestWithCodeCheck(rr, 1300, "")
    val robotMaps: RbkAllMaps = JsonHelper.mapper.readValue(robotMapsStr, jacksonTypeRef())
    // 1302 请求参数中的文件名必须带有后缀名，而 robotMaps.maps 是不带后缀名的，需要从 mapFilesInfo 中取
    val maps = robotMaps.mapFilesInfo.map { it.name }
    // 查所有地图的 md5
    val mapMd5Str = RobotRbkAdapter.requestWithCodeCheck(
      rr,
      1302,
      JsonHelper.mapper.writeValueAsString(mapOf("map_names" to maps)),
    )
    val mapMd5: RbkMapMd5 = JsonHelper.mapper.readValue(mapMd5Str, jacksonTypeRef())
    val r = mapMd5.mapInfo.map {
      RobotMapNameMd5(it.name, it.md5, robotMaps.currentMapMd5 == it.md5)
    }
    return r
  }

  /**
   * 读取此机器人的指定地图，将其存储到场景下。文件名随机命名，扩展名 .smap。
   *
   * RBK 指令 4011
   * https://seer-group.feishu.cn/wiki/MQSKwwKIti5Yl4k6LyLcSkZbnXg
   */
  fun loadSaveRobotMapFile(
    rr: RobotRuntime,
    mapName: String,
    check: Boolean = false,
  ): Pair<Boolean, RobotAreaMapRecord?> {
    val sr = rr.sr
    val dir = SceneFileService.getSceneRobotMapsDir(sr.sceneId)
    // 4011 不能传后缀名，所以去除文件后缀名
    val lastDotIndex = mapName.lastIndexOf('.')
    val rmExtensionMapName = if (lastDotIndex > 0) {
      mapName.substring(0, lastDotIndex)
    } else {
      mapName
    }
    val reqStr = JsonHelper.mapper.writeValueAsString(mapOf("map_name" to rmExtensionMapName))
    val resStr = RobotRbkAdapter.requestWithCodeCheck(rr, 4011, reqStr)

    // 检查地图的路径类型
    if (check) {
      val smap = SmapHelper.strToSmap(resStr)
      smap.advancedCurveList?.let {
        for (path in it) {
          if (path.className == "ArcPath") return Pair(false, null)
        }
      }
    }

    val targetFileName = "${IdHelper.uuidStr()}.smap"
    val targetFile = File(dir, targetFileName)
    FileUtils.write(targetFile, resStr, Charsets.UTF_8)

    val md5 = DigestUtils.md5Hex(resStr)

    return Pair(true, RobotAreaMapRecord(mapName, SceneFileService.fileToPath(rr.sr.sceneId, targetFile), md5))
  }

  /**
   * 读取此机器人的所有地图，将其存储到场景下。文件名随机命名，扩展名 .smap。
   *
   * RBK 指令 1300
   * 查机器人所有的地图
   * https://seer-group.feishu.cn/wiki/Eu5pwBAnrihjWYkE9kLc6FPln0b
   *
   * RBK 指令 4011
   * 下载地图
   * https://seer-group.feishu.cn/wiki/MQSKwwKIti5Yl4k6LyLcSkZbnXg
   */
  fun loadSaveRobotMapFiles(rr: RobotRuntime): List<RobotAreaMapRecord> {
    SceneFileService.getSceneRobotMapsDir(rr.sr.sceneId)

    // 拿机器人的所有的地图
    val robotMapsStr = RobotRbkAdapter.requestWithCodeCheck(rr, 1300, "")
    val robotMaps: RbkAllMaps = JsonHelper.mapper.readValue(robotMapsStr, jacksonTypeRef())

    val r = mutableListOf<RobotAreaMapRecord>()
    for (mapName in robotMaps.maps) {
      val (_, record) = loadSaveRobotMapFile(rr, mapName)
      r.add(record!!)
    }

    return r
  }

  /**
   * 上传地图到机器人
   *
   * RBK 指令 4010
   * https://seer-group.feishu.cn/wiki/QN8nw6YebiF41kk72bocW6hunbh
   * TODO 全更新上去，还是根据 md5/地图名比对，只传不同的 控制权
   * TODO rbk 上原本有，调度中没有的地图，要不要在 rbk 中删掉
   */
  fun pushMapToRobot(rr: RobotRuntime) {
    val maps = SceneAreaService.listMapsByRobotGroupId(rr.sr, rr.config.groupId)
    for (map in maps) {
      // 地图完全相同则不推送了
      if (map.mapName == rr.selfReport?.main?.currentMap && map.mapMd5 == rr.selfReport?.main?.currentMapMd5) continue
      val file = SceneFileService.pathToFile(rr.sr.sceneId, map.mapFile)
      if (!file.exists()) continue
      // 只读取不做任何格式化，以防 MD5 发生变化
      val reqStr = FileUtils.readFileToString(file, StandardCharsets.UTF_8)
      // 推送地图需要控制权因此先请求控制权
      RobotRbkAdapter.requestControl(rr)

      // 如果机器人只存在于当前场景的一个区域时，用 2025，推送地图，并将其切换为机器人正在使用的地图。
      if (maps.size == 1) {
        RobotRbkAdapter.requestWithCodeCheck(rr, 2025, reqStr, 30000)
        return // 此时，return 和 continue 的效果是一样的。
      }

      // TODO 需要推送多个地图给机器人时，何时应该让机器人切换地图？

      // 推送地图的名称如果是车正在使用的地图须使用 2025 或者用 4010 + 2022
      if (rr.selfReport?.main?.currentMap == map.mapName) {
        // 推送并做切换比较耗时，需要单独设置超时时间 30s
        RobotRbkAdapter.requestWithCodeCheck(rr, 2025, reqStr, 30000)
      } else {
        RobotRbkAdapter.requestWithCodeCheck(rr, 4010, reqStr)
      }
    }
    // TODO 处理上传失败
  }

  /**
   * 获取机器人的碰撞模型。从机器人组中取。
   * 采用机器人坐标系。
   */
  fun getCollisionModel(rr: RobotRuntime): Polygon {
    val group = rr.mustGetGroup()
    // 之前步骤保证这里非空
    val groupBound = group.collisionModel.bound
    return groupBound
  }

  /**
   * 实际的碰撞模型：载货时是容器的模型，非载货时是机器人组的碰撞模型
   */
  fun getActualCollisionModel(rr: RobotRuntime): Polygon {
    val filledBin = rr.bins.find { it.status == RobotBinStatus.Filled || it.status == RobotBinStatus.Cancelled }
    return if (filledBin == null) {
      getCollisionModel(rr)
    } else {
      val containerTypeName = rr.sr.orders[filledBin.orderId]?.order?.containerTypeName
      val polygon = rr.sr.containerTypes.values.find { it.name == containerTypeName }?.polygon
      // 找不到容器类型，则使用第一个容器类型？还是直接报错？
      polygon ?: rr.sr.containerTypes.values.first().polygon
    }
  }

  /**
   * 专门用来修改是否接单
   *
   * TODO 暂不校验机器人空闲：可能需要在机器人执行运单时，让其不接单（即不接后续的运单）
   */
  fun updateOffDuty(rr: RobotRuntime, offDuty: Boolean) {
    val sr = rr.sr

    val old = rr.offDuty
    FleetLogger.info(
      module = "RobotRuntime",
      subject = if (offDuty) "OffDuty" else "OnDuty",
      sr = sr,
      robotName = rr.robotName,
      msg = emptyMap(),
    )

    if (old && !offDuty) {
      FleetEventService.fire(
        FleetEvent(name = "Robot::OnDuty", sceneId = sr.sceneId, robotName = rr.robotName),
      )
    } else if (!old && offDuty) {
      FleetEventService.fire(
        FleetEvent(name = "Robot::OffDuty", sceneId = sr.sceneId, robotName = rr.robotName),
      )
    }
    sr.withOrderLock {
      rr.offDuty = offDuty
      persistRobotRuntime(rr)
    }
    // 不接单时释放控制权
    if (offDuty) asyncReleaseRobotMaster(rr)
  }

  /**
   * 根据运单当前步骤的目标点，更新机器人期望的点位
   */
  fun updateExpectedOccupiedPointByOrderCurrent(rr: RobotRuntime, or: OrderRuntime) {
    rr.expectedOccupiedPointName = or.getCurrentStep()?.let {
      rr.sr.mapCache.getPointCacheByGroupAndLoc(rr, it.location)?.point?.name
    }
  }

  /**
   * 添加最近三条路径导航任务
   */
  fun addRecentNavTasks(rr: RobotRuntime, record: NavTaskRuntime) {
    rr.recentNavTasks.add(record)
    if (rr.recentNavTasks.size > 3) {
      rr.recentNavTasks.poll() // 移除最旧的记录
    }
  }

  /**
   * 列出目前在指定区域的机器人。
   */
  fun listRobotsInArea(sr: SceneRuntime, areaId: Int): List<RobotRuntime> =
    sr.robots.values.filter { it.selfReport?.stand?.areaId == areaId }

  /**
   * 是否能给机器人发送控制指令，如 3066
   */
  fun isControlCommandAllowed(rr: RobotRuntime): Boolean {
    if (!isOnline(rr)) return false
    val main = rr.selfReport?.main ?: return false
    return main.controlledByFleetOrFree
  }

  /**
   * 机器人急停或软急停
   */
  fun isEmcOrSoftEmc(rr: RobotRuntime): Boolean =
    rr.selfReport?.main?.softEmc == true || rr.selfReport?.main?.emergency == true

  /**
   * 是否有路径导航正在执行
   */
  fun isNavTaskGoing(rr: RobotRuntime): Boolean {
    val raw = rr.selfReport?.rawReport ?: return false
    val taskType = raw["task_type"] as Int? ?: return false
    val taskStatus = raw["task_status"] as Int? ?: return false
    return taskType > 0 &&
      !(taskStatus == NavTaskResult.NT_COMPLETED || taskStatus == NavTaskResult.NT_CANCELLED)
  }

  fun updateTrafficRobot(rr: RobotRuntime) {
    // 检测机器人是否在线
    if (isOnline(rr)) {
      // 调用交管重置的接口
      if (rr.orders.isNotEmpty()) {
        logger.debug("${rr.robotName} on line and has orders")
        return
      }
      rr.sr.trafficService.resetByRobot(rr)
    } else {
      FleetEventService.fire(
        FleetEvent(name = "Robot::Dispose", sceneId = rr.sr.sceneId, robotName = rr.robotName),
      )
    }
  }

  /**
   * 如果机器人有运单，报错
   */
  private fun throwIfRobotWithOrders(rr: RobotRuntime) {
    if (rr.orders.isNotEmpty()) throw BzError("errRobotNotIdleCmdOrders", rr.robotName)
  }

  /**
   * 重置机器人运单相关。
   * 在运单锁里执行。
   */
  fun resetRobot(sr: SceneRuntime, rr: RobotRuntime) = sr.withOrderLock {
    val orderIds = rr.orders.keys

    FleetLogger.info(
      module = "Order",
      subject = "ResetByRobot",
      sr = rr.sr,
      robotName = rr.robotName,
      msg = mapOf(
        "cmdStatus" to rr.getExecuteStatus(),
        "executing" to rr.executingStep?.digest(),
        "orders" to orderIds,
        "bins" to rr.bins,
      ),
    )

    val sec = rr.executingStep
    if (sec != null) StepExecuteService.cancelExecuting(sec, "Reset robot")

    // 强制清理交管
    sr.trafficService.resetByRobot(rr)

    // 取消运单，不能调用 cancelOrder 方法，因为会重复处理机器人
    val doneOn = Date()
    for (or in rr.orders.values) {
      if (or.order.status == OrderStatus.Cancelled) continue

      // 更新 order 的状态为 Cancelled, 并计算 timeCost
      // TODO 标记好还是用统一的 cancelOrder 好
      val newOrder = or.order.copy(
        fault = false,
        faultReason = null,
        faultDuration = countFaultDuration(or),
        status = OrderStatus.Cancelled,
        doneOn = doneOn,
        processingTime = timeCostOrZero(doneOn, or.order.createdOn),
        executingTime = timeCostOrZero(doneOn, or.order.robotAllocatedOn),
      )
      updateAndPersistOrder(or, newOrder, "Reset robot, cancel order")
    }

    // 重置机器人运行时状态
    rr.autoOrder = null
    rr.chargingOrderDoneOn = null
    rr.currentMapNotMatchedError = false
    rr.executingStep = null
    rr.expectedOccupiedPointName = null
    rr.forcedCharging = false
    rr.fetchFailureNum = 0
    rr.fetchCounter = 0
    rr.fetchFailureToAutoReconnectCounter = 0
    rr.idleFrom = Date()
    rr.lastFetchError = null
    rr.navigating = false
    rr.orderReject = null
    rr.orders.clear()
    rr.pushingMap = false
    rr.reportingChargingOn = null
    rr.recentNavTasks.clear()
    rr.stepReject = null

    rr.selectSameOrderId = null
    rr.coolingFrom = null

    // 清理库位
    resetBins(rr)

    // 清空实车的背篓或载货状态。
    try {
      setRbkBinsEmpty(rr)
    } catch (e: Exception) {
      // setRbkBinsEmpty() 可能会由于没有机器人的控制权而报错，这里接一道，确保后续的逻辑能正常执行完。
      logger.error("robot=${rr.robotName}, set rbk bins empty failed. ", e)
    }

    // 根据标签清除相关告警
    AlarmService.removeByTag(rr.tag)

    persistRobotRuntime(rr)
  }

  /**
   * 重置机器人之后调用，将实车的所有背篓都置空，或者将实车置为未载货状态。
   * 高危操作，但是由用户自行规范和约束 “重置” 行为：重置机器人、重置所有机器人、删除机器人、禁用机器人。
   */
  fun setRbkBinsEmpty(rr: RobotRuntime) {
    if (!rr.config.simulated) {
      // 删除已停用的机器人也会调用此方法，由于已停用的机器人 client=null，但是会报错：No rbk client。
      // 不能用 rr.client == null 判断，否则会导致删除连接方式为 RobotToFleet 的在线状态的机器人时，不会掉调用此方法。
      if (rr.config.disabled) {
        logger.warn("do not set rbk bins empty cause robot=${rr.robotName} already disabled.")
        return
      }
      // 删除离线的机器人时，也会调用此方法，并触发 client 的重连机制，导致响应较慢，甚至超时。
      if (!isOnline(rr)) {
        logger.warn("do not set rbk bins empty cause robot=${rr.robotName} offline.")
        return
      }
      val selfBinNum = rr.config.selfBinNum
      if (selfBinNum == 1) {
        // 部分特殊情况下，拼单逻辑可能会在业务层处理，此时料箱车的 selfBinNum 也是 1。
        val rbkBinNum = rr.selfReport?.main?.bins?.size ?: 0
        if (rbkBinNum > 1) {
          // 清空所有料箱车的背篓
          RobotRbkAdapter.emptyAllRobotRbkBin(rr)
        } else {
          // 清空单负载机器人的载货状态
          RobotRbkAdapter.setRobotToUnloadedStatus(rr)
        }
      } else if (selfBinNum > 1) {
        RobotRbkAdapter.emptyAllRobotRbkBin(rr)
      } else {
        // 不可能是小于 1 的整数。
        logger.warn("try to empty robot=${rr.robotName} bins, but its selfBinNum=$selfBinNum, must greater than 1 ")
      }
    } else {
      // 清空仿真机器人储位不在这里处理
    }
  }

  /**
   * 触发或者清除机器人的软急停状态。
   *
   * @param enable false: 触发软急停状态；true: 清除软急停状态。
   */
  fun setSoftEmc(sr: SceneRuntime, rr: RobotRuntime, enable: Boolean) = sr.withOrderLock {
    RobotRbkAdapter.setSoftEmc(rr, enable)
  }

  /**
   * 异步释放机器人的控制权
   */
  private fun asyncReleaseRobotMaster(rr: RobotRuntime) {
    BaseConcurrentCenter.highTimeSensitiveExecutor.submit {
      try {
        // 若有控制权，则释放控制权
        if (rr.selfReport?.main?.controlledByFleet == true) {
          RobotRbkAdapter.unsetRobotMaster(rr)
        }
      } catch (e: Exception) {
        logger.error("Release robot ${rr.robotName} master fail: ${e.message}")
      }
    }
  }

  /**
   * 机器人是否能参与派单。TODO 跟诊断合并
   */
  fun isRobotOkToAcceptOrder(rr: RobotRuntime): Boolean {
    val main = rr.selfReport?.main ?: return false
    return !rr.disabled() &&
      rr.ltStatus == LifetimeStatus.Initialized &&
      !rr.offDuty &&
      isOnline(rr) &&
      main.emergency != true &&
      main.softEmc != true &&
      main.controlledByFleet &&
      // 不能有故障单
      rr.orders.keys.none { rr.sr.orders[it]?.order?.fault == true }
  }

  /**
   * 执行
   */
  fun isRobotOkToExecutingOrder(rr: RobotRuntime): Boolean {
    val main = rr.selfReport?.main
    return main != null &&
      rr.ltStatus == LifetimeStatus.Initialized &&
      isOnline(rr) &&
      main.emergency != true &&
      main.softEmc != true &&
      main.controlledByFleet &&
      rr.trafficReady &&
      !rr.isCooling() &&
      !SysEmc.isSysEmc()
  }

  /**
   * 如果机器人不适合执行任务和指令，等待。
   * 不考虑不接单。
   */
  fun suspendUntilRobotOkToExecuting(rr: RobotRuntime, where: String) {
    if (isRobotOkToExecutingOrder(rr)) return

    logger.warn("suspendIfRobotOrThrowIfDisabled, robot=$rr, at=$where")

    while (true) {
      if (isRobotOkToExecutingOrder(rr)) break

      Thread.sleep(1000)
    }
  }

  /**
   * 如果机器人或场景被停用，抛异常
   * TODO 机器人重新配置导致 Disposed 应该怎么处理
   */
  fun throwIfDisabledOrDisposed(rr: RobotRuntime) {
    if (rr.disabled() || rr.ltStatus == LifetimeStatus.Disposed) {
      throw OrderProcessingError(OpErrCode.RobotDisposed, lo("OpErrCodeRobotDisposed"))
    }
    if (rr.sr.status == SceneStatus.Disposed || rr.sr.status == SceneStatus.Disabled) {
      throw OrderProcessingError(OpErrCode.SceneDisposed, lo("OpErrCodeSceneDisposed"))
    }
  }

  fun alarmsToStr(rr: RobotRuntime): String = rr.selfReport?.main?.alarms?.joinToString("|") {
    "[${it.code}]${it.message}"
  } ?: lo("labelNone")
}