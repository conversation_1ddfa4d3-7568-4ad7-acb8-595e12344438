package com.seer.trick.fleet.traffic.distributed.lock

import com.seer.trick.fleet.traffic.distributed.block.BlockItem
import com.seer.trick.fleet.traffic.distributed.helper.LockHelper
import com.seer.trick.fleet.traffic.distributed.lock.domain.LockRequest
import com.seer.trick.fleet.traffic.distributed.lock.domain.LockResponse
import com.seer.trick.fleet.traffic.distributed.lock.graph.BoundingBox
import com.seer.trick.fleet.traffic.distributed.lock.graph.GraphType
import com.seer.trick.fleet.traffic.distributed.lock.model.SpaceLock
import com.seer.trick.fleet.traffic.distributed.lock.tree.QuadTree
import com.seer.trick.fleet.traffic.distributed.map.*
import com.seer.trick.fleet.traffic.distributed.service.DistributedTrafficService
import org.slf4j.LoggerFactory

/**
 * 锁闭服务
 * */
object LockService {

  private val logger = LoggerFactory.getLogger(javaClass)

  fun lockHandle(request: LockRequest): LockResponse {
    var spaces = request.spaces
    val collision = checkCollisions(spaces, request.sceneId, request.mapName)
    if (collision.isEmpty()) {
      var spaceLock = LockHelper.lockSpacesUnion(spaces)
      //  检查是否有改变，如果一样大小，则不需要再次插入
      val originSpaceLock = QuadTree.queryByKey(spaceLock.getKey(), sceneMapKey(request.sceneId, request.mapName))
      if (originSpaceLock != null && checkSameSpaceLock(originSpaceLock, spaceLock)) {
        return LockResponse(request.id, true, spaces)
      }

      spaces = preProcessSpaceLock(spaces, spaceLock, request.sceneId, request.mapName)
      spaceLock = LockHelper.lockSpacesUnion(spaces)
      if (QuadTree.insert(spaceLock.box, spaceLock, sceneMapKey(request.sceneId, request.mapName))) {
        postProcessSpaceLock(spaceLock, request.sceneId, request.mapName)
        return LockResponse(request.id, true, spaces)
      }
    }

    return processCollisions(collision, request)
  }

  private fun processCollisions(collision: MutableList<SpaceLock>, request: LockRequest): LockResponse {
    var result: MutableList<SpaceLock> = mutableListOf()
    val blocks: MutableList<BlockItem> = mutableListOf()
    for (space in request.spaces) {
      for (c in collision) {
        if (space.checkCollision(c)) {
          val key = c.getKey().split("-><-")
          val block = BlockItem(key[2], LockType.valueOf(key[0]), null, null)
          blocks.add(block)
        }
      }
      if (blocks.isNotEmpty()) {
        break
      }
      result.add(space)
    }
    if (result.isEmpty()) {
      return LockResponse(request.id, false, mutableListOf(), blocks)
    }
    var spaceLock = LockHelper.lockSpacesUnion(result)
    val originSpaceLock = QuadTree.queryByKey(spaceLock.getKey(), sceneMapKey(request.sceneId, request.mapName))
    if (originSpaceLock != null && checkSameSpaceLock(originSpaceLock, spaceLock)) {
      return LockResponse(request.id, true, result, blocks)
    }

    result = preProcessSpaceLock(result, request.spaces, spaceLock, request.sceneId, request.mapName)
    if (result.isEmpty()) {
      return LockResponse(request.id, false, mutableListOf(), blocks)
    }
    spaceLock = LockHelper.lockSpacesUnion(result)
    if (QuadTree.insert(spaceLock.box, spaceLock, sceneMapKey(request.sceneId, request.mapName))) {
      postProcessSpaceLock(spaceLock, request.sceneId, request.mapName)
      return LockResponse(request.id, true, result, blocks)
    } else if (request.tryNumber < 3) {
      request.tryNumber++
      return lockHandle(request)
    }
    return LockResponse(request.id, false, mutableListOf(), blocks)
  }

  // 检查 两个 锁闭所占空间资源 是否相同
  private fun checkSameSpaceLock(origin: SpaceLock, current: SpaceLock): Boolean {
    if (!origin.box.sameBounding(current.box)) {
      return false
    }
    if (origin.cells.size != current.cells.size) {
      return false
    }
    for (i in 0 until origin.cells.size) {
      if (!origin.cells[i].box.sameBounding(current.cells[i].box)) {
        return false
      }
    }
    return true
  }

  private fun checkCollisions(
    spaces: MutableList<SpaceLock>,
    sceneId: String,
    mapName: String,
  ): MutableList<SpaceLock> {
    // 构建最大碰撞空间
    val maxBox = buildBox(spaces)

    val locks = QuadTree.queryByBounding(maxBox, sceneMapKey(sceneId, mapName))
    // 初次检测，帅选需要真正做碰撞检测的
    val needCheck: MutableList<SpaceLock> = mutableListOf()
    for (l in locks) {
      if (maxBox.intersect(l.box)) {
        needCheck.add(l)
      }
    }
    return needCheck
  }

  private fun buildBox(spaces: MutableList<SpaceLock>): BoundingBox {
    val maxBox = BoundingBox()
    for (space in spaces) {
      maxBox.updateBoundingBox(space.box)
    }
    return maxBox
  }

  private fun preProcessSpaceLock(
    spaces: MutableList<SpaceLock>,
    spaceLock: SpaceLock,
    sceneId: String,
    mapName: String,
  ): MutableList<SpaceLock> {
    val blockAreas = QuadTree.findBlockAreaByMapName(sceneMapKey(sceneId, mapName))
    if (blockAreas.isEmpty()) return spaces

    val robotName = spaceLock.getKey().split("-><-")[2]
    val collisionBlockArea = mutableListOf<BlockArea>()
    for (blockArea in blockAreas) {
      if (blockArea.shape.box.intersect(spaceLock.box) && !blockArea.robots.contains(robotName)) {
        collisionBlockArea.add(blockArea)
      }
    }

    if (collisionBlockArea.isEmpty()) {
      return spaces
    }

    val result = mutableListOf<SpaceLock>()
    for (space in spaces) {
      for (blockArea in collisionBlockArea) {
        if (blockArea.robots.contains(robotName)) continue

        if (space.checkCollision(blockArea.shape)) {
          if (blockArea.robots.size < blockArea.robotNum) {
            blockArea.robots.add(robotName)
          } else {
            return result
          }
        }
      }
      result.add(space)
    }
    return result
  }

  private fun preProcessSpaceLock(
    spaces: MutableList<SpaceLock>,
    allSpace: MutableList<SpaceLock>,
    spaceLock: SpaceLock,
    sceneId: String,
    mapName: String,
  ): MutableList<SpaceLock> {
    val spaceLocks = preProcessSpaceLock(spaces, spaceLock, sceneId, mapName)

    if (DistributedTrafficService.findSceneBySceneId(sceneId).config.preRotate) {
      if (allSpace.size > spaceLocks.size &&
        allSpace[spaceLocks.size].cells[0].layers[0].shape.type == GraphType.CIRCLE &&
        spaceLocks.isNotEmpty()
      ) {
        spaceLocks.removeLast()
      }
    }
    if (spaceLocks.isNotEmpty() && !checkJoint(allSpace.last(), sceneId, mapName)) {
      val last = spaceLocks.last()

      if (checkJoint(last, sceneId, mapName)) {
        var index = spaceLocks.size - 1
        for (i in spaceLocks.size - 2 downTo 0) {
          val space = spaceLocks[i]
          if (space.cells[0].layers[0].shape.type == GraphType.CIRCLE) continue
          val position = space.cells.last().tPosition
          if (position == null ||
            !MapService.findPointByName(sceneId, mapName, space.groupName, position.pointName).junction
          ) {
            break
          }
          index--
        }
        if (index == 0) return mutableListOf()
        return spaceLocks.subList(0, index)
      }
    }
    return spaceLocks
  }

  private fun checkJoint(spaceLock: SpaceLock, sceneId: String, mapName: String): Boolean {
    val tPosition = spaceLock.cells.last().tPosition
    return (
      spaceLock.cells[0].layers[0].shape.type == GraphType.CIRCLE &&
        MapService
          .findPointByName(sceneId, mapName, spaceLock.groupName, spaceLock.cells[0].sPosition.pointName).junction
      ) ||
      (
        tPosition != null &&
          MapService.findPointByName(sceneId, mapName, spaceLock.groupName, tPosition.pointName).junction
        )
  }

  private fun postProcessSpaceLock(spaceLock: SpaceLock, sceneId: String, mapName: String) {
    val blockAreas = QuadTree.findBlockAreaByMapName(sceneMapKey(sceneId, mapName))
    if (blockAreas.isEmpty()) return

    val robotName = spaceLock.getKey().split("-><-")[2]
    blockAreas.filter { it.robots.contains(robotName) }.map {
      if (!it.shape.checkCollision(spaceLock)) {
        it.robots.remove(robotName)
      }
    }
  }

  fun queryByBounding(box: BoundingBox, sceneId: String, mapName: String): MutableList<SpaceLock> =
    QuadTree.queryByBounding(box, sceneMapKey(sceneId, mapName))

  fun queryCurrentMapAllSpaceLock(mapCode: String): MutableList<SpaceLock> =
    QuadTree.queryCurrentMapAllSpaceLock(mapCode)

  fun queryAllSpaceLock(): MutableMap<String, MutableList<SpaceLock>> = QuadTree.queryAllSpaceLock()

  fun querySceneSpaceLock(sceneId: String): MutableMap<String, MutableList<SpaceLock>> =
    QuadTree.querySceneSpaceLock(sceneId)

  fun removeSpaceLockByKey(key: String, sceneId: String, mapName: String): Boolean {
    try {
      logger.info("remove $key")
      return QuadTree.remove(key, sceneMapKey(sceneId, mapName))
    } catch (e: NullPointerException) {
      logger.error("remove $key is error", e)
      return false
    }
  }

  fun querySpaceLockByKey(key: String, sceneId: String, mapName: String): SpaceLock? =
    QuadTree.queryByKey(key, sceneMapKey(sceneId, mapName))

  fun findPointsInBounding(box: BoundingBox, sceneId: String, mapName: String): MutableList<Point> =
    QuadTree.findPointsInBounding(box, sceneMapKey(sceneId, mapName))

  fun findLinesInBounding(box: BoundingBox, sceneId: String, mapName: String): MutableList<Line> =
    QuadTree.findLinesInBounding(box, sceneMapKey(sceneId, mapName))

  /**
   *  key: sceneId-mapName
   * */
  fun initQuadTree(map: Map<String, MapArea>) {
    QuadTree.init(map)
  }

  fun updateQuadTree(sceneMapKey: String, area: MapArea) {
    QuadTree.update(sceneMapKey, area)
  }

  /**
   *  key: sceneId-mapName
   * */
  fun initBlockArea(bas: MutableMap<String, MutableList<BlockArea>>) {
    QuadTree.initBlockArea(bas)
  }

  fun sceneMapKey(sceneId: String, mapName: String): String = "$sceneId-$mapName"
}

enum class LockType {
  ROBOT,
  RACK,
  AREA,
  TEMPLE,
  EXTERNAL,
  ROBOT_WAIT,
}