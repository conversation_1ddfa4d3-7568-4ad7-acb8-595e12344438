package com.seer.trick.fleet.falcon.bp

import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.falcon.domain.ParamObjectType
import com.seer.trick.fleet.order.OrderService
import com.seer.trick.fleet.service.SceneService

class CompleteTransportOrderBp : AbstractBp() {

  override fun process() {
    val orderId = mustGetBlockInputParam("orderId") as String

    val sceneId = OrderService.mustGetSceneIdByOrderId(orderId)
    val sr = SceneService.mustGetSceneById(sceneId)
    val or = sr.mustGetOrderById(orderId)

    OrderService.updateAndPersistOrder(or, or.order.copy(stepFixed = true), "Set step fixed by falcon bp")
    removeResource("TransportOrder", orderId)
  }

  companion object {

    val def = BlockDef(
      CompleteTransportOrderBp::class.simpleName!!,
      color = "#C7DCA7",
      inputParams = listOf(
        BlockInputParamDef(
          "orderId",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.OrderId),
        ),
      ),
    )
  }
}