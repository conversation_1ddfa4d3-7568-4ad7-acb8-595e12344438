package com.seer.trick.fleet.mars

import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.robot.rachel.*

object MarsDomainConverter {

  fun convertRobotConfig(rr: RobotRuntime): MrSystemConfig {
    val config = rr.config
    return MrSystemConfig(
      mutableMapOf(
        "id" to config.robotName,
        "disabled" to config.disabled,
        "offDuty" to rr.offDuty,
        "vendor" to "Seer",
        "connectionType" to "GwWsLight",
        "gwAuthId" to rr.config.gwAuthId,
        "gwAuthSecret" to rr.config.gwAuthSecret,
        "robotHost" to config.robotIp,
        "selfBinNum" to config.selfBinNum,
      ),
    )
  }

  fun convertSelfReport(report: RobotSelfReport): MrRobotSelfReport = MrRobotSelfReport(
    error = report.error,
    errorMsg = report.errorMsg,
    timestamp = report.timestamp,
    main = report.main?.let { convertReportMain(it) },
    rawReport = report.rawReport,
  )

  private fun convertReportMain(main: RobotSelfReportMain): MrRobotSelfReportMain = MrRobotSelfReportMain(
    battery = main.battery,
    x = main.x,
    y = main.y,
    direction = main.direction,
    // currentAreaId = main., TODO
    // currentAreaName = main., TODO
    currentMap = main.currentMap,
    currentMapMd5 = main.currentMapMd5,
    currentSite = main.currentPoint,
    blocked = main.blocked,
    charging = main.charging,
    emergency = main.emergency,
    softEmc = main.softEmc,
    relocStatusLabel = main.relocStatusLabel,
    confidence = main.confidence,
    todayOdo = main.todayOdo,
    currentLockNickName = main.controller,
    alerts = main.alarms?.map { convertRobotAlarm(it) },
    moveStatusInfo = main.moveStatusInfo,
  )

  private fun convertRobotAlarm(alarm: RobotAlarm): MrRobotAlert = MrRobotAlert(
    level = convertAlarmLevel(alarm.level),
    code = alarm.code,
    message = alarm.message,
    times = alarm.times,
    timestamp = alarm.timestamp,
  )

  private fun convertAlarmLevel(level: RobotAlarmLevel): MrRobotAlertLevel = when (level) {
    RobotAlarmLevel.Info -> MrRobotAlertLevel.Info
    RobotAlarmLevel.Warning -> MrRobotAlertLevel.Warning
    RobotAlarmLevel.Error -> MrRobotAlertLevel.Error
    RobotAlarmLevel.Fatal -> MrRobotAlertLevel.Fatal
  }
}