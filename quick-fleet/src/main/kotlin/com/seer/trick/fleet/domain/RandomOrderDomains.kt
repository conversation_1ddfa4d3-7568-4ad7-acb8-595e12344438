package com.seer.trick.fleet.domain

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude

/**
 * 随机发单模式
 */
enum class RandomMode {
  Simple, // 简单随机发单
  Group, // 按机器人组随机发单
  BzLine, // 按业务线随机发单
}

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class RandomOrderConfig(
  val enabled: Boolean = false, // 是否启用，默认不启用，该属性保存时保持不启用状态
  val mode: RandomMode = RandomMode.Simple, // 随机发单模式
  val simpleConfig: SimpleConfig? = null, // 简单模式的配置
  val groupConfigs: List<GroupConfig>? = null, // 机器人组发单的配置
  val bzLineConfigs: List<BzLineConfig>? = null, // 业务线发单的配置
)

/**
 * 简单随机发单配置
 */
data class SimpleConfig(val containerTypeNames: List<String> = emptyList(), val keepOrderNum: Int = 10)

/**
 * 机器人组随机发单配置
 */
data class GroupConfig(
  val robotGroupId: String, // 机器人组 ID
  val robotGroupName: String, // 机器人组名称
  val containerTypeNames: List<String> = emptyList(), // 容器类型
  val keepOrderNum: Int = 10, // 保持单量
)

/**
 * 业务线随机发单配置
 */
data class BzLineConfig(
  val id: String, // 业务线 Id。前端用于排序
  // val name: String, // 业务线名称，暂不使用
  val fromLabels: String, // 起点库位/点位标签，多个使用逗号分隔
  val toLabels: String, // 终点库位/点位标签，多个使用逗号分隔
  val robotGroupNames: String, // 限定机器人组。使用的是组名，多个使用逗号分隔
  val containerTypeNames: List<String> = emptyList(), // 容器类型
  val keepOrderNum: Int = 10, // 保持单量
)