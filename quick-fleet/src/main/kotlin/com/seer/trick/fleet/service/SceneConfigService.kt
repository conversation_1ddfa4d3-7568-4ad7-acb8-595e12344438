package com.seer.trick.fleet.service

import com.fasterxml.jackson.module.kotlin.convertValue
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.fleet.domain.SceneConfig
import com.seer.trick.fleet.order.OrderService
import com.seer.trick.helper.JsonFileHelper
import com.seer.trick.helper.JsonHelper
import org.slf4j.LoggerFactory
import java.io.File

/**
 * 场景的配置管理。
 */
object SceneConfigService {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 加载一个场景的配置
   */
  fun init(sr: SceneRuntime) {
    val config: SceneConfig = JsonFileHelper.readJsonFromFile(getSceneConfigFile(sr.sceneId)) ?: return
    sr.config = config

    // 更新交管高级配置
    sr.updateDevConfigs(config.trafficDevConfigStr)
  }

  /**
   * 更新部分配置项。返回更新后的全量配置对象。
   * 要求快速返回，不要阻塞。
   */
  fun patchSceneConfig(sr: SceneRuntime, update: EntityValue): SceneConfig = sr.withOrderLock {
    logger.info("Patch scene config, scene=$sr, update=$update")

    // 利用 map 和 Jackson 做部分更新；因此只支持单层字段
    val old = sr.config
    val m: EntityValue = JsonHelper.mapper.convertValue(old)
    m.putAll(update)
    val newConfig: SceneConfig = JsonHelper.mapper.convertValue(m, jacksonTypeRef())

    // 检查 TODO 并发风险，加什么锁合适
    if (newConfig.trafficMethod != old.trafficMethod) {
      // 如果要修改交管策略，必须没有未完成的运单
      sr.throwIfSceneWithOrders()
    }

    saveSceneConfig(sr, newConfig)

    if (newConfig.trafficMethod != old.trafficMethod) {
      // 修改交管策略
      OrderService.changeTrafficService(sr, newConfig.trafficMethod)
    }

    // 更新交管高级配置
    sr.updateDevConfigs(newConfig.trafficDevConfigStr)

    return@withOrderLock newConfig
  }

  /**
   * 更新：暂停交管规划
   */
  fun updateTrafficPlanPaused(sr: SceneRuntime, f: Boolean) {
    patchSceneConfig(sr, mutableMapOf("trafficPlanPaused" to f))
  }

  // 只负责持久化
  private fun saveSceneConfig(sr: SceneRuntime, config: SceneConfig) {
    sr.config = config
    JsonFileHelper.writeJsonToFile(getSceneConfigFile(sr.sceneId), config, true)
  }

  private fun getSceneConfigFile(sceneId: String): File {
    val dir = SceneFileService.getSceneDir(sceneId)
    return File(dir, "scene-config.json")
  }
}