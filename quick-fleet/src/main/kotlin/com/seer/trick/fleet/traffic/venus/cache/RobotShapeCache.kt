package com.seer.trick.fleet.traffic.venus.cache

import com.seer.trick.fleet.domain.GeoHelper
import com.seer.trick.fleet.domain.Polygon
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.PI
import kotlin.math.floor

/**
 * 缓存机器人碰撞模型在离散角度(5°)下绕原点旋转后的多边形。
 * 为 translateThenRotate(robotModel, dx,dy, dx,dy, angle) 做加速：
 * 最终形状 = cachedRotate(angle) 平移 (dx,dy)。
 */
object RobotShapeCache {
  private const val STEP_DEG = 1
  private const val STEPS = 360 / STEP_DEG // 360

  /**
   * 缓存表：每种 robot 模型(Polygon 实例) -> 360 个方位的多边形。
   */

  // todo 后续去掉
  private class PolyKey(val poly: Polygon) {
    private val id = System.identityHashCode(poly)
    override fun hashCode(): Int = id
    override fun equals(other: Any?): Boolean = (other as? PolyKey)?.poly === poly
  }

  private val cache: ConcurrentHashMap<PolyKey, Array<Polygon>> = ConcurrentHashMap()

  /**
   * 获取绕原点旋转 [angleRad] 后的模型多边形。
   * angle 会被量化到 5° 档位。
   */
  fun getRotated(model: Polygon, angleRad: Double): Polygon {
    val twoPi = 2 * PI
    val stepRad = STEP_DEG * PI / 180.0
    val normRad = ((angleRad % twoPi) + twoPi) % twoPi // 0<=rad<2π
    val idx = floor(normRad / stepRad).toInt() % STEPS

    val key = PolyKey(model)
    val arr = cache.computeIfAbsent(key) {
      // 预计算 360 个角度的旋转模型
      Array(STEPS) { i ->
        val rad = i * STEP_DEG * PI / 180.0
        GeoHelper.rotate(model, 0.0, 0.0, rad)
      }
    }
    return arr[idx]
  }

  /**
   * 基于 (width, head, tail) 三个参数描述的矩形模型键。
   * 直接依赖 `Double` 精确相等作为哈希/比较依据，
   * 风险：
   * 浮点误差导致缓存 miss：业务代码若传入的尺寸经过不同计算路径，比如 1.0 与 0.999999999
   *    即使逻辑上等长，二进制值可能略有差异，导致 key 不相等 --> 无法命中缓存。
   * 内存增长：同一物理尺寸的多个近似值会各自生成 360×Polygon 数组，
   */
  private data class RectKey(val width: Double, val head: Double, val tail: Double)

  private val rectCache: ConcurrentHashMap<RectKey, Array<Polygon>> = ConcurrentHashMap()

  fun getRotated(robotWidth: Double, robotHead: Double, robotTail: Double, angleRad: Double): Polygon {
    val twoPi = 2 * PI
    val stepRad = STEP_DEG * PI / 180.0
    val normRad = ((angleRad % twoPi) + twoPi) % twoPi
    val idx = floor(normRad / stepRad).toInt() % STEPS

    val key = RectKey(robotWidth, robotHead, robotTail)
    val arr = rectCache.computeIfAbsent(key) {
      // 构造矩形原型：机器人局部坐标系，中心位于 (robotHead - robotTail) / 2,0)，x 正方向朝向车头
      val base = Polygon.fromRectCenterWidthHeight((robotHead - robotTail) / 2, 0.0, robotHead + robotTail, robotWidth)
      Array(STEPS) { i ->
        val rad = i * STEP_DEG * PI / 180.0
        GeoHelper.rotate(base, 0.0, 0.0, rad)
      }
    }
    return arr[idx]
  }
}