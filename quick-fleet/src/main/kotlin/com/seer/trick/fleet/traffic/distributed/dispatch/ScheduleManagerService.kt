package com.seer.trick.fleet.traffic.distributed.dispatch

import com.seer.trick.fleet.traffic.distributed.context.ContextManagerService
import com.seer.trick.fleet.traffic.distributed.context.domain.RobotContext
import com.seer.trick.fleet.traffic.distributed.service.DistributedTrafficService
import io.javalin.util.NamedThreadFactory
import org.slf4j.LoggerFactory
import java.util.concurrent.*

/**
 *  任务调度执行入口
 *
 * */
object ScheduleManagerService {

  private val logger = LoggerFactory.getLogger(javaClass)

  // 定义一个线程池
  private val schedulePool = ScheduledThreadPoolExecutor(1, NamedThreadFactory("dispatch"))

  fun start() {
    // 启动线程池
    schedulePool.scheduleWithFixedDelay(this::doSchedule, 1000, 500, TimeUnit.MILLISECONDS)
  }

  private fun doSchedule(): Boolean {
    try {
      // 查询所有车的状态信息
      val allRobotInfo = DistributedTrafficService.queryAllRobotInfo()
      val robots: MutableList<RobotContext> = ArrayList()
      for (data in allRobotInfo) {
        try {
          val robotContext = ContextManagerService.updateRobotContext(data) ?: continue
          robots.add(robotContext)
        } catch (eu: RuntimeException) {
          logger.error("update robot context failed $eu")
        } catch (e: Exception) {
          logger.error("update robot context failed $e")
        }
      }

      ScheduleService.schedule(robots)
    } catch (e: Exception) {
      logger.error("doSchedule error", e)
    }
    return true
  }

  fun stop() {
    schedulePool.shutdown()
  }
}