package com.seer.trick.fleet.traffic.distributed.deadlock.helper

import com.seer.trick.fleet.traffic.distributed.context.domain.RobotContext
import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper
import com.seer.trick.fleet.traffic.distributed.map.Line
import com.seer.trick.fleet.traffic.distributed.map.PointType
import org.slf4j.LoggerFactory

/**
 *  死锁校验辅助类
 * */
object DeadLockHelper {

  private val logger = LoggerFactory.getLogger(javaClass)

  fun canPass(robot: RobotContext, line: Line): Boolean {
    // 2、检测是否解死锁不可穿行
    if (!line.passThrough) {
      logger.warn("${robot.robotName} can't pass line:${line.lineName}, passThrough:${line.passThrough}")
      return false
    }
    if (!line.robotCanPass(robot.baseDomain.isLoading())) {
      return false
    }
    // 3、业务限制
    if (line.end.type != PointType.COMMON && line.end.type != PointType.ROTATE) {
      logger.warn("${robot.robotName} can't pass line:${line.lineName}, next point type is ${line.end.type}")
      return false
    }
    // todo other check
    return true
  }

  fun pointNotRotate(line: Line, robotHeading: Int): Boolean = !line.start.rotate &&
    !AngleHelper.sameAngleInFiveDegree(robotHeading, line.enterDir) &&
    !AngleHelper.sameAngleInFiveDegree(AngleHelper.processAngle(robotHeading + AngleHelper.DOWN_ANGLE), line.enterDir)

  fun sortMapByValue(map: MutableMap<String, Int>): MutableMap<String, Int> {
    if (map.isEmpty()) return map
    // 将 Map 转换为 List，其中每个元素是一个 Pair，包含 key 和 value
    val sortedList = map.toList().sortedWith(compareBy { it.second })

    // 创建一个 LinkedHashMap 以保留排序后的顺序

    return sortedList.toMap(LinkedHashMap())
  }
}