package com.seer.trick.fleet.handler

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.getReqBody
import com.seer.trick.base.http.handler.MissingQueryParamError
import com.seer.trick.base.script.ScriptCenter
import com.seer.trick.base.script.ScriptExeRequest
import com.seer.trick.fleet.domain.GeoHelper
import com.seer.trick.fleet.domain.MapPath
import com.seer.trick.fleet.domain.MoveDirection
import com.seer.trick.fleet.domain.Polygon
import com.seer.trick.fleet.domain.Pose2D
import com.seer.trick.fleet.service.RobotLoadCollisionService
import com.seer.trick.fleet.service.SceneService
import com.seer.trick.fleet.traffic.dev.ADG
import com.seer.trick.fleet.traffic.dev.ECBS
import com.seer.trick.fleet.traffic.dev.MapfResult
import com.seer.trick.fleet.traffic.dev.MapfTestReq
import com.seer.trick.fleet.traffic.dev.VenusLoadTaskReq
import com.seer.trick.fleet.traffic.venus.PlanService
import com.seer.trick.fleet.traffic.venus.ResConflictManager
import com.seer.trick.fleet.traffic.venus.VenusTrafficService
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.StringHelper
import io.javalin.http.Context

object DevHandler {

  fun registerHandlers() {
    val c = Handlers("api/fleet/dev")

    c.post("mapf-test", ::mapfTest, auth())
    c.post("venus-load-task", ::venusLoadTask, auth())

    c.post("debug-collisions", ::debugCollisions, auth())

    c.get("mapf-high-nodes", ::getMapfHighNodes, auth())
  }

  private fun mapfTest(ctx: Context) {
    val req: MapfTestReq = ctx.getReqBody()

    // 规划和后处理可以通过脚本定制，否则使用默认实现

    val solution: MapfResult = if (!req.planScriptFunc.isNullOrBlank()) {
      // 转换成可以通过字段访问的
      val reqMap: Map<String, Any?> = JsonHelper.mapper.convertValue(req, jacksonTypeRef())
      ScriptCenter.execute(ScriptExeRequest(req.planScriptFunc, arrayOf(reqMap)), jacksonTypeRef())
    } else {
      val cbs = ECBS(
        w = req.w,
        mapDimX = req.mapDimX,
        mapDimY = req.mapDimY,
        obstacles = req.obstacles,
        tasks = req.tasks,
      )
      cbs.search()
    }

    val deps: Map<String, List<String>> = if (!req.adgScriptFunc.isNullOrBlank()) {
      ScriptCenter.execute(ScriptExeRequest(req.adgScriptFunc, arrayOf(solution)), jacksonTypeRef())
    } else {
      ADG(solution).nodes
    }

    ctx.json(mapOf("solution" to solution, "adg" to deps))
  }

  private fun venusLoadTask(ctx: Context) {
    val req: VenusLoadTaskReq = ctx.getReqBody()

    PlanService.planCase(req.sceneName, req.name)
  }

  private fun debugCollisions(ctx: Context) {
    val req: BuildCollisionsReq = ctx.getReqBody()

    val result = mutableMapOf<String, DebugCollisionRobotResult>()

    val sr = SceneService.mustGetSceneById(req.sceneId)

    for ((robotName, action) in req.actions) {
      val rr = sr.mustGetRobot(robotName)
      val main = rr.selfReport?.main ?: continue
      val cm = RobotLoadCollisionService.buildCollisionModel(rr, main)

      val pointNames = StringHelper.splitTrim(action, "->")

      val areaMapCache = rr.sr.mapCache.areaById[req.areaId]?.mergedMap
        ?: throw BzError("errBzError", "No area ${req.areaId}")

      val shapes = if (pointNames.size == 1) {
        val p1 = pointNames[0]
        val pr = areaMapCache.pointNameMap[p1] ?: throw BzError("errBzError", "No point $p1")
        RobotLoadCollisionService.buildCollisionShape(
          cm,
          Pose2D(
            pr.point.x,
            pr.point.y,
            0.0,
          ),
        )
      } else {
        val shapes = mutableListOf<Polygon>()

        var p1EndTheta: Double
        for (i in 1 until pointNames.size) {
          val p1 = pointNames[i - 1]
          val p2 = pointNames[i]
          val pr1 = areaMapCache.pointNameMap[p1] ?: throw BzError("errBzError", "No point $p1")

          val pathKey = MapPath.getKey(p1, p2)
          val pathR = areaMapCache.pathKeyMap[pathKey] ?: throw BzError("errBzError", "No path $pathKey")

          val robotEnterTheta = pathR.path.tracePoints.first().tangent

          p1EndTheta = pathR.path.tracePoints.last().tangent

          shapes.addAll(
            ResConflictManager.buildEdgeSpace(
              cm,
              pr1.point.x,
              pr1.point.y,
              p1EndTheta,
              robotEnterTheta,
              pathR.path,
              true, // TODO
              MoveDirection.Forward,
            ),
          )
        }
        shapes
      }

      result[robotName] = DebugCollisionRobotResult(shapes, mutableSetOf())
    }

    for ((robotName1, robotResult1) in result) {
      for ((robotName2, robotResult2) in result) {
        if (robotName1 == robotName2) continue
        if (hasCollision(robotResult1.shapes, robotResult2.shapes)) robotResult1.conflictedRobots += robotName2
      }
    }

    ctx.json(result)
  }

  private fun hasCollision(shapes1: List<Polygon>, shapes2: List<Polygon>): Boolean {
    if (shapes1.isEmpty() || shapes2.isEmpty()) return false
    for (p1 in shapes1) {
      for (p2 in shapes2) {
        if (GeoHelper.isPolygonsIntersecting(p1, p2)) return true
      }
    }
    return false
  }

  data class BuildCollisionsReq(val sceneId: String, val areaId: Int, val actions: List<DebugCollisionsReq>)

  data class DebugCollisionsReq(val robotName: String, val action: String)

  data class DebugCollisionRobotResult(val shapes: List<Polygon>, val conflictedRobots: MutableSet<String>)

  private fun getMapfHighNodes(ctx: Context) {
    val sceneId = ctx.queryParam("sceneId") ?: throw MissingQueryParamError("sceneId")
    val sr = SceneService.mustGetSceneById(sceneId)
    val ts = sr.trafficService
    if (ts is VenusTrafficService) {
      ctx.json(ts.getLastHighNodes() ?: emptyList<Any>())
    } else {
      ctx.json(emptyList<Any>())
    }
  }
}