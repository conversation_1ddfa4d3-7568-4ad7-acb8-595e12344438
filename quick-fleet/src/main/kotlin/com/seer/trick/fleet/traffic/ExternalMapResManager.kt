package com.seer.trick.fleet.traffic

import com.seer.trick.fleet.domain.MapPath
import com.seer.trick.fleet.domain.MapPoint
import com.seer.trick.fleet.domain.Point2D
import com.seer.trick.fleet.domain.Polygon
import com.seer.trick.fleet.map.MapPathCache
import com.seer.trick.fleet.map.MapPointCache
import com.seer.trick.fleet.service.SceneService
import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

/**
 * 外部地图资源申请和释放管理。
 * 可以占用点位、路径或空间区域。
 */
object ExternalMapResManager {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 已占用资源 unit id -> unit
   */
  val occupied: MutableMap<String, MapResourceUnit> = ConcurrentHashMap()

  private val resLock = ReentrantLock()

  private fun <T> withResLock(worker: () -> T): T = resLock.withLock { worker() }

  /**
   * 申请占用一批资源。
   * 必须都成功才返回成功，否则失败。
   * 资源按 owner 是可重入的。例如如果 owner 是某个机器人 A，机器人之前申请了点位 AP1，同一个机器人再申请 AP1 仍成功。
   * unitId 不能重复，不能在已占用的资源中出现。
   */
  fun request(units: List<MapResourceUnit>): Boolean = withResLock {
    logger.info("request external map resource: $units")
    if (units.isEmpty()) return@withResLock false
    // 校验：暂时只支持申请一个场景、一个区域、一个 owner
    if (checkIllegal(units)) return@withResLock false

    // 校验：是否是已经申请过的
    for (u in units) {
      val t = occupied[u.unitId]
      if (t != null && t != u) {
        logger.warn("unitId ${u.unitId}: $u is already occupied: $t")
        return@withResLock false
      }
    }

    val sceneId = units.first().sceneId
    val areaId = units.first().areaId
    val owner = units.first().owner
    val sr = SceneService.getSceneById(sceneId) ?: return@withResLock false
    val area = sr.mapCache.areaById[areaId] ?: return@withResLock false
    val pointMap = area.mergedMap.pointNameMap
    val pathMap = area.mergedMap.pathKeyMap

    val newUnits =
      occupied.values.filter { it.sceneId == sceneId && it.areaId == areaId && it.owner == owner }.toMutableList()
    newUnits.addAll(units)

    val pair = unitsToPolygons(newUnits, pointMap, pathMap)
    if (pair.first) { // 有不存在的点位、路径
      return@withResLock false
    }

    // 调用交管申请资源
    // 申请不到就 return false
    val r = sr.trafficService.requestSpaceResource(owner, areaId.toString(), pair.second)

    // 记录占用
    if (r) {
      units.forEach { occupied[it.unitId] = it }
      logger.info("request external map SUCCESS: $units")
    }

    return@withResLock r
  }

  private fun unitsToPolygons(
    units: List<MapResourceUnit>,
    pointMap: MutableMap<String, MapPointCache>,
    pathMap: MutableMap<String, MapPathCache>,
  ): Pair<Boolean, MutableList<Polygon>> {
    val polygons = mutableListOf<Polygon>()

    for (unit in units) {
      for (name in unit.pointNames ?: emptyList()) {
        val p = pointMap[name]?.point ?: return Pair(true, polygons)
        polygons.add(pointToPolygon(p))
      }
      for (key in unit.pathKeys ?: emptyList()) {
        val p = pathMap[key] ?: return Pair(true, polygons)
        polygons.addAll(pathToPolygon(p.path))
      }
      polygons.addAll(unit.spatialZones ?: emptyList())
    }

    return Pair(false, polygons.toSet().toMutableList()) // 去重
  }

  private fun pointToPolygon(point: MapPoint): Polygon = positionToPolygon(point.x, point.y)

  /**
   * 生成一个 10cm * 10cm 的矩形
   */
  private fun positionToPolygon(x: Double, y: Double): Polygon = Polygon(
    listOf(
      Point2D(x - 0.05, y + 0.05),
      Point2D(x + 0.05, y + 0.05),
      Point2D(x + 0.05, y - 0.05),
      Point2D(x - 0.05, y - 0.05),
    ),
  )

  /**
   * 将路径转为一段空间资源
   *
   * TODO 目前 MapPath.tracePoints 是按照每 0.1 m 一个点切分的，暂时用这个，不用更细的维度了。
   *  更细的维度：将路径转换为 b 样条，然后再将路径按 1mm 长度切开，并按 1 mm 生成多边形
   */
  private fun pathToPolygon(path: MapPath): List<Polygon> = path.tracePoints.map { positionToPolygon(it.x, it.y) }

  /**
   * 按单元 ID 释放资源。
   */
  fun release(unitIds: List<String>) = withResLock {
    logger.info("release external map resource: $unitIds")
    val removeUnits = unitIds.mapNotNull { occupied[it] }
    if (removeUnits.isEmpty()) return@withResLock
    if (checkIllegal(removeUnits)) return@withResLock

    // 剩余的资源。交管的接口是全量接口，要传入 owner 在当前 area 剩余的所有资源
    val sceneId = removeUnits.first().sceneId
    val areaId = removeUnits.first().areaId
    val owner = removeUnits.first().owner
    val units = occupied.values.filter {
      it.sceneId == sceneId &&
        it.areaId == areaId &&
        it.owner == owner &&
        it.unitId !in unitIds
    }

    val sr = SceneService.getSceneById(sceneId) ?: return@withResLock
    val area = sr.mapCache.areaById[areaId] ?: return@withResLock
    val pointMap = area.mergedMap.pointNameMap
    val pathMap = area.mergedMap.pathKeyMap

    val pair = unitsToPolygons(units, pointMap, pathMap)
    if (pair.first) { // 有不存在的点位、路径
      return@withResLock
    }

    // 调用交管申请资源
    // 释放资源时，新的 polygon list 一定比原本的小，所以一定会成功
    // 交管要求 polygon list 为空时，调用 release 接口，不是 request 接口
    val r = if (pair.second.size > 0) {
      sr.trafficService.requestSpaceResource(owner, areaId.toString(), pair.second)
    } else {
      sr.trafficService.releaseSpaceResource(owner, areaId.toString())
    }

    // 记录占用
    if (r) {
      unitIds.forEach { occupied.remove(it) }
    }

    return@withResLock
  }

  private fun checkIllegal(units: List<MapResourceUnit>): Boolean {
    // 校验：暂时只支持申请一个场景、一个区域、一个 owner
    val sceneId = units.first().sceneId
    val areaId = units.first().areaId
    val owner = units.first().owner
    for (unit in units) {
      if (unit.sceneId != sceneId || unit.areaId != areaId || unit.owner != owner) {
        logger.warn("request units must in the same area, and same owner")
        return true
      }
    }
    return false
  }

  /**
   * 按 owner 释放 owner 的所有资源。
   */
  fun releaseByOwner(owner: String, sceneId: String? = null, areaId: Int? = null) = withResLock {
    logger.info("Release by owner: $owner, sceneId: $sceneId, areaId: $areaId")
    var units = occupied.values.filter { it.owner == owner }
    if (sceneId != null) units = units.filter { it.sceneId == sceneId }
    if (areaId != null) units = units.filter { it.areaId == areaId }

    // 根据 sceneId、areaId 分组
    val groups = units.groupBy { it.sceneId to it.areaId }
    for ((key, _) in groups) {
      val (sceneId, areaId) = key
      val sr = SceneService.getSceneById(sceneId) ?: continue
      sr.trafficService.releaseSpaceResource(owner, areaId.toString())
    }
    units.map { occupied.remove(it.unitId) }
  }

  fun releaseAll() = withResLock {
    logger.info("Release all")
    // 根据 sceneId、areaId 分组
    val groups = occupied.values.groupBy { it.sceneId to it.areaId }
    for ((key, value) in groups) {
      val (sceneId, areaId) = key
      val owners = value.map { it.owner }.toSet()
      val sr = SceneService.getSceneById(sceneId) ?: continue
      for (owner in owners) {
        sr.trafficService.releaseSpaceResource(owner, areaId.toString()) // TODO 后续需要交管提供一个释放全部的接口
      }
    }
    occupied.clear()
  }
}

/**
 * 资源申请和释放的最小单元
 *
 * ownerId、sceneId、areaId 暂时放在 unit 里，如果后续支持同时申请不同场景、区域、所有者的资源时，不用改数据结构
 */
data class MapResourceUnit(
  val unitId: String, // 单元 ID，必须唯一，建议使用 UUID
  val owner: String, // 所有者，可以是机器人名称、外部系统名称。约束：实施时，外部系统名不能与机器同名，不同外部系统名称唯一。
  val reason: String = "", // 原因
  val sceneId: String, // 场景 ID
  val areaId: Int, // 在哪个区域！
  val pointNames: List<String>? = null, // 占用点位
  val pathKeys: List<String>? = null, // 占用路径
  val spatialZones: List<Polygon>? = null, // 占用空间区域
)