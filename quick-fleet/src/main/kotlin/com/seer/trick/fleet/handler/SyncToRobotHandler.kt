package com.seer.trick.fleet.handler

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.base.http.*
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.fleet.service.SceneRobotSyncService
import com.seer.trick.fleet.service.SceneRobotSyncService.PushMapRobotContext.Companion.PUSH_ROBOT_MAP_RESULT_PUSHING
import com.seer.trick.helper.JsonHelper
import io.javalin.http.Context
import io.javalin.websocket.WsMessageContext

/**
 * 同步场景（地图）给机器人
 */
object SyncToRobotHandler : WebSocketSubscriber() {

  fun registerHandlers() {
    WebSocketManager.subscribers += this

    val c = Handlers("api/fleet/scenes")

    // 检查场景和机器人地图一致性
    c.post("{sceneId}/check-maps", ::checkMaps, auth())

    // 推送地图给机器人
    c.post("{sceneId}/push-maps", ::pushMapsToRobots, auth())
    // 取消异步推送任务
    c.post("{sceneId}/cancel-push-task-async", ::cancelPushTaskAsync, auth())
  }

  override fun onMessage(ctx: WsMessageContext, msg: WsMsg, env: WsEnv) {
    when (msg.action) {
      "Fleet::Scene::SyncMaps" -> onSyncMaps(ctx, msg)
    }
  }

  private fun onSyncMaps(ctx: WsMessageContext, msg: WsMsg) {
    val req: SceneIdReq = JsonHelper.mapper.readValue(msg.content!!, jacksonTypeRef())

    val cr = SceneRobotSyncService.queryCheckMapsProgress(req.sceneId)

    val pr = SceneRobotSyncService.queryPushMapsProgress(req.sceneId)

    val task = SceneRobotSyncService.queryAsyncPushTask(req.sceneId)
    val ar = if (task == null) {
      mapOf("task" to false)
    } else {
      mapOf("task" to true, "done" to (task.status != PUSH_ROBOT_MAP_RESULT_PUSHING), "desc" to task.desc())
    }

    val r = mapOf("check" to cr, "push" to pr, "async" to ar)
    ctx.send(WsMsg.json("Fleet::Scene::SyncMaps::Reply", r, replyToId = msg.id))
  }

  /**
   * 检查场景和机器人地图一致性。发起后立即返回。
   */
  private fun checkMaps(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")

    SceneRobotSyncService.checkMaps(sceneId)
  }

  data class PushMapsToRobotsReq(
    val robotNames: List<String>, // 要推送给哪些机器人
    val async: Boolean = false,
  )

  /**
   * 推送地图给机器人。发起后立即返回。
   */
  private fun pushMapsToRobots(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    val req: PushMapsToRobotsReq = ctx.getReqBody()

    // 若存在异步任务则需先取消
    val asyncPushTask = SceneRobotSyncService.queryAsyncPushTask(sceneId)
    if (asyncPushTask?.ok() == false) throw BzError("errExistBgPushTask")

    if (req.async) {
      SceneRobotSyncService.pushToRobotsAsync(sceneId, req.robotNames)
    } else {
      // 同步推送时，先清一下异步消息
      SceneRobotSyncService.removeAsyncPushTask(sceneId)
      SceneRobotSyncService.pushToRobots(sceneId, req.robotNames)
    }

    ctx.status(200)
  }

  data class SceneIdReq(val sceneId: String)

  /**
   * 取消异步任务
   */
  private fun cancelPushTaskAsync(ctx: Context) {
    val sceneId = ctx.pathParam("sceneId")
    SceneRobotSyncService.cancelPushTaskAsync(sceneId)
    ctx.status(200)
  }
}