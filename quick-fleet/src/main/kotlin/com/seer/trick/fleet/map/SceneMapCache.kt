package com.seer.trick.fleet.map

import com.seer.trick.BzError
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.SceneRuntime
import java.util.concurrent.ConcurrentHashMap

/**
 * 一个场景的地图的运行时表示。通过构建索引，预先计算，加速访问和计算。
 * 分四个级别：1、整个场景的；2、分区域的；3、分机器人组的；4、分机器人的；
 */
class SceneMapCache(private val sr: SceneRuntime) {

  /**
   * 所有机器人所有区域的所有点位。
   * 仅用于前端
   */
  @Volatile
  var pointNames: Set<String> = emptySet()

  /**
   * 所有机器人所有区域的所有库位。
   * 仅用于前端
   */
  @Volatile
  var binNames: Set<String> = emptySet()

  /**
   * 各个库位上的 binTask 详情。 库位名称 -> 库位上的 binTask 的键值对。
   *  示例：{"LOC-AP1": ["Wait", "Load", "Unload"]}
   *  仅用于前端
   */
  @Volatile
  var binTasks: Map<String, Set<String>> = emptyMap()

  /**
   * 根据区域 id 查找区域的运行时
   */
  val areaById: MutableMap<Int, SceneAreaCache> = ConcurrentHashMap()

  /**
   * 机器人组地图。group id -> group map
   */
  val groupMaps: MutableMap<Int, GroupMapCache> = ConcurrentHashMap()

  /**
   * 机器人的地图。robot name ->
   */
  val robotMaps: MutableMap<String, RobotMapConfigCache> = ConcurrentHashMap()

  /**
   * 根据区域 ID 获取区域缓存。
   */
  fun mustGetAreaCacheById(areaId: Int): SceneAreaCache = areaById[areaId] ?: throw BzError("errNoAreaById", areaId)

  /**
   * 获取一个机器人组的所有区域的地图
   */
  fun getAllAreaMapsOfGroup(rgId: Int): List<AreaMapCache> = areaById.values.mapNotNull { it.groupedMaps[rgId] }

  /**
   * 根据机器人当前位置获取区域缓存。
   */
  fun getAreaCacheByStand(rr: RobotRuntime): SceneAreaCache? {
    val stand = rr.selfReport?.stand ?: return null
    val areaId = stand.areaId
    return rr.sr.mapCache.areaById[areaId]
  }

  /**
   * 根据机器人当前位置和组获取组地图缓存。
   */
  fun getAreaMapCacheByStandAndGroup(rr: RobotRuntime): AreaMapCache? {
    val stand = rr.selfReport?.stand ?: return null
    val areaId = stand.areaId
    val area = rr.sr.mapCache.areaById[areaId]
    return area?.groupedMaps?.get(rr.config.groupId)
  }

  /**
   * 在机器人（组）地图里找点位
   */
  fun getPointCacheByRobotAndName(rr: RobotRuntime, pointName: String): MapPointCache? {
    for (ar in areaById.values) {
      val gm = ar.groupedMaps[rr.config.groupId] ?: continue
      val point = gm.pointNameMap[pointName]
      if (point != null) return point
    }
    return null
  }

  /**
   * 根据机器人当前位置获取点位缓存。
   * 如果机器人当前区域为空、点位为空，或获取不到区域、点位信息，返回 null。
   */
  fun getPointCacheByStand(rr: RobotRuntime): MapPointCache? {
    val stand = rr.selfReport?.stand ?: return null
    val areaId = stand.areaId
    val standPointName = stand.pointName ?: return null
    val areaCache = rr.sr.mapCache.areaById[areaId] ?: return null
    return areaCache.mergedMap.pointNameMap[standPointName]
  }

  /**
   * 将一个位置（点位名称、点位别名、库位名称）转换为点位。按机器人组地图。
   */
  fun getPointCacheByGroupAndLoc(rr: RobotRuntime, location: String): MapPointCache? {
    if (location.isBlank()) return null

    for (area in areaById.values) {
      val gm = area.groupedMaps[rr.config.groupId] ?: continue
      val p = locationToPoint(gm, location)
      if (p != null) return p
    }
    return null
  }

  fun mustGetPointCacheByGroupAndLoc(rr: RobotRuntime, location: String): MapPointCache =
    getPointCacheByGroupAndLoc(rr, location) ?: throw BzError("errNoSuchBinOrLocation", location)

  /**
   * 将一个位置（点位名称、点位别名、库位名称）转换为点位。不区分机器人（按合并后的地图）。
   */
  fun getPointCacheByLoc(sr: SceneRuntime, location: String): MapPointCache? {
    val cache = sr.mapCache

    for (ar in cache.areaById.values) {
      val p = locationToPoint(ar.mergedMap, location)
      if (p != null) return p
    }
    return null
  }

  /**
   * 将一个位置（点位名称、点位别名、库位名称）转换为点位。
   */
  private fun locationToPoint(amc: AreaMapCache, location: String): MapPointCache? {
    val point = amc.pointLabelMap[location] ?: amc.pointNameMap[location]
    if (point != null) return point
    val bin = amc.binNameMap[location]
    if (bin != null) {
      if (!bin.bin.workPointName.isNullOrBlank()) {
        return amc.pointNameMap[bin.bin.workPointName]
      }
    }
    return null
  }

  /**
   * 列出一个机器人组地图的所有点位
   */
  fun listPointsOfRobotGroup(rgId: Int): List<MapPointCache> = areaById.values.mapNotNull {
    it.groupedMaps[rgId]?.pointIdMap?.values
  }.flatten()

  /**
   * 列出一个机器人组地图的所有点位，并过滤
   */
  fun listPointsOfRobotGroup(rgId: Int, prediction: (pc: MapPointCache) -> Boolean): List<MapPointCache> =
    areaById.values.mapNotNull {
      it.groupedMaps[rgId]?.pointIdMap?.values?.filter { pc -> prediction(pc) }
    }.flatten()

  /**
   * 列出一个机器人所有的停靠点
   */
  fun listParkAllowedPointsByRobot(rr: RobotRuntime) = listPointsOfRobotGroup(rr.config.groupId) { pc ->
    isParkAllowed(pc)
  }

  /**
   * 列出一个机器人所有的充电点
   */
  fun listChargeAllowedPointsByRobot(rr: RobotRuntime) = listPointsOfRobotGroup(rr.config.groupId) { pc ->
    isChargeAllowed(pc)
  }

  /**
   * 是否是停靠点。按机器人、组、默认取值
   */
  fun isParkAllowed(pc: MapPointCache): Boolean =
    (pc.point.parkAllowed || pc.point.name.startsWith("PP")) && !pc.point.disabled

  /**
   * 是否是停靠点。按机器人、组、默认取值
   */
  fun isChargeAllowed(pc: MapPointCache): Boolean =
    (pc.point.chargeAllowed || pc.point.name.startsWith("CP")) && !pc.point.disabled

  /**
   * 列出某个机器人组所有 SM 点
   */
  fun listSwitchMapPointsByGroup(groupId: Int): List<MapPointCache> =
    listPointsOfRobotGroup(groupId) { pc -> pc.point.name.startsWith("SM") }

  /**
   * 更新整个缓存
   * TODO 更精细的处理
   */
  fun update() {
    rebuildAreas()
    rebuildBinTasks()
  }

  private fun rebuildAreas() {
    // 所有区域所有点
    val pointNames = mutableSetOf<String>()
    val binNames = mutableSetOf<String>()

    areaById.clear()
    groupMaps.clear()

    // 机器人组 -> 组所在的区域
    val groupAreas: MutableMap<Int, MutableSet<Int>> = HashMap()
    // 机器人组 -> smap 地图名 -> 区域
    val groupSmapMapToAreas: MutableMap<Int, MutableMap<String, Int>> = HashMap()

    for (area in sr.areas) {
      // 停用则不加载
      if (area.disabled) continue

      areaById[area.id] = SceneAreaCache(sr.sceneId, area)

      for ((gId, gr) in area.groupsMap) {
        groupAreas.getOrPut(gId) { HashSet() }.add(area.id)
        groupSmapMapToAreas.getOrPut(gId) { HashMap() }[gr.mapName] = area.id
      }

      for (p in area.mergedMap.points) {
        pointNames.add(p.name)
      }
      for (bin in area.mergedMap.bins) {
        binNames.add(bin.name)
      }
    }

    for (rg in sr.robotGroups.values) {
      groupMaps[rg.id] = GroupMapCache(
        groupAreas[rg.id] ?: emptySet(),
        groupSmapMapToAreas[rg.id] ?: emptyMap(),
      )
    }

    this.pointNames = pointNames
    this.binNames = binNames
  }

  /**
   * 所有区域，每个库位上的所有 binTask 名
   */
  private fun rebuildBinTasks() {
    val binTasks = mutableMapOf<String, Set<String>>()
    for (area in sr.areas) {
      // 停用则不加载
      if (area.disabled) continue
      for (bin in area.mergedMap.bins) {
        binTasks[bin.name] = bin.binTaskMap?.keys?.toSet() ?: emptySet()
      }
    }
    this.binTasks = binTasks
  }
}