package com.seer.trick.fleet.traffic.distributed.dispatch

import com.seer.trick.fleet.traffic.distributed.block.BlockItem
import com.seer.trick.fleet.traffic.distributed.block.BlockService
import com.seer.trick.fleet.traffic.distributed.context.domain.RobotContext
import com.seer.trick.fleet.traffic.distributed.deadlock.DeadLockManager
import com.seer.trick.fleet.traffic.distributed.dispatch.strategy.*
import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper
import com.seer.trick.fleet.traffic.distributed.helper.LogParseHelper
import com.seer.trick.fleet.traffic.distributed.lock.LockService
import com.seer.trick.fleet.traffic.distributed.lock.LockType
import com.seer.trick.fleet.traffic.distributed.lock.domain.LockRequest
import com.seer.trick.fleet.traffic.distributed.lock.model.SpaceLock
import com.seer.trick.fleet.traffic.distributed.lock.move.SpaceLockCalculate
import com.seer.trick.fleet.traffic.distributed.map.MapService
import com.seer.trick.fleet.traffic.distributed.plan.model.PathAction
import com.seer.trick.fleet.traffic.distributed.plan.model.PathType
import com.seer.trick.fleet.traffic.distributed.service.DistributedTrafficService
import org.slf4j.LoggerFactory
import java.util.*

/**
 *  调度处理器
 *  用来执行路径计算 和 申请
 * */
object ScheduleProcessor {

  private val logger = LoggerFactory.getLogger(javaClass)

  private val calculateDistance = 4.0

  private val limitLength = 10

  private val strategies: List<Strategy> = listOf(
    PreventStrategy,
    PBSStrategy,
  )

  // 锁闭申请
  fun scheduleTask(context: RobotContext) = processRequest(buildRequest(context))

  /**
   *  构建请求信息
   * */
  private fun buildRequest(context: RobotContext): ScheduleRequest {
    // 构建基础请求信息
    val request = buildRequestData(context)
    // 构建请求信息的空间资源
    buildPathSpaceLock(request)
    // 计算路径资源申请的长度
    calculatePath(request)

    processStrategy(request)

    processJoint(request)
    return request
  }

  // 构建请求信息
  private fun buildRequestData(context: RobotContext): ScheduleRequest {
    val plan = context.plan
    val restPath = plan.restPath()
    // 没有路径的时候
    val scheduleRequest = if (restPath.isEmpty()) {
      logger.warn("${context.robotName} current no rest path")
      ScheduleRequest(
        robotName = context.robotName,
        context = context,
        allocatedIndex = -1,
        currentIndex = -1,
        limitLength = limitLength,
      )
    } else {
      ScheduleRequest(
        robotName = context.robotName,
        context = context,
        allocatedIndex = plan.allocateIndex,
        currentIndex = plan.index,
        limitLength = limitLength,
        reservePath = plan.reservePath,
        requestPath = buildRequestPath(plan.reservePath, plan.queryPreparePath()),
        remainPath = plan.queryPreparePath(),
      )
    }
    // 对特殊区域的记录
    specialArea(scheduleRequest)
    return scheduleRequest
  }

  // 构建请求信息
  private fun buildRequestPath(reservePath: List<PathAction>, remainPath: List<PathAction>): MutableList<PathAction> {
    val requestPath: MutableList<PathAction> = mutableListOf()
    if (reservePath.isEmpty()) {
      remainPath.forEach { requestPath.add(it) }
      return requestPath
    }
    reservePath.forEach { requestPath.add(it) }

    if (remainPath.isEmpty()) {
      return requestPath
    }
    val last = reservePath.last()
    for (i in remainPath.indices) {
      if (last.index < remainPath[i].index) {
        requestPath.add(remainPath[i])
      }
    }
    return requestPath
  }

  // 特殊区域的申请和释放
  private fun specialArea(request: ScheduleRequest): ScheduleRequest {
    val areas = request.context.plan.areas
    if (areas.isNotEmpty()) {
      for (area in areas) {
        when (area.type) {
          AreaType.AVOID -> {
            processAvoidArea(request, area)
          }
          else -> {
          }
        }
      }
    } else {
      checkPassArea(request)
    }
    request.specialAreas = request.context.plan.areas
    return request
  }

  private fun checkPassArea(request: ScheduleRequest) {
    val path = request.requestPath
    var startIndex = -1L
    var targetIndex = -1L
    var pointNames: MutableList<String> = mutableListOf()
    val areas: MutableList<SpecialArea> = mutableListOf()
    for (i in 0 until path.size) {
      val p = path[i]
      val s = MapService.findPointInAvoidArea(request.context.sceneId, request.context.mapName, p.start.pointName)
      val t = MapService.findPointInAvoidArea(request.context.sceneId, request.context.mapName, p.target.pointName)
      if (s && t && startIndex == -1L) {
        startIndex = p.index
      }
      if (s && t) {
        targetIndex = p.index
      }
      if (s && t) {
        if (!pointNames.contains(p.start.pointName)) {
          pointNames.add(p.start.pointName)
        }
        if (!pointNames.contains(p.target.pointName)) {
          pointNames.add(p.target.pointName)
        }
      }
      if (startIndex != -1L && targetIndex != -1L && (!t || i == path.size - 1)) {
        val specialArea = SpecialArea(
          type = AreaType.AVOID,
          startIndex = startIndex,
          targetIndex = targetIndex,
          points = pointNames,
        )
        areas.add(specialArea)
        logger.info("add special area: $specialArea")
        startIndex = -1L
        targetIndex = -1L
        pointNames = mutableListOf()
      }
    }
    request.context.plan.areas.addAll(areas)
  }

  private fun processAvoidArea(request: ScheduleRequest, area: SpecialArea) {
    // TODO 当前索引可能会存在问题
    val curPoint = request.context.baseDomain.curPoint
    val currentAction = request.context.plan.queryCurrentAction()
    if (area.startIndex <= request.currentIndex &&
      request.currentIndex <= area.targetIndex &&
      curPoint != null &&
      currentAction != null &&
      curPoint.pointName != currentAction.target.pointName
    ) {
      val key: String =
        LockType.ROBOT.toString() + "-><-" + request.context.groupName + "-><-" + request.context.robotName
      LockService.removeSpaceLockByKey(key, request.context.sceneId, request.context.mapName)
    } else if (request.currentIndex == area.targetIndex &&
      curPoint != null &&
      currentAction != null &&
      curPoint.pointName == currentAction.target.pointName &&
      (
        curPoint.distance(currentAction.target) < 0.2 ||
          (System.currentTimeMillis() - request.context.pauseTime) > 1000 * 20
        )
    ) {
      request.context.plan.removeArea(area)
      return
    }
    val pathActions = request.requestPath.filter { it.index <= area.targetIndex }.toMutableList()
    request.requestPath = pathActions
  }

  // 路径申请前的处理
  private fun buildPathSpaceLock(request: ScheduleRequest): ScheduleRequest {
    // 取所有的路径
    val totalPath = request.requestPath
    val context = request.context
    val areas = request.specialAreas
    totalPath.forEach { path ->
      if (checkPathInAvoidArea(areas, path)) {
        return@forEach
      }
      if (path.runLock == null) {
        path.startLock = SpaceLockCalculate.robotAndContainerStaticSpaceLock(
          robotName = context.robotName,
          sceneId = context.sceneId,
          groupName = context.groupName,
          robotHeading = path.robotInHeading,
          point = path.start,
          mapName = path.mapName,
          container = path.containerName,
          containerHeading = path.containerInHeading,
        )
        path.runLock = SpaceLockCalculate.moveSpaceLock(path)
        path.targetLock = SpaceLockCalculate.robotAndContainerStaticSpaceLock(
          robotName = context.robotName,
          sceneId = context.sceneId,
          groupName = context.groupName,
          robotHeading = path.robotOutHeading,
          point = path.target,
          mapName = path.mapName,
          container = path.containerName,
          containerHeading = path.containerOutHeading,
        )
      }
    }
    return request
  }

  private fun checkPathInAvoidArea(areas: MutableList<SpecialArea>, path: PathAction): Boolean {
    for (area in areas) {
      if (area.type == AreaType.AVOID && path.index >= area.startIndex && path.index <= area.targetIndex) {
        return true
      }
    }
    return false
  }

  // 对路径进行计算
  private fun calculatePath(request: ScheduleRequest): ScheduleRequest {
    // 取需要申请的路径
    val requestPath = request.requestPath
    val allocatedIndex = request.allocatedIndex
    //  旋转时需要截断
    //  折返时需要截断
    val path: MutableList<PathAction> = LinkedList()
    val avoidAreas = request.specialAreas.filter { it.type == AreaType.AVOID }
    var distance: Double = 0.0
    for (i in 0 until requestPath.size - 1) {
      val start = requestPath[i]
      if (startPointInAvoidArea(avoidAreas, start)) {
        break
      }
      val next = requestPath[i + 1]
      path.add(start)
      // 检测是否需要旋转
      if (start.isRotate() && i != 0 && start.index >= allocatedIndex) {
        break
      }
      if (next.isRotate() && next.index >= allocatedIndex) {
        path.add(next)
        break
      }
      if (next.type == PathType.STOP) {
        path.add(next)
        break
      }
      // 检测折返
      val curPoint = request.context.baseDomain.curPoint
      if (start.isMove() &&
        next.isMove() &&
        AngleHelper.opposeAngle(
          MapService
            .findLineByName(request.context.sceneId, start.mapName, request.context.groupName, start.lineName).outDir,
          MapService
            .findLineByName(request.context.sceneId, next.mapName, request.context.groupName, next.lineName).enterDir,
        ) &&
        curPoint != null &&
        curPoint.distance(start.target) >= 0.05 &&
        start.index >= allocatedIndex &&
        (System.currentTimeMillis() - request.context.pauseTime) <= 1000 * 60
      ) {
        logger.debug("${request.robotName} detect in ${start.target.pointName} -- ${next.target.pointName}")
        break
      }
      if (distance > calculateDistance && start.index >= allocatedIndex) {
        break
      }
      distance += start.distance()
    }
    val index = if (path.isNotEmpty() && path.last().index < allocatedIndex) {
      path.last().index
    } else {
      -1
    }
    if (index != -1L || path.isEmpty()) {
      requestPath.forEach {
        if (it.index in (index + 1)..allocatedIndex) {
          path.add(it)
        }
      }
    }
    // 为原点任务
    if (path.isEmpty() && requestPath.size == 1 && !startPointInAvoidArea(avoidAreas, requestPath[0])) {
      path.add(requestPath[0])
    }
    logger.info(
      "${request.robotName} current index ${request.currentIndex} | allocate index $allocatedIndex " +
        "|prepare request is ${LogParseHelper.parsePathLog(path)}",
    )
    request.requestPath = path
    return request
  }

  private fun startPointInAvoidArea(avoidAreas: List<SpecialArea>, start: PathAction): Boolean {
    if (avoidAreas.isEmpty()) return false
    for (area in avoidAreas) {
      if (start.index >= area.startIndex && start.index <= area.targetIndex) {
        return true
      }
    }
    return false
  }

  private fun processJoint(request: ScheduleRequest) {
    if (request.requestPath.isEmpty()) return
    val path = request.requestPath
    val action = path.last()
    if (!action.target.junction) return

    val remainPath = request.remainPath
    for (p in remainPath) {
      if (p.index <= action.index) continue
      path.add(p)
      if (!p.target.junction) break
    }
    request.requestPath = path
    logger.info("joint prepare request is ${LogParseHelper.parsePathLog(path)}")
  }

  /**
   *  路径计算的 策略处理
   * */
  private fun processStrategy(request: ScheduleRequest) {
    var st: Strategy? = null
    for (strategy in strategies) {
      if (strategy.check(request.context)) {
        st = strategy
      }
    }
    st?.handle(request)
  }

  // 用来申请资源信息，并处理下发结果
  private fun processRequest(request: ScheduleRequest) {
    val response = applySpaceLock(request)
    // 判断是否有新路径需要申请

    val pushPath = calculatePushPath(request, response)

    // 下发路径,简单处理
    if (pushPath.isNotEmpty() && DistributedTrafficService.pushPath(request.context, pushPath)) {
      val robotContext = request.context
      robotContext.deadLock.deadLockMessageClear()
      robotContext.plan.reservePath = response.resultPath
      robotContext.plan.allocateIndex = pushPath.last().index
      robotContext.pauseTime = System.currentTimeMillis()
    }

    processDeadLock(
      request.context,
      processBlockItems(response, request.blockRobot, request.version ?: System.currentTimeMillis().toString()),
    )
  }

  /**
   *  申请 锁闭信息
   * */
  private fun applySpaceLock(request: ScheduleRequest): ScheduleResponse {
    val totalPath = request.requestPath
    val robotName = request.robotName
    val mapName = request.context.mapName
    if (totalPath.isEmpty()) {
      val actions = mutableListOf<PathAction>()
      addAvoidAreaPath(request, actions)
      actions
      return ScheduleResponse(robotName, resultPath = actions)
    }
    val lockSpaces: MutableList<SpaceLock> = mutableListOf()
    for (action in totalPath) {
      val spaceLock = action.runLock
      if (spaceLock != null) {
        lockSpaces.add(spaceLock)
      }
    }
    if (lockSpaces.isEmpty()) {
      return ScheduleResponse(robotName, resultPath = totalPath)
    }
    val lockResponse = LockService
      .lockHandle(
        LockRequest(id = robotName, mapName = mapName, sceneId = request.context.sceneId, spaces = lockSpaces),
      )
    if (!lockResponse.success) {
      logger.error("lockHandle fail $lockResponse")
    }
    return ScheduleResponse(
      robotName = robotName,
      resultPath = if (lockResponse.success) {
        val spaces = lockResponse.spaces
        val actions = if (spaces.isNotEmpty()) {
          totalPath.subList(0, spaces.size)
        } else {
          mutableListOf<PathAction>()
        }
        addAvoidAreaPath(request, actions)
        actions
      } else {
        mutableListOf()
      },
      blocks = lockResponse.blocks,
    )
  }

  /**
   *  机选需要 下发的路径
   * */
  private fun calculatePushPath(request: ScheduleRequest, response: ScheduleResponse): MutableList<PathAction> {
    val allocatedIndex = request.allocatedIndex
    val resultPath = response.resultPath
    val requestPath: MutableList<PathAction> = LinkedList()
    for (path in resultPath) {
      if (path.index <= allocatedIndex) {
        continue
      }
      requestPath.add(path)
    }
    return requestPath
  }

  private fun addAvoidAreaPath(request: ScheduleRequest, requestPath: MutableList<PathAction>) {
    val avoidAreas = request.specialAreas.filter { it.type == AreaType.AVOID }
    for (avoidArea in avoidAreas) {
      if (requestPath.isEmpty() &&
        request.requestPath.isEmpty() &&
        avoidArea.startIndex == 0L &&
        request.currentIndex == -1L
      ) {
        request.remainPath.forEach {
          if (it.index <= avoidArea.targetIndex) {
            requestPath.add(it)
          }
        }
      } else if (requestPath.isNotEmpty() && avoidArea.startIndex == (requestPath.last().index + 1)) {
        request.remainPath.forEach {
          if (it.index >= avoidArea.startIndex && it.index <= avoidArea.targetIndex) {
            requestPath.add(it)
          }
        }
        break
      }
    }
  }

  // 调用死锁检查模块检测死锁
  private fun processDeadLock(context: RobotContext?, blocks: List<BlockItem>) {
    if (blocks.isEmpty() || context == null) return
    if (System.currentTimeMillis() - context.pauseTime > 5 * 1000) {
      // 通知解死锁进行检测处理是否发生死锁
      DeadLockManager.checkDeadLock(context.robotName)
    }
  }

  // 处理障碍物信息
  private fun processBlockItems(
    response: ScheduleResponse,
    blockRobot: List<String>,
    version: String,
  ): List<BlockItem> {
    val blocks = response.blocks
    val robotName = response.robotName
//    val blockItems = traffic.blockService.findBlock(robotName)
    // 构建障碍物信息
    val blockItems: MutableList<BlockItem> = mutableListOf()
    blockItems.addAll(blocks)
    val list = blocks.map(BlockItem::code).toList()
    val items =
      blockRobot.filter { !list.contains(it) }
        .map { BlockItem(code = it, type = LockType.ROBOT_WAIT, point = null, version = version) }
        .toList()
    blockItems.addAll(items)
    logger.debug("$robotName current block robot is ${blockItems.map { it.code }.toList()}")
    BlockService.updateBlock(robotName, blockItems)
    return blockItems
  }
}