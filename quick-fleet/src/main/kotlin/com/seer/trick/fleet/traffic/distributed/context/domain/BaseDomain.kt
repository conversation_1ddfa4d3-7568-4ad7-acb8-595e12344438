package com.seer.trick.fleet.traffic.distributed.context.domain

import com.seer.trick.fleet.traffic.distributed.helper.AngleHelper
import com.seer.trick.fleet.traffic.distributed.map.Position

class BaseDomain {

  @Volatile
  var status: RobotStatus = RobotStatus.IDLE // 机器人状态信息信息

  @Volatile
  var robotHeading: Int = AngleHelper.ERROR_ANGLE // 机器人当前角度

  @Volatile
  var containerType: String? = null // 带载的类型

  @Volatile
  var containerHeading: Int = AngleHelper.ERROR_ANGLE // 带载角度

  @Volatile
  var curPoint: Position? = null // 机器人当前所在的码点信息

  @Volatile
  var length = 0.0 // 机器人的长度

  // 判断是否带载
  fun isLoading(): Boolean = containerType != null
}