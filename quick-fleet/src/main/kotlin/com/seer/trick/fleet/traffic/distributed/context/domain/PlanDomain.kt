package com.seer.trick.fleet.traffic.distributed.context.domain

import com.seer.trick.fleet.traffic.distributed.dispatch.SpecialArea
import com.seer.trick.fleet.traffic.distributed.plan.model.PathAction
import com.seer.trick.fleet.traffic.distributed.prevent.PreventResponse
import java.util.*
import java.util.concurrent.CopyOnWriteArrayList

class PlanDomain {

  var path: MutableList<PathAction> = LinkedList() // 路径

  @Volatile
  var index: Long = -1 // 当前索引

  @Volatile
  var allocateIndex: Long = -1 // 分配索引

  @Volatile
  var reservePath: MutableList<PathAction> = LinkedList() // 持有的路径信息

  @Volatile
  var areas: MutableList<SpecialArea> = CopyOnWriteArrayList()

  @Volatile
  var prevent: PreventResponse? = null // 预防信息

  @Volatile
  var preventTime: Long = 0

  fun clear() {
    path = LinkedList()
    index = -1
    allocateIndex = -1
    reservePath = LinkedList()
    prevent = null
    preventTime = 0
    areas = CopyOnWriteArrayList()
  }

  // 查询剩余的路径
  fun restPath(): MutableList<PathAction> {
    if (index.toInt() == -1 && path.size > 0) {
      return path
    }
    if (index >= 0 && index < path.size) {
      return path.subList(index.toInt(), path.size)
    }
    return LinkedList()
  }

  fun queryPreparePath(): MutableList<PathAction> {
    if (allocateIndex.toInt() == -1 && path.size > 0) {
      return path
    }
    if (allocateIndex >= 0 && allocateIndex < path.size) {
      return path.subList(allocateIndex.toInt(), path.size)
    }
    return LinkedList()
  }

  fun queryNextAction(): PathAction? {
    if (index < path.size - 1) {
      return path[index.toInt() + 1]
    }
    return null
  }

  fun queryCurrentAction(): PathAction? {
    if (index >= 0 && index < path.size) {
      return path[index.toInt()]
    }
    return null
  }

  fun queryActionByIndex(pathIndex: Long): PathAction? {
    if (index >= 0 && index < path.size) {
      return path[pathIndex.toInt()]
    }
    return null
  }

  fun removeArea(area: SpecialArea): Boolean = areas.remove(area)

  fun queryPreventIndex(): Long? {
    val p = prevent ?: return null
    val pathAction = p.reserveTable[p.index]
    return pathAction?.index
  }
}