package com.seer.trick.fleet.falcon.bp

import com.seer.trick.BzError
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.falcon.domain.*
import com.seer.trick.fleet.order.OrderFaultService
import com.seer.trick.fleet.order.OrderService
import com.seer.trick.fleet.service.SceneService

/**
 * 等待运单步骤完成
 */
class WaitStepCompleteBp : AbstractWaitBp() {

  override fun process() {
    val stepId = mustGetBlockInputParam("stepId") as String
    val orderId = EntityRwService.findOneById("TransportStep", stepId)?.get("orderId")
      ?: throw BzError("errNoOrderStepById", stepId)
    val sceneId = OrderService.mustGetSceneIdByOrderId(orderId as String)
    val sr = SceneService.mustGetSceneById(sceneId)
    val or = sr.mustGetOrderById(orderId)

    if (or.order.stepFixed) throw BzError("errAddStepWhenSealed", orderId)

    val oldStepId = getInternalVariable("$orderId-stepId")
    // 故障的步骤一定是当前的步骤？
    val currentStep = or.steps.find { it.id == oldStepId && it.stepIndex == or.order.currentStepIndex }
    // 若已存在则是故障重试
    if (currentStep != null) {
      OrderFaultService.retryByOrder(sr, orderId)
    }
    // 设置内部变量
    getOrSetBlockInternalVariable("$orderId-stepId") { stepId }
    // 等待运单步骤完成
    waitStepComplete(sr, or, stepId)

    val robotName = or.order.actualRobotName
    if (!robotName.isNullOrBlank()) addActualVehicle(robotName)
    setBlockOutputParams(mapOf("robotName" to robotName))
  }

  companion object {
    val def = BlockDef(
      WaitStepCompleteBp::class.simpleName!!,
      color = "#C7DCA7",
      inputParams = listOf(
        BlockInputParamDef(
          "stepId",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.StepId),
        ),
      ),
      outputParams = listOf(
        BlockOutputParamDef(
          "robotName",
          BlockParamType.String,
          objectTypes = listOf(ParamObjectType.RobotName),
        ),
      ),
    )
  }
}