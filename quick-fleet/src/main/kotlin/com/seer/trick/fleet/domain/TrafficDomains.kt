package com.seer.trick.fleet.domain

enum class PositionType {
  Point, // 点
  Path, // 线
}

/**
 * 表示一个交管任务的起点
 */
data class TrafficTaskSource(
  val areaId: Int,
  val areaName: String, // 方便调试
  val x: Double,
  val y: Double,
  val theta: Double, // 机器人起始角度
  val type: PositionType,
  val pointName: String? = null,
  val pathPositions: List<PathPosition>? = null, // 路径上的位置，可能有多种估测
)

/**
 * 表示一个交管任务的子任务：目标点和动作
 */
data class TrafficTaskTarget(
  val areaId: Int, // 目标点
  val areaName: String, // 方便调试
  val x: Double, // 区域 id
  val y: Double, // 动作参数
  val pointName: String, // 机器人终点位置角度要求，如果为空则没有约束
  val rbkArgs: String? = null, // 目标点 x
  val theta: Double? = null, // 目标点 y
  val containerId: String? = null, // 容器编码
  val containerStartAngle: Double? = null, // 调用路径规划时容器角度 TODO TV 放起点上
  val containerTargetAngle: Double? = null, // 终点容器角度
)

data class RobotShowTrafficMessage(
  val pathResource: PathResource? = null, // 机器人路径
  val spaceResources: List<SpaceResource>? = null, // 空间资源信息
  val blockedMessage: BlockedMessage? = null, // 阻挡信息
)

data class PathResource(
  val travelledPointNames: List<String>? = null, // 已经走过的路径
  val unTravelPointNames: List<String>? = null, // 未来将要走的路径
)

/**
 * 坐标系为全局坐标系
 */
data class SpaceResource(
  val type: SpaceResourceType, // 类型
  val points: List<Point2D>, // 顶点坐标 , 如果是圆，为圆心坐标
  val radius: Double? = null, // 圆的半径
)

data class BlockedMessage(
  val robotBlocked: List<String>, // 被机器人阻挡
  val containerBlocked: List<String>, // 被货架阻挡， 预留
)

enum class SpaceResourceType {
  Rect, // 矩形
  Circle, // 圆形
  Polygon, // 多边形
}

data class PlanResult(
  val success: Boolean, // 是否规划成功
  val code: String? = null, // 错误码
  val args: List<String>? = null, // 参数信息
)