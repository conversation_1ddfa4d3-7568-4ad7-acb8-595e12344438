package com.seer.trick.fleet.traffic.distributed.deadlock.replan

import com.seer.trick.fleet.traffic.distributed.block.BlockItem
import com.seer.trick.fleet.traffic.distributed.context.ContextManagerService
import com.seer.trick.fleet.traffic.distributed.context.domain.RobotContext
import com.seer.trick.fleet.traffic.distributed.context.domain.RobotStatus
import com.seer.trick.fleet.traffic.distributed.deadlock.DeadLockProcessor
import com.seer.trick.fleet.traffic.distributed.deadlock.helper.DeadLockPathPlanHelper
import com.seer.trick.fleet.traffic.distributed.deadlock.helper.NeighborSearchService
import com.seer.trick.fleet.traffic.distributed.deadlock.model.DeadLockMessage
import com.seer.trick.fleet.traffic.distributed.deadlock.model.NeighborDomain
import com.seer.trick.fleet.traffic.distributed.helper.PathInfoHelper
import com.seer.trick.fleet.traffic.distributed.lock.LockType
import com.seer.trick.fleet.traffic.distributed.plan.model.PathAction
import org.slf4j.LoggerFactory

/**
 *  固定障碍物死锁处理
 * */
object ReplanDeadLockProcessor : DeadLockProcessor {

  private val logger = LoggerFactory.getLogger(javaClass)

  override fun check(robotName: String, block: MutableList<BlockItem>): DeadLockMessage {
    val context = ContextManagerService.queryRobotContext(robotName)

    val message = DeadLockMessage(robotName = robotName, mapName = context.mapName, context = context)
    if (System.currentTimeMillis() - context.pauseTime <= 10 * 1000) {
      logger.info("$robotName |robot is not stop")
      return message
    }
    if (context.deadLock.rePlanTime > 0 &&
      System.currentTimeMillis() - context.deadLock.rePlanTime < 2 * 1000
    ) {
      logger.info(
        "$robotName  unlocking the deadlock too often. Wait a moment " +
          "${2 * 1000 - (System.currentTimeMillis() - context.deadLock.rePlanTime)}",
      )
      return message
    }
    for (b in block) {
      if (b.type == LockType.RACK) {
        message.state = true
        message.fixedBlocks.add(b)
      }
      if (b.type == LockType.ROBOT) {
        val blockContext = ContextManagerService.queryRobotContext(b.code)

        if (blockContext.baseDomain.status == RobotStatus.ERROR ||
          blockContext.baseDomain.status == RobotStatus.OFFLINE
        ) {
          message.state = true
          message.fixedBlocks.add(b)
        }
      }
    }
    return message
  }

  override fun handle(message: DeadLockMessage): Boolean {
    val fixedBlocks = message.fixedBlocks
    val points = if (fixedBlocks.isEmpty()) {
      mutableListOf()
    } else {
      fixedBlocks.mapNotNull { blockItem -> blockItem.point?.pointName }.toMutableList()
    }
    val context = message.context

    context.deadLock.rePlanTime = System.currentTimeMillis()
    val neighborDomain = NeighborSearchService.process(context, message.neighborDomains)
    if (neighborDomain.canPass.isNotEmpty()) {
      //  重规划 寻找路径
      val targetPath = replanHandler(context, points, neighborDomain)
      if (targetPath != null) {
        val path = PathInfoHelper.joinPath(context.plan.path, targetPath, context.plan.allocateIndex)
        ContextManagerService.updatePath(context.robotName, path)
        return true
      }
    }
    if (neighborDomain.backPass != null) {
      val targetPath = backPlanHandler(context, points, neighborDomain)
      if (targetPath != null) {
        val path = PathInfoHelper.joinPath(context.plan.path, targetPath, context.plan.allocateIndex)
        ContextManagerService.updatePath(context.robotName, path)
        return true
      }
    }
    return false
  }

  private fun backPlanHandler(
    context: RobotContext,
    points: MutableList<String>,
    neighborDomain: NeighborDomain,
  ): MutableList<PathAction>? {
    var forbiddenPoints: MutableList<String> = mutableListOf()
    forbiddenPoints.addAll(points)
    forbiddenPoints.addAll(neighborDomain.cantPass)
    forbiddenPoints = forbiddenPoints.distinct().toMutableList()
    return DeadLockPathPlanHelper
      .toTargetPath(context, neighborDomain.cantRotate, forbiddenPoints, neighborDomain.backPass)
  }

  private fun replanHandler(
    context: RobotContext,
    points: MutableList<String>,
    neighborDomain: NeighborDomain,
  ): MutableList<PathAction>? {
    var forbiddenPoints: MutableList<String> = mutableListOf()
    forbiddenPoints.addAll(points)
    forbiddenPoints.addAll(neighborDomain.cantPass)
    forbiddenPoints = forbiddenPoints.distinct().toMutableList()
    return DeadLockPathPlanHelper.toTargetPath(context, neighborDomain.cantRotate, forbiddenPoints, null)
  }
}