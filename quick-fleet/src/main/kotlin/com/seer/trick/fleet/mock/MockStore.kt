package com.seer.trick.fleet.mock

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.file.FileManager
import com.seer.trick.fleet.domain.RbkModel
import com.seer.trick.helper.JsonFileHelper
import com.seer.trick.helper.JsonHelper
import org.slf4j.LoggerFactory
import java.io.File

/**
 * 仿真模块数据的存储、读写
 */
object MockStore {

  private val logger = LoggerFactory.getLogger(this::class.java)

  // TODO 要不要将默认的模型文件（default-model.json）放到 resource 中？
  private val defaultModel: RbkModel = JsonHelper.mapper.readValue(
    "{\"model\":\"dri\",\"deviceTypes\":[{\"ord\":0,\"name\":\"DI\",\"desc\":\"DI\",\"maxCount\":81,\"devices\":[{\"name\":\"DI0\",\"desc\":\"REk=\",\"isDisplay\":false,\"isEnabled\":true,\"deviceParams\":[{\"key\":\"basic\",\"desc\":\"基础参数\",\"type\":\"arrayParam\",\"arrayParam\":{\"params\":[{\"key\":\"id\",\"type\":\"uint32\",\"unit\":\"\",\"desc\":\"H1.2.10以前:\\\\nNormalid范围：0-8\\\\nVirtualid范围：9-16\\\\nModbusid范围：17-80\\\\n\\\\nH1.2.10以前(含curtis扩展IO):\\\\nNormalid范围：0-16\\\\nVirtualid范围：17-24\\\\nModbusid范围：25-80\\\\n\\\\nH1.2.10及以后:\\\\nNormalid范围：0-10\\\\nVirtualid范围：11-18\\\\nModbusid范围：19-80\\\\n\\\\nH1.2.10及以后(含curtis扩展IO):\\\\nNormalid范围：0-18\\\\nVirtualid范围：19-26\\\\nModbusid范围：27-80\",\"cloneEnable\":false,\"uint32Value\":0,\"uint32Maxvalue\":80,\"uint32Minvalue\":0},{\"key\":\"inverse\",\"type\":\"bool\",\"unit\":\"\",\"desc\":\"反向电平触发(虚拟DI和curtisk扩展DI除外)\",\"cloneEnable\":false,\"boolValue\":false}]},\"cloneEnable\":false}]},{\"name\":\"DI1\",\"desc\":\"REk=\",\"isDisplay\":false,\"isEnabled\":true,\"deviceParams\":[{\"key\":\"basic\",\"desc\":\"基础参数\",\"type\":\"arrayParam\",\"arrayParam\":{\"params\":[{\"key\":\"id\",\"type\":\"uint32\",\"unit\":\"\",\"desc\":\"H1.2.10以前:\\\\nNormalid范围：0-8\\\\nVirtualid范围：9-16\\\\nModbusid范围：17-80\\\\n\\\\nH1.2.10以前(含curtis扩展IO):\\\\nNormalid范围：0-16\\\\nVirtualid范围：17-24\\\\nModbusid范围：25-80\\\\n\\\\nH1.2.10及以后:\\\\nNormalid范围：0-10\\\\nVirtualid范围：11-18\\\\nModbusid范围：19-80\\\\n\\\\nH1.2.10及以后(含curtis扩展IO):\\\\nNormalid范围：0-18\\\\nVirtualid范围：19-26\\\\nModbusid范围：27-80\",\"cloneEnable\":false,\"uint32Value\":1,\"uint32Maxvalue\":80,\"uint32Minvalue\":0},{\"key\":\"inverse\",\"type\":\"bool\",\"unit\":\"\",\"desc\":\"反向电平触发(虚拟DI和curtisk扩展DI除外)\",\"cloneEnable\":false,\"boolValue\":false}]},\"cloneEnable\":false}]},{\"name\":\"DI2\",\"desc\":\"REk=\",\"isDisplay\":false,\"isEnabled\":true,\"deviceParams\":[{\"key\":\"basic\",\"desc\":\"基础参数\",\"type\":\"arrayParam\",\"arrayParam\":{\"params\":[{\"key\":\"id\",\"type\":\"uint32\",\"unit\":\"\",\"desc\":\"H1.2.10以前:\\\\nNormalid范围：0-8\\\\nVirtualid范围：9-16\\\\nModbusid范围：17-80\\\\n\\\\nH1.2.10以前(含curtis扩展IO):\\\\nNormalid范围：0-16\\\\nVirtualid范围：17-24\\\\nModbusid范围：25-80\\\\n\\\\nH1.2.10及以后:\\\\nNormalid范围：0-10\\\\nVirtualid范围：11-18\\\\nModbusid范围：19-80\\\\n\\\\nH1.2.10及以后(含curtis扩展IO):\\\\nNormalid范围：0-18\\\\nVirtualid范围：19-26\\\\nModbusid范围：27-80\",\"cloneEnable\":false,\"uint32Value\":2,\"uint32Maxvalue\":80,\"uint32Minvalue\":0},{\"key\":\"inverse\",\"type\":\"bool\",\"unit\":\"\",\"desc\":\"反向电平触发(虚拟DI和curtisk扩展DI除外)\",\"cloneEnable\":false,\"boolValue\":false}]},\"cloneEnable\":false}]},{\"name\":\"DI3\",\"desc\":\"REk=\",\"isDisplay\":false,\"isEnabled\":true,\"deviceParams\":[{\"key\":\"basic\",\"desc\":\"基础参数\",\"type\":\"arrayParam\",\"arrayParam\":{\"params\":[{\"key\":\"id\",\"type\":\"uint32\",\"unit\":\"\",\"desc\":\"H1.2.10以前:\\\\nNormalid范围：0-8\\\\nVirtualid范围：9-16\\\\nModbusid范围：17-80\\\\n\\\\nH1.2.10以前(含curtis扩展IO):\\\\nNormalid范围：0-16\\\\nVirtualid范围：17-24\\\\nModbusid范围：25-80\\\\n\\\\nH1.2.10及以后:\\\\nNormalid范围：0-10\\\\nVirtualid范围：11-18\\\\nModbusid范围：19-80\\\\n\\\\nH1.2.10及以后(含curtis扩展IO):\\\\nNormalid范围：0-18\\\\nVirtualid范围：19-26\\\\nModbusid范围：27-80\",\"cloneEnable\":false,\"uint32Value\":3,\"uint32Maxvalue\":80,\"uint32Minvalue\":0},{\"key\":\"inverse\",\"type\":\"bool\",\"unit\":\"\",\"desc\":\"反向电平触发(虚拟DI和curtisk扩展DI除外)\",\"cloneEnable\":false,\"boolValue\":false}]},\"cloneEnable\":false}]},{\"name\":\"DI4\",\"desc\":\"REk=\",\"isDisplay\":false,\"isEnabled\":true,\"deviceParams\":[{\"key\":\"basic\",\"desc\":\"基础参数\",\"type\":\"arrayParam\",\"arrayParam\":{\"params\":[{\"key\":\"id\",\"type\":\"uint32\",\"unit\":\"\",\"desc\":\"H1.2.10以前:\\\\nNormalid范围：0-8\\\\nVirtualid范围：9-16\\\\nModbusid范围：17-80\\\\n\\\\nH1.2.10以前(含curtis扩展IO):\\\\nNormalid范围：0-16\\\\nVirtualid范围：17-24\\\\nModbusid范围：25-80\\\\n\\\\nH1.2.10及以后:\\\\nNormalid范围：0-10\\\\nVirtualid范围：11-18\\\\nModbusid范围：19-80\\\\n\\\\nH1.2.10及以后(含curtis扩展IO):\\\\nNormalid范围：0-18\\\\nVirtualid范围：19-26\\\\nModbusid范围：27-80\",\"cloneEnable\":false,\"uint32Value\":4,\"uint32Maxvalue\":80,\"uint32Minvalue\":0},{\"key\":\"inverse\",\"type\":\"bool\",\"unit\":\"\",\"desc\":\"反向电平触发(虚拟DI和curtisk扩展DI除外)\",\"cloneEnable\":false,\"boolValue\":false}]},\"cloneEnable\":false}]},{\"name\":\"DI5\",\"desc\":\"REk=\",\"isDisplay\":false,\"isEnabled\":true,\"deviceParams\":[{\"key\":\"basic\",\"desc\":\"基础参数\",\"type\":\"arrayParam\",\"arrayParam\":{\"params\":[{\"key\":\"id\",\"type\":\"uint32\",\"unit\":\"\",\"desc\":\"H1.2.10以前:\\\\nNormalid范围：0-8\\\\nVirtualid范围：9-16\\\\nModbusid范围：17-80\\\\n\\\\nH1.2.10以前(含curtis扩展IO):\\\\nNormalid范围：0-16\\\\nVirtualid范围：17-24\\\\nModbusid范围：25-80\\\\n\\\\nH1.2.10及以后:\\\\nNormalid范围：0-10\\\\nVirtualid范围：11-18\\\\nModbusid范围：19-80\\\\n\\\\nH1.2.10及以后(含curtis扩展IO):\\\\nNormalid范围：0-18\\\\nVirtualid范围：19-26\\\\nModbusid范围：27-80\",\"cloneEnable\":false,\"uint32Value\":5,\"uint32Maxvalue\":80,\"uint32Minvalue\":0},{\"key\":\"inverse\",\"type\":\"bool\",\"unit\":\"\",\"desc\":\"反向电平触发(虚拟DI和curtisk扩展DI除外)\",\"cloneEnable\":false,\"boolValue\":false}]},\"cloneEnable\":false}]},{\"name\":\"DI6\",\"desc\":\"REk=\",\"isDisplay\":false,\"isEnabled\":true,\"deviceParams\":[{\"key\":\"basic\",\"desc\":\"基础参数\",\"type\":\"arrayParam\",\"arrayParam\":{\"params\":[{\"key\":\"id\",\"type\":\"uint32\",\"unit\":\"\",\"desc\":\"H1.2.10以前:\\\\nNormalid范围：0-8\\\\nVirtualid范围：9-16\\\\nModbusid范围：17-80\\\\n\\\\nH1.2.10以前(含curtis扩展IO):\\\\nNormalid范围：0-16\\\\nVirtualid范围：17-24\\\\nModbusid范围：25-80\\\\n\\\\nH1.2.10及以后:\\\\nNormalid范围：0-10\\\\nVirtualid范围：11-18\\\\nModbusid范围：19-80\\\\n\\\\nH1.2.10及以后(含curtis扩展IO):\\\\nNormalid范围：0-18\\\\nVirtualid范围：19-26\\\\nModbusid范围：27-80\",\"cloneEnable\":false,\"uint32Value\":6,\"uint32Maxvalue\":80,\"uint32Minvalue\":0},{\"key\":\"inverse\",\"type\":\"bool\",\"unit\":\"\",\"desc\":\"反向电平触发(虚拟DI和curtisk扩展DI除外)\",\"cloneEnable\":false,\"boolValue\":false}]},\"cloneEnable\":false}]},{\"name\":\"DI7\",\"desc\":\"REk=\",\"isDisplay\":false,\"isEnabled\":true,\"deviceParams\":[{\"key\":\"basic\",\"desc\":\"基础参数\",\"type\":\"arrayParam\",\"arrayParam\":{\"params\":[{\"key\":\"id\",\"type\":\"uint32\",\"unit\":\"\",\"desc\":\"H1.2.10以前:\\\\nNormalid范围：0-8\\\\nVirtualid范围：9-16\\\\nModbusid范围：17-80\\\\n\\\\nH1.2.10以前(含curtis扩展IO):\\\\nNormalid范围：0-16\\\\nVirtualid范围：17-24\\\\nModbusid范围：25-80\\\\n\\\\nH1.2.10及以后:\\\\nNormalid范围：0-10\\\\nVirtualid范围：11-18\\\\nModbusid范围：19-80\\\\n\\\\nH1.2.10及以后(含curtis扩展IO):\\\\nNormalid范围：0-18\\\\nVirtualid范围：19-26\\\\nModbusid范围：27-80\",\"cloneEnable\":false,\"uint32Value\":7,\"uint32Maxvalue\":80,\"uint32Minvalue\":0},{\"key\":\"inverse\",\"type\":\"bool\",\"unit\":\"\",\"desc\":\"反向电平触发(虚拟DI和curtisk扩展DI除外)\",\"cloneEnable\":false,\"boolValue\":false}]},\"cloneEnable\":false}]},{\"name\":\"DI8\",\"desc\":\"REk=\",\"isDisplay\":false,\"isEnabled\":true,\"deviceParams\":[{\"key\":\"basic\",\"desc\":\"基础参数\",\"type\":\"arrayParam\",\"arrayParam\":{\"params\":[{\"key\":\"id\",\"type\":\"uint32\",\"unit\":\"\",\"desc\":\"H1.2.10以前:\\\\nNormalid范围：0-8\\\\nVirtualid范围：9-16\\\\nModbusid范围：17-80\\\\n\\\\nH1.2.10以前(含curtis扩展IO):\\\\nNormalid范围：0-16\\\\nVirtualid范围：17-24\\\\nModbusid范围：25-80\\\\n\\\\nH1.2.10及以后:\\\\nNormalid范围：0-10\\\\nVirtualid范围：11-18\\\\nModbusid范围：19-80\\\\n\\\\nH1.2.10及以后(含curtis扩展IO):\\\\nNormalid范围：0-18\\\\nVirtualid范围：19-26\\\\nModbusid范围：27-80\",\"cloneEnable\":false,\"uint32Value\":8,\"uint32Maxvalue\":80,\"uint32Minvalue\":0},{\"key\":\"inverse\",\"type\":\"bool\",\"unit\":\"\",\"desc\":\"反向电平触发(虚拟DI和curtisk扩展DI除外)\",\"cloneEnable\":false,\"boolValue\":false}]},\"cloneEnable\":false}]}]},{\"ord\":0,\"name\":\"DISensor\",\"desc\":\"DISensor\",\"maxCount\":81,\"devices\":[{\"name\":\"DISensor\",\"desc\":\"DISensor\",\"isDisplay\":true,\"isEnabled\":false,\"deviceParams\":[{\"key\":\"basic\",\"desc\":\"基础参数\",\"type\":\"arrayParam\",\"arrayParam\":{\"params\":[{\"key\":\"id\",\"type\":\"uint32\",\"unit\":\"\",\"desc\":\"选择相应的DI序号\",\"cloneEnable\":false,\"uint32Value\":0,\"uint32Maxvalue\":80,\"uint32Minvalue\":0}]},\"cloneEnable\":false},{\"key\":\"shape\",\"desc\":\"形状\",\"type\":\"comboParam\",\"comboParam\":{\"childKey\":\"arc\",\"childParams\":[{\"key\":\"arc\",\"desc\":\"扇形\",\"params\":[{\"key\":\"x\",\"type\":\"double\",\"unit\":\"m\",\"desc\":\"x\",\"cloneEnable\":false,\"doubleValue\":0,\"doubleMaxvalue\":1000,\"doubleMinvalue\":-1000},{\"key\":\"y\",\"type\":\"double\",\"unit\":\"m\",\"desc\":\"y\",\"cloneEnable\":false,\"doubleValue\":0,\"doubleMaxvalue\":1000,\"doubleMinvalue\":-1000},{\"key\":\"z\",\"type\":\"double\",\"unit\":\"m\",\"desc\":\"z\",\"cloneEnable\":false,\"doubleValue\":0,\"doubleMaxvalue\":1000,\"doubleMinvalue\":-1000},{\"key\":\"yaw\",\"type\":\"double\",\"unit\":\"deg\",\"desc\":\"yaw\",\"cloneEnable\":false,\"doubleValue\":0,\"doubleMaxvalue\":180,\"doubleMinvalue\":-180},{\"key\":\"minDist\",\"type\":\"double\",\"unit\":\"m\",\"desc\":\"最近距离\",\"cloneEnable\":false,\"doubleValue\":0,\"doubleMaxvalue\":999.99,\"doubleMinvalue\":0},{\"key\":\"maxDist\",\"type\":\"double\",\"unit\":\"m\",\"desc\":\"最远距离\",\"cloneEnable\":false,\"doubleValue\":0.1,\"doubleMaxvalue\":999.99,\"doubleMinvalue\":0},{\"key\":\"range\",\"type\":\"double\",\"unit\":\"deg\",\"desc\":\"角度范围\",\"cloneEnable\":false,\"doubleValue\":0,\"doubleMaxvalue\":360,\"doubleMinvalue\":0}]},{\"key\":\"vertex\",\"desc\":\"多边形\",\"params\":[{\"key\":\"posX1\",\"type\":\"double\",\"unit\":\"m\",\"desc\":\"posX1\",\"cloneEnable\":false,\"doubleValue\":0,\"doubleMaxvalue\":999.99,\"doubleMinvalue\":-999},{\"key\":\"posY1\",\"type\":\"double\",\"unit\":\"m\",\"desc\":\"posY1\",\"cloneEnable\":false,\"doubleValue\":0,\"doubleMaxvalue\":999.99,\"doubleMinvalue\":-999}]}]},\"cloneEnable\":false},{\"key\":\"type\",\"desc\":\"type\",\"type\":\"comboParam\",\"comboParam\":{\"childKey\":\"none\",\"childParams\":[{\"key\":\"none\",\"desc\":\"空\",\"params\":[]},{\"key\":\"collision\",\"desc\":\"碰撞条\",\"params\":[]},{\"key\":\"infrared\",\"desc\":\"红外\",\"params\":[]},{\"key\":\"ultrasonic\",\"desc\":\"超声\",\"params\":[]},{\"key\":\"fallingDown\",\"desc\":\"防跌落\",\"params\":[]},{\"key\":\"goodsDetect\",\"desc\":\"goodsdetection\",\"params\":[]},{\"key\":\"ignoreTask\",\"desc\":\"ignoretaskinstruction\",\"params\":[]}]},\"cloneEnable\":false},{\"key\":\"func\",\"desc\":\"功能\",\"type\":\"comboParam\",\"comboParam\":{\"childKey\":\"none\",\"childParams\":[{\"key\":\"none\",\"desc\":\"空\",\"params\":[]},{\"key\":\"stop\",\"desc\":\"停止\",\"params\":[]},{\"key\":\"slowdown\",\"desc\":\"减速\",\"params\":[]}]},\"cloneEnable\":false}]}]},{\"ord\":0,\"name\":\"DO\",\"desc\":\"DO\",\"maxCount\":80,\"devices\":[{\"name\":\"DO0\",\"desc\":\"RE8=\",\"isDisplay\":false,\"isEnabled\":true,\"deviceParams\":[{\"key\":\"basic\",\"desc\":\"基础参数\",\"type\":\"arrayParam\",\"arrayParam\":{\"params\":[{\"key\":\"id\",\"type\":\"uint32\",\"unit\":\"\",\"desc\":\"H1.2.10以前:\\\\nNormalid范围：0-6\\\\n保留区：7-14\\\\nModbusid范围：15-79\\\\n\\\\nH1.2.10及以后:\\\\nNormalid范围：0-9\\\\n保留区：10-17\\\\nModbusid范围：18-79\\\\n(DO8DO9输出为400mA)\",\"cloneEnable\":false,\"uint32Value\":0,\"uint32Maxvalue\":79,\"uint32Minvalue\":0},{\"key\":\"default\",\"type\":\"bool\",\"unit\":\"\",\"desc\":\"开机时默认状态\",\"cloneEnable\":false,\"boolValue\":true}]},\"cloneEnable\":false}]},{\"name\":\"DO1\",\"desc\":\"RE8=\",\"isDisplay\":false,\"isEnabled\":true,\"deviceParams\":[{\"key\":\"basic\",\"desc\":\"基础参数\",\"type\":\"arrayParam\",\"arrayParam\":{\"params\":[{\"key\":\"id\",\"type\":\"uint32\",\"unit\":\"\",\"desc\":\"H1.2.10以前:\\\\nNormalid范围：0-6\\\\n保留区：7-14\\\\nModbusid范围：15-79\\\\n\\\\nH1.2.10及以后:\\\\nNormalid范围：0-9\\\\n保留区：10-17\\\\nModbusid范围：18-79\\\\n(DO8DO9输出为400mA)\",\"cloneEnable\":false,\"uint32Value\":1,\"uint32Maxvalue\":79,\"uint32Minvalue\":0},{\"key\":\"default\",\"type\":\"bool\",\"unit\":\"\",\"desc\":\"开机时默认状态\",\"cloneEnable\":false,\"boolValue\":false}]},\"cloneEnable\":false}]},{\"name\":\"DO2\",\"desc\":\"RE8=\",\"isDisplay\":false,\"isEnabled\":true,\"deviceParams\":[{\"key\":\"basic\",\"desc\":\"基础参数\",\"type\":\"arrayParam\",\"arrayParam\":{\"params\":[{\"key\":\"id\",\"type\":\"uint32\",\"unit\":\"\",\"desc\":\"H1.2.10以前:\\\\nNormalid范围：0-6\\\\n保留区：7-14\\\\nModbusid范围：15-79\\\\n\\\\nH1.2.10及以后:\\\\nNormalid范围：0-9\\\\n保留区：10-17\\\\nModbusid范围：18-79\\\\n(DO8DO9输出为400mA)\",\"cloneEnable\":false,\"uint32Value\":2,\"uint32Maxvalue\":79,\"uint32Minvalue\":0},{\"key\":\"default\",\"type\":\"bool\",\"unit\":\"\",\"desc\":\"开机时默认状态\",\"cloneEnable\":false,\"boolValue\":false}]},\"cloneEnable\":false}]},{\"name\":\"DO3\",\"desc\":\"RE8=\",\"isDisplay\":false,\"isEnabled\":true,\"deviceParams\":[{\"key\":\"basic\",\"desc\":\"基础参数\",\"type\":\"arrayParam\",\"arrayParam\":{\"params\":[{\"key\":\"id\",\"type\":\"uint32\",\"unit\":\"\",\"desc\":\"H1.2.10以前:\\\\nNormalid范围：0-6\\\\n保留区：7-14\\\\nModbusid范围：15-79\\\\n\\\\nH1.2.10及以后:\\\\nNormalid范围：0-9\\\\n保留区：10-17\\\\nModbusid范围：18-79\\\\n(DO8DO9输出为400mA)\",\"cloneEnable\":false,\"uint32Value\":3,\"uint32Maxvalue\":79,\"uint32Minvalue\":0},{\"key\":\"default\",\"type\":\"bool\",\"unit\":\"\",\"desc\":\"开机时默认状态\",\"cloneEnable\":false,\"boolValue\":false}]},\"cloneEnable\":false}]},{\"name\":\"DO4\",\"desc\":\"RE8=\",\"isDisplay\":false,\"isEnabled\":true,\"deviceParams\":[{\"key\":\"basic\",\"desc\":\"基础参数\",\"type\":\"arrayParam\",\"arrayParam\":{\"params\":[{\"key\":\"id\",\"type\":\"uint32\",\"unit\":\"\",\"desc\":\"H1.2.10以前:\\\\nNormalid范围：0-6\\\\n保留区：7-14\\\\nModbusid范围：15-79\\\\n\\\\nH1.2.10及以后:\\\\nNormalid范围：0-9\\\\n保留区：10-17\\\\nModbusid范围：18-79\\\\n(DO8DO9输出为400mA)\",\"cloneEnable\":false,\"uint32Value\":4,\"uint32Maxvalue\":79,\"uint32Minvalue\":0},{\"key\":\"default\",\"type\":\"bool\",\"unit\":\"\",\"desc\":\"开机时默认状态\",\"cloneEnable\":false,\"boolValue\":false}]},\"cloneEnable\":false}]},{\"name\":\"DO5\",\"desc\":\"RE8=\",\"isDisplay\":false,\"isEnabled\":true,\"deviceParams\":[{\"key\":\"basic\",\"desc\":\"基础参数\",\"type\":\"arrayParam\",\"arrayParam\":{\"params\":[{\"key\":\"id\",\"type\":\"uint32\",\"unit\":\"\",\"desc\":\"H1.2.10以前:\\\\nNormalid范围：0-6\\\\n保留区：7-14\\\\nModbusid范围：15-79\\\\n\\\\nH1.2.10及以后:\\\\nNormalid范围：0-9\\\\n保留区：10-17\\\\nModbusid范围：18-79\\\\n(DO8DO9输出为400mA)\",\"cloneEnable\":false,\"uint32Value\":5,\"uint32Maxvalue\":79,\"uint32Minvalue\":0},{\"key\":\"default\",\"type\":\"bool\",\"unit\":\"\",\"desc\":\"开机时默认状态\",\"cloneEnable\":false,\"boolValue\":true}]},\"cloneEnable\":false}]},{\"name\":\"DO6\",\"desc\":\"RE8=\",\"isDisplay\":false,\"isEnabled\":true,\"deviceParams\":[{\"key\":\"basic\",\"desc\":\"基础参数\",\"type\":\"arrayParam\",\"arrayParam\":{\"params\":[{\"key\":\"id\",\"type\":\"uint32\",\"unit\":\"\",\"desc\":\"H1.2.10以前:\\\\nNormalid范围：0-6\\\\n保留区：7-14\\\\nModbusid范围：15-79\\\\n\\\\nH1.2.10及以后:\\\\nNormalid范围：0-9\\\\n保留区：10-17\\\\nModbusid范围：18-79\\\\n(DO8DO9输出为400mA)\",\"cloneEnable\":false,\"uint32Value\":6,\"uint32Maxvalue\":79,\"uint32Minvalue\":0},{\"key\":\"default\",\"type\":\"bool\",\"unit\":\"\",\"cloneEnable\":false,\"boolValue\":true}]},\"cloneEnable\":false}]}]},{\"ord\":0,\"name\":\"chassis\",\"desc\":\"chassis device type\",\"maxCount\":1,\"devices\":[{\"name\":\"chassis\",\"desc\":\"Y2hhc3Npcw==\",\"isDisplay\":true,\"isEnabled\":true,\"deviceParams\":[{\"key\":\"shape\",\"desc\":\"Robot chassis shape\",\"type\":\"comboParam\",\"comboParam\":{\"childKey\":\"rectangle\",\"childParams\":[{\"key\":\"rectangle\",\"desc\":\"rectangle\",\"params\":[{\"key\":\"width\",\"type\":\"double\",\"unit\":\"m\",\"desc\":\"wide\",\"cloneEnable\":false,\"doubleValue\":0.545,\"doubleMaxvalue\":9,\"doubleMinvalue\":0},{\"key\":\"head\",\"type\":\"double\",\"unit\":\"m\",\"desc\":\"head\",\"cloneEnable\":false,\"doubleValue\":0.405,\"doubleMaxvalue\":9,\"doubleMinvalue\":0},{\"key\":\"tail\",\"type\":\"double\",\"unit\":\"m\",\"desc\":\"tail\",\"cloneEnable\":false,\"doubleValue\":0.405,\"doubleMaxvalue\":9,\"doubleMinvalue\":0},{\"key\":\"height\",\"type\":\"double\",\"unit\":\"m\",\"desc\":\"robot height\",\"cloneEnable\":false,\"doubleValue\":0.28,\"doubleMaxvalue\":100,\"doubleMinvalue\":0.01}]}]},\"cloneEnable\":false}]}]}]}",
    jacksonTypeRef()
  )

  /**
   * 从文件中读取配置，如果没有配置或解析失败，返回默认配置。
   */
  fun loadMockConfig(): MockConfig {
    val file = getMockConfigFile()
    return try {
      JsonFileHelper.readJsonFromFile(file) ?: MockConfig()
    } catch (e: Exception) {
      logger.error("读取仿真配置文件失败", e)
      MockConfig()
    }
  }

  /**
   * 将配置保存到文件中。
   */
  fun saveMockConfig(config: MockConfig) {
    val file = getMockConfigFile()
    JsonFileHelper.writeJsonToFile(file, config, true)
  }

  private fun getMockConfigFile(): File {
    return File(BaseCenter.baseConfig.configDir, "mock-config.json")
  }

  /**
   * 加载所有仿真机器人运行数据，键是机器人 ID
   */
  fun loadMockRobotRecords(): Map<String, MockSeerRobotRecord> {
    val dir = ensureMockRobotRecordDir()
    val records =
      dir.listFiles()?.filter { it.isFile }?.mapNotNull { JsonFileHelper.readJsonFromFile<MockSeerRobotRecord>(it) }
        ?: emptyList()
    val map = mutableMapOf<String, MockSeerRobotRecord>()
    for (r in records) {
      map[r.id] = r
    }
    return map
  }

  /**
   * 删除仿真机器人运行数据文件。
   */
  fun removeMockRobotsRecords(ids: List<String>) {
    // TODO
    val dir = ensureMockRobotRecordDir()
    val records = dir.listFiles()?.filter { it.isFile }?.filter { it.nameWithoutExtension in ids } ?: emptyList()
    for (r in records) {
      r.delete()
    }
  }

  /**
   * 异步更新仿真机器人运行数据文件。
   */
  fun updateMockRobotRecordAsync(record: MockSeerRobotRecord) {
    val dir = ensureMockRobotRecordDir()
    JsonFileHelper.writeJsonToFile(File(dir, "${record.id}.json"), record, true)
  }

  /**
   * 保存仿真机器人运行数据的文件，里面每个机器人一个。
   */
  private fun ensureMockRobotRecordDir(): File {
    val dir = File(FileManager.ensureFilesDir(), "mock-robots")
    dir.mkdirs()
    return dir
  }

  /**
   * 保存仿真机器人模型文件的目录
   */
  private fun ensureMockRobotModelDir(): File {
    // 模型文件的存放位置：./files/mock-robots/models
    val modelDir = File(ensureMockRobotRecordDir(), "models")
    modelDir.mkdirs()
    return modelDir
  }

  /**
   * 获取仿真机器人模型文件。如果没有对应的模型文件，则加载默认的模型文件。
   */
  fun loadMockRobotModel(id: String): RbkModel {
    val modelDir = ensureMockRobotModelDir()

    val modelFile = File(modelDir, "$id-model.json")
    if (modelFile.exists()) {
      // 万一是个空的 JSON 文件咋办？
      val model = JsonFileHelper.readJsonFromFile<RbkModel>(modelFile)
      if (model != null) return model
    }

    // // 加载默认的模型文件，不将默认的模型文件写入到本地文件了。
    // val defaultModelFile = File(modelDir, "default-model.json")
    // if (!defaultModelFile.exists()) {
    //   JsonFileHelper.writeJsonToFile(defaultModelFile, defaultModel, true)
    // }

    // 没有读取到对应的模型文件，返回默认的模型文件。
    return defaultModel
  }

  /**
   * 保存仿真机器人模型文件。
   */
  fun saveMockRobotModel(id: String, model: RbkModel) {
    val modelDir = ensureMockRobotModelDir()
    JsonFileHelper.writeJsonToFile(File(modelDir, "$id-model.json"), model, true)
  }

  /**
   * 删除仿真机器人模型文件。
   */
  fun removeMockRobotModels(ids: List<String>) {
    val modelDir = ensureMockRobotModelDir()
    val records = modelDir.listFiles()?.filter { it.isFile }
      ?.filter { it.nameWithoutExtension.split("-").first() in ids } ?: emptyList()
    for (r in records) {
      r.delete()
    }
  }

  /**
   * 获取仿真机器人运行数据文件。注意可能不存在。
   */
  private fun getMockRobotRecordFile(id: String): File {
    return File(ensureMockRobotRecordDir(), "$id.json")
  }

}