package com.seer.trick.fleet.mars

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.failure.FailureRecordReq
import com.seer.trick.base.failure.FailureRecorder
import com.seer.trick.base.soc.SocService
import com.seer.trick.base.soc.SysMonitorService
import com.seer.trick.fleet.domain.RobotHelper
import com.seer.trick.fleet.domain.RobotSelfReport
import com.seer.trick.fleet.domain.RobotVendor
import com.seer.trick.fleet.service.MapService
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.RobotService
import com.seer.trick.helper.BoolHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.getTypeMessage
import com.seer.trick.robot.handler.GwWsServer
import com.seer.trick.robot.light.ReportApplyNextOrder
import com.seer.trick.robot.light.ReportApplyReq
import com.seer.trick.robot.light.ReportApplyRes
import com.seer.trick.robot.rachel.DrOrderStatus
import com.seer.trick.robot.sto.OrderResult
import com.seer.trick.robot.sto.StOrderStatus
import org.slf4j.LoggerFactory
import java.util.*

/**
 * 光通讯上报申请模式的服务端管理
 */
object MarsReportApplyManager {

  private val logger = LoggerFactory.getLogger(javaClass)

  fun init() {
    GwWsServer.onReportApply = ::onReportApply
  }

  /**
   * 服务端收到上报申请
   */
  fun onReportApply(req: ReportApplyReq): ReportApplyRes {
    val sr = MarsCenter.getSceneOrFirstMarsScene()
    val rr = sr.mustGetRobot(req.robotName)

    val selfReport = toSelfReport(rr, req)

    RobotService.updateSelfReport(rr, selfReport)

    // 处理直接运单，更新老单，下发新单

    val currentOrder = req.currentOrder
    val lastOrder = req.lastOrder

    if (currentOrder != null) updateDirectOrderBySimple(currentOrder, req.robotName)
    if (lastOrder != null) updateDirectOrderBySimple(lastOrder, req.robotName)

    var nextOrderError: Exception? = null

    val nextOrder = if (currentOrder == null) {
      try {
        val nextOrderEv = EntityRwService.findOne(
          "DirectRobotOrder",
          Cq.and(listOf(Cq.eq("status", DrOrderStatus.Created.name), Cq.eq("robotName", req.robotName))),
          FindOptions(sort = listOf("createdOn")),
        )
        if (nextOrderEv != null) {
          val nextOrderId = EntityHelper.mustGetId(nextOrderEv)

          val seer3066 = BoolHelper.anyToBool(nextOrderEv["seer3066"])
          val moves: List<EntityValue> = JsonHelper.mapper.readValue(nextOrderEv["moves"] as String, jacksonTypeRef())
          logger.info(
            "下发直接运单给机器人 '${rr.robotName}' 单号='$nextOrderId' (上一单 ${lastOrder?.orderId}) : $moves",
          )
          ReportApplyNextOrder(nextOrderId, req.robotName, seer3066, moves)
        } else {
          null
        }
      } catch (e: Exception) {
        nextOrderError = e
        null
      }
    } else {
      null
    }

    // 从服务端发起取消
    val cancelledOrderIds = mutableListOf<String>()
    val manualDoneOrderIds = mutableListOf<String>()
    for (or in req.unfinishedOrders) {
      val orderEv = EntityRwService.findOneById("DirectRobotOrder", or.orderId)
      val status = orderEv?.get("status") as String?
      if (status == null || status == DrOrderStatus.Cancelled.name) {
        cancelledOrderIds += or.orderId
      } else if (status == DrOrderStatus.ManualDone.name) {
        manualDoneOrderIds += or.orderId
      }
    }
    if (cancelledOrderIds.isNotEmpty()) {
      logger.info("从服务端取消直接运单：$cancelledOrderIds，机器人=${req.robotName}")
    }
    if (manualDoneOrderIds.isNotEmpty()) {
      logger.info("从服务端手工完成直接运单：$cancelledOrderIds，机器人=${req.robotName}")
    }

    SysMonitorService.log(
      subject = "Light",
      target = req.robotName,
      field = "reportApply",
      value = "GW current order: $currentOrder, GW last order: $lastOrder, " +
        "GW unfinished orders: ${req.unfinishedOrders}, " +
        (if (nextOrderError != null) "server next order error: ${nextOrderError.getTypeMessage()}" else "") +
        "server next order: ${nextOrder?.id}, server cancelled orders: $cancelledOrderIds, " +
        "server manual done orders: $manualDoneOrderIds",
    )

    SocService.updateNode(
      "机器人",
      "RobotReportApply:${req.robotName}",
      "机器人上报申领:${req.robotName}",
      "请求：当前运单=$currentOrder。请求：上一运单：$lastOrder。" +
        "请求：GW 里所有未完成的运单：${req.unfinishedOrders}。" +
        (if (nextOrderError != null) "响应：下一运单服务器内错误：${nextOrderError.getTypeMessage()}" else "") +
        "响应：下一运单=${nextOrder?.id}。取消运单：$cancelledOrderIds。手工完成：$manualDoneOrderIds",
    )

    return ReportApplyRes(nextOrder, cancelledOrderIds, manualDoneOrderIds)
  }

  private fun toSelfReport(rr: RobotRuntime, req: ReportApplyReq): RobotSelfReport {
    val rawReport = req.selfReport.rawReport
    var errorMsg = if (rawReport == null) "机器人离线" else ""
    // 目前没有用到碰撞模型，所以 shape 为空集合
    val main = RobotHelper.rawToMain(RobotVendor.Seer, rawReport, rr)
    val stand = MapService.estimateStand(rr, main)
    errorMsg = if (stand == null) "机器人位置无法判断" else errorMsg
    return RobotSelfReport(
      error = stand == null,
      errorMsg = errorMsg,
      main = main,
      stand = stand,
      rawReport = rawReport,
    )
  }

  private fun updateDirectOrderBySimple(or: OrderResult, robotName: String) {
    val serverOrderEv = EntityRwService.findOneById("DirectRobotOrder", or.orderId) ?: return
    val serverStatus = serverOrderEv["status"] as String?

    var update: EntityValue? = null

    if (serverStatus == DrOrderStatus.Done.name ||
      serverStatus == DrOrderStatus.ManualDone.name ||
      serverStatus == DrOrderStatus.Cancelled.name
    ) {
      if (or.status.name != serverStatus) {
        logger.error(
          "直接运单 ${or.orderId} 状态已经是终态了 $serverStatus，但收到上报 ${or.status}，机器人=$robotName",
        )
        // 仅仅当变成另一种终态时才修改
        if (or.status == StOrderStatus.Done || or.status == StOrderStatus.Cancelled) {
          update = mutableMapOf("status" to or.status.name, "doneOn" to Date(), "errMsg" to "")
        }
      }
    } else if (or.status == StOrderStatus.Created && serverStatus == DrOrderStatus.Created.name) {
      // GW 收到就是已下发
      logger.info("直接运单 ${or.orderId} 状态从 Created 变为 Sent，机器人=$robotName")
      update = mutableMapOf("status" to DrOrderStatus.Sent.name, "doneOn" to null, "errMsg" to "")
    } else if (or.status == StOrderStatus.Created && serverStatus == DrOrderStatus.Sent.name) {
      // 已下发不变
    } else if (serverStatus != or.status.name) {
      // 注意可能出现 GW 里是 Created，但 M4 里是终态
      logger.info(
        "直接运单 ${or.orderId} 状态从 $serverStatus 变为 ${or.status}，机器人=$robotName",
      )
      val doneOn = if (or.status == StOrderStatus.Done || or.status == StOrderStatus.Cancelled) Date() else null
      update = mutableMapOf("status" to or.status.name, "doneOn" to doneOn, "errMsg" to or.errMsg)
    }

    if (update != null) {
      EntityRwService.updateOne("DirectRobotOrder", Cq.idEq(or.orderId), update)

      if (or.status == StOrderStatus.Failed) {
        // 记录一次故障
        FailureRecorder.addAsync(
          FailureRecordReq(
            kind = "RobotTransport",
            subKind = "DirectOrder",
            source = "直接运单=${or.orderId}",
            part = robotName,
            desc = "直接运单失败。" + (or.errMsg ?: "未知原因"),
          ),
        )
      }
    }
  }
}