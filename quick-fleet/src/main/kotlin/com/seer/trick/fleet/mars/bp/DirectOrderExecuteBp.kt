package com.seer.trick.fleet.mars.bp

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*
import com.seer.trick.fleet.service.SceneService
import com.seer.trick.helper.JsonHelper
import com.seer.trick.robot.RobotAppManager

class DirectOrderExecuteBp : AbstractBp() {

  override fun process() {
    val sceneName = mustGetBlockInputParam("sceneName") as String? ?: ""
    val robotId = mustGetBlockInputParam("robotId") as String? ?: ""
    val desc = getBlockInputParam("desc") as String?
    val taskId = getBlockInputParam("taskId") as String?
    val seer3066 = getBlockInputParamAsBool("seer3066") as Boolean? ?: false
    val fillMiddleLandmarks = getBlockInputParamAsBool("fillMiddleLandmarks")
    val unlockCurrentDirectSiteIds = getBlockInputParamAsBool("unlockCurrentDirectSiteIds") as Boolean? ?: false

    val children = serialRunChildren(defaultChildrenConfig, CHILD_DEFAULT, blockContext)
    var moveList: List<EntityValue> = children.map {
      val ms = it.getInternalVariable("move") as String
      JsonHelper.mapper.readValue(ms, jacksonTypeRef())
    }

    var movesStr = ""
    val sr2 = RobotAppManager.getLightScene(sceneName)
    val sr3 = SceneService.getSceneByName(sceneName)
    if (sr2 != null) {
      // 2.5 光通讯
      if (fillMiddleLandmarks) moveList = sr2.rachel!!.lightScheduler.buildMoves(robotId, moveList)
      movesStr = JsonHelper.mapper.writeValueAsString(moveList)
    } else if (sr3 != null) {
      // 3 代光通讯
      if (fillMiddleLandmarks) moveList = sr3.marsService.scheduler.buildMoves(robotId, moveList)
      movesStr = JsonHelper.mapper.writeValueAsString(moveList)
    }

    val orderId = getOrSetBlockInternalVariable("orderId") {
      val ev: EntityValue = mutableMapOf(
        "taskId" to taskId,
        "robotName" to robotId,
        "status" to "Created",
        "seer3066" to seer3066,
        "moves" to movesStr,
        "description" to desc,
      )
      val orderId = EntityRwService.createOne("DirectRobotOrder", ev)
      logger.info("创建直接运单完成，'$orderId'，机器人=$robotId，任务=$taskId")
      orderId
    } as String

    addActualVehicle(robotId)
    logger.info("等待直接运单完成，'$orderId'，机器人=$robotId，任务=$taskId")

    addResource("DirectRobotOrder", orderId, mapOf("orderId" to orderId))
    // 等待运单完成
    while (true) {
      val order = EntityRwService.findOneById("DirectRobotOrder", orderId) ?: return
      val status = order["status"] as String
      if (status == "Done") {
        logger.info("直接运单，完成，'$orderId'，机器人=$robotId")
        break
      } else if (status == "Cancelled") {
        logger.info("直接运单，取消，'$orderId'，机器人=$robotId")
        // 直接运单被取消时，抛出异常取消整个猎鹰任务
        throw TaskCancelledError()
      } else if (status == "Failed") {
        logger.info("直接运单，失败，'$orderId'，机器人=$robotId")
        throw BzError("errDirectRobotOrderFailed", orderId)
      }
      Thread.sleep(500)
    }

    if (fillMiddleLandmarks) {
      val siteIds: List<String> = moveList
        .flatMap { move -> listOf(move["id"] as String?, move["source_id"] as String?) } // 获取 id 和 source_id
        .filterNotNull() // 过滤 null 值
        .distinct()

      if (sr2 != null) {
        // 处理 sr2 的逻辑
        val scheduleService = sr2.rachel?.scheduleService
        if (unlockCurrentDirectSiteIds) {
          // 获取加锁资源并解锁
          scheduleService?.unlockBySiteIds(robotId, siteIds)
        } else {
          // 按机器人解锁
          scheduleService?.unlockByRobot(robotId)
        }
      } else {
        // 处理 sr3 的逻辑
        val scheduleService = sr3?.marsService?.scheduleService
        if (unlockCurrentDirectSiteIds) {
          scheduleService?.unlockBySiteIds(robotId, siteIds)
        } else {
          // 按机器人解锁
          scheduleService?.unlockByRobot(robotId)
        }
      }
    }

    // 添加猎鹰任务相关业务对象
    addRelatedObject("DirectRobotOrder", orderId, sceneName)
  }

  companion object {

    val def = BlockDef(
      DirectOrderExecuteBp::class.simpleName!!,
      color = "#C7DCA7",
      inputParams = listOf(
        BlockInputParamDef("sceneName", BlockParamType.String, true),
        BlockInputParamDef("robotId", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.RobotName)),
        BlockInputParamDef("desc", BlockParamType.String, false),
        BlockInputParamDef("taskId", BlockParamType.String, false),
        BlockInputParamDef("seer3066", BlockParamType.Boolean, false),
        BlockInputParamDef("fillMiddleLandmarks", BlockParamType.Boolean, false),
        BlockInputParamDef("unlockCurrentDirectSiteIds", BlockParamType.Boolean, false),
      ),
      outputParams = listOf(
        BlockOutputParamDef("orderId", BlockParamType.String, objectTypes = listOf(ParamObjectType.DirectOrderId)),
      ),
      children = listOf(BlockChildDef(CHILD_DEFAULT, childrenMinNum = 1, childrenMaxNum = null)),
    )
  }
}