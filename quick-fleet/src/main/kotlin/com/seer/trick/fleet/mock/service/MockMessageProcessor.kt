package com.seer.trick.fleet.mock.service

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.node.ArrayNode
import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.fleet.domain.RbkModel
import com.seer.trick.fleet.mock.*
import com.seer.trick.fleet.mock.service.MockMoveProcessor.getMoveMethod
import com.seer.trick.fleet.seer.PointHelper
import com.seer.trick.fleet.seer.Smap
import com.seer.trick.fleet.seer.SmapCurveHelper
import com.seer.trick.fleet.service.RobotGroupService
import com.seer.trick.fleet.service.RobotService
import com.seer.trick.fleet.service.SceneFileService
import com.seer.trick.fleet.service.SceneService
import com.seer.trick.helper.DateHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.getTypeMessage
import com.seer.trick.robot.vendor.seer.rbk.RbkFrame
import org.apache.commons.codec.digest.DigestUtils
import org.apache.commons.io.FileUtils
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import java.io.File
import java.nio.charset.StandardCharsets
import java.util.*
import kotlin.math.PI
import kotlin.math.abs
import kotlin.math.cos
import kotlin.math.sin
import kotlin.random.Random

object MockMessageProcessor {

  private val logger = LoggerFactory.getLogger(this::class.java)

  // 不需要校验控制权的 API 的编号
  private val ignoreApiNoList = listOf(
    1000, 1020, 1060, 1100, 1110, 1300, 1302, 1400, 1500,
    4005, 4006, // 获取控制权、释放控制权不用校验，否则会导致其他请求方无法强抢控制权。
    // 6004, // 设置仿真机器人软急停是否也要校验控制权？  RBK 要求请求方有机器人的控制权时，才能修改软急停状态。
  )

  /**
   * 查询机器人信息
   */
  fun handle1000(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    val res: EntityValue = mutableMapOf(
      "ret_code" to 0,
      "id" to mr.config.id,
      "vehicle_id" to mr.config.name,
      "current_map" to mr.record.map,
    )
    return JsonHelper.writeValueAsString(res)
  }

  /**
   * 查询导航状态
   */
  fun handle1020(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    val status = mr.currentTask?.status?.code ?: 404
    val res: EntityValue = mutableMapOf("ret_code" to 0, "task_status" to status)
    return JsonHelper.writeValueAsString(res)
  }

  // 查询控制权状态
  fun handle1060(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    val res: EntityValue = mutableMapOf(
      "ret_code" to 0,
      "nick_name" to mr.record.currentLock.nickName,
      "locked" to mr.record.currentLock.locked,
    )
    return JsonHelper.writeValueAsString(res)
  }

  /**
   * 查询机器人状态
   */
  fun handle1100(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    val sceneId = mr.config.sceneId
    SceneService.mustGetSceneById(sceneId)
    // 离开充电点且充电中时置为未充电。无需持久化，仿真耗电和仿真充电的任务中会做持久化
    val isChargingPoint = mr.record.point?.startsWith("CP") ?: false
    if (!isChargingPoint && mr.record.charging) {
      mr.setRecord(mr.record.copy(charging = false))
    }

    // TODO 仿真机器人的库位管理。
    // val rr = sr.mustGetRobot(mr.record.name)
    // // 暂时这样，机器人有货物但库位被清空了则自动清空货物
    // if (mr.record.goodsRegion != null &&
    //   rr.bins.find { it.status != RobotBinStatus.Empty && it.status != RobotBinStatus.Reserved } == null
    // ) {
    //   // 不能这么干，每次都 setRecord 会重复触发 MockSeerRobotRuntime.lock.notifyAll()，因此仿真机器人停不下来
    //   mr.setRecord(record = mr.record.copy(goodsRegion = null))
    // }

    // logger.info("${mr.config.name} handle 1100 msg: ${msg.bodyStr}")

    val res: EntityValue = mutableMapOf(
      "ret_code" to 0,
      "battery_level" to mr.record.battery,
      "x" to mr.record.x,
      "y" to mr.record.y,
      "vx" to mr.currentSpeed.speed,
      "w" to mr.currentSpeed.rotSpeed,
      "angle" to mr.record.theta,
      "current_station" to mr.record.point,
      "blocked" to mr.record.blocked,
      "charging" to mr.record.charging,
      "current_map" to mr.record.map, // ?:sr.map.sceneMapRuntime.sceneMap.areas.firstOrNull()?.name // 默认第一个场景区域
      "vehicle_id" to mr.config.name,
      "current_lock" to mutableMapOf(
        "nick_name" to mr.record.currentLock.nickName,
        "locked" to mr.record.currentLock.locked,
      ),
      "reloc_status" to 1,
      "fatals" to mr.record.fatals(),
      "errors" to mr.record.errors(),
      "warnings" to mr.record.warnings(),
      "notices" to mr.record.notices(),
      "current_map_md5" to mr.config.maps.firstOrNull { it.mapName == mr.record.map }?.mapMd5,
      "goods_region" to mr.record.goodsRegion,
      "confidence" to Random.nextDouble(0.5, 0.9),
      "emergency" to mr.record.emc,
      "soft_emc" to mr.record.softEmc,
      "containers" to mr.record.selfBins,
      "loadmap_status" to mr.record.loadmapStatus,
      "task_status" to if (mr.currentTask?.status == MoveTaskStatus.Cancelling) {
        MoveTaskStatus.Cancelled.code
      } else {
        mr.currentTask?.status?.code ?: MoveTaskStatus.Init.code
      },
      "task_type" to if (mr.currentTask != null) {
        TaskType.PathToPoint.code
      } else {
        TaskType.None.code
      },
      "target_id" to if (mr.currentTask != null) {
        mr.currentTask?.step3066?.id ?: ""
      } else {
        ""
      },

    )
    return JsonHelper.writeValueAsString(res)
  }

  /**
   * 查询导航任务状态
   */
  fun handle1110(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    synchronized(mr.lock) {
      //    logger.info("${mr.config.name} handle 1110 msg: ${msg.bodyStr}")
      val reqObj: EntityValue = JsonHelper.mapper.readValue(msg.bodyStr, jacksonTypeRef())

      @Suppress("UNCHECKED_CAST")
      val taskIds = reqObj["task_ids"] as List<String>
      val statusListMap = mutableListOf<MutableMap<String, Any>>()
      taskIds.forEach {
        statusListMap.add(
          mutableMapOf(
            "task_id" to it,
            "status" to run {
              val status = mr.tasks[it]?.status
              // Cancelling 和 Failing 为内部状态，此状态下对外是 Running
              if (status == MoveTaskStatus.Cancelling || status == MoveTaskStatus.Failing) {
                MoveTaskStatus.Running.code
              } else {
                status?.code ?: MoveTaskStatus.NotFound
              }
            },
          ),
        )
      }

      val res: EntityValue = mutableMapOf(
        "ret_code" to 0,
        "task_status_package" to mutableMapOf(
          "task_status_list" to statusListMap,
        ),
      )

      return JsonHelper.writeValueAsString(res)
    }
  }

  /**
   * 查询机器人载入的地图以及存储的地图
   *
   * https://seer-group.feishu.cn/wiki/Eu5pwBAnrihjWYkE9kLc6FPln0b
   */
  fun handle1300(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
//    logger.info("${mr.config.name} handle 1300")
    val mapList = mr.config.maps.mapNotNull { m ->
      val file = SceneFileService.pathToFile(mr.config.sceneId, m.mapFile)
      if (file.exists() && file.isFile) {
        val lastModified = file.lastModified()
        val date = Date(lastModified)
        mapOf(
          "name" to m.mapName,
          "modified" to DateHelper.formatDate(date, "yyyy-MM-dd HH:mm:ss"),
          "size" to file.length(), // 1300 返回的文件 size，单位是字节
        )
      } else {
        null
      }
    }

    val currentMapMd5 = mr.config.maps.firstOrNull { it.mapName == mr.record.map }?.mapMd5
    val currentMap = rmExtension(mr, mr.record.map ?: "")
    val res = mutableMapOf(
      "ret_code" to 0,
      "current_map" to currentMap,
      "current_map_md5" to currentMapMd5,
      "map_files_info" to mapList,
      "maps" to mr.config.maps.map { rmExtension(mr, it.mapName) },
    )
    return JsonHelper.writeValueAsString(res)
  }

  private fun rmExtension(mr: MockSeerRobotRuntime, filename: String): String {
    val index = filename.lastIndexOf(".")
    // 与 rbk 保持一致，去除后缀名
    return if (index > 0) {
      filename.substring(0, index)
    } else {
      filename
    }
  }

  /**
   * 获取指定地图列表的 MD5 值
   *
   * https://seer-group.feishu.cn/wiki/ITvMwXIqZipLsIkpwGBcFhUvnce
   */
  fun handle1302(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
//    logger.info("${mr.config.name} handle 1302 msg: ${msg.bodyStr}")

    val req: MockMapNamesReq = JsonHelper.mapper.readValue(msg.bodyStr, jacksonTypeRef())

    val mapList = mr.config.maps.filter { req.mapNames.contains(it.mapName) }
    val info = mapList.map { a -> mapOf("name" to a.mapName, "md5" to a.mapMd5) }

    return JsonHelper.mapper.writeValueAsString(mapOf("ret_code" to 0, "map_info" to info))
  }

  /**
   * 获取机器人参数配置
   */
  fun handle1400(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    // logger.info("${mr.config.name} handle 1400")
    val req: Req1400 = JsonHelper.mapper.readValue(msg.bodyStr, jacksonTypeRef())
    val rConfig = buildRobotConfig(mr)
    val res = mutableMapOf<String, Any?>("create_on" to Date(), "ret_code" to 0)
    if (req.plugin == null) {
      if (req.param is Map<*, *>) {
        for ((pluginName, params) in req.param) {
          val plugin = rConfig[pluginName]
          if (plugin != null && params is List<*> && params.isNotEmpty()) {
            res[pluginName as String] = params.associateWith { plugin[it] }
          }
        }
      } else {
        if (req.param == null || (req.param is String && req.param.isBlank())) {
          res.putAll(rConfig)
        } else {
          // 虽然提示不明显，但这是抄 rbk 过来的
          res["err_msg"] = "param: param"
          res["ret_code"] = 40002
        }
      }
    } else {
      val reqPlugin = rConfig[req.plugin]
      if (req.param != null) {
        when (req.param) {
          is String -> {
            if (reqPlugin != null && reqPlugin[req.param] != null) {
              res[req.plugin] = mapOf(req.param to reqPlugin[req.param])
            }
          }

          is Map<*, *> -> {
            for ((pluginName, params) in req.param) {
              val plugin = rConfig[pluginName]
              if (plugin != null && params is List<*> && params.isNotEmpty()) {
                res[pluginName as String] = params.associateWith { plugin[it] }
              }
            }
          }

          else -> {
            res["err_msg"] = "param: param"
            res["ret_code"] = 40002
          }
        }
      } else {
        if (reqPlugin != null) res.putAll(reqPlugin)
      }
    }

    return JsonHelper.mapper.writeValueAsString(res)
  }

  /**
   * 构建机器人参数配置
   * TODO 暂时只支持 MoveFactory
   */
  private fun buildRobotConfig(mr: MockSeerRobotRuntime): Map<String, Map<String, Any?>> {
    val moveConfig = MockService.getMoveConfig(mr.config.sceneId, mr.config.name)
    return mapOf(
      "MoveFactory" to
        mapOf(
          "MaxSpeed" to mapOf(
            "advanced" to false,
            "defaultValue" to 1.0,
            "desc" to "Max Speed (m/s)",
            "group" to "Navigation",
            "maxValue" to 2.0126405599426103,
            "minValue" to 0.001,
            "type" to "double",
            "value" to moveConfig.maxSpeed,
          ),
          "MaxBackSpeed" to mapOf(
            "advanced" to false,
            "defaultValue" to 1.0,
            "desc" to "Maximum reverse speed (m/s)",
            "group" to "Navigation",
            "maxValue" to 2.0126405599426103,
            "minValue" to 0.001,
            "type" to "double",
            "value" to moveConfig.maxBackSpeed,
          ),
          "MaxRot" to mapOf(
            "advanced" to false,
            "defaultValue" to 90.0,
            "desc" to "Maximum rotation speed (°/s)",
            "group" to "Navigation",
            "maxValue" to 360.0,
            "minValue" to 0.001,
            "type" to "double",
            "value" to moveConfig.maxRotSpeed,
          ),
          "Load_MaxSpeed" to mapOf(
            "advanced" to false,
            "defaultValue" to 1.0,
            "desc" to "Load maximum speed (m/s)",
            "group" to "Navigation",
            "maxValue" to 2.0126405599426103,
            "minValue" to 0.001,
            "type" to "double",
            "value" to moveConfig.loadedMaxSpeed,
          ),
          "Load_MaxBackSpeed" to mapOf(
            "advanced" to false,
            "defaultValue" to 0.5,
            "desc" to "Load maximum reverse speed (m/s)",
            "group" to "Navigation",
            "maxValue" to 2.0126405599426103,
            "minValue" to 0.001,
            "type" to "double",
            "value" to moveConfig.loadedMaxBackSpeed,
          ),
          "Load_MaxRot" to mapOf(
            "advanced" to false,
            "defaultValue" to 60.0,
            "desc" to "Load maximum rotation speed (°/s)",
            "group" to "Navigation",
            "maxValue" to 360.0,
            "minValue" to 0.001,
            "type" to "double",
            "value" to moveConfig.loadedMaxRotSpeed,
          ),
        ),
    )
  }

  data class Req1400(
    val plugin: String?,
    val param: Any?, // rbk 文档上说可以为 array，但实际测试不行，只支持 String 和 Map
  )

  /**
   * TODO 下载机器人模型
   *
   * https://seer-group.feishu.cn/wiki/Ld9rwbVciilVbRkN42UcE6p1npe
   */
  fun handle1500(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    // model 默认给定了一个底盘形状且模型文件上传时检查 params，所以 params 不会为 null
    val model = JsonHelper.writeToTree(mr.getMockRobotModel())
    val params = model?.get("deviceTypes")?.asIterable() // deviceTypes 是 JSON 对象数组
      ?.find { it["name"]?.asText() == "chassis" }
      ?.get("devices")?.asIterable() // devices 是 JSON 对象数组
      ?.find { it["name"]?.asText() == "chassis" }
      ?.get("deviceParams")?.asIterable() // deviceParams 是 JSON 对象数组
      ?.find { it["key"]?.asText() == "shape" }
      ?.get("comboParam")
      ?.get("childParams")?.asIterable() // childParams 是 JSON 对象数组
      ?.find { it["key"]?.asText() == "rectangle" }
      ?.get("params") as ArrayNode // params 是 JSON 对象数组
    // height 暂时不检查
    val paramKeys = params.filter {
      it["key"]?.asText() == "tail" || it["key"]?.asText() == "head" || it["key"]?.asText() == "width"
    }
    if (paramKeys.size != 3) {
      val polygon = RobotGroupService.defaultCollisionPolygon
      var minX = Double.MAX_VALUE
      var maxX = -Double.MAX_VALUE
      for (point in polygon.points) {
        if (point.x < minX) minX = point.x
        if (point.x > maxX) maxX = point.x
      }
      val width = abs(polygon.points.first().y * 2)
      val tail = abs(minX)
      val head = maxX

      setNodeValue(mr, params, "width", width)
      setNodeValue(mr, params, "head", head)
      setNodeValue(mr, params, "tail", tail)
    }

    return JsonHelper.writeValueAsString(model)
  }

  private fun setNodeValue(mr: MockSeerRobotRuntime, params: ArrayNode, key: String, value: Double) {
    val oldNode = params.find { it["key"]?.asText() == key } as ObjectNode?
    if (oldNode != null) {
      oldNode.put("doubleValue", value)
    } else {
      val node = JsonHelper.mapper.createObjectNode()
      node.put("key", key)
      node.put("unit", "m")
      node.put("doubleValue", value)
      params.add(node)
    }
  }

  /**
   * 开环控制
   */
  fun handle2010(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
//    logger.info("${mr.config.name} handle 2100 msg: ${msg.bodyStr}")
    val reqObj: EntityValue = JsonHelper.mapper.readValue(msg.bodyStr, jacksonTypeRef())
    val vx = (reqObj["vx"] as Double? ?: 0.0) / 10 // 100 ms 更新一次
    val vy = (reqObj["vy"] as Double? ?: 0.0) / 10
    val w = (reqObj["w"] as Double? ?: 0.0) / 10
    // 更新机器人的位置和朝向
    synchronized(mr.lock) {
      val x = mr.record.x + vx * cos(mr.record.theta) - vy * sin(mr.record.theta)
      val y = mr.record.y + vx * sin(mr.record.theta) + vy * cos(mr.record.theta)
      val theta = mr.record.theta + w
      val point = MockSmapHelper.getClosestPoint(mr, 1.0)
      mr.setRecord(mr.record.copy(x = x, y = y, theta = theta, point = point))
    }

    val res = mutableMapOf("ret_code" to 0)
    return JsonHelper.writeValueAsString(res)
  }

  /**
   * 切换指定地图
   */
  fun handle2022(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    logger.info("${mr.config.name} handle 2022 msg: ${msg.bodyStr}")
    // 更新仿真机器人的当前地图为指定地图
    val req: MockMapNameReq = JsonHelper.mapper.readValue(msg.bodyStr, jacksonTypeRef())
    val map = mr.config.maps.firstOrNull { it.mapName == "${req.mapName}.smap" }
      ?: return JsonHelper.writeValueAsString(mapOf("ret_code" to 40051, "err_msg" to "map ${req.mapName} not exists"))
    if (!SceneFileService.pathToFile(mr.config.sceneId, map.mapFile).exists()) {
      return JsonHelper.writeValueAsString(mapOf("ret_code" to 40051, "err_msg" to "map ${req.mapName} not exists"))
    }
    switchMap(mr, map.mapName)
    return JsonHelper.writeValueAsString(mutableMapOf("ret_code" to 0))
  }

  /**
   * 为仿真车切换地图
   */
  private fun switchMap(mr: MockSeerRobotRuntime, mapName: String) {
    // 清除老地图缓存
    mr.clearCurrentMapCache()
    val record = mr.record.copy(map = mapName)
    mr.setRecord(record)
    MockStore.updateMockRobotRecordAsync(record)

    val robotConfig = MockService.config.robots.find { it.id == mr.robotId }
    if (robotConfig != null) {
      synchronized(MockService) {
        val robotConfigs = MockService.config.robots.toMutableList()
        robotConfigs.removeIf { it.id == mr.robotId }
        // 随机初始化点位并保存
        val configs = MockService.initRobotPointsRandomly(listOf(mr.config))
        mr.setConfig(configs[0])
        robotConfigs.add(configs[0])
        MockService.saveMockConfig(MockService.config.copy(robots = robotConfigs))
        // 重新解析当前地图
        mr.reloadCurrentMapCache()
      }
    }
  }

  /**
   * 上传并切换地图
   * 上传并切换载入地图
   */
  fun handle2025(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    // 不要打印 body 太大！
    logger.info("${mr.config.name} handle 2025 msg")
    val smap: Smap = JsonHelper.mapper.readValue(msg.bodyStr, jacksonTypeRef())
    // smap 中的 mapName 是不带后缀名的
    val mapName = "${smap.header.mapName}.smap"
    // 上传地图
    uploadToRobot(mr, mapName, msg.bodyStr)
    // 切换仿真机器人的当前地图为指定地图
    // TODO switchMap 可能会抛异常，需要捕获并上报
    switchMap(mr, mapName)
    return JsonHelper.writeValueAsString(mutableMapOf("ret_code" to 0))
  }

  /**
   * 暂停路径导航
   */
  fun handle3001(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    logger.info("${mr.config.name} handle 3001 msg: ${msg.bodyStr}")
    synchronized(mr.lock) {
      mr.movePaused = true
    }
    val res = mutableMapOf("ret_code" to 0)
    return JsonHelper.writeValueAsString(res)
  }

  /**
   * 恢复路径导航
   */
  fun handle3002(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    logger.info("${mr.config.name} handle 3002 msg: ${msg.bodyStr}")
    synchronized(mr.lock) {
      mr.movePaused = false
      mr.lock.notifyAll()
    }
    val res = mutableMapOf("ret_code" to 0)
    return JsonHelper.writeValueAsString(res)
  }

  /**
   * 取消路径导航
   */
  fun handle3003(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    logger.info("${mr.config.name} handle 3003 msg: ${msg.bodyStr}")
    // 4 = COMPLETED, 5 = FAILED, 6 = CANCELED
    // 4 = COMPLETED, 5 = FAILED, 6 = CANCELED
    MockMoveProcessor.triggerTaskError(mr, MoveTaskStatus.Cancelling)
    return JsonHelper.writeValueAsString(mapOf("ret_code" to 0))
  }

  /**
   * 路径导航
   */
  fun handle3051(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    val reqObj: EntityValue = JsonHelper.mapper.readValue(msg.bodyStr, jacksonTypeRef())
    val move = Step3051(
      taskId = reqObj["task_id"] as String,
      id = reqObj["id"] as String,
    )
    // 3051 导航是覆盖执行的，所以把之前的任务取消
    mr.moveTaskList.clear()
    MockSmapHelper.getPointHelper(mr.robotId, move.id)
      ?: return JsonHelper.writeValueAsString(
        mapOf(
          "ret_code" to 52701,
          "err_msg" to "can not find target id",
        ),
      )
    // 移动时重新刷新配置
    mr.moveConfig = MockService.getMoveConfig(mr.config.sceneId, mr.config.name)
    mr.future = mr.taskExecutor.submit {
      MockMoveProcessor.process3051Move(move, mr)
    }

    return JsonHelper.writeValueAsString(mapOf("ret_code" to 0))
  }

  /**
   * 指定路径导航
   */
  fun handle3066(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    logger.debug(" ${mr.config.name} handle 3066：${msg.bodyStr}")

    val reqObj: EntityValue = JsonHelper.mapper.readValue(msg.bodyStr, jacksonTypeRef())

    @Suppress("UNCHECKED_CAST")
    val moves = (reqObj["move_task_list"] as List<EntityValue>).map {
      Step3066(
        id = it["id"] as String,
        taskId = it["task_id"] as String,
        sourceId = it["source_id"] as String,
        reachAngle = (it["reach_angle"] as Double?) ?: PI,
        operation = it["operation"] as String?,
        scriptArgs = it["script_args"] as Map<String, Any>?,
        scriptName = it["script_name"] as String?,
        binTask = it["binTask"] as String?,
        recognize = (it["recognize"] as Boolean?) ?: false,
        method = it["method"] as String?,
        moveAngle = it["move_angle"] as Double?,
        speedW = it["speed_w"] as Double?,
        skillName = it["skill_name"] as String?,
        locMode = it["loc_mode"] as Int?,
        startRotDir = it["start_rot_dir"] as Int?,
        percentage = it["percentage"] as Double?,
        binIndex = StringUtils.firstNonBlank(it["#containerName"] as String?, it["container_name"] as String?),
        goodsId = StringUtils.firstNonBlank(it["goods_id"] as String?, it["goodsId"] as String?),
        spin = it["spin"] as Boolean?,
        goodsDir = it["goods_dir"] as Double?,
      )
    }

    try {
      val movesRuntimes: MutableList<MoveTaskRuntime> = mutableListOf()
      val currentTimeMillis = System.currentTimeMillis()
      val firstMove = moves.first()
      // var moveTaskList = LinkedBlockingQueue<List<MoveTaskRuntime>>()
      if (prepareMove(mr, firstMove)) return JsonHelper.writeValueAsString(mutableMapOf("ret_code" to 0))
      // 移动时重新刷新配置
      mr.moveConfig = MockService.getMoveConfig(mr.config.sceneId, mr.config.name)
      moves.forEach { move ->
        if (move.id != "SELF_POSITION" && move.sourceId != "SELF_POSITION") {
          val targetPoint = MockMoveProcessor.getPointHelper(mr, move.id)
          val sourcePoint = MockMoveProcessor.getPointHelper(mr, move.sourceId)

          handlePointValidation(mr, targetPoint, move.id, "target")?.let { return it }
          handlePointValidation(mr, sourcePoint, move.sourceId, "source")?.let { return it }

          val pathHelper =
            MockMoveProcessor.getPathHelper(mr, sourcePoint!!.point.instanceName, targetPoint!!.point.instanceName)
              ?: return handleError(mr, "path plan failed", "52702", move.sourceId, move.id)

          val spline = SmapCurveHelper.transferPathToSpline(pathHelper.path)
          val pathKey = MockMoveProcessor.getPathKey(sourcePoint.point.instanceName, targetPoint.point.instanceName)

          val distance = SmapCurveHelper.getLength(spline, pathHelper.path)
          // 根据正走还是倒走的不同，其目标角度不同
          val isMovingForward = getMoveMethod(move, pathHelper)
          val steps = MockMoveProcessor.calculateSteps(
            distance,
            if (isMovingForward) mr.moveConfig?.maxSpeed!! else mr.moveConfig?.maxBackSpeed!!,
          )
          val curvePoint2D = SmapCurveHelper.getPathPositionAndAngle(spline, steps, pathHelper.path.className, 0.0)

          MockMoveProcessor.addSplineMap(mr, pathKey, pathHelper, spline)
          MockMoveProcessor.addCurvePoint2DMap(mr, pathKey, curvePoint2D)
        }

        // 取货前校验能否取这个容器
        MockContainersProcessor.checkSelfBinsBeforeMove(mr, move)

        val duration = System.currentTimeMillis() - currentTimeMillis
        logger.info("仿真机器人 ${mr.config.name} 处理 3066 请求耗时: $duration ms")
      }

      synchronized(mr.lock) {
        // 告警检查
        if (mr.record.alarms.any { it.level == "Error" } &&
          (mr.currentTask?.status == MoveTaskStatus.Failing || mr.currentTask?.status == MoveTaskStatus.Failed)
        ) {
          // 4. 如果 rbk 任务失败并且 保持在 Error 状态 ，那么接下来调度下发的任务，都会变成取消状态。直到Error被清除。
          moves.forEach {
            val moveRuntime = MoveTaskRuntime(taskId = it.taskId, step3066 = it, status = MoveTaskStatus.Cancelled)
            mr.tasks[it.taskId] = moveRuntime
          }
          return JsonHelper.writeValueAsString(mutableMapOf("ret_code" to 0))
        }

        // 控制权检查
        if (mr.record.currentLock.locked && mr.record.currentLock.nickName != RobotService.NICKNAME) {
          return JsonHelper.writeValueAsString(
            mutableMapOf(
              "ret_code" to 40020,
              "err_msg" to "control is preempted..., can't execute any standalone operation",
            ),
          )
        }
        moves.forEach {
          val moveRuntime = MoveTaskRuntime(taskId = it.taskId, step3066 = it, status = MoveTaskStatus.Init)
          movesRuntimes.add(moveRuntime)
          mr.tasks[it.taskId] = moveRuntime
        }
      }

      MockMoveProcessor.putMoveTaskList(mr, movesRuntimes)
      return JsonHelper.writeValueAsString(mutableMapOf("ret_code" to 0))
    } catch (e: Exception) {
      MockAlarmProcessor.addOrUpdateAlarm(
        mr,
        MockSeerRobotAlarm("Error", "5005", e.message ?: "Unknown exception"),
      )
      MockStore.updateMockRobotRecordAsync(mr.record)
      return JsonHelper.writeValueAsString(mutableMapOf("ret_code" to -1, "err_msg" to e.getTypeMessage()))
    }
  }

  /**
   * 预处理，判断路径是否连续
   */
  private fun prepareMove(mr: MockSeerRobotRuntime, firstMove: Step3066): Boolean {
    val flatMoves = mr.moveTaskList.flatMap { it }
    val previousMove = flatMoves.lastOrNull { it.step3066.id != "SELF_POSITION" }
    if (firstMove.id != "SELF_POSITION" && previousMove != null && firstMove.sourceId != previousMove.step3066.id) {
      // 如果当前路径的起点和前一路径的终点不相同，代表路径不一致
      MockAlarmProcessor.addOrUpdateAlarm(
        mr,
        MockSeerRobotAlarm(
          "Error",
          "52201",
          "3066 Robot sending task error," +
            " cur_source_name = ${firstMove.sourceId}, cur_task_id = ${firstMove.taskId}, " +
            "last_target_name = ${previousMove.step3066.id}, last_task_id = ${previousMove.step3066.taskId}",
        ),
      )
      logger.error("${mr.config.name} 3066 Robot sending task error," +
        " cur_source_name = ${firstMove.sourceId}, cur_task_id = ${firstMove.taskId}, " +
        "last_target_name = ${previousMove.step3066.id}, last_task_id = ${previousMove.step3066.taskId}")
      MockStore.updateMockRobotRecordAsync(mr.record)
      // 设置机器人导航状态为取消
      mr.currentTask?.let { task ->
        val taskRuntime = task.copy(status = MoveTaskStatus.Cancelled)
        mr.updateCurrentTask(taskRuntime)
      }
      flatMoves.forEach {
        mr.tasks[it.taskId] = it.copy(status = MoveTaskStatus.Cancelled)
      }
      mr.moveTaskList.clear()
      return true
    }
    return false
  }

  // 提取一个公共方法，处理位点查找失败的情况
  private fun handlePointValidation(
    mr: MockSeerRobotRuntime,
    point: PointHelper?,
    pointId: String,
    pointType: String,
  ): String? = if (point == null) {
    MockAlarmProcessor.addOrUpdateAlarm(
      mr,
      MockSeerRobotAlarm("Error", "52701", "无法找到 $pointType 位点 id: $pointId"),
    )
    JsonHelper.writeValueAsString(
      mutableMapOf(
        "ret_code" to -1,
        "err_msg" to "无法找到 $pointType 位点 id: $pointId",
      ),
    )
  } else {
    null
  }

  // 提取一个错误处理的公共方法
  private fun handleError(
    mr: MockSeerRobotRuntime,
    msg: String,
    errorCode: String,
    sourceId: String,
    targetId: String,
  ): String {
    MockAlarmProcessor.addOrUpdateAlarm(
      mr,
      MockSeerRobotAlarm("Error", errorCode, "$msg from: $sourceId to: $targetId"),
    )
    return JsonHelper.writeValueAsString(
      mutableMapOf(
        "ret_code" to -1,
        "err_msg" to "无法找到路径: from $sourceId to $targetId",
      ),
    )
  }

  /**
   * 抢占控制权
   */
  fun handle4005(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    logger.info("${mr.config.name} handle 4005 msg: ${msg.bodyStr}")
    val reqObj: EntityValue = JsonHelper.mapper.readValue(msg.bodyStr, jacksonTypeRef()) ?: return ""
    val req4055 = Req4005(
      nickName = reqObj["nick_name"] as String,
    )
    mr.record.currentLock.locked = true // 先抢
    mr.record.currentLock.nickName = req4055.nickName
    mr.record.currentLock.address = msg.address
    mr.setRecord(mr.record.copy(master = req4055.nickName))
    return JsonHelper.writeValueAsString(mutableMapOf("ret_code" to 0))
  }

  /**
   * 释放控制权
   */
  fun handle4006(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    logger.info("${mr.config.name} handle 4006 msg: ${msg.bodyStr}")
    mr.record.currentLock.nickName = ""
    mr.record.currentLock.address = ""
    mr.record.currentLock.locked = false // 最后释放
    mr.setRecord(mr.record.copy(master = null))
    return JsonHelper.writeValueAsString(mutableMapOf("ret_code" to 0))
  }

  /**
   * 清除机器人当前所有报错（其实是只清除 Error 告警级别）
   */
  fun handle4009(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    val alarms = mr.record.alarms.toMutableList()
    alarms.removeIf { it.level == "Error" }
    val record = mr.record.copy(alarms = alarms)
    mr.setRecord(record)
    MockStore.updateMockRobotRecordAsync(record)
    return JsonHelper.writeValueAsString(mutableMapOf("ret_code" to 0))
  }

  /**
   * 上传地图到机器人，只做上传
   *
   * https://seer-group.feishu.cn/wiki/QN8nw6YebiF41kk72bocW6hunbh
   */
  fun handle4010(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    logger.info("${mr.config.name} handle 4010")
    val smap: Smap = JsonHelper.mapper.readValue(msg.bodyStr, jacksonTypeRef())
    // smap 中的 mapName 是不带后缀名的
    val mapName = "${smap.header.mapName}.smap"
    uploadToRobot(mr, mapName, msg.bodyStr)
    return JsonHelper.mapper.writeValueAsString(mapOf("ret_code" to 0))
  }

  /**
   * 上传文件到仿真机器人上
   */
  private fun uploadToRobot(mr: MockSeerRobotRuntime, mapName: String, uploadContent: String) {
    // 与导入时的 smap 保存路径保持一致
    val m4MapsDir = SceneFileService.getSceneRobotMapsDir(mr.config.sceneId)
    val targetFile = File(File(m4MapsDir, mr.config.name).apply { mkdirs() }, mapName)
    FileUtils.writeStringToFile(targetFile, uploadContent, StandardCharsets.UTF_8)

    // 直接根据文件内容获取 MD5，不使用读取文件流来获取 MD5，会有并发风险
    val md5 = DigestUtils.md5Hex(uploadContent)
    val newMapList = mr.config.maps.toMutableList()
    // 先删除相同文件
    newMapList.removeIf { it.mapMd5 == md5 || it.mapName == mapName }
    // 将这张地图加到仿真机器人的配置中
    newMapList.add(MockSeerRobotMap(mapName, SceneFileService.fileToPath(mr.config.sceneId, targetFile), md5))
    mr.setConfig(mr.config.copy(maps = newMapList))

    val robotConfig = MockService.config.robots.find { it.id == mr.robotId }
    if (robotConfig != null) {
      synchronized(MockService) {
        val robots = MockService.config.robots.toMutableList()
        robots.removeIf { it.id == mr.robotId }
        robots.add(mr.config)
        // 保存仿真配置
        MockService.saveMockConfig(MockService.config.copy(robots = robots))
      }
    }
  }

  /**
   * 从机器人下载地图
   *
   * https://seer-group.feishu.cn/wiki/MQSKwwKIti5Yl4k6LyLcSkZbnXg
   */
  fun handle4011(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    logger.info("${mr.config.name} handle 4011 msg: ${msg.bodyStr}")

    val req: MockMapNameReq = JsonHelper.mapper.readValue(msg.bodyStr, jacksonTypeRef())

    // mapName 目前存的时候是带后缀名，而 rbk 4011 接口请求时不需要后缀名，为保持一致，将请求的文件名加后缀匹配
    val m = mr.config.maps.firstOrNull { it.mapName == "${req.mapName}.smap" }
      ?: return JsonHelper.writeValueAsString(mapOf("ret_code" to 40051, "err_msg" to "no this map file"))
    val file = SceneFileService.pathToFile(mr.config.sceneId, m.mapFile)
    return if (file.exists()) {
      FileUtils.readFileToString(file, StandardCharsets.UTF_8)
    } else {
      JsonHelper.writeValueAsString(mapOf<String, Any>("ret_code" to 40051, "err_msg" to "no this map file"))
    }
  }

  /**
   * 删除机器人地图
   *
   * https://seer-group.feishu.cn/wiki/TsgowkwkUifHZtkXJyUcBC17n9f
   */
  fun handle4012(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    logger.info("${mr.config.name} handle 4012")

    val req: MockMapNameReq = JsonHelper.mapper.readValue(msg.bodyStr, jacksonTypeRef())

    val m = mr.config.maps.firstOrNull { it.mapName == req.mapName }
      ?: return JsonHelper.writeValueAsString(mapOf("ret_code" to 40051, "err_msg" to "no this map file"))

    val file = SceneFileService.pathToFile(mr.config.sceneId, m.mapFile)
    file.deleteOnExit()

    // 从仿真机器人的配置中移除这个地图
    mr.setConfig(mr.config.copy(maps = mr.config.maps.filter { it.mapName != req.mapName }))

    return JsonHelper.writeValueAsString(mapOf("ret_code" to 0))
  }

  /**
   * 上传模型文件到机器人
   *
   * https://seer-group.feishu.cn/wiki/MPzKwfCQvizVS7kV9b3cvadlnUg
   */
  fun handle4200(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    logger.info("${mr.config.name} handle 4200")

    val model: RbkModel = JsonHelper.mapper.readValue(msg.bodyStr, jacksonTypeRef())
    // 上传时必须有底盘模型
    JsonHelper.mapper.readTree(msg.bodyStr)?.get("deviceTypes")?.asIterable() // deviceTypes 是 JSON 对象数组
      ?.find { it["name"]?.asText() == "chassis" }
      ?.get("devices")?.asIterable() // devices 是 JSON 对象数组
      ?.find { it["name"]?.asText() == "chassis" }
      ?.get("deviceParams")?.asIterable() // deviceParams 是 JSON 对象数组
      ?.find { it["key"]?.asText() == "shape" }
      ?.get("comboParam")
      ?.get("childParams")?.asIterable() // childParams 是 JSON 对象数组
      ?.find { it["key"]?.asText() == "rectangle" }
      ?.get("params")?.asIterable() // params 是 JSON 对象数组
      ?: return JsonHelper.writeValueAsString(
        mapOf(
          "ret_code" to 40069,
          "err_msg" to "model save error",
        ),
      )

    mr.updateAndSaveMockRobotModel(model)

    return JsonHelper.writeValueAsString(mapOf("ret_code" to 0))
  }

  /**
   * 设置仿真机器人的软急停状态
   *  参考 RBK 的软急停表现：
   *    1.不用修改仿真机器人的控制权状态和控制权所有者。
   */
  fun handle6004(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    logger.info("${mr.config.name} handle 6004")

    val req: Req6004 = JsonHelper.mapper.readValue(msg.bodyStr, jacksonTypeRef())
    mr.setRecord(mr.record.copy(softEmc = req.status))
    MockStore.updateMockRobotRecordAsync(mr.record)

    return JsonHelper.writeValueAsString(mapOf("ret_code" to 0))
  }

  /**
   * 清除单负载仿真机器人的载货状态
   */
  fun handle6080(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    logger.info("${mr.config.name} handle 6080, but do nothing...")
    // TODO: 先不做具体的实现，确保可以正常调用此接口即可。
    return JsonHelper.writeValueAsString(mapOf("ret_code" to 0))
  }

  /**
   * 根据容器号清空机器人背篓
   *
   * https://seer-group.feishu.cn/wiki/G4b9wL1ALirG65kdIomcUzXnnah
   */
  fun handle6801(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    logger.info("${mr.config.name} handle 6801")

    val req: ClearSelfBinByContainerIdReq = JsonHelper.mapper.readValue(msg.bodyStr, jacksonTypeRef())
    MockContainersProcessor.clearSelfBinByContainerId(mr, req.containerId)

    return JsonHelper.writeValueAsString(mapOf("ret_code" to 0))
  }

  /**
   * 清空指定机器人库位
   *
   * https://seer-group.feishu.cn/wiki/FC7swWwwaiUNSykNoZUc1kqJnMe
   */
  fun handle6802(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    logger.info("${mr.config.name} handle 6802")

    val req: ClearSelfBinReq = JsonHelper.mapper.readValue(msg.bodyStr, jacksonTypeRef())
    MockContainersProcessor.clearSelfBin(mr, req)
    return JsonHelper.writeValueAsString(mapOf("ret_code" to 0))
  }

  /**
   * 解绑所有机器人库位
   *
   * https://seer-group.feishu.cn/wiki/MPfvwpE4lihsOVkZZ7RcDSupnHd
   */
  fun handle6803(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    logger.info("${mr.config.name} handle 6803")

    MockContainersProcessor.clearAllSelfBins(mr)

    return JsonHelper.writeValueAsString(mapOf("ret_code" to 0))
  }

  /**
   * 绑定容器到机器人库位
   *
   * https://seer-group.feishu.cn/wiki/Eo5wwuZMQiJVOektzDScFAxNnqe
   */
  fun handle6804(mr: MockSeerRobotRuntime, msg: RbkFrame): String {
    logger.info("${mr.config.name} handle 6804")

    val req: BindContainerSelfBinReq = JsonHelper.mapper.readValue(msg.bodyStr, jacksonTypeRef())
    MockContainersProcessor.bindContainerToSelfBin(mr, req)

    return JsonHelper.writeValueAsString(mapOf("ret_code" to 0))
  }

  /**
   * 针对部分接口，校验请求方是否有目标机器人的控制权。
   *
   * 对于需要校验控制权的 API 直接在对应的 handle 方法中调用此方法即可。
   *
   * 如何判断请求方有目标机器人的控制权？
   *  1. 目标机器人的控制权必须是被抢占状态。
   *  2. 目标机器人的控制权的所有者的 IP 地址，与请求方的 IP 地址相同。 不考虑 IPV6
   *    a 请求中不会包含 nick_name，所以仿真机器人无法根据请求中的 nick_name 判断机器人的控制权。
   *    b 根据请求方 IP 地址校验控制权的局限性：
   *      在复杂的组网环境中，可能无法通过校验请求方的 IP 地址来判断请求方是否已有控制权。
   *      但是仿真机器人通常是本地使用的，不会遇到此问题。
   *      TODO 等有必要时再解决此问题。
   *        方式 1：请求发给仿真机器人发送请求时，请求内容增加 nick_name 字段，便于仿真机器校验；但是不能给实车发这个字段。
   */
  fun ensureCurrentLock(mr: MockSeerRobotRuntime, apiNo: Int, address: String): Map<String, Any>? {
    // 如果不需要校验控制权，则直接返回。
    if (apiNo in ignoreApiNoList) return null

    val currentLock = mr.record.currentLock

    // 强制校验，请求方没有控制权时无法控制机器人。
    // 但是对于 RBK 来说，没有控制权时，也是会响应控制请求的。
    if (!currentLock.locked) return ensureCurrentLockFailed()

    // 请求方没有目标机器人的控制权
    if (currentLock.address != address) return ensureCurrentLockFailed()

    // 目前无法根据 nick_name 校验控制权

    return null
  }

  private fun ensureCurrentLockFailed() = mapOf(
    "create_on" to Date(),
    "ret_code" to 40020,
    "err_msg" to "control is preempted..., can't execute any standalone operation",
  )
}

data class Step3066(
  val id: String, // 目标站点
  val taskId: String, // 任务对应的唯一 id
  val sourceId: String, // 起始站点
  val reachAngle: Double, // 到点精度
  val operation: String? = null, // 终点动作
  val scriptArgs: Map<String, Any>? = null, // 脚本参数
  val scriptName: String? = null, // 脚本名称
  val binTask: String? = null, // 到点库位动作
  val recognize: Boolean = false, // 是否是识别动作
  val method: String? = null, // 只能 forward (正走)， 或 backward (倒走)，如果缺省则使用路径上配置的方式
  val moveAngle: Double? = null, // 旋转角度
  val speedW: Double? = null, // 角速度
  val skillName: String? = null, // 任务类型，原地旋转时只能为 GoByOdometer
  val locMode: Int? = null, // 定位模式。1 表示激光定位，0 或者其他 表示按里程定位。 默认值为 0
  val startRotDir: Int? = null, // 旋转方向，-1: 顺时针转 0: 朝近的方向旋转 1: 逆时针转
  val percentage: Double? = null, // 线路百分比
  val binIndex: String? = null, // 机器人背篓索引
  val goodsId: String? = null, // 货物id
  val increaseSpinAngle: Double? = null, // 托盘旋转的弧度，正数则逆时针旋转，为负数顺时针旋转
  var spin: Boolean? = null, // 是否随动
  val goodsDir: Double? = null, // 货物朝向，单位角度
)

data class Step3051(val id: String, val taskId: String)

data class Step(val id: String, val taskId: String)

data class Req4005(val nickName: String)

/**
 * 6004 设置仿真机器人的软急停状态。
 *
 * @param status true: 让仿真机器人处于软急停状态；false：取消仿真机器人的软急停状态。
 */
data class Req6004(val status: Boolean)

/**
 * 6801 根据 containerId 清空机器人指定背篓
 */
data class ClearSelfBinByContainerIdReq(
  @JsonProperty("goods_id")
  val containerId: String, // 容器号
)

/**
 * 6802 根据 binIndex 清空机器人指定背篓
 */
data class ClearSelfBinReq(
  @JsonProperty("container_name")
  val binIndex: String, // 库位编号，即 binIndex
)

/**
 * 6804 绑定容器到指定背篓
 */
data class BindContainerSelfBinReq(
  @JsonProperty("goods_id")
  val containerId: String, // 容器 ID
  @JsonProperty("container_name")
  val binIndex: String, // 库位编号，即 binIndex
  @JsonProperty("desc")
  val desc: String, // 描述
)