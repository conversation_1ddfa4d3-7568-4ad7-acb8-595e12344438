package com.seer.trick.fleet

import com.seer.trick.I18N.lo
import com.seer.trick.base.AppModule
import com.seer.trick.base.alarm.AlarmGroup
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.falcon.FalconCenter
import com.seer.trick.falcon.domain.BlockDefGroup
import com.seer.trick.fleet.falcon.FleetOrderResourceCleaner
import com.seer.trick.fleet.falcon.bp.*
import com.seer.trick.fleet.handler.*
import com.seer.trick.fleet.mars.*
import com.seer.trick.fleet.mars.bp.DirectOrderExecuteBp
import com.seer.trick.fleet.mars.bp.DirectOrderMoveBp
import com.seer.trick.fleet.service.*
import org.graalvm.polyglot.Value

/**
 * 三代调度模块
 */
object FleetModule : AppModule() {

  override fun init() {
    AlarmService.registerGroup(AlarmGroup("Fleet", lo("labelFleet"), 15))
  }

  override fun registerHttpHandlers() {
    SceneHandler.registerHandlers()
    DeviceHandler.registerHandlers()
    MockHandler.registerHandlers()
    RobotHandler.registerHandlers()
    OrderHandler.registerHandlers()
    DevHandler.registerHandlers()
    OperationRecordHandler.registerHandlers()
    SyncToRobotHandler.registerHandlers()
    DiagnosisHandler.registerHandlers()
    ExternalMapResHandler.registerHandlers()
  }

  override fun beforeScript() {
    DirectOrderResourceCleaner.init()
    FleetOrderResourceCleaner.init()
  }

  override fun afterScript() {
    FleetService.init()

    // 临时版光通讯
    MarsCenter.init()
  }

  override fun dispose() {
    FleetService.dispose()
  }

  override fun putMoreScriptBindings(bindings: Value) {
    bindings.putMember("mars", MarsScript)
    bindings.putMember("fleet3", FleetScriptApi)
  }

  override fun registerMoreBp() {
    val directOrderBlocks = listOf(
      FalconCenter.registerBp(DirectOrderExecuteBp::class, DirectOrderExecuteBp.def),
      FalconCenter.registerBp(DirectOrderMoveBp::class, DirectOrderMoveBp.def),
    )
    directOrderBlocks.forEach { it.color = "rgba(3,205,142,0.4)" }
    FalconCenter.registerBpGroup(
      BlockDefGroup("DirectOrder", "", 30, false, directOrderBlocks),
    )

    val transportOrderBlocks = listOf(
      FalconCenter.registerBp(CreateTransportOrderBp::class, CreateTransportOrderBp.def),
      FalconCenter.registerBp(AddStepAndWaitCompleteBp::class, AddStepAndWaitCompleteBp.def),
      FalconCenter.registerBp(AddTransportStepBp::class, AddTransportStepBp.def),
      FalconCenter.registerBp(WaitStepCompleteBp::class, WaitStepCompleteBp.def),
      FalconCenter.registerBp(CompleteTransportOrderBp::class, CompleteTransportOrderBp.def),
    )
    transportOrderBlocks.forEach { it.color = "rgba(3,205,142,0.3)" }
    FalconCenter.registerBpGroup(
      BlockDefGroup("TransportOrder", "", 15, false, transportOrderBlocks),
    )
  }
}