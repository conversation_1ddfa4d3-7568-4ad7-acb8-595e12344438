package com.seer.trick.fleet.traffic.distributed.lock.tree

import com.seer.trick.fleet.traffic.distributed.lock.graph.BoundingBox
import com.seer.trick.fleet.traffic.distributed.lock.model.SpaceLock
import com.seer.trick.fleet.traffic.distributed.map.*
import org.slf4j.LoggerFactory
import java.util.concurrent.*
import java.util.concurrent.atomic.*
import java.util.concurrent.locks.*

object QuadTree {

  private val logger = LoggerFactory.getLogger(javaClass)

  private val maxDeep = 5

  private var rootTree: MutableMap<String, QuadTreeNode> = ConcurrentHashMap()

  // 互斥区域
  private val blockAreas = ConcurrentHashMap<String, MutableList<BlockArea>>()

  val ab = AtomicBoolean(false)

  // 读写锁
  private val readWriteLock = ReentrantReadWriteLock()
  private val readLock = readWriteLock.readLock()
  private val writeLock = readWriteLock.writeLock()

  fun initBlockArea(bas: MutableMap<String, MutableList<BlockArea>>) {
    bas.forEach { (t, u) -> blockAreas[t] = u }
  }

  fun init(map: Map<String, MapArea>) {
    map.forEach { (mapName, area) -> buildTreeNode(mapName, area) }
  }

  fun update(mapName: String, area: MapArea) {
    val lock = writeLock.tryLock(100, TimeUnit.MILLISECONDS)
    if (!lock) {
      logger.error("$mapName |update map can not get write lock for quad tree")
      return
    }
    try {
      val currentMapAllSpaceLock = queryCurrentMapAllSpaceLock(mapName)
      buildTreeNode(mapName, area)
      currentMapAllSpaceLock.forEach { spaceLock -> insert(spaceLock.box, spaceLock, mapName) }
    } catch (e: Exception) {
      logger.error("$mapName |update map can not get write lock for quad tree", e)
    } finally {
      writeLock.unlock()
    }
  }

  private fun buildTreeNode(mapName: String, area: MapArea) {
    // 构建根节点
    val rootNode = QuadTreeNode(0, mapName)
    // 构建区域
    val boundingBox = BoundingBox(
      area.bound.minX ?: 0.0,
      area.bound.maxY ?: 0.0,
      area.bound.maxX
        ?: 0.0,
      area.bound.minY ?: 0.0,
    )
    // 构建节点
    rootNode.boundingBox = boundingBox
    buildTree(rootNode, mapName)
    insertMapInfo(rootNode, area)
    rootTree[mapName] = rootNode
  }

  private fun buildTree(node: QuadTreeNode, mapCode: String) {
    if (node.deep < maxDeep) {
      val region = node.boundingBox!!
      // 构建四个节点
      val midWidth = (region.left + region.right) / 2
      val midHeight = (region.top + region.bottom) / 2
      if (midWidth - region.left < 5 || midHeight - region.bottom < 5) {
        return
      }
      val ltNode =
        QuadTreeNode(node.deep + 1, mapCode, node, BoundingBox(region.left, region.top, midWidth, midHeight), 0)
      val rtNode =
        QuadTreeNode(node.deep + 1, mapCode, node, BoundingBox(midWidth, region.top, region.right, midHeight), 1)
      val lbNode =
        QuadTreeNode(node.deep + 1, mapCode, node, BoundingBox(region.left, midHeight, midWidth, region.bottom), 2)
      val rbNode =
        QuadTreeNode(node.deep + 1, mapCode, node, BoundingBox(midWidth, midHeight, region.right, region.bottom), 3)
      node.child[node.leftTop] = ltNode
      node.child[node.rightTop] = rtNode
      node.child[node.leftBottom] = lbNode
      node.child[node.rightBottom] = rbNode
    }
    // 构建子节点
    if (node.child.isNotEmpty()) {
      for (node in node.child) {
        buildTree(node.value, mapCode)
      }
    }
  }

  /**
   * 插入锁闭元素到死叉树中
   * */
  fun insert(box: BoundingBox, element: SpaceLock, mapCode: String): Boolean {
    // 定位在哪个节点中插入
    val root = rootTree[mapCode] ?: return false // todo 抛出异常
    val node = findNode(root, box)
    val lock = writeLock.tryLock(100, TimeUnit.MILLISECONDS)
    if (!lock) {
      logger.error("{} |space lock can not get write lock for quad tree", element.getKey())
      return false
    }
    try {
      val spaceMap: MutableMap<String, SpaceLock> = findRobotSpaceLock(node)
      for (space in spaceMap) {
        if (space.value.getKey() == element.getKey()) continue
        if (space.value.checkCollision(element)) {
          logger.error("{} |space lock can not insert for quad tree collision with {}", element.getKey(), space.key)
          return false
        }
      }
      // 删除y节点锁 信息
      val deleteNode = findNode(root, element.getKey())
      if (deleteNode != null) {
        deleteNode.remove(element.getKey())
        logger.info("{} |remove space lock {}", element.getKey(), System.currentTimeMillis())
      }
      node.insert(element)
      logger.info("{} |insert space lock {}", element.getKey(), System.currentTimeMillis())
    } finally {
      writeLock.unlock()
    }
    return true
  }

  private fun findRobotSpaceLock(node: QuadTreeNode): MutableMap<String, SpaceLock> {
    val robotSpaceLock = node.findChildRobotSpaceLock()
    if (node.parent != null) {
      robotSpaceLock.putAll(node.parent!!.findParentRobotSpaceLock())
    }

    return robotSpaceLock
//    val spaceMap: MutableMap<String, SpaceLock> = mutableMapOf()
//    spaceMap.putAll(node.collision.filter { it.value.type == LockType.ROBOT })
//    if (node.child.isEmpty()) {
//      return spaceMap
//    }
//    for (node in node.child) {
//      spaceMap.putAll(findRobotSpaceLock(node.value))
//    }
//    return spaceMap
  }

  /**
   * 寻找到节点
   * */
  private fun findNode(node: QuadTreeNode, box: BoundingBox): QuadTreeNode {
    // 节点是否为叶子节点
    if (node.child.isEmpty()) {
      return node
    }
    var result: QuadTreeNode = node
    for (node in node.child) {
      val b = node.value.boundingBox ?: continue
      if (b.intersect(box)) {
        if (b.onRegion(box)) {
          return findNode(node.value, box)
        }
        return result
      }
    }
    return result
  }

  fun findNode(node: QuadTreeNode, key: String): QuadTreeNode? {
    // 节点是否为叶子节点
    if (node.collision.keys.contains(key)) {
      return node
    }
    if (node.child.isEmpty()) {
      return null
    }
    for (child in node.child.values) {
      val cNode = findNode(child, key)
      if (cNode != null) {
        return cNode
      }
    }
    return null
  }

  /**
   *  通过区域查询
   * */
  fun queryByBounding(box: BoundingBox, mapCode: String): MutableList<SpaceLock> {
    val root = rootTree[mapCode] ?: return mutableListOf()
    val lock = readLock.tryLock(100, TimeUnit.MILLISECONDS)
    if (!lock) {
      logger.error("{} | can not get read lock for quad tree", box.toString())
      return mutableListOf()
    }
    try {
      val node = findNode(root, box)
      return findAllSpaceLock(node)
    } finally {
      readLock.unlock()
    }
  }

  private fun findAllSpaceLock(node: QuadTreeNode): MutableList<SpaceLock> {
    val spaceLocks = node.findAllChildSpaceLock()
    if (node.parent != null) {
      spaceLocks.addAll(node.parent!!.findAllParentSpaceLock())
    }
    return spaceLocks
//    val spaceMap: MutableList<SpaceLock> = mutableListOf()
//    spaceMap.addAll(node.collision.values)
//    if (node.child.isEmpty()) {
//      return spaceMap
//    }
//    for (node in node.child) {
//      spaceMap.addAll(findAllSpaceLock(node.value))
//    }
//    return spaceMap
  }

  fun queryCurrentMapAllSpaceLock(mapCode: String): MutableList<SpaceLock> {
    val root = rootTree[mapCode] ?: return mutableListOf()
    val lock = readLock.tryLock(100, TimeUnit.MILLISECONDS)
    if (!lock) {
      logger.error("{} | can not get read lock for quad tree", mapCode)
      return mutableListOf()
    }
    try {
      return findAllSpaceLock(root)
    } finally {
      readLock.unlock()
    }
  }

  fun queryAllSpaceLock(): MutableMap<String, MutableList<SpaceLock>> {
    val lock = readLock.tryLock(100, TimeUnit.MILLISECONDS)
    if (!lock) {
      logger.error("can not get read lock for quad tree")
      return mutableMapOf()
    }
    try {
      val spaceMap: MutableMap<String, MutableList<SpaceLock>> = mutableMapOf()
      for (key in rootTree.keys) {
        spaceMap[key] = queryCurrentMapAllSpaceLock(key)
      }
      return spaceMap
    } finally {
      readLock.unlock()
    }
  }

  /**
   *  通过主键查询
   * */
  fun queryByKey(key: String, mapCode: String): SpaceLock? {
    val root = rootTree[mapCode] ?: return null
    val lock = readLock.tryLock(100, TimeUnit.MILLISECONDS)
    if (!lock) {
      logger.error("{} | can not get read lock for quad tree", key)
      return null
    }
    try {
      return root.query(key)
    } finally {
      readLock.unlock()
    }
  }

  fun remove(key: String, mapCode: String): Boolean {
    val root = rootTree[mapCode] ?: return false // todo 抛出异常
    val lock = writeLock.tryLock(100, TimeUnit.MILLISECONDS)
    if (!lock) {
      logger.error("{} | can not get read lock for quad tree", key)
      return false
    }
    try {
      return root.remove(key)
    } finally {
      writeLock.unlock()
    }
  }

  /**
   *  处理地图信息，将地图 点 和 线 信息 插入到树中
   * */
  private fun insertMapInfo(rootNode: QuadTreeNode, mapArea: MapArea) {
    val points = mapArea.points
    val lines = mapArea.lines
    // 更新点的信息
    for (p in points.values) {
      val node = findTreeNode(rootNode, p)
      node.treePoint.add(p)
    }
    // 更新线的信息
    for (l in lines.values) {
      val node = findTreeNode(rootNode, l)
      node.treeEdge.add(l)
    }
  }

  private fun findTreeNode(rootNode: QuadTreeNode, p: Point): QuadTreeNode {
    if (rootNode.child.isEmpty()) {
      return rootNode
    }
    for (node in rootNode.child.values) {
      val boundingBox = node.boundingBox ?: continue
      if (boundingBox.intersect(p.x, p.y)) {
        if (boundingBox.onRegion(p.x, p.y)) {
          return findTreeNode(node, p)
        }
        return node
      }
    }
    return rootNode
  }

  private fun findTreeNode(rootNode: QuadTreeNode, l: Line): QuadTreeNode {
    if (rootNode.child.isEmpty()) {
      return rootNode
    }
    for (node in rootNode.child.values) {
      val boundingBox = node.boundingBox ?: continue
      if (boundingBox.intersect(l.start.x, l.start.y) &&
        boundingBox.intersect(l.end.x, l.end.y)
      ) {
        if (boundingBox.onRegion(l.start.x, l.start.y) &&
          boundingBox.onRegion(l.end.x, l.end.y)
        ) {
          return findTreeNode(node, l)
        }
        return node
      }
    }
    return rootNode
  }

  // 查找给定区域内的点
  fun findPointsInBounding(box: BoundingBox, mapCode: String): MutableList<Point> {
    val node = rootTree[mapCode]?.let {
      val findNode = findNode(it, box)
      findNode
    } ?: return mutableListOf()

    val points = node.queryTreePoints()
    val result: MutableList<Point> = mutableListOf()
    for (p in points) {
      if (box.onRegion(p.x, p.y)) {
        result.add(p)
      }
    }
    return result
  }

  // 查找给定区域内的边
  fun findLinesInBounding(box: BoundingBox, mapCode: String): MutableList<Line> {
    val node = rootTree[mapCode]?.let {
      val findNode = findNode(it, box)
      findNode
    } ?: return mutableListOf()
    // todo 对于相交的边可能存在遗落的情况
    val lines = node.queryTreeLines()
    val result: MutableList<Line> = mutableListOf()
    for (l in lines) {
      if (box.onRegion(l.start.x, l.start.y) ||
        box.onRegion(l.end.x, l.end.y)
      ) {
        result.add(l)
      }
    }
    return result
  }

  // 查询地图内的互斥区域
  fun findBlockAreaByMapName(mapName: String): MutableList<BlockArea> = blockAreas[mapName] ?: mutableListOf()
  fun querySceneSpaceLock(sceneId: String): MutableMap<String, MutableList<SpaceLock>> {
    val lock = readLock.tryLock(100, TimeUnit.MILLISECONDS)
    if (!lock) {
      logger.error("can not get read lock for quad tree")
      return mutableMapOf()
    }
    try {
      val spaceMap: MutableMap<String, MutableList<SpaceLock>> = mutableMapOf()
      for (key in rootTree.keys) {
        if (key.startsWith(sceneId)) {
          spaceMap[key] = queryCurrentMapAllSpaceLock(key)
        }
      }
      return spaceMap
    } finally {
      readLock.unlock()
    }
  }
}