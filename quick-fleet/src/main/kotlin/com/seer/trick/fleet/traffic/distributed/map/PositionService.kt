package com.seer.trick.fleet.traffic.distributed.map

import com.seer.trick.fleet.traffic.distributed.lock.LockService
import com.seer.trick.fleet.traffic.distributed.lock.graph.*
import org.slf4j.LoggerFactory

/**
 *  位置信息
 * */
object PositionService {

  private val logger = LoggerFactory.getLogger(javaClass)

  private const val FIX_MIN_DISTANCE = 0.5

  fun findPerhapsPosition(site: Site, box: BoundingBox, sceneId: String, mapName: String): Point? {
    var point: Point? = null
    // 查询 区间的 点
    val pointsInBox = LockService.findPointsInBounding(box, sceneId, mapName)
    if (pointsInBox.isEmpty()) {
      return point
    }
    var minDistance = pointsInBox.first().getDistance(site.x, site.y)
    var minPoint: Point = pointsInBox.first()
    for (i in 1 until pointsInBox.size) {
      val p = pointsInBox[i]
      val distance = p.getDistance(site.x, site.y)
      if (distance < minDistance) {
        minDistance = distance
        minPoint = p
      }
    }
    if (minDistance < FIX_MIN_DISTANCE) {
      point = minPoint
    }
    return point
  }

  fun findPerhapsLine(v: Vector, box: BoundingBox, direction: Int, sceneId: String, mapName: String): Line? {
    var line: Line? = null
    val linesInBox = LockService.findLinesInBounding(box, sceneId, mapName)
    if (linesInBox.isEmpty()) {
      return null
    }
    var minLine: Line = linesInBox.first()
    var cost = if (minLine.type == LineType.STRAIGHT) {
      VeLine(Vector(minLine.start.x, minLine.start.y), Vector(minLine.end.x, minLine.end.y))
        .vectorProjectToLine(v)
    } else {
      minLine.getClosePose(v.x, v.y).getDistance(v.x, v.y)
    }

    for (i in 1 until linesInBox.size) {
      val l = linesInBox[i]
      val cost1 = if (l.type == LineType.STRAIGHT) {
        VeLine(Vector(l.start.x, l.start.y), Vector(l.end.x, l.end.y))
          .vectorProjectToLine(v)
      } else {
        l.getClosePose(v.x, v.y).getDistance(v.x, v.y)
      }
      if (cost1 + 0.01 < cost) {
        cost = cost1
        minLine = l
      }
    }
    if (cost < FIX_MIN_DISTANCE) {
      line = minLine
    }
    return line
  }
}