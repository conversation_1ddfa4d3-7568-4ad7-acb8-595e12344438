package com.seer.trick.fleet.mock.service

import com.fasterxml.jackson.module.kotlin.convertValue
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.base.concurrent.BaseConcurrentCenter
import com.seer.trick.base.concurrent.PollingJobManager
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.fleet.device.door.DoorService
import com.seer.trick.fleet.device.lift.LiftService
import com.seer.trick.fleet.domain.RbkModel
import com.seer.trick.fleet.domain.RobotAreaMapRecord
import com.seer.trick.fleet.mock.*
import com.seer.trick.fleet.seer.Smap
import com.seer.trick.fleet.service.*
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper
import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

/**
 * 仿真模块服务。
 */
object MockService {

  private val logger = LoggerFactory.getLogger(javaClass)

  @Volatile
  var config: MockConfig = MockConfig()
    private set

  @Volatile
  var paused: Boolean = false

  /**
   * 仿真倍率
   */
  @Volatile
  var mockSpeedFactor: Double = 1.0
    private set

  /**
   * 每秒帧数
   */
  const val MOCK_FPS = 50

  /**
   * 每秒帧数 2
   */
  const val MOCK_FPS2 = 20

  const val DEFAULT_MAX_SPEED = 1.2
  const val DEFAULT_MAX_BACK_SPEED = 1.2
  const val DEFAULT_MAX_ROT_SPEED = 60.0
  const val DEFAULT_LOADED_MAX_SPEED = 1.0
  const val DEFAULT_LOADED_MAX_BACK_SPEED = 1.0
  const val DEFAULT_LOADED_MAX_ROTSPEED = 60.0

  // 注意键是机器人 ID 不是机器人名称
  val robotRuntimeMapByRobotId: MutableMap<String, MockSeerRobotRuntime> = ConcurrentHashMap()

  // 机器人名称到机器人 ID 的映射
  private val robotNameToIdMap: MutableMap<String, String> = ConcurrentHashMap()

  /**
   * 初始化仿真模块。
   * 1. 加载配置
   * 2. 恢复机器人状态
   * 3. 恢复路径导航任务
   * 4. 开始仿真
   */
  @Synchronized
  fun init() {
    logger.info("init MockService")
    config = MockStore.loadMockConfig()

    // 恢复机器人状态
    val robotRecordMap = MockStore.loadMockRobotRecords()

    for (robotConfig in config.robots) {
      val rr = MockSeerRobotRuntime(robotConfig.id, robotConfig, robotRecordMap[robotConfig.id])
      robotRuntimeMapByRobotId[robotConfig.id] = rr
      robotNameToIdMap[robotConfig.name] = robotConfig.id
    }
    // TODO 这几个定时器停不下来，理论上也不用停，因为 MockService 一直活着
    // 使用 pollingJob 仿真充电
    PollingJobManager.submit(
      threadName = "MockCharge",
      remark = "mock charge",
      interval = { config.battery.chargePeriod },
      logger = logger,
      workerMaxTime = -1,
      stopCondition = { false },
      exceptionContinue = true,
      tags = setOf(getMockTag()),
      worker = MockService::mockCharge,
    )
    // 仿真耗电
    PollingJobManager.submit(
      threadName = "MockBatteryConsumption",
      remark = "mock battery consumption",
      interval = { config.battery.consumePeriod },
      logger = logger,
      workerMaxTime = -1,
      stopCondition = { false },
      exceptionContinue = true,
      tags = setOf(getMockTag()),
      worker = MockService::mockBatteryConsumption,
    )
    // 仿真报警
    PollingJobManager.submit(
      threadName = "MockAlarm",
      remark = "mock alarm",
      interval = { 500L },
      logger = logger,
      workerMaxTime = 1000,
      stopCondition = { false },
      exceptionContinue = true,
      tags = setOf(getMockTag()),
      worker = {
        mockEmcAlarms()
        mockSoftEmcAlarms()
        mockBlockedAlarms()
      },
    )
    // TODO 恢复路径导航任务，从数据库
  }

  /**
   * 获取仿真后台任务的标签
   */
  fun getMockTag(): String = "mock"

  /**
   * 销毁清理仿真模块。
   */
  @Synchronized
  fun dispose() {
    for (rc in robotRuntimeMapByRobotId.values) {
      rc.dispose()
    }
    robotRuntimeMapByRobotId.clear()
    robotNameToIdMap.clear()

    // 删除仿真后台轮训
    PollingJobManager.removeByTag(getMockTag())
  }

  /**
   * 返回仿真系统当前状态
   */
  fun getRealtimeFrame(): MockRealtimeFrame = MockRealtimeFrame(
    paused,
    mockSpeedFactor,
    config.battery.enabled,
    robotRuntimeMapByRobotId.values.map {
      it.toMockRobotRealtimeFrame()
    },
  )

  /**
   * 暂停/恢复仿真总体
   */
  fun pauseOrResume(paused: Boolean) {
    MockService.paused = paused

    if (paused) {
      for (rr in robotRuntimeMapByRobotId.values) rr.pause()
    } else {
      for (rr in robotRuntimeMapByRobotId.values) rr.resume()
    }
  }

  /**
   * 设置仿真的倍率
   */
  fun updateSpeedFactor(speed: Double) {
    if (speed <= 0.0) throw BzError("errBzError", "Speed must greater than 0.0")

    mockSpeedFactor = speed
  }

  /**
   * 修改仿真配置。
   */
  fun updateConfig(config: MockConfig) {
    MockService.config = config
    MockStore.saveMockConfig(config)
    val newRcMap = config.robots.associateBy { it.id }
    val removeIds = mutableListOf<String>()

    // TODO 优化代码
    for ((id, rr) in robotRuntimeMapByRobotId) {
      val rc = newRcMap[id]
      if (rc != null) {
        // 这个机器人没被删
        rr.setConfig(rc)
      } else {
        // 这个机器人被删了
        rr.dispose()
        robotRuntimeMapByRobotId.remove(id)
        removeIds += id
      }
    }
    // 检查机器人是否是新加的，新加的机器人则持久化一下仿真机器人记录
    val robotRecordMap = MockStore.loadMockRobotRecords()
    for (rc in config.robots) {
      val rr = robotRuntimeMapByRobotId[rc.id]
      if (rr == null && rc.maps.isNotEmpty()) {
        val record = if (robotRecordMap[rc.id] != null) {
          robotRecordMap[rc.id]
        } else {
          val record = rc.toRecord()
          // 不存在则持久化仿真机器人记录
          MockStore.updateMockRobotRecordAsync(record)
          record
        }
        robotRuntimeMapByRobotId[rc.id] = MockSeerRobotRuntime(rc.id, rc, record)
      }
    }

    MockStore.removeMockRobotsRecords(removeIds)
    MockStore.removeMockRobotModels(removeIds)

    // 重新算 robotNameToIdMap
    robotNameToIdMap.clear()
    robotNameToIdMap.putAll(config.robots.associateBy({ it.name }, { it.id }))
  }

  /**
   * 更新机器人仿真配置
   */
  fun saveMockConfig(config: MockConfig) = synchronized(this) {
    MockService.config = config
    MockStore.saveMockConfig(config)
  }

  /**
   * 添加多个仿真机器人。注意 ID 后端生成。返回机器人 ID 列表。注意检查重名！
   */
  fun addMockRobots(configs: List<MockSeerRobotConfig>): List<String> {
    // 生成机器人 ID
    val configs2 = configs.map { it.copy(id = IdHelper.oidStr()) }

    // 校验重名
    for (rc in configs2) {
      if (robotNameToIdMap[rc.name] != null) {
        val existRobotId = robotNameToIdMap[rc.name]
        logger.info("用户想要添加已存在的机器人名称，robotName=${rc.name}，existRobotId=$existRobotId")
        throw BzError(
          "errRobotNameExist",
          "机器人名 ${rc.name} 已存在, 请修改，已存在的机器人ID: $existRobotId",
        )
      }
    }

    config.robots += configs2
    MockStore.saveMockConfig(config)

    val robotRecordMap = MockStore.loadMockRobotRecords()
    for (rc in configs2) {
      val record = robotRecordMap[rc.id] ?: rc.toRecord()
      val rr = MockSeerRobotRuntime(rc.id, rc, record)
      robotRuntimeMapByRobotId[rc.id] = rr
      robotNameToIdMap[rc.name] = rc.id

      MockStore.updateMockRobotRecordAsync(rr.record)
    }

    return emptyList()
  }

  /**
   * 删除仿真机器人
   */
  fun removeMockRobots(ids: List<String>) {
    config.robots = config.robots.filter { !ids.contains(it.id) }
    MockStore.saveMockConfig(config)

    for (id in ids) {
      try {
        val rr = robotRuntimeMapByRobotId[id]
        rr?.dispose()
        robotRuntimeMapByRobotId.remove(id)
        robotNameToIdMap.values.remove(id)
      } catch (e: Exception) {
        logger.error("删除仿真机器人失败，id=$id", e)
      }
    }

    MockStore.removeMockRobotsRecords(ids)
    MockStore.removeMockRobotModels(ids)
  }

  /**
   * 批量重命名仿真机器人。names 是机器人 ID -> 机器人名。注意检查重名！
   */
  fun updateMockRobotsNames(names: Map<String, String>) {
    // 校验重名
    for ((id, name) in names) {
      if (robotNameToIdMap[name] != null && robotNameToIdMap[name] != id) {
        val existRobotId = robotNameToIdMap[name]
        logger.info("用户想要重命名为已存在的机器人名称，robotName=$name, existRobotId=$existRobotId")
        throw BzError("errRobotNameExist", "机器人名 $name 已存在, 请修改，已存在的机器人ID: $existRobotId")
      }
    }

    config.robots = config.robots.map { if (names.containsKey(it.id)) it.copy(name = names[it.id]!!) else it }
    MockStore.saveMockConfig(config)

    val robotRecordMap = MockStore.loadMockRobotRecords()
    for ((id, name) in names) {
      val rr = robotRuntimeMapByRobotId[id]
      if (rr == null) {
        // MockSeerRobotRuntime 不存在，则建一个
        val rc = config.robots.firstOrNull { it.id == id }
        if (rc != null) {
          val record = robotRecordMap[id] ?: rc.toRecord()
          robotRuntimeMapByRobotId[id] = MockSeerRobotRuntime(id, rc, record)
        }
      } else {
        rr.setConfig(rr.config.copy(name = name))
      }

      robotRuntimeMapByRobotId[id]?.let { MockStore.updateMockRobotRecordAsync(it.record) }

      robotNameToIdMap.values.remove(id) // 删除老的 k-v
      robotNameToIdMap[name] = id // 添加新的 k-v
    }
  }

  /**
   * 批量修改仿真机器人配置。只修改简单状态，地图走专门的方法。
   * 仅当修改单个机器人时，允许修改点位、坐标等不能重复的属性。
   *
   * 不推荐建新的 MockSeerRobotRuntime，因为 MockSeerRobotRuntime 里有很多状态，比如正在执行的任务，需要重新加载
   */
  fun updateMockRobotsConfigs(ids: List<String>, update: EntityValue) {
    if (ids.isEmpty()) throw BzError("errRobotIdEmpty", "请选择要修改的机器人")

    // 多个机器人时，不能改校验点位、坐标等不能重复的属性
    if (ids.size > 1) {
      if (update.containsKey("initPoint")) throw BzError("errRobotUpdateInitPoint", "批量修改时不能修改出生点位")
      if (update.containsKey("initPositionX") && update.containsKey("initPositionX")) {
        throw BzError("errRobotUpdateInitPosition", "批量修改时不能修改出生 x，y 坐标")
      }
    }

    // 校验重名
    if (!(update["name"] as String?).isNullOrBlank()) {
      val name = update["name"] as String?
      if (ids.size > 1) throw BzError("errRobotNameConflict", "批量修改时不能改为相同的机器人名 $name")

      if (robotNameToIdMap[name] != null && robotNameToIdMap[name] != ids[0]) {
        throw BzError("errRobotNameExist", "机器人名 $name 已存在, 请修改，已存在的机器人ID: ${robotNameToIdMap[name]}")
      }
    }

    // TODO 校验能否改机器人点位，尤其是执行任务中的时候

    // 遍历 config.robots 更新配置，然后重新启动 MockSeerRobotRuntime
    val newConfigList = config.robots.map {
      if (ids.contains(it.id)) {
        // 先将 it 转换为 map，然后用 update 里的 key 替换原本已经存在的
        val newConfig: EntityValue = JsonHelper.mapper.convertValue(it)
        update.forEach { (k, v) -> newConfig[k] = v }
        JsonHelper.mapper.convertValue(newConfig, MockSeerRobotConfig::class.java)
      } else {
        it
      }
    }
    config.robots = newConfigList
    MockStore.saveMockConfig(config)

    val robotRecordMap = MockStore.loadMockRobotRecords()
    for (id in ids) {
      val rc = config.robots.firstOrNull { it.id == id } ?: continue // TODO 找不到 config 应该报错
      val rr = robotRuntimeMapByRobotId[id]
      if (rr == null) {
        // MockSeerRobotRuntime 不存在，则建一个
        robotRuntimeMapByRobotId[id] = MockSeerRobotRuntime(id, rc, robotRecordMap[rc.id])
      } else {
        rr.setConfig(rc)
      }

      robotNameToIdMap.values.remove(id) // 删除老的 k-v
      robotNameToIdMap[rc.name] = id // 添加新的 k-v
    }
  }

  /**
   * 获取仿真机器人的模型文件。
   */
  fun getMockRobotsModel(ids: List<String>): List<RbkModel> =
    ids.mapNotNull { robotRuntimeMapByRobotId[it]?.getMockRobotModel() }

  /**
   * 批量更新仿真机器人的模型文件。
   */
  fun updateMockRobotsModel(ids: List<String>, model: RbkModel) {
    if (ids.isEmpty()) throw BzError("errRobotIdEmpty", "请选择要修改的机器人")
    logger.info("update mock robots model, robot ids=$ids")
    ids.forEach { robotRuntimeMapByRobotId[it]?.updateAndSaveMockRobotModel(model) }
  }

  /**
   * 批量修改仿真机器人地图列表。
   */
  fun updateMockRobotsMaps(ids: List<String>, maps: List<MockSeerRobotMap>) {
    if (ids.isEmpty()) throw BzError("errRobotIdEmpty", "请选择要修改的机器人")

    config.robots = config.robots.map { if (it.id in ids) it.copy(maps = maps) else it }
    MockStore.saveMockConfig(config)

    val robotRecordMap = MockStore.loadMockRobotRecords()
    for (id in ids) {
      val rc = config.robots.firstOrNull { it.id == id } ?: continue // TODO 找不到 config 应该报错
      val rr = robotRuntimeMapByRobotId[id]
      if (rr == null) {
        // MockSeerRobotRuntime 不存在，则建一个
        robotRuntimeMapByRobotId[id] = MockSeerRobotRuntime(id, rc, robotRecordMap[rc.id])
      } else {
        rr.setConfig(rc)
        rr.reloadSmapCache()
      }
    }
  }

  /**
   * 批量修改仿真机器人告警。
   */
  fun updateMockRobotsAlarms(ids: List<String>, alarms: List<MockSeerRobotAlarm>) {
    if (ids.isEmpty()) throw BzError("errRobotIdEmpty", "请选择要修改的机器人")

    val robotRecordMap = MockStore.loadMockRobotRecords()
    for (id in ids) {
      val rc = config.robots.firstOrNull { it.id == id } ?: continue // TODO 找不到 config 应该报错
      val rr = robotRuntimeMapByRobotId[id]
      if (rr == null) {
        // MockSeerRobotRuntime 不存在，则建一个
        robotRuntimeMapByRobotId[id] = MockSeerRobotRuntime(id, rc, robotRecordMap[rc.id]?.copy(alarms = alarms))
      } else {
        MockAlarmProcessor.updateAlarms(rr) { alarms }
      }

      robotRuntimeMapByRobotId[id]?.let { MockStore.updateMockRobotRecordAsync(it.record) }
    }
  }

  /**
   * 批量修改仿真机器人当前状态。只修改简单状态，告警走专门的方法。
   * 仅当修改单个机器人时，允许修改点位、坐标等不能重复的属性。
   */
  fun updateMockRobotsRecords(ids: List<String>, update: EntityValue) {
    if (ids.isEmpty()) throw BzError("errRobotIdEmpty", "请选择要修改的机器人")

    if (update.containsKey("alarms")) throw BzError("errRobotUpdateAlarms", "请使用单独的方法修改告警")
    if (update.containsKey("id")) throw BzError("errRobotUpdateId", "不能修改机器人 id")

    // 如果修改了地图，检查地图是否存在，不存在则报错。
    var newMapName: String? = null
    var newSmap: Smap? = null
    if (update.containsKey("map")) {
      newMapName = update["map"] as String?
      for (id in ids) {
        val rc = config.robots.firstOrNull { it.id == id } ?: throw BzError("errMockRobotNotFound", id)
        newSmap = rc.maps.firstOrNull { it.mapName == newMapName }?.toSmap(rc.sceneId)
          ?: throw BzError("errMapNotFound", "机器人 $id 上没有指定的地图 $newMapName")
      }
      // 切换地图后，如果不更新点位的话，分配随机点位给机器人
      if (!update.containsKey("point")) {
        allocatePointRandomly(ids, update)
      }
    }

    // 修改点位的校验与修正 执行任务时不能修改点位
    if (update.containsKey("point")) {
      updatePointPos(update, ids, newMapName, newSmap)
    }

    // 急停后，修改控制权
    if (update.containsKey("emc")) {
      updateCurrentLockByEmc(update, ids)
    }

    // 修改控制者
    if (update.containsKey("master")) {
      val master = update["master"] as String?
      for (id in ids) {
        val rr = robotRuntimeMapByRobotId[id] ?: throw BzError("errMockRobotNotFound", id)
        val (locked, nickName) = if (master.isNullOrBlank()) {
          Pair(false, null)
        } else {
          Pair(true, master)
        }
        val record = rr.record.copy(
          currentLock = rr.record.currentLock.copy(
            locked,
            nickName,
            if (locked) "/localhost:5800" else null,
          ),
          master = master,
        )
        rr.setRecord(record)
      }
    }

    val robotRecordMap = MockStore.loadMockRobotRecords()
    for (id in ids) {
      val rc = config.robots.firstOrNull { it.id == id } ?: continue // TODO 找不到 config 应该报错
      val rr = robotRuntimeMapByRobotId[id]
      if (rr == null) {
        // MockSeerRobotRuntime 不存在，则建一个
        val record = mergeRobotRecord(robotRecordMap[rc.id], update)
        robotRuntimeMapByRobotId[id] = MockSeerRobotRuntime(id, rc, record)
      } else {
        rr.setRecord(mergeRobotRecord(rr.record, update))
        rr.registerMap()
      }

      robotRuntimeMapByRobotId[id]?.let { MockStore.updateMockRobotRecordAsync(it.record) }

      // 若修改了地图且绑定场景，则异步通知交管切换地图，而机器人上报自身信息需要耗一定的时间，所以尝试切换 5 次
      if (update.containsKey("map")) {
        val sr = SceneService.getSceneById(rc.sceneId)
        sr?.listRobots()?.find { it.robotName == rc.name }?.let {
          BaseConcurrentCenter.highTimeSensitiveExecutor.submit {
            var count = 0
            while (count++ < 5 && !sr.trafficService.switchMap(it)) {
              Thread.sleep(1000)
            }
          }
        }
      }
    }
  }

  /**
   * 更新点位的坐标
   */
  private fun updatePointPos(update: EntityValue, ids: List<String>, newMapName: String?, newSmap: Smap?) {
    val newPointName = update["point"] as String?

    // 当前点位的话，只能一台车一台车的改，但 x、y 坐标可以批量改，比如把多台车的 x 坐标改成相同的
    if (ids.size > 1) throw BzError("errRobotBatchUpdatePosition", "不能批量修改点位")

    // 点位不可能批量修改，因此只取一个就行
    val robotId = ids.first()
    val rr = robotRuntimeMapByRobotId[ids.first()] ?: throw BzError("errMockRobotNotFound", robotId)
    // 正在执行任务的机器人不能修改位置
    if (rr.currentTask?.status == MoveTaskStatus.Init || rr.currentTask?.status == MoveTaskStatus.Running) {
      throw BzError("errMockRobotPointNotChange", robotId)
    }
    // 根据点位坐标覆盖机器人当前 x,y 值。
    if (newPointName.isNullOrBlank()) {
      // 清空点位
      update["x"] = 0.0
      update["y"] = 0.0
    } else {
      // 更新了机器人当前地图的话，在新的地图里找 point；否则，在当前的地图里找 point
      val mapName = newMapName ?: rr.getCurrentSmapFile()?.name // 这里不能取 smap.header.mapName，控制器部门以外部的 .smap 文件名为准
      val smap = (if (newMapName != null) newSmap else rr.getCurrentSmapFile()?.content)
        ?: throw BzError("errMapNotFound", "机器人上 $robotId 找不到地图 ${rr.record.map}")
      // 检查地图上是否有此点位，没有则报错。
      val newPoint = smap.getAllPoints().firstOrNull { it.instanceName == newPointName } // 站点 ID -> 站点对象
        ?: throw BzError("errPointNotFoundInMap", "地图 $mapName 里找不到指定的点位 $newPointName")
      update["x"] = newPoint.pos.x
      update["y"] = newPoint.pos.y
    }
  }

  /**
   * 根据急停状态更新控制权
   */
  private fun updateCurrentLockByEmc(update: EntityValue, ids: List<String>) {
    for (id in ids) {
      val rr = robotRuntimeMapByRobotId[id] ?: throw BzError("errMockRobotNotFound", id)
      if (update["emc"] == true) {
        // 急停
        if (rr.config.setMasterAfterEmc) {
          rr.record.currentLock.locked = true
          rr.record.currentLock.nickName = "this"
        }
      } else {
        // 释放急停
        if (rr.config.unsetMasterAfterUnClearEmc) {
          rr.record.currentLock.locked = false
          rr.record.currentLock.nickName = ""
        }
      }
    }
  }

  /**
   * 随机分配点位
   */
  private fun allocatePointRandomly(ids: List<String>, update: EntityValue) {
    if (ids.size == 1) {
      val rConfig = config.robots.firstOrNull { ids.contains(it.id) } ?: throw BzError("errNoRobot", ids[0])
      val configs = initRobotPointsRandomly(listOf(rConfig.copy(initMap = update["map"] as String)))
      val rConfigs = config.robots.toMutableList()
      rConfigs.removeIf { rConfig.id == it.id }
      rConfigs.add(configs[0])
      update["point"] = configs[0].initPoint
      update["x"] = configs[0].initPositionX
      update["y"] = configs[0].initPositionY
      updateConfig(config.copy(robots = rConfigs))
    } else {
      val rConfigs: MutableList<MockSeerRobotConfig> = mutableListOf()
      config.robots.forEach {
        if (ids.contains(it.id)) {
          rConfigs.add(it.copy(initMap = update["map"] as String))
        } else {
          rConfigs.add(it)
        }
      }
      val configs = initRobotPointsRandomly(rConfigs)
      updateConfig(config.copy(robots = configs))
      configs.forEach {
        val record = it.toRecord()
        val rr = robotRuntimeMapByRobotId[it.id]
        if (rr == null) {
          robotRuntimeMapByRobotId[it.id] = MockSeerRobotRuntime(it.id, it, record)
        } else {
          rr.setRecord(record)
        }
        MockStore.updateMockRobotRecordAsync(record)
      }
    }
  }

  private fun mergeRobotRecord(old: MockSeerRobotRecord?, update: EntityValue): MockSeerRobotRecord {
    val new: EntityValue = if (old != null) JsonHelper.mapper.convertValue(old) else mutableMapOf()
    update.forEach { (k, v) -> new[k] = v }
    return JsonHelper.mapper.convertValue(new, jacksonTypeRef())
  }

  /**
   * 更新动作配置。
   */
  fun updateActionsCosts(actionsCosts: MockActionsCosts) {
    config = config.copy(actionsCosts = actionsCosts)
    MockStore.saveMockConfig(config)
  }

  /**
   * 更新电量（充电/耗电）配置
   */
  fun updateBatteryConfig(battery: MockBattery) {
    config = config.copy(battery = battery)
    MockStore.saveMockConfig(config)
  }

  /**
   * 按机器人，暂停/恢复/取消指定机器人路径导航任务。
   */
  fun prcRobotsNavTasksByRobots(ids: List<String>, action: RobotNavTaskAction) {
    when (action) {
      RobotNavTaskAction.Pause ->
        for (id in ids) robotRuntimeMapByRobotId[id]?.pause()

      RobotNavTaskAction.Resume ->
        for (id in ids) robotRuntimeMapByRobotId[id]?.resume()

      RobotNavTaskAction.Cancel ->
        for (id in ids) robotRuntimeMapByRobotId[id]?.future?.cancel(true)
    }
  }

  /**
   * 随机分派机器人当前点位。
   *
   * 根据机器人当前地图分组，然后按组分配库位。
   * 将地图的所有停靠点 ParkPoint 放到一个 Set 中，随机分配给机器人。如果点位不够，则用充电点 ChargePoint、动作点 ActionPoint 凑。
   *
   *
   */
  fun changeRobotInitPointsRandomly(ids: List<String>) { // TODO 重命名
    val map1 = mutableMapOf<String, Smap>() // robotId -> smap
    val map2 = mutableMapOf<String, MutableSet<String>>() // （当前地图上的机器人） smap.md5 -> Set<robotId>
    for (id in ids) {
      val rr = robotRuntimeMapByRobotId[id] ?: continue
      val smapFile = rr.getCurrentSmapFile() ?: throw BzError("errRobotNoMap", "机器人未配置地图，或地图不匹配")
      map1[smapFile.md5] = smapFile.content
      val set = map2[smapFile.md5] ?: mutableSetOf()
      set.add(id)
      map2[smapFile.md5] = set
    }

    for ((md5, set1) in map2) { // set1 为同地图的机器人 id Set
      val smap = map1[md5]!!
      val points = (
        smap.getParkPoints().shuffled() + smap.getChargePoints().shuffled() + smap.getActionPoints()
          .shuffled() + smap.getLocationMarks().shuffled()
        ).toMutableList() // 当前地图的 停靠点 + 充电点 + AP 点 + LM 点，维持顺序
      if (set1.size > points.size) throw BzError("errLackPoint", set1.size, points.size)
      for (id in set1.shuffled()) {
        val rr = robotRuntimeMapByRobotId[id]!!
        val p = points.removeFirst()
        rr.setRecord(rr.record.copy(point = p.instanceName, x = p.pos.x, y = p.pos.y, theta = p.dir ?: 0.0))

        MockStore.updateMockRobotRecordAsync(rr.record)
      }
    }
  }

  fun initRobotPointsRandomly(configs: List<MockSeerRobotConfig>): List<MockSeerRobotConfig> {
    val md5ToSmap = mutableMapOf<String, Smap>() // md5 -> smap
    val mapRobots = mutableMapOf<String, MutableSet<String>>() // md5 -> robotIds

    // 预处理：构建地图缓存和机器人分组
    configs.forEach { config ->
      // TODO 如果机器人没有初始地图，则在 config.map 里随机选一个，并放到 initMap 里
      val initMap = config.maps.firstOrNull { it.mapName == config.initMap } ?: return@forEach
      val md5 = initMap.mapMd5

      md5ToSmap.getOrPut(md5) {
        initMap.toSmapFile(config.sceneId)?.content ?: return@forEach
      }

      mapRobots.getOrPut(md5) { mutableSetOf() }.add(config.id)
    }

    // 分配点位逻辑
    val config = mapRobots.flatMap { (md5, robotIds) ->
      val smap = md5ToSmap.getValue(md5) // TODO 如果 smap 为 null，会抛异常的
      val points = (
        smap.getParkPoints() + smap.getChargePoints() +
          smap.getActionPoints() + smap.getLocationMarks()
        )
        .shuffled()
        .toMutableList()

      // 这抛 IllegalArgumentException，改为 BzError 更好。便于理解和国际化
      require(robotIds.size <= points.size) {
        "errLackParkPoint: 机器人数量(${robotIds.size})超过地图点位(${points.size})"
      }

      robotIds.shuffled().zip(points).map { (robotId, point) ->
        configs.first { it.id == robotId }.copy(
          initPoint = point.instanceName,
          initPositionX = point.pos.x,
          initPositionY = point.pos.y,
          initTheta = point.dir ?: 0.0,
        )
      }
    }
    return config
  }

  fun initOneRobotByPoint(robotId: String, mapName: String, pointName: String) {
    logger.debug("initOneRobotByPoint: $robotId, $mapName, $pointName")
    val rr = robotRuntimeMapByRobotId[robotId] ?: throw BzError("errMockRobotNotFound", robotId)
    val rc = config.robots.firstOrNull { it.id == robotId } ?: throw BzError("errMockRobotNotFound", robotId)
    val smap = rc.maps.firstOrNull { it.mapName == mapName }?.toSmap(rc.sceneId)
      ?: throw BzError("errMapNotFound", "机器人 $robotId 上没有指定的地图 $mapName")

    // 检查地图上是否有此点位，没有则报错。
    val point = smap.getAllPoints().firstOrNull { it.instanceName == pointName } // 站点 ID -> 站点对象
      ?: throw BzError("errPointNotFoundInMap", "地图 $mapName 里找不到指定的点位 $pointName")

    rr.setRecord(
      rr.record.copy(
        map = mapName,
        point = point.instanceName,
        x = point.pos.x,
        y = point.pos.y,
        theta = point.dir ?: 0.0,
        loadmapStatus = 1,
      ),
    )
    MockStore.updateMockRobotRecordAsync(rr.record)
    rr.reloadSmapCache()
  }

  /**
   * 设置机器人的点位
   */
  fun setRobotInitPoint(robotName: String, pointName: String?, x: Double?, y: Double?, angle: Double?) {
    val id = robotNameToIdMap[robotName] ?: run {
      logger.error("找不到机器人 $robotName")
      return
    }
    val rr = robotRuntimeMapByRobotId[id] ?: run {
      logger.error("找不到机器人运行记录 $robotName")
      return
    }
    if (pointName.isNullOrBlank()) {
      throw BzError("errPointEmpty", "点位名称为空")
    }

    val (finalX, finalY) = if (x == null || y == null) {
      val pointHelper = MockSmapHelper.getPointHelper(rr.robotId, pointName)
        ?: throw BzError("errPointNoMap", "点位不存在")
      Pair(pointHelper.point.pos.x, pointHelper.point.pos.y)
    } else {
      Pair(x, y)
    }

    rr.disconnect()

    // 构建新的记录并更新
    val newRecord = rr.record.copy(
      point = pointName,
      x = finalX,
      y = finalY,
      theta = angle ?: 0.0,
    )
    rr.setRecord(newRecord)
    MockStore.updateMockRobotRecordAsync(newRecord)

    // 使用定时器异步延迟重连，避免阻塞当前线程
    val scheduler = Executors.newSingleThreadScheduledExecutor()
    scheduler.schedule({
      rr.reconnect()
      scheduler.shutdown() // 重连后关闭调度器
    }, 1500, TimeUnit.MILLISECONDS)
  }

  /**
   * 一键重置。机器人恢复初始状态（删除状态记录）。路径导航任务全删除。
   */
  fun reset() {
    logger.info("Reset all mock robots")

    // 删除现有的所有仿真机器人，后再重新创建一份
    val newMockRobotConfigs = mutableListOf<MockSeerRobotConfig>()
    for (sr in SceneService.listScenes()) {
      newMockRobotConfigs += sr.listRobots().map { toMockSeerRobotConfig(sr, it) }.toMutableList()
    }

    val robotConfigs = initRobotPointsRandomly(newMockRobotConfigs)
    updateConfig(MockConfig(robots = robotConfigs))
  }

  /**
   * 重新连接机器人
   */
  fun reconnect(ids: List<String>) {
    for (id in ids) {
      val rr = robotRuntimeMapByRobotId[id] ?: continue
      rr.reconnect()
    }
  }

  /**
   * 返回所有仿真机器人状态，带未完成的路径导航
   */
  fun getMockRobotsSnapshots(): List<MockSeerRobotsSnapshot> = robotRuntimeMapByRobotId.values.map {
    MockSeerRobotsSnapshot(it.record)
  }

  /**
   * 从给定快照恢复机器人 TODO 和任务状态
   */
  fun restoreRobotsSnapshots(ss: List<MockSeerRobotsSnapshot>) {
    for (s in ss) {
      val rr = robotRuntimeMapByRobotId[s.record.id]
      if (rr == null) {
        // 当前机器人被删了，继续恢复其它机器人的状态
        logger.warn("restoreRobotsSnapshots robot not found: ${s.record.id}")
        continue
      }
      rr.setRecord(s.record)
      MockStore.updateMockRobotRecordAsync(s.record)
    }
  }

  // 仿真充电, 每 x 秒充 1%
  private fun mockCharge() {
    if (!config.battery.enabled) return

    for (rr in robotRuntimeMapByRobotId.values) {
      if (!rr.record.point.isNullOrBlank() && (rr.record.battery ?: 0.0) < 1) {
        if (rr.record.charging) {
          rr.setRecord(rr.record.copy(charging = true, battery = (rr.record.battery ?: 0.0) + 0.01))
          MockStore.updateMockRobotRecordAsync(rr.record)
        }
      }
    }
  }

  // 仿真耗电，每 x 秒耗电 0.1 %
  //
  // 不在充电点就认为耗电
  // TODO 运动中耗电更多，原地待机耗电少
  // 耗电到 0 之后，就不再降低
  // TODO 并发问题
  private fun mockBatteryConsumption() {
    if (!config.battery.enabled) return

    for (rr in robotRuntimeMapByRobotId.values) {
      if (!rr.record.point.isNullOrBlank() && (rr.record.battery ?: 0.0) > 0) {
        if (!rr.record.charging) { // 不是充电中的状态就耗电
          rr.setRecord(rr.record.copy(charging = false, battery = (rr.record.battery ?: 0.0) - 0.001))
          MockStore.updateMockRobotRecordAsync(rr.record)
        }
      }
    }
  }

  private fun mockEmcAlarms() {
    for (rr in robotRuntimeMapByRobotId.values) {
      if (rr.record.emc) {
        MockAlarmProcessor.addOrUpdateAlarm(rr, MockSeerRobotAlarm("Warning", "54004", "EMC: emergency stop"))
      } else {
        MockAlarmProcessor.removeAlarm(rr, "54004")
      }
    }
  }

  /**
   * 暂未找到关于软急停的告警码，先自定义一个 94004 吧
   */
  private fun mockSoftEmcAlarms() {
    for (rr in robotRuntimeMapByRobotId.values) {
      if (rr.record.softEmc) {
        MockAlarmProcessor.addOrUpdateAlarm(rr, MockSeerRobotAlarm("Warning", "94004", "Soft EMC: soft emergency stop"))
      } else {
        MockAlarmProcessor.removeAlarm(rr, "94004")
      }
    }
  }

  private fun mockBlockedAlarms() {
    for (rr in robotRuntimeMapByRobotId.values) {
      if (rr.record.blocked) {
        MockAlarmProcessor.addOrUpdateAlarm(rr, MockSeerRobotAlarm("Error", "52200", "robot is blocked"))
      } else {
        MockAlarmProcessor.removeAlarm(rr, "52200")
      }
    }
  }

  /**
   * 一键仿真：为场景自动创建仿真配置。
   * 遍历机器人组和机器人。创建同名仿真机器人。采用机器人连调度的方式。
   * 将场景配置中所有机器人的配置的连接方式改为：机器人连调度的方式。
   * TODO 耗时吗？
   */
  fun mockScene(sr: SceneRuntime, srList: List<SceneRuntime>) {
    logger.info("Mock scene: $sr")

    // 将场景中的电梯改为仿真
    LiftService.changeAlLiftsSimulated(sr, true)

    // 将场景中的门都改为仿真模式
    DoorService.changeAllDoorsSimulated(sr, true)

    // 将场景机器人改为仿真
    RobotService.changeAllRobotsSimulated(sr, true)

    // 创建仿真场景
    val mockRobotConfigs: MutableList<MockSeerRobotConfig> = sr.listRobots()
      .map { rr -> toMockSeerRobotConfig(sr, rr) }.toMutableList()

    // 保留其他场景的仿真机器人
    for (osr in srList) {
      if (osr.sceneId == sr.sceneId || osr.basic.disabled) continue
      for (rr in osr.listRobots()) {
        val t = config.robots.firstOrNull { it.name == rr.robotName && it.sceneId == osr.sceneId }
        if (t != null) {
          mockRobotConfigs.add(t)
        }
      }
    }
    // 一键仿真时，对场景内所有未停用的仿真机器人，没有同名的则创建
    val dupConfig = mockRobotConfigs.groupBy { it.name }.filter { it.value.size > 1 }.flatMap { it.value }.filter {
      it.sceneId == sr.sceneId && !it.disabled
    }
    if (dupConfig.isNotEmpty()) {
      mockRobotConfigs.removeAll(dupConfig)
    }
    val configs = initRobotPointsRandomly(mockRobotConfigs)
    // TODO 拷贝 MockConfig 其他已有配置
    val mockCfg = MockConfig(robots = configs)
    updateConfig(mockCfg)
  }

  private fun toMockSeerRobotConfig(sr: SceneRuntime, rr: RobotRuntime): MockSeerRobotConfig {
    val groupId = rr.config.groupId

    // 从机器人所在的启用的区域中选择一张地图作为机器的 initMap
    val initAreaMap = findInitMapsForMockRobot(sr, groupId)
    val initMap = MockSeerRobotMap(initAreaMap.mapName, initAreaMap.mapFile, initAreaMap.mapMd5, initAreaMap.remark)

    val mockMaps = SceneAreaService.listMapsByRobotGroupId(sr, groupId).map { areaMap ->
      // 转换为仿真用的结构
      MockSeerRobotMap(areaMap.mapName, areaMap.mapFile, areaMap.mapMd5, areaMap.remark)
    }
    return MockSeerRobotConfig(
      id = IdHelper.oidStr(),
      sceneId = sr.sceneId,
      name = rr.robotName,
      disabled = rr.config.disabled,
      remark = rr.config.remark,
      fleetTcpServerIp = "127.0.0.1",
      fleetTcpServerPort = BzConfigManager.getByPathAsInt("ScWcs", "tcpServer", "port"),
      initMap = initMap.mapName, // 必须是已启的区域的的地图
      maps = mockMaps,
      containerNum = if (rr.config.selfBinNum > 1) rr.config.selfBinNum else null, // 多储位机器人时，启用背篓
      keepInArm = rr.config.plusOne,
    )
  }

  /**
   * 取调度中机器人组的未禁用的地图列表，作为仿真机器人的 initMap。
   */
  private fun findInitMapsForMockRobot(sr: SceneRuntime, groupId: Int): RobotAreaMapRecord {
    // 复杂的场景中，部分分组机器人所在的区域可能都被停用了，会导致这些机器人没有初始地图。
    val areas = sr.areas.filter { it.groupsMap.keys.contains(groupId) }
    val enabledAreas = areas.filter { !it.disabled }
    for (area in enabledAreas) {
      val gm = area.groupsMap[groupId]
      if (gm != null) return gm
    }

    // 机器人分组 "{0}" 所属的区域 "{2}" 都被禁用了，机器人 "{1}" 没有可用的地图。
    val groupName = sr.robotGroups[groupId]?.name ?: throw BzError("errNoRobotGroupId", groupId)
    val robotNames = sr.robots.values.filter { it.config.groupId == groupId }.map { it.robotName }
    throw BzError("errNoEnabledAreasForRobotGroup", groupName, robotNames, areas.map { it.name })
  }

  /**
   * 取消一个场景中全部机器人的仿真
   */
  fun cancelMockScene(sr: SceneRuntime) {
    logger.info("Cancel mock scene: $sr")

    // 取消场景中所有电梯的仿真模式
    LiftService.changeAlLiftsSimulated(sr, false)

    // 取消场景中门的仿真
    DoorService.changeAllDoorsSimulated(sr, false)

    // 取消场景机器人的仿真
    RobotService.changeAllRobotsSimulated(sr, false)

    for (rr in sr.listRobots()) {
      // 销毁仿真运行时
      val robotId = robotNameToIdMap.remove(rr.config.robotName)
      robotId?.let { robotRuntimeMapByRobotId.remove(robotId)?.dispose() }
    }

    // 兼容停用的场景 sr.listRobots 为空的场景
    val removeRobotIds = robotRuntimeMapByRobotId.values.filter { it.config.sceneId == sr.sceneId }
      .map { it.robotId }
    for (id in removeRobotIds) {
      robotNameToIdMap.values.remove(id)
      robotRuntimeMapByRobotId.remove(id)?.dispose()
    }
    MockStore.removeMockRobotsRecords(removeRobotIds)
    MockStore.removeMockRobotModels(removeRobotIds)
  }

  /**
   * 初始化一组仿真机器人
   */
  fun initSceneMockRobots(sr: SceneRuntime, robotNames: List<String>) {
    // 筛选新建的条件并转化成仿真配置
    val mockRobotConfigs =
      sr.listRobots().filter {
        it.robotName in robotNames &&
          SceneAreaService.listMapsByRobotGroupId(sr, it.mustGetGroup().id).isNotEmpty()
      }.map { rr -> toMockSeerRobotConfig(sr, rr) }
    // 保存配置文件
    config.robots += mockRobotConfigs
    MockStore.saveMockConfig(config)
    // 新增的仿真初始化仿真运行时
    mockRobotConfigs.forEach { config ->
      val mockSeerRobotRuntime = MockSeerRobotRuntime(
        robotId = config.id,
        config = config,
        record = config.toRecord(),
      )
      robotRuntimeMapByRobotId[config.id] = mockSeerRobotRuntime
      robotNameToIdMap[config.name] = config.id
      MockStore.updateMockRobotRecordAsync(mockSeerRobotRuntime.record)
    }
    // 重分配点位
    initRobotCurrentPointRandomly(mockRobotConfigs.map { it.id })
  }

  /**
   * 重新分配 ids 对应的机器人点位，其余的机器人不重新分配
   */
  private fun initRobotCurrentPointRandomly(ids: List<String>) {
    val md5ToSmap = mutableMapOf<String, Smap>() // smap.md5 -> smap
    // （当前地图上的要随机点位的机器人） smap.md5 -> Set<MockSeerRobotRuntime>
    val md5ToInitRobots = mutableMapOf<String, MutableSet<MockSeerRobotRuntime>>()
    // (当前地图上所有的机器人) smap.md5 -> {robotId: point}
    val md5ToNoChangeRobots = mutableMapOf<String, MutableMap<String, String?>>()

    for (rr in robotRuntimeMapByRobotId.values) {
      // TODO 还没有地图的时候创建了仿真，此步先跳过，后续上传地图后需要在合适的位置初始化机器人的位置以及 mapCatch
      val smapFile = rr.getCurrentSmapFile() ?: continue
      val robotId = rr.robotId
      md5ToSmap[smapFile.md5] = smapFile.content
      if (robotId in ids) {
        md5ToInitRobots.getOrPut(smapFile.md5) { mutableSetOf() }.add(rr) // 要重新分配点位的机器人
      } else {
        md5ToNoChangeRobots.getOrPut(smapFile.md5) { mutableMapOf() }[robotId] = rr.record.point // 不重新分配点位的机器人
      }
    }

    // 随机分配逻辑
    for ((md5, mrList) in md5ToInitRobots) {
      val smap = md5ToSmap[md5] ?: throw BzError("errMapNotFound", "找不到地图 $md5")
      // 所有可放机器人的点位
      val allPoints = smap.getParkPoints().shuffled() + smap.getChargePoints().shuffled() +
        smap.getActionPoints().shuffled() + smap.getLocationMarks().shuffled()

      val allocatedPoints = md5ToNoChangeRobots[md5]?.values ?: emptyList()
      // 剩余可用的放机器人的点位
      val availablePoints = allPoints.filter { it.instanceName !in allocatedPoints }
      if (availablePoints.size < mrList.size) {
        throw BzError("errLackPoint", mrList.size, availablePoints.size)
      }

      mrList.shuffled().zip(availablePoints).forEach { (rr, p) ->
        rr.setRecord(rr.record.copy(point = p.instanceName, x = p.pos.x, y = p.pos.y, theta = p.dir ?: 0.0))
        MockStore.updateMockRobotRecordAsync(rr.record)
      }
    }
  }

  /**
   * 抢占控制权并使机器人当前任务失败
   */
  fun setCurrentTaskFailed(ids: List<String>) {
    for (id in ids) {
      val rr = robotRuntimeMapByRobotId[id] ?: continue
      MockAlarmProcessor.addOrUpdateAlarm(rr, MockSeerRobotAlarm("Error", "01010101", "导航被人工失败（仿真）"))
      rr.setCurrentTaskFailed()
    }
  }

  /**
   * 抢占控制权并使机器人当前任务取消
   */
  fun setCurrentTaskCancelled(ids: List<String>) {
    for (id in ids) {
      val rr = robotRuntimeMapByRobotId[id] ?: continue
      rr.takeController("human")
      rr.setCurrentTaskCancelled()
    }
  }

  /**
   * 修改机器人急停状态
   */
  fun setEmc(ids: List<String>, emc: Boolean) {
    updateCurrentLockByEmc(mutableMapOf("emc" to emc), ids)
    for (id in ids) {
      val record = robotRuntimeMapByRobotId[id] ?: continue
      record.setRecord(record.record.copy(emc = emc))
    }
  }

  /**
   * 修改机器人的软急停状态
   *   其实直接在场景中操作机器人的软急停状态即可。
   */
  fun setSoftEmc(ids: List<String>, softEmc: Boolean) {
    for (id in ids) {
      val record = robotRuntimeMapByRobotId[id] ?: continue
      record.setRecord(record.record.copy(softEmc = softEmc))
    }
  }

  /**
   * 重置机器人背篓、货架形状
   */
  fun resetRobotContainers(robotName: String) {
    val rr = robotRuntimeMapByRobotId.values.firstOrNull { it.config.name == robotName } ?: return
    MockContainersProcessor.clearAllSelfBins(rr)
    MockContainersProcessor.removeGoodsRegion(rr)
  }

  /**
   * 导入地图，修改机器人或组状态的时候检查，启用了的仿真机器人没有则创建
   */
  fun initOrRemoveMockByRobotState(sr: SceneRuntime) {
    val robots = sr.listRobots()
    val mockRobotMap = config.robots.associateBy { it.name }
    // 只有非停用且仿真且有地图的机器人才能创建仿真
    val (needMockRobots, needRemoveRobots) = robots.partition {
      !it.disabled() &&
        it.config.simulated &&
        SceneAreaService.listMapsByRobotGroupId(sr, it.config.groupId).isNotEmpty()
    }
    val needMockNames = needMockRobots.map { it.robotName }
    val needRemoveNames = needRemoveRobots.map { it.robotName }
    // 删除场景不同但同名机器人,当前场景的未仿真的机器人,被删除的机器人
    val removeIds = mockRobotMap.values.filter {
      (it.sceneId != sr.sceneId && it.name in needMockNames) ||
        (it.sceneId == sr.sceneId && it.name in needRemoveNames)
    }.map { it.id }
    // 仿真中没有的才新增
    val createMockNames = needMockNames.filter { mockRobotMap[it] == null }
    // 删除和新增
    if (removeIds.isNotEmpty()) removeMockRobots(removeIds)
    if (createMockNames.isNotEmpty()) initSceneMockRobots(sr, createMockNames)
  }

  /**
   * 根据机器人名字删除仿真机器人
   */
  fun removeMockByName(robotNames: List<String>) {
    val removeIds = config.robots.filter { it.name in robotNames }.map { it.id }
    if (removeIds.isNotEmpty()) removeMockRobots(removeIds)
  }

  /**
   * 获取默认的移动配置
   */
  fun getDefaultMoveConfig(modelType: String): MoveConfig = when (modelType) {
    // 顶升
    "jack" -> MoveConfig(
      maxSpeed = 1.8,
      maxBackSpeed = 1.8,
      maxRotSpeed = 90.0,
      loadedMaxSpeed = 1.5,
      loadedMaxBackSpeed = 1.5,
      loadedMaxRotSpeed = 90.0,
    )

    // 地牛
    "trans-fork" -> MoveConfig(
      maxSpeed = 1.2,
      maxBackSpeed = 1.2,
      maxRotSpeed = 90.0,
      loadedMaxSpeed = 1.0,
      loadedMaxBackSpeed = 1.0,
      loadedMaxRotSpeed = 90.0,
    )

    // 叉车
    "height-fork" -> MoveConfig(
      maxSpeed = 1.2,
      maxBackSpeed = 1.2,
      maxRotSpeed = 60.0,
      loadedMaxSpeed = 1.0,
      loadedMaxBackSpeed = 1.0,
      loadedMaxRotSpeed = 60.0,
    )

    // 料箱
    "picking" -> MoveConfig(
      maxSpeed = 1.2,
      maxBackSpeed = 1.2,
      maxRotSpeed = 90.0,
      loadedMaxSpeed = 1.2,
      loadedMaxBackSpeed = 1.2,
      loadedMaxRotSpeed = 90.0,
    )

    else -> {
      // 叉车的所有速度是最小的，若是其他类型暂时按叉车处理？
      MoveConfig(
        maxSpeed = 1.2,
        maxBackSpeed = 1.2,
        maxRotSpeed = 60.0,
        loadedMaxSpeed = 1.0,
        loadedMaxBackSpeed = 1.0,
        loadedMaxRotSpeed = 60.0,
      )
    }
  }

  /**
   * 获取机器人移动配置，优先取机器人组的速度，没有再取默认值
   */
  fun getMoveConfig(sceneId: String, robotName: String): MoveConfig {
    // 有些仿真可能对应的场景已经删除了，但仿真还没删，所以使用 getSceneById 尝试获取
    val group = SceneService.getSceneById(sceneId)?.robots?.get(robotName)?.mustGetGroup()
    // jack 比较常用，若没有 modelType 则按 jack 机器人来处理
    val defaultConfig = getDefaultMoveConfig(group?.modelType ?: "jack")
    val actionsCosts = config.actionsCosts

    return MoveConfig(
      actionsCosts.maxVelocity ?: group?.maxSpeed ?: defaultConfig.maxSpeed,
      actionsCosts.maxBackVelocity ?: group?.maxBackSpeed ?: defaultConfig.maxBackSpeed,
      actionsCosts.maxRotateVelocity ?: group?.maxRotSpeed ?: defaultConfig.maxRotSpeed,
      actionsCosts.maxVelocity ?: group?.loadedMaxSpeed ?: defaultConfig.loadedMaxSpeed,
      actionsCosts.maxBackVelocity ?: group?.loadedMaxBackSpeed ?: defaultConfig.loadedMaxBackSpeed,
      actionsCosts.maxRotateVelocity ?: group?.loadedMaxRotSpeed ?: defaultConfig.loadedMaxRotSpeed,
    )
  }
}