package com.seer.trick.fleet.service

import com.fasterxml.jackson.databind.node.ObjectNode
import com.seer.trick.BzError
import com.seer.trick.I18N
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.domain.SceneHelper.movePointCloudImage
import com.seer.trick.fleet.domain.SceneHelper.moveSvgMap
import com.seer.trick.fleet.map.SceneAreaCache
import com.seer.trick.fleet.order.OrderService
import com.seer.trick.fleet.seer.*
import com.seer.trick.helper.JsonFileHelper
import com.seer.trick.helper.JsonHelper
import org.apache.commons.codec.digest.DigestUtils
import org.apache.commons.io.FileUtils
import org.slf4j.LoggerFactory
import java.io.File
import java.io.FileInputStream
import java.nio.charset.StandardCharsets

/**
 * 场景的区域管理。
 * TODO 触发事件
 */
object SceneAreaService {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 加载并初始化一个场景下的所有区域
   */
  fun init(sr: SceneRuntime) {
    val areas: List<SceneArea> = JsonFileHelper.readJsonFromFile(getAreasFile(sr.sceneId)) ?: emptyList()
    // 兼容老版本的相对路径、gmMap，后续现场都更新重新保存后，再删除
    val newAreas = mutableListOf<SceneArea>()
    for (area in areas) {
      val envPointCloud = area.mergedMap.envPointCloud?.copy(
        imagePath = SceneFileService.subpath(sr.sceneId, area.mergedMap.envPointCloud.imagePath),
      )
      val svgMapFile = area.mergedMap.svgMapFile?.let { SceneFileService.subpath(sr.sceneId, it) }
      val mergedMap = area.mergedMap.copy(envPointCloud = envPointCloud, svgMapFile = svgMapFile)
      val groupsMap = mutableMapOf<Int, RobotAreaMapRecord>()
      val gmMap = mutableMapOf<Int, SceneAreaMap>()
      for ((gId, gm) in area.groupsMap) {
        val mapFile = SceneFileService.subpath(sr.sceneId, gm.mapFile)
        groupsMap[gId] = gm.copy(mapFile = mapFile)
        if (area.gmMap.isEmpty()) {
          val file = SceneFileService.pathToFile(sr.sceneId, mapFile)
          if (!file.exists()) continue
          val str = FileUtils.readFileToString(file, StandardCharsets.UTF_8)
          val smap = SmapHelper.strToSmap(str)
          var areaMap = SmapHelper.smapToScene(smap, gm.mapName)
          // 移动点云、SVG 地图
          areaMap = movePointCloudImage(sr.sceneId, areaMap, area.name)
          areaMap = moveSvgMap(sr.sceneId, areaMap, area.name)
          gmMap[gId] = areaMap
        }
      }

      // 之前的曲线类型 PathCurveType.Other 包含了 DegenerateBezier、ArcPath，现在增加了 DegenerateBezier，故需更新一下 other 类型
      val paths = mergedMap.paths.map {
        if (it.curveType == PathCurveType.Other && it.bezierPaths?.get(0)?.controls?.size != 2) {
          it.copy(curveType = PathCurveType.DegenerateBezier)
        } else {
          it
        }
      }

      newAreas.add(
        area.copy(
          mergedMap = mergedMap.copy(paths = paths),
          groupsMap = groupsMap,
          gmMap = area.gmMap.ifEmpty { gmMap },
        ),
      )
    }
    sr.areas = newAreas
  }

  /**
   * 取调度中机器人的地图列表
   */
  fun listMapsByRobot(sr: SceneRuntime, robotName: String): List<RobotAreaMapRecord> {
    val areas = sr.areas
    val group = RobotGroupService.mustGetRobotGroupByRobot(sr, robotName)
    return areas.mapNotNull { area -> area.groupsMap[group.id] }
  }

  /**
   * 取调度中机器组的地图列表
   */
  fun listMapsByRobotGroupId(sr: SceneRuntime, groupId: Int): List<RobotAreaMapRecord> {
    val areas = sr.areas
    return areas.mapNotNull { area -> area.groupsMap[groupId] }
  }

  /**
   * 添加一个新区域。
   * 区域新增比较简单，不影响正在进行的运单。
   */
  fun create(sr: SceneRuntime, req: SceneArea, checkContext: CheckAreaContext): SceneArea = sr.withOrderLock {
    val area = req.copy(id = 1 + (sr.areas.maxOfOrNull { it.id } ?: 0))
    val areas = sr.areas + area

    updateAreas(sr, areas, checkContext)

    logger.info("Create area of scene $sr: $req")

    notifyAreaChanged(sr)

    return@withOrderLock area
  }

  /**
   * 删除几个区域。
   * 加运单锁，停止运单关键行为。
   * 删除的条件时：区域内没有正在运行的运单；区域内没有机器人。
   */
  fun remove(sr: SceneRuntime, ids: List<Int>) = sr.withOrderLock {
    for (areaId in ids) {
      checkBeforeRemoveOrDisableArea(sr, areaId)
    }

    val areas = sr.areas.toMutableList()
    areas.removeAll { ids.contains(it.id) }

    val checkContext = CheckAreaContext(mutableListOf())
    updateAreas(sr, areas, checkContext)

    logger.info("Remove areas of scene $sr: $ids")

    notifyAreaChanged(sr)
  }

  /**
   * 检查是否能删除、停用区域。不能抛异常。
   * 停用、删除的条件时：区域内没有正在运行的运单；区域内没有机器人。
   */
  private fun checkBeforeRemoveOrDisableArea(sr: SceneRuntime, areaId: Int) {
    val ar = sr.mapCache.areaById[areaId] ?: return
    if (OrderService.listAreaRunningOrders(sr, areaId).isNotEmpty()) {
      throw BzError("errRemoveAreaHasRunningOrders", ar.schema.name)
    }
    if (RobotService.listRobotsInArea(sr, areaId).isNotEmpty()) {
      throw BzError("errRemoveAreaHasRobots", ar.schema.name)
    }
  }

  /**
   * 更新一个区域。
   * TODO 现在后处理放在 updateAreas 里，比较低效
   */
  fun updateAreaConfig(sr: SceneRuntime, req: SceneArea, checkContext: CheckAreaContext) = sr.withOrderLock {
    val areas = sr.areas.toMutableList()
    val index = areas.indexOfFirst { it.id == req.id }
    if (index < 0) throw BzError("errNoAreaById", req.id)
    areas[index] = req

    updateAreas(sr, areas, checkContext)

    logger.info("update area of scene $sr: ${req.id}:${req.name}")

    notifyAreaChanged(sr)
  }

  /**
   * 批量启停区域
   */
  fun disableAreas(sr: SceneRuntime, req: DisableAreas) = sr.withOrderLock {
    val areas = sr.areas.toMutableList()
    val ids = req.ids
    if (ids.isNotEmpty()) {
      for (id in ids) {
        // TODO 有一个失败则全部失败？还是允许部分成功？
        checkBeforeRemoveOrDisableArea(sr, id)
        val index = areas.indexOfFirst { it.id == id }
        if (index < 0) throw BzError("errNoAreaById", id)
        if (areas[index].disabled == req.disabled) continue
        areas[index] = areas[index].copy(disabled = req.disabled)
      }
    }

    persistConfigs(sr.sceneId, areas)

    sr.areas = areas
    sr.mapCache.update()

    logger.info("disable areas of scene $sr: ${req.ids}")

    notifyAreaChanged(sr)
  }

  data class DisableAreas(val disabled: Boolean, val ids: List<Int>)

  /**
   * 整体替换场景的所有区域。
   */
  fun replaceAllAreas(sr: SceneRuntime, areas: List<SceneArea>, checkContext: CheckAreaContext) = sr.withOrderLock {
    updateAreas(sr, areas, checkContext)

    logger.info("Replace all areas of scene $sr")

    notifyAreaChanged(sr)
  }

  // 目前，添加删除修改区域，都先全部重置所有区域，后面优化。
  // 区域更新后，要更新地图缓存。这步耗时。TODO 需要优化。去掉重复计算。
  // TODO 会更新交管，后面去掉
  private fun updateAreas(sr: SceneRuntime, areas: List<SceneArea>, checkContext: CheckAreaContext) {
    // 容错：过滤掉不存在的机器人组的地图
    val robotGroupIds = sr.robotGroups.keys

    // TODO 这里反了，应该尽量用新的，只保留旧的 id 等少量字段
    var newAreas = areas.map { area ->
      area.copy(
        ui = area.ui,
        groupsMap = area.groupsMap.filterKeys { robotGroupIds.contains(it) },
        mergedMap = area.mergedMap,
      )
    }
    newAreas = SceneHelper.postProcessAreas(sr, newAreas, checkContext)
    // 校验不通过则不更新
    if (checkContext.errors.isNotEmpty()) return

    persistConfigs(sr.sceneId, newAreas)

    sr.withOrderLock {
      sr.areas = newAreas
      sr.mapCache.update()
    }
  }

  /**
   * 通知系统其他部分区域的变更，目前主要是通知交管。
   * 注意目前这个方法会在运单锁中调用！
   */
  private fun notifyAreaChanged(sr: SceneRuntime) = sr.withOrderLock {
    sr.trafficService.updateTrafficSchema()
  }

  // 只负责持久化
  private fun persistConfigs(sceneId: String, areas: List<SceneArea>) {
    JsonFileHelper.writeJsonToFile(getAreasFile(sceneId), areas, true)
  }

  fun getAreasFile(sceneId: String): File {
    val dir = SceneFileService.getSceneDir(sceneId)
    return File(dir, "areas.json")
  }

  /**
   * 更新区域的所有地图
   */
  fun updateAreaMap(
    sr: SceneRuntime,
    areaCache: SceneAreaCache,
    mergedMapReq: SceneAreaMap,
    groupMapsReq: Map<Int, SceneAreaMap>,
    ui: UiConfig?,
  ) = sr.withOrderLock {
    // 前端必传合并地图与所有组地图，只需校验合并地图与所有组地图的元素是否一致即可
    val (isSame, msg) = compareMap(mergedMapReq, groupMapsReq)
    if (!isSame) throw BzError(msg)

    // 反转路径
    val reversedPathMap: MutableMap<String, MapPath> = HashMap()
    val paths = mergedMapReq.paths.map {
      if (it.reversed) {
        val reversedPath = reversedPath(areaCache, it)
        reversedPathMap[it.key] = reversedPath
        reversedPath
      } else {
        it
      }
    }
    // 更新合并地图
    val mergedMap = mergedMapReq.copy(bound = calcBound(mergedMapReq), paths = paths)
    // 更新组地图
    val gmMap = mutableMapOf<Int, SceneAreaMap>()
    for ((gId, gm) in groupMapsReq) {
      val areaMap = areaCache.schema.gmMap[gId] ?: continue
      val paths = gm.paths.mapNotNull { if (it.reversed) reversedPathMap[it.key] else it }
      gmMap[gId] = areaMap.copy(
        bound = calcBound(gm),
        points = gm.points,
        paths = paths,
        bins = gm.bins,
        zones = gm.zones,
      )
    }

    // 比对差异，更新地图文件
    val groupsMap = areaCache.schema.groupsMap.toMutableMap()
    for ((gId, gm2) in gmMap) {
      val gm1 = areaCache.groupedMaps[gId]?.areaMap ?: continue
      val diffMap = SceneAreaMapValueDiff(gm1, gm2)
      if (!diffMap.isAnyChanged()) continue
      groupsMap[gId]?.let {
        val mapFile = SceneFileService.pathToFile(sr.sceneId, it.mapFile)
        val oldMap = JsonHelper.mapper.readTree(mapFile)
        // 合并差异
        val helper = SmapOutputHelper(oldMap as ObjectNode, diffMap, gm2.points)
        helper.merge()
        // 写回 smap 文件
        JsonHelper.mapper.writeValue(mapFile, oldMap)
        val md5 = FileInputStream(mapFile).use { input -> DigestUtils.md5Hex(input) }
        groupsMap[gId] = it.copy(mapMd5 = md5)
      }
    }

    val newArea = areaCache.schema.copy(
      mergedMap = areaCache.schema.mergedMap.copy(
        points = mergedMap.points,
        paths = mergedMap.paths,
        bins = mergedMap.bins,
        zones = mergedMap.zones,
      ),
      gmMap = gmMap,
      groupsMap = groupsMap,
      ui = ui,
    )

    // 内存操作，不重新加载文件合并
    updateArea(sr, newArea)
  }

  /**
   * 计算边界 TODO 后续支持点位添加再将其纳入计算
   */
  private fun calcBound(areaMap: SceneAreaMap): Rect {
    if (areaMap.zones.isEmpty()) return areaMap.bound

    val maxX1 = (areaMap.bound.cx * 2 + areaMap.bound.width) / 2
    val maxY1 = (areaMap.bound.cy * 2 + areaMap.bound.height) / 2
    val minX1 = (areaMap.bound.cx * 2 - areaMap.bound.width) / 2
    val minY1 = (areaMap.bound.cy * 2 - areaMap.bound.height) / 2
    val (minX2, maxX2, minY2, maxY2) = GeoHelper.calcBound2D(areaMap.zones.flatMap { it.polygon }, { it.x }, { it.y })

    // 若在原边界内则不重新计算
    if (maxX1 > maxX2 && minX1 < minX2 && maxY1 > maxY2 && minY1 < minY2) return areaMap.bound

    val minX = minX1.coerceAtMost(minX2)
    val maxX = maxX1.coerceAtLeast(maxX2)
    val minY = minY1.coerceAtMost(minY2)
    val maxY = maxY1.coerceAtLeast(maxY2)

    val cx = ((minX + maxX) / 2).roundLengthPrecision()
    val cy = ((minY + maxY) / 2).roundLengthPrecision()
    val width = (maxX - minX).roundLengthPrecision()
    val height = (maxY - minY).roundLengthPrecision()

    return Rect(cx = cx, cy = cy, width = width, height = height)
  }

  /**
   * 比较合并地图与组地图，只比较元素数量是否一致、组元素是否在合并地图中存在
   */
  private fun compareMap(mergedMap: SceneAreaMap, groupMaps: Map<Int, SceneAreaMap>): Pair<Boolean, String> {
    val mergedPoints = mergedMap.points.map { it.name }
    val mergedPaths = mergedMap.paths.map { it.key }
    val mergedBins = mergedMap.bins.map { it.name }
    // 区块目前没有做合并，需要去重一下
    val mergedZones = mergedMap.zones.map { it.name }.distinct()
    val points: MutableList<String> = mutableListOf()
    val paths: MutableList<String> = mutableListOf()
    val bins: MutableList<String> = mutableListOf()
    val zones: MutableList<String> = mutableListOf()
    for (gm in groupMaps.values) {
      for (point in gm.points) {
        if (!mergedPoints.contains(point.name)) {
          return Pair(false, I18N.lo("errExistInconsistentElement", listOf(I18N.lo("pointMsg"), point.name)))
        }
        if (!points.contains(point.name)) points.add(point.name)
      }

      for (path in gm.paths) {
        if (!mergedPaths.contains(path.key)) {
          return Pair(false, I18N.lo("errExistInconsistentElement", listOf(I18N.lo("pathMsg"), path.key)))
        }
        if (!paths.contains(path.key)) paths.add(path.key)
      }
      for (bin in gm.bins) {
        if (!mergedBins.contains(bin.name)) {
          return Pair(false, I18N.lo("errExistInconsistentElement", listOf(I18N.lo("binMsg"), bin.name)))
        }
        if (!bins.contains(bin.name)) bins.add(bin.name)
      }
      for (zone in gm.zones) {
        if (!mergedZones.contains(zone.name)) {
          return Pair(false, I18N.lo("errExistInconsistentElement", listOf(I18N.lo("zoneMsg"), zone.name)))
        }
        if (!zones.contains(zone.name)) zones.add(zone.name)
      }
    }
    if (mergedPoints.size != points.size) return Pair(false, I18N.lo("errInconsistentQuantity", listOf("Point")))
    if (mergedPaths.size != paths.size) return Pair(false, I18N.lo("errInconsistentQuantity", listOf("Path")))
    if (mergedBins.size != bins.size) return Pair(false, I18N.lo("errInconsistentQuantity", listOf("Bin")))
    if (mergedZones.size != zones.size) return Pair(false, I18N.lo("errInconsistentQuantity", listOf("Zone")))
    return Pair(true, "")
  }

  /**
   * 反转路径，前端已反转了 fromPointId、fromPointName、toPointId、toPointName、controlPoints
   */
  private fun reversedPath(areaCache: SceneAreaCache, path: MapPath): MapPath {
    val moveDir = if (MoveDirection.Backward == path.moveDirection) MoveDirection.Forward else MoveDirection.Backward
    // 重新计算 bezierPaths、tracePoints
    val fromPoint = areaCache.mergedMap.pointNameMap[path.fromPointName]!!
    val toPoint = areaCache.mergedMap.pointNameMap[path.toPointName]!!
    val bSpline = SceneCurveHelper.transferPathToSpline(path, fromPoint.point, toPoint.point)
    val beziers = SmapCurveHelper.transferBSplineToBezier(bSpline)

    // 目前是按 CurveSplitType.Distance 计算
    val controlNum = path.bezierPaths?.get(0)?.controls?.size ?: 0
    val num = (path.actualLength / 0.1 + 1).toInt()
    val tracePoints = SmapCurveHelper.getPathPositionAndAngle(
      bSpline,
      num,
      SceneCurveHelper.toSmapCurveType(path.curveType, controlNum),
    )
    val middlePoint = tracePoints[tracePoints.size / 2]
    return path.copy(
      moveDirection = moveDir,
      containerDir = null, // 需要界面重新设置
      middlePoint = middlePoint,
      bezierPaths = beziers,
      tracePoints = tracePoints,
      reversed = false,
    )
  }

  /**
   * 更新区域，在内存中操作
   */
  private fun updateArea(sr: SceneRuntime, area: SceneArea) {
    val areas = sr.areas.toMutableList()
    val index = areas.indexOfFirst { it.id == area.id }
    if (index < 0) throw BzError("errNoAreaById", area.id)
    areas[index] = area
    val robotGroupIds = sr.robotGroups.keys
    val newAreas = areas.map { area ->
      area.copy(groupsMap = area.groupsMap.filterKeys { robotGroupIds.contains(it) })
    }
    val sceneId = sr.sceneId
    AlarmService.removeByTag("$sceneId-conflictingElementsAlarm")
    sr.withOrderLock {
      sr.areas = newAreas
      sr.mapCache.update()
    }
    // TODO 检查不同区域是否有同名点位、同编号、同名库位，在这检查的话组地图已经持久化了
    persistConfigs(sr.sceneId, newAreas)
    logger.info("update area of scene $sr: ${area.id}:${area.name}")
    notifyAreaChanged(sr)
  }
}

/**
 * 校验区域合法性的上下文
 */
data class CheckAreaContext(val errors: MutableList<String>)