package com.seer.trick.fleet.order

import com.seer.trick.BzError
import com.seer.trick.I18N
import com.seer.trick.base.alarm.AlarmItem
import com.seer.trick.base.alarm.AlarmLevel
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.base.concurrent.PollingJobManager
import com.seer.trick.fleet.FleetLogger
import com.seer.trick.fleet.device.lift.LiftDispatcher
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.map.SceneAreaCache
import com.seer.trick.fleet.map.SceneMapAlg
import com.seer.trick.fleet.service.SceneFileService
import com.seer.trick.fleet.service.SceneRuntime
import com.seer.trick.fleet.service.SceneService
import com.seer.trick.helper.*
import org.slf4j.LoggerFactory
import java.io.File
import java.util.concurrent.ConcurrentHashMap

/**
 * 随机发单服务
 */
object RandomCreateOrderService {
  private val logger = LoggerFactory.getLogger(javaClass)
  
  /**
   * 随机派单的配置。sceneId -> RandomOrderConfig
   */
  private val configMap: MutableMap<String, RandomOrderConfig> = ConcurrentHashMap()
  
  /**
   * 随机派单的启用状态。 sceneId -> enabled
   */
  private var enabledMap: MutableMap<String, Boolean> = ConcurrentHashMap()
  
  /**
   * 业务线名称 -> 非终态的运单的 ID
   */
  private val bzLineOrderCache: MutableMap<String, Set<String>> = ConcurrentHashMap()
  
  @Synchronized
  fun init(sceneId: String) {
    logger.info("Init random-order service for $sceneId")
    configMap[sceneId] = getConfig(sceneId) ?: RandomOrderConfig()
    enabledMap[sceneId] = false
  }
  
  private fun loop(sr: SceneRuntime) {
    val config = configMap[sr.sceneId] ?: return
    when (config.mode) {
      RandomMode.Simple -> simpleRandomCreateOrder(sr)
      RandomMode.Group -> groupRandomCreateOrder(sr)
      RandomMode.BzLine -> bzLineRandomCreateOrder(sr)
    }
  }
  
  private fun simpleRandomCreateOrder(sr: SceneRuntime) {
    val simpleConfig = configMap[sr.sceneId]?.simpleConfig ?: return
    var orderNum = sr.orders.values.filter { it.order.kind == OrderKind.Business }.size
    val areaCaches = sr.mapCache.areaById.values.filter { !it.schema.disabled }
    while (orderNum < simpleConfig.keepOrderNum) {
      // val area = areaCaches.randomOrNull() ?: break
      val rr = sr.listRobots().filter { !it.disabled() }.randomOrNull() ?: break
      val gId = rr.config.groupId
      val gName = sr.robotGroups[gId]?.name ?: break
      // 创建随机运单
      createRandomOrder(sr, areaCaches, gId, gName, simpleConfig.containerTypeNames)
      orderNum++
    }
  }
  
  private fun groupRandomCreateOrder(sr: SceneRuntime) {
    val groupConfigs = configMap[sr.sceneId]?.groupConfigs ?: return
    for (group in groupConfigs) {
      val gId = NumHelper.anyToInt(group.robotGroupId) ?: break
      if (sr.mustGetRobotGroupById(gId).disabled) break
      var orderNum = sr.orders.values.filter {
        it.order.kind == OrderKind.Business && it.order.expectedRobotGroups?.contains(group.robotGroupName) == true
      }.size
      val areaCaches = sr.mapCache.areaById.values.filter { !it.schema.disabled && it.groupedMaps.containsKey(gId) }
      while (orderNum < group.keepOrderNum) {
        // val area = areaCaches.randomOrNull() ?: break
        // 创建随机运单
        createRandomOrder(sr, areaCaches, gId, group.robotGroupName, group.containerTypeNames)
        orderNum++
      }
    }
  }
  
  private fun createRandomOrder(
    sr: SceneRuntime,
    areas: List<SceneAreaCache>,
    gId: Int,
    groupName: String,
    containerTypes: List<String> = emptyList(),
  ) {
    if (areas.isEmpty()) return
    val fromArea = areas.random()
    val toArea = areas.random()
    val fromGmc = fromArea.groupedMaps[gId] ?: return
    val fromPoints = fromGmc.pointNameMap.values.filter { !it.point.disabled && it.point.type == "ActionPoint" }
    if (fromPoints.isEmpty()) return
    
    // 随机起点
    val fromPoint = fromPoints.random().point.name
    val toGmc = toArea.groupedMaps[gId] ?: return
    val toPoints = toGmc.pointNameMap.values.filter { !it.point.disabled && it.point.type == "ActionPoint" }
    if (toPoints.isEmpty()) return
    
    // 随机终点
    val toPoint = toPoints.random().point.name
    if (fromArea == toArea) {
      // 同一个区域内的运单
      val alg = SceneMapAlg(listOf(fromGmc), emptySet())
      if (!alg.getShortestPath(fromPoint, toPoint).found) return
      
      val b = sr.listRobots().filter { it.config.groupId == gId && !it.disabled() }.any { rr ->
        val curAreaId = rr.selfReport?.stand?.areaId ?: return@any false
        val curPoint = rr.expectedOccupiedPointName ?: return@any false
        if (curAreaId == fromArea.schema.id) {
          alg.getShortestPath(curPoint, fromPoint).found
        } else {
          // 机器人当前位置到起点的可达性
          LiftDispatcher.findBestLift(rr, curAreaId, fromArea.schema.id, curPoint, fromPoint) != null
        }
      }
      if (!b) return
    } else {
      // 跨区域的运单，一般要过一次电梯
      val b = sr.listRobots().filter { it.config.groupId == gId && !it.disabled() }.any { rr ->
        val curAreaId = rr.selfReport?.stand?.areaId ?: return@any false
        val curPoint = rr.expectedOccupiedPointName ?: return@any false
        // 机器人当前位置到起点的可达性
        val b1 = LiftDispatcher.findBestLift(rr, curAreaId, fromArea.schema.id, curPoint, fromPoint) != null
        // 起点到终点的可达性
        val b2 = LiftDispatcher.findBestLift(rr, fromArea.schema.id, toArea.schema.id, fromPoint, toPoint) != null
        b1 && b2
      }
      if (!b) return
    }
    
    // 随机单号
    val oId = OrderService.generateOrderId()
    val steps = listOf(
      TransportStep(
        id = "$oId-Step0",
        orderId = oId,
        stepIndex = 0,
        status = StepStatus.Executable,
        location = fromPoint,
        forLoad = true,
        withdrawOrderAllowed = true,
        rbkArgs = JsonHelper.writeValueAsString(mutableMapOf("operation" to "Load")),
      ),
      TransportStep(
        id = "$oId-Step1",
        orderId = oId,
        stepIndex = 1,
        status = StepStatus.Executable,
        location = toPoint,
        forUnload = true,
        withdrawOrderAllowed = false,
        rbkArgs = JsonHelper.writeValueAsString(mutableMapOf("operation" to "Unload")),
      ),
    )
    val order = TransportOrder(
      id = oId,
      status = OrderStatus.ToBeAllocated,
      containerId = "container-$oId",
      expectedRobotGroups = listOf(groupName),
      keyLocations = listOf(fromPoint),
      stepNum = steps.size,
      stepFixed = true,
      sceneId = sr.sceneId,
      containerTypeName = (if (containerTypes.isEmpty()) null else containerTypes.random()),
    )
    OrderService.createOrders(sr, listOf(OrderRuntime(order, steps)))
  }
  
  private fun bzLineRandomCreateOrder(sr: SceneRuntime) {
    configMap[sr.sceneId]?.bzLineConfigs?.forEach { bzLine ->
      try {
        val containerTypes = bzLine.containerTypeNames
        val cachedOrderIds = bzLineOrderCache[bzLine.id] ?: emptySet()
        val liveOrderIds = sr.orders.values.filter { it.id in cachedOrderIds }.map { it.id }.toMutableSet()
        if (liveOrderIds.size > bzLine.keepOrderNum) return@forEach
        // 校验机器人分组
        val robotGroups: List<String> = bzLine.robotGroupNames.let { StringHelper.splitTrim(it, ",") }
        val checkedRobotGroups = sr.robotGroups.values.filter { !it.disabled && it.name in robotGroups }
        if (robotGroups.isNotEmpty() && checkedRobotGroups.isEmpty()) {
          val ai = AlarmItem(
            sceneId = sr.sceneId,
            group = "Fleet",
            code = "RandomCreateOrder",
            key = "RandomCreateOrder-${bzLine.id}",
            message = I18N.lo("errCreateRandomOrderNoRobot", listOf(bzLine.id, robotGroups.joinToString(","))),
            level = AlarmLevel.Error,
            args = listOf(),
            tags = setOf(sr.tag),
          )
          AlarmService.addItem(ai, ttl = 5000)
          return@forEach
        }
        val robotGroupIds = checkedRobotGroups.map { it.id }
        val locations = findActionPointsCache(sr, robotGroupIds)
        val bzLineFromLabels: List<String> = bzLine.fromLabels.let { StringHelper.splitTrim(it, ",") }
        val bzLineToLabels: List<String> = bzLine.toLabels.let { StringHelper.splitTrim(it, ",") }
        // 根据标签选择起点，TODO 优化
        val fromPoints = locations.filter { it.m4labels.any { label -> label in bzLineFromLabels } }
        if (fromPoints.isEmpty()) return@forEach
        (0 until (bzLine.keepOrderNum - liveOrderIds.size)).map { count ->
          try {
            // val locationSize = locations.filter { it.m4labels.any { label -> label in bzLine.fromLabels } }
            // if (locationSize < 2) {
            //   // TODO 不满足创建运单的条件，但是不应该在这里处理。先 return。
            //   return@map
            // } else if (locationSize < bzLine.keepOrderNum * 2) {
            //   // TODO AP 点数量较少，告警，但是也不应该在这里处理。
            // }
            val fromLoc = fromPoints.random()
            // 根据标签选择终点，TODO 优化
            val toPoints = locations.filter {
              it.name != fromLoc.name && it.m4labels.any { label -> label in bzLineToLabels }
            }
            if (toPoints.isEmpty()) {
              // 及时写入内存
              bzLineOrderCache[bzLine.id] = liveOrderIds
              return@forEach
            }
            
            val toLoc = toPoints.random()
            val orderId = OrderService.generateOrderId()
            val containerId = IdHelper.oidStr()
            
            // 创建运单步骤，在起点 Load，在终点 Unload。
            val steps = listOf(fromLoc, toLoc).mapIndexed { index, loc ->
              // 由于不是库位，所以只能用 operation
              val rbkArgsObj = mapOf("operation" to if (index == 0) "Load" else "Unload")
              TransportStep(
                id = "$orderId-Step$index",
                orderId = orderId,
                stepIndex = index,
                location = loc.name,
                rbkArgs = JsonHelper.writeValueAsString(rbkArgsObj),
                forLoad = index == 0,
                forUnload = index == 1,
                withdrawOrderAllowed = index == 0,
              )
            }
            
            // 创建运单
            val order = TransportOrder(
              id = orderId,
              status = OrderStatus.ToBeAllocated,
              containerId = containerId, // 先用单号表示吧，不然可能不适用于料箱车了。
              expectedRobotGroups = checkedRobotGroups.map { it.name },
              keyLocations = steps.map { it.location },
              stepNum = steps.size,
              stepFixed = true,
              sceneId = sr.sceneId,
              containerTypeName = (if (containerTypes.isEmpty()) null else containerTypes.random()),
            )
            OrderService.createOrders(sr, listOf(OrderRuntime(order, steps)))
            liveOrderIds.add(orderId)
          } catch (e: Exception) {
            FleetLogger.error(
              module = "RandomOrder",
              subject = "CreateByBzLine",
              sr = sr,
              robotName = null,
              msg = mapOf(
                "msg" to "create order failed, (${count + liveOrderIds.size}/${bzLine.keepOrderNum})",
                "errMsg" to e.getTypeMessage(),
              ),
            )
          }
        }
        
        bzLineOrderCache[bzLine.id] = liveOrderIds
      } catch (e: Exception) {
        FleetLogger.error(
          module = "RandomOrder",
          subject = "CreateByBzLine",
          sr = sr,
          robotName = null,
          msg = mapOf("errMsg" to e.getTypeMessage()),
        )
      }
    }
  }
  
  private fun findActionPointsCache(sr: SceneRuntime, robotGroupIds: List<Int>): List<MapPoint> {
    val enabledAreas = sr.mapCache.areaById.values.filter { !it.schema.disabled }
    if (enabledAreas.isEmpty()) throw BzError("errCreateRandomOrderNoEnabledArea")
    
    val areasContainsMap = enabledAreas.filter { it.groupedMaps.keys.isNotEmpty() }
    if (areasContainsMap.isEmpty()) throw BzError("errCreateRandomOrderButAllAreasNoMap")
    
    // 没有指定机器人分组，则随机选择一个区域
    if (robotGroupIds.isEmpty()) {
      // 随机选择一个可用区域，再从此区域中随机选择一张地图，并提取此地图中所有未禁用的 AP 点。
      // TODO 如果随机发达需要考虑夸区域的话，这里需要再处理下。
      return areasContainsMap.random().groupedMaps.values.random().pointNameMap.values.filter {
        !it.point.disabled && it.point.type == "ActionPoint"
      }.map { it.point }
    } else {
      // 指定了机器人分组，则还需要筛选机器人所在的区域
      val areasWithExpectedRobotGroups =
        areasContainsMap.filter { it.groupedMaps.keys.any { rgId -> rgId in robotGroupIds } }
      if (areasWithExpectedRobotGroups.isEmpty()) throw BzError("errCreateRandomOrderNoAreaForRobotGroups")
      
      return areasWithExpectedRobotGroups.random().groupedMaps.values.random().pointNameMap.values.filter {
        !it.point.disabled && it.point.type == "ActionPoint"
      }.map { it.point }
    }
  }
  
  private fun submit(sceneId: String, newEnabled: Boolean) {
    val enabled = enabledMap[sceneId] ?: return
    val sr = SceneService.mustGetSceneById(sceneId)
    enabledMap[sceneId] = if (newEnabled && enabled) {
      // 已启用了则直接返回
      return
    } else if (newEnabled) {
      // 未启用过，新请求启用
      true
    } else if (enabled) {
      // 已启用，但新请求关闭启用
      false
    } else {
      return
    }
    
    PollingJobManager.submit(
      threadName = "Ft-random-order-${sr.basic.name}",
      remark = "Create random order for scene ${sr.basic.name}",
      interval = { 500L },
      logger = logger,
      workerMaxTime = -1,
      stopCondition = { sr.status == SceneStatus.Disposed || !enabledMap[sceneId]!! },
      exceptionContinue = true,
      tags = setOf(sr.tag),
      worker = { loop(sr) },
    )
  }
  
  @Synchronized
  fun save(sceneId: String, reqConfig: RandomOrderConfig) {
    logger.info("Save random create order config")
    val fixConfig = when (reqConfig.mode) {
      RandomMode.Simple -> reqConfig.copy(groupConfigs = null, bzLineConfigs = null)
      RandomMode.Group -> reqConfig.copy(simpleConfig = null, bzLineConfigs = null)
      RandomMode.BzLine -> reqConfig.copy(simpleConfig = null, groupConfigs = null)
    }
    // configMap 用于缓存是否启用状态的配置，持久化时，均按不启用状态保存
    configMap[sceneId] = fixConfig
    JsonFileHelper.writeJsonToFile(getConfigFile(sceneId), fixConfig.copy(enabled = false), true)
    
    submit(sceneId, fixConfig.enabled)
  }
  
  private fun getConfigFile(sceneId: String): File {
    val dir = SceneFileService.getSceneDir(sceneId)
    return File(dir, "random-order-config.json")
  }
  
  fun getConfig(sceneId: String): RandomOrderConfig? =
    configMap[sceneId] ?: JsonFileHelper.readJsonFromFile(getConfigFile(sceneId))
}