package com.seer.trick.fleet.device.lift

import com.seer.trick.BzError
import com.seer.trick.I18N
import com.seer.trick.base.alarm.AlarmItem
import com.seer.trick.base.alarm.AlarmLevel
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.base.concurrent.PollingJobManager
import com.seer.trick.fleet.FleetLogger
import com.seer.trick.fleet.device.lift.adapter.LiftAdapterJinBo
import com.seer.trick.fleet.device.lift.adapter.LiftAdapterMock
import com.seer.trick.fleet.device.lift.adapter.LiftAdapterScript
import com.seer.trick.fleet.domain.SceneStatus
import com.seer.trick.fleet.service.FleetEvent
import com.seer.trick.fleet.service.FleetEventService
import com.seer.trick.fleet.service.SceneFileService
import com.seer.trick.fleet.service.SceneRuntime
import com.seer.trick.helper.JsonFileHelper
import com.seer.trick.helper.getTypeMessage
import org.slf4j.LoggerFactory
import java.io.File

/**
 * 电梯的基础服务
 */
object LiftService {
  
  private val logger = LoggerFactory.getLogger(javaClass)
  
  fun init() {
    //
  }
  
  fun dispose() {
  }
  
  /**
   * 加载并初始化一个场景下的电梯
   */
  fun init(sr: SceneRuntime) = sr.withOrderLock {
    val liftConfigs: List<SceneLift> = JsonFileHelper.readJsonFromFile(getLiftsFile(sr.sceneId)) ?: emptyList()
    sr.lifts.clear()
    
    val lifts = liftConfigs.map {
      val lr = LiftRuntime(it)
      initLift(sr, lr)
      lr
    }.associateBy { it.config.id }
    
    sr.lifts.putAll(lifts)
    
    PollingJobManager.submit(
      threadName = "FtLift-${sr.no}",
      remark = "Fetching lifts states for scene $sr",
      interval = { 1000L },
      logger = logger,
      workerMaxTime = 30 * 1000,
      stopCondition = { sr.status == SceneStatus.Disposed },
      exceptionContinue = true,
      tags = setOf(sr.tag),
      worker = { reportAll(sr) },
    )
  }
  
  fun getLiftTag(sr: SceneRuntime): String = "FtLift-${sr.no}"
  
  private fun initLift(sr: SceneRuntime, lr: LiftRuntime) {
    if (lr.config.disabled) return
    
    // 选择适配器类型
    if (lr.config.mock) {
      lr.adapter = LiftAdapterMock(lr.config)
    } else {
      when (lr.config.adapterType) {
        LiftAdapterType.JinBo -> lr.adapter = LiftAdapterJinBo(lr.config)
        LiftAdapterType.Script -> lr.adapter = LiftAdapterScript(lr.config)
        LiftAdapterType.Mock -> lr.adapter = LiftAdapterMock(lr.config)
      }
    }
    
    // 适配器初始化
    lr.adapter.init(sr)
  }
  
  /**
   * 添加电梯
   */
  fun add(sr: SceneRuntime, req: SceneLift): SceneLift {
    val lift = sr.withOrderLock {
      // 电梯名称唯一
      if (sr.lifts.values.any { it.config.name == req.name }) {
        throw BzError("errNameConflict", req.name) // 添加失败，已经存在同名的电梯了。
      }
      
      val lift = req.copy(id = (sr.lifts.values.maxOfOrNull { it.config.id } ?: 0) + 1)
      val lr = LiftRuntime(lift)
      sr.lifts[lift.id] = lr
      initLift(sr, lr)
      
      val liftConfigs = sr.lifts.values.map { it.config }.toMutableList()
      liftConfigs += lift
      persist(sr, liftConfigs)
      lift
    }
    
    logger.info("Create lift of scene $sr: $lift")
    
    FleetEventService.fire(
      FleetEvent(
        name = "Lift::Create",
        sceneId = sr.sceneId,
        extra = mapOf("liftId" to lift.id),
      ),
    )
    
    return lift
  }
  
  /**
   * 删除电梯
   */
  fun remove(sr: SceneRuntime, liftIds: List<Int>) {
    sr.withOrderLock {
      for (id in liftIds) {
        // TODO
        //   1.不能删除正在被使用的电梯。
        //   2.如何判断电梯正在被使用？
        
        val lr = sr.lifts.remove(id)
        if (lr != null) dispose(lr)
      }
      
      val liftConfigs = sr.lifts.values.map { it.config }
      persist(sr, liftConfigs)
    }
    
    logger.info("Removed lifts from scene ${sr.sceneId}: $liftIds")
    
    FleetEventService.fire(
      
      FleetEvent(
        name = "Lift::Remove",
        sceneId = sr.sceneId,
        extra = mapOf("liftIds" to liftIds),
      ),
    )
  }
  
  /**
   * 不抛异常
   */
  private fun dispose(dr: LiftRuntime) {
    dr.adapter.dispose()
  }
  
  /**
   * 修改电梯。
   */
  fun update(sr: SceneRuntime, lift: SceneLift) {
    sr.withOrderLock {
      // TODO 不能修改正在被使用的电梯
      
      val lr = sr.lifts[lift.id] ?: return@withOrderLock
      dispose(lr)
      lr.config = lift
      initLift(sr, lr)
      
      val liftConfigs = sr.lifts.values.map { it.config }
      persist(sr, liftConfigs)
    }
    
    logger.info("Updated lift ${lift.id} of scene ${sr.sceneId}")
    
    FleetEventService.fire(
      
      FleetEvent(
        name = "Lift::Update",
        sceneId = sr.sceneId,
        extra = mapOf("liftId" to lift.id),
      ),
    )
  }
  
  private fun persist(sr: SceneRuntime, lifts: List<SceneLift>) {
    JsonFileHelper.writeJsonToFile(getLiftsFile(sr.sceneId), lifts, true)
  }
  
  private fun getLiftsFile(sceneId: String): File {
    val dir = SceneFileService.getSceneDir(sceneId)
    return File(dir, "lifts.json")
  }
  
  /**
   * 定时更新电梯的运行状态
   */
  private fun reportAll(sr: SceneRuntime) {
    if (sr.basic.disabled) return
    
    for (lr in sr.lifts.values) {
      fetchReports(sr, lr)
    }
  }
  
  /**
   * 获取电梯的运行状态并更新到内存中。
   */
  private fun fetchReports(sr: SceneRuntime, lr: LiftRuntime) {
    if (lr.config.disabled) return
    try {
      val old = lr.toUiReport()
      val report = lr.adapter.report()
      lr.online = report.online // 如果直接赋值 true 的话，会导致无法修改仿真电梯的在线状态。
      lr.fault = report.fault
      lr.faultMsg = report.faultMsg
      lr.lastStatusUpdateTime = report.timestamp
      lr.autoMode = report.autoMode
      lr.currentFloor = report.currentFloor?.toString()
      lr.targetFloor = report.targetFloor?.toString()
      lr.doors = report.doors
      lr.people = report.people
      
      val new = lr.toUiReport()
      if (old.toStringExceptTime() != new.toStringExceptTime()) {
        // TODO 优化：电梯信息较多，（人或者机器人）频繁使用电梯时会导致刷屏。
        FleetLogger.info(
          module = "FetchLiftReport",
          subject = "Changed",
          sr = sr,
          robotName = null,
          msg = mapOf("name" to lr.config.name, "new" to new, "old" to old), // 先打印更新后的，方便看日志。
        )
      }
    } catch (e: Exception) {
      lr.online = false // 一定么？
      lr.fault = true
      lr.faultMsg = e.getTypeMessage() // todo 是否会存在多条错误。
      // 此时不修改 status 和 lastStatusUpdateTime
      val ai = AlarmItem(
        sceneId = sr.sceneId,
        group = "Fleet",
        code = "FetchLiftReportFailed",
        key = "FetchLiftReportFailed-${lr.config.name}",
        level = AlarmLevel.Error,
        message = I18N.lo("errFetchLiftReportFailed", listOf(lr.config.name, e.getTypeMessage())),
        tags = setOf(sr.tag),
      )
      AlarmService.addItem(ai, ttl = 5000)
    }
  }
  
  /**
   * 获取电梯的展示信息：电梯名称 -> 电梯个运行时信息。
   */
  fun listLiftUiReports(sr: SceneRuntime): Map<String, Any> =
    sr.lifts.values.map { it.toUiReport() }.associateBy { it.name }
  
  fun mustGetLiftById(sr: SceneRuntime, liftId: Int) = sr.lifts[liftId]
    ?: throw BzError("errLiftNotFoundById", sr.basic.name, liftId)
  
  fun mustGetLiftByName(sr: SceneRuntime, liftName: String) = sr.lifts.values.firstOrNull { it.config.name == liftName }
    ?: throw BzError("errLiftNotFoundByName", sr.basic.name, liftName)
  
  fun updateMockLiftRecords(sr: SceneRuntime, ids: Set<Int>, update: UpdateLiftRecord) {
    ids.forEach {
      val lr = mustGetLiftById(sr, it)
      if (lr.adapter !is LiftAdapterMock) throw BzError("errLiftNotMock", lr.config.name)
      (lr.adapter as LiftAdapterMock).updateRecord(update)
    }
  }
  
  fun gotoAndOpenDoor(sr: SceneRuntime, lr: LiftRuntime, targetFloor: Int, remark: String) {
    if (lr.config.disabled) throw BzError("errLiftDisabled", lr.config.name)
    lr.adapter.gotoOpenDoor(null, targetFloor, remark)
  }
  
  fun closeDoor(sr: SceneRuntime, lr: LiftRuntime, remark: String) {
    lr.adapter.closeDoor(null, remark)
  }
  
  /**
   * 启用、禁用电梯的仿真模式。
   */
  fun changeAlLiftsSimulated(sr: SceneRuntime, simulated: Boolean) {
    sr.withOrderLock {
      // TODO 不能修改正在被使用的电梯
      val liftConfigs = sr.lifts.values.map { lr ->
        // 启用仿真时，忽略已经是仿真适配器的电梯
        // 取消仿真时，忽略适配器类型就是 LiftAdapterType.Mock 的电梯。
        if ((simulated && lr.adapter is LiftAdapterMock) ||
          (!simulated && lr.config.adapterType == LiftAdapterType.Mock)
        ) {
          return@map lr.config
        }
        
        // 先 dispose
        dispose(lr)
        
        // 修改内存的配置
        val config = lr.config.copy(mock = simulated)
        lr.config = config
        
        // 重新初始化
        initLift(sr, lr)
        
        FleetEventService.fire(
          FleetEvent(
            name = "Lift::Update",
            sceneId = sr.sceneId,
            extra = mapOf("liftId" to lr.config.id, "msg" to "mock = $simulated"),
          ),
        )
        
        config
      }
      
      persist(sr, liftConfigs)
    }
    logger.info("Change simulated of all lifts for $sr")
  }
}