package com.seer.trick.fleet.traffic.venus

/**
 * 冲突避免表（Conflict Avoidance Table，CAT）。
 * 以 [location, time] 对的形式存储被占用的信息，以便快速冲突查询。
 */
class ConflictAvoidanceTable(private val windowLimit: Long? = null) {

  private val table: MutableMap<Long, MutableSet<String>> = HashMap() // 时间 -> 占用点名集合
  private var maxTime: Long = 0

  /**
   * 将完整的机器人路径插入 CAT。
   */
  fun addPath(path: List<State>) {
    for (state in path) {
      val ts = state.timeStart
      // val te = minOf(state.timeEnd, windowLimit ?: Long.MAX_VALUE)
      val te = state.timeEnd
      // 如果状态完全在窗口外，跳过
      // if (ts >= (windowLimit ?: Long.MAX_VALUE)) continue

      val locId = state.toPosition.pointName ?: continue
      for (t in ts..te) {
        table.computeIfAbsent(t) { HashSet() }.add(locId)
      }
      if (te > maxTime) maxTime = te
    }
  }

  /**
   * 在从 `curr` 移动到 `next` 时，检查时间步 `t` 是否存在冲突。
   */
  fun hasConflict(currPointName: String, nextPointName: String, t: Long): Boolean {
    if (t > maxTime) return false
    val pointsAtT = table[t] ?: return false
    // 顶点冲突（Vertex conflict）
    if (pointsAtT.contains(nextPointName)) return true
    // 边冲突（Edge conflict，swap）
    if (currPointName != nextPointName) {
      val pointsPrev = table[t - 1]
      if (pointsPrev != null && pointsPrev.contains(nextPointName) && pointsAtT.contains(currPointName)) return true
    }
    return false
  }
}