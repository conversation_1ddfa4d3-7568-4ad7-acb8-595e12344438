package com.seer.trick.fleet.traffic.distributed.deadlock.link

import com.seer.trick.fleet.traffic.distributed.block.BlockItem
import com.seer.trick.fleet.traffic.distributed.block.BlockService
import com.seer.trick.fleet.traffic.distributed.context.ContextManagerService
import com.seer.trick.fleet.traffic.distributed.context.domain.*
import com.seer.trick.fleet.traffic.distributed.deadlock.DeadLockProcessor
import com.seer.trick.fleet.traffic.distributed.deadlock.helper.DeadLockHelper
import com.seer.trick.fleet.traffic.distributed.deadlock.helper.PathCollisionHelper
import com.seer.trick.fleet.traffic.distributed.deadlock.model.DeadLockMessage
import com.seer.trick.fleet.traffic.distributed.deadlock.model.RobotNode
import com.seer.trick.fleet.traffic.distributed.lock.LockType
import org.slf4j.LoggerFactory
import java.util.*

/**
 *  环死锁处理
 * */
object LinkDeadLockProcessor : DeadLockProcessor {

  private val logger = LoggerFactory.getLogger(javaClass)

  private const val ROBOT_STOP_TIME = 5 * 1000
  override fun check(robotName: String, block: MutableList<BlockItem>): DeadLockMessage {
    val context = ContextManagerService.queryRobotContext(robotName)

    val deadLockMessage =
      DeadLockMessage(robotName = robotName, mapName = context.mapName, context = context)
    // 停下来的时间
    if (System.currentTimeMillis() - context.pauseTime <= ROBOT_STOP_TIME) {
      logger.info("$robotName |robot is not stop")
      return deadLockMessage
    }
    if (context.state == TrafficStatus.DEADLOCK) {
      logger.info("$robotName |robot is in deadlock")
      return deadLockMessage
    }
    if (context.deadLock.linkTime > 0 &&
      System.currentTimeMillis() - context.deadLock.linkTime < ROBOT_STOP_TIME
    ) {
      logger.info(
        "$robotName  the deadlock too often. Wait a moment " +
          "${ROBOT_STOP_TIME - (System.currentTimeMillis() - context.deadLock.linkTime)}",
      )
      return deadLockMessage
    }
    val robot = PathCollisionHelper.pathCollisionRobot(context.sceneId, context.plan.queryNextAction())
    if (robot != null && !block.map { it.code }.contains(robot)) {
      block.add(0, BlockItem(code = robot, type = LockType.ROBOT, point = null, version = null))
    }
    // 检测机器人是否成环
    val link = checkLink(robotName, block)
    if (link.isEmpty()) {
      logger.info("$robotName |block is not link")
      return deadLockMessage
    }
    deadLockMessage.state = true
    deadLockMessage.linkRobots = link
    return deadLockMessage
  }

  override fun handle(message: DeadLockMessage): Boolean {
    logger.info("${message.robotName}|LinkDeadLockProcessor handle dead lock start, link robots ${message.linkRobots}")
    val link = message.linkRobots
    if (link.isEmpty() || link.size < 2) {
      logger.warn("${message.robotName}|LinkDeadLockProcessor link is annulus")
      return false
    }
    // 处理和更新环的死锁信息
    if (!processLink(link, message.context)) {
      return false
    }
    // 处理机器人路线上的阻挡物有哪些
    message.blockMap = processOnPath(link)
    // 优先级处理
    priority(message)
    val result = AvoidDeadLockHandler.process(message)
    val success = result.any { it.value }
    if (!success) {
      val calRobot = message.calRobot
      if (calRobot != null) {
        val context = ContextManagerService.queryRobotContext(calRobot)
        val list = message.blockMap[calRobot]
        if (!list.isNullOrEmpty()) {
          for (block in list) {
            if (block == calRobot) continue
            val robotContext = ContextManagerService.queryRobotContext(block)
            robotContext.deadLock.failIncrease()
            robotContext.deadLock.linkSuccessPoint = null
          }
        }
        context.deadLock.failIncrease()
        context.deadLock.linkSuccess = false
        context.deadLock.linkRobots = link
        context.deadLock.linkSuccessPoint = null
      }
    } else {
      val calRobot = message.calRobot
      if (calRobot != null && result[calRobot] == true) {
        val context = ContextManagerService.queryRobotContext(calRobot)
        context.deadLock.linkSuccess()
      }
    }
    return success
  }

  private fun processLink(link: MutableList<String>, firstContext: RobotContext): Boolean {
    // 检测所有车的解死锁时间并进行更新
    val linkContexts: MutableList<RobotContext> = mutableListOf()
    for (robot in link) {
      val context = ContextManagerService.queryRobotContext(robot)
      // 时间检测
      val time = context.deadLock.linkTime
      if (time > 0 && System.currentTimeMillis() - time < ROBOT_STOP_TIME) {
        logger.info(
          "$robot |unlocking the deadlock too often. Wait a moment " +
            "${ROBOT_STOP_TIME - (System.currentTimeMillis() - time)}",
        )
        return false
      }
      linkContexts.add(context)
    }
    if (!link.contains(firstContext.robotName)) {
      linkContexts.add(firstContext)
    }
    for (context in linkContexts) {
      context.deadLock.linkTime = System.currentTimeMillis()
    }
    return true
  }

  /**
   * 优先级
   * 1、空载让带载
   * 2、少车让多车
   * 3、失败的车让其他车解
   * */
  private fun priority(message: DeadLockMessage) {
    val linkedRobots = message.linkRobots
    val blockMap = message.blockMap
    val priorMap: MutableMap<String, Int> = mutableMapOf()
    for (robot in linkedRobots) {
      var prior = 1000
      val context = ContextManagerService.queryRobotContext(robot)

      val loading = context.baseDomain.isLoading()
      if (loading) prior += 5000
      if (context.deadLock.linkSuccess &&
        (System.currentTimeMillis() - context.deadLock.linkTime) < 60 * 1000
      ) {
        prior -= 1500
      }
      if (context.deadLock.failCount > 0) prior += context.deadLock.failCount * 1000
      val values = blockMap[robot]
      if (values != null && values.isNotEmpty()) prior += 2000 * values.size
      priorMap[robot] = prior
    }
    val mapByValue = DeadLockHelper.sortMapByValue(priorMap)
    val firstKey = mapByValue.keys.first()
    logger.debug("priority $mapByValue and first process robot: $firstKey")
    message.calRobot = firstKey
  }

  private fun processOnPath(link: MutableList<String>): MutableMap<String, MutableList<String>> {
    val map: MutableMap<String, MutableList<String>> = mutableMapOf()
    val allContext = ContextManagerService.queryAllRobotContext()
      .filter { c -> c.state == TrafficStatus.RUNNING || c.state == TrafficStatus.DEADLOCK }.toMutableList()
//    val robots = allContext.map { context -> context.robotName }.toList()
    val allBlocks = BlockService.findAllBlocks().filter { it.key !in link }
    for (robot in link) {
      val block = BlockService.findBlock(robot)
      val context = ContextManagerService.queryRobotContext(robot)

      val items = this.findRobot(block).map { blockItem -> blockItem.code }.toMutableList()
      val blocks = findBlockOnPath(context, items, allContext, link)
      if (blocks.isNotEmpty()) {
        val first = blocks[0]
        val nums = mutableListOf<String>()
        nums.addAll(blocks)
        nums.addAll(link)
        val last = blocks.removeLast()

        val follows = findLinkFollow(
          ContextManagerService.queryRobotContext(last),
          allContext.filter { System.currentTimeMillis() - it.pauseTime > ROBOT_STOP_TIME }.toMutableList(),
          nums,
          allBlocks,
        )
        blocks.addAll(follows)
        logger.debug("$robot find block on path: $blocks")
        if (link.contains(first)) {
          map[first] = blocks
        }
      }
//      map[robot] = blocks
    }
    logger.info("$link on path | $map")
    return map
  }

  private fun findLinkFollow(
    robot: RobotContext,
    allContext: List<RobotContext>,
    link: MutableList<String>,
    allBlocks: Map<String, MutableList<BlockItem>>,
  ): MutableList<String> {
    val result = mutableListOf<String>()
    // 定义当前的 context
    var context: RobotContext? = robot
    var nextContext: RobotContext? = null
    var maxLimit = 10
    while (context != null && maxLimit > 0) {
      result.add(context.robotName)
      // 检查阻挡得障碍物
      var bflag = false
      for (block in allBlocks) {
        if (result.contains(block.key)) continue
        if (block.value.find { it.code == context!!.robotName && it.type == LockType.ROBOT } != null) {
          val nc = try {
            ContextManagerService.queryRobotContext(block.key)
          } catch (e: Exception) {
            logger.error("find robot ${block.key} context is null!")
            null
          }
          if (nc == null) continue
          context = nc
          if (context.state == TrafficStatus.IDLE || context.plan.path.isEmpty()) continue
          logger.debug("${robot.robotName} find block on path: ${context.robotName} -> ${block.key}")
          bflag = true
          maxLimit--
          break
        }
      }
      if (bflag) continue
      enter@ for (next in allContext) {
        if (next.robotName == context?.robotName || next.robotName in link || next.robotName in result) continue
        // 检测 距离 和 索引的差值
        val path = next.plan.restPath()
        var loopNum = 0
        var distance = 0.0
        for (p in path) {
          if (loopNum > 10 || distance > next.baseDomain.length * 2) {
            break
          }
          if (p.target.pointName == context?.baseDomain?.curPoint?.pointName) {
            nextContext = next
            break@enter
          }
          loopNum++
          distance += p.target.distance(p.start)
        }
      }
      context = nextContext
      nextContext = null
      maxLimit--
    }
    return result
  }

  private fun findLinkFollow1(
    robot: RobotContext,
    blocks: Map<String, MutableList<BlockItem>>,
    link: MutableList<String>,
  ): MutableList<String> {
    // 找到是否存在阻挡的机器人为当前机器人
    val result = mutableListOf<String>()
    var robotName = robot.robotName
    var maxLimit = 50
    do {
      val followRobots = findFollowRobot(robotName, blocks)
      result.add(robotName)
      if (followRobots.isNotEmpty()) {
        // 若有多个，择其一而选
        robotName = followRobots.first { !result.contains(it) }
      }
      maxLimit--
    } while (followRobots.isNotEmpty() && maxLimit > 0)
    // 对结果做后处理，将一些长远等待的机器人排除出去
    return postProcess(robot, result, link)
  }

  private fun postProcess(
    robot: RobotContext,
    follows: MutableList<String>,
    link: MutableList<String>,
  ): MutableList<String> {
    val result: MutableList<String> = mutableListOf()
    val map = link.filter { it != robot.robotName }
      .associateBy { robotName -> ContextManagerService.queryRobotContext(robotName).baseDomain.curPoint?.pointName }
    // 首先检测前两个，再检测之后的
    if (follows.size > 1) {
      val block = BlockService.findBlock(follows[1])
      if (block.first { it.code == follows[0] }.type == LockType.ROBOT_WAIT) {
        // 检测是否远距离等待的
        val context = ContextManagerService.queryRobotContext(follows[1])
        val restPath = context.plan.restPath()
        for (p in restPath) {
          if (map.keys.contains(p.target.pointName)) {
            if (map[p.target.pointName] == follows[0]) {
              return follows
            } else {
              // 截断
              result.add(follows[0])
              return result
            }
          }
        }
      }
    }
    // todo 待完善
    return follows
  }

  private fun findFollowRobot(robotName: String, blocks: Map<String, MutableList<BlockItem>>): MutableList<String> {
    val followRobots = mutableListOf<String>()
    for (b in blocks) {
      if (b.value.find { it.code == robotName } != null) {
        followRobots.add(b.key)
      }
    }
    return followRobots
  }

  private fun findBlockOnPath(
    robot: RobotContext,
    items: MutableList<String>,
    allContext: MutableList<RobotContext>,
    link: MutableList<String>,
  ): MutableList<String> {
    val list = robot.plan.restPath().map { action -> action.target.pointName }.toList()
    val map: MutableMap<Int, String> = mutableMapOf()
    map[0] = items.first { it in link }
    for (context in allContext) {
      if (context.robotName == robot.robotName) continue
      val pointCode = if (context.plan.allocateIndex.toInt() == -1) {
        context.baseDomain.curPoint?.pointName
      } else {
        context.plan.queryCurrentAction()?.target?.pointName
      }
      if (pointCode != null && pointCode in list) {
//        if (context.robotName in items) continue // items.remove(context.robotCode)
        val index = list.indexOfFirst { it == pointCode }
        // 存在重复进行提示并覆盖之前的
        if (map.keys.contains(index)) {
          logger.warn("${context.robotName} index $index is exist, it is ${map[index]}")
        }
        map[index] = context.robotName
      }
    }
    val blocks = map.keys.sorted().mapNotNull { i -> map[i] }.toMutableList()
    // 添加到后面
    val blockRobots: MutableList<String> = LinkedList()
    if (blocks.isNotEmpty()) blockRobots.add(blocks[0])
    for (i in 1 until blocks.size) {
      val p = blocks[i - 1]
      val b = blocks[i]
      val findBlock = BlockService.findBlock(b)
      for (bl in findBlock) {
        if (bl.code == p) {
          if (!blockRobots.contains(p)) {
            break
          }
          blockRobots.add(b)
        }
      }
    }
    // todo 概率很小的情况暂时没有考虑，等遇到再调整
    if (blockRobots.isNotEmpty()) {
      for (robotName in blockRobots) {
        try {
          val context = ContextManagerService.queryRobotContext(robotName)

          context.deadLock.linkTime = System.currentTimeMillis()
        } catch (e: Exception) {
          logger.error("query robot $robotName context is null!")
        }
      }
      val b = blockRobots[0]
      val findBlock = BlockService.findBlock(b)
      for (bl in findBlock) {
        if (items.contains(bl.code) && bl.type == LockType.ROBOT) {
          blockRobots.add(0, bl.code)
          break
        }
      }
    }
    // items.addAll(blocks)
    return blockRobots
  }

  private fun checkLink(robot: String, block: MutableList<BlockItem>): MutableList<String> {
    val items = this.findRobot(block)
    if (items.isEmpty()) {
      logger.warn("$robot |block is not robot")
      return mutableListOf()
    }
    val blockMap = mutableMapOf<String, MutableList<BlockItem>>()
    blockMap.put(robot, items)
    val linkNode = RobotNode(null, robot)
    val queue: Queue<RobotNode> = LinkedList()
    val close: MutableList<String> = mutableListOf()
    var version: String? = null
    queue.offer(linkNode)
    while (queue.isNotEmpty()) {
      val node = queue.poll()
      close.add(node.robotName)
      val next = blockMap[node.robotName] ?: continue
      for (item in next) {
        // 死锁预防考虑版本问题
        if (item.type == LockType.ROBOT_WAIT) {
          if (version == null) {
            version = item.version
          } else if (version != item.version) {
            continue
          }
        }
        // 查询机器人的状态是否为运行状态，并且是否已经被阻挡
        val robotContext = ContextManagerService.queryRobotContext(item.code)
        // todo 条件 待后期完善看是否还需要添加
        if (!(
            (robotContext.state == TrafficStatus.RUNNING || robotContext.state == TrafficStatus.DEADLOCK) &&
              robotContext.baseDomain.status == RobotStatus.WORK &&
              System.currentTimeMillis() - robotContext.pauseTime > ROBOT_STOP_TIME
            )
        ) {
          continue
        }
        if (item.code in close) {
          // 回溯
          if (recall(node, item.code)) {
            return buildLink(node, item.code)
          }
          continue
        }

        val block0 = BlockService.findBlock(item.code)
        val robot = PathCollisionHelper.pathCollisionRobot(robotContext.sceneId, robotContext.plan.queryNextAction())
        if (robot != null && !block0.map { it.code }.contains(robot)) {
          block0.add(0, BlockItem(code = robot, type = LockType.ROBOT, point = null, version = null))
        }

        val items0 = this.findRobot(block0)
        if (items0.isEmpty()) {
          logger.info("${item.code} |block is not robot")
          continue
        }
        val node0 = RobotNode(node, item.code)
        blockMap[item.code] = items0
        queue.add(node0)
      }
    }
    return mutableListOf()
  }

  private fun buildLink(node: RobotNode, code: String): MutableList<String> {
    val link: MutableList<String> = mutableListOf()
    var current = node
    do {
      link.add(current.robotName)
      current = current.pre!!
    } while (current.robotName != code && current.pre != null)
    if (link.isNotEmpty()) link.add(current.robotName)
    return link
  }

  // 回溯
  private fun recall(node: RobotNode?, code: String): Boolean {
    var current = node
    while (current != null) {
      if (current.robotName == code) {
        return true
      }
      current = current.pre
    }
    return false
  }
}