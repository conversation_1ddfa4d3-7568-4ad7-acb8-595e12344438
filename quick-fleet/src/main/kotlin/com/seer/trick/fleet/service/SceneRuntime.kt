package com.seer.trick.fleet.service

import com.seer.trick.BzError
import com.seer.trick.base.SysEmc
import com.seer.trick.fleet.algorithm.MatchingAlgorithm
import com.seer.trick.fleet.device.door.DoorRuntime
import com.seer.trick.fleet.device.lift.LiftRuntime
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.map.SceneMapCache
import com.seer.trick.fleet.mars.MarsService
import com.seer.trick.fleet.order.DispatchOrderService
import com.seer.trick.fleet.order.DispatchProfile
import com.seer.trick.fleet.order.OrderRuntime
import com.seer.trick.fleet.traffic.DummyTrafficService
import com.seer.trick.fleet.traffic.TrafficService
import com.seer.trick.helper.BoolHelper
import com.seer.trick.helper.NumHelper
import com.seer.trick.helper.StringHelper
import com.seer.trick.helper.shortId
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

/**
 * 一个场景的运行时状态。只封装数据和对数据直接读写的方法。不封装业务。
 * 比如，根据 id 获取运单、列出所有运单，是直接读写；但获取"已执行"的运单，是业务逻辑。
 */
class SceneRuntime(
  /**
   * 场景基础信息
   */
  @Volatile var basic: SceneBasic,
) {

  /**
   * 一个自增的编号
   */
  val no = synchronized(javaClass) { noCounter++ }

  /**
   * PollingJob 和 alarm 的标签
   */
  val tag = "Scene-${basic.id}"

  /**
   * 场景 ID
   */
  val sceneId = basic.id

  /**
   * 此场景主状态
   */
  @Volatile
  var status: SceneStatus = SceneStatus.Disabled

  /**
   * 场景配置
   */
  @Volatile
  var config: SceneConfig = SceneConfig()

  /**
   * 地图区域配置。因为有 mapCache，所以这里是 list。
   */
  @Volatile
  var areas: List<SceneArea> = emptyList()

  /**
   * 机器人组配置
   */
  @Volatile
  var robotGroups: Map<Int, RobotGroup> = emptyMap()

  /**
   * 容器类型配置
   */
  @Volatile
  var containerTypes: Map<Int, SceneContainerType> = emptyMap()

  /**
   * 地图信息缓存，用于加速访问
   */
  val mapCache = SceneMapCache(this)

  /**
   * 所有机器人。停用的也在里面。by robot name
   */
  val robots: MutableMap<String, RobotRuntime> = ConcurrentHashMap()

  /**
   * 门。停用的也在里面。
   */
  val doors: MutableMap<Int, DoorRuntime> = ConcurrentHashMap()

  /**
   * 所有电梯。停用的也在里面。by lift id
   */
  var lifts: MutableMap<Int, LiftRuntime> = ConcurrentHashMap()

  /**
   * 所有未执行完的运单 by order id
   */
  val orders: MutableMap<String, OrderRuntime> = ConcurrentHashMap()

  /**
   * 派单算法；尽量重用！
   */
  @Volatile
  var matchingAlgorithm: MatchingAlgorithm = DispatchOrderService.updateMatchingAlgorithm(this)

  /**
   * 派单专用线程
   */
  val dispatchExecutor: ExecutorService = Executors.newSingleThreadExecutor()

  /**
   * 派单性能跟踪
   */
  @Volatile
  var dispatchProfile: DispatchProfile = DispatchProfile()

  /**
   * 临时光通讯版本
   */
  val marsService = MarsService(this)

  /**
   * 交管
   */
  @Volatile
  var trafficService: TrafficService = DummyTrafficService(this)

  /**
   * 交管高级配置
   */
  private val devConfigs: MutableMap<String, String> = ConcurrentHashMap()

  val opRecorder = OperationRecordService(this)

  // 此场景下处理订单的锁
  private val orderLock = ReentrantLock(true)

  init {
    dispatchExecutor.submit {
      Thread.currentThread().name = "FtDp-e-$no"
    }
  }

  /**
   * 场景下处理订单的操作放在这个锁里。
   * 不能有耗时操作。
   * 目前数据库操作不算耗时操作。
   */
  fun <T : Any?> withOrderLock(block: () -> T): T = orderLock.withLock(block)

  /**
   * 获取场景全部信息
   */
  fun getSchema(): SceneSchema = SceneSchema(
    sceneId,
    basic,
    config,
    SceneStructure(
      robotGroups.values.toList(),
      emptyList(),
      robots.values.toList().map { it.config }.sortedBy { it.robotName },
      areas,
      containerTypes.values.toList(),
    ),
  )

  /**
   * 列出一个场景下的所有机器人组。返回副本。
   */
  fun listRobotGroups(): List<RobotGroup> = robotGroups.values.toList()

  /**
   * 获取场景下的一个机器人组
   */
  fun mustGetRobotGroupById(groupId: Int): RobotGroup =
    robotGroups[groupId] ?: throw BzError("errNoRobotGroupId", groupId)

  /**
   * 获取场景下的一个容器类型
   */
  fun mustGetContainerTypeById(containerTypeId: Int): SceneContainerType =
    containerTypes[containerTypeId] ?: throw BzError("errNoContainerTypeId", containerTypeId)

  /**
   * 列出场景下所有容器类型。返回副本。
   */
  fun listContainerTypes(): List<SceneContainerType> = containerTypes.values.toList()

  /**
   * 列出场景下所有机器人，包括停用的。返回副本。
   */
  fun listRobots(): List<RobotRuntime> = robots.values.toList()

  /**
   * 根据机器人名称获取机器人运行时
   */
  fun mustGetRobot(robotName: String): RobotRuntime = robots[robotName] ?: throw BzError("errNoRobot", robotName)

  /**
   * 根据运单号获取运单
   */
  fun mustGetOrderById(orderId: String): OrderRuntime = orders[orderId] ?: throw BzError("errMrNoOrder", orderId)

  /**
   * 返回副本
   */
  fun listOrders(): List<OrderRuntime> = orders.values.toList()

  /**
   * 检查场景有运单，报错
   */
  fun throwIfSceneWithOrders() {
    val anyGoing = orders.values.any { !it.order.status.finalStatus }
    if (anyGoing) throw BzError("errSceneNotIdle", "$sceneId / ${basic.name}")
  }

  /**
   * 场景启用、初始化完成、系统未停用 - 常用放一起
   */
  fun isEnabledInitializedNotEmc() = !SysEmc.isSysEmc() && !basic.disabled && status == SceneStatus.Initialized

  /**
   * 更新交管高级配置 v1=1&v2=2&v3=3
   */
  fun updateDevConfigs(configStr: String?) {
    val parts = StringHelper.splitTrim(configStr, "&")
    devConfigs.clear()
    for (part in parts) {
      val kv = StringHelper.splitTrim(part, "=")
      if (kv.size != 2) continue
      devConfigs[kv[0]] = kv[1]
    }
  }

  /**
   * 读取交管高级配置项：字符串类型
   */
  fun getDevConfig(key: String, defaultValue: String? = null): String? {
    val v = devConfigs[key]
    if (!v.isNullOrBlank()) return v
    return defaultValue
  }

  /**
   * 读取交管高级配置项：布尔类型
   */
  fun getDevConfigAsBool(key: String): Boolean? {
    val v = devConfigs[key]
    return BoolHelper.anyToBool(v)
  }

  /**
   * 读取交管高级配置项：Int 类型
   */

  fun getDevConfigAsInt(key: String, defaultValue: Int? = null): Int? {
    val v = devConfigs[key]
    return NumHelper.anyToInt(v) ?: defaultValue
  }

  /**
   * 读取交管高级配置项：Long 类型
   */

  fun getDevConfigAsLong(key: String, defaultValue: Long? = null): Long? {
    val v = devConfigs[key]
    return NumHelper.anyToLong(v) ?: defaultValue
  }

  /**
   * 读取交管高级配置项：Double 类型
   */
  fun getDevConfigAsDouble(key: String, defaultValue: Double? = null): Double? {
    val v = devConfigs[key]
    return NumHelper.anyToDouble(v) ?: defaultValue
  }

  override fun toString(): String = "${basic.name}/${sceneId.shortId()}"

  companion object {
    var noCounter = 0
  }
}