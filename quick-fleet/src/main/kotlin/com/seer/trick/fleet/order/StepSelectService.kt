package com.seer.trick.fleet.order

import com.seer.trick.BzError
import com.seer.trick.I18N
import com.seer.trick.base.LifetimeStatus
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.base.concurrent.PollingJobManager
import com.seer.trick.fleet.FleetLogger
import com.seer.trick.fleet.diagnosis.RobotOrderPrediction
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.service.MapService
import com.seer.trick.fleet.service.MapService.NOT_ACHIEVABLE_COST
import com.seer.trick.fleet.service.RobotBinService
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.fleet.service.RobotService
import com.seer.trick.fleet.service.RobotService.persistRobotRuntime
import org.slf4j.LoggerFactory
import java.util.*

/**
 * 决策机器人执行哪个步骤。
 * 主要是针对多负载机器人：下一个步骤该是当前运单的下一步，还是换一个运单执行。
 */
object StepSelectService {

  private val logger = LoggerFactory.getLogger(javaClass)

  fun initRobot(rr: RobotRuntime) {
    PollingJobManager.submit(
      threadName = getThreadName(rr),
      remark = "Select robot step: ${rr.robotName}",
      interval = { 500L },
      logger = logger,
      workerMaxTime = 2000L,
      stopCondition = { rr.sr.status == SceneStatus.Disposed || rr.ltStatus == LifetimeStatus.Disposed },
      exceptionContinue = true,
      tags = setOf(rr.sr.tag, rr.tag),
      worker = { tryToSelect(rr) },
    )
  }

  fun getThreadName(rr: RobotRuntime): String = "StepSelect-${rr.sr.no}-${rr.robotName}"

  /**
   * 在专门的线程执行
   */
  private fun tryToSelect(rr: RobotRuntime) {
    if (!RobotService.isRobotOkToAcceptOrder(rr)) return

    // 当前正在执行且不可被打断
    if (StepExecuteService.isExecutingAndWithdrawStepNotAllowed(rr)) return

    // 机器人当前位置
    val fromPoint = MapService.findBestStartPointNameForPlan(rr) ?: return

    if (RobotOrderPrediction.checkRobotExecutingStep(rr) != null) return

    // 如果支持 N+1 且货叉已经有货了，只查询当前运单的放货步骤，其他不能执行
    val bin999 = RobotBinService.findFilledBin999(rr)
    val orders = if (bin999 != null) {
      // 货叉有货就先执行货叉上的运单步骤
      val order999 = rr.orders.values.find { it.order.id == bin999.orderId }
      if (order999 != null) {
        // TODO 要加非业务单吗
        rr.orders.values.filter { it.order.kind != OrderKind.Business } + order999
      } else {
        // TODO 到这的话，运单被删除了？
        rr.orders.values
      }
    } else {
      rr.orders.values
    }

    if (orders.isEmpty()) return

    var best: SelectedOrderStep? = null
    var nextStep: TransportStep?

    for (or in orders) {
      if (RobotOrderPrediction.checkOrderExecutableStep(or) != null) continue

      // 限制必须接某个单的
      if (!rr.selectSameOrderId.isNullOrBlank() && or.id != rr.selectSameOrderId) continue

      nextStep = findNextStep(or) ?: continue

      val cost = try {
        val result = estimateStepCost(rr, fromPoint, nextStep)
        if (result == NOT_ACHIEVABLE_COST && !or.order.fault) {
          val tt = rr.pendingTrafficTask
          if (tt != null && !tt.status.finalStatus) {
            // 交管执行过程中，机器人可能不在位置上，不要报错
          } else {
            // 如果获取不到成本，让运单失败
            val reason = I18N.lo(
              "FleetDiagnosis_OrderStepCannotAchievable",
              listOf(fromPoint, nextStep.location),
            )
            OrderFaultService.markOrderAndRobotFault(rr.sr, or, null, reason)
            or.executionReject = RejectReason("estimateStepCostFail", listOf("Failed to calc cost"))
          }
          continue // 不通
        } else {
          result
        }
      } catch (e: BzError) {
        // 如果点位不存在，让运单失败
        if (!or.order.fault) {
          val reason =
            I18N.lo(
              e.code,
              listOf(if (e.args.isNotEmpty())e.args[0] else null),
            )
          OrderFaultService.markOrderAndRobotFault(rr.sr, or, null, reason)
        }
        or.executionReject = RejectReason("estimateStepCostFail", listOf("Failed to calc cost"))
        continue // 不通
      }

      if (best == null ||
        DispatchOrderService.compareOrders(
          rr.sr,
          or.order.priority,
          cost,
          or.order.createdOn,
          best.or.order.priority,
          best.cost,
          best.or.order.createdOn,
        ) < 0
      ) {
        best = SelectedOrderStep(or, nextStep.stepIndex, cost)
      }
    }

    // 没选出
    if (best == null) return

    rr.sr.withOrderLock {
      val or = best.or

      // 在冷静期内
      if (or.isCooling() || rr.isCooling()) return@withOrderLock

      val sec = rr.executingStep

      if (sec != null) {
        // 分派未改变
        if (sec.or.id == best.or.id && sec.stepId == best.getStep().id) return@withOrderLock
        val secCost = try {
          val result = estimateStepCost(rr, fromPoint, sec.getStep())
          if (result == NOT_ACHIEVABLE_COST) return@withOrderLock else result
        } catch (_: BzError) {
          return@withOrderLock
        }
        // 成本比目前的高，不换
        if (DispatchOrderService.compareOrders(
            rr.sr,
            sec.or.order.priority,
            secCost,
            sec.or.order.createdOn,
            best.or.order.priority,
            best.cost,
            best.or.order.createdOn,
          ) < 0
        ) {
          return@withOrderLock
        }

        // 成本差异不大
        if (sec.or.order.priority == best.or.order.priority &&
          secCost - best.cost <= rr.sr.config.decideWithdrawnMinCost()
        ) {
          return@withOrderLock
        }
      }

      // 实际做出选择
      doSelectNextStep(rr, best)
    }
  }

  /**
   * 寻找这个运单是否适合做最优的下一步
   */
  fun findNextStep(or: OrderRuntime): TransportStep? {
    if (or.steps.isEmpty()) return null

    // 正在执行不能被打断
    if (!or.withdrawStepAllowed) return null

    val order = or.order
    var nextIndex = order.doneStepIndex + 1
    // 跳过 Skipped 的步骤
    while (nextIndex < order.stepNum) {
      val step = or.steps[nextIndex]
      if (step.status == StepStatus.Skipped) {
        ++nextIndex
      } else {
        break
      }
    }

    if (nextIndex >= order.stepNum) return null
    val step = or.steps[nextIndex]
    // 再次检查一下状态
    if (step.status == StepStatus.Executable || step.status == StepStatus.Executing) return step
    return null
  }

  fun estimateStepCost(rr: RobotRuntime, fromPoint: String, step: TransportStep): Double {
    // TODO 即使同一个站点，料箱机器人，能按层取
    val toLocation = step.location
    // TODO 如果没有目标位置
    if (toLocation.isBlank()) throw BzError("errNoBinOrLocation")

    return MapService.getShortestPathCostOfCrossAreas(rr, fromPoint, toLocation)
  }

  /**
   * 修改 rr.executingStep
   * 无阻塞性代码。在运单锁里执行。
   */
  private fun doSelectNextStep(rr: RobotRuntime, selected: SelectedOrderStep) = rr.sr.withOrderLock {
    val or = selected.or
    // TODO 状态检查跟 checkOrderExecutableStep 合并

    // 已经完成的步骤不再参与选择
    val stepStatus = selected.getStep().status
    if (stepStatus == StepStatus.Done || stepStatus == StepStatus.Skipped) {
      logger.warn("Next step has been done or cancelled, ignore it. Step=$selected, status=$stepStatus")
      return@withOrderLock
    }

    // 二次检查任务状态
    val coe = RobotOrderPrediction.checkOrderExecutableStep(or)
    if (coe != null) {
      logger.info("Failed to select next step, order ${or.id} is not executable: $coe")
      return@withOrderLock
    }

    // 运单的分派已改变
    if (or.order.actualRobotName != rr.robotName) {
      logger.warn("The robot of order ${or.id} changed, from ${or.order.actualRobotName} to $rr.")
      return@withOrderLock
    }

    // 机器人状态综合检查
    if (!RobotService.isRobotOkToAcceptOrder(rr)) return@withOrderLock

    // 机器人状态检查
    val cre = RobotOrderPrediction.checkRobotExecutingStep(rr)
    if (cre != null) {
      logger.info("Failed to select next step, robot $rr is not executable: $cre")
      return@withOrderLock
    }

    // 多负载机器人，选择更适合的库位
    RobotBinService.findBetterBinBeforeStepExecuting(rr, selected)

    // 步骤执行前检查库位情况
    AlarmService.removeItem("SceneRobotLoadFail-${rr.robotName}")
    AlarmService.removeItem("SceneRobotUnloadFail-${rr.robotName}")
    if (!RobotBinService.checkBinBeforeStepExecuting(rr, selected)) return@withOrderLock

    val sec = rr.executingStep
    if (sec != null) {
      withdrawStep(sec, "Withdraw step by step select service")
    }

    startExecutingStep(rr, selected)
  }

  /**
   * 撤回运单步骤，准备执行其他运单步骤。
   */
  fun withdrawStep(sec: StepExecuteContext, reason: String) {
    val rr = sec.rr
    val or = sec.or
    logger.info("Withdraw step=$sec. robot=$rr. reason=$reason")

    StepExecuteService.cancelExecuting(sec, reason)

    val newOrder = or.order.copy(status = OrderStatus.Pending)
    OrderService.updateAndPersistOrder(or, newOrder, reason)
  }

  /**
   * 当步骤真的可以执行，标记运单、步骤 Executing，机器人 Moving
   * 前面都检查过了，不再检查了
   */
  private fun startExecutingStep(rr: RobotRuntime, selected: SelectedOrderStep) {
    val or = selected.or
    val stepIndex = selected.stepIndex

    val sec = StepExecuteContext(rr, or, stepIndex)

    // 待执行的，变为正在执行的
    rr.executingStep = sec

    // 修改运单数据
    val newOrder = or.order.copy(status = OrderStatus.Executing, currentStepIndex = stepIndex)
    OrderService.updateAndPersistOrder(or, newOrder, "Mark order executing when step starts executing")

    // 修改步骤数据
    val newStep = selected.getStep().copy(status = StepStatus.Executing, startOn = Date())
    OrderService.updateAndPersistStep(or, newStep, "Mark step starts executing")

    // 修改机器人数据
    rr.idleFrom = null
    persistRobotRuntime(rr)

    // 如果某个步骤禁止了重分配，后续都禁止
    if (!selected.getStep().withdrawOrderAllowed) {
      or.withdrawOrderAllowed = false
    }

    FleetLogger.info(
      module = "ExecuteStep",
      subject = "StepStart",
      sr = rr.sr,
      robotName = rr.robotName,
      msg = mapOf("step" to selected),
    )

    StepExecuteService.executeStep(rr, sec)
  }
}

/**
 * 封装选择步骤的信息，主要是成本
 */
data class SelectedOrderStep(val or: OrderRuntime, val stepIndex: Int, val cost: Double) {

  fun getStep() = or.steps[stepIndex]

  override fun toString(): String = "Selected: ${or.order.id}:$stepIndex"
}