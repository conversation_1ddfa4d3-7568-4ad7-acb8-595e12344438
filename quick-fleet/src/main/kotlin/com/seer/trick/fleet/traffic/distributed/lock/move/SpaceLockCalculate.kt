package com.seer.trick.fleet.traffic.distributed.lock.move

import com.seer.trick.fleet.traffic.distributed.context.ContextManagerService
import com.seer.trick.fleet.traffic.distributed.context.domain.RobotContext
import com.seer.trick.fleet.traffic.distributed.helper.*
import com.seer.trick.fleet.traffic.distributed.lock.base.RobotContainerSpaceLock
import com.seer.trick.fleet.traffic.distributed.lock.base.StaticRobotSpaceLock
import com.seer.trick.fleet.traffic.distributed.lock.graph.Vector
import com.seer.trick.fleet.traffic.distributed.lock.model.SpaceLock
import com.seer.trick.fleet.traffic.distributed.map.*
import com.seer.trick.fleet.traffic.distributed.plan.model.PathAction
import com.seer.trick.fleet.traffic.distributed.plan.model.PathType
import org.slf4j.LoggerFactory

/**
 * 移动锁闭计算
 * */
object SpaceLockCalculate {

  private val logger = LoggerFactory.getLogger(javaClass)

  fun robotStaticSpaceLock(
    robotName: String,
    sceneId: String,
    groupName: String,
    heading: Int,
    point: Position?,
    mapName: String,
  ): SpaceLock? {
    if (point == null) {
      logger.error("robotCode or robotType is null")
      return null
    }
    return StaticRobotSpaceLock.buildStaticSpaceLock(robotName, sceneId, groupName, point, heading, mapName)
  }

  /**
   *  容器锁闭申请
   * */
  fun robotAndContainerStaticSpaceLock(
    robotName: String,
    sceneId: String,
    groupName: String,
    robotHeading: Int,
    point: Position,
    mapName: String,
    container: String?,
    containerHeading: Int = AngleHelper.ERROR_ANGLE,
  ): SpaceLock {
    val spaceLock =
      StaticRobotSpaceLock.buildStaticSpaceLock(robotName, sceneId, groupName, point, robotHeading, mapName)
    if (container != null) {
      val containerSpaceLock = RobotContainerSpaceLock.buildContainerSpaceLock(
        robotName = robotName,
        sceneId = sceneId,
        groupName = groupName,
        point = point,
        containerHeading = containerHeading,
        mapName = mapName,
        container = container,
      )
      spaceLock.union(containerSpaceLock)
    }
    return spaceLock
  }

  fun moveSpaceLock(pathAction: PathAction): SpaceLock {
    val context = pathAction.robotName.let { ContextManagerService.queryRobotContext(it) }
    // 判断类型
    return when (pathAction.type) {
      PathType.ARC -> {
        robotArcSpaceLock(pathAction, context.sceneId)
      }

      PathType.CURVE -> {
        robotCurveSpaceLock(pathAction, context.sceneId)
      }

      PathType.ROTATE -> {
        robotRotateSpaceLock(
          robotName = pathAction.robotName,
          sceneId = context.sceneId,
          groupName = context.groupName,
          point = pathAction.target,
          mapName = pathAction.mapName,
          container = pathAction.containerName,
          containerHeading = pathAction.containerOutHeading,
        )
      }

      PathType.TURNING -> {
        containerRotateSpaceLock(
          robotName = pathAction.robotName,
          sceneId = context.sceneId,
          groupName = context.groupName,
          point = pathAction.target,
          robotNeedRotate = !AngleHelper.sameAngleInFiveDegree(pathAction.robotInHeading, pathAction.robotOutHeading),
          robotHeading = pathAction.robotOutHeading,
          mapName = pathAction.mapName,
          container = pathAction.containerName!!,
        )
      }

      PathType.SECTOR -> {
        robotSectorSpaceLock(pathAction, context.sceneId)
      }

      PathType.STRAIGHT -> {
        robotMoveSpaceLock(
          robotName = pathAction.robotName,
          sceneId = context.sceneId,
          groupName = context.groupName,
          start = pathAction.start,
          target = pathAction.target,
          robotHeading = pathAction.robotOutHeading,
          mapName = pathAction.mapName,
          container = pathAction.containerName,
          containerHeading = pathAction.containerOutHeading,
        )
      }

      PathType.STOP -> {
        stopSpaceLock(pathAction, context)
      }
    }
  }

  private fun stopSpaceLock(pathAction: PathAction, context: RobotContext): SpaceLock {
    if (pathAction.start.pointName != pathAction.target.pointName) {
      val line =
        MapService.findLineByName(context.sceneId, pathAction.mapName, context.groupName, pathAction.lineName)
      var opposeAngle = AngleHelper.opposeAngle(pathAction.robotInHeading, line.enterDir)
      if (pathAction.start.distance(line.start) > 0.2 &&
        (line.type == LineType.THREE_BEZIER || line.type == LineType.LUMINANCE_CURVE)
      ) {
        val traces = line.tracePoses
        for (i in 0 until traces.size - 1) {
          if (traces[i].getDistance(pathAction.start.x, pathAction.start.y) < 0.2) {
            opposeAngle = AngleHelper.opposeAngle(
              pathAction.robotInHeading,
              AngleHelper.getAngle(Vector(traces[i].x, traces[i].y), Vector(traces[i + 1].x, traces[i + 1].y)),
            )
          }
        }
      }
      val robotOutHeading = if (opposeAngle) {
        AngleHelper.processAngle(
          line.outDir + AngleHelper.DOWN_ANGLE,
        )
      } else {
        line.outDir
      }
      val spaceLock = if (line.type == LineType.LUMINANCE_CURVE) {
        robotCurveSpaceLock(pathAction.copy(type = PathType.CURVE, robotOutHeading = robotOutHeading), context.sceneId)
      } else if (line.type == LineType.THREE_BEZIER) {
        robotArcSpaceLock(pathAction.copy(type = PathType.ARC, robotOutHeading = robotOutHeading), context.sceneId)
      } else {
        robotMoveSpaceLock(
          robotName = pathAction.robotName,
          sceneId = context.sceneId,
          groupName = context.groupName,
          start = pathAction.start,
          target = pathAction.target,
          robotHeading = pathAction.robotInHeading,
          mapName = pathAction.mapName,
          container = pathAction.containerName,
          containerHeading = pathAction.containerInHeading,
        )
      }
      if (!AngleHelper.sameAngleInFiveDegree(pathAction.robotOutHeading, robotOutHeading)) {
        val rotateSpaceLock = robotRotateSpaceLock(
          robotName = pathAction.robotName,
          sceneId = context.sceneId,
          groupName = context.groupName,
          point = pathAction.target,
          mapName = pathAction.mapName,
          container = pathAction.containerName,
          containerHeading = pathAction.containerOutHeading,
        )
        spaceLock.union(rotateSpaceLock)
      }
      return spaceLock
    }
    return if (!AngleHelper.sameAngleInFiveDegree(pathAction.robotInHeading, pathAction.robotOutHeading)) {
      robotRotateSpaceLock(
        robotName = pathAction.robotName,
        sceneId = context.sceneId,
        groupName = context.groupName,
        point = pathAction.target,
        mapName = pathAction.mapName,
        container = pathAction.containerName,
        containerHeading = pathAction.containerOutHeading,
      )
    } else {
      robotAndContainerStaticSpaceLock(
        robotName = pathAction.robotName,
        sceneId = context.sceneId,
        groupName = context.groupName,
        robotHeading = pathAction.robotOutHeading,
        point = pathAction.target,
        mapName = pathAction.mapName,
        container = pathAction.containerName,
        containerHeading = pathAction.containerOutHeading,
      )
    }
  }

  private fun containerRotateSpaceLock(
    robotName: String,
    sceneId: String,
    groupName: String,
    point: Point,
    robotNeedRotate: Boolean,
    robotHeading: Int,
    mapName: String,
    container: String,
  ): SpaceLock {
    val containerSpaceLock = RobotContainerSpaceLock.buildContainerRotateSpaceLock(
      robotName = robotName,
      sceneId = sceneId,
      groupName = groupName,
      point = point,
      mapName = mapName,
      container = container,
    )
    if (robotNeedRotate) {
      val robotRotateSpaceLock =
        StaticRobotSpaceLock.buildRotateSpaceLock(robotName, sceneId, groupName, point, mapName)
      containerSpaceLock.union(robotRotateSpaceLock)
    } else {
      val robotSpaceLock =
        StaticRobotSpaceLock.buildStaticSpaceLock(robotName, sceneId, groupName, point, robotHeading, mapName)
      containerSpaceLock.union(robotSpaceLock)
    }

    return containerSpaceLock
  }

  /**
   * 旋转锁闭计算
   * */
  fun robotRotateSpaceLock(
    robotName: String,
    sceneId: String,
    groupName: String,
    point: Position,
    mapName: String,
    container: String? = null,
    containerHeading: Int,
  ): SpaceLock {
    val spaceLock = StaticRobotSpaceLock.buildRotateSpaceLock(robotName, sceneId, groupName, point, mapName)
    if (container != null && containerHeading != AngleHelper.ERROR_ANGLE) {
      val containerSpaceLock = RobotContainerSpaceLock.buildContainerSpaceLock(
        robotName = robotName,
        sceneId = sceneId,
        groupName = groupName,
        point = point,
        containerHeading = containerHeading,
        mapName = mapName,
        container = container,
      )
      spaceLock.union(containerSpaceLock)
    }
    return spaceLock
  }

  /**
   * 移动锁闭计算
   * */
  private fun robotMoveSpaceLock(
    robotName: String,
    sceneId: String,
    groupName: String,
    start: Position,
    target: Position,
    robotHeading: Int,
    mapName: String,
    container: String?,
    containerHeading: Int = AngleHelper.ERROR_ANGLE,
  ): SpaceLock {
    val startSpaceLock = StaticRobotSpaceLock
      .buildStaticSpaceLock(robotName, sceneId, groupName, start, robotHeading, mapName)
    val targetSpaceLock = StaticRobotSpaceLock
      .buildStaticSpaceLock(robotName, sceneId, groupName, target, robotHeading, mapName)
    val spaceLock = LockHelper.moveSpaceLock(startSpaceLock, targetSpaceLock)
    if (container != null) {
      val startContainerSpaceLock = RobotContainerSpaceLock.buildContainerSpaceLock(
        robotName = robotName,
        sceneId = sceneId,
        groupName = groupName,
        point = start,
        containerHeading = containerHeading,
        mapName = mapName,
        container = container,
      )
      val targetContainerSpaceLock = RobotContainerSpaceLock.buildContainerSpaceLock(
        robotName = robotName,
        sceneId = sceneId,
        groupName = groupName,
        point = target,
        containerHeading = containerHeading,
        mapName = mapName,
        container = container,
      )
      val containerSpaceLock = LockHelper.moveSpaceLock(startContainerSpaceLock, targetContainerSpaceLock)
      spaceLock.union(containerSpaceLock)
    }
    return spaceLock
  }

  /**
   * 弧线锁闭计算
   * */
  private fun robotArcSpaceLock(pathAction: PathAction, sceneId: String): SpaceLock {
    val robot = pathAction.robotName
    val groupName = pathAction.groupName
    val start = pathAction.start
    val target = pathAction.target
    val robotInHeading = pathAction.robotInHeading
    val robotOutHeading = pathAction.robotOutHeading
    val mapName = pathAction.mapName
    val lineName = pathAction.lineName
    val line = MapService.findLineByName(sceneId, mapName, groupName, lineName)
    val container = pathAction.containerName
    val containerInHeading = pathAction.containerInHeading
    val containerOutHeading = pathAction.containerOutHeading
    val diff = robotInHeading - containerInHeading

    val spaceLock = robotAndContainerStaticSpaceLock(
      robotName = robot,
      sceneId = sceneId,
      groupName = groupName,
      robotHeading = robotInHeading,
      point = start,
      mapName = mapName,
      container = container,
      containerHeading = containerInHeading,
    )
    val length =
      BezierCurveHelper.calculateLength(100, line.start, line.controls[0], line.controls[1], line.end, lineName)
    val d = (length / 0.2 + 1).toInt()
    var sites = BezierCurveHelper.sampleUniformly(d, line.start, line.controls[0], line.controls[1], line.end, lineName)
    var opposeAngle = AngleHelper.opposeAngle(robotInHeading, line.enterDir)
    if (start.distance(line.start) > 0.2) {
      var findStart = false
      for (i in 0 until sites.size - 1) {
        if (sites[i].distance(start) < 0.2) {
          opposeAngle = AngleHelper.opposeAngle(robotInHeading, AngleHelper.getAngle(sites[i], sites[i + 1]))
          sites = sites.subList(i, sites.size)
          findStart = true
          break
        }
      }
      if (!findStart) {
        logger.error("start point not found in bezier curve")
        if (line.start.distance(start) > line.end.distance(start)) {
          opposeAngle =
            AngleHelper.opposeAngle(robotInHeading, AngleHelper.getAngle(sites[sites.size - 2], sites[sites.size - 1]))
          sites = sites.subList(sites.size - 2, sites.size)
        }
      }
    }
    val spaceLocks: MutableList<SpaceLock> = mutableListOf()
    if (sites.size > 2) {
      for (i in 0 until sites.size - 1) {
        val s = sites[i]
        val t = sites[i + 1]
        val heading = if (opposeAngle) {
          AngleHelper.processAngle(AngleHelper.getAngle(s, t) + AngleHelper.DOWN_ANGLE)
        } else {
          AngleHelper.getAngle(s, t)
        }
        val containerHeading = if (container != null) {
          AngleHelper.processAngle(heading - diff)
        } else {
          AngleHelper.ERROR_ANGLE
        }
        val moveSpaceLock = robotMoveSpaceLock(
          robotName = robot,
          sceneId = sceneId,
          groupName = groupName,
          start = s,
          target = t,
          robotHeading = heading,
          mapName = mapName,
          container = container,
          containerHeading = containerHeading,
        )
        spaceLocks.add(moveSpaceLock)
      }
    }

    val targetSpaceLock = robotAndContainerStaticSpaceLock(
      robotName = robot,
      sceneId = sceneId,
      groupName = groupName,
      robotHeading = robotOutHeading,
      point = target,
      mapName = mapName,
      container = container,
      containerHeading = containerOutHeading,
    )
    for (sl in spaceLocks) {
      spaceLock.union(sl)
    }
    spaceLock.union(targetSpaceLock)
    return spaceLock
  }

  /**
   *  高阶曲线锁闭信息构建
   * */
  private fun robotCurveSpaceLock(pathAction: PathAction, sceneId: String): SpaceLock {
    val groupName = pathAction.groupName
    val target = pathAction.target
    val robotInHeading = pathAction.robotInHeading
    val robotOutHeading = pathAction.robotOutHeading
    val mapName = pathAction.mapName
    val lineName = pathAction.lineName
    val robot = pathAction.robotName
    val line = MapService.findLineByName(sceneId, mapName, groupName, lineName)
    var traces = line.tracePoses
    if (traces.isEmpty()) {
      throw RuntimeException("tracePoses is empty")
    }
    val start = pathAction.start
    val container = pathAction.containerName
    val containerInHeading = pathAction.containerInHeading
    val containerOutHeading = pathAction.containerOutHeading
    val diff = robotInHeading - containerInHeading

    val spaceLock =
      robotAndContainerStaticSpaceLock(
        robotName = robot,
        sceneId = sceneId,
        groupName = groupName,
        robotHeading = robotInHeading,
        point = pathAction.start,
        mapName = mapName,
        container = container,
        containerHeading = containerInHeading,
      )
    var opposeAngle = AngleHelper.opposeAngle(robotInHeading, line.enterDir)
    if (pathAction.start.distance(line.start) > 0.2) {
      var findStart = false
      for (i in 0 until traces.size - 1) {
        if (traces[i].getDistance(start.x, start.y) < 0.2) {
          opposeAngle = AngleHelper.opposeAngle(
            robotInHeading,
            AngleHelper.getAngle(Vector(traces[i].x, traces[i].y), Vector(traces[i + 1].x, traces[i + 1].y)),
          )
          traces = traces.subList(i, traces.size)
          findStart = true
          break
        }
      }
      if (!findStart) {
        logger.error("start point not found in bezier curve")
        if (line.start.distance(start) > line.end.distance(start)) {
          opposeAngle =
            AngleHelper.opposeAngle(
              robotInHeading,
              AngleHelper.getAngle(
                Vector(traces[traces.size - 2].x, traces[traces.size - 2].y),
                Vector(traces[traces.size - 1].x, traces[traces.size - 1].y),
              ),
            )
          traces = traces.subList(traces.size - 2, traces.size)
        }
      }
    }
    val spaceLocks: MutableList<SpaceLock> = mutableListOf()
    var preSite = traces[0]
    for (i in 1 until traces.size - 1) {
      val heading = if (opposeAngle) {
        AngleHelper.processAngle(
          preSite.theta + AngleHelper.vectorAngle(preSite.theta, traces[i].theta) / 2 +
            AngleHelper.DOWN_ANGLE,
        )
      } else {
        AngleHelper.processAngle(preSite.theta + AngleHelper.vectorAngle(preSite.theta, traces[i].theta) / 2)
      }
      val containerHeading = if (container != null) {
        AngleHelper.processAngle(heading - diff)
      } else {
        AngleHelper.ERROR_ANGLE
      }
      val moveSpaceLock = robotMoveSpaceLock(
        robotName = robot,
        sceneId = sceneId,
        groupName = groupName,
        start = Position(preSite.x, preSite.y, lineName, PosType.LINE),
        target = Position(traces[i].x, traces[i].y, lineName, PosType.LINE),
        robotHeading = heading,
        mapName = mapName,
        container = container,
        containerHeading = containerHeading,
      )
      spaceLocks.add(moveSpaceLock)
      preSite = traces[i]
    }
    val targetSpaceLock = robotAndContainerStaticSpaceLock(
      robotName = robot,
      sceneId = sceneId,
      groupName = groupName,
      robotHeading = robotOutHeading,
      point = target,
      mapName = mapName,
      container = container,
      containerHeading = containerOutHeading,
    )
    for (sl in spaceLocks) {
      spaceLock.union(sl)
    }
    spaceLock.union(targetSpaceLock)
    return spaceLock
  }

  private fun robotSectorSpaceLock(pathAction: PathAction, sceneId: String): SpaceLock {
    val spaceLock = robotAndContainerStaticSpaceLock(
      robotName = pathAction.robotName,
      sceneId = sceneId,
      groupName = pathAction.groupName,
      robotHeading = pathAction.robotInHeading,
      point = pathAction.start,
      mapName = pathAction.mapName,
      container = pathAction.containerName,
      containerHeading = pathAction.containerInHeading,
    )
    val spaceLock1 = robotAndContainerStaticSpaceLock(
      robotName = pathAction.robotName,
      sceneId = sceneId,
      groupName = pathAction.groupName,
      robotHeading = pathAction.robotOutHeading,
      point = pathAction.target,
      mapName = pathAction.mapName,
      container = pathAction.containerName,
      containerHeading = pathAction.containerOutHeading,
    )
    spaceLock.union(spaceLock1)
    return spaceLock
  }
}