package com.seer.trick.fleet.order

import RobotOrderDiagnostics
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.ComplexQuery
import com.seer.trick.Cq
import com.seer.trick.I18N.lo
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.SysEmc
import com.seer.trick.base.alarm.AlarmAction
import com.seer.trick.base.alarm.AlarmItem
import com.seer.trick.base.alarm.AlarmLevel
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.base.concurrent.PollingJobManager
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.id.IdGenManager
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.fleet.FleetLogger
import com.seer.trick.fleet.diagnosis.RobotOrderPrediction
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.service.*
import com.seer.trick.fleet.traffic.TrafficService
import com.seer.trick.fleet.traffic.distributed.service.DistributedInteractionService
import com.seer.trick.fleet.traffic.venus.VenusTrafficService
import com.seer.trick.helper.JsonHelper
import org.slf4j.LoggerFactory
import java.util.*

/**
 * 运单服务。运单相关服务的总服务。
 * 会修改 RobotOrderRuntime。
 */
object OrderService {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 初始化运单服务。目前应该不怎么耗时，未来可能较为耗时。
   * 只能被调用一次。被 SceneService.initScene
   */
  fun init(sr: SceneRuntime) {
    logger.info("Start | Init order service of scene $sr")

    markUnfinishedAutoOrdersCancelled(sr.sceneId)

    restoreBzOrders(sr)

    // 启动定时派单、选择步骤、下发交管任务
    PollingJobManager.submit(
      threadName = getDpThreadName(sr),
      remark = "Dispatching orders and steps and traffic tasks for scene " + sr.basic.name,
      interval = { 1000L },
      logger = logger,
      workerMaxTime = 5 * 1000,
      stopCondition = { sr.status == SceneStatus.Disposed },
      exceptionContinue = true,
      tags = setOf(sr.tag),
      worker = { dispatchOrdersAndStepsAndTrafficTasks(sr) },
    )

    initTraffic(sr, sr.config.trafficMethod)

    PollingJobManager.submit(
      threadName = getHkThreadName(sr),
      remark = "House keeping for scene " + sr.basic.name,
      interval = { 1000L },
      logger = logger,
      workerMaxTime = 5 * 1000,
      stopCondition = { sr.status == SceneStatus.Disposed },
      exceptionContinue = true,
      tags = setOf(sr.tag),
      worker = {
        houseKeeping(sr)
      },
    )

    logger.info("Done | Init order service of scene $sr")
  }

  /**
   *  获取派单的 threadName
   */
  fun getDpThreadName(sr: SceneRuntime): String = "Ft-Dp-p-${sr.no}"

  /**
   *  获取告警维护的 threadName
   */
  fun getHkThreadName(sr: SceneRuntime): String = "Ft-Hk-${sr.no}"

  /**
   * 从数据库中恢复未执行完的业务运单。
   */
  private fun restoreBzOrders(sr: SceneRuntime) {
    val bzOrList = listUnfinishedBzOrderWithSteps(sr.sceneId)

    logger.info("Restoring ${bzOrList.size} orders for scene ${sr.sceneId}")

    for (or in bzOrList) {
      sr.orders[or.id] = or

      val order = or.order

      // 非故障则启动后自动恢复执行，否则当前步骤则进入失败重试 TODO 为什么要这么做？
      // TODO 移到 restoreBzOrders 之前，专门更新这类运单，这里不修改
      if (order.status == OrderStatus.Executing && !order.fault) {
        or.updateOrder(order.copy(status = OrderStatus.Pending))
        // TODO 要不要持久化
      }
    }
  }

  /**
   * 标记数据库中未完成的自动单全取消
   */
  private fun markUnfinishedAutoOrdersCancelled(sceneId: String) {
    var q = unfinishedOrderCq(sceneId)
    q = Cq.and(q, Cq.ne("kind", OrderKind.Business.name))

    EntityRwService.updateMany(
      "TransportOrder",
      q,
      mutableMapOf("status" to OrderStatus.Cancelled.name, "fault" to false, "faultReason" to ""),
    )
  }

  /**
   * 加载一个场景下未完成的运单及步骤，仅业务单
   */
  private fun listUnfinishedBzOrderWithSteps(sceneId: String): List<OrderRuntime> {
    var q = unfinishedOrderCq(sceneId)
    q = Cq.and(q, Cq.eq("kind", OrderKind.Business.name))

    val orderEvList = EntityRwService.findMany("TransportOrder", q)
    return orderEvList.map(::evToOrder).map { order ->
      val steps = EntityRwService.findMany(
        "TransportStep",
        Cq.eq("orderId", order.id),
        FindOptions(sort = listOf("stepIndex")),
      ).map(::evToStep)
      OrderRuntime(order, steps)
    }.filter {
      // TODO 优化更新运单代码
      val rejectReason = RobotOrderDiagnostics.checkOrderValidate(it, false).firstRejection
      // 校验不通过则直接取消运单
      if (rejectReason != null) {
        EntityRwService.updateOne(
          "TransportOrder",
          Cq.eq("id", it.id),
          mutableMapOf("status" to OrderStatus.Cancelled.name, "fault" to false, "faultReason" to ""),
          null,
        )
      }
      rejectReason == null
    }
  }

  // 初始化交管实现
  private fun initTraffic(sr: SceneRuntime, tm: TrafficMethod) {
    logger.info("Init traffic service of scene $sr: $tm")

    // TODO 是不是先初始化好再给 sr.trafficService 赋值

    sr.trafficService = when (tm) {
      TrafficMethod.Distributed -> DistributedInteractionService(sr)
      TrafficMethod.Venus -> VenusTrafficService(sr)
    }

    sr.trafficService.init()

    sr.trafficService.initTrafficSchema()
  }

  fun dispose(sr: SceneRuntime) {
    sr.trafficService.dispose()
  }

  /**
   * 更新 order 到 or 并持久化 order。
   * desc 不怕长，必须说明更新了什么，为什么更新。
   */
  fun updateAndPersistOrder(or: OrderRuntime, order: TransportOrder, desc: String) {
    logger.info("Update order ${or.id}: $desc")
    if (order.id.isBlank()) {
      throw IllegalArgumentException("Bad order")
    }
    or.updateOrder(order)
    persistUpdatedOrder(order)
  }

  /**
   * 循环派单、派步骤、派交管任务
   */
  private fun dispatchOrdersAndStepsAndTrafficTasks(sr: SceneRuntime) {
    val config = sr.config
    if (sr.status != SceneStatus.Initialized || config.trafficPlanPaused || SysEmc.isSysEmc()) return

    // TODO dispatchOrders 实际是异步的，最好等派完单再派步骤
    DispatchOrderService.dispatchOrders(sr)

    TrafficService.submitTaskToTraffic(sr)
  }

  /**
   * 更改交管实现
   * TODO 先让场景 initialized = false
   */
  fun changeTrafficService(sr: SceneRuntime, tm: TrafficMethod) = sr.withOrderLock {
    logger.info("Change traffic service of scene $sr! old: ${sr.trafficService.javaClass.simpleName}")
    val old = sr.trafficService
    old.dispose()

    initTraffic(sr, tm)
  }

  /**
   * 产生下一个运单号。目前所有场景的放一起。
   */
  fun generateOrderId(): String {
    val em = BaseCenter.mustGetEntityMeta("TransportOrder")
    return IdGenManager.generateId(em.idGen!!)
  }

  /**
   * 创建新运单。
   * 应该不用加任何锁。
   * 把传入的 OrderRuntime 直接给 sr.orders。
   */
  fun createOrders(sr: SceneRuntime, orList: List<OrderRuntime>) {
    if (SceneService.disabled(sr)) throw BzError("errSceneDisabled", sr.sceneId, sr.basic.name)

    for (or in orList) {
      for (step in or.steps) {
        checkStep(sr, step)
      }

      val order = or.order

      // 确保运单的 keyLocations 是非空的。统一在这里处理，否则 handler 和 falconBp 都得处理。
      var keyLocations = or.steps.map { step -> step.location }.filter { it.isNotBlank() }.distinct()
      if (keyLocations.isEmpty() && !order.keyLocations.isNullOrEmpty()) keyLocations = order.keyLocations
      if (keyLocations.isEmpty()) throw BzError("errEmptyKeyLocations")

      // 检查容器类型
      val typeName = order.containerTypeName
      if (!typeName.isNullOrBlank() && sr.listContainerTypes().find { c -> c.name == typeName } == null) {
        throw BzError("errNotExistContainerType", typeName)
      }

      // 取放货点位
      val loadLoc = or.steps.find { it.forLoad }?.location
      val unloadLoc = or.steps.find { it.forUnload }?.location

      or.updateOrder(
        order.copy(
          sceneName = sr.basic.name, // 根据 order 中的 sceneId，强制更新 order 的 sceneName。
          keyLocations = keyLocations,
          loadPoint = loadLoc,
          unloadPoint = unloadLoc,
        ),
      )
    }

    // 都没问题了再加入到运单列表
    for (or in orList) {
      sr.orders[or.id] = or
    }

    persistCreatedOrdersAndSteps(orList)

    FleetLogger.info(
      module = "Order",
      subject = "CreateOrders",
      sr = sr,
      robotName = null,
      msg = mapOf("orders" to orList.map { it.toAll() }),
    )

    // 立即请求一次分派
    DispatchOrderService.dispatchOrders(sr)
  }

  /**
   * 校验运单步骤的合理性。有问题抛异常。
   */
  private fun checkStep(sr: SceneRuntime, step: TransportStep) {
    // 暂不校验 location 是否有效。
    val location = step.location
    val stepNo = step.stepIndex + 1

    // 校验动作参数
    if (!step.rbkArgs.isNullOrEmpty()) {
      val rbkArgsMap: Map<String, Any?> = try {
        JsonHelper.mapper.readValue(step.rbkArgs, jacksonTypeRef())
      } catch (err: Exception) {
        throw BzError("errStepRbkArgsInvalid", stepNo, step.rbkArgs, err.message)
      }

      // 只能使用 operation 或者 binTask
      val operation = rbkArgsMap["operation"]?.toString()
      val binTask = rbkArgsMap["binTask"]?.toString()
      if (!operation.isNullOrEmpty() && !binTask.isNullOrEmpty()) {
        throw BzError("errStepTooManyOp", stepNo)
      }

      // 使用 binTask 时，location 的值必须是库位名称，并且目标库位上有同名的 binTask
      if (!binTask.isNullOrEmpty()) {
        val binTaskNames = sr.mapCache.binTasks[location]
          ?: throw BzError("errStepLocationInvalid", stepNo)
        if (!binTaskNames.contains(binTask)) {
          throw BzError("errStepBinTaskInvalid", stepNo, location, binTask)
        }
      }
    }
  }

  /**
   * 创建推空闲车单。
   * 在运单锁里执行。
   */
  fun createIdleAvoidOrder(sr: SceneRuntime, robotName: String, or: OrderRuntime, idlePointName: String) =
    sr.withOrderLock {
      val order = or.order
      val rr = sr.mustGetRobot(robotName)
      if (rr.orders.isNotEmpty()) {
        return@withOrderLock
      }

      if (RobotOrderPrediction.checkRobotAvailableForNewBzOrders(rr) != null) {
        logger.warn("Robot $rr not available for new avoid order!")
        val item = RobotOrderPrediction.checkRobotAvailableForNewBzOrders(rr)
        val message = lo("FleetDiagnosis_${item?.code}", item?.params)
        val ai = AlarmItem(
          sceneId = sr.sceneId,
          group = "Fleet",
          code = "*********",
          key = "*********-$robotName",
          level = AlarmLevel.Error,
          message = lo("*********", listOf(robotName, message)),
          args = listOf(sr.sceneId, robotName, order.id),
          tags = setOf(rr.tag, rr.sr.tag),
        )
        AlarmService.addItem(ai, ttl = 5000)
        return@withOrderLock
      }

      logger.info("Create transport order of avoid. Idle point=$idlePointName. $order")

      createOrders(sr, listOf(or))

      rr.autoOrder = RobotAutoOrder(RobotAutoOrderType.GiveWay, or.id, idlePointName)
      // 停靠，直接分派
      rr.orders[or.id] = or

      RobotService.persistRobotRuntime(rr)
    }

  /**
   * 向一个运单添加多个步骤。
   * 为防并发，在运单锁里执行。
   */
  fun addSteps(sr: SceneRuntime, orderId: String, newStepsReq: List<TransportStep>, stepFixed: Boolean) =
    sr.withOrderLock {
      val or = sr.mustGetOrderById(orderId)

      if (or.order.stepFixed) {
        throw BzError("errAddStepsButStepFixed", orderId)
      }

      val newSteps = newStepsReq.map {
        val step = it.copy(orderId = or.id)
        checkStep(sr, step)
        step
      }
      or.addSteps(newSteps)
      persistAddedSteps(newSteps)

      // 取放货点位
      val loadLoc = or.steps.find { it.forLoad }?.location
      val unloadLoc = or.steps.find { it.forUnload }?.location

      val newOrder = or.order.copy(
        stepNum = or.steps.size,
        stepFixed = stepFixed,
        loadPoint = loadLoc,
        unloadPoint = unloadLoc,
      )
      updateAndPersistOrder(or, newOrder, "Update order after steps added")

      // 立即请求一次分派
      DispatchOrderService.dispatchOrders(sr)
    }

  /**
   * 修改一个或多个运单步骤。按 id（步骤 ID）为主键。
   */
  fun updateSteps(sr: SceneRuntime, or: OrderRuntime, updates: List<UpdateStepReq>) = sr.withOrderLock {
    // 检查运单状态
    if (or.order.status == OrderStatus.Cancelled || or.order.status == OrderStatus.Done) {
      throw BzError("errCodeErr", "Bad order status for updating step: ${or.order.status}")
    }

    val steps = updates.map { update ->
      val oldStep = or.steps[update.stepIndex]
      if (oldStep.status != StepStatus.Executable) {
        throw BzError("errCodeErr", "Bad step status for updating step: ${oldStep.status}")
      }
      updateStepDiffFields(oldStep, update)
    }

    for (step in steps) {
      updateAndPersistStep(or, step, "Update steps by user")
    }

    // 取放货点位
    val loadLoc = or.steps.find { it.forLoad }?.location
    val unloadLoc = or.steps.find { it.forUnload }?.location

    if (loadLoc != or.order.loadPoint || unloadLoc != or.order.unloadPoint) {
      val newOrder = or.order.copy(
        loadPoint = loadLoc,
        unloadPoint = unloadLoc,
      )
      updateAndPersistOrder(or, newOrder, "Update order after steps updated")
    }

    // 更新缓存的运单信息，并持久化到数据库中。
    FleetLogger.info(
      module = "Order",
      subject = "UpdateSteps",
      sr = sr,
      robotName = null,
      msg = mapOf(
        "orderId" to or.id,
        "updatingSteps" to updates,
      ),
    )
  }

  /**
   * 产生修改后的步骤
   */
  private fun updateStepDiffFields(oldStep: TransportStep, update: UpdateStepReq): TransportStep {
    val updateRbkArgsStr = update.rbkArgs?.let { JsonHelper.writeValueAsString(it) }
    val newRbkArgsStr =
      if (updateRbkArgsStr != null && updateRbkArgsStr != oldStep.rbkArgs) updateRbkArgsStr else oldStep.rbkArgs

    return oldStep.copy(
      location = if (!update.location.isNullOrBlank()) {
        update.location
      } else {
        oldStep.location
      },
      rbkArgs = newRbkArgsStr,
      forLoad = update.forLoad ?: oldStep.forLoad,
      forUnload = update.forUnload ?: oldStep.forUnload,
      withdrawOrderAllowed = update.withdrawOrderAllowed ?: oldStep.withdrawOrderAllowed,
    )
  }

  /**
   * 删除未执行的运单步骤。
   */
  fun removeSteps(sr: SceneRuntime, or: OrderRuntime, stepIds: List<String>) = sr.withOrderLock {
    // / 检查运单状态
    if (or.order.status == OrderStatus.Cancelled || or.order.status == OrderStatus.Done) {
      throw BzError("errCodeErr", "Bad order status for removing steps: ${or.order.status}")
    }

    for (stepId in stepIds) {
      val oldStep = or.steps.find { it.id == stepId } ?: continue
      if (oldStep.status != StepStatus.Executable) {
        throw BzError("errCodeErr", "Bad step status for updating step: ${oldStep.status}")
      }
    }
    // 重新计算 stepIndex
    val steps = or.steps.filterNot { it.id in stepIds }.mapIndexed { index, step ->
      step.copy(stepIndex = index)
    }
    or.replaceSteps(steps)

    EntityRwService.removeMany("TransportStep", Cq.include("id", stepIds))

    // 取放货点位
    val loadLoc = or.steps.find { it.forLoad }?.location
    val unloadLoc = or.steps.find { it.forUnload }?.location

    val newOrder = or.order.copy(
      stepNum = or.steps.size,
      loadPoint = loadLoc,
      unloadPoint = unloadLoc,
    )
    updateAndPersistOrder(or, newOrder, "Update order after steps deleted")

    // 更新缓存的运单信息，并持久化到数据库中。
    FleetLogger.info(
      module = "Order",
      subject = "DeleteSteps",
      sr = sr,
      robotName = null,
      msg = mapOf("orderId" to or.id, "deletedStepIds" to stepIds),
    )
  }

  // 未完成运单的查询条件
  private fun unfinishedOrderCq(sceneId: String): ComplexQuery = Cq.and(
    Cq.eq("sceneId", sceneId),
    Cq.include(
      "status",
      listOf(
        OrderStatus.ToBeAllocated.name,
        OrderStatus.Allocated.name,
        OrderStatus.Executing.name,
        OrderStatus.Pending.name,
      ),
    ),
  )

  /**
   * 计算故障时间：若上一次是故障则故障时长就为上一次修改时间到现在
   */
  fun countFaultDuration(or: OrderRuntime) = if (or.order.fault) {
    val oldOrder = EntityRwService.findOneById("TransportOrder", or.id)
    val modifiedOn = oldOrder?.get("modifiedOn") as Date?
    val duration = if (modifiedOn != null) {
      (System.currentTimeMillis() - modifiedOn.time) / 1000.0
    } else {
      0.0
    }
    (or.order.faultDuration ?: 0.0) + duration
  } else {
    or.order.faultDuration
  }

  /**
   * 计算去放货的时长
   */
  fun countUnloadDuration(or: OrderRuntime) = if (or.order.unloadDuration == null && or.order.unloaded) {
    val unloadStep = or.steps.find { it.forUnload }
    ((unloadStep?.endOn?.time ?: 0) - (unloadStep?.startOn?.time ?: 0)) / 1000.0
  } else {
    or.order.unloadDuration
  }

  /**
   * 计算去取货的时长
   */
  fun countLoadDuration(or: OrderRuntime) = if (or.order.loadDuration == null && or.order.loaded) {
    val loadStep = or.steps.find { it.forLoad }
    // 为 0 的话估计就是 bug
    ((loadStep?.endOn?.time ?: 0) - (loadStep?.startOn?.time ?: 0)) / 1000.0
  } else {
    or.order.loadDuration
  }

  /**
   * 更新运单的优先级。
   */
  fun updatePriority(sr: SceneRuntime, orderId: String, priority: Int) = sr.withOrderLock {
    // 考虑到二次派单，可以调整非终态运单的优先级。
    // 即使调整了机器人正在执行的运单的优先级，也没啥太大的影响。
    val or = sr.mustGetOrderById(orderId)
    if (or.order.kind != OrderKind.Business) {
      throw BzError("errUpdatePriorityButNotBzOrder", orderId) // 自动运单的优先级没有太大的作用，就不修改了。
    }
    updateAndPersistOrder(or, or.order.copy(priority = priority), "Update priority")
    FleetLogger.info(
      module = "Order",
      subject = "UpdatePriority",
      sr = sr,
      robotName = or.order.actualRobotName,
      msg = mapOf("msg" to "from ${or.order.priority} to $priority"),
    )
  }

  /**
   * 保存新运单到数据库
   */
  private fun persistCreatedOrdersAndSteps(req: List<OrderRuntime>) {
    val evList = req.map { orderToEv(it.order) }

    val steps: List<EntityValue> = req.map {
      val stepEvList: List<EntityValue> = JsonHelper.mapper.convertValue(it.steps, jacksonTypeRef())
      stepEvList
    }.flatten()

    EntityRwService.createMany("TransportStep", steps)
    EntityRwService.createMany("TransportOrder", evList)
  }

  /**
   * 保存修改后的运单到数据库 TODO 删除
   */
  fun persistUpdatedOrder(order: TransportOrder) {
    // TODO 之类的所有都需要捕获异常
    val ev = orderToEv(order)
    EntityRwService.updateOne("TransportOrder", Cq.idEq(order.id), ev)
  }

  private fun orderToEv(order: TransportOrder): EntityValue {
    val ev: EntityValue = JsonHelper.mapper.convertValue(order, jacksonTypeRef())
    return ev
  }

  /**
   * 保存新步骤到数据库
   */
  private fun persistAddedSteps(steps: List<TransportStep>) {
    val evList: List<EntityValue> = JsonHelper.mapper.convertValue(steps, jacksonTypeRef())
    EntityRwService.createMany("TransportStep", evList)
  }

  /**
   * 更新步骤到 or 并保存到数据库。
   * 这里不需要加锁，只这里加没有意义，因为没有二次判断。
   */
  fun updateAndPersistStep(or: OrderRuntime, step: TransportStep, reason: String) {
    logger.info("Update order step: $reason. order=${or.id}, step [${step.stepIndex}]: $step")
    or.updateStep(step)
    val ev: EntityValue = JsonHelper.mapper.convertValue(step, jacksonTypeRef())
    EntityRwService.updateOne("TransportStep", Cq.idEq(step.id), ev)
  }

  /**
   * 根据运单号获取场景，单号在同一个数据库里全局唯一。
   */
  fun mustGetSceneIdByOrderId(orderId: String): String {
    val order = EntityRwService.findOneById("TransportOrder", orderId) ?: throw BzError("errNoOrderById", orderId)
    return order["sceneId"] as String? ?: throw BzError("errOrderNoSceneId", orderId)
  }

  /**
   * 是否有再装的潜力
   * 简单实现：没有全满。Cancelled 也不行：需要认为处理
   * TODO 复杂实现 都载货；虽然只是预定，但已处于不可被重新分派的状态
   */
  fun canRobotLoadMore(rr: RobotRuntime): Boolean = rr.bins.any {
    it.status == RobotBinStatus.Empty || it.status == RobotBinStatus.Reserved
  }

  fun calculateCanLoadCount(rr: RobotRuntime): Int = rr.bins.filter {
    it.status != RobotBinStatus.Filled
  }.size

  /**
   * 列出场景中正在执行的单子。isOrderRunning
   * 本方法本身不加锁。需要的话在外层加锁。
   */
  fun listSceneRunningOrders(sr: SceneRuntime) = sr.orders.values.filter {
    isOrderRunning(it.order)
  }

  /**
   * 列出区域中正在执行的单子。isOrderRunning, isOrderAssociatedWithArea
   * 本方法本身不加锁。需要的话在外层加锁。
   */
  fun listAreaRunningOrders(sr: SceneRuntime, areaId: Int) = sr.orders.values.filter {
    isOrderRunning(it.order) && isOrderAssociatedWithArea(it, sr, areaId)
  }

  /**
   * 运单是否正在执行。正在执行指已分配及之后、未终态的状态的单子。但故障了不算运行！
   * 故障了，比如地图问题，要允许改地图。
   */
  fun isOrderRunning(order: TransportOrder) = !order.fault &&
    order.status in listOf(
      OrderStatus.Allocated,
      OrderStatus.Executing,
      OrderStatus.Pending,
    )

  /**
   * 运单是否与区域关联：指运单有步骤的终点在区域内。
   * TODO 已完成的步骤是否就不考虑了。
   */
  fun isOrderAssociatedWithArea(or: OrderRuntime, sr: SceneRuntime, areaId: Int): Boolean {
    sr.mapCache.areaById[areaId] ?: return false
    for (step in or.steps) {
      if (step.location.isBlank()) continue
      val area = sr.mapCache.areaById[areaId] ?: return false
      if (area.mergedMap.pointNameMap.contains(step.location) ||
        area.mergedMap.pointLabelMap.contains(step.location) ||
        area.mergedMap.binNameMap.contains(step.location)
      ) {
        return true
      }
    }
    return false
  }

  /**
   * 获取运单的 oldRobots，并且其中包 actualRobotName
   */
  fun getOldRobotsContainsActualRobot(order: TransportOrder): List<String>? {
    val actualRobotName = order.actualRobotName
    val oldRobots = order.oldRobots?.toMutableList()

    return if (actualRobotName.isNullOrEmpty()) {
      oldRobots
    } else if (oldRobots.isNullOrEmpty()) {
      listOf(actualRobotName)
    } else {
      oldRobots.add(actualRobotName)
      oldRobots.distinct()
    }
  }

  private fun failedOrdersToAlarms(sr: SceneRuntime) {
    try {
      // 先删除清理运单的告警
      AlarmService.removeAllBySceneIdAndCode(sr.sceneId, "FleetOrderFault")
      doFailedOrdersToAlarms(sr)
    } catch (e: Exception) {
      logger.error("failedOrdersToAlarms error", e)
    }
  }

  private fun doFailedOrdersToAlarms(sr: SceneRuntime) {
    val faultOrders = sr.listOrders().filter { it.order.fault }

    // 添加运单故障告警
    for (or in faultOrders) {
      if (recoveryOrderIfCurrentMapNotMatch(sr, or)) continue
      val manualFinishAllowed = StepManualFinishService.allowManualFinish(sr, or)
      val ai = AlarmItem(
        sceneId = sr.sceneId,
        group = "Fleet",
        code = "FleetOrderFault",
        key = "FleetOrderFault-${or.id}",
        level = AlarmLevel.Error,
        message = StepManualFinishService.buildStepErrMsg(sr, or),
        args = listOf(sr.sceneId, or.id),
        actions = listOfNotNull(
          AlarmAction(lo("btnFaultRetry")),
          AlarmAction(lo("btnCancelOrder")),
          if (manualFinishAllowed) AlarmAction(lo("btnManualFinishedTask")) else null,
        ),
        tags = setOf(sr.tag),
      )

      AlarmService.addItem(ai, ttl = 3000) { actionIndex, args ->
        AlarmService.removeItem(ai.key)
        when (actionIndex) {
          0 -> OrderFaultService.retryByOrder(sr, args[1] as String)
          1 -> OrderCancelService.cancelOrder(sr, args[1] as String)
          2 -> StepManualFinishService.manualFinishingStep(sr, or)
        }
      }
    }
  }

  /**
   * 计算运单的一些时间。不抛异常
   */
  private fun calcOrderTime(sr: SceneRuntime) {
    try {
      for (or in sr.listOrders()) {
        if (or.order.waitExecuteDuration != null) continue
        val robotName = or.order.actualRobotName ?: continue
        val rr = sr.mustGetRobot(robotName)
        if (!rr.navigating) continue
        val firstStep = or.steps.firstOrNull() ?: continue
        // TODO 这样计算可能会有遗漏
        // 计算执行等待时间
        val waitExecuteDuration = if (or.order.doneStepIndex > 0) {
          // 步骤已经不是第一步了，但等待执行时间还是为空，说明步骤完成很快，直接使用创建运单到步骤完成的时长替代
          ((firstStep.endOn?.time ?: System.currentTimeMillis()) - or.order.createdOn.time) / 1000.0
        } else {
          // 步骤是第一步则取当前时间减去运单创建时间
          (System.currentTimeMillis() - or.order.createdOn.time) / 1000.0
        }
        sr.withOrderLock {
          val newOrder = or.order.copy(waitExecuteDuration = waitExecuteDuration)
          updateAndPersistOrder(or, newOrder, "Update waitExecuteDuration")
        }
      }
    } catch (e: Exception) {
      logger.error("calcOrderTime error", e)
    }
  }

  /**
   * 恢复因地图不匹配发生故障的运单
   */
  private fun recoveryOrderIfCurrentMapNotMatch(sr: SceneRuntime, or: OrderRuntime): Boolean {
    // 因地图不匹配原因发生故障的机器人
    val errorRobot = sr.listRobots()
      .firstOrNull {
        it.robotName == or.order.actualRobotName &&
          it.selfReport?.main?.currentMapNotMatched == false &&
          it.currentMapNotMatchedError
      }

    // 由于地图不匹配原因发生故障则自动故障重试
    if (errorRobot != null) {
      errorRobot.currentMapNotMatchedError = false
      try {
        OrderFaultService.retryByOrder(sr, or.id)
      } catch (e: Exception) {
        logger.error(lo("errRetryFaultOrderFail", listOf(or.id, e.message)))
      }
      return true
    }
    return false
  }

  /**
   * 校验机器人当前地图与调度中的机器人组地图是否一致，如果不一致则返回异常信息
   */
  fun checkCurrentMap(rr: RobotRuntime): String? {
    val notMatched = rr.selfReport?.main?.currentMapNotMatched
    return if (notMatched == null) {
      // 机器人离线
      lo("errRobotOffline", listOf(rr.robotName))
    } else if (notMatched) {
      // 机器人地图不匹配
      rr.currentMapNotMatchedError = true
      lo("errCurrentMapNotMatched", listOf(rr.robotName))
    } else {
      null
    }
  }

  private fun houseKeeping(sr: SceneRuntime) {
    if (sr.status != SceneStatus.Initialized) return
    calcOrderTime(sr)
    failedOrdersToAlarms(sr)
    RobotBinService.canceledBinsToAlarms(sr)
  }

  /**
   * ev to order，虽然现在实现简单，但将来可能有特殊字段
   */
  fun evToOrder(ev: EntityValue): TransportOrder = JsonHelper.mapper.convertValue(ev, jacksonTypeRef())

  /**
   * ev to step，虽然现在实现简单，但将来可能有特殊字段
   */

  fun evToStep(ev: EntityValue): TransportStep = JsonHelper.mapper.convertValue(ev, jacksonTypeRef())
}

/**
 * 不能修改这些字段
 *    id orderId stepIndex processingTime executingTime createdOn startOn endOn
 */
data class UpdateStepReq(
  val id: String, // 接口调用方必传
  val orderId: String, // 接口调用方必传
  val stepIndex: Int, // 接口调用方必传
  val status: StepStatus? = null,
  val location: String? = null,
  val rbkArgs: Map<String, Any?>? = null, // 动作参数
  val forLoad: Boolean? = null, // 在此步取货
  val forUnload: Boolean? = null, // 在此步卸货
  val withdrawOrderAllowed: Boolean? = null, // 是否可以重分派
)