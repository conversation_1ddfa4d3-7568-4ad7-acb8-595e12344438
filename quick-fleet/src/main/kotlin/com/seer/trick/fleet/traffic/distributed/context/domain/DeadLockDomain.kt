package com.seer.trick.fleet.traffic.distributed.context.domain

class DeadLockDomain {

  @Volatile
  var rePlanTime: Long = 0 // 重规划时间记录

  @Volatile
  var linkTime: Long = 0 // 解环时间记录

  @Volatile
  var idleTime: Long = 0 // 解 推空闲车的时间，避免重复推空闲车

  @Volatile
  var linkSuccess: Boolean = true

  var linkRobots: MutableList<String> = mutableListOf()

  var replanSuccess: Boolean = true

  var blockRobots: MutableList<String> = mutableListOf()

  @Volatile
  var failCount: Int = 0 // 失败次数

  val unreachablePoints: MutableList<String> = mutableListOf() // 已经过滤的可达点

  @Volatile
  var linkSuccessPoint: Pair<String, Int>? = null

  fun failIncrease() {
    failCount++
  }

  fun deadLockMessageClear() {
    failCount = 0
    unreachablePoints.clear()
  }

  fun linkSuccess() {
    linkSuccess = true
    linkRobots.clear()
    failCount = 0
  }
}