package com.seer.trick.fleet.domain

import com.seer.trick.BzError
import com.seer.trick.I18N
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.base.file.FileManager
import com.seer.trick.fleet.seer.Smap
import com.seer.trick.fleet.seer.SmapPos
import com.seer.trick.fleet.seer.SmapToRobotMapConverter
import com.seer.trick.fleet.service.CheckAreaContext
import com.seer.trick.fleet.service.SceneFileService
import com.seer.trick.fleet.service.SceneRuntime
import com.seer.trick.helper.JsonFileHelper
import org.apache.commons.io.FileUtils
import org.slf4j.LoggerFactory
import kotlin.collections.ArrayList
import kotlin.collections.HashMap
import kotlin.collections.HashSet

/**
 * 对场景相关对象的处理。
 */
object SceneHelper {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 后处理一个场景的所有区域，返回处理后的
   * 1. 合并地图；2. 处理点云图片；3. 处理 SVG 地图 4.处理上传的图片；
   */
  fun postProcessAreas(sr: SceneRuntime, areas: List<SceneArea>, checkContext: CheckAreaContext): List<SceneArea> {
    val start = System.currentTimeMillis()
    val sceneId = sr.sceneId
    AlarmService.removeByTag("$sceneId-conflictingElementsAlarm")
    try { // 合并区域地图
      val newAreas = areas.map { area ->
        var areaMap = mergeAreaMaps(sr, area.groupsMap, checkContext)
        areaMap = movePointCloudImage(sceneId, areaMap, area.name)
        areaMap = moveSvgMap(sceneId, areaMap, area.name)
        area.copy(mergedMap = areaMap, gmMap = parseGroupsMap(sceneId, area.name, area.groupsMap))
      }

      // 检查不同区域是否有同名点位、同编号、同名库位
      val errors = checkAreaConflicts(newAreas)
      checkContext.errors.addAll(errors)

      return newAreas
    } finally {
      val cost = System.currentTimeMillis() - start
      logger.debug("Postprocess scene: $sceneId cost: $cost ms")
    }
  }

  /**
   * 校验区域间的点位、编号、库位的冲突
   */
  private fun checkAreaConflicts(newAreas: List<SceneArea>): List<String> {
    // 过滤掉停用的区域
    val activeAreas = newAreas.filter { !it.disabled }
    // 检查不同区域是否有同名点位、同编号、同名库位
    val nameToAreaMap: MutableMap<String, String> = HashMap()
    val numberToAreaMap: MutableMap<String, AreaPoint> = HashMap()
    val binToAreaMap: MutableMap<String, String> = HashMap()
    val smPointType = "SwitchMap"
    val errors: MutableList<String> = ArrayList()
    activeAreas.forEach { area ->
      for (point in area.mergedMap.points) {
        // 除了 SM 点，其他类型点位不允许同名
        if (nameToAreaMap.containsKey(point.name) && point.type != smPointType) {
          val duplicateAreaName = nameToAreaMap[point.name]
          errors.add(I18N.lo("errDuplicatePointName", listOf(area.name, duplicateAreaName, point.name)))
          continue
        }
        nameToAreaMap[point.name] = area.name

        // 摘取点位的编号
        val regex = Regex("""\D+""")
        val numList = point.name.split(regex)
        val serialNo = numList.last()
        if (numberToAreaMap.containsKey(serialNo)) {
          // 除了 SM 点 与 SM 点可以相同，其他不允许有编号相同的点位
          val areaPoint = numberToAreaMap[serialNo]
          if (areaPoint!!.pointType != smPointType || point.type != smPointType) {
            errors.add(
              I18N.lo(
                "errDuplicateSerialNo",
                listOf(area.name, point.name, areaPoint.areaName, areaPoint.pointName, serialNo),
              ),
            )
            continue
          }
        }
        numberToAreaMap[serialNo] = AreaPoint(area.name, point.name, point.type)
      }

      // 不允许相同的库位
      for (bin in area.mergedMap.bins) {
        if (binToAreaMap.containsKey(bin.name)) {
          val duplicateAreaName = binToAreaMap[bin.name]
          errors.add(I18N.lo("errDuplicateBinNameInDifferentAreas", listOf(area.name, duplicateAreaName, bin.name)))
          continue
        }
        binToAreaMap[bin.name] = area.name
      }
    }
    return errors
  }

  data class AreaPoint(val areaName: String, val pointName: String, val pointType: String)

  /**
   * 将一个区域下多机器人的地图合并为该区域的地图
   */
  private fun mergeAreaMaps(
    sr: SceneRuntime,
    groupsMap: Map<Int, RobotAreaMapRecord>,
    checkContext: CheckAreaContext,
  ): SceneAreaMap {
    val converter = SmapToRobotMapConverter()
    val idToBinMap: MutableMap<Int, List<BinPoint>> = HashMap()
    val idToPointMap: MutableMap<Int, Map<String, SmapPos>> = HashMap()
    for ((gId, rm) in groupsMap) { // 遍历机器人组
      val mapFile = SceneFileService.pathToFile(sr.sceneId, rm.mapFile)
      val smap: Smap? = JsonFileHelper.readJsonFromFile(mapFile)
      if (smap == null) {
        logger.error("Map file $mapFile not found")
        continue
      }

      // 点位或路径不存在则报错
      if (smap.advancedPointList.isNullOrEmpty() || smap.advancedCurveList.isNullOrEmpty()) {
        throw BzError("errNoPointOrPath", rm.mapName)
      }

      converter.add(smap, rm.mapName)
      idToBinMap[gId] = smap.getAllBins().map { BinPoint(it.instanceName, it.pointName) }
      idToPointMap[gId] = smap.getAllPoints().associate { it.instanceName to it.pos }
    }

    // 校验库位
    val binErrors = checkBin(sr, idToBinMap, groupsMap)
    // 校验同名点位的差异度
    val pointErrors = checkPointDiff(sr, idToPointMap)
    checkContext.errors.addAll(binErrors)
    checkContext.errors.addAll(pointErrors)

    return converter.build()
  }

  /**
   * 解析所有组地图
   */
  private fun parseGroupsMap(
    sceneId: String,
    areaName: String,
    groupsMap: Map<Int, RobotAreaMapRecord>,
  ): Map<Int, SceneAreaMap> {
    val gmMap = mutableMapOf<Int, SceneAreaMap>()
    for ((gId, rm) in groupsMap) { // 遍历机器人组
      val mapFile = SceneFileService.pathToFile(sceneId, rm.mapFile)
      val smap: Smap? = JsonFileHelper.readJsonFromFile(mapFile)
      if (smap == null) {
        logger.error("Map file $mapFile not found")
        continue
      }

      // 点位或路径不存在则报错
      if (smap.advancedPointList.isNullOrEmpty() || smap.advancedCurveList.isNullOrEmpty()) {
        throw BzError("errNoPointOrPath", rm.mapName)
      }
      val converter = SmapToRobotMapConverter()
      converter.add(smap, rm.mapName)
      var areaMap = converter.build()
      // 移动点云、SVG 地图
      areaMap = movePointCloudImage(sceneId, areaMap, areaName)
      areaMap = moveSvgMap(sceneId, areaMap, areaName)
      gmMap[gId] = areaMap
    }
    return gmMap
  }

  data class BinPoint(val binName: String, val pointName: String?)

  /**
   * 校验不同机器人组中同名点位的差异度
   */
  private fun checkPointDiff(sr: SceneRuntime, idToPointMap: MutableMap<Int, Map<String, SmapPos>>): List<String> {
    val pointNameMap: MutableMap<String, MutableList<Int>> = HashMap()
    for ((gId, point) in idToPointMap) {
      for (name in point.keys) {
        val gIds = pointNameMap.computeIfAbsent(name) { ArrayList() }
        gIds.add(gId)
      }
    }

    val errors: MutableList<String> = ArrayList()
    val duplicateNameMap = pointNameMap.filter { it.value.size > 1 }
    for ((point, gIds) in duplicateNameMap) {
      for (index in 0 until gIds.size) {
        val gId = gIds[index]
        val pos = idToPointMap[gId]!![point]!!
        for (nextIndex in gIds.size - 1 downTo 1) {
          val nextGId = gIds[nextIndex]
          val nextPos = idToPointMap[nextGId]!![point]!!
          // 计算欧式距离，不能大于 0.15m
          val distance = GeoHelper.euclideanDistance(pos.x, pos.y, nextPos.x, nextPos.y)
          // TODO 这个值是否需要变为可配置？
          val maxDistance = 0.15
          if (distance > maxDistance) {
            errors.add(
              I18N.lo(
                "errPointOutOfDistance",
                listOf(point, sr.robotGroups[gId]?.name, sr.robotGroups[nextGId]?.name, maxDistance),
              ),
            )
          }
        }
      }
    }
    return errors
  }

  /**
   * 校验库位相关：同名库位、不同机器人组库位绑定的点位不同
   */
  private fun checkBin(
    sr: SceneRuntime,
    idToBinMap: MutableMap<Int, List<BinPoint>>,
    groupsMap: Map<Int, RobotAreaMapRecord>,
  ): List<String> {
    // binName -> gId
    val binToIdMap: MutableMap<String, Int> = HashMap()
    val errors: MutableList<String> = ArrayList()
    for ((gId, binPoints) in idToBinMap) {
      val binSet: MutableSet<String> = HashSet()
      for (binPoint in binPoints) {
        if (!binSet.add(binPoint.binName)) {
          // 同一个 smap 中不允许有同名库位
          errors.add(I18N.lo("errDuplicateBinName", listOf(groupsMap[gId]?.mapName, binPoint.binName)))
          continue
        }

        if (binToIdMap.containsKey(binPoint.binName)) {
          val gId1 = binToIdMap[binPoint.binName]
          val duplicatedPointName = idToBinMap[gId1]?.find { it.binName == binPoint.binName }?.pointName
          if (duplicatedPointName != binPoint.pointName) {
            // 库位 M 在不同机器人组的地图里绑定到了不同的点位
            errors.add(
              I18N.lo(
                "errBinBoundDifferentPoint",
                listOf(binPoint.binName, sr.robotGroups[gId]?.name, sr.robotGroups[gId1]?.name),
              ),
            )
          }
          continue
        }
        binToIdMap[binPoint.binName] = gId
      }
    }
    return errors
  }

  // 移动点云图片
  fun movePointCloudImage(sceneId: String, sceneAreaMap: SceneAreaMap, areaName: String): SceneAreaMap {
    val envPointCloud = sceneAreaMap.envPointCloud ?: return sceneAreaMap
    if (envPointCloud.imagePath.isBlank()) return sceneAreaMap

    val tmpFile = FileManager.pathToFile(envPointCloud.imagePath)
    val newFile = SceneFileService.getSceneAreaEnvPointCloudFile(sceneId, areaName)
    val oldFile = if (!tmpFile.exists()) {
      SceneFileService.pathToFile(sceneId, envPointCloud.imagePath)
    } else {
      tmpFile
    }

    if (oldFile.canonicalPath == newFile.canonicalPath) return sceneAreaMap
    newFile.delete()
    FileUtils.moveFile(oldFile, newFile)

    return sceneAreaMap.copy(
      envPointCloud = envPointCloud.copy(
        imagePath = SceneFileService.fileToPath(sceneId, newFile),
      ),
    )
  }

  // 移动 SVG 地图
  fun moveSvgMap(sceneId: String, sceneAreaMap: SceneAreaMap, areaName: String): SceneAreaMap {
    if (sceneAreaMap.svgMapFile.isNullOrBlank()) return sceneAreaMap

    // 临时文件
    val tmpFile = FileManager.pathToFile(sceneAreaMap.svgMapFile)
    val newFile = SceneFileService.getSceneAreaSvgMapFile(sceneId, areaName)
    val oldFile = if (!tmpFile.exists()) {
      SceneFileService.pathToFile(sceneId, sceneAreaMap.svgMapFile)
    } else {
      tmpFile
    }

    if (oldFile.canonicalPath == newFile.canonicalPath) return sceneAreaMap
    newFile.delete()
    FileUtils.moveFile(tmpFile, newFile)

    return sceneAreaMap.copy(svgMapFile = SceneFileService.fileToPath(sceneId, newFile))
  }

  /**
   * 检查是否存在圆弧类型的路径
   */
  fun existArcPath(sceneId: String, groupsMap: Map<Int, RobotAreaMapRecord>): Boolean {
    for (mp in groupsMap.values) {
      val mapFile = SceneFileService.pathToFile(sceneId, mp.mapFile)
      val smap: Smap = JsonFileHelper.readJsonFromFile(mapFile) ?: continue
      val curveList = smap.advancedCurveList ?: continue
      for (path in curveList) {
        if (path.className == "ArcPath") return true
      }
    }
    return false
  }
}