package com.seer.trick.fleet.traffic.venus

import com.seer.trick.fleet.FleetLogger
import com.seer.trick.fleet.service.SceneRuntime
import com.seer.trick.fleet.traffic.TrafficTaskStatus
import java.util.*

/**
 * CBS 高层求解器。只能被用一次。
 */
class CbsResolver(sr: SceneRuntime, requests: Map<String, RobotPlanRequest>, o: PathFindingOption) :
  BaseHighResolver(sr, requests, o) {

  /**
   * 下一个节点 ID
   */
  @Volatile
  private var highNodeIdCounter = 0L

  /**
   * 最后一个处理的节点（从 FOCAL 取出）
   */
  private var lastHighNode: HighNode? = null

  /**
   * OPEN
   * TODO 为什么不也用冲突数量作为第一层判断，因为这是 open，要保证最优
   */
  private val open = PriorityQueue<HighNode> { o1, o2 ->
    if (o1.cost != o2.cost) {
      o1.cost.compareTo(o2.cost)
    } else {
      // 用 ID 打破平局
      o1.id.compareTo(o2.id)
    }
  }

  /**
   * FOCAL
   */
  private val focal = PriorityQueue<HighNode> { o1, o2 ->
    if (o1.conflictNum != o2.conflictNum) {
      o1.conflictNum.compareTo(o2.conflictNum)
    } else if (o1.cost != o2.cost) {
      o1.cost.compareTo(o2.cost)
    } else {
      // 用 ID 打破平局
      o1.id.compareTo(o2.id)
    }
  }

  /**
   * CLOSED
   */
  val processed: MutableList<HighNode> = Collections.synchronizedList(ArrayList())

  /**
   * 记录在搜索过程中因无法生成合法子节点而被判定失败的机器人。
   * 用于最后的降级方案构造。
   */
  private val failRobots = mutableSetOf<String>()

  private lateinit var startAvoidConstraintsAll: List<RobotConstraint>

  /**
   * 开始求解
   */
  override fun doResolve(): PlanResult {
    // 根节点
    val rootNode = buildRootNode() ?: return buildHighResult(false, "No init solution")

    // 空闲机器人避让预处理（让路运单）
    resolveIdleRobot(rootNode)

    open.offer(rootNode)
    focal.offer(rootNode)

    var costMin = rootNode.cost

    while (open.isNotEmpty()) {
      val timeout = getResolveTimeout()
      if (System.currentTimeMillis() - startOn > timeout) {
        return buildHighResult(false, "No solution in limit time $timeout ms")
      }

      val oldCostMin = costMin
      costMin = open.peek().cost

      // 如果当下的最小成本变大了，让更多节点进入 FOCAL
      if (costMin > oldCostMin) {
        for (n in open) {
          if (n.cost > oldCostMin * o.highW && n.cost <= costMin * o.highW) {
            focal.offer(n)
          }
          // TODO 避免遍历整个 OPEN 集合
          // if (val > bestCost * m_w) {
          //   break;
          // }
        }
      }

      val n = focal.poll()
      open.remove(n) // TODO 效率优化
      ++highNodeExpanded
      lastHighNode = n
      processed += n

      logMe(
        "Current node: ${n.id} | Master robot: ${n.newConstraintForRobotName} | Conflicts: ${n.conflictNum} | " +
          "Cost: ${n.cost} | First conflict: ${n.conflictConstraints}",
      )

      if (n.conflictConstraints.isNullOrEmpty()) {
        // 无冲突，得解
        val solutions = mutableMapOf<String, RobotSolution>()
        for (robotCtx in n.robots.values) {
          if (robotCtx.solution != null) solutions[robotCtx.robotName] = robotCtx.solution
        }
        solutions.forEach { (robotName, sol) ->
          FleetLogger.trace(
            module = logModuleHighResolver,
            subject = "HighSearchResultPath",
            sr = sr,
            robotName = robotName,
            msg = mapOf(
              "initPath" to n.robots[robotName]!!.request.initPath,
              "path" to sol.path,
            ),
          )
        }
        return buildHighResult(true, "", solutions)
      }

      // 有冲突
      for ((robotName, cs) in n.conflictConstraints) {
        val childNodes = buildChildNodes(n, robotName, cs)

        for (childNode in childNodes) {
          // TODO 会不会重复？防止高层节点重复检查？
          open.offer(childNode)

          // TODO 之类为啥要重复检查无冲突得解
          if (childNode.cost <= costMin * o.highW) {
            if (childNode.conflictConstraints.isNullOrEmpty()) {
              // 无冲突，得解
              val solutions = mutableMapOf<String, RobotSolution>()
              for (robotCtx in childNode.robots.values) {
                if (robotCtx.solution != null) solutions[robotCtx.robotName] = robotCtx.solution
              }
              solutions.forEach { (robotName, sol) ->
                FleetLogger.trace(
                  module = logModuleHighResolver,
                  subject = "HighSearchResultPath",
                  sr = sr,
                  robotName = robotName,
                  msg = mapOf(
                    "initPath" to n.robots[robotName]!!.request.initPath,
                    "path" to sol.path,
                  ),
                )
              }
              return buildHighResult(true, "", solutions)
            }
            focal.offer(childNode)
          }
        }

        // 若无法为该机器人生成子节点，记录失败
        if (childNodes.isEmpty()) failRobots += robotName
      }

      // 如果 OPEN 为空，尝试降级方案（向 OPEN/FOCAL 注入新根节点并 continue）
      if (open.isEmpty()) {
        buildFallbackNode(rootNode, failRobots)?.let { fb ->
          open.offer(fb)
          focal.offer(fb)
          failRobots.clear()
        }
      }
    }

    // 无解
    return buildHighResult(false, "No solution, all states checked")
  }

  /**
   * 构造根节点
   */
  private fun buildRootNode(): HighNode? {
    val start = System.currentTimeMillis()

    val robotContexts = mutableMapOf<String, RobotPlanContext>()
    val allConstraints = mutableMapOf<String, List<RobotConstraint>>()
    for ((robotName, robotReq) in requests) {
      robotContexts[robotName] = RobotPlanContext(
        robotName,
        robotReq,
        robotReq.trafficTask != null,
        reserveTargetPoint[robotName],
        null,
      )
      allConstraints[robotName] = mutableListOf()
    }

    allConstraints.putAll(ResConflictManager.buildInitPathConstraints(requests))

    // 预生成起点/旋转避让静态约束，后续复用（不包含当前机器人本身）
    startAvoidConstraintsAll =
      generateStartPointAvoidanceConstraints(robotContexts, o, "CBSStartRootStatic") ?: emptyList()

    // 对每个机器人求解
    for ((robotName, _) in robotContexts) {
      // 为当前机器人组合起点避让约束（排除自身）
      val constraintsForRobot =
        (allConstraints[robotName] ?: emptyList()) +
          startAvoidConstraintsAll.filter { it.masterRobotName != robotName }

      allConstraints[robotName] = constraintsForRobot
    }

    // 所有机器人总成本
    var cost = 0.0
    // 所有机器人展开的总节点数
    var expandedCount = 0L

    // 对每个机器人求解
    for ((robotName, robotCtx) in robotContexts) {
      if (robotCtx.request.trafficTask == null) continue // 不动

      val lowResolver = TargetManyLowResolver(
        sr,
        robotName,
        robotCtx.request,
        sr.mapCache.areaById[robotCtx.request.trafficTask.areaId]!!
          .groupedMaps[robotCtx.request.trafficTask.robotGroupId]!!,
        o.collisionModels,
        robotCtx.reservePointName,
        friendlyId,
        highNodeIdCounter,
        allConstraints[robotName]!!, // 已经包含了起点避让约束
        robotContexts,
        o,
      )
      val rs = lowResolver.search("Build the root high node")
      expandedCount += rs.expandedCount
      lowNodeExpanded += rs.expandedCount

      // 无初始解
      if (!rs.ok) return null

      robotContexts[rs.robotName] = robotCtx.copy(solution = rs)

      cost += rs.cost
    }

    // TODO 为什么要拿最早的
    val (conflictNum, constraints) = ResConflictManager.getConflictNumAndEarliest(robotContexts, o)

    val n = HighNode(
      id = highNodeIdCounter,
      parentId = -1,
      robots = robotContexts,
      newConstraintForRobotName = null, // 开始时不为任何机器人
      newConstraint = null,
      allConstraints = allConstraints,
      conflictNum = conflictNum,
      conflictConstraints = constraints,
      cost = cost,
      planCost = System.currentTimeMillis() - start,
      lowExpanded = expandedCount,
    )
    logHighNode(n)
    return n
  }

  /**
   * 构造子节点。让路可能产生多个子节点；普通子节点一次只产生一个。
   */
  private fun buildChildNodes(
    parent: HighNode,
    robotName: String, // 对该机器人施加约束
    newConstraint: RobotConstraint,
  ): List<HighNode> {
    val childNodes = mutableListOf<HighNode>()

    // 根据约束情况为空闲车创建让路运单
    // 暂时先注释
    ensureGiveWayOrderForConstraint(parent.robots[robotName]!!, robotName)

    val n1 = buildNormalChildNode(parent, robotName, newConstraint)
    if (n1 != null) childNodes += n1

    return childNodes
  }

  /**
   * 产生一个普通子节点。
   */
  private fun buildNormalChildNode(
    parent: HighNode,
    toRobotName: String, // 给这个机器人施加约束
    newConstraint: RobotConstraint,
  ): HighNode? {
    // 放前面，每次尝试构造都 +1
    val childNodeId = synchronized(this) {
      ++highNodeIdCounter
    }

    FleetLogger.debug(
      module = logModuleHighResolver,
      subject = "BuildNormalChild",
      sr = sr,
      robotName = toRobotName,
      msg = mapOf(
        "childNodeId" to childNodeId,
        "toRobotName" to toRobotName,
        "newConstraint" to newConstraint,
        "parentId" to parent.id,
      ),
    )

    // 向机器人添加新约束
    val childAllConstraints = ResConflictManager.addConstraints(parent.allConstraints, toRobotName, newConstraint)
    // 一个机器人的所有约束
    val robotConstraints = childAllConstraints[toRobotName]!!

    val robotCtx = parent.robots[toRobotName]!!

    val lowResolver = TargetManyLowResolver(
      sr,
      toRobotName,
      robotCtx.request,
      sr.mapCache.areaById[robotCtx.request.robotInfo.stand.areaId]!!.groupedMaps[
        sr.mustGetRobot(robotCtx.robotName)
          .mustGetGroup().id,
      ]!!,
      o.collisionModels,
      robotCtx.reservePointName,
      friendlyId,
      childNodeId,
      robotConstraints,
      parent.robots,
      o,
    )
    val rs = lowResolver.search("Build a normal child node")
    lowNodeExpanded += rs.expandedCount

    // 找不到解
    if (!rs.ok) return null

    val robotNewCtx = robotCtx.copy(solution = rs)

    // clone 一份
    val newRobotContexts: MutableMap<String, RobotPlanContext> = HashMap(parent.robots)
    newRobotContexts[toRobotName] = robotNewCtx

    val (conflictNum, constraints) = ResConflictManager.getConflictNumAndEarliest(newRobotContexts, o)

    // 检查是否已存在相同约束
    if (constraints != null &&
      constraints.values.filter { it.pointName == toRobotName }.any {
        val newConstraint = it
        robotConstraints.any { it == newConstraint }
      }
    ) {
      FleetLogger.error(
        module = logModuleHighResolver,
        subject = "DuplicateConstraint",
        sr = sr,
        robotName = toRobotName,
        msg = mapOf(
          "constraint" to constraints.values.first(),
          "parentId" to parent.id,
          "robotConstraints" to robotConstraints,
          "constraints" to constraints,
          "path" to rs.path,
        ),
      )
      throw IllegalStateException("Duplicate constraint for robot $toRobotName: ${constraints.values.first()}")
    }

    val cost = parent.cost - robotCtx.solution!!.cost + rs.cost

    val n = HighNode(
      id = childNodeId,
      parentId = parent.id,
      newConstraintForRobotName = toRobotName,
      newConstraint = newConstraint,
      allConstraints = childAllConstraints,
      robots = newRobotContexts,
      conflictNum = conflictNum,
      conflictConstraints = constraints,
      cost = cost,
      planCost = rs.planCost,
      lowExpanded = rs.expandedCount,
    )
    logHighNode(n)
    return n
  }

  /**
   * 构造高层结果
   */
  private fun buildHighResult(
    ok: Boolean,
    reason: String,
    solutions: Map<String, RobotSolution> = emptyMap(),
  ): PlanResult = PlanResult(
    ok,
    reason,
    solutions,
    giveWayGoals = mapOf(),
    timeCost = System.currentTimeMillis() - startOn,
    highNodeExpanded = highNodeExpanded,
    lowNodeExpanded = lowNodeExpanded,
  )

  override fun logHighNode(n: HighNode) {
    val solDesc = n.robots.values.joinToString("；") {
      "${it.robotName}: stand=${it.request.robotInfo.stand}, task=${it.request.trafficTask}," +
        " reservePointName=${it.reservePointName}, " +
        "sol=${it.solution}"
    }

    nodeLogs += VenusHelper.logTimestamp() + " 产生高层节点 | ID=${n.id} | 父节点=${n.parentId}" +
      " | 新解冲突数=${n.conflictNum} | 新解成本=${n.cost}" +
      " | 耗时=${n.planCost}ms | 展开底层节点=${n.lowExpanded}" +
      " | 施加新约束=${n.newConstraint} | 施加给机器人=${n.newConstraintForRobotName} " +
      " | 解=$solDesc"
  }

  /**
   * 让路：空闲机器人位于其他机器人路径上时，提前为其下发让路运单。
   */
  private fun resolveIdleRobot(root: HighNode) {
    // 有任务机器人的路径集合
    val taskPaths = root.robots.values
      .filter { it.request.trafficTask != null }
      .flatMap { it.solution?.path ?: emptyList() }

    // 无任务机器人
    val idleRobots = root.robots.values
      .filter { it.request.trafficTask == null || it.request.trafficTask.status == TrafficTaskStatus.Success }
      .map { it.robotName }

    for (idle in idleRobots) {
      val ctx = root.robots[idle] ?: continue
      val stand = ctx.request.robotInfo.stand
      val conflict = taskPaths.any { s -> s.toPosition.pointName == stand.pointName }
      if (conflict) ensureGiveWayOrderForConstraint(ctx, idle)
    }
  }

  /**
   * 构造失败降级节点：对 failRobots 保持原地或执行 initPath。
   */
  private fun buildFallbackNode(root: HighNode, failRobots: Collection<String>): HighNode? {
    if (failRobots.isEmpty()) return null

    val activeCtx = root.robots.filterValues { it.request.trafficTask != null }
    if (activeCtx.keys.all { it in failRobots }) return null

    val ctx = root.robots.toMutableMap()

    failRobots
      .asSequence()
      .mapNotNull { name -> activeCtx[name]?.let { name to it } }
      .forEach { (name, old) ->
        val newSol = old.request.initPath?.takeIf { it.isNotEmpty() }?.let { path ->
          val fixed = buildList {
            addAll(path.dropLast(1))
            add(path.last().copy(timeEnd = -1))
          }
          RobotSolution(robotName = name, path = fixed, ok = true, cost = 0.0)
        }
        ctx[name] = old.copy(solution = newSol, moved = true)
      }

    val newCost = ctx.values.sumOf { it.solution?.cost ?: 0.0 }
    val (confNum, earliest) = ResConflictManager.getConflictNumAndEarliest(ctx, o)

    return HighNode(
      id = ++highNodeIdCounter,
      parentId = -1,
      robots = ctx,
      newConstraintForRobotName = null,
      newConstraint = null,
      allConstraints = root.allConstraints,
      conflictNum = confNum,
      conflictConstraints = earliest,
      cost = newCost,
      planCost = 0,
      lowExpanded = 0,
    )
  }
}