package com.seer.trick.fleet.service

import com.seer.trick.base.file.FileManager
import com.seer.trick.fleet.domain.SceneBasic
import com.seer.trick.helper.JsonFileHelper
import org.apache.commons.io.FileUtils
import org.apache.commons.io.FilenameUtils
import java.io.File
import java.nio.file.Paths

/**
 * 管理场景数据的持久化和读取。
 * 每个场景一个文件夹。
 */
object SceneFileService {

  /**
   * 列出所有场景的 ID
   */
  fun listScenesIds(): List<String> {
    val dir = getScenesDir()
    if (!dir.exists()) return emptyList()
    return dir.list()?.mapNotNull {
      if (!File(dir, it).isDirectory) return@mapNotNull null // 必须是目录
      val name = File(it).name
      if (!name.startsWith("scene-")) return@mapNotNull null // 防止混入了其它文件，导致加载失败
      val id = name.substringAfter("scene-")
      id.ifBlank { null }
    } ?: emptyList()
  }

  /**
   * 读取指定场景的基础信息
   */
  fun loadSceneBasic(id: String): SceneBasic? {
    val dir = getSceneDir(id)
    if (!dir.exists()) return null
    val file = File(dir, "scene-basic.json")
    if (!file.exists()) return null
    return JsonFileHelper.readJsonFromFile(file)
  }

  fun saveSceneBasic(basic: SceneBasic) {
    val dir = getSceneDir(basic.id)
    dir.mkdirs()
    JsonFileHelper.writeJsonToFile(File(dir, "scene-basic.json"), basic)

    // TODO 清理机器人地图文件，删除不用的机器人地图文件
  }

  /**
   * 删除指定场景的所有文件
   */
  fun removeSceneFiles(id: String) {
    val dir = getSceneDir(id)
    if (!dir.exists()) return
    FileUtils.deleteDirectory(dir)
  }

  /**
   * 一个指定场景的目录
   */
  fun getSceneDir(sceneId: String): File = File(getScenesDir(), "scene-$sceneId")

  /**
   * 指定场景存放机器人地图的目录
   */
  fun getSceneRobotMapsDir(sceneId: String): File = File(getSceneDir(sceneId), "robot-maps")

  /**
   * 存放机器人模型文件的目录
   */
  fun getSceneRobotModelsDir(sceneId: String): File = File(getSceneDir(sceneId), "robot-models")

  /**
   * 场景下存放导入的 RDS CORE 场景的地图
   *
   */
  fun getSceneRdsCoreDir(sceneId: String): File = File(getSceneDir(sceneId), "rds-core-scene")

  /**
   * 场景文件存放目录
   */
  private fun getScenesDir(): File = File(FileManager.getFilesDir(), "fleet-scenes3")

  /**
   * 场景图片资源目录
   */
  fun getSceneImageDir(sceneId: String): File = File(getSceneDir(sceneId), "image")

  /**
   * 一个指定场景区域的点云图片。
   */
  fun getSceneAreaEnvPointCloudFile(sceneId: String, areaName: String): File =
    File(getSceneImageDir(sceneId), "env-$areaName.png")

  /**
   * 一个指定场景区域的 SVG 图片。
   */
  fun getSceneAreaSvgMapFile(sceneId: String, areaName: String): File =
    File(getSceneImageDir(sceneId), "area-map-$areaName.svg")

  /**
   * 一个制定场景的 上传的图片
   */
  fun getSceneAreaUploadImageFile(sceneId: String, areaName: String, picId: String): File =
    File(getSceneImageDir(sceneId), "$areaName-$picId.png")

  /**
   * 相对于场景目录的路径
   */
  fun fileToPath(sceneId: String, file: File): String = getSceneDir(sceneId).toURI().relativize(file.toURI()).path

  /**
   * 场景里的文件
   */
  fun pathToFile(sceneId: String, path: String): File = File(getSceneDir(sceneId), path)

  /**
   * 去除场景路径
   */
  fun subpath(sceneId: String, path: String): String {
    val scenePath = Paths.get("fleet-scenes3", "scene-$sceneId")
    val filePath = Paths.get(path)
    val subpath = if (filePath.startsWith(scenePath) && scenePath.nameCount < filePath.nameCount) {
      filePath.subpath(scenePath.nameCount, filePath.nameCount).toString()
    } else {
      path
    }
    return FilenameUtils.separatorsToUnix(subpath)
  }
}