package com.seer.trick.fleet.device.door.adapter


import com.seer.trick.fleet.device.door.DoorAdapterReport

/**
 * 门适配器。与门控通讯，监控状态，下发指令。
 */
abstract class DoorAdapter {

  open fun init() {
  }

  /**
   * 不抛异常
   */
  open fun dispose() {
  }

  open fun report(): DoorAdapterReport = DoorAdapterReport()

  /**
   * 只是发开门信号，不等门实际开
   */
  open fun openDoor(remark: String) {
  }

  /**
   * 支持发关门信号，不等门关
   */
  open fun closeDoor(remark: String) {
  }
}