package com.seer.trick.fleet.device.lift.adapter.vendor.jinbo

import com.seer.trick.base.net.tcp.ReqResTcpClient

/**
 * <AUTHOR>
 * @date 2025/5/27 11:54
 */
class JinBoTcpClient(liftName: String, host: String, port: Int) {

  private val client = new(liftName, host, port)

  private var flowNoCounter = 0

  fun request(
    apiNo: Int,
    requestStr: String,
    reqMaxTry: Int = REQ_MAX_TRY,
    reqRetryDelay: Long = REQ_RETRY_DELAY,
  ): String = client.request(
    
    JinBoTcpMsg(nextFlowNo(), apiNo, requestStr),
    reqMaxTry = reqMaxTry,
    reqRetryDelay = reqRetryDelay,
  ).body

  fun dispose() {
    client.dispose()
  }

  @Synchronized
  private fun nextFlowNo(): Int {
    val no = (flowNoCounter + 1) % 256
    flowNoCounter = no
    return no
  }

  companion object {
    const val START_MARK: Byte = 0x5A
    const val HEAD_SIZE = 8
    const val REQ_MAX_TRY = 3
    const val REQ_RETRY_DELAY = 1000L

    fun new(liftName: String, host: String, port: Int): ReqResTcpClient<JinBoTcpMsg> {
      val encoder = JinBoTcpMsgEncoder()
      val decoder = JinBoTcpMsgDecoder()
      return ReqResTcpClient(host, port, encoder, decoder, logPrefix = "[$liftName]", readTimeout = 5000)
    }
  }
}