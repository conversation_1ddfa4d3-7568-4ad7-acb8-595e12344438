package com.seer.trick.fleet.device.door

import java.util.*

/**
 * 门的配置
 */
data class SceneDoor(
  val id: Int,
  val name: String = "",
  val disabled: Boolean = false, // 是否禁用。
  val areaId: Int = -1, // 所在区域，门只能属于一个区域
  val controlledPathKeys: List<String> = emptyList(), // 门下的所有路径，对于双向路要填两条！
  val openPreDist: Double = 0.0, // 机器人距离自动门多远时，自动门可以开门。
  val controlledPointNames: List<String> = emptyList(), // 在哪些点位可以开关门，和 controlledPathKeys 互斥
  //
  val x: Double? = null, // 自动门在地图中的 X 坐标值，如果没配置，则将门显示在其中一条 controlledPathKey 的中点。
  val y: Double? = null, // 自动门在地图中的 Y 坐标值，如果没配置，则将门显示在其中一条 controlledPathKey 的中点。
  val theta: Double = 0.0, // 自动门在地图中的朝向
  val width: Double = 2.0, // 宽度

  val mock: Boolean = false, // 是否是仿真模式
  val adapterType: DoorAdapterType = DoorAdapterType.Mock, // 适配器类型

  // PLC
  val plcName: String? = null,
  val plcOpenDoor: PlcWriteCommand? = null, // 控制开门的地址
  val plcCloseDoor: PlcWriteCommand? = null, // 控制关门的地址
  val plcOpenedStatus: PlcReadCommand? = null, // 开门到位的地址

  val plcFaultStatus: PlcFaultCommand? = null, // 故障信号处理

  val needOpenReset: Boolean = false, // 是否需要开门复位，需要的时候在关门的时候先开门复位
  val needCloseReset: Boolean = false, // 是否需要关门复位
  // 如果关门重置间隔大于 0 则在关门指令下发指定间隔后复位；否则在开门时先复位关门（如果 needCloseReset 为 true）
  val closeResetInterval: Long = 0,

  // 部分自动门会自己关，需要持续下发开门信号
  val needKeepOpening: Boolean = false, // 是否需要持续下发开门信号
  val keepOpeningInterval: Long = 500, // 持续下发开门信号的时间间隔
)

enum class DoorAdapterType {
  Plc,
  Http,
  Script,
  Mock,
}

data class PlcWriteCommand(
  val code: Int,
  val address: Int,
  val slaveId: Int = 0,
  val value: Int = 0, // 写出的值
)

data class PlcReadCommand(
  val code: Int,
  val address: Int,
  val slaveId: Int = 0,
  val value: Int = 0, // 期望的值
)

data class PlcFaultCommand(val code: Int, val address: Int, val slaveId: Int = 0, val faults: List<FaultMessage>)

data class FaultMessage(
  val value: Int, // 故障值
  val desc: String, // 故障描述
)

/**
 * 自动门这类设备的特有属性
 */
data class DoorConfig(
  /**
   * 自动门保持开门到位状态的持续时间，单位 ms。
   *  - 0：此自动门不会自动关门。
   *  - 0 ~ 5000，无效的数值，会被处理成 0。
   *  - >5000，保持开门到位状态一段时间之后，就自动关门了。
   *
   *  计算路径成本时，可能会用到这个数据。
   */
  val openHoldingMs: Long = 0,

  /**
   * 自动门从“关门状态” 变为“开门到位状态” 的耗时，单位 ms 。
   * 计算路径成本时，可能会用到这个数据。
   */
  val openCostsMs: Long = 5000,
)

/**
 * 需要展示在界面上的自动门详情
 */
data class DoorUiReport(
  val online: Boolean, // 是否在线
  val status: DoorMainStatus, // 开门状态。关键信息放到前面，看日志方便些。
  val fault: Boolean, // 是否故障：
  val faultMsg: String, // 故障信息。可能会有多条故障信息。
  val lastStatusUpdateTime: Date, // 最后一次状态更新的时间
  val disabled: Boolean, // 是否禁用
  val openDoorRobotNames: List<String>, // 开门机器人列表

) {
  fun toStringExceptTime() =
    "online: $online, disabled: $disabled, status: $status, openDoorRobotNames: $openDoorRobotNames, " +
      "fault: $fault, faultMsg: $faultMsg"
}

/**
 * 门适配器报告
 */
data class DoorAdapterReport(
  val online: Boolean = false, // 是否在线
  val fault: Boolean = false, // 是否故障：
  val faultMsg: String = "", // 故障信息。可能会有多条故障信息。
  val mainStatus: DoorMainStatus = DoorMainStatus.Unknown,
  val timestamp: Date = Date(),
)

/**
 * 门设备状态
 */
enum class DoorMainStatus {
  Unknown, // 门状态未知
  Closed, // 门已关闭
  Closing, // 门正在关闭
  Opening, // 门正在打开
  Opened, // 门已打开
}