package com.seer.trick.fleet.service

import com.seer.trick.I18N.lo
import com.seer.trick.fleet.FleetLogger
import com.seer.trick.fleet.adapter.RobotRbkAdapter
import com.seer.trick.fleet.device.door.DoorDispatcher
import com.seer.trick.fleet.domain.*
import com.seer.trick.fleet.order.MoveActionsTransferRbk
import com.seer.trick.fleet.order.OrderService
import com.seer.trick.fleet.order.Req3066
import com.seer.trick.fleet.order.StepExecuteContext
import com.seer.trick.fleet.seer.RotationDirection
import com.seer.trick.fleet.traffic.TrafficTaskRuntime
import com.seer.trick.fleet.traffic.TrafficTaskStatus
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.getTypeMessage
import com.seer.trick.helper.shortId
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.ConcurrentHashMap

/**
 * 负责处理机器人的移动动作（MoveActionRuntime）：下发并监控。
 * 注意：动作产生、下发、发送给机器人后，TrafficTask 和 StepExecuteContext 可能被取消。
 */
object RobotMoveService {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 下发机器人接下来的动作。交管层调用。
   */
  fun appendActions(rr: RobotRuntime, tt: TrafficTaskRuntime, moves: List<MoveActionRuntime>) =
    rr.sr.withOrderLock {
      if (moves.isEmpty()) return@withOrderLock

      if (tt != rr.pendingTrafficTask || tt.status == TrafficTaskStatus.Cancelled) {
        // 交管任务已过时
        FleetLogger.info(
          module = "RobotMove",
          subject = "AppendMovesButTaskCancel",
          sr = rr.sr,
          robotName = rr.robotName,
          msg = mapOf(
            "oldTrafficTask" to tt.id,
            "pendingTrafficTask" to rr.pendingTrafficTask?.id,
          ),
        )
        return@withOrderLock
      }

      FleetLogger.info(
        module = "RobotMove",
        subject = "AppendMoves",
        sr = rr.sr,
        robotName = rr.robotName,
        msg = mapOf(
          "newMoves" to moves.map { it.req },
          "trafficTask" to tt.id,
        ),
      )

      // 在 append 的时候，产生路径导航请求（3066），只产生一次
      MoveActionsTransferRbk.buildNavTasks(rr, moves)

      // 按需添加一些过门需要的参数
      DoorDispatcher.prepareMoveActions(rr, moves)

      rr.moveActions.addAll(moves)
      tt.moveActions.addAll(moves)
    }

  /**
   * 执行下发给机器人的动作。StepExecuteService 调用。在步骤执行线程里执行，阻塞执行。
   * 有异常不要处理（让运单失败），抛出去，由 StepExecuteService 处理。
   */
  fun move(sec: StepExecuteContext, tt: TrafficTaskRuntime) {
    // 因为交管产生动作需要时间，以及执行需要条件（如可控），因此套一层循环
    // 如果机器人销毁，或者步骤、交管任务被取消，结束
    while (true) {
      doMove(sec, tt)

      // 成功或失败了
      if (tt.status == TrafficTaskStatus.Success || tt.status == TrafficTaskStatus.Failed) return

      // 被撤回、取消了
      if (sec.withdrawn || tt.status == TrafficTaskStatus.Cancelled) {
        throw OrderProcessingError(OpErrCode.Interrupted, "Robot move interrupted")
      }

      Thread.sleep(200)
    }
  }

  /**
   * 尝试下发动作并等待完成。运单故障类异常抛出去让上层处理！
   */
  private fun doMove(sec: StepExecuteContext, tt: TrafficTaskRuntime) {
    val rr = sec.rr
    if (rr.moveActions.isEmpty()) return

    // 没有已经下发的 moveAction，则标记导航结束
    if (!rr.moveActions.any { it.sent && it.status == MoveActionStatus.Created }) {
      rr.navigating = false
    }

    throwIfStepOrTrafficTaskCancelled(sec, tt)
    RobotService.throwIfDisabledOrDisposed(rr)

    if (RobotService.isRobotOkToExecutingOrder(rr)) {
      // 处理前置和后置设备动作，获取准备好的动作列表
      RobotMoveProcessor.beforeSendingMoveActionToRobot(sec, tt)

      // 关键动作标记是否可撤回
      markWithdraw(sec, tt)
    }

    throwIfStepOrTrafficTaskCancelled(sec, tt)
    RobotService.throwIfDisabledOrDisposed(rr)

    if (RobotService.isRobotOkToExecutingOrder(rr)) {
      // 发送 3066
      sendAllCreatedActions(rr, sec, tt)
    }

    throwIfStepOrTrafficTaskCancelled(sec, tt)
    RobotService.throwIfDisabledOrDisposed(rr)

    // 查询一次 3066 结果
    queryNavTasks(rr)

    throwIfStepOrTrafficTaskCancelled(sec, tt)
    RobotService.throwIfDisabledOrDisposed(rr)

    // 队列中有失败的
    val failedAction = rr.moveActions.find { it.anyFailed() }
    if (failedAction != null) {
      Thread.sleep(1000) // 立即查可能查不到告警
      RobotService.fetchStateNow(rr)
      val alarmsStr = RobotService.alarmsToStr(rr)
      val failedNavTask = failedAction.relatedNavTasks.find {
        it.taskStatus == NavTaskResult.NT_FAILED || it.taskStatus == NavTaskResult.NT_CANCELLED
      }
      val msg = lo(
        "OpErrCodeNavTaskFailed",
        listOf(alarmsStr, failedNavTask?.navTask?.taskId, failedAction.req.id, tt.id),
      )
      throw OrderProcessingError(OpErrCode.NavTaskFailed, msg)
    }

    val doneAction = rr.sr.withOrderLock {
      val doneIndex = rr.moveActions.indexOfLast { it.allComplete() }
      if (doneIndex >= 0) {
        // 如果最后一个动作是原地动作，且前一个动作已经完成，当最后一个动作完成 ——
        // 交管会下发最后一个动作是原地动作的动作序列
        rr.moveActions[doneIndex]
      } else {
        null
      }
    }
    if (doneAction != null) afterActionDone(sec, tt, doneAction)
  }

  /**
   * 标记 withdrawOrderAllowed 和 withdrawStepAllowed
   */
  private fun markWithdraw(sec: StepExecuteContext, tt: TrafficTaskRuntime) {
    val rr = sec.rr
    rr.sr.withOrderLock {
      val step = sec.getStep()
      val finalAction = rr.moveActions.firstOrNull { it.req.finalAction }

      if (finalAction != null && (step.forLoad || step.forUnload)) {
        // 要下发取货动作了，不能撤回
        rr.doingKeyAction = true

        if (sec.or.withdrawOrderAllowed) {
          logger.info("Make order ${sec.or.id} withdraw not allowed, final action=${finalAction.req}")
          sec.or.withdrawOrderAllowed = false
        }

        setWithdrawStepAllowed(sec, step, finalAction.req, !rr.doingKeyAction)
      } else {
        // 非关键动作 || 取放货的 moveAction 还没创建
        rr.doingKeyAction = false
        setWithdrawStepAllowed(sec, step, finalAction?.req, !rr.doingKeyAction)
      }
    }
  }

  private fun setWithdrawStepAllowed(
    sec: StepExecuteContext,
    step: TransportStep,
    finalActionReq: MoveActionReq?,
    withdrawAllowed: Boolean,
  ) {
    if (sec.or.withdrawStepAllowed != withdrawAllowed) {
      logger.info(
        "Make step ${sec.or.id}:${step.stepIndex} withdraw ${if (withdrawAllowed) "allowed" else "not allowed"}, final action=$finalActionReq",
      )
    }
    sec.or.withdrawStepAllowed = withdrawAllowed
  }

  /**
   * 取所有已创建、未下发的动作，下发。暂时不控制下发数量（交给交管控制）。
   */
  private fun sendAllCreatedActions(
    rr: RobotRuntime,
    sec: StepExecuteContext,
    tt: TrafficTaskRuntime,
  ) {
    // checkFilteredActions(rr)
    // 动作序列前面可能有已处理的动作。中间动作可能已被取消。因此只过滤创建、未发送的、未被过滤掉的。
    // 未被过滤掉的主要是某些动作不能下发给交管，比如充电的最后一个 self position 动作。
    val createdActions = rr.moveActions.filter {
      it.status == MoveActionStatus.Created &&
        !it.sent &&
        it.releasePreprocessOk
    }

    if (createdActions.isEmpty()) return

    // 将动作转换成 rbk 的动作，同时在转换过程中建立 MoveActionRuntime 与 Req3066 的关联关系
    val navTasks = createdActions.map { it.relatedNavTasks }.flatten()
    if (navTasks.isEmpty()) return

    val navTaskReqs = navTasks.map { it.navTask }

    // 此方法从开始到这里，存在此时动作已被取消的情况。
    // 其实不管怎么检查，总存在请求控制权、发 3066 前，动作被取消的情况。
    // 因此，这里先不增加检查了，如果确实被取消，在后续结果那处理。

    // 校验 md5
    OrderService.checkCurrentMap(rr)?.let {
      throw OrderProcessingError(OpErrCode.MapNotSync, it)
    }

    // 下发动作给 rbk
    try {
      RobotRbkAdapter.go3066(rr, navTaskReqs)
    } catch (e: Exception) {
      throw OrderProcessingError(OpErrCode.FailedToSendNavTask, e.getTypeMessage())
    }

    // 如果下发后被取消，这里取消下
    if (isStepOrTrafficTaskCancelled(sec, tt)) {
      tryToCancelNavTask(rr)
    }

    rr.sr.withOrderLock {
      // 标记导航开始
      rr.navigating = true
      // 标记发送成功
      for (action in createdActions) {
        action.sent = true
      }
      navTasks.map { it.endSendTime = Date() }
    }

    val actionDescIds = createdActions.map { it.descId() }
    FleetLogger.info(
      module = "RobotMove",
      subject = "MoveSent",
      sr = rr.sr,
      robotName = rr.robotName,
      msg = mapOf("moves" to actionDescIds, "req3066s" to navTaskReqs.map { it.taskId }),
    )
  }

  /**
   * 取所有已发送的动作，查询状态。
   * 取所有已发送的动作的目的是为了加快上报的速度，之前是一个一个上报的，当机器人很快，点位密集的时候，会卡顿
   * 此方法不加运单锁，不二次检查动作状态，这两步在上层做。
   */
  private fun queryNavTasks(rr: RobotRuntime) {
    // 注意，这里生效的前提是：完成（成功失败取消）后移除 MoveAction。
    // it.filtered 为 true 时代表被过滤掉了，并没有真正发给机器人，所以也并不需要查询是否完成
    val sentActions = rr.moveActions.filter { it.sent && it.status == MoveActionStatus.Created }
    if (sentActions.isEmpty()) return

    // 收集所有需要查询的 taskId（包括组合动作的多个 Req3066）
    val taskIdsToQuery = sentActions.flatMap { action ->
      action.relatedNavTasks.map { it.navTask.taskId }
    }.distinct()

    if (taskIdsToQuery.isEmpty()) return

    val results = try {
      RobotRbkAdapter.query3066List(rr, taskIdsToQuery)
    } catch (e: Exception) {
      // 错误就退出，外层循环再发起查询
      FleetLogger.error(
        module = "RobotMove",
        subject = "Query3066Error",
        sr = rr.sr,
        robotName = rr.robotName,
        msg = mapOf("taskIds" to taskIdsToQuery),
        e,
      )
      return
    }
    if (results.isNullOrEmpty()) return // 查不到结果，直接退出

    // 首先更新所有路径导航的状态
    for (action in sentActions) {
      for (navTasks in action.relatedNavTasks) {
        val queryResult = results.firstOrNull { it.taskId == navTasks.navTask.taskId }
        if (queryResult != null) navTasks.taskStatus = queryResult.status
      }
    }
  }

  /**
   * 动作执行完成，标记动作成功。通知交管。
   * 这个方法不能全加锁。
   */
  private fun afterActionDone(
    sec: StepExecuteContext,
    tt: TrafficTaskRuntime,
    action: MoveActionRuntime,
  ) {
    val rr = sec.rr
    FleetLogger.info(
      module = "RobotMove",
      subject = "MoveDone",
      sr = rr.sr,
      robotName = rr.robotName,
      msg = mapOf("move" to action.descId()),
    )

    // 获取最新状态
    RobotService.fetchStateNow(rr)

    // 再次检查下是否需要上报完成：如果步骤执行或任务已经取消，就不用再上报了
    throwIfStepOrTrafficTaskCancelled(sec, tt)

    // 移除完成了的所有动作！
    while (rr.moveActions.isNotEmpty()) {
      val ma = rr.moveActions.removeAt(0)
      ma.status = MoveActionStatus.Done

      // 关门等动作，不能跳动作
      RobotMoveProcessor.afterMoveActionDone(rr, action)

      if (ma == action) break
    }

    throwIfStepOrTrafficTaskCancelled(sec, tt)

    val trafficService = rr.sr.trafficService
    trafficService.afterMoveActionDone(rr, action.req)

    throwIfStepOrTrafficTaskCancelled(sec, tt)

    // 任务的最后一个动作
    if (action.req.finalAction) {
      // 通过 tt=Success 让 StepExecuteService 知道任务已经完成
      tt.updateStatus(rr, TrafficTaskStatus.Success, null)
      if (rr.moveActions.isNotEmpty()) clearAllActions(rr)

      trafficService.afterTrafficTaskDone(rr)
    }
  }

  /**
   * 清空 rr.moveActions 和 rr.recentReq3066s
   */
  fun clearAllActions(rr: RobotRuntime) = rr.sr.withOrderLock {
    rr.moveActions.clear()
    rr.recentNavTasks.clear()
  }

  /**
   * 取消导航任务，如果机器人可控。不报错。
   * TODO 可惜不能指定 task_id 取消
   */
  fun tryToCancelNavTask(rr: RobotRuntime) {
    rr.coolingFrom = System.currentTimeMillis()
    if (RobotService.isControlCommandAllowed(rr)) {
      logger.info("Cancel nav task")
      try {
        RobotRbkAdapter.cancelNavTask(rr)
      } catch (e: Exception) {
        logger.error("Cancel nav task", e)
      }
    } else {
      logger.info("Try to cancel 3066, but robot out of control or offline")
    }
  }

  /**
   * 如果步骤或交管任务被撤回或取消，抛 Interrupted
   */
  private fun throwIfStepOrTrafficTaskCancelled(sec: StepExecuteContext, tt: TrafficTaskRuntime) {
    if (isStepOrTrafficTaskCancelled(sec, tt)) {
      throw OrderProcessingError(OpErrCode.Interrupted, "Robot move interrupted")
    }
  }

  /**
   * sec 或 tt 被取消
   */
  private fun isStepOrTrafficTaskCancelled(sec: StepExecuteContext, tt: TrafficTaskRuntime) =
    sec.withdrawn || tt.status == TrafficTaskStatus.Cancelled
}

/**
 * 移动动作请求部分，即对移动的要求。
 */
data class MoveActionReq(
  val id: String = IdHelper.oidStr(), // TODO 更有意义的 ID？
  val index: Long, // 这次移动在整个规划的移动中的索引
  val finalAction: Boolean = false, // 最后一个动作
  val deviceId: String? = null, // 设备 id
  val fromPointName: String,
  val toPointName: String,
  val robotEnterTheta: Double, // 机器人进入线路的弧度 TODO 允许 null
  val robotExitTheta: Double, // 机器人离开线路的弧度 TODO 允许 null
  val loadEnterTheta: Double? = null, // 货物进入的弧度 TODO 允许 null
  val loadExitTheta: Double? = null, // 货物退出的弧度 TODO 允许 null
  val rbkArgs: String? = null, // 动作参数
  val rotationDirection: RotationDirection? = null, // 用于控制旋转的方向
  val percentage: Double? = null, // 占路径的百分比
  val orderId: String?,
  val stepIndex: Int?,
  val stepId: String?,
  val trafficMethod: TrafficMethod,
  val extra: MutableMap<String, Any?> = ConcurrentHashMap(), // 额外参数，可变
  val createdOn: Date = Date(),
)

enum class MoveActionStatus {
  Created,
  Done,
}

/**
 * 仅在内存里，不持久化
 */
class MoveActionRuntime(val ttId: String, val req: MoveActionReq) {

  @Volatile
  var status: MoveActionStatus = MoveActionStatus.Created

  @Volatile
  var releasePreprocessOk: Boolean = false // 下发前的预处理成功

  /**
   * 是否已发给机器人
   */
  @Volatile
  var sent = false

  /**
   * 关联的路径导航请求列表，一个 MoveAction 可能对应多个路径导航
   */
  val relatedNavTasks: MutableList<NavTaskRuntime> = mutableListOf()

  /**
   * 添加关联的路径导航请求
   */
  fun addNavTask(navTask: Req3066): NavTaskRuntime {
    val navTaskRuntime = NavTaskRuntime(navTask = navTask, startSendTime = Date())
    relatedNavTasks.add(navTaskRuntime)
    return navTaskRuntime
  }

  fun anyFailed() = relatedNavTasks.isNotEmpty() &&
    relatedNavTasks.any {
      it.taskStatus == NavTaskResult.NT_FAILED || it.taskStatus == NavTaskResult.NT_CANCELLED
    }

  fun allComplete() = relatedNavTasks.isNotEmpty() &&
    relatedNavTasks.all {
      it.taskStatus == NavTaskResult.NT_COMPLETED
    }

  override fun toString(): String = "(req=$req, tt=$ttId, sent=$sent, status=$status)"

  /**
   * 更好的移动动作标识，把关联任务、步骤、运单也输出。
   */
  fun descId(): String =
    "Action=${req.id}/tt=${ttId.shortId()}/Step=${req.stepIndex}/Order=${req.orderId?.shortId()}/Index=${req.index}"
}

/**
 * 路径导航请求的运行时状态包装类
 */
data class NavTaskRuntime(
  val navTask: Req3066,
  @Volatile
  var taskStatus: Int = 0,
  val startSendTime: Date, // 发送开始时间
  var endSendTime: Date? = null, // 发送完成时间（初始为 null)
)