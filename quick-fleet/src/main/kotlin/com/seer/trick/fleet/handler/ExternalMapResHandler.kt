package com.seer.trick.fleet.handler

import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.getReqBody
import com.seer.trick.fleet.traffic.ExternalMapResManager
import com.seer.trick.fleet.traffic.MapResourceUnit
import io.javalin.http.Context

object ExternalMapResHandler {

  fun registerHandlers() {
    val c = Handlers("api/fleet/external-map-res")
    c.post("request", ::request, auth())
    c.post("release", ::release, auth())
    c.post("release-by-owner", ::releaseByOwner, auth())
    c.post("release-all", ::releaseAll, auth())
  }

  private fun request(ctx: Context) {
    val req: List<MapResourceUnit> = ctx.getReqBody()
    ctx.json(ExternalMapResManager.request(req))
  }

  private fun release(ctx: Context) {
    val req: List<String> = ctx.getReqBody()
    ExternalMapResManager.release(req)
  }

  private fun releaseByOwner(ctx: Context) {
    val req: ReleaseByOwnerReq = ctx.getReqBody()
    ExternalMapResManager.releaseByOwner(req.owner, req.sceneId, req.areaId)
  }

  data class ReleaseByOwnerReq(val owner: String, val sceneId: String? = null, val areaId: Int? = null)

  private fun releaseAll(ctx: Context) {
    ExternalMapResManager.releaseAll()
  }
}