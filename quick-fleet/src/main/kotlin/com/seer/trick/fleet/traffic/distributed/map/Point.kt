package com.seer.trick.fleet.traffic.distributed.map

import com.fasterxml.jackson.annotation.JsonIgnore
import kotlin.math.abs

class Point(
  val name: String,
  val type: PointType,
  x: Double,
  y: Double,
  val direction: Int?,
  val avoid: Boolean, // 是否可避让
  val rotate: Boolean, // 是否可旋转
  val junction: Boolean = false, // 是否可连接申请
) : Position(x = x, y = y, pointName = name, posType = PosType.POINT) {

  // 出边
  @JsonIgnore
  var toLines: MutableList<Line> = mutableListOf()

  // 入边
  @JsonIgnore
  var fromLines: MutableList<Line> = mutableListOf()

  override fun toString(): String =
    "Point(pointName='$pointName', type=${type.name}, x=$x, y=$y, avoid=$avoid, rotate=$rotate, direction=$direction)"

  fun copy(dx: Double, dy: Double): Point {
    val p = Point(name, type, dx, dy, direction, avoid, rotate, junction)
    p.toLines = toLines
    p.fromLines = fromLines
    return p
  }
}

/**
 * 点的类型
 * */
enum class PointType {
  // 普通类型
  COMMON,

  // 工作站类型
  STATION,

  // 充电点
  CHARGE,

  // 旋转点
  ROTATE,

  // 未知
  UNDEFINE,
}

// 在点或线的位置信息
open class Position(val x: Double, val y: Double, val pointName: String, val posType: PosType) {
  fun distance(p: Position): Double = abs(x - p.x) + abs(y - p.y)

  fun getDistance(px: Double, py: Double): Double = abs(x - px) + abs(y - py)
}

enum class PosType { POINT, LINE, UNDEFINE }

// 带方向的位置信息
data class Pose(val x: Double, val y: Double, val theta: Int) {
  fun getDistance(px: Double, py: Double): Double = abs(x - px) + abs(y - py)
}

// 位置信息
open class Site(val x: Double, val y: Double) {
  fun distance(p: Site): Double = abs(x - p.x) + abs(y - p.y)
}

data class Bound(val minX: Double, val maxX: Double, val minY: Double, val maxY: Double)