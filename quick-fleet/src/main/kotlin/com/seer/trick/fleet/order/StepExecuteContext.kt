package com.seer.trick.fleet.order

import com.seer.trick.fleet.domain.Point2D
import com.seer.trick.fleet.domain.TransportStep
import com.seer.trick.fleet.map.MapPointCache
import com.seer.trick.fleet.service.RobotRuntime
import com.seer.trick.helper.IdHelper

/**
 * 表示步骤的一次执行
 * 如果步骤执行被撤回、故障，下次新建一个对象。
 */
class StepExecuteContext(val rr: RobotRuntime, val or: OrderRuntime, val stepIndex: Int) {
  val exeId = IdHelper.oidStr()

  val orderId = or.order.id
  val stepId = or.steps[stepIndex].id

  /**
   * 是否被撤回
   */
  @Volatile
  var withdrawn: Boolean = false

  /**
   * 故障
   */
  @Volatile
  var fault: Boolean = false

  /**
   * 故障原因
   */
  @Volatile
  var faultMsg: String? = null

  /**
   * 目标点位
   */
  val targetPointCache: MapPointCache

  /**
   * 目标点位所在区域
   */
  val targetAreaId: Int

  /**
   * 动作 operation/binTask
   */
  val stepOp: String? = getStep().parseBinTaskOrOperation()

  init {
    val step = getStep()
    targetPointCache = rr.sr.mapCache.mustGetPointCacheByGroupAndLoc(rr, step.location)
    targetAreaId = targetPointCache.areaId
  }

  /**
   * step 会被修改，所以不缓存
   */
  fun getStep(): TransportStep = or.steps[stepIndex]

  fun digest(): StepExecuteDigest {
    val step = getStep()
    val stepPointCoordinate = targetPointCache.point.let { Point2D(it.x, it.y) }

    return StepExecuteDigest(
      orderId = orderId,
      stepId = step.id,
      stepIndex = stepIndex,
      stepLocation = step.location,
      stepPointName = targetPointCache.point.name,
      stepPointCoordinate = stepPointCoordinate,
      stepTargetAreaId = targetAreaId,
      stepOp = stepOp,
    )
  }

  /**
   * 获取放料架时的料架方向，若运单有则取运单的料架方向，否则使用点位/库位上的料架方向
   */
  fun getUnloadContainerDir(): Double? {
    if (or.order.containerDir != null) return or.order.containerDir
    val areaCache = rr.sr.mapCache.areaById[targetAreaId] ?: return null
    val areaMapCache = areaCache.groupedMaps[rr.mustGetGroup().id] ?: return null
    // 该点若是库位则取库位上的方向，否则取点位上的方向
    val dir = areaMapCache.binNameMap[getStep().location]?.bin?.unloadContainerDir
    return dir ?: areaMapCache.pointNameMap[getStep().location]?.point?.unloadContainerDir
  }

  override fun toString(): String =
    "$orderId:$stepIndex" + (if (withdrawn) "(withdrawn)" else "") + (if (fault) " (fault)" else "")
}

/**
 * 选择要执行的运单步骤
 */
data class StepExecuteDigest(
  val orderId: String,
  val stepId: String,
  val stepIndex: Int,
  val stepLocation: String?,
  val stepPointName: String?,
  val stepPointCoordinate: Point2D?,
  val stepTargetAreaId: Int?,
  val stepOp: String?,
)