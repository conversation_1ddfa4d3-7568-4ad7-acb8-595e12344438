package com.seer.trick.fleet.map

import com.seer.trick.fleet.domain.*

/**
 * 运行时，表示一个区域的地图。既可以表示这个区域所有组合并的地图，也可以表示一个组的地图。
 * 不可变对象。
 * mapName 可能为空
 */
class AreaMapCache(val areaId: Int, val areaMap: SceneAreaMap, val mapName: String) {

  /**
   * point id -> point
   */
  val pointIdMap: MutableMap<Long, MapPointCache> = HashMap()

  /**
   * point name -> point
   */
  val pointNameMap: MutableMap<String, MapPointCache> = HashMap()

  /**
   * point label -> point
   */
  val pointLabelMap: MutableMap<String, MapPointCache> = HashMap()

  /**
   * path id -> path
   */
  val pathIdMap: MutableMap<Long, MapPathCache> = HashMap()

  /**
   * path key -> path
   */
  val pathKeyMap: MutableMap<String, MapPathCache> = HashMap()

  /**
   * zone id -> zone
   */
  val zoneIdMap: MutableMap<Long, MapZoneCache> = HashMap()

  /**
   * bin point id -> bin point
   */
  // val binPointMap: Map<Long, SceneBinPoint>  = HashMap()

  /**
   * bin id -> bin
   */
  val binIdMap: MutableMap<Long, MapBinCache> = HashMap()

  /**
   * bin name -> bin
   */
  val binNameMap: MutableMap<String, MapBinCache> = HashMap()

  init {
    this.initPoints()
    this.initPaths()
    this.initZones()
    this.initBins()
  }

  private fun initPoints() {
    for (point in areaMap.points) {
      val forwardPaths: MutableList<MapPath> = ArrayList() // 从这个点位出发的所有路径
      val backwardPaths: MutableList<MapPath> = ArrayList() // 到从这个点位的所有路径
      for (path in areaMap.paths) {
        if (path.fromPointId == point.id) forwardPaths += path
        if (path.toPointId == point.id) backwardPaths += path
      }
      val pr = MapPointCache(point, forwardPaths, backwardPaths, areaId)

      pointIdMap[point.id] = pr
      pointNameMap[point.name] = pr
      if (!point.label.isNullOrBlank()) pointLabelMap[point.label] = pr
    }
  }

  private fun initPaths() {
    for (path in areaMap.paths) {
      val pr = MapPathCache(path)
      pathIdMap[path.id] = pr
      pathKeyMap[path.key] = pr
    }
  }

  private fun initBins() {
    for (bin in areaMap.bins) {
      val br = MapBinCache(bin)
      binIdMap[bin.id] = br
      binNameMap[bin.name] = br
    }
  }

  private fun initZones() {
    for (zone in areaMap.zones) {
      val zp = Polygon(zone.polygon)

      val intersectingZones = listIntersectingZones(zone, zp)
      val zoneRuntime = MapZoneCache(zone, zp, intersectingZones)

      if (zone.polygon.isNotEmpty()) {
        for (p in areaMap.points) {
          if (GeoHelper.isPointInPolygon(zp, p.x, p.y)) {
            zoneRuntime.pointNames += p.name
          }
        }
      }

      zoneIdMap[zone.id] = zoneRuntime
    }

    // 最后把与区块相交的其他区块中的点合到这个区块：
    // 如果三个区块 ZA、ZB、ZC 相交，则三个区块实际是一个大区块。三个区块内的点位集合的并集是每个区块所覆盖的点。
    for (zr in zoneIdMap.values) {
      for (zId in zr.intersectingZones) {
        val z2 = zoneIdMap[zId]!!
        // 合并原因是因为互斥区的传递性，只有互斥区才会传递！！
        if (z2.zone.type == "BlockArea") {
          zr.pointNames += z2.pointNames
        }
      }
    }
  }

  private fun listIntersectingZones(z1: MapZone, zp1: Polygon): Set<Long> {
    val otherZones: MutableSet<Long> = HashSet()
    if (z1.polygon.isEmpty()) return otherZones
    for (z2 in areaMap.zones) {
      if (z1 == z2) continue
      if (z2.polygon.isEmpty()) continue
      val zp2 = Polygon(z2.polygon)
      if (GeoHelper.isPolygonsIntersecting(zp1, zp2)) otherZones += z2.id
    }
    return otherZones
  }
}

/**
 * 运行时，表示一个区域的地图，地图中的点位。
 * 不可变对象。
 */
data class MapPointCache(
  val point: MapPoint,
  val forwardPaths: List<MapPath>, // 从这个点位出发的所有路径
  val backwardPaths: List<MapPath>, // 到从这个点位的所有路径
  val areaId: Int, // 所属区域
  // val zones: List<SceneZone>, // 这个点位所在的所有区块
)

/**
 * 场景中路径的辅助类。
 * 不可变对象。
 */
data class MapPathCache(val path: MapPath)

/**
 * 场景中区块的辅助类
 * 不可变对象。
 */
data class MapZoneCache(
  val zone: MapZone,
  val polygon: Polygon, // 区域多边形
  val intersectingZones: Set<Long>, // 与这个区块相交的其他区块
  val pointNames: MutableSet<String> = HashSet(), // 这个区块中所有点位名
  // val paths: List<MapPath>, // 与区块相交所有路径
)

data class MapBinCache(val bin: SceneBin)