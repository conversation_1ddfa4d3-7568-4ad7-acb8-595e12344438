package com.seer.trick.fleet.traffic.distributed.block

import com.seer.trick.fleet.traffic.distributed.lock.LockType
import com.seer.trick.fleet.traffic.distributed.map.Point
import java.util.concurrent.*

/**
 *  障碍物管理中心
 * */
object BlockService {

  private val blockMap: MutableMap<String, MutableList<BlockItem>> = ConcurrentHashMap()

  /**
   *  查询障碍物信息
   * */
  fun findBlock(blockId: String): MutableList<BlockItem> {
    return blockMap[blockId] ?: return mutableListOf()
  }

  fun findAllBlocks(): MutableMap<String, MutableList<BlockItem>> = blockMap

  /**
   *  添加障碍物信息,多个
   * */
  fun addBlocks(blockId: String, blockItems: MutableList<BlockItem>) {
    if (blockItems.isEmpty()) {
      return
    }
    blockMap.getOrDefault(blockId, mutableListOf()).addAll(blockItems)
  }

  /**
   *  添加障碍物信息
   * */
  fun addBlock(blockId: String, blockItem: BlockItem) {
    blockMap.getOrDefault(blockId, mutableListOf()).add(blockItem)
  }

  /**
   *  更新障碍物信息
   * */
  fun updateBlock(blockId: String, blockItems: MutableList<BlockItem>) {
    if (blockItems.isEmpty()) {
      return removeBlock(blockId)
    }
    blockMap[blockId] = blockItems
  }

  /**
   *  移除障碍物信息
   * */
  fun removeBlock(blockId: String) {
    blockMap.remove(blockId)
  }

  /**
   *  移除障碍物信息
   * */
  fun removeBlockByItemCode(itemCode: String) {
    blockMap.forEach { (_, blockItems) ->
      blockItems.removeIf { it.code == itemCode }
    }
  }
}

data class BlockItem(val code: String, val type: LockType, var point: Point?, val version: String?)