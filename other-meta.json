{"PmProject": {"name": "PmProject", "label": "项目", "group": "PM", "disabled": true, "type": "Entity", "fields": {"name": {"name": "name", "label": "项目名称", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "项目编号", "type": "String", "scale": "Single", "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 2, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 13, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 14, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "projectManager": {"name": "projectManager", "label": "项目经理", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "saleman": {"name": "saleman", "label": "销售", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "deliveryMan": {"name": "deliveryMan", "label": "实施", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "description": {"name": "description", "label": "概述", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"input": "TextArea", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 10, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "consumer": {"name": "consumer", "label": "客户", "type": "Reference", "scale": "Single", "refEntity": "FbCustomer", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "saleOrderNo": {"name": "saleOrderNo", "label": "销售订单号", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "state": {"name": "state", "label": "状态", "type": "String", "scale": "Single", "inlineOptionBill": {"items": [{"value": "NotStart", "label": "未启动"}, {"value": "Working", "label": "进行中"}, {"value": "Paused", "label": "已暂停"}, {"value": "Deliverying", "label": "实施中"}, {"value": "Done", "label": "已完成"}]}, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "fileContract": {"name": "fileContract", "label": "合同", "type": "File", "scale": "Single", "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 15, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "fileTechContract": {"name": "fileTechContract", "label": "技术协议", "type": "File", "scale": "Single", "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 16, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "fileLayoutCad": {"name": "fileLayoutCad", "label": "现场 CAD", "type": "File", "scale": "Single", "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 17, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "otherFiles": {"name": "otherFiles", "label": "其他文件", "type": "File", "scale": "List", "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 18, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"enabled": true, "fixedPrefix": "PM", "flowNoWidth": 4}, "digest": {"fields": ["name"]}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "detailsPageName": "PmProjectDetail", "listStats": {"items": []}, "listCard": {"lines": []}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}}, "PmProjectIssue": {"name": "PmProjectIssue", "label": "项目问题", "group": "PM", "disabled": true, "type": "Entity", "fields": {"title": {"name": "title", "label": "标题", "type": "String", "scale": "Single", "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "block": true, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 2, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 12, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 13, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "images": {"name": "images", "label": "附件图片", "type": "Image", "scale": "List", "decimals": 0, "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 8, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "files": {"name": "files", "label": "附件文件", "type": "File", "scale": "List", "decimals": 0, "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 9, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "description": {"name": "description", "label": "描述", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"input": "TextArea", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 7, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "processedBy": {"name": "processedBy", "label": "处理人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 100, "timestampPrecision": "Second"}}, "project": {"name": "project", "label": "项目", "type": "Reference", "scale": "Single", "refEntity": "PmProject", "inputRequired": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "state": {"name": "state", "label": "状态", "type": "String", "scale": "Single", "inlineOptionBill": {"items": [{"value": "Open", "label": "待解决"}, {"value": "Processing", "label": "处理中"}, {"value": "Resolved", "label": "已解决"}, {"value": "Closed", "label": "已关闭"}, {"value": "Rejected", "label": "已拒绝"}]}, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"enabled": true, "fixedPrefix": "PMW", "flowNoWidth": 4}, "digest": {"fields": ["title"]}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": []}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}}, "PmProjectKeyEvent": {"name": "PmProjectKeyEvent", "label": "项目事件", "group": "PM", "type": "Entity", "fields": {"project": {"name": "project", "label": "项目", "type": "Reference", "scale": "Single", "refEntity": "PmProject", "inputRequired": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "content": {"name": "content", "label": "内容", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 5, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 7, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 8, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 9, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "eventOn": {"name": "eventOn", "label": "发生时间", "type": "Date", "scale": "Single", "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 4}, "digest": {"fields": []}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": []}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}}, "PmProjectLandmark": {"name": "PmProjectLandmark", "label": "项目里程碑", "group": "PM", "type": "Entity", "fields": {"name": {"name": "name", "label": "名称", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "planOn": {"name": "planOn", "label": "计划完成日期", "type": "Date", "scale": "Single", "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "state": {"name": "state", "label": "状态", "type": "String", "scale": "Single", "inlineOptionBill": {"items": [{"value": "NotStart", "label": "未完成"}, {"value": "Progressing", "label": "进行中"}, {"value": "Done", "label": "已完成"}]}, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 200, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "doneOn": {"name": "doneOn", "label": "实际完成日期", "type": "Date", "scale": "Single", "sqlType": "Date", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "project": {"name": "project", "label": "所属项目", "type": "Reference", "scale": "Single", "refEntity": "PmProject", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 5, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 9, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 10, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 11, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 4}, "digest": {"fields": ["name"]}, "scale": "Instances", "sort": "planOn", "userNotice": {"targetUserFields": []}, "actions": {}, "listTableStyleExt": "const r = { rows: {}, cells: {} };\nconst stateColors = {\n    \"NotStart\": \"#FFDB9A\",\n    \"Progressing\": \"#66A9E8\",\n    \"Done\": \"#73CB67\"\n}\nfor (const row of page) {\n    const id = row[\"id\"]\n    const planOn = row[\"planOn\"]\n    const doneOn = row[\"doneOn\"]\n    const timeoutBg = doneOn > planOn ? \"#F9B5B5\" : \"\"\n    r.cells[id] = { state: { style: { background: stateColors[row[\"state\"]], color: \"#fff\"} }, doneOn: { style: { background: timeoutBg } } }\n}\nreturn r;", "listStats": {"items": []}, "listCard": {"lines": []}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}}, "PmProjectTask": {"name": "PmProjectTask", "label": "项目待办事项", "group": "PM", "type": "Entity", "fields": {"project": {"name": "project", "label": "项目", "type": "Reference", "scale": "Single", "refEntity": "PmProject", "inputRequired": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "startedOn": {"name": "startedOn", "label": "开始时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "endedOn": {"name": "endedOn", "label": "结束时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "content": {"name": "content", "label": "内容", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "processedBy": {"name": "processedBy", "label": "处理人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "seqNo": {"name": "seqNo", "label": "顺序号", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 11, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 13, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 14, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 15, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "kind": {"name": "kind", "label": "类型", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "progress": {"name": "progress", "label": "进度", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "state": {"name": "state", "label": "状态", "type": "String", "scale": "Single", "inlineOptionBill": {"items": [{"value": "NotStart", "label": "未开始"}, {"value": "Progressing", "label": "进行中"}, {"value": "Done", "label": "已完成"}]}, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 4}, "digest": {"fields": []}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": []}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}}, "PmWorkOrder": {"name": "PmWorkOrder", "label": "项目工单", "group": "PM", "type": "Entity", "fields": {"title": {"name": "title", "label": "标题", "type": "String", "scale": "Single", "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "block": true, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "woState": {"name": "woState", "label": "工单状态", "type": "String", "scale": "Single", "inlineOptionBill": {"items": [{"value": "Open", "label": "待解决"}, {"value": "Processing", "label": "处理中"}, {"value": "Resolved", "label": "已解决"}, {"value": "Closed", "label": "已关闭"}, {"value": "Rejected", "label": "已拒绝"}]}, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 2, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 12, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 11, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 13, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "images": {"name": "images", "label": "附件图片", "type": "Image", "scale": "List", "decimals": 0, "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 8, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "files": {"name": "files", "label": "附件文件", "type": "File", "scale": "List", "decimals": 0, "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 9, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "description": {"name": "description", "label": "描述", "type": "String", "scale": "Single", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"input": "TextArea", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 7, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "processedBy": {"name": "processedBy", "label": "处理人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 100, "timestampPrecision": "Second"}}, "project": {"name": "project", "label": "项目", "type": "Reference", "scale": "Single", "refEntity": "PmProject", "inputRequired": true, "decimals": 0, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"enabled": true, "fixedPrefix": "PMW", "flowNoWidth": 4}, "digest": {"fields": ["title"]}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": []}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}}, "MfProcess": {"name": "MfProcess", "label": "工序", "group": "MES", "disabled": true, "type": "Entity", "fields": {"name": {"name": "name", "label": "工序名称", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "disabled": {"name": "disabled", "label": "禁用", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 4, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 7, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 8, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "btDisabled": {"name": "btDisabled", "label": "停用", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 4}, "digest": {"fields": ["name"]}, "scale": "Instances", "disabledFilter": true, "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": []}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}}, "MfRouting": {"name": "MfRouting", "label": "工艺路线", "group": "MES", "disabled": true, "type": "Entity", "fields": {"name": {"name": "name", "label": "名称", "type": "String", "scale": "Single", "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "state": {"name": "state", "label": "状态", "type": "String", "scale": "Single", "inlineOptionBill": {"items": [{"value": "Created", "label": "未启用"}, {"value": "Approved", "label": "已启用"}, {"value": "Disabled", "label": "已停用"}]}, "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "sop": {"name": "sop", "label": "作业指导书文件", "type": "File", "scale": "Single", "sqlType": "None", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "编号", "type": "String", "scale": "Single", "inputRequired": true, "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 9, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 10, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "product": {"name": "product", "label": "所属产品", "type": "String", "scale": "Single", "refEntity": "MfProduct", "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 4}, "digest": {"fields": ["name"]}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": []}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}}, "MfRoutingStep": {"name": "MfRoutingStep", "label": "工序图步", "group": "MES", "disabled": true, "type": "Entity", "fields": {"process": {"name": "process", "label": "使用工序", "type": "Reference", "scale": "Single", "refEntity": "MfProcess", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 3, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 6, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 7, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 4}, "digest": {"fields": []}, "scale": "Instances", "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": []}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}}, "MtSerialNoRecord": {"name": "MtSerialNoRecord", "label": "序列号", "group": "MT", "disabled": true, "type": "Entity", "fields": {"code": {"name": "code", "label": "序列号", "type": "String", "scale": "Single", "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 2, "lineColumnWidth": 0, "listTableColumnWidth": 200, "timestampPrecision": "Second"}}, "source": {"name": "source", "label": "来源", "type": "String", "scale": "Single", "inlineOptionBill": {"items": [{"value": "Internal", "label": "内部"}, {"value": "External", "label": "外部"}]}, "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 3, "lineColumnWidth": 0, "listTableColumnWidth": 80, "timestampPrecision": "Second"}}, "id": {"name": "id", "label": "ID", "type": "String", "scale": "Single", "fuzzyFilter": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Hidden", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 11, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdBy": {"name": "created<PERSON>y", "label": "创建人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 12, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedBy": {"name": "modifiedBy", "label": "最后修改人", "type": "Reference", "scale": "Single", "refEntity": "HumanUser", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 14, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "createdOn": {"name": "createdOn", "label": "创建时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 13, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "modifiedOn": {"name": "modifiedOn", "label": "最后修改时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 15, "lineColumnWidth": 0, "listTableDisabled": true, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "ownerOrg": {"name": "ownerOrg", "label": "所属组织", "type": "String", "scale": "Single", "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "<PERSON><PERSON><PERSON>", "displayOrder": 4, "lineColumnWidth": 0, "listTableColumnWidth": 150, "timestampPrecision": "Second"}}, "state": {"name": "state", "label": "状态", "type": "String", "scale": "Single", "inlineOptionBill": {"items": [{"value": "Init", "label": "未使用"}, {"value": "Active", "label": "正在关联"}, {"value": "Used", "label": "停止关联"}, {"value": "Aborted", "label": "作废"}]}, "inputRequired": true, "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"input": "Select", "read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 5, "lineColumnWidth": 0, "listTableColumnWidth": 80, "timestampPrecision": "Second"}}, "material": {"name": "material", "label": "关联物料", "type": "Reference", "scale": "Single", "refEntity": "FbMaterial", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 30, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 6, "lineColumnWidth": 0, "listTableColumnWidth": 200, "timestampPrecision": "Second"}}, "bindOn": {"name": "bindOn", "label": "关联开始时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 7, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "unbindOn": {"name": "unbindOn", "label": "解除关联时间", "type": "DateTime", "scale": "Single", "sqlType": "DateTime", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 8, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "remark": {"name": "remark", "label": "备注", "type": "String", "scale": "Single", "sqlType": "<PERSON><PERSON><PERSON><PERSON>", "length": 50, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 9, "lineColumnWidth": 0, "listTableColumnWidth": 200, "timestampPrecision": "Second"}}, "btDisabled": {"name": "btDisabled", "label": "停用", "type": "Boolean", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "<PERSON><PERSON><PERSON>", "create": "Editable", "update": "Editable", "displayOrder": 10, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}, "version": {"name": "version", "label": "修订版本", "type": "Int", "scale": "Single", "sqlType": "Int", "length": 0, "numWidth": 0, "numScale": 0, "view": {"read": "Hidden", "create": "Hidden", "update": "Hidden", "displayOrder": 1, "lineColumnWidth": 0, "listTableColumnWidth": 0, "timestampPrecision": "Second"}}}, "indexes": [], "idGen": {"flowNoWidth": 4}, "digest": {"fields": ["code"]}, "scale": "Instances", "disabledFilter": true, "userNotice": {"targetUserFields": []}, "actions": {}, "listStats": {"items": []}, "listCard": {"lines": []}, "orderConfig": {"kinds": [], "states": [], "pushOrderStates": [], "pushOrders": [], "thisQtyFields": [], "orderStatesToCreateInv": [], "outboundInvStates": []}, "pagesButtons": {"ListMain": {"buttons": []}, "ListItem": {"buttons": []}, "View": {"buttons": []}, "Create": {"buttons": []}, "Edit": {"buttons": []}}}}