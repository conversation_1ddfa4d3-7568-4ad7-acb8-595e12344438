# 项目结构

`trick-device-io` 与设备连接的底层实现：

- 仙工机器人：RBK 协议客户端

`trick-gw` 多协议网关。 独立应用。

一般与仙工机器人安装在同一子网内。将 RBK 协议转换为更易用的形式。如通过 HTTP 协议调用 RBK 协议。

解决机器人在内网，服务器在外网的问题：网关通过 WebSocket 客户端连接到服务器。

`trick-seer-connector` 统一与仙工机器人的通讯方式。统一接口，不必管底层实现。底层可以选择：

- 通过 RBK 协议直接连接到机器人。要求调用者与机器人在同一网络。
- 机器人与 `trick-gw` 连接，`trick-gw` 通过 WebSocket 与 `trick-seer-connector` 连接。

# 开发笔记 - 姚远

Java 代码里，将 Exception 包裹到 RuntimeException 其实不好，把错误内藏了一层。

Javalin 用 Jetty，Spring Boot 默认用 Tomcat，改成用 Jetty

WebSocket 区分身份，比如有些事件只发给浏览器。

Seabird 移植
增加了 MongoDB 的支持
不绑定 Javalin
增加多租户支持

MongoDB driver sync 与 log 有兼容性问题

## 待办

- 引用，可选的筛选
- 企业微信登录
- 企业微信登录，服务分企业
- 图片上传下载：文件大小
- U8 脚本
- WebSocket 登录和企业边界
- 403 前端报错
- 国际化
- 授权
- 集团化管理
- 供应链

---

- SQL：不同租户不同表结构
- SQL 实现多租户：查询加条件，表加 RO 列
- 增删改查 version 对不对
- 实体增删改后的通知，让列表刷新
- 出库单复制不了
- 盘点、库存调整
- 菜单机制
- SQL：唯一性约束和报错

## 国际化

### 消息国际化

产生一个报错，虽然当前请求的语言是 A，但是这个报错要被记录，被其他语言看。

后端给前端的消息，国际化在前端做？

语言如何一层层传下来的问题

异常产生时用 KEY 和参数，在 Web 层/日志层国际化，这样不必把语言层层传下去

非异常类给前端的消息有哪些？

### 数据国际化

业务对象的字段的国际化。业务对象可能有多个需要国际化的字段，如名称、规格。

需要国际化的字段。切语言标签。