// 在 trick-server-k 目录运行 node util/clean-stats-config.js

const fs = require('fs')
const path = require('path')

const configFilePath = path.join("trick-m4", "src", "main", "resources", "stats-config.json")

function clean() {
  if (!fs.existsSync(configFilePath)) {
    console.log("stats-config.json 不存在")
  }

  const configFileStr = fs.readFileSync(configFilePath, {encoding: "utf-8"})
  const configList = JSON.parse(configFileStr)

  for (let config of configList) {
    cleanObj(config)
  }

  console.log("after clean")
  console.log(configList)

  fs.writeFileSync(configFilePath, JSON.stringify(configList, null, 2), {encoding: "utf-8"})
}


function cleanObj(o) {
  if (Array.isArray(o)) {
    for (const item of o) cleanObj(item)
  } else if (typeof o === "object") {
    for (const key of Object.keys(o)) {
      const value = o[key]
      if (value === null || value === false || value === "") delete o[key]
      cleanObj(o[key])
    }
  }
}

clean()