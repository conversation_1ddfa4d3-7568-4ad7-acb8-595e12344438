/**
 * 将 ui 下 .json 中存在，.txt 中不存在的字典，复制到 .txt 中
 */

const fs = require("fs");

function readTxtDict(path) {
  const lines = fs.readFileSync(path, {encoding: "utf-8"}).split("\n").filter(line => {
    return line.trim().length > 0 && !line.trimStart().startsWith("#")
  })
  const txtDict = {}
  for (const line of lines) {
    if (line.trim().length <= 0 || line.trimStart().startsWith("#") || line.trimStart().startsWith("[")) {

    } else {
      const arr = line.split("=")
      txtDict[arr[0]] = arr[1] // 这里性能差，但看的清楚
    }
  }
  return {txtDict, lines}
}

function readJsonDict(path) {
  return JSON.parse(fs.readFileSync(path, {encoding: "utf-8"}))
}


function execute() {
  const {txtDict, lines} = readTxtDict("../trick-ui/f2-ui/i18n/ui-base-zh.txt")
  const jsonDict = readJsonDict(`../trick-ui/f2-ui/src/i18n/zh.json`)

  for (const key in jsonDict) {
    if (!txtDict[key]) {
      console.log(key)
      // txtDict[key] = jsonDict[key]
      lines.push(`${key}=${jsonDict[key]}`)
    }
  }
  // fs.writeFileSync("../trick-ui/f2-ui/i18n/ui-base-zh.txt", Object.entries(txtDict).map(([key, value]) => `${key}=${value}`).join("\n"))
  fs.writeFileSync("../trick-ui/f2-ui/i18n/ui-base-zh.txt", lines.join("\n"), {encoding: "utf-8"})
}


execute()