const lark = require('@larksuiteoapi/node-sdk');
const fs = require("fs");
const path = require("path");

/////////////////// 飞书翻译组件 start //////////////////////////////////

// app id, app secret 为自己应用的 appId, appSecret
const client = new lark.Client({
  appId: 'cli_a6ab76c54fbe500c',
  appSecret: 'OS3AfsODz2aKn1mubiAhWcnBHEHV0yRP',
})

function translate(sl, tl, index, key, text) {
  return client.translation.text.translate({
      data: {
        source_language: sl,
        text: text,
        target_language: tl,
        glossary: [{ // 术语表
          from: '仙工',
          to: 'Seer',
        }, {
          from: '海柔',
          to: 'Hai',
        }, {
          from: '海康',
          to: 'Hik',
        }],
      },
    },
    lark.withTenantToken("")
  ).then(async res => {
    await sleep(100)
    return {key: key, tl: tl, index: index, value: res.data.text}
  })
}

// 此次要翻译的语言
// const langKeys = ["zh_tc", "en", "jp", "ru", "de", "fr", "it", "pl", "th", "hi", "id", "es", "pt", "ko", "vi"]
const langKeys = ["zh_tc", "en", "jp"]

// 字典： M4 语言 - 飞书语言
const langMap = {
  // "zh": "zh", // 简体中文
  "zh_tc": "zh-Hant", // 繁体中文
  "en": "en", // 英文
  "jp": "ja", // 日语,
  "ru": "ru", // 俄语,
  "de": "de", // 德语,
  "fr": "fr", // 法语,
  "it": "it", // 意大利语,
  "pl": "pl", // 波兰语,
  "th": "th", // 泰语,
  "hi": "hi", // 印地语,
  "id": "id", // 印尼语,
  "es": "es", // 西班牙语,
  "pt": "pt", // 葡萄牙语,
  "ko": "ko", // 朝鲜语,
  "vi": "vi", // 越南语
}

// 飞书限制 20 QPS，实测下来每次发 5 个比较稳
const poolSize = 5

// 用于控制并发数
class ConcurrencyPromisePool {
  constructor(limit) {
    this.limit = limit;
    this.runningNum = 0;
    this.queue = [];
    this.results = [];
  }

  all(promises = []) {
    return new Promise((resolve, reject) => {
      for (const promise of promises) {
        this._run(promise, resolve, reject);
      }
    });
  }

  _run(promise, resolve, reject) {
    if (this.runningNum >= this.limit) {
      console.log('>>> 达到上限，入队：', promise);
      this.queue.push(promise);
      return;
    }

    ++this.runningNum;
    promise()
      .then(res => {
        console.log(">>> 完成任务：", res);
        this.results.push(res);
        --this.runningNum;

        if (this.queue.length === 0 && this.runningNum === 0) {
          return resolve(this.results);
        }
        if (this.queue.length) {
          this._run(this.queue.shift(), resolve, reject);
        }
      })
      .catch(reject);
  }
}

/////////////////// 飞书翻译组件 end //////////////////////////////////

function getKeyValue(line) {
  const firstIndex = line.indexOf("=")
  const k = line.substring(0, firstIndex).trimEnd() // key
  const v = line.substring(firstIndex + 1).trimStart() // value
  return {k, v}
}

/**
 * 读行模式的字典，去掉 # [xxx] 空行
 * @param path
 */
function readPureLines(path) {
  const lines = fs.readFileSync(path, {encoding: "utf-8"}).split("\n")
  const map = {}
  for (const line of lines) {
    if (line.trim().length <= 0 || line.trimStart().startsWith("#") || line.trimStart().startsWith("[")) continue
    const {k, v} = getKeyValue(line)
    map[k] = v // 这里性能差，但看的清楚
  }
  return map
}

/**
 * 读带分组的字典，保留 [xxx]
 * @param path
 * @returns {{groups: *[], map: {}}}
 *    groups: ["base", "fleet.falcon", "stats"]
 *    map: {base: { k1: v1, k2: v2}, fleet.falcon: {}, stats: {}}
 */
function readGroupLines(path) {
  const lines = fs.readFileSync(path, {encoding: "utf-8"}).split("\n").filter(line => {
    return line.trim().length > 0 && !line.trimStart().startsWith("#")
  })

  // 所有类型的字典 {base: { k1: v1, k2: v2}, fleet.falcon: {}, jp: {}}
  const groups = []
  const map = {}
  let key = ""
  for (const l of lines) {
    const line = l.trim()
    if (!map[key] && key !== "") {
      map[key] = {}
    }

    if (line.trimStart().startsWith("[")) {
      key = line.trimStart().substring(1, line.length - 1)
      groups.push(key)
    } else {
      const {k, v} = getKeyValue(line)
      map[key][k] = v // 这里性能差，但看的清楚
    }
  }
  return {groups, map}
}


function cleanSourceFile(path) {
  const {groups, map} = readGroupLines(path)

  const executedKeys = [] // 用来去重
  const result = []
  for (const g of groups) {
    result.push(`\n[${g}]`)

    // 对 map[k0] 按照 key 排序
    const keys = Object.keys(map[g])
    keys.sort()

    keys.forEach(key => {
      if (!executedKeys.includes(key)) { // 去重
        result.push(`${key}=${map[g][key]}`)
        executedKeys.push(key)
      }
    })
  }

  fs.writeFileSync(path, result.join("\n"), {encoding: "utf-8"})
}

function cleanFixFile(sourcePath, target) {
  const {groups, map} = readGroupLines(sourcePath)

  for (const lang of langKeys) {
    const fixMap = readPureLines(`${target}${lang}.txt`)
    const result = []
    for (const g of groups) {

      // 对 map[k0] 按照 key 排序
      const keys = Object.keys(map[g])
      keys.sort()

      keys.forEach(key => {
        if (fixMap[key]) {
          result.push(`${key}=${fixMap[key]}`)
        }
      })
    }
    fs.writeFileSync(`${target}${lang}.txt`, result.join("\n"), {encoding: "utf-8"})
  }
}

async function translateServerBase() {
  const st = new Date().getTime()
  // 清理原始字典表中的空行
  cleanSourceFile("trick-m4/src/main/resources/server-base-zh.txt")
  cleanFixFile("trick-m4/src/main/resources/server-base-zh.txt", "trick-m4/src/main/resources/server-fix-")

  // 已翻译的字典
  const existTranslatedMap = {} // 所有已翻译的字典 {zh_tc: { k1: v1, k2: v2}, en: {}, jp: {}}
  for (const langM4 of langKeys) {
    if (!fs.existsSync(`trick-m4/src/main/resources/server-base-${langM4}.txt`)) continue
    const translatedStr = fs.readFileSync(`trick-m4/src/main/resources/server-base-${langM4}.txt`, {encoding: "utf-8"})
    const translatedLines = translatedStr.split("\n")
    const map0 = {} // langM4 语言下，已翻译的字典 { k1: v1, k2: v2}
    for (const line of translatedLines) {
      if (line.startsWith("#") || line.trim().length === 0 || (line.startsWith('[') && line.trim().endsWith(']'))) {
        continue
      }
      const firstIndex = line.indexOf("=")
      const k = line.substring(0, firstIndex).trimEnd() // key
      const tv = line.substring(firstIndex + 1).trimStart() // 已翻译的值
      console.log(`已翻译的字典：key=${k} tv=${tv}`)
      map0[k] = tv
    }
    existTranslatedMap[langM4] = map0
  }

  // 加到 promises 里，不立即执行
  const promises = []
  const sourceStr = fs.readFileSync("trick-m4/src/main/resources/server-base-zh.txt", {encoding: "utf-8"})
  const sourceLines = sourceStr.split("\n")
  for (const langM4 of langKeys) { // M4 语言类型
    const langLark = langMap[langM4] // 飞书语言类型
    if (!langLark) {
      console.log(`>>> 未找到 ${langM4} 对应的翻译语言`)
      continue
    }

    for (let i = 0; i < sourceLines.length; ++i) {
      const line = sourceLines[i]
      if (line.startsWith("#") || line.trim().length === 0 || (line.startsWith('[') && line.trim().endsWith(']'))) {
        continue
      }

      const firstIndex = line.indexOf("=")
      const k = line.substring(0, firstIndex).trimEnd() // key
      const sv = line.substring(firstIndex + 1).trimStart() // 中文值

      if (existTranslatedMap[langM4] && existTranslatedMap[langM4][k]) {
        console.log(`已翻译过的字典，跳过调用飞书翻译，key=${k}，sv=${sv}，tv=${existTranslatedMap[langM4][k]}`)
      } else
        promises.push(() => translate("zh", langLark, i, k, sv))
    }
  }

  // 执行 promises
  // 防止一行也不调用飞书翻译时，不生成的问题
  promises.push(() => sleep(100).then(async () => {
    return {key: null, tl: null, index: null, value: null}
  }))
  console.log(`promises.length = ${promises.length}`)
  const pool = new ConcurrencyPromisePool(poolSize)
  pool.all(promises).then(() => {
    console.table(pool.results)
    const ct = new Date().getTime()
    console.log(`转换完成，耗时：${ct - st} ms`)
  }).then(() => {
    for (const langM4 of langKeys) { // M4 语音类型
      const langLark = langMap[langM4] // 飞书语言类型

      const translatedMap = {}
      pool.results.filter(it => it.tl === langLark).map((it) => {
        translatedMap[it.key] = it.value
      })

      console.log(`${langM4} translatedMap = ${JSON.stringify(translatedMap)}`)

      const resultList = []
      for (const line of sourceLines) {
        if (line.startsWith("#") || line.trim().length === 0 || (line.startsWith('[') && line.trim().endsWith(']'))) {
          resultList.push(line)
          continue
        }

        const firstIndex = line.indexOf("=")
        const k = line.substring(0, firstIndex).trimEnd() // key
        // const sv = line.substring(firstIndex + 1).trimStart() // 中文值
        const tv = translatedMap[k] // 目标语言的值

        if (tv)
          resultList.push(`${k}=${tv}`)
        else // 没翻译的话，把 existTranslatedMap 的值放进去
          resultList.push(`${k}=${existTranslatedMap[langM4][k]}`)
      }

      // console.table(resultList)
      const resultStr = resultList.join("\n")
      fs.writeFileSync(`trick-m4/src/main/resources/server-base-${langM4}.txt`, resultStr, {encoding: "utf-8"})
    }

    const ct = new Date().getTime()
    console.log(`生成 .text 完成，耗时：${ct - st} ms`)
    console.log("===================== end ==============")
  })

}

async function translateUiBase() {
  const st = new Date().getTime()

  let feDir = "m4frontend" // 新的 git 仓库名
  if (!fs.existsSync(path.join("..", feDir))) {
    feDir = "trick-ui" // 老的 git 仓库名
  }

  // 清理原始字典表中的空行
  cleanSourceFile(path.join("..", feDir, "project", "m4-ui-lib", "i18n", `ui-base-zh.txt`))
  cleanFixFile(path.join("..", feDir, "project", "m4-ui-lib", "i18n", `ui-base-zh.txt`),
    path.join("..", feDir, "project", "m4-ui-lib", "i18n", `ui-base-`))

  const metaStr = fs.readFileSync(path.join("..", feDir, "project", "m4-ui-lib", "i18n", `ui-base-zh.txt`), {encoding: "utf-8"})
  const lines = metaStr.split("\n")

  // 已翻译的字典
  const existTranslatedMap = {}
  for (const langM4 of langKeys) {
    if (!fs.existsSync(path.join("..", feDir, "project", "m4-ui-lib", "i18n", `ui-base-${langM4}.txt`))) continue
    const translatedStr = fs.readFileSync(path.join("..", feDir, "project", "m4-ui-lib", "i18n", `ui-base-${langM4}.txt`), {encoding: "utf-8"})
    const translatedLines = translatedStr.split("\n")
    const tt = {}
    for (const line of translatedLines) {
      if (line.startsWith("#") || line.trim().length === 0 || (line.startsWith('[') && line.trim().endsWith(']'))) {
        continue
      }
      const firstIndex = line.indexOf("=")
      const k = line.substring(0, firstIndex).trimEnd() // key
      const tv = line.substring(firstIndex + 1).trimStart() // 已翻译的值
      console.log(`已翻译的字典：key=${k} tv=${tv}`)
      tt[k] = tv
    }
    existTranslatedMap[langM4] = tt
  }
  // console.log(`existTranslatedMap = ${JSON.stringify(existTranslatedMap)}`)
  // console.table(existTranslatedMap)

  const promises = []

  // 加到 promises 里，不立即执行
  for (let i = 0; i < langKeys.length; i++) {
    const langM4 = langKeys[i] // M4 语言类型
    const langLark = langMap[langKeys[i]] // 飞书语言类型
    if (!langLark) {
      console.log(`>>> 未找到 ${langM4} 对应的翻译语言`)
      continue
    }

    // for (let i = 0; i < 30; ++i) {
    for (let i = 0; i < lines.length; ++i) {
      const line = lines[i]
      if (line.startsWith("#") || line.trim().length === 0 || (line.startsWith('[') && line.trim().endsWith(']'))) {
        continue
      }

      const firstIndex = line.indexOf("=")
      const k = line.substring(0, firstIndex).trimEnd() // key
      const sv = line.substring(firstIndex + 1).trimStart() // 中文值

      if (existTranslatedMap[langM4] && existTranslatedMap[langM4][k]) {
        console.log(`已翻译过的字典，跳过调用飞书翻译，key=${k}，sv=${sv}，tv=${existTranslatedMap[langM4][k]}`)
      } else
        promises.push(() => translate("zh", langLark, i, k, sv))
    }
  }

  // 执行 promises
  // 防止一行也不调用飞书翻译时，不生成的问题
  promises.push(() => sleep(100).then(async res => {
    return {key: null, tl: null, index: null, value: null}
  }))
  console.log(`promises.length = ${promises.length}`)
  const pool = new ConcurrencyPromisePool(poolSize)
  pool.all(promises).then(() => {
    console.table(pool.results)
    const ct = new Date().getTime()
    console.log(`调用飞书完成，耗时：${ct - st} ms`)
  }).then(() => {
    for (const langM4 of langKeys) { // M4 语音类型
      const langLark = langMap[langM4] // 飞书语言类型

      const translatedMap = {}
      pool.results.filter(it => it.tl === langLark).map((it) => {
        translatedMap[it.key] = it.value
      })

      console.log(`${langM4} translatedMap = ${JSON.stringify(translatedMap)}`)

      const resultList = []
      for (const line of lines) {
        if (line.startsWith("#") || line.trim().length === 0 || (line.startsWith('[') && line.trim().endsWith(']'))) {
          resultList.push(line)
          continue
        }

        const firstIndex = line.indexOf("=")
        const k = line.substring(0, firstIndex).trimEnd() // key
        const sv = line.substring(firstIndex + 1).trimStart() // 中文值
        const tv = translatedMap[k] // 目标语言的值

        if (tv)
          resultList.push(`${k}=${tv}`)
        else // 没翻译的话，把 existTranslatedMap 的值放进去
          resultList.push(`${k}=${existTranslatedMap[langM4][k]}`)
      }

      // console.table(resultList)
      const resultStr = resultList.join("\n")
      fs.writeFileSync(path.join("..", feDir, "project", "m4-ui-lib", "i18n", `ui-base-${langM4}.txt`), resultStr, {encoding: "utf-8"})
    }

    const ct = new Date().getTime()
    console.log(`生成 .text 完成，耗时：${ct - st} ms`)
  }).then(() => {
    // ui-base-xx.txt + ui-fix-xx.txt + xx.json ---> xx.json
    langKeys.push("zh")
    for (const langM4 of langKeys) {
      const pair = {}

      const jsonFilePath = path.join("..", feDir, "project", "m4-ui-lib", "src", "i18n", `${langM4}.json`)
      if (fs.existsSync(jsonFilePath)) {
        const jsonText = fs.readFileSync(jsonFilePath, {encoding: "utf-8"})
        const jsonData = JSON.parse(jsonText)

        for (const k in jsonData) {
          const v = jsonData[k]
          console.log(`in exist json file k=${k} v=${v}`)
          pair[k] = v
        }
      }

      const basePair = {}
      const file = path.join("..", feDir, "project", "m4-ui-lib", "i18n", `ui-base-${langM4}.txt`)
      const fileTxt = fs.readFileSync(file, {encoding: "utf-8"})
      const lines = fileTxt.split("\n")
      // console.log(lines.length)
      for (const line of lines) {
        const line2 = line.trim()
        if (!line2) continue
        const sIndex = line2.indexOf("=")
        if (sIndex <= 0) continue
        const key = line2.substring(0, sIndex).trim()
        const value = line2.substring(sIndex + 1).trim()
        if (!key) continue
        pair[key] = value
        basePair[key] = value
      }

      const fixNewLines = []
      const fixFile = path.join("..", feDir, "project", "m4-ui-lib", "i18n", `ui-fix-${langM4}.txt`)
      if (fs.existsSync(fixFile)) {
        const fixFileTxt = fs.readFileSync(fixFile, {encoding: "utf-8"})
        const fixLines = fixFileTxt.split("\n")
        for (const line of fixLines) {
          const line2 = line.trim()
          if (!line2) {
            fixNewLines.push(line2)
            continue
          }
          const sIndex = line2.indexOf("=")
          if (sIndex <= 0) {
            fixNewLines.push(line2)
            continue
          }
          const key = line2.substring(0, sIndex).trim()
          const value = line2.substring(sIndex + 1).trim()
          if (!key) continue

          const ov = basePair[key]
          if (ov !== value) {
            pair[key] = value
            fixNewLines.push(line2)
          }
        }
        const fixNewLinesStr = fixNewLines.join("\n")
        fs.writeFileSync(path.join("..", feDir, "project", "m4-ui-lib", "i18n", `ui-fix-${langM4}.txt`), fixNewLinesStr, {encoding: "utf-8"})
      }

      fs.writeFileSync(path.join("..", feDir, "project", "m4-ui-lib", "src", "i18n", `${langM4}.json`), JSON.stringify(pair, null, 2), {encoding: "utf-8"})
    }

    const ct = new Date().getTime()
    console.log(`生成 .json 完成，耗时：${ct - st} ms`)
    console.log("===================== end ==============")
  })

}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

const args = process.argv[2]
console.log("args: " + args)
if (args === "0" || args === "server")
  // 后端国际化
  translateServerBase()
else if (args === "1" || args === "ui")
  // 前端国际化
  translateUiBase()
else
  console.log("需要指定国际化前端还是后端的字典：0 后端，1 前端")
