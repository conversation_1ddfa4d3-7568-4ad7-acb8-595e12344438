const fs = require('fs')
const path = require("path")

const entityFilePath = path.join("trick-m4", "src", "main", "resources", "entities.json")
const dictFilePath = path.join("trick-m4", "src", "main", "resources", "server-base-zh.txt")

function cleanExistI18nEntitiesKey() {
    if (fs.existsSync(dictFilePath)) {
        const dictFile = fs.readFileSync(dictFilePath, {encoding: "utf-8"})
        const existTranslatedLines = dictFile.split("\n")
        let r = []
        for (const line of existTranslatedLines) {
            if (line.startsWith("i18n_entity.")) {
                continue
            }
            r.push(line)
        }
        fs.writeFileSync(dictFilePath, r.join("\n"), {encoding: "utf-8"})
    }
}

function parseExistDict() {
    const existTranslatedMap = {}
    let existTranslatedLines = []
    if (fs.existsSync(dictFilePath)) {
        const dictFile = fs.readFileSync(dictFilePath, {encoding: "utf-8"})
        existTranslatedLines = dictFile.split("\n")
    }
    for (const line of existTranslatedLines) {
        if (line.startsWith("#") || line.trim().length === 0) {
            continue
        }
        const firstIndex = line.indexOf("=")
        const k = line.substring(0, firstIndex).trimEnd() // key
        const tv = line.substring(firstIndex + 1).trimStart() // 已翻译的值
        // console.log(`已翻译的字典：key=${k} tv=${tv}`)
        existTranslatedMap[k] = tv
    }
    return {existTranslatedMap, existTranslatedLines}
}

function ensureNotExtracted(str) {
    if (str && str.startsWith("i18n_entity."))
        // {}
        throw new Error(`${str} 已被提取过 key，请重新生成 entities.json`)
}

function extract() {
    if (fs.existsSync(entityFilePath)) {
        console.log(`entities.json 不存在！！！`)
    }

    const args1 = process.argv[2]
    console.log("args1: " + args1)
    if (args1 === "init") cleanExistI18nEntitiesKey()

    // 解析 dictFilePath 中先有的字典
    let {existTranslatedMap, existTranslatedLines} = parseExistDict()
    // console.log(`existTranslatedMap = `)
    // console.table(existTranslatedMap)
    // console.table(existTranslatedLines)
    existTranslatedLines.push("\n#")

    // 取 meta 里所有的需转换的 key
    const entityFile = fs.readFileSync(entityFilePath, {encoding: "utf-8"})
    const entities = JSON.parse(entityFile)
    // console.table(entities)

    for (const entity of entities) {
        const entityName = entity["name"]
        // >> normal
        {
            // label
            const label = entity["label"]
            if (args1 === "init") ensureNotExtracted(label)
            const k = `i18n_entity.${entityName}.label`
            console.log(`${k} = ${label}`)
            if (!existTranslatedMap[k]) {
                existTranslatedLines.push(`${k}=${label}`)
                entity["label"] = k
            }
        }

        {
            // group
            const group = entity["group"]
            if (args1 === "init") ensureNotExtracted(group)
            const k = `i18n_entity.${entityName}.group`
            console.log(`${k} = ${group}`)
            if (!existTranslatedMap[k]) {
                existTranslatedLines.push(`${k}=${group}`)
                entity["group"] = k
            }
        }

        {
            // digest.formatJs ???
            // const digest = entity["digest"]
            // if (digest) {
            //     const formatJs = digest["formatJs"]
            //     if (formatJs) {
            //         const k = `i18n_entity.${entityName}.digest.formatJs`
            //         if (!existTranslatedMap[k]) {
            //             existTranslatedLines.push(`${k}=${formatJs}`)
            //             digest["formatJs"] = k
            //         }
            //     }
            // }
        }

        {
            //  listStats.items[i].label
            // listStats.items[i].scriptStr ???
            const listStats = entity["listStats"]
            if (listStats) {
                const items = listStats["items"]
                // for (const item of items) {
                for (let i = 0; i < items.length; i++) {
                    const item = items[i]
                    const label = item["label"]
                    if (args1 === "init") ensureNotExtracted(label)
                    if (label) {
                        const k = `i18n_entity.${entityName}.listStats.items[${i}].label`
                        if (!existTranslatedMap[k]) {
                            existTranslatedLines.push(`${k}=${label}`)
                            item["label"] = k
                        }
                    }
                    const scriptStr = item["scriptStr"]
                    if (args1 === "init") ensureNotExtracted(scriptStr)
                    if (scriptStr) {
                        const k = `i18n_entity.${entityName}.listStats.items[${i}].scriptStr`
                        if (!existTranslatedMap[k]) {
                            existTranslatedLines.push(`${k}=${scriptStr}`)
                            item["scriptStr"] = k
                        }
                    }
                }
            }
        }

        {
            // pagesButtons[k].buttons[i].label
            const pagesButtons = entity["pagesButtons"]
            for (const bt in pagesButtons) {
                const buttons = pagesButtons[bt]["buttons"]
                for (let i = 0; i < buttons.length; i++) {
                    const button = buttons[i]
                    const label = button["label"]
                    if (args1 === "init") ensureNotExtracted(label)
                    const k = `i18n_entity.${entityName}.pagesButtons.${bt}.buttons[${i}].label`
                    if (!existTranslatedMap[k]) {
                        existTranslatedLines.push(`${k}=${label}`)
                        button["label"] = k
                    }
                }
            }
        }

        // TODO indexes[i].name
        // TODO codeParse.rules[i].regex ???
        // TODO codeParse.rules[i].mapFields[i].demoText ???
        // TODO orderConfig.pushOrderStates[i] ???

        {
            // orderConfig.kinds[i].label
            // orderConfig.states[i].label
            // orderConfig.states[i].nextStates[i].buttonLabel
            const orderConfig = entity["orderConfig"]
            if (orderConfig) {
                // const kinds = orderConfig["kinds"]
                // if (kinds) {
                //     for (let i = 0; i < kinds.length; i++) {
                //         const kind = kinds[i]
                //         const l = kind["label"]
                //         if (args1 === "init") ensureNotExtracted(l)
                //         const name = kind["name"]
                //         const k = `i18n_entity.${entityName}.orderConfig.kinds.${name}.label`
                //         if (!existTranslatedMap[k]) {
                //             existTranslatedLines.push(`${k}=${l}`)
                //             kind["label"] = k
                //         }
                //     }
                // }

                // const states = orderConfig["states"]
                // if (states) {
                //     for (let i = 0; i < states.length; i++) {
                //         const state = states[i]
                //         const l = state["label"]
                //         if (args1 === "init") ensureNotExtracted(l)
                //         const id = state["id"]
                //         const k = `i18n_entity.${entityName}.orderConfig.states.${id}.label`
                //         if (!existTranslatedMap[k]) {
                //             existTranslatedLines.push(`${k}=${l}`)
                //             state["label"] = k
                //         }
                //
                //         const nextStates = state["nextStates"]
                //         if (nextStates) {
                //             for (let j = 0; j < nextStates.length; j++) {
                //                 const nextState = nextStates[j]
                //                 const l = nextState["buttonLabel"]
                //                 if (args1 === "init") ensureNotExtracted(l)
                //                 const nextId = nextState["id"]
                //                 const k = `i18n_entity.${entityName}.orderConfig.states.${id}.nextStates.${nextId}.buttonLabel`
                //                 if (!existTranslatedMap[k]) {
                //                     existTranslatedLines.push(`${k}=${l}`)
                //                     nextState["buttonLabel"] = k
                //                 }
                //             }
                //         }
                //     }
                // }
            }
        }

        // kinds.kinds[k].label
        {
            const kinds = entity["kinds"]
            if (kinds) {
                for (const k1 in kinds["kinds"]) {
                    const kind = kinds["kinds"][k1]
                    const l = kind["label"]
                    if (args1 === "init") ensureNotExtracted(l)
                    const k = `i18n_entity.${entityName}.kinds.kinds.${k1}.label`
                    if (!existTranslatedMap[k]) {
                        existTranslatedLines.push(`${k}=${l}`)
                        kind["label"] = k
                    }
                }
            }
        }

        // states.states[k].label
        // states.states[k].nextStates
        {
            const states = entity["states"]
            if (states) {
                for (const k1 in states["states"]) {
                    const state = states["states"][k1]
                    const l = state["label"]
                    if (args1 === "init") ensureNotExtracted(l)
                    const k = `i18n_entity.${entityName}.states.states.${k1}.label`
                    if (!existTranslatedMap[k]) {
                        existTranslatedLines.push(`${k}=${l}`)
                        state["label"] = k
                    }

                    const nextStates = state["nextStates"]
                    for (const nextState of nextStates) {
                        const nId = nextState["id"]
                        const l = nextState["buttonLabel"]
                        if (args1 === "init") ensureNotExtracted(l)
                        const k = `i18n_entity.${entityName}.states.states.${k1}.nextStates.${nId}.buttonLabel`
                        if (!existTranslatedMap[k]) {
                            existTranslatedLines.push(`${k}=${l}`)
                            nextState["buttonLabel"] = k
                        }
                    }
                }
            }
        }

        {
            // listCard.lines[i][i].prefix
            // listCard.lines[i][i].suffix
            // listCard.lines[i][i].formatMapping[i].replaceText
            const lines = entity["listCard"]["lines"]
            if (lines) {
                for (let i = 0; i < lines.length; i++) {
                    const l1 = lines[i]
                    for (let j = 0; j < l1.length; j++) {
                        const listCardLinePart = l1[j]

                        if (listCardLinePart) {
                            const fieldName = listCardLinePart["fieldName"]

                            const prefix = listCardLinePart["prefix"]
                            if (args1 === "init") ensureNotExtracted(prefix)
                            if (prefix) {
                                const k = `i18n_entity.${entityName}.listCard.${fieldName}.prefix`
                                if (!existTranslatedMap[k]) {
                                    existTranslatedLines.push(`${k}=${prefix}`)
                                    listCardLinePart["prefix"] = k
                                }
                            }
                            const suffix = listCardLinePart["suffix"]
                            if (args1 === "init") ensureNotExtracted(suffix)
                            if (suffix) {
                                const k = `i18n_entity.${entityName}.listCard.${fieldName}.suffix`
                                if (!existTranslatedMap[k]) {
                                    existTranslatedLines.push(`${k}=${suffix}`)
                                    listCardLinePart["suffix"] = k
                                }
                            }
                            const formatMapping = listCardLinePart["formatMapping"]
                            if (formatMapping) {
                                for (let m = 0; m < formatMapping.length; m++) {
                                    const formatMappingItem = formatMapping[m]
                                    if (formatMappingItem) {
                                        const replaceText = formatMappingItem["replaceText"]
                                        if (args1 === "init") ensureNotExtracted(replaceText)
                                        if (replaceText) {
                                            const k = `i18n_entity.${entityName}.listCard.${fieldName}.formatMapping[${m}].replaceText`
                                            if (!existTranslatedMap[k]) {
                                                existTranslatedLines.push(`${k}=${replaceText}`)
                                                formatMappingItem["replaceText"] = k
                                            }
                                        }
                                    }
                                }
                            }

                            // if (listCardLinePart) {
                            //     const prefix = listCardLinePart["prefix"]
                            //     if (prefix) {
                            //         const k = `i18n_entity.${entityName}.listCard[${i}][${j}].prefix`
                            //         if (!existTranslatedMap[k]) {
                            //             existTranslatedLines.push(`${k}=${prefix}`)
                            //             listCardLinePart["prefix"] = k
                            //         }
                            //     }
                            //
                            //     const suffix = listCardLinePart["suffix"]
                            //     if (suffix) {
                            //         const k = `i18n_entity.${entityName}.listCard[${i}][${j}].suffix`
                            //         if (!existTranslatedMap[k]) {
                            //             existTranslatedLines.push(`${k}=${suffix}`)
                            //             listCardLinePart["suffix"] = k
                            //         }
                            //     }
                            //
                            //     const formatMapping = listCardLinePart["formatMapping"]
                            //     if (formatMapping) {
                            //         for (let m = 0; m < formatMapping.length; m++) {
                            //             const formatMappingItem = formatMapping[m]
                            //             if (formatMappingItem) {
                            //                 const replaceText = formatMappingItem["replaceText"]
                            //                 if (replaceText) {
                            //                     const k = `i18n_entity.${entityName}.listCard[${i}][${j}].formatMapping[${m}].replaceText`
                            //                     if (!existTranslatedMap[k]) {
                            //                         existTranslatedLines.push(`${k}=${replaceText}`)
                            //                         formatMappingItem["replaceText"] = k
                            //                     }
                            //                 }
                            //             }
                            //         }
                            //     }
                            // }
                        }
                    }
                }
            }
        }

        const fields = entity["fields"]
        for (const fieldName in fields) {
            const field = fields[fieldName]
            {
                // fields[k].view.trueText
                // fields[k].view.falseText
                const view = field["view"]
                if (view) {
                    const trueText = view["trueText"]
                    if (args1 === "init") ensureNotExtracted(trueText)
                    if (trueText) {
                        const k = `i18n_entity.${entityName}.fields.${fieldName}.view.trueText`
                        if (!existTranslatedMap[k]) {
                            existTranslatedLines.push(`${k}=${trueText}`)
                            view["trueText"] = k
                        }
                    }

                    const falseText = view["falseText"]
                    if (args1 === "init") ensureNotExtracted(falseText)
                    if (falseText) {
                        const k = `i18n_entity.${entityName}.fields.${fieldName}.view.falseText`
                        if (!existTranslatedMap[k]) {
                            existTranslatedLines.push(`${k}=${falseText}`)
                            view["falseText"] = k
                        }
                    }
                }
            }

            {
                // fields[k].label
                const k = `i18n_entity.${entityName}.fields.${fieldName}.label`
                if (args1 === "init") ensureNotExtracted(field["label"])
                if (!existTranslatedMap[k]) {
                    existTranslatedLines.push(`${k}=${field["label"]}`)
                    field["label"] = k
                }
            }

            {
                // fields[k].tip
                const tip = field["tip"]
                if (args1 === "init") ensureNotExtracted(tip)
                if (tip) {
                    const k = `entity.${entityName}.fields.${fieldName}.tip`
                    if (!existTranslatedMap[k]) {
                        existTranslatedLines.push(`${k}=${tip}`)
                        field["label"] = k
                    }
                }
            }

            // TODO fields[k].defaultValue ???

            {
                // fields[k].inlineOptionBill.items[i].label
                const inlineOptionBill = field["inlineOptionBill"]
                if (inlineOptionBill) {
                    const items = inlineOptionBill["items"]
                    if (items) {
                        for (const item of items) {
                            const v = item["value"]
                            const l = item["label"]
                            if (args1 === "init") ensureNotExtracted(l)

                            const k = `i18n_entity.${entityName}.fields.${fieldName}.inlineOptionBill.items.${v}.label`
                            if (!existTranslatedMap[k]) {
                                existTranslatedLines.push(`${k}=${l}`)
                                item["label"] = k
                            }
                        }
                    }
                }
            }
            {
                // fields[k].listBatchButtons.buttons[i].label
                const listBatchButtons = field["listBatchButtons"]
                if (listBatchButtons) {
                    const buttons = listBatchButtons["buttons"]
                    if (buttons) {
                        for (let i = 0; i < buttons.length; i++) {
                            const button = buttons[i]
                            // for (const button of buttons) {
                            const l = button["label"]
                            if (args1 === "init") ensureNotExtracted(l)
                            const k = `i18n_entity.${entityName}.fields.${fieldName}.listBatchButtons.buttons[${i}].label`
                            if (!existTranslatedMap[k]) {
                                existTranslatedLines.push(`${k}=${l}`)
                                button["label"] = k
                            }
                        }
                    }
                }
            }
        }

        console.log(existTranslatedLines)

        // 写到新的 meta.json
        fs.writeFileSync(entityFilePath, JSON.stringify(entities, null, 2), {encoding: "utf-8"})

        // 将新的 key 追加到 dict.txt
        fs.writeFileSync(dictFilePath, existTranslatedLines.join("\n"), {encoding: "utf-8"})

    }
}

extract()
