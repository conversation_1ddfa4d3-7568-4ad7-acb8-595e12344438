const fs = require("fs");
const path = require("path");

function fixIdFields1() {
  let c = 0
  const builtInDir = path.join( "meta", "builtin")
  console.log(builtInDir)
  console.log(fs.existsSync(builtInDir))
  const files = fs.readdirSync(builtInDir, { withFileTypes: true })

  files.forEach(entry => {
    const fullPath = path.join(builtInDir, entry.name);
    // 获取文件信息
    const entityText = fs.readFileSync(fullPath, { encoding: "utf-8" })
    const entity = JSON.parse(entityText)
    // console.log(jsonData)
    if (entity.fields["id"].length == 600) {
      // console.log("chagne " + entity.name)
      console.log(entity.name)
      c++
      entity.fields["id"].length = 50
      // console.log(entity)
      fs.writeFileSync(fullPath, JSON.stringify(entity, null, 2), { encoding: "utf-8" })
    }
  })

  console.log("fixIdFields1 change count: " + c)
}

function fixIdFields2() {
  let c = 0

  const p = path.join("trick-m4", "src", "main", "resources", "entities.json")
  const entitiesTxt = fs.readFileSync(p, { encoding: "utf-8" })
  let entities = JSON.parse(entitiesTxt)
  entities.forEach(entity => {
    if (entity.fields["id"].length == 600) {
      // console.log("chagne " + entity.name)
      c++
      entity.fields["id"].length = 50
    }
  })
  fs.writeFileSync(p, JSON.stringify(entities, null, 2), { encoding: "utf-8" })

  console.log("fixIdFields2 change count: " + c)
}

fixIdFields1()
fixIdFields2()