
/**
 * 比较新老字典是否有遗漏的 key
 */

const fs = require("fs");


function getKeyValue(line) {
  const firstIndex = line.indexOf("=")
  const k = line.substring(0, firstIndex).trimEnd() // key
  const v = line.substring(firstIndex + 1).trimStart() // value
  return {k, v}
}

function readPureLines(path) {
  const lines = fs.readFileSync(path, {encoding: "utf-8"}).split("\n")
  const map = {}
  for (const line of lines) {
    if (line.trim().length <= 0 || line.trimStart().startsWith("#") || line.trimStart().startsWith("[")) continue
    const {k, v} = getKeyValue(line)
    map[k] = v // 这里性能差，但看的清楚
  }
  return map
}


function execute() {
  const newDict = readPureLines("trick-m4/src/main/resources/server-base-zh.txt")
  const oldDict = readPureLines("trick-m4/src/main/resources/server-base-zh-bak.txt")

  // const newDict = readPureLines("../trick-ui/f2-ui/i18n/ui-base-zh.txt")
  // const oldDict = readPureLines("../trick-ui/f2-ui/i18n/ui-base-zh-bak.txt")
  const oldKeys = Object.keys(oldDict)
  const newKeys = Object.keys(newDict)
  const diffKeys = oldKeys.filter(k => !newKeys.includes(k))
  const diffKeys2 = newKeys.filter(k => !oldKeys.includes(k))
  console.log(`diffKeys = ${JSON.stringify(diffKeys)}`)
  console.log(`diffKeys2 = ${JSON.stringify(diffKeys2)}`)
}


execute()