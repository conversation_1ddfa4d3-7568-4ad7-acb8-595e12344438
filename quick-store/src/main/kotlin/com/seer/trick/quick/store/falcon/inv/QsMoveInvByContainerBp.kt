package com.seer.trick.quick.store.falcon.inv

import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.falcon.domain.ParamObjectType
import com.seer.trick.quick.store.base.QsBaseUpdateService

/**
 * 按容器移动库存
 */
class QsMoveInvByContainerBp : AbstractBp() {

  override fun process() {
    val leafContainerId = mustGetBlockInputParam(ipLeafContainerId.name) as String
    val toBinId = getBlockInputParam(ipToBinId.name) as String? ?: ""
    val state = getBlockInputParam("state") as String?
    val binEv = EntityRwService.findOneById("FbBin", toBinId)
    val update: EntityValue = mutableMapOf("bin" to toBinId)

    if (!state.isNullOrBlank()) {
      // 获取库存的实体类信息 从中获取state字段
      val em = BaseCenter.mustGetEntityMeta("FbInvLayout")
      em.checkIdExisted()
      val fields = em.fields
      val list = fields["state"]?.inlineOptionBill?.items
      val valuesArray = list?.map { it.value }?.toTypedArray() ?: arrayOf()
      // 判断输入的state 是否在当前已启用的库存状态字段中
      val isStatePresent = valuesArray.contains(state)

      if (isStatePresent) {
        update["state"] = state
      } else {
        throw BzError("errFalconBlockInputParamRangeError", blockConfig.name, "state")
      }
    }

    update += if (binEv != null) {
      QsBaseUpdateService.fixFbBinToFbIvnBin(binEv)
    } else {
      QsBaseUpdateService.clearFbInvBinAttributes()
    }

    val r = EntityRwService.updateMany(
      "FbInvLayout",
      Cq.eq(FieldMeta.FIELD_LEAF_CONTAINER, leafContainerId),
      update,
    )
    logger.info("MoveInvByContainerBp change $r rows invLayout")
    val ids = EntityRwService.findIds("FbInvLayout", Cq.include(FieldMeta.FIELD_LEAF_CONTAINER, leafContainerId))

    // 添加猎鹰任务相关对象
    for (id in ids) {
      addRelatedObject("FbInvLayout", id, null)
    }
  }

  companion object {
    private val ipLeafContainerId = BlockInputParamDef(
      "leafContainerId",
      BlockParamType.String,
      true,
      objectTypes = listOf(ParamObjectType.ContainerId),
    )
    private val ipToBinId = BlockInputParamDef(
      "toBinId",
      BlockParamType.String,
      objectTypes = listOf(ParamObjectType.BinId),
    )

    val def = BlockDef(
      QsMoveInvByContainerBp::class.simpleName!!,
      color = "#CEE6F3",
      inputParams = listOf(ipLeafContainerId, ipToBinId, BlockInputParamDef("state", BlockParamType.String, false)),
    )
  }
}