package com.seer.trick.quick.store

import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService

@Deprecated("即将删除")
object BinContainerInvReadService {

  /**
   * 加载容器库存明细，转化成 QsOldInvLine
   */
  fun loadContainerInvToOldInvLines(containerId: String): List<EntityValue> {
    val lines = EntityRwService.findMany("FbInvLayout", Cq.eq(FieldMeta.FIELD_TOP_CONTAINER, containerId))
    for (line in lines) {
      line["refInvId"] = line["id"]
      line.remove("id")
    }
    return lines
  }

  /**
   * 加载容器内的所有库存并按格子分好
   */
  fun loadContainerInvBySub(containerId: String): Map<Int, List<EntityValue>> {
    // 加载容器内所有库存，并按各自分好
    val containerInvLayouts = EntityRwService.findMany(
      "FbInvLayout", Cq.eq(FieldMeta.FIELD_TOP_CONTAINER, containerId)
    )
    val subInvLayouts: MutableMap<Int, MutableList<EntityValue>> = mutableMapOf()
    for (cil in containerInvLayouts) {
      var subContainerId = cil[FieldMeta.FIELD_SUB_CONTAINER_ID] as Int? ?: 1
      if (subContainerId <= 0) subContainerId = 1
      subInvLayouts.getOrPut(subContainerId) { ArrayList() }.add(cil)
    }
    return subInvLayouts
  }

  fun mustGetBin(binId: String): EntityValue {
    return EntityRwService.findOneById("FbBin", binId)
      ?: throw BzError("errNoBinById", binId)
  }

  fun mustGetContainer(containerId: String): EntityValue {
    return EntityRwService.findOneById("FbContainer", containerId)
      ?: throw BzError("errNoSuchContainerById", containerId)
  }

  fun loadBinIdOfContainer(containerId: String): String? {
    val containerEv = mustGetContainer(containerId)
    return containerEv["bin"] as String?
  }

  fun loadContainerIdOfBin(binId: String): String? {
    val binEv = mustGetBin(binId)
    return binEv["container"] as String?
  }

  /**
   * 容器所在库区
   */
  fun getDistrictOfContainer(containerEv: EntityValue): String? {
    val binId = containerEv["bin"] as String? ?: return null
    val binEv = EntityRwService.findOneById("FbBin", binId) ?: return null
    return binEv["district"] as String?
  }

  /**
   * 找库区内一个未停用、未占用的库位
   */
  fun listEmptyBinInDistrict(districtId: String): EntityValue? {
    return EntityRwService.findOne(
      "FbBin",
      Cq.and(
        listOf(Cq.eq("district", districtId), Cq.ne(FieldMeta.FIELD_DISABLED, true), Cq.eq("loadStatus", "Empty")),
      )
    )
  }

}