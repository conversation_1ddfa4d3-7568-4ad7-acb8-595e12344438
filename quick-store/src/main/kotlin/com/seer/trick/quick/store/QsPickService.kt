package com.seer.trick.quick.store

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.Cq
import com.seer.trick.base.concurrent.BaseConcurrentCenter.highTimeSensitiveExecutor
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.entity.service.change.EntityChange
import com.seer.trick.base.entity.service.extension.EntityServiceExtension
import com.seer.trick.base.reslock.ResLockService
import com.seer.trick.base.script.ScriptCenter
import com.seer.trick.base.script.ScriptExeRequest
import com.seer.trick.base.script.ScriptTraceContext
import com.seer.trick.helper.NumHelper
import com.seer.trick.helper.submitCatch
import org.slf4j.LoggerFactory
import kotlin.concurrent.withLock
import kotlin.math.min

object QsPickService : EntityServiceExtension() {
  
  private val logger = LoggerFactory.getLogger(this::class.java)
  
  override fun afterCreating(em: EntityMeta, evList: List<EntityValue>) {
    if (em.name != "QsPickOrder") return
    
    for (ev in evList) {
      if (ev[FieldMeta.FIELD_ORDER_STATE] == "Done") {
        highTimeSensitiveExecutor.submitCatch("后处理拣货单完成状态", logger) {
          donePick(EntityHelper.mustGetId(ev))
        }
      }
    }
  }
  
  override fun afterUpdating(em: EntityMeta, changes: List<EntityChange>) {
    if (em.name != "QsPickOrder") return
    
    for (c in changes) {
      val oldEv = c.oldValue ?: continue
      val newEv = c.newValue ?: continue
      if (oldEv[FieldMeta.FIELD_ORDER_STATE] != "Done" && newEv[FieldMeta.FIELD_ORDER_STATE] == "Done") {
        highTimeSensitiveExecutor.submitCatch("后处理拣货单完成状态", logger) {
          donePick(c.id)
        }
      }
    }
  }
  
  private fun donePick(orderId: String) = ResLockService.resLock.withLock {
    // 重新加载，保证最新
    val order = EntityRwService.findOne("QsPickOrder", Cq.idEq(orderId)) ?: return
    logger.info("后处理拣货单完成状态：$order")
    
    val containerId = order["container"] as String
    
    val lines = EntityHelper.mustGetLines(order, FieldMeta.FIELD_LINES)
    
    var reInvAssign = false // 是否需要再分配库存
    
    // 扣库存扩展 qsPickOrderToReduceInv(pickOrder)
    if (ScriptCenter.exists("qsPickOrderToReduceInv")) {
      val extRes: PickOrderToReduceInvRes = ScriptCenter.execute(
        ScriptExeRequest("qsPickOrderToReduceInv", arrayOf(ScriptTraceContext(), order)),
        jacksonTypeRef(),
      )
      if (extRes.error) {
        logger.error("拣货后扣库存失败：" + extRes.errorMsg)
      } else {
        reInvAssign = extRes.reInvAssign
      }
    } else {
      for (line in lines) {
        tryToReduceInv(line, containerId)
        
        val planQty = NumHelper.anyToDouble(line["planQty"]) ?: continue
        val qty = NumHelper.anyToDouble(line["qty"]) ?: continue
        if (qty < planQty) reInvAssign = true
      }
    }
    
    // 清工作状态
    ContainerUpdateService.clearContainerWorkStatus(containerId)
    
    // 修复容器状态
    BinContainerInvUpdateService.fixContainerFilled(containerId)
    
    if (reInvAssign) { // reInvAssign(orderId, lines, config)
    }
  }
  
  /**
   * 从拣货单行扣减库存。
   */
  private fun tryToReduceInv(line: EntityValue, containerId: String) {
    logger.info("尝试为分拣单行减库存，容器=$containerId，分拣单行=$line")
    
    if (tryToReduceInvByLayoutId(line)) return
    
    tryToReduceInvBySearch(line, containerId)
  }
  
  // 先看拣货单行上直接记录的库存 ID 能不能找到
  private fun tryToReduceInvByLayoutId(line: EntityValue): Boolean {
    val planQty = NumHelper.anyToDouble(line["planQty"]) ?: 0.0 // 期望分拣量
    val pickQty = NumHelper.anyToDouble(line["qty"]) ?: 0.0 // 实际分拣的量
    
    val layoutId = line["invLayoutId"] as String?
    if (layoutId.isNullOrBlank()) return false
    val invLayoutEv = EntityRwService.findOneById("FbInvLayout", layoutId) ?: return false
    
    val invQty = NumHelper.anyToDouble(invLayoutEv["qty"]) ?: 0.0 // 库存数量
    val usedQty = NumHelper.anyToDouble(invLayoutEv["usedQty"]) ?: 0.0 // 已分配数量
    val newInvQty = invQty - pickQty // 新库存数量
    val newUsedQty = usedQty - planQty // 扣减已分配数量
    logger.info(
      "扣减库存明细，期望分拣数=$planQty，实际分拣数=$pickQty，" +
        "明细ID=$layoutId，原数量=$invQty，新数量=$newInvQty，原已分配=$usedQty，新已分配=$newUsedQty",
    ) // if (newInvQty <= 0) {
    // TODO 先不删，库存明细被多个分拣用到？
    // EntityRwService.removeOne("FbInvLayout", Cq.idEq(invLayoutId))
    EntityRwService.updateOne(
      "FbInvLayout",
      Cq.idEq(layoutId),
      mutableMapOf("qty" to newInvQty, "usedQty" to newUsedQty),
    )
    
    return true
  }
  
  // 如果不能通过库存明细 ID 找到，尝试通过字段匹配找。通过寻找同一个容器内的其他库存
  private fun tryToReduceInvBySearch(line: EntityValue, containerId: String) {
    var pickQty = NumHelper.anyToDouble(line["qty"]) ?: 0.0 // 实际分拣的量
    
    val subContainerId = line[FieldMeta.FIELD_SUB_CONTAINER_ID] as Int?
    
    val cqList = mutableListOf(
      Cq.eq(FieldMeta.FIELD_MATERIAL, line[FieldMeta.FIELD_MATERIAL]),
      Cq.eq(FieldMeta.FIELD_TOP_CONTAINER, containerId),
    ) // TODO 更多库存特征字段
    if (subContainerId != null && subContainerId > 0) {
      cqList += Cq.eq(FieldMeta.FIELD_SUB_CONTAINER_ID, subContainerId)
    }
    
    // 按数量从小到大
    val layouts = EntityRwService.findMany("FbInvLayout", Cq.and(cqList), FindOptions(sort = listOf("-qty")))
    
    for (layout in layouts) {
      val layoutId = EntityHelper.mustGetId(layout)
      val invQty = NumHelper.anyToDouble(layout["qty"]) ?: continue
      if (invQty <= 0) continue
      // TODO 这里没管已分配可能有问题
      val thisQty = min(pickQty, invQty)
      
      val newInvQty = invQty - thisQty
      pickQty -= thisQty
      
      logger.info(
        "扣减库存明细，实际分拣数=$thisQty，" +
          "明细ID=$layoutId，原数量=$invQty，新数量=$newInvQty",
      )
      EntityRwService.updateOne(
        "FbInvLayout",
        Cq.idEq(layoutId),
        mutableMapOf("qty" to newInvQty),
      )
      if (pickQty <= 0) break
    }
  }
  
  // 准备重新分配库存
  // private fun reInvAssign(
  //   orderId: String, lines: List<EntityValue>, outboundConfig: OutboundConfig
  // ) {
  //   // 盘亏记录
  //   val countFixes: MutableList<EntityValue> = ArrayList()
  //
  //   val outboundOrderIds: MutableSet<String> = HashSet()
  //
  //   for (line in lines) {
  //     val planQty = NumHelper.anyToDouble(line["planQty"]) ?: continue
  //     val qty = NumHelper.anyToDouble(line["qty"]) ?: continue
  //     if (qty >= planQty) continue
  //
  //     val delta = planQty - qty
  //
  //     // 盘亏记录
  //     val cf: EntityValue = mutableMapOf(
  //       "container" to line["container"],
  //       FieldMeta.FIELD_SUB_CONTAINER_ID to line[FieldMeta.FIELD_SUB_CONTAINER_ID],
  //       "qty" to -delta, // 盘亏，负
  //       "remark" to "分拣实物不足。分拣单号=$orderId"
  //     )
  //     for (fn in outboundConfig.invMatchFields) cf[fn] = line[fn]
  //     MaterialManager.fillMaterialFieldsIntoLine(cf[FieldMeta.FIELD_MATERIAL] as String, cf)
  //
  //     countFixes += cf
  //
  //     // 虽然记录了单行 ID，但是更新单据时，这个不稳定，所以还是用单号+行号去查
  //     val outboundOrderId = line["sourceOrderId"] as String?
  //     val outboundLineNo = line["sourceLineNo"] as Int?
  //     if (outboundOrderId == null || outboundLineNo == null) {
  //       logger.error("分拣单行上没有完整的来源单据和行号信息：$line")
  //       continue
  //     }
  //
  //     val outboundLine = EntityRwService.findOne(
  //       "FbOutboundOrderLine",
  //       Cq.and(
  //         listOf(
  //           Cq.eq(FieldMeta.FIELD_PARENT_ID, outboundOrderId),
  //           Cq.eq(FieldMeta.FIELD_LINE_NO, outboundLineNo)
  //         )
  //       )
  //     )
  //     if (outboundLine == null) {
  //       logger.error("找不到出库单行 $outboundOrderId:$outboundLineNo")
  //       continue
  //     }
  //
  //     outboundOrderIds += outboundOrderId
  //
  //     var invAssignedQty = NumHelper.anyToDouble(outboundLine["invAssignedQty"]) ?: 0.0
  //     logger.info("出库单行 $outboundOrderId:$outboundLineNo，已分配数量从 $invAssignedQty 减去 $delta")
  //     invAssignedQty -= delta
  //
  //     EntityRwService.updateOne(
  //       outboundConfig.outboundEmName + "Line", Cq.idEq(EntityHelper.mustGetId(outboundLine)),
  //       mutableMapOf("invAssignedQty" to invAssignedQty)
  //     )
  //   }
  //
  //   if (outboundOrderIds.isNotEmpty()) {
  //     logger.info("更新以下出库单，库存分配未完成 $outboundOrderIds")
  //     EntityRwService.updateMany(
  //       outboundConfig.outboundEmName, Cq.include("id", outboundOrderIds.toList()),
  //       mutableMapOf("invAssignedAll" to false)
  //     )
  //   }
  //
  //   if (countFixes.isNotEmpty()) {
  //     logger.info("创建盘亏记录 $countFixes")
  //     EntityRwService.createMany("FbCountFix", countFixes)
  //   }
  // }
}

data class PickOrderToReduceInvRes(
  val error: Boolean = false,
  val errorMsg: String? = null,
  val reInvAssign: Boolean = false, // 是否需要重新分配库存出库
)