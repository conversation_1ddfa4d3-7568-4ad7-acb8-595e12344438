package com.seer.trick.quick.store.falcon

import com.seer.trick.Cq

import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockOutputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.quick.store.base.QsResApplication
import com.seer.trick.quick.store.base.QsResAssignmentService
import com.seer.trick.quick.store.base.QsResType

/**
 * 预定资源 demo BP
 */
class ResAssignDemoBp : AbstractBp() {

  override fun process() {
    val tx = QsResAssignmentService.begin()
    val app1 = QsResApplication(
      tx,
      QsResType.Bin,
      num = 2,
      filter = Cq.include("district", listOf("a", "b")),
      sort = listOf("row", "column", "layer"),
      bzMark = "Inbound FindEmptyBinBp",
      bzDesc = "sourceOrderId",
    )
    QsResAssignmentService.request(app1)

    val app2 = QsResApplication(
      tx,
      QsResType.Container,
      num = 2,
      filter = Cq.include("id", listOf("cId1", "cId2")),
      sort = listOf("row", "column", "layer"),
      bzMark = "Inbound FindEmptyBinBp",
      bzDesc = "sourceOrderId",
    )
    QsResAssignmentService.request(app2)

    if (tx.bins.size == 2 && tx.containers.size == 2) {
      QsResAssignmentService.commit(tx)
    } else {
      QsResAssignmentService.rollback(tx)
    }
  }

  companion object {
    val def = BlockDef(
      ResAssignDemoBp::class.simpleName!!,
      inputParams = listOf(
        BlockInputParamDef("districtIds", BlockParamType.String, true),
      ),
      outputParams = listOf(
        BlockOutputParamDef("binId", BlockParamType.String),
      ),
    )
  }
}