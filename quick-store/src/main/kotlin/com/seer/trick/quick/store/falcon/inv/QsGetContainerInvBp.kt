package com.seer.trick.quick.store.falcon.inv

import com.seer.trick.Cq
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*

/**
 * 获取容器内的库存明细 最外层
 */
class QsGetContainerInvBp : AbstractBp() {

  override fun process() {
    val containerId = mustGetBlockInputParam("containerId") as String

    val inv = EntityRwService.findMany("FbInvLayout", Cq.eq(FieldMeta.FIELD_TOP_CONTAINER, containerId))
    if (inv.isEmpty()) {
      setBlockOutputParams(mapOf("found" to false, "inv" to null))
    } else {
      setBlockOutputParams(mapOf("found" to true, "inv" to inv))
    }
  }

  companion object {
    val def = BlockDef(
      QsGetContainerInvBp::class.simpleName!!,
      color = "#7293d8",
      inputParams = listOf(
        BlockInputParamDef(
          "containerId",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.ContainerId),
        ),
      ),
      outputParams = listOf(
        BlockOutputParamDef("found", BlockParamType.Boolean),
        BlockOutputParamDef("inv", BlockParamType.JSONArray),
      ),
    )
  }
}