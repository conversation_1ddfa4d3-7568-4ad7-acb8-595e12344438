package com.seer.trick.quick.store.falcon.container

import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*
import com.seer.trick.helper.NumHelper
import com.seer.trick.quick.store.base.QsResApplication
import com.seer.trick.quick.store.base.QsResAssignmentService
import com.seer.trick.quick.store.base.QsResType

/**
 * 寻找空库位
 */
class QsFindEmptyContainerBp : AbstractBp() {

  override fun process() {
    val districtIdStr = mustGetBlockInputParam(ipDistrictIds.name) as String
    val districtIds = districtIdStr.split(",")
    val sortStr = getBlockInputParam(ipSortStr.name) as String?
//    val keepTrying = getBlockInputParamAsBool(ipKeepTrying.name)
    val timeout = getBlockInputParamAsLong(timeout.name) ?: -1
    val groupPriority = NumHelper.anyToInt(getBlockInputParamAsLong(groupPriority.name)) ?: 0
    val priority = NumHelper.anyToInt(getBlockInputParamAsLong(priority.name)) ?: 0
    val bzMark = getBlockInputParam(bzMark.name) as String? ?: taskRuntime.taskId
    val bzDesc = getBlockInputParam(bzDesc.name) as String? ?: ""

    val o = if (sortStr.isNullOrEmpty()) {
      emptyList()
    } else {
      sortStr.split(",")
    }
    val tx = QsResAssignmentService.begin()
    val qra = QsResApplication(
      tx = tx,
      type = QsResType.Bin,
      groupPriority = groupPriority,
      priority = priority,
      timeout = timeout,
      num = 1,
      bzMark = bzMark,
      bzDesc = bzDesc,
      sort = o,
      filter = Cq.include("district", districtIds),
    )
    // TODO 资源申请是阻塞的需要增加一个非阻塞的方法
    val request: List<EntityValue>? = QsResAssignmentService.request(qra)

    if (request.isNullOrEmpty()) {
      QsResAssignmentService.rollback(tx)
    } else {
      QsResAssignmentService.commit(tx)
    }
    setBlockOutputParams(
      mapOf(
        opFound.name to !request.isNullOrEmpty(),
        opBinId.name to request?.get(0)?.get("bin") as String?,
        opContainerId.name to request?.get(0)?.get("topContainer") as String?,
      ),
    )
  }

  companion object {

    private val ipDistrictIds = BlockInputParamDef(
      "districtIds",
      BlockParamType.String,
      true,
      objectTypes = listOf(ParamObjectType.DistrictId),
    )
    private val ipSortStr = BlockInputParamDef("sort", BlockParamType.String)

    //    private val ipKeepTrying = BlockInputParamDef("keepTrying", BlockParamType.Boolean)
    private val timeout = BlockInputParamDef("timeout", BlockParamType.Long)
    private val groupPriority = BlockInputParamDef("groupPriority", BlockParamType.Long)
    private val priority = BlockInputParamDef("priority", BlockParamType.Long)
    private val bzMark = BlockInputParamDef("bzMark", BlockParamType.String)
    private val bzDesc = BlockInputParamDef("bzDesc", BlockParamType.String)

    private val opFound = BlockOutputParamDef("found", BlockParamType.Boolean)
    private val opBinId = BlockOutputParamDef(
      "binId",
      BlockParamType.String,
      objectTypes = listOf(ParamObjectType.BinId),
    )
    private val opContainerId = BlockOutputParamDef(
      "containerId",
      BlockParamType.String,
      objectTypes = listOf(ParamObjectType.ContainerId),
    )

    val def = BlockDef(
      QsFindEmptyContainerBp::class.simpleName!!,
      color = "#C7DCA7",
      inputParams = listOf(ipDistrictIds, ipSortStr, groupPriority, priority, timeout, bzMark, bzDesc),
      outputParams = listOf(opFound, opBinId, opContainerId),
    )
  }
}