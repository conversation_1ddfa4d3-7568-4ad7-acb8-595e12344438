package com.seer.trick.quick.store

import com.seer.trick.BzError
import com.seer.trick.base.concurrent.BaseConcurrentCenter.highTimeSensitiveExecutor
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.extension.EntityServiceExtension
import com.seer.trick.base.reslock.ResLockService
import com.seer.trick.helper.submitCatch
import org.slf4j.LoggerFactory
import kotlin.concurrent.withLock

/**
 * 放置容器（人工上架单）。
 * 只能创建，不允许修改。
 */
object QsPutOnContainerService : EntityServiceExtension() {

  private val logger = LoggerFactory.getLogger(this::class.java)

  override fun beforeCreating(em: EntityMeta, evList: List<EntityValue>): List<String>? {
    if (em.name != "QsPutOnContainerOrder") return null
    // TODO 检查参数合法
    // 加载当前库存
    for (ev in evList) {
      val containerId = ev["container"] as String?
      // 解决 NPE 问题
      if (containerId.isNullOrBlank()) throw BzError("errPutOnContainerMissingContainerBin")
      ev["oldInvLines"] = BinContainerInvReadService.loadContainerInvToOldInvLines(containerId)
    }
    return null
  }

  override fun afterCreating(em: EntityMeta, evList: List<EntityValue>) {
    if (em.name != "QsPutOnContainerOrder") return
    for (ev in evList) {
      highTimeSensitiveExecutor.submitCatch("后处理创建人工上架单", logger) {
        putOnContainer(ev)
      }
    }
  }

  private fun putOnContainer(ev: EntityValue) = ResLockService.resLock.withLock {
    logger.info("后处理创建人工上架单：$ev")
    val containerId = ev["container"] as String
    val binId = ev["bin"] as String

    BinContainerInvUpdateService.moveContainerToBin(containerId, binId)
  }

}