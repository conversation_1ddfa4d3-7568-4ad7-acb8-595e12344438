package com.seer.trick.quick.store.falcon.inv

import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.bz.wms.inv.CreateInvLine
import com.seer.trick.bz.wms.inv.CreateInvService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.falcon.domain.ParamObjectType
import com.seer.trick.helper.DateHelper
import com.seer.trick.helper.NumHelper
import com.seer.trick.helper.StringHelper
import com.seer.trick.quick.store.base.QsBaseReadService
import java.util.*

/**
 * 从单据创建库存
 */
class QsCreateInvFromOrderBp : AbstractBp() {

  @Suppress("UNCHECKED_CAST")
  override fun process() {
    val order = mustGetBlockInputParam(ipOrder.name) as EntityValue
    val orderId = EntityHelper.mustGetId(order)

    // 数量字段
    val qtyFieldName = mustGetBlockInputParam("qtyFieldName") as String
    // 物料字段
    val materialFieldName = mustGetBlockInputParam("materialFieldName") as String
    // 库存状态
    val state = getBlockInputParam("state") as String? ?: ""
    // 库位
    val binId = getBlockInputParam("bin") as String?
    // 容器
    val container = getBlockInputParam("container") as String?
    // 从单行拷贝哪些附加字段，一般是批次、供应商等特征
    val copyFieldsStr = getBlockInputParam("copyFields") as String?
    val copyFields = StringHelper.splitTrim(copyFieldsStr, ",")

    val lotNoFormat = getBlockInputParam("lotNoFormat") as String?

    val qty = NumHelper.anyToDouble(order[qtyFieldName])
    if (qty == null || qty <= 0) {
      logger.warn(
        "Quantity is empty or less than or equal to 0, skip creating inventory details=$orderId",
      )
      return
    }

    val materialId = order[materialFieldName] as String?
    if (materialId.isNullOrBlank()) {
      logger.warn("The material is empty, skip creation $orderId")
      return
    }

    val moreFields = mutableMapOf<String, Any?>()
    for (fn in copyFields) moreFields[fn] = order[fn]

//    if (!binId.isNullOrBlank()) {
//      val bin = EntityRwService.findOneById("FbBin", binId) ?: throw BzError("errNoBinById", binId)
//      moreFields["district"] = bin["district"]
//      moreFields["channel"] = bin["channel"]
//      moreFields["row"] = bin["row"]
//      moreFields["column"] = bin["column"]
//      moreFields["layer"] = bin["layer"]
//      moreFields["depth"] = bin["depth"]
//    }

    if (!lotNoFormat.isNullOrBlank()) {
      val lotNo = DateHelper.formatDate(Date(), lotNoFormat)
      moreFields["lotNo"] = lotNo
    }
    if (!container.isNullOrEmpty()) {
      moreFields[FieldMeta.FIELD_TOP_CONTAINER] = QsBaseReadService.findTopContainerId(container)
    }
    val ids = CreateInvService.fixCreateInvLayoutByLines(
      listOf(
        CreateInvLine(
          qty = qty,
          material = materialId,
          state = state,
          bin = binId,
          leafContainer = container,
          subContainerId = null,
          moreFields = moreFields,
        ),
      ),
    )

    val layouts = EntityRwService.findMany("FbInvLayout", Cq.include("id", ids))
    logger.info("CreateInvFromOrderBp, layouts.size = ${layouts.size}")
    logger.debug("CreateInvFromOrderBp, layouts = $layouts")

    // 添加猎鹰任务相关对象
    if (!container.isNullOrBlank()) {
      addRelatedObject("FbContainer", container, null)
    }
    for (id in ids) {
      addRelatedObject("FbInvLayout", id, null)
    }
  }

  companion object {

    private val ipOrder = BlockInputParamDef(
      "order",
      BlockParamType.JSONObject,
      true,
      objectTypes = listOf(ParamObjectType.OrderId),
    )
    private val ipQtyFieldName = BlockInputParamDef(
      "qtyFieldName",
      BlockParamType.String,
      false,
      objectTypes = listOf(ParamObjectType.EntityFieldName),
    )
    private val ipMaterialFieldName = BlockInputParamDef(
      "materialFieldName",
      BlockParamType.String,
      false,
      objectTypes = listOf(ParamObjectType.EntityFieldName),
    )
    private val ipState = BlockInputParamDef("state", BlockParamType.String, false)
    private val ipBin = BlockInputParamDef(
      "bin",
      BlockParamType.String,
      false,
      objectTypes = listOf(ParamObjectType.BinId),
    )
    private val ipContainer = BlockInputParamDef(
      "container",
      BlockParamType.String,
      false,
      objectTypes = listOf(ParamObjectType.ContainerId),
    )
    private val ipCopyFields = BlockInputParamDef("copyFields", BlockParamType.String, false)
    private val ipLotNoFormat = BlockInputParamDef("lotNoFormat", BlockParamType.String, false)

    val def = BlockDef(
      QsCreateInvFromOrderBp::class.simpleName!!,
      color = "#A8D297",
      inputParams = listOf(
        ipOrder,
        ipQtyFieldName,
        ipMaterialFieldName,
        ipState,
        ipBin,
        ipContainer,
        ipCopyFields,
        ipLotNoFormat,
      ),
    )
  }
}