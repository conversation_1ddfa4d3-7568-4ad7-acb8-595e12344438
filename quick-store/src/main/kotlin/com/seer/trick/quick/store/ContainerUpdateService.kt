package com.seer.trick.quick.store

import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import org.slf4j.LoggerFactory

/**
 * 所有对容器的更新放在这里
 */
@Deprecated("即将删除")
object ContainerUpdateService {

  private val logger = LoggerFactory.getLogger(this::class.java)

  /**
   * 标记容器工作状态
   */
  fun markContainerWorkStatus(containerId: String, workStatus: String, targetBinId: String) {
    val containerEv = mustGetContainer(containerId)
    logger.info(
      "修改容器工作状态（workStatus=$workStatus）。容器=$containerId，目标库位=$targetBinId。当前容器状态=$containerEv"
    )

    EntityRwService.updateOne(
      "FbContainer", Cq.idEq(containerId),
      mutableMapOf("workStatus" to workStatus, "targetBin" to targetBinId)
    )
  }

  /**
   * 清除容器工作状态
   */
  fun clearContainerWorkStatus(containerId: String) {
    val containerEv = mustGetContainer(containerId)
    logger.info(
      "清除容器工作状态（workStatus）。容器=$containerId。当前容器状态=$containerEv"
    )

    EntityRwService.updateOne(
      "FbContainer", Cq.idEq(containerId),
      mutableMapOf("workStatus" to "")
    )
  }

  private fun mustGetContainer(containerId: String): EntityValue {
    return EntityRwService.findOneById("FbContainer", containerId)
      ?: throw BzError("errNoSuchContainerById", containerId)
  }

}