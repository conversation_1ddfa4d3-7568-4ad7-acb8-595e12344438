package com.seer.trick.quick.store

import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.reslock.ResLockService
import org.slf4j.LoggerFactory
import kotlin.concurrent.withLock

/**
 * 需要处理库位、容器、库存的同步更新
 */
@Deprecated("即将删除")
object BinContainerInvUpdateService {

  private val logger = LoggerFactory.getLogger(this::class.java)

  /**
   * 将容器从库位移动到机器人
   */
  fun moveContainerFromBinToRobot(
    containerId: String, fromBin: String, toRobot: String
  ) = ResLockService.resLock.withLock {
    val logHead = "将容器 $containerId 从库位 $fromBin 移动到机器人 $toRobot。"

    val containerEv = EntityRwService.findOneById("FbContainer", containerId)
    if (containerEv == null) {
      logger.error(logHead + "但找不到容器 $containerId")
      return@withLock
    }

    val binEv = EntityRwService.findOneById("FbBin", fromBin)
    if (binEv == null) {
      logger.error(logHead + "但找不到起点库位 $fromBin")
      return@withLock
    }

    logger.info(logHead + "当前容器=$containerEv。当前起点库位=$binEv。")

    EntityRwService.updateOne(
      "FbContainer", Cq.idEq(containerId),
      mutableMapOf("bin" to "", "onRobot" to toRobot)
    )

    EntityRwService.updateOne(
      "FbBin", Cq.idEq(fromBin),
      mutableMapOf("loadStatus" to "Empty", "occupied" to false, "container" to "", "purpose" to "")
    )

    EntityRwService.updateMany(
      "FbInvLayout", Cq.eq(FieldMeta.FIELD_TOP_CONTAINER, containerId),
      mutableMapOf("bin" to "", "district" to "", "warehouse" to "", "onRobot" to toRobot)
    )

    // TODO 检查容器确实在起点上
  }

  /**
   * 将容器移到新库位；修改容器、新库位、原库位（如果有），移动库存
   */
  fun moveContainerToBin(containerId: String, toBin: String) = ResLockService.resLock.withLock {
    val logHead = "将容器 $containerId 移动到库位 $toBin。"

    val containerEv = EntityRwService.findOneById("FbContainer", containerId)
    if (containerEv == null) {
      logger.error(logHead + "但找不到容器 $containerId")
      return@withLock
    }
    val containerCurrentBin = containerEv["bin"] as String?
    if (containerCurrentBin == toBin) {
      logger.warn(logHead + "容器 $containerId 已经在库位 $toBin")
      return@withLock
    }

    val binEv = EntityRwService.findOneById("FbBin", toBin)
    if (binEv == null) {
      logger.error(logHead + "但找不到目标库位 $toBin")
      return@withLock
    }

    logger.info(logHead + "当前容器=$containerEv。当前目标库位=$binEv。")

    EntityRwService.updateOne(
      "FbContainer", Cq.idEq(containerId),
      mutableMapOf("bin" to toBin, "onRobot" to "")
    )

    EntityRwService.updateOne(
      "FbBin", Cq.idEq(toBin),
      mutableMapOf(
        "loadStatus" to "Occupied", "occupied" to true, "container" to containerId,
        "pendingContainer" to null, "purpose" to ""
      )
    )

    if (!containerCurrentBin.isNullOrBlank()) {
      logger.info(logHead + "解绑老库位 $containerCurrentBin")
      EntityRwService.updateOne(
        "FbBin", Cq.idEq(containerCurrentBin),
        mutableMapOf("loadStatus" to "Empty", "occupied" to false, "container" to "", "purpose" to "")
      )
    }

    val invUpdate: EntityValue = mutableMapOf("district" to "", "warehouse" to "", "onRobot" to "")
    setBinToInv(binEv, invUpdate)
    EntityRwService.updateMany(
      "FbInvLayout", Cq.eq(FieldMeta.FIELD_TOP_CONTAINER, containerId),
      invUpdate
    )

    // TODO 容器已经在终点上
  }

  /**
   * 从库位上取下容器
   */
  fun takeOffContainer(
    containerId: String, binId: String, keepInv: Boolean
  ) = ResLockService.resLock.withLock {
    val logHead = "从库位 $binId 上取下容器 $containerId。保留库存=$keepInv"

    val containerEv = EntityRwService.findOneById("FbContainer", containerId)
    if (containerEv == null) {
      logger.error(logHead + "但找不到容器 $containerId")
      return@withLock
    }

    val binEv = EntityRwService.findOneById("FbBin", binId)
    if (binEv == null) {
      logger.error(logHead + "但找不到库位 $binId")
      return@withLock
    }

    logger.info(logHead + "当前容器=$containerEv。当前库位=$binEv。")

    EntityRwService.updateOne(
      "FbContainer", Cq.idEq(containerId),
      mutableMapOf("bin" to "")
    )

    EntityRwService.updateOne(
      "FbBin", Cq.idEq(binId),
      mutableMapOf("loadStatus" to "Empty", "occupied" to false, "container" to "", "purpose" to "")
    )

    if (keepInv) {
      removeInvLocationByContainer(containerId)
    } else {
      val oldInv = EntityRwService.findMany("FbInvLayout", Cq.eq(FieldMeta.FIELD_TOP_CONTAINER, containerId))
      logger.info(logHead + "删除库存：$oldInv")

      EntityRwService.removeMany("FbInvLayout", Cq.eq(FieldMeta.FIELD_TOP_CONTAINER, containerId))
    }

    fixContainerFilled(containerId)
  }

  /**
   * 修正容器空满状态
   */
  fun fixContainerFilled(containerId: String) = ResLockService.resLock.withLock {
    if (containerId.isBlank()) return@withLock
    // TODO 内外部容器
    val count = EntityRwService.count("FbInvLayout", Cq.eq(FieldMeta.FIELD_TOP_CONTAINER, containerId))
    val filled = count > 0
    EntityRwService.updateOne("FbContainer", Cq.idEq(containerId), mutableMapOf("filled" to filled))
  }

  private fun setBinToInv(binEv: EntityValue?, layout: EntityValue) {
    if (binEv != null) {
      layout["bin"] = binEv["id"]
      layout["district"] = binEv["district"]
      layout["warehouse"] = binEv["warehouse"]
      layout["row"] = binEv["row"]
      layout["column"] = binEv["column"]
      layout["layer"] = binEv["layer"]
      layout["depth"] = binEv["depth"]
    } else {
      layout["bin"] = null
      layout["district"] = null
      layout["warehouse"] = null
      layout["row"] = null
      layout["column"] = null
      layout["layer"] = null
      layout["depth"] = null
    }
  }

  // 删除容器内所有库存的位置信息
  private fun removeInvLocationByContainer(containerId: String) {
    val update: EntityValue = mutableMapOf()
    setBinToInv(null, update)
    EntityRwService.updateMany("FbInvLayout", Cq.eq(FieldMeta.FIELD_TOP_CONTAINER, containerId), update)
  }

}