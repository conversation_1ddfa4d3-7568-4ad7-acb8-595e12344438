package com.seer.trick.quick.store

import com.seer.trick.base.concurrent.BaseConcurrentCenter.highTimeSensitiveExecutor
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.change.EntityChange
import com.seer.trick.base.entity.service.extension.EntityServiceExtension
import com.seer.trick.base.reslock.ResLockService
import com.seer.trick.bz.wms.inv.CreateInvService
import com.seer.trick.helper.submitCatch
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import kotlin.concurrent.withLock

/**
 * 装货
 */
object QsPutService : EntityServiceExtension() {

  private val logger = LoggerFactory.getLogger(this::class.java)

  override fun afterCreating(em: EntityMeta, evList: List<EntityValue>) {
    if (em.name != "QsPutOrder") return
    for (ev in evList) {
      if (ev[FieldMeta.FIELD_ORDER_STATE] == "Done") {
        highTimeSensitiveExecutor.submitCatch("后处理装货单完成", logger) {
          donePut(EntityHelper.mustGetId(ev))
        }
      }
    }
  }

  override fun afterUpdating(em: EntityMeta, changes: List<EntityChange>) {
    if (em.name != "QsPutOrder") return
    for (c in changes) {
      val oldEv = c.oldValue ?: continue
      val newEv = c.newValue ?: continue
      if (oldEv[FieldMeta.FIELD_ORDER_STATE] != "Done" && newEv[FieldMeta.FIELD_ORDER_STATE] == "Done") {
        highTimeSensitiveExecutor.submitCatch("后处理装货单完成", logger) {
          donePut(c.id)
        }
      }
    }
  }

  // 装货单创建库存、移动库存；解绑原库位（如果有）；绑定新库位；修正容器空满状态
  private fun donePut(orderId: String) = ResLockService.resLock.withLock {
    val order = EntityRwService.findOneById("QsPutOrder", orderId) ?: return // 不会发生
    logger.info("后处理装货单完成：$order")

    val containerId = order["container"] as String
    val binId = order["bin"] as String? // 上架库位

    val containerEv = BinContainerInvReadService.mustGetContainer(containerId)
    val containerCurrentBinId = containerEv["bin"] as String?

    // 如果填了上架库位，且容器内有库存，且与之前不同，移动容器、库存
    if (!binId.isNullOrBlank() && binId != containerCurrentBinId) {
      BinContainerInvUpdateService.moveContainerToBin(containerId, binId)
    }

    // 创建新库存
    val lines = EntityHelper.getLines(order, FieldMeta.FIELD_LINES)
    if (!lines.isNullOrEmpty()) {
      val invLayouts = lines.map { line ->
        val il: EntityValue = mutableMapOf()
        il.putAll(line)
        il.remove("id")
        il.remove("inboundOn")

        // TODO 暂不考虑多层装
        il[FieldMeta.FIELD_TOP_CONTAINER] = containerId
        il[FieldMeta.FIELD_LEAF_CONTAINER] = containerId
        il["bin"] = StringUtils.firstNonBlank(binId, containerCurrentBinId)
        il
      }
      // 先用原来的机制
      CreateInvService.fixCreateInvLayout(invLayouts)
    }

    // 清工作状态
    ContainerUpdateService.clearContainerWorkStatus(containerId)

    // 修正容器空满状态
    BinContainerInvUpdateService.fixContainerFilled(containerId)

    // TODO 剥离原本的代码
    // if (BinInvService.sysEnabled()) {
    //   val invLayouts = EntityRwService.findMany("FbInvLayout", Cq.eq(FieldMeta.FIELD_TOP_CONTAINER, containerId))
    // BinInvService.updateBinInv(QsBzType.Put, binId, containerId, invLayouts)
    // } else {
    // TODO 原本的 invLayouts container ， container bin
    // }
  }
}