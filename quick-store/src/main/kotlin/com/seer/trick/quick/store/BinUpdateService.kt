package com.seer.trick.quick.store

import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import org.slf4j.LoggerFactory

/**
 * 所有对库位的更新放在这里
 */
@Deprecated("即将删除")
object BinUpdateService {
  
  private val logger = LoggerFactory.getLogger(this::class.java)
  
  /**
   * 标记库位要被运走
   */
  fun makeBinToUnload(binId: String, purpose: String) {
    val binEv = BinContainerInvReadService.mustGetBin(binId)
    logger.info("标记库位要被运走（loadStatus=ToUnload）。库位=$binId，目的=$purpose。当前库位状态=$binEv")
    
    EntityRwService.updateOne(
      "FbBin", Cq.idEq(binId),
      mutableMapOf("loadStatus" to "ToUnload", "purpose" to purpose)
    )
  }
  
  /**
   * 标记库位要被运来
   */
  fun makeBinToLoad(binId: String, pendingContainer: String, purpose: String) {
    val binEv = BinContainerInvReadService.mustGetBin(binId)
    logger.info(
      "标记库位要被运来（loadStatus=ToUnload）。库位=$binId，要远来的容器=$pendingContainer，目的=$purpose。当前库位状态=$binEv"
    )
    
    EntityRwService.updateOne(
      "FbBin", Cq.idEq(binId),
      mutableMapOf("loadStatus" to "ToLoad", "pendingContainer" to pendingContainer, "purpose" to purpose)
    )
  }
  
  fun findEmptyBinInDistrict(
    districtId: String, pendingContainer: String?, purpose: String?
  ): EntityValue? {
    val binEv = EntityRwService.findOne(
      "FbBin", Cq.and(listOf(Cq.eq("district", districtId), Cq.eq("loadStatus", "Empty")))
    ) ?: return null
    
    EntityRwService.updateOne(
      "FbBin", Cq.idEq(EntityHelper.mustGetId(binEv)),
      mutableMapOf("loadStatus" to "ToLoad", "pendingContainer" to pendingContainer, "purpose" to purpose)
    )
    
    return binEv
  }
  
  fun mustGetDistrict(districtId: String): EntityValue {
    return EntityRwService.findOneById("FbDistrict", districtId) ?: throw BzError("errNoDistrictById", districtId)
  }
  
}