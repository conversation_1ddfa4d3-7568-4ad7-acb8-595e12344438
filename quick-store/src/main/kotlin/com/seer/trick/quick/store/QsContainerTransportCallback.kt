package com.seer.trick.quick.store

import com.seer.trick.bz.cto.ContainerTransportCallback
import com.seer.trick.bz.cto.CtoDto
import org.slf4j.LoggerFactory

/**
 * 容器搬运，回调
 */
object QsContainerTransportCallback : ContainerTransportCallback() {

  private val logger = LoggerFactory.getLogger(this::class.java)

  // 出库，取货后
  // 将库存、容器转移到机器人身上；标记起点为空
  override fun afterLoad(order: CtoDto) {
    if (!(order.postProcessMark == "Outbound" || order.postProcessMark == "MoveBin")) return

    val containerId = order.container
    if (containerId.isNullOrBlank()) {
      logger.error("容器搬运单 ${order.id} 的容器为空")
      return
    }
    val fromBin = order.fromBin
    if (fromBin.isNullOrBlank()) {
      logger.error("容器搬运单 ${order.id} 的起点库位为空")
      return
    }

    val robotName = order.robotName
    if (robotName.isNullOrBlank()) {
      logger.error("容器搬运单 ${order.id} 的机器人为空")
      return
    }

    BinContainerInvUpdateService.moveContainerFromBinToRobot(containerId, fromBin, robotName)
  }

  // 出库 卸货后
  // 将库存、容器转移到终点；标记终点库位
  // 注意不删除库存，下架时删除
  override fun afterUnload(order: CtoDto) {
    if (!(order.postProcessMark == "Outbound" || order.postProcessMark == "MoveBin")) return

    val containerId = order.container
    if (containerId.isNullOrBlank()) {
      logger.error("容器搬运单 ${order.id} 的容器为空")
      return
    }
    val toBin = order.toBin
    if (toBin.isNullOrBlank()) {
      logger.error("容器搬运单 ${order.id} 的终点库位为空")
      return
    }

    BinContainerInvUpdateService.moveContainerToBin(containerId, toBin)
  }

  // 兜底，如果之前没有收到 Load 或 Unload，这边做兜底处理
  override fun onDone(order: CtoDto) {
    if (!(order.postProcessMark == "Outbound" || order.postProcessMark == "MoveBin")) return

    if (!order.loaded) {
      logger.warn("容器搬运单 ${order.id} 成功，但 load 未标记，额外处理一次取货")
      afterLoad(order)
    }
    if (!order.unloaded) {
      logger.warn("容器搬运单 ${order.id} 成功，但 unloaded 未标记，额外处理一次卸货")
      afterUnload(order)
    }
  }

  // TODO 如果未取货，取消库存分配；如果已取货，人工完成
  override fun onCancelled(order: CtoDto) {
    if (order.postProcessMark != "Outbound") return
  }

}