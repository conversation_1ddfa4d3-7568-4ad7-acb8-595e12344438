package com.seer.trick.quick.store.falcon.inv

import com.seer.trick.BzError
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.bz.wms.inv.ReduceInvFromBinReq
import com.seer.trick.bz.wms.inv.ReduceInvService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.falcon.domain.ParamObjectType
import com.seer.trick.helper.NumHelper

/**
 * 按单据从库位删除库存
 */
class QsReduceBinInvFromOrderBp : AbstractBp() {

  @Suppress("UNCHECKED_CAST")
  override fun process() {
    val order = mustGetBlockInputParam("ev") as EntityValue
    val materialField = mustGetBlockInputParam("materialField") as String
    val qtyField = mustGetBlockInputParam("qtyField") as String
    val binField = mustGetBlockInputParam("binField") as String
    val subContainerIdField = getBlockInputParam("subContainerIdField") as String?
    val outboundOrderIdField = getBlockInputParam("outboundOrderIdField") as String?
    val pickOrderIdField = getBlockInputParam("pickOrderIdField") as String?

    val material = order[materialField] as String
    val qty = NumHelper.anyToDouble(order[qtyField]) ?: throw BzError("errMissingParam", "qty")
    val bin = order[binField] as String
    val subContainerId = NumHelper.anyToInt(order[subContainerIdField])
    val outboundOrderId = order[outboundOrderIdField] as String? ?: ""
    val pickOrderId = order[pickOrderIdField] as String? ?: ""

    ReduceInvService.reduceInvFromBin(
      ReduceInvFromBinReq(
        material = material,
        qty = qty,
        bin = bin,
        subContainerId = subContainerId,
        outboundOrderId = outboundOrderId,
        pickOrderId = pickOrderId,
      ),
    )
  }

  companion object {

    val def = BlockDef(
      QsReduceBinInvFromOrderBp::class.simpleName!!,
      color = "#A8D297",
      inputParams = listOf(
        BlockInputParamDef("ev", BlockParamType.JSONObject, true),
        BlockInputParamDef(
          "materialField",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.EntityFieldName),
        ),
        BlockInputParamDef(
          "qtyField",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.EntityFieldName),
        ),
        BlockInputParamDef(
          "binField",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.EntityFieldName),
        ),
        BlockInputParamDef(
          "subContainerIdField",
          BlockParamType.String,
          false,
          objectTypes = listOf(ParamObjectType.EntityFieldName),
        ),
        BlockInputParamDef(
          "outboundOrderIdField",
          BlockParamType.String,
          false,
          objectTypes = listOf(ParamObjectType.EntityFieldName),
        ),
        BlockInputParamDef(
          "pickOrderIdField",
          BlockParamType.String,
          false,
          objectTypes = listOf(ParamObjectType.EntityFieldName),
        ),
      ),
    )
  }
}