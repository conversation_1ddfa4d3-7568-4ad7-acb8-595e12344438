package com.seer.trick.quick.store.falcon.inv

import com.seer.trick.Cq
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*

/**
 * 按容器删除库存明细
 */
class QsRemoveInvByContainerBp : AbstractBp() {

  override fun process() {
    val container = mustGetBlockInputParam("containerId") as String

    val c = EntityRwService.removeMany("FbInvLayout", Cq.eq(FieldMeta.FIELD_LEAF_CONTAINER, container))
    logger.info("QsRemoveInvByContainerBp remove $c rows invLayout")
//    if (c > 0) EntityRwService.updateOne("FbContainer", Cq.idEq(container), mutableMapOf("filled" to false))
    setBlockOutputParams(mapOf("count" to c))
  }

  companion object {
    val def = BlockDef(
      QsRemoveInvByContainerBp::class.simpleName!!,
      color = "#FFF9C9",
      inputParams = listOf(
        BlockInputParamDef(
          "containerId",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.ContainerId),
        ),
      ),
      outputParams = listOf(BlockOutputParamDef("count", BlockParamType.Long)),
    )
  }
}