package com.seer.trick.quick.store.falcon.bc


import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*
import com.seer.trick.quick.store.base.QsBaseReadService

class QsGetBinContainerBp : AbstractBp() {

  override fun process() {
    val binId = mustGetBlockInputParam(ipBinId.name) as String
    val binInv = QsBaseReadService.mustGetBinInvByBin(binId)

    val containerId = binInv["topContainer"] as String?
    if (containerId.isNullOrBlank()) {
      setBlockOutputParams(mapOf(opBinEmpty.name to true, opContainerId.name to null))
    } else {
      setBlockOutputParams(mapOf(opBinEmpty.name to false, opContainerId.name to containerId))
    }
  }

  companion object {
    val ipBinId = BlockInputParamDef(
      "binId",
      BlockParamType.String,
      true,
      objectTypes = listOf(ParamObjectType.BinId),
    )

    val opBinEmpty = BlockOutputParamDef("binEmpty", BlockParamType.Boolean)
    val opContainerId = BlockOutputParamDef(
      "containerId",
      BlockParamType.String,
      objectTypes = listOf(ParamObjectType.ContainerId),
    )

    val def = BlockDef(
      QsGetBinContainerBp::class.simpleName!!,
      color = "#f8fdcb",
      inputParams = listOf(ipBinId),
      outputParams = listOf(
        opBinEmpty,
        opContainerId,
      ),
    )
  }
}