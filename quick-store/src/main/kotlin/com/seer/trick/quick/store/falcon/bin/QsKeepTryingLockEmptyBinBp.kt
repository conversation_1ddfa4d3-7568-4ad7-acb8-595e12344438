package com.seer.trick.quick.store.falcon.bin

import com.seer.trick.Cq
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.falcon.domain.ParamObjectType
import com.seer.trick.quick.store.base.QsResApplication
import com.seer.trick.quick.store.base.QsResAssignmentService
import com.seer.trick.quick.store.base.QsResAssignmentService.MARK_BIN_LOCKED
import com.seer.trick.quick.store.base.QsResType

/**
 * 等待库位为空并锁定
 */
class QsKeepTryingLockEmptyBinBp : AbstractBp() {

  override fun process() {
    val binId = mustGetBlockInputParam("binId") as String
    val bzDesc = getBlockInputParam("bzDesc") as String? ?: ""

    val tx = QsResAssignmentService.begin()
    val app = QsResApplication(
      tx,
      QsResType.Bin,
      num = 1,
      filter = Cq.and(Cq.eq("bin", binId), Cq.ne("binFilled", true)),
      sort = listOf(),
      bzMark = MARK_BIN_LOCKED,
      bzDesc = bzDesc,
    )
    QsResAssignmentService.request(app)

    if (tx.bins.size == 1) {
      QsResAssignmentService.commit(tx)
    } else {
      QsResAssignmentService.rollback(tx)
    }

    addRelatedObject("FbBin", binId, null)
  }

  companion object {

    val def = BlockDef(
      QsKeepTryingLockEmptyBinBp::class.simpleName!!,
      color = "#A5B6DD",
      inputParams = listOf(
        BlockInputParamDef("binId", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.BinId)),
        BlockInputParamDef("bzDesc", BlockParamType.String, false),
      ),
    )
  }
}