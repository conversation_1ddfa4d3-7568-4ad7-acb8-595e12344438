package com.seer.trick.quick.store

import com.seer.trick.base.AppModule
import com.seer.trick.base.entity.service.extension.EntityServiceExtensions
import com.seer.trick.base.http.WebSocketManager
import com.seer.trick.base.stats.manila.ManilaReportService
import com.seer.trick.bz.BzScript
import com.seer.trick.bz.cto.ContainerTransportService
import com.seer.trick.bz.falcon.BzFalconResourceCleaner
import com.seer.trick.bz.falcon.bp.bc.BindBinContainerBp
import com.seer.trick.bz.falcon.bp.bc.GetBinContainerBp
import com.seer.trick.bz.falcon.bp.bc.GetContainerBinBp
import com.seer.trick.bz.falcon.bp.bc.UnbindBinContainerBp
import com.seer.trick.bz.falcon.bp.bin.FindNotOccupiedBinBp
import com.seer.trick.bz.falcon.bp.bin.KeepTryingLockBinBp
import com.seer.trick.bz.falcon.bp.bin.KeepTryingLockNotOccupiedBinBp
import com.seer.trick.bz.falcon.bp.bin.LockBinOnceBp
import com.seer.trick.bz.falcon.bp.bin.SetBinNotOccupiedBp
import com.seer.trick.bz.falcon.bp.bin.SetBinUnLockBp
import com.seer.trick.bz.falcon.bp.container.CreateTraceContainerBp
import com.seer.trick.bz.falcon.bp.container.FindEmptyContainerBp
import com.seer.trick.bz.falcon.bp.container.GetContainerTypeStoreDistrictsBp
import com.seer.trick.bz.falcon.bp.container.RemoveTraceContainerBp
import com.seer.trick.bz.falcon.bp.inv.CreateInvFromOrderBp
import com.seer.trick.bz.falcon.bp.inv.CreateInvFromOrderLinesBp
import com.seer.trick.bz.falcon.bp.inv.GetContainerInvBp
import com.seer.trick.bz.falcon.bp.inv.MoveInvByContainerBp
import com.seer.trick.bz.falcon.bp.inv.ReduceBinInvFromOrderBp
import com.seer.trick.bz.falcon.bp.inv.RemoveInvByContainerBp
import com.seer.trick.bz.handler.BinHandler
import com.seer.trick.bz.handler.BzWsManager
import com.seer.trick.bz.handler.OrderHandler
import com.seer.trick.bz.handler.UpOrderHandler
import com.seer.trick.bz.handler.WmsHandler
import com.seer.trick.bz.order.OrderService
import com.seer.trick.bz.stats.BzStatsService
import com.seer.trick.bz.wms.BinOverviewService
import com.seer.trick.bz.wms.CallContainerService
import com.seer.trick.bz.wms.CountService
import com.seer.trick.bz.wms.DirectPutawayOrderService
import com.seer.trick.bz.wms.OutboundService
import com.seer.trick.bz.wms.PickService
import com.seer.trick.bz.wms.inv.InvLayoutExtensionService
import com.seer.trick.falcon.FalconCenter
import com.seer.trick.falcon.domain.BlockDefGroup
import com.seer.trick.quick.store.base.QsResAssignmentService
import com.seer.trick.quick.store.base.SyncBinToBinInvService
import com.seer.trick.quick.store.base.SyncContainerToBinInvService
import com.seer.trick.quick.store.base.SyncInvLayoutToBinInvService
import com.seer.trick.quick.store.falcon.QsFalconResourceCleaner
import com.seer.trick.quick.store.falcon.bc.QsBindBinContainerBp
import com.seer.trick.quick.store.falcon.bc.QsGetBinContainerBp
import com.seer.trick.quick.store.falcon.bc.QsGetContainerBinBp
import com.seer.trick.quick.store.falcon.bc.QsUnbindBinContainerBp
import com.seer.trick.quick.store.falcon.bin.*
import com.seer.trick.quick.store.falcon.container.QsCreateTraceContainerBp
import com.seer.trick.quick.store.falcon.container.QsFindEmptyContainerBp
import com.seer.trick.quick.store.falcon.container.QsGetContainerTypeStoreDistrictsBp
import com.seer.trick.quick.store.falcon.container.QsRemoveTraceContainerBp
import com.seer.trick.quick.store.falcon.inv.*
import com.seer.trick.quick.store.stats.InvSnapShotService
import com.seer.trick.quick.store.stats.QsStatsService
import org.graalvm.polyglot.Value

object QuickStoreModule : AppModule() {

  init {
    EntityServiceExtensions.addExtension(OrderService)
    EntityServiceExtensions.addExtension(BinOverviewService)
    EntityServiceExtensions.addExtension(PickService)
    EntityServiceExtensions.addExtension(OutboundService)
    EntityServiceExtensions.addExtension("CallEmptyContainerOrder", CallContainerService)
    EntityServiceExtensions.addExtension("ContainerTransportOrder", ContainerTransportService)
    EntityServiceExtensions.addExtension("FbCountOrder", CountService)
    EntityServiceExtensions.addExtension("FbCountTask", CountService)
    EntityServiceExtensions.addExtension("FbDirectPutawayOrder", DirectPutawayOrderService)
    EntityServiceExtensions.addExtension("FbInvLayout", InvLayoutExtensionService)
  }

  override fun registerMoreBp() {
    val binBlocks = listOf(
      FalconCenter.registerBp(FindNotOccupiedBinBp::class, FindNotOccupiedBinBp.def),
      FalconCenter.registerBp(SetBinNotOccupiedBp::class, SetBinNotOccupiedBp.def),
      FalconCenter.registerBp(KeepTryingLockBinBp::class, KeepTryingLockBinBp.def),
      FalconCenter.registerBp(KeepTryingLockNotOccupiedBinBp::class, KeepTryingLockNotOccupiedBinBp.def),
      FalconCenter.registerBp(LockBinOnceBp::class, LockBinOnceBp.def),
      FalconCenter.registerBp(SetBinUnLockBp::class, SetBinUnLockBp.def),
      FalconCenter.registerBp(QsFindEmptyBinBp::class, QsFindEmptyBinBp.def),
      FalconCenter.registerBp(QsKeepTryingLockBinBp::class, QsKeepTryingLockBinBp.def),
      FalconCenter.registerBp(QsKeepTryingLockEmptyBinBp::class, QsKeepTryingLockEmptyBinBp.def),
      FalconCenter.registerBp(QsLockBinOnceBp::class, QsLockBinOnceBp.def),
      FalconCenter.registerBp(QsRemoveBzMarkBp::class, QsRemoveBzMarkBp.def),
      FalconCenter.registerBp(QsTakeOffContainerBp::class, QsTakeOffContainerBp.def),
    )
    binBlocks.forEach { it.color = "rgba(182,233,252,1)" }
    FalconCenter.registerBpGroup(BlockDefGroup("Bin", "", 45, false, binBlocks))

    val containerBlocks = listOf(
      FalconCenter.registerBp(FindEmptyContainerBp::class, FindEmptyContainerBp.def),
      FalconCenter.registerBp(CreateTraceContainerBp::class, CreateTraceContainerBp.def),
      FalconCenter.registerBp(RemoveTraceContainerBp::class, RemoveTraceContainerBp.def),
      FalconCenter.registerBp(GetContainerTypeStoreDistrictsBp::class, GetContainerTypeStoreDistrictsBp.def),
      FalconCenter.registerBp(QsCreateTraceContainerBp::class, QsCreateTraceContainerBp.def),
      FalconCenter.registerBp(QsFindEmptyContainerBp::class, QsFindEmptyContainerBp.def),
      FalconCenter.registerBp(QsGetContainerTypeStoreDistrictsBp::class, QsGetContainerTypeStoreDistrictsBp.def),
      FalconCenter.registerBp(QsRemoveTraceContainerBp::class, QsRemoveTraceContainerBp.def),
    )
    containerBlocks.forEach { it.color = "rgba(182,233,252,0.7)" }
    FalconCenter.registerBpGroup(BlockDefGroup("Container", "", 50, false, containerBlocks))

    val binContainerBlocks = listOf(
      FalconCenter.registerBp(BindBinContainerBp::class, BindBinContainerBp.def),
      FalconCenter.registerBp(UnbindBinContainerBp::class, UnbindBinContainerBp.def),
      FalconCenter.registerBp(GetContainerBinBp::class, GetContainerBinBp.def),
      FalconCenter.registerBp(GetBinContainerBp::class, GetBinContainerBp.def),
    )
    binContainerBlocks.forEach { it.color = "rgba(182,233,252,0.5)" }
    FalconCenter.registerBpGroup(
      BlockDefGroup("BinContainer", "", 55, false, binContainerBlocks),
    )

    val invBlocks = listOf(
      FalconCenter.registerBp(CreateInvFromOrderLinesBp::class, CreateInvFromOrderLinesBp.def),
      FalconCenter.registerBp(CreateInvFromOrderBp::class, CreateInvFromOrderBp.def),
      FalconCenter.registerBp(MoveInvByContainerBp::class, MoveInvByContainerBp.def),
      FalconCenter.registerBp(GetContainerInvBp::class, GetContainerInvBp.def),
      FalconCenter.registerBp(RemoveInvByContainerBp::class, RemoveInvByContainerBp.def),
      FalconCenter.registerBp(ReduceBinInvFromOrderBp::class, ReduceBinInvFromOrderBp.def),
      FalconCenter.registerBp(QsCreateInvFromOrderBp::class, QsCreateInvFromOrderBp.def),
      FalconCenter.registerBp(QsCreateInvFromOrderLinesBp::class, QsCreateInvFromOrderLinesBp.def),
      FalconCenter.registerBp(QsGetContainerInvBp::class, QsGetContainerInvBp.def),
      FalconCenter.registerBp(QsMoveInvByContainerBp::class, QsMoveInvByContainerBp.def),
      FalconCenter.registerBp(QsReduceBinInvFromOrderBp::class, QsReduceBinInvFromOrderBp.def),
      FalconCenter.registerBp(QsRemoveInvByContainerBp::class, QsRemoveInvByContainerBp.def),
    )
    invBlocks.forEach { it.color = "rgba(182,233,252,0.3)" }
    FalconCenter.registerBpGroup(BlockDefGroup("Inv", "", 60, false, invBlocks))

    val bcBlocks = listOf(
      FalconCenter.registerBp(QsBindBinContainerBp::class, QsBindBinContainerBp.def),
      FalconCenter.registerBp(QsUnbindBinContainerBp::class, QsUnbindBinContainerBp.def),
      FalconCenter.registerBp(QsGetBinContainerBp::class, QsGetBinContainerBp.def),
      FalconCenter.registerBp(QsGetContainerBinBp::class, QsGetContainerBinBp.def),
    )
    bcBlocks.forEach { it.color = "rgba(243,157,244,0.85)" }
    FalconCenter.registerBpGroup(BlockDefGroup("QuickStoreBc", "", 41, false, bcBlocks))
  }

  override fun afterScript() {
    CallContainerService.init()
    OutboundService.init()
    BinOverviewService.init()

    EntityServiceExtensions.addExtension("QsPutOnContainerOrder", QsPutOnContainerService)
    EntityServiceExtensions.addExtension("QsTakeOffContainerOrder", QsTakeOffContainerService)
    EntityServiceExtensions.addExtension("QsPutOrder", QsPutService)
    EntityServiceExtensions.addExtension("QsPickOrder", QsPickService)
    EntityServiceExtensions.addExtension("QsMoveBinOrder", QsMoveBinService)
    EntityServiceExtensions.addExtension("FbBin", SyncBinToBinInvService)
    EntityServiceExtensions.addExtension("FbContainer", SyncContainerToBinInvService)
    EntityServiceExtensions.addExtension("FbInvLayout", SyncInvLayoutToBinInvService)

    QsOutboundService.init()
    QsCallContainerService.init()

    QsStatsService.init()
    InvSnapShotService.init()

    ContainerTransportService.addCallback(QsContainerTransportCallback)

    QsResAssignmentService.init()
  }

  override fun putMoreScriptBindings(bindings: Value) {
    bindings.putMember("bz", BzScript)
  }

  override fun beforeScript() {
    BzFalconResourceCleaner.init()
    QsFalconResourceCleaner.init()
  }

  override fun registerStatsChart() {
    ManilaReportService.reportGenerators.add(QsStatsService::genReportQuickStore)
    BzStatsService.register()
  }

  override fun registerHttpHandlers() {
    QsWsManager.registerHandlers()

    OrderHandler.registerHandlers()
    UpOrderHandler.registerHandlers()
    WmsHandler.registerHandlers()
    BinHandler.registerHandlers()

    WebSocketManager.subscribers += BzWsManager
  }

  override fun dispose() {
    QsStatsService.dispose()
    InvSnapShotService.dispose()
    ManilaReportService.reportGenerators.remove(QsStatsService::genReportQuickStore)
  }
}