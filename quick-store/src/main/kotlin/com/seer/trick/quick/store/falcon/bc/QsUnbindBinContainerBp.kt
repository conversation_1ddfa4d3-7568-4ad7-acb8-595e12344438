package com.seer.trick.quick.store.falcon.bc

import com.seer.trick.Cq

import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.falcon.domain.ParamObjectType
import com.seer.trick.quick.store.base.QsBaseUpdateService

class QsUnbindBinContainerBp : AbstractBp() {

  override fun process() {
    val binId = mustGetBlockInputParam("binId") as String
    val containerId = mustGetBlockInputParam("containerId") as String
    val unlockBin = getBlockInputParamAsBool("unlockBin")
    val unlockContainer = getBlockInputParamAsBool("unlockContainer")
    val removingInv = getBlockInputParamAsBool("removingInv")

    QsBaseUpdateService.takeOffContainer(containerId, binId, removingInv)
    if (unlockBin) QsBaseUpdateService.removeBzMark(Cq.eq("bin", binId))
    if (unlockContainer) QsBaseUpdateService.removeBzMark(Cq.eq("topContainer", containerId))

    addRelatedObject("FbBin", binId, null)
    addRelatedObject("FbContainer", containerId, null)
  }

  companion object {

    val def = BlockDef(
      QsUnbindBinContainerBp::class.simpleName!!,
      color = "#9EC8B9",
      inputParams = listOf(
        BlockInputParamDef(
          "binId",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.BinId),
        ),
        BlockInputParamDef(
          "containerId",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.ContainerId),
        ),
        BlockInputParamDef(
          "unlockBin",
          BlockParamType.Boolean,
          false,
        ),
        BlockInputParamDef(
          "unlockContainer",
          BlockParamType.Boolean,
          false,
        ),
        BlockInputParamDef(
          "removingInv",
          BlockParamType.Boolean,
          false,
        ),
      ),
    )
  }
}