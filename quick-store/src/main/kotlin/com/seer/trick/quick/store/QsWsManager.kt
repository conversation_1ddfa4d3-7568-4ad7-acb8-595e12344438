package com.seer.trick.quick.store

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.ComplexQuery
import com.seer.trick.Cq

import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.http.WebSocketManager
import com.seer.trick.base.http.WebSocketSubscriber
import com.seer.trick.base.http.WsEnv
import com.seer.trick.base.http.WsMsg
import com.seer.trick.helper.JsonHelper
import io.javalin.websocket.WsMessageContext

object QsWsManager : WebSocketSubscriber() {

  fun registerHandlers() {
    WebSocketManager.subscribers += this
  }

  override fun onMessage(ctx: WsMessageContext, msg: WsMsg, env: WsEnv) {
    when (msg.action) {
      "QsWorkStation::Query" -> onQsWorkStation(ctx, msg)
    }
  }

  private fun onQsWorkStation(ctx: WsMessageContext, msg: WsMsg) {
    val query: QsWorkStationQuery = JsonHelper.mapper.readValue(msg.content, jacksonTypeRef()) ?: return
    
    val myBins = if (query.binCq != null) {
      EntityRwService.findMany("FbBin", query.binCq)
    } else {
      null
    }

    val binIds = myBins?.map(EntityHelper::mustGetId)

    val districts = EntityRwService.findMany("FbDistrict", Cq.all())

    var putOrders: List<EntityValue>? = null
    var pickOrders: List<EntityValue>? = null
    var ctOrders: List<EntityValue>? = null

    if (!binIds.isNullOrEmpty()) {
      putOrders = EntityRwService.findMany(
        
        "QsPutOrder",
        Cq.and(listOf(Cq.eq(FieldMeta.FIELD_ORDER_STATE, "Todo"), Cq.include("targetBin", binIds))),
        FindOptions(sort = listOf("+createdOn")),
      )
      pickOrders = EntityRwService.findMany(
        
        "QsPickOrder",
        Cq.and(listOf(Cq.eq(FieldMeta.FIELD_ORDER_STATE, "Todo"), Cq.include("targetBin", binIds))),
        FindOptions(sort = listOf("+createdOn")),
      )
      ctOrders = EntityRwService.findMany(
        
        "ContainerTransportOrder",
        Cq.and(
          listOf(
            Cq.include("status", listOf("Building", "Created", "Assigned", "Failed")),
            Cq.or(listOf(Cq.include("fromBin", binIds), Cq.include("toBin", binIds))),
          ),
        ),
        FindOptions(sort = listOf("+createdOn")),
      )
    }

    val res = mapOf(
      "myBins" to myBins,
      "districts" to districts,
      "putOrders" to putOrders,
      "pickOrders" to pickOrders,
      "ctOrders" to ctOrders,
    )
    ctx.send(WsMsg.json("QsWorkStation::Reply", res))
  }

  data class QsWorkStationQuery(val workStationName: String, val binCq: ComplexQuery? = null)
}