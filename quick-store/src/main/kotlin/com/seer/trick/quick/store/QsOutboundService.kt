package com.seer.trick.quick.store

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.Cq
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.entity.service.extension.EntityServiceExtension
import com.seer.trick.base.entity.service.extension.EntityServiceExtensions
import com.seer.trick.base.reslock.ResLockService
import com.seer.trick.base.script.ScriptCenter
import com.seer.trick.base.script.ScriptExeRequest
import com.seer.trick.base.script.ScriptTraceContext
import com.seer.trick.falcon.task.FalconTaskDefService
import com.seer.trick.helper.DateHelper
import com.seer.trick.helper.NumHelper
import com.seer.trick.helper.submitLongRun
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.Executors
import kotlin.concurrent.withLock
import kotlin.math.min

/**
 * 出库，库存分配
 */
object QsOutboundService : EntityServiceExtension() {
  
  private val logger = LoggerFactory.getLogger(this::class.java)
  
  fun init() {
    EntityServiceExtensions.addExtension("QsOutboundOrder", this)
    
    Executors.newSingleThreadExecutor().submitLongRun("QS 出库定时处理", logger, {
      var delay = BzConfigManager.getByPathAsLong("ScQuickStore", "outbound", "autoAssignDelay") ?: 0L
      if (delay <= 0) delay = 5000
      delay
    }) {
      tryOutbound()
    }
  }
  
  /**
   * 进行一轮出库库存分配
   */
  private fun tryOutbound() {
    // 每次重新解析，热更新
    if (!BzConfigManager.getByPathAsBoolean("ScQuickStore", "outbound", "autoAssign")) return
    
    val config = parseConfig()
    
    // 列出待处理的出库单
    val orders = listToBeProcessedOutboundOrders()
    if (orders.isNullOrEmpty()) return
    
    // 处理单行，计算目标位置标识。对单行进行排序。只包含待分配数量大于零的单行。
    val loList = sortOrderLines(config, orders)
    
    val ac = AssignmentContext()
    
    ResLockService.resLock.withLock {
      for (lo in loList) {
        if (lo.assignedQty >= lo.outboundQty) continue
        
        // 优先在已找到的容器内找剩余库存
        tryUsedContainers(config, ac, lo)
        if (lo.assignedQty >= lo.outboundQty) continue
        
        // 在新容器中尝试分配
        tryNewContainers(config, ac, lo)
      }
      
      if (ac.usedContainers.isEmpty()) return // 本轮无分配
      
      for (containerId in ac.usedContainers) {
        val cc = ac.containerMap[containerId] ?: continue
        
        // 计算是否是整托出
        val allUsed = decideContainerAllOut(cc, ac)
        
        // 为容器找目标库位，找不到放弃本轮分配和出库
        val targetBinId = findTargetBinId(config, allUsed, containerId)
        if (targetBinId.isNullOrBlank()) {
          // TODO 记录阻塞
          withdrawContainerAssignments(cc)
          continue
        }
        cc.targetBinId = targetBinId
        
        // 更新库存明细已分配数量
        updateInvLayouts(ac, cc)
        
        // 处理容器：工作状态、目标库位
        // TODO 抛异常咋办
        // 整托出就不标记了
        if (!cc.allUsed) {
          ContainerUpdateService.markContainerWorkStatus(containerId, "ToPick", targetBinId)
        }
        
        // 处理起点库位：工作状态、目的
        // TODO 抛异常咋办
        BinUpdateService.makeBinToUnload(cc.fromBinId, "Outbound")
        
        // 处理终点库位：工作状态、目标容器、目的
        // TODO 抛异常咋办
        BinUpdateService.makeBinToLoad(targetBinId, containerId, "Outbound")
        
        // 生成容器搬运单
        createCto(config, containerId, cc.fromBinId, targetBinId)
        
        // 拣货单
        createPickOrder(cc)
      }
    }
    
    // TODO 建立对象间的联系：容器搬运单、拣货单、出库单、库存明细
    
    // 更新单行的 invAssignedQty
    for (lo in loList) {
      lo.line["initInvAssignedQty"] = lo.line["invAssignedQty"]
      lo.line["invAssignedQty"] = lo.assignedQty
    }
    // 更新出库单
    for (order in orders) updateOutboundOrderAfterAssignment(order)
  }
  
  // 列出待分配库存的出库单
  private fun listToBeProcessedOutboundOrders(): List<EntityValue>? =
    if (ScriptCenter.exists("qsListToBeProcessedOutboundOrders")) {
      val orderIds: List<String> = ScriptCenter.execute(
        ScriptExeRequest("qsListToBeProcessedOutboundOrders", arrayOf(ScriptTraceContext())),
        jacksonTypeRef(),
      )
      if (orderIds.isNotEmpty()) {
        EntityRwService.findMany("QsOutboundOrder", Cq.include("id", orderIds))
      } else {
        null
      }
    } else {
      // 状态为已提交，库存都找到为 false
      EntityRwService.findMany(
        "QsOutboundOrder",
        Cq.and(listOf(Cq.eq(FieldMeta.FIELD_ORDER_STATE, "Committed"), Cq.ne("invAssignedAll", true))),
      )
    }
  
  // 对单行进行排序。只包含待分配数量大于零的单行。
  private fun sortOrderLines(
    config: OutboundConfig,
    orders: List<EntityValue>,
  ): List<OutboundLineAndOrder> {
    // 收集出库单行
    val loList: List<OutboundLineAndOrder> = orders.mapNotNull { order ->
      EntityHelper.getLines(order, FieldMeta.FIELD_LINES)?.let { lines ->
        lines.map { line ->
          val qty = NumHelper.anyToDouble(line["qty"]) ?: return@mapNotNull null
          val invAssignedQty = NumHelper.anyToDouble(line["invAssignedQty"]) ?: 0.0
          if (invAssignedQty >= qty) return@mapNotNull null
          val targetKey = decideOutboundLineTargetKey(config, order, line)
          OutboundLineAndOrder(
            line,
            order,
            outboundQty = qty,
            assignedQty = invAssignedQty,
            targetKey = targetKey,
          )
        }
      }
    }.flatten()
    
    if (ScriptCenter.exists("qsSortOutboundLines")) {
      // 对传入对象直接排序
      ScriptCenter.execute(ScriptExeRequest("qsSortOutboundLines", arrayOf(ScriptTraceContext(), loList)))
    } else {
      loList.sortedByDescending {
        NumHelper.anyToInt(it.order["typePriority"]) ?: 0 // 出库单类型优先级，从大到小
      }.sortedByDescending {
        NumHelper.anyToInt(it.order["priority"]) ?: 0 // 出库单优先级，从大到小
      }.sortedByDescending {
        NumHelper.anyToInt(it.line["priority"]) ?: 0 // 出库单行优先级，从大到小
      }.sortedBy {
        DateHelper.anyToDate(it.order["createdOn"]) // 出库单创建时间，从远到近
      }
    }
    
    return loList
  }
  
  // 获取出库目标区域标识
  private fun decideOutboundLineTargetKey(
    config: OutboundConfig,
    order: EntityValue,
    line: EntityValue,
  ): String? {
    return if (ScriptCenter.exists("qsDecideOutboundLineTargetKey")) {
      ScriptCenter.execute(
        ScriptExeRequest("qsDecideOutboundLineTargetKey", arrayOf(ScriptTraceContext(), config, order, line)),
        jacksonTypeRef(),
      )
    } else {
      return StringUtils.firstNonBlank(
        order["targetKey"] as String?,
        order["targetWorkSite"] as String?,
        order["targetDistrict"] as String?,
      )
    }
  }
  
  // 在已用容器中，为出库单行寻找库存
  private fun tryUsedContainers(
    config: OutboundConfig,
    ac: AssignmentContext,
    lo: OutboundLineAndOrder,
  ) {
    if (lo.assignedQty >= lo.outboundQty) return
    
    // 为出库单行加载符合条件的所有库存
    val invLayouts = findAllInvInUsedContainersForOutboundLine(config, ac, lo)
    for (invLayout in invLayouts) {
      val topContainerId = invLayout[FieldMeta.FIELD_TOP_CONTAINER] as String? ?: continue
      val cc = ac.containerMap[topContainerId] ?: continue
      
      if (!tryToAssign(ac, lo, cc)) continue
      
      if (lo.outboundQty - lo.assignedQty <= 0) return
    }
  }
  
  // 在已用容器中，为出库单行加载符合条件的所有库存；首先过滤去往相同区域
  private fun findAllInvInUsedContainersForOutboundLine(
    config: OutboundConfig,
    ac: AssignmentContext,
    lo: OutboundLineAndOrder,
  ): List<EntityValue> {
    return if (ScriptCenter.exists("qsFindAllInvInUsedContainersForOutboundLine")) {
      ScriptCenter.execute(
        ScriptExeRequest("qsFindAllInvInUsedContainersForOutboundLine", arrayOf(ScriptTraceContext(), config, ac, lo)),
        jacksonTypeRef(),
      )
    } else {
      // 默认筛选条件为：
      // - 库存明细的状态为“存储中”。
      // - 物料相同。
      // - 单行上其他与库存明细同名字段，如果有值，需要相等。例如批次字段相同。
      // 默认排序为：
      // - 库存明细，入库时间。（即先进先出）
      ac.usedContainers.asSequence().filter { containerId ->
        val cc = ac.containerMap[containerId] ?: return@filter false
        lo.targetKey == null || cc.targetKey == lo.targetKey
      }.mapNotNull { ac.containerMap[it]?.invLayouts }.flatten().filter {
        if (it[FieldMeta.FIELD_MATERIAL] != lo.line[FieldMeta.FIELD_MATERIAL]) return@filter false
        if (config.invMatchFields.isNotEmpty()) {
          for (fn in config.invMatchFields) {
            val fv = lo.line[fn]
            if (fv != null) if (it[fn] != fv) return@filter false
          }
        }
        true
      }.sortedBy { it["inboundOn"] as Date? }.toList()
    }
  }
  
  // 在新容器中，为出库单行寻找库存
  private fun tryNewContainers(
    config: OutboundConfig,
    ac: AssignmentContext,
    lo: OutboundLineAndOrder,
  ) {
    if (lo.assignedQty >= lo.outboundQty) return
    
    // 为出库单行加载符合条件的所有库存
    val invLayouts = loadAllInvForOutboundLine(config, lo)
    for (invLayout in invLayouts) {
      // val invLayoutId = EntityHelper.mustGetId(invLayout)
      val topContainerId = invLayout[FieldMeta.FIELD_TOP_CONTAINER] as String? ?: continue
      
      // 检查，确定是新容器
      if (ac.usedContainers.contains(topContainerId)) continue
      
      // 容器当前库位
      val fromBin = invLayout["bin"] as String? ?: continue
      
      // 初始化容器和库存使用
      val cc = initContainer(ac, topContainerId, fromBin, lo.targetKey) ?: continue
      
      // 先检查运输能力
      if (!checkContainerTransport(ac, topContainerId)) {
        ac.containerMap[topContainerId]?.noMoreTransport = true
        continue
      }
      
      if (!tryToAssign(ac, lo, cc)) continue
      
      ac.usedContainers += topContainerId
      
      if (lo.outboundQty - lo.assignedQty <= 0) return
    }
  }
  
  // 检查运输能力
  private fun checkContainerTransport(ac: AssignmentContext, topContainerId: String): Boolean =
    if (ScriptCenter.exists("qsCheckContainerTransportForOutbound")) {
      ScriptCenter.execute(
        ScriptExeRequest("qsCheckContainerTransportForOutbound", arrayOf(ScriptTraceContext(), ac, topContainerId)),
        jacksonTypeRef(),
      )
    } else {
      true
    }
  
  // 为出库单行加载符合条件的所有库存
  private fun loadAllInvForOutboundLine(
    config: OutboundConfig,
    lo: OutboundLineAndOrder,
  ): List<EntityValue> = if (ScriptCenter.exists("qsLoadAllInvForOutboundLine")) {
    ScriptCenter.execute(
      ScriptExeRequest("qsLoadAllInvForOutboundLine", arrayOf(ScriptTraceContext(), config, lo)),
      jacksonTypeRef()
    )
  } else {
    // 默认筛选条件为：
    // - 库存明细的状态为“存储中”。
    // - 物料相同。
    // - 单行上其他与库存明细同名字段，如果有值，需要相等。例如批次字段相同。
    // 默认排序为：
    // - 库存明细，入库时间。（即先进先出）
    val cqItems = mutableListOf(
      Cq.eq(FieldMeta.FIELD_MATERIAL, lo.line[FieldMeta.FIELD_MATERIAL]),
    )
    if (config.invMatchFields.isNotEmpty()) {
      for (fn in config.invMatchFields) {
        val fv = lo.line[fn]
        if (fv != null) cqItems += Cq.eq(fn, fv)
      }
    }
    
    EntityRwService.findMany("FbInvLayout", Cq.and(cqItems), FindOptions(sort = listOf("+inboundOn")))
  }
  
  // 初始化容器和库存使用
  private fun initContainer(
    ac: AssignmentContext,
    topContainerId: String,
    fromBinId: String,
    targetKey: String?,
  ): ContainerContext? {
    if (ac.badContainers.contains(topContainerId)) return null
    
    var cc = ac.containerMap[topContainerId]
    if (cc != null) return cc
    
    val containerEv = EntityRwService.findOneById("FbContainer", topContainerId)
      ?: return null
    // 不能正在被处理
    val workStatus = containerEv["workStatus"] as String?
    if (!workStatus.isNullOrBlank()) {
      ac.badContainers += topContainerId
      return null
    }
    
    val cInvLayouts = EntityRwService.findMany(
      "FbInvLayout",
      Cq.eq(FieldMeta.FIELD_TOP_CONTAINER, topContainerId),
    )
    cc = ContainerContext(
      containerId = topContainerId,
      fromBinId = fromBinId,
      targetKey = targetKey,
      invLayouts = cInvLayouts,
    )
    ac.containerMap[topContainerId] = cc
    
    for (il in cInvLayouts) {
      // 初始化已使用数量
      val qty = NumHelper.anyToDouble(il["qty"]) ?: 0.0
      val usedQty = NumHelper.anyToDouble(il["usedQty"]) ?: 0.0
      val ilId = EntityHelper.mustGetId(il)
      val restQty = qty - usedQty
      ac.invLayoutUsedMap[ilId] = InvLayoutUsed(ilId, il, restQty = restQty, oldUsedQty = usedQty)
    }
    
    return cc
  }
  
  // 返回 true 表示有分配
  private fun tryToAssign(ac: AssignmentContext, lo: OutboundLineAndOrder, cc: ContainerContext): Boolean {
    var assigned = false // 是否有过分配
    for (il in cc.invLayouts) {
      val invLayoutId = EntityHelper.mustGetId(il)
      val ilu = ac.invLayoutUsedMap[invLayoutId] ?: continue
      if (ilu.restQty <= 0) continue
      
      // 此库存明细可分配数量
      val q = min(ilu.restQty, lo.outboundQty - lo.assignedQty)
      // 记录分配
      ac.containerMap[cc.containerId]!!.assignments += InvAssigment(lo, ilu, q)
      ilu.usedQty += q
      ilu.restQty -= q
      lo.assignedQty += q
      
      assigned = true
      if (lo.assignedQty >= lo.outboundQty) break
    }
    
    return assigned
  }
  
  // 容器是否整托出
  private fun decideContainerAllOut(cc: ContainerContext, ac: AssignmentContext): Boolean {
    var allUsed = true
    for (il in cc.invLayouts) {
      val ilu = ac.invLayoutUsedMap[EntityHelper.mustGetId(il)] ?: continue
      if (ilu.restQty > 0) allUsed = false
    }
    cc.allUsed = allUsed
    return allUsed
  }
  
  // 寻找出库的目标库位
  private fun findTargetBinId(
    config: OutboundConfig,
    allUsed: Boolean,
    containerId: String,
  ): String? = if (ScriptCenter.exists("qsOutboundFindTargetBinId")) {
    ScriptCenter.execute(
      ScriptExeRequest("qsOutboundFindTargetBinId", arrayOf(ScriptTraceContext(), config, allUsed, containerId)),
      jacksonTypeRef(),
    )
  } else {
    val bin = if (allUsed) {
      if (!config.wholeOutDistrict.isNullOrBlank()) {
        BinContainerInvReadService.listEmptyBinInDistrict(config.wholeOutDistrict)
      } else {
        null
      }
    } else {
      if (!config.pickDistrict.isNullOrBlank()) {
        BinContainerInvReadService.listEmptyBinInDistrict(config.pickDistrict)
      } else {
        null
      }
    }
    bin?.let { EntityHelper.mustGetId(it) }
  }
  
  // 撤回对容器的分配，扣减出库单行已分配数量
  private fun withdrawContainerAssignments(cc: ContainerContext) {
    for (ag in cc.assignments) {
      ag.lo.assignedQty -= ag.q
    }
  }
  
  // 更新库存明细已分配数量
  private fun updateInvLayouts(ac: AssignmentContext, cc: ContainerContext) {
    for (il in cc.invLayouts) {
      val ilId = EntityHelper.mustGetId(il)
      val ilu = ac.invLayoutUsedMap[ilId] ?: continue
      if (ilu.usedQty <= 0) continue
      InvLayoutUpdateService.updateInvLayoutUsedQty(ilu.invLayoutId, ilu.usedQty + ilu.oldUsedQty, ilu.oldUsedQty)
    }
  }
  
  // 生成容器搬运单
  private fun createCto(
    config: OutboundConfig,
    containerId: String,
    fromBinId: String,
    toBinId: String,
  ) {
    val transportOrderStatus = if (config.pendingOut) "Building" else "Created"
    val def = if (!config.containerOutFalconTask.isNullOrBlank()) {
      FalconTaskDefService.mustFetchLatestTaskDefById(config.containerOutFalconTask)
    } else {
      null
    }
    val transportOrder: EntityValue = mutableMapOf(
      "container" to containerId,
      "kind" to "ContainerOut",
      "status" to transportOrderStatus,
      "fromBin" to fromBinId,
      "toBin" to toBinId,
      "falconTaskDefId" to config.containerOutFalconTask, // 用哪个猎鹰任务执行此容器搬运单
      "falconTaskDefLabel" to def?.label,
      "postProcessMark" to "Outbound", // 处理标记
    )
    val id = EntityRwService.createOne("ContainerTransportOrder", transportOrder)
    logger.info("创建容器搬运单 id=$id，$transportOrder")
  }
  
  // 创建装货单
  private fun createPickOrder(cc: ContainerContext) {
    if (cc.allUsed) return
    
    // 分配 -> 拣货单行
    val pickLines = cc.assignments.map { ag ->
      val pickLine: EntityValue = mutableMapOf()
      // 先把单行的字段都拷入
      pickLine.putAll(ag.lo.line)
      // 把库存明细的字段都拷入
      pickLine.putAll(ag.ilu.invLayout)
      
      // 删除特殊含义字段
      pickLine.remove("id")
      pickLine.remove(FieldMeta.FIELD_PARENT_ID)
      pickLine.remove(FieldMeta.FIELD_LINE_NO)
      
      // 期望分拣、实际分拣
      pickLine["planQty"] = ag.q
      pickLine["qty"] = ag.q
      
      pickLine["outboundOrderId"] = ag.lo.order["id"]
      pickLine["outboundLineId"] = ag.lo.line["id"]
      pickLine["outboundLineNo"] = ag.lo.line[FieldMeta.FIELD_LINE_NO]
      pickLine["invLayoutId"] = ag.ilu.invLayoutId
      
      pickLine
    }
    if (pickLines.isEmpty()) return
    
    val pickOrder: EntityValue = mutableMapOf(
      FieldMeta.FIELD_ORDER_STATE to "Todo",
      "container" to cc.containerId,
      "targetBin" to cc.targetBinId,
      FieldMeta.FIELD_LINES to pickLines,
    )
    
    val id = EntityRwService.createOne("QsPickOrder", pickOrder)
    logger.info("创建拣货单 id=$id，$pickOrder")
  }
  
  // 更新出库单。更新出库单的 invAssignedAll 状态，更新单行。
  private fun updateOutboundOrderAfterAssignment(order: EntityValue) {
    val orderId = EntityHelper.mustGetId(order)
    val lines = EntityHelper.getLines(order, FieldMeta.FIELD_LINES)
    var needUpdate = false
    var short = false
    if (lines.isNullOrEmpty()) {
      needUpdate = true
    } else {
      for (line in lines) {
        val qty = NumHelper.anyToDouble(line["qty"]) ?: continue
        val invAssignedQty = NumHelper.anyToDouble(line["invAssignedQty"]) ?: 0.0
        val initInvAssignedQty = NumHelper.anyToDouble(line["initInvAssignedQty"]) ?: 0.0
        if (invAssignedQty < qty) {
          short = true
          //   break
        }
        if (initInvAssignedQty != invAssignedQty) { // TODO compare qty
          needUpdate = true
          logger.info(
            "出库单行 $orderId:${line[FieldMeta.FIELD_LINE_NO]} " +
              "已分配库存数量从 ${line["initInvAssignedQty"]} 变为 ${line["invAssignedQty"]}",
          )
        }
      }
    }
    if (!short) {
      order["invAssignedAll"] = true // 所有库存都找到了
      logger.info("出库单 $orderId 库存分配完成")
    } else {
      logger.info("出库单 $orderId 库存部分分配")
    }
    if (needUpdate) {
      EntityRwService.updateOne("QsOutboundOrder", Cq.idEq(orderId), order)
    }
  }
  
  @Suppress("UNCHECKED_CAST")
  private fun parseConfig(): OutboundConfig {
    val configEv = BzConfigManager.getByPath("ScQuickStore", "outbound") as EntityValue? ?: mutableMapOf()
    
    return OutboundConfig(
      invMatchFields = configEv["invMatchFields"] as List<String>? ?: emptyList(),
      wholeOutDistrict = configEv["wholeOutDistrict"] as String?,
      pickDistrict = configEv["pickDistrict"] as String?,
      pendingOut = configEv["pendingOut"] as Boolean? ?: false,
      containerOutFalconTask = configEv["containerOutFalconTask"] as String?,
    )
  }
  
  data class OutboundLineAndOrder(
    @JvmField
    val line: EntityValue,
    @JvmField
    val order: EntityValue,
    @JvmField
    val outboundQty: Double, // 期望出库数量
    @JvmField
    var assignedQty: Double, // 已分配数量
    @JvmField
    val targetKey: String?, // 目标位置标识
  )
  
  data class OutboundConfig(
    @JvmField
    val invMatchFields: List<String>, // 库存匹配字段
    @JvmField
    val wholeOutDistrict: String?, // 整托出库库区
    @JvmField
    val pickDistrict: String?, // 分拣出库库区
    @JvmField
    val pendingOut: Boolean, // 创建出库容器搬运单的初始状态
    @JvmField
    val containerOutFalconTask: String?, // 处理出库容器搬运单的猎鹰任务
  )
  
  data class AssignmentContext(
    @JvmField
    val badContainers: MutableSet<String> = HashSet(), // 不能用的容器
    @JvmField
    val containerMap: MutableMap<String, ContainerContext> = HashMap(),
    @JvmField
    val invLayoutUsedMap: MutableMap<String, InvLayoutUsed> = HashMap(),
    @JvmField
    val usedContainers: MutableList<String> = ArrayList(), // 本轮分配使用的容器，用数组保证按顺序运输
  )
  
  data class InvLayoutUsed(
    @JvmField
    val invLayoutId: String,
    @JvmField
    val invLayout: EntityValue,
    @JvmField
    var restQty: Double,
    @JvmField
    var oldUsedQty: Double, // 本轮分配前
    @JvmField
    var usedQty: Double = 0.0,
  )
  
  data class ContainerContext(
    @JvmField
    val containerId: String,
    @JvmField
    val fromBinId: String,
    @JvmField
    var targetKey: String?, // 目标位置标识
    @JvmField
    var targetBinId: String? = null, // 目标库位
    @JvmField
    val invLayouts: List<EntityValue>,
    @JvmField
    var allUsed: Boolean = false, // 是否全被用掉了（整托出）
    @JvmField
    var noMoreTransport: Boolean = false, // 标记无运输能力
    @JvmField
    val assignments: MutableList<InvAssigment> = ArrayList(),
  )
  
  data class InvAssigment(val lo: OutboundLineAndOrder, val ilu: InvLayoutUsed, val q: Double)
}