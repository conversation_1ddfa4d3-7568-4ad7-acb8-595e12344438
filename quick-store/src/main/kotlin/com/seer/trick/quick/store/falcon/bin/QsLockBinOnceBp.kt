package com.seer.trick.quick.store.falcon.bin

import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*
import com.seer.trick.quick.store.base.QsResApplication
import com.seer.trick.quick.store.base.QsResAssignmentService
import com.seer.trick.quick.store.base.QsResAssignmentService.MARK_TO_UNLOAD_HERE
import com.seer.trick.quick.store.base.QsResType

/**
 * 锁定一次库位
 */
class QsLockBinOnceBp : AbstractBp() {

  override fun process() {
    val binId = mustGetBlockInputParam("binId") as String
    val notFoundToAborted = getBlockInputParam("notFoundToAborted") as Boolean? ?: false
    val notFoundToFault = getBlockInputParam("notFoundToFault") as Boolean? ?: false
    val bzDesc = getBlockInputParam("bzDesc") as String? ?: ""

    val tx = QsResAssignmentService.begin()
    val app = QsResApplication(
      tx,
      QsResType.Bin,
      timeout = 1,
      num = 1,
      filter = Cq.eq("bin", binId),
      sort = listOf(),
      bzMark = MARK_TO_UNLOAD_HERE,
      bzDesc = bzDesc,
    )
    QsResAssignmentService.request(app)
    if (tx.bins.size == 1) {
      QsResAssignmentService.commit(tx)
      setBlockOutputParams(mutableMapOf("ok" to true))
    } else {
      QsResAssignmentService.rollback(tx)
      if (notFoundToAborted) {
        throw FatalError("Lock bin $binId")
      } else if (notFoundToFault) {
        throw BzError("errLockBinFailed", binId)
      }
      setBlockOutputParams(mutableMapOf("ok" to false))
    }

    addRelatedObject("FbBin", binId, null)
  }

  companion object {

    val def = BlockDef(
      QsLockBinOnceBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(
        BlockInputParamDef("binId", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.BinId)),
        BlockInputParamDef("notFoundToFault", BlockParamType.Boolean, false),
        BlockInputParamDef("notFoundToAborted", BlockParamType.Boolean, false),
        BlockInputParamDef("bzDesc", BlockParamType.String, false),
      ),
      outputParams = listOf(
        BlockOutputParamDef("ok", BlockParamType.Boolean),
      ),
    )
  }
}