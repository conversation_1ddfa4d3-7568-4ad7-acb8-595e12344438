package com.seer.trick.quick.store.base

import com.seer.trick.BzError
import com.seer.trick.ComplexQuery
import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.helper.IdHelper
import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import java.util.concurrent.locks.ReentrantLock
import kotlin.collections.ArrayList
import kotlin.concurrent.withLock

/**
 * 申请和分配资源（库位、容器、库存）。支持批量申请多个资源（两个空容器）。支持资源事务：多次申请，提交、回滚。
 * 排序：先按组优先级（从大到小）、再按优先级（从大到小），最后按时间
 */
object QsResAssignmentService {

  // fixme 放业务那吧
  // 常用的 btBzMark 类型
  const val MARK_TO_UNLOAD_HERE = "ToUnloadHere"
  const val MARK_BIN_LOCKED = "BinLocked"
  const val MARK_OUT_EMPTY_CONTAINER = "OutEmptyContainer"

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 待分配的申请。id->
   */
  private val pendingApplications: MutableMap<String, QsResApplication> = ConcurrentHashMap()

  /**
   * 已完成的分配结果。app id->
   */
  private val done: MutableMap<String, QsResAssignmentResult> = ConcurrentHashMap()

  /**
   * 停止服务 TODO 暂没用
   */
  @Volatile
  var disposed = false

  private val lock = ReentrantLock()
  private val condition = lock.newCondition()

  fun init() {
    Executors.newSingleThreadExecutor().submit(::loop)
    releaseUnCommited()
  }

  /**
   * 开始事务，返回资源上下文
   */
  fun begin(): QsResTx = QsResTx()

  /**
   * 请求一批资源。阻塞执行。返回申请到的资源对象。
   * 返回 null 表示申请失败，目前就只有超时这一种情况。
   */
  fun request(app: QsResApplication): List<EntityValue>? {
    logger.debug("Request resources: $app")

    // 业务标记不能为空
    if (app.bzMark.isBlank()) throw BzError("errCodeErr", "bzMark is blank")
    if (app.num < 0) throw BzError("errCodeErr", "application.num < 0")

    val tx = app.tx
    if (tx.commitedOrRollback) throw BzError("errCodeErr", "ctx is commited or rollback")
    // 记录到待分配
    pendingApplications[app.id] = app

    // 立即触发一次分配 TODO 如果资源分配卡住
    tryToAssign()

    // 等分配结果
    while (!disposed) {
      lock.withLock {
        val result = done.remove(app.id)
        if (result != null) {
          // 有结果了
          if (result.res != null) {
            // 记录分配结果到上下文
            when (app.type) {
              QsResType.Bin -> tx.bins.addAll(result.res)
              QsResType.Container -> tx.containers.addAll(result.res)
              QsResType.Inventory -> tx.inventories.addAll(result.res)
            }
          }
          return result.res
        }

        // 兼容多线程同时申请多个资源，其中某个线程 commit、rollback 后，其余的资源申请应及时退出
        if (tx.commitedOrRollback) return null

        // TODO 中断
        condition.await(200, TimeUnit.MILLISECONDS)
      }
    }

    return null
  }

  /**
   * 提交资源事务
   */
  fun commit(tx: QsResTx) {
    synchronized(tx) {
      if (tx.commitedOrRollback) throw BzError("errCodeErr", "ctx is commited or rollback")
      tx.commitedOrRollback = true

      // 提交的方法是清除 btPrepare 标记

      // TODO 暂不考虑内部报错

      if (tx.bins.isNotEmpty()) {
        EntityRwService.updateMany(
          "FbBinInv",
          Cq.include("id", tx.bins.map(EntityHelper::mustGetId)),
          commitUpdate(),
        )
      }

      if (tx.containers.isNotEmpty()) {
        EntityRwService.updateMany(
          "FbBinInv",
          Cq.include("id", tx.containers.map(EntityHelper::mustGetId)),
          commitUpdate(),
        )
      }

      if (tx.inventories.isNotEmpty()) {
        EntityRwService.updateMany(
          "FbInvLayout",
          Cq.include("id", tx.inventories.map(EntityHelper::mustGetId)),
          commitUpdate(),
        )
      }
    }
  }

  /**
   * 回滚资源申请
   */
  fun rollback(tx: QsResTx) {
    synchronized(tx) {
      if (tx.commitedOrRollback) throw BzError("errCodeErr", "ctx is commited or rollback")
      tx.commitedOrRollback = true

      // 回滚的方法是清除 btPrepare 标记、业务标记

      // TODO 暂不考虑内部报错

      if (tx.bins.isNotEmpty()) {
        EntityRwService.updateMany(
          "FbBinInv",
          Cq.include("id", tx.bins.map(EntityHelper::mustGetId)),
          rollbackUpdate(),
        )
      }

      if (tx.containers.isNotEmpty()) {
        EntityRwService.updateMany(
          "FbBinInv",
          Cq.include("id", tx.containers.map(EntityHelper::mustGetId)),
          rollbackUpdate(),
        )
      }

      if (tx.inventories.isNotEmpty()) {
        EntityRwService.updateMany(
          "FbInvLayout",
          Cq.include("id", tx.inventories.map(EntityHelper::mustGetId)),
          rollbackUpdate(),
        )
      }
    }
  }

  /**
   * 提交资源分配时，对资源的修改
   */
  private fun commitUpdate(): EntityValue = mutableMapOf(
    FieldMeta.FIELD_PREPARE to 0,
  )

  /**
   * 回滚资源分配时，对资源的修改
   */
  fun rollbackUpdate(): EntityValue = mutableMapOf(
    FieldMeta.FIELD_PREPARE to 0,
    FieldMeta.FIELD_BZ_MARK to "",
    FieldMeta.FIELD_BZ_DESC to "",
  )

  /**
   * 资源分配循环
   */
  private fun loop() {
    while (!disposed) {
      tryToAssign()
      Thread.sleep(500)
    }
  }

  /**
   * 一次资源分配
   * 不能抛异常，TODO 包一层 try-catch
   */
  @Synchronized
  private fun tryToAssign() = QsResService.resLock.withLock {
    // 拷贝一份 TODO 并发修改异常
    val applications = ArrayList(pendingApplications.values)

    // 先按组优先级排序：优先级越大在前面
    applications.sortBy { -it.groupPriority }
    // 再按优先级排序：优先级越大在前面
    applications.sortBy { -it.priority }
    // 再按创建时间排序
    applications.sortBy { it.createdOn }

    var anyDone = false

    // 先删除过期的
    for (app in applications) {
      if (app.timeout > 0 && app.createdOn - System.currentTimeMillis() > app.timeout) {
        // 分配超时，结束
        pendingApplications.remove(app.id)
        done[app.id] = QsResAssignmentResult(null)

        anyDone = true
        continue
      }

      // 资源过滤，未停用、btPrepare=0、业务标记为空
      val q = Cq.and(
        // Cq.ne(FieldMeta.FIELD_DISABLED, true),
        // Cq.ne("containerDisabled", true),
        Cq.or(
          Cq.eq(FieldMeta.FIELD_PREPARE, 0),
          Cq.isNull(FieldMeta.FIELD_PREPARE),
        ),
        Cq.empty(FieldMeta.FIELD_BZ_MARK),
        app.filter,
      )

      val resList: List<EntityValue>? = if (app.type == QsResType.Bin || app.type == QsResType.Container) {
        // 库位或容器
        val resList = EntityRwService.findMany("FbBinInv", q, FindOptions(sort = app.sort, limit = app.num))
        // TODO 这里可能会被被的代码改，尤其是没放到 resLock 里的代码。更好的写法是一条一条的分配，记录 version、id
        if (resList.size == app.num) {
          // 资源满足
          // 第一阶段标记 btPrepare=1
          EntityRwService.updateMany(
            "FbBinInv",
            Cq.include("id", resList.map(EntityHelper::mustGetId)),
            mutableMapOf(
              FieldMeta.FIELD_PREPARE to 1,
              FieldMeta.FIELD_BZ_MARK to app.bzMark, // 业务标记
              FieldMeta.FIELD_BZ_DESC to app.bzDesc,
            ),
          )
          // 计入上下文
          if (app.type == QsResType.Bin) {
            app.tx.bins += resList
          } else {
            app.tx.containers += resList
          }
          resList
        } else {
          null
        }
      } else {
        // 库存 TODO 库存的数量不能这么处理
        // EntityRwService.findMany("FbInvLayout", q, FindOptions(sort = app.sort, limit = app.num))
        null
      }

      if (resList != null) {
        // 成功分配后
        pendingApplications.remove(app.id)
        done[app.id] = QsResAssignmentResult(resList)

        anyDone = true
      }
    }

    // 如果有分配，触发通知
    if (anyDone) {
      lock.withLock {
        condition.signalAll()
      }
    }
  }

  /**
   * 重启后，释放未提交的资源预定
   */
  private fun releaseUnCommited() {
    EntityRwService.updateMany("FbBinInv", Cq.eq(FieldMeta.FIELD_PREPARE, 1), rollbackUpdate())
    EntityRwService.updateMany("FbInvLayout", Cq.eq(FieldMeta.FIELD_PREPARE, 1), rollbackUpdate())
  }
}

/**
 * 资源分配事务
 */
class QsResTx {
  /**
   * 已分配库位（FbBinInv）
   */
  val bins: MutableList<EntityValue> = CopyOnWriteArrayList()

  /**
   * 已分配容器（FbBinInv）
   */
  val containers: MutableList<EntityValue> = CopyOnWriteArrayList()

  /**
   * 已分配库存明细（FbInvLayout）
   */
  val inventories: MutableList<EntityValue> = CopyOnWriteArrayList()

  /**
   * 是否已提交或已回滚
   */
  @Volatile
  var commitedOrRollback = false
}

/**
 * 资源申请单
 */
data class QsResApplication(
  val tx: QsResTx, // 所属事务
  val type: QsResType, // 资源类型
  val groupPriority: Int = 0, // 组优先级，最大优先级越高
  val priority: Int = 0, // 优先级，最大优先级越高
  val timeout: Long = -1, // 超时时间（毫秒），<=0 不超时
  val num: Int = 1, // 申请数量
  val filter: ComplexQuery, // 过滤条件
  val sort: List<String>, // 排序条件
  val bzMark: String, // 业务标记
  val bzDesc: String, // 业务描述
  val id: String = IdHelper.oidStr(),
  val createdOn: Long = System.currentTimeMillis(),
)

/**
 * 资源类型
 */
enum class QsResType {
  Bin, // 库位
  Container, // 容器
  Inventory, // 库存
}

/**
 * 资源分配结果
 */
class QsResAssignmentResult(
  // 已分配的资源业务对象。null 表示分配失败
  val res: List<EntityValue>?,
)