package com.seer.trick.quick.store

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.reslock.ResLockService
import com.seer.trick.base.script.ScriptCenter
import com.seer.trick.base.script.ScriptExeRequest
import com.seer.trick.base.script.ScriptTraceContext
import com.seer.trick.base.soc.SocService
import com.seer.trick.falcon.task.FalconTaskDefService
import com.seer.trick.helper.NumHelper
import com.seer.trick.helper.submitLongRun
import org.slf4j.LoggerFactory
import java.util.concurrent.Executors
import kotlin.concurrent.withLock
import kotlin.math.ceil
import kotlin.math.min

/**
 * 叫空容器
 * 容器可以带格子。
 *
 * 找空容器：筛选和排序
 *
 * 排序：尾箱优先、所在库区、存放位置、先进先出
 *
 * 容器属性：是否全空、容器物料种数、容器物料编号列表、容器总空满率、容器空满格率、容器物料总数、每格子空满率、每个格子物料种数、
 */
object QsCallContainerService {

  private val logger = LoggerFactory.getLogger(this::class.java)

  fun init() {
    Executors.newSingleThreadExecutor().submitLongRun("处理：为入库单叫空容器", logger, {
      var delay = BzConfigManager.getByPathAsInt("ScQuickStore", "callContainer", "autoCallForInboundDelay") ?: 0
      if (delay <= 0) delay = 5000
      delay.toLong()
    }) {
      val enabled = BzConfigManager.getByPath("ScQuickStore", "callContainer", "autoCallForInbound") as Boolean?
      if (enabled == true) {
        SocService.updateNode("业务", "QuickStore:AutoCallForInbound", "QS 为入库单叫空容器", "开始")
        tryCallContainers()
      } else {
        SocService.removeNode("QuickStore:AutoCallForInbound")
      }
    }
  }

  private fun tryCallContainers() {
    val config = parseConfig()

    // 列出所有未成功叫空容器的
    // callContainerAll 是此入库单已全部找到空容器的标志
    // 按创建时间排序
    val orders = EntityRwService.findMany(
      "QsInboundOrder",
      Cq.and(listOf(Cq.eq(FieldMeta.FIELD_ORDER_STATE, "Committed"), Cq.ne("callContainerAll", true))),
      FindOptions(listOf("id"), listOf("createdOn")),
    )
    if (orders.isEmpty()) return

    // 汇总单行
    val loList: MutableList<InboundLineAndOrder> = ArrayList()
    for (order in orders) {
      val lines = EntityHelper.mustGetLines(order, FieldMeta.FIELD_LINES)
      for (line in lines) {
        val materialId = line[FieldMeta.FIELD_MATERIAL] as String? ?: continue
        val qty = NumHelper.anyToDouble(line["qty"]) ?: continue
        if (qty <= 0) continue
        val ccQty = NumHelper.anyToDouble(line["ccQty"]) ?: 0.0
        if (ccQty >= qty) continue
        loList += InboundLineAndOrder(
          line,
          order,
          materialId,
          inboundQty = qty,
          initAssignedQty = ccQty,
          assignedQty = ccQty,
          restQty = qty - ccQty,
        )
      }
    }
    if (loList.isEmpty()) return

    // 加载容器类型
    val containerTypeMap = mutableMapOf<String, ContainerType>()
    val containerTypes = EntityRwService.findMany("FbContainerType", Cq.all())
    for (ct in containerTypes) {
      val id = EntityHelper.mustGetId(ct)
      var subNum = ct["subNum"] as Int? ?: 1
      if (subNum <= 0) subNum = 1
      containerTypeMap[id] = ContainerType(id, subNum)
    }

    // TODO containerTypeMap 为空报错

    val ccc = CallContainerContext(containerTypeMap)

    ResLockService.resLock.withLock {
      for (lo in loList) {
        tryCallContainers(ccc, lo)
      }

      if (ccc.usedContainers.isEmpty()) return // 本轮无分配

      for (containerId in ccc.usedContainers) {
        val cc = ccc.containerMap[containerId] ?: continue
        if (cc.assignments.isEmpty()) continue

        // 为容器找目标库位，找不到放弃本轮分配和出库
        val targetBinId = findTargetBinId(config, containerId)
        if (targetBinId.isNullOrBlank()) {
          // TODO 记录阻塞
          withdrawContainerAssignments(cc)
          continue
        }
        cc.targetBinId = targetBinId

        // 处理容器：工作状态、目标库位
        // TODO 抛异常咋办
        ContainerUpdateService.markContainerWorkStatus(containerId, "ToPut", targetBinId)

        // 处理起点库位：工作状态、目的
        // TODO 抛异常咋办
        BinUpdateService.makeBinToUnload(cc.fromBinId, "CallContainer")

        // 处理终点库位：工作状态、目标容器、目的
        // TODO 抛异常咋办
        BinUpdateService.makeBinToLoad(targetBinId, containerId, "CallContainer")

        // 生成容器搬运单
        createCto(config, containerId, cc.fromBinId, targetBinId)

        // 创建装货单
        createPutOrder(cc)
      }

      // 更新单行的 invAssignedQty
      for (lo in loList) {
        lo.line["initCcQty"] = lo.line["ccQty"]
        lo.line["ccQty"] = lo.assignedQty
      }
      // 更新入库单
      for (order in orders) updateInboundOrder(order)
    }
  }

  /**
   * 为单行尝试叫
   */
  private fun tryCallContainers(ccc: CallContainerContext, lo: InboundLineAndOrder) {
    if (lo.restQty <= 0) return

    // 先重用已分配的容器
    tryUsedContainers(ccc, lo)

    // 先找半箱
    tryPartialContainers(ccc, lo)
    if (lo.restQty <= 0) return

    // 再找全空的箱子
    tryEmptyContainers(ccc, lo)
  }

  // 加载 物料装容器相关参数 TODO 抛异常还是
  private fun loadMaterialParam(ccc: CallContainerContext, materialId: String): MaterialParam {
    var mp = ccc.materialParamMap[materialId]
    if (mp != null) return mp

    val materialEv = EntityRwService.findOneById("FbMaterial", materialId)
      ?: throw BzError("errBzNoMaterialById", materialId)

    val leafCategory = materialEv["leafCategory"] as String?
    if (leafCategory.isNullOrEmpty()) throw BzError("errBzMaterialNoCategory", materialId)
    val leafCategoryEv = EntityRwService.findOneById("FbMaterialCategory", leafCategory)
      ?: throw BzError("errBzNoMaterialCategoryById", leafCategory)

    @Suppress("UNCHECKED_CAST")
    val storeDistricts = leafCategoryEv["storeDistricts"] as List<String>?
    if (storeDistricts.isNullOrEmpty()) {
      throw BzError("errBzMaterialCategoryNoStoreDistricts", leafCategory)
    }

    val mqList = EntityRwService.findMany(
      "FbMaterialContainerMaxQty",
      Cq.and(listOf(Cq.eq(FieldMeta.FIELD_MATERIAL, materialId), Cq.ne(FieldMeta.FIELD_DISABLED, true))),
    )
    if (mqList.isEmpty()) throw BzError("errBzNoMaterialContainerMaxQty", materialId)

    val mQMap: MutableMap<String, MaterialContainerMaxQty> = HashMap()
    val containerTypes: MutableList<String> = ArrayList()
    for (ev in mqList) {
      val containerType = ev["containerType"] as String? ?: continue
      val maxQty = NumHelper.anyToInt(ev["maxQty"]) ?: continue
      if (maxQty <= 0) continue

      if (!containerTypes.contains(containerType)) containerTypes += containerType
      mQMap[containerType] = MaterialContainerMaxQty(containerType, maxQty)
    }
    if (mQMap.isEmpty()) throw BzError("NoMaterialContainerMaxQty2", materialId)

    mp = MaterialParam(materialId, leafCategory, storeDistricts, containerTypes, mQMap)
    ccc.materialParamMap[materialId] = mp

    return mp
  }

  // 尝试已分配过的
  private fun tryUsedContainers(ccc: CallContainerContext, lo: InboundLineAndOrder) {
    for (containerId in ccc.usedContainers) {
      val cc = ccc.containerMap[containerId] ?: continue

      val containerType = cc.containerType
      val containerDistrict = cc.containerDistrict
      val mp = ccc.materialParamMap[lo.materialId] ?: continue

      // 能用于这个物料
      if (!isContainerForMaterial(containerType, containerDistrict, mp)) continue

      tryInContainer(ccc, cc, lo)
      if (lo.restQty <= 0) break
    }
  }

  // 尝试分配含有物料的半箱
  private fun tryPartialContainers(ccc: CallContainerContext, lo: InboundLineAndOrder) {
    // 有物料的所有容器，按占用率从高到低
    val materialContainers = loadMaterialPartialContainers(ccc, lo.materialId).values.sortedByDescending { it.used }
    for (cc in materialContainers) {
      tryInContainer(ccc, cc, lo)
      if (lo.restQty <= 0) break
    }
  }

  // 尝试分配全空箱子
  private fun tryEmptyContainers(ccc: CallContainerContext, lo: InboundLineAndOrder) {
    val materialContainers = loadMaterialEmptyContainers(ccc, lo.materialId).values.toList() // 快照，会变
    // TODO 全空容器的排序
    for (cc in materialContainers) {
      tryInContainer(ccc, cc, lo)
      if (lo.restQty <= 0) break
    }
  }

  // 加载一个物料能用的半箱
  private fun loadMaterialPartialContainers(
    ccc: CallContainerContext,
    materialId: String,
  ): Map<String, ContainerContext> = ccc.materialToPartialContainers.getOrPut(materialId) {
    val materialContainers = mutableMapOf<String, ContainerContext>()
    val invLayouts = EntityRwService.findMany("FbInvLayout", Cq.eq(FieldMeta.FIELD_MATERIAL, materialId))
    for (invLayout in invLayouts) {
      val topContainerId = invLayout[FieldMeta.FIELD_TOP_CONTAINER] as String?
      if (topContainerId.isNullOrBlank()) continue
      var cc = ccc.containerMap[topContainerId]
      if (cc != null) {
        materialContainers[topContainerId] = cc
      } else {
        // 初始化容器，容器属性、库存
        val containerEv = EntityRwService.findOneById("FbContainer", topContainerId) ?: continue
        val containerType = containerEv["type"] as String? ?: continue
        val containerTypeObj = ccc.containerTypeMap[containerType] ?: continue
        val currentBinId = containerEv["bin"] as String? ?: continue
        val containerDistrict = BinContainerInvReadService.getDistrictOfContainer(containerEv) ?: continue

        // 加载容器内的所有库存并按格子分好
        val subInvLayouts = BinContainerInvReadService.loadContainerInvBySub(topContainerId)

        // 计算占用率
        val subUsed: MutableMap<Int, SubUsed> = HashMap()
        var usedAll = 0.0
        for (subId in 1..containerTypeObj.subNum) {
          val layouts = subInvLayouts[subId]
          if (layouts.isNullOrEmpty()) {
            subUsed[subId] = SubUsed(false, null, 0.0, 0.0)
          } else {
            // 计算已占用量
            var used = 0.0
            var bad = false // 无法计算
            var inMaterialId: String? = null
            for (layout in layouts) {
              val qty = NumHelper.anyToDouble(layout["qty"]) ?: continue
              if (qty <= 0) continue
              inMaterialId = layout[FieldMeta.FIELD_MATERIAL] as String?
              if (inMaterialId.isNullOrBlank()) {
                bad = true
                break
              }
              val mp = loadMaterialParam(ccc, inMaterialId)
              val maxQty = mp.maxQty[containerType]
              if (maxQty == null) {
                bad = true
                break
              }
              used += qty / maxQty.maxQty
            }
            subUsed[subId] = if (bad) {
              SubUsed(true, null, 0.0, 0.0)
            } else {
              usedAll += usedAll
              SubUsed(false, inMaterialId, used, used)
            }
          }
        }

        cc = ContainerContext(
          topContainerId, containerType, containerDistrict, currentBinId,
          containerTypeObj.subNum, subInvLayouts, subUsed, usedAll, usedAll,
        )
        ccc.containerMap[topContainerId] = cc
        materialContainers[topContainerId] = cc
      }
    }
    materialContainers
  }

  // 加载一个物料能用的全空的箱子
  private fun loadMaterialEmptyContainers(
    ccc: CallContainerContext,
    materialId: String,
  ): Map<String, ContainerContext> = ccc.materialToEmptyContainers.getOrPut(materialId) {
    val materialContainers = mutableMapOf<String, ContainerContext>()
    val mp = loadMaterialParam(ccc, materialId)

    // 先找所有空箱：空、未禁用、工作状态为空、有库位
    val emptyContainers = EntityRwService.findMany(
      "FbContainer",
      Cq.and(
        listOf(
          Cq.ne("filled", true),
          Cq.ne(FieldMeta.FIELD_DISABLED, true),
          Cq.empty("workStatus"),
          Cq.notEmpty("bin"),
        ),
      ),
    )

    for (containerEv in emptyContainers) {
      val containerId = EntityHelper.mustGetId(containerEv)

      val containerType = containerEv["type"] as String? ?: continue
      val containerTypeObj = ccc.containerTypeMap[containerType] ?: continue
      val currentBinId = containerEv["bin"] as String? ?: continue
      // 容器所在库区 TODO 效率优化
      val containerDistrict = BinContainerInvReadService.getDistrictOfContainer(containerEv) ?: continue

      // 没用过、能用于这个物料
      if (ccc.usedContainers.contains(containerId) ||
        !isContainerForMaterial(containerType, containerDistrict, mp)
      ) {
        continue
      }

      // 之前计算过！
      var cc = ccc.containerMap[containerId]
      if (cc != null) {
        materialContainers[containerId] = cc
        continue
      }

      // 初始化格子占用
      val subUsed = mutableMapOf<Int, SubUsed>()
      for (subNo in 1..containerTypeObj.subNum) {
        subUsed[subNo] = SubUsed(false, null, 0.0, 0.0)
      }

      cc = ContainerContext(
        containerId, containerType, containerDistrict, currentBinId,
        containerTypeObj.subNum, HashMap(), subUsed, 0.0, 0.0,
      )
      ccc.containerMap[containerId] = cc
      materialContainers[containerId] = cc
    }

    materialContainers
  }

  // 容器能否用来装物料：物料允许的容器类型、物料允许的存放区域
  private fun isContainerForMaterial(containerType: String, containerDistrict: String, mp: MaterialParam): Boolean =
    containerType.isNotBlank() &&
      mp.containerTypes.contains(containerType) &&
      containerDistrict.isNotBlank() &&
      mp.storeDistricts.contains(containerDistrict)

  // 尝试在一个容器内分配
  private fun tryInContainer(
    ccc: CallContainerContext,
    cc: ContainerContext,
    lo: InboundLineAndOrder,
  ) {
    val mp = loadMaterialParam(ccc, lo.materialId)
    val maxQc = mp.maxQty[cc.containerType] ?: return
    for (subNo in 1..cc.subNum) {
      if (!tryInSub(ccc, cc, subNo, maxQc, lo)) continue

      // 将容器从全空列表中删除，加入半空列表
      ccc.materialToEmptyContainers[lo.materialId]?.remove(cc.containerId)
      ccc.materialToPartialContainers.getOrPut(lo.materialId) { HashMap() }[cc.containerId] = cc

      if (lo.restQty <= 0) return
    }

    if (lo.restQty <= 0) return
  }

  // 尝试在一个格子里分配。如果格子已经分配过其他物料不能再分配：一个格子一定不能混放
  private fun tryInSub(
    ccc: CallContainerContext,
    cc: ContainerContext,
    subNo: Int,
    maxQc: MaterialContainerMaxQty,
    lo: InboundLineAndOrder,
  ): Boolean {
    val subUsed = cc.subUsed[subNo] ?: return false
    if (subUsed.materialId != null && subUsed.materialId != lo.materialId) return false

    val containerRestQty = ceil((1 - subUsed.now) * maxQc.maxQty)
    if (containerRestQty <= 0) return true
    val q = min(lo.restQty, containerRestQty)
    lo.restQty -= q
    lo.assignedQty += q
    subUsed.now += q / maxQc.maxQty
    subUsed.materialId = lo.materialId
    cc.used += q / maxQc.maxQty

    cc.assignments += CallAssigment(lo, subNo, q)

    if (!ccc.usedContainers.contains(cc.containerId)) ccc.usedContainers += cc.containerId

    return true
  }

  // 寻找出库的目标库位
  private fun findTargetBinId(config: CallContainerConfig, containerId: String): String? =
    if (ScriptCenter.exists("qsCallContainerFindTargetBinId")) {
      ScriptCenter.execute(
        ScriptExeRequest("qsCallContainerFindTargetBinId", arrayOf(ScriptTraceContext(), config, containerId)),
        jacksonTypeRef(),
      )
    } else {
      val bin = if (config.outDistrict.isNullOrBlank()) {
        null
      } else {
        BinContainerInvReadService.listEmptyBinInDistrict(config.outDistrict)
      }
      bin?.let { EntityHelper.mustGetId(it) }
    }

  // 撤回对容器的分配，扣减出库单行已分配数量
  private fun withdrawContainerAssignments(cc: ContainerContext) {
    for (ag in cc.assignments) {
      ag.lo.assignedQty -= ag.q
    }
  }

  // 生成容器搬运单
  private fun createCto(
    config: CallContainerConfig,
    containerId: String,
    fromBinId: String,
    toBinId: String,
  ) {
    val transportOrderStatus = "Created"
    val def = if (!config.containerOutFalconTask.isNullOrBlank()) {
      FalconTaskDefService.mustFetchLatestTaskDefById(config.containerOutFalconTask)
    } else {
      null
    }
    val transportOrder: EntityValue = mutableMapOf(
      "container" to containerId,
      "kind" to "ContainerOut",
      "status" to transportOrderStatus,
      "fromBin" to fromBinId,
      "toBin" to toBinId,
      "falconTaskDefId" to config.containerOutFalconTask, // 用哪个猎鹰任务执行此容器搬运单
      "falconTaskDefLabel" to def?.label,
      "postProcessMark" to "Outbound", // 处理标记
    )
    val id = EntityRwService.createOne("ContainerTransportOrder", transportOrder)
    logger.info("创建容器搬运单 id=$id，$transportOrder")
  }

  // 创建装货单
  private fun createPutOrder(cc: ContainerContext) {
    // 分配 -> 装货单行
    val putLines = cc.assignments.map { ca ->
      val putLine: EntityValue = mutableMapOf()
      // 先把单行的字段都拷入
      putLine.putAll(ca.lo.line)

      // 删除特殊含义字段
      putLine.remove("id")
      putLine.remove(FieldMeta.FIELD_PARENT_ID)
      putLine.remove(FieldMeta.FIELD_LINE_NO)

      putLine[FieldMeta.FIELD_SUB_CONTAINER_ID] = ca.subNo

      // 期望装货、实际装货
      putLine["planQty"] = ca.q
      putLine["qty"] = ca.q

      putLine["inboundOrderId"] = ca.lo.order["id"]
      putLine["inboundLineId"] = ca.lo.line["id"]
      putLine["inboundLineNo"] = ca.lo.line[FieldMeta.FIELD_LINE_NO]

      putLine
    }
    if (putLines.isEmpty()) return

    val putOrder: EntityValue = mutableMapOf(
      FieldMeta.FIELD_ORDER_STATE to "Todo",
      "container" to cc.containerId,
      "targetBin" to cc.targetBinId,
      FieldMeta.FIELD_LINES to putLines,
    )

    val id = EntityRwService.createOne("QsPutOrder", putOrder)
    logger.info("创建装货单 id=$id，$putOrder")
  }

  // 更新出库单。更新出库单的 invAssignedAll 状态，更新单行。
  private fun updateInboundOrder(order: EntityValue) {
    val orderId = EntityHelper.mustGetId(order)
    val lines = EntityHelper.getLines(order, FieldMeta.FIELD_LINES)
    var needUpdate = false
    var short = false
    if (lines.isNullOrEmpty()) {
      needUpdate = true
    } else {
      for (line in lines) {
        val qty = NumHelper.anyToDouble(line["qty"]) ?: continue
        val ccQty = NumHelper.anyToDouble(line["ccQty"]) ?: 0.0
        if (ccQty < qty) {
          short = true
          break
        }
        if (line["initCcQty"] != line["ccQty"]) {
          needUpdate = true
          logger.info(
            "入库单行 $orderId:${line[FieldMeta.FIELD_LINE_NO]} " +
              "已叫容器数量从 ${line["initCcQty"]} 变为 ${line["ccQty"]}",
          )
        }
      }
    }
    if (!short) {
      order["callContainerAll"] = true
      logger.info("入库单 $orderId 叫空容器完成")
    }
    if (needUpdate) {
      EntityRwService.updateOne("QsInboundOrder", Cq.idEq(orderId), order)
    }
  }

  @Suppress("UNCHECKED_CAST")
  private fun parseConfig(): CallContainerConfig {
    val configEv = BzConfigManager.getByPath("ScQuickStore", "callContainer") as EntityValue? ?: mutableMapOf()

    return CallContainerConfig(
      outDistrict = configEv["outDistrict"] as String?,
      containerOutFalconTask = configEv["containerOutFalconTask"] as String?,
    )
  }

  data class InboundLineAndOrder(
    @JvmField
    val line: EntityValue,
    @JvmField
    val order: EntityValue,
    @JvmField
    val materialId: String,
    @JvmField
    val inboundQty: Double, // 期望入库数量
    @JvmField
    val initAssignedQty: Double, // 已分配数量
    @JvmField
    var assignedQty: Double, // 已分配数量
    @JvmField
    var restQty: Double, // 还未分配数量
  )

  data class CallContainerContext(
    val containerTypeMap: Map<String, ContainerType>,
    val containerMap: MutableMap<String, ContainerContext> = HashMap(),
    // 有此物料的所有容器, material id -> container id
    val materialToPartialContainers: MutableMap<String, MutableMap<String, ContainerContext>> = HashMap(),
    // 能用于装物料的全空的容器
    val materialToEmptyContainers: MutableMap<String, MutableMap<String, ContainerContext>> = HashMap(),
    val materialParamMap: MutableMap<String, MaterialParam> = HashMap(),
    val usedContainers: MutableList<String> = ArrayList(),
  )

  data class ContainerContext(
    val containerId: String,
    val containerType: String,
    val containerDistrict: String,
    val fromBinId: String,
    val subNum: Int,
    val subInvLayouts: Map<Int, List<EntityValue>>, // 按格号归结
    val subUsed: MutableMap<Int, SubUsed>, // 格子的空间占用率
    val initUsed: Double, // 占用率
    var used: Double, // 占用率 TODO 应该除以格数
    val assignments: MutableList<CallAssigment> = ArrayList(),
    var targetBinId: String? = null,
  )

  data class ContainerType(val id: String, val subNum: Int)

  /**
   * 格子的空间占用率
   */
  data class SubUsed(
    var bad: Boolean, // 这个格子不能用，比如不能算出占用率
    var materialId: String?, // 现在格子里只能放一种料
    val init: Double,
    var now: Double,
  )

  /**
   * 物料装容器相关参数
   */
  data class MaterialParam(
    val materialId: String,
    val leafCategory: String, // 物料最小分类
    val storeDistricts: List<String>, // 允许存放的库区
    val containerTypes: List<String>, // 允许存放的容器类型
    val maxQty: Map<String, MaterialContainerMaxQty>, // 物料在容器类型中最多放几个
  )

  /**
   * 物料在容器类型中最多放几个
   */
  data class MaterialContainerMaxQty(val containerType: String, val maxQty: Int)

  data class CallAssigment(val lo: InboundLineAndOrder, val subNo: Int, val q: Double)

  data class CallContainerConfig(
    @JvmField
    val outDistrict: String?, // 出库库区
    @JvmField
    val containerOutFalconTask: String?, // 处理出库容器搬运单的猎鹰任务
  )
}