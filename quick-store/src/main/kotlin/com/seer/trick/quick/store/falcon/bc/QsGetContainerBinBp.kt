package com.seer.trick.quick.store.falcon.bc

import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*
import com.seer.trick.quick.store.base.QsBaseReadService

class QsGetContainerBinBp : AbstractBp() {

  override fun process() {
    val containerId = mustGetBlockInputParam(ipContainerId.name) as String

    val binInv = QsBaseReadService.getBinInvByContainer(containerId)
    if (binInv == null) {
      setBlockOutputParams(mapOf(opFound.name to false, opBinId.name to null))
      return
    }

    setBlockOutputParams(mapOf(opFound.name to true, opBinId.name to binInv["bin"]))
  }

  companion object {
    private val ipContainerId = BlockInputParamDef(
      "containerId",
      BlockParamType.String,
      true,
      objectTypes = listOf(ParamObjectType.ContainerId),
    )

    private val opFound = BlockOutputParamDef("found", BlockParamType.Boolean)
    private val opBinId = BlockOutputParamDef(
      "binId",
      BlockParamType.String,
      objectTypes = listOf(ParamObjectType.BinId),
    )

    val def = BlockDef(
      QsGetContainerBinBp::class.simpleName!!,
      color = "#A8D297",
      inputParams = listOf(ipContainerId),
      outputParams = listOf(opFound, opBinId),
    )
  }
}