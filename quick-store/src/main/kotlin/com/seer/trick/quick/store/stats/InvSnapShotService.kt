package com.seer.trick.quick.store.stats

import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.*
import com.seer.trick.base.soc.SysMonitorService
import com.seer.trick.base.stats.manila.ManilaReportService
import com.seer.trick.helper.IdHelper
import java.util.*
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

object InvSnapShotService {
  private val scheduler = Executors.newSingleThreadScheduledExecutor()

  fun init() {
    scheduler.scheduleAtFixedRate(::genSnapShoot, 40 * 60 * 1000, 24 * 60 * 60 * 1000, TimeUnit.MILLISECONDS)
  }

  fun dispose() {
    scheduler.shutdownNow()
  }

  // 生成库存快照 FbInvSnapShot
  private fun genSnapShoot() {
    if (ManilaReportService.disabled()) return
    // val invStats =
    val uuid = IdHelper.oidStr()
    
    SysMonitorService.log(
      "Stat",
      "FbInvSnapShot",
      "genSnapShoot",
      "Start::stat",
      instant = ManilaReportService.disableLogging(),
    )
    val ao = AggregationOptions(
      fields = listOf(AdvancedField(AggFun.SUM, "qty", "qty")),
      groupBy = listOf(GroupByField("btMaterial", "btMaterial", null)),
    )
    val d = Date()
    val r = EntityStatsService.aggregateQuery("FbInvLayout", Cq.lt("createdOn", d), ao)
    SysMonitorService.log(
      "Stat",
      "FbInvSnapShot",
      "genSnapShoot",
      "aggregate, size: ${r.size}, first: ${r.firstOrNull()}",
      instant = ManilaReportService.disableLogging(),
    )

    val snapShoots = mutableListOf<EntityValue>()
    for (ev in r) {
      snapShoots.add(
        mutableMapOf(
          "btMaterial" to ev["btMaterial"],
          "qty" to ev["qty"],
          "uuid" to uuid,
          "createdOn" to d,
          "modifiedOn" to d,
        ),
      )
    }

    EntityRwService.createMany("FbInvSnapShot", snapShoots, CreateOptions(keepDate = true))

    SysMonitorService.log(
      "Stat",
      "FbInvSnapShot",
      "genSnapShoot",
      "Finish::stat",
      remove = true,
      instant = ManilaReportService.disableLogging(),
    )
  }
}