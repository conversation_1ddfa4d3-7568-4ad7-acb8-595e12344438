package com.seer.trick.quick.store.base

import com.seer.trick.Cq

import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.change.EntityChange
import com.seer.trick.base.entity.service.extension.EntityServiceExtension
import com.seer.trick.bz.wms.ContainerService
import org.slf4j.LoggerFactory

/**
 * 增删改容器 -> 同步库位库存
 * 停用的容器也同步，在库位库存表里也停用
 *
 * 只创建最外层容器的 FbBinInv，即 pContainer 为空的容器。
 *
 * TODO 未绑定库位的容器，绑定库位后，删除一条记录
 * TODO 如果容器表上的容器所在库位等字段不再维护，需要在业务代码中调用 BinInvService#updateBinInv
 * TODO 如何处理最外层容器、非最外层容器
 * TODO 如何处理容器与库位的绑定、解绑
 *
 */
object SyncContainerToBinInvService : EntityServiceExtension() {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 创建一条最外层容器后，创建一条库位库存
   */
  override fun afterCreating(em: EntityMeta, evList: List<EntityValue>) {
    if (em.name != "FbContainer") return
    QsResService.resExecutor.submit {
      try {
        // 最外层容器
        val binInvEvList = evList.filter(::isTopContainer).map { containerToBinInv(it) }
        EntityRwService.createMany("FbBinInv", binInvEvList)

        // 非最外层容器
        evList.filter { !isTopContainer(it) }.map { ev ->
          // 重新计算最外层容器的数据
          regenTopContainerBinInv(ev)
        }
      } catch (e: Exception) {
        logger.error("Failed to create bin inventories for creating containers", e)
      }
    }
  }

  /**
   * 原本是最外层容器，更新后不是 ->
   * 原本不是最外层容器，更新后是 ->
   * 更新前后都不是最外层容器 ->
   * 更新前后都是最外层容器，不改库位 ->
   * 更新前后都是最外层容器，改库位 ->
   */
  override fun afterUpdating(em: EntityMeta, changes: List<EntityChange>) {
    if (em.name != "FbContainer") return
    QsResService.resExecutor.submit {
      try {
        for (c in changes) {
          val new = c.newValue ?: continue
          // patchContainerToBinInv(c.id, new)
          regenTopContainerBinInv(new)
        }
      } catch (e: Exception) {
        logger.error("Failed to create bin inventories for creating containers", e)
      }
    }
  }

  /**
   * 删除容器后。如果是未绑定库位的容器的库位库存，则删掉；如果是库位的库位库存，则清空容器相关字段
   */
  override fun afterRemoving(em: EntityMeta, oldValues: List<EntityValue>) {
    if (em.name != "FbContainer") return
    QsResService.resExecutor.submit {
      try {
        val cIds = oldValues.map { EntityHelper.mustGetId(it) }
        removeContainers(cIds)
        oldValues.map { regenTopContainerBinInv(it) }
      } catch (e: Exception) {
        logger.error("Failed to create bin inventories for removing containers", e)
      }
    }
  }

  override fun afterBigChange(em: EntityMeta) {
    if (em.name != "FbContainer") return
    QsResService.resExecutor.submit {
      try {
        completeSync()
      } catch (e: Exception) {
        logger.error("Failed to sync after containers big changes", e)
      }
    }
  }

  /**
   * 全量同步。比对容器和库位库存。
   * 耗时
   * TODO 在锁里？
   */
  fun completeSync() {
    // 所有的最外层容器
    val topContainers = EntityRwService.findMany("FbContainer", Cq.empty("pContainer"))
    val topContainerIdSet = topContainers.map { EntityHelper.mustGetId(it) }.toSet()

    // 库位库存中的容器
    val binInvList = EntityRwService.findMany("FbBinInv", Cq.notEmpty("topContainer"))
    val existedContainerIdSet = binInvList.map { it["topContainer"] as String }

    // 新增的库位库存
    val newBinInvList = mutableListOf<EntityValue>()

    for (containerEv in topContainers) {
      val containerId = EntityHelper.mustGetId(containerEv)
      if (containerId !in existedContainerIdSet) {
        // 新增的容器
        newBinInvList += containerToBinInv(containerEv)
      } else {
        // 更新的容器。按照
        patchContainerToBinInv(containerId, containerEv)
      }
    }

    if (newBinInvList.isNotEmpty()) {
      EntityRwService.createMany("FbBinInv", newBinInvList)
    }

    // 要删除的容器
    val removingContainerIds = binInvList.filter {
      val topContainerId = it["topContainerId"] as String?
      !topContainerId.isNullOrBlank() && topContainerId !in topContainerIdSet
    }.map { it["topContainerId"] as String }
    removeContainers(removingContainerIds)
  }

  /**
   * 删除未绑定库位的容器的库位库存；清空绑定了库位的容器的库位库存上的容器相关字段
   */
  private fun removeContainers(cIds: List<String>) {
    EntityRwService.removeMany("FbBinInv", Cq.and(Cq.empty("bin"), Cq.include("topContainer", cIds)))
    EntityRwService.updateMany(
      
      "FbBinInv",
      Cq.and(Cq.notEmpty("bin"), Cq.include("topContainer", cIds)),
      buildCleanContainerPatch(),
    )
  }

  /**
   * 重新生成最外层容器对应的库位库存
   *
   * @param ev 容器的 entityValue
   */
  private fun regenTopContainerBinInv(ev: EntityValue) {
    val cId = EntityHelper.mustGetId(ev)
    if (isTopContainer((ev))) {
      patchContainerToBinInv(cId, ev)
    } else {
      val topC = ContainerService.findTopContainer(cId)
      patchContainerToBinInv(EntityHelper.mustGetId(topC), topC)
    }
  }

  /**
   * 更新库位库存。ev 上有库位，且库位存在，存在则更新库位库存；否则，则新建库位库存
   *
   * TODO 新建容器时指定了不存在的库位，库位库存中暂时不维护这个库位，此容器视为没绑定库位的容器
   *
   * @param ev 容器的 entityValue，不是库位库存的
   */
  private fun patchContainerToBinInv(cId: String, ev: EntityValue) {
    // 直接更新库位库存表上的库存相关字段
    EntityRwService.updateOne("FbBinInv", Cq.eq("topContainer", cId), containerToBinInv(ev))
  }

  /**
   * 判断容器是最外层容器：pContainer 为空
   */
  private fun isTopContainer(ev: EntityValue): Boolean = (ev["pContainer"] as String?).isNullOrBlank()

  /**
   * 容器 FbContainer 字段名 -> 库位库存 FbBinInv 字段名
   */
  private val fieldMapping = mutableMapOf(
    "subNum" to "subNum",
    "id" to "topContainer",
    "type" to "topContainerType",
    "filled" to "containerFilled",
    "btDisabled" to "containerDisabled",
  )

  /**
   * 清空容器字段的 update
   */
  private fun buildCleanContainerPatch(): EntityValue = fieldMapping.mapValues { null }.toMutableMap()

  /**
   * 将一个容器对象转换为一个库位库存对象
   *
   * @param ev 容器的 entityValue
   */
  private fun containerToBinInv(ev: EntityValue): EntityValue {
    val r = mutableMapOf<String, Any?>()
    for ((sf, tf) in fieldMapping) {
      r[tf] = ev[sf]
    }

    // 遍历查所有子容器
    val cId = EntityHelper.mustGetId(ev)
    r["containers"] = ContainerService.findAllChildContainerIds(cId).joinToString(",")

    return r
  }
}