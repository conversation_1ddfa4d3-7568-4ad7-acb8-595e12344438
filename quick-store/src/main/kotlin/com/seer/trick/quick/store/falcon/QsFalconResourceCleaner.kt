package com.seer.trick.quick.store.falcon

import com.seer.trick.Cq
import com.seer.trick.falcon.task.FalconTaskService
import com.seer.trick.quick.store.base.QsBaseUpdateService
import org.slf4j.LoggerFactory

object QsFalconResourceCleaner {
  
  private val logger = LoggerFactory.getLogger(javaClass)
  
  fun init() {
    FalconTaskService.addResCleaner("QsFindNotOccupiedBin", QsFalconResourceCleaner::removeBinBzMark)
  }
  
  private fun removeBinBzMark(
    taskId: String,
    resType: String,
    resId: String,
    args: Map<String, Any?>?,
  ) {
    if (args == null) {
      logger.error("removeBinBzMark args = null")
      return
    }
    val binId = args["binId"] as String
    logger.info("removeBinBzMark binId=$binId falconTaskId=$taskId")
    
    QsBaseUpdateService.removeBzMark(Cq.eq("bin", binId))
  }
}