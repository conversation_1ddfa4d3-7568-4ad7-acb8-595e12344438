package com.seer.trick.quick.store.base

import com.seer.trick.BzError
import com.seer.trick.ComplexQuery
import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.reslock.ResLockService
import com.seer.trick.bz.wms.inv.CreateInvService
import org.slf4j.LoggerFactory
import kotlin.concurrent.withLock

/**
 * 仓配，基础修改类功能：修改库位库存、库存明细
 * TODO 对空库位即将运来货物，或满库位即将被运走，是否要标记：状态、即将要运来的容器、目的。要不要标记容器即将被运输的状态。
 *
 * TODO 封装函数，当库位、容器等改变，修改关联属性
 */
object QsBaseUpdateService {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 将容器从库位移动到机器人
   */
  fun moveContainerFromBinToRobot(containerId: String, fromBin: String, toRobot: String) =
    QsResService.resLock.withLock {
      val binInv = QsBaseReadService.mustGetBinInvByBin(fromBin)
      // 验证库位上的当前容器确实是
      if (binInv["topContainer"] != containerId) {
        throw BzError("errBinContainer1Container2", fromBin, containerId, binInv["topContainer"])
      }
      // 获得解绑后的容器信息，新增一条容器记录在FbInvBin
      val c = containerMaterialAttributes(binInv)
      c["onRobot"] = toRobot
      QsBaseReadService.addBinInvRecord(c)
      // 获得解绑后的库位更新库位，去除容器物料信息
      QsBaseReadService.updateBinInvById(fromBin, unBoundBinAttributes())

      EntityRwService.updateOne(
        "FbBinInv",
        Cq.idEq(EntityHelper.mustGetId(binInv)),
        mutableMapOf("occupied" to false, "topContainer" to ""),
      )

      EntityRwService.updateMany(
        "FbInvLayout",
        Cq.eq(FieldMeta.FIELD_TOP_CONTAINER, containerId),
        (clearFbInvBinAttributes() + mutableMapOf("onRobot" to toRobot)).toMutableMap(),
      )

      // TODO 专门的日志，先打日志还是先操作
      logger.info("Move container $containerId from bin $fromBin to robot $toRobot")
    }

  /**
   * 将容器移到新库位。容器之前可能在其他库位上，或不在库位上。
   */
  fun moveContainerToBin(containerId: String, toBin: String) = QsResService.resLock.withLock {
    val fromBinInv = QsBaseReadService.mustGetBinInvByContainer(containerId)
    val fromBin = fromBinInv["bin"] as String?

    // 已经在目标库位上了
    if (fromBin == toBin) {
      logger.warn("Move container $containerId to $toBin, but the container already on the bin")
      return@withLock
    }

    val toBinInv = QsBaseReadService.mustGetBinInvByBin(toBin)
    val toBinContainer = toBinInv["topContainer"] as String?
    // 目标库位上有货，或已有容器但与目标容器不同
    if (toBinInv["occupied"] == true || !toBinContainer.isNullOrBlank() && toBinContainer != containerId) {
      throw BzError("errMoveContainerToBinOldContainer", containerId, toBin, toBinContainer)
    }

    logger.info("Move container $containerId from bin $fromBin to bin $toBin")

    if (fromBin != null) { // 表示原先绑定在其他库位上需要解绑
      QsBaseReadService.updateBinInvById(fromBin, unBoundBinAttributes())
    } else {
      QsBaseReadService.removeBinInvByContainerId(containerId)
    }
    // 绑定到新库位上
    QsBaseReadService.updateBinInvById(toBin, boundBinAttributes(fromBinInv))

    EntityRwService.updateOne(
      "FbBinInv",
      Cq.idEq(EntityHelper.mustGetId(fromBinInv)),
      mutableMapOf("occupied" to false, "topContainer" to ""),
    )

    EntityRwService.updateOne(
      "FbBinInv",
      Cq.idEq(EntityHelper.mustGetId(toBinInv)),
      mutableMapOf("occupied" to true, "topContainer" to containerId),
    )

    EntityRwService.updateMany(
      "FbInvLayout",
      Cq.eq(FieldMeta.FIELD_TOP_CONTAINER, containerId),
      binInvToInvLayoutUpdate(toBinInv),
    )
  }

  /**
   * 从库位上取下容器
   */
  fun takeOffContainer(containerId: String, binId: String, removingInv: Boolean) =
    QsResService.resLock.withLock {
      val binInv = QsBaseReadService.mustGetBinInvByContainer(containerId)

      // 容器不在指定库位上
      if (binInv["bin"] != binId) {
        throw BzError("errContainerNoOnBin", containerId, binId)
      }

      logger.info("Take off container $containerId from bin $binId")
      // 解绑后新增容器
      val res = getContainerAttributes(binInv).toMutableMap().apply {
        if (removingInv) {
          this[ContainerField.CONTAINERFILLED] = false
        } else {
          putAll(getMaterialAttributes(binInv))
        }
      }
      QsBaseReadService.addBinInvRecord(res)
      // 更新库位
      QsBaseReadService.updateBinInvById(binId, unBoundBinAttributes())

      EntityRwService.updateOne(
        "FbBinInv",
        Cq.idEq(EntityHelper.mustGetId(binInv)),
        mutableMapOf("occupied" to false, "topContainer" to ""),
      )

      if (!removingInv) {
        EntityRwService.updateMany(
          "FbInvLayout",
          Cq.eq(FieldMeta.FIELD_TOP_CONTAINER, containerId),
          clearFbInvBinAttributes(),
        )
      } else {
        // 删除库存
        logger.info("Remove inv layouts by container $containerId")
        EntityRwService.removeMany("FbInvLayout", Cq.eq(FieldMeta.FIELD_TOP_CONTAINER, containerId))
      }
    }

  /**
   * 根据库存明细，修正容器空满状态
   */
  fun fixContainerFilled(containerId: String) = QsResService.resLock.withLock {
    if (containerId.isBlank()) return@withLock
    val count = EntityRwService.count("FbInvLayout", Cq.eq(FieldMeta.FIELD_TOP_CONTAINER, containerId))
    val filled = count > 0
    // TODO 修正容器空满状态
    EntityRwService.updateOne("FbContainer", Cq.idEq(containerId), mutableMapOf("filled" to filled))
    EntityRwService.updateOne(
      "FbBinInv",
      Cq.eq(ContainerField.TOPCONTAINER, containerId),
      mutableMapOf
      (ContainerField.CONTAINERFILLED to filled),
    )
  }

  /**
   * 装货。将库存放入容器。新增库存。
   * 将容器标记为有货
   */
  fun putInvToContainer(containerId: String, invLayouts: List<EntityValue>) {
    if (invLayouts.isNotEmpty()) {
      val records = invLayouts.map { line ->
        val il: EntityValue = mutableMapOf()
        il.putAll(line)
        il.remove("id")
        il.remove("inboundOn")

        il[FieldMeta.FIELD_TOP_CONTAINER] = containerId
        il[FieldMeta.FIELD_LEAF_CONTAINER] = containerId
        il
      }
      CreateInvService.fixCreateInvLayout(records)
    }
  }

  /**
   * 清库位库存的业务标记、业务描述
   */
  fun removeBzMark(cq: ComplexQuery) {
    ResLockService.resLock.withLock {
      EntityRwService.updateMany("FbBinInv", cq, QsResAssignmentService.rollbackUpdate())
    }
  }

  /**
   * 库位实体转库存明细实体库位属性
   */
  fun fixFbBinToFbIvnBin(ev: EntityValue): EntityValue = BinField.baseFields.associateWith { ev[it] }.toMutableMap()

  /**
   * 容器从库位拿开后库位需要更新的字段
   * fixme Attributes -> fields
   */
  private fun unBoundBinAttributes(): EntityValue = (
    clearContainerAttributes() + clearMaterialAttributes() + clearQtyOthAttributes() +
      mutableMapOf(BinField.BINFILLED to false)
    ).toMutableMap()

  private fun boundBinAttributes(ev: EntityValue): EntityValue = (
    getMaterialAttributes(ev) + getContainerAttributes(ev) + getQtyOtherAttributes(ev) +
      mutableMapOf(BinField.BINFILLED to true)
    ).toMutableMap()

  /**
   *
   */
  private fun containerMaterialAttributes(ev: EntityValue): EntityValue =
    (getContainerAttributes(ev) + getMaterialAttributes(ev) + getQtyOtherAttributes(ev)).toMutableMap()

  /**
   * 从库位库存中获取容器属性
   */
  private fun getContainerAttributes(ev: EntityValue): EntityValue =
    ContainerField.fields.associateWith { ev[it] }.toMutableMap()

  /**
   * 从库位库存中获取物料属性
   */
  private fun getMaterialAttributes(ev: EntityValue): EntityValue =
    MaterialField.fields.associateWith { ev[it] }.toMutableMap()

  /**
   * 获取金额数量信息
   */
  private fun getQtyOtherAttributes(ev: EntityValue): EntityValue =
    OtherField.fields.associateWith { ev[it] }.toMutableMap()

  /**
   * 清空库位库存的容器属性
   */
  private fun clearContainerAttributes(): EntityValue = ContainerField.fields.associateWith { null }.toMutableMap()

  /**
   * 清空库位库存的物料属性
   */
  private fun clearMaterialAttributes(): EntityValue = MaterialField.fields.associateWith { null }.toMutableMap()

  /**
   * 清空库位库存其他属性
   */
  private fun clearQtyOthAttributes(): EntityValue = OtherField.fields.associateWith { null }.toMutableMap()

  /**
   * 清空库存明细库位相关属性
   */
  fun clearFbInvBinAttributes(): EntityValue = BinField.baseFields.associateWith { null }.toMutableMap()

  /**
   * 用库位库存，库位和库位相关属性，修改库存明细的库位和库位相关属性
   */
  private fun binInvToInvLayoutUpdate(ev: EntityValue): EntityValue =
    BinField.baseFields.associateWith { ev[it] }.toMutableMap()
}

// fixme 定义常量还不如直接用字符串
object MaterialField {
  private const val BTMATERIAL = "btMaterial"
  private const val BTMATERIALID = "btMaterialId"
  private const val BTMATERIALMODEL = "btMaterialModel"
  private const val BTMATERIALSPEC = "btMaterialSpec"
  private const val BTMATERIALCATEGORY = "btMaterialCategory"
  private const val BTMATERIALCATEGORYNAME = "btMaterialCategoryName"
  private const val BTMATERIALNAME = "btMaterialName"
  private const val BTMATERIALTOPCATEGORY = "btMaterialTopCategory"
  private const val BTMATERIALTOPCATEGORYNAME = "btMaterialTopCategoryName"
  private const val MATERIALIDS = "materialIds"
  private const val MATERIALNAMES = "materialNames"

  val fields = listOf(
    BTMATERIAL, BTMATERIALID, BTMATERIALMODEL, BTMATERIALSPEC,
    BTMATERIALCATEGORY, BTMATERIALCATEGORYNAME, BTMATERIALNAME,
    BTMATERIALTOPCATEGORY, BTMATERIALTOPCATEGORYNAME, MATERIALIDS, MATERIALNAMES,
  )
}

object BinField {
  //  private const val BINDISABLED = "binDisabled"
  private const val ROW = "row"
  private const val COLUMN = "column"
  private const val LAYER = "layer"
  private const val DEPTH = "depth"
  private const val RACK = "rack"
  private const val CHANNEL = "channel"
  private const val WORKSITE = "workSite"
  private const val ASSEMBLYLINE = "assemblyLine"
  const val BINFILLED = "binFilled"

  //  private const val MATERIALCATEGORYLABEL = "materialCategoryLabel"
  private const val WAREHOUSE = "warehouse"
  private const val DISTRICT = "district"
  private const val BIN = "bin"
//  private const val PENDINGCONTAINER = "pendingContainer"

  val baseFields = listOf(
    WAREHOUSE, DISTRICT, BIN, ROW, COLUMN, LAYER, DEPTH, RACK, CHANNEL,
    WORKSITE, ASSEMBLYLINE,
  )
}

object ContainerField {
  private const val CONTAINERDISABLED = "containerDisabled"
  const val CONTAINERFILLED = "containerFilled"
  private const val CONTAINERS = "containers"
  private const val SUBNUM = "subNum"
  const val TOPCONTAINER = "topContainer"
  private const val TOPCONTAINERTYPE = "topContainerType"
  val fields = listOf(
    CONTAINERDISABLED,
    CONTAINERFILLED,
    CONTAINERS,
    SUBNUM,
    TOPCONTAINER,
    TOPCONTAINERTYPE,
  )
}

object OtherField {
  private const val LOTNO = "lotNo"
  private const val QTY = "qty"
  private const val AMOUNT = "amount"

  //  private const val ONROBOT = "onRobot"
  val fields = listOf(
    LOTNO,
    QTY,
    AMOUNT,
  )
}