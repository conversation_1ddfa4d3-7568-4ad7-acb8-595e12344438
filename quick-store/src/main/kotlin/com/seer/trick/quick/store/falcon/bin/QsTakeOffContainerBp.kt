package com.seer.trick.quick.store.falcon.bin

import com.seer.trick.BzError
import com.seer.trick.Cq

import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.falcon.domain.ParamObjectType
import com.seer.trick.quick.store.base.QsBaseReadService
import com.seer.trick.quick.store.base.QsBaseUpdateService

class QsTakeOffContainerBp : AbstractBp() {

  override fun process() {
    val binId = mustGetBlockInputParam("binId") as String
    val removingInv = getBlockInputParamAsBool("removingInv")
    val unlockBin = getBlockInputParamAsBool("unlockBin")

    val containerId = QsBaseReadService.loadContainerIdOfBin(binId) ?: throw BzError("errNoContainerByBin", binId)
    QsBaseUpdateService.takeOffContainer(containerId, binId, removingInv)

    if (unlockBin) QsBaseUpdateService.removeBzMark(Cq.eq("bin", binId))

    addRelatedObject("FbBin", binId, null)
  }

  companion object {

    val def = BlockDef(
      QsTakeOffContainerBp::class.simpleName!!,
      color = "#f8fdcb",
      inputParams = listOf(
        BlockInputParamDef("binId", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.BinId)),
        BlockInputParamDef("removingInv", BlockParamType.Boolean),
        BlockInputParamDef("unlockBin", BlockParamType.Boolean),
      ),
    )
  }
}