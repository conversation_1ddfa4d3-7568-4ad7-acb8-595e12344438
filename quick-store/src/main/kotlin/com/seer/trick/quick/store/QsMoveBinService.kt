package com.seer.trick.quick.store

import com.seer.trick.BzError
import com.seer.trick.base.concurrent.BaseConcurrentCenter.highTimeSensitiveExecutor
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.extension.EntityServiceExtension
import com.seer.trick.base.reslock.ResLockService
import com.seer.trick.helper.submitCatch
import org.slf4j.LoggerFactory
import kotlin.concurrent.withLock

/**
 * 移库（将单个容器从一个库位移动到另一个）
 * 只能创建，不允许修改。
 */
object QsMoveBinService : EntityServiceExtension() {

  private val logger = LoggerFactory.getLogger(this::class.java)

  override fun beforeCreating(em: EntityMeta, evList: List<EntityValue>): List<String>? {
    if (em.name != "QsMoveBinOrder") return null
    // 检查参数合法
    ResLockService.resLock.withLock {
      for (ev in evList) {
        var containerId = ev["container"] as String?
        var fromBin = ev["fromBin"] as String?
        if (!containerId.isNullOrBlank() && !fromBin.isNullOrBlank()) {
          // 都录入了
          val containerEv = BinContainerInvReadService.mustGetContainer(containerId)
          val binEv = BinContainerInvReadService.mustGetBin(fromBin)

          val containerCurrentBin = containerEv["bin"] as String?
          // 如果容器在库位上，容器当前库位必须与起点一致
          if (!containerCurrentBin.isNullOrBlank() && containerCurrentBin != fromBin)
            throw BzError("errContainerBadCurrentBin", containerId, containerCurrentBin, fromBin)
          if (containerCurrentBin.isNullOrBlank()) {
            // 如果容器当前库位为空
            val oldContainerId = binEv["container"] as String?
            if (!oldContainerId.isNullOrBlank() && oldContainerId != containerId)
              throw BzError("errBinHasContainerNotToLoad", fromBin, binEv["container"], containerId)
            // 将容器绑定到起点
            BinContainerInvUpdateService.moveContainerToBin(containerId, fromBin)
          }
        } else if (!fromBin.isNullOrBlank()) {
          // 录了库位没录容器
          val binEv = BinContainerInvReadService.mustGetBin(fromBin)

          containerId = binEv["container"] as String?
          if (containerId.isNullOrBlank()) throw BzError("errBinNoContainer", fromBin)

          ev["container"] = containerId
        } else if (!containerId.isNullOrBlank()) {
          // 录了容器没录库位
          val containerEv = BinContainerInvReadService.mustGetContainer(containerId)
          fromBin = containerEv["bin"] as String?
          if (fromBin.isNullOrBlank()) throw BzError("errContainerNoBinMissingFromBin", containerId)

          ev["fromBin"] = fromBin
        } else {
          throw BzError("errContainerOrFromBinSpecified")
        }

        // 提前确定终点库位

        var actualToBin = ""
        val expectToBinId = ev["expectToBin"] as String?
        if (!expectToBinId.isNullOrBlank()) {
          // 指定了终点库位
          val binEv = BinContainerInvReadService.mustGetBin(expectToBinId)
          if (binEv["loadStatus"] != "Empty") throw BzError("errBinNotEmpty", expectToBinId)
          actualToBin = expectToBinId
        } else {
          // 指定了终点库区
          val expectToDistrict = ev["expectToDistrict"] as String?
          if (!expectToDistrict.isNullOrBlank()) {
            val toBin = BinUpdateService.findEmptyBinInDistrict(expectToDistrict, containerId, "")
              ?: throw BzError("errNoEmptyBinInDistrict", expectToDistrict)
            actualToBin = EntityHelper.mustGetId(toBin)
          }
        }

        ev["actualToBin"] = actualToBin

        // 起点库位状态：即将运走
        BinUpdateService.makeBinToUnload(fromBin, "")

        // TODO 容器工作状态
      }
    }

    for (ev in evList) {
      val containerId = ev["container"] as String
      ev["oldInvLines"] = BinContainerInvReadService.loadContainerInvToOldInvLines(containerId)
    }
    return null
  }

  override fun afterCreating(em: EntityMeta, evList: List<EntityValue>) {
    if (em.name != "QsMoveBinOrder") return
    for (ev in evList) {
      highTimeSensitiveExecutor.submitCatch("后处理移库单", logger) {
        moveBin(ev)
      }
    }
  }

  private fun moveBin(ev: EntityValue) = ResLockService.resLock.withLock {
    logger.info("后处理移库单：$ev")
    
    ev["container"] as String
    ev["fromBin"] as String

    // 创建容器搬运单
    val transportOrder: EntityValue = mutableMapOf(
      "container" to ev["container"],
      "kind" to "ContainerMoveBin",
      "status" to "Created",
      "fromBin" to ev["fromBin"],
      "toBin" to ev["actualToBin"],
      "falconTaskDefId" to "QsContainerMoveBin", // 内建
      "falconTaskDefLabel" to "处理移库单",
      "postProcessMark" to "MoveBin", // 处理标记
    )
    val id = EntityRwService.createOne("ContainerTransportOrder", transportOrder)
    logger.info("创建容器搬运单 id=$id，$transportOrder")
  }

}