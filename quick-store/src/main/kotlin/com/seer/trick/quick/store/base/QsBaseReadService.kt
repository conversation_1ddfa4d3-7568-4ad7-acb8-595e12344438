package com.seer.trick.quick.store.base

import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions

/**
 * 仓配，基础读取类功能
 */
object QsBaseReadService {

  /**
   * 根据库位 ID 获取库位对象
   */
  fun mustGetBin(binId: String): EntityValue = EntityRwService.findOneById("FbBin", binId)
    ?: throw BzError("errNoBinById", binId)

  /**
   * 根据容器 ID 获取容器对象
   */
  fun mustGetContainer(containerId: String): EntityValue =
    EntityRwService.findOneById("FbContainer", containerId)
      ?: throw BzError("errNoSuchContainerById", containerId)

  /**
   * 根据库位 ID 获取库位库存记录，找不到返回 null
   */
  fun getBinInvByBin(binId: String): EntityValue? =
    EntityRwService.findOne("FbBinInv", Cq.eq("bin", binId))

  /**
   * 根据库位 ID 获取库位库存记录
   */
  fun mustGetBinInvByBin(binId: String): EntityValue =
    getBinInvByBin(binId) ?: throw BzError("errNoBinInvByBin", binId)

  /**
   * 根据容器 ID 获取库位库存记录，找不到返回 null
   */
  fun getBinInvByContainer(containerId: String): EntityValue? =
    EntityRwService.findOne("FbBinInv", Cq.eq("topContainer", containerId))

  /**
   * 根据容器 ID 获取库位库存记录
   */
  fun mustGetBinInvByContainer(containerId: String): EntityValue =
    getBinInvByContainer(containerId) ?: throw BzError("errNoBinInvByContainer", containerId)

  /**
   * 根据容器 ID 获取此容器当前正在的库位 ID，没有返回 null
   */
  fun loadBinIdOfContainer(containerId: String): String? {
    val ev =
      EntityRwService.findOne("FbBinInv", Cq.eq("topContainer", containerId), FindOptions(projection = listOf("bin")))
        ?: return null
    val bin = ev["bin"] as String?
    if (bin.isNullOrBlank()) return null // 强制空串返回 null
    return bin
  }

  /**
   * 获取库位上的容器 ID，没有返回 null
   */
  fun loadContainerIdOfBin(binId: String): String? {
    val ev =
      EntityRwService.findOne("FbBinInv", Cq.eq("bin", binId), FindOptions(projection = listOf("topContainer")))
        ?: return null
    val container = ev["topContainer"] as String?
    if (container.isNullOrBlank()) return null // 强制空串返回 null
    return container
  }

  /**
   * 加载容器库存明细，转化成 QsOldInvLine
   */
  fun loadContainerInvToOldInvLines(containerId: String): List<EntityValue> {
    val lines = EntityRwService.findMany("FbInvLayout", Cq.eq(FieldMeta.FIELD_TOP_CONTAINER, containerId))
    for (line in lines) {
      line["refInvId"] = line["id"]
      line.remove("id")
    }
    return lines
  }

  /**
   * 加载容器内的所有库存并按格子分好
   */
  fun loadContainerInvBySub(containerId: String): Map<Int, List<EntityValue>> {
    // 加载容器内所有库存，并按各自分好
    val containerInvLayouts = EntityRwService.findMany(
      "FbInvLayout",
      Cq.eq(FieldMeta.FIELD_TOP_CONTAINER, containerId),
    )
    val subInvLayouts: MutableMap<Int, MutableList<EntityValue>> = mutableMapOf()
    for (cil in containerInvLayouts) {
      var subContainerId = cil[FieldMeta.FIELD_SUB_CONTAINER_ID] as Int? ?: 1
      if (subContainerId <= 0) subContainerId = 1
      subInvLayouts.getOrPut(subContainerId) { ArrayList() }.add(cil)
    }
    return subInvLayouts
  }

  /**
   * 根据库位 id 更新库位库存
   */
  fun updateBinInvById(binId: String, ev: EntityValue) {
    EntityRwService.updateOne("FbBinInv", Cq.eq("bin", binId), ev)
  }

  /**
   * 新增一条库位库存
   */
  fun addBinInvRecord(ev: EntityValue) {
    EntityRwService.createOne("FbBinInv", ev)
  }

  /**
   * 根据容器 id 删除
   */
  fun removeBinInvByContainerId(containerId: String) {
    EntityRwService.removeOne("FbBinInv", Cq.eq("topContainer", containerId))
  }

  /**
   * 根据容器 id 找到最外层容器
   */
  fun findTopContainerId(containerId: String): String {
    val mgc = mustGetContainer(containerId)
    val pContainer = mgc["pContainer"] as String?
    if (!pContainer.isNullOrEmpty()) {
      return findTopContainerId(pContainer)
    }
    return containerId
  }
}