package com.seer.trick.quick.store.stats

import com.seer.trick.ComplexQuery
import com.seer.trick.Cq
import com.seer.trick.base.concurrent.BaseConcurrentCenter
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta.Companion.FIELD_PARENT_ID
import com.seer.trick.base.entity.service.*
import com.seer.trick.base.soc.SysMonitorService
import com.seer.trick.base.stats.manila.ManilaReportService
import com.seer.trick.helper.DateHelper
import org.apache.commons.lang3.time.DateUtils
import java.util.Calendar
import java.util.Date
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

object QsStatsService {
  
  private const val QS_INBOUND_ORDER_COUNT = "QsInboundOrderCount"
  private const val QS_INBOUND_INV_QTY = "QsInboundInvQty"
  private const val QS_OUTBOUND_ORDER_COUNT = "QsOutboundOrderCount"
  private const val QS_OUTBOUND_INV_QTY = "QsOutboundInvQty"
  private const val QS_STATS_INV_SUMMARY = "QsStatsInvSummary"
  private const val QS_STATS_MATERIAL_COUNT = "QsStatsMaterialCount"
  
  // 定时器向 worker 中插入任务
  private val scheduler = Executors.newSingleThreadScheduledExecutor()
  
  // 统计报表放到单线程中执行，避免占用过多资源。改用 BaseConcurrentCenter.statsExecutor
  // private val worker = Executors.newSingleThreadExecutor()
  
  fun init() {
    scheduler.scheduleAtFixedRate(
      ::genReportQuickStore, // TODO 每次都是全量统计？
      30 * 60 * 1000,
      2 * 60 * 60 * 1000,
      TimeUnit.MILLISECONDS,
    )
  }
  
  fun dispose() {
    scheduler.shutdownNow()
    // BaseConcurrentCenter.statsExecutor.shutdownNow()
  }
  
  private val qsInboundOutboundEntityMap = mapOf(
    "QsInboundOrder" to "QsInboundOrderLine",
    "QsOutboundOrder" to "QsOutboundOrderLine",
  )
  
  private val qsInboundOutboundStatsType =
    listOf(StatisticDateType.Day, StatisticDateType.Month, StatisticDateType.Year)
  
  fun genReportQuickStore(additionalCq: ComplexQuery? = null) {
    BaseConcurrentCenter.statsExecutor.submit {
      
      val logInstant = ManilaReportService.disableLogging()
      try {
        SysMonitorService.log(
          "Stat",
          "genReportQuickStore",
          "-",
          "Stat start",
          instant = logInstant,
        )
        statsQsInboundOutboundOrder(additionalCq)
        statsInvSummaryAndMaterialCount()
      } catch (e: Exception) {
        SysMonitorService.log(
          "Stat",
          "genReportQuickStore",
          "-",
          "Stat fail: ${e.message}",
          instant = logInstant,
        )
      } finally {
        SysMonitorService.log(
          "Stat",
          "genReportQuickStore",
          "-",
          "Stat finished",
          instant = logInstant,
        )
      }
    }
  }
  
  private fun statsQsInboundOutboundOrder(additionalCq: ComplexQuery? = null) {
    if (ManilaReportService.disabled()) return
    
    for ((orderName, lineName) in qsInboundOutboundEntityMap) {
      val orderCountSubject = if (orderName == "QsInboundOrder") QS_INBOUND_ORDER_COUNT else QS_OUTBOUND_ORDER_COUNT
      val lineSumSubject = if (orderName == "QsInboundOrder") QS_INBOUND_INV_QTY else QS_OUTBOUND_INV_QTY
      SysMonitorService.log(
        "Stat",
        lineSumSubject,
        "qty",
        "Start::stat",
        instant = ManilaReportService.disableLogging(),
      )
      val cq = additionalCq ?: Cq.all()
      for (type in qsInboundOutboundStatsType) {
        val firstOrder =
          EntityRwService.findOne(orderName, cq, FindOptions(sort = listOf("createdOn"))) ?: continue
        val firstDate = DateHelper.anyToDate(firstOrder["createdOn"]) ?: Date()
        val toDay = Date()
        var dt = when (type) {
          StatisticDateType.Day -> DateUtils.truncate(firstDate, Calendar.DATE)
          StatisticDateType.Month -> DateUtils.truncate(firstDate, Calendar.MONTH)
          StatisticDateType.Year -> DateUtils.truncate(firstDate, Calendar.YEAR)
          else -> DateUtils.truncate(firstDate, Calendar.DATE) // TODO 暂不支持 Hour
        }
        var dtStr: String
        while (dt <= toDay) {
          val period = ManilaReportService.formatPeriod(dt, type)
          val startedOn = ManilaReportService.date2StartedOn(dt, type)
          val finishedOn = ManilaReportService.date2FinishedOn(dt, type)
          val orders = EntityRwService.findMany(
            orderName,
            Cq.and(Cq.gte("createdOn", startedOn), Cq.lte("createdOn", finishedOn)),
          )
          // 统计单头
          EntityRwService.removeMany(
            "StatsTimelineValueReport",
            Cq.and(
              Cq.eq("subject", orderCountSubject),
              Cq.eq("target", "all"),
              Cq.eq("period", period),
            ),
          )
          EntityRwService.createOne(
            "StatsTimelineValueReport",
            mutableMapOf(
              "subject" to orderCountSubject,
              "target" to "all",
              "periodType" to type,
              "period" to period,
              "value" to orders.size,
              "startedOn" to startedOn,
              "finishedOn" to finishedOn,
            ),
          )
          
          // 统计单行
          val orderIds = orders.map { it["id"] as String }
          val lineAo = AggregationOptions(
            fields = listOf(AdvancedField(AggFun.SUM, "qty", "value")),
            groupBy = listOf(GroupByField("createdOn", "createdOn", type)), // TODO 如果要按物料汇总，改这里
          )
          val lineSumResult = EntityStatsService.aggregateQuery(
            lineName,
            Cq.include(FIELD_PARENT_ID, orderIds),
            lineAo,
          )
          dtStr = DateHelper.formatDate(dt, "yyyy-MM-dd HH")
          SysMonitorService.log(
            "Stat",
            lineSumSubject,
            "qty",
            "aggregate::$dtStr, period: $type, size: ${lineSumResult.size}, first: ${lineSumResult.firstOrNull()}",
            instant = ManilaReportService.disableLogging(),
          )
          // 不区分物料的话，应该只有一行
          for (ev in lineSumResult) {
            EntityRwService.removeMany(
              "StatsTimelineValueReport",
              Cq.and(
                Cq.eq("subject", lineSumSubject),
                Cq.eq("target", "all"),
                Cq.eq("period", period),
              ),
            )
            EntityRwService.createOne(
              "StatsTimelineValueReport",
              mutableMapOf(
                "subject" to lineSumSubject,
                "target" to "all",
                "periodType" to type,
                "period" to period,
                "value" to ev["value"],
                "startedOn" to startedOn,
                "finishedOn" to finishedOn,
              ),
            )
          }
          dt = when (type) {
            StatisticDateType.Hour -> DateUtils.addHours(dt, 1)
            StatisticDateType.Day -> DateUtils.addDays(dt, 1)
            StatisticDateType.Week -> DateUtils.addWeeks(dt, 1)
            StatisticDateType.Month -> DateUtils.addMonths(dt, 1)
            StatisticDateType.Quarter -> DateUtils.addMonths(dt, 3)
            StatisticDateType.Year -> DateUtils.addYears(dt, 1)
          }
        }
      }
      SysMonitorService.log(
        "Stat",
        lineSumSubject,
        "qty",
        "Finish::Stat",
        remove = true,
        instant = ManilaReportService.disableLogging(),
      )
    }
  }
  
  /**
   * 根据库存快照 + 库存变更记录，计算某天结束时的期内库存数量、期内物料数量
   *
   * 1. 取最近一周的日期
   * 2. 按日期处理，从库存快照表取 d 天最后一组数据（同 uuid）
   * 3. 取 d 日的快照创建时间后的库存变更，将库存快照 + 库存变更汇总数据 = 某日结余库存
   */
  private fun statsInvSummaryAndMaterialCount() {
    if (ManilaReportService.disabled()) return
    
    val lineSumSubject = "$QS_STATS_INV_SUMMARY&$QS_STATS_MATERIAL_COUNT"
    SysMonitorService.log(
      "Stat",
      lineSumSubject,
      "qty",
      "Start::Stat",
      instant = ManilaReportService.disableLogging(),
    )
    val lastSnapShot =
      EntityRwService.findOne("FbInvSnapShot", Cq.all(), FindOptions(sort = listOf("-id"))) ?: return
    val uuid = lastSnapShot["uuid"] as String? ?: return
    val snapShotTime = DateHelper.anyToDate(lastSnapShot["createdOn"]) ?: return
    val snapShots = EntityRwService.findMany(
      "FbInvSnapShot",
      Cq.eq("uuid", uuid),
      FindOptions(sort = listOf("-id")),
    )
    
    val invSummary: MutableMap<String, Double> = snapShots.associateBy(
      { it["btMaterial"] as String },
      { it["qty"] as Double },
    ).toMutableMap()
    
    // 将一周前到现在的时间，按天拆成数组，取其结束时间
    // val lastWeek = Date(System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000)
    
    // 从上次库存快照的时间到现在，按天拆成数组
    val days = (snapShotTime.time until Date().time).step(24 * 60 * 60 * 1000).map { Date(it) }
    
    val ao = AggregationOptions(
      fields = listOf(AdvancedField(AggFun.SUM, "qty", "value")),
      groupBy = listOf(GroupByField("btMaterial", "btMaterial", null)),
    )
    var dStr: String
    for (d in days) {
      // 取 d 日的开始时间，或者生成了库存快照后的时间
      val t = DateHelper.getDayStart(d)
      val startTime = if (t.after(snapShotTime)) t else snapShotTime
      val endTime = DateHelper.getDayEnd(d)
      
      // 取 d 日 lastT 时刻之后的所有库存变更，汇总后加到 invSummary
      val ar =
        EntityStatsService.aggregateQuery(
          "FbInvChange",
          Cq.between("createdOn", startTime, endTime),
          ao,
        )
      dStr = DateHelper.formatDate(d, "yyyy-MM-dd HH")
      SysMonitorService.log(
        "Stat",
        lineSumSubject,
        "qty",
        "aggregate::$dStr, period: Day, size: ${ar.size}, first: ${ar.firstOrNull()}",
        instant = ManilaReportService.disableLogging(),
      )
      for (ev in ar) {
        val btMaterial = ev["btMaterial"] as String
        val qty = ev["value"] as Double
        invSummary[btMaterial] = invSummary.getOrDefault(btMaterial, 0.0) + qty
      }
      // val period = ar.firstOrNull()?.get("createdOn") as String?
      val period = DateHelper.formatDate(d, "yyyy-MM-dd")
      
      // 生成期内库存数量报表
      for ((target, v) in invSummary) {
        EntityRwService.removeMany(
          "StatsTimelineValueReport",
          Cq.and(Cq.eq("subject", QS_STATS_INV_SUMMARY), Cq.eq("target", target), Cq.eq("period", period)),
        )
        EntityRwService.createOne(
          "StatsTimelineValueReport",
          mutableMapOf(
            "subject" to QS_STATS_INV_SUMMARY,
            "target" to target,
            "periodType" to StatisticDateType.Day,
            "period" to period,
            "value" to v,
            "startedOn" to startTime,
            "finishedOn" to endTime,
          ),
        )
      }
      // 生成期内物料数量报表
      createOrUpdateReport(
        "all",
        period,
        invSummary.values.filter {
          it > 0
        }.size,
        QS_STATS_MATERIAL_COUNT,
        StatisticDateType.Day,
      )
      
      createOrUpdateReport("all", period, invSummary.values.sum(), QS_STATS_INV_SUMMARY, StatisticDateType.Day)
      
      // 统计月
      statsInvSummaryAndMaterialMonthAndYear(
        snapShotTime,
        snapShots,
        DateHelper.formatDate(
          snapShotTime,
          "yyyy-MM",
        ),
        GroupByField("createdOn", "createdOn", StatisticDateType.Month),
        StatisticDateType.Month,
      )
      // 统计年
      statsInvSummaryAndMaterialMonthAndYear(
        snapShotTime,
        snapShots,
        DateHelper.formatDate(
          snapShotTime,
          "yyyy",
        ),
        GroupByField("createdOn", "createdOn", StatisticDateType.Year),
        StatisticDateType.Year,
      )
    }
    SysMonitorService.log(
      "Stat",
      lineSumSubject,
      "qty",
      "Finish::Stat",
      remove = true,
      instant = ManilaReportService.disableLogging(),
    )
  }
  
  private fun statsInvSummaryAndMaterialMonthAndYear(
    snapShotTime: Date,
    snapShots: List<EntityValue>,
    defPeriod: String,
    groupByField: GroupByField,
    sdt: StatisticDateType,
  ) {
    val fields = listOf(AdvancedField(AggFun.SUM, "qty", "value"))
    val gt = Cq.gt("createdOn", snapShotTime)
    val aom = AggregationOptions(
      fields = fields,
      groupBy = listOf(
        GroupByField("btMaterial", "btMaterial", null),
        groupByField,
      ),
    )
    val aggregatedResults = EntityStatsService.aggregateQuery("FbInvChange", gt, aom)
    val btMap = aggregatedResults.associateBy({ it["btMaterial"] as String }, { it }).toMutableMap()
    
    val invSummary: MutableMap<String, Double> = snapShots.associateBy(
      { it["btMaterial"] as String },
      { it["qty"] as Double },
    ).toMutableMap()
    
    val all: MutableMap<String, Double> = mutableMapOf()
    val allMaterial: MutableMap<String, MutableMap<String, Double>> = mutableMapOf()
    
    invSummary.forEach { (target, v) ->
      val removedData = btMap.remove(target) ?: emptyMap()
      invSummary[target] = v + (removedData["value"] ?: 0.0) as Double
      val period = removedData.getOrDefault("createdOn", defPeriod) as String
      all[period] = all.getOrDefault(period, 0.0) + invSummary[target]!!
      val orDefault = allMaterial.getOrDefault(period, mutableMapOf())
      orDefault[target] = orDefault.getOrDefault(target, 0.0) + invSummary[target]!!
      allMaterial[period] = orDefault
      
      createOrUpdateReport(target, period, invSummary[target]!!, QS_STATS_INV_SUMMARY, sdt)
    }
    
    btMap.forEach { (target, data) ->
      val period = data["createdOn"] as String? ?: return@forEach
      val value = (data["value"] ?: 0.0) as Double
      all[period] = all.getOrDefault(period, 0.0) + value
      
      val orDefault = allMaterial.getOrDefault(period, mutableMapOf())
      orDefault[target] = orDefault.getOrDefault(target, 0.0) + value
      allMaterial[period] = orDefault
      createOrUpdateReport(target, period, value, QS_STATS_INV_SUMMARY, sdt)
    }
    
    all.forEach { (period, value) ->
      createOrUpdateReport("all", period, value, QS_STATS_INV_SUMMARY, sdt)
    }
    
    allMaterial.forEach { (period, value) ->
      createOrUpdateReport("all", period, value.filter { it.value > 0 }.size, QS_STATS_MATERIAL_COUNT, sdt)
    }
  }
  
  private fun createOrUpdateReport(
    target: String,
    period: String,
    value: Any,
    subject: String,
    sdt: StatisticDateType,
  ) {
    EntityRwService.removeMany(
      "StatsTimelineValueReport",
      Cq.and(
        Cq.eq("subject", subject),
        Cq.eq("target", target),
        Cq.eq("periodType", sdt),
        Cq.eq("period", period),
      ),
    )
    EntityRwService.createOne(
      "StatsTimelineValueReport",
      mutableMapOf(
        "subject" to subject,
        "target" to target,
        "periodType" to sdt.name,
        "period" to period,
        "value" to value,
        "startedOn" to ManilaReportService.period2StartedOn(period, sdt),
        "finishedOn" to ManilaReportService.period2FinishedOn(period, sdt),
      ),
    )
  }
}