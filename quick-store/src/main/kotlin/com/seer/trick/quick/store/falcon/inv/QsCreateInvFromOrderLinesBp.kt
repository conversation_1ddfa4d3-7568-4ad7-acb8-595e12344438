package com.seer.trick.quick.store.falcon.inv

import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.bz.wms.inv.CreateInvLine
import com.seer.trick.bz.wms.inv.CreateInvService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.falcon.domain.ParamObjectType
import com.seer.trick.helper.NumHelper
import com.seer.trick.helper.StringHelper
import com.seer.trick.quick.store.base.QsBaseReadService

/**
 * 从单据单行创建库存
 */
class QsCreateInvFromOrderLinesBp : AbstractBp() {

  @Suppress("UNCHECKED_CAST")
  override fun process() {
    // 单据
    val orderEv = mustGetBlockInputParam("order") as EntityValue
    val orderId = EntityHelper.mustGetId(orderEv)
    val lines = EntityHelper.getLines(orderEv, FieldMeta.FIELD_LINES)
    if (lines.isNullOrEmpty()) {
      logger.info("lines is empty $orderId")
      return
    }

    // 数量字段
    val qtyFieldName = mustGetBlockInputParam("qtyFieldName") as String
    // 物料字段
    val materialFieldName = mustGetBlockInputParam("materialFieldName") as String
    // 库存状态
    val state = getBlockInputParam("state") as String? ?: ""
    // 库位
    val binId = getBlockInputParam("bin") as String?
    // 容器
    val container = getBlockInputParam("container") as String?
    // 从单行拷贝哪些附加字段，一般是批次、供应商等特征
    val copyFieldsStr = getBlockInputParam("copyFields") as String?
    val copyFields = StringHelper.splitTrim(copyFieldsStr, ",")

    val reqLines = mutableListOf<CreateInvLine>()
    var topContainer: String? = null
    if (!container.isNullOrEmpty()) {
      topContainer = QsBaseReadService.findTopContainerId(container)
    }

    for (line in lines) {
      val qty = NumHelper.anyToDouble(line[qtyFieldName]) ?: continue
      if (qty <= 0) continue

      val materialId = line[materialFieldName] as String?
      if (materialId.isNullOrBlank()) continue

      val moreFields = mutableMapOf<String, Any?>()
      for (fn in copyFields) moreFields[fn] = line[fn]
//      if (!binId.isNullOrBlank()) {
//        val bin = EntityRwService.findOneById("FbBin", binId) ?: throw BzError("errNoBinById", binId)
//        moreFields["district"] = bin["district"]
//        moreFields["row"] = bin["row"]
//        moreFields["column"] = bin["column"]
//        moreFields["layer"] = bin["layer"]
//        moreFields["depth"] = bin["depth"]
//      }
      moreFields[FieldMeta.FIELD_TOP_CONTAINER] = topContainer
      reqLines += CreateInvLine(
        qty = qty,
        material = materialId,
        state = state,
        bin = binId,
        leafContainer = container,
        subContainerId = null,
        moreFields = moreFields,
      )
    }

    if (reqLines.isNotEmpty()) {
      val ids = CreateInvService.fixCreateInvLayoutByLines(reqLines)
      // 添加猎鹰任务相关对象
      if (!container.isNullOrBlank()) {
        addRelatedObject("FbContainer", container, null)
      }
      for (id in ids) {
        addRelatedObject("FbInvLayout", id, null)
      }
    }
  }

  companion object {

    val def = BlockDef(
      QsCreateInvFromOrderLinesBp::class.simpleName!!,
      color = "#18b0a1",
      inputParams = listOf(
        BlockInputParamDef(
          "order",
          BlockParamType.JSONObject,
          true,
          objectTypes = listOf(ParamObjectType.OrderId),
        ),
        BlockInputParamDef(
          "qtyFieldName",
          BlockParamType.String,
          false,
          objectTypes = listOf(ParamObjectType.EntityFieldName),
        ),
        BlockInputParamDef(
          "materialFieldName",
          BlockParamType.String,
          false,
          objectTypes = listOf(ParamObjectType.EntityFieldName),
        ),
        BlockInputParamDef("state", BlockParamType.String, false),
        BlockInputParamDef(
          "bin",
          BlockParamType.String,
          false,
          objectTypes = listOf(ParamObjectType.BinId),
        ),
        BlockInputParamDef(
          "container",
          BlockParamType.String,
          false,
          objectTypes = listOf(ParamObjectType.ContainerId),
        ),
        BlockInputParamDef("copyFields", BlockParamType.String, false),
      ),
    )
  }
}