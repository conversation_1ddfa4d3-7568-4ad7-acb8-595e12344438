package com.seer.trick.quick.store.falcon.bin

import com.seer.trick.Cq
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*
import com.seer.trick.quick.store.base.QsBaseUpdateService

/**
 * 移除业务标记
 */
class QsRemoveBzMarkBp : AbstractBp() {

  override fun process() {
    val binId = mustGetBlockInputParam("binId") as String

    QsBaseUpdateService.removeBzMark(Cq.eq("bin", binId))

    addRelatedObject("FbBin", binId, null)
  }

  companion object {
    val def = BlockDef(
      QsRemoveBzMarkBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(
        BlockInputParamDef("binId", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.BinId)),
      ),
    )
  }
}