package com.seer.trick.quick.store.base

import com.seer.trick.Cq

import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.entity.service.change.EntityChange
import com.seer.trick.base.entity.service.extension.EntityServiceExtension
import org.slf4j.LoggerFactory

/**
 * 增删改库位 -> 同步库位库存
 * 停用的库位也同步，在库位库存表里也停用
 *
 * fixme 只更库位相关字段，不更新容器相关字段:
 * - 如果库位表保留 container、容器表保留 bin 字段，则上架后会同时更新容器表、库位表。
 *    此类只更新库位相关字段，SyncContainerToBinInvService 更新容器相关字段。
 * - 如果库位表不保留 container、容器表不保留 bin 字段，则上架需要调用 BinInvService#updateBinInv
 *    无需在此类中更新容器相关字段
 *
 * fixme
 * TODO 暂定：不能直接改库位上的容器。如果要改库位上的容器，需要调用 BinInvService#updateBinInv
 *
 */
object SyncBinToBinInvService : EntityServiceExtension() {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 创建一条库位后，创建一条库位库存
   * TODO 要不要在资源锁内
   */
  override fun afterCreating(em: EntityMeta, evList: List<EntityValue>) {
    if (em.name != "FbBin") return
    QsResService.resExecutor.submit {
      try {
        val binInvEvList = evList.map(::binToBinInv)
        EntityRwService.createMany("FbBinInv", binInvEvList)
      } catch (e: Exception) {
        logger.error("Failed to create bin inventories for creating bins", e)
      }
    }
  }

  /**
   * 如果库位更新了，把库位库存的同名属性也更新，如所属库区、仓库
   */
  override fun afterUpdating(em: EntityMeta, changes: List<EntityChange>) {
    if (em.name != "FbBin") return
    QsResService.resExecutor.submit {
      try {
        for (c in changes) {
          val ev = c.newValue ?: continue
          saveBinInv(c.id, binToBinInvPatch(ev))
        }
      } catch (e: Exception) {
        logger.error("Failed to update bin inventories for updating bins", e)
      }
    }
  }

  /**
   * 删除了库位，也删除库位库存，暂时不做任何校验
   *
   * 删了库位时，考虑容器是否还在
   */
  override fun afterRemoving(em: EntityMeta, oldValues: List<EntityValue>) {
    if (em.name != "FbBin") return
    QsResService.resExecutor.submit {
      try {
        val binIds = oldValues.map(EntityHelper::mustGetId)
        removeBins(binIds)
      } catch (e: Exception) {
        logger.error("Failed to remove bin inventories for removing bins", e)
      }
    }
  }

  /**
   * 库位容器做过导入导出了……需要比对两边
   */
  override fun afterBigChange(em: EntityMeta) {
    if (em.name != "FbBin") return
    QsResService.resExecutor.submit {
      try {
        completeSync()
      } catch (e: Exception) {
        logger.error("Failed to sync after bins big changes", e)
      }
    }
  }

  /**
   * 全量同步。比对库位和库位库存。
   * 耗时
   * TODO 在锁里？
   */
  fun completeSync() {
    // 所有库位
    val binEvList = EntityRwService.findMany("FbBin", Cq.all())
    // 所有库位 ID
    val binIdSet = binEvList.map(EntityHelper::mustGetId).toSet()

    // 所有库位库存
    val binInvList = EntityRwService.findMany("FbBinInv", Cq.all(), FindOptions(projection = listOf("id", "bin")))

    // 库位库存表里已存在的库位 ID
    val existedBinIdSet = binInvList.mapNotNull { it["bin"] as String? }.toSet()

    // 待新增的库位库存
    val newBinInvList = mutableListOf<EntityValue>()

    for (binEv in binEvList) {
      val binId = EntityHelper.mustGetId(binEv)
      if (binId !in existedBinIdSet) {
        // 新增的库位
        newBinInvList += binToBinInv(binEv)
      } else {
        // 更新库位（可能实际没更新，都当做更新）
        saveBinInv(binId, binToBinInvPatch(binEv))
      }
    }

    if (newBinInvList.isNotEmpty()) {
      EntityRwService.createMany("FbBinInv", newBinInvList)
    }

    // 要删除的库位库存
    val removingBinIds = binInvList.filter {
      val binId = it["bin"] as String? // bin 可能为空：容器行
      !binId.isNullOrBlank() && binId !in binIdSet
    }.map { it["bin"] as String }

    removeBins(removingBinIds)
  }

  /**
   * 保存库位库存。存在则更新，不存在则新建
   * fixme 不想再做一次查询了；即使查，也不能一次次查，也不需要加载对象
   */
  private fun saveBinInv(cId: String, binInvPatch: EntityValue) {
    val exist = EntityRwService.findOne("FbBinInv", Cq.eq("bin", cId))
    if (exist == null) {
      EntityRwService.createOne("FbBinInv", binInvPatch)
    } else {
      EntityRwService.updateOne("FbBinInv", Cq.eq("bin", cId), binInvPatch)
    }
  }

  /**
   * 删除库位的库位库存。如果库位库存上有容器，则只清空库位相关字段
   */
  private fun removeBins(binIds: List<String>) {
    // 有容器，只清空库位字段
    EntityRwService.updateMany(
      
      "FbBinInv",
      // fixme 如果定义了 topContainer 常量，要用
      Cq.and(Cq.include("bin", binIds), Cq.notEmpty("topContainer")),
      buildCleanBinPatch(),
    )
    // 没有容器，直接删掉库位库存
    EntityRwService.removeMany("FbBinInv", Cq.and(Cq.include("bin", binIds), Cq.empty("topContainer")))
  }

  /**
   * 库位 FbBin 字段名 -> 库位库存 FbBinInv 字段名
   */
  private val binToBinInvFieldMapping = mapOf(
    "id" to "bin",
    "btDisabled" to "binDisabled",
    "occupied" to "binFilled",
    "district" to "district",
    "warehouse" to "warehouse",
    "row" to "row",
    "column" to "column",
    "layer" to "layer",
    "depth" to "depth",
    "rack" to "rack",
    "channel" to "channel",
    "workSite" to "workSite",
    "assemblyLine" to "assemblyLine",
  )

  /**
   * 清空库位字段的 update
   */
  private fun buildCleanBinPatch(): EntityValue = binToBinInvFieldMapping.mapValues { null }.toMutableMap()

  /**
   * 将一个新库位对象转换为一个库位库存对象
   */
  private fun binToBinInv(binEv: EntityValue): EntityValue {
    val r: EntityValue = mutableMapOf()
    for ((sf, tf) in binToBinInvFieldMapping) {
      r[tf] = binEv[sf]
    }
    return r
  }

  /**
   * 将一个修改后的库位对象转换为需要更新到库位库存对象的对象：更新库位的属性
   *
   * TODO 是否有必要单独存在
   */
  private fun binToBinInvPatch(newValue: EntityValue): EntityValue = binToBinInv(newValue)
}