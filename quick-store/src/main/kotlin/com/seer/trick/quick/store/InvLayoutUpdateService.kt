package com.seer.trick.quick.store

import com.seer.trick.Cq
import com.seer.trick.base.entity.service.EntityRwService
import org.slf4j.LoggerFactory

/**
 * 所有对库存明细的更新放在这里
 */
@Deprecated("即将删除")
object InvLayoutUpdateService {

  private val logger = LoggerFactory.getLogger(this::class.java)

  fun updateInvLayoutUsedQty(id: String, usedQty: Double, oldUsedQty: Double) {
    logger.info("更新库存明细 已分配数量，id=$id，从 $oldUsedQty 到 $usedQty")
    EntityRwService.updateOne(
      "FbInvLayout", Cq.idEq(id), mutableMapOf("usedQty" to usedQty)
    )
  }

}