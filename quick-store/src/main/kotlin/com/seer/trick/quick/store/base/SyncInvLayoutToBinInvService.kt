package com.seer.trick.quick.store.base

import com.seer.trick.Cq

import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.change.EntityChange
import com.seer.trick.base.entity.service.extension.EntityServiceExtension
import com.seer.trick.helper.NumHelper
import org.slf4j.LoggerFactory

/**
 * 增删改库存明细 -> 同步库位库存
 * TODO 停用的库存明细，算没有这个库存
 */
object SyncInvLayoutToBinInvService : EntityServiceExtension() {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 新增了库存明细。将关联库位、容器上的库存重新汇总。
   */
  override fun afterCreating(em: EntityMeta, evList: List<EntityValue>) {
    if (em.name != "FbInvLayout") return
    QsResService.resExecutor.submit {
      try {
        partialSync(evList)
      } catch (e: Exception) {
        logger.error("Failed to sync after inv layouts creating", e)
      }
    }
  }

  /**
   * 修改了库存明细。将关联库位、容器上的库存重新汇总。
   */
  override fun afterUpdating(em: EntityMeta, changes: List<EntityChange>) {
    if (em.name != "FbInvLayout") return
    QsResService.resExecutor.submit {
      try {
        partialSync(changes.mapNotNull { it.oldValue } + changes.mapNotNull { it.newValue })
      } catch (e: Exception) {
        logger.error("Failed to sync after inv layouts updating", e)
      }
    }
  }

  /**
   * 删除了库存明细。将关联库位、容器上的库存重新汇总。
   */
  override fun afterRemoving(em: EntityMeta, oldValues: List<EntityValue>) {
    if (em.name != "FbInvLayout") return
    QsResService.resExecutor.submit {
      try {
        partialSync(oldValues)
      } catch (e: Exception) {
        logger.error("Failed to sync after inv layouts removing", e)
      }
    }
  }

  /**
   * 部分同步。新增修改删除的库存明细，包括改变前后的。
   */
  private fun partialSync(evList: List<EntityValue>) {
    // 受影响的库位
    val binIds = mutableSetOf<String>()

    // 受影响的容器（不在库位上的）
    val containerIds = mutableSetOf<String>()

    for (ly in evList) {
      val binId = ly["bin"] as String?
      if (!binId.isNullOrBlank()) {
        binIds.add(binId)
        continue
      }

      val containerId = ly["topContainer"] as String?
      if (!containerId.isNullOrBlank()) {
        containerIds.add(containerId)
      }
    }

    syncByBins(binIds)
    syncByContainers(containerIds)
  }

  /**
   * 重新计算指定库位上的库存
   */
  private fun syncByBins(binIds: Set<String>) {
    if (binIds.isEmpty()) return
    val evList = EntityRwService.findMany("FbInvLayout", Cq.include("bin", binIds.toList()))
    if (evList.isNotEmpty()) {
      updateBinInv(evList)
    } else {
      removeBinMaterial(binIds)
    }
  }

  /**
   * 移除库位上的物料
   */
  private fun removeBinMaterial(binIds: Set<String>) {
    for (binId in binIds) {
      val ev = EntityRwService.findOne("FbBinInv", Cq.eq("bin", binId)) ?: continue
      val containers = if ((ev["containers"] as String?).isNullOrEmpty()) {
        mutableSetOf()
      } else {
        (ev["containers"] as String).split(",").toMutableSet()
      }
      val newEv = toBinInvPatch(InvSummary(bins = mutableSetOf(binId), containers = containers))
      newEv.remove("containers")
      newEv.remove("topContainer")
      EntityRwService.updateOne("FbBinInv", Cq.eq("bin", binId), newEv)
    }
  }

  /**
   * 重新计算指定容器上的库存
   */
  private fun syncByContainers(containerIds: Set<String>) {
    if (containerIds.isEmpty()) return
    val evList = EntityRwService.findMany("FbInvLayout", Cq.include("topContainer", containerIds.toList()))
    if (evList.isNotEmpty()) {
      updateBinInv(evList)
    } else {
      removeContainersMaterial(containerIds)
    }
  }

  /**
   * 移除容器上的物料
   */
  private fun removeContainersMaterial(containerIds: Set<String>) {
    for (containerId in containerIds) {
      val newEv = toBinInvPatch(InvSummary(containers = mutableSetOf(containerId)))
      newEv.remove("containers")
      newEv.remove("topContainer")
      EntityRwService.updateOne("FbBinInv", Cq.eq("topContainer", containerId), newEv)
    }
  }

  override fun afterBigChange(em: EntityMeta) {
    if (em.name != "FbInvLayout") return
    QsResService.resExecutor.submit {
      try {
        completeSync()
      } catch (e: Exception) {
        logger.error("Failed to sync after inv layouts big changes", e)
      }
    }
  }

  /**
   * 按库位或容器聚合库存
   */
  data class InvSummary(
    var qty: Double = 0.0,
//    var materialId: String? = null,
//    var materialNum: Int = 0,
    val btMaterialIds: MutableSet<String> = mutableSetOf(),
    // TODO 其他字段
    val btMaterialModels: MutableSet<String> = mutableSetOf(),
    val btMaterialSpecs: MutableSet<String> = mutableSetOf(),
    // TODO 重命名
    val btMaterialCategorys: MutableSet<String> = mutableSetOf(),
    val btMaterialCategoryNames: MutableSet<String> = mutableSetOf(),
    val btMaterialNames: MutableSet<String> = mutableSetOf(),
    // TODO 重命名
    val btMaterialTopCategorys: MutableSet<String> = mutableSetOf(),
    val btMaterialTopCategoryNames: MutableSet<String> = mutableSetOf(),
    val lotNos: MutableSet<String> = mutableSetOf(),
    var amount: Double = 0.0,
    val containers: MutableSet<String> = mutableSetOf(),
    val bins: MutableSet<String> = mutableSetOf(),
    val topContainers: MutableSet<String> = mutableSetOf(),
  )

  /**
   * 根据库存明细更细库位容器
   * @param layouts 库存明细的记录
   */
  private fun updateBinInv(layouts: List<EntityValue>) {
    // 按库位聚合的库存
    val binInvMap: MutableMap<String, InvSummary> = HashMap()

    // 按容器聚合的库存
    val containerInvMap: MutableMap<String, InvSummary> = HashMap()

    // 先按库位，否则按最外层容器聚合库存
    for (ly in layouts) {
      val binId = ly["bin"] as String?
      if (!binId.isNullOrBlank()) {
        addToSummary(binInvMap.getOrPut(binId) { InvSummary() }, ly)
        continue
      }

      val containerId = ly["topContainer"] as String?
      if (!containerId.isNullOrBlank()) {
        addToSummary(containerInvMap.getOrPut(containerId) { InvSummary() }, ly)
      }
    }

    // 部分更新到库位库存
    for ((binId, summary) in binInvMap) {
      val ev = toBinInvPatch(summary)
      EntityRwService.updateOne("FbBinInv", Cq.eq("bin", binId), ev)
    }

    // 部分更新到容器库存 TODO 库位解绑容器无法根据容器更新数据，需要在解绑之前新建一条容器数据
    for ((containerId, summary) in containerInvMap) {
      val ev = toBinInvPatch(summary)
      ev.remove("topContainer")
      EntityRwService.updateOne("FbBinInv", Cq.eq("topContainer", containerId), ev)
    }
  }

  /**
   * 全量更新。
   */
  fun completeSync() {
    // 所有库存明细
    val layouts = EntityRwService.findMany("FbInvLayout", Cq.all())
    updateBinInv(layouts)
//    // 按库位聚合的库存
//    val binInvMap: MutableMap<String, InvSummary> = HashMap()
//
//    // 按容器聚合的库存
//    val containerInvMap: MutableMap<String, InvSummary> = HashMap()
//
//    // 先按库位，否则按最外层容器聚合库存
//    for (ly in layouts) {
//      val binId = ly["bin"] as String?
//      if (!binId.isNullOrBlank()) {
//        addToSummary(binInvMap.getOrPut(binId) { InvSummary() }, ly)
//        continue
//      }
//
//      val containerId = ly["topContainer"] as String?
//      if (!containerId.isNullOrBlank()) {
//        addToSummary(containerInvMap.getOrPut(containerId) { InvSummary() }, ly)
//      }
//    }
//
//    // 部分更新到库位库存
//    for ((binId, summary) in binInvMap) {
//      val ev = toBinInvPatch(summary)
//      EntityRwService.updateOne("FbBinInv", Cq.eq("bin", binId), ev)
//    }
//
//    // 部分更新到容器库存
//    for ((containerId, summary) in containerInvMap) {
//      val ev = toBinInvPatch(summary)
//      EntityRwService.updateOne("FbBinInv", Cq.eq("container", containerId), ev)
//    }
  }

  /**
   * 将 summary 转成库位库存业务对象。
   * 注意更新空满状态
   */
  private fun toBinInvPatch(summary: InvSummary): EntityValue {
    // 库位、容器的占用状态
    return mutableMapOf<String, Any?>().apply {
      this["lotNo"] = summary.lotNos.singleOrNull()
      this["qty"] = summary.qty
      this["amount"] = summary.amount

      val hasSingleMaterial = summary.btMaterialIds.size == 1
      this["btMaterial"] = summary.btMaterialIds.firstOrNull().orEmpty().takeIf { hasSingleMaterial } ?: ""
      this["btMaterialId"] = this["btMaterial"]
      this["btMaterialName"] = summary.btMaterialNames.firstOrNull().orEmpty().takeIf { hasSingleMaterial } ?: ""
      this["btMaterialModel"] = summary.btMaterialModels.firstOrNull().orEmpty().takeIf { hasSingleMaterial } ?: ""
      this["btMaterialSpec"] = summary.btMaterialSpecs.firstOrNull().orEmpty().takeIf { hasSingleMaterial } ?: ""
      this["btMaterialCategory"] = summary.btMaterialCategorys.firstOrNull().orEmpty().takeIf { hasSingleMaterial }
        ?: ""
      this["btMaterialCategoryName"] =
        summary.btMaterialCategoryNames.firstOrNull().orEmpty().takeIf { hasSingleMaterial }
          ?: ""
      this["btMaterialTopCategory"] =
        summary.btMaterialTopCategorys.firstOrNull().orEmpty().takeIf { hasSingleMaterial }
          ?: ""
      this["btMaterialTopCategoryName"] =
        summary.btMaterialTopCategoryNames.firstOrNull().orEmpty().takeIf { hasSingleMaterial }
          ?: ""

      this["containers"] = summary.containers.joinToString(",")
      this["materialIds"] = summary.btMaterialIds.joinToString(",")
      this["materialNames"] = summary.btMaterialNames.joinToString(",")

      this["binFilled"] = if (summary.bins.isEmpty()) {
        null
      } else {
        summary.qty > 0 || summary.containers.isNotEmpty()
      }
      this["containerFilled"] = when {
        summary.containers.isNotEmpty() && summary.qty > 0 -> true
        summary.containers.isNotEmpty() -> false
        else -> null
      }
      this["topContainer"] = summary.topContainers.firstOrNull() ?: ""
    }
  }

  /**
   * 将库存明细汇总到 summary
   */
  private fun addToSummary(summary: InvSummary, ly: EntityValue) {
    ly["lotNo"]?.let { summary.lotNos += it as String } // 批次
    ly["qty"]?.let { NumHelper.anyToDouble(ly["qty"])?.let { summary.qty += it } } // 数量
    ly["amount"]?.let { NumHelper.anyToDouble(ly["amount"])?.let { summary.amount += it } } // 金额

    ly["btMaterialId"]?.let { summary.btMaterialIds += it as String } // 物料编号
    ly["btMaterialName"]?.let { summary.btMaterialNames += it as String } // 物料名称
    ly["btMaterialModel"]?.let { summary.btMaterialModels += it as String } // 物料型号
    ly["btMaterialSpec"]?.let { summary.btMaterialSpecs += it as String } // 物料规格

    ly["btMaterialCategory"]?.let { summary.btMaterialCategorys += it as String } // 物料分类编号
    ly["btMaterialCategoryName"]?.let { summary.btMaterialCategoryNames += it as String } // 物料分类名称
    ly["btMaterialTopCategory"]?.let { summary.btMaterialTopCategorys += it as String } // 物料一级分类编号
    ly["btMaterialTopCategoryName"]?.let { summary.btMaterialTopCategoryNames += it as String } // 物料一级分类名称
    ly["leafContainerType"]?.let { summary.containers += it as String } // 容器列表
    ly["topContainer"]?.let {
      summary.containers += it as String
      summary.topContainers += it as String
    } // 容器列表
    ly["bin"]?.let { summary.bins += it as String }
  }
}