package com.seer.trick.quick.store

import com.seer.trick.BzError

import com.seer.trick.base.concurrent.BaseConcurrentCenter.highTimeSensitiveExecutor
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.extension.EntityServiceExtension
import com.seer.trick.base.reslock.ResLockService
import com.seer.trick.helper.BoolHelper
import com.seer.trick.helper.submitCatch
import org.slf4j.LoggerFactory
import kotlin.concurrent.withLock

/**
 * 取走容器（人工下架单）。
 * 只能创建，不允许修改。
 */
object QsTakeOffContainerService : EntityServiceExtension() {

  private val logger = LoggerFactory.getLogger(this::class.java)

  override fun beforeCreating(em: EntityMeta, evList: List<EntityValue>): List<String>? {
    if (em.name != "QsTakeOffContainerOrder") return null
    // TODO 检查参数合法
    // 加载当前库存
    for (ev in evList) {
      var containerId = ev["container"] as String?
      var binId = ev["bin"] as String?
      if (!containerId.isNullOrBlank() && binId.isNullOrBlank()) {
        // 填了容器，找库位
        binId = BinContainerInvReadService.loadBinIdOfContainer(containerId)
        if (binId.isNullOrBlank()) throw BzError("errContainerNoBin", containerId)
      } else if (!binId.isNullOrBlank() && containerId.isNullOrBlank()) {
        // 填了库位，找容器
        containerId = BinContainerInvReadService.loadContainerIdOfBin(binId)
        if (containerId.isNullOrBlank()) throw BzError("errBinNoContainer", containerId)
      } else if (!containerId.isNullOrBlank() && !binId.isNullOrBlank()) {
        // 填了容器和库位，检查是否一致
        val binId2 = BinContainerInvReadService.loadBinIdOfContainer(containerId)
        if (binId2.isNullOrBlank())
          throw BzError("errTakeOffContainerNoBin", containerId, binId)
        else if (binId != binId2)
          throw BzError("errTakeOffContainerBadBin", containerId, binId, binId2)

        val containerId2 = BinContainerInvReadService.loadContainerIdOfBin(binId)
        if (containerId != containerId2)
          throw BzError("errTakeOffContainerBadContainer", binId, containerId, containerId2)
      } else if (containerId.isNullOrBlank() && binId.isNullOrBlank()) {
        // 都没填，不行
        throw BzError("errTakeOffContainerContainerBinAtLeastOne")
      }

      ev["container"] = containerId
      ev["bin"] = binId

      ev["oldInvLines"] = BinContainerInvReadService.loadContainerInvToOldInvLines(containerId!!)
    }
    return null
  }

  override fun afterCreating(em: EntityMeta, evList: List<EntityValue>) {
    if (em.name != "QsTakeOffContainerOrder") return
    for (ev in evList) {
      highTimeSensitiveExecutor.submitCatch("后处理创建人工下架单", logger) {
        takeOffContainer(ev)
      }
    }
  }

  private fun takeOffContainer(ev: EntityValue) = ResLockService.resLock.withLock {
    logger.info("后处理创建人工下架单：$ev")
    val containerId = ev["container"] as String
    val binId = ev["bin"] as String

    val keepInv = BoolHelper.anyToBool(ev["keepInv"])

    BinContainerInvUpdateService.takeOffContainer(containerId, binId, keepInv)
  }

}