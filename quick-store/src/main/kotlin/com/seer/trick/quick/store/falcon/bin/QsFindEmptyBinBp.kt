package com.seer.trick.quick.store.falcon.bin

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.ComplexQuery
import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.StringHelper
import com.seer.trick.quick.store.base.QsResApplication
import com.seer.trick.quick.store.base.QsResAssignmentService
import com.seer.trick.quick.store.base.QsResAssignmentService.MARK_TO_UNLOAD_HERE
import com.seer.trick.quick.store.base.QsResType

/**
 * 在指定库区内寻找一个空库位
 */
class QsFindEmptyBinBp : AbstractBp() {

  override fun process() {
    val districtIdStr = mustGetBlockInputParam("districtIds") as String
    val districtIds = StringHelper.splitTrim(districtIdStr, ",")
    val keepTrying = getBlockInputParamAsBool("keepTrying") as Boolean? ?: false
    val bzDesc = getBlockInputParam("bzDesc") as String? ?: ""

    // 过滤条件
    val cqStr = getBlockInputParam("cq") as String?
    val filter: ComplexQuery =
      if (!cqStr.isNullOrBlank()) JsonHelper.mapper.readValue(cqStr, jacksonTypeRef()) else Cq.all()
    // 排序
    val sortStr = getBlockInputParam("sort") as String?
    val sort: List<String> =
      if (!sortStr.isNullOrBlank()) JsonHelper.mapper.readValue(sortStr, jacksonTypeRef()) else listOf()

    // 调用申请和分配资源机制
    // TODO 按库区顺序，申请资源
    val tx = QsResAssignmentService.begin()
    val app = QsResApplication(
      tx,
      QsResType.Bin,
      timeout = if (keepTrying) -1 else 3,
      num = 1,
      filter = Cq.and(Cq.include("district", districtIds), filter),
      sort = sort,
      bzMark = MARK_TO_UNLOAD_HERE,
      bzDesc = bzDesc,
    )
    QsResAssignmentService.request(app)
    if (tx.bins.size == 1) {
      QsResAssignmentService.commit(tx)
    } else {
      QsResAssignmentService.rollback(tx)
    }

    val bin = tx.bins.firstOrNull()
    if (bin != null) {
      val binId = EntityHelper.mustGetId(bin)
      logger.info("found empty bin in district=$districtIdStr")
      addResource("FindNotOccupiedBin", binId, mapOf("binId" to binId))
      setBlockOutputParams(mapOf("found" to true, "binId" to binId))
    } else {
      logger.info("no empty bin in district=$districtIdStr")
      setBlockOutputParams(mapOf("found" to false, "binId" to null))
    }
  }

  companion object {
    val def = BlockDef(
      QsFindEmptyBinBp::class.simpleName!!,
      color = "#5294E2",
      inputParams = listOf(
        BlockInputParamDef(
          "districtIds",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.DistrictId),
        ),
        BlockInputParamDef("cq", BlockParamType.String, false),
        BlockInputParamDef("sort", BlockParamType.String, false),
        BlockInputParamDef("keepTrying", BlockParamType.Boolean),
        BlockInputParamDef("bzDesc", BlockParamType.String),
      ),
      outputParams = listOf(
        BlockOutputParamDef("found", BlockParamType.Boolean),
        BlockOutputParamDef(
          "binId",
          BlockParamType.String,
          objectTypes = listOf(ParamObjectType.BinId),
        ),
      ),
    )
  }
}