package com.seer.trick.quick.store.falcon.container

import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*

/**
 * 查询容器类型的存储区
 */
class QsGetContainerTypeStoreDistrictsBp : AbstractBp() {

  override fun process() {
    val containerId = mustGetBlockInputParam("containerId") as String

    val containerEv = EntityRwService.findOne("FbContainer", Cq.idEq(containerId))
      ?: throw BzError("errNoSuchContainerById", containerId)

    val containerCategoryId = containerEv["type"] as String?
    if (containerCategoryId.isNullOrBlank()) throw BzError("errContainerNoType", containerId)

    val typeEv = EntityRwService.findOne("FbContainerType", Cq.idEq(containerCategoryId))
      ?: throw BzError("errNoSuchContainerTypeById", containerId)

    @Suppress("UNCHECKED_CAST")
    val storeDistricts = typeEv["storeDistricts"] as List<String>?
    if (storeDistricts.isNullOrEmpty()) throw BzError("errContainerTypeNoStoreDistricts", containerId)

    setBlockOutputParams(mapOf("storeDistricts" to storeDistricts))
  }

  companion object {

    val def = BlockDef(
      QsGetContainerTypeStoreDistrictsBp::class.simpleName!!,
      color = "#bea7e1",
      inputParams = listOf(
        BlockInputParamDef(
          "containerId",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.ContainerId),
        ),
      ),
      outputParams = listOf(
        BlockOutputParamDef(
          "storeDistricts",
          BlockParamType.JSONArray,
          objectTypes = listOf(ParamObjectType.DistrictId),
        ),
      ),
    )
  }
}