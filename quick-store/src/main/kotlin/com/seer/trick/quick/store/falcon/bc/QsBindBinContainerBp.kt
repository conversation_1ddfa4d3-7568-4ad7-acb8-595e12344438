package com.seer.trick.quick.store.falcon.bc

import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.falcon.domain.ParamObjectType
import com.seer.trick.quick.store.base.QsBaseUpdateService

/**
 * 绑定库位与容器
 */
class QsBindBinContainerBp : AbstractBp() {

  override fun process() {
    val binId = mustGetBlockInputParam("binId") as String
    val containerId = mustGetBlockInputParam("containerId") as String
    // TODO 是否保留检查容器与库位的关系
    // val cbs = getBlockInputParamAsBool("checkBinStatus")
    // val ccs = getBlockInputParamAsBool("checkContainerStatus")

    QsBaseUpdateService.moveContainerToBin(containerId, binId)

    addRelatedObject("FbBin", binId, null)
    addRelatedObject("FbContainer", containerId, null)
  }

  companion object {

    val def = BlockDef(
      QsBindBinContainerBp::class.simpleName!!,
      color = "#ffd22b",
      inputParams = listOf(
        BlockInputParamDef(
          "binId",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.BinId),
        ),
        BlockInputParamDef(
          "containerId",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.ContainerId),
        ),
        // BlockInputParamDef("checkBinStatus", BlockParamType.Boolean),
        // BlockInputParamDef("checkContainerStatus", BlockParamType.Boolean),
      ),
    )
  }
}