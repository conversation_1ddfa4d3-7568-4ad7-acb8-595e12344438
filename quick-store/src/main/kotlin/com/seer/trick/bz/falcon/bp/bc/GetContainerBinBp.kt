package com.seer.trick.bz.falcon.bp.bc

import com.seer.trick.Cq
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*

class GetContainerBinBp : AbstractBp() {

  override fun process() {
    val containerId = mustGetBlockInputParam(ipContainerId.name) as String
    
    val bin = EntityRwService.findOne("FbBin", Cq.eq("container", containerId), null)
    if (bin == null) {
      setBlockOutputParams(mapOf(opFound.name to false, opBinId.name to null))
      return
    }
    
    setBlockOutputParams(mapOf(opFound.name to true, opBinId.name to bin["id"]))
  }
  
  companion object {
    private val ipContainerId = BlockInputParamDef(
      "containerId", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.ContainerId)
    )
    
    private val opFound = BlockOutputParamDef("found", BlockParamType.Boolean)
    private val opBinId = BlockOutputParamDef(
      "binId", BlockParamType.String, objectTypes = listOf(ParamObjectType.BinId)
    )
    
    val def = BlockDef(
      GetContainerBinBp::class.simpleName!!,
      color = "#A8D297",
      inputParams = listOf(ipContainerId),
      outputParams = listOf(opFound, opBinId)
    )
  }
  
}