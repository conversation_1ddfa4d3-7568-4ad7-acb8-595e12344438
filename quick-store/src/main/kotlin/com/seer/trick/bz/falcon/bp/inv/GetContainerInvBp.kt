package com.seer.trick.bz.falcon.bp.inv

import com.seer.trick.Cq
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*

class GetContainerInvBp : AbstractBp() {
  
  override fun process() {
    val containerId = mustGetBlockInputParam("containerId") as String
    
    // TODO 应该是 topContainer
    val inv = EntityRwService.findMany("FbInvLayout", Cq.eq(FieldMeta.FIELD_LEAF_CONTAINER, containerId))
    if (inv.isEmpty()) {
      setBlockOutputParams(mapOf("found" to false, "inv" to null))
    } else {
      setBlockOutputParams(mapOf("found" to true, "inv" to inv))
    }
  }
  
  companion object {
    val def = BlockDef(
      GetContainerInvBp::class.simpleName!!,
      color = "#7293d8",
      inputParams = listOf(
        BlockInputParamDef(
          "containerId", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.ContainerId)
        )
      ), outputParams = listOf(
        BlockOutputParamDef("found", BlockParamType.Boolean),
        BlockOutputParamDef("inv", BlockParamType.JSONArray)
      )
    )
  }
  
}