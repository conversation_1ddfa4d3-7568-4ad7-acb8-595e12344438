package com.seer.trick.bz.falcon.bp.container

import com.seer.trick.Cq

import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.falcon.domain.ParamObjectType

class RemoveTraceContainerBp : AbstractBp() {
  override fun process() {
    val containerId = getBlockInputParam(ipContainerId.name)

    // TODO 要不要加锁？
    logger.info("RemoveTraceContainerBp 删除追踪容器")
    EntityRwService.removeOne("FbContainer", Cq.eq("id", containerId))
  }

  companion object {
    private val ipContainerId = BlockInputParamDef(
      "containerId",
      BlockParamType.String,
      true,
      objectTypes = listOf(ParamObjectType.ContainerId),
    )

    val def = BlockDef(
      RemoveTraceContainerBp::class.simpleName!!,
      color = "#EBE3D5",
      inputParams = listOf(ipContainerId),
    )
  }
}