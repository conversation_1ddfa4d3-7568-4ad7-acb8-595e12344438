package com.seer.trick.bz.wms.resv

import com.seer.trick.BzError
import com.seer.trick.ComplexQuery
import com.seer.trick.Cq

import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.helper.JsonHelper
import org.slf4j.LoggerFactory

/**
 * 预定资源
 *
 * 创建预定资源实体，然后 while(true) 等待分配到资源
 *
 * 业务代码调用预定有 2 种模式：
 * - 等待到预定成功
 * - 尝试一次，然后就立即返回成功或者失败
 */
object ReserveService {
  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 预定空库位
   *
   * TODO 这里要传的参数太多了，要简化一下
   */
  fun resvEmptyBin(
    
    districtId: List<String>,
    sourceOrderType: String,
    sourceOrderId: String,
    group: String,
    desc: String,
    noWait: Boolean, // TODO nowait 找不到分配新的状态是否有必要
  ): String {
    val id = ReserveCenter.reserve(
      
      ReserveRecord(
        ResvType.Bin,
        ResvStatus.Waiting,
        0,
        sourceOrderType,
        sourceOrderId,
        group,
        Cq.include("district", districtId),
        FindOptions(),
        "",
        "FindEmptyBin",
        desc,
        noWait,
      ),
    )
    return checkResvRes(id, noWait)
  }

  /**
   * 预定指定条件的库位
   */
  fun resvBin(
    
    query: ComplexQuery,
    sourceOrderType: String,
    sourceOrderId: String,
    group: String,
    desc: String,
    noWait: Boolean,
  ): String {
    val id = ReserveCenter.reserve(
      
      ReserveRecord(
        ResvType.Bin,
        ResvStatus.Waiting,
        0,
        sourceOrderType,
        sourceOrderId,
        group,
        query,
        FindOptions(),
        "",
        "FindEmptyBin",
        desc,
        noWait,
      ),
    )
    return checkResvRes(id, noWait)
  }

  /**
   * 预定空容器
   */
  fun resvEmptyContainer(
    
    districtId: List<String>,
    sourceOrderType: String,
    sourceOrderId: String,
    group: String,
    desc: String,
    noWait: Boolean,
  ): String {
    val id = ReserveCenter.reserve(
      
      ReserveRecord(
        ResvType.Container,
        ResvStatus.Waiting,
        0,
        sourceOrderType,
        sourceOrderId,
        group,
        Cq.include("district", districtId),
        FindOptions(),
        "",
        "FindEmptyBin",
        desc,
        noWait,
      ),
    )
    return checkResvRes(id, noWait)
  }

  /**
   * 预定容器
   */
  fun resvContainer(
    
    query: ComplexQuery,
    sourceOrderType: String,
    sourceOrderId: String,
    group: String,
    desc: String,
    noWait: Boolean,
  ): String {
    val id = ReserveCenter.reserve(
      
      ReserveRecord(
        ResvType.Container,
        ResvStatus.Waiting,
        0,
        sourceOrderType,
        sourceOrderId,
        group,
        query,
        FindOptions(),
        "",
        "FindEmptyBin",
        desc,
        noWait,
      ),
    )
    return checkResvRes(id, noWait)
  }

  private fun checkResvRes(id: String, noWait: Boolean): String {
    while (true) {
      val ev = ResvRepo.findById(id)
      if (ev?.status == ResvStatus.Assigned) {
        return ev.res
      } else if (ev?.status == ResvStatus.Canceled) {
        if (noWait) {
          throw BzError("errResvNotFound", JsonHelper.mapper.writeValueAsString(ev)) // TODO i18n
        } else {
          throw BzError("errResvCanceled", JsonHelper.mapper.writeValueAsString(ev)) // TODO i18n
        }
      }
    }
  }

  /**
   * 批量预定
   *
   * TODO 场景：
   *  - 同时预定库位、容器
   *  - 预定多个库位、容器
   */
  fun resvList() {
  }
}