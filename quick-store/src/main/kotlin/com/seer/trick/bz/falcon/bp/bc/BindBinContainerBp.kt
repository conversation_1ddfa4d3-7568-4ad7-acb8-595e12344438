package com.seer.trick.bz.falcon.bp.bc

import com.seer.trick.bz.wms.BindContainerBinOption
import com.seer.trick.bz.wms.CoreWmsRelationService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.falcon.domain.ParamObjectType

class BindBinContainerBp : AbstractBp() {

  override fun process() {
    val binId = mustGetBlockInputParam("binId") as String
    val containerId = mustGetBlockInputParam("containerId") as String
    val unlockBin = getBlockInputParamAsBool("unlockBin")
    val unlockContainer = getBlockInputParamAsBool("unlockContainer")
    val cbs = getBlockInputParamAsBool("checkBinStatus")
    val ccs = getBlockInputParamAsBool("checkContainerStatus")

    CoreWmsRelationService.bindContainerBin(
      containerId,
      binId,
      BindContainerBinOption(
        unlockContainerAfter = unlockContainer,
        unlockBinAfter = unlockBin,
        checkBinStatus = cbs,
        checkContainerStatus = ccs,
      ),
    )

    addRelatedObject("FbBin", binId, null)
    addRelatedObject("FbContainer", containerId, null)
  }

  companion object {

    val def = BlockDef(
      BindBinContainerBp::class.simpleName!!,
      color = "#ffd22b",
      inputParams = listOf(
        BlockInputParamDef(
          "binId",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.BinId),
        ),
        BlockInputParamDef(
          "containerId",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.ContainerId),
        ),
        BlockInputParamDef("unlockBin", BlockParamType.Boolean),
        BlockInputParamDef("unlockContainer", BlockParamType.Boolean),
        BlockInputParamDef("checkBinStatus", BlockParamType.Boolean),
        BlockInputParamDef("checkContainerStatus", BlockParamType.Boolean),
      ),
    )
  }
}