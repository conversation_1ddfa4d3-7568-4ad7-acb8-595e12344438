package com.seer.trick.bz.falcon.bp.container

import com.seer.trick.base.entity.IdGenRule
import com.seer.trick.base.entity.id.IdGenManager
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*

class CreateTraceContainerBp : AbstractBp() {

  override fun process() {
    val containerType = mustGetBlockInputParam(ipContainerType.name) as String

    val idGenRule = IdGenRule(true, "TC-$containerType-", 6)
    val containerId = IdGenManager.generateId(idGenRule)
    logger.debug("创建追踪容器，id=$containerId，type=$containerType")
    EntityRwService.createOne("FbContainer", mutableMapOf("id" to containerId, "type" to containerType))

    setBlockOutputParams(mapOf(opContainerId.name to containerId))
    addRelatedObject("FbContainer", containerId, null)
  }

  companion object {

    private val ipContainerType = BlockInputParamDef(
      "containerType",
      BlockParamType.String,
      true,
      objectTypes = listOf(ParamObjectType.ContainerType),
    )

    private val opContainerId = BlockOutputParamDef(
      "containerId",
      BlockParamType.String,
      objectTypes = listOf(ParamObjectType.ContainerId),
    )

    val def = BlockDef(
      CreateTraceContainerBp::class.simpleName!!,
      color = "#F8DFD4",
      inputParams = listOf(ipContainerType),
      outputParams = listOf(opContainerId),
    )
  }
}