package com.seer.trick.bz.wms

import com.seer.trick.BzError
import com.seer.trick.ComplexQuery
import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.reslock.ResLockService
import org.slf4j.LoggerFactory
import kotlin.concurrent.withLock

/**
 * 处理仓储业务对象间关系
 * - 容器库位，绑定、解绑
 */
object CoreWmsRelationService {
  
  private val logger = LoggerFactory.getLogger(this::class.java)
  
  /**
   * TODO 检查：库位空、容器未绑定；对锁定的要求
   */
  fun bindContainerBin(
    containerId: String,
    binId: String,
    o: BindContainerBinOption = BindContainerBinOption(),
    reason: String = "",
  ) = ResLockService.resLock.withLock {
    val binEv = EntityRwService.findOne("FbBin", Cq.idEq(binId))
      ?: throw BzError("errNoBinById", binId)
    val containerEv = EntityRwService.findOne("FbContainer", Cq.idEq(containerId))
      ?: throw BzError("errNoSuchContainerById", containerId)
    
    val oldContainer = binEv["container"] as String?
    val oldLocked = binEv["locked"]
    val oldOccupied = binEv["occupied"]
    logger.info(
      "绑定容器库位。库位='$binId'，当前容器='$oldContainer'，当前锁定状态=$oldLocked，当前占用状态=$oldOccupied" +
        "新容器='$containerId'，同时解锁库位=${o.unlockBinAfter}。原因=$reason",
    )
    
    if (o.checkBinStatus) {
      if (!oldContainer.isNullOrBlank() && oldContainer != containerId) {
        throw BzError(
          "errBinBound",
          binId,
          oldContainer,
        )
      }
    }
    if (o.checkContainerStatus) {
      val oldBin = containerEv["bin"] as String?
      if (!oldBin.isNullOrBlank() && oldBin != binId) throw BzError("errContainerBound", containerId, oldBin)
    }
    
    val u: EntityValue = mutableMapOf("container" to containerId, "occupied" to true)
    if (o.unlockBinAfter) u["locked"] = false
    
    EntityRwService.updateOne("FbBin", Cq.eq("id", binId), u)
    
    val uc = mutableMapOf("bin" to binId, "district" to binEv["district"], "warehouse" to binEv["warehouse"])
    if (o.unlockContainerAfter) {
      val oldLockedContainer = containerEv["locked"]
      val oldTaskType = containerEv["taskType"]
      logger.info(
        "绑定容器库位，解锁容器并清除任务类型。容器='$containerId'。" +
          "容器原锁定状态=$oldLockedContainer，原任务类型=$oldTaskType",
      )
      uc["locked"] = false
      uc["taskType"] = ""
    }
    EntityRwService.updateOne("FbContainer", Cq.eq("id", containerId), uc)
  }
  
  /**
   * 会检查传入的容器确实是库位上当前容器
   */
  fun unbindContainerBin(
    containerId: String,
    binId: String,
    o: UnbindContainerBinOption = UnbindContainerBinOption(),
    reason: String = "",
  ) = ResLockService.resLock.withLock {
    val binEv = EntityRwService.findOne("FbBin", Cq.idEq(binId))
      ?: throw BzError("errNoBinById", binId)
    val containerEv = EntityRwService.findOne("FbContainer", Cq.idEq(containerId))
      ?: throw BzError("errNoSuchContainerById", containerId)
    
    val oldContainer = binEv["container"]
    val oldLocked = binEv["locked"]
    val oldOccupied = binEv["occupied"]
    logger.info(
      "解绑容器库位。库位='$binId'，当前容器='$oldContainer'，当前锁定状态=$oldLocked，当前占用状态=$oldOccupied" +
        "期望解绑容器='$containerId'。同时解锁库位=${o.unlockBinAfter}。原因=$reason",
    )
    
    if (oldContainer != containerId) throw BzError("errUnbindBinContainerBadBind", binId, containerId, oldContainer)
    
    val u: EntityValue = mutableMapOf("container" to "", "occupied" to false)
    if (o.unlockBinAfter) u["locked"] = false
    
    EntityRwService.updateOne("FbBin", Cq.eq("id", binId), u)
    
    val uc: EntityValue = mutableMapOf("bin" to "", "district" to "", "warehouse" to "")
    if (o.unlockContainerAfter) {
      val oldLockedContainer = containerEv["locked"]
      val oldTaskType = containerEv["taskType"]
      logger.info(
        "解绑容器库位，解锁容器并清除任务类型。容器='$containerId'。" +
          "容器原锁定状态=$oldLockedContainer，原任务类型=$oldTaskType",
      )
      uc["locked"] = false
      uc["taskType"] = ""
    }
    EntityRwService.updateOne("FbContainer", Cq.eq("id", containerId), uc)
  }
  
  fun setBinNotOccupied(
    binId: String,
    o: SetBinNotOccupiedOption = SetBinNotOccupiedOption(),
    reason: String? = null,
  ) = ResLockService.resLock.withLock {
    val binEv = EntityRwService.findOne("FbBin", Cq.idEq(binId))
      ?: throw BzError("errNoBinById", binId)
    val oldContainer = binEv["container"]
    val oldLocked = binEv["locked"]
    val oldOccupied = binEv["occupied"]
    
    logger.info(
      "库位置为空，库位=$binId，当前容器='$oldContainer'，当前锁定状态=$oldLocked，当前占用状态=$oldOccupied。" +
        "原因=$reason",
    )
    
    val u: EntityValue = mutableMapOf("container" to "", "occupied" to false)
    if (o.unlockAfter) {
      u["locked"] = false
      u["lockedBy"] = null
    }
    EntityRwService.updateOne("FbBin", Cq.eq("id", binId), u)
  }
  
  /**
   * 只设占用，不设容器
   */
  fun setBinOccupied(
    binId: String,
    o: SetBinOccupiedOption = SetBinOccupiedOption(),
    reason: String? = null,
  ) = ResLockService.resLock.withLock {
    val binEv = EntityRwService.findOne("FbBin", Cq.idEq(binId))
      ?: throw BzError("errNoBinById", binId)
    val oldContainer = binEv["container"]
    val oldLocked = binEv["locked"]
    val oldOccupied = binEv["occupied"]
    
    logger.info(
      "库位置为占用，库位=$binId，当前容器='$oldContainer'，当前锁定状态=$oldLocked，当前占用状态=$oldOccupied。" +
        "原因=$reason",
    )
    
    val u: EntityValue = mutableMapOf("occupied" to true)
    if (o.unlockAfter) u["locked"] = false
    EntityRwService.updateOne("FbBin", Cq.eq("id", binId), u)
  }
  
  /**
   * 反复尝试锁定库位直到成功
   */
  fun keepTryingLockBin(binId: String, reason: String? = null) {
    while (true) {
      ResLockService.resLock.withLock {
        val binEv = EntityRwService.findOne("FbBin", Cq.idEq(binId))
          ?: throw BzError("errNoBinById", binId)
        val oldLocked = binEv["locked"]
        
        if (oldLocked != true) {
          if (1L == EntityRwService.updateOne(
              "FbBin",
              Cq.and(listOf(Cq.eq("id", binId), Cq.ne("locked", true))),
              mutableMapOf("locked" to true, "lockedBy" to reason),
            )
          ) {
            logger.info("锁定库位，库位=$binId，原因=$reason")
            return
          }
        }
      }
      Thread.sleep(500)
    }
  }
  
  /**
   * 反复尝试锁定库位直到成功
   */
  fun lockBinIfNotLocked(binId: String, reason: String? = null): Boolean {
    ResLockService.resLock.withLock {
      val binEv = EntityRwService.findOne("FbBin", Cq.idEq(binId))
        ?: throw BzError("errNoBinById", binId)
      val oldLocked = binEv["locked"]
      if (oldLocked == true) return false
      if (1L == EntityRwService.updateOne(
          "FbBin",
          Cq.and(listOf(Cq.eq("id", binId), Cq.ne("locked", true))),
          mutableMapOf("locked" to true),
        )
      ) {
        logger.info("锁定库位，库位=$binId，原因=$reason")
        return true
      } else {
        return false
      }
    }
  }
  
  /**
   * 等待库位为空并锁定
   */
  fun keepTryingLockNotOccupiedBin(binId: String, reason: String? = null) {
    while (true) {
      ResLockService.resLock.withLock {
        val binEv = EntityRwService.findOne("FbBin", Cq.idEq(binId))
          ?: throw BzError("errNoBinById", binId)
        val oldLocked = binEv["locked"]
        val oldOccupied = binEv["occupied"]
        
        if (oldLocked != true && oldOccupied != true) {
          if (1L == EntityRwService.updateOne(
              "FbBin",
              Cq.and(listOf(Cq.eq("id", binId), Cq.ne("locked", true), Cq.ne("occupied", true))),
              mutableMapOf("locked" to true),
            )
          ) {
            logger.info("等待库位为空并锁定，库位=$binId，原因=$reason")
            return
          }
        }
      }
      Thread.sleep(500)
    }
  }
  
  /**
   * 在指定库区内寻找一个空库位
   */
  fun findOneNotOccupiedUnlockedBinInDistricts(
    districtIds: List<String>,
    o: FindOneNotOccupiedUnlockedBinInDistrictsOptions,
  ): EntityValue? {
    while (true) {
      if (districtIds.isEmpty()) throw BzError("errDistrictIdsEmpty")
      for (districtId in districtIds) {
        val binEv = findOneNotOccupiedUnlockedBinInDistrict(districtId, o.query, o.sort)
        if (binEv != null) return binEv
      }
      
      if (!o.keepTrying) return null
      Thread.sleep(500)
    }
  }
  
  private fun findOneNotOccupiedUnlockedBinInDistrict(
    districtId: String,
    query: ComplexQuery?,
    sort: List<String>?,
  ): EntityValue? = ResLockService.resLock.withLock {
    if (EntityRwService.findOne("FbDistrict", Cq.idEq(districtId)) == null) {
      throw BzError("errNoSuchDistrictById", districtId)
    }
    
    val qItems = mutableListOf(
      Cq.eq("district", districtId),
      Cq.ne("locked", true),
      Cq.ne("occupied", true),
      Cq.ne("btDisabled", true),
    )
    
    if (query != null) qItems += query
    val binEv = EntityRwService.findOne("FbBin", Cq.and(qItems), FindOptions(sort = sort))
      ?: return null
    val binId = EntityHelper.mustGetId(binEv)
    return if (1L == EntityRwService.updateOne(
        "FbBin",
        Cq.and(listOf(Cq.idEq(binId), Cq.ne("locked", true))),
        mutableMapOf("locked" to true),
      )
    ) {
      binEv
    } else {
      null
    }
  }
  
  /**
   * 解锁指定库位
   */
  fun unlockBin(binId: String, requiredLock: Boolean) {
    val binEv = EntityRwService.findOne("FbBin", Cq.idEq(binId))
      ?: throw BzError("errNoBinById", binId)
    if (requiredLock && binEv["locked"] == false) throw BzError("errNoLockById", binId)
    
    EntityRwService.updateOne(
      "FbBin",
      Cq.and(listOf(Cq.eq("id", binId))),
      mutableMapOf("locked" to false),
    )
  }
}

data class BindContainerBinOption(
  val unlockContainerAfter: Boolean = false, // 同时解锁容器
  val unlockBinAfter: Boolean = false, // 同时解锁库位
  val checkBinStatus: Boolean = false, // 检查库位状态
  val checkContainerStatus: Boolean = false, // 检查容器状态
)

data class UnbindContainerBinOption(
  val unlockContainerAfter: Boolean = false, // 同时解锁容器
  val unlockBinAfter: Boolean = false, // 同时解锁库位
)

data class SetBinNotOccupiedOption(val unlockAfter: Boolean = false)

data class SetBinOccupiedOption(val unlockAfter: Boolean = false)

data class FindOneNotOccupiedUnlockedBinInDistrictsOptions(
  val query: ComplexQuery? = null,
  val sort: List<String>? = null,
  val keepTrying: Boolean = false,
)