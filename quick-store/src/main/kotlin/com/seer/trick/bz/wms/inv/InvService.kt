package com.seer.trick.bz.wms.inv

import com.seer.trick.BzError
import com.seer.trick.ComplexQuery
import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.bz.wms.*
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.NumHelper
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.util.*
import kotlin.math.min

/**
 * 库存管理
 */
object InvService {

  private val logger: Logger = LoggerFactory.getLogger(this::class.java)

  /**
   * 从单据创建库存；批量检查单据；检查 FIELD_INV_PROCESSED 字段，如果未处理，则处理
   */
  @Deprecated("此方法不要用，待维护")
  fun orderToCreateInv(em: EntityMeta) {
    val oc = em.orderConfig
    if (oc?.enabled != true) return
    val orderToCreateInvStates = oc.orderStatesToCreateInv // 单据计入库存时的状态
    if (orderToCreateInvStates.isEmpty()) return

    val q = Cq.and(
      listOf(
        Cq.include(FieldMeta.FIELD_ORDER_STATE, orderToCreateInvStates),
        Cq.ne(FieldMeta.FIELD_INV_PROCESSED, true)
      )
    )
    val sourceOrders = EntityRwService.findMany(em.name, q)
    if (sourceOrders.isEmpty()) return

    val sourceOrderIds = sourceOrders.map(EntityHelper::mustGetId)
    logger.info("Order to create inv, name=${em.name}, ids=$sourceOrderIds")

    // val invState = oc.toCreateInvState!! // 创建库存的状态
    sourceOrders.map { order ->
      try {
        // doOrderToCreateInv(em, order, invState)
        EntityRwService.updateOne(
          em.name, Cq.idEq(EntityHelper.mustGetId(order)), mutableMapOf(FieldMeta.FIELD_INV_PROCESSED to true)
        )
      } catch (e: Exception) {
        logger.error("Failed to create inv", e)
      }
    }
  }

  // 检查、分配库存
  // 单头里可能有出库限制，比如指定仓库
  fun assignInvForOutbound(ev: EntityValue, em: EntityMeta) {
    // TODO 一个标记字段防止重复处理
    // TODO 出库单状态正确，但是配置错误，导致这个出库单漏处理
    val oc = em.orderConfig
    if (ev[FieldMeta.FIELD_ORDER_STATE] != oc?.outboundInvAssignState) return // 是否要分配库存

    val orderId = EntityHelper.mustGetId(ev)
    logger.info("Assign inv for outbound, order id=${orderId}")

    val outboundInvStates = oc?.outboundInvStates // 允许出库的库存状态

    val lines = EntityHelper.mustGetLines(ev, FieldMeta.FIELD_LINES)

    val requests = lines.map { line ->
      val warehouse = ev["warehouse"] as String?
      val district = ev["district"] as String?
      AssignInvReq(
        material = line[FieldMeta.FIELD_MATERIAL_ID] as String,
        qty = line["qty"] as Double,
        outboundOrderId = orderId,
        outboundOrderLineNo = line[FieldMeta.FIELD_LINE_NO] as Int,
        filter = AssignInvFilter(
          state = outboundInvStates,
          warehouse = if (!warehouse.isNullOrEmpty()) listOf(warehouse) else null,
          district = if (!district.isNullOrEmpty()) listOf(district) else null
        ),
        sort = emptyList()
      )
    }

    val assignments = tryToAssignInv(requests)

    // TODO i18n
    val shortLineNos = assignments.filter { it.short }.map { a ->
      "出库单 ${a.request.outboundOrderId}，第 ${a.request.outboundOrderLineNo} 行，物料 ${a.request.material}，" + "计划出库 ${a.request.qty}，缺 ${a.shortQty}"
    }
    if (shortLineNos.isNotEmpty()) {
      val shortLines = shortLineNos.joinToString(", ")
      logger.info("库存不够：$shortLines")
      throw BzError("errInvShort", shortLines)
    } else {
      logger.info("库存够了")
    }

    doAssign(assignments)
  }

  // 暂不考虑多个请求匹配到一条库存明细
  // 暂不考虑一些并发问题
  private fun tryToAssignInv(requests: List<AssignInvReq>): List<InvAssignment> {
    logger.info("Try to assign inv: $requests")

    val assignments: MutableList<InvAssignment> = ArrayList()
    val usedMap: MutableMap<String, InvLayoutUsed> = HashMap()

    for (request in requests) {
      val assignment = InvAssignment(request, short = true, shortQty = 0.0, items = ArrayList())
      assignments.add(assignment)

      val query = requestToQuery(request.material, request.filter)
      // TODO 全查的性能
      val invLayouts = EntityRwService.findMany("FbInvLayout", query)

      var qty = request.qty
      for (invLayout in invLayouts) {
        val invLayoutId = EntityHelper.mustGetId(invLayout)
        val used = usedMap[invLayoutId]
        if (used != null && used.unused > 0) {
          val assignedQty = min(used.unused, qty)
          qty -= assignedQty
          used.unused -= assignedQty
          assignment.items.add(InvAssignmentItem(invLayout, assignedQty))
        } else {
          val invQty = invLayout["qty"] as Double
          val assignedQty = min(invQty, qty)
          qty -= assignedQty
          val ilUsed = InvLayoutUsed(invLayout, invQty - assignedQty)
          usedMap[invLayoutId] = ilUsed
          assignment.items.add(InvAssignmentItem(invLayout, assignedQty))
        }

        if (qty <= 0) break
      }

      assignment.short = qty > 0
      assignment.shortQty = qty
    }

    return assignments
  }

  // 将分配库存的请求转换为查询库存明细的条件
  fun requestToQuery(materialId: String, filter: AssignInvFilter): ComplexQuery {
    val items = mutableListOf(
      Cq.eq(FieldMeta.FIELD_MATERIAL_ID, materialId),
      Cq.ne("assigned", true), // 表示已分配库存
    )

    if (!filter.core.matLotNo.isNullOrEmpty()) items += Cq.eq("matLotNo", filter.core.matLotNo)
    if (!filter.core.matSerialNo.isNullOrEmpty()) items += Cq.eq("matSerialNo", filter.core.matSerialNo)

    if (!filter.state.isNullOrEmpty()) items += Cq.include("state", filter.state)

    if (!filter.warehouse.isNullOrEmpty()) items += Cq.include("warehouse", filter.warehouse)
    if (!filter.district.isNullOrEmpty()) items += Cq.include("district", filter.district)
    if (!filter.bin.isNullOrEmpty()) items += Cq.include("bin", filter.bin)

    if (!filter.vendor.isNullOrEmpty()) items += Cq.include("vendor", filter.vendor)
    if (!filter.owner.isNullOrEmpty()) items += Cq.include("owner", filter.owner)

    // TODO 锁定等其他状态

    return Cq.and(items)
  }

  // 检查库存通过后，实际分配库存
  private fun doAssign(assignments: List<InvAssignment>) {
    logger.info("DO assign inv for outbound")

    for (assignment in assignments) {
      for (item in assignment.items) {
        val invLayoutId = EntityHelper.mustGetId(item.invLayout)
        val invQty = item.invLayout["qty"] as Double // 库存明细记录的总数量
        val newQty = if (NumHelper.isNumberEqual(invQty, item.qty)) 0 else invQty - item.qty
        // 全用了，暂不直接删除
        logger.info("Reduce inv, new qty=${newQty}, inv id=$invLayoutId")
        EntityRwService.updateOne(
          "FbInvLayout", Cq.idEq(invLayoutId), mutableMapOf("qty" to newQty)
        )
        // 暂存已分配的
        val al: EntityValue = mutableMapOf()
        al.putAll(item.invLayout)
        al["qty"] = item.qty
        al["state"] = "Assigned"
        al["assigned"] = true
        al["id"] = IdHelper.oidStr()
        al["refInv"] = invLayoutId
        al["outboundOrderId"] = assignment.request.outboundOrderId
        al["outboundOrderLineNo"] = assignment.request.outboundOrderLineNo
        EntityRwService.createOne("FbInvLayout", al)
      }
    }
  }

}


