package com.seer.trick.bz.falcon.bp.bin

import com.seer.trick.bz.wms.CoreWmsRelationService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.falcon.domain.ParamObjectType

/**
 * 反复尝试锁定库位直到成功
 */
class KeepTryingLockBinBp : AbstractBp() {

  override fun process() {
    val binId = mustGetBlockInputParam("binId") as String
    val reason = getBlockInputParam("reason") as String?

    CoreWmsRelationService.keepTryingLockBin(binId, reason)
    addRelatedObject("FbBin", binId, null)
  }

  companion object {

    val def = BlockDef(
      KeepTryingLockBinBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(
        BlockInputParamDef("binId", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.BinId)),
        BlockInputParamDef("reason", BlockParamType.String, false),
      ),
    )
  }
}