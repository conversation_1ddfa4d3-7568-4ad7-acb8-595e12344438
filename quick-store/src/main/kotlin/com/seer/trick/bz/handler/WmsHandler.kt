package com.seer.trick.bz.handler

import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.getReqBody
import com.seer.trick.base.http.handler.OrderIdsReq
import com.seer.trick.bz.wms.CallContainerByOrderReq
import com.seer.trick.bz.wms.CallContainerService
import io.javalin.http.Context

object WmsHandler {
  
  fun registerHandlers() {
    val c = Handlers("api/wms")
    
    c.post("call-container-by-order", ::callContainerByOrder, auth())
    c.post("call-container-direct", ::callDirectContainers, auth())
    
    c.post("reset-inbound-order-call-container-qty", ::resetInboundOrderCallContainerQty, auth())
    
    c.post("reset-outbound-order-inv-assigned-qty", ::resetOutboundOrderInvAssignedQty, auth())
    
    c.post("direct-outbound", ::directOutbound, auth())
  }
  
  private fun callContainerByOrder(ctx: Context) {
    
    val req: CallContainerByOrderReq = ctx.getReqBody()
    CallContainerService.callContainerByOrder(req)
    ctx.status(200)
  }
  
  private fun callDirectContainers(ctx: Context) {
    
    val req: CallContainerDirectReq = ctx.getReqBody()
    CallContainerService.callDirectContainers(req.containerIds)
    ctx.status(200)
  }
  
  private fun resetInboundOrderCallContainerQty(ctx: Context) {
    
    val req: OrderIdsReq = ctx.getReqBody()
    val counted = if (req.orderIds.isNotEmpty()) {
      EntityRwService.updateOne(
        "FbInboundOrder", Cq.include("id", req.orderIds), mutableMapOf("callContainerAll" to false)
      )
      EntityRwService.updateMany(
        "FbInboundOrderLine", Cq.include(FieldMeta.FIELD_PARENT_ID, req.orderIds),
        mutableMapOf("callContainerQty" to 0.0)
      )
    } else {
      0
    }
    ctx.json(mapOf("updatedLinesCount" to counted))
  }
  
  private fun resetOutboundOrderInvAssignedQty(ctx: Context) {
    val req: OrderIdsReq = ctx.getReqBody()
    val counted = if (req.orderIds.isNotEmpty()) {
      EntityRwService.updateMany(
        "FbOutboundOrderLine", Cq.include(FieldMeta.FIELD_PARENT_ID, req.orderIds),
        mutableMapOf("invAssignedQty" to 0.0)
      )
    } else {
      0
    }
    ctx.json(mapOf("updatedCount" to counted))
  }
  
  private fun directOutbound(ctx: Context) {
    
    val req: DirectOutboundReq = ctx.getReqBody()
    
    val orderName = BzConfigManager.getByPath("ScWms", "outbound", "directOrder") as String?
    if (orderName.isNullOrBlank()) throw BzError("errDirectorOutboundNoOrder")
    
    BaseCenter.mustGetEntityMeta(orderName)
    
    var layoutEvList: List<EntityValue>
    val binIds: List<String>
    if (req.type == DirectOutboundType.Container) {
      if (req.containerIds.isNullOrEmpty()) throw BzError("errMissingParam", "containerIds")
      
      layoutEvList = EntityRwService.findMany(
        "FbInvLayout",
        Cq.or(
          listOf(
            Cq.include(FieldMeta.FIELD_LEAF_CONTAINER, req.containerIds),
            Cq.include(FieldMeta.FIELD_TOP_CONTAINER, req.containerIds)
          )
        )
      )
      if (layoutEvList.isEmpty()) throw BzError("errDirectorOutboundEmptyLayouts")
      
      val binSet = mutableSetOf<String>()
      for (lyEv in layoutEvList) {
        val bin = lyEv["bin"] as String?
        if (!bin.isNullOrBlank()) binSet += bin
      }
      binIds = binSet.toList()
    } else if (req.type == DirectOutboundType.Bin) {
      if (req.binIds.isNullOrEmpty()) throw BzError("errMissingParam", "binIds")
      
      val binEvList = EntityRwService.findMany("FbBin", Cq.include("id", req.binIds))
      for (binEv in binEvList) {
        if (binEv["occupied"] != true) throw BzError("errDirectorOutboundBinNotOccupied", binEv["id"])
      }
      
      binIds = req.binIds
      layoutEvList = EntityRwService.findMany("FbInvLayout", Cq.include("bin", binIds))
      if (layoutEvList.isEmpty()) throw BzError("errDirectorOutboundEmptyLayouts")
    } else {
      if (req.layoutIds.isNullOrEmpty()) throw BzError("errMissingParam", "layoutIds")
      
      layoutEvList = EntityRwService.findMany("FbInvLayout", Cq.include("id", req.layoutIds))
      val binSet = mutableSetOf<String>()
      for (layoutEv in layoutEvList) {
        val bin = layoutEv["bin"] as String?
        if (!bin.isNullOrBlank()) binSet.add(bin)
      }
      binIds = binSet.toList()
      if (req.wholeBin) {
        layoutEvList = EntityRwService.findMany("FbInvLayout", Cq.include("bin", binIds))
      }
    }
    
    if (layoutEvList.isEmpty()) throw BzError("errDirectorOutboundEmptyLayouts")
    
    val lines = layoutEvList.map { lyEv ->
      val line: EntityValue = mutableMapOf()
      line.putAll(lyEv)
      line["layoutId"] = line["id"]
      line.remove("id")
      line
    }
    
    val order: EntityValue = mutableMapOf(
      FieldMeta.FIELD_ORDER_STATE to "Init",
      "direct" to true,
      "bins" to binIds,
      FieldMeta.FIELD_LINES to lines
    )
    val id = EntityRwService.createOne(orderName, order)
    ctx.json(mapOf("orderId" to id, "orderName" to orderName))
  }
  
}

data class DirectOutboundReq(
  val type: DirectOutboundType,
  val layoutIds: List<String>? = null,
  val binIds: List<String>? = null,
  val containerIds: List<String>? = null,
  val wholeBin: Boolean = false
)

enum class DirectOutboundType {
  InvLayout, Bin, Container
}

data class CallContainerDirectReq(
  val containerIds: List<String>
)