package com.seer.trick.bz.falcon.bp.bin

import com.seer.trick.bz.wms.CoreWmsRelationService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.falcon.domain.ParamObjectType

/**
 * 等待库位为空并锁定
 */
class KeepTryingLockNotOccupiedBinBp : AbstractBp() {

  override fun process() {
    val binId = mustGetBlockInputParam("binId") as String

    CoreWmsRelationService.keepTryingLockNotOccupiedBin(binId, "")
    addRelatedObject("FbBin", binId, null)
  }

  companion object {

    val def = BlockDef(
      KeepTryingLockNotOccupiedBinBp::class.simpleName!!,
      color = "#A5B6DD",
      inputParams = listOf(
        BlockInputParamDef("binId", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.BinId)),
      ),
    )
  }
}