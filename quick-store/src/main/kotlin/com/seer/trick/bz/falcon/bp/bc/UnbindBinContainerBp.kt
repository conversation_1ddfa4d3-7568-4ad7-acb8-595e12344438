package com.seer.trick.bz.falcon.bp.bc


import com.seer.trick.bz.wms.CoreWmsRelationService
import com.seer.trick.bz.wms.UnbindContainerBinOption
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.falcon.domain.ParamObjectType

class UnbindBinContainerBp : AbstractBp() {

  override fun process() {
    val binId = mustGetBlockInputParam("binId") as String
    val containerId = mustGetBlockInputParam("containerId") as String
    val unlockBin = getBlockInputParamAsBool("unlockBin")
    val unlockContainer = getBlockInputParamAsBool("unlockContainer")

    CoreWmsRelationService.unbindContainerBin(
      
      containerId,
      binId,
      UnbindContainerBinOption(unlockContainerAfter = unlockContainer, unlockBinAfter = unlockBin),
    )

    addRelatedObject("FbBin", binId, null)
    addRelatedObject("FbContainer", containerId, null)
  }

  companion object {

    val def = BlockDef(
      UnbindBinContainerBp::class.simpleName!!,
      color = "#9EC8B9",
      inputParams = listOf(
        BlockInputParamDef(
          "binId",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.BinId),
        ),
        BlockInputParamDef(
          "containerId",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.ContainerId),
        ),
        BlockInputParamDef("unlockBin", BlockParamType.Boolean),
        BlockInputParamDef("unlockContainer", BlockParamType.Boolean),
      ),
    )
  }
}