package com.seer.trick.bz.falcon.bp.bin


import com.seer.trick.bz.wms.CoreWmsRelationService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*

/**
 * 解锁库位
 */
class SetBinUnLockBp : AbstractBp() {

  override fun process() {
    val binId = mustGetBlockInputParam("binId") as String
    val requireLock = getBlockInputParamAsBool("requireLock") as Boolean? ?: false

    CoreWmsRelationService.unlockBin(binId, requireLock)
    addRelatedObject("FbBin", binId, null)
  }

  companion object {
    val def = BlockDef(
      SetBinUnLockBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(
        BlockInputParamDef("binId", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.BinId)),
        BlockInputParamDef("requireLock", BlockParamType.Boolean, false),
      ),
    )
  }
}