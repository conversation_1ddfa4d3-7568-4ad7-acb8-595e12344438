package com.seer.trick.bz.wms.inv

import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.bz.wms.MaterialManager
import org.slf4j.LoggerFactory
import java.util.*

/**
 * 创建库存
 */
object CreateInvService {

  private val logger = LoggerFactory.getLogger(this::class.java)

  /**
   * 修复并创建库存明细。通过 CreateInvLine 对象保证关键字段填对。
   */
  fun fixCreateInvLayoutByLines(lines: List<CreateInvLine>): List<String> {
    val layouts = lines.map { it.toEv() }
    return fixCreateInvLayout(layouts)
  }

  /**
   * 修复并创建库存明细。填充物料信息、位置信息、容器等。
   */
  fun fixCreateInvLayout(layouts: List<EntityValue>): List<String> {
    val now = Date()
    val containers: MutableList<String> = ArrayList()
    for (layout in layouts) {
      // 填充物料信息
      val materialId = layout[FieldMeta.FIELD_MATERIAL] as String?
      if (!materialId.isNullOrBlank()) MaterialManager.fillMaterialFieldsIntoLine(materialId, layout)

      // 填充位置信息
      val binId = layout["bin"] as String?
      if (!binId.isNullOrBlank()) {
        val binEv = EntityRwService.findOneById("FbBin", binId)
        if (binEv != null) {
          layout["district"] = binEv["district"]
          layout["warehouse"] = binEv["warehouse"]
          layout["row"] = binEv["row"]
          layout["column"] = binEv["column"]
          layout["layer"] = binEv["layer"]
          layout["depth"] = binEv["depth"]
          layout["rack"] = binEv["rack"]
          layout["channel"] = binEv["channel"]
          layout["workSite"] = binEv["workSite"]
          layout["assemblyLine"] = binEv["assemblyLine"]
        }
      }

      // 如果只有一层容器，最外取最内
      val leafContainer = layout[FieldMeta.FIELD_LEAF_CONTAINER] as String?
      val topContainer = layout[FieldMeta.FIELD_TOP_CONTAINER] as String?
      fixContainerType(leafContainer, topContainer, layout)
      if (!leafContainer.isNullOrBlank() && topContainer.isNullOrBlank()) {
        layout[FieldMeta.FIELD_TOP_CONTAINER] = leafContainer
        containers.add(leafContainer)
      }
      if (!topContainer.isNullOrBlank() && topContainer != leafContainer) containers.add(topContainer)

      // 入库时间
      if (!layout.containsKey("inboundOn")) {
        layout["inboundOn"] = now
      }
    }
    logger.info("创建库存明细：$layouts")
    val ids = EntityRwService.createMany("FbInvLayout", layouts)
    // 设置容器有货
    EntityRwService.updateMany("FbContainer", Cq.include("id", containers), mutableMapOf("filled" to true))
    return ids
  }

  // 添加容器类型
  private fun fixContainerType(leafContainer: String?, topContainer: String?, ev: EntityValue) {
    val list = listOfNotNull(leafContainer, topContainer)
    if (list.isEmpty()) return
    val m = EntityRwService.findMany("FbContainer", Cq.include("id", list))
      .associateBy { it["id"] as String }
    if (!leafContainer.isNullOrEmpty()) {
      ev[FieldMeta.FIELD_LEAF_CONTAINER_TYPE] = m[leafContainer]?.get("type")
    }
    if (!topContainer.isNullOrEmpty()) {
      ev[FieldMeta.FIELD_TOP_CONTAINER_TYPE] = m[topContainer]?.get("type")
    }
  }
}

/**
 * 用于创建库存，表示一条库存明细。通过这个类，明确必须提供的字段
 */
data class CreateInvLine(
  val qty: Double,
  val material: String?, // 物料编号，一般不为空
  val state: String, // 生成的库存明细的状态
  val bin: String? = null, // 存储库位
  val leafContainer: String? = null, // 最内层容器
  val subContainerId: Int? = null, // 格子
  val moreFields: Map<String, Any?>? = null, // 直接映射字段
) {

  fun toEv(): EntityValue {
    val ev: EntityValue = mutableMapOf(
      "qty" to qty,
      FieldMeta.FIELD_MATERIAL to material,
      "state" to state,
      "bin" to bin,
      FieldMeta.FIELD_LEAF_CONTAINER to leafContainer,
      FieldMeta.FIELD_SUB_CONTAINER_ID to subContainerId,
    )
    if (!moreFields.isNullOrEmpty()) ev.putAll(moreFields)
    return ev
  }
}