package com.seer.trick.bz.order

import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.change.EntityChange
import com.seer.trick.base.entity.service.extension.EntityServiceExtension

object OrderService : EntityServiceExtension() {
  
  // fun beforeCreating(
  //   em: EntityMeta, evList: List<EntityValue>
  // ) {
  //   if (em.orderConfig.toAssignInv) {
  //     // 检查、分配库存
  //     // for (ev in evList) InvService.assignInvForOutbound(ev, em)
  //   }
  // }
  
  override fun afterCreating(em: EntityMeta, evList: List<EntityValue>) {
    val ids = evList.map(EntityHelper::mustGetId)
    OrderWriteBackService.tryWriteBack(em, ids)
  }
  
  // fun beforeUpdating(
  //   em: EntityMeta, ids: List<String>, update: EntityValue
  // ) {
  //   if (em.orderConfig.toAssignInv) {
  //     // 检查、分配库存
  //     // 对于更新，先把全量实体值组装出来
  //     val orders = EntityRwService.findMany(em.name, Cq.include("id", ids))
  //     for (order in orders) {
  //       order.putAll(update)
  //       // InvService.assignInvForOutbound(order, em)
  //     }
  //   }
  // }
  
  override fun afterUpdating(em: EntityMeta, changes: List<EntityChange>) {
    val ids = changes.map { it.id }
    OrderWriteBackService.tryWriteBack(em, ids)
  }
  
  // private fun toCreateInv(entityMeta: EntityMeta) {
  //   // if (entityMeta.orderConfig.toCreateInv) {
  //   //   InvService.orderToCreateInv(entityMeta)
  //   // }
  // }
  
  override fun afterRemoving(em: EntityMeta, oldValues: List<EntityValue>) {
    // TODO 小心，删除会导致重新计算回写
    val ids = oldValues.map(EntityHelper::mustGetId)
    OrderWriteBackService.tryWriteBack(em, ids)
  }
  
}