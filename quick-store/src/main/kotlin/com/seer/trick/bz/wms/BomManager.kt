package com.seer.trick.bz.wms

import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.helper.NumHelper

object BomManager {
  
  // 生产工单物料需求展开到备料单
  fun mfgOrderToMaterialPrepareOrder(
    mfgOrder: EntityValue, prepareOrder: EntityValue,
  ) {
    val mfgOrderLines = EntityHelper.getLines(mfgOrder, FieldMeta.FIELD_LINES)!!
    val requirementMap: MutableMap<String, Double> = HashMap() // material to qty
    
    for (mfgOrderLine in mfgOrderLines) {
      val matId = mfgOrderLine[FieldMeta.FIELD_MATERIAL_ID] as String
      
      val mfgQty = NumHelper.anyToDouble(mfgOrderLine["qty"])
      if (mfgQty == null || mfgQty == 0.0) continue
      
      val bom = EntityRwService.findOne(
        "FbProductSimpleBom",
        Cq.and(listOf(Cq.eq("finalMaterial", matId), Cq.eq("enabled", true)))
      ) ?: throw BzError("errBzNoActiveBomForMat", matId)
      
      val bomLines = EntityHelper.getLines(bom, FieldMeta.FIELD_LINES)!!
      for (bl in bomLines) {
        val lineMatId = bl[FieldMeta.FIELD_MATERIAL_ID] as String
        requirementMap[lineMatId] =
          (requirementMap[lineMatId] ?: 0.0) + (NumHelper.anyToDouble(bl["qty"]) ?: 0.0) * mfgQty
      }
    }
    
    val prepareOrderLines = requirementMap.keys.mapIndexed { i, matId ->
      val line: EntityValue = mutableMapOf(
        FieldMeta.FIELD_PARENT_ID to prepareOrder["id"],
        FieldMeta.FIELD_LINE_NO to i + 1,
        "qty" to requirementMap[matId],
        FieldMeta.FIELD_MATERIAL to matId,
        FieldMeta.FIELD_MATERIAL_ID to matId,
      )
      MaterialManager.fillMaterialFieldsIntoLine(matId, line)
      line
    }
    
    prepareOrder[FieldMeta.FIELD_LINES] = prepareOrderLines
  }
}
