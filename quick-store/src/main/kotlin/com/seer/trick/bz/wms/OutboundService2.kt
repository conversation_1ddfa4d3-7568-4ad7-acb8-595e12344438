package com.seer.trick.bz.wms

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.ComplexQuery
import com.seer.trick.Cq
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.entity.service.extension.EntityServiceExtension
import com.seer.trick.base.reslock.ResLockService
import com.seer.trick.base.script.ScriptCenter
import com.seer.trick.base.script.ScriptExeRequest
import com.seer.trick.base.script.ScriptTraceContext
import com.seer.trick.base.soc.SocService
import com.seer.trick.falcon.task.FalconTaskDefService
import com.seer.trick.helper.NumHelper
import com.seer.trick.helper.StringHelper
import com.seer.trick.helper.submitLongRun
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import java.util.concurrent.Executors
import kotlin.concurrent.withLock
import kotlin.math.min

// 当出库单状态为 Committed 时找库存。出库单行，数量字段 qty。
// invAssignedAll 是此出库单已全部找到库存的标志
// 生成的分拣单初始状态是 "Todo"。单行计划拣出数量字段 planQty，实际数量字段 qty
// 对于匹配字段（如批次），如果出库单行的这个字段值为字符串空，则不参与匹配。
object OutboundService2 : EntityServiceExtension() {
  
  private val logger = LoggerFactory.getLogger(this::class.java)
  
  fun init() {
    Executors.newSingleThreadExecutor().submitLongRun("出库定时处理", logger, {
      var delay = BzConfigManager.getByPathAsInt("ScWms", "outbound", "autoAssignDelay") ?: 0
      if (delay <= 0) delay = 5000
      delay.toLong()
    }) {
      val enabled = BzConfigManager.getByPath("ScWms", "outbound", "autoAssign") as Boolean?
      
      SocService.updateNode("业务", "Bz:AutoOutbound", "出库处理", "启用=$enabled")
      
      if (enabled == true) {
        tryOutbound()
      }
    }
  }
  
  override fun beforeUpdating(em: EntityMeta, ids: List<String>, update: EntityValue): Long? {
    val directOutboundOrderName = BzConfigManager.getByPath("ScWms", "outbound", "directOrder") as String?
    if (em.name == directOutboundOrderName) {
      if (update[FieldMeta.FIELD_ORDER_STATE] == "Committed") {
        for (id in ids) {
          val oldOrder = EntityRwService.findOne(directOutboundOrderName, Cq.idEq(id)) ?: continue
          // 因为承接的直接出库单可能还用于非直接出库，因此检查下 oldOrder["direct"] 标记
          if (oldOrder[FieldMeta.FIELD_ORDER_STATE] == "Init" && oldOrder["direct"] == true) {
            logger.info("提交直接入库单：$id")
            val lines = EntityHelper.getLines(update, FieldMeta.FIELD_LINES)
              ?: EntityHelper.getLines(oldOrder, FieldMeta.FIELD_LINES)
            lockDirectOrderContainerLayouts(lines, id)
          }
        }
      }
    }
    return null
  }
  
  /**
   * 注释见：doOutbound
   */
  private fun tryOutbound() {
    // 每次重新解析，热更新
    val outboundConfigs = parseOrderConfigs() // TODO 放到 block，记录当时的规则？
    
    for (config in outboundConfigs) { // TODO 需要记录执行到哪个 config 了
      if (config.pickEmName.isBlank()) continue // TODO 系统监控
      
      // TODO orderIds 需要幂等
      val orderIds: List<String>? = if (ScriptCenter.exists("listToBeProcessedOutboundOrders")) {
        ScriptCenter.execute(
          ScriptExeRequest("listToBeProcessedOutboundOrders", arrayOf(ScriptTraceContext(), config.outboundEmName)),
          jacksonTypeRef(),
        )
      } else {
        null
      }
      
      if (orderIds != null) {
        if (orderIds.isNotEmpty()) {
          // 注意保持 id 顺序
          for (orderId in orderIds) {
            // TODO order 要幂等
            val order = EntityRwService.findOneById(config.outboundEmName, orderId) ?: continue
            doOutboundWithNoError(config, order)
          }
        }
      } else {
        // 状态为已提交，库存都找到为 false；按时间排序
        val orders = EntityRwService.findMany(
          config.outboundEmName,
          Cq.and(listOf(Cq.eq(FieldMeta.FIELD_ORDER_STATE, "Committed"), Cq.ne("invAssignedAll", true))),
          FindOptions(sort = listOf("createdOn")),
        )
        // TODO orders 要幂等
        for (order in orders) { // TODO 要记录已经执行完的 order
          doOutboundWithNoError(config, order)
        }
      }
    }
  }
  
  private fun doOutboundWithNoError(config: OutboundConfig, order: EntityValue) {
    val orderId = EntityHelper.mustGetId(order)
    try {
      doOutbound(config, order)
    } catch (e: BzError) {
      logger.info(e.message ?: "")
    } catch (e: Exception) {
      logger.error("尝试出库分配库存失败 ${config.outboundEmName}:$orderId", e)
    }
  }
  
  /**
   * 找库存暂时只支持逐单（出库单）处理。
   * 带资源锁执行。
   * 要求整单库存全满足，否则抛出异常：code=errBzOutboundLineShort
   * 锁定容器，将容器 taskType 设为 Pick
   * 锁定库存明细，不对库存明细进行其他修改。
   * 如果启用分拣，按容器创建分拣单，初始状态 "Todo"
   * 出库单行，更新 invAssignedQty
   * 出库单，invAssignedAll 设置为 true
   */
  private fun doOutbound(config: OutboundConfig, order: EntityValue) {
    val direct = order["direct"] == true
    val orderId = EntityHelper.mustGetId(order)
    logger.info("执行出库单，分配库存 ${config.outboundEmName}:$orderId 直接出库=$direct")
    
    val lines = EntityHelper.getLines(order, FieldMeta.FIELD_LINES)
    if (lines.isNullOrEmpty()) return
    
    val containerAssignedList: MutableList<ContainerAssigned> = ArrayList() // TODO 如何记录执行中的这些数据
    val plans: MutableList<EntityValue> = ArrayList() // TODO 如何记录执行中的数据
    
    // 按容器归集分拣单
    val pickOrders: Map<String, EntityValue>
    var short = false
    ResLockService.resLock.withLock {
      if (direct) { // TODO 后面想
        // 直接出库，不用分库存
        pickOrders = directOrderToPickOrders(config, orderId, lines)
        
        val containerIds = pickOrders.keys.toList()
        // 这里要多改一个任务类型，TODO 但是没有清的动作
        for (containerId in containerIds) {
          EntityRwService.updateMany(
            "FbContainer",
            Cq.include("id", containerIds),
            mutableMapOf("taskType" to "Pick"),
          )
        }
      } else {
        for (line in lines) {
          // TODO shortQty 要不要幂等
          // TODO containerAssignedList 作为输入参数，会被改，如何记录
          val shortQty = doOutboundLine(config, line, orderId, order, containerAssignedList, plans)
          short = short || (shortQty > 0)
        }
        
        if (plans.isEmpty()) return // 不太可能，就是库存没满足；但如果发生了，跳出大范围
        pickOrders = plansToPickOrders(plans, config, containerAssignedList)
        
        val containerIds = pickOrders.keys.toList()
        val c1 = EntityRwService.updateMany(
          "FbContainer",
          Cq.include("id", containerIds),
          mutableMapOf("locked" to true, "taskType" to "Pick"),
        )
        logger.info("锁定容器，更新数=$c1")
        
        // TODO FIELD_LEAF_CONTAINER
        val c2 = EntityRwService.updateMany(
          "FbInvLayout",
          Cq.include(FieldMeta.FIELD_LEAF_CONTAINER, containerIds),
          mutableMapOf("locked" to true),
        )
        logger.info("锁定库存明细数=$c2")
      }
    }
    
    // 锁外 ----------
    
    for (ca in containerAssignedList) {
      val pickOrder = pickOrders[ca.id] ?: continue
      val allUsed = ca.isContainerAllUsed()
      pickOrder["allUsed"] = allUsed
    }
    
    if (config.wholeOut) {
      // 分拣单直接完成
      for (po in pickOrders.values) {
        po[FieldMeta.FIELD_ORDER_STATE] = "Done"
        po["submittedPostProcessed"] = true
      }
    }
    
    // 生成分拣单
    logger.info("分拣单数=${pickOrders.size}：${pickOrders.values}")
    
    // 扩展生成分拣单
    val extRes: PickOrderToReduceInvRes? = if (ScriptCenter.exists("createPickOrderExt")) {
      ScriptCenter.execute(
        ScriptExeRequest(
          "createPickOrderExt",
          arrayOf(
            ScriptTraceContext(),
            config.pickEmName,
            config.outboundEmName,
            pickOrders.values.toList(),
            config.invMatchFields,
          ),
        ),
        jacksonTypeRef(),
        
        )
    } else {
      null
    }
    
    if (extRes != null) {
      if (extRes.error) throw BzError("errBzError", extRes.errorMsg)
    } else {
      EntityRwService.createMany(config.pickEmName, pickOrders.values.toList())
    }
    if (!short) {
      order["invAssignedAll"] = true // 全找到了
    }
    
    // 更新整个出库单，其实不太好
    EntityRwService.updateOne(config.outboundEmName, Cq.idEq(orderId), order)
    
    if (config.noOutTransportOrder != true) {
      for (containerId in pickOrders.keys) {
        val po = pickOrders[containerId]
        val pickOrderId = po?.get("id") as String? ?: ""
        val outboundId = po?.get("sourceOrderId")
        // 当前库位
        val binEv = EntityRwService.findOne("FbBin", Cq.eq("container", containerId))
        if (binEv == null) {
          logger.warn("容器 '$containerId' 当前库位为空")
          continue
        }
        
        // 生成容器搬运单
        val transportOrderStatus = if (config.pendingOut) "Building" else "Created"
        val def = if (!config.containerOutFalconTask.isNullOrBlank()) {
          FalconTaskDefService.mustFetchLatestTaskDefById(config.containerOutFalconTask)
        } else {
          null
        }
        val transportOrder: EntityValue = mutableMapOf(
          "container" to containerId,
          "kind" to "ContainerOut",
          "status" to transportOrderStatus,
          "fromBin" to EntityHelper.mustGetId(binEv),
          "falconTaskDefId" to config.containerOutFalconTask, // 用哪个猎鹰任务执行此容器搬运单
          "falconTaskDefLabel" to def?.label,
          "sourceOrderId" to outboundId,
        )
        for (field in config.outboundOrderToContainerTransportFields) {
          transportOrder[field] = order[field]
        }
        val transportOrderId = EntityRwService.createOne("ContainerTransportOrder", transportOrder)
        
        // TODO
        // 写到分拣单的 containerOutOrderId
        EntityRwService.updateOne(
          config.pickEmName,
          Cq.idEq(pickOrderId),
          mutableMapOf("containerOutOrderId" to transportOrderId),
        )
      }
    }
  }
  
  /**
   * 出库单行分配库存。返回值：缺货数量。
   */
  private fun doOutboundLine(
    config: OutboundConfig,
    line: EntityValue,
    orderId: String,
    order: EntityValue,
    containerAssignedList: MutableList<ContainerAssigned>,
    plans: MutableList<EntityValue>,
  ): Double {
    val lineNo = EntityHelper.mustGetLineNo(line)
    logger.info("为出库单行找库存 $orderId:$lineNo $line")
    
    val materialId = line[FieldMeta.FIELD_MATERIAL]
    
    val qty = NumHelper.anyToDouble(line["qty"]) ?: 0.0 // 需要出库的总数
    if (qty <= 0) return 0.0
    val invAssignedQty = NumHelper.anyToDouble(line["invAssignedQty"]) ?: 0.0 // 已分配库存的数量
    var restQty = qty - invAssignedQty // TODO
    logger.debug("本次需要出库 $restQty")
    if (restQty <= 0) return 0.0
    
    // 先在已经要出库的容器里找
    restQty = findPendingContainers(config, restQty, containerAssignedList, line, plans) // TODO
    
    // 找新容器
    if (restQty > 0) {
      val cqList = matchFieldsToCqEq(line, config.invMatchFields)
      cqList += Cq.ne("locked", true)
      if (!config.invFilterStates.isNullOrEmpty()) {
        cqList += Cq.include("state", config.invFilterStates)
      }
      if (!config.invFilterDistricts.isNullOrEmpty()) {
        cqList += Cq.include("district", config.invFilterDistricts)
      }
      
      var layoutSort = listOf("inboundOn") // 默认按出库时间排序
      
      // 通过脚本扩展找库存的方法
      if (!config.invFinderScriptFunc.isNullOrBlank()) {
        val r: InvFinderScriptFuncResult? = ScriptCenter.execute(
          ScriptExeRequest(config.invFinderScriptFunc, arrayOf(line, order)),
          jacksonTypeRef(),
        )
        if (r?.query != null) cqList += r.query
        if (r?.sort != null) layoutSort = r.sort
      }
      
      var layouts = EntityRwService.findMany(
        "FbInvLayout",
        Cq.and(cqList),
        FindOptions(sort = layoutSort),
      )
      logger.debug("找到初步匹配的库存明细：${layouts.size}")
      
      // 如果需要分库区，进一步排一下序
      if (!config.invFilterDistricts.isNullOrEmpty() && config.invFilterDistricts.size > 1) {
        layouts = layouts.sortedBy { config.invFilterDistricts.indexOf(it["district"]) }
      }
      
      val badContainerIds: MutableList<String> = ArrayList() // 已经查过不符合要求的容器
      for (layout in layouts) {
        val containerId = layout[FieldMeta.FIELD_LEAF_CONTAINER] as String
        if (containerAssignedList.find { it.id == containerId } != null ||
          // 这里找新容器，如果这里不为 null 就不是新容器
          badContainerIds.indexOf(containerId) >= 0
        ) {
          continue
        }
        val containerEv = EntityRwService.findOne("FbContainer", Cq.idEq(containerId)) ?: continue
        if (containerEv["locked"] == true) continue
        val ca = initContainerAssigned(containerId)
        val restQty2 = findInContainer(config, restQty, ca, line, plans)
        if (restQty2 < restQty) {
          // 表示在此容器中找到了
          containerAssignedList += ca
          restQty = restQty2
        } else {
          badContainerIds += containerId
        }
        if (restQty <= 0) break
      }
    }
    
    if (restQty > 0) {
      // 缺货
      if (config.shortMode == OutboundShortMode.Part) {
        // 部分出
        line["invAssignedQty"] = qty - restQty
      } else {
        // 报错
        throw BzError("errBzOutboundLineShort", lineNo, restQty, materialId)
      }
    } else {
      line["invAssignedQty"] = qty
    }
    
    return restQty
  }
  
  // 先在已找到的容器里找
  private fun findPendingContainers(
    config: OutboundConfig,
    restQtyInit: Double,
    containerAssignedList: List<ContainerAssigned>,
    line: EntityValue,
    pickLines: MutableList<EntityValue>,
  ): Double {
    var restQty = restQtyInit
    for (containerA in containerAssignedList) {
      restQty = findInContainer(config, restQty, containerA, line, pickLines)
      if (restQty <= 0) return restQty
    }
    return restQty
  }
  
  // 在指定容器里找。restQtyInit 是找这个容器前，剩余待分配数量。返回找完这个容器后的待分配数量。
  private fun findInContainer(
    config: OutboundConfig,
    restQtyInit: Double,
    ca: ContainerAssigned,
    line: EntityValue,
    pickLines: MutableList<EntityValue>,
  ): Double {
    logger.info("在容器 '${ca.id}' 中找库存，待找=$restQtyInit $ca")
    var restQty = restQtyInit
    for (sa in ca.subList) {
      // base.logInfo(`layout=${sa.layout} line=${line}`)
      for (lu in sa.layouts) {
        if (!isLayoutMatch(lu.layout, line, config.invMatchFields)) continue
        if (lu.assignedQty >= lu.storeQty) continue
        val thisQty = min(restQty, lu.storeQty - lu.assignedQty)
        lu.assignedQty += thisQty
        restQty -= thisQty
        
        val pickLine: EntityValue = mutableMapOf(
          "qty" to thisQty,
          "container" to ca.id,
          FieldMeta.FIELD_SUB_CONTAINER_ID to sa.subId,
          "sourceLineId" to line["id"],
          "sourceLineNo" to line[FieldMeta.FIELD_LINE_NO],
          "sourceOrderId" to line[FieldMeta.FIELD_PARENT_ID],
        )
        for (fn in config.invMatchFields) {
          pickLine[fn] = lu.layout[fn]
        }
        for (fn in config.outboundLineToPickFields) {
          pickLine[fn] = line[fn]
        }
        logger.info("分配库存：$pickLine")
        pickLines += pickLine
        
        if (restQty <= 0) return restQty
      }
      if (restQty <= 0) return restQty
    }
    return restQty
  }
  
  private fun isLayoutMatch(layout: EntityValue, line: EntityValue, fields: List<String>): Boolean {
    for (fn in fields) {
      val v = line[fn]
      if (v is String? && v.isNullOrBlank()) continue // 如果是字符串，则单行指定的值则匹配，不指定则不参与匹配
      if (layout[fn] != line[fn]) return false
    }
    return true
  }
  
  fun matchFieldsToCqEq(line: EntityValue, fields: List<String>): MutableList<ComplexQuery> {
    val cqList = mutableListOf<ComplexQuery>()
    for (fn in fields) {
      val v = line[fn]
      if (v is String? && v.isNullOrBlank()) continue // 如果是字符串，则单行指定的值则匹配，不指定则不参与匹配
      cqList += Cq.eq(fn, v)
    }
    return cqList
  }
  
  /**
   * 分拣单初始状态 "Todo"
   */
  private fun plansToPickOrders(
    plans: List<EntityValue>,
    config: OutboundConfig,
    containerAssignedList: List<ContainerAssigned>,
  ): Map<String, EntityValue> {
    val pickOrders: MutableMap<String, EntityValue> = HashMap() // by container
    if (config.wholeOut) {
      // 一个容器要出全出
      for (ca in containerAssignedList) {
        if (!ca.isAnyAssigned()) continue // 未使用
        val plan = plans.find { it["container"] == ca.id }
        val lines = mutableListOf<EntityValue>()
        for (subA in ca.subList) {
          for (lu in subA.layouts) {
            val line: EntityValue = mutableMapOf(
              "container" to ca.id,
              FieldMeta.FIELD_SUB_CONTAINER_ID to subA.subId,
              "planQty" to lu.assignedQty,
              "qty" to lu.storeQty,
              "sourceOrderId" to plan?.get("sourceOrderId") as String?,
              "sourceLineId" to plan?.get("sourceLineId") as String?,
              "sourceLineNo" to plan?.get("sourceLineNo") as Int?,
            )
            for (fn in config.invMatchFields) {
              line[fn] = lu.layout[fn]
            }
            val materialId = line[FieldMeta.FIELD_MATERIAL] as String?
            if (!materialId.isNullOrEmpty()) MaterialManager.fillMaterialFieldsIntoLine(materialId, line)
            lines += line
          }
        }
        
        val pickOrder: EntityValue = mutableMapOf(
          FieldMeta.FIELD_BZ_KIND to "NormalTask",
          FieldMeta.FIELD_ORDER_STATE to "Todo",
          "container" to ca.id,
          "sourceOrderId" to plans.find { it["container"] == ca.id }?.get("sourceOrderId") as String?,
          FieldMeta.FIELD_LINES to lines,
        )
        pickOrders[ca.id] = pickOrder
      }
    } else {
      // 正常分拣
      for (plan in plans) {
        val containerId = plan["container"] as String
        var pickOrder = pickOrders[containerId]
        if (pickOrder == null) {
          pickOrder = mutableMapOf(
            FieldMeta.FIELD_BZ_KIND to "NormalTask",
            FieldMeta.FIELD_ORDER_STATE to "Todo",
            "container" to containerId,
            "sourceOrderId" to plan["sourceOrderId"] as String?,
            FieldMeta.FIELD_LINES to ArrayList<EntityValue>(),
          )
          pickOrders[containerId] = pickOrder
        }
        val qty = NumHelper.anyToDouble(plan["qty"])!!
        val line: EntityValue = mutableMapOf(
          "container" to containerId,
          FieldMeta.FIELD_SUB_CONTAINER_ID to plan[FieldMeta.FIELD_SUB_CONTAINER_ID] as Int?,
          "planQty" to qty,
          "qty" to qty,
          "sourceOrderId" to plan["sourceOrderId"] as String?,
          "sourceLineId" to plan["sourceLineId"] as String?,
          "sourceLineNo" to plan["sourceLineNo"] as Int?,
        )
        for (fn in config.invMatchFields) {
          line[fn] = plan[fn]
        }
        for (fn in config.outboundLineToPickFields) {
          line[fn] = plan[fn]
        }
        val materialId = line[FieldMeta.FIELD_MATERIAL] as String?
        if (!materialId.isNullOrEmpty()) MaterialManager.fillMaterialFieldsIntoLine(materialId, line)
        val lines = EntityHelper.getMutableLines(pickOrder, FieldMeta.FIELD_LINES)!!
        lines += line
      }
    }
    return pickOrders
  }
  
  /**
   * 直接出库单到分拣单
   */
  private fun directOrderToPickOrders(
    config: OutboundConfig,
    outboundOrderId: String,
    lines: List<EntityValue>,
  ): Map<String, EntityValue> {
    val pickOrders: MutableMap<String, EntityValue> = HashMap() // by container
    
    for (outLine in lines) {
      val layoutId = outLine["layoutId"] as String?
      if (layoutId.isNullOrBlank()) {
        logger.error("直接出库，出库单行没有库存 ID，出库单=$outboundOrderId，行=$outLine")
        continue
      }
      val layoutEv = EntityRwService.findOne("FbInvLayout", Cq.idEq(layoutId))
      if (layoutEv == null) {
        logger.error("直接出库，查不到库存明细，出库单=$outboundOrderId，layoutId=$layoutId")
        continue
      }
      val containerId = StringUtils.firstNonBlank(
        layoutEv[FieldMeta.FIELD_TOP_CONTAINER] as String?,
        layoutEv[FieldMeta.FIELD_LEAF_CONTAINER] as String?,
      )
      if (containerId.isNullOrBlank()) {
        logger.error("直接出库，容器未知，出库单=$outboundOrderId，layoutId=$layoutId")
        continue
      }
      
      var pickOrder = pickOrders[containerId]
      if (pickOrder == null) {
        pickOrder = mutableMapOf(
          "direct" to true,
          FieldMeta.FIELD_BZ_KIND to "NormalTask",
          FieldMeta.FIELD_ORDER_STATE to "Todo",
          "container" to containerId,
          "sourceOrderId" to outboundOrderId,
          FieldMeta.FIELD_LINES to ArrayList<EntityValue>(),
        )
        pickOrders[containerId] = pickOrder
      }
      val qty = NumHelper.anyToDouble(outLine["qty"])!!
      val pickLine: EntityValue = mutableMapOf(
        "layoutId" to layoutId, // 关键！
        "container" to containerId,
        FieldMeta.FIELD_SUB_CONTAINER_ID to layoutEv[FieldMeta.FIELD_SUB_CONTAINER_ID] as Int?,
        "planQty" to qty,
        "qty" to qty,
        "sourceOrderId" to outboundOrderId,
        "sourceLineId" to outLine["id"] as String?,
        "sourceLineNo" to outLine[FieldMeta.FIELD_LINE_NO] as Int?,
      )
      for (fn in config.invMatchFields) {
        pickLine[fn] = outLine[fn]
      }
      for (fn in config.outboundLineToPickFields) {
        pickLine[fn] = outLine[fn]
      }
      val materialId = pickLine[FieldMeta.FIELD_MATERIAL] as String?
      if (!materialId.isNullOrEmpty()) MaterialManager.fillMaterialFieldsIntoLine(materialId, pickLine)
      val pickLines = EntityHelper.getMutableLines(pickOrder, FieldMeta.FIELD_LINES)!!
      pickLines += pickLine
    }
    
    return pickOrders
  }
  
  /**
   * 将容器里的库存按格子汇总，如果库存没在格子里，放到 0 格子
   */
  private fun initContainerAssigned(containerId: String): ContainerAssigned {
    val layouts = EntityRwService.findMany(
      "FbInvLayout",
      Cq.and(listOf(Cq.eq(FieldMeta.FIELD_LEAF_CONTAINER, containerId), Cq.ne("locked", true))),
    )
    val subList: MutableList<ContainerSubAssigned> = ArrayList()
    for (layout in layouts) {
      val qty = NumHelper.anyToDouble(layout["qty"])
      if (qty == null || qty <= 0) continue
      val subContainerId = layout[FieldMeta.FIELD_SUB_CONTAINER_ID] as Int? ?: 0
      var subA = subList.find { it.subId == subContainerId }
      if (subA == null) {
        subA = ContainerSubAssigned(subId = subContainerId)
        subList += subA
      }
      subA.layouts += InvLayoutAssigned(layout, qty)
    }
    return ContainerAssigned(containerId, subList)
  }
  
  @Suppress("UNCHECKED_CAST")
  private fun parseOrderConfigs(): List<OutboundConfig> {
    val ordersConfigs = BzConfigManager.getByPath("ScWms", "outbound", "orders") as List<*>? ?: return emptyList()
    return ordersConfigs.mapNotNull { orderConfig ->
      val ocEv = orderConfig as EntityValue
      
      val invMatchFields = ocEv["invMatchFields"] as MutableList<String>? ?: ArrayList()
      if (invMatchFields.indexOf(FieldMeta.FIELD_MATERIAL) < 0) invMatchFields += FieldMeta.FIELD_MATERIAL
      
      val outboundLineToPickFieldsStr = ocEv["outboundLineToPickFields"] as String? ?: ""
      val outboundLineToPickFields = StringHelper.splitTrim(outboundLineToPickFieldsStr, ",")
      
      val outboundOrderEm = ocEv["outboundOrderEm"] as String?
      if (outboundOrderEm.isNullOrBlank()) return@mapNotNull null
      
      val shortModeStr = ocEv["shortMode"] as String?
      val shortMode = if (shortModeStr.isNullOrBlank()) null else OutboundShortMode.valueOf(shortModeStr)
      
      val outboundOrderToContainerTransportFieldsStr = ocEv["outboundOrderToContainerTransportFields"] as String? ?: ""
      val outboundOrderToContainerTransportFields =
        StringHelper.splitTrim(outboundOrderToContainerTransportFieldsStr, ",")
      
      OutboundConfig(
        outboundEmName = ocEv["outboundOrderEm"] as String,
        wholeOut = ocEv["wholeOut"] as Boolean? ?: false,
        pickEmName = ocEv["pickOrderEm"] as String? ?: "",
        invMatchFields = invMatchFields,
        outboundLineToPickFields = outboundLineToPickFields,
        noOutTransportOrder = ocEv["noOutTransportOrder"] as Boolean? ?: false,
        noInTransportOrder = ocEv["noInTransportOrder"] as Boolean? ?: false,
        pendingOut = ocEv["pendingOut"] as Boolean? ?: false,
        outboundOrderToContainerTransportFields = outboundOrderToContainerTransportFields,
        invFinderScriptFunc = ocEv["invFinderScriptFunc"] as String?,
        containerOutFalconTask = ocEv["containerOutFalconTask"] as String?,
        containerInFalconTask = ocEv["containerInFalconTask"] as String?,
        invFilterDistricts = ocEv["invFilterDistricts"] as List<String>?,
        invFilterStates = ocEv["invFilterStates"] as List<String>?,
        shortMode = shortMode,
      )
    }
  }
  
  fun findOutboundConfig(emName: String): OutboundConfig? {
    val outboundConfigs = parseOrderConfigs()
    return outboundConfigs.find { it.pickEmName == emName }
  }
  
  /**
   * 锁定直接出库单的容器和库存明细
   */
  private fun lockDirectOrderContainerLayouts(lines: List<EntityValue>?, orderId: String) {
    if (lines.isNullOrEmpty()) return
    
    ResLockService.resLock.withLock {
      val errors = mutableListOf<String>()
      
      val layoutIds = lines.map { it["layoutId"] as String }
      val layoutEvList = EntityRwService.findMany("FbInvLayout", Cq.include("id", layoutIds))
      
      val containerIds = mutableSetOf<String>()
      
      for ((i, layoutId) in layoutIds.withIndex()) {
        val layoutEv = layoutEvList.find { it["id"] == layoutId }
        if (layoutEv == null) {
          errors += "第 ${i + 1} 行库存明细已不存在，可能已被出库。id=$layoutId"
        } else {
          if (layoutEv["locked"] == true) {
            errors += "第 ${i + 1} 行库存明细已被锁定，正在被用于其他出库。id=$layoutId"
          } else {
            val containerId = StringUtils.firstNonBlank(
              layoutEv[FieldMeta.FIELD_TOP_CONTAINER] as String?,
              layoutEv[FieldMeta.FIELD_LEAF_CONTAINER] as String?,
            )
            if (containerId.isNullOrBlank()) {
              errors += "第 ${i + 1} 行库存明细上没有配置容器。id=$layoutId"
            } else {
              containerIds += containerId
            }
          }
        }
      }
      
      logger.info("直接出库 $orderId，尝试锁定库存：" + layoutIds.joinToString(", "))
      logger.info("直接出库 $orderId，尝试锁定容器：" + containerIds.joinToString(", "))
      
      if (errors.isNotEmpty()) {
        logger.error("直接出库 $orderId 锁定资源失败：" + errors.joinToString(" "))
        throw BzError("errDirectOutboundSubmitFail", errors.joinToString("\n"))
      }
      
      EntityRwService.updateMany(
        "FbInvLayout",
        Cq.include("id", layoutIds),
        mutableMapOf("locked" to true),
      )
      
      EntityRwService.updateMany(
        "FbContainer",
        Cq.include("id", containerIds.toList()),
        mutableMapOf("locked" to true),
      )
    }
  }
}