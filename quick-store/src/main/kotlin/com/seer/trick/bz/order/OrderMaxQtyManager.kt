package com.seer.trick.bz.order

import com.seer.trick.*
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.helper.NumHelper

@Deprecated("Using OrderQtyService")
object OrderMaxQtyManager {

  /**
   * 计算一个单据的所有单行的本次最大可发生数量
   * 从一个下游单据，计算已发生数量
   */
  fun calcOccurredQty(
    orderId: String, orderName: String, orderKind: String
  ): List<Double> {
    // 先找上游单据，至多有一个上游单据
    val record = EntityRwService.findOne("OrderFlowRecord", Cq.eq("targetOrderId", orderId))
      ?: return emptyList()

    val sourceOrderName = record["sourceOrderName"] as String
    val sourceOrderId = record["sourceOrderId"] as String

    val sourceOrder = EntityRwService.findOne(sourceOrderName, Cq.idEq(sourceOrderId))
      ?: throw BzError("errBzNoSuchOrderNameId", sourceOrderName, sourceOrderId)

    // 找目前所有下推，限定类型
    val records = EntityRwService.findMany(
      "OrderFlowRecord",
      Cq.and(
        listOf(
          Cq.eq("sourceOrderId", sourceOrderId),
          Cq.eq("targetOrderName", orderName),
          Cq.eq("targetOrderType", orderKind),
        )
      ),
    )

    val lines: MutableList<Double> =
      EntityHelper.getLines(sourceOrder, FieldMeta.FIELD_LINES)!!.map { 0.0 }.toMutableList()

    val downOrderIds = records.map { r -> r["targetOrderId"] as String }.toMutableList()
    downOrderIds.remove(orderId) // 去掉自己
    if (downOrderIds.isNotEmpty()) return lines

    val downOrders = EntityRwService.findMany(orderName, Cq.include("id", downOrderIds))

    val downOrderEm = BaseCenter.mustGetEntityMeta(orderName)

    for (downOrder in downOrders) {
      val downOrderLines = EntityHelper.getLines(downOrder, FieldMeta.FIELD_LINES)!!
      for (lineIndex in 0 until lines.size) {
        val downLine = downOrderLines.getOrNull(lineIndex) ?: continue // 其实应该要求行数相同
        val thisQtyFields = downOrderEm.orderConfig?.thisQtyFields ?: continue
        val qty = thisQtyFields.map { fn -> NumHelper.anyToDouble(downLine[fn]) ?: 0.0 }
          .reduce { aac, v -> aac + v }
        val old = NumHelper.anyToDouble(lines[lineIndex]) ?: 0.0
        lines[lineIndex] = old + qty
      }
    }

    return lines
  }

}