package com.seer.trick.bz.wms

import com.seer.trick.Cq
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.change.EntityChange
import com.seer.trick.base.entity.service.extension.EntityServiceExtension
import com.seer.trick.bz.BzExecutor.binOverviewExecutor
import com.seer.trick.helper.NumHelper
import com.seer.trick.helper.submitLongRun
import org.slf4j.LoggerFactory

/**
 * 库位一览
 */
object BinOverviewService : EntityServiceExtension() {

  private val logger = LoggerFactory.getLogger(this::class.java)

  @Volatile
  private var needToRebuild = true

  @Volatile
  var districtMap: Map<String, DistrictOverview> = emptyMap()

  fun init() {
    var lastNeedInv = needInvLayout()
    
    binOverviewExecutor.submitLongRun("处理库位一览", logger, { 3000 }) {
      val needInv = needInvLayout()
      if (needToRebuild || lastNeedInv != needInv) {
        needToRebuild = false
        lastNeedInv = needInv

        rebuild()
      }
    }
  }

  override fun afterCreating(em: EntityMeta, evList: List<EntityValue>) {
    tryRebuild(em)
  }

  override fun afterUpdating(em: EntityMeta, changes: List<EntityChange>) {
    tryRebuild(em)
  }

  override fun afterRemoving(em: EntityMeta, oldValues: List<EntityValue>) {
    tryRebuild(em)
  }

  override fun afterBigChange(em: EntityMeta) {
    tryRebuild(em)
  }

  private fun tryRebuild(em: EntityMeta) {
    if (em.name == "FbDistrict" || em.name == "FbBin" || em.name == "FbInvLayout" && needInvLayout())
      needToRebuild = true
  }

  fun list(): Collection<DistrictOverview> {
    return districtMap.values
  }

  private fun rebuild() {
    val districtEvList = EntityRwService.findMany("FbDistrict", Cq.ne(FieldMeta.FIELD_DISABLED, true))
    val binEvList = EntityRwService.findMany("FbBin", Cq.all())

    val districtMap: MutableMap<String, DistrictOverview> = HashMap()
    for (dEv in districtEvList) {
      val dId = EntityHelper.mustGetId(dEv)
      districtMap[dId] = DistrictOverview(dId, dEv["name"] as String? ?: "", dEv["displayOrder"] as Int? ?: 0)
    }

    val needInv = needInvLayout()

    val invLayoutsByBins = if (needInv) {
      val allInvLayouts = EntityRwService.findMany("FbInvLayout", Cq.all())
      allInvLayouts.groupBy { it["bin"] as String? }
    } else {
      emptyMap()
    }

    for (bin in binEvList) {
      val binId = EntityHelper.mustGetId(bin)
      val districtId = bin["district"] as String? ?: continue
      val row = bin["row"] as Int? ?: 1
      val column = bin["column"] as Int? ?: 1
      val layer = bin["layer"] as Int? ?: 1
      val depth = bin["depth"] as Int? ?: 1
      val disabled = bin["btDisabled"] as Boolean? ?: false
      val occupied = bin["occupied"] as Boolean? ?: false
      val locked = bin["locked"] as Boolean? ?: false

      val containerId = bin["container"] as String?

      val district = districtMap[districtId] ?: continue
      district.binNum++

      if (row < district.rowMin) district.rowMin = row
      if (row > district.rowMax) district.rowMax = row
      if (column < district.columnMin) district.columnMin = column
      if (column > district.columnMax) district.columnMax = column
      if (layer < district.layerMin) district.layerMin = layer
      if (layer > district.layerMax) district.layerMax = layer
      if (depth < district.depthMin) district.depthMin = depth
      if (depth > district.depthMax) district.depthMax = depth

      val binInvLayouts: List<BinInvLayout>? = if (needInv) {
        val invLayouts = invLayoutsByBins[binId]
        if (invLayouts != null) {
          val binInvLayouts = mutableListOf<BinInvLayout>()
          for (il in invLayouts) {
            val b = binInvLayouts.find { it.materialId == il[FieldMeta.FIELD_MATERIAL] }
            val qty = NumHelper.anyToDouble(il["qty"]) ?: 0.0
            if (b != null) {
              b.qty += qty
            } else {
              binInvLayouts += BinInvLayout(
                il[FieldMeta.FIELD_MATERIAL] as String,
                il[FieldMeta.FIELD_MATERIAL_NAME] as String?,
                qty
              )
            }
          }
          binInvLayouts
        } else {
          null
        }
      } else {
        null
      }

      val binOverview = BinOverview(
        id = binId,
        row = row,
        column = column,
        layer = layer,
        depth = depth,
        disabled = disabled,
        occupied = occupied,
        loadStatus = bin["loadStatus"] as String?,
        locked = locked,
        containerId = containerId,
        invLayouts = binInvLayouts
      )

      district.map
        .getOrPut(row) { HashMap() }
        .getOrPut(column) { HashMap() }
        .getOrPut(layer) { HashMap(2) }[depth] = binOverview
    }

    this.districtMap = districtMap

    // WebSocketManager.sendAllAsync(WsMsg("BinOverview", JsonHelper.writeValueAsString(districtMap)))
    // WebSocketManager.sendAllAsync(WsMsg("BinOverviewChanged", ""))
  }

  private fun needInvLayout(): Boolean {
    return BzConfigManager.getByPath("BinOverview", "view") == "BinInv"
  }

}

data class DistrictOverview(
  val id: String,
  val label: String,
  val displayOrder: Int,
  val map: MutableMap<Int, MutableMap<Int, MutableMap<Int, MutableMap<Int, BinOverview>>>> = HashMap(), // 排-列-层-深
  var binNum: Int = 0,
  var rowMin: Int = 1,
  var rowMax: Int = 0,
  var columnMin: Int = 1,
  var columnMax: Int = 0,
  var layerMin: Int = 1,
  var layerMax: Int = 0,
  var depthMin: Int = 1,
  var depthMax: Int = 0,
)

data class BinOverview(
  val id: String,
  val row: Int,
  val column: Int,
  val layer: Int,
  val depth: Int,
  val disabled: Boolean,
  val occupied: Boolean,
  val loadStatus: String?,
  val locked: Boolean,
  val containerId: String?,
  val invLayouts: List<BinInvLayout>? = null
)

data class BinInvLayout(
  val materialId: String,
  val materialName: String?,
  var qty: Double
)