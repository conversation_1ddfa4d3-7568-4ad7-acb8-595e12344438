package com.seer.trick.bz.cto

import com.seer.trick.Cq
import com.seer.trick.base.concurrent.BaseConcurrentCenter.highTimeSensitiveExecutor
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.UpdateOptions
import com.seer.trick.base.entity.service.change.EntityChange
import com.seer.trick.base.entity.service.extension.EntityServiceExtension
import com.seer.trick.falcon.domain.CreateTaskReq
import com.seer.trick.falcon.task.FalconTaskDefService
import com.seer.trick.falcon.task.FalconTaskService
import com.seer.trick.helper.submitCatch
import org.slf4j.LoggerFactory
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 容器搬运单
 */
object ContainerTransportService : EntityServiceExtension() {

  private val logger = LoggerFactory.getLogger(this::class.java)

  private val callbacks: MutableList<ContainerTransportCallback> = CopyOnWriteArrayList()

  fun addCallback(cb: ContainerTransportCallback) {
    if (callbacks.contains(cb)) {
      logger.error("重复添加容器搬运回调 $cb")
      return
    }
    callbacks += cb
  }

  override fun afterCreating(em: EntityMeta, evList: List<EntityValue>) {
    if (em.name != "ContainerTransportOrder") return
    highTimeSensitiveExecutor.submitCatch("后处理容器搬运单", logger) {
      for (ev in evList) {
        tryStartFalconTask(ev)
      }
    }
  }

  override fun afterUpdating(em: EntityMeta, changes: List<EntityChange>) {
    if (em.name != "ContainerTransportOrder") return
    highTimeSensitiveExecutor.submitCatch("后处理容器搬运单", logger) {
      for (c in changes) {
        val oldEv = c.oldValue ?: continue
        val newEv = c.newValue ?: continue

        val oldOrder = CtoDto(oldEv)
        val newOrder = CtoDto(newEv)

        tryStartFalconTask(newEv) // 为什么为 null

        // 取货完成
        if (!oldOrder.loaded && newOrder.loaded) {
          for (cb in callbacks) {
            cb.afterLoad(newOrder)
          }
        }
        // 放货完成
        if (!oldOrder.unloaded && newOrder.unloaded) {
          for (cb in callbacks) {
            cb.afterUnload(newOrder)
          }
        }
        // 完成
        if (oldOrder.status != CtoStatus.Done && newOrder.status == CtoStatus.Done) {
          for (cb in callbacks) {
            cb.onDone(newOrder)
          }
        }
        // 取消
        if (oldOrder.status != CtoStatus.Cancelled && newOrder.status == CtoStatus.Cancelled) {

          // 容器搬运单已被取消，取消与之关联的猎鹰任务
          val falconTaskId = newOrder.falconTaskId
          val orderId = newOrder.id
          if (!falconTaskId.isNullOrBlank() &&
            FalconTaskService.unfinishedTasks.keys.any { it == falconTaskId } // 忽略已结束的猎鹰任务。
          ) {
            logger.info("容器搬运单 $orderId 已取消，并取消关联的猎鹰任务 $falconTaskId")
            // TODO: 部分情况下，reason 会被 TaskCancelledError 覆盖
            FalconTaskService.cancelTask(falconTaskId, "关联的容器搬运单 $orderId 已取消")
          }

          for (cb in callbacks) {
            cb.onCancelled(newOrder)
          }
        }
      }
    }
  }

  /**
   * 用猎鹰任务搬运容器搬运单
   */
  private fun tryStartFalconTask(ev: EntityValue) {
    val orderId = EntityHelper.mustGetId(ev)
    val status = ev["status"] as String?
    val falconTaskId = ev["falconTaskId"] as String?
    val falconTaskDefId = ev["falconTaskDefId"] as String?
    val container = ev["container"] as String?

    /**
     * 如果传递了猎鹰任务模板ID，则直接用，没有则查猎鹰任务模板名
     * 匹配模板名对应的猎鹰任务配置，注：猎鹰任务模板名称不要重复
     */
    val falconTaskDef = FalconTaskDefService.list().find { it.label == ev["falconTaskDefLabel"] }
    if (status == "Created" && falconTaskId.isNullOrBlank() &&
      (!falconTaskDefId.isNullOrBlank() || falconTaskDef != null)
    ) {
      val tr = FalconTaskService.createTopTask(
        CreateTaskReq(
          falconTaskDefId ?: falconTaskDef!!.id, mapOf(
            "container" to container,
            "fromBin" to ev["fromBin"],
            "toBin" to ev["toBin"],
            "transportOrderId" to orderId
          )
        )
      )

      EntityRwService.updateOne(
        "ContainerTransportOrder", Cq.idEq(orderId),
        mutableMapOf("falconTaskId" to tr.taskId), UpdateOptions(muteExt = true) // 防止死循环
      )

      logger.info("为容器搬运单创建猎鹰任务，容器搬运单=$orderId，任务=${tr.taskId}")
      FalconTaskService.runTaskAsync(tr)
    }
  }

}

class CtoDto(val ev: EntityValue) {
  val id = ev["id"] as String
  val status = toStatus()
  val loaded = ev["loaded"] as Boolean? ?: false
  val unloaded = ev["unloaded"] as Boolean? ?: false
  val container = ev["container"] as String?
  val fromBin = ev["fromBin"] as String?
  val toBin = ev["toBin"] as String?
  val robotName = ev["robotName"] as String?
  val postProcessMark = ev["postProcessMark"] as String?
  val falconTaskId = ev["falconTaskId"] as String?

  private fun toStatus(): CtoStatus? {
    val status = ev["status"] as String?
    return try {
      if (status.isNullOrBlank()) null else CtoStatus.valueOf(status)
    } catch (_: Exception) {
      null
    }
  }
}

enum class CtoStatus {
  Building, Created, Assigned, Failed, Done, Cancelled
}

/**
 * 容器搬运关键生命周期回调
 */
abstract class ContainerTransportCallback {

  /**
   * 取货完成后
   */
  open fun afterLoad(order: CtoDto) {
    // do nothing
  }

  /**
   * 放货完成后
   */
  open fun afterUnload(order: CtoDto) {
    // do nothing
  }

  /**
   * 运单完成
   */
  open fun onDone(order: CtoDto) {
    // do nothing
  }

  /**
   * 运单取消
   */
  open fun onCancelled(order: CtoDto) {
    // do nothing
  }

}