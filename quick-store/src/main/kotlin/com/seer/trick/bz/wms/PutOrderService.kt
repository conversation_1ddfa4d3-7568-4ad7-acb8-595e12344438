package com.seer.trick.bz.wms

import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.bz.wms.inv.CreateInvLine
import com.seer.trick.bz.wms.inv.CreateInvService
import org.slf4j.LoggerFactory

/**
 * 装货单
 */
object PutOrderService {

  private val logger = LoggerFactory.getLogger(this::class.java)

  /**
   * 装货单状态改为 Done。
   * 生成库存明细。
   * 需要利用容器查库位表，获得容器当前存储位置。
   */
  fun finishPutOrderByContainer(containerId: String) {
    val order = EntityRwService.findOne(
      "PutinContainerOrder",
      Cq.and(listOf(Cq.eq("container", containerId), Cq.eq(FieldMeta.FIELD_ORDER_STATE, "Filled"))),
    )
    if (order == null) {
      logger.error("按容器完成装货单，但找不到容器 '$containerId' 对应的未完成装货单")
      return
    }
    val orderId = EntityHelper.mustGetId(order)
    logger.info("按容器完成装货单，容器 '$containerId' 装货单 '$orderId'")

    EntityRwService.updateOne(
      "PutinContainerOrder",
      Cq.idEq(orderId),
      mutableMapOf(FieldMeta.FIELD_ORDER_STATE to "Done"),
    )

    val binEv = EntityRwService.findOne("FbBin", Cq.eq("container", containerId))
    if (binEv == null) {
      logger.error("完成装货单，按容器 '$containerId' 找不到存储库位")
    }

    // 生成库存
    val putOrderLines = EntityHelper.getLines(order, FieldMeta.FIELD_LINES)
    if (!putOrderLines.isNullOrEmpty()) {
      val copyFields = CallContainerService.getCopyFields()
      val invLines = putOrderLines.map {
        val moreFields = it.filterKeys { copyFields.contains(it) }
        CreateInvLine(
          qty = it["qty"] as Double,
          material = it[FieldMeta.FIELD_MATERIAL] as String,
          state = "Storing",
          bin = binEv?.get("id") as String?,
          leafContainer = containerId,
          subContainerId = it[FieldMeta.FIELD_SUB_CONTAINER_ID] as Int?,
          moreFields = moreFields,
        )
      }

      logger.info("完成装货单，生成库存明细")
      CreateInvService.fixCreateInvLayoutByLines(invLines)
    }

    // 修复容器状态
    ContainerService.fixContainerFilled(containerId)
  }
}