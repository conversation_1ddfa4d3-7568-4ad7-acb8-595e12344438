package com.seer.trick.bz.wms

import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.reslock.ResLockService
import kotlin.concurrent.withLock

object ContainerService {

  /**
   * 矫正容器空满状态
   */
  fun fixContainerFilled(containerId: String) {
    if (containerId.isBlank()) return
    val count = EntityRwService.count("FbInvLayout", Cq.eq(FieldMeta.FIELD_LEAF_CONTAINER, containerId))
    val filled = count > 0
    EntityRwService.updateOne("FbContainer", Cq.idEq(containerId), mutableMapOf("filled" to filled))
  }

  /**
   * 在指定区域找空容器，找不到指定数量抛异常
   */
  fun findEmptyContainersInDistrict(districtId: String, num: Int): List<String> {
    val bins = EntityRwService.findMany(
      "FbBin",
      Cq.and(listOf(Cq.eq("district", districtId), Cq.notEmpty("container"))),
      FindOptions(sort = listOf("id")),
    )
    val foundContainerIds: MutableList<String> = ArrayList(num)
    ResLockService.resLock.withLock {
      for (bin in bins) {
        val containerId = bin["container"] as String?
        if (containerId.isNullOrBlank()) continue
        val containerEv = EntityRwService.findOne("FbContainer", Cq.idEq(containerId))
        if (containerEv?.get("filled") != true && containerEv?.get("locked") != true) foundContainerIds += containerId
        if (foundContainerIds.size == num) break
      }
      if (foundContainerIds.size < num) throw BzError("errBzNoEnoughEmptyContainer", num, foundContainerIds.size)
      EntityRwService.updateMany("FbContainer", Cq.include("id", foundContainerIds), mutableMapOf("locked" to true))
    }
    return foundContainerIds
  }

  /**
   * 查所有子容器 ID
   *
   * 注意：防循环导致的死锁
   */
  fun findAllChildContainerIds(cId: String): List<String> {
    val r = mutableSetOf(cId)
    findChildContainers(cId, r)
    return r.toList()
  }

  private fun findChildContainers(cId: String, l: MutableSet<String>) {
    val children = EntityRwService.findMany("FbContainer", Cq.eq("pContainer", cId))
    for (child in children) {
      val childId = EntityHelper.mustGetId(child)
      if (childId in l) continue
      l.add(childId)
      findChildContainers(childId, l)
    }
  }

  fun findTopContainer(cId: String): EntityValue {
    val ev = EntityRwService.findOneById("FbContainer", cId) ?: throw BzError("errContainerNotFound", cId)
    val pId = ev["pContainer"] as String?
    return if (pId.isNullOrBlank()) ev else findTopContainer(pId)
  }

  fun findTopContainerId(cId: String): String = EntityHelper.mustGetId(findTopContainer(cId))
}