package com.seer.trick.bz.handler

import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.I18N.lo
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.HttpServerManager.noAuth
import com.seer.trick.base.http.getReqBody
import com.seer.trick.bz.wms.BinOverviewService
import io.javalin.http.Context
import org.apache.commons.lang3.StringUtils
import kotlin.math.max

object BinHandler {

  fun registerHandlers() {
    val c = Handlers("api/wms")
    c.post("batch-create-bins", ::batchCreateBins, auth())
    c.get("bin-overview", ::getBinOverview, noAuth())
  }

  private fun batchCreateBins(ctx: Context) {
    val req: BatchCreateBinReq = ctx.getReqBody()
    // 校验
    // 1: 正整数 2: 停用 开始=结束
    checkBatchBinRequestParams(req)

    val bins: MutableList<EntityValue> = ArrayList()
    val duplicatedCountSet: MutableSet<String> = mutableSetOf()
    val list = req.editNoList.filter {
      (!it.disabled && (it.cardType != BinsAttribute.DEPTH || req.depthOn))
    }
    val listDisable = req.editNoList.filter {
      (it.disabled && (it.cardType != BinsAttribute.DEPTH || req.depthOn))
    }
    var row: Int? = null
    var col: Int? = null
    var layer: Int? = null
    var depth: Int? = null
    for (binCard in listDisable) {
      when (binCard.cardType) {
        BinsAttribute.ROW -> row = req.rowStart
        BinsAttribute.COLUMN -> col = req.columnStart
        BinsAttribute.LAYER -> layer = req.layerStart
        BinsAttribute.DEPTH -> depth = req.depthStart
      }
    }

    val existedIds: Set<String> =
      EntityRwService.findMany("FbBin", Cq.all(), FindOptions(projection = listOf("id")))
        .map(EntityHelper::mustGetId).toSet()
    generateBins(
      list, 0, "", bins, duplicatedCountSet, existedIds, req,
      row, col, layer, depth,
    )

//    val rowNoWidth = max(req.rowNoWidth, req.rowEnd.toString().length)
//    val columnNoWidth = max(req.columnNoWidth, req.columnEnd.toString().length)
//    val layerNoWidth = max(req.layerNoWidth, req.layerEnd.toString().length)
//    val depthNoWidth = max(req.depthNoWidth, req.depthNoWidth.toString().length)
//
//    val bins: MutableList<EntityValue> = ArrayList()
//
//    val existedIds: Set<String> =
//      EntityRwService.findMany("FbBin", Cq.all(), FindOptions(projection = listOf("id")))
//        .map(EntityHelper::mustGetId).toSet()
//
//    var duplicatedCount = 0
//
//    for (row in req.rowStart..req.rowEnd) {
//      val rowNo = StringUtils.leftPad(row.toString(), rowNoWidth, "0")
//      for (col in req.columnStart..req.columnEnd) {
//        val colNo = StringUtils.leftPad(col.toString(), columnNoWidth, "0")
//        for (layer in req.layerStart..req.layerEnd) {
//          val layerNo = StringUtils.leftPad(layer.toString(), layerNoWidth, "0")
//          if (req.depthOn) {
//            for (depth in req.depthStart..req.depthEnd) {
//              val depthNo = StringUtils.leftPad(depth.toString(), depthNoWidth, "0")
//              val id = "${req.prefix}$rowNo-$colNo-$layerNo-$depthNo"
//              if (existedIds.contains(id)) {
//                ++duplicatedCount
//                continue
//              }
//              bins += buildBin(id, row, col, layer, depth, req)
//            }
//          } else {
//            val id = "${req.prefix}$rowNo-$colNo-$layerNo"
//            if (existedIds.contains(id)) {
//              ++duplicatedCount
//              continue
//            }
//            bins += buildBin(id, row, col, layer, 1, req)
//          }
//        }
//      }
//    }

    EntityRwService.createMany("FbBin", bins)
    ctx.json(mapOf("duplicatedCount" to duplicatedCountSet.size, "createdCount" to bins.size))
  }

  // 校验参数 1: 正整数 2: 停用 开始=结束
  private fun checkBatchBinRequestParams(req: BatchCreateBinReq) {
    if (req.rowStart > req.rowEnd) throw BzError(lo("errRowStartGtRowEnd"))
    if (req.columnStart > req.columnEnd) throw BzError(lo("errColumnStartGtColumnEnd"))
    if (req.layerStart > req.layerEnd) throw BzError(lo("errLayerStartGtLayerEnd"))
    if (req.depthOn && req.depthStart > req.depthEnd) throw BzError(lo("errDepthStartGtDepthEnd"))
    // 校验参数必须时正整数
    fun Int.requirePositiveInt(errorKey: String) {
      require(this > 0) { throw BzError(lo(errorKey)) }
    }

    req.rowStart.requirePositiveInt("errRowStartMustPosInt")
    req.rowEnd.requirePositiveInt("errRowEndMustPosInt")
    req.columnStart.requirePositiveInt("errColumnStartMustPosInt")
    req.columnEnd.requirePositiveInt("errColumnEndMustPosInt")
    req.layerStart.requirePositiveInt("errLayerStartMustPosInt")
    req.layerEnd.requirePositiveInt("errLayerEndMustPosInt")
    if (req.depthOn) {
      req.depthStart.requirePositiveInt("errDepthStartMustPosInt")
      req.depthEnd.requirePositiveInt("errDepthEndMustPosInt")
    }
    // 校验停用时 开始 == 结束
    req.editNoList.forEach { binCard ->
      if (binCard.disabled) {
        when (binCard.cardType) {
          BinsAttribute.ROW -> require(req.rowStart == req.rowEnd) { throw BzError(lo("errRowStartEQRowEnd")) }
          BinsAttribute.COLUMN -> require(req.columnStart == req.columnEnd) {
            throw BzError(lo("errColumnStartEQColumnEnd"))
          }

          BinsAttribute.LAYER -> require(req.layerStart == req.layerEnd) {
            throw BzError(lo("errLayerStartEQLayerEnd"))
          }

          BinsAttribute.DEPTH -> if (req.depthOn) {
            require(req.depthStart == req.depthEnd) {
              throw BzError(lo("errDepthStartEQDepthEnd"))
            }
          }
        }
      }
    }
  }

  /**
   * 根据配置的排列层深生成 binId
   */
  private fun generateBins(
    items: List<BinCard>,
    index: Int,
    idTmp: String,
    result: MutableList<EntityValue>,
    duplicatedCount: MutableSet<String>,
    existedIds: Set<String>,
    req: BatchCreateBinReq,
    row: Int?,
    col: Int?,
    layer: Int?,
    depth: Int?,
  ) {
    val binCard = items[index]

    // 判断这个配置是排、列、层、深 获取它的的位数限制
    val (start, end, noWidth) = when (binCard.cardType) {
      BinsAttribute.ROW -> Triple(req.rowStart, req.rowEnd, max(binCard.noWidth, req.rowEnd.toString().length))
      BinsAttribute.COLUMN -> Triple(
        req.columnStart,
        req.columnEnd,
        max(binCard.noWidth, req.columnEnd.toString().length),
      )

      BinsAttribute.LAYER -> Triple(req.layerStart, req.layerEnd, max(binCard.noWidth, req.layerEnd.toString().length))
      BinsAttribute.DEPTH -> Triple(req.depthStart, req.depthEnd, max(binCard.noWidth, req.depthEnd.toString().length))
    }

    val prefixTrimmed = binCard.prefix.trim()
    val suffixTrimmed = binCard.suffix.trim()
    for (s in start..end) {
      val r = StringUtils.leftPad(s.toString(), noWidth, "0")
      val binId = "$idTmp$prefixTrimmed$r$suffixTrimmed"
      // 判断这个配置是排/列/层/深，获取当前排/列/层/深的值
      val (rowTmp, colTmp, layerTmp, depthTmp) = when (binCard.cardType) {
        BinsAttribute.ROW -> Quadruple(s, col, layer, depth)
        BinsAttribute.COLUMN -> Quadruple(row, s, layer, depth)
        BinsAttribute.LAYER -> Quadruple(row, col, s, depth)
        BinsAttribute.DEPTH -> Quadruple(row, col, layer, s)
      }
      // 判断是配置数组最后一个如果不是数组最后一个配置不生成库位继续向下遍历直到最后一个才生成库位id
      if (index == items.size - 1) {
        if (existedIds.contains(binId)) {
          duplicatedCount += binId
          continue
        }
        result += buildBin(binId, rowTmp, colTmp, layerTmp, depthTmp, req)
      } else {
        generateBins(
          items, index + 1, binId, result, duplicatedCount,
          existedIds, req, rowTmp, colTmp, layerTmp, depthTmp,
        )
      }
    }
  }

  data class Quadruple<A, B, C, D>(val first: A, val second: B, val third: C, val fourth: D)

//  private fun generateBins(
//    items: List<BinCard>,
//    index: Int,
//    idTmp: String,
//    result: MutableList<EntityValue>,
//    req: BatchCreateBinReq,
//    row: Int?,
//    col: Int?,
//    layer: Int?,
//    depth: Int?,
//  ) {
//    val binCard = items[index]
//    var start = 0
//    var end = 0
//    val noWidth = if (binCard.cardType == BinsAttribute.ROW) {
//      start = req.rowStart
//      end = req.rowEnd
//      max(binCard.noWidth, req.rowEnd.toString().length)
//    } else if (binCard.cardType == BinsAttribute.COLUMN) {
//      start = req.columnStart
//      end = req.columnEnd
//      max(binCard.noWidth, req.columnEnd.toString().length)
//    } else if (binCard.cardType == BinsAttribute.LAYER) {
//      start = req.layerStart
//      end = req.layerEnd
//      max(binCard.noWidth, req.layerEnd.toString().length)
//    } else if (binCard.cardType == BinsAttribute.DEPTH) {
//      start = req.depthStart
//      end = req.depthEnd
//      max(binCard.noWidth, req.depthOn.toString().length)
//    } else {
//      throw BzError("未知异常")
//    }
//    for (s in start..end) {
//      val r = StringUtils.leftPad(s.toString(), noWidth, "0")
//      val binId = "$idTmp${items[index].prefix.trim()}$r${items[index].suffix.trim()}"
//      var rowTmp = row
//      var colTmp = col
//      var layerTmp = layer
//      var depthTmp = depth
//      if (binCard.cardType == BinsAttribute.ROW) {
//        rowTmp = s
//      }
//      if (binCard.cardType == BinsAttribute.COLUMN) {
//        colTmp = s
//      }
//      if (binCard.cardType == BinsAttribute.LAYER) {
//        layerTmp = s
//      }
//      if (binCard.cardType == BinsAttribute.DEPTH) {
//        depthTmp = s
//      }
//      if (index == items.size - 1) {
//        result += buildBin(binId, rowTmp, colTmp, layerTmp, depthTmp, req)
//      } else {
//        generateBins(items, index + 1, binId, result, req, rowTmp, colTmp, layerTmp, depthTmp)
//      }
//    }
//  }

  private fun buildBin(
    id: String,
    row: Int?,
    col: Int?,
    layer: Int?,
    depth: Int?,
    req: BatchCreateBinReq,
  ): EntityValue = mutableMapOf(
    "id" to id,
    "btDisabled" to false,
    "occupied" to false,
    "warehouse" to req.warehouse,
    "district" to req.district,
    "rack" to req.rack,
    "assemblyLine" to req.assemblyLine,
    "row" to row,
    "column" to col,
    "layer" to layer,
    "depth" to depth,
  )

  private fun getBinOverview(ctx: Context) {
    ctx.json(BinOverviewService.list())
  }
}

data class BatchCreateBinReq(
  val warehouse: String? = null, // 仓库
  val district: String? = null, // 库区
  val rack: String? = null, // 货架
  val assemblyLine: String? = null, // 产线
  // val workStation: String? = null,
//  val prefix: String? = null,
//  val rowNoWidth: Int = 0,
  val rowStart: Int = 0, // 排开始
  val rowEnd: Int = 0, // 排结束
//  val columnNoWidth: Int = 0,
  val columnStart: Int = 0, // 列开始
  val columnEnd: Int = 0, // 列结束
//  val layerNoWidth: Int = 0,
  val layerStart: Int = 0, // 层开始
  val layerEnd: Int = 0, // 层结束
  val depthOn: Boolean = false, // 是否启用深度
//  val depthNoWidth: Int = 0,
  val depthStart: Int = 0, // 深开始
  val depthEnd: Int = 0, // 深结束
  val editNoList: List<BinCard> = emptyList(),
)

data class BinCard(
  val cardType: BinsAttribute,
  val noWidth: Int = 0,
  val prefix: String = "",
  val suffix: String = "",
  val disabled: Boolean = true,
)

enum class BinsAttribute {
  ROW,
  COLUMN,
  LAYER,
  DEPTH,
}