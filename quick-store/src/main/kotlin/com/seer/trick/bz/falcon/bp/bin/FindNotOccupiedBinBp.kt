package com.seer.trick.bz.falcon.bp.bin

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.ComplexQuery
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.bz.wms.CoreWmsRelationService
import com.seer.trick.bz.wms.FindOneNotOccupiedUnlockedBinInDistrictsOptions
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.StringHelper

/**
 * 在指定库区内寻找一个空库位
 */
class FindNotOccupiedBinBp : AbstractBp() {

  override fun process() {
    val districtIdStr = mustGetBlockInputParam("districtIds") as String
    val districtIds = StringHelper.splitTrim(districtIdStr, ",")
    val keepTrying = getBlockInputParamAsBool("keepTrying") as Boolean? ?: false

    val cqStr = getBlockInputParam("cq") as String?
    val cq: ComplexQuery? = if (cqStr.isNullOrBlank()) {
      null
    } else {
      JsonHelper.mapper.readValue(cqStr, jacksonTypeRef())
    }
    val sortStr = getBlockInputParam("sort") as String?
    val sort: List<String>? = if (sortStr.isNullOrBlank()) {
      null
    } else {
      JsonHelper.mapper.readValue(sortStr, jacksonTypeRef())
    }

    val binEv = CoreWmsRelationService.findOneNotOccupiedUnlockedBinInDistricts(
      districtIds,
      FindOneNotOccupiedUnlockedBinInDistrictsOptions(cq, sort, keepTrying),
    )
    if (binEv != null) {
      val binId = EntityHelper.mustGetId(binEv)
      logger.info("在库区找空库位，找到库位: $binId。库区列表=$districtIdStr")
      addResource("FindNotOccupiedBin", binId, mapOf("binId" to binId))
      setBlockOutputParams(mapOf("found" to true, "binId" to binId))
    } else {
      logger.info("在库区找空库位，没找到。库区列表=$districtIdStr")
      setBlockOutputParams(mapOf("found" to false, "binId" to null))
    }
  }

  companion object {
    val def = BlockDef(
      FindNotOccupiedBinBp::class.simpleName!!,
      color = "#5294E2",
      inputParams = listOf(
        BlockInputParamDef(
          "districtIds",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.DistrictId),
        ),
        BlockInputParamDef("cq", BlockParamType.String, false),
        BlockInputParamDef("sort", BlockParamType.String, false),
        BlockInputParamDef("keepTrying", BlockParamType.Boolean),
      ),
      outputParams = listOf(
        BlockOutputParamDef("found", BlockParamType.Boolean),
        BlockOutputParamDef(
          "binId",
          BlockParamType.String,
          objectTypes = listOf(ParamObjectType.BinId),
        ),
      ),
    )
  }
}