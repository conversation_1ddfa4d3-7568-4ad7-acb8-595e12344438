package com.seer.trick.bz.falcon.bp.container

import com.seer.trick.Cq
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.reslock.ResLockService
import com.seer.trick.base.soc.SocService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*
import com.seer.trick.helper.BoolHelper
import kotlin.concurrent.withLock

class FindEmptyContainerBp : AbstractBp() {
  
  override fun process() {
    val districtIdStr = mustGetBlockInputParam(ipDistrictIds.name) as String
    val districtIds = districtIdStr.split(",")
    val sortStr = getBlockInputParam(ipSortStr.name) as String?
    val keepTrying = getBlockInputParamAsBool(ipKeepTrying.name)
    
    var counter = 1
    
    try {
      do {
        // TODO 排序规则
        val qList = listOf(Cq.include("district", districtIds), Cq.ne("btDisabled", true))
        val o = if (sortStr == null) FindOptions() else {
          val st = sortStr.split(",")
          FindOptions(sort = st)
        }
        val bins = EntityRwService.findMany("FbBin", Cq.and(qList), o)
        val cbMap = bins.filter { !(it["container"] as String?).isNullOrBlank() }
          .associate { it["container"] as String to it["id"] as String }

        ResLockService.resLock.withLock {
          // TODO 优化查询
          for ((cid, bid) in cbMap.entries) {
            val container = EntityRwService.findOne(
              "FbContainer", Cq.and(listOf(Cq.idEq(cid), Cq.ne("filled", true)))
            ) ?: continue
            // TODO 兼容堆栈、队列式库区
            if (!(BoolHelper.anyToBool(container["locked"]))) {
              // TODO lock container
              EntityRwService.updateMany("FbContainer", Cq.eq("id", cid), mutableMapOf("locked" to true))
              setBlockOutputParams(mapOf(opFound.name to true, opBinId.name to bid, opContainerId.name to cid))
              return
            }
          }
        }
        
        SocService.updateNode(
          "业务", "Bz:FindEmptyContainerBp:${taskRuntime.taskId}", "找不到空容器",
          "找不到空容器。猎鹰任务=${taskRuntime.taskId}。库区=$districtIdStr",
        )
        Thread.sleep(600)
        ++counter
      } while (keepTrying)
      
      setBlockOutputParams(mapOf(opFound.name to false, opBinId.name to null, opContainerId.name to null))
    } finally {
      SocService.removeNode("Bz:FindEmptyContainerBp:${taskRuntime.taskId}")
    }
  }
  
  companion object {
    
    private val ipDistrictIds = BlockInputParamDef(
      "districtIds", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.DistrictId)
    )
    private val ipSortStr = BlockInputParamDef("sort", BlockParamType.String)
    private val ipKeepTrying = BlockInputParamDef("keepTrying", BlockParamType.Boolean)
    
    private val opFound = BlockOutputParamDef("found", BlockParamType.Boolean)
    private val opBinId = BlockOutputParamDef(
      "binId", BlockParamType.String, objectTypes = listOf(ParamObjectType.BinId)
    )
    private val opContainerId = BlockOutputParamDef(
      "containerId", BlockParamType.String, objectTypes = listOf(ParamObjectType.ContainerId)
    )
    
    val def = BlockDef(
      FindEmptyContainerBp::class.simpleName!!,
      color = "#C7DCA7",
      inputParams = listOf(ipDistrictIds, ipSortStr, ipKeepTrying),
      outputParams = listOf(opFound, opBinId, opContainerId),
    )
  }
}