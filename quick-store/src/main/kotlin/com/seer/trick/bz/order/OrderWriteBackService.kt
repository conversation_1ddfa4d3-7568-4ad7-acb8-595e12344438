package com.seer.trick.bz.order

import com.seer.trick.Cq
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.helper.NumHelper
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * 回写上游单据
 */
object OrderWriteBackService {

  private val logger: Logger = LoggerFactory.getLogger(this::class.java)

  /**
   * 单据反写
   * 幂等的。
   */
  fun tryWriteBack(downEm: EntityMeta, downOrderIds: List<String>) {
    // 是否是单据
    val downOrderConfig = downEm.orderConfig
    if (downOrderConfig?.enabled != true) return

    // 没有需要回写的上游
    if (downOrderConfig.upOrderConfig.items.isEmpty()) return

    val downOrderEvList = EntityRwService.findMany(downEm.name, Cq.include("id", downOrderIds))
    val sources: MutableMap<String, WriteBackSource> = HashMap()
    for (downOrderEv in downOrderEvList) {
      val upOrderName = downOrderEv[FieldMeta.FIELD_SOURCE_ORDER_NAME] as String? ?: continue
      val upOrderId = downOrderEv[FieldMeta.FIELD_SOURCE_ORDER_ID] as String? ?: continue
      sources[upOrderName + upOrderId] = WriteBackSource(upOrderName, upOrderId)
    }

    for (s in sources.values) {
      writeBackOneSource(s.upOrderName, s.upOrderId, downEm)
    }

    // 多级反写
    val upOrderGroups = sources.values.groupBy { it.upOrderName }
    for ((upOrderName, orders) in upOrderGroups) {
      val ids = orders.map { it.upOrderId }
      val em = BaseCenter.mustGetEntityMeta(upOrderName)
      tryWriteBack(em, ids)
    }
  }

  private fun writeBackOneSource(upOrderName: String, upOrderId: String, downEm: EntityMeta) {
    logger.info("计算反写，上游单据=$upOrderName/$upOrderId")
    val upOrderEm = BaseCenter.mustGetEntityMeta(upOrderName)
    val upLineEntityName = EntityHelper.mustGetLineEntityName(upOrderEm)
    val upOrderConfigItem = UpOrderService.mustGetUpOrderConfigItem(downEm, upOrderName)
    if (upOrderConfigItem.lineWriteBackMapping.isEmpty()) {
      logger.warn("下游单据未配置反写字段，下游单据=${downEm.name}，上游单据=${upOrderName}")
      return
    }

    // TODO 支持下推到多个业务对象
    // 找目前所有下推
    val downOrderEvList = EntityRwService.findMany(
      downEm.name, Cq.eq(FieldMeta.FIELD_SOURCE_ORDER_ID, upOrderId),
    )

    // 源单单行
    val upLines = EntityRwService.findMany(
      upLineEntityName,
      Cq.eq(FieldMeta.FIELD_PARENT_ID, upOrderId),
      FindOptions(sort = listOf(FieldMeta.FIELD_LINE_NO))
    )

    val updateLines: List<EntityValue> = upLines.map { mutableMapOf("id" to EntityHelper.mustGetId(it)) }

    for (downOrderEv in downOrderEvList) {
      val downOrderState = downOrderEv[FieldMeta.FIELD_ORDER_STATE] as String? ?: continue
      if (upOrderConfigItem.writeBackStates.indexOf(downOrderState) < 0) continue

      val downLines = EntityHelper.mustGetLines(downOrderEv, FieldMeta.FIELD_LINES)
      for (downLine in downLines) {
        if (downLine[FieldMeta.FIELD_SOURCE_ORDER_ID] != upOrderId) continue

        val upLineNo = NumHelper.anyToInt(downLine[FieldMeta.FIELD_SOURCE_LINE_NO]) ?: continue
        val upLineIndex = upLineNo - 1
        if (upLineIndex < 0 || upLineIndex >= upLines.size) continue

        for (m in upOrderConfigItem.lineWriteBackMapping) {
          val q = NumHelper.anyToDouble(downLine[m.downField]) ?: 0.0
          val old = NumHelper.anyToDouble(updateLines[upLineIndex][m.upField]) ?: 0.0
          updateLines[upLineIndex][m.upField] = old + q
        }
      }
    }

    for ((li, upLine) in updateLines.withIndex()) {
      if (upLine.size <= 1) continue
      logger.info("更新反写单行 $upOrderName/$upOrderId/${li + 1} $upLine")
      EntityRwService.updateOne(upLineEntityName, Cq.idEq(EntityHelper.mustGetId(upLine)), upLine)
    }

    // 计算汇总字段
    val upOrderEv = EntityRwService.findOne(upOrderName, Cq.idEq(upOrderId))
    if (upOrderEv != null) {
      val update = EntityRwService.calcSumFieldsUpdate(upOrderEv, upOrderEm)
      if (update.isNotEmpty()) {
        EntityRwService.updateOne(upOrderName, Cq.idEq(upOrderId), update)
      }
    }
  }
}

data class WriteBackSource(
  val upOrderName: String,
  val upOrderId: String
)
