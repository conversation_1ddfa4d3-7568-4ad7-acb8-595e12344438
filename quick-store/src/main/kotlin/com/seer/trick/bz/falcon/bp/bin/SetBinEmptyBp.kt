// package com.seer.trick.bz.falcon.bp.bin
//
// import com.seer.trick.Cq
//
// import com.seer.trick.base.entity.EntityValue
// import com.seer.trick.base.entity.service.EntityRwService
// import com.seer.trick.falcon.bp.AbstractBp
// import com.seer.trick.falcon.domain.BlockDef
// import com.seer.trick.falcon.domain.BlockInputParamDef
// import com.seer.trick.falcon.domain.BlockParamType
//
// @Deprecated("用 SetBinNotOccupiedBp") 先注释这个类，等确定没人用这个Bp在删除
// class SetBinEmptyBp : AbstractBp() {
//
//  override fun process() {
//    val binId = mustGetBlockInputParam(ipBinId.name)
//    val notOccupied = getBlockInputParamAsBool(ipNotOccupied.name)
//    val unlock = getBlockInputParamAsBool(ipUnlock.name)
//    val noContainer = getBlockInputParamAsBool(ipNoContainer.name)
//
//    val update: EntityValue = mutableMapOf()
//    if (notOccupied) update["occupied"] = false
//    if (unlock) update["locked"] = false
//    if (noContainer) update["container"] = ""
//
//    // TODO 锁？
//    logger.info("SetBinEmptyBp 设置容器非空，id=$binId，update=$update")
//    EntityRwService.updateOne("FbBin", Cq.eq("id", binId), update)
//    addRelatedObject("FbBin", binId as String, null)
//  }
//
//  companion object {
//    val ipBinId = BlockInputParamDef("binId", BlockParamType.String, true)
//    val ipNotOccupied = BlockInputParamDef("notOccupied", BlockParamType.Boolean)
//    val ipUnlock = BlockInputParamDef("unlock", BlockParamType.Boolean)
//    val ipNoContainer = BlockInputParamDef("noContainer", BlockParamType.Boolean)
//
//    val def = BlockDef(
//      SetBinEmptyBp::class.simpleName!!,
//      color = "#E19898",
//      inputParams = listOf(
//        ipBinId,
//        ipNotOccupied,
//        ipUnlock,
//        ipNoContainer,
//      ),
//    )
//  }
// }