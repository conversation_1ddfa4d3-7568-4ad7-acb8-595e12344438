package com.seer.trick.bz.wms

import com.seer.trick.BzError
import com.seer.trick.base.concurrent.BaseConcurrentCenter.highTimeSensitiveExecutor
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.change.EntityChange
import com.seer.trick.base.entity.service.extension.EntityServiceExtension
import com.seer.trick.bz.wms.inv.CreateInvService
import com.seer.trick.helper.submitCatch
import org.slf4j.LoggerFactory

/**
 * 手工上架单
 */
object DirectPutawayOrderService : EntityServiceExtension() {

  private val logger = LoggerFactory.getLogger(this::class.java)

  override fun beforeCreating(em: EntityMeta, evList: List<EntityValue>): List<String>? {
    if (em.name != "FbDirectPutawayOrder") return null

    for (ev in evList) {
      if (ev[FieldMeta.FIELD_ORDER_STATE] != "Submitted") continue
      checkOrder(ev)
    }

    return null
  }

  override fun afterCreating(em: EntityMeta, evList: List<EntityValue>) {
    if (em.name != "FbDirectPutawayOrder") return

    for (ev in evList) {
      if (ev[FieldMeta.FIELD_ORDER_STATE] != "Submitted") continue
      highTimeSensitiveExecutor.submitCatch("处理手工上架单", logger) {
        createInv(ev)
      }
    }
  }

  override fun beforeUpdating(em: EntityMeta, ids: List<String>, update: EntityValue): Long? {
    if (em.name != "FbDirectPutawayOrder") return null

    for (id in ids) {
      val oldEv = EntityRwService.findOneById("FbDirectPutawayOrder", id) ?: continue
      if (!(oldEv[FieldMeta.FIELD_ORDER_STATE] != "Submitted" && update[FieldMeta.FIELD_ORDER_STATE] == "Submitted")
      ) continue
      val newEv = EntityHelper.clone(oldEv)
      newEv.putAll(EntityHelper.clone(update))
      checkOrder(newEv)
    }

    return null
  }

  override fun afterUpdating(em: EntityMeta, changes: List<EntityChange>) {
    for (c in changes) {
      val oldEv = c.oldValue ?: continue
      val newEv = c.newValue ?: continue
      if (!(oldEv[FieldMeta.FIELD_ORDER_STATE] != "Submitted" && newEv[FieldMeta.FIELD_ORDER_STATE] == "Submitted")
      ) continue
      createInv(newEv)
    }
  }

  private fun checkOrder(order: EntityValue) {
    val binId = order["bin"] as String?
    if (binId.isNullOrBlank())
      throw BzError("errBzError", "库位必填")

    val binEv = EntityRwService.findOneById("FbBin", binId)
      ?: throw BzError("errBzError", "库位 $binId 不存在")

    if (binEv["locked"] == true)
      throw BzError("errBzError", "库位 $binId 正在用于其他业务（被锁定）")

    val oldContainer = binEv["container"] as String?
    if (!oldContainer.isNullOrBlank())
      throw BzError("errBzError", "库位 $binId 上已有容器 $oldContainer")

    if (binEv["occupied"] == true)
      throw BzError("errBzError", "库位 $binId 已被占用")

    val containerId = order["container"] as String?
    if (containerId.isNullOrBlank())
      throw BzError("errBzError", "容器必填")

    val containerEv = EntityRwService.findOneById("FbContainer", containerId)
      ?: throw BzError("errBzError", "容器 $containerId 不存在")

    if (containerEv["locked"] == true)
      throw BzError("errBzError", "容器 $containerId 正在用于其他业务（被锁定）")

    if (containerEv["filled"] == true)
      throw BzError("errBzError", "容器 $containerId 内有货")
  }

  private fun createInv(order: EntityValue) {
    val orderId = EntityHelper.mustGetId(order)
    logger.info("处理手工上架单 $orderId $order")

    val binId = order["bin"] as String
    val containerId = order["container"] as String

    // 绑定容器、库位
    CoreWmsRelationService.bindContainerBin(containerId, binId)

    val lines = EntityHelper.getLines(order, FieldMeta.FIELD_LINES) ?: return
    for (line in lines) {
      line.remove("id")
      line["bin"] = binId
      line[FieldMeta.FIELD_LEAF_CONTAINER] = containerId
    }
    CreateInvService.fixCreateInvLayout(lines)

    ContainerService.fixContainerFilled(containerId)
  }

}