package com.seer.trick.bz.wms.inv

import com.seer.trick.Cq
import com.seer.trick.base.concurrent.BaseConcurrentCenter.highTimeSensitiveExecutor
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.extension.EntityServiceExtension
import com.seer.trick.helper.NumHelper

/**
 * FbInvLayout 变更触发的实体
 *
 */
object InvLayoutExtensionService : EntityServiceExtension() {

  override fun beforeCreating(em: EntityMeta, evList: List<EntityValue>): List<String>? {
    for (ev in evList) {
      highTimeSensitiveExecutor.submit { recordInvChange(InvChangeType.CREATE, null, ev) }
    }
    return null
  }

  override fun beforeUpdating(em: EntityMeta, ids: List<String>, update: EntityValue): Long? {
    val oldEvList = EntityRwService.findMany(em.name, Cq.include("id", ids))
    for (oldEv in oldEvList) {
      highTimeSensitiveExecutor.submit { recordInvChange(InvChangeType.UPDATE, oldEv, update) }
    }
    return null
  }

  override fun beforeRemoving(em: EntityMeta, ids: List<String>): Long? {
    val oldEvList = EntityRwService.findMany(em.name, Cq.include("id", ids))
    for (oldEv in oldEvList) {
      highTimeSensitiveExecutor.submit { recordInvChange(InvChangeType.REMOVE, oldEv, null) }
    }
    return null
  }

  /**
   * 填充关联业务对象的字段，如从库位取排、列、层、工位、货架、巷道，从容器取最外层容器类型、最内层容器类型
   */
  private fun fillRefEntityField(old: EntityValue?, new: EntityValue) {
    // btMaterial -> btMaterialCategory -> btMaterialTopCategory
    // topContainer -> topContainerType
    // leafContainer -> leafContainerType
    // bin -> district, warehouse, rack, channel, workSite, assemblyLine,
    // TODO robotBin
  }

  /**
   * 记录库存变更
   *
   * TODO 暂时只关注 qty 变化量
   */
  private fun recordInvChange(type: InvChangeType, old: EntityValue?, new: EntityValue?) {
    when (type) {
      InvChangeType.CREATE -> {
        // 创建新的库存明细 生成的库存变更
        if (new == null) return
        createInvChangeRecord(new)
      }

      InvChangeType.UPDATE -> {
        // 更新库存明细 生成的库存变更
        if (old == null || new == null) return
        val qty = (NumHelper.anyToDouble(new["qty"]) ?: 0.0) - (NumHelper.anyToDouble(old["qty"]) ?: 0.0)
        old["qty"] = qty
        old.remove("id")
        createInvChangeRecord(old)
      }

      InvChangeType.REMOVE -> {
        // 删除库存明细 生成的库存变更
        if (old == null) return
        old["qty"] = -(NumHelper.anyToDouble(old["qty"]) ?: 0.0)
        createInvChangeRecord(old)
      }
    }
  }

  // 创建 FbInvChange 记录
  private fun createInvChangeRecord(ev: EntityValue) {
    val e: EntityValue = ev.toMutableMap()
    EntityRwService.createOne("FbInvChange", e)
  }
}

enum class InvChangeType {
  CREATE, // 创建库存明细
  UPDATE, // 更新库存明细
  REMOVE, // 删除库存明细
}