package com.seer.trick.bz.falcon.bp.bin


import com.seer.trick.bz.wms.CoreWmsRelationService
import com.seer.trick.bz.wms.SetBinNotOccupiedOption
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.falcon.domain.ParamObjectType

class SetBinNotOccupiedBp : AbstractBp() {

  override fun process() {
    val binId = mustGetBlockInputParam("binId") as String
    val unlockAfter = getBlockInputParamAsBool("unlockAfter")

    CoreWmsRelationService.setBinNotOccupied(binId, SetBinNotOccupiedOption(unlockAfter = unlockAfter))
    addRelatedObject("FbBin", binId, null)
  }

  companion object {

    val def = BlockDef(
      SetBinNotOccupiedBp::class.simpleName!!,
      color = "#f8fdcb",
      inputParams = listOf(
        BlockInputParamDef("binId", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.BinId)),
        BlockInputParamDef("unlockAfter", BlockParamType.Boolean),
      ),
    )
  }
}