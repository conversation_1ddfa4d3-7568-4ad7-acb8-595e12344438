package com.seer.trick.bz.wms.resv

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.ComplexQuery
import com.seer.trick.Cq

import com.seer.trick.base.concurrent.BaseConcurrentCenter
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta.Companion.FIELD_VERSION
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.reslock.ResLockService
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.submitLongRun
import org.slf4j.LoggerFactory
import kotlin.concurrent.withLock

/**
 * 资源分配中心
 *
 * TODO 如何设计重分配机制，比如江西铜业的空轴存储区
 */
object ReserveCenter {
  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 等待分配的资源预定单
   *
   * 每条记录只能预定 1 个资源，如 1 个容器、1 个库位。
   * 如果有业务单请求出 100 个空容器，则创建 100 条资源预定记录，记录的 sourceOrderId 相同。
   *
   * 如果 100 个空容器，要分 20 批次出，则需要用的 ResvRecord.group 分组，组内的资源分配是原子的，全成功或者全失败。
   */
  private val reservingList = mutableListOf<ReserveRecord>()

  /**
   * 预定某种资源，供业务代码、脚本使用
   * 阻塞至分配完成
   *
   * 幂等
   * 业务上传一个唯一 ID，比如寻空库位猎鹰任务传 “猎鹰任务 ID + 块 ID”
   * ID 已存在，则取指定实体记录的结果
   * 如果资源已分配，则返回分配结果，否则等待至分配完成
   */
  fun reserve(resv: ReserveRecord): String {
    // TODO 生成 id
    resv.id = IdHelper.oidStr()
    // 先持久化后放到内存
    ResvRepo.createRecord(resv)
    reservingList.add(resv)

    // TODO noWait 也不立即触发分配，多等 1 s 影响不大
    return resv.id!!
  }

  fun init() {
    
    cleanAssigningRecords()
    reservingList.addAll(ResvRepo.loadAll())
    // reloadUnfinishedRecords()

    // 每秒处理一轮，每轮尝试为所有的预定单分配资源
    BaseConcurrentCenter.lowTimeSensitiveExecutor.submitLongRun("ReserveCenter", logger, { 1000L }, ::assign)
  }

  /**
   * TODO 清理所有执行中的 “预定资源记录”
   *
   * 放弃所有分配中（Assigning）的状态，会退到等待中，然后重新分配。Assigning 状态的 ResvRecord 全改为 Waiting，
   * 所有带 prepare == xxx 的资源都改为 prepare = null, btBzMark = null
   */
  private fun cleanAssigningRecords() {
    EntityRwService.updateMany(
      
      "FbBinInv",
      Cq.notEmpty("btPrepare"),
      mutableMapOf("btPrepare" to null, "btBzMark" to null, "btBzDesc" to null),
    )
    EntityRwService.updateMany(
      
      "FbInvLayout",
      Cq.notEmpty("btPrepare"),
      mutableMapOf("btPrepare" to null, "btBzMark" to null, "btBzDesc" to null),
    )
    EntityRwService.updateMany(
      
      "ResvRecord",
      Cq.eq("status", ResvStatus.Assigning),
      mutableMapOf("status" to ResvStatus.Waiting, "res" to null),
    )
  }

  /**
   * TODO 加载所有未完成的 “预定资源记录”
   *
   * 把所有非 Assigned、Canceled 状态的记录放到 reservingList 里
   */
  private fun reloadUnfinishedRecords() {
  }

  /**
   * 分配资源
   *
   * 按批次分配资源，批次优先级 = 批内 ResvRecord 最高的 priority
   * ------------------
   * 二阶段提交
   *
   * 第一阶段：寻找并更新资源的 btBzMark、prepare(=分组 key)
   *
   * 判断 “更新的资源数” 与 “待分配资源数” 是否相同，相同则提交，否则回滚
   *
   * 第二阶段提交：update prepare = null where prepare = 分组 key
   * 第二阶段回滚：update prepare = null, btBzMark = null where prepare = 分组 key
   *
   */
  private fun assign() {
    
    val gl = sortAndGroup() // 待分配的组

    for (g in gl) {
      // 第一阶段 分配资源
      val assignResult = g.records.mapNotNull { assignResource(it) }

      // 第二阶段
      if (assignResult.size == g.records.size) {
        // 第二阶段 提交
        submit(g)
      } else {
        // 第二阶段 回滚
        rollback(g)
      }
    }
  }

  /**
   * 对预定资源记录分组，并以组内最大 priority 作为组的 priority
   */
  private fun sortAndGroup(): List<ReserveRecordGroup> {
    val list1 = reservingList.filter { it.status == ResvStatus.Waiting }
      .groupBy { it.sourceOrderType + it.sourceOrderId + it.group }
    return list1.map {
      ReserveRecordGroup(
        it.key,
        it.value.maxOf { resv -> resv.priority },
        it.value.sortedByDescending { resv -> resv.priority },
        // null,
      )
    }.sortedByDescending { it.priority }
  }

  /**
   * 第一阶段：尝试分配资源
   */
  private fun assignResource(resv: ReserveRecord): String? {
    // resv.status = ResvStatus.Assigning
    // ResvRepo.updateStatus(resv)

    val r = ResLockService.resLock.withLock {
      when (resv.type) {
        ResvType.Bin -> assignBin(resv)
        ResvType.Container -> assignContainer(resv)
        ResvType.Inventory -> assignInventory(resv)
      }
    }

    return r
  }

  private fun assignBin(resv: ReserveRecord): String? {
    // Q：为什么不直接 update QsBinIv，然后根据 prepare == resv.toKey() 取出来锁定的资源，能立即锁住，不用管 version 是否变化
    // A：可能有顺序要求，update 时无法指定顺序，只能随机。M4 的项目中按照一定顺序锁定资源是高频需求，故先查后更新。查的时候能指定排序顺序

    val r = EntityRwService.findOne("FbBinInv", wrapQuery(resv), resv.o) ?: return null
    val binId = EntityHelper.mustGetId(r)

    val c = EntityRwService.updateOne(
      
      "FbBinInv",
      Cq.and(Cq.idEq(binId), Cq.eq(FIELD_VERSION, r[FIELD_VERSION])),
      mutableMapOf("btPrepare" to resv.toKey(), "btBzMark" to resv.btBzMark, "btBzDesc" to resv.btBzDesc),
    )
    if (c == 1L) {
      resv.status = ResvStatus.Assigning
      EntityRwService.updateOne(
        
        "ResvRecord",
        Cq.idEq(resv.id!!),
        mutableMapOf("status" to ResvStatus.Assigning, "res" to binId),
      )
    }

    return if (c == 1L) binId else null
  }

  private fun assignContainer(resv: ReserveRecord): String? {
    val r = EntityRwService.findOne("FbBinInv", wrapQuery(resv), resv.o) ?: return null
    val cId = r["topContainer"] as String? ?: return null // TODO 暂定：寻找容器只能找到机器人可搬运的容器，即最外层容器

    val c = EntityRwService.updateOne(
      
      "FbBinInv",
      Cq.and(Cq.idEq(cId), Cq.eq(FIELD_VERSION, r[FIELD_VERSION])),
      mutableMapOf("btPrepare" to resv.toKey(), "btBzMark" to resv.btBzMark, "btBzDesc" to resv.btBzDesc),
    )
    if (c == 1L) {
      resv.status = ResvStatus.Assigning
      EntityRwService.updateOne(
        
        "ResvRecord",
        Cq.idEq(resv.id!!),
        mutableMapOf("status" to ResvStatus.Assigning, "res" to cId),
      )
    }

    return if (c == 1L) cId else null
  }

  private fun assignInventory(resv: ReserveRecord): String? {
    // TODO
    return null
  }

  /**
   * 第二阶段：提交
   */
  private fun submit(g: ReserveRecordGroup) {
    // 提交库位库存
    EntityRwService.updateMany("FbBinInv", Cq.eq("btPrepare", g.key), mutableMapOf("btPrepare" to null))

    // 提交库存明细
    EntityRwService.updateMany("FbInvLayout", Cq.eq("btPrepare", g.key), mutableMapOf("btPrepare" to null))

    // 提交 ResvRecord 状态
    EntityRwService.updateMany("ResvRecord", Cq.eq("key", g.key), mutableMapOf("status" to ResvStatus.Assigned))
    g.records.map {
      it.status = ResvStatus.Assigned
      reservingList.remove(it)
    }
  }

  /**
   * 第二阶段：回滚
   */
  private fun rollback(g: ReserveRecordGroup) {
    // 回滚库位库存
    EntityRwService.updateMany(
      
      "FbBinInv",
      Cq.eq("btPrepare", g.key),
      mutableMapOf("btPrepare" to null, "btBzMark" to null, "btBzDesc" to null),
    )

    // 回滚库存明细
    EntityRwService.updateMany(
      
      "FbInvLayout",
      Cq.eq("btPrepare", g.key),
      mutableMapOf("btPrepare" to null, "btBzMark" to null, "btBzDesc" to null),
    )

    // 回滚 ResvRecord 状态
    EntityRwService.updateMany("ResvRecord", Cq.eq("key", g.key), mutableMapOf("status" to ResvStatus.Waiting))
    g.records.map {
      if (it.noWait) {
        // case：只预定一次
        it.status = ResvStatus.Canceled
        ResvRepo.updateStatus(it)
        reservingList.remove(it)
      } else {
        // case：等待至预定成功
        it.status = ResvStatus.Waiting
      }
    }
  }

  private fun wrapQuery(resv: ReserveRecord) = Cq.and(Cq.empty("btPrepare"), Cq.empty("btBzMark"), resv.query)
}

object ResvRepo {
  fun createRecord(resv: ReserveRecord) {
    EntityRwService.createOne("ResvRecord", dtoToEv(resv))
  }

  fun updateStatus(resv: ReserveRecord) {
    EntityRwService.updateOne("ResvRecord", Cq.eq("id", resv.id), mutableMapOf("status" to resv.status))
  }

  fun findById(id: String): ReserveRecord? {
    val ev = EntityRwService.findOne("ResvRecord", Cq.eq("id", id)) ?: return null
    return evToDto(ev)
  }

  fun loadAll(): List<ReserveRecord> {
    val evList = EntityRwService.findMany(
      
      "ResvRecord",
      Cq.and(Cq.ne("status", ResvStatus.Assigned), Cq.ne("status", ResvStatus.Canceled)),
    )
    return evList.map { evToDto(it) }
  }

  private fun evToDto(ev: EntityValue): ReserveRecord {
    val r = ReserveRecord(
      ResvType.valueOf(ev["type"] as String),
      ResvStatus.valueOf(ev["status"] as String),
      ev["priority"] as Int,
      ev["sourceOrderType"] as String,
      ev["sourceOrderId"] as String,
      ev["group"] as String,
      JsonHelper.mapper.readValue(ev["query"] as String, jacksonTypeRef()),
      JsonHelper.mapper.readValue(ev["o"] as String, jacksonTypeRef()),
      ev["res"] as String,
      ev["btBzMark"] as String,
      ev["btBzDesc"] as String,
      ev["noWait"] as Boolean,
    )
    r.id = ev["id"] as String
    return r
  }

  private fun dtoToEv(resv: ReserveRecord): EntityValue = mutableMapOf(
    "id" to resv.id,
    "type" to resv.type,
    "status" to resv.status,
    "priority" to resv.priority,
    "sourceOrderType" to resv.sourceOrderType,
    "sourceOrderId" to resv.sourceOrderId,
    "group" to resv.group,
    "key" to resv.toKey(),
    "query" to JsonHelper.mapper.writeValueAsString(resv.query),
    "o" to JsonHelper.mapper.writeValueAsString(resv.o),
    "res" to resv.res,
    "btBzMark" to resv.btBzMark,
    "btBzDesc" to resv.btBzDesc,
  )
}

/**
 * 预定资源实体
 *
 * TODO 定时清理：自动删除 3 天前 Assigned、Canceled 的记录
 *
 * 实体名/类型 - 容器、库位、库存
 * query
 * priority
 * 业务单号：用于批量分配 ： 一个叫空容器单 出 100 料箱，按 10 个一批出，在这里是 10 个业务单号
 * 分配结果
 * btBzMark（预定类型）
 * btBzDesc
 * 状态：Waiting, Assigning, Assigned, Canceled TODO 如何清理 btBzMark
 *
 * TODO 生成 id
 */
data class ReserveRecord(
  val type: ResvType,
  var status: ResvStatus,
  val priority: Int,
  val sourceOrderType: String,
  val sourceOrderId: String,
  val group: String,
  val query: ComplexQuery,
  val o: FindOptions,
  val res: String,
  val btBzMark: String,
  val btBzDesc: String,
  val noWait: Boolean, // TODO 预定一次后立即返回结果，不要重试
) {
  var id: String? = null // TODO 在 ReserveCenter.reserve 中生成还是实体最开始就生成 待定

  fun toKey(): String = sourceOrderType + sourceOrderId + group // TODO 持久化
}

data class ReserveRecordGroup(
  val key: String, // sourceOrderType + sourceOrderId + group
  val priority: Int,
  val records: List<ReserveRecord>,
  // var res: String?,
)

enum class ResvStatus {
  Waiting,
  Assigning,
  Assigned,
  Canceled,
}

enum class ResvType {
  Container,
  Bin,
  Inventory,
}