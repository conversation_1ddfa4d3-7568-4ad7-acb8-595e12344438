package com.seer.trick.bz.handler

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.base.http.WebSocketSubscriber
import com.seer.trick.base.http.WsEnv
import com.seer.trick.base.http.WsMsg
import com.seer.trick.bz.wms.BinOverviewService
import com.seer.trick.helper.JsonHelper
import io.javalin.websocket.WsMessageContext

object BzWsManager : WebSocketSubscriber() {

  override fun onMessage(ctx: WsMessageContext, msg: WsMsg, env: WsEnv) {
    when (msg.action) {
      "BinOverview::Query" -> onBinOverviewReq(ctx, msg)
    }
  }

  data class BinOverviewReq(
    val district: String?,
  )

  private fun onBinOverviewReq(ctx: WsMessageContext, msg: WsMsg) {
    val req: BinOverviewReq = JsonHelper.mapper.readValue(msg.content, jacksonTypeRef())
    val map = if (req.district.isNullOrBlank()) null else BinOverviewService.districtMap[req.district]
    ctx.send(WsMsg.json("BinOverview::Reply", map, replyToId = msg.id))
  }

}
