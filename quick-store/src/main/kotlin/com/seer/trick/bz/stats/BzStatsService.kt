package com.seer.trick.bz.stats

import com.seer.trick.Cq
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.stats.ChartGroup
import com.seer.trick.base.stats.ChartItem
import com.seer.trick.base.stats.StatsService
import com.seer.trick.base.stats.StatsService.buildShowLabelOnTop
import com.seer.trick.helper.JsonHelper

object BzStatsService {

  fun register() {
    StatsService.addGroup(
      ChartGroup(
        "Wms", "仓储", 1, mutableListOf(
          ChartItem("ContainerFilled", "容器空满率", "", 0),
          ChartItem("BinFilled", "库位空满率", "", 0),
        )
      )
    )
    StatsService.internalCalcMap["ContainerFilled"] = ::calcContainerFilled
    StatsService.internalCalcMap["BinFilled"] = ::calcBinFilled
  }

  private fun calcContainerFilled(): String {
    val evList = EntityRwService.findMany(
      "FbContainer", Cq.all(),
      FindOptions(projection = listOf("btDisabled", "filled"))
    )

    val all = evList.size
    var disabled = 0
    var filled = 0

    for (ev in evList) {
      if (ev["btDisabled"] == true) disabled++
      else if (ev["filled"] == true) filled++
    }

    val option = mapOf(
      "title" to mapOf("text" to "容器空满率", "x" to "left", "y" to "top"),
      "legend" to mapOf("x" to "right", "y" to "top"),
      "series" to listOf(
        mapOf(
          "type" to "pie", "data" to listOf(
            mapOf("name" to "有货", "value" to filled),
            mapOf("name" to "无货", "value" to all - filled - disabled),
            mapOf("name" to "停用", "value" to disabled),
          ) + buildShowLabelOnTop()
        ),
      )
    )
    return JsonHelper.mapper.writeValueAsString(option)
  }

  private fun calcBinFilled(): String {
    val evList = EntityRwService.findMany(
      "FbBin", Cq.all(),
      FindOptions(projection = listOf("btDisabled", "occupied"))
    )

    val all = evList.size
    var disabled = 0
    var occupied = 0

    for (ev in evList) {
      if (ev["btDisabled"] == true) disabled++
      else if (ev["occupied"] == true) occupied++
    }

    val option = mapOf(
      "title" to mapOf("text" to "库位空满率", "x" to "left", "y" to "top"),
      "legend" to mapOf("x" to "right", "y" to "top"),
      "series" to listOf(
        mapOf(
          "type" to "pie", "data" to listOf(
            mapOf("name" to "有货", "value" to occupied),
            mapOf("name" to "无货", "value" to all - occupied - disabled),
            mapOf("name" to "停用", "value" to disabled),
          ) + buildShowLabelOnTop()
        ),
      )
    )
    return JsonHelper.mapper.writeValueAsString(option)
  }

}