package com.seer.trick.bz.order

import com.seer.trick.BzError
import com.seer.trick.Cq

import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.*
import com.seer.trick.base.entity.service.EntityRwService

/**
 * 上拉单据
 */
object UpOrderService {

  fun fetchUpOrder(req: FetchUpOrderReq): EntityValue {
    val downOrderEm = BaseCenter.mustGetEntityMeta(req.downOrderName)
    val upOrderConfigItem = mustGetUpOrderConfigItem(downOrderEm, req.upOrderName)

    val upOrderEv = EntityRwService.findOne(req.upOrderName, Cq.idEq(req.upOrderId))
      ?: throw BzError("errBzNoSuchOrderNameId", req.upOrderName, req.upOrderId)

    return mapUpOrder(upOrderConfigItem, upOrderEv, req)
  }

  private fun mapUpOrder(
    upOrderConfigItem: UpOrderConfigItem, upOrderEv: EntityValue, req: FetchUpOrderReq
  ): EntityValue {
    val downOrderEv: EntityValue = mutableMapOf(
      FieldMeta.FIELD_SOURCE_ORDER_ID to req.upOrderId,
    )

    // 映射单头
    for (mapping in upOrderConfigItem.headFieldMapping) {
      if (upOrderEv.containsKey(mapping.sourceField)) {
        downOrderEv[mapping.targetField] = upOrderEv[mapping.sourceField]
      }
    }

    // 映射单行
    val upLines = EntityHelper.mustGetLines(upOrderEv, FieldMeta.FIELD_LINES)
    val downLines: MutableList<EntityValue> = ArrayList()
    for (upLine in upLines) {
      // 关系字段
      val downLine: EntityValue = mutableMapOf(
        FieldMeta.FIELD_SOURCE_ORDER_ID to req.upOrderId,
        FieldMeta.FIELD_SOURCE_LINE_NO to upLine[FieldMeta.FIELD_LINE_NO]
      )
      // 映射物料类字段
      val m = upLine[FieldMeta.FIELD_MATERIAL] as String?
      if (!m.isNullOrBlank()) {
        for (f in FieldMeta.MaterialFields) downLine[f] = upLine[f]
      }
      // 其他字段
      for (mapping in upOrderConfigItem.lineFieldMapping) {
        if (upLine.containsKey(mapping.sourceField)) {
          downLine[mapping.targetField] = upLine[mapping.sourceField]
        }
      }
      downLines += downLine
    }
    downOrderEv[FieldMeta.FIELD_LINES] = downLines

    return downOrderEv
  }

  fun mustGetUpOrderConfigItem(downOrderEm: EntityMeta, upOrderName: String): UpOrderConfigItem {
    return downOrderEm.orderConfig?.upOrderConfig?.items?.find { it.upOrderName == upOrderName }
      ?: throw BzError("errNoUpOrderConfig", downOrderEm.name, upOrderName)
  }

}

data class FetchUpOrderReq(
  val downOrderName: String,
  val upOrderName: String,
  val upOrderId: String,
)