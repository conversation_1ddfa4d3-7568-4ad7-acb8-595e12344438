package com.seer.trick.bz.wms.resv

/**
 * 两阶段提交示例 TODO 后续删掉
 */

data class MyEntity(var prepare: Boolean = false, var btBzMark: Boolean = false, var version: Int = 0)

interface Participant {
  fun prepare(entities: List<MyEntity>): Boolean
  fun commit(entities: List<MyEntity>)
  fun rollback(entities: List<MyEntity>)
}

class Coordinator(val participants: List<Participant>, val entities: List<MyEntity>) {
  fun twoPhaseCommit(): Boolean {
    // 第一阶段：准备阶段
    val prepareResults = participants.map { it.prepare(entities) }
    // 第一阶段所有参与者都准备好了
    val allPrepared = prepareResults.all { it }

    if (allPrepared) {
      // 第二阶段：提交
      participants.forEach { it.commit(entities) }
      return true
    } else {
      // 第二阶段：回滚
      participants.forEach { it.rollback(entities) }
      return false
    }
  }
}

class ConcreteParticipant : Participant {
  override fun prepare(entities: List<MyEntity>): Boolean {
    for (entity in entities) {
      if (entity.prepare) {
        println("Entity is already in prepare state. Skipping...")
        return false
      }
      entity.prepare = true
      entity.version++
      println("Participant prepared entity with version: ${entity.version}")
    }
    return true
  }

  override fun commit(entities: List<MyEntity>) {
    for (entity in entities) {
      if (entity.prepare) {
        entity.btBzMark = true
        entity.version++
        println("Participant committed entity with version: ${entity.version}")
      }
    }
  }

  override fun rollback(entities: List<MyEntity>) {
    for (entity in entities) {
      if (entity.prepare) {
        entity.prepare = false
        entity.version++
        println("Participant rolled back entity with version: ${entity.version}")
      }
    }
  }
}

fun main() {
  // 创建多个实体
  val entities = listOf(MyEntity(), MyEntity(), MyEntity())
  // 创建多个参与者
  val participants = listOf(ConcreteParticipant(), ConcreteParticipant())
  // 创建协调者
  val coordinator = Coordinator(participants, entities)
  // 执行两阶段提交
  val result = coordinator.twoPhaseCommit()
  println("Two - phase commit result: $result")
}