package com.seer.trick.bz.falcon.bp.bin

import com.seer.trick.BzError
import com.seer.trick.bz.wms.CoreWmsRelationService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*

/**
 * 锁定一次库位
 */
class LockBinOnceBp : AbstractBp() {

  override fun process() {
    val binId = mustGetBlockInputParam("binId") as String
    val notFoundToAborted = getBlockInputParam("notFoundToAborted") as Boolean? ?: false
    val notFoundToFault = getBlockInputParam("notFoundToFault") as Boolean? ?: false

    if (CoreWmsRelationService.lockBinIfNotLocked(binId, "")) {
      setBlockOutputParams(mutableMapOf("ok" to true))
    } else {
      if (notFoundToAborted) {
        throw FatalError("Lock bin $binId")
      } else if (notFoundToFault) {
        throw BzError("errLockBinFailed", binId)
      }
      setBlockOutputParams(mutableMapOf("ok" to false))
    }
    addRelatedObject("FbBin", binId, null)
  }

  companion object {

    val def = BlockDef(
      LockBinOnceBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(
        BlockInputParamDef("binId", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.BinId)),
        BlockInputParamDef("notFoundToFault", BlockParamType.Boolean, false),
        BlockInputParamDef("notFoundToAborted", BlockParamType.Boolean, false),
      ),
      outputParams = listOf(
        BlockOutputParamDef("ok", BlockParamType.Boolean),
      ),
    )
  }
}