package com.seer.trick.bz

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.bz.wms.CallContainerService
import com.seer.trick.bz.wms.PutOrderService
import com.seer.trick.bz.wms.inv.CreateInvService

object BzScript {
  
  fun tryCallContainer() {
    CallContainerService.tryCallContainer()
  }
  
  // fun tryOutbound() {
  //   // TODO
  //   // OutboundService.tryOutbound()
  // }
  
  fun finishPutOrderByContainer(containerId: String) {
    PutOrderService.finishPutOrderByContainer(containerId)
  }
  
  /**
   * 修复并创建库存明细。填充物料信息、位置信息、容器等。
   */
  fun fixCreateInvLayout(layouts: List<EntityValue>) {
    CreateInvService.fixCreateInvLayout(layouts)
  }
  
  
}