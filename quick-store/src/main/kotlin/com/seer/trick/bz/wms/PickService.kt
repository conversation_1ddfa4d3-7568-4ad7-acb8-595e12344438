package com.seer.trick.bz.wms

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.concurrent.BaseConcurrentCenter.highTimeSensitiveExecutor
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.entity.service.UpdateOptions
import com.seer.trick.base.entity.service.change.EntityChange
import com.seer.trick.base.entity.service.extension.EntityServiceExtension
import com.seer.trick.base.reslock.ResLockService
import com.seer.trick.base.script.ScriptCenter
import com.seer.trick.base.script.ScriptExeRequest
import com.seer.trick.base.script.ScriptTraceContext
import com.seer.trick.falcon.task.FalconTaskDefService
import com.seer.trick.helper.NumHelper
import org.slf4j.LoggerFactory
import kotlin.concurrent.withLock
import kotlin.math.min

object PickService : EntityServiceExtension() {

  private val logger = LoggerFactory.getLogger(this::class.java)

  override fun afterCreating(em: EntityMeta, evList: List<EntityValue>) {
    val outboundConfig = OutboundService.findOutboundConfig(em.name) ?: return
    highTimeSensitiveExecutor.submit {
      for (ev in evList) {
        try {
          tryToFinishPick(outboundConfig, ev)
        } catch (e: Exception) {
          logger.error("finishPick", e)
        }
      }
    }
  }

  override fun afterUpdating(em: EntityMeta, changes: List<EntityChange>) {
    val outboundConfig = OutboundService.findOutboundConfig(em.name) ?: return

    highTimeSensitiveExecutor.submit {
      for (c in changes) {
        val ev = c.newValue ?: continue
        try {
          tryToFinishPick(outboundConfig, ev)
        } catch (e: Exception) {
          logger.error("finishPick", e)
        }
      }
    }
  }

  /**
   * 处理分拣单状态达到 "Done" 但 submittedPostProcessed 为 false。
   * 扣减库存。
   * 如果实际拣出数量不足，补充出库，生成盘亏单
   * 前面环节要保证 qty <= planQty
   * 运回 noInTransportOrder
   */
  private fun tryToFinishPick(config: OutboundConfig, pickOrder: EntityValue) {
    val orderId = EntityHelper.mustGetId(pickOrder)
    // 重新加载，保证最新
    val order = EntityRwService.findOne(config.pickEmName, Cq.idEq(orderId))
      ?: throw BzError("errBzNoSuchOrderNameId", config.pickEmName, orderId)
    val lines = EntityHelper.mustGetLines(order, FieldMeta.FIELD_LINES)

    if (order[FieldMeta.FIELD_ORDER_STATE] != "Done" || order["submittedPostProcessed"] == true) return

    logger.info("分拣后处理 $orderId")

    var reInvAssign = false // 是否需要再分配库存

    // 扣库存扩展 pickOrderToReduceInv(pickOrder, pickEmName)
    val extRes: PickOrderToReduceInvRes? = if (ScriptCenter.exists("pickOrderToReduceInv")) {
      ScriptCenter.execute(
        ScriptExeRequest("pickOrderToReduceInv", arrayOf(ScriptTraceContext(), pickOrder, config.pickEmName)),
        jacksonTypeRef(),
      )
    } else {
      null
    }
    if (extRes != null) {
      if (extRes.error) {
        throw BzError("errBzError", extRes.errorMsg)
      } else {
        reInvAssign = extRes.reInvAssign
      }
    } else {
      for (line in lines) {
        reduceInv(line, config)

        val planQty = NumHelper.anyToDouble(line["planQty"]) ?: continue
        val qty = NumHelper.anyToDouble(line["qty"]) ?: continue
        if (qty < planQty) reInvAssign = true
      }
    }

    // 修复容器状态
    val containerId = order["container"] as String?
    if (!containerId.isNullOrBlank()) {
      ContainerService.fixContainerFilled(containerId)
      val pickOrder = EntityRwService.findOne(
        config.pickEmName,
        Cq.and(listOf(Cq.eq("container", containerId), Cq.eq("btOrderState", "Todo")))
      )
      // 没有等待分拣的分拣单后,再解锁库存
      if (pickOrder == null) {
        // 分拣完成后，该容器的库存全部解锁
        EntityRwService.updateMany(
          "FbInvLayout", Cq.eq("leafContainer", containerId), mutableMapOf("locked" to false)
        )
      }
    }



    if (reInvAssign) {
      ResLockService.resLock.withLock {
        reInvAssign(orderId, lines, config)
      }
    }

    // 当前库位
    val binEv = EntityRwService.findOne("FbBin", Cq.eq("container", containerId))
    if (binEv == null) {
      logger.warn("容器 '$containerId' 当前库位为空")
    }

    val def = if (!config.containerInFalconTask.isNullOrBlank()) {
      FalconTaskDefService.mustFetchLatestTaskDefById(config.containerInFalconTask)
    } else {
      null
    }

    if (config.noInTransportOrder != true) {
      // 容器搬运单
      val transportOrder: EntityValue = mutableMapOf(
        "container" to containerId,
        "kind" to "ContainerIn",
        "status" to "Created",
        "fromBin" to binEv?.get("id"), // TODO toBin!
        "falconTaskDefId" to config.containerInFalconTask,
        "falconTaskDefLabel" to def?.label,
        "sourceOrderId" to orderId,
      )
      EntityRwService.createOne("ContainerTransportOrder", transportOrder)
    }

    // 更新标记
    EntityRwService.updateOne(
      config.pickEmName,
      Cq.idEq(orderId),
      mutableMapOf("submittedPostProcessed" to true),
      UpdateOptions(muteExt = true),
    )
  }

  /**
   * 按 planQty 降低库存；会删除零库存；会解锁库存明细
   */
  private fun reduceInv(line: EntityValue, config: OutboundConfig) {
    // planQty 是希望拣出的量
    var qty = NumHelper.anyToDouble(line["planQty"])
    if (qty == null || qty <= 0) {
      logger.error("希望拣出的量无效：$qty")
      return
    }
    val containerId = line["container"] as String
    val subContainerId = line[FieldMeta.FIELD_SUB_CONTAINER_ID] as Int?
    logger.info("分拣单行=$line")

    val cqList = OutboundService.matchFieldsToCqEq(line, config.invMatchFields)
    cqList += Cq.eq(FieldMeta.FIELD_LEAF_CONTAINER, containerId)
    if (subContainerId != null && subContainerId > 0) {
      cqList += Cq.eq(FieldMeta.FIELD_SUB_CONTAINER_ID, subContainerId)
    }

    // 按数量从小到大
    val layouts = EntityRwService.findMany("FbInvLayout", Cq.and(cqList), FindOptions(sort = listOf("-qty")))
    for (layout in layouts) {
      val layoutId = EntityHelper.mustGetId(layout)
      val layoutQty = NumHelper.anyToDouble(layout["qty"]) ?: continue
      val thisQty = min(qty, layoutQty)
      qty -= thisQty
      if (thisQty == layoutQty) {
        logger.info("分拣，减少 $thisQty，删除库存明细 $layout")
        EntityRwService.removeOne("FbInvLayout", Cq.idEq(layoutId))
      } else {
        val layoutQty2 = layoutQty - thisQty
        logger.info("分拣，库存明细从 $layoutQty 减少 $thisQty 到 $layoutQty2")
        // 以为分拣单是按容器组织的，这个明细不会被多个分拣单用，所以这里可以解锁
        EntityRwService.updateOne(
          "FbInvLayout",
          Cq.idEq(layoutId),
          mutableMapOf("qty" to layoutQty2, "locked" to false),
        )
      }
      if (qty <= 0) break
    }

    if (qty > 0) {
      logger.error("分拣后还有 $qty 个物料找不到扣减的库存明细，分拣行 $line")
    }
  }

  // 准备重新分配库存
  private fun reInvAssign(orderId: String, lines: List<EntityValue>, outboundConfig: OutboundConfig) {
    // 盘亏记录
    val countFixes: MutableList<EntityValue> = ArrayList()

    val outboundOrderIds: MutableSet<String> = HashSet()

    for (line in lines) {
      val planQty = NumHelper.anyToDouble(line["planQty"]) ?: continue
      val qty = NumHelper.anyToDouble(line["qty"]) ?: continue
      if (qty >= planQty) continue

      val delta = planQty - qty

      // 盘亏记录
      val cf: EntityValue = mutableMapOf(
        "container" to line["container"],
        FieldMeta.FIELD_SUB_CONTAINER_ID to line[FieldMeta.FIELD_SUB_CONTAINER_ID],
        "qty" to -delta, // 盘亏，负
        "remark" to "分拣实物不足。分拣单号=$orderId",
      )
      for (fn in outboundConfig.invMatchFields) cf[fn] = line[fn]
      MaterialManager.fillMaterialFieldsIntoLine(cf[FieldMeta.FIELD_MATERIAL] as String, cf)

      countFixes += cf

      // 虽然记录了单行 ID，但是更新单据时，这个不稳定，所以还是用单号+行号去查
      val outboundOrderId = line["sourceOrderId"] as String?
      val outboundLineNo = line["sourceLineNo"] as Int?
      if (outboundOrderId == null || outboundLineNo == null) {
        logger.error("分拣单行上没有完整的来源单据和行号信息：$line")
        continue
      }
      val em = BaseCenter.mustGetEntityMeta(outboundConfig.outboundEmName)
      if (!em.fields.containsKey(FieldMeta.FIELD_LINES)) continue
      val lineEntity = em.fields[FieldMeta.FIELD_LINES]!!.refEntity.toString()

      val outboundLine = EntityRwService.findOne(
        lineEntity,
        Cq.and(
          listOf(
            Cq.eq(FieldMeta.FIELD_PARENT_ID, outboundOrderId),
            Cq.eq(FieldMeta.FIELD_LINE_NO, outboundLineNo),
          ),
        ),
      )
      if (outboundLine == null) {
        logger.error("找不到出库单行 $outboundOrderId:$outboundLineNo")
        continue
      }

      outboundOrderIds += outboundOrderId

      var invAssignedQty = NumHelper.anyToDouble(outboundLine["invAssignedQty"]) ?: 0.0
      logger.info("出库单行 $outboundOrderId:$outboundLineNo，已分配数量从 $invAssignedQty 减去 $delta")
      invAssignedQty -= delta

      EntityRwService.updateOne(
        lineEntity,
        Cq.idEq(EntityHelper.mustGetId(outboundLine)),
        mutableMapOf("invAssignedQty" to invAssignedQty),
      )
    }

    if (outboundOrderIds.isNotEmpty()) {
      logger.info("更新以下出库单，库存分配未完成 $outboundOrderIds")
      EntityRwService.updateMany(
        outboundConfig.outboundEmName,
        Cq.include("id", outboundOrderIds.toList()),
        mutableMapOf("invAssignedAll" to false),
      )
    }

    if (countFixes.isNotEmpty()) {
      logger.info("创建盘亏记录 $countFixes")
      EntityRwService.createMany("FbCountFix", countFixes)
    }
  }
}

data class PickOrderToReduceInvRes(
  val error: Boolean = false,
  val errorMsg: String? = null,
  val reInvAssign: Boolean = false, // 是否需要重新分配库存出库
)