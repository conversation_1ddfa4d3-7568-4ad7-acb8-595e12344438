package com.seer.trick.bz.wms

import com.seer.trick.Cq
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.change.EntityChange
import com.seer.trick.base.entity.service.extension.EntityServiceExtension
import com.seer.trick.base.reslock.ResLockService
import com.seer.trick.bz.wms.inv.CreateInvService
import com.seer.trick.falcon.task.FalconTaskDefService
import com.seer.trick.helper.NumHelper
import com.seer.trick.helper.submitCatch
import org.slf4j.LoggerFactory
import java.util.concurrent.Executors
import kotlin.concurrent.withLock

/**
 * 盘点
 */
object CountService : EntityServiceExtension() {

  private val logger = LoggerFactory.getLogger(this::class.java)

  private val processSubmittedExecutor = Executors.newSingleThreadExecutor()

  override fun afterCreating(em: EntityMeta, evList: List<EntityValue>) {
    if (em.name == "FbCountOrder") {
      for (ev in evList) {
        if (ev[FieldMeta.FIELD_ORDER_STATE] != "Submitted") continue
        processSubmittedExecutor.submitCatch("处理提交的盘点单", logger) { buildCountTasks(ev) }
      }
    } else if (em.name == "FbCountTask") {
      for (ev in evList) {
        if (ev[FieldMeta.FIELD_ORDER_STATE] != "Submitted") continue
        processSubmittedExecutor.submitCatch("处理提交的盘点任务", logger) { finishCountTask(ev) }
      }
    }
  }

  override fun afterUpdating(em: EntityMeta, changes: List<EntityChange>) {
    if (em.name == "FbCountOrder") {
      for (c in changes) {
        val ev = c.newValue ?: continue
        val state = ev[FieldMeta.FIELD_ORDER_STATE]
        if (state == "Submitted") {
          processSubmittedExecutor.submitCatch("处理提交的盘点单", logger) { buildCountTasks(ev) }
        } else if (state == "Done") {
          processSubmittedExecutor.submitCatch("完成盘点单", logger) { doneCount(ev) }
        }
      }
    } else if (em.name == "FbCountTask") {
      for (c in changes) {
        val ev = c.newValue ?: continue
        if (ev[FieldMeta.FIELD_ORDER_STATE] != "Submitted") continue
        processSubmittedExecutor.submitCatch("处理提交的盘点任务", logger) { finishCountTask(ev) }
      }
    }
  }

  // 盘点单 -> 生成盘点任务、容器搬运单
  private fun buildCountTasks(order: EntityValue) {
    // 已提交、未生成任务
    if (order[FieldMeta.FIELD_ORDER_STATE] != "Submitted" || order["taskGenerated"] == true) return

    val orderId = EntityHelper.mustGetId(order)
    logger.info("开始为盘点单 $orderId 生成盘点任务")

    val config = parseCountConfig()

    val countTargets = ResLockService.resLock.withLock {
      // 列出盘点对象
      val countTargets = listCountTargets(order)

      // 锁定容器和库存
      if (countTargets.byContainers.isNotEmpty()) {
        val c1 = EntityRwService.updateMany(
          "FbContainer", Cq.include("id", countTargets.byContainers.keys.toList()),
          mutableMapOf("locked" to true, "taskType" to "Count") // 容器任务类型=盘点
        )
        logger.info("锁定容器，更新数=$c1")

        val c2 = EntityRwService.updateMany(
          "FbInvLayout", Cq.include("id", countTargets.invLayoutIds),
          mutableMapOf("locked" to true)
        )
        logger.info("锁定库存明细数=$c2")
      }

      countTargets
    }

    // 创建盘点任务、搬运单
    val statLines = if (countTargets.byContainers.isNotEmpty()) {
      createCountTasks(order, countTargets, config)
    } else {
      emptyList()
    }

    // 标记已生成任务，产生任务统计
    EntityRwService.updateOne(
      "FbCountOrder", Cq.idEq(orderId), mutableMapOf("taskGenerated" to true, "taskLines" to statLines)
    )
    logger.info("盘点单 $orderId 已生成任务")
  }

  // 从库存明细中盘点目标
  // TODO 没有库存的容器不会出来
  private fun listCountTargets(order: EntityValue): CountTargets {
    val cqList = mutableListOf(Cq.ne("locked", true))

    @Suppress("UNCHECKED_CAST")
    val containers = order["containers"] as List<String>?
    if (!containers.isNullOrEmpty()) {
      cqList += Cq.or(listOf(Cq.include("topContainer", containers), Cq.include("leafContainer", containers)))
    }

    @Suppress("UNCHECKED_CAST")
    val bins = order["bins"] as List<String>?
    if (!bins.isNullOrEmpty()) {
      cqList += Cq.include("bin", bins)
    }


    @Suppress("UNCHECKED_CAST")
    val districts = order["districts"] as List<String>?
    if (!districts.isNullOrEmpty()) {
      cqList += Cq.include("district", districts)
    }

    @Suppress("UNCHECKED_CAST")
    val materials = order["materials"] as List<String>?
    if (!materials.isNullOrEmpty()) {
      cqList += Cq.include(FieldMeta.FIELD_MATERIAL, materials)
    }

    val invLayouts = EntityRwService.findMany("FbInvLayout", Cq.and(cqList))

    // 按容器归集库存明细
    val byContainers = mutableMapOf<String, MutableList<EntityValue>>()
    for (il in invLayouts) {
      // 兼容下内外层容器
      val container = (il["topContainer"] as String?) ?: (il["leafContainer"] as String?) ?: continue
      val byContainer = byContainers.getOrPut(container) { ArrayList() }
      byContainer += il
    }

    return CountTargets(byContainers, invLayouts.map { EntityHelper.mustGetId(it) })
  }

  // 根据找到的容器和储存生成盘点任务、容器搬运单；返回任务统计行
  private fun createCountTasks(
    order: EntityValue, countTargets: CountTargets, config: CountConfig
  ): List<EntityValue> {
    if (countTargets.byContainers.isEmpty()) return emptyList()

    val countOrderId = EntityHelper.mustGetId(order)

    val countTaskEm = BaseCenter.mustGetEntityMeta("FbCountTask")
    val ctoEm = BaseCenter.mustGetEntityMeta("ContainerTransportOrder")

    // 盘点任务列表
    val countTasks = mutableListOf<EntityValue>()

    // 任务统计行
    val statLines = mutableListOf<EntityValue>()

    // 容器搬运单列表
    val transportOrders = mutableListOf<EntityValue>()

    for ((containerId, invLayouts) in countTargets.byContainers) {
      // 盘点单统计行
      val statLine = CountOrderContainerStatLine()

      // 任务单行
      val taskLines = mutableListOf<EntityValue>()
      for (il in invLayouts) {
        val qty = NumHelper.anyToDouble(il["qty"]) ?: continue
        if (qty <= 0) continue
        val line = mutableMapOf(
          "sourceInvLayout" to il["id"],
          "actualQty" to qty
        )
        statLine.qty += qty
        statLine.materials += il[FieldMeta.FIELD_MATERIAL] as String

        line.putAll(il)

        line.remove("id")

        taskLines += line
      }

      val fromBin = invLayouts.first()["bin"] as String

      // 盘点任务
      val countTask: EntityValue = mutableMapOf(
        "countOrderId" to countOrderId,
        "container" to containerId,
        "bin" to fromBin,
        FieldMeta.FIELD_ORDER_STATE to "Init",
        FieldMeta.FIELD_LINES to taskLines
      )
      // 直接分配 ID，便于记录
      val taskId = EntityRwService.ensureId(countTaskEm, countTask)

      countTasks += countTask

      statLines += mutableMapOf(
        "container" to containerId,
        "bin" to fromBin,
        "qty" to statLine.qty, // 容器内库存总数
        "materialsNum" to statLine.materials.size,
        "taskId" to taskId,
      )

      // 生成容器搬运单
      if (config.noOutTransportOrder != true) {
        val def = if (!config.containerOutFalconTask.isNullOrBlank())
          FalconTaskDefService.mustFetchLatestTaskDefById(config.containerOutFalconTask)
        else
          null

        val binEv = EntityRwService.findOne("FbBin", Cq.idEq(fromBin))
        if (binEv == null) {
          logger.warn("找不到库位 '${fromBin}'")
        }

        val transportOrder: EntityValue = mutableMapOf(
          "container" to containerId,
          "kind" to "ContainerOut",
          "status" to "Created",
          "fromBin" to fromBin,
          "fromChannel" to binEv?.get("channel"),
          "falconTaskDefId" to config.containerOutFalconTask, // 用哪个猎鹰任务执行此容器搬运单
          "falconTaskDefLabel" to def?.label,
          "sourceOrderId" to taskId,
        )
        // 直接分配 ID，便于记录
        val transportId = EntityRwService.ensureId(ctoEm, transportOrder)

        transportOrders += transportOrder

        // 写到盘点任务
        countTask["containerOutOrderId"] = transportId
      }
    }

    EntityRwService.createMany("FbCountTask", countTasks)

    EntityRwService.createMany("ContainerTransportOrder", transportOrders)

    return statLines
  }

  // 生成盘差；运回库：容器搬运单，猎鹰任务；
  private fun finishCountTask(task: EntityValue) {
    // 已提交、未完成
    if (task[FieldMeta.FIELD_ORDER_STATE] != "Submitted" || task["submittedPostProcessed"] == true) return

    val taskId = EntityHelper.mustGetId(task)
    logger.info("后处理盘点任务 $taskId")

    val config = parseCountConfig()

    val containerId = task["container"] as String
    val binId = task["bin"] as String // 来源库位

    // 生成盘差
    countTaskToDiff(task)

    // 当前库位，于是要求移动库存……
    val binEv = EntityRwService.findOne("FbBin", Cq.idEq(binId))
    if (binEv == null) {
      logger.warn("盘点任务 $taskId 来源库位 '$binId' 找不到")
    }

    val def = if (!config.containerInFalconTask.isNullOrBlank())
      FalconTaskDefService.mustFetchLatestTaskDefById(config.containerInFalconTask)
    else
      null

    val transportId: String? = if (config.noInTransportOrder != true) {
      // 容器搬运单
      val transportOrder: EntityValue = mutableMapOf(
        "container" to containerId,
        "kind" to "ContainerIn",
        "status" to "Created",
        "toBin" to binEv?.get("id"),
        "toChannel" to binEv?.get("channel"),
        "falconTaskDefId" to config.containerInFalconTask,
        "falconTaskDefLabel" to def?.label,
        "sourceOrderId" to taskId,
      )
      EntityRwService.createOne("ContainerTransportOrder", transportOrder)
    } else {
      null
    }

    // 更新标记
    EntityRwService.updateOne(
      "FbCountTask", Cq.idEq(taskId),
      mutableMapOf("submittedPostProcessed" to true, "containerInOrderId" to transportId),
    )
  }

  private fun countTaskToDiff(task: EntityValue) {
    val lines = EntityHelper.getLines(task, FieldMeta.FIELD_LINES) ?: return
    val taskId = EntityHelper.mustGetId(task)

    val containerId = task["container"] as String
    val binId = task["bin"] as String

    val diffLines = mutableListOf<EntityValue>()
    for (taskLine in lines) {
      val qty = NumHelper.anyToDouble(taskLine["qty"]) ?: 0.0
      val actualQty = NumHelper.anyToDouble(taskLine["actualQty"]) ?: 0.0
      if (NumHelper.isNumberEqual(qty, actualQty)) continue
      val diffLine = EntityHelper.clone(taskLine)
      diffLine.remove("id")
      diffLine["fix"] = true // 默认执行
      val diff = actualQty - qty
      diffLine["qty"] = qty
      diffLine["actualQty"] = actualQty
      diffLine["diffQty"] = diff // TODO 精度控制

      diffLine["taskId"] = taskId
      diffLine["container"] = containerId
      diffLine["bin"] = binId

      diffLines += diffLine

      logger.info("盘点任务 $taskId 行 ${taskLine[FieldMeta.FIELD_LINE_NO]} 生成盘差 $diff。$diffLine")
    }

    if (diffLines.isNotEmpty()) {
      val countOrderId = task["countOrderId"] as String?
      if (countOrderId.isNullOrBlank()) {
        logger.error("盘点任务 $taskId 没有关联的盘点单，无法记录盘差！")
        return
      }
      val countOrder = EntityRwService.findOneById("FbCountOrder", countOrderId)
      if (countOrder == null) {
        logger.error("盘点任务 $taskId 关联的盘点单 $countOrderId 不存在，无法记录盘差！")
        return
      }
      val oldDiffLines = EntityHelper.getMutableLines(countOrder, "diffLines") ?: ArrayList()
      oldDiffLines += diffLines

      logger.info("向盘点单 $countOrderId 追加 ${diffLines.size} 条盘差。")
      EntityRwService.updateOne("FbCountOrder", Cq.idEq(countOrderId), mutableMapOf("diffLines" to oldDiffLines))
    }
  }

  private fun doneCount(order: EntityValue) {
    if (order[FieldMeta.FIELD_ORDER_STATE] != "Done" || order["doneProcessed"] == true) return

    val orderId = EntityHelper.mustGetId(order)

    val diffLines = EntityHelper.getLines(order, "diffLines")
    if (!diffLines.isNullOrEmpty()) {
      val newInvLayouts = mutableListOf<EntityValue>()
      // 盘亏记录
      val countFixes: MutableList<EntityValue> = ArrayList()

      for (line in diffLines) {
        if (line["fix"] != true) continue
        val sourceInvLayoutId = line["sourceInvLayout"] as String?
        if (sourceInvLayoutId.isNullOrBlank()) {
          val il = EntityHelper.clone(line)
          il.remove("id")
          il["qty"] = il["actualQty"]
          il[FieldMeta.FIELD_TOP_CONTAINER] = il["container"] // 容器！
          il[FieldMeta.FIELD_LEAF_CONTAINER] = il["container"] // 容器！
          newInvLayouts += il
          logger.info("盘点完成 $orderId，创建新库存明细 $il")
        } else {
          val actualQty = NumHelper.anyToDouble(line["actualQty"]) ?: 0.0
          if (actualQty == 0.0) {
            logger.info("盘点完成 $orderId，删除空的库存明细，$sourceInvLayoutId")
            EntityRwService.removeOne("FbInvLayout", Cq.idEq(sourceInvLayoutId))
          } else {
            logger.info("盘点完成 $orderId，库存明细 $sourceInvLayoutId 改数量为 $actualQty")
            EntityRwService.updateOne("FbInvLayout", Cq.idEq(sourceInvLayoutId), mutableMapOf("qty" to actualQty))
          }
        }

        // 盘亏记录
        val cf = EntityHelper.clone(line)
        cf["qty"] = line["diffQty"]
        cf["remark"] to "盘点单号=$orderId"
        countFixes += cf
      }

      if (newInvLayouts.isNotEmpty()) {
        CreateInvService.fixCreateInvLayout(newInvLayouts)
      }

      if (countFixes.isNotEmpty()) {
        EntityRwService.createMany("FbCountFix", countFixes)
      }
    }

    // 最后解锁容器、库位
    val taskLines = EntityHelper.getLines(order, "taskLines")
    if (!taskLines.isNullOrEmpty()) {
      val containerIds = taskLines.map { it["container"] as String }
      val c1 = EntityRwService.updateMany(
        "FbContainer", Cq.include("id", containerIds),
        mutableMapOf("locked" to false, "taskType" to "")
      )
      logger.info("盘点完成 $orderId，解锁容器 $containerIds，更新数=$c1")

      val c2 = EntityRwService.updateMany(
        "FbInvLayout", Cq.include(FieldMeta.FIELD_TOP_CONTAINER, containerIds),
        mutableMapOf("locked" to false)
      )
      logger.info("盘点完成 $orderId，解锁库存明细数=$c2")
    }

    EntityRwService.updateOne("FbCountOrder", Cq.idEq(orderId), mutableMapOf("doneProcessed" to true))
  }

  private fun parseCountConfig(): CountConfig {
    val noOutTransportOrder = BzConfigManager.getByPathAsBoolean("ScWms", "count", "noOutTransportOrder")
    val noInTransportOrder = BzConfigManager.getByPathAsBoolean("ScWms", "count", "noInTransportOrder")
    val containerOutFalconTask = BzConfigManager.getByPathAsString("ScWms", "count", "containerOutFalconTask")
    val containerInFalconTask = BzConfigManager.getByPathAsString("ScWms", "count", "containerInFalconTask")

    return CountConfig(
      noOutTransportOrder = noOutTransportOrder,
      noInTransportOrder = noInTransportOrder,
      containerOutFalconTask = containerOutFalconTask,
      containerInFalconTask = containerInFalconTask,
    )
  }

}

data class CountTargets(
  val byContainers: Map<String, List<EntityValue>>,
  val invLayoutIds: List<String>
)

data class CountOrderContainerStatLine(
  var qty: Double = 0.0,
  var materials: MutableSet<String> = mutableSetOf(),
)

data class CountConfig(
  val noOutTransportOrder: Boolean? = null,
  val noInTransportOrder: Boolean? = null,
  val containerOutFalconTask: String? = null,
  val containerInFalconTask: String? = null,
)