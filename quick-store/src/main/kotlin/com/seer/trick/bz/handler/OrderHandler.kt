package com.seer.trick.bz.handler

import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.*
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.getReqBody
import com.seer.trick.bz.wms.AssignInvFilter
import com.seer.trick.bz.wms.BomManager
import com.seer.trick.bz.wms.inv.InvService
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.NumHelper
import io.javalin.http.Context

object OrderHandler {

  fun registerHandlers() {
    val c = Handlers("api/order")
    c.post("push", OrderHandler::pushOrder, auth())
    // c.post("occurred-qty", OrderHandler::calcOccurred<PERSON>ty, auth())
    c.post("inv-ref", OrderHandler::invRef, auth())
  }

  private fun pushOrder(ctx: Context) {
    val req: PushOrderReq = ctx.getReqBody()

    val em = BaseCenter.mustGetEntityMeta(req.fromOrder)
    val oc = em.orderConfig

    val config = oc?.pushOrders?.getOrNull(req.pushIndex)
      ?: throw BzError("errOrderNoPushConfig", "${req.fromOrder}:${req.pushIndex}")

    // let src_order_config = ar.get_order_config(&oo.org.ro_id, &req.from_entity_name)?;
    // let dst_order_config = ar.get_order_config(&oo.org.ro_id, &req.to_entity_name)?;

    val sourceOrders = EntityRwService.findMany(req.fromOrder, Cq.include("id", req.fromOrderIds))

    val txId = IdHelper.oidStr()

    val res: MutableList<PushOrderRes> = ArrayList()
    for (sourceOrder in sourceOrders) {
      val sourceOrderId = sourceOrder["id"] as String

      // 映射和创建新单据
      val targetOrder = mapOrder(sourceOrder, config, em)
      val targetOrderId = EntityRwService.createOne(config.downOrderName, targetOrder)

      // 记录单据关系
      val ofr: EntityValue = mutableMapOf(
        "txId" to txId,
        "pushType" to "OneByOne",
        "sourceOrderName" to req.fromOrder,
        "sourceOrderId" to sourceOrderId,
        "targetOrderName" to config.downOrderName,
        "targetOrderType" to config.downOrderKind,
        "targetOrderId" to targetOrderId
      )
      EntityRwService.createOne("OrderFlowRecord", ofr)

      // TODO 记录单行关系

      res += PushOrderRes(config.downOrderName, targetOrderId)
    }

    ctx.json(res)
  }

  // 映射单据
  private fun mapOrder(
    sourceOrder: EntityValue, config: PushOrderConfigItem, em: EntityMeta,
  ): EntityValue {
    val ev: EntityValue = mutableMapOf(
      FieldMeta.FIELD_BZ_KIND to config.downOrderKind,
      FieldMeta.FIELD_ORDER_STATE to config.downOrderState,
    )
    for (mapping in config.headFieldMapping) {
      val sv = sourceOrder[mapping.sourceField]
      if (sv != null) ev[mapping.targetField] = sv
    }

    val sourceLines = EntityHelper.mustGetLines(sourceOrder, FieldMeta.FIELD_LINES)
    val newLines: MutableList<EntityValue> = ArrayList()
    for (line in sourceLines) {
      val newLine: EntityValue = mutableMapOf()
      val m = line[FieldMeta.FIELD_MATERIAL] as String?
      if (!m.isNullOrBlank()) {
        for (f in FieldMeta.MaterialFields) newLine[f] = line[f]
      }
      for (mapping in config.lineFieldMapping) {
        val sv = line[mapping.sourceField]
        if (sv != null) newLine[mapping.targetField] = sv
      }
      newLines += newLine
    }
    ev[FieldMeta.FIELD_LINES] = newLines
    // BOM 展开
    if (em.name == "FbMfgOrder" && config.downOrderName == "FbMaterialPrepareOrder") {
      BomManager.mfgOrderToMaterialPrepareOrder(sourceOrder, ev)
    }
    return ev
  }

  // 从一个下游单据计算所有下游单据的已发生数量
  // private fun calcOccurredQty(ctx: Context) {
  //   val req: CalcOccurredQtyReq2 = ctx.getReqBody()
  //
  //   val r = OrderMaxQtyManager.calcOccurredQty(req.orderId, req.orderName, req.orderKind)
  //
  //   ctx.json(r)
  // }

  // 获取参考库存
  private fun invRef(ctx: Context) {
    val req: InvRefReq = ctx.getReqBody()

    // val em = BaseCenter.mustGetEntityMeta(req.entityName)
    // val oc = em.orderConfig

    val lines = EntityHelper.getLines(req.ev, FieldMeta.FIELD_LINES)!!
    val r = lines.map { line ->
      val materialId = line[FieldMeta.FIELD_MATERIAL_ID] as String
      val q1 = InvService.requestToQuery(materialId, AssignInvFilter())
      val layouts1 = EntityRwService.findMany("FbInvLayout", q1, FindOptions(projection = listOf("qty")))
      val invQty1: Double = layouts1.sumOf { ev -> (NumHelper.anyToDouble(ev["qty"]) ?: 0.0) }

      // @Suppress("UNCHECKED_CAST")
      // val filter = AssignInvFilter(
      //   warehouse = (line[oc.invRefWarehouseLineField] as List<String>?)
      //     ?: (line[oc.invRefWarehouseHeadField] as List<String>?),
      //   district = (line[oc.invRefDistrictLineField] as List<String>?)
      //     ?: (line[oc.invRefDistrictHeadField] as List<String>?)
      // )
      // TODO 其他字段
      // const q2 = requestToQuery(materialId, filter)
      val layouts2 = EntityRwService.findMany("FbInvLayout", q1, FindOptions(projection = listOf("qty")))
      val invQty2: Double = layouts2.sumOf { ev -> (NumHelper.anyToDouble(ev["qty"]) ?: 0.0) }

      InvRefRes(invQty1, invQty2)
    }

    ctx.json(r)
  }

}

data class PushOrderReq(val fromOrder: String, val pushIndex: Int, val fromOrderIds: List<String>)

data class PushOrderRes(val orderName: String, val orderId: String)

data class CalcOccurredQtyReq2(val orderId: String, val orderName: String, val orderKind: String)

data class InvRefReq(val entityName: String, val ev: EntityValue)

data class InvRefRes(val invQty1: Double, val invQty2: Double)