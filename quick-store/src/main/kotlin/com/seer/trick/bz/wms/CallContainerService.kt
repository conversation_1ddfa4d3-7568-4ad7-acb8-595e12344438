package com.seer.trick.bz.wms

import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.entity.service.extension.EntityServiceExtension
import com.seer.trick.base.reslock.ResLockService
import com.seer.trick.base.soc.SocService
import com.seer.trick.helper.NumHelper
import com.seer.trick.helper.StringHelper
import com.seer.trick.helper.submitLongRun
import org.slf4j.LoggerFactory
import java.util.concurrent.Executors
import kotlin.concurrent.withLock
import kotlin.math.min

/**
 * 叫空容器
 * 容器可以带格子。
 *
 * 找空容器：筛选和排序
 *
 * 排序：尾箱优先、所在库区、存放位置、先进先出
 *
 * 容器属性：是否全空、容器物料种数、容器物料编号列表、容器总空满率、容器空满格率、容器物料总数、每格子空满率、每个格子物料种数、
 */
object CallContainerService : EntityServiceExtension() {

  private val logger = LoggerFactory.getLogger(javaClass)

  fun init() {
    Executors.newSingleThreadExecutor().submitLongRun("处理：为入库单叫空容器", logger, {
      var delay = BzConfigManager.getByPathAsInt("ScWms", "callContainer", "autoCallForInboundDelay") ?: 0
      if (delay <= 0) delay = 5000
      delay.toLong()
    }) {
      val enabled = BzConfigManager.getByPath("ScWms", "callContainer", "autoCallForInbound") as Boolean?
      if (enabled == true) {
        SocService.updateNode("业务", "Bz:AutoCallForInbound", "为入库单叫空容器", "开始")
        tryCallContainer()
      } else {
        SocService.removeNode("Bz:AutoCallForInbound")
      }
    }
  }

  fun tryCallContainer() {
    // 列出所有未成功叫空容器的
    // callContainerAll 是此入库单已全部找到空容器的标志
    // 按创建时间排序
    val orders = EntityRwService.findMany(
      "FbInboundOrder",
      Cq.and(listOf(Cq.eq(FieldMeta.FIELD_ORDER_STATE, "Committed"), Cq.ne("callContainerAll", true))),
      FindOptions(listOf("id"), listOf("createdOn"))
    )
    for (order in orders) {
      val orderId = EntityHelper.mustGetId(order)
      try {
        // Stop 表示必须整单全部找到，否则放弃
        callContainerByOrder(CallContainerByOrderReq(orderId, ShortOption.Stop))
      } catch (e: InterruptedException) {
        throw e
      } catch (e: BzError) {
        logger.info(e.message ?: "")
      } catch (e: Exception) {
        logger.error("尝试叫容器，入库单=$orderId", e)
      }
    }
  }

  /**
   * 如果成功，会新建装货单
   * 锁定容器，将容器任务状态改为装箱（Put）
   * 入库单行已出容器数量 callContainerQty
   * 入库单已全部找到空容器，会将入库单的 callContainerAll 置为 true。
   */
  fun callContainerByOrder(req: CallContainerByOrderReq) {
    logger.info("叫空容器，入库单=${req.orderId}，选项=${req.shortOption}")

    val order = EntityRwService.findOne("FbInboundOrder", Cq.idEq(req.orderId))
      ?: throw BzError("errBzNoSuchOrderNameId", "FbInboundOrder", req.orderId)
    val orderId = EntityHelper.mustGetId(order)
    val lines = EntityHelper.mustGetLines(order, FieldMeta.FIELD_LINES)

    val copyFields = getCopyFields()

    val materialIds = MaterialManager.listMaterialIdsFromOrderLines(order)
    val mpMap = loadMaterialParams(materialIds)

    // 是否缺容器
    var short = false

    // 修改过的单行
    val changedLines = mutableListOf<EntityValue>()

    ResLockService.resLock.withLock {
      val cpMap = loadContainerParams()

      val plans = mutableMapOf<String, MutableList<EntityValue>>() // container id ->

      for (line in lines) {
        val lineNo = line[FieldMeta.FIELD_LINE_NO] as Int

        val qty = NumHelper.anyToDouble(line["qty"])
        if (qty == null || qty <= 0) {
          logger.warn("单行 $orderId : $lineNo 入库数量无效：'$qty'")
          continue
        }
        val callContainerQty = NumHelper.anyToDouble(line["callContainerQty"]) ?: 0.0
        var restQty = qty - callContainerQty
        if (restQty <= 0) {
          logger.info("单行 $orderId : $lineNo 入库数量：'$qty' <= 已出容器数量 '$callContainerQty'")
          continue
        }

        val materialId = line[FieldMeta.FIELD_MATERIAL] as String? ?: continue
        val mp = mpMap[materialId] ?: continue

        // 不会发生，前面检查了 storeDistricts 不能为空
        // if (mp.storeDistricts.isEmpty()) throw BzError("") // 存储区，必须有，从哪个区域出容器

        // 筛选容器，赋权排序
        val cp2List: MutableList<ContainerParamSortWeight> = ArrayList()
        for (cp in cpMap.values) {
          // 容器类型过滤
          if (!mp.containerTypes.contains(cp.containerType)) continue
          // 存储库区过虑
          if (cp.storeDistrict.isNullOrBlank() || !mp.storeDistricts.contains(cp.storeDistrict)) continue
          // 物料类型过滤：只能同种物料放到一种容器
          if (cp.materialCategorySet.isNotEmpty() && !cp.materialCategorySet.contains(mp.leafCategory)) continue

          val cp2 = ContainerParamSortWeight(cp)

          // 按库区排序
          cp2.sortWeights += mp.storeDistricts.indexOf(cp.storeDistrict).toDouble()
          // 按已有库存总数排序，即实现尾箱优先
          cp2.sortWeights += -cp.totalQty
          cp2List += cp2
        }

        // 就地排序 TODO 只需要前 N 个
        cp2List.sortBy { it.sortWeights[0] }
        cp2List.sortBy { it.sortWeights[1] }

        for (cp2 in cp2List) {
          for (subNo in 1..cp2.cp.subNum) {
            if (cp2.cp.subInv.containsKey(subNo)) continue // 格子已被用
            val mq = mp.maxQty[cp2.cp.containerType] ?: continue
            val q = min(restQty, mq.maxQty.toDouble())

            // 分拣单行
            val putLine: EntityValue = mutableMapOf(
              FieldMeta.FIELD_MATERIAL to materialId,
              "container" to cp2.cp.containerId,
              FieldMeta.FIELD_SUB_CONTAINER_ID to subNo,
              "planQty" to q,
              "qty" to q
            )
            // 额外的库存特征字段
            for (fn in copyFields) putLine[fn] = line[fn]
            // 填充物料信息
            MaterialManager.fillMaterialFieldsIntoLine(materialId, putLine)

            val putLines = plans.getOrPut(cp2.cp.containerId) { ArrayList() }
            putLines += putLine

            // 增加容器已使用情况
            cp2.cp.totalQty += q
            val invLayouts = cp2.cp.subInv.getOrPut(subNo) { ArrayList() }
            invLayouts += putLine // 近似下

            restQty -= q
            if (restQty <= 0) break
          }
          if (restQty <= 0) break
        }

        if (restQty > 0) {
          if (req.shortOption == ShortOption.Stop) {
            throw BzError("errBzNoEnoughContainerForMaterial", materialId)
          } else {
            short = true
          }
        }

        line["callContainerQty"] = qty - restQty
        changedLines += line
      }

      // 没有产生新的装货
      if (plans.isEmpty()) return

      val putOrders = plans.keys.map { containerId ->
        val putOrder: EntityValue = mutableMapOf(
          "container" to containerId,
          "sourceOrderId" to orderId,
          FieldMeta.FIELD_ORDER_STATE to "Todo",
          FieldMeta.FIELD_BZ_KIND to "NormalTask",
          FieldMeta.FIELD_LINES to plans[containerId]
        )
        putOrder
      }
      // 持久化任务
      EntityRwService.createMany("PutinContainerOrder", putOrders)
      logger.info("创建装货单：$putOrders")

      // 锁容器
      val containerIds = plans.keys.toList()
      EntityRwService.updateMany(
        "FbContainer",
        Cq.include("id", containerIds),
        mutableMapOf("locked" to true, "taskType" to "Put")
      )

      // 更新入库单行
      for (line in changedLines) {
        val lineId = EntityHelper.mustGetId(line)
        val lineNo = line[FieldMeta.FIELD_LINE_NO] as Int
        val callContainerQty = line["callContainerQty"]
        logger.debug("入库单行完成叫空容器数量修改 $orderId : $lineNo = $callContainerQty")
        EntityRwService.updateOne(
          "FbInboundOrderLine", Cq.idEq(lineId), mutableMapOf("callContainerQty" to callContainerQty)
        )
      }

      // 更新入库单状态
      if (!short) {
        logger.info("入库单已全部找到空容器 $orderId")
        EntityRwService.updateOne("FbInboundOrder", Cq.idEq(orderId), mutableMapOf("callContainerAll" to true))
      }
    }
  }

  fun getCopyFields(): List<String> {
    val fieldsStr = BzConfigManager.getByPath("ScWms", "callContainer", "copyFields") as String?
    var fields = StringHelper.splitTrim(fieldsStr, ",").toMutableList()
    if (fields.isEmpty()) fields = mutableListOf(FieldMeta.FIELD_MATERIAL) // 默认按物料来
    else if (!fields.contains(FieldMeta.FIELD_MATERIAL)) fields += FieldMeta.FIELD_MATERIAL // 一定要有物料
    return fields
  }

  // 加载 物料装容器相关参数
  private fun loadMaterialParams(materialIds: List<String>): Map<String, MaterialParam> {
    val mpMap: MutableMap<String, MaterialParam> = HashMap()
    for (materialId in materialIds) {
      if (mpMap.contains(materialId)) continue

      val materialEv = EntityRwService.findOneById("FbMaterial", materialId)
        ?: throw BzError("errBzNoMaterialById", materialId)

      val leafCategory = materialEv["leafCategory"] as String?
      if (leafCategory.isNullOrEmpty()) throw BzError("errBzMaterialNoCategory", materialId)
      val leafCategoryEv = EntityRwService.findOneById("FbMaterialCategory", leafCategory)
        ?: throw BzError("errBzNoMaterialCategoryById", leafCategory)

      @Suppress("UNCHECKED_CAST")
      val storeDistricts = leafCategoryEv["storeDistricts"] as List<String>?
      if (storeDistricts.isNullOrEmpty())
        throw BzError("errBzMaterialCategoryNoStoreDistricts", leafCategory)

      val mqList = EntityRwService.findMany(
        "FbMaterialContainerMaxQty",
        Cq.and(listOf(Cq.eq(FieldMeta.FIELD_MATERIAL, materialId), Cq.ne(FieldMeta.FIELD_DISABLED, true)))
      )
      if (mqList.isEmpty()) throw BzError("errBzNoMaterialContainerMaxQty", materialId)

      val mQMap: MutableMap<String, MaterialContainerMaxQty> = HashMap()
      val containerTypes: MutableList<String> = ArrayList()
      for (ev in mqList) {
        val containerType = ev["containerType"] as String? ?: continue
        val maxQty = NumHelper.anyToInt(ev["maxQty"]) ?: continue
        if (maxQty <= 0) continue

        if (!containerTypes.contains(containerType)) containerTypes += containerType
        mQMap[containerType] = MaterialContainerMaxQty(containerType, maxQty)
      }
      if (mQMap.isEmpty()) throw BzError("NoMaterialContainerMaxQty2", materialId)

      mpMap[materialId] = MaterialParam(materialId, leafCategory, storeDistricts, containerTypes, mQMap)
    }

    return mpMap
  }

  private fun loadContainerParams(): Map<String, ContainerParam> {
    val cpMap: MutableMap<String, ContainerParam> = HashMap()

    val containerEvList = EntityRwService.findMany("FbContainer", Cq.all())
    for (containerEv in containerEvList) {
      if (containerEv[FieldMeta.FIELD_DISABLED] == true || containerEv["locked"] == true) continue
      val containerId = EntityHelper.mustGetId(containerEv)
      val containerType = containerEv["type"] as String? ?: continue

      val containerTypeEv = EntityRwService.findOneById("FbContainerType", containerType) ?: continue
      var subNum = NumHelper.anyToInt(containerTypeEv["subNum"]) ?: continue
      if (subNum <= 0) subNum = 1

      var storeBin: String? = null
      var storeDistrict: String? = null
      // TODO 容器到库位的效率优化
      val binEv = EntityRwService.findOne("FbBin", Cq.eq("container", containerId))
      if (binEv != null) {
        storeBin = EntityHelper.mustGetId(binEv)
        storeDistrict = binEv["district"] as String?
      }

      // TODO 效率容易
      val invLayouts = EntityRwService.findMany("FbInvLayout", Cq.eq(FieldMeta.FIELD_LEAF_CONTAINER, containerId))

      var totalQty = 0.0 // 物料总个数
      val subInv: MutableMap<Int, MutableList<EntityValue>> = HashMap()
      val materialSet = mutableSetOf<String>()
      val materialCategorySet = mutableSetOf<String>()

      for (il in invLayouts) {
        val subNo = il[FieldMeta.FIELD_SUB_CONTAINER_ID] as Int? ?: 1
        val qty = NumHelper.anyToDouble(il["qty"]) ?: 0.0
        if (qty <= 0) continue
        totalQty += qty
        val subInvLayouts = subInv.getOrPut(subNo) { ArrayList() }
        subInvLayouts += invLayouts

        val materialId = il[FieldMeta.FIELD_MATERIAL] as String? ?: continue
        materialSet += materialId

        val materialEv = EntityRwService.findOneById("FbMaterial", materialId)
          ?: throw BzError("errBzNoMaterialById", materialId)
        val leafCategory = materialEv["leafCategory"] as String?
        if (!leafCategory.isNullOrBlank()) materialCategorySet += leafCategory
      }
      // 已用的子容器数量
      val usedSubNum = subInv.size

      cpMap[containerId] = ContainerParam(
        containerId, containerType, subNum, storeDistrict, storeBin,
        invLayouts, subInv, materialSet, materialCategorySet, totalQty, usedSubNum
      )
    }

    return cpMap
  }

  override fun beforeCreating(em: EntityMeta, evList: List<EntityValue>): List<String>? {
    if (em.name != "CallEmptyContainerOrder") return null
    for (ev in evList) {
      val district = ev["district"] as String?
      if (district.isNullOrBlank()) throw BzError("errBzMissingKeyParam", "district")
      val num = NumHelper.anyToInt(ev["num"])
      if (num == null || num <= 0) throw BzError("errBzMissingKeyParam", "num")
      callEmptyContainerFromDistrict(district, num)
    }
    return null
  }

  /**
   * 创建装货单
   * 锁定容器；任务状态改为装货
   */
  private fun callEmptyContainerFromDistrict(districtId: String, num: Int) {
    val containerIds = ContainerService.findEmptyContainersInDistrict(districtId, num)

    doPrepareCallContainer(containerIds)
  }

  /**
   * 指定容器出库装货
   * 创建装货单
   * 锁定容器；任务状态改为装货
   */
  fun callDirectContainers(containerIds: List<String>) {
    ResLockService.resLock.withLock {
      for (containerId in containerIds) {
        val containerEv = EntityRwService.findOneById("FbContainer", containerId)
          ?: throw BzError("errNoSuchContainerById", containerId)
        if (containerEv["locked"] == true) throw BzError("errContainerLocked", containerId)
      }

      doPrepareCallContainer(containerIds)
    }
  }

  private fun doPrepareCallContainer(containerIds: List<String>) {
    val orders: List<EntityValue> = containerIds.map {
      mutableMapOf(
        "container" to it,
        FieldMeta.FIELD_ORDER_STATE to "Todo",
        FieldMeta.FIELD_BZ_KIND to "NormalTask",
        FieldMeta.FIELD_LINES to emptyList<EntityValue>() // 单行为空
      )
    }
    EntityRwService.createMany("PutinContainerOrder", orders)

    // 锁容器、任务状态
    EntityRwService.updateMany(
      "FbContainer",
      Cq.include("id", containerIds),
      mutableMapOf("locked" to true, "taskType" to "Put")
    )
  }

}

data class CallContainerByOrderReq(
  val orderId: String,
  val shortOption: ShortOption,
)

enum class ShortOption {
  Stop, Part
}

/**
 * 物料装容器相关参数
 */
data class MaterialParam(
  val materialId: String,
  val leafCategory: String,
  val storeDistricts: List<String>,
  val containerTypes: List<String>, // 允许存放的容器类型
  val maxQty: Map<String, MaterialContainerMaxQty>, // containerType ->
)

data class MaterialContainerMaxQty(
  val containerType: String,
  val maxQty: Int
)

data class ContainerParam(
  val containerId: String,
  val containerType: String,
  val subNum: Int, // 子容器数
  val storeDistrict: String?,
  val storeBin: String?,
  // used
  val invLayouts: List<EntityValue>, // 容器内所有库存
  val subInv: MutableMap<Int, MutableList<EntityValue>>, // 有库存的格子的库存
  val materialSet: Set<String>, // 已有物料
  val materialCategorySet: Set<String>, // 已有物料类别
  var totalQty: Double, // 物料总数量
  val usedSumNum: Int
)

data class ContainerParamSortWeight(
  val cp: ContainerParam,
  val sortWeights: MutableList<Double> = ArrayList(), // 权重
)

data class CallContainerPlan(
  val mixKey: String,
  val materialId: String,
  val feature: Map<String, Any?>, // 额外的筛选字段
  var restQty: Double, // 待装数量
  var assignedQty: Double = 0.0, // 实际成功分配的数量
  var qty: Double, // 入库数
  var callContainerQty: Double, // 已叫
  val sources: MutableList<OrderLineSource>, // 来源单据单行
)