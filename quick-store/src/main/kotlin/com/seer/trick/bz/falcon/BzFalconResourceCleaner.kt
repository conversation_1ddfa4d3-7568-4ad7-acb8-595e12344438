package com.seer.trick.bz.falcon

import com.seer.trick.Cq
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.falcon.task.FalconTaskService
import org.slf4j.LoggerFactory

object BzFalconResourceCleaner {

  private val logger = LoggerFactory.getLogger(this::class.java)

  fun init() {
    FalconTaskService.addResCleaner("FindNotOccupiedBin", BzFalconResourceCleaner::unlockBin)
  }

  @Suppress("UNUSED_PARAMETER")
  private fun unlockBin(
    taskId: String, resType: String, resId: String, args: Map<String, Any?>?
  ) {
    if (args == null) {
      logger.error("解锁库位，args为空")
      return
    }
    val binId = args["binId"] as String
    logger.info("解锁库位，库位=$binId，猎鹰任务编号=$taskId")

    EntityRwService.updateOne("FbBin", Cq.idEq(binId), mutableMapOf("locked" to false))
  }
}