package com.seer.trick.bz.wms

import com.seer.trick.BzError
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService

object MaterialManager {

  /**
   * 从单据单行中抽出所有物料编号
   */
  fun listMaterialIdsFromOrderLines(order: EntityValue): List<String> {
    val lines = EntityHelper.getLines(order, FieldMeta.FIELD_LINES)
    if (lines.isNullOrEmpty()) return emptyList()

    val materialIds = mutableListOf<String>()
    for (line in lines) {
      val matId = line[FieldMeta.FIELD_MATERIAL] as String?
      if (matId.isNullOrBlank()) continue
      if (!materialIds.contains(matId)) materialIds += matId
    }

    return materialIds
  }

  /**
   * 根据物料编号，填充物料名称、规格等物料字段
   */
  fun fillMaterialFieldsIntoLine(materialId: String, line: EntityValue) {
    val matEv = EntityRwService.findOneById("FbMaterial", materialId)
      ?: throw BzError("errBzNoMaterialById", materialId)
    val category1 = matEv["category1"] as String?
    if (!category1.isNullOrEmpty()) {
      EntityRwService.findOneById("FbMaterialCategory", category1)?.let {
        line[FieldMeta.FIELD_MATERIAL_TOP_CATEGORY_NAME] =
          it["name"]
      }
    }
    line[FieldMeta.FIELD_MATERIAL_ID] = matEv["id"]
    line[FieldMeta.FIELD_MATERIAL_NAME] = matEv["name"]
    line[FieldMeta.FIELD_MATERIAL_MODEL] = matEv["model"]
    line[FieldMeta.FIELD_MATERIAL_SPEC] = matEv["spec"]
    line[FieldMeta.FIELD_MATERIAL_CATEGORY] = matEv["leafCategory"]
    line[FieldMeta.FIELD_MATERIAL_CATEGORY_NAME] = matEv["name"]
    // FIXME 最外层物料分类取的不对
    line[FieldMeta.FIELD_MATERIAL_TOP_CATEGORY] = matEv["category1"]

    // line[FieldMeta.FIELD_MATERIAL_CATEGORY_NAME] = matEv["name"]
    line[FieldMeta.FIELD_MATERIAL_IMAGE] = matEv["image"]
  }
}