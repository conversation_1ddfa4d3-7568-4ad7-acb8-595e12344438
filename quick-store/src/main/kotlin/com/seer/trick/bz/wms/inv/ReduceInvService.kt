package com.seer.trick.bz.wms.inv

import com.seer.trick.BzError
import com.seer.trick.Cq

import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.reslock.ResLockService
import com.seer.trick.helper.NumHelper
import org.slf4j.LoggerFactory
import kotlin.concurrent.withLock

/**
 * 减少库存
 */
object ReduceInvService {
  
  private val logger = LoggerFactory.getLogger(this::class.java)
  
  /**
   * 从指定库位（可以附加指定格子）扣减指定物料的库存
   */
  fun reduceInvFromBin(req: ReduceInvFromBinReq) {
    val filterItems = mutableListOf(
      Cq.eq("bin", req.bin),
      Cq.eq(FieldMeta.FIELD_MATERIAL, req.material)
    )
    if (req.subContainerId != null && req.subContainerId > 0) {
      filterItems += Cq.eq(FieldMeta.FIELD_SUB_CONTAINER_ID, req.subContainerId)
    }
    if (!req.moreMatches.isNullOrEmpty()) {
      for ((f, v) in req.moreMatches) filterItems += Cq.eq(f, v)
    }
    
    ResLockService.resLock.withLock {
      val layouts = EntityRwService.findMany(
        "FbInvLayout", Cq.and(filterItems), FindOptions(sort = listOf("-inboundOn", "-createdOn"))
      )
      val invQty = layouts.sumOf { NumHelper.anyToDouble(it["qty"]) ?: 0.0 }
      if (invQty < req.qty) {
        throw BzError("errInvShort2", req.material, req.qty, invQty)
      }
      var restQty = req.qty
      for (layout in layouts) {
        val lyQty = NumHelper.anyToDouble(layout["qty"]) ?: continue
        if (restQty >= lyQty) {
          val lyId = EntityHelper.mustGetId(layout)
          logger.info("出库扣减整条库存，id=$lyId，库存数量=$lyQty，请求=$req")
          EntityRwService.removeOne("FbInvLayout", Cq.idEq(lyId))
          restQty -= lyQty
        } else {
          val lyId = EntityHelper.mustGetId(layout)
          logger.info("出库扣减部分库存，id=$lyId，库存数量=$lyQty，扣减=$restQty，请求=$req")
          EntityRwService.updateOne("FbInvLayout", Cq.idEq(lyId), mutableMapOf("qty" to (lyQty - restQty)))
          break
        }
      }
    }
  }
  
}

data class ReduceInvFromBinReq(
  val material: String,
  val qty: Double,
  val bin: String,
  val subContainerId: Int? = null,
  val outboundOrderId: String,
  val pickOrderId: String,
  val moreMatches: Map<String, Any>? = null,
)