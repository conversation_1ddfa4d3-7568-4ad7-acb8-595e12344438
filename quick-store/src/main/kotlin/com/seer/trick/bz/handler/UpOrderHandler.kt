package com.seer.trick.bz.handler

import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.getReqBody
import com.seer.trick.base.http.operator

import com.seer.trick.bz.order.CalcOccurredQtyReq
import com.seer.trick.bz.order.FetchUpOrderReq
import com.seer.trick.bz.order.OrderQtyService
import com.seer.trick.bz.order.UpOrderService
import io.javalin.http.Context

/**
 * 拉取上游单据
 */
object UpOrderHandler {
  
  fun registerHandlers() {
    val c = Handlers("api/order/pull-up")
    c.post("fetch", ::fetchUpOrder, auth())
    c.post("occurred-qty", ::calA, auth())
  }
  
  private fun fetchUpOrder(ctx: Context) {
    
    val req: FetchUpOrderReq = ctx.getReqBody()
    
    val downOrderEv = UpOrderService.fetchUpOrder(req)
    ctx.json(downOrderEv)
  }
  
  private fun calA(ctx: Context) {
    
    val req: CalcOccurredQtyReq = ctx.getReqBody()
    
    val r = OrderQtyService.calcOccurredQty(req)
    ctx.json(r)
  }
  
}