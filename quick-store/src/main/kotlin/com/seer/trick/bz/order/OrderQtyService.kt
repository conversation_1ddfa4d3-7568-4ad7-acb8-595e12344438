package com.seer.trick.bz.order

import com.seer.trick.*
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.helper.NumHelper

object OrderQtyService {

  /**
   * 计算已发生数量
   * 如果下游单据未保存 downOrderId 为 null
   */
  fun calcOccurredQty(req: CalcOccurredQtyReq): List<Double> {
    val upOrderEv = EntityRwService.findOne(req.upOrderName, Cq.idEq(req.upOrderId))
      ?: throw BzError("errBzNoSuchOrderNameId", req.upOrderName, req.upOrderId)

    // TODO 支持下推到多个业务对象
    // 找目前所有下推
    val downOrderEvList = EntityRwService.findMany(
      req.downOrderName, Cq.eq(FieldMeta.FIELD_SOURCE_ORDER_ID, req.upOrderId),
    )

    val lines: MutableList<Double> = EntityHelper.mustGetLines(upOrderEv, FieldMeta.FIELD_LINES)
      .map { 0.0 }.toMutableList()

    val downOrderEm = BaseCenter.mustGetEntityMeta(req.downOrderName)
    val thisQtyFields = downOrderEm.orderConfig?.thisQtyFields ?: return lines

    for (downOrderEv in downOrderEvList) {
      if (downOrderEv[FieldMeta.FIELD_ID] == req.downOrderId) continue

      val downLines = EntityHelper.mustGetLines(downOrderEv, FieldMeta.FIELD_LINES)
      for (downLine in downLines) {
        if (downLine[FieldMeta.FIELD_SOURCE_ORDER_ID] != req.upOrderId) continue

        val upLineNo = downLine[FieldMeta.FIELD_SOURCE_LINE_NO] as Int
        val upLineIndex = upLineNo - 1
        if (upLineIndex < 0 || upLineIndex >= lines.size) continue

        val qty = thisQtyFields.map { fn -> NumHelper.anyToDouble(downLine[fn]) ?: 0.0 }
          .reduce { aac, v -> aac + v }
        val old = NumHelper.anyToDouble(lines[upLineIndex]) ?: 0.0
        lines[upLineIndex] = old + qty
      }
    }

    return lines
  }

}


data class CalcOccurredQtyReq(
  val upOrderName: String,
  val upOrderId: String,
  val downOrderName: String,
  val downOrderId: String? = null
)
