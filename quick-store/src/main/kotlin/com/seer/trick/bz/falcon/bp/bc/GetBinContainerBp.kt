package com.seer.trick.bz.falcon.bp.bc

import com.seer.trick.BzError
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*

class GetBinContainerBp : AbstractBp() {
  
  override fun process() {
    val binId = mustGetBlockInputParam(ipBinId.name) as String
    val bin = EntityRwService.findOneById("FbBin", binId) ?: throw BzError("errNoBinById", binId)
    
    val containerId = bin["container"] as String?
    if (containerId.isNullOrBlank()) {
      setBlockOutputParams(mapOf(opBinEmpty.name to true, opContainerId.name to null))
    } else {
      setBlockOutputParams(mapOf(opBinEmpty.name to false, opContainerId.name to containerId))
    }
  }
  
  companion object {
    val ipBinId = BlockInputParamDef(
      "binId", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.BinId)
    )
    
    val opBinEmpty = BlockOutputParamDef("binEmpty", BlockParamType.Boolean)
    val opContainerId = BlockOutputParamDef(
      "containerId", BlockParamType.String, objectTypes = listOf(ParamObjectType.ContainerId)
    )
    
    val def = BlockDef(
      GetBinContainerBp::class.simpleName!!,
      color = "#f8fdcb",
      inputParams = listOf(ipBinId), outputParams = listOf(
        opBinEmpty, opContainerId
      )
    )
  }
  
}