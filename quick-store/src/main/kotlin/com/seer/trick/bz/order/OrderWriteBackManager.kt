package com.seer.trick.bz.order

import com.seer.trick.Cq
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.helper.NumHelper
import org.slf4j.Logger
import org.slf4j.LoggerFactory

@Deprecated("Use OrderWriteBackService")
object OrderWriteBackManager {
  
  private val logger: Logger = LoggerFactory.getLogger(this::class.java)
  
  /**
   * 单据反写
   */
  fun tryWriteBack(em: EntityMeta, ids: List<String>) {
    // 是否是单据
    val orderConfig = em.orderConfig
    if (orderConfig?.enabled != true) return
    
    // 查询单据流
    val records = EntityRwService.findMany("OrderFlowRecord", Cq.include("targetOrderId", ids))
    
    val sources = recordsToSources(records)
    for (s in sources) {
      writeBackOneSource(s.sourceOrderName, s.sourceOrderId)
    }
    
    // 多级反写
    val sourceOrderGroups = sources.groupBy { it.sourceOrderName }
    for ((sourceOrderName, orders) in sourceOrderGroups) {
      val srcIds = orders.map { it.sourceOrderId }
      val srcEm = BaseCenter.mustGetEntityMeta(sourceOrderName)
      tryWriteBack(srcEm, srcIds)
    }
  }
  
  // 去重源单
  private fun recordsToSources(records: List<EntityValue>): List<WriteBackSource2> {
    val map: MutableMap<String, WriteBackSource2> = HashMap()
    for (r in records) {
      val sourceOrderName = r["sourceOrderName"] as String
      val sourceOrderId = r["sourceOrderId"] as String
      map[sourceOrderName + sourceOrderId] = WriteBackSource2(sourceOrderName, sourceOrderId)
    }
    return map.values.toList()
  }
  
  private fun writeBackOneSource(sourceOrderName: String, sourceOrderId: String) {
    logger.info("Write back one order, source=$sourceOrderName/$sourceOrderId")
    val sourceOrderEm = BaseCenter.mustGetEntityMeta(sourceOrderName)
    val orderConfig = sourceOrderEm.orderConfig
    val lineEntityName = EntityHelper.mustGetLineEntityName(sourceOrderEm)
    
    // 先找这个单据目前所有的下推记录
    val q = Cq.and(
      listOf(
        Cq.eq("sourceOrderName", sourceOrderName), Cq.eq("sourceOrderId", sourceOrderId)
      )
    )
    val allDownRecords = EntityRwService.findMany("OrderFlowRecord", q)
    
    logger.info("Write back，down orders num=${allDownRecords.size}")
    
    if (allDownRecords.isEmpty()) return
    
    // 源单单行
    val sourceLines = EntityRwService.findMany(
      lineEntityName,
      Cq.eq(FieldMeta.FIELD_PARENT_ID, sourceOrderId),
      FindOptions(sort = listOf(FieldMeta.FIELD_LINE_NO))
    )
    
    val updateLines: List<EntityValue> = sourceLines.map { mutableMapOf() }
    
    for (record in allDownRecords) {
      val targetOrderName = record["targetOrderName"] as String
      val targetEm = BaseCenter.mustGetEntityMeta(targetOrderName)
      val targetLineEntityName = EntityHelper.mustGetLineEntityName(targetEm)
      val targetOrderId = record["targetOrderId"] as String
      val targetOrderType = record["targetOrderType"] as String
      
      // 找对应的配置
      val po = orderConfig?.pushOrders?.find { po ->
        po.downOrderName == targetOrderName && po.downOrderKind == targetOrderType
      }
      if (po == null) {
        logger.error("No push config, down order=$targetOrderType/$targetOrderName/$targetOrderId")
        continue
      }
      if (po.lineWriteBackMapping.isEmpty()) {
        logger.info("Not write back, down order=$targetOrderType/$targetOrderName/$targetOrderId")
        continue
      }
      
      val downOrder = EntityRwService.findOne(
        targetOrderName,
        Cq.idEq(targetOrderId),
        FindOptions(projection = listOf(FieldMeta.FIELD_ORDER_STATE))
      )
      if (downOrder == null) {
        logger.error("No down order in DB, $targetOrderType/$targetOrderName/$targetOrderId")
        continue
      }
      
      val downOrderState = downOrder[FieldMeta.FIELD_ORDER_STATE] as String
      
      if (po.downOrderBackStates.indexOf(downOrderState) < 0) {
        logger.info("Down order state not for writing back, down order=$targetOrderId state=$downOrderState")
        continue
      }
      
      // 下游单行要求相等、对应
      val downLines = EntityRwService.findMany(
        targetLineEntityName,
        Cq.eq(FieldMeta.FIELD_PARENT_ID, targetOrderId),
        FindOptions(sort = listOf(FieldMeta.FIELD_LINE_NO))
      )
      if (sourceLines.size != downLines.size) {
        logger.error("Failed to write back, lines not match, down = [${downLines.size}] up = [${sourceLines.size}]")
        continue
      }
      downLines.forEachIndexed { i, downLine ->
        for (m in po.lineWriteBackMapping) {
          val dq = NumHelper.anyToDouble(downLine[m.downField]) ?: 0.0
          val old = NumHelper.anyToDouble(updateLines[i][m.upField]) ?: 0.0
          updateLines[i][m.upField] = old + dq
        }
      }
    }
    
    sourceLines.forEachIndexed { i, sourceLine ->
      val lineId = sourceLine["id"] as String
      val update = updateLines[i]
      if (update.isNotEmpty()) {
        logger.info("DO write back [$i]#${lineId} $update")
        EntityRwService.updateOne(lineEntityName, Cq.idEq(lineId), update)
      }
    }
    
    // 计算汇总字段
    val sourceOrder = EntityRwService.findOne(sourceOrderName, Cq.idEq(sourceOrderId))
    if (sourceOrder != null) {
      val update = EntityRwService.calcSumFieldsUpdate(sourceOrder, sourceOrderEm)
      if (update.isNotEmpty()) {
        EntityRwService.updateOne(sourceOrderName, Cq.idEq(sourceOrderId), update)
      }
    }
  }
}

data class WriteBackSource2(
  val sourceOrderName: String,
  val sourceOrderId: String
)
