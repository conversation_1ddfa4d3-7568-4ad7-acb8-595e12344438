package com.seer.trick.bz.wms

import com.seer.trick.base.entity.EntityValue
import java.util.*

data class StoreLocations(
  val warehouse: String? = null,
  val district: String? = null,
  val bin: String? = null,
)

data class Partners(
  val vendor: String? = null,
  val owner: String? = null,
)

data class InvFeature(
  val matLotNo: String? = null,
  val matSerialNo: String? = null,
)

data class PriceAmount(
  val price: Double? = null,
  val amount: Double? = null,
)

data class Exp(
  val expDate: Date? = null, // 到期日期
  val mfgDate: Date? = null, // 生产日期
)

data class AssignInvReq(
  val material: String,
  val qty: Double,
  val outboundOrderId: String,
  val outboundOrderLineNo: Int,
  val filter: AssignInvFilter,
  val sort: List<AssignInvSort>
)

class AssignInvFilter(
  val state: List<String>? = null,
  //
  val warehouse: List<String>? = null,
  val district: List<String>? = null,
  val bin: List<String>? = null,
  //
  val vendor: List<String>? = null, // 供应商
  val owner: List<String>? = null, // 货主,
  
  val core: InvFeature = InvFeature()
)

data class AssignInvSort(
  val type: AssignInvSortType? = null,
  val values: List<String>? = null
)

enum class AssignInvSortType {
  InboundOn, Warehouse, District, Bin
}

// 库存分配结果
data class InvAssignment(
  val request: AssignInvReq,// 对应请求
  var short: Boolean, // 是否缺货
  var shortQty: Double,
  val items: MutableList<InvAssignmentItem> // 分配明细
)

data class InvAssignmentItem(
  val invLayout: EntityValue,
  val qty: Double // 分配数量
)

data class InvLayoutUsed(
  val invLayout: EntityValue,
  var unused: Double // 剩余
)

data class OrderLineSource(
  @JvmField
  val orderId: String,
  @JvmField
  val lineId: String,
  @JvmField
  val lineNo: Int,
)
