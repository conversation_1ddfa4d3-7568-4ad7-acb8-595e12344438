package com.seer.trick.robot.rachel.adapter

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef

import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.robot.map.SceneMapManager
import com.seer.trick.robot.rachel.MrCmdResult
import com.seer.trick.robot.rachel.MrCmdResultKind
import com.seer.trick.robot.rachel.MrRobotRuntime
import com.seer.trick.robot.rachel.MrStep
import com.seer.trick.robot.tom.CreateTomOrderReq
import com.seer.trick.robot.tom.TerminateReq
import com.seer.trick.robot.tom.TomAgent
import com.seer.trick.robot.tom.TomBlock
import org.slf4j.LoggerFactory

class MrRobotAdapterTom(private val tomUrlRoot: String) : MrRobotAdapter() {

  private val logger = LoggerFactory.getLogger(this::class.java)

  override fun sendCmd(sceneMap: SceneMapManager, rr: MrRobotRuntime, step: MrStep): MrCmdResult {
    val tomOrderId = IdHelper.oidStr()
    rr.currentTomOrderId = tomOrderId

    logger.info("发送调度运单，机器人 ${rr.id}，通用运单${step.orderId}:${step.stepIndex}，调度运单=$tomOrderId")

    var block: TomBlock = if (step.rbkArgs.isNullOrBlank())
      TomBlock()
    else
      JsonHelper.mapper.readValue(step.rbkArgs, jacksonTypeRef())

    block = block.copy(blockId = "$tomOrderId-S1", location = step.location.site!!)

    // TODO 检查机器人当前空闲

    try {
      TomAgent.createOrder(
        tomUrlRoot,
        CreateTomOrderReq(
          id = tomOrderId,
          vehicle = rr.id,
          complete = true,
          blocks = listOf(block)
        )
      )
    } catch (e: InterruptedException) { // TODO 中断
      return MrCmdResult(MrCmdResultKind.Cancelled)
    } catch (e: Exception) {
      return MrCmdResult(MrCmdResultKind.Failed, e.message)
    }

    try {
      while (!Thread.interrupted()) {
        val res = try {
          TomAgent.queryOrder(tomUrlRoot, tomOrderId)
        } catch (e: Exception) {
          return MrCmdResult(MrCmdResultKind.Failed, e.message)
        }
        if (res == null) {
          return MrCmdResult(MrCmdResultKind.Failed, "找不到调度运单 '$tomOrderId'")
        }

        val state = res.state
        if (state == "FINISHED") {
          return MrCmdResult(MrCmdResultKind.Ok)
        } else if (state == "FAILED") {
          return MrCmdResult(MrCmdResultKind.Failed, "调度运单失败 '$tomOrderId'")
        } else if (state == "STOPPED") {
          return MrCmdResult(MrCmdResultKind.Cancelled, "调度运单被人为终止 '$tomOrderId'")
        }
        // 已创建=CREATED，
        // 等待=WAITING(block为空或者所有block都已经做完但是因为complete为false导致一直在等待新的block)，
        // 正在执行=RUNNING，
        // 暂停=SUSPENDED，
        // 完成=FINISHED，
        // 失败=FAILED(主动失败)，
        // 终止=STOPPED(被人为终止)，
        // 无法执行=Error(参数错误)，
        // 待分配=TOBEDISPATCHED
        try {
          Thread.sleep(500)
        } catch (e: InterruptedException) {
          return MrCmdResult(MrCmdResultKind.Cancelled)
        }
      }

      return MrCmdResult(MrCmdResultKind.Cancelled)
    } finally {
      // 清当前单号记录
//      rr.currentTomOrderId = null
    }
  }

  override fun cancelCmd(rr: MrRobotRuntime) {
    logger.info("取消机器人当前步，机器人 ${rr.id}")

    try {
      // TODO 为什么为 null
      val step = rr.getCurrentStep() ?: return

      val tomOrderId = rr.currentTomOrderId
      if (tomOrderId.isNullOrBlank()) {
        logger.error("取消机器人当前步，当前步：${step.orderId}:${step.stepIndex}，但调度运单为空")
      } else {
        logger.info("取消机器人当前步，当前步：${step.orderId}:${step.stepIndex}，调度运单=$tomOrderId")
        TomAgent.terminate(tomUrlRoot, TerminateReq(listOf(tomOrderId)))
        rr.currentTomOrderId = null
      }
    } catch (e: Exception) {
      logger.error("取消 CORE 命令", e)
    }
  }

}