package com.seer.trick.robot.single.bp


import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockInputParamOption
import com.seer.trick.falcon.domain.BlockOutputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import java.math.BigDecimal
import java.math.RoundingMode

class RobotSingleAngleRadianBp : AbstractBp() {

  override fun process() {
    val inputDegrees = mustGetBlockInputParam("degrees") as Double

    val degrees180 = BigDecimal("180")
    val pi = BigDecimal(Math.PI.toString())
    val outputDegrees: Double =
      when(val conversionType = mustGetBlockInputParam("conversionType") as String) {
      "RadianToAngle" -> {
        degrees180
          .multiply(BigDecimal(inputDegrees))
          .divide(pi, 2,  RoundingMode.HALF_UP)
          .toDouble()
      }
      "AngleToRadian" -> {
        pi.multiply(BigDecimal(inputDegrees))
          .divide(degrees180, 2, RoundingMode.HALF_UP)
          .toDouble()
      }
      else ->
        throw IllegalArgumentException("Unknown conversionType: $conversionType")
    }

    setBlockOutputParams(mapOf("degrees" to outputDegrees))
  }

  companion object {

    val def = BlockDef(
      RobotSingleAngleRadianBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(
        BlockInputParamDef("degrees", BlockParamType.Double, true),
        BlockInputParamDef(
          "conversionType", BlockParamType.String, true,
          options = listOf(
            BlockInputParamOption("RadianToAngle"),
            BlockInputParamOption("AngleToRadian")
          )
        )
      ),
      outputParams = listOf(BlockOutputParamDef("degrees", BlockParamType.Double))
    )
    
  }
}