package com.seer.trick.robot


import com.seer.trick.base.AppModule
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.stats.manila.ManilaReportService
import com.seer.trick.falcon.FalconCenter
import com.seer.trick.falcon.domain.BlockDefGroup
import com.seer.trick.robot.falcon.RobotFalconResourceCleaner
import com.seer.trick.robot.falcon.bp.cto.MarkContainerTransportOrderDoneBp
import com.seer.trick.robot.falcon.bp.cto.UpdateContainerTransportOrderBp
import com.seer.trick.robot.falcon.bp.ndc.*
import com.seer.trick.robot.falcon.tom.*
import com.seer.trick.robot.gw.GwCenter
import com.seer.trick.robot.handler.*
import com.seer.trick.robot.script.ScriptRobot
import com.seer.trick.robot.single.bp.*
import com.seer.trick.robot.stats.RobotStatsService
import com.seer.trick.robot.stats.custom.RobotRateStats
import com.seer.trick.robot.vendor.hai.HaiAdapter
import com.seer.trick.robot.vendor.hik.HikAdapter
import com.seer.trick.robot.vendor.ndc.NdcAdapter
import com.seer.trick.robot.vendor.seer.MrBinRobotArgsManager
import org.graalvm.polyglot.Value

object RobotModule : AppModule() {

  override fun afterDb() {
    MrBinRobotArgsManager.parse()
    RobotStatsService.init()
  }

  override fun beforeScript() {
    RobotFalconResourceCleaner.init()

    GwWsServer.init()
    val haiAdapter = BzConfigManager.getByPath("ScWcs", "hai", "haiAdapter") == true
    if (haiAdapter) HaiAdapter.init()

    val hikAdapter = BzConfigManager.getByPath("ScWcs", "hik", "hikAdapter") == true
    if (hikAdapter) HikAdapter.start()

    val ndcRCS = BzConfigManager.getByPath("ScWcs", "ndc", "ndcRCS") == true
    if (ndcRCS) NdcAdapter.start()
  }

  override fun afterScript() {
    RobotAppManager.init()
    RobotRateStats.init()
  }

  override fun registerHttpHandlers() {
    RobotAppHandler.registerHandlers()
    RaSingleAppHandler.registerHandlers()
    RaTomAppHandler.registerHandlers()
    MapHandler.registerHandlers()
    RobotStatsHandler.registerHandlers()

    GwHandler.registerHandlers()
    StoHandler.registerHandlers()
    NdcHandler.registerHandlers()
    YoubeiHandler.registerHandlers()

    RaRachelRobotHandler.registerHandlers()

    RaRachelOrderHandler.registerHandlers()
    HaiHandler.registerHandlers()
    HikHandler.registerHandlers()

    RobotWsManager.registerHandlers()
  }

  override fun dispose() {
    
    RobotAppManager.dispose()

    GwCenter.dispose()

    GwWsServer.dispose()

    HaiAdapter.dispose()

    NdcAdapter.dispose()

    RobotStatsService.dispose()

    RobotRateStats.dispose()
  }

  override fun registerMoreBp() {
    val singleBlocks = listOf(
      FalconCenter.registerBp(RobotSingleNavigationBp::class, RobotSingleNavigationBp.def),
      FalconCenter.registerBp(RobotSingleTranslationBp::class, RobotSingleTranslationBp.def),
      FalconCenter.registerBp(RobotSingleRotateBp::class, RobotSingleRotateBp.def),
      FalconCenter.registerBp(RobotSingleForkBp::class, RobotSingleForkBp.def),
      FalconCenter.registerBp(RobotSingleJackingBp::class, RobotSingleJackingBp.def),
      FalconCenter.registerBp(RobotSinglePlayAudioBp::class, RobotSinglePlayAudioBp.def),
      FalconCenter.registerBp(RobotSingleStopAudioBp::class, RobotSingleStopAudioBp.def),
      FalconCenter.registerBp(RobotSingleUserDefinedBp::class, RobotSingleUserDefinedBp.def),
      FalconCenter.registerBp(RobotSingleRollerBp::class, RobotSingleRollerBp.def),
      FalconCenter.registerBp(RobotSingleAngleRadianBp::class, RobotSingleAngleRadianBp.def),
      FalconCenter.registerBp(RobotSingleWaitDIBp::class, RobotSingleWaitDIBp.def),
      FalconCenter.registerBp(RobotSingleSetDOBp::class, RobotSingleSetDOBp.def),
      FalconCenter.registerBp(RobotSingleGetAllStatusBp::class, RobotSingleGetAllStatusBp.def),
      FalconCenter.registerBp(RobotSingleGetOneStatusBp::class, RobotSingleGetOneStatusBp.def),
    )
    singleBlocks.forEach { it.color = "rgba(156,147,253,0.85)" }
    FalconCenter.registerBpGroup(
      BlockDefGroup("RobotSingleControl", "", 35, false, singleBlocks),
    )

    val containerTransportBlocks = listOf(
      FalconCenter.registerBp(UpdateContainerTransportOrderBp::class, UpdateContainerTransportOrderBp.def),
      FalconCenter.registerBp(MarkContainerTransportOrderDoneBp::class, MarkContainerTransportOrderDoneBp.def),
    )
    containerTransportBlocks.forEach { it.color = "rgba(3,205,142,0.6)" }
    FalconCenter.registerBpGroup(
      BlockDefGroup("ContainerTransport", "", 25, false, containerTransportBlocks),
    )

    val seerTomBlockList = listOf(
      FalconCenter.registerBp(CreateTomOrderBp::class, CreateTomOrderBp.def),
      FalconCenter.registerBp(AddTomBlockBp::class, AddTomBlockBp.def),
      FalconCenter.registerBp(CompleteTomOrderBp::class, CompleteTomOrderBp.def),
      FalconCenter.registerBp(RobotSetDOBp::class, RobotSetDOBp.def),
      FalconCenter.registerBp(RobotWaitDIBp::class, RobotWaitDIBp.def),
      FalconCenter.registerBp(RobotReadDIBp::class, RobotReadDIBp.def),
    )
    seerTomBlockList.forEach { it.color = "rgba(3,205,142,0.5)" }
    FalconCenter.registerBpGroup(BlockDefGroup("SeerTom", "", 20, false, seerTomBlockList))

    val ndcBlocks = listOf(
      FalconCenter.registerBp(CreateNdcOrderBp::class, CreateNdcOrderBp.def),
      FalconCenter.registerBp(AllowNdcLoadBp::class, AllowNdcLoadBp.def),
      FalconCenter.registerBp(AllowNdcUnloadBp::class, AllowNdcUnloadBp.def),
      FalconCenter.registerBp(WaitUntilNdcArriveStartBinBp::class, WaitUntilNdcArriveStartBinBp.def),
      FalconCenter.registerBp(WaitUntilNdcArriveEndBinBp::class, WaitUntilNdcArriveEndBinBp.def),
      FalconCenter.registerBp(WaitUntilNdcLoadedBp::class, WaitUntilNdcLoadedBp.def),
      FalconCenter.registerBp(WaitUntilNdcUnloadedBp::class, WaitUntilNdcUnloadedBp.def),
      FalconCenter.registerBp(WaitUntilNdcFinishBp::class, WaitUntilNdcFinishBp.def),
    )
    ndcBlocks.forEach { it.color = "rgba(3,205,142,0.2)" }
    FalconCenter.registerBpGroup(BlockDefGroup("Ndc", "", 40, false, ndcBlocks))
  }

  override fun afterHttp() {
    GwCenter.init()
  }

  override fun putMoreScriptBindings(bindings: Value) {
    bindings.putMember("wcs", ScriptRobot)
  }

  override fun registerStatsChart() {
    ManilaReportService.reportGenerators.add(RobotRateStats::rateStats)
  }
}