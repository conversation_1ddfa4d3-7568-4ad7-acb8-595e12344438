package com.seer.trick.robot.gw

import com.seer.trick.BzError

import com.seer.trick.base.BaseCenter
import com.seer.trick.helper.JsonFileHelper
import com.seer.trick.robot.single.RaSingleManager
import com.seer.trick.robot.sto.StoManager
import org.slf4j.LoggerFactory
import java.io.File
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

object GwCenter {

  private val logger = LoggerFactory.getLogger(GwCenter::class.java)

  val bgCacheExecutor: ExecutorService = Executors.newCachedThreadPool()

  @Volatile
  var gwConfig = GwConfig()
    private set

  private fun getConfigFile(): File = File(BaseCenter.baseConfig.configDir, "gw.json")

  private fun loadConfig(): GwConfig {
    val file = getConfigFile()
    return JsonFileHelper.readJsonFromFile<GwConfig>(file) ?: GwConfig()
  }

  fun saveConfig(config: GwConfig) {
    val file = getConfigFile()
    JsonFileHelper.writeJsonToFile(file, config, pretty = true)

    // 异步执行
    bgCacheExecutor.submit {
      try {
        reset()
      } catch (e: Exception) {
        logger.error("Save config", e)
      }
    }
  }

  @Synchronized
  fun init() {
    gwConfig = loadConfig()
    
    if (!gwConfig.enabled) {
      for (robot in gwConfig.localConfigs) {
        StoManager.removeRobot(robot.name)
      }
      return
    } else {
      logger.info("Start to init GW")
      // 删除缓存，防止界面变更机器人名称后缓存未清理。而直接 clear 整个 stoRobotMap 会导致正在执行的机器人丢失
      val keys = StoManager.stoRobotMap.keys
      val gwRobotNames = gwConfig.localConfigs.map { it.name }
      for (robotName in keys) {
        if (!gwRobotNames.contains(robotName) && robotName != RaSingleManager.DEFAULT_NAME) {
          StoManager.removeRobot(robotName)
        }
      }
      StoManager.init()
    }

    WsServer.init(gwConfig)

    WsClients.init(gwConfig)

    LocalRobots.init(gwConfig)

    HttpServer.init(gwConfig)

    SinglePortRbkServers.init(gwConfig)

    ReportApplyClient.init(gwConfig)

    logger.info("Done to init GW")
  }

  @Synchronized
  fun dispose() {
    logger.info("Dispose GW")
    SinglePortRbkServers.dispose()

    HttpServer.dispose()

    WsServer.dispose()

    WsClients.dispose()

    ReportApplyClient.dispose()

    LocalRobots.dispose()
  }

  private fun reset() {
    logger.info("Reset GW")

    dispose()

    init()
  }

  fun mustGetFirstRobotName(): String {
    val localConfigs = gwConfig.localConfigs
    if (localConfigs.isEmpty()) {
      throw BzError("errNoAnyRobot")
    }
    return localConfigs[0].name
  }
}