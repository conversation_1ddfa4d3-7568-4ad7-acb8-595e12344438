package com.seer.trick.robot.gw


import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.http.WsMsg
import com.seer.trick.base.ws.WsClient
import com.seer.trick.helper.*
import com.seer.trick.robot.gw.GwCenter.bgCacheExecutor
import com.seer.trick.robot.handler.GwRbkResult
import okhttp3.WebSocket
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.util.concurrent.Executors
import java.util.concurrent.Future

/**
 * 连接到外部一个 WS 服务器
 */
object WsClients {

  @Volatile
  var clients: List<GwWsClient> = emptyList()
    private set

  fun init(gwConfig: GwConfig) {
    val configs = gwConfig.webSocketClientConfigs
    if (configs.isEmpty()) return

    val newClients: MutableList<GwWsClient> = ArrayList()
    for (config in configs) {
      if (config.disabled) continue
      if (config.authId.isNullOrBlank() || config.authSecret.isNullOrBlank()) continue
      newClients += GwWsClient(config, config.authId, config.authSecret, gwConfig.localConfigs.map { it.name })
    }
    clients = newClients
  }

  fun dispose() {
    for (client in clients) client.dispose()
  }
}

/**
 * 保持连接。如果连不上，会自动重试。
 */
class GwWsClient(
  config: WebSocketClientConfig,
  private val authId: String,
  private val authSecret: String,
  private val robotNames: List<String>,
) {

  private val logger: Logger = LoggerFactory.getLogger(javaClass)

  private val logHead = "[${config.url}] "

  val client = WsClient(config.url, ::onOpen, ::onMessage)

  @Volatile
  private var registerFuture: Future<*>? = null
  
  /**
   * GW 客户端向服务器注册专用线程
   */
  private val registerLoopExecutor = Executors.newCachedThreadPool()

  init {
    client.init()

    registerFuture = registerLoopExecutor.submitLongRun("GW WS 客户端发送注册", logger, { 5000 }) {
      register()
    }
  }

  fun dispose() {
    registerFuture?.cancel(true)
    client.dispose()
  }

  private fun register() {
    val logOn = BzConfigManager.getByPath("ScDev", "logWsClient") == true
    

    // 注册我这里有哪些机器人
    val register = GwWsClientRegister(authId, authSecret, robotNames)
    val msg = WsMsg.json("GwWsClientRegister", register, id = IdHelper.oidStr())

    if (logOn) logger.debug("[${client.getUrl()}] WebSocket 发送注册请求：$register，reqId=${msg.id}")
    try {
      client.requestNoResp(msg)
    } catch (e: Throwable) {
      logger.error("register error ${e.getTypeMessage()}")
    }
  }

  private fun onOpen(@Suppress("UNUSED_PARAMETER") webSocket: WebSocket) {
    bgCacheExecutor.submitCatch("发送注册", logger) {
      register()
    }
  }

  private fun onMessage(webSocket: WebSocket, msg: WsMsg) {
    if (msg.action == "GwSeerRbkRequest") {
      val req: WsRequest = msg.contentAsType()!!
      

      // TODO onMessage 方法是不是不能被阻塞
      bgCacheExecutor.submit {
        try {
          val httpReq = GwRbkRequest(msg.id, req.robotName, req.apiNo, req.requestStr)

          val gwRbkResult = try {
            val res = LocalRobots.requestRbk(httpReq)
            GwRbkResult(true, msg.id, req.robotName, req.apiNo, res)
          } catch (e: Exception) {
            GwRbkResult(true, msg.id, req.robotName, req.apiNo, "", e.getTypeMessage())
          }

          val reply = WsMsg.json("GwSeerRbkReply", gwRbkResult, id = IdHelper.oidStr(), replyToId = msg.id)
          // logger.debug("Write back reply, reply to ${msg.id}, result kind=${res.kind}, err=${res.errorMsg}")
          webSocket.send(JsonHelper.mapper.writeValueAsString(reply))
          // logger.debug("Write back reply ok? $r")
        } catch (e: Exception) {
          logger.error(logHead + "处理 GwRbkRequest 报错", e)
        }
      }
    }
  }
}

data class GwWsClientRegister(val authId: String, val authSecret: String, val robotNames: List<String>)

data class WsRequest(val robotName: String, val apiNo: Int, val requestStr: String)