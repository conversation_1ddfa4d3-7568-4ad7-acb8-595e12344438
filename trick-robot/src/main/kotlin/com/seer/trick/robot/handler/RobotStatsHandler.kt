package com.seer.trick.robot.handler

import com.seer.trick.base.http.*
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.HttpServerManager.noAuth
import com.seer.trick.base.stats.manila.ManilaReportService
import com.seer.trick.robot.stats.RobotStatsService
import com.seer.trick.robot.stats.domain.RobotStats
import io.javalin.http.Context
import org.slf4j.LoggerFactory

object RobotStatsHandler {
  private val logger = LoggerFactory.getLogger(javaClass)

  fun registerHandlers() {
    val c = Handlers("api/robot")

    // 重新生成报表
    c.post("stats/gen-report", ::genReport, auth())
    // 仿真机器人数据
    c.post("stats/mock-report", ::mockReport, noAuth())
  }

  private fun genReport(ctx: Context) {
    
    logger.info("用户决定重新生成报告")
    ManilaReportService.genReport()
  }

  private fun mockReport(ctx: Context) {
    val req: RobotStats = ctx.getReqBody()
    RobotStatsService.tryRecordRobotStatus(req)
  }
}