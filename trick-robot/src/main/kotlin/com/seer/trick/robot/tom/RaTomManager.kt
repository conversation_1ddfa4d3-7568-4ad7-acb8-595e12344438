package com.seer.trick.robot.tom


import com.seer.trick.robot.RobotAppSceneConfig
import com.seer.trick.robot.RobotAppSceneRuntime
import com.seer.trick.robot.handler.RobotWsManager
import com.seer.trick.robot.rachel.MrRobotCmdStatus
import com.seer.trick.robot.rachel.MrRobotInfoAll
import com.seer.trick.robot.rachel.MrRobotSelfReport

/**
 * 基于二代调度的应用模式。
 */
class RaTomManager(sceneConfig: RobotAppSceneConfig, val sceneRuntime: RobotAppSceneRuntime) {
  
  /**
   * 状态监控
   */
  private val stateAgent = RaTomState(
    sceneConfig.name, sceneConfig.tom.coreUrl, sceneRuntime, { mapAgent.tryUpdateSceneMap(it) }
  )
  
  /**
   * 地图监控
   */
  private val mapAgent = RaTomMap(sceneConfig.name, sceneConfig.tom.coreUrl, sceneRuntime.map)
  
  fun init() {
    stateAgent.init()
  }
  
  fun dispose() {
    stateAgent.dispose()
  }
  
  /**
   * 概述当前信息
   */
  fun digest(): RaTomDigest {
    return RaTomDigest(
      status = stateAgent.tr,
      robotGroupNames = mapAgent.sceneMap?.robotGroup?.map { it.name },
      areaNames = mapAgent.sceneMap?.areas?.map { it.name },
    )
  }

  fun listTomIdleRobots(): List<TomRobotRecord> {
    val all: MutableList<TomRobotRecord> = mutableListOf()
    val d = digest()
    d.status?.robots?.forEach { rr->
      rr.procBusiness
      if(rr.procBusiness || !rr.dispatchable) return@forEach
      all += rr
    }
    return all
  }
  
}

/**
 * 概述当前信息
 */
data class RaTomDigest(
  val status: TomRuntimeRecord?,
  val robotGroupNames: List<String>?,
  val areaNames: List<String>?,
)