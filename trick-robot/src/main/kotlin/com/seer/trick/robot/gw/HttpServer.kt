package com.seer.trick.robot.gw

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.http.getReqBody
import com.seer.trick.helper.DateHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.NumHelper
import com.seer.trick.robot.rachel.RobotVendor
import com.seer.trick.robot.vendor.hik.HikAdapter
import com.seer.trick.robot.vendor.hik.HikRobot
import com.seer.trick.robot.vendor.hik.MoveRobotReq
import io.javalin.Javalin
import io.javalin.http.Context
import org.apache.commons.lang3.time.DateUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.time.Duration
import java.time.temporal.ChronoUnit

/**
 * <PERSON>lin closed 了之后 不能重用，所以还是新起一个实例
 */
object HttpServer {

  private val logger: Logger = LoggerFactory.getLogger(javaClass)

  @Volatile
  private var httpServer: Javalin? = null

  fun init(config: GwConfig) {
    if (!config.httpServerConfig.enabled) return

    var httpServer = httpServer
    if (httpServer == null) {
      httpServer = buildServer()
      HttpServer.httpServer = httpServer
    }

    logger.info("启动网关 HTTP 服务器，端口={}", config.httpServerConfig.port)

    httpServer.post("/api/req-res", HttpServer::handleRbkRequest)

    httpServer.get("/api/robots", HttpServer::listRobots)
    httpServer.get("/api/robots/{id}", HttpServer::fetchRobot)
    httpServer.post("/api/move-robot", HttpServer::moveRobot)

    // v3
    httpServer.post("/api/hik/move/v3", GwHikHandler::move)

    // v4
    httpServer.post("/api/hik/move", GwHikHandler::moveV4)
    httpServer.post("/api/hik/pause", GwHikHandler::pause)
    httpServer.post("/api/hik/resume", GwHikHandler::resume)
    httpServer.post("/api/hik/cancel", GwHikHandler::cancel)
    httpServer.post("/api/hik/jackLoad", GwHikHandler::jackLoad)
    httpServer.post("/api/hik/jackUnload", GwHikHandler::jackUnload)
    httpServer.post("/api/hik/jackMove", GwHikHandler::jackMove)
    httpServer.post("/api/hik/charge", GwHikHandler::charge)
    httpServer.post("/api/hik/changeSlamSetting", GwHikHandler::changeSlamSetting)
    httpServer.post("/api/hik/getSlam/{robotNo}", GwHikHandler::getSlam)

    // v5
    httpServer.post("/api/hik/move/v5", GwHikHandler::moveV5)
    httpServer.post("/api/hik/pause/v5", GwHikHandler::pauseV5)
    httpServer.post("/api/hik/resume/v5", GwHikHandler::resumeV5)
    httpServer.post("/api/hik/jackUnload/v5", GwHikHandler::jackUnloadV5)
    httpServer.post("/api/hik/jackLoad/v5", GwHikHandler::jackLoadV5)
    httpServer.post("/api/hik/jackMove/v5", GwHikHandler::jackMoveV5)
    httpServer.post("/api/hik/charge/v5", GwHikHandler::chargeV5)

    // 抢占机器人的控制权
    httpServer.post("/api/hik/lock", GwHikHandler::lock)
    // 释放机器人的控制权
    httpServer.post("/api/hik/unlock", GwHikHandler::unlock)
    // 列出机器人控制权情况
    httpServer.post("/api/hik/list-owner", GwHikHandler::listOwner)

    httpServer.exception(BzError::class.java) { e, ctx ->
      ctx.status(400)
      logger.info("BzError {}", e.message, e.cause)
      ctx.json(mapOf("code" to e.code, "message" to e.message, "args" to e.args))
    }

    httpServer.start(config.httpServerConfig.port)
  }

  private fun buildServer(): Javalin = Javalin.create { jc ->
    jc.showJavalinBanner = false
    // jc.compression.gzipOnly()
    // jc.staticFiles.add { staticFiles ->
    //   staticFiles.hostedPath = "/"
    //   staticFiles.directory = "/dist"
    //   staticFiles.location = Location.CLASSPATH
    //   staticFiles.precompress = false
    // }
    jc.plugins.enableCors { cors ->
      cors.add { it.anyHost() }
    }
    jc.jetty.wsFactoryConfig { c ->
      c.maxFrameSize = 1024 * 1024 * 50 // 50M
      c.maxTextMessageSize = 1024 * 1024 * 50 // 50M
      c.idleTimeout = Duration.of(60, ChronoUnit.SECONDS)
    }
  }

  fun dispose() {
    httpServer?.close()
  }

  private fun handleRbkRequest(ctx: Context) {
    if (!checkAuth(ctx)) return

    val req = ctx.bodyAsClass(GwRbkRequest::class.java)
    
    logger.debug("收到 HTTP 信道调用 RBK 请求：$req")
    val res = LocalRobots.requestRbk(req)
    ctx.json(res)
  }

  private fun checkAuth(ctx: Context): Boolean {
    val authId = ctx.queryParam("authId")
    val authSecret = ctx.queryParam("authSecret")
    if (authId.isNullOrBlank() || authSecret.isNullOrBlank()) {
      ctx.status(401)
      return false
    }
    val config = GwCenter.gwConfig.httpServerConfig
    if (!(authId == config.authId && authSecret == config.authSecret)) {
      ctx.status(401)
      return false
    }

    return true
  }

  private fun listRobots(ctx: Context) {
    // 不同厂商的数据结果不必相同
    val reports: List<EntityValue> = GwCenter.gwConfig.localConfigs.map { lc ->
      getRobotReport(lc)
    }

    // 字段分组 config, basic, feature, report, laser
    val fieldStr = ctx.queryParam("fields") ?: "" // all 全部；剩下的先用海康的接口编号（如 0x01）分组吧
    val fields = fieldStr.split(",")
    if (fieldStr.isBlank() || fields.contains("all")) {
      // 全部字段
    } else {
      if (!fields.contains("config")) {
        reports.forEach { it.remove("config") }
      }
      if (!fields.contains("basic")) {
        reports.forEach { it.remove("basic") }
      }
      if (!fields.contains("feature")) {
        reports.forEach { it.remove("feature") }
      }
      if (!fields.contains("report")) {
        reports.forEach {
          it.remove("report")
        }
      }
      if (!fields.contains("laser")) {
        reports.forEach { it.remove("laser") }
      }
    }

    ctx.json(reports)
  }

  private fun fetchRobot(ctx: Context) {
    val id = ctx.pathParam("id")

    // 不同厂商的数据结果不必相同
    val lc = GwCenter.gwConfig.localConfigs.firstOrNull { it.name == id }
      ?: throw BzError("errGwNoRobot", id)

    val report = getRobotReport(lc)

    // 字段分组
    val fieldStr = ctx.queryParam("fields") ?: "" // all 全部；剩下的先用海康的接口编号（如 0x01）分组吧
    val fields = fieldStr.split(",")
    if (fieldStr.isBlank() || fields.contains("all")) {
      // 全部字段
    } else {
      if (!fields.contains("config")) {
        report.remove("config")
      }
      if (!fields.contains("basic")) {
        report.remove("basic")
      }
      if (!fields.contains("feature")) {
        report.remove("feature")
      }
      if (!fields.contains("report")) {
        report.remove("report")
      }
      if (!fields.contains("laser")) {
        report.remove("laser")
      }
    }

    ctx.json(report)
  }

  fun getRobotReport(lc: LocalConfig): EntityValue = when (lc.vendor) {
    RobotVendor.Seer -> HashMap() // TODO
    RobotVendor.Hik -> {
      val robotNo = getRobotNo(lc)
      val robot = HikAdapter.robots.values.firstOrNull { it.robotNo == robotNo } ?: HikRobot(
        robotNo = robotNo,
        status = -1, // 未初始化过
        lastReportOn = DateHelper.anyToDate("2000-01-01 00:00:00")
          ?: DateUtils.parseDate("2000-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss"),
      )
      robot.config.online = false
      robot.config.ip = lc.ip

      // 把 gw 的部分参数同步到 robot
      robot.config.disabled = lc.disabled
      robot.config.robotId = lc.name
      if (robot.status != 0x57) robot.config.online = true

      JsonHelper.mapper.readValue(JsonHelper.writeValueAsString(robot), jacksonTypeRef())
    }

    RobotVendor.Hai -> HashMap() // TODO
  }

  private fun getRobotNo(lc: LocalConfig) = try {
    NumHelper.anyToInt(lc.name)
  } catch (e: Exception) {
    throw BzError("errCodeErr", "robotId ${lc.name} not an int")
  } ?: throw BzError("errCodeErr", "robotId ${lc.name} not an int")

  private fun moveRobot(ctx: Context) {
    val req: MoveRobotReq = ctx.getReqBody()
    HikAdapter.manualMove(req)
  }
}