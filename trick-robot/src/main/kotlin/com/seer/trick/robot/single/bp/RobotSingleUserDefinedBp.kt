package com.seer.trick.robot.single.bp


import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockOutputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper

class RobotSingleUserDefinedBp : AbstractTaskChainBp() {

  override fun process() {
    val userDefinedAction = JsonHelper.mapper.writeValueAsString(mustGetBlockInputParam("userDefinedAction"))
    val apiNo = getBlockInputParam("apiNo") as Long? ?: 3051
    val reqId = getBlockInputParam("reqId") as String? ?: IdHelper.uuidStr()

    val gwRbkResult = executeTask(RbkRequest(reqId, apiNo.toInt(), userDefinedAction, "自定义动作"))
    setBlockOutputParams(mapOf("rbkResult" to gwRbkResult))
  }

  companion object {

    val def = BlockDef(
      RobotSingleUserDefinedBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(
        BlockInputParamDef("userDefinedAction", BlockParamType.JSONObject, true),
//        BlockInputParamDef("apiNo", BlockParamType.Long),
//        BlockInputParamDef("reqId", BlockParamType.String),
      ),
      outputParams = listOf(
        BlockOutputParamDef("rbkResult", BlockParamType.String)
      )
    )

  }

}