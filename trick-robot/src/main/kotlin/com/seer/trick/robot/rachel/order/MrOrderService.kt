package com.seer.trick.robot.rachel.order

import com.seer.trick.BzError

import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.id.IdGenManager
import com.seer.trick.robot.rachel.*
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.ConcurrentHashMap

/**
 * 管理运单。管理运单的创建、更新。
 */
class MrOrderService(private val rachel: RaRachelManager) {

  private val logger = LoggerFactory.getLogger(this::class.java)

  // 内存里的运单池
  val orders: MutableMap<String, MrOrderRuntime> = ConcurrentHashMap()

  fun mustGetOrderById(orderId: String): MrOrderRuntime {
    return orders[orderId] ?: throw BzError("errMrNoOrder", orderId)
  }

  fun generateOrderId(): String {
    val em = BaseCenter.mustGetEntityMeta("WcsMrOrder")
    return IdGenManager.generateId(em.idGen!!)
  }

  fun createOrders(req: List<MrOrderWithSteps>) {
    logger.info("创建通用运单：$req")
    for (os in req) {
      orders[os.order.id] = MrOrderRuntime(os.order, os.steps)
    }

    // 持久化新单
    MrRepo.createOrders(req)

    // 请求一次分派
    rachel.dispatchOrderService.dispatchOrders()
  }


  fun createStep(step: MrStep) = rachel.withKeyLock {
    val or = orders[step.orderId] ?: throw BzError("errMrNoOrder", step.orderId)
    or.addStep(step)
    MrRepo.createStep(step)
  }

  fun updateOrderAndAddSteps(order: MrOrderRuntime, steps: List<MrStep>, stepFixed: Boolean) =
    rachel.withKeyLock {
      steps.forEach { e ->
        createStep(e)
      }
      updateOrder(order.order.copy(stepNum = order.order.stepNum + steps.size, stepFixed = stepFixed))
    }


    fun updateOrder(order: MrOrder) = rachel.withKeyLock {
    val or = orders[order.id] ?: throw BzError("errMrNoOrder", order.id)
    val oldOrder = or.order
    if (!(oldOrder.status == MrOrderStatus.Building || oldOrder.status == MrOrderStatus.ToBeAllocated || oldOrder.status == MrOrderStatus.Pending))
      throw BzError("errMrUpdateOrderBadStatus", order.id, oldOrder.status)
    or.order = order

    MrRepo.updateOrderAsync(order)

    rachel.dispatchOrderService.dispatchOrders()
  }

  fun restoreOrders() = rachel.withKeyLock {
    val owsList = MrRepo.listOrderWithSteps()
    logger.info("恢复运单数量 ${owsList.size}")

    for (tws in owsList) {
      orders[tws.order.id] = MrOrderRuntime(tws.order, tws.steps)
    }
  }

  /**
   * 外部不要直接调用。
   */
  fun markOrderDone(rr: MrRobotRuntime, or: MrOrderRuntime) = rachel.withKeyLock {
    logger.info("通用运单完成（成功） ${or.orderId}，机器人 ${rr.id}")
    // 先移除机器人自己的任务列表
    rr.orders.remove(or.orderId)

    // 如果到了最后了，库位没用，取消占用
    val binIndex = rr.bins.indexOfFirst { it.orderId == or.orderId }
    if (binIndex >= 0) {
      logger.info("通用运单完成，取消占用库位 $binIndex，通用运单 ${or.orderId}，机器人 ${rr.id}")
      val oldBin = rr.bins[binIndex]
      if (oldBin.status == MrRobotBinStatus.Reserved) {
        rr.bins[binIndex] = MrRobotBin(binIndex, status = MrRobotBinStatus.Empty, orderId = null)
      }
    }

    val newOrder = or.order.copy(status = MrOrderStatus.Done, doneOn = Date())
    or.order = newOrder
    orders.remove(or.orderId)

    MrRepo.updateOrderAsync(newOrder)
  }

  /**
   * 外部不要直接调用。经过取消过程，最后标记取消
   */
  fun markOrderCancel(or: MrOrderRuntime) = rachel.withKeyLock {
    // 完成取消
    or.order = or.order.copy(
      fault = false,
      status = MrOrderStatus.Cancelled,
      doneOn = Date()
    )
    MrRepo.updateOrderAsync(or.order)

    orders.remove(or.orderId)
  }

  fun resetRobot(rr: MrRobotRuntime) = rachel.withKeyLock {
    val orderIds = rr.orders.keys
    val resources = rachel.scheduleService.listMySiteIds(rr.id)
    logger.info( "重置机器人 ${rr.id}，命令状态=${rr.cmdStatus}，" +
          "当前运单=${rr.currentOrder?.orderId}:${rr.currentStepIndex}，运单列表=${orderIds}，" +
          "库位状态=${rr.bins}，占用资源=$resources"
    )

    rr.adapter.cancelCmd(rr)
    rr.cmdFuture?.cancel(true)
    rr.cmdFuture = null

    rr.setIdle(logger, "要求重置机器人")
    rr.orders.clear()

    rr.bins.clear()
    for (i in 1..rr.systemConfig.selfBinNum) {
      rr.bins.add(MrRobotBin(i - 1))
    }

    MrRepo.saveRobotAsync(rr)

    rachel.scheduleService.unlockByRobot(rr.id)
  }

}