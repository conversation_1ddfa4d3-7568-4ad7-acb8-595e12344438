package com.seer.trick.robot.gw

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.JsonNode
import com.seer.trick.BzError

import com.seer.trick.base.concurrent.BaseConcurrentCenter.highTimeSensitiveExecutor
import com.seer.trick.helper.JsonHelper
import com.seer.trick.robot.rachel.RobotVendor
import com.seer.trick.robot.vendor.seer.rbk.RbkClient
import org.slf4j.LoggerFactory

class LocalRobot(val robotName: String, val config: LocalConfig) {

  private val logger = LoggerFactory.getLogger(javaClass)

  @Volatile
  var rbkClient: RbkClient? = null

  /**
   * 不耗时
   */
  fun init() {
    if (config.vendor == RobotVendor.Seer && !config.mock) initRbkClient()
  }

  private fun initRbkClient() {
    logger.info("网关，初始化仙工机器人 [$robotName] ${config.ip}")
    val client = RbkClient(config.name, config.ip)
    rbkClient = client

    // 异步测试
    highTimeSensitiveExecutor.submit {
      logger.debug("网关，发送测试请求到仙工机器人 $robotName")
      try {
        val res = client.request(1000, "{}")
        logger.debug("测试请求仙工机器人成功 [$robotName] $res")
      } catch (e: Exception) {
        logger.error("测试请求仙工机器人报错", e)
      }
    }
  }

  fun dispose() {
    rbkClient?.dispose()
    rbkClient = null
  }

  fun requestRbk(req: GwRbkRequest): String {
    // logger.debug("Got request $req")
    val client = rbkClient ?: throw BzError("errNoRbkClient")

    var reqStr = req.reqStr
    if (reqStr.isNullOrBlank() && req.reqObj != null) {
      reqStr = JsonHelper.writeValueAsString(req.reqObj)
    }
    if (reqStr.isNullOrBlank()) reqStr = ""
    return client.request(req.rbkApiNo, reqStr)
  }
}

@JsonIgnoreProperties
data class GwRbkRequest(
  val reqId: String,
  val robotId: String,
  // API 编号
  val rbkApiNo: Int = 0,
  // 请求字符串
  val reqStr: String? = null,
  // 请求 JSON 对象
  val reqObj: JsonNode? = null,
) {
  override fun toString(): String = "$robotId|$rbkApiNo < $reqStr"
}