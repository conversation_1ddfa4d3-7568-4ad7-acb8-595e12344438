package com.seer.trick.robot.stats.custom

import com.seer.trick.ComplexQuery
import com.seer.trick.Cq

import com.seer.trick.base.concurrent.BaseConcurrentCenter
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.AdvancedField
import com.seer.trick.base.entity.service.AggFun
import com.seer.trick.base.entity.service.AggregationOptions
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.EntityStatsService
import com.seer.trick.base.entity.service.GroupByField
import com.seer.trick.base.entity.service.StatisticDateType
import com.seer.trick.base.soc.SysMonitorService
import com.seer.trick.base.stats.manila.ManilaReportService
import com.seer.trick.helper.NumHelper
import com.seer.trick.helper.NumHelper.roundToDecimal
import org.slf4j.LoggerFactory
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

/**
 * 基于机器人状态变化表的比率类的统计
 */
object RobotRateStats {
  private val logger = LoggerFactory.getLogger(javaClass)
  private val scheduler = Executors.newSingleThreadScheduledExecutor()

  fun init() {
    scheduler.scheduleAtFixedRate(RobotRateStats::rateStats, 30 * 60 * 1000, 60 * 60 * 1000, TimeUnit.MILLISECONDS)
  }

  fun dispose() {
    scheduler.shutdownNow()
  }

  /**
   * 统计比率类型，包括空闲率、工作率、故障率、空载率 TODO 满载率
   */
  fun rateStats(additionalCq: ComplexQuery? = null) {
    BaseConcurrentCenter.statsExecutor.submit {
      
      try {
        SysMonitorService.log(
          
          "Stat",
          "rate",
          "robot",
          "Start::stat::rate",
          instant = ManilaReportService.disableLogging(),
        )
        // 按小时、机器人名称统计在线时长
        val robotsOnlineStats = onlineStatsGroupByHour("robotName")
        // 按小时统计所有机器人的在线时长
        val allOnlineStats = onlineStatsGroupByHour(null)
        // 空载率 = 空载时长 / 在线时长
        rateCalculationByType("Empty", "RobotNoLoadRate", allOnlineStats, robotsOnlineStats)
        // 故障率 = 故障时长 / 在线时长
        rateCalculationByType("InError", "RobotFailureRate", allOnlineStats, robotsOnlineStats)
        // 空闲率 = 空闲时长 / 在线时长
        rateCalculationByType("Idle", "RobotIdleRate", allOnlineStats, robotsOnlineStats)
      } catch (e: Throwable) {
        SysMonitorService.log(
          
          "Stat",
          "rate",
          "robot",
          "Stat rate fail: ${e.message}",
          instant = ManilaReportService.disableLogging(),
        )
        logger.error(e.message ?: "Unknown Exception", e)
      } finally {
        SysMonitorService.log(
          
          "Stat",
          "rate",
          "robot",
          "Finish::stat::rate",
          instant = ManilaReportService.disableLogging(),
        )
      }
    }
  }

  private fun rateCalculationByType(
    
    type: String,
    reportSubject: String,
    allOnlineStats: List<EntityValue>,
    robotsOnlineStats: List<EntityValue>,
  ) {
    val allStatsByHour = EntityStatsService.aggregateQuery(
      
      "RobotPropChangeTimeline",
      Cq.and(Cq.eq("type", type), Cq.isNotNull("finishedOn")),
      AggregationOptions(
        fields = listOf(
          AdvancedField(AggFun.SUM, "duration", "duration"),
          AdvancedField(null, "finishedOn", "finishedOn"),
        ),
        groupBy = listOf(
          GroupByField("finishedOn", "finishedOn", StatisticDateType.Hour),
        ),
      ),
    )

    // robotName 类型的统计
    val robotsStatsByHour = EntityStatsService.aggregateQuery(
      
      "RobotPropChangeTimeline",
      Cq.and(Cq.eq("type", type), Cq.isNotNull("finishedOn")),
      AggregationOptions(
        fields = listOf(AdvancedField(AggFun.SUM, "duration", "duration")),
        groupBy = listOf(
          GroupByField("robotName", "robotName", null),
          GroupByField("finishedOn", "finishedOn", StatisticDateType.Hour),
        ),
      ),
    )

    rateCalculation(reportSubject, allStatsByHour, allOnlineStats)
    rateCalculation(reportSubject, robotsStatsByHour, robotsOnlineStats)
  }

  private fun rateCalculation(
    
    reportSubject: String,
    allStatsByHour: List<EntityValue>,
    allOnlineStats: List<EntityValue>,
  ) {
    val dateFormat = SimpleDateFormat("yyyy-MM-dd HH")
    val result: MutableMap<StatisticDateType, MutableMap<String, Pair<Double, Double>>> = mutableMapOf()
    /*
     * 必须基于在线时间统计，否则基于存在主题统计的话，若某段时间内没有这个主题，应该默认是这个主题数据为 0，否则就会缺失这时间段的在线时长统计。
     * 比如：一天都在线但只有两条错误时长记录，那么基于这两条进行统计，则一天内就会缺失很多在线时长的统计
     */
    for (item in allOnlineStats) {
      val finishedOn = item["finishedOn"].toString()
      val target = item["robotName"]?.toString() ?: "all"
      val denominator = NumHelper.anyToDouble(item["duration"]) ?: 0.0
      val molecule = if (denominator == 0.0) {
        0.0
      } else {
        val filter: MutableList<(item: EntityValue) -> Boolean> = ArrayList()
        if (target != "all") {
          filter.add { it["robotName"].toString() == target }
        }
        filter.add { it["finishedOn"].toString() == finishedOn }
        val duration = allStatsByHour.find { entity ->
          var isTrue = true
          filter.forEach { isTrue = isTrue && it(entity) }
          isTrue
        }?.get("duration")
        NumHelper.anyToDouble(duration) ?: 0.0
      }
      val date = dateFormat.parse(finishedOn)
      // 按照小时、日、周、月、季度、年的顺序计算
      accumulateRatioData(result, target, date, molecule, denominator, StatisticDateType.Hour)
      accumulateRatioData(result, target, date, molecule, denominator, StatisticDateType.Day)
      accumulateRatioData(result, target, date, molecule, denominator, StatisticDateType.Week)
      accumulateRatioData(result, target, date, molecule, denominator, StatisticDateType.Month)
      accumulateRatioData(result, target, date, molecule, denominator, StatisticDateType.Quarter)
      accumulateRatioData(result, target, date, molecule, denominator, StatisticDateType.Year)
    }

    for ((periodType, data) in result) {
      for ((targetAndPeriod, pair) in data) {
        val totalMolecular = pair.first
        val totalDenominator = pair.second
        /*
         * 如果分子比分母大则跳过，原因是一个周期内的分母还没计算结束，分子已经先计算了多次
         * 比如空闲时间，当机器人发生改变的时候就会计算一次，而在线时间是在满一个周期后才计算一次
         */
        if (totalDenominator < totalMolecular) continue
        val ratio = if (totalDenominator != 0.0) (totalMolecular / totalDenominator).roundToDecimal(5) else 0.0
        saveStatResult(
          
          reportSubject,
          periodType,
          targetAndPeriod,
          ratio,
          totalMolecular,
          totalDenominator,
        )
      }
    }
  }

  /**
   * 统计在线时长
   */
  private fun onlineStatsGroupByHour(groupByField: String?): List<EntityValue> {
    val groupBy = if (groupByField == null) {
      mutableListOf()
    } else {
      mutableListOf(GroupByField(groupByField, groupByField, null))
    }
    groupBy.add(GroupByField("finishedOn", "finishedOn", StatisticDateType.Hour))
    return EntityStatsService.aggregateQuery(
      
      "RobotPropChangeTimeline",
      Cq.and(Cq.eq("type", "Online"), Cq.isNotNull("finishedOn")),
      AggregationOptions(
        fields = listOf(
          AdvancedField(null, "type", "type"),
          AdvancedField(AggFun.SUM, "duration", "duration"),
          AdvancedField(null, "finishedOn", "finishedOn"),
        ),
        groupBy = groupBy,
      ),
    )
  }

  private fun accumulateRatioData(
    ratioResult: MutableMap<StatisticDateType, MutableMap<String, Pair<Double, Double>>>,
    target: String,
    date: Date,
    molecular: Double,
    denominator: Double,
    periodType: StatisticDateType,
  ) {
    val periodKey = "$target#${ManilaReportService.formatPeriod(date, periodType)}"
    val pair = ratioResult.getOrPut(periodType) { mutableMapOf() }
      .getOrPut(periodKey) { Pair(0.0, 0.0) }

    ratioResult[periodType]!![periodKey] = Pair(pair.first + molecular, pair.second + denominator)
  }

  private fun saveStatResult(
    
    reportSubject: String,
    periodType: StatisticDateType,
    targetAndPeriod: String,
    totalValue: Double,
    molecular: Double? = null,
    denominator: Double? = null,
  ) {
    val split = targetAndPeriod.split("#")
    val target = split[0]
    val period = split[1]
    val periodTrim = period.replace("H", "")

    // 删除旧记录
    EntityRwService.removeMany(
      
      "StatsTimelineValueReport",
      Cq.and(
        Cq.eq("subject", reportSubject),
        Cq.eq("target", target),
        Cq.eq("periodType", periodType),
        Cq.eq("period", period),
      ),
    )

    // 准备要插入的数据
    val data: EntityValue = mutableMapOf(
      "subject" to reportSubject,
      "target" to target,
      "periodType" to periodType,
      "period" to period,
      "value" to totalValue,
      "startedOn" to ManilaReportService.period2StartedOn(periodTrim, periodType),
      "finishedOn" to ManilaReportService.period2FinishedOn(periodTrim, periodType),
      "molecular" to molecular,
      "denominator" to denominator,
    )

    // 插入新的聚合结果
    EntityRwService.createOne("StatsTimelineValueReport", data)
  }
}