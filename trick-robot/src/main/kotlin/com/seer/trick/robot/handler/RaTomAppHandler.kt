package com.seer.trick.robot.handler

import com.seer.trick.BzError
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.HttpServerManager.noAuth
import com.seer.trick.base.http.getReqBody

import com.seer.trick.robot.RobotAppManager
import com.seer.trick.robot.tom.MarkCompleteReq
import com.seer.trick.robot.tom.TerminateReq
import com.seer.trick.robot.tom.TomAgent
import com.seer.trick.robot.tom.TomRobotIO
import io.javalin.http.Context

object RaTomAppHandler {

  fun registerHandlers() {
    val c = Handlers("api/robot/tom")

    c.post("dispatch-mode", ::changeDispatchMode, auth()) // 设置机器人接单状态
    c.post("control", ::control, auth()) // 获取、释放机器人控制权
    c.post("pause-navigation", ::pauseNavigation, auth()) // 暂停、继续导航
    c.post("re-loc", ::reLoc, auth()) //  重定位
    c.post("re-loc-confirm", ::reLocConfirm, auth())
    c.post("clear-robot-all-error", ::clearRobotAllError, auth()) // 清除机器人报错
    c.post("clear-third-error", ::clearThirdError, auth()) // 清除机器人第三方错误
    c.post("clear-third-warning", ::clearThirdWarning, auth()) // 清除机器人第三方Warning
    c.post("set-soft-emc", ::setSoftIOEMC, auth()) // 设置软急停
    c.post("set-robot-io", ::setRobotIO, auth()) // 设置IO

    c.get("order", ::getOrder, noAuth())
    c.post("list-orders", ::queryOrderPage, noAuth())
    c.post("terminate-orders", ::terminateOrders, auth())
    c.post("complete-orders", ::completeOrders, auth())
    c.post("set-orders-priority", ::raisePriority, auth())
  }

  private fun reLocConfirm(ctx: Context) {
    
    val req: ReLocConfirmReq = ctx.getReqBody()

    val url = RobotAppManager.mustGetScene(req.tomId).config.tom.coreUrl

    TomAgent.reLocConfirm(url, req.vehicles)

    ctx.status(200)
  }

  private fun changeDispatchMode(ctx: Context) {
    
    val req: ChangeDispatchModeReq = ctx.getReqBody()
    val url = RobotAppManager.mustGetScene(req.tomId).config.tom.coreUrl
    TomAgent.changeDispatchMode(url, req.vehicles, req.type)
    ctx.status(200)
  }

  private fun control(ctx: Context) {
    
    val req: ControlReq = ctx.getReqBody()
    val url = RobotAppManager.mustGetScene(req.tomId).config.tom.coreUrl
    if (req.control) {
      TomAgent.dominateControl(url, req.vehicles)
    } else {
      TomAgent.releaseControl(url, req.vehicles)
    }
    ctx.status(200)
  }

  private fun pauseNavigation(ctx: Context) {
    
    val req: PauseNavigationReq = ctx.getReqBody()
    val url = RobotAppManager.mustGetScene(req.tomId).config.tom.coreUrl
    if (req.pause) {
      TomAgent.gotoSitePause(url, req.vehicles)
    } else {
      TomAgent.gotoSiteResume(url, req.vehicles)
    }
    ctx.status(200)
  }

  private fun reLoc(ctx: Context) {
    
    val req: ReLocReq = ctx.getReqBody()

    val url = RobotAppManager.mustGetScene(req.tomId).config.tom.coreUrl

    TomAgent.reLoc(url, req.vehicle, req.xAxis, req.yAxis, req.angle, req.length)

    ctx.status(200)
  }

  private fun clearRobotAllError(ctx: Context) {
    
    val req: CommonReq = ctx.getReqBody()
    val url = RobotAppManager.mustGetScene(req.tomId).config.tom.coreUrl
    TomAgent.clearRobotAllError(url, req.vehicles)
    ctx.status(200)
  }

  private fun clearThirdError(ctx: Context) {
    
    val req: ClearThirdWarningReq = ctx.getReqBody()
    val url = RobotAppManager.mustGetScene(req.tomId).config.tom.coreUrl
    TomAgent.clearThirdError(url, req.vehicle)
    ctx.status(200)
  }

  private fun clearThirdWarning(ctx: Context) {
    
    val req: ClearThirdWarningReq = ctx.getReqBody()
    val url = RobotAppManager.mustGetScene(req.tomId).config.tom.coreUrl
    TomAgent.clearThirdWarning(url, req.vehicle)
    ctx.status(200)
  }

  private fun setSoftIOEMC(ctx: Context) {
    
    val req: SoftIOEMCReq = ctx.getReqBody()
    val url = RobotAppManager.mustGetScene(req.tomId).config.tom.coreUrl
    req.vehicles.stream().forEach { TomAgent.setSoftIOEMC(url, it, req.status) }
    ctx.status(200)
  }

  private fun setRobotIO(ctx: Context) {
    
    val req: RobotIOReq = ctx.getReqBody()
    val url = RobotAppManager.mustGetScene(req.tomId).config.tom.coreUrl
    TomAgent.setRobotIO(url, req.vehicle, req.type, req.id, req.status)
    ctx.status(200)
  }

  private fun getOrder(ctx: Context) {
    

    val tomId = ctx.queryParam("tomId")
    if (tomId.isNullOrBlank()) throw BzError("errMissingHttpQueryParam", "tomId")

    val orderId = ctx.queryParam("orderId")
    if (orderId.isNullOrBlank()) throw BzError("errMissingHttpQueryParam", "orderId")

    val url = TomAgent.getTomUrlRoot(tomId)
    val order = TomAgent.queryOrder(url, orderId)

    ctx.json(mapOf("order" to order))
  }

  private fun queryOrderPage(ctx: Context) {
    
    val req: TomQueryOrderPageReq = ctx.getReqBody()
    val url = TomAgent.getTomUrlRoot(req.tomId)
    val o = TomAgent.QueryOrderPageOptions(
      vehicle = req.vehicle,
      state = req.state,
      notCompleteOnly = req.notCompleteOnly,
    )
    val r = TomAgent.queryOrderPage(url, req.pageNo, req.pageSize, o)
    ctx.json(r)
  }

  private fun terminateOrders(ctx: Context) {
    
    val req: TomTerminateReq = ctx.getReqBody()
    val url = TomAgent.getTomUrlRoot(req.tomId)
    TomAgent.terminate(url, TerminateReq(req.idList, req.disableVehicle))
    ctx.status(200)
  }

  private fun completeOrders(ctx: Context) {
    
    val req: TomCompleteReq = ctx.getReqBody()
    val url = TomAgent.getTomUrlRoot(req.tomId)
    TomAgent.markComplete(url, MarkCompleteReq(idList = req.idList))
    ctx.status(200)
  }

  private fun raisePriority(ctx: Context) {
    val req: TomOrdersPriorityReq = ctx.getReqBody()
    val url = RobotAppManager.mustGetScene(req.tomId).config.tom.coreUrl
    req.idList.stream().forEach { TomAgent.setOrderPriority(url, it, req.priority) }
    ctx.status(200)
  }

  data class ReLocConfirmReq(val tomId: String, val vehicles: List<String>)

  data class ChangeDispatchModeReq(val tomId: String, val vehicles: List<String>, val type: String)

  data class ControlReq(
    val tomId: String,
    val vehicles: List<String>,
    val control: Boolean, // true: 抢占 false: 释放
  )

  data class PauseNavigationReq(
    val tomId: String,
    val vehicles: List<String>,
    val pause: Boolean, // true: 继续导航 false: 暂停导航
  )

  data class ClearThirdWarningReq(val tomId: String, val vehicle: String)

  data class SoftIOEMCReq(
    val tomId: String,
    val vehicles: List<String>, // TODO 改为批量
    val status: Boolean, // true: 设置为急停, false：为取消软急停
  )

  data class RobotIOReq(val tomId: String, val vehicle: String, val id: Int, val type: TomRobotIO, val status: Boolean)

  data class ReLocReq(
    val tomId: String,
    val vehicle: String,
    val xAxis: Double,
    val yAxis: Double,
    val length: Double,
    val angle: Double,
  )

  data class CommonReq(val tomId: String, val vehicles: List<String>)

  data class TomQueryOrderPageReq(
    val tomId: String,
    val pageNo: Int = -1,
    val pageSize: Int = 20,
    val vehicle: String? = null,
    val state: List<String>? = null,
    val notCompleteOnly: Boolean = false,
    val externalId: String? = null,
  )

  data class TomTerminateReq(val tomId: String, val idList: List<String>, val disableVehicle: Boolean = false)

  data class TomCompleteReq(val tomId: String, val idList: List<String>)

  data class TomOrdersPriorityReq(val tomId: String, val idList: List<String>, val priority: Int)
}