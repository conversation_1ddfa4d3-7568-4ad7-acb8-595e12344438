package com.seer.trick.robot.map

import com.seer.wcs.GeoHelper

/**
 * 对场景地图进行封装，预计算、索引，方便快捷操作。
 */
class SceneMapRuntime(val sceneMap: MrSceneMap) {

  val areaIdToIndexMap: MutableMap<String, AreaIndex> = HashMap()

  val siteIdToIndexMap: MutableMap<String, SiteIndex> = HashMap()

  val sites: MutableList<MrSite> = ArrayList() // 所有站点

  val binIdToIndexMap: MutableMap<String, BinIndex> = HashMap()

  // 特区 id -> 在此特区内的所有站点
  val zoneIdToIndexMap: MutableMap<String, ZoneIndex> = HashMap()

  val defaultAlg = SceneMapAlg(sceneMap)

  init {
    for ((ai, area) in sceneMap.areas.withIndex()) {
      areaIdToIndexMap[area.id] = AreaIndex(ai, area)

      for ((si, site) in area.sites.withIndex()) {
        siteIdToIndexMap[site.id] = SiteIndex(area.id, ai, si, site)
        sites += site
      }
      for ((bi, bin) in area.bins.withIndex()) {
        binIdToIndexMap[bin.binId] = BinIndex(area.id, ai, bi, bin.siteId, bin)
      }
      for (zone in area.zones) {
        val sets = mutableSetOf<String>()
        for (site in area.sites) {
          // 暂不考虑旋转
          if (GeoHelper.isPointInRect(site.x, site.y, zone.x, zone.y, zone.width, zone.height))
            sets += site.id
        }
        zoneIdToIndexMap[zone.id] = ZoneIndex(zone.id, zone, sets)
      }
    }
  }

  fun getIndexBySiteIdOrBinId(siteOrBinId: String): SiteIndex? {
    val siteIndex = siteIdToIndexMap[siteOrBinId]
    if (siteIndex != null) return siteIndex

    val binIndex = binIdToIndexMap[siteOrBinId]
    if (binIndex != null) return siteIdToIndexMap[binIndex.siteId]

    return null
  }

}