package com.seer.trick.robot.vendor.seer

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.http.WsMsg
import com.seer.trick.base.soc.SocAttention
import com.seer.trick.base.soc.SocService
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.robot.gw.GwRbkRequest
import com.seer.trick.robot.handler.GwWsServer
import com.seer.trick.robot.map.SceneMapManager
import com.seer.trick.robot.rachel.ConnectionType
import com.seer.trick.robot.rachel.MrRobotSelfReport
import com.seer.trick.robot.rachel.RobotSelfReportMainHelper.rawToMain
import com.seer.trick.robot.rachel.RobotVendor
import com.seer.trick.robot.vendor.seer.rbk.RbkClient
import org.slf4j.LoggerFactory
import java.util.concurrent.Executors
import java.util.concurrent.Future

class SeerConnector(
  val robotName: String,
  val mm: SceneMapManager,
  private val authId: String, // 用于 WS 通讯
  private val authSecret: String, // 用于 WS 通讯
  private val connectType: ConnectionType,
  private val rbkIp: String? = null,
  private val onReport: ((report: MrRobotSelfReport) -> Unit)? = null,
) {

  private val logger = LoggerFactory.getLogger(this::class.java)

  @Volatile
  private var disposed = false

  @Volatile
  private var rbkClient: RbkClient? = null

  private val stateExecutor = Executors.newSingleThreadExecutor()

  @Volatile
  private var stateWorker: Future<*>? = null

  @Volatile
  private var fetching = false

  @Volatile
  private var stateReport: MrRobotSelfReport? = null

  /**
   * 不耗时
   */
  @Synchronized
  fun init() {
    if (disposed) throw IllegalStateException("Connector has been disposed")

    init2()

    if (connectType.realtime) {
      stateWorker = stateExecutor.submit { fetchStateLoop() }
    }

    updateSocState("初始化完成")
  }

  /**
   * dispose 后不会再被使用
   */
  @Synchronized
  fun dispose() {
    disposed = true

    stateWorker?.cancel(true)
    stateWorker = null

    this.rbkClient?.dispose()
    this.rbkClient = null

    SocService.removeNode("SeerConnector:State:$robotName")
  }

  @Synchronized
  fun reset() {
    this.rbkClient?.dispose()
    this.rbkClient = null

    init2()
  }

  private fun init2() {
    if (connectType == ConnectionType.Rbk) {
      // 此步不耗时
      rbkClient = RbkClient(robotName, rbkIp ?: throw BzError("errCodeErr", "No RBK IP"))
    }
  }

  fun requestRbk(apiNo: Int, requestStr: String, timeout: Long = 10 * 1000): String =
    when (connectType) {
      ConnectionType.Rbk -> {
        val client = rbkClient ?: throw BzError("errNoRbkClient")
        client.request(apiNo, requestStr)
      }

      ConnectionType.GwWs, ConnectionType.GwWsLight -> {
        val req = WsRequest(robotName, apiNo, requestStr)
        val msg = WsMsg.json("GwSeerRbkRequest", req, id = IdHelper.oidStr())
        GwWsServer.request(robotName, authId, authSecret, msg, timeout)
      }

      ConnectionType.Mock -> {
        MockSeerRobotService.requestRbk(GwRbkRequest("", robotName, apiNo, requestStr))
      }
    }

  fun requestWithCodeCheck(apiNo: Int, reqStr: String): JsonNode? {
    val r = requestRbk(apiNo, reqStr) // TODO
    val n = JsonHelper.mapper.readTree(r)
    val resCode = n["ret_code"]?.asInt()
    val errMsg = n["err_msg"]?.asText()
    if (resCode != 0) {
      logger.error("RequestFail, apiNo=$apiNo, reqStr=$reqStr, resCode=$resCode, errMsg=$errMsg")
      throw BzError("errRbkNotOk", resCode, errMsg)
    }
    return n
  }

  /**
   * 请求控制权
   */
  fun requestControl(): JsonNode? {
    val reqStr = JsonHelper.mapper.writeValueAsString(mapOf("nick_name" to "M4"))
    return requestWithCodeCheck(4005, reqStr)
  }

  /**
   * 路径导航，由机器人自己规划路径
   * https://support.seer-group.com/d/1674676713470087169.html
   */
  fun go3051(req: Map<String, Any?>): JsonNode? {
    val reqStr = JsonHelper.mapper.writeValueAsString(req)
    return requestWithCodeCheck(3051, reqStr)
  }

  fun go3066(moveList: List<EntityValue>): JsonNode? {
    val reqStr = JsonHelper.mapper.writeValueAsString(mapOf("move_task_list" to moveList))
    return requestWithCodeCheck(3066, reqStr)
  }

  /**
   * https://support.seer-group.com/d/1674676595492704257.html
   */
  fun queryGo(taskId: String): QueryGoResult? {
    val reqStr = JsonHelper.mapper.writeValueAsString(mapOf("task_ids" to listOf(taskId)))
    val r = requestWithCodeCheck(1110, reqStr)
    val taskStatusNode = r?.get("task_status_package")?.get("task_status_list")?.asIterable()?.first()
      ?: return null
    // data class QueryGoResult(
    //  // task_status	number	0 = NONE, 1 = WAITING(目前不可能出现该状态), 2 = RUNNING, 3 = SUSPENDED, 4 = COMPLETED, 5 = FAILED, 6 = CANCELED
    //  val status: Int = 0,
    //  @JsonProperty("task_id")
    //  val taskId: String = "",
    // )
    val status = taskStatusNode["status"]?.asInt() ?: 0
    val rTaskId = taskStatusNode["task_id"]?.asText() ?: ""
    return QueryGoResult(status, rTaskId)
  }

  fun cancelGo() = requestWithCodeCheck(3003, "")

  private fun fetchStateLoop() {
    
    while (!Thread.interrupted()) {
      fetchState()
      Thread.sleep(500) // TODO
    }
  }

  private val fetchReqBody = JsonHelper.mapper.writeValueAsString(mapOf("return_laser" to false))

  private fun fetchState() {
    if (disposed || fetching) return
    fetching = true

    updateSocState("获取状态")

    val selfReport = try {
      // request 不会被卡住，自己有超时机制
      val resStr = requestRbk(1100, fetchReqBody)
      val ev: EntityValue = if (resStr.isBlank()) {
        HashMap(0)
      } else {
        JsonHelper.mapper.readValue(resStr, jacksonTypeRef())
      }
      updateSocState("获取状态成功")
      MrRobotSelfReport(
        false,
        null,
        main = rawToMain(RobotVendor.Seer, ev, mm),
        rawReport = ev,
      )
    } catch (e: Throwable) {
      if (e is InterruptedException) throw e
      updateSocState("获取状态失败：" + e.message, SocAttention.Red)
      MrRobotSelfReport(true, e.message)
    } finally {
      fetching = false
    }

    stateReport = selfReport
    onReport?.invoke(selfReport)

    // 如果获取失败，重连！！
    if (selfReport.error) {
      reset()
    }
  }

  private fun updateSocState(state: String, attention: SocAttention = SocAttention.None) {
    SocService.updateNode(
      "机器人",
      "SeerConnector:State:$robotName",
      "机器人实时连接器:状态:$robotName",
      state,
      attention,
    )
  }
}

data class WsRequest(val robotName: String, val apiNo: Int, val requestStr: String)

data class QueryGoResult(
  // task_status	number	0 = NONE, 1 = WAITING(目前不可能出现该状态), 2 = RUNNING, 3 = SUSPENDED, 4 = COMPLETED, 5 = FAILED, 6 = CANCELED
  val status: Int = 0,
  val taskId: String = "",
)