package com.seer.trick.robot.falcon.tom


import com.seer.trick.falcon.FalconLogLevel
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.falcon.domain.ParamObjectType
import com.seer.trick.robot.tom.MarkCompleteReq
import com.seer.trick.robot.tom.TomAgent

class CompleteTomOrderBp : AbstractBp() {
  
  override fun process() {
    val tomId = mustGetBlockInputParam("tomId") as String
    val orderId = mustGetBlockInputParam("orderId") as String
    
    val tomUrl = TomAgent.getTomUrlRoot(tomId)
    
    log(FalconLogLevel.Info, "结束调度运单：$orderId")
    TomAgent.markComplete(tomUrl, MarkCompleteReq(orderId))
    
    removeResource("TomOrder", orderId)
  }
  
  companion object {
    
    val def = BlockDef(
      CompleteTomOrderBp::class.simpleName!!,
      color = "#C7DCA7",
      inputParams = listOf(
        BlockInputParamDef("tomId", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.RobotScene)),
        BlockInputParamDef("orderId", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.TomOrderId)),
      ),
    )
  }
  
}