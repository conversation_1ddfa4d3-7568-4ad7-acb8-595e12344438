package com.seer.trick.robot.single.bp

import com.seer.trick.BzError

import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockOutputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.robot.RobotAppManager

class RobotSingleGetAllStatusBp : AbstractBp() {

  override fun process() {
    val scene = RobotAppManager.getEnabledSingleSceneOrNull() ?: throw BzError("errRobotAppNoScene")
    val rmSingle = scene.single!!
    val rawReport = rmSingle.stateAgent.report?.rawReport

    setBlockOutputParams(mapOf("status" to rawReport))
  }

  companion object {

    val def = BlockDef(
      RobotSingleGetAllStatusBp::class.simpleName!!,
      color = "#FFA6FF",
      outputParams = listOf(BlockOutputParamDef("status", BlockParamType.JSONObject)),
    )
  }
}