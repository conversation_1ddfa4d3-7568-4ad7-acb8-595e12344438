package com.seer.trick.robot.falcon.tom



import com.seer.trick.falcon.FalconLogLevel
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*
import com.seer.trick.robot.tom.TomAgent
import com.seer.trick.robot.tom.TomRobotIO

class RobotSetDOBp : AbstractBp() {

  override fun process() {
    val tomId = mustGetBlockInputParam("tomId") as String
    val vehicle = mustGetBlockInputParam("vehicle") as String
    val id = mustGetBlockInputParamAsLong("id").toInt()
    val status = getBlockInputParam("status") as Boolean? ?: false
    val tomUrl = TomAgent.getTomUrlRoot(tomId)
    val resultStr = TomAgent.setRobotIO(tomUrl, vehicle, TomRobotIO.DO, id, status)
    log(FalconLogLevel.Info, "设置机器人$vehicle 的DO id:$id status:$status")
    setBlockOutputParams(mapOf("result" to resultStr))
  }

  companion object {

    val def = BlockDef(
      RobotSetDOBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(
        BlockInputParamDef("tomId", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.RobotScene)),
        BlockInputParamDef("vehicle", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.RobotName)),
        BlockInputParamDef("id", BlockParamType.Long, true),
        BlockInputParamDef("status", BlockParamType.Boolean, false)
      ),
      outputParams = listOf(
        BlockOutputParamDef("result", BlockParamType.String)
      )
    )

  }

}