package com.seer.trick.robot.sto

import com.seer.trick.Cq

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import java.util.*

/**
 * 负责读写简单运单
 */
object StoStore {

  fun create(order: StOrder): String {
    return EntityRwService.createOne("SimpleTransportOrder", order.toEv())
  }

  fun updateCurrentMove(orderId: String, currentMove: Int) {
    EntityRwService.updateOne("SimpleTransportOrder", Cq.idEq(orderId), mutableMapOf("currentMove" to currentMove))
  }

  fun updateOrderStatus(
    order: StOrder,
    status: StOrderStatus, errorMsg: String, doneOn: Date?
  ) {
    order.status = status
    order.doneOn = doneOn
    order.errorMsg = errorMsg
    EntityRwService.updateOne("SimpleTransportOrder", Cq.idEq(order.id), order.toEv())
  }

  fun updateOrderStatus(
    orderId: String,
    status: StOrderStatus, errorMsg: String, doneOn: Date?
  ) {
    val update: EntityValue = mutableMapOf("status" to status.name, "errorMsg" to errorMsg, "doneOn" to doneOn)
    EntityRwService.updateOne("SimpleTransportOrder", Cq.idEq(orderId), update)
  }

  fun updateOrder(order: StOrder) {
    EntityRwService.updateOne("SimpleTransportOrder", Cq.idEq(order.id), order.toEv())
  }

  fun existsById(orderId: String): Boolean {
    return null !=
        EntityRwService.findOne("SimpleTransportOrder", Cq.idEq(orderId), FindOptions(projection = listOf("id")))
  }

  fun loadOrder(orderId: String): StOrder? {
    val ev = EntityRwService.findOneById("SimpleTransportOrder", orderId) ?: return null
    return StOrder.fromEv(ev)
  }

  /**
   * 应该最多有一个
   */
  fun loadCurrentOrder(robotName: String): StOrder? {
    val ev = EntityRwService.findOne(
      "SimpleTransportOrder",
      Cq.and(
        listOf(
          Cq.eq("robotName", robotName),
          Cq.include(
            "status", listOf(
              StOrderStatus.Created.name, StOrderStatus.Failed.name
            )
          )
        )
      ),
      FindOptions(sort = listOf("createdOn"))
    ) ?: return null
    return StOrder.fromEv(ev)
  }

  fun loadLastOrder(robotName: String): StOrder? {
    val ev = EntityRwService.findOne(
      "SimpleTransportOrder",
      Cq.and(
        listOf(
          Cq.eq("robotName", robotName),
          Cq.include(
            "status", listOf(
              StOrderStatus.Done.name, StOrderStatus.Cancelled.name
            )
          )
        )
      ),
      FindOptions(sort = listOf("-createdOn")) // 注意这里
    ) ?: return null
    return StOrder.fromEv(ev)
  }

  fun loadPendingOrders(robotName: String): List<StOrder> {
    val orders = EntityRwService.findMany(
      
      "SimpleTransportOrder",
      Cq.and(
        listOf(
          Cq.eq("robotName", robotName),
          Cq.include(
            "status", listOf(
              StOrderStatus.Created.name, StOrderStatus.Failed.name
            )
          )
        )
      ),
      FindOptions(sort = listOf("createdOn"))
    )

    return orders.map { StOrder.fromEv(it) }
  }

}