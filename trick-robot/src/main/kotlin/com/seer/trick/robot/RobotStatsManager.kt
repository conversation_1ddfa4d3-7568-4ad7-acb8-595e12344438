package com.seer.trick.robot

import com.seer.trick.Cq

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.helper.BoolHelper
import com.seer.trick.helper.NumHelper
import com.seer.trick.robot.rachel.MrRobotAlertLevel
import com.seer.trick.robot.rachel.MrRobotSelfReport
import com.seer.trick.robot.stats.RobotStatsService
import com.seer.trick.robot.stats.RobotStatsType
import com.seer.trick.robot.stats.domain.ContainerStats
import com.seer.trick.robot.stats.domain.FetchRobotStateBy
import com.seer.trick.robot.stats.domain.RobotStats
import com.seer.trick.robot.tom.TomRuntimeRecord

object RobotStatsManager {

  /**
   * 转化 tom 的数据 为 RobotStats，并触发统计
   */
  fun transferTomToRobotStats(record: TomRuntimeRecord) {
    val robotStatsList = record.robots.map { tomRobotRecord ->
      val mainReport = tomRobotRecord.mainReport
      val ifError =
        mainReport?.alerts?.any { it.level == MrRobotAlertLevel.Error || it.level == MrRobotAlertLevel.Fatal }
      val rbkReport = tomRobotRecord.rbkReport
      val vx = NumHelper.anyToDouble(rbkReport?.get("vx"))
      val vy = NumHelper.anyToDouble(rbkReport?.get("vy"))
      val w = NumHelper.anyToDouble(rbkReport?.get("w"))
      val odo = NumHelper.anyToDouble(rbkReport?.get("odo"))
      val robotStats = RobotStats(
        battery = mainReport?.battery ?: 0.0,
        todayOdo = mainReport?.todayOdo ?: 0.0,
        vx = vx ?: 0.0,
        vy = vy ?: 0.0,
        w = w ?: 0.0,
        odo = odo ?: 0.0,
        charging = mainReport?.charging ?: false,
        working = tomRobotRecord.procBusiness,
        isLoaded = tomRobotRecord.loaded,
        online = tomRobotRecord.online,
        ifError = ifError ?: false,
        robot = tomRobotRecord.id,
        direction = radianToDegree(mainReport?.direction ?: 0.0),
        fetchBy = FetchRobotStateBy.Core,
        containers = convertToContainerStats(tomRobotRecord.rbkReport) ?: emptyList(),
      )
      robotStats
    }
    robotStatsList.forEach { e ->
      RobotStatsService.tryRecordRobotStatus(e)
    }
  }

  /**
   * 转化 single 的数据 为 RobotStats，并触发统计
   */
  fun transferSingleToRobotStats(record: MrRobotSelfReport) {
    val main = record.main
    val rawReport = record.rawReport

    // 根据导航状态，判断工作状态。
    val taskStatus = NumHelper.anyToInt(rawReport?.get("task_status")) ?: -1
    val working = listOf(2, 3).indexOf(taskStatus) > -1 // 导航状态为 2(导航中) 或者 3(暂停导航) 时，机器人处于工作状态。

    // 根据机构状态，判断载货状态
    val jackIsFull = BoolHelper.anyToBool(rawReport?.get("jack_isFull")) // 顶升车上是否有料
    val rollerIsFull = BoolHelper.anyToBool(rawReport?.get("roller_isFull")) // 辊筒车上是否有料。 TODO:多辊筒咋办
    val forkPressure = NumHelper.anyToDouble(rawReport?.get("fork_pressure_actual")) ?: 0.0 // 叉车货叉称得的货物重量，但不是所有叉车都有称重功能
    val forkIsFull = forkPressure > 0.0 // TODO:如何更精准的判断叉车的载货状态。
    val containers = convertToContainerStats(rawReport) ?: emptyList()
    val containerIsFull = containers.any { it.hasGoods } // 料箱车其中一个储位上有货，就料箱车就是载货状态。
    val isLoaded = jackIsFull || rollerIsFull || forkIsFull || containerIsFull

    var robotName = rawReport?.get("vehicle_id") as String?
    // 离线的机器人
    if (robotName == null) {
      val onlineRecord = EntityRwService.findOne(
        
        "RobotPropChangeTimeline",
        Cq.eq("type", RobotStatsType.Online.name),
        FindOptions(sort = listOf("-id")),
      )
      // 单车场景下，若上一次在线记录不存在则还没有单车连接成功过，若是连接过，则上一次在线的机器人一定是当前离线状态
      robotName = onlineRecord?.get("robotName") as String? ?: return
    }
    @Suppress("UNCHECKED_CAST")
    val errors = rawReport?.get("errors") as List<Any>? ?: emptyList()

    @Suppress("UNCHECKED_CAST")
    val fatals = rawReport?.get("fatals") as List<Any>? ?: emptyList()
    // 机器人上报是否故障，包括 errors、fatals
    val isError = errors.isNotEmpty() || fatals.isNotEmpty()
    // 构造数据对象
    val singleRobotStats = RobotStats(
      robot = robotName,
      battery = main?.battery ?: 0.0,
      odo = NumHelper.anyToDouble(rawReport?.get("odo")) ?: 0.0,
      todayOdo = NumHelper.anyToDouble(rawReport?.get("today_odo")) ?: 0.0,
      vx = NumHelper.anyToDouble(rawReport?.get("vx")) ?: 0.0,
      vy = NumHelper.anyToDouble(rawReport?.get("vy")) ?: 0.0,
      w = NumHelper.anyToDouble(rawReport?.get("w")) ?: 0.0,
      direction = radianToDegree(main?.direction ?: 0.0),
      charging = main?.charging ?: false,
      working = working,
      isLoaded = isLoaded,
      online = record.isOnline(),
      ifError = isError,
      containers = containers,
      fetchBy = FetchRobotStateBy.SingleRobot,
    )

    RobotStatsService.tryRecordRobotStatus(singleRobotStats)
  }

  // 将弧度转换为角度，并规整到 0 到 360 度范围内
  private fun radianToDegree(radian: Double): Double {
    val degree = radian * (180 / Math.PI)
    return (degree % 360 + 360) % 360 // 取模确保在 [0, 360) 范围
  }

  /**
   * 将 rbk 上报的容器信息转换成 ContainerStats
   */
  private fun convertToContainerStats(rbkReport: EntityValue?): List<ContainerStats>? {
    // 从 rbkReport 获取"containers"字段，假设其类型为 List<Map<String, Any>> 或 null
    val containersRawData = rbkReport?.get("containers") as? List<Map<String, Any>> ?: return null

    // 将 List<Map<String, Any>> 转换为 List<ContainerStats>
    return containersRawData.map { container ->
      ContainerStats(
        containerName = container["container_name"] as? String ?: "",
        goodsId = container["goods_id"] as? String,
        hasGoods = container["has_goods"] as? Boolean ?: false,
      )
    }
  }
}