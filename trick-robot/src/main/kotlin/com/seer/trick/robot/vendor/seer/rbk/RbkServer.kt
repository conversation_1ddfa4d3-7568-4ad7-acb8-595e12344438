package com.seer.trick.robot.vendor.seer.rbk

import io.netty.bootstrap.ServerBootstrap
import io.netty.channel.ChannelFuture
import io.netty.channel.ChannelHandlerContext
import io.netty.channel.ChannelInitializer
import io.netty.channel.ChannelPipeline
import io.netty.channel.nio.NioEventLoopGroup
import io.netty.channel.socket.SocketChannel
import io.netty.channel.socket.nio.NioServerSocketChannel
import org.slf4j.LoggerFactory
import java.net.InetSocketAddress

class RbkServer(
  private val port: Int,
  onMessage: (ctx: ChannelHandlerContext, msg: RbkFrame) -> Unit,
) {

  private val logger = LoggerFactory.getLogger(this::class.java)

  private var future: ChannelFuture? = null
  private var group: NioEventLoopGroup = NioEventLoopGroup()

  init {
    try {
      val b = ServerBootstrap()
      b.group(group)
        .channel(NioServerSocketChannel::class.java)
        .localAddress(InetSocketAddress(port))
        .childHandler(object : ChannelInitializer<SocketChannel>() {
          override fun initChannel(ch: SocketChannel) {
            val pipeline: ChannelPipeline = ch.pipeline()
            pipeline.addLast(RbkDecoder())
            pipeline.addLast(RbkInboundHandler(onMessage, ::onError))
          }
        })
      future = b.bind().sync() // 因为打开本地端口，应该很快
      logger.info("Rbk Server on, port=$port")
    } finally {
      group.shutdownGracefully().sync()
    }
  }

  private fun onError(e: Throwable) {
    logger.error("onError", e)
  }

  fun dispose() {
    logger.info("Rbk Server dispose, port=$port")
    try {
      future?.channel()?.closeFuture()?.sync()
    } catch (e: Exception) {
      logger.error("close future", e)
    }
    try {
      group.shutdownGracefully().sync()
    } catch (e: Exception) {
      logger.error("close group", e)
    }
  }

}