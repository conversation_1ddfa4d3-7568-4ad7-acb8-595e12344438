package com.seer.trick.robot.single.bp


import com.seer.trick.falcon.domain.*
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper

class RobotSingleForkBp : AbstractTaskChainBp() {
  
  override fun process() {
    val station = mustGetBlockInputParam("station") as String
    val operation = mustGetBlockInputParam("operation") as String
    val startHeight = getBlockInputParam("startHeight") as Double? ?: 0.100
    val forkMidHeight = getBlockInputParam("forkMidHeight") as Double? ?: 0.100
    val endHeight = getBlockInputParam("endHeight") as Double? ?: 0.500
    val forkDist = getBlockInputParam("forkDist") as Double? ?: 0.00
    val recfile = getBlockInputParam("recfile")
    val reqId = getBlockInputParam("reqId") as String? ?: IdHelper.uuidStr()
    val req = JsonHelper.mapper.writeValueAsString(
      mapOf(
        "id" to station,
        "operation" to operation,
        "start_height" to startHeight,
        "fork_mid_height" to forkMidHeight,
        "end_height" to endHeight,
        "fork_dist" to forkDist,
        "recfile" to recfile,
        "recognize" to (recfile != null)
      )
    )
    val gwRbkResult = executeTask(RbkRequest(reqId, 3051, req, "叉货/放货"))
    setBlockOutputParams(mapOf("rbkResult" to gwRbkResult))
  }
  
  companion object {
    
    val def = BlockDef(
      RobotSingleForkBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(
        BlockInputParamDef(
          "station", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.BinId, ParamObjectType.SiteId)
        ),
        BlockInputParamDef(
          "operation", BlockParamType.String, true,
          options = listOf(
            BlockInputParamOption("ForkLoad"),
            BlockInputParamOption("ForkUnload"),
            BlockInputParamOption("ForkHeight"),
            BlockInputParamOption("ForkForward")
          ),
          defaultValue = "ForkLoad"
        ),
        BlockInputParamDef("startHeight", BlockParamType.Double, defaultValue = 0.100),
        BlockInputParamDef("forkMidHeight", BlockParamType.Double, defaultValue = 0.100),
        BlockInputParamDef("endHeight", BlockParamType.Double, defaultValue = 0.500),
        BlockInputParamDef("forkDist", BlockParamType.Double, defaultValue = 0.00),
        BlockInputParamDef("recfile", BlockParamType.String),
//        BlockInputParamDef("reqId", BlockParamType.String),
      ),
      outputParams = listOf(
        BlockOutputParamDef("rbkResult", BlockParamType.String)
      )
    )
    
  }
  
}