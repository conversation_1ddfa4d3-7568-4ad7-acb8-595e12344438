package com.seer.trick.robot.single.bp


import com.seer.trick.falcon.domain.*
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper

class RobotSingleJackingBp : AbstractTaskChainBp() {
  
  override fun process() {
    val station = mustGetBlockInputParam("station") as String
    val operation = mustGetBlockInputParam("operation") as String
    val jackHeight = getBlockInputParam("jackHeight") as Double? ?: 0.010
    val usePgv = getBlockInputParamAsBool("usePgv")
    val useDownPgv = getBlockInputParamAsBool("useDownPgv")
    val recfile = getBlockInputParam("recfile")
    val reqId = getBlockInputParam("reqId") as String? ?: IdHelper.uuidStr()
    val req = JsonHelper.mapper.writeValueAsString(
      mapOf(
        "id" to station,
        "operation" to operation,
        "jack_height" to jackHeight,
        "use_pgv" to usePgv,
        "use_down_pgv" to useDownPgv,
        "recfile" to recfile,
        "recognize" to (recfile != null)
      )
    )
    val gwRbkResult = executeTask(RbkRequest(reqId, 3051, req, "顶升"))
    setBlockOutputParams(mapOf("rbkResult" to gwRbkResult))
  }
  
  companion object {
    
    val def = BlockDef(
      RobotSingleJackingBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(
        BlockInputParamDef(
          "station", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.BinId, ParamObjectType.SiteId)
        ),
        BlockInputParamDef(
          "operation", BlockParamType.String,
          options = listOf(
            BlockInputParamOption("JackLoad"),
            BlockInputParamOption("JackUnload")
          ),
          defaultValue = "JackLoad"
        ),
        BlockInputParamDef("jackHeight", BlockParamType.Double, defaultValue = 0.010),
        BlockInputParamDef("usePgv", BlockParamType.Boolean),
        BlockInputParamDef("useDownPgv", BlockParamType.Boolean),
        BlockInputParamDef("recfile", BlockParamType.String),
//        BlockInputParamDef("reqId", BlockParamType.String),
      ),
      outputParams = listOf(
        BlockOutputParamDef("rbkResult", BlockParamType.String)
      )
    )
    
  }
  
}