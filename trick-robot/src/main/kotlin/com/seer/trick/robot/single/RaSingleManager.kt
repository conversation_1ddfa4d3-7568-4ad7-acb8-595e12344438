package com.seer.trick.robot.single

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.Cq

import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.helper.FileHelper
import com.seer.trick.helper.JsonFileHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.robot.RobotAppSceneConfig
import com.seer.trick.robot.RobotAppSceneRuntime
import com.seer.trick.robot.rachel.MrRobotSelfReport
import com.seer.trick.robot.sto.StOrder
import com.seer.trick.robot.sto.StoManager
import com.seer.trick.robot.vendor.seer.rbk.RbkClient
import org.slf4j.LoggerFactory
import java.io.File
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import kotlin.collections.ArrayList

/**
 * 单车应用模式特有的管理
 */
class RaSingleManager(private val sceneConfig: RobotAppSceneConfig, val sceneRuntime: RobotAppSceneRuntime) {

  private val logger = LoggerFactory.getLogger(this::class.java)

  /**
   * 负责连接机器人
   */
  @Volatile
  var rbkClient: RbkClient = RbkClient(DEFAULT_NAME, "127.0.0.1")
    private set

  /**
   * 负责监控机器人状态
   */
  val stateAgent = RaSingleState(this)

  /**
   * 负责监控地图
   */
  private val mapAgent = RaSingleMap(this)

  /**
   * 负责记录统计数据
   */
  private val statisticAgent: RaSingleStatistic = RaSingleStatistic(this)

  @Volatile
  var singleConfig: RaSingleConfig = loadDefaultConfig()
    private set

  /**
   * 机器人模型
   */
  val robotModelMap: MutableMap<String, MutableMap<String, String>> = ConcurrentHashMap()

  fun init() {
    rbkClient.dispose()
    initRbkClient()

    stateAgent.init()
    mapAgent.init()
    initRaSingleConfig()
    statisticAgent.init()
  }

  private fun initRbkClient() {
    var host = sceneConfig.single.host
    if (host.isNullOrBlank()) host = "127.0.0.1"
    rbkClient = RbkClient(DEFAULT_NAME, host)
  }

  fun dispose() {
    stateAgent.dispose()
    mapAgent.dispose()

    rbkClient.dispose()
    statisticAgent.dispose()
  }

  fun reset() {
    rbkClient.dispose()
    initRbkClient()
  }

  /**
   * 获取单车应用状态信息简述
   */
  fun digest(): RaSingleDigest {
    val map = mapAgent.map
    // 该机器人名字仅作为单车的名称展示
    val singleRobotName = stateAgent.report?.rawReport?.get("vehicle_id") as String?
    val robotName = StoManager.mustGetRobotName()
    // 拿网关运单
    var currentSto: EntityValue? = null
    var lastSto: EntityValue? = null
    val stoRobot = StoManager.stoRobotMap[robotName]
    if (stoRobot != null) {
      currentSto = getStoOrderOrNull(stoRobot.currentOrder)
      lastSto = getStoOrderOrNull(stoRobot.lastOrder)
    }

    return RaSingleDigest(
      report = stateAgent.report,
      currentMapName = map?.name,
      currentMapMd5 = map?.md5,
      robotName = robotName,
      currentSto = currentSto,
      lastSto = lastSto,
      controlledByMe = stateAgent.controlledByMe,
      singleRobotName = singleRobotName,
      modelName = robotModelMap.values.firstOrNull()?.get("modelName"),
    )
  }

  private fun getStoOrderOrNull(order: StOrder?): EntityValue? {
    var targetSto: EntityValue? = null
    order?.let {
      targetSto = it.toEv()
      if (it.moves.isNotEmpty()) {
        targetSto!!["sourceId"] = it.moves[0]["source_id"] ?: stateAgent.report?.rawReport?.get("current_station")
        targetSto!!["targetId"] = it.moves[it.moves.size - 1]["id"]
      }
    }
    return targetSto
  }

  /**
   * 单车应用获取控制权时传的名字
   */
  fun getNickname(): String = "${sceneConfig.name}::M4GW"

  private fun initRaSingleConfig() {
    val configFile = getConfigFile()
    if (configFile.exists()) {
      val config: RaSingleConfig? = JsonFileHelper.readJsonFromFile(configFile)
      if (config != null) singleConfig = config
    }
  }

  private fun getConfigFile(): File = File(BaseCenter.baseConfig.configDir, "ra-single-config.json")

  private fun getCustomDeviceConfigFile(): File = File(BaseCenter.baseConfig.configDir, "custom-device-config.json")

  private fun loadDefaultConfig(): RaSingleConfig {
    val configStr = FileHelper.loadClasspathResourceAsString("/ra-single-config.json")
    if (configStr.isNullOrBlank()) throw RuntimeException("没有找到内置的单车信息配置文件")
    return JsonHelper.mapper.readValue(configStr, jacksonTypeRef())
  }

  fun saveDisplayFieldConfig(config: List<DisplayField>) {
    logger.info("更新单车显示字段配置")
    singleConfig = singleConfig.copy(displayFieldConfig = config)
    val configFile = getConfigFile()
    JsonFileHelper.writeJsonToFile(configFile, singleConfig, true)
  }

  fun initDisplayFieldConfig() {
    val defaultConfig = loadDefaultConfig()
    saveDisplayFieldConfig(defaultConfig.displayFieldConfig)
  }

  fun saveSpeedConfig(config: List<SpeedSetting>) {
    logger.info("更新单车速度配置")
    singleConfig = singleConfig.copy(speedConfig = config)
    val configFile = getConfigFile()
    JsonFileHelper.writeJsonToFile(configFile, singleConfig, true)
  }

  fun initSpeedConfig() {
    val defaultConfig = loadDefaultConfig()
    saveSpeedConfig(defaultConfig.speedConfig)
  }

  fun saveCustomDeviceConfig(config: List<ComponentRow>) {
    val configFile = getCustomDeviceConfigFile()
    JsonFileHelper.writeJsonToFile(configFile, config, true)
  }

  fun getCustomDeviceConfig(): List<ComponentRow> =
    JsonFileHelper.readJsonFromFile(getCustomDeviceConfigFile()) ?: emptyList()

  fun listRobotRuntime(startTime: Date, endTime: Date): List<RobotRuntime> {
    val reqStartDateTime = startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()
    val reqEndDateTime = endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()
    val dataTimeList: MutableList<LocalDateTime> = ArrayList()
    var startDateTime = reqStartDateTime
    if (startDateTime.plusDays(1).isBefore(reqEndDateTime)) throw BzError("errCannotExceedStatisticTimeSpan", 1)
    while (startDateTime.isBefore(reqEndDateTime) && startDateTime.plusHours(1).isBefore(reqEndDateTime)) {
      // 按小时统计
      startDateTime = startDateTime.plusHours(1)
      dataTimeList += startDateTime
    }
    dataTimeList += reqEndDateTime

    val records = EntityRwService.findMany(
      
      "RaSingleNavigateRecord",
      Cq.and(
        listOf(
          Cq.gte("createdOn", startTime),
          Cq.lt("createdOn", endTime),
          Cq.eq("taskStatus", SeerRobotState.ROBOT_RUNNING),
        ),
      ),
      FindOptions(sort = listOf("createdOn")),
    )
    val runtimeList: MutableList<RobotRuntime> = ArrayList()
    var start = reqStartDateTime
    var index = 0
    for (dateTime in dataTimeList) {
      var cost = 0.0
      while (index < records.size) {
        val record = records[index]
        val createdOn = record["createdOn"] as Date
        val createdDateTime = createdOn.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()
        if (createdDateTime.isBefore(dateTime)) {
          cost += (record["cost"] ?: 0.0) as Double
        } else {
          break
        }
        index++
      }
      val costBigDecimal = BigDecimal("$cost")
      // 将毫秒转分钟
      cost = costBigDecimal.divide(BigDecimal("60000"), 2, RoundingMode.HALF_UP).toDouble()
      runtimeList += RobotRuntime(
        Date.from(start.atZone(ZoneId.systemDefault()).toInstant()),
        Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant()),
        cost,
      )
      start = dateTime
    }
    return runtimeList
  }

  fun listBatteryLevel(startTime: Date, endTime: Date): List<BatteryLevel> {
    val reqStartDateTime = startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()
    val reqEndDateTime = endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime()
    if (reqStartDateTime.plusDays(3).isBefore(reqEndDateTime)) throw BzError("errCannotExceedStatisticTimeSpan", 3)
    val records = EntityRwService.findMany(
      
      "RaSingleBatteryRecord",
      Cq.and(
        listOf(
          Cq.gte("createdOn", startTime),
          Cq.lt("createdOn", endTime),
        ),
      ),
      FindOptions(sort = listOf("createdOn")),
    )

    val result: MutableList<BatteryLevel> = ArrayList()
    for (record in records) {
      result += BatteryLevel((record["createdOn"] as Date), (record["batteryLevel"] ?: 0.0) as Double)
    }

    return result
  }

  fun getMotor2MaxLengthMeter(): Double {
    val maxLength = getPropFromMotor("motor2", "func", "maxLength")
    val type = maxLength.get("type").asText() // motor2 的 linear 的 maxLength 的数据类型就是 double 。
    return maxLength.get("${type}Value").asDouble()
  }

  fun getMotor2MinLengthMeter(): Double {
    val minLength = getPropFromMotor("motor2", "func", "minLength")
    val type = minLength.get("type").asText() // motor2 的 linear 的 minLength 的数据类型就是 double 。
    return minLength.get("${type}Value").asDouble()
  }

  /**
   * 从机器人模型文件中，获取 motor 的某个属性。
   * @param motorName motor 的名称，通过 Roboshop Pro 确认具体值。
   * @param propType motor 的属性的分类，通过 Roboshop Pro 确认具体值，例如：basic, func
   * @param propName motor 的属性的名称，通过 Roboshop Pro 确认具体值，例如：maxSpeed, minSpeed
   * @return
   */
  private fun getPropFromMotor(motorName: String, propType: String, propName: String): JsonNode {
    val type = getMotorByName(motorName).get("deviceParams")?.find { it.get("key")?.asText() == propType }
      ?: throw BzError("errMotorTypeNotFound", motorName, propType)

    val propList = when (val paramType = type.get("type").asText()) {
      "arrayParam" -> getArrayParams(type)
      "comboParam" -> getComboParams(type)
      else -> throw BzError("errMotorParamTypeUnsupported", motorName, propType, paramType)
    }

    return propList.find { it.get("key")?.asText() == propName }
      ?: throw BzError("errMotorPropNotFound", motorName, propType, propName)
  }

  private fun getMotorByName(motorName: String): JsonNode {
    return listModelMotorList().firstOrNull { it["name"].asText() == motorName }
      ?: throw BzError("errTargetMotorNotFound", motorName)
  }

  /**
   * 从缓存的机器人模型文件中，获取 motor 列表
   */
  private fun listModelMotorList(): JsonNode {
    val model = robotModelMap.values.firstOrNull() ?: throw BzError("errModelNotFound")
    return JsonHelper.mapper.readTree(model["motorListStr"]) ?: throw BzError("errMotorNotFound") // 可能为 null
  }

  /**
   * 从机器人的模型文件中，找到数据结构为 arrayParam 的属性的集合。
   */
  private fun getArrayParams(node: JsonNode): JsonNode {
    return node.get("arrayParam").get("params")
  }

  /**
   * 从机器人的模型文件中，找到数据结构为 comboParam 的属性的集合。
   */
  private fun getComboParams(node: JsonNode): JsonNode {
    val comboParam = node.get("comboParam")
    val selectedCpName = comboParam.get("childKey").asText()
    // 解析的是机器人正在使用的模型文件，一定是能通过 selectedCpName 找到对应的属性（linear、steer...）的
    return comboParam.get("childParams").find { it.get("key").asText() == selectedCpName }!!.get("params")
  }

  fun rbkEmergencyStop(): Boolean =
    stateAgent.report?.main?.softEmc == true || stateAgent.report?.main?.emergency == true

  companion object {

    const val DEFAULT_NAME = "default"
  }
}

/**
 * 单车应用状态信息简述
 */
data class RaSingleDigest(
  val report: MrRobotSelfReport?,
  val currentMapName: String?,
  val currentMapMd5: String?,
  val robotName: String?,
  val currentSto: EntityValue?,
  val lastSto: EntityValue?,
  val controlledByMe: Boolean?,
  val singleRobotName: String?, // 单车机器人名称，仅作为展示
  val modelName: String?, // 机器人型号名
)

data class RaSingleConfig(
  // 显示的字段配置
  val displayFieldConfig: List<DisplayField>,
  // 限定速度配置
  val speedConfig: List<SpeedSetting>,
)

data class DisplayField(val id: Int, val label: String, val value: String, val expression: String)

data class SpeedSetting(
  val label: String,
  val level: String,
  // 当前档位下，机器人的速度（vx）上限
  val vxMax: Double,
  // 仅对全向车的控制有效；当前档位下，机器人的横移速度（vy）上限。仅针对全向车
  val vyMax: Double,
  // 当前档位下，机器人的旋转速度（w）上限
  val omegaMax: Double,
)

data class RobotRuntime(
  val startTime: Date,
  val endTime: Date,
  val cost: Double, // 分钟
)

// 某个分钟的电量
data class BatteryLevel(val time: Date, val value: Double)

data class ComponentRow(val id: Int, val components: List<DeviceComponent>)

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class DeviceComponent(
  val id: Int,
  // 组件类型
  val type: ComponentType,
  // 标签类型的属性
  val content: String?,
  val nextSpace: Double?,
  val textColor: String?,
  val backgroundColor: String?,
  val rightAlign: Boolean?,
  // 变量类型的属性：表达式
  val expression: String?,
  // 按钮类型的属性
  val operation: String?,
  val label: String?,
  val theme: ButtonTheme?,
  // 文本框类型的属性
  val variableName: String?,
  val defaultValue: Any?,
  val textType: TextType?,
  val unit: String?,
)

enum class ComponentType {
  Text,
  Variable,
  Label,
  Button,
}

enum class TextType {
  Text,
  Integer,
  Double,
}

enum class ButtonTheme {
  Main,
  Secondary,
  Ordinary,
  Warning,
  Disable,
}

object SeerRobotState {
  const val ROBOT_NONE = 0
  const val ROBOT_WAITING = 1
  const val ROBOT_RUNNING = 2
  const val ROBOT_SUSPENDED = 3
  const val ROBOT_COMPLETED = 4
  const val ROBOT_FAILED = 5
  const val ROBOT_CANCELED = 6
}