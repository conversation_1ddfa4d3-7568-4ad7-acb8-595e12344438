package com.seer.trick.robot.map

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.util.*

/**
 * 移动机器人场景地图。Mr 指 Mobile Robot。因为像 Area、Site、Bin 容易重名，因此加前缀。
 * 站点、路径、特区的 id 必须在整个场景内唯一，如果场景内有多个区域
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class MrSceneMap(
  val version: Long = 0,
  val lastModified: Date? = null,
  val areas: List<MrSceneArea> = emptyList(),
  //
  val areaIdCounter: Long = 0,
  val siteIdCounter: Long = 0,
  val edgeIdCounter: Long = 0,
  val zoneIdCounter: Long = 0,
)

/**
 * 区域
 */
data class MrSceneArea(
  val id: String = "",
  val name: String = "",
  val mapName: String? = null, // 地图名称，不带地图文件后缀
  val sites: List<MrSite> = emptyList(), // 站点
  val edges: List<MrEdge> = emptyList(), // 路径
  val zones: List<MrZone> = emptyList(), // 特区
  val bins: List<MrBin> = emptyList(), // 库位
  val bound: MinMaxXY = MinMaxXY(),
  val lines: List<MrAdvancedLine> = emptyList(), // shop 中的高级线
  val doors: List<MrDoor> = emptyList(),
  val backgroundImage: MrBackgroundImage? = null, // 背景图
)

data class MrSite(
  val id: String = "", // 还是必须有的
  val type: String = "",  // 站点类型，如 LocationMark
  val x: Double = 0.0,
  val y: Double = 0.0,
  val direction: Double? = null,
  val ignoreDirection: Boolean = false,   // 忽略角度
  val disabled: Boolean = false,
  val park: Boolean = false, // 可停靠
  val noRotation: Boolean = false, // 不可旋转
  val notForMakeWay: Boolean = false, // 不能用于避让
) {
  // val instanceName by lazy {
  //   siteTypeToNamePrefix[type] + id
  // }
  //
  // companion object {
  //
  //   private val siteTypeToNamePrefix = mapOf(
  //     "LocationMark" to "LM",
  //     "ActionPoint" to "AP",
  //     "ParkPoint" to "PP",
  //     "ChargePoint" to "CP",
  //     "TransferLocation" to "TL",
  //     "WorkingLocation" to "WL",
  //     "SwitchMap" to "SM",
  //     "HomeRegion" to "HR"
  //   )
  // }
}

data class MrEdge(
  val id: String = "",
  val fromId: String = "",
  val fromZ: Double? = null,
  val toId: String = "",
  val toZ: Double? = null,
  var dir: EdgeDirection? = null,
  val curveType: CurveType? = null,
  val controls: List<Point2D>? = null, // 控制点
  val length: Double? = null,
  val disabled: Boolean = false,
  val noRotation: Boolean = false, // 不可旋转
  val notForDeadlock: Boolean = false, // 解死锁是否可穿行（默认可以）：如果车进入解死锁状态，能不能经过这条线路。比如工作站（导致靠近一些问题）、充点附近。
  val forRobotGroups: List<String>? = null, // 限制可用机器人组
  val driveDirections: List<DriveDirection>? = null // 限制行驶方向
)

enum class EdgeDirection {
  Dual, Forward
}

enum class CurveType {
  StraightPath, BezierPath, ArcPath, DegenerateBezier, NURBS6
}

data class MinMaxXY(
  val init: Boolean? = null,
  val minX: Double? = null,
  val maxX: Double? = null,
  val minY: Double? = null,
  val maxY: Double? = null,
)

data class MrZone(
  val id: String = "",
  val type: String,
  val x: Double = 0.0,
  val y: Double = 0.0,
  val width: Double = 0.0,
  val height: Double = 0.0,
  val direction: Double = 0.0,
  val disabled: Boolean = false,
  val points: List<Point2D> = emptyList(),
)

data class MrBin(
  val binId: String,
  val siteId: String
)

enum class DriveDirection {
  Forward, Backward, Shift
}

data class AreaIndex(
  val areaIndex: Int,
  val area: MrSceneArea,
)

data class SiteIndex(
  val areaId: String,
  val areaIndex: Int,
  val siteIndex: Int,
  val site: MrSite,
)

data class BinIndex(
  val areaId: String,
  val areaIndex: Int,
  val binIndex: Int,
  val siteId: String,
  val bin: MrBin,
)

data class ZoneIndex(
  val zoneId: String,
  val zone: MrZone,
  val siteIds: Set<String>, // 特区内的站点
)

data class AreaSite(
  val area: MrSceneArea,
  val site: MrSite,
)

/**
 * 机器人在场景中的位置
 */
data class SceneLocation(
  val area: MrSceneArea,
  val site: MrSite?,
  val x: Double,
  val y: Double,
)

data class Point2D(
  val x: Double,
  val y: Double,
  val z: Double? = null, // NURBS6 使用
) {
  override fun toString(): String {
    return "[${x},${y}]"
  }
}

data class MrAdvancedLine(
  val className: String,
  val instanceName: String,
  val points: List<Point2D>,
)

data class MrDoor(
  val name: String,
  val points: Point2D,
)

data class MrBackgroundImage(
  val fileName: String,
  val points: List<Point2D> = emptyList()
)
