package com.seer.trick.robot.vendor.hai

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.seer.trick.base.entity.EntityValue
import io.netty.channel.ChannelHandlerContext
import java.math.BigInteger
import java.util.*

class HaiRobotRuntime(
  val id: String,
  @Volatile
  var ctx: ChannelHandlerContext
) {

  @Volatile
  var report: HaiRobotReport? = null

  @Volatile
  var currentCmd: MoveCmd? = null

  @Volatile
  var lastDoneCmdSeqNum: Int? = null

  @Volatile
  var sendUnfinishedList = mutableListOf<String>()

  private var msgSeq = 0

  @Volatile
  var reportCount = 0

  @Synchronized
  fun nextMsgSeq(): Int {
    val v = if (msgSeq == Int.MAX_VALUE) 0 else msgSeq + 1
    msgSeq = v
    return v
  }

}


@JsonIgnoreProperties
data class HaiRobotReport(
  val rawReport: EntityValue,
  val main: HaiRobotReportMain
)

data class HaiRobotReportMain(
  val seqNum: BigInteger,
  val robotId: String,
  val robotState: Int, // 机器人状态，定义: RobotState
  val timestamp: Date = Date()
)


data class MoveCmd(
  val robotId: String,
  val seqNum: Int,
  val body: EntityValue
)
