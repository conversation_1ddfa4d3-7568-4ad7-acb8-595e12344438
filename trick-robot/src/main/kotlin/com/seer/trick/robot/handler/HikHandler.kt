package com.seer.trick.robot.handler

import com.fasterxml.jackson.annotation.JsonAlias
import com.seer.trick.Cq

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.FieldMeta
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.file.FileManager
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.noAuth
import com.seer.trick.helper.FileHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.NumHelper
import com.seer.trick.helper.StringHelper
import io.javalin.http.ContentType
import io.javalin.http.Context
import org.apache.commons.io.FileUtils
import org.apache.commons.io.IOUtils
import java.io.File
import java.io.FileInputStream

object HikHandler {

  @Volatile
  private var lmapMd5 = ""

  @Volatile
  private var podConfigMd5 = ""

  @Volatile
  private var zipFile: File? = null

  fun registerHandlers() {
    val c = Handlers("api/wcs/hik")
    c.get("resources", HikHandler::getResource, noAuth())
  }

  private fun getResource(ctx: Context) {
    

    // 用不到，暂时所有车资源相同
    // val deviceCodeStr = ctx.queryParam("deviceCode")

    val codeStr = ctx.queryParam("code")
    val codes = StringHelper.splitTrim(codeStr, ",")

    val pEv = EntityRwService.findOne("HikResourcePack", Cq.eq("active", true))
    val lmap = fileFvToResItem(pEv?.get("lmap"))
    var podConfig = fileFvToResItem(pEv?.get("podConfig"))
    if (podConfig != null) {
      podConfig = podConfig.copy(name = "pod_conf.xml") // 名字必须是这个
    }

    if (codes.isEmpty()) {
      if (pEv == null) {
        ctx.result("""{}""")
      } else {
        val files: MutableList<HikResourceItem> = ArrayList()
        if (lmap != null) files += lmap
        if (podConfig != null) files += podConfig
        ctx.result(JsonHelper.writeValueAsString(HikListResourceResult(files, files.size)))
      }
      ctx.contentType(ContentType.APPLICATION_OCTET_STREAM)
    } else {
      // 先不管请求几个，都给
      var zipFile = zipFile
      if (!(lmap != null && lmap.md5 == lmapMd5 && podConfig != null && podConfig.md5 == podConfigMd5
            && zipFile != null)
      ) {
        // 重新构建
        val dir = FileManager.nextTmpFile()
        dir.mkdirs()
        if (lmap != null) copyFile(File(dir, lmap.name), pEv?.get("lmap"))
        if (podConfig != null) copyFile(File(dir, podConfig.name), pEv?.get("podConfig"))

        zipFile = FileManager.nextTmpFile("zip")
        FileHelper.zipDirToFile(dir, zipFile)

        lmapMd5 = lmap?.md5 ?: ""
        podConfigMd5 = podConfig?.md5 ?: ""
        HikHandler.zipFile = zipFile
      }
      ctx.header("Content-Disposition", "attachment; filename=file.zip")
      ctx.contentType(ContentType.APPLICATION_OCTET_STREAM)
      FileInputStream(zipFile).use { IOUtils.copy(it, ctx.outputStream()) }
    }
  }

  @Suppress("UNCHECKED_CAST")
  private fun fileFvToResItem(fv: Any?): HikResourceItem? {
    if (fv == null) return null
    val fvEv = fv as EntityValue
    val size = NumHelper.anyToLong(fvEv[FieldMeta.FIELD_FILE_SIZE]) ?: return null
    val path = fvEv[FieldMeta.FIELD_FILE_PATH] as String? ?: return null
    val name = fvEv[FieldMeta.FIELD_FILE_NAME] as String? ?: File(path).name
    val md5 = fvEv[FieldMeta.FIELD_FILE_MD5] as String? ?: return null

    return HikResourceItem(size, name, md5)
  }

  @Suppress("UNCHECKED_CAST")
  private fun copyFile(file: File, fv: Any?) {
    if (fv == null) return
    val fvEv = fv as EntityValue
    val path = fvEv[FieldMeta.FIELD_FILE_PATH] as String? ?: return
    val srcFile = FileManager.pathToFile(path)
    FileUtils.copyFile(srcFile, file)
  }

}

data class HikListResourceResult(
  val files: List<HikResourceItem>,
  @JsonAlias("file_num")
  val fileNum: Int     // RCS 回传的资源列表中，存在在此字段 file_num
)

data class HikResourceItem(
  val size: Long,
  val name: String,
  val md5: String
)