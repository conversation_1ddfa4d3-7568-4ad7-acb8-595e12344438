package com.seer.trick.robot.single.bp

import com.seer.trick.BzError

import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockInputParamOption
import com.seer.trick.falcon.domain.BlockOutputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.robot.RobotAppManager

class RobotSingleGetOneStatusBp : AbstractBp() {

  override fun process() {
    val queryStatus = mustGetBlockInputParam("status") as String
    val scene = RobotAppManager.getEnabledSingleSceneOrNull() ?: throw BzError("errRobotAppNoScene")
    val rmSingle = scene.single!!
    val rawReport = rmSingle.stateAgent.report?.rawReport
    val status: Any? =
      when (queryStatus) {
        "batteryLevel" -> rawReport?.get("battery_level")
        "currentStation" -> rawReport?.get("current_station")
        "charging" -> rawReport?.get("charging")
        else -> throw BzError("errNotSupportedStatus", queryStatus)
      }
    setBlockOutputParams(mapOf("status" to status))
  }

  companion object {

    val def = BlockDef(
      RobotSingleGetOneStatusBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(
        BlockInputParamDef(
          "status",
          BlockParamType.String,
          true,
          options = listOf(
            BlockInputParamOption("batteryLevel"),
            BlockInputParamOption("currentStation"),
            BlockInputParamOption("charging"),
          ),
        ),
      ),
      outputParams = listOf(BlockOutputParamDef("status", BlockParamType.Any)),
    )
  }
}