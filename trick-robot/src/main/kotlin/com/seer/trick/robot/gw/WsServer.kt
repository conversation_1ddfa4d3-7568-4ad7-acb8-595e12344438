package com.seer.trick.robot.gw


import com.seer.trick.robot.gw.GwCenter.bgCacheExecutor
import io.javalin.Javalin
import io.javalin.websocket.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.time.Duration
import java.time.temporal.ChronoUnit

object WsServer {

  private val logger: Logger = LoggerFactory.getLogger(WsServer::class.java)

  @Volatile
  private var wsServer: Javalin? = null

  fun init(gwConfig: GwConfig) {
    val config = gwConfig.webSocketServerConfig
    if (!config.enabled) return

    logger.info("网关启动 WebSocket 服务器，端口={}", config.port)
    val server = Javalin.create { jc ->
      jc.showJavalinBanner = false
      if (config.gzip) jc.compression.gzipOnly()

      jc.plugins.enableCors { cors ->
        cors.add { it.anyHost() }
      }
      jc.jetty.wsFactoryConfig { c ->
        c.maxFrameSize = 1024 * 1024 * 50 // 50M
        c.maxTextMessageSize = 1024 * 1024 * 50 // 50M
        c.idleTimeout = Duration.of(60, ChronoUnit.SECONDS)
      }
    }
    server.ws("/ws") { ws: WsConfig -> handleWebSocket(ws) }
    server.start(config.port)
    wsServer = server
  }

  fun dispose() {
    wsServer?.close()
  }

  private fun handleWebSocket(ws: WsConfig) {
    ws.onConnect { ctx: WsConnectContext ->
      logger.info("收到 WebSocket 连接：{}", ctx.session.remote)
    }
    ws.onMessage { ctx: WsMessageContext ->
      val req = ctx.messageAsClass(GwRbkRequest::class.java)
      
      bgCacheExecutor.submit {
        try {
          val res = LocalRobots.requestRbk(req)
          ctx.send(res) // convert to json
        } catch (e: Exception) {
          logger.error("处理 GwRbkRequest 报错，{}", ctx.session.remote, e)
        }
      }
    }
    ws.onClose { ctx: WsCloseContext ->
      logger.info("WebSocket 已关闭，{}", ctx.session.remote)
    }
    ws.onError { ctx: WsErrorContext ->
      logger.error("WebSocket 报错，{}", ctx.session.remote, ctx.error())
    }
  }

}