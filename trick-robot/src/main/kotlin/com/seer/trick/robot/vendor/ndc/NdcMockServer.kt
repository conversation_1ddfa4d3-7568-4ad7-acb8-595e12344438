package com.seer.trick.robot.vendor.ndc


import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.helper.NumHelper
import com.seer.wcs.device.tcp.TcpServer
import io.netty.buffer.ByteBuf
import io.netty.buffer.ByteBufUtil
import io.netty.buffer.CompositeByteBuf
import io.netty.buffer.Unpooled
import io.netty.channel.ChannelHandlerContext
import org.slf4j.LoggerFactory
import java.util.concurrent.CopyOnWriteArrayList

object NdcMockServer {

  private val logger = LoggerFactory.getLogger(this::class.java)

  private var gwList: List<ChannelHandlerContext> = CopyOnWriteArrayList()

  private var serv: TcpServer<NdcFrame>? = null

  fun start() {
    val port = NumHelper.anyToInt(BzConfigManager.getByPath("ScWcs", "ndc", "port")) ?: 6000
    serv = TcpServer("Ndc仿真服务器", port, NdcTcp.schema, false, NdcMockServer::onServerMessage)
  }

  fun dispose() {
    try {
      if (serv != null) {
        logger.info("NdcMockServer dispose 关闭仿真服务器")
        serv?.dispose()
      }
    } finally {
      serv = null
    }
  }

  private fun onServerMessage(ctx: ChannelHandlerContext, frame: NdcFrame) {
    try {
      val headStr = ByteBufUtil.hexDump(frame.headBuf, 0, frame.headLength)
      val bodyStr = ByteBufUtil.hexDump(frame.bodyBuf, 0, frame.bodyLength)
      if (frame.bodyLength > 0)
        logger.info("NDC 收到 GW 消息: headLength=${frame.headLength} bodyLength=${frame.bodyLength} headBuf=$headStr bodyBuf=$bodyStr")
      
      if (!gwList.contains(ctx)) gwList += ctx
    } finally {
      frame.headBuf.release()
      frame.bodyBuf.release()
    }
  }

  private fun buildBa(req: ReceiveBa): CompositeByteBuf {
    val body: ByteBuf = Unpooled.buffer(10)
    body.writeShort(NdcMsgType.Ba.code) // msg type
    body.writeShort(NdcMsgType.Ba.bodyLength - 4) // rest length
    body.writeShort(req.index) // index 2
    body.writeByte(req.transportStructure) // transport structure  1
    body.writeByte(req.status) // status 1
    body.writeByte(req.parNo) // par no 1
    body.writeByte(0)

    return NdcTcp.buildFrame(1, body)
  }

  fun sendBa(req: ReceiveBa) {
    val msg = buildBa(req)
    gwList.forEach {
      it.writeAndFlush(msg)
    }
  }

  private fun buildBb(req: ReceiveBb): CompositeByteBuf {
    val body: ByteBuf = Unpooled.buffer(12)
    body.writeShort(NdcMsgType.Bb.code) // msg type
    body.writeShort(NdcMsgType.Bb.bodyLength - 4) // rest length
    body.writeShort(req.index) // index 2
    body.writeByte(req.transportStructure) // transport structure  1
    body.writeByte(req.status) // status 1
    body.writeByte(req.parNo) // par no 1
    body.writeByte(req.spare) // spare 1
    body.writeShort(req.ikey) // ikey 2

    return NdcTcp.buildFrame(1, body)
  }

  fun sendBb(req: ReceiveBb) {
    val msg = buildBb(req)
    gwList.forEach {
      it.writeAndFlush(msg)
    }
  }


  private fun buildS(req: ReceiveS): CompositeByteBuf {
    val body: ByteBuf = Unpooled.buffer(16)
    body.writeShort(NdcMsgType.S.code) // msg type 2
    body.writeShort(NdcMsgType.S.bodyLength - 4) // rest length 2
    body.writeShort(req.index) // index 2
    body.writeByte(req.transportStructure) // transport structure 1
    body.writeByte(req.orderStatus) // order status 1
    body.writeShort(req.magic) // magic 2
    body.writeShort(req.magic2) // magic2 2
    body.writeByte(req.carNo) // car no 1
    body.writeByte(req.spare) // spare 1
    body.writeShort(req.carStat) // car stat 2
    // body.writeShort(req.carStn) // car stn 2
    // body.writeShort(req.magic3) // magic3 2

    return NdcTcp.buildFrame(1, body)
  }

  fun sendS(req: ReceiveS) {
    val msg = buildS(req)
    gwList.forEach {
      it.writeAndFlush(msg)
    }
  }

  private fun buildPong(): CompositeByteBuf {
    return NdcTcp.buildFrame(5, Unpooled.buffer())
  }

  fun pong() {
    val msg = buildPong()
    gwList.forEach {
      it.writeAndFlush(msg)
    }

  }
}