package com.seer.trick.robot.rachel.dispatching


import com.seer.trick.base.soc.SocService
import com.seer.trick.robot.map.AreaSite
import com.seer.trick.robot.map.MrSite
import com.seer.trick.robot.rachel.*
import com.seer.trick.robot.rachel.scheduler.MrScheduleService
import org.slf4j.LoggerFactory
import java.util.*

/**
 * 机器人停靠
 */
class MrParkingService(private val rachel: RaRachelManager) {

  private val logger = LoggerFactory.getLogger(this::class.java)

  fun tryParking() {
    val noBestStartRobotIds = mutableListOf<String>()

    val parkingRobots = rachel.robots.values
      .filter(rachel.dispatchOrderService::canRobotAllocated)
      .filter(::shouldRobotParking).mapNotNull {
        val bestStart = it.selfReport?.main?.let { main -> rachel.sceneRuntime.map.findBestStart(main) }
        if (bestStart != null) RobotWithBestStart(it, bestStart)
        else {
          noBestStartRobotIds += it.id
          null
        }
      }
    val idleParkingSites = listIdleParkingSites()

    val assigned = mutableMapOf<String, String>() // robot -> parking site
    if (parkingRobots.isNotEmpty() && idleParkingSites.isNotEmpty()) {
      val costs = mutableListOf<ParkingCost>()
      for (r in parkingRobots) {
        for (ps in idleParkingSites) {
          val cost = rachel.sceneRuntime.map.calcCost(r.bestStart.site.id, ps)
          if (cost != null && cost != Double.MAX_VALUE) {
            costs += ParkingCost(r.rr, r.bestStart.site.id, ps, cost)
          }
        }
      }
      // 就地排序
      costs.sortBy { it.cost }
      for (c in costs) {
        if (assigned.containsValue(c.parkingSite)) continue // 检查停靠点是否已被分配
        if (assigned.contains(c.rr.id)) continue
        doParkingOrder(c.rr, c.parkingSite)
        assigned[c.rr.id] = c.parkingSite

      }
    }

    val msg = (if (noBestStartRobotIds.isNotEmpty()) "找不到停靠起点的机器人：$noBestStartRobotIds" else null) +
        (" 待停靠机器人：" + parkingRobots.joinToString(", ") { it.rr.id + "@" + it.bestStart }) +
        " 空闲停靠点：$idleParkingSites" +
        " 分配停靠：$assigned"
    SocService.updateNode("机器人", "FleetParking", "通用停靠", msg)
    //校验停靠是否完成或取消，为了解耦在每次派单停靠后检查清理
    rachel.scheduleService.checkParkingOrder()
  }

  /**
   * 在线、可接单等在调用前判断
   */
  private fun shouldRobotParking(rr: MrRobotRuntime): Boolean {
    val basic = rr.cmdStatus == MrRobotCmdStatus.Idle && rr.orders.isEmpty()
        // 空闲 N 秒后去停靠
        && (System.currentTimeMillis() - rr.idleFrom.time > 1000 * 5L)
    if (!basic) return false
    // 如果当前已经在一个停靠点则不再停靠
    val currentSiteId = rr.selfReport?.main?.currentSite
    val currentSite = currentSiteId?.let { rachel.sceneRuntime.map.sceneMapRuntime.siteIdToIndexMap[it]?.site }
    return !(currentSite != null && isParkingSite(currentSite))
  }

  private fun listIdleParkingSites(): List<String> {
    // 兼容仙工
    return rachel.sceneRuntime.map.sceneMapRuntime.sites.filter(::isParkingSite)
      .filter { rachel.scheduleService.getOwnerBySiteId(it.id) == null }
      .filter { rachel.scheduleService.checkParkingBySiteId(it.id) == null }
      .map { it.id }
  }

  // 是否是停靠点
  private fun isParkingSite(site: MrSite): Boolean {
    return site.id.startsWith("PP") || site.park
  }

  private fun doParkingOrder(rr: MrRobotRuntime, parkingSite: String) {
    val orderId = rachel.orderService.generateOrderId() + "P"

    logger.info("让机器人 ${rr.id} 停靠在 $parkingSite，停靠运单=$orderId")

    val order = MrOrder(
      kind = MrOrderKind.Parking,
      id = orderId,
      status = MrOrderStatus.Allocated, // 直接已分派
      stepFixed = true,
      stepNum = 1,
      expectedRobotNames = listOf(rr.id),
      actualRobotName = rr.id,
      robotAllocatedOn = Date(),
    )
    val step = MrStep(
      id = orderId + "Step0",
      orderId = orderId,
      status = MrStepStatus.Executable,
      interruptible = true,
      location = LocationFeature(site = parkingSite)
    )
    val os = MrOrderWithSteps(order, listOf(step))
    val or = MrOrderRuntime(os.order, os.steps)
    rachel.orderService.orders[os.order.id] = or
    MrRepo.createOrders(listOf(os))

    rr.orders[or.orderId] = or
    or.historyRobots += rr.id
    MrRepo.saveRobotAsync(rr)
    MrRepo.updateOrderAsync(or.order)
    //预定停靠站点
    rachel.scheduleService.updateParkingSiteId(orderId, parkingSite)
  }


}

data class RobotWithBestStart(
  val rr: MrRobotRuntime,
  val bestStart: AreaSite
)

data class ParkingCost(
  val rr: MrRobotRuntime,
  val bestStartSiteId: String,
  val parkingSite: String,
  val cost: Double
)