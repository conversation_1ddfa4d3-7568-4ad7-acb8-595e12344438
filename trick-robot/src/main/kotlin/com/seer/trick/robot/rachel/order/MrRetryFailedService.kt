package com.seer.trick.robot.rachel.order

import com.seer.trick.BzError

import com.seer.trick.robot.rachel.*
import org.slf4j.LoggerFactory
import java.util.*

/**
 * 重试故障的机器人
 */
class MrRetryFailedService(private val rachel: RaRachelManager) {

  private val logger = LoggerFactory.getLogger(this::class.java)

  fun retryFailedRobot(rr: MrRobotRuntime) = rachel.withKeyLock {
    logger.info(
      "重试失败的机器人 '${rr.id}'，机器人命令状态=${rr.cmdStatus}, 当前运单=${rr.currentOrder?.orderId}:${rr.currentStepIndex}"
    )

    if (rr.cmdStatus != MrRobotCmdStatus.Failed) throw BzError("errRobotNotFailed", rr.id)

    val or = rr.mustGetCurrentOrder()
    if (or.order.status != MrOrderStatus.Executing)
      throw BzError("errRetryFailedRobotButOrderNotExecuting", or.orderId, or.order.status)

    val currentStepIndex = rr.mustGetCurrentStepIndex()

    rr.cmdStatus = MrRobotCmdStatus.Idle
    rr.idleFrom = Date()
    MrRepo.saveRobotAsync(rr)

    or.order = or.order.copy(fault = false)
    MrRepo.updateOrderAsync(or.order)

    rachel.robotController.executeStep(rr, or, currentStepIndex, rr.currentStepCost)
  }

}
