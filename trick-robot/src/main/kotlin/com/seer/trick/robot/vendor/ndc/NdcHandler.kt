package com.seer.trick.robot.vendor.ndc


import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.noAuth
import com.seer.trick.base.http.getReqBody
import io.javalin.http.Context
import org.slf4j.LoggerFactory

object NdcHandler {
  private val logger = LoggerFactory.getLogger(this::class.java)

  fun registerHandlers() {
    val c = Handlers("api/wcs/gw/ndc")
    c.post("send-q", NdcHandler::sendQ, noAuth())
    c.post("receive-ba", NdcHandler::receiveBa, noAuth())
    c.post("receive-bb", NdcHandler::receiveBb, noAuth())
    c.post("receive-s", NdcHandler::receiveS, noAuth())
    c.post("receive-pong", NdcHandler::receivePong, noAuth())
    c.post("ping", NdcHandler::ping, noAuth())
    c.post("send-n", NdcHandler::sendN, noAuth())
    c.post("send-m", NdcHandler::sendM, noAuth())
  }

  private fun sendQ(ctx: Context) {
    val req: SendQ = ctx.getReqBody()
    NdcAdapter.sendQ(req.priority, req.iKey, req.fromBin, req.toBin)
    ctx.status(200)
  }

  private fun receiveBa(ctx: Context) {
    val req: ReceiveBa = ctx.getReqBody()
    NdcMockServer.sendBa(req)
    ctx.status(200)
  }

  private fun receiveBb(ctx: Context) {
    val req: ReceiveBb = ctx.getReqBody()
    NdcMockServer.sendBb(req)
    ctx.status(200)
  }

  private fun receiveS(ctx: Context) {
    val req: ReceiveS = ctx.getReqBody()
    NdcMockServer.sendS(req)
    ctx.status(200)
  }

  private fun receivePong(ctx: Context) {
    NdcMockServer.pong()
    ctx.status(200)
  }

  private fun ping(ctx: Context) {
    NdcAdapter.ping()
    ctx.status(200)
  }

  private fun sendN(ctx: Context) {
    val req: SendN = ctx.getReqBody()
    NdcAdapter.sendN(req.index)
    ctx.status(200)
  }

  private fun sendM(ctx: Context) {
    val req: SendM = ctx.getReqBody()
    NdcAdapter.sendM(req)
    ctx.status(200)
  }


}

data class SendQ(
  val priority: Int,
  val iKey: Int,
  val fromBin: Int,
  val toBin: Int,
)

data class ReceiveBa(
  val index: Int,
  val transportStructure: Int,
  val status: Int,
  val parNo: Int,
)

data class ReceiveBb(
  val index: Int,
  val transportStructure: Int,
  val status: Int,
  val parNo: Int,
  val spare: Int,
  val ikey: Int,
)

data class ReceiveS(
  val index: Int,
  val transportStructure: Int,
  val orderStatus: Int,
  val magic: Int,
  val magic2: Int,
  val carNo: Int,
  val spare: Int,
  val carStat: Int,
  val carStn: Int,
  val magic3: Int,
  val noOfLp: Int? = null
)

data class SendN(
  val index: Int,
)

data class SendM(
  val index: Int,
  val function: Int,
  val parameterNumber: Int = 0,
  val priority: Int = 0,
  val p0: Int = 0,
  val p1: Int = 0,
  val p2: Int = 0,
  val p3: Int = 0,
  val p4: Int = 0,
)
