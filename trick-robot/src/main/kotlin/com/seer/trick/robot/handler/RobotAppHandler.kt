package com.seer.trick.robot.handler

import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.getReqBody

import com.seer.trick.robot.RobotAppConfig
import com.seer.trick.robot.RobotAppManager
import io.javalin.http.Context

object RobotAppHandler {

  fun registerHandlers() {
    val c = Handlers("api/robot")
    c.get("config", ::getConfig, auth())
    c.post("config", ::updateConfig, auth())
  }

  private fun getConfig(ctx: Context) {
    ctx.json(RobotAppManager.config)
  }

  private fun updateConfig(ctx: Context) {
    
    val req: RobotAppConfig = ctx.getReqBody()
    RobotAppManager.saveConfig(req)
    ctx.status(200)
  }

}
