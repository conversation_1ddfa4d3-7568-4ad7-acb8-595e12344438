package com.seer.trick.robot.sto

import com.seer.trick.BzError

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.NumHelper
import com.seer.trick.helper.getTypeMessage
import com.seer.trick.robot.RobotAppManager
import com.seer.trick.robot.gw.GwRbkRequest
import com.seer.trick.robot.gw.LocalRobots
import com.seer.trick.robot.gw.ReportApplyClient
import com.seer.trick.robot.single.RaSingleManager
import org.slf4j.LoggerFactory

// TODO 1050 同步机器人报错信息
object StoSeer {

  private val logger = LoggerFactory.getLogger(this::class.java)

  private fun requestControl(order: StOrder) {
    logger.info("请求控制权 ${order.robotName}:${order.id}:${order.currentMove + 1}")
    val sceneOrNull = RobotAppManager.getEnabledSingleSceneOrNull()
    val nickName = sceneOrNull?.single?.getNickname() ?: "GW"
    val req4005 = JsonHelper.writeValueAsString(mapOf("nick_name" to nickName))
    try {
      requestRbk(order.robotName, 4005, req4005)
    } catch (e: Exception) {
      throw BzError("errRequestControl", order.robotName)
    }
  }

  /**
   * 尝试请求控制权，获取不到则抛异常
   */
  fun tryToRequestControl(order: StOrder) {
    val sceneOrNull = RobotAppManager.getEnabledSingleSceneOrNull()
    val rawReport = if (sceneOrNull == null) {
      ReportApplyClient.rawReport
    } else {
      sceneOrNull.single?.stateAgent?.report?.rawReport
    }
    val currentLock = rawReport?.get("current_lock") as Map<*, *>?
    val currentLockNickName = currentLock?.get("nick_name") as String?
    val isControlled = currentLock?.get("locked") as Boolean?
    val taskStatus = NumHelper.anyToInt(rawReport?.get("task_status"))
    val emergency = rawReport?.get("emergency") as Boolean?
    val softEmc = rawReport?.get("soft_emc") as Boolean?
    val isSelf = currentLockNickName == "this"
    val nickName = sceneOrNull?.single?.getNickname() ?: "GW"

    if (currentLockNickName == nickName) return

    // 导航中、急停、机器人控制权已被抢占，则不能抢占控制权
    if (listOf(1, 2, 3).indexOf(taskStatus) >= 0 ||
      emergency == true ||
      softEmc == true ||
      (isControlled == true && !isSelf)
    ) {
      throw BzError("errCannotRequestControl", rawReport?.get("vehicle_id") ?: order.robotName)
    }
    requestControl(order)
  }

  fun move3051(order: StOrder, stepIndex: Int) {
    tryToRequestControl(order)
    startMove3051(order, stepIndex)
    awaitMove3051(order, stepIndex)
  }

  private fun startMove3051(order: StOrder, stepIndex: Int) {
    val stepIndexLabel = stepIndex + 1 // 与 3066 日志保持一致：日志和报错信息中，都从 1 开始标记 move 的序号，而非从 0 开始。
    // logger.info("3051 reqId=${reqId}")
    val ev = order.moves[stepIndex]
    val req3051 = JsonHelper.writeValueAsString(ev["moveTask"])
    logger.info("发送 3051, ${order.id}:$stepIndexLabel/${order.robotName}，moveTask=$req3051")
    try {
      requestRbk(order.robotName, 3051, req3051)
    } catch (e: Exception) {
      val errMsg = "[Step $stepIndexLabel] 发送 3051 移动失败。${e.getTypeMessage()}"
      logger.error("发送 3051 ${order.id}:$stepIndexLabel/${order.robotName}，$errMsg")
      throw BzError("errMove3051", errMsg)
    }
  }

  // 0 = NONE, 1 = WAITING(目前不可能出现该状态), 2 = RUNNING, 3 = SUSPENDED, 4 = COMPLETED, 5 = FAILED, 6 = CANCELED, 404 = NOTFOUND
  // 这里查没有用 task_id
  private fun awaitMove3051(order: StOrder, stepIndex: Int) {
    val stepIndexLabel = stepIndex + 1 // 与 3066 日志保持一致：日志和报错信息中，都从 1 开始标记 move 的序号，而非从 0 开始。
    logger.info("等待 3051 完成，${order.id}:$stepIndexLabel/${order.robotName}")

    var errCount = 0
    while (true) {
      Thread.sleep(200)
      if (order.status == StOrderStatus.Cancelled) {
        logger.info("等待过程中运单被取消 ${order.id}:$stepIndexLabel/${order.robotName}")
        return
      }
      val move = order.moves[stepIndex]
      val taskId = move["task_id"] as String
      val req1110 = JsonHelper.mapper.writeValueAsString(mapOf("task_ids" to listOf(taskId)))
      val res1110 = try {
        requestRbk(order.robotName, 1110, req1110)
      } catch (e: Exception) {
        val errMsg = "[Step $stepIndexLabel] Failed to query move result. ${e.getTypeMessage()}"
        logger.error("等待 3051 完成 ${order.id}:$stepIndexLabel/${order.robotName} $errMsg")
        throw BzError("errAwaitMove", errMsg)
      }

      val n = JsonHelper.mapper.readTree(res1110)
      val tsNode = n?.get("task_status_package")?.get("task_status_list")?.asIterable()?.firstOrNull()
        ?: continue // TODO 最多等待
      val taskStatus = tsNode.get("status")?.asInt()
      val resTaskId = tsNode.get("task_id")?.asText() // TODO 搜 task id?
      if (taskId != resTaskId) {
        logger.error("查询 3051 结果，任务 ID 不匹配，期望 $taskId，实际 $resTaskId")
        throw BzError("errQuery3051BadTaskId", taskId, resTaskId)
      }
      if (taskStatus == 4) {
        return
      } else if (taskStatus == 5 || taskStatus == 6 || taskStatus == 0 || taskStatus == 404) {
        ++errCount
        // 如果是 0 或 404 重试几次
        if (errCount > 5 && (taskStatus == 0 || taskStatus == 404) || taskStatus == 5 || taskStatus == 6) {
          val errMsg = "[Step $stepIndexLabel] 导航结果=$taskStatus。"
          logger.error("3051 失败 ${order.id}:$stepIndexLabel/${order.robotName} $errMsg")
          throw BzError("errAwaitMove", errMsg)
        }
      }
    }
  }

  /**
   * 因为目前 3066 一个指令中间不能有动作点，所以，以动作点为结束，将一个大的步骤列表切分为多个 3066 指令
   */
  fun split3066MoveList(moves: List<EntityValue>): List<List<EntityValue>> {
    val moveLists = mutableListOf<List<EntityValue>>()
    var moveList = mutableListOf<EntityValue>()
    for (move in moves) {
      if (move["operation"] != null || move["binTask"] != null) {
        moveList += move
        moveLists += moveList
        moveList = mutableListOf()
      } else {
        moveList += move
      }
    }
    if (moveList.isNotEmpty()) moveLists += moveList

    return moveLists
  }

  /**
   * 将 moveList 一次性发下去
   */
  fun start3066Move(order: StOrder, moveList: List<EntityValue>) {
    val moveTaskList = moveList.map { it["moveTask"] }
    val req = mutableMapOf("move_task_list" to moveTaskList)
    val req3066 = JsonHelper.writeValueAsString(req)
    logger.debug("3066: $req3066")
    try {
      requestRbk(order.robotName, 3066, req3066)
    } catch (e: Exception) {
      val errMsg = "发送 3066 移动失败。${e.getTypeMessage()}, $moveList"
      logger.error("发送 3066 ${order.robotName}:${order.id}:${order.currentMove + 1}. $errMsg")
      throw BzError("errMove3066", errMsg)
    }
  }

  fun await3066Move(order: StOrder, move: EntityValue) {
    logger.info("等待 3066 完成，${order.id}:${order.currentMove + 1}/${order.robotName}，$move")

    var errCount = 0
    while (true) {
      Thread.sleep(200)
      if (order.status == StOrderStatus.Cancelled) {
        logger.info("等待过程中运单被取消 ${order.id}:${order.currentMove + 1}/${order.robotName}")
        return
      }
      val taskId = move["task_id"] as String
      val req1110 = JsonHelper.mapper.writeValueAsString(mapOf("task_ids" to listOf(taskId)))
      val res1110 = try {
        requestRbk(order.robotName, 1110, req1110)
      } catch (e: Exception) {
        val errMsg = "查询失败 ${e.getTypeMessage()}。$move"
        logger.error("3066 失败 ${order.id}:${order.currentMove + 1}/${order.robotName} $errMsg")
        throw BzError("errAwaitMove", errMsg)
      }

      val n = JsonHelper.mapper.readTree(res1110)
      val tsNode = n?.get("task_status_package")?.get("task_status_list")?.asIterable()?.firstOrNull()
        ?: continue // TODO 最多等待
      val taskStatus = tsNode.get("status")?.asInt()
      val resTaskId = tsNode.get("task_id")?.asText() // TODO 搜 task id?
      if (taskId != resTaskId) {
        logger.error("查询 3066 结果，任务 ID 不匹配，期望 $taskId，实际 $resTaskId")
        throw BzError("errQuery3066BadTaskId", taskId, resTaskId)
      }
      if (taskStatus == 4) {
        return
      } else if (taskStatus == 5 || taskStatus == 6 || taskStatus == 0 || taskStatus == 404) {
        ++errCount
        // 如果是 0 或 404 重试几次
        if (errCount > 5 && (taskStatus == 0 || taskStatus == 404) || taskStatus == 5 || taskStatus == 6) {
          val errMsg = "[任务状态=$taskStatus]. $move"
          logger.error("3066 失败 ${order.id}:${order.currentMove + 1}/${order.robotName} $errMsg")
          throw BzError("errAwaitMove", errMsg)
        }
      }
    }
  }

  fun cancelMove(order: StOrder) {
    logger.info("取消路径导航 ${order.id}:${order.currentMove}/${order.robotName}")
    requestControl(order)
    try {
      requestRbk(order.robotName, 3003, "")
    } catch (e: Exception) {
      val errMsg = "Failed to send cancelling move. ${e.getTypeMessage()}"
      logger.error("取消路径导航 ${order.id}:${order.currentMove}/${order.robotName}. $errMsg")
    }
  }

  fun requestRbk(robotName: String, rbkApiNo: Int, reqStr: String): String {
    val sceneOrNull = RobotAppManager.getEnabledSingleSceneOrNull()

    return if (sceneOrNull != null) {
      val single = sceneOrNull.single ?: throw BzError("errRobotNoAnyScene")
      val singleRes = single.rbkClient.requestWithReturnCodeError(rbkApiNo, reqStr)
      singleRes
    } else {
      // 无场景时只能取消单车运单
      if (robotName == RaSingleManager.DEFAULT_NAME) {
        logger.error("当前无单车场景，不能执行单车运单")
        throw BzError("errRobotNoAnyScene")
      }
      val reqId = IdHelper.oidStr()
      val gwRes = LocalRobots.requestRbk(GwRbkRequest(reqId, robotName, rbkApiNo, reqStr))
      val infoNode = JsonHelper.mapper.readTree(gwRes)
      val returnCode = infoNode["ret_code"]?.asInt()
      val errorMsg = infoNode["err_msg"]?.asText()
      if (returnCode != 0) throw BzError("errRbkReturnNonZero", returnCode, errorMsg, rbkApiNo)
      gwRes
    }
  }
}