package com.seer.trick.robot.sto


import com.seer.trick.base.soc.SocAttention
import com.seer.trick.base.soc.SocService
import com.seer.trick.robot.vendor.hai.HaiAdapter
import org.slf4j.LoggerFactory

object StoHai {

  private val logger = LoggerFactory.getLogger(this::class.java)

  fun move(order: StOrder, stepIndex: Int) {
    logger.debug("海柔机器人移动")

    // Holding 等待车连上
    var rr = HaiAdapter.robots[order.robotName]
    while (rr == null && !Thread.interrupted()) {
      SocService.updateNode(
        "GW",
        "GW:StoHai:WaitRobot:${order.robotName}",
        "等待海柔车上线::${order.robotName}",
        order.id,
        SocAttention.Red
      )
      Thread.sleep(500)
      rr = HaiAdapter.robots[order.robotName]
    }
    SocService.removeNode("GW:StoHai:WaitRobot:${order.robotName}")
    if (rr == null) return

    val step = order.moves[stepIndex]

    val seqNum = HaiAdapter.move(order.robotName, step)

    // 等待动作完成
    while (!Thread.interrupted()) {
      val last = rr.lastDoneCmdSeqNum
      val maxNum = rr.sendUnfinishedList
      if (last != null && last >= seqNum && maxNum.size < 20) return

      if (order.status == StOrderStatus.Cancelled) {
        logger.info("等待海柔完成，但运单已取消 ${order.id}")
        return
      }

      Thread.sleep(200)
    }
  }

}