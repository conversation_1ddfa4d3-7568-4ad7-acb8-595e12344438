package com.seer.trick.robot.rachel

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.Cq

import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.helper.JsonHelper
import org.apache.commons.lang3.StringUtils
import java.util.concurrent.Executors
import java.util.concurrent.Future

/**
 * 持久层。
 */
object MrRepo {

  private val executor = Executors.newSingleThreadExecutor()

  fun createOrders(req: List<MrOrderWithSteps>) {
    val evList = req.map { orderToEv(it.order) }
    EntityRwService.createMany("WcsMrOrder", evList)

    val steps = req.map { it.steps }.flatten().map { stepToEv(it) }
    EntityRwService.createMany("WcsMrOrderStep", steps)
  }

  fun createStep(req: MrStep) {
    EntityRwService.createOne("WcsMrOrderStep",  stepToEv(req) )
  }


  fun updateOrderAsync(order: MrOrder): Future<*> = executor.submit { // TODO 之类的所有都需要捕获异常
    val ev = orderToEv(order)
    EntityRwService.updateOne("WcsMrOrder", Cq.idEq(order.id), ev)
  }

  fun updateStepAsync(step: MrStep): Future<*> = executor.submit {
    val ev = stepToEv(step)
    EntityRwService.updateOne("WcsMrOrderStep", Cq.idEq(step.id), ev)
  }

  fun listOrderWithSteps(): List<MrOrderWithSteps> {
    val q = Cq.include(
      "status", listOf(
        MrOrderStatus.Building.name,
        MrOrderStatus.ToBeAllocated.name,
        MrOrderStatus.Allocated.name,
        MrOrderStatus.Executing.name,
        MrOrderStatus.Pending.name,
        MrOrderStatus.Withdrawn.name,
      )
    )
    val orderEvList = EntityRwService.findMany("WcsMrOrder", q)
    return orderEvList.map(MrRepo::evToOrder).map { order ->
      val steps = EntityRwService.findMany(
        "WcsMrOrderStep", Cq.eq("orderId", order.id), FindOptions(sort = listOf("stepIndex"))
      ).map(MrRepo::evToStep)
      MrOrderWithSteps(order, steps)
    }
  }

  private fun orderToEv(order: MrOrder): EntityValue {
    val ev: EntityValue = JsonHelper.mapper.convertValue(order, jacksonTypeRef())
    serializeLocation(ev, "loadLocation", order.loadLocation)
    serializeLocation(ev, "unloadLocation", order.unloadLocation)

    return ev
  }

  private fun evToOrder(ev: EntityValue): MrOrder {
    val order: MrOrder = JsonHelper.mapper.convertValue(ev, jacksonTypeRef())
    val loadLocation = deserializeLocation(ev, "loadLocation")
    val unloadLocation = deserializeLocation(ev, "unloadLocation")
    return order.copy(loadLocation = loadLocation, unloadLocation = unloadLocation)
  }

  private fun stepToEv(step: MrStep): EntityValue {
    val ev: EntityValue = JsonHelper.mapper.convertValue(step, jacksonTypeRef())
    serializeLocation(ev, "location", step.location)
    return ev
  }

  private fun evToStep(ev: EntityValue): MrStep {
    val step: MrStep = JsonHelper.mapper.convertValue(ev, jacksonTypeRef())
    val location = deserializeLocation(ev, "location")
    return step.copy(location = location)
  }

  private fun serializeLocation(taskEv: EntityValue, field: String, loc: LocationFeature?) {
    taskEv.remove(field)
    if (loc != null) {
      val llEv: EntityValue = JsonHelper.mapper.convertValue(loc, jacksonTypeRef())
      for ((fn, fv) in llEv) {
        taskEv[field + StringUtils.capitalize(fn)] = fv
      }
    }
  }

  private fun deserializeLocation(ev: EntityValue, field: String): LocationFeature {
    val locationEv: EntityValue = mutableMapOf()
    for ((fn, fv) in ev) {
      if (fn.startsWith(field)) {
        val subFn = StringUtils.uncapitalize(fn.substring(field.length))
        locationEv[subFn] = fv
      }
    }
    if (locationEv.isEmpty()) return LocationFeature()
    return JsonHelper.mapper.convertValue(locationEv, jacksonTypeRef())
  }

  fun saveRobotAsync(rr: MrRobotRuntime): Future<*> = executor.submit<Unit> {
    val ev = robotRuntimeToEv(rr)
    val id = EntityHelper.mustGetId(ev)
    if (null == EntityRwService.findOne("MrRobotRuntimeRecord", Cq.idEq(id))) {
      EntityRwService.createOne("MrRobotRuntimeRecord", ev)
    } else {
      EntityRwService.updateOne("MrRobotRuntimeRecord", Cq.idEq(id), ev)
    }
  }

  fun clearRobotRuntime(id: String) {
    EntityRwService.updateOne(
      "MrRobotRuntimeRecord", Cq.idEq(id), mutableMapOf(
        "id" to id,
        "rtOrders" to null,
        "rtBins" to "",
        "rtCmdStatus" to MrRobotCmdStatus.Idle.name,
        "rtCurrentOrder" to "",
        "rtCurrentStep" to ""
      )
    )
  }

  private fun robotRuntimeToEv(rr: MrRobotRuntime): EntityValue {
    return mutableMapOf(
      "id" to rr.id,
      "rtOrders" to rr.orders.values.joinToString(",") { it.orderId },
      "rtBins" to JsonHelper.writeValueAsString(rr.bins),
      "rtCmdStatus" to rr.cmdStatus.name,
      "rtCurrentOrder" to rr.currentOrder?.orderId,
      "rtCurrentStep" to rr.currentStepIndex
    )
  }

}