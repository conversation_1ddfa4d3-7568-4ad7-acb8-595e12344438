package com.seer.trick.robot.falcon


import com.seer.trick.falcon.task.FalconTaskService
import com.seer.trick.robot.tom.TerminateReq
import com.seer.trick.robot.tom.TomAgent
import org.slf4j.LoggerFactory

object RobotFalconResourceCleaner {

  private val logger = LoggerFactory.getLogger(this::class.java)

  fun init() {
    FalconTaskService.addResCleaner("TomOrder", ::cleanTomOrder)
  }

  @Suppress("UNUSED_PARAMETER")
  private fun cleanTomOrder(
    taskId: String, resType: String, resId: String, args: Map<String, Any?>?
  ) {
    if (args == null) {
      logger.error("清理调度运单，args为空")
      return
    }
    val tomId = args["tomId"] as String
    val orderId = args["orderId"] as String
    logger.info("清理调度运单，调度=$tomId，运单号=$orderId")

    val tomUrl = TomAgent.getTomUrlRoot(tomId)

    TomAgent.terminate(tomUrl, TerminateReq(listOf(orderId), true))
  }

}