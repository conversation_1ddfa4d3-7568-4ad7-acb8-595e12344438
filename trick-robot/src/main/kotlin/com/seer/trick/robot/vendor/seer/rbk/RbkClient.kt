package com.seer.trick.robot.vendor.seer.rbk

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError

import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper

/**
 * - 自动根据 API 编号选择端口请求。
 * - 延迟连接：实例化之后不是立即连接的，而是首次请求时连接的。
 * - 自动重连：当连接断开、请求出现故障时，会自动重连。遇到错误后，例如，对方关闭。本实例仍是可用，内部会重建连接。
 * - RBK 协议很可能是纯串行的协议。但这里假设可以不等第一个请求响应就发第二个请求。
 *
 * 一个 RBK 机器人
 * 机器人状态 API	19204	10
 * 机器人控制 API	19205	5
 * 机器人导航 API	19206	5
 * 机器人配置 API	19207	5
 * 其他      API	19210	5
 * 机器人推送 API	19301	10
 *
 */
class RbkClient(robotName: String, val host: String, portStart: Int = 19204) {

  private val stateClient: RbkPortClient = RbkPortClient(robotName, host, portStart) // 19204
  private val controlClient: RbkPortClient = RbkPortClient(robotName, host, portStart + 1) // 19205
  private val navClient: RbkPortClient = RbkPortClient(robotName, host, portStart + 2) // 19206
  private val configClient: RbkPortClient = RbkPortClient(robotName, host, portStart + 3) // 19207
  private val miscClient: RbkPortClient = RbkPortClient(robotName, host, portStart + 6) // 19210

  /**
   * 不会抛出异常
   */
  fun dispose() {
    stateClient.dispose()
    controlClient.dispose()
    navClient.dispose()
    configClient.dispose()
    miscClient.dispose()
  }

  /**
   * 不抛异常，返回结果
   */
  fun request(
    
    apiNo: Int,
    requestStr: String,
    reqMaxTry: Int = RbkPortClient.REQ_MAX_TRY,
    reqRetryDelay: Long = RbkPortClient.REQ_RETRY_DELAY,
  ): String = when (apiNo) {
    in 1000..1999 -> stateClient.request(apiNo, requestStr, reqMaxTry, reqRetryDelay)
    in 2000..2999 -> controlClient.request(apiNo, requestStr, reqMaxTry, reqRetryDelay)
    in 3000..3999 -> navClient.request(apiNo, requestStr, reqMaxTry, reqRetryDelay)
    in 4000..5999 -> configClient.request(apiNo, requestStr, reqMaxTry, reqRetryDelay)
    in 6000..6998 -> miscClient.request(apiNo, requestStr, reqMaxTry, reqRetryDelay)
    else -> throw IllegalArgumentException("Bad api no: $apiNo")
  }

  // ret_code 非零抛异常
  fun requestWithReturnCodeError(
    
    apiNo: Int,
    requestStr: String,
    reqMaxTry: Int = RbkPortClient.REQ_MAX_TRY,
    reqRetryDelay: Long = RbkPortClient.REQ_RETRY_DELAY,
  ): String {
    val resStr = request(apiNo, requestStr, reqMaxTry, reqRetryDelay)
    checkReturnCodeError(resStr, apiNo)
    return resStr
  }

  private fun checkReturnCodeError(resStr: String?, apiNo: Int) {
    val infoNode = JsonHelper.mapper.readTree(resStr)
    val returnCode = infoNode["ret_code"]?.asInt()
    val errorMsg = infoNode["err_msg"]?.asText()
    if (returnCode != 0) throw BzError("errRbkReturnNonZero", returnCode, errorMsg, apiNo)
  }

  //
  // 19204
  //

  fun fetchPointCloud(): JsonNode? {
    val resStr = requestWithReturnCodeError(1009, "")
    val resNode = JsonHelper.mapper.readTree(resStr)
    return resNode["lasers"]
  }

  fun fetch1000(reqStr: String): String = requestWithReturnCodeError(1000, reqStr)

  fun fetch1100(reqStr: String): String = requestWithReturnCodeError(1100, reqStr)

  /**
   * 获取机器人模型
   */
  fun fetchModel(): String = request(1500, "")

  /**
   * 查询机器人导航信息
   */
  fun queryRbkNavigation(): RbkNavigation {
    val req = mapOf("simple" to false)
    val reqStr = JsonHelper.mapper.writeValueAsString(req)
    val resStr = requestWithReturnCodeError(1020, reqStr)
    val resNode = JsonHelper.mapper.readTree(resStr)

    val finishedPath = resNode["finished_path"]?.asIterable()?.map { it.asText() }
    val unfinishedPath = resNode["unfinished_path"]?.asIterable()?.map { it.asText() }
    val containerList = resNode["containers"]?.asIterable()?.map {
      Container(
        it["container_name"]?.asText(),
        it["goods_id"]?.asText(),
        it["desc"]?.asText(),
        it["has_goods"]?.asBoolean() ?: false,
      )
    }

    return RbkNavigation(
      resNode["task_status"].asInt(),
      resNode["task_type"].asInt(),
      resNode["target_id"]?.asText(),
      finishedPath,
      unfinishedPath,
      resNode["move_status_info"]?.asText(),
      containerList,
    )
  }

  /**
   * 查询机器人身上货物信息（料箱机器人）
   */
  fun queryRobotGoods(): List<Container> = queryRbkNavigation().containers ?: emptyList()

  fun queryReLoc(): Int? {
    val resStr = requestWithReturnCodeError(1021, "")
    val resNode = JsonHelper.mapper.readTree(resStr)
    return resNode["reloc_status"]?.asInt()
  }

  fun queryMapStatus(): Int {
    val resStr = requestWithReturnCodeError(1022, "")
    val resNode = JsonHelper.mapper.readTree(resStr)
    return resNode["loadmap_status"].asInt()
  }

  fun queryOwner(): String = requestWithReturnCodeError(1060, "")

  fun queryMoveTaskStatusList(taskIds: List<String>): String {
    val req = mapOf("task_ids" to taskIds)
    val reqStr = JsonHelper.mapper.writeValueAsString(req)

    return requestWithReturnCodeError(1110, reqStr)
  }

  // 列出所有 smap
  fun listSmaps(): String = requestWithReturnCodeError(1300, "")

  fun listSmapsAsObj(): RobotMapRes {
    val reqStr = requestWithReturnCodeError(1300, "")
    return JsonHelper.mapper.readValue(reqStr, jacksonTypeRef())
  }

  data class RobotMapRes(
    @JsonAlias("current_map")
    val currentMap: String? = null,
    @JsonAlias("current_map_md5")
    val currentMapMD5: String? = null,
    @JsonAlias("maps")
    val maps: List<String>? = null,
  )

  fun getMapsMd5(maps: List<String>): Map<String, String> {
    val req = mapOf("map_names" to maps.map { "$it.smap" })
    val reqStr = JsonHelper.mapper.writeValueAsString(req)
    val resStr = requestWithReturnCodeError(1302, reqStr)
    val resNode = JsonHelper.mapper.readTree(resStr)

    val result: MutableMap<String, String> = HashMap()
    resNode["map_info"].asIterable().map { mapNode ->
      result[mapNode["name"].asText()] = mapNode["md5"].asText()
    }
    return result
  }

  // 获取机器人参数
  fun fetchParams(): String = requestWithReturnCodeError(1400, "")

  //
  // 19205
  //

  fun startReLoc(req: ReLoc) {
    val reqStr = JsonHelper.mapper.writeValueAsString(req)
    requestWithReturnCodeError(2002, reqStr)
  }

  fun confirmReLoc() {
    requestWithReturnCodeError(2003, "")
  }

  fun cancelReLoc() {
    requestWithReturnCodeError(2004, "")
  }

  fun sendOpenLoopSpeed(speed: Speed) {
    val reqStr = JsonHelper.mapper.writeValueAsString(speed)

    requestWithReturnCodeError(2010, reqStr)
  }

  fun setMap(mapName: String) {
    val req = mapOf("map_name" to mapName)
    val reqStr = JsonHelper.mapper.writeValueAsString(req)

    requestWithReturnCodeError(2022, reqStr)
  }

  //
  // 19206
  //

  fun pauseNavigation() {
    requestWithReturnCodeError(3001, "")
  }

  fun resumeNavigation() {
    requestWithReturnCodeError(3002, "")
  }

  fun cancelNavigation() {
    requestWithReturnCodeError(3003, "")
  }

  fun gotoSite(toSiteInstanceName: String): String {
    val id = IdHelper.oidStr()
    val req = mapOf("id" to toSiteInstanceName, "task_id" to id)
    val reqStr = JsonHelper.mapper.writeValueAsString(req)
    requestWithReturnCodeError(3051, reqStr)
    return id
  }

  fun cancelMoveTaskList(): String = requestWithReturnCodeError(3067, "")

  //
  // 19207
  //

  /**
   * 申请对 AGV 的控制权，返回是否成功
   */
  fun dominateAgv(dominatingName: String) {
    val req = mapOf("nick_name" to dominatingName)
    val reqStr = JsonHelper.mapper.writeValueAsString(req)

    requestWithReturnCodeError(4005, reqStr)
  }

  fun releaseAgv(): Boolean = try {
    requestWithReturnCodeError(4006, "")
    true
  } catch (e: Exception) {
    false
  }

  fun clearAllError() {
    requestWithReturnCodeError(4009, "")
  }

  fun postSmap(content: String) {
    requestWithReturnCodeError(4010, content)
  }

  /**
   * 读取一个 smap
   */
  fun fetchSmap(filename: String): String {
    val req = mapOf("map_name" to filename)
    val reqStr = JsonHelper.mapper.writeValueAsString(req)

    return request(4011, reqStr)
  }

  /***
   * 删除一个 smap
   */
  fun removeSmap(filename: String) {
    val req = mapOf("map_name" to filename)
    val reqStr = JsonHelper.mapper.writeValueAsString(req)

    requestWithReturnCodeError(4012, reqStr)
  }

  fun configReportChannel(interval: Int) {
    val req = mapOf("interval" to interval)
    val reqStr = JsonHelper.mapper.writeValueAsString(req)
    requestWithReturnCodeError(4091, reqStr)
  }

  /**
   * 更新机器人参数
   */
  fun updateParams(params: String) {
    requestWithReturnCodeError(4101, params)
  }

  /**
   * 推送机器人模型
   */
  fun postModel(model: String) {
    requestWithReturnCodeError(4200, model)
  }

  //
  // 19210
  //

  fun setDO(id: Int, status: Boolean) {
    val req = mapOf("id" to id, "status" to status)
    val reqStr = JsonHelper.mapper.writeValueAsString(req)

    requestWithReturnCodeError(6001, reqStr)
  }

  fun softEms(status: Boolean) {
    val req = mapOf("status" to status)
    val reqStr = JsonHelper.mapper.writeValueAsString(req)
    requestWithReturnCodeError(6004, reqStr)
  }

  fun setVirtualDI(id: Int, status: Boolean) {
    val req = mapOf("id" to id, "status" to status)
    val reqStr = JsonHelper.mapper.writeValueAsString(req)

    requestWithReturnCodeError(6020, reqStr)
  }

  fun unbindBoxByGoodsId(goodsId: String) {
    val req = mapOf("goods_id" to goodsId)
    val reqStr = JsonHelper.mapper.writeValueAsString(req)

    requestWithReturnCodeError(6801, reqStr)
  }

  /**
   * containerName 机器人身上的位置：1、2、3、…… 999
   */
  fun unbindBoxByContainerName(containerName: String) {
    val req = mapOf("container_name" to containerName)
    val reqStr = JsonHelper.mapper.writeValueAsString(req)

    requestWithReturnCodeError(6802, reqStr)
  }

  fun unbindAllBoxes() {
    requestWithReturnCodeError(6803, "")
  }

  /**
   * 绑定货物 id 到名为 containerName 的料箱
   */
  fun bindBox(goodsId: String, containerName: String, desc: String = "") {
    val req = mapOf("goods_id" to goodsId, "container_name" to containerName, "desc" to desc)
    val reqStr = JsonHelper.mapper.writeValueAsString(req)

    requestWithReturnCodeError(6804, reqStr)
  }

  fun fetchChassisMode(): String = fetchModel()
    .takeIf { it.isNotBlank() }
    ?.let { modelString ->
      runCatching {
        val types = JsonHelper.mapper.readTree(modelString)["deviceTypes"]
        val type =
          types?.firstOrNull { it["name"]?.asText() == "chassis" }?.get("devices")?.get(0)?.get("deviceParams")
        type?.firstOrNull { it["key"].asText() == "mode" }?.get("comboParam")?.get("childKey")?.asText() ?: ""
      }.getOrElse { throw BzError("errFetchChassisMode", it) }
    } ?: ""
}

@JsonInclude(JsonInclude.Include.NON_NULL)
data class Speed(
  val vx: Double? = null,
  val vy: Double? = null,
  val w: Double? = null,
  val steer: Int? = null,
  val realSteer: Double? = null,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class ReLoc(
  // bool	是否为自动重定位，当存在该字段且值为true，忽略以下所有字段
  val isAuto: Boolean? = false,

  // 世界坐标系中的 x 坐标, 单位 m
  val x: Double? = null,

  // 世界坐标系中的 y 坐标, 单位 m
  val y: Double? = null,

  // 世界坐标系中的角度, 单位 rad
  val angle: Double? = null,

  // 重定位区域半径，单位 m
  val length: Double? = null,

  // 在 RobotHome 重定位(若为 true, 前三个参数无效, 并从 Roboshop 参数配置中的 RobotHome1-5 重定位, 若 RobotHome1-5 未配置, 则不做任何操作。若缺省则认为是 false)
  val home: Boolean? = false,
)

data class Container(val containerName: String?, val goodsId: String?, val desc: String?, val hasGoods: Boolean = false)

data class RbkNavigation(
  // 0 = NONE, 1 = WAITING(目前不可能出现该状态), 2 = RUNNING, 3 = SUSPENDED, 4 = COMPLETED, 5 = FAILED, 6 = CANCELED
  val taskStatus: Int,
  // 0 = 没有导航, 1 = 自由导航到任意点, 2 = 自由导航到站点, 3 = 路径导航到站点, 7 = 平动转动, 100 = 其他
  val taskType: Int,
  // 导航要去的站点
  val targetId: String?,
  // 导航路径上已经经过的站点
  val finishedPath: List<String>?,
  // 导航路径上尚未经过的站点
  val unfinishedPath: List<String>?,
  // 导航任务附加信息
  val moveStatusInfo: String?,
  val containers: List<Container>?,
)