package com.seer.trick.robot.single.bp


import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockOutputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper

class RobotSingleTranslationBp : AbstractTaskChainBp() {

  override fun process() {
    val dist = mustGetBlockInputParam("dist") as Double
    val vx = getBlockInputParam("vx") as Double? ?: 0.5
    val vy = getBlockInputParam("vy") as Double? ?: 0.0
    val mode = getBlockInputParam("mode") as String? ?: "0"
    val reqId = getBlockInputParam("reqId") as String? ?: IdHelper.uuidStr()
    val req = JsonHelper.mapper.writeValueAsString(
      mapOf(
        "dist" to dist,
        "vx" to vx,
        "vy" to vy,
        "mode" to Integer.parseInt(mode)
      )
    )
    val gwRbkResult = executeTask(RbkRequest(reqId, 3055, req, "平动"))
    setBlockOutputParams(mapOf("rbkResult" to gwRbkResult))
  }

  companion object {

    val def = BlockDef(
      RobotSingleTranslationBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(
        BlockInputParamDef("dist", BlockParamType.Double, true),
        BlockInputParamDef("vx", BlockParamType.Double, false, defaultValue = 0.5),
        BlockInputParamDef("vy", BlockParamType.Double, false, defaultValue = 0.0),
//        BlockInputParamDef(
//          "mode", BlockParamType.String, options = listOf(
//            BlockInputParamOption("0"), BlockInputParamOption("1")
//          )
//        ),
//        BlockInputParamDef("reqId", BlockParamType.String),
      ),
      outputParams = listOf(
        BlockOutputParamDef("rbkResult", BlockParamType.String)
      )
    )

  }

}