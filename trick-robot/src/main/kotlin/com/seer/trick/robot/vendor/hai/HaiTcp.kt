package com.seer.trick.robot.vendor.hai

import com.seer.wcs.device.tcp.FixedHeadFrameSchema
import io.netty.buffer.ByteBuf
import io.netty.buffer.CompositeByteBuf
import io.netty.buffer.Unpooled
import java.nio.charset.StandardCharsets

object HaiTcp {

  val schema = FixedHeadFrameSchema(
    byteArrayOf(0xAD.toByte(), 0xDE.toByte(), 0xCE.toByte(), 0xFA.toByte()),
    4 * 7,
    { buf -> buf.getIntLE(4) },
    { head, body ->
      head.readerIndex(head.readerIndex() + 8) // 跳过前 8 个字节
      val channel = head.readIntLE() // 消息类型
      val msgSeq = head.readIntLE()
      val devSeq = head.readIntLE()
      // val crcBody = head.readIntLE()
      // val crcHead = head.readIntLE()
      // logger.info("crc, $crcHead, $crc1, $crc2, $crc3, $crc4")

      val bodyJsonStr = body.toString(StandardCharsets.UTF_8)
      // release !!!!
      head.release()
      body.release()
      HaiFrame(channel, msgSeq, devSeq, bodyJsonStr)
    }
  )

  fun buildFrame(
    channel: Int, // 报文通道
    robotId: Int, // 设备 ID
    seqNum: Int,
    bodyStr: String
  ): CompositeByteBuf {
    val bodyBytes = bodyStr.toByteArray(StandardCharsets.UTF_8)
    val crcBody = crcCcitt(bodyBytes)

    val headBuf: ByteBuf = Unpooled.buffer(schema.headLength)
    headBuf.writeBytes(schema.start)
    headBuf.writeIntLE(bodyBytes.size)
    headBuf.writeIntLE(channel)
    headBuf.writeIntLE(seqNum)
    headBuf.writeIntLE(robotId)
    headBuf.writeIntLE(crcBody)

    val headBytes1 = headBuf.array().sliceArray(0 until schema.headLength - 4)
    val crcHead = crcCcitt(headBytes1)

    // logger.debug("crc body: $crcBody, crc head: $crcHead")

    headBuf.writeIntLE(crcHead) // TODO .array() 返回内容对不对

    val reqBuf: CompositeByteBuf = Unpooled.compositeBuffer()
    reqBuf.addComponent(true, headBuf)
    reqBuf.addComponent(true, Unpooled.wrappedBuffer(bodyBytes))
    return reqBuf
  }

  private fun crcCcitt(bytes: ByteArray): Int {
    var crc = 0
    for (byte in bytes) {
      crc = crc xor (byte.toInt() shl 8)
      for (j in 0 until 8) {
        crc = if (crc and 0x8000 != 0) {
          (crc shl 1) xor 0x1021
        } else {
          crc shl 1
        }
        crc = crc and 0xffff // 强制去掉最高两位
      }
    }
    return crc
  }

}

//

data class HaiFrame(
  val channel: Int, // 消息类型
  val msgSeq: Int, // 流水号
  val devSeq: Int, // 设备编号
  val bodyJsonStr: String // body JSON
)

object RobotState {
  const val ROBOT_READY_TO_INIT = 0 // 机器人启动以后的初始状态，等待初始化指令
  const val ROBOT_IDLE = 1 // 空闲状态，等待任务指令(MOVE、BIN_OP)
  const val ROBOT_RUNNING = 2 // 运行状态(正在执行任务）
  const val ROBOT_ABNORMAL = 3 // 异常状态(内部故障，或者执行任务过程中发生异常需要处理)
  const val ROBOT_RECOVERY = 4 // 恢复状态
  const val ROBOT_PAUSED = 5 // 暂停状态
}

object MessageChannel {
  const val REPORT_INFO_REQ = 0 // 状态信息上报
  const val REPORT_INFO_ACK = 1
  const val INSTRUCTION_REQ = 2 // 第二节中的其他报文，如RCS2LL_INIT 、RCS2LL_MOVE 等都属于 INSTRUCTION_REQ 通道
  const val INSTRUCTION_ACK = 3
  const val INSTRUCTION_RET = 4
  const val REPORT_EVENT_REQ = 5 // 事件信息报告
  const val REPORT_EVENT_ACK = 6
  const val INSTRUCTION_CTRL = 7
}