package com.seer.trick.robot.gw

import com.seer.trick.robot.rachel.RobotVendor

class GwConfig(

  val enabled: Boolean = false,

  /**
   * 提供一个 Http 服务器
   */
  val httpServerConfig: HttpServerConfig = HttpServerConfig(),

  /**
   * 连接本地的机器人
   */
  val localConfigs: List<LocalConfig> = ArrayList(),

  /**
   * 启动单端口 RBK 协议服务器
   */
  val singlePortRbkServerConfigs: List<SinglePortRbkServerConfig> = ArrayList(),

  /**
   * 向远端遵循 RBK 协议的 TCP 服务器主动发起和保持连接
   */
  val remoteRbkConfigs: List<RemoteRbkConfig> = emptyList(),

  /**
   * 启动一个 WebSocket 服务
   */
  val webSocketServerConfig: WebSocketServerConfig = WebSocketServerConfig(),

  /**
   * 向远端的一个 WebSocket 服务器建立和保持连接
   */
  val webSocketClientConfigs: List<WebSocketClientConfig> = emptyList(),

  /**
   * 主动上报和请求任务模式
   */
  val reportApplyMode: ReportApplyMode = ReportApplyMode(),
)

class LocalConfig(
  val name: String = "",
  val disabled: Boolean = false,
  val vendor: RobotVendor = RobotVendor.Seer,
  val ip: String = "",
  val mock: Boolean = false
)

/**
 * 提供一个 Http 服务器
 */
class HttpServerConfig(
  val enabled: Boolean = false,
  /**
   * 提供 HTTP 服务器的端口，默认 7600
   */
  val port: Int = 7600,
  /**
   * 如果启用身份验证
   */
  val authId: String = "admin123",
  /**
   * 如果启用身份验证
   */
  val authSecret: String = "12345678"
)

/**
 * 向远端的一个遵循 RBK 协议的中继
 */
class RemoteRbkConfig(
  val disabled: Boolean = false,
  val ip: String = "",
  /**
   * 转发本地哪个机器人的通讯
   */
  val localRbkName: String = "",
  /**
   * 聚合成一个端口
   */
  val singlePort: Int? = null,
  /**
   * 还是按 RBK 本身多端口发，但只打开几个端口，默认打开全部
   */
  val enabledPorts: List<Int>? = null,
  /**
   * 如果启用身份验证
   */
  val authId: String? = null,
  /**
   * 如果启用身份验证
   */
  val authSecret: String? = null,
  /**
   * 断连后重新连接的时间间隔，单位毫秒，默认 10 秒
   */
  val connectRetryDelay: Long? = null
)

/**
 * 向远端的一个 WebSocket 服务器建立和保持连接
 */
class WebSocketClientConfig(
  val disabled: Boolean = false,
  val url: String = "",
  /**
   * 断连后重新连接的时间间隔，单位毫秒，默认 10 秒
   */
  val connectRetryDelay: Long? = null,
  /**
   * 如果启用身份验证
   */
  val authId: String? = null,
  /**
   * 如果启用身份验证
   */
  val authSecret: String? = null,
  /**
   * 启用 Gzip 压缩
   */
  val gzip: Boolean = false
)

/**
 * 启动一个 WebSocket 服务
 */
class WebSocketServerConfig(
  val enabled: Boolean = false,
  /**
   * 提供服务的端口，默认 7604
   */
  val port: Int = 7604,
  /**
   * 如果启用身份验证
   */
  val authId: String? = null,
  /**
   * 如果启用身份验证
   */
  val authSecret: String? = null,
  /**
   * 启用 Gzip 压缩
   */
  val gzip: Boolean = false
)

class SinglePortRbkServerConfig(
  val disabled: Boolean = false,
  val port: Int = 19200,
  val robotName: String = "",
)

class ReportApplyMode(
  val enabled: Boolean = false,
  val url: String = "http://127.0.0.1:5800/api/wcs/mr/robot/report-apply",
  val reportDelay: Long = 1000,
  val reportAlarm: Boolean = false,
)