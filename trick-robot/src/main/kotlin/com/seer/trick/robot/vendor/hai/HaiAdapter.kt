package com.seer.trick.robot.vendor.hai

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError

import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.soc.SocService
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.NumHelper
import com.seer.wcs.device.tcp.TcpServer
import io.netty.channel.ChannelHandlerContext
import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap

/**
 * 处理状态上报；
 * 未初始化的初始化；
 * 发送命令，记录当前命令；接收命令反馈
 */
object HaiAdapter {

  private val logger = LoggerFactory.getLogger(this::class.java)

  @Volatile
  private var server: TcpServer<HaiFrame>? = null

  val robots: MutableMap<String, HaiRobotRuntime> = ConcurrentHashMap()

  // 默认端口 4762
  fun init(port: Int = 4762) {
    val noSsl = BzConfigManager.getByPath("ScWcs", "hai", "haiNoSSL") == true
    server = TcpServer("海柔适配器", port, HaiTcp.schema, !noSsl, HaiAdapter::onServerMessage)

    val haiMockRobot = BzConfigManager.getByPath("ScWcs", "hai", "haiMockRobot")
    val mockConfig = NumHelper.anyToInt(haiMockRobot)
    if (mockConfig != null && mockConfig > 0) {
      MockHaiRobotService.init(mockConfig)
    }
  }

  fun dispose() {
    robots.clear()
    try {
      server?.dispose()
    } catch (e: Exception) {
      logger.error("销毁报错", e)
    }
  }

  private fun onServerMessage(ctx: ChannelHandlerContext, frame: HaiFrame) {
    // logger.debug("frame: $frame")
    when (frame.channel) {
      MessageChannel.REPORT_INFO_REQ -> onReport(ctx, frame)
      MessageChannel.INSTRUCTION_ACK -> onInstructionAck(ctx, frame)
      MessageChannel.INSTRUCTION_RET -> onInstructionRet(frame, ctx)
      else -> logger.warn("尚未处理的信道：${frame.channel},=${frame}")
    }
  }

  private fun onReport(ctx: ChannelHandlerContext, frame: HaiFrame) {
    // logger.debug("Robot report: ${frame.bodyJsonStr}")
    val rawReport: EntityValue = JsonHelper.mapper.readValue(frame.bodyJsonStr, jacksonTypeRef())
    val main: HaiRobotReportMain = JsonHelper.mapper.readValue(frame.bodyJsonStr, jacksonTypeRef())
    val report = HaiRobotReport(rawReport, main)
    // logger.debug("Robot report obj: $body")

    val r = synchronized(this) {
      var r = robots[main.robotId]
      if (r == null) {
        logger.info("海柔机器人注册 '${main.robotId}'")
        r = HaiRobotRuntime(main.robotId, ctx)
        robots[main.robotId] = r
      }
      r.report = report
      r.ctx = ctx
      r
    }

    r.reportCount = (r.reportCount + 1) % 10
    if (r.reportCount == 0) {
      val resFrame = HaiTcp.buildFrame(MessageChannel.REPORT_INFO_ACK, frame.devSeq, frame.msgSeq, "")
      ctx.writeAndFlush(resFrame)
    }

    SocService.updateNode("GW", "GW:HaiRobot:SelfReport::${main.robotId}", "海柔机器人自身报告", rawReport)

    if (main.robotState == RobotState.ROBOT_READY_TO_INIT) {
      // 初始化机器人
      initRobot(ctx, frame.devSeq)
    }
  }

  /**
   * 仅表示此指令收到，不表示完成
   * TODO init 的 ACK 也在这里收到，需要剔除
   */
  private fun onInstructionAck(@Suppress("UNUSED_PARAMETER") ctx: ChannelHandlerContext, frame: HaiFrame) {
    logger.debug("收到指令回复：$frame,当前命令:${frame.msgSeq}")
    val robot = robots[frame.devSeq.toString()]
    if (robot == null) {
      logger.error("收到指令回复，但机器人 '${frame.devSeq}' 不存在")
      return
    }

    synchronized(this) {
      val cmd = robot.currentCmd
      if (cmd == null) {
        logger.error("收到命令 '${frame.msgSeq}' 回复，但机器人 '${robot.id}' 当前没有命令")
      } else if (cmd.seqNum != frame.msgSeq) {
        logger.error("收到命令 '${frame.msgSeq}' 回复，但机器人 '${robot.id}' 当前命令不符 ${cmd.seqNum}")
      }
      robot.currentCmd = null
      robot.lastDoneCmdSeqNum = frame.msgSeq
    }
  }

  /**
   * 表示此指令完成
   */
  private fun onInstructionRet(frame: HaiFrame, ctx: ChannelHandlerContext) {
    val robot = robots[frame.devSeq.toString()]
    if (robot == null) {
      logger.error("收到指令回复，但机器人 '${frame.devSeq}' 不存在")
      return
    }
    val resFrame = HaiTcp.buildFrame(MessageChannel.INSTRUCTION_ACK, frame.devSeq, frame.msgSeq, "")
    ctx.writeAndFlush(resFrame)
    logger.debug("回复信道4:${frame.msgSeq}")
    robot.sendUnfinishedList.remove(frame.msgSeq.toString())
  }

  private fun initRobot(ctx: ChannelHandlerContext, robotId: Int) {
    logger.info("初始化海柔机器人：$robotId")
    val robot = robots[robotId.toString()] ?: throw BzError("errNoRobot", robotId)
    val msgSeq = robot.nextMsgSeq()
    val req = mapOf(
      "msgType" to 0, // MessageType::RCS2LL_INIT
      "seqNum" to msgSeq,
      "robotId" to robotId.toString()
    )
    val frame =
      HaiTcp.buildFrame(MessageChannel.INSTRUCTION_REQ, robotId, msgSeq, JsonHelper.writeValueAsString(req))
    ctx.writeAndFlush(frame)
  }

  /**
   * req 按海柔单车协议构造
   */
  fun move(robotId: String, req: EntityValue): Int {
    logger.debug("海柔机器人移动 '$robotId': $req")
    val robot = robots[robotId] ?: throw BzError("errNoRobot", robotId)
    if (robot.currentCmd != null) {
      logger.error("发送命令，但上一个命令位执行完 '${robot.currentCmd}'")
    }
    val msgSeq = robot.nextMsgSeq()
    logger.debug("下发 msgSeq=$msgSeq")
    req["seqNum"] = msgSeq
    req["robotId"] = robotId

    synchronized(this) {
      val cmd = MoveCmd(robotId, msgSeq, req)
      robot.currentCmd = cmd
    }

    val frame = HaiTcp.buildFrame(
      MessageChannel.INSTRUCTION_REQ, robotId.toInt(), msgSeq, JsonHelper.writeValueAsString(req)
    )
    robot.ctx.writeAndFlush(frame)

    robot.sendUnfinishedList.add(msgSeq.toString())
    return msgSeq
  }

}
