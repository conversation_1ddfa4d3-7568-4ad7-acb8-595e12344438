package com.seer.trick.robot.rachel.order


import com.seer.trick.robot.rachel.*
import org.slf4j.LoggerFactory

/**
 * 负责取消通用运单
 */
class MrOrderCancelService(private val rachel: RaRachelManager) {

  private val logger = LoggerFactory.getLogger(this::class.java)

  /**
   * 发起取消通用运单
   */
  fun cancelOrder(orderId: String) = rachel.withKeyLock {
    val or = rachel.orderService.mustGetOrderById(orderId)
    val order = or.order
    val status = order.status

    logger.info(
      "取消通用运单：$orderId，当前状态=$status，当前步=${order.currentStepIndex}，分配机器人=${order.actualRobotName}"
    )

    // todo order.fault == true 要不要特殊处理

    if (status == MrOrderStatus.Building || status == MrOrderStatus.ToBeAllocated) {
      // 直接标记已取消
      rachel.orderService.markOrderCancel(or)
    } else if (status == MrOrderStatus.Allocated || status == MrOrderStatus.Pending
      || status == MrOrderStatus.Withdrawn
    ) {
      cancelAllocatedOrPendingOrWithdrawnOrder(or)
    } else if (status == MrOrderStatus.Executing) {
      cancelExecutingOrder(or)
    }

    rachel.dispatchOrderService.dispatchOrders()
  }

  private fun cancelAllocatedOrPendingOrWithdrawnOrder(or: MrOrderRuntime) {
    rachel.orderService.markOrderCancel(or)

    val rr = rachel.robots[or.order.actualRobotName]
    if (rr == null) {
      logger.error("取消通用运单 '${or.orderId}'，关联机器人 '${or.order.actualRobotName}' 不存在")
    } else {
      // 删除机器人的运单列表
      rr.orders.remove(or.orderId)
      fixBins(rr, or)
      MrRepo.saveRobotAsync(rr)
    }
  }

  private fun cancelExecutingOrder(or: MrOrderRuntime) {
    or.order = or.order.copy(status = MrOrderStatus.Cancelling)
    MrRepo.updateOrderAsync(or.order)

    val rr = rachel.robots[or.order.actualRobotName]
    if (rr == null) {
      logger.error("取消通用运单 '${or.orderId}'，关联机器人 '${or.order.actualRobotName}' 不存在")
      return
    }
    if (rr.currentOrder?.orderId != or.orderId) {
      logger.error("撤销执行的运单 ${or.orderId}，但它不是机器人当前运单 ${rr.currentOrder?.orderId}")
      return
    }

    rachel.robotController.cancelCmd(rr)
  }

  fun onCmdOkWhenCancelling(rr: MrRobotRuntime, or: MrOrderRuntime, stepIndex: Int) {
    logger.info("运单步骤完成（OK）${or.orderId}:$stepIndex。但运单标记取消中，机器人=${rr.id}")

    rachel.orderService.markOrderCancel(or)

    fixRobotAfterCancel(rr, or)
  }

  fun onCmdCancelledWhenCancelling(rr: MrRobotRuntime, or: MrOrderRuntime, stepIndex: Int) {
    logger.info("运单步骤被取消，${or.orderId}:$stepIndex。运单标记取消中，机器人=${rr.id}")

    rachel.orderService.markOrderCancel(or)

    fixRobotAfterCancel(rr, or)
  }

  fun onCmdFailedWhenCancelling(rr: MrRobotRuntime, or: MrOrderRuntime, stepIndex: Int) {
    logger.info("运单步骤被失败，${or.orderId}:$stepIndex。运单标记取消中，机器人=${rr.id}")

    rachel.orderService.markOrderCancel(or)

    rr.cmdStatus = MrRobotCmdStatus.Failed
    MrRepo.saveRobotAsync(rr)
  }

  private fun fixRobotAfterCancel(rr: MrRobotRuntime, or: MrOrderRuntime) {
    val currentOrder = rr.currentOrder
    if (currentOrder != null && currentOrder.orderId != or.orderId) {
      logger.error(
        "取消后要修复机器人 ${rr.id} 状态，但取消的单子 ${or.orderId} " +
            "不是机器人当前运单 ${currentOrder.orderId}:${rr.currentStepIndex}"
      )
      return
    }

    rr.setIdle(logger, "取消后修复机器人状态（触发运单=${or.orderId}）")
    // 删除机器人的运单列表
    rr.orders.remove(or.orderId)

    fixBins(rr, or)

    MrRepo.saveRobotAsync(rr)
  }

  /**
   * 修正机器人储位
   */
  private fun fixBins(rr: MrRobotRuntime, or: MrOrderRuntime) {
    val bin = rr.bins.find { it.orderId == or.orderId }
    if (bin != null) {
      logger.info("清理机器人库位，机器人 ${rr.id} 库位 ${bin.index} 当前状态：${bin.status}")
      if (bin.status == MrRobotBinStatus.Reserved) {
        rr.bins[bin.index] = MrRobotBin(bin.index) // 清空
      } else if (bin.status == MrRobotBinStatus.Filled) {
        rr.bins[bin.index] = MrRobotBin(bin.index, status = MrRobotBinStatus.Cancelled, orderId = or.orderId) // 人工处理
      }
    } else if (or.order.kind == MrOrderKind.Parking) {
      // do nothing
    } else {
      logger.error("清理机器人库位，机器人 ${rr.id} 没有与通用运单 ${or.orderId} 绑定的库位")
    }
  }

}