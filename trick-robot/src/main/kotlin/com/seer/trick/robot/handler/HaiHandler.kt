package com.seer.trick.robot.handler


import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.noAuth
import com.seer.trick.base.http.getReqBody
import com.seer.trick.helper.JsonHelper
import com.seer.trick.robot.vendor.hai.HaiAdapter
import com.seer.trick.robot.vendor.hai.HaiRoute
import com.seer.trick.robot.vendor.hai.HaiStep
import com.seer.wcs.MrPosition
import io.javalin.http.Context

object HaiHandler {

  fun registerHandlers() {
    val c = Handlers("api/hai/")
    c.post("test", HaiHandler::test, noAuth())
    c.post("test-task", HaiHandler::testTask, noAuth())
  }

  private fun test(ctx: Context) {
    val req: MutableMap<String, Any?> = ctx.getReqBody()
    HaiAdapter.move(req["robotId"] as String, req)
    ctx.status(200)
  }

  private fun testTask(ctx: Context) {
    

    val req: HaiTestTsk = ctx.getReqBody()

    val moves = HaiRoute.fillMiddleLandmarks(req.robotId, req.from, req.steps)

    val order: EntityValue = mutableMapOf(
      "robotName" to req.robotId,
      "status" to "Created",
      "moves" to JsonHelper.mapper.writeValueAsString(moves)
    )
    EntityRwService.createOne("DirectRobotOrder", order)

    ctx.json(moves)
  }

}

data class HaiTestTsk(
  val robotId: String,
  val from: MrPosition,
  val steps: List<HaiStep>
)