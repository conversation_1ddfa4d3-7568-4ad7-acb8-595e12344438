package com.seer.trick.robot.rachel

import com.seer.trick.BzError

import com.seer.trick.base.failure.FailureLevel
import com.seer.trick.base.failure.FailureRecordReq
import com.seer.trick.base.failure.FailureRecorder
import com.seer.trick.base.soc.SocAttention
import com.seer.trick.base.soc.SocService
import com.seer.trick.robot.map.SceneMapManager
import com.seer.trick.robot.rachel.adapter.MrRobotAdapter
import com.seer.trick.robot.vendor.seer.SeerConnector
import org.slf4j.Logger
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.Future

/**
 * 机器人的运行时表示
 */
class MrRobotRuntime(val id: String) {

  @Volatile
  lateinit var systemConfig: MrSystemConfig
    private set

  /**
   * 与机器人通讯的底层适配器
   */
  @Volatile
  lateinit var adapter: MrRobotAdapter

  /**
   * 机器人自身上报信息
   */
  @Volatile
  var selfReport: MrRobotSelfReport? = null
    private set

  @Volatile
  var connector: SeerConnector? = null

  // --------------------------
  // 机器人的通用运单
  // --------------------------

  val orders: MutableMap<String, MrOrderRuntime> = ConcurrentHashMap()

  val bins: MutableList<MrRobotBin> = Collections.synchronizedList(ArrayList())

  @Volatile
  var cmdStatus: MrRobotCmdStatus = MrRobotCmdStatus.Idle

  @Volatile
  var idleFrom: Date = Date()

  @Volatile
  var currentOrder: MrOrderRuntime? = null
    private set

  @Volatile
  var currentStepIndex: Int? = null
    private set

  @Volatile
  var currentStepCost: Double? = null

  val selectExecutor: ExecutorService = Executors.newSingleThreadExecutor()
  val cmdExecutor: ExecutorService = Executors.newSingleThreadExecutor()

  var cmdFuture: Future<*>? = null

  @Volatile
  var cmdExecutorEnter: Boolean = false

  @Volatile
  var currentTomOrderId: String? = null

  private val timestampRegex = """\[\d{4}-\d{2}-\d{2}T[^]]*]""".toRegex()

  fun updateSystemConfig(config: MrSystemConfig) {
    this.systemConfig = config

    bins.clear()
    for (i in 1..config.selfBinNum) {
      bins.add(MrRobotBin(i - 1))
    }
  }

  fun mustGetCurrentOrder(): MrOrderRuntime = currentOrder ?: throw BzError("errMrNoCurrentOrder", id)

  fun mustGetCurrentStepIndex(): Int = currentStepIndex ?: throw BzError("errMrNoCurrentStep", id)

  /**
   * 当前有没有正在执行的步骤
   */
  fun getCurrentStep(): MrStep? {
    val index = currentStepIndex ?: return null
    return currentOrder?.steps?.get(index)
  }

  fun setIdle(logger: Logger, reason: String) {
    logger.info("将机器人 $id 命令状态改为 Idle，清除当前运单和步骤。原因：$reason")
    cmdStatus = MrRobotCmdStatus.Idle
    idleFrom = Date()
    currentOrder = null
    currentStepIndex = null
  }

  fun setMoving(logger: Logger, or: MrOrderRuntime, stepIndex: Int, cost: Double?) {
    logger.info(
      "将机器人 $id 命令状态改为 Moving，设置当前运单 ${or.orderId} (${or.order.status}) 和步骤 $stepIndex。成本=$cost",
    )
    cmdStatus = MrRobotCmdStatus.Moving
    currentOrder = or
    currentStepIndex = stepIndex
    currentStepCost = cost
  }

  fun updateSelfReport(report: MrRobotSelfReport?, scene: SceneMapManager) {
    if (systemConfig.disabled) return // 不接受更新
    diffReport(selfReport, report)
    selfReport = report
    SocService.updateNode(
      "机器人",
      "RobotSelfReport:$id",
      "机器人自身报告:$id",
      report,
      if (report == null || report.error) SocAttention.Red else SocAttention.None,
    )
  }

  private fun diffReport(selfReport: MrRobotSelfReport?, report: MrRobotSelfReport?) {
    
    // 只记录 fatal/error
    val oldAlerts = selfReport?.main?.alerts
      ?.filter { it.level == MrRobotAlertLevel.Error || it.level == MrRobotAlertLevel.Fatal }
    val newAlerts = report?.main?.alerts
      ?.filter { it.level == MrRobotAlertLevel.Error || it.level == MrRobotAlertLevel.Fatal }
    if (newAlerts != null) {
      if (oldAlerts == null) {
        recordFailures(newAlerts)
      } else {
        recordFailures(newAlerts.filter { oldAlerts.find { it2 -> it2.code == it.code } == null })
      }
    }
  }

  private fun recordFailures(alerts: List<MrRobotAlert>) {
    if (alerts.isEmpty()) return

    for (a in alerts) {
      val level = when (a.level) {
        MrRobotAlertLevel.Fatal -> FailureLevel.Fatal
        MrRobotAlertLevel.Error -> FailureLevel.Error
        MrRobotAlertLevel.Warning -> FailureLevel.Warning
        else -> continue
      }
      val req = FailureRecordReq(
        kind = "RobotAlarm",
        subKind = "RobotSelf",
        level = level,
        part = id,
        desc = "[${a.code}] ${a.message.replaceFirst(timestampRegex, "")}",
      )
      FailureRecorder.addAsync(req)
    }
  }

  override fun toString(): String = "$id ($cmdStatus)"
}