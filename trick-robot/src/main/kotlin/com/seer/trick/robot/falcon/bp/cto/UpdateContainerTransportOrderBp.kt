package com.seer.trick.robot.falcon.bp.cto

import com.seer.trick.Cq

import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.UpdateOptions
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*

class UpdateContainerTransportOrderBp : AbstractBp() {

  override fun process() {
    val orderId = mustGetBlockInputParam("orderId") as String
    val field = mustGetBlockInputParam("field") as String
    val fv = getBlockInputParam("fv")

    EntityRwService.updateOne(
      
      "ContainerTransportOrder",
      Cq.idEq(orderId),
      mutableMapOf(field to fv),
      UpdateOptions(muteExt = true),
    )
    // 添加猎鹰任务相关业务对象
    addRelatedObject("ContainerTransportOrder", orderId, null)
  }

  companion object {

    val def = BlockDef(
      UpdateContainerTransportOrderBp::class.simpleName!!,
      color = "#CEE6F3",
      inputParams = listOf(
        BlockInputParamDef("orderId", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.CtoId)),
        BlockInputParamDef(
          "field",
          BlockParamType.String,
          true,
          options = listOf(
            BlockInputParamOption("robotName", "机器人"),
            BlockInputParamOption("status", "状态"),
            BlockInputParamOption("fromBin", "起点库位"),
            BlockInputParamOption("toBin", "终点库位"),
            BlockInputParamOption("loaded", "已取货"),
            BlockInputParamOption("unloaded", "已放货"),
          ),
        ),
        BlockInputParamDef("fv", BlockParamType.Any, true),
      ),
    )
  }
}