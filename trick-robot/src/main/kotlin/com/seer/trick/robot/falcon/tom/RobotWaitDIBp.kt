package com.seer.trick.robot.falcon.tom



import com.seer.trick.falcon.FalconLogLevel
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*
import com.seer.trick.robot.tom.TomAgent


class RobotWaitDIBp : AbstractBp() {

  override fun process() {
    val tomId = mustGetBlockInputParam("tomId") as String
    val vehicle = mustGetBlockInputParam("vehicle") as String
    val id = mustGetBlockInputParamAsLong("id").toInt()
    val timeOut = getBlockInputParamAsLong("timeOut")
    val status = getBlockInputParamAsBool("status") as Boolean? ?: false
    val tomUrl = TomAgent.getTomUrlRoot(tomId)
    val result = TomAgent.waitRobotIO(tomUrl, vehicle, timeOut, id, status)
    log(FalconLogLevel.Info, "等待机器人$vehicle" + "的DI" + " " + "id:" + id + " " + "status:" + status)
    setBlockOutputParams(mapOf("$vehicle DI" to result))
  }

  companion object {

    val def = BlockDef(
      RobotWaitDIBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(
        BlockInputParamDef("tomId", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.RobotScene)),
        BlockInputParamDef("vehicle", BlockParamType.String, true),
        BlockInputParamDef("id", BlockParamType.Long, true),
        BlockInputParamDef("timeOut", BlockParamType.Long, false),
        BlockInputParamDef("status", BlockParamType.Boolean, false)
      ),
      outputParams = listOf(
        BlockOutputParamDef("DI", BlockParamType.String)
      )
    )

  }

}