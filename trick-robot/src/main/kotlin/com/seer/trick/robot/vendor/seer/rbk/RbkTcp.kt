package com.seer.trick.robot.vendor.seer.rbk

import com.seer.wcs.device.tcp.FixedHeadFrameSchema
import java.nio.charset.StandardCharsets

object RbkTcp {
  
  val schema = FixedHeadFrameSchema(
    byteArrayOf(0x5A.toByte(), 0x01.toByte()),
    16,
    { buf -> buf.getInt(4) },
    { head, body ->
      val flowNo = head.getShort(2).toInt()
      // val bodyLength = head.getInt(4)
      val apiNo = head.getShort(8).toInt()
      val bodyStr = body.toString(StandardCharsets.UTF_8)
      
      head.release()
      body.release()
      
      RbkFrame(flowNo, apiNo, bodyStr)
    }
  )
}