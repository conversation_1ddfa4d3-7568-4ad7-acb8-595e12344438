package com.seer.trick.robot.rachel

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.helper.NumHelper
import com.seer.trick.robot.map.SceneMapManager
import com.seer.trick.robot.vendor.seer.SeerHelper.rawToMainSeer

object RobotSelfReportMainHelper {
  
  /**
   * 将各品牌机器人的自报数据转换为 MrRobotSelfReportMain
   */
  @Suppress("UNCHECKED_CAST")
  fun rawToMain(vendor: RobotVendor, rawReport: EntityValue?, mm: SceneMapManager): MrRobotSelfReportMain? {
    if (rawReport == null) return null
    
    return when (vendor) {
      RobotVendor.Seer -> {
        rawToMainSeer(rawReport, null, mm)
      }
      
      RobotVendor.Hai -> {
        val bi = rawReport["batteryInfo"] as Map<String, Any?>?
        val charging = bi?.get("state") as Int? == 1
        val battery = (NumHelper.anyToInt(bi?.get("powerLevel")) ?: 0).toDouble() / 100.0
        
        val mapPosition = rawReport["mapPosition"] as Map<String, Any?>?
        val x = NumHelper.anyToDouble(mapPosition?.get("x"))
        val y = NumHelper.anyToDouble(mapPosition?.get("y"))
        val theta = NumHelper.anyToDouble(mapPosition?.get("theta"))
        
        // 海柔不支持多区域，默认使用第一个区域
        val area = mm.sceneMapRuntime.sceneMap.areas.firstOrNull()
        
        MrRobotSelfReportMain(
          battery = battery,
          x = x,
          y = y,
          direction = theta,
          charging = charging,
          currentAreaId = area?.id,
          currentAreaName = area?.name,
          currentMap = area?.mapName ?: area?.name,
        )
      }
      
      RobotVendor.Hik -> {
        val report = rawReport["report"] as EntityValue? ?: return null
        
        val x: Double = (NumHelper.anyToDouble(report["x"]) ?: -1000.0) / 1000
        val y = (NumHelper.anyToDouble(report["y"]) ?: -1000.0) / 1000
        val direction = (NumHelper.anyToDouble(report["direction"]) ?: -1000.0) / 1000
        val batteryLevel = (NumHelper.anyToDouble(report["batteryLevel"]) ?: -100.0) / 100
        val status = NumHelper.anyToInt(report["status"]) ?: -1
        
        // 海康不支持多区域，默认使用第一个区域
        val area = mm.sceneMapRuntime.sceneMap.areas.firstOrNull()
        
        MrRobotSelfReportMain(
          battery = batteryLevel,
          x = x,
          y = y,
          direction = direction,
          charging = status == 7, // status 7 是充电中
          currentAreaId = area?.id,
          currentAreaName = area?.name,
          currentMap = area?.mapName ?: area?.name,
        )
      }
    }
  }
  
}