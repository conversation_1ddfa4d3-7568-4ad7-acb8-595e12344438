package com.seer.trick.robot.rachel.dispatching


import com.seer.trick.base.concurrent.BaseConcurrentCenter.highTimeSensitiveExecutor
import com.seer.trick.helper.submitCatch
import com.seer.trick.robot.rachel.*
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.Future

/**
 * 派单管理。将运单分给机器人。
 * 触发时机：1、定时的分派；2、一些特定的事件触发立即分派（如有新单创建）
 */
class MrDispatchOrderService(private val rachel: RaRachelManager) {

  private val logger = LoggerFactory.getLogger(this::class.java)

  /**
   * 非阻塞，相当于发了一次请求。触发一次全局派单。提交给派单线程然后立即返回。不能报错。
   */
  fun dispatchOrders(): Future<*> = rachel.dispatchExecutor.submitCatch("派单", logger) {
    

    // 列出可派的所有运单
    val orders = rachel.orderService.orders.values
      .filter(::canOrderBeAllocatedOrAllocatedAgain)
      .filter { it.order.kind != MrOrderKind.Parking } // Parking 单不用分

    // 列出可派的所有机器人
    val robots = rachel.robots.values.filter(::canRobotAllocated)

    if (orders.isNotEmpty() && robots.isNotEmpty()) {
      // 计算全匹配，然后按成本从低到高排序
      val allocations = listSortedPreAllocations(orders, robots)
      if (allocations.isNotEmpty()) tryAllocate(allocations)
    }

    // 最后停靠
    rachel.parkingService.tryParking()

    // TODO 充电
  }

  // 能被分或二分的单子只能处于：ToBeAllocated、Allocated、Executing、Pending，且 Executing、Pending 要检查是否可打断
  private fun canOrderBeAllocatedOrAllocatedAgain(or: MrOrderRuntime): Boolean {
    val order = or.order
    val status = order.status

    // 如果运单被标记为故障、被撤回、取消中，一定不派
    if (order.fault || order.status == MrOrderStatus.Withdrawn || order.status == MrOrderStatus.Cancelling) return false

    // 运单已载货，不能被重派
    if (order.loaded) return false // 如果已载货，不能二分；即使后面卸了也不行

    // 未分派或已分派，一定可以尝试
    if (status == MrOrderStatus.ToBeAllocated || status == MrOrderStatus.Allocated) return true

    // 但如果已执行
    if (status == MrOrderStatus.Executing || status == MrOrderStatus.Pending) {
      if (order.kind == MrOrderKind.Parking) return true // 停靠
      if (order.currentStepIndex < 0) return true
      for (i in 0..order.currentStepIndex) {
        val step = or.steps[i]
        if (!step.interruptible) return false
      }
      return true
    }
    return false
  }

  // 初步评估机器人是否能接新单，返回 false 一定不能，返回 true 仍有可能不能
  fun canRobotAllocated(rr: MrRobotRuntime): Boolean {
    val selfReport = rr.selfReport
    val loadable = canRobotLoadMore(rr)
    return !rr.systemConfig.disabled
        && !rr.systemConfig.offDuty
        && (selfReport != null && !selfReport.error)
        && (rr.cmdStatus == MrRobotCmdStatus.Idle || rr.cmdStatus == MrRobotCmdStatus.Moving)
        && loadable
  }

  /**
   * 是否有再装的潜力
   * 简单实现：没有全满
   * TODO 复杂实现 都载货；虽然只是预定，但已处于不可被重新分派的状态
   */
  private fun canRobotLoadMore(rr: MrRobotRuntime): Boolean {
    return !rr.bins.all { it.status == MrRobotBinStatus.Filled }
  }

  private fun listSortedPreAllocations(
    orders: List<MrOrderRuntime>, robots: List<MrRobotRuntime>
  ): List<PreAllocation> {
    val allocations: MutableList<PreAllocation> = Collections.synchronizedList(ArrayList()) // TODO 实现，有序数组
    val futures: MutableList<Future<*>> = ArrayList()

    for (or in orders) {
      val order = or.order

      for (rr in robots) {
        if (!order.expectedRobotNames.isNullOrEmpty()
          && !order.expectedRobotNames.contains(rr.id)
        ) continue

        // TODO Groups
        // if (!order.expectedRobotGroups.isNullOrEmpty()
        //   && CollectionUtils.intersection(order.expectedRobotGroups, rr.config.groups).isEmpty()
        // ) continue

        // TODO 限制计算线程数
        futures += highTimeSensitiveExecutor.submit {
          // 异步执行，再测一次
          if (!canRobotAllocated(rr)) return@submit
          val fromLocation = rr.adapter.findBestStart(rachel.sceneRuntime.map, rr)
          val toLocation = order.keySites?.firstOrNull() // TODO 如果已经做完前面步骤，起始点已经改变
          val toLocationSiteId = rachel.sceneRuntime.map.sceneMapRuntime.getIndexBySiteIdOrBinId(toLocation!!)!!.site.id
          val cost = rr.adapter.calcMoveCost(rachel.sceneRuntime.map, rr, fromLocation, toLocationSiteId)
          if (cost != null && cost >= 0 && cost != Double.MAX_VALUE) {
            allocations += PreAllocation(
              robotName = rr.id,
              orderId = order.id,
              priority = order.priority,
              cost = cost,
              createdOn = order.createdOn.time
            )
          }
        }
      }
    }

    for (f in futures) {
      try {
        f.get()
      } catch (e: Exception) {
        logger.error("build allocation", e)
      }
    }
    // 先按优先级从大到校、再按成本从小到大、最后按实际从小到大排序
    return allocations.sortedWith(compareByDescending<PreAllocation> { it.priority }.thenBy { it.cost }
      .thenBy { it.createdOn })
  }

  /**
   * 里面要二次检查运单、机器人。两个原因：前面的计算是异步的；在分配过程中会改变运单和机器人的状态，比如打断它
   */
  private fun tryAllocate(allocations: List<PreAllocation>) = rachel.withKeyLock {
    val checkedOrders: MutableSet<String> = HashSet() // 不再检查运单集

    for (alloc in allocations) {
      if (checkedOrders.contains(alloc.orderId)) continue

      val or = rachel.orderService.orders[alloc.orderId] ?: continue
      val order = or.order
      // 再次检查运单状态
      if (!canOrderBeAllocatedOrAllocatedAgain(or)) continue

      val rr = rachel.robots[alloc.robotName] ?: continue
      // 再次检查机器人可接单性
      if (!canRobotAllocated(rr)) continue

      if (alloc.robotName == order.actualRobotName) {
        // 分配未改变
        checkedOrders += alloc.orderId
        continue
      } else {
        // 检查是否没有本质的改变
        val oldAlloc = allocations.find { it.orderId == order.id && it.robotName == order.actualRobotName }
        if (oldAlloc != null && oldAlloc.cost - alloc.cost < 10) {
          // TODO 阈值
          // 没有本质改变
          checkedOrders += alloc.orderId
          continue
        }
      }

      // ----- 以下，要改变分派了

      // 找不到目标库位，就继续；如果找到，可能是空库位或挤走别的任务
      val targetBin = rr.bins.find { it.status == MrRobotBinStatus.Empty }
        ?: findReservedWorstOrderBin(rr, alloc, allocations)
        ?: continue

      // 要被挤走的单子
      val or2 = if (!targetBin.orderId.isNullOrBlank())
        rachel.orderService.orders[targetBin.orderId]
      else if (rr.orders.isNotEmpty() && rr.orders.values.first().order.kind == MrOrderKind.Parking) {
        rr.orders.values.first()
      } else {
        null
      }

      // 只要有一个运单在 Executing 状态被撤回，本轮就不能分配
      var anyWithdrawExecuting = false

      // order 状态，只有四个可能 ToBeAllocated、Allocated、Executing、Pending
      val order1Msg = "准备给新机器人 ${alloc.robotName}，新成本=${alloc.cost}，老成本=${or.order.dispatchCost}"
      if (order.status == MrOrderStatus.Executing) {
        withdrawExecutingOrder(or, order1Msg)
        anyWithdrawExecuting = true
      } else if (order.status == MrOrderStatus.Pending || order.status == MrOrderStatus.Allocated) {
        withdrawAllocatedOrPendingOrder(or, order1Msg)
      } else {
        // 什么都不用做
      }

      checkedOrders += or.orderId

      if (or2 != null) {
        val order2 = or2.order

        // 前面 findReservedWorstOrderBin 确保 targetBin 关联的单子不能是 Withdrawn
        // targetBin 保证 order2 也是可以被二分的
        val order2Msg = "因为这个机器人要准备接新单 ${or.orderId}，目标库位=${targetBin.index}，" +
            "新单成本=${alloc.cost}，原单成本=${order2.dispatchCost}"
        if (order2.status == MrOrderStatus.Executing) {
          withdrawExecutingOrder(or2, order2Msg)
          anyWithdrawExecuting = true
        } else if (order2.status == MrOrderStatus.Pending || order2.status == MrOrderStatus.Allocated) {
          withdrawAllocatedOrPendingOrder(or2, order2Msg)
          if (order2.kind == MrOrderKind.Parking) {
            // 直接取消停靠
            rachel.orderService.markOrderCancel(or2)
          }
        } else {
          // 什么都不用做
        }

        checkedOrders += or2.orderId
      }

      if (!anyWithdrawExecuting) {
        allocateOne(or, rr, targetBin, alloc.cost)
      }
    }
  }

  /**
   * 找能被挤走的最差的运单；能被挤走的运单一定是未载货的
   * 前面要检查机器人不能处于故障、取消、打断等状态
   */
  private fun findReservedWorstOrderBin(
    rr: MrRobotRuntime, refAlloc: PreAllocation, allocations: List<PreAllocation>
  ): MrRobotBin? {
    var best: PreAllocation? = null
    var bestBin: MrRobotBin? = null
    for (bin in rr.bins) {
      if (bin.status != MrRobotBinStatus.Reserved) continue
      // 应该能在本轮的分配表中找到，如果找不到，说明机器人的这个运单也不能被二分
      val alloc = allocations.find { it.orderId == bin.orderId } ?: continue
      if (best != null && (best.priority > alloc.priority || best.cost < alloc.cost)) continue
      if (alloc.priority > refAlloc.priority) continue // 不能抢更高优先级的
      if (!(alloc.cost - refAlloc.cost > 3)) continue // 除非距离差大于阈值

      if (!bin.orderId.isNullOrBlank()) {
        val or = rachel.orderService.orders[bin.orderId]
        if (or == null) {
          logger.error(
            "findReservedWorstOrderBin 时，机器人 ${rr.id} 库位 ${bin.index} 关联单据 ${bin.orderId} 不在运单池里"
          )
          continue
        }
        if (or.order.status == MrOrderStatus.Withdrawn
          || or.order.status == MrOrderStatus.Cancelling || or.order.status == MrOrderStatus.Cancelled
        ) continue
      } else {
        logger.error("findReservedWorstOrderBin 时，机器人 ${rr.id} 库位 ${bin.index} 关联单据为空")
        continue
      }

      best = alloc
      bestBin = bin
    }
    return bestBin
  }

  /**
   * 外部加锁执行
   */
  private fun allocateOne(
    or: MrOrderRuntime, rr: MrRobotRuntime, targetBin: MrRobotBin, cost: Double
  ) {
    if (targetBin.status != MrRobotBinStatus.Empty) {
      logger.error( "分配通用运单 '${or.orderId} [${or.order.status}]' 给机器人 '${rr.id}'，" +
            "但目标库位 '${targetBin.index}' 不是空的 '${targetBin.status}'"
      )
      return
    }

    logger.info( "分配通用运单 '${or.orderId} [${or.order.status}]' 给机器人 '${rr.id}'，预留库位 '${targetBin.index}'"
    )

    // 不改机器人 cmdStatus，执行步骤时才改

    rr.orders[or.orderId] = or // 把运单给机器人的运单池
    rr.bins[targetBin.index] = MrRobotBin(targetBin.index, MrRobotBinStatus.Reserved, or.orderId)
    or.historyRobots += rr.id

    or.order = or.order.copy(
      status = MrOrderStatus.Allocated,
      actualRobotName = rr.id,
      robotAllocatedOn = Date(),
      dispatchCost = cost
    )

    MrRepo.saveRobotAsync(rr)
    MrRepo.updateOrderAsync(or.order)
  }

  // 注意起那么检查 or 是可以被二分的
  private fun withdrawAllocatedOrPendingOrder(or: MrOrderRuntime, reason: String) {
    logger.info("撤回 ${or.order.status} 运单 '${or.orderId}'，从当前机器人 '${or.order.actualRobotName}'，$reason")

    val rr = rachel.robots[or.order.actualRobotName]!!
    rr.orders.remove(or.orderId)
    // 如果运单与机器人关联，但机器人储位上没有这个运单，表示运单已卸货
    // 但不管是要被分还是被挤走的运单，应该都不会处于以卸货的状态
    val bin = rr.bins.find { it.orderId == or.orderId }
    if (bin != null) rr.bins[bin.index] = MrRobotBin(bin.index)

    or.order = or.order.copy(
      status = MrOrderStatus.ToBeAllocated,
      actualRobotName = null,
      robotAllocatedOn = null,
    )

    MrRepo.saveRobotAsync(rr)
    MrRepo.updateOrderAsync(or.order)
  }

  private fun withdrawExecutingOrder(or: MrOrderRuntime, reason: String) {
    logger.info(
      "撤回 Executing 运单 '${or.orderId}:${or.order.currentStepIndex}'，从当前机器人 '${or.order.actualRobotName}'，$reason"
    )

    or.order = or.order.copy(status = MrOrderStatus.Withdrawn)
    MrRepo.updateOrderAsync(or.order)

    val rr = rachel.robots[or.order.actualRobotName]!!
    rachel.robotController.cancelCmd(rr)
  }

}