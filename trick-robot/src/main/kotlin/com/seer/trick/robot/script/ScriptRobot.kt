package com.seer.trick.robot.script

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.Cq

import com.seer.trick.base.MapToAnyNull
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.script.ScriptRobustExecutor
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.JsonHelper.writeValueAsString
import com.seer.trick.robot.RobotAppManager
import com.seer.trick.robot.rachel.MrRobotInfoAll
import com.seer.trick.robot.tom.TomRobotRecord
import com.seer.trick.robot.vendor.hai.HaiRoute
import com.seer.trick.robot.vendor.hai.HaiStep
import org.slf4j.LoggerFactory

object ScriptRobot {

  private val logger = LoggerFactory.getLogger(this::class.java)

  fun isRobotOnline(robotName: String): Boolean {
    val sr = RobotAppManager.mustGetFirstScene()
    return sr.rachel!!.isOnline(robotName)
  }

  // fun mustGetBinRobotArgs(bin: String, action: String): BinRobotArgs {
  //   return MrBinRobotArgsManager.mustGetBinRobotArgs(bin, action)
  // }
  //
  // fun mustGet3051Params(bin: String, action: String): Map<String, Any?> {
  //   val args = MrBinRobotArgsManager.mustGetBinRobotArgs(bin, action)
  //   val params: MutableMap<String, Any?> = HashMap(args.rbkFields)
  //   params["source_id"] = "SELF_POSITION"
  //   params["id"] = args.site
  //   return params
  // }

  /**
   * 未禁用、可接单、在线的机器人
   */
  fun listWorkableRobots(): List<MrRobotInfoAll> {
    val sr = RobotAppManager.mustGetFirstRachelScene()
    val rachel = sr.rachel!!

    
    val all: MutableList<MrRobotInfoAll> = mutableListOf()
    for (rr in rachel.robots.values) {
      if (rr.systemConfig.disabled || rr.systemConfig.offDuty || !rachel.isOnline(rr.id)) continue
      val ra = rachel.getRobotInfoAll(rr)
      all += ra
    }
    return all
  }

  /**
   * 未禁用、可接单、在线、空闲（扩展任务状态）的机器人
   */
  fun listExtIdleRobots(): List<MrRobotInfoAll> {
    val sr = RobotAppManager.mustGetFirstRachelScene()
    val rachel = sr.rachel!!
    
    val all: MutableList<MrRobotInfoAll> = mutableListOf()
    for (rr in rachel.robots.values) {
      if (rr.systemConfig.disabled || rr.systemConfig.offDuty || !rachel.isOnline(rr.id)) continue
      val ra = rachel.getRobotInfoAll(rr)
      if (ra.runtimeRecord["extTaskStatus"] != "Idle") continue
      all += ra
    }
    return all
  }

  fun listIdleTomRobots(): List<TomRobotRecord> {
    val sr = RobotAppManager.mustGetFirstTomScene()
    val tom = sr.tom!!
    return tom.listTomIdleRobots()
  }

  fun getCost(startSiteId: String, endSiteId: String): Double? {
    val sr = RobotAppManager.mustGetFirstScene()
    val startLocation = sr.map.sceneMapRuntime.getIndexBySiteIdOrBinId(startSiteId)!!.site.id
    val endLocation = sr.map.sceneMapRuntime.getIndexBySiteIdOrBinId(endSiteId)!!.site.id
    return sr.map.calcCost(startLocation, endLocation)
  }

  fun getTomRobotLocationByName(name: String?): String? {
    val sr = RobotAppManager.mustGetFirstTomScene()
    val tom = sr.tom!!
    val d = tom.digest()
    val tomRobotRecord = d.status?.robots?.first { it.id == name }
    return tomRobotRecord?.mainReport?.currentSite!!
  }

  /**
   * 未禁用、可接单、在线、空闲的机器人
   */
  fun listIdleRobots(): List<MrRobotInfoAll> {
    val sr = RobotAppManager.mustGetFirstRachelScene()
    val rachel = sr.rachel!!

    return rachel.listIdleRobots()
  }

  fun mustGetRobotInfoAll(id: String): MrRobotInfoAll {
    val sr = RobotAppManager.mustGetFirstRachelScene()
    val rachel = sr.rachel!!

    return rachel.mustGetRobotInfoAll(id)
  }

  fun findClosestRobotConnectedPoint(robotName: String): EntityValue? {
    val sr = RobotAppManager.mustGetFirstRachelScene()
    val rachel = sr.rachel!!

    return rachel.findClosestRobotConnectedPoint(robotName)
  }

  fun buildHaiMoves(robotId: String, rawSteps: String): List<EntityValue> {
    val steps: List<HaiStep> = JsonHelper.mapper.readValue(rawSteps, jacksonTypeRef())
    return HaiRoute.buildMoves(robotId, steps)
  }

  fun buildSeerMoves(robotId: String, rawSteps: String): List<EntityValue> {
    val sr = RobotAppManager.mustGetFirstRachelScene()
    val rachel = sr.rachel!!

    val steps: List<EntityValue> = JsonHelper.mapper.readValue(rawSteps, jacksonTypeRef())
    return rachel.lightScheduler.buildMoves(robotId, steps)
  }

  /**
   * 采用 RunOnce 的方式，创建直接运单，并等待其完成
   */
  fun awaitRunOnceDirectRobotOrder(
    
    robotName: String,
    mainId: String,
    action: String,
    moves: List<MapToAnyNull>,
  ): String {
    logger.info(
      "采用 RunOnce 的方式，创建直接运单，并等待其完成。机器人=$robotName, mainId=$mainId, action=$action, moves=$moves",
    )
    val r = ScriptRobustExecutor.runOnce(mainId, action) {
      val orderId = createDirectRobotOrder(robotName, mainId, action, moves)
      mutableMapOf("orderId" to orderId)
    }
    val orderId = r["orderId"] as String
    awaitDirectRobotOrder(robotName, orderId)
    return orderId
  }

  private fun createDirectRobotOrder(
    
    robotName: String,
    taskId: String,
    description: String,
    moves: List<MapToAnyNull>,
  ): String {
    val movesStr = JsonHelper.mapper.writeValueAsString(moves)
    val ev: EntityValue = mutableMapOf(
      "taskId" to taskId, // 运单所属的任务编号
      "robotName" to robotName,
      "status" to "Created",
      "moves" to movesStr,
      "description" to description,
    )
    val orderId = EntityRwService.createOne("DirectRobotOrder", ev)

    logger.info("创建直接运单完成，'$orderId'，机器人=$robotName")

    // TODO 要不要
    //   soc.updateStringNode(`Robot::CurrentOrder::${robotName}`, `机器人::当前运单::${robotName}`,
    //     `${orderId}:${description}`, "None")

    return orderId
  }

  private fun awaitDirectRobotOrder(robotName: String, orderId: String) {
    try {
      // 等待运单完成
      while (true) {
        val order = EntityRwService.findOneById("DirectRobotOrder", orderId)
          ?: throw BzError("errDirectRobotOrderNonexistent", orderId)
        val status = order["status"] as String
        if (status == "Done") {
          logger.info("直接运单，完成，'$orderId'，机器人=$robotName")
          break
        } else if (status == "Failed") {
          logger.info("直接运单，失败，'$orderId'，机器人=$robotName")
          throw BzError("errDirectRobotOrderFailed", orderId)
        }
        Thread.sleep(500)
      }
    } finally {
      // soc.removeNode(`Robot::CurrentOrder::${robotName}`)
    }
  }

  fun unlockBySiteIds(robotId: String, siteIds: List<String>) {
    val sr = RobotAppManager.mustGetFirstScene()
    val rachel = sr.rachel!!
    rachel.scheduleService.unlockBySiteIds(robotId, siteIds)
  }

  fun tryLockOneSiteByName(
    @Suppress("UNUSED_PARAMETER") 
    robotId: String,
    siteIds: List<String>,
  ): String? {
    val sr = RobotAppManager.mustGetFirstScene()
    val rachel = sr.rachel!!

    return rachel.scheduleService.tryLockOneSiteBySiteId(robotId, siteIds)
  }

  /**
   * 重置所有扩展机器人，终止所有后台任务，清楚所有地图资源锁
   */
  fun resetExtRobots() {
    logger.info("重置所有扩展机器人")

    val sr = RobotAppManager.mustGetFirstScene()
    val rachel = sr.rachel!!

    // 先令所有机器人停止接单
    // val robotConfigs = EntityRwService.findMany("MrRobotSystemConfig", Cq.all())
    val ids = rachel.robots.keys.toList()
    rachel.updateOffDuty(ids, true)

    // 放弃后台任务
    val evList = EntityRwService.findMany("RobustScriptExecutor", Cq.all())
    logger.info("未完成后台任务数：${evList.size}")
    for (ev in evList) {
      ScriptRobustExecutor.abort(EntityHelper.mustGetId(ev))
    }

    // 重启脚本
    // ScriptCenter.reload()
    // logger.info("resetExtRobots 脚本已重启")

    // 机器人状态空闲
    EntityRwService.updateMany(
      
      "MrRobotRuntimeRecord",
      Cq.all(),
      mutableMapOf("extTaskStatus" to "Idle", "ctOrders" to null, "taskStatus" to "Idle"),
    )

    // 释放资源
    rachel.scheduleService.unlockAll()

    // 释放申请
    rachel.lightScheduler.reset()

    // 恢复之前的接单设置
    //    for (rcEv in robotConfigs) {
    //      EntityRwService.updateOne(
    //        "MrRobotSystemConfig", Cq.idEq(EntityHelper.mustGetId(rcEv)), mutableMapOf("offDuty" to rcEv["offDuty"])
    //      )
    //    }
  }

  /**
   * 仅用于单车应用，直接给 RBK 下发指令。指令格式和指令内容请参考 RBK API 。
   * @param tc
   * @param reqMap 详见 SingleRobotReq 中相关字段的说明
   * @return 字符串形式的的响应内容，例如：{"create_on":"2024-11-12T02:59:08.139+0000","ret_code":0}
   */
  fun requestSingleRobotDirectly(reqMap: MapToAnyNull): String {
    logger.debug("request single robot directly, request=$reqMap")
    val req: SingleRobotReq = JsonHelper.mapper.convertValue(reqMap, jacksonTypeRef())
    val singleScene = RobotAppManager.mustGetRmSingle(req.sceneName)
    val client = singleScene.rbkClient
    val apiNo = req.apiNo
    val reqObj = req.reqObj
    req.timeout
    return if (!req.checkRetCode) {
      client.request(apiNo, writeValueAsString(reqObj))
    } else {
      client.requestWithReturnCodeError(apiNo, writeValueAsString(reqObj))
    }
  }
}

data class SingleRobotReq(
  val sceneName: String, // 单车应用场景名称，例如 "Single"
  val apiNo: Int, // RBK API 的指令编号，例如 3051
  val reqObj: MapToAnyNull, // RBK API 的指令内容，例如 {"id": "LM1", "task_id": "20241112-0001"}
  val remark: String = "", // 请求的备注信息，例如 "测试请求"
  val checkRetCode: Boolean = true, // 默认值为 true，即校验 API 响应内容中的 ret_code 字段, 如果 ret_code 不为 0，则报错。
  val timeout: Long = 10 * 1000L, // 请求的超时时间，单位 ms。
)