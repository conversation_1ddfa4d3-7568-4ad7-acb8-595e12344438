package com.seer.trick.robot.single.bp


import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockInputParamOption
import com.seer.trick.falcon.domain.BlockOutputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper

class RobotSingleSetDOBp : AbstractTaskChainBp() {
  override fun process() {
    val id = mustGetBlockInputParam("id") as Long
    val status = mustGetBlockInputParam("status") as String
    val req = JsonHelper.mapper.writeValueAsString(
      mapOf(
        "operation" to "SetDO",
        "args" to mapOf(
          "DO" to listOf(
            mapOf(
              "id" to id,
              "status" to (status == "status_1")
            )
          )
        )
      )
    )
    val gwRbkResult = executeTask(RbkRequest(IdHelper.uuidStr(), 3051, req, "设置 DO"))
    setBlockOutputParams(mapOf("rbkResult" to gwRbkResult))
  }

  companion object {

    val def = BlockDef(
      RobotSingleSetDOBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(
        BlockInputParamDef("id", BlockParamType.Long, true),
        BlockInputParamDef(
          "status",
          BlockParamType.String,
          true,
          options = listOf(
            BlockInputParamOption("status_0"),
            BlockInputParamOption("status_1")
          ),
          defaultValue = "status_1")
      ),
      outputParams = listOf(
        BlockOutputParamDef("rbkResult", BlockParamType.String)
      )
    )

  }
}