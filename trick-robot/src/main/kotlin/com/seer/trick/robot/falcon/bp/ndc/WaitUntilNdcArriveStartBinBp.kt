package com.seer.trick.robot.falcon.bp.ndc

import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.FatalError

import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.robot.vendor.ndc.NdcOrderState

class WaitUntilNdcArriveStartBinBp : AbstractBp() {
  override fun process() {
    val orderId = mustGetBlockInputParam("orderId") as String

    while (true) {
      val order = EntityRwService.findOne("NdcOrder", Cq.eq("id", orderId), null)
        ?: throw BzError("errCodeErr", "等待的 NDC 运单不存在")

      val status = order["status"] as Int? ?: 0
      if (status == NdcOrderState.Deleted) {
        logger.error("NDC 机器人任务，运单被取消，状态：$status")
        throw FatalError("errNdcCanceled", "等待的 NDC 运单被取消")
      } else if (status == NdcOrderState.Abort) {
        logger.error("NDC 机器人任务，运单被终止，状态：$status")
        throw FatalError("errNdcAbort", "等待的 NDC 运单被终止")
      } else if (status >= NdcOrderState.ReadyToLoad) {
        logger.debug("机器人已到达取货位，等待 GW 允许取货")
        break
      }

      Thread.sleep(1688)
    }
  }

  companion object {

    val def = BlockDef(
      WaitUntilNdcArriveStartBinBp::class.simpleName!!,
      color = "#F2EE9D",
      inputParams = listOf(
        BlockInputParamDef("orderId", BlockParamType.String, true)
      )
    )
  }

}