package com.seer.trick.robot.single

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.soc.SocAttention
import com.seer.trick.base.soc.SocService
import com.seer.trick.helper.JsonHelper
import com.seer.trick.robot.RobotStatsManager.transferSingleToRobotStats
import com.seer.trick.robot.rachel.MrRobotSelfReport
import com.seer.trick.robot.rachel.RobotSelfReportMainHelper.rawToMain
import com.seer.trick.robot.rachel.RobotVendor
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors
import java.util.concurrent.Future

/**
 * 单车上报状态管理。
 * 轮训，读取机器人状态。
 */
class RaSingleState(private val parent: RaSingleManager) {

  private val stateExecutor = Executors.newSingleThreadExecutor()

  @Volatile
  private var stateWorker: Future<*>? = null

  @Volatile
  private var stateFetching = false

  @Volatile
  var report: MrRobotSelfReport? = null
    private set

  @Volatile
  var controlledByMe: Boolean? = false // 我们有控制权
    private set

  fun init() {
    stateWorker = stateExecutor.submit { fetchStateLoop() }
  }

  fun dispose() {
    stateWorker?.cancel(true)
    stateWorker = null
    SocService.removeNode("RmSingle:State")
  }

  private fun fetchStateLoop() {
    
    while (!Thread.interrupted()) {
      try {
        fetchState()
        Thread.sleep(500)
      } catch (e: InterruptedException) {
        return
      }
    }
  }

  // 不要激光数据
  private val fetchReqBody = JsonHelper.mapper.writeValueAsString(mapOf("return_laser" to false))

  private fun fetchState() {
    if (stateFetching) return
    stateFetching = true

    updateSocState("获取状态...")

    val selfReport = try {
      val resStr = parent.rbkClient.fetch1100(fetchReqBody)
      val ev: EntityValue = if (resStr.isBlank()) {
        HashMap(0)
      } else {
        JsonHelper.mapper.readValue(resStr, jacksonTypeRef())
      }
      MrRobotSelfReport(
        false,
        null,
        main = rawToMain(RobotVendor.Seer, ev, parent.sceneRuntime.map),
        rawReport = ev,
      )
    } catch (e: Throwable) {
      if (e is InterruptedException) throw e
      MrRobotSelfReport(true, e.message)
    } finally {
      stateFetching = false
    }

    report = selfReport
    // 异步更新离线机器人的统计信息，parent.reset() 带有耗时，所以更新状态提到前面
    transferSingleToRobotStats(selfReport)
    if (selfReport.error) {
      controlledByMe = false
      updateSocState("获取状态失败：" + selfReport.errorMsg, SocAttention.Red)
      parent.reset()

      parent.sceneRuntime.updateRobotAlarms(listOf(RaSingleManager.DEFAULT_NAME), emptyMap())
    } else {
      // 控制权
      controlledByMe = report?.main?.currentLockNickName == parent.getNickname()

      updateSocState("获取状态成功")

      parent.sceneRuntime.updateRobotAlarms(emptyList(), mapOf(RaSingleManager.DEFAULT_NAME to selfReport.main))

      // 更新类型
      report?.rawReport?.let {
        val md5 = it["model_md5"] as String
        if (parent.robotModelMap[md5] == null) {
          parent.robotModelMap.clear()
          val model: MutableMap<String, String> = ConcurrentHashMap()
          val modelString = parent.rbkClient.fetchModel()
          val modelNode = JsonHelper.mapper.readTree(modelString)
          val type = modelNode["deviceTypes"]?.firstOrNull { item -> item["name"]?.asText() == "chassis" }
            ?.get("devices")?.get(0)?.get("deviceParams")
          val chassisMode = type?.firstOrNull { item -> item["key"].asText() == "mode" }
            ?.get("comboParam")?.get("childKey")?.asText() ?: ""
          val modelName = modelNode?.get("model")?.asText() ?: ""
          val motorList = modelNode["deviceTypes"]?.firstOrNull { item -> item["name"]?.asText() == "motor" }
            ?.get("devices")
          model["motorListStr"] = JsonHelper.mapper.writeValueAsString(motorList)
          model["chassisMode"] = chassisMode
          model["modelName"] = modelName
          parent.robotModelMap[md5] = model
        }
      }
    }
  }

  private fun updateSocState(desc: String, attention: SocAttention = SocAttention.None) {
    SocService.updateNode("机器人", "RmSingle:State", "单车状态", desc, attention)
  }
}