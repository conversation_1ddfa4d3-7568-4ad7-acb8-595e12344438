package com.seer.trick.robot.vendor.seer.rbk

import io.netty.channel.ChannelHandler.Sharable
import io.netty.channel.ChannelHandlerContext
import io.netty.channel.SimpleChannelInboundHandler
import java.nio.channels.ClosedChannelException

@Sharable
class RbkInboundHandler(
  private val onMessage: (ctx: ChannelHandlerContext, msg: RbkFrame) -> Unit,
  private val onError: (e: Throwable) -> Unit
) : SimpleChannelInboundHandler<RbkFrame>() {

  override fun channelRead0(channelHandlerContext: ChannelHandlerContext, msg: RbkFrame) {
    onMessage(channelHandlerContext, msg)
  }

  override fun exceptionCaught(ctx: ChannelHandlerContext, cause: Throwable) {
    try {
      ctx.close()
    } catch (e: Exception) {
      //
    }
    onError(cause)
  }
  override fun channelInactive(ctx: ChannelHandlerContext) {
    onError(ClosedChannelException())
  }
}