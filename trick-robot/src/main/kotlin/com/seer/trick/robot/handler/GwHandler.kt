package com.seer.trick.robot.handler

import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.getReqBody

import com.seer.trick.robot.gw.GwCenter
import com.seer.trick.robot.gw.GwConfig
import com.seer.trick.robot.sto.CreateStoReq
import com.seer.trick.robot.sto.StoManager
import com.seer.trick.robot.sto.StoStore
import io.javalin.http.Context

object GwHandler {
  
  /**
   * 网关配置和简单运单接口
   */
  fun registerHandlers() {
    // TODO URL
    val c = Handlers("api/wcs/gw")
    c.get("config", ::handleGetConfig, auth())
    c.post("config", ::handleSaveConfig, auth())

    c.post("sto", ::handleCreateSimpleTransportOrder, auth())
    c.get("sto/{id}", ::handleQuerySimpleTransportOrder, auth())
    c.post("sto/cancel", ::handleCancelSimpleTransportOrder, auth())
    c.post("sto/retry", ::handleRetrySimpleTransportOrder, auth())
  }

  private fun handleGetConfig(ctx: Context) {
    ctx.json(GwCenter.gwConfig)
  }

  private fun handleSaveConfig(ctx: Context) {
    val req: GwConfig = ctx.getReqBody()
    GwCenter.saveConfig(req)

    ctx.status(200)
  }

  private fun handleCreateSimpleTransportOrder(ctx: Context) {
    val order: CreateStoReq = ctx.getReqBody()
    StoManager.start(order)

    ctx.status(200)
  }

  private fun handleQuerySimpleTransportOrder(ctx: Context) {
    val orderId = ctx.pathParam("id")
    val op = StoStore.loadOrder(orderId)
    if (op != null) {
      ctx.json(mapOf("found" to true, "order" to op))
    } else {
      ctx.json(mapOf("found" to false, "order" to null))
    }
  }

  private fun handleCancelSimpleTransportOrder(ctx: Context) {
    val req: CancelSimpleTransportOrderReq = ctx.getReqBody()
    StoManager.cancelCurrentOrder(req.robotName)
    ctx.status(200)
  }

  private fun handleRetrySimpleTransportOrder(ctx: Context) {
    val req: RetrySimpleTransportOrderReq = ctx.getReqBody()
    StoManager.retryFailed(req.robotName)
    ctx.status(200)
  }

}

data class CancelSimpleTransportOrderReq(
  val robotName: String
)

data class RetrySimpleTransportOrderReq(
  val robotName: String
)
