package com.seer.trick.robot

import com.seer.trick.BzError

import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.log.SystemKeyEvent
import com.seer.trick.base.log.SystemKeyEventService
import com.seer.trick.helper.JsonFileHelper
import com.seer.trick.robot.single.RaSingleManager
import com.seer.trick.robot.sto.StoManager
import org.slf4j.LoggerFactory
import java.io.File
import java.util.concurrent.ConcurrentHashMap

/**
 * 机器人应用管理，总管理
 */
object RobotAppManager {
  
  private val logger = LoggerFactory.getLogger(javaClass)
  
  @Volatile
  var config: RobotAppConfig = RobotAppConfig()
    private set
  
  // 分场景
  val scenes: MutableMap<String, RobotAppSceneRuntime> = ConcurrentHashMap()
  
  fun init() {
    logger.info("Init robot application manager")
    loadConfig()
    logger.info("Scenes: ${config.scenes.size}")
    
    scenes.clear()
    for (sc in config.scenes) {
      if (sc.disabled) continue
      
      val sr = RobotAppSceneRuntime(sc)
      try {
        sr.init()
      } catch (e: Exception) {
        logger.error("Init scene ${sc.name}", e)
        continue
      }
      scenes[sc.name] = sr
    }
    logger.info("Done to init scenes")
    
    patchTomConfigs()
    
    val sceneOrNull = getEnabledSingleSceneOrNull()
    if (sceneOrNull == null) {
      StoManager.removeRobot(RaSingleManager.DEFAULT_NAME)
    } else {
      StoManager.init()
    }
  }
  
  fun dispose() {
    logger.info("Dispose robot application manager")
    val scenes = scenes.values.toList()
    this.scenes.clear()
    for (sr in scenes) {
      try {
        sr.dispose()
      } catch (e: Exception) {
        logger.error("Dispose robot application manager ${sr.config.name}", e)
      }
    }
  }
  
  private fun loadConfig() {
    val config: RobotAppConfig? = JsonFileHelper.readJsonFromFile(getConfigFile())
    if (config != null) this.config = config
  }
  
  fun saveConfig(config: RobotAppConfig) {
    logger.info("修改机器人应用配置：$config")
    val singleScenes = config.scenes.filter { it.mode === RobotAppMode.Single && !it.disabled }
    if (singleScenes.size > 1) throw BzError("errOnlyOneSingleAppCanBeEnabled")
    
    SystemKeyEventService.record(SystemKeyEvent(group = "Robot", title = "修改机器人应用配置"))
    removeMap(config)
    this.config = config
    JsonFileHelper.writeJsonToFile(getConfigFile(), config, true)
    
    // patchTomConfigs()
    dispose()
    init()
  }
  
  private fun removeMap(config: RobotAppConfig) {
    val oldMaps = this.config.scenes.map { it.name }
    val newMaps = config.scenes.map { it.name }
    for (m in oldMaps) {
      if (!newMaps.contains(m)) {
        val mapFile = File(File(BaseCenter.baseConfig.configDir, "map"), "$m.scene.json")
        if (mapFile.exists()) {
          mapFile.delete()
          SystemKeyEventService.record(SystemKeyEvent(group = "Robot", title = "删除场景"))
        }
      }
    }
  }
  
  // 兼容早期 Tom 配置
  @Suppress("UNCHECKED_CAST")
  private fun patchTomConfigs() {
    val wcs: EntityValue = BaseCenter.bzConfig.getOrPut("ScWcs") { HashMap<String, Any?>() } as EntityValue
    val seer = wcs.getOrPut("seer") { HashMap<String, Any?>() } as EntityValue
    val toms: MutableList<EntityValue> = ArrayList()
    for (scene in config.scenes) {
      if (scene.mode != RobotAppMode.Tom) continue
      toms.add(mutableMapOf("id" to scene.name, "url" to scene.tom.coreUrl))
    }
    seer["toms"] = toms
  }
  
  private fun getConfigFile(): File = File(BaseCenter.baseConfig.configDir, "robot-config.json")
  
  /**
   * 根据场景名获取场景
   */
  fun mustGetScene(name: String): RobotAppSceneRuntime {
    val sr = scenes[name]
    if (sr != null) return sr
    val sc = config.scenes.find { it.name == name }
    if (sc == null) throw BzError("errRobotAppNoSceneByName", name)
    if (sc.disabled) throw BzError("errRobotAppSceneDisabled", name)
    throw BzError("errCodeErr", "mustGetScene")
  }
  
  /**
   * 拿第一个场景
   */
  fun mustGetFirstScene(): RobotAppSceneRuntime = scenes.values.firstOrNull() ?: throw BzError("errRobotNoAnyScene")
  
  /**
   * 拿第一个第三代调度场景
   */
  fun mustGetFirstRachelScene(): RobotAppSceneRuntime = scenes.values.firstOrNull {
    it.config.mode == RobotAppMode.Rachel || it.config.mode == RobotAppMode.Light
  }
    ?: throw BzError("errRobotNoAnyScene")
  
  fun mustGetFirstTomScene(): RobotAppSceneRuntime = scenes.values.firstOrNull { it.config.mode == RobotAppMode.Tom }
    ?: throw BzError("errRobotNoAnyScene")
  
  /**
   * 拿第一个单车场景
   */
  fun mustGetFirstSingleScene(): RobotAppSceneRuntime = scenes.values.firstOrNull {
    it.config.mode == RobotAppMode.Single
  }
    ?: throw BzError("errRobotNoAnyScene")
  
  /**
   * 拿单车场景的管理
   */
  fun mustGetRmSingle(name: String): RaSingleManager =
    mustGetScene(name).single ?: throw BzError("errRobotAppNoSingleScene", name)
  
  /**
   * 拿第一个光通讯场景
   */
  fun mustGetFirstLightScene(): RobotAppSceneRuntime = scenes.values.firstOrNull {
    it.config.mode == RobotAppMode.Light
  }
    ?: throw BzError("errRobotNoAnyScene")
  
  /**
   * 根据场景名取光通讯场景
   */
  fun getLightScene(sceneName: String): RobotAppSceneRuntime? = scenes.values.filter {
    it.config.mode == RobotAppMode.Light
  }.firstOrNull { it.config.name == sceneName }
  
  /**
   * 获取第一个启用的单车场景
   */
  fun getEnabledSingleSceneOrNull(): RobotAppSceneRuntime? = scenes.values.firstOrNull {
    !it.config.disabled && it.config.mode == RobotAppMode.Single
  }
}