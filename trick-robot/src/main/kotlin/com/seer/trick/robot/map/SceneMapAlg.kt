package com.seer.trick.robot.map

import com.seer.wcs.GeoHelper.euclideanDistance
import com.seer.wcs.ShortestPath
import org.jgrapht.Graph
import org.jgrapht.alg.shortestpath.DijkstraShortestPath
import org.jgrapht.graph.DefaultWeightedEdge
import org.jgrapht.graph.builder.GraphTypeBuilder

/**
 * 对地图算法的封装
 */
class SceneMapAlg(scene: MrSceneMap, excludedSiteIds: Set<String>? = null) {

  private val graph: Graph<String, DefaultWeightedEdge>

  val g: DijkstraShortestPath<String, DefaultWeightedEdge>

  init {
    graph = buildEmptyGraph()

    // 按区域构建，不考虑跨区域的边
    for (area in scene.areas) {
      val siteMap: MutableMap<String, MrSite> = HashMap()
      for (site in area.sites) {
        if (excludedSiteIds != null && excludedSiteIds.contains(site.id)) continue
        graph.addVertex(site.id)
        siteMap[site.id] = site
      }

      // TODO 可能有路径重复
      for (edge in area.edges) {
        val site1 = siteMap[edge.fromId] ?: continue
        val site2 = siteMap[edge.toId] ?: continue
        val length = euclideanDistance(site1.x, site1.y, site2.x, site2.y)
        if (length <= 0) continue
        if (edge.dir == EdgeDirection.Dual) {
          val e1 = graph.addEdge(edge.fromId, edge.toId)
          if (e1 != null) graph.setEdgeWeight(e1, length)
          val e2 = graph.addEdge(edge.toId, edge.fromId)
          if (e2 != null) graph.setEdgeWeight(e2, length)
        } else if (edge.dir == EdgeDirection.Forward) {
          val e = graph.addEdge(edge.fromId, edge.toId)
          if (e != null) graph.setEdgeWeight(e, length)
        } else {
          val e = graph.addEdge(edge.toId, edge.fromId)
          if (e != null) graph.setEdgeWeight(e, length)
        }
      }
    }

    g = DijkstraShortestPath(graph)
  }

  fun getShortestPath(from: String, end: String): ShortestPath {
    if (!graph.containsVertex(from)) return ShortestPath(false, reason = "No start point: $from")
    if (!graph.containsVertex(end)) return ShortestPath(false, reason = "No end point: $end")
    val p = g.getPath(from, end) ?: return ShortestPath(false)
    return ShortestPath(true, p.vertexList)
  }

  private fun buildEmptyGraph(): Graph<String, DefaultWeightedEdge> {
    return GraphTypeBuilder
      .directed<String, DefaultWeightedEdge>()
      .allowingMultipleEdges(false)
      .allowingSelfLoops(false)
      .edgeClass(DefaultWeightedEdge::class.java)
      .weighted(true).buildGraph()
  }

}
