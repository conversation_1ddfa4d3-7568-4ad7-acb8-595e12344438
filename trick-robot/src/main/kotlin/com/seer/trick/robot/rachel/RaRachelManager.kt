package com.seer.trick.robot.rachel

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.Cq

import com.seer.trick.base.SysEmc
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.soc.SocService
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.NumHelper
import com.seer.trick.robot.RobotAppMode
import com.seer.trick.robot.RobotAppSceneConfig
import com.seer.trick.robot.RobotAppSceneRuntime
import com.seer.trick.robot.light.LightDispatcher
import com.seer.trick.robot.light.LightScheduler
import com.seer.trick.robot.rachel.adapter.MrRobotAdapterRbk
import com.seer.trick.robot.rachel.adapter.MrRobotAdapterTom
import com.seer.trick.robot.rachel.dispatching.MrDispatchOrderService
import com.seer.trick.robot.rachel.dispatching.MrDispatchStepService
import com.seer.trick.robot.rachel.dispatching.MrParkingService
import com.seer.trick.robot.rachel.order.MrOrderCancelService
import com.seer.trick.robot.rachel.order.MrOrderService
import com.seer.trick.robot.rachel.order.MrRetryFailedService
import com.seer.trick.robot.rachel.robot.MrRobotController
import com.seer.trick.robot.rachel.scheduler.MrScheduleService
import com.seer.trick.robot.tom.RaTomMap
import com.seer.trick.robot.tom.RaTomState
import com.seer.trick.robot.tom.TomRuntimeRecord
import com.seer.trick.robot.vendor.seer.SeerConnector
import com.seer.wcs.GeoHelper
import com.seer.wcs.WcsExecutor
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.Future
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

/**
 * 基于三代调度的应用模式。
 * 光通讯也基于这个类。
 */
class RaRachelManager(val sceneConfig: RobotAppSceneConfig, val sceneRuntime: RobotAppSceneRuntime) {

  private val logger = LoggerFactory.getLogger(this::class.java)

  // 运单基础管理
  val orderService = MrOrderService(this)

  // 取消运单
  val orderCancelService = MrOrderCancelService(this)

  // 失败重试
  val retryFailedService = MrRetryFailedService(this)

  // 派单
  val dispatchOrderService = MrDispatchOrderService(this)

  // 步骤分派
  val dispatchStepService = MrDispatchStepService(this)

  // 路径规划和交通管制
  val scheduleService = MrScheduleService(this)

  // 停靠
  val parkingService = MrParkingService(this)

  // 向下控制机器人
  val robotController = MrRobotController(this)

  // 光通讯

  // 光通讯派单
  val lightDispatcher = LightDispatcher(this)

  // 光通讯路径规划和交通管制
  val lightScheduler = LightScheduler(this)

  // 机器人运行时。停用的也在里面
  val robots: MutableMap<String, MrRobotRuntime> = ConcurrentHashMap()

  @Volatile
  private var updateAlarmFuture: Future<*>? = null

  // 兼容 Tom 模式，状态监控
  @Volatile
  private var stateAgent: RaTomState? = null

  // 兼容 Tom 模式，地图监控
  @Volatile
  private var mapAgent: RaTomMap? = null

  // 派单专用线程
  val dispatchExecutor: ExecutorService = Executors.newSingleThreadExecutor()

  // 关键修改加锁
  private val keyLocker = ReentrantLock()

  /**
   * 全局锁
   */
  fun withKeyLock(worker: () -> Unit) {
    keyLocker.withLock(worker)
  }

  @Synchronized
  fun init() {
    if (sceneConfig.rachel.withTom) {
      val tomUrlRoot = sceneConfig.rachel.tomUrlRoot
      if (tomUrlRoot.isNullOrBlank()) {
        logger.error("场景配置中没有配置 Tom 的 URL")
        return
      }

      val mapAgent = RaTomMap(sceneConfig.name, tomUrlRoot, sceneRuntime.map)
      this.mapAgent = mapAgent

      val stateAgent = RaTomState(
        sceneConfig.name,
        tomUrlRoot,
        sceneRuntime,
        { mapAgent.tryUpdateSceneMap(it) },
        ::onTomRobotStates,
      )
      stateAgent.init()
    }

    SysEmc.addCallback(::onSysEmc)

    // 先恢复运单，初始化（恢复）机器人需要
    orderService.restoreOrders()

    // 启动时先不接单还是直接开始接单
    val onDutyOnBoot = sceneConfig.rachel.onDutyOnBoot
    // val onDutyOnBoot = BzConfigManager.getByPathAsBoolean("ScWcs", "fleet", "onDutyOnBoot")
    try {
      // 按场景过滤
      val systemConfigEvList = EntityRwService.findMany("MrRobotSystemConfig", Cq.eq("scene", sceneConfig.name))
      for (systemConfigEv in systemConfigEvList) {
        // 默认启动时先不接单
        if (!onDutyOnBoot) systemConfigEv["offDuty"] = true
        addRobot(systemConfigEv)
      }
    } catch (e: Exception) {
      // TODO 告诉用户
      logger.error("初始化机器人报错", e)
    }

    updateAlarmFuture = WcsExecutor.submitLongRun("更新机器人报警", logger, 500, ::updateRobotAlarms)

    if (sceneConfig.mode == RobotAppMode.Rachel) {
      WcsExecutor.submitLongRun("派单", logger, 1000) {
        // 都是非阻塞代码

        // 派单
        dispatchOrderService.dispatchOrders()

        // 选步骤
        for (rr in robots.values) {
          dispatchStepService.dispatchStep(rr)
        }
      }
    } else if (sceneConfig.mode == RobotAppMode.Light) {
      lightDispatcher.init()
    }
  }

  @Synchronized
  fun dispose() {
    SysEmc.removeCallback(::onSysEmc)

    stateAgent?.dispose()

    updateAlarmFuture?.cancel(true)

    for (rr in robots.values) {
      disposeRobot(rr)
    }

    robots.clear()
  }

  fun addRobots(systemConfigEvList: List<EntityValue>) {
    for (ev in systemConfigEvList) {
      ev["scene"] = sceneConfig.name
      EntityRwService.createOne("MrRobotSystemConfig", ev)
      addRobot(ev)
    }
  }

  @Synchronized
  private fun addRobot(systemConfigEv: EntityValue) {
    val config = MrSystemConfig(systemConfigEv)

    val id = EntityHelper.mustGetId(systemConfigEv)
    val rr = MrRobotRuntime(id)
    robots[id] = rr

    rr.updateSystemConfig(config)

    initRobot(rr)
  }

  @Synchronized
  private fun initRobot(rr: MrRobotRuntime) {
    if (rr.systemConfig.disabled) return

    reconnectRobot(rr)

    if (sceneConfig.rachel.withTom) {
      rr.adapter = MrRobotAdapterTom(sceneConfig.rachel.tomUrlRoot!!)
    } else {
      rr.adapter = MrRobotAdapterRbk(this)
    }

    restoreRuntime(rr)
  }

  /**
   * 如果 MrRobotRuntimeRecord 不存在，自动创建
   */
  private fun restoreRuntime(rr: MrRobotRuntime) {
    val ev = EntityRwService.findOne("MrRobotRuntimeRecord", Cq.idEq(rr.id))
    if (ev != null) {
      val orderIdsStr = ev["rtOrders"] as String?
      if (!orderIdsStr.isNullOrBlank()) {
        val orderIds = orderIdsStr.split(",")
        for (orderId in orderIds) {
          val order = orderService.orders[orderId] ?: continue
          rr.orders[orderId] = order
        }
      }

      val binsStr = ev["rtBins"] as String?
      if (!binsStr.isNullOrBlank()) {
        val bins: List<MrRobotBin> = JsonHelper.mapper.readValue(binsStr, jacksonTypeRef())
        rr.bins.clear()
        rr.bins.addAll(bins)
      }

      // TODO 如果运单不存在，bins 清理了

      // 不处理 cmdStatus 了，靠其他状态
      // cmdStatus = MrRobotCmdStatus.valueOf(ev["rtCmdStatus"] as String)

      // val currentOrderId = ev["rtCurrentOrder"] as String?
      // val currentStepIndex = ev["rtCurrentStep"] as Int?
      // val or = if (!currentOrderId.isNullOrBlank() && currentStepIndex != null) {
      //   MrOrderService.orders[currentOrderId]
      // } else {
      //   null
      // }
      // TODO 不应该恢复执行，应该进入故障状态？清掉 currentStep？等待重新分派？
      // if (or != null && currentStepIndex != null) {
      //   logger.info("恢复机器人当前运单、执行步骤：${currentOrderId}:${currentStepIndex}")
      //   MrRobotController.executeStep(rr, or, currentStepIndex)
      // }
    } else {
      // 确保有此对象，小心初始化值
      EntityRwService.createOne(
        
        "MrRobotRuntimeRecord",
        mutableMapOf(
          "id" to rr.id,
          "rtCmdStatus" to MrRobotCmdStatus.Idle.name,
          "extTaskStatus" to "Idle",
        ),
      )
    }
  }

  /**
   * 允许反复调用
   */
  @Synchronized
  fun disposeRobot(rr: MrRobotRuntime) {
    rr.connector?.dispose()
    rr.connector = null

    updateSelfReport(rr.id, null)

    SocService.removeNode("SeerConnector:${rr.id}")
    SocService.removeNode("RobotSelfReport:${rr.id}")
    SocService.removeNode("RobotReportApply:${rr.id}")
    SocService.removeNode("RobotMap:${rr.id}")
  }

  @Synchronized
  private fun reconnectRobot(rr: MrRobotRuntime) {
    rr.connector?.dispose()
    rr.connector = null

    updateSelfReport(rr.id, null)

    if (rr.systemConfig.disabled) return

    val ct = rr.systemConfig.connectionType
    if (ct != null) {
      val connector = SeerConnector(
        robotName = rr.id,
        mm = sceneRuntime.map,
        authId = rr.systemConfig.gwAuthId,
        authSecret = rr.systemConfig.gwAuthSecret,
        connectType = ct,
        rbkIp = rr.systemConfig.robotHost,
        onReport = { updateSelfReport(rr.id, it) },
      )
      rr.connector = connector
      connector.init()
    }
  }

  @Synchronized
  fun removeRobots(ids: List<String>) {
    for (id in ids) {
      val rr = robots.remove(id)
      if (rr != null) {
        disposeRobot(rr)
      }
      EntityRwService.removeOne("MrRobotSystemConfig", Cq.idEq(id))
      EntityRwService.removeOne("MockSeerRobot", Cq.idEq(id))
      scheduleService.updateRobotLocation(id, null)
    }
  }

  fun updateRobot(update: MrRobotUpdate) {
    updateRobot(update.id, update.systemConfig, update.runtimeRecord, true)
  }

  fun updateOffDuty(ids: List<String>, offDuty: Boolean) {
    for (id in ids) {
      updateRobot(id, mutableMapOf("offDuty" to offDuty), null)
    }
  }

  private fun updateRobot(
    
    robotName: String,
    configEv: EntityValue?,
    runtimeEv: EntityValue?,
    ifDisposeRobot: Boolean = false,
  ) = withKeyLock {
    val rr = robots[robotName] ?: return@withKeyLock

    val busy = rr.orders.isNotEmpty()
    if (busy) throw BzError("errRobotUpdateBusy")

    if (configEv != null) {
      EntityRwService.updateOne("MrRobotSystemConfig", Cq.idEq(robotName), configEv)
      // 因为会被 update 修改，所以重新获取下
      val systemConfig = EntityRwService.findOne("MrRobotSystemConfig", Cq.idEq(robotName))
      if (systemConfig == null) {
        logger.error("更新机器人，但 MrRobotSystemConfig 不存在，机器人=$robotName")
      } else {
        rr.updateSystemConfig(MrSystemConfig(systemConfig))
      }
    }

    if (runtimeEv != null) {
      EntityRwService.updateOne("MrRobotRuntimeRecord", Cq.idEq(robotName), runtimeEv)
      // TODO 更多更新
      // TODO if (update.runtimeRecord != null) this.runtimeRecord = update.runtimeRecord
    }
    if (ifDisposeRobot) {
      disposeRobot(rr)
      initRobot(rr)
    }
  }

  fun reconnectRobots(ids: List<String>) {
    for (id in ids) {
      val rr = robots[id] ?: continue
      reconnectRobot(rr)
    }
  }

  fun listAllAll(): List<MrRobotInfoAll> = robots.values.map { getRobotInfoAll(it) }

  fun mustGetRobotInfoAll(id: String): MrRobotInfoAll {
    val rr = robots[id] ?: throw BzError("errNoRobot", id)
    return getRobotInfoAll(rr)
  }

  fun getRobotInfoAll(rr: MrRobotRuntime): MrRobotInfoAll {
    val runtimeRecord = EntityRwService.findOne("MrRobotRuntimeRecord", Cq.idEq(rr.id)) ?: HashMap(0)

    val step = rr.getCurrentStep()
    val targetSite = if (step?.status == MrStepStatus.Executing) {
      step.location.site
    } else {
      null
    }

    val lockedSiteIds = scheduleService.listMySiteIds(rr.id)
    val pendingSiteIds = lightScheduler.pendingSiteIds[rr.id]

    return MrRobotInfoAll(
      rr.id,
      rr.systemConfig.ev,
      runtimeRecord,
      isOnline(rr.id),
      rr.selfReport,
      targetSite,
      lockedSiteIds,
      pendingSiteIds,
    )
  }

  fun isOnline(robotName: String): Boolean {
    val rr = robots[robotName] ?: return false
    val rrr = rr.selfReport
    if (rrr == null || rrr.error) return false
    val reportOn = rrr.timestamp
    return (System.currentTimeMillis() - reportOn.time) <= 6 * 1000
  }

  /**
   * 未禁用、可接单、在线、空闲的机器人
   */
  fun listIdleRobots(): List<MrRobotInfoAll> {
    
    val all: MutableList<MrRobotInfoAll> = mutableListOf()
    for (rr in robots.values) {
      if (rr.systemConfig.disabled || rr.systemConfig.offDuty || !isOnline(rr.id)) continue
      if (rr.cmdStatus != MrRobotCmdStatus.Idle) continue
      val ra = getRobotInfoAll(rr)
      all += ra
    }
    return all
  }

  private fun onSysEmc(enabled: Boolean) {
    // TODO
  }

  fun findClosestRobotConnectedPoint(robotName: String): EntityValue? {
    val rr = robots[robotName] ?: return null
    val main = rr.selfReport?.main ?: return null
    val rx = main.x ?: return null
    val ry = main.y ?: return null

    val points = EntityRwService.findMany("RobotConnectedPoint", Cq.all())
    var point: EntityValue? = null
    var minD = Double.MAX_VALUE
    for (p in points) {
      val x = NumHelper.anyToDouble(p["x"]) ?: continue
      val y = NumHelper.anyToDouble(p["y"]) ?: continue
      val d = GeoHelper.euclideanDistance(rx, ry, x, y)
      if (d <= 1 && d < minD) { // <= 1m
        point = p
        minD = d
      }
    }
    return point
  }

  /**
   * 更新报告的唯一路径
   */
  fun updateSelfReport(robotName: String, report: MrRobotSelfReport?) {
    val rr = robots[robotName] ?: return

    rr.updateSelfReport(report, sceneRuntime.map)

    scheduleService.updateRobotLocation(robotName, sceneRuntime.map.getSceneLocation(report?.main))
  }

  /**
   * 清除机器人报错，仙工机器人
   */
  fun clearRobotSelfAlarm(ids: List<String>) {
    for (id in ids) {
      val rr = robots[id] ?: continue
      try {
        clearRobotSelfAlarm(rr)
      } catch (e: Exception) {
        logger.error("清理报警")
      }
    }
  }

  /**
   * 清除机器人报错，仙工机器人
   */
  private fun clearRobotSelfAlarm(rr: MrRobotRuntime) {
    if (rr.systemConfig.vendor != RobotVendor.Seer) return
    val connector = rr.connector ?: return
    connector.requestControl()
    connector.requestWithCodeCheck(4009, "")

    // TODO 释放控制权
  }

  private fun updateRobotAlarms() {
    val robots = robots.values.toList().filter { !it.systemConfig.disabled }
    val offlineRobots = robots.filter { !isOnline(it.id) }.map { it.id }

    val mains: MutableMap<String, MrRobotSelfReportMain?> = mutableMapOf()
    for (robot in robots) {
      if (!robot.selfReport?.main?.alerts.isNullOrEmpty()) mains[robot.id] = robot.selfReport?.main
    }
    sceneRuntime.updateRobotAlarms(offlineRobots, mains)
  }

  private fun onTomRobotStates(tr: TomRuntimeRecord) {
    if (tr.error) {
      for (robot in robots.values) {
        updateSelfReport(robot.id, MrRobotSelfReport(true, tr.errorMsg))
      }
    } else {
      for (robot in tr.robots) {
        updateSelfReport(robot.id, MrRobotSelfReport(false, null, Date(), robot.mainReport, robot.rbkReport))
      }
    }
  }
}