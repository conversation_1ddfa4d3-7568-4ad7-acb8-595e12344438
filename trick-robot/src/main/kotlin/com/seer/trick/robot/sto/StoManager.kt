package com.seer.trick.robot.sto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.Cq

import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.helper.BoolHelper
import com.seer.trick.helper.DateHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.robot.RobotAppManager
import com.seer.trick.robot.gw.GwCenter
import com.seer.trick.robot.rachel.RobotVendor
import com.seer.trick.robot.single.RaSingleManager
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentLinkedQueue

object StoManager {

  private val logger = LoggerFactory.getLogger(StoManager::class.java)
  val stoRobotMap: MutableMap<String, StoRobot> = ConcurrentHashMap()

  @Synchronized
  fun init() {
    
    val gwConfig = GwCenter.gwConfig
    if (gwConfig.enabled) {
      for (robot in gwConfig.localConfigs) {
        if (robot.disabled) continue
        if (stoRobotMap.containsKey(robot.name)) continue
        val stoRobot = StoRobot(robot.name)
        stoRobotMap[robot.name] = stoRobot
        stoRobot.init()
      }
    }
    // 添加单车场景机器人
    val sceneOrNull = RobotAppManager.getEnabledSingleSceneOrNull()
    sceneOrNull?.let {
      if (!stoRobotMap.containsKey(RaSingleManager.DEFAULT_NAME)) {
        val stoRobot = StoRobot(RaSingleManager.DEFAULT_NAME)
        stoRobotMap[RaSingleManager.DEFAULT_NAME] = stoRobot
        stoRobot.init()
      }
    }
  }

  fun start(order: CreateStoReq) {
    val robot = stoRobotMap[order.robotName] ?: throw BzError("errGwNoLocalRobot", order.robotName)
    robot.start(order)
  }

  fun cancelCurrentOrder(robotName: String) {
    val robot = stoRobotMap[robotName] ?: throw BzError("errGwNoLocalRobot", robotName)
    robot.cancelOrManualDoneCurrentOrder(StOrderStatus.Cancelled)
  }

  fun cancelOrders(robotName: String, cancelledOrderIds: List<String>) {
    val robot = stoRobotMap[robotName] ?: throw BzError("errGwNoLocalRobot", robotName)
    robot.cancelOrders(cancelledOrderIds)
  }

  fun retryFailed(robotName: String) {
    val robot = stoRobotMap[robotName] ?: throw BzError("errGwNoLocalRobot", robotName)
    robot.retryFailed()
  }

  fun manualDoneOrders(robotName: String, manualDoneOrderIds: List<String>) {
    val robot = stoRobotMap[robotName] ?: throw BzError("errGwNoLocalRobot", robotName)
    robot.manualDoneOrders(manualDoneOrderIds)
  }

  fun fetchRecentOrders(robotName: String): RecentOrders {
    val robot = stoRobotMap[robotName] ?: throw BzError("errGwNoLocalRobot", robotName)
    var unfinishedEvList = EntityRwService.findMany(
      
      "SimpleTransportOrder",
      Cq.and(
        listOf(
          Cq.eq("robotName", robotName),
          Cq.include("status", listOf(StOrderStatus.Created.name, StOrderStatus.Failed.name)),
        ),
      ),
      FindOptions(sort = listOf("+createdOn")),
    )
    // 上报时，单车运单不上报，否则在服务端找不到该运单就会被服务端取消
    val prefix = BaseCenter.entityMetaMap["SimpleTransportOrder"]?.idGen?.fixedPrefix ?: ""
    unfinishedEvList = unfinishedEvList.filter { !(it["id"]?.toString()?.startsWith(prefix) ?: false) }
    return RecentOrders(
      currentOrder = robot.currentOrder?.toResult(),
      lastOrder = robot.lastOrder?.toResult(),
      unfinishedOrders = unfinishedEvList.mapNotNull {
        try {
          OrderResult(it["id"] as String, StOrderStatus.valueOf(it["status"] as String), it["errMsg"] as String?)
        } catch (e: Exception) {
          null
        }
      },
    )
  }

  fun listRobots(): List<StoRobot> = stoRobotMap.values.toList()

  fun cancelOrder(robotName: String, orderId: String) {
    val robot = stoRobotMap[robotName] ?: throw BzError("errGwNoLocalRobot", robotName)
    robot.cancelOrder(orderId)
  }

  fun manualDoneOrder(robotName: String, orderId: String) {
    val robot = stoRobotMap[robotName] ?: throw BzError("errGwNoLocalRobot", robotName)
    robot.manualDoneOrder(orderId)
  }

  fun removeRobot(robotName: String) {
    val robot = stoRobotMap[robotName]
    robot?.dispose()
    stoRobotMap.remove(robotName)
  }

  // TODO 暂时做兼容性处理，当只有单车场景则返回单车默认值，否则返回第一个网关配置的机器人
  fun mustGetRobotName(): String {
    val keys = stoRobotMap.keys
    if (keys.isEmpty()) {
      throw BzError("errRobotNoAnyScene")
    }
    keys.firstOrNull { it != RaSingleManager.DEFAULT_NAME }?.let { return it }
    return keys.first()
  }
}

object StoQueue {

  private val logger = LoggerFactory.getLogger(this::class.java)
  private val waitQueueMap: MutableMap<String, Queue<StOrder>> = ConcurrentHashMap()

  fun enqueue(order: StOrder) {
    logger.info("单车运单 ${order.id} 加入队列")
    val queue = waitQueueMap.computeIfAbsent(order.robotName) { ConcurrentLinkedQueue() }
    queue += order
  }

  fun dequeue(robotName: String): StOrder? {
    val queue = waitQueueMap[robotName]
    val stOrder = queue?.poll()
    if (stOrder != null) {
      logger.info("单车运单 ${stOrder.id} 从队列中取出")
    }
    return stOrder
  }

  fun remove(robotName: String, orderId: String) {
    val queue = waitQueueMap[robotName]
    if (queue == null || queue.isEmpty()) return
    waitQueueMap.remove(robotName)
    val newQueue: Queue<StOrder> = ConcurrentLinkedQueue()
    for (queOrder in queue) {
      if (queOrder.id != orderId) {
        newQueue += queOrder
      }
    }
    waitQueueMap[robotName] = newQueue
    logger.info("单车运单 $orderId 从队列中移除")
  }

  fun clear(robotName: String) {
    logger.info("删除机器人 $robotName 的单车运单队列")
    waitQueueMap.remove(robotName)
  }
}

data class RecentOrders(
  val currentOrder: OrderResult?,
  val lastOrder: OrderResult?,
  val unfinishedOrders: List<OrderResult>,
)

data class OrderResult(val orderId: String, val status: StOrderStatus, val errMsg: String? = null)

@JsonIgnoreProperties
data class CreateStoReq(
  val id: String = "", // 单号
  val vendor: RobotVendor = RobotVendor.Seer, // 厂商
  val robotName: String = "", // 指定机器人
  val seer3066: Boolean = false, // 采用 3066 导航
  val moves: List<EntityValue> = emptyList(),
)

@JsonIgnoreProperties
class StOrder(
  val id: String = "",
  val vendor: RobotVendor = RobotVendor.Seer,
  val robotName: String = "",
  val seer3066: Boolean = false, // 采用 3066 导航
  val moves: List<EntityValue> = emptyList(),
  val createdOn: Date = Date(),
) {

  @Volatile
  var status: StOrderStatus = StOrderStatus.Created

  @Volatile
  var currentMove = 0

  @Volatile
  var doneOn: Date? = null

  @Volatile
  var errorMsg: String? = null

  fun toResult(): OrderResult = OrderResult(id, status, errorMsg)

  fun toEv(): EntityValue = mutableMapOf(
    "id" to this.id,
    "vendor" to this.vendor.name,
    "robotName" to this.robotName,
    "createdOn" to this.createdOn,
    "status" to this.status.name,
    "seer3066" to this.seer3066,
    "moves" to JsonHelper.mapper.writeValueAsString(this.moves),
    "currentMove" to this.currentMove,
    "doneOn" to this.doneOn,
    "errorMsg" to this.errorMsg,
  )

  override fun toString(): String = JsonHelper.writeValueAsString(toEv())

  companion object {

    fun fromEv(ev: EntityValue): StOrder {
      val vendor = ev["vendor"] as String?
      val order = StOrder(
        id = EntityHelper.mustGetId(ev),
        vendor = if (vendor.isNullOrBlank()) RobotVendor.Seer else RobotVendor.valueOf(vendor),
        robotName = ev["robotName"] as String,
        seer3066 = BoolHelper.anyToBool(ev["seer3066"]),
        moves = JsonHelper.mapper.readValue(ev["moves"] as String, jacksonTypeRef()),
        createdOn = DateHelper.anyToDate(ev["createdOn"])!!,
      )
      order.status = StOrderStatus.valueOf(ev["status"] as String)
      order.currentMove = ev["currentMove"] as Int
      order.doneOn = DateHelper.anyToDate(ev["doneOn"])
      order.errorMsg = ev["errorMsg"] as String?

      return order
    }
  }
}

enum class StOrderStatus {
  Created,
  Done,
  Failed,
  Cancelled,
}