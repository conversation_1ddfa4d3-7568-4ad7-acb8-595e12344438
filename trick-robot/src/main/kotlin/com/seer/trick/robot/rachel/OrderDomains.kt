package com.seer.trick.robot.rachel

import org.apache.commons.lang3.StringUtils
import java.util.*

/**
 * 移动机器人“通用运单”
 */
data class MrOrder(
  val reqId: String = "", // 请求 ID
  val containerId: String? = null, // 搬运的容器编号
  // val containerType: String? = null,// 搬运的容器类型
  // val weight: Double? = null, // 货物重量
  // val volume: Double? = null, // 货物体积
  // val size: Double? = null, // 货物面积
  // val sizeX: Double? = null, // 货物长
  // val sizeY: Double? = null, // 货物宽
  // val sizeZ: Double? = null, // 货物高
  val materialId: String? = null, // 搬运货物的物料编号
  val materialKind: String? = null, // 货物类别
  // val area: String? = null, // 业务发生区域
  val taskBatch: String? = null, // 对于多负载机器人期望一起取放的任务
  val priority: Int = 0, // 优先级
  val expectedRobotNames: List<String>? = null, // 期望以下机器人执行
  val expectedRobotGroups: List<String>? = null, // 期望以下机器人组执行
  val keySites: List<String>? = null, // 关键点位
  val createdOn: Date = Date(),
  val kind: MrOrderKind? = null,
  val noLoad: Boolean = false, // 不用于装卸货
  //
  val id: String = "",
  val status: MrOrderStatus = MrOrderStatus.Building,
  val fault: Boolean = false,
  val stepNum: Int = 0,
  val stepFixed: Boolean = false, // 所有步骤是否都已创建好，不会再追加了
  val currentStepIndex: Int = -1,
  val doneStepIndex: Int = -1,
  val actualRobotName: String? = null,
  val robotAllocatedOn: Date? = null,
  val loadLocation: LocationFeature? = null, // 取货位置
  val unloadLocation: LocationFeature? = null, // 放货位置
  val doneOn: Date? = null,
  val loaded: Boolean = false,
  val unloaded: Boolean = false,
  val dispatchCost: Double? = null,
)

/**
 * 移动机器人运单状态
 */
enum class MrOrderStatus {
  Building,
  ToBeAllocated,
  Allocated,
  Executing,  // 正在执行，即运单的某个步骤正在被机器人执行！
  Pending, // 已执行了至少一个步骤，但当前没有步骤被执行
  Withdrawn, // 仅当 Executing 的单子被二分，实际取消前会先标记为 Withdrawn
  Done,
  Cancelling,
  Cancelled,
}

/**
 * 移动机器人特殊类型
 */
enum class MrOrderKind {
  Parking
}

/**
 * 通用运单步骤
 */
data class MrStep(
  val id: String = "",
  val orderId: String = "",
  val stepIndex: Int = 0,
  val status: MrStepStatus = MrStepStatus.Building,
  val createdOn: Date = Date(),
  val interruptible: Boolean = false, // 是否可以被打断
  val location: LocationFeature = LocationFeature(), // 作业位置
  val rbkArgs: String? = null, // 动作参数
  val forLoad: Boolean = false, // 在此步取货
  val forUnload: Boolean = false, // 在此步卸货
  val startOn: Date? = null,
  val endOn: Date? = null
)

data class LocationFeature(
  val site: String? = null, // 点位
  // val bin: String? = null, // 库位
  // val district: String? = null, // 区域
  // val channel: String? = null, // 巷道
  // val rackNo: String? = null, // 货架编号
  // val row: Int? = null, // 排
  // val column: Int? = null, // 列
  // val layer: Int? = null, // 层
)

/**
 * 运单步骤状态
 */
enum class MrStepStatus {
  Building,
  Executable,
  Executing,
  Withdrawn, // 仅当 Executing 的运单被二分，实际取消前会先标记为 Withdrawn
  Done,
  Skipped
}

data class MrOrderWithSteps(
  val order: MrOrder,
  val steps: List<MrStep>
)

class MrOrderRuntime(
  @Volatile
  var order: MrOrder,
  @Volatile
  var steps: List<MrStep>
) {
  
  val orderId = order.id
  
  var shortOrderId = StringUtils.substring(order.id, -6)
  
  // 记录历史分派
  val historyRobots: MutableList<String> = Collections.synchronizedList(ArrayList())
  
  fun setStep(step: MrStep) {
    val steps = this.steps.toMutableList()
    steps[step.stepIndex] = step
    this.steps = steps
  }

  fun addStep(step:MrStep){
    val steps = this.steps.toMutableList()
    steps.add(step)
    this.steps = steps
  }

  override fun toString(): String {
    return "$orderId (${order.status})"
  }
  
}

data class PreAllocation(
  val robotName: String,
  val orderId: String,
  val priority: Int,
  val cost: Double,
  val createdOn: Long,
)

data class SelectedStep(
  val order: MrOrderRuntime,
  val step: MrStep,
  val cost: Double,
)

data class MrCmdResult(
  val kind: MrCmdResultKind,
  val msg: String? = null,
)

enum class MrCmdResultKind {
  Ok,
  Failed, // 失败
  Cancelled, // 取消
  // Interrupted
}