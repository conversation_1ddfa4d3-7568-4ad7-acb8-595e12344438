package com.seer.trick.robot.single.bp

import com.seer.trick.BzError

import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.NumHelper
import com.seer.trick.robot.RobotAppManager
import com.seer.trick.robot.single.RaSingleManager

abstract class AbstractTaskChainBp : AbstractBp() {

  fun executeTask(req: RbkRequest): String {
    val scene = RobotAppManager.getEnabledSingleSceneOrNull() ?: throw BzError("errRobotAppNoScene")
    val rmSingle = scene.single!!
    rmSingle.stateAgent.report

    logger.info("reqId：${req.reqId}，正在执行 [${req.move}] ")
    // 尝试获取请求控制权
    tryRequestControl(rmSingle)
    val resStr = rmSingle.rbkClient.requestWithReturnCodeError(req.api<PERSON>o, req.reqStr)
    awaitComplete(rmSingle, req.reqId)
    return resStr
  }

  private fun tryRequestControl(rmSingle: RaSingleManager) {
    val currentLock = rmSingle.stateAgent.report?.rawReport?.get("current_lock") as Map<*, *>?
    val currentLockNickName = rmSingle.stateAgent.report?.main?.currentLockNickName
    val isControlled = currentLock?.get("locked") as Boolean?
    val isSelf = currentLockNickName == "this"
    val taskStatus = NumHelper.anyToInt(rmSingle.stateAgent.report?.rawReport?.get("task_status"))
    val nickName = rmSingle.getNickname()
    if (currentLockNickName == nickName) return

    // 导航中、急停、机器人控制权已被抢占，则不能抢占控制权
    if (listOf(1, 2, 3).indexOf(taskStatus) >= 0 || rmSingle.rbkEmergencyStop() || (isControlled == true && !isSelf)) {
      throw BzError("errCannotRequestControl", rmSingle.stateAgent.report?.rawReport?.get("vehicle_id") as String?)
    }
    // 请求控制权
    rmSingle.rbkClient.dominateAgv(nickName)
  }

  private fun awaitComplete(rmSingle: RaSingleManager, reqId: String) {
    var errCount = 0
    while (true) {
      Thread.sleep(200)
      val res1020 = rmSingle.rbkClient.requestWithReturnCodeError(1020, "")
      val resNode = JsonHelper.mapper.readTree(res1020)
      val taskStatus = resNode?.get("task_status")?.asInt() ?: continue
      if (taskStatus == 4) {
        logger.info("动作执行完成 $reqId")
        break
      } else if (taskStatus == 5 || taskStatus == 6 || taskStatus == 0 || taskStatus == 404) {
        ++errCount
        if (errCount > 5 && (taskStatus == 0 || taskStatus == 404) || taskStatus == 5 || taskStatus == 6) {
          val errMsg = "动作执行结果 = $taskStatus。${resNode["kind"]}: ${resNode["errorMsg"]}"
          logger.error("等待动作完成 $errMsg")
          throw BzError("errAwaitMove", errMsg)
        }
      }
    }
  }
}

data class RbkRequest(val reqId: String, val apiNo: Int, val reqStr: String, val move: String)