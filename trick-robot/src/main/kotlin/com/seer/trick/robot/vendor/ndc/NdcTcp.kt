package com.seer.trick.robot.vendor.ndc

import com.seer.wcs.device.tcp.FixedHeadFrameSchema
import io.netty.buffer.ByteBuf
import io.netty.buffer.ByteBufUtil
import io.netty.buffer.CompositeByteBuf
import io.netty.buffer.Unpooled
import org.slf4j.LoggerFactory

object NdcTcp {
  private val logger = LoggerFactory.getLogger(this::class.java)

  val schema = FixedHeadFrameSchema(
    byteArrayOf(0x87.toByte(), 0xCD.toByte()), // 固定开头
    8,
    { buf -> buf.getShort(4).toInt() },
    { head, body ->
      head.readerIndex(2)
      val headLength = head.readShort().toInt()
      val bodyLength = head.readShort().toInt()
      val functionCode = head.readShort().toInt()

      val type = if (functionCode == 1) body.getShort(0).toInt() else 0

      // head.release()

      NdcFrame(headLength, bodyLength, functionCode, type, head, body)
    }
  )

  fun buildFrame(functionCode: Int, body: ByteBuf): CompositeByteBuf {
    val headBuf: ByteBuf = Unpooled.buffer(schema.headLength)
    headBuf.writeBytes(schema.start) // 4
    headBuf.writeShort(schema.headLength) // 2
    headBuf.writeShort(body.readableBytes()) // 2
    headBuf.writeShort(functionCode)

    val reqBuf: CompositeByteBuf = Unpooled.compositeBuffer()
    reqBuf.addComponent(true, headBuf)
    reqBuf.addComponent(true, body)

    // 后面挪动到 write
    val headStr = ByteBufUtil.hexDump(headBuf, 0, headBuf.readableBytes())
    val bodyStr = ByteBufUtil.hexDump(body, 0, body.readableBytes())
    // 不打印 ping、pong 消息
    if (body.readableBytes() > 0)
      logger.debug("NdcAdapter 构建 NDC 消息，headLength=${schema.headLength} bodyLength=${body.readableBytes()} headBuf=${headStr} bodyBuf=${bodyStr}")

    return reqBuf
  }
}


// 头
data class NdcFrame(
  val headLength: Int,
  val bodyLength: Int,
  val functionCode: Int,
  val type: Int, // 消息类型
  val headBuf: ByteBuf,
  val bodyBuf: ByteBuf
)

object NdcTaskState {
  const val NDC_INIT = 0
}

enum class NdcMsgType(val code: Int, val bodyLength: Int) {
  Q(0x71, 14), // GW 下发任务
  Ba(0x62, 10), // NDC 回复 Q、S、M TODO 长度？
  Bb(0x62, 12), // NDC 回复 Q、S、M TODO 长度？
  S(0x73, 16), // NDC 上报任务状态
  M(0x6D, 8), // GW 更改任务参数
  N(0x6E, 6), // GW 删除任务
}


data class NdcOrder(
  val id: String = "",
  val fromBin: Int = 0,
  val toBin: Int = 0,
  val priority: Int = 0x80, // 0x80 不实用优先级；0x81 - 0xE3，0xE3 优先级最高
  val iKey: Int = 0,
  val state: Int = 0,
)

object NdcOrderState {
  const val Created = 0
  const val Sent = 100
  const val SentR1 = 105 // 命令接受
  const val SentR2 = 110 // 参数值接受
  const val ReadyToLoad = 204 //
  const val AllowLoad = 205
  const val ReadyToLoadB = 210
  const val LoadDoneS = 300
  const val LoadDoneM = 305
  const val LoadDoneB = 310
  const val ReadyToUnloadS = 400
  const val ReadyToUnload = 404 //
  const val AllowNdcUnloadBp = 405
  const val ReadyToUnloadB = 410
  const val UnloadDoneS = 500
  const val UnlaodDoneM = 505
  const val UnloadDoneB = 510
  const val TaskDoneS = 600
  const val TaskDoneM = 605
  const val TaskDoneB = 610
  const val TaskDone = 800 // 任务完成
  const val Abort = 900 // 任务被终止
  const val Deleted = 999 // 任务被删除
}

object NdcS {
  const val AgvAssigned = 1  // AGV 已分配
  const val LoadReq = 0x10  // 装货请求
  const val LoadDone = 0x11 // 装货成功
  const val LoadOverTime = 0x12 // 装货失败-操作超时
  const val LoadApplyAdd = 0x13 // 装货地址申请（主要用于巷道）
  const val LoadNoGoods = 0x14  // 装货失败-检测无货
  const val UnloadReq = 0x20  // 卸货请求
  const val UnloadDone = 0x21 // 卸货成功
  const val UnloadOvertime = 0x22 // 卸货失败-操作超时
  const val UnloadApplyAdd = 0x23 // 卸货地址申请（主要用于巷道）
  const val UnloadNoGoods = 0x24  // 卸货失败-检测无货
  const val Deleted = 0x30  // 任务被删除
  const val Abort = 0x31  // 任务意外终止
  const val Finished = 0x50 // 命令结束
}

object NdcB {
  const val Reject = 0 // 命令拒绝
  const val Accept = 1 // 命令接受
  const val Finished = 3 // 命令结束
  const val Canceled = 4 // 命令取消
  const val ParamConfirmed = 7 // 参数确认
  const val PriorityError = 9 // 优先级错误

  const val BuffFulled = 11 // 命令缓冲区满
  const val Error = 17 // 严重错误，命令执行结束
  const val ParamDeleted = 18 // 参数删除
  const val ParamAccepted = 19 // 参数值接受
  const val ParamNotAccepted = 20 // 参数值末被接受
  const val CancelConfirmed = 25 // 取消命令确认
  const val LackParam = 26 // 缺少参数
  const val DupIkey = 27 // IKEY 重复
  const val ChangePoriorityConfirmed = 35 // 改变命令优先级确认
}