package com.seer.trick.robot.handler

import com.seer.trick.BzError
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager
import com.seer.trick.base.http.getReqBody

import com.seer.trick.helper.JsonHelper
import com.seer.trick.robot.sto.CreateStoReq
import com.seer.trick.robot.sto.StoManager
import com.seer.trick.robot.sto.StoStore
import io.javalin.http.Context

object StoHandler {

  fun registerHandlers() {
    val c = Handlers("api/wcs/sto")
    c.post("create", ::handleCreateSimpleTransportOrder, HttpServerManager.auth())
    c.get("get/{id}", ::handleQueryOrder, HttpServerManager.auth())
    c.post("cancel-current", ::handleCancelCurrentOrder, HttpServerManager.auth())
    c.post("cancel", ::handle<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HttpServerManager.auth())
    c.post("retry", ::handleRetryOrder, HttpServerManager.auth())
    c.post("done", ::handleManualDoneOrder, HttpServerManager.auth())
  }

  private fun handleCreateSimpleTransportOrder(ctx: Context) {
    

    val stepReq: CreateStoStepReq = ctx.getReqBody()
    if (stepReq.steps.isEmpty()) throw BzError("errStepCannotBeEmpty")
    val steps = stepReq.steps.sortedBy { it.index }
    val order = CreateStoReq(
      robotName = stepReq.robotName,
      moves = steps.map { step ->
        val ev: EntityValue = HashMap()
        ev["id"] = step.targetId
        if (step.method == StoMethod.BinTask) {
          if (!step.binTask.isNullOrBlank()) ev["binTask"] = step.binTask
        } else if (step.method == StoMethod.Operation) {
          if (!step.operation.isNullOrBlank()) {
            ev["operation"] = step.operation
            if (!step.operationArg.isNullOrBlank()) {
              val argMap = try {
                JsonHelper.mapper.readValue(step.operationArg, MutableMap::class.java)
              } catch (e: Exception) {
                throw BzError("errParseJsonString", step.operationArg)
              }
              for ((key, value) in argMap) {
                if (key == "id" || key == "source_id" || key == "task_id") continue
                ev[key as String] = value
              }
            }
          }
        }
        ev
      },
    )

    StoManager.start(order)

    ctx.status(200)
  }

  private fun handleQueryOrder(ctx: Context) {
    

    val orderId = ctx.pathParam("id")
    val op = StoStore.loadOrder(orderId)
    if (op != null) {
      ctx.json(mapOf("found" to true, "order" to op))
    } else {
      ctx.json(mapOf("found" to false, "order" to null))
    }
  }

  private fun handleCancelCurrentOrder(ctx: Context) {
    

    val req: StoReq = ctx.getReqBody()
    StoManager.cancelCurrentOrder(req.robotName)
    ctx.status(200)
  }

  private fun handleRetryOrder(ctx: Context) {
    

    val req: StoReq = ctx.getReqBody()
    StoManager.retryFailed(req.robotName)
    ctx.status(200)
  }

  private fun handleCancelOrder(ctx: Context) {
    

    val req: StoReq = ctx.getReqBody()
    if (req.orderId == null) throw BzError("errNoOrderId")
    StoManager.cancelOrder(req.robotName, req.orderId)
    ctx.status(200)
  }

  private fun handleManualDoneOrder(ctx: Context) {
    

    val req: StoReq = ctx.getReqBody()
    if (req.orderId == null) throw BzError("errNoOrderId")
    StoManager.manualDoneOrder(req.robotName, req.orderId)
    ctx.status(200)
  }
}

data class StoReq(
  val robotName: String,
  val orderId: String?,
)

data class CreateStoStepReq(
  val robotName: String,
  val steps: List<Step>,
)

data class Step(
  // 序号
  val index: Int,
  // 目标站点
  val targetId: String,
  // 方式: BinTask、Operation
  val method: StoMethod,
  // 动作
  val operation: String? = null,
  // binTask
  val binTask: String? = null,
  // 动作参数
  val operationArg: String? = null,
)

enum class StoMethod {
  BinTask,
  Operation,
}