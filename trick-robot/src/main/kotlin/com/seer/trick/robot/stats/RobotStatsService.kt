package com.seer.trick.robot.stats

import com.seer.trick.BzError
import com.seer.trick.Cq

import com.seer.trick.base.concurrent.BaseConcurrentCenter
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.stats.manila.ManilaReportService
import com.seer.trick.helper.BoolHelper
import com.seer.trick.helper.DateHelper
import com.seer.trick.helper.NumHelper
import com.seer.trick.helper.getTypeMessage
import com.seer.trick.robot.stats.domain.RobotStats
import org.apache.commons.lang3.time.DateUtils
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.BlockingQueue
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.LinkedBlockingQueue
import kotlin.math.round

// TODO 检查相反状态的记录是否存在，比如新建的机器人状态是 idle，是否有 working，避免刚创建机器人时的数据遗漏。
object RobotStatsService {
  private val logger = LoggerFactory.getLogger(javaClass)

  // 当前状态缓存
  // robotName -> {type -> current value}
  private val robotCache: MutableMap<String, MutableMap<RobotStatsType, Any?>> = ConcurrentHashMap()

  /**
   * 待统计的任务
   */
  private val needStatTasks: BlockingQueue<RobotStats> = LinkedBlockingQueue(500)

  // 放到单线程的线程池里执行
  // 防止同时修改多个字段导致的数据异常，
  // 同时 tryRecordRobotStatus 函数中，要按照固定顺序解析上报的字段
  // private val worker = Executors.newSingleThreadExecutor()

  // 仅用于往 worker 中塞 “按小时拆分持续型状态的 RobotPropChangeTimeline 记录” 任务
//  private val scheduler = Executors.newScheduledThreadPool(1)

  // 需要进行切割的类型
  private val splitList = listOf(
    RobotStatsType.Charging,
    RobotStatsType.Idle,
    RobotStatsType.Working,
    RobotStatsType.InError,
    RobotStatsType.Online,
    RobotStatsType.Empty,
  )

  // 需要处理离线是的状态类型
  private val onlineListState = listOf(
    RobotStatsType.ChargeState,
    RobotStatsType.IdleState,
    RobotStatsType.WorkState,
    RobotStatsType.ErrorState,
    RobotStatsType.LoadState,
  )

  // 需要处理不统计类型
  private val onlineList = listOf(
    RobotStatsType.Battery,
  )

  fun init() {
    initRobotCache()
    // 将时长类的切分再状态终完成，一次只需要启动时执行一次就行了
    
    BaseConcurrentCenter.lowTimeSensitiveExecutor.submit {
      asyncExecute {
        try {
          splitFinishedRecord()
        } catch (e: Exception) {
          logger.error(e.getTypeMessage())
        }
      }
      asyncExecute { singleThreadStat() }
    }
  }

//  private fun splitRobotStatRecord(): () -> Unit = {
//  }

  fun dispose() {
    // worker.shutdownNow()
    robotCache.clear()
  }

  /**
   * 初始化机器人缓存
   *
   * 找出所有 finishedOn 为空的行，即未完成的行，（按照 id 默认排序后）放到 robotCache 中
   */
  private fun initRobotCache() {
    // TODO 从数据库中读取原本的未完成的数据 or 后续传新的
    

    reloadRobotChangeRecord(RobotStatsType.Battery)
    reloadRobotChangeRecord(RobotStatsType.Mileage)
    reloadRobotChangeRecord(RobotStatsType.VelocityX)
    reloadRobotChangeRecord(RobotStatsType.VelocityY)
    reloadRobotChangeRecord(RobotStatsType.Velocity)
    reloadRobotChangeRecord(RobotStatsType.RotateVelocity)
    reloadRobotChangeRecord(RobotStatsType.Direction)
    reloadRobotChangeRecord(RobotStatsType.ChargeState)
    reloadRobotChangeRecord(RobotStatsType.WorkState)
    reloadRobotChangeRecord(RobotStatsType.ErrorState)
    reloadRobotChangeRecord(RobotStatsType.OnlineState)
    reloadRobotChangeRecord(RobotStatsType.LoadState)

    // val robotRecords = EntityRwService.findMany("RobotPropChangeTimeline", Cq.empty("finishedOn"))
    // robotRecords.forEach { record -> parseRobotChangeRecord(record) }
  }

  private fun reloadRobotChangeRecord(type: RobotStatsType) {
    val record = EntityRwService.findOne(
      
      "RobotPropChangeTimeline",
      Cq.eq("type", type.name),
      FindOptions(sort = listOf("-id")),
    ) ?: return
    val robotName = record["robotName"] as String? ?: return

    val v = parseValue(record["newValue"], type)
    // 把 oldValue 塞到 robotCache 中
    robotCache.putIfAbsent(robotName, mutableMapOf())
    robotCache[robotName]?.putIfAbsent(type, v)
  }

  // 将数据库中取出的 String 转换成对应的格式
  private fun parseValue(v: Any?, type: RobotStatsType): Any? = when (type) {
    RobotStatsType.Battery,
    RobotStatsType.Mileage,
    RobotStatsType.VelocityX,
    RobotStatsType.VelocityY,
    RobotStatsType.Velocity,
    RobotStatsType.RotateVelocity,
    RobotStatsType.Direction,
    -> NumHelper.anyToDouble(v)

    RobotStatsType.ChargeState,
    RobotStatsType.WorkState,
    RobotStatsType.ErrorState,
    RobotStatsType.OnlineState,
    RobotStatsType.LoadState,
    -> BoolHelper.anyToBool(v)

    else -> null
  }

  /**
   * 记录机器人属性变化
   *
   * 异步执行，不阻塞业务代码
   */
  fun tryRecordRobotStatus(report: RobotStats) {
    if (ManilaReportService.disabled()) return // 禁用统计
    needStatTasks.put(report)
  }

  /**
   * 单线程异步执行统计任务
   */
  private fun singleThreadStat() {
    while (!Thread.interrupted()) {
      val report = needStatTasks.take()
      try {
        // 必须先记录在线状态，否则无法计算故障率、空闲率等占比的分母
        recordRobotChanges(report, RobotStatsType.OnlineState, report.online)
        recordRobotChanges(report, RobotStatsType.Battery, report.battery)
        recordRobotChanges(report, RobotStatsType.Mileage, report.todayOdo)
        // TODO 此类数据会刷屏，暂时不记录。需要清理机制
        // recordRobotChanges(report, RobotStatsType.VelocityX, report.vx)
        // recordRobotChanges(report, RobotStatsType.VelocityY, report.vy)
        // recordRobotChanges(report, RobotStatsType.RotateVelocity, report.w)
        // recordRobotChanges(report, RobotStatsType.Direction, report.direction)
        recordRobotChanges(report, RobotStatsType.ChargeState, report.charging)
        recordRobotChanges(report, RobotStatsType.WorkState, report.working)
        recordRobotChanges(report, RobotStatsType.LoadState, report.isLoaded)
        recordRobotChanges(report, RobotStatsType.ErrorState, report.ifError)
      } catch (e: Throwable) {
        logger.error("tryRecordRobotStatus error", e)
      }
    }
  }

  /**
   * 结束机器人状态变化的统计
   */
  private fun finishRobotStat(robotName: String, recordType: RobotStatsType?, finishedOn: Date) {
    if (recordType == null || !splitList.contains(recordType)) return
    val lastRecord = EntityRwService.findOne(
      
      "RobotPropChangeTimeline",
      Cq.and(
        Cq.eq("type", recordType.name),
        Cq.eq("robotName", robotName),
      ),
      FindOptions(sort = listOf("-id")),
    )
    if (lastRecord == null || lastRecord["finishedOn"] != null) {
      return
    }

    val startedOn = DateHelper.anyToDate(lastRecord["startedOn"]) ?: return
    val duration = finishedOn.time - startedOn.time
    EntityRwService.updateOne(
      
      "RobotPropChangeTimeline",
      Cq.idEq(EntityHelper.mustGetId(lastRecord)),
      mutableMapOf(
        "newValue" to lastRecord["oldValue"],
        "finishedOn" to finishedOn,
        "duration" to duration,
      ),
    )
  }

  /**
   * 尝试记录状态变化
   *
   * 从 robotCache 中取出原本的值，如果能取到，则视为上一状态 lr
   * 如果 lr 的值与 value 相同，则不记录；不同，则记录，并更新 robotCache
   *
   * TODO 周期性的记录，强制记录 change
   *
   * TODO 区分新增还是更新，
   * TODO 需要记录原本的 id，减少再查一次的时间
   *
   * 先持久化再更新内存数据
   *
   */
  private fun recordRobotChanges(robotStats: RobotStats, recordType: RobotStatsType, nv: Any) {
    val robotName = robotStats.robot
    val map = robotCache[robotName] ?: mutableMapOf()
    val now = Date()
    splitByHour(robotName, recordType.persistType, now) // 切分时长类型
    splitByHour(robotName, recordType.reversePersistType, now) // 切分时长类型
    if (!robotStats.online && onlineListState.contains(recordType)) { // 时长类型的统计需要结束
      finishRobotStat(robotName, recordType.persistType, now)
      finishRobotStat(robotName, recordType.reversePersistType, now)
      finishRobotState(robotName, recordType, now)
      map.remove(recordType)
      return
    }

    if (!robotStats.online && onlineList.contains(recordType)) {
      return
    }

    val ov = map[recordType]
    if (ov == nv) {
      // TODO 空闲时长与工作时长计算不对，比如一开始就是空闲，状态没有发生变化则空闲时长就会一直是 0
      // 值相同，不记录
      return
    }

    // 值不同，持久化
    when (recordType) {
      // 实时状态
      RobotStatsType.Battery,
      RobotStatsType.VelocityX, RobotStatsType.VelocityY, RobotStatsType.RotateVelocity,
      -> {
        val delta = round(((NumHelper.anyToDouble(nv) ?: 0.0) - (NumHelper.anyToDouble(ov) ?: 0.0)) * 1e3) / 1e3
        createChangeRecord(robotName, recordType, ov, nv, delta, now, now)
      }

      RobotStatsType.Direction -> {
        val delta = round(((NumHelper.anyToDouble(nv) ?: 0.0) - (NumHelper.anyToDouble(ov) ?: 0.0)) * 1e3) / 1e3
        if (!(Math.toRadians(15.0) < delta || delta < Math.toRadians(-15.0))) { // ±15° 范围不记录，滤波
          return // 不更新旧值
        }
        createChangeRecord(robotName, recordType, ov, nv, delta, now, now)
      }

      RobotStatsType.Mileage -> {
        val delta = round(((NumHelper.anyToDouble(nv) ?: 0.0) - (NumHelper.anyToDouble(ov) ?: 0.0)) * 1e3) / 1e3
        if (delta < 2) { // 同名机器人，里程数不同导致负数，过滤负数 滤波大于 2m 的时候才记录
          return
        }
        createChangeRecord(robotName, recordType, ov, nv, delta, now, now)
      }
      // 持续状态 - 标准
      RobotStatsType.WorkState, RobotStatsType.ErrorState, RobotStatsType.OnlineState, RobotStatsType.LoadState,
      RobotStatsType.IdleState,
      -> {
        if (recordType.persistType == null) {
          throw BzError("errRobotStatsType", "$recordType is not persist type")
        }
        // 创建实时状态变化记录 oldValue -> newValue
        createChangeRecord(robotName, recordType, ov, nv, null, now, now)
        if (nv == true) {
          // 标记持续状态的记录开始 oldValue, startedOn
          createStateRecord(robotName, recordType.persistType, nv, now)
          // 更新相反状态的记录
          if (recordType.reversePersistType != null) {
            updateStateRecord(robotName, recordType.reversePersistType, ov, false, now) { _, _ -> null }
          }
        } else {
          // 标记持续状态的记录结束 newValue, finishedOn, delta, duration
          // 工作状态、报错状态、在线状态的 delta 没啥用，直接返回 null
          updateStateRecord(robotName, recordType.persistType, ov, nv, now) { _, _ -> null }
          // 创建相反状态
          if (recordType.reversePersistType != null) {
            // TODO 目前有反向状态的值都是布尔值
            createStateRecord(robotName, recordType.reversePersistType, true, now)
          }
        }
      }

      // 持续状态 - 充电状态
      RobotStatsType.ChargeState -> {
        if (recordType.persistType == null) {
          throw BzError("errRobotStatsType", "$recordType is not persist type")
        }

        // 创建实时状态变化记录 oldValue -> newValue
        createChangeRecord(robotName, recordType, ov, nv, null, now, now)
        if (nv == true) {
          // 标记持续状态的记录开始 oldValue, startedOn
          createStateRecord(robotName, recordType.persistType, robotStats.battery, now)
        } else {
          // 标记持续状态的记录结束 newValue, finishedOn, delta, duration
          updateStateRecord(robotName, recordType.persistType, robotStats.battery, ov, now) { o, n ->
            // 电量变化值。先把 String 转成 Double
            round(((NumHelper.anyToDouble(n) ?: 0.0) - (NumHelper.anyToDouble(o) ?: 0.0)) * 1e3) / 1e3
          }
        }
      }

      else -> {
        logger.warn("暂不支持解析的 RobotStatsType: $recordType")
      }
    }

    // 值变了，记录到内存
    map[recordType] = nv
    robotCache[robotName] = map
  }

  /**
   * 对于记录单点状态的 record，创建 record，并传 oldValue, newValue, delta, finishedOn
   */
  private fun createChangeRecord(
    
    robotName: String,
    recordType: RobotStatsType,
    oldValue: Any?,
    newValue: Any?,
    delta: Any?,
    startedOn: Date,
    finishedOn: Date,
  ) {
    EntityRwService.createOne(
      
      "RobotPropChangeTimeline",
      mutableMapOf(
        "robotName" to robotName,
        "type" to recordType,
        "oldValue" to oldValue,
        "newValue" to newValue,
        "delta" to delta,
        "startedOn" to startedOn,
        "finishedOn" to finishedOn,
      ),
    )
  }

  /**
   * 对于要记录开始和结束的 record，创建 startedOn 的记录，传入 oldValue, startedOn
   */
  private fun createStateRecord(
    
    robotName: String,
    recordType: RobotStatsType,
    nv: Any?,
    startedOn: Date,
  ) {
    EntityRwService.createOne(
      
      "RobotPropChangeTimeline",
      mutableMapOf(
        "robotName" to robotName,
        "type" to recordType,
        "oldValue" to nv,
        "startedOn" to startedOn,
        "duration" to 0,
        "percentageBase" to 1.0,
      ),
    )
  }

  // 对于要记录开始和结束的 record，更新 finishedOn，传入 newValue, finishedOn，计算 delta，duration
  private fun updateStateRecord(
    
    robotName: String,
    recordType: RobotStatsType,
    ov: Any?,
    newValue: Any?,
    finishedOn: Date,
    deltaFun: (ov: Any?, nv: Any?) -> Any?,
  ) {
    val lastRecord = EntityRwService.findOne(
      
      "RobotPropChangeTimeline",
      Cq.and(
        Cq.eq("type", recordType.name),
        Cq.eq("robotName", robotName),
      ),
      FindOptions(sort = listOf("-id")),
    )
    if (lastRecord == null) {
      logger.warn(
        "未找到匹配的 RobotPropChangeTimeline 状态变化记录，robotName=$robotName recordType=${recordType.name}",
      )
      return
    }
    val lastRecordId = EntityHelper.mustGetId(lastRecord)
    // 没有旧值则说明是刚连接、刚启动、刚完成上一个周期的打点的情况之一
    finishedOnlineRecord(robotName, ov, recordType, finishedOn)
    val startedOn = DateHelper.anyToDate(lastRecord["startedOn"])
    val duration = if (startedOn != null) {
      finishedOn.time - startedOn.time
    } else {
      null
    }
    val oldValue = lastRecord["oldValue"]
    val delta = deltaFun(oldValue, newValue)

    EntityRwService.updateOne(
      
      "RobotPropChangeTimeline",
      Cq.idEq(lastRecordId),
      mutableMapOf(
        "newValue" to newValue,
        "delta" to delta,
        "finishedOn" to finishedOn,
        "duration" to duration,
      ),
    )
  }

  /**
   * 闭合在线记录周期报表解决查询在线、工作时长不一致的问题
   */
  private fun finishedOnlineRecord(
    
    robotName: String,
    ov: Any?,
    recordType: RobotStatsType,
    finishedOn: Date,
  ) {
    if (!(ov != null && recordType != RobotStatsType.Online)) return

    val lastRecord = EntityRwService.findOne(
      
      "RobotPropChangeTimeline",
      Cq.and(
        Cq.eq("type", RobotStatsType.Online),
        Cq.eq("robotName", robotName),
      ),
      FindOptions(sort = listOf("-id")),
    ) ?: return

    val startedOn = DateHelper.anyToDate(lastRecord["startedOn"]) ?: return
    val diffTime = (finishedOn.time - startedOn.time).takeIf { it >= 0 } ?: return

    EntityRwService.updateOne(
      
      "RobotPropChangeTimeline",
      Cq.idEq(EntityHelper.mustGetId(lastRecord)),
      mutableMapOf(
        "finishedOn" to finishedOn,
        "duration" to diffTime,
      ),
    )

    EntityRwService.createOne(
      
      "RobotPropChangeTimeline",
      mutableMapOf(
        "robotName" to robotName,
        "type" to RobotStatsType.Online,
        "oldValue" to (lastRecord["newValue"] ?: lastRecord["oldValue"]),
        "startedOn" to finishedOn,
        "duration" to 0,
      ),
    )
  }

  // 拆分已完成的持续型的机器人状态变化记录
  // 如 14:58:30 - 15:01:30 的一个工作状态，需要拆成 14:58:30 - 14:59:30 和 14:59:30 - 15:01:30
  private fun splitFinishedRecord() {
    // 只处理近 3 天的。
    val fromDate = DateUtils.addDays(Date(), -3)

    val records = EntityRwService.findMany(
      
      "RobotPropChangeTimeline",
      Cq.and(
        Cq.gt("startedOn", fromDate),
        Cq.include(
          "type",
          listOf(
            RobotStatsType.Charging,
            RobotStatsType.Idle,
            RobotStatsType.Working,
            RobotStatsType.InError,
            RobotStatsType.Online,
            RobotStatsType.Empty,
          ),
        ),
        Cq.isNotNull("finishedOn"),
      ),
    )
    val now = Date()
    for (record in records) {
      val startedOn = DateHelper.anyToDate(record["startedOn"]) ?: now // 一定有 startedOn，这里的 now 仅用于通过类型校验
      val finishedOn = DateHelper.anyToDate(record["finishedOn"]) ?: now
      val startList = DateHelper.hoursBetween(startedOn, finishedOn)
      // 如果中间跨小时了，记录最终的状态，更新原记录的 finishedOn、duration
      // TODO 要考虑 Idle 过程中 M4 关闭的情况
      val newValue = record["newValue"]
      val oldValue = record["oldValue"]
      if (startList.isNotEmpty()) {
        val duration = startList.first().time - startedOn.time
        EntityRwService.updateOne(
          
          "RobotPropChangeTimeline",
          Cq.eq("id", record["id"]),
          mutableMapOf(
            "finishedOn" to Date(startList.first().time - 1),
            "duration" to duration,
            "newValue" to oldValue,
          ),
        )
      }
      // 拆开
      for ((i, start) in startList.withIndex()) {
        if (start != startList.last()) {
          record.remove("id")
          record["startedOn"] = start
          record["finishedOn"] = Date(startList[i + 1].time - 1)
          record["duration"] = startList[i + 1].time - start.time
          record["newValue"] = oldValue
          EntityRwService.createOne("RobotPropChangeTimeline", record)
        } else {
          // 如果是最后一个，更新记录的 finishedOn、duration
          record.remove("id")
          record["startedOn"] = startList.last()
          record["finishedOn"] = finishedOn
          record["duration"] = finishedOn.time - startList.last().time + 1
          record["newValue"] = newValue
          EntityRwService.createOne("RobotPropChangeTimeline", record)
        }
      }
    }
  }

  /**
   * 时长类的统计需要按照时间切分，比如：在线时长，如果一天都在线，在线没有变化，就不会更新值的变化
   * 统计查询今天的在线时间就无法统计，因此就需要将这种类型的的统计按照小时进行切割
   * 涉及到的统计类型有如下：Charging,Idle,Working,InError,Online,Empty
   */
  private fun splitByHour(robotName: String, recordType: RobotStatsType?, finishedOn: Date) {
    if (recordType == null || !splitList.contains(recordType)) return
    val lastRecord = EntityRwService.findOne(
      
      "RobotPropChangeTimeline",
      Cq.and(
        Cq.eq("type", recordType.name),
        Cq.eq("robotName", robotName),
      ),
      FindOptions(sort = listOf("-id")),
    )
    if (lastRecord == null) return
    if (lastRecord["finishedOn"] != null) return
    val startedOn = DateHelper.anyToDate(lastRecord["startedOn"]) ?: return
    val startList = DateHelper.hoursBetween(startedOn, finishedOn)
    if (startList.isEmpty()) return // 不跨不需要切分
    val splitDate = DateHelper.nextHourStart(startedOn)
    // 切分数据
    val duration = splitDate.time - startedOn.time // 均为闭区间
    EntityRwService.updateOne(
      
      "RobotPropChangeTimeline",
      Cq.idEq(EntityHelper.mustGetId(lastRecord)),
      mutableMapOf(
        "newValue" to lastRecord["oldValue"],
        "finishedOn" to Date(splitDate.time - 1), // 00:00:00.000 - 00:59:59.999 其持续时间视为 1 小时整 = 3600 * 1000 ms
        "duration" to duration,
      ),
    )
    lastRecord.remove("id")
    // 超过一个小时的基础数据没改变认为那段时间宕机或者没启用统计，统计时长的现在按小时切分了，同类型数据不可能间隔两小时没有数据
    lastRecord["startedOn"] = if (startList.size > 1) {
      finishedOn
    } else {
      splitDate
    }
    EntityRwService.createOne("RobotPropChangeTimeline", lastRecord)
  }

  private fun finishRobotState(robotName: String, recordType: RobotStatsType, finishedOn: Date) {
    val lastRecord = EntityRwService.findOne(
      
      "RobotPropChangeTimeline",
      Cq.and(
        Cq.eq("robotName", robotName),
        Cq.eq("type", recordType.name),
      ),
      FindOptions(sort = listOf("-id")),
    )
    if (lastRecord == null || lastRecord["newValue"] == null) {
      return
    }
    lastRecord.remove("id")
    lastRecord["startedOn"] = finishedOn
    lastRecord["finishedOn"] = finishedOn
    lastRecord["oldValue"] = lastRecord.remove("newValue")
    EntityRwService.createOne("RobotPropChangeTimeline", lastRecord)
  }

  /**
   * 拆开未完成的持续型的机器人状态变化记录
   * 如果跨小时，就按小时拆开
   * 拆分时，更新最后一条不带 finishedOn 的 newValue（同 oldValue）、duration、finishedOn（闭区间），并创建一条新的不带 finishedOn 的记录
   *
   * @param tc 上下文
   * @param endCurrentPeriod 是否结束当前周期的统计
   */
//  private fun splitUnfinishedRecord(types: List<RobotStatsType>, endCurrentPeriod: Boolean = false) {
//    val records = EntityRwService.findMany(
//      
//      "RobotPropChangeTimeline",
//      Cq.and(
//        Cq.include("type", types),
//        Cq.isNull("finishedOn"),
//      ),
//    )
//
//    // TODO 无法处理 M4 关闭的场景
//    //  dispose 中加处理不太能满足需求，断电，关闭 cmd 窗口等均执行不完
//    //  目前想的比较好的方式是加一项目 M4 在线时间，精度 1-5 mins，然后所有持续时间类型的数据，都要减去当前的 M4 离线时间。减到 0 就视为 0。
//    //  需要注意，M4 关闭时间有可能在 Idle 持续中，也可能不在 Idle 内。所以即可能要减去 M4 离线时间，也可能不减……
//    val now = Date()
//    for (record in records) {
//      val robotName = record["robotName"] as String? ?: ""
//      val type = record["type"] as String? ?: ""
//      val startedOn = DateHelper.anyToDate(record["startedOn"]) ?: now // 一定有 startedOn，这里的 now 仅用于通过类型校验
//
//      // 是同一小时的话，返回空 list
//      val startList = DateHelper.hoursBetween(startedOn, now).toMutableList()
//      // 结束当前周期的统计
//      if (endCurrentPeriod) {
//        startList.add(now)
//      }
//      for ((index, start) in startList.withIndex()) { // start 是中间每小时的开始时间 xx:00:00.000
//        // 先更新 finishedOn、duration
//        // 后创建一条线的 RobotPropChangeTimeline，与原本的分开
//        val lastRecord = EntityRwService.findOne(
//          
//          "RobotPropChangeTimeline",
//          Cq.and(
//            Cq.eq("robotName", robotName),
//            Cq.eq("type", type),
//            Cq.isNull("finishedOn"),
//          ),
//        )
//        if (lastRecord != null) {
//          val recordId = EntityHelper.mustGetId(lastRecord)
//          val end = Date(start.time - 1)
//          // 持续时间 duration
//          // 00:00:00.000 - 00:59:59.999 其持续时间视为 1 小时整 = 3600 * 1000 ms
//          // startedOn、finishedOn 均为闭区间
//          val duration = end.time - (DateHelper.anyToDate(lastRecord["startedOn"]) ?: startedOn).time + 1
//          EntityRwService.updateOne(
//            
//            "RobotPropChangeTimeline",
//            Cq.eq("id", recordId),
//            mutableMapOf("finishedOn" to end, "duration" to duration, "newValue" to lastRecord["oldValue"]),
//          )
//        }
//        // 若是最后一条数据，则不创建下一个周期
//        if (index == startList.lastIndex) {
//          // 清除缓存，下一个周期由处理状态的逻辑来创建
//          robotCache.remove(robotName)
//        } else {
//          record.remove("id")
//          record.remove("finishedOn")
//          record["startedOn"] = start
//          EntityRwService.createOne("RobotPropChangeTimeline", record)
//        }
//      }
//    }
//  }

  private fun asyncExecute(p: () -> Unit) = BaseConcurrentCenter.statsExecutor.run { p() }
}

/**
 * 机器人状态变化类型
 *
 * b：
 *  - true： 持久的状态（如充电）
 *  - false： 实时的状态（如电量）
 *
 * persistType：
 *  实时状态关联的持久化状态，
 *  比如充电状态变化时，需要记录 2 条：
 *   - 一条是充电状态从 true 变为 false / false 变为 true；
 *   - 另一条是整个的充电过程的开始、结束，开始充电时，创建一条新的充电记录 r，充电完成后标记 r 完成并更新 finishedOn、duration 等。
 *
 * reversePersistType：
 *   持久化状态的反向状态，如 “工作中” 的相反状态是 “空闲中”
 *
 */
enum class RobotStatsType(
  val b: Boolean = false,
  val persistType: RobotStatsType? = null,
  val reversePersistType: RobotStatsType? = null,
) {
  Battery, // 电量
  Mileage, // 里程
  VelocityX, // x 方向速度
  VelocityY, // y 方向速度
  Velocity, // 速度
  RotateVelocity, // 旋转速度
  Direction, // 朝向？转向次数？
  Charging(true), // 充电中
  ChargeState(persistType = Charging), // 充电状态

  Idle, // 空闲中
  IdleState(persistType = Idle), // 空闲状态变化

  Working(true), // 工作中
  WorkState(persistType = Working, reversePersistType = Idle), // 工作状态变化

  InError(true), // 故障中
  ErrorState(persistType = InError), // 故障状态变化

  Online(true), // 在线
  OnlineState(persistType = Online), // 在线状态变化

  Empty, // 空载
  Loaded(true), // 载货
  LoadState(persistType = Loaded, reversePersistType = Empty), // 负载状态变化
}