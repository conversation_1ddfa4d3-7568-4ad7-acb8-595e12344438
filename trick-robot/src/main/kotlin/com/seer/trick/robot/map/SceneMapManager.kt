package com.seer.trick.robot.map

import com.seer.trick.BzError

import com.seer.trick.base.BaseCenter
import com.seer.trick.base.log.SystemKeyEvent
import com.seer.trick.base.log.SystemKeyEventService
import com.seer.trick.helper.JsonFileHelper
import com.seer.trick.robot.RobotAppSceneConfig
import com.seer.trick.robot.rachel.MrRobotSelfReportMain
import com.seer.wcs.GeoHelper
import com.seer.wcs.ShortestPath
import org.slf4j.LoggerFactory
import java.io.File
import java.util.*

/**
 * 移动机器人场景地图管理
 */
class SceneMapManager(private val sceneConfig: RobotAppSceneConfig) {

  private val logger = LoggerFactory.getLogger(this::class.java)

  @Volatile
  var sceneMapRuntime: SceneMapRuntime = SceneMapRuntime(MrSceneMap())
    private set

  @Synchronized
  fun load() {
    val newScene: MrSceneMap? = JsonFileHelper.readJsonFromFile(getMapFile())
    if (newScene != null) this.sceneMapRuntime = SceneMapRuntime(newScene)

    buildCache()
  }

  @Synchronized
  fun update(sm: MrSceneMap) {
    logger.info("修改场景")
    val old = sceneMapRuntime.sceneMap
    val sm2 = sm.copy(version = old.version + 1, lastModified = Date())
    sceneMapRuntime = SceneMapRuntime(sm2)
    JsonFileHelper.writeJsonToFile(getMapFile(), sm2, true)

    SystemKeyEventService.record(SystemKeyEvent(group = "Robot", title = "修改场景"))

    // 会重新构建缓存
    buildCache()
  }

  private fun buildCache() {
    // TODO
  }

  private fun getMapFile(): File {
    // TODO 去掉特殊字符
    val sceneName = sceneConfig.name
    return File(getMapConfigDir(), "$sceneName.scene.json")
  }

  /**
   * 默认 10cm 内
   */
  fun mustGetClosestSite(x: Double, y: Double, close: Double = 0.1): AreaSite {
    return getClosestSite(x, y, close) ?: throw BzError("errPositionToNoSite", x, y)
  }

  /**
   * 默认 10cm 内
   */
  private fun getClosestSite(x: Double, y: Double, close: Double = 3.0): AreaSite? {
    var min = Double.MAX_VALUE
    var minSite: AreaSite? = null

    for (area in sceneMapRuntime.sceneMap.areas) {
      for (site in area.sites) {
        val d = GeoHelper.euclideanDistance(x, y, site.x, site.y)
        if (d < min && d < close) {
          min = d
          minSite = AreaSite(area, site)
        }
      }
    }

    return minSite
  }

  /**
   * 获取机器人位置。如果 siteId 为空，尝试根据 x,y 找最近站点
   */
  private fun getSceneLocation(siteId: String?, x: Double, y: Double): SceneLocation? {
    if (!siteId.isNullOrBlank()) {
      val index = sceneMapRuntime.siteIdToIndexMap[siteId] ?: return null
      return SceneLocation(sceneMapRuntime.areaIdToIndexMap[index.areaId]!!.area, index.site, x, y)
    } else {
      val site = getClosestSite(x, y) ?: return null
      return SceneLocation(site.area, site.site, x, y)
    }
  }

  fun getSceneLocation(main: MrRobotSelfReportMain?): SceneLocation? {
    return if (main?.x != null && main.y != null) {
      getSceneLocation(main.currentSite, main.x, main.y)
    } else null
  }

  fun findBestStart(main: MrRobotSelfReportMain): AreaSite? {
    val currentSite = main.currentSite
    if (!currentSite.isNullOrBlank()) {
      val index = sceneMapRuntime.siteIdToIndexMap[currentSite] ?: return null
      return AreaSite(sceneMapRuntime.areaIdToIndexMap[index.areaId]!!.area, index.site)
    }

    if (main.x == null || main.y == null) return null
    return getClosestSite(main.x, main.y)
  }

  fun getAreaByAnyName(name: String): MrSceneArea? {
    return sceneMapRuntime.sceneMap.areas.find { it.id == name || it.name == name || it.mapName == name }
  }

  /**
   * 带缓存；不通返回 Double.MAX_VALUE
   */
  fun calcCost(startSiteId: String?, endSiteId: String?): Double? {
    if (startSiteId.isNullOrBlank() || endSiteId.isNullOrBlank()) return null
    val r = sceneMapRuntime.defaultAlg.g.getPath(startSiteId, endSiteId)
    var cost = r?.weight
    if (cost == null || cost == Double.POSITIVE_INFINITY || cost == Double.NEGATIVE_INFINITY) cost = Double.MAX_VALUE
    return cost
  }

  fun getShortestPath(startSiteId: String?, endSiteId: String?): ShortestPath? {
    if (startSiteId.isNullOrBlank() || endSiteId.isNullOrBlank()) return null
    return sceneMapRuntime.defaultAlg.getShortestPath(startSiteId, endSiteId)
  }

  fun remove() {
    logger.info("删除场景")
    val mapFile = getMapFile()
    if (mapFile.exists()) {
      mapFile.delete()
      SystemKeyEventService.record(SystemKeyEvent(group = "Robot", title = "删除场景"))
    }
  }

  companion object {

    private fun getMapConfigDir(): File {
      val dir = File(BaseCenter.baseConfig.configDir, "map")
      if (!dir.exists()) dir.mkdirs()
      return dir
    }

  }

}
