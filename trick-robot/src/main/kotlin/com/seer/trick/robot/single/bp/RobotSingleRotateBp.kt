package com.seer.trick.robot.single.bp


import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockOutputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper
import java.math.BigDecimal
import java.math.RoundingMode

class RobotSingleRotateBp : AbstractTaskChainBp() {

  override fun process() {
    val angle = mustGetBlockInputParam("angle") as Double
    val vw = getBlockInputParam("vw") as Double? ?: 45.0
    val mode = getBlockInputParam("mode") as String? ?: "0"
    val reqId = getBlockInputParam("reqId") as String? ?: IdHelper.uuidStr()

    val degrees180 = BigDecimal("180")
    val pi = BigDecimal(Math.PI.toString())

    val req = JsonHelper.mapper.writeValueAsString(
      mapOf(
        "angle" to pi.multiply(BigDecimal(angle)).divide(degrees180, 2, RoundingMode.HALF_UP).toDouble(),
        "vw" to pi.multiply(BigDecimal(vw)).divide(degrees180, 2, RoundingMode.HALF_UP).toDouble(),
        "mode" to Integer.parseInt(mode)
      )
    )
    val gwRbkResult = executeTask(RbkRequest(reqId, 3056, req, "转动"))
    setBlockOutputParams(mapOf("rbkResult" to gwRbkResult))
  }

  companion object {

    val def = BlockDef(
      RobotSingleRotateBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(
        BlockInputParamDef("angle", BlockParamType.Double, true, defaultValue = 90.0),
        BlockInputParamDef("vw", BlockParamType.Double, false, defaultValue = 45.0),
//        BlockInputParamDef(
//          "mode", BlockParamType.String, options = listOf(
//            BlockInputParamOption("0"), BlockInputParamOption("1")
//          )
//        ),
//        BlockInputParamDef("reqId", BlockParamType.String),
      ),
      outputParams = listOf(
        BlockOutputParamDef("rbkResult", BlockParamType.String)
      )
    )

  }

}