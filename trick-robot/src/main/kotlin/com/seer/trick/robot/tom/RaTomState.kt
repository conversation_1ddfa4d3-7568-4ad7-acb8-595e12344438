package com.seer.trick.robot.tom


import com.seer.trick.base.failure.FailureLevel
import com.seer.trick.base.failure.FailureRecordReq
import com.seer.trick.base.failure.FailureRecorder
import com.seer.trick.base.soc.SocAttention
import com.seer.trick.base.soc.SocService
import com.seer.trick.helper.getTypeMessage
import com.seer.trick.robot.RobotAppSceneRuntime
import com.seer.trick.robot.RobotStatsManager
import com.seer.trick.robot.rachel.MrRobotAlert
import com.seer.trick.robot.rachel.MrRobotAlertLevel
import com.seer.trick.robot.rachel.MrRobotSelfReportMain
import java.util.concurrent.Executors
import java.util.concurrent.Future

/**
 * 调度（CORE）实时获取状态。
 * 定时读取机器人状态。
 */
class RaTomState(
  private val sceneName: String,
  private val tomUrlRoot: String,
  private val sceneRuntime: RobotAppSceneRuntime,
  private val onMapMd5: (md5: String) -> Unit,
  private val onStateUpdate: ((tr: TomRuntimeRecord) -> Unit)? = null,
) {

  private val stateExecutor = Executors.newSingleThreadExecutor()

  @Volatile
  private var stateWorker: Future<*>? = null

  @Volatile
  private var stateFetching = false

  @Volatile
  var tr: TomRuntimeRecord? = null
    private set

  fun init() {
    stateWorker = stateExecutor.submit { fetchStateLoop() }
  }

  fun dispose() {
    stateWorker?.cancel(true)
    stateWorker = null
    SocService.removeNode("RmTom:State")
  }

  private fun fetchStateLoop() {
    
    while (!Thread.interrupted()) {
      try {
        fetchState()
        Thread.sleep(500)
      } catch (e: InterruptedException) {
        return
      }
    }
  }

  private fun fetchState() {
    if (stateFetching) return
    stateFetching = true

    updateSocState("获取状态...")

    val record = try {
      TomAgent.fetchTomRobotStatus(sceneName, tomUrlRoot)
    } catch (e: Throwable) {
      if (e is InterruptedException) throw e
      TomRuntimeRecord(sceneName, tomUrlRoot, error = true, errorMsg = e.getTypeMessage())
    } finally {
      stateFetching = false
    }

    tr = record

    // 检查 core 的错误信息
    recordFailures(record.alarms, sceneName, "CoreAlarm", "Scene")
    // 检查机器人自身的错误信息
    for (robot in record.robots) {
      robot.mainReport?.alerts?.let {
        recordFailures(it, robot.id, "RobotAlarm", "RobotSelf")
      }
    }

    if (record.error) {
      updateSocState("获取状态失败：" + record.errorMsg, SocAttention.Red)
    } else {
      updateSocState("获取状态成功")

      RobotStatsManager.transferTomToRobotStats(record)
      updateRobotAlarms(record.robots)

      onMapMd5(record.sceneMd5)
    }

    onStateUpdate?.invoke(record)
  }

  /**
   * 记录 Error、Fatal 级别的故障到故障记录中
   */
  private fun recordFailures(alerts: List<MrRobotAlert>, id: String, kind: String, subKind: String) {
    val errAlerts = alerts.filter { it.level != MrRobotAlertLevel.Info }
    if (errAlerts.isEmpty()) return
    val timestampRegex = """\[\d{4}-\d{2}-\d{2}T[^]]*]""".toRegex()
    for (a in errAlerts) {
      val level = when (a.level) {
        MrRobotAlertLevel.Fatal -> FailureLevel.Fatal
        MrRobotAlertLevel.Error -> FailureLevel.Error
        MrRobotAlertLevel.Warning -> FailureLevel.Warning
        else -> continue
      }
      val req = FailureRecordReq(
        kind = kind,
        subKind = subKind,
        level = level,
        part = id,
        desc = "[${a.code}] ${a.message.replaceFirst(timestampRegex, "")}",
      )
      FailureRecorder.addAsync(req)
    }
  }

  private fun updateRobotAlarms(robots: List<TomRobotRecord>) {
    val offlineRobots = robots.filter { !it.online }.map { it.id }
    val mains: MutableMap<String, MrRobotSelfReportMain?> = mutableMapOf()
    for (robot in robots) {
      if (!robot.mainReport?.alerts.isNullOrEmpty()) mains[robot.id] = robot.mainReport
    }
    sceneRuntime.updateRobotAlarms(offlineRobots, mains)
  }

  private fun updateSocState(desc: String, attention: SocAttention = SocAttention.None) {
    SocService.updateNode("机器人", "RmTom:State", "调度状态:$sceneName", desc, attention)
  }
}