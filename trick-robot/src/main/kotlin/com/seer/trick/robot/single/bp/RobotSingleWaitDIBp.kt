package com.seer.trick.robot.single.bp


import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockInputParamOption
import com.seer.trick.falcon.domain.BlockOutputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper

class RobotSingleWaitDIBp : AbstractTaskChainBp() {
  override fun process() {
    val status = mustGetBlockInputParam("status") as String
    val id = mustGetBlockInputParam("id") as Long
    val timeout = getBlockInputParamAsLong("timeout")

    val args: MutableMap<String, Any> = mutableMapOf(
      "DI" to listOf(
        mapOf(
          "id" to id,
          "status" to (status == "status_1")
        )
      )
    )
    if (timeout != null) args["timeout"] = timeout

    val req = JsonHelper.mapper.writeValueAsString(
      mapOf(
        "operation" to "WaitDI",
        "args" to args
      )
    )
    val gwRbkResult = executeTask(RbkRequest(IdHelper.uuidStr(), 3051, req, "等待触发 DI"))
    setBlockOutputParams(mapOf("rbkResult" to gwRbkResult))
  }

  companion object {

    val def = BlockDef(
      RobotSingleWaitDIBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(
        BlockInputParamDef("id", BlockParamType.Long, true),
        BlockInputParamDef(
          "status",
          BlockParamType.String,
          true,
          options = listOf(
            BlockInputParamOption("status_1"),
            BlockInputParamOption("status_0")
          ),
          defaultValue = "status_1"),
        BlockInputParamDef("timeout", BlockParamType.Long, false)
      ),
      outputParams = listOf(
        BlockOutputParamDef("rbkResult", BlockParamType.String)
      )
    )

  }
}