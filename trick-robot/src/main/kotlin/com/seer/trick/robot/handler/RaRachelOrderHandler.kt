package com.seer.trick.robot.handler

import com.seer.trick.BzError

import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.getReqBody

import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.robot.RobotAppManager
import com.seer.trick.robot.rachel.*
import io.javalin.http.Context

/**
 * 三代调度运单接口
 */
object RaRachelOrderHandler {
  
  fun registerHandlers() {
    val c = Handlers("api/robot/rachel")
    c.post("create-order", ::createOrder, auth())
    c.post("cancel-order", ::cancelOrder, auth())
    c.post("retry-failed-robot", ::retryFailedRobot, auth())
    c.post("reset-robot", ::resetRobot, auth())
  }
  
  private fun createOrder(ctx: Context) {
    
    val req: CreateOrderReq = ctx.getReqBody()
    
    val ra = RobotAppManager.mustGetScene(req.scene)
    val rachel = ra.rachel!!
    
    val orderId = rachel.orderService.generateOrderId()
    
    val ows = req.toOrder(orderId)
    rachel.orderService.createOrders(listOf(ows))
    
    ctx.json(CreateOrderRes(orderId = orderId, reqId = req.reqId))
  }
  
  private fun cancelOrder(ctx: Context) {
    
    val req: CancelOrderReq = ctx.getReqBody()
    
    val ra = RobotAppManager.mustGetScene(req.scene)
    val rachel = ra.rachel!!
    
    rachel.orderCancelService.cancelOrder(req.orderId)
    ctx.status(200)
  }
  
  private fun retryFailedRobot(ctx: Context) {
    val req: RetryFailedRobotReq = ctx.getReqBody()
    
    val ra = RobotAppManager.mustGetScene(req.scene)
    val rachel = ra.rachel!!
    
    val rr = rachel.robots[req.robotName] ?: throw BzError("errNoRobot", req.robotName)
    if (rr.cmdStatus != MrRobotCmdStatus.Failed) throw BzError("errRobotNotFailed", rr.id)
    
    rachel.retryFailedService.retryFailedRobot(rr)
    
    ctx.status(200)
  }
  
  private fun resetRobot(ctx: Context) {
    
    val req: ResetRobotReq = ctx.getReqBody()
    
    val ra = RobotAppManager.mustGetScene(req.scene)
    val rachel = ra.rachel!!
    
    val rr = rachel.robots[req.robotName] ?: throw BzError("errNoRobot", req.robotName)
    rachel.orderService.resetRobot(rr)
    
    ctx.status(200)
  }
  
  /**
   * 搬运任务
   */
  data class CreateOrderReq(
    val scene: String,
    val reqId: String, // 请求 ID
    val status: MrOrderStatus = MrOrderStatus.Building, // 状态
    val priority: Int = 0, // 优先级
    val expectedRobotNames: List<String>? = null, // 期望以下机器人执行
    val expectedRobotGroups: List<String>? = null, // 期望以下机器人组执行
    val containerId: String? = null, // 搬运的容器编号
    val keySites: List<String>? = null, // 关键点位
    val stepFixed: Boolean = false, // 不再追加新的步骤
    val steps: List<CreateStepReq> = emptyList()
  ) {
    
    fun toOrder(orderId: String): MrOrderWithSteps {
      val order = MrOrder(
        id = orderId,
        reqId = reqId,
        status = status,
        priority = priority,
        expectedRobotNames = expectedRobotNames,
        expectedRobotGroups = expectedRobotGroups,
        containerId = containerId,
        keySites = keySites,
        stepFixed = stepFixed,
        stepNum = steps.size
      )
      val steps = this.steps.mapIndexed { index, step ->
        step.toStep(orderId, index)
      }
      return MrOrderWithSteps(order, steps)
    }
    
  }
  
  /**
   * 搬运任务中的一步
   */
  data class CreateStepReq(
    val status: MrStepStatus = MrStepStatus.Building,
    val location: LocationFeature, // 作业位置
    val operation: String? = null, // 机器人动作
    val operationArgs: Any? = null, // 动作参数
    val forLoad: Boolean = false, // 在此步取货
    val forUnload: Boolean = false, // 在此步卸货
  ) {
    
    fun toStep(orderId: String, index: Int): MrStep {
      return MrStep(
        id = IdHelper.oidStr(),
        orderId = orderId,
        stepIndex = index,
        status = status,
        location = location,
        rbkArgs = JsonHelper.writeValueAsString(operationArgs),
        forLoad = forLoad,
        forUnload = forUnload
      )
    }
    
  }
  
  data class CreateOrderRes(
    val orderId: String,
    val reqId: String,
  )
  
  data class CancelOrderReq(
    val scene: String,
    val orderId: String,
  )
  
  data class RetryFailedRobotReq(
    val scene: String,
    val robotName: String
  )
  
  data class ResetRobotReq(
    val scene: String,
    val robotName: String
  )
  
}

