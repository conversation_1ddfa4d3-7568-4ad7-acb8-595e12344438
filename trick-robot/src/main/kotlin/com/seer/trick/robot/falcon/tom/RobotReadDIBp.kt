package com.seer.trick.robot.falcon.tom



import com.seer.trick.falcon.FalconLogLevel
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*


import com.seer.trick.robot.tom.TomAgent


class RobotReadDIBp : AbstractBp() {

  override fun process() {
    val tomId = mustGetBlockInputParam("tomId") as String
    val vehicle = mustGetBlockInputParam("vehicle") as String
    val id = mustGetBlockInputParamAsLong("id").toInt()
    val tomUrl = TomAgent.getTomUrlRoot(tomId)
    val result = TomAgent.readRobotIO(tomUrl, vehicle, id)
    log(FalconLogLevel.Info, "等待机器人$vehicle" + "的DI" + " " + "id:" + id)
    setBlockOutputParams(mapOf("$vehicle DI" to result))
  }

  companion object {

    val def = BlockDef(
      RobotReadDIBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(
        BlockInputParamDef("tomId", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.RobotScene)),
        BlockInputParamDef("vehicle", BlockParamType.String, true),
        BlockInputParamDef("id", BlockParamType.Long, true),
      ),
      outputParams = listOf(
        BlockOutputParamDef("DI", BlockParamType.String)
      )
    )

  }

}