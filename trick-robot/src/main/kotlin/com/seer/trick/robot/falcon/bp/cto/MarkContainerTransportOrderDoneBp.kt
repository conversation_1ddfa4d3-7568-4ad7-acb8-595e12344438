package com.seer.trick.robot.falcon.bp.cto

import com.seer.trick.Cq

import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.UpdateOptions
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*
import java.util.*

class MarkContainerTransportOrderDoneBp : AbstractBp() {

  override fun process() {
    val orderId = mustGetBlockInputParam("orderId") as String
    val newState = mustGetBlockInputParam("newState") as String

    EntityRwService.updateOne(
      
      "ContainerTransportOrder",
      Cq.idEq(orderId),
      mutableMapOf("status" to newState, "doneOn" to Date()),
      UpdateOptions(muteExt = true),
    )
    // 添加猎鹰任务相关业务对象
    addRelatedObject("ContainerTransportOrder", orderId, null)
  }

  companion object {

    val def = BlockDef(
      MarkContainerTransportOrderDoneBp::class.simpleName!!,
      color = "#7293d8",
      inputParams = listOf(
        BlockInputParamDef("orderId", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.CtoId)),
        BlockInputParamDef(
          "newState",
          BlockParamType.String,
          true,
          options = listOf(
            BlockInputParamOption("Done", "完成"),
            BlockInputParamOption("Failed", "失败"),
            BlockInputParamOption("Cancelled", "取消"),
          ),
        ),
      ),
    )
  }
}