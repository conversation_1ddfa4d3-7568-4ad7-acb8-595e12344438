package com.seer.trick.robot.single.bp


import com.seer.trick.falcon.domain.*
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper

class RobotSingleRollerBp : AbstractTaskChainBp() {
  
  override fun process() {
    val station = mustGetBlockInputParam("station") as String
    val operation = mustGetBlockInputParam("operation") as String
    val direction = mustGetBlockInputParam("direction") as String
    val reqId = getBlockInputParam("reqId") as String? ?: IdHelper.uuidStr()
    val req = JsonHelper.mapper.writeValueAsString(
      mapOf(
        "id" to station,
        "operation" to operation,
        "direction" to direction
      )
    )
    val gwRbkResult = executeTask(RbkRequest(reqId, 3051, req, "滚筒"))
    setBlockOutputParams(mapOf("rbkResult" to gwRbkResult))
  }
  
  companion object {
    
    val def = BlockDef(
      RobotSingleRollerBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(
        BlockInputParamDef(
          "station", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.BinId, ParamObjectType.SiteId)
        ),
        BlockInputParamDef(
          "operation", BlockParamType.String, true,
          options = listOf(
            BlockInputParamOption("RollerLoad"),
            BlockInputParamOption("RollerUnload")
          ),
          defaultValue = "RollerLoad"
        ),
        BlockInputParamDef(
          "direction", BlockParamType.String, true,
          options = listOf(
            BlockInputParamOption("left"),
            BlockInputParamOption("right"),
            BlockInputParamOption("front"),
            BlockInputParamOption("back")
          ),
          defaultValue = "left"
        ),
//        BlockInputParamDef("reqId", BlockParamType.String),
      ),
      outputParams = listOf(
        BlockOutputParamDef("rbkResult", BlockParamType.String)
      )
    )
    
  }
  
}