package com.seer.trick.robot.handler

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.http.WebSocketManager
import com.seer.trick.base.http.WebSocketSubscriber
import com.seer.trick.base.http.WsEnv
import com.seer.trick.base.http.WsMsg
import com.seer.trick.helper.JsonHelper
import com.seer.trick.robot.RobotAppManager
import com.seer.trick.robot.RobotAppMode
import com.seer.trick.robot.rachel.MrRobotSelfReport
import com.seer.trick.robot.single.RaSingleManager
import com.seer.trick.robot.single.SpeedSetting
import com.seer.trick.robot.sto.StoManager
import com.seer.trick.robot.tom.TomAgent
import io.javalin.websocket.WsMessageContext
import org.apache.commons.lang3.ObjectUtils
import org.slf4j.LoggerFactory
import java.math.BigDecimal
import java.math.RoundingMode
import java.util.*
import kotlin.math.abs

/**
 * 本模块 WebSocket 管理
 */
object RobotWsManager : WebSocketSubscriber() {

  private val logger = LoggerFactory.getLogger(this::class.java)

  fun registerHandlers() {
    WebSocketManager.subscribers += this
  }

  override fun onMessage(ctx: WsMessageContext, msg: WsMsg, env: WsEnv) {
    when (msg.action) {
      "RobotApp::SingleDigest::Query" -> onSingleDigest(ctx, msg)
      "RobotApp::TomDigest::Query" -> onTomDigest(ctx, msg)
      "RobotApp::RachelDigest::Query" -> onRachelDigest(ctx, msg)
      "RobotApp::MapRobots::Query" -> onMapRobots(ctx, msg)
      "WCS::GW::RobotOrder" -> onGwRobotOrder(ctx, msg)
      "TomOrderPage::Query" -> onTomOrderPage(ctx, msg)
      "RobotApp::InstitutionalControl" -> onInstitutionalControl(ctx, msg)
      "RobotApp::Navigation::Stop" -> onNavigationStop(ctx, msg)
      "Robot::OpenLoop" -> onOpenLoopReq(ctx, msg)
      "RobotApp::Runtime::Statistic" -> runtimeStatistic(ctx, msg)
      "RobotApp::BatteryLevel::Statistic" -> batteryLevelStatistic(ctx, msg)
    }
  }

  private fun onSingleDigest(ctx: WsMessageContext, msg: WsMsg) {
    val req: SceneName = JsonHelper.mapper.readValue(msg.content!!, jacksonTypeRef())
    val sr = RobotAppManager.mustGetScene(req.sceneName)
    val d = sr.single?.digest()
    ctx.send(WsMsg.json("RobotApp::SingleDigest::Reply", d))
  }

  private fun onTomDigest(ctx: WsMessageContext, msg: WsMsg) {
    val req: SceneName = JsonHelper.mapper.readValue(msg.content!!, jacksonTypeRef())
    val sr = RobotAppManager.mustGetScene(req.sceneName)
    val d = sr.tom?.digest()
    ctx.send(WsMsg.json("RobotApp::TomDigest::Reply", d))
  }

  private fun onRachelDigest(ctx: WsMessageContext, msg: WsMsg) {
    
    val req: SceneName = JsonHelper.mapper.readValue(msg.content!!, jacksonTypeRef())
    val sr = RobotAppManager.mustGetScene(req.sceneName)
    val robots = sr.rachel!!.listAllAll()
    ctx.send(WsMsg.json("RobotApp::RachelDigest::Reply", robots, replyToId = msg.id))
  }

  // 给前端地图用的机器人信息
  private fun onMapRobots(ctx: WsMessageContext, msg: WsMsg) {
    
    val req: SceneName = JsonHelper.mapper.readValue(msg.content!!, jacksonTypeRef())
    val sr = RobotAppManager.mustGetScene(req.sceneName)

    val robots: List<MapRobot> = (
      if (sr.config.mode == RobotAppMode.Single) {
        val d = sr.single!!.digest()
        listOf(MapRobot(id = d.robotName ?: "", selfReport = d.report))
      } else if (sr.config.mode == RobotAppMode.Tom) {
        val d = sr.tom!!.digest()
        d.status?.robots?.map {
          MapRobot(id = it.id, selfReport = MrRobotSelfReport(main = it.mainReport, rawReport = it.rbkReport))
        }
      } else if (sr.config.mode == RobotAppMode.Rachel || sr.config.mode == RobotAppMode.Light) {
        val robots = sr.rachel!!.listAllAll()
        robots.map {
          MapRobot(
            id = it.id,
            selfReport = it.selfReport,
            systemConfig = it.systemConfig,
            runtimeRecord = it.runtimeRecord,
            targetSite = it.targetSite,
            lockedSiteIds = it.lockedSiteIds,
            pendingSiteIds = it.pendingSiteIds,
          )
        }
      } else {
        null
      }
      ) ?: emptyList()

    ctx.send(WsMsg.json("RobotApp::MapRobots::Reply", robots, replyToId = msg.id))
  }

  private fun onGwRobotOrder(ctx: WsMessageContext, msg: WsMsg) {
    val req: GwRobotOrderReq = JsonHelper.mapper.readValue(msg.content, jacksonTypeRef())

    val robot = StoManager.stoRobotMap[req.robotName]
    val r = if (robot != null) {
      mapOf(
        "robotName" to robot.robotName,
        "currentOrder" to robot.currentOrder?.toEv(),
        "lastOrder" to robot.lastOrder?.toEv(),
      )
    } else {
      emptyMap()
    }

    ctx.send(WsMsg.json("WCS::GW::RobotOrder::Reply", r, replyToId = msg.id))
  }

  private fun onTomOrderPage(ctx: WsMessageContext, msg: WsMsg) {
    if (msg.content.isNullOrBlank()) return
    val req: RaTomAppHandler.TomQueryOrderPageReq = JsonHelper.mapper.readValue(msg.content, jacksonTypeRef()) ?: return
    val url = TomAgent.getTomUrlRoot(req.tomId)
    val o = TomAgent.QueryOrderPageOptions(
      vehicle = req.vehicle,
      state = req.state,
      notCompleteOnly = req.notCompleteOnly,
      externalId = req.externalId,
    )
    
    val r = TomAgent.queryOrderPage(url, req.pageNo, req.pageSize, o)

    ctx.send(WsMsg.json("TomOrderPage::Reply", r, replyToId = msg.id))
  }

  private fun onInstitutionalControl(ctx: WsMessageContext, msg: WsMsg) {
    val req: ControlReq = JsonHelper.mapper.readValue(msg.content, jacksonTypeRef())
    val single = RobotAppManager.mustGetScene(req.sceneName).single
      ?: throw BzError("errRobotAppNoSingleScene", req.sceneName)
    

    when (req.deviceType) {
      "fork" -> {
        forkOperation(req, single)
      }

      "jack" -> {
        jackOperation(req, single)
      }

      "miniFork" -> {
        miniForkOperation(req, single)
      }

      "box" -> {
        boxOperation(req, single)
      }
    }
    ctx.send(WsMsg.json("RobotApp::InstitutionalControl::Reply", "", replyToId = msg.id))
  }

  private fun forkOperation(req: ControlReq, single: RaSingleManager) {
    when (req.operation) {
      "ForkAdjust" -> {
        val rbkReq = mutableMapOf(
          "height" to req.height,
        )
        val reqStr = JsonHelper.mapper.writeValueAsString(rbkReq)
        single.rbkClient.requestWithReturnCodeError(6040, reqStr)
      }

      "Load" -> {
        val rbkReq = mutableMapOf(
          "end_height" to req.endHeight,
          "operation" to "ForkLoad",
          "skill_name" to "Action",
        )
        if (ObjectUtils.isNotEmpty(req.recfile)) {
          rbkReq["recfile"] = req.recfile!!
          rbkReq["recognize"] = true
        }
        val reqStr = JsonHelper.mapper.writeValueAsString(rbkReq)
        single.rbkClient.requestWithReturnCodeError(3051, reqStr)
      }

      "Unload" -> {
        val rbkReq = mapOf(
          "end_height" to req.endHeight,
          "operation" to "ForkUnload",
          "skill_name" to "Action",
        )
        val reqStr = JsonHelper.mapper.writeValueAsString(rbkReq)
        single.rbkClient.requestWithReturnCodeError(3051, reqStr)
      }

      "Height" -> {
        val reqStr = JsonHelper.mapper.writeValueAsString(
          mapOf(
            "end_height" to req.endHeight,
            "operation" to "ForkHeight",
            "skill_name" to "Action",
          ),
        )
        single.rbkClient.requestWithReturnCodeError(3051, reqStr)
      }
    }
  }

  private fun jackOperation(req: ControlReq, single: RaSingleManager) {
    when (req.operation) {
      "Load" -> {
        val rbkReq = mutableMapOf(
          "open_blinkDO" to false,
          "operation" to "JackLoad",
          "skill_name" to "Action",
        )
        if (ObjectUtils.isNotEmpty(req.recfile)) {
          rbkReq["recfile"] = req.recfile!!
        }
        val reqStr = JsonHelper.mapper.writeValueAsString(rbkReq)
        single.rbkClient.requestWithReturnCodeError(3051, reqStr)
      }

      "Unload" -> {
        val rbkReq = mutableMapOf(
          "open_blinkDO" to false,
          "operation" to "JackUnload",
          "skill_name" to "Action",
        )
        val reqStr = JsonHelper.mapper.writeValueAsString(rbkReq)
        single.rbkClient.requestWithReturnCodeError(3051, reqStr)
      }

      "Height" -> {
        val reqStr = JsonHelper.mapper.writeValueAsString(mapOf("height" to req.height))
        single.rbkClient.requestWithReturnCodeError(6073, reqStr)
      }
    }
  }

  private fun miniForkOperation(req: ControlReq, single: RaSingleManager) {
    when (req.operation) {
      "Load" -> {
        val rbkReq = mapOf(
          "end_height" to 1,
          "operation" to "ForkLoad",
          "skill_name" to "Action",
        )
        val reqStr = JsonHelper.mapper.writeValueAsString(rbkReq)
        single.rbkClient.requestWithReturnCodeError(3051, reqStr)
      }

      "Unload" -> {
        val rbkReq = mapOf(
          "end_height" to 0,
          "operation" to "ForkUnload",
          "skill_name" to "Action",
        )
        val reqStr = JsonHelper.mapper.writeValueAsString(rbkReq)
        single.rbkClient.requestWithReturnCodeError(3051, reqStr)
      }
    }
  }

  private fun boxOperation(req: ControlReq, single: RaSingleManager) {
    val rbkReq = when (req.operation) {
      "InternalUnload" -> {
        mapOf(
          "id" to "SELF_POSITION",
          "operation" to "script",
          "script_args" to mapOf("operation" to "in_put", "container" to req.containerId, "goodsId" to req.goodsId),
          "script_name" to "containerRobot.py",
        )
      }

      "InternalLoad" -> {
        mapOf(
          "id" to "SELF_POSITION",
          "operation" to "script",
          "script_args" to mapOf("operation" to "in_take", "container" to req.containerId, "goodsId" to req.goodsId),
          "script_name" to "containerRobot.py",
        )
      }

      else -> {
        return
      }
    }
    val reqStr = JsonHelper.mapper.writeValueAsString(rbkReq)
    single.rbkClient.requestWithReturnCodeError(3051, reqStr)
  }

  /**
   * 停止导航
   */
  private fun onNavigationStop(ctx: WsMessageContext, msg: WsMsg) {
    val req: SceneName = JsonHelper.mapper.readValue(msg.content, jacksonTypeRef())
    val single = RobotAppManager.mustGetScene(req.sceneName).single
      ?: throw BzError("errRobotAppNoSingleScene", req.sceneName)
    
    logger.info("Stop navigation")
    single.rbkClient.requestWithReturnCodeError(3003, "")
    ctx.send(WsMsg.json("RobotApp::Navigation::Reply", "", replyToId = msg.id))
  }

  private fun onOpenLoopReq(ctx: WsMessageContext, msg: WsMsg) {
    val req: RobotOpenLoopReq = JsonHelper.mapper.readValue(msg.content, jacksonTypeRef())
    val single = RobotAppManager.mustGetScene(req.sceneName).single
      ?: throw BzError("errRobotAppNoSingleScene", req.sceneName)

    if (single.stateAgent.controlledByMe != true) {
      ctx.send(WsMsg.json("Robot::OpenLoop::Reply", "Robot No ControlPower", replyToId = msg.id))
      return
    }

    val speedConfig = single.singleConfig.speedConfig
    val speedSetting = speedConfig.find { it.level == req.speedSetting.level } ?: return
    val reqXMax = req.speedSetting.vxMax
    val reqYMax = req.speedSetting.vyMax
    val reqWMax = req.speedSetting.omegaMax
    if (reqXMax != speedSetting.vxMax || reqYMax != speedSetting.vyMax || reqWMax != speedSetting.omegaMax) {
      ctx.send(WsMsg.json("Robot::OpenLoop::Reply", mapOf("level" to speedSetting.level), replyToId = msg.id))
    }

    
    move(req, ctx, msg, single, speedSetting)
  }

  private fun move(
    
    req: RobotOpenLoopReq,
    ctx: WsMessageContext,
    msg: WsMsg,
    single: RaSingleManager,
    speedSetting: SpeedSetting,
  ) {
    // 允许 10% 的误操作
    if (abs(req.y) <= 0.1 && abs(req.x) <= 0.1) {
      ctx.send(WsMsg.json("Robot::OpenLoop::Reply", "", replyToId = msg.id))
      return
    }

    // 10% 以内的操作忽略，方便进行直线运动或者原地旋转
    val vx = if (abs(req.y) > 0.1) {
      BigDecimal(req.y).multiply(BigDecimal(speedSetting.vxMax)).setScale(3, RoundingMode.HALF_UP).toDouble()
    } else {
      0.0
    }

    // 判断是否为全向车
    val model = single.robotModelMap.values.firstOrNull()
    val isOmni = model?.get("chassisMode") == "omni"
    val vy: Double
    var w: Double
    val rbkReq: Map<String, Any>
    // 全向车且不旋转的情况下，有 vy，无 w
    if (isOmni && !req.rotate) {
      vy = if (abs(req.x) > 0.1) {
        BigDecimal(req.x).multiply(BigDecimal(speedSetting.vyMax)).setScale(3, RoundingMode.HALF_UP).toDouble()
      } else {
        0.0
      }
      // 为了虚拟摇杆操作方向与运动方式一致，需取反
      rbkReq = mapOf("vx" to vx, "vy" to -vy)
    } else {
      val pi = BigDecimal(Math.PI.toString())
      w = if (abs(req.x) > 0.1) {
        pi.multiply(BigDecimal(req.x).multiply(BigDecimal(speedSetting.omegaMax)))
          .divide(BigDecimal("180"), 3, RoundingMode.HALF_UP)
          .toDouble()
      } else {
        0.0
      }

      // 机器人前进时，为了虚拟摇杆操作方向与运动方式一致，故下发的 w 需要取反
      if (vx >= 0) w = -w

      w = if (w > 0) {
        w.coerceAtMost(180.0)
      } else {
        w.coerceAtLeast(-180.0)
      }
      rbkReq = mapOf("vx" to vx, "w" to w)
    }
    val rbkReqStr = JsonHelper.mapper.writeValueAsString(rbkReq)
    single.rbkClient.requestWithReturnCodeError(2010, rbkReqStr)
    ctx.send(WsMsg.json("Robot::OpenLoop::Reply", "move", replyToId = msg.id))
  }

  private fun runtimeStatistic(ctx: WsMessageContext, msg: WsMsg) {
    val req: StatisticReq = JsonHelper.mapper.readValue(msg.content, jacksonTypeRef())
    
    val single = try {
      RobotAppManager.mustGetRmSingle(req.sceneName)
    } catch (e: BzError) {
      logger.error("SceneName: ${req.sceneName} not found")
      ctx.send(WsMsg.json("RobotApp::Runtime::Reply", "NoScene"))
      return
    }
    ctx.send(WsMsg.json("RobotApp::Runtime::Reply", single.listRobotRuntime(req.startTime, req.endTime)))
  }

  private fun batteryLevelStatistic(ctx: WsMessageContext, msg: WsMsg) {
    val req: StatisticReq = JsonHelper.mapper.readValue(msg.content, jacksonTypeRef())
    
    val single = try {
      RobotAppManager.mustGetRmSingle(req.sceneName)
    } catch (e: BzError) {
      logger.error("SceneName: ${req.sceneName} not found")
      ctx.send(WsMsg.json("RobotApp::BatteryLevel::Reply", "NoScene"))
      return
    }
    ctx.send(WsMsg.json("RobotApp::BatteryLevel::Reply", single.listBatteryLevel(req.startTime, req.endTime)))
  }

  data class SceneName(val sceneName: String)

  data class GwRobotOrderReq(val robotName: String)

  data class MapRobot(
    val id: String,
    val selfReport: MrRobotSelfReport?,
    val systemConfig: EntityValue? = null,
    val runtimeRecord: EntityValue? = null,
    val targetSite: String? = null,
    val lockedSiteIds: List<String>? = null,
    val pendingSiteIds: List<String>? = null, // 目前只用于光通讯
  )

  data class RobotOpenLoopReq(
    val sceneName: String,
    val timestamp: Long,
    // 横向速度百分比，低于 0.1 不处理
    val x: Double,
    // 纵向速度百分比，低于 0.1 不处理
    val y: Double,
    // 仅对全向车的控制有效；默认为 false，此时后端将 x 轴当做 w 处理；true 时，后端将 x 轴当做 vy 处理。
    val rotate: Boolean = false,
    val speedSetting: SpeedSetting,
  )

  data class ControlReq(
    val sceneName: String,
    // 机构类型
    val deviceType: String,
    // 具体的操作
    val operation: String,
    // 插车的货叉升降高度
    val endHeight: Double?,
    // 顶升车的机构高度
    val height: Double?,
    // 识别文件
    val recfile: String?,
    // 料箱车：容器编号
    val containerId: Int?,
    // 料箱车：货物编号
    val goodsId: String?,
  )

  data class StatisticReq(val sceneName: String, val startTime: Date, val endTime: Date)
}