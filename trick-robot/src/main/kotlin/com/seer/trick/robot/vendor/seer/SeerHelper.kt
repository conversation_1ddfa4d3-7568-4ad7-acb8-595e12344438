package com.seer.trick.robot.vendor.seer

import com.seer.trick.I18N
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.helper.NumHelper
import com.seer.trick.robot.map.SceneMapManager
import com.seer.trick.robot.rachel.MrRobotAlert
import com.seer.trick.robot.rachel.MrRobotAlertLevel
import com.seer.trick.robot.rachel.MrRobotSelfReportMain
import org.apache.commons.lang3.StringUtils
import org.apache.commons.lang3.math.NumberUtils
import org.slf4j.LoggerFactory

object SeerHelper {

  private val logger = LoggerFactory.getLogger(this::class.java)

  fun rawToMainSeer(rawReport: EntityValue, coreArea: String?, mm: SceneMapManager): MrRobotSelfReportMain {
    val currentStation = rawReport["current_station"] as String? ?: ""
    val x = NumHelper.anyToDouble(rawReport["x"])
    val y = NumHelper.anyToDouble(rawReport["y"])
    val angle = NumHelper.anyToDouble(rawReport["angle"])

    val alerts: MutableList<MrRobotAlert> = ArrayList()
    parseSeerAlert(rawReport["fatals"], MrRobotAlertLevel.Fatal, alerts)
    parseSeerAlert(rawReport["errors"], MrRobotAlertLevel.Error, alerts)
    parseSeerAlert(rawReport["warnings"], MrRobotAlertLevel.Warning, alerts)
    parseSeerAlert(rawReport["notices"], MrRobotAlertLevel.Info, alerts)

    val relocStatus = NumHelper.anyToInt(rawReport["reloc_status"])
    val relocStatusLabel = I18N.lo("relocStatus_$relocStatus")

    @Suppress("UNCHECKED_CAST")
    val currentLock = rawReport["current_lock"] as Map<String, Any?>?
    val currentLockNickName = currentLock?.get("nick_name") as String?

    val currentMap = rawReport["current_map"] as String?
    // 去掉后缀
    val currentMapMain = if (currentMap == null) {
      null
    } else if (currentMap.endsWith(".smap")) {
      StringUtils.substringBeforeLast(currentMap, ".smap")
    } else if (currentMap.endsWith(".SMAP")) {
      StringUtils.substringBeforeLast(currentMap, ".SMAP")
    } else {
      currentMap
    }

    val currentArea = if (!currentMapMain.isNullOrBlank()) {
      mm.getAreaByAnyName(currentMapMain)
    } else {
      null
    }

    // val xStr = x?.toString() ?: "?"
    // val yStr = y?.toString() ?: "?"
    return MrRobotSelfReportMain(
      battery = NumHelper.anyToDouble(rawReport["battery_level"]),
      x = x,
      y = y,
      direction = angle,
      currentSite = currentStation,
      currentAreaId = currentArea?.id,
      currentAreaName = coreArea ?: currentArea?.name,
      // currentLocationDesc = "[$currentStation] ($xStr, $yStr)",
      blocked = rawReport["blocked"] as Boolean?,
      charging = rawReport["charging"] as Boolean?,
      currentMap = currentMap,
      currentMapMd5 = rawReport["current_map_md5"] as String?,
      emergency = rawReport["emergency"] as Boolean?,
      softEmc = rawReport["soft_emc"] as Boolean?,
      todayOdo = NumHelper.anyToDouble(rawReport["today_odo"]), // 今日累计行驶里程, 单位 m
      confidence = NumHelper.anyToDouble(rawReport["confidence"]), // 机器人的定位置信度, 范围 [0, 1]
      relocStatusLabel = relocStatusLabel,
      currentLockNickName = currentLockNickName,
      moveStatusInfo = rawReport["move_status_info"] as String?,
      alerts = alerts,
    )
  }

  fun parseSeerAlert(node: Any?, level: MrRobotAlertLevel, alerts: MutableList<MrRobotAlert>) {
    try {
      if (node == null) return
      val errors = node as List<*>
      for (e in errors) {
        @Suppress("UNCHECKED_CAST")
        val eo = e as Map<String, Any?>
        val desc = eo["desc"] as String?
        val times = eo["times"] as Int?
        // 调度报错 code 直接事一个字段
        val code = eo.keys.find { NumberUtils.isDigits(it) } ?: (eo["code"] as Int?)?.toString()
        if (!desc.isNullOrBlank() && code != null) {
          alerts += MrRobotAlert(level, code, desc, times ?: 1)
        }
      }
    } catch (e: Exception) {
      logger.error("解析仙工机器人报告中的告警", e)
    }
  }
}