package com.seer.trick.robot

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.seer.trick.base.entity.EntityValue

/**
 * 机器人应用配置。总配置
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class RobotAppConfig(
  val scenes: List<RobotAppSceneConfig> = emptyList(), // 分场景配置
)

/**
 * 机器人应用场景配置
 */
data class RobotAppSceneConfig(
  val name: String = "",
  val mode: RobotAppMode = RobotAppMode.None,
  val disabled: Boolean = false,
  val remark: String = "",
  val displayOrder: Int? = null,
  val single: SingleAppConfig = SingleAppConfig(), // 单机配置
  val tom: TomAppConfig = TomAppConfig(), // 二代调度（CORE）配置
  val rachel: RachelConfig = RachelConfig(), // 三代调度配置
)

/**
 * 机器人应用模式
 */
enum class RobotAppMode {
  None,
  Single, // 单机应用
  Tom, // 二代调度（CORE）
  Rachel, // 三代调度
  Light, // 光通讯调度
}

data class SingleAppConfig(
  val host: String? = null,
  val robotImage: String? = null,
  val image: EntityValue? = null,
  val robotDetailsConfigStr: String? = null,
)

data class TomAppConfig(
  val coreUrl: String = "",
)

data class RachelConfig(
  val onDutyOnBoot: Boolean = false,
  val displayType: String? = null, // 前端用
  val noResourceSites: Boolean = false, // 前端用
  val cpnEnabled: Boolean = false,
  val cpnDispatchPeriod: Long? = null,
  val withTom: Boolean = false, // 执行用 Tom
  val tomUrlRoot: String? = null,
)