package com.seer.trick.robot.light

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.robot.sto.OrderResult

/**
 * GW 向 M4 上报
 */
data class ReportApplyReq(
  val robotName: String,
  val selfReport: ReportApplySelfReport,
  val currentOrder: OrderResult?,
  val lastOrder: OrderResult?,
  val unfinishedOrders: List<OrderResult>
)

data class ReportApplySelfReport(
  val rawReport: EntityValue?
)

/**
 * M4 向 GW 响应
 */
@JsonIgnoreProperties
data class ReportApplyRes(
  val nextOrder: ReportApplyNextOrder? = null,
  val cancelledOrderIds: List<String>? = emptyList(),
  val manualDoneOrderIds: List<String>? = emptyList(),
)

@JsonIgnoreProperties
data class ReportApplyNextOrder(
  val id: String = "",
  val robotName: String = "",
  val seer3066: Boolean = false,
  val moves: List<EntityValue> = emptyList()
)