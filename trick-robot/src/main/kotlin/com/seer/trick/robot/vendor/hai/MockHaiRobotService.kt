package com.seer.trick.robot.vendor.hai

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef

import com.seer.trick.base.concurrent.BaseConcurrentCenter.getAdhocLoopThead
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.NumHelper
import com.seer.wcs.device.tcp.TcpClient
import io.netty.channel.ChannelHandlerContext
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

object MockHaiRobotService {

  @Volatile
  private var robots: List<MockHaiRobot> = emptyList()

  fun init(num: Int) {
    val noSsl = BzConfigManager.getByPath("ScWcs", "hai", "haiNoSSL") == true
    robots = (1..num).map {
      val client = TcpClient("127.0.0.1", 4762, HaiTcp.schema, !noSsl, MockHaiRobotService::onMessage)
      
      val ev = EntityRwService.findOneById("HaiMockRobot", it.toString())
      val x = NumHelper.anyToDouble(ev?.get("posX"))
      val y = NumHelper.anyToDouble(ev?.get("posY"))
      val robot = MockHaiRobot(it, client, x, y)
      getAdhocLoopThead("Mock Hai robot $num") {
        while (!Thread.interrupted()) {
          report(robot)
          Thread.sleep(100)
        }
      }
      robot
    }
  }

  fun onMessage(ctx: ChannelHandlerContext, frame: HaiFrame) {
    when (frame.channel) {
      MessageChannel.INSTRUCTION_REQ -> onInstruction(ctx, frame)
    }
  }

  @Suppress("UNCHECKED_CAST")
  private fun onInstruction(ctx: ChannelHandlerContext, frame: HaiFrame) {
    val body: EntityValue = JsonHelper.mapper.readValue(frame.bodyJsonStr, jacksonTypeRef())
    val msgType = body["msgType"] as Int
    val seqNum = body["seqNum"] as Int
    val robotId = body["robotId"] as String
    val robotIdNum = robotId.toInt()

    val robot = robots.find { it.robotId == robotIdNum } ?: return

    if (msgType == 0) {
      robot.state = RobotState.ROBOT_IDLE
    } else {
      robot.executor.submit {
        robot.state = RobotState.ROBOT_RUNNING

        ctx.writeAndFlush(HaiTcp.buildFrame(MessageChannel.INSTRUCTION_ACK, robotIdNum, seqNum, "{}"))

        // 是否要修改位置
        val targetPosition = body["targetPosition"] as EntityValue?
        robot.x = NumHelper.anyToDouble(targetPosition?.get("x"))
        robot.y = NumHelper.anyToDouble(targetPosition?.get("y"))
        robot.dir = NumHelper.anyToDouble(targetPosition?.get("theta"))
        // val report: EntityValue = mutableMapOf(
        //   "mapPosition" to targetPosition
        // )
        // robot.report = report

        val haiMockStepDelayStr = BzConfigManager.getByPath("ScWcs", "hai", "haiMockStepDelay")
        var haiMockStepDelay = NumHelper.anyToInt(haiMockStepDelayStr) ?: 600
        if (haiMockStepDelay <= 0) haiMockStepDelay = 100
        Thread.sleep(haiMockStepDelay.toLong())

        robot.state = RobotState.ROBOT_IDLE

        ctx.writeAndFlush(HaiTcp.buildFrame(MessageChannel.INSTRUCTION_RET, robotIdNum, seqNum, "{}"))
      }
    }
  }

  private fun report(robot: MockHaiRobot) {
    val seqNum = ++robot.seqNum

    val ev = EntityRwService.findOneById("HaiMockRobot", robot.robotId.toString())
    val battery = NumHelper.anyToDouble(ev?.get("battery")) ?: .5

    val report: EntityValue = mutableMapOf(
      "seqNum" to seqNum,
      "robotId" to robot.robotId,
      "robotState" to robot.state,
      "batteryInfo" to mutableMapOf("powerLevel" to (battery * 100)),
      "mapPosition" to mutableMapOf("x" to robot.x, "y" to robot.y, "theta" to robot.dir)
    )

    val frame = HaiTcp.buildFrame(
      MessageChannel.REPORT_INFO_REQ, robot.robotId, seqNum,
      JsonHelper.mapper.writeValueAsString(report)
    )
    robot.client.write(frame)
  }
}

class MockHaiRobot(
  val robotId: Int,
  val client: TcpClient<HaiFrame>,
  @Volatile
  var x: Double? = null,
  @Volatile
  var y: Double? = null,
  @Volatile
  var dir: Double? = null,
) {
  @Volatile
  var seqNum: Int = 0

  @Volatile
  var state: Int = RobotState.ROBOT_READY_TO_INIT

  val executor: ExecutorService = Executors.newSingleThreadExecutor()

}