package com.seer.trick.robot.single

import com.seer.trick.Cq

import com.seer.trick.base.concurrent.BaseConcurrentCenter.getAdhocLoopThead
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.soc.SocAttention
import com.seer.trick.base.soc.SocService
import com.seer.trick.helper.IdHelper
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.Future

/**
 * TODO 目前用的把数据记录数据库的方式可能导致数量很大；而且一条数据一个业务对象没必要。
 */
class RaSingleStatistic(
  private val parent: RaSingleManager,
) {
  private val logger = LoggerFactory.getLogger(javaClass)
  
  @Volatile
  private var runtimeFuture: Future<*>? = null
  
  @Volatile
  private var electricityFuture: Future<*>? = null
  
  @Volatile
  private var lastNavigation: EntityValue = mutableMapOf()
  
  fun init() {
    runtimeFuture = getAdhocLoopThead("SingleStats::Navigation") {
      navigationStatistic()
    }
    
    electricityFuture = getAdhocLoopThead("SingleStats::Electricity") {
      electricityStatistic()
    }
  }
  
  fun dispose() {
    electricityFuture?.cancel(true)
    runtimeFuture?.cancel(true)
  }
  
  private fun navigationStatistic() {
    
    logger.info("开始记录机器人导航信息")
    while (!Thread.interrupted()) {
      try {
        val report = parent.stateAgent.report
        val rawReport: EntityValue? = report?.rawReport
        if (rawReport != null) {
          val ev: EntityValue = newNavigationRecord(rawReport)
          // taskStatus、taskType、targetId、targetPoint 发生变化则记录一次导航记录，并更新上一次导航记录的时长，若是获取不到报告则更新结束上一次记录
          if (statusToString(ev) != statusToString(lastNavigation)) {
            updateCost()
            EntityRwService.createOne("RaSingleNavigateRecord", ev)
            lastNavigation = ev
          }
        } else {
          // 机器人离线的情况
          updateCost()
        }
        Thread.sleep(500)
      } catch (e: InterruptedException) {
        updateCost()
        return
      } catch (e: Throwable) {
        if (SocService.getNode("navigationStatistic") == null) {
          logger.error("记录机器人导航信息时发生异常：${e.message}")
        }
        SocService.updateNode(
          "机器人统计",
          "navigationStatistic",
          "统计机器人导航信息失败: ${e.message}",
          "navigationStatistic",
          SocAttention.Red
        )
      }
    }
  }
  
  private fun newNavigationRecord(rawReport: EntityValue): EntityValue {
    return mutableMapOf(
      "id" to IdHelper.oidStr(),
      "taskStatus" to rawReport["task_status"],
      "taskType" to rawReport["task_type"],
      "targetId" to rawReport["target_id"],
      "targetPoint" to rawReport["target_point"],
      "robotName" to RaSingleManager.DEFAULT_NAME,
      "createdBy" to "system",
      "modifiedBy" to "system",
      "createdOn" to Date()
    )
  }
  
  private fun updateCost() {
    if (lastNavigation["taskStatus"] != SeerRobotState.ROBOT_RUNNING) return
    // 必须重新创建新对象，否则更新时会删除创建时间等字段
    val updateEv = lastNavigation.toMutableMap()
    updateEv["cost"] = (System.currentTimeMillis() - (updateEv["createdOn"] as Date).time).toDouble()
    EntityRwService.updateOne("RaSingleNavigateRecord", Cq.idEq(updateEv["id"] as String), updateEv)
  }
  
  private fun statusToString(ev: EntityValue): String {
    return "${ev["taskStatus"]},${ev["taskType"]},${ev["targetId"]},${ev["targetPoint"]}"
  }
  
  private fun electricityStatistic() {
    
    logger.info("开始记录机器人电池信息")
    while (!Thread.interrupted()) {
      try {
        val report = parent.stateAgent.report
        report?.rawReport?.let {
          val ev: EntityValue = mutableMapOf(
            "id" to IdHelper.oidStr(),
            "charging" to it["charging"],
            "batteryLevel" to it["battery_level"],
            "batteryTemp" to it["battery_temp"],
            "voltage" to it["voltage"],
            "current" to it["current"],
            "robotName" to RaSingleManager.DEFAULT_NAME,
            "createdBy" to "system",
            "modifiedBy" to "system",
            "createdOn" to Date()
          )
          EntityRwService.createOne("RaSingleBatteryRecord", ev)
        }
        Thread.sleep(60000)
      } catch (e: InterruptedException) {
        return
      } catch (e: Throwable) {
        if (SocService.getNode("electricityStatistic") == null) {
          logger.error("记录机器人电池信息时发生异常：${e.message}")
        }
        SocService.updateNode(
          "机器人统计",
          "electricityStatistic",
          "统计机器人电池信息失败: ${e.message}",
          "electricityStatistic",
          SocAttention.Red
        )
      }
    }
  }
}