package com.seer.trick.robot.vendor.ndc

import com.seer.trick.Cq

import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.soc.SocAttention
import com.seer.trick.base.soc.SocService
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.NumHelper
import com.seer.wcs.device.tcp.TcpClient
import io.netty.buffer.ByteBuf
import io.netty.buffer.ByteBufUtil
import io.netty.buffer.CompositeByteBuf
import io.netty.buffer.Unpooled
import io.netty.channel.ChannelHandlerContext
import org.slf4j.LoggerFactory
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledFuture
import java.util.concurrent.TimeUnit

object NdcAdapter {
  
  private val logger = LoggerFactory.getLogger(this::class.java)
  
  private val lock = Object()
  
  @Volatile
  private var flowNo = 0
  
  private var client: TcpClient<NdcFrame>? = null
  
  private val sendWorker = Executors.newSingleThreadExecutor()
  private val executor = Executors.newScheduledThreadPool(1)
  private var futures: List<ScheduledFuture<*>> = mutableListOf()
  
  
  private fun getClient(): TcpClient<NdcFrame> {
    synchronized(lock) {
      return if (client != null) client!!
      else {
        val cfg = getNdcUrl()
        client = TcpClient(cfg.ip, cfg.port, NdcTcp.schema, false, NdcAdapter::onMessage)
        client!!
      }
    }
  }
  
  fun dispose() {
    synchronized(lock) {
      val t = client
      client = null
      t?.dispose()
      
      for (f in futures) {
        f.cancel(true)
      }
    }
  }
  
  @Synchronized
  fun nextFlowNo(): Int {
    return (flowNo++) % 1000 + 1
  }
  
  
  fun start() {
    
    
    val cfg = getNdcUrl()
    client = TcpClient(cfg.ip, cfg.port, NdcTcp.schema, false, NdcAdapter::onMessage)
    
    NdcMockServer.dispose()
    if (cfg.mock == true) {
      NdcMockServer.start()
    }
    futures += executor.scheduleWithFixedDelay(NdcAdapter::ping2, 0, 5000, TimeUnit.MILLISECONDS)
    
    futures += executor.scheduleWithFixedDelay(NdcAdapter::processOrder, 1800, 500, TimeUnit.MILLISECONDS)
    
    initFlowNo()
    // executor.submit(::ping2)
  }
  
  private fun initFlowNo() {
    
    val lastOrder = EntityRwService.findOne("NdcOrder", Cq.gt("ikey", 0), FindOptions(sort = listOf("-id")))
    if (lastOrder != null) {
      flowNo = NumHelper.anyToInt(lastOrder["ikey"]) ?: 0
    }
  }
  
  private fun processOrder() {
    
    
    sendWorker.submit {
      val createdOrders = EntityRwService.findMany("NdcOrder", Cq.eq("status", NdcOrderState.Created))
      if (createdOrders.isEmpty()) return@submit
      
      for (order in createdOrders) {
        val orderId = order["id"] as String
        val k = NumHelper.anyToInt(order["ikey"])
        val ikey = if (k != null && k > 0) k else nextFlowNo()
        val p = (NumHelper.anyToInt(order["priority"]) ?: 0) + 0x80
        val startBin = NumHelper.anyToInt(order["startBin"]) ?: 0
        val endBin = NumHelper.anyToInt(order["endBin"]) ?: 0
        EntityRwService.updateOne("NdcOrder", Cq.eq("id", orderId), mutableMapOf("ikey" to ikey), null)
        sendQ(priority = p, ikey = ikey, startBin, endBin)
        EntityRwService.updateOne(
          "NdcOrder", Cq.eq("id", orderId), mutableMapOf("status" to NdcOrderState.Sent), null
        )
      }
    }
    
    sendWorker.submit {
      val loadOrders = EntityRwService.findMany(
        "NdcOrder", Cq.and(listOf(Cq.eq("status", NdcOrderState.ReadyToLoad), Cq.eq("allowLoad", true)))
      )
      if (loadOrders.isEmpty()) return@submit
      
      for (order in loadOrders) {
        val orderId = order["id"] as String
        (NumHelper.anyToInt(order["priority"]) ?: 0) + 0x80
        NumHelper.anyToInt(order["startBin"]) ?: 0
        NumHelper.anyToInt(order["endBin"]) ?: 0
        NumHelper.anyToInt(order["ikey"]) ?: 0
        val index = NumHelper.anyToInt(order["index"]) ?: 0
        
        sendM(SendM(index, 1, 10, 0, 1, 0, 0, 0, 0))
        EntityRwService.updateOne(
          "NdcOrder", Cq.eq("id", orderId), mutableMapOf("status" to NdcOrderState.AllowLoad), null
        )
      }
    }
    
    sendWorker.submit {
      val unloadOrders = EntityRwService.findMany(
        "NdcOrder", Cq.and(listOf(Cq.eq("status", NdcOrderState.ReadyToUnload), Cq.eq("allowUnload", true)))
      )
      if (unloadOrders.isEmpty()) return@submit
      
      for (order in unloadOrders) {
        val orderId = order["id"] as String
        (NumHelper.anyToInt(order["priority"]) ?: 0) + 0x80
        NumHelper.anyToInt(order["startBin"]) ?: 0
        NumHelper.anyToInt(order["endBin"]) ?: 0
        NumHelper.anyToInt(order["ikey"]) ?: 0
        val index = NumHelper.anyToInt(order["index"]) ?: 0
        
        // TODO 不允许放货，要换库位的场景
        sendM(SendM(index, 1, 10, 0, 1, 0, 0, 0, 0))
        EntityRwService.updateOne(
          "NdcOrder", Cq.eq("id", orderId), mutableMapOf("status" to NdcOrderState.AllowLoad), null
        )
      }
    }
  }
  
  fun onMessage(ctx: ChannelHandlerContext, frame: NdcFrame) {
    
    try {
      val headStr = ByteBufUtil.hexDump(frame.headBuf, 0, frame.headLength)
      val bodyStr = ByteBufUtil.hexDump(frame.bodyBuf, 0, frame.bodyLength)
      
      val log = BzConfigManager.getByPath("ScDev", "logTcpWrite") == true
      // 只打印需要解析的消息
      if (frame.bodyLength != 0 && (frame.type == NdcMsgType.S.code || frame.type == NdcMsgType.Ba.code))
        logger.info(
          "NdcAdapter 收到 NDC 消息: headLength=${frame.headLength} bodyLength=${frame.bodyLength} headBuf=$headStr bodyBuf=$bodyStr"
        )
      else if (frame.functionCode == 4 || frame.functionCode == 5)
        logger.info(
          "NdcAdapter 收到 NDC 心跳消息: headLength=${frame.headLength} bodyLength=${frame.bodyLength} headBuf=$headStr bodyBuf=$bodyStr"
        )
      else if (log)
        logger.info(
          "NdcAdapter 收到异常 NDC 消息，headLength=${frame.headLength} bodyLength=${frame.bodyLength} headBuf=$headStr bodyBuf=$bodyStr"
        )
      
      val headFunctionCode = frame.headBuf.getShort(6).toInt()
      when (headFunctionCode) {
        4 -> {
          val msg = "NdcAdapter 收到 NDC 心跳 functionCode=4"
          logger.debug(msg)
          // logger.debug("NdcAdapter 收到 NDC 心跳 functionCode=4，后续发 pong")
          // pong()
          SocService.updateNode("Ndc", "ndc-pong-4", "receive pong 4", msg, SocAttention.Green)
        }
        
        5 -> {
          val msg = "NdcAdapter 收到 NDC 心跳 functionCode=5"
          logger.debug(msg)
          // logger.debug("NdcAdapter 收到 NDC 心跳 functionCode=5，后续发 ping")
          // ping()
          SocService.updateNode("Ndc", "ndc-pong-5", "receive pong 5", msg, SocAttention.Green)
        }
        
        else -> {
          when (frame.type) {
            NdcMsgType.Q.code -> {}
            NdcMsgType.S.code -> {
              parseS(ctx, frame)
            }
            
            NdcMsgType.Ba.code -> {
              if (frame.bodyLength == 10) parseBa(ctx, frame)
              else parseBb(ctx, frame)
            }
          }
        }
      }
    } catch (e: Throwable) {
      logger.error("NdcAdapter error on process NDC message, e=${e.message}", e)
      dispose()
    } finally {
      frame.headBuf.release()
      frame.bodyBuf.release()
    }
  }
  
  @Suppress("UNCHECKED_CAST")
  private fun getNdcUrl(): NdcCfg {
    val port = NumHelper.anyToInt(BzConfigManager.getByPath("ScWcs", "ndc", "port")) ?: 6000
    val mock = BzConfigManager.getByPath("ScWcs", "ndc", "mock") as Boolean? ?: false
    if (!mock) {
      val ip = BzConfigManager.getByPath("ScWcs", "ndc", "ip") as String? ?: "127.0.0.1"
      return NdcCfg(ip, port, mock)
    }
    
    return NdcCfg("127.0.0.1", port, true)
  }
  
  
  /**
   * 开启 NDC 的心跳
   *
   * 开启后，每次收到 NDC 的 pong 消息，GW 均需要回复 pong。
   */
  
  private fun ping2() {
    
    try {
      ping()
      SocService.removeNode("ndc-ping-error")
    } catch (e: Throwable) {
      logger.error("NdcAdapter ping error, e=${e.message}", e)
      SocService.updateNode("Ndc", "ndc-ping-error", "ping error", e.message, SocAttention.Red)
    }
  }
  
  fun ping() {
    SocService.updateNode("Ndc", "ndc-ping", "ping start", "start ping", SocAttention.Green)
    val frame = NdcTcp.buildFrame(4, Unpooled.buffer())
    logger.debug("NdcAdapter ping")
    getClient().write(frame)
    SocService.updateNode("Ndc", "ndc-ping", "ping end", "written ping", SocAttention.Green)
  }
  
  private fun pong() {
    val frame = NdcTcp.buildFrame(5, Unpooled.buffer())
    logger.debug("NdcAdapter ping")
    getClient().write(frame)
  }
  
  // 上位给 NDC 发任务
  // 构建 Q(b)
  fun sendQ(priority: Int, ikey: Int, fromBin: Int, toBin: Int) {
    
    val t = NdcMsgType.Q
    
    val body: ByteBuf = Unpooled.buffer(t.bodyLength)
    body.writeShort(t.code)
    body.writeShort(t.bodyLength - 4)
    body.writeByte(0x01)
    body.writeByte(priority)
    body.writeShort(1)
    body.writeShort(ikey)
    body.writeShort(fromBin)
    body.writeShort(toBin)
    
    val frame = NdcTcp.buildFrame(1, body)
    getClient().write(frame)
  }
  
  // B 用来回复 Q、N、M
  // 解析 B(a)
  fun parseBa(ctx: ChannelHandlerContext, frame: NdcFrame) {
    synchronized(this) {
      val type = frame.bodyBuf.readShort()
      val restLength = frame.bodyBuf.readShort()
      val index = frame.bodyBuf.readShort().toInt()
      val transportStructure = frame.bodyBuf.readByte()
      val status = frame.bodyBuf.readByte().toInt()
      val parNo = frame.bodyBuf.readByte()
      logger.debug(
        "NdcAdapter 解析 NDS Ba 消息，type=0x${Integer.toHexString(type.toInt())}, restLength=$restLength, " +
          "index=$index, transportStructure=$transportStructure, status=0x${Integer.toHexString(status)}, parNo=$parNo"
      )
      
      // 1 命令接受，17 参数值接受
      if (status == NdcB.Accept || status == NdcB.ParamAccepted) {
        val order = EntityRwService.findOne(
          "NdcOrder",
          Cq.and(listOf(Cq.eq("index", index), Cq.lt("status", NdcOrderState.TaskDone))),
          FindOptions(sort = listOf("-id"))
        )
        if (order == null) {
          logger.warn("B 消息上报的 index 找不到对应的未完成的 NDC 运单，index=$index")
          return
        }
        
        val update: EntityValue = mutableMapOf("index" to index)
        if (status == NdcB.Accept && order["status"] == NdcOrderState.Sent) update["status"] = NdcOrderState.SentR1
        if (status == NdcB.ParamAccepted && order["status"] == NdcOrderState.SentR1) update["status"] =
          NdcOrderState.SentR2
        EntityRwService.updateOne("NdcOrder", Cq.eq("id", order["id"]), update)
        logger.debug("Ba id=${order["id"]} update= ${JsonHelper.writeValueAsString(update)}")
      } else if (status == NdcB.ParamConfirmed) {
        logger.debug("B 消息上报的 “参数确认”，index=$index")
      }
      logger.debug("NdcAdapter 处理 NDS Ba 消息，完成")
    }
  }
  
  // 解析 B(b)
  @Synchronized // TODO 放到 worker 里执行
  fun parseBb(ctx: ChannelHandlerContext, frame: NdcFrame) {
    synchronized(this) {
      val type = frame.bodyBuf.readShort()
      val restLength = frame.bodyBuf.readShort()
      val index = frame.bodyBuf.readShort().toInt()
      val transportStructure = frame.bodyBuf.readByte()
      val status = frame.bodyBuf.readByte().toInt()
      val parNo = frame.bodyBuf.readByte()
      val spare = frame.bodyBuf.readByte()
      val ikey = frame.bodyBuf.readShort()
      logger.debug(
        "NdcAdapter 解析 NDS Bb 消息，type=0x${Integer.toHexString(type.toInt())}, restLength=$restLength, " +
          "index=$index, transportStructure=$transportStructure, status=0x${Integer.toHexString(status)}, " +
          "parNo=$parNo, spare=$spare, ikey=$ikey"
      )
      
      // 1 命令接受，17 参数值接受
      if (status == NdcB.Accept || status == NdcB.ParamAccepted) {
        val order = EntityRwService.findOne(
          "NdcOrder",
          Cq.and(listOf(Cq.eq("ikey", ikey), Cq.lt("status", NdcOrderState.TaskDone))),
          FindOptions(sort = listOf("-id"))
        )
        if (order == null) {
          logger.warn("B 消息上报的 iKey 找不到对应的未完成的 NDC 运单，iKey=$ikey，index=$index")
          return
        }
        
        val update: EntityValue = mutableMapOf("index" to index)
        // TODO 这里的两个 B 消息是一起发的还是先后发的
        if (status == NdcB.Accept && order["status"] == NdcOrderState.Sent) update["status"] = NdcOrderState.SentR1
        if (status == NdcB.ParamAccepted && order["status"] == NdcOrderState.SentR1) update["status"] =
          NdcOrderState.SentR2
        EntityRwService.updateOne("NdcOrder", Cq.eq("id", order["id"]), update)
        logger.debug("Bb id=${order["id"]} update= ${JsonHelper.writeValueAsString(update)}")
      } else if (status == NdcB.ParamConfirmed) {
        logger.debug("B 消息上报的 “参数确认”，index=$index，ikey=$ikey")
      }
      logger.debug("NdcAdapter 处理 NDS Bb 消息，完成")
    }
  }
  
  // S NDC 上报任务的执行情况
  fun parseS(ctx: ChannelHandlerContext, frame: NdcFrame) {
    val type = frame.bodyBuf.readShort() // 消息类型
    val length = frame.bodyBuf.readShort() // 剩余 body 长度
    val index = frame.bodyBuf.readShort().toInt() // index
    val transportStructure = frame.bodyBuf.readByte() // 运输结构
    val status = frame.bodyBuf.readByte() // 状态
    val magic = frame.bodyBuf.readShort().toInt() // magic
    val magic2 = frame.bodyBuf.readShort() // magic2
    logger.debug(
      "NdcAdapter 解析 NDS S 消息，type=0x${Integer.toHexString(type.toInt())}，length=$length，index=$index，" +
        "transportStructure=$transportStructure，status=0x${Integer.toHexString(status.toInt())}，" +
        "magic=$0x${Integer.toHexString(magic)}，magic2=0x${Integer.toHexString(magic2.toInt())}"
    )
    
    val order = EntityRwService.findOne(
      "NdcOrder",
      Cq.and(listOf(Cq.eq("index", index), Cq.lt("status", NdcOrderState.TaskDone))),
      FindOptions(sort = listOf("-id"))
    )
    if (order == null) {
      logger.warn("没有符合状态的 NdcOrder")
      return
    }
    val orderId = order["id"] ?: ""
    
    when (magic) {
      NdcS.AgvAssigned -> {}
      NdcS.LoadReq -> {
        EntityRwService.updateOne(
          "NdcOrder", Cq.eq("id", order["id"]), mutableMapOf("status" to NdcOrderState.ReadyToLoad)
        )
      }
      
      NdcS.LoadDone -> {
        EntityRwService.updateOne(
          "NdcOrder", Cq.eq("id", orderId), mutableMapOf("status" to NdcOrderState.LoadDoneS)
        )
      }
      
      NdcS.LoadOverTime -> {}
      NdcS.LoadNoGoods -> {}
      NdcS.UnloadReq -> {
        EntityRwService.updateOne(
          "NdcOrder", Cq.eq("id", order["id"]), mutableMapOf("status" to NdcOrderState.ReadyToUnload)
        )
      }
      
      NdcS.UnloadDone -> {
        EntityRwService.updateOne(
          "NdcOrder", Cq.eq("id", order["id"]), mutableMapOf("status" to NdcOrderState.UnloadDoneS)
        )
      }
      
      NdcS.UnloadOvertime -> {}
      NdcS.UnloadNoGoods -> {}
      NdcS.Deleted -> {
        // TODO NDC 任务被删除，需要终止猎鹰任务，清理库位
        EntityRwService.updateOne(
          "NdcOrder", Cq.eq("id", order["id"]), mutableMapOf("status" to NdcOrderState.Deleted)
        )
      }
      
      NdcS.Abort -> {}
      NdcS.Finished -> {
        EntityRwService.updateOne(
          "NdcOrder", Cq.eq("id", order["id"]), mutableMapOf("status" to NdcOrderState.TaskDone)
        )
      }
    }
    
    // 固定响应
    sendM(SendM(index, 1, 25, 0, 1, 0, 0, 0, 0))
  }
  
  
  // N 上位删除 AGV 命令
  fun sendN(index: Int) {
    
    val t = NdcMsgType.N
    
    val body: ByteBuf = Unpooled.buffer(t.bodyLength)
    body.writeShort(t.code)
    body.writeShort(t.bodyLength - 4)
    body.writeShort(index)
    
    val frame = NdcTcp.buildFrame(1, body)
    getClient().write(frame)
  }
  
  // M 改变局部参数；改变优先级
  fun sendM(req: SendM) {
    
    val t = NdcMsgType.M
    
    val body: ByteBuf = if (req.function != 4) Unpooled.buffer(6) else Unpooled.buffer(4)
    if (req.function != 4) {
      // 更新局部参数
      body.writeShort(t.code)
      body.writeShort(6)
      body.writeShort(req.index)
      body.writeByte(req.function)
      body.writeByte(req.parameterNumber)
      body.writeShort(req.p0)
    } else {
      // 更改优先级
      body.writeShort(t.code)
      body.writeShort(4)
      body.writeShort(req.index)
      body.writeByte(req.function)
      body.writeByte(req.parameterNumber)
    }
    
    val frame = NdcTcp.buildFrame(1, body)
    getClient().write(frame)
  }
  
  
  // TODO 在上层就转换
  private fun toNdcBin(binId: String): Int {
    return 0
  }
  
  fun buildBody(type: NdcMsgType, body: ByteBuf): CompositeByteBuf {
    val bodyHead: ByteBuf = Unpooled.buffer(body.readableBytes() + 4)
    
    
    val r: CompositeByteBuf = Unpooled.compositeBuffer()
    r.addComponent(true, bodyHead)
    r.addComponent(true, body)
    return r
  }
  
}

data class NdcCfg(
  val ip: String,
  val port: Int,
  val mock: Boolean? = false
)





