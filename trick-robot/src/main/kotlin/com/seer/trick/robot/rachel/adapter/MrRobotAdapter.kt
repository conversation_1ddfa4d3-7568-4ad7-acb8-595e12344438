package com.seer.trick.robot.rachel.adapter


import com.seer.trick.robot.map.SceneMapManager
import com.seer.trick.robot.rachel.MrCmdResult
import com.seer.trick.robot.rachel.MrRobotRuntime
import com.seer.trick.robot.rachel.MrStep

abstract class MrRobotAdapter {

  /**
   * 阻塞执行
   */
  abstract fun sendCmd(sceneMap: SceneMapManager, rr: MrRobotRuntime, step: MrStep): MrCmdResult

  /**
   * 不能抛异常。里面会 cmdFuture
   */
  abstract fun cancelCmd(rr: MrRobotRuntime)

  /**
   * 耗时。未知返回 null；不通返回 Double.MAX_VALUE
   */
  fun calcMoveCost(
    sceneMap: SceneMapManager, @Suppress("UNUSED_PARAMETER") rr: MrRobotRuntime, fromSite: String?, toSite: String?
  ): Double? {
    return sceneMap.calcCost(fromSite, toSite)
  }

  fun findBestStart(sceneMap: SceneMapManager, rr: MrRobotRuntime): String? {
    return rr.selfReport?.main?.let { sceneMap.findBestStart(it) }?.site?.id
  }

}