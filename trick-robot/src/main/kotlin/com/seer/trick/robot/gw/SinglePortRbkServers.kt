package com.seer.trick.robot.gw


import com.seer.trick.robot.vendor.seer.rbk.RbkEncoder
import com.seer.trick.robot.vendor.seer.rbk.RbkServer
import java.util.concurrent.ConcurrentHashMap

object SinglePortRbkServers {

  private val singlePortRbkServers: MutableMap<String, RbkServer> = ConcurrentHashMap()

  fun init(gwConfig: GwConfig) {
    for (sc in gwConfig.singlePortRbkServerConfigs) {
      if (sc.disabled) continue
      singlePortRbkServers[sc.robotName] = RbkServer(sc.port) { ctx, msg ->
        val req = GwRbkRequest(reqId = "", robotId = sc.robotName, rbkApiNo = msg.apiNo, reqStr = msg.bodyStr)

        try {
          val r = LocalRobots.requestRbk(req)
          val buf = RbkEncoder.buildReqBytes(msg.apiNo, r, msg.flowNo)
          ctx.writeAndFlush(buf)
        } catch (e: Exception) {
          ctx.channel().close() // TODO 对吗
        }
      }
    }
  }

  fun dispose() {
    for (s in singlePortRbkServers.values) {
      s.dispose()
    }
  }
}