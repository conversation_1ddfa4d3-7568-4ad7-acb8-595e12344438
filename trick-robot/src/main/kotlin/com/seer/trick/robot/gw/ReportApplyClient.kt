package com.seer.trick.robot.gw

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError

import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.http.WsMsg
import com.seer.trick.base.soc.SocAttention
import com.seer.trick.base.soc.SocService
import com.seer.trick.base.soc.SysMonitorService
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.StringHelper
import com.seer.trick.helper.getTypeMessage
import com.seer.trick.robot.light.ReportApplyReq
import com.seer.trick.robot.light.ReportApplyRes
import com.seer.trick.robot.light.ReportApplySelfReport
import com.seer.trick.robot.rachel.RobotVendor
import com.seer.trick.robot.sto.CreateStoReq
import com.seer.trick.robot.sto.StoManager
import com.seer.trick.robot.sto.StoStore
import com.seer.trick.robot.vendor.hai.HaiAdapter
import org.slf4j.LoggerFactory
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.Executors
import java.util.concurrent.Future

object ReportApplyClient {

  private val logger = LoggerFactory.getLogger(this::class.java)

  @Volatile
  private var disposed = false

  private val executor = Executors.newCachedThreadPool()

  private val futures: MutableList<Future<*>> = CopyOnWriteArrayList()

  @Volatile
  var rawReport: EntityValue? = null

  fun init(gwConfig: GwConfig) {
    if (!gwConfig.reportApplyMode.enabled) return
    disposed = false

    // 
    // 恢复当前运单
    for (local in gwConfig.localConfigs) {
      if (local.disabled) continue
      futures += executor.submit { loop(local.name, local.vendor) }
    }
  }

  @Synchronized
  fun dispose() {
    disposed = true

    for (f in futures) {
      try {
        f.cancel(true)
      } catch (e: Exception) {
        logger.error("dispose", e)
      }
    }
    futures.clear()
  }

  private fun loop(robotName: String, vendor: RobotVendor) {
    try {
      while (!disposed) {
        try {
          reportApply(robotName, vendor)

          SysMonitorService.log(
            subject = "GW",
            target = robotName,
            field = "ReportApply::loop",
            value = "loop success robot=$robotName",
            level = SysMonitorService.SysMonitorLevel.Info,
          )
          SocService.updateNode(
            "GW",
            "GW:ReportApply:$robotName",
            "网关:报告&申请任务:$robotName",
            "成功",
          )
        } catch (e: Exception) {
          // logger.error("reportApply", e)
          SysMonitorService.log(
            subject = "GW",
            target = robotName,
            field = "ReportApply::loop",
            value = "loop error robot=$robotName message：${e.getTypeMessage()}",
            level = SysMonitorService.SysMonitorLevel.Error,
          )

          SocService.updateNode(
            "GW",
            "GW:ReportApply:$robotName",
            "网关:报告&申请任务:$robotName",
            "失败：" + e.message,
            SocAttention.Red,
          )
        }
        Thread.sleep(GwCenter.gwConfig.reportApplyMode.reportDelay)
      }
    } finally {
      SysMonitorService.log(
        subject = "GW",
        target = robotName,
        field = "ReportApply::loop",
        value = "loop disposed robot=$robotName",
        level = SysMonitorService.SysMonitorLevel.Error,
        remove = true,
      )

      SocService.removeNode("GW:ReportApply:$robotName")
    }
  }

  private fun reportApply(robotName: String, vendor: RobotVendor) {
    

    rawReport = fetchSelfReport(robotName, vendor)

    val ros = StoManager.fetchRecentOrders(robotName)

    val req = ReportApplyReq(
      robotName,
      ReportApplySelfReport(rawReport),
      currentOrder = ros.currentOrder,
      lastOrder = ros.lastOrder,
      unfinishedOrders = ros.unfinishedOrders,
    )

    val res = send(req)
    if (res.nextOrder != null) {
      if (!StoStore.existsById(res.nextOrder.id)) {
        logger.info("网关收到下一运单，机器人=$robotName, 运单=${res.nextOrder.id}, 动作=${res.nextOrder.moves}")
        val order = CreateStoReq(res.nextOrder.id, vendor, robotName, res.nextOrder.seer3066, res.nextOrder.moves)
        StoManager.start(order)
      }
    }

    if (!res.cancelledOrderIds.isNullOrEmpty()) {
      logger.warn("从服务端取消简单运单，机器人=$robotName，运单=${res.cancelledOrderIds}")
      StoManager.cancelOrders(robotName, res.cancelledOrderIds)
    }

    if (!res.manualDoneOrderIds.isNullOrEmpty()) {
      logger.warn("从服务端手工完成简单运单，机器人=$robotName，运单=${res.manualDoneOrderIds}")
      StoManager.manualDoneOrders(robotName, res.manualDoneOrderIds)
    }
  }

  private val fetchReqBody = JsonHelper.mapper.writeValueAsString(mapOf("return_laser" to false))

  private val filterReportFields = listOf(
    "battery_level", "x", "y", "angle", "current_station", "current_map",
    "blocked", "charging", "current_lock", "confidence", "reloc_status",
    "move_status_info",
  )

  private val alarmFields = listOf("fatals", "errors", "warnings", "notices")

  private fun fetchSelfReport(robotName: String, vendor: RobotVendor): EntityValue? =
    if (vendor == RobotVendor.Seer) {
      SysMonitorService.log(
        subject = "GW",
        target = robotName,
        field = "ReportApply::fetchSelfReport",
        value = "fetchSelfReport start, robotName=$robotName",
      )

      try {
        val result = LocalRobots.requestRbk(GwRbkRequest("", robotName, 1100, fetchReqBody))

        SysMonitorService.log(
          subject = "GW",
          target = robotName,
          field = "ReportApply::fetchSelfReport",
          value = "fetchSelfReport result, robotName=$robotName, result=$result",
        )

        val originReport: EntityValue = JsonHelper.mapper.readValue(result, jacksonTypeRef())
        val simplifiedReport: EntityValue = mutableMapOf()
        for (fn in filterReportFields) simplifiedReport[fn] = originReport[fn]
        // 是否上报故障
        if (GwCenter.gwConfig.reportApplyMode.reportAlarm) {
          for (fn in alarmFields) simplifiedReport[fn] = originReport[fn]
        }
        // 是否有额外传输字段
        val extraField = BzConfigManager.getByPath("ScWcs", "light", "extraField") as String?
        StringHelper.splitTrim(extraField, ",").forEach { simplifiedReport[it] = originReport[it] }
        simplifiedReport
      } catch (_: Exception) {
        null
      }
    } else if (vendor == RobotVendor.Hai) {
      HaiAdapter.robots[robotName]?.report?.rawReport
    } else {
      val lc = GwCenter.gwConfig.localConfigs.firstOrNull { it.name == robotName }
        ?: throw BzError("errGwNoRobot", robotName)
      val report = HttpServer.getRobotReport(lc)
      report
    }

  private fun send(req: ReportApplyReq): ReportApplyRes {
    val urls = StringHelper.splitTrim(GwCenter.gwConfig.reportApplyMode.url, ",")
    if (urls.isEmpty()) throw BzError("errNoReportApplyUrl")

    val reqStr = JsonHelper.mapper.writeValueAsString(req)
    val wsReq = WsMsg("GwWsLightReportApplyRequest", reqStr)

    var lastError: Throwable? = null

    for (client in WsClients.clients) {
      try {
        val wsRes = client.client.request(wsReq)
        return JsonHelper.mapper.readValue(wsRes.content, jacksonTypeRef())
      } catch (e: Throwable) {
        lastError = e
      }
    }

    throw BzError(lastError, "errCodeErr", lastError?.message)
  }
}