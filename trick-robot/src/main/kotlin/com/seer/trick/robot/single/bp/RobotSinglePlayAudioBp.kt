package com.seer.trick.robot.single.bp


import com.seer.trick.falcon.domain.*
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper

class RobotSinglePlayAudioBp : AbstractTaskChainBp() {
  
  override fun process() {
    val station = mustGetBlockInputParam("station") as String
    val audioFilename = mustGetBlockInputParam("audioFilename") as String
    val loop = getBlockInputParamAsBool("loop")
    val reqId = getBlockInputParam("reqId") as String? ?: IdHelper.uuidStr()
    val req = JsonHelper.mapper.writeValueAsString(
      mapOf(
        "id" to station,
        "operation" to "sound",
        "sounds_args" to mapOf(
          "name" to audioFilename,
          "loop" to if (loop) 1 else 0
        )
      )
    )
    val gwRbkResult = executeTask(RbkRequest(reqId, 3051, req, "播放音频"))
    setBlockOutputParams(mapOf("rbkResult" to gwRbkResult))
  }
  
  companion object {
    
    val def = BlockDef(
      RobotSinglePlayAudioBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(
        BlockInputParamDef(
          "station", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.BinId, ParamObjectType.SiteId)
        ),
        BlockInputParamDef("audioFilename", BlockParamType.String, true),
        BlockInputParamDef("loop", BlockParamType.Boolean),
//        BlockInputParamDef("reqId", BlockParamType.String),
      ),
      outputParams = listOf(
        BlockOutputParamDef("rbkResult", BlockParamType.String)
      )
    )
    
  }
  
}