package com.seer.trick.robot.falcon.tom

import com.seer.trick.BzError

import com.seer.trick.falcon.FalconLogLevel
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*
import com.seer.trick.helper.StringHelper
import com.seer.trick.robot.tom.CreateTomOrderReq
import com.seer.trick.robot.tom.TomAgent

class CreateTomOrderBp : AbstractBp() {

  override fun process() {
    val tomId = mustGetBlockInputParam("tomId") as String
    val vehicle = getBlockInputParam("vehicle") as String?
    val group = getBlockInputParam("group") as String?
    val keyRouteStr = getBlockInputParam("keyRouteStr") as String?
    if (keyRouteStr.isNullOrBlank()) throw BzError("errTomOrderKeyRouteMissing")
    val keyRoute = StringHelper.splitTrim(keyRouteStr, ",")
    val priority = getBlockInputParamAsLong("priority")
    val keyTask = getBlockInputParam("keyTask") as String?
    val keyGoodsId = getBlockInputParam("keyGoodsId") as String?
    // 取货块数量。存在且大于0时，表示该订单需要至少 X 个空背篓才能执行，即该订单有 X 个取货动作块
    val loadBlockCount = getBlockInputParamAsLong("loadBlockCount")
    // 取消后是否封口
    val notMarkComplete = getBlockInputParamAsBool("notMarkComplete")
    val label = getBlockInputParam("label") as String?

    val tomUrl = TomAgent.getTomUrlRoot(tomId)
    var orderId = internalVariables["tomOrderId"] as String? ?: ""
    if (orderId.isNotBlank()) {
      log(FalconLogLevel.Info, "运单创建调度运单，但之前已有调度运单 '$orderId'")
      val order = TomAgent.awaitVehicle(tomUrl, orderId)
      if (order == null) {
        log(FalconLogLevel.Info, "调度运单 '$orderId' 没找到，丢弃，重新创建新运单")
        removeBlockInternalVariables("tomOrderId")
        removeResource("TomOrder", orderId)
      } else if (order.state == "STOPPED") {
        log(FalconLogLevel.Error, "调度运单 '$orderId' 状态为 STOPPED，丢弃，重新创建新运单")
        removeBlockInternalVariables("tomOrderId")
        removeResource("TomOrder", orderId)
      }
      // TODO FINISHED
    }

    orderId = getOrSetBlockInternalVariable("tomOrderId") {
      TomAgent.generateId(tomUrl)
    } as String

    val req = CreateTomOrderReq(
      id = orderId,
      vehicle = vehicle,
      group = group,
      keyRoute = keyRoute,
      priority = priority?.toInt(),
      keyTask = keyTask,
      keyGoodsId = keyGoodsId,
      loadBlockCount = loadBlockCount,
      label = label,
      externalId = taskRuntime.taskId, // 将猎鹰任务的任务 id 设置为 core 运单的外部单号, externalId 可以不唯一
    )
    addRelatedObject("TomOrder", orderId, tomId)
    log(FalconLogLevel.Info, "准备发送调度运单：$req")
    TomAgent.createOrder(tomUrl, req)
    log(FalconLogLevel.Info, "调度运单已发送：$orderId")

    // 如果没选,运单就封口
    if (!notMarkComplete) addResource("TomOrder", orderId, mapOf("tomId" to tomId, "orderId" to orderId))

    // 等派到车
    val order = TomAgent.awaitVehicle(tomUrl, orderId) ?: throw BzError("errTomOrderAwaitNotFound", orderId)
    if (order.vehicle.isNullOrBlank()) throw BzError("errTomOrderNoVehicle", orderId, order.state)
    log(FalconLogLevel.Info, "实际机器人：${order.vehicle}")
    addRelatedObject("Robot", order.vehicle, tomId)
    addActualVehicle(order.vehicle)
    setBlockOutputParams(mapOf("orderId" to orderId, "actualVehicle" to order.vehicle))
  }

  companion object {

    val def = BlockDef(
      CreateTomOrderBp::class.simpleName!!,
      color = "#C7DCA7",
      inputParams = listOf(
        BlockInputParamDef("tomId", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.RobotScene)),
        BlockInputParamDef("vehicle", BlockParamType.String, false, objectTypes = listOf(ParamObjectType.RobotName)),
        BlockInputParamDef("group", BlockParamType.String, false, objectTypes = listOf(ParamObjectType.RobotGroup)),
        BlockInputParamDef(
          "keyRouteStr",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.SiteId, ParamObjectType.BinId),
        ),
        BlockInputParamDef("priority", BlockParamType.Long, false),
        BlockInputParamDef("keyTask", BlockParamType.String, false),
        BlockInputParamDef("label", BlockParamType.String, false),
        BlockInputParamDef(
          "keyGoodsId",
          BlockParamType.String,
          false,
          objectTypes = listOf(ParamObjectType.ContainerId),
        ),
        BlockInputParamDef("loadBlockCount", BlockParamType.Long, false),
        BlockInputParamDef("notMarkComplete", BlockParamType.Boolean, false),
      ),
      outputParams = listOf(
        BlockOutputParamDef("orderId", BlockParamType.String, objectTypes = listOf(ParamObjectType.TomOrderId)),
        BlockOutputParamDef("actualVehicle", BlockParamType.String, objectTypes = listOf(ParamObjectType.RobotName)),
      ),
    )
  }
}