package com.seer.trick.robot.handler

import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.noAuth
import com.seer.trick.base.http.getReqBody
import io.javalin.http.Context
import org.slf4j.LoggerFactory

object YoubeiHandler {

  private val logger = LoggerFactory.getLogger(this::class.java)

  fun registerHandlers() {
    val c = Handlers("api/test/yb/")
    c.post("action-apply", YoubeiHandler::actionApply, noAuth())
    c.post("next", YoubeiHandler::next, noAuth())
    c.post("task-callback", YoubeiHandler::taskCallback, noAuth())
    c.post("action-callback", YoubeiHandler::actionCallBack, noAuth())
  }

  private var boxList = mutableListOf<String>()

  private fun actionApply(ctx: Context) {
    val req: Map<String, Any> = ctx.getReqBody()
    logger.info("action-apply = $req")

    val r: Map<String, Any> = if (boxList.isEmpty()) {
      mapOf(
        "code" to "error",
        "message" to "boxList is empty"
      )
    } else {
      val next = boxList[0]
      boxList.removeAt(0)
      mapOf(
        "code" to "ok",
        "boxNo" to next,
        "message" to ""
      )
    }

    ctx.json(r)
  }

  private fun next(ctx: Context) {
    val req: Map<String, Any> = ctx.getReqBody()
    logger.info("box arrive port = $req")

    boxList.add((req["boxNo"] ?: "").toString())
  }


  private fun taskCallback(ctx: Context) {
    val req: Map<String, Any> = ctx.getReqBody()
    logger.info("task-callback = $req")
  }

  private fun actionCallBack(ctx: Context) {
    val req: Map<String, Any> = ctx.getReqBody()
    logger.info("action-callback = $req")
  }


}