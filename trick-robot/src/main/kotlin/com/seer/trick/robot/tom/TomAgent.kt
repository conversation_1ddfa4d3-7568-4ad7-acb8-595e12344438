package com.seer.trick.robot.tom

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.fasterxml.jackson.module.kotlin.readValue
import com.seer.trick.BzError

import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.IdGenRule
import com.seer.trick.base.entity.id.IdGenManager
import com.seer.trick.base.failure.FailureLevel
import com.seer.trick.base.failure.FailureRecordReq
import com.seer.trick.base.failure.FailureRecorder
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.getTypeMessage
import com.seer.trick.robot.RobotAppManager
import com.seer.trick.robot.map.MrSceneArea
import com.seer.trick.robot.rachel.MrRobotAlert
import com.seer.trick.robot.rachel.MrRobotAlertLevel
import com.seer.trick.robot.rachel.MrRobotSelfReportMain
import com.seer.trick.robot.vendor.seer.SeerHelper
import com.seer.trick.robot.vendor.seer.SmapIO
import okhttp3.HttpUrl.Companion.toHttpUrlOrNull
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import okhttp3.ResponseBody
import org.apache.commons.io.IOUtils
import org.slf4j.LoggerFactory
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.*

// 动作快状态：
// 已创建=CREATED，
// 正在执行=RUNNING，
// 等待=WAITING（等待拼单或者放行信号），
// 正在回调=POSTING（正在进行消息回调流程），
// 暂停=SUSPENDED，
// 完成=FINISHED，
// 失败=FAILED，
// 终止=STOPPED，
// 手动终止=MANUAL_FINISHED，
// 无法执行=Error(参数错误)

/**
 * 封装与调度（CORE）的交互
 */
object TomAgent {

  private val logger = LoggerFactory.getLogger(this::class.java)

  private val JSON = "application/json; charset=utf-8".toMediaType()

  private val client = OkHttpClient() // 线程安全？

  fun createOrder(uriRoot: String, req: CreateTomOrderReq): String =
    postCommonRequest("$uriRoot/setOrder", req)

  fun addBlocks(uriRoot: String, req: AddBlocksReq): String =
    postCommonRequest("$uriRoot/addBlocks", req)

  fun markComplete(uriRoot: String, req: MarkCompleteReq): String =
    postCommonRequest("$uriRoot/markComplete", req)

  fun terminate(uriRoot: String, req: TerminateReq): String =
    postCommonRequest("$uriRoot/terminate", req)

  data class QueryOrderPageOptions(
    val vehicle: String? = null,
    val state: List<String>? = null,
    val notCompleteOnly: Boolean = false,
    val externalId: String? = null,
  )

  /*
* 读取机器人DI值
* @param urlRoot     调度URL
* @param robot       机器人Id
* @param id          DI/DO 编号
*/
  fun readRobotIO(urlRoot: String, robot: String, id: Int): String =
    getRobotDI(urlRoot, robot, id)

  /*
   * 等待机器人DI值
   * @param urlRoot     调度URL
   * @param robot       机器人Id
   * @param timeOut     超时时间(ms)
   * @param id          DI/DO 编号
   * @param status true: 开启 false：关闭
   */
  fun waitRobotIO(urlRoot: String, robot: String, timeOut: Long?, id: Int, status: Boolean): String {
    var robotDI = getRobotDI(urlRoot, robot, id)
    if (timeOut != null) {
      val startTime = System.currentTimeMillis()
      while (!Thread.interrupted()) {
        robotDI = getRobotDI(urlRoot, robot, id)
        val elapsedTime = System.currentTimeMillis() - startTime
        if (robotDI == status.toString() || elapsedTime >= timeOut) {
          break
        }
        Thread.sleep(100)
      }
    } else {
      while (!Thread.interrupted()) {
        robotDI = getRobotDI(urlRoot, robot, id)
        if (robotDI == status.toString()) {
          break
        }
        Thread.sleep(100)
      }
    }
    return robotDI
  }

  /*
   * 删除字符串中包裹的双引号
   */
  fun removeQuotes(input: String): String = if (input.startsWith("\"") && input.endsWith("\"")) {
    input.substring(1, input.length - 1)
  } else {
    input
  }

  /*
   * 获取机器人DI
   */
  private fun getRobotDI(urlRoot: String, robot: String, id: Int): String {
    val url = "$urlRoot/robotsStatus"
    val request = Request.Builder().url(url).get().build()
    val resStr = fetchResponseAsString(request)
    val jn = JsonHelper.mapper.readTree(resStr)
    var robotDI = "null"

    jn["report"]?.asIterable()?.forEach {
      if (it.isNull) return@forEach
      val robotId = it["uuid"].toString()
      val rbkNode = it["rbk_report"]
      if (removeQuotes(robotId) == robot) {
        val arrayDI = rbkNode["DI"]
        robotDI = arrayDI[id]["status"].toString()
      }
    }
    return robotDI
  }

  fun queryOrderPage(
    
    uriRoot: String,
    pageNo: Int,
    pageSize: Int,
    o: QueryOrderPageOptions?,
  ): TomQueryPageResult {
    val where = o?.let {
      val filters = mutableListOf<Any>()
      if (!o.vehicle.isNullOrBlank()) {
        filters.add(listOf("vehicle", "EQ", o.vehicle))
      }
      if (!o.state.isNullOrEmpty()) {
        filters.add(listOf("state", "IN", "(" + o.state.joinToString(", ") { "\"$it\"" } + ")"))
      }
      if (o.notCompleteOnly) {
        filters.add(listOf("complete", "EQ", "0"))
      }
      if (!o.externalId.isNullOrEmpty()) { // 根据运单 id 查询
        filters.add(listOf("externalId", "EQ", o.externalId))
      }
      if (filters.isNotEmpty()) {
        JsonHelper.writeValueAsString(mapOf("relation" to "AND", "predicates" to filters))
      } else {
        null
      }
    }
    val b = "$uriRoot/orders".toHttpUrlOrNull()!!.newBuilder()
      .addQueryParameter("page", pageNo.toString())
      .addQueryParameter("size", pageSize.toString())
      .addQueryParameter("orderBy", "receiveTime")
      .addQueryParameter("orderMethod", "descending")
    if (!where.isNullOrBlank()) b.addQueryParameter("where", where)
    val url = b.build()

    val request = Request.Builder().url(url).get().build()
    val resStr = fetchResponseAsString(request)
    return JsonHelper.mapper.readValue(resStr, jacksonTypeRef())
  }

  fun queryOrder(uriRoot: String, orderId: String): TomQueryOrderResult? {
    var count = 1
    val maxCount = 3
    while (count <= maxCount) {
      Thread.sleep(1000)
      val request = Request.Builder().url("$uriRoot/orderDetails/$orderId").get().build()
      try {
        val resStr = fetchResponseAsString(request)
        return JsonHelper.mapper.readValue(resStr, jacksonTypeRef())
      } catch (e: BzError) {
        logger.error("查询运单 $orderId 报错，重试第 $count 次", e)
        count++
        if (count > maxCount) {
          logger.error("查询运单 $orderId 报错超过 $maxCount 次，返回失败", e)
          if (e.code == "wcs_err_tom_HttpError404") {
            return null
          } else {
            throw e
          }
        }
      }
    }
    return null
  }

  fun queryOrderActualVehicle(uriRoot: String, orderId: String): String? {
    // todo 网络波动异常的处理
    val or = queryOrder(uriRoot, orderId)
    return or?.vehicle
  }

  fun awaitVehicle(tomUrl: String, orderId: String): TomQueryOrderResult? {
    // 需要抛出异常，避免中断被吞掉
    // todo 网络波动异常的处理
    var notFoundCount = 0
    try {
      while (true) {
        val order = queryOrder(tomUrl, orderId)
        if (order == null) {
          notFoundCount++
          if (notFoundCount > 10) return null
          Thread.sleep(500)
        } else {
          notFoundCount = 0
          if (!order.vehicle.isNullOrBlank()) return order
          val state = order.state
          // 如果都已经终态了，不再等
          if (state == "FINISHED" || state == "FAILED" || state == "STOPPED" || state == "Error") return order
          Thread.sleep(500)
        }
      }
    } catch (e: Exception) {
      // 对于中断可能会有 InterruptedException、InterruptIoException
      throw e
    }
  }

  fun awaitBlock(
    
    tomUrl: String,
    orderId: String,
    blockId: String,
  ): Pair<TomQueryOrderResult?, TomQueryBlockResult?> {
    try {
      // 修改的原因是原本的代码，将各种异常的处理逻辑耦合了，Thread.interrupted() 会清除中断状态，导致异常没被正确的抛出
      // 尤其的是可能会出现，同样的中断，某个时刻被抛出了，某个时刻没被抛出。
      // todo queryOrder 时，假如由于网络波动，导致请求失败，此时是会进入故障状态，等待人工处理，但实际上此时自动重连然后告警是最好的
      var notFoundCount = 0
      while (true) {
        val order = queryOrder(tomUrl, orderId)
        val block = order?.blocks?.find { it.blockId == blockId }
        if (order == null || block == null) {
          notFoundCount++
          if (notFoundCount > 10) return Pair(order, null)
          Thread.sleep(500)
        } else {
          notFoundCount = 0
          if (block.state == "FINISHED") {
            return Pair(order, block)
          } else if (block.state == "FAILED" ||
            block.state == "STOPPED" ||
            block.state == "MANUAL_FINISHED" ||
            block.state == "Error"
          ) {
            return Pair(order, block)
          }
          Thread.sleep(500)
        }
      }
    } catch (e: Exception) {
      logger.error("执行运单遇到错误，报错'$orderId':", e)
      throw e
    }
  }

  /**
   * 等待 CORE 运单状态变为终态
   * 有 errors 的时候，表示有错误发生，不认为是终态
   */
  fun awaitBlockFinalState(tomId: String, orderId: String, blockId: String): Boolean {
    val order = queryOrder(tomId, orderId)
    val block = order?.blocks?.find { it.blockId == blockId }
    if (order != null && block != null && order.errors.isNullOrEmpty()) {
      if (block.state == "FINISHED" ||
        block.state == "MANUAL_FINISHED" ||
        block.state == "STOPPED" ||
        block.state == "Error"
      ) {
        return true
      }
    }
    return false
  }

  data class RedoFailedOrderReq(val vehicles: List<String>)

  fun redoFailedOrder(uriRoot: String, req: RedoFailedOrderReq): String =
    postCommonRequest("$uriRoot/redoFailedOrder", req)

  data class ManualFinishedWithContainersReq(val vehicles: List<ManualFinishedWithContainer>)

  data class ManualFinishedWithContainer(val name: String, val containerName: String)

  data class ManualFinishedWithoutContainersReq(val vehicles: List<String>)

  fun manualFinishedWithContainers(uriRoot: String, req: ManualFinishedWithContainersReq): String =
    postCommonRequest("$uriRoot/manualFinished", req)

  fun manualFinishedWithoutContainers(
    
    uriRoot: String,
    req: ManualFinishedWithoutContainersReq,
  ): String = postCommonRequest("$uriRoot/manualFinished", req)

  @Suppress("UNCHECKED_CAST")
  fun getTomUrlRoot(tomId: String?): String {
    val toms = BzConfigManager.getByPath("ScWcs", "seer", "toms") as List<*>?
    val tom = if (tomId.isNullOrBlank()) {
      toms?.firstOrNull()
    } else {
      toms?.find {
        val item = it as EntityValue?
        item?.get("id") == tomId
      }
    } ?: throw BzError("wcs_err_TomNotFound", tomId)
    val tomEv = tom as EntityValue
    val url = tomEv["url"] as String?
    if (url.isNullOrBlank()) throw BzError("wcs_err_TomNoUrl", tomId)
    return url
  }

  private fun postCommonRequest(path: String, req: Any): String {
    val reqStr = JsonHelper.mapper.writeValueAsString(req)
    val request = Request.Builder().url(path)
      .post(reqStr.toRequestBody(JSON)).build()
    return fetchResponseAsString(request)
  }

  // 返回响应正文，调用者记的关闭 response
  private fun fetchResponseBody(request: Request): ResponseBody {
    try {
      val response: Response?
      val url = request.url.toString()
      try {
        response = client.newCall(request).execute()

        if (response.isSuccessful) {
          return response.body ?: throw BzError("wcs_err_tom_TomResponseEmpty", url)
        } else {
          val resStr = response.body?.string()
          if (response.code == 400) {
            val res: TomResult = JsonHelper.mapper.readValue(resStr, jacksonTypeRef())
            if (res.code == 50003 && url.endsWith("addBlocks")) {
              throw BzError("wcs_err_tom_Completed_No_Path")
            }
            throw BzError("wcs_err_tom_TomError", res.code, res.msg, url)
          } else if (response.code == 404) {
            throw BzError("wcs_err_tom_HttpError404", url)
          } else {
            throw BzError("wcs_err_tom_HttpError", response.code, resStr, url)
          }
        }
      } catch (e: IOException) {
        // 直接抛出，避免 interruptIoException 被包装成其他异常被吞掉，进而导致被处理成其他的异常，资源未被清理
        throw e
      } catch (e: Exception) {
        throw BzError("wcs_err_tom_OtherError", e.getTypeMessage(), url)
      }
    } catch (e: BzError) {
      FailureRecorder.addAsync(
        
        FailureRecordReq(kind = "CoreError", subKind = "Call", level = FailureLevel.Error, desc = e.message ?: ""),
      )
      throw e
    }
  }

  // 返回响应正文
  private fun fetchResponseAsString(request: Request): String {
    val resStr = fetchResponseBody(request).use { it.string() }
    if (resStr.isNotBlank()) {
      return resStr
    } else {
      throw BzError("wcs_err_tom_TomResponseEmpty", request.url.toString())
    }
  }

  private fun fetchTomRobotStatus(tomEv: EntityValue): TomRuntimeRecord? {
    val id = tomEv["id"] as String? ?: return null
    val urlRoot = tomEv["url"] as String? ?: return null

    return fetchTomRobotStatus(id, urlRoot)
  }

  fun fetchTomRobotStatus(tomId: String, urlRoot: String): TomRuntimeRecord {
    val url = "$urlRoot/robotsStatus".toHttpUrlOrNull()!!.newBuilder().build()
    val request = Request.Builder().url(url).get().build()
    try {
      val resStr = fetchResponseAsString(request)
      val jn = JsonHelper.mapper.readTree(resStr)

      val alarmsNode = jn["alarms"]
      val alarms = fetchRobotStatuses(alarmsNode)

      val robots: List<TomRobotRecord> = jn["report"]?.asIterable()?.map {
        if (it.isNull) return@map null
        val robotId = it["uuid"].asText()
        val online = it["connection_status"].asInt() == 1
        val rbkNode = it["rbk_report"]
        val rbkReport: EntityValue? = if (rbkNode == null || rbkNode.isNull) {
          null
        } else {
          // TODO 性能优化
          JsonHelper.mapper.readValue(JsonHelper.writeValueAsString(rbkNode))
        }
        // 兼容控制权
        val reasonNode = it["undispatchable_reason"]
        val reason: EntityValue? = if (reasonNode == null || reasonNode.isNull) {
          null
        } else {
          JsonHelper.mapper.readValue(JsonHelper.writeValueAsString(reasonNode))
        }
        val currentLock = rbkReport?.get("lock_info") as? MutableMap<String, Any?>
        rbkReport?.set("current_lock", currentLock)
        if (reason?.get("unlock") == 0) {
          currentLock?.set("nick_name", "SEER-CORE")
        }
        // 拿到core场景区域
        val basicInfoNode = it["basic_info"]?.get("current_area")?.let { node ->
          if (node.isArray) {
            node.mapNotNull { it.asText() }
          } else {
            emptyList()
          }
        } ?: emptyList()

        val scene = RobotAppManager.mustGetScene(tomId)

        val mainReport = if (rbkReport != null) {
          SeerHelper.rawToMainSeer(rbkReport, basicInfoNode.joinToString(separator = ","), scene.map)
        } else {
          null
        }

        val currentOrder = it["current_order"]?.get("id")?.asText()

        val dispatchable = it["dispatchable"]?.asBoolean() ?: false

        val procBusiness = it["procBusiness"]?.asBoolean() ?: false

        val isLoaded = it["isLoaded"]?.asBoolean() ?: false

        TomRobotRecord(
          robotId,
          online,
          rbkReport,
          mainReport,
          currentOrder = currentOrder,
          dispatchable = dispatchable,
          procBusiness = procBusiness,
          loaded = isLoaded,
        )
      }?.filterNotNull() ?: emptyList()

      val sceneMd5 = jn["scene_md5"]?.asText() ?: ""

      return TomRuntimeRecord(tomId, urlRoot, robots, alarms, sceneMd5)
    } catch (e: Exception) {
      return TomRuntimeRecord(tomId, urlRoot, error = true, errorMsg = e.message)
    }
  }

  private fun fetchRobotStatuses(alarmsNode: JsonNode?): List<MrRobotAlert> {
    // TODO 性能优化
    val rawReport: EntityValue = if (alarmsNode == null || alarmsNode.isNull) {
      return emptyList()
    } else {
      JsonHelper.mapper.readValue(JsonHelper.writeValueAsString(alarmsNode))
    }
    val alarms: MutableList<MrRobotAlert> = ArrayList()
    SeerHelper.parseSeerAlert(rawReport["fatals"], MrRobotAlertLevel.Fatal, alarms)
    SeerHelper.parseSeerAlert(rawReport["errors"], MrRobotAlertLevel.Error, alarms)
    SeerHelper.parseSeerAlert(rawReport["warnings"], MrRobotAlertLevel.Warning, alarms)
    SeerHelper.parseSeerAlert(rawReport["notices"], MrRobotAlertLevel.Info, alarms)
    return alarms
  }

  fun fetchMap(uriRoot: String, vehicle: String, map: String): MrSceneArea {
    val req = mapOf("vehicle" to vehicle, "map" to "$map.smap") // 加后缀！
    val resStr = postCommonRequest("$uriRoot/robotSmap", req)

    val n = JsonHelper.mapper.readTree(resStr)
    return SmapIO.importSmap(n)
  }

  fun downloadScene(urlRoot: String, file: File) {
    val url = "$urlRoot/downloadScene".toHttpUrlOrNull()!!.newBuilder().build()
    val request = Request.Builder().url(url).get().build()
    fetchResponseBody(request).use { response ->
      FileOutputStream(file).use { os -> IOUtils.copy(response.byteStream(), os) }
    }
  }

  /**
   * 确认重定位
   */
  fun reLocConfirm(uriRoot: String, robots: List<String>): String =
    postCommonRequest("$uriRoot/reLocConfirm", mapOf("vehicles" to robots))

  /*
   * 改变机器人接单状态
   * @param status dispatchable:小车可接单 undispatchable_unignore:小车不可接单，但是占用资源 undispatchable_ignore车不可接单也不占用资源
   */
  fun changeDispatchMode(uriRoot: String, robots: List<String>, status: String): String =
    postCommonRequest("$uriRoot/dispatchable", mapOf("vehicles" to robots, "type" to status))

  /*
   * 抢占控制权
   */
  fun dominateControl(uriRoot: String, robots: List<String>): String =
    postCommonRequest("$uriRoot/lock", mapOf("vehicles" to robots))

  /*
   * 释放控制权
   */
  fun releaseControl(uriRoot: String, robots: List<String>): String =
    postCommonRequest("$uriRoot/unlock", mapOf("vehicles" to robots))

  /*
   * 暂停导航
   */
  fun gotoSitePause(uriRoot: String, robots: List<String>): String =
    postCommonRequest("$uriRoot/gotoSitePause", mapOf("vehicles" to robots))

  /*
   * 继续导航
   */
  fun gotoSiteResume(uriRoot: String, robots: List<String>): String =
    postCommonRequest("$uriRoot/gotoSiteResume", mapOf("vehicles" to robots))

  /*
   * 重定位
   * @param xAxis 世界坐标系中的 x 坐标, 单位 m
   * @param yAxis 世界坐标系中的 x 坐标, 单位 m
   * @param angle 世界坐标系中的角度, 单位 rad
   * @param length 重定位 区域半径，单位 m
   */
  fun reLoc(
    
    uriRoot: String,
    robot: String,
    xAxis: Double,
    yAxis: Double,
    angle: Double,
    length: Double,
  ): String = postCommonRequest(
    
    "$uriRoot/reloc",
    mapOf("vehicle" to robot, "x" to xAxis, "y" to yAxis, "angle" to angle, "length" to length),
  )

  /*
   * 清除机器人错误
   */
  fun clearRobotAllError(uriRoot: String, robots: List<String>): String =
    postCommonRequest("$uriRoot/clearRobotAllError", mapOf("vehicles" to robots))

  /*
   * 清除机器人第三方错误
   */
  fun clearThirdError(uriRoot: String, robot: Any): String =
    postCommonRequest("$uriRoot/clear3rdError", mapOf("vehicle" to robot))

  /*
   * 清除机器人第三方Warning
   */
  fun clearThirdWarning(uriRoot: String, robot: String): String =
    postCommonRequest("$uriRoot/clear3rdWarning", mapOf("vehicle" to robot))

  /*
   * 软急停
   * @param status true: 设置软急停 false：取消软急停
   */
  fun setSoftIOEMC(uriRoot: String, robot: String, status: Boolean): String =
    postCommonRequest("$uriRoot/setSoftIOEMC", mapOf("vehicle" to robot, "status" to status))

  /*
   * 设置机器人IO
   * @param id     DI/DO 编号
   * @param status true: 开启 false：关闭
   */
  fun setRobotIO(uriRoot: String, robot: String, type: TomRobotIO, id: Int, status: Boolean): String =
    postCommonRequest(
      
      "$uriRoot/setRobotIO",
      mapOf("vehicle" to robot, "status" to status, "id" to id, "type" to type.name),
    )

  fun generateId(uriRoot: String): String {
    val idGenRule = IdGenRule(true, "CO-", 6)
    var orderId: String
    while (true) {
      orderId = IdGenManager.generateId(idGenRule)
      val request = Request.Builder().url("$uriRoot/orderDetails/$orderId").get().build()
      try {
        val response = client.newCall(request).execute()
        val resStr = response.body?.string()
        if (response.isSuccessful) {
          logger.info("core 单号：$orderId 已存在")
        } else {
          val res: TomResult = JsonHelper.mapper.readValue(resStr, jacksonTypeRef())
          if (response.code == 404 && res.code == 50002) {
            break
          } else {
            logger.error("Unknown exception, code: ${response.code}, message: ${response.message}")
            throw BzError("errHttpFail", response.code, response.message)
          }
        }
      } catch (e: IOException) {
        throw BzError("wcs_err_tom_IOError", e.getTypeMessage(), request.url.toString())
      } catch (e: Exception) {
        throw BzError("wcs_err_tom_OtherError", e.getTypeMessage(), request.url.toString())
      }
    }

    return orderId
  }

  /*
   * 设置 core 运单优先级
   */
  fun setOrderPriority(uriRoot: String, orderId: String, priority: Int): String =
    postCommonRequest("$uriRoot/setPriority", mapOf("id" to orderId, "priority" to priority))
}

data class CreateTomOrderReq(
  val id: String, // 运单 ID，需保证宇宙唯一，可以是任意值，对长度没有限制
  val externalId: String? = null, // 外部单号，可以不唯一
  val vehicle: String? = null, // 指定机器人
  val group: String? = null, // 指定机器人组
  val label: String? = null, // 指定机器人标签
  val keyRoute: List<String>? = null, // 关键点，用于辅助确定派单机器人；若不填，则系统会根据当前运行情况自动选择合适的机器人派单。建议填写。关键点不能是 SELF_POSITION
  val keyTask: String? = null, // 为 "load"或者"unload"，如果填写其他字段会被自动忽略，用于辅助确定派单机器人；若不填，则系统会根据当前运行情况自动选择合适的机器人派单
  val keyGoodsId: String? = null, // 针对料箱车 n+1 的场景，辅助 core 最后一个取货第一个放
  val loadBlockCount: Long? = null, // 取货块数量。存在且大于 0 时，表示该订单需要至少 X 个空背篓才能执行，即该订单有 X 个取货动作块
  val priority: Int? = null, // 运单优先级，数字越大代表订单优先级越高
  val complete: Boolean = false, // 运单是否封口
  val prePointRedo: Boolean? = false, // 触发动作块重做策略，机器人是否要先去前置点
  val mapfPriority: Int? = null, // mapf 框架下机器人执行订单避让优先级
  val blocks: List<TomBlock> = emptyList(), // 运单的动作块
)

data class AddBlocksReq(val id: String, val blocks: List<TomBlock>, val complete: Boolean = false)

// 请求数据中的 blocks 可由两种形式的动作块组成，目的地为地图点位和目的地为地图库位，如上图所示，其中目的地为地图点位的动作块又可分为机器人去目标点执行机构动作或执行脚本，因此 blocks 一共支持三种不同方式的数据结构：
// 1. 目的地为地图点位，去目标点位执行机器人机构动作
// 2. 目的地为地图点位，去目标点位执行机器人脚本
// 3. 目的地为地图库位，料箱机器人去目标库位取放货，或去目的库位执行此库位配置的库位动作
data class TomBlock(
  val blockId: String = "", // 动作块 ID
  val location: String = "", // 目的地名称，站点名，或库位。例如：AP28。如果为 SELF_POSITION 表示该动作为原地动作
  //
  val operation: String? = null, // 执行机构动作 https://seer-group.yuque.com/pf4yvd/ruzsiq/asupc6#BGbKQ
  val operationArgs: Any? = null, // 动作参数 https://seer-group.yuque.com/pf4yvd/ruzsiq/asupc6#BGbKQ
  //
  val scriptName: String? = null, // 脚本名称，机器人中的脚本名称
  val scriptArgs: Any? = null, // 脚本中定义的参数 https://seer-group.yuque.com/pf4yvd/ruzsiq/asupc6#oFdre
  //
  val binTask: String? = null,
  val goodsId: String? = null, // 货物编号，上位系统可以通过此参数管理货物状态。可不填，如不填，系统会自动生成随机编号
  val preBinTask: String? = null, // 当机器人到达最后一段线路前，会依据 binTask 对应键中的 JSON，执行对应动作。
  //
  val postAction: Any? = null, // 此字段为 block 完成状态回调
  val nextLocation: String? = null, // 发单发到前置点时，实际要去的库位的编号
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class MarkCompleteReq(
  val id: String? = null, // 需要通知封口的运单 ID，用于通知单个运单封口
  val idList: List<String>? = null, // 需要封口的运单 ID 列表，用于通知多个运单封口
)

data class TerminateReq(val idList: List<String>, val disableVehicle: Boolean = false)

@JsonIgnoreProperties(ignoreUnknown = true)
data class TomResult(
  val code: Int,
  @JsonAlias("create_on")
  val createdOn: Date?,
  val msg: String? = null,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class TomQueryPageResult(val page: Int, val size: Int, val total: Int, val list: List<TomQueryOrderResult>)

@JsonIgnoreProperties(ignoreUnknown = true)
data class TomQueryOrderResult(
  val id: String,
  val priority: Int?,
  val complete: Boolean = false,
  val receiveTime: Long? = null,
  val group: String? = null,
  val state: String? = null,
  val terminalTime: Long? = null,
  val vehicle: String? = null,
  val blocks: List<TomQueryBlockResult>? = null,
  val errors: List<TomError>? = null,
  val externalId: String? = null,
)

data class TomError(val code: Int? = null, val desc: String? = null)

@JsonIgnoreProperties(ignoreUnknown = true)
data class TomQueryBlockResult(
  val blockId: String = "",
  val state: String = "",
  val location: String = "",
  val operation: String = "",
  val binTask: String = "",
  val goodsId: String = "",
  val containerName: String? = null, // 货物所在的背篓名称
  @JsonAlias("script_name")
  val scriptName: String = "",
  val recognize: Boolean? = null,
)

/**
 * 从调度读取到到最新状态
 */
data class TomRuntimeRecord(
  val tomId: String,
  val uriRoot: String,
  val robots: List<TomRobotRecord> = emptyList(),
  val alarms: List<MrRobotAlert> = emptyList(), // 调度告警
  @JsonAlias("scene_md5")
  val sceneMd5: String = "",
  val error: Boolean = false,
  val errorMsg: String? = null,
  val fetchOn: Date = Date(),
)

class TomRobotRecord(
  @JvmField
  val id: String, // vehicle_id
  @JvmField
  val online: Boolean,
  @JvmField
  val rbkReport: EntityValue?,
  @JvmField
  val mainReport: MrRobotSelfReportMain?,
  @JvmField
  val currentOrder: String?, // 当前调度运单
  @JvmField
  val dispatchable: Boolean, // 调度是否可控机器人
  @JvmField
  val procBusiness: Boolean, // 是否正在执行用户下发的运单
  @JvmField
  val loaded: Boolean, // 机器人是否载货中
  // val chassis: EntityValue?,
)

enum class TomRobotIO {
  DO,
  DI, // DI 指虚拟 DI 不然不能设置
}