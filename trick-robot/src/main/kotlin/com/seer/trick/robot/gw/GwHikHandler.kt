package com.seer.trick.robot.gw

import com.seer.trick.BzError
import com.seer.trick.base.http.getReqBody
import com.seer.trick.helper.NumHelper
import com.seer.trick.robot.vendor.hik.*
import com.seer.trick.robot.vendor.hik.HikAdapter.updateLockStatus
import io.javalin.http.Context

object GwHikHandler {

  private const val GW_HIK_OWNER_NICKNAME = "gw-hik-owner-nickname"

  fun move(ctx: Context) {
    val req: Move322Req = ctx.getReqBody()
    req.robotNo = NumHelper.anyToInt(req.robotId) ?: throw BzError("errCodeErr", "robotId ${req.robotId} not an int")
    HikAdapter.move322(req)
    ctx.status(200)
  }

  fun moveV4(ctx: Context) {
    val req: List<Move322Req> = ctx.getReqBody()
    req.forEach {
      it.robotNo = NumHelper.anyToInt(it.robotId)
        ?: throw BzError("errCodeErr", "robotId ${it.robotId} not an int")
    }
    HikAdapter.move322V4(req)
    ctx.status(200)
  }

  fun moveV5(ctx: Context) {
    val reqs: List<Move322Req> = ctx.getReqBody()
    if (reqs.isEmpty()) return

    // 检查全通过后再追加到机器人任务 list，否则全扔掉，保证原子性
    synchronized(HikAdapter) {
      // 1 检查
      // 1.1 指定的机器人存在
      // 1.2 有控制权
      for (req in reqs) req.robotNo = ensureRobotExistAndUnderControl(robotId = req.robotId, ctx)

      // 追加到 robot.tasks
      for (req in reqs) {
        val robot = HikAdapter.robots[req.robotNo] ?: throw BzError("errNoRobot", req.robotNo)
        robot.tasks += req // TODO 缓存的模式：覆盖 or 追加
      }
    }

    ctx.status(200)
  }

  private fun ensureRobotExistAndUnderControl(robotId: String, ctx: Context): Int {
    val robotNo: Int = NumHelper.anyToInt(robotId) ?: throw BzError("errCodeErr", "robotId $robotId not an int")
    val robot = HikAdapter.robots[robotNo] ?: throw BzError("errNoRobot", robotId)

    val nickname = ctx.cookie(GW_HIK_OWNER_NICKNAME)
    if (robot.nickName != nickname) throw BzError("errNoRobot", "No control to robot $robotNo")
    return robotNo
  }

  fun jackLoad(ctx: Context) {
    val req: Load312Req = ctx.getReqBody()
    req.robotNo = NumHelper.anyToInt(req.robotId) ?: throw BzError("errCodeErr", "robotId ${req.robotId} not an int")
    HikAdapter.jackLoad312(req)
  }

  fun jackLoadV5(ctx: Context) {
    val req: Load312Req = ctx.getReqBody()
    req.robotNo = ensureRobotExistAndUnderControl(req.robotId, ctx)
    HikAdapter.jackLoad312(req)
  }

  fun jackUnload(ctx: Context) {
    val req: Unload314Req = ctx.getReqBody()
    req.robotNo = NumHelper.anyToInt(req.robotId) ?: throw BzError("errCodeErr", "robotId ${req.robotId} not an int")
    HikAdapter.jackUnload314(req)
  }

  fun jackUnloadV5(ctx: Context) {
    val req: Unload314Req = ctx.getReqBody()
    req.robotNo = ensureRobotExistAndUnderControl(req.robotId, ctx)
    HikAdapter.jackUnload314(req)
  }

  fun jackMove(ctx: Context) {
    val req: LoadMove324Req = ctx.getReqBody()
    req.robotNo = NumHelper.anyToInt(req.robotId) ?: throw BzError("errCodeErr", "robotId ${req.robotId} not an int")
    HikAdapter.jackMove324(req)
  }

  fun jackMoveV5(ctx: Context) {
    val req: LoadMove324Req = ctx.getReqBody()
    req.robotNo = ensureRobotExistAndUnderControl(req.robotId, ctx)
    HikAdapter.jackMove324(req)
  }

  fun charge(ctx: Context) {
    val req: Charge32cReq = ctx.getReqBody()
    req.robotNo = NumHelper.anyToInt(req.robotId) ?: throw BzError("errCodeErr", "robotId ${req.robotId} not an int")
    HikAdapter.charge32c(req)
  }

  fun chargeV5(ctx: Context) {
    val req: Charge32cReq = ctx.getReqBody()
    req.robotNo = ensureRobotExistAndUnderControl(req.robotId, ctx)
    HikAdapter.charge32c(req)
  }

  fun pause(ctx: Context) {
    val req: Pause900Req = ctx.getReqBody()
    req.robotNo = NumHelper.anyToInt(req.robotId) ?: throw BzError("errCodeErr", "robotId ${req.robotId} not an int")
    HikAdapter.pause900(req)
  }


  fun pauseV5(ctx: Context) {
    val req: Pause900Req = ctx.getReqBody()
    req.robotNo = ensureRobotExistAndUnderControl(req.robotId, ctx)
    HikAdapter.pause900(req)
  }

  fun resume(ctx: Context) {
    val req: Resume902Req = ctx.getReqBody()
    req.robotNo = NumHelper.anyToInt(req.robotId) ?: throw BzError("errCodeErr", "robotId ${req.robotId} not an int")
    HikAdapter.resume902(req)
  }

  fun resumeV5(ctx: Context) {
    val req: Resume902Req = ctx.getReqBody()
    req.robotNo = ensureRobotExistAndUnderControl(req.robotId, ctx)
    HikAdapter.resume902(req)
  }

  fun cancel(ctx: Context) {
    val req: Cancel904Req = ctx.getReqBody()
    req.robotNo = NumHelper.anyToInt(req.robotId) ?: throw BzError("errCodeErr", "robotId ${req.robotId} not an int")
    HikAdapter.cancel904(req)
  }

  fun cancelV5(ctx: Context) {
    val req: Cancel904Req = ctx.getReqBody()
    req.robotNo = ensureRobotExistAndUnderControl(req.robotId, ctx)
    HikAdapter.cancel904(req)
  }

  fun changeSlamSetting(ctx: Context) {
    val req: ChangeSlamSettingReq = ctx.getReqBody()
    req.robotNo = NumHelper.anyToInt(req.robotId) ?: throw BzError("errCodeErr", "robotId ${req.robotId} not an int")
    HikAdapter.changeSlamSetting(req)
  }

  fun getSlam(ctx: Context) {
    val robotNo: Int = NumHelper.anyToInt(ctx.pathParam("robotNo"))
      ?: throw BzError("errMissingHttpPathParam", "robotNo")
    val res = HikAdapter.get218SlamRes(robotNo)

    ctx.json(res)
  }

  fun lock(ctx: Context) {
    val req: LockRobotReq = ctx.getReqBody()
    val robotNo: Int = NumHelper.anyToInt(req.robotId)
      ?: throw BzError("errCodeErr", "robotId ${req.robotId} not an int")

    // 抢占机器人的控制权
    updateLockStatus(robotNo, true, req.nickname)
    ctx.cookie(GW_HIK_OWNER_NICKNAME, req.nickname)
  }

  fun unlock(ctx: Context) {
    val req: UnlockRobotReq = ctx.getReqBody()
    val robotNo: Int = NumHelper.anyToInt(req.robotId)
      ?: throw BzError("errCodeErr", "robotId ${req.robotId} not an int")

    // 释放机器人的控制权
    updateLockStatus(robotNo, false, req.nickname)
    // ctx.removeCookie(GW_HIK_OWNER_NICKNAME)
  }

  fun listOwner(ctx: Context) {
    val req: GetOwnerReq = ctx.getReqBody()

    val r = mutableMapOf<String, Any?>()
    // 没指定机器人 ID 的话，就返回全部的
    if (req.robotIds.isEmpty()) {
      HikAdapter.robots.values.forEach {
        r[it.robotNo.toString()] = it.nickName
      }
      ctx.json(r)
      return
    }

    for (robotId in req.robotIds) {
      val robotNo: Int = NumHelper.anyToInt(robotId)
        ?: throw BzError("errCodeErr", "robotId $robotId not an int")

      val robot = HikAdapter.robots[robotNo] ?: throw BzError("errNoRobot", robotNo)

      r[robotId] = robot.nickName
    }

    ctx.json(r)
  }

}