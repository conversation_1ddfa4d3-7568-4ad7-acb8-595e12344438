package com.seer.trick.robot.vendor.seer.rbk

import io.netty.buffer.ByteBuf
import io.netty.channel.ChannelHandlerContext
import io.netty.handler.codec.ByteToMessageDecoder
import java.nio.charset.StandardCharsets

/**
 * 字节流 -> Rbk 数据包
 */
class RbkDecoder : ByteToMessageDecoder() {

  // private val logger = LoggerFactory.getLogger(this::class.java)

  @Volatile
  private var started = false

  @Volatile
  private var flowNo = 0

  @Volatile
  private var apiNo = 0

  @Volatile
  private var bodySize = -1

  override fun decode(channelHandlerContext: ChannelHandlerContext, buf: ByteBuf, out: MutableList<Any>) {
    // logger.debug("readableBytes: " + buf.readableBytes())
    if (!started) {
      while (buf.readableBytes() > 0) {
        if (buf.readByte().toInt() == START_MARK) {
          // logger.debug("started")
          started = true
          break
        }
      }
    }
    if (!started) return

    if (bodySize < 0 && buf.readableBytes() >= 15) {
      buf.readByte() // 协议版本
      flowNo = buf.readShort().toInt() // 序号
      bodySize = buf.readInt()
      apiNo = buf.readShort().toInt()
      buf.readerIndex(buf.readerIndex() + 6) // 内部使用区域
      // logger.debug("rbk pkg $flowNo - $apiNo: $bodySize")
    }
    if (bodySize < 0) return

    if (buf.readableBytes() < bodySize) return

    val bodyStr = if (bodySize == 0) "" else buf.readCharSequence(bodySize, StandardCharsets.UTF_8).toString()

    out.add(RbkFrame(flowNo, apiNo, bodyStr))

    started = false
    flowNo = -1
    bodySize = -1
  }

  companion object {
    const val START_MARK = 0x5A
    const val HEAD_SIZE = 16
    const val PROTO_VERSION = 0x01
  }

}
