package com.seer.trick.robot.rachel.robot


import com.seer.trick.base.concurrent.BaseConcurrentCenter.highTimeSensitiveExecutor
import com.seer.trick.robot.rachel.*
import org.slf4j.LoggerFactory
import java.util.*

/**
 * 负责控制机器人执行
 */
class MrRobotController(private val rachel: RaRachelManager) {
  
  private val logger = LoggerFactory.getLogger(this::class.java)
  
  /**
   * 无阻塞性代码（实际执行部分在专用线程里执行）；要能响应取消
   */
  fun executeStep(
    rr: MrRobotRuntime, or: MrOrderRuntime, stepIndex: Int, cost: Double?
  ) = rachel.withKeyLock {
    logger.info("机器人 ${rr.id} 执行运单步骤 ${or.orderId}:${stepIndex} 准备")
    
    or.order = or.order.copy(status = MrOrderStatus.Executing, currentStepIndex = stepIndex)
    
    val newStep = or.steps[stepIndex].copy(status = MrStepStatus.Executing, startOn = Date())
    or.setStep(newStep)
    
    rr.setMoving(logger, or, newStep.stepIndex, cost)
    
    MrRepo.saveRobotAsync(rr)
    MrRepo.updateOrderAsync(or.order)
    MrRepo.updateStepAsync(newStep)
    
    rr.cmdExecutorEnter = false
    rr.cmdFuture = rr.cmdExecutor.submit {
      rachel.withKeyLock {
        rr.cmdExecutorEnter = true
      }
      try {
        sendCmd(rr, or, newStep)
      } catch (e: Exception) {
        logger.error("send cmd 意外报错", e)
      } finally {
        rachel.withKeyLock {
          rr.cmdExecutorEnter = false
          rr.cmdFuture = null
        }
      }
    }
  }
  
  /**
   * 在命令执行线程中执行步骤；可中断。方法在大锁内执行。
   */
  private fun sendCmd(rr: MrRobotRuntime, or: MrOrderRuntime, step: MrStep) {
    val logHead = "机器人 ${rr.id} 执行运单步骤 ${step.orderId}:${step.stepIndex} "
    logger.info(logHead + "进入执行线程")
    
    val result = try {
      // 阻塞的
      rr.adapter.sendCmd(rachel.sceneRuntime.map, rr, step)
    } catch (e: InterruptedException) {
      logger.info(logHead + "执行被中断")
      MrCmdResult(MrCmdResultKind.Cancelled, "发送命令过程中被中断")
    } catch (e: Exception) {
      logger.error(logHead + "执行报错", e)
      MrCmdResult(MrCmdResultKind.Failed, "sendCmd 报错：" + e.message)
    }
    
    logger.info("机器人 ${rr.id}，发送命令结束，结果=$result")
    
    // TODO 一个风险：中断 cmd 线程，中断 sendCmd 方法，发送机器人停止，但机器人实际还要运行一段才能停下
    
    // 下面的方法不响应中断
    
    // TODO 可以不释放
    rachel.scheduleService.unlockByRobot(rr.id)
    
    rachel.withKeyLock {
      val newCurrentStep = rr.getCurrentStep()
      if (step.id != newCurrentStep?.id) {
        logger.error(
          "机器人 ${rr.id} 命令结束，本来执行步骤 ${step.orderId}:${step.stepIndex}，" +
            "但机器人当前运单 ${rr.currentOrder?.orderId}:${rr.currentStepIndex}"
        )
      } else if (step.status != newCurrentStep.status) {
        logger.info(
          "机器人 ${rr.id} 命令结束，步骤 ${step.orderId}:${step.stepIndex}，" +
            "步骤状态从 ${step.status} 变为 ${newCurrentStep.status}"
        )
      }
      
      // 状态可能已经变了，比如被打断
      val latestStep = or.steps[step.stepIndex]
      
      when (result.kind) {
        MrCmdResultKind.Ok -> onCmdOk(rr, or, latestStep)
        MrCmdResultKind.Cancelled -> onCmdCancelled(rr, or, latestStep)
        MrCmdResultKind.Failed -> onCmdFailed(rr, or, latestStep)
      }
    }
    
    // 步骤在上面的方法内部负责持久化
    MrRepo.saveRobotAsync(rr)
    MrRepo.updateOrderAsync(or.order)
    
    rachel.dispatchOrderService.dispatchOrders()
    rachel.dispatchStepService.dispatchStep(rr)
  }
  
  /**
   * 持久化机器人、运单在外面进行；持久化步骤在里面进行。
   */
  private fun onCmdOk(rr: MrRobotRuntime, or: MrOrderRuntime, step: MrStep) = rachel.withKeyLock {
    if (rr.cmdStatus == MrRobotCmdStatus.Interrupted) {
      // 只是日志记下
      logger.info("命令完成，但机器人 ${rr.id} 被中断，运单'${or.orderId}'")
    }
    
    // 修改步骤状态
    val newStep = step.copy(status = MrStepStatus.Done, endOn = Date())
    or.setStep(newStep)
    
    MrRepo.updateStepAsync(newStep)
    
    rr.setIdle(logger, "步骤运行成功")
    
    updateLoadForCmdOk(rr, or, step)
    
    if (or.order.status == MrOrderStatus.Cancelling) {
      rachel.orderCancelService.onCmdOkWhenCancelling(rr, or, step.stepIndex)
      return@withKeyLock
    }
    
    // withdrawn 一定清
    or.order = or.order.copy(
      doneStepIndex = or.order.currentStepIndex,
      status = MrOrderStatus.Pending // TODO 在什么地方改这个状态不好
    )
    // 最后一步？
    val order = or.order
    if (order.stepFixed && step.stepIndex == order.stepNum - 1) {
      // TODO 或者后续步骤都跳过了，也完成
      rachel.orderService.markOrderDone(rr, or)
    }
  }
  
  /**
   * 如果是取放货步骤，更新机器人储位状态、运单标记
   */
  private fun updateLoadForCmdOk(rr: MrRobotRuntime, or: MrOrderRuntime, step: MrStep) {
    if (or.order.kind == MrOrderKind.Parking) return
    
    // 卸货后，库位就释放了！
    val unloadStepIndex = or.steps.find { it.forUnload }?.stepIndex
    if (unloadStepIndex != null && unloadStepIndex < step.stepIndex) return
    
    val orderId = or.orderId
    val binIndex = rr.bins.indexOfFirst { it.orderId == orderId }
    if (binIndex >= 0) {
      if (step.forLoad) {
        logger.info("Mark bin filled for load, r=${rr.id}, bin=$binIndex")
        rr.bins[binIndex] = MrRobotBin(binIndex, status = MrRobotBinStatus.Filled, orderId = orderId)
        or.order = or.order.copy(loaded = true, loadLocation = step.location)
      } else if (step.forUnload) {
        logger.info("Mark bin empty for unload, r=${rr.id}, bin=$binIndex")
        rr.bins[binIndex] = MrRobotBin(binIndex, status = MrRobotBinStatus.Empty, orderId = null)
        or.order = or.order.copy(unloaded = true, unloadLocation = step.location)
      }
    } else {
      logger.error("命令成功后更新库位，机器人 ${rr.id} 没有运单 ${orderId} 的库位，机器人库位=${rr.bins}")
    }
  }
  
  /**
   * 持久化机器人、运单在外面进行；持久化步骤在里面进行。
   */
  private fun onCmdCancelled(rr: MrRobotRuntime, or: MrOrderRuntime, step: MrStep) =
    rachel.withKeyLock {
      logger.info(
        "收到命令结束，机器人=${rr.id}, 命令状态=${rr.cmdStatus}, " +
          "当前运单=${or.orderId}，运单状态=${or.order.status}, " +
          "当前步骤=${step.stepIndex}，步骤状态=${step.status}"
      )
      if (or.order.status == MrOrderStatus.Cancelling) {
        rachel.orderCancelService.onCmdCancelledWhenCancelling(rr, or, step.stepIndex)
      } else if (step.status == MrStepStatus.Withdrawn) {
        // 步骤被二分了
        resetWithdrawnStep(rr, or, step)
      } else if (or.order.status == MrOrderStatus.Withdrawn) {
        // 运单被二分了
        resetWithdrawnOrder(rr, or, step)
      } else {
        logger.error(
          "标记机器人和运单故障，因为命令被取消，但运单或步骤未处于取消或撤回状态。"
            + "机器人=${rr.id}, 当前命令状态=${rr.cmdStatus}, "
            + "当前运单=${or.orderId}，运单状态=${or.order.status}, "
            + "当前步骤=${step.stepIndex}，步骤状态=${step.status}"
        )
        rr.cmdStatus = MrRobotCmdStatus.Failed
        or.order = or.order.copy(fault = true)
        // TODO step 要标记吗
      }
    }
  
  private fun resetWithdrawnOrder(rr: MrRobotRuntime, or: MrOrderRuntime, step: MrStep) {
    logger.info(
      "重置运单，因为运单被重新分派, 运单=${or.orderId}:${step.stepIndex}，机器人=${rr.id}, 命令状态=${rr.cmdStatus}"
    )
    
    rr.setIdle(logger, "重置被撤回的运单（触发运单=${or.orderId}）")
    rr.orders.remove(or.orderId)
    
    if (or.order.kind == MrOrderKind.Parking) {
      logger.info("重置运单 ${or.orderId}，运单是停靠单，直接取消运单")
      rachel.orderService.markOrderCancel(or)
    } else {
      // 储位复位
      val binIndex = rr.bins.indexOfFirst { it.orderId == or.orderId }
      if (binIndex < 0) {
        logger.error(
          "重置运单，机器人 ${rr.id} 的库位中没有运单 ${or.orderId} 预留的，机器人库位：${rr.bins}"
        )
      } else {
        rr.bins[binIndex] = MrRobotBin(binIndex)
      }
      
      or.order = or.order.copy(
        status = MrOrderStatus.ToBeAllocated,
        actualRobotName = null,
        robotAllocatedOn = null,
        currentStepIndex = -1,
        doneStepIndex = -1,
      )
      
      for (i in 0..step.stepIndex) {
        val oldStep = or.steps[i]
        // 注意状态范围，不要修改 Skipped
        if (oldStep.status == MrStepStatus.Executing || oldStep.status == MrStepStatus.Done) {
          val newStep = or.steps[i].copy(status = MrStepStatus.Executable)
          or.setStep(newStep)
          
          MrRepo.updateStepAsync(newStep)
        }
      }
    }
  }
  
  private fun resetWithdrawnStep(rr: MrRobotRuntime, or: MrOrderRuntime, step: MrStep) {
    logger.info(
      "命令取消，机器人当前步骤被撤回，清理。机器人=${rr.id}, 运单=${or.orderId}，运单状态=${or.order.status}，" +
        "当前步骤：${step.stepIndex}"
    )
    
    rr.setIdle(logger, "重置撤回的步骤：${step.orderId}:${step.stepIndex}")
    
    logger.info("修改步骤状态为 Executable，${step.orderId}:${step.stepIndex}")
    val newStep = step.copy(status = MrStepStatus.Executable)
    or.setStep(newStep)
    MrRepo.updateStepAsync(newStep)
    
    logger.info("修改运单状态为 Pending，${step.orderId}，修改前状态=${or.order.status}")
    or.order = or.order.copy(status = MrOrderStatus.Pending)
    MrRepo.updateOrderAsync(or.order)
  }
  
  /**
   * 持久化机器人、运单在外面进行；持久化步骤在里面进行。
   */
  private fun onCmdFailed(rr: MrRobotRuntime, or: MrOrderRuntime, step: MrStep) {
    logger.error(
      "命令失败，机器人=${rr.id}，机器人命令状态=${rr.cmdStatus}，" +
        "当前运单 ${or.orderId}:${step.stepIndex}，运单状态=${or.order.status}"
    )
    // 机器人一定标记失败
    rr.cmdStatus = MrRobotCmdStatus.Failed
    
    if (or.order.status == MrOrderStatus.Cancelling || or.order.status == MrOrderStatus.Withdrawn) {
      rachel.orderCancelService.onCmdFailedWhenCancelling(rr, or, step.stepIndex)
    } else {
      or.order = or.order.copy(fault = true)
      // TODO step 要标记吗
    }
  }
  
  /**
   * 目前只有当机器人在执行 Executing 运单时会调用此方法。
   *
   * 取消机器人当前命令、当前步骤。
   * 不会改变机器人当前步骤
   * 要求机器人一定由当前步骤在执行
   * TODO 检查调用者的线程
   */
  fun cancelCmd(rr: MrRobotRuntime) = rachel.withKeyLock {
    val step = rr.getCurrentStep()
    
    val cmdFuture = rr.cmdFuture
    rr.cmdFuture = null
    
    val hasCmd = if (cmdFuture != null)
      if (cmdFuture.isCancelled) "当前有 cmdFuture，已取消"
      else "当前有命令执行，未取消"
    else "当前无 cmdFuture"
    
    val hasCmdEnter = if (rr.cmdExecutorEnter) "cmdFuture 开始执行" else "cmdFuture 未开始"
    
    if (step == null) {
      logger.error(
        "机器人 ${rr.id}，取消命令，但机器人没有当前步骤，当前运单=${rr.currentOrder?.orderId}。$hasCmd。$hasCmdEnter。"
      )
      return@withKeyLock
    }
    
    logger.info(
      "机器人${rr.id}，取消命令，将机器人标记为被打断，当前步骤：${step.orderId}:${step.stepIndex}，步骤状态=${step.status}。" +
        "$hasCmd。$hasCmdEnter。"
    )
    
    rr.cmdStatus = MrRobotCmdStatus.Interrupted
    MrRepo.saveRobotAsync(rr)
    
    if (cmdFuture == null || !rr.cmdExecutorEnter) {
      logger.info("机器人${rr.id}，取消命令，未实际执行")
      val or = rachel.orderService.mustGetOrderById(step.orderId)
      onCmdCancelled(rr, or, step)
      return@withKeyLock
    }
    
    // 先 cancel cmd 再 cancel cmdFuture
    highTimeSensitiveExecutor.submit {
      rr.adapter.cancelCmd(rr)
    }
    
    cmdFuture.cancel(true)
  }
  
}