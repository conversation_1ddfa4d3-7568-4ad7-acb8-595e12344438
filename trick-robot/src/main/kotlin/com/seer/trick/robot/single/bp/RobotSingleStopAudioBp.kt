package com.seer.trick.robot.single.bp


import com.seer.trick.falcon.domain.*
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper

class RobotSingleStopAudioBp : AbstractTaskChainBp() {
  
  override fun process() {
    val station = mustGetBlockInputParam("station") as String
    val reqId = getBlockInputParam("reqId") as String? ?: IdHelper.uuidStr()
    val req = JsonHelper.mapper.writeValueAsString(
      mapOf(
        "id" to station,
        "operation" to "sound",
        "sounds_args" to
            mapOf(
              "stop" to 1
            )
      )
    )
    val gwRbkResult = executeTask(RbkRequest(reqId, 3051, req, "停止播放"))
    setBlockOutputParams(mapOf("rbkResult" to gwRbkResult))
  }
  
  companion object {
    
    val def = BlockDef(
      RobotSingleStopAudioBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(
        BlockInputParamDef(
          "station", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.BinId, ParamObjectType.SiteId)
        ),
//        BlockInputParamDef("reqId", BlockParamType.String),
      ),
      outputParams = listOf(
        BlockOutputParamDef("rbkResult", BlockParamType.String)
      )
    )
    
  }
  
}