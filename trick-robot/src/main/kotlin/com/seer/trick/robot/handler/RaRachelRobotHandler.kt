package com.seer.trick.robot.handler

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.getReqBody

import com.seer.trick.robot.RobotAppManager
import com.seer.trick.robot.handler.MapHandler.mustGetSceneRuntime
import com.seer.trick.robot.rachel.MrRobotUpdate
import io.javalin.http.Context

/**
 * 三代调度机器人接口
 */
object RaRachelRobotHandler {

  fun registerHandlers() {
    val c = Handlers("api/robot/rachel")
    c.post("add-robots", ::addRobots, auth())
    c.post("remove-robots", ::removeRobots, auth())
    c.post("update-robot", ::updateRobot, auth())
    c.post("off-duty", ::updateOffDuty, auth())
    c.post("reconnect", ::reconnect, auth())
    c.get("all-all", ::fetch<PERSON>ll<PERSON>ll, auth())

    c.post("clear-alarm", ::clearAlarm, auth())
  }

  private fun addRobots(ctx: Context) {
    

    val req: AddRobotsReq = ctx.getReqBody()

    val ra = RobotAppManager.mustGetScene(req.scene)
    val rachel = ra.rachel!!

    rachel.addRobots(req.systemConfigs)

    ctx.status(200)
  }

  private fun removeRobots(ctx: Context) {
    

    val req: RemoveRobotsReq = ctx.getReqBody()

    val ra = RobotAppManager.mustGetScene(req.scene)
    val rachel = ra.rachel!!

    rachel.removeRobots(req.ids)

    ctx.status(200)
  }

  private fun updateRobot(ctx: Context) {
    

    val req: MrRobotUpdate = ctx.getReqBody()

    val ra = RobotAppManager.mustGetScene(req.scene)
    val rachel = ra.rachel!!

    rachel.updateRobot(req)

    ctx.status(200)
  }

  private fun updateOffDuty(ctx: Context) {
    

    val req: UpdateOffDutyReq = ctx.getReqBody()

    val ra = RobotAppManager.mustGetScene(req.scene)
    val rachel = ra.rachel!!

    rachel.updateOffDuty(req.ids, req.offDuty)

    ctx.status(200)
  }

  private fun reconnect(ctx: Context) {
    
    val req: RobotIds = ctx.getReqBody()

    val ra = RobotAppManager.mustGetScene(req.scene)
    val rachel = ra.rachel!!

    rachel.reconnectRobots(req.ids)

    ctx.status(200)
  }

  private fun fetchAllAll(ctx: Context) {
    

    val sr = mustGetSceneRuntime(ctx)
    val rachel = sr.rachel!!

    val robots = rachel.listAllAll()
    ctx.json(robots)
  }

  // 清除仙工机器人自身告警
  private fun clearAlarm(ctx: Context) {
    

    val req: RobotIds = ctx.getReqBody()
    val ra = RobotAppManager.mustGetScene(req.scene)
    val rachel = ra.rachel!!

    rachel.clearRobotSelfAlarm(req.ids)

    ctx.status(200)
  }

  class AddRobotsReq(val scene: String, val systemConfigs: List<EntityValue>)

  class RemoveRobotsReq(val scene: String, val ids: List<String>)

  data class UpdateOffDutyReq(val scene: String, val ids: List<String>, val offDuty: Boolean = false)

  class RobotIds(val scene: String, val ids: List<String>)
}