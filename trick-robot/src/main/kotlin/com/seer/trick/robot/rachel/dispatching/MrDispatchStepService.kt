package com.seer.trick.robot.rachel.dispatching

import com.seer.trick.BzError

import com.seer.trick.robot.rachel.*
import org.slf4j.LoggerFactory
import java.util.concurrent.Future

/**
 * 决策机器人收到的多个单子执行哪个（步骤）。
 */
class MrDispatchStepService(private val rachel: RaRachelManager) {

  private val logger = LoggerFactory.getLogger(this::class.java)

  /**
   * 非阻塞，不报错。在机器人自己的步骤选择线程中执行
   */
  fun dispatchStep(rr: MrRobotRuntime): Future<*> = rr.selectExecutor.submit {
    
    try {
      if (!canRobotExecutingStep(rr)) return@submit

      // 当前正在执行且不可被打断
      val currentStep = rr.getCurrentStep()
      if (currentStep != null && !currentStep.interruptible) {
        return@submit
      }

      val orders = rr.orders.values.toList()
      if (orders.isEmpty()) {
        // logger.info("TryToAssignStep ${config.name}, no task")
        return@submit
      }

      val step = findNextStep(rr, orders) ?: return@submit

      tryToExecute(rr, step)
    } catch (e: Exception) {
      logger.error("tryToSelect", e)
    }
  }

  private fun canRobotExecutingStep(rr: MrRobotRuntime): Boolean {
    val selfReport = rr.selfReport
    return !rr.systemConfig.disabled && !rr.systemConfig.offDuty
        && (selfReport != null && !selfReport.error)
        && (rr.cmdStatus == MrRobotCmdStatus.Idle || rr.cmdStatus == MrRobotCmdStatus.Moving)
  }

  private fun findNextStep(
    rr: MrRobotRuntime, orders: List<MrOrderRuntime>
  ): SelectedStep? {
    try {
      var minCost = Double.MAX_VALUE
      var bestStep: SelectedStep? = null

      for (or in orders) {
        if (!isOrderExecutable(or)) continue
        val step = findNextStep(or) ?: continue
        val cost = estimateStepCost(rr, or.steps[step.stepIndex])
        if (cost == null || cost == Double.MAX_VALUE) continue // 不通
        if (cost < minCost) {
          minCost = cost
          bestStep = SelectedStep(or, step, cost)
        }
      }
      return bestStep
    } catch (e: Exception) {
      logger.error("findNextStep", e)
      return null
    }
  }

  private fun isOrderExecutable(or: MrOrderRuntime): Boolean {
    val order = or.order
    val status = order.status
    return !order.fault && (status == MrOrderStatus.Allocated || status == MrOrderStatus.Pending)
  }

  private fun findNextStep(or: MrOrderRuntime): MrStep? {
    if(or.steps.isEmpty()){
      return null
    }
    val order = or.order
    var nextIndex = order.doneStepIndex + 1
    while (nextIndex < order.stepNum) {
      val step = or.steps[nextIndex]
      when (step.status) {
        MrStepStatus.Executable, MrStepStatus.Executing -> return step
        MrStepStatus.Skipped -> nextIndex++
        else -> return null
      }
    }
    return null
  }

  private fun estimateStepCost(rr: MrRobotRuntime, step: MrStep): Double? {
    // TODO 即使同一个站点，料箱机器人，能按层取
    val fromLocation = rr.adapter.findBestStart(rachel.sceneRuntime.map, rr)
    val toLocation = step.location.site
    val toLocationSiteId = rachel.sceneRuntime.map.sceneMapRuntime.getIndexBySiteIdOrBinId(toLocation!!)!!.site.id

    return rr.adapter.calcMoveCost(rachel.sceneRuntime.map, rr, fromLocation, toLocationSiteId)


  }

  /**
   * 无阻塞性代码
   */
  private fun tryToExecute(rr: MrRobotRuntime, selected: SelectedStep) = rachel.withKeyLock {
    val or = selected.order

    // 二次检查任务状态
    if (!isOrderExecutable(or)) {
      logger.info("机器人 ${rr.id} 要执行运单 ${or.orderId}，但目前状态已不可再被执行")
      return@withKeyLock
    }

    if (or.order.actualRobotName != rr.id) {
      logger.info("机器人 ${rr.id} 要执行运单 ${or.orderId}，但目前运单被分给机器人 ${or.order.actualRobotName}")
      return@withKeyLock
    }

    val currentStep = rr.getCurrentStep()

    if (selected.step.id == currentStep?.id) return@withKeyLock // 正在执行
    if (currentStep != null && !currentStep.interruptible) return@withKeyLock

    val newStepLabel = "${selected.order.orderId}:${selected.step.stepIndex}，成本=${selected.cost}"
    if (currentStep != null) {
      val currentStepCost = estimateStepCost(rr, currentStep) ?: Double.MAX_VALUE
      rr.currentStepCost = currentStepCost // 更新
      val oldStepLabel = "${currentStep.orderId}:${currentStep.stepIndex}，成本=${currentStepCost}"
      if (currentStepCost == Double.MAX_VALUE || (currentStepCost - selected.cost > 2)) {
        // 成功如果没有明显变化不更改
        logger.info("机器人 ${rr.id} 要运行新步骤：$newStepLabel。但先撤原步骤：$oldStepLabel")
        withdrawStep(rr, "准备给新步骤 ${selected.order.orderId}:${selected.step.stepIndex}")
      }
    } else {
      logger.info("机器人 ${rr.id} 要运行新步骤：$newStepLabel。")
      rachel.robotController.executeStep(rr, selected.order, selected.step.stepIndex, selected.cost)
    }
  }

  // 停靠单不会进这个
  private fun withdrawStep(rr: MrRobotRuntime, reason: String) {
    val or = rr.currentOrder!!
    val step = rr.getCurrentStep()!!

    logger.info("机器人 ${rr.id} 撤回运单步骤 ${or.orderId}:${step.stepIndex}，$reason")

    // 只改步骤状态？TODO 会不会导致任务被分给别的机器人
    val newStep = step.copy(status = MrStepStatus.Withdrawn)
    or.setStep(newStep)
    MrRepo.updateStepAsync(newStep)

    // 为了防止单子被二分，单子本身也要撤回
    or.order = or.order.copy(status = MrOrderStatus.Withdrawn)
    MrRepo.updateOrderAsync(or.order)

    rachel.robotController.cancelCmd(rr)
  }

}