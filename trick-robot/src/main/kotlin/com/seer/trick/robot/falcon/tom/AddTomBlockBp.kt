package com.seer.trick.robot.falcon.tom

import com.seer.trick.BzError
import com.seer.trick.I18N

import com.seer.trick.base.alarm.AlarmAction
import com.seer.trick.base.alarm.AlarmItem
import com.seer.trick.base.alarm.AlarmLevel
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.falcon.FalconLogLevel
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*
import com.seer.trick.falcon.task.FalconTaskService
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.robot.tom.AddBlocksReq
import com.seer.trick.robot.tom.TomAgent
import com.seer.trick.robot.tom.TomBlock
import org.apache.commons.lang3.StringUtils

class AddTomBlockBp : AbstractBp() {

  override fun process() {
    val tomId = mustGetBlockInputParam("tomId") as String
    val orderId = mustGetBlockInputParam("orderId") as String
    if (orderId.isBlank()) throw BzError("errorTomOrderIdEmpty")

    val location = mustGetBlockInputParam("location") as String
    if (location.isBlank()) throw BzError("errorLocationEmpty")

    val operation = getBlockInputParam("operation") as String?
    val binTask = getBlockInputParam("binTask") as String?
    val preBinTask = getBlockInputParam("preBinTask") as String?
    val goodsId = getBlockInputParam("goodsId") as String?
    val nextLocation = getBlockInputParam("nextLocation") as String?

    val tomUrl = TomAgent.getTomUrlRoot(tomId)

    var tomBlockId: String?

    while (true) {
      tomBlockId = internalVariables["blockId"] as String? ?: ""
      if (tomBlockId.isNotBlank()) {
        log(FalconLogLevel.Info, "添加调度块，但已记录有调度块：'$orderId:$tomBlockId'")
        val (_, br) = TomAgent.awaitBlock(tomUrl, orderId, tomBlockId)
        if (br == null) {
          log(FalconLogLevel.Error, "调度块 '$orderId:$tomBlockId' 没找到，丢弃，重新创建块")
          removeBlockInternalVariables("blockId")
          tomBlockId = null
        }
      }

      if (tomBlockId.isNullOrBlank()) {
        tomBlockId = IdHelper.oidStr()
        log(FalconLogLevel.Info, "创建调度块：$orderId:$tomBlockId")
        setBlockInternalVariables(mapOf("blockId" to tomBlockId))

        val newBlock = TomBlock(
          tomBlockId,
          location = location,
          operation = operation,
          binTask = binTask,
          preBinTask = preBinTask,
          goodsId = goodsId,
          nextLocation = nextLocation,
        )
        TomAgent.addBlocks(tomUrl, AddBlocksReq(orderId, listOf(newBlock)))
      }

      val (or, br) = TomAgent.awaitBlock(tomUrl, orderId, tomBlockId)
      if (br != null && (br.state == "FINISHED" || br.state == "MANUAL_FINISHED")) {
        break
      } else if (br != null && br.state == "STOPPED") {
        throw FatalError(
          I18N.lo("errTomBlockStopped", listOf(orderId, tomBlockId, JsonHelper.mapper.writeValueAsString(or?.errors))),
        )
      } else {
        // 不能删先 removeBlockInternalVariables("blockId")
        val opDesc = StringUtils.firstNonBlank(binTask, operation, preBinTask, "")
        val msg = "调度运单遇到错误。目标位置=$location。货物=$goodsId。动作=$opDesc。" +
          "单号=$tomId。块=$tomBlockId。错误=${br?.state}。"
        log(FalconLogLevel.Error, msg)

        val actualVehicle =
          TomAgent.queryOrderActualVehicle(tomUrl, orderId) ?: throw BzError("errTomOrderNoVehicle2", orderId)

        val record = EntityRwService.findOneById("FalconTaskRecord", taskRuntime.taskId, null)
        // 进入故障等待人工点击处理
        val ai = AlarmItem(
          group = "Falcon",
          code = "TomBlockFailed",
          key = "TomBlockFailed-$tomBlockId", // 要唯一
          level = AlarmLevel.Error,
          message = I18N.lo(
            "errFalconFailed",
            listOf(record?.get("defLabel"), taskRuntime.taskId, record?.get("endedReason")),
          ),
          args = listOf(taskRuntime.taskId, orderId),
          actions = listOf(
            AlarmAction(I18N.lo("btnViewDetail"), uiAction = "FalconViewTask"),
            AlarmAction(I18N.lo("btnFaultRetry")),
            AlarmAction(I18N.lo("btnManualFinishedTask")),
            AlarmAction(I18N.lo("btnAbortTask")),
          ),
        )
        val callback = fun(index: Int, _: List<Any?>) {
          if (index != 0) {
            AlarmService.removeItem(ai.key)
          }
          when (index) {
            1 -> TomAgent.redoFailedOrder(tomUrl, TomAgent.RedoFailedOrderReq(listOf(actualVehicle)))
            2 -> {
              if (goodsId.isNullOrBlank()) {
                TomAgent.manualFinishedWithoutContainers(
                  
                  tomUrl,
                  TomAgent.ManualFinishedWithoutContainersReq(listOf(actualVehicle)),
                )
              } else {
                TomAgent.manualFinishedWithContainers(
                  
                  tomUrl,
                  TomAgent.ManualFinishedWithContainersReq(
                    listOf(
                      TomAgent.ManualFinishedWithContainer(actualVehicle, goodsId), // FIXME 这里的要传背篓号，不是料箱号
                    ),
                  ),
                )
              }
            }
            3 -> {
              FalconTaskService.cancelTask(taskRuntime.taskId, "ByAlarmAction")
            }
          }
        }
        AlarmService.request(ai, null, callback) {
          // 单独一个检查查看调度块的状态是否被外部改变
          val state = TomAgent.awaitBlockFinalState(tomUrl, orderId, tomBlockId)
          if (state) AlarmService.removeItem(ai.key)
          return@request state
        }
      }
    }

    setBlockOutputParams(mapOf("blockId" to tomBlockId))
  }

  companion object {

    val def = BlockDef(
      AddTomBlockBp::class.simpleName!!,
      color = "#C7DCA7",
      inputParams = listOf(
        BlockInputParamDef(
          "tomId",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.RobotScene),
        ),
        BlockInputParamDef(
          "orderId",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.TomOrderId),
        ),
        BlockInputParamDef(
          "location",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.BinId, ParamObjectType.SiteId),
        ),
        BlockInputParamDef("operation", BlockParamType.String, false),
        BlockInputParamDef("binTask", BlockParamType.String, false),
        BlockInputParamDef("preBinTask", BlockParamType.String, false),
        BlockInputParamDef(
          "goodsId",
          BlockParamType.String,
          false,
          objectTypes = listOf(ParamObjectType.ContainerId),
        ),
        BlockInputParamDef("nextLocation", BlockParamType.String, false),
      ),
      outputParams = listOf(
        BlockOutputParamDef("blockId", BlockParamType.String, objectTypes = listOf(ParamObjectType.TomBlockId)),
      ),
    )
  }
}