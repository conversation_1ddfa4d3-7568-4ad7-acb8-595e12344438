package com.seer.trick.robot.falcon.bp.ndc

import com.seer.trick.Cq

import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType

class AllowNdcLoadBp : AbstractBp() {
  override fun process() {
    val orderId = mustGetBlockInputParam("orderId") as String
    logger.info("allow ndc load orderId: $orderId")
    EntityRwService.updateOne("NdcOrder", Cq.eq("id", orderId), mutableMapOf("allowLoad" to true), null)
    // 添加猎鹰任务相关业务对象
    addRelatedObject("NdcOrder", orderId, null)
  }

  companion object {
    val def = BlockDef(
      AllowNdcLoadBp::class.simpleName!!,
      color = "#F2EE9D",
      inputParams = listOf(
        BlockInputParamDef("orderId", BlockParamType.String, true),
      ),
    )
  }
}