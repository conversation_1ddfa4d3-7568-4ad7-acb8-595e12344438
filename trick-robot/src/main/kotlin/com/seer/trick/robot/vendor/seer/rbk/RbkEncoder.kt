package com.seer.trick.robot.vendor.seer.rbk

import io.netty.buffer.ByteBuf
import io.netty.buffer.CompositeByteBuf
import io.netty.buffer.Unpooled
import java.nio.charset.StandardCharsets

object RbkEncoder {

  fun buildReqBytes(apiNo: Int, bodyStr: String, flowNo: Int): CompositeByteBuf {
    val bodyBytes = bodyStr.toByteArray(StandardCharsets.UTF_8)
    val headBuf: ByteBuf = Unpooled.buffer(RbkDecoder.HEAD_SIZE)
    val bodyBuf: ByteBuf = Unpooled.copiedBuffer(bodyBytes)
    headBuf.writeByte(RbkDecoder.START_MARK)
    headBuf.writeByte(RbkDecoder.PROTO_VERSION)
    headBuf.writeShort(flowNo)
    headBuf.writeInt(bodyBytes.size)
    headBuf.writeShort(apiNo)
    for (i in 0..5) headBuf.writeByte(0)
    val reqBuf: CompositeByteBuf = Unpooled.compositeBuffer()
    reqBuf.addComponent(true, headBuf)
    reqBuf.addComponent(true, bodyBuf)
    return reqBuf
  }

}