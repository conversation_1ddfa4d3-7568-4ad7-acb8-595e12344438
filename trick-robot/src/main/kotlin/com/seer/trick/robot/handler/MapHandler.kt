package com.seer.trick.robot.handler

import com.seer.trick.BzError
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.HttpServerManager.noAuth
import com.seer.trick.base.http.getReqBody

import com.seer.trick.helper.JsonHelper
import com.seer.trick.robot.RobotAppManager
import com.seer.trick.robot.RobotAppSceneRuntime
import com.seer.trick.robot.map.MrSceneMap
import com.seer.trick.robot.rachel.ConnectionType
import com.seer.trick.robot.vendor.seer.SmapIO
import io.javalin.http.Context
import org.apache.commons.io.IOUtils
import java.nio.charset.StandardCharsets

/**
 * 场景地图
 */
object MapHandler {
  
  fun registerHandlers() {
    val c = Handlers("api/robot/map")
    c.get("scene-map", ::getSceneMap, noAuth())
    c.post("scene-map", ::updateSceneMap, auth())
    
    c.get("get-robot-smap", ::getRobotSmap, auth())
    c.post("upload-convert-smap", ::uploadConvertSmap, auth())
    c.get("sites", ::listSites, auth())
    c.get("site-to-bins", ::listSiteToBins, auth())
  }
  
  private fun getSceneMap(ctx: Context) {
    val sr = mustGetSceneRuntime(ctx)
    ctx.json(sr.map.sceneMapRuntime.sceneMap)
  }
  
  private fun updateSceneMap(ctx: Context) {
    
    val req: MrSceneMap = ctx.getReqBody()
    
    val sr = mustGetSceneRuntime(ctx)
    sr.map.update(updateCounter(req))
    
    ctx.status(200)
  }
  
  private fun getRobotSmap(ctx: Context) {
    val sr = mustGetSceneRuntime(ctx)
    val rachel = sr.rachel ?: throw BzError("errUnsupportedFetchMap2", "")
    
    val robotId = ctx.queryParam("robotId")
    if (robotId.isNullOrBlank()) throw BzError("errMissingHttpQueryParam", "robotId")
    
    val rr = rachel.robots[robotId] ?: throw BzError("errNoRobot", robotId)
    val rt = rr.systemConfig.connectionType
    val modelMap = if (rt == ConnectionType.Rbk || rt == ConnectionType.GwWs) {
      val connector = rr.connector ?: throw IllegalStateException()
      SmapIO.fetchCurrentMap(connector).area
    } else {
      throw BzError("errUnsupportedFetchMap", rt?.name)
    }
    
    ctx.json(modelMap)
  }
  
  private fun uploadConvertSmap(ctx: Context) {
    val file = ctx.uploadedFile("f0") ?: throw BzError("errNoUploadedFile", "f0")
    val str = IOUtils.toString(file.content(), StandardCharsets.UTF_8)
    val n = JsonHelper.mapper.readTree(str)
    val modelMap = SmapIO.importSmap(n)
    // rbk 1100 指令上报的地图是不带扩展名的 .smap 文件名，这里统一用 rbk 的规则
    val mapName = file.filename().substringBeforeLast(".") // 去掉扩展名
    ctx.json(modelMap.copy(mapName = mapName))
  }
  
  // 列出所有站点
  private fun listSites(ctx: Context) {
    val sr = mustGetSceneRuntime(ctx)
    
    val siteIds = sr.map.sceneMapRuntime.sites.filter { !it.disabled }.map { it.id }
    ctx.json(siteIds)
  }
  
  fun mustGetSceneRuntime(ctx: Context): RobotAppSceneRuntime {
    val sceneName = ctx.queryParam("scene")
    return if (sceneName.isNullOrBlank()) {
      RobotAppManager.mustGetFirstScene()
    } else {
      RobotAppManager.mustGetScene(sceneName)
    }
  }
  
  fun updateCounter(mr: MrSceneMap): MrSceneMap {
    var siteIdCounter: Long = 0
    var edgeIdCounter: Long = 0
    var zoneIdCounter: Long = 0
    val areas = mr.areas
    for (area in areas) {
      zoneIdCounter += area.zones.size
      siteIdCounter += area.sites.size
      edgeIdCounter += area.edges.size
    }
    return mr.copy(
      areaIdCounter = areas.size.toLong(),
      zoneIdCounter = zoneIdCounter, siteIdCounter = siteIdCounter, edgeIdCounter = edgeIdCounter
    )
  }
  
  // 列出所有站点以及其对应的 bins
  private fun listSiteToBins(ctx: Context) {
    val sr = mustGetSceneRuntime(ctx)
    val siteToBins = sr.map.sceneMapRuntime.sites.filter { !it.disabled }.associate { it.id to mutableListOf<String>() }
    for ((binId, binIndex) in sr.map.sceneMapRuntime.binIdToIndexMap) {
      siteToBins[binIndex.siteId]?.add(binId)
    }
    ctx.json(siteToBins)
  }
  
}
