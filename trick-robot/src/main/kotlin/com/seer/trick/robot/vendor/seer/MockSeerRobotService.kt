package com.seer.trick.robot.vendor.seer

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.Cq

import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.change.EntityChange
import com.seer.trick.base.entity.service.extension.EntityServiceExtension
import com.seer.trick.base.entity.service.extension.EntityServiceExtensions
import com.seer.trick.helper.JsonHelper
import com.seer.trick.robot.RobotAppManager
import com.seer.trick.robot.gw.GwRbkRequest
import com.seer.wcs.GeoHelper
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import kotlin.math.ceil

object MockSeerRobotService : EntityServiceExtension() {

  private val logger = LoggerFactory.getLogger(this::class.java)

  private val robots: MutableMap<String, MockSeerRobot> = ConcurrentHashMap()

  init {
    EntityServiceExtensions.addExtension("MockSeerRobot", this)
  }

  override fun afterUpdating(em: EntityMeta, changes: List<EntityChange>) {
    for (c in changes) {
      val robot = robots[c.id] ?: continue
      val ev = c.newValue ?: continue
      robot.update(ev)
    }
  }

  @Synchronized
  private fun prepareRbk(robotId: String): MockSeerRobot {
    var robot = robots[robotId]
    if (robot != null) {
      return robot
    } else {
      robot = MockSeerRobot(robotId)
      var rEv = EntityRwService.findOne("MockSeerRobot", Cq.idEq(robotId))
      if (rEv != null) {
        robot.update(rEv)
      } else {
        logger.info("创建仿真机器人 '$robotId'")
        rEv = mutableMapOf("id" to robotId)
        EntityRwService.createOne("MockSeerRobot", rEv)
      }
      robots[robotId] = robot // 防止上面报错，导致 robots 中的是不全的
      return robot
    }
  }

  fun requestRbk(req: GwRbkRequest): String {
    val mr = prepareRbk(req.robotId)
    return when (req.rbkApiNo) {
      1020 -> handle1020(req, mr)
      1100 -> handle1100(req, mr)
      1110 -> handle1110(req, mr)
      1300 -> handle1300(req)
      3003 -> handle3003(req, mr)
      3051 -> handle3051(req, mr)
      3066 -> handle3066(req, mr)
      4005 -> handle4005(req)
      4011 -> handle4011(req)
      else -> throw IllegalArgumentException("Unknown rbkApiNo: ${req.rbkApiNo}")
    }
  }

  // 3051 查结果
  private fun handle1020(req: GwRbkRequest, mr: MockSeerRobot): String {
    val status = mr.lastTaskIdStatus ?: 404
    val res: EntityValue = mutableMapOf("ret_code" to 0, "task_status" to status)

    return JsonHelper.writeValueAsString(res)
  }

  private fun handle1100(req: GwRbkRequest, mr: MockSeerRobot): String {
    val sr = RobotAppManager.mustGetFirstScene()
    var mapName = sr.map.sceneMapRuntime.sceneMap.areas.firstOrNull()?.name
    mr.currentStation.takeIf { !it.isNullOrBlank() }?.let { currentStation ->
      sr.map.sceneMapRuntime.sceneMap.areas.find { area ->
        area.sites.any { site ->
          StringUtils.equals(site.id, currentStation)
        }
      }?.let { area ->
        mapName = area.name
      }
    }

    val res: EntityValue = mutableMapOf(
      "ret_code" to 0,
      "battery_level" to 1,
      "x" to mr.x,
      "y" to mr.y,
      "angle" to 0.0,
      "current_station" to mr.currentStation,
      "blocked" to false,
      "charging" to false,
      "current_map" to mapName, // 默认第一个场景区域
    )
    return JsonHelper.writeValueAsString(res)
  }

  // 3066 查结果
  private fun handle1110(req: GwRbkRequest, mr: MockSeerRobot): String {
    val reqObj: EntityValue = JsonHelper.mapper.readValue(req.reqStr, jacksonTypeRef())

    @Suppress("UNCHECKED_CAST")
    val taskIds = reqObj["task_ids"] as List<String>
    val taskId = taskIds.first()

    val taskStatus = mr.tasks[taskId] ?: 404

    val res: EntityValue = mutableMapOf(
      "ret_code" to 0,
      "task_status_package" to mutableMapOf(
        "task_status_list" to listOf(
          mutableMapOf(
            "task_id" to taskId,
            "status" to taskStatus,
          ),
        ),
      ),
    )

    return JsonHelper.writeValueAsString(res)
  }

  private fun handle1300(req: GwRbkRequest): String {
    val res = mutableMapOf("ret_code" to 0, "current_map" to "default")
    return JsonHelper.writeValueAsString(res)
  }

  // 取消
  private fun handle3003(req: GwRbkRequest, mr: MockSeerRobot): String {
    logger.info("仿真机器人 ${mr.name} 收到 3003 撤销路径导航")
    // 4 = COMPLETED, 5 = FAILED, 6 = CANCELED
    synchronized(this) {
      if (mr.lastTaskId != null) mr.lastTaskIdStatus = 6
      for (taskId in mr.tasks.keys) {
        val current = mr.tasks[taskId]
        if (!(current == 4 || current == 5 || current == 6)) {
          logger.debug("仿真机器人 ${mr.name} 任务 $taskId 撤销")
          mr.tasks[taskId] = 6
        }
      }
    }
    val res = mutableMapOf("ret_code" to 0)
    return JsonHelper.writeValueAsString(res)
  }

  private fun handle3051(req: GwRbkRequest, mr: MockSeerRobot): String {
    val reqObj: EntityValue = JsonHelper.mapper.readValue(req.reqStr, jacksonTypeRef())
    val targetStation = reqObj["id"] as String
    val taskId = reqObj["task_id"] as String

    mr.tasks[taskId] = 2
    mr.lastTaskId = taskId
    mr.lastTaskIdStatus = 2

    mr.taskExecutor.submit {
      Thread.sleep(3000)

      mr.updateCurrentStation(targetStation)

      mr.tasks[taskId] = 4

      mr.lastTaskId = taskId
      mr.lastTaskIdStatus = 4
    }

    val res = mutableMapOf("ret_code" to 0)
    return JsonHelper.writeValueAsString(res)
  }

  private fun handle3066(req: GwRbkRequest, mr: MockSeerRobot): String {
    logger.debug("仿真机器人 ${mr.name} 处理 3066：${req.reqStr}")

    val reqObj: EntityValue = JsonHelper.mapper.readValue(req.reqStr, jacksonTypeRef())

    @Suppress("UNCHECKED_CAST")
    val moveList = reqObj["move_task_list"] as List<EntityValue>
    val moves = moveList.map { Step3066(it["id"] as String, it["task_id"] as String) }

    for (move in moves) {
      mr.tasks[move.taskId] = 0
    }

    val sr = RobotAppManager.mustGetFirstScene()

    mr.taskExecutor.submit {
      for ((mi, move) in moves.withIndex()) {
        logger.debug("仿真机器人 ${mr.name} 当前：${mr.currentStation}，处理移动：$mi: $move")
        if (mr.tasks[move.taskId] == 6) {
          logger.debug("仿真机器人 ${mr.name} 处理移动过程中被取消 taskId=${move.taskId}")
          return@submit
        }
        val targetSiteId = move.id
        val targetSite = sr.map.sceneMapRuntime.getIndexBySiteIdOrBinId(targetSiteId)!!.site
        val dis = GeoHelper.euclideanDistance(mr.x, mr.y, targetSite.x, targetSite.y)
        if (dis > 0) {
          val steps = ceil(dis / 2.0 * 1000 / 200).toInt() // 2m/s
          val dx = (targetSite.x - mr.x) / steps
          val dy = (targetSite.y - mr.y) / steps
          for (i in 0 until steps) {
            if (mr.tasks[move.taskId] == 6) {
              logger.debug("仿真机器人 ${mr.name} 处理移动过程中被取消 taskId=${move.taskId}")
              return@submit
            }

            mr.tasks[move.taskId] = 2 // 标记为执行中，防止长时间为 0 的报错
            Thread.sleep(200)
            mr.x += dx
            mr.y += dy
          }
        }

        mr.updateCurrentStation(move.id)
        Thread.sleep(500)

        mr.tasks[move.taskId] = 4
      }
    }

    val res = mutableMapOf("ret_code" to 0)
    return JsonHelper.writeValueAsString(res)
  }

  private fun handle4005(req: GwRbkRequest): String {
    val res = mutableMapOf("ret_code" to 0)
    return JsonHelper.writeValueAsString(res)
  }

  private fun handle4011(req: GwRbkRequest): String {
    // TODO 暂不支持
    val res = mutableMapOf("ret_code" to 0)
    return JsonHelper.writeValueAsString(res)
  }
}

class MockSeerRobot(val name: String) {

  @Volatile
  var currentStation: String? = ""

  @Volatile
  var x = 0.0

  @Volatile
  var y = 0.0

  val tasks: MutableMap<String, Int> = ConcurrentHashMap()

  @Volatile
  var lastTaskId: String? = null

  @Volatile
  var lastTaskIdStatus: Int? = null

  var taskExecutor: ExecutorService = Executors.newSingleThreadExecutor()

  fun updateCurrentStation(s: String) {
    val sr = RobotAppManager.mustGetFirstScene()

    val targetSite = sr.map.sceneMapRuntime.getIndexBySiteIdOrBinId(s)?.site ?: return

    currentStation = targetSite.id
    x = targetSite.x
    y = targetSite.y
  }

  fun update(rEv: EntityValue) {
    val staringStation = rEv["staringStation"] as String?
    if (!staringStation.isNullOrBlank() && currentStation.isNullOrBlank()) {
      updateCurrentStation(staringStation)
    }
  }
}

data class Step3066(val id: String, val taskId: String)