package com.seer.trick.robot.rachel.adapter

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.robot.map.SceneMapManager
import com.seer.trick.robot.rachel.*
import com.seer.trick.robot.rachel.scheduler.ScheduleResultKind.*
import com.seer.trick.robot.vendor.seer.SeerConnector
import org.slf4j.LoggerFactory

class MrRobotAdapterRbk(private val rachel: RaRachelManager) : MrRobotAdapter() {

  private val logger = LoggerFactory.getLogger(this::class.java)

  override fun sendCmd(sceneMap: SceneMapManager, rr: MrRobotRuntime, step: MrStep): MrCmdResult {
    while (true) {
      val startSite = findBestStart(sceneMap, rr) ?: throw BzError("errRobotMoveNoStart", rr.id)

      val sr = rachel.scheduleService.schedule(rr, step, startSite)
      if (sr.kind == NoMove) {
        break
      } else if (sr.kind == NoPath) {
        return MrCmdResult(MrCmdResultKind.Failed, "寻路失败，无法找到合适路径")
      } else if (sr.kind == Interrupted) {
        return MrCmdResult(MrCmdResultKind.Cancelled, "寻路过程中被中断")
      }

      try { // 过程中移动
        var fromSite = startSite
        val moves = sr.nextSites.mapIndexed { index, it ->
          val m: EntityValue = mutableMapOf("source_id" to fromSite, "id" to it, "reach_angle" to Math.PI)
          fromSite = it
          m
        }
        val mr = doMoves(rr, step, moves)
        if (mr != null) return mr
      } finally {
        rachel.scheduleService.unlockByRobot(rr.id)
      }
    }

    // 最后一步，动作！

    val mc: EntityValue =
      mutableMapOf("source_id" to "SELF_POSITION", "id" to step.location.site!!, "reach_angle" to -1.0)
    if (!step.rbkArgs.isNullOrBlank()) {
      mc.putAll(JsonHelper.mapper.readValue(step.rbkArgs, jacksonTypeRef<Map<String, Any?>>()))
    }

    val mr = doMoves(rr, step, listOf(mc))
    if (mr != null) return mr

    return MrCmdResult(MrCmdResultKind.Ok)
  }

  private fun doMoves(rr: MrRobotRuntime, step: MrStep, moves: List<EntityValue>): MrCmdResult? {
    for (move in moves) {
      if (move["task_id"] == null) move["task_id"] = IdHelper.oidStr()
    }

    val connector = rr.connector ?: return MrCmdResult(MrCmdResultKind.Failed, "无连接器")

    // TODO 什么时候请求控制权会失败
    try {
      connector.requestControl()
    } catch (e: Exception) {
      return MrCmdResult(MrCmdResultKind.Failed, "请求控制权失败")
    }

    // TODO 这个方法如果直接抛异常，运单状态不会便！
    try {
      connector.go3066(moves)
    } catch (e: Exception) {
      return MrCmdResult(MrCmdResultKind.Failed, "请求运动失败")
    }

    logger.info("机器人 ${rr.id} 3066 已发送，运单 ${step.orderId}:${step.stepIndex}，命令 $moves")

    for ((mi, move) in moves.withIndex()) {
      val r = awaitMove(mi, move, moves, connector)
      if (r != null) return r
    }

    return null
  }

  // 返回 null 表示当前步完成
  private fun awaitMove(
    
    mi: Int,
    move: EntityValue,
    moves: List<EntityValue>,
    connector: SeerConnector,
  ): MrCmdResult? {
    val taskId = move["task_id"] as String
    val currentLabel = "${mi + 1}/${moves.size}:$move"

    while (true) {
      val query = connector.queryGo(taskId)
      if (query != null) {
        when (query.status) {
          4 -> {
            logger.debug("机器人 ${connector.robotName}，查询移动完成 $currentLabel")
            return null
          }

          5 -> return MrCmdResult(MrCmdResultKind.Failed, "移动错误 $currentLabel")
          6 -> return MrCmdResult(MrCmdResultKind.Cancelled, "移动被取消 $currentLabel")
        }
      } else {
        return MrCmdResult(MrCmdResultKind.Failed, "查询返回空 $currentLabel")
      }

      try {
        Thread.sleep(500)
      } catch (e: InterruptedException) {
        return MrCmdResult(MrCmdResultKind.Cancelled, "移动被中断 $currentLabel")
      }
    }
  }

  override fun cancelCmd(rr: MrRobotRuntime) {
    rr.connector?.cancelGo()
  }
}