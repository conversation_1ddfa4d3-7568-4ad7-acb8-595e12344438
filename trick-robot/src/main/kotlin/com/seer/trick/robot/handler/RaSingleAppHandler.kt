package com.seer.trick.robot.handler

import com.fasterxml.jackson.databind.JsonNode
import com.seer.trick.BzError
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.HttpServerManager.noAuth
import com.seer.trick.base.http.getReqBody

import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.NumHelper
import com.seer.trick.robot.RobotAppManager
import com.seer.trick.robot.single.ComponentRow
import com.seer.trick.robot.single.DisplayField
import com.seer.trick.robot.single.RaSingleManager
import com.seer.trick.robot.single.SpeedSetting
import com.seer.trick.robot.vendor.seer.rbk.Container
import com.seer.trick.robot.vendor.seer.rbk.ReLoc
import com.seer.trick.robot.vendor.seer.rbk.Speed
import io.javalin.http.Context

object RaSingleAppHandler {
  
  fun registerHandlers() {
    val c = Handlers("api/robot/single")
    c.post("query-robot", ::queryRobot, auth())
    
    c.post("clear-error", ::clearAllError, auth())
    
    c.post("dominate-control", ::dominateAgv, auth())
    c.post("release-control", ::releaseAgv, auth())
    
    c.post("set-di", ::setDI, auth())
    c.post("set-do", ::setDO, auth())
    
    c.post("open-loop", ::handleOpenLoopRequest, auth())
    c.post("open-loop-speed", ::sendOpenLoopSpeed, auth())
    c.get("chassis-mode", ::getChassisMode, noAuth())
    
    c.post("re-location", ::queryReLoc, auth())
    c.post("start-re-location", ::startReLoc, auth())
    c.post("cancel-re-location", ::cancelReLoc, auth())
    c.post("confirm-location", ::confirmReLoc, auth())
    
    c.post("goto-site", ::startGotoSite, auth())
    c.post("cancel-navigation", ::cancelNavigation, auth())
    c.post("pause-navigation", ::pauseNavigation, auth())
    c.post("resume-navigation", ::resumeNavigation, auth())
    c.get("query-navigation", ::queryNavigation, auth())
    
    c.get("self-goods", ::getSelfGoods, auth())
    c.post("bind-box", ::bindBox, auth())
    c.post("unbind-box", ::unbindBox, auth())
    
    c.get("list-DI", ::listDI, auth())
    c.get("list-DO", ::listDO, auth())
    c.get("list-recfile", ::listRecfile, auth())
    c.get("device-query", ::deviceQuery, auth())
    
    c.get("query-current-map", ::queryCurrentMap, auth())
    c.get("list-all-map", ::listAllMap, auth())
    c.post("switch-map", ::switchMap, auth())
    
    c.post("display-field-config", ::updateDisplayField, auth())
    c.get("display-field-config", ::getDisplayField, auth())
    c.get("default-display-field-config", ::getDefaultDisplayField, auth())
    c.post("speed-config", ::updateSpeedConfig, auth())
    c.get("speed-config", ::getSpeedConfig, auth())
    c.get("default-speed-config", ::getDefaultSpeedConfig, auth())
    c.post("custom-device-config", ::updateCustomDeviceConfig, auth())
    c.get("custom-device-config", ::getCustomDeviceConfig, auth())
    c.post("custom-device-operation", ::customDeviceOperation, auth())
    
    c.post("rec-and-forkload", ::recAndForkLoad, auth())
  }
  
  private fun mustGetRmSingleFromQuery(ctx: Context): RaSingleManager {
    val sceneName = ctx.queryParam("scene")
    if (sceneName.isNullOrBlank()) throw BzError("errMissingHttpQueryParam", sceneName)
    
    return RobotAppManager.mustGetRmSingle(sceneName)
  }
  
  // 查询机器人身上货物信息
  private fun getSelfGoods(ctx: Context) {
    
    val single = mustGetRmSingleFromQuery(ctx)
    
    val goods = single.rbkClient.queryRobotGoods()
    ctx.json(goods)
  }
  
  private fun bindBox(ctx: Context) {
    
    val req: BindBoxReq = ctx.getReqBody()
    
    val single = RobotAppManager.mustGetScene(req.sceneName).single
      ?: throw BzError("errRobotAppNoSingleScene", req.sceneName)
    if (single.stateAgent.controlledByMe != true) throw BzError("errRobotAppNoControlPower")
    
    single.rbkClient.bindBox(req.goodsId, req.containerName, req.desc)
    ctx.status(200)
  }
  
  private fun unbindBox(ctx: Context) {
    
    
    val req: UnbindBoxReq = ctx.getReqBody()
    val single = RobotAppManager.mustGetScene(req.sceneName).single
      ?: throw BzError("errRobotAppNoSingleScene", req.sceneName)
    if (single.stateAgent.controlledByMe != true) throw BzError("errRobotAppNoControlPower")
    
    single.rbkClient.unbindBoxByContainerName(req.containerName)
    ctx.status(200)
  }
  
  private fun queryRobot(ctx: Context) {
    
    val single = mustGetRmSingleFromQuery(ctx)
    
    val res = single.rbkClient.fetch1100(JsonHelper.mapper.writeValueAsString(mapOf("return_laser" to false)))
    ctx.json(res)
  }
  
  /**
   * 确认重定位
   */
  private fun confirmReLoc(ctx: Context) {
    
    val single = mustGetRmSingleFromQuery(ctx)
    if (single.stateAgent.controlledByMe != true) throw BzError("errRobotAppNoControlPower")
    
    single.rbkClient.confirmReLoc()
    ctx.status(200)
  }
  
  private fun cancelNavigation(ctx: Context) {
    
    val single = mustGetRmSingleFromQuery(ctx)
    if (single.stateAgent.controlledByMe != true) throw BzError("errRobotAppNoControlPower")
    
    single.rbkClient.cancelNavigation()
    ctx.status(200)
  }
  
  private fun pauseNavigation(ctx: Context) {
    
    val single = mustGetRmSingleFromQuery(ctx)
    if (single.stateAgent.controlledByMe != true) throw BzError("errRobotAppNoControlPower")
    
    single.rbkClient.pauseNavigation()
    ctx.status(200)
  }
  
  private fun resumeNavigation(ctx: Context) {
    
    val single = mustGetRmSingleFromQuery(ctx)
    if (single.stateAgent.controlledByMe != true) throw BzError("errRobotAppNoControlPower")
    
    single.rbkClient.resumeNavigation()
    ctx.status(200)
  }
  
  private fun clearAllError(ctx: Context) {
    
    val single = mustGetRmSingleFromQuery(ctx)
    if (single.stateAgent.controlledByMe != true) throw BzError("errRobotAppNoControlPower")
    
    single.rbkClient.clearAllError()
    ctx.status(200)
  }
  
  private fun dominateAgv(ctx: Context) {
    
    val single = mustGetRmSingleFromQuery(ctx)
    
    single.rbkClient.dominateAgv(single.getNickname())
    ctx.status(200)
  }
  
  /**
   * 释放控制权
   */
  private fun releaseAgv(ctx: Context) {
    
    val single = mustGetRmSingleFromQuery(ctx)
    single.rbkClient.releaseAgv()
    ctx.status(200)
  }
  
  private fun setDI(ctx: Context) {
    
    val req: DigitalReq = ctx.getReqBody()
    val single = RobotAppManager.mustGetRmSingle(req.sceneName)
    
    single.rbkClient.setVirtualDI(req.id!!, req.status)
    ctx.status(200)
  }
  
  private fun setDO(ctx: Context) {
    
    val req: DigitalReq = ctx.getReqBody()
    val single = RobotAppManager.mustGetRmSingle(req.sceneName)
    if (single.stateAgent.controlledByMe != true) throw BzError("errRobotAppNoControlPower")
    
    single.rbkClient.setDO(req.id!!, req.status)
    ctx.status(200)
  }
  
  private fun handleOpenLoopRequest(ctx: Context) {
    
    val req: OpenLoopButtonStatusReq = ctx.getReqBody()
    val single = RobotAppManager.mustGetRmSingle(req.sceneName)
    if (single.stateAgent.controlledByMe != true) throw BzError("errRobotAppNoControlPower")
    
    val mode = single.rbkClient.fetchChassisMode()
    
    // 根据chassis mode处理按键信息
    if (req.stop) {
      single.rbkClient.sendOpenLoopSpeed(Speed())
      ctx.status(204)
      return
    }
    // 操作舵轮时，vx表示舵轮线速度，操作车身时，vx表示车身坐标系下的速度
    val vx: Double? = when {
      req.forward -> req.v
      req.backward -> -req.v
      else -> null
    }
    val vy: Double? = when {
      req.left -> req.v
      req.right -> -req.v
      else -> null
    }
    val w: Double? = when {
      req.rotateCounterclockwise -> req.w
      req.rotateClockwise -> -req.w
      else -> null
    }
    val steer: Int? = when {
      mode == "steer" && req.steerOp && req.rotateCounterclockwise -> 1
      mode == "steer" && req.steerOp && req.rotateClockwise -> -1
      else -> null
    }
    val realSteer: Double? = when {
      req.steerOp && (req.forward || req.backward) -> req.steer
      else -> null
    }
    
    val speed = Speed(vx = vx, vy = vy, w = w, steer = steer, realSteer = realSteer)
    single.rbkClient.sendOpenLoopSpeed(speed)
    ctx.status(204)
  }
  
  private fun sendOpenLoopSpeed(ctx: Context) {
    
    val req: SpeedReq = ctx.getReqBody()
    val single = RobotAppManager.mustGetRmSingle(req.sceneName)
    if (single.stateAgent.controlledByMe != true) throw BzError("errRobotAppNoControlPower")
    
    single.rbkClient.sendOpenLoopSpeed(Speed(req.vx, req.vy, req.w, req.steer, req.realSteer))
    ctx.status(200)
  }
  
  private fun getChassisMode(ctx: Context) {
    
    val single = mustGetRmSingleFromQuery(ctx)
    val chassisMode = single.rbkClient.fetchChassisMode()
    ctx.json(mapOf("chassisMode" to chassisMode))
  }
  
  private fun queryReLoc(ctx: Context) {
    
    val single = mustGetRmSingleFromQuery(ctx)
    if (single.stateAgent.controlledByMe != true) throw BzError("errRobotAppNoControlPower")
    
    val status = single.rbkClient.queryReLoc()
    ctx.json(mapOf("status" to status))
  }
  
  private fun startReLoc(ctx: Context) {
    
    val req: ReLocReq = ctx.getReqBody()
    val single = RobotAppManager.mustGetRmSingle(req.sceneName)
    if (single.stateAgent.controlledByMe != true) throw BzError("errRobotAppNoControlPower")
    
    single.rbkClient.startReLoc(ReLoc(req.isAuto, req.x, req.y, req.angle, req.length, req.home))
    ctx.status(204)
  }
  
  private fun cancelReLoc(ctx: Context) {
    
    val single = mustGetRmSingleFromQuery(ctx)
    if (single.stateAgent.controlledByMe != true) throw BzError("errRobotAppNoControlPower")
    
    single.rbkClient.cancelReLoc()
    ctx.status(204)
  }
  
  private fun startGotoSite(ctx: Context) {
    
    val req: GotoSiteReq = ctx.getReqBody()
    val single = RobotAppManager.mustGetRmSingle(req.sceneName)
    if (single.stateAgent.controlledByMe != true) throw BzError("errRobotAppNoControlPower")
    
    val taskId = single.rbkClient.gotoSite(req.site)
    ctx.json(mapOf("taskId" to taskId))
  }
  
  private fun deviceQuery(ctx: Context) {
    val single = mustGetRmSingleFromQuery(ctx)
    
    val modelNode = single.rbkClient.fetchModel()
    val deviceTypesNode = JsonHelper.mapper.readTree(modelNode)["deviceTypes"]
    val controlRes: DeviceControlRes
    
    val rbkRes1100 = single.rbkClient.requestWithReturnCodeError(1100, "")
    val resNode = JsonHelper.mapper.readTree(rbkRes1100)
    
    val types: MutableList<String> = mutableListOf()
    
    // 判断是否有叉车机构
    if (checkDeviceEnabled(deviceTypesNode, "fork")) {
      val devices = mustGetJsonNode(deviceTypesNode, "name", "fork", "devices")
      val deviceParams = mustGetJsonNode(devices, "name", "fork", "deviceParams")
      val comboParam = mustGetJsonNode(deviceParams, "key", "type", "comboParam")
      
      // 判断是否是地牛
      if (comboParam["childKey"].asText() != "byDO") {
        types.add("fork") // 搬运叉车、堆高叉车、平衡重叉车
      } else {
        types.add("miniFork") // 地牛暂不展示信息
      }
    }
    
    // 判断是否有顶升机构
    if (checkDeviceEnabled(deviceTypesNode, "jack")) types.add("jack")
    
    // TODO：判断是否有滚筒机构，目前先不适配滚筒机器人。
    
    // 判断是否有料箱机构
    if (checkDeviceEnabled(deviceTypesNode, "container")) types.add("box")
    
    controlRes = if (types.size == 0) {
      DeviceControlRes("base")
    } else if (types.size == 1) {
      val type = types.first()
      if (type == "fork") {
        DeviceControlRes(type, fork = newFork(resNode))
      } else if (type == "miniFork") {
        DeviceControlRes(type)
      } else if (type == "jack") {
        DeviceControlRes(type, jack = newJack(resNode))
      } else if (type == "box") {
        DeviceControlRes(type, box = newBox(resNode))
      } else {
        throw BzError("errNoSuchDevice")
      }
    } else {
      DeviceControlRes(
        "multi",
        fork = if (types.any { it == "fork" }) newFork(resNode) else null,
        jack = if (types.any { it == "jack" }) newJack(resNode) else null,
        box = if (types.any { it == "box" }) newBox(resNode) else null,
      )
    }
    ctx.json(controlRes)
  }
  
  private fun mustGetJsonNode(parentNode: JsonNode, nodeKey: String, nodeValue: String, targetKey: String): JsonNode {
    var resNode: JsonNode? = null
    for (node in parentNode) {
      if (node[nodeKey].asText() == nodeValue) {
        resNode = node[targetKey]
        break
      }
    }
    if (resNode == null) {
      throw BzError("errNoJsonNode")
    }
    return resNode
  }
  
  private fun checkDeviceEnabled(deviceTypes: JsonNode, deviceName: String): Boolean {
    val devices = mustGetJsonNode(deviceTypes, "name", deviceName, "devices")
    if (deviceName == "container") {
      // 料箱车会启用若干个 container，只要其中一个是 enabled 的就行。
      return devices.any { it.get("isEnabled").asBoolean() }
    }
    return mustGetJsonNode(devices, "name", deviceName, "isEnabled").asBoolean()
  }
  
  private fun newBox(resNode: JsonNode): Box {
    val containerList = resNode["containers"]?.asIterable()?.map {
      Container(
        it["container_name"]?.asText(),
        it["goods_id"]?.asText(),
        it["desc"]?.asText(),
        it["has_goods"]?.asBoolean() ?: false,
      )
    }
    return Box(
      resNode["task_status"]?.asInt() ?: 0,
      resNode["task_type"]?.asInt() ?: 0,
      resNode["unfinished_path"]?.asIterable()?.map { it.asText() },
      containerList ?: emptyList(),
    )
  }
  
  // TODO 查询识别文件进行展示
  private fun newJack(resNode: JsonNode) = Jack(
    resNode["jack_emc"]?.asBoolean() ?: false,
    resNode["jack_error_code"]?.asInt() ?: 0,
    resNode["jack_height"]?.asDouble() ?: 0.0,
    resNode["jack_isFull"]?.asBoolean() ?: false,
    resNode["jack_mode"]?.asBoolean() ?: false,
    resNode["jack_state"]?.asInt() ?: 0,
    resNode["recfile"]?.asText() ?: "",
  )
  
  private fun newFork(resNode: JsonNode) = Fork(
    resNode["fork_auto_flag"]?.asBoolean() ?: false,
    resNode["fork_height_in_place"]?.asBoolean() ?: false,
    resNode["fork_height"]?.asDouble() ?: 0.0,
    resNode["recfile"]?.asText() ?: "",
  )
  
  private fun listDO(ctx: Context) {
    val single = mustGetRmSingleFromQuery(ctx)
    val rbkResult = single.rbkClient.fetch1100("")
    val rbkNode = JsonHelper.mapper.readTree(rbkResult)
    val doList = rbkNode["DO"]!!.asIterable().map {
      Digital(it["id"].asInt(), it["status"].asBoolean())
    }
    ctx.json(doList)
  }
  
  private fun listDI(ctx: Context) {
    val single = mustGetRmSingleFromQuery(ctx)
    val rbkResult = single.rbkClient.fetch1100("")
    val rbkNode = JsonHelper.mapper.readTree(rbkResult)
    val diList = rbkNode["DI"]!!.asIterable().map {
      Digital(it["id"].asInt(), it["status"].asBoolean())
    }
    ctx.json(diList)
  }
  
  private fun listRecfile(ctx: Context) {
    val single = mustGetRmSingleFromQuery(ctx)
    val rbkResult = single.rbkClient.requestWithReturnCodeError(1505, "")
    val rbkNode = JsonHelper.mapper.readTree(rbkResult)
    val recfileList = rbkNode["list"]!!.asIterable().map {
      Recfile(it["md5"].asText(), it["relative_path"].asText())
    }
    ctx.json(recfileList)
  }
  
  private fun queryCurrentMap(ctx: Context) {
    val single = mustGetRmSingleFromQuery(ctx)
    
    val rbkRes1300 = single.rbkClient.requestWithReturnCodeError(1300, "")
    val resNode = JsonHelper.mapper.readTree(rbkRes1300)
    
    ctx.json(mapOf("currentMap" to resNode["current_map"], "currentMapMD5" to resNode["current_map_md5"]))
  }
  
  private fun listAllMap(ctx: Context) {
    val single = mustGetRmSingleFromQuery(ctx)
    
    val rbkRes1300 = single.rbkClient.requestWithReturnCodeError(1300, "")
    val resNode = JsonHelper.mapper.readTree(rbkRes1300)
    
    ctx.json(mapOf("maps" to resNode["maps"], "mapFiles" to (resNode["map_files_info"] ?: emptyList())))
  }
  
  private fun switchMap(ctx: Context) {
    
    val req: SwitchMapReq = ctx.getReqBody()
    val single = RobotAppManager.mustGetRmSingle(req.sceneName)
    if (single.stateAgent.controlledByMe != true) throw BzError("errRobotAppNoControlPower")
    
    val reqMap = mapOf(
      "script_name" to "syspy/switchMap.py",
      "script_args" to mapOf("map" to req.mapName),
      "operation" to "Script",
      "id" to req.siteId,
      "source_id" to req.siteId,
      "task_id" to IdHelper.uuidStr(),
    )
    val reqString = JsonHelper.mapper.writeValueAsString(reqMap)
    single.rbkClient.requestWithReturnCodeError(3051, reqString)
    ctx.status(200)
  }
  
  /**
   * 更新机器人信息展示的字段
   */
  private fun updateDisplayField(ctx: Context) {
    
    val req: DisplayFieldReq = ctx.getReqBody()
    val single = RobotAppManager.mustGetRmSingle(req.sceneName)
    single.saveDisplayFieldConfig(req.displayFieldConfig)
    ctx.status(200)
  }
  
  /**
   * 获取显示字段
   */
  private fun getDisplayField(ctx: Context) {
    val single = mustGetRmSingleFromQuery(ctx)
    ctx.json(single.singleConfig.displayFieldConfig)
  }
  
  /**
   * 获取显示字段默认值
   */
  private fun getDefaultDisplayField(ctx: Context) {
    val single = mustGetRmSingleFromQuery(ctx)
    single.initDisplayFieldConfig()
    ctx.json(single.singleConfig.displayFieldConfig)
  }
  
  /**
   * 更新机器人速度档次配置
   */
  private fun updateSpeedConfig(ctx: Context) {
    
    val req: SpeedConfigReq = ctx.getReqBody()
    val single = RobotAppManager.mustGetRmSingle(req.sceneName)
    single.saveSpeedConfig(req.speedConfig)
    ctx.status(200)
  }
  
  private fun getSpeedConfig(ctx: Context) {
    val single = mustGetRmSingleFromQuery(ctx)
    ctx.json(single.singleConfig.speedConfig)
  }
  
  private fun getDefaultSpeedConfig(ctx: Context) {
    val single = mustGetRmSingleFromQuery(ctx)
    single.initSpeedConfig()
    ctx.json(single.singleConfig.speedConfig)
  }
  
  private fun updateCustomDeviceConfig(ctx: Context) {
    val req: CustomDeviceConfigReq = ctx.getReqBody()
    val single = RobotAppManager.mustGetRmSingle(req.sceneName)
    single.saveCustomDeviceConfig(req.rows)
    ctx.status(200)
  }
  
  private fun getCustomDeviceConfig(ctx: Context) {
    val single = mustGetRmSingleFromQuery(ctx)
    ctx.json(single.getCustomDeviceConfig())
  }
  
  private fun customDeviceOperation(ctx: Context) {
    val req: CustomDeviceOperationReq = ctx.getReqBody()
    val single = RobotAppManager.mustGetRmSingle(req.sceneName)
    val jsonNode = JsonHelper.mapper.readTree(req.operation)
    if (jsonNode["operation"]?.asText() != "Script") throw BzError("errCannotExecuteNonDeviceOperation")
    if (single.stateAgent.controlledByMe != true) throw BzError("errRobotAppNoControlPower")
    
    val rbkNavigation = single.rbkClient.queryRbkNavigation()
    if (rbkNavigation.taskStatus != 1 && rbkNavigation.taskStatus != 2) {
      // 不是 WAITING 且不是 RUNNING 才能执行新的动作
      single.rbkClient.requestWithReturnCodeError(3051, req.operation)
    }
    ctx.status(200)
  }
  
  /**
   * 识别并叉取，仅对 CDD、DBD、CPD 有效
   */
  private fun recAndForkLoad(ctx: Context) {
    
    val req: RecAndForkLoadReq = ctx.getReqBody()
    
    val single = RobotAppManager.mustGetRmSingle(req.sceneName)
    
    // 读取识别叉取功能的相关配置
    val startHeight = req.startHeight
    val loadDelta = NumHelper.anyToDouble(BzConfigManager.getByPath("ScSingle", "recAndForkLoad", "loadDelta")) ?: 0.05
    val backHeight = NumHelper.anyToDouble(BzConfigManager.getByPath("ScSingle", "recAndForkLoad", "backHeight")) ?: 0.2
    val readRecFile = BzConfigManager.getByPathAsString("ScSingle", "recAndForkLoad", "recFile")
    val recFile = if (readRecFile.isNullOrBlank()) "multi/mQuickGo.multi" else readRecFile
    
    val recAndForkLoadReq = mutableMapOf<String, Any>(
      "id" to "SELF_POSITION",
      "operation" to "QuickGo",
      "start_height" to startHeight,
      "recfile" to recFile,
    )
    
    // 基于车型，作必要的参数调整。
    val maxHeight = try {
      single.getMotor2MaxLengthMeter()
    } catch (_: Exception) {
      1.0 // 获取 motor2 失败，当前叉车的货叉是通过 DO 控制的。
    }
    if (startHeight >= maxHeight) throw BzError("errRecAndForkLoadOverHeight", startHeight, maxHeight)
    if (maxHeight == 1.0) {
      // 通过 DO 控制的货叉，ForkLoad 时，end_height 设置为 1
      recAndForkLoadReq["end_height"] = 1
      recAndForkLoadReq["back_height"] = 1
    } else if (maxHeight < 0.3) {
      // CDD 货叉的最大高度为 0.205m
      recAndForkLoadReq["end_height"] = maxHeight
      recAndForkLoadReq["back_height"] = maxHeight
    } else {
      recAndForkLoadReq["end_height"] = startHeight + loadDelta
      recAndForkLoadReq["back_height"] = backHeight
    }
    
    val agent = single.stateAgent
    if (agent.controlledByMe != true) throw BzError("errRobotAppNoControlPower")
    
    val taskStatus = NumHelper.anyToInt(agent.report?.rawReport?.get("task_status"))
    // 1:待执行； 2:执行中； 3:挂起状态（暂停导航）
    if (listOf(1, 2, 3).indexOf(taskStatus) >= 0) throw BzError("errRobotExecutingTask")
    
    // TODO: 不能给正在执行单车猎鹰任务、单车运单的机器人下发这个任务。
    
    single.rbkClient.requestWithReturnCodeError(3051, JsonHelper.writeValueAsString(recAndForkLoadReq))
    
    ctx.status(200)
  }
  
  private fun queryNavigation(ctx: Context) {
    val single = mustGetRmSingleFromQuery(ctx)
    ctx.json(single.rbkClient.queryRbkNavigation())
  }
  
  data class SwitchMapReq(
    val sceneName: String,
    val mapName: String,
    val siteId: String = "SELF_POSITION", // SM点的站点名称，绑定了 SM 点工位、库位名称，SELF_POSITION
  )
  
  data class BindBoxReq(
    val sceneName: String,
    val containerName: String,
    val goodsId: String = "",
    val desc: String = "",
  )
  
  data class UnbindBoxReq(
    val sceneName: String,
    val containerName: String,
  )
  
  data class DigitalReq(
    val sceneName: String,
    val id: Int? = null,
    val status: Boolean,
  )
  
  data class OpenLoopButtonStatusReq(
    val sceneName: String,
    val forward: Boolean = false,
    val backward: Boolean = false,
    val left: Boolean = false,
    val right: Boolean = false,
    val rotateClockwise: Boolean = false,
    val rotateCounterclockwise: Boolean = false,
    val stop: Boolean = false,
    val steerOp: Boolean = false, // 是否在操作舵轮，true 表示正在操作舵轮，false表示正在操作车体
    val v: Double = 0.0,
    val w: Double = 0.0,
    val steer: Double = 0.0,
  )
  
  data class SpeedReq(
    val sceneName: String,
    val vx: Double? = null,
    val vy: Double? = null,
    val w: Double? = null,
    val steer: Int? = null,
    val realSteer: Double? = null,
  )
  
  data class ReLocReq(
    val sceneName: String,
    
    val isAuto: Boolean? = null, // bool	是否为自动重定位，当存在该字段且值为true，忽略以下所有字段
    
    val x: Double? = null,
    
    val y: Double? = null,
    
    val angle: Double? = null,
    
    val length: Double? = null, // 重定位区域半径，单位 m
    
    val home: Boolean? = null, // 在 RobotHome 重定位(若为 true, 前三个参数无效, 并从 RoboShop 参数配置中的 RobotHome1-5 重定位, 若 RobotHome1-5 未配置, 则不做任何操作。若缺省则认为是 false)
  )
  
  data class GotoSiteReq(
    val sceneName: String,
    val site: String,
  )
  
  // 部分机器人可能存在多种机构，例如带顶升机构的滚筒车。
  data class DeviceControlRes(
    // 设备类型：
    //  - base: AMB，纯底盘，没有任何上装机构
    //  - fork: 可调整货叉高度的货叉机构，对应的车型有搬运叉车、堆高叉车、平衡重叉车
    //  - miniFork: 特指地牛，MP10S。
    //  - jack: 表示顶升机构
    //  - box: 特指料箱车
    //  - multi: 多种机构组合
    //  - 其他的机构以后再说...
    val deviceType: String = "",
    val fork: Fork? = null,
    val jack: Jack? = null,
    val box: Box? = null,
  )
  
  data class Fork(
    // 叉车模式 -- true：自动模式，控制器控制叉车。 false：手动模式，方向盘控制叉车
    val forkAutoFlag: Boolean,
    // 货叉状态
    val forkHeightInPlace: Boolean,
    // 货叉当前高度
    val forkHeight: Double,
    // 识别文件
    val recfile: String?,
  )
  
  data class Jack(
    // 急停状态
    val jackEmc: Boolean,
    // 顶升错误码
    val jackErrorCode: Int,
    // 当前顶升高度
    val jackHeight: Double,
    // 载货状态（顶升机构上是否有料）
    val jackIsFull: Boolean,
    // 运行模式
    val jackMode: Boolean,
    // 运行状态
    val jackState: Int,
    // 当前使用的识别文件的名称
    val recfile: String?,
  )
  
  data class Box(
    val taskStatus: Int,
    val taskType: Int,
    val unfinishedPath: List<String>?,
    val containers: List<Container>,
  )
  
  data class Digital(
    val id: Int,
    val status: Boolean,
  )
  
  data class Recfile(
    val md5: String,
    val relativePath: String,
  )
  
  data class DisplayFieldReq(
    val sceneName: String,
    val displayFieldConfig: List<DisplayField>,
  )
  
  data class SpeedConfigReq(
    val sceneName: String,
    val speedConfig: List<SpeedSetting>,
  )
  
  data class CustomDeviceConfigReq(
    val sceneName: String,
    val rows: List<ComponentRow>,
  )
  
  data class CustomDeviceOperationReq(
    val sceneName: String,
    val operation: String,
  )
  
  data class RecAndForkLoadReq(
    val sceneName: String,
    // val id: String = "SELF_POSITION", // 固定值 SELF_POSITION
    // val operation: String = "QuickGo", // 固定值 QuickGo 。
    val startHeight: Double, // 取货前，货叉的最终高度值，相对于地面的绝对高度值。
    // val endHeight: Double, // 取货后，货叉的最终高度值，相对于地面的绝对高度值。
    // val backHeight: Double, // 取货并返回 id 对应的点位后，货叉的最终高度值，相对于地面的绝对高度值。
    // val recFile: String,  // 识别栈板时 需要用到识别文件。 例如："multi/mQuickGO.multi"
  )
}