package com.seer.trick.robot.tom

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

import com.seer.trick.base.BaseCenter
import com.seer.trick.base.file.FileManager
import com.seer.trick.helper.JsonFileHelper
import com.seer.trick.helper.submitCatch
import com.seer.trick.robot.handler.MapHandler.updateCounter
import com.seer.trick.robot.map.*
import com.seer.trick.robot.vendor.seer.*
import org.apache.commons.compress.archivers.examples.Expander
import org.apache.commons.io.FileUtils
import org.slf4j.LoggerFactory
import java.io.File
import java.util.concurrent.Executors
import kotlin.io.path.Path

/**
 * 调度地图监控。
 * 定制获取更新
 */
class RaTomMap(
  private val sceneName: String,
  private val tomUrlRoot: String,
  private val mapManager: SceneMapManager
) {
  
  private val logger = LoggerFactory.getLogger(this::class.java)
  
  private val updateExecutor = Executors.newSingleThreadExecutor()
  
  @Volatile
  var lastMd5: String? = null
  
  @Volatile
  var sceneMap: TomSceneMap? = null
  
  @Synchronized
  fun tryUpdateSceneMap(md5: String) {
    if (md5 == lastMd5) return
    lastMd5 = md5
    updateExecutor.submitCatch("更新调度场景", logger) { update(md5) }
  }
  
  private fun update(md5: String) {
    
    logger.info("更新调度场景 ${sceneName}。MD5=$md5")
    val sceneFile = FileManager.nextTmpFile("zip", "CoreScene-")
    // 删除过去的因为每次重启都会生成一个
    FileManager.ensureTmpDir().listFiles()?.forEach {
      if (it.name.startsWith("CoreScene-") && !it.name.endsWith(".zip")) FileUtils.deleteDirectory(it)
    }
    TomAgent.downloadScene(tomUrlRoot, sceneFile)
    
    val sceneDir = File(sceneFile.absolutePath.substringBeforeLast(".zip"))
    // FileHelper.unzipFileToDir(sceneFile, sceneDir)
    Expander().expand(sceneFile, sceneDir)
    sceneFile.delete()
    // 移动背景图至 ui-ext
    val toFile = Path(sceneDir.path, "areas").toFile()
    if (toFile.exists()) {
      val dst = Path(BaseCenter.baseConfig.uiExtDir.path, "areas").toFile()
      if (dst.exists()) {
        dst.delete()
      }
      FileUtils.copyDirectory(toFile, dst)
    }
    
    val sceneJsonFile = File(sceneDir, "rds.scene")
    val tomScene: TomSceneMap? = JsonFileHelper.readJsonFromFile(sceneJsonFile)
    
    // logger.debug("Core Scene: $scene")
    this.sceneMap = tomScene
    
    if (tomScene != null) {
      val mrScene = toMrSceneMap(tomScene)
      mapManager.update(mrScene)
    }
  }
  
  // tom scene to m4 scene map
  private fun toMrSceneMap(tomScene: TomSceneMap): MrSceneMap {
    var indexZones = 0
    var indexEdges = 0
    val areas = tomScene.areas.mapIndexed { areaIdx, tomArea ->
      val id = "Area$areaIdx"
      val sites = SmapIO.toSites(tomArea.logicalMap.advancedPoints)
      val b = SmapIO.updateBound(sites)
      // 将 smap、和 core的整合
      val smapZones: MutableList<SmapAdvancedArea> = (tomArea.logicalMap.advancedBlocks
        + tomArea.logicalMap.advancedAreaList).toMutableList()
      indexZones += if (areaIdx == 0) 0 else (tomScene.areas[areaIdx - 1].logicalMap.advancedBlocks.size
        + tomScene.areas[areaIdx - 1].logicalMap.advancedAreaList.size)
      indexEdges += if (areaIdx == 0) 0 else tomScene.areas[areaIdx - 1].logicalMap.advancedCurves.size
      MrSceneArea(
        id = id, name = tomArea.name,
        sites = sites,
        edges = SmapIO.toEdges(tomArea.logicalMap.advancedCurves, sites, indexEdges),
        zones = SmapIO.toZones(smapZones, indexZones),
        bins = SmapIO.toBins(tomArea.logicalMap.binLocationsList),
        doors = toDoors(tomScene.doors, sites),
        backgroundImage = toImage(tomArea.backgroundImage),
        bound = b
      )
    }
    // TODO 更新计数器
    return updateCounter(MrSceneMap(areas = areas))
  }
  
  private fun toDoors(doors: List<TomSceneDoor>?, mrSites: List<MrSite>): List<MrDoor> {
    if (doors.isNullOrEmpty()) return emptyList()
    val mrDoors: MutableList<MrDoor> = ArrayList()
    for (tsd in doors) {
      val pathNameList = tsd.pathNameList
      if (pathNameList.isEmpty()) return emptyList()
      val split = pathNameList[0].split("-")
      // 判断门是否在这个区域内
      mrSites.find { site -> site.id == split[0] } ?: continue
      mrSites.find { site -> site.id == split[1] } ?: continue
      mrDoors += MrDoor(
        name = tsd.name,
        points = Point2D(
          x = tsd.pos!!.x,
          y = tsd.pos!!.y,
        )
      )
    }
    return mrDoors
  }
  
  private fun toImage(backgroundImage: TomSceneAreaBackgroundImage?): MrBackgroundImage? {
    if (backgroundImage == null || backgroundImage.fileName.isBlank()) return null
    val points: MutableList<Point2D> = ArrayList()
    for (p in backgroundImage.posList) {
      points += Point2D(x = p.x, y = p.y)
    }
    return MrBackgroundImage(
      fileName = backgroundImage.fileName,
      points = points,
    )
  }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class TomSceneMap(
  val robotGroup: List<TomSceneRobotGroup> = emptyList(),
  val areas: List<TomSceneArea> = emptyList(),
  val doors: List<TomSceneDoor>? = null,
)

data class TomSceneRobotGroup(
  val name: String = "",
  val robot: List<TomSceneRobot> = emptyList(),
)

data class TomSceneRobot(
  val id: String = "",
)

data class TomSceneArea(
  val name: String = "",
  val logicalMap: TomSceneAreaLogicalMap = TomSceneAreaLogicalMap(),
  val backgroundImage: TomSceneAreaBackgroundImage? = null,
)

data class TomSceneAreaLogicalMap(
  val advancedPoints: List<SmapAdvancedPoint> = emptyList(),
  val advancedCurves: List<SmapAdvancedCurve> = emptyList(),
  val advancedBlocks: List<SmapAdvancedArea> = emptyList(),
  val binLocationsList: List<SmapBinLocations> = emptyList(),
  val advancedAreaList: List<SmapAdvancedArea> = emptyList(),
)

data class TomSceneAreaBackgroundImage(
  val fileName: String,
  val posList: List<SmapPos> = emptyList(),
)

data class TomSceneDoor(
  val name: String,
  val pathNameList: List<String> = emptyList(),
  val pos: SmapPos?
)
