package com.seer.trick.robot.vendor.hai

import com.seer.trick.BzError
import com.seer.trick.Cq

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.helper.NumHelper
import com.seer.trick.robot.RobotAppManager
import com.seer.trick.robot.map.MrSite
import com.seer.wcs.MrPosition
import kotlin.math.abs
import kotlin.math.atan2

/**
 * 任务分解
 */
object HaiRoute {

  /**
   * 先规划出一条途径各个 steps 的路径
   */
  fun buildMoves(robotId: String, steps: List<HaiStep>): List<EntityValue> {
    val sr = RobotAppManager.mustGetFirstScene()

    val rr = sr.rachel!!.robots[robotId] ?: throw BzError("errNoRobot", robotId)
    val main = rr.selfReport?.main ?: throw BzError("errRobotOffline", robotId)
    val x = main.x
    val y = main.y
    val dir = main.direction
    if (x == null || y == null) throw BzError("errRobotNoPosition", robotId)
    val pos = MrPosition(x, y, dir)
    return fillMiddleLandmarks(robotId, pos, steps)
  }

  /**
   * 将粗步骤，如：从当前点到 A，再到 B 取货，再到 C 放货，再到 D
   * 寻路，填充中间点，转换为机器人能接受的动作序列，补充抬叉高度等参数
   * 锁定资源
   */
  fun fillMiddleLandmarks(
    robotId: String, startPos: MrPosition, steps: List<HaiStep>
  ): MutableList<EntityValue> {
    val sr = RobotAppManager.mustGetFirstScene()

    val stepEnvList = buildStepEnv(steps)

    val startSite = sr.map.mustGetClosestSite(startPos.x, startPos.y)

    val siteIds = stepEnvList.map { it.site.id }
    sr.rachel!!.scheduleService.checkConnectivity(startSite.site.id, siteIds)

    val paths = awaitFindPath(robotId, startSite.site.id, siteIds)

    val moves: MutableList<EntityValue> = ArrayList()
    var fromSite = sr.map.mustGetClosestSite(startPos.x, startPos.y).site
    var robotDirection = startPos.direction ?: 0.0
    for ((si, stepEnv) in stepEnvList.withIndex()) {
      if (fromSite.id != stepEnv.site.id) {
        val sites = paths[si].map { sr.map.sceneMapRuntime.siteIdToIndexMap[it]!!.site }

        // 移动指令（RCS2LL_MOVE）只能是直线移动或者转弯，不能同时移动和转弯。具体表现在x，y，
        // theta 三个参数中只有一个可变。当theta为0或Pi时，x的改变才是合法的。当theta为Pi/2或-Pi/2时，y
        // 的改变才是合法的。新theta与现theta只能相差Pi/2。
        for (i in 1 until sites.size) {
          val lm1 = sites[i - 1]
          val lm2 = sites[i]
          val moveDirection = atan2(lm2.y - lm1.y, lm2.x - lm1.x) // 移动方向 -PI ~ PI
          val directionChanged = Math.round(abs(moveDirection - robotDirection) / Math.PI * 180)
          if (!((directionChanged < 5) || (directionChanged in 175..185))) { // 0 度 或 180 度
            // 获取特殊的旋转点位
            val robotRotatePoint = EntityRwService.findOne(
              "RobotRotatePoint",
              Cq.and(listOf(Cq.eq("x", lm2.x), Cq.eq("y", lm2.y)))
            )
            // 先转向
            // 需要旋转的点,如果跟目标位置(不是下一个点位)在同一个x或者y轴上,择获取对应库位的方向作为旋转角度,
            // 如果下一个点位和x和y与目标库位的都不相等的话,则是绕路,
            // 如果查到有特殊旋转点,则机器人旋转方要按照特殊旋转点的放转
            robotDirection =
              if (stepEnv.binEv != null && (NumHelper.isNumberEqual(lm1.x, stepEnv.binEv["robotX"] as Double) ||
                    NumHelper.isNumberEqual(lm1.y, stepEnv.binEv["robotY"] as Double))
                && (NumHelper.isNumberEqual(lm2.x, stepEnv.binEv["robotX"] as Double) ||
                    NumHelper.isNumberEqual(lm2.y, stepEnv.binEv["robotY"] as Double))
              ) {
                stepEnv.pos.direction ?: moveDirection
              } else (if (robotRotatePoint != null) {
                robotRotatePoint["robotDirection"]
              } else moveDirection) as Double
            appendMoveCmd(moves, lm1.x, lm1.y, robotDirection)
          }
          // 移动
          appendMoveCmd(moves, lm2.x, lm2.y, robotDirection)
        }
      }

      // 最后一个指令，如果需要取放货
      val step = stepEnv.step
      val binEv = stepEnv.binEv
      val toSite = stepEnv.site
      if (step.operation == HaiCmdOp.Load || step.operation == HaiCmdOp.Unload) {
        val boxHeight = NumHelper.anyToDouble(binEv?.get("boxHeight"))
          ?: throw BzError("errCodeErr", "Missing param: boxHeight")
        val boxDirection = NumHelper.anyToDouble(binEv?.get("boxDirection"))
          ?: throw BzError("errCodeErr", "Missing param: boxDirection")
        if (step.operation == HaiCmdOp.Load) {
          appendLoadCmd(moves, toSite.x, toSite.y, boxDirection, boxHeight, step.robotBin, robotDirection)
        } else {
          appendUnloadCmd(moves, toSite.x, toSite.y, boxDirection, boxHeight, step.robotBin, robotDirection)
        }
      }

      fromSite = toSite
    }

    return moves
  }

  private fun buildStepEnv(steps: List<HaiStep>): List<HaiStepEnv> {
    val sr = RobotAppManager.mustGetFirstScene()

    return steps.map { step ->
      val binEv = if (!step.bin.isNullOrBlank()) {
        // TODO WCS 用到了业务
        EntityRwService.findOne("FbBin", Cq.idEq(step.bin)) ?: throw BzError("errNoBin", step.bin)
      } else {
        null
      }
      var toPos = step.position
      if (toPos == null && binEv != null) toPos = binToPosition(binEv)
      if (toPos == null) throw BzError("errCodeErr", "Missing param: bin/position")

      val site = sr.map.mustGetClosestSite(toPos.x, toPos.y).site

      HaiStepEnv(step, toPos, binEv, site)
    }
  }

  private fun binToPosition(bin: EntityValue): MrPosition {
    val x = NumHelper.anyToDouble(bin["robotX"]) ?: throw BzError("errBinNoRobot", bin["id"])
    val y = NumHelper.anyToDouble(bin["robotY"]) ?: throw BzError("errBinNoRobot", bin["id"])
    val direction = bin["robotDirection"] as Double? // ?: throw BzError("errBinNoRobot", bin["id"])

    return MrPosition(x, y, direction)
  }

  private fun awaitFindPath(
    robotId: String, startSite: String, siteIds: List<String>
  ): List<List<String>> {
    val sr = RobotAppManager.mustGetFirstScene()

    while (true) { // TODO 取消
      val paths = sr.rachel!!.scheduleService.plan(robotId, startSite, siteIds)
      if (paths != null) return paths
      Thread.sleep(1000)
    }
  }

  private fun appendMoveCmd(
    cmdList: MutableList<EntityValue>, x: Double, y: Double, direction: Double
  ) {
    cmdList += mutableMapOf(
      "msgType" to 1,
      "targetPosition" to mutableMapOf(
        "x" to x,
        "y" to y,
        "theta" to direction
      )
    )
  }

  private fun appendForkDown(
    cmdList: MutableList<EntityValue>, x: Double, y: Double, robotDirection: Double
  ) {
    // 货叉下降
    cmdList += mutableMapOf(
      "msgType" to 1,
      "targetPosition" to mutableMapOf(
        "x" to x,
        "y" to y,
        "theta" to robotDirection
      ),
      "preconditions" to mutableMapOf(
        "liftPositionMax" to 380,
        "liftPositionMin" to 380
      )
    )
  }

  private fun appendLoadCmd(
    cmdList: MutableList<EntityValue>, x: Double, y: Double, boxDirection: Double, boxHeight: Double, robotBin: Int,
    robotDirection: Double
  ) {
    // 取货到货叉上
    cmdList += mutableMapOf(
      "msgType" to 2,
      "targetPosition" to mutableMapOf(
        "x" to x,
        "y" to y,
        "theta" to boxDirection // 当取货放货的时候,theta为货叉的旋转角度
      ),
      "binId" to "",
      "opType" to 2,
      "targetHeight" to boxHeight, // 高度，小心整数、浮点数
      "binType" to 0 // 货箱类型 0:扫码识别，1:不用扫码识别
    )

    // 货叉放货到背篓上
    cmdList += mutableMapOf(
      "msgType" to 3,
      "opType" to 3,
      "srcTray" to mutableMapOf(
        "id" to 0,
        "type" to 0  // 货叉 0，背篓 1, 定制背篓 2
      ),
      "binId" to "",
      "dstTray" to mutableMapOf(
        "id" to robotBin, // 背篓编号
        "type" to 1 // 货叉 0，背篓 1, 定制背篓 2
      )
    )

    // 货叉下降
    appendForkDown(cmdList, x, y, robotDirection)
  }

  // 放货的时候,从背篓到货叉后也要下降,机器在去放货的目的地的时候,会提前把货从背篓中拿出来,再移动
  private fun appendUnloadCmd(
    cmdList: MutableList<EntityValue>, x: Double, y: Double, boxDirection: Double, boxHeight: Double, robotBin: Int,
    robotDirection: Double
  ) {
    // 从背篓上取货到货叉上
    cmdList += mutableMapOf(
      "msgType" to 3,
      "opType" to 3,
      "srcTray" to mutableMapOf(
        "id" to robotBin,
        "type" to 1  // 货叉 0，背篓 1, 定制背篓 2
      ),
      "binId" to "",
      "dstTray" to mutableMapOf(
        "id" to 0, // 背篓编号
        "type" to 0 // 货叉 0，背篓 1, 定制背篓 2
      )
    )

    // 货叉下降
    appendForkDown(cmdList, x, y, robotDirection)

    // 从货叉取货到料架上
    cmdList += mutableMapOf(
      "msgType" to 2,
      "targetPosition" to mutableMapOf(
        "x" to x,
        "y" to y,
        "theta" to boxDirection // 当取货放货的时候,theta为货叉的旋转角度
      ),
      "binId" to "",
      "opType" to 0,
      "targetHeight" to boxHeight, // 高度，小心整数、浮点数
      "binType" to 0 // 货箱类型 0:扫码识别，1:不用扫码识别
    )

    // 货叉下降
    appendForkDown(cmdList, x, y, robotDirection)
  }

}

/**
 * 要不指定库位，对于 Move 可以只指定 position
 */
data class HaiStep(
  val operation: HaiCmdOp = HaiCmdOp.Move,
  val bin: String? = null, // 哪个库位
  val position: MrPosition? = null, // 位置
  val container: String = "",
  val robotBin: Int = 0, // 身上的库位
)

enum class HaiCmdOp {
  Move, Load, Unload
}

data class HaiStepEnv(
  val step: HaiStep,
  val pos: MrPosition,
  val binEv: EntityValue?,
  val site: MrSite
)