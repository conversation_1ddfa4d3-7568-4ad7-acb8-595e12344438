package com.seer.trick.robot.vendor.seer

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.JsonNode
import com.seer.trick.BzError

import com.seer.trick.helper.JsonHelper
import com.seer.trick.robot.map.*
import kotlin.math.pow
import kotlin.math.sqrt

object SmapIO {

  fun fetchCurrentMap(connector: SeerConnector): SceneAreaAndStr {
    val res = connector.requestRbk(1300, "", 5000)

    val n = JsonHelper.mapper.readTree(res)
    val returnCode = n["ret_code"]?.asInt()
    val errorMsg = n["err_msg"]?.asText()
    if (returnCode != null && returnCode != 0) throw BzError("errFetchCurrentMapFail", connector.robotName, errorMsg)

    val currentMapFile = n["current_map"]?.asText()
      ?: throw BzError("errFetchCurrentMapFail", connector.robotName, "No value")

    return fetchSmap(connector, currentMapFile)
  }

  // 读取一个 smap
  private fun fetchSmap(connector: SeerConnector, filename: String): SceneAreaAndStr {
    val req = mapOf("map_name" to filename)
    val reqString = JsonHelper.mapper.writeValueAsString(req)
    val res = connector.requestRbk(4011, reqString, 20000)

    val n = JsonHelper.mapper.readTree(res)
    val returnCode = n["ret_code"]?.asInt()
    val errorMsg = n["err_msg"]?.asText()
    if (returnCode != null && returnCode != 0) throw BzError("errFetchMapFail", connector.robotName, errorMsg)

    return SceneAreaAndStr(importSmap(n), res)
  }

  fun importSmap(mapStr: String): MrSceneArea {
    val n = JsonHelper.mapper.readTree(mapStr)
    return importSmap(n)
  }

  fun importSmap(jsonNode: JsonNode): MrSceneArea {
    val smap: Smap = JsonHelper.mapper.treeToValue(jsonNode, Smap::class.java)

    val sites: List<MrSite> = toSites(smap.advancedPointList)

    val edges: List<MrEdge> = toEdges(smap.advancedCurveList, sites)

    val zones: List<MrZone> = toZones(smap.advancedAreaList)

    val bins: List<MrBin> = toBins(smap.binLocationsList)

    val lines: List<MrAdvancedLine> = smap.advancedLineList?.takeIf { it.isNotEmpty() }?.let { toAdvancedLine(it) }
      ?: emptyList()

    val bound = updateBound(sites)

    return MrSceneArea(
      mapName = smap.header.mapName,
      sites = sites,
      edges = edges,
      zones = zones,
      bins = bins,
      bound = bound,
      lines = lines,
    )
  }

  /**
   * id 和 name 都取 instanceName
   */
  fun toSites(advancedPoints: List<SmapAdvancedPoint>?): List<MrSite> {
    if (advancedPoints.isNullOrEmpty()) return emptyList()
    return advancedPoints.map { ap ->
      val id = ap.instanceName
      // val name = StringUtils.firstNonBlank(
      //   ap.property?.firstOrNull { it.key == "label" }?.stringValue ?: "",
      //   ap.instanceName
      // )

      // val sps = smapPropertiesToScene(ap.property)
      // 属性中删除 label
      // sps.removeIf { it.key == "label" }
      MrSite(
        id = id,
        type = ap.className,
        x = ap.pos.x,
        y = ap.pos.y,
        direction = ap.dir,
        ignoreDirection = ap.ignoreDir ?: false,
      )
    }
  }

  /**
   * 注意双向路
   */
  fun toEdges(advancedCurves: List<SmapAdvancedCurve>?, mrSites: List<MrSite>, preIndex: Int = 0): List<MrEdge> {
    if (advancedCurves.isNullOrEmpty()) return emptyList()

    val edges: MutableList<MrEdge> = ArrayList()
    val pathMap: MutableMap<String, MrEdge> = HashMap()

    for (i in 1..advancedCurves.size) {
      val ac = advancedCurves[i - 1]

      val key1 = "${ac.startPos.instanceName}_${ac.endPos.instanceName}"
      val key2 = "${ac.endPos.instanceName}_${ac.startPos.instanceName}"
      val existedPath = pathMap[key1] ?: pathMap[key2]
      if (existedPath != null) {
        // 改为双向路
        existedPath.dir = EdgeDirection.Dual
        continue
      }

      val srcSite = mrSites.find { site -> site.id == ac.startPos.instanceName } ?: continue
      val dstSite = mrSites.find { site -> site.id == ac.endPos.instanceName } ?: continue
      // val sps = smapPropertiesToScene(ac.property)
      val controls: MutableList<Point2D> = ArrayList()
      listOf(ac.controlPos1, ac.controlPos2, ac.controlPos3, ac.controlPos4).forEach { controlPos ->
        controlPos?.let {
          controls.add(Point2D(x = it.x, y = it.y, z = it.z))
        }
      }
      // TODO 简单计算下长度，算直线距离
      var length = ac.property.firstOrNull { it.key == "length" }?.doubleValue // 这个属性可能不赋值
      if (length == null || length <= 0) {
        length = sqrt((srcSite.x - dstSite.x).pow(2.0) + (srcSite.y - dstSite.y).pow(2.0))
      }

      val path = MrEdge(
        id = (preIndex + i).toString(),
        dir = EdgeDirection.Forward,
        fromId = srcSite.id,
        fromZ = ac.startPos.pos.z,
        toZ = ac.endPos.pos.z,
        toId = dstSite.id,
        curveType = CurveType.valueOf(ac.className), // TODO 解析不了算直线
        controls = controls,
        length = length,
      )
      edges += path
      pathMap[key1] = path
    }
    return edges
  }

  fun toZones(advancedAreas: List<SmapAdvancedArea>?, preIndex: Int = 0): List<MrZone> {
    if (advancedAreas.isNullOrEmpty()) return emptyList()
    return advancedAreas.mapIndexed { index, a ->
      // 点有四个点
      val x1 = a.posGroup.minOfOrNull { it.x } ?: 0.0
      val x2 = a.posGroup.maxOfOrNull { it.x } ?: 0.0
      val y1 = a.posGroup.minOfOrNull { it.y } ?: 0.0
      val y2 = a.posGroup.maxOfOrNull { it.y } ?: 0.0
      val id = (preIndex + index + 1).toString()
      val points: MutableList<Point2D> = ArrayList()
      for (p in a.posGroup) {
        points += Point2D(
          x = p.x,
          y = p.y,
        )
      }
      MrZone(
        id,
        a.className,
        x1,
        y2, // y2 因为取左上角
        x2 - x1,
        y2 - y1,
        a.dir,
        points = points,
        // properties = smapPropertiesToScene(a.property)
      )
    }
  }

  fun toBins(binLocations: List<SmapBinLocations>?): List<MrBin> {
    val bins: MutableList<MrBin> = ArrayList()
    if (binLocations.isNullOrEmpty()) return emptyList()
    for (sl in binLocations) {
      for (bin in sl.binLocationList) {
        // 过滤掉没有绑定站点的库位
        if (bin.pointName == null) continue
        bins += MrBin(
          binId = bin.instanceName,
          siteId = bin.pointName,
        )
      }
    }
    return bins
  }

  private fun smapPropertiesToScene(smapProperties: List<SmapMapProperty>?): MutableList<AgvSceneElementProperty> {
    val sceneProperties: MutableList<AgvSceneElementProperty> = mutableListOf()
    if (smapProperties == null) return sceneProperties
    for (sp in smapProperties) {
      when (sp.type) {
        "string", "json" -> sceneProperties += AgvSceneElementProperty(sp.key, sp.type, sp.stringValue)
        "bool" -> sceneProperties += AgvSceneElementProperty(sp.key, sp.type, sp.boolValue)
        "int", "int32" -> sceneProperties += AgvSceneElementProperty(sp.key, sp.type, sp.int32Value)
        "uint32" -> sceneProperties += AgvSceneElementProperty(sp.key, sp.type, sp.uint32Value)
        "int64" -> sceneProperties += AgvSceneElementProperty(sp.key, sp.type, sp.int64Value)
        "uint64" -> sceneProperties += AgvSceneElementProperty(sp.key, sp.type, sp.uint64Value)
        "float" -> sceneProperties += AgvSceneElementProperty(sp.key, sp.type, sp.floatValue)
        "double" -> sceneProperties += AgvSceneElementProperty(sp.key, sp.type, sp.doubleValue)
        "bytes" -> sceneProperties += AgvSceneElementProperty(sp.key, sp.type, sp.bytesValue)
      }
    }
    return sceneProperties
  }

  fun updateBound(sites: List<MrSite>): MinMaxXY {
    if (sites.isEmpty()) return MinMaxXY(false)

    var minX = Double.MAX_VALUE
    var minY = Double.MAX_VALUE
    var maxX = -Double.MAX_VALUE
    var maxY = -Double.MAX_VALUE
    for (site in sites) {
      if (site.x < minX) minX = site.x
      if (site.x > maxX) maxX = site.x
      if (site.y < minY) minY = site.y
      if (site.y > maxY) maxY = site.y
    }
    return MinMaxXY(true, minX, maxX, minY, maxY)
  }

  private fun toAdvancedLine(advancedLineList: List<SmapAdvancedLine>): List<MrAdvancedLine> {
    val lines: MutableList<MrAdvancedLine> = ArrayList()
    for (a in advancedLineList) {
      val points: MutableList<Point2D> = ArrayList()
      points += Point2D(
        x = a.line.startPos.x,
        y = a.line.startPos.y,
      )
      points += Point2D(
        x = a.line.endPos.x,
        y = a.line.endPos.y,
      )
      lines += MrAdvancedLine(
        className = a.className,
        instanceName = a.instanceName,
        points = points,
      )
    }
    return lines
  }
}

data class SceneAreaAndStr(val area: MrSceneArea, val rawStr: String)

data class Smap(
  val header: SmapMapHeader,
  val normalPosList: List<SmapPos> = emptyList(),
  val advancedPointList: List<SmapAdvancedPoint>? = null,
  val advancedLineList: List<SmapAdvancedLine>? = null,
  val advancedCurveList: List<SmapAdvancedCurve>? = null,
  val binLocationsList: List<SmapBinLocations>? = null,
  val advancedAreaList: List<SmapAdvancedArea>? = null,
  // val rssiPosList: List<SmapPos>? = null,
  // val reflectorPosList: List<SmapReflectorPos>? = null,
  // @JsonProperty("user_data")
  // val userData: List<SmapMapProperty>? = null,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class SmapMapHeader(
  val mapType: String = "2D-Map",
  val mapName: String,
  val minPos: SmapPos,
  val maxPos: SmapPos,
  val resolution: Double = 0.02,
  val version: String = "1.0.6",
)

data class SmapPos(
  val x: Double = 0.0,
  val y: Double = 0.0,
  val z: Double? = null, // NURBS6 使用
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class SmapAdvancedPoint(
  val className: String = "",
  val instanceName: String = "",
  val pos: SmapPos = SmapPos(0.0, 0.0),
  val dir: Double? = null,
  val ignoreDir: Boolean? = null,
  val attribute: SmapMapAttribute? = null,
  val property: List<SmapMapProperty>? = null,
)

data class SmapAdvancedLine(
  val className: String,
  val instanceName: String,
  val line: SmapMapLine,
  val attribute: SmapMapAttribute? = null,
  // val property: List<SmapMapProperty>? = null
)

data class SmapMapLine(val startPos: SmapPos, val endPos: SmapPos)

data class SmapMapAttribute(
  val description: String? = null,
  val colorPen: Long? = null,
  val colorBrush: Long? = null,
  val colorFont: Long? = null,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class SmapAdvancedCurve(
  val className: String,
  val instanceName: String,
  val startPos: SmapAdvancedCurveEndpoint,
  val endPos: SmapAdvancedCurveEndpoint,
  val controlPos1: SmapPos?,
  val controlPos2: SmapPos?,
  val controlPos3: SmapPos?,
  val controlPos4: SmapPos?,
  val property: List<SmapMapProperty> = emptyList(),
  // val devices: List<SmapDevice>? = null,
  val attribute: SmapMapAttribute? = null,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class SmapAdvancedCurveEndpoint(val instanceName: String = "", val pos: SmapPos = SmapPos(0.0, 0.0))

// 场景元素的属性定义
data class AgvSceneElementProperty(val key: String, val type: String, val value: Any?)

@JsonInclude(JsonInclude.Include.NON_NULL)
class SmapMapProperty(
  val key: String,
  val type: String = "",
  var value: String? = null, // 早期 protobuf 兼容，现在不用
  @JsonAlias("string_value")
  val stringValue: String? = null,
  val boolValue: Boolean? = null,
  val int32Value: Int? = null,
  val uint32Value: Long? = null,
  val int64Value: Long? = null,
  val uint64Value: Long? = null,
  val floatValue: Float? = null,
  val doubleValue: Double? = null,
  val bytesValue: ByteArray? = null,
)

data class SmapBinLocations(val binLocationList: List<SmapBinLocation> = emptyList())

// pointName 库位没有绑定时，该字段不存在
data class SmapBinLocation(val className: String, val instanceName: String, val pointName: String?)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class SmapAdvancedArea(
  val className: String,
  val instanceName: String,
  val posGroup: List<SmapPos>,
  val dir: Double,
  val property: List<SmapMapProperty>? = null,
  val attribute: SmapMapAttribute? = null,
  // val devices: List<SmapDevice>? = null
)