package com.seer.trick.robot


import com.seer.trick.robot.map.SceneMapManager
import com.seer.trick.robot.rachel.MrRobotSelfReportMain
import com.seer.trick.robot.rachel.RaRachelManager
import com.seer.trick.robot.single.RaSingleManager
import com.seer.trick.robot.tom.RaTomManager

/**
 * 一个场景的运行时管理
 */
class RobotAppSceneRuntime(val config: RobotAppSceneConfig) {

  /**
   * 单车特有的管理
   */
  @Volatile
  var single: RaSingleManager? = null
    private set

  /**
   * 二代车队特有的管理
   */
  @Volatile
  var tom: RaTomManager? = null
    private set

  /**
   * 三代和光通讯特有的管理
   */
  @Volatile
  var rachel: RaRachelManager? = null
    private set

  /**
   * 场景地图管理
   */
  val map = SceneMapManager(config)

  fun init() {
    when (config.mode) {
      RobotAppMode.Single -> {
        val single = RaSingleManager(config, this)
        this.single = single
        single.init()
      }

      RobotAppMode.Tom -> {
        val tom = RaTomManager(config, this)
        this.tom = tom
        tom.init()
      }

      RobotAppMode.Rachel, RobotAppMode.Light -> {
        val rachel = RaRachelManager(config, this)
        this.rachel = rachel
        rachel.init()
      }

      else -> {}
    }

    map.load()
  }

  fun dispose() {
    // TODO 如果模式不变，只需要简单重配
    when (config.mode) {
      RobotAppMode.Single -> {
        single?.dispose()
        single = null
      }

      RobotAppMode.Tom -> {
        tom?.dispose()
        tom = null
      }

      RobotAppMode.Rachel, RobotAppMode.Light -> {
        rachel?.dispose()
        rachel = null
      }

      else -> {}
    }
  }

  fun updateRobotAlarms(offlineRobots: List<String>, mains: Map<String, MrRobotSelfReportMain?>) {
    // TODO 三代调度之前，机器人的告警，暂不进入系统告警
  }
}