package com.seer.trick.robot.single


import com.seer.trick.base.soc.SocAttention
import com.seer.trick.base.soc.SocService
import com.seer.trick.helper.getTypeMessage
import com.seer.trick.robot.map.MrSceneMap
import com.seer.trick.robot.vendor.seer.SmapIO
import java.util.concurrent.Executors
import java.util.concurrent.Future

/**
 * 单车地图管理
 * 定时更新机器人当前地图。
 * TODO 机器人多地图管理
 */
class RaSingleMap(private val parent: RaSingleManager) {

  private val mapExecutor = Executors.newSingleThreadExecutor()

  @Volatile
  private var mapWorker: Future<*>? = null

  @Volatile
  private var mapFetching = false

  @Volatile
  var map: CurrentMap? = null
    private set

  fun init() {
    mapWorker = mapExecutor.submit { fetchMapLoop() }
  }

  fun dispose() {
    mapWorker?.cancel(true)
    mapWorker = null
    SocService.removeNode("RmSingle:Map")
  }

  private fun fetchMapLoop() {
    
    while (!Thread.interrupted()) {
      try {
        fetchMap()
        Thread.sleep(3000)
      } catch (e: InterruptedException) {
        return
      }
    }
  }

  private fun fetchMap() {
    if (mapFetching) return
    mapFetching = true

    updateSocState("获取状态...")

    try {
      val map2 = parent.rbkClient.listSmapsAsObj()
      val newCurrentMap = map2.currentMap
      val newCurrentMapMD5 = map2.currentMapMD5
      if (newCurrentMap.isNullOrBlank()) {
        updateSocState("查询机器人当前地图为空", SocAttention.Red)
      } else {
        if (map == null || map?.name != newCurrentMap || map?.md5 != newCurrentMapMD5) {
          val mapStr = parent.rbkClient.fetchSmap(newCurrentMap)
          map = CurrentMap(newCurrentMap, newCurrentMapMD5, mapStr)
          updateSocState("机器人当前地图：$newCurrentMap")

          var area = SmapIO.importSmap(mapStr)
          area = area.copy(name = newCurrentMap, mapName = newCurrentMap)
          val oldArea = parent.sceneRuntime.map.sceneMapRuntime.sceneMap.areas.firstOrNull()
          if (oldArea != null) {
            // 保持区域 id/name 不变
            val nameMap = if (oldArea.name.isNullOrBlank()) newCurrentMap else oldArea.name
            area = area.copy(id = oldArea.id, name = nameMap)
          }
          // 更新场景
          parent.sceneRuntime.map.update(MrSceneMap(areas = listOf(area)))
        }
      }
    } catch (e: Throwable) {
      if (e is InterruptedException) throw e
      updateSocState("获取状态失败：" + e.getTypeMessage(), SocAttention.Red)
      parent.reset()
    } finally {
      mapFetching = false
    }
  }

  private fun updateSocState(desc: String, attention: SocAttention = SocAttention.None) {
    SocService.updateNode("机器人", "RmSingle:Map", "单车地图", desc, attention)
  }

  data class CurrentMap(val name: String, val md5: String?, val mapStr: String)
}