package com.seer.trick.robot.rachel

import com.seer.trick.base.entity.EntityValue
import java.util.*

/**
 * 一个移动机器人的系统配置
 */
class MrSystemConfig(val ev: EntityValue) {

  val id: String = ev["id"] as String // 机器人 ID/名称
  val disabled: Boolean = ev["disabled"] as <PERSON>ole<PERSON>? ?: false // 停用
  val offDuty: Boolean = ev["offDuty"] as <PERSON><PERSON><PERSON>? ?: false // 不接单
  val vendor = parseRobotVendor() // 品牌厂商
  val connectionType = parseConnectionType() // 连接方式
  val gwAuthId = ev["gwAuthId"] as String? ?: ""
  val gwAuthSecret = ev["gwAuthSecret"] as String? ?: ""
  val robotHost = ev["robotHost"] as String?
  val selfBinNum: Int = ev["selfBinNum"] as Int? ?: 1 // 能一次载几个货

  private fun parseConnectionType(): ConnectionType? = try {
    ConnectionType.valueOf(ev["connectionType"] as String)
  } catch (e: Exception) {
    null
  }

  private fun parseRobotVendor(): RobotVendor {
    val str = ev["vendor"] as String?
    return if (str.isNullOrBlank()) RobotVendor.Seer else RobotVendor.valueOf(str)
  }
}

enum class ConnectionType(
  val realtime: Boolean, // 是否是实时连接（目前只有光通讯不是）
) {
  Rbk(true),
  GwWs(true),
  GwWsLight(false),
  Mock(true),
}

enum class RobotVendor {
  Seer,
  Hai,
  Hik,
}

/**
 * 前端接口更新移动机器人
 */
class MrRobotUpdate(
  val scene: String,
  val id: String,
  val systemConfig: EntityValue? = null,
  val runtimeRecord: EntityValue? = null, // 可以不修改
)

/**
 * 汇总移动机器人信息：配置、状态
 */
class MrRobotInfoAll(
  @JvmField
  val id: String,
  @JvmField
  val systemConfig: EntityValue,
  @JvmField
  val runtimeRecord: EntityValue,
  @JvmField
  val online: Boolean,
  @JvmField
  val selfReport: MrRobotSelfReport?,
  @JvmField
  val targetSite: String?,
  @JvmField
  val lockedSiteIds: List<String>?,
  @JvmField
  val pendingSiteIds: List<String>?, // 目前只用于光通讯
)

/**
 * 机器人自身上报的信息
 */
class MrRobotSelfReport(
  @JvmField
  val error: Boolean = false, // 是否有错，如机器人离线
  @JvmField
  val errorMsg: String? = null, // 错误原因
  @JvmField
  val timestamp: Date = Date(), // 获取机器人报告的时间
  @JvmField
  val main: MrRobotSelfReportMain? = null, // 主要字段
  @JvmField
  val rawReport: EntityValue? = null, // 原始报文
) {
  fun isOnline(): Boolean {
    if (error || rawReport.isNullOrEmpty()) return false
    return (System.currentTimeMillis() - timestamp.time) <= 6 * 1000
  }
}

/**
 * 机器人自身上报的信息，主要、通用字段
 */
data class MrRobotSelfReportMain(
  @JvmField
  val battery: Double? = null, // 电池电量 0..1
  @JvmField
  val x: Double? = null, // 机器人位置 x
  @JvmField
  val y: Double? = null, // 机器人位置 y
  @JvmField
  val direction: Double? = null, // 机器人车头朝向（弧度）
  @JvmField
  val currentAreaId: String? = null, // 当前区域（MrSceneArea.id）
  @JvmField
  val currentAreaName: String? = null, // 当前区域（MrSceneArea.name）
  @JvmField
  val currentMap: String? = null, // 当前地图名
  @JvmField
  val currentMapMd5: String? = null, // 当前地图 MD5
  @JvmField
  val currentSite: String? = null, // 当前站点（MrSite.id）
  @JvmField
  val blocked: Boolean? = null, // 是否被阻挡（障碍物、其他机器人、含义复杂……）
  @JvmField
  val charging: Boolean? = null, // 充电中
  @JvmField
  val emergency: Boolean? = null, // 急停按下
  @JvmField
  val softEmc: Boolean? = null, // 软急停按下
  @JvmField
  val relocStatusLabel: String? = null, // 重定位状态
  @JvmField
  val confidence: Double? = null, // 定位置信度 0..1
  @JvmField
  val todayOdo: Double? = null, // 今日里程
  @JvmField
  val currentLockNickName: String? = null, // 控制权所有者名称
  @JvmField
  val alerts: List<MrRobotAlert>? = null, // 告警
  @JvmField
  val moveStatusInfo: String? = null, // 导航状态信息
)

/**
 * 机器人告警
 */
data class MrRobotAlert(
  @JvmField
  val level: MrRobotAlertLevel = MrRobotAlertLevel.Info,
  @JvmField
  val code: String? = null,
  @JvmField
  val message: String = "",
  @JvmField
  val times: Int? = null,
  @JvmField
  val timestamp: Date = Date(),
)

enum class MrRobotAlertLevel {
  Info,
  Warning,
  Error,
  Fatal,
}

enum class MrRobotCmdStatus {
  Idle,
  Moving,
  Interrupted, // 当运单或运单步骤被二分，要先把机器人标记为被打断
  Failed,
}

data class MrRobotBin(
  val index: Int,
  val status: MrRobotBinStatus = MrRobotBinStatus.Empty,
  val orderId: String? = null,
)

enum class MrRobotBinStatus {

  Empty,
  Reserved,
  Filled,
  Cancelled, // 机器人载货后运单被取消，货物需要人工处理
}

/**
 * 直接运单状态
 */
enum class DrOrderStatus {
  Created,
  Sent, // 已下发
  Done,
  ManualDone, // 手工完成
  Failed,
  Cancelled,
}