package com.seer.trick.robot.vendor.seer.rbk



/**
 * RBK 一个端口的客户端
 */
class RbkPortClient(robotName: String, host: String, port: Int) {

  private val rbkTcpClient = RbkTcpClient.new(robotName, host, port)

  private var flowNoCounter = 0

  /**
   * 串行，一个请求发出后，必须等待响应。
   * 不抛异常，返回结果
   */
  fun request(
    
    apiNo: Int,
    reqStr: String,
    reqMaxTry: Int = REQ_MAX_TRY,
    reqRetryDelay: Long = REQ_RETRY_DELAY,
  ): String {
    val flowNo = nextFlowNo()
    return rbkTcpClient.request(
      
      RbkTcpMsg(flowNo, apiNo, reqStr),
      reqMaxTry = reqMaxTry,
      reqRetryDelay = reqRetryDelay,
    ).body
  }

  /**
   * dispose 后，此客户端不再使用
   */
  fun dispose() {
    rbkTcpClient.dispose()
  }

  @Synchronized
  private fun nextFlowNo(): Int {
    val no = (flowNoCounter + 1) % 512
    flowNoCounter = no
    return no
  }

  companion object {
    const val REQ_MAX_TRY = 3
    const val REQ_RETRY_DELAY = 1000L
  }
}