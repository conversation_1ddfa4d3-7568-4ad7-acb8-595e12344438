package com.seer.trick.robot.vendor.seer.rbk

import com.seer.trick.base.net.tcp.*
import java.io.EOFException
import java.io.InputStream
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.charset.StandardCharsets

object RbkTcpClient {

  const val START_MARK: Byte = 0x5A
  const val HEAD_SIZE = 16
  const val PROTO_VERSION = 0x01

  fun new(robotName: String, host: String, port: Int): ReqResTcpClient<RbkTcpMsg> {
    val encoder = RbkTcpMsgEncoder()
    val decoder = RbkTcpMsgDecoder()
    return ReqResTcpClient(host, port, encoder, decoder, logPrefix = "[$robotName]", readTimeout = 5000)
  }
}

class RbkTcpMsgEncoder : MsgEncoder<RbkTcpMsg> {

  override fun encode(msg: RbkTcpMsg): ByteBuffer {
    val bodyBytes = msg.body.toByteArray(StandardCharsets.UTF_8)

    val buf = ByteBuffer.allocate(RbkTcpClient.HEAD_SIZE + bodyBytes.size)
    buf.order(ByteOrder.BIG_ENDIAN)

    buf.put(RbkTcpClient.START_MARK)
    buf.put(RbkTcpClient.PROTO_VERSION.toByte())
    buf.putShort(msg.flowNo.toShort())
    buf.putInt(bodyBytes.size)
    buf.putShort(msg.apiNo.toShort())
    for (i in 0..5) buf.put(0)

    buf.put(bodyBytes)

    buf.flip()
    return buf
  }
}

/**
 * 外部保证加锁访问
 */
class RbkTcpMsgDecoder : MsgDecoder<RbkTcpMsg> {

  private var started = false

  private val headArray: ByteArray = ByteArray(RbkTcpClient.HEAD_SIZE - 1)
  private var headLimit = 0

  private var headOk = false

  private var flowNo: Short = 0
  private var bodySize: Int = 0
  private var apiNo: Short = 0
  private var bodyArray: ByteArray = ByteArray(1024) // 持有最大
  private var bodyLimit = 0

  override fun decode(inputStream: InputStream): RbkTcpMsg? {
    if (!started) {
      val next = inputStream.read()
      if (next == -1) throw EOFException("Read start " + RbkTcpClient.START_MARK)
      if (next.toByte() == RbkTcpClient.START_MARK) {
        started = true
      } else {
        return null
      }
    }

    // 一定 started
    if (!headOk) {
      val headSizeInc = inputStream.read(headArray, headLimit, headArray.size - headLimit)
      if (headSizeInc == -1) throw EOFException("Read head")
      headLimit += headSizeInc
      if (headLimit == RbkTcpClient.HEAD_SIZE - 1) {
        headOk = true
        val headBuf = ByteBuffer.wrap(headArray)
        headBuf.order(ByteOrder.BIG_ENDIAN)
        headBuf.get() // 版本
        flowNo = headBuf.getShort()
        bodySize = headBuf.getInt()
        apiNo = headBuf.getShort()

        if (bodySize > bodyArray.size) {
          bodyArray = ByteArray(bodySize)
        }
      } else {
        return null
      }
    }

    // 一定 headOk
    val bodySizeInc = inputStream.read(bodyArray, bodyLimit, bodySize - bodyLimit)
    if (bodySizeInc == -1) throw EOFException("Read body")
    bodyLimit += bodySizeInc
    return if (bodyLimit == bodySize) {
      val body = String(bodyArray, 0, bodySize, StandardCharsets.UTF_8)
      val msg = RbkTcpMsg(flowNo.toInt(), apiNo.toInt(), body)
      reset()
      msg
    } else {
      null
    }
  }

  private fun reset() {
    started = false
    headLimit = 0
    headOk = false
    bodyLimit = 0
    bodySize = 0
    flowNo = 0
    apiNo = 0
  }
}

data class RbkTcpMsg(val flowNo: Int, val apiNo: Int, val body: String)