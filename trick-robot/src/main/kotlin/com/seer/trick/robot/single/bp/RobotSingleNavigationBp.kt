package com.seer.trick.robot.single.bp


import com.seer.trick.falcon.domain.*
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper

class RobotSingleNavigationBp : AbstractTaskChainBp() {
  
  override fun process() {
    val station = mustGetBlockInputParam("station") as String
    val req = JsonHelper.mapper.writeValueAsString(mapOf("id" to station))
    val reqId = getBlockInputParam("reqId") as String? ?: IdHelper.uuidStr()
    
    val gwRbkResult = executeTask(RbkRequest(reqId, 3051, req, "路径导航"))
    setBlockOutputParams(mapOf("rbkResult" to gwRbkResult))
  }
  
  companion object {
    
    val def = BlockDef(
      RobotSingleNavigationBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(
        BlockInputParamDef(
          "station", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.BinId, ParamObjectType.SiteId)
        ),
//        BlockInputParamDef("reqId", BlockParamType.String),
      ),
      outputParams = listOf(
        BlockOutputParamDef("rbkResult", BlockParamType.String)
      )
    )
    
  }
  
}