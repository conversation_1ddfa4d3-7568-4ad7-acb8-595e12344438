package com.seer.trick.robot.handler

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError

import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.http.WebSocketManager
import com.seer.trick.base.http.WebSocketSubscriber
import com.seer.trick.base.http.WsEnv
import com.seer.trick.base.http.WsMsg
import com.seer.trick.helper.JsonHelper
import com.seer.trick.robot.RobotAppManager
import com.seer.trick.robot.light.ReportApplyManager
import com.seer.trick.robot.light.ReportApplyReq
import com.seer.trick.robot.light.ReportApplyRes
import io.javalin.websocket.WsContext
import io.javalin.websocket.WsMessageContext
import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

/**
 * 用于接受 GW 的 WebSocket 通讯
 */
object GwWsServer : WebSocketSubscriber() {

  private val logger = LoggerFactory.getLogger(this::class.java)

  private val sessionIdToClient: MutableMap<String, GwClient> = ConcurrentHashMap()

  private val replyMap: MutableMap<String, GwRbkResult> = HashMap()
  private val lock = ReentrantLock()
  private val condition = lock.newCondition()

  @Volatile
  private var disposed = false

  /**
   * 覆盖默认的上报通路
   */
  @Volatile
  var onReportApply: ((req: ReportApplyReq) -> ReportApplyRes)? = null

  fun init() {
    WebSocketManager.subscribers += this
  }

  fun dispose() {
    disposed = true
  }

  fun request(robotName: String, authId: String, authSecret: String, msg: WsMsg, timeout: Long): String {
    // logger.debug("Send WS msg to robot $robotName, Request=$msg")
    val r = doRequest(robotName, authId, authSecret, msg, timeout)
    // logger.debug("Send WS msg to robot $robotName, Result=$r")
    return r
  }

  private fun doRequest(robotName: String, authId: String, authSecret: String, msg: WsMsg, timeout: Long): String {
    val target = getCtxByRobotName(authId, authSecret, robotName)?.ctx
      ?: throw BzError("errNoRbkClient")

    if (!target.session.isOpen) throw BzError("errNoRbkClient")

    target.send(msg)
    val startTime = System.currentTimeMillis()
    while (!disposed) {
      lock.withLock {
        val res = replyMap.remove(msg.id)
        if (res != null) {
          if (res.ok) {
            return res.resStr
          } else {
            throw BzError("errCodeErr", res.errorMsg)
          }
        }

        // 冗余校验
        val elapsed = System.currentTimeMillis() - startTime
        if (elapsed >= timeout) {
          logger.error("WebSocket 请求超时：$msg")
          throw BzError("errCodeErr", "Timeout")
        }
        try {
          if (!condition.await(timeout, TimeUnit.MILLISECONDS)) {
            throw BzError("errCodeErr", "Timeout")
          }
        } catch (e: InterruptedException) {
          throw BzError("errCodeErr", "Timeout")
        }
      }
    }
    throw BzError("errCodeErr", "Disposed")
  }

  private fun getCtxByRobotName(authId: String, authSecret: String, robotName: String): GwClient? {
    // TODO auth secret 错误报错
    return sessionIdToClient.values.find {
      it.authId == authId && it.authSecret == authSecret && it.robotNames.contains(robotName) && it.ctx.session.isOpen
    }
  }

  override fun onCloseOrError(ctx: WsContext) {
    removeSession(ctx.sessionId)
  }

  private fun removeSession(sessionId: String) {
    sessionIdToClient.remove(sessionId)
  }

  override fun onMessage(ctx: WsMessageContext, msg: WsMsg, env: WsEnv) {
    val logOn = BzConfigManager.getByPath("ScDev", "logWsClient") == true
    
    if (logOn) {
      logger.info(
        "收到 GW/WS 消息，session id=${ctx.sessionId}, ip=${ctx.session.remoteAddress}, reqId=${msg.id}, action=${msg.action}",
      )
    }

    if (msg.action == "GwWsClientRegister") {
      val req: GwWsClientRegister = JsonHelper.mapper.readValue(msg.content as String, jacksonTypeRef())
      if (logOn) {
        logger.info(
          "收到 GW/WS 注册，authId=${req.authId}, 机器人=${req.robotNames}, session id=${ctx.sessionId}, ip=${ctx.session.remoteAddress}",
        )
      }
      sessionIdToClient[ctx.sessionId] = GwClient(req.authId, req.authSecret, req.robotNames, ctx)
    } else if (msg.action == "GwSeerRbkReply") {
      val req: GwRbkResult = JsonHelper.mapper.readValue(msg.content, jacksonTypeRef())
      lock.withLock {
        replyMap[msg.replyToId] = req
        condition.signalAll()
      }
    } else if (msg.action == "GwWsLightReportApplyRequest") {
      val req: ReportApplyReq = JsonHelper.mapper.readValue(msg.content, jacksonTypeRef())

      if (logOn) logger.info("收到 GwWsLightReportApplyRequest，$req")

      // V3 为新调度光通讯，V2 为旧版光通讯，未配置过则默认为 V3
      var lightVersion = BzConfigManager.getByPath("ScWcs", "light", "version") as String?
      if (lightVersion.isNullOrEmpty()) lightVersion = "V3"
      val res = if (lightVersion == "V3") {
        onReportApply?.invoke(req)
      } else {
        val sr = RobotAppManager.mustGetFirstLightScene()
        ReportApplyManager.onReportApply(req, sr)
      }

      ctx.send(WsMsg("GwWsLightReportApplyReply", JsonHelper.writeValueAsString(res), replyToId = msg.id))
    }
  }
}

data class GwWsClientRegister(val authId: String, val authSecret: String, val robotNames: List<String>)

data class GwClient(val authId: String, val authSecret: String, val robotNames: List<String>, val ctx: WsMessageContext)

data class GwRbkResult(
  val ok: Boolean,
  val reqId: String,
  val robotName: String,
  val apiNo: Int,
  var resStr: String = "",
  var errorMsg: String? = null,
)