package com.seer.trick.robot.vendor.seer

import com.seer.trick.BzError

import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.helper.BoolHelper
import com.seer.trick.helper.CollectionHelper
import com.seer.trick.helper.NumHelper
import com.seer.trick.helper.XlsHelper
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import java.io.File
import java.io.FileInputStream
import java.io.InputStream
import java.util.concurrent.ConcurrentHashMap

/**
 * “|” 之前的系统变量，之后发给 RBK
 * 路径：DI.0.value 变成：{ "DI": [ {value: ""} ] }
 * 路径：args.position.x 变成： { "args" : { "position" : { "x": 1 } } }
 *
 */
object MrBinRobotArgsManager {

  private val logger = LoggerFactory.getLogger(this::class.java)

  private val dict: MutableMap<String, BinRobotArgs> = ConcurrentHashMap()

  fun mustGetBinRobotArgs(bin: String, action: String): BinRobotArgs {
    return dict[buildDictId(bin, action)] ?: throw BzError("errNoBinRobotArgs", bin, action)
  }

  fun parse() {
    
    val file = File(BaseCenter.baseConfig.configDir, "BinRobotArgs.xlsx")
    if (!file.exists()) return
    logger.info("有机器人库位参数文件需要解析")
    FileInputStream(file).use { steam ->
      parse(steam)
    }
  }

  @Suppress("UNCHECKED_CAST")
  private fun parse(steam: InputStream) {
    val headers: MutableList<String> = ArrayList()

    val paths: MutableList<List<String>> = ArrayList()
    val types: MutableList<String> = ArrayList()
    val rows: MutableList<MutableList<Any?>> = ArrayList()

    val dict: MutableMap<String, BinRobotArgs> = HashMap()

    XlsHelper.importXls(steam) { rowIndex, row ->
      if (rowIndex == 0) {
        XlsHelper.rowToStringList(row, headers)
        logger.debug("headers: $headers")
        for (ci in 0 until headers.size) {
          val path = headers[ci].split(".")
          paths += path
        }
      } else if (rowIndex == 1) {
        XlsHelper.rowToStringList(row, types)
        if (types.size < headers.size) throw BzError("errTypesNotEnoughForHeaders")
        logger.debug("types: $types")
      } else {
        val row2: MutableList<Any?> = ArrayList()
        XlsHelper.rowToList(row, row2)
        rows += row2
      }
    }

    val sepColumnIndex = headers.indexOf("|")

    for (row in rows) {
      if (row.size < 3) continue
      val bin = row[0] as String? ?: continue
      val action = row[1] as String? ?: continue
      val site = row[2] as String? ?: ""

      val r = BinRobotArgs(bin = bin, action = action, site = site)
      dict[buildDictId(bin, action)] = r

      for (ci in 3 until headers.size) {
        if (ci == sepColumnIndex) continue

        val type = types[ci]

        val cellRawValue = row.getOrNull(ci)
        val cellValue = fixValueType(cellRawValue, type, ci) ?: continue

        var parent: Any = if (ci < sepColumnIndex) r.fields else r.rbkFields
        val path = paths[ci]
        for ((pathIndex, pathItem) in path.withIndex()) {
          if (StringUtils.isNumeric(pathItem)) {
            val valueIndex = pathItem.toInt()
            val parentTyped = parent as MutableList<Any?>
            if (pathIndex == path.size - 1) {
              CollectionHelper.setItemToList(parentTyped, valueIndex, cellValue)
            } else {
              if (valueIndex >= parentTyped.size || parentTyped[valueIndex] == null) {
                CollectionHelper.setItemToList(parentTyped, valueIndex, newMiddleValue(path[pathIndex + 1]))
              }
              parent = parentTyped[valueIndex]!!
            }
          } else {
            val parentTyped = parent as MutableMap<String, Any?>
            if (pathIndex == path.size - 1) {
              parentTyped[pathItem] = cellValue
            } else {
              if (!parentTyped.containsKey(pathItem)) {
                parentTyped[pathItem] = newMiddleValue(path[pathIndex + 1])
              }
              parent = parentTyped[pathItem]!!
            }
          }
        }
      }
    }

    MrBinRobotArgsManager.dict.clear()
    MrBinRobotArgsManager.dict.putAll(dict)
  }

  fun buildDictId(bin: String, action: String): String {
    return "$bin:$action"
  }

  private fun newMiddleValue(nextPathItem: String): Any {
    return if (StringUtils.isNumeric(nextPathItem)) {
      ArrayList<Any?>()
    } else {
      HashMap<String, Any?>()
    }
  }

  private fun fixValueType(cellRawValue: Any?, type: String, columnIndex: Int): Any? {
    return when (type) {
      "String" -> cellRawValue?.toString() ?: ""
      "Int" -> NumHelper.anyToLong(cellRawValue)
      "Float" -> NumHelper.anyToDouble(cellRawValue)
      "Boolean" -> BoolHelper.anyToBool(cellRawValue)
      else -> throw BzError("errCodeErr", "BadColumnType: ${columnIndex + 1} ${type}")
    }
  }

}

data class BinRobotArgs(
  @JvmField
  val bin: String,
  @JvmField
  val action: String,
  @JvmField
  val site: String = "",
  @JvmField
  val fields: EntityValue = mutableMapOf(),
  @JvmField
  val rbkFields: EntityValue = mutableMapOf()
)