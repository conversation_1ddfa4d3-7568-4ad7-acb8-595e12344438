package com.seer.trick.robot.vendor.hik

import com.seer.trick.BzError
import com.seer.trick.helper.IpHelper
import com.seer.trick.helper.NumHelper
import com.seer.trick.robot.rachel.RobotVendor
import com.seer.wcs.device.tcp.FixedHeadFrameSchema
import com.seer.wcs.device.tcp.TcpServer
import io.netty.buffer.ByteBuf
import io.netty.buffer.ByteBufUtil
import io.netty.buffer.CompositeByteBuf
import io.netty.buffer.Unpooled
import io.netty.channel.ChannelHandlerContext
import org.slf4j.LoggerFactory
import java.net.InetSocketAddress
import java.nio.charset.StandardCharsets
import java.time.Instant
import java.util.*
import java.util.concurrent.*


/**
 * TODO list
 *
 * 资源服务器 url
 * 地码类型
 * 协议版本号
 *
 * TODO 任务下发失败后的回调
 * TODO 申请资源
 */

object HikAdapter {

  private val logger = LoggerFactory.getLogger(this::class.java)

  val robots: MutableMap<Int, HikRobot> = ConcurrentHashMap()

  // @Volatile
  // var currentTaskId = 0
  // val taskIdMap: MutableMap<String, Int> = ConcurrentHashMap()
  //
  // @Volatile
  // var currentSubTaskId = 0
  // val subTaskIdMap: MutableMap<String, Int> = ConcurrentHashMap()
  // val taskIdLock = Object()

  private val executor = Executors.newSingleThreadExecutor()


  private val schema = FixedHeadFrameSchema(
    "GBP\$".toByteArray(StandardCharsets.US_ASCII),
    32,
    { buf -> buf.getShort(4).toInt() - 32 }, // 协议中的消息长度是整包（含头的），因此减去
    { head, body -> HkFrame(head, body) }
  )


  private val executor2: ScheduledExecutorService = Executors.newScheduledThreadPool(5)

  @Volatile
  private var future1: ScheduledFuture<*>? = null

  @Volatile
  private var future2: ScheduledFuture<*>? = null

  // 保留字节 17

  private val reserved1 = ByteArray(1)
  private val reserved2 = ByteArray(2)
  private val reserved3 = ByteArray(3)
  private val reserved6 = ByteArray(6)
  private val reserved7 = ByteArray(7)
  private val reserved8 = ByteArray(8)
  private val reserved10 = ByteArray(10)
  private val reserved12 = ByteArray(12)
  private val reserved16 = ByteArray(16)
  private val headReserved17 = ByteArray(17)
  private val reserved18 = ByteArray(18)
  private val reserved20 = ByteArray(20)
  private val reserved28 = ByteArray(28)
  private val reserved40 = ByteArray(40)
  private val reserved48 = ByteArray(48)
  private val reserved80 = ByteArray(80)
  private val reserved96 = ByteArray(96)

  init {
    Arrays.fill(headReserved17, 0)
    // robots[143] = HikRobot(143, true)
    // robots[199] = HikRobot(199, true)
  }

  fun start() {
    TcpServer("海康适配器", 8988, schema, false, HikAdapter::onServerMessage)
    future1 = executor2.scheduleAtFixedRate(this::markOffline, 0, 1, TimeUnit.SECONDS)
    future2 = executor2.scheduleAtFixedRate(this::sendNextTask, 0, 500, TimeUnit.MILLISECONDS)
  }

  private fun mustGetRobot(robotNo: Int): HikRobot {
    return robots[robotNo] ?: throw BzError("errNoRobot", robotNo)
  }

  private fun markOffline() {
    for (r in robots.values) {
      if (Instant.now().minusMillis(TimeUnit.SECONDS.toMillis(5)).isAfter(r.lastReportOn.toInstant())) {
        if (r.status != -1) logger.debug("mark robot[${r.robotNo}] offline, status=0xffff")
        r.status = 0x57 // 87
        r.statusHex = "0x57"
        r.statusStr = "设备与平台失联"
        r.report = r.report.copy(
          status = 0x57, // 87
          statusHex = "0x57",
          statusStr = "设备与平台失联"
        )
      }
    }
  }

  //////////////////////////////////////////////////////////////////////////
  // fun transTaskId(taskIdU: String): Int {
  //   synchronized(taskIdLock) {
  //     val t = taskIdMap[taskIdU]
  //     if (t != null) {
  //       return t
  //     } else {
  //       currentTaskId = (currentTaskId + 1) % 65535
  //       taskIdMap[taskIdU] = currentTaskId
  //       return currentTaskId
  //     }
  //   }
  // }
  //
  // fun transSubTaskId(subTaskIdU: String): Int {
  //   synchronized(taskIdLock) {
  //     val t = subTaskIdMap[subTaskIdU]
  //     if (t != null) {
  //       return t
  //     } else {
  //       currentSubTaskId = (currentSubTaskId + 1) % 255
  //       subTaskIdMap[subTaskIdU] = currentSubTaskId
  //       return currentSubTaskId
  //     }
  //   }
  // }
  //
  // fun removeTask(taskId: Int, subTaskId: Int) {
  //   synchronized(taskIdLock) {
  //     taskIdMap.entries.removeIf { it.value == taskId }
  //     subTaskIdMap.entries.removeIf { it.value == subTaskId }
  //   }
  // }

  //////////////////////////////////////////////////////////////////////////

  fun test(req: MutableMap<String, Any?>) {
    val mode = req["mode"] as String

    when (mode) {
      // "322" -> test322(req) // 空车运动
      "A00" -> testA00(req)
      "700" -> test700(req)
      // "312" -> test312(req) // 单独举升
      // "314" -> test314(req) // 单独放下举升机构
      // "324" -> test324(req) // 载货移动
      // "326" -> test326(req) // 放货 TODO 并移动？
      // "32c" -> test32c(req) // 充电
      // "904" -> test904(req) // 取消任务
      "200" -> test200(req) // 取消任务
      // "216" -> test216(req) // 开启/关闭障碍物轮廓信息上报
    }
  }

  private fun testA00(req: MutableMap<String, Any?>) {
    val robotNo = req["robotNo"] as Int
    sendA00(robotNo)
  }

  private fun sendA00(robotNo: Int) {
    val robot = mustGetRobot(robotNo)

    val flowNo = synchronized(this) {
      robot.flowNo + 1
    }

    val bodyBuf = Unpooled.buffer(48)
    bodyBuf.writeInt(robotNo)
    bodyBuf.writeByte(0)
    bodyBuf.writeBytes(reserved3)
    bodyBuf.writeBytes(reserved40)

    val resFrame = buildFrame(0xA00, flowNo, 0, bodyBuf)
    robot.ctx?.writeAndFlush(resFrame)
    logger.info("Written A00 $robotNo")
  }

  private fun send7f04(robotNo: Int) {
    val robot = mustGetRobot(robotNo)

    val flowNo = synchronized(this) {
      robot.flowNo + 1
    }

    val bodyBuf = Unpooled.buffer(264)
    bodyBuf.writeInt(robotNo) // 设备编号 4
    bodyBuf.writeByte(0x77) // 任务 1
    bodyBuf.writeByte(0) // 保留 1
    bodyBuf.writeShort(0) // 资源类型 2

    // TODO: 获取服务器的网络 IP，且是能 ping 通机器人 IP。
    //    服务和机器人在同网段时：可以根据机器人的 IP，从本机 IP 列表中筛选。
    //    服务就运行在海康机器人的控制器里面时：用 localhost 或者 127.0.0.1 作为 URL 中的 IP 即可。
    //    服务和机器人在不同网段，且本机存在多个 IP 地址时：？？？
    val localUrl = "http://**************:5800/api/wcs/hik/resources"
    "http://***********:8182/rcms/web/slamMap/getFileListForAgv.action"
    bodyBuf.writeBytes(
      toByteArray("$localUrl?deviceCode=$robotNo", 256)
    ) // url 256
    // 文件内容如下：
    // {
    //     "files": [
    //         {
    //             "size": 171130,
    //             "name": "SLAM_qq_1.hka",
    //             "md5": "3e31028d223b7879c411da953bc1316f"
    //         },
    //         {
    //             "size": 5889,
    //             "name": "MAP_qq_1.lmap",
    //             "md5": "6dd4264637fa6ab5b0c30750d7052bf7"
    //         },
    //         {
    //             "name": "QR_DD_2.xml",
    //             "size": 13959,
    //             "version": 2,
    //             "md5": "550f424fed9ceebffb7e7a86238a50ee"
    //         },
    //         {
    //             "size": 287,
    //             "name": "pod_conf.xml",
    //             "md5": "6e9491fe793f2ac86a7027da59cc462d"
    //         }
    //     ],
    //     "file_num": 4
    // }

    val resFrame = buildFrame(0x7f04, flowNo, 0, bodyBuf)
    robot.ctx?.writeAndFlush(resFrame)
    logger.info("Written 7f04 $robotNo")
  }

  private fun test700(req: MutableMap<String, Any?>) {
    val robotNo = req["robotNo"] as Int
    val robot = mustGetRobot(robotNo)

    val flowNo = synchronized(this) {
      robot.flowNo + 1
    }

    val bodyBuf = Unpooled.buffer(8)
    bodyBuf.writeInt(robotNo)
    bodyBuf.writeByte(req["op"] as Int) // 控制类型 0 举升 1 放下
    bodyBuf.writeByte(0)
    bodyBuf.writeShort(0)

    val resFrame = buildFrame(0x700, flowNo, 0, bodyBuf)
    robot.ctx?.writeAndFlush(resFrame)
    logger.info("Written 700 $robotNo")
  }

  // 312 举升货架
  fun jackLoad312(req: Load312Req) {
    val robotNo = req.robotNo
    val robot = mustGetRobot(req.robotNo)

    val flowNo = synchronized(this) {
      robot.flowNo + 1
    }

    val body = build312Req(req)
    logger.debug("Send 312 $robotNo")
    val frame = buildFrame(0x312, flowNo, 0, body)
    robot.ctx?.writeAndFlush(frame)
    logger.info("Written 312 $robotNo")
  }


  // 314 下放货架
  fun jackUnload314(req: Unload314Req) {
    val robotNo = req.robotNo
    val robot = mustGetRobot(req.robotNo)

    val flowNo = synchronized(this) {
      robot.flowNo + 1
    }

    val body = build314Req(req)

    logger.debug("Send 312 $robotNo")
    val frame = buildFrame(0x314, flowNo, 0, body)
    robot.ctx?.writeAndFlush(frame)
    logger.info("Written 312 $robotNo")
  }

  // 324 移动并取放货
  fun jackMove324(req: LoadMove324Req) {
    val robotNo = req.robotNo
    val robot = mustGetRobot(req.robotNo)

    val flowNo = synchronized(this) {
      robot.flowNo + 1
    }

    val body324 = build324Req(req)

    logger.debug("Send 324 $robotNo")
    val frame = buildFrame(0x324, flowNo, 0, body324)
    robot.ctx?.writeAndFlush(frame)
    logger.info("Written 324 $robotNo")
  }

  // 32c 充电
  fun charge32c(req: Charge32cReq) {
    val robotNo = req.robotNo
    val robot = mustGetRobot(req.robotNo)

    val flowNo = synchronized(this) {
      robot.flowNo + 1
    }

    val body32c = build32cReq(req)

    logger.debug("Send 32c $robotNo")
    val frame = buildFrame(0x32c, flowNo, 0, body32c)
    robot.ctx?.writeAndFlush(frame)
    logger.info("Written 32c $robotNo")
  }

  // 获取设备能力集
  private fun test200(req: MutableMap<String, Any?>) {
    val robotNo = req["robotNo"] as Int
    val robot = mustGetRobot(robotNo)

    val flowNo = synchronized(this) {
      robot.flowNo + 1
    }

    // 任务基本信息
    val body = Unpooled.buffer(4)
    body.writeInt(robotNo) // 设备编号

    logger.debug("Send 200 $robotNo")
    val frame = buildFrame(0x200, flowNo, 0, body)
    robot.ctx?.writeAndFlush(frame)
    logger.info("Written 200 $robotNo")
  }

  // 暂停任务
  fun pause900(req: Pause900Req) {
    val robotNo = req.robotNo
    val robot = mustGetRobot(req.robotNo)

    val flowNo = synchronized(this) {
      robot.flowNo + 1
    }

    // 任务基本信息
    val body = Unpooled.buffer(4 + 2 + 1 + 1)
    body.writeInt(robotNo) // 设备编号
    body.writeShort(req.taskId) // 任务ID
    body.writeByte(req.subTaskId) // 任务子ID
    body.writeBytes(reserved1) // 保留    1

    logger.debug("Send 900 $robotNo")
    val frame = buildFrame(0x900, flowNo, 0, body)
    robot.ctx?.writeAndFlush(frame)
    logger.info("Written 900 $robotNo")
  }

  // 继续任务
  fun resume902(req: Resume902Req) {
    val robotNo = req.robotNo
    val robot = mustGetRobot(req.robotNo)

    val flowNo = synchronized(this) {
      robot.flowNo + 1
    }

    // 任务基本信息
    val body = Unpooled.buffer(4 + 2 + 1 + 1)
    body.writeInt(robotNo) // 设备编号
    body.writeShort(req.taskId) // 任务ID
    body.writeByte(req.subTaskId) // 任务子ID
    body.writeBytes(reserved1) // 保留    1

    logger.debug("Send 902 $robotNo")
    val frame = buildFrame(0x902, flowNo, 0, body)
    robot.ctx?.writeAndFlush(frame)
    logger.info("Written 902 $robotNo")
  }

  // 取消任务
  fun cancel904(req: Cancel904Req) {
    val robotNo = req.robotNo
    val robot = mustGetRobot(req.robotNo)

    val flowNo = synchronized(this) {
      robots[robotNo]?.tasks?.clear()
      robot.flowNo + 1
    }

    // 任务基本信息
    val body = Unpooled.buffer(4 + 2 + 1 + 1)
    body.writeInt(robotNo) // 设备编号
    body.writeShort(req.taskId) // 任务ID
    body.writeByte(req.subTaskId) // 任务子ID
    body.writeBytes(reserved1) // 保留    1

    logger.debug("Send 904 $robotNo")
    val frame = buildFrame(0x904, flowNo, 0, body)
    robot.ctx?.writeAndFlush(frame)
    logger.info("Written 904 $robotNo")
  }

  // 开启障碍物轮廓信息上报
  fun changeSlamSetting(req: ChangeSlamSettingReq) {
    val robotNo = req.robotNo
    val robot = mustGetRobot(req.robotNo)

    val flowNo = synchronized(this) {
      robot.flowNo + 1
    }

    // 任务基本信息
    val body = Unpooled.buffer(4 + 1 + 3)
    body.writeInt(robotNo) // 设备编号
    body.writeByte(req.op) // 开关 0 关闭，1 开启
    body.writeBytes(reserved3) // 保留    3

    logger.debug("Send 216 $robotNo")
    val frame = buildFrame(0x216, flowNo, 0, body)
    robot.ctx?.writeAndFlush(frame)
    logger.info("Written 216 $robotNo")


    synchronized(this) {
      if (req.op == 1) {
        val new = robot.copy()
        new.config.laserReport = true
        robots[robotNo] = new
      } else {
        val new = robot.copy()
        new.config.laserReport = false
        new.laser = HikLaser()
        robots[robotNo] = new
      }
    }
  }

  fun get218SlamRes(robotNo: Int): HikLaser {
    val robot = mustGetRobot(robotNo)
    return if (robot.config.laserReport)
      robot.laser
    else throw BzError("errCodeErr", "Robot [$robotNo] SLAM report not enabled")
  }

  fun updateLockStatus(robotNo: Int, locked: Boolean, nickName: String) {
    val robot = mustGetRobot(robotNo)
    synchronized(this) {
      if (!locked && robot.nickName != nickName) throw BzError("errCodeErr", "No control to release")
      val old = robot.locked
      robot.locked = locked
      val nn = if (locked) nickName else null
      robot.nickName = nn
      logger.info("update locked status of robot[$robotNo] from $old to $locked, nickName=$nn")
    }
  }

  fun move322(req: Move322Req) {
    val robotNo = req.robotNo
    val robot = mustGetRobot(req.robotNo)

    val flowNo = synchronized(this) {
      robot.flowNo + 1
    }

    val body322 = build322Req(req)
    logger.debug("Send 322 $robotNo")
    val frame = buildFrame(0x322, flowNo, 0, body322)
    try {
      robot.ctx?.writeAndFlush(frame) // TODO 写失败的情况 DefaultChannelPromise@3684a5d7(failure: io.netty.channel.StacklessClosedChannelException)
    } catch (e: Exception) {
      logger.error("Send 322 writeAndFlush error, $robotNo error", e)
      throw e
    }
  }

  fun move322V4(reqs: List<Move322Req>) {
    if (reqs.isEmpty()) return

    for (req in reqs) {
      val robotNo = req.robotNo

      synchronized(this) { // TODO 整个任务不是原子的，可能会前面的追加成功了，后面的追加失败抛异常。后面改为 sync 中先检查后追加任务
        val robot = mustGetRobot(req.robotNo)
        val exist =
          robot.tasks.firstOrNull { it.taskId == req.taskId && it.subTaskId == req.subTaskId } // TODO 这里暂时认为 单台车上累加任务的 taskId、subTaskId 不会重复。
        if (exist == null)
          robot.tasks += req
        else {
          logger.info("SubTask already exist, robotNo=$robotNo, taskId=${req.taskId}, subTaskId=${req.subTaskId}")
        }
      }
    }
  }


  fun sendNextTask() {
    for (robotNo in robots.keys) {
      synchronized(this) {
        val robot = robots[robotNo] ?: return@synchronized
        if (robot.tasks.isEmpty()) return@synchronized


        val nextTask = robot.tasks.get(0)
        when {
          nextTask is Move322Req -> {
            executor.submit {
              Thread.sleep(500)
              val flowNo = synchronized(this) {
                robot.flowNo + 1
              }
              val body = build322Req(nextTask)
              logger.debug("Send 322 $robotNo")
              val frame = buildFrame(0x322, flowNo, 0, body)
              try {
                robot.ctx?.writeAndFlush(frame) // TODO 写失败的情况 DefaultChannelPromise@3684a5d7(failure: io.netty.channel.StacklessClosedChannelException)
              } catch (e: Exception) {
                logger.error("Send 322 writeAndFlush error, $robotNo error", e)
                throw e
              }
            }

          }

          nextTask is Load312Req -> {
            val flowNo = synchronized(this) {
              robot.flowNo + 1
            }
            val body = build312Req(nextTask)
            logger.debug("Send 312 $robotNo")
            val frame = buildFrame(0x312, flowNo, 0, body)
            robot.ctx?.writeAndFlush(frame)
            logger.info("Written 312 $robotNo")
          }

          nextTask is Unload314Req -> {
            val flowNo = synchronized(this) {
              robot.flowNo + 1
            }
            val body = build314Req(nextTask)
            logger.debug("Send 314 $robotNo")
            val frame = buildFrame(0x314, flowNo, 0, body)
            robot.ctx?.writeAndFlush(frame)
            logger.info("Written 314 $robotNo")
          }

          nextTask is LoadMove324Req -> {
            val flowNo = synchronized(this) {
              robot.flowNo + 1
            }
            val body = build324Req(nextTask)
            logger.debug("Send 324 $robotNo")
            val frame = buildFrame(0x324, flowNo, 0, body)
            robot.ctx?.writeAndFlush(frame)
            logger.info("Written 324 $robotNo")
          }

          else -> {
            throw BzError("errCodeErr", "Unsupported task type，type=${nextTask::javaClass.name}")
          }
        }
      }
    }
  }


  //////////////////////////////////////////////////////////////////////////

  private fun onServerMessage(ctx: ChannelHandlerContext, frame: HkFrame) {
    try {
      val msgType = frame.head.getShort(6).toInt() // 消息类型
      val msgType16 = Integer.toHexString(msgType)
      val flowNo = frame.head.getInt(8)
      val protoVersion = frame.head.getByte(12).toInt()
      logger.debug("on msgType: 0x$msgType16, flowNo=$flowNo")
      
      when (msgType) {
        // 机器人注册
        0x01 -> onServerRegister(ctx, frame, flowNo, protoVersion)
  
        // 机器人能力集合
        0x202 -> onServer202(ctx, frame, flowNo, protoVersion)
        // 机器人上报电池信息
        0x212 -> onBatteryReport(ctx, frame, flowNo, protoVersion)
        // SLAM 定位信息上报
        0x214 -> onLocatingInfoReport(ctx, frame, flowNo, protoVersion)
        // 实时点云
        0x217 -> onServer217(ctx, frame, flowNo, protoVersion)
        0x218 -> onServer218(ctx, frame, flowNo, protoVersion)
        // 机器人上报桩略
        0x300 -> onServerReport(ctx, frame, flowNo, protoVersion)
        0x303 -> onServer303(ctx, frame, flowNo, protoVersion)
        // 0x323 -> onServer323(ctx, frame, flowNo, protoVersion)
        0x323 -> onServer323V4(ctx, frame, flowNo, protoVersion)
  
        0x101 -> onServer101(ctx, frame, flowNo, protoVersion)
        0x7f04 -> onServer7f05(ctx, frame, flowNo, protoVersion)
  
        // 机器人申请空间锁定
        0x800 -> onServer800SpaceRequest(ctx, frame, flowNo, protoVersion)
  
        else -> {
          val robotNo = frame.body.readInt() // 设备编号 4
          synchronized(this) {
            if (!robots.containsKey(robotNo)) {
              robots[robotNo] = HikRobot(
                robotNo,
                lastReportOn = Date(),
              )
            } else {
              // TODO 关掉 ctx
              robots[robotNo]?.ctx = ctx
            }
          }
          logger.warn("Un processed msg type: 0x$msgType16")
        }
      }
    } finally {
      frame.head.release()
      frame.body.release()
    }
  }

  private fun onServerRegister(ctx: ChannelHandlerContext, frame: HkFrame, flowNo: Int, protoVersion: Int) {
    val robotNo = frame.body.readInt() // 设备编号 4
    frame.body.readInt() // 设备厂商 4
    frame.body.readBytes(48) // 设备序列号 48
    frame.body.readByte() // 设备类型 1
    frame.body.readByte() // 设备状态 1
    val length = frame.body.readShort().toInt() // 设备长度 2
    val width = frame.body.readShort().toInt() // 设备宽度 2
    val height = frame.body.readShort().toInt() // 设备高度 2
    val rotateDiameter = frame.body.readShort().toInt() // 设备旋转直径 2
    val weight = frame.body.readShort().toInt() // 设备重量 2

    val ip = (ctx.channel().remoteAddress() as InetSocketAddress).address.hostAddress
    val port = (ctx.channel().remoteAddress() as InetSocketAddress).port

    logger.info("Robot register: $robotNo, protoVersion=$protoVersion ip=$ip port=$port")

    synchronized(this) {
      val robot = robots[robotNo]
      if (robot == null) {
        val new = HikRobot(
          robotNo,
          lastReportOn = Date(),
          basic = HikBasic(
            length = length,
            width = width,
            height = height,
            weight = weight,
            rotateDiameter = rotateDiameter,
          ), ctx = ctx
        )
        new.config.ip = ip
        new.config.port = port
        robots[robotNo] = new
      } else {
        // TODO 关掉 ctx
        robots[robotNo]?.ctx = ctx
      }
    }

    send7f04(robotNo)

    val bodyBuf = Unpooled.buffer(12)
    bodyBuf.writeInt(robotNo) // 设备编号 4
    bodyBuf.writeInt(200) // 回复结果 4
    bodyBuf.writeByte(0) // 异常码 1
    bodyBuf.writeBytes("AA".toByteArray(StandardCharsets.US_ASCII)) // TODO 地码类型 2 二维码 XY、激光 DD
    // bodyBuf.writeBytes("XY".toByteArray(StandardCharsets.US_ASCII)) // TODO 地码类型 2 二维码 XY、激光 DD
    bodyBuf.writeByte(0) // 保留字节 1

    val resFrame = buildFrame(0x2, flowNo, protoVersion, bodyBuf)
    ctx.writeAndFlush(resFrame)
    logger.info("Written res, Robot register: $robotNo")
  }

  private fun onBatteryReport(ctx: ChannelHandlerContext, frame: HkFrame, flowNo: Int, protoVersion: Int) {
    val robotNo = frame.body.readInt() // 设备编号
    val batteryLevel = frame.body.readByte().toInt() // 电池电量

    synchronized(this) {
      val robot = robots[robotNo]
      if (robot != null) {
        robots[robotNo] = robot.copy(
          lastReportOn = Date(),
          ctx = ctx
        )
      } else {
        logger.warn("No report, but no robot register $robotNo")
      }
    }

    val bodyBuf = Unpooled.buffer(12)
    bodyBuf.writeInt(robotNo) // 设备编号
    bodyBuf.writeInt(200) // 回复结果
    bodyBuf.writeByte(0) // 异常码
    bodyBuf.writeBytes(reserved3) // 保留字节

    val resFrame = buildFrame(0x213, flowNo, protoVersion, bodyBuf)
    ctx.writeAndFlush(resFrame)
    logger.info("Written res, Robot battery: $robotNo, $batteryLevel")
  }

  // SLAM 定位信息上报
  private fun onLocatingInfoReport(ctx: ChannelHandlerContext, frame: HkFrame, flowNo: Int, protoVersion: Int) {
    val robotNo = frame.body.readInt() // 设备编号(4)
    val posX = frame.body.readInt() // 当前位置X坐标(4)
    val posY = frame.body.readInt() // 当前位置Y坐标(4)
    val direction = frame.body.readInt() // 当前角度(4)
    val confidence = frame.body.readInt() // 置信度(4)
    val pos = "pos($posX, $posY, $direction)"
    var msg = ""

    synchronized(this) {
      val robot = robots[robotNo]
      if (robot != null) {
        // 仅更新机器人的置信度即可。
        //    从文档中的描述可知，当设备检测到 SLAM 定位置信度较差且定位存在跳变时，机器人才会上报上述信息，
        //    所以从定时上报的「任务状态(0x300)」中，获取设备的位置信息和角度，会更可靠。
        val oldReport = robot.report
        val oldConfidence = oldReport.confidence
        if (oldConfidence != confidence) {
          msg = "Robot[$robotNo] confidence changed $oldConfidence -> $confidence at $pos"
          logger.info(msg)
          robot.report = oldReport.copy(confidence = confidence)
        }
      } else {
        msg = "Unrecorded robot reports Locating info: Robot[$robotNo] current confidence=$confidence at $pos"
        logger.warn(msg)
      }
    }

    val bodyBuf = Unpooled.buffer(12)
    bodyBuf.writeInt(robotNo) // 设备编号
    bodyBuf.writeInt(200) // 回复结果。 200：成功；201：失败。
    bodyBuf.writeByte(0) // 异常码
    bodyBuf.writeBytes(reserved3) // 保留字节

    val resFrame = buildFrame(0x213, flowNo, protoVersion, bodyBuf)
    ctx.writeAndFlush(resFrame)
    logger.info("Written res, Robot confidence: $msg")
  }

  private fun onServerReport(ctx: ChannelHandlerContext, frame: HkFrame, flowNo: Int, protoVersion: Int) {
    val robotNo = frame.body.readInt() // 设备编号  4
    val taskId = frame.body.readShort().toInt() // 任务 ID  2
    val subTaskId = frame.body.readByte().toInt() // 子任务 ID 1
    frame.body.readByte() // 姿态信任校验标识 1
    val status = frame.body.readInt() // 设备状态 4
    val x = frame.body.readInt() //  // X 坐标 4
    val y = frame.body.readInt() // Y 坐标 4
    val direction = frame.body.readInt() // 角度 4
    val speed = frame.body.readInt() // 速度 4
    frame.body.readInt() // 线加速度 4
    frame.body.readInt() // 线减速度 4
    frame.body.readInt() // 角速度 4
    frame.body.readInt() // 角加速度 4
    frame.body.readShort() // 地码类型 2
    frame.body.readByte() // 升级状态 1
    frame.body.readByte() // 资源下载状态 1
    frame.body.readShort() // 主告警号 2
    frame.body.readShort() // 子告警号 2
    frame.body.readShort().toInt() // 电池温度 2
    val batteryAmm = frame.body.readShort().toInt() // 电池电流 2
    val batterVot = frame.body.readShort().toInt() // 电池电压 2
    val batterLevel = frame.body.readByte().toInt() // 电池电量 1
    frame.body.readByte() // 保留 1
    frame.body.readInt() // 角减速度 4
    // 我们期望的置信度，应该是机器人通过 0x214 指令上报的置信度，而非巡线置信度。
    frame.body.readByte().toInt() // 巡线置信度 1  目前仅用于牵引车的巡线功能，表示巡线定位的置信度；设备扫到二维码时，置信度为 100，连续 1 米未扫到地码，置信度变为 0.
    frame.body.readByte().toInt() // 当前导航类型 1
    frame.body.readBytes(41) // 预留 41
    val actuatorIdx = frame.body.readByte().toInt() // 执行机构索引 1
    // 执行机构信息
    val actuatorInfo: Any? = when (actuatorIdx) {
      3 -> { // 探测到的货架信息
        // （注意读取顺序）：shelfId_32 + direction3_4 + diffX_2 + diffY_2 + reserved_4 + diffDirection_4
        val shelfIdByteBuf = frame.body.readBytes(32) // 货物字符串 ID  32
        val shelfIdBa = ByteArray(shelfIdByteBuf.readableBytes())
        shelfIdByteBuf.readBytes(shelfIdBa)
        val shelfId = byteArrayToString(shelfIdBa)
        val direction3 = frame.body.readInt()
        val diffX = frame.body.readShort()
        val diffY = frame.body.readShort()
        frame.body.readBytes(4)         // 保留 4 bytes
        val diffDirection = frame.body.readInt()

        ShelfInfoDetected(shelfId, direction3, diffX, diffY, diffDirection)
      }

      4 -> {
        // 货物信息（注意读取顺序）：shelfId_32 + posX_4 + posY_4 + direction_4 + status_1 + reserved_3
        val shelfIdByteBuf = frame.body.readBytes(32) // 货物字符串 ID  32
        val shelfIdBa = ByteArray(shelfIdByteBuf.readableBytes())
        shelfIdByteBuf.readBytes(shelfIdBa)
        val shelfId = byteArrayToString(shelfIdBa)
        val posX = frame.body.readInt() // 货物 x 坐标  4
        val posY = frame.body.readInt() // 货物 y 坐标 4
        val direction4 = frame.body.readInt() // 探测到的货物角度。
        val status4 = frame.body.readByte().toInt() // 举升状态 1
        // 保留 3

        ShelfInfo(shelfId, posX, posY, direction4, status4)
      }

      else -> {
        // 0：无附加信息； 1：滚筒设备； 2：预留； 5：预留； 6：预留； 7：叉车信息
        null
      }

    }

    val statusHex = "0x${Integer.toHexString(status)}" // 将 Int 类型的 status 转换为 hexString，并附加 0x 前缀后，再输出

    logger.info("Robot report: $robotNo, [$taskId:$subTaskId] status=$statusHex, ($x, $y, @$direction) v=$speed battery=$batterLevel")

    synchronized(this) {
      val robot = robots[robotNo]
      if (robot == null) {
        logger.warn("No report, but no robot register $robotNo")
      } else {
        robots[robotNo] = robot.copy(
          lastReportOn = Date(),
          reported = true,
          status = status, statusHex = statusHex, statusStr = getRobotStatusStr(statusHex),
          reportTaskId = taskId, reportSubTaskId = subTaskId,
          report = HikReport(
            status = status, statusHex = statusHex, statusStr = getRobotStatusStr(statusHex),
            x = x, y = y, direction = direction, speed = speed,
            batteryAmm = batteryAmm, batteryVol = batterVot, batteryLevel = batterLevel,
            confidence = robot.report.confidence, taskId = taskId, subTaskId = subTaskId,
            actuatorIdx = actuatorIdx, actuatorInfo = actuatorInfo
          )
        )
        if (status > 0x41) {
          // cancelMove(ctx, robot, protoVersion)
        } else {
          // 机器人上报的 taskId + subTaskId 一定是收到了的
          if (robot.tasks.isNotEmpty()) {
            if (robot.tasks[0].taskId == taskId && robot.tasks[0].subTaskId == subTaskId) {
              logger.warn("remove task 0, robotNo=${robotNo}, taskId=${robot.tasks[0].taskId}, subTaskId=${robot.tasks[0].subTaskId}")
              robot.tasks.removeAt(0)
              // executor.submit {
              //   synchronized(this) {
              //     logger.warn("remove task 0, robotNo=${robotNo}, taskId=${robot.tasks[0].taskId}, subTaskId=${robot.tasks[0].subTaskId}")
              //     robot.tasks.removeAt(0)
              //     if (robots[robotNo]?.tasks?.isNotEmpty() == true) robots[robotNo]?.tasks?.removeAt(0)
              //   }
              // }
            } else if (robot.tasks[0].taskId != taskId) {

            }
          }

          if (status == 1 || status == 2) {


            // 任务完成了
            executor.submit {
              try {
                // testMove(ctx, robotNo, protoVersion)
                // testMove322(ctx, robotNo, protoVersion)
                // testMove322Shuttle(ctx, robotNo, protoVersion)
                // testMove322Circle(ctx, robotNo, protoVersion)

                // 移除缓存的任务 TODO 更好的时机
                // removeTask(taskId, subTaskId)
              } catch (e: Exception) {
                logger.error("testMove", e)
              }
            }
          }
        }
      }
    }

    val bodyBuf = Unpooled.buffer(12)
    bodyBuf.writeInt(robotNo) // 设备编号 4
    bodyBuf.writeInt(200) // 回复结果 4
    bodyBuf.writeByte(0) // 异常码 1
    bodyBuf.writeBytes(reserved3) // 保留字节 3

    val resFrame = buildFrame(0x301, flowNo, protoVersion, bodyBuf)
    ctx.writeAndFlush(resFrame)
    logger.debug("Written res, Robot report: $robotNo")
  }

  private fun getRobotStatusStr(code: String): String {
    return hikRobotStatusCodeStrMap[code] ?: ""
  }

  private val hikRobotStatusCodeStrMap = mapOf(
    "0x1" to "设备空闲，设备处于 ready 状态，即可以执行指令的状态",
    "0x2" to "设备已完成收到的任务",
    "0x3" to "控制指令执行中，设备正在执行平台发的控制指令",
    "0x4" to "弧线行走中，设备收到平台发的弧线控制指令，且处于弧线段的路径上运动",
    "0x5" to "设备的举升机构正在执行动作，举升或下放",
    "0x6" to "设备正在进行自主调整",
    "0x7" to "设备正在充电",
    "0x8" to "设备正在进行电池充满维护",
    "0x9" to "设备正在进行动态绕障",
    "0xA" to "设备正在巡线中（牵引车使用）",
    "0xB" to "设备强制完成当前任务",
    "0xC" to "设备滚动控制中",
    "0xD" to "设备对接微调中",
    "0x41" to "前方遇障",
    "0x42" to "后方遇障",
    "0x43" to "左侧遇障",
    "0x44" to "右侧遇障",
    "0x45" to "弧线移动时前方遇障",
    "0x46" to "弧线移动时后方遇障",
    "0x47" to "弧线移动时左侧遇障",
    "0x48" to "弧线移动时右侧遇障",
    "0x49" to "设备动态绕障失败",
    "0x57" to "设备与平台失联",
    "0x81" to "设备收到暂停指令，并处于暂停状态",
    "0x82" to "设备处于异常中，如拍急停，或硬件出错等",
    "0x83" to "异常偏航",
    "0xC1" to "平台指令错误",
    "0x381" to "待机模式中",
    "0x385" to "电量过低预警"
  )

  private fun onServer202(ctx: ChannelHandlerContext, frame: HkFrame, flowNo: Int, protoVersion: Int) {
    val robotNo = frame.body.readInt() // 设备编号 4
    val loadCapability = frame.body.readShort() // 负载能力 2
    frame.body.readUnsignedByte() // 续航能力 1
    frame.body.readByte() // 是否支持称重 1
    // 精度 ：
    frame.body.readByte() // 空载精度 1
    frame.body.readByte() // 载货精度 1
    frame.body.readByte() // 最大货架偏移 1
    frame.body.readByte() // 保留字节 1
    // 空车运动性能：
    val acc = frame.body.readInt() // 直线加速度 4
    val dece = frame.body.readInt() // 直线减速度 4
    val speed = frame.body.readInt() // 最大线速度 4

    val wAcc = frame.body.readInt() // 角加速度 4
    val wDece = frame.body.readInt() // 角减速度 4
    val wSpeed = frame.body.readInt() // 最大角速度 4

    val bAcc = frame.body.readInt() // 弧线加速度 4
    val bDece = frame.body.readInt() // 弧线减速度 4
    val bSpeed = frame.body.readInt() // 弧线最大速度 4

    synchronized(this) {
      val robot = robots[robotNo]
      if (robot == null) {
        logger.warn("No report, but no robot register $robotNo")
      } else {
        robots[robotNo] = robot.copy(
          lastReportOn = Date(),
          reported = true, feature = HikFeature(
            speed = speed, acc = acc, dece = dece,
            wSpeed = wSpeed, wAcc = wAcc, wDece = wDece,
            bSpeed = bSpeed, bAcc = bAcc, bDece = bDece,
          )
        )
      }
    }

    logger.info("$robotNo: 202, loadCapability: $loadCapability")
  }

  private fun onServer303(ctx: ChannelHandlerContext, frame: HkFrame, flowNo: Int, protoVersion: Int) {
    val robotNo = frame.body.readInt() // 设备编号
    val status = frame.body.readInt()
    if (status == 200) {
      logger.debug("$robotNo: 302/303 sent successful")
    } else {
      logger.warn("$robotNo: 302/303 sent failed")
    }
  }

  private fun onServer323(ctx: ChannelHandlerContext, frame: HkFrame, flowNo: Int, protoVersion: Int) {
    val robotNo = frame.body.readInt() // 设备编号
    val status = frame.body.readInt()
    if (status == 200) {
      logger.debug("$robotNo: 322/323 sent successful")
    } else {
      logger.warn("$robotNo: 322/323 sent failed")
    }
  }

  fun onServer323V4(ctx: ChannelHandlerContext, frame: HkFrame, flowNo: Int, protoVersion: Int) {
    frame.body.readInt()
    frame.body.readInt()

    // synchronized(this) {
    //   val robot = robots[robotNo] ?: throw BzError("NoRobot", robotNo)
    //   if (status == 200) { // 成功
    //     logger.debug("Robot receive 323 report SUCCESS, robotNo=$robotNo, status=$status")
    //     if (robot.tasks.isNotEmpty()) robot.tasks.removeAt(0)
    //     robots[robotNo] = robot
    //   } else {
    //     logger.warn("Robot receive 323 report FAILED, robotNo=$robotNo, status=$status")
    //   }
    // }
  }

  private fun onServer101(ctx: ChannelHandlerContext, frame: HkFrame, flowNo: Int, protoVersion: Int) {
    val robotNo = frame.body.readInt() // 设备编号
    val status = frame.body.readInt() //  状态 200 成功、其他 失败
    if (status == 200) {
      logger.debug("$robotNo: 0x101 状态上报成功")
    } else {
      logger.warn("$robotNo: 0x101 状态上报失败，status: $status")
    }
  }

  private fun onServer7f05(ctx: ChannelHandlerContext, frame: HkFrame, flowNo: Int, protoVersion: Int) {
    val robotNo = frame.body.readInt() // 设备编号
    val status = frame.body.readInt() //  状态 200 成功、其他 失败
    if (status == 200) {
      logger.debug("$robotNo: 7f05 资源下载成功")
    } else {
      logger.warn("$robotNo: 7f05 资源下载失败")
    }
  }

  private fun onServer217(ctx: ChannelHandlerContext, frame: HkFrame, flowNo: Int, protoVersion: Int) {
    val robotNo = frame.body.readInt() // 设备编号
    val status = frame.body.readInt() //  状态 200 成功、其他 失败
    if (status == 200) {
      logger.debug("$robotNo: 217 设置障碍物轮廓信息上报成功")
    } else {
      logger.warn("$robotNo: 217 设置障碍物轮廓信息上报失败")
    }
  }

  // 障碍物轮廓信息上报
  private fun onServer218(ctx: ChannelHandlerContext, frame: HkFrame, flowNo: Int, protoVersion: Int) {
    val robotNo = frame.body.readInt() // 设备编号  4
    val sortType = frame.body.readByte() // 排序类型  1
    val pointNum1 = frame.body.readUnsignedByte() // 障碍点数  1
    val pointNum2 = frame.body.readUnsignedByte() // 障碍点数溢出部分 1
    val none = frame.body.readByte() // 保留  1
    val posX = frame.body.readInt() // 激光 x 坐标
    val posY = frame.body.readInt() // 激光 y 坐标
    val posTheta = frame.body.readInt() // 激光偏航角度
    val none2 = frame.body.readInt() // 保留  4
    val details = frame.body.readBytes(pointNum1 + pointNum2)

    logger.debug(
      "$robotNo : 218 sortType = $sortType, pointNum1 = $pointNum1, pointNum2 = $pointNum2, " +
          "none = $none, posX = $posX, posY = $posY, posTheta = $posTheta, none2 = $none2"
    )
    logger.debug("{} : 218, details = {}", robotNo, details)

    val body = Unpooled.buffer(12)
    body.writeInt(robotNo)
    body.writeInt(200)
    body.writeInt(0)
    body.writeBytes(reserved3)
    val res = buildFrame(0x219, flowNo, protoVersion, body)
    ctx.writeAndFlush(res)
    logger.info("Written res, 219, robotNo = $robotNo")

    if (robots[robotNo]?.config?.laserReport != true) return

    val ba = ByteArray(pointNum1 + pointNum2)
    details.readBytes(ba)

    val laser = HikLaser(
      sortType = sortType.toInt(),
      pointNum = pointNum1 + pointNum2,
      posX = posX,
      posY = posY,
      posTheta = posTheta,
      details = ba.toList().map { NumHelper.uByteToInt(byte = it) }
      // details = ba.toList().map { org.eclipse.milo.opcua.stack.core.types.builtin.unsigned.UByte.valueOf(it).toInt() }
    )
    synchronized(this) {
      val robot = robots[robotNo]
      if (robot == null) {
        logger.warn("No report, but no robot register $robotNo")
      } else {
        robots[robotNo] = robot.copy(
          lastReportOn = Date(),
          laser = laser
        )
      }
    }
  }

  // private fun cancelMove(ctx: ChannelHandlerContext, robot: HikRobot, protoVersion: Int) {
  //   logger.warn("Cancel move, ${robot.robotNo}")
  //   val flowNo = synchronized(this) {
  //     val flowNo = robot.flowNo + 1
  //     robots[robot.robotNo] = robot.copy(flowNo = flowNo)
  //     flowNo
  //   }
  //   val body = Unpooled.buffer(8)
  //
  //   // 任务基本信息
  //   body.writeInt(robot.robotNo) // 设备编号
  //   body.writeShort(robot.reportTaskId) // 任务ID
  //   body.writeByte(robot.reportSubTaskId) // 任务子ID
  //   body.writeByte(0) // 保留
  //
  //   val resFrame = buildFrame(0x904, flowNo, protoVersion, body)
  //   ctx.writeAndFlush(resFrame)
  // }

  // 设备申请空间锁定
  private fun onServer800SpaceRequest(ctx: ChannelHandlerContext, frame: HkFrame, flowNo: Int, protoVersion: Int) {
    val robotNo = frame.body.readInt() // 设备编号
    logger.info("Space request: ${ByteBufUtil.hexDump(frame.head)}")

    val body = Unpooled.buffer(8)
    body.writeInt(robotNo) // 设备编号
    body.writeByte(1) // 回复结果
    body.writeByte(0) // 错误码
    body.writeByte(0) // 保留
    body.writeByte(0) // 保留

    val resFrame = buildFrame(0x801, flowNo, protoVersion, body)
    ctx.writeAndFlush(resFrame)
  }

  //////////////////////////////////////////////////////////////////////////

  private fun build312Req(req: Load312Req): ByteBuf {
    val body = Unpooled.buffer(28 + 52 + 8 + 24 + 28 + 48 + 84)

    // 任务基本信息
    writeTaskBaseInfo(body, req)

    // 当前目标信息
    writeTargetInfo(body, req.targetPosition)

    // 最终目标信息
    writeFinalPosition(body, req.finalPosition)

    // 避障参数
    writeHazardAvoidance(body, req.ha)

    // 保留 28
    body.writeBytes(reserved28)

    // 保留 48
    body.writeBytes(reserved48)

    // 货物信息 84
    writeGoods(body, req.goods)

    return body
  }

  private fun build314Req(req: Unload314Req): ByteBuf {
    val body = Unpooled.buffer(28 + 52 + 8 + 24 + 28 + 48 + 84)

    // 任务基本信息
    writeTaskBaseInfo(body, req)

    // 当前目标信息
    writeTargetInfo(body, req.targetPosition)

    // 最终目标信息
    writeFinalPosition(body, req.finalPosition)

    // 避障参数
    // write314HazardAvoidance(body)
    writeHazardAvoidance(body, req.ha)

    // 保留 28
    body.writeBytes(reserved28)

    // 保留 48
    body.writeBytes(reserved48)

    // 货物信息 84
    writeGoods(body, req.goods)

    return body
  }

  private fun build322Req(req: Move322Req): ByteBuf {
    val body = Unpooled.buffer(28 + 52 + 8 + 24 + 48 + 484 + 28) // 672

    // 任务基本信息
    writeTaskBaseInfo(body, req)

    // 目标信息，终点坐标
    writeTargetInfo(body, req.targetPosition)

    // 最终目标信息，这个看起来写啥都行
    writeFinalPosition(body, req.finalPosition)

    // 避障参数
    writeHazardAvoidance(body, req.ha)

    // 预留 48
    body.writeBytes(reserved48)

    // 多段路径信息
    writePathV4(body, req.sourcePosition, req.targetPosition, req.paths)

    // 执行机构信息，仅叉车使用，其他车型填 0
    body.writeBytes(reserved28)

    logger.info("322 body size ${body.readableBytes()}")

    return body
  }

  fun manualMove(req: MoveRobotReq) {
    val robotNo = NumHelper.anyToInt(req.robotId)
      ?: throw BzError("errCodeErr", "robotId '${req.robotId}' not an int")
    val robot = mustGetRobot(robotNo)

    val flowNo = synchronized(this) {
      robot.flowNo + 1
    }

    val req = Move322Req(
      robotNo = robotNo,
      taskId = 233,
      subTaskId = 0,
      taskType = 0,
      moveType = 0,
      sourcePosition = SourcePosition(robot.report.x, robot.report.y),
      targetPosition = req.target,
      finalPosition = FinalPosition(req.target.x, req.target.y),
      paths = listOf(Path(type = 0))
    )
    val body322 = build322Manual(req)

    logger.debug("Send 322 $robotNo")
    val frame = buildFrame(0x322, flowNo, 0, body322)
    try {
      robot.ctx?.writeAndFlush(frame) // TODO 写失败的情况 DefaultChannelPromise@3684a5d7(failure: io.netty.channel.StacklessClosedChannelException)
    } catch (e: Exception) {
      logger.error("Send 322 writeAndFlush error, $robotNo error", e)
      throw e
    }
  }

  private fun build322Manual(req: Move322Req): ByteBuf {
    val body = Unpooled.buffer(28 + 52 + 8 + 24 + 48 + 484 + 28) // 672

    // 任务基本信息
    writeTaskBaseInfo(body, req)

    // 目标信息，终点坐标
    writeTargetInfo(body, req.targetPosition)

    // 最终目标信息，这个看起来写啥都行
    writeFinalPosition(body, req.finalPosition)

    // 避障参数
    writeHazardAvoidance(body, req.ha)

    // 预留 48
    body.writeBytes(reserved48)

    // 多段路径信息 484
    writePath(body, req.sourcePosition, req.targetPosition, req.paths)

    // 执行机构信息，仅叉车使用，其他车型填 0
    body.writeBytes(reserved28)

    logger.info("322 body size ${body.readableBytes()}")

    return body
  }


  private fun build324Req(req: LoadMove324Req): ByteBuf {
    val body = Unpooled.buffer(28 + 52 + 8 + 24 + 48 + 484 + 28 + 84)

    // 任务基本信息 28
    writeTaskBaseInfo(body, req)

    // 当前目标信息 52
    writeTargetInfo(body, req.targetPosition)

    // 最终目标信息 8
    writeFinalPosition(body, req.finalPosition)

    // 避障参数 24
    writeHazardAvoidance(body, req.ha)

    // 保留 48
    body.writeBytes(reserved48)

    // 多段路径参数 484
    writePath(body, req.sourcePosition, req.targetPosition, req.paths)

    // 执行机构信息 28
    body.writeBytes(reserved28)

    // 货物信息 84
    writeGoods(body, req.goods)

    return body
  }


  private fun build32cReq(req: Charge32cReq): ByteBuf {
    val body = Unpooled.buffer(28 + 52 + 8 + 24 + 48 + 484 + 12)

    // 任务基本信息
    writeTaskBaseInfo(body, req)

    // 当前目标信息
    writeTargetInfo(body, req.targetPosition)

    // 最终目标信息
    writeFinalPosition(body, req.finalPosition)

    // 避障参数
    writeHazardAvoidance(body, req.ha)

    // 一段无效的字节
    body.writeBytes(reserved48)

    // 多段路径信息
    writePath(body, req.sourcePosition, req.targetPosition, req.paths)

    // 电池控制参数
    writeCharge(body, req.charge)

    logger.info("32c body size ${body.readableBytes()}")


    return body
  }

  //////////////////////////////////////////////////////////////////////////

  private fun buildFrame(
    msgType: Int, // 消息类型
    flowNo: Int, // 报文序号
    protoVersion: Int, // 协议版本号
    bodyBuf: ByteBuf
  ): CompositeByteBuf {
    val headBuf: ByteBuf = Unpooled.buffer(schema.headLength)
    headBuf.writeBytes(schema.start)
    headBuf.writeShort(bodyBuf.readableBytes() + 32) // 注意是整包大小
    headBuf.writeShort(msgType)
    headBuf.writeInt(flowNo)
    headBuf.writeByte(protoVersion) // 消息版本号 1
    headBuf.writeByte(0) // 消息加密类型 1
    headBuf.writeByte(0) // 协议内容类型 1
    headBuf.writeBytes(headReserved17)  // 保留字节 17

    val reqBuf: CompositeByteBuf = Unpooled.compositeBuffer()
    reqBuf.addComponent(true, headBuf)
    reqBuf.addComponent(true, bodyBuf)
    return reqBuf
  }

  // string -> fixed size byte array
  private fun toByteArray(str: String, size: Int): ByteArray {
    val goodsBa = str.toByteArray(StandardCharsets.US_ASCII)
    val ba = ByteArray(size)
    System.arraycopy(goodsBa, 0, ba, 0, goodsBa.size)
    return ba
  }

  private fun byteArrayToString(ba: ByteArray): String { // ba = [0x31, 0x30, 0x30, 0x30, 0x30, 0x31, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 ...]
    var i = ba.size - 1
    while (i >= 0) { // 去掉后面无效的 0x00
      if (ba[i].toInt() != 0x00) break
      i--
    }
    return String(ba.slice(0..i).toByteArray(), StandardCharsets.UTF_8)
  }

  // 任务基本信息
  private fun writeTaskBaseInfo(body: ByteBuf, req: BaseReq) {
    body.writeInt(req.robotNo) // 设备编号  4
    body.writeShort(req.taskId) // 任务ID   2
    body.writeByte(req.subTaskId) // 任务子ID  1
    body.writeByte(req.moveType) // 移动类型    1
    body.writeShort(req.taskType) // 大任务类型    1
    body.writeByte(0) // 动作同步类型 1
    body.writeByte(0) // 保留 1
    body.writeByte(0) // 是否播报语音 1
    body.writeByte(0) // 是否亮灯 1
    body.writeShort(0) // 反光板对接距离 2
    body.writeByte(0) // 货架类型 1
    body.writeByte(0) // 强制控制车体移动 1
    body.writeBytes(reserved10)
  }

  private fun writeSourcePosition(body: ByteBuf, position: SourcePosition) {
    body.writeInt(position.x)
    body.writeInt(position.y)
  }

  private fun writeTargetPosition(body: ByteBuf, position: TargetPosition) {
    body.writeInt(position.x)
    body.writeInt(position.y)
  }

  private fun writeFinalPosition(body: ByteBuf, position: FinalPosition) {
    body.writeInt(position.x)
    body.writeInt(position.y)
  }

  private fun writeTargetInfo(body: ByteBuf, target: TargetPosition) {
    writeTargetPosition(body, target)
    body.writeInt(target.direction) // 目标点方向
    body.writeByte(target.targetType) // 目标点类型
    body.writeByte(target.accuracyClass) // 精度等级
    body.writeShort(target.reachDist.toInt()) // 目标点距离精度
    body.writeInt(target.reachAngleV) // 目标点小车角度精度
    body.writeInt(target.reachAngleG) // 目标点货架角度精度
    body.writeInt(target.maxSpeed) // 最大速度
    body.writeInt(target.routeAngle) // 全向车路径角度
    body.writeBytes(reserved20)
  }

  // 写避障参数
  private fun writeHazardAvoidance(body: ByteBuf, ha: HazardAvoidance) {
    // 贝塞尔曲线
    body.writeShort(ha.distance) // 距离阈值
    body.writeByte(0) // 保留
    body.writeByte(ha.funIdx) // 功能索引
    body.writeShort(ha.safeDistF) // 前方安全距离
    body.writeShort(ha.safeDistB) // 后方安全距离
    body.writeShort(ha.safeDistL) // 左侧安全距离
    body.writeShort(ha.safeDistR) // 右侧安全距离
    body.writeBytes(reserved12)
  }

  // 写路径
  private fun writePath(body: ByteBuf, source: SourcePosition, target: TargetPosition, paths: List<Path>) {
    body.writeByte(paths.size)
    body.writeByte(0)
    body.writeShort(480)

    // 遍历路径
    // 路径类型 p.type  0 直线，1 贝塞尔曲线，3 矩形顶点
    for (p in paths) {
      body.writeShort(p.v) // 最大速度
      body.writeShort(p.proportion) // 目标路径比例
      body.writeByte(p.type) // 轨迹类型
      // 控制点数量
      when (p.type) {
        3 -> {
          body.writeByte(1)
        }

        else -> {
          body.writeByte(2 + p.controlPoints.size)
        }
      }
      body.writeByte(0) // 是否保持车身姿态
      body.writeByte(0) // 是否巡线
      body.writeByte(0) // 分叉点线条数量
      body.writeByte(0) // 此段路径选择的巡线编号
      body.writeBytes(reserved6) // 保留

      // 控制点
      when (p.type) {
        0 -> {
          // 直线
          writeSourcePosition(body, source)
          writeTargetPosition(body, target)
          // 对齐控制点，补全
          repeat(8) {
            body.writeBytes(reserved8)
          }
        }

        1 -> {
          // 贝塞尔曲线
          writeSourcePosition(body, source)
          for (cp in p.controlPoints) {
            body.writeInt(cp.x)
            body.writeInt(cp.y)
          }
          writeTargetPosition(body, target)
          // 对齐控制点，补全
          repeat(8 - p.controlPoints.size) {
            body.writeBytes(reserved8)
          }
        }

        3 -> {
          // 矩形顶点
          writeTargetPosition(body, target)
          // 对齐控制点，补全
          repeat(9) {
            body.writeBytes(reserved8)
          }
        }
      }
    }

    // 对齐路径，补全
    repeat(5 - paths.size) {
      body.writeBytes(reserved96)
    }
  }

  // 写路径
  private fun writePathV4(body: ByteBuf, source: SourcePosition, target: TargetPosition, paths: List<Path>) {
    body.writeByte(paths.size)
    body.writeByte(0)
    body.writeShort(480)

    // 遍历路径
    // 路径类型 p.type  0 直线，1 贝塞尔曲线，3 矩形顶点
    for (p in paths) {
      body.writeShort(p.v) // 最大速度
      body.writeShort(p.proportion) // 目标路径比例
      body.writeByte(p.type) // 轨迹类型
      body.writeByte(p.controlPoints.size) // 控制点数量
      body.writeByte(0) // 是否保持车身姿态
      body.writeByte(0) // 是否巡线
      body.writeByte(0) // 分叉点线条数量
      body.writeByte(0) // 此段路径选择的巡线编号
      body.writeBytes(reserved6) // 保留

      // 控制点
      for (cp in p.controlPoints) {
        body.writeInt(cp.x)
        body.writeInt(cp.y)
      }
      // 对齐控制点，补全
      repeat(10 - p.controlPoints.size) {
        body.writeBytes(reserved8)
      }
    }

    // 对齐路径，补全
    repeat(5 - paths.size) {
      body.writeBytes(reserved96)
    }
  }

  private fun writeGoods(body: ByteBuf, goods: Goods) {
    body.writeBytes(toByteArray(goods.id, 32))
    body.writeByte(goods.type) // 货架类型 1。测试时使用的值为 0x64
    body.writeByte(goods.loadAccuracy) // 举升精度 1。测试时使用的值为 0x32
    body.writeByte(goods.adjustType) // 调整类型 1
    body.writeByte(0) //   储位类型
    body.writeShort(goods.loadHeight) // 举升高度 2
    body.writeByte(0) // 货码识别 1
    body.writeByte(0) // 堆垛功能 1
    body.writeInt(goods.moveDirection) // 货架运行角度    4   999000
    body.writeInt(goods.targetDirection) // 货架目标角度    4   999000
    body.writeInt(goods.length) // 货架长度        4
    body.writeInt(goods.width) // 货架宽度        4
    body.writeShort(goods.weight) // 货架重量        2
    body.writeByte(goods.unloadAccuracy) // 放货精度 1。测试时使用的值为 0x32
    body.writeBytes(reserved7) // 保留            7
    body.writeBytes(reserved18) // 保留            18
  }

  private fun writeCharge(body: ByteBuf, chargeParams: ChargeParams) {
    body.writeShort(chargeParams.type) // 充电类型
    body.writeShort(chargeParams.time1) // 充电时间1
    body.writeShort(chargeParams.pileId) // 充电桩编号
    body.writeBytes(reserved2) // 保留  2
    body.writeInt(IpHelper.ipToInt(chargeParams.pileIp)) // 充电桩 ip
  }
}

data class HkFrame(
  val head: ByteBuf,
  val body: ByteBuf
)

//////////////////////////////////////////////////////////////////////////

data class SourcePosition(
  val x: Int, // 起点 X
  val y: Int, // 起点 Y
)

data class TargetPosition(
  val x: Int, // 目标点 X
  val y: Int, // 目标点 Y
  val direction: Int, // 目标点角度

  val targetType: Int = 0, // 目标点类型（即目标点属性，附录8） 0 非储位或充电位；1 储位点；2 充电点；3 电梯。
  val accuracyClass: Int = 0, // 精度等级。此次任务使用的精度等级方案。协议中无特殊说明，仅出现了取值为 0 的情况。

  val reachDist: Short = 0, // 到点距离精度。有点用
  val reachAngleV: Int = 0, // 小车到点角度精度。没啥用
  val reachAngleG: Int = 0, // 货架到点角度精度。没啥用
  val maxSpeed: Int = 600, // 最大速度，这里的速度没啥用，Path 里的是最终生效的
  val routeAngle: Int = 0, // 全向车路径角度
)

data class FinalPosition(
  val x: Int, // 最终目标点 X
  val y: Int, // 最终目标点 Y
)

data class HazardAvoidance(
  val distance: Int = 0, // 距离阈值
  val funIdx: Int = 0, // 功能索引
  val safeDistF: Int = 0, // 前方安全距离
  val safeDistB: Int = 0, // 后方安全距离
  val safeDistL: Int = 0, // 左侧安全距离
  val safeDistR: Int = 0, // 右侧安全距离
)

data class Path(
  val type: Int, // 轨迹类型  0 直线；1 贝塞尔曲线；
  val proportion: Int = 1000, //  目标路径比例
  val v: Int = 1500, // 速度，实际生效的速度
  // 是否保持车身姿态
  // 是否巡线
  val controlPoints: List<ControlPoint> = emptyList() // 中间点 list
)

data class ControlPoint(
  val x: Int,
  val y: Int,
)

data class Goods(
  val id: String = "", // 货架号，最长 32 字节
  val type: Int = 100, // 货架类型，附录33。
  val loadAccuracy: Int = 50, // 举货架精度，0~255mm。
  val adjustType: Int = 0, // 调整类型（包括举之前和举着货架移动过程中的）：0 不限制；1 设备与货架可以小角度相关旋转，转盘不能转；2 设备与货架不可以相对旋转，转盘不可以转；3 设备与货架可以相对旋转，转盘不能转；4 设备与货架不可以相对旋转，货架可以转；5 设备与货架可以小角度相对旋转，转盘可以转
  val loadHeight: Int, // 举升高度，是绝对高度，不是相对高度。0~65535mm。
  val moveDirection: Int = 999000, // 货架移动角度，999000 表示不关注
  val targetDirection: Int = 999000, // 货架目标角度，999000 表示不关注
  val length: Int, // 货架长度
  val width: Int, // 货架宽度
  val weight: Int, // 货架重量
  val unloadAccuracy: Int, // 放货精度，0~255mm。
)

data class ChargeParams(
  val type: Int, // 电池控制类型 01 充电
  val time1: Int = 60, // 时间参数1：
  // 0 立即关机；
  // 1-60 延时1-60s后重启（1:1s）；
  // 60-204 延时10-1440min后重启（1:10min）；
  // 205-255 延时1h-51h后重启（1:1h）
  val pileId: Int, // 无线充电桩编号
  val pileIp: String = "0.0.0.0", // 无线充电桩 IP
  // val chargeTime2 : Int, // 时间参数2：无符号 uint32_t
  // 0 立即关机；
  // 1-15*24*3600 延时1-15*24*3600s后重启（1:1s）；
)

//////////////////////////////////////////////////////////////////////////

open class BaseReq(
  open val robotId: String,
  open val robotNo: Int,
  open val taskId: Int, // 任务ID
  open val subTaskId: Int, // 任务子ID
  open val taskType: Int = 0, // 大任务类型 0 通用；1 充电；2 切换地图；3 自检；

  open val moveType: Int, // 移动类型  0 （复杂路径）前进；1 （复杂路径）后退；2（直线路径）轴向直线前进；3（直线路径）轴向直线后退；4 （直线路径）斜线前进；5（直线路径）斜线后退；6（复杂路径）（设备自行计算旋转方向）旋转；7 （复杂路径）顺时针旋转；8（复杂路径）逆时针旋转；9 原地静止
  open val ha: HazardAvoidance, // 避障参数
)

// 移动
data class Move322Req(
  override val robotId: String = "",
  override var robotNo: Int,
  val taskIdU: String = "", // Seer 任务 ID
  val subTaskIdU: String = "", // Seer 任务子 ID
  override val taskId: Int, // 任务ID
  override val subTaskId: Int, // 任务子ID
  override val taskType: Int = 0, // 大任务类型 0 通用；1 充电；2 切换地图；3 自检；

  override val moveType: Int, // 移动类型  0 （复杂路径）前进；1 （复杂路径）后退；2（直线路径）轴向直线前进；3（直线路径）轴向直线后退；4 （直线路径）斜线前进；5（直线路径）斜线后退；6（复杂路径）（设备自行计算旋转方向）旋转；7 （复杂路径）顺时针旋转；8（复杂路径）逆时针旋转；9 原地静止

  override val ha: HazardAvoidance = HazardAvoidance(),

  val sourcePosition: SourcePosition, // 起点
  val targetPosition: TargetPosition, // 终点
  val finalPosition: FinalPosition, // 最终目标点

  val paths: List<Path>, // 多段路径参数
) : BaseReq(robotId, robotNo, taskId, subTaskId, taskType, moveType, ha)

// 载货移动
data class LoadMove324Req(
  override val robotId: String = "",
  override var robotNo: Int,
  override val taskId: Int, // 任务ID
  override val subTaskId: Int, // 任务子ID
  override val taskType: Int, // 大任务类型

  override val moveType: Int, // 移动类型

  override val ha: HazardAvoidance = HazardAvoidance(),

  val sourcePosition: SourcePosition, // 起点
  val targetPosition: TargetPosition, // 终点
  val finalPosition: FinalPosition, // 最终目标点

  val paths: List<Path>, // 多段路径参数

  val goods: Goods, // 货物信息
) : BaseReq(robotId, robotNo, taskId, subTaskId, taskType, moveType, ha)

// 卸货移动
data class Unload326Req(
  override val robotId: String = "",
  override val robotNo: Int,
  override val taskId: Int, // 任务ID
  override val subTaskId: Int, // 任务子ID
  override val taskType: Int, // 大任务类型
  override val moveType: Int, // 移动类型

  override val ha: HazardAvoidance = HazardAvoidance(),

  val sourcePosition: SourcePosition, // 起点
  val targetPosition: TargetPosition, // 终点
  val finalPosition: FinalPosition, // 最终目标点

  val paths: List<Path>, // 多段路径参数
  val goods: Goods, // 货物信息
) : BaseReq(robotId, robotNo, taskId, subTaskId, taskType, moveType, ha)

// 原地取货
data class Load312Req(
  override val robotId: String = "",
  override var robotNo: Int,
  override val taskId: Int, // 任务ID
  override val subTaskId: Int, // 任务子ID
  override val taskType: Int, // 大任务类型

  override val moveType: Int, // 移动类型

  override val ha: HazardAvoidance = HazardAvoidance(),

  val sourcePosition: SourcePosition, // 起点
  val targetPosition: TargetPosition, // 终点
  val finalPosition: FinalPosition, // 最终目标点

  val goods: Goods, // 货物信息
) : BaseReq(robotId, robotNo, taskId, subTaskId, taskType, moveType, ha)

// 原地放货
data class Unload314Req(
  override val robotId: String = "",
  override var robotNo: Int,
  override val taskId: Int, // 任务ID
  override val subTaskId: Int, // 任务子ID
  override val taskType: Int, // 大任务类型

  override val moveType: Int, // 移动类型

  override val ha: HazardAvoidance = HazardAvoidance(),

  val sourcePosition: SourcePosition, // 起点
  val targetPosition: TargetPosition, // 终点
  val finalPosition: FinalPosition, // 最终目标点

  val goods: Goods, // 货物信息
) : BaseReq(robotId, robotNo, taskId, subTaskId, taskType, moveType, ha)

// 充电
data class Charge32cReq(
  override val robotId: String = "",
  override var robotNo: Int,
  override val taskId: Int, // 任务ID
  override val subTaskId: Int, // 任务子ID
  override val taskType: Int, // 大任务类型

  override val moveType: Int, // 移动类型

  override val ha: HazardAvoidance = HazardAvoidance(),

  val sourcePosition: SourcePosition, // 起点
  val targetPosition: TargetPosition, // 终点
  val finalPosition: FinalPosition, // 最终目标点

  val paths: List<Path>, // 多段路径参数

  val charge: ChargeParams, // 充电参数
) : BaseReq(robotId, robotNo, taskId, subTaskId, taskType, moveType, ha)

// 暂停任务
data class Pause900Req(
  val robotId: String = "",
  var robotNo: Int,
  val taskId: Int, // 任务ID
  val subTaskId: Int, // 任务子ID
)

// 继续任务
data class Resume902Req(
  val robotId: String = "",
  var robotNo: Int,
  val taskId: Int, // 任务ID
  val subTaskId: Int, // 任务子ID
)

// 取消任务
data class Cancel904Req(
  val robotId: String = "",
  var robotNo: Int,
  val taskId: Int, // 任务ID
  val subTaskId: Int, // 任务子ID
)

data class ChangeSlamSettingReq(
  val robotId: String = "",
  var robotNo: Int,
  val op: Int // 0 关闭上报，1 开启上报
)

data class LockRobotReq(
  val robotId: String = "", // 机器人 ID
  val nickname: String = "" // 申请方
)

data class UnlockRobotReq(
  val robotId: String = "", // 机器人 ID
  val nickname: String = "" // 申请方
)

data class GetOwnerReq(
  val robotIds: List<String> = emptyList() // 机器人 ID list
)

//////////////////////////////////////////////////////////////////////////

data class HikRobot(
  val robotNo: Int,
  val reported: Boolean = false,
  val flowNo: Int = 0,
  val cmdTaskId: Int = 0,
  val cmdSubTaskId: Int = 0,
  val tasking: Boolean = false,
  val reportTaskId: Int = 0,
  val reportSubTaskId: Int = 0,
  var status: Int = -1, // 包含阻挡状态，-2 未初始化，-1 长时间未上报，置为失联，
  // 包括（0x41-前方遇障；0x42-后方遇障；0x43-左侧遇障；0x44右侧遇障；0x45-弧线移动时前方遇障；0x46-弧线移动时后方遇障；0x47-弧线移动时左侧遇障；0x48-弧线移动时右侧遇障。
  var statusHex: String = "0xffffffff", // 16 进制状态
  var statusStr: String = "机器人未建立过连接",
  val lastReportOn: Date = Date(),

  val config: HikCfg = HikCfg(),
  val basic: HikBasic = HikBasic(),
  val feature: HikFeature = HikFeature(),
  var report: HikReport = HikReport(),
  var laser: HikLaser = HikLaser(),

  var ctx: ChannelHandlerContext? = null,
  var tasks: MutableList<BaseReq> = mutableListOf(),

  var locked: Boolean = false, // 当前控制权是否被抢占 true-机器人的控制权归 GW；false-机器人的控制权被释放了。
  var nickName: String? = null // 控制权所有者昵称信息
)

data class HikCfg(
  var robotId: String = "",
  val vendor: RobotVendor = RobotVendor.Hik,
  var disabled: Boolean = false,
  var online: Boolean = false,
  // val robotNo: Int,
  var ip: String = "",
  var port: Int = 0,
  var laserReport: Boolean = false,
)

data class HikBasic(
  val length: Int = 0, // 设备长度 mm
  val width: Int = 0, // 设备宽度 mm
  val height: Int = 0, // 设备高度 mm
  val weight: Int = 0, // 设备重量 kg
  val rotateDiameter: Int = 0, // 设备旋转直径 mm
)

data class HikFeature(
  // 直线
  val speed: Int = 0,
  val acc: Int = 0,
  val dece: Int = 0,
  // 角
  val wSpeed: Int = 0,
  val wAcc: Int = 0,
  val wDece: Int = 0,
  // 弧线
  val bSpeed: Int = 0,
  val bAcc: Int = 0,
  val bDece: Int = 0
)

data class HikReport(
  val status: Int = -1, // 设备状态
  val statusHex: String = "0xffffffff", // 设备状态
  val statusStr: String = "机器人未建立过连接", // 设备状态
  val x: Int = 0, // X 坐标
  val y: Int = 0, // Y 坐标
  val direction: Int = 0, // 角度
  val speed: Int = 0, // 速度
  val batteryAmm: Int = 0, // 电池电流 1/100A
  val batteryVol: Int = 0, // 电池电压 1/100V
  val batteryLevel: Int = 0, // 电池电量 1-100
  val confidence: Int = 0, // 置信度 0-100。 数据来源：机器人通过 0x214（SLAM 定位信息上报） 指令上报自身的置信度，数值越大匹配程度越低。
  val guidMode: Int = 0, // 导航类型 0 未知；1 二维码；2 SLAM
  val taskId: Int = 0, // 任务号
  val subTaskId: Int = 0, // 子任务号
  val speedDece: Int = 0, // 线减速度，mm/(s*s)
  val rotateDec: Int = 0, // 设备旋转角减速度，deg/(s*s)
  val actuatorIdx: Int = 0, // 执行机构索引；0-无附加信息；1-滚筒设备；2,5,6-预留，无附加信息；3-探测到的货架信息；4-货物信息；7-叉车信息。
  val actuatorInfo: Any? = null, // 执行机构信息：ShelfInfoDetected（actuatorIdx=3）| ShelfInfo（actuatorIdx=4）；
)

data class ShelfInfoDetected(
  val shelfId: String = "unknown",  // 32 bytes，
  val direction: Int = 0,           // 4 bytes，探测到的货物角度，即货码到 X 正方向的地码坐标系的角度，范围(-180, 180](1/1000deg)
  val diffX: Short = 0,             // 2 bytes，货码相对于货架中心点的 X 方向偏差，单位 mm
  val diffY: Short = 0,             // 2 bytes，货码相对于货架中心点的 Y 方向偏差，单位 mm
  // 4 bytes，保留。
  val diffDirection: Int = 0        // 4 bytes，货码相对于货架中心点的角度偏差，单位 1/1000 度。
)

data class ShelfInfo(
  val shelfId: String = "unknown", // 32 bytes；表示探测到的货架的字符串 ID，即货码上的字符串。
  val posX: Int = 0, // 4 bytes，货物位置的 X 方向坐标，单位 mm。
  val posY: Int = 0, // 4 bytes，货物位置的 X 方向坐标，单位 mm。
  val direction: Int = 0, // 4 bytes，探测到的货物角度，即货码到 X 正方向的地码坐标系的角度，范围(-180, 180](1/1000deg)
  val status: Int = 0, // 1 byte，举升机构的状态。当 actuatorIdx=4 时，此值有效。0-未知状态；1-上升完成，再在顶部；2-下降完成，在最底部；3-中间状态强制停止；4处理过程中；5-异常，顶部脱离；6-异常，底部脱离；7-异常，举升时上传感器超时未检测到；8-异常，下放时下传感器超时未检测到；9-异常，上下传感器都为检测到；10-转盘归零过程；11-转盘归零过程出错。
)

data class HikLaser(
  val sortType: Int = 0,
  val pointNum: Int = 0,
  val posX: Int = 0,
  val posY: Int = 0,
  val posTheta: Int = 0,
  val details: List<Int> = emptyList()
)


//////////////////////////////////////////////
data class MoveRobotReq(
  val robotId: String,
  val target: TargetPosition,
  val targetType: Int,
)
