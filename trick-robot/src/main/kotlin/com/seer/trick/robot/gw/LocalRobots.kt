package com.seer.trick.robot.gw

import com.seer.trick.BzError

import com.seer.trick.robot.vendor.seer.MockSeerRobotService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap

object LocalRobots {

  private val logger: Logger = LoggerFactory.getLogger(this::class.java)

  private val robots: MutableMap<String, LocalRobot> = ConcurrentHashMap()

  // 并发的初始化本地 RBK 连接。不等待连接完成（可能会耗时）。
  fun init(config: GwConfig) {
    

    logger.info("网关配置机器人数量：{}", config.localConfigs.size)
    if (config.localConfigs.isEmpty()) return

    for (rc in config.localConfigs) {
      if (rc.disabled || rc.mock) continue
      val robot = LocalRobot(rc.name, rc)
      robot.init()
      robots[rc.name] = robot
    }
  }

  @Synchronized
  fun dispose() {
    for (client in robots.values) {
      client.dispose()
    }
    robots.clear()
  }

  fun requestRbk(req: GwRbkRequest): String {
    val config = GwCenter.gwConfig.localConfigs.find { it.name == req.robotId && !it.disabled }
      ?: throw BzError("errNoLocalRobot", req.robotId)
    if (config.mock) {
      return MockSeerRobotService.requestRbk(req)
    } else {
      val robot = robots[req.robotId] ?: throw BzError("errNoLocalRobot", req.robotId)
      return robot.requestRbk(req)
    }
  }
}