package com.seer.trick.robot.falcon.bp.ndc


import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.IdGenRule
import com.seer.trick.base.entity.id.IdGenManager
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockOutputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.robot.vendor.ndc.NdcOrderState

class CreateNdcOrderBp : AbstractBp() {
  override fun process() {
    val priority = mustGetBlockInputParamAsLong("priority")
    val startBin = mustGetBlockInputParamAsLong("startBin")
    val endBin = mustGetBlockInputParamAsLong("endBin")

    val orderId: String = getOrSetBlockInternalVariable("ndcOrderId") {
      val orderId = IdGenManager.generateId(idGenRule)
      logger.info("产生 NDC 运单号：$orderId")
      val falconId = taskRuntime.taskId

      // 创建 NDC 任务单
      val ev: EntityValue =
        mutableMapOf(
          "id" to orderId,
          "falconId" to falconId,
          "priority" to priority,
          "startBin" to startBin,
          "endBin" to endBin,
          "status" to NdcOrderState.Created,
        )
      EntityRwService.createOne("NdcOrder", ev, null)

      logger.info("NDC 运单已生成")
      // TODO 记录关联资源
      orderId
    } as String

    // TODO 等待 2 次 b 消息返回，返回了才说明创建任务成功了

    // TODO 取出来 index、iKey

    setBlockOutputParams(mapOf("orderId" to orderId))

    // 添加猎鹰任务相关业务对象
    addRelatedObject("NdcOrder", orderId, null)
  }

  companion object {
    private val idGenRule = IdGenRule(true, "NDC-", 6)

    val def = BlockDef(
      CreateNdcOrderBp::class.simpleName!!,
      color = "#F2EE9D",
      inputParams = listOf(
        BlockInputParamDef("priority", BlockParamType.Long, true),
        BlockInputParamDef("startBin", BlockParamType.Long, true),
        BlockInputParamDef("endBin", BlockParamType.Long, true),
      ),
      outputParams = listOf(
        BlockOutputParamDef("orderId", BlockParamType.String),
      ),
    )
  }
}