package com.seer.trick.robot.handler

import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.noAuth
import io.javalin.http.Context
import org.slf4j.LoggerFactory

object NdcHandler {

  private val logger = LoggerFactory.getLogger(this::class.java)

  fun registerHandlers() {
    val c = Handlers("api/test/ndc")
    c.post("find-part", NdcHandler::findPart, noAuth())
  }

  private fun findPart(ctx: Context) {
    // val req = ctx.getReqBody<>()
    ctx.json({})
  }


}