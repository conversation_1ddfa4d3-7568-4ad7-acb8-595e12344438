package com.seer.trick.robot.light

import com.seer.trick.BzError

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.soc.SocService
import com.seer.trick.robot.rachel.RaRachelManager
import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.max

/**
 * 寻路，目前只用于光通讯
 */
class LightScheduler(private val rachel: RaRachelManager) {
  
  private val logger = LoggerFactory.getLogger(this::class.java)
  
  val pendingSiteIds: MutableMap<String, List<String>> = ConcurrentHashMap()
  
  /**
   * 先规划出一条途径各个 steps 的路径
   */
  fun buildMoves(robotId: String, steps: List<EntityValue>): List<EntityValue> {
    val rr = rachel.robots[robotId] ?: throw BzError("errNoRobot", robotId)
    val main = rr.selfReport?.main ?: throw BzError("errRobotOffline", robotId)
    val startSite = main.currentSite
    if (startSite.isNullOrBlank()) throw BzError("errRobotNoCurrentStation", robotId)
    return fillMiddleLandmarks(robotId, startSite, steps)
  }
  
  /**
   * 将粗步骤，如：从当前点到 A，再到 B 取货，再到 C 放货，再到 D
   * 寻路，填充中间点
   * 锁定资源
   */
  private fun fillMiddleLandmarks(
    robotId: String, startSite: String, steps: List<EntityValue>
  ): MutableList<EntityValue> {
    logger.debug("寻找中间点，机器人=$robotId，起点=$startSite，步骤=${steps}")
    val siteIds: List<String> = steps.mapIndexed { index, it ->
      val siteIdOrBinId = it["id"] as String? // 可能是 Bin 也可能是站点
      if (siteIdOrBinId.isNullOrBlank()) throw BzError("errBzError", "Missing 'id' param in step [$index]: $it")
      val si = rachel.sceneRuntime.map.sceneMapRuntime.getIndexBySiteIdOrBinId(siteIdOrBinId)
        ?: throw BzError("errBzError", "No point/bin in fleet map: $siteIdOrBinId")
      si.site.id
    }
    
    rachel.scheduleService.checkConnectivity(startSite, siteIds)
    
    val paths = awaitFindPath(robotId, startSite, siteIds)
    
    val moves: MutableList<EntityValue> = ArrayList()
    var fromSite = startSite
    for ((si, toSiteId) in siteIds.withIndex()) {
      if (fromSite != toSiteId) {
        val sites = paths[si].map { rachel.sceneRuntime.map.sceneMapRuntime.siteIdToIndexMap[it]!!.site }
        // 去掉最后点，只加中间点
        for (i in 1 until sites.size - 1) {
          fromSite = sites[i - 1].id
          moves += mutableMapOf("id" to sites[i].id, "source_id" to fromSite)
        }
        fromSite = sites[max(sites.size - 2, 0)].id // 倒数第二个点
      }
      // 最后一个指令，如果需要取放货
      val step = steps[si]
      step["source_id"] = fromSite
      moves += step
      fromSite = toSiteId
    }
    
    return moves
  }
  
  private fun awaitFindPath(
    robotId: String, startSite: String, siteIds: List<String>
  ): List<List<String>> {
    while (!Thread.interrupted()) {
      pendingSiteIds[robotId] = siteIds
      val paths = rachel.scheduleService.plan(robotId, startSite, siteIds)
      if (paths != null) {
        SocService.removeNode("AwaitPathFind:$robotId")
        pendingSiteIds.remove(robotId)
        return paths
      } else {
        SocService.updateNode(
          "机器人", "AwaitPathFind:$robotId", "机器人寻路:$robotId", "找不到可用路径，起点=$startSite，中间点=$siteIds"
        )
      }
      Thread.sleep(1000)
    }
    throw InterruptedException()
  }
  
  fun reset() {
    pendingSiteIds.clear()
    for (rr in rachel.robots.values) SocService.removeNode("AwaitPathFind:${rr.id}")
  }
  
}