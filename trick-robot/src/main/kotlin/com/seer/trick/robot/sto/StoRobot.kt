package com.seer.trick.robot.sto

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError

import com.seer.trick.base.concurrent.BaseConcurrentCenter.getAdhocLoopThead
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.soc.SocService
import com.seer.trick.base.soc.SysMonitorService
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.robot.RobotAppManager
import com.seer.trick.robot.rachel.RobotVendor
import com.seer.trick.robot.vendor.hai.HaiAdapter
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.Executors
import java.util.concurrent.Future

class StoRobot(val robotName: String) {

  private val logger = LoggerFactory.getLogger(this::class.java)

  private val orderExecutor = Executors.newSingleThreadExecutor()

  @Volatile
  private var executeFuture: Future<*>? = null

  @Volatile
  var currentOrder: StOrder? = null
    private set

  @Volatile
  var lastOrder: StOrder? = null
    private set

  fun init() {
    recover()
    executeFuture = getAdhocLoopThead("StoRobot: $robotName") { executeStoLoop() }
    val order = currentOrder
    if (order != null) {
      updateSoc("当前运单：${order.id}/${order.currentMove} ${order.status}")
    } else {
      updateSoc("当前运单：无")
    }
  }

  private fun recover() {
    try {
      // 先清空再重新加载到队列中
      StoQueue.clear(robotName)
      val stOrders = StoStore.loadPendingOrders(robotName)
      for (order in stOrders) {
        StoQueue.enqueue(order)
      }
      val current = if (stOrders.isNotEmpty()) stOrders[0] else null
      logger.info("机器人 $robotName 加载到当前运单=${current?.id} 状态=${current?.status}")

      lastOrder = StoStore.loadLastOrder(robotName)
      logger.info("机器人 $robotName 加载到上一运单=${lastOrder?.id} 状态=${lastOrder?.status}")
    } catch (e: Exception) {
      logger.error("恢复运单失败", e)
    }
  }

  fun dispose() {
    SocService.removeNode("Sto:Execute:$robotName")
    executeFuture?.cancel(true)
  }

  private fun updateSoc(msg: String) {
    SysMonitorService.log(
      subject = "GW",
      target = robotName,
      field = "Sto::Execute",
      value = "SimpleTransportOrder update status msg=$msg",
    )

    SocService.updateNode("GW", "Sto:Execute:$robotName", "单车简单运单：$robotName", msg)
  }

  @Synchronized
  fun start(req: CreateStoReq) {
    if (req.vendor == RobotVendor.Hik) throw throw BzError("errStoHikNotSupported")

    if (StoManager.stoRobotMap.keys.find { it == req.robotName } == null) {
      throw throw BzError("errStoNoRobot", req.id, req.robotName)
    }
    if (StoStore.existsById(req.id)) throw BzError("errCreateStoOrderExists", req.id)

    logger.info("创建新单。机器人=$robotName，上一单=${lastOrder?.id}。新单=$req")
    var order = StOrder(
      id = req.id,
      vendor = req.vendor,
      robotName = req.robotName,
      seer3066 = req.seer3066,
      moves = req.moves,
    )

    // 填充 source_id
    if (req.seer3066) {
      if (order.moves[0]["source_id"] == null) {
        val currentStation = getCurrentStation()
        if (currentStation.isNullOrBlank()) throw BzError("errRobotNoCurrentStation", order.robotName)

        order.moves[0]["source_id"] = currentStation
      }
      for (i in 0 until order.moves.size) {
        val move = order.moves[i]
        if (i > 0) move["source_id"] = order.moves[i - 1]["id"]
      }
    } else {
      if (order.moves[0]["source_id"] == null) {
        // 获取当前位置作为起始点，保证导航界面有起始点
        val currentStation = getCurrentStation()
        if (!currentStation.isNullOrBlank()) order.moves[0]["source_id"] = currentStation
      }
    }
    val id = StoStore.create(order)
    if (order.id.isBlank()) {
      order = StOrder(
        id = id,
        vendor = order.vendor,
        robotName = order.robotName,
        seer3066 = order.seer3066,
        moves = order.moves,
      )
    }
    // 加入队列中等待
    StoQueue.enqueue(order)
  }

  private fun executeOrder(order: StOrder) {
    if (order.vendor == RobotVendor.Seer) {
      // 初次设置、重新设置任务 id
      for (i in 0 until order.moves.size) {
        val move = order.moves[i]
        move["task_id"] = IdHelper.oidStr()
      }
      StoStore.updateOrder(order)
    }

    if (order.seer3066) {
      orderExecutor.submit { executeWrap(order) { doExecuteSeer3066(order) } }
    } else {
      orderExecutor.submit { executeWrap(order) { doExecuteOtherOrder(order) } }
    }
  }

  private fun doExecuteSeer3066(order: StOrder): Boolean {
    val orderId = order.id

    // 获取当前位置，是站点名
    val currentStation = getCurrentStation()
    if (currentStation.isNullOrBlank()) throw BzError("errRobotNoPosition", order.robotName)

    // 待执行的步骤
    val moves = order.moves.subList(order.currentMove, order.moves.size).toMutableList()
    val firstMove = moves[0]
    val fromPoint = firstMove["source_id"] as String?
    if (fromPoint.isNullOrBlank()) throw BzError("errRobotNavNoSourceId")
    val toPoint = firstMove["id"] as String?
    if (toPoint.isNullOrBlank()) throw BzError("errRobotNavNoId")

    val sr = RobotAppManager.mustGetFirstSingleScene()
    val scene = sr.map.sceneMapRuntime

    // 将可能得库位名转化为站点
    val fromSite = if (fromPoint != "SELF_POSITION") {
      val si = scene.getIndexBySiteIdOrBinId(fromPoint)
        ?: throw BzError("errNoSuchBinOrLocation", fromPoint)
      si.site.id
    } else {
      "SELF_POSITION"
    }
    val toSite = if (toPoint != "SELF_POSITION") {
      val si = scene.getIndexBySiteIdOrBinId(toPoint)
        ?: throw BzError("errNoSuchBinOrLocation", toPoint)
      si.site.id
    } else {
      "SELF_POSITION"
    }

    logger.info(
      "准备发送 3066, ${order.id}/${order.robotName}，从第 ${order.currentMove + 1} 步。" +
        "机器人当前位置：$currentStation，当前第一步起点：$fromPoint ($fromSite)，当前第一步终点：$toPoint ($toSite)",
    )

    // 矫正起点终点
    if (currentStation == fromSite) {
      // 正常情况下，机器人当前位置就应该是下一步的起点
    } else if (currentStation == toSite) {
      // 但如果已经到了下一步终点了（比如因为故障恢复），则把下一步的起点改为当前点。
      logger.info("将机器人 ${order.robotName} 第一步改为当前点：$currentStation")
      val newFirstMove = HashMap(firstMove)
      newFirstMove["source_id"] = currentStation
      // 终点（"id"）可能是站点、库位、或者 SELF_POSITION，机器人可能要在终点执行动作，保留原始数据，在即将下发指令前再做更细致的处理。
      // newFirstMove["id"] = currentStation
      moves[0] = newFirstMove
    } else if (fromSite == "SELF_POSITION" && toPoint == "SELF_POSITION") {
      // 允许 source_id 和 id 同时为 SELF_POSITION；认为机器人在当前点
    } else {
      // 如果不在下一步的起点或终点，报错
      throw BzError("errMoveRobotFromToStation", order.robotName, currentStation, fromPoint, toPoint)
    }

    // 因为目前 3066 一个指令中间不能有动作点，所以，以动作点为结束，将一个大的步骤列表切分为多个 3066 指令
    val moveLists = StoSeer.split3066MoveList(moves)

    // 逐个 3066 执行下发、查询
    for (moveList in moveLists) {
      // 防备过程中被取消
      if (order.status == StOrderStatus.Cancelled) {
        logger.info("单车运单 $robotName，运单已取消：$orderId:${order.currentMove + 1}")
        updateSoc("单号=$orderId，等待第 ${order.currentMove + 1} 步完成，但已取消")
        return false
      }

      logger.info("单车运单 $robotName，执行运单步骤：$orderId:${order.currentMove + 1}，$moveList")
      updateSoc("单号=$orderId，执行步骤 ${order.currentMove + 1} : $moveList")

      // 修正 SELF_POSITION，解决 RBK 的报错：“Error [52702] ["Path planning failed. Cannot find Path between:{1},{2}",3,3]”
      for (move in moveList) { // moveSample = {"id":"", "source_id":"", "task_id":"", "binTask":""}

        val siteIdOrBinId = move["id"] as String // 站点名称或库位名称
        val checkedId = scene.getIndexBySiteIdOrBinId(siteIdOrBinId)?.site?.id
          ?: throw BzError("errNoSuchBinOrLocation", siteIdOrBinId)

        val sourceIdOrBinId = move["source_id"] as String // source_id 的站点名称或者库位名称
        val checkedSourceId = scene.getIndexBySiteIdOrBinId(sourceIdOrBinId)?.site?.id

        val moveTask = HashMap<String, Any>(move) // moveTask 为待下发的导航任务
        listOf("moveTask", "bizAddition").forEach { moveTask.remove(it) } // 移除指令中的非原始数据，否则故障重试后，会有很多嵌套的冗余数据。

        val binTask = move["binTask"] as String?
        val operation = move["operation"] as String?
        if (!binTask.isNullOrBlank()) {
          moveTask["binTask"] = binTask
          // 如果当前 move 有 binTask，则还需要写入参数 dispatcherArgs
          moveTask["dispatcherArgs"] = mapOf(
            "blockId" to (move["task_id"] as String) + "-" + order.currentMove,
            "orgLoc" to siteIdOrBinId,
            "startP" to 1.0,
            "endP" to 1.0,
          )
        } else if (!operation.isNullOrBlank()) {
          // binTask 和 operation 不应该同时出现在导航指令中。
          moveTask["operation"] = operation
        }

        // 机器人即将原地执行导航任务，需要修正必要的参数。
        if (checkedId == checkedSourceId) {
          // 如果机器人已经在 move 的 id 了，就将 move 的 id 改为 SELF_POSITION
          moveTask["source_id"] = "SELF_POSITION"
          moveTask["id"] = "SELF_POSITION"
          moveTask["percentage"] = 1.0

          // 如果当前 move 有 binTask，则还需要写入参数 dispatcherArgs
          if (!binTask.isNullOrBlank()) {
            moveTask["dispatcherArgs"] = mapOf(
              "blockId" to (move["task_id"] as String) + "-" + order.currentMove,
              "orgLoc" to siteIdOrBinId,
              "startP" to 1.0,
              "endP" to 1.0,
            )
          }
        }
        move["moveTask"] = moveTask
      }

      // 每次下发 moveList 之前，都抢一次控制权，以免下发失败。
      StoSeer.tryToRequestControl(order)
      // 发送当前一批 moveList
      StoSeer.start3066Move(order, moveList)

      // 挨个查 moveList 中每个 move 是否完成
      for (move in moveList) {
        // 防备过程中被取消
        if (order.status == StOrderStatus.Cancelled) {
          logger.info("单车运单 $robotName，运单已取消：$orderId:${order.currentMove + 1}")
          updateSoc("单号=$orderId，等待第 ${order.currentMove + 1} 步完成，但已取消")
          return false
        }

        // 查 move 完成
        StoSeer.await3066Move(order, move)

        logger.info("单车运单 $robotName，运单步骤执行完成：$orderId:${order.currentMove + 1}，$move")
        updateSoc("单号=$orderId，运单步骤执行完成：$orderId:${order.currentMove + 1}，$move")

        // 修改运单的当前步骤标记
        ++order.currentMove
        StoStore.updateCurrentMove(orderId, order.currentMove)
      }
    }

    return true
  }

  private fun doExecuteOtherOrder(order: StOrder): Boolean {
    val orderId = order.id
    for (i in order.currentMove until order.moves.size) {
      if (order.status == StOrderStatus.Cancelled) {
        logger.info("单车运单 $robotName，运单已取消：$orderId:${i + 1}，执行前")
        updateSoc("单号=$orderId，执行第 ${i + 1} 步，执行前，已取消")
        return false
      }

      logger.info("单车运单 $robotName，执行运单步骤：$orderId:${i + 1}")
      updateSoc("单号=$orderId，执行第 ${i + 1} 步，开始")

      order.currentMove = i
      StoStore.updateCurrentMove(orderId, i)

      if (order.vendor == RobotVendor.Seer) {
        // 如果机器人就在当前 move 的 id 上，并且当前 move 没有 binTask 或者 operation，则不下发当前 move 给机器人
        val move = order.moves[i]
        val toPoint = move["id"] as String?
          ?: throw BzError("errMove3051", "move 缺少 id, move=$move")
        val singleScene = RobotAppManager.mustGetFirstSingleScene().map.sceneMapRuntime
        val toSite = singleScene.getIndexBySiteIdOrBinId(toPoint)?.site?.id
        if (toSite.isNullOrBlank()) throw BzError("errNoSuchBinOrLocation", toPoint)

        val currentStation = getCurrentStation()
        if (currentStation.isNullOrBlank()) {
          throw BzError("errRobotNoPosition", "机器人不在点位上，请将其移动到任意点位上")
        }

        val binTask = move["binTask"] as String?
        val operation = move["operation"] as String?
        val moveTask = HashMap<String, Any>(move) // moveTask 为待下发的导航任务
        listOf("moveTask", "bizAddition").forEach { moveTask.remove(it) } // 移除指令中的非原始数据。

        // 机器人原地执行导航任务时，需要做一些特殊处理。
        if (currentStation == toSite) {
          if (binTask.isNullOrBlank() && operation.isNullOrBlank()) {
            // 机器人在当前位置不需要做任何操作，直接跳过当前 move，即使发了，rbk 也不会创建导航任务。
            move["bizAddition"] = mapOf("skipped" to true)
            logger.info("单车运单 $robotName，跳过运单步骤 $orderId:${i + 1}，机器人已在目标点，且不执行动作，$move")
            updateSoc("单号=$orderId，执行第 ${i + 1} 步，跳过")
            continue // 继续处理下一个 move
          }

          // 确定要下发当前 move，将待下发的导航指令放到 moveTask，保留原始数据。
          moveTask["id"] = "SELF_POSITION"
          moveTask["source_id"] = "SELF_POSITION"
        } else {
          // 不是原地导航任务，更新一下 moveTask 的 source_id
          moveTask["source_id"] = currentStation
        }
        move["moveTask"] = moveTask

        StoSeer.move3051(order, i)
      } else if (order.vendor == RobotVendor.Hai) {
        StoHai.move(order, i)
      }

      // 如果已取消或失败，不要重复处理
      if (order.status == StOrderStatus.Cancelled) {
        logger.info("单车运单 $robotName，运单已取消：$orderId:${i + 1}，执行后")
        updateSoc("单号=$orderId，执行第 ${i + 1} 步，执行后，已取消")
        return false
      }

      logger.info("单车运单 $robotName，运单步骤完成 $orderId:${i + 1}")
      updateSoc("单号=$orderId，执行第 ${i + 1} 步，结束")
    }

    if (order.vendor == RobotVendor.Hai) {
      // 收到全部的回复信号后,才算执行完成,前面的只算下发完成
      val rr = HaiAdapter.robots[order.robotName]!!
      while (!Thread.interrupted()) {
        Thread.sleep(1000)
        val maxNum = rr.sendUnfinishedList
        if (maxNum.size == 0) break
      }
    }

    return true
  }

  private fun executeWrap(order: StOrder, process: () -> Boolean) {
    val orderId = order.id
    logger.info("执行运单：$order")
    updateSoc("运单开始，单号=$orderId")

    try {
      // false 表示处理失败
      if (!process()) return

      logger.info("运单完成 $orderId/${order.robotName}")
      updateSoc("运单完成，单号=$orderId")

      lastOrder = order
      currentOrder = null
      StoStore.updateOrderStatus(order, StOrderStatus.Done, "", Date())
    } catch (e: Exception) {
      logger.error("运单失败 $orderId/${order.robotName}", e)
      updateSoc("运单失败，单号=$orderId：${e.message}")

      StoStore.updateOrderStatus(order, StOrderStatus.Failed, e.message ?: "", Date())
    }
  }

  private val fetchReqBody = JsonHelper.mapper.writeValueAsString(mapOf("return_laser" to false))

  private fun getCurrentStation(): String? {
    return try {
      val result = StoSeer.requestRbk(robotName, 1100, fetchReqBody)
      if (result.isNullOrBlank()) return null
      val originReport: EntityValue? = JsonHelper.mapper.readValue(result, jacksonTypeRef())
      originReport?.get("current_station") as String?
    } catch (e: Exception) {
      null
    }
  }

  fun cancelOrManualDoneCurrentOrder(newStatus: StOrderStatus) {
    val order = synchronized(this) {
      val order = currentOrder
      if (order == null) {
        logger.error("取消/手工完成机器人当前运单，但机器人 $robotName 当前没有运单")
        return
      }

      logger.info("取消/手工完成运单 ${order.id}，机器人=$robotName，新状态 $newStatus")
      updateSoc("取消/手工完成运单 ${order.id}，机器人=$robotName，新状态 $newStatus")

      StoStore.updateOrderStatus(order, newStatus, "", Date())

      currentOrder = null
      lastOrder = order

      order
    }

    if (order.vendor == RobotVendor.Seer) {
      StoSeer.cancelMove(order)
    } else if (order.vendor == RobotVendor.Hai) {
      logger.warn("不支持向单车发送取消海柔简单运单")
    } else if (order.vendor == RobotVendor.Hik) {
      logger.warn("不支持向单车发送取消海康简单运单")
    }
  }

  fun cancelOrders(cancelledOrderIds: List<String>) {
    synchronized(this) {
      for (orderId in cancelledOrderIds) {
        try {
          if (orderId == currentOrder?.id) {
            cancelOrManualDoneCurrentOrder(StOrderStatus.Cancelled)
          } else {
            cancelOrder(orderId)
          }
        } catch (e: Exception) {
          logger.error("取消简单运单 $orderId", e)
        }
      }
    }
  }

  fun retryFailed() {
    val order = synchronized(this) {
      val order = currentOrder ?: throw BzError("errStoRetryFailedNoCurrent")
      if (order.status != StOrderStatus.Failed) {
        throw BzError("errStoRetryFailedBadCurrentStatus", order.id, order.status)
      }

      logger.info("重试失败的运单，机器人=$robotName，当前运单=${order.id}:${order.currentMove}")
      StoStore.updateOrderStatus(order, StOrderStatus.Created, "", null)

      order
    }

    executeOrder(order)
  }

  fun manualDoneOrders(manualDoneOrderIds: List<String>) {
    synchronized(this) {
      for (orderId in manualDoneOrderIds) {
        try {
          if (orderId == currentOrder?.id) {
            cancelOrManualDoneCurrentOrder(StOrderStatus.Done)
          } else {
            manualDoneOrder(orderId)
          }
        } catch (e: Exception) {
          logger.error("人工完成简单运单 $orderId", e)
        }
      }
    }
  }

  fun cancelOrder(orderId: String) {
    StoQueue.remove(robotName, orderId)
    StoStore.updateOrderStatus(orderId, StOrderStatus.Cancelled, "", Date())
  }

  fun manualDoneOrder(orderId: String) {
    StoQueue.remove(robotName, orderId)
    StoStore.updateOrderStatus(orderId, StOrderStatus.Done, "手工完成", Date())
  }

  private fun executeStoLoop() {
    
    while (!Thread.interrupted()) {
      try {
        val status = currentOrder?.status
        if (currentOrder == null || status == StOrderStatus.Done || status == StOrderStatus.Cancelled) {
          val stOrder = StoQueue.dequeue(robotName)
          currentOrder = stOrder
          stOrder?.let {
            // 失败运单保持阻塞，等待手动重试
            if (it.status != StOrderStatus.Failed) {
              executeOrder(stOrder)
            }
          }
        }
        Thread.sleep(500)
      } catch (e: InterruptedException) {
        return
      } catch (e: Exception) {
        logger.error("executeStoLoop", e)
      }
    }
  }
}