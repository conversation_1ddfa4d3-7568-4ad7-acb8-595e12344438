package com.seer.trick.robot.stats.domain

import com.seer.trick.base.entity.service.StatisticDateType
import java.util.Date

data class RobotStats(
  val robot: String, // 机器人名称
  val battery: Double, // 电量
  val odo: Double, // 行驶里程
  val todayOdo: Double, // 今日行驶里程
  val vx: Double, // 机器人在机器人坐标系的 x 方向实际的速度, 单位 m/s
  val vy: Double, // 机器人在机器人坐标系的 y 方向实际的速度, 单位 m/s
  val w: Double, // 机器人在机器人坐标系的实际的角速度(即顺时针转为负, 逆时针转为正), 单位 rad/s
  val charging: Boolean, // 是否正在充电
  val working: Boolean, // 是否正在工作
  val isLoaded: Boolean, // 是否装载了货物
  val online: Boolean, // 是否在线
  val ifError: Boolean, // 是否发生错误
  val direction: Double, // 机器人朝向, 角度
  val fetchBy: FetchRobotStateBy, // 数据来源
  val containers: List<ContainerStats>, // 容器信息
)

data class ContainerStats(
  val containerName: String, // 容器名
  val goodsId: String? = null, // 货物 id
  val hasGoods: Boolean, // 是否载货
)

enum class FetchRobotStateBy {
  Core,
  SingleRobot,
  Fleet,
}

data class StatsTimelineValueReport(
  val subject: String, // 主题，如：充电状态、充电次数、充电时长、故障次数、故障时长等
  val obj: String, // 关键词，如：一般是机器人名；所有机器人
  val value: String, // 值
  val cycleType: StatisticDateType, // 周期类型
  val time: String, // 时间点，如：2024-01-01 12H、2024-01-01、2024 W1、2024-01 、2024 Q1、2024
  val startOn: Date, // 开始时间
  val endOn: Date, // 结束时间
)