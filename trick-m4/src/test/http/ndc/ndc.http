### Q ->
POST http://localhost:5800/api/wcs/gw/ndc/send-q
Content-Type: application/json

{
  "priority": 129,
  "iKey": 3,
  "fromBin": 3,
  "toBin": 4
}

### -> B(b) 下达的命令的任务接受
POST http://localhost:5800/api/wcs/gw/ndc/receive-bb
Content-Type: application/json

{
  "index": 1,
  "transportStructure": 1,
  "status": 1,
  "parNo": 255,
  "ikey": 61
}

### -> B(a) 下达的命令的参数值接受
POST http://localhost:5800/api/wcs/gw/ndc/receive-ba
Content-Type: application/json

{
  "index": 1,
  "transportStructure": 1,
  "status": 19,
  "parNo": 255
}

### -> B(b) 参数值接受
POST http://localhost:5800/api/wcs/gw/ndc/receive-bb
Content-Type: application/json

{
  "index": 1234,
  "transportStructure": 1,
  "status": 19,
  "parNo": 255,
  "spare": 23,
  "ikey": 10
}

### -> B(a) 更新局部参数，参数确认
POST http://localhost:5800/api/wcs/gw/ndc/receive-ba
Content-Type: application/json

{
  "index": 1234,
  "transportStructure": 1,
  "status": 19,
  "parNo": 255
}


### -> S
POST http://localhost:5800/api/wcs/gw/ndc/receive-s
Content-Type: application/json

{
  "index": 1,
  "transportStructure": 1,
  "orderStatus":  1,
  "magic": 1,
  "magic2":  1,
  "carNo": 1,
  "spare":  1,
  "carStat": 1,
  "carStn":  1,
  "magic3": 1,
  "noOfLp":  1
}

### -> S 请求装货
POST http://localhost:5800/api/wcs/gw/ndc/receive-s
Content-Type: application/json

{
  "index": 1,
  "transportStructure": 110,
  "orderStatus":  3,
  "magic": 16,
  "magic2":  1,
  "carNo": 1,
  "spare":  1,
  "carStat": 1,
  "carStn":  1,
  "magic3": 1,
  "noOfLp":  1
}


### -> S 装货完成
POST http://localhost:5800/api/wcs/gw/ndc/receive-s
Content-Type: application/json

{
  "index": 1,
  "transportStructure": 110,
  "orderStatus":  17,
  "magic": 17,
  "magic2":  1,
  "carNo": 1,
  "spare":  1,
  "carStat": 1,
  "carStn":  1,
  "magic3": 1,
  "noOfLp":  1
}


### -> S 请求卸货
POST http://localhost:5800/api/wcs/gw/ndc/receive-s
Content-Type: application/json

{
  "index": 1,
  "transportStructure": 110,
  "orderStatus":  3,
  "magic": 32,
  "magic2":  1,
  "carNo": 1,
  "spare":  1,
  "carStat": 1,
  "carStn":  1,
  "magic3": 1,
  "noOfLp":  1
}

### -> S 卸货完成
POST http://localhost:5800/api/wcs/gw/ndc/receive-s
Content-Type: application/json

{
  "index": 1,
  "transportStructure": 110,
  "orderStatus":  33,
  "magic": 33,
  "magic2":  1,
  "carNo": 1,
  "spare":  1,
  "carStat": 1,
  "carStn":  1,
  "magic3": 1,
  "noOfLp":  1
}


### -> S 任务完成
POST http://localhost:5800/api/wcs/gw/ndc/receive-s
Content-Type: application/json

{
  "index": 1,
  "transportStructure": 110,
  "orderStatus":  3,
  "magic": 80,
  "magic2":  1,
  "carNo": 1,
  "spare":  1,
  "carStat": 1,
  "carStn":  1,
  "magic3": 1,
  "noOfLp":  1
}

### N -> 取消任务
POST http://localhost:5800/api/wcs/gw/ndc/send-n
Content-Type: application/json

{
  "index": 1
}


### M -> 更新局部参数
POST http://localhost:5800/api/wcs/gw/ndc/send-m
Content-Type: application/json

{
  "index": 1,
  "function": 1,
  "parameterNumber": 10,
  "priority": 9,
  "p0": 8,
  "p1": 0,
  "p2": 0,
  "p3": 0,
  "p4": 0
}

### M -> 更新 25 号参数为 1
POST http://localhost:5800/api/wcs/gw/ndc/send-m
Content-Type: application/json

{
  "index": 1,
  "function": 1,
  "parameterNumber": 25,
  "priority": 9,
  "p0": 1,
  "p1": 0,
  "p2": 0,
  "p3": 0,
  "p4": 0
}

### M -> 更新优先级
POST http://localhost:5800/api/wcs/gw/ndc/send-m
Content-Type: application/json

{
  "index": 1,
  "function": 4,
  "parameterNumber": 3,
  "priority": 7,
  "p0": 0,
  "p1": 0,
  "p2": 0,
  "p3": 0,
  "p4": 0
}

### ping
POST http://localhost:5800/api/wcs/gw/ndc/ping
Content-Type: application/json

{}


### pong
POST http://localhost:5800/api/wcs/gw/ndc/receive-pong
Content-Type: application/json

{}
