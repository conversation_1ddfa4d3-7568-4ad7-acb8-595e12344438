### 抢控制权

POST http://localhost:7600/api/hik/lock

{
    "robotId":"143",
    "nickname": "abc"
}


### 释放控制权

POST http://localhost:7600/api/hik/unlock

{
    "robotId":"143",
    "nickname": "abc"
}


### 列出所有机器人的控制权归属
POST http://localhost:7600/api/hik/list-owner

{
    "robotIds":["143"]
}


### 带控制权的机器人任务

POST http://localhost:7600/api/hik/move/v5
Content-Type: application/json
Cookie: gw-hik-owner-nickname=abc;

[
  {
    "robotId": "143",
    "taskId": 2,
    "subTaskId": 1,
    "taskType": 0,
    "moveType": 0,
    "sourcePosition": {
      "x": 21059,
      "y": 20729
    },
    "targetPosition": {
      "x": 21059,
      "y": 22986,
      "direction": 90000,
      "targetType": 0
    },
    "finalPosition": {
      "x": 0,
      "y": 0
    },
    "paths": [
      {
        "type": 0,
        "v": 1500,
        "controlPoints": [
          {
            "x": 21059,
            "y": 20729
          },
          {
            "x": 21059,
            "y": 21926
          }
        ]
      },
      {
        "type": 0,
        "v": 1500,
        "controlPoints": [
          {
            "x": 21059,
            "y": 21926
          },
          {
            "x": 21059,
            "y": 22986
          }
        ]
      }
    ]
  }
]



