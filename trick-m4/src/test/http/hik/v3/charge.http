# 激光 充电
# chargeType = 1

POST http://localhost:7600/api/hik/charge
Content-Type: application/json

{
  "robotNo": 143,
  "taskId": 123,
  "subTaskId": 1,
  "taskType": 1,
  "moveMethod": 1,
  "maxSpeed": 100,
  "sourcePosition": {
    "x": 20390,
    "y": 20729
  },
  "targetPosition": {
    "x": 20390,
    "y": 19639,
    "direction": 90000,
    "targetType" : 2
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "v": 300,
      "type": 0
    }
  ],
  "charge": {
    "type": 1,
    "time1": 60,
    "pileId": 0,
    "pileIp": "0.0.0.0"
  }
}

###

# 取消任务

POST http://localhost:7600/api/hik/cancel
Content-Type: application/json

{
  "robotNo": 143,
  "taskId": 123,
  "subTaskId": 1
}

###

# 激光 充电点 -> 前置点

POST http://localhost:7600/api/hik/move
Content-Type: application/json

{
  "mode": "322",
  "robotNo": 143,
  "taskId": 123,
  "subTaskId": 1,
  "moveType": 0,

  "sourcePosition": {
    "x": 20390,
    "y": 19639
  },
  "targetPosition": {
    "x": 20390,
    "y": 20729,
    "direction": 90000,
    "targetType" : 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0
    }
  ]
}

###

