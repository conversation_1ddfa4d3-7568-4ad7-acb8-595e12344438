# 激光 旋转

POST http://localhost:7600/api/hik/move/v3
Content-Type: application/json

{
  "robotId": "143",
  "taskId": 123,
  "subTaskId": 1,
  "taskType": 0,
  "moveMethod": 0,
  "sourcePosition": {
    "x": 23394,
    "y": 22983
  },
  "targetPosition": {
    "x": 23394,
    "y": 22983,
    "direction": 90000,
    "targetType" : 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0
    }
  ]
}

###

# 激光  停靠点 -> 左下角

POST http://localhost:7600/api/hik/move/v3
Content-Type: application/json

{
  "robotId": "143",
  "taskId": 123,
  "subTaskId": 1,
  "taskType": 0,
  "moveMethod": 0,
  "sourcePosition" : {
    "x": 23394,
    "y": 22983
  },
  "targetPosition" : {
    "x": 22599,
    "y": 23904,
    "direction": 0,
    "targetType" : 0
  },
  "finalPosition" : {
    "x": 0,
    "y":  0
  },
  "paths": [
    {
      "type": 0
    }
  ]
}

###


# 激光 旋转

POST http://localhost:7600/api/hik/move/v3
Content-Type: application/json

{
  "robotId": "143",
  "taskId": 123,
  "subTaskId": 1,
  "taskType": 0,
  "moveMethod": 0,
  "sourcePosition" : {
    "x": 21120,
    "y": 20677
  },
  "targetPosition" : {
    "x": 21120,
    "y": 20677,
    "direction": -90000,
    "targetType" : 0
  },
  "finalPosition" : {
    "x": 0,
    "y":  0
  },
  "paths": [
    {
      "type": 0
    }
  ]
}

###


# 激光 停靠点 -> 左下角

POST http://localhost:7600/api/hik/move
Content-Type: application/json

{
  "robotId": "143",
  "taskId": 123,
  "subTaskId": 1,
  "taskType": 0,
  "moveMethod": 0,
  "sourcePosition": {
    "x": 21120,
    "y": 20677
  },
  "targetPosition": {
    "x": 21120,
    "y": 20043,
    "direction": -90000,
    "targetType" : 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0
    }
  ]
}

###

