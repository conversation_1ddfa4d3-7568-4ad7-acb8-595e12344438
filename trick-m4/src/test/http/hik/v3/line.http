################################################################################################
#   左下角 -> 右下角
################################################################################################

# 激光 旋转 左下角

POST http://localhost:7600/api/hik/move
Content-Type: application/json

{
  "robotNo": 143,
  "taskId": 123,
  "subTaskId": 1,
  "moveMethod": 0,
  "sourcePosition": {
    "x": 21059,
    "y": 20729
  },
  "targetPosition": {
    "x": 21059,
    "y": 20729,
    "direction": 0,
    "targetType" : 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0
    }
  ]
}

###

# 激光 前进 左下角 -> 右下角

POST http://localhost:7600/api/hik/move
Content-Type: application/json

{

  "robotNo": 143,
  "taskId": 123,
  "subTaskId": 1,
  "moveMethod": 0,
  "sourcePosition": {
    "x": 21059,
    "y": 20729
  },
  "targetPosition": {
    "x": 23391,
    "y": 20729,
    "direction": 0,
    "targetType" : 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0
    }
  ]
}

###

# 激光 旋转 右下角

POST http://localhost:7600/api/hik/move
Content-Type: application/json

{

  "robotNo": 143,
  "taskId": 123,
  "subTaskId": 1,
  "moveMethod": 0,
  "sourcePosition": {
    "x": 23391,
    "y": 20729
  },
  "targetPosition": {
    "x": 23391,
    "y": 20729,
    "direction": 90000,
    "targetType" : 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0
    }
  ]
}

###


# 激光 前进 右下角 -> 左下角

POST http://localhost:7600/api/hik/move
Content-Type: application/json

{

  "robotNo": 143,
  "taskId": 123,
  "subTaskId": 1,
  "moveMethod": 0,
  "sourcePosition" : {
    "x": 23391,
    "y": 20729
  },
  "targetPosition" : {
    "x": 21059,
    "y": 20729,
    "direction": 180000,
    "targetType" : 0
  },
  "finalPosition" : {
    "x": 0,
    "y":  0
  },
  "paths": [
    {
      "type": 0
    }
  ]
}

###

# 激光 左下角 -> 充电前置点

POST http://localhost:7600/api/hik/move
Content-Type: application/json

{

  "robotNo": 143,
  "taskId": 124,
  "subTaskId": 1,
  "moveMethod": 1,
  "sourcePosition" : {
    "x": 21059,
    "y": 20729
  },
  "targetPosition" : {
    "x": 20390,
    "y": 20729,
    "direction": 0,
    "targetType" : 0
  },
  "finalPosition" : {
    "x": 0,
    "y":  0
  },
  "paths": [
    {
      "type": 0
    }
  ]
}

###

# 充电前置点 -> 左下角

POST http://localhost:7600/api/hik/move
Content-Type: application/json

{
  "robotNo": 143,
  "taskId": 123,
  "subTaskId": 1,
  "moveMethod": 0,
  "sourcePosition" : {
    "x": 20390,
    "y": 20729
  },
  "targetPosition" : {
    "x": 21059,
    "y": 20729,
    "direction": 0,
    "targetType" : 0
  },
  "finalPosition" : {
    "x": 0,
    "y":  0
  },
  "paths": [
    {
      "type": 0
    }
  ]
}

###

