# 激光车 顶升车 直线 移动 右上角 -> 左上角

POST http://localhost:7600/api/hik/jackMove
Content-Type: application/json

{
  "robotNo": 143,
  "taskId": 123,
  "subTaskId": 1,
  "moveType": 2,
  "sourcePosition": {
    "x": 23393,
    "y": 22988
  },
  "targetPosition": {
    "x": 21120,
    "y": 22988,
    "direction": 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0
    }
  ],
  "goods": {
    "id": "",
    "adjustType": 3,
    "moveDirection": 999000,
    "targetDirection": 0,
    "length": 1000,
    "width": 1000,
    "weight": 0
  }
}

###

# 激光车 顶升车 直线 移动 左上角 -> 右上角

POST http://localhost:7600/api/hik/jackMove
Content-Type: application/json

{
  "robotNo": 143,
  "taskId": 123,
  "subTaskId": 1,
  "moveType": 2,
  "sourcePosition": {
    "x": 21120,
    "y": 22988
  },
  "targetPosition": {
    "x": 23393,
    "y": 22988,
    "direction": 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0
    }
  ],
  "goods": {
    "id": "",
    "adjustType": 3,
    "moveDirection": 999000,
    "targetDirection": 0,
    "length": 1000,
    "width": 1000,
    "weight": 0
  }
}

###

# 激光车 顶升车 直线 右上角 -> 右下角

POST http://localhost:7600/api/hik/jackMove
Content-Type: application/json

{
  "robotNo": 143,
  "taskId": 123,
  "subTaskId": 1,
  "moveType": 2,

  "sourcePosition": {
    "x": 23391,
    "y": 22986
  },
  "targetPosition": {
    "x": 21059,
    "y": 20729,
    "direction": 180000
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0
    }
  ],
  "goods": {
    "id": "",
    "adjustType": 3,
    "moveDirection": 999000,
    "targetDirection": 0,
    "length": 1000,
    "width": 1000,
    "weight": 0
  }
}

###

# 激光车 顶升车 直线 右下角 -> 左下角

POST http://localhost:7600/api/hik/jackMove
Content-Type: application/json

{
  "robotNo": 143,
  "taskId": 123,
  "subTaskId": 1,
  "moveType": 0,
  "sourcePosition": {
    "x": 23391,
    "y": 20729
  },
  "targetPosition": {
    "x": 21059,
    "y": 20729,
    "direction": 90000
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0
    }
  ],
  "goods": {
    "id": "",
    "adjustType": 3,
    "moveDirection": 999000,
    "targetDirection": 0,
    "length": 1000,
    "width": 1000,
    "weight": 0
  }
}

###

# 激光车 顶升车 直线 左下角 -> 左上角

POST http://localhost:7600/api/hik/jackMove
Content-Type: application/json

{
  "robotNo": 143,
  "taskId": 123,
  "subTaskId": 1,
  "moveType": 0,
  "sourcePosition": {
    "x": 21059,
    "y": 20729
  },
  "targetPosition": {
    "x":  21059,
    "y":  22986,
    "direction": 90000
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0
    }
  ],
  "goods": {
    "id": "",
    "adjustType": 3,
    "moveDirection": 999000,
    "targetDirection": 0,
    "length": 1000,
    "width": 1000,
    "weight": 0
  }
}

###

# 激光车 顶升车 直线 左下角 -> 右下角

POST http://localhost:7600/api/hik/jackMove
Content-Type: application/json

{
  "robotNo": 143,
  "taskId": 123,
  "subTaskId": 1,
  "moveType": 2,
  "sourcePosition": {
    "x": 21059,
    "y": 20729
  },
  "targetPosition": {
    "x": 23391,
    "y": 20729,
    "direction": 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0
    }
  ],
  "goods": {
    "id": "",
    "adjustType": 3,
    "moveDirection": 999000,
    "targetDirection": 0,
    "length": 1000,
    "width": 1000,
    "weight": 0
  }
}

###