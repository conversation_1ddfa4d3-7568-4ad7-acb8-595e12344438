# 激光 顶升车 旋转  左上角
POST http://localhost:7600/api/hik/jackMove
Content-Type: application/json

{
  "robotNo": 143,
  "taskId": 140,
  "subTaskId": 0,
  "taskType": 0,
  "moveType": 0,
  "sourcePosition": {
    "x": 21059,
    "y": 22986
  },
  "targetPosition": {
    "x": 21059,
    "y": 22986,
    "direction": 0
  },
  "finalPosition": {
    "x": 21059,
    "y": 22986
  },
  "paths": [
    {
      "type": 0
    }
  ],
  "goods": {
    "id": "",
    "adjustType": 0,
    "moveDirection": 999000,
    "targetDirection": 0,
    "length": 1000,
    "width": 1000,
    "weight": 0
  }
}

###

# 激光车 顶升车 旋转 右上角

POST http://localhost:7600/api/hik/jackMove
Content-Type: application/json

{
  "robotNo": 143,
  "taskId": 123,
  "subTaskId": 1,

  "taskType": 0,
  "moveType": 0,
  "sourcePosition": {
    "x": 23391,
     "y": 22986
  },
  "targetPosition": {
    "x": 23391,
    "y": 22986,
    "direction": -90000
  },
  "finalPosition": {
    "x": 23391,
    "y": 22986
  },
  "paths": [
    {
      "type": 0
    }
  ],
  "goods": {
    "id": "",
    "adjustType": 3,
    "moveDirection": 999000,
    "targetDirection": 0,
    "length": 1000,
    "width": 1000,
    "weight": 0
  }
}

###

# 激光 顶升车 旋转 左下角
POST http://localhost:7600/api/hik/jackMove
Content-Type: application/json

{
  "mode": "324",
  "robotNo": 143,
  "taskId": 140,
  "subTaskId": 0,

  "taskType": 0,
  "moveType": 0,
  "sourcePosition": {
    "x": 21059,
    "y": 20729
  },
  "targetPosition": {
    "x": 21059,
    "y": 20729,
    "direction": 90000
  },
  "finalPosition": {
    "x": 21059,
    "y": 20729
  },
  "paths": [
    {
      "type": 0
    }
  ],
  "goods": {
    "id": "",
    "adjustType": 0,
    "moveDirection": 999000,
    "targetDirection": 0,
    "length": 1000,
    "width": 1000,
    "weight": 0
  }
}

###

# 激光车 顶升车 旋转 右下角

POST http://localhost:7600/api/hik/jackMove
Content-Type: application/json

{
  "mode": "324",
  "robotNo": 143,
  "taskId": 123,
  "subTaskId": 1,
  "taskType": 0,
  "moveType": 2,
  "sourcePosition": {
    "x": 23391,
    "y": 20729
  },
  "targetPosition": {
    "x": 23391,
    "y": 20729,
    "direction": 180000
  },
  "finalPosition": {
    "x": 23391,
    "y": 20729
  },
  "paths": [
    {
      "type": 0
    }
  ],
  "goods": {
    "id": "",
    "adjustType": 0,
    "moveDirection": 999000,
    "targetDirection": 0,
    "length": 1000,
    "width": 1000,
    "weight": 0
  }
}

###
