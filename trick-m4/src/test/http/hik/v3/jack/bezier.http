# 激光 顶升车 曲线 左上角 -> 右下角

POST http://localhost:7600/api/hik/jackMove
Content-Type: application/json

{
  "robotNo": 143,
  "taskId": 123,
  "subTaskId": 1,
  "moveType": 0,

  "sourcePosition": {
    "x": 21059,
    "y": 22986
  },
  "targetPosition": {
    "x": 23391,
    "y": 20729,
    "direction": 0,
    "reachDist": 0,

  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 1,
      "controlPoints": [
        {
          "x": 22031,
          "y": 22986
        },
        {
          "x": 22843,
          "y": 22986
        },
        {
          "x": 21607,
          "y": 20729
        },
        {
          "x": 22419,
          "y": 20729
        }
      ]
    }
  ],
  "goods": {
    "id": "",
    "adjustType": 3,
    "moveDirection": 999000,
    "targetDirection": 0,
    "length": 1000,
    "width": 1000,
    "weight": 0
  }
}

###

# 激光 顶升车 曲线 右下角 -> 左上角
POST http://localhost:7600/api/hik/jackMove
Content-Type: application/json

{
  "robotNo": 143,
  "taskId": 125,
  "subTaskId": 0,
  "moveType": 0,

  "sourcePosition": {
    "x": 23391,
    "y": 20729
  },
  "targetPosition": {
    "x": 21059,
    "y": 22986,
    "direction": 180000
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 1,
      "controlPoints": [
        {
          "x": 22419,
          "y": 20729
        },
        {
          "x": 21607,
          "y": 20729
        },
        {
          "x": 22843,
          "y": 22986
        },
        {
          "x": 22031,
          "y": 22986
        }
      ]
    }
  ],
  "goods": {
    "id": "",
    "adjustType": 3,
    "moveDirection": 999000,
    "targetDirection": 0,
    "length": 1000,
    "width": 1000,
    "weight": 0
  }
}

###

# 激光 322 曲线 1 左下角 -> 右上角

POST http://localhost:7600/api/hik/jackMove
Content-Type: application/json

{
  "robotNo": 143,
  "taskId": 123,
  "subTaskId": 1,
  "taskType": 0,
  "moveType": 0,
  "sourcePosition": {
    "x": 21059,
    "y": 20729
  },
  "targetPosition": {
    "x": 23391,
    "y": 22986,
    "direction": -90000
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 1,
      "controlPoints": [
        {
          "x": 21059,
          "y": 21591
        },
        {
          "x": 21059,
          "y": 22322
        },
        {
          "x": 23391,
          "y": 21392
        },
        {
          "x": 23391,
          "y": 21392
        }
      ]
    }
  ],
  "goods": {
    "id": "",
    "adjustType": 3,
    "moveDirection": 999000,
    "targetDirection": 0,
    "length": 1000,
    "width": 1000,
    "weight": 0
  }
}

###

# 激光 322 曲线 1 右上角 -> 左下角

POST http://localhost:7600/api/hik/jackMove
Content-Type: application/json

{
  "robotNo": 143,
  "taskId": 123,
  "subTaskId": 1,
  "taskType": 0,
  "moveType": 0,
  "sourcePosition": {
    "x": 23391 ,
    "y": 22986
  },
  "targetPosition": {
    "x": 21059,
    "y": 20729,
    "direction": 90000
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 1,
      "controlPoints": [
        {
          "x": 23391,
          "y": 21392
        },
        {
          "x": 23391,
          "y": 21392
        },
        {
          "x": 21059,
          "y": 22322
        },
        {
          "x": 21059,
          "y": 21591
        }
      ]
    }
  ],
  "goods": {
    "id": "",
    "adjustType": 3,
    "moveDirection": 999000,
    "targetDirection": 0,
    "length": 1000,
    "width": 1000,
    "weight": 0
  }
}

###
