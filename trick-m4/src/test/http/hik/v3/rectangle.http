# 激光模式 转圈 1 旋转 左下角

POST http://localhost:7600/api/hik/move/v3
Content-Type: application/json

{
  "robotId": "143",
  "taskId": 1,
  "subTaskId": 1,
  "taskType": 0,
  "moveType": 0,
  "sourcePosition": {
    "x": 21059,
    "y": 20729
  },
  "targetPosition": {
    "x": 21059,
    "y": 20729,
    "direction": 90000,
    "targetType": 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0
    }
  ]
}

###

# 激光模式 转圈 2 前进 左边向上

POST http://localhost:7600/api/hik/move/v3
Content-Type: application/json

{
"robotId": "143",
  "taskId": 2,
  "subTaskId": 1,
  "taskType": 0,
  "moveType": 0,
  "sourcePosition": {
    "x": 21059,
    "y": 20729
  },
  "targetPosition": {
    "x": 21059,
    "y": 22986,
    "direction": 90000,
    "targetType" : 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0
    }
  ]
}

###

# 激光模式 转圈 3 旋转 左上角

POST http://localhost:7600/api/hik/move
Content-Type: application/json

[{
  "robotId": "143",
  "taskId": 2,
  "subTaskId": 1,
  "taskType": 0,
  "moveType": 0,
  "sourcePosition": {
    "x": 21059,
    "y": 22986
  },
  "targetPosition": {
    "x": 21059,
    "y": 22986,
    "direction": -90000,
    "targetType": 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0
    }
  ]
}]

###

# 激光模式 转圈 2 前进 左边向下

POST http://localhost:7600/api/hik/move/v3
Content-Type: application/json

{
  "robotId": "143",
  "taskId": 2,
  "subTaskId": 1,
  "taskType": 0,
  "moveType": 0,
  "sourcePosition": {
    "x": 21059,
    "y": 22986
  },
  "targetPosition": {
    "x": 21059,
    "y": 20729,
    "direction": -90000,
    "targetType": 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0,
      "controlPoints": [
        {
          "x": 21059,
          "y": 22986
        },
        {
          "x": 21059,
          "y": 20729
        }
      ]
    }
  ]
}

###

# 激光模式 转圈 4 前进 上边向右

POST http://localhost:7600/api/hik/move
Content-Type: application/json

{
"robotId": "143",
  "taskId": 3,
  "subTaskId": 1,
  "taskType": 0,
  "moveType": 0,
  "sourcePosition": {
    "x": 21059,
    "y": 22986
  },
  "targetPosition": {
    "x": 23391,
    "y": 22986,
    "direction": 0,
    "targetType" : 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0
    }
  ]
}

###

# 激光模式 转圈 5 旋转 右上角

POST http://localhost:7600/api/hik/move
Content-Type: application/json

{
"robotId": "143",
  "taskId": 2,
  "subTaskId": 1,
  "taskType": 0,
  "moveType": 0,

  "sourcePosition": {
    "x": 23391,
    "y": 22986
  },
  "targetPosition": {
    "x": 23391,
    "y": 22986,
    "direction": -90000,
    "targetType" : 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0
    }
  ]
}

###

# 激光模式 转圈 4 前进 上边向左

POST http://localhost:7600/api/hik/move
Content-Type: application/json

{
"robotId": "143",
  "taskId": 3,
  "subTaskId": 1,
  "taskType": 0,
  "moveType": 0,
  "sourcePosition": {
    "x": 23391,
    "y": 22986
  },
  "targetPosition": {
    "x": 21059 ,
    "y": 22986 ,
    "direction": 180000,
    "targetType" : 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0
    }
  ]
}

###

# 激光模式 转圈 6 前进 右边向下

POST http://localhost:7600/api/hik/move
Content-Type: application/json

{
"robotId": "143",
  "taskId": 2,
  "subTaskId": 1,
  "taskType": 0,
  "moveType": 0,

  "sourcePosition": {
    "x": 23393 ,
    "y": 22988
  },
  "targetPosition": {
    "x": 23393,
    "y": 20677,
    "direction": -90000,
    "targetType" : 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0
    }
  ]
}

###


# 激光模式 转圈 7 旋转 右下角

POST http://localhost:7600/api/hik/move
Content-Type: application/json

{
"robotId": "143",
  "taskId": 2,
  "subTaskId": 1,
  "taskType": 0,
  "moveType": 0,
  "routeAngle": 0,
  "ha": {
    "distance": 0,
    "funIdx": 0,
    "safeDistF": 0,
    "safeDistB": 0,
    "safeDistL": 0,
    "safeDistR": 0
  },
  "sourcePosition": {
    "x": 23391,
    "y": 20677
  },
  "targetPosition": {
    "x": 23391,
    "y": 20677,
    "direction": 180000,
    "targetType" : 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0
    }
  ]
}

###

# 激光模式 转圈 8 前进 底边向左

POST http://localhost:7600/api/hik/move
Content-Type: application/json

{
"robotId": "143",
  "taskId": 2,
  "subTaskId": 1,
  "taskType": 0,
  "moveType": 0,

  "sourcePosition": {
    "x": 23391,
    "y": 20729
  },
  "targetPosition": {
    "x": 21059,
    "y": 20729,
    "direction": 180000,
    "targetType" : 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0
    }
  ]
}

###
