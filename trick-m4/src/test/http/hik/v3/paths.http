
# 激光模式 转圈 4 前进 左上角 -> 右上角 -> 右下角

POST http://localhost:7600/api/hik/move
Content-Type: application/json

{
  "robotNo": 143,
  "taskId": 3,
  "subTaskId": 1,
  "taskType": 0,
  "moveType": 0,
  "sourcePosition": {
    "x": 21059,
    "y": 22986
  },
  "targetPosition": {
    "x": 23391,
    "y": 22986,
    "direction": 0,
    "targetType" : 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0,
      "v": 100
    }
  ]
}

###

# 激光模式 转圈 5 旋转 右上角

POST http://localhost:7600/api/hik/move
Content-Type: application/json

{
  "robotNo": 143,
  "taskId": 2,
  "subTaskId": 1,
  "taskType": 0,
  "moveType": 0,

  "sourcePosition": {
    "x": 23391,
    "y": 22986
  },
  "targetPosition": {
    "x": 23391,
    "y": 22986,
    "direction": -90000,
    "targetType" : 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0
    }
  ]
}

###

# 激光模式 转圈 6 前进 右边向下

POST http://localhost:7600/api/hik/move
Content-Type: application/json

{
  "robotNo": 143,
  "taskId": 2,
  "subTaskId": 1,
  "taskType": 0,
  "moveType": 0,

  "sourcePosition": {
    "x": 23391,
    "y": 22986
  },
  "targetPosition": {
    "x": 23391,
    "y": 20729,
    "direction": -90000,
    "targetType" : 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0
    }
  ]
}

###


# 激光模式 转圈 7 旋转 右下角

POST http://localhost:7600/api/hik/move
Content-Type: application/json

{
  "robotNo": 143,
  "taskId": 2,
  "subTaskId": 1,
  "taskType": 0,
  "moveType": 0,

  "sourcePosition": {
    "x": 23391,
    "y": 20729
  },
  "targetPosition": {
    "x": 23391,
    "y": 20729,
    "direction": 180000,
    "targetType" : 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0
    }
  ]
}

###