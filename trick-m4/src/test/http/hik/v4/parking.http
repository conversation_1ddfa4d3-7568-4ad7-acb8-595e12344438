# 激光 旋转

POST http://localhost:7600/api/hik/move
Content-Type: application/json

[
  {
    "robotId": "143",
    "taskId": 123,
    "subTaskId": 1,
    "taskType": 0,
    "moveMethod": 0,
    "sourcePosition": {
      "x": 21059,
      "y": 20083
    },
    "targetPosition": {
      "x": 21059,
      "y": 20083,
      "direction": 90000,
      "targetType" : 0
    },
    "finalPosition": {
      "x": 0,
      "y": 0
    },
    "paths": [
      {
        "type": 0,
        "controlPoints" : [
          {
            "x": 21059,
            "y": 20083
          },
          {
            "x": 21059,
            "y": 20083
          }
        ]
      }
    ]
  }
]

###

# 激光  停靠点 -> 左下角

POST http://localhost:7600/api/hik/move
Content-Type: application/json

[
{
  "robotId": "143",
  "taskId": 123,
  "subTaskId": 1,
  "taskType": 0,
  "moveMethod": 0,
  "sourcePosition" : {
    "x": 21059,
    "y": 20083
  },
  "targetPosition" : {
    "x": 21059,
    "y": 20729,
    "direction": 90000,
    "targetType" : 0
  },
  "finalPosition" : {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0,
      "controlPoints" : [
        {
          "x": 21059,
          "y": 20083
        },
        {
          "x": 21059,
          "y": 20729
        }
      ]
    }
  ]
}
]

###


# 激光 旋转

POST http://localhost:7600/api/hik/move
Content-Type: application/json

[
{
  "robotId": "143",
  "taskId": 123,
  "subTaskId": 1,
  "taskType": 0,
  "moveMethod": 0,
  "sourcePosition" : {
    "x": 21120,
    "y": 20677
  },
  "targetPosition" : {
    "x": 21120,
    "y": 20677,
    "direction": -90000,
    "targetType" : 0
  },
  "finalPosition" : {
    "x": 0,
    "y":  0
  },
  "paths": [
    {
      "type": 0,
      "controlPoints" : [
        {
          "x": 21120,
          "y": 20043
        },
        {
          "x": 21120,
          "y": 20043
        }
      ]
    }
  ]
}
]

###


# 激光 左下角 -> 停靠点

POST http://localhost:7600/api/hik/move
Content-Type: application/json

[
{
  "robotId": "143",
  "taskId": 123,
  "subTaskId": 0,
  "taskType": 0,
  "moveMethod": 0,
  "sourcePosition": {
    "x": 21059,
    "y": 20793
  },
  "targetPosition": {
    "x": 21059,
    "y": 20083,
    "direction": -90000,
    "targetType" : 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0,
      "controlPoints" : [
        {
          "x": 21059,
          "y": 20793
        },
        {
          "x": 21059,
          "y": 20083
        }
    ]
    }
  ]
}
]

###





# 其它测试

POST http://localhost:7600/api/hik/move
Content-Type: application/json

[
  {
    "robotId": "143",
    "taskId": 123,
    "subTaskId": 1,
    "taskType": 0,
    "moveMethod": 0,
    "sourcePosition" : {
      "x": 21059,
      "y": 20083
    },
    "targetPosition" : {
      "x": 21059,
      "y": 20729,
      "direction": -90000,
      "targetType" : 0
    },
    "finalPosition" : {
      "x": 0,
      "y":  0
    },
    "paths": [
      {
        "type": 0,
        "controlPoints" : [
          {
            "x": 21059,
            "y": 20083
          },
          {
            "x": 21059,
            "y": 20729
          }
        ]
      }
    ]
  }
]
