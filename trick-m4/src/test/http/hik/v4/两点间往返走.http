# 激光模式 转圈 1 左上角 <--> 左下角

POST http://localhost:7600/api/hik/move
Content-Type: application/json

[
  {
    "robotId": "143",
    "taskId": 2,
    "subTaskId": 1,
    "taskType": 0,
    "moveType": 0,
    "sourcePosition": {
      "x": 21059,
      "y": 22986
    },
    "targetPosition": {
      "x": 21059,
      "y": 22986,
      "direction": -90000,
      "targetType": 0
    },
    "finalPosition": {
      "x": 0,
      "y": 0
    },
    "paths": [
      {
        "type": 0
      }
    ]
  },
  {
    "robotId": "143",
    "taskId": 2,
    "subTaskId": 2,
    "taskType": 0,
    "moveType": 0,
    "sourcePosition": {
      "x": 21059,
      "y": 22986
    },
    "targetPosition": {
      "x": 21059,
      "y": 20729,
      "direction": -90000,
      "targetType": 0
    },
    "finalPosition": {
      "x": 0,
      "y": 0
    },
    "paths": [
      {
        "type": 0,
        "controlPoints": [
          {
            "x": 21059,
            "y": 22986
          },
          {
            "x": 21059,
            "y": 20729
          }
        ]
      }
    ]
  },
  {
    "robotId": "143",
    "taskId": 2,
    "subTaskId": 3,
    "taskType": 0,
    "moveType": 0,
    "sourcePosition": {
      "x": 21059,
      "y": 20729
    },
    "targetPosition": {
      "x": 21059,
      "y": 20729,
      "direction": 90000,
      "targetType": 0
    },
    "finalPosition": {
      "x": 0,
      "y": 0
    },
    "paths": [
      {
        "type": 0
      }
    ]
  },
  {
    "robotId": "143",
    "taskId": 2,
    "subTaskId": 4,
    "taskType": 0,
    "moveType": 0,
    "sourcePosition": {
      "x": 21059,
      "y": 20729
    },
    "targetPosition": {
      "x": 21059,
      "y": 22986,
      "direction": 90000,
      "targetType": 0
    },
    "finalPosition": {
      "x": 0,
      "y": 0
    },
    "paths": [
      {
        "type": 0,
        "controlPoints": [
          {
            "x": 21059,
            "y": 20729
          },
          {
            "x": 21059,
            "y": 22986
          }
        ]
      }
    ]
  }
]

###
