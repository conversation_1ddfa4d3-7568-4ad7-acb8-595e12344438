
# 激光模式 转圈 2 前进 左边向上 1

POST http://localhost:7600/api/hik/move/v3
Content-Type: application/json

{
  "robotId": "143",
  "taskId": 2,
  "subTaskId": 1,
  "taskType": 0,
  "moveType": 0,
  "sourcePosition": {
    "x": 21059,
    "y": 20729
  },
  "targetPosition": {
    "x": 21059,
    "y": 21926,
    "direction": 90000,
    "targetType": 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0,
      "v": 1500,
      "controlPoints": [
        {
          "x": 21059,
          "y": 20729
        },
        {
          "x": 21059,
          "y": 21926
        }
      ]
    }
  ]
}

###


# 激光模式 转圈 2 前进 左边向上 2

POST http://localhost:7600/api/hik/move/v3
Content-Type: application/json

{
  "robotId": "143",
  "taskId": 2,
  "subTaskId": 2,
  "taskType": 0,
  "moveType": 0,
  "sourcePosition": {
    "x": 21059,
    "y": 21926
  },
  "targetPosition": {
    "x": 21059,
    "y": 22986,
    "direction": 90000,
    "targetType": 0
  },
  "finalPosition": {
    "x": 0,
    "y": 0
  },
  "paths": [
    {
      "type": 0,
      "v": 1500,
      "controlPoints": [
        {
          "x": 21059,
          "y": 21926
        },
        {
          "x": 21059,
          "y": 22986
        }
      ]
    }
  ]
}

###
