errFalconLabelEmpty=タスク名必須
errFalconLabelRepeat=重複タスク名："{0}"
errFleetNoSuchSceneById=存在しないシーン：{0}
errIllegalFile=不正ファイル
errNoRdsScene=配車シーン欠如
errRbkReqResBadCode=リクエストロボット失敗。エラーコード：{0}。エラー情報：{1}
pwdRequirementModerate=大文字、小文字、数字を含む、最低 8 文字
pwdRequirementStrong=大文字、小文字、数字、特殊文字を含む、最低 12 文字
pwdRequirementWeak="最低 1 文字"
errAdminRequired=管理人権限必要
errAggregateQuery=集計検索エラー: {0}
errBadColumnName=列名無効 "{0}"
errBadHttpMethod=サポートしない Http方法：{0}
errBadIP=IP 拡張子ミス："{0}"
errBadReqBodyJson=JSON 拡張子エラー：{0}
errBadRequestTimepointOrTimestamp=タイムスタンプ（timestamp）とタイムライン（timeline）同時検索不可。
errBinHasContainerNotToLoad=ロケーション {0}にはすでに容器 {1}があり，容器 {2}を置くことができない
errBinNoContainer=ロケーション {} に必ず1つの容器が必要
errBinNoRobot=ロケーション "{0}" ロボット位置パラメーター未設定
errBinNotEmpty=ロケーション {} は入力不要
errBpNoChild=コンポーネントにサブコンポーネント "{0}"が設定されていない
errBpRunError=コンペ―ネント"{1}" 運行エラー：{0}
errBreakLoop=終了循環
errBzError=業務エラー：{0}
errBzMaterialCategoryNoStoreDistricts=商品分類 "{0}" 格納倉庫エリア未設定
errBzMaterialNoCategory=商品 "{0}" 分類未設定
errBzMissingKeyParam=必須パラメーター欠如："{0}"
errBzNoActiveBomForMat=商品 "{0}" 使用商品リストが見つからない
errBzNoEnoughContainerForMaterial=商品 "{0}"を入れるスペース不足
errBzNoEnoughEmptyContainer=空いている容器不足（期待値
errBzNoMaterialById=商品 "{0}"が見つからない
errBzNoMaterialCategoryById=商品分類 "{0}"が見つからない
errBzNoMaterialContainerMaxQty=商品 "{0}" 容器容量未設定
errBzNoMaterialContainerMaxQty2=商品 "{0}" が“有効な”容器容量未設定
errBzNoSuchOrderNameId=見つからないオーダー "{0}" / "{1}"
errBzOutboundLineShort=第 {0} 行，商品 "{2}"，在庫不足{1} 個
errCannotExceedStatisticTimeSpan=統計期間は {0} 日を超えてはいけない
errCannotExecuteNonDeviceOperation=機関制御でない操作は実行してはいけない
errCodeErr=プログラムエラー：{0}
errColumnStartGtColumnEnd=開始列番号は終了列番号より大きい
errComplexQueryBadOp=サポートしない演算子"{0}"
errComplexQueryInMultiple=検索には複数の値を指定する必要がある
errComplexQueryMissingField1=検索には"フィールド1"を指定する必要がある
errComplexQueryMissingOp=検索には演算子が必要
errComplexQueryNotSupportField={0} タイプのフィールド {1}|{2} は{3} の操作列をサポートしない
errComplexQueryUnknownType=サポートしない検索タイプ "{0}"
errComplexQueryValueNeedTwo=フィールド "{0}" は2つの検索値が必要
errComplexQueryValueNotString=フィールド "{0}"の検索値は文字列ではない
errComplexQueryValueNull=フィールド "{0}" 検索値は空
errContainerBadCurrentBin=容器 {0} はロケーション {1}にあり， {2}にない
errContainerLocked=容器 "{0}" は現在使用中の可能性があります。他の容器を試してください（ロック
errContainerNoBin=容器 "{0}" の現在ロケーションなし
errContainerNoBinMissingFromBin=容器 {0} の現在ロケーションが見つからない，始点として指定する必要がある
errContainerNoType=容器 "{0}"が タイプ未設定
errContainerOrFromBinSpecified=容器または始点ロケーションのいずれかを指定する必要あり
errContainerTypeNoStoreDistricts=容器タイプ "{0}" が格納エリア未設定
errDbProcessing=データベース処理中，しばらくしてから再試行してください
errDbRestoreNoInfo=データファイルが正しくない（ info.txtがない）
errDepthStartGtDepthEnd=開始奥行は終了奥行より大きい
errDingBadConfig=DingTalkログイン設定が不完全
errDingCallErr=DingTalkリクエスト失敗，コード
errDingNotEnabled=DingTalkが無効状態
errDingShuNoCode=codeなし
errDirFilesNull=目次中のファイル一覧に失敗
errDirNotExists=存在しない目次：{0}
errDirectOutboundSubmitFail=直接出庫提出失敗。原因：{0}
errDirectorOutboundBinNotOccupied=ロケーション {0} が空欄。（直接出庫）
errDirectorOutboundEmptyLayouts=出庫可能在庫明細なし。（直接出庫）
errDirectorOutboundNoOrder=直接出庫未設定のオーダーリスト
errDistrictIdsEmpty=少なくとも1つの倉庫エリアを指定する必要あり
errDuplicatedKeyError="{0}" の "{1}" フィールド値重複不可，重複値
errEmptyEntityValue=業務対象 "{0}" 値は空
errEmptyPassword=パスワード入力必須
errEntityNoField=業務対象 "{0}" フィールドなし "{1}"
errEntityRequestMissingIdOrQueryParam="id" 或いは "query" パラメーター指定必要あり
errEntityRequestMissingQueryParam=検索に"query" パラメーター指定必要あり
errExceedingMaxDecimalLength=業務対象 {0} のフィールド値 {1}小数部の最大長さ {2}を超えている
errExceedingMaxIntegerLength=業務対象 {0} のフィールド値 {1} は整数部の最大長さ {2}を超えている
errExceedingMaxValue=業務対象 {0} のフィールド値 {1}は許容される最大値 {2}を超えている
errExceedingMinValue=業務対象 {0} のフィールド値 {1} は許容される最小値 {2}を超えている
errFSNotEnabled=飞书無効状態
errFalconBlockInputParamNotList=コンペ―ネント"{0}" 入力パラメーター "{1}" 文字列であることが必要
errFalconBlockInputParamRangeError=コンペ―ネント"{0}" 入力パラメーター "{1}" 範囲エラー
errFalconBlockOptionParamError=コンペ―ネント"{0}" 選択項目パラメーター "{1}" エラー
errFalconCreateTaskNoDefId=タスクを作成エラー，テンプレート未指定
errFalconExpressionError=クリエイト値エラー。表現式"{0}"。詳細：{1}
errFalconMissingBlockInputParam=コンペ―ネント"{0}" 入力パラメーター "{1}"欠如
errFalconRecordRemoveRunning=実行中のタスクがあるため削除できない，猎鹰タスク番号：{0}
errFalconThrowPrefix=エラー：{0}
errFeiShuBadConfig=飞书ログイン設定が不完全
errFeiShuCallErr=飞书リクエスト失敗，コード
errFeiShuNoCode=codeなし
errFieldTextTooLong=フィールド "{0}.{1}" の内容長さ {2} 最大制限 {3}を超えている
errFieldTypeMismatchWithSQLDataType=業務対象 {0} フィールド値 {1} のフィールドタイプ{2} と SQL データタイプ {3} と一致しません
errFileNotDirectory=ファイルは目次ではない：{0}
errFileNotExists=存在しないファイル：{0}
errFileNotInDir=ファイル "{0}" が目次 "{1}" 中にない
errGwNoRobot=ゲートウェイにロボット "{0}"が設定されていない
errHttpFail=HTTP リクエスト失敗，HTTP 応答コード
errHttpResponseBodyEmpty=HTTP リクエスト応答なし
errInitializeScheduler=タイム設定タスク初期化に失敗！"{0}"
errInterruptedException=実行中断
errInvLayoutNoContainer=在庫詳細に容器がない{0}
errInvShort=在庫不足：{0}
errInvShort2=在庫不足，商品 {0}，出庫待ち
errLayerStartGtLayerEnd=開始層番号は終了層番号より大きい
errLockBinFailed=ロケーション "{0}" のロック失敗，すでにロックされた位置
errMissingHttpPathParam=パスパラメーター欠如：{0}
errMissingHttpQueryParam=HTTP 検索パラメーター欠如：{0}
errMissingIdField=業務対象 {1} ({0})  id フィールド欠如
errMissingParam=パラメーター欠如：{0}
errMrNoCurrentOrder=机器 "{0}" 現在実行中オーダーなし
errMrNoCurrentStep=ロボット "{0}" 現在実行中オーダーステップなし
errMrNoOrder=存在しないオーダー或いは実行済み "{0}"
errMrUpdateOrderBadStatus=オーダー更新不可。オーダー
errMySqlQueryListNotSupport=MySQLでは複数値フィールド {0}|{1}のクエリはサポートされていない
errNoBin=ロケーション "{0}"未発見
errNoBinById=ロケーション "{0}"が見つからない
errNoBinRobotArgs=アクションパラメータ欠如，ロケーション
errNoBpType=コンポーネント"{0}"が見つからない
errNoConfig=設定エラー
errNoDeviceHost=設備 アドレス/IP 未設定
errNoDeviceType=設備タイプ未設定
errNoDistrictById=倉庫エリア"{0}"が見つからない
errNoEmptyBinInDistrict=倉庫エリア内に内空いているロケーションがない
errNoFalconTaskDefById=タスクテンプレートが見つからない，テンプレートID
errNoJsonNode=存在しない JsonNode
errNoLockById=このロケーションはロックされていない "{0}"
errNoMongoDB=使用可能なMongoDBがない
errNoOrderLine=受注ラインなし，受注番号
errNoRoutePath={0} から{1}まで到着可能パスなし
errNoScriptFunctionProvided=脚本関数未指定
errNoSqlDb=SQL データりソースが利用できない
errNoSuchContainerById=容器 "{0}"が見つからない
errNoSuchContainerTypeById=容器タイプ "{0}"が見つからない
errNoSuchDistrictById=倉庫エリア"{0}"が見つからない
errNoSuchEntity=業務対象が見つからない "{0}"
errNoSuchScriptFunction=脚本関数 "{0}"が見つからない
errNoSuchUserById=ユーザーが見つからない。id
errNoTaskDefByLabel=タスクテンプレートが見つからない，テンプレート名
errNoUiDir=インターフェースファイル目次が見つからない：{0}
errNoUpOrderConfig=伝票の上位設定が見つからない。上位伝票
errNoUploadedFile=アップロードファイルはリストフィールド中に保管："{0}"
errNoUserUsername=ユーザーが見つからない。ユーザー名
errNoWidthTooSmall=番号の幅が小さすぎる
errNonDecimal=業務対象 {0} のフィールド値 {1} は小数ではない
errNonInteger=業務対象 {0} 的フィールド値 {1} は整数ではない
errNumOverflow=値 {0} が数字タイプ範囲 {1}~{2}を超えている
errOrderNoPushConfig=伝票の下位設定が見つからない：{0}
errParseJsonFile=JSON ファイル分析失敗
errParseJsonString=JSON 文字列分析失敗，値は：{0}
errPasswordNotMatch=パスワードエラー
errPwdErrExceedLimit=パスワードの入力ミスが制限を超えた，管理者にリセットを依頼するか {0} 秒後に再試行してください
errPwdExpired=パスワードが期限切れ，管理者にリセットを依頼してください
errPwdLengthExceedLimit=パスワード長さが要件を満たしていない，要求は {0} から {1} 位
errPwdStrengthNotAllow=パスワード安全強度が要件を満たしていない，現在のパスワード強度は：{0}，要求される強度は{1}
errRecoverBadExternalCallAsync=キャンセルまたは終了した非同期呼び出しのみ再試行可能
errRefFieldNoRefEntity=フィールド "{0}" 未設定 "業務対象引用"
errRetryFailedRobotButOrderNotExecuting=故障ロボット再起動，現在オーダー {0} ステータスは実行中ではなく {1}
errRobotNotFailed=ロボット "{0}" 故障なし
errRowStartGtRowEnd=開始行番号は終了行番号より大きい
errScriptBadReturnNull=脚本関数 {0} フィードバック値エラー，入力必須
errScriptEntityRw=脚本業務対象読み書きエラー：{0}
errScriptExt=脚本エラー：{0}
errScriptReturnNotString=脚本関数 "{0}" のフィードバック値返回値が文字列ではない：{1}
errSetToStatementInvalidJavaClass=setToStatement がサポートしないパラメータータイプ "{0}"
errSignInNoPassword=パスワード必要
errSimpleScriptBp=簡易脚本実行失敗：{0}
errSingletonEntityBadOp=単一業務対象 "{0}" は "{1}"の呼び出しができない
errSqlEmptyWhereNotAllowed=全体更新禁止
errSqlUniqueConstraintViolation=値の重複。値
errStepCannotBeEmpty=手順入力必須
errSyncKeyMultiple=複数値フィールド "{0}" は業務主キーとして使用できない
errSyncKeyNotUniqInCurrent=元の業務対象リスト中，唯一キーではない："{0}"
errSyncKeyNotUniqInNew=新規業務対象リスト中，唯一キーではない："{0}"
errTakeOffContainerBadBin=ロケーション "{1}" から容器 "{0}"，を取り出す要求がありますが、システムの記録によると容器は現在庫位"{2}" にある
errTakeOffContainerBadContainer=ロケーション "{0}" から容器 "{1}"，を取り出す要求がありますが、システムの記録によると、庫位にある容器は {2}
errTakeOffContainerContainerBinAtLeastOne=倉庫口の容器下ろし、容器またはロケーションを少なくとも一つ指定してください
errTakeOffContainerNoBin=ロケーション "{1}" から容器  "{0}"，を取り出す要求がありますが、システムの記録によると容器は現在どの庫位にない
errTooLongNum=フィールド {0} の値 {1} 大きすぎる（桁数が多すぎる），桁数は  {2} 桁を超えてはいけない，整数部は{3}桁を超えてはいけない
errTypesNotEnoughForHeaders=タイプ数不足
errUnbindBinContainerBadBind=ロケーション "{0}" と容器 "{1}"，の紐付け解除を試したが、現在庫位にある容器は "{2}"である
errUnsupportedDBType=サポートしないデータベースタイプ：{0}
errUnsupportedDataType=サポートしないデータタイプ：{0}
errUnsupportedHttpRequestMethod=HTTP方法エラー "{0}"
errUserDisabled=アカウント無効
errWsNotConnected=接続されていない [Websocket {0}，状態
errWsSendFail=送信失敗 [Websocket {0}]
errWsSendTimeout=送信タイムアウト [Websocket {0}]
errWwxBadConfig=企業WeChatのログイン設定が不完全
errWwxCallErr=企業WeChatリクエスト失敗，コード
errWwxNoCode=codeなし
errWwxNotEnabled=企業WeChatが無効状態
errXlsFormulaNotSupported=セル内数式はサポートされていない
Falcon_BlockGroup_Basic=基礎コンポーネント
Falcon_BlockGroup_Bin=ロケーション
Falcon_BlockGroup_BinContainer=容器ロケーション
Falcon_BlockGroup_ConditionAndLoop=条件と循環
Falcon_BlockGroup_Container=容器
Falcon_BlockGroup_ContainerTransport=容器搬送リスト
Falcon_BlockGroup_Cq=ComplexQuery
Falcon_BlockGroup_CustomBlocks=カスタマイズコンポーネント
Falcon_BlockGroup_Entity=業務対象
Falcon_BlockGroup_Inv=在庫
Falcon_Bp_AbortTaskBp_Input_msg_description=猎鹰タスクの終了原因
Falcon_Bp_AbortTaskBp_Input_msg_label=エラー情報
Falcon_Bp_AbortTaskBp_description=猎鹰タスク終了
Falcon_Bp_AbortTaskBp_label=タスク終了
Falcon_Bp_BindBinContainerBp_Input_binId_description=ロケーション ID
Falcon_Bp_BindBinContainerBp_Input_binId_label=ロケーション番号
Falcon_Bp_BindBinContainerBp_Input_containerId_label=容器番号
Falcon_Bp_BindBinContainerBp_Input_unlockBin_description=ロック解除とロケーション同期必要
Falcon_Bp_BindBinContainerBp_Input_unlockBin_label=ロケーションロック解除
Falcon_Bp_BindBinContainerBp_Input_unlockContainer_description=ロック解除容器同期必要
Falcon_Bp_BindBinContainerBp_Input_unlockContainer_label=容器のロック解除
Falcon_Bp_BindBinContainerBp_description=ロケーションと容器の紐付け
Falcon_Bp_BindBinContainerBp_label=ロケーション容器紐付け
Falcon_Bp_BreakBp_Input_condition_description=条件が満たされると循環終了
Falcon_Bp_BreakBp_Input_condition_label=条件
Falcon_Bp_BreakBp_description=条件が満たされると異常発生、循環終了
Falcon_Bp_BreakBp_label=終了循環 Break
Falcon_Bp_CreateInvFromOrderBp_Input_bin_label=ロケーション
Falcon_Bp_CreateInvFromOrderBp_Input_container_description=容器
Falcon_Bp_CreateInvFromOrderBp_Input_container_label=容器
Falcon_Bp_CreateInvFromOrderBp_Input_copyFields_description=複数フィールドは“,”区切し，これらのフィールドと対応するデータは、在庫情報にコピーされる。
Falcon_Bp_CreateInvFromOrderBp_Input_copyFields_label=コピーフィールド
Falcon_Bp_CreateInvFromOrderBp_Input_lotNoFormat_label=ロケーション番号フォーマット
Falcon_Bp_CreateInvFromOrderBp_Input_materialFieldName_description=オーダーリスト上表示された商品フィールド名を入力してください
Falcon_Bp_CreateInvFromOrderBp_Input_materialFieldName_label=商品フィールド
Falcon_Bp_CreateInvFromOrderBp_Input_order_description=オーダーリスト対象を入力してください
Falcon_Bp_CreateInvFromOrderBp_Input_order_label=オーダーリスト
Falcon_Bp_CreateInvFromOrderBp_Input_qtyFieldName_description=オーダーリスト上表示された数量のフィールド名を入力してください
Falcon_Bp_CreateInvFromOrderBp_Input_qtyFieldName_label=数量フィールド
Falcon_Bp_CreateInvFromOrderBp_Input_state_description=デフォルト三つの格納状態：Received:受領済み；Storing:格納中；Assigned:割当済み。
Falcon_Bp_CreateInvFromOrderBp_Input_state_label=在庫状態
Falcon_Bp_CreateInvFromOrderBp_description=オーダーリスト情報に基づいて在庫情報作成
Falcon_Bp_CreateInvFromOrderBp_label=オーダーリストから在庫作成
Falcon_Bp_CreateInvFromOrderLinesBp_Input_bin_label=ロケーション
Falcon_Bp_CreateInvFromOrderLinesBp_Input_container_label=容器
Falcon_Bp_CreateInvFromOrderLinesBp_Input_copyFields_description=複数のフィールドは“,”で区切し，これらのフィールドと対応するデータを在庫情報にコピー。
Falcon_Bp_CreateInvFromOrderLinesBp_Input_copyFields_label=リストから以下のフィールドをコピー
Falcon_Bp_CreateInvFromOrderLinesBp_Input_materialFieldName_description=リスト行上の商品フィールド名を入力してください
Falcon_Bp_CreateInvFromOrderLinesBp_Input_materialFieldName_label=リスト行上の商品フィールド名
Falcon_Bp_CreateInvFromOrderLinesBp_Input_order_description=オーダーリスト対象を入力してください
Falcon_Bp_CreateInvFromOrderLinesBp_Input_order_label=オーダーリスト対象
Falcon_Bp_CreateInvFromOrderLinesBp_Input_qtyFieldName_description=リスト行上の数量フィールド名を入力してください
Falcon_Bp_CreateInvFromOrderLinesBp_Input_qtyFieldName_label=リスト行上の数量フィールド名
Falcon_Bp_CreateInvFromOrderLinesBp_Input_state_description=デフォルトの三つの格納状態：Received:格納済み；Storing:格納中；Assigned:配分済み。
Falcon_Bp_CreateInvFromOrderLinesBp_Input_state_label=格納状態
Falcon_Bp_CreateInvFromOrderLinesBp_description=リスト行の情報に基づいて在庫情報を作成
Falcon_Bp_CreateInvFromOrderLinesBp_label=オーダーリスト行から在庫作成
Falcon_Bp_CreateTraceContainerBp_Input_containerType_description=M4 記録の容器タイプを入力してください。
Falcon_Bp_CreateTraceContainerBp_Input_containerType_label=容器タイプ
Falcon_Bp_CreateTraceContainerBp_Output_containerId_description=新規追加容器番号。
Falcon_Bp_CreateTraceContainerBp_Output_containerId_label=容器番号
Falcon_Bp_CreateTraceContainerBp_description=容器追跡作成。
Falcon_Bp_CreateTraceContainerBp_label=容器追跡作成
Falcon_Bp_DelayBp_Input_timeMillis_description=遅延時間（ミリ秒）
Falcon_Bp_DelayBp_Input_timeMillis_label=時間（ミリ秒）
Falcon_Bp_DelayBp_description=後続モジュールの実行を遅延させるため、遅延時間は入力されたパラメータに基づいて決定される
Falcon_Bp_DelayBp_label=遅延
Falcon_Bp_ExpressionBp_Input_expression_description=表示式
Falcon_Bp_ExpressionBp_Input_expression_label=表示ラベル
Falcon_Bp_ExpressionBp_Output_expResult_label=表示ラベル結果Falcon_Bp_ExpressionBp_Output_expResult_description
Falcon_Bp_ExpressionBp_description=入力された式に基づいて結果を計算
Falcon_Bp_ExpressionBp_label=表示ラベルリクエスト
Falcon_Bp_FindEmptyContainerBp_Input_districtIds_description=倉庫エリア番号。必要に応じ， M4 記録の倉庫エリア番号を入力してください。
Falcon_Bp_FindEmptyContainerBp_Input_districtIds_label=倉庫エリアId
Falcon_Bp_FindEmptyContainerBp_Input_keepTrying_description=使用禁止：(デフォルト)1回のみ検索；有効：空の容器が見つかるまで繰り返し検索
Falcon_Bp_FindEmptyContainerBp_Input_keepTrying_label=成功するまで再試行
Falcon_Bp_FindEmptyContainerBp_Input_sort_description=目標倉庫エリア内ロケーションの順序方法。
Falcon_Bp_FindEmptyContainerBp_Input_sort_label=順序
Falcon_Bp_FindEmptyContainerBp_Output_binId_description=目標空容器を格納するロケーション的番号。
Falcon_Bp_FindEmptyContainerBp_Output_binId_label=ロケーション番号
Falcon_Bp_FindEmptyContainerBp_Output_containerId_description=見つかった空き容器番号。
Falcon_Bp_FindEmptyContainerBp_Output_containerId_label=容器番号
Falcon_Bp_FindEmptyContainerBp_Output_found_description=true:見つかった；false:見つからなかった。
Falcon_Bp_FindEmptyContainerBp_Output_found_label=空の容器が見つかったかどうか
Falcon_Bp_FindEmptyContainerBp_description=要望ルールに基づき、目標の倉庫エリアから空の容器を探してロック
Falcon_Bp_FindEmptyContainerBp_label=空き容器を見つける
Falcon_Bp_FindFieldValueByIdBp_Input_entityName_label=業務対象名
Falcon_Bp_FindFieldValueByIdBp_Input_field_description=検索フィールドの指定
Falcon_Bp_FindFieldValueByIdBp_Input_field_label=フィールド
Falcon_Bp_FindFieldValueByIdBp_Input_id_label=業務対象 id
Falcon_Bp_FindFieldValueByIdBp_Output_found_description=業務対象実体が存在するかどうか
Falcon_Bp_FindFieldValueByIdBp_Output_found_label=業務対象存在
Falcon_Bp_FindFieldValueByIdBp_Output_value_description=フィールド値検索
Falcon_Bp_FindFieldValueByIdBp_Output_value_label=値
Falcon_Bp_FindFieldValueByIdBp_description=id に基づいて業務対象実体の指定フィールド検索
Falcon_Bp_FindFieldValueByIdBp_label=根据 id 查找業務対象的フィールド
Falcon_Bp_FindNotOccupiedBinBp_Input_cq_description=空いているロケーションを探す条件
Falcon_Bp_FindNotOccupiedBinBp_Input_cq_label=条件
Falcon_Bp_FindNotOccupiedBinBp_Input_districtIds_label=倉庫エリアリスト
Falcon_Bp_FindNotOccupiedBinBp_Input_keepTrying_description=trueの場合は成功するまで繰り返し空庫位を探し、falseの場合は1回だけ探す。
Falcon_Bp_FindNotOccupiedBinBp_Input_keepTrying_label=成功するまで繰り返す
Falcon_Bp_FindNotOccupiedBinBp_Input_sort_description=順序規則
Falcon_Bp_FindNotOccupiedBinBp_Input_sort_label=順序
Falcon_Bp_FindNotOccupiedBinBp_Output_binId_description=見つかったロケーション ID
Falcon_Bp_FindNotOccupiedBinBp_Output_binId_label=ロケーション ID
Falcon_Bp_FindNotOccupiedBinBp_Output_found_description=true は見つかったこと，false は見つからないこと
Falcon_Bp_FindNotOccupiedBinBp_Output_found_label=見つかったかどうか
Falcon_Bp_FindNotOccupiedBinBp_description=倉庫内の空いている位置検索
Falcon_Bp_FindNotOccupiedBinBp_label=空いているロケーション検索
Falcon_Bp_FindOneBp_Input_entityName_label=実体
Falcon_Bp_FindOneBp_Input_field_label=フィールド
Falcon_Bp_FindOneBp_Input_value_label=Value
Falcon_Bp_FindOneBp_label=単一記録検索
Falcon_Bp_FindOneEntityByIdBp_Input_entityName_description=業務対象名
Falcon_Bp_FindOneEntityByIdBp_Input_entityName_label=業務対象名
Falcon_Bp_FindOneEntityByIdBp_Input_id_description=業務対象実体 id
Falcon_Bp_FindOneEntityByIdBp_Input_id_label=業務対象 id
Falcon_Bp_FindOneEntityByIdBp_Output_ev_description=業務対象実体详情
Falcon_Bp_FindOneEntityByIdBp_Output_ev_label=業務対象
Falcon_Bp_FindOneEntityByIdBp_Output_found_description=見つかったかどうか
Falcon_Bp_FindOneEntityByIdBp_Output_found_label=見つかったかどうか
Falcon_Bp_FindOneEntityByIdBp_description=IDによる業務対象実体の検索
Falcon_Bp_FindOneEntityByIdBp_label=根据 id 查找業務対象
Falcon_Bp_GetBinContainerBp_Input_binId_description=ロケーション ID
Falcon_Bp_GetBinContainerBp_Input_binId_label=ロケーション番号
Falcon_Bp_GetBinContainerBp_Output_binEmpty_description=容器があるかどうか，true は空欄
Falcon_Bp_GetBinContainerBp_Output_binEmpty_label=ロケーションがあるかどうか
Falcon_Bp_GetBinContainerBp_Output_containerId_description=容器番号
Falcon_Bp_GetBinContainerBp_Output_containerId_label=容器番号
Falcon_Bp_GetBinContainerBp_description=ロケーション上の容器取得，前提は前のロケーションと容器が紐付けられること。取得できない場合は，ロケーション ID が nullになる。
Falcon_Bp_GetBinContainerBp_label=ロケーション上の容器取得
Falcon_Bp_GetContainerBinBp_Input_containerId_description=容器番号
Falcon_Bp_GetContainerBinBp_Input_containerId_label=容器番号
Falcon_Bp_GetContainerBinBp_Output_binId_description=ロケーション番号
Falcon_Bp_GetContainerBinBp_Output_binId_label=ロケーション番号
Falcon_Bp_GetContainerBinBp_Output_found_description=見つかったかどうか，true は見つかった
Falcon_Bp_GetContainerBinBp_Output_found_label=見つかったかどうか
Falcon_Bp_GetContainerBinBp_description=容器が格納されたロケーション取得，前提は前のロケーションと容器が紐付けられること。取得できない場合は，ロケーション ID が nullになる。
Falcon_Bp_GetContainerBinBp_label=容器が格納されているロケーションを取得
Falcon_Bp_GetContainerInvBp_Input_containerId_description=M4で記録された容器番号を入力してください
Falcon_Bp_GetContainerInvBp_Input_containerId_label=容器番号
Falcon_Bp_GetContainerInvBp_Output_found_description=true:見つけた；false:見つからない。
Falcon_Bp_GetContainerInvBp_Output_found_label=見つかったかどうか
Falcon_Bp_GetContainerInvBp_Output_inv_description=在庫明細リスト。
Falcon_Bp_GetContainerInvBp_Output_inv_label=在庫明細リスト
Falcon_Bp_GetContainerInvBp_description=容器番号に基づいて対応する在庫詳細を取得
Falcon_Bp_GetContainerInvBp_label=容器内在庫明細取得
Falcon_Bp_GetContainerTypeStoreDistrictsBp_Input_containerId_description=M4 記録の容器番号を入力してください。
Falcon_Bp_GetContainerTypeStoreDistrictsBp_Input_containerId_label=容器番号
Falcon_Bp_GetContainerTypeStoreDistrictsBp_Output_storeDistricts_description=倉庫エリア名称集計，配列。
Falcon_Bp_GetContainerTypeStoreDistrictsBp_Output_storeDistricts_label=格納エリア
Falcon_Bp_GetContainerTypeStoreDistrictsBp_description=容器番号に基づいて、この種類の容器を格納できる倉庫エリアの名前を照会
Falcon_Bp_GetContainerTypeStoreDistrictsBp_label=容器タイプ的格納エリア検索
Falcon_Bp_IfBp_Input_condition_description=判断条件
Falcon_Bp_IfBp_Input_condition_label=条件
Falcon_Bp_IfBp_description=条件判断、条件が満たされた時のみサブモジュールが実行される
Falcon_Bp_IfBp_label=IF条件
Falcon_Bp_IfElseBp_Input_condition_description=判断条件
Falcon_Bp_IfElseBp_Input_condition_label=条件
Falcon_Bp_IfElseBp_description=条件判断、条件が満たされた場合にtrue のサブモジュールを実行し、満たされない場合にfalse のサブモジュールを実行
Falcon_Bp_IfElseBp_label=IF ELSE条件
Falcon_Bp_IterateListBp_Context_index_description=検索
Falcon_Bp_IterateListBp_Context_item_description=単一データ情報
Falcon_Bp_IterateListBp_Input_list_description=反復リスト
Falcon_Bp_IterateListBp_Input_list_label=リスト
Falcon_Bp_IterateListBp_Input_parallel_description=並行処理かどうかの判定
Falcon_Bp_IterateListBp_Input_parallel_label=並行
Falcon_Bp_IterateListBp_description=配列を反復処理し、並行処理にも対応
Falcon_Bp_IterateListBp_label=配列の反復処理
Falcon_Bp_KeepTryingLockBinBp_Input_binId_description=ロケーション ID
Falcon_Bp_KeepTryingLockBinBp_Input_binId_label=ロケーション
Falcon_Bp_KeepTryingLockBinBp_Input_reason_description=ロック原因
Falcon_Bp_KeepTryingLockBinBp_Input_reason_label=原因
Falcon_Bp_KeepTryingLockBinBp_description=ロケーションロック成功まで繰り返し操作
Falcon_Bp_KeepTryingLockBinBp_label=ロケーションロック成功まで繰り返し操作
Falcon_Bp_KeepTryingLockNotOccupiedBinBp_Input_binId_description=ロケーション ID
Falcon_Bp_KeepTryingLockNotOccupiedBinBp_Input_binId_label=ロケーション番号
Falcon_Bp_KeepTryingLockNotOccupiedBinBp_description=ロケーションな、ロック待ち
Falcon_Bp_KeepTryingLockNotOccupiedBinBp_label=ロケーションなし、ロック待ち
Falcon_Bp_LockBinOnceBp_Input_binId_label=ロケーション番号
Falcon_Bp_LockBinOnceBp_Input_notFoundToAborted_description=失敗した場合、タスクを終了させる必要があるかどうか、true は必要
Falcon_Bp_LockBinOnceBp_Input_notFoundToAborted_label=終了タスクが見つからない
Falcon_Bp_LockBinOnceBp_Input_notFoundToFault_description=失敗した場合、タスクを故障させる必要があるかどうか。true は必要
Falcon_Bp_LockBinOnceBp_Input_notFoundToFault_label=タスク故障が見つからない
Falcon_Bp_LockBinOnceBp_Output_ok_label=ロックが成功したかどうか
Falcon_Bp_LockBinOnceBp_description=ロックされていないラックを一度ロックし、成功した場合は true を返し、失敗した場合は false を返す
Falcon_Bp_LockBinOnceBp_label=ロックされていないロケーションをロック
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_newState_Option_Cancelled=キャンセル
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_newState_Option_Done=完了
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_newState_Option_Failed=失敗
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_newState_description=更新要望状態
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_newState_label=更新状態
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_orderId_description=容器搬送リスト番号，唯一標識
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_orderId_label=リスト番号
Falcon_Bp_MarkContainerTransportOrderDoneBp_description=リスト番号で容器搬送リスト状態を更新
Falcon_Bp_MarkContainerTransportOrderDoneBp_label=容器搬送リスト完了標記
Falcon_Bp_MoveInvByContainerBp_Input_leafContainerId_description=最も内側の容器番号を入力してください。
Falcon_Bp_MoveInvByContainerBp_Input_leafContainerId_label=最も内側の容器
Falcon_Bp_MoveInvByContainerBp_Input_state_description=在庫状態を指定された在庫状態に変更
Falcon_Bp_MoveInvByContainerBp_Input_state_label=在庫状態指定
Falcon_Bp_MoveInvByContainerBp_Input_toBinId_description=最も内側の容器を配置するロケーション番号。
Falcon_Bp_MoveInvByContainerBp_Input_toBinId_label=終点ロケーション
Falcon_Bp_MoveInvByContainerBp_description=最も内側の容器情報を別のロケーションに紐付ける。
Falcon_Bp_MoveInvByContainerBp_label=容器で在庫移動
Falcon_Bp_PadIntStrBp_Input_numLength_description=数値を文字列に変換した後、指定された長さが元の長さより短い場合、指定された長さは無視、そうでない場合は0で補足
Falcon_Bp_PadIntStrBp_Input_numLength_label=数字長さ
Falcon_Bp_PadIntStrBp_Input_num_description=変換必要数字
Falcon_Bp_PadIntStrBp_Input_num_label=数字
Falcon_Bp_PadIntStrBp_Input_prefix_description=変換後の文字列のプレフィックス。
Falcon_Bp_PadIntStrBp_Input_prefix_label=プレフィックス
Falcon_Bp_PadIntStrBp_Input_suffix_description=変換後の文字列のサフィックス
Falcon_Bp_PadIntStrBp_Input_suffix_label=サフィックス
Falcon_Bp_PadIntStrBp_Output_numStr_description=数字変換後の文字列
Falcon_Bp_PadIntStrBp_Output_numStr_label=数字変換後の文字列
Falcon_Bp_PadIntStrBp_description=条件に基づいて， 数字を指定長さの文字列に変換，不足部分は0で補足
Falcon_Bp_PadIntStrBp_label=数字を指定長さの文字列に変換
Falcon_Bp_ParallelFlowBp_description=並行実行
Falcon_Bp_ParallelFlowBp_label=並行実行
Falcon_Bp_PrintBp_Input_message_description=印刷必要情報
Falcon_Bp_PrintBp_Input_message_label=情報
Falcon_Bp_PrintBp_description=印刷
Falcon_Bp_PrintBp_label=印刷
Falcon_Bp_ReduceBinInvFromOrderBp_Input_binField_description=リスト上に表示されたロケーションのフを入力してください
Falcon_Bp_ReduceBinInvFromOrderBp_Input_binField_label=ロケーションフィールド
Falcon_Bp_ReduceBinInvFromOrderBp_Input_ev_description=オーダーリスト情報を入力してください
Falcon_Bp_ReduceBinInvFromOrderBp_Input_ev_label=オーダーリスト
Falcon_Bp_ReduceBinInvFromOrderBp_Input_materialField_description=リスト上に表示された商品のフィールド名を入力してください
Falcon_Bp_ReduceBinInvFromOrderBp_Input_materialField_label=商品フィールド
Falcon_Bp_ReduceBinInvFromOrderBp_Input_outboundOrderIdField_label=出庫リスト番号
Falcon_Bp_ReduceBinInvFromOrderBp_Input_pickOrderIdField_label=ピッキングリスト番号
Falcon_Bp_ReduceBinInvFromOrderBp_Input_qtyField_description=リスト上に表示された数量のフィールド名を入力してください
Falcon_Bp_ReduceBinInvFromOrderBp_Input_qtyField_label=数量フィールド
Falcon_Bp_ReduceBinInvFromOrderBp_Input_subContainerIdField_label=グリッド
Falcon_Bp_ReduceBinInvFromOrderBp_description=オーダーリストに基づいてロケーションから在庫削除
Falcon_Bp_ReduceBinInvFromOrderBp_label=オーダーリスト基づいてロケーションから在庫削除
Falcon_Bp_RemoveInvByContainerBp_Input_containerId_description=M4 記録容器番号を入力してください。
Falcon_Bp_RemoveInvByContainerBp_Input_containerId_label=容器番号
Falcon_Bp_RemoveInvByContainerBp_Output_count_description=削除された在庫明細数量。
Falcon_Bp_RemoveInvByContainerBp_Output_count_label=明細数量
Falcon_Bp_RemoveInvByContainerBp_description=容器番号に基づいて対応する在庫明細を削除。
Falcon_Bp_RemoveInvByContainerBp_label=容器別在庫明細削除
Falcon_Bp_RemoveTraceContainerBp_Input_containerId_description=M4 記録の容器追跡番号を入力してください。
Falcon_Bp_RemoveTraceContainerBp_Input_containerId_label=容器番号
Falcon_Bp_RemoveTraceContainerBp_description=容器追跡削除。
Falcon_Bp_RemoveTraceContainerBp_label=容器追跡削除
Falcon_Bp_RepeatNumBp_Child_default_label=コンポーネント
Falcon_Bp_RepeatNumBp_Context_index_description=シリアル番号
Falcon_Bp_RepeatNumBp_Context_index_label=シリアル番号
Falcon_Bp_RepeatNumBp_Input_num_description=サブコンポーネント重複実行回数
Falcon_Bp_RepeatNumBp_Input_num_label=実行回数
Falcon_Bp_RepeatNumBp_description=サブコンポーネント重複実行
Falcon_Bp_RepeatNumBp_label=重複実行 Repeat
Falcon_Bp_SerialFlowBp_description=シリアル実行
Falcon_Bp_SerialFlowBp_label=シリアル実行
Falcon_Bp_SetBinEmptyBp_Input_binId_description=ロケーション ID
Falcon_Bp_SetBinEmptyBp_Input_binId_label=ロケーション番号
Falcon_Bp_SetBinEmptyBp_Input_noContainer_description=true は該当ロケーションの容器なし設定
Falcon_Bp_SetBinEmptyBp_Input_noContainer_label=容器なし
Falcon_Bp_SetBinEmptyBp_Input_notOccupied_description=true はロケーション未使用設定
Falcon_Bp_SetBinEmptyBp_Input_notOccupied_label=未使用
Falcon_Bp_SetBinEmptyBp_Input_unlock_label=ロック解除
Falcon_Bp_SetBinEmptyBp_Input_unlock_label_description=true は該当ロケーションロック解除設定
Falcon_Bp_SetBinEmptyBp_description=ロケーション未使用（放棄）設定
Falcon_Bp_SetBinEmptyBp_label=ロケーション未使用（放棄）設定
Falcon_Bp_SetBinNotOccupiedBp_Input_binId_description=ロケーション ID
Falcon_Bp_SetBinNotOccupiedBp_Input_binId_label=ロケーション番号
Falcon_Bp_SetBinNotOccupiedBp_Input_unlockAfter_description=true は未使用ロック解除ロケーション設定，falseは未使用ロック解除しないロケーション設定
Falcon_Bp_SetBinNotOccupiedBp_Input_unlockAfter_label=ロック解除
Falcon_Bp_SetBinNotOccupiedBp_description=ロケーション未使用設定
Falcon_Bp_SetBinNotOccupiedBp_label=ロケーション未使用設定
Falcon_Bp_SetBinUnLockBp_Input_binId_label=ロケーション番号
Falcon_Bp_SetBinUnLockBp_Input_requireLock_description=このロケーションがロックされたロケーションであることを要求する、ロックされていなければ異常終了
Falcon_Bp_SetBinUnLockBp_Input_requireLock_label=このロケーションがロックされたロケーションであることを要求
Falcon_Bp_SetBinUnLockBp_description=指定ロケーションのロック解除
Falcon_Bp_SetBinUnLockBp_label=ロケーションのロック解除
Falcon_Bp_SetTaskVariableBp_Input_varName_description=タスク变量的名称
Falcon_Bp_SetTaskVariableBp_Input_varName_label=タスク変数の名前
Falcon_Bp_SetTaskVariableBp_Input_varValue_description=タスク変数値
Falcon_Bp_SetTaskVariableBp_Input_varValue_label=変数の値
Falcon_Bp_SetTaskVariableBp_description=设置タスク变量
Falcon_Bp_SetTaskVariableBp_label=タスク変数設定
Falcon_Bp_SimpleScriptBp_Input_scriptStr_label=脚本りソースコードコード
Falcon_Bp_SimpleScriptBp_Output_scriptOut_description=脚本実行戻り値
Falcon_Bp_SimpleScriptBp_Output_scriptOut_label=戻り値
Falcon_Bp_SimpleScriptBp_label=シンプル脚本
Falcon_Bp_SuiBp_Input_config_description=操作ユーザー設定
Falcon_Bp_SuiBp_Input_config_label=設定
Falcon_Bp_SuiBp_Output_button_description=ユーザークリックボタン
Falcon_Bp_SuiBp_Output_button_label=ユーザークリックボタン
Falcon_Bp_SuiBp_Output_input_description=ユーザー入力パラメーター
Falcon_Bp_SuiBp_Output_input_label=ユーザー入力
Falcon_Bp_SuiBp_description=ユーザー操作待ち
Falcon_Bp_SuiBp_label=ユーザー介入
Falcon_Bp_ThrowBp_Input_condition_description=エラー発信したかどうか
Falcon_Bp_ThrowBp_Input_condition_label=条件
Falcon_Bp_ThrowBp_Input_errMsg_description=エラー情報発信
Falcon_Bp_ThrowBp_Input_errMsg_label=エラー情報
Falcon_Bp_ThrowBp_description=エラー発信
Falcon_Bp_ThrowBp_label=エラー発信
Falcon_Bp_TimestampBp_Input_formatDate_description=日付形式指定
Falcon_Bp_TimestampBp_Input_formatDate_label=日付形式
Falcon_Bp_TimestampBp_Output_timestamp_description=指定形式の日付拡張子文字列に戻る
Falcon_Bp_TimestampBp_Output_timestamp_label=現在タイムスタンプ
Falcon_Bp_TimestampBp_description=指定形式の現在時間文字列に戻る
Falcon_Bp_TimestampBp_label=現在の時間にバック
Falcon_Bp_TriggerTaskEventBp_Input_eventData_description=イベントデータ
Falcon_Bp_TriggerTaskEventBp_Input_eventData_label=イベントデータ
Falcon_Bp_TriggerTaskEventBp_Input_eventName_description=イベント名
Falcon_Bp_TriggerTaskEventBp_Input_eventName_label=イベントデータ
Falcon_Bp_TriggerTaskEventBp_description=イベント名
Falcon_Bp_TriggerTaskEventBp_label=タスクイベントのトリガー
Falcon_Bp_TryCatchBp_Input_ignoreAbort_description=エラーキャンセル
Falcon_Bp_TryCatchBp_Input_ignoreAbort_label=エラーキャンセル
Falcon_Bp_TryCatchBp_Input_swallowError_description=エラースロー
Falcon_Bp_TryCatchBp_Input_swallowError_label=エラースロー
Falcon_Bp_TryCatchBp_description=エラー情報獲得
Falcon_Bp_TryCatchBp_label=try-catch
Falcon_Bp_UnbindBinContainerBp_Input_binId_description=ロケーション ID
Falcon_Bp_UnbindBinContainerBp_Input_binId_label=ロケーション番号
Falcon_Bp_UnbindBinContainerBp_Input_containerId_description=容器番号
Falcon_Bp_UnbindBinContainerBp_Input_containerId_label=容器番号
Falcon_Bp_UnbindBinContainerBp_Input_unlockBin_description=ロケーションをロック解除するかどうか
Falcon_Bp_UnbindBinContainerBp_Input_unlockBin_label=ロケーションロック解除
Falcon_Bp_UnbindBinContainerBp_Input_unlockContainer_description=容器をロック解除するかどうか
Falcon_Bp_UnbindBinContainerBp_Input_unlockContainer_label=容器のロック解除
Falcon_Bp_UnbindBinContainerBp_description=ロケーションと容器紐付け解除
Falcon_Bp_UnbindBinContainerBp_label=ロケーション容器紐付け解除
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_Option_fromBin=始点ロケーション
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_Option_loaded=荷役済み
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_Option_robotName=ロボット
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_Option_status=状態
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_Option_toBin=終点ロケーション
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_Option_unloaded=荷卸し済み
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_description=更新必要のフィールド名
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_label=フィールド
Falcon_Bp_UpdateContainerTransportOrderBp_Input_fv_description=更新要望フィールド値
Falcon_Bp_UpdateContainerTransportOrderBp_Input_fv_label=フィールド値
Falcon_Bp_UpdateContainerTransportOrderBp_Input_orderId_description=容器搬送リスト番号，唯一標識
Falcon_Bp_UpdateContainerTransportOrderBp_Input_orderId_label=リスト番号
Falcon_Bp_UpdateContainerTransportOrderBp_description=容器搬送リスト指定フィールド値更新
Falcon_Bp_UpdateContainerTransportOrderBp_label=容器搬送リスト更新
Falcon_Bp_UpdateEntityFieldBp_Input_entityName_description=業務対象名
Falcon_Bp_UpdateEntityFieldBp_Input_entityName_label=業務対象名
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_Any=任意タイプ（Any）
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_Boolean=Booleanタイプ（Boolean）
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_Double=ダブル精度浮動小数点タイプ（Double）
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_JSONArray=JSON 配列（JSONArray）
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_JSONObject=JSON 対象（JSONObject）
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_Long=長整数タイプ（Long）
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_String=文字列タイプ（String）
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_description=検索必要のフィールドタイプ
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_label=検索フィールドタイプ
Falcon_Bp_UpdateEntityFieldBp_Input_queryField_description=検索必要フィールド名
Falcon_Bp_UpdateEntityFieldBp_Input_queryField_label=検索フィールド
Falcon_Bp_UpdateEntityFieldBp_Input_queryValue_description=検索必要のフィールド値
Falcon_Bp_UpdateEntityFieldBp_Input_queryValue_label=検索値
Falcon_Bp_UpdateEntityFieldBp_Input_setToNull_label=nullに設定
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_Any=任意タイプ（Any）
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_Boolean=Booleanタイプ（Boolean）
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_Double=ダブル精度浮動小数点タイプ（Double）
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_JSONArray=JSON 配列（JSONArray）
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_JSONObject=JSON 対象（JSONObject）
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_Long=長整数タイプ（Long）
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_String=文字列タイプ（String）
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_description=フィールドのタイプ更新
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_label=フィールドタイプ更新
Falcon_Bp_UpdateEntityFieldBp_Input_updateField_description=更新必要のフィールド
Falcon_Bp_UpdateEntityFieldBp_Input_updateField_label=フィールド更新
Falcon_Bp_UpdateEntityFieldBp_Input_updateMany_description=ロット更新が必要かどうか
Falcon_Bp_UpdateEntityFieldBp_Input_updateMany_label=ロット更新
Falcon_Bp_UpdateEntityFieldBp_Input_updateValue_description=フィールド更新後の値
Falcon_Bp_UpdateEntityFieldBp_Input_updateValue_label=更新値
Falcon_Bp_UpdateEntityFieldBp_description=条件に基づいて業務対象のフィールド更新
Falcon_Bp_UpdateEntityFieldBp_label=条件に基づいて業務対象シングルフィールド更新
Falcon_Bp_UpdateOneEntityByIdBp_Input_entityName_description=業務対象名称
Falcon_Bp_UpdateOneEntityByIdBp_Input_entityName_label=業務対象名称
Falcon_Bp_UpdateOneEntityByIdBp_Input_fieldName_description=フィールド名更新
Falcon_Bp_UpdateOneEntityByIdBp_Input_fieldName_label=フィールド名
Falcon_Bp_UpdateOneEntityByIdBp_Input_fv_description=更新後のフィールド値
Falcon_Bp_UpdateOneEntityByIdBp_Input_fv_label=フィールド値
Falcon_Bp_UpdateOneEntityByIdBp_Input_id_description=実体 Id
Falcon_Bp_UpdateOneEntityByIdBp_Input_id_label=Id
Falcon_Bp_UpdateOneEntityByIdBp_Input_setToNull_label=nullに設定
Falcon_Bp_UpdateOneEntityByIdBp_description=idに基づいて 業務対象単一フィールド更新
Falcon_Bp_UpdateOneEntityByIdBp_label=idに基づいて 業務対象単一フィールド検索
Falcon_Bp_WebSocketBp_Input_eventName_description=WebSocket 通信表示
Falcon_Bp_WebSocketBp_Input_eventName_label=イベント発信
Falcon_Bp_WebSocketBp_Input_message_description=内容発信
Falcon_Bp_WebSocketBp_Input_message_label=内容発信
Falcon_Bp_WebSocketBp_description=WebSocket ユーザー側に情報発信
Falcon_Bp_WebSocketBp_label=WebSocket 情報発信
Falcon_Bp_WhileBp_Input_condition_label=条件
Falcon_Bp_WhileBp_description=条件が満たされた場合、サブモジュールが継続実行される
Falcon_Bp_WhileBp_label=循環実行 While
ModbusDeviceNotInit=Modbus 設備 "{0}" 未初期化
wcs_err_TomNoUrl=配車 "{0}" 未設定 URL
wcs_err_TomNotFound=配車未設定 "{0}"
wcs_err_tom_BlockError=ブロック実行失敗、注文番号
wcs_err_tom_BlockNotFound=調整でブロックが見つからない、注文番号
wcs_err_tom_Completed_No_Path=配車エラー，既に封印されており回復できない。理由：到達可能なパスが見つからない
wcs_err_tom_ConnectError=配車サーバー接続失敗：{0}
wcs_err_tom_HttpError=調整インターフェースのその他のエラー：[{0}] {1}
wcs_err_tom_HttpError404=調整インターフェースで404エラー
wcs_err_tom_IOError=リクエスト配車，IO エラー：{0}，リクエストアドレス：{1}
wcs_err_tom_OtherError=リクエスト配車，その他エラー：{0}，リクエストID：{1}
wcs_err_tom_TomError=配車エラー，エラーコード
wcs_err_tom_TomResponseEmpty=調整のリクエストテキストが空
errDeviceDisabled=停止中の設備 {0}
errNoDevice=存在しない設備：{0}
Falcon_BlockGroup_PLC=PLC
Falcon_Bp_ModbusReadBp_Input_address_description=Modbus アドレス
Falcon_Bp_ModbusReadBp_Input_address_label=アドレス
Falcon_Bp_ModbusReadBp_Input_code_Option_1=0x01 読取りコイル状態
Falcon_Bp_ModbusReadBp_Input_code_Option_2=0x02 読取り入力状態
Falcon_Bp_ModbusReadBp_Input_code_Option_3=0x03 読取りプロセッサレジスタ維持
Falcon_Bp_ModbusReadBp_Input_code_Option_4=0x04 読取り入力プロセッサレジスタ
Falcon_Bp_ModbusReadBp_Input_code_description=Modbus 機能コード
Falcon_Bp_ModbusReadBp_Input_code_label=機能コード
Falcon_Bp_ModbusReadBp_Input_deviceName_description=設備名，事前にPLCデバイス設定メニューで構成する必要がある
Falcon_Bp_ModbusReadBp_Input_deviceName_label=設備名
Falcon_Bp_ModbusReadBp_Input_maxRetry_description=最大再試行回数，デフォルトでPLC 設備設定メニュー中設定できる最大再試行回数
Falcon_Bp_ModbusReadBp_Input_maxRetry_label=最大再試行回数
Falcon_Bp_ModbusReadBp_Input_retryDelay_description=再試行間隔、デフォルトはPLCデバイス設定メニューで設定された再試行間隔
Falcon_Bp_ModbusReadBp_Input_retryDelay_label=再試行待ち時間（ミリ秒）
Falcon_Bp_ModbusReadBp_Input_slaveId_description=Modbus 設備id，デフォルトは0
Falcon_Bp_ModbusReadBp_Input_slaveId_label=Slave ID
Falcon_Bp_ModbusReadBp_Output_value_description=対応アドレス値読取り
Falcon_Bp_ModbusReadBp_description=読取り Modbus の値
Falcon_Bp_ModbusReadBp_label=Modbus 読取り
Falcon_Bp_ModbusReadEqBp_Input_address_description=Modbus アドレス
Falcon_Bp_ModbusReadEqBp_Input_address_label=アドレス
Falcon_Bp_ModbusReadEqBp_Input_code_Option_1=0x01 読取りコイル状態
Falcon_Bp_ModbusReadEqBp_Input_code_Option_2=0x02 読取り入力状態
Falcon_Bp_ModbusReadEqBp_Input_code_Option_3=0x03 読取りプロセッサレジスタ維持
Falcon_Bp_ModbusReadEqBp_Input_code_Option_4=0x04 読取り入力プロセッサレジスタ
Falcon_Bp_ModbusReadEqBp_Input_code_description=Modbus の機能コード
Falcon_Bp_ModbusReadEqBp_Input_code_label=機能コード
Falcon_Bp_ModbusReadEqBp_Input_deviceName_description=設備名，事前にPLCデバイス設定メニューで構成する必要がある
Falcon_Bp_ModbusReadEqBp_Input_deviceName_label=設備名
Falcon_Bp_ModbusReadEqBp_Input_maxRetry_description=1.読み取り失敗の場合、最大自動再試行回数、デフォルトはPLCデバイス設定メニューで設定された最大再試行回数
Falcon_Bp_ModbusReadEqBp_Input_maxRetry_label=失敗回数制限
Falcon_Bp_ModbusReadEqBp_Input_readDelay_description=2回の読み取り間の間隔、デフォルトは1ｓ
Falcon_Bp_ModbusReadEqBp_Input_readDelay_label=読取り間隔
Falcon_Bp_ModbusReadEqBp_Input_readLimit_description=11.読み取った値が期待値と一致しない回数の制限、デフォルトは-1。未入力または-1は無制限
Falcon_Bp_ModbusReadEqBp_Input_readLimit_label=読み取り値が期待値と一致しない回数の制限
Falcon_Bp_ModbusReadEqBp_Input_retryDelay_description=3.読み取り失敗の場合の再試行間隔、デフォルトはPLCデバイス設定メニューで設定された再試行間隔
Falcon_Bp_ModbusReadEqBp_Input_retryDelay_label=失敗再試行待ち時間（ミリ秒）
Falcon_Bp_ModbusReadEqBp_Input_slaveId_description=Modbus 設備id，デフォルトは 0
Falcon_Bp_ModbusReadEqBp_Input_slaveId_label=Slave ID
Falcon_Bp_ModbusReadEqBp_Input_targetValue_description=目標値
Falcon_Bp_ModbusReadEqBp_Input_targetValue_label=目標値
Falcon_Bp_ModbusReadEqBp_description=Modbus を読み取り、期待値に一致するまで。一致しない場合、最大再試行回数を超えるまで読み取りを続ける
Falcon_Bp_ModbusReadEqBp_label=Modbus 読取りが一致するまで
Falcon_Bp_ModbusWriteBp_Input_address_description=Modbus アドレス
Falcon_Bp_ModbusWriteBp_Input_address_label=アドレス
Falcon_Bp_ModbusWriteBp_Input_code_Option_5=0x05 単一コイル書き込み
Falcon_Bp_ModbusWriteBp_Input_code_Option_6=0x06 単一プロセッサレジスタ書き込み
Falcon_Bp_ModbusWriteBp_Input_code_description=Modbus 機能コード
Falcon_Bp_ModbusWriteBp_Input_code_label=機能コード
Falcon_Bp_ModbusWriteBp_Input_deviceName_description=設備名，事前に PLC デバイス設定メニューで設定する必要あり
Falcon_Bp_ModbusWriteBp_Input_deviceName_label=設備名
Falcon_Bp_ModbusWriteBp_Input_maxRetry_description=最大再試行回数，デフォルトは PLC 設備設定メニューで設定された最大再試行回数
Falcon_Bp_ModbusWriteBp_Input_maxRetry_label=最大再試行回数
Falcon_Bp_ModbusWriteBp_Input_retryDelay_description=再試行間隔，デフォルトは PLC 設備設定メニューで設定された再試行間隔
Falcon_Bp_ModbusWriteBp_Input_retryDelay_label=再試行待ち時間（ミリ秒）
Falcon_Bp_ModbusWriteBp_Input_slaveId_description=Modbus 設備id，デフォルトは0
Falcon_Bp_ModbusWriteBp_Input_slaveId_label=Slave ID
Falcon_Bp_ModbusWriteBp_Input_value_description=書き込み値
Falcon_Bp_ModbusWriteBp_Input_value_label=値
Falcon_Bp_ModbusWriteBp_description=Modbusに書き込み
Falcon_Bp_ModbusWriteBp_label=Modbus書き込み
Falcon_Bp_S7ReadBp_Input_bitOffset_description=ビットオフセット
Falcon_Bp_S7ReadBp_Input_bitOffset_label=バイトオフセット
Falcon_Bp_S7ReadBp_Input_blockType_description=プロトコル内容に基づいて適切なブロックタイプを選択してください
Falcon_Bp_S7ReadBp_Input_blockType_label=ブロックタイプ
Falcon_Bp_S7ReadBp_Input_byteOffset_description=バイト変移量は，アドレス番号として理解できます
Falcon_Bp_S7ReadBp_Input_byteOffset_label=バイト変移量
Falcon_Bp_S7ReadBp_Input_dataType_description=プロトコル内容に基づいて適切なデータタイプを選択してください
Falcon_Bp_S7ReadBp_Input_dataType_label=データタイプ
Falcon_Bp_S7ReadBp_Input_dbId_description=DB 番号を入力してください。プロトコル内容に基づいて正しい値を入力してください
Falcon_Bp_S7ReadBp_Input_dbId_label=dbId
Falcon_Bp_S7ReadBp_Input_deviceName_description=“PLC 設備管理”で設定した S7 デバイスの名前を入力してください
Falcon_Bp_S7ReadBp_Input_deviceName_label=設備名
Falcon_Bp_S7ReadBp_Input_maxRetry_description=最大再試行回数
Falcon_Bp_S7ReadBp_Input_maxRetry_label=最大再試行回数
Falcon_Bp_S7ReadBp_Input_retryDelay_description=再試行時間間隔，単位ミリ秒
Falcon_Bp_S7ReadBp_Input_retryDelay_label=再試行待ち時間（ミリ秒）
Falcon_Bp_S7ReadBp_Output_value_description=値
Falcon_Bp_S7ReadBp_Output_value_label=値
Falcon_Bp_S7ReadBp_description=S7 プロトコルを使用して PLC デバイスからデータを 1 回読み取り、取得した値をこのコンポーネントの出力パラメータとして設定
Falcon_Bp_S7ReadBp_label=S7 読取り
Falcon_Bp_S7ReadEqBp_Input_bitOffset_description=位変移量
Falcon_Bp_S7ReadEqBp_Input_bitOffset_label=位変移量
Falcon_Bp_S7ReadEqBp_Input_blockType_description=プロトコルに基づいて、対応するブロックタイプを選択してください。
Falcon_Bp_S7ReadEqBp_Input_blockType_label=ブロックタイプ
Falcon_Bp_S7ReadEqBp_Input_byteOffset_description=バイト変移量，可以理解为アドレス番号。
Falcon_Bp_S7ReadEqBp_Input_byteOffset_label=バイト変移量
Falcon_Bp_S7ReadEqBp_Input_dataType_description=プロトコルに基づいて、対応するデータタイプを選択してください。
Falcon_Bp_S7ReadEqBp_Input_dataType_label=データタイプ
Falcon_Bp_S7ReadEqBp_Input_dbId_description=DB 6.番号、プロトコルに基づいて対応する数値を入力してください。
Falcon_Bp_S7ReadEqBp_Input_dbId_label=dbId
Falcon_Bp_S7ReadEqBp_Input_deviceName_description=“PLC 設備管理”3.に設定されたS7デバイスの名前を入力してください。
Falcon_Bp_S7ReadEqBp_Input_deviceName_label=設備名
Falcon_Bp_S7ReadEqBp_Input_maxRetry_description=最大的再試行回数
Falcon_Bp_S7ReadEqBp_Input_maxRetry_label=最大再試行回数
Falcon_Bp_S7ReadEqBp_Input_readDelay_description=两次読取り之间的間隔，デフォルト为 1s
Falcon_Bp_S7ReadEqBp_Input_readDelay_label=読取り間隔
Falcon_Bp_S7ReadEqBp_Input_readLimit_description=读到的値与预期不同的回数制限，デフォルト値 -1。不填、-1 即不制限
Falcon_Bp_S7ReadEqBp_Input_readLimit_label=读値不符预期的回数制限
Falcon_Bp_S7ReadEqBp_Input_retryDelay_description=読取り失敗情况下，再試行間隔，デフォルト为 PLC 設備配置菜单中设置的再試行間隔
Falcon_Bp_S7ReadEqBp_Input_retryDelay_label=失敗再試行等待（ミリ秒）
Falcon_Bp_S7ReadEqBp_Input_value_description=期望値
Falcon_Bp_S7ReadEqBp_Input_value_label=値
Falcon_Bp_S7ReadEqBp_description=S7プロトコルを使用して、PLCデバイスからデータを読み取り、読み取った値が期待値と一致するまで読み続ける。
Falcon_Bp_S7ReadEqBp_label=S7 読取り値と一致するまで
Falcon_Bp_S7WriteBp_Input_bitOffset_description=ビット変移量
Falcon_Bp_S7WriteBp_Input_bitOffset_label=ビット変移量
Falcon_Bp_S7WriteBp_Input_blockType_description=请根据协议内容选择对应的块タイプ。
Falcon_Bp_S7WriteBp_Input_blockType_label=块タイプ
Falcon_Bp_S7WriteBp_Input_byteOffset_description=バイト変移量，アドレス番号として理解。
Falcon_Bp_S7WriteBp_Input_byteOffset_label=バイト変移量
Falcon_Bp_S7WriteBp_Input_dataType_description=请根据协议内容选择对应データタイプ。
Falcon_Bp_S7WriteBp_Input_dataType_label=データタイプ
Falcon_Bp_S7WriteBp_Input_dbId_description=DB 番号，请根据协议内容入力对应的数値。
Falcon_Bp_S7WriteBp_Input_dbId_label=dbId
Falcon_Bp_S7WriteBp_Input_deviceName_description=请入力“PLC 設備管理”中配置的 S7 設備的名称。
Falcon_Bp_S7WriteBp_Input_deviceName_label=設備名
Falcon_Bp_S7WriteBp_Input_maxRetry_description=最大再試行回数
Falcon_Bp_S7WriteBp_Input_maxRetry_label=最大再試行回数
Falcon_Bp_S7WriteBp_Input_retryDelay_description=再試行時間間隔，単位ミリ秒
Falcon_Bp_S7WriteBp_Input_retryDelay_label=再試行待ち時間（ミリ秒）
Falcon_Bp_S7WriteBp_Input_value_description=目標値
Falcon_Bp_S7WriteBp_Input_value_label=値
Falcon_Bp_S7WriteBp_description=通过 S7 协议向 PLC 設備写入一次目標値。
Falcon_Bp_S7WriteBp_label=S7 写入
errModbusReadEqNotMatch=ModbusReadEqBp の読取値が期待値と異なる回数が制限オーバー
errS7ReadEqNotMatch=S7ReadEqBp 读値与期望不同回数超过制限
RbkApiNoNoSupported=サポートしない API 番号 {0}
RbkClientConnectFail=RBK接続 失敗 {0}
RbkClientRequestFail=リクエスト RBK 失敗 {0}
RobotNotExistedById=ロボット "{0}" が存在しない
Falcon_BlockGroup_DirectOrder=直接運送指示書の実行
Falcon_BlockGroup_Map=地図
Falcon_BlockGroup_Ndc=NDC 配車番号
Falcon_BlockGroup_SeerTom=弊社オーダー調達
Falcon_Bp_AddTomBlockBp_Input_binTask_description=binTask
Falcon_Bp_AddTomBlockBp_Input_binTask_label=binTask
Falcon_Bp_AddTomBlockBp_Input_goodsId_description=容器番号
Falcon_Bp_AddTomBlockBp_Input_goodsId_label=容器番号
Falcon_Bp_AddTomBlockBp_Input_location_description=目的地名，ステーション名，或いはロケーション
Falcon_Bp_AddTomBlockBp_Input_location_label=ステーション
Falcon_Bp_AddTomBlockBp_Input_operation_description=実行機構動作
Falcon_Bp_AddTomBlockBp_Input_operation_label=動作（operation）
Falcon_Bp_AddTomBlockBp_Input_orderId_description=【第2世代スケジュール運搬作成】ブロック生成された“運送番号”
Falcon_Bp_AddTomBlockBp_Input_orderId_label=運送番号
Falcon_Bp_AddTomBlockBp_Input_tomId_description=《ロボットアプリケーション》ページでの“第2世代配車”のシーン名
Falcon_Bp_AddTomBlockBp_Input_tomId_label=配車 ID
Falcon_Bp_AddTomBlockBp_Output_blockId_description=配車中この動作のブロック ID
Falcon_Bp_AddTomBlockBp_Output_blockId_label=ブロック ID
Falcon_Bp_AddTomBlockBp_description=追加動作ブロック
Falcon_Bp_AddTomBlockBp_label=配送運搬ブロックの実行
Falcon_Bp_AllowNdcLoadBp_Input_orderId_label=運送リスト番号
Falcon_Bp_AllowNdcLoadBp_description=無効
Falcon_Bp_AllowNdcLoadBp_label=NDC 荷役許可
Falcon_Bp_AllowNdcUnloadBp_Input_orderId_label=運送リスト番号
Falcon_Bp_AllowNdcUnloadBp_description=無効
Falcon_Bp_AllowNdcUnloadBp_label=NDC 荷卸し許可
Falcon_Bp_CompleteTomOrderBp_Input_orderId_description=【第2世代配車運搬番号作成】ブロック生成された“運送番号”
Falcon_Bp_CompleteTomOrderBp_Input_orderId_label=運送番号
Falcon_Bp_CompleteTomOrderBp_Input_tomId_description=《ロボットアプリケーション》ページ中“第2世代配車”のシーン名
Falcon_Bp_CompleteTomOrderBp_Input_tomId_label=配車 ID
Falcon_Bp_CompleteTomOrderBp_description=封止
Falcon_Bp_CompleteTomOrderBp_label=配車搬送番号終了
Falcon_Bp_CreateNdcOrderBp_Input_endBin_description=NDC ロケーション，Short タイプ
Falcon_Bp_CreateNdcOrderBp_Input_endBin_label=終点
Falcon_Bp_CreateNdcOrderBp_Input_priority_label=優先級
Falcon_Bp_CreateNdcOrderBp_Input_startBin_description=NDC ロケーション，Short タイプ
Falcon_Bp_CreateNdcOrderBp_Input_startBin_label=始点
Falcon_Bp_CreateNdcOrderBp_Output_orderId_description=《NDC 運送リスト》番号
Falcon_Bp_CreateNdcOrderBp_Output_orderId_label=運送リスト番号
Falcon_Bp_CreateNdcOrderBp_description=M4が始点、終点、優先級を一緒に NDCに送信；NDC 自動実行；NDC読取り、放出を完了後、タスクが終了した後、M4に報告。
Falcon_Bp_CreateNdcOrderBp_label=NDC運送リスト作成
Falcon_Bp_CreateTomOrderBp_Input_group_description=指定ロボットグループを選択し、このグループに属するロボットを割り当てて実行
Falcon_Bp_CreateTomOrderBp_Input_group_label=ロボットグループ指定
Falcon_Bp_CreateTomOrderBp_Input_keyGoodsId_description=倉庫車n+1のシーンにおいて、coreが最後にピックアップした最初の放置を補助
Falcon_Bp_CreateTomOrderBp_Input_keyGoodsId_label=keyGoodsId
Falcon_Bp_CreateTomOrderBp_Input_keyRouteStr_description=重要なポイントで、ロボットの割り当てを支援します；空白の場合は、システムが現在の状態に基づいて適切なロボットを自動選択
Falcon_Bp_CreateTomOrderBp_Input_keyRouteStr_label=コア位置
Falcon_Bp_CreateTomOrderBp_Input_keyTask_description=为"load"または"unload"，として記入、他のフィールドは自動的に無視されます；空白の場合は、システムが現在の状態に基づいて適切なロボットを自動選択
Falcon_Bp_CreateTomOrderBp_Input_keyTask_label=keyTask
Falcon_Bp_CreateTomOrderBp_Input_label_description=特定のロボットラベルを指定し、現在の運送指示書にこのラベルのロボットを指定
Falcon_Bp_CreateTomOrderBp_Input_label_label=label
Falcon_Bp_CreateTomOrderBp_Input_notMarkComplete_description=猎鹰タスクをキャンセルし、調整運送指示書を封印
Falcon_Bp_CreateTomOrderBp_Input_notMarkComplete_label=タスク搬送リストキャンセル
Falcon_Bp_CreateTomOrderBp_Input_priority_description=運送指示書の優先度、数字が大きいほど優先度が高い
Falcon_Bp_CreateTomOrderBp_Input_priority_label=優先順位
Falcon_Bp_CreateTomOrderBp_Input_tomId_description=「ロボットアプリケーション管理」ページの「第2世代調整」のシーン名
Falcon_Bp_CreateTomOrderBp_Input_tomId_label=シーン名
Falcon_Bp_CreateTomOrderBp_Input_vehicle_description=指定ロボットを選択するために指定されたロボットを実行に割り当てる
Falcon_Bp_CreateTomOrderBp_Input_vehicle_label=ロボット指定
Falcon_Bp_CreateTomOrderBp_Output_actualVehicle_description=第2世代調整運送指示書で割り当てられたロボット
Falcon_Bp_CreateTomOrderBp_Output_actualVehicle_label=割り当てられたロボット
Falcon_Bp_CreateTomOrderBp_Output_orderId_description=第2世代調整運送指示書番号
Falcon_Bp_CreateTomOrderBp_Output_orderId_label=運送指示書番号
Falcon_Bp_CreateTomOrderBp_description=第2世代調整運送指示書を作成、つまりコア調整
Falcon_Bp_CreateTomOrderBp_label=第2世代調整運送指示書の作成
Falcon_Bp_DirectOrderExecuteBp_Input_desc_label=説明
Falcon_Bp_DirectOrderExecuteBp_Input_fillMiddleLandmarks_description=【直接運送指示書のステップ】の間に他のサイトがある場合、間のポイントを埋めます
Falcon_Bp_DirectOrderExecuteBp_Input_fillMiddleLandmarks_label=中間点を埋める
Falcon_Bp_DirectOrderExecuteBp_Input_robotId_label=ロボット番号
Falcon_Bp_DirectOrderExecuteBp_Input_sceneName_description=《ロボットアプリケーション管理》（2.5世代配車）シーン名、または《配車シーン》（3 世代配車）のシーン名
Falcon_Bp_DirectOrderExecuteBp_Input_sceneName_label=シーン名
Falcon_Bp_DirectOrderExecuteBp_Input_seer3066_description=3066指令を使用して、特定ナビゲートパス指定
Falcon_Bp_DirectOrderExecuteBp_Input_seer3066_label=3066 指令有効
Falcon_Bp_DirectOrderExecuteBp_Input_taskId_label=タスク番号
Falcon_Bp_DirectOrderExecuteBp_Output_orderId_description=《直接運送指示書》番号
Falcon_Bp_DirectOrderExecuteBp_Output_orderId_label=オーダー番号
Falcon_Bp_DirectOrderExecuteBp_description=《直接運送指示書》を作成し、その実行を待機（再作成はしません）
Falcon_Bp_DirectOrderExecuteBp_label=直接運送指示書の実行
Falcon_Bp_DirectOrderMoveBp_Input_binTask_label=binTask
Falcon_Bp_DirectOrderMoveBp_Input_containerId_label=容器番号
Falcon_Bp_DirectOrderMoveBp_Input_id_description=目的地名，ステーション名，或いはロケーション
Falcon_Bp_DirectOrderMoveBp_Input_id_label=ステーション
Falcon_Bp_DirectOrderMoveBp_Input_operation_description=実行機構操作
Falcon_Bp_DirectOrderMoveBp_Input_operation_label=動作（operation）
Falcon_Bp_DirectOrderMoveBp_description=直接運送指示書手順
Falcon_Bp_DirectOrderMoveBp_label=直接運送指示書手順
Falcon_Bp_MapPointBinBp_Input_pointId_label=ステーション
Falcon_Bp_MapPointBinBp_Output_binId_label=ロケーション
Falcon_Bp_MapPointBinBp_Output_found_label=ステーションロケーション設定済み
Falcon_Bp_MapPointBinBp_label=ステーションロケーション
Falcon_Bp_WaitUntilNdcArriveEndBinBp_Input_orderId_label=運送リスト番号
Falcon_Bp_WaitUntilNdcArriveEndBinBp_description=無効
Falcon_Bp_WaitUntilNdcArriveEndBinBp_label=NDC 終点到着待ち
Falcon_Bp_WaitUntilNdcArriveStartBinBp_Input_orderId_label=運送リスト番号
Falcon_Bp_WaitUntilNdcArriveStartBinBp_description=無効
Falcon_Bp_WaitUntilNdcArriveStartBinBp_label=NDC 到着始点待ち
Falcon_Bp_WaitUntilNdcFinishBp_Input_orderId_label=運送リスト番号
Falcon_Bp_WaitUntilNdcFinishBp_label=NDC タスク完了待ち
Falcon_Bp_WaitUntilNdcLoadedBp_Input_orderId_label=運送リスト番号
Falcon_Bp_WaitUntilNdcLoadedBp_label=NDC荷役完了待ち
Falcon_Bp_WaitUntilNdcUnloadedBp_Input_orderId_label=運送リスト番号
Falcon_Bp_WaitUntilNdcUnloadedBp_label=NDC 荷卸し完了待ち
relocStatus_0=位置決定失敗
relocStatus_1=位置決定正確
relocStatus_2=位置再設定中
relocStatus_3=位置決定終了
relocStatus_null=位置状態不明
Falcon_BlockGroup_RobotSingleControl=ロボット制御
Falcon_Bp_RobotReadDIBp_Input_id_description=指定された DI 番号
Falcon_Bp_RobotReadDIBp_Input_id_label=DI 番号
Falcon_Bp_RobotReadDIBp_Input_tomId_description=《ロボット運用》ページの“二代目配車”のシーン名
Falcon_Bp_RobotReadDIBp_Input_tomId_label=配車 ID
Falcon_Bp_RobotReadDIBp_Input_vehicle_description=指定ロボット
Falcon_Bp_RobotReadDIBp_Input_vehicle_label=指定ロボット
Falcon_Bp_RobotReadDIBp_Output_result_label=読取り結果Falcon_Bp_RobotReadDIBp_description
Falcon_Bp_RobotReadDIBp_label=読取り DI
Falcon_Bp_RobotSetDOBp_Input_id_description=DO 番号の指定
Falcon_Bp_RobotSetDOBp_Input_id_label=DO 番号
Falcon_Bp_RobotSetDOBp_Input_status_description=指定 DO の状態をこの状態に変更
Falcon_Bp_RobotSetDOBp_Input_status_label=目標状態
Falcon_Bp_RobotSetDOBp_Input_tomId_description=《ロボット運用》ページの“二代目配車”にシーン名
Falcon_Bp_RobotSetDOBp_Input_tomId_label=配車 ID
Falcon_Bp_RobotSetDOBp_Input_vehicle_description=指定ロボット
Falcon_Bp_RobotSetDOBp_Input_vehicle_label=指定ロボット
Falcon_Bp_RobotSetDOBp_Output_result_description=実行後の応答結果Falcon_Bp_RobotSetDOBp_Output_result_label
Falcon_Bp_RobotSetDOBp_description=指定されたDO状態を変更用
Falcon_Bp_RobotSetDOBp_label=DO設定
Falcon_Bp_RobotSingleAngleRadianBp_Input_conversionType_Option_AngleToRadian=角度をラジアン変換
Falcon_Bp_RobotSingleAngleRadianBp_Input_conversionType_Option_RadianToAngle=ラジアンを角度変換
Falcon_Bp_RobotSingleAngleRadianBp_Input_conversionType_description=切り替えタイプ選択，数値を要望する角度またはラジアンに変換。
Falcon_Bp_RobotSingleAngleRadianBp_Input_conversionType_label=切り替えタイプ
Falcon_Bp_RobotSingleAngleRadianBp_Input_degrees_description=切り替えタイプ選択，数値を要望する角度またはラジアンに変換。
Falcon_Bp_RobotSingleAngleRadianBp_Input_degrees_label=数値
Falcon_Bp_RobotSingleAngleRadianBp_Output_degrees_description=変換後の度数またはラジアン。
Falcon_Bp_RobotSingleAngleRadianBp_Output_degrees_label=結果Falcon_Bp_RobotSingleAngleRadianBp_description
Falcon_Bp_RobotSingleAngleRadianBp_label=ラジアンと角度変換
Falcon_Bp_RobotSingleForkBp_Input_endHeight_description=フォーク調整の最終高度，デフォルト値 0.500（単位：m）
Falcon_Bp_RobotSingleForkBp_Input_endHeight_label=終了高度
Falcon_Bp_RobotSingleForkBp_Input_forkDist_description=对于前移式フォーク，フォーク向前移動的距離，デフォルト値 0.00（単位：m）
Falcon_Bp_RobotSingleForkBp_Input_forkDist_label=前進距離（前進式フォーク）
Falcon_Bp_RobotSingleForkBp_Input_forkMidHeight_description=走行时フォーク高度，デフォルト値 0.100（単位：m）
Falcon_Bp_RobotSingleForkBp_Input_forkMidHeight_label=走行高度
Falcon_Bp_RobotSingleForkBp_Input_operation_Option_ForkForward=フォーク前進（ForkForward）
Falcon_Bp_RobotSingleForkBp_Input_operation_Option_ForkHeight=フォーク上昇（ForkHeight）
Falcon_Bp_RobotSingleForkBp_Input_operation_Option_ForkLoad=フォーク荷物取り（ForkLoad）
Falcon_Bp_RobotSingleForkBp_Input_operation_Option_ForkUnload=フォーク荷物置き（ForkUnload）
Falcon_Bp_RobotSingleForkBp_Input_operation_description=フォーク機構操作可能動作
Falcon_Bp_RobotSingleForkBp_Input_operation_label=動作
Falcon_Bp_RobotSingleForkBp_Input_recfile_description=ロボット実行特定動作时，需要用到此ファイル进行识别比对之类的
Falcon_Bp_RobotSingleForkBp_Input_recfile_label=識別ファイル
Falcon_Bp_RobotSingleForkBp_Input_reqId_description=コンポーネント実行時の標識，オプション選択
Falcon_Bp_RobotSingleForkBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleForkBp_Input_startHeight_description=フォークのスタート高度，デフォルト値 0.100（単位：m）
Falcon_Bp_RobotSingleForkBp_Input_startHeight_label=スタート高度
Falcon_Bp_RobotSingleForkBp_Input_station_description=ナビゲーションの目標ステーション
Falcon_Bp_RobotSingleForkBp_Input_station_label=目標ステーション
Falcon_Bp_RobotSingleForkBp_Output_rbkResult_description=実行後の応答結果
Falcon_Bp_RobotSingleForkBp_Output_rbkResult_label=rbk リクエスト結果Falcon_Bp_RobotSingleForkBp_description
Falcon_Bp_RobotSingleForkBp_label=フォーク操作 / 荷降ろし
Falcon_Bp_RobotSingleGetAllStatusBp_Output_status_description=ロボットの全部状態，データタイプ为はJsonObject
Falcon_Bp_RobotSingleGetAllStatusBp_Output_status_label=状態
Falcon_Bp_RobotSingleGetAllStatusBp_description=ロボット1100インターフェースの原始レポートを取得
Falcon_Bp_RobotSingleGetAllStatusBp_label=ロボットのすべての状態を取得
Falcon_Bp_RobotSingleGetOneStatusBp_Input_status_Option_batteryLevel=バッテリー残量
Falcon_Bp_RobotSingleGetOneStatusBp_Input_status_Option_currentStation=現在ステーション
Falcon_Bp_RobotSingleGetOneStatusBp_Input_status_description=ロボット状態，例えば：バッテリー残量、現在のステーション
Falcon_Bp_RobotSingleGetOneStatusBp_Input_status_label=状態
Falcon_Bp_RobotSingleGetOneStatusBp_Output_status_description=ロボットの指定状態
Falcon_Bp_RobotSingleGetOneStatusBp_Output_status_label=状態
Falcon_Bp_RobotSingleGetOneStatusBp_description=入力パラメーターに基づいてロボットの指定された状態を返す
Falcon_Bp_RobotSingleGetOneStatusBp_label=ロボット指定状態取得
Falcon_Bp_RobotSingleJackingBp_Input_jackHeight_description=将ジャッキンぐ機構上昇高度，デフォルト値 0.010（単位：m）
Falcon_Bp_RobotSingleJackingBp_Input_jackHeight_label=ジャッキンぐ高度
Falcon_Bp_RobotSingleJackingBp_Input_operation_Option_JackLoad=ジャッキンぐ（JackLoad）
Falcon_Bp_RobotSingleJackingBp_Input_operation_Option_JackUnload=下ろす（JackUnload）
Falcon_Bp_RobotSingleJackingBp_Input_operation_description=ジャッキンぐ機構操作可能動作
Falcon_Bp_RobotSingleJackingBp_Input_operation_label=動作
Falcon_Bp_RobotSingleJackingBp_Input_recfile_description=ロボット特定動作実行中，このファイルのを利用して識別対照する
Falcon_Bp_RobotSingleJackingBp_Input_recfile_label=ファイル識別
Falcon_Bp_RobotSingleJackingBp_Input_reqId_description=コンポーネント実行時の標識，オプション選択
Falcon_Bp_RobotSingleJackingBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleJackingBp_Input_station_description=ナビゲーションの目標ステーション
Falcon_Bp_RobotSingleJackingBp_Input_station_label=目標ステーション
Falcon_Bp_RobotSingleJackingBp_Input_useDownPgv_description=下部ビジョン PGV使用
Falcon_Bp_RobotSingleJackingBp_Input_useDownPgv_label=下部ビジョン PGV使用
Falcon_Bp_RobotSingleJackingBp_Input_usePgv_description=上部ビジョン PGV使用
Falcon_Bp_RobotSingleJackingBp_Input_usePgv_label=上部ビジョン PGV使用
Falcon_Bp_RobotSingleJackingBp_Output_rbkResult_description=実行後の応答結果
Falcon_Bp_RobotSingleJackingBp_Output_rbkResult_label=rbk リクエスト結果Falcon_Bp_RobotSingleJackingBp_description
Falcon_Bp_RobotSingleJackingBp_label=ジャッキンぐ
Falcon_Bp_RobotSingleNavigationBp_Input_reqId_description=コンポーネント実行時の標識，オプション選択
Falcon_Bp_RobotSingleNavigationBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleNavigationBp_Input_station_description=ナビゲーションの目標ステーション
Falcon_Bp_RobotSingleNavigationBp_Input_station_label=目標ステーション
Falcon_Bp_RobotSingleNavigationBp_Output_rbkResult_description=実行後の応答結果
Falcon_Bp_RobotSingleNavigationBp_Output_rbkResult_label=rbk リクエスト結果Falcon_Bp_RobotSingleNavigationBp_description
Falcon_Bp_RobotSingleNavigationBp_label=パスナビゲーション
Falcon_Bp_RobotSinglePlayAudioBp_Input_audioFilename_description=ロボット再生オーディオファイル指定用
Falcon_Bp_RobotSinglePlayAudioBp_Input_audioFilename_label=オーディオファイル名
Falcon_Bp_RobotSinglePlayAudioBp_Input_loop_description=循環再生を選択するとオーディオは繰り返し再生され、それ以外は1回のみ再生される
Falcon_Bp_RobotSinglePlayAudioBp_Input_loop_label=循環再生
Falcon_Bp_RobotSinglePlayAudioBp_Input_reqId_description=コンポーネント実行時の標識，オプション選択
Falcon_Bp_RobotSinglePlayAudioBp_Input_reqId_label=ID
Falcon_Bp_RobotSinglePlayAudioBp_Input_station_description=ナビゲーション的目標ステーション
Falcon_Bp_RobotSinglePlayAudioBp_Input_station_label=目標ステーション
Falcon_Bp_RobotSinglePlayAudioBp_Output_rbkResult_description=実行後の応答結果
Falcon_Bp_RobotSinglePlayAudioBp_Output_rbkResult_label=rbk リクエスト結果Falcon_Bp_RobotSinglePlayAudioBp_description
Falcon_Bp_RobotSinglePlayAudioBp_label=オーディオ再生
Falcon_Bp_RobotSingleRollerBp_Input_direction_Option_back=後
Falcon_Bp_RobotSingleRollerBp_Input_direction_Option_front=前
Falcon_Bp_RobotSingleRollerBp_Input_direction_Option_left=左
Falcon_Bp_RobotSingleRollerBp_Input_direction_Option_right=右
Falcon_Bp_RobotSingleRollerBp_Input_direction_description=ローラー機構の動作方向
Falcon_Bp_RobotSingleRollerBp_Input_direction_label=方向
Falcon_Bp_RobotSingleRollerBp_Input_operation_Option_RollerLoad=ローラー荷役（RollerUnload）
Falcon_Bp_RobotSingleRollerBp_Input_operation_Option_RollerUnload=ローラー荷卸し（RollerLoad）
Falcon_Bp_RobotSingleRollerBp_Input_operation_description=ローラー機構が操作可能動作
Falcon_Bp_RobotSingleRollerBp_Input_operation_label=動作
Falcon_Bp_RobotSingleRollerBp_Input_reqId_description=コンポーネント実行時の標識，オプション選択
Falcon_Bp_RobotSingleRollerBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleRollerBp_Input_station_description=ナビゲーションの目標ステーション
Falcon_Bp_RobotSingleRollerBp_Input_station_label=目標ステーション
Falcon_Bp_RobotSingleRollerBp_Output_rbkResult_description=実行后の応答結果
Falcon_Bp_RobotSingleRollerBp_Output_rbkResult_label=rbk リクエスト結果Falcon_Bp_RobotSingleRollerBp_description
Falcon_Bp_RobotSingleRollerBp_label=ローラー
Falcon_Bp_RobotSingleRotateBp_Input_angle_description=ロボットジャッキンぐ角度制御
Falcon_Bp_RobotSingleRotateBp_Input_angle_label=ジャッキンぐ角度（°）
Falcon_Bp_RobotSingleRotateBp_Input_mode_description=走行モードは距離に基づいて移動、位置決めモードは正確な位置決めが必要、デフォルトは走行モード
Falcon_Bp_RobotSingleRotateBp_Input_mode_label=モード
Falcon_Bp_RobotSingleRotateBp_Input_reqId_description=コンポーネント実行時の標識，オプション選択
Falcon_Bp_RobotSingleRotateBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleRotateBp_Input_vw_description=ロボットジャッキンぐ速度制御，デフォルト値 45.0（°/s）
Falcon_Bp_RobotSingleRotateBp_Input_vw_label=ジャッキンぐ角速度（°/s）
Falcon_Bp_RobotSingleRotateBp_Output_rbkResult_description=実行後の応答結果
Falcon_Bp_RobotSingleRotateBp_Output_rbkResult_label=rbk リクエスト結果Falcon_Bp_RobotSingleRotateBp_description
Falcon_Bp_RobotSingleRotateBp_label=回転
Falcon_Bp_RobotSingleSetDOBp_Input_id_description=指定されたDO 番号
Falcon_Bp_RobotSingleSetDOBp_Input_id_label=DO 番号
Falcon_Bp_RobotSingleSetDOBp_Input_status_Option_status_0=0（無効）
Falcon_Bp_RobotSingleSetDOBp_Input_status_Option_status_1=1（有効）
Falcon_Bp_RobotSingleSetDOBp_Input_status_description=指定されたDOの状態をこの状態に変更
Falcon_Bp_RobotSingleSetDOBp_Input_status_label=目標状態
Falcon_Bp_RobotSingleSetDOBp_Output_rbkResult_description=実行後の応答結果
Falcon_Bp_RobotSingleSetDOBp_Output_rbkResult_label=rbk リクエスト結果Falcon_Bp_RobotSingleSetDOBp_description
Falcon_Bp_RobotSingleSetDOBp_label=DO設定
Falcon_Bp_RobotSingleStopAudioBp_Input_reqId_description=コンポーネント実行時の標識，オプション選択
Falcon_Bp_RobotSingleStopAudioBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleStopAudioBp_Input_station_description=ナビゲーションの目標ステーション
Falcon_Bp_RobotSingleStopAudioBp_Input_station_label=目標ステーション
Falcon_Bp_RobotSingleStopAudioBp_Output_rbkResult_description=実行後の応答結果
Falcon_Bp_RobotSingleStopAudioBp_Output_rbkResult_label=rbk リクエスト結果Falcon_Bp_RobotSingleStopAudioBp_description
Falcon_Bp_RobotSingleStopAudioBp_label=再生停止
Falcon_Bp_RobotSingleTranslationBp_Input_dist_description=ロボット運動距離限定使用
Falcon_Bp_RobotSingleTranslationBp_Input_dist_label=直線移動距離（単位：m）
Falcon_Bp_RobotSingleTranslationBp_Input_mode_Option_0=走行モード
Falcon_Bp_RobotSingleTranslationBp_Input_mode_Option_1=位置決めモード
Falcon_Bp_RobotSingleTranslationBp_Input_mode_description=走行モードは距離に基づいて移動、位置決めモードは正確な位置決めが必要、デフォルトは走行モード
Falcon_Bp_RobotSingleTranslationBp_Input_mode_label=モード
Falcon_Bp_RobotSingleTranslationBp_Input_reqId_description=コンポーネント実行時の標識，オプション選択
Falcon_Bp_RobotSingleTranslationBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleTranslationBp_Input_vx_description=前後移動速度を制御、負の値は後退、正の値は前進、デフォルト値は0.5（単位：m/s）
Falcon_Bp_RobotSingleTranslationBp_Input_vx_label=前進速度（vx，単位：m/s）
Falcon_Bp_RobotSingleTranslationBp_Input_vy_description=左右方向の速度を制御、全方向ロボットにのみ有効、他のロボットは0、デフォルト値 0.0（単位：m/s）
Falcon_Bp_RobotSingleTranslationBp_Input_vy_label=横移動速度（vy，単位：m/s）
Falcon_Bp_RobotSingleTranslationBp_Output_rbkResult_description=実行後の応答結果
Falcon_Bp_RobotSingleTranslationBp_Output_rbkResult_label=rbk リクエスト結果Falcon_Bp_RobotSingleTranslationBp_description
Falcon_Bp_RobotSingleTranslationBp_label=平行移動
Falcon_Bp_RobotSingleUserDefinedBp_Input_apiNo_description=指令番号，デフォルトは 3051
Falcon_Bp_RobotSingleUserDefinedBp_Input_apiNo_label=API 番号
Falcon_Bp_RobotSingleUserDefinedBp_Input_reqId_description=コンポーネント実行時の標識，オプション選択
Falcon_Bp_RobotSingleUserDefinedBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleUserDefinedBp_Input_userDefinedAction_description=カスタム動作指令（JSON 文字列形式）
Falcon_Bp_RobotSingleUserDefinedBp_Input_userDefinedAction_label=動作
Falcon_Bp_RobotSingleUserDefinedBp_Output_rbkResult_description=実行後の応答結果
Falcon_Bp_RobotSingleUserDefinedBp_Output_rbkResult_label=rbk リクエスト結果Falcon_Bp_RobotSingleUserDefinedBp_description
Falcon_Bp_RobotSingleUserDefinedBp_label=カスタム動作
Falcon_Bp_RobotSingleWaitDIBp_Input_id_description=指定された DI 番号
Falcon_Bp_RobotSingleWaitDIBp_Input_id_label=DI 番号
Falcon_Bp_RobotSingleWaitDIBp_Input_status_Option_status_0=0（無効）
Falcon_Bp_RobotSingleWaitDIBp_Input_status_Option_status_1=1（有効）
Falcon_Bp_RobotSingleWaitDIBp_Input_status_description=DIの状態が期待される状態である場合、DIがトリガーされたことを示す。
Falcon_Bp_RobotSingleWaitDIBp_Input_status_label=希望状態
Falcon_Bp_RobotSingleWaitDIBp_Input_timeout_description=DIがトリガーされるのを待つタイムアウト時間、未記入の場合はずっと待機
Falcon_Bp_RobotSingleWaitDIBp_Input_timeout_label=タイムアウト時間（s）
Falcon_Bp_RobotSingleWaitDIBp_Output_rbkResult_description=実行後の応答結果
Falcon_Bp_RobotSingleWaitDIBp_Output_rbkResult_label=rbk リクエスト結果Falcon_Bp_RobotSingleWaitDIBp_description
Falcon_Bp_RobotSingleWaitDIBp_label=DIトリガー待機
Falcon_Bp_RobotWaitDIBp_Input_id_description=指定された DI 番号
Falcon_Bp_RobotWaitDIBp_Input_id_label=DI 番号
Falcon_Bp_RobotWaitDIBp_Input_status_description=当DI の状態が希望状態時， DI トリガーを示す。
Falcon_Bp_RobotWaitDIBp_Input_status_label=希望状態
Falcon_Bp_RobotWaitDIBp_Input_timeOut_description=DIがトリガーされるのを待つタイムアウト時間、未記入の場合はずっと待機
Falcon_Bp_RobotWaitDIBp_Input_timeOut_label=タイムアウト時間（s）
Falcon_Bp_RobotWaitDIBp_Input_tomId_description=《ロボット運用》ページの“二代目配車”のシーン名
Falcon_Bp_RobotWaitDIBp_Input_tomId_label=配車 ID
Falcon_Bp_RobotWaitDIBp_Input_vehicle_description=指定ロボット
Falcon_Bp_RobotWaitDIBp_Input_vehicle_label=指定ロボット
Falcon_Bp_RobotWaitDIBp_Output_result_description=実行後の応答結果
Falcon_Bp_RobotWaitDIBp_Output_result_label=結果Falcon_Bp_RobotWaitDIBp_description
Falcon_Bp_RobotWaitDIBp_label=DIのトリガー待機I
T00010001=経路計画パラメータの検証失敗
T00010002=経路計画、出発位置ルートコード {0} に基づきルートが見つからなかったので，このルートコードが現在のロボットグループ {1} のマップ上に存在するか確認してください。
T00010003=経路計画、出発点または終点が見つからなかった。始点コード {0} ，終点コード {1}， 所在ロボットグループ {2}
T00010004=経路計画，{0} パスが見つからなかった。
T00010010=経路計画，{0} 計画成功
errAwaitMove=ナビゲーション完了待ち，但ナビゲーション失敗，具体的原因：{0}
errCreateStoOrderExists=存在するオーダー
errDirectRobotOrderFailed=直接運送リスト失敗，リストID
errFailedToFetchMapFromRobot=ロボット {0} からマップを取得失敗
errFetchChassisMode=シャーシ駆動タイプの取得失敗
errFetchCurrentMapFail=ロボットの現在位置地図取得失敗，ロボット
errFetchMapFail=読取りロボット地図失敗，ロボット
errGwNoLocalRobot=ゲートウェイにロボット {0}がない
errMotorNotFound=モデルファイルからモーター情報を読み取ることができない。
errMotorParamTypeUnsupported=モデルファイル {0} の {1} はサポートしないパラメータータイプ {2}を使用，ポートされているのは arrayParam と comboParam 。
errMotorPropNotFound=モデルファイルの {0} の {1} 属性 {2} がない。
errMotorTypeNotFound=モデルファイルの {0} にはタイプ {1} も属性分類がない。
errMove3051=3051 パスナビゲーション発送失敗：{0}
errMove3066=3066 パスナビゲーション発送失敗：{0}
errMoveRobotFromToStation=ロボット {0} 現在 {1}にある，最初のパス始点 {2} 或いは終点 {3}に戻してから 再試行してください
errNoAnyRobot=存在しないロボット
errNoDefaultMap=デフォルト地図が見つからない（ファーストエリア、最初車種）
errNoFirstLightScene=光通信シーンなし
errNoReportApplyUrl=レポートアドレスの指定なし
errNoRobot=存在しないロボット "{0}"
errNoSuchBinOrLocation=名前{0} の位置またはロケーションが見つからない
errNotRobotInTom=配車 {0} 可能ロボット {1}が存在しない
errOnlyOneSingleAppCanBeEnabled=システムは単一の車両アプリケーションしか有効にできない
errPositionToNoSite=位置（{0},{1}）付近にステーションがない
errQuery3066BadTaskId=3066 検索結果，タスク IDが一致しない，期待値 {0}，実際 {1}
errRbkResErr=rbk リクエスト失敗，番号：{0}，タイプ：{1}，原因：{2}
errRbkResultKindConnectFail=rbk 接続失敗
errRecAndForkLoadOverHeight=入力されたピックアップ前の高さ {0} がフォークの最大リフト高さ {1}を超えている 。
errRequestControl=リクエスト制御権失敗，ロボット
errRetryButOrderNotFailed=再試行，オーダー状態失敗ではない
errRobotAppNoControlPower=制御権なし
errRobotAppNoScene=設定されていないシーン
errRobotAppNoSceneById=存在しないシーン：{0}
errRobotAppNoSceneByName=存在しないシーン：{0}
errRobotAppNoSingleScene=このシーン {0} はロボットには適応しない。
errRobotAppSceneDisabled=停止したシーン {0}
errRobotExecutingTask=ロボットは現在ナビゲーションタスクを実行中。
errRobotHasSimpleOrderCurrent=ロボット {0} リスト実行中 {1}，新しいタスク受領不可
errRobotMoveNoStart=ロボット {0} が実行できるタスク始点が見つからない
errRobotNavNoId=パスナビゲーションリクエストエラー，終点パラメーター 'id'必要
errRobotNavNoSourceId=パスナビゲーションリクエストエラー，始点パラメーター 'source_id'必要
errRobotNoAnyScene=現在どのシーンもない
errRobotNoConnector=ロボット "{0}" とのコネクタがない
errRobotNoCurrentStation=ロボット {0}が同じ ステーションにない
errRobotNoPosition=ロボット "{0}" 位置読取りエラー，ロボット位置確認必要
errRobotOffline=ロボット "{0}" オフラインか、接続失敗
errRobotUpdateBusy=ロボット作業中，更新不可
errStoHikNotSupported=シングルオーダーは海康ロボットサポートしない
errStoNoRobot=リスト生成 {1} 失敗，ゲートにロボット {0}存在しない
errStoRetryFailedBadCurrentStatus=現在リスト {0} の状態は失敗ではなく {1}
errStoRetryFailedNoCurrent=ロボットに実行中リストなし
errTargetMotorNotFound=モデルファイルには名前 {0}  motor がない。
errTomBlockStopped=配車ブロック {0}:{1} 停止、手動処理、エラー情報 {2}
errTomDisconnected=配車サーバが存在しない，或いは接続に失敗：{0}
errTomOrderAwaitNotFound=送信後配車オーダー "{0}"が見つからない
errTomOrderKeyRouteMissing=少なくとも1つの重要な位置を指定する必要あり
errTomOrderNoVehicle=運送リスト "{0}" にロボットが割り当てられていない（運送李リスト状態：{1}）
errTomOrderNoVehicle2=運送リスト "{0}" 見配車ロボット
errUnsupportedFetchMap=以下の接続方法でマップを取得することはサポートしない：{0}
errUnsupportedFetchMap2=このシナリオではマップの取得がサポートしない
errorLocationEmpty=位置（Location）必須
errorTomOrderIdEmpty=配車運送リスト番号必須
entity.DemoEntity.fields.componentTableField.tip=コンポーネントリストコンポーネントリストコンポーネントリストコンポーネントリストコンポーネントリストコンポーネントリストコンポーネントリスト
entity.DemoEntity.fields.fileField.tip=ファイルのアップロードファイルのアップロードファイルのアップロードファイルのアップロードファイルのアップロードファイルのアップロードファイルのアップロードファイルのアップロードファイルのアップロードファイルのアップロードファイルのアップロード
entity.DemoEntity.fields.floatField.tip=入力説明入力説明入力説明入力説明入力説明入力説明
entity.DemoEntity.fields.id.tip=入力説明
entity.DemoEntity.fields.imageField.tip=ファイルのアップロードファイルのアップロードファイルのアップロードファイルのアップロードファイルのアップロードファイルのアップロードファイルのアップロードファイルのアップロード
entity.DemoEntity.fields.stringField.tip=入力説明
i18n_entity.AgentUser.fields.appId.label=appId
i18n_entity.AgentUser.fields.appKey.label=appKey
i18n_entity.AgentUser.fields.btDisabled.label=使用停止
i18n_entity.AgentUser.fields.createdBy.label=作成者
i18n_entity.AgentUser.fields.createdOn.label=作成日
i18n_entity.AgentUser.fields.id.label=ID
i18n_entity.AgentUser.fields.modifiedBy.label=最終改訂人
i18n_entity.AgentUser.fields.modifiedOn.label=最終改訂時間
i18n_entity.AgentUser.fields.remark.label=備考
i18n_entity.AgentUser.fields.version.label=バージョン変更
i18n_entity.AgentUser.group=Core
i18n_entity.AgentUser.label=代理アカウント
i18n_entity.ApiCallTrace.fields.apiType.inlineOptionBill.items.HTTP.label=HTTP
i18n_entity.ApiCallTrace.fields.apiType.label=ポートタイプ
i18n_entity.ApiCallTrace.fields.costTime.label=経過時間
i18n_entity.ApiCallTrace.fields.createdBy.label=作成者
i18n_entity.ApiCallTrace.fields.createdOn.label=作成時間
i18n_entity.ApiCallTrace.fields.httpMethod.inlineOptionBill.items.DELETE.label=DELETE
i18n_entity.ApiCallTrace.fields.httpMethod.inlineOptionBill.items.GET.label=GET
i18n_entity.ApiCallTrace.fields.httpMethod.inlineOptionBill.items.POST.label=POST
i18n_entity.ApiCallTrace.fields.httpMethod.inlineOptionBill.items.PUT.label=PUT
i18n_entity.ApiCallTrace.fields.httpMethod.label=HTTP方法
i18n_entity.ApiCallTrace.fields.httpPath.label=HTTP パス
i18n_entity.ApiCallTrace.fields.httpUrl.label=リクエスト URL
i18n_entity.ApiCallTrace.fields.id.label=ID
i18n_entity.ApiCallTrace.fields.modifiedBy.label=最終改訂人
i18n_entity.ApiCallTrace.fields.modifiedOn.label=時間変更
i18n_entity.ApiCallTrace.fields.reqBody.label=リクエストテキスト
i18n_entity.ApiCallTrace.fields.reqBodyOn.label=リクエストテキスト記録要求
i18n_entity.ApiCallTrace.fields.reqEndOn.label=応答時間発信
i18n_entity.ApiCallTrace.fields.reqIp.label=リクエスト IP
i18n_entity.ApiCallTrace.fields.reqStartOn.label=リクエスト時間受信
i18n_entity.ApiCallTrace.fields.reqUser.label=リクエストユーザー
i18n_entity.ApiCallTrace.fields.resBody.label=応答テキスト
i18n_entity.ApiCallTrace.fields.resBodyOn.label=応答テキスト記録要求
i18n_entity.ApiCallTrace.fields.resCode.label=応答コード
i18n_entity.ApiCallTrace.fields.version.label=バージョン修正
i18n_entity.ApiCallTrace.group=Core
i18n_entity.ApiCallTrace.label=ポート呼び出し履歴
i18n_entity.ApiCallTrace.pagesButtons.ListMain.buttons[0].label=削除
i18n_entity.ApiCallTrace.pagesButtons.ListMain.buttons[1].label=エクスポート
i18n_entity.ApiCallTrace.pagesButtons.ListMain.buttons[2].label=設定
i18n_entity.CallEmptyContainerOrder.fields.createdBy.label=作成者
i18n_entity.CallEmptyContainerOrder.fields.createdOn.label=作成時間
i18n_entity.CallEmptyContainerOrder.fields.district.label=倉庫エリア
i18n_entity.CallEmptyContainerOrder.fields.id.label=リスト番号
i18n_entity.CallEmptyContainerOrder.fields.modifiedBy.label=最終改訂人
i18n_entity.CallEmptyContainerOrder.fields.modifiedOn.label=最終変更時間
i18n_entity.CallEmptyContainerOrder.fields.num.label=数量
i18n_entity.CallEmptyContainerOrder.fields.version.label=バージョン修正
i18n_entity.CallEmptyContainerOrder.group=Warehouse
i18n_entity.CallEmptyContainerOrder.label=空き容器を呼び出す
i18n_entity.CallEmptyContainerOrder.listCard.district.prefix=倉庫エリア
i18n_entity.CallEmptyContainerOrder.listCard.num.prefix=数量
i18n_entity.CallEmptyContainerOrder.pagesButtons.Create.buttons[0].label=提出
i18n_entity.ContainerTransportOrder.fields.atPort.label=倉庫口
i18n_entity.ContainerTransportOrder.fields.container.label=容器
i18n_entity.ContainerTransportOrder.fields.createdBy.label=作成者
i18n_entity.ContainerTransportOrder.fields.createdOn.label=作成時間
i18n_entity.ContainerTransportOrder.fields.doneOn.label=完了時間
i18n_entity.ContainerTransportOrder.fields.errMsg.label=エラー原因
i18n_entity.ContainerTransportOrder.fields.expectedRobot.label=指定ロボット
i18n_entity.ContainerTransportOrder.fields.falconTaskDefId.label=猎鹰タスクテンプレート ID
i18n_entity.ContainerTransportOrder.fields.falconTaskDefLabel.label=猎鹰タスクテンプレート名
i18n_entity.ContainerTransportOrder.fields.falconTaskId.label=猎鹰タスク番号
i18n_entity.ContainerTransportOrder.fields.fromBin.label=始点ロケーション
i18n_entity.ContainerTransportOrder.fields.fromChannel.label=始点サブレーン
i18n_entity.ContainerTransportOrder.fields.id.label=ID
i18n_entity.ContainerTransportOrder.fields.kind.label=タイプ
i18n_entity.ContainerTransportOrder.fields.loaded.label=荷役済み
i18n_entity.ContainerTransportOrder.fields.modifiedBy.label=最終改訂人
i18n_entity.ContainerTransportOrder.fields.modifiedOn.label=最終変更時間
i18n_entity.ContainerTransportOrder.fields.postProcessMark.label=処理標記
i18n_entity.ContainerTransportOrder.fields.priority.label=優先級
i18n_entity.ContainerTransportOrder.fields.remark.label=備考
i18n_entity.ContainerTransportOrder.fields.robotName.label=実行ロボット
i18n_entity.ContainerTransportOrder.fields.sourceOrderId.label=オーダーリスト紐付け
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Assigned.label=配車済み
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Building.label=未提出
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Cancelled.label=キャンセル
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Created.label=提出済み
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Done.label=完了
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Failed.label=失敗
i18n_entity.ContainerTransportOrder.fields.status.label=状態
i18n_entity.ContainerTransportOrder.fields.toBin.label=終点ロケーション
i18n_entity.ContainerTransportOrder.fields.toChannel.label=終点サブレーン
i18n_entity.ContainerTransportOrder.fields.unloaded.label=荷卸し済み
i18n_entity.ContainerTransportOrder.fields.version.label=バージョン修正
i18n_entity.ContainerTransportOrder.group=WCS
i18n_entity.ContainerTransportOrder.label=容器搬送リスト
i18n_entity.ContainerTransportOrder.listCard.container.prefix=容器
i18n_entity.ContainerTransportOrder.listCard.doneOn.prefix=完了
i18n_entity.ContainerTransportOrder.listCard.falconTaskId.prefix=猎鹰タスク
i18n_entity.ContainerTransportOrder.listCard.fromBin.suffix=--->
i18n_entity.ContainerTransportOrder.listCard.priority.prefix=優先級
i18n_entity.ContainerTransportOrder.listCard.robotName.prefix=ロボット
i18n_entity.ContainerTransportOrder.listCard.sourceOrderId.prefix=オーダーリスト紐付け
i18n_entity.ContainerTransportOrder.listStats.items[0].label=失敗
i18n_entity.DemoComponent.fields.btLineNo.label=リスト番号
i18n_entity.DemoComponent.fields.btParentId.label=所属オーダー
i18n_entity.DemoComponent.fields.createdBy.label=作成者
i18n_entity.DemoComponent.fields.createdOn.label=作成時間
i18n_entity.DemoComponent.fields.floatValue.label=floatValue
i18n_entity.DemoComponent.fields.id.label=番号
i18n_entity.DemoComponent.fields.modifiedBy.label=最終改訂人
i18n_entity.DemoComponent.fields.modifiedOn.label=最終変更時間
i18n_entity.DemoComponent.fields.referenceField.label=引用
i18n_entity.DemoComponent.fields.ro.label=元組織
i18n_entity.DemoComponent.fields.stringField.label=ファイル
i18n_entity.DemoComponent.fields.version.label=バージョン
i18n_entity.DemoComponent.group=Test
i18n_entity.DemoComponent.label=コンポーネントテスト
i18n_entity.DemoComponentTable.fields.btLineNo.label=リスト番号
i18n_entity.DemoComponentTable.fields.btParentId.label=所属オーダー
i18n_entity.DemoComponentTable.fields.createdBy.label=作成者
i18n_entity.DemoComponentTable.fields.createdOn.label=作成時間
i18n_entity.DemoComponentTable.fields.dateField.label=dateField
i18n_entity.DemoComponentTable.fields.id.label=番号
i18n_entity.DemoComponentTable.fields.intField.label=intField
i18n_entity.DemoComponentTable.fields.modifiedBy.label=最終改訂人
i18n_entity.DemoComponentTable.fields.modifiedOn.label=最終変更時間
i18n_entity.DemoComponentTable.fields.referenceField.label=引用
i18n_entity.DemoComponentTable.fields.ro.label=元組織
i18n_entity.DemoComponentTable.fields.stringField.label=ファイル
i18n_entity.DemoComponentTable.fields.version.label=バージョン
i18n_entity.DemoComponentTable.group=Test
i18n_entity.DemoComponentTable.label=テストコンポーネントリスト
i18n_entity.DemoEntity.fields.booleanField.label=boolean
i18n_entity.DemoEntity.fields.booleanListField.label=boolean複数値
i18n_entity.DemoEntity.fields.checkList2Field.inlineOptionBill.items.v1.label=選択項目 1
i18n_entity.DemoEntity.fields.checkList2Field.inlineOptionBill.items.v2.label=選択項目 2
i18n_entity.DemoEntity.fields.checkList2Field.inlineOptionBill.items.v3.label=選択項目 3
i18n_entity.DemoEntity.fields.checkList2Field.inlineOptionBill.items.v4.label=選択項目 4
i18n_entity.DemoEntity.fields.checkList2Field.label=選択項目リスト（複数選択）
i18n_entity.DemoEntity.fields.checkListField.inlineOptionBill.items.v1.label=選択項目 1
i18n_entity.DemoEntity.fields.checkListField.inlineOptionBill.items.v2.label=選択項目 2
i18n_entity.DemoEntity.fields.checkListField.inlineOptionBill.items.v3.label=選択項目 3
i18n_entity.DemoEntity.fields.checkListField.inlineOptionBill.items.v4.label=選択項目 4
i18n_entity.DemoEntity.fields.checkListField.label=選択項目リスト（単一選択）
i18n_entity.DemoEntity.fields.componentField.label=コンポーネント
i18n_entity.DemoEntity.fields.componentListField.label=コンポーネント複数値
i18n_entity.DemoEntity.fields.componentTableField.label=コンポーネントリスト
i18n_entity.DemoEntity.fields.createdBy.label=作成者
i18n_entity.DemoEntity.fields.createdOn.label=作成時間
i18n_entity.DemoEntity.fields.dateField.label=日付
i18n_entity.DemoEntity.fields.dateListField.label=日付複数値
i18n_entity.DemoEntity.fields.dateTimeField.label=日付時間
i18n_entity.DemoEntity.fields.dateTimeListField.label=日付時間複数値
i18n_entity.DemoEntity.fields.fileField.label=ファイル
i18n_entity.DemoEntity.fields.fileListField.label=ファイル複数値
i18n_entity.DemoEntity.fields.floatField.label=浮動小数
i18n_entity.DemoEntity.fields.floatListField.label=浮動小数点複数値
i18n_entity.DemoEntity.fields.id.label=番号
i18n_entity.DemoEntity.fields.imageField.label=イメージ図
i18n_entity.DemoEntity.fields.imageListField.label=イメージ図複数値
i18n_entity.DemoEntity.fields.intField.label=整数
i18n_entity.DemoEntity.fields.intListField.label=整数複数値
i18n_entity.DemoEntity.fields.modifiedBy.label=最終改訂人
i18n_entity.DemoEntity.fields.modifiedOn.label=最終変更時間
i18n_entity.DemoEntity.fields.passwordField.label=パスワード入力
i18n_entity.DemoEntity.fields.referenceField.label=引用
i18n_entity.DemoEntity.fields.referenceListField.label=複数値引用
i18n_entity.DemoEntity.fields.ro.label=元組織
i18n_entity.DemoEntity.fields.selectField.inlineOptionBill.items.v1.label=選択項目 1
i18n_entity.DemoEntity.fields.selectField.inlineOptionBill.items.v2.label=選択項目 2
i18n_entity.DemoEntity.fields.selectField.inlineOptionBill.items.v3.label=選択項目 3
i18n_entity.DemoEntity.fields.selectField.inlineOptionBill.items.v4.label=選択項目 4
i18n_entity.DemoEntity.fields.selectField.label=入力選択
i18n_entity.DemoEntity.fields.selectListField.inlineOptionBill.items.v1.label=選択項目 1
i18n_entity.DemoEntity.fields.selectListField.inlineOptionBill.items.v2.label=選択項目 2
i18n_entity.DemoEntity.fields.selectListField.inlineOptionBill.items.v3.label=選択項目 3
i18n_entity.DemoEntity.fields.selectListField.inlineOptionBill.items.v4.label=選択項目 4
i18n_entity.DemoEntity.fields.selectListField.label=選択入力（複数値）
i18n_entity.DemoEntity.fields.stringField.label=ファイル
i18n_entity.DemoEntity.fields.stringListField.label=ファイル複数値
i18n_entity.DemoEntity.fields.textAreaField.label=複数行ファイル入力
i18n_entity.DemoEntity.fields.textAreaListField.label=複数行ファイル入力（複数値）
i18n_entity.DemoEntity.fields.version.label=バージョン
i18n_entity.DemoEntity.group=Test
i18n_entity.DemoEntity.label=テスト実体
i18n_entity.DemoEntity2.fields.booleanField.label=boolean
i18n_entity.DemoEntity2.fields.booleanListField.label=boolean複数値
i18n_entity.DemoEntity2.fields.checkList2Field.inlineOptionBill.items.v1.label=選択項目 1
i18n_entity.DemoEntity2.fields.checkList2Field.inlineOptionBill.items.v2.label=選択項目 2
i18n_entity.DemoEntity2.fields.checkList2Field.inlineOptionBill.items.v3.label=選択項目 3
i18n_entity.DemoEntity2.fields.checkList2Field.inlineOptionBill.items.v4.label=選択項目 4
i18n_entity.DemoEntity2.fields.checkList2Field.label=選択項目リスト（複数選択）
i18n_entity.DemoEntity2.fields.checkListField.inlineOptionBill.items.v1.label=選択項目 1
i18n_entity.DemoEntity2.fields.checkListField.inlineOptionBill.items.v2.label=選択項目 2
i18n_entity.DemoEntity2.fields.checkListField.inlineOptionBill.items.v3.label=選択項目 3
i18n_entity.DemoEntity2.fields.checkListField.inlineOptionBill.items.v4.label=選択項目 4
i18n_entity.DemoEntity2.fields.checkListField.label=選択項目リスト（単一選択）
i18n_entity.DemoEntity2.fields.componentField.label=コンポーネント
i18n_entity.DemoEntity2.fields.componentListField.label=コンポーネント複数値
i18n_entity.DemoEntity2.fields.componentTableField.label=コンポーネントリスト
i18n_entity.DemoEntity2.fields.createdBy.label=作成者
i18n_entity.DemoEntity2.fields.createdOn.label=作成時間
i18n_entity.DemoEntity2.fields.dateField.label=日付
i18n_entity.DemoEntity2.fields.dateListField.label=日付複数値
i18n_entity.DemoEntity2.fields.dateTimeField.label=日付時間
i18n_entity.DemoEntity2.fields.dateTimeListField.label=日付時間複数値
i18n_entity.DemoEntity2.fields.fileField.label=ファイル
i18n_entity.DemoEntity2.fields.fileListField.label=ファイル複数値
i18n_entity.DemoEntity2.fields.floatField.label=浮動小数点
i18n_entity.DemoEntity2.fields.floatListField.label=浮動小数点複数値
i18n_entity.DemoEntity2.fields.id.label=番号
i18n_entity.DemoEntity2.fields.imageField.label=イメージ図
i18n_entity.DemoEntity2.fields.imageListField.label=イメージ図複数値
i18n_entity.DemoEntity2.fields.intField.label=整数
i18n_entity.DemoEntity2.fields.intListField.label=整数複数値
i18n_entity.DemoEntity2.fields.modifiedBy.label=最終改訂人
i18n_entity.DemoEntity2.fields.modifiedOn.label=最終変更時間
i18n_entity.DemoEntity2.fields.passwordField.label=パスワード入力
i18n_entity.DemoEntity2.fields.referenceField.label=引用
i18n_entity.DemoEntity2.fields.referenceListField.label=引用複数値
i18n_entity.DemoEntity2.fields.ro.label=元組織
i18n_entity.DemoEntity2.fields.selectField.inlineOptionBill.items.v1.label=選択項目 1
i18n_entity.DemoEntity2.fields.selectField.inlineOptionBill.items.v2.label=選択項目 2
i18n_entity.DemoEntity2.fields.selectField.inlineOptionBill.items.v3.label=選択項目 3
i18n_entity.DemoEntity2.fields.selectField.inlineOptionBill.items.v4.label=選択項目 4
i18n_entity.DemoEntity2.fields.selectField.label=入力選択
i18n_entity.DemoEntity2.fields.selectListField.inlineOptionBill.items.v1.label=選択項目 1
i18n_entity.DemoEntity2.fields.selectListField.inlineOptionBill.items.v2.label=選択項目 2
i18n_entity.DemoEntity2.fields.selectListField.inlineOptionBill.items.v3.label=選択項目 3
i18n_entity.DemoEntity2.fields.selectListField.inlineOptionBill.items.v4.label=選択項目 4
i18n_entity.DemoEntity2.fields.selectListField.label=入力選択（複数値）
i18n_entity.DemoEntity2.fields.stringField.label=ファイル
i18n_entity.DemoEntity2.fields.stringListField.label=ファイル複数値
i18n_entity.DemoEntity2.fields.textAreaField.label=複数行ファイル入力
i18n_entity.DemoEntity2.fields.textAreaListField.label=複数行ファイル入力（複数値）
i18n_entity.DemoEntity2.fields.version.label=バージョン
i18n_entity.DemoEntity2.group=Test
i18n_entity.DemoEntity2.label=テスト実体2
i18n_entity.Department.fields.createdBy.label=作成者
i18n_entity.Department.fields.createdOn.label=作成時間
i18n_entity.Department.fields.disabled.label=無効
i18n_entity.Department.fields.id.label=番号
i18n_entity.Department.fields.leafNode.label=子部門
i18n_entity.Department.fields.level.label=階級
i18n_entity.Department.fields.modifiedBy.label=最終改訂人
i18n_entity.Department.fields.modifiedOn.label=最終変更時間
i18n_entity.Department.fields.name.label=名称
i18n_entity.Department.fields.owner.label=責任者
i18n_entity.Department.fields.parentNode.label=上級部門
i18n_entity.Department.fields.ro.label=元組織
i18n_entity.Department.fields.rootNode.label=トップ部門
i18n_entity.Department.fields.version.label=バージョン
i18n_entity.Department.group=User
i18n_entity.Department.label=部門
i18n_entity.Department.listCard.owner.prefix=責任者
i18n_entity.DirectRobotOrder.fields.createdBy.label=作成者
i18n_entity.DirectRobotOrder.fields.createdOn.label=作成時間
i18n_entity.DirectRobotOrder.fields.description.label=説明
i18n_entity.DirectRobotOrder.fields.doneOn.label=完了時間
i18n_entity.DirectRobotOrder.fields.errMsg.label=エラー原因
i18n_entity.DirectRobotOrder.fields.id.label=ID
i18n_entity.DirectRobotOrder.fields.modifiedBy.label=最終改訂人
i18n_entity.DirectRobotOrder.fields.modifiedOn.label=変更時間
i18n_entity.DirectRobotOrder.fields.moves.label=動作
i18n_entity.DirectRobotOrder.fields.robotName.label=ロボット名
i18n_entity.DirectRobotOrder.fields.seer3066.label=指定パスナビゲーション
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Cancelled.label=キャンセル済み
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Created.label=新規作成
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Done.label=完了
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Failed.label=失敗
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.ManualDone.label=手動完了
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Sent.label=発信済み
i18n_entity.DirectRobotOrder.fields.status.label=状態
i18n_entity.DirectRobotOrder.fields.status.listBatchButtons.buttons[0].label=キャンセル
i18n_entity.DirectRobotOrder.fields.status.listBatchButtons.buttons[1].label=手動完了
i18n_entity.DirectRobotOrder.fields.taskId.label=所属タスク
i18n_entity.DirectRobotOrder.fields.version.label=バージョン修正
i18n_entity.DirectRobotOrder.group=WCS
i18n_entity.DirectRobotOrder.label=直接運送リスト
i18n_entity.DirectRobotOrder.listCard.createdOn.prefix=作成
i18n_entity.DirectRobotOrder.listCard.description.prefix=説明
i18n_entity.DirectRobotOrder.listCard.robotName.prefix=ロボット
i18n_entity.DirectRobotOrder.listCard.taskId.prefix=所属タスク
i18n_entity.DirectRobotOrder.listStats.items[0].label=新規作成
i18n_entity.DirectRobotOrder.listStats.items[1].label=発信
i18n_entity.DirectRobotOrder.listStats.items[2].label=失敗
i18n_entity.EmptyContainerStoreOrder.fields.container.label=容器
i18n_entity.EmptyContainerStoreOrder.fields.containerType.label=容器タイプ
i18n_entity.EmptyContainerStoreOrder.fields.createdBy.label=作成者
i18n_entity.EmptyContainerStoreOrder.fields.createdOn.label=作成時間
i18n_entity.EmptyContainerStoreOrder.fields.id.label=ID
i18n_entity.EmptyContainerStoreOrder.fields.modifiedBy.label=最終改訂人
i18n_entity.EmptyContainerStoreOrder.fields.modifiedOn.label=最終変更時間
i18n_entity.EmptyContainerStoreOrder.fields.storeDistrict.label=格納エリア
i18n_entity.EmptyContainerStoreOrder.fields.version.label=バージョン修正
i18n_entity.EmptyContainerStoreOrder.group=Warehouse
i18n_entity.EmptyContainerStoreOrder.label=新容器荷役リスト
i18n_entity.EmptyContainerStoreOrder.listCard.container.prefix=容器
i18n_entity.EmptyContainerStoreOrder.listCard.storeDistrict.prefix=格納エリア
i18n_entity.EmptyContainerStoreOrder.pagesButtons.ListMain.buttons[0].label=新規作成
i18n_entity.EmptyContainerStoreOrder.pagesButtons.ListMain.buttons[1].label=削除
i18n_entity.EmptyContainerStoreOrder.pagesButtons.ListMain.buttons[2].label=空箱置き済み
i18n_entity.EntityChangedRecord.fields.changeType.label=変更タイプ
i18n_entity.EntityChangedRecord.fields.createdBy.label=作成者
i18n_entity.EntityChangedRecord.fields.createdOn.label=作成時間
i18n_entity.EntityChangedRecord.fields.entityFields.label=フィールドリスト
i18n_entity.EntityChangedRecord.fields.entityId.label=実体 ID
i18n_entity.EntityChangedRecord.fields.entityName.label=実体名
i18n_entity.EntityChangedRecord.fields.id.label=ID
i18n_entity.EntityChangedRecord.fields.modifiedBy.label=最終改訂人
i18n_entity.EntityChangedRecord.fields.modifiedOn.label=最終変更時間
i18n_entity.EntityChangedRecord.fields.version.label=バージョン修正
i18n_entity.EntityChangedRecord.group=Core
i18n_entity.EntityChangedRecord.label=実体変更履歴
i18n_entity.EntityComment.fields.content.label=内容
i18n_entity.EntityComment.fields.createdBy.label=作成者
i18n_entity.EntityComment.fields.createdOn.label=作成時間
i18n_entity.EntityComment.fields.entityId.label=entityId
i18n_entity.EntityComment.fields.entityName.label=実体
i18n_entity.EntityComment.fields.id.label=番号
i18n_entity.EntityComment.fields.modifiedBy.label=最終改訂人
i18n_entity.EntityComment.fields.modifiedOn.label=最終変更時間
i18n_entity.EntityComment.fields.ro.label=企業
i18n_entity.EntityComment.fields.version.label=バージョン
i18n_entity.EntityComment.group=Core
i18n_entity.EntityComment.label=実体評価
i18n_entity.EntitySyncRecord.fields.bzType.label=業務タイプ
i18n_entity.EntitySyncRecord.fields.cost.label=経過時間（ミリ秒）
i18n_entity.EntitySyncRecord.fields.createdBy.label=作成者
i18n_entity.EntitySyncRecord.fields.createdCount.label=実体数量新規追加
i18n_entity.EntitySyncRecord.fields.createdOn.label=作成時間
i18n_entity.EntitySyncRecord.fields.deletedCount.label=実体数量削除
i18n_entity.EntitySyncRecord.fields.entityName.label=実体名
i18n_entity.EntitySyncRecord.fields.faileReason.label=失敗原因
i18n_entity.EntitySyncRecord.fields.id.label=ID
i18n_entity.EntitySyncRecord.fields.modifiedBy.label=最終改訂人
i18n_entity.EntitySyncRecord.fields.modifiedOn.label=最終変更時間
i18n_entity.EntitySyncRecord.fields.oldCount.label=以前実体数量同期
i18n_entity.EntitySyncRecord.fields.ro.label=企業
i18n_entity.EntitySyncRecord.fields.success.label=成功
i18n_entity.EntitySyncRecord.fields.syncCount.label=同步実体数量
i18n_entity.EntitySyncRecord.fields.syncOn.label=同步時間
i18n_entity.EntitySyncRecord.fields.txId.label=事務ID
i18n_entity.EntitySyncRecord.fields.updatedCount.label=実体数量更新
i18n_entity.EntitySyncRecord.fields.version.label=バージョン
i18n_entity.EntitySyncRecord.group=Core
i18n_entity.EntitySyncRecord.label=実体同期履歴
i18n_entity.ExternalCallRecord.fields.createdBy.label=作成者
i18n_entity.ExternalCallRecord.fields.createdOn.label=作成時間
i18n_entity.ExternalCallRecord.fields.doneOn.label=完了時間
i18n_entity.ExternalCallRecord.fields.failedNum.label=失敗回数
i18n_entity.ExternalCallRecord.fields.failedReason.label=失敗原因
i18n_entity.ExternalCallRecord.fields.id.label=ID
i18n_entity.ExternalCallRecord.fields.modifiedBy.label=最終改訂人
i18n_entity.ExternalCallRecord.fields.modifiedOn.label=最終変更時間
i18n_entity.ExternalCallRecord.fields.okChecker.label=検査成功方法
i18n_entity.ExternalCallRecord.fields.options.label=選択項目
i18n_entity.ExternalCallRecord.fields.req.label=リクエスト詳細
i18n_entity.ExternalCallRecord.fields.resBody.label=応答テキスト
i18n_entity.ExternalCallRecord.fields.resCode.label=応答コード
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Aborted.label=終了
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Cancelled.label=キャンセル済み
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Done.label=成功
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Failed.label=失敗
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Init.label=開始
i18n_entity.ExternalCallRecord.fields.status.label=状態
i18n_entity.ExternalCallRecord.fields.url.label=URL
i18n_entity.ExternalCallRecord.fields.version.label=バージョン修正
i18n_entity.ExternalCallRecord.group=Core
i18n_entity.ExternalCallRecord.label=第三者配車履歴
i18n_entity.ExternalCallTrace.fields.createdBy.label=作成者
i18n_entity.ExternalCallTrace.fields.createdOn.label=作成時間
i18n_entity.ExternalCallTrace.fields.id.label=ID
i18n_entity.ExternalCallTrace.fields.ioError.label=通信エラー
i18n_entity.ExternalCallTrace.fields.ioError.view.trueText=IO エラー
i18n_entity.ExternalCallTrace.fields.ioErrorMsg.label=通信エラー原因
i18n_entity.ExternalCallTrace.fields.method.label=HTTP方法
i18n_entity.ExternalCallTrace.fields.modifiedBy.label=最終改訂人
i18n_entity.ExternalCallTrace.fields.modifiedOn.label=最終変更時間
i18n_entity.ExternalCallTrace.fields.reqBody.label=リクエストテキスト
i18n_entity.ExternalCallTrace.fields.reqOn.label=リクエスト時間
i18n_entity.ExternalCallTrace.fields.resBody.label=応答テキスト
i18n_entity.ExternalCallTrace.fields.resCode.label=応答コード
i18n_entity.ExternalCallTrace.fields.resOn.label=応答時間
i18n_entity.ExternalCallTrace.fields.url.label=URL
i18n_entity.ExternalCallTrace.fields.version.label=バージョン修正
i18n_entity.ExternalCallTrace.group=Core
i18n_entity.ExternalCallTrace.label=HTTP 顧客側ログ
i18n_entity.FailureRecord.fields.createdBy.label=作成者
i18n_entity.FailureRecord.fields.createdOn.label=作成時間
i18n_entity.FailureRecord.fields.desc.label=説明
i18n_entity.FailureRecord.fields.firstOn.label=初回発生時間
i18n_entity.FailureRecord.fields.id.label=ID
i18n_entity.FailureRecord.fields.kind.label=区分
i18n_entity.FailureRecord.fields.lastOn.label=最新発生時間
i18n_entity.FailureRecord.fields.level.inlineOptionBill.items.Fatal.label=厳重
i18n_entity.FailureRecord.fields.level.inlineOptionBill.items.Warning.label=警告
i18n_entity.FailureRecord.fields.level.label=レベル
i18n_entity.FailureRecord.fields.modifiedBy.label=最終改訂人
i18n_entity.FailureRecord.fields.modifiedOn.label=最終変更時間
i18n_entity.FailureRecord.fields.num.label=回数
i18n_entity.FailureRecord.fields.part.label=対象
i18n_entity.FailureRecord.fields.source.label=りソース
i18n_entity.FailureRecord.fields.subKind.label=サブ区分
i18n_entity.FailureRecord.fields.version.label=バージョン修正
i18n_entity.FailureRecord.group=Core
i18n_entity.FailureRecord.label=故障履歴
i18n_entity.FalconBlockChildId.fields.blockId.label=blockId
i18n_entity.FalconBlockChildId.fields.childId.label=childId
i18n_entity.FalconBlockChildId.fields.contextKey.label=contextKey
i18n_entity.FalconBlockChildId.fields.createdBy.label=作成者
i18n_entity.FalconBlockChildId.fields.createdOn.label=作成時間
i18n_entity.FalconBlockChildId.fields.id.label=ID
i18n_entity.FalconBlockChildId.fields.index.label=index
i18n_entity.FalconBlockChildId.fields.modifiedBy.label=最終改訂人
i18n_entity.FalconBlockChildId.fields.modifiedOn.label=変更時間
i18n_entity.FalconBlockChildId.fields.taskId.label=taskId
i18n_entity.FalconBlockChildId.fields.version.label=バージョン修正
i18n_entity.FalconBlockChildId.group=Falcon
i18n_entity.FalconBlockChildId.label=猎鹰タスクブロック ID
i18n_entity.FalconBlockChildId.listCard.contextKey.prefix=contextKey
i18n_entity.FalconBlockChildId.listCard.createdOn.prefix=新規作成
i18n_entity.FalconBlockChildId.listCard.taskId.prefix=taskId
i18n_entity.FalconBlockRecord.fields.blockConfigId.label=blockConfigId
i18n_entity.FalconBlockRecord.fields.createdBy.label=作成者
i18n_entity.FalconBlockRecord.fields.createdOn.label=作成時間
i18n_entity.FalconBlockRecord.fields.endedOn.label=endedOn
i18n_entity.FalconBlockRecord.fields.endedReason.label=endedReason
i18n_entity.FalconBlockRecord.fields.failureNum.label=失敗回数
i18n_entity.FalconBlockRecord.fields.id.label=ID
i18n_entity.FalconBlockRecord.fields.inputParams.label=inputParams
i18n_entity.FalconBlockRecord.fields.internalVariables.label=internalVariables
i18n_entity.FalconBlockRecord.fields.modifiedBy.label=最終改訂人
i18n_entity.FalconBlockRecord.fields.modifiedOn.label=変更時間
i18n_entity.FalconBlockRecord.fields.outputParams.label=outputParams
i18n_entity.FalconBlockRecord.fields.startedOn.label=startedOn
i18n_entity.FalconBlockRecord.fields.status.label=status
i18n_entity.FalconBlockRecord.fields.taskId.label=taskId
i18n_entity.FalconBlockRecord.fields.version.label=バージョン修正
i18n_entity.FalconBlockRecord.group=Falcon
i18n_entity.FalconBlockRecord.label=猎鹰タスクブロック履歴
i18n_entity.FalconBlockRecord.listCard.blockConfigId.prefix=blockConfigId
i18n_entity.FalconBlockRecord.listCard.taskId.prefix=taskId
i18n_entity.FalconLog.fields.blockId.label=コンポーネント紐付け
i18n_entity.FalconLog.fields.createdBy.label=作成者
i18n_entity.FalconLog.fields.createdOn.label=作成時間
i18n_entity.FalconLog.fields.id.label=ID
i18n_entity.FalconLog.fields.level.inlineOptionBill.items.Debug.label=普通
i18n_entity.FalconLog.fields.level.inlineOptionBill.items.Error.label=エラー
i18n_entity.FalconLog.fields.level.inlineOptionBill.items.Info.label=重要
i18n_entity.FalconLog.fields.level.label=レベル
i18n_entity.FalconLog.fields.message.label=情報
i18n_entity.FalconLog.fields.modifiedBy.label=最終改訂人
i18n_entity.FalconLog.fields.modifiedOn.label=変更時間
i18n_entity.FalconLog.fields.taskId.label=タスク紐付け
i18n_entity.FalconLog.fields.version.label=バージョン修正
i18n_entity.FalconLog.group=Falcon
i18n_entity.FalconLog.label=猎鹰ログ
i18n_entity.FalconLog.listCard.blockId.prefix=コンポーネント紐付け
i18n_entity.FalconLog.listCard.createdOn.prefix=新規作成
i18n_entity.FalconRelatedObject.fields.createdBy.label=作成者
i18n_entity.FalconRelatedObject.fields.createdOn.label=作成時間
i18n_entity.FalconRelatedObject.fields.id.label=ID
i18n_entity.FalconRelatedObject.fields.modifiedBy.label=最終改訂人
i18n_entity.FalconRelatedObject.fields.modifiedOn.label=最終変更時間
i18n_entity.FalconRelatedObject.fields.objectArgs.label=objectArgs
i18n_entity.FalconRelatedObject.fields.objectId.label=objectId
i18n_entity.FalconRelatedObject.fields.objectType.label=objectType
i18n_entity.FalconRelatedObject.fields.taskId.label=taskId
i18n_entity.FalconRelatedObject.fields.version.label=バージョン修正
i18n_entity.FalconRelatedObject.group=Falcon
i18n_entity.FalconRelatedObject.label=猎鹰タスク関連対象
i18n_entity.FalconRelatedObject.listCard.objectArgs.prefix=objectArgs
i18n_entity.FalconRelatedObject.listCard.objectId.prefix=objectId
i18n_entity.FalconTaskRecord.fields.actualRobots.label=ロボット実行
i18n_entity.FalconTaskRecord.fields.createdBy.label=作成者
i18n_entity.FalconTaskRecord.fields.createdOn.label=作成時間
i18n_entity.FalconTaskRecord.fields.defId.label=ID定義
i18n_entity.FalconTaskRecord.fields.defLabel.label=タスクテンプレート
i18n_entity.FalconTaskRecord.fields.defVersion.label=テンプレートバージョン
i18n_entity.FalconTaskRecord.fields.endedOn.label=終了時間
i18n_entity.FalconTaskRecord.fields.endedReason.label=エラー原因
i18n_entity.FalconTaskRecord.fields.failureNum.label=故障回数
i18n_entity.FalconTaskRecord.fields.id.label=ID
i18n_entity.FalconTaskRecord.fields.inputParams.label=パラメーター入力
i18n_entity.FalconTaskRecord.fields.modifiedBy.label=最終改訂人
i18n_entity.FalconTaskRecord.fields.modifiedOn.label=変更時間
i18n_entity.FalconTaskRecord.fields.paused.label=一時停止
i18n_entity.FalconTaskRecord.fields.ro.label=RO
i18n_entity.FalconTaskRecord.fields.rootBlockStateId.label=元ブロックID
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.100.label=作成済み
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.120.label=開始済み
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.140.label=故障
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.160.label=完了
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.180.label=キャンセル済み
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.190.label=放棄済み
i18n_entity.FalconTaskRecord.fields.status.label=状態
i18n_entity.FalconTaskRecord.fields.subTask.label=サブタスク
i18n_entity.FalconTaskRecord.fields.topTaskId.label=トップフロアタスク ID
i18n_entity.FalconTaskRecord.fields.variables.label=タスク变量
i18n_entity.FalconTaskRecord.fields.version.label=バージョン修正
i18n_entity.FalconTaskRecord.group=Falcon
i18n_entity.FalconTaskRecord.label=猎鹰タスク履歴
i18n_entity.FalconTaskRecord.listCard.createdOn.prefix=新規作成
i18n_entity.FalconTaskRecord.listCard.defLabel.prefix=テンプレート
i18n_entity.FalconTaskRecord.listCard.defVersion.prefix=(
i18n_entity.FalconTaskRecord.listCard.defVersion.suffix=)
i18n_entity.FalconTaskRecord.listCard.endedOn.prefix=終了
i18n_entity.FalconTaskRecord.listStats.items[0].label=故障
i18n_entity.FalconTaskRecord.listStats.items[1].label=一時停止
i18n_entity.FalconTaskRecord.listStats.items[2].label=本日新規追加
i18n_entity.FalconTaskRecord.listStats.items[3].label=本日キャンセル失敗
i18n_entity.FalconTaskRecord.listStats.items[4].label=今週新規追加
i18n_entity.FalconTaskRecord.listStats.items[5].label=今週キャンセル失敗
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[0].label=削除
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[1].label=エクスポート
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[2].label=実行一時停止
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[3].label=実行継続
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[4].label=故障再試行
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[5].label=実行キャンセル
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[6].label=削除
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[7].label=テンプレート
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[8].label=全局制御
i18n_entity.FalconTaskResource.fields.args.label=パラメーター
i18n_entity.FalconTaskResource.fields.createdBy.label=作成者
i18n_entity.FalconTaskResource.fields.createdOn.label=作成時間
i18n_entity.FalconTaskResource.fields.id.label=ID
i18n_entity.FalconTaskResource.fields.modifiedBy.label=最終改訂人
i18n_entity.FalconTaskResource.fields.modifiedOn.label=最終変更時間
i18n_entity.FalconTaskResource.fields.resId.label=りソース ID
i18n_entity.FalconTaskResource.fields.resType.label=りソースタイプ
i18n_entity.FalconTaskResource.fields.taskId.label=タスク番号
i18n_entity.FalconTaskResource.fields.version.label=バージョン修正
i18n_entity.FalconTaskResource.group=Falcon
i18n_entity.FalconTaskResource.label=猎鹰タスクりソース
i18n_entity.FalconTaskResource.listCard.resId.prefix=りソース ID
i18n_entity.FbAssemblyLine.fields.building.label=建物
i18n_entity.FbAssemblyLine.fields.buildingLayer.label=フロア
i18n_entity.FbAssemblyLine.fields.createdBy.label=作成者
i18n_entity.FbAssemblyLine.fields.createdOn.label=作成時間
i18n_entity.FbAssemblyLine.fields.disabled.label=無効
i18n_entity.FbAssemblyLine.fields.id.label=リスト番号
i18n_entity.FbAssemblyLine.fields.modifiedBy.label=最終改訂人
i18n_entity.FbAssemblyLine.fields.modifiedOn.label=最終変更時間
i18n_entity.FbAssemblyLine.fields.name.label=名称
i18n_entity.FbAssemblyLine.fields.remark.label=備考
i18n_entity.FbAssemblyLine.fields.ro.label=元組織
i18n_entity.FbAssemblyLine.fields.version.label=バージョン
i18n_entity.FbAssemblyLine.group=MainData
i18n_entity.FbAssemblyLine.label=生産ライン
i18n_entity.FbBin.fields.assemblyLine.label=所属生産ライン
i18n_entity.FbBin.fields.boxDirection.label=フォーク方向
i18n_entity.FbBin.fields.boxHeight.label=フォークリフティング高度
i18n_entity.FbBin.fields.btDisabled.label=使用停止
i18n_entity.FbBin.fields.channel.label=所在サブレーン
i18n_entity.FbBin.fields.column.label=列
i18n_entity.FbBin.fields.container.label=ロケーション上容器
i18n_entity.FbBin.fields.createdBy.label=作成者
i18n_entity.FbBin.fields.createdOn.label=作成時間
i18n_entity.FbBin.fields.depth.label=奥
i18n_entity.FbBin.fields.district.label=所属倉庫エリア
i18n_entity.FbBin.fields.id.label=ロケーション番号
i18n_entity.FbBin.fields.layer.label=フロア
i18n_entity.FbBin.fields.loadStatus.inlineOptionBill.items.Empty.label=未占有
i18n_entity.FbBin.fields.loadStatus.inlineOptionBill.items.Occupied.label=占有済み
i18n_entity.FbBin.fields.loadStatus.inlineOptionBill.items.ToLoad.label=まもなく到着予定
i18n_entity.FbBin.fields.loadStatus.inlineOptionBill.items.ToUnload.label=まもなく発送予定
i18n_entity.FbBin.fields.loadStatus.label=占有状態
i18n_entity.FbBin.fields.locked.label=ロック
i18n_entity.FbBin.fields.locked.listBatchButtons.buttons[0].label=ロック解除
i18n_entity.FbBin.fields.lockedBy.label=ロック原因
i18n_entity.FbBin.fields.materialCategoryLabel.label=格納商品分類名称
i18n_entity.FbBin.fields.modifiedBy.label=最終改訂人
i18n_entity.FbBin.fields.modifiedOn.label=最終変更時間
i18n_entity.FbBin.fields.occupied.label=在庫あり
i18n_entity.FbBin.fields.occupied.view.trueText=在庫あり
i18n_entity.FbBin.fields.pendingContainer.label=運ばれる予定の容器
i18n_entity.FbBin.fields.purpose.label=用途
i18n_entity.FbBin.fields.rack.label=所属ラック
i18n_entity.FbBin.fields.remark.label=備考
i18n_entity.FbBin.fields.ro.label=元組織
i18n_entity.FbBin.fields.robotDirection.label=ロボット方向
i18n_entity.FbBin.fields.robotX.label=ロボット位置 X
i18n_entity.FbBin.fields.robotY.label=ロボット位置 Y
i18n_entity.FbBin.fields.row.label=列
i18n_entity.FbBin.fields.version.label=バージョン
i18n_entity.FbBin.fields.warehouse.label=所属倉庫
i18n_entity.FbBin.fields.workSite.label=所属ワークテーブル
i18n_entity.FbBin.group=MainData
i18n_entity.FbBin.label=ロケーション
i18n_entity.FbBin.listCard.container.prefix=ロケーション上容器
i18n_entity.FbBin.listCard.district.prefix=所属倉庫エリア
i18n_entity.FbBin.listStats.items[0].label=処理中（ロック）
i18n_entity.FbBin.listStats.items[1].label=在庫あり
i18n_entity.FbBin.listStats.items[2].label=到着予定
i18n_entity.FbBin.listStats.items[3].label=出発予定
i18n_entity.FbBin.pagesButtons.ListMain.buttons[0].label=新規追加
i18n_entity.FbBin.pagesButtons.ListMain.buttons[1].label=ロット編集
i18n_entity.FbBin.pagesButtons.ListMain.buttons[2].label=削除
i18n_entity.FbBin.pagesButtons.ListMain.buttons[3].label=エクスポート
i18n_entity.FbBin.pagesButtons.ListMain.buttons[4].label=インポート
i18n_entity.FbBin.pagesButtons.ListMain.buttons[5].label=ロケーションロット作成
i18n_entity.FbContainer.fields.bin.label=所在ロケーション
i18n_entity.FbContainer.fields.btDisabled.label=使用停止
i18n_entity.FbContainer.fields.createdBy.label=作成者
i18n_entity.FbContainer.fields.createdOn.label=作成時間
i18n_entity.FbContainer.fields.district.label=現在倉庫エリア
i18n_entity.FbContainer.fields.filled.label=在庫あり
i18n_entity.FbContainer.fields.fixedStoreBin.label=ロケーション格納指定
i18n_entity.FbContainer.fields.height.label=容器高さ
i18n_entity.FbContainer.fields.length.label=容器長さ
i18n_entity.FbContainer.fields.locked.label=ロック
i18n_entity.FbContainer.fields.locked.listBatchButtons.buttons[0].label=ロック解除
i18n_entity.FbContainer.fields.maxWeight.label=容器承重
i18n_entity.FbContainer.fields.modifiedBy.label=最終改訂人
i18n_entity.FbContainer.fields.modifiedOn.label=最終変更時間
i18n_entity.FbContainer.fields.onRobot.label=所在ロボット
i18n_entity.FbContainer.fields.pContainer.label=親容器
i18n_entity.FbContainer.fields.preBin.label=格納ロケーション予定
i18n_entity.FbContainer.fields.qcResult.label=品質検査結果i18n_entity.FbContainer.fields.id.label
i18n_entity.FbContainer.fields.remark.label=備考
i18n_entity.FbContainer.fields.ro.label=元組織
i18n_entity.FbContainer.fields.state.label=状態
i18n_entity.FbContainer.fields.subNum.label=格子数
i18n_entity.FbContainer.fields.targetBin.label=搬送予定のロケーション
i18n_entity.FbContainer.fields.taskType.inlineOptionBill.items.Count.label=棚卸中
i18n_entity.FbContainer.fields.taskType.inlineOptionBill.items.Pick.label=ピッキング待ち
i18n_entity.FbContainer.fields.taskType.inlineOptionBill.items.Put.label=箱入り待ち
i18n_entity.FbContainer.fields.taskType.label=タスクタイプ
i18n_entity.FbContainer.fields.type.label=容器タイプ
i18n_entity.FbContainer.fields.version.label=バージョン
i18n_entity.FbContainer.fields.volume.label=容器容積
i18n_entity.FbContainer.fields.warehouse.label=現在倉庫
i18n_entity.FbContainer.fields.width.label=容器幅
i18n_entity.FbContainer.fields.workStatus.inlineOptionBill.items.ToPick.label=ピッキング待ち
i18n_entity.FbContainer.fields.workStatus.inlineOptionBill.items.ToPut.label=積荷待ち
i18n_entity.FbContainer.fields.workStatus.label=ワーク状態
i18n_entity.FbContainer.group=MainData
i18n_entity.FbContainer.label=容器
i18n_entity.FbContainer.listCard.bin.prefix=所在ロケーション
i18n_entity.FbContainer.listCard.type.prefix=タイプ
i18n_entity.FbContainer.listStats.items[0].label=処理者中（ロック）
i18n_entity.FbContainer.listStats.items[1].label=在庫あり
i18n_entity.FbContainer.listStats.items[2].label=ロケーションにない
i18n_entity.FbContainerType.fields.btDisabled.label=使用停止
i18n_entity.FbContainerType.fields.createdBy.label=作成者
i18n_entity.FbContainerType.fields.createdOn.label=作成時間
i18n_entity.FbContainerType.fields.id.label=容器タイプコード
i18n_entity.FbContainerType.fields.mixedMaterial.label=商品混載
i18n_entity.FbContainerType.fields.modifiedBy.label=最終改訂人
i18n_entity.FbContainerType.fields.modifiedOn.label=最終変更時間
i18n_entity.FbContainerType.fields.name.label=容器タイプ名称
i18n_entity.FbContainerType.fields.remark.label=説明
i18n_entity.FbContainerType.fields.ro.label=元組織
i18n_entity.FbContainerType.fields.storeDistricts.label=格納倉庫エリア
i18n_entity.FbContainerType.fields.subNum.label=分格数
i18n_entity.FbContainerType.fields.version.label=バージョン
i18n_entity.FbContainerType.group=MainData
i18n_entity.FbContainerType.label=容器タイプ
i18n_entity.FbContainerType.listCard.storeDistricts.prefix=格納倉庫エリア
i18n_entity.FbCountDiffLine.fields.actualQty.label=実際数量
i18n_entity.FbCountDiffLine.fields.bin.label=本来格納ロケーション
i18n_entity.FbCountDiffLine.fields.btLineNo.label=リスト行番号
i18n_entity.FbCountDiffLine.fields.btMaterial.label=商品
i18n_entity.FbCountDiffLine.fields.btMaterialCategory.label=商品分類番号
i18n_entity.FbCountDiffLine.fields.btMaterialCategoryName.label=商品分類名称
i18n_entity.FbCountDiffLine.fields.btMaterialId.label=商品番号
i18n_entity.FbCountDiffLine.fields.btMaterialImage.label=商品イメージ図
i18n_entity.FbCountDiffLine.fields.btMaterialModel.label=商品型番
i18n_entity.FbCountDiffLine.fields.btMaterialName.label=商品名称
i18n_entity.FbCountDiffLine.fields.btMaterialSpec.label=商品仕様
i18n_entity.FbCountDiffLine.fields.btParentId.label=所属受注オーダー
i18n_entity.FbCountDiffLine.fields.container.label=容器
i18n_entity.FbCountDiffLine.fields.createdBy.label=作成者
i18n_entity.FbCountDiffLine.fields.createdOn.label=作成時間
i18n_entity.FbCountDiffLine.fields.diffQty.label=差異数量
i18n_entity.FbCountDiffLine.fields.fix.label=実行
i18n_entity.FbCountDiffLine.fields.id.label=ID
i18n_entity.FbCountDiffLine.fields.lotNo.label=ロット番号
i18n_entity.FbCountDiffLine.fields.modifiedBy.label=最終改訂人
i18n_entity.FbCountDiffLine.fields.modifiedOn.label=変更時間
i18n_entity.FbCountDiffLine.fields.qty.label=本来数量
i18n_entity.FbCountDiffLine.fields.qualityLevel.label=品質レベル
i18n_entity.FbCountDiffLine.fields.remark.label=備考
i18n_entity.FbCountDiffLine.fields.sourceInvLayout.label=在庫明細紐付け
i18n_entity.FbCountDiffLine.fields.subContainerId.label=格子番号
i18n_entity.FbCountDiffLine.fields.taskId.label=棚卸タスク
i18n_entity.FbCountDiffLine.fields.version.label=バージョン修正
i18n_entity.FbCountDiffLine.group=Warehouse
i18n_entity.FbCountDiffLine.label=棚卸差損記録行
i18n_entity.FbCountDiffLine.listCard.btMaterial.prefix=商品
i18n_entity.FbCountDiffLine.listCard.subContainerId.prefix=格子番号
i18n_entity.FbCountFix.fields.btMaterial.label=商品
i18n_entity.FbCountFix.fields.btMaterialCategory.label=商品分類番号
i18n_entity.FbCountFix.fields.btMaterialCategoryName.label=商品分類名称
i18n_entity.FbCountFix.fields.btMaterialId.label=商品番号
i18n_entity.FbCountFix.fields.btMaterialImage.label=商品イメージ図
i18n_entity.FbCountFix.fields.btMaterialModel.label=商品型番
i18n_entity.FbCountFix.fields.btMaterialName.label=商品名称
i18n_entity.FbCountFix.fields.btMaterialSpec.label=商品仕様
i18n_entity.FbCountFix.fields.container.label=容器
i18n_entity.FbCountFix.fields.createdBy.label=作成者
i18n_entity.FbCountFix.fields.createdOn.label=作成時間
i18n_entity.FbCountFix.fields.id.label=ID
i18n_entity.FbCountFix.fields.lotNo.label=ロット番号
i18n_entity.FbCountFix.fields.modifiedBy.label=最終改訂人
i18n_entity.FbCountFix.fields.modifiedOn.label=変更時間
i18n_entity.FbCountFix.fields.qty.label=变更数量
i18n_entity.FbCountFix.fields.qualityLevel.label=品質レベル
i18n_entity.FbCountFix.fields.remark.label=備考
i18n_entity.FbCountFix.fields.subContainerId.label=格子番号
i18n_entity.FbCountFix.fields.version.label=バージョン修正
i18n_entity.FbCountFix.group=Warehouse
i18n_entity.FbCountFix.label=在庫修正履歴
i18n_entity.FbCountFix.listCard.container.prefix=容器
i18n_entity.FbCountFix.listCard.qty.prefix=变更数量
i18n_entity.FbCountFix.listCard.subContainerId.prefix=格子番号
i18n_entity.FbCountOrder.fields.bins.label=棚卸予定ロケーションリスト
i18n_entity.FbCountOrder.fields.btOrderState.inlineOptionBill.items.Done.label=完了
i18n_entity.FbCountOrder.fields.btOrderState.inlineOptionBill.items.Init.label=未提出
i18n_entity.FbCountOrder.fields.btOrderState.inlineOptionBill.items.Submitted.label=提出済み
i18n_entity.FbCountOrder.fields.btOrderState.label=業務状態
i18n_entity.FbCountOrder.fields.btOrderStateReason.label=状態説明
i18n_entity.FbCountOrder.fields.containers.label=棚卸予定容器リスト
i18n_entity.FbCountOrder.fields.createdBy.label=作成者
i18n_entity.FbCountOrder.fields.createdOn.label=作成時間
i18n_entity.FbCountOrder.fields.diffLines.label=棚卸差損リスト
i18n_entity.FbCountOrder.fields.districts.label=棚卸予定倉庫エリアリスト
i18n_entity.FbCountOrder.fields.doneProcessed.label=棚卸差損処理済み
i18n_entity.FbCountOrder.fields.id.label=リスト番号
i18n_entity.FbCountOrder.fields.materials.label=棚卸予定商品リスト
i18n_entity.FbCountOrder.fields.modifiedBy.label=最終改訂人
i18n_entity.FbCountOrder.fields.modifiedOn.label=変更時間
i18n_entity.FbCountOrder.fields.remark.label=説明
i18n_entity.FbCountOrder.fields.taskGenerated.label=棚卸タスク已生成
i18n_entity.FbCountOrder.fields.taskLines.label=タスク统计行
i18n_entity.FbCountOrder.fields.version.label=バージョン修正
i18n_entity.FbCountOrder.group=Warehouse
i18n_entity.FbCountOrder.label=棚卸リスト
i18n_entity.FbCountOrder.states.states.Done.label=完了
i18n_entity.FbCountOrder.states.states.Init.label=未提出
i18n_entity.FbCountOrder.states.states.Init.nextStates.Submitted.buttonLabel=提出
i18n_entity.FbCountOrder.states.states.Submitted.label=提出済み
i18n_entity.FbCountOrder.states.states.Submitted.nextStates.Done.buttonLabel=棚卸差損実行
i18n_entity.FbCountTask.fields.bin.label=元ロケーション
i18n_entity.FbCountTask.fields.btLines.label=シングル行
i18n_entity.FbCountTask.fields.btOrderState.inlineOptionBill.items.Init.label=棚卸待ち
i18n_entity.FbCountTask.fields.btOrderState.inlineOptionBill.items.Submitted.label=棚卸済み
i18n_entity.FbCountTask.fields.btOrderState.label=業務状態
i18n_entity.FbCountTask.fields.btOrderStateReason.label=状態説明
i18n_entity.FbCountTask.fields.container.label=棚卸容器
i18n_entity.FbCountTask.fields.containerInOrderId.label=容器入庫運送リスト番号
i18n_entity.FbCountTask.fields.containerOutOrderId.label=容器出庫運送リスト番号
i18n_entity.FbCountTask.fields.countOrderId.label=所属棚卸リスト
i18n_entity.FbCountTask.fields.createdBy.label=作成者
i18n_entity.FbCountTask.fields.createdOn.label=作成時間
i18n_entity.FbCountTask.fields.id.label=ID
i18n_entity.FbCountTask.fields.modifiedBy.label=最終改訂人
i18n_entity.FbCountTask.fields.modifiedOn.label=最終変更時間
i18n_entity.FbCountTask.fields.remark.label=説明
i18n_entity.FbCountTask.fields.submittedPostProcessed.label=提出後処理完了
i18n_entity.FbCountTask.fields.version.label=バージョン修正
i18n_entity.FbCountTask.group=Warehouse
i18n_entity.FbCountTask.label=棚卸タスク
i18n_entity.FbCountTask.listCard.bin.prefix=ロケーション
i18n_entity.FbCountTask.listCard.container.prefix=容器
i18n_entity.FbCountTask.listStats.items[0].label=棚卸待ち
i18n_entity.FbCountTask.states.states.Init.label=棚卸待ち
i18n_entity.FbCountTask.states.states.Init.nextStates.Submitted.buttonLabel=提出
i18n_entity.FbCountTask.states.states.Submitted.label=棚卸済み
i18n_entity.FbCountTaskLine.fields.actualQty.label=実際数量
i18n_entity.FbCountTaskLine.fields.amount.label=金額
i18n_entity.FbCountTaskLine.fields.bin.label=所在ロケーション
i18n_entity.FbCountTaskLine.fields.btLineNo.label=行番号
i18n_entity.FbCountTaskLine.fields.btMaterial.label=商品
i18n_entity.FbCountTaskLine.fields.btMaterialCategory.label=商品分類番号
i18n_entity.FbCountTaskLine.fields.btMaterialCategoryName.label=商品分類名称
i18n_entity.FbCountTaskLine.fields.btMaterialId.label=商品番号
i18n_entity.FbCountTaskLine.fields.btMaterialImage.label=商品イメージ図
i18n_entity.FbCountTaskLine.fields.btMaterialModel.label=商品型番
i18n_entity.FbCountTaskLine.fields.btMaterialName.label=商品名称
i18n_entity.FbCountTaskLine.fields.btMaterialSpec.label=商品仕様
i18n_entity.FbCountTaskLine.fields.btParentId.label=所属タスク
i18n_entity.FbCountTaskLine.fields.createdBy.label=作成者
i18n_entity.FbCountTaskLine.fields.createdOn.label=作成時間
i18n_entity.FbCountTaskLine.fields.district.label=所在倉庫エリア
i18n_entity.FbCountTaskLine.fields.expDate.label=有效期間
i18n_entity.FbCountTaskLine.fields.id.label=ID
i18n_entity.FbCountTaskLine.fields.leafContainer.label=容器
i18n_entity.FbCountTaskLine.fields.lotNo.label=ロット番号
i18n_entity.FbCountTaskLine.fields.mfgDate.label=生産日付
i18n_entity.FbCountTaskLine.fields.modifiedBy.label=最終改訂人
i18n_entity.FbCountTaskLine.fields.modifiedOn.label=最終変更時間
i18n_entity.FbCountTaskLine.fields.owner.label=荷主
i18n_entity.FbCountTaskLine.fields.price.label=単価
i18n_entity.FbCountTaskLine.fields.qty.label=数量
i18n_entity.FbCountTaskLine.fields.qualityLevel.label=品質レベル
i18n_entity.FbCountTaskLine.fields.sourceInvLayout.label=在庫明細引用
i18n_entity.FbCountTaskLine.fields.subContainerId.label=格子
i18n_entity.FbCountTaskLine.fields.topContainer.label=外側容器
i18n_entity.FbCountTaskLine.fields.unitLabel.label=単位名称
i18n_entity.FbCountTaskLine.fields.vendor.label=サプライヤー
i18n_entity.FbCountTaskLine.fields.version.label=バージョン修正
i18n_entity.FbCountTaskLine.fields.warehouse.label=所在倉庫
i18n_entity.FbCountTaskLine.group=Warehouse
i18n_entity.FbCountTaskLine.label=棚卸タスク行
i18n_entity.FbCountTaskLine.listCard.actualQty.prefix=実際数量
i18n_entity.FbCountTaskLine.listCard.btMaterialId.prefix=商品番号
i18n_entity.FbCountTaskLine.listCard.lotNo.prefix=ロット号
i18n_entity.FbCountTaskLine.listCard.qty.prefix=在庫数量
i18n_entity.FbCountTaskStatLine.fields.bin.label=棚卸ロケーション
i18n_entity.FbCountTaskStatLine.fields.btLineNo.label=行号
i18n_entity.FbCountTaskStatLine.fields.btParentId.label=所属受注オーダー
i18n_entity.FbCountTaskStatLine.fields.container.label=棚卸容器
i18n_entity.FbCountTaskStatLine.fields.createdBy.label=作成者
i18n_entity.FbCountTaskStatLine.fields.createdOn.label=作成時間
i18n_entity.FbCountTaskStatLine.fields.id.label=ID
i18n_entity.FbCountTaskStatLine.fields.materialsNum.label=商品数記録
i18n_entity.FbCountTaskStatLine.fields.modifiedBy.label=最終改訂人
i18n_entity.FbCountTaskStatLine.fields.modifiedOn.label=最終変更時間
i18n_entity.FbCountTaskStatLine.fields.qty.label=総数量記録
i18n_entity.FbCountTaskStatLine.fields.taskId.label=棚卸タスク
i18n_entity.FbCountTaskStatLine.fields.version.label=バージョン修正
i18n_entity.FbCountTaskStatLine.group=Warehouse
i18n_entity.FbCountTaskStatLine.label=棚卸リストタスク統計行
i18n_entity.FbCountTaskStatLine.listCard.materialsNum.prefix=商品数
i18n_entity.FbCountTaskStatLine.listCard.qty.prefix=総数量
i18n_entity.FbCustomer.fields.address.label=アドレス
i18n_entity.FbCustomer.fields.btDisabled.label=使用停止
i18n_entity.FbCustomer.fields.contact.label=連絡先
i18n_entity.FbCustomer.fields.createdBy.label=作成者
i18n_entity.FbCustomer.fields.createdOn.label=作成時間
i18n_entity.FbCustomer.fields.id.label=顧客コード
i18n_entity.FbCustomer.fields.modifiedBy.label=最終改訂人
i18n_entity.FbCustomer.fields.modifiedOn.label=最終変更時間
i18n_entity.FbCustomer.fields.name.label=顧客名称
i18n_entity.FbCustomer.fields.phone.label=電話
i18n_entity.FbCustomer.fields.remark.label=備考
i18n_entity.FbCustomer.fields.ro.label=元組織
i18n_entity.FbCustomer.fields.version.label=バージョン
i18n_entity.FbCustomer.group=MainData
i18n_entity.FbCustomer.label=顧客
i18n_entity.FbCustomer.listCard.btDisabled.formatMapping[0].replaceText=使用停止済み
i18n_entity.FbCustomer.listCard.contact.prefix=連絡先
i18n_entity.FbCustomer.listCard.name.prefix=顧客名
i18n_entity.FbCustomer.listCard.phone.prefix=电话
i18n_entity.FbDepartment.fields.btDisabled.label=使用停止
i18n_entity.FbDepartment.fields.btHiLeafNode.label=葉ノード
i18n_entity.FbDepartment.fields.btHiNodeLevel.label=ノードレベル
i18n_entity.FbDepartment.fields.btHiParentNode.label=上級
i18n_entity.FbDepartment.fields.btHiRootNode.label=最上層ノード
i18n_entity.FbDepartment.fields.createdBy.label=作成者
i18n_entity.FbDepartment.fields.createdOn.label=作成時間
i18n_entity.FbDepartment.fields.id.label=番号
i18n_entity.FbDepartment.fields.modifiedBy.label=最終改訂人
i18n_entity.FbDepartment.fields.modifiedOn.label=最終変更時間
i18n_entity.FbDepartment.fields.name.label=名称
i18n_entity.FbDepartment.fields.owner.label=責任者
i18n_entity.FbDepartment.fields.ro.label=元組織
i18n_entity.FbDepartment.fields.version.label=バージョン
i18n_entity.FbDepartment.group=User
i18n_entity.FbDepartment.label=元組織
i18n_entity.FbDepartment.listCard.owner.prefix=責任者
i18n_entity.FbDevTask.fields.comments.label=評価
i18n_entity.FbDevTask.fields.createdBy.label=作成者
i18n_entity.FbDevTask.fields.createdOn.label=作成時間
i18n_entity.FbDevTask.fields.description.label=説明
i18n_entity.FbDevTask.fields.files.label=添付ファイル
i18n_entity.FbDevTask.fields.id.label=番号
i18n_entity.FbDevTask.fields.images.label=添付イメージ図
i18n_entity.FbDevTask.fields.kind.inlineOptionBill.items.Bug.label=欠陥
i18n_entity.FbDevTask.fields.kind.inlineOptionBill.items.Feature.label=機能
i18n_entity.FbDevTask.fields.kind.inlineOptionBill.items.Improvement.label=アップグレード
i18n_entity.FbDevTask.fields.kind.inlineOptionBill.items.Test.label=テスト
i18n_entity.FbDevTask.fields.kind.label=タイプ
i18n_entity.FbDevTask.fields.modifiedBy.label=最終改訂人
i18n_entity.FbDevTask.fields.modifiedOn.label=最終変更時間
i18n_entity.FbDevTask.fields.priority.inlineOptionBill.items.High.label=高
i18n_entity.FbDevTask.fields.priority.inlineOptionBill.items.Low.label=低
i18n_entity.FbDevTask.fields.priority.inlineOptionBill.items.Normal.label=中
i18n_entity.FbDevTask.fields.priority.label=優先級
i18n_entity.FbDevTask.fields.processedBy.label=処理者人
i18n_entity.FbDevTask.fields.project.label=プロジェクト
i18n_entity.FbDevTask.fields.ro.label=企業
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Closed.label=クローズ済み
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Open.label=新規作成
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Processing.label=処理中
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Rejected.label=拒否済み
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Resolved.label=解決済み
i18n_entity.FbDevTask.fields.state.label=状態
i18n_entity.FbDevTask.fields.testImages.label=テストイメージ図
i18n_entity.FbDevTask.fields.testResult.label=テスト結果i18n_entity.FbDevTask.fields.devVersion.label
i18n_entity.FbDevTask.fields.title.label=タイトル
i18n_entity.FbDevTask.fields.version.label=バージョン
i18n_entity.FbDevTask.group=Dev
i18n_entity.FbDevTask.label=協同タスク
i18n_entity.FbDevVersion.fields.createdBy.label=作成者
i18n_entity.FbDevVersion.fields.createdOn.label=作成時間
i18n_entity.FbDevVersion.fields.displayOrder.label=順序表示
i18n_entity.FbDevVersion.fields.done.label=完了
i18n_entity.FbDevVersion.fields.id.label=ID
i18n_entity.FbDevVersion.fields.modifiedBy.label=最終改訂人
i18n_entity.FbDevVersion.fields.modifiedOn.label=最終変更時間
i18n_entity.FbDevVersion.fields.name.label=名称
i18n_entity.FbDevVersion.fields.planDoneOn.label=完了予定時間
i18n_entity.FbDevVersion.fields.ro.label=企業
i18n_entity.FbDevVersion.fields.version.label=バージョン
i18n_entity.FbDevVersion.group=Dev
i18n_entity.FbDevVersion.label=イテレーション
i18n_entity.FbDirectPutawayOrder.fields.bin.label=格納ロケーション
i18n_entity.FbDirectPutawayOrder.fields.btInvProcessed.label=在庫処理済み
i18n_entity.FbDirectPutawayOrder.fields.btLines.label=シングル行
i18n_entity.FbDirectPutawayOrder.fields.btOrderKind.inlineOptionBill.items.MfFinishedInbound.label=完成品入庫
i18n_entity.FbDirectPutawayOrder.fields.btOrderKind.inlineOptionBill.items.OtherInbound.label=その他入庫
i18n_entity.FbDirectPutawayOrder.fields.btOrderKind.inlineOptionBill.items.PurchaseInbound.label=購買入庫
i18n_entity.FbDirectPutawayOrder.fields.btOrderKind.label=タイプ
i18n_entity.FbDirectPutawayOrder.fields.btOrderState.inlineOptionBill.items.Init.label=未提出
i18n_entity.FbDirectPutawayOrder.fields.btOrderState.inlineOptionBill.items.Submitted.label=提出済み
i18n_entity.FbDirectPutawayOrder.fields.btOrderState.label=業務状態
i18n_entity.FbDirectPutawayOrder.fields.btOrderStateReason.label=状態説明
i18n_entity.FbDirectPutawayOrder.fields.container.label=格納容器
i18n_entity.FbDirectPutawayOrder.fields.createdBy.label=作成者
i18n_entity.FbDirectPutawayOrder.fields.createdOn.label=作成時間
i18n_entity.FbDirectPutawayOrder.fields.id.label=リスト番号
i18n_entity.FbDirectPutawayOrder.fields.modifiedBy.label=最終改訂人
i18n_entity.FbDirectPutawayOrder.fields.modifiedOn.label=最終変更時間
i18n_entity.FbDirectPutawayOrder.fields.remark.label=備考
i18n_entity.FbDirectPutawayOrder.fields.ro.label=元組織
i18n_entity.FbDirectPutawayOrder.fields.version.label=バージョン
i18n_entity.FbDirectPutawayOrder.group=Warehouse
i18n_entity.FbDirectPutawayOrder.label=手動荷役リスト
i18n_entity.FbDirectPutawayOrder.listCard.bin.prefix=格納ロケーション
i18n_entity.FbDirectPutawayOrder.listCard.container.prefix=格納容器
i18n_entity.FbDirectPutawayOrder.states.states.Init.label=未提出
i18n_entity.FbDirectPutawayOrder.states.states.Init.nextStates.Submitted.buttonLabel=提出
i18n_entity.FbDirectPutawayOrder.states.states.Submitted.label=提出済み
i18n_entity.FbDirectPutawayOrderLine.fields.btLineNo.label=行号
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterial.label=商品
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialCategory.label=商品分類番号
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialCategoryName.label=商品分類名称
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialId.label=商品番号
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialImage.label=商品イメージ図
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialModel.label=商品型番
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialName.label=商品名称
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialSpec.label=商品仕様
i18n_entity.FbDirectPutawayOrderLine.fields.btParentId.label=所属荷役リスト
i18n_entity.FbDirectPutawayOrderLine.fields.createdBy.label=作成者
i18n_entity.FbDirectPutawayOrderLine.fields.createdOn.label=作成時間
i18n_entity.FbDirectPutawayOrderLine.fields.id.label=ID
i18n_entity.FbDirectPutawayOrderLine.fields.lotNo.label=ロット番号
i18n_entity.FbDirectPutawayOrderLine.fields.modifiedBy.label=最終改訂人
i18n_entity.FbDirectPutawayOrderLine.fields.modifiedOn.label=最終変更時間
i18n_entity.FbDirectPutawayOrderLine.fields.price.label=単価
i18n_entity.FbDirectPutawayOrderLine.fields.qty.label=格納数量
i18n_entity.FbDirectPutawayOrderLine.fields.qualityLevel.label=品質レベル
i18n_entity.FbDirectPutawayOrderLine.fields.ro.label=元組織
i18n_entity.FbDirectPutawayOrderLine.fields.subContainerId.label=格子番号
i18n_entity.FbDirectPutawayOrderLine.fields.unitLabel.label=単位名称
i18n_entity.FbDirectPutawayOrderLine.fields.version.label=バージョン
i18n_entity.FbDirectPutawayOrderLine.group=Warehouse
i18n_entity.FbDirectPutawayOrderLine.label=手動荷役リスト行
i18n_entity.FbDirectPutawayOrderLine.listCard.btMaterialId.prefix=商品番号
i18n_entity.FbDirectPutawayOrderLine.listCard.qty.prefix=数量
i18n_entity.FbDirectPutawayOrderLine.listCard.subContainerId.prefix=格子番号
i18n_entity.FbDistrict.fields.btDisabled.label=使用停止
i18n_entity.FbDistrict.fields.createdBy.label=作成者
i18n_entity.FbDistrict.fields.createdOn.label=作成時間
i18n_entity.FbDistrict.fields.displayOrder.label=順序表示
i18n_entity.FbDistrict.fields.id.label=倉庫エリア番号
i18n_entity.FbDistrict.fields.modifiedBy.label=最終改訂人
i18n_entity.FbDistrict.fields.modifiedOn.label=最終変更時間
i18n_entity.FbDistrict.fields.name.label=倉庫エリア名称
i18n_entity.FbDistrict.fields.remark.label=備考
i18n_entity.FbDistrict.fields.ro.label=元組織
i18n_entity.FbDistrict.fields.structure.label=倉庫エリア構造
i18n_entity.FbDistrict.fields.version.label=バージョン
i18n_entity.FbDistrict.fields.warehouse.label=所属倉庫
i18n_entity.FbDistrict.group=MainData
i18n_entity.FbDistrict.label=倉庫エリア
i18n_entity.FbGoodsOwner.fields.address.label=アドレス
i18n_entity.FbGoodsOwner.fields.btDisabled.label=使用停止
i18n_entity.FbGoodsOwner.fields.contact.label=連絡先
i18n_entity.FbGoodsOwner.fields.createdBy.label=作成者
i18n_entity.FbGoodsOwner.fields.createdOn.label=作成時間
i18n_entity.FbGoodsOwner.fields.id.label=荷主コード
i18n_entity.FbGoodsOwner.fields.modifiedBy.label=最終改訂人
i18n_entity.FbGoodsOwner.fields.modifiedOn.label=最終変更時間
i18n_entity.FbGoodsOwner.fields.name.label=荷主名称
i18n_entity.FbGoodsOwner.fields.phone.label=連絡電話
i18n_entity.FbGoodsOwner.fields.remark.label=備考
i18n_entity.FbGoodsOwner.fields.ro.label=元組織
i18n_entity.FbGoodsOwner.fields.version.label=バージョン
i18n_entity.FbGoodsOwner.group=MainData
i18n_entity.FbGoodsOwner.label=荷主
i18n_entity.FbGoodsOwner.listCard.contact.prefix=連絡先
i18n_entity.FbGoodsOwner.listCard.name.prefix=荷主名
i18n_entity.FbGoodsOwner.listCard.phone.prefix=電話
i18n_entity.FbInboundOrder.fields.asnId.label=製品到着通知番号
i18n_entity.FbInboundOrder.fields.btInvProcessed.label=在庫処理済み
i18n_entity.FbInboundOrder.fields.btLines.label=シングル行
i18n_entity.FbInboundOrder.fields.btOrderKind.inlineOptionBill.items.MfFinishedInbound.label=完成品入庫
i18n_entity.FbInboundOrder.fields.btOrderKind.inlineOptionBill.items.OtherInbound.label=その他入庫
i18n_entity.FbInboundOrder.fields.btOrderKind.inlineOptionBill.items.PurchaseInbound.label=購買入庫
i18n_entity.FbInboundOrder.fields.btOrderKind.label=タイプ
i18n_entity.FbInboundOrder.fields.btOrderState.inlineOptionBill.items.Committed.label=提出済み
i18n_entity.FbInboundOrder.fields.btOrderState.inlineOptionBill.items.Init.label=未提出
i18n_entity.FbInboundOrder.fields.btOrderState.label=業務状態
i18n_entity.FbInboundOrder.fields.btOrderStateReason.label=状態説明
i18n_entity.FbInboundOrder.fields.callContainerAll.label=空いている容器呼び出し済み
i18n_entity.FbInboundOrder.fields.createdBy.label=作成者
i18n_entity.FbInboundOrder.fields.createdOn.label=作成時間
i18n_entity.FbInboundOrder.fields.district.label=入庫倉庫エリア
i18n_entity.FbInboundOrder.fields.id.label=リスト番号
i18n_entity.FbInboundOrder.fields.mfgFQCOrderId.label=成品品質検査リスト番号
i18n_entity.FbInboundOrder.fields.mfgOrderId.label=生産リスト番号
i18n_entity.FbInboundOrder.fields.modifiedBy.label=最終改訂人
i18n_entity.FbInboundOrder.fields.modifiedOn.label=最終変更時間
i18n_entity.FbInboundOrder.fields.planQty.label=納品数量
i18n_entity.FbInboundOrder.fields.purchaseOrderId.label=受注リスト番号
i18n_entity.FbInboundOrder.fields.qty.label=今回入庫数量
i18n_entity.FbInboundOrder.fields.recevingOrderId.label=納品リスト番号
i18n_entity.FbInboundOrder.fields.remark.label=備考
i18n_entity.FbInboundOrder.fields.ro.label=元組織
i18n_entity.FbInboundOrder.fields.vendor.label=サプライヤー
i18n_entity.FbInboundOrder.fields.version.label=バージョン
i18n_entity.FbInboundOrder.fields.warehouse.label=入庫倉庫
i18n_entity.FbInboundOrder.group=Warehouse
i18n_entity.FbInboundOrder.label=入庫リスト
i18n_entity.FbInboundOrder.states.states.Committed.label=提出済み
i18n_entity.FbInboundOrder.states.states.Init.label=未提出
i18n_entity.FbInboundOrder.states.states.Init.nextStates.Committed.buttonLabel=提出
i18n_entity.FbInboundOrderLine.fields.bin.label=格納ロケーション
i18n_entity.FbInboundOrderLine.fields.btLineNo.label=行番号
i18n_entity.FbInboundOrderLine.fields.btMaterial.label=商品
i18n_entity.FbInboundOrderLine.fields.btMaterialCategory.label=商品分類番号
i18n_entity.FbInboundOrderLine.fields.btMaterialCategoryName.label=商品分類名称
i18n_entity.FbInboundOrderLine.fields.btMaterialId.label=商品番号
i18n_entity.FbInboundOrderLine.fields.btMaterialImage.label=商品イメージ図
i18n_entity.FbInboundOrderLine.fields.btMaterialModel.label=商品型番
i18n_entity.FbInboundOrderLine.fields.btMaterialName.label=商品名称
i18n_entity.FbInboundOrderLine.fields.btMaterialSpec.label=商品仕様
i18n_entity.FbInboundOrderLine.fields.btParentId.label=所属入庫リスト
i18n_entity.FbInboundOrderLine.fields.callContainerQty.label=割当済み容器数量
i18n_entity.FbInboundOrderLine.fields.createdBy.label=作成者
i18n_entity.FbInboundOrderLine.fields.createdOn.label=作成時間
i18n_entity.FbInboundOrderLine.fields.finishedQty.label=過去入庫数量
i18n_entity.FbInboundOrderLine.fields.id.label=ID
i18n_entity.FbInboundOrderLine.fields.lotNo.label=ロット番号
i18n_entity.FbInboundOrderLine.fields.materialName.label=商品名称
i18n_entity.FbInboundOrderLine.fields.modifiedBy.label=最終改訂人
i18n_entity.FbInboundOrderLine.fields.modifiedOn.label=最終変更時間
i18n_entity.FbInboundOrderLine.fields.planQty.label=納品数量
i18n_entity.FbInboundOrderLine.fields.price.label=単価
i18n_entity.FbInboundOrderLine.fields.qty.label=入庫数量
i18n_entity.FbInboundOrderLine.fields.qualityLevel.label=品質レベル
i18n_entity.FbInboundOrderLine.fields.ro.label=元組織
i18n_entity.FbInboundOrderLine.fields.storeQty.label=格納数量
i18n_entity.FbInboundOrderLine.fields.unitLabel.label=単位名称
i18n_entity.FbInboundOrderLine.fields.version.label=バージョン
i18n_entity.FbInboundOrderLine.group=Warehouse
i18n_entity.FbInboundOrderLine.label=入庫シングル行
i18n_entity.FbInboundOrderLine.listCard.btMaterialId.prefix=商品番号
i18n_entity.FbInboundOrderLine.listCard.callContainerQty.prefix=容器割り当て
i18n_entity.FbInboundOrderLine.listCard.qty.prefix=入庫
i18n_entity.FbInboundOrderLine.listCard.storeQty.prefix=格納
i18n_entity.FbInvChange.fields.btMaterial.label=商品
i18n_entity.FbInvChange.fields.btMaterialCategory.label=商品分類番号
i18n_entity.FbInvChange.fields.btMaterialCategoryName.label=商品分類名称
i18n_entity.FbInvChange.fields.btMaterialId.label=商品番号
i18n_entity.FbInvChange.fields.btMaterialImage.label=商品イメージ図
i18n_entity.FbInvChange.fields.btMaterialModel.label=商品型番
i18n_entity.FbInvChange.fields.btMaterialName.label=商品名称
i18n_entity.FbInvChange.fields.btMaterialSpec.label=商品仕様
i18n_entity.FbInvChange.fields.container.label=容器
i18n_entity.FbInvChange.fields.createdBy.label=作成者
i18n_entity.FbInvChange.fields.createdOn.label=作成時間
i18n_entity.FbInvChange.fields.id.label=ID
i18n_entity.FbInvChange.fields.lotNo.label=ロット
i18n_entity.FbInvChange.fields.modifiedBy.label=最終改訂人
i18n_entity.FbInvChange.fields.modifiedOn.label=最終変更時間
i18n_entity.FbInvChange.fields.qty.label=数量
i18n_entity.FbInvChange.fields.subContainerId.label=格子番号
i18n_entity.FbInvChange.fields.version.label=バージョン修正
i18n_entity.FbInvChange.group=Warehouse
i18n_entity.FbInvChange.label=在庫变更
i18n_entity.FbInvLayout.fields.amount.label=金额
i18n_entity.FbInvLayout.fields.bin.label=所在ロケーション
i18n_entity.FbInvLayout.fields.btMaterial.label=商品
i18n_entity.FbInvLayout.fields.btMaterialCategory.label=商品分類番号
i18n_entity.FbInvLayout.fields.btMaterialCategoryName.label=商品分類名称
i18n_entity.FbInvLayout.fields.btMaterialId.label=商品番号
i18n_entity.FbInvLayout.fields.btMaterialImage.label=商品イメージ図
i18n_entity.FbInvLayout.fields.btMaterialModel.label=商品型番
i18n_entity.FbInvLayout.fields.btMaterialName.label=商品名称
i18n_entity.FbInvLayout.fields.btMaterialSpec.label=商品仕様
i18n_entity.FbInvLayout.fields.column.label=列
i18n_entity.FbInvLayout.fields.createdBy.label=作成者
i18n_entity.FbInvLayout.fields.createdOn.label=作成時間
i18n_entity.FbInvLayout.fields.depth.label=奥
i18n_entity.FbInvLayout.fields.district.label=所在倉庫エリア
i18n_entity.FbInvLayout.fields.expDate.label=有效期間
i18n_entity.FbInvLayout.fields.id.label=ID
i18n_entity.FbInvLayout.fields.inboundOn.label=入庫時間
i18n_entity.FbInvLayout.fields.inboundOrderId.label=入庫リスト番号
i18n_entity.FbInvLayout.fields.inboundOrderLineNo.label=入庫シングル行番号
i18n_entity.FbInvLayout.fields.layer.label=フロア
i18n_entity.FbInvLayout.fields.leafContainer.label=内側容器
i18n_entity.FbInvLayout.fields.locked.label=ロック
i18n_entity.FbInvLayout.fields.locked.listBatchButtons.buttons[0].label=ロック解除
i18n_entity.FbInvLayout.fields.lotNo.label=ロット番号
i18n_entity.FbInvLayout.fields.matLotNo.label=ロット
i18n_entity.FbInvLayout.fields.matSerialNo.label=シリアル番号
i18n_entity.FbInvLayout.fields.mfgDate.label=生産日付
i18n_entity.FbInvLayout.fields.modifiedBy.label=最終改訂人
i18n_entity.FbInvLayout.fields.modifiedOn.label=最終変更時間
i18n_entity.FbInvLayout.fields.onRobot.label=所在ロボット
i18n_entity.FbInvLayout.fields.outboundOrderId.label=出庫リスト番号
i18n_entity.FbInvLayout.fields.outboundOrderLineNo.label=出庫シングル行号
i18n_entity.FbInvLayout.fields.owner.label=荷主
i18n_entity.FbInvLayout.fields.price.label=単価
i18n_entity.FbInvLayout.fields.qty.label=数量
i18n_entity.FbInvLayout.fields.refInv.label=在庫明細引用
i18n_entity.FbInvLayout.fields.row.label=列
i18n_entity.FbInvLayout.fields.state.inlineOptionBill.items.Assigned.label=割当済み
i18n_entity.FbInvLayout.fields.state.inlineOptionBill.items.Received.label=受領済み
i18n_entity.FbInvLayout.fields.state.inlineOptionBill.items.Storing.label=格納中
i18n_entity.FbInvLayout.fields.state.label=在庫状態
i18n_entity.FbInvLayout.fields.subContainerId.label=格子
i18n_entity.FbInvLayout.fields.topContainer.label=最外层容器
i18n_entity.FbInvLayout.fields.usedQty.label=割当済み数量
i18n_entity.FbInvLayout.fields.vendor.label=サプライヤー
i18n_entity.FbInvLayout.fields.version.label=バージョン修正
i18n_entity.FbInvLayout.fields.warehouse.label=所在倉庫
i18n_entity.FbInvLayout.group=Warehouse
i18n_entity.FbInvLayout.label=在庫明細
i18n_entity.FbInvLayout.listCard.bin.prefix=ロケーション
i18n_entity.FbInvLayout.listCard.inboundOn.prefix=入庫時間
i18n_entity.FbInvLayout.listCard.lotNo.prefix=ロット番号
i18n_entity.FbInvLayout.listCard.qty.prefix=数量
i18n_entity.FbInvLayout.listCard.topContainer.prefix=容器
i18n_entity.FbInvSnapShot.fields.btMaterial.label=商品
i18n_entity.FbInvSnapShot.fields.btMaterialCategory.label=商品分類番号
i18n_entity.FbInvSnapShot.fields.btMaterialCategoryName.label=商品分類名称
i18n_entity.FbInvSnapShot.fields.btMaterialId.label=商品番号
i18n_entity.FbInvSnapShot.fields.btMaterialImage.label=商品イメージ図
i18n_entity.FbInvSnapShot.fields.btMaterialModel.label=商品型番
i18n_entity.FbInvSnapShot.fields.btMaterialName.label=商品名称
i18n_entity.FbInvSnapShot.fields.btMaterialSpec.label=商品仕様
i18n_entity.FbInvSnapShot.fields.createdBy.label=作成者
i18n_entity.FbInvSnapShot.fields.createdOn.label=作成時間
i18n_entity.FbInvSnapShot.fields.id.label=ID
i18n_entity.FbInvSnapShot.fields.modifiedBy.label=最終改訂人
i18n_entity.FbInvSnapShot.fields.modifiedOn.label=最終変更時間
i18n_entity.FbInvSnapShot.fields.qty.label=数量
i18n_entity.FbInvSnapShot.fields.uuid.label=uuid
i18n_entity.FbInvSnapShot.fields.version.label=バージョン修正
i18n_entity.FbInvSnapShot.group=Warehouse
i18n_entity.FbInvSnapShot.label=在庫照会
i18n_entity.FbMaterial.fields.abc.label=ABC 分類
i18n_entity.FbMaterial.fields.btDisabled.label=使用停止
i18n_entity.FbMaterial.fields.categoriesDesc.label=商品分類説明
i18n_entity.FbMaterial.fields.category1.label=一級分類
i18n_entity.FbMaterial.fields.category2.label=二級分類
i18n_entity.FbMaterial.fields.category3.label=三級分類
i18n_entity.FbMaterial.fields.createdBy.label=作成者
i18n_entity.FbMaterial.fields.createdOn.label=作成時間
i18n_entity.FbMaterial.fields.displayDecimals.label=小数点桁数
i18n_entity.FbMaterial.fields.endOn.label=日付使用停止
i18n_entity.FbMaterial.fields.height.label=高さ
i18n_entity.FbMaterial.fields.id.label=商品コード
i18n_entity.FbMaterial.fields.image.label=イメージ図
i18n_entity.FbMaterial.fields.leafCategory.label=商品分類
i18n_entity.FbMaterial.fields.length.label=長さ
i18n_entity.FbMaterial.fields.mainUnit.label=計量単位名称
i18n_entity.FbMaterial.fields.mainVendor.label=メインサプライヤー
i18n_entity.FbMaterial.fields.mixLotNo.label=ロット混載
i18n_entity.FbMaterial.fields.mixMaterial.label=商品混載
i18n_entity.FbMaterial.fields.model.label=型番
i18n_entity.FbMaterial.fields.modifiedBy.label=最終改訂人
i18n_entity.FbMaterial.fields.modifiedOn.label=最終変更時間
i18n_entity.FbMaterial.fields.name.label=商品名称
i18n_entity.FbMaterial.fields.owner.label=荷主
i18n_entity.FbMaterial.fields.price.label=単価
i18n_entity.FbMaterial.fields.remark.label=備考
i18n_entity.FbMaterial.fields.ro.label=元組織
i18n_entity.FbMaterial.fields.spec.label=商品仕様
i18n_entity.FbMaterial.fields.startOn.label=日付使用
i18n_entity.FbMaterial.fields.syncOut.label=外部インポート
i18n_entity.FbMaterial.fields.topCat.label=在庫種類名称
i18n_entity.FbMaterial.fields.unit.label=商品単位
i18n_entity.FbMaterial.fields.unitLabel.label=単位名称
i18n_entity.FbMaterial.fields.version.label=バージョン
i18n_entity.FbMaterial.fields.volume.label=容積
i18n_entity.FbMaterial.fields.weight.label=重量
i18n_entity.FbMaterial.fields.width.label=幅
i18n_entity.FbMaterial.group=MainData
i18n_entity.FbMaterial.label=商品
i18n_entity.FbMaterial.listCard.leafCategory.prefix=商品分類
i18n_entity.FbMaterialCategory.fields.btDisabled.label=使用停止
i18n_entity.FbMaterialCategory.fields.createdBy.label=作成者
i18n_entity.FbMaterialCategory.fields.createdOn.label=作成時間
i18n_entity.FbMaterialCategory.fields.id.label=分類番号
i18n_entity.FbMaterialCategory.fields.modifiedBy.label=最終改訂人
i18n_entity.FbMaterialCategory.fields.modifiedOn.label=最終変更時間
i18n_entity.FbMaterialCategory.fields.name.label=名称
i18n_entity.FbMaterialCategory.fields.parent.label=上級分類
i18n_entity.FbMaterialCategory.fields.remark.label=備考
i18n_entity.FbMaterialCategory.fields.storeDistricts.label=格納倉庫エリア
i18n_entity.FbMaterialCategory.fields.version.label=バージョン修正
i18n_entity.FbMaterialCategory.group=MainData
i18n_entity.FbMaterialCategory.label=商品分類
i18n_entity.FbMaterialCategory.listCard.storeDistricts.prefix=格納倉庫エリア
i18n_entity.FbMaterialContainerMaxQty.fields.btDisabled.label=使用停止
i18n_entity.FbMaterialContainerMaxQty.fields.btMaterial.label=商品
i18n_entity.FbMaterialContainerMaxQty.fields.containerType.label=容器タイプ
i18n_entity.FbMaterialContainerMaxQty.fields.createdBy.label=作成者
i18n_entity.FbMaterialContainerMaxQty.fields.createdOn.label=作成時間
i18n_entity.FbMaterialContainerMaxQty.fields.id.label=ID
i18n_entity.FbMaterialContainerMaxQty.fields.maxQty.label=最大格納数
i18n_entity.FbMaterialContainerMaxQty.fields.modifiedBy.label=最終改訂人
i18n_entity.FbMaterialContainerMaxQty.fields.modifiedOn.label=最終変更時間
i18n_entity.FbMaterialContainerMaxQty.fields.version.label=バージョン修正
i18n_entity.FbMaterialContainerMaxQty.group=MainData
i18n_entity.FbMaterialContainerMaxQty.label=商品容器容量
i18n_entity.FbMaterialContainerMaxQty.listCard.btMaterial.prefix=商品保管
i18n_entity.FbMaterialContainerMaxQty.listCard.maxQty.prefix=最大保管数
i18n_entity.FbMaterialContainerMaxQty.listCard.maxQty.suffix=個
i18n_entity.FbMaterialLot.fields.createdBy.label=作成者
i18n_entity.FbMaterialLot.fields.createdOn.label=作成時間
i18n_entity.FbMaterialLot.fields.disabled.label=使用禁止
i18n_entity.FbMaterialLot.fields.id.label=生産ロット
i18n_entity.FbMaterialLot.fields.material.label=所属商品
i18n_entity.FbMaterialLot.fields.modifiedBy.label=最終改訂人
i18n_entity.FbMaterialLot.fields.modifiedOn.label=最終変更時間
i18n_entity.FbMaterialLot.fields.name.label=生産ロット名称
i18n_entity.FbMaterialLot.fields.remark.label=備考
i18n_entity.FbMaterialLot.fields.ro.label=元組織
i18n_entity.FbMaterialLot.fields.version.label=バージョン
i18n_entity.FbMaterialLot.group=MainData
i18n_entity.FbMaterialLot.label=商品ロット
i18n_entity.FbMaterialUnit.fields.basic.label=基本単位
i18n_entity.FbMaterialUnit.fields.createdBy.label=作成者
i18n_entity.FbMaterialUnit.fields.createdOn.label=作成時間
i18n_entity.FbMaterialUnit.fields.disabled.label=使用禁止
i18n_entity.FbMaterialUnit.fields.id.label=単位コード
i18n_entity.FbMaterialUnit.fields.modifiedBy.label=最終改訂人
i18n_entity.FbMaterialUnit.fields.modifiedOn.label=最終変更時間
i18n_entity.FbMaterialUnit.fields.name.label=単位名称
i18n_entity.FbMaterialUnit.fields.parent.label=親級単位
i18n_entity.FbMaterialUnit.fields.ratio.label=換算関係
i18n_entity.FbMaterialUnit.fields.remark.label=備考
i18n_entity.FbMaterialUnit.fields.ro.label=元組織
i18n_entity.FbMaterialUnit.fields.version.label=バージョン
i18n_entity.FbMaterialUnit.group=MainData
i18n_entity.FbMaterialUnit.label=商品単位
i18n_entity.FbOutboundOrder.fields.btInvProcessed.label=在庫処理済み
i18n_entity.FbOutboundOrder.fields.btLines.label=シングル行
i18n_entity.FbOutboundOrder.fields.btOrderKind.inlineOptionBill.items.Product.label=商品出庫
i18n_entity.FbOutboundOrder.fields.btOrderKind.label=タイプ
i18n_entity.FbOutboundOrder.fields.btOrderState.inlineOptionBill.items.Committed.label=提出済み
i18n_entity.FbOutboundOrder.fields.btOrderState.inlineOptionBill.items.Init.label=未提出
i18n_entity.FbOutboundOrder.fields.btOrderState.label=業務状態
i18n_entity.FbOutboundOrder.fields.btOrderStateReason.label=状態説明
i18n_entity.FbOutboundOrder.fields.createdBy.label=作成者
i18n_entity.FbOutboundOrder.fields.createdOn.label=作成時間
i18n_entity.FbOutboundOrder.fields.customer.label=顧客
i18n_entity.FbOutboundOrder.fields.district.label=出庫倉庫エリア
i18n_entity.FbOutboundOrder.fields.id.label=リスト番号
i18n_entity.FbOutboundOrder.fields.invAssignedAll.label=直接出庫
i18n_entity.FbOutboundOrder.fields.modifiedBy.label=最終改訂人
i18n_entity.FbOutboundOrder.fields.modifiedOn.label=最終変更時間
i18n_entity.FbOutboundOrder.fields.planQty.label=計画出庫数量
i18n_entity.FbOutboundOrder.fields.priority.label=優先級
i18n_entity.FbOutboundOrder.fields.qty.label=今回出庫数量
i18n_entity.FbOutboundOrder.fields.receiver.label=納品単位
i18n_entity.FbOutboundOrder.fields.remark.label=備考
i18n_entity.FbOutboundOrder.fields.ro.label=元組織
i18n_entity.FbOutboundOrder.fields.saleOrderId.label=販売リスト番号
i18n_entity.FbOutboundOrder.fields.saleShipOrderId.label=販売出荷リスト
i18n_entity.FbOutboundOrder.fields.version.label=バージョン
i18n_entity.FbOutboundOrder.fields.warehouse.label=出庫倉庫
i18n_entity.FbOutboundOrder.group=Warehouse
i18n_entity.FbOutboundOrder.label=出庫リスト
i18n_entity.FbOutboundOrder.listCard.priority.prefix=優先級
i18n_entity.FbOutboundOrder.states.states.Committed.label=提出済み
i18n_entity.FbOutboundOrder.states.states.Init.label=未提出
i18n_entity.FbOutboundOrder.states.states.Init.nextStates.Committed.buttonLabel=提出
i18n_entity.FbOutboundOrderLine.fields.btInvRefAll.label=トータル在庫
i18n_entity.FbOutboundOrderLine.fields.btInvRefMatch.label=使用可能在庫
i18n_entity.FbOutboundOrderLine.fields.btLineNo.label=行番号
i18n_entity.FbOutboundOrderLine.fields.btMaterial.label=商品
i18n_entity.FbOutboundOrderLine.fields.btMaterialCategory.label=商品分類番号
i18n_entity.FbOutboundOrderLine.fields.btMaterialCategoryName.label=商品分類名称
i18n_entity.FbOutboundOrderLine.fields.btMaterialId.label=商品番号
i18n_entity.FbOutboundOrderLine.fields.btMaterialImage.label=商品イメージ図
i18n_entity.FbOutboundOrderLine.fields.btMaterialModel.label=商品型番
i18n_entity.FbOutboundOrderLine.fields.btMaterialName.label=商品名称
i18n_entity.FbOutboundOrderLine.fields.btMaterialSpec.label=商品仕様
i18n_entity.FbOutboundOrderLine.fields.btParentId.label=所属出庫リスト
i18n_entity.FbOutboundOrderLine.fields.createdBy.label=作成者
i18n_entity.FbOutboundOrderLine.fields.createdOn.label=作成時間
i18n_entity.FbOutboundOrderLine.fields.finishedQty.label=之前出庫数量
i18n_entity.FbOutboundOrderLine.fields.id.label=ID
i18n_entity.FbOutboundOrderLine.fields.invAssignedQty.label=割当済み在庫数量
i18n_entity.FbOutboundOrderLine.fields.layoutId.label=在庫明細 ID紐付け
i18n_entity.FbOutboundOrderLine.fields.lotNo.label=ロット番号
i18n_entity.FbOutboundOrderLine.fields.materialName.label=商品名称
i18n_entity.FbOutboundOrderLine.fields.modifiedBy.label=最終改訂人
i18n_entity.FbOutboundOrderLine.fields.modifiedOn.label=最終変更時間
i18n_entity.FbOutboundOrderLine.fields.planQty.label=计划出庫总数
i18n_entity.FbOutboundOrderLine.fields.qty.label=出庫数量
i18n_entity.FbOutboundOrderLine.fields.qualityLevel.label=品質レベル
i18n_entity.FbOutboundOrderLine.fields.ro.label=元組織
i18n_entity.FbOutboundOrderLine.fields.version.label=バージョン
i18n_entity.FbOutboundOrderLine.group=Warehouse
i18n_entity.FbOutboundOrderLine.label=出庫シングル行
i18n_entity.FbOutboundOrderLine.listCard.btMaterialId.prefix=商品番号
i18n_entity.FbOutboundOrderLine.listCard.invAssignedQty.prefix=割当済み在庫
i18n_entity.FbOutboundOrderLine.listCard.lotNo.prefix=ロット番号
i18n_entity.FbOutboundOrderLine.listCard.qty.prefix=数量
i18n_entity.FbPkg.fields.createdBy.label=作成者
i18n_entity.FbPkg.fields.createdOn.label=作成時間
i18n_entity.FbPkg.fields.disabled.label=使用禁止
i18n_entity.FbPkg.fields.id.label=梱包コード
i18n_entity.FbPkg.fields.material.label=所属商品
i18n_entity.FbPkg.fields.modifiedBy.label=最終改訂人
i18n_entity.FbPkg.fields.modifiedOn.label=最終変更時間
i18n_entity.FbPkg.fields.name.label=梱包名称
i18n_entity.FbPkg.fields.purpose.label=梱包用途
i18n_entity.FbPkg.fields.qty.label=梱包内数量
i18n_entity.FbPkg.fields.remark.label=備考
i18n_entity.FbPkg.fields.ro.label=元組織
i18n_entity.FbPkg.fields.version.label=バージョン
i18n_entity.FbPkg.group=MainData
i18n_entity.FbPkg.label=梱包仕様
i18n_entity.FbPkg.listCard.disabled.formatMapping[0].replaceText=使用停止済み
i18n_entity.FbPkg.listCard.material.prefix=所属商品
i18n_entity.FbPkg.listCard.name.prefix=梱包名
i18n_entity.FbPkg.listCard.qty.prefix=最大置き数
i18n_entity.FbPkg.listCard.qty.suffix=個
i18n_entity.FbVendor.fields.address.label=アドレス
i18n_entity.FbVendor.fields.btDisabled.label=使用停止
i18n_entity.FbVendor.fields.contact.label=連絡先
i18n_entity.FbVendor.fields.createdBy.label=作成者
i18n_entity.FbVendor.fields.createdOn.label=作成時間
i18n_entity.FbVendor.fields.email.label=メールアドレス
i18n_entity.FbVendor.fields.id.label=サプライヤー番号
i18n_entity.FbVendor.fields.level.label=レベル
i18n_entity.FbVendor.fields.modifiedBy.label=最終改訂人
i18n_entity.FbVendor.fields.modifiedOn.label=最終変更時間
i18n_entity.FbVendor.fields.name.label=サプライヤー名称
i18n_entity.FbVendor.fields.phone.label=連絡電話
i18n_entity.FbVendor.fields.remark.label=備考
i18n_entity.FbVendor.fields.ro.label=元組織
i18n_entity.FbVendor.fields.version.label=バージョン
i18n_entity.FbVendor.group=MainData
i18n_entity.FbVendor.label=サプライヤー
i18n_entity.FbVendor.listCard.contact.prefix=連絡先
i18n_entity.FbVendor.listCard.name.prefix=名称
i18n_entity.FbVendor.listCard.phone.prefix=電話
i18n_entity.FbWarehouse.fields.address.label=アドレス
i18n_entity.FbWarehouse.fields.btDisabled.label=使用停止
i18n_entity.FbWarehouse.fields.contact.label=連絡先
i18n_entity.FbWarehouse.fields.createdBy.label=作成者
i18n_entity.FbWarehouse.fields.createdOn.label=作成時間
i18n_entity.FbWarehouse.fields.defaultBin.label=デフォルトロケーション
i18n_entity.FbWarehouse.fields.displayOrder.label=順序表示
i18n_entity.FbWarehouse.fields.id.label=倉庫番号
i18n_entity.FbWarehouse.fields.latitude.label=位置緯度
i18n_entity.FbWarehouse.fields.longitude.label=位置経度
i18n_entity.FbWarehouse.fields.modifiedBy.label=最終改訂人
i18n_entity.FbWarehouse.fields.modifiedOn.label=最終変更時間
i18n_entity.FbWarehouse.fields.name.label=倉庫名称
i18n_entity.FbWarehouse.fields.phone.label=連絡電話
i18n_entity.FbWarehouse.fields.remark.label=備考
i18n_entity.FbWarehouse.fields.ro.label=元組織
i18n_entity.FbWarehouse.fields.version.label=バージョン
i18n_entity.FbWarehouse.fields.volume.label=容積
i18n_entity.FbWarehouse.group=MainData
i18n_entity.FbWarehouse.label=倉庫
i18n_entity.FbWarehouse.listCard.btDisabled.formatMapping[0].replaceText=使用停止済み
i18n_entity.FbWorkPosition.fields.createdBy.label=作成者
i18n_entity.FbWorkPosition.fields.createdOn.label=作成時間
i18n_entity.FbWorkPosition.fields.disabled.label=使用禁止
i18n_entity.FbWorkPosition.fields.id.label=リスト番号
i18n_entity.FbWorkPosition.fields.modifiedBy.label=最終改訂人
i18n_entity.FbWorkPosition.fields.modifiedOn.label=最終変更時間
i18n_entity.FbWorkPosition.fields.name.label=名称
i18n_entity.FbWorkPosition.fields.remark.label=備考
i18n_entity.FbWorkPosition.fields.ro.label=元組織
i18n_entity.FbWorkPosition.fields.version.label=バージョン
i18n_entity.FbWorkPosition.group=MainData
i18n_entity.FbWorkPosition.label=ポジション
i18n_entity.FbWorkPosition.listCard.disabled.formatMapping[0].replaceText=使用停止済み
i18n_entity.FbWorkSite.fields.bin.label=所属ロケーション
i18n_entity.FbWorkSite.fields.createdBy.label=作成者
i18n_entity.FbWorkSite.fields.createdOn.label=作成時間
i18n_entity.FbWorkSite.fields.disabled.label=使用禁止
i18n_entity.FbWorkSite.fields.id.label=ワークテーブルコード
i18n_entity.FbWorkSite.fields.kind.label=ワークテーブルタイプ
i18n_entity.FbWorkSite.fields.line.label=所属生産ライン
i18n_entity.FbWorkSite.fields.modifiedBy.label=最終改訂人
i18n_entity.FbWorkSite.fields.modifiedOn.label=最終変更時間
i18n_entity.FbWorkSite.fields.name.label=ワークテーブル名称
i18n_entity.FbWorkSite.fields.position.label=所属岗位
i18n_entity.FbWorkSite.fields.remark.label=備考
i18n_entity.FbWorkSite.fields.ro.label=元組織
i18n_entity.FbWorkSite.fields.version.label=バージョン
i18n_entity.FbWorkSite.group=MainData
i18n_entity.FbWorkSite.label=ワークテーブル
i18n_entity.FbWorkSite.listCard.bin.prefix=所属ロケーション
i18n_entity.FbWorkSite.listCard.disabled.formatMapping[0].replaceText=使用停止済み
i18n_entity.FbWorkSite.listCard.kind.prefix=ワークテーブルタイプ
i18n_entity.FbWorkSite.listCard.name.prefix=ワークテーブル名
i18n_entity.FbWorkSite.listCard.position.prefix=所属ポジション
i18n_entity.HaiMockRobot.fields.battery.label=バッテリー残量
i18n_entity.HaiMockRobot.fields.createdBy.label=作成者
i18n_entity.HaiMockRobot.fields.createdOn.label=作成時間
i18n_entity.HaiMockRobot.fields.id.label=ID
i18n_entity.HaiMockRobot.fields.modifiedBy.label=最終改訂人
i18n_entity.HaiMockRobot.fields.modifiedOn.label=最終変更時間
i18n_entity.HaiMockRobot.fields.posX.label=位置 X
i18n_entity.HaiMockRobot.fields.posY.label=位置 Y
i18n_entity.HaiMockRobot.fields.version.label=バージョン修正
i18n_entity.HaiMockRobot.group=WCS
i18n_entity.HaiMockRobot.label=海柔エミュレーションロボット
i18n_entity.HaiMockRobot.listCard.battery.prefix=バッテリー残量
i18n_entity.HaiMockRobot.listCard.posX.prefix=位置 X
i18n_entity.HaiMockRobot.listCard.posY.prefix=位置 Y
i18n_entity.HikResourcePack.fields.active.label=メイン設定
i18n_entity.HikResourcePack.fields.createdBy.label=作成者
i18n_entity.HikResourcePack.fields.createdOn.label=作成時間
i18n_entity.HikResourcePack.fields.id.label=ID
i18n_entity.HikResourcePack.fields.lmap.label=レーザーポイントクラウドファイル lmap
i18n_entity.HikResourcePack.fields.modifiedBy.label=最終改訂人
i18n_entity.HikResourcePack.fields.modifiedOn.label=最終変更時間
i18n_entity.HikResourcePack.fields.podConfig.label=ラック設定 XML
i18n_entity.HikResourcePack.fields.remark.label=備考
i18n_entity.HikResourcePack.fields.version.label=バージョン修正
i18n_entity.HikResourcePack.group=WCS
i18n_entity.HikResourcePack.label=海康りソース
i18n_entity.HikResourcePack.listCard.version.prefix=バージョン
i18n_entity.HumanUser.fields.btDisabled.label=使用停止
i18n_entity.HumanUser.fields.company.label=会社
i18n_entity.HumanUser.fields.createdBy.label=作成者
i18n_entity.HumanUser.fields.createdOn.label=作成時間
i18n_entity.HumanUser.fields.directSignInDisabled.label=禁止直接登录
i18n_entity.HumanUser.fields.disabled.label=使用禁止
i18n_entity.HumanUser.fields.email.label=Eメール
i18n_entity.HumanUser.fields.externalAdded.label=外部追加
i18n_entity.HumanUser.fields.externalSource.label=外部ソース
i18n_entity.HumanUser.fields.externalUserId.label=外部ユーザー ID
i18n_entity.HumanUser.fields.id.label=番号
i18n_entity.HumanUser.fields.modifiedBy.label=最終改訂人
i18n_entity.HumanUser.fields.modifiedOn.label=最終変更時間
i18n_entity.HumanUser.fields.password.label=パスワード
i18n_entity.HumanUser.fields.phone.label=携帯電話
i18n_entity.HumanUser.fields.pwdErrCount.label=パスワードエラー計数
i18n_entity.HumanUser.fields.pwdSetOn.label=パスワード設定時間
i18n_entity.HumanUser.fields.ro.label=元組織
i18n_entity.HumanUser.fields.roAdmin.label=企業管理人
i18n_entity.HumanUser.fields.roleIds.label=役割
i18n_entity.HumanUser.fields.truename.label=本名
i18n_entity.HumanUser.fields.username.label=ユーザー名
i18n_entity.HumanUser.fields.version.label=バージョン
i18n_entity.HumanUser.group=User
i18n_entity.HumanUser.label=ユーザー
i18n_entity.HumanUser.listCard.phone.prefix=携帯電話
i18n_entity.HumanUser.listCard.truename.prefix=本名
i18n_entity.HumanUserSession.fields.createdBy.label=作成者
i18n_entity.HumanUserSession.fields.createdOn.label=作成時間
i18n_entity.HumanUserSession.fields.expiredAt.label=27.有効期限
i18n_entity.HumanUserSession.fields.id.label=ID
i18n_entity.HumanUserSession.fields.modifiedBy.label=最終改訂人
i18n_entity.HumanUserSession.fields.modifiedOn.label=最終変更時間
i18n_entity.HumanUserSession.fields.ro.label=RO
i18n_entity.HumanUserSession.fields.userId.label=ユーザー
i18n_entity.HumanUserSession.fields.userToken.label=トークン
i18n_entity.HumanUserSession.fields.version.label=バージョン修正
i18n_entity.HumanUserSession.group=Core
i18n_entity.HumanUserSession.label=ユーザー会話
i18n_entity.IdGen.fields.createdBy.label=作成者
i18n_entity.IdGen.fields.createdOn.label=作成時間
i18n_entity.IdGen.fields.flowNo.label=連番
i18n_entity.IdGen.fields.id.label=番号
i18n_entity.IdGen.fields.key.label=グループ
i18n_entity.IdGen.fields.modifiedBy.label=最終改訂人
i18n_entity.IdGen.fields.modifiedOn.label=最終変更時間
i18n_entity.IdGen.fields.ro.label=元組織
i18n_entity.IdGen.fields.timestamp.label=日付
i18n_entity.IdGen.fields.version.label=バージョン
i18n_entity.IdGen.group=Core
i18n_entity.IdGen.label=番号規則
i18n_entity.LePage.fields.content.label=内容
i18n_entity.LePage.fields.createdBy.label=作成者
i18n_entity.LePage.fields.createdOn.label=作成時間
i18n_entity.LePage.fields.id.label=ID
i18n_entity.LePage.fields.label.label=表示名
i18n_entity.LePage.fields.modifiedBy.label=最終改訂人
i18n_entity.LePage.fields.modifiedOn.label=最終変更時間
i18n_entity.LePage.fields.name.label=ページ名
i18n_entity.LePage.fields.version.label=バージョン修正
i18n_entity.LePage.group=Core
i18n_entity.LePage.label=カスタマイズインターフェース
i18n_entity.ListFilterCase.fields.content.label=内容
i18n_entity.ListFilterCase.fields.createdBy.label=作成者
i18n_entity.ListFilterCase.fields.createdOn.label=作成時間
i18n_entity.ListFilterCase.fields.global.label=グローバル
i18n_entity.ListFilterCase.fields.id.label=番号
i18n_entity.ListFilterCase.fields.modifiedBy.label=最終改訂人
i18n_entity.ListFilterCase.fields.modifiedOn.label=最終変更時間
i18n_entity.ListFilterCase.fields.owner.label=所有者
i18n_entity.ListFilterCase.fields.page.label=ページ
i18n_entity.ListFilterCase.fields.ro.label=元組織
i18n_entity.ListFilterCase.fields.version.label=バージョン
i18n_entity.ListFilterCase.group=Core
i18n_entity.ListFilterCase.label=リスト検索方法
i18n_entity.MockSeerRobot.fields.createdBy.label=作成者
i18n_entity.MockSeerRobot.fields.createdOn.label=作成時間
i18n_entity.MockSeerRobot.fields.currentStation.label=現在ステーション
i18n_entity.MockSeerRobot.fields.id.label=ID
i18n_entity.MockSeerRobot.fields.modifiedBy.label=最終改訂人
i18n_entity.MockSeerRobot.fields.modifiedOn.label=最終変更時間
i18n_entity.MockSeerRobot.fields.staringStation.label=出発ステーション
i18n_entity.MockSeerRobot.fields.version.label=バージョン修正
i18n_entity.MockSeerRobot.group=WCS
i18n_entity.MockSeerRobot.label=エミュレーション弊社ロボット
i18n_entity.MockSeerRobot.listCard.createdOn.prefix=作成
i18n_entity.MockSeerRobot.listCard.staringStation.prefix=出発ステーション
i18n_entity.MrRobotRuntimeRecord.fields.channel.label=所在サブレーン
i18n_entity.MrRobotRuntimeRecord.fields.createdBy.label=作成者
i18n_entity.MrRobotRuntimeRecord.fields.createdOn.label=作成時間
i18n_entity.MrRobotRuntimeRecord.fields.ctOrders.label=容器搬送リスト
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Charging.label=充電中
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Idle.label=空いている
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Inbound.label=入庫中
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Outbound.label=出庫中
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Parking.label=停車中
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Tasking.label=運送リスト実行
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.label=拡張タスク状態
i18n_entity.MrRobotRuntimeRecord.fields.id.label=ID
i18n_entity.MrRobotRuntimeRecord.fields.modifiedBy.label=最終改訂人
i18n_entity.MrRobotRuntimeRecord.fields.modifiedOn.label=最終変更時間
i18n_entity.MrRobotRuntimeRecord.fields.offDuty.label=受注しない
i18n_entity.MrRobotRuntimeRecord.fields.port.label=所在倉庫口
i18n_entity.MrRobotRuntimeRecord.fields.rtBins.label=リアルタイム配車積載状況
i18n_entity.MrRobotRuntimeRecord.fields.rtCmdStatus.label=配車実行状態リアルタイム確認
i18n_entity.MrRobotRuntimeRecord.fields.rtCurrentOrder.label=配車現在運送リストリアルタイム確認
i18n_entity.MrRobotRuntimeRecord.fields.rtCurrentStep.label=現在運送リスト進捗リアルタイム確認
i18n_entity.MrRobotRuntimeRecord.fields.rtOrders.label=配車运单リストリアルタイム確認
i18n_entity.MrRobotRuntimeRecord.fields.taskStatus.inlineOptionBill.items.Charging.label=充電中
i18n_entity.MrRobotRuntimeRecord.fields.taskStatus.inlineOptionBill.items.Idle.label=空いている
i18n_entity.MrRobotRuntimeRecord.fields.taskStatus.inlineOptionBill.items.Tasking.label=タスク中
i18n_entity.MrRobotRuntimeRecord.fields.taskStatus.label=タスク状態
i18n_entity.MrRobotRuntimeRecord.fields.version.label=バージョン修正
i18n_entity.MrRobotRuntimeRecord.group=WCS
i18n_entity.MrRobotRuntimeRecord.label=移動ロボット運行記録
i18n_entity.MrRobotRuntimeRecord.listCard.ctOrders.prefix=容器搬送リスト
i18n_entity.MrRobotRuntimeRecord.listCard.rtBins.prefix=荷物
i18n_entity.MrRobotRuntimeRecord.listCard.rtCurrentOrder.prefix=現在搬送リスト
i18n_entity.MrRobotSystemConfig.fields.category.label=分類
i18n_entity.MrRobotSystemConfig.fields.connectionType.inlineOptionBill.items.GwWs.label=ゲートウェイ接続サーバー（GWチャネル）
i18n_entity.MrRobotSystemConfig.fields.connectionType.inlineOptionBill.items.GwWsLight.label=ゲートウェイ接続サーバー（光通信）
i18n_entity.MrRobotSystemConfig.fields.connectionType.inlineOptionBill.items.Mock.label=シミュレーション
i18n_entity.MrRobotSystemConfig.fields.connectionType.inlineOptionBill.items.Rbk.label=サーバーとロボット接続
i18n_entity.MrRobotSystemConfig.fields.connectionType.label=接続タイプ
i18n_entity.MrRobotSystemConfig.fields.createdBy.label=作成者
i18n_entity.MrRobotSystemConfig.fields.createdOn.label=作成時間
i18n_entity.MrRobotSystemConfig.fields.disabled.label=使用停止
i18n_entity.MrRobotSystemConfig.fields.gwAuthId.label=ゲートウェイ登録ID
i18n_entity.MrRobotSystemConfig.fields.gwAuthSecret.label=ゲートウェイログインキー
i18n_entity.MrRobotSystemConfig.fields.id.label=ID
i18n_entity.MrRobotSystemConfig.fields.image.label=イメージ図
i18n_entity.MrRobotSystemConfig.fields.modifiedBy.label=最終改訂人
i18n_entity.MrRobotSystemConfig.fields.modifiedOn.label=最終変更時間
i18n_entity.MrRobotSystemConfig.fields.offDuty.label=受注しない
i18n_entity.MrRobotSystemConfig.fields.robotHost.label=ロボットアドレス
i18n_entity.MrRobotSystemConfig.fields.scene.label=シーン
i18n_entity.MrRobotSystemConfig.fields.selfBinNum.label=最大荷物積載数
i18n_entity.MrRobotSystemConfig.fields.ssl.label=SSL/TSL 暗号化
i18n_entity.MrRobotSystemConfig.fields.tags.label=ラベル
i18n_entity.MrRobotSystemConfig.fields.taskMode.inlineOptionBill.items.Custom.label=カスタマイズ配車
i18n_entity.MrRobotSystemConfig.fields.taskMode.inlineOptionBill.items.SingleRobot.label=M4A1
i18n_entity.MrRobotSystemConfig.fields.taskMode.inlineOptionBill.items.Tom.label=Core
i18n_entity.MrRobotSystemConfig.fields.taskMode.label=タスクモード
i18n_entity.MrRobotSystemConfig.fields.tomId.label=弊社配車 ID
i18n_entity.MrRobotSystemConfig.fields.vendor.inlineOptionBill.items.Hai.label=海柔
i18n_entity.MrRobotSystemConfig.fields.vendor.inlineOptionBill.items.Hik.label=海康
i18n_entity.MrRobotSystemConfig.fields.vendor.inlineOptionBill.items.Seer.label=弊社
i18n_entity.MrRobotSystemConfig.fields.vendor.label=ベンダー
i18n_entity.MrRobotSystemConfig.fields.version.label=バージョン修正
i18n_entity.MrRobotSystemConfig.group=WCS
i18n_entity.MrRobotSystemConfig.label=移動ロボットシステム設定
i18n_entity.MrRobotSystemConfig.listCard.scene.prefix=シーン
i18n_entity.MrRobotSystemConfig.listCard.selfBinNum.prefix=最大積載
i18n_entity.NdcOrder.fields.allowLoad.label=荷役許可
i18n_entity.NdcOrder.fields.allowUnload.label=荷卸し許可
i18n_entity.NdcOrder.fields.createdBy.label=作成者
i18n_entity.NdcOrder.fields.createdOn.label=作成時間
i18n_entity.NdcOrder.fields.endBin.label=終点
i18n_entity.NdcOrder.fields.falconId.label=猎鹰タスク
i18n_entity.NdcOrder.fields.id.label=ID
i18n_entity.NdcOrder.fields.ikey.label=ikey
i18n_entity.NdcOrder.fields.index.label=index
i18n_entity.NdcOrder.fields.modifiedBy.label=最終改訂人
i18n_entity.NdcOrder.fields.modifiedOn.label=最終変更時間
i18n_entity.NdcOrder.fields.priority.label=優先級
i18n_entity.NdcOrder.fields.startBin.label=始点
i18n_entity.NdcOrder.fields.status.label=状態
i18n_entity.NdcOrder.fields.version.label=バージョン
i18n_entity.NdcOrder.group=NDC
i18n_entity.NdcOrder.label=NDC 運送リスト
i18n_entity.OrderFlowRecord.fields.createdBy.label=作成者
i18n_entity.OrderFlowRecord.fields.createdOn.label=作成時間
i18n_entity.OrderFlowRecord.fields.id.label=ID
i18n_entity.OrderFlowRecord.fields.modifiedBy.label=最終改訂人
i18n_entity.OrderFlowRecord.fields.modifiedOn.label=最終変更時間
i18n_entity.OrderFlowRecord.fields.pushType.label=タイプ
i18n_entity.OrderFlowRecord.fields.sourceOrderId.label=元の注文番号
i18n_entity.OrderFlowRecord.fields.sourceOrderName.label=元の注文名
i18n_entity.OrderFlowRecord.fields.targetOrderId.label=新規注文番号
i18n_entity.OrderFlowRecord.fields.targetOrderName.label=新規注文名
i18n_entity.OrderFlowRecord.fields.targetOrderType.label=新規注文タイプ
i18n_entity.OrderFlowRecord.fields.txId.label=事務ID
i18n_entity.OrderFlowRecord.fields.version.label=バージョン修正
i18n_entity.OrderFlowRecord.group=Core
i18n_entity.OrderFlowRecord.label=オーダーリストフロー記録
i18n_entity.PickOrder.fields.allUsed.label=ワンパレットピッキング
i18n_entity.PickOrder.fields.btLines.label=リスト行
i18n_entity.PickOrder.fields.btOrderKind.inlineOptionBill.items.NormalTask.label=積荷タスク
i18n_entity.PickOrder.fields.btOrderKind.label=タイプ
i18n_entity.PickOrder.fields.btOrderState.inlineOptionBill.items.Done.label=ピッキング完了
i18n_entity.PickOrder.fields.btOrderState.inlineOptionBill.items.Todo.label=ピッキング待ち
i18n_entity.PickOrder.fields.btOrderState.label=業務状態
i18n_entity.PickOrder.fields.btOrderStateReason.label=状態説明
i18n_entity.PickOrder.fields.container.label=容器
i18n_entity.PickOrder.fields.containerBackOrderId.label=容器回库運送リスト番号
i18n_entity.PickOrder.fields.containerOutDone.label=容器出庫搬送完了
i18n_entity.PickOrder.fields.containerOutOrderId.label=容器出庫運送リスト番号
i18n_entity.PickOrder.fields.createdBy.label=作成者
i18n_entity.PickOrder.fields.createdOn.label=作成時間
i18n_entity.PickOrder.fields.id.label=リスト番号
i18n_entity.PickOrder.fields.modifiedBy.label=最終改訂人
i18n_entity.PickOrder.fields.modifiedOn.label=最終変更時間
i18n_entity.PickOrder.fields.sourceOrderId.label=出庫リスト番号
i18n_entity.PickOrder.fields.submittedPostProcessed.label=後処理者完了
i18n_entity.PickOrder.fields.version.label=バージョン修正
i18n_entity.PickOrder.group=Warehouse
i18n_entity.PickOrder.label=ピッキングリスト
i18n_entity.PickOrder.listCard.container.prefix=容器
i18n_entity.PickOrder.listCard.sourceOrderId.prefix=出庫リスト
i18n_entity.PickOrder.states.states.Done.label=ピッキング完了
i18n_entity.PickOrder.states.states.Todo.label=ピッキング待ち
i18n_entity.PickOrder.states.states.Todo.nextStates.Done.buttonLabel=完了
i18n_entity.PickOrderLine.fields.btLineNo.label=行番号
i18n_entity.PickOrderLine.fields.btMaterial.label=商品
i18n_entity.PickOrderLine.fields.btMaterialCategory.label=商品分類番号
i18n_entity.PickOrderLine.fields.btMaterialCategoryName.label=商品分類名称
i18n_entity.PickOrderLine.fields.btMaterialId.label=商品番号
i18n_entity.PickOrderLine.fields.btMaterialImage.label=商品イメージ図
i18n_entity.PickOrderLine.fields.btMaterialModel.label=商品型番
i18n_entity.PickOrderLine.fields.btMaterialName.label=商品名称
i18n_entity.PickOrderLine.fields.btMaterialSpec.label=商品仕様
i18n_entity.PickOrderLine.fields.btParentId.label=所属オーダーリスト
i18n_entity.PickOrderLine.fields.container.label=容器
i18n_entity.PickOrderLine.fields.createdBy.label=作成者
i18n_entity.PickOrderLine.fields.createdOn.label=作成時間
i18n_entity.PickOrderLine.fields.id.label=ID
i18n_entity.PickOrderLine.fields.lotNo.label=ロット番号
i18n_entity.PickOrderLine.fields.modifiedBy.label=最終改訂人
i18n_entity.PickOrderLine.fields.modifiedOn.label=最終変更時間
i18n_entity.PickOrderLine.fields.planQty.label=希望ピッキング数量数
i18n_entity.PickOrderLine.fields.qty.label=実際ピッキング数量
i18n_entity.PickOrderLine.fields.qualityLevel.label=品質レベル
i18n_entity.PickOrderLine.fields.sourceLineId.label=元リスト行 ID
i18n_entity.PickOrderLine.fields.sourceLineNo.label=元行番号
i18n_entity.PickOrderLine.fields.sourceOrderId.label=元オーダーリスト
i18n_entity.PickOrderLine.fields.subContainerId.label=格子番号
i18n_entity.PickOrderLine.fields.version.label=バージョン修正
i18n_entity.PickOrderLine.group=Warehouse
i18n_entity.PickOrderLine.label=ピッキングリスト行
i18n_entity.PickOrderLine.listCard.btMaterialId.prefix=商品番号
i18n_entity.PickOrderLine.listCard.lotNo.prefix=ロット
i18n_entity.PickOrderLine.listCard.planQty.prefix=希望ピッキング
i18n_entity.PickOrderLine.listCard.qty.prefix=実際ピッキング
i18n_entity.PickOrderLine.listCard.subContainerId.prefix=格子番号
i18n_entity.PlcDeviceConfig.fields.autoRetry.label=自動再接続
i18n_entity.PlcDeviceConfig.fields.createdBy.label=作成者
i18n_entity.PlcDeviceConfig.fields.createdOn.label=作成時間
i18n_entity.PlcDeviceConfig.fields.disabled.label=使用停止
i18n_entity.PlcDeviceConfig.fields.endpoint.label=絶族文字列
i18n_entity.PlcDeviceConfig.fields.host.label=アドレス/IP
i18n_entity.PlcDeviceConfig.fields.id.label=名称（ID）
i18n_entity.PlcDeviceConfig.fields.maxRetry.label=最大再試行回数
i18n_entity.PlcDeviceConfig.fields.modifiedBy.label=最終改訂人
i18n_entity.PlcDeviceConfig.fields.modifiedOn.label=最終変更時間
i18n_entity.PlcDeviceConfig.fields.port.label=ポート
i18n_entity.PlcDeviceConfig.fields.rack.label=S7ラック番号
i18n_entity.PlcDeviceConfig.fields.retryDelay.label=再試行待ち（ミリ秒）
i18n_entity.PlcDeviceConfig.fields.slot.label=S7 スロット番号
i18n_entity.PlcDeviceConfig.fields.subType.label=サブタイプ
i18n_entity.PlcDeviceConfig.fields.timeout.label=接続タイムアウト（ミリ秒）
i18n_entity.PlcDeviceConfig.fields.type.inlineOptionBill.items.Modbus.label=Modbus
i18n_entity.PlcDeviceConfig.fields.type.inlineOptionBill.items.OPCUA.label=OPCUA
i18n_entity.PlcDeviceConfig.fields.type.inlineOptionBill.items.S7.label=S7
i18n_entity.PlcDeviceConfig.fields.type.label=タイプ
i18n_entity.PlcDeviceConfig.fields.version.label=バージョン修正
i18n_entity.PlcDeviceConfig.group=WCS
i18n_entity.PlcDeviceConfig.label=PLC 設備設置
i18n_entity.PlcDeviceConfig.listCard.host.prefix=アドレス/IP
i18n_entity.PlcDeviceConfig.listCard.port.prefix=ポート
i18n_entity.PlcRwLog.fields.action.label=操作
i18n_entity.PlcRwLog.fields.createdBy.label=作成者
i18n_entity.PlcRwLog.fields.createdOn.label=作成時間
i18n_entity.PlcRwLog.fields.deviceName.label=設備名
i18n_entity.PlcRwLog.fields.deviceType.label=設備タイプ
i18n_entity.PlcRwLog.fields.id.label=ID
i18n_entity.PlcRwLog.fields.ip.label=IP
i18n_entity.PlcRwLog.fields.modifiedBy.label=最終改訂人
i18n_entity.PlcRwLog.fields.modifiedOn.label=最終変更時間
i18n_entity.PlcRwLog.fields.oldValueDesc.label=元値
i18n_entity.PlcRwLog.fields.reason.label=原因
i18n_entity.PlcRwLog.fields.rw.inlineOptionBill.items.Read.label=読み
i18n_entity.PlcRwLog.fields.rw.inlineOptionBill.items.Write.label=書き
i18n_entity.PlcRwLog.fields.rw.label=読み書き
i18n_entity.PlcRwLog.fields.valueDesc.label=値
i18n_entity.PlcRwLog.fields.version.label=バージョン修正
i18n_entity.PlcRwLog.group=WCS
i18n_entity.PlcRwLog.label=PLC67.読み書き記録
i18n_entity.PlcRwLog.listCard.oldValueDesc.prefix=元の値
i18n_entity.PlcRwLog.listCard.valueDesc.prefix=値
i18n_entity.PutinContainerOrder.fields.btLines.label=シングル行
i18n_entity.PutinContainerOrder.fields.btOrderKind.inlineOptionBill.items.NormalTask.label=積荷タスク
i18n_entity.PutinContainerOrder.fields.btOrderKind.label=タイプ
i18n_entity.PutinContainerOrder.fields.btOrderState.inlineOptionBill.items.Done.label=格納完了
i18n_entity.PutinContainerOrder.fields.btOrderState.inlineOptionBill.items.Filled.label=積み込み完了
i18n_entity.PutinContainerOrder.fields.btOrderState.inlineOptionBill.items.Todo.label=積み込み待ち
i18n_entity.PutinContainerOrder.fields.btOrderState.label=業務状態
i18n_entity.PutinContainerOrder.fields.btOrderStateReason.label=状態説明
i18n_entity.PutinContainerOrder.fields.container.label=容器
i18n_entity.PutinContainerOrder.fields.containerBackOrderId.label=容器回库運送リスト番号
i18n_entity.PutinContainerOrder.fields.containerOutDone.label=容器出庫搬送完了
i18n_entity.PutinContainerOrder.fields.containerOutOrderId.label=容器出庫運送リスト番号
i18n_entity.PutinContainerOrder.fields.createdBy.label=作成者
i18n_entity.PutinContainerOrder.fields.createdOn.label=作成時間
i18n_entity.PutinContainerOrder.fields.id.label=リスト番号
i18n_entity.PutinContainerOrder.fields.modifiedBy.label=最終改訂人
i18n_entity.PutinContainerOrder.fields.modifiedOn.label=最終変更時間
i18n_entity.PutinContainerOrder.fields.sourceOrderId.label=入庫リスト番号
i18n_entity.PutinContainerOrder.fields.version.label=バージョン修正
i18n_entity.PutinContainerOrder.group=Warehouse
i18n_entity.PutinContainerOrder.label=積荷リスト
i18n_entity.PutinContainerOrder.listCard.container.prefix=容器
i18n_entity.PutinContainerOrder.states.states.Done.label=格納完了
i18n_entity.PutinContainerOrder.states.states.Filled.label=積み込み完了
i18n_entity.PutinContainerOrder.states.states.Todo.label=積み込み待ち
i18n_entity.PutinContainerOrder.states.states.Todo.nextStates.Filled.buttonLabel=完了
i18n_entity.PutinContainerOrderLine.fields.btLineNo.label=行番号
i18n_entity.PutinContainerOrderLine.fields.btMaterial.label=商品
i18n_entity.PutinContainerOrderLine.fields.btMaterialCategory.label=商品分類番号
i18n_entity.PutinContainerOrderLine.fields.btMaterialCategoryName.label=商品分類名称
i18n_entity.PutinContainerOrderLine.fields.btMaterialId.label=商品番号
i18n_entity.PutinContainerOrderLine.fields.btMaterialImage.label=商品イメージ図
i18n_entity.PutinContainerOrderLine.fields.btMaterialModel.label=商品型番
i18n_entity.PutinContainerOrderLine.fields.btMaterialName.label=商品名称
i18n_entity.PutinContainerOrderLine.fields.btMaterialSpec.label=商品仕様
i18n_entity.PutinContainerOrderLine.fields.btParentId.label=所属オーダーリスト
i18n_entity.PutinContainerOrderLine.fields.container.label=容器
i18n_entity.PutinContainerOrderLine.fields.createdBy.label=作成者
i18n_entity.PutinContainerOrderLine.fields.createdOn.label=作成時間
i18n_entity.PutinContainerOrderLine.fields.id.label=ID
i18n_entity.PutinContainerOrderLine.fields.lotNo.label=ロット番号
i18n_entity.PutinContainerOrderLine.fields.modifiedBy.label=最終改訂人
i18n_entity.PutinContainerOrderLine.fields.modifiedOn.label=最終変更時間
i18n_entity.PutinContainerOrderLine.fields.planQty.label=计划装入数
i18n_entity.PutinContainerOrderLine.fields.price.label=単価
i18n_entity.PutinContainerOrderLine.fields.qty.label=実際積み込み数
i18n_entity.PutinContainerOrderLine.fields.qualityLevel.label=品質レベル
i18n_entity.PutinContainerOrderLine.fields.sourceLineId.label=元リスト行 ID
i18n_entity.PutinContainerOrderLine.fields.sourceLineNo.label=元行番号
i18n_entity.PutinContainerOrderLine.fields.sourceOrderId.label=元オーダーリスト
i18n_entity.PutinContainerOrderLine.fields.subContainerId.label=格子番号
i18n_entity.PutinContainerOrderLine.fields.unitLabel.label=単位名称
i18n_entity.PutinContainerOrderLine.fields.version.label=バージョン修正
i18n_entity.PutinContainerOrderLine.group=Warehouse
i18n_entity.PutinContainerOrderLine.label=積荷リスト行
i18n_entity.PutinContainerOrderLine.listCard.btMaterialId.prefix=商品番号
i18n_entity.PutinContainerOrderLine.listCard.btMaterialName.prefix=商品名称
i18n_entity.PutinContainerOrderLine.listCard.lotNo.prefix=ロット番号
i18n_entity.PutinContainerOrderLine.listCard.planQty.prefix=予定の積み込み
i18n_entity.PutinContainerOrderLine.listCard.qty.prefix=実際積み込み
i18n_entity.PutinContainerOrderLine.listCard.subContainerId.prefix=格子番号
i18n_entity.QsInboundOrder.fields.btBzKind.inlineOptionBill.items.Normal.label=一般入庫
i18n_entity.QsInboundOrder.fields.btBzKind.inlineOptionBill.items.Other.label=その他入庫
i18n_entity.QsInboundOrder.fields.btBzKind.label=業務タイプ
i18n_entity.QsInboundOrder.fields.btLines.label=リスト行
i18n_entity.QsInboundOrder.fields.btOrderState.inlineOptionBill.items.Cancelled.label=キャンセル済み
i18n_entity.QsInboundOrder.fields.btOrderState.inlineOptionBill.items.Committed.label=提出済み
i18n_entity.QsInboundOrder.fields.btOrderState.inlineOptionBill.items.Init.label=未提出
i18n_entity.QsInboundOrder.fields.btOrderState.label=業務状態
i18n_entity.QsInboundOrder.fields.btOrderStateReason.label=状態説明
i18n_entity.QsInboundOrder.fields.callContainerAll.label=空き容器分配完了
i18n_entity.QsInboundOrder.fields.createdBy.label=作成者
i18n_entity.QsInboundOrder.fields.createdOn.label=作成時間
i18n_entity.QsInboundOrder.fields.id.label=リスト番号
i18n_entity.QsInboundOrder.fields.modifiedBy.label=最終改訂人
i18n_entity.QsInboundOrder.fields.modifiedOn.label=最終変更時間
i18n_entity.QsInboundOrder.fields.otherType.label=その他タイプ
i18n_entity.QsInboundOrder.fields.priority.label=優先級
i18n_entity.QsInboundOrder.fields.remark.label=備考
i18n_entity.QsInboundOrder.fields.ro.label=元組織
i18n_entity.QsInboundOrder.fields.version.label=バージョン
i18n_entity.QsInboundOrder.group=Quick Store
i18n_entity.QsInboundOrder.kinds.kinds.Normal.label=一般入庫
i18n_entity.QsInboundOrder.kinds.kinds.Other.label=その他入庫
i18n_entity.QsInboundOrder.label=QS 入庫リスト
i18n_entity.QsInboundOrder.states.states.Cancelled.label=キャンセル済み
i18n_entity.QsInboundOrder.states.states.Committed.label=提出済み
i18n_entity.QsInboundOrder.states.states.Init.label=未提出
i18n_entity.QsInboundOrder.states.states.Init.nextStates.Cancelled.buttonLabel=キャンセル
i18n_entity.QsInboundOrder.states.states.Init.nextStates.Committed.buttonLabel=提出
i18n_entity.QsInboundOrderLine.fields.btLineNo.label=行番号
i18n_entity.QsInboundOrderLine.fields.btMaterial.label=商品
i18n_entity.QsInboundOrderLine.fields.btMaterialCategory.label=商品分類番号
i18n_entity.QsInboundOrderLine.fields.btMaterialCategoryName.label=商品分類名称
i18n_entity.QsInboundOrderLine.fields.btMaterialId.label=商品番号
i18n_entity.QsInboundOrderLine.fields.btMaterialImage.label=商品イメージ図
i18n_entity.QsInboundOrderLine.fields.btMaterialModel.label=商品型番
i18n_entity.QsInboundOrderLine.fields.btMaterialName.label=商品名称
i18n_entity.QsInboundOrderLine.fields.btMaterialSpec.label=商品仕様
i18n_entity.QsInboundOrderLine.fields.btParentId.label=所属出庫リスト
i18n_entity.QsInboundOrderLine.fields.ccQty.label=空き容器分配数量
i18n_entity.QsInboundOrderLine.fields.createdBy.label=作成者
i18n_entity.QsInboundOrderLine.fields.createdOn.label=作成時間
i18n_entity.QsInboundOrderLine.fields.id.label=ID
i18n_entity.QsInboundOrderLine.fields.lotNo.label=ロット番号
i18n_entity.QsInboundOrderLine.fields.modifiedBy.label=最終改訂人
i18n_entity.QsInboundOrderLine.fields.modifiedOn.label=最終変更時間
i18n_entity.QsInboundOrderLine.fields.priority.label=優先級
i18n_entity.QsInboundOrderLine.fields.qty.label=入庫数量
i18n_entity.QsInboundOrderLine.fields.ro.label=元組織
i18n_entity.QsInboundOrderLine.fields.version.label=バージョン
i18n_entity.QsInboundOrderLine.group=Quick Store
i18n_entity.QsInboundOrderLine.label=QS 入庫リスト行
i18n_entity.QsInboundOrderLine.listCard.qty.prefix=数量
i18n_entity.QsMoveBinOrder.fields.actualToBin.label=実際終点ロケーション
i18n_entity.QsMoveBinOrder.fields.btBzKind.inlineOptionBill.items.Normal.label=デフォルト
i18n_entity.QsMoveBinOrder.fields.btBzKind.label=業務タイプ
i18n_entity.QsMoveBinOrder.fields.container.label=容器
i18n_entity.QsMoveBinOrder.fields.createdBy.label=作成者
i18n_entity.QsMoveBinOrder.fields.createdOn.label=作成時間
i18n_entity.QsMoveBinOrder.fields.expectToBin.label=指定終点ロケーション
i18n_entity.QsMoveBinOrder.fields.expectToDistrict.label=指定終点倉庫エリア
i18n_entity.QsMoveBinOrder.fields.fromBin.label=始点ロケーション
i18n_entity.QsMoveBinOrder.fields.id.label=ID
i18n_entity.QsMoveBinOrder.fields.modifiedBy.label=最終改訂人
i18n_entity.QsMoveBinOrder.fields.modifiedOn.label=最終変更時間
i18n_entity.QsMoveBinOrder.fields.oldInvLines.label=移動時容器内の在庫明細
i18n_entity.QsMoveBinOrder.fields.remark.label=説明
i18n_entity.QsMoveBinOrder.fields.version.label=バージョン修正
i18n_entity.QsMoveBinOrder.group=Quick Store
i18n_entity.QsMoveBinOrder.kinds.kinds.Normal.label=デフォルト
i18n_entity.QsMoveBinOrder.label=QS 移庫リスト
i18n_entity.QsMoveBinOrder.listCard.container.prefix=容器
i18n_entity.QsMoveBinOrder.listCard.fromBin.suffix=->
i18n_entity.QsOldInvLine.fields.amount.label=金额
i18n_entity.QsOldInvLine.fields.btLineNo.label=行番号
i18n_entity.QsOldInvLine.fields.btMaterial.label=商品
i18n_entity.QsOldInvLine.fields.btMaterialCategory.label=商品分類番号
i18n_entity.QsOldInvLine.fields.btMaterialCategoryName.label=商品分類名称
i18n_entity.QsOldInvLine.fields.btMaterialImage.label=商品イメージ図
i18n_entity.QsOldInvLine.fields.btMaterialModel.label=商品型番
i18n_entity.QsOldInvLine.fields.btMaterialName.label=商品名称
i18n_entity.QsOldInvLine.fields.btMaterialSpec.label=商品仕様
i18n_entity.QsOldInvLine.fields.btParentId.label=所属受注オーダー
i18n_entity.QsOldInvLine.fields.createdBy.label=作成者
i18n_entity.QsOldInvLine.fields.createdOn.label=作成時間
i18n_entity.QsOldInvLine.fields.expDate.label=有效期間
i18n_entity.QsOldInvLine.fields.id.label=ID
i18n_entity.QsOldInvLine.fields.inboundOn.label=入庫時間
i18n_entity.QsOldInvLine.fields.leafContainer.label=内側容器
i18n_entity.QsOldInvLine.fields.lotNo.label=ロット番号
i18n_entity.QsOldInvLine.fields.matLotNo.label=ロット
i18n_entity.QsOldInvLine.fields.matSerialNo.label=シリアル番号
i18n_entity.QsOldInvLine.fields.mfgDate.label=生産日付
i18n_entity.QsOldInvLine.fields.modifiedBy.label=最終改訂人
i18n_entity.QsOldInvLine.fields.modifiedOn.label=最終変更時間
i18n_entity.QsOldInvLine.fields.owner.label=荷主
i18n_entity.QsOldInvLine.fields.price.label=単価
i18n_entity.QsOldInvLine.fields.qty.label=数量
i18n_entity.QsOldInvLine.fields.refInvId.label=在庫明細 ID紐付け
i18n_entity.QsOldInvLine.fields.subContainerId.label=格子
i18n_entity.QsOldInvLine.fields.topContainer.label=外側容器
i18n_entity.QsOldInvLine.fields.vendor.label=サプライヤー
i18n_entity.QsOldInvLine.fields.version.label=バージョン修正
i18n_entity.QsOldInvLine.group=Quick Store
i18n_entity.QsOldInvLine.label=QS 現在在庫明細
i18n_entity.QsOldInvLine.listStats.items[0].label=処理中（ロック）
i18n_entity.QsOutboundOrder.fields.btLines.label=リスト行
i18n_entity.QsOutboundOrder.fields.btOrderKind.inlineOptionBill.items.Default.label=デフォルト
i18n_entity.QsOutboundOrder.fields.btOrderKind.label=タイプ
i18n_entity.QsOutboundOrder.fields.btOrderState.inlineOptionBill.items.Committed.label=提出済み
i18n_entity.QsOutboundOrder.fields.btOrderState.inlineOptionBill.items.Init.label=未提出
i18n_entity.QsOutboundOrder.fields.btOrderState.label=業務状態
i18n_entity.QsOutboundOrder.fields.btOrderStateReason.label=状態説明
i18n_entity.QsOutboundOrder.fields.createdBy.label=作成者
i18n_entity.QsOutboundOrder.fields.createdOn.label=作成時間
i18n_entity.QsOutboundOrder.fields.id.label=リスト番号
i18n_entity.QsOutboundOrder.fields.invAssignedAll.label=在庫分配完了
i18n_entity.QsOutboundOrder.fields.modifiedBy.label=最終改訂人
i18n_entity.QsOutboundOrder.fields.modifiedOn.label=最終変更時間
i18n_entity.QsOutboundOrder.fields.priority.label=優先級
i18n_entity.QsOutboundOrder.fields.remark.label=備考
i18n_entity.QsOutboundOrder.fields.ro.label=元組織
i18n_entity.QsOutboundOrder.fields.typePriority.label=タイプ優先級
i18n_entity.QsOutboundOrder.fields.version.label=バージョン
i18n_entity.QsOutboundOrder.group=Quick Store
i18n_entity.QsOutboundOrder.label=QS 出庫リスト
i18n_entity.QsOutboundOrder.states.states.Committed.label=提出済み
i18n_entity.QsOutboundOrder.states.states.Init.label=未提出
i18n_entity.QsOutboundOrder.states.states.Init.nextStates.Committed.buttonLabel=提出
i18n_entity.QsOutboundOrderLine.fields.btLineNo.label=行番号
i18n_entity.QsOutboundOrderLine.fields.btMaterial.label=商品
i18n_entity.QsOutboundOrderLine.fields.btMaterialCategory.label=商品分類番号
i18n_entity.QsOutboundOrderLine.fields.btMaterialCategoryName.label=商品分類名称
i18n_entity.QsOutboundOrderLine.fields.btMaterialId.label=商品番号
i18n_entity.QsOutboundOrderLine.fields.btMaterialImage.label=商品イメージ図
i18n_entity.QsOutboundOrderLine.fields.btMaterialModel.label=商品型番
i18n_entity.QsOutboundOrderLine.fields.btMaterialName.label=商品名称
i18n_entity.QsOutboundOrderLine.fields.btMaterialSpec.label=商品仕様
i18n_entity.QsOutboundOrderLine.fields.btParentId.label=所属出庫单
i18n_entity.QsOutboundOrderLine.fields.createdBy.label=作成者
i18n_entity.QsOutboundOrderLine.fields.createdOn.label=作成時間
i18n_entity.QsOutboundOrderLine.fields.id.label=ID
i18n_entity.QsOutboundOrderLine.fields.invAssignedQty.label=割当済み在庫数量
i18n_entity.QsOutboundOrderLine.fields.lotNo.label=ロット番号
i18n_entity.QsOutboundOrderLine.fields.modifiedBy.label=最終改訂人
i18n_entity.QsOutboundOrderLine.fields.modifiedOn.label=最終変更時間
i18n_entity.QsOutboundOrderLine.fields.priority.label=優先級
i18n_entity.QsOutboundOrderLine.fields.qty.label=出庫数量
i18n_entity.QsOutboundOrderLine.fields.ro.label=元組織
i18n_entity.QsOutboundOrderLine.fields.version.label=バージョン
i18n_entity.QsOutboundOrderLine.group=Quick Store
i18n_entity.QsOutboundOrderLine.label=QS 出庫リスト行
i18n_entity.QsOutboundOrderLine.listCard.qty.prefix=出庫数量
i18n_entity.QsPickOrder.fields.btLines.label=リスト行
i18n_entity.QsPickOrder.fields.btOrderKind.inlineOptionBill.items.NormalTask.label=積荷タスク
i18n_entity.QsPickOrder.fields.btOrderKind.label=タイプ
i18n_entity.QsPickOrder.fields.btOrderState.inlineOptionBill.items.Done.label=ピッキング完了
i18n_entity.QsPickOrder.fields.btOrderState.inlineOptionBill.items.Todo.label=ピッキング待ち
i18n_entity.QsPickOrder.fields.btOrderState.label=業務状態
i18n_entity.QsPickOrder.fields.btOrderStateReason.label=状態説明
i18n_entity.QsPickOrder.fields.container.label=容器
i18n_entity.QsPickOrder.fields.createdBy.label=作成者
i18n_entity.QsPickOrder.fields.createdOn.label=作成時間
i18n_entity.QsPickOrder.fields.id.label=リスト番号
i18n_entity.QsPickOrder.fields.modifiedBy.label=最終改訂人
i18n_entity.QsPickOrder.fields.modifiedOn.label=最終変更時間
i18n_entity.QsPickOrder.fields.targetBin.label=目標ロケーション
i18n_entity.QsPickOrder.fields.version.label=バージョン修正
i18n_entity.QsPickOrder.group=Quick Store
i18n_entity.QsPickOrder.label=QS ピッキングリスト
i18n_entity.QsPickOrder.listCard.container.prefix=容器
i18n_entity.QsPickOrder.states.states.Done.label=ピッキング完了
i18n_entity.QsPickOrder.states.states.Todo.label=ピッキング待ち
i18n_entity.QsPickOrder.states.states.Todo.nextStates.Done.buttonLabel=完了
i18n_entity.QsPickOrderLine.fields.btLineNo.label=行番号
i18n_entity.QsPickOrderLine.fields.btMaterial.label=商品
i18n_entity.QsPickOrderLine.fields.btMaterialCategory.label=商品分類番号
i18n_entity.QsPickOrderLine.fields.btMaterialCategoryName.label=商品分類名称
i18n_entity.QsPickOrderLine.fields.btMaterialId.label=商品番号
i18n_entity.QsPickOrderLine.fields.btMaterialImage.label=商品イメージ図
i18n_entity.QsPickOrderLine.fields.btMaterialModel.label=商品型番
i18n_entity.QsPickOrderLine.fields.btMaterialName.label=商品名称
i18n_entity.QsPickOrderLine.fields.btMaterialSpec.label=商品仕様
i18n_entity.QsPickOrderLine.fields.btParentId.label=所属オーダーリスト
i18n_entity.QsPickOrderLine.fields.createdBy.label=作成者
i18n_entity.QsPickOrderLine.fields.createdOn.label=作成時間
i18n_entity.QsPickOrderLine.fields.id.label=ID
i18n_entity.QsPickOrderLine.fields.invLayoutId.label=在庫明細 ID
i18n_entity.QsPickOrderLine.fields.lotNo.label=ロット番号
i18n_entity.QsPickOrderLine.fields.modifiedBy.label=最終改訂人
i18n_entity.QsPickOrderLine.fields.modifiedOn.label=最終変更時間
i18n_entity.QsPickOrderLine.fields.outboundLineId.label=出庫リスト行 ID
i18n_entity.QsPickOrderLine.fields.outboundLineNo.label=出庫リスト行番号
i18n_entity.QsPickOrderLine.fields.outboundOrderId.label=出庫リスト番号
i18n_entity.QsPickOrderLine.fields.planQty.label=希望ピッキング数
i18n_entity.QsPickOrderLine.fields.qty.label=実際ピッキング数
i18n_entity.QsPickOrderLine.fields.subContainerId.label=格子番号
i18n_entity.QsPickOrderLine.fields.topContainer.label=容器
i18n_entity.QsPickOrderLine.fields.version.label=バージョン修正
i18n_entity.QsPickOrderLine.group=Quick Store
i18n_entity.QsPickOrderLine.label=QS ピッキングリスト行
i18n_entity.QsPickOrderLine.listCard.planQty.prefix=希望数量
i18n_entity.QsPickOrderLine.listCard.qty.prefix=実際数量
i18n_entity.QsPutOnContainerOrder.fields.bin.label=格納ロケーション
i18n_entity.QsPutOnContainerOrder.fields.container.label=容器
i18n_entity.QsPutOnContainerOrder.fields.createdBy.label=作成者
i18n_entity.QsPutOnContainerOrder.fields.createdOn.label=作成時間
i18n_entity.QsPutOnContainerOrder.fields.id.label=ID
i18n_entity.QsPutOnContainerOrder.fields.modifiedBy.label=最終改訂人
i18n_entity.QsPutOnContainerOrder.fields.modifiedOn.label=最終変更時間
i18n_entity.QsPutOnContainerOrder.fields.oldInvLines.label=格納時容器内の在庫明細
i18n_entity.QsPutOnContainerOrder.fields.version.label=バージョン修正
i18n_entity.QsPutOnContainerOrder.group=Quick Store
i18n_entity.QsPutOnContainerOrder.label=QS 手動荷役リスト
i18n_entity.QsPutOnContainerOrder.listCard.bin.prefix=格納ロケーション
i18n_entity.QsPutOrder.fields.bin.label=格納ロケーション
i18n_entity.QsPutOrder.fields.btLines.label=リスト行
i18n_entity.QsPutOrder.fields.btOrderKind.inlineOptionBill.items.NormalTask.label=積荷タスク
i18n_entity.QsPutOrder.fields.btOrderKind.label=タイプ
i18n_entity.QsPutOrder.fields.btOrderState.inlineOptionBill.items.Done.label=積荷完了
i18n_entity.QsPutOrder.fields.btOrderState.inlineOptionBill.items.Todo.label=積荷待ち
i18n_entity.QsPutOrder.fields.btOrderState.label=業務状態
i18n_entity.QsPutOrder.fields.btOrderStateReason.label=状態説明
i18n_entity.QsPutOrder.fields.container.label=容器
i18n_entity.QsPutOrder.fields.createdBy.label=作成者
i18n_entity.QsPutOrder.fields.createdOn.label=作成時間
i18n_entity.QsPutOrder.fields.id.label=リスト番号
i18n_entity.QsPutOrder.fields.modifiedBy.label=最終改訂人
i18n_entity.QsPutOrder.fields.modifiedOn.label=最終変更時間
i18n_entity.QsPutOrder.fields.oldInvLines.label=容器内本来在庫明細
i18n_entity.QsPutOrder.fields.targetBin.label=目標ロケーション
i18n_entity.QsPutOrder.fields.version.label=バージョン修正
i18n_entity.QsPutOrder.group=Quick Store
i18n_entity.QsPutOrder.label=QS 積荷リスト
i18n_entity.QsPutOrder.listCard.bin.prefix=格納ロケーション
i18n_entity.QsPutOrder.listCard.container.prefix=容器
i18n_entity.QsPutOrder.states.states.Done.label=積荷完了
i18n_entity.QsPutOrder.states.states.Todo.label=積荷待ち
i18n_entity.QsPutOrder.states.states.Todo.nextStates.Done.buttonLabel=完了
i18n_entity.QsPutOrderLine.fields.btLineNo.label=行番号
i18n_entity.QsPutOrderLine.fields.btMaterial.label=商品
i18n_entity.QsPutOrderLine.fields.btMaterialCategory.label=商品分類番号
i18n_entity.QsPutOrderLine.fields.btMaterialCategoryName.label=商品分類名称
i18n_entity.QsPutOrderLine.fields.btMaterialId.label=商品番号
i18n_entity.QsPutOrderLine.fields.btMaterialImage.label=商品イメージ図
i18n_entity.QsPutOrderLine.fields.btMaterialModel.label=商品型番
i18n_entity.QsPutOrderLine.fields.btMaterialName.label=商品名称
i18n_entity.QsPutOrderLine.fields.btMaterialSpec.label=商品仕様
i18n_entity.QsPutOrderLine.fields.btParentId.label=所属オーダーリスト
i18n_entity.QsPutOrderLine.fields.createdBy.label=作成者
i18n_entity.QsPutOrderLine.fields.createdOn.label=作成時間
i18n_entity.QsPutOrderLine.fields.id.label=ID
i18n_entity.QsPutOrderLine.fields.lotNo.label=ロット番号
i18n_entity.QsPutOrderLine.fields.modifiedBy.label=最終改訂人
i18n_entity.QsPutOrderLine.fields.modifiedOn.label=最終変更時間
i18n_entity.QsPutOrderLine.fields.planQty.label=希望積荷数量
i18n_entity.QsPutOrderLine.fields.qty.label=実際積荷数量
i18n_entity.QsPutOrderLine.fields.subContainerId.label=格子番号
i18n_entity.QsPutOrderLine.fields.version.label=バージョン修正
i18n_entity.QsPutOrderLine.group=Quick Store
i18n_entity.QsPutOrderLine.label=QS 積荷リスト行
i18n_entity.QsPutOrderLine.listCard.planQty.prefix=希望積荷
i18n_entity.QsPutOrderLine.listCard.qty.prefix=実際積荷
i18n_entity.QsTakeOffContainerOrder.fields.bin.label=荷卸しロケーション
i18n_entity.QsTakeOffContainerOrder.fields.container.label=容器
i18n_entity.QsTakeOffContainerOrder.fields.createdBy.label=作成者
i18n_entity.QsTakeOffContainerOrder.fields.createdOn.label=作成時間
i18n_entity.QsTakeOffContainerOrder.fields.id.label=ID
i18n_entity.QsTakeOffContainerOrder.fields.keepInv.label=在庫保留
i18n_entity.QsTakeOffContainerOrder.fields.modifiedBy.label=最終改訂人
i18n_entity.QsTakeOffContainerOrder.fields.modifiedOn.label=最終変更時間
i18n_entity.QsTakeOffContainerOrder.fields.oldInvLines.label=荷卸し容器内在庫明細
i18n_entity.QsTakeOffContainerOrder.fields.version.label=バージョン修正
i18n_entity.QsTakeOffContainerOrder.group=Quick Store
i18n_entity.QsTakeOffContainerOrder.label=QS 手動荷卸しリスト
i18n_entity.QsTakeOffContainerOrder.listCard.bin.prefix=荷卸しロケーション
i18n_entity.RaSingleBatteryRecord.fields.batteryLevel.label=バッテリー残量
i18n_entity.RaSingleBatteryRecord.fields.batteryTemp.label=バッテリー温度
i18n_entity.RaSingleBatteryRecord.fields.charging.label=充電必要かどうか
i18n_entity.RaSingleBatteryRecord.fields.createdBy.label=作成者
i18n_entity.RaSingleBatteryRecord.fields.createdOn.label=作成時間
i18n_entity.RaSingleBatteryRecord.fields.current.label=電流
i18n_entity.RaSingleBatteryRecord.fields.id.label=番号
i18n_entity.RaSingleBatteryRecord.fields.modifiedBy.label=最終改訂人
i18n_entity.RaSingleBatteryRecord.fields.modifiedOn.label=最終変更時間
i18n_entity.RaSingleBatteryRecord.fields.robotName.label=ロボット名称
i18n_entity.RaSingleBatteryRecord.fields.version.label=バージョン修正
i18n_entity.RaSingleBatteryRecord.fields.voltage.label=電圧
i18n_entity.RaSingleBatteryRecord.group=RaSingle
i18n_entity.RaSingleBatteryRecord.label=ロボットバッテリー記録
i18n_entity.RaSingleNavigateRecord.fields.cost.label=必要時間
i18n_entity.RaSingleNavigateRecord.fields.createdBy.label=作成者
i18n_entity.RaSingleNavigateRecord.fields.createdOn.label=作成時間
i18n_entity.RaSingleNavigateRecord.fields.id.label=番号
i18n_entity.RaSingleNavigateRecord.fields.modifiedBy.label=最終改訂人
i18n_entity.RaSingleNavigateRecord.fields.modifiedOn.label=最終変更時間
i18n_entity.RaSingleNavigateRecord.fields.robotName.label=ロボット名称
i18n_entity.RaSingleNavigateRecord.fields.targetId.label=目標ステーション
i18n_entity.RaSingleNavigateRecord.fields.targetPoint.label=目標座標位置
i18n_entity.RaSingleNavigateRecord.fields.taskId.label=ナビゲーション ID
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.0.label=NONE
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.1.label=WAITING
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.2.label=RUNNING
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.3.label=SUSPENDED
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.4.label=COMPLETED
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.5.label=FAILED
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.6.label=CANCELED
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.label=ナビゲーション状態
i18n_entity.RaSingleNavigateRecord.fields.taskType.inlineOptionBill.items.0.label=ナビゲーションなし
i18n_entity.RaSingleNavigateRecord.fields.taskType.inlineOptionBill.items.1.label=自由ナビゲーション到着位置
i18n_entity.RaSingleNavigateRecord.fields.taskType.inlineOptionBill.items.100.label=その他
i18n_entity.RaSingleNavigateRecord.fields.taskType.inlineOptionBill.items.2.label=自由ナビゲーション到着ステーション
i18n_entity.RaSingleNavigateRecord.fields.taskType.inlineOptionBill.items.3.label=パスナビゲーション到着ステーション
i18n_entity.RaSingleNavigateRecord.fields.taskType.inlineOptionBill.items.7.label=平行移動ジャッキンぐ
i18n_entity.RaSingleNavigateRecord.fields.taskType.label=ナビゲーションタイプ
i18n_entity.RaSingleNavigateRecord.fields.version.label=バージョン修正
i18n_entity.RaSingleNavigateRecord.group=RaSingle
i18n_entity.RaSingleNavigateRecord.label=ロボットナビゲーション記録
i18n_entity.ResourceLock.fields.createdBy.label=作成者
i18n_entity.ResourceLock.fields.createdOn.label=作成時間
i18n_entity.ResourceLock.fields.id.label=ID
i18n_entity.ResourceLock.fields.lockOn.label=ロック時間
i18n_entity.ResourceLock.fields.locked.label=ロック
i18n_entity.ResourceLock.fields.modifiedBy.label=最終改訂人
i18n_entity.ResourceLock.fields.modifiedOn.label=変更時間
i18n_entity.ResourceLock.fields.owner.label=ロックした人
i18n_entity.ResourceLock.fields.reason.label=ロック原因
i18n_entity.ResourceLock.fields.resId.label=りソース ID
i18n_entity.ResourceLock.fields.resType.label=りソースタイプ
i18n_entity.ResourceLock.fields.version.label=バージョン修正
i18n_entity.ResourceLock.group=Core
i18n_entity.ResourceLock.label=りソースロック
i18n_entity.RobotConnectedPoint.fields.bin.label=ロケーション紐付け
i18n_entity.RobotConnectedPoint.fields.channel.label=所在サブレーン
i18n_entity.RobotConnectedPoint.fields.createdBy.label=作成者
i18n_entity.RobotConnectedPoint.fields.createdOn.label=作成時間
i18n_entity.RobotConnectedPoint.fields.id.label=ID
i18n_entity.RobotConnectedPoint.fields.modifiedBy.label=最終改訂人
i18n_entity.RobotConnectedPoint.fields.modifiedOn.label=最終変更時間
i18n_entity.RobotConnectedPoint.fields.remark.label=備考
i18n_entity.RobotConnectedPoint.fields.version.label=バージョン修正
i18n_entity.RobotConnectedPoint.fields.x.label=位置 x
i18n_entity.RobotConnectedPoint.fields.y.label=位置 y
i18n_entity.RobotConnectedPoint.group=WCS
i18n_entity.RobotConnectedPoint.label=ロボット通信位置
i18n_entity.RobotConnectedPoint.listCard.bin.prefix=ロケーション紐付け
i18n_entity.RobotConnectedPoint.listCard.x.prefix=位置 x
i18n_entity.RobotConnectedPoint.listCard.y.prefix=位置 y
i18n_entity.RobotPropChangeTimeline.fields.createdBy.label=作成者
i18n_entity.RobotPropChangeTimeline.fields.createdOn.label=作成時間
i18n_entity.RobotPropChangeTimeline.fields.delta.label=变化量
i18n_entity.RobotPropChangeTimeline.fields.duration.label=経過時間（ミリ秒）
i18n_entity.RobotPropChangeTimeline.fields.finishedOn.label=終了時間
i18n_entity.RobotPropChangeTimeline.fields.id.label=ID
i18n_entity.RobotPropChangeTimeline.fields.modifiedBy.label=最終改訂人
i18n_entity.RobotPropChangeTimeline.fields.modifiedOn.label=最終変更時間
i18n_entity.RobotPropChangeTimeline.fields.newValue.label=新しい値
i18n_entity.RobotPropChangeTimeline.fields.oldValue.label=古い値
i18n_entity.RobotPropChangeTimeline.fields.robotName.label=ロボット
i18n_entity.RobotPropChangeTimeline.fields.startedOn.label=開始時間
i18n_entity.RobotPropChangeTimeline.fields.type.label=状態变化タイプ
i18n_entity.RobotPropChangeTimeline.fields.version.label=バージョン修正
i18n_entity.RobotPropChangeTimeline.group=Stats
i18n_entity.RobotPropChangeTimeline.label=ロボット状態変化シート
i18n_entity.RobustScriptExecutor.fields.args.label=パラメーター
i18n_entity.RobustScriptExecutor.fields.createdBy.label=作成者
i18n_entity.RobustScriptExecutor.fields.createdOn.label=作成時間
i18n_entity.RobustScriptExecutor.fields.description.label=説明
i18n_entity.RobustScriptExecutor.fields.fault.label=故障
i18n_entity.RobustScriptExecutor.fields.faultMsg.label=故障情報
i18n_entity.RobustScriptExecutor.fields.funcName.label=方法
i18n_entity.RobustScriptExecutor.fields.id.label=ID
i18n_entity.RobustScriptExecutor.fields.modifiedBy.label=最終改訂人
i18n_entity.RobustScriptExecutor.fields.modifiedOn.label=最終変更時間
i18n_entity.RobustScriptExecutor.fields.version.label=バージョン修正
i18n_entity.RobustScriptExecutor.group=Core
i18n_entity.RobustScriptExecutor.label=バックグラウンタスク
i18n_entity.RobustScriptExecutor.pagesButtons.ListMain.buttons[0].label=ロット編集
i18n_entity.RobustScriptExecutor.pagesButtons.ListMain.buttons[1].label=削除
i18n_entity.RobustScriptExecutor.pagesButtons.ListMain.buttons[2].label=回復
i18n_entity.ScriptRunOnce.fields.createdBy.label=作成者
i18n_entity.ScriptRunOnce.fields.createdOn.label=作成時間
i18n_entity.ScriptRunOnce.fields.id.label=ID
i18n_entity.ScriptRunOnce.fields.modifiedBy.label=最終改訂人
i18n_entity.ScriptRunOnce.fields.modifiedOn.label=変更時間
i18n_entity.ScriptRunOnce.fields.output.label=出力
i18n_entity.ScriptRunOnce.fields.version.label=バージョン修正
i18n_entity.ScriptRunOnce.group=Core
i18n_entity.ScriptRunOnce.label=一回脚本運行
i18n_entity.SimpleTransportOrder.fields.createdBy.label=作成者
i18n_entity.SimpleTransportOrder.fields.createdOn.label=作成時間
i18n_entity.SimpleTransportOrder.fields.currentMove.label=現在手順
i18n_entity.SimpleTransportOrder.fields.doneOn.label=終了時間
i18n_entity.SimpleTransportOrder.fields.errorMsg.label=エラー情報
i18n_entity.SimpleTransportOrder.fields.id.label=ID
i18n_entity.SimpleTransportOrder.fields.modifiedBy.label=最終改訂人
i18n_entity.SimpleTransportOrder.fields.modifiedOn.label=最終変更時間
i18n_entity.SimpleTransportOrder.fields.moves.label=動作リスト
i18n_entity.SimpleTransportOrder.fields.robotName.label=ロボット
i18n_entity.SimpleTransportOrder.fields.seer3066.label=指定パスナビゲーション
i18n_entity.SimpleTransportOrder.fields.status.inlineOptionBill.items.Cancelled.label=キャンセル済み
i18n_entity.SimpleTransportOrder.fields.status.inlineOptionBill.items.Created.label=作成済み
i18n_entity.SimpleTransportOrder.fields.status.inlineOptionBill.items.Done.label=完了済み
i18n_entity.SimpleTransportOrder.fields.status.inlineOptionBill.items.Failed.label=失敗
i18n_entity.SimpleTransportOrder.fields.status.label=状態
i18n_entity.SimpleTransportOrder.fields.vendor.label=ベンダー
i18n_entity.SimpleTransportOrder.fields.version.label=バージョン修正
i18n_entity.SimpleTransportOrder.group=GW
i18n_entity.SimpleTransportOrder.label=ロボット運送リスト
i18n_entity.SimpleTransportOrder.listCard.robotName.prefix=ロボット
i18n_entity.SimpleTransportOrder.listCard.vendor.prefix=ベンダー
i18n_entity.SimpleTransportOrder.listStats.items[0].label=実行中
i18n_entity.SimpleTransportOrder.listStats.items[1].label=失敗
i18n_entity.SocNode.fields.attention.label=注意
i18n_entity.SocNode.fields.createdBy.label=作成者
i18n_entity.SocNode.fields.createdOn.label=作成時間
i18n_entity.SocNode.fields.description.label=説明
i18n_entity.SocNode.fields.id.label=ID
i18n_entity.SocNode.fields.label.label=ラベル
i18n_entity.SocNode.fields.modifiedBy.label=最終改訂人
i18n_entity.SocNode.fields.modifiedOn.label=最終変更時間
i18n_entity.SocNode.fields.modifiedReason.label=更新原因
i18n_entity.SocNode.fields.modifiedTimestamp.label=更新時間
i18n_entity.SocNode.fields.value.label=値
i18n_entity.SocNode.fields.version.label=バージョン修正
i18n_entity.SocNode.group=Core
i18n_entity.SocNode.label=監視ノード
i18n_entity.SocNode.listCard.modifiedTimestamp.prefix=更新時間:
i18n_entity.StatsTimelineValueReport.fields.createdBy.label=作成者
i18n_entity.StatsTimelineValueReport.fields.createdOn.label=作成時間
i18n_entity.StatsTimelineValueReport.fields.finishedOn.label=終了時間
i18n_entity.StatsTimelineValueReport.fields.id.label=ID
i18n_entity.StatsTimelineValueReport.fields.modifiedBy.label=最終改訂人
i18n_entity.StatsTimelineValueReport.fields.modifiedOn.label=最終変更時間
i18n_entity.StatsTimelineValueReport.fields.period.label=周期
i18n_entity.StatsTimelineValueReport.fields.periodType.label=周期タイプ
i18n_entity.StatsTimelineValueReport.fields.startedOn.label=開始時間
i18n_entity.StatsTimelineValueReport.fields.subject.label=テーマ
i18n_entity.StatsTimelineValueReport.fields.target.label=キーワード
i18n_entity.StatsTimelineValueReport.fields.value.label=値
i18n_entity.StatsTimelineValueReport.fields.version.label=バージョン修正
i18n_entity.StatsTimelineValueReport.group=Stats
i18n_entity.StatsTimelineValueReport.label=時間順序数値レポート
i18n_entity.SystemKeyEvent.fields.content.label=内容
i18n_entity.SystemKeyEvent.fields.createdBy.label=作成者
i18n_entity.SystemKeyEvent.fields.createdOn.label=作成時間
i18n_entity.SystemKeyEvent.fields.group.label=モジュール
i18n_entity.SystemKeyEvent.fields.id.label=ID
i18n_entity.SystemKeyEvent.fields.level.inlineOptionBill.items.Error.label=エラー
i18n_entity.SystemKeyEvent.fields.level.inlineOptionBill.items.Info.label=普通
i18n_entity.SystemKeyEvent.fields.level.inlineOptionBill.items.Warning.label=警告
i18n_entity.SystemKeyEvent.fields.level.label=レベル
i18n_entity.SystemKeyEvent.fields.modifiedBy.label=最終改訂人
i18n_entity.SystemKeyEvent.fields.modifiedOn.label=最終変更時間
i18n_entity.SystemKeyEvent.fields.relatedUser.label=関連ユーザー
i18n_entity.SystemKeyEvent.fields.title.label=タイトル
i18n_entity.SystemKeyEvent.fields.version.label=バージョン修正
i18n_entity.SystemKeyEvent.group=Core
i18n_entity.SystemKeyEvent.label=システム重要イベント
i18n_entity.TransportOrder.fields.actualRobotName.label=実行ロボット
i18n_entity.TransportOrder.fields.containerId.label=容器番号
i18n_entity.TransportOrder.fields.createdBy.label=作成者
i18n_entity.TransportOrder.fields.createdOn.label=作成時間
i18n_entity.TransportOrder.fields.currentStepIndex.label=現在手順
i18n_entity.TransportOrder.fields.dispatchCost.label=配車コスト
i18n_entity.TransportOrder.fields.doneOn.label=完了時刻
i18n_entity.TransportOrder.fields.doneStepIndex.label=完了手順
i18n_entity.TransportOrder.fields.expectedRobotGroups.label=ロボットグループ指定
i18n_entity.TransportOrder.fields.expectedRobotNames.label=ロボット指定
i18n_entity.TransportOrder.fields.externalId.label=外部リスト番号
i18n_entity.TransportOrder.fields.fault.label=故障
i18n_entity.TransportOrder.fields.id.label=ID
i18n_entity.TransportOrder.fields.keyLocations.label=重要ポイント
i18n_entity.TransportOrder.fields.kind.inlineOptionBill.items.Business.label=業務
i18n_entity.TransportOrder.fields.kind.inlineOptionBill.items.Parking.label=停車
i18n_entity.TransportOrder.fields.kind.label=タイプ
i18n_entity.TransportOrder.fields.loadPoint.label=荷役位置
i18n_entity.TransportOrder.fields.loaded.label=荷役済み
i18n_entity.TransportOrder.fields.modifiedBy.label=最終改訂人
i18n_entity.TransportOrder.fields.modifiedOn.label=変更時間
i18n_entity.TransportOrder.fields.priority.label=優先級
i18n_entity.TransportOrder.fields.ro.label=RO
i18n_entity.TransportOrder.fields.robotAllocatedOn.label=ロボット割り当て時刻
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.Allocated.label=配車済み
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.Cancelled.label=キャンセル
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.Done.label=完了
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.Executing.label=実行中
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.Pending.label=実行待ち
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.ToBeAllocated.label=配車待ち
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.Withdrawn.label=取り消し
i18n_entity.TransportOrder.fields.status.label=状態
i18n_entity.TransportOrder.fields.stepFixed.label=手順固定
i18n_entity.TransportOrder.fields.stepNum.label=タスク手順
i18n_entity.TransportOrder.fields.taskBatch.label=タスクバッチ
i18n_entity.TransportOrder.fields.unloadPoint.label=荷卸し位置
i18n_entity.TransportOrder.fields.unloaded.label=荷卸し済み
i18n_entity.TransportOrder.fields.version.label=バージョン修正
i18n_entity.TransportOrder.group=Fleet
i18n_entity.TransportOrder.label=新規共通運送リスト
i18n_entity.TransportOrder.listCard.actualRobotName.prefix=ロボット実行
i18n_entity.TransportOrder.listCard.createdOn.prefix=作成
i18n_entity.TransportOrder.listCard.kind.prefix=タイプ
i18n_entity.TransportOrder.listStats.items[0].label=故障
i18n_entity.TransportOrder.listStats.items[1].label=配車待ち
i18n_entity.TransportOrder.listStats.items[2].label=配車済み
i18n_entity.TransportStep.fields.createdBy.label=作成者
i18n_entity.TransportStep.fields.createdOn.label=作成時間
i18n_entity.TransportStep.fields.endOn.label=実行終了時間
i18n_entity.TransportStep.fields.forLoad.label=荷役位置
i18n_entity.TransportStep.fields.forUnload.label=荷卸し位置
i18n_entity.TransportStep.fields.id.label=ID
i18n_entity.TransportStep.fields.location.label=作業位置
i18n_entity.TransportStep.fields.modifiedBy.label=最終改訂人
i18n_entity.TransportStep.fields.modifiedOn.label=変更時間
i18n_entity.TransportStep.fields.orderId.label=運送リスト番号
i18n_entity.TransportStep.fields.startOn.label=実行開始時間
i18n_entity.TransportStep.fields.status.label=状態
i18n_entity.TransportStep.fields.stepIndex.label=第　ステップ
i18n_entity.TransportStep.fields.version.label=バージョン修正
i18n_entity.TransportStep.group=Fleet
i18n_entity.TransportStep.label=新規共用運送リスト手順
i18n_entity.TransportStep.listCard.locationSite.prefix=ロケーション
i18n_entity.TransportStep.listCard.operation.prefix=動作
i18n_entity.TransportStep.listCard.stepIndex.prefix=第
i18n_entity.TransportStep.listCard.stepIndex.suffix=ステップ
i18n_entity.UserNotice.fields.actionType.label=動作タイプ
i18n_entity.UserNotice.fields.content.label=テキスト
i18n_entity.UserNotice.fields.createdBy.label=作成者
i18n_entity.UserNotice.fields.createdOn.label=作成時間
i18n_entity.UserNotice.fields.entityId.label=実体 ID
i18n_entity.UserNotice.fields.entityName.label=実体名
i18n_entity.UserNotice.fields.hasContent.label=テキストあり
i18n_entity.UserNotice.fields.id.label=ID
i18n_entity.UserNotice.fields.modifiedBy.label=最終改訂人
i18n_entity.UserNotice.fields.modifiedOn.label=最終変更時間
i18n_entity.UserNotice.fields.read.label=読取り済み
i18n_entity.UserNotice.fields.readOn.label=読取り時間
i18n_entity.UserNotice.fields.title.label=タイトル
i18n_entity.UserNotice.fields.userId.label=ユーザー
i18n_entity.UserNotice.fields.version.label=バージョン修正
i18n_entity.UserNotice.group=Core
i18n_entity.UserNotice.label=ユーザー通知
i18n_entity.UserOpLog.fields.content.label=操作内容
i18n_entity.UserOpLog.fields.createdBy.label=作成者
i18n_entity.UserOpLog.fields.createdOn.label=作成時間
i18n_entity.UserOpLog.fields.id.label=ID
i18n_entity.UserOpLog.fields.level.inlineOptionBill.items.Danger.label=危険
i18n_entity.UserOpLog.fields.level.inlineOptionBill.items.Normal.label=正常
i18n_entity.UserOpLog.fields.level.label=レベル
i18n_entity.UserOpLog.fields.modifiedBy.label=最終改訂人
i18n_entity.UserOpLog.fields.modifiedOn.label=最終変更時間
i18n_entity.UserOpLog.fields.operator.label=ユーザー
i18n_entity.UserOpLog.fields.page.label=ページ
i18n_entity.UserOpLog.fields.version.label=バージョン修正
i18n_entity.UserOpLog.group=Core
i18n_entity.UserOpLog.label=ユーザー操作ログ
i18n_entity.UserRole.fields.createdBy.label=作成者
i18n_entity.UserRole.fields.createdOn.label=作成時間
i18n_entity.UserRole.fields.defaultRole.label=デフォルト役割
i18n_entity.UserRole.fields.id.label=番号
i18n_entity.UserRole.fields.modifiedBy.label=最終改訂人
i18n_entity.UserRole.fields.modifiedOn.label=最終変更時間
i18n_entity.UserRole.fields.name.label=役割名
i18n_entity.UserRole.fields.pItems.label=権限リスト
i18n_entity.UserRole.fields.ro.label=元組織
i18n_entity.UserRole.fields.version.label=バージョン
i18n_entity.UserRole.group=User
i18n_entity.UserRole.label=ユーザー役割
i18n_entity.UserRole.listCard.createdBy.prefix=作成者
i18n_entity.UserRole.listCard.modifiedOn.prefix=変更時間
i18n_entity.WcsMrOrder.fields.actualRobotName.label=ロボット実行
i18n_entity.WcsMrOrder.fields.cancelling.label=キャンセル中
i18n_entity.WcsMrOrder.fields.containerId.label=容器番号
i18n_entity.WcsMrOrder.fields.createdBy.label=作成者
i18n_entity.WcsMrOrder.fields.createdOn.label=作成時間
i18n_entity.WcsMrOrder.fields.currentStepIndex.label=現在手順
i18n_entity.WcsMrOrder.fields.dispatchCost.label=配車コスト
i18n_entity.WcsMrOrder.fields.doneOn.label=完了時刻
i18n_entity.WcsMrOrder.fields.doneStepIndex.label=完了手順
i18n_entity.WcsMrOrder.fields.executing.label=実行中
i18n_entity.WcsMrOrder.fields.expectedRobotGroups.label=ロボットグループ指定
i18n_entity.WcsMrOrder.fields.expectedRobotNames.label=指定ロボット指定
i18n_entity.WcsMrOrder.fields.fault.label=故障
i18n_entity.WcsMrOrder.fields.id.label=ID
i18n_entity.WcsMrOrder.fields.keySites.label=重要ポイント
i18n_entity.WcsMrOrder.fields.kind.inlineOptionBill.items.Parking.label=停車
i18n_entity.WcsMrOrder.fields.kind.label=タイプ
i18n_entity.WcsMrOrder.fields.loadLocationSite.label=荷役位置
i18n_entity.WcsMrOrder.fields.loaded.label=荷役済み
i18n_entity.WcsMrOrder.fields.materialId.label=商品番号
i18n_entity.WcsMrOrder.fields.materialKind.label=商品タイプ
i18n_entity.WcsMrOrder.fields.modifiedBy.label=最終改訂人
i18n_entity.WcsMrOrder.fields.modifiedOn.label=変更時間
i18n_entity.WcsMrOrder.fields.priority.label=優先級
i18n_entity.WcsMrOrder.fields.reqId.label=リクエストリスト番号
i18n_entity.WcsMrOrder.fields.ro.label=RO
i18n_entity.WcsMrOrder.fields.robotAllocatedOn.label=ロボット配車時刻
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Allocated.label=配車済み
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Building.label=構築中
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Cancelled.label=キャンセル済み
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Cancelling.label=キャンセル中
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Done.label=完了済み
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Executing.label=実行中
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Pending.label=実行待ち
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.ToBeAllocated.label=配車待ち
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Withdrawn.label=取り消し済み
i18n_entity.WcsMrOrder.fields.status.label=状態
i18n_entity.WcsMrOrder.fields.stepFixed.label=手順固定
i18n_entity.WcsMrOrder.fields.stepNum.label=タスク手順
i18n_entity.WcsMrOrder.fields.taskBatch.label=タスクロット
i18n_entity.WcsMrOrder.fields.unloadLocationSite.label=荷卸し位置
i18n_entity.WcsMrOrder.fields.unloaded.label=荷卸し済み
i18n_entity.WcsMrOrder.fields.version.label=バージョン修正
i18n_entity.WcsMrOrder.fields.withdrawn.label=配車再実行
i18n_entity.WcsMrOrder.group=WCS
i18n_entity.WcsMrOrder.label=共通用運送リスト
i18n_entity.WcsMrOrder.listCard.actualRobotName.prefix=実行ロボット
i18n_entity.WcsMrOrder.listCard.createdOn.prefix=作成
i18n_entity.WcsMrOrder.listCard.kind.prefix=タイプ
i18n_entity.WcsMrOrder.listStats.items[0].label=故障
i18n_entity.WcsMrOrder.listStats.items[1].label=配車待ち
i18n_entity.WcsMrOrder.listStats.items[2].label=配車済み
i18n_entity.WcsMrOrderStep.fields.createdBy.label=作成者
i18n_entity.WcsMrOrderStep.fields.createdOn.label=作成時間
i18n_entity.WcsMrOrderStep.fields.endOn.label=実行終了時間
i18n_entity.WcsMrOrderStep.fields.forLoad.label=荷役位置
i18n_entity.WcsMrOrderStep.fields.forUnload.label=荷卸し位置
i18n_entity.WcsMrOrderStep.fields.id.label=ID
i18n_entity.WcsMrOrderStep.fields.locationSite.label=動作ロケーション位置
i18n_entity.WcsMrOrderStep.fields.modifiedBy.label=最終改訂人
i18n_entity.WcsMrOrderStep.fields.modifiedOn.label=変更時間
i18n_entity.WcsMrOrderStep.fields.operation.label=動作
i18n_entity.WcsMrOrderStep.fields.operationArgs.label=動作パラメーター
i18n_entity.WcsMrOrderStep.fields.orderId.label=運送リスト番号
i18n_entity.WcsMrOrderStep.fields.startOn.label=実行開始時間
i18n_entity.WcsMrOrderStep.fields.status.label=状態
i18n_entity.WcsMrOrderStep.fields.stepIndex.label=第　ステップ
i18n_entity.WcsMrOrderStep.fields.version.label=バージョン修正
i18n_entity.WcsMrOrderStep.group=WCS
i18n_entity.WcsMrOrderStep.label=共用運送リスト手順
i18n_entity.WcsMrOrderStep.listCard.locationSite.prefix=ロケーション位置
i18n_entity.WcsMrOrderStep.listCard.operation.prefix=動作
i18n_entity.WcsMrOrderStep.listCard.stepIndex.prefix=第
i18n_entity.WcsMrOrderStep.listCard.stepIndex.suffix=ステップ
i18n_entity.WcsRobotTaskLog.fields.args.label=パラメーター
i18n_entity.WcsRobotTaskLog.fields.category.label=区分
i18n_entity.WcsRobotTaskLog.fields.code.label=コード
i18n_entity.WcsRobotTaskLog.fields.createdBy.label=作成者
i18n_entity.WcsRobotTaskLog.fields.createdOn.label=作成時間
i18n_entity.WcsRobotTaskLog.fields.id.label=ID
i18n_entity.WcsRobotTaskLog.fields.level.label=レベル
i18n_entity.WcsRobotTaskLog.fields.modifiedBy.label=最終改訂人
i18n_entity.WcsRobotTaskLog.fields.modifiedOn.label=変更時間
i18n_entity.WcsRobotTaskLog.fields.robotName.label=ロボット
i18n_entity.WcsRobotTaskLog.fields.taskId.label=タスク番号
i18n_entity.WcsRobotTaskLog.fields.tcId.label=追跡番号
i18n_entity.WcsRobotTaskLog.fields.version.label=バージョン修正
i18n_entity.WcsRobotTaskLog.group=WCS
i18n_entity.WcsRobotTaskLog.label=ロボットタスクログ
i18n_entity.WcsRobotTaskLog.listCard.category.prefix=区分
i18n_entity.WcsRobotTaskLog.listCard.code.prefix=コード
i18n_entity.WcsRobotTaskLog.listCard.level.prefix=レベル
i18n_entity.WcsRobotTaskLog.listCard.taskId.prefix=タスク番号