<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="INFO">
  <Appenders>
    <!-- 控制台日志配置 -->
    <Console name="Console" target="SYSTEM_OUT">
      <PatternLayout>
        <!-- 控制台日志只打时间，不打日期 -->
        <Pattern>%d{HH:mm:ss.SSS} %highlight{${LOG_LEVEL_PATTERN:-%5p}}{FATAL=red, ERROR=red, WARN=yellow,
          INFO=green, DEBUG=green, TRACE=green} --- [%15.15t] %logger{1} - %m%n%ex
        </Pattern>
        <!-- <charset>UTF-8</charset> --> <!-- 加上会导致乱码 -->
      </PatternLayout>
    </Console>

    <!-- 系统监控日志。不压缩。 -->
    <RollingFile name="SysMon" fileName="${sys:logPath}/logs/sys-mon.log"
                 filePattern="${sys:logPath}/logs/sys-mon-%d{yyyy-MM-dd-HH}-%i.log">
      <PatternLayout>
        <Pattern>%msg%n</Pattern>
        <charset>UTF-8</charset>
      </PatternLayout>
      <Policies>
        <TimeBasedTriggeringPolicy modulate="true"/>
        <SizeBasedTriggeringPolicy size="100MB"/>
      </Policies>
      <!-- fileIndex 不限制每天内日志文件数量 -->
      <DefaultRolloverStrategy fileIndex="nomax">
        <Delete basePath="${sys:logPath}/logs" maxDepth="2">
          <IfFileName glob="sys-mon-*.log"/>
          <!-- 最多保留 3 天日志 -->
          <IfLastModified age="P3D"/>
        </Delete>
      </DefaultRolloverStrategy>
    </RollingFile>

    <!-- Distributed 交管日志 -->
    <RollingFile name="TrafficFile" fileName="${sys:logPath}/logs/traffic-info.log"
                 filePattern="${sys:logPath}/logs/traffic-info-%d{yyyy-MM-dd-HH}-%i.log.zip">
      <PatternLayout>
        <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36}:%L - %msg%n</Pattern>
        <charset>UTF-8</charset>
      </PatternLayout>
      <Policies>
        <TimeBasedTriggeringPolicy modulate="true"/>
        <SizeBasedTriggeringPolicy size="128MB"/>
      </Policies>
      <DefaultRolloverStrategy fileIndex="nomax">
        <Delete basePath="${sys:logPath}/logs" maxDepth="2">
          <IfFileName glob="traffic-info-*.log.zip"/>
          <IfLastModified age="P7D"/>
        </Delete>
      </DefaultRolloverStrategy>
    </RollingFile>

    <!-- Distributed 预防死锁日志 -->
    <RollingFile name="TrafficPreventFile" fileName="${sys:logPath}/logs/prevent-info.log"
                 filePattern="${sys:logPath}/logs/prevent-info-%d{yyyy-MM-dd-HH}-%i.log.zip">
      <PatternLayout>
        <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36}:%L - %msg%n</Pattern>
        <charset>UTF-8</charset>
      </PatternLayout>
      <Policies>
        <TimeBasedTriggeringPolicy modulate="true"/>
        <SizeBasedTriggeringPolicy size="128MB"/>
      </Policies>
      <DefaultRolloverStrategy fileIndex="nomax">
        <Delete basePath="${sys:logPath}/logs" maxDepth="2">
          <IfFileName glob="prevent-info-*.log.zip"/>
          <IfLastModified age="P7D"/>
        </Delete>
      </DefaultRolloverStrategy>
    </RollingFile>

    <!-- RbkLog -->
    <RollingFile name="RbkLogFile" fileName="${sys:logPath}/logs/rbk.log"
                 filePattern="${sys:logPath}/logs/rbk-%d{yyyy-MM-dd-HH}-%i.log.zip">
      <PatternLayout>
        <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n</Pattern>
        <charset>UTF-8</charset>
      </PatternLayout>
      <Policies>
        <TimeBasedTriggeringPolicy modulate="true"/>
        <SizeBasedTriggeringPolicy size="100MB"/>
      </Policies>
      <DefaultRolloverStrategy fileIndex="nomax">
        <Delete basePath="${sys:logPath}/logs" maxDepth="2">
          <IfFileName glob="rbk-*.log.zip"/>
          <!-- 保留 5 天 -->
          <IfLastModified age="P5D"/>
        </Delete>
      </DefaultRolloverStrategy>
    </RollingFile>

    <!-- 其他日志文件 -->
    <RollingFile name="RollingFile" fileName="${sys:logPath}/logs/f2.log"
                 filePattern="${sys:logPath}/logs/f2-%d{yyyy-MM-dd-HH}-%i.log.zip">
      <PatternLayout>
        <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n</Pattern>
        <charset>UTF-8</charset>
      </PatternLayout>
      <Policies>
        <TimeBasedTriggeringPolicy modulate="true"/>
        <SizeBasedTriggeringPolicy size="100MB"/>
      </Policies>
      <DefaultRolloverStrategy fileIndex="nomax">
        <Delete basePath="${sys:logPath}/logs" maxDepth="2">
          <IfFileName glob="f2-*.log.zip"/>
          <!-- 保留 30 天 -->
          <IfLastModified age="P30D"/>
        </Delete>
      </DefaultRolloverStrategy>
    </RollingFile>
  </Appenders>

  <Loggers>
    <Root level="warn">
      <AppenderRef ref="Console"/>
      <AppenderRef ref="RollingFile"/>
    </Root>
    <Logger name="com.seer" level="debug" additivity="false">
      <AppenderRef ref="Console"/>
      <AppenderRef ref="RollingFile"/>
    </Logger>
    <!-- 调度的日志 -->
    <Logger name="Fleet" level="debug" additivity="false">
      <AppenderRef ref="Console"/>
      <AppenderRef ref="RollingFile"/>
    </Logger>
    <Logger name="SysMon" level="debug" additivity="false">
      <AppenderRef ref="Console"/>
      <AppenderRef ref="SysMon"/>
    </Logger>
    <Logger name="com.seer.trick.fleet.traffic.distributed" level="debug" additivity="false">
      <AppenderRef ref="TrafficFile"/>
    </Logger>
    <Logger name="com.seer.trick.fleet.traffic.distributed.prevent" level="debug" additivity="false">
      <AppenderRef ref="TrafficPreventFile"/>
    </Logger>
    <Logger name="RbkLog" level="debug" additivity="false">
      <AppenderRef ref="RbkLogFile"/>
    </Logger>
  </Loggers>

</Configuration>