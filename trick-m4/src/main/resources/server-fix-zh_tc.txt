errAdminRequired=需要管理員權限
errBadColumnName=列名無效 "{0}"
errBadHttpMethod=不支持的 Http 方法：{0}
errBadIP=IP 格式不正確："{0}"
errBadReqBodyJson=JSON 格式錯誤：{0}
errBinNoContainer=庫位 "{0}" 上當前沒有容器
errBinNoRobot=庫位 "{0}" 未配置機器人位置參數
errBpNoChild=組件未配置子組件 "{0}"
errBpRunError=組件 "{1}" 運行報錯：{0}
errBzError=業務異常：{0}
errBzMaterialCategoryNoStoreDistricts=物料分類 "{0}" 未配置存儲庫區
errBzMaterialNoCategory=物料 "{0}" 未配置分類
errBzMissingKeyParam=缺少必填參數："{0}"
errBzNoActiveBomForMat=找不到物料 "{0}" 已啓用的物料清單
errBzNoEnoughContainerForMaterial=裝物料 "{0}" 的空箱不足
errBzNoEnoughEmptyContainer=空容器不足（期望
errBzNoMaterialById=找不到物料 "{0}"
errBzNoMaterialCategoryById=找不到物料分類 "{0}"
errBzNoMaterialContainerMaxQty=物料 "{0}" 未配置容器容量
errBzNoMaterialContainerMaxQty2=物料 "{0}" 未配置“有效的”容器容量
errBzNoSuchOrderNameId=找不到的單據 "{0}" / "{1}"
errBzOutboundLineShort=第 {0} 行，物料 "{2}"，庫存缺 {1} 個
errCodeErr=程序錯誤：{0}
errColumnStartGtColumnEnd=起始列號不能大於結束列號
errComplexQueryBadOp=不支持的運算符 "{0}"
errComplexQueryInMultiple=查詢需要指定多個值
errComplexQueryMissingField1=查詢需要指明"字段1"
errComplexQueryMissingOp=查詢需要運算符
errComplexQueryUnknownType=不支持的查詢類型 "{0}"
errComplexQueryValueNeedTwo=字段 "{0}" 需要兩個查詢值
errComplexQueryValueNotString=字段 "{0}" 查詢值不是字符串
errComplexQueryValueNull=字段 "{0}" 查詢值爲空
errContainerLocked=容器 "{0}" 可能正在被使用，請嘗試其他容器（鎖定
errContainerNoBin=容器 "{0}" 當前庫位爲空
errContainerNoType=容器 "{0}" 未設置類型
errContainerTypeNoStoreDistricts=容器類型 "{0}" 未配置存儲區
errDbProcessing=正在處理數據庫，請稍後再試
errDbRestoreNoInfo=數據文件不正確（缺 info.txt）
errDepthStartGtDepthEnd=起始深度不能大於結束深度
errDirFilesNull=列出目錄下的文件失敗
errDirNotExists=目錄不存在：{0}
errDirectOutboundSubmitFail=直接出庫提交失敗。原因：{0}
errDirectorOutboundBinNotOccupied=庫位 {0} 是空的。（直接出庫）
errDirectorOutboundEmptyLayouts=可出庫的庫存明細爲空。（直接出庫）
errDirectorOutboundNoOrder=直接出庫未配置承接單據
errDistrictIdsEmpty=需要指定至少一個庫區
errDuplicatedKeyError="{0}" 的 "{1}" 字段值不能重複，重複值
errEmptyEntityValue=業務對象 "{0}" 值爲空
errEmptyPassword=密碼不能爲空
errEntityNoField=業務對象 "{0}" 沒有字段 "{1}"
errEntityRequestMissingIdOrQueryParam=需要指定 "id" 或 "query" 參數
errEntityRequestMissingQueryParam=查詢需要指定 "query" 參數
errFalconBlockInputParamNotList=組件 "{0}" 輸入參數 "{1}" 需要是數組
errFalconBlockInputParamRangeError=組件 "{0}" 輸入參數 "{1}" 範圍錯誤
errFalconBlockOptionParamError=組件 "{0}" 選項參數 "{1}" 錯誤
errFalconCreateTaskNoDefId=無法創建任務，未指定模板
errFalconExpressionError=表達式求值錯誤。表達式 "{0}"。詳情：{1}
errFalconMissingBlockInputParam=組件 "{0}" 缺少輸入參數 "{1}"
errFalconThrowPrefix=錯誤：{0}
errFieldTextTooLong=字段 "{0}.{1}" 的內容長度 {2} 超過最大限制 {3}
errFileNotDirectory=文件不是目錄：{0}
errFileNotExists=文件不存在：{0}
errFileNotInDir=文件 "{0}" 不在目錄 "{1}" 中
errGwNoRobot=網關中未配置機器人 "{0}"
errHttpFail=HTTP 請求失敗，HTTP 響應碼
errHttpResponseBodyEmpty=HTTP 請求響應爲空{0}
errInitializeScheduler=定時任務初始化失敗！"{0}"
errInterruptedException=執行被中斷
errInvLayoutNoContainer=庫存明細上沒有容器 {0}
errInvShort=庫存不足：{0}
errInvShort2=庫存不足，物料 {0}，待出庫
errLayerStartGtLayerEnd=起始層號不能大於結束層號
errLockBinFailed=鎖定庫位 "{0}" 失敗，之前已被鎖定
errMissingHttpPathParam=缺少路徑參數：{0}
errMissingHttpQueryParam=缺少 HTTP 查詢參數：
errMissingIdField=業務對象 {1} ({0}) 缺少 id 字段
errMissingParam=缺少參數：{0}
errMrNoCurrentOrder=機器 "{0}" 當前沒有運單
errMrNoCurrentStep=機器人 "{0}" 當前沒有正在執行的運單步驟
errMrNoOrder=運單不存在或已執行完成 "{0}"
errMrUpdateOrderBadStatus=運單已不允許更新。運單
errNoBin=未找到庫位 "{0}"
errNoBinById=找不到庫位 "{0}"
errNoBinRobotArgs=缺動作參數，庫位
errNoBpType=找不到組件 "{0}"
errNoConfig=未指定配置
errNoFalconTaskDefById=找不到任務模板，模板ID
errNoMongoDB=沒有可用的 MongoDB
errNoOrderLine=無單行，單號
errNoRoutePath=找不到可達路徑，從 {0} 到 {1}
errNoScriptFunctionProvided=未指定腳本函數
errNoSqlDb=SQL 數據源不可用
errNoSuchContainerById=找不到容器 "{0}"
errNoSuchContainerTypeById=找不到容器類型 "{0}"
errNoSuchDistrictById=找不到庫區 "{0}"
errNoSuchEntity=找不到業務對象 "{0}"
errNoSuchScriptFunction=找不到腳本函數 "{0}"
errNoSuchUserById=找不到用戶。id
errNoTaskDefByLabel=找不到任務模板，模板名
errNoUiDir=找不到界面文件目錄：{0}
errNoUpOrderConfig=找不到上游單據配置。上游單據
errNoUploadedFile=上傳的文件要放在表單字段中："{0}"
errNoUserUsername=找不到用戶。用戶名
errNoWidthTooSmall=編號寬度過小
errOrderNoPushConfig=找不到單據下推配置：{0}
errPasswordNotMatch=密碼錯誤
errRecoverBadExternalCallAsync=只能重試取消或終止的異步調用
errRefFieldNoRefEntity=字段 "{0}" 未配置 "引用業務對象"
errRetryFailedRobotButOrderNotExecuting=重啓故障機器人，但當前運單 {0} 狀態不是執行中，而是 {1}
errRobotNotFailed=機器人 "{0}" 並未故障
errRowStartGtRowEnd=起始排號不能大於結束排號
errScriptEntityRw=腳本業務對象讀寫報錯：{0}
errScriptExt=腳本報錯：{0}
errScriptReturnNotString=腳本函數 "{0}" 返回值不是字符串：{1}
errSetToStatementInvalidJavaClass=setToStatement 不支持的參數類型 "{0}"
errSignInNoPassword=需要密碼
errSimpleScriptBp=執行簡易腳本失敗：{0}
errSingletonEntityBadOp=單例業務對象 "{0}" 不能調用 "{1}"
errSqlEmptyWhereNotAllowed=禁止全部更新
errSqlUniqueConstraintViolation=值重複了。值
errSyncKeyMultiple=多值字段 "{0}" 不能作爲業務主鍵
errSyncKeyNotUniqInCurrent=在原業務對象列表中，鍵並不唯一："{0}"
errSyncKeyNotUniqInNew=在新業務對象列表中，鍵並不唯一："{0}"
errTakeOffContainerBadBin=要求從庫位 "{1}" 下架容器 "{0}"，但系統記錄容器目前在庫位 "{2}"
errTakeOffContainerBadContainer=要從庫位 "{0}" 下架容器 "{1}"，但系統記錄庫位上的容器是 {2}
errTakeOffContainerContainerBinAtLeastOne=庫口容器下架，容器或庫位至少指定一個
errTakeOffContainerNoBin=要求從庫位 "{1}" 下架容器 "{0}"，但系統記錄容器目前不在任何庫位上
errTypesNotEnoughForHeaders=類型數不足
errUnbindBinContainerBadBind=嘗試解綁庫位 "{0}" 和容器 "{1}"，但庫位上現在的容器是 "{2}"
errUnsupportedHttpRequestMethod=HTTP 方法錯誤 "{0}"
errUserDisabled=帳戶已被禁用
errWsNotConnected=未連接 [Websocket {0}，狀態
errWsSendFail=發送失敗 [Websocket {0}]
errWsSendTimeout=發送超時 [Websocket {0}]
errWwxBadConfig=企業微信登錄配置不全
errWwxCallErr=企業微信請求失敗，代碼
errWwxNoCode=回調無 code
errWwxNotEnabled=未啓用企業微信
errXlsFormulaNotSupported=不支持單元格里有公式
Falcon_BlockGroup_Basic=基礎組件
Falcon_BlockGroup_Bin=庫位
Falcon_BlockGroup_BinContainer=容器庫位
Falcon_BlockGroup_Container=容器
Falcon_BlockGroup_ContainerTransport=容器搬運單
Falcon_BlockGroup_Cq=複雜查詢
Falcon_BlockGroup_CustomBlocks=定製組件
Falcon_BlockGroup_Entity=業務對象
Falcon_BlockGroup_Inv=庫存
Falcon_Bp_AbortTaskBp_Input_msg_label=錯誤信息
Falcon_Bp_AbortTaskBp_label=終止任務
Falcon_Bp_BindBinContainerBp_label=綁定庫位容器
Falcon_Bp_CreateInvFromOrderBp_label=從單據創建庫存
Falcon_Bp_CreateInvFromOrderLinesBp_Input_bin_label=庫位
Falcon_Bp_CreateInvFromOrderLinesBp_Input_container_label=容器
Falcon_Bp_CreateInvFromOrderLinesBp_Input_copyFields_label=從單行上複製以下字段
Falcon_Bp_CreateInvFromOrderLinesBp_Input_materialFieldName_label=單行上的物料字段名
Falcon_Bp_CreateInvFromOrderLinesBp_Input_order_label=單據對象
Falcon_Bp_CreateInvFromOrderLinesBp_Input_qtyFieldName_label=單行上的數量字段名
Falcon_Bp_CreateInvFromOrderLinesBp_Input_state_label=存儲狀態
Falcon_Bp_CreateInvFromOrderLinesBp_label=從單據單行創建庫存
Falcon_Bp_CreateTraceContainerBp_label=創建追蹤容器
Falcon_Bp_DelayBp_label=延遲
Falcon_Bp_ExpressionBp_Input_expression_label=表達式
Falcon_Bp_ExpressionBp_label=表達式求值
Falcon_Bp_FindEmptyContainerBp_label=找空容器
Falcon_Bp_FindFieldValueByIdBp_Input_entityName_label=業務對象名
Falcon_Bp_FindFieldValueByIdBp_Input_id_label=業務對象 id
Falcon_Bp_FindFieldValueByIdBp_Output_found_label=業務對象存在
Falcon_Bp_FindFieldValueByIdBp_Output_value_label=值
Falcon_Bp_FindFieldValueByIdBp_label=根據 id 查找業務對象的字段
Falcon_Bp_FindNotOccupiedBinBp_Input_districtIds_label=庫區列表
Falcon_Bp_FindNotOccupiedBinBp_Input_keepTrying_label=反覆嘗試至成功
Falcon_Bp_FindNotOccupiedBinBp_Output_binId_label=庫位 ID
Falcon_Bp_FindNotOccupiedBinBp_label=尋空庫位
Falcon_Bp_FindOneBp_Input_entityName_label=實體
Falcon_Bp_FindOneBp_Input_field_label=字段
Falcon_Bp_FindOneBp_Input_value_label=價值
Falcon_Bp_FindOneBp_label=查單條記錄
Falcon_Bp_FindOneEntityByIdBp_Input_entityName_label=業務對象名
Falcon_Bp_FindOneEntityByIdBp_Input_id_label=業務對象 id
Falcon_Bp_FindOneEntityByIdBp_label=根據 id 查找業務對象
Falcon_Bp_GetBinContainerBp_label=獲取庫位上的容器
Falcon_Bp_GetContainerBinBp_label=獲取容器所在庫位
Falcon_Bp_GetContainerInvBp_label=獲取容器內庫存明細
Falcon_Bp_GetContainerTypeStoreDistrictsBp_label=查詢容器的類型的存儲區
Falcon_Bp_IfBp_label=如果
Falcon_Bp_IfElseBp_label=如果還有
Falcon_Bp_IterateListBp_label=遍歷數組
Falcon_Bp_KeepTryingLockBinBp_Input_binId_label=庫位
Falcon_Bp_KeepTryingLockBinBp_Input_reason_label=原因
Falcon_Bp_KeepTryingLockBinBp_label=反覆嘗試鎖定庫位直到成功
Falcon_Bp_KeepTryingLockNotOccupiedBinBp_label=等待庫位爲空並鎖定
Falcon_Bp_LockBinOnceBp_Input_binId_label=庫位編號
Falcon_Bp_LockBinOnceBp_Input_notFoundToAborted_label=找不到終止任務
Falcon_Bp_LockBinOnceBp_Input_notFoundToFault_label=找不到讓任務故障
Falcon_Bp_LockBinOnceBp_label=鎖定未鎖定的庫位
Falcon_Bp_MarkContainerTransportOrderDoneBp_label=標記容器搬運單完成
Falcon_Bp_MoveInvByContainerBp_label=按容器移動庫存
Falcon_Bp_PadIntStrBp_Input_numLength_label=數字長度
Falcon_Bp_PadIntStrBp_Input_num_label=數字
Falcon_Bp_PadIntStrBp_Input_prefix_label=前綴
Falcon_Bp_PadIntStrBp_Input_suffix_label=後綴
Falcon_Bp_PadIntStrBp_Output_numStr_label=數字轉換後的字符串
Falcon_Bp_PadIntStrBp_label=將數字轉換爲指定長度的字符串
Falcon_Bp_ParallelFlowBp_label=並行執行
Falcon_Bp_PrintBp_Input_message_label=消息
Falcon_Bp_PrintBp_label=打印
Falcon_Bp_ReduceBinInvFromOrderBp_label=按單據從庫位刪除庫存
Falcon_Bp_RemoveInvByContainerBp_label=按容器刪除庫存明細
Falcon_Bp_RemoveTraceContainerBp_label=刪除追蹤容器
Falcon_Bp_RepeatNumBp_Child_default_label=組件
Falcon_Bp_RepeatNumBp_Context_index_label=序號
Falcon_Bp_RepeatNumBp_Input_num_label=執行次數
Falcon_Bp_RepeatNumBp_label=重複執行
Falcon_Bp_SerialFlowBp_label=串行執行
Falcon_Bp_SetBinEmptyBp_label=設置庫位未佔用（棄用）
Falcon_Bp_SetBinNotOccupiedBp_label=設置庫位未佔用
Falcon_Bp_SetTaskVariableBp_label=設置任務變量
Falcon_Bp_SimpleScriptBp_Input_scriptStr_label=腳本源碼
Falcon_Bp_SimpleScriptBp_Output_scriptOut_label=返回值
Falcon_Bp_SimpleScriptBp_label=簡易腳本
Falcon_Bp_ThrowBp_Input_condition_label=條件
Falcon_Bp_ThrowBp_Input_errMsg_label=異常信息
Falcon_Bp_ThrowBp_label=拋出異常
Falcon_Bp_TimestampBp_Input_formatDate_label=日期格式
Falcon_Bp_TimestampBp_Output_timestamp_label=當前時間戳
Falcon_Bp_TimestampBp_label=返回當前時間
Falcon_Bp_TriggerTaskEventBp_label=觸發任務事件
Falcon_Bp_TryCatchBp_Input_ignoreAbort_label=忽略取消異常
Falcon_Bp_TryCatchBp_Input_swallowError_label=吞掉異常不拋出
Falcon_Bp_TryCatchBp_label=試捕
Falcon_Bp_UnbindBinContainerBp_label=解綁庫位容器
Falcon_Bp_UpdateContainerTransportOrderBp_label=更新容器搬運單
Falcon_Bp_UpdateEntityFieldBp_label=根據條件更新業務對象單個字段
Falcon_Bp_UpdateOneEntityByIdBp_label=根據 id 更新業務對象單個字段
Falcon_Bp_WebSocketBp_Input_eventName_label=發送事件
Falcon_Bp_WebSocketBp_Input_message_label=發送內容
Falcon_Bp_WebSocketBp_label=WebSocket 發送信息
ModbusDeviceNotInit=Modbus 設備 "{0}" 未初始化
wcs_err_TomNoUrl=調度 "{0}" 未配置 URL
wcs_err_TomNotFound=未配置調度 "{0}"
wcs_err_tom_BlockError=調度說塊執行失敗，單號
wcs_err_tom_BlockNotFound=調度說塊找不到，單號
wcs_err_tom_ConnectError=連接調度服務器失敗：{0}
wcs_err_tom_HttpError=請求調度接口其他報錯：[{0}] {1}
wcs_err_tom_HttpError404=請求調度接口報 404
wcs_err_tom_OtherError=請求調度，其他錯誤：{0}
wcs_err_tom_TomError=調度報錯，錯誤碼
wcs_err_tom_TomResponseEmpty=請求調度響應正文爲空
errNoDevice=無此設備：{0}
Falcon_BlockGroup_PLC=PLC
Falcon_Bp_ModbusReadBp_Input_address_label=地址
Falcon_Bp_ModbusReadBp_Input_code_label=功能碼
Falcon_Bp_ModbusReadBp_Input_deviceName_label=設備名
Falcon_Bp_ModbusReadBp_Input_slaveId_label=從屬ID
Falcon_Bp_ModbusReadBp_label=Modbus 讀取
Falcon_Bp_ModbusReadEqBp_Input_address_label=地址
Falcon_Bp_ModbusReadEqBp_Input_code_label=功能碼
Falcon_Bp_ModbusReadEqBp_Input_deviceName_label=設備名
Falcon_Bp_ModbusReadEqBp_Input_readDelay_label=讀取間隔
Falcon_Bp_ModbusReadEqBp_Input_slaveId_label=從屬ID
Falcon_Bp_ModbusReadEqBp_Input_targetValue_label=目標值
Falcon_Bp_ModbusReadEqBp_label=Modbus 讀取直到等於
Falcon_Bp_ModbusWriteBp_Input_address_label=地址
Falcon_Bp_ModbusWriteBp_Input_code_label=功能碼
Falcon_Bp_ModbusWriteBp_Input_deviceName_label=設備名
Falcon_Bp_ModbusWriteBp_Input_slaveId_label=從屬ID
Falcon_Bp_ModbusWriteBp_Input_value_label=值
Falcon_Bp_ModbusWriteBp_label=Modbus 寫
RbkApiNoNoSupported=不支持 API 編號 {0}
RbkClientConnectFail=連接 RBK 失敗 {0}
RbkClientRequestFail=請求 RBK 失敗 {0}
RobotNotExistedById=機器人 "{0}" 不存在
Falcon_BlockGroup_DirectOrder=直接運單
Falcon_BlockGroup_Map=地圖
Falcon_BlockGroup_Ndc=NDC 調度運單
Falcon_BlockGroup_SeerTom=仙工調度運單
Falcon_Bp_AddTomBlockBp_Input_binTask_label=binTask
Falcon_Bp_AddTomBlockBp_Input_goodsId_label=貨物 ID
Falcon_Bp_AddTomBlockBp_Input_location_label=站點
Falcon_Bp_AddTomBlockBp_Input_operation_label=動作（operation）
Falcon_Bp_AddTomBlockBp_Input_orderId_label=運單號
Falcon_Bp_AddTomBlockBp_Input_tomId_label=調度 ID
Falcon_Bp_AddTomBlockBp_Output_blockId_label=塊 ID
Falcon_Bp_AddTomBlockBp_label=執行運單塊
Falcon_Bp_AllowNdcLoadBp_Input_orderId_label=運單號
Falcon_Bp_AllowNdcLoadBp_label=允許 NDC 裝貨
Falcon_Bp_AllowNdcUnloadBp_Input_orderId_label=運單號
Falcon_Bp_AllowNdcUnloadBp_label=允許 NDC 卸貨
Falcon_Bp_CompleteTomOrderBp_Input_orderId_label=運單號
Falcon_Bp_CompleteTomOrderBp_Input_tomId_label=調度 ID
Falcon_Bp_CompleteTomOrderBp_label=結束調度運單
Falcon_Bp_CreateNdcOrderBp_Input_endBin_label=終點
Falcon_Bp_CreateNdcOrderBp_Input_priority_label=優先級
Falcon_Bp_CreateNdcOrderBp_Input_startBin_label=起點
Falcon_Bp_CreateNdcOrderBp_Output_orderId_label=運單號
Falcon_Bp_CreateNdcOrderBp_label=創建 NDC 運單
Falcon_Bp_CreateTomOrderBp_Input_group_label=指定機器人組
Falcon_Bp_CreateTomOrderBp_Input_keyRouteStr_label=關鍵位置
Falcon_Bp_CreateTomOrderBp_Input_notMarkComplete_label=取消任務運單不封口
Falcon_Bp_CreateTomOrderBp_Input_priority_label=優先級
Falcon_Bp_CreateTomOrderBp_Input_tomId_label=調度 ID
Falcon_Bp_CreateTomOrderBp_Input_vehicle_label=指定機器人
Falcon_Bp_CreateTomOrderBp_Output_actualVehicle_label=分配的機器人
Falcon_Bp_CreateTomOrderBp_Output_orderId_label=運單號
Falcon_Bp_CreateTomOrderBp_label=創建調度運單
Falcon_Bp_DirectOrderExecuteBp_label=直接運單執行
Falcon_Bp_DirectOrderMoveBp_label=直接運單步驟
Falcon_Bp_MapPointBinBp_Input_pointId_label=站點
Falcon_Bp_MapPointBinBp_Output_binId_label=庫位
Falcon_Bp_MapPointBinBp_Output_found_label=站點配了庫位
Falcon_Bp_MapPointBinBp_label=站點的庫位
Falcon_Bp_WaitUntilNdcArriveEndBinBp_Input_orderId_label=運單號
Falcon_Bp_WaitUntilNdcArriveEndBinBp_label=等待 NDC 到達終點
Falcon_Bp_WaitUntilNdcArriveStartBinBp_Input_orderId_label=運單號
Falcon_Bp_WaitUntilNdcArriveStartBinBp_label=等待 NDC 到達起點
Falcon_Bp_WaitUntilNdcFinishBp_Input_orderId_label=運單號
Falcon_Bp_WaitUntilNdcFinishBp_label=等待 NDC 任務完成
Falcon_Bp_WaitUntilNdcLoadedBp_Input_orderId_label=運單號
Falcon_Bp_WaitUntilNdcLoadedBp_label=等待 NDC 裝貨完成
Falcon_Bp_WaitUntilNdcUnloadedBp_Input_orderId_label=運單號
Falcon_Bp_WaitUntilNdcUnloadedBp_label=等待 NDC 卸貨完成
relocStatus_0=定位失敗
relocStatus_1=定位正確
relocStatus_2=正在重定位
relocStatus_3=定位完成
relocStatus_null=定位狀態未知
errAwaitMove=等待導航完成，但導航失敗，具體原因：{0}
errCreateStoOrderExists=運單已存在
errDirectRobotOrderFailed=直接運單失敗，單號
errFailedToFetchMapFromRobot=從機器人 {0} 獲取地圖失敗
errFetchCurrentMapFail=獲取機器人當前地圖失敗，機器人
errFetchMapFail=讀取機器人地圖失敗，機器人
errGwNoLocalRobot=網關沒有機器人 {0}
errMove3051=發送 3051 路徑導航失敗：{0}
errMove3066=發送 3066 路徑導航失敗：{0}
errMoveRobotFromToStation=機器人 {0} 當前在 {1}，請現將其移回路徑起點 {2} 或終點 {3} 再重試
errNoDefaultMap=找不到默認地圖（第一個區域第一個車型）
errNoReportApplyUrl=沒有指定上報地址
errNoRobot=無此機器人 "{0}"
errNoSuchBinOrLocation=找不到名爲 {0} 站點或庫位
errNotRobotInTom=調度 {0} 裡沒有機器人 {1}
errPositionToNoSite=位置（{0},{1}）附近沒有站點
errQuery3066BadTaskId=查詢 3066 結果，任務 ID 不匹配，期望 {0}，實際 {1}
errRequestControl=請求控制權失敗，機器人
errRetryButOrderNotFailed=重試，但訂單狀態不是失敗
errRobotAppNoSceneByName=無此場景：{0}
errRobotAppNoSingleScene=場景 {0} 不是單機應用
errRobotAppSceneDisabled=場景 {0} 已停用
errRobotHasSimpleOrderCurrent=機器人 {0} 還在執行運單 {1}，不能接收新單
errRobotMoveNoStart=找不到機器人 {0} 執行任務合適的起始點
errRobotNavNoId=路徑導航請求錯誤，需要終點參數 'id'
errRobotNavNoSourceId=路徑導航請求錯誤，需要起點參數 'source_id'
errRobotNoConnector=沒有與機器人 "{0}" 的連接器
errRobotNoCurrentStation=機器人 {0} 當前不在一個站點上
errRobotNoPosition=獲取不到機器人 "{0}" 位置，請確認機器人在點位上
errRobotOffline=機器人 "{0}" 不在線或失去聯繫
errRobotUpdateBusy=機器人正在工作，不能更新
errStoHikNotSupported=網關簡單運單不支持海康機器人
errStoNoRobot=創建運單 {1} 失敗，網關中無機器人 {0}
errStoRetryFailedBadCurrentStatus=當前運單 {0} 的狀態不是失敗而是 {1}
errStoRetryFailedNoCurrent=機器人當前沒有運單
errTomDisconnected=調度服務器不存在，或與其連接失敗：{0}
errTomOrderAwaitNotFound=發送後查不到調度運單 "{0}"
errTomOrderKeyRouteMissing=必須指定至少一個關鍵位置
errTomOrderNoVehicle=運單 "{0}" 未分派機器人（運單狀態：{1}）
errTomOrderNoVehicle2=運單 "{0}" 未分派機器人
errUnsupportedFetchMap=不支持以下連接方式獲取地圖：{0}
errorLocationEmpty=位置（Location）不能爲空
errorTomOrderIdEmpty=調度運單號不能爲空
i18n_entity.AgentUser.fields.appId.label=應用
i18n_entity.AgentUser.fields.appKey.label=應用密鑰
i18n_entity.AgentUser.fields.btDisabled.label=停用
i18n_entity.AgentUser.fields.createdBy.label=創建人
i18n_entity.AgentUser.fields.createdOn.label=創建時間
i18n_entity.AgentUser.fields.id.label=ID
i18n_entity.AgentUser.fields.modifiedBy.label=最後修改人
i18n_entity.AgentUser.fields.modifiedOn.label=最後修改時間
i18n_entity.AgentUser.fields.remark.label=備註
i18n_entity.AgentUser.fields.version.label=修訂版本
i18n_entity.AgentUser.group=核心
i18n_entity.AgentUser.label=代理帳戶
i18n_entity.ApiCallTrace.fields.apiType.inlineOptionBill.items.HTTP.label=HTTP
i18n_entity.ApiCallTrace.fields.apiType.label=接口類型
i18n_entity.ApiCallTrace.fields.costTime.label=耗時
i18n_entity.ApiCallTrace.fields.createdBy.label=創建人
i18n_entity.ApiCallTrace.fields.createdOn.label=創建時間
i18n_entity.ApiCallTrace.fields.httpMethod.inlineOptionBill.items.DELETE.label=DELETE
i18n_entity.ApiCallTrace.fields.httpMethod.inlineOptionBill.items.GET.label=GET
i18n_entity.ApiCallTrace.fields.httpMethod.inlineOptionBill.items.POST.label=POST
i18n_entity.ApiCallTrace.fields.httpMethod.inlineOptionBill.items.PUT.label=PUT
i18n_entity.ApiCallTrace.fields.httpMethod.label=HTTP 方法
i18n_entity.ApiCallTrace.fields.httpPath.label=HTTP 路徑
i18n_entity.ApiCallTrace.fields.httpUrl.label=請求 URL
i18n_entity.ApiCallTrace.fields.id.label=ID
i18n_entity.ApiCallTrace.fields.modifiedBy.label=最後修改人
i18n_entity.ApiCallTrace.fields.modifiedOn.label=修改時間
i18n_entity.ApiCallTrace.fields.reqBody.label=請求正文
i18n_entity.ApiCallTrace.fields.reqBodyOn.label=要求記錄請求正文
i18n_entity.ApiCallTrace.fields.reqEndOn.label=發送響應時間
i18n_entity.ApiCallTrace.fields.reqIp.label=請求 IP
i18n_entity.ApiCallTrace.fields.reqStartOn.label=收到請求時間
i18n_entity.ApiCallTrace.fields.reqUser.label=請求用戶
i18n_entity.ApiCallTrace.fields.resBody.label=響應正文
i18n_entity.ApiCallTrace.fields.resBodyOn.label=要求記錄響應正文
i18n_entity.ApiCallTrace.fields.resCode.label=響應代碼
i18n_entity.ApiCallTrace.fields.version.label=修訂版本
i18n_entity.ApiCallTrace.group=核心
i18n_entity.ApiCallTrace.label=接口調用記錄
i18n_entity.ApiCallTrace.pagesButtons.ListMain.buttons[0].label=刪除
i18n_entity.ApiCallTrace.pagesButtons.ListMain.buttons[1].label=導出
i18n_entity.ApiCallTrace.pagesButtons.ListMain.buttons[2].label=配置
i18n_entity.CallEmptyContainerOrder.fields.createdBy.label=創建人
i18n_entity.CallEmptyContainerOrder.fields.createdOn.label=創建時間
i18n_entity.CallEmptyContainerOrder.fields.district.label=庫區
i18n_entity.CallEmptyContainerOrder.fields.id.label=單號
i18n_entity.CallEmptyContainerOrder.fields.modifiedBy.label=最後修改人
i18n_entity.CallEmptyContainerOrder.fields.modifiedOn.label=最後修改時間
i18n_entity.CallEmptyContainerOrder.fields.num.label=數量
i18n_entity.CallEmptyContainerOrder.fields.version.label=修訂版本
i18n_entity.CallEmptyContainerOrder.group=倉庫
i18n_entity.CallEmptyContainerOrder.label=叫空容器
i18n_entity.CallEmptyContainerOrder.pagesButtons.Create.buttons[0].label=提交
i18n_entity.ContainerTransportOrder.fields.atPort.label=在庫口
i18n_entity.ContainerTransportOrder.fields.container.label=容器
i18n_entity.ContainerTransportOrder.fields.createdBy.label=創建人
i18n_entity.ContainerTransportOrder.fields.createdOn.label=創建時間
i18n_entity.ContainerTransportOrder.fields.doneOn.label=完成時間
i18n_entity.ContainerTransportOrder.fields.errMsg.label=錯誤原因
i18n_entity.ContainerTransportOrder.fields.expectedRobot.label=指定機器人
i18n_entity.ContainerTransportOrder.fields.falconTaskDefId.label=獵鷹任務模版 ID
i18n_entity.ContainerTransportOrder.fields.falconTaskDefLabel.label=獵鷹任務模版名
i18n_entity.ContainerTransportOrder.fields.falconTaskId.label=獵鷹任務編號
i18n_entity.ContainerTransportOrder.fields.fromBin.label=起點庫位
i18n_entity.ContainerTransportOrder.fields.fromChannel.label=起點巷道
i18n_entity.ContainerTransportOrder.fields.id.label=ID
i18n_entity.ContainerTransportOrder.fields.kind.label=類型
i18n_entity.ContainerTransportOrder.fields.loaded.label=已取貨
i18n_entity.ContainerTransportOrder.fields.modifiedBy.label=最後修改人
i18n_entity.ContainerTransportOrder.fields.modifiedOn.label=最後修改時間
i18n_entity.ContainerTransportOrder.fields.postProcessMark.label=處理標記
i18n_entity.ContainerTransportOrder.fields.priority.label=優先級
i18n_entity.ContainerTransportOrder.fields.remark.label=備註
i18n_entity.ContainerTransportOrder.fields.robotName.label=執行機器人
i18n_entity.ContainerTransportOrder.fields.sourceOrderId.label=關聯單據
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Assigned.label=已派車
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Building.label=未提交
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Cancelled.label=取消
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Created.label=已提交
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Done.label=完成
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Failed.label=失敗
i18n_entity.ContainerTransportOrder.fields.status.label=狀態
i18n_entity.ContainerTransportOrder.fields.toBin.label=終點庫位
i18n_entity.ContainerTransportOrder.fields.toChannel.label=終點巷道
i18n_entity.ContainerTransportOrder.fields.unloaded.label=已放貨
i18n_entity.ContainerTransportOrder.fields.version.label=修訂版本
i18n_entity.ContainerTransportOrder.group=WCS
i18n_entity.ContainerTransportOrder.label=容器搬運單
i18n_entity.ContainerTransportOrder.listStats.items[0].label=失敗
i18n_entity.DemoComponent.fields.createdBy.label=創建人
i18n_entity.DemoComponent.fields.createdOn.label=創建時間
i18n_entity.DemoComponent.fields.id.label=編號
i18n_entity.DemoComponent.fields.modifiedBy.label=最後修改人
i18n_entity.DemoComponent.fields.modifiedOn.label=最後修改時間
i18n_entity.DemoComponent.fields.referenceField.label=引用
i18n_entity.DemoComponent.fields.ro.label=根組織
i18n_entity.DemoComponent.fields.stringField.label=文本
i18n_entity.DemoComponent.fields.version.label=版本
i18n_entity.DemoComponent.group=測試
i18n_entity.DemoComponent.label=測試組件
i18n_entity.DemoComponentTable.fields.createdBy.label=創建人
i18n_entity.DemoComponentTable.fields.createdOn.label=創建時間
i18n_entity.DemoComponentTable.fields.dateField.label=日期字段
i18n_entity.DemoComponentTable.fields.id.label=編號
i18n_entity.DemoComponentTable.fields.intField.label=場域
i18n_entity.DemoComponentTable.fields.modifiedBy.label=最後修改人
i18n_entity.DemoComponentTable.fields.modifiedOn.label=最後修改時間
i18n_entity.DemoComponentTable.fields.referenceField.label=引用
i18n_entity.DemoComponentTable.fields.ro.label=根組織
i18n_entity.DemoComponentTable.fields.stringField.label=文本
i18n_entity.DemoComponentTable.fields.version.label=版本
i18n_entity.DemoComponentTable.group=測試
i18n_entity.DemoComponentTable.label=測試組件表格
i18n_entity.DemoEntity.fields.booleanField.label=布林
i18n_entity.DemoEntity.fields.booleanListField.label=布林多值
i18n_entity.DemoEntity.fields.checkList2Field.inlineOptionBill.items.v1.label=選項 1
i18n_entity.DemoEntity.fields.checkList2Field.inlineOptionBill.items.v2.label=選項 2
i18n_entity.DemoEntity.fields.checkList2Field.inlineOptionBill.items.v3.label=選項 3
i18n_entity.DemoEntity.fields.checkList2Field.inlineOptionBill.items.v4.label=選項 4
i18n_entity.DemoEntity.fields.checkList2Field.label=選項清單（多選）
i18n_entity.DemoEntity.fields.checkListField.inlineOptionBill.items.v1.label=選項 1
i18n_entity.DemoEntity.fields.checkListField.inlineOptionBill.items.v2.label=選項 2
i18n_entity.DemoEntity.fields.checkListField.inlineOptionBill.items.v3.label=選項 3
i18n_entity.DemoEntity.fields.checkListField.inlineOptionBill.items.v4.label=選項 4
i18n_entity.DemoEntity.fields.checkListField.label=選項清單（單選）
i18n_entity.DemoEntity.fields.componentField.label=組件
i18n_entity.DemoEntity.fields.componentListField.label=組件多值
i18n_entity.DemoEntity.fields.componentTableField.label=組件表格
i18n_entity.DemoEntity.fields.createdBy.label=創建人
i18n_entity.DemoEntity.fields.createdOn.label=創建時間
i18n_entity.DemoEntity.fields.dateField.label=日期
i18n_entity.DemoEntity.fields.dateListField.label=日期多值
i18n_entity.DemoEntity.fields.dateTimeField.label=日期時間
i18n_entity.DemoEntity.fields.dateTimeListField.label=日期時間多值
i18n_entity.DemoEntity.fields.fileField.label=文件
i18n_entity.DemoEntity.fields.fileListField.label=文件多值
i18n_entity.DemoEntity.fields.floatField.label=浮點數
i18n_entity.DemoEntity.fields.floatListField.label=浮點數多值
i18n_entity.DemoEntity.fields.id.label=編號
i18n_entity.DemoEntity.fields.imageField.label=圖片
i18n_entity.DemoEntity.fields.imageListField.label=圖片多值
i18n_entity.DemoEntity.fields.intField.label=整數
i18n_entity.DemoEntity.fields.intListField.label=整數多值
i18n_entity.DemoEntity.fields.modifiedBy.label=最後修改人
i18n_entity.DemoEntity.fields.modifiedOn.label=最後修改時間
i18n_entity.DemoEntity.fields.passwordField.label=密碼輸入
i18n_entity.DemoEntity.fields.referenceField.label=引用
i18n_entity.DemoEntity.fields.referenceListField.label=引用多值
i18n_entity.DemoEntity.fields.ro.label=根組織
i18n_entity.DemoEntity.fields.selectField.inlineOptionBill.items.v1.label=選項 1
i18n_entity.DemoEntity.fields.selectField.inlineOptionBill.items.v2.label=選項 2
i18n_entity.DemoEntity.fields.selectField.inlineOptionBill.items.v3.label=選項 3
i18n_entity.DemoEntity.fields.selectField.inlineOptionBill.items.v4.label=選項 4
i18n_entity.DemoEntity.fields.selectField.label=選擇輸入
i18n_entity.DemoEntity.fields.selectListField.inlineOptionBill.items.v1.label=選項 1
i18n_entity.DemoEntity.fields.selectListField.inlineOptionBill.items.v2.label=選項 2
i18n_entity.DemoEntity.fields.selectListField.inlineOptionBill.items.v3.label=選項 3
i18n_entity.DemoEntity.fields.selectListField.inlineOptionBill.items.v4.label=選項 4
i18n_entity.DemoEntity.fields.selectListField.label=選擇輸入（多值）
i18n_entity.DemoEntity.fields.stringField.label=文本
i18n_entity.DemoEntity.fields.stringListField.label=文本多值
i18n_entity.DemoEntity.fields.textAreaField.label=多行文本輸入
i18n_entity.DemoEntity.fields.textAreaListField.label=多行文本輸入（多值）
i18n_entity.DemoEntity.fields.version.label=版本
i18n_entity.DemoEntity.group=測試
i18n_entity.DemoEntity.label=測試實體
i18n_entity.Department.fields.createdBy.label=創建人
i18n_entity.Department.fields.createdOn.label=創建時間
i18n_entity.Department.fields.disabled.label=禁用
i18n_entity.Department.fields.id.label=編號
i18n_entity.Department.fields.leafNode.label=葉部門
i18n_entity.Department.fields.level.label=層級
i18n_entity.Department.fields.modifiedBy.label=最後修改人
i18n_entity.Department.fields.modifiedOn.label=最後修改時間
i18n_entity.Department.fields.name.label=名稱
i18n_entity.Department.fields.owner.label=負責人
i18n_entity.Department.fields.parentNode.label=上級部門
i18n_entity.Department.fields.ro.label=根組織
i18n_entity.Department.fields.rootNode.label=頂級部門
i18n_entity.Department.fields.version.label=版本
i18n_entity.Department.group=用戶
i18n_entity.Department.label=部門
i18n_entity.DirectRobotOrder.fields.createdBy.label=創建人
i18n_entity.DirectRobotOrder.fields.createdOn.label=創建時間
i18n_entity.DirectRobotOrder.fields.description.label=描述
i18n_entity.DirectRobotOrder.fields.doneOn.label=完成時間
i18n_entity.DirectRobotOrder.fields.errMsg.label=錯誤原因
i18n_entity.DirectRobotOrder.fields.id.label=ID
i18n_entity.DirectRobotOrder.fields.modifiedBy.label=最後修改人
i18n_entity.DirectRobotOrder.fields.modifiedOn.label=修改時間
i18n_entity.DirectRobotOrder.fields.moves.label=動作
i18n_entity.DirectRobotOrder.fields.robotName.label=機器人名
i18n_entity.DirectRobotOrder.fields.seer3066.label=指定路徑導航
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Cancelled.label=已取消
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Created.label=新建
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Done.label=已完成
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Failed.label=失敗
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.ManualDone.label=手工完成
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Sent.label=已下發
i18n_entity.DirectRobotOrder.fields.status.label=狀態
i18n_entity.DirectRobotOrder.fields.status.listBatchButtons.buttons[0].label=取消
i18n_entity.DirectRobotOrder.fields.status.listBatchButtons.buttons[1].label=手工完成
i18n_entity.DirectRobotOrder.fields.taskId.label=所屬任務
i18n_entity.DirectRobotOrder.fields.version.label=修訂版本
i18n_entity.DirectRobotOrder.group=WCS
i18n_entity.DirectRobotOrder.label=直接運單
i18n_entity.DirectRobotOrder.listStats.items[0].label=新建
i18n_entity.DirectRobotOrder.listStats.items[1].label=下發
i18n_entity.DirectRobotOrder.listStats.items[2].label=失敗
i18n_entity.EmptyContainerStoreOrder.fields.container.label=容器
i18n_entity.EmptyContainerStoreOrder.fields.containerType.label=容器類型
i18n_entity.EmptyContainerStoreOrder.fields.createdBy.label=創建人
i18n_entity.EmptyContainerStoreOrder.fields.createdOn.label=創建時間
i18n_entity.EmptyContainerStoreOrder.fields.id.label=ID
i18n_entity.EmptyContainerStoreOrder.fields.modifiedBy.label=最後修改人
i18n_entity.EmptyContainerStoreOrder.fields.modifiedOn.label=最後修改時間
i18n_entity.EmptyContainerStoreOrder.fields.storeDistrict.label=存儲區
i18n_entity.EmptyContainerStoreOrder.fields.version.label=修訂版本
i18n_entity.EmptyContainerStoreOrder.group=倉庫
i18n_entity.EmptyContainerStoreOrder.label=新容器上架單
i18n_entity.EmptyContainerStoreOrder.pagesButtons.ListMain.buttons[0].label=新建
i18n_entity.EmptyContainerStoreOrder.pagesButtons.ListMain.buttons[1].label=刪除
i18n_entity.EmptyContainerStoreOrder.pagesButtons.ListMain.buttons[2].label=已放空箱
i18n_entity.EntityChangedRecord.fields.changeType.label=修改類型
i18n_entity.EntityChangedRecord.fields.createdBy.label=創建人
i18n_entity.EntityChangedRecord.fields.createdOn.label=創建時間
i18n_entity.EntityChangedRecord.fields.entityFields.label=字段列表
i18n_entity.EntityChangedRecord.fields.entityId.label=實體 ID
i18n_entity.EntityChangedRecord.fields.entityName.label=實體名
i18n_entity.EntityChangedRecord.fields.id.label=ID
i18n_entity.EntityChangedRecord.fields.modifiedBy.label=最後修改人
i18n_entity.EntityChangedRecord.fields.modifiedOn.label=最後修改時間
i18n_entity.EntityChangedRecord.fields.version.label=修訂版本
i18n_entity.EntityChangedRecord.group=核心
i18n_entity.EntityChangedRecord.label=實體修改記錄
i18n_entity.EntityComment.fields.content.label=內容
i18n_entity.EntityComment.fields.createdBy.label=創建人
i18n_entity.EntityComment.fields.createdOn.label=創建時間
i18n_entity.EntityComment.fields.entityId.label=entityId
i18n_entity.EntityComment.fields.entityName.label=實體
i18n_entity.EntityComment.fields.id.label=編號
i18n_entity.EntityComment.fields.modifiedBy.label=最後修改人
i18n_entity.EntityComment.fields.modifiedOn.label=最後修改時間
i18n_entity.EntityComment.fields.ro.label=企業
i18n_entity.EntityComment.fields.version.label=版本
i18n_entity.EntityComment.group=核心
i18n_entity.EntityComment.label=實體評論
i18n_entity.EntitySyncRecord.fields.bzType.label=業務類型
i18n_entity.EntitySyncRecord.fields.cost.label=耗時（毫秒）
i18n_entity.EntitySyncRecord.fields.createdBy.label=創建人
i18n_entity.EntitySyncRecord.fields.createdCount.label=新增實體數量
i18n_entity.EntitySyncRecord.fields.createdOn.label=創建時間
i18n_entity.EntitySyncRecord.fields.deletedCount.label=刪除實體數量
i18n_entity.EntitySyncRecord.fields.entityName.label=實體名
i18n_entity.EntitySyncRecord.fields.faileReason.label=失敗原因
i18n_entity.EntitySyncRecord.fields.id.label=ID
i18n_entity.EntitySyncRecord.fields.modifiedBy.label=最後修改人
i18n_entity.EntitySyncRecord.fields.modifiedOn.label=最後修改時間
i18n_entity.EntitySyncRecord.fields.oldCount.label=同步前實體數量
i18n_entity.EntitySyncRecord.fields.ro.label=企業
i18n_entity.EntitySyncRecord.fields.success.label=成功
i18n_entity.EntitySyncRecord.fields.syncCount.label=同步實體數量
i18n_entity.EntitySyncRecord.fields.syncOn.label=同步時間
i18n_entity.EntitySyncRecord.fields.txId.label=事務ID
i18n_entity.EntitySyncRecord.fields.updatedCount.label=更新實體數量
i18n_entity.EntitySyncRecord.fields.version.label=版本
i18n_entity.EntitySyncRecord.group=核心
i18n_entity.EntitySyncRecord.label=實體同步記錄
i18n_entity.ExternalCallRecord.fields.createdBy.label=創建人
i18n_entity.ExternalCallRecord.fields.createdOn.label=創建時間
i18n_entity.ExternalCallRecord.fields.doneOn.label=完成時間
i18n_entity.ExternalCallRecord.fields.failedNum.label=失敗次數
i18n_entity.ExternalCallRecord.fields.id.label=ID
i18n_entity.ExternalCallRecord.fields.modifiedBy.label=最後修改人
i18n_entity.ExternalCallRecord.fields.modifiedOn.label=最後修改時間
i18n_entity.ExternalCallRecord.fields.okChecker.label=成功檢查方法
i18n_entity.ExternalCallRecord.fields.options.label=選項
i18n_entity.ExternalCallRecord.fields.req.label=請求詳情
i18n_entity.ExternalCallRecord.fields.resBody.label=響應正文
i18n_entity.ExternalCallRecord.fields.resCode.label=響應碼
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Aborted.label=已終止
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Cancelled.label=已取消
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Done.label=成功
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Failed.label=失敗
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Init.label=開始
i18n_entity.ExternalCallRecord.fields.status.label=狀態
i18n_entity.ExternalCallRecord.fields.url.label=URL
i18n_entity.ExternalCallRecord.fields.version.label=修訂版本
i18n_entity.ExternalCallRecord.group=核心
i18n_entity.ExternalCallRecord.label=第三方調用記錄
i18n_entity.ExternalCallTrace.fields.createdBy.label=創建人
i18n_entity.ExternalCallTrace.fields.createdOn.label=創建時間
i18n_entity.ExternalCallTrace.fields.id.label=ID
i18n_entity.ExternalCallTrace.fields.ioError.label=通訊報錯
i18n_entity.ExternalCallTrace.fields.ioError.view.trueText=IO 報錯
i18n_entity.ExternalCallTrace.fields.ioErrorMsg.label=通訊報錯原因
i18n_entity.ExternalCallTrace.fields.method.label=HTTP 方法
i18n_entity.ExternalCallTrace.fields.modifiedBy.label=最後修改人
i18n_entity.ExternalCallTrace.fields.modifiedOn.label=最後修改時間
i18n_entity.ExternalCallTrace.fields.reqBody.label=請求正文
i18n_entity.ExternalCallTrace.fields.reqOn.label=請求時間
i18n_entity.ExternalCallTrace.fields.resBody.label=響應正文
i18n_entity.ExternalCallTrace.fields.resCode.label=響應碼
i18n_entity.ExternalCallTrace.fields.resOn.label=響應時間
i18n_entity.ExternalCallTrace.fields.url.label=URL
i18n_entity.ExternalCallTrace.fields.version.label=修訂版本
i18n_entity.ExternalCallTrace.group=核心
i18n_entity.ExternalCallTrace.label=HTTP 客戶端日誌
i18n_entity.FailureRecord.fields.createdBy.label=創建人
i18n_entity.FailureRecord.fields.createdOn.label=創建時間
i18n_entity.FailureRecord.fields.desc.label=描述
i18n_entity.FailureRecord.fields.firstOn.label=首次發生時間
i18n_entity.FailureRecord.fields.id.label=ID
i18n_entity.FailureRecord.fields.kind.label=類別
i18n_entity.FailureRecord.fields.lastOn.label=最新發生時間
i18n_entity.FailureRecord.fields.level.inlineOptionBill.items.Fatal.label=嚴重
i18n_entity.FailureRecord.fields.level.inlineOptionBill.items.Warning.label=警告
i18n_entity.FailureRecord.fields.level.label=級別
i18n_entity.FailureRecord.fields.modifiedBy.label=最後修改人
i18n_entity.FailureRecord.fields.modifiedOn.label=最後修改時間
i18n_entity.FailureRecord.fields.num.label=次數
i18n_entity.FailureRecord.fields.part.label=對象
i18n_entity.FailureRecord.fields.source.label=來源
i18n_entity.FailureRecord.fields.subKind.label=子類別
i18n_entity.FailureRecord.fields.version.label=修訂版本
i18n_entity.FailureRecord.group=核心
i18n_entity.FailureRecord.label=故障記錄
i18n_entity.FalconBlockChildId.fields.blockId.label=區塊ID
i18n_entity.FalconBlockChildId.fields.childId.label=childId
i18n_entity.FalconBlockChildId.fields.contextKey.label=ContextKey
i18n_entity.FalconBlockChildId.fields.createdBy.label=創建人
i18n_entity.FalconBlockChildId.fields.createdOn.label=創建時間
i18n_entity.FalconBlockChildId.fields.id.label=ID
i18n_entity.FalconBlockChildId.fields.index.label=index
i18n_entity.FalconBlockChildId.fields.modifiedBy.label=最後修改人
i18n_entity.FalconBlockChildId.fields.modifiedOn.label=修改時間
i18n_entity.FalconBlockChildId.fields.taskId.label=taskId
i18n_entity.FalconBlockChildId.fields.version.label=修訂版本
i18n_entity.FalconBlockChildId.group=獵鷹
i18n_entity.FalconBlockChildId.label=獵鷹任務塊子塊 ID
i18n_entity.FalconBlockRecord.fields.blockConfigId.label=Block ConfigId
i18n_entity.FalconBlockRecord.fields.createdBy.label=創建人
i18n_entity.FalconBlockRecord.fields.createdOn.label=創建時間
i18n_entity.FalconBlockRecord.fields.endedOn.label=結束
i18n_entity.FalconBlockRecord.fields.endedReason.label=enddReason
i18n_entity.FalconBlockRecord.fields.failureNum.label=失敗數
i18n_entity.FalconBlockRecord.fields.id.label=ID
i18n_entity.FalconBlockRecord.fields.internalVariables.label=內部變量
i18n_entity.FalconBlockRecord.fields.modifiedBy.label=最後修改人
i18n_entity.FalconBlockRecord.fields.modifiedOn.label=修改時間
i18n_entity.FalconBlockRecord.fields.outputParams.label=輸出參數
i18n_entity.FalconBlockRecord.fields.startedOn.label=開始
i18n_entity.FalconBlockRecord.fields.status.label=狀態
i18n_entity.FalconBlockRecord.fields.taskId.label=taskId
i18n_entity.FalconBlockRecord.fields.version.label=修訂版本
i18n_entity.FalconBlockRecord.group=獵鷹
i18n_entity.FalconBlockRecord.label=獵鷹任務塊記錄
i18n_entity.FalconLog.fields.blockId.label=關聯組件
i18n_entity.FalconLog.fields.createdBy.label=創建人
i18n_entity.FalconLog.fields.createdOn.label=創建時間
i18n_entity.FalconLog.fields.id.label=ID
i18n_entity.FalconLog.fields.level.inlineOptionBill.items.Debug.label=一般
i18n_entity.FalconLog.fields.level.inlineOptionBill.items.Error.label=錯誤
i18n_entity.FalconLog.fields.level.inlineOptionBill.items.Info.label=重要
i18n_entity.FalconLog.fields.level.label=級別
i18n_entity.FalconLog.fields.message.label=消息
i18n_entity.FalconLog.fields.modifiedBy.label=最後修改人
i18n_entity.FalconLog.fields.modifiedOn.label=修改時間
i18n_entity.FalconLog.fields.taskId.label=關聯任務
i18n_entity.FalconLog.fields.version.label=修訂版本
i18n_entity.FalconLog.group=獵鷹
i18n_entity.FalconLog.label=獵鷹日誌
i18n_entity.FalconRelatedObject.fields.createdBy.label=創建人
i18n_entity.FalconRelatedObject.fields.createdOn.label=創建時間
i18n_entity.FalconRelatedObject.fields.id.label=ID
i18n_entity.FalconRelatedObject.fields.modifiedBy.label=最後修改人
i18n_entity.FalconRelatedObject.fields.modifiedOn.label=最後修改時間
i18n_entity.FalconRelatedObject.fields.objectArgs.label=objectArgs
i18n_entity.FalconRelatedObject.fields.objectId.label=objectId
i18n_entity.FalconRelatedObject.fields.objectType.label=objectType
i18n_entity.FalconRelatedObject.fields.taskId.label=taskId
i18n_entity.FalconRelatedObject.group=獵鷹
i18n_entity.FalconRelatedObject.label=獵鷹任務相關對象
i18n_entity.FalconTaskRecord.fields.createdBy.label=創建人
i18n_entity.FalconTaskRecord.fields.createdOn.label=創建時間
i18n_entity.FalconTaskRecord.fields.defId.label=定義 ID
i18n_entity.FalconTaskRecord.fields.defLabel.label=任務模版
i18n_entity.FalconTaskRecord.fields.defVersion.label=模版版本
i18n_entity.FalconTaskRecord.fields.endedOn.label=結束時間
i18n_entity.FalconTaskRecord.fields.endedReason.label=錯誤原因
i18n_entity.FalconTaskRecord.fields.failureNum.label=故障次數
i18n_entity.FalconTaskRecord.fields.id.label=ID
i18n_entity.FalconTaskRecord.fields.inputParams.label=輸入參數
i18n_entity.FalconTaskRecord.fields.modifiedBy.label=最後修改人
i18n_entity.FalconTaskRecord.fields.modifiedOn.label=修改時間
i18n_entity.FalconTaskRecord.fields.paused.label=已暫停
i18n_entity.FalconTaskRecord.fields.ro.label=RO
i18n_entity.FalconTaskRecord.fields.rootBlockStateId.label=根塊ID
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.100.label=已創建
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.120.label=已開始
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.140.label=故障
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.160.label=已完成
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.180.label=已取消
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.190.label=已失敗
i18n_entity.FalconTaskRecord.fields.status.label=狀態
i18n_entity.FalconTaskRecord.fields.subTask.label=子任務
i18n_entity.FalconTaskRecord.fields.topTaskId.label=頂層任務 ID
i18n_entity.FalconTaskRecord.fields.variables.label=任務變量
i18n_entity.FalconTaskRecord.fields.version.label=修訂版本
i18n_entity.FalconTaskRecord.group=獵鷹
i18n_entity.FalconTaskRecord.label=獵鷹任務記錄
i18n_entity.FalconTaskRecord.listCard.defVersion.prefix=(
i18n_entity.FalconTaskRecord.listCard.defVersion.suffix=)
i18n_entity.FalconTaskRecord.listStats.items[0].label=故障
i18n_entity.FalconTaskRecord.listStats.items[1].label=已暫停
i18n_entity.FalconTaskRecord.listStats.items[2].label=今日新增
i18n_entity.FalconTaskRecord.listStats.items[3].label=今日失敗取消
i18n_entity.FalconTaskRecord.listStats.items[4].label=本週新增
i18n_entity.FalconTaskRecord.listStats.items[5].label=本週失敗取消
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[0].label=刪除
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[1].label=導出
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[2].label=暫停執行
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[3].label=繼續執行
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[4].label=故障重試
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[5].label=取消執行
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[6].label=刪除
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[7].label=模版
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[8].label=全局控制
i18n_entity.FalconTaskResource.fields.args.label=參數
i18n_entity.FalconTaskResource.fields.createdBy.label=創建人
i18n_entity.FalconTaskResource.fields.createdOn.label=創建時間
i18n_entity.FalconTaskResource.fields.id.label=ID
i18n_entity.FalconTaskResource.fields.modifiedBy.label=最後修改人
i18n_entity.FalconTaskResource.fields.modifiedOn.label=最後修改時間
i18n_entity.FalconTaskResource.fields.resId.label=資源 ID
i18n_entity.FalconTaskResource.fields.resType.label=資源類型
i18n_entity.FalconTaskResource.fields.taskId.label=任務編號
i18n_entity.FalconTaskResource.fields.version.label=修訂版本
i18n_entity.FalconTaskResource.group=獵鷹
i18n_entity.FalconTaskResource.label=獵鷹任務資源
i18n_entity.FbAssemblyLine.fields.building.label=樓
i18n_entity.FbAssemblyLine.fields.buildingLayer.label=樓層
i18n_entity.FbAssemblyLine.fields.createdBy.label=創建人
i18n_entity.FbAssemblyLine.fields.createdOn.label=創建時間
i18n_entity.FbAssemblyLine.fields.disabled.label=禁用
i18n_entity.FbAssemblyLine.fields.id.label=單號
i18n_entity.FbAssemblyLine.fields.modifiedBy.label=最後修改人
i18n_entity.FbAssemblyLine.fields.modifiedOn.label=最後修改時間
i18n_entity.FbAssemblyLine.fields.name.label=名稱
i18n_entity.FbAssemblyLine.fields.remark.label=備註
i18n_entity.FbAssemblyLine.fields.ro.label=根組織
i18n_entity.FbAssemblyLine.fields.version.label=版本
i18n_entity.FbAssemblyLine.group=主數據
i18n_entity.FbAssemblyLine.label=產線
i18n_entity.FbBin.fields.assemblyLine.label=所屬產線
i18n_entity.FbBin.fields.boxDirection.label=貨叉方向
i18n_entity.FbBin.fields.boxHeight.label=貨叉擡升高度
i18n_entity.FbBin.fields.btDisabled.label=停用
i18n_entity.FbBin.fields.channel.label=所在巷道
i18n_entity.FbBin.fields.column.label=列
i18n_entity.FbBin.fields.container.label=庫位上容器
i18n_entity.FbBin.fields.createdBy.label=創建人
i18n_entity.FbBin.fields.createdOn.label=創建時間
i18n_entity.FbBin.fields.depth.label=深
i18n_entity.FbBin.fields.district.label=所屬庫區
i18n_entity.FbBin.fields.id.label=庫位編號
i18n_entity.FbBin.fields.layer.label=層
i18n_entity.FbBin.fields.loadStatus.inlineOptionBill.items.Empty.label=未佔用
i18n_entity.FbBin.fields.loadStatus.inlineOptionBill.items.Occupied.label=已佔用
i18n_entity.FbBin.fields.loadStatus.inlineOptionBill.items.ToLoad.label=即將運來
i18n_entity.FbBin.fields.loadStatus.inlineOptionBill.items.ToUnload.label=即將運走
i18n_entity.FbBin.fields.loadStatus.label=佔用狀態
i18n_entity.FbBin.fields.locked.label=鎖定
i18n_entity.FbBin.fields.locked.listBatchButtons.buttons[0].label=解鎖
i18n_entity.FbBin.fields.lockedBy.label=鎖定原因
i18n_entity.FbBin.fields.materialCategoryLabel.label=存儲物料分類名稱
i18n_entity.FbBin.fields.modifiedBy.label=最後修改人
i18n_entity.FbBin.fields.modifiedOn.label=最後修改時間
i18n_entity.FbBin.fields.occupied.label=有貨
i18n_entity.FbBin.fields.occupied.view.trueText=有貨
i18n_entity.FbBin.fields.pendingContainer.label=要被運來的容器
i18n_entity.FbBin.fields.purpose.label=用途
i18n_entity.FbBin.fields.rack.label=所屬貨架
i18n_entity.FbBin.fields.remark.label=備註
i18n_entity.FbBin.fields.ro.label=根組織
i18n_entity.FbBin.fields.robotDirection.label=機器人方向
i18n_entity.FbBin.fields.robotX.label=機器人位置 X
i18n_entity.FbBin.fields.robotY.label=機器人位置 Y
i18n_entity.FbBin.fields.row.label=排
i18n_entity.FbBin.fields.version.label=版本
i18n_entity.FbBin.fields.warehouse.label=所屬倉庫
i18n_entity.FbBin.fields.workSite.label=所屬工位
i18n_entity.FbBin.group=主數據
i18n_entity.FbBin.label=庫位
i18n_entity.FbBin.listStats.items[0].label=處理中（鎖定）
i18n_entity.FbBin.listStats.items[1].label=有貨
i18n_entity.FbBin.pagesButtons.ListMain.buttons[0].label=新增
i18n_entity.FbBin.pagesButtons.ListMain.buttons[1].label=批量編輯
i18n_entity.FbBin.pagesButtons.ListMain.buttons[2].label=刪除
i18n_entity.FbBin.pagesButtons.ListMain.buttons[3].label=導出
i18n_entity.FbBin.pagesButtons.ListMain.buttons[4].label=導入
i18n_entity.FbBin.pagesButtons.ListMain.buttons[5].label=批量創建庫位
i18n_entity.FbContainer.fields.bin.label=所在庫位
i18n_entity.FbContainer.fields.btDisabled.label=停用
i18n_entity.FbContainer.fields.createdBy.label=創建人
i18n_entity.FbContainer.fields.createdOn.label=創建時間
i18n_entity.FbContainer.fields.district.label=當前庫區
i18n_entity.FbContainer.fields.filled.label=有貨
i18n_entity.FbContainer.fields.fixedStoreBin.label=指定庫位存儲
i18n_entity.FbContainer.fields.height.label=容器高度
i18n_entity.FbContainer.fields.id.label=容器編號
i18n_entity.FbContainer.fields.length.label=容器長度
i18n_entity.FbContainer.fields.locked.label=鎖定
i18n_entity.FbContainer.fields.locked.listBatchButtons.buttons[0].label=解鎖
i18n_entity.FbContainer.fields.maxWeight.label=容器承重
i18n_entity.FbContainer.fields.modifiedBy.label=最後修改人
i18n_entity.FbContainer.fields.modifiedOn.label=最後修改時間
i18n_entity.FbContainer.fields.onRobot.label=所在機器人
i18n_entity.FbContainer.fields.pContainer.label=父容器
i18n_entity.FbContainer.fields.preBin.label=預定存儲庫位
i18n_entity.FbContainer.fields.qcResult.label=質檢結果
i18n_entity.FbContainer.fields.remark.label=備註
i18n_entity.FbContainer.fields.ro.label=根組織
i18n_entity.FbContainer.fields.state.label=狀態
i18n_entity.FbContainer.fields.subNum.label=格數
i18n_entity.FbContainer.fields.targetBin.label=要被運往的庫位
i18n_entity.FbContainer.fields.taskType.inlineOptionBill.items.Count.label=盤點中
i18n_entity.FbContainer.fields.taskType.inlineOptionBill.items.Pick.label=待分揀
i18n_entity.FbContainer.fields.taskType.inlineOptionBill.items.Put.label=待裝箱
i18n_entity.FbContainer.fields.taskType.label=任務類型
i18n_entity.FbContainer.fields.type.label=容器類型
i18n_entity.FbContainer.fields.version.label=版本
i18n_entity.FbContainer.fields.volume.label=容器容積
i18n_entity.FbContainer.fields.warehouse.label=當前倉庫
i18n_entity.FbContainer.fields.width.label=容器寬度
i18n_entity.FbContainer.fields.workStatus.label=工作狀態
i18n_entity.FbContainer.group=主數據
i18n_entity.FbContainer.label=容器
i18n_entity.FbContainer.listStats.items[0].label=處理中（鎖定）
i18n_entity.FbContainer.listStats.items[1].label=有貨
i18n_entity.FbContainerType.fields.btDisabled.label=停用
i18n_entity.FbContainerType.fields.createdBy.label=創建人
i18n_entity.FbContainerType.fields.createdOn.label=創建時間
i18n_entity.FbContainerType.fields.id.label=容器類型編碼
i18n_entity.FbContainerType.fields.mixedMaterial.label=物料混放
i18n_entity.FbContainerType.fields.modifiedBy.label=最後修改人
i18n_entity.FbContainerType.fields.modifiedOn.label=最後修改時間
i18n_entity.FbContainerType.fields.name.label=容器類型名稱
i18n_entity.FbContainerType.fields.remark.label=說明
i18n_entity.FbContainerType.fields.ro.label=根組織
i18n_entity.FbContainerType.fields.storeDistricts.label=存儲庫區
i18n_entity.FbContainerType.fields.subNum.label=分格數
i18n_entity.FbContainerType.fields.version.label=版本
i18n_entity.FbContainerType.group=主數據
i18n_entity.FbContainerType.label=容器類型
i18n_entity.FbCountDiffLine.fields.actualQty.label=實際數量
i18n_entity.FbCountDiffLine.fields.bin.label=原存放庫位
i18n_entity.FbCountDiffLine.fields.btLineNo.label=行號
i18n_entity.FbCountDiffLine.fields.btMaterial.label=物料
i18n_entity.FbCountDiffLine.fields.btMaterialCategory.label=物料分類編號
i18n_entity.FbCountDiffLine.fields.btMaterialCategoryName.label=物料分類名稱
i18n_entity.FbCountDiffLine.fields.btMaterialId.label=物料編號
i18n_entity.FbCountDiffLine.fields.btMaterialImage.label=物料圖片
i18n_entity.FbCountDiffLine.fields.btMaterialModel.label=物料型號
i18n_entity.FbCountDiffLine.fields.btMaterialName.label=物料名稱
i18n_entity.FbCountDiffLine.fields.btMaterialSpec.label=物料規格
i18n_entity.FbCountDiffLine.fields.btParentId.label=所屬訂單
i18n_entity.FbCountDiffLine.fields.container.label=容器
i18n_entity.FbCountDiffLine.fields.createdBy.label=創建人
i18n_entity.FbCountDiffLine.fields.createdOn.label=創建時間
i18n_entity.FbCountDiffLine.fields.diffQty.label=差異數量
i18n_entity.FbCountDiffLine.fields.fix.label=執行
i18n_entity.FbCountDiffLine.fields.id.label=ID
i18n_entity.FbCountDiffLine.fields.lotNo.label=批次號
i18n_entity.FbCountDiffLine.fields.modifiedBy.label=最後修改人
i18n_entity.FbCountDiffLine.fields.modifiedOn.label=修改時間
i18n_entity.FbCountDiffLine.fields.qty.label=原數量
i18n_entity.FbCountDiffLine.fields.qualityLevel.label=質量等級
i18n_entity.FbCountDiffLine.fields.remark.label=備註
i18n_entity.FbCountDiffLine.fields.sourceInvLayout.label=關聯庫存明細
i18n_entity.FbCountDiffLine.fields.subContainerId.label=格號
i18n_entity.FbCountDiffLine.fields.taskId.label=盤點任務
i18n_entity.FbCountDiffLine.fields.version.label=修訂版本
i18n_entity.FbCountDiffLine.group=倉庫
i18n_entity.FbCountDiffLine.label=盤差記錄行
i18n_entity.FbCountFix.fields.btMaterial.label=物料
i18n_entity.FbCountFix.fields.btMaterialCategory.label=物料分類編號
i18n_entity.FbCountFix.fields.btMaterialCategoryName.label=物料分類名稱
i18n_entity.FbCountFix.fields.btMaterialId.label=物料編號
i18n_entity.FbCountFix.fields.btMaterialImage.label=物料圖片
i18n_entity.FbCountFix.fields.btMaterialModel.label=物料型號
i18n_entity.FbCountFix.fields.btMaterialName.label=物料名稱
i18n_entity.FbCountFix.fields.btMaterialSpec.label=物料規格
i18n_entity.FbCountFix.fields.container.label=容器
i18n_entity.FbCountFix.fields.createdBy.label=創建人
i18n_entity.FbCountFix.fields.createdOn.label=創建時間
i18n_entity.FbCountFix.fields.id.label=ID
i18n_entity.FbCountFix.fields.lotNo.label=批次號
i18n_entity.FbCountFix.fields.modifiedBy.label=最後修改人
i18n_entity.FbCountFix.fields.modifiedOn.label=修改時間
i18n_entity.FbCountFix.fields.qty.label=變更數量
i18n_entity.FbCountFix.fields.qualityLevel.label=質量等級
i18n_entity.FbCountFix.fields.remark.label=備註
i18n_entity.FbCountFix.fields.subContainerId.label=格子號
i18n_entity.FbCountFix.fields.version.label=修訂版本
i18n_entity.FbCountFix.group=倉庫
i18n_entity.FbCountFix.label=庫存修正記錄
i18n_entity.FbCountOrder.fields.bins.label=計劃盤點庫位列表
i18n_entity.FbCountOrder.fields.btOrderState.inlineOptionBill.items.Done.label=完成
i18n_entity.FbCountOrder.fields.btOrderState.inlineOptionBill.items.Init.label=未提交
i18n_entity.FbCountOrder.fields.btOrderState.inlineOptionBill.items.Submitted.label=已提交
i18n_entity.FbCountOrder.fields.btOrderState.label=單據狀態
i18n_entity.FbCountOrder.fields.btOrderStateReason.label=狀態說明
i18n_entity.FbCountOrder.fields.containers.label=計劃盤點容器列表
i18n_entity.FbCountOrder.fields.createdBy.label=創建人
i18n_entity.FbCountOrder.fields.createdOn.label=創建時間
i18n_entity.FbCountOrder.fields.diffLines.label=盤差列表
i18n_entity.FbCountOrder.fields.districts.label=計劃盤點庫區列表
i18n_entity.FbCountOrder.fields.doneProcessed.label=盤差已處理
i18n_entity.FbCountOrder.fields.id.label=單號
i18n_entity.FbCountOrder.fields.materials.label=計劃盤點物料列表
i18n_entity.FbCountOrder.fields.modifiedBy.label=最後修改人
i18n_entity.FbCountOrder.fields.modifiedOn.label=修改時間
i18n_entity.FbCountOrder.fields.remark.label=說明
i18n_entity.FbCountOrder.fields.taskGenerated.label=盤點任務已生成
i18n_entity.FbCountOrder.fields.taskLines.label=任務統計行
i18n_entity.FbCountOrder.fields.version.label=修訂版本
i18n_entity.FbCountOrder.group=倉庫
i18n_entity.FbCountOrder.label=盤點單
i18n_entity.FbCountTask.fields.bin.label=來源庫位
i18n_entity.FbCountTask.fields.btLines.label=單行
i18n_entity.FbCountTask.fields.btOrderState.inlineOptionBill.items.Init.label=待盤點
i18n_entity.FbCountTask.fields.btOrderState.inlineOptionBill.items.Submitted.label=已盤點
i18n_entity.FbCountTask.fields.btOrderState.label=單據狀態
i18n_entity.FbCountTask.fields.btOrderStateReason.label=狀態說明
i18n_entity.FbCountTask.fields.container.label=盤點容器
i18n_entity.FbCountTask.fields.containerInOrderId.label=容器入庫運單號
i18n_entity.FbCountTask.fields.containerOutOrderId.label=容器出庫運單號
i18n_entity.FbCountTask.fields.countOrderId.label=所屬盤點單
i18n_entity.FbCountTask.fields.createdBy.label=創建人
i18n_entity.FbCountTask.fields.createdOn.label=創建時間
i18n_entity.FbCountTask.fields.id.label=ID
i18n_entity.FbCountTask.fields.modifiedBy.label=最後修改人
i18n_entity.FbCountTask.fields.modifiedOn.label=最後修改時間
i18n_entity.FbCountTask.fields.remark.label=說明
i18n_entity.FbCountTask.fields.submittedPostProcessed.label=提交後處理完成
i18n_entity.FbCountTask.fields.version.label=修訂版本
i18n_entity.FbCountTask.group=倉庫
i18n_entity.FbCountTask.label=盤點任務
i18n_entity.FbCountTask.listStats.items[0].label=待盤點
i18n_entity.FbCountTaskLine.fields.actualQty.label=實際數量
i18n_entity.FbCountTaskLine.fields.amount.label=金額
i18n_entity.FbCountTaskLine.fields.bin.label=所在庫位
i18n_entity.FbCountTaskLine.fields.btLineNo.label=行號
i18n_entity.FbCountTaskLine.fields.btMaterial.label=物料
i18n_entity.FbCountTaskLine.fields.btMaterialCategory.label=物料分類編號
i18n_entity.FbCountTaskLine.fields.btMaterialCategoryName.label=物料分類名稱
i18n_entity.FbCountTaskLine.fields.btMaterialId.label=物料編號
i18n_entity.FbCountTaskLine.fields.btMaterialImage.label=物料圖片
i18n_entity.FbCountTaskLine.fields.btMaterialModel.label=物料型號
i18n_entity.FbCountTaskLine.fields.btMaterialName.label=物料名稱
i18n_entity.FbCountTaskLine.fields.btMaterialSpec.label=物料規格
i18n_entity.FbCountTaskLine.fields.btParentId.label=所屬任務
i18n_entity.FbCountTaskLine.fields.createdBy.label=創建人
i18n_entity.FbCountTaskLine.fields.createdOn.label=創建時間
i18n_entity.FbCountTaskLine.fields.district.label=所在庫區
i18n_entity.FbCountTaskLine.fields.expDate.label=有效期
i18n_entity.FbCountTaskLine.fields.id.label=ID
i18n_entity.FbCountTaskLine.fields.leafContainer.label=容器
i18n_entity.FbCountTaskLine.fields.lotNo.label=批次號
i18n_entity.FbCountTaskLine.fields.mfgDate.label=生產日期
i18n_entity.FbCountTaskLine.fields.modifiedBy.label=最後修改人
i18n_entity.FbCountTaskLine.fields.modifiedOn.label=最後修改時間
i18n_entity.FbCountTaskLine.fields.owner.label=貨主
i18n_entity.FbCountTaskLine.fields.price.label=單價
i18n_entity.FbCountTaskLine.fields.qty.label=數量
i18n_entity.FbCountTaskLine.fields.qualityLevel.label=質量等級
i18n_entity.FbCountTaskLine.fields.sourceInvLayout.label=引用庫存明細
i18n_entity.FbCountTaskLine.fields.subContainerId.label=格子
i18n_entity.FbCountTaskLine.fields.topContainer.label=最外層容器
i18n_entity.FbCountTaskLine.fields.unitLabel.label=單位名稱
i18n_entity.FbCountTaskLine.fields.vendor.label=供應商
i18n_entity.FbCountTaskLine.fields.version.label=修訂版本
i18n_entity.FbCountTaskLine.fields.warehouse.label=所在倉庫
i18n_entity.FbCountTaskLine.group=倉庫
i18n_entity.FbCountTaskLine.label=盤點任務行
i18n_entity.FbCountTaskStatLine.fields.bin.label=盤點庫位
i18n_entity.FbCountTaskStatLine.fields.btLineNo.label=行號
i18n_entity.FbCountTaskStatLine.fields.btParentId.label=所屬訂單
i18n_entity.FbCountTaskStatLine.fields.container.label=盤點容器
i18n_entity.FbCountTaskStatLine.fields.createdBy.label=創建人
i18n_entity.FbCountTaskStatLine.fields.createdOn.label=創建時間
i18n_entity.FbCountTaskStatLine.fields.id.label=ID
i18n_entity.FbCountTaskStatLine.fields.materialsNum.label=記錄物料數
i18n_entity.FbCountTaskStatLine.fields.modifiedBy.label=最後修改人
i18n_entity.FbCountTaskStatLine.fields.modifiedOn.label=最後修改時間
i18n_entity.FbCountTaskStatLine.fields.qty.label=記錄總數量
i18n_entity.FbCountTaskStatLine.fields.taskId.label=盤點任務
i18n_entity.FbCountTaskStatLine.fields.version.label=修訂版本
i18n_entity.FbCountTaskStatLine.group=倉庫
i18n_entity.FbCountTaskStatLine.label=盤點單任務統計行
i18n_entity.FbCustomer.fields.address.label=地址
i18n_entity.FbCustomer.fields.btDisabled.label=停用
i18n_entity.FbCustomer.fields.contact.label=聯繫人
i18n_entity.FbCustomer.fields.createdBy.label=創建人
i18n_entity.FbCustomer.fields.createdOn.label=創建時間
i18n_entity.FbCustomer.fields.id.label=客戶編碼
i18n_entity.FbCustomer.fields.modifiedBy.label=最後修改人
i18n_entity.FbCustomer.fields.modifiedOn.label=最後修改時間
i18n_entity.FbCustomer.fields.name.label=客戶名稱
i18n_entity.FbCustomer.fields.phone.label=聯繫電話
i18n_entity.FbCustomer.fields.remark.label=備註
i18n_entity.FbCustomer.fields.ro.label=根組織
i18n_entity.FbCustomer.fields.version.label=版本
i18n_entity.FbCustomer.group=主數據
i18n_entity.FbCustomer.label=客戶
i18n_entity.FbCustomer.listCard.btDisabled.formatMapping[0].replaceText=已停用
i18n_entity.FbDepartment.fields.btDisabled.label=停用
i18n_entity.FbDepartment.fields.btHiLeafNode.label=葉節點
i18n_entity.FbDepartment.fields.btHiNodeLevel.label=層
i18n_entity.FbDepartment.fields.btHiParentNode.label=上級
i18n_entity.FbDepartment.fields.btHiRootNode.label=頂層節點
i18n_entity.FbDepartment.fields.createdBy.label=創建人
i18n_entity.FbDepartment.fields.createdOn.label=創建時間
i18n_entity.FbDepartment.fields.id.label=編號
i18n_entity.FbDepartment.fields.modifiedBy.label=最後修改人
i18n_entity.FbDepartment.fields.modifiedOn.label=最後修改時間
i18n_entity.FbDepartment.fields.name.label=名稱
i18n_entity.FbDepartment.fields.owner.label=負責人
i18n_entity.FbDepartment.fields.ro.label=根組織
i18n_entity.FbDepartment.fields.version.label=版本
i18n_entity.FbDepartment.group=用戶
i18n_entity.FbDepartment.label=組織
i18n_entity.FbDevTask.fields.comments.label=評論
i18n_entity.FbDevTask.fields.createdBy.label=創建人
i18n_entity.FbDevTask.fields.createdOn.label=創建時間
i18n_entity.FbDevTask.fields.description.label=描述
i18n_entity.FbDevTask.fields.devVersion.label=迭代
i18n_entity.FbDevTask.fields.files.label=附件文件
i18n_entity.FbDevTask.fields.id.label=編號
i18n_entity.FbDevTask.fields.images.label=附件圖片
i18n_entity.FbDevTask.fields.kind.inlineOptionBill.items.Bug.label=缺陷
i18n_entity.FbDevTask.fields.kind.inlineOptionBill.items.Feature.label=功能
i18n_entity.FbDevTask.fields.kind.inlineOptionBill.items.Improvement.label=優化
i18n_entity.FbDevTask.fields.kind.inlineOptionBill.items.Test.label=測試
i18n_entity.FbDevTask.fields.kind.label=類型
i18n_entity.FbDevTask.fields.modifiedBy.label=最後修改人
i18n_entity.FbDevTask.fields.modifiedOn.label=最後修改時間
i18n_entity.FbDevTask.fields.priority.inlineOptionBill.items.High.label=高
i18n_entity.FbDevTask.fields.priority.inlineOptionBill.items.Low.label=低
i18n_entity.FbDevTask.fields.priority.inlineOptionBill.items.Normal.label=中
i18n_entity.FbDevTask.fields.priority.label=優先級
i18n_entity.FbDevTask.fields.processedBy.label=處理人
i18n_entity.FbDevTask.fields.project.label=項目
i18n_entity.FbDevTask.fields.ro.label=企業
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Closed.label=已關閉
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Open.label=新建
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Processing.label=進行中
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Rejected.label=已拒絕
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Resolved.label=已解決
i18n_entity.FbDevTask.fields.state.label=狀態
i18n_entity.FbDevTask.fields.testImages.label=測試圖片
i18n_entity.FbDevTask.fields.testResult.label=測試結果
i18n_entity.FbDevTask.fields.title.label=標題
i18n_entity.FbDevTask.fields.version.label=版本
i18n_entity.FbDevTask.group=開發
i18n_entity.FbDevTask.label=協同任務
i18n_entity.FbDevVersion.fields.createdBy.label=創建人
i18n_entity.FbDevVersion.fields.createdOn.label=創建時間
i18n_entity.FbDevVersion.fields.displayOrder.label=顯示順序
i18n_entity.FbDevVersion.fields.done.label=已完成
i18n_entity.FbDevVersion.fields.id.label=ID
i18n_entity.FbDevVersion.fields.modifiedBy.label=最後修改人
i18n_entity.FbDevVersion.fields.modifiedOn.label=最後修改時間
i18n_entity.FbDevVersion.fields.name.label=名稱
i18n_entity.FbDevVersion.fields.planDoneOn.label=計劃完成時間
i18n_entity.FbDevVersion.fields.ro.label=企業
i18n_entity.FbDevVersion.fields.version.label=版本
i18n_entity.FbDevVersion.group=開發
i18n_entity.FbDevVersion.label=迭代
i18n_entity.FbDirectPutawayOrder.fields.bin.label=上架庫位
i18n_entity.FbDirectPutawayOrder.fields.btInvProcessed.label=庫存已處理
i18n_entity.FbDirectPutawayOrder.fields.btLines.label=單行
i18n_entity.FbDirectPutawayOrder.fields.btOrderKind.inlineOptionBill.items.MfFinishedInbound.label=產成品入庫
i18n_entity.FbDirectPutawayOrder.fields.btOrderKind.inlineOptionBill.items.OtherInbound.label=其他入庫
i18n_entity.FbDirectPutawayOrder.fields.btOrderKind.inlineOptionBill.items.PurchaseInbound.label=採購入庫
i18n_entity.FbDirectPutawayOrder.fields.btOrderKind.label=類型
i18n_entity.FbDirectPutawayOrder.fields.btOrderState.inlineOptionBill.items.Init.label=未提交
i18n_entity.FbDirectPutawayOrder.fields.btOrderState.inlineOptionBill.items.Submitted.label=已提交
i18n_entity.FbDirectPutawayOrder.fields.btOrderState.label=狀態
i18n_entity.FbDirectPutawayOrder.fields.btOrderStateReason.label=狀態說明
i18n_entity.FbDirectPutawayOrder.fields.container.label=上架容器
i18n_entity.FbDirectPutawayOrder.fields.createdBy.label=創建人
i18n_entity.FbDirectPutawayOrder.fields.createdOn.label=創建時間
i18n_entity.FbDirectPutawayOrder.fields.id.label=單號
i18n_entity.FbDirectPutawayOrder.fields.modifiedBy.label=最後修改人
i18n_entity.FbDirectPutawayOrder.fields.modifiedOn.label=最後修改時間
i18n_entity.FbDirectPutawayOrder.fields.remark.label=備註
i18n_entity.FbDirectPutawayOrder.fields.ro.label=根組織
i18n_entity.FbDirectPutawayOrder.fields.version.label=版本
i18n_entity.FbDirectPutawayOrder.group=倉庫
i18n_entity.FbDirectPutawayOrder.label=人工上架單
i18n_entity.FbDirectPutawayOrderLine.fields.btLineNo.label=行號
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterial.label=物料
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialCategory.label=物料分類編號
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialCategoryName.label=物料分類名稱
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialId.label=物料編號
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialImage.label=物料圖片
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialModel.label=物料型號
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialName.label=物料名稱
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialSpec.label=物料規格
i18n_entity.FbDirectPutawayOrderLine.fields.btParentId.label=所屬上架單
i18n_entity.FbDirectPutawayOrderLine.fields.createdBy.label=創建人
i18n_entity.FbDirectPutawayOrderLine.fields.createdOn.label=創建時間
i18n_entity.FbDirectPutawayOrderLine.fields.id.label=ID
i18n_entity.FbDirectPutawayOrderLine.fields.lotNo.label=批次號
i18n_entity.FbDirectPutawayOrderLine.fields.modifiedBy.label=最後修改人
i18n_entity.FbDirectPutawayOrderLine.fields.modifiedOn.label=最後修改時間
i18n_entity.FbDirectPutawayOrderLine.fields.price.label=單價
i18n_entity.FbDirectPutawayOrderLine.fields.qty.label=上架數量
i18n_entity.FbDirectPutawayOrderLine.fields.qualityLevel.label=質量等級
i18n_entity.FbDirectPutawayOrderLine.fields.ro.label=根組織
i18n_entity.FbDirectPutawayOrderLine.fields.subContainerId.label=格號
i18n_entity.FbDirectPutawayOrderLine.fields.unitLabel.label=單位名稱
i18n_entity.FbDirectPutawayOrderLine.fields.version.label=版本
i18n_entity.FbDirectPutawayOrderLine.group=倉庫
i18n_entity.FbDirectPutawayOrderLine.label=人工上架單行
i18n_entity.FbDirectPutawayOrderLine.listCard.qty.prefix=數量: 
i18n_entity.FbDistrict.fields.btDisabled.label=停用
i18n_entity.FbDistrict.fields.createdBy.label=創建人
i18n_entity.FbDistrict.fields.createdOn.label=創建時間
i18n_entity.FbDistrict.fields.displayOrder.label=顯示順序
i18n_entity.FbDistrict.fields.id.label=庫區編號
i18n_entity.FbDistrict.fields.modifiedBy.label=最後修改人
i18n_entity.FbDistrict.fields.modifiedOn.label=最後修改時間
i18n_entity.FbDistrict.fields.name.label=庫區名稱
i18n_entity.FbDistrict.fields.remark.label=備註
i18n_entity.FbDistrict.fields.ro.label=根組織
i18n_entity.FbDistrict.fields.structure.label=庫區結構
i18n_entity.FbDistrict.fields.version.label=版本
i18n_entity.FbDistrict.fields.warehouse.label=所屬倉庫
i18n_entity.FbDistrict.group=主數據
i18n_entity.FbDistrict.label=庫區
i18n_entity.FbGoodsOwner.fields.address.label=地址
i18n_entity.FbGoodsOwner.fields.btDisabled.label=停用
i18n_entity.FbGoodsOwner.fields.contact.label=聯繫人
i18n_entity.FbGoodsOwner.fields.createdBy.label=創建人
i18n_entity.FbGoodsOwner.fields.createdOn.label=創建時間
i18n_entity.FbGoodsOwner.fields.id.label=貨主編碼
i18n_entity.FbGoodsOwner.fields.modifiedBy.label=最後修改人
i18n_entity.FbGoodsOwner.fields.modifiedOn.label=最後修改時間
i18n_entity.FbGoodsOwner.fields.name.label=貨主名稱
i18n_entity.FbGoodsOwner.fields.phone.label=聯繫電話
i18n_entity.FbGoodsOwner.fields.remark.label=備註
i18n_entity.FbGoodsOwner.fields.ro.label=根組織
i18n_entity.FbGoodsOwner.fields.version.label=版本
i18n_entity.FbGoodsOwner.group=主數據
i18n_entity.FbGoodsOwner.label=貨主
i18n_entity.FbInboundOrder.fields.asnId.label=到貨通知號
i18n_entity.FbInboundOrder.fields.btInvProcessed.label=庫存已處理
i18n_entity.FbInboundOrder.fields.btLines.label=單行
i18n_entity.FbInboundOrder.fields.btOrderKind.inlineOptionBill.items.MfFinishedInbound.label=產成品入庫
i18n_entity.FbInboundOrder.fields.btOrderKind.inlineOptionBill.items.OtherInbound.label=其他入庫
i18n_entity.FbInboundOrder.fields.btOrderKind.inlineOptionBill.items.PurchaseInbound.label=採購入庫
i18n_entity.FbInboundOrder.fields.btOrderKind.label=類型
i18n_entity.FbInboundOrder.fields.btOrderState.inlineOptionBill.items.Committed.label=已提交
i18n_entity.FbInboundOrder.fields.btOrderState.inlineOptionBill.items.Init.label=未提交
i18n_entity.FbInboundOrder.fields.btOrderState.label=狀態
i18n_entity.FbInboundOrder.fields.btOrderStateReason.label=狀態說明
i18n_entity.FbInboundOrder.fields.callContainerAll.label=已叫空容器
i18n_entity.FbInboundOrder.fields.createdBy.label=創建人
i18n_entity.FbInboundOrder.fields.createdOn.label=創建時間
i18n_entity.FbInboundOrder.fields.district.label=入庫庫區
i18n_entity.FbInboundOrder.fields.id.label=單號
i18n_entity.FbInboundOrder.fields.mfgFQCOrderId.label=成品質檢單號
i18n_entity.FbInboundOrder.fields.mfgOrderId.label=生產工單號
i18n_entity.FbInboundOrder.fields.modifiedBy.label=最後修改人
i18n_entity.FbInboundOrder.fields.modifiedOn.label=最後修改時間
i18n_entity.FbInboundOrder.fields.planQty.label=收貨數量
i18n_entity.FbInboundOrder.fields.purchaseOrderId.label=採購訂單號
i18n_entity.FbInboundOrder.fields.qty.label=本次入庫數量
i18n_entity.FbInboundOrder.fields.recevingOrderId.label=收貨單號
i18n_entity.FbInboundOrder.fields.remark.label=備註
i18n_entity.FbInboundOrder.fields.ro.label=根組織
i18n_entity.FbInboundOrder.fields.vendor.label=供應商
i18n_entity.FbInboundOrder.fields.version.label=版本
i18n_entity.FbInboundOrder.fields.warehouse.label=入庫倉庫
i18n_entity.FbInboundOrder.group=倉庫
i18n_entity.FbInboundOrder.label=入庫單
i18n_entity.FbInboundOrderLine.fields.bin.label=存儲庫位
i18n_entity.FbInboundOrderLine.fields.btLineNo.label=行號
i18n_entity.FbInboundOrderLine.fields.btMaterial.label=物料
i18n_entity.FbInboundOrderLine.fields.btMaterialCategory.label=物料分類編號
i18n_entity.FbInboundOrderLine.fields.btMaterialCategoryName.label=物料分類名稱
i18n_entity.FbInboundOrderLine.fields.btMaterialId.label=物料編號
i18n_entity.FbInboundOrderLine.fields.btMaterialImage.label=物料圖片
i18n_entity.FbInboundOrderLine.fields.btMaterialModel.label=物料型號
i18n_entity.FbInboundOrderLine.fields.btMaterialName.label=物料名稱
i18n_entity.FbInboundOrderLine.fields.btMaterialSpec.label=物料規格
i18n_entity.FbInboundOrderLine.fields.btParentId.label=所屬入庫單
i18n_entity.FbInboundOrderLine.fields.callContainerQty.label=已分配容器數量
i18n_entity.FbInboundOrderLine.fields.createdBy.label=創建人
i18n_entity.FbInboundOrderLine.fields.createdOn.label=創建時間
i18n_entity.FbInboundOrderLine.fields.finishedQty.label=之前入庫數量
i18n_entity.FbInboundOrderLine.fields.id.label=ID
i18n_entity.FbInboundOrderLine.fields.lotNo.label=批次號
i18n_entity.FbInboundOrderLine.fields.materialName.label=物料名稱
i18n_entity.FbInboundOrderLine.fields.modifiedBy.label=最後修改人
i18n_entity.FbInboundOrderLine.fields.modifiedOn.label=最後修改時間
i18n_entity.FbInboundOrderLine.fields.planQty.label=收貨數量
i18n_entity.FbInboundOrderLine.fields.price.label=單價
i18n_entity.FbInboundOrderLine.fields.qty.label=入庫數量
i18n_entity.FbInboundOrderLine.fields.qualityLevel.label=質量等級
i18n_entity.FbInboundOrderLine.fields.ro.label=根組織
i18n_entity.FbInboundOrderLine.fields.storeQty.label=上架數量
i18n_entity.FbInboundOrderLine.fields.unitLabel.label=單位名稱
i18n_entity.FbInboundOrderLine.fields.version.label=版本
i18n_entity.FbInboundOrderLine.group=倉庫
i18n_entity.FbInboundOrderLine.label=入庫單行
i18n_entity.FbInboundOrderLine.listCard.qty.prefix=數量: 
i18n_entity.FbInvLayout.fields.amount.label=金額
i18n_entity.FbInvLayout.fields.bin.label=所在庫位
i18n_entity.FbInvLayout.fields.btMaterial.label=物料
i18n_entity.FbInvLayout.fields.btMaterialCategory.label=物料分類編號
i18n_entity.FbInvLayout.fields.btMaterialCategoryName.label=物料分類名稱
i18n_entity.FbInvLayout.fields.btMaterialId.label=物料編號
i18n_entity.FbInvLayout.fields.btMaterialImage.label=物料圖片
i18n_entity.FbInvLayout.fields.btMaterialModel.label=物料型號
i18n_entity.FbInvLayout.fields.btMaterialName.label=物料名稱
i18n_entity.FbInvLayout.fields.btMaterialSpec.label=物料規格
i18n_entity.FbInvLayout.fields.column.label=列
i18n_entity.FbInvLayout.fields.createdBy.label=創建人
i18n_entity.FbInvLayout.fields.createdOn.label=創建時間
i18n_entity.FbInvLayout.fields.depth.label=深
i18n_entity.FbInvLayout.fields.district.label=所在庫區
i18n_entity.FbInvLayout.fields.expDate.label=有效期
i18n_entity.FbInvLayout.fields.id.label=ID
i18n_entity.FbInvLayout.fields.inboundOn.label=入庫時間
i18n_entity.FbInvLayout.fields.inboundOrderId.label=入庫單號
i18n_entity.FbInvLayout.fields.inboundOrderLineNo.label=入庫單行號
i18n_entity.FbInvLayout.fields.layer.label=層
i18n_entity.FbInvLayout.fields.leafContainer.label=最內層容器
i18n_entity.FbInvLayout.fields.locked.label=鎖定
i18n_entity.FbInvLayout.fields.locked.listBatchButtons.buttons[0].label=解鎖
i18n_entity.FbInvLayout.fields.lotNo.label=批次號
i18n_entity.FbInvLayout.fields.matLotNo.label=批次
i18n_entity.FbInvLayout.fields.matSerialNo.label=序列號
i18n_entity.FbInvLayout.fields.mfgDate.label=生產日期
i18n_entity.FbInvLayout.fields.modifiedBy.label=最後修改人
i18n_entity.FbInvLayout.fields.modifiedOn.label=最後修改時間
i18n_entity.FbInvLayout.fields.onRobot.label=所在機器人
i18n_entity.FbInvLayout.fields.outboundOrderId.label=出庫單號
i18n_entity.FbInvLayout.fields.outboundOrderLineNo.label=出庫單行號
i18n_entity.FbInvLayout.fields.owner.label=貨主
i18n_entity.FbInvLayout.fields.price.label=單價
i18n_entity.FbInvLayout.fields.qty.label=數量
i18n_entity.FbInvLayout.fields.refInv.label=引用庫存明細
i18n_entity.FbInvLayout.fields.row.label=排
i18n_entity.FbInvLayout.fields.state.inlineOptionBill.items.Assigned.label=已分配
i18n_entity.FbInvLayout.fields.state.inlineOptionBill.items.Received.label=已收貨
i18n_entity.FbInvLayout.fields.state.inlineOptionBill.items.Storing.label=存儲中
i18n_entity.FbInvLayout.fields.state.label=庫存狀態
i18n_entity.FbInvLayout.fields.subContainerId.label=格子
i18n_entity.FbInvLayout.fields.topContainer.label=最外層容器
i18n_entity.FbInvLayout.fields.usedQty.label=已分配數量
i18n_entity.FbInvLayout.fields.vendor.label=供應商
i18n_entity.FbInvLayout.fields.version.label=修訂版本
i18n_entity.FbInvLayout.fields.warehouse.label=所在倉庫
i18n_entity.FbInvLayout.group=倉庫
i18n_entity.FbInvLayout.label=庫存明細
i18n_entity.FbMaterial.fields.abc.label=ABC 分類
i18n_entity.FbMaterial.fields.btDisabled.label=停用
i18n_entity.FbMaterial.fields.categoriesDesc.label=物料分類描述
i18n_entity.FbMaterial.fields.category1.label=一級分類
i18n_entity.FbMaterial.fields.category2.label=二級分類
i18n_entity.FbMaterial.fields.category3.label=三級分類
i18n_entity.FbMaterial.fields.createdBy.label=創建人
i18n_entity.FbMaterial.fields.createdOn.label=創建時間
i18n_entity.FbMaterial.fields.displayDecimals.label=小數點位數
i18n_entity.FbMaterial.fields.endOn.label=停用日期
i18n_entity.FbMaterial.fields.height.label=高度
i18n_entity.FbMaterial.fields.id.label=物料編碼
i18n_entity.FbMaterial.fields.image.label=圖片
i18n_entity.FbMaterial.fields.leafCategory.label=物料分類
i18n_entity.FbMaterial.fields.length.label=長度
i18n_entity.FbMaterial.fields.mainUnit.label=主計量單位名稱
i18n_entity.FbMaterial.fields.mainVendor.label=主供應商
i18n_entity.FbMaterial.fields.mixLotNo.label=批次混放
i18n_entity.FbMaterial.fields.mixMaterial.label=物料混放
i18n_entity.FbMaterial.fields.model.label=型號
i18n_entity.FbMaterial.fields.modifiedBy.label=最後修改人
i18n_entity.FbMaterial.fields.modifiedOn.label=最後修改時間
i18n_entity.FbMaterial.fields.name.label=物料名稱
i18n_entity.FbMaterial.fields.owner.label=貨主
i18n_entity.FbMaterial.fields.price.label=單價
i18n_entity.FbMaterial.fields.remark.label=備註
i18n_entity.FbMaterial.fields.ro.label=根組織
i18n_entity.FbMaterial.fields.spec.label=物料規格
i18n_entity.FbMaterial.fields.startOn.label=啓用日期
i18n_entity.FbMaterial.fields.syncOut.label=外部導入
i18n_entity.FbMaterial.fields.topCat.label=存貨大類名稱
i18n_entity.FbMaterial.fields.unit.label=物料單位
i18n_entity.FbMaterial.fields.unitLabel.label=單位名稱
i18n_entity.FbMaterial.fields.version.label=版本
i18n_entity.FbMaterial.fields.volume.label=容積
i18n_entity.FbMaterial.fields.weight.label=重量
i18n_entity.FbMaterial.fields.width.label=寬度
i18n_entity.FbMaterial.group=主數據
i18n_entity.FbMaterial.label=物料
i18n_entity.FbMaterialCategory.fields.btDisabled.label=停用
i18n_entity.FbMaterialCategory.fields.createdBy.label=創建人
i18n_entity.FbMaterialCategory.fields.createdOn.label=創建時間
i18n_entity.FbMaterialCategory.fields.id.label=分類編號
i18n_entity.FbMaterialCategory.fields.modifiedBy.label=最後修改人
i18n_entity.FbMaterialCategory.fields.modifiedOn.label=最後修改時間
i18n_entity.FbMaterialCategory.fields.name.label=名稱
i18n_entity.FbMaterialCategory.fields.parent.label=上級分類
i18n_entity.FbMaterialCategory.fields.remark.label=備註
i18n_entity.FbMaterialCategory.fields.storeDistricts.label=存儲庫區
i18n_entity.FbMaterialCategory.fields.version.label=修訂版本
i18n_entity.FbMaterialCategory.group=主數據
i18n_entity.FbMaterialCategory.label=物料分類
i18n_entity.FbMaterialContainerMaxQty.fields.btDisabled.label=停用
i18n_entity.FbMaterialContainerMaxQty.fields.btMaterial.label=物料
i18n_entity.FbMaterialContainerMaxQty.fields.containerType.label=容器類型
i18n_entity.FbMaterialContainerMaxQty.fields.createdBy.label=創建人
i18n_entity.FbMaterialContainerMaxQty.fields.createdOn.label=創建時間
i18n_entity.FbMaterialContainerMaxQty.fields.id.label=ID
i18n_entity.FbMaterialContainerMaxQty.fields.maxQty.label=最多放幾個
i18n_entity.FbMaterialContainerMaxQty.fields.modifiedBy.label=最後修改人
i18n_entity.FbMaterialContainerMaxQty.fields.modifiedOn.label=最後修改時間
i18n_entity.FbMaterialContainerMaxQty.fields.version.label=修訂版本
i18n_entity.FbMaterialContainerMaxQty.group=主數據
i18n_entity.FbMaterialContainerMaxQty.label=物料容器容量
i18n_entity.FbMaterialLot.fields.createdBy.label=創建人
i18n_entity.FbMaterialLot.fields.createdOn.label=創建時間
i18n_entity.FbMaterialLot.fields.disabled.label=禁用
i18n_entity.FbMaterialLot.fields.id.label=生產批號
i18n_entity.FbMaterialLot.fields.material.label=所屬物料
i18n_entity.FbMaterialLot.fields.modifiedBy.label=最後修改人
i18n_entity.FbMaterialLot.fields.modifiedOn.label=最後修改時間
i18n_entity.FbMaterialLot.fields.name.label=生產批名稱
i18n_entity.FbMaterialLot.fields.remark.label=備註
i18n_entity.FbMaterialLot.fields.ro.label=根組織
i18n_entity.FbMaterialLot.fields.version.label=版本
i18n_entity.FbMaterialLot.group=主數據
i18n_entity.FbMaterialLot.label=物料批次
i18n_entity.FbMaterialUnit.fields.basic.label=基本單位
i18n_entity.FbMaterialUnit.fields.createdBy.label=創建人
i18n_entity.FbMaterialUnit.fields.createdOn.label=創建時間
i18n_entity.FbMaterialUnit.fields.disabled.label=禁用
i18n_entity.FbMaterialUnit.fields.id.label=單位編碼
i18n_entity.FbMaterialUnit.fields.modifiedBy.label=最後修改人
i18n_entity.FbMaterialUnit.fields.modifiedOn.label=最後修改時間
i18n_entity.FbMaterialUnit.fields.name.label=單位名稱
i18n_entity.FbMaterialUnit.fields.parent.label=父級單位
i18n_entity.FbMaterialUnit.fields.ratio.label=換算關係
i18n_entity.FbMaterialUnit.fields.remark.label=備註
i18n_entity.FbMaterialUnit.fields.ro.label=根組織
i18n_entity.FbMaterialUnit.fields.version.label=版本
i18n_entity.FbMaterialUnit.group=主數據
i18n_entity.FbMaterialUnit.label=物料單位
i18n_entity.FbOutboundOrder.fields.btInvProcessed.label=庫存已處理
i18n_entity.FbOutboundOrder.fields.btLines.label=單行
i18n_entity.FbOutboundOrder.fields.btOrderKind.inlineOptionBill.items.Product.label=商品出庫
i18n_entity.FbOutboundOrder.fields.btOrderKind.label=類型
i18n_entity.FbOutboundOrder.fields.btOrderState.inlineOptionBill.items.Committed.label=已提交
i18n_entity.FbOutboundOrder.fields.btOrderState.inlineOptionBill.items.Init.label=未提交
i18n_entity.FbOutboundOrder.fields.btOrderState.label=狀態
i18n_entity.FbOutboundOrder.fields.btOrderStateReason.label=狀態說明
i18n_entity.FbOutboundOrder.fields.createdBy.label=創建人
i18n_entity.FbOutboundOrder.fields.createdOn.label=創建時間
i18n_entity.FbOutboundOrder.fields.customer.label=客戶
i18n_entity.FbOutboundOrder.fields.direct.label=直接出庫
i18n_entity.FbOutboundOrder.fields.district.label=出庫庫區
i18n_entity.FbOutboundOrder.fields.id.label=單號
i18n_entity.FbOutboundOrder.fields.invAssignedAll.label=庫存分配完成
i18n_entity.FbOutboundOrder.fields.modifiedBy.label=最後修改人
i18n_entity.FbOutboundOrder.fields.modifiedOn.label=最後修改時間
i18n_entity.FbOutboundOrder.fields.planQty.label=計劃出庫數量
i18n_entity.FbOutboundOrder.fields.priority.label=優先級
i18n_entity.FbOutboundOrder.fields.qty.label=本次出庫數量
i18n_entity.FbOutboundOrder.fields.receiver.label=收貨單位
i18n_entity.FbOutboundOrder.fields.remark.label=備註
i18n_entity.FbOutboundOrder.fields.ro.label=根組織
i18n_entity.FbOutboundOrder.fields.saleOrderId.label=銷售訂單號
i18n_entity.FbOutboundOrder.fields.saleShipOrderId.label=銷售發貨單
i18n_entity.FbOutboundOrder.fields.version.label=版本
i18n_entity.FbOutboundOrder.fields.warehouse.label=出庫倉庫
i18n_entity.FbOutboundOrder.group=倉庫
i18n_entity.FbOutboundOrder.label=出庫單
i18n_entity.FbOutboundOrderLine.fields.btInvRefAll.label=總庫存
i18n_entity.FbOutboundOrderLine.fields.btInvRefMatch.label=可用庫存
i18n_entity.FbOutboundOrderLine.fields.btLineNo.label=行號
i18n_entity.FbOutboundOrderLine.fields.btMaterial.label=物料
i18n_entity.FbOutboundOrderLine.fields.btMaterialCategory.label=物料分類編號
i18n_entity.FbOutboundOrderLine.fields.btMaterialCategoryName.label=物料分類名稱
i18n_entity.FbOutboundOrderLine.fields.btMaterialId.label=物料編號
i18n_entity.FbOutboundOrderLine.fields.btMaterialImage.label=物料圖片
i18n_entity.FbOutboundOrderLine.fields.btMaterialModel.label=物料型號
i18n_entity.FbOutboundOrderLine.fields.btMaterialName.label=物料名稱
i18n_entity.FbOutboundOrderLine.fields.btMaterialSpec.label=物料規格
i18n_entity.FbOutboundOrderLine.fields.btParentId.label=所屬出庫單
i18n_entity.FbOutboundOrderLine.fields.createdBy.label=創建人
i18n_entity.FbOutboundOrderLine.fields.createdOn.label=創建時間
i18n_entity.FbOutboundOrderLine.fields.finishedQty.label=之前出庫數量
i18n_entity.FbOutboundOrderLine.fields.id.label=ID
i18n_entity.FbOutboundOrderLine.fields.invAssignedQty.label=已分配庫存數量
i18n_entity.FbOutboundOrderLine.fields.layoutId.label=引用庫存明細
i18n_entity.FbOutboundOrderLine.fields.lotNo.label=批次號
i18n_entity.FbOutboundOrderLine.fields.materialName.label=物料名稱
i18n_entity.FbOutboundOrderLine.fields.modifiedBy.label=最後修改人
i18n_entity.FbOutboundOrderLine.fields.modifiedOn.label=最後修改時間
i18n_entity.FbOutboundOrderLine.fields.planQty.label=計劃出庫總數
i18n_entity.FbOutboundOrderLine.fields.qty.label=出庫數量
i18n_entity.FbOutboundOrderLine.fields.qualityLevel.label=質量等級
i18n_entity.FbOutboundOrderLine.fields.ro.label=根組織
i18n_entity.FbOutboundOrderLine.fields.version.label=版本
i18n_entity.FbOutboundOrderLine.group=倉庫
i18n_entity.FbOutboundOrderLine.label=出庫單行
i18n_entity.FbPkg.fields.createdBy.label=創建人
i18n_entity.FbPkg.fields.createdOn.label=創建時間
i18n_entity.FbPkg.fields.disabled.label=禁用
i18n_entity.FbPkg.fields.id.label=包裝代碼
i18n_entity.FbPkg.fields.material.label=所屬物料
i18n_entity.FbPkg.fields.modifiedBy.label=最後修改人
i18n_entity.FbPkg.fields.modifiedOn.label=最後修改時間
i18n_entity.FbPkg.fields.name.label=包裝名稱
i18n_entity.FbPkg.fields.purpose.label=包裝用途
i18n_entity.FbPkg.fields.qty.label=包內數量
i18n_entity.FbPkg.fields.remark.label=備註
i18n_entity.FbPkg.fields.ro.label=根組織
i18n_entity.FbPkg.fields.version.label=版本
i18n_entity.FbPkg.group=主數據
i18n_entity.FbPkg.label=包裝規格
i18n_entity.FbVendor.fields.address.label=地址
i18n_entity.FbVendor.fields.btDisabled.label=停用
i18n_entity.FbVendor.fields.contact.label=聯繫人
i18n_entity.FbVendor.fields.createdBy.label=創建人
i18n_entity.FbVendor.fields.createdOn.label=創建時間
i18n_entity.FbVendor.fields.email.label=電子郵箱
i18n_entity.FbVendor.fields.id.label=供應商編號
i18n_entity.FbVendor.fields.level.label=級別
i18n_entity.FbVendor.fields.modifiedBy.label=最後修改人
i18n_entity.FbVendor.fields.modifiedOn.label=最後修改時間
i18n_entity.FbVendor.fields.name.label=供應商名稱
i18n_entity.FbVendor.fields.phone.label=聯繫電話
i18n_entity.FbVendor.fields.remark.label=備註
i18n_entity.FbVendor.fields.ro.label=根組織
i18n_entity.FbVendor.fields.version.label=版本
i18n_entity.FbVendor.group=主數據
i18n_entity.FbVendor.label=供應商
i18n_entity.FbWarehouse.fields.address.label=地址
i18n_entity.FbWarehouse.fields.btDisabled.label=停用
i18n_entity.FbWarehouse.fields.contact.label=聯繫人
i18n_entity.FbWarehouse.fields.createdBy.label=創建人
i18n_entity.FbWarehouse.fields.createdOn.label=創建時間
i18n_entity.FbWarehouse.fields.defaultBin.label=默認庫位
i18n_entity.FbWarehouse.fields.displayOrder.label=顯示順序
i18n_entity.FbWarehouse.fields.id.label=倉庫編號
i18n_entity.FbWarehouse.fields.latitude.label=位置緯度
i18n_entity.FbWarehouse.fields.longitude.label=位置經度
i18n_entity.FbWarehouse.fields.modifiedBy.label=最後修改人
i18n_entity.FbWarehouse.fields.modifiedOn.label=最後修改時間
i18n_entity.FbWarehouse.fields.name.label=倉庫名稱
i18n_entity.FbWarehouse.fields.phone.label=聯繫電話
i18n_entity.FbWarehouse.fields.remark.label=備註
i18n_entity.FbWarehouse.fields.ro.label=根組織
i18n_entity.FbWarehouse.fields.version.label=版本
i18n_entity.FbWarehouse.fields.volume.label=容積
i18n_entity.FbWarehouse.group=主數據
i18n_entity.FbWarehouse.label=倉庫
i18n_entity.FbWarehouse.listCard.btDisabled.formatMapping[0].replaceText=已停用
i18n_entity.FbWorkPosition.fields.createdBy.label=創建人
i18n_entity.FbWorkPosition.fields.createdOn.label=創建時間
i18n_entity.FbWorkPosition.fields.disabled.label=禁用
i18n_entity.FbWorkPosition.fields.id.label=單號
i18n_entity.FbWorkPosition.fields.modifiedBy.label=最後修改人
i18n_entity.FbWorkPosition.fields.modifiedOn.label=最後修改時間
i18n_entity.FbWorkPosition.fields.name.label=名稱
i18n_entity.FbWorkPosition.fields.remark.label=備註
i18n_entity.FbWorkPosition.fields.ro.label=根組織
i18n_entity.FbWorkPosition.fields.version.label=版本
i18n_entity.FbWorkPosition.group=主數據
i18n_entity.FbWorkPosition.label=崗位
i18n_entity.FbWorkSite.fields.bin.label=所屬庫位
i18n_entity.FbWorkSite.fields.createdBy.label=創建人
i18n_entity.FbWorkSite.fields.createdOn.label=創建時間
i18n_entity.FbWorkSite.fields.disabled.label=禁用
i18n_entity.FbWorkSite.fields.id.label=工位編碼
i18n_entity.FbWorkSite.fields.kind.label=工位類型
i18n_entity.FbWorkSite.fields.line.label=所屬產線
i18n_entity.FbWorkSite.fields.modifiedBy.label=最後修改人
i18n_entity.FbWorkSite.fields.modifiedOn.label=最後修改時間
i18n_entity.FbWorkSite.fields.name.label=工位名稱
i18n_entity.FbWorkSite.fields.position.label=所屬崗位
i18n_entity.FbWorkSite.fields.remark.label=備註
i18n_entity.FbWorkSite.fields.ro.label=根組織
i18n_entity.FbWorkSite.fields.version.label=版本
i18n_entity.FbWorkSite.group=主數據
i18n_entity.FbWorkSite.label=工位
i18n_entity.HaiMockRobot.fields.battery.label=電量
i18n_entity.HaiMockRobot.fields.createdBy.label=創建人
i18n_entity.HaiMockRobot.fields.createdOn.label=創建時間
i18n_entity.HaiMockRobot.fields.id.label=ID
i18n_entity.HaiMockRobot.fields.modifiedBy.label=最後修改人
i18n_entity.HaiMockRobot.fields.modifiedOn.label=最後修改時間
i18n_entity.HaiMockRobot.fields.posX.label=位置 X
i18n_entity.HaiMockRobot.fields.posY.label=位置 Y
i18n_entity.HaiMockRobot.fields.version.label=修訂版本
i18n_entity.HaiMockRobot.group=WCS
i18n_entity.HaiMockRobot.label=海柔仿真機器人
i18n_entity.HikResourcePack.fields.active.label=主配置
i18n_entity.HikResourcePack.fields.createdBy.label=創建人
i18n_entity.HikResourcePack.fields.createdOn.label=創建時間
i18n_entity.HikResourcePack.fields.id.label=ID
i18n_entity.HikResourcePack.fields.lmap.label=激光點雲文件 lmap
i18n_entity.HikResourcePack.fields.modifiedBy.label=最後修改人
i18n_entity.HikResourcePack.fields.modifiedOn.label=最後修改時間
i18n_entity.HikResourcePack.fields.podConfig.label=貨架配置 XML
i18n_entity.HikResourcePack.fields.remark.label=備註
i18n_entity.HikResourcePack.fields.version.label=修訂版本
i18n_entity.HikResourcePack.group=WCS
i18n_entity.HikResourcePack.label=海康資源包
i18n_entity.HumanUser.fields.btDisabled.label=停用
i18n_entity.HumanUser.fields.company.label=公司
i18n_entity.HumanUser.fields.createdBy.label=創建人
i18n_entity.HumanUser.fields.createdOn.label=創建時間
i18n_entity.HumanUser.fields.directSignInDisabled.label=禁止直接登錄
i18n_entity.HumanUser.fields.disabled.label=禁用
i18n_entity.HumanUser.fields.email.label=郵件
i18n_entity.HumanUser.fields.externalAdded.label=外部添加
i18n_entity.HumanUser.fields.externalSource.label=外部來源
i18n_entity.HumanUser.fields.externalUserId.label=外部用戶 ID
i18n_entity.HumanUser.fields.id.label=編號
i18n_entity.HumanUser.fields.modifiedBy.label=最後修改人
i18n_entity.HumanUser.fields.modifiedOn.label=最後修改時間
i18n_entity.HumanUser.fields.password.label=密碼
i18n_entity.HumanUser.fields.phone.label=手機
i18n_entity.HumanUser.fields.ro.label=根組織
i18n_entity.HumanUser.fields.roAdmin.label=企業管理員
i18n_entity.HumanUser.fields.roleIds.label=角色
i18n_entity.HumanUser.fields.truename.label=真實姓名
i18n_entity.HumanUser.fields.username.label=用戶名
i18n_entity.HumanUser.fields.version.label=版本
i18n_entity.HumanUser.group=用戶
i18n_entity.HumanUser.label=用戶
i18n_entity.HumanUserSession.fields.createdBy.label=創建人
i18n_entity.HumanUserSession.fields.createdOn.label=創建時間
i18n_entity.HumanUserSession.fields.expiredAt.label=過期時間
i18n_entity.HumanUserSession.fields.id.label=ID
i18n_entity.HumanUserSession.fields.modifiedBy.label=最後修改人
i18n_entity.HumanUserSession.fields.modifiedOn.label=最後修改時間
i18n_entity.HumanUserSession.fields.ro.label=RO
i18n_entity.HumanUserSession.fields.userId.label=用戶
i18n_entity.HumanUserSession.fields.userToken.label=令牌
i18n_entity.HumanUserSession.fields.version.label=修訂版本
i18n_entity.HumanUserSession.group=核心
i18n_entity.HumanUserSession.label=用戶會話
i18n_entity.IdGen.fields.createdBy.label=創建人
i18n_entity.IdGen.fields.createdOn.label=創建時間
i18n_entity.IdGen.fields.flowNo.label=流水號
i18n_entity.IdGen.fields.id.label=編號
i18n_entity.IdGen.fields.key.label=組
i18n_entity.IdGen.fields.modifiedBy.label=最後修改人
i18n_entity.IdGen.fields.modifiedOn.label=最後修改時間
i18n_entity.IdGen.fields.ro.label=根組織
i18n_entity.IdGen.fields.timestamp.label=日期
i18n_entity.IdGen.fields.version.label=版本
i18n_entity.IdGen.group=核心
i18n_entity.IdGen.label=編號規則
i18n_entity.LePage.fields.content.label=內容
i18n_entity.LePage.fields.createdBy.label=創建人
i18n_entity.LePage.fields.createdOn.label=創建時間
i18n_entity.LePage.fields.id.label=ID
i18n_entity.LePage.fields.label.label=顯示名
i18n_entity.LePage.fields.modifiedBy.label=最後修改人
i18n_entity.LePage.fields.modifiedOn.label=最後修改時間
i18n_entity.LePage.fields.name.label=頁面名
i18n_entity.LePage.fields.version.label=修訂版本
i18n_entity.LePage.group=核心
i18n_entity.LePage.label=定製界面
i18n_entity.ListFilterCase.fields.content.label=內容
i18n_entity.ListFilterCase.fields.createdBy.label=創建人
i18n_entity.ListFilterCase.fields.createdOn.label=創建時間
i18n_entity.ListFilterCase.fields.global.label=全局
i18n_entity.ListFilterCase.fields.id.label=編號
i18n_entity.ListFilterCase.fields.modifiedBy.label=最後修改人
i18n_entity.ListFilterCase.fields.modifiedOn.label=最後修改時間
i18n_entity.ListFilterCase.fields.owner.label=所有者
i18n_entity.ListFilterCase.fields.page.label=頁面
i18n_entity.ListFilterCase.fields.ro.label=根組織
i18n_entity.ListFilterCase.fields.version.label=版本
i18n_entity.ListFilterCase.group=核心
i18n_entity.ListFilterCase.label=列表查詢方案
i18n_entity.MockSeerRobot.fields.createdBy.label=創建人
i18n_entity.MockSeerRobot.fields.createdOn.label=創建時間
i18n_entity.MockSeerRobot.fields.currentStation.label=當前站點
i18n_entity.MockSeerRobot.fields.id.label=ID
i18n_entity.MockSeerRobot.fields.modifiedBy.label=最後修改人
i18n_entity.MockSeerRobot.fields.modifiedOn.label=最後修改時間
i18n_entity.MockSeerRobot.fields.staringStation.label=出生站點
i18n_entity.MockSeerRobot.fields.version.label=修訂版本
i18n_entity.MockSeerRobot.group=WCS
i18n_entity.MockSeerRobot.label=仿真仙工機器人
i18n_entity.MrRobotRuntimeRecord.fields.channel.label=所在巷道
i18n_entity.MrRobotRuntimeRecord.fields.createdBy.label=創建人
i18n_entity.MrRobotRuntimeRecord.fields.createdOn.label=創建時間
i18n_entity.MrRobotRuntimeRecord.fields.ctOrders.label=容器搬運單
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Charging.label=充電中
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Idle.label=空閒
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Inbound.label=入庫中
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Outbound.label=出庫中
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Parking.label=去停靠
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Tasking.label=執行運單
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.label=擴展任務狀態
i18n_entity.MrRobotRuntimeRecord.fields.id.label=ID
i18n_entity.MrRobotRuntimeRecord.fields.modifiedBy.label=最後修改人
i18n_entity.MrRobotRuntimeRecord.fields.modifiedOn.label=修改時間
i18n_entity.MrRobotRuntimeRecord.fields.port.label=所在庫口
i18n_entity.MrRobotRuntimeRecord.fields.rtBins.label=實時調度載貨情況
i18n_entity.MrRobotRuntimeRecord.fields.rtCmdStatus.label=實時調度執行狀態
i18n_entity.MrRobotRuntimeRecord.fields.rtCurrentOrder.label=實時調度當前運單
i18n_entity.MrRobotRuntimeRecord.fields.rtCurrentStep.label=實時調度當前運單步驟
i18n_entity.MrRobotRuntimeRecord.fields.rtOrders.label=實時調度運單列表
i18n_entity.MrRobotRuntimeRecord.fields.taskStatus.inlineOptionBill.items.Charging.label=充電中
i18n_entity.MrRobotRuntimeRecord.fields.taskStatus.inlineOptionBill.items.Idle.label=空閒
i18n_entity.MrRobotRuntimeRecord.fields.taskStatus.inlineOptionBill.items.Tasking.label=任務中
i18n_entity.MrRobotRuntimeRecord.fields.taskStatus.label=任務狀態
i18n_entity.MrRobotRuntimeRecord.fields.version.label=修訂版本
i18n_entity.MrRobotRuntimeRecord.group=WCS
i18n_entity.MrRobotRuntimeRecord.label=移動機器人運行記錄
i18n_entity.MrRobotSystemConfig.fields.category.label=類別
i18n_entity.MrRobotSystemConfig.fields.connectionType.inlineOptionBill.items.GwWs.label=網關連接服務器（GW 信道）
i18n_entity.MrRobotSystemConfig.fields.connectionType.inlineOptionBill.items.GwWsLight.label=網關連接服務器（光通訊）
i18n_entity.MrRobotSystemConfig.fields.connectionType.inlineOptionBill.items.Mock.label=仿真
i18n_entity.MrRobotSystemConfig.fields.connectionType.inlineOptionBill.items.Rbk.label=服務器連接機器人
i18n_entity.MrRobotSystemConfig.fields.connectionType.label=連接類型
i18n_entity.MrRobotSystemConfig.fields.createdBy.label=創建人
i18n_entity.MrRobotSystemConfig.fields.createdOn.label=創建時間
i18n_entity.MrRobotSystemConfig.fields.disabled.label=停用
i18n_entity.MrRobotSystemConfig.fields.gwAuthId.label=網關登錄ID
i18n_entity.MrRobotSystemConfig.fields.gwAuthSecret.label=網關登錄密鑰
i18n_entity.MrRobotSystemConfig.fields.id.label=ID
i18n_entity.MrRobotSystemConfig.fields.image.label=圖片
i18n_entity.MrRobotSystemConfig.fields.modifiedBy.label=最後修改人
i18n_entity.MrRobotSystemConfig.fields.modifiedOn.label=最後修改時間
i18n_entity.MrRobotSystemConfig.fields.offDuty.label=不接單
i18n_entity.MrRobotSystemConfig.fields.robotHost.label=機器人地址
i18n_entity.MrRobotSystemConfig.fields.selfBinNum.label=最大載貨數
i18n_entity.MrRobotSystemConfig.fields.ssl.label=SSL/TSL 加密
i18n_entity.MrRobotSystemConfig.fields.tags.label=標籤
i18n_entity.MrRobotSystemConfig.fields.taskMode.inlineOptionBill.items.Custom.label=定製調度
i18n_entity.MrRobotSystemConfig.fields.taskMode.inlineOptionBill.items.SingleRobot.label=M4A1
i18n_entity.MrRobotSystemConfig.fields.taskMode.inlineOptionBill.items.Tom.label=核心
i18n_entity.MrRobotSystemConfig.fields.taskMode.label=任務模式
i18n_entity.MrRobotSystemConfig.fields.tomId.label=仙工調度 ID
i18n_entity.MrRobotSystemConfig.fields.vendor.inlineOptionBill.items.Hai.label=海柔
i18n_entity.MrRobotSystemConfig.fields.vendor.inlineOptionBill.items.Hik.label=海康
i18n_entity.MrRobotSystemConfig.fields.vendor.inlineOptionBill.items.Seer.label=仙工
i18n_entity.MrRobotSystemConfig.fields.vendor.label=廠商
i18n_entity.MrRobotSystemConfig.fields.version.label=修訂版本
i18n_entity.MrRobotSystemConfig.group=WCS
i18n_entity.MrRobotSystemConfig.label=移動機器人系統配置
i18n_entity.OrderFlowRecord.fields.createdBy.label=創建人
i18n_entity.OrderFlowRecord.fields.createdOn.label=創建時間
i18n_entity.OrderFlowRecord.fields.id.label=ID
i18n_entity.OrderFlowRecord.fields.modifiedBy.label=最後修改人
i18n_entity.OrderFlowRecord.fields.modifiedOn.label=最後修改時間
i18n_entity.OrderFlowRecord.fields.pushType.label=類型
i18n_entity.OrderFlowRecord.fields.sourceOrderId.label=源單號
i18n_entity.OrderFlowRecord.fields.sourceOrderName.label=源單名
i18n_entity.OrderFlowRecord.fields.targetOrderId.label=新單號
i18n_entity.OrderFlowRecord.fields.targetOrderName.label=新單名
i18n_entity.OrderFlowRecord.fields.targetOrderType.label=新單類型
i18n_entity.OrderFlowRecord.fields.txId.label=事務ID
i18n_entity.OrderFlowRecord.fields.version.label=修訂版本
i18n_entity.OrderFlowRecord.group=核心
i18n_entity.OrderFlowRecord.label=單據流轉記錄
i18n_entity.PickOrder.fields.allUsed.label=整託分揀
i18n_entity.PickOrder.fields.btLines.label=單行
i18n_entity.PickOrder.fields.btOrderKind.inlineOptionBill.items.NormalTask.label=裝貨任務
i18n_entity.PickOrder.fields.btOrderKind.label=類型
i18n_entity.PickOrder.fields.btOrderState.inlineOptionBill.items.Done.label=完成分揀
i18n_entity.PickOrder.fields.btOrderState.inlineOptionBill.items.Todo.label=等待分揀
i18n_entity.PickOrder.fields.btOrderState.label=狀態
i18n_entity.PickOrder.fields.btOrderStateReason.label=狀態說明
i18n_entity.PickOrder.fields.container.label=容器
i18n_entity.PickOrder.fields.containerBackOrderId.label=容器回庫運單號
i18n_entity.PickOrder.fields.containerOutDone.label=容器出庫搬運完成
i18n_entity.PickOrder.fields.containerOutOrderId.label=容器出庫運單號
i18n_entity.PickOrder.fields.createdBy.label=創建人
i18n_entity.PickOrder.fields.createdOn.label=創建時間
i18n_entity.PickOrder.fields.id.label=單號
i18n_entity.PickOrder.fields.modifiedBy.label=最後修改人
i18n_entity.PickOrder.fields.modifiedOn.label=最後修改時間
i18n_entity.PickOrder.fields.sourceOrderId.label=出庫單號
i18n_entity.PickOrder.fields.submittedPostProcessed.label=後處理完成
i18n_entity.PickOrder.fields.version.label=修訂版本
i18n_entity.PickOrder.group=倉庫
i18n_entity.PickOrder.label=分揀單
i18n_entity.PickOrderLine.fields.btLineNo.label=行號
i18n_entity.PickOrderLine.fields.btMaterial.label=物料
i18n_entity.PickOrderLine.fields.btMaterialCategory.label=物料分類編號
i18n_entity.PickOrderLine.fields.btMaterialCategoryName.label=物料分類名稱
i18n_entity.PickOrderLine.fields.btMaterialId.label=物料編號
i18n_entity.PickOrderLine.fields.btMaterialImage.label=物料圖片
i18n_entity.PickOrderLine.fields.btMaterialModel.label=物料型號
i18n_entity.PickOrderLine.fields.btMaterialName.label=物料名稱
i18n_entity.PickOrderLine.fields.btMaterialSpec.label=物料規格
i18n_entity.PickOrderLine.fields.btParentId.label=所屬單據
i18n_entity.PickOrderLine.fields.container.label=容器
i18n_entity.PickOrderLine.fields.createdBy.label=創建人
i18n_entity.PickOrderLine.fields.createdOn.label=創建時間
i18n_entity.PickOrderLine.fields.id.label=ID
i18n_entity.PickOrderLine.fields.lotNo.label=批次號
i18n_entity.PickOrderLine.fields.modifiedBy.label=最後修改人
i18n_entity.PickOrderLine.fields.modifiedOn.label=最後修改時間
i18n_entity.PickOrderLine.fields.planQty.label=期望揀出數
i18n_entity.PickOrderLine.fields.qty.label=實際揀出數
i18n_entity.PickOrderLine.fields.qualityLevel.label=質量等級
i18n_entity.PickOrderLine.fields.sourceLineId.label=來源單行 ID
i18n_entity.PickOrderLine.fields.sourceLineNo.label=來源行號
i18n_entity.PickOrderLine.fields.sourceOrderId.label=來源單據
i18n_entity.PickOrderLine.fields.subContainerId.label=格號
i18n_entity.PickOrderLine.fields.version.label=修訂版本
i18n_entity.PickOrderLine.group=倉庫
i18n_entity.PickOrderLine.label=分揀單行
i18n_entity.PlcDeviceConfig.fields.autoRetry.label=自動重連
i18n_entity.PlcDeviceConfig.fields.createdBy.label=創建人
i18n_entity.PlcDeviceConfig.fields.createdOn.label=創建時間
i18n_entity.PlcDeviceConfig.fields.disabled.label=停用
i18n_entity.PlcDeviceConfig.fields.endpoint.label=連接字符串
i18n_entity.PlcDeviceConfig.fields.host.label=地址/IP
i18n_entity.PlcDeviceConfig.fields.id.label=名稱（ID）
i18n_entity.PlcDeviceConfig.fields.maxRetry.label=最大重試次數
i18n_entity.PlcDeviceConfig.fields.modifiedBy.label=最後修改人
i18n_entity.PlcDeviceConfig.fields.modifiedOn.label=最後修改時間
i18n_entity.PlcDeviceConfig.fields.port.label=端口
i18n_entity.PlcDeviceConfig.fields.rack.label=S7 機架號
i18n_entity.PlcDeviceConfig.fields.retryDelay.label=重試等待（毫秒）
i18n_entity.PlcDeviceConfig.fields.slot.label=S7 槽號
i18n_entity.PlcDeviceConfig.fields.subType.label=子類型
i18n_entity.PlcDeviceConfig.fields.type.inlineOptionBill.items.Modbus.label=Modbus
i18n_entity.PlcDeviceConfig.fields.type.inlineOptionBill.items.OPCUA.label=OPCUA
i18n_entity.PlcDeviceConfig.fields.type.inlineOptionBill.items.S7.label=S7
i18n_entity.PlcDeviceConfig.fields.type.label=類型
i18n_entity.PlcDeviceConfig.fields.version.label=修訂版本
i18n_entity.PlcDeviceConfig.group=WCS
i18n_entity.PlcDeviceConfig.label=PLC 設備配置
i18n_entity.PlcRwLog.fields.action.label=操作
i18n_entity.PlcRwLog.fields.createdBy.label=創建人
i18n_entity.PlcRwLog.fields.createdOn.label=創建時間
i18n_entity.PlcRwLog.fields.deviceName.label=設備名
i18n_entity.PlcRwLog.fields.deviceType.label=設備類型
i18n_entity.PlcRwLog.fields.id.label=ID
i18n_entity.PlcRwLog.fields.ip.label=IP
i18n_entity.PlcRwLog.fields.modifiedBy.label=最後修改人
i18n_entity.PlcRwLog.fields.modifiedOn.label=最後修改時間
i18n_entity.PlcRwLog.fields.oldValueDesc.label=原值
i18n_entity.PlcRwLog.fields.reason.label=原因
i18n_entity.PlcRwLog.fields.rw.inlineOptionBill.items.Read.label=讀
i18n_entity.PlcRwLog.fields.rw.inlineOptionBill.items.Write.label=寫
i18n_entity.PlcRwLog.fields.rw.label=讀寫
i18n_entity.PlcRwLog.fields.valueDesc.label=值
i18n_entity.PlcRwLog.fields.version.label=修訂版本
i18n_entity.PlcRwLog.group=WCS
i18n_entity.PlcRwLog.label=PLC 讀寫記錄
i18n_entity.PlcRwLog.listCard.oldValueDesc.prefix=原值：
i18n_entity.PlcRwLog.listCard.valueDesc.prefix=值：
i18n_entity.PutinContainerOrder.fields.btLines.label=單行
i18n_entity.PutinContainerOrder.fields.btOrderKind.inlineOptionBill.items.NormalTask.label=裝貨任務
i18n_entity.PutinContainerOrder.fields.btOrderKind.label=類型
i18n_entity.PutinContainerOrder.fields.btOrderState.inlineOptionBill.items.Done.label=上架完成
i18n_entity.PutinContainerOrder.fields.btOrderState.inlineOptionBill.items.Filled.label=裝入完成
i18n_entity.PutinContainerOrder.fields.btOrderState.inlineOptionBill.items.Todo.label=等待裝入
i18n_entity.PutinContainerOrder.fields.btOrderState.label=狀態
i18n_entity.PutinContainerOrder.fields.btOrderStateReason.label=狀態說明
i18n_entity.PutinContainerOrder.fields.container.label=容器
i18n_entity.PutinContainerOrder.fields.containerBackOrderId.label=容器回庫運單號
i18n_entity.PutinContainerOrder.fields.containerOutDone.label=容器出庫搬運完成
i18n_entity.PutinContainerOrder.fields.containerOutOrderId.label=容器出庫運單號
i18n_entity.PutinContainerOrder.fields.createdBy.label=創建人
i18n_entity.PutinContainerOrder.fields.createdOn.label=創建時間
i18n_entity.PutinContainerOrder.fields.id.label=單號
i18n_entity.PutinContainerOrder.fields.modifiedBy.label=最後修改人
i18n_entity.PutinContainerOrder.fields.modifiedOn.label=最後修改時間
i18n_entity.PutinContainerOrder.fields.sourceOrderId.label=入庫單號
i18n_entity.PutinContainerOrder.fields.version.label=修訂版本
i18n_entity.PutinContainerOrder.group=倉庫
i18n_entity.PutinContainerOrder.label=裝貨單
i18n_entity.PutinContainerOrderLine.fields.btLineNo.label=行號
i18n_entity.PutinContainerOrderLine.fields.btMaterial.label=物料
i18n_entity.PutinContainerOrderLine.fields.btMaterialCategory.label=物料分類編號
i18n_entity.PutinContainerOrderLine.fields.btMaterialCategoryName.label=物料分類名稱
i18n_entity.PutinContainerOrderLine.fields.btMaterialId.label=物料編號
i18n_entity.PutinContainerOrderLine.fields.btMaterialImage.label=物料圖片
i18n_entity.PutinContainerOrderLine.fields.btMaterialModel.label=物料型號
i18n_entity.PutinContainerOrderLine.fields.btMaterialName.label=物料名稱
i18n_entity.PutinContainerOrderLine.fields.btMaterialSpec.label=物料規格
i18n_entity.PutinContainerOrderLine.fields.btParentId.label=所屬單據
i18n_entity.PutinContainerOrderLine.fields.container.label=容器
i18n_entity.PutinContainerOrderLine.fields.createdBy.label=創建人
i18n_entity.PutinContainerOrderLine.fields.createdOn.label=創建時間
i18n_entity.PutinContainerOrderLine.fields.id.label=ID
i18n_entity.PutinContainerOrderLine.fields.lotNo.label=批次號
i18n_entity.PutinContainerOrderLine.fields.modifiedBy.label=最後修改人
i18n_entity.PutinContainerOrderLine.fields.modifiedOn.label=最後修改時間
i18n_entity.PutinContainerOrderLine.fields.planQty.label=計劃裝入數
i18n_entity.PutinContainerOrderLine.fields.price.label=單價
i18n_entity.PutinContainerOrderLine.fields.qty.label=實際裝入數
i18n_entity.PutinContainerOrderLine.fields.qualityLevel.label=質量等級
i18n_entity.PutinContainerOrderLine.fields.sourceLineId.label=來源單行 ID
i18n_entity.PutinContainerOrderLine.fields.sourceLineNo.label=來源行號
i18n_entity.PutinContainerOrderLine.fields.sourceOrderId.label=來源單據
i18n_entity.PutinContainerOrderLine.fields.subContainerId.label=格號
i18n_entity.PutinContainerOrderLine.fields.unitLabel.label=單位名稱
i18n_entity.PutinContainerOrderLine.fields.version.label=修訂版本
i18n_entity.PutinContainerOrderLine.group=倉庫
i18n_entity.PutinContainerOrderLine.label=裝貨單行
i18n_entity.QsInboundOrder.fields.btLines.label=單行
i18n_entity.QsInboundOrder.fields.btOrderState.inlineOptionBill.items.Committed.label=已提交
i18n_entity.QsInboundOrder.fields.btOrderState.inlineOptionBill.items.Init.label=未提交
i18n_entity.QsInboundOrder.fields.btOrderState.label=狀態
i18n_entity.QsInboundOrder.fields.btOrderStateReason.label=狀態說明
i18n_entity.QsInboundOrder.fields.callContainerAll.label=叫空容器分配完成
i18n_entity.QsInboundOrder.fields.createdBy.label=創建人
i18n_entity.QsInboundOrder.fields.createdOn.label=創建時間
i18n_entity.QsInboundOrder.fields.id.label=單號
i18n_entity.QsInboundOrder.fields.modifiedBy.label=最後修改人
i18n_entity.QsInboundOrder.fields.modifiedOn.label=最後修改時間
i18n_entity.QsInboundOrder.fields.priority.label=優先級
i18n_entity.QsInboundOrder.fields.remark.label=備註
i18n_entity.QsInboundOrder.fields.ro.label=根組織
i18n_entity.QsInboundOrder.fields.version.label=版本
i18n_entity.QsInboundOrder.group=快速商店
i18n_entity.QsInboundOrder.label=QS 入庫單
i18n_entity.QsInboundOrderLine.fields.btLineNo.label=行號
i18n_entity.QsInboundOrderLine.fields.btMaterial.label=物料
i18n_entity.QsInboundOrderLine.fields.btMaterialCategory.label=物料分類編號
i18n_entity.QsInboundOrderLine.fields.btMaterialCategoryName.label=物料分類名稱
i18n_entity.QsInboundOrderLine.fields.btMaterialId.label=物料編號
i18n_entity.QsInboundOrderLine.fields.btMaterialImage.label=物料圖片
i18n_entity.QsInboundOrderLine.fields.btMaterialModel.label=物料型號
i18n_entity.QsInboundOrderLine.fields.btMaterialName.label=物料名稱
i18n_entity.QsInboundOrderLine.fields.btMaterialSpec.label=物料規格
i18n_entity.QsInboundOrderLine.fields.btParentId.label=所屬出庫單
i18n_entity.QsInboundOrderLine.fields.ccQty.label=叫空容器分配數量
i18n_entity.QsInboundOrderLine.fields.createdBy.label=創建人
i18n_entity.QsInboundOrderLine.fields.createdOn.label=創建時間
i18n_entity.QsInboundOrderLine.fields.id.label=ID
i18n_entity.QsInboundOrderLine.fields.lotNo.label=批次號
i18n_entity.QsInboundOrderLine.fields.modifiedBy.label=最後修改人
i18n_entity.QsInboundOrderLine.fields.modifiedOn.label=最後修改時間
i18n_entity.QsInboundOrderLine.fields.priority.label=優先級
i18n_entity.QsInboundOrderLine.fields.qty.label=入庫數量
i18n_entity.QsInboundOrderLine.fields.ro.label=根組織
i18n_entity.QsInboundOrderLine.fields.version.label=版本
i18n_entity.QsInboundOrderLine.group=快速商店
i18n_entity.QsInboundOrderLine.label=QS 入庫單行
i18n_entity.QsOldInvLine.fields.amount.label=金額
i18n_entity.QsOldInvLine.fields.btLineNo.label=行號
i18n_entity.QsOldInvLine.fields.btMaterial.label=物料
i18n_entity.QsOldInvLine.fields.btMaterialCategory.label=物料分類編號
i18n_entity.QsOldInvLine.fields.btMaterialCategoryName.label=物料分類名稱
i18n_entity.QsOldInvLine.fields.btMaterialImage.label=物料圖片
i18n_entity.QsOldInvLine.fields.btMaterialModel.label=物料型號
i18n_entity.QsOldInvLine.fields.btMaterialName.label=物料名稱
i18n_entity.QsOldInvLine.fields.btMaterialSpec.label=物料規格
i18n_entity.QsOldInvLine.fields.btParentId.label=所屬訂單
i18n_entity.QsOldInvLine.fields.createdBy.label=創建人
i18n_entity.QsOldInvLine.fields.createdOn.label=創建時間
i18n_entity.QsOldInvLine.fields.expDate.label=有效期
i18n_entity.QsOldInvLine.fields.id.label=ID
i18n_entity.QsOldInvLine.fields.inboundOn.label=入庫時間
i18n_entity.QsOldInvLine.fields.leafContainer.label=最內層容器
i18n_entity.QsOldInvLine.fields.lotNo.label=批次號
i18n_entity.QsOldInvLine.fields.matLotNo.label=批次
i18n_entity.QsOldInvLine.fields.matSerialNo.label=序列號
i18n_entity.QsOldInvLine.fields.mfgDate.label=生產日期
i18n_entity.QsOldInvLine.fields.modifiedBy.label=最後修改人
i18n_entity.QsOldInvLine.fields.modifiedOn.label=最後修改時間
i18n_entity.QsOldInvLine.fields.owner.label=貨主
i18n_entity.QsOldInvLine.fields.price.label=單價
i18n_entity.QsOldInvLine.fields.qty.label=數量
i18n_entity.QsOldInvLine.fields.refInvId.label=關聯庫存明細 ID
i18n_entity.QsOldInvLine.fields.subContainerId.label=格子
i18n_entity.QsOldInvLine.fields.topContainer.label=最外層容器
i18n_entity.QsOldInvLine.fields.vendor.label=供應商
i18n_entity.QsOldInvLine.fields.version.label=修訂版本
i18n_entity.QsOldInvLine.group=快速商店
i18n_entity.QsOldInvLine.label=QS 現有庫存明細
i18n_entity.QsOldInvLine.listStats.items[0].label=處理中（鎖定）
i18n_entity.QsOutboundOrder.fields.btLines.label=單行
i18n_entity.QsOutboundOrder.fields.btOrderKind.inlineOptionBill.items.Default.label=默認
i18n_entity.QsOutboundOrder.fields.btOrderKind.label=類型
i18n_entity.QsOutboundOrder.fields.btOrderState.inlineOptionBill.items.Committed.label=已提交
i18n_entity.QsOutboundOrder.fields.btOrderState.inlineOptionBill.items.Init.label=未提交
i18n_entity.QsOutboundOrder.fields.btOrderState.label=狀態
i18n_entity.QsOutboundOrder.fields.btOrderStateReason.label=狀態說明
i18n_entity.QsOutboundOrder.fields.createdBy.label=創建人
i18n_entity.QsOutboundOrder.fields.createdOn.label=創建時間
i18n_entity.QsOutboundOrder.fields.id.label=單號
i18n_entity.QsOutboundOrder.fields.invAssignedAll.label=庫存分配完成
i18n_entity.QsOutboundOrder.fields.modifiedBy.label=最後修改人
i18n_entity.QsOutboundOrder.fields.modifiedOn.label=最後修改時間
i18n_entity.QsOutboundOrder.fields.priority.label=優先級
i18n_entity.QsOutboundOrder.fields.remark.label=備註
i18n_entity.QsOutboundOrder.fields.ro.label=根組織
i18n_entity.QsOutboundOrder.fields.typePriority.label=類型優先級
i18n_entity.QsOutboundOrder.fields.version.label=版本
i18n_entity.QsOutboundOrder.group=快速商店
i18n_entity.QsOutboundOrder.label=QS 出庫單
i18n_entity.QsOutboundOrderLine.fields.btLineNo.label=行號
i18n_entity.QsOutboundOrderLine.fields.btMaterial.label=物料
i18n_entity.QsOutboundOrderLine.fields.btMaterialCategory.label=物料分類編號
i18n_entity.QsOutboundOrderLine.fields.btMaterialCategoryName.label=物料分類名稱
i18n_entity.QsOutboundOrderLine.fields.btMaterialId.label=物料編號
i18n_entity.QsOutboundOrderLine.fields.btMaterialImage.label=物料圖片
i18n_entity.QsOutboundOrderLine.fields.btMaterialModel.label=物料型號
i18n_entity.QsOutboundOrderLine.fields.btMaterialName.label=物料名稱
i18n_entity.QsOutboundOrderLine.fields.btMaterialSpec.label=物料規格
i18n_entity.QsOutboundOrderLine.fields.btParentId.label=所屬出庫單
i18n_entity.QsOutboundOrderLine.fields.createdBy.label=創建人
i18n_entity.QsOutboundOrderLine.fields.createdOn.label=創建時間
i18n_entity.QsOutboundOrderLine.fields.id.label=ID
i18n_entity.QsOutboundOrderLine.fields.invAssignedQty.label=已分配庫存數量
i18n_entity.QsOutboundOrderLine.fields.lotNo.label=批次號
i18n_entity.QsOutboundOrderLine.fields.modifiedBy.label=最後修改人
i18n_entity.QsOutboundOrderLine.fields.modifiedOn.label=最後修改時間
i18n_entity.QsOutboundOrderLine.fields.priority.label=優先級
i18n_entity.QsOutboundOrderLine.fields.qty.label=出庫數量
i18n_entity.QsOutboundOrderLine.fields.ro.label=根組織
i18n_entity.QsOutboundOrderLine.fields.version.label=版本
i18n_entity.QsOutboundOrderLine.group=快速商店
i18n_entity.QsOutboundOrderLine.label=QS 出庫單行
i18n_entity.QsPickOrder.fields.btLines.label=單行
i18n_entity.QsPickOrder.fields.btOrderKind.inlineOptionBill.items.NormalTask.label=裝貨任務
i18n_entity.QsPickOrder.fields.btOrderKind.label=類型
i18n_entity.QsPickOrder.fields.btOrderState.inlineOptionBill.items.Done.label=揀貨完成
i18n_entity.QsPickOrder.fields.btOrderState.inlineOptionBill.items.Todo.label=待揀貨
i18n_entity.QsPickOrder.fields.btOrderState.label=狀態
i18n_entity.QsPickOrder.fields.btOrderStateReason.label=狀態說明
i18n_entity.QsPickOrder.fields.container.label=容器
i18n_entity.QsPickOrder.fields.createdBy.label=創建人
i18n_entity.QsPickOrder.fields.createdOn.label=創建時間
i18n_entity.QsPickOrder.fields.id.label=單號
i18n_entity.QsPickOrder.fields.modifiedBy.label=最後修改人
i18n_entity.QsPickOrder.fields.modifiedOn.label=最後修改時間
i18n_entity.QsPickOrder.fields.targetBin.label=目標庫位
i18n_entity.QsPickOrder.fields.version.label=修訂版本
i18n_entity.QsPickOrder.group=快速商店
i18n_entity.QsPickOrder.label=QS 揀貨單
i18n_entity.QsPickOrderLine.fields.btLineNo.label=行號
i18n_entity.QsPickOrderLine.fields.btMaterial.label=物料
i18n_entity.QsPickOrderLine.fields.btMaterialCategory.label=物料分類編號
i18n_entity.QsPickOrderLine.fields.btMaterialCategoryName.label=物料分類名稱
i18n_entity.QsPickOrderLine.fields.btMaterialId.label=物料編號
i18n_entity.QsPickOrderLine.fields.btMaterialImage.label=物料圖片
i18n_entity.QsPickOrderLine.fields.btMaterialModel.label=物料型號
i18n_entity.QsPickOrderLine.fields.btMaterialName.label=物料名稱
i18n_entity.QsPickOrderLine.fields.btMaterialSpec.label=物料規格
i18n_entity.QsPickOrderLine.fields.btParentId.label=所屬單據
i18n_entity.QsPickOrderLine.fields.createdBy.label=創建人
i18n_entity.QsPickOrderLine.fields.createdOn.label=創建時間
i18n_entity.QsPickOrderLine.fields.id.label=ID
i18n_entity.QsPickOrderLine.fields.invLayoutId.label=庫存明細 ID
i18n_entity.QsPickOrderLine.fields.lotNo.label=批次號
i18n_entity.QsPickOrderLine.fields.modifiedBy.label=最後修改人
i18n_entity.QsPickOrderLine.fields.modifiedOn.label=最後修改時間
i18n_entity.QsPickOrderLine.fields.outboundLineId.label=出庫單行 ID
i18n_entity.QsPickOrderLine.fields.outboundLineNo.label=出庫單行行號
i18n_entity.QsPickOrderLine.fields.outboundOrderId.label=出庫單號
i18n_entity.QsPickOrderLine.fields.planQty.label=期望揀出數
i18n_entity.QsPickOrderLine.fields.qty.label=實際揀出數
i18n_entity.QsPickOrderLine.fields.subContainerId.label=格號
i18n_entity.QsPickOrderLine.fields.topContainer.label=容器
i18n_entity.QsPickOrderLine.fields.version.label=修訂版本
i18n_entity.QsPickOrderLine.group=快速商店
i18n_entity.QsPickOrderLine.label=QS 揀貨單行
i18n_entity.QsPutOnContainerOrder.fields.bin.label=上架庫位
i18n_entity.QsPutOnContainerOrder.fields.container.label=容器
i18n_entity.QsPutOnContainerOrder.fields.createdBy.label=創建人
i18n_entity.QsPutOnContainerOrder.fields.createdOn.label=創建時間
i18n_entity.QsPutOnContainerOrder.fields.id.label=ID
i18n_entity.QsPutOnContainerOrder.fields.modifiedBy.label=最後修改人
i18n_entity.QsPutOnContainerOrder.fields.modifiedOn.label=最後修改時間
i18n_entity.QsPutOnContainerOrder.fields.oldInvLines.label=上架時容器內的庫存明細
i18n_entity.QsPutOnContainerOrder.fields.version.label=修訂版本
i18n_entity.QsPutOnContainerOrder.group=快速商店
i18n_entity.QsPutOnContainerOrder.label=QS 庫口上架單
i18n_entity.QsPutOrder.fields.bin.label=上架庫位
i18n_entity.QsPutOrder.fields.btLines.label=單行
i18n_entity.QsPutOrder.fields.btOrderKind.inlineOptionBill.items.NormalTask.label=裝貨任務
i18n_entity.QsPutOrder.fields.btOrderKind.label=類型
i18n_entity.QsPutOrder.fields.btOrderState.inlineOptionBill.items.Done.label=裝貨完成
i18n_entity.QsPutOrder.fields.btOrderState.inlineOptionBill.items.Todo.label=待裝貨
i18n_entity.QsPutOrder.fields.btOrderState.label=狀態
i18n_entity.QsPutOrder.fields.btOrderStateReason.label=狀態說明
i18n_entity.QsPutOrder.fields.container.label=容器
i18n_entity.QsPutOrder.fields.createdBy.label=創建人
i18n_entity.QsPutOrder.fields.createdOn.label=創建時間
i18n_entity.QsPutOrder.fields.id.label=單號
i18n_entity.QsPutOrder.fields.modifiedBy.label=最後修改人
i18n_entity.QsPutOrder.fields.modifiedOn.label=最後修改時間
i18n_entity.QsPutOrder.fields.oldInvLines.label=容器內原有庫存明細
i18n_entity.QsPutOrder.fields.targetBin.label=目標庫位
i18n_entity.QsPutOrder.fields.version.label=修訂版本
i18n_entity.QsPutOrder.group=快速商店
i18n_entity.QsPutOrder.label=QS 裝貨單
i18n_entity.QsPutOrderLine.fields.btLineNo.label=行號
i18n_entity.QsPutOrderLine.fields.btMaterial.label=物料
i18n_entity.QsPutOrderLine.fields.btMaterialCategory.label=物料分類編號
i18n_entity.QsPutOrderLine.fields.btMaterialCategoryName.label=物料分類名稱
i18n_entity.QsPutOrderLine.fields.btMaterialId.label=物料編號
i18n_entity.QsPutOrderLine.fields.btMaterialImage.label=物料圖片
i18n_entity.QsPutOrderLine.fields.btMaterialModel.label=物料型號
i18n_entity.QsPutOrderLine.fields.btMaterialName.label=物料名稱
i18n_entity.QsPutOrderLine.fields.btMaterialSpec.label=物料規格
i18n_entity.QsPutOrderLine.fields.btParentId.label=所屬單據
i18n_entity.QsPutOrderLine.fields.createdBy.label=創建人
i18n_entity.QsPutOrderLine.fields.createdOn.label=創建時間
i18n_entity.QsPutOrderLine.fields.id.label=ID
i18n_entity.QsPutOrderLine.fields.lotNo.label=批次號
i18n_entity.QsPutOrderLine.fields.modifiedBy.label=最後修改人
i18n_entity.QsPutOrderLine.fields.modifiedOn.label=最後修改時間
i18n_entity.QsPutOrderLine.fields.planQty.label=期望裝貨數量
i18n_entity.QsPutOrderLine.fields.qty.label=實際裝貨數量
i18n_entity.QsPutOrderLine.fields.subContainerId.label=格號
i18n_entity.QsPutOrderLine.fields.version.label=修訂版本
i18n_entity.QsPutOrderLine.group=快速商店
i18n_entity.QsPutOrderLine.label=QS 裝貨單行
i18n_entity.QsTakeOffContainerOrder.fields.bin.label=下架庫位
i18n_entity.QsTakeOffContainerOrder.fields.container.label=容器
i18n_entity.QsTakeOffContainerOrder.fields.createdBy.label=創建人
i18n_entity.QsTakeOffContainerOrder.fields.createdOn.label=創建時間
i18n_entity.QsTakeOffContainerOrder.fields.id.label=ID
i18n_entity.QsTakeOffContainerOrder.fields.keepInv.label=保留庫存
i18n_entity.QsTakeOffContainerOrder.fields.modifiedBy.label=最後修改人
i18n_entity.QsTakeOffContainerOrder.fields.modifiedOn.label=最後修改時間
i18n_entity.QsTakeOffContainerOrder.fields.oldInvLines.label=下架時容器內的庫存明細
i18n_entity.QsTakeOffContainerOrder.fields.version.label=修訂版本
i18n_entity.QsTakeOffContainerOrder.group=快速商店
i18n_entity.QsTakeOffContainerOrder.label=QS 庫口下架單
i18n_entity.ResourceLock.fields.createdBy.label=創建人
i18n_entity.ResourceLock.fields.createdOn.label=創建時間
i18n_entity.ResourceLock.fields.id.label=ID
i18n_entity.ResourceLock.fields.lockOn.label=鎖定時間
i18n_entity.ResourceLock.fields.locked.label=鎖定
i18n_entity.ResourceLock.fields.modifiedBy.label=最後修改人
i18n_entity.ResourceLock.fields.modifiedOn.label=修改時間
i18n_entity.ResourceLock.fields.owner.label=鎖定者
i18n_entity.ResourceLock.fields.reason.label=鎖定原因
i18n_entity.ResourceLock.fields.resId.label=資源 ID
i18n_entity.ResourceLock.fields.resType.label=資源類型
i18n_entity.ResourceLock.fields.version.label=修訂版本
i18n_entity.ResourceLock.group=核心
i18n_entity.ResourceLock.label=資源鎖
i18n_entity.RobotConnectedPoint.fields.bin.label=關聯庫位
i18n_entity.RobotConnectedPoint.fields.channel.label=所在巷道
i18n_entity.RobotConnectedPoint.fields.createdBy.label=創建人
i18n_entity.RobotConnectedPoint.fields.createdOn.label=創建時間
i18n_entity.RobotConnectedPoint.fields.id.label=ID
i18n_entity.RobotConnectedPoint.fields.modifiedBy.label=最後修改人
i18n_entity.RobotConnectedPoint.fields.modifiedOn.label=最後修改時間
i18n_entity.RobotConnectedPoint.fields.remark.label=備註
i18n_entity.RobotConnectedPoint.fields.version.label=修訂版本
i18n_entity.RobotConnectedPoint.fields.x.label=位置 x
i18n_entity.RobotConnectedPoint.fields.y.label=位置 y
i18n_entity.RobotConnectedPoint.group=WCS
i18n_entity.RobotConnectedPoint.label=機器人通訊點
i18n_entity.RobustScriptExecutor.fields.args.label=參數
i18n_entity.RobustScriptExecutor.fields.createdBy.label=創建人
i18n_entity.RobustScriptExecutor.fields.createdOn.label=創建時間
i18n_entity.RobustScriptExecutor.fields.description.label=描述
i18n_entity.RobustScriptExecutor.fields.fault.label=故障
i18n_entity.RobustScriptExecutor.fields.faultMsg.label=故障信息
i18n_entity.RobustScriptExecutor.fields.funcName.label=方法
i18n_entity.RobustScriptExecutor.fields.id.label=ID
i18n_entity.RobustScriptExecutor.fields.modifiedBy.label=最後修改人
i18n_entity.RobustScriptExecutor.fields.modifiedOn.label=最後修改時間
i18n_entity.RobustScriptExecutor.fields.version.label=修訂版本
i18n_entity.RobustScriptExecutor.group=核心
i18n_entity.RobustScriptExecutor.label=後臺任務
i18n_entity.RobustScriptExecutor.pagesButtons.ListMain.buttons[0].label=批量編輯
i18n_entity.RobustScriptExecutor.pagesButtons.ListMain.buttons[1].label=刪除
i18n_entity.RobustScriptExecutor.pagesButtons.ListMain.buttons[2].label=恢復
i18n_entity.ScriptRunOnce.fields.createdBy.label=創建人
i18n_entity.ScriptRunOnce.fields.createdOn.label=創建時間
i18n_entity.ScriptRunOnce.fields.id.label=ID
i18n_entity.ScriptRunOnce.fields.modifiedBy.label=最後修改人
i18n_entity.ScriptRunOnce.fields.modifiedOn.label=修改時間
i18n_entity.ScriptRunOnce.fields.output.label=輸出
i18n_entity.ScriptRunOnce.fields.version.label=修訂版本
i18n_entity.ScriptRunOnce.group=核心
i18n_entity.ScriptRunOnce.label=腳本運行一次
i18n_entity.SimpleTransportOrder.fields.createdBy.label=創建人
i18n_entity.SimpleTransportOrder.fields.createdOn.label=創建時間
i18n_entity.SimpleTransportOrder.fields.currentMove.label=當前步驟
i18n_entity.SimpleTransportOrder.fields.doneOn.label=結束時間
i18n_entity.SimpleTransportOrder.fields.errorMsg.label=錯誤消息
i18n_entity.SimpleTransportOrder.fields.id.label=ID
i18n_entity.SimpleTransportOrder.fields.modifiedBy.label=最後修改人
i18n_entity.SimpleTransportOrder.fields.modifiedOn.label=最後修改時間
i18n_entity.SimpleTransportOrder.fields.moves.label=動作列表
i18n_entity.SimpleTransportOrder.fields.robotName.label=機器人
i18n_entity.SimpleTransportOrder.fields.seer3066.label=指定路徑導航
i18n_entity.SimpleTransportOrder.fields.status.inlineOptionBill.items.Cancelled.label=已取消
i18n_entity.SimpleTransportOrder.fields.status.inlineOptionBill.items.Created.label=已創建
i18n_entity.SimpleTransportOrder.fields.status.inlineOptionBill.items.Done.label=已完成
i18n_entity.SimpleTransportOrder.fields.status.inlineOptionBill.items.Failed.label=失敗
i18n_entity.SimpleTransportOrder.fields.status.label=狀態
i18n_entity.SimpleTransportOrder.fields.vendor.label=廠商
i18n_entity.SimpleTransportOrder.fields.version.label=修訂版本
i18n_entity.SimpleTransportOrder.group=GW
i18n_entity.SimpleTransportOrder.label=網關簡單運單
i18n_entity.SimpleTransportOrder.listStats.items[0].label=執行中
i18n_entity.SimpleTransportOrder.listStats.items[1].label=失敗
i18n_entity.SocNode.fields.attention.label=注意
i18n_entity.SocNode.fields.createdBy.label=創建人
i18n_entity.SocNode.fields.createdOn.label=創建時間
i18n_entity.SocNode.fields.description.label=說明
i18n_entity.SocNode.fields.id.label=ID
i18n_entity.SocNode.fields.label.label=標籤
i18n_entity.SocNode.fields.modifiedBy.label=最後修改人
i18n_entity.SocNode.fields.modifiedOn.label=最後修改時間
i18n_entity.SocNode.fields.modifiedReason.label=更新原因
i18n_entity.SocNode.fields.modifiedTimestamp.label=更新時間
i18n_entity.SocNode.fields.value.label=值
i18n_entity.SocNode.fields.version.label=修訂版本
i18n_entity.SocNode.group=核心
i18n_entity.SocNode.label=監控節點
i18n_entity.SocNode.listCard.modifiedTimestamp.prefix=更新時間:
i18n_entity.SystemKeyEvent.fields.content.label=內容
i18n_entity.SystemKeyEvent.fields.createdBy.label=創建人
i18n_entity.SystemKeyEvent.fields.createdOn.label=創建時間
i18n_entity.SystemKeyEvent.fields.group.label=模塊
i18n_entity.SystemKeyEvent.fields.id.label=ID
i18n_entity.SystemKeyEvent.fields.level.inlineOptionBill.items.Error.label=錯誤
i18n_entity.SystemKeyEvent.fields.level.inlineOptionBill.items.Info.label=普通
i18n_entity.SystemKeyEvent.fields.level.inlineOptionBill.items.Warning.label=警告
i18n_entity.SystemKeyEvent.fields.level.label=級別
i18n_entity.SystemKeyEvent.fields.modifiedBy.label=最後修改人
i18n_entity.SystemKeyEvent.fields.modifiedOn.label=最後修改時間
i18n_entity.SystemKeyEvent.fields.relatedUser.label=相關用戶
i18n_entity.SystemKeyEvent.fields.title.label=標題
i18n_entity.SystemKeyEvent.fields.version.label=修訂版本
i18n_entity.SystemKeyEvent.group=核心
i18n_entity.SystemKeyEvent.label=系統關鍵事件
i18n_entity.UserNotice.fields.actionType.label=動作類型
i18n_entity.UserNotice.fields.content.label=正文
i18n_entity.UserNotice.fields.createdBy.label=創建人
i18n_entity.UserNotice.fields.createdOn.label=創建時間
i18n_entity.UserNotice.fields.entityId.label=實體 ID
i18n_entity.UserNotice.fields.entityName.label=實體名
i18n_entity.UserNotice.fields.hasContent.label=有正文
i18n_entity.UserNotice.fields.id.label=ID
i18n_entity.UserNotice.fields.modifiedBy.label=最後修改人
i18n_entity.UserNotice.fields.modifiedOn.label=最後修改時間
i18n_entity.UserNotice.fields.read.label=已讀
i18n_entity.UserNotice.fields.readOn.label=已讀時間
i18n_entity.UserNotice.fields.title.label=標題
i18n_entity.UserNotice.fields.userId.label=用戶
i18n_entity.UserNotice.fields.version.label=修訂版本
i18n_entity.UserNotice.group=核心
i18n_entity.UserNotice.label=用戶通知
i18n_entity.UserOpLog.fields.content.label=操作內容
i18n_entity.UserOpLog.fields.createdBy.label=創建人
i18n_entity.UserOpLog.fields.createdOn.label=創建時間
i18n_entity.UserOpLog.fields.id.label=ID
i18n_entity.UserOpLog.fields.level.inlineOptionBill.items.Danger.label=危險
i18n_entity.UserOpLog.fields.level.inlineOptionBill.items.Normal.label=正常
i18n_entity.UserOpLog.fields.level.label=級別
i18n_entity.UserOpLog.fields.modifiedBy.label=最後修改人
i18n_entity.UserOpLog.fields.modifiedOn.label=最後修改時間
i18n_entity.UserOpLog.fields.operator.label=用戶
i18n_entity.UserOpLog.fields.page.label=頁面
i18n_entity.UserOpLog.fields.version.label=修訂版本
i18n_entity.UserOpLog.group=核心
i18n_entity.UserOpLog.label=用戶操作日誌
i18n_entity.UserRole.fields.createdBy.label=創建人
i18n_entity.UserRole.fields.createdOn.label=創建時間
i18n_entity.UserRole.fields.defaultRole.label=默認角色
i18n_entity.UserRole.fields.id.label=編號
i18n_entity.UserRole.fields.modifiedBy.label=最後修改人
i18n_entity.UserRole.fields.modifiedOn.label=最後修改時間
i18n_entity.UserRole.fields.name.label=角色名
i18n_entity.UserRole.fields.pItems.label=權限列表
i18n_entity.UserRole.fields.ro.label=根組織
i18n_entity.UserRole.fields.version.label=版本
i18n_entity.UserRole.group=用戶
i18n_entity.UserRole.label=用戶角色
i18n_entity.WcsMrOrder.fields.actualRobotName.label=執行機器人
i18n_entity.WcsMrOrder.fields.cancelling.label=取消中
i18n_entity.WcsMrOrder.fields.containerId.label=容器編號
i18n_entity.WcsMrOrder.fields.createdBy.label=創建人
i18n_entity.WcsMrOrder.fields.createdOn.label=創建時間
i18n_entity.WcsMrOrder.fields.currentStepIndex.label=當前步驟
i18n_entity.WcsMrOrder.fields.dispatchCost.label=分派成本
i18n_entity.WcsMrOrder.fields.doneOn.label=完成時刻
i18n_entity.WcsMrOrder.fields.doneStepIndex.label=已完成步驟
i18n_entity.WcsMrOrder.fields.executing.label=執行中
i18n_entity.WcsMrOrder.fields.expectedRobotGroups.label=指定機器人組
i18n_entity.WcsMrOrder.fields.expectedRobotNames.label=指定機器人
i18n_entity.WcsMrOrder.fields.fault.label=故障
i18n_entity.WcsMrOrder.fields.id.label=ID
i18n_entity.WcsMrOrder.fields.keySites.label=關鍵點位
i18n_entity.WcsMrOrder.fields.kind.inlineOptionBill.items.Parking.label=停靠
i18n_entity.WcsMrOrder.fields.kind.label=類型
i18n_entity.WcsMrOrder.fields.loadLocationSite.label=取貨點位
i18n_entity.WcsMrOrder.fields.loaded.label=已取貨
i18n_entity.WcsMrOrder.fields.materialId.label=物料編號
i18n_entity.WcsMrOrder.fields.materialKind.label=物料類型
i18n_entity.WcsMrOrder.fields.modifiedBy.label=最後修改人
i18n_entity.WcsMrOrder.fields.modifiedOn.label=修改時間
i18n_entity.WcsMrOrder.fields.priority.label=優先級
i18n_entity.WcsMrOrder.fields.reqId.label=請求單號
i18n_entity.WcsMrOrder.fields.ro.label=RO
i18n_entity.WcsMrOrder.fields.robotAllocatedOn.label=機器人分配時刻
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Allocated.label=已分派
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Building.label=構建中
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Cancelled.label=已取消
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Cancelling.label=取消中
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Done.label=已完成
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Executing.label=執行中
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Pending.label=待執行
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.ToBeAllocated.label=待分派
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Withdrawn.label=被撤回
i18n_entity.WcsMrOrder.fields.status.label=狀態
i18n_entity.WcsMrOrder.fields.stepFixed.label=步驟固定
i18n_entity.WcsMrOrder.fields.stepNum.label=任務步數
i18n_entity.WcsMrOrder.fields.taskBatch.label=任務批次
i18n_entity.WcsMrOrder.fields.unloadLocationSite.label=放貨點位
i18n_entity.WcsMrOrder.fields.unloaded.label=已放貨
i18n_entity.WcsMrOrder.fields.version.label=修訂版本
i18n_entity.WcsMrOrder.fields.withdrawn.label=重新分派
i18n_entity.WcsMrOrder.group=WCS
i18n_entity.WcsMrOrder.label=通用運單
i18n_entity.WcsMrOrder.listStats.items[0].label=故障
i18n_entity.WcsMrOrder.listStats.items[1].label=待分派
i18n_entity.WcsMrOrder.listStats.items[2].label=已分派
i18n_entity.WcsMrOrderStep.fields.createdBy.label=創建人
i18n_entity.WcsMrOrderStep.fields.createdOn.label=創建時間
i18n_entity.WcsMrOrderStep.fields.endOn.label=結束執行時間
i18n_entity.WcsMrOrderStep.fields.forLoad.label=取貨點
i18n_entity.WcsMrOrderStep.fields.forUnload.label=放貨點
i18n_entity.WcsMrOrderStep.fields.id.label=ID
i18n_entity.WcsMrOrderStep.fields.locationSite.label=動作點位
i18n_entity.WcsMrOrderStep.fields.modifiedBy.label=最後修改人
i18n_entity.WcsMrOrderStep.fields.modifiedOn.label=修改時間
i18n_entity.WcsMrOrderStep.fields.operation.label=動作
i18n_entity.WcsMrOrderStep.fields.operationArgs.label=動作參數
i18n_entity.WcsMrOrderStep.fields.orderId.label=運單單號
i18n_entity.WcsMrOrderStep.fields.startOn.label=開始執行時間
i18n_entity.WcsMrOrderStep.fields.status.label=狀態
i18n_entity.WcsMrOrderStep.fields.stepIndex.label=第幾步
i18n_entity.WcsMrOrderStep.fields.version.label=修訂版本
i18n_entity.WcsMrOrderStep.group=WCS
i18n_entity.WcsMrOrderStep.label=通用運單步驟
i18n_entity.WcsRobotTaskLog.fields.args.label=參數
i18n_entity.WcsRobotTaskLog.fields.category.label=類別
i18n_entity.WcsRobotTaskLog.fields.code.label=代碼
i18n_entity.WcsRobotTaskLog.fields.createdBy.label=創建人
i18n_entity.WcsRobotTaskLog.fields.createdOn.label=創建時間
i18n_entity.WcsRobotTaskLog.fields.id.label=ID
i18n_entity.WcsRobotTaskLog.fields.level.label=級別
i18n_entity.WcsRobotTaskLog.fields.modifiedBy.label=最後修改人
i18n_entity.WcsRobotTaskLog.fields.modifiedOn.label=修改時間
i18n_entity.WcsRobotTaskLog.fields.robotName.label=機器人
i18n_entity.WcsRobotTaskLog.fields.taskId.label=任務編號
i18n_entity.WcsRobotTaskLog.fields.tcId.label=追蹤號
i18n_entity.WcsRobotTaskLog.fields.version.label=修訂版本
i18n_entity.WcsRobotTaskLog.group=WCS
i18n_entity.WcsRobotTaskLog.label=機器人任務日誌