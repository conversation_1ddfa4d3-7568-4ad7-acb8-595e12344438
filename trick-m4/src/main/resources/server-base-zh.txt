
[base]
asyncPushTaskOkMsg=系统已完成后台地图推送。开始于 {0}。完成于 {1}。<br/>共推送 {2} 个机器人{3}。<br/>成功 {4} 个{5}。<br/>失败 {6} 个{7}。
asyncPushTaskPushingMsg=系统当前正在后台进行地图推送。开始于 {0}。<br/>预计推送 {1} 个机器人{2}。<br/>还有 {3} 个正在推送中{4}。
bgTaskErrorMsg=后台任务 [{0}] 故障：{1}
binMsg=库位
binNoMsg=第 {0} 层（{1} 号）背篓
btnAbortTask=放弃任务
btnCancelOrder=取消运单
btnCancelTask=取消任务
btnFaultRetry=故障重试
btnManualBindM4Bin=确认绑定
btnManualBindRobot=已放入
btnManualContinue=重试
btnManualContinueMsg=请确认已经取走机器人 {0} 第 {1} 层（{2} 号）背篓的容器
btnManualFinishedTask=人工完成
btnManualReBindRobot=已重新绑定
btnManualSkip=忽略
btnManualUnbindM4Bin=确定解绑
btnManualUnbindRobot=已取走
btnViewDetail=查看详情
colonMsg=：{0}
errDownloadLogsTimeOverRange=最多只能连续取一周的日志，请修改开始或结束日期
errEmptyStatsSubject=统计科目不能为空
errFalconLabelEmpty=任务名不能为空
errFalconLabelRepeat=重复的任务名："{0}"
errFleetNoSuchSceneById=无此场景：{0}
errIllegalFile=非法文件
errInvalidMapFile=无效的地图文件
errNoRdsScene=调度场景缺失
errRbkBinContainerId=机器人 {0} 第 {1} 层（{2} 号）背篓所需的容器编号是 {3} 而实际容器编号是 {4}，请将正确的容器放入背篓，然后点击 “重试”
errRbkBinContainerIdPlusOne=机器人 {0} 货叉（999 号）背篓所需的容器编号是 {1} 而实际容器编号是 {2}，请将正确的容器放入背篓，然后点击 “重试”
errRbkBinIsEmpty=机器人 {0} 第 {1} 层（{2} 号）背篓无货，请放入容器 {3}，然后点击 “重试”
errRbkBinIsEmptyPlusOne=机器人 {0} 货叉（999 号）背篓无货，请放入容器 {1}，然后点击 “重试”
errRbkBinOccupied=机器人 {0} 取货时预留的背篓被占用，请将第 {1} 层（{2} 号）背篓的货物取走，再点击 “重试”
errRbkBinOccupiedPlusOne=机器人 {0} 取货时预留的背篓被占用，请将货叉（999 号）背篓的货物取走，再点击 “重试”
errRbkReqResBadCode=请求机器人失败。错误码：{0}。错误消息：{1}
labelBgTask=后台任务
labelNone=无
pathMsg=路径
pointMsg=点位
pwdRequirementModerate=包含大小写英文、数字，至少 8 位
pwdRequirementStrong=包含大写英文、小写英文、数字、特殊字符，至少 12 位
pwdRequirementWeak="至少 1 位"
reason=原因
robotArmMsg=货叉
system=系统
zoneMsg=区块

[base.error]
errAddStepsButNotBzOrder=只能给业务运单添加步骤，运单 {0} 不是业务运单。
errAddStepsButStepFixed=添加步骤失败，运单 {0} 已封口，无法添加步骤。
errAdminRequired=需要管理员权限
errAggregateQuery=聚合查询异常: {0}
errAreaNotFoundById=无此区域：{0}
errArmContainerForUnload=准备卸货叉上的货物，但准备卸的货物与货叉的货物不一致
errArmEmptyForUnload=准备卸货叉上的货，但货叉无货
errArmNotEmptyForLoad=准备取货，但货叉上有货物
errArmNotEmptyForUnload=准备卸背篓上的货物，但货叉有货
errBadColumnName=列名无效 "{0}"
errBadHttpMethod=不支持的 Http 方法：{0}
errBadIP=IP 格式不正确："{0}"
errBadReqBodyJson=JSON 格式错误：{0}
errBadRequestTimepointOrTimestamp=不能同时使用时间戳（timestamp）和时间点（timeline）进行查询。
errBinBound=库位已绑定其它容器，库位 {0}，容器 {1}
errBinBoundDifferentPoint=库位 {0} 在不同机器人组的地图里绑定到了不同的点位。请检查机器人组 {1} 和机器人组 {2} 的地图
errBinHasContainerNotToLoad=库位 {0} 上已有容器 {1}，不能再放容器 {2}
errBinNoContainer=要求库位 {0} 上必须有一个容器
errBinNoRobot=库位 "{0}" 未配置机器人位置参数
errBinNotEmpty=库位 {0} 必须是空的、未占用
errBpNoChild=组件未配置子组件 "{0}"
errBpRunError=组件 "{1}" 运行报错：{0}
errBreakLoop=终止循环
errBzError=业务异常：{0}
errBzMaterialCategoryNoStoreDistricts=物料分类 "{0}" 未配置存储库区
errBzMaterialNoCategory=物料 "{0}" 未配置分类
errBzMissingKeyParam=缺少必填参数："{0}"
errBzNoActiveBomForMat=找不到物料 "{0}" 已启用的物料清单
errBzNoEnoughContainerForMaterial=装物料 "{0}" 的空箱不足
errBzNoEnoughEmptyContainer=空容器不足（期望={0}，只有={1}）
errBzNoMaterialById=找不到物料 "{0}"
errBzNoMaterialCategoryById=找不到物料分类 "{0}"
errBzNoMaterialContainerMaxQty=物料 "{0}" 未配置容器容量
errBzNoMaterialContainerMaxQty2=物料 "{0}" 未配置“有效的”容器容量
errBzNoSuchOrderNameId=找不到的单据 "{0}" / "{1}"
errBzOutboundLineShort=第 {0} 行，物料 "{2}"，库存缺 {1} 个
errCancelOrderWhenLoadButNoUnload=机器人 {0} 已取货但未放货时，取消了运单，请将第 {1} 层（{2} 号）背篓里的货物移走，再点击 “人工完成”
errCancelOrderWhenPlusOneLoadButNoUnload=机器人 {0} 已取货但未放货时，取消了运单，请将货叉（999 号）背篓里的货物移走，再点击 “人工完成”
errCannotCreateNewBuiltinTaskDef=不能新建内建猎鹰任务
errCannotExceedStatisticTimeSpan=统计的时间跨度不能超过 {0} 天
errCannotExecuteNonDeviceOperation=不能执行非机构控制的操作
errCannotModifyBuiltinFromFalseToTrue=不能将普通猎鹰任务修改为内建猎鹰任务，任务名={0}
errCannotModifyBuiltinLabel=不能修改内建猎鹰任务的任务名，旧值={0}，新值={1}
errCannotRemoveBuiltinTaskDef=不能删除内建任务模板：{0}
errCannotRequestControl=机器人 {0} 在导航中或急停或控制权已被抢占，不能去获取控制权
errCodeErr=程序错误：{0}
errColumnEndMustPosInt=结束列号必须时正整数
errColumnStartEQColumnEnd=列禁用时，起始列号必须等于结束列号
errColumnStartGtColumnEnd=起始列号不能大于结束列号
errColumnStartMustPosInt=起始列号必须时正整数
errComplexQueryBadOp=不支持的运算符 "{0}"
errComplexQueryInMultiple=查询需要指定多个值
errComplexQueryMissingField1=查询需要指明"字段1"
errComplexQueryMissingOp=查询需要运算符
errComplexQueryNotSupportField={0} 类型的字段 {1}|{2} 不支持 {3} 操作符
errComplexQueryUnknownType=不支持的查询类型 "{0}"
errComplexQueryValueNeedTwo=字段 "{0}" 需要两个查询值
errComplexQueryValueNotString=字段 "{0}" 查询值不是字符串
errComplexQueryValueNull=字段 "{0}" 查询值为空
errContainerBadCurrentBin=容器 {0} 当前在库位 {1}，不在 {2}
errContainerBound=容器已绑定其它库位，容器 {0}，库位 {1}
errContainerLocked=容器 "{0}" 可能正在被使用，请尝试其他容器（锁定=true）
errContainerNoBin=容器 "{0}" 当前库位为空
errContainerNoBinMissingFromBin=查询不到容器 {0} 当前库位，必须指定一个库位作为起点
errContainerNoType=容器 "{0}" 未设置类型
errContainerNotFound=找不到容器，容器 ID = {0}
errContainerOrFromBinSpecified=至少指定容器或起点库位
errContainerTypeNoStoreDistricts=容器类型 "{0}" 未配置存储区
errDbProcessing=正在处理数据库，请稍后再试
errDbRestoreNoInfo=数据文件不正确（缺 info.txt）
errDeleteStepsButNoRobot=删除运单步骤失败，场景中没有执行运单 {0} 的机器人 {1}
errDeleteStepsButOrderIdNotMatch=删除运单步骤失败，步骤 {0} 不属于运单 {1}
errDeleteStepsButStepExecuting=删除运单步骤失败，机器人 {0} 正在执行运单 {1} 的步骤 {2}。
errDeleteStepsButStepSelected=删除运单步骤失败，机器人 {0} 即将执行运单 {1} 的步骤 {2}。
errDepthEndMustPosInt=结束深号必须时正整数
errDepthStartEQDepthEnd=深禁用时，起始深号必须等于结束深号
errDepthStartGtDepthEnd=起始深度不能大于结束深度
errDepthStartMustPosInt=起始深号必须时正整数
errDingBadConfig=钉钉登录配置不全
errDingCallErr=钉钉请求失败，代码={0}
errDingNotEnabled=未启用钉钉
errDingShuNoCode=回调无 code
errDirFilesNull=列出目录下的文件失败
errDirNotExists=目录不存在：{0}
errDirectOutboundSubmitFail=直接出库提交失败。原因：{0}
errDirectorOutboundBinNotOccupied=库位 {0} 是空的。（直接出库）
errDirectorOutboundEmptyLayouts=可出库的库存明细为空。（直接出库）
errDirectorOutboundNoOrder=直接出库未配置承接单据
errDistrictIdsEmpty=需要指定至少一个库区
errDuplicateBinName=地图 {0} 内存在同名库位：{1}。请修改库位名称
errDuplicateBinNameInDifferentAreas=不同区域不允许有同名库位：区域 {0} 和区域 {1} 有同名库位 {2}
errDuplicateManilaFilterCaseName=重复的统计方案名：{0}
errDuplicatePointName=不同区域不允许有同名点位（SM 点除外）：区域 {0} 和区域 {1} 有同名点位 {2}
errDuplicateSerialNo=不同区域不允许有编号相同的点位：区域 {0} 有点位 {1}，区域 {2} 有点位 {3}，编号都是 {4}
errDuplicatedKeyError="{0}" 的 "{1}" 字段值不能重复，重复值={2}
errEmptyEntityValue=业务对象 "{0}" 值为空
errEmptyPassword=密码不能为空
errEntityNoField=业务对象 "{0}" 没有字段 "{1}"
errEntityRequestMissingIdOrQueryParam=需要指定 "id" 或 "query" 参数
errEntityRequestMissingQueryParam=查询需要指定 "query" 参数
errExceedingMaxDecimalLength=业务对象 {0} 的字段值 {1} 超过小数部分允许的最大长度 {2}
errExceedingMaxIntegerLength=业务对象 {0} 的字段值 {1} 超过整数部分允许的最大长度 {2}
errExceedingMaxValue=业务对象 {0} 的字段值 {1} 超过允许的最大值 {2}
errExceedingMinValue=业务对象 {0} 的字段值 {1} 超过允许的最小值 {2}
errExistBgPushTask=已经存在后台推送任务，请先取消再重试
errExistInconsistentElement=不同地图间存在不一致的{0}：{1}
errFSNotEnabled=未启用飞书
errFalconBlockInputParamNotList=组件 "{0}" 输入参数 "{1}" 需要是数组
errFalconBlockInputParamRangeError=组件 "{0}" 输入参数 "{1}" 范围错误
errFalconBlockOptionParamError=组件 "{0}" 选项参数 "{1}" 错误
errFalconCreateTaskNoDefId=无法创建任务，未指定模板
errFalconExpressionError=表达式求值错误。表达式 "{0}"。详情：{1}
errFalconMissingBlockInputParam=组件 "{0}" 缺少输入参数 "{1}"
errFalconRecordRemoveRunning=有正在运行的任务，无法删除，猎鹰任务编号：{0}
errFalconThrowPrefix=错误：{0}
errFeiShuBadConfig=飞书登录配置不全
errFeiShuCallErr=飞书请求失败，代码={0}
errFeiShuNoCode=回调无 code
errFieldTextTooLong=字段 "{0}.{1}" 的内容长度 {2} 超过最大限制 {3}
errFieldTypeMismatchWithSQLDataType=业务对象 {0} 字段值 {1} 的字段类型 {2} 与 SQL 数据类型 {3} 不匹配
errFileNotDirectory=文件不是目录：{0}
errFileNotExists=文件不存在：{0}
errFileNotInDir=文件 "{0}" 不在目录 "{1}" 中
errGwNoRobot=网关中未配置机器人 "{0}"
errHttpFail=HTTP 请求失败，HTTP 响应码={0}，正文={1}
errHttpResponseBodyEmpty=HTTP 请求响应为空
errIllegalPeriodTime=非法的时间参数，周期类型 {0} 时间数值 {1}
errInconsistentQuantity=合并地图与组地图的 {0} 数量不一致
errIndexFieldDuplicated=索引的字段 "{0}" 重复
errIndexFieldLenTooLong=索引 {0} 的字段总长度超过数据库限制 {1}
errIndexNameDuplicated=索引的名称 "{0}" 重复
errInitializeScheduler=定时任务初始化失败！"{0}"
errInterruptedException=执行被中断
errInvLayoutNoContainer=库存明细上没有容器 {0}
errInvShort=库存不足：{0}
errInvShort2=库存不足，物料 {0}，待出库={1}，库存={2}
errLayerEndMustPosInt=结束层号必须时正整数
errLayerStartEQLayerEnd=层禁用时，起始层号必须等于结束层号
errLayerStartGtLayerEnd=起始层号不能大于结束层号
errLayerStartMustPosInt=起始层号必须时正整数
errLoadContainerToSelfBinFail=将货物 {0} 放到机器人的{1}中失败
errLockBinFailed=锁定库位 "{0}" 失败，之前已被锁定
errManualCancelOrder=人工取消运单 {0}
errMissingHttpPathParam=缺少路径参数：{0}
errMissingHttpQueryParam=缺少 HTTP 查询参数：{0}
errMissingIdField=业务对象 {1} ({0}) 缺少 id 字段
errMissingParam=缺少参数：{0}
errModifyOffDuty=机器人 {0} 空闲时才能修改为不接单
errModifyProhibition={0}
errModifyProhibitionDefaultMsg=当前选中的数据所在状态不允许修改。
errModifyRobotConfig=机器人 {0} 空闲时才能修改配置
errMrNoCurrentOrder=机器 "{0}" 当前没有运单
errMrNoCurrentStep=机器人 "{0}" 当前没有正在执行的运单步骤
errMrNoOrder=运单不存在或已执行完成 "{0}"
errMrUpdateOrderBadStatus=运单已不允许更新。运单="{0}"，当前状态={1}
errMySqlQueryListNotSupport=MySQL 暂不支持查询多值字段 {0}|{1}。
errNameConflict=名称冲突：{0}
errNoBin=未找到库位 "{0}"
errNoBinById=找不到库位 "{0}"
errNoBinForOrder=机器人 "{0}" 上找不到运单 "{1}" 关联的库位
errNoBinRobotArgs=缺动作参数，库位="{0}"，动作="{1}"
errNoBpType=找不到组件 "{0}"
errNoConfig=未指定配置
errNoDeviceHost=设备 地址/IP 未配置
errNoDeviceType=设备类型未配置
errNoDistrictById=找不到库区 "{0}"
errNoEmptyBinInDistrict=库区内没有空库位
errNoFalconGlobalVariable=找不到全局控制变量 {0}
errNoFalconTaskDefById=找不到任务模板，模板ID={0}
errNoJsonNode=不存在 JsonNode
errNoLockById=该库位未被锁定 "{0}"
errNoMongoDB=没有可用的 MongoDB
errNoOrderLine=无单行，单号={0}
errNoRoutePath=找不到可达路径，从 {0} 到 {1}
errNoScriptFunctionProvided=未指定脚本函数
errNoSqlDb=SQL 数据源不可用
errNoSuchContainerById=找不到容器 "{0}"
errNoSuchContainerTypeById=找不到容器类型 "{0}"
errNoSuchDistrictById=找不到库区 "{0}"
errNoSuchEntity=找不到业务对象 "{0}"
errNoSuchScriptFunction=找不到脚本函数 "{0}"
errNoSuchUserById=找不到用户。id="{0}"
errNoTaskDefByLabel=找不到任务模板，模板名={0}
errNoUiDir=找不到界面文件目录：{0}
errNoUpOrderConfig=找不到上游单据配置。上游单据={1}，下游单据={0}
errNoUploadedFile=上传的文件要放在表单字段中："{0}"
errNoUserUsername=找不到用户。用户名="{0}"
errNoWidthTooSmall=编号宽度过小
errNonDecimal=业务对象 {0} 的字段值 {1} 不是小数
errNonInteger=业务对象 {0} 的字段值 {1} 不是整数
errNumOverflow=值 {0} 超过数字类型范围 {1}~{2}
errOrderNoPushConfig=找不到单据下推配置：{0}
errOrderStepExecutingFail=执行运单 {0} 步骤 {1} 失败
errParseJsonFile=解析 JSON 文件失败
errParseJsonString=解析 JSON 字符串失败，值为：{0}
errPasswordNotMatch=密码错误
errPointOutOfDistance=点位 {0} 在机器人组 {1} 和机器人组 {2} 的地图中，距离过远（不能超过 {3} 米）
errPutOnContainerMissingContainerBin=容器上架，必须指定容器
errPwdErrExceedLimit=密码错误超过限制，请联系管理员重置或 {0} 秒后重试
errPwdExpired=密码已过期，请联系管理员重置
errPwdLengthExceedLimit=密码长度不符合要求，要求为 {0} 到 {1} 位
errPwdStrengthNotAllow=密码强度不符合要求，当前密码强度要求为：{0}，要求为 {1}
errRecoverBadExternalCallAsync=只能重试取消或终止的异步调用
errRefFieldNoRefEntity=字段 "{0}" 未配置 "引用业务对象"
errRemoveProhibition={0}
errRemoveProhibitionDefaultMsg=当前选中的数据所在状态不允许删除。
errRetryFailedRobotButOrderNotExecuting=重启故障机器人，但当前运单 {0} 状态不是执行中，而是 {1}
errRetryFaultOrderFail=故障重试运单 {0} 失败，原因：{1}
errRetryFaultOrderNoRobot=故障重试失败，场景 {0} 中不存在执行运单 {1} 的机器人 {2}
errRobotNotFailed=机器人 "{0}" 无故障
errRowEndMustPosInt=结束排号必须时正整数
errRowStartEQRowEnd=排禁用时，起始排号必须等于结束排号
errRowStartGtRowEnd=起始排号不能大于结束排号
errRowStartMustPosInt=起始排号必须时正整数
errScriptBadReturnNull=脚本函数 {0} 返回值异常，不能为空
errScriptBuildError=构建脚本源码失败
errScriptCloseContextError=关闭上下文报错
errScriptDisposeError=销毁脚本系统报错
errScriptEntityRw=脚本业务对象读写报错：{0}
errScriptExt=脚本报错：{0}
errScriptReturnNotString=脚本函数 "{0}" 返回值不是字符串：{1}
errScriptStartError=脚本启动失败
errSetToStatementInvalidJavaClass=setToStatement 不支持的参数类型 "{0}"
errSignInNoPassword=需要密码
errSimpleScriptBp=执行简易脚本失败：{0}
errSingletonEntityBadOp=单例业务对象 "{0}" 不能调用 "{1}"
errSqlEmptyWhereNotAllowed=禁止全部更新
errSqlUniqueConstraintViolation=值重复了。值={2}，表={0}，索引={1}。
errStartedOnLtFinishedOn=开始时间必须小于结束时间
errStepCannotBeEmpty=步骤不能为空
errStepIndexCannotBeEmpty=步骤索引不能为空
errSubmitEmptyStepWhenSealed=不能提交了一个已封口但步骤为空的运单
errSyncKeyMultiple=多值字段 "{0}" 不能作为业务主键
errSyncKeyNotUniqInCurrent=在原业务对象列表中，键并不唯一："{0}"
errSyncKeyNotUniqInNew=在新业务对象列表中，键并不唯一："{0}"
errTakeOffContainerBadBin=要求从库位 "{1}" 下架容器 "{0}"，但系统记录容器目前在库位 "{2}"
errTakeOffContainerBadContainer=要从库位 "{0}" 下架容器 "{1}"，但系统记录库位上的容器是 {2}
errTakeOffContainerContainerBinAtLeastOne=库口容器下架，容器或库位至少指定一个
errTakeOffContainerNoBin=要求从库位 "{1}" 下架容器 "{0}"，但系统记录容器目前不在任何库位上
errTooLongNum=字段 {0} 的值 {1} 太大（位数过多），位数不能超过 {2} 位，整数部分不能超过 {3} 位
errTypesNotEnoughForHeaders=类型数不足
errUnbindBinContainerBadBind=尝试解绑库位 "{0}" 和容器 "{1}"，但库位上现在的容器是 "{2}"
errUnsupportedDBType=不支持数据库类型：{0}
errUnsupportedDataType=不支持数据类型：{0}
errUnsupportedHttpRequestMethod=HTTP 方法错误 "{0}"
errUpdatePriorityButNotBzOrder=只能修改业务运单的优先级，运单 {0} 不是业务运单。
errUpdateStepButStepExecuting=更新运单步骤失败，机器人 {0} 正在执行运单 {1} 的步骤 {2}。
errUpdateStepsButNoRobot=更新运单步骤失败，场景中没有执行运单 {0} 的机器人 {1}
errUpdateStepsButOrderIdNotMatch=更新运单步骤失败，步骤 {0} 不属于运单 {1}
errUpdateStepsButStepSelected=更新运单步骤失败，机器人 {0} 即将执行运单 {1} 的步骤 {2}。
errUserDisabled=账户已被禁用
errWsNotConnected=未连接 [Websocket {0}，状态={1}]
errWsSendFail=发送失败 [Websocket {0}]
errWsSendTimeout=发送超时 [Websocket {0}]
errWwxBadConfig=企业微信登录配置不全
errWwxCallErr=企业微信请求失败，代码={0}
errWwxNoCode=回调无 code
errWwxNotEnabled=未启用企业微信
errXlsFormulaNotSupported=不支持单元格里有公式

[base.falcon]
Falcon_BlockGroup_Basic=基础组件
Falcon_BlockGroup_Bin=库位
Falcon_BlockGroup_BinContainer=容器库位
Falcon_BlockGroup_ConditionAndLoop=条件和循环
Falcon_BlockGroup_Container=容器
Falcon_BlockGroup_ContainerTransport=容器搬运单
Falcon_BlockGroup_Cq=ComplexQuery
Falcon_BlockGroup_CustomBlocks=定制组件
Falcon_BlockGroup_Entity=业务对象
Falcon_BlockGroup_FalconRecord=猎鹰任务
Falcon_BlockGroup_Inv=库存
Falcon_Bp_AbortTaskBp_Input_msg_description=终止猎鹰任务原因
Falcon_Bp_AbortTaskBp_Input_msg_label=报错信息
Falcon_Bp_AbortTaskBp_description=终止猎鹰任务
Falcon_Bp_AbortTaskBp_label=终止任务
Falcon_Bp_BindBinContainerBp_Input_binId_description=库位 ID
Falcon_Bp_BindBinContainerBp_Input_binId_label=库位编号
Falcon_Bp_BindBinContainerBp_Input_checkBinStatus_description=如果库位上绑定了其它容器，则报错
Falcon_Bp_BindBinContainerBp_Input_checkBinStatus_label=检查库位状态
Falcon_Bp_BindBinContainerBp_Input_checkContainerStatus_description=如果容器绑定了其它库位，则报错
Falcon_Bp_BindBinContainerBp_Input_checkContainerStatus_label=检查容器状态
Falcon_Bp_BindBinContainerBp_Input_containerId_description=容器编号
Falcon_Bp_BindBinContainerBp_Input_containerId_label=容器编号
Falcon_Bp_BindBinContainerBp_Input_unlockBin_description=需要同步解锁库位
Falcon_Bp_BindBinContainerBp_Input_unlockBin_label=解锁库位
Falcon_Bp_BindBinContainerBp_Input_unlockContainer_description=需要同步解锁容器
Falcon_Bp_BindBinContainerBp_Input_unlockContainer_label=解锁容器
Falcon_Bp_BindBinContainerBp_description=将库位与容器进行绑定
Falcon_Bp_BindBinContainerBp_label=绑定库位容器
Falcon_Bp_BreakBp_Input_condition_description=条件满足时退出循环
Falcon_Bp_BreakBp_Input_condition_label=条件
Falcon_Bp_BreakBp_description=满足条件会抛出异常，从而退出循环
Falcon_Bp_BreakBp_label=终止循环 Break
Falcon_Bp_CreateInvFromOrderBp_Input_bin_description=库位
Falcon_Bp_CreateInvFromOrderBp_Input_bin_label=库位
Falcon_Bp_CreateInvFromOrderBp_Input_container_description=容器
Falcon_Bp_CreateInvFromOrderBp_Input_container_label=容器
Falcon_Bp_CreateInvFromOrderBp_Input_copyFields_description=多个字段用“,”分隔，会将这些字段以及对应的数据拷贝到库存信息中。
Falcon_Bp_CreateInvFromOrderBp_Input_copyFields_label=拷贝字段
Falcon_Bp_CreateInvFromOrderBp_Input_lotNoFormat_label=库位编号格式
Falcon_Bp_CreateInvFromOrderBp_Input_materialFieldName_description=请输入单据上表示物料的字段名
Falcon_Bp_CreateInvFromOrderBp_Input_materialFieldName_label=物料字段
Falcon_Bp_CreateInvFromOrderBp_Input_order_description=请输入单据对象
Falcon_Bp_CreateInvFromOrderBp_Input_order_label=单据
Falcon_Bp_CreateInvFromOrderBp_Input_qtyFieldName_description=请输入单据上表示数量的字段名
Falcon_Bp_CreateInvFromOrderBp_Input_qtyFieldName_label=数量字段
Falcon_Bp_CreateInvFromOrderBp_Input_state_description=默认的三种存储状态：Received:已收货；Storing:存储中；Assigned:已分配。
Falcon_Bp_CreateInvFromOrderBp_Input_state_label=库存状态
Falcon_Bp_CreateInvFromOrderBp_description=根据单据信息创建库存信息
Falcon_Bp_CreateInvFromOrderBp_label=从单据创建库存
Falcon_Bp_CreateInvFromOrderLinesBp_Input_bin_description=库位
Falcon_Bp_CreateInvFromOrderLinesBp_Input_bin_label=库位
Falcon_Bp_CreateInvFromOrderLinesBp_Input_container_description=容器
Falcon_Bp_CreateInvFromOrderLinesBp_Input_container_label=容器
Falcon_Bp_CreateInvFromOrderLinesBp_Input_copyFields_description=多个字段用“,”分隔，会将这些字段以及对应的数据拷贝到库存信息中。
Falcon_Bp_CreateInvFromOrderLinesBp_Input_copyFields_label=从单行上拷贝以下字段
Falcon_Bp_CreateInvFromOrderLinesBp_Input_materialFieldName_description=请输入单行上表示物料的字段名。
Falcon_Bp_CreateInvFromOrderLinesBp_Input_materialFieldName_label=单行上的物料字段名
Falcon_Bp_CreateInvFromOrderLinesBp_Input_order_description=请输入单据对象
Falcon_Bp_CreateInvFromOrderLinesBp_Input_order_label=单据对象
Falcon_Bp_CreateInvFromOrderLinesBp_Input_qtyFieldName_description=请输入单行上表示数量的字段名。
Falcon_Bp_CreateInvFromOrderLinesBp_Input_qtyFieldName_label=单行上的数量字段名
Falcon_Bp_CreateInvFromOrderLinesBp_Input_state_description=默认的三种存储状态：Received:已收货；Storing:存储中；Assigned:已分配。
Falcon_Bp_CreateInvFromOrderLinesBp_Input_state_label=存储状态
Falcon_Bp_CreateInvFromOrderLinesBp_description=根据单行信息创建库存信息
Falcon_Bp_CreateInvFromOrderLinesBp_label=从单据单行创建库存
Falcon_Bp_CreateTraceContainerBp_Input_containerType_description=请输入 M4 记录的容器类型。
Falcon_Bp_CreateTraceContainerBp_Input_containerType_label=容器类型
Falcon_Bp_CreateTraceContainerBp_Output_containerId_description=新建的容器编号。
Falcon_Bp_CreateTraceContainerBp_Output_containerId_label=容器编号
Falcon_Bp_CreateTraceContainerBp_description=创建追踪容器。
Falcon_Bp_CreateTraceContainerBp_label=创建追踪容器
Falcon_Bp_DelayBp_Input_timeMillis_description=延迟时间(毫秒)
Falcon_Bp_DelayBp_Input_timeMillis_label=时间（毫秒）
Falcon_Bp_DelayBp_description=用来延迟执行后面的模块，延迟多久根据输入的参数
Falcon_Bp_DelayBp_label=延迟
Falcon_Bp_ExpressionBp_Input_expression_description=表达式
Falcon_Bp_ExpressionBp_Input_expression_label=表达式
Falcon_Bp_ExpressionBp_Output_expResult_description=表达式的结果
Falcon_Bp_ExpressionBp_Output_expResult_label=表达式结果
Falcon_Bp_ExpressionBp_description=根据输入的表达式来计算结果
Falcon_Bp_ExpressionBp_label=表达式求值
Falcon_Bp_FindEmptyContainerBp_Input_districtIds_description=库区编号。请根据需求，输入 M4 记录的库区编号。
Falcon_Bp_FindEmptyContainerBp_Input_districtIds_label=库区 Id
Falcon_Bp_FindEmptyContainerBp_Input_keepTrying_description=禁用：(默认)只查找一次；启用：一直查找，直到找到空容器。
Falcon_Bp_FindEmptyContainerBp_Input_keepTrying_label=重试直到成功
Falcon_Bp_FindEmptyContainerBp_Input_sort_description=目标库区内库位的排序方式。
Falcon_Bp_FindEmptyContainerBp_Input_sort_label=排序
Falcon_Bp_FindEmptyContainerBp_Output_binId_description=存放目标空容器的库位的编号。
Falcon_Bp_FindEmptyContainerBp_Output_binId_label=库位编号
Falcon_Bp_FindEmptyContainerBp_Output_containerId_description=被找到的空容器的编号。
Falcon_Bp_FindEmptyContainerBp_Output_containerId_label=容器编号
Falcon_Bp_FindEmptyContainerBp_Output_found_description=true:已找到；false:未找到。
Falcon_Bp_FindEmptyContainerBp_Output_found_label=是否找到找空容器
Falcon_Bp_FindEmptyContainerBp_description=按照期望的规则，从目标库区寻找空容器，并锁定此容器。
Falcon_Bp_FindEmptyContainerBp_label=找空容器
Falcon_Bp_FindFalconRecordFieldBp_Input_fieldName_label=读取字段
Falcon_Bp_FindFalconRecordFieldBp_Output_fieldValue_label=读取值
Falcon_Bp_FindFalconRecordFieldBp_label=读取任务记录字段
Falcon_Bp_FindFieldValueByIdBp_Input_entityName_description=业务对象名
Falcon_Bp_FindFieldValueByIdBp_Input_entityName_label=业务对象名
Falcon_Bp_FindFieldValueByIdBp_Input_field_description=指定查询的字段
Falcon_Bp_FindFieldValueByIdBp_Input_field_label=字段
Falcon_Bp_FindFieldValueByIdBp_Input_id_description=业务对象实体 id
Falcon_Bp_FindFieldValueByIdBp_Input_id_label=业务对象 id
Falcon_Bp_FindFieldValueByIdBp_Output_found_description=业务对象实体是否存在
Falcon_Bp_FindFieldValueByIdBp_Output_found_label=业务对象存在
Falcon_Bp_FindFieldValueByIdBp_Output_value_description=查询字段值
Falcon_Bp_FindFieldValueByIdBp_Output_value_label=值
Falcon_Bp_FindFieldValueByIdBp_description=根据 id 查找业务对象实体的指定字段
Falcon_Bp_FindFieldValueByIdBp_label=根据 id 查找业务对象的字段
Falcon_Bp_FindNotOccupiedBinBp_Input_cq_description=寻找空库位的条件
Falcon_Bp_FindNotOccupiedBinBp_Input_cq_label=条件
Falcon_Bp_FindNotOccupiedBinBp_Input_districtIds_description=库区列表
Falcon_Bp_FindNotOccupiedBinBp_Input_districtIds_label=库区列表
Falcon_Bp_FindNotOccupiedBinBp_Input_keepTrying_description=为 true 时重复寻找空库位直到成功，为 false 则只找一次
Falcon_Bp_FindNotOccupiedBinBp_Input_keepTrying_label=反复尝试至成功
Falcon_Bp_FindNotOccupiedBinBp_Input_sort_description=排序规则
Falcon_Bp_FindNotOccupiedBinBp_Input_sort_label=排序
Falcon_Bp_FindNotOccupiedBinBp_Output_binId_description=找到的库位 ID
Falcon_Bp_FindNotOccupiedBinBp_Output_binId_label=库位 ID
Falcon_Bp_FindNotOccupiedBinBp_Output_found_description=true 为找到了，false 为未找到
Falcon_Bp_FindNotOccupiedBinBp_Output_found_label=是否找到
Falcon_Bp_FindNotOccupiedBinBp_description=在库区中寻找空库位
Falcon_Bp_FindNotOccupiedBinBp_label=寻空库位
Falcon_Bp_FindOneBp_Input_entityName_label=实体
Falcon_Bp_FindOneBp_Input_field_label=字段
Falcon_Bp_FindOneBp_Input_value_label=Value
Falcon_Bp_FindOneBp_label=查单条记录
Falcon_Bp_FindOneEntityByIdBp_Input_entityName_description=业务对象名
Falcon_Bp_FindOneEntityByIdBp_Input_entityName_label=业务对象名
Falcon_Bp_FindOneEntityByIdBp_Input_id_description=业务对象实体 id
Falcon_Bp_FindOneEntityByIdBp_Input_id_label=业务对象 id
Falcon_Bp_FindOneEntityByIdBp_Output_ev_description=业务对象实体详情
Falcon_Bp_FindOneEntityByIdBp_Output_ev_label=业务对象
Falcon_Bp_FindOneEntityByIdBp_Output_found_description=是否找到
Falcon_Bp_FindOneEntityByIdBp_Output_found_label=是否找到
Falcon_Bp_FindOneEntityByIdBp_description=根据 id 查找业务对象实体
Falcon_Bp_FindOneEntityByIdBp_label=根据 id 查找业务对象
Falcon_Bp_GetBinContainerBp_Input_binId_description=库位 ID
Falcon_Bp_GetBinContainerBp_Input_binId_label=库位编号
Falcon_Bp_GetBinContainerBp_Output_binEmpty_description=容器是否为空，true 为空
Falcon_Bp_GetBinContainerBp_Output_binEmpty_label=库位是否为空
Falcon_Bp_GetBinContainerBp_Output_containerId_description=容器编号
Falcon_Bp_GetBinContainerBp_Output_containerId_label=容器编号
Falcon_Bp_GetBinContainerBp_description=获获取库位上的容器，获取的前提是之前库位与容器绑定了，如果获取不到，容器编号返回 null
Falcon_Bp_GetBinContainerBp_label=获取库位上的容器
Falcon_Bp_GetContainerBinBp_Input_containerId_description=容器编号
Falcon_Bp_GetContainerBinBp_Input_containerId_label=容器编号
Falcon_Bp_GetContainerBinBp_Output_binId_description=库位编号
Falcon_Bp_GetContainerBinBp_Output_binId_label=库位编号
Falcon_Bp_GetContainerBinBp_Output_found_description=是否找到，true 为找到
Falcon_Bp_GetContainerBinBp_Output_found_label=是否找到
Falcon_Bp_GetContainerBinBp_description=获取容器所在的库位，获取的前提是之前库位与容器绑定了，如果获取不到，库位 ID 返回 null
Falcon_Bp_GetContainerBinBp_label=获取容器所在库位
Falcon_Bp_GetContainerInvBp_Input_containerId_description=请输入 M4 记录的容器编号
Falcon_Bp_GetContainerInvBp_Input_containerId_label=容器编号
Falcon_Bp_GetContainerInvBp_Output_found_description=true:已找到；false:未找到。
Falcon_Bp_GetContainerInvBp_Output_found_label=是否找到
Falcon_Bp_GetContainerInvBp_Output_inv_description=库存明细单。
Falcon_Bp_GetContainerInvBp_Output_inv_label=库存明细单
Falcon_Bp_GetContainerInvBp_description=根据容器编号获取对应的库存明细。
Falcon_Bp_GetContainerInvBp_label=获取容器内库存明细
Falcon_Bp_GetContainerTypeStoreDistrictsBp_Input_containerId_description=请输入 M4 记录的容器的编号。
Falcon_Bp_GetContainerTypeStoreDistrictsBp_Input_containerId_label=容器编号
Falcon_Bp_GetContainerTypeStoreDistrictsBp_Output_storeDistricts_description=库区名称的集合，数组。
Falcon_Bp_GetContainerTypeStoreDistrictsBp_Output_storeDistricts_label=存储区
Falcon_Bp_GetContainerTypeStoreDistrictsBp_description=根据容器编号，查询可以存放此类容器的库区的名称。
Falcon_Bp_GetContainerTypeStoreDistrictsBp_label=查询容器的类型的存储区
Falcon_Bp_IfBp_Input_condition_description=判断条件
Falcon_Bp_IfBp_Input_condition_label=条件
Falcon_Bp_IfBp_description=条件判断，仅在条件满足时才可以继续执行子模块
Falcon_Bp_IfBp_label=如果 IF
Falcon_Bp_IfElseBp_Input_condition_description=判断条件
Falcon_Bp_IfElseBp_Input_condition_label=条件
Falcon_Bp_IfElseBp_description=条件判断，满足条件时走true 的子模块，条件不满足时走 false 的子模块
Falcon_Bp_IfElseBp_label=如果否则 IF ELSE
Falcon_Bp_IterateListBp_Context_index_description=索引
Falcon_Bp_IterateListBp_Context_item_description=单条数据信息
Falcon_Bp_IterateListBp_Input_list_description=遍历的列表
Falcon_Bp_IterateListBp_Input_list_label=列表
Falcon_Bp_IterateListBp_Input_parallel_description=判断是否并行遍历
Falcon_Bp_IterateListBp_Input_parallel_label=并行
Falcon_Bp_IterateListBp_description=遍历数组，可支持并行
Falcon_Bp_IterateListBp_label=遍历数组
Falcon_Bp_KeepTryingLockBinBp_Input_binId_description=库位 ID
Falcon_Bp_KeepTryingLockBinBp_Input_binId_label=库位
Falcon_Bp_KeepTryingLockBinBp_Input_reason_description=锁定的原因
Falcon_Bp_KeepTryingLockBinBp_Input_reason_label=原因
Falcon_Bp_KeepTryingLockBinBp_description=反复尝试锁定库位直到成功
Falcon_Bp_KeepTryingLockBinBp_label=反复尝试锁定库位直到成功
Falcon_Bp_KeepTryingLockNotOccupiedBinBp_Input_binId_description=库位 ID
Falcon_Bp_KeepTryingLockNotOccupiedBinBp_Input_binId_label=库位编号
Falcon_Bp_KeepTryingLockNotOccupiedBinBp_description=等待库位为空并锁定
Falcon_Bp_KeepTryingLockNotOccupiedBinBp_label=等待库位为空并锁定
Falcon_Bp_LockBinOnceBp_Input_binId_label=库位编号
Falcon_Bp_LockBinOnceBp_Input_notFoundToAborted_description=失败是否需要让任务终止，true 为需要
Falcon_Bp_LockBinOnceBp_Input_notFoundToAborted_label=找不到终止任务
Falcon_Bp_LockBinOnceBp_Input_notFoundToFault_description=失败是否需要让任务故障，true 为需要
Falcon_Bp_LockBinOnceBp_Input_notFoundToFault_label=找不到让任务故障
Falcon_Bp_LockBinOnceBp_Output_ok_description=是否锁定成功，成功为 true，失败为 false
Falcon_Bp_LockBinOnceBp_Output_ok_label=是否锁定成功
Falcon_Bp_LockBinOnceBp_description=尝试锁定一次未锁定的库位，成功返回 true，否则返回 false
Falcon_Bp_LockBinOnceBp_label=锁定未锁定的库位
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_newState_Option_Cancelled=取消
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_newState_Option_Done=完成
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_newState_Option_Failed=失败
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_newState_description=期望更新的状态
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_newState_label=更新的状态
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_orderId_description=容器搬运单的单号，唯一标识
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_orderId_label=单号
Falcon_Bp_MarkContainerTransportOrderDoneBp_description=根据单号更新容器搬运单的状态
Falcon_Bp_MarkContainerTransportOrderDoneBp_label=标记容器搬运单完成
Falcon_Bp_MoveInvByContainerBp_Input_leafContainerId_description=请输入最内层容器的编号。
Falcon_Bp_MoveInvByContainerBp_Input_leafContainerId_label=最内层容器
Falcon_Bp_MoveInvByContainerBp_Input_state_description=将库存状态修改为指定的库存状态
Falcon_Bp_MoveInvByContainerBp_Input_state_label=指定库存状态
Falcon_Bp_MoveInvByContainerBp_Input_toBinId_description=将要放置最内层容器的库位的编号。
Falcon_Bp_MoveInvByContainerBp_Input_toBinId_label=终点库位
Falcon_Bp_MoveInvByContainerBp_description=将最内层容器信息，绑定到另一个库位上。
Falcon_Bp_MoveInvByContainerBp_label=按容器移动库存
Falcon_Bp_PadIntStrBp_Input_numLength_description=数字转字符串后，若指定长度小于原长度，则忽略指定长度；否则用0补齐
Falcon_Bp_PadIntStrBp_Input_numLength_label=数字长度
Falcon_Bp_PadIntStrBp_Input_num_description=需要转换的数字
Falcon_Bp_PadIntStrBp_Input_num_label=数字
Falcon_Bp_PadIntStrBp_Input_prefix_description=转换后字符串前缀
Falcon_Bp_PadIntStrBp_Input_prefix_label=前缀
Falcon_Bp_PadIntStrBp_Input_suffix_description=转换后字符串后缀
Falcon_Bp_PadIntStrBp_Input_suffix_label=后缀
Falcon_Bp_PadIntStrBp_Output_numStr_description=数字转换后的字符串
Falcon_Bp_PadIntStrBp_Output_numStr_label=数字转换后的字符串
Falcon_Bp_PadIntStrBp_description=根据条件，将数字转换为指定长度的字符串，不足则补0
Falcon_Bp_PadIntStrBp_label=将数字转换为指定长度的字符串
Falcon_Bp_ParallelFlowBp_description=并行执行
Falcon_Bp_ParallelFlowBp_label=并行执行
Falcon_Bp_PrintBp_Input_message_description=需要打印的信息
Falcon_Bp_PrintBp_Input_message_label=消息
Falcon_Bp_PrintBp_description=打印
Falcon_Bp_PrintBp_label=打印
Falcon_Bp_ReduceBinInvFromOrderBp_Input_binField_description=请输入单行上表示库位的字段名。
Falcon_Bp_ReduceBinInvFromOrderBp_Input_binField_label=库位字段
Falcon_Bp_ReduceBinInvFromOrderBp_Input_ev_description=请输入单据信息
Falcon_Bp_ReduceBinInvFromOrderBp_Input_ev_label=单据
Falcon_Bp_ReduceBinInvFromOrderBp_Input_materialField_description=请输入单行上表示物料的字段名
Falcon_Bp_ReduceBinInvFromOrderBp_Input_materialField_label=物料字段
Falcon_Bp_ReduceBinInvFromOrderBp_Input_outboundOrderIdField_label=出库单号
Falcon_Bp_ReduceBinInvFromOrderBp_Input_pickOrderIdField_label=分拣单号
Falcon_Bp_ReduceBinInvFromOrderBp_Input_qtyField_description=请输入单行上表示数量的字段名。
Falcon_Bp_ReduceBinInvFromOrderBp_Input_qtyField_label=数量字段
Falcon_Bp_ReduceBinInvFromOrderBp_Input_subContainerIdField_label=格子
Falcon_Bp_ReduceBinInvFromOrderBp_description=按单据从库位删除库存
Falcon_Bp_ReduceBinInvFromOrderBp_label=按单据从库位删除库存
Falcon_Bp_RemoveInvByContainerBp_Input_containerId_description=请输入 M4 记录的容器编号。
Falcon_Bp_RemoveInvByContainerBp_Input_containerId_label=容器编号
Falcon_Bp_RemoveInvByContainerBp_Output_count_description=被删除的库存明细数量。
Falcon_Bp_RemoveInvByContainerBp_Output_count_label=明细数量
Falcon_Bp_RemoveInvByContainerBp_description=根据容器编号删除对应的库存明细。
Falcon_Bp_RemoveInvByContainerBp_label=按容器删除库存明细
Falcon_Bp_RemoveTraceContainerBp_Input_containerId_description=请输入 M4 记录的追踪容器的编号。
Falcon_Bp_RemoveTraceContainerBp_Input_containerId_label=容器编号
Falcon_Bp_RemoveTraceContainerBp_description=删除追踪容器。
Falcon_Bp_RemoveTraceContainerBp_label=删除追踪容器
Falcon_Bp_RepeatNumBp_Child_default_label=组件
Falcon_Bp_RepeatNumBp_Context_index_description=序号
Falcon_Bp_RepeatNumBp_Context_index_label=序号
Falcon_Bp_RepeatNumBp_Input_num_description=子组件重复执行次数
Falcon_Bp_RepeatNumBp_Input_num_label=执行次数
Falcon_Bp_RepeatNumBp_description=子组件重复执行
Falcon_Bp_RepeatNumBp_label=重复执行 Repeat
Falcon_Bp_SerialFlowBp_description=串行执行
Falcon_Bp_SerialFlowBp_label=串行执行
Falcon_Bp_SetBinEmptyBp_Input_binId_description=库位 ID
Falcon_Bp_SetBinEmptyBp_Input_binId_label=库位编号
Falcon_Bp_SetBinEmptyBp_Input_noContainer_description=true 为设置该库位为无容器
Falcon_Bp_SetBinEmptyBp_Input_noContainer_label=无容器
Falcon_Bp_SetBinEmptyBp_Input_notOccupied_description=true 为设置库位为未占用
Falcon_Bp_SetBinEmptyBp_Input_notOccupied_label=未占用
Falcon_Bp_SetBinEmptyBp_Input_unlock_label=解锁
Falcon_Bp_SetBinEmptyBp_Input_unlock_label_description=true 为解锁该库位
Falcon_Bp_SetBinEmptyBp_description=设置库位未占用（弃用）
Falcon_Bp_SetBinEmptyBp_label=设置库位未占用（弃用）
Falcon_Bp_SetBinNotOccupiedBp_Input_binId_description=库位 ID
Falcon_Bp_SetBinNotOccupiedBp_Input_binId_label=库位编号
Falcon_Bp_SetBinNotOccupiedBp_Input_unlockAfter_description=true 为设置未占用并解锁库位，false 为设置未占用不解锁库位
Falcon_Bp_SetBinNotOccupiedBp_Input_unlockAfter_label=解锁
Falcon_Bp_SetBinNotOccupiedBp_description=设置库位未占用
Falcon_Bp_SetBinNotOccupiedBp_label=设置库位未占用
Falcon_Bp_SetBinUnLockBp_Input_binId_label=库位编号
Falcon_Bp_SetBinUnLockBp_Input_requireLock_description=是否要求该库位为锁定库位 未锁定则异常终止
Falcon_Bp_SetBinUnLockBp_Input_requireLock_label=要求该库位为锁定库位
Falcon_Bp_SetBinUnLockBp_description=解锁指定库位
Falcon_Bp_SetBinUnLockBp_label=解锁库位
Falcon_Bp_SetGlobalVariableBp_Input_varName_description=填变量名，不是变量值
Falcon_Bp_SetGlobalVariableBp_Input_varName_label=变量名
Falcon_Bp_SetGlobalVariableBp_Input_varValue_description=数据类型要与全局变量匹配
Falcon_Bp_SetGlobalVariableBp_Input_varValue_label=变量值
Falcon_Bp_SetGlobalVariableBp_description=只能改全局变量的值，不能新增全局变量。全局变量中没有指定的 name 时，会抛异常
Falcon_Bp_SetGlobalVariableBp_label=设置全局变量
Falcon_Bp_SetTaskVariableBp_Input_varName_description=任务变量的名称
Falcon_Bp_SetTaskVariableBp_Input_varName_label=变量名称
Falcon_Bp_SetTaskVariableBp_Input_varValue_description=任务变量的值
Falcon_Bp_SetTaskVariableBp_Input_varValue_label=变量值
Falcon_Bp_SetTaskVariableBp_description=设置任务变量
Falcon_Bp_SetTaskVariableBp_label=设置任务变量
Falcon_Bp_SimpleScriptBp_Input_scriptStr_label=脚本源码
Falcon_Bp_SimpleScriptBp_Output_scriptOut_description=执行脚本的返回值
Falcon_Bp_SimpleScriptBp_Output_scriptOut_label=返回值
Falcon_Bp_SimpleScriptBp_label=简易脚本
Falcon_Bp_SuiBp_Input_config_description=配置操作的用户
Falcon_Bp_SuiBp_Input_config_label=配置
Falcon_Bp_SuiBp_Output_button_description=用户点击的按钮
Falcon_Bp_SuiBp_Output_button_label=用户点击的按钮
Falcon_Bp_SuiBp_Output_input_description=用户的输入参数
Falcon_Bp_SuiBp_Output_input_label=用户的输入
Falcon_Bp_SuiBp_description=等待用户操作
Falcon_Bp_SuiBp_label=用户介入
Falcon_Bp_ThrowBp_Input_condition_description=是否抛出异常
Falcon_Bp_ThrowBp_Input_condition_label=条件
Falcon_Bp_ThrowBp_Input_errMsg_description=抛出异常信息
Falcon_Bp_ThrowBp_Input_errMsg_label=异常信息
Falcon_Bp_ThrowBp_description=抛出异常
Falcon_Bp_ThrowBp_label=抛出异常
Falcon_Bp_TimestampBp_Input_formatDate_description=指定日期格式
Falcon_Bp_TimestampBp_Input_formatDate_label=日期格式
Falcon_Bp_TimestampBp_Output_timestamp_description=返回指定日期格式的字符串
Falcon_Bp_TimestampBp_Output_timestamp_label=当前时间戳
Falcon_Bp_TimestampBp_description=返回指定格式的当前时间字符串
Falcon_Bp_TimestampBp_label=返回当前时间
Falcon_Bp_TriggerTaskEventBp_Input_eventData_description=事件的数据
Falcon_Bp_TriggerTaskEventBp_Input_eventData_label=事件数据
Falcon_Bp_TriggerTaskEventBp_Input_eventName_description=事件的名称
Falcon_Bp_TriggerTaskEventBp_Input_eventName_label=事件名称
Falcon_Bp_TriggerTaskEventBp_description=触发任务事件
Falcon_Bp_TriggerTaskEventBp_label=触发任务事件
Falcon_Bp_TryCatchBp_Input_ignoreAbort_description=忽略取消异常
Falcon_Bp_TryCatchBp_Input_ignoreAbort_label=忽略取消异常
Falcon_Bp_TryCatchBp_Input_swallowError_description=将异常吞掉，不抛出
Falcon_Bp_TryCatchBp_Input_swallowError_label=吞掉异常不抛出
Falcon_Bp_TryCatchBp_description=捕获异常信息
Falcon_Bp_TryCatchBp_label=try-catch
Falcon_Bp_UnbindBinContainerBp_Input_binId_description=库位 ID
Falcon_Bp_UnbindBinContainerBp_Input_binId_label=库位编号
Falcon_Bp_UnbindBinContainerBp_Input_containerId_description=容器编号
Falcon_Bp_UnbindBinContainerBp_Input_containerId_label=容器编号
Falcon_Bp_UnbindBinContainerBp_Input_unlockBin_description=是否解锁库位
Falcon_Bp_UnbindBinContainerBp_Input_unlockBin_label=解锁库位
Falcon_Bp_UnbindBinContainerBp_Input_unlockContainer_description=是否解锁容器
Falcon_Bp_UnbindBinContainerBp_Input_unlockContainer_label=解锁容器
Falcon_Bp_UnbindBinContainerBp_description=将库位与容器解绑
Falcon_Bp_UnbindBinContainerBp_label=解绑库位容器
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_Option_fromBin=起点库位
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_Option_loaded=已取货
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_Option_robotName=机器人
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_Option_status=状态
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_Option_toBin=终点库位
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_Option_unloaded=已放货
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_description=需要更新的字段名
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_label=字段
Falcon_Bp_UpdateContainerTransportOrderBp_Input_fv_description=期望更新的字段值
Falcon_Bp_UpdateContainerTransportOrderBp_Input_fv_label=字段值
Falcon_Bp_UpdateContainerTransportOrderBp_Input_orderId_description=容器搬运单的单号，唯一标识
Falcon_Bp_UpdateContainerTransportOrderBp_Input_orderId_label=单号
Falcon_Bp_UpdateContainerTransportOrderBp_description=更新容器搬运单指定字段的值
Falcon_Bp_UpdateContainerTransportOrderBp_label=更新容器搬运单
Falcon_Bp_UpdateEntityFieldBp_Input_entityName_description=业务对象名
Falcon_Bp_UpdateEntityFieldBp_Input_entityName_label=业务对象名
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_Any=任意类型（Any）
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_Boolean=布尔类型（Boolean）
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_Double=双精度浮点型（Double）
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_JSONArray=JSON 数组（JSONArray）
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_JSONObject=JSON 对象（JSONObject）
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_Long=长整型（Long）
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_String=字符串类型（String）
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_description=要查询字段的类型
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_label=查询字段类型
Falcon_Bp_UpdateEntityFieldBp_Input_queryField_description=要查询的字段名
Falcon_Bp_UpdateEntityFieldBp_Input_queryField_label=查询的字段
Falcon_Bp_UpdateEntityFieldBp_Input_queryValue_description=要查询字段的值
Falcon_Bp_UpdateEntityFieldBp_Input_queryValue_label=查询值
Falcon_Bp_UpdateEntityFieldBp_Input_setToNull_label=置为 null
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_Any=任意类型（Any）
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_Boolean=布尔类型（Boolean）
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_Double=双精度浮点型（Double）
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_JSONArray=JSON 数组（JSONArray）
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_JSONObject=JSON 对象（JSONObject）
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_Long=长整型（Long）
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_String=字符串类型（String）
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_description=更新字段的类型
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_label=更新字段的类型
Falcon_Bp_UpdateEntityFieldBp_Input_updateField_description=要更新的字段
Falcon_Bp_UpdateEntityFieldBp_Input_updateField_label=更新的字段
Falcon_Bp_UpdateEntityFieldBp_Input_updateMany_description=是否批量更新
Falcon_Bp_UpdateEntityFieldBp_Input_updateMany_label=批量更新
Falcon_Bp_UpdateEntityFieldBp_Input_updateValue_description=字段更新后的值
Falcon_Bp_UpdateEntityFieldBp_Input_updateValue_label=更新值
Falcon_Bp_UpdateEntityFieldBp_description=根据条件更新业务对象单个字段
Falcon_Bp_UpdateEntityFieldBp_label=根据条件更新业务对象单个字段
Falcon_Bp_UpdateFalconRecordFieldBp_Input_fieldName_label=设置字段
Falcon_Bp_UpdateFalconRecordFieldBp_Output_fieldValue_label=设置值
Falcon_Bp_UpdateFalconRecordFieldBp_label=设置任务记录字段
Falcon_Bp_UpdateOneEntityByIdBp_Input_entityName_description=业务对象名称
Falcon_Bp_UpdateOneEntityByIdBp_Input_entityName_label=业务对象名称
Falcon_Bp_UpdateOneEntityByIdBp_Input_fieldName_description=更新字段名
Falcon_Bp_UpdateOneEntityByIdBp_Input_fieldName_label=字段名
Falcon_Bp_UpdateOneEntityByIdBp_Input_fv_description=更新后字段的值
Falcon_Bp_UpdateOneEntityByIdBp_Input_fv_label=字段值
Falcon_Bp_UpdateOneEntityByIdBp_Input_id_description=实体 Id
Falcon_Bp_UpdateOneEntityByIdBp_Input_id_label=Id
Falcon_Bp_UpdateOneEntityByIdBp_Input_setToNull_label=置为 null
Falcon_Bp_UpdateOneEntityByIdBp_description=根据 id 更新业务对象实体单个字段
Falcon_Bp_UpdateOneEntityByIdBp_label=根据 id 更新业务对象单个字段
Falcon_Bp_WebSocketBp_Input_eventName_description=WebSocket 通信标识
Falcon_Bp_WebSocketBp_Input_eventName_label=发送事件
Falcon_Bp_WebSocketBp_Input_message_description=发送内容
Falcon_Bp_WebSocketBp_Input_message_label=发送内容
Falcon_Bp_WebSocketBp_description=给 WebSocket 客户端发送消息
Falcon_Bp_WebSocketBp_label=WebSocket 发送信息
Falcon_Bp_WhileBp_Input_condition_label=条件
Falcon_Bp_WhileBp_description=在条件满足时一直继续执行子模块
Falcon_Bp_WhileBp_label=循环执行 While
Falcon_label=猎鹰任务
errSubBlockNotFound={0}找不到 {1} 的子块 {2}
labelFalcon=猎鹰任务

[wcs.base]
ModbusDeviceNotInit=Modbus 设备 "{0}" 未初始化
wcs_err_TomNoUrl=调度 "{0}" 未配置 URL
wcs_err_TomNotFound=未配置调度 "{0}"
wcs_err_tom_BlockError=调度说块执行失败，单号={0}，块={1}，状态={2}
wcs_err_tom_BlockNotFound=调度说块找不到，单号={0}，块={1}
wcs_err_tom_Completed_No_Path=调度报错，且已经封口，无法恢复。原因：找不到路径可达站点
wcs_err_tom_ConnectError=连接调度服务器失败：{0}
wcs_err_tom_HttpError=请求调度接口其他报错：[{0}] {1}
wcs_err_tom_HttpError404=请求调度接口报 404
wcs_err_tom_IOError=请求调度，IO 错误：{0}，请求地址：{1}
wcs_err_tom_OtherError=请求调度，其他错误：{0}，请求地址：{1}
wcs_err_tom_TomError=调度报错，错误码={0}，消息={1}
wcs_err_tom_TomResponseEmpty=请求调度响应正文为空

[wcs.error]
clientDeviceExceptionConnectFail=链接失败 {0}
clientDeviceExceptionDeviceDisabled=设备被禁用 {0}
clientDeviceExceptionDeviceNotExist=设备不存在 {0}
clientDeviceExceptionOpCancel=线程被打断 {0}
clientDeviceExceptionOpTimeout=操作超时 {0}
clientDeviceExceptionReadFail=读取失败 {0}
clientDeviceExceptionReqError=请求错误 {0}
clientDeviceExceptionWriteFail=写入失败 {0}
errDeviceDisabled=设备 {0} 已停用
errNoDevice=无此设备：{0}

[wcs.falcon]
Falcon_BlockGroup_PLC=PLC
Falcon_Bp_ModbusReadBp_Input_address_description=Modbus 的地址
Falcon_Bp_ModbusReadBp_Input_address_label=地址
Falcon_Bp_ModbusReadBp_Input_code_Option_1=0x01 读取线圈状态
Falcon_Bp_ModbusReadBp_Input_code_Option_2=0x02 读取输入状态
Falcon_Bp_ModbusReadBp_Input_code_Option_3=0x03 读取保持寄存器
Falcon_Bp_ModbusReadBp_Input_code_Option_4=0x04 读取输入寄存器
Falcon_Bp_ModbusReadBp_Input_code_description=Modbus 的功能码
Falcon_Bp_ModbusReadBp_Input_code_label=功能码
Falcon_Bp_ModbusReadBp_Input_deviceName_description=设备名称，需要先在 PLC 设备配置菜单中进行配置
Falcon_Bp_ModbusReadBp_Input_deviceName_label=设备名
Falcon_Bp_ModbusReadBp_Input_maxRetry_description=最大重试次数，默认为 PLC 设备配置菜单中设置的最大重试次数
Falcon_Bp_ModbusReadBp_Input_maxRetry_label=最大重试次数
Falcon_Bp_ModbusReadBp_Input_retryDelay_description=重试间隔，默认为 PLC 设备配置菜单中设置的重试间隔
Falcon_Bp_ModbusReadBp_Input_retryDelay_label=重试等待（毫秒）
Falcon_Bp_ModbusReadBp_Input_slaveId_description=Modbus 设备id，默认为 0
Falcon_Bp_ModbusReadBp_Input_slaveId_label=Slave ID
Falcon_Bp_ModbusReadBp_Output_value_description=读取出的对应地址的值
Falcon_Bp_ModbusReadBp_Output_value_label=值
Falcon_Bp_ModbusReadBp_description=读取 Modbus 的值
Falcon_Bp_ModbusReadBp_label=Modbus 读取
Falcon_Bp_ModbusReadEqBp_Input_address_description=Modbus 的地址
Falcon_Bp_ModbusReadEqBp_Input_address_label=地址
Falcon_Bp_ModbusReadEqBp_Input_code_Option_1=0x01 读取线圈状态
Falcon_Bp_ModbusReadEqBp_Input_code_Option_2=0x02 读取输入状态
Falcon_Bp_ModbusReadEqBp_Input_code_Option_3=0x03 读取保持寄存器
Falcon_Bp_ModbusReadEqBp_Input_code_Option_4=0x04 读取输入寄存器
Falcon_Bp_ModbusReadEqBp_Input_code_description=Modbus 的功能码
Falcon_Bp_ModbusReadEqBp_Input_code_label=功能码
Falcon_Bp_ModbusReadEqBp_Input_deviceName_description=设备名称，需要先在 PLC 设备配置菜单中进行配置
Falcon_Bp_ModbusReadEqBp_Input_deviceName_label=设备名
Falcon_Bp_ModbusReadEqBp_Input_maxRetry_description=读失败情况下，最大自动重试次数，默认为 PLC 设备配置菜单中设置的最大重试次数
Falcon_Bp_ModbusReadEqBp_Input_maxRetry_label=失败次数限制
Falcon_Bp_ModbusReadEqBp_Input_readDelay_description=两次读取之间的间隔，默认为 1s
Falcon_Bp_ModbusReadEqBp_Input_readDelay_label=读取间隔
Falcon_Bp_ModbusReadEqBp_Input_readLimit_description=读到的值与预期不同的次数限制，默认值 -1。不填、-1 即不限制
Falcon_Bp_ModbusReadEqBp_Input_readLimit_label=读值不符预期的次数限制
Falcon_Bp_ModbusReadEqBp_Input_retryDelay_description=读取失败情况下，重试间隔，默认为 PLC 设备配置菜单中设置的重试间隔
Falcon_Bp_ModbusReadEqBp_Input_retryDelay_label=失败重试等待（毫秒）
Falcon_Bp_ModbusReadEqBp_Input_slaveId_description=Modbus 设备id，默认为 0
Falcon_Bp_ModbusReadEqBp_Input_slaveId_label=Slave ID
Falcon_Bp_ModbusReadEqBp_Input_targetValue_description=目标值
Falcon_Bp_ModbusReadEqBp_Input_targetValue_label=目标值
Falcon_Bp_ModbusReadEqBp_description=读取 Modbus 直到等于预期的值，如果不等于则一直读取直到超过最大重试次数
Falcon_Bp_ModbusReadEqBp_label=Modbus 读取直到等于
Falcon_Bp_ModbusWriteBp_Input_address_description=Modbus 的地址
Falcon_Bp_ModbusWriteBp_Input_address_label=地址
Falcon_Bp_ModbusWriteBp_Input_code_Option_5=0x05 写单个线圈
Falcon_Bp_ModbusWriteBp_Input_code_Option_6=0x06 写单个寄存器
Falcon_Bp_ModbusWriteBp_Input_code_description=Modbus 的功能码
Falcon_Bp_ModbusWriteBp_Input_code_label=功能码
Falcon_Bp_ModbusWriteBp_Input_deviceName_description=设备名称，需要先在 PLC 设备配置菜单中进行配置
Falcon_Bp_ModbusWriteBp_Input_deviceName_label=设备名
Falcon_Bp_ModbusWriteBp_Input_maxRetry_description=最大重试次数，默认为 PLC 设备配置菜单中设置的最大重试次数
Falcon_Bp_ModbusWriteBp_Input_maxRetry_label=最大重试次数
Falcon_Bp_ModbusWriteBp_Input_retryDelay_description=重试间隔，默认为 PLC 设备配置菜单中设置的重试间隔
Falcon_Bp_ModbusWriteBp_Input_retryDelay_label=重试等待（毫秒）
Falcon_Bp_ModbusWriteBp_Input_slaveId_description=Modbus 设备id，默认为 0
Falcon_Bp_ModbusWriteBp_Input_slaveId_label=Slave ID
Falcon_Bp_ModbusWriteBp_Input_value_description=写入的值
Falcon_Bp_ModbusWriteBp_Input_value_label=值
Falcon_Bp_ModbusWriteBp_description=写入 Modbus
Falcon_Bp_ModbusWriteBp_label=Modbus 写
Falcon_Bp_S7ReadBp_Input_bitOffset_description=位偏移量
Falcon_Bp_S7ReadBp_Input_bitOffset_label=位偏移量
Falcon_Bp_S7ReadBp_Input_blockType_description=请根据协议内容选择对应的块类型。
Falcon_Bp_S7ReadBp_Input_blockType_label=块类型
Falcon_Bp_S7ReadBp_Input_byteOffset_description=字节偏移量，可以理解为地址编号。
Falcon_Bp_S7ReadBp_Input_byteOffset_label=字节偏移量
Falcon_Bp_S7ReadBp_Input_dataType_description=请根据协议内容选择对应的数据类型。
Falcon_Bp_S7ReadBp_Input_dataType_label=数据类型
Falcon_Bp_S7ReadBp_Input_dbId_description=DB 编号，请根据协议内容输入对应的数值。
Falcon_Bp_S7ReadBp_Input_dbId_label=dbId
Falcon_Bp_S7ReadBp_Input_deviceName_description=请输入“PLC 设备管理”中配置的 S7 设备的名称。
Falcon_Bp_S7ReadBp_Input_deviceName_label=设备名
Falcon_Bp_S7ReadBp_Input_maxRetry_description=最大的重试次数
Falcon_Bp_S7ReadBp_Input_maxRetry_label=最大重试次数
Falcon_Bp_S7ReadBp_Input_retryDelay_description=重试时间间隔，单位毫秒
Falcon_Bp_S7ReadBp_Input_retryDelay_label=重试等待（毫秒）
Falcon_Bp_S7ReadBp_Output_value_description=值
Falcon_Bp_S7ReadBp_Output_value_label=值
Falcon_Bp_S7ReadBp_description=通过 S7 协议读取一次 PLC 设备的一个数据，并将读取到的值作为此组件的输出参数。
Falcon_Bp_S7ReadBp_label=S7 读取
Falcon_Bp_S7ReadEqBp_Input_bitOffset_description=位偏移量
Falcon_Bp_S7ReadEqBp_Input_bitOffset_label=位偏移量
Falcon_Bp_S7ReadEqBp_Input_blockType_description=请根据协议内容选择对应的块类型。
Falcon_Bp_S7ReadEqBp_Input_blockType_label=块类型
Falcon_Bp_S7ReadEqBp_Input_byteOffset_description=字节偏移量，可以理解为地址编号。
Falcon_Bp_S7ReadEqBp_Input_byteOffset_label=字节偏移量
Falcon_Bp_S7ReadEqBp_Input_dataType_description=请根据协议内容选择对应数据类型。
Falcon_Bp_S7ReadEqBp_Input_dataType_label=数据类型
Falcon_Bp_S7ReadEqBp_Input_dbId_description=DB 编号，请根据协议内容输入对应的数值。
Falcon_Bp_S7ReadEqBp_Input_dbId_label=dbId
Falcon_Bp_S7ReadEqBp_Input_deviceName_description=请输入“PLC 设备管理”中配置的 S7 设备的名称。
Falcon_Bp_S7ReadEqBp_Input_deviceName_label=设备名
Falcon_Bp_S7ReadEqBp_Input_maxRetry_description=最大的重试次数
Falcon_Bp_S7ReadEqBp_Input_maxRetry_label=最大重试次数
Falcon_Bp_S7ReadEqBp_Input_readDelay_description=两次读取之间的间隔，默认为 1s
Falcon_Bp_S7ReadEqBp_Input_readDelay_label=读取间隔
Falcon_Bp_S7ReadEqBp_Input_readLimit_description=读到的值与预期不同的次数限制，默认值 -1。不填、-1 即不限制
Falcon_Bp_S7ReadEqBp_Input_readLimit_label=读值不符预期的次数限制
Falcon_Bp_S7ReadEqBp_Input_retryDelay_description=读取失败情况下，重试间隔，默认为 PLC 设备配置菜单中设置的重试间隔
Falcon_Bp_S7ReadEqBp_Input_retryDelay_label=失败重试等待（毫秒）
Falcon_Bp_S7ReadEqBp_Input_value_description=期望值
Falcon_Bp_S7ReadEqBp_Input_value_label=值
Falcon_Bp_S7ReadEqBp_description=通过 S7 协议一直读取 PLC 设备的一个数据，直到读取到的值与期望值相同才停止。
Falcon_Bp_S7ReadEqBp_label=S7 读取直到等于
Falcon_Bp_S7WriteBp_Input_bitOffset_description=位偏移量
Falcon_Bp_S7WriteBp_Input_bitOffset_label=位偏移量
Falcon_Bp_S7WriteBp_Input_blockType_description=请根据协议内容选择对应的块类型。
Falcon_Bp_S7WriteBp_Input_blockType_label=块类型
Falcon_Bp_S7WriteBp_Input_byteOffset_description=字节偏移量，可以理解为地址编号。
Falcon_Bp_S7WriteBp_Input_byteOffset_label=字节偏移量
Falcon_Bp_S7WriteBp_Input_dataType_description=请根据协议内容选择对应数据类型。
Falcon_Bp_S7WriteBp_Input_dataType_label=数据类型
Falcon_Bp_S7WriteBp_Input_dbId_description=DB 编号，请根据协议内容输入对应的数值。
Falcon_Bp_S7WriteBp_Input_dbId_label=dbId
Falcon_Bp_S7WriteBp_Input_deviceName_description=请输入“PLC 设备管理”中配置的 S7 设备的名称。
Falcon_Bp_S7WriteBp_Input_deviceName_label=设备名
Falcon_Bp_S7WriteBp_Input_maxRetry_description=最大的重试次数
Falcon_Bp_S7WriteBp_Input_maxRetry_label=最大重试次数
Falcon_Bp_S7WriteBp_Input_retryDelay_description=重试时间间隔，单位毫秒
Falcon_Bp_S7WriteBp_Input_retryDelay_label=重试等待（毫秒）
Falcon_Bp_S7WriteBp_Input_value_description=目标值
Falcon_Bp_S7WriteBp_Input_value_label=值
Falcon_Bp_S7WriteBp_description=通过 S7 协议向 PLC 设备写入一次目标值。
Falcon_Bp_S7WriteBp_label=S7 写入
errModbusReadEqNotMatch=ModbusReadEqBp 读值与期望不同次数超过限制
errS7ReadEqNotMatch=S7ReadEqBp 读值与期望不同次数超过限制

[fleet.base]
RbkApiNoNoSupported=不支持 API 编号 {0}
RbkClientConnectFail=连接 RBK 失败 {0}
RbkClientRequestFail=请求 RBK 失败 {0}
RobotAlarm=机器人报警
RobotNotExistedById=机器人 "{0}" 不存在
btnPushMap=推送地图
errSceneMapsPushing=上一次推送未完成，请等待或先取消上一次推送
errorFleetOrderFault=运单 "{0}" 故障。机器人="{1}"。原因：{2}。
labelFleet=车队管理
manualLoadTip=若要人工完成，先从 {0} 位置取下货物，放置在机器人身上，再点击人工完成。
manualLoadTip1=若要人工完成，先从 {0} 位置取下货物，放置在机器人货叉（999 号）背篓，不要放入机器人其他背篓，再点击人工完成。
manualLoadTip2=若要人工完成，先从 {0} 位置取下货物，放置在机器人身上的第 {1} 层（{2} 号）背篓，再点击人工完成。
manualUnloadTip=若要人工完成，请从机器人身上取下货物，放置在 {0} 位置，再点击人工完成。
manualUnloadTip1=若要人工完成，请从机器人货叉（999 号）背篓中取下货物，放置在 {0} 位置，再点击人工完成。
manualUnloadTip2=若要人工完成，请从机器人身上的第 {0} 层（{1} 号）背篓取下货物，放置在 {2} 位置，再点击人工完成。

[fleet.falcon]
Falcon_BlockGroup_DirectOrder=直接运单
Falcon_BlockGroup_Map=地图
Falcon_BlockGroup_Ndc=NDC 调度运单
Falcon_BlockGroup_SeerTom=二代调度运单
Falcon_BlockGroup_TransportOrder=三代调度运单
Falcon_Bp_AddStepAndWaitCompleteBp_Input_binTask_description=binTask 键，填写库位时使用
Falcon_Bp_AddStepAndWaitCompleteBp_Input_binTask_label=binTask
Falcon_Bp_AddStepAndWaitCompleteBp_Input_forLoad_description=如果在这一步取货，必须正确设置！
Falcon_Bp_AddStepAndWaitCompleteBp_Input_forLoad_label=在此步取货
Falcon_Bp_AddStepAndWaitCompleteBp_Input_forUnload_description=如果在这一步放货，必须正确设置！
Falcon_Bp_AddStepAndWaitCompleteBp_Input_forUnload_label=在此步卸货
Falcon_Bp_AddStepAndWaitCompleteBp_Input_location_description=点位或库位
Falcon_Bp_AddStepAndWaitCompleteBp_Input_location_label=点位或库位
Falcon_Bp_AddStepAndWaitCompleteBp_Input_nextStepSameOrder_description=多负载机器人，此参数为 true 时，这个步骤做完，下一个步骤必须还是同一运单的，不允许执行其他机器人的运单
Falcon_Bp_AddStepAndWaitCompleteBp_Input_nextStepSameOrder_label=强制要求下一步必须相同运单
Falcon_Bp_AddStepAndWaitCompleteBp_Input_operationArgs_description=动作参数，JSON 格式，示例：{"end_height": 0.1}，表示货叉上升到 0.1m 的高度
Falcon_Bp_AddStepAndWaitCompleteBp_Input_operationArgs_label=rbkArgs
Falcon_Bp_AddStepAndWaitCompleteBp_Input_operation_description=机构动作名。如 JackLoad，ForkLoad
Falcon_Bp_AddStepAndWaitCompleteBp_Input_operation_label=operation
Falcon_Bp_AddStepAndWaitCompleteBp_Input_orderId_description=步骤所属的运单单号
Falcon_Bp_AddStepAndWaitCompleteBp_Input_orderId_label=运单号
Falcon_Bp_AddStepAndWaitCompleteBp_Input_withdrawOrderAllowed_description=如果允许，机器人可以在执行此步的过程中（实际取货放发生前），去接更适合的运单
Falcon_Bp_AddStepAndWaitCompleteBp_Input_withdrawOrderAllowed_label=允许重分派
Falcon_Bp_AddStepAndWaitCompleteBp_Output_robotName_description=机器人名称
Falcon_Bp_AddStepAndWaitCompleteBp_Output_robotName_label=机器人名称
Falcon_Bp_AddStepAndWaitCompleteBp_Output_stepId_description=步骤 ID
Falcon_Bp_AddStepAndWaitCompleteBp_Output_stepId_label=步骤 ID
Falcon_Bp_AddStepAndWaitCompleteBp_description=添加三代运单步骤，且等待运单步骤完成
Falcon_Bp_AddStepAndWaitCompleteBp_label=添加三代运单步骤并等待完成
Falcon_Bp_AddTomBlockBp_Input_binTask_description=binTask
Falcon_Bp_AddTomBlockBp_Input_binTask_label=binTask
Falcon_Bp_AddTomBlockBp_Input_goodsId_description=容器编号
Falcon_Bp_AddTomBlockBp_Input_goodsId_label=容器编号
Falcon_Bp_AddTomBlockBp_Input_location_description=目的地名称，站点名，或库位
Falcon_Bp_AddTomBlockBp_Input_location_label=站点
Falcon_Bp_AddTomBlockBp_Input_nextLocation_description=当前 block 是前置点时，实际要去的库位编号
Falcon_Bp_AddTomBlockBp_Input_nextLocation_label=下一目标点
Falcon_Bp_AddTomBlockBp_Input_operation_description=执行机构动作
Falcon_Bp_AddTomBlockBp_Input_operation_label=动作（operation）
Falcon_Bp_AddTomBlockBp_Input_orderId_description=【创建二代调度运单】块生成的“运单号”
Falcon_Bp_AddTomBlockBp_Input_orderId_label=运单号
Falcon_Bp_AddTomBlockBp_Input_tomId_description=《机器人应用》页面中“二代调度”的场景名称
Falcon_Bp_AddTomBlockBp_Input_tomId_label=调度 ID
Falcon_Bp_AddTomBlockBp_Output_blockId_description=调度中该动作的块 ID
Falcon_Bp_AddTomBlockBp_Output_blockId_label=块 ID
Falcon_Bp_AddTomBlockBp_description=追加动作块
Falcon_Bp_AddTomBlockBp_label=添加二代运单块
Falcon_Bp_AddTransportStepBp_Input_binTask_description=binTask 键，填写库位时使用
Falcon_Bp_AddTransportStepBp_Input_binTask_label=binTask
Falcon_Bp_AddTransportStepBp_Input_forLoad_description=如果在这一步取货，必须正确设置！
Falcon_Bp_AddTransportStepBp_Input_forLoad_label=在此步取货
Falcon_Bp_AddTransportStepBp_Input_forUnload_description=如果在这一步放货，必须正确设置！
Falcon_Bp_AddTransportStepBp_Input_forUnload_label=在此步卸货
Falcon_Bp_AddTransportStepBp_Input_location_description=点位或库位
Falcon_Bp_AddTransportStepBp_Input_location_label=点位或库位
Falcon_Bp_AddTransportStepBp_Input_nextStepSameOrder_description=多负载机器人，此参数为 true 时，这个步骤做完，下一个步骤必须还是同一运单的，不允许执行其他机器人的运单
Falcon_Bp_AddTransportStepBp_Input_nextStepSameOrder_label=强制要求下一步必须相同运单
Falcon_Bp_AddTransportStepBp_Input_operationArgs_description=动作参数，JSON 格式，示例：{"end_height": 0.1}，表示货叉上升到 0.1m 的高度
Falcon_Bp_AddTransportStepBp_Input_operationArgs_label=rbkArgs
Falcon_Bp_AddTransportStepBp_Input_operation_description=机构动作名。如 JackLoad，ForkLoad
Falcon_Bp_AddTransportStepBp_Input_operation_label=operation
Falcon_Bp_AddTransportStepBp_Input_orderId_description=步骤所属的运单单号
Falcon_Bp_AddTransportStepBp_Input_orderId_label=运单号
Falcon_Bp_AddTransportStepBp_Input_withdrawOrderAllowed_description=如果允许，机器人可以在执行此步的过程中（实际取货放发生前），去接更适合的运单
Falcon_Bp_AddTransportStepBp_Input_withdrawOrderAllowed_label=允许重分派
Falcon_Bp_AddTransportStepBp_Output_stepId_description=步骤 ID
Falcon_Bp_AddTransportStepBp_Output_stepId_label=步骤 ID
Falcon_Bp_AddTransportStepBp_description=只添加三代运单步骤，与“等待三代运单步骤完成”搭配使用
Falcon_Bp_AddTransportStepBp_label=添加三代运单步骤
Falcon_Bp_AllowNdcLoadBp_Input_orderId_label=运单号
Falcon_Bp_AllowNdcLoadBp_description=未启用
Falcon_Bp_AllowNdcLoadBp_label=允许 NDC 装货
Falcon_Bp_AllowNdcUnloadBp_Input_orderId_label=运单号
Falcon_Bp_AllowNdcUnloadBp_description=未启用
Falcon_Bp_AllowNdcUnloadBp_label=允许 NDC 卸货
Falcon_Bp_CompleteTomOrderBp_Input_orderId_description=【创建二代调度运单】块生成的“运单号”
Falcon_Bp_CompleteTomOrderBp_Input_orderId_label=运单号
Falcon_Bp_CompleteTomOrderBp_Input_tomId_description=《机器人应用》页面中“二代调度”的场景名称
Falcon_Bp_CompleteTomOrderBp_Input_tomId_label=调度 ID
Falcon_Bp_CompleteTomOrderBp_description=封口
Falcon_Bp_CompleteTomOrderBp_label=结束二代调度运单
Falcon_Bp_CompleteTransportOrderBp_Input_orderId_description=运单号
Falcon_Bp_CompleteTransportOrderBp_Input_orderId_label=运单号
Falcon_Bp_CompleteTransportOrderBp_description=结束三代运单
Falcon_Bp_CompleteTransportOrderBp_label=结束三代运单
Falcon_Bp_CreateNdcOrderBp_Input_endBin_description=NDC 的库位，Short 类型
Falcon_Bp_CreateNdcOrderBp_Input_endBin_label=终点
Falcon_Bp_CreateNdcOrderBp_Input_priority_label=优先级
Falcon_Bp_CreateNdcOrderBp_Input_startBin_description=NDC 的库位，Short 类型
Falcon_Bp_CreateNdcOrderBp_Input_startBin_label=起点
Falcon_Bp_CreateNdcOrderBp_Output_orderId_description=《NDC 运单》单号
Falcon_Bp_CreateNdcOrderBp_Output_orderId_label=运单号
Falcon_Bp_CreateNdcOrderBp_description=M4 将起点、终点、优先级一起发给 NDC；NDC 自动执行；NDC 取完、放完、任务完成后上报 M4
Falcon_Bp_CreateNdcOrderBp_label=创建 NDC 运单
Falcon_Bp_CreateTomOrderBp_Input_group_description=指定机器人组，用于选车时指派属于这个机器人组的机器人执行
Falcon_Bp_CreateTomOrderBp_Input_group_label=指定机器人组
Falcon_Bp_CreateTomOrderBp_Input_keyGoodsId_description=针对料箱车 n+1 的场景，辅助 core 最后一个取货第一个放
Falcon_Bp_CreateTomOrderBp_Input_keyGoodsId_label=keyGoodsId
Falcon_Bp_CreateTomOrderBp_Input_keyRouteStr_description=关键点，用于辅助确定派单机器人；若不填，则系统会根据当前运行情况自动选择合适的机器人派单
Falcon_Bp_CreateTomOrderBp_Input_keyRouteStr_label=关键位置
Falcon_Bp_CreateTomOrderBp_Input_keyTask_description=为"load"或者"unload"，如果填写其他字段会被自动忽略，用于辅助确定派单机器人；若不填，则系统会根据当前运行情况自动选择合适的机器人派单
Falcon_Bp_CreateTomOrderBp_Input_keyTask_label=keyTask
Falcon_Bp_CreateTomOrderBp_Input_label_description=指定机器人标签，要求系统就当前运单指定给特定标签的机器人
Falcon_Bp_CreateTomOrderBp_Input_label_label=label
Falcon_Bp_CreateTomOrderBp_Input_loadBlockCount_description=存在且大于 0 时，表示该订单需要至少 X 个空背篓才能执行，即该订单有 X 个取货动作块
Falcon_Bp_CreateTomOrderBp_Input_loadBlockCount_label=取货块数量
Falcon_Bp_CreateTomOrderBp_Input_notMarkComplete_description=取消猎鹰任务，不封口调度运单
Falcon_Bp_CreateTomOrderBp_Input_notMarkComplete_label=取消任务运单不封口
Falcon_Bp_CreateTomOrderBp_Input_priority_description=运单优先级，数字越大代表订单优先级越高
Falcon_Bp_CreateTomOrderBp_Input_priority_label=优先级
Falcon_Bp_CreateTomOrderBp_Input_tomId_description=《机器人应用管理》页面中“二代调度”的场景名称
Falcon_Bp_CreateTomOrderBp_Input_tomId_label=场景名称
Falcon_Bp_CreateTomOrderBp_Input_vehicle_description=指定机器人，用于选车时指派指定的机器人执行
Falcon_Bp_CreateTomOrderBp_Input_vehicle_label=指定机器人
Falcon_Bp_CreateTomOrderBp_Output_actualVehicle_description=二代调度分配的机器人
Falcon_Bp_CreateTomOrderBp_Output_actualVehicle_label=分配的机器人
Falcon_Bp_CreateTomOrderBp_Output_orderId_description=二代调度运单号
Falcon_Bp_CreateTomOrderBp_Output_orderId_label=运单号
Falcon_Bp_CreateTomOrderBp_description=创建二代调度运单，即 core 调度
Falcon_Bp_CreateTomOrderBp_label=创建二代调度运单
Falcon_Bp_CreateTransportOrderBp_Input_containerDir_description=终点放置容器的方向，单位是角度
Falcon_Bp_CreateTransportOrderBp_Input_containerDir_label=终点容器方向
Falcon_Bp_CreateTransportOrderBp_Input_containerId_description=容器编号
Falcon_Bp_CreateTransportOrderBp_Input_containerId_label=容器编号
Falcon_Bp_CreateTransportOrderBp_Input_containerTypeName_description=容器类型的名称
Falcon_Bp_CreateTransportOrderBp_Input_containerTypeName_label=容器类型名称
Falcon_Bp_CreateTransportOrderBp_Input_expectedRobotGroups_description=期待执行的机器组，可指定多个，使用逗号分隔
Falcon_Bp_CreateTransportOrderBp_Input_expectedRobotGroups_label=期待执行的机器组
Falcon_Bp_CreateTransportOrderBp_Input_expectedRobotNames_description=期待执行的机器人，可指定多个，使用逗号分隔
Falcon_Bp_CreateTransportOrderBp_Input_expectedRobotNames_label=期待执行的机器人
Falcon_Bp_CreateTransportOrderBp_Input_keyLocations_description=用于辅助确定派单机器人，填写一个或多个站点或库位名称，逗号分隔
Falcon_Bp_CreateTransportOrderBp_Input_keyLocations_label=关键位置
Falcon_Bp_CreateTransportOrderBp_Input_priority_description=整数，数字越大代表优先级越高
Falcon_Bp_CreateTransportOrderBp_Input_priority_label=优先级
Falcon_Bp_CreateTransportOrderBp_Input_sceneName_description=三代调度的场景名称，非必填，默认取第一个场景名称
Falcon_Bp_CreateTransportOrderBp_Input_sceneName_label=场景名称
Falcon_Bp_CreateTransportOrderBp_Output_orderId_description=步骤所属的运单单号
Falcon_Bp_CreateTransportOrderBp_Output_orderId_label=运单号
Falcon_Bp_CreateTransportOrderBp_Output_robotName_description=机器人名称
Falcon_Bp_CreateTransportOrderBp_Output_robotName_label=机器人名称
Falcon_Bp_CreateTransportOrderBp_description=创建三代调度运单
Falcon_Bp_CreateTransportOrderBp_label=创建三代调度运单
Falcon_Bp_DirectOrderExecuteBp_Input_desc_label=描述
Falcon_Bp_DirectOrderExecuteBp_Input_fillMiddleLandmarks_description=若【直接运单步骤】之间有其它站点，则将中间站点填充入直接运单中
Falcon_Bp_DirectOrderExecuteBp_Input_fillMiddleLandmarks_label=填充中间点
Falcon_Bp_DirectOrderExecuteBp_Input_robotId_label=机器人编号
Falcon_Bp_DirectOrderExecuteBp_Input_sceneName_description=填《机器人应用管理》（2.5 代调度）的场景名，或者《调度场景》（3 代调度）的场景名
Falcon_Bp_DirectOrderExecuteBp_Input_sceneName_label=场景名称
Falcon_Bp_DirectOrderExecuteBp_Input_seer3066_description=用3066指令，指定路径导航
Falcon_Bp_DirectOrderExecuteBp_Input_seer3066_label=3066 指令启用
Falcon_Bp_DirectOrderExecuteBp_Input_taskId_label=任务编号
Falcon_Bp_DirectOrderExecuteBp_Input_unlockCurrentDirectSiteIds_description=默认为 false，代表解锁该机器人加锁的所以资源，为 true 代表解锁该直接运单加锁的资源
Falcon_Bp_DirectOrderExecuteBp_Input_unlockCurrentDirectSiteIds_label=解锁资源时是否只解锁该直接运单加锁的资源
Falcon_Bp_DirectOrderExecuteBp_Output_orderId_description=《直接运单》单号
Falcon_Bp_DirectOrderExecuteBp_Output_orderId_label=单号
Falcon_Bp_DirectOrderExecuteBp_description=创建《直接运单》并等待其执行完成（不会重复创建）
Falcon_Bp_DirectOrderExecuteBp_label=直接运单执行
Falcon_Bp_DirectOrderMoveBp_Input_binTask_label=binTask
Falcon_Bp_DirectOrderMoveBp_Input_containerId_label=容器编号
Falcon_Bp_DirectOrderMoveBp_Input_id_description=目的地名称，站点名，或库位
Falcon_Bp_DirectOrderMoveBp_Input_id_label=站点
Falcon_Bp_DirectOrderMoveBp_Input_operation_description=执行机构动作
Falcon_Bp_DirectOrderMoveBp_Input_operation_label=动作（operation）
Falcon_Bp_DirectOrderMoveBp_description=直接运单步骤
Falcon_Bp_DirectOrderMoveBp_label=直接运单步骤
Falcon_Bp_MapPointBinBp_Input_pointId_label=站点
Falcon_Bp_MapPointBinBp_Output_binId_label=库位
Falcon_Bp_MapPointBinBp_Output_found_label=站点配了库位
Falcon_Bp_MapPointBinBp_label=站点的库位
Falcon_Bp_WaitStepCompleteBp_Input_stepId_description=运单的步骤 ID
Falcon_Bp_WaitStepCompleteBp_Input_stepId_label=步骤 ID
Falcon_Bp_WaitStepCompleteBp_Output_robotName_description=机器人名称
Falcon_Bp_WaitStepCompleteBp_Output_robotName_label=机器人名称
Falcon_Bp_WaitStepCompleteBp_description=等待三代运单步骤执行完成，与“添加三代运单步骤”搭配使用
Falcon_Bp_WaitStepCompleteBp_label=等待三代运单步骤完成
Falcon_Bp_WaitUntilNdcArriveEndBinBp_Input_orderId_label=运单号
Falcon_Bp_WaitUntilNdcArriveEndBinBp_description=未启用
Falcon_Bp_WaitUntilNdcArriveEndBinBp_label=等待 NDC 到达终点
Falcon_Bp_WaitUntilNdcArriveStartBinBp_Input_orderId_label=运单号
Falcon_Bp_WaitUntilNdcArriveStartBinBp_description=未启用
Falcon_Bp_WaitUntilNdcArriveStartBinBp_label=等待 NDC 到达起点
Falcon_Bp_WaitUntilNdcFinishBp_Input_orderId_label=运单号
Falcon_Bp_WaitUntilNdcFinishBp_label=等待 NDC 任务完成
Falcon_Bp_WaitUntilNdcLoadedBp_Input_orderId_label=运单号
Falcon_Bp_WaitUntilNdcLoadedBp_label=等待 NDC 装货完成
Falcon_Bp_WaitUntilNdcUnloadedBp_Input_orderId_label=运单号
Falcon_Bp_WaitUntilNdcUnloadedBp_label=等待 NDC 卸货完成

[fleet.single.base]
relocStatus_0=重定位初始化中
relocStatus_1=重定位成功
relocStatus_2=正在重定位
relocStatus_3=地图载入中
relocStatus_null=定位状态未知

[fleet.single.falcon]
Falcon_BlockGroup_RobotSingleControl=单车控制
Falcon_Bp_RobotReadDIBp_Input_id_description=指定的 DI 的编号
Falcon_Bp_RobotReadDIBp_Input_id_label=DI 编号
Falcon_Bp_RobotReadDIBp_Input_tomId_description=《机器人应用》页面中“二代调度”的场景名称
Falcon_Bp_RobotReadDIBp_Input_tomId_label=调度 ID
Falcon_Bp_RobotReadDIBp_Input_vehicle_description=指定机器人
Falcon_Bp_RobotReadDIBp_Input_vehicle_label=指定机器人
Falcon_Bp_RobotReadDIBp_Output_result_label=读取结果
Falcon_Bp_RobotReadDIBp_description=读取指定机器人的DI
Falcon_Bp_RobotReadDIBp_label=读取 DI
Falcon_Bp_RobotSetDOBp_Input_id_description=指定 DO 的编号
Falcon_Bp_RobotSetDOBp_Input_id_label=DO 编号
Falcon_Bp_RobotSetDOBp_Input_status_description=将指定 DO 的状态修改为该状态
Falcon_Bp_RobotSetDOBp_Input_status_label=目标状态
Falcon_Bp_RobotSetDOBp_Input_tomId_description=《机器人应用》页面中“二代调度”的场景名称
Falcon_Bp_RobotSetDOBp_Input_tomId_label=调度 ID
Falcon_Bp_RobotSetDOBp_Input_vehicle_description=指定机器人
Falcon_Bp_RobotSetDOBp_Input_vehicle_label=指定机器人
Falcon_Bp_RobotSetDOBp_Output_result_description=执行后的响应结果
Falcon_Bp_RobotSetDOBp_Output_result_label=result 请求结果
Falcon_Bp_RobotSetDOBp_description=用于修改指定 DO 的状态
Falcon_Bp_RobotSetDOBp_label=设置 DO
Falcon_Bp_RobotSingleAngleRadianBp_Input_conversionType_Option_AngleToRadian=角度转弧度
Falcon_Bp_RobotSingleAngleRadianBp_Input_conversionType_Option_RadianToAngle=弧度转角度
Falcon_Bp_RobotSingleAngleRadianBp_Input_conversionType_description=选择转换类型，将数值换算为期望的度数，或者弧度数。
Falcon_Bp_RobotSingleAngleRadianBp_Input_conversionType_label=转换类型
Falcon_Bp_RobotSingleAngleRadianBp_Input_degrees_description=选择转换类型，将数值换算为期望的度数，或者弧度数。
Falcon_Bp_RobotSingleAngleRadianBp_Input_degrees_label=数值
Falcon_Bp_RobotSingleAngleRadianBp_Output_degrees_description=被转换后的度数，或者弧度数。
Falcon_Bp_RobotSingleAngleRadianBp_Output_degrees_label=结果
Falcon_Bp_RobotSingleAngleRadianBp_description=角度与弧度之间相互转换
Falcon_Bp_RobotSingleAngleRadianBp_label=弧度与角度转换
Falcon_Bp_RobotSingleForkBp_Input_endHeight_description=货叉调节的最后高度，默认值 0.500（单位：m）
Falcon_Bp_RobotSingleForkBp_Input_endHeight_label=结束高度
Falcon_Bp_RobotSingleForkBp_Input_forkDist_description=对于前移式货叉，货叉向前移动的距离，默认值 0.00（单位：m）
Falcon_Bp_RobotSingleForkBp_Input_forkDist_label=前移距离（前移式货叉）
Falcon_Bp_RobotSingleForkBp_Input_forkMidHeight_description=行走时货叉的高度，默认值 0.100（单位：m）
Falcon_Bp_RobotSingleForkBp_Input_forkMidHeight_label=行走高度
Falcon_Bp_RobotSingleForkBp_Input_operation_Option_ForkForward=货叉前移（ForkForward）
Falcon_Bp_RobotSingleForkBp_Input_operation_Option_ForkHeight=货叉顶高（ForkHeight）
Falcon_Bp_RobotSingleForkBp_Input_operation_Option_ForkLoad=货叉取货（ForkLoad）
Falcon_Bp_RobotSingleForkBp_Input_operation_Option_ForkUnload=货叉放货（ForkUnload）
Falcon_Bp_RobotSingleForkBp_Input_operation_description=货叉机构可操作的动作
Falcon_Bp_RobotSingleForkBp_Input_operation_label=动作
Falcon_Bp_RobotSingleForkBp_Input_recfile_description=机器人执行特定动作时，需要用到此文件进行识别比对之类的
Falcon_Bp_RobotSingleForkBp_Input_recfile_label=识别文件
Falcon_Bp_RobotSingleForkBp_Input_reqId_description=组件执行时的标识，选填
Falcon_Bp_RobotSingleForkBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleForkBp_Input_startHeight_description=货叉的起始高度，默认值 0.100（单位：m）
Falcon_Bp_RobotSingleForkBp_Input_startHeight_label=起始高度
Falcon_Bp_RobotSingleForkBp_Input_station_description=导航的目标站点
Falcon_Bp_RobotSingleForkBp_Input_station_label=目标站点
Falcon_Bp_RobotSingleForkBp_Output_rbkResult_description=执行后的响应结果
Falcon_Bp_RobotSingleForkBp_Output_rbkResult_label=rbk 请求结果
Falcon_Bp_RobotSingleForkBp_description=操作叉货机构
Falcon_Bp_RobotSingleForkBp_label=叉货/放货
Falcon_Bp_RobotSingleGetAllStatusBp_Output_status_description=机器人的全部状态，数据类型为 JsonObject
Falcon_Bp_RobotSingleGetAllStatusBp_Output_status_label=状态
Falcon_Bp_RobotSingleGetAllStatusBp_description=获取机器人 1100 接口的原始报告
Falcon_Bp_RobotSingleGetAllStatusBp_label=获取机器人全部状态
Falcon_Bp_RobotSingleGetOneStatusBp_Input_status_Option_batteryLevel=电量
Falcon_Bp_RobotSingleGetOneStatusBp_Input_status_Option_charging=充电状态
Falcon_Bp_RobotSingleGetOneStatusBp_Input_status_Option_currentStation=当前站点
Falcon_Bp_RobotSingleGetOneStatusBp_Input_status_description=机器人的状态，比如：电量、当前点位
Falcon_Bp_RobotSingleGetOneStatusBp_Input_status_label=状态
Falcon_Bp_RobotSingleGetOneStatusBp_Output_status_description=机器人的指定状态
Falcon_Bp_RobotSingleGetOneStatusBp_Output_status_label=状态
Falcon_Bp_RobotSingleGetOneStatusBp_description=根据输入参数返回机器人的指定状态
Falcon_Bp_RobotSingleGetOneStatusBp_label=获取机器人指定状态
Falcon_Bp_RobotSingleJackingBp_Input_jackHeight_description=将顶升机构升起的高度，默认值 0.010（单位：m）
Falcon_Bp_RobotSingleJackingBp_Input_jackHeight_label=顶升高度
Falcon_Bp_RobotSingleJackingBp_Input_operation_Option_JackLoad=顶起（JackLoad）
Falcon_Bp_RobotSingleJackingBp_Input_operation_Option_JackUnload=放下（JackUnload）
Falcon_Bp_RobotSingleJackingBp_Input_operation_description=顶升机构可操作的动作
Falcon_Bp_RobotSingleJackingBp_Input_operation_label=动作
Falcon_Bp_RobotSingleJackingBp_Input_recfile_description=机器人执行特定动作时，需要用到此文件进行识别比对之类的
Falcon_Bp_RobotSingleJackingBp_Input_recfile_label=识别文件
Falcon_Bp_RobotSingleJackingBp_Input_reqId_description=组件执行时的标识，选填
Falcon_Bp_RobotSingleJackingBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleJackingBp_Input_station_description=导航的目标站点
Falcon_Bp_RobotSingleJackingBp_Input_station_label=目标站点
Falcon_Bp_RobotSingleJackingBp_Input_useDownPgv_description=使用下视 PGV
Falcon_Bp_RobotSingleJackingBp_Input_useDownPgv_label=使用下视 PGV
Falcon_Bp_RobotSingleJackingBp_Input_usePgv_description=使用上视 PGV
Falcon_Bp_RobotSingleJackingBp_Input_usePgv_label=使用上视 PGV
Falcon_Bp_RobotSingleJackingBp_Output_rbkResult_description=执行后的响应结果
Falcon_Bp_RobotSingleJackingBp_Output_rbkResult_label=rbk 请求结果
Falcon_Bp_RobotSingleJackingBp_description=操作顶升机构
Falcon_Bp_RobotSingleJackingBp_label=顶升
Falcon_Bp_RobotSingleNavigationBp_Input_reqId_description=组件执行时的标识，选填
Falcon_Bp_RobotSingleNavigationBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleNavigationBp_Input_station_description=导航的目标站点
Falcon_Bp_RobotSingleNavigationBp_Input_station_label=目标站点
Falcon_Bp_RobotSingleNavigationBp_Output_rbkResult_description=执行后的响应结果
Falcon_Bp_RobotSingleNavigationBp_Output_rbkResult_label=rbk 请求结果
Falcon_Bp_RobotSingleNavigationBp_description=导航机器人到目标站点
Falcon_Bp_RobotSingleNavigationBp_label=路径导航
Falcon_Bp_RobotSinglePlayAudioBp_Input_audioFilename_description=用于指定机器人播放的音频文件
Falcon_Bp_RobotSinglePlayAudioBp_Input_audioFilename_label=音频文件名
Falcon_Bp_RobotSinglePlayAudioBp_Input_loop_description=勾选循环播放则会重复播放该音频，否则只播放一次
Falcon_Bp_RobotSinglePlayAudioBp_Input_loop_label=循环播放
Falcon_Bp_RobotSinglePlayAudioBp_Input_reqId_description=组件执行时的标识，选填
Falcon_Bp_RobotSinglePlayAudioBp_Input_reqId_label=ID
Falcon_Bp_RobotSinglePlayAudioBp_Input_station_description=导航的目标站点
Falcon_Bp_RobotSinglePlayAudioBp_Input_station_label=目标站点
Falcon_Bp_RobotSinglePlayAudioBp_Output_rbkResult_description=执行后的响应结果
Falcon_Bp_RobotSinglePlayAudioBp_Output_rbkResult_label=rbk 请求结果
Falcon_Bp_RobotSinglePlayAudioBp_description=控制机器人播放音频
Falcon_Bp_RobotSinglePlayAudioBp_label=播放音频
Falcon_Bp_RobotSingleRollerBp_Input_direction_Option_back=后
Falcon_Bp_RobotSingleRollerBp_Input_direction_Option_front=前
Falcon_Bp_RobotSingleRollerBp_Input_direction_Option_left=左
Falcon_Bp_RobotSingleRollerBp_Input_direction_Option_right=右
Falcon_Bp_RobotSingleRollerBp_Input_direction_description=滚筒机构滚动的方向
Falcon_Bp_RobotSingleRollerBp_Input_direction_label=方向
Falcon_Bp_RobotSingleRollerBp_Input_operation_Option_RollerLoad=滚筒上料（RollerUnload）
Falcon_Bp_RobotSingleRollerBp_Input_operation_Option_RollerUnload=滚筒下料（RollerLoad）
Falcon_Bp_RobotSingleRollerBp_Input_operation_description=滚筒机构可操作的动作
Falcon_Bp_RobotSingleRollerBp_Input_operation_label=动作
Falcon_Bp_RobotSingleRollerBp_Input_reqId_description=组件执行时的标识，选填
Falcon_Bp_RobotSingleRollerBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleRollerBp_Input_station_description=导航的目标站点
Falcon_Bp_RobotSingleRollerBp_Input_station_label=目标站点
Falcon_Bp_RobotSingleRollerBp_Output_rbkResult_description=执行后的响应结果
Falcon_Bp_RobotSingleRollerBp_Output_rbkResult_label=rbk 请求结果
Falcon_Bp_RobotSingleRollerBp_description=操作滚筒机构
Falcon_Bp_RobotSingleRollerBp_label=滚筒
Falcon_Bp_RobotSingleRotateBp_Input_angle_description=控制机器人转动的角度
Falcon_Bp_RobotSingleRotateBp_Input_angle_label=转动角度（°）
Falcon_Bp_RobotSingleRotateBp_Input_mode_description=里程模式则根据里程进行运动, 定位模式则需要定位精准, 若缺省则默认为里程模式
Falcon_Bp_RobotSingleRotateBp_Input_mode_label=模式
Falcon_Bp_RobotSingleRotateBp_Input_reqId_description=组件执行时的标识，选填
Falcon_Bp_RobotSingleRotateBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleRotateBp_Input_vw_description=控制机器人转动的速度，默认值 45.0（°/s）
Falcon_Bp_RobotSingleRotateBp_Input_vw_label=转动角速度（°/s）
Falcon_Bp_RobotSingleRotateBp_Output_rbkResult_description=执行后的响应结果
Falcon_Bp_RobotSingleRotateBp_Output_rbkResult_label=rbk 请求结果
Falcon_Bp_RobotSingleRotateBp_description=控制机器人原地转动
Falcon_Bp_RobotSingleRotateBp_label=转动
Falcon_Bp_RobotSingleSetDOBp_Input_id_description=指定 DO 的编号
Falcon_Bp_RobotSingleSetDOBp_Input_id_label=DO 编号
Falcon_Bp_RobotSingleSetDOBp_Input_status_Option_status_0=0（禁用）
Falcon_Bp_RobotSingleSetDOBp_Input_status_Option_status_1=1（启用）
Falcon_Bp_RobotSingleSetDOBp_Input_status_description=将指定 DO 的状态修改为该状态
Falcon_Bp_RobotSingleSetDOBp_Input_status_label=目标状态
Falcon_Bp_RobotSingleSetDOBp_Output_rbkResult_description=执行后的响应结果
Falcon_Bp_RobotSingleSetDOBp_Output_rbkResult_label=rbk 请求结果
Falcon_Bp_RobotSingleSetDOBp_description=用于修改指定 DO 的状态
Falcon_Bp_RobotSingleSetDOBp_label=设置 DO
Falcon_Bp_RobotSingleStopAudioBp_Input_reqId_description=组件执行时的标识，选填
Falcon_Bp_RobotSingleStopAudioBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleStopAudioBp_Input_station_description=导航的目标站点
Falcon_Bp_RobotSingleStopAudioBp_Input_station_label=目标站点
Falcon_Bp_RobotSingleStopAudioBp_Output_rbkResult_description=执行后的响应结果
Falcon_Bp_RobotSingleStopAudioBp_Output_rbkResult_label=rbk 请求结果
Falcon_Bp_RobotSingleStopAudioBp_description=控制机器人停止播放音频
Falcon_Bp_RobotSingleStopAudioBp_label=停止播放
Falcon_Bp_RobotSingleTranslationBp_Input_dist_description=用于限定机器人运动的距离
Falcon_Bp_RobotSingleTranslationBp_Input_dist_label=直线运动距离（单位：m）
Falcon_Bp_RobotSingleTranslationBp_Input_mode_Option_0=里程模式
Falcon_Bp_RobotSingleTranslationBp_Input_mode_Option_1=定位模式
Falcon_Bp_RobotSingleTranslationBp_Input_mode_description=里程模式则根据里程进行运动, 定位模式则需要定位精准, 若缺省则默认为里程模式
Falcon_Bp_RobotSingleTranslationBp_Input_mode_label=模式
Falcon_Bp_RobotSingleTranslationBp_Input_reqId_description=组件执行时的标识，选填
Falcon_Bp_RobotSingleTranslationBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleTranslationBp_Input_vx_description=控制前后移动的速度，负值为后退，正值为前进，默认值 0.5（单位：m/s）
Falcon_Bp_RobotSingleTranslationBp_Input_vx_label=前进速度（vx，单位：m/s）
Falcon_Bp_RobotSingleTranslationBp_Input_vy_description=控制左右方向的速度，只对全向车有用，其他车型均为 0，默认值 0.0（单位：m/s）
Falcon_Bp_RobotSingleTranslationBp_Input_vy_label=横移速度（vy，单位：m/s）
Falcon_Bp_RobotSingleTranslationBp_Output_rbkResult_description=执行后的响应结果
Falcon_Bp_RobotSingleTranslationBp_Output_rbkResult_label=rbk 请求结果
Falcon_Bp_RobotSingleTranslationBp_description=控制机器人自由移动
Falcon_Bp_RobotSingleTranslationBp_label=平动
Falcon_Bp_RobotSingleUserDefinedBp_Input_apiNo_description=指令编号，默认是 3051
Falcon_Bp_RobotSingleUserDefinedBp_Input_apiNo_label=API 编号
Falcon_Bp_RobotSingleUserDefinedBp_Input_reqId_description=组件执行时的标识，选填
Falcon_Bp_RobotSingleUserDefinedBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleUserDefinedBp_Input_userDefinedAction_description=自定义的动作指令（JSON 字符串的形式）
Falcon_Bp_RobotSingleUserDefinedBp_Input_userDefinedAction_label=动作
Falcon_Bp_RobotSingleUserDefinedBp_Output_rbkResult_description=执行后的响应结果
Falcon_Bp_RobotSingleUserDefinedBp_Output_rbkResult_label=rbk 请求结果
Falcon_Bp_RobotSingleUserDefinedBp_description=让机器人执行一个自定义的动作
Falcon_Bp_RobotSingleUserDefinedBp_label=自定义动作
Falcon_Bp_RobotSingleWaitDIBp_Input_id_description=指定的 DI 的编号
Falcon_Bp_RobotSingleWaitDIBp_Input_id_label=DI 编号
Falcon_Bp_RobotSingleWaitDIBp_Input_status_Option_status_0=0（禁用）
Falcon_Bp_RobotSingleWaitDIBp_Input_status_Option_status_1=1（启用）
Falcon_Bp_RobotSingleWaitDIBp_Input_status_description=当 DI 的状态为期望状态时，表示 DI 被触发了。
Falcon_Bp_RobotSingleWaitDIBp_Input_status_label=期望状态
Falcon_Bp_RobotSingleWaitDIBp_Input_timeout_description=等待 DI 被触发的超时时间，不填则一直等待
Falcon_Bp_RobotSingleWaitDIBp_Input_timeout_label=超时时间（s）
Falcon_Bp_RobotSingleWaitDIBp_Output_rbkResult_description=执行后的响应结果
Falcon_Bp_RobotSingleWaitDIBp_Output_rbkResult_label=rbk 请求结果
Falcon_Bp_RobotSingleWaitDIBp_description=等待 DI 的状态变更为期望的状态。
Falcon_Bp_RobotSingleWaitDIBp_label=等待触发 DI
Falcon_Bp_RobotWaitDIBp_Input_id_description=指定的 DI 的编号
Falcon_Bp_RobotWaitDIBp_Input_id_label=DI 编号
Falcon_Bp_RobotWaitDIBp_Input_status_description=当 DI 的状态为期望状态时，表示 DI 被触发了。
Falcon_Bp_RobotWaitDIBp_Input_status_label=期望状态
Falcon_Bp_RobotWaitDIBp_Input_timeOut_description=等待 DI 被触发的超时时间，不填则一直等待
Falcon_Bp_RobotWaitDIBp_Input_timeOut_label=超时时间（s）
Falcon_Bp_RobotWaitDIBp_Input_tomId_description=《机器人应用》页面中“二代调度”的场景名称
Falcon_Bp_RobotWaitDIBp_Input_tomId_label=调度 ID
Falcon_Bp_RobotWaitDIBp_Input_vehicle_description=指定机器人
Falcon_Bp_RobotWaitDIBp_Input_vehicle_label=指定机器人
Falcon_Bp_RobotWaitDIBp_Output_result_description=执行后的响应结果
Falcon_Bp_RobotWaitDIBp_Output_result_label=等待结果
Falcon_Bp_RobotWaitDIBp_description=等待 DI 的状态变更为期望的状态
Falcon_Bp_RobotWaitDIBp_label=等待触发 DI

[fleet.error]
OpErrCodeNavTaskFailed=导航任务失败。机器人告警：{0}。路径导航 task id={1}。调度动作={2}。交管任务={3}。
OpErrCodeRobotDisposed=执行运单步骤过程中，机器人被销毁。
OpErrCodeSceneDisposed=执行运单步骤过程中，场景被销毁。
T00010001=路径规划参数校验未通过
T00010002=路径规划，根据起点路线编码 {0} 未找到路线，请排查此路线编码是否存在于当前机器人组 {1} 地图上
T00010003=路径规划，根据点编码未查询到点位信息，点编码 {0} ，所在机器人组 {2}，地图 {3}
T00010004=路径规划，{0} 未找到路径，起点 {1}，终点 {2}，可能和不可旋转点 {3} 或者带载找不到换向点将料架旋转到目标角度有关
T00010005=路径规划，机器人锁获取超时
T00010006=路径规划，机器人 {0} 停车超时，请检查是否可以申请到停车锁闭资源
T00010007=路径规划，机器人 {0} 带载 {1} 容器，但是找不到该容器配置的尺寸信息
T00010008=路径规划，{0} 有未知异常，需人工排查
T00010010=路径规划，{0} 规划成功
T00020001=机器人 {0} 构建锁闭失败，请检查所在机器人组 {1} 是否配置机器人尺寸信息
T00020002=机器人 {0} 顶升货架 {1} 角度不能为空
T00020003=机器人 {0} 容器类型 {1} 可能未和机器人组 {2} 绑定，请检查容器类型配置
T00020010={0} 申请空间资源失败，当前位置的空间资源被 {1} 占有，请将当前机器人移动到附近未被其他机器人占用的点位上,然后点击下方的按钮重试
T00020011=机器人 {0} 离线，且存在占有空间资源信息，请检查该机器人是否在系统中，若不在系统中，请点击下方按钮释放该机器人的空间资源
T00030001=机器人发生死锁，死锁车辆 {0}
T00030002=机器人 {0} 不能接推空闲车运单，原因是 {1}
T00030003=机器人 {0} 还持有运单 {1}，不能接推空闲车运单
btnReleaseSpaceLock=释放空间资源
btnResetSpaceLock=重置空间资源
err3066Cancelled=请求机器人的导航指令被取消，指令id：{0}
err3066Fail=请求机器人的导航指令失败，指令id：{0}。请查看机器人告警，确认失败原因
errAddStepLocationInvalid=使用 “binTask” 的 “站点或库位(location)” 的值必须是库位名称。
errAddStepTooManyOp=存在多个动作描述，请选择 “动作(operation)” 或者 “binTask” 。
errAddStepWhenSealed=运单号{0}已封口，不能继续追加
errAwaitMove=等待导航完成，但导航失败，具体原因：{0}
errChangeTrafficButOrders=错误：场景中有 {0} 个未完成的运单，不能修改交管策略。请等待运单完成或取消。
errCollisionModelEmpty=碰撞模型不能为空
errCreateRandomOrderNoArea=随机发单，失败：业务线 "{0}" 期望的机器人分组 "{1}"  。
errCreateRandomOrderNoRobot=随机发单，失败：业务线 "{0}" 期望的机器人分组 "{1}" 中没有可用的机器人。
errCreateStoOrderExists=运单已存在={0}
errCrossAreaButNoLift=没有可用电梯：机器人 "{0}" 执行运单步骤 "{1}" 从 "{2}" 到 "{3}" 失败
errCrossAreaLiftsAvailableNone=暂无可用电梯，机器人"{0}" 执行运单步骤 "{1}"，无法从 "{2}" 到 "{3}"。
errCurrentMapNotMatched=机器人 {0} 当前地图与所在区域地图不匹配
errDirectRobotOrderFailed=直接运单失败，单号={0}
errDirectRobotOrderNonexistent=直接运单不存在，单号={0}
errDoorNotFoundById=场景 "{0}" 中没有 ID 为 "{1}" 的门
errDoorReadFaultMsgButNull=获取自动门 "{0}" 的故障信息失败，读到的值为 null
errDoorReadStatusButNull=获取自动门 "{0}" 的状态失败，读到的值为 null
errDuplicatedSceneName=场景名称 {0} 已存在，请输入其他场景名称。
errEmptyKeyLocations=关键位置不能为空！
errEmptySceneName=场景名称不能为空，请输入有效场景名称。
errFailedToFetchMapFromRobot=从机器人 {0} 获取地图失败
errFalconFailed=猎鹰任务 <{0}> 失败。任务={1}。原因：{2}
errFaultContainer=容器编号为：{0}。
errFaultOptions=您可以排除故障后重试，或人工完成，或取消运单（取消后不可恢复）。
errFaultReason=故障原因：{0}。
errFaultReasonUnknown=故障原因未知。
errFetchChassisMode=获取底盘驱动类型失败
errFetchCurrentMapFail=获取机器人当前地图失败，机器人={0}，具体原因={1}
errFetchDoorReportFailed=获取门 "{0}" 的运行状态失败，原因：{1}
errFetchLiftReportFailed=获取电梯 "{0}" 的运行状态失败，原因：{1}
errFetchMapFail=读取机器人地图失败，机器人={0}，具体原因={1}
errFileNotFound=文件不存在 {0}
errFleetNoSuchSceneByName=无此场景：{0}
errGroupMapNoPointOrBin=机器组 <{0}> 地图中没有点位或库位 {1}
errGwNoLocalRobot=网关没有机器人 {0}
errIllegalPath=非法的文件路径  {0}
errInvalidPath=无效的文件路径 {0}
errKeyLocationNotReachable=运单关键位置无效
errLackPoint=缺少地图点位，机器人数：{0}，地图点位数：{1}
errLiftControl=控制电梯 {0} 失败，原因：{1}
errLiftDisabled=电梯 "{0}" 已停用。
errLiftJinBoFloorCodeMustInt=此电梯的「区域楼层 {1}」的「楼层编码」必须是整数。
errLiftJinBoNoClient=此电梯还未建立客户端。
errLiftJinBoReplyEmptyData=此电梯上报的信息为空。
errLiftNotFoundById=场景 "{0}" 中没有 ID 为 "{1}" 的电梯
errLiftNotFoundByName=场景 "{0}" 中没有名称为 "{1}" 的电梯
errLiftNotMock=电梯 "{0}" 未启用仿真模式。
errManualFinishStepNoExecutingSelected=人工完成运单步骤失败，机器人 {0} 的运单 {1} 没有执行中的步骤
errManualFinishStepsButNoRobot=人工完成运单步骤失败，场景中没有执行运单 {0} 的机器人 {1}
errMapNotFound=找不到地图，{0}
errMissingJson=请求参数缺失
errMockRobotNotFound=找不到仿真机器人 {0}
errMockRobotPointNotChange=机器人 {0} 的位置不能修改，机器人正在执行
errMotorNotFound=从模型文件中读取 motor 信息失败。
errMotorParamTypeUnsupported=模型文件的 {0} 的 {1} 使用了不支持的参数类型 {2}，支持的有 arrayParam 和 comboParam 。
errMotorPropNotFound=模型文件的 {0} 的 {1} 没有属性 {2} 。
errMotorTypeNotFound=模型文件的 {0} 没有类型为 {1} 的属性分类。
errMove3051=发送 3051 路径导航失败：{0}
errMove3066=发送 3066 路径导航失败：{0}
errMoveRobotFromToStation=机器人 {0} 当前在 {1}，请现将其移回路径起点 {2} 或终点 {3} 再重试
errMustGetRobotGroupByRobot=场景 {0} 中找不到机器人 {1} 的组
errNoAnyRobot=不存在机器人
errNoAreaById=找不到区域，id={0}
errNoBinOrLocation=没有目标库位或点位
errNoBins=库位 {0} 不存在或已停用
errNoCloseLiftFunc=在脚本中找不到电梯 {0}:{1} 的关门函数 {2}
errNoDefaultMap=找不到默认地图（第一个区域第一个车型）
errNoEnabledAreasForRobotGroup=机器人分组 "{0}" 所属的区域 "{2}" 都被禁用了，机器人 "{1}" 没有可用的地图
errNoEnabledScene=未启用任何场景
errNoFirstLightScene=没有任何光通讯场景
errNoFromPointName=找不到起始点 {0}
errNoKeyLocations=没有关键位置
errNoLocalRobot=找不到本地机器人 {0}
errNoOpenLiftFunc=在脚本中找不到电梯 {0}:{1} 的开门函数 {2}
errNoOrderById=找不到运单 {0}
errNoOrderStepById=找不到运单步骤 {0}
errNoOrders=没有符合条件的业务单
errNoPointOrDisabled=点位 {0} 不存在或已停用
errNoPointOrPath=地图 {0} 无点位或无路径
errNoProperty=无此属性 “{0}”
errNoRbkClient=与机器人通讯的客户端未初始化
errNoReportApplyUrl=没有指定上报地址
errNoReportLiftFunc=在脚本中找不到电梯 {0}:{1} 的上报信息函数 {2}
errNoRobot=无此机器人 "{0}"
errNoRobotGroupId=找不到机器人组 ID {0}
errNoRobotGroupName=找不到机器人组名 {0}
errNoRobots=找不到机器人：{0}
errNoSceneBasicFile=找不到场景基础文件
errNoSceneByOrderId=找不到运单号{0}对应的场景
errNoSuchBinOrLocation=找不到名为 {0} 点位或库位
errNoToPointName=找不到终点 {0}
errNotExistContainerType=容器类型名称 {0} 不存在
errNotRobotInTom=调度 {0} 里没有机器人 {1}
errOnlyOneSingleAppCanBeEnabled=系统只能启用一个单车应用
errOrderNoSceneId=运单 {0} 没有对应场景
errOrderStepFailed={0} 运单 {1} 第 {2} 步故障。
errOrderStepLocationNotInGroupMap=请检查当前运单第 {0} 步骤，其终点点位/库位 {1} 是否在当前机器人组所在的地图上
errPositionToNoSite=位置（{0},{1}）附近没有站点
errQuery3066BadTaskId=查询 3066 结果，任务 ID 不匹配，期望 {0}，实际 {1}
errRbkNotOk=RBK 报错。报错码={0}。消息={1}
errRbkResErr=rbk 请求失败，编号：{0}，类型：{1}，原因：{2}
errRbkResultKindConnectFail=rbk 连接失败
errRecAndForkLoadOverHeight=输入的取货前高度 {0} 超过货叉的最大举升高度 {1} 。
errRemoveAreaHasRobots=区域 {0} 内有机器人，不能删除或停用
errRemoveAreaHasRunningOrders=区域 {0} 内有正在运行的运单，不能删除或停用
errRemoveRobotGroupButNotEmpty=删除机器人分组 "{0}" 失败，此分组还存在机器人 "{1}" 。
errReplaying=重放正在进行
errRequestControl=请求控制权失败，机器人={0}
errRetryButOrderNotFailed=重试，但订单状态不是失败
errRobotAppNoControlPower=无控制权
errRobotAppNoScene=未配置场景
errRobotAppNoSceneById=无此场景：{0}
errRobotAppNoSceneByName=无此场景：{0}
errRobotAppNoSingleScene=场景 {0} 不是单车应用。
errRobotAppSceneDisabled=场景 {0} 已停用
errRobotBadGroup=错误：机器人 {0} 必须属于一个机器人组（或所属机器人组已删除）
errRobotBinNum=机器人 "{0}" 在系统中配置的最大载货数是 "{1}"，但实际读取到机器人只有 "{2}" 个库位，请检查配置
errRobotBinNumBadOrder=机器人 "{0}" 启用的背篓编号异常："{1}"；请按要求配置其模型文件的 "container"
errRobotBinPlusOne=机器人 "{0}" 在系统中配置了 "N+1"："{1}"，但实际读取到机器人 "N+1"："{2}"，请检查配置
errRobotBinPlusOneNotConfig=机器人 "{0}" 需要开启" N+1"，请检查配置
errRobotBinPlusOneNotMatchSelfNum=机器人 "{0}" 开启了 "N+1" 但最大载货数为："{1}"，请检查配置
errRobotBinPlusOneShouldNotConfig=机器人 "{0}" 配置的最大载货数 "{1}" 小于实际数量 "{2}" 时不允许配置 "N+1"，请检查配置
errRobotBinUpdate=配置更新失败，机器人 "{0}" 库位索引 "{1}" 上不是空的，当前请检查配置
errRobotDisabled=机器人 {0} 已停用
errRobotExecutingTask=机器人正在执行导航任务。
errRobotGroupDisabled=机器人组 {0} 已停用
errRobotHasSimpleOrderCurrent=机器人 {0} 还在执行运单 {1}，不能接收新单
errRobotIpConflict=错误：机器人 "{0}" 的 IP 地址 "{1}" 已被同场景中的机器人 {2} 使用。
errRobotIpMissing=错误：请输入机器人 "{0}" 的 IP 地址。
errRobotMapInconsistentWithScene=机器人地图与 {0} 场景地图不一致
errRobotMoveNoStart=找不到机器人 {0} 执行任务合适的起始点
errRobotNameDuplicated=错误：机器人名不能重复：{0}
errRobotNavNoId=路径导航请求错误，需要终点参数 'id'
errRobotNavNoSourceId=路径导航请求错误，需要起点参数 'source_id'
errRobotNoAnyScene=当前没有任何场景
errRobotNoConnector=没有与机器人 "{0}" 的连接器
errRobotNoCurrentStation=机器人 {0} 当前不在一个站点上
errRobotNoPosition=获取不到机器人 "{0}" 位置，请确认机器人在点位上
errRobotNotIdleCmdOrders=机器人运单不为空，机器人 {0}
errRobotNotIdleCmdStatus=机器人非空闲，机器人 {0}，cmdStatus={1}
errRobotNotInitialized=机器人未初始化，机器人 {0}
errRobotOffline=机器人 "{0}" 不在线或失去联系
errRobotRuntimeBinRemove=机器人 "{0}" 在系统配置的最大载货数和运行记录不匹配，尝试删除运行记录中的库位索引 "{1}" 失败，存在货物或运单
errRobotRuntimePlusOneRemove=机器人 "{0}" 在系统中没有配置 "N+1"，但运行记录中存在 "N+1" 库位相关的货物和运单，请检查配置
errRobotUpdateBusy=机器人正在工作，不能更新
errSceneDisabled=场景 "{0} ({1})" 已停用。
errSceneNameCannotBeBlank=场景名称不能为空
errSceneNotIdle=场景不是空闲的，存在运单，场景 ID {0}
errSimpleTestBadContainerTypeName=发单失败：当前场景 "{0}" 不存在容器类型 "{1}"
errSimpleTestBadInitialPos=发单失败：当前场景 "{0}" 不存在出生点 "{1}"
errSimpleTestBadPriority=发单失败：运单优先级的描述异常，原文是 "{0}"，请按要求填写后重试
errSimpleTestBadRobotName=发单失败：当前场景 "{0}" 不存在机器人 "{1}"
errSimpleTestBadStepAction=发单失败：第 "{0}" 个步骤缺少动作描述，请按要求填写后重试
errSimpleTestBadStepContainerDir=发单失败：第 "{0}" 个步骤的 "容器放货角度" 异常，原文是 "{1}"，请按要求填写后重试
errSimpleTestBadStepLocation=发单失败：第 "{0}" 个步骤缺少目标点位，请按要求填写后重试
errSimpleTestBadStepStruct=发单失败：第 "{0}" 个步骤的描述异常，原文是 "{1}"，请按要求填写后重试
errSimpleTestInitialPosOnlyForRealRobot=发单失败：机器人 "{0}" 不是仿真机器人，不能指定出生点 "{1}"
errSimpleTestLoadUnloadStepsInvalid=发单失败：运单中带 "load" 和 "unload" 动作描述的步骤必须成对出现，且最多只能有一组。
errStepBinTaskInvalid=第 {0} 步的目标库位 “{1}” 没有名称为 “{2}” 的 binTask。
errStepLocationInvalid=第 {0} 步使用 “binTask”，此步骤的 “站点或库位(location)” 的值必须是库位名称。
errStepRbkArgsInvalid=第 {0} 步的动作参数异常 {1}, 原因：{2}
errStepTooManyOp=第 {0} 步存在多个动作描述，请选择 “动作(operation)” 或者 “binTask” 。
errStoHikNotSupported=单车运单不支持海康机器人
errStoNoRobot=创建运单 {1} 失败，网关中无机器人 {0}
errStoRetryFailedBadCurrentStatus=当前运单 {0} 的状态不是失败而是 {1}
errStoRetryFailedNoCurrent=机器人当前没有运单
errTargetMotorNotFound=模型文件中没有名称为 {0} 的 motor 。
errTomBlockStopped=调度块 {0}:{1} 被停止，请人工处理，错误信息 {2}
errTomDisconnected=调度服务器不存在，或与其连接失败：{0}
errTomOrderAwaitNotFound=发送后查不到调度运单 "{0}"
errTomOrderKeyRouteMissing=必须指定至少一个关键位置
errTomOrderNoVehicle=运单 "{0}" 未分派机器人（运单状态：{1}）
errTomOrderNoVehicle2=运单 "{0}" 未分派机器人
errUnsupportedFetchMap=不支持以下连接方式获取地图：{0}
errUnsupportedFetchMap2=不支持在此种场景下获取地图
errorLocationEmpty=位置（Location）不能为空
errorTomOrderIdEmpty=调度运单号不能为空
warningNotFindStartPoint=机器人当前位置不在点上也不在线上，距离点和线超出允许范围 {0}（m）
warningRobotAreaMapNotFound=机器人当前地图：{0}，机器人组：{1} 的地图中无法找到该地图，请检查机器人地图与调度是否一致
warningRobotFetchError=获取机器人 "{0}" 的状态失败
warningStepSelectError=机器人 "{0}" 无法执行下一步，货叉上有货，请及时处理

[stats]
StatsLabel_FalconTaskCreate=生成的猎鹰任务记录数量
StatsLabel_FalconTaskDone=完成的猎鹰任务记录数量
StatsLabel_FalconTaskError=异常的猎鹰任务记录数量
StatsLabel_QsInboundInvQty=入库数量
StatsLabel_QsInboundOrderCount=入库单数
StatsLabel_QsOutboundInvQty=出库数量
StatsLabel_QsOutboundOrderCount=出库单数
StatsLabel_QsStatsInvSummary=期末库存总数量
StatsLabel_QsStatsMaterialCount=期末库存物料种数
StatsLabel_RobotChargeDuration=充电时长
StatsLabel_RobotChargeTimes=充电次数
StatsLabel_RobotEmptyDuration=空载时长
StatsLabel_RobotErrorDuration=故障时长
StatsLabel_RobotErrorTimes=故障次数
StatsLabel_RobotFailureRate=故障率
StatsLabel_RobotIdleDuration=空闲时长
StatsLabel_RobotIdleRate=空闲率
StatsLabel_RobotLoadDuration=载货时长
StatsLabel_RobotMileage=新增里程
StatsLabel_RobotNoLoadRate=空载率
StatsLabel_RobotOnlineDuration=在线时长
StatsLabel_RobotWorkDuration=工作时长
StatsLabel_RobotWorkTimes=工作次数
StatsLabel_period=周期
StatsLabel_subject=科目
StatsLabel_target=对象

[entity]
entity.DemoEntity.fields.componentTableField.tip=组件表格组件表格组件表格组件表格组件表格组件表格组件表格
entity.DemoEntity.fields.fileField.tip=上传文件上传文件上传文件上传文件上传文件上传文件上传文件上传文件上传文件上传文件上传文件
entity.DemoEntity.fields.floatField.tip=输入说明输入说明输入说明输入说明输入说明输入说明
entity.DemoEntity.fields.id.tip=输入说明
entity.DemoEntity.fields.imageField.tip=上传文件上传文件上传文件上传文件上传文件上传文件上传文件上传文件
entity.DemoEntity.fields.stringField.tip=输入说明

[entity.i18n]

[qs]
Falcon_BlockGroup_QuickStoreBc=QS 容器库位
Falcon_BlockGroup_QuickStoreBin=QS 库位
Falcon_BlockGroup_QuickStoreContainer=QS 容器
Falcon_BlockGroup_QuickStoreInv=QS 库存
Falcon_Bp_QsBindBinContainerBp_Input_binId_description=库位 ID
Falcon_Bp_QsBindBinContainerBp_Input_binId_label=库位编号
Falcon_Bp_QsBindBinContainerBp_Input_containerId_description=容器编号
Falcon_Bp_QsBindBinContainerBp_Input_containerId_label=容器编号
Falcon_Bp_QsBindBinContainerBp_description=将库位与容器进行绑定
Falcon_Bp_QsBindBinContainerBp_label=QS 绑定库位容器
Falcon_Bp_QsCreateInvFromOrderBp_Input_bin_description=库位
Falcon_Bp_QsCreateInvFromOrderBp_Input_bin_label=库位
Falcon_Bp_QsCreateInvFromOrderBp_Input_container_description=容器
Falcon_Bp_QsCreateInvFromOrderBp_Input_container_label=容器
Falcon_Bp_QsCreateInvFromOrderBp_Input_copyFields_description=多个字段用“,”分隔，会将这些字段以及对应的数据拷贝到库存信息中。
Falcon_Bp_QsCreateInvFromOrderBp_Input_copyFields_label=拷贝字段
Falcon_Bp_QsCreateInvFromOrderBp_Input_lotNoFormat_label=库位编号格式
Falcon_Bp_QsCreateInvFromOrderBp_Input_materialFieldName_description=请输入单据上表示物料的字段名
Falcon_Bp_QsCreateInvFromOrderBp_Input_materialFieldName_label=物料字段
Falcon_Bp_QsCreateInvFromOrderBp_Input_order_description=请输入单据对象
Falcon_Bp_QsCreateInvFromOrderBp_Input_order_label=单据
Falcon_Bp_QsCreateInvFromOrderBp_Input_qtyFieldName_description=请输入单据上表示数量的字段名
Falcon_Bp_QsCreateInvFromOrderBp_Input_qtyFieldName_label=数量字段
Falcon_Bp_QsCreateInvFromOrderBp_Input_state_description=默认的三种存储状态：Received:已收货；Storing:存储中；Assigned:已分配。
Falcon_Bp_QsCreateInvFromOrderBp_Input_state_label=库存状态
Falcon_Bp_QsCreateInvFromOrderBp_description=根据单据信息创建库存信息
Falcon_Bp_QsCreateInvFromOrderBp_label=从单据创建库存
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_bin_description=库位
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_bin_label=库位
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_container_description=容器
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_container_label=容器
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_copyFields_description=多个字段用“,”分隔，会将这些字段以及对应的数据拷贝到库存信息中。
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_copyFields_label=从单行上拷贝以下字段
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_materialFieldName_description=请输入单行上表示物料的字段名。
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_materialFieldName_label=单行上的物料字段名
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_order_description=请输入单据对象
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_order_label=单据对象
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_qtyFieldName_description=请输入单行上表示数量的字段名。
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_qtyFieldName_label=单行上的数量字段名
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_state_description=默认的三种存储状态：Received:已收货；Storing:存储中；Assigned:已分配。
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_state_label=存储状态
Falcon_Bp_QsCreateInvFromOrderLinesBp_description=根据单行信息创建库存信息
Falcon_Bp_QsCreateInvFromOrderLinesBp_label=从单据单行创建库存
Falcon_Bp_QsCreateTraceContainerBp_Input_containerType_description=请输入 M4 记录的容器类型。
Falcon_Bp_QsCreateTraceContainerBp_Input_containerType_label=容器类型
Falcon_Bp_QsCreateTraceContainerBp_Output_containerId_description=新建的容器编号。
Falcon_Bp_QsCreateTraceContainerBp_Output_containerId_label=容器编号
Falcon_Bp_QsCreateTraceContainerBp_description=创建追踪容器。
Falcon_Bp_QsCreateTraceContainerBp_label=创建追踪容器
Falcon_Bp_QsFindEmptyBinBp_Input_cq_description=寻找空库位的条件
Falcon_Bp_QsFindEmptyBinBp_Input_cq_label=条件
Falcon_Bp_QsFindEmptyBinBp_Input_districtIds_label=库区列表
Falcon_Bp_QsFindEmptyBinBp_Input_keepTrying_description=为 true 时重复寻找空库位直到成功，为 false 则只找一次
Falcon_Bp_QsFindEmptyBinBp_Input_keepTrying_label=反复尝试至成功
Falcon_Bp_QsFindEmptyBinBp_Input_sort_description=例如 ["row", "column", "layer"]
Falcon_Bp_QsFindEmptyBinBp_Input_sort_label=排序规则
Falcon_Bp_QsFindEmptyBinBp_Output_binId_description=找到的库位 ID
Falcon_Bp_QsFindEmptyBinBp_Output_binId_label=库位 ID
Falcon_Bp_QsFindEmptyBinBp_Output_found_description=true 为找到了，false 为未找到
Falcon_Bp_QsFindEmptyBinBp_Output_found_label=是否找到
Falcon_Bp_QsFindEmptyBinBp_description=在库区中寻找空库位
Falcon_Bp_QsFindEmptyBinBp_label=QS 寻空库位
Falcon_Bp_QsFindEmptyContainerBp_Input_bzDesc_description=业务描述。
Falcon_Bp_QsFindEmptyContainerBp_Input_bzDesc_label=业务描述
Falcon_Bp_QsFindEmptyContainerBp_Input_bzMark_description=业务标记。
Falcon_Bp_QsFindEmptyContainerBp_Input_bzMark_label=业务标记
Falcon_Bp_QsFindEmptyContainerBp_Input_districtIds_description=库区编号。请根据需求，输入 M4 记录的库区编号。
Falcon_Bp_QsFindEmptyContainerBp_Input_districtIds_label=库区 Id
Falcon_Bp_QsFindEmptyContainerBp_Input_groupPriority_description=组优先级。
Falcon_Bp_QsFindEmptyContainerBp_Input_groupPriority_label=组优先级
Falcon_Bp_QsFindEmptyContainerBp_Input_priority_description=优先级。
Falcon_Bp_QsFindEmptyContainerBp_Input_priority_label=优先级
Falcon_Bp_QsFindEmptyContainerBp_Input_sort_description=目标库区内库位的排序方式。
Falcon_Bp_QsFindEmptyContainerBp_Input_sort_label=排序
Falcon_Bp_QsFindEmptyContainerBp_Input_timeout_description=等待超时时间。
Falcon_Bp_QsFindEmptyContainerBp_Input_timeout_label=超时时间
Falcon_Bp_QsFindEmptyContainerBp_Output_binId_description=存放目标空容器的库位的编号。
Falcon_Bp_QsFindEmptyContainerBp_Output_binId_label=库位编号
Falcon_Bp_QsFindEmptyContainerBp_Output_containerId_description=被找到的空容器的编号。
Falcon_Bp_QsFindEmptyContainerBp_Output_containerId_label=容器编号
Falcon_Bp_QsFindEmptyContainerBp_Output_found_description=true:已找到；false:未找到。
Falcon_Bp_QsFindEmptyContainerBp_Output_found_label=是否找到找空容器
Falcon_Bp_QsFindEmptyContainerBp_description=按照期望的规则，从目标库区寻找空容器，并锁定此容器。
Falcon_Bp_QsFindEmptyContainerBp_label=找空容器
Falcon_Bp_QsGetBinContainerBp_Input_binId_description=库位 ID
Falcon_Bp_QsGetBinContainerBp_Input_binId_label=库位编号
Falcon_Bp_QsGetBinContainerBp_Output_binEmpty_description=容器是否为空，true 为空
Falcon_Bp_QsGetBinContainerBp_Output_binEmpty_label=库位为空
Falcon_Bp_QsGetBinContainerBp_Output_containerId_label=容器编号
Falcon_Bp_QsGetBinContainerBp_description=获获取库位上的容器，获取的前提是之前库位与容器绑定了，如果获取不到，容器编号返回 null
Falcon_Bp_QsGetBinContainerBp_label=QS 获取库位上的容器
Falcon_Bp_QsGetContainerBinBp_Input_containerId_label=容器编号
Falcon_Bp_QsGetContainerBinBp_Output_binId_label=库位编号
Falcon_Bp_QsGetContainerBinBp_Output_found_description=是否找到，true 为找到
Falcon_Bp_QsGetContainerBinBp_Output_found_label=找到库位
Falcon_Bp_QsGetContainerBinBp_description=获取容器所在的库位，获取的前提是之前库位与容器绑定了，如果获取不到，库位 ID 返回 null
Falcon_Bp_QsGetContainerBinBp_label=QS 获取容器所在库位
Falcon_Bp_QsGetContainerInvBp_Input_containerId_description=请输入 M4 记录的容器编号
Falcon_Bp_QsGetContainerInvBp_Input_containerId_label=容器编号
Falcon_Bp_QsGetContainerInvBp_Output_found_description=true:已找到；false:未找到。
Falcon_Bp_QsGetContainerInvBp_Output_found_label=是否找到
Falcon_Bp_QsGetContainerInvBp_Output_inv_description=库存明细单。
Falcon_Bp_QsGetContainerInvBp_Output_inv_label=库存明细单
Falcon_Bp_QsGetContainerInvBp_description=根据容器编号获取对应的库存明细。
Falcon_Bp_QsGetContainerInvBp_label=获取容器内库存明细
Falcon_Bp_QsGetContainerTypeStoreDistrictsBp_Input_containerId_description=请输入 M4 记录的容器的编号。
Falcon_Bp_QsGetContainerTypeStoreDistrictsBp_Input_containerId_label=容器编号
Falcon_Bp_QsGetContainerTypeStoreDistrictsBp_Output_storeDistricts_description=库区名称的集合，数组。
Falcon_Bp_QsGetContainerTypeStoreDistrictsBp_Output_storeDistricts_label=存储区
Falcon_Bp_QsGetContainerTypeStoreDistrictsBp_description=根据容器编号，查询可以存放此类容器的库区的名称。
Falcon_Bp_QsGetContainerTypeStoreDistrictsBp_label=查询容器的类型的存储区
Falcon_Bp_QsKeepTryingLockBinBp_Input_binId_label=库位 ID
Falcon_Bp_QsKeepTryingLockBinBp_Input_bzDesc_description=bzDesc
Falcon_Bp_QsKeepTryingLockBinBp_Input_bzDesc_label=锁定的原因
Falcon_Bp_QsKeepTryingLockBinBp_label=QS 反复尝试锁定库位直到成功
Falcon_Bp_QsKeepTryingLockEmptyBinBp_Input_binId_label=库位 ID
Falcon_Bp_QsKeepTryingLockEmptyBinBp_Input_bzDesc_description=bzDesc
Falcon_Bp_QsKeepTryingLockEmptyBinBp_Input_bzDesc_label=锁定的原因
Falcon_Bp_QsKeepTryingLockEmptyBinBp_label=QS 等待库位变空后锁定
Falcon_Bp_QsLockBinOnceBp_Input_binId_label=库位 ID
Falcon_Bp_QsLockBinOnceBp_Input_bzDesc_description=bzDesc
Falcon_Bp_QsLockBinOnceBp_Input_bzDesc_label=锁定的原因
Falcon_Bp_QsLockBinOnceBp_Input_notFoundToAborted_description=失败是否需要让任务终止，true 为需要
Falcon_Bp_QsLockBinOnceBp_Input_notFoundToAborted_label=找不到终止任务
Falcon_Bp_QsLockBinOnceBp_Input_notFoundToFault_description=失败是否需要让任务故障，true 为需要
Falcon_Bp_QsLockBinOnceBp_Input_notFoundToFault_label=找不到让任务故障
Falcon_Bp_QsLockBinOnceBp_Output_ok_description=成功为 true，失败为 false
Falcon_Bp_QsLockBinOnceBp_Output_ok_label=是否锁定成功
Falcon_Bp_QsLockBinOnceBp_description=尝试锁定一次未锁定的库位，成功返回 true，否则返回 false
Falcon_Bp_QsLockBinOnceBp_label=QS 锁定未锁定的库位
Falcon_Bp_QsMoveInvByContainerBp_Input_leafContainerId_description=请输入最内层容器的编号。
Falcon_Bp_QsMoveInvByContainerBp_Input_leafContainerId_label=最内层容器
Falcon_Bp_QsMoveInvByContainerBp_Input_state_description=将库存状态修改为指定的库存状态
Falcon_Bp_QsMoveInvByContainerBp_Input_state_label=指定库存状态
Falcon_Bp_QsMoveInvByContainerBp_Input_toBinId_description=将要放置最内层容器的库位的编号。
Falcon_Bp_QsMoveInvByContainerBp_Input_toBinId_label=终点库位
Falcon_Bp_QsMoveInvByContainerBp_description=将最内层容器信息，绑定到另一个库位上。
Falcon_Bp_QsMoveInvByContainerBp_label=按容器移动库存
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_binField_description=请输入单行上表示库位的字段名。
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_binField_label=库位字段
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_ev_description=请输入单据信息
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_ev_label=单据
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_materialField_description=请输入单行上表示物料的字段名
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_materialField_label=物料字段
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_outboundOrderIdField_label=出库单号
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_pickOrderIdField_label=分拣单号
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_qtyField_description=请输入单行上表示数量的字段名。
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_qtyField_label=数量字段
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_subContainerIdField_label=格子
Falcon_Bp_QsReduceBinInvFromOrderBp_description=按单据从库位删除库存
Falcon_Bp_QsReduceBinInvFromOrderBp_label=按单据从库位删除库存
Falcon_Bp_QsRemoveBzMarkBp_Input_binId_label=库位编号
Falcon_Bp_QsRemoveBzMarkBp_Input_requireLock_description=是否要求该库位为锁定库位 未锁定则异常终止
Falcon_Bp_QsRemoveBzMarkBp_Input_requireLock_label=要求该库位为锁定库位
Falcon_Bp_QsRemoveBzMarkBp_description=解锁指定库位
Falcon_Bp_QsRemoveBzMarkBp_label=QS 解锁库位
Falcon_Bp_QsRemoveInvByContainerBp_Input_containerId_description=请输入 M4 记录的容器编号。
Falcon_Bp_QsRemoveInvByContainerBp_Input_containerId_label=容器编号
Falcon_Bp_QsRemoveInvByContainerBp_Output_count_description=被删除的库存明细数量。
Falcon_Bp_QsRemoveInvByContainerBp_Output_count_label=明细数量
Falcon_Bp_QsRemoveInvByContainerBp_description=根据容器编号删除对应的库存明细。
Falcon_Bp_QsRemoveInvByContainerBp_label=按容器删除库存明细
Falcon_Bp_QsRemoveTraceContainerBp_Input_containerId_description=请输入 M4 记录的追踪容器的编号。
Falcon_Bp_QsRemoveTraceContainerBp_Input_containerId_label=容器编号
Falcon_Bp_QsRemoveTraceContainerBp_description=删除追踪容器。
Falcon_Bp_QsRemoveTraceContainerBp_label=删除追踪容器
Falcon_Bp_QsTakeOffContainerBp_Input_binId_label=库位 ID
Falcon_Bp_QsTakeOffContainerBp_Input_removingInv_label=删除容器内库存
Falcon_Bp_QsTakeOffContainerBp_Input_unlockAfter_description=true 为设置未占用并解锁库位，false 为设置未占用不解锁库位
Falcon_Bp_QsTakeOffContainerBp_Input_unlockBin_label=解锁
Falcon_Bp_QsTakeOffContainerBp_description=下架库位上的容器，并可业务标记
Falcon_Bp_QsTakeOffContainerBp_label=QS 置空库位
Falcon_Bp_QsUnbindBinContainerBp_Input_binId_description=库位 ID
Falcon_Bp_QsUnbindBinContainerBp_Input_binId_label=库位编号
Falcon_Bp_QsUnbindBinContainerBp_Input_containerId_label=容器编号
Falcon_Bp_QsUnbindBinContainerBp_Input_removingInv_description=true 则删除容器内的库存明细
Falcon_Bp_QsUnbindBinContainerBp_Input_removingInv_label=删除库存
Falcon_Bp_QsUnbindBinContainerBp_Input_unlockBin_label=解锁库位
Falcon_Bp_QsUnbindBinContainerBp_Input_unlockContainer_label=解锁容器
Falcon_Bp_QsUnbindBinContainerBp_description=将库位与容器解绑
Falcon_Bp_QsUnbindBinContainerBp_label=QS 解绑库位容器
errBinContainer1Container2=期望库位 {0} 上有容器 {1}，但实际有 {2}
errContainerNoOnBin=容器 {0} 不在库位 {1} 上
errMoveContainerToBinOldContainer=要把容器 {0} 移动到库位 {1} 上，但库位上已经有容器 {2}
errNoBinInvByBin=找不到库位库存，库位={0}
errNoBinInvByContainer=找不到容器库存，容器={0}
errNoContainerByBin=找不到绑定的容器，库位={0}

[fleet.diagnosis]
FleetCheckItem_BackgroundJob=后台任务正常的机器人才能派单/移动
FleetCheckItem_ExpectedRobotExecutingOtherOrder=期待执行的机器人是否正在执行其他运单
FleetCheckItem_FirstStepDone=运单的第一个步骤完成就不可重分派
FleetCheckItem_LiftAvailableWhenCrossArea=机器人要乘梯上下楼时，有可用电梯
FleetCheckItem_NotReallocation=开启禁用重分派后已分派的运单就不允许重分派
FleetCheckItem_OnDutyRobotsUnreachable=可接单的机器人路径不可达
FleetCheckItem_OrderFault=运单没有故障，才允许被执行
FleetCheckItem_OrderFindNextStepFail=运单没有可执行的步骤
FleetCheckItem_OrderIsAllocated=运单已分派才能继续执行
FleetCheckItem_OrderLoaded=运单取货之后就不允许重分派了
FleetCheckItem_OrderNoNextStep=运单有后续的待执行步骤才能够继续执行运单
FleetCheckItem_OrderNotBusiness=运单是业务单时才能被分派
FleetCheckItem_OrderNotToBeAllocated=运单状态是否为待分派
FleetCheckItem_OrderNotWithdrawnOrCancelled=运单状态是撤回或取消时不允许分派
FleetCheckItem_OrderParametersValidate=检测运单参数是否合法
FleetCheckItem_OrderReachability=运单的关键路径对场景中机器人是可达的，运单才能执行
FleetCheckItem_OrderStepCannotAchievable=运单中步骤对场景中机器人是可达的，运单步骤才能执行
FleetCheckItem_RobotAlreadyParked=机器人已经在停靠点上时无需停靠
FleetCheckItem_RobotAutoOrder=机器人正在执行自动运单（如充电、停靠）时无需停靠
FleetCheckItem_RobotBatteryBelowChargeNeed=机器人电量低于 chargeNeed 阈值时才会生成充电任务
FleetCheckItem_RobotBatteryBelowChargeOnly=机器人电量低于 chargeOnly 阈值时必须去充电
FleetCheckItem_RobotBatteryExceedChargeOnly=机器人电量高于 chargeOnly 阈值时才可接单
FleetCheckItem_RobotBlocked=机器人没有被阻挡才能继续执行
FleetCheckItem_RobotBlockedByTraffic=机器人能获取到交管资源才能继续执行
FleetCheckItem_RobotBusy=机器人在执行业务单的话，就不能执行停靠或充电
FleetCheckItem_RobotCanReachOrder=检测这个运单对该机器人是否是可达的
FleetCheckItem_RobotCanReachPoint=检测机器人是否能够到达对应点位
FleetCheckItem_RobotCharging=机器人如果正在充电则无需生成充电任务
FleetCheckItem_RobotChargingNotEnoughTime=机器人没有在充电或者不是刚开始充电，才能接单
FleetCheckItem_RobotChargingNotFull=机器人如果正在充电的话，需要充电到预设的 full 电量才会去停靠
FleetCheckItem_RobotChargingPaused=自动充电功能未暂停才能生成充电任务
FleetCheckItem_RobotChargingPointAvailable=机器人有可用的充电点时，机器人才能去充电
FleetCheckItem_RobotCmdInterrupted=机器人运单状态不为被打断时才能接单
FleetCheckItem_RobotCompositiveControlledFailed=机器人不是系统受控状态，请检查机器人禁用、控制权、接单、急停状态以及系统急停状态
FleetCheckItem_RobotCurrentPointNameNotBlank=机器人起始位置不为空才能执行任务
FleetCheckItem_RobotDisabled=未停用的机器人才能接单
FleetCheckItem_RobotFailed=机器人只有没有出现故障的时候才能接单,包括业务层故障以及机器人上报故障
FleetCheckItem_RobotForceCharging=未处于强冲状态的机器人才能接单
FleetCheckItem_RobotIsOrderExpected=该机器人是该运单期望的时候才能接单
FleetCheckItem_RobotNoMoreBin=有可用库位的机器人才能接单
FleetCheckItem_RobotNoOrder=有运单的机器人才可以移动
FleetCheckItem_RobotNotMaster=机器人的控制权在调度（或未被任何系统控制）时才能接单
FleetCheckItem_RobotOffDuty=机器人的接单状态为可接单时才能接单
FleetCheckItem_RobotOffline=在线的机器人才能接单，离线不能接单
FleetCheckItem_RobotOrdersFault=机器人接的运单必须没有故障时才可以继续执行
FleetCheckItem_RobotParkPointAvailable=机器人有可用的停靠点时才能去停靠
FleetCheckItem_RobotParkingPaused=自动停靠功能未暂停时才能生成停靠任务
FleetCheckItem_RobotPendingTrafficTaskCancelling=机器人在执行交管任务正在取消中，需要等待取消完毕
FleetCheckItem_RobotToCharging=机器人正在执行充电任务时无需再生成新的充电任务
FleetCheckItem_RobotToPark=机器人在执行停靠任务时无需再生成新的停靠任务
FleetCheckItem_RobotWaitIdleTimeoutToPark=机器人执行完任务后满足空闲等待时间后才会去停靠
FleetCheckItem_SelfReportError=机器人自身没有上报错误才能接单
FleetCheckItem_TrafficNotReady=必须等待交管初始化完成
FleetCheckItem_TrafficPlanPaused=交管路径规划必须没有暂停时，机器人才能继续执行
FleetCheckItem_validateRobotsStatus=当前运单有可用机器人时才能继续执行
FleetDiagnosis_AllRobotUnDispatchable=所有期待执行的机器人都无法接这个单，不接单原因：{0}
FleetDiagnosis_BackgroundJobFailed=以下后台任务停止：{0}
FleetDiagnosis_ErrKeyLocationInvalidate=运单关键位置无效
FleetDiagnosis_ErrorCurrentStepIndex=当前步骤的索引错误
FleetDiagnosis_ErrorLoad=不涉及装卸货时，完成了装货或者卸货
FleetDiagnosis_ErrorOrderStepIndex=运单的步骤索引错误
FleetDiagnosis_ErrorStepIndex=运单步骤的索引错误
FleetDiagnosis_ErrorStepSize=步骤数量与实际的步骤数不一致
FleetDiagnosis_ExpectedRobotExecutingOtherOrder=期待执行的机器人正在执行其他运单
FleetDiagnosis_FirstStepDone=第一个运单步骤已经完成
FleetDiagnosis_FromPointNullOrBlank=机器人初始位置为空，不能执行任务
FleetDiagnosis_KeyLocationsNull=关键位置为空
FleetDiagnosis_LiftsAvailableNone=暂无可用电梯
FleetDiagnosis_MissingGroups=运单期待执行的机器人组 {0} 不存在
FleetDiagnosis_NoAvailableRobots=没有可接单的机器人
FleetDiagnosis_NoExpectedRobot=运单没有期待执行的机器人
FleetDiagnosis_NotReallocation=运单已禁用重分派
FleetDiagnosis_OnDutyRobotsUnreachable=可接单的机器人 {0} 路径不可达
FleetDiagnosis_OrderFault=运单为故障单
FleetDiagnosis_OrderIsNull=运单为空
FleetDiagnosis_OrderLoaded=运单已取货
FleetDiagnosis_OrderNoNextStep=运单没有下一个步骤，期待封口
FleetDiagnosis_OrderNotAllocated=运单未被分派，处于待分派状态
FleetDiagnosis_OrderNotBusiness=运单不是业务单，运单类型：{0}
FleetDiagnosis_OrderNotToBeAllocated=运单状态不是待分派状态，状态：{0}
FleetDiagnosis_OrderNotWithdrawnOrCancelled=运单状态为撤回或取消，状态：{0}
FleetDiagnosis_OrderStepCannotAchievable=机器人当前位置为 {0}，运单下一步的终点 {1} 不可达
FleetDiagnosis_OrderStepPlusOneMustUnload=运单的下一步必须是 "N+1" 放货
FleetDiagnosis_PointIsNull=点位名称为空
FleetDiagnosis_RobotAlreadyParked=机器人已经在停靠点了,无需继续停靠
FleetDiagnosis_RobotAutoOrder=机器人正在执行自动运单（如充电、停靠）
FleetDiagnosis_RobotBatteryBelowChargeNeed=机器人电量充足，高于 chargeNeed 阈值={0}
FleetDiagnosis_RobotBatteryBelowChargeOnly=机器人电量足够，高于 chargeOnly 阈值={0}
FleetDiagnosis_RobotBatteryExceedChargeOnly=机器人电量过低，只能去充电={0}
FleetDiagnosis_RobotBatteryLowButBusy=机器人虽然电量低，但正在执行业务运单，不能充电
FleetDiagnosis_RobotBlocked=机器人被阻挡
FleetDiagnosis_RobotBlockedByTraffic=机器人被阻挡：{0}
FleetDiagnosis_RobotBusy=机器人正在执行业务运单
FleetDiagnosis_RobotCanReachOrder=该运单的关键路径对机器人不可达，请检查
FleetDiagnosis_RobotCanReachPoint=机器人无法到达对应点位，请检查地图
FleetDiagnosis_RobotChargeOnly=机器人电量过低，只能去充电
FleetDiagnosis_RobotCharging=机器人正在充电
FleetDiagnosis_RobotChargingNotEnoughTime=机器人开始充电了，至少充一段时间，不能立即结束
FleetDiagnosis_RobotChargingNotFull=机器人正在充电，但没达到预设的 full 电量
FleetDiagnosis_RobotChargingPaused=机器人已关闭自动充电
FleetDiagnosis_RobotCmdInterrupted=机器人正在撤销上一运单，请稍等
FleetDiagnosis_RobotCurrentPointNameNullOrBlank=机器人初始位置为空，不能执行任务
FleetDiagnosis_RobotDisabled=机器人被停用，请先启用该机器人
FleetDiagnosis_RobotFailed=机器人执行故障，故障原因={0}，请先找到故障的机器人并故障重试尝试恢复
FleetDiagnosis_RobotForceCharging=机器人处于强充状态
FleetDiagnosis_RobotGroupUnreachable=运单期待执行的机器人组 {0}，不可达关键位置 {1}
FleetDiagnosis_RobotIsNull=机器人为空
FleetDiagnosis_RobotNoAvailableChargingPoint=地图上没有空闲的充电点，请检查
FleetDiagnosis_RobotNoAvailableChargingPointByCollision=地图上有空闲的充电点但可能会碰撞:{0},请检查
FleetDiagnosis_RobotNoAvailableParkPoint=地图上没有空闲的停靠点，请检查
FleetDiagnosis_RobotNoAvailableParkPointByCollision=地图上有空闲的停靠点但可能会碰撞:{0},请检查
FleetDiagnosis_RobotNoMoreBin=机器人没有更多库位（载货能力）了
FleetDiagnosis_RobotNoOrder=机器人当前没有接运单，请检查机器人能否接单以及运单能否被执行
FleetDiagnosis_RobotNotChargeNeed=机器人当前电量大于可以充电的电量，不需要去充电
FleetDiagnosis_RobotNotChargeOnly=机器人当前电量大于“必须充电”的电量，不需要强充
FleetDiagnosis_RobotNotInterruptible=机器人正在执行不可中断的任务
FleetDiagnosis_RobotNotMaster=没有控制权，请先获取控制权
FleetDiagnosis_RobotNotOrderExpectedRobotGroups=该运单期待的机器人组为={0},该机器人不满足
FleetDiagnosis_RobotNotOrderExpectedRobotNames=该运单期待的机器人为={0},该机器人不满足
FleetDiagnosis_RobotOffDuty=机器人被设置为不接单，请先设置机器人接单
FleetDiagnosis_RobotOffline=机器人断连，请检查
FleetDiagnosis_RobotParkingPaused=机器人已关闭自动停靠
FleetDiagnosis_RobotPendingTrafficTaskCancelling=交管任务正在取消中。请稍候
FleetDiagnosis_RobotSelfFailed=机器人自身上报故障，故障原因={0}，请先处理 rbk 故障
FleetDiagnosis_RobotToCharging=机器人正在去充电
FleetDiagnosis_RobotToPark=机器人正在执行停靠
FleetDiagnosis_RobotWaitIdleTimeoutToPark=机器人需要空闲一段时间才会去停靠，还没超时
FleetDiagnosis_RobotsAllUnreachable=可用的机器人都不可达关键位置 {0}
FleetDiagnosis_RobotsOrderFault=机器人的运单故障
FleetDiagnosis_RobotsUnreachable=运单期待执行的机器人 {0}，不可达关键位置 {1}
FleetDiagnosis_SelfReportError=机器人上报了错误={0}
FleetDiagnosis_TrafficNotReady=请等待交管初始化完成
FleetDiagnosis_TrafficPlanPaused=交管路径规划被暂停
FleetDiagnosis_errNoFromPointName=找不到起始点 {0}
FleetDiagnosis_errNoRobots=运单期待执行的机器人不存在，期待执行的机器人：{0}
FleetDiagnosis_errNoSuchBinOrLocation=找不到名为 {0} 点位或库位
FleetDiagnosis_errNoToPointName=找不到终点 {0}
i18n_button_abort_label=放弃
i18n_button_manualFinished_label=人工完成
i18n_button_retry_label=重试
i18n_entity.AgentUser.fields.appId.label=appId
i18n_entity.AgentUser.fields.appKey.label=appKey
i18n_entity.AgentUser.fields.btDisabled.label=停用
i18n_entity.AgentUser.fields.createdBy.label=创建人
i18n_entity.AgentUser.fields.createdOn.label=创建时间
i18n_entity.AgentUser.fields.id.label=ID
i18n_entity.AgentUser.fields.modifiedBy.label=最后修改人
i18n_entity.AgentUser.fields.modifiedOn.label=最后修改时间
i18n_entity.AgentUser.fields.remark.label=备注
i18n_entity.AgentUser.fields.version.label=修订版本
i18n_entity.AgentUser.group=Core
i18n_entity.AgentUser.label=代理账户
i18n_entity.ApiCallTrace.fields.apiType.inlineOptionBill.items.HTTP.label=HTTP
i18n_entity.ApiCallTrace.fields.apiType.label=接口类型
i18n_entity.ApiCallTrace.fields.costTime.label=耗时
i18n_entity.ApiCallTrace.fields.createdBy.label=创建人
i18n_entity.ApiCallTrace.fields.createdOn.label=创建时间
i18n_entity.ApiCallTrace.fields.httpMethod.inlineOptionBill.items.DELETE.label=DELETE
i18n_entity.ApiCallTrace.fields.httpMethod.inlineOptionBill.items.GET.label=GET
i18n_entity.ApiCallTrace.fields.httpMethod.inlineOptionBill.items.POST.label=POST
i18n_entity.ApiCallTrace.fields.httpMethod.inlineOptionBill.items.PUT.label=PUT
i18n_entity.ApiCallTrace.fields.httpMethod.label=HTTP 方法
i18n_entity.ApiCallTrace.fields.httpPath.label=HTTP 路径
i18n_entity.ApiCallTrace.fields.httpUrl.label=请求 URL
i18n_entity.ApiCallTrace.fields.id.label=ID
i18n_entity.ApiCallTrace.fields.modifiedBy.label=最后修改人
i18n_entity.ApiCallTrace.fields.modifiedOn.label=修改时间
i18n_entity.ApiCallTrace.fields.reqBody.label=请求正文
i18n_entity.ApiCallTrace.fields.reqBodyOn.label=要求记录请求正文
i18n_entity.ApiCallTrace.fields.reqEndOn.label=发送响应时间
i18n_entity.ApiCallTrace.fields.reqIp.label=请求 IP
i18n_entity.ApiCallTrace.fields.reqStartOn.label=收到请求时间
i18n_entity.ApiCallTrace.fields.reqUser.label=请求用户
i18n_entity.ApiCallTrace.fields.resBody.label=响应正文
i18n_entity.ApiCallTrace.fields.resBodyOn.label=要求记录响应正文
i18n_entity.ApiCallTrace.fields.resCode.label=响应代码
i18n_entity.ApiCallTrace.fields.version.label=修订版本
i18n_entity.ApiCallTrace.group=Core
i18n_entity.ApiCallTrace.label=接口调用记录
i18n_entity.ApiCallTrace.pagesButtons.ListMain.buttons[0].label=删除
i18n_entity.ApiCallTrace.pagesButtons.ListMain.buttons[1].label=导出
i18n_entity.ApiCallTrace.pagesButtons.ListMain.buttons[2].label=配置
i18n_entity.BgTaskRecord.fields.args.label=输入参数
i18n_entity.BgTaskRecord.fields.createdBy.label=创建人
i18n_entity.BgTaskRecord.fields.createdOn.label=创建时间
i18n_entity.BgTaskRecord.fields.fault.label=任务失败
i18n_entity.BgTaskRecord.fields.faultMsg.label=失败原因
i18n_entity.BgTaskRecord.fields.id.label=ID
i18n_entity.BgTaskRecord.fields.modifiedBy.label=最后修改人
i18n_entity.BgTaskRecord.fields.modifiedOn.label=最后修改时间
i18n_entity.BgTaskRecord.fields.name.label=后台任务名
i18n_entity.BgTaskRecord.fields.paused.label=已暂停
i18n_entity.BgTaskRecord.fields.version.label=修订版本
i18n_entity.BgTaskRecord.group=Core
i18n_entity.BgTaskRecord.label=后台任务记录
i18n_entity.BgTaskRecord.pagesButtons.ListMain.buttons[0].label=导出
i18n_entity.BgTaskRecord.pagesButtons.ListMain.buttons[1].label=继续
i18n_entity.BgTaskRecord.pagesButtons.ListMain.buttons[2].label=暂停
i18n_entity.BgTaskRecord.pagesButtons.ListMain.buttons[3].label=故障重试
i18n_entity.BgTaskRecord.pagesButtons.ListMain.buttons[4].label=取消
i18n_entity.BgTaskStepRecord.fields.createdBy.label=创建人
i18n_entity.BgTaskStepRecord.fields.createdOn.label=创建时间
i18n_entity.BgTaskStepRecord.fields.id.label=ID
i18n_entity.BgTaskStepRecord.fields.modifiedBy.label=最后修改人
i18n_entity.BgTaskStepRecord.fields.modifiedOn.label=最后修改时间
i18n_entity.BgTaskStepRecord.fields.output.label=执行结果
i18n_entity.BgTaskStepRecord.fields.taskId.label=后台任务 ID
i18n_entity.BgTaskStepRecord.fields.version.label=修订版本
i18n_entity.BgTaskStepRecord.group=Core
i18n_entity.BgTaskStepRecord.label=后台任务块记录
i18n_entity.BgTaskStepRecord.pagesButtons.ListMain.buttons[0].label=导出
i18n_entity.CallEmptyContainerOrder.fields.createdBy.label=创建人
i18n_entity.CallEmptyContainerOrder.fields.createdOn.label=创建时间
i18n_entity.CallEmptyContainerOrder.fields.district.label=库区
i18n_entity.CallEmptyContainerOrder.fields.id.label=单号
i18n_entity.CallEmptyContainerOrder.fields.modifiedBy.label=最后修改人
i18n_entity.CallEmptyContainerOrder.fields.modifiedOn.label=最后修改时间
i18n_entity.CallEmptyContainerOrder.fields.num.label=数量
i18n_entity.CallEmptyContainerOrder.fields.version.label=修订版本
i18n_entity.CallEmptyContainerOrder.group=Warehouse
i18n_entity.CallEmptyContainerOrder.label=叫空容器
i18n_entity.CallEmptyContainerOrder.listCard.district.prefix=库区
i18n_entity.CallEmptyContainerOrder.listCard.num.prefix=数量
i18n_entity.CallEmptyContainerOrder.pagesButtons.Create.buttons[0].label=提交
i18n_entity.ContainerTransportOrder.fields.atPort.label=在库口
i18n_entity.ContainerTransportOrder.fields.container.label=容器
i18n_entity.ContainerTransportOrder.fields.createdBy.label=创建人
i18n_entity.ContainerTransportOrder.fields.createdOn.label=创建时间
i18n_entity.ContainerTransportOrder.fields.doneOn.label=完成时间
i18n_entity.ContainerTransportOrder.fields.errMsg.label=错误原因
i18n_entity.ContainerTransportOrder.fields.expectedRobot.label=指定机器人
i18n_entity.ContainerTransportOrder.fields.falconTaskDefId.label=猎鹰任务模版 ID
i18n_entity.ContainerTransportOrder.fields.falconTaskDefLabel.label=猎鹰任务模版名
i18n_entity.ContainerTransportOrder.fields.falconTaskId.label=猎鹰任务编号
i18n_entity.ContainerTransportOrder.fields.fromBin.label=起点库位
i18n_entity.ContainerTransportOrder.fields.fromChannel.label=起点巷道
i18n_entity.ContainerTransportOrder.fields.id.label=ID
i18n_entity.ContainerTransportOrder.fields.kind.label=类型
i18n_entity.ContainerTransportOrder.fields.loaded.label=已取货
i18n_entity.ContainerTransportOrder.fields.modifiedBy.label=最后修改人
i18n_entity.ContainerTransportOrder.fields.modifiedOn.label=最后修改时间
i18n_entity.ContainerTransportOrder.fields.postProcessMark.label=处理标记
i18n_entity.ContainerTransportOrder.fields.priority.label=优先级
i18n_entity.ContainerTransportOrder.fields.remark.label=备注
i18n_entity.ContainerTransportOrder.fields.robotName.label=执行机器人
i18n_entity.ContainerTransportOrder.fields.sourceOrderId.label=关联单据
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Assigned.label=已派车
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Building.label=未提交
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Cancelled.label=取消
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Created.label=已提交
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Done.label=完成
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Failed.label=失败
i18n_entity.ContainerTransportOrder.fields.status.label=状态
i18n_entity.ContainerTransportOrder.fields.toBin.label=终点库位
i18n_entity.ContainerTransportOrder.fields.toChannel.label=终点巷道
i18n_entity.ContainerTransportOrder.fields.unloaded.label=已放货
i18n_entity.ContainerTransportOrder.fields.version.label=修订版本
i18n_entity.ContainerTransportOrder.group=WCS
i18n_entity.ContainerTransportOrder.label=容器搬运单
i18n_entity.ContainerTransportOrder.listCard.container.prefix=容器
i18n_entity.ContainerTransportOrder.listCard.doneOn.prefix=完成
i18n_entity.ContainerTransportOrder.listCard.falconTaskId.prefix=猎鹰任务
i18n_entity.ContainerTransportOrder.listCard.fromBin.suffix=--->
i18n_entity.ContainerTransportOrder.listCard.priority.prefix=优先级
i18n_entity.ContainerTransportOrder.listCard.robotName.prefix=机器人
i18n_entity.ContainerTransportOrder.listCard.sourceOrderId.prefix=关联单据
i18n_entity.ContainerTransportOrder.listStats.items[0].label=失败
i18n_entity.DemoComponent.fields.btLineNo.label=行号
i18n_entity.DemoComponent.fields.btParentId.label=所属订单
i18n_entity.DemoComponent.fields.createdBy.label=创建人
i18n_entity.DemoComponent.fields.createdOn.label=创建时间
i18n_entity.DemoComponent.fields.floatValue.label=floatValue
i18n_entity.DemoComponent.fields.id.label=编号
i18n_entity.DemoComponent.fields.modifiedBy.label=最后修改人
i18n_entity.DemoComponent.fields.modifiedOn.label=最后修改时间
i18n_entity.DemoComponent.fields.referenceField.label=引用
i18n_entity.DemoComponent.fields.ro.label=根组织
i18n_entity.DemoComponent.fields.stringField.label=文本
i18n_entity.DemoComponent.fields.version.label=版本
i18n_entity.DemoComponent.group=Test
i18n_entity.DemoComponent.label=测试组件
i18n_entity.DemoComponentTable.fields.btLineNo.label=行号
i18n_entity.DemoComponentTable.fields.btParentId.label=所属订单
i18n_entity.DemoComponentTable.fields.createdBy.label=创建人
i18n_entity.DemoComponentTable.fields.createdOn.label=创建时间
i18n_entity.DemoComponentTable.fields.dateField.label=dateField
i18n_entity.DemoComponentTable.fields.id.label=编号
i18n_entity.DemoComponentTable.fields.intField.label=intField
i18n_entity.DemoComponentTable.fields.modifiedBy.label=最后修改人
i18n_entity.DemoComponentTable.fields.modifiedOn.label=最后修改时间
i18n_entity.DemoComponentTable.fields.referenceField.label=引用
i18n_entity.DemoComponentTable.fields.ro.label=根组织
i18n_entity.DemoComponentTable.fields.stringField.label=文本
i18n_entity.DemoComponentTable.fields.version.label=版本
i18n_entity.DemoComponentTable.group=Test
i18n_entity.DemoComponentTable.label=测试组件表格
i18n_entity.DemoEntity.fields.booleanField.label=布尔
i18n_entity.DemoEntity.fields.booleanListField.label=布尔多值
i18n_entity.DemoEntity.fields.checkList2Field.inlineOptionBill.items.v1.label=选项 1
i18n_entity.DemoEntity.fields.checkList2Field.inlineOptionBill.items.v2.label=选项 2
i18n_entity.DemoEntity.fields.checkList2Field.inlineOptionBill.items.v3.label=选项 3
i18n_entity.DemoEntity.fields.checkList2Field.inlineOptionBill.items.v4.label=选项 4
i18n_entity.DemoEntity.fields.checkList2Field.label=选项清单（多选）
i18n_entity.DemoEntity.fields.checkListField.inlineOptionBill.items.v1.label=选项 1
i18n_entity.DemoEntity.fields.checkListField.inlineOptionBill.items.v2.label=选项 2
i18n_entity.DemoEntity.fields.checkListField.inlineOptionBill.items.v3.label=选项 3
i18n_entity.DemoEntity.fields.checkListField.inlineOptionBill.items.v4.label=选项 4
i18n_entity.DemoEntity.fields.checkListField.label=选项清单（单选）
i18n_entity.DemoEntity.fields.componentField.label=组件
i18n_entity.DemoEntity.fields.componentListField.label=组件多值
i18n_entity.DemoEntity.fields.componentTableField.label=组件表格
i18n_entity.DemoEntity.fields.createdBy.label=创建人
i18n_entity.DemoEntity.fields.createdOn.label=创建时间
i18n_entity.DemoEntity.fields.dateField.label=日期
i18n_entity.DemoEntity.fields.dateListField.label=日期多值
i18n_entity.DemoEntity.fields.dateTimeField.label=日期时间
i18n_entity.DemoEntity.fields.dateTimeListField.label=日期时间多值
i18n_entity.DemoEntity.fields.fileField.label=文件
i18n_entity.DemoEntity.fields.fileListField.label=文件多值
i18n_entity.DemoEntity.fields.floatField.label=浮点数
i18n_entity.DemoEntity.fields.floatListField.label=浮点数多值
i18n_entity.DemoEntity.fields.id.label=编号
i18n_entity.DemoEntity.fields.imageField.label=图片
i18n_entity.DemoEntity.fields.imageListField.label=图片多值
i18n_entity.DemoEntity.fields.intField.label=整数
i18n_entity.DemoEntity.fields.intListField.label=整数多值
i18n_entity.DemoEntity.fields.modifiedBy.label=最后修改人
i18n_entity.DemoEntity.fields.modifiedOn.label=最后修改时间
i18n_entity.DemoEntity.fields.passwordField.label=密码输入
i18n_entity.DemoEntity.fields.referenceField.label=引用
i18n_entity.DemoEntity.fields.referenceListField.label=引用多值
i18n_entity.DemoEntity.fields.ro.label=根组织
i18n_entity.DemoEntity.fields.selectField.inlineOptionBill.items.v1.label=选项 1
i18n_entity.DemoEntity.fields.selectField.inlineOptionBill.items.v2.label=选项 2
i18n_entity.DemoEntity.fields.selectField.inlineOptionBill.items.v3.label=选项 3
i18n_entity.DemoEntity.fields.selectField.inlineOptionBill.items.v4.label=选项 4
i18n_entity.DemoEntity.fields.selectField.label=选择输入
i18n_entity.DemoEntity.fields.selectListField.inlineOptionBill.items.v1.label=选项 1
i18n_entity.DemoEntity.fields.selectListField.inlineOptionBill.items.v2.label=选项 2
i18n_entity.DemoEntity.fields.selectListField.inlineOptionBill.items.v3.label=选项 3
i18n_entity.DemoEntity.fields.selectListField.inlineOptionBill.items.v4.label=选项 4
i18n_entity.DemoEntity.fields.selectListField.label=选择输入（多值）
i18n_entity.DemoEntity.fields.stringField.label=文本
i18n_entity.DemoEntity.fields.stringListField.label=文本多值
i18n_entity.DemoEntity.fields.textAreaField.label=多行文本输入
i18n_entity.DemoEntity.fields.textAreaListField.label=多行文本输入（多值）
i18n_entity.DemoEntity.fields.version.label=版本
i18n_entity.DemoEntity.group=Test
i18n_entity.DemoEntity.label=测试实体
i18n_entity.DemoEntity2.fields.booleanField.label=布尔
i18n_entity.DemoEntity2.fields.booleanListField.label=布尔多值
i18n_entity.DemoEntity2.fields.checkList2Field.inlineOptionBill.items.v1.label=选项 1
i18n_entity.DemoEntity2.fields.checkList2Field.inlineOptionBill.items.v2.label=选项 2
i18n_entity.DemoEntity2.fields.checkList2Field.inlineOptionBill.items.v3.label=选项 3
i18n_entity.DemoEntity2.fields.checkList2Field.inlineOptionBill.items.v4.label=选项 4
i18n_entity.DemoEntity2.fields.checkList2Field.label=选项清单（多选）
i18n_entity.DemoEntity2.fields.checkListField.inlineOptionBill.items.v1.label=选项 1
i18n_entity.DemoEntity2.fields.checkListField.inlineOptionBill.items.v2.label=选项 2
i18n_entity.DemoEntity2.fields.checkListField.inlineOptionBill.items.v3.label=选项 3
i18n_entity.DemoEntity2.fields.checkListField.inlineOptionBill.items.v4.label=选项 4
i18n_entity.DemoEntity2.fields.checkListField.label=选项清单（单选）
i18n_entity.DemoEntity2.fields.componentField.label=组件
i18n_entity.DemoEntity2.fields.componentListField.label=组件多值
i18n_entity.DemoEntity2.fields.componentTableField.label=组件表格
i18n_entity.DemoEntity2.fields.createdBy.label=创建人
i18n_entity.DemoEntity2.fields.createdOn.label=创建时间
i18n_entity.DemoEntity2.fields.dateField.label=日期
i18n_entity.DemoEntity2.fields.dateListField.label=日期多值
i18n_entity.DemoEntity2.fields.dateTimeField.label=日期时间
i18n_entity.DemoEntity2.fields.dateTimeListField.label=日期时间多值
i18n_entity.DemoEntity2.fields.fileField.label=文件
i18n_entity.DemoEntity2.fields.fileListField.label=文件多值
i18n_entity.DemoEntity2.fields.floatField.label=浮点数
i18n_entity.DemoEntity2.fields.floatListField.label=浮点数多值
i18n_entity.DemoEntity2.fields.id.label=编号
i18n_entity.DemoEntity2.fields.imageField.label=图片
i18n_entity.DemoEntity2.fields.imageListField.label=图片多值
i18n_entity.DemoEntity2.fields.intField.label=整数
i18n_entity.DemoEntity2.fields.intListField.label=整数多值
i18n_entity.DemoEntity2.fields.modifiedBy.label=最后修改人
i18n_entity.DemoEntity2.fields.modifiedOn.label=最后修改时间
i18n_entity.DemoEntity2.fields.passwordField.label=密码输入
i18n_entity.DemoEntity2.fields.referenceField.label=引用
i18n_entity.DemoEntity2.fields.referenceListField.label=引用多值
i18n_entity.DemoEntity2.fields.ro.label=根组织
i18n_entity.DemoEntity2.fields.selectField.inlineOptionBill.items.v1.label=选项 1
i18n_entity.DemoEntity2.fields.selectField.inlineOptionBill.items.v2.label=选项 2
i18n_entity.DemoEntity2.fields.selectField.inlineOptionBill.items.v3.label=选项 3
i18n_entity.DemoEntity2.fields.selectField.inlineOptionBill.items.v4.label=选项 4
i18n_entity.DemoEntity2.fields.selectField.label=选择输入
i18n_entity.DemoEntity2.fields.selectListField.inlineOptionBill.items.v1.label=选项 1
i18n_entity.DemoEntity2.fields.selectListField.inlineOptionBill.items.v2.label=选项 2
i18n_entity.DemoEntity2.fields.selectListField.inlineOptionBill.items.v3.label=选项 3
i18n_entity.DemoEntity2.fields.selectListField.inlineOptionBill.items.v4.label=选项 4
i18n_entity.DemoEntity2.fields.selectListField.label=选择输入（多值）
i18n_entity.DemoEntity2.fields.stringField.label=文本
i18n_entity.DemoEntity2.fields.stringListField.label=文本多值
i18n_entity.DemoEntity2.fields.textAreaField.label=多行文本输入
i18n_entity.DemoEntity2.fields.textAreaListField.label=多行文本输入（多值）
i18n_entity.DemoEntity2.fields.version.label=版本
i18n_entity.DemoEntity2.group=Test
i18n_entity.DemoEntity2.label=测试实体2
i18n_entity.Department.fields.createdBy.label=创建人
i18n_entity.Department.fields.createdOn.label=创建时间
i18n_entity.Department.fields.disabled.label=禁用
i18n_entity.Department.fields.id.label=编号
i18n_entity.Department.fields.leafNode.label=叶部门
i18n_entity.Department.fields.level.label=层级
i18n_entity.Department.fields.modifiedBy.label=最后修改人
i18n_entity.Department.fields.modifiedOn.label=最后修改时间
i18n_entity.Department.fields.name.label=名称
i18n_entity.Department.fields.owner.label=负责人
i18n_entity.Department.fields.parentNode.label=上级部门
i18n_entity.Department.fields.ro.label=根组织
i18n_entity.Department.fields.rootNode.label=顶级部门
i18n_entity.Department.fields.version.label=版本
i18n_entity.Department.group=User
i18n_entity.Department.label=部门
i18n_entity.Department.listCard.owner.prefix=负责人
i18n_entity.DirectRobotOrder.fields.createdBy.label=创建人
i18n_entity.DirectRobotOrder.fields.createdOn.label=创建时间
i18n_entity.DirectRobotOrder.fields.description.label=描述
i18n_entity.DirectRobotOrder.fields.doneOn.label=完成时间
i18n_entity.DirectRobotOrder.fields.errMsg.label=错误原因
i18n_entity.DirectRobotOrder.fields.id.label=ID
i18n_entity.DirectRobotOrder.fields.modifiedBy.label=最后修改人
i18n_entity.DirectRobotOrder.fields.modifiedOn.label=修改时间
i18n_entity.DirectRobotOrder.fields.moves.label=动作
i18n_entity.DirectRobotOrder.fields.robotName.label=机器人名
i18n_entity.DirectRobotOrder.fields.seer3066.label=指定路径导航
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Cancelled.label=已取消
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Created.label=新建
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Done.label=已完成
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Failed.label=失败
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.ManualDone.label=手工完成
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Sent.label=已下发
i18n_entity.DirectRobotOrder.fields.status.label=状态
i18n_entity.DirectRobotOrder.fields.status.listBatchButtons.buttons[0].label=取消
i18n_entity.DirectRobotOrder.fields.status.listBatchButtons.buttons[1].label=手工完成
i18n_entity.DirectRobotOrder.fields.taskId.label=所属任务
i18n_entity.DirectRobotOrder.fields.version.label=修订版本
i18n_entity.DirectRobotOrder.group=WCS
i18n_entity.DirectRobotOrder.label=直接运单
i18n_entity.DirectRobotOrder.listCard.createdOn.prefix=创建
i18n_entity.DirectRobotOrder.listCard.description.prefix=描述
i18n_entity.DirectRobotOrder.listCard.robotName.prefix=机器人
i18n_entity.DirectRobotOrder.listCard.taskId.prefix=所属任务
i18n_entity.DirectRobotOrder.listStats.items[0].label=新建
i18n_entity.DirectRobotOrder.listStats.items[1].label=下发
i18n_entity.DirectRobotOrder.listStats.items[2].label=失败
i18n_entity.EmptyContainerStoreOrder.fields.container.label=容器
i18n_entity.EmptyContainerStoreOrder.fields.containerType.label=容器类型
i18n_entity.EmptyContainerStoreOrder.fields.createdBy.label=创建人
i18n_entity.EmptyContainerStoreOrder.fields.createdOn.label=创建时间
i18n_entity.EmptyContainerStoreOrder.fields.id.label=ID
i18n_entity.EmptyContainerStoreOrder.fields.modifiedBy.label=最后修改人
i18n_entity.EmptyContainerStoreOrder.fields.modifiedOn.label=最后修改时间
i18n_entity.EmptyContainerStoreOrder.fields.storeDistrict.label=存储区
i18n_entity.EmptyContainerStoreOrder.fields.version.label=修订版本
i18n_entity.EmptyContainerStoreOrder.group=Warehouse
i18n_entity.EmptyContainerStoreOrder.label=新容器上架单
i18n_entity.EmptyContainerStoreOrder.listCard.container.prefix=容器
i18n_entity.EmptyContainerStoreOrder.listCard.storeDistrict.prefix=存储区
i18n_entity.EmptyContainerStoreOrder.pagesButtons.ListMain.buttons[0].label=新建
i18n_entity.EmptyContainerStoreOrder.pagesButtons.ListMain.buttons[1].label=删除
i18n_entity.EmptyContainerStoreOrder.pagesButtons.ListMain.buttons[2].label=已放空箱
i18n_entity.EntityChangedRecord.fields.changeType.label=修改类型
i18n_entity.EntityChangedRecord.fields.createdBy.label=创建人
i18n_entity.EntityChangedRecord.fields.createdOn.label=创建时间
i18n_entity.EntityChangedRecord.fields.entityFields.label=字段列表
i18n_entity.EntityChangedRecord.fields.entityId.label=实体 ID
i18n_entity.EntityChangedRecord.fields.entityName.label=实体名
i18n_entity.EntityChangedRecord.fields.id.label=ID
i18n_entity.EntityChangedRecord.fields.modifiedBy.label=最后修改人
i18n_entity.EntityChangedRecord.fields.modifiedOn.label=最后修改时间
i18n_entity.EntityChangedRecord.fields.version.label=修订版本
i18n_entity.EntityChangedRecord.group=Core
i18n_entity.EntityChangedRecord.label=实体修改记录
i18n_entity.EntityComment.fields.content.label=内容
i18n_entity.EntityComment.fields.createdBy.label=创建人
i18n_entity.EntityComment.fields.createdOn.label=创建时间
i18n_entity.EntityComment.fields.entityId.label=entityId
i18n_entity.EntityComment.fields.entityName.label=实体
i18n_entity.EntityComment.fields.id.label=编号
i18n_entity.EntityComment.fields.modifiedBy.label=最后修改人
i18n_entity.EntityComment.fields.modifiedOn.label=最后修改时间
i18n_entity.EntityComment.fields.ro.label=企业
i18n_entity.EntityComment.fields.version.label=版本
i18n_entity.EntityComment.group=Core
i18n_entity.EntityComment.label=实体评论
i18n_entity.EntitySyncRecord.fields.bzType.label=业务类型
i18n_entity.EntitySyncRecord.fields.cost.label=耗时（毫秒）
i18n_entity.EntitySyncRecord.fields.createdBy.label=创建人
i18n_entity.EntitySyncRecord.fields.createdCount.label=新增实体数量
i18n_entity.EntitySyncRecord.fields.createdOn.label=创建时间
i18n_entity.EntitySyncRecord.fields.deletedCount.label=删除实体数量
i18n_entity.EntitySyncRecord.fields.entityName.label=实体名
i18n_entity.EntitySyncRecord.fields.faileReason.label=失败原因
i18n_entity.EntitySyncRecord.fields.id.label=ID
i18n_entity.EntitySyncRecord.fields.modifiedBy.label=最后修改人
i18n_entity.EntitySyncRecord.fields.modifiedOn.label=最后修改时间
i18n_entity.EntitySyncRecord.fields.oldCount.label=同步前实体数量
i18n_entity.EntitySyncRecord.fields.ro.label=企业
i18n_entity.EntitySyncRecord.fields.success.label=成功
i18n_entity.EntitySyncRecord.fields.syncCount.label=同步实体数量
i18n_entity.EntitySyncRecord.fields.syncOn.label=同步时间
i18n_entity.EntitySyncRecord.fields.txId.label=事务ID
i18n_entity.EntitySyncRecord.fields.updatedCount.label=更新实体数量
i18n_entity.EntitySyncRecord.fields.version.label=版本
i18n_entity.EntitySyncRecord.group=Core
i18n_entity.EntitySyncRecord.label=实体同步记录
i18n_entity.ExternalCallRecord.fields.createdBy.label=创建人
i18n_entity.ExternalCallRecord.fields.createdOn.label=创建时间
i18n_entity.ExternalCallRecord.fields.doneOn.label=完成时间
i18n_entity.ExternalCallRecord.fields.failedNum.label=失败次数
i18n_entity.ExternalCallRecord.fields.failedReason.label=失败原因
i18n_entity.ExternalCallRecord.fields.id.label=ID
i18n_entity.ExternalCallRecord.fields.modifiedBy.label=最后修改人
i18n_entity.ExternalCallRecord.fields.modifiedOn.label=最后修改时间
i18n_entity.ExternalCallRecord.fields.okChecker.label=成功检查方法
i18n_entity.ExternalCallRecord.fields.options.label=选项
i18n_entity.ExternalCallRecord.fields.req.label=请求详情
i18n_entity.ExternalCallRecord.fields.resBody.label=响应正文
i18n_entity.ExternalCallRecord.fields.resCode.label=响应码
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Aborted.label=已终止
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Cancelled.label=已取消
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Done.label=成功
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Failed.label=失败
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Init.label=开始
i18n_entity.ExternalCallRecord.fields.status.label=状态
i18n_entity.ExternalCallRecord.fields.url.label=URL
i18n_entity.ExternalCallRecord.fields.version.label=修订版本
i18n_entity.ExternalCallRecord.group=Core
i18n_entity.ExternalCallRecord.label=第三方调用记录
i18n_entity.ExternalCallTrace.fields.createdBy.label=创建人
i18n_entity.ExternalCallTrace.fields.createdOn.label=创建时间
i18n_entity.ExternalCallTrace.fields.id.label=ID
i18n_entity.ExternalCallTrace.fields.ioError.label=通讯报错
i18n_entity.ExternalCallTrace.fields.ioError.view.trueText=IO 报错
i18n_entity.ExternalCallTrace.fields.ioErrorMsg.label=通讯报错原因
i18n_entity.ExternalCallTrace.fields.method.label=HTTP 方法
i18n_entity.ExternalCallTrace.fields.modifiedBy.label=最后修改人
i18n_entity.ExternalCallTrace.fields.modifiedOn.label=最后修改时间
i18n_entity.ExternalCallTrace.fields.reqBody.label=请求正文
i18n_entity.ExternalCallTrace.fields.reqOn.label=请求时间
i18n_entity.ExternalCallTrace.fields.resBody.label=响应正文
i18n_entity.ExternalCallTrace.fields.resCode.label=响应码
i18n_entity.ExternalCallTrace.fields.resOn.label=响应时间
i18n_entity.ExternalCallTrace.fields.url.label=URL
i18n_entity.ExternalCallTrace.fields.version.label=修订版本
i18n_entity.ExternalCallTrace.group=Core
i18n_entity.ExternalCallTrace.label=HTTP 客户端日志
i18n_entity.FailureRecord.fields.createdBy.label=创建人
i18n_entity.FailureRecord.fields.createdOn.label=创建时间
i18n_entity.FailureRecord.fields.desc.label=描述
i18n_entity.FailureRecord.fields.firstOn.label=首次发生时间
i18n_entity.FailureRecord.fields.id.label=ID
i18n_entity.FailureRecord.fields.kind.label=类别
i18n_entity.FailureRecord.fields.lastOn.label=最新发生时间
i18n_entity.FailureRecord.fields.level.inlineOptionBill.items.Error.label=故障
i18n_entity.FailureRecord.fields.level.inlineOptionBill.items.Fatal.label=严重
i18n_entity.FailureRecord.fields.level.inlineOptionBill.items.Warning.label=警告
i18n_entity.FailureRecord.fields.level.label=级别
i18n_entity.FailureRecord.fields.modifiedBy.label=最后修改人
i18n_entity.FailureRecord.fields.modifiedOn.label=最后修改时间
i18n_entity.FailureRecord.fields.num.label=次数
i18n_entity.FailureRecord.fields.part.label=对象
i18n_entity.FailureRecord.fields.source.label=来源
i18n_entity.FailureRecord.fields.subKind.label=子类别
i18n_entity.FailureRecord.fields.version.label=修订版本
i18n_entity.FailureRecord.group=Core
i18n_entity.FailureRecord.label=故障记录
i18n_entity.FalconBlockChildId.fields.blockId.label=blockId
i18n_entity.FalconBlockChildId.fields.childId.label=childId
i18n_entity.FalconBlockChildId.fields.contextKey.label=contextKey
i18n_entity.FalconBlockChildId.fields.createdBy.label=创建人
i18n_entity.FalconBlockChildId.fields.createdOn.label=创建时间
i18n_entity.FalconBlockChildId.fields.id.label=ID
i18n_entity.FalconBlockChildId.fields.index.label=index
i18n_entity.FalconBlockChildId.fields.modifiedBy.label=最后修改人
i18n_entity.FalconBlockChildId.fields.modifiedOn.label=修改时间
i18n_entity.FalconBlockChildId.fields.taskId.label=taskId
i18n_entity.FalconBlockChildId.fields.version.label=修订版本
i18n_entity.FalconBlockChildId.group=Falcon
i18n_entity.FalconBlockChildId.label=猎鹰任务块子块 ID
i18n_entity.FalconBlockChildId.listCard.contextKey.prefix=contextKey
i18n_entity.FalconBlockChildId.listCard.createdOn.prefix=创建
i18n_entity.FalconBlockChildId.listCard.taskId.prefix=taskId
i18n_entity.FalconBlockRecord.fields.blockConfigId.label=blockConfigId
i18n_entity.FalconBlockRecord.fields.createdBy.label=创建人
i18n_entity.FalconBlockRecord.fields.createdOn.label=创建时间
i18n_entity.FalconBlockRecord.fields.endedOn.label=endedOn
i18n_entity.FalconBlockRecord.fields.endedReason.label=endedReason
i18n_entity.FalconBlockRecord.fields.failureNum.label=失败次数
i18n_entity.FalconBlockRecord.fields.id.label=ID
i18n_entity.FalconBlockRecord.fields.inputParams.label=inputParams
i18n_entity.FalconBlockRecord.fields.internalVariables.label=internalVariables
i18n_entity.FalconBlockRecord.fields.modifiedBy.label=最后修改人
i18n_entity.FalconBlockRecord.fields.modifiedOn.label=修改时间
i18n_entity.FalconBlockRecord.fields.outputParams.label=outputParams
i18n_entity.FalconBlockRecord.fields.startedOn.label=startedOn
i18n_entity.FalconBlockRecord.fields.status.label=status
i18n_entity.FalconBlockRecord.fields.taskId.label=taskId
i18n_entity.FalconBlockRecord.fields.version.label=修订版本
i18n_entity.FalconBlockRecord.group=Falcon
i18n_entity.FalconBlockRecord.label=猎鹰任务块记录
i18n_entity.FalconBlockRecord.listCard.blockConfigId.prefix=blockConfigId
i18n_entity.FalconBlockRecord.listCard.taskId.prefix=taskId
i18n_entity.FalconLog.fields.blockId.label=关联组件
i18n_entity.FalconLog.fields.createdBy.label=创建人
i18n_entity.FalconLog.fields.createdOn.label=创建时间
i18n_entity.FalconLog.fields.id.label=ID
i18n_entity.FalconLog.fields.level.inlineOptionBill.items.Debug.label=一般
i18n_entity.FalconLog.fields.level.inlineOptionBill.items.Error.label=错误
i18n_entity.FalconLog.fields.level.inlineOptionBill.items.Info.label=重要
i18n_entity.FalconLog.fields.level.label=级别
i18n_entity.FalconLog.fields.message.label=消息
i18n_entity.FalconLog.fields.modifiedBy.label=最后修改人
i18n_entity.FalconLog.fields.modifiedOn.label=修改时间
i18n_entity.FalconLog.fields.taskId.label=关联任务
i18n_entity.FalconLog.fields.version.label=修订版本
i18n_entity.FalconLog.group=Falcon
i18n_entity.FalconLog.label=猎鹰日志
i18n_entity.FalconLog.listCard.blockId.prefix=关联组件
i18n_entity.FalconLog.listCard.createdOn.prefix=创建
i18n_entity.FalconRelatedObject.fields.createdBy.label=创建人
i18n_entity.FalconRelatedObject.fields.createdOn.label=创建时间
i18n_entity.FalconRelatedObject.fields.id.label=ID
i18n_entity.FalconRelatedObject.fields.modifiedBy.label=最后修改人
i18n_entity.FalconRelatedObject.fields.modifiedOn.label=最后修改时间
i18n_entity.FalconRelatedObject.fields.objectArgs.label=objectArgs
i18n_entity.FalconRelatedObject.fields.objectId.label=objectId
i18n_entity.FalconRelatedObject.fields.objectType.label=objectType
i18n_entity.FalconRelatedObject.fields.taskId.label=taskId
i18n_entity.FalconRelatedObject.fields.version.label=修订版本
i18n_entity.FalconRelatedObject.group=Falcon
i18n_entity.FalconRelatedObject.label=猎鹰任务相关对象
i18n_entity.FalconRelatedObject.listCard.objectArgs.prefix=objectArgs
i18n_entity.FalconRelatedObject.listCard.objectId.prefix=objectId
i18n_entity.FalconTaskRecord.fields.actualRobots.label=执行机器人
i18n_entity.FalconTaskRecord.fields.createdBy.label=创建人
i18n_entity.FalconTaskRecord.fields.createdOn.label=创建时间
i18n_entity.FalconTaskRecord.fields.defId.label=定义 ID
i18n_entity.FalconTaskRecord.fields.defLabel.label=任务模版
i18n_entity.FalconTaskRecord.fields.defVersion.label=模版版本
i18n_entity.FalconTaskRecord.fields.endedOn.label=结束时间
i18n_entity.FalconTaskRecord.fields.endedReason.label=错误原因
i18n_entity.FalconTaskRecord.fields.failureNum.label=故障次数
i18n_entity.FalconTaskRecord.fields.id.label=ID
i18n_entity.FalconTaskRecord.fields.inputParams.label=输入参数
i18n_entity.FalconTaskRecord.fields.modifiedBy.label=最后修改人
i18n_entity.FalconTaskRecord.fields.modifiedOn.label=修改时间
i18n_entity.FalconTaskRecord.fields.paused.label=已暂停
i18n_entity.FalconTaskRecord.fields.ro.label=RO
i18n_entity.FalconTaskRecord.fields.rootBlockStateId.label=根块ID
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.100.label=已创建
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.120.label=已开始
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.140.label=故障
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.160.label=已完成
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.180.label=已取消
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.190.label=已放弃
i18n_entity.FalconTaskRecord.fields.status.label=状态
i18n_entity.FalconTaskRecord.fields.subTask.label=子任务
i18n_entity.FalconTaskRecord.fields.topTaskId.label=顶层任务 ID
i18n_entity.FalconTaskRecord.fields.variables.label=任务变量
i18n_entity.FalconTaskRecord.fields.version.label=修订版本
i18n_entity.FalconTaskRecord.group=Falcon
i18n_entity.FalconTaskRecord.label=猎鹰任务记录
i18n_entity.FalconTaskRecord.listCard.createdOn.prefix=创建
i18n_entity.FalconTaskRecord.listCard.defLabel.prefix=模板
i18n_entity.FalconTaskRecord.listCard.defVersion.prefix=(
i18n_entity.FalconTaskRecord.listCard.defVersion.suffix=)
i18n_entity.FalconTaskRecord.listCard.endedOn.prefix=结束
i18n_entity.FalconTaskRecord.listStats.items[0].label=故障
i18n_entity.FalconTaskRecord.listStats.items[1].label=已暂停
i18n_entity.FalconTaskRecord.listStats.items[2].label=今日新增
i18n_entity.FalconTaskRecord.listStats.items[3].label=今日失败取消
i18n_entity.FalconTaskRecord.listStats.items[4].label=本周新增
i18n_entity.FalconTaskRecord.listStats.items[5].label=本周失败取消
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[0].label=删除
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[1].label=导出
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[2].label=暂停执行
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[3].label=继续执行
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[4].label=故障重试
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[5].label=取消执行
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[6].label=删除
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[7].label=模版
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[8].label=全局控制
i18n_entity.FalconTaskResource.fields.args.label=参数
i18n_entity.FalconTaskResource.fields.createdBy.label=创建人
i18n_entity.FalconTaskResource.fields.createdOn.label=创建时间
i18n_entity.FalconTaskResource.fields.id.label=ID
i18n_entity.FalconTaskResource.fields.modifiedBy.label=最后修改人
i18n_entity.FalconTaskResource.fields.modifiedOn.label=最后修改时间
i18n_entity.FalconTaskResource.fields.resId.label=资源 ID
i18n_entity.FalconTaskResource.fields.resType.label=资源类型
i18n_entity.FalconTaskResource.fields.taskId.label=任务编号
i18n_entity.FalconTaskResource.fields.version.label=修订版本
i18n_entity.FalconTaskResource.group=Falcon
i18n_entity.FalconTaskResource.label=猎鹰任务资源
i18n_entity.FalconTaskResource.listCard.resId.prefix=资源 ID
i18n_entity.FbAssemblyLine.fields.building.label=楼
i18n_entity.FbAssemblyLine.fields.buildingLayer.label=楼层
i18n_entity.FbAssemblyLine.fields.createdBy.label=创建人
i18n_entity.FbAssemblyLine.fields.createdOn.label=创建时间
i18n_entity.FbAssemblyLine.fields.disabled.label=禁用
i18n_entity.FbAssemblyLine.fields.id.label=单号
i18n_entity.FbAssemblyLine.fields.modifiedBy.label=最后修改人
i18n_entity.FbAssemblyLine.fields.modifiedOn.label=最后修改时间
i18n_entity.FbAssemblyLine.fields.name.label=名称
i18n_entity.FbAssemblyLine.fields.remark.label=备注
i18n_entity.FbAssemblyLine.fields.ro.label=根组织
i18n_entity.FbAssemblyLine.fields.version.label=版本
i18n_entity.FbAssemblyLine.group=MainData
i18n_entity.FbAssemblyLine.label=产线
i18n_entity.FbBin.fields.assemblyLine.label=所属产线
i18n_entity.FbBin.fields.boxDirection.label=货叉方向
i18n_entity.FbBin.fields.boxHeight.label=货叉抬升高度
i18n_entity.FbBin.fields.btDisabled.label=停用
i18n_entity.FbBin.fields.channel.label=所在巷道
i18n_entity.FbBin.fields.column.label=列
i18n_entity.FbBin.fields.container.label=库位上容器
i18n_entity.FbBin.fields.createdBy.label=创建人
i18n_entity.FbBin.fields.createdOn.label=创建时间
i18n_entity.FbBin.fields.depth.label=深
i18n_entity.FbBin.fields.district.label=所属库区
i18n_entity.FbBin.fields.id.label=库位编号
i18n_entity.FbBin.fields.layer.label=层
i18n_entity.FbBin.fields.loadStatus.inlineOptionBill.items.Empty.label=未占用
i18n_entity.FbBin.fields.loadStatus.inlineOptionBill.items.Occupied.label=已占用
i18n_entity.FbBin.fields.loadStatus.inlineOptionBill.items.ToLoad.label=即将运来
i18n_entity.FbBin.fields.loadStatus.inlineOptionBill.items.ToUnload.label=即将运走
i18n_entity.FbBin.fields.loadStatus.label=占用状态
i18n_entity.FbBin.fields.locked.label=锁定
i18n_entity.FbBin.fields.locked.listBatchButtons.buttons[0].label=解锁
i18n_entity.FbBin.fields.lockedBy.label=锁定原因
i18n_entity.FbBin.fields.materialCategoryLabel.label=存储物料分类名称
i18n_entity.FbBin.fields.modifiedBy.label=最后修改人
i18n_entity.FbBin.fields.modifiedOn.label=最后修改时间
i18n_entity.FbBin.fields.occupied.label=有货
i18n_entity.FbBin.fields.occupied.view.trueText=有货
i18n_entity.FbBin.fields.pendingContainer.label=要被运来的容器
i18n_entity.FbBin.fields.purpose.label=用途
i18n_entity.FbBin.fields.rack.label=所属货架
i18n_entity.FbBin.fields.remark.label=备注
i18n_entity.FbBin.fields.ro.label=根组织
i18n_entity.FbBin.fields.robotDirection.label=机器人方向
i18n_entity.FbBin.fields.robotX.label=机器人位置 X
i18n_entity.FbBin.fields.robotY.label=机器人位置 Y
i18n_entity.FbBin.fields.row.label=排
i18n_entity.FbBin.fields.version.label=版本
i18n_entity.FbBin.fields.warehouse.label=所属仓库
i18n_entity.FbBin.fields.workSite.label=所属工位
i18n_entity.FbBin.group=MainData
i18n_entity.FbBin.label=库位
i18n_entity.FbBin.listCard.container.prefix=库位上容器
i18n_entity.FbBin.listCard.district.prefix=所属库区
i18n_entity.FbBin.listStats.items[0].label=处理中（锁定）
i18n_entity.FbBin.listStats.items[1].label=有货
i18n_entity.FbBin.listStats.items[2].label=即将运来
i18n_entity.FbBin.listStats.items[3].label=即将运走
i18n_entity.FbBin.pagesButtons.ListMain.buttons[0].label=新增
i18n_entity.FbBin.pagesButtons.ListMain.buttons[1].label=批量编辑
i18n_entity.FbBin.pagesButtons.ListMain.buttons[2].label=删除
i18n_entity.FbBin.pagesButtons.ListMain.buttons[3].label=导出
i18n_entity.FbBin.pagesButtons.ListMain.buttons[4].label=导入
i18n_entity.FbBin.pagesButtons.ListMain.buttons[5].label=批量创建库位
i18n_entity.FbBinInv.fields.amount.label=金额
i18n_entity.FbBinInv.fields.assemblyLine.label=所属产线
i18n_entity.FbBinInv.fields.bin.label=库位
i18n_entity.FbBinInv.fields.binDisabled.label=库位停用
i18n_entity.FbBinInv.fields.binFilled.label=库位是否有货
i18n_entity.FbBinInv.fields.btBzDesc.label=业务描述
i18n_entity.FbBinInv.fields.btBzMark.label=业务标记
i18n_entity.FbBinInv.fields.btMaterial.label=物料
i18n_entity.FbBinInv.fields.btMaterialCategory.label=物料分类编号
i18n_entity.FbBinInv.fields.btMaterialCategoryName.label=物料分类名称
i18n_entity.FbBinInv.fields.btMaterialId.label=物料编号
i18n_entity.FbBinInv.fields.btMaterialModel.label=物料型号
i18n_entity.FbBinInv.fields.btMaterialName.label=物料名称
i18n_entity.FbBinInv.fields.btMaterialSpec.label=物料规格
i18n_entity.FbBinInv.fields.btMaterialTopCategory.label=物料一级分类编号
i18n_entity.FbBinInv.fields.btMaterialTopCategoryName.label=物料一级分类名称
i18n_entity.FbBinInv.fields.btPrepare.label=btPrepare
i18n_entity.FbBinInv.fields.channel.label=巷道
i18n_entity.FbBinInv.fields.column.label=列
i18n_entity.FbBinInv.fields.containerDisabled.label=容器停用
i18n_entity.FbBinInv.fields.containerFilled.label=容器是否有货
i18n_entity.FbBinInv.fields.containers.label=容器列表
i18n_entity.FbBinInv.fields.createdBy.label=创建人
i18n_entity.FbBinInv.fields.createdOn.label=创建时间
i18n_entity.FbBinInv.fields.depth.label=深
i18n_entity.FbBinInv.fields.district.label=库区
i18n_entity.FbBinInv.fields.id.label=ID
i18n_entity.FbBinInv.fields.layer.label=层
i18n_entity.FbBinInv.fields.lotNo.label=批次号
i18n_entity.FbBinInv.fields.materialCategoryLabel.label=存储物料分类名称
i18n_entity.FbBinInv.fields.materialIds.label=库位上的物料
i18n_entity.FbBinInv.fields.materialNames.label=库位上物料名称列表
i18n_entity.FbBinInv.fields.modifiedBy.label=最后修改人
i18n_entity.FbBinInv.fields.modifiedOn.label=最后修改时间
i18n_entity.FbBinInv.fields.onRobot.label=所在机器人
i18n_entity.FbBinInv.fields.pendingContainer.label=要被运来的容器
i18n_entity.FbBinInv.fields.qty.label=数量
i18n_entity.FbBinInv.fields.rack.label=货架
i18n_entity.FbBinInv.fields.robotBin.label=所在机器人库位
i18n_entity.FbBinInv.fields.row.label=排
i18n_entity.FbBinInv.fields.subNum.label=格数
i18n_entity.FbBinInv.fields.topContainer.label=最外层容器
i18n_entity.FbBinInv.fields.topContainerType.label=最外层容器类型
i18n_entity.FbBinInv.fields.version.label=修订版本
i18n_entity.FbBinInv.fields.warehouse.label=仓库
i18n_entity.FbBinInv.fields.workSite.label=工位
i18n_entity.FbBinInv.group=Core
i18n_entity.FbBinInv.label=库位库存
i18n_entity.FbContainer.fields.bin.label=所在库位
i18n_entity.FbContainer.fields.btDisabled.label=停用
i18n_entity.FbContainer.fields.createdBy.label=创建人
i18n_entity.FbContainer.fields.createdOn.label=创建时间
i18n_entity.FbContainer.fields.district.label=当前库区
i18n_entity.FbContainer.fields.filled.label=有货
i18n_entity.FbContainer.fields.fixedStoreBin.label=指定库位存储
i18n_entity.FbContainer.fields.height.label=容器高度
i18n_entity.FbContainer.fields.id.label=容器编号
i18n_entity.FbContainer.fields.length.label=容器长度
i18n_entity.FbContainer.fields.locked.label=锁定
i18n_entity.FbContainer.fields.locked.listBatchButtons.buttons[0].label=解锁
i18n_entity.FbContainer.fields.maxWeight.label=容器承重
i18n_entity.FbContainer.fields.modifiedBy.label=最后修改人
i18n_entity.FbContainer.fields.modifiedOn.label=最后修改时间
i18n_entity.FbContainer.fields.onRobot.label=所在机器人
i18n_entity.FbContainer.fields.pContainer.label=父容器
i18n_entity.FbContainer.fields.preBin.label=预定存储库位
i18n_entity.FbContainer.fields.qcResult.label=质检结果
i18n_entity.FbContainer.fields.remark.label=备注
i18n_entity.FbContainer.fields.ro.label=根组织
i18n_entity.FbContainer.fields.state.label=状态
i18n_entity.FbContainer.fields.subNum.label=格数
i18n_entity.FbContainer.fields.targetBin.label=要被运往的库位
i18n_entity.FbContainer.fields.taskType.inlineOptionBill.items.Count.label=盘点中
i18n_entity.FbContainer.fields.taskType.inlineOptionBill.items.Pick.label=待分拣
i18n_entity.FbContainer.fields.taskType.inlineOptionBill.items.Put.label=待装箱
i18n_entity.FbContainer.fields.taskType.label=任务类型
i18n_entity.FbContainer.fields.type.label=容器类型
i18n_entity.FbContainer.fields.version.label=版本
i18n_entity.FbContainer.fields.volume.label=容器容积
i18n_entity.FbContainer.fields.warehouse.label=当前仓库
i18n_entity.FbContainer.fields.width.label=容器宽度
i18n_entity.FbContainer.fields.workStatus.inlineOptionBill.items.ToPick.label=待拣货
i18n_entity.FbContainer.fields.workStatus.inlineOptionBill.items.ToPut.label=待装货
i18n_entity.FbContainer.fields.workStatus.label=工作状态
i18n_entity.FbContainer.group=MainData
i18n_entity.FbContainer.label=容器
i18n_entity.FbContainer.listCard.bin.prefix=所在库位
i18n_entity.FbContainer.listCard.type.prefix=类型
i18n_entity.FbContainer.listStats.items[0].label=处理中（锁定）
i18n_entity.FbContainer.listStats.items[1].label=有货
i18n_entity.FbContainer.listStats.items[2].label=没在库位上
i18n_entity.FbContainerType.fields.btDisabled.label=停用
i18n_entity.FbContainerType.fields.createdBy.label=创建人
i18n_entity.FbContainerType.fields.createdOn.label=创建时间
i18n_entity.FbContainerType.fields.id.label=容器类型编码
i18n_entity.FbContainerType.fields.mixedMaterial.label=物料混放
i18n_entity.FbContainerType.fields.modifiedBy.label=最后修改人
i18n_entity.FbContainerType.fields.modifiedOn.label=最后修改时间
i18n_entity.FbContainerType.fields.name.label=容器类型名称
i18n_entity.FbContainerType.fields.remark.label=说明
i18n_entity.FbContainerType.fields.ro.label=根组织
i18n_entity.FbContainerType.fields.storeDistricts.label=存储库区
i18n_entity.FbContainerType.fields.subNum.label=分格数
i18n_entity.FbContainerType.fields.version.label=版本
i18n_entity.FbContainerType.group=MainData
i18n_entity.FbContainerType.label=容器类型
i18n_entity.FbContainerType.listCard.storeDistricts.prefix=存储库区
i18n_entity.FbCountDiffLine.fields.actualQty.label=实际数量
i18n_entity.FbCountDiffLine.fields.bin.label=原存放库位
i18n_entity.FbCountDiffLine.fields.btLineNo.label=行号
i18n_entity.FbCountDiffLine.fields.btMaterial.label=物料
i18n_entity.FbCountDiffLine.fields.btMaterialCategory.label=物料分类编号
i18n_entity.FbCountDiffLine.fields.btMaterialCategoryName.label=物料分类名称
i18n_entity.FbCountDiffLine.fields.btMaterialId.label=物料编号
i18n_entity.FbCountDiffLine.fields.btMaterialImage.label=物料图片
i18n_entity.FbCountDiffLine.fields.btMaterialModel.label=物料型号
i18n_entity.FbCountDiffLine.fields.btMaterialName.label=物料名称
i18n_entity.FbCountDiffLine.fields.btMaterialSpec.label=物料规格
i18n_entity.FbCountDiffLine.fields.btParentId.label=所属订单
i18n_entity.FbCountDiffLine.fields.container.label=容器
i18n_entity.FbCountDiffLine.fields.createdBy.label=创建人
i18n_entity.FbCountDiffLine.fields.createdOn.label=创建时间
i18n_entity.FbCountDiffLine.fields.diffQty.label=差异数量
i18n_entity.FbCountDiffLine.fields.fix.label=执行
i18n_entity.FbCountDiffLine.fields.id.label=ID
i18n_entity.FbCountDiffLine.fields.lotNo.label=批次号
i18n_entity.FbCountDiffLine.fields.modifiedBy.label=最后修改人
i18n_entity.FbCountDiffLine.fields.modifiedOn.label=修改时间
i18n_entity.FbCountDiffLine.fields.qty.label=原数量
i18n_entity.FbCountDiffLine.fields.qualityLevel.label=质量等级
i18n_entity.FbCountDiffLine.fields.remark.label=备注
i18n_entity.FbCountDiffLine.fields.sourceInvLayout.label=关联库存明细
i18n_entity.FbCountDiffLine.fields.subContainerId.label=格号
i18n_entity.FbCountDiffLine.fields.taskId.label=盘点任务
i18n_entity.FbCountDiffLine.fields.version.label=修订版本
i18n_entity.FbCountDiffLine.group=Warehouse
i18n_entity.FbCountDiffLine.label=盘差记录行
i18n_entity.FbCountDiffLine.listCard.btMaterial.prefix=物料
i18n_entity.FbCountDiffLine.listCard.subContainerId.prefix=格号
i18n_entity.FbCountFix.fields.btMaterial.label=物料
i18n_entity.FbCountFix.fields.btMaterialCategory.label=物料分类编号
i18n_entity.FbCountFix.fields.btMaterialCategoryName.label=物料分类名称
i18n_entity.FbCountFix.fields.btMaterialId.label=物料编号
i18n_entity.FbCountFix.fields.btMaterialImage.label=物料图片
i18n_entity.FbCountFix.fields.btMaterialModel.label=物料型号
i18n_entity.FbCountFix.fields.btMaterialName.label=物料名称
i18n_entity.FbCountFix.fields.btMaterialSpec.label=物料规格
i18n_entity.FbCountFix.fields.container.label=容器
i18n_entity.FbCountFix.fields.createdBy.label=创建人
i18n_entity.FbCountFix.fields.createdOn.label=创建时间
i18n_entity.FbCountFix.fields.id.label=ID
i18n_entity.FbCountFix.fields.lotNo.label=批次号
i18n_entity.FbCountFix.fields.modifiedBy.label=最后修改人
i18n_entity.FbCountFix.fields.modifiedOn.label=修改时间
i18n_entity.FbCountFix.fields.qty.label=变更数量
i18n_entity.FbCountFix.fields.qualityLevel.label=质量等级
i18n_entity.FbCountFix.fields.remark.label=备注
i18n_entity.FbCountFix.fields.subContainerId.label=格子号
i18n_entity.FbCountFix.fields.version.label=修订版本
i18n_entity.FbCountFix.group=Warehouse
i18n_entity.FbCountFix.label=库存修正记录
i18n_entity.FbCountFix.listCard.container.prefix=容器
i18n_entity.FbCountFix.listCard.qty.prefix=变更数量
i18n_entity.FbCountFix.listCard.subContainerId.prefix=格号
i18n_entity.FbCountOrder.fields.bins.label=计划盘点库位列表
i18n_entity.FbCountOrder.fields.btOrderState.inlineOptionBill.items.Done.label=完成
i18n_entity.FbCountOrder.fields.btOrderState.inlineOptionBill.items.Init.label=未提交
i18n_entity.FbCountOrder.fields.btOrderState.inlineOptionBill.items.Submitted.label=已提交
i18n_entity.FbCountOrder.fields.btOrderState.label=业务状态
i18n_entity.FbCountOrder.fields.btOrderStateReason.label=状态说明
i18n_entity.FbCountOrder.fields.containers.label=计划盘点容器列表
i18n_entity.FbCountOrder.fields.createdBy.label=创建人
i18n_entity.FbCountOrder.fields.createdOn.label=创建时间
i18n_entity.FbCountOrder.fields.diffLines.label=盘差列表
i18n_entity.FbCountOrder.fields.districts.label=计划盘点库区列表
i18n_entity.FbCountOrder.fields.doneProcessed.label=盘差已处理
i18n_entity.FbCountOrder.fields.id.label=单号
i18n_entity.FbCountOrder.fields.materials.label=计划盘点物料列表
i18n_entity.FbCountOrder.fields.modifiedBy.label=最后修改人
i18n_entity.FbCountOrder.fields.modifiedOn.label=修改时间
i18n_entity.FbCountOrder.fields.remark.label=说明
i18n_entity.FbCountOrder.fields.taskGenerated.label=盘点任务已生成
i18n_entity.FbCountOrder.fields.taskLines.label=任务统计行
i18n_entity.FbCountOrder.fields.version.label=修订版本
i18n_entity.FbCountOrder.group=Warehouse
i18n_entity.FbCountOrder.label=盘点单
i18n_entity.FbCountOrder.states.states.Done.label=完成
i18n_entity.FbCountOrder.states.states.Init.label=未提交
i18n_entity.FbCountOrder.states.states.Init.nextStates.Submitted.buttonLabel=提交
i18n_entity.FbCountOrder.states.states.Submitted.label=已提交
i18n_entity.FbCountOrder.states.states.Submitted.nextStates.Done.buttonLabel=执行盘差
i18n_entity.FbCountTask.fields.bin.label=来源库位
i18n_entity.FbCountTask.fields.btLines.label=单行
i18n_entity.FbCountTask.fields.btOrderState.inlineOptionBill.items.Init.label=待盘点
i18n_entity.FbCountTask.fields.btOrderState.inlineOptionBill.items.Submitted.label=已盘点
i18n_entity.FbCountTask.fields.btOrderState.label=业务状态
i18n_entity.FbCountTask.fields.btOrderStateReason.label=状态说明
i18n_entity.FbCountTask.fields.container.label=盘点容器
i18n_entity.FbCountTask.fields.containerInOrderId.label=容器入库运单号
i18n_entity.FbCountTask.fields.containerOutOrderId.label=容器出库运单号
i18n_entity.FbCountTask.fields.countOrderId.label=所属盘点单
i18n_entity.FbCountTask.fields.createdBy.label=创建人
i18n_entity.FbCountTask.fields.createdOn.label=创建时间
i18n_entity.FbCountTask.fields.id.label=ID
i18n_entity.FbCountTask.fields.modifiedBy.label=最后修改人
i18n_entity.FbCountTask.fields.modifiedOn.label=最后修改时间
i18n_entity.FbCountTask.fields.remark.label=说明
i18n_entity.FbCountTask.fields.submittedPostProcessed.label=提交后处理完成
i18n_entity.FbCountTask.fields.version.label=修订版本
i18n_entity.FbCountTask.group=Warehouse
i18n_entity.FbCountTask.label=盘点任务
i18n_entity.FbCountTask.listCard.bin.prefix=库位
i18n_entity.FbCountTask.listCard.container.prefix=容器
i18n_entity.FbCountTask.listStats.items[0].label=待盘点
i18n_entity.FbCountTask.states.states.Init.label=待盘点
i18n_entity.FbCountTask.states.states.Init.nextStates.Submitted.buttonLabel=提交
i18n_entity.FbCountTask.states.states.Submitted.label=已盘点
i18n_entity.FbCountTaskLine.fields.actualQty.label=实际数量
i18n_entity.FbCountTaskLine.fields.amount.label=金额
i18n_entity.FbCountTaskLine.fields.bin.label=所在库位
i18n_entity.FbCountTaskLine.fields.btLineNo.label=行号
i18n_entity.FbCountTaskLine.fields.btMaterial.label=物料
i18n_entity.FbCountTaskLine.fields.btMaterialCategory.label=物料分类编号
i18n_entity.FbCountTaskLine.fields.btMaterialCategoryName.label=物料分类名称
i18n_entity.FbCountTaskLine.fields.btMaterialId.label=物料编号
i18n_entity.FbCountTaskLine.fields.btMaterialImage.label=物料图片
i18n_entity.FbCountTaskLine.fields.btMaterialModel.label=物料型号
i18n_entity.FbCountTaskLine.fields.btMaterialName.label=物料名称
i18n_entity.FbCountTaskLine.fields.btMaterialSpec.label=物料规格
i18n_entity.FbCountTaskLine.fields.btParentId.label=所属任务
i18n_entity.FbCountTaskLine.fields.createdBy.label=创建人
i18n_entity.FbCountTaskLine.fields.createdOn.label=创建时间
i18n_entity.FbCountTaskLine.fields.district.label=所在库区
i18n_entity.FbCountTaskLine.fields.expDate.label=有效期
i18n_entity.FbCountTaskLine.fields.id.label=ID
i18n_entity.FbCountTaskLine.fields.leafContainer.label=容器
i18n_entity.FbCountTaskLine.fields.lotNo.label=批次号
i18n_entity.FbCountTaskLine.fields.mfgDate.label=生产日期
i18n_entity.FbCountTaskLine.fields.modifiedBy.label=最后修改人
i18n_entity.FbCountTaskLine.fields.modifiedOn.label=最后修改时间
i18n_entity.FbCountTaskLine.fields.owner.label=货主
i18n_entity.FbCountTaskLine.fields.price.label=单价
i18n_entity.FbCountTaskLine.fields.qty.label=数量
i18n_entity.FbCountTaskLine.fields.qualityLevel.label=质量等级
i18n_entity.FbCountTaskLine.fields.sourceInvLayout.label=引用库存明细
i18n_entity.FbCountTaskLine.fields.subContainerId.label=格子
i18n_entity.FbCountTaskLine.fields.topContainer.label=最外层容器
i18n_entity.FbCountTaskLine.fields.unitLabel.label=单位名称
i18n_entity.FbCountTaskLine.fields.vendor.label=供应商
i18n_entity.FbCountTaskLine.fields.version.label=修订版本
i18n_entity.FbCountTaskLine.fields.warehouse.label=所在仓库
i18n_entity.FbCountTaskLine.group=Warehouse
i18n_entity.FbCountTaskLine.label=盘点任务行
i18n_entity.FbCountTaskLine.listCard.actualQty.prefix=实际数量
i18n_entity.FbCountTaskLine.listCard.btMaterialId.prefix=物料编号
i18n_entity.FbCountTaskLine.listCard.lotNo.prefix=批次号
i18n_entity.FbCountTaskLine.listCard.qty.prefix=库存数量
i18n_entity.FbCountTaskStatLine.fields.bin.label=盘点库位
i18n_entity.FbCountTaskStatLine.fields.btLineNo.label=行号
i18n_entity.FbCountTaskStatLine.fields.btParentId.label=所属订单
i18n_entity.FbCountTaskStatLine.fields.container.label=盘点容器
i18n_entity.FbCountTaskStatLine.fields.createdBy.label=创建人
i18n_entity.FbCountTaskStatLine.fields.createdOn.label=创建时间
i18n_entity.FbCountTaskStatLine.fields.id.label=ID
i18n_entity.FbCountTaskStatLine.fields.materialsNum.label=记录物料数
i18n_entity.FbCountTaskStatLine.fields.modifiedBy.label=最后修改人
i18n_entity.FbCountTaskStatLine.fields.modifiedOn.label=最后修改时间
i18n_entity.FbCountTaskStatLine.fields.qty.label=记录总数量
i18n_entity.FbCountTaskStatLine.fields.taskId.label=盘点任务
i18n_entity.FbCountTaskStatLine.fields.version.label=修订版本
i18n_entity.FbCountTaskStatLine.group=Warehouse
i18n_entity.FbCountTaskStatLine.label=盘点单任务统计行
i18n_entity.FbCountTaskStatLine.listCard.materialsNum.prefix=物料数
i18n_entity.FbCountTaskStatLine.listCard.qty.prefix=总数量
i18n_entity.FbCustomer.fields.address.label=地址
i18n_entity.FbCustomer.fields.btDisabled.label=停用
i18n_entity.FbCustomer.fields.contact.label=联系人
i18n_entity.FbCustomer.fields.createdBy.label=创建人
i18n_entity.FbCustomer.fields.createdOn.label=创建时间
i18n_entity.FbCustomer.fields.id.label=客户编码
i18n_entity.FbCustomer.fields.modifiedBy.label=最后修改人
i18n_entity.FbCustomer.fields.modifiedOn.label=最后修改时间
i18n_entity.FbCustomer.fields.name.label=客户名称
i18n_entity.FbCustomer.fields.phone.label=联系电话
i18n_entity.FbCustomer.fields.remark.label=备注
i18n_entity.FbCustomer.fields.ro.label=根组织
i18n_entity.FbCustomer.fields.version.label=版本
i18n_entity.FbCustomer.group=MainData
i18n_entity.FbCustomer.label=客户
i18n_entity.FbCustomer.listCard.btDisabled.formatMapping[0].replaceText=已停用
i18n_entity.FbCustomer.listCard.contact.prefix=联系人
i18n_entity.FbCustomer.listCard.name.prefix=客户名
i18n_entity.FbCustomer.listCard.phone.prefix=电话
i18n_entity.FbDepartment.fields.btDisabled.label=停用
i18n_entity.FbDepartment.fields.btHiLeafNode.label=叶节点
i18n_entity.FbDepartment.fields.btHiNodeLevel.label=层
i18n_entity.FbDepartment.fields.btHiParentNode.label=上级
i18n_entity.FbDepartment.fields.btHiRootNode.label=顶层节点
i18n_entity.FbDepartment.fields.createdBy.label=创建人
i18n_entity.FbDepartment.fields.createdOn.label=创建时间
i18n_entity.FbDepartment.fields.id.label=编号
i18n_entity.FbDepartment.fields.modifiedBy.label=最后修改人
i18n_entity.FbDepartment.fields.modifiedOn.label=最后修改时间
i18n_entity.FbDepartment.fields.name.label=名称
i18n_entity.FbDepartment.fields.owner.label=负责人
i18n_entity.FbDepartment.fields.ro.label=根组织
i18n_entity.FbDepartment.fields.version.label=版本
i18n_entity.FbDepartment.group=User
i18n_entity.FbDepartment.label=组织
i18n_entity.FbDepartment.listCard.owner.prefix=负责人
i18n_entity.FbDevTask.fields.comments.label=评论
i18n_entity.FbDevTask.fields.createdBy.label=创建人
i18n_entity.FbDevTask.fields.createdOn.label=创建时间
i18n_entity.FbDevTask.fields.description.label=描述
i18n_entity.FbDevTask.fields.devVersion.label=迭代
i18n_entity.FbDevTask.fields.files.label=附件文件
i18n_entity.FbDevTask.fields.id.label=编号
i18n_entity.FbDevTask.fields.images.label=附件图片
i18n_entity.FbDevTask.fields.kind.inlineOptionBill.items.Bug.label=缺陷
i18n_entity.FbDevTask.fields.kind.inlineOptionBill.items.Feature.label=功能
i18n_entity.FbDevTask.fields.kind.inlineOptionBill.items.Improvement.label=优化
i18n_entity.FbDevTask.fields.kind.inlineOptionBill.items.Test.label=测试
i18n_entity.FbDevTask.fields.kind.label=类型
i18n_entity.FbDevTask.fields.modifiedBy.label=最后修改人
i18n_entity.FbDevTask.fields.modifiedOn.label=最后修改时间
i18n_entity.FbDevTask.fields.priority.inlineOptionBill.items.High.label=高
i18n_entity.FbDevTask.fields.priority.inlineOptionBill.items.Low.label=低
i18n_entity.FbDevTask.fields.priority.inlineOptionBill.items.Normal.label=中
i18n_entity.FbDevTask.fields.priority.label=优先级
i18n_entity.FbDevTask.fields.processedBy.label=处理人
i18n_entity.FbDevTask.fields.project.label=项目
i18n_entity.FbDevTask.fields.ro.label=企业
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Closed.label=已关闭
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Open.label=新建
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Processing.label=进行中
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Rejected.label=已拒绝
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Resolved.label=已解决
i18n_entity.FbDevTask.fields.state.label=状态
i18n_entity.FbDevTask.fields.testImages.label=测试图片
i18n_entity.FbDevTask.fields.testResult.label=测试结果
i18n_entity.FbDevTask.fields.title.label=标题
i18n_entity.FbDevTask.fields.version.label=版本
i18n_entity.FbDevTask.group=Dev
i18n_entity.FbDevTask.label=协同任务
i18n_entity.FbDevVersion.fields.createdBy.label=创建人
i18n_entity.FbDevVersion.fields.createdOn.label=创建时间
i18n_entity.FbDevVersion.fields.displayOrder.label=显示顺序
i18n_entity.FbDevVersion.fields.done.label=已完成
i18n_entity.FbDevVersion.fields.id.label=ID
i18n_entity.FbDevVersion.fields.modifiedBy.label=最后修改人
i18n_entity.FbDevVersion.fields.modifiedOn.label=最后修改时间
i18n_entity.FbDevVersion.fields.name.label=名称
i18n_entity.FbDevVersion.fields.planDoneOn.label=计划完成时间
i18n_entity.FbDevVersion.fields.ro.label=企业
i18n_entity.FbDevVersion.fields.version.label=版本
i18n_entity.FbDevVersion.group=Dev
i18n_entity.FbDevVersion.label=迭代
i18n_entity.FbDirectPutawayOrder.fields.bin.label=上架库位
i18n_entity.FbDirectPutawayOrder.fields.btInvProcessed.label=库存已处理
i18n_entity.FbDirectPutawayOrder.fields.btLines.label=单行
i18n_entity.FbDirectPutawayOrder.fields.btOrderKind.inlineOptionBill.items.MfFinishedInbound.label=产成品入库
i18n_entity.FbDirectPutawayOrder.fields.btOrderKind.inlineOptionBill.items.OtherInbound.label=其他入库
i18n_entity.FbDirectPutawayOrder.fields.btOrderKind.inlineOptionBill.items.PurchaseInbound.label=采购入库
i18n_entity.FbDirectPutawayOrder.fields.btOrderKind.label=类型
i18n_entity.FbDirectPutawayOrder.fields.btOrderState.inlineOptionBill.items.Init.label=未提交
i18n_entity.FbDirectPutawayOrder.fields.btOrderState.inlineOptionBill.items.Submitted.label=已提交
i18n_entity.FbDirectPutawayOrder.fields.btOrderState.label=业务状态
i18n_entity.FbDirectPutawayOrder.fields.btOrderStateReason.label=状态说明
i18n_entity.FbDirectPutawayOrder.fields.container.label=上架容器
i18n_entity.FbDirectPutawayOrder.fields.createdBy.label=创建人
i18n_entity.FbDirectPutawayOrder.fields.createdOn.label=创建时间
i18n_entity.FbDirectPutawayOrder.fields.id.label=单号
i18n_entity.FbDirectPutawayOrder.fields.modifiedBy.label=最后修改人
i18n_entity.FbDirectPutawayOrder.fields.modifiedOn.label=最后修改时间
i18n_entity.FbDirectPutawayOrder.fields.remark.label=备注
i18n_entity.FbDirectPutawayOrder.fields.ro.label=根组织
i18n_entity.FbDirectPutawayOrder.fields.version.label=版本
i18n_entity.FbDirectPutawayOrder.group=Warehouse
i18n_entity.FbDirectPutawayOrder.label=人工上架单
i18n_entity.FbDirectPutawayOrder.listCard.bin.prefix=上架库位
i18n_entity.FbDirectPutawayOrder.listCard.container.prefix=上架容器
i18n_entity.FbDirectPutawayOrder.states.states.Init.label=未提交
i18n_entity.FbDirectPutawayOrder.states.states.Init.nextStates.Submitted.buttonLabel=提交
i18n_entity.FbDirectPutawayOrder.states.states.Submitted.label=已提交
i18n_entity.FbDirectPutawayOrderLine.fields.btLineNo.label=行号
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterial.label=物料
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialCategory.label=物料分类编号
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialCategoryName.label=物料分类名称
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialId.label=物料编号
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialImage.label=物料图片
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialModel.label=物料型号
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialName.label=物料名称
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialSpec.label=物料规格
i18n_entity.FbDirectPutawayOrderLine.fields.btParentId.label=所属上架单
i18n_entity.FbDirectPutawayOrderLine.fields.createdBy.label=创建人
i18n_entity.FbDirectPutawayOrderLine.fields.createdOn.label=创建时间
i18n_entity.FbDirectPutawayOrderLine.fields.id.label=ID
i18n_entity.FbDirectPutawayOrderLine.fields.lotNo.label=批次号
i18n_entity.FbDirectPutawayOrderLine.fields.modifiedBy.label=最后修改人
i18n_entity.FbDirectPutawayOrderLine.fields.modifiedOn.label=最后修改时间
i18n_entity.FbDirectPutawayOrderLine.fields.price.label=单价
i18n_entity.FbDirectPutawayOrderLine.fields.qty.label=上架数量
i18n_entity.FbDirectPutawayOrderLine.fields.qualityLevel.label=质量等级
i18n_entity.FbDirectPutawayOrderLine.fields.ro.label=根组织
i18n_entity.FbDirectPutawayOrderLine.fields.subContainerId.label=格号
i18n_entity.FbDirectPutawayOrderLine.fields.unitLabel.label=单位名称
i18n_entity.FbDirectPutawayOrderLine.fields.version.label=版本
i18n_entity.FbDirectPutawayOrderLine.group=Warehouse
i18n_entity.FbDirectPutawayOrderLine.label=人工上架单行
i18n_entity.FbDirectPutawayOrderLine.listCard.btMaterialId.prefix=物料编号
i18n_entity.FbDirectPutawayOrderLine.listCard.qty.prefix=数量
i18n_entity.FbDirectPutawayOrderLine.listCard.subContainerId.prefix=格号
i18n_entity.FbDistrict.fields.btDisabled.label=停用
i18n_entity.FbDistrict.fields.createdBy.label=创建人
i18n_entity.FbDistrict.fields.createdOn.label=创建时间
i18n_entity.FbDistrict.fields.displayOrder.label=显示顺序
i18n_entity.FbDistrict.fields.id.label=库区编号
i18n_entity.FbDistrict.fields.modifiedBy.label=最后修改人
i18n_entity.FbDistrict.fields.modifiedOn.label=最后修改时间
i18n_entity.FbDistrict.fields.name.label=库区名称
i18n_entity.FbDistrict.fields.remark.label=备注
i18n_entity.FbDistrict.fields.ro.label=根组织
i18n_entity.FbDistrict.fields.structure.label=库区结构
i18n_entity.FbDistrict.fields.version.label=版本
i18n_entity.FbDistrict.fields.warehouse.label=所属仓库
i18n_entity.FbDistrict.group=MainData
i18n_entity.FbDistrict.label=库区
i18n_entity.FbGoodsOwner.fields.address.label=地址
i18n_entity.FbGoodsOwner.fields.btDisabled.label=停用
i18n_entity.FbGoodsOwner.fields.contact.label=联系人
i18n_entity.FbGoodsOwner.fields.createdBy.label=创建人
i18n_entity.FbGoodsOwner.fields.createdOn.label=创建时间
i18n_entity.FbGoodsOwner.fields.id.label=货主编码
i18n_entity.FbGoodsOwner.fields.modifiedBy.label=最后修改人
i18n_entity.FbGoodsOwner.fields.modifiedOn.label=最后修改时间
i18n_entity.FbGoodsOwner.fields.name.label=货主名称
i18n_entity.FbGoodsOwner.fields.phone.label=联系电话
i18n_entity.FbGoodsOwner.fields.remark.label=备注
i18n_entity.FbGoodsOwner.fields.ro.label=根组织
i18n_entity.FbGoodsOwner.fields.version.label=版本
i18n_entity.FbGoodsOwner.group=MainData
i18n_entity.FbGoodsOwner.label=货主
i18n_entity.FbGoodsOwner.listCard.contact.prefix=联系人
i18n_entity.FbGoodsOwner.listCard.name.prefix=货主名
i18n_entity.FbGoodsOwner.listCard.phone.prefix=电话
i18n_entity.FbInboundOrder.fields.asnId.label=到货通知号
i18n_entity.FbInboundOrder.fields.btInvProcessed.label=库存已处理
i18n_entity.FbInboundOrder.fields.btLines.label=单行
i18n_entity.FbInboundOrder.fields.btOrderKind.inlineOptionBill.items.MfFinishedInbound.label=产成品入库
i18n_entity.FbInboundOrder.fields.btOrderKind.inlineOptionBill.items.OtherInbound.label=其他入库
i18n_entity.FbInboundOrder.fields.btOrderKind.inlineOptionBill.items.PurchaseInbound.label=采购入库
i18n_entity.FbInboundOrder.fields.btOrderKind.label=类型
i18n_entity.FbInboundOrder.fields.btOrderState.inlineOptionBill.items.Committed.label=已提交
i18n_entity.FbInboundOrder.fields.btOrderState.inlineOptionBill.items.Init.label=未提交
i18n_entity.FbInboundOrder.fields.btOrderState.label=业务状态
i18n_entity.FbInboundOrder.fields.btOrderStateReason.label=状态说明
i18n_entity.FbInboundOrder.fields.callContainerAll.label=已叫空容器
i18n_entity.FbInboundOrder.fields.createdBy.label=创建人
i18n_entity.FbInboundOrder.fields.createdOn.label=创建时间
i18n_entity.FbInboundOrder.fields.district.label=入库库区
i18n_entity.FbInboundOrder.fields.id.label=单号
i18n_entity.FbInboundOrder.fields.mfgFQCOrderId.label=成品质检单号
i18n_entity.FbInboundOrder.fields.mfgOrderId.label=生产工单号
i18n_entity.FbInboundOrder.fields.modifiedBy.label=最后修改人
i18n_entity.FbInboundOrder.fields.modifiedOn.label=最后修改时间
i18n_entity.FbInboundOrder.fields.planQty.label=收货数量
i18n_entity.FbInboundOrder.fields.purchaseOrderId.label=采购订单号
i18n_entity.FbInboundOrder.fields.qty.label=本次入库数量
i18n_entity.FbInboundOrder.fields.recevingOrderId.label=收货单号
i18n_entity.FbInboundOrder.fields.remark.label=备注
i18n_entity.FbInboundOrder.fields.ro.label=根组织
i18n_entity.FbInboundOrder.fields.vendor.label=供应商
i18n_entity.FbInboundOrder.fields.version.label=版本
i18n_entity.FbInboundOrder.fields.warehouse.label=入库仓库
i18n_entity.FbInboundOrder.group=Warehouse
i18n_entity.FbInboundOrder.label=入库单
i18n_entity.FbInboundOrder.states.states.Committed.label=已提交
i18n_entity.FbInboundOrder.states.states.Init.label=未提交
i18n_entity.FbInboundOrder.states.states.Init.nextStates.Committed.buttonLabel=提交
i18n_entity.FbInboundOrderLine.fields.bin.label=存储库位
i18n_entity.FbInboundOrderLine.fields.btLineNo.label=行号
i18n_entity.FbInboundOrderLine.fields.btMaterial.label=物料
i18n_entity.FbInboundOrderLine.fields.btMaterialCategory.label=物料分类编号
i18n_entity.FbInboundOrderLine.fields.btMaterialCategoryName.label=物料分类名称
i18n_entity.FbInboundOrderLine.fields.btMaterialId.label=物料编号
i18n_entity.FbInboundOrderLine.fields.btMaterialImage.label=物料图片
i18n_entity.FbInboundOrderLine.fields.btMaterialModel.label=物料型号
i18n_entity.FbInboundOrderLine.fields.btMaterialName.label=物料名称
i18n_entity.FbInboundOrderLine.fields.btMaterialSpec.label=物料规格
i18n_entity.FbInboundOrderLine.fields.btParentId.label=所属入库单
i18n_entity.FbInboundOrderLine.fields.callContainerQty.label=已分配容器数量
i18n_entity.FbInboundOrderLine.fields.createdBy.label=创建人
i18n_entity.FbInboundOrderLine.fields.createdOn.label=创建时间
i18n_entity.FbInboundOrderLine.fields.finishedQty.label=之前入库数量
i18n_entity.FbInboundOrderLine.fields.id.label=ID
i18n_entity.FbInboundOrderLine.fields.lotNo.label=批次号
i18n_entity.FbInboundOrderLine.fields.materialName.label=物料名称
i18n_entity.FbInboundOrderLine.fields.modifiedBy.label=最后修改人
i18n_entity.FbInboundOrderLine.fields.modifiedOn.label=最后修改时间
i18n_entity.FbInboundOrderLine.fields.planQty.label=收货数量
i18n_entity.FbInboundOrderLine.fields.price.label=单价
i18n_entity.FbInboundOrderLine.fields.qty.label=入库数量
i18n_entity.FbInboundOrderLine.fields.qualityLevel.label=质量等级
i18n_entity.FbInboundOrderLine.fields.ro.label=根组织
i18n_entity.FbInboundOrderLine.fields.storeQty.label=上架数量
i18n_entity.FbInboundOrderLine.fields.unitLabel.label=单位名称
i18n_entity.FbInboundOrderLine.fields.version.label=版本
i18n_entity.FbInboundOrderLine.group=Warehouse
i18n_entity.FbInboundOrderLine.label=入库单行
i18n_entity.FbInboundOrderLine.listCard.btMaterialId.prefix=物料编号
i18n_entity.FbInboundOrderLine.listCard.callContainerQty.prefix=分配容器
i18n_entity.FbInboundOrderLine.listCard.qty.prefix=入库
i18n_entity.FbInboundOrderLine.listCard.storeQty.prefix=上架
i18n_entity.FbInvChange.fields.btMaterial.label=物料
i18n_entity.FbInvChange.fields.btMaterialCategory.label=物料分类编号
i18n_entity.FbInvChange.fields.btMaterialCategoryName.label=物料分类名称
i18n_entity.FbInvChange.fields.btMaterialId.label=物料编号
i18n_entity.FbInvChange.fields.btMaterialImage.label=物料图片
i18n_entity.FbInvChange.fields.btMaterialModel.label=物料型号
i18n_entity.FbInvChange.fields.btMaterialName.label=物料名称
i18n_entity.FbInvChange.fields.btMaterialSpec.label=物料规格
i18n_entity.FbInvChange.fields.container.label=容器
i18n_entity.FbInvChange.fields.createdBy.label=创建人
i18n_entity.FbInvChange.fields.createdOn.label=创建时间
i18n_entity.FbInvChange.fields.id.label=ID
i18n_entity.FbInvChange.fields.lotNo.label=批次
i18n_entity.FbInvChange.fields.modifiedBy.label=最后修改人
i18n_entity.FbInvChange.fields.modifiedOn.label=最后修改时间
i18n_entity.FbInvChange.fields.qty.label=数量
i18n_entity.FbInvChange.fields.subContainerId.label=格号
i18n_entity.FbInvChange.fields.version.label=修订版本
i18n_entity.FbInvChange.group=Warehouse
i18n_entity.FbInvChange.label=库存变更
i18n_entity.FbInvLayout.fields.amount.label=金额
i18n_entity.FbInvLayout.fields.assemblyLine.label=所属产线
i18n_entity.FbInvLayout.fields.bin.label=所在库位
i18n_entity.FbInvLayout.fields.btBzDesc.label=业务描述
i18n_entity.FbInvLayout.fields.btBzMark.label=业务标记
i18n_entity.FbInvLayout.fields.btMaterial.label=物料
i18n_entity.FbInvLayout.fields.btMaterialCategory.label=物料分类编号
i18n_entity.FbInvLayout.fields.btMaterialCategoryName.label=物料分类名称
i18n_entity.FbInvLayout.fields.btMaterialId.label=物料编号
i18n_entity.FbInvLayout.fields.btMaterialImage.label=物料图片
i18n_entity.FbInvLayout.fields.btMaterialModel.label=物料型号
i18n_entity.FbInvLayout.fields.btMaterialName.label=物料名称
i18n_entity.FbInvLayout.fields.btMaterialSpec.label=物料规格
i18n_entity.FbInvLayout.fields.btMaterialTopCategory.label=物料一级分类编号
i18n_entity.FbInvLayout.fields.btMaterialTopCategoryName.label=物料一级分类名称
i18n_entity.FbInvLayout.fields.btPrepare.label=btPrepare
i18n_entity.FbInvLayout.fields.channel.label=巷道
i18n_entity.FbInvLayout.fields.column.label=列
i18n_entity.FbInvLayout.fields.createdBy.label=创建人
i18n_entity.FbInvLayout.fields.createdOn.label=创建时间
i18n_entity.FbInvLayout.fields.depth.label=深
i18n_entity.FbInvLayout.fields.district.label=所在库区
i18n_entity.FbInvLayout.fields.expDate.label=有效期
i18n_entity.FbInvLayout.fields.id.label=ID
i18n_entity.FbInvLayout.fields.inboundOn.label=入库时间
i18n_entity.FbInvLayout.fields.inboundOrderId.label=入库单号
i18n_entity.FbInvLayout.fields.inboundOrderLineNo.label=入库单行号
i18n_entity.FbInvLayout.fields.layer.label=层
i18n_entity.FbInvLayout.fields.leafContainer.label=最内层容器
i18n_entity.FbInvLayout.fields.leafContainerType.label=最内层容器类型
i18n_entity.FbInvLayout.fields.locked.label=锁定
i18n_entity.FbInvLayout.fields.locked.listBatchButtons.buttons[0].label=解锁
i18n_entity.FbInvLayout.fields.lotNo.label=批次号
i18n_entity.FbInvLayout.fields.matLotNo.label=批次
i18n_entity.FbInvLayout.fields.matSerialNo.label=序列号
i18n_entity.FbInvLayout.fields.mfgDate.label=生产日期
i18n_entity.FbInvLayout.fields.modifiedBy.label=最后修改人
i18n_entity.FbInvLayout.fields.modifiedOn.label=最后修改时间
i18n_entity.FbInvLayout.fields.onRobot.label=所在机器人
i18n_entity.FbInvLayout.fields.outboundOrderId.label=出库单号
i18n_entity.FbInvLayout.fields.outboundOrderLineNo.label=出库单行号
i18n_entity.FbInvLayout.fields.owner.label=货主
i18n_entity.FbInvLayout.fields.price.label=单价
i18n_entity.FbInvLayout.fields.qty.label=数量
i18n_entity.FbInvLayout.fields.rack.label=货架
i18n_entity.FbInvLayout.fields.refInv.label=引用库存明细
i18n_entity.FbInvLayout.fields.robotBin.label=所在机器人的库位
i18n_entity.FbInvLayout.fields.row.label=排
i18n_entity.FbInvLayout.fields.state.inlineOptionBill.items.Assigned.label=已分配
i18n_entity.FbInvLayout.fields.state.inlineOptionBill.items.Received.label=已收货
i18n_entity.FbInvLayout.fields.state.inlineOptionBill.items.Storing.label=存储中
i18n_entity.FbInvLayout.fields.state.label=库存状态
i18n_entity.FbInvLayout.fields.subContainerId.label=格子
i18n_entity.FbInvLayout.fields.topContainer.label=最外层容器
i18n_entity.FbInvLayout.fields.topContainerType.label=最外层容器类型
i18n_entity.FbInvLayout.fields.usedQty.label=已分配数量
i18n_entity.FbInvLayout.fields.validityDay.label=有效期天数
i18n_entity.FbInvLayout.fields.vendor.label=供应商
i18n_entity.FbInvLayout.fields.version.label=修订版本
i18n_entity.FbInvLayout.fields.warehouse.label=所在仓库
i18n_entity.FbInvLayout.fields.workSite.label=工位
i18n_entity.FbInvLayout.group=Warehouse
i18n_entity.FbInvLayout.label=库存明细
i18n_entity.FbInvLayout.listCard.bin.prefix=库位
i18n_entity.FbInvLayout.listCard.inboundOn.prefix=入库时间
i18n_entity.FbInvLayout.listCard.lotNo.prefix=批次号
i18n_entity.FbInvLayout.listCard.qty.prefix=数量
i18n_entity.FbInvLayout.listCard.topContainer.prefix=容器
i18n_entity.FbInvSnapShot.fields.btMaterial.label=物料
i18n_entity.FbInvSnapShot.fields.btMaterialCategory.label=物料分类编号
i18n_entity.FbInvSnapShot.fields.btMaterialCategoryName.label=物料分类名称
i18n_entity.FbInvSnapShot.fields.btMaterialId.label=物料编号
i18n_entity.FbInvSnapShot.fields.btMaterialImage.label=物料图片
i18n_entity.FbInvSnapShot.fields.btMaterialModel.label=物料型号
i18n_entity.FbInvSnapShot.fields.btMaterialName.label=物料名称
i18n_entity.FbInvSnapShot.fields.btMaterialSpec.label=物料规格
i18n_entity.FbInvSnapShot.fields.createdBy.label=创建人
i18n_entity.FbInvSnapShot.fields.createdOn.label=创建时间
i18n_entity.FbInvSnapShot.fields.id.label=ID
i18n_entity.FbInvSnapShot.fields.modifiedBy.label=最后修改人
i18n_entity.FbInvSnapShot.fields.modifiedOn.label=最后修改时间
i18n_entity.FbInvSnapShot.fields.qty.label=数量
i18n_entity.FbInvSnapShot.fields.uuid.label=uuid
i18n_entity.FbInvSnapShot.fields.version.label=修订版本
i18n_entity.FbInvSnapShot.group=Warehouse
i18n_entity.FbInvSnapShot.label=库存快照
i18n_entity.FbMaterial.fields.abc.label=ABC 分类
i18n_entity.FbMaterial.fields.btDisabled.label=停用
i18n_entity.FbMaterial.fields.categoriesDesc.label=物料分类描述
i18n_entity.FbMaterial.fields.category1.label=一级分类
i18n_entity.FbMaterial.fields.category2.label=二级分类
i18n_entity.FbMaterial.fields.category3.label=三级分类
i18n_entity.FbMaterial.fields.createdBy.label=创建人
i18n_entity.FbMaterial.fields.createdOn.label=创建时间
i18n_entity.FbMaterial.fields.displayDecimals.label=小数点位数
i18n_entity.FbMaterial.fields.endOn.label=停用日期
i18n_entity.FbMaterial.fields.height.label=高度
i18n_entity.FbMaterial.fields.id.label=物料编码
i18n_entity.FbMaterial.fields.image.label=图片
i18n_entity.FbMaterial.fields.leafCategory.label=物料分类
i18n_entity.FbMaterial.fields.length.label=长度
i18n_entity.FbMaterial.fields.mainUnit.label=主计量单位名称
i18n_entity.FbMaterial.fields.mainVendor.label=主供应商
i18n_entity.FbMaterial.fields.mixLotNo.label=批次混放
i18n_entity.FbMaterial.fields.mixMaterial.label=物料混放
i18n_entity.FbMaterial.fields.model.label=型号
i18n_entity.FbMaterial.fields.modifiedBy.label=最后修改人
i18n_entity.FbMaterial.fields.modifiedOn.label=最后修改时间
i18n_entity.FbMaterial.fields.name.label=物料名称
i18n_entity.FbMaterial.fields.owner.label=货主
i18n_entity.FbMaterial.fields.price.label=单价
i18n_entity.FbMaterial.fields.remark.label=备注
i18n_entity.FbMaterial.fields.ro.label=根组织
i18n_entity.FbMaterial.fields.spec.label=物料规格
i18n_entity.FbMaterial.fields.startOn.label=启用日期
i18n_entity.FbMaterial.fields.syncOut.label=外部导入
i18n_entity.FbMaterial.fields.topCat.label=存货大类名称
i18n_entity.FbMaterial.fields.unit.label=物料单位
i18n_entity.FbMaterial.fields.unitLabel.label=单位名称
i18n_entity.FbMaterial.fields.version.label=版本
i18n_entity.FbMaterial.fields.volume.label=容积
i18n_entity.FbMaterial.fields.weight.label=重量
i18n_entity.FbMaterial.fields.width.label=宽度
i18n_entity.FbMaterial.group=MainData
i18n_entity.FbMaterial.label=物料
i18n_entity.FbMaterial.listCard.leafCategory.prefix=物料分类
i18n_entity.FbMaterialCategory.fields.btDisabled.label=停用
i18n_entity.FbMaterialCategory.fields.createdBy.label=创建人
i18n_entity.FbMaterialCategory.fields.createdOn.label=创建时间
i18n_entity.FbMaterialCategory.fields.id.label=分类编号
i18n_entity.FbMaterialCategory.fields.modifiedBy.label=最后修改人
i18n_entity.FbMaterialCategory.fields.modifiedOn.label=最后修改时间
i18n_entity.FbMaterialCategory.fields.name.label=名称
i18n_entity.FbMaterialCategory.fields.parent.label=上级分类
i18n_entity.FbMaterialCategory.fields.remark.label=备注
i18n_entity.FbMaterialCategory.fields.storeDistricts.label=存储库区
i18n_entity.FbMaterialCategory.fields.version.label=修订版本
i18n_entity.FbMaterialCategory.group=MainData
i18n_entity.FbMaterialCategory.label=物料分类
i18n_entity.FbMaterialCategory.listCard.storeDistricts.prefix=存储库区
i18n_entity.FbMaterialContainerMaxQty.fields.btDisabled.label=停用
i18n_entity.FbMaterialContainerMaxQty.fields.btMaterial.label=物料
i18n_entity.FbMaterialContainerMaxQty.fields.containerType.label=容器类型
i18n_entity.FbMaterialContainerMaxQty.fields.createdBy.label=创建人
i18n_entity.FbMaterialContainerMaxQty.fields.createdOn.label=创建时间
i18n_entity.FbMaterialContainerMaxQty.fields.id.label=ID
i18n_entity.FbMaterialContainerMaxQty.fields.maxQty.label=最多放几个
i18n_entity.FbMaterialContainerMaxQty.fields.modifiedBy.label=最后修改人
i18n_entity.FbMaterialContainerMaxQty.fields.modifiedOn.label=最后修改时间
i18n_entity.FbMaterialContainerMaxQty.fields.version.label=修订版本
i18n_entity.FbMaterialContainerMaxQty.group=MainData
i18n_entity.FbMaterialContainerMaxQty.label=物料容器容量
i18n_entity.FbMaterialContainerMaxQty.listCard.btMaterial.prefix=存放物料
i18n_entity.FbMaterialContainerMaxQty.listCard.maxQty.prefix=最多放
i18n_entity.FbMaterialContainerMaxQty.listCard.maxQty.suffix=个
i18n_entity.FbMaterialLot.fields.createdBy.label=创建人
i18n_entity.FbMaterialLot.fields.createdOn.label=创建时间
i18n_entity.FbMaterialLot.fields.disabled.label=禁用
i18n_entity.FbMaterialLot.fields.id.label=生产批号
i18n_entity.FbMaterialLot.fields.material.label=所属物料
i18n_entity.FbMaterialLot.fields.modifiedBy.label=最后修改人
i18n_entity.FbMaterialLot.fields.modifiedOn.label=最后修改时间
i18n_entity.FbMaterialLot.fields.name.label=生产批名称
i18n_entity.FbMaterialLot.fields.remark.label=备注
i18n_entity.FbMaterialLot.fields.ro.label=根组织
i18n_entity.FbMaterialLot.fields.version.label=版本
i18n_entity.FbMaterialLot.group=MainData
i18n_entity.FbMaterialLot.label=物料批次
i18n_entity.FbMaterialUnit.fields.basic.label=基本单位
i18n_entity.FbMaterialUnit.fields.createdBy.label=创建人
i18n_entity.FbMaterialUnit.fields.createdOn.label=创建时间
i18n_entity.FbMaterialUnit.fields.disabled.label=禁用
i18n_entity.FbMaterialUnit.fields.id.label=单位编码
i18n_entity.FbMaterialUnit.fields.modifiedBy.label=最后修改人
i18n_entity.FbMaterialUnit.fields.modifiedOn.label=最后修改时间
i18n_entity.FbMaterialUnit.fields.name.label=单位名称
i18n_entity.FbMaterialUnit.fields.parent.label=父级单位
i18n_entity.FbMaterialUnit.fields.ratio.label=换算关系
i18n_entity.FbMaterialUnit.fields.remark.label=备注
i18n_entity.FbMaterialUnit.fields.ro.label=根组织
i18n_entity.FbMaterialUnit.fields.version.label=版本
i18n_entity.FbMaterialUnit.group=MainData
i18n_entity.FbMaterialUnit.label=物料单位
i18n_entity.FbOutboundOrder.fields.btInvProcessed.label=库存已处理
i18n_entity.FbOutboundOrder.fields.btLines.label=单行
i18n_entity.FbOutboundOrder.fields.btOrderKind.inlineOptionBill.items.Product.label=商品出库
i18n_entity.FbOutboundOrder.fields.btOrderKind.label=类型
i18n_entity.FbOutboundOrder.fields.btOrderState.inlineOptionBill.items.Committed.label=已提交
i18n_entity.FbOutboundOrder.fields.btOrderState.inlineOptionBill.items.Init.label=未提交
i18n_entity.FbOutboundOrder.fields.btOrderState.label=业务状态
i18n_entity.FbOutboundOrder.fields.btOrderStateReason.label=状态说明
i18n_entity.FbOutboundOrder.fields.createdBy.label=创建人
i18n_entity.FbOutboundOrder.fields.createdOn.label=创建时间
i18n_entity.FbOutboundOrder.fields.customer.label=客户
i18n_entity.FbOutboundOrder.fields.direct.label=直接出库
i18n_entity.FbOutboundOrder.fields.district.label=出库库区
i18n_entity.FbOutboundOrder.fields.id.label=单号
i18n_entity.FbOutboundOrder.fields.invAssignedAll.label=库存分配完成
i18n_entity.FbOutboundOrder.fields.modifiedBy.label=最后修改人
i18n_entity.FbOutboundOrder.fields.modifiedOn.label=最后修改时间
i18n_entity.FbOutboundOrder.fields.planQty.label=计划出库数量
i18n_entity.FbOutboundOrder.fields.priority.label=优先级
i18n_entity.FbOutboundOrder.fields.qty.label=本次出库数量
i18n_entity.FbOutboundOrder.fields.receiver.label=收货单位
i18n_entity.FbOutboundOrder.fields.remark.label=备注
i18n_entity.FbOutboundOrder.fields.ro.label=根组织
i18n_entity.FbOutboundOrder.fields.saleOrderId.label=销售订单号
i18n_entity.FbOutboundOrder.fields.saleShipOrderId.label=销售发货单
i18n_entity.FbOutboundOrder.fields.version.label=版本
i18n_entity.FbOutboundOrder.fields.warehouse.label=出库仓库
i18n_entity.FbOutboundOrder.group=Warehouse
i18n_entity.FbOutboundOrder.label=出库单
i18n_entity.FbOutboundOrder.listCard.priority.prefix=优先级
i18n_entity.FbOutboundOrder.states.states.Committed.label=已提交
i18n_entity.FbOutboundOrder.states.states.Init.label=未提交
i18n_entity.FbOutboundOrder.states.states.Init.nextStates.Committed.buttonLabel=提交
i18n_entity.FbOutboundOrderLine.fields.btInvRefAll.label=总库存
i18n_entity.FbOutboundOrderLine.fields.btInvRefMatch.label=可用库存
i18n_entity.FbOutboundOrderLine.fields.btLineNo.label=行号
i18n_entity.FbOutboundOrderLine.fields.btMaterial.label=物料
i18n_entity.FbOutboundOrderLine.fields.btMaterialCategory.label=物料分类编号
i18n_entity.FbOutboundOrderLine.fields.btMaterialCategoryName.label=物料分类名称
i18n_entity.FbOutboundOrderLine.fields.btMaterialId.label=物料编号
i18n_entity.FbOutboundOrderLine.fields.btMaterialImage.label=物料图片
i18n_entity.FbOutboundOrderLine.fields.btMaterialModel.label=物料型号
i18n_entity.FbOutboundOrderLine.fields.btMaterialName.label=物料名称
i18n_entity.FbOutboundOrderLine.fields.btMaterialSpec.label=物料规格
i18n_entity.FbOutboundOrderLine.fields.btParentId.label=所属出库单
i18n_entity.FbOutboundOrderLine.fields.createdBy.label=创建人
i18n_entity.FbOutboundOrderLine.fields.createdOn.label=创建时间
i18n_entity.FbOutboundOrderLine.fields.finishedQty.label=之前出库数量
i18n_entity.FbOutboundOrderLine.fields.id.label=ID
i18n_entity.FbOutboundOrderLine.fields.invAssignedQty.label=已分配库存数量
i18n_entity.FbOutboundOrderLine.fields.layoutId.label=关联库存明细 ID
i18n_entity.FbOutboundOrderLine.fields.lotNo.label=批次号
i18n_entity.FbOutboundOrderLine.fields.materialName.label=物料名称
i18n_entity.FbOutboundOrderLine.fields.modifiedBy.label=最后修改人
i18n_entity.FbOutboundOrderLine.fields.modifiedOn.label=最后修改时间
i18n_entity.FbOutboundOrderLine.fields.planQty.label=计划出库总数
i18n_entity.FbOutboundOrderLine.fields.qty.label=出库数量
i18n_entity.FbOutboundOrderLine.fields.qualityLevel.label=质量等级
i18n_entity.FbOutboundOrderLine.fields.ro.label=根组织
i18n_entity.FbOutboundOrderLine.fields.version.label=版本
i18n_entity.FbOutboundOrderLine.group=Warehouse
i18n_entity.FbOutboundOrderLine.label=出库单行
i18n_entity.FbOutboundOrderLine.listCard.btMaterialId.prefix=物料编号
i18n_entity.FbOutboundOrderLine.listCard.invAssignedQty.prefix=已分配库存
i18n_entity.FbOutboundOrderLine.listCard.lotNo.prefix=批次号
i18n_entity.FbOutboundOrderLine.listCard.qty.prefix=数量
i18n_entity.FbPkg.fields.createdBy.label=创建人
i18n_entity.FbPkg.fields.createdOn.label=创建时间
i18n_entity.FbPkg.fields.disabled.label=禁用
i18n_entity.FbPkg.fields.id.label=包装代码
i18n_entity.FbPkg.fields.material.label=所属物料
i18n_entity.FbPkg.fields.modifiedBy.label=最后修改人
i18n_entity.FbPkg.fields.modifiedOn.label=最后修改时间
i18n_entity.FbPkg.fields.name.label=包装名称
i18n_entity.FbPkg.fields.purpose.label=包装用途
i18n_entity.FbPkg.fields.qty.label=包内数量
i18n_entity.FbPkg.fields.remark.label=备注
i18n_entity.FbPkg.fields.ro.label=根组织
i18n_entity.FbPkg.fields.version.label=版本
i18n_entity.FbPkg.group=MainData
i18n_entity.FbPkg.label=包装规格
i18n_entity.FbPkg.listCard.disabled.formatMapping[0].replaceText=已停用
i18n_entity.FbPkg.listCard.material.prefix=所属物料
i18n_entity.FbPkg.listCard.name.prefix=包装名
i18n_entity.FbPkg.listCard.qty.prefix=最多放
i18n_entity.FbPkg.listCard.qty.suffix=个
i18n_entity.FbVendor.fields.address.label=地址
i18n_entity.FbVendor.fields.btDisabled.label=停用
i18n_entity.FbVendor.fields.contact.label=联系人
i18n_entity.FbVendor.fields.createdBy.label=创建人
i18n_entity.FbVendor.fields.createdOn.label=创建时间
i18n_entity.FbVendor.fields.email.label=电子邮箱
i18n_entity.FbVendor.fields.id.label=供应商编号
i18n_entity.FbVendor.fields.level.label=级别
i18n_entity.FbVendor.fields.modifiedBy.label=最后修改人
i18n_entity.FbVendor.fields.modifiedOn.label=最后修改时间
i18n_entity.FbVendor.fields.name.label=供应商名称
i18n_entity.FbVendor.fields.phone.label=联系电话
i18n_entity.FbVendor.fields.remark.label=备注
i18n_entity.FbVendor.fields.ro.label=根组织
i18n_entity.FbVendor.fields.version.label=版本
i18n_entity.FbVendor.group=MainData
i18n_entity.FbVendor.label=供应商
i18n_entity.FbVendor.listCard.contact.prefix=联系人
i18n_entity.FbVendor.listCard.name.prefix=名称
i18n_entity.FbVendor.listCard.phone.prefix=电话
i18n_entity.FbWarehouse.fields.address.label=地址
i18n_entity.FbWarehouse.fields.btDisabled.label=停用
i18n_entity.FbWarehouse.fields.contact.label=联系人
i18n_entity.FbWarehouse.fields.createdBy.label=创建人
i18n_entity.FbWarehouse.fields.createdOn.label=创建时间
i18n_entity.FbWarehouse.fields.defaultBin.label=默认库位
i18n_entity.FbWarehouse.fields.displayOrder.label=显示顺序
i18n_entity.FbWarehouse.fields.id.label=仓库编号
i18n_entity.FbWarehouse.fields.latitude.label=位置纬度
i18n_entity.FbWarehouse.fields.longitude.label=位置经度
i18n_entity.FbWarehouse.fields.modifiedBy.label=最后修改人
i18n_entity.FbWarehouse.fields.modifiedOn.label=最后修改时间
i18n_entity.FbWarehouse.fields.name.label=仓库名称
i18n_entity.FbWarehouse.fields.phone.label=联系电话
i18n_entity.FbWarehouse.fields.remark.label=备注
i18n_entity.FbWarehouse.fields.ro.label=根组织
i18n_entity.FbWarehouse.fields.version.label=版本
i18n_entity.FbWarehouse.fields.volume.label=容积
i18n_entity.FbWarehouse.group=MainData
i18n_entity.FbWarehouse.label=仓库
i18n_entity.FbWarehouse.listCard.btDisabled.formatMapping[0].replaceText=已停用
i18n_entity.FbWorkPosition.fields.createdBy.label=创建人
i18n_entity.FbWorkPosition.fields.createdOn.label=创建时间
i18n_entity.FbWorkPosition.fields.disabled.label=禁用
i18n_entity.FbWorkPosition.fields.id.label=单号
i18n_entity.FbWorkPosition.fields.modifiedBy.label=最后修改人
i18n_entity.FbWorkPosition.fields.modifiedOn.label=最后修改时间
i18n_entity.FbWorkPosition.fields.name.label=名称
i18n_entity.FbWorkPosition.fields.remark.label=备注
i18n_entity.FbWorkPosition.fields.ro.label=根组织
i18n_entity.FbWorkPosition.fields.version.label=版本
i18n_entity.FbWorkPosition.group=MainData
i18n_entity.FbWorkPosition.label=岗位
i18n_entity.FbWorkPosition.listCard.disabled.formatMapping[0].replaceText=已停用
i18n_entity.FbWorkSite.fields.bin.label=所属库位
i18n_entity.FbWorkSite.fields.createdBy.label=创建人
i18n_entity.FbWorkSite.fields.createdOn.label=创建时间
i18n_entity.FbWorkSite.fields.disabled.label=禁用
i18n_entity.FbWorkSite.fields.id.label=工位编码
i18n_entity.FbWorkSite.fields.kind.label=工位类型
i18n_entity.FbWorkSite.fields.line.label=所属产线
i18n_entity.FbWorkSite.fields.modifiedBy.label=最后修改人
i18n_entity.FbWorkSite.fields.modifiedOn.label=最后修改时间
i18n_entity.FbWorkSite.fields.name.label=工位名称
i18n_entity.FbWorkSite.fields.position.label=所属岗位
i18n_entity.FbWorkSite.fields.remark.label=备注
i18n_entity.FbWorkSite.fields.ro.label=根组织
i18n_entity.FbWorkSite.fields.version.label=版本
i18n_entity.FbWorkSite.group=MainData
i18n_entity.FbWorkSite.label=工位
i18n_entity.FbWorkSite.listCard.bin.prefix=所属库位
i18n_entity.FbWorkSite.listCard.disabled.formatMapping[0].replaceText=已停用
i18n_entity.FbWorkSite.listCard.kind.prefix=工位类型
i18n_entity.FbWorkSite.listCard.name.prefix=工位名
i18n_entity.FbWorkSite.listCard.position.prefix=所属岗位
i18n_entity.HaiMockRobot.fields.battery.label=电量
i18n_entity.HaiMockRobot.fields.createdBy.label=创建人
i18n_entity.HaiMockRobot.fields.createdOn.label=创建时间
i18n_entity.HaiMockRobot.fields.id.label=ID
i18n_entity.HaiMockRobot.fields.modifiedBy.label=最后修改人
i18n_entity.HaiMockRobot.fields.modifiedOn.label=最后修改时间
i18n_entity.HaiMockRobot.fields.posX.label=位置 X
i18n_entity.HaiMockRobot.fields.posY.label=位置 Y
i18n_entity.HaiMockRobot.fields.version.label=修订版本
i18n_entity.HaiMockRobot.group=WCS
i18n_entity.HaiMockRobot.label=海柔仿真机器人
i18n_entity.HaiMockRobot.listCard.battery.prefix=电量
i18n_entity.HaiMockRobot.listCard.posX.prefix=位置 X
i18n_entity.HaiMockRobot.listCard.posY.prefix=位置 Y
i18n_entity.HikResourcePack.fields.active.label=主配置
i18n_entity.HikResourcePack.fields.createdBy.label=创建人
i18n_entity.HikResourcePack.fields.createdOn.label=创建时间
i18n_entity.HikResourcePack.fields.id.label=ID
i18n_entity.HikResourcePack.fields.lmap.label=激光点云文件 lmap
i18n_entity.HikResourcePack.fields.modifiedBy.label=最后修改人
i18n_entity.HikResourcePack.fields.modifiedOn.label=最后修改时间
i18n_entity.HikResourcePack.fields.podConfig.label=货架配置 XML
i18n_entity.HikResourcePack.fields.remark.label=备注
i18n_entity.HikResourcePack.fields.version.label=修订版本
i18n_entity.HikResourcePack.group=WCS
i18n_entity.HikResourcePack.label=海康资源包
i18n_entity.HikResourcePack.listCard.version.prefix=版本
i18n_entity.HumanUser.fields.btDisabled.label=停用
i18n_entity.HumanUser.fields.company.label=公司
i18n_entity.HumanUser.fields.createdBy.label=创建人
i18n_entity.HumanUser.fields.createdOn.label=创建时间
i18n_entity.HumanUser.fields.directSignInDisabled.label=禁止直接登录
i18n_entity.HumanUser.fields.disabled.label=禁用
i18n_entity.HumanUser.fields.email.label=邮件
i18n_entity.HumanUser.fields.externalAdded.label=外部添加
i18n_entity.HumanUser.fields.externalSource.label=外部来源
i18n_entity.HumanUser.fields.externalUserId.label=外部用户 ID
i18n_entity.HumanUser.fields.id.label=编号
i18n_entity.HumanUser.fields.modifiedBy.label=最后修改人
i18n_entity.HumanUser.fields.modifiedOn.label=最后修改时间
i18n_entity.HumanUser.fields.password.label=密码
i18n_entity.HumanUser.fields.phone.label=手机
i18n_entity.HumanUser.fields.pwdErrCount.label=密码错误计数
i18n_entity.HumanUser.fields.pwdSetOn.label=密码设置时间
i18n_entity.HumanUser.fields.ro.label=根组织
i18n_entity.HumanUser.fields.roAdmin.label=企业管理员
i18n_entity.HumanUser.fields.roleIds.label=角色
i18n_entity.HumanUser.fields.truename.label=真实姓名
i18n_entity.HumanUser.fields.username.label=用户名
i18n_entity.HumanUser.fields.version.label=版本
i18n_entity.HumanUser.group=User
i18n_entity.HumanUser.label=用户
i18n_entity.HumanUser.listCard.phone.prefix=手机
i18n_entity.HumanUser.listCard.truename.prefix=真实姓名
i18n_entity.HumanUserSession.fields.createdBy.label=创建人
i18n_entity.HumanUserSession.fields.createdOn.label=创建时间
i18n_entity.HumanUserSession.fields.expiredAt.label=过期时间
i18n_entity.HumanUserSession.fields.id.label=ID
i18n_entity.HumanUserSession.fields.modifiedBy.label=最后修改人
i18n_entity.HumanUserSession.fields.modifiedOn.label=最后修改时间
i18n_entity.HumanUserSession.fields.ro.label=RO
i18n_entity.HumanUserSession.fields.userId.label=用户
i18n_entity.HumanUserSession.fields.userToken.label=令牌
i18n_entity.HumanUserSession.fields.version.label=修订版本
i18n_entity.HumanUserSession.group=Core
i18n_entity.HumanUserSession.label=用户会话
i18n_entity.IdGen.fields.createdBy.label=创建人
i18n_entity.IdGen.fields.createdOn.label=创建时间
i18n_entity.IdGen.fields.flowNo.label=流水号
i18n_entity.IdGen.fields.id.label=编号
i18n_entity.IdGen.fields.key.label=组
i18n_entity.IdGen.fields.modifiedBy.label=最后修改人
i18n_entity.IdGen.fields.modifiedOn.label=最后修改时间
i18n_entity.IdGen.fields.ro.label=根组织
i18n_entity.IdGen.fields.timestamp.label=日期
i18n_entity.IdGen.fields.version.label=版本
i18n_entity.IdGen.group=Core
i18n_entity.IdGen.label=编号规则
i18n_entity.LePage.fields.content.label=内容
i18n_entity.LePage.fields.createdBy.label=创建人
i18n_entity.LePage.fields.createdOn.label=创建时间
i18n_entity.LePage.fields.id.label=ID
i18n_entity.LePage.fields.label.label=显示名
i18n_entity.LePage.fields.modifiedBy.label=最后修改人
i18n_entity.LePage.fields.modifiedOn.label=最后修改时间
i18n_entity.LePage.fields.name.label=页面名
i18n_entity.LePage.fields.version.label=修订版本
i18n_entity.LePage.group=Core
i18n_entity.LePage.label=定制界面
i18n_entity.ListFilterCase.fields.content.label=内容
i18n_entity.ListFilterCase.fields.createdBy.label=创建人
i18n_entity.ListFilterCase.fields.createdOn.label=创建时间
i18n_entity.ListFilterCase.fields.global.label=全局
i18n_entity.ListFilterCase.fields.id.label=编号
i18n_entity.ListFilterCase.fields.modifiedBy.label=最后修改人
i18n_entity.ListFilterCase.fields.modifiedOn.label=最后修改时间
i18n_entity.ListFilterCase.fields.owner.label=所有者
i18n_entity.ListFilterCase.fields.page.label=页面
i18n_entity.ListFilterCase.fields.ro.label=根组织
i18n_entity.ListFilterCase.fields.version.label=版本
i18n_entity.ListFilterCase.group=Core
i18n_entity.ListFilterCase.label=列表查询方案
i18n_entity.MockSeerRobot.fields.createdBy.label=创建人
i18n_entity.MockSeerRobot.fields.createdOn.label=创建时间
i18n_entity.MockSeerRobot.fields.currentStation.label=当前站点
i18n_entity.MockSeerRobot.fields.id.label=ID
i18n_entity.MockSeerRobot.fields.modifiedBy.label=最后修改人
i18n_entity.MockSeerRobot.fields.modifiedOn.label=最后修改时间
i18n_entity.MockSeerRobot.fields.staringStation.label=出生站点
i18n_entity.MockSeerRobot.fields.version.label=修订版本
i18n_entity.MockSeerRobot.group=WCS
i18n_entity.MockSeerRobot.label=仿真仙工机器人
i18n_entity.MockSeerRobot.listCard.createdOn.prefix=创建
i18n_entity.MockSeerRobot.listCard.staringStation.prefix=出生站点
i18n_entity.MrRobotRuntimeRecord.fields.channel.label=所在巷道
i18n_entity.MrRobotRuntimeRecord.fields.createdBy.label=创建人
i18n_entity.MrRobotRuntimeRecord.fields.createdOn.label=创建时间
i18n_entity.MrRobotRuntimeRecord.fields.ctOrders.label=容器搬运单
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Charging.label=充电中
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Idle.label=空闲
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Inbound.label=入库中
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Outbound.label=出库中
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Parking.label=去停靠
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Tasking.label=执行运单
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.label=扩展任务状态
i18n_entity.MrRobotRuntimeRecord.fields.faultMsg.label=故障信息
i18n_entity.MrRobotRuntimeRecord.fields.id.label=ID
i18n_entity.MrRobotRuntimeRecord.fields.modifiedBy.label=最后修改人
i18n_entity.MrRobotRuntimeRecord.fields.modifiedOn.label=最后修改时间
i18n_entity.MrRobotRuntimeRecord.fields.offDuty.label=不接单
i18n_entity.MrRobotRuntimeRecord.fields.port.label=所在库口
i18n_entity.MrRobotRuntimeRecord.fields.rtBins.label=实时调度载货情况
i18n_entity.MrRobotRuntimeRecord.fields.rtCmdStatus.label=实时调度执行状态
i18n_entity.MrRobotRuntimeRecord.fields.rtCurrentOrder.label=实时调度当前运单
i18n_entity.MrRobotRuntimeRecord.fields.rtCurrentStep.label=实时调度当前运单步骤
i18n_entity.MrRobotRuntimeRecord.fields.rtOrders.label=实时调度运单列表
i18n_entity.MrRobotRuntimeRecord.fields.selectSameOrderId.label=必须指定此运单的步骤
i18n_entity.MrRobotRuntimeRecord.fields.taskStatus.inlineOptionBill.items.Charging.label=充电中
i18n_entity.MrRobotRuntimeRecord.fields.taskStatus.inlineOptionBill.items.Idle.label=空闲
i18n_entity.MrRobotRuntimeRecord.fields.taskStatus.inlineOptionBill.items.Tasking.label=任务中
i18n_entity.MrRobotRuntimeRecord.fields.taskStatus.label=任务状态
i18n_entity.MrRobotRuntimeRecord.fields.version.label=修订版本
i18n_entity.MrRobotRuntimeRecord.group=WCS
i18n_entity.MrRobotRuntimeRecord.label=移动机器人运行记录
i18n_entity.MrRobotRuntimeRecord.listCard.ctOrders.prefix=容器搬运单
i18n_entity.MrRobotRuntimeRecord.listCard.rtBins.prefix=货物
i18n_entity.MrRobotRuntimeRecord.listCard.rtCurrentOrder.prefix=当前运单
i18n_entity.MrRobotSystemConfig.fields.category.label=类别
i18n_entity.MrRobotSystemConfig.fields.connectionType.inlineOptionBill.items.GwWs.label=网关连接服务器（GW 信道）
i18n_entity.MrRobotSystemConfig.fields.connectionType.inlineOptionBill.items.GwWsLight.label=网关连接服务器（光通讯）
i18n_entity.MrRobotSystemConfig.fields.connectionType.inlineOptionBill.items.Mock.label=仿真
i18n_entity.MrRobotSystemConfig.fields.connectionType.inlineOptionBill.items.Rbk.label=服务器连接机器人
i18n_entity.MrRobotSystemConfig.fields.connectionType.label=连接类型
i18n_entity.MrRobotSystemConfig.fields.createdBy.label=创建人
i18n_entity.MrRobotSystemConfig.fields.createdOn.label=创建时间
i18n_entity.MrRobotSystemConfig.fields.disabled.label=停用
i18n_entity.MrRobotSystemConfig.fields.gwAuthId.label=网关登录ID
i18n_entity.MrRobotSystemConfig.fields.gwAuthSecret.label=网关登录秘钥
i18n_entity.MrRobotSystemConfig.fields.id.label=ID
i18n_entity.MrRobotSystemConfig.fields.image.label=图片
i18n_entity.MrRobotSystemConfig.fields.modifiedBy.label=最后修改人
i18n_entity.MrRobotSystemConfig.fields.modifiedOn.label=最后修改时间
i18n_entity.MrRobotSystemConfig.fields.offDuty.label=不接单
i18n_entity.MrRobotSystemConfig.fields.robotHost.label=机器人地址
i18n_entity.MrRobotSystemConfig.fields.scene.label=场景
i18n_entity.MrRobotSystemConfig.fields.selfBinNum.label=最大载货数
i18n_entity.MrRobotSystemConfig.fields.ssl.label=SSL/TSL 加密
i18n_entity.MrRobotSystemConfig.fields.tags.label=标签
i18n_entity.MrRobotSystemConfig.fields.taskMode.inlineOptionBill.items.Custom.label=定制调度
i18n_entity.MrRobotSystemConfig.fields.taskMode.inlineOptionBill.items.SingleRobot.label=M4A1
i18n_entity.MrRobotSystemConfig.fields.taskMode.inlineOptionBill.items.Tom.label=Core
i18n_entity.MrRobotSystemConfig.fields.taskMode.label=任务模式
i18n_entity.MrRobotSystemConfig.fields.tomId.label=仙工调度 ID
i18n_entity.MrRobotSystemConfig.fields.vendor.inlineOptionBill.items.Hai.label=海柔
i18n_entity.MrRobotSystemConfig.fields.vendor.inlineOptionBill.items.Hik.label=海康
i18n_entity.MrRobotSystemConfig.fields.vendor.inlineOptionBill.items.Seer.label=仙工
i18n_entity.MrRobotSystemConfig.fields.vendor.label=厂商
i18n_entity.MrRobotSystemConfig.fields.version.label=修订版本
i18n_entity.MrRobotSystemConfig.group=WCS
i18n_entity.MrRobotSystemConfig.label=移动机器人系统配置
i18n_entity.MrRobotSystemConfig.listCard.scene.prefix=场景
i18n_entity.MrRobotSystemConfig.listCard.selfBinNum.prefix=最多载货
i18n_entity.NdcOrder.fields.allowLoad.label=允许取货
i18n_entity.NdcOrder.fields.allowUnload.label=允许放货
i18n_entity.NdcOrder.fields.createdBy.label=创建人
i18n_entity.NdcOrder.fields.createdOn.label=创建时间
i18n_entity.NdcOrder.fields.endBin.label=终点
i18n_entity.NdcOrder.fields.falconId.label=猎鹰任务
i18n_entity.NdcOrder.fields.id.label=ID
i18n_entity.NdcOrder.fields.ikey.label=ikey
i18n_entity.NdcOrder.fields.index.label=index
i18n_entity.NdcOrder.fields.modifiedBy.label=最后修改人
i18n_entity.NdcOrder.fields.modifiedOn.label=最后修改时间
i18n_entity.NdcOrder.fields.priority.label=优先级
i18n_entity.NdcOrder.fields.startBin.label=起点
i18n_entity.NdcOrder.fields.status.label=状态
i18n_entity.NdcOrder.fields.version.label=版本
i18n_entity.NdcOrder.group=NDC
i18n_entity.NdcOrder.label=NDC 运单
i18n_entity.OrderFlowRecord.fields.createdBy.label=创建人
i18n_entity.OrderFlowRecord.fields.createdOn.label=创建时间
i18n_entity.OrderFlowRecord.fields.id.label=ID
i18n_entity.OrderFlowRecord.fields.modifiedBy.label=最后修改人
i18n_entity.OrderFlowRecord.fields.modifiedOn.label=最后修改时间
i18n_entity.OrderFlowRecord.fields.pushType.label=类型
i18n_entity.OrderFlowRecord.fields.sourceOrderId.label=源单号
i18n_entity.OrderFlowRecord.fields.sourceOrderName.label=源单名
i18n_entity.OrderFlowRecord.fields.targetOrderId.label=新单号
i18n_entity.OrderFlowRecord.fields.targetOrderName.label=新单名
i18n_entity.OrderFlowRecord.fields.targetOrderType.label=新单类型
i18n_entity.OrderFlowRecord.fields.txId.label=事务ID
i18n_entity.OrderFlowRecord.fields.version.label=修订版本
i18n_entity.OrderFlowRecord.group=Core
i18n_entity.OrderFlowRecord.label=单据流转记录
i18n_entity.PickOrder.fields.allUsed.label=整托分拣
i18n_entity.PickOrder.fields.btLines.label=单行
i18n_entity.PickOrder.fields.btOrderKind.inlineOptionBill.items.NormalTask.label=装货任务
i18n_entity.PickOrder.fields.btOrderKind.label=类型
i18n_entity.PickOrder.fields.btOrderState.inlineOptionBill.items.Done.label=完成分拣
i18n_entity.PickOrder.fields.btOrderState.inlineOptionBill.items.Todo.label=等待分拣
i18n_entity.PickOrder.fields.btOrderState.label=业务状态
i18n_entity.PickOrder.fields.btOrderStateReason.label=状态说明
i18n_entity.PickOrder.fields.container.label=容器
i18n_entity.PickOrder.fields.containerBackOrderId.label=容器回库运单号
i18n_entity.PickOrder.fields.containerOutDone.label=容器出库搬运完成
i18n_entity.PickOrder.fields.containerOutOrderId.label=容器出库运单号
i18n_entity.PickOrder.fields.createdBy.label=创建人
i18n_entity.PickOrder.fields.createdOn.label=创建时间
i18n_entity.PickOrder.fields.id.label=单号
i18n_entity.PickOrder.fields.modifiedBy.label=最后修改人
i18n_entity.PickOrder.fields.modifiedOn.label=最后修改时间
i18n_entity.PickOrder.fields.sourceOrderId.label=出库单号
i18n_entity.PickOrder.fields.submittedPostProcessed.label=后处理完成
i18n_entity.PickOrder.fields.version.label=修订版本
i18n_entity.PickOrder.group=Warehouse
i18n_entity.PickOrder.label=分拣单
i18n_entity.PickOrder.listCard.container.prefix=容器
i18n_entity.PickOrder.listCard.sourceOrderId.prefix=出库单
i18n_entity.PickOrder.states.states.Done.label=完成分拣
i18n_entity.PickOrder.states.states.Todo.label=等待分拣
i18n_entity.PickOrder.states.states.Todo.nextStates.Done.buttonLabel=完成
i18n_entity.PickOrderLine.fields.btLineNo.label=行号
i18n_entity.PickOrderLine.fields.btMaterial.label=物料
i18n_entity.PickOrderLine.fields.btMaterialCategory.label=物料分类编号
i18n_entity.PickOrderLine.fields.btMaterialCategoryName.label=物料分类名称
i18n_entity.PickOrderLine.fields.btMaterialId.label=物料编号
i18n_entity.PickOrderLine.fields.btMaterialImage.label=物料图片
i18n_entity.PickOrderLine.fields.btMaterialModel.label=物料型号
i18n_entity.PickOrderLine.fields.btMaterialName.label=物料名称
i18n_entity.PickOrderLine.fields.btMaterialSpec.label=物料规格
i18n_entity.PickOrderLine.fields.btParentId.label=所属单据
i18n_entity.PickOrderLine.fields.container.label=容器
i18n_entity.PickOrderLine.fields.createdBy.label=创建人
i18n_entity.PickOrderLine.fields.createdOn.label=创建时间
i18n_entity.PickOrderLine.fields.id.label=ID
i18n_entity.PickOrderLine.fields.lotNo.label=批次号
i18n_entity.PickOrderLine.fields.modifiedBy.label=最后修改人
i18n_entity.PickOrderLine.fields.modifiedOn.label=最后修改时间
i18n_entity.PickOrderLine.fields.planQty.label=期望拣出数
i18n_entity.PickOrderLine.fields.qty.label=实际拣出数
i18n_entity.PickOrderLine.fields.qualityLevel.label=质量等级
i18n_entity.PickOrderLine.fields.sourceLineId.label=来源单行 ID
i18n_entity.PickOrderLine.fields.sourceLineNo.label=来源行号
i18n_entity.PickOrderLine.fields.sourceOrderId.label=来源单据
i18n_entity.PickOrderLine.fields.subContainerId.label=格号
i18n_entity.PickOrderLine.fields.version.label=修订版本
i18n_entity.PickOrderLine.group=Warehouse
i18n_entity.PickOrderLine.label=分拣单行
i18n_entity.PickOrderLine.listCard.btMaterialId.prefix=物料编号
i18n_entity.PickOrderLine.listCard.lotNo.prefix=批次
i18n_entity.PickOrderLine.listCard.planQty.prefix=期望拣出
i18n_entity.PickOrderLine.listCard.qty.prefix=实际拣出
i18n_entity.PickOrderLine.listCard.subContainerId.prefix=格号
i18n_entity.PlcDeviceConfig.fields.autoRetry.label=自动重连
i18n_entity.PlcDeviceConfig.fields.createdBy.label=创建人
i18n_entity.PlcDeviceConfig.fields.createdOn.label=创建时间
i18n_entity.PlcDeviceConfig.fields.disabled.label=停用
i18n_entity.PlcDeviceConfig.fields.endpoint.label=连接字符串
i18n_entity.PlcDeviceConfig.fields.host.label=地址/IP
i18n_entity.PlcDeviceConfig.fields.id.label=名称（ID）
i18n_entity.PlcDeviceConfig.fields.maxRetry.label=最大重试次数
i18n_entity.PlcDeviceConfig.fields.modifiedBy.label=最后修改人
i18n_entity.PlcDeviceConfig.fields.modifiedOn.label=最后修改时间
i18n_entity.PlcDeviceConfig.fields.port.label=端口
i18n_entity.PlcDeviceConfig.fields.rack.label=S7 机架号
i18n_entity.PlcDeviceConfig.fields.retryDelay.label=重试等待（毫秒）
i18n_entity.PlcDeviceConfig.fields.slot.label=S7 槽号
i18n_entity.PlcDeviceConfig.fields.subType.label=子类型
i18n_entity.PlcDeviceConfig.fields.timeout.label=连接超时时间（毫秒）
i18n_entity.PlcDeviceConfig.fields.type.inlineOptionBill.items.Modbus.label=Modbus
i18n_entity.PlcDeviceConfig.fields.type.inlineOptionBill.items.OPCUA.label=OPCUA
i18n_entity.PlcDeviceConfig.fields.type.inlineOptionBill.items.S7.label=S7
i18n_entity.PlcDeviceConfig.fields.type.label=类型
i18n_entity.PlcDeviceConfig.fields.version.label=修订版本
i18n_entity.PlcDeviceConfig.group=WCS
i18n_entity.PlcDeviceConfig.label=PLC 设备配置
i18n_entity.PlcDeviceConfig.listCard.host.prefix=地址/IP
i18n_entity.PlcDeviceConfig.listCard.port.prefix=端口
i18n_entity.PlcRwLog.fields.action.label=操作
i18n_entity.PlcRwLog.fields.createdBy.label=创建人
i18n_entity.PlcRwLog.fields.createdOn.label=创建时间
i18n_entity.PlcRwLog.fields.deviceName.label=设备名
i18n_entity.PlcRwLog.fields.deviceType.label=设备类型
i18n_entity.PlcRwLog.fields.id.label=ID
i18n_entity.PlcRwLog.fields.ip.label=IP
i18n_entity.PlcRwLog.fields.modifiedBy.label=最后修改人
i18n_entity.PlcRwLog.fields.modifiedOn.label=最后修改时间
i18n_entity.PlcRwLog.fields.oldValueDesc.label=原值
i18n_entity.PlcRwLog.fields.reason.label=原因
i18n_entity.PlcRwLog.fields.rw.inlineOptionBill.items.Read.label=读
i18n_entity.PlcRwLog.fields.rw.inlineOptionBill.items.Write.label=写
i18n_entity.PlcRwLog.fields.rw.label=读写
i18n_entity.PlcRwLog.fields.valueDesc.label=值
i18n_entity.PlcRwLog.fields.version.label=修订版本
i18n_entity.PlcRwLog.group=WCS
i18n_entity.PlcRwLog.label=PLC 读写记录
i18n_entity.PlcRwLog.listCard.oldValueDesc.prefix=原值
i18n_entity.PlcRwLog.listCard.valueDesc.prefix=值
i18n_entity.PutinContainerOrder.fields.btLines.label=单行
i18n_entity.PutinContainerOrder.fields.btOrderKind.inlineOptionBill.items.NormalTask.label=装货任务
i18n_entity.PutinContainerOrder.fields.btOrderKind.label=类型
i18n_entity.PutinContainerOrder.fields.btOrderState.inlineOptionBill.items.Done.label=上架完成
i18n_entity.PutinContainerOrder.fields.btOrderState.inlineOptionBill.items.Filled.label=装入完成
i18n_entity.PutinContainerOrder.fields.btOrderState.inlineOptionBill.items.Todo.label=等待装入
i18n_entity.PutinContainerOrder.fields.btOrderState.label=业务状态
i18n_entity.PutinContainerOrder.fields.btOrderStateReason.label=状态说明
i18n_entity.PutinContainerOrder.fields.container.label=容器
i18n_entity.PutinContainerOrder.fields.containerBackOrderId.label=容器回库运单号
i18n_entity.PutinContainerOrder.fields.containerOutDone.label=容器出库搬运完成
i18n_entity.PutinContainerOrder.fields.containerOutOrderId.label=容器出库运单号
i18n_entity.PutinContainerOrder.fields.createdBy.label=创建人
i18n_entity.PutinContainerOrder.fields.createdOn.label=创建时间
i18n_entity.PutinContainerOrder.fields.id.label=单号
i18n_entity.PutinContainerOrder.fields.modifiedBy.label=最后修改人
i18n_entity.PutinContainerOrder.fields.modifiedOn.label=最后修改时间
i18n_entity.PutinContainerOrder.fields.sourceOrderId.label=入库单号
i18n_entity.PutinContainerOrder.fields.version.label=修订版本
i18n_entity.PutinContainerOrder.group=Warehouse
i18n_entity.PutinContainerOrder.label=装货单
i18n_entity.PutinContainerOrder.listCard.container.prefix=容器
i18n_entity.PutinContainerOrder.states.states.Done.label=上架完成
i18n_entity.PutinContainerOrder.states.states.Filled.label=装入完成
i18n_entity.PutinContainerOrder.states.states.Todo.label=等待装入
i18n_entity.PutinContainerOrder.states.states.Todo.nextStates.Filled.buttonLabel=完成
i18n_entity.PutinContainerOrderLine.fields.btLineNo.label=行号
i18n_entity.PutinContainerOrderLine.fields.btMaterial.label=物料
i18n_entity.PutinContainerOrderLine.fields.btMaterialCategory.label=物料分类编号
i18n_entity.PutinContainerOrderLine.fields.btMaterialCategoryName.label=物料分类名称
i18n_entity.PutinContainerOrderLine.fields.btMaterialId.label=物料编号
i18n_entity.PutinContainerOrderLine.fields.btMaterialImage.label=物料图片
i18n_entity.PutinContainerOrderLine.fields.btMaterialModel.label=物料型号
i18n_entity.PutinContainerOrderLine.fields.btMaterialName.label=物料名称
i18n_entity.PutinContainerOrderLine.fields.btMaterialSpec.label=物料规格
i18n_entity.PutinContainerOrderLine.fields.btParentId.label=所属单据
i18n_entity.PutinContainerOrderLine.fields.container.label=容器
i18n_entity.PutinContainerOrderLine.fields.createdBy.label=创建人
i18n_entity.PutinContainerOrderLine.fields.createdOn.label=创建时间
i18n_entity.PutinContainerOrderLine.fields.id.label=ID
i18n_entity.PutinContainerOrderLine.fields.lotNo.label=批次号
i18n_entity.PutinContainerOrderLine.fields.modifiedBy.label=最后修改人
i18n_entity.PutinContainerOrderLine.fields.modifiedOn.label=最后修改时间
i18n_entity.PutinContainerOrderLine.fields.planQty.label=计划装入数
i18n_entity.PutinContainerOrderLine.fields.price.label=单价
i18n_entity.PutinContainerOrderLine.fields.qty.label=实际装入数
i18n_entity.PutinContainerOrderLine.fields.qualityLevel.label=质量等级
i18n_entity.PutinContainerOrderLine.fields.sourceLineId.label=来源单行 ID
i18n_entity.PutinContainerOrderLine.fields.sourceLineNo.label=来源行号
i18n_entity.PutinContainerOrderLine.fields.sourceOrderId.label=来源单据
i18n_entity.PutinContainerOrderLine.fields.subContainerId.label=格号
i18n_entity.PutinContainerOrderLine.fields.unitLabel.label=单位名称
i18n_entity.PutinContainerOrderLine.fields.version.label=修订版本
i18n_entity.PutinContainerOrderLine.group=Warehouse
i18n_entity.PutinContainerOrderLine.label=装货单行
i18n_entity.PutinContainerOrderLine.listCard.btMaterialId.prefix=物料编号
i18n_entity.PutinContainerOrderLine.listCard.btMaterialName.prefix=物料名称
i18n_entity.PutinContainerOrderLine.listCard.lotNo.prefix=批次号
i18n_entity.PutinContainerOrderLine.listCard.planQty.prefix=计划装入
i18n_entity.PutinContainerOrderLine.listCard.qty.prefix=实际装入
i18n_entity.PutinContainerOrderLine.listCard.subContainerId.prefix=格号
i18n_entity.QsInboundOrder.fields.btBzKind.inlineOptionBill.items.Normal.label=普通入库
i18n_entity.QsInboundOrder.fields.btBzKind.inlineOptionBill.items.Other.label=其他入库
i18n_entity.QsInboundOrder.fields.btBzKind.label=业务类型
i18n_entity.QsInboundOrder.fields.btLines.label=单行
i18n_entity.QsInboundOrder.fields.btOrderState.inlineOptionBill.items.Cancelled.label=已取消
i18n_entity.QsInboundOrder.fields.btOrderState.inlineOptionBill.items.Committed.label=已提交
i18n_entity.QsInboundOrder.fields.btOrderState.inlineOptionBill.items.Init.label=未提交
i18n_entity.QsInboundOrder.fields.btOrderState.label=业务状态
i18n_entity.QsInboundOrder.fields.btOrderStateReason.label=状态说明
i18n_entity.QsInboundOrder.fields.callContainerAll.label=叫空容器分配完成
i18n_entity.QsInboundOrder.fields.createdBy.label=创建人
i18n_entity.QsInboundOrder.fields.createdOn.label=创建时间
i18n_entity.QsInboundOrder.fields.id.label=单号
i18n_entity.QsInboundOrder.fields.modifiedBy.label=最后修改人
i18n_entity.QsInboundOrder.fields.modifiedOn.label=最后修改时间
i18n_entity.QsInboundOrder.fields.otherType.label=其他类型
i18n_entity.QsInboundOrder.fields.priority.label=优先级
i18n_entity.QsInboundOrder.fields.remark.label=备注
i18n_entity.QsInboundOrder.fields.ro.label=根组织
i18n_entity.QsInboundOrder.fields.version.label=版本
i18n_entity.QsInboundOrder.group=Quick Store
i18n_entity.QsInboundOrder.kinds.kinds.Normal.label=普通入库
i18n_entity.QsInboundOrder.kinds.kinds.Other.label=其他入库
i18n_entity.QsInboundOrder.label=QS 入库单
i18n_entity.QsInboundOrder.states.states.Cancelled.label=已取消
i18n_entity.QsInboundOrder.states.states.Committed.label=已提交
i18n_entity.QsInboundOrder.states.states.Init.label=未提交
i18n_entity.QsInboundOrder.states.states.Init.nextStates.Cancelled.buttonLabel=取消
i18n_entity.QsInboundOrder.states.states.Init.nextStates.Committed.buttonLabel=提交
i18n_entity.QsInboundOrderLine.fields.btLineNo.label=行号
i18n_entity.QsInboundOrderLine.fields.btMaterial.label=物料
i18n_entity.QsInboundOrderLine.fields.btMaterialCategory.label=物料分类编号
i18n_entity.QsInboundOrderLine.fields.btMaterialCategoryName.label=物料分类名称
i18n_entity.QsInboundOrderLine.fields.btMaterialId.label=物料编号
i18n_entity.QsInboundOrderLine.fields.btMaterialImage.label=物料图片
i18n_entity.QsInboundOrderLine.fields.btMaterialModel.label=物料型号
i18n_entity.QsInboundOrderLine.fields.btMaterialName.label=物料名称
i18n_entity.QsInboundOrderLine.fields.btMaterialSpec.label=物料规格
i18n_entity.QsInboundOrderLine.fields.btParentId.label=所属出库单
i18n_entity.QsInboundOrderLine.fields.ccQty.label=叫空容器分配数量
i18n_entity.QsInboundOrderLine.fields.createdBy.label=创建人
i18n_entity.QsInboundOrderLine.fields.createdOn.label=创建时间
i18n_entity.QsInboundOrderLine.fields.id.label=ID
i18n_entity.QsInboundOrderLine.fields.lotNo.label=批次号
i18n_entity.QsInboundOrderLine.fields.modifiedBy.label=最后修改人
i18n_entity.QsInboundOrderLine.fields.modifiedOn.label=最后修改时间
i18n_entity.QsInboundOrderLine.fields.priority.label=优先级
i18n_entity.QsInboundOrderLine.fields.qty.label=入库数量
i18n_entity.QsInboundOrderLine.fields.ro.label=根组织
i18n_entity.QsInboundOrderLine.fields.version.label=版本
i18n_entity.QsInboundOrderLine.group=Quick Store
i18n_entity.QsInboundOrderLine.label=QS 入库单行
i18n_entity.QsInboundOrderLine.listCard.qty.prefix=数量
i18n_entity.QsMoveBinOrder.fields.actualToBin.label=实际终点库位
i18n_entity.QsMoveBinOrder.fields.btBzKind.inlineOptionBill.items.Normal.label=默认
i18n_entity.QsMoveBinOrder.fields.btBzKind.label=业务类型
i18n_entity.QsMoveBinOrder.fields.container.label=容器
i18n_entity.QsMoveBinOrder.fields.createdBy.label=创建人
i18n_entity.QsMoveBinOrder.fields.createdOn.label=创建时间
i18n_entity.QsMoveBinOrder.fields.expectToBin.label=指定终点库位
i18n_entity.QsMoveBinOrder.fields.expectToDistrict.label=指定终点库区
i18n_entity.QsMoveBinOrder.fields.fromBin.label=起点库位
i18n_entity.QsMoveBinOrder.fields.id.label=ID
i18n_entity.QsMoveBinOrder.fields.modifiedBy.label=最后修改人
i18n_entity.QsMoveBinOrder.fields.modifiedOn.label=最后修改时间
i18n_entity.QsMoveBinOrder.fields.oldInvLines.label=移动时容器内的库存明细
i18n_entity.QsMoveBinOrder.fields.remark.label=说明
i18n_entity.QsMoveBinOrder.fields.version.label=修订版本
i18n_entity.QsMoveBinOrder.group=Quick Store
i18n_entity.QsMoveBinOrder.kinds.kinds.Normal.label=默认
i18n_entity.QsMoveBinOrder.label=QS 移库单
i18n_entity.QsMoveBinOrder.listCard.container.prefix=容器
i18n_entity.QsMoveBinOrder.listCard.fromBin.suffix=->
i18n_entity.QsOldInvLine.fields.amount.label=金额
i18n_entity.QsOldInvLine.fields.btLineNo.label=行号
i18n_entity.QsOldInvLine.fields.btMaterial.label=物料
i18n_entity.QsOldInvLine.fields.btMaterialCategory.label=物料分类编号
i18n_entity.QsOldInvLine.fields.btMaterialCategoryName.label=物料分类名称
i18n_entity.QsOldInvLine.fields.btMaterialImage.label=物料图片
i18n_entity.QsOldInvLine.fields.btMaterialModel.label=物料型号
i18n_entity.QsOldInvLine.fields.btMaterialName.label=物料名称
i18n_entity.QsOldInvLine.fields.btMaterialSpec.label=物料规格
i18n_entity.QsOldInvLine.fields.btParentId.label=所属订单
i18n_entity.QsOldInvLine.fields.createdBy.label=创建人
i18n_entity.QsOldInvLine.fields.createdOn.label=创建时间
i18n_entity.QsOldInvLine.fields.expDate.label=有效期
i18n_entity.QsOldInvLine.fields.id.label=ID
i18n_entity.QsOldInvLine.fields.inboundOn.label=入库时间
i18n_entity.QsOldInvLine.fields.leafContainer.label=最内层容器
i18n_entity.QsOldInvLine.fields.lotNo.label=批次号
i18n_entity.QsOldInvLine.fields.matLotNo.label=批次
i18n_entity.QsOldInvLine.fields.matSerialNo.label=序列号
i18n_entity.QsOldInvLine.fields.mfgDate.label=生产日期
i18n_entity.QsOldInvLine.fields.modifiedBy.label=最后修改人
i18n_entity.QsOldInvLine.fields.modifiedOn.label=最后修改时间
i18n_entity.QsOldInvLine.fields.owner.label=货主
i18n_entity.QsOldInvLine.fields.price.label=单价
i18n_entity.QsOldInvLine.fields.qty.label=数量
i18n_entity.QsOldInvLine.fields.refInvId.label=关联库存明细 ID
i18n_entity.QsOldInvLine.fields.subContainerId.label=格子
i18n_entity.QsOldInvLine.fields.topContainer.label=最外层容器
i18n_entity.QsOldInvLine.fields.vendor.label=供应商
i18n_entity.QsOldInvLine.fields.version.label=修订版本
i18n_entity.QsOldInvLine.group=Quick Store
i18n_entity.QsOldInvLine.label=QS 现有库存明细
i18n_entity.QsOldInvLine.listStats.items[0].label=处理中（锁定）
i18n_entity.QsOutboundOrder.fields.btLines.label=单行
i18n_entity.QsOutboundOrder.fields.btOrderKind.inlineOptionBill.items.Default.label=默认
i18n_entity.QsOutboundOrder.fields.btOrderKind.label=类型
i18n_entity.QsOutboundOrder.fields.btOrderState.inlineOptionBill.items.Committed.label=已提交
i18n_entity.QsOutboundOrder.fields.btOrderState.inlineOptionBill.items.Init.label=未提交
i18n_entity.QsOutboundOrder.fields.btOrderState.label=业务状态
i18n_entity.QsOutboundOrder.fields.btOrderStateReason.label=状态说明
i18n_entity.QsOutboundOrder.fields.createdBy.label=创建人
i18n_entity.QsOutboundOrder.fields.createdOn.label=创建时间
i18n_entity.QsOutboundOrder.fields.id.label=单号
i18n_entity.QsOutboundOrder.fields.invAssignedAll.label=库存分配完成
i18n_entity.QsOutboundOrder.fields.modifiedBy.label=最后修改人
i18n_entity.QsOutboundOrder.fields.modifiedOn.label=最后修改时间
i18n_entity.QsOutboundOrder.fields.priority.label=优先级
i18n_entity.QsOutboundOrder.fields.remark.label=备注
i18n_entity.QsOutboundOrder.fields.ro.label=根组织
i18n_entity.QsOutboundOrder.fields.typePriority.label=类型优先级
i18n_entity.QsOutboundOrder.fields.version.label=版本
i18n_entity.QsOutboundOrder.group=Quick Store
i18n_entity.QsOutboundOrder.label=QS 出库单
i18n_entity.QsOutboundOrder.states.states.Committed.label=已提交
i18n_entity.QsOutboundOrder.states.states.Init.label=未提交
i18n_entity.QsOutboundOrder.states.states.Init.nextStates.Committed.buttonLabel=提交
i18n_entity.QsOutboundOrderLine.fields.btLineNo.label=行号
i18n_entity.QsOutboundOrderLine.fields.btMaterial.label=物料
i18n_entity.QsOutboundOrderLine.fields.btMaterialCategory.label=物料分类编号
i18n_entity.QsOutboundOrderLine.fields.btMaterialCategoryName.label=物料分类名称
i18n_entity.QsOutboundOrderLine.fields.btMaterialId.label=物料编号
i18n_entity.QsOutboundOrderLine.fields.btMaterialImage.label=物料图片
i18n_entity.QsOutboundOrderLine.fields.btMaterialModel.label=物料型号
i18n_entity.QsOutboundOrderLine.fields.btMaterialName.label=物料名称
i18n_entity.QsOutboundOrderLine.fields.btMaterialSpec.label=物料规格
i18n_entity.QsOutboundOrderLine.fields.btParentId.label=所属出库单
i18n_entity.QsOutboundOrderLine.fields.createdBy.label=创建人
i18n_entity.QsOutboundOrderLine.fields.createdOn.label=创建时间
i18n_entity.QsOutboundOrderLine.fields.id.label=ID
i18n_entity.QsOutboundOrderLine.fields.invAssignedQty.label=已分配库存数量
i18n_entity.QsOutboundOrderLine.fields.lotNo.label=批次号
i18n_entity.QsOutboundOrderLine.fields.modifiedBy.label=最后修改人
i18n_entity.QsOutboundOrderLine.fields.modifiedOn.label=最后修改时间
i18n_entity.QsOutboundOrderLine.fields.priority.label=优先级
i18n_entity.QsOutboundOrderLine.fields.qty.label=出库数量
i18n_entity.QsOutboundOrderLine.fields.ro.label=根组织
i18n_entity.QsOutboundOrderLine.fields.version.label=版本
i18n_entity.QsOutboundOrderLine.group=Quick Store
i18n_entity.QsOutboundOrderLine.label=QS 出库单行
i18n_entity.QsOutboundOrderLine.listCard.qty.prefix=出库数量
i18n_entity.QsPickOrder.fields.btLines.label=单行
i18n_entity.QsPickOrder.fields.btOrderKind.inlineOptionBill.items.NormalTask.label=装货任务
i18n_entity.QsPickOrder.fields.btOrderKind.label=类型
i18n_entity.QsPickOrder.fields.btOrderState.inlineOptionBill.items.Done.label=拣货完成
i18n_entity.QsPickOrder.fields.btOrderState.inlineOptionBill.items.Todo.label=待拣货
i18n_entity.QsPickOrder.fields.btOrderState.label=业务状态
i18n_entity.QsPickOrder.fields.btOrderStateReason.label=状态说明
i18n_entity.QsPickOrder.fields.container.label=容器
i18n_entity.QsPickOrder.fields.createdBy.label=创建人
i18n_entity.QsPickOrder.fields.createdOn.label=创建时间
i18n_entity.QsPickOrder.fields.id.label=单号
i18n_entity.QsPickOrder.fields.modifiedBy.label=最后修改人
i18n_entity.QsPickOrder.fields.modifiedOn.label=最后修改时间
i18n_entity.QsPickOrder.fields.targetBin.label=目标库位
i18n_entity.QsPickOrder.fields.version.label=修订版本
i18n_entity.QsPickOrder.group=Quick Store
i18n_entity.QsPickOrder.label=QS 拣货单
i18n_entity.QsPickOrder.listCard.container.prefix=容器
i18n_entity.QsPickOrder.states.states.Done.label=拣货完成
i18n_entity.QsPickOrder.states.states.Todo.label=待拣货
i18n_entity.QsPickOrder.states.states.Todo.nextStates.Done.buttonLabel=完成
i18n_entity.QsPickOrderLine.fields.btLineNo.label=行号
i18n_entity.QsPickOrderLine.fields.btMaterial.label=物料
i18n_entity.QsPickOrderLine.fields.btMaterialCategory.label=物料分类编号
i18n_entity.QsPickOrderLine.fields.btMaterialCategoryName.label=物料分类名称
i18n_entity.QsPickOrderLine.fields.btMaterialId.label=物料编号
i18n_entity.QsPickOrderLine.fields.btMaterialImage.label=物料图片
i18n_entity.QsPickOrderLine.fields.btMaterialModel.label=物料型号
i18n_entity.QsPickOrderLine.fields.btMaterialName.label=物料名称
i18n_entity.QsPickOrderLine.fields.btMaterialSpec.label=物料规格
i18n_entity.QsPickOrderLine.fields.btParentId.label=所属单据
i18n_entity.QsPickOrderLine.fields.createdBy.label=创建人
i18n_entity.QsPickOrderLine.fields.createdOn.label=创建时间
i18n_entity.QsPickOrderLine.fields.id.label=ID
i18n_entity.QsPickOrderLine.fields.invLayoutId.label=库存明细 ID
i18n_entity.QsPickOrderLine.fields.lotNo.label=批次号
i18n_entity.QsPickOrderLine.fields.modifiedBy.label=最后修改人
i18n_entity.QsPickOrderLine.fields.modifiedOn.label=最后修改时间
i18n_entity.QsPickOrderLine.fields.outboundLineId.label=出库单行 ID
i18n_entity.QsPickOrderLine.fields.outboundLineNo.label=出库单行行号
i18n_entity.QsPickOrderLine.fields.outboundOrderId.label=出库单号
i18n_entity.QsPickOrderLine.fields.planQty.label=期望拣出数
i18n_entity.QsPickOrderLine.fields.qty.label=实际拣出数
i18n_entity.QsPickOrderLine.fields.subContainerId.label=格号
i18n_entity.QsPickOrderLine.fields.topContainer.label=容器
i18n_entity.QsPickOrderLine.fields.version.label=修订版本
i18n_entity.QsPickOrderLine.group=Quick Store
i18n_entity.QsPickOrderLine.label=QS 拣货单行
i18n_entity.QsPickOrderLine.listCard.planQty.prefix=期望数量
i18n_entity.QsPickOrderLine.listCard.qty.prefix=实际数量
i18n_entity.QsPutOnContainerOrder.fields.bin.label=上架库位
i18n_entity.QsPutOnContainerOrder.fields.container.label=容器
i18n_entity.QsPutOnContainerOrder.fields.createdBy.label=创建人
i18n_entity.QsPutOnContainerOrder.fields.createdOn.label=创建时间
i18n_entity.QsPutOnContainerOrder.fields.id.label=ID
i18n_entity.QsPutOnContainerOrder.fields.modifiedBy.label=最后修改人
i18n_entity.QsPutOnContainerOrder.fields.modifiedOn.label=最后修改时间
i18n_entity.QsPutOnContainerOrder.fields.oldInvLines.label=上架时容器内的库存明细
i18n_entity.QsPutOnContainerOrder.fields.version.label=修订版本
i18n_entity.QsPutOnContainerOrder.group=Quick Store
i18n_entity.QsPutOnContainerOrder.label=QS 人工上架单
i18n_entity.QsPutOnContainerOrder.listCard.bin.prefix=上架库位
i18n_entity.QsPutOrder.fields.bin.label=上架库位
i18n_entity.QsPutOrder.fields.btLines.label=单行
i18n_entity.QsPutOrder.fields.btOrderKind.inlineOptionBill.items.NormalTask.label=装货任务
i18n_entity.QsPutOrder.fields.btOrderKind.label=类型
i18n_entity.QsPutOrder.fields.btOrderState.inlineOptionBill.items.Done.label=装货完成
i18n_entity.QsPutOrder.fields.btOrderState.inlineOptionBill.items.Todo.label=待装货
i18n_entity.QsPutOrder.fields.btOrderState.label=业务状态
i18n_entity.QsPutOrder.fields.btOrderStateReason.label=状态说明
i18n_entity.QsPutOrder.fields.container.label=容器
i18n_entity.QsPutOrder.fields.createdBy.label=创建人
i18n_entity.QsPutOrder.fields.createdOn.label=创建时间
i18n_entity.QsPutOrder.fields.id.label=单号
i18n_entity.QsPutOrder.fields.modifiedBy.label=最后修改人
i18n_entity.QsPutOrder.fields.modifiedOn.label=最后修改时间
i18n_entity.QsPutOrder.fields.oldInvLines.label=容器内原有库存明细
i18n_entity.QsPutOrder.fields.targetBin.label=目标库位
i18n_entity.QsPutOrder.fields.version.label=修订版本
i18n_entity.QsPutOrder.group=Quick Store
i18n_entity.QsPutOrder.label=QS 装货单
i18n_entity.QsPutOrder.listCard.bin.prefix=上架库位
i18n_entity.QsPutOrder.listCard.container.prefix=容器
i18n_entity.QsPutOrder.states.states.Done.label=装货完成
i18n_entity.QsPutOrder.states.states.Todo.label=待装货
i18n_entity.QsPutOrder.states.states.Todo.nextStates.Done.buttonLabel=完成
i18n_entity.QsPutOrderLine.fields.btLineNo.label=行号
i18n_entity.QsPutOrderLine.fields.btMaterial.label=物料
i18n_entity.QsPutOrderLine.fields.btMaterialCategory.label=物料分类编号
i18n_entity.QsPutOrderLine.fields.btMaterialCategoryName.label=物料分类名称
i18n_entity.QsPutOrderLine.fields.btMaterialId.label=物料编号
i18n_entity.QsPutOrderLine.fields.btMaterialImage.label=物料图片
i18n_entity.QsPutOrderLine.fields.btMaterialModel.label=物料型号
i18n_entity.QsPutOrderLine.fields.btMaterialName.label=物料名称
i18n_entity.QsPutOrderLine.fields.btMaterialSpec.label=物料规格
i18n_entity.QsPutOrderLine.fields.btParentId.label=所属单据
i18n_entity.QsPutOrderLine.fields.createdBy.label=创建人
i18n_entity.QsPutOrderLine.fields.createdOn.label=创建时间
i18n_entity.QsPutOrderLine.fields.id.label=ID
i18n_entity.QsPutOrderLine.fields.lotNo.label=批次号
i18n_entity.QsPutOrderLine.fields.modifiedBy.label=最后修改人
i18n_entity.QsPutOrderLine.fields.modifiedOn.label=最后修改时间
i18n_entity.QsPutOrderLine.fields.planQty.label=期望装货数量
i18n_entity.QsPutOrderLine.fields.qty.label=实际装货数量
i18n_entity.QsPutOrderLine.fields.subContainerId.label=格号
i18n_entity.QsPutOrderLine.fields.version.label=修订版本
i18n_entity.QsPutOrderLine.group=Quick Store
i18n_entity.QsPutOrderLine.label=QS 装货单行
i18n_entity.QsPutOrderLine.listCard.planQty.prefix=期望装货
i18n_entity.QsPutOrderLine.listCard.qty.prefix=实际装货
i18n_entity.QsTakeOffContainerOrder.fields.bin.label=下架库位
i18n_entity.QsTakeOffContainerOrder.fields.container.label=容器
i18n_entity.QsTakeOffContainerOrder.fields.createdBy.label=创建人
i18n_entity.QsTakeOffContainerOrder.fields.createdOn.label=创建时间
i18n_entity.QsTakeOffContainerOrder.fields.id.label=ID
i18n_entity.QsTakeOffContainerOrder.fields.keepInv.label=保留库存
i18n_entity.QsTakeOffContainerOrder.fields.modifiedBy.label=最后修改人
i18n_entity.QsTakeOffContainerOrder.fields.modifiedOn.label=最后修改时间
i18n_entity.QsTakeOffContainerOrder.fields.oldInvLines.label=下架时容器内的库存明细
i18n_entity.QsTakeOffContainerOrder.fields.version.label=修订版本
i18n_entity.QsTakeOffContainerOrder.group=Quick Store
i18n_entity.QsTakeOffContainerOrder.label=QS 人工下架单
i18n_entity.QsTakeOffContainerOrder.listCard.bin.prefix=下架库位
i18n_entity.RaSingleBatteryRecord.fields.batteryLevel.label=电量
i18n_entity.RaSingleBatteryRecord.fields.batteryTemp.label=电池温度
i18n_entity.RaSingleBatteryRecord.fields.charging.label=是否充电
i18n_entity.RaSingleBatteryRecord.fields.createdBy.label=创建人
i18n_entity.RaSingleBatteryRecord.fields.createdOn.label=创建时间
i18n_entity.RaSingleBatteryRecord.fields.current.label=电流
i18n_entity.RaSingleBatteryRecord.fields.id.label=编号
i18n_entity.RaSingleBatteryRecord.fields.modifiedBy.label=最后修改人
i18n_entity.RaSingleBatteryRecord.fields.modifiedOn.label=最后修改时间
i18n_entity.RaSingleBatteryRecord.fields.robotName.label=机器人名称
i18n_entity.RaSingleBatteryRecord.fields.version.label=修订版本
i18n_entity.RaSingleBatteryRecord.fields.voltage.label=电压
i18n_entity.RaSingleBatteryRecord.group=RaSingle
i18n_entity.RaSingleBatteryRecord.label=机器人电池记录
i18n_entity.RaSingleNavigateRecord.fields.cost.label=花费时长
i18n_entity.RaSingleNavigateRecord.fields.createdBy.label=创建人
i18n_entity.RaSingleNavigateRecord.fields.createdOn.label=创建时间
i18n_entity.RaSingleNavigateRecord.fields.id.label=编号
i18n_entity.RaSingleNavigateRecord.fields.modifiedBy.label=最后修改人
i18n_entity.RaSingleNavigateRecord.fields.modifiedOn.label=最后修改时间
i18n_entity.RaSingleNavigateRecord.fields.robotName.label=机器人名称
i18n_entity.RaSingleNavigateRecord.fields.targetId.label=目标站点
i18n_entity.RaSingleNavigateRecord.fields.targetPoint.label=目标坐标点
i18n_entity.RaSingleNavigateRecord.fields.taskId.label=导航 ID
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.0.label=NONE
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.1.label=WAITING
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.2.label=RUNNING
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.3.label=SUSPENDED
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.4.label=COMPLETED
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.5.label=FAILED
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.6.label=CANCELED
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.label=导航状态
i18n_entity.RaSingleNavigateRecord.fields.taskType.inlineOptionBill.items.0.label=没有导航
i18n_entity.RaSingleNavigateRecord.fields.taskType.inlineOptionBill.items.1.label=自由导航到任意点
i18n_entity.RaSingleNavigateRecord.fields.taskType.inlineOptionBill.items.100.label=其他
i18n_entity.RaSingleNavigateRecord.fields.taskType.inlineOptionBill.items.2.label=自由导航到站点
i18n_entity.RaSingleNavigateRecord.fields.taskType.inlineOptionBill.items.3.label=路径导航到站点
i18n_entity.RaSingleNavigateRecord.fields.taskType.inlineOptionBill.items.7.label=平动转动
i18n_entity.RaSingleNavigateRecord.fields.taskType.label=导航类型
i18n_entity.RaSingleNavigateRecord.fields.version.label=修订版本
i18n_entity.RaSingleNavigateRecord.group=RaSingle
i18n_entity.RaSingleNavigateRecord.label=机器人导航记录
i18n_entity.ResourceLock.fields.createdBy.label=创建人
i18n_entity.ResourceLock.fields.createdOn.label=创建时间
i18n_entity.ResourceLock.fields.id.label=ID
i18n_entity.ResourceLock.fields.lockOn.label=锁定时间
i18n_entity.ResourceLock.fields.locked.label=锁定
i18n_entity.ResourceLock.fields.modifiedBy.label=最后修改人
i18n_entity.ResourceLock.fields.modifiedOn.label=修改时间
i18n_entity.ResourceLock.fields.owner.label=锁定者
i18n_entity.ResourceLock.fields.reason.label=锁定原因
i18n_entity.ResourceLock.fields.resId.label=资源 ID
i18n_entity.ResourceLock.fields.resType.label=资源类型
i18n_entity.ResourceLock.fields.version.label=修订版本
i18n_entity.ResourceLock.group=Core
i18n_entity.ResourceLock.label=资源锁
i18n_entity.RobotConnectedPoint.fields.bin.label=关联库位
i18n_entity.RobotConnectedPoint.fields.channel.label=所在巷道
i18n_entity.RobotConnectedPoint.fields.createdBy.label=创建人
i18n_entity.RobotConnectedPoint.fields.createdOn.label=创建时间
i18n_entity.RobotConnectedPoint.fields.id.label=ID
i18n_entity.RobotConnectedPoint.fields.modifiedBy.label=最后修改人
i18n_entity.RobotConnectedPoint.fields.modifiedOn.label=最后修改时间
i18n_entity.RobotConnectedPoint.fields.remark.label=备注
i18n_entity.RobotConnectedPoint.fields.version.label=修订版本
i18n_entity.RobotConnectedPoint.fields.x.label=位置 x
i18n_entity.RobotConnectedPoint.fields.y.label=位置 y
i18n_entity.RobotConnectedPoint.group=WCS
i18n_entity.RobotConnectedPoint.label=机器人通讯点
i18n_entity.RobotConnectedPoint.listCard.bin.prefix=关联库位
i18n_entity.RobotConnectedPoint.listCard.x.prefix=位置 x
i18n_entity.RobotConnectedPoint.listCard.y.prefix=位置 y
i18n_entity.RobotPropChangeTimeline.fields.createdBy.label=创建人
i18n_entity.RobotPropChangeTimeline.fields.createdOn.label=创建时间
i18n_entity.RobotPropChangeTimeline.fields.delta.label=变化量
i18n_entity.RobotPropChangeTimeline.fields.duration.label=时长（毫秒）
i18n_entity.RobotPropChangeTimeline.fields.finishedOn.label=结束时间
i18n_entity.RobotPropChangeTimeline.fields.id.label=ID
i18n_entity.RobotPropChangeTimeline.fields.modifiedBy.label=最后修改人
i18n_entity.RobotPropChangeTimeline.fields.modifiedOn.label=最后修改时间
i18n_entity.RobotPropChangeTimeline.fields.newValue.label=新值
i18n_entity.RobotPropChangeTimeline.fields.oldValue.label=旧值
i18n_entity.RobotPropChangeTimeline.fields.percentageBase.label=占比基数
i18n_entity.RobotPropChangeTimeline.fields.robotName.label=机器人
i18n_entity.RobotPropChangeTimeline.fields.startedOn.label=开始时间
i18n_entity.RobotPropChangeTimeline.fields.type.label=状态变化类型
i18n_entity.RobotPropChangeTimeline.fields.version.label=修订版本
i18n_entity.RobotPropChangeTimeline.group=Stats
i18n_entity.RobotPropChangeTimeline.label=机器人状态变化表
i18n_entity.RobustScriptExecutor.fields.args.label=参数
i18n_entity.RobustScriptExecutor.fields.createdBy.label=创建人
i18n_entity.RobustScriptExecutor.fields.createdOn.label=创建时间
i18n_entity.RobustScriptExecutor.fields.description.label=描述
i18n_entity.RobustScriptExecutor.fields.fault.label=故障
i18n_entity.RobustScriptExecutor.fields.faultMsg.label=故障信息
i18n_entity.RobustScriptExecutor.fields.funcName.label=方法
i18n_entity.RobustScriptExecutor.fields.id.label=ID
i18n_entity.RobustScriptExecutor.fields.modifiedBy.label=最后修改人
i18n_entity.RobustScriptExecutor.fields.modifiedOn.label=最后修改时间
i18n_entity.RobustScriptExecutor.fields.version.label=修订版本
i18n_entity.RobustScriptExecutor.group=Core
i18n_entity.RobustScriptExecutor.label=后台任务
i18n_entity.RobustScriptExecutor.pagesButtons.ListMain.buttons[0].label=批量编辑
i18n_entity.RobustScriptExecutor.pagesButtons.ListMain.buttons[1].label=删除
i18n_entity.RobustScriptExecutor.pagesButtons.ListMain.buttons[2].label=恢复
i18n_entity.ScriptRunOnce.fields.createdBy.label=创建人
i18n_entity.ScriptRunOnce.fields.createdOn.label=创建时间
i18n_entity.ScriptRunOnce.fields.id.label=ID
i18n_entity.ScriptRunOnce.fields.modifiedBy.label=最后修改人
i18n_entity.ScriptRunOnce.fields.modifiedOn.label=修改时间
i18n_entity.ScriptRunOnce.fields.output.label=输出
i18n_entity.ScriptRunOnce.fields.version.label=修订版本
i18n_entity.ScriptRunOnce.group=Core
i18n_entity.ScriptRunOnce.label=脚本运行一次
i18n_entity.SimpleTransportOrder.fields.createdBy.label=创建人
i18n_entity.SimpleTransportOrder.fields.createdOn.label=创建时间
i18n_entity.SimpleTransportOrder.fields.currentMove.label=当前步骤
i18n_entity.SimpleTransportOrder.fields.doneOn.label=结束时间
i18n_entity.SimpleTransportOrder.fields.errorMsg.label=错误消息
i18n_entity.SimpleTransportOrder.fields.id.label=ID
i18n_entity.SimpleTransportOrder.fields.modifiedBy.label=最后修改人
i18n_entity.SimpleTransportOrder.fields.modifiedOn.label=最后修改时间
i18n_entity.SimpleTransportOrder.fields.moves.label=动作列表
i18n_entity.SimpleTransportOrder.fields.robotName.label=机器人
i18n_entity.SimpleTransportOrder.fields.seer3066.label=指定路径导航
i18n_entity.SimpleTransportOrder.fields.status.inlineOptionBill.items.Cancelled.label=已取消
i18n_entity.SimpleTransportOrder.fields.status.inlineOptionBill.items.Created.label=已创建
i18n_entity.SimpleTransportOrder.fields.status.inlineOptionBill.items.Done.label=已完成
i18n_entity.SimpleTransportOrder.fields.status.inlineOptionBill.items.Failed.label=失败
i18n_entity.SimpleTransportOrder.fields.status.label=状态
i18n_entity.SimpleTransportOrder.fields.vendor.label=厂商
i18n_entity.SimpleTransportOrder.fields.version.label=修订版本
i18n_entity.SimpleTransportOrder.group=GW
i18n_entity.SimpleTransportOrder.label=单车运单
i18n_entity.SimpleTransportOrder.listCard.robotName.prefix=机器人
i18n_entity.SimpleTransportOrder.listCard.vendor.prefix=厂商
i18n_entity.SimpleTransportOrder.listStats.items[0].label=执行中
i18n_entity.SimpleTransportOrder.listStats.items[1].label=失败
i18n_entity.SocNode.fields.attention.label=注意
i18n_entity.SocNode.fields.createdBy.label=创建人
i18n_entity.SocNode.fields.createdOn.label=创建时间
i18n_entity.SocNode.fields.description.label=说明
i18n_entity.SocNode.fields.id.label=ID
i18n_entity.SocNode.fields.label.label=标签
i18n_entity.SocNode.fields.modifiedBy.label=最后修改人
i18n_entity.SocNode.fields.modifiedOn.label=最后修改时间
i18n_entity.SocNode.fields.modifiedReason.label=更新原因
i18n_entity.SocNode.fields.modifiedTimestamp.label=更新时间
i18n_entity.SocNode.fields.value.label=值
i18n_entity.SocNode.fields.version.label=修订版本
i18n_entity.SocNode.group=Core
i18n_entity.SocNode.label=监控节点
i18n_entity.SocNode.listCard.modifiedTimestamp.prefix=更新时间:
i18n_entity.StatsTimelineValueReport.fields.createdBy.label=创建人
i18n_entity.StatsTimelineValueReport.fields.createdOn.label=创建时间
i18n_entity.StatsTimelineValueReport.fields.denominator.label=分母
i18n_entity.StatsTimelineValueReport.fields.finishedOn.label=结束时间
i18n_entity.StatsTimelineValueReport.fields.id.label=ID
i18n_entity.StatsTimelineValueReport.fields.modifiedBy.label=最后修改人
i18n_entity.StatsTimelineValueReport.fields.modifiedOn.label=最后修改时间
i18n_entity.StatsTimelineValueReport.fields.molecular.label=分子
i18n_entity.StatsTimelineValueReport.fields.period.label=周期
i18n_entity.StatsTimelineValueReport.fields.periodType.label=周期类型
i18n_entity.StatsTimelineValueReport.fields.startedOn.label=开始时间
i18n_entity.StatsTimelineValueReport.fields.subject.label=主题
i18n_entity.StatsTimelineValueReport.fields.target.label=关键词
i18n_entity.StatsTimelineValueReport.fields.value.label=值
i18n_entity.StatsTimelineValueReport.fields.version.label=修订版本
i18n_entity.StatsTimelineValueReport.group=Stats
i18n_entity.StatsTimelineValueReport.label=时间序列数值报表
i18n_entity.SystemKeyEvent.fields.content.label=内容
i18n_entity.SystemKeyEvent.fields.createdBy.label=创建人
i18n_entity.SystemKeyEvent.fields.createdOn.label=创建时间
i18n_entity.SystemKeyEvent.fields.group.label=模块
i18n_entity.SystemKeyEvent.fields.id.label=ID
i18n_entity.SystemKeyEvent.fields.level.inlineOptionBill.items.Error.label=错误
i18n_entity.SystemKeyEvent.fields.level.inlineOptionBill.items.Info.label=普通
i18n_entity.SystemKeyEvent.fields.level.inlineOptionBill.items.Warning.label=警告
i18n_entity.SystemKeyEvent.fields.level.label=级别
i18n_entity.SystemKeyEvent.fields.modifiedBy.label=最后修改人
i18n_entity.SystemKeyEvent.fields.modifiedOn.label=最后修改时间
i18n_entity.SystemKeyEvent.fields.relatedUser.label=相关用户
i18n_entity.SystemKeyEvent.fields.title.label=标题
i18n_entity.SystemKeyEvent.fields.version.label=修订版本
i18n_entity.SystemKeyEvent.group=Core
i18n_entity.SystemKeyEvent.label=系统关键事件
i18n_entity.TransportOrder.fields.actualRobotName.label=执行机器人
i18n_entity.TransportOrder.fields.containerDir.label=终点放置容器的方向
i18n_entity.TransportOrder.fields.containerId.label=容器
i18n_entity.TransportOrder.fields.containerTypeName.label=容器类型名称
i18n_entity.TransportOrder.fields.createdBy.label=创建人
i18n_entity.TransportOrder.fields.createdOn.label=创建时间
i18n_entity.TransportOrder.fields.currentStepIndex.label=当前步骤
i18n_entity.TransportOrder.fields.dispatchCost.label=分派成本
i18n_entity.TransportOrder.fields.doneOn.label=完成时间
i18n_entity.TransportOrder.fields.doneStepIndex.label=已完成步骤
i18n_entity.TransportOrder.fields.executingTime.label=执行耗时（秒）
i18n_entity.TransportOrder.fields.expectedRobotGroups.label=指定机器人组
i18n_entity.TransportOrder.fields.expectedRobotNames.label=指定机器人
i18n_entity.TransportOrder.fields.externalId.label=外部单号
i18n_entity.TransportOrder.fields.failureNum.label=故障次数
i18n_entity.TransportOrder.fields.fault.label=故障
i18n_entity.TransportOrder.fields.fault.view.trueText=故障
i18n_entity.TransportOrder.fields.faultDuration.label=故障时长（s）
i18n_entity.TransportOrder.fields.faultReason.label=故障原因
i18n_entity.TransportOrder.fields.id.label=单号
i18n_entity.TransportOrder.fields.keyLocations.label=关键点位
i18n_entity.TransportOrder.fields.kind.inlineOptionBill.items.Business.label=业务
i18n_entity.TransportOrder.fields.kind.inlineOptionBill.items.Charging.label=充电
i18n_entity.TransportOrder.fields.kind.inlineOptionBill.items.IdleAvoid.label=推空闲车
i18n_entity.TransportOrder.fields.kind.inlineOptionBill.items.Parking.label=停靠
i18n_entity.TransportOrder.fields.kind.label=类型
i18n_entity.TransportOrder.fields.loadDuration.label=取货时长（s）
i18n_entity.TransportOrder.fields.loadPoint.label=取货点位
i18n_entity.TransportOrder.fields.loaded.label=已取货
i18n_entity.TransportOrder.fields.modifiedBy.label=最后修改人
i18n_entity.TransportOrder.fields.modifiedOn.label=最后修改时间
i18n_entity.TransportOrder.fields.oldRobots.label=历史分配机器人
i18n_entity.TransportOrder.fields.priority.label=优先级
i18n_entity.TransportOrder.fields.processingTime.label=处理耗时（秒）
i18n_entity.TransportOrder.fields.ro.label=RO
i18n_entity.TransportOrder.fields.robotAllocatedOn.label=机器人分配时间
i18n_entity.TransportOrder.fields.sceneId.label=场景 ID
i18n_entity.TransportOrder.fields.sceneName.label=场景名
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.Allocated.label=已分派
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.Cancelled.label=已取消
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.Done.label=已完成
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.Executing.label=执行中
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.Pending.label=待执行
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.ToBeAllocated.label=待分派
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.Withdrawn.label=被撤回
i18n_entity.TransportOrder.fields.status.label=状态
i18n_entity.TransportOrder.fields.stepFixed.label=已封口
i18n_entity.TransportOrder.fields.stepNum.label=任务步数
i18n_entity.TransportOrder.fields.taskBatch.label=任务批次
i18n_entity.TransportOrder.fields.unloadDuration.label=放货时长（s）
i18n_entity.TransportOrder.fields.unloadPoint.label=放货点位
i18n_entity.TransportOrder.fields.unloaded.label=已放货
i18n_entity.TransportOrder.fields.version.label=修订版本
i18n_entity.TransportOrder.fields.waitExecuteDuration.label=执行等待时长（s）
i18n_entity.TransportOrder.group=Fleet
i18n_entity.TransportOrder.label=新通用运单
i18n_entity.TransportOrder.listCard.actualRobotName.prefix=执行机器人
i18n_entity.TransportOrder.listCard.createdOn.prefix=创建
i18n_entity.TransportOrder.listCard.kind.prefix=类型
i18n_entity.TransportOrder.listStats.items[0].label=故障
i18n_entity.TransportOrder.listStats.items[1].label=待分派
i18n_entity.TransportOrder.listStats.items[2].label=未封口
i18n_entity.TransportOrder.listStats.items[3].label=今日创建
i18n_entity.TransportOrder.listStats.items[4].label=今日完成
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[0].label=新增
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[1].label=批量编辑
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[2].label=删除
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[3].label=导出
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[4].label=取消
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[5].label=封口
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[6].label=故障重试
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[7].label=修改优先级
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[8].label=导出快照
i18n_entity.TransportStep.fields.createdBy.label=创建人
i18n_entity.TransportStep.fields.createdOn.label=创建时间
i18n_entity.TransportStep.fields.endOn.label=结束执行时间
i18n_entity.TransportStep.fields.executingTime.label=执行耗时（秒）
i18n_entity.TransportStep.fields.forLoad.label=在此步取货
i18n_entity.TransportStep.fields.forUnload.label=在此步放货
i18n_entity.TransportStep.fields.id.label=ID
i18n_entity.TransportStep.fields.location.label=作业位置
i18n_entity.TransportStep.fields.modifiedBy.label=最后修改人
i18n_entity.TransportStep.fields.modifiedOn.label=修改时间
i18n_entity.TransportStep.fields.nextStepSameOrder.label=下一个步骤必须是同一运单的
i18n_entity.TransportStep.fields.orderId.label=运单单号
i18n_entity.TransportStep.fields.processingTime.label=处理耗时（秒）
i18n_entity.TransportStep.fields.rbkArgs.label=动作参数
i18n_entity.TransportStep.fields.startOn.label=开始执行时间
i18n_entity.TransportStep.fields.status.label=状态
i18n_entity.TransportStep.fields.stepIndex.label=第几步
i18n_entity.TransportStep.fields.version.label=修订版本
i18n_entity.TransportStep.fields.withdrawOrderAllowed.label=允许重分派
i18n_entity.TransportStep.group=Fleet
i18n_entity.TransportStep.label=新通用运单步骤
i18n_entity.TransportStep.listCard.locationSite.prefix=点位
i18n_entity.TransportStep.listCard.operation.prefix=动作
i18n_entity.TransportStep.listCard.stepIndex.prefix=第
i18n_entity.TransportStep.listCard.stepIndex.suffix=步
i18n_entity.UserNotice.fields.actionType.label=动作类型
i18n_entity.UserNotice.fields.content.label=正文
i18n_entity.UserNotice.fields.createdBy.label=创建人
i18n_entity.UserNotice.fields.createdOn.label=创建时间
i18n_entity.UserNotice.fields.entityId.label=实体 ID
i18n_entity.UserNotice.fields.entityName.label=实体名
i18n_entity.UserNotice.fields.hasContent.label=有正文
i18n_entity.UserNotice.fields.id.label=ID
i18n_entity.UserNotice.fields.modifiedBy.label=最后修改人
i18n_entity.UserNotice.fields.modifiedOn.label=最后修改时间
i18n_entity.UserNotice.fields.read.label=已读
i18n_entity.UserNotice.fields.readOn.label=已读时间
i18n_entity.UserNotice.fields.title.label=标题
i18n_entity.UserNotice.fields.userId.label=用户
i18n_entity.UserNotice.fields.version.label=修订版本
i18n_entity.UserNotice.group=Core
i18n_entity.UserNotice.label=用户通知
i18n_entity.UserOpLog.fields.content.label=操作内容
i18n_entity.UserOpLog.fields.createdBy.label=创建人
i18n_entity.UserOpLog.fields.createdOn.label=创建时间
i18n_entity.UserOpLog.fields.id.label=ID
i18n_entity.UserOpLog.fields.level.inlineOptionBill.items.Danger.label=危险
i18n_entity.UserOpLog.fields.level.inlineOptionBill.items.Normal.label=正常
i18n_entity.UserOpLog.fields.level.label=级别
i18n_entity.UserOpLog.fields.modifiedBy.label=最后修改人
i18n_entity.UserOpLog.fields.modifiedOn.label=最后修改时间
i18n_entity.UserOpLog.fields.operator.label=用户
i18n_entity.UserOpLog.fields.page.label=页面
i18n_entity.UserOpLog.fields.version.label=修订版本
i18n_entity.UserOpLog.group=Core
i18n_entity.UserOpLog.label=用户操作日志
i18n_entity.UserRole.fields.createdBy.label=创建人
i18n_entity.UserRole.fields.createdOn.label=创建时间
i18n_entity.UserRole.fields.defaultRole.label=默认角色
i18n_entity.UserRole.fields.id.label=编号
i18n_entity.UserRole.fields.modifiedBy.label=最后修改人
i18n_entity.UserRole.fields.modifiedOn.label=最后修改时间
i18n_entity.UserRole.fields.name.label=角色名
i18n_entity.UserRole.fields.pItems.label=权限列表
i18n_entity.UserRole.fields.ro.label=根组织
i18n_entity.UserRole.fields.version.label=版本
i18n_entity.UserRole.group=User
i18n_entity.UserRole.label=用户角色
i18n_entity.UserRole.listCard.createdBy.prefix=创建人
i18n_entity.UserRole.listCard.modifiedOn.prefix=修改时间
i18n_entity.WcsMrOrder.fields.actualRobotName.label=执行机器人
i18n_entity.WcsMrOrder.fields.cancelling.label=取消中
i18n_entity.WcsMrOrder.fields.containerId.label=容器编号
i18n_entity.WcsMrOrder.fields.createdBy.label=创建人
i18n_entity.WcsMrOrder.fields.createdOn.label=创建时间
i18n_entity.WcsMrOrder.fields.currentStepIndex.label=当前步骤
i18n_entity.WcsMrOrder.fields.dispatchCost.label=分派成本
i18n_entity.WcsMrOrder.fields.doneOn.label=完成时刻
i18n_entity.WcsMrOrder.fields.doneStepIndex.label=已完成步骤
i18n_entity.WcsMrOrder.fields.executing.label=执行中
i18n_entity.WcsMrOrder.fields.expectedRobotGroups.label=指定机器人组
i18n_entity.WcsMrOrder.fields.expectedRobotNames.label=指定机器人
i18n_entity.WcsMrOrder.fields.fault.label=故障
i18n_entity.WcsMrOrder.fields.id.label=ID
i18n_entity.WcsMrOrder.fields.keySites.label=关键点位
i18n_entity.WcsMrOrder.fields.kind.inlineOptionBill.items.Parking.label=停靠
i18n_entity.WcsMrOrder.fields.kind.label=类型
i18n_entity.WcsMrOrder.fields.loadLocationSite.label=取货点位
i18n_entity.WcsMrOrder.fields.loaded.label=已取货
i18n_entity.WcsMrOrder.fields.materialId.label=物料编号
i18n_entity.WcsMrOrder.fields.materialKind.label=物料类型
i18n_entity.WcsMrOrder.fields.modifiedBy.label=最后修改人
i18n_entity.WcsMrOrder.fields.modifiedOn.label=修改时间
i18n_entity.WcsMrOrder.fields.priority.label=优先级
i18n_entity.WcsMrOrder.fields.reqId.label=请求单号
i18n_entity.WcsMrOrder.fields.ro.label=RO
i18n_entity.WcsMrOrder.fields.robotAllocatedOn.label=机器人分配时刻
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Allocated.label=已分派
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Building.label=构建中
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Cancelled.label=已取消
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Cancelling.label=取消中
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Done.label=已完成
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Executing.label=执行中
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Pending.label=待执行
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.ToBeAllocated.label=待分派
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Withdrawn.label=被撤回
i18n_entity.WcsMrOrder.fields.status.label=状态
i18n_entity.WcsMrOrder.fields.stepFixed.label=步骤固定
i18n_entity.WcsMrOrder.fields.stepNum.label=任务步数
i18n_entity.WcsMrOrder.fields.taskBatch.label=任务批次
i18n_entity.WcsMrOrder.fields.unloadLocationSite.label=放货点位
i18n_entity.WcsMrOrder.fields.unloaded.label=已放货
i18n_entity.WcsMrOrder.fields.version.label=修订版本
i18n_entity.WcsMrOrder.fields.withdrawn.label=重新分派
i18n_entity.WcsMrOrder.group=WCS
i18n_entity.WcsMrOrder.label=通用运单
i18n_entity.WcsMrOrder.listCard.actualRobotName.prefix=执行机器人
i18n_entity.WcsMrOrder.listCard.createdOn.prefix=创建
i18n_entity.WcsMrOrder.listCard.kind.prefix=类型
i18n_entity.WcsMrOrder.listStats.items[0].label=故障
i18n_entity.WcsMrOrder.listStats.items[1].label=待分派
i18n_entity.WcsMrOrder.listStats.items[2].label=已分派
i18n_entity.WcsMrOrderStep.fields.createdBy.label=创建人
i18n_entity.WcsMrOrderStep.fields.createdOn.label=创建时间
i18n_entity.WcsMrOrderStep.fields.endOn.label=结束执行时间
i18n_entity.WcsMrOrderStep.fields.forLoad.label=取货点
i18n_entity.WcsMrOrderStep.fields.forUnload.label=放货点
i18n_entity.WcsMrOrderStep.fields.id.label=ID
i18n_entity.WcsMrOrderStep.fields.locationSite.label=动作点位
i18n_entity.WcsMrOrderStep.fields.modifiedBy.label=最后修改人
i18n_entity.WcsMrOrderStep.fields.modifiedOn.label=修改时间
i18n_entity.WcsMrOrderStep.fields.operation.label=动作
i18n_entity.WcsMrOrderStep.fields.operationArgs.label=动作参数
i18n_entity.WcsMrOrderStep.fields.orderId.label=运单单号
i18n_entity.WcsMrOrderStep.fields.startOn.label=开始执行时间
i18n_entity.WcsMrOrderStep.fields.status.label=状态
i18n_entity.WcsMrOrderStep.fields.stepIndex.label=第几步
i18n_entity.WcsMrOrderStep.fields.version.label=修订版本
i18n_entity.WcsMrOrderStep.group=WCS
i18n_entity.WcsMrOrderStep.label=通用运单步骤
i18n_entity.WcsMrOrderStep.listCard.locationSite.prefix=点位
i18n_entity.WcsMrOrderStep.listCard.operation.prefix=动作
i18n_entity.WcsMrOrderStep.listCard.stepIndex.prefix=第
i18n_entity.WcsMrOrderStep.listCard.stepIndex.suffix=步
i18n_entity.WcsRobotTaskLog.fields.args.label=参数
i18n_entity.WcsRobotTaskLog.fields.category.label=类别
i18n_entity.WcsRobotTaskLog.fields.code.label=代码
i18n_entity.WcsRobotTaskLog.fields.createdBy.label=创建人
i18n_entity.WcsRobotTaskLog.fields.createdOn.label=创建时间
i18n_entity.WcsRobotTaskLog.fields.id.label=ID
i18n_entity.WcsRobotTaskLog.fields.level.label=级别
i18n_entity.WcsRobotTaskLog.fields.modifiedBy.label=最后修改人
i18n_entity.WcsRobotTaskLog.fields.modifiedOn.label=修改时间
i18n_entity.WcsRobotTaskLog.fields.robotName.label=机器人
i18n_entity.WcsRobotTaskLog.fields.taskId.label=任务编号
i18n_entity.WcsRobotTaskLog.fields.tcId.label=追踪号
i18n_entity.WcsRobotTaskLog.fields.version.label=修订版本
i18n_entity.WcsRobotTaskLog.group=WCS
i18n_entity.WcsRobotTaskLog.label=机器人任务日志
i18n_entity.WcsRobotTaskLog.listCard.category.prefix=类别
i18n_entity.WcsRobotTaskLog.listCard.code.prefix=代码
i18n_entity.WcsRobotTaskLog.listCard.level.prefix=级别
i18n_entity.WcsRobotTaskLog.listCard.taskId.prefix=任务号