
[base]
asyncPushTaskOkMsg=システムはバックグラウンドマップのプッシュを完了しました。{0}で開始しました。{1}で完了しました。<br/>合計{2}個のロボット{3}をプッシュしました。<br/>成功{4}個{5}。<br/>失敗{6}個{7}。
asyncPushTaskPushingMsg=システムは現在、バックグラウンドで地図のプッシュを行っています。開始は{0}です。<br/>{1}個のロボット{2}をプッシュする予定です。<br/>まだ{3}個が{4}にプッシュされています。
bgTaskErrorMsg=バックグラウンドタスク[{0}]障害:{1}
binMsg=ライブラリビット
binNoMsg=第{0}層（{1}号）のバックバスケット
btnAbortTask=任務を放棄する
btnCancelOrder=運送状をキャンセルする
btnCancelTask=タスクをキャンセル
btnFaultRetry=障害の再試行
btnManualBindM4Bin=商品をM 4ライブラリにバインドする場所:{0}、インデックス:{1}
btnManualBindRobot=入れました
btnManualContinue=再試行
btnManualContinueMsg=ロボット{0}第{1}層({2}番)のバックバスケットの容器を取ったことを確認してください
btnManualFinishedTask=手動で完了
btnManualReBindRobot=再バインドされました
btnManualSkip=無視する
btnManualUnbindM4Bin=M 4ライブラリの位置:{0}から商品のバインドを解除します。index:{1}
btnManualUnbindRobot=取られました
btnViewDetail=詳細を見る
colonMsg=: {0}
errDownloadLogsTimeOverRange=最大1週間連続してログを取得できます。開始日または終了日を変更してください
errEmptyStatsSubject=統計科目は空にできません
errFalconLabelEmpty=タスク名は空にできません
errFalconLabelRepeat=重複するタスク名:"{0}"
errFleetNoSuchSceneById=このシーンはありません:{0}
errIllegalFile=違法文書
errInvalidMapFile=無効なマップ文書
errNoRdsScene=スケジューリングシーンが欠落しています
errRbkBinContainerId=ロボット{0}第{1}層バックバスケット({2}番)に必要なコンテナ番号は{3}で、実際のコンテナ番号は{4}です。正しいコンテナをバックバスケットに入れて、「再試行」をクリックしてください
errRbkBinContainerIdPlusOne=ロボット{0}フォーク(999番)バックバスケットに必要なコンテナ番号は{1}で、実際のコンテナ番号は{2}です。正しいコンテナをバックバスケットに入れて、「再試行」をクリックしてください
errRbkBinIsEmpty=ロボット{0}第{1}層バックバスケット({2}号)は在庫がないので、容器{3}に入れて「再試行」をクリックしてください
errRbkBinIsEmptyPlusOne=ロボット{0}フォーク(999番)バックバスケットは品切れです。容器{1}に入れて、「再試行」をクリックしてください
errRbkBinOccupied=ロボット{0}が集荷時に予約したバックバスケットが占有されているので、第{1}層({2}号)のバックバスケットの貨物を取って、「再試行」をクリックしてください
errRbkBinOccupiedPlusOne=ロボット{0}集荷時に予約したバックバスケットが占有されているので、フォーク(999番)バックバスケットの貨物を取って、「再試行」をクリックしてください
errRbkReqResBadCode=ロボットのリクエストに失敗しました。エラーコード:{0}。エラーメッセージ:{1}
labelBgTask=バックエンドタスク
labelNone=なし
pathMsg=パス
pointMsg=ポイント
pwdRequirementModerate=大文字小文字の英語、数字、少なくとも8桁を含む
pwdRequirementStrong=大文字の英語、小文字の英語、数字、特殊文字を含み、少なくとも12桁
pwdRequirementWeak=「少なくとも1人」
reason=理由
robotArmMsg=フォーク
system=システム
zoneMsg=ブロック

[base.error]
errAddStepsButNotBzOrder=業務用オーダーのみステップを追加できます。オーダー {0} は業務用オーダーではありません。
errAddStepsButStepFixed=ステップの追加に失敗しました。オーダー {0} は確定済みのため、ステップを追加できません。
errAdminRequired=管理者権限が必要です
errAggregateQuery=集計クエリ例外:{0}
errAreaNotFoundById=この領域はありません:{0}
errArmContainerForUnload=荷降ろしフォークの貨物を準備するが、荷降ろしする貨物とフォークの貨物が一致しない
errArmEmptyForUnload=荷降ろしフォークの品物を準備していますが、フォークは品物がありません
errArmNotEmptyForLoad=商品を受け取る準備ができていますが、フォークに商品があります
errArmNotEmptyForUnload=バックバスケットの貨物を降ろす準備をしていますが、フォークは在庫があります
errBadColumnName=列名が無効です"{0}"
errBadHttpMethod=サポートされていないHttpメソッド:{0}
errBadIP=IP形式が正しくありません:"{0}"
errBadReqBodyJson=JSONフォーマットエラー:{0}
errBadRequestTimepointOrTimestamp=タイムスタンプとタイムラインを同時に使用してクエリを実行することはできません。
errBinBound=ライブラリビットは他のコンテナにバインドされています。ライブラリビット{0}、コンテナ{1}
errBinBoundDifferentPoint=ライブラリ{0}は、異なるロボットグループのマップ内の異なるポイントにバインドされています。ロボットグループ{1}とロボットグループ{2}のマップを確認してください
errBinHasContainerNotToLoad=ライブラリ位置{0}にはすでにコンテナ{1}があり、コンテナ{2}を置くことはできません
errBinNoContainer=ライブラリビット{0}にコンテナが必要です
errBinNoRobot=ライブラリ位置"{0}"はロボットの位置パラメータを設定していません
errBinNotEmpty=ライブラリ{0}は空で、占有されていない必要があります
errBpNoChild=コンポーネント未設定の子コンポーネント"{0}"
errBpRunError=コンポーネント"{1}"の実行エラー:{0}
errBreakLoop=ループの終了
errBzError=ビジネス例外:{0}
errBzMaterialCategoryNoStoreDistricts=マテリアル分類"{0}"未構成のリポジトリ領域
errBzMaterialNoCategory=マテリアル"{0}"未設定の分類
errBzMissingKeyParam=必須パラメーターが不足しています:"{0}"
errBzNoActiveBomForMat=マテリアル"{0}"有効化済みマテリアルリストが見つかりません
errBzNoEnoughContainerForMaterial=資材「{0}」の空箱が不足している
errBzNoEnoughEmptyContainer=空のコンテナが不足しています（期待={0}、={1}のみ）
errBzNoMaterialById=物料"{0}"が見つかりません。
errBzNoMaterialCategoryById=物料分類"{0}"が見つかりません。
errBzNoMaterialContainerMaxQty=マテリアル"{0}"未設定のコンテナ容量
errBzNoMaterialContainerMaxQty2=マテリアル"{0}"未設定の"有効な"コンテナ容量
errBzNoSuchOrderNameId=見つからない書類"{0}"/"{1}"
errBzOutboundLineShort={0}行目、資材「{2}」、在庫が{1}個不足している
errCancelOrderWhenLoadButNoUnload=ロボット{0}が集荷されたが、出荷されていない場合、運送状をキャンセルしたので、第{1}層({2}号)のかごの中の貨物を移動し、「人工完成」をクリックしてください
errCancelOrderWhenPlusOneLoadButNoUnload=ロボット{0}が集荷されたが、出荷されていない場合、運送伝票をキャンセルしたので、フォーク(999番)のかごの中の貨物を移動し、「人工完成」をクリックしてください
errCannotCreateNewBuiltinTaskDef=内蔵のファルコンタスクを新規作成できません。
errCannotExceedStatisticTimeSpan=統計の期間は{0}日を超えてはならない
errCannotExecuteNonDeviceOperation=非機関制御の操作は実行できません
errCannotModifyBuiltinFromFalseToTrue=通常のファルコンタスクを組み込みファルコンタスクに変更することはできません。タスク名={0}
errCannotModifyBuiltinLabel=組み込みのファルコンタスクのタスク名を変更できません。古い値={0}、新しい値={1}です。
errCannotRemoveBuiltinTaskDef=組み込みタスクテンプレートを削除できません:{0}
errCannotRequestControl=ロボット{0}はナビゲーション中や急停止や制御権が奪われ、制御権を得ることができない
errCodeErr=プログラムエラー:{0}
errColumnEndMustPosInt=終了列番号は正の整数でなければなりません。
errColumnStartEQColumnEnd=列が無効化されている場合、開始列番号は終了列番号と同じでなければなりません。
errColumnStartGtColumnEnd=開始列番号は終了列番号より大きくできません
errColumnStartMustPosInt=開始列番号は正の整数でなければなりません。
errComplexQueryBadOp=サポートされていない演算子"{0}"
errComplexQueryInMultiple=クエリは複数の値を指定する必要があります
errComplexQueryMissingField1=クエリは「フィールド1」を指定する必要があります。
errComplexQueryMissingOp=クエリには演算子が必要です
errComplexQueryNotSupportField={0}タイプのフィールド{1}|{2}は{3}演算子をサポートしていません
errComplexQueryUnknownType=サポートされていないクエリタイプ"{0}"
errComplexQueryValueNeedTwo=フィールド"{0}"には2つのクエリ値が必要です
errComplexQueryValueNotString=フィールド"{0}"クエリ値は文字列ではありません
errComplexQueryValueNull=フィールド"{0}"クエリ値が空です
errContainerBadCurrentBin=コンテナ{0}は現在{1}にあり、{2}にはありません
errContainerBound=コンテナは他のライブラリビット、コンテナ{0}、ライブラリビット{1}にバインドされています
errContainerLocked=コンテナ"{0}"が使用されている可能性があります。他のコンテナを試してください(ロック=true)
errContainerNoBin=コンテナ"{0}"の現在のライブラリビットは空です
errContainerNoBinMissingFromBin=コンテナ{0}の現在のライブラリビットをクエリできません。開始点としてライブラリビットを指定する必要があります
errContainerNoType=コンテナ"{0}"にはタイプが設定されていません
errContainerNotFound=コンテナが見つかりません、コンテナID={0}
errContainerOrFromBinSpecified=少なくともコンテナまたは出発点ライブラリのビットを指定します
errContainerTypeNoStoreDistricts=コンテナタイプ"{0}"未構成ストア
errDbProcessing=データベースを処理しています。後で再試行してください
errDbRestoreNoInfo=データ文書正しくありません（info. txtが不足しています）
errDeleteStepsButNoRobot=オーダーステップの削除に失敗しました。シナリオ内にオーダー {0} を実行するロボット {1} が存在しません。
errDeleteStepsButOrderIdNotMatch=オーダーステップの削除に失敗しました。ステップ {0} はオーダー {1} に属していません。
errDeleteStepsButStepExecuting=オーダーステップの削除に失敗しました。ロボット {0} は現在オーダー {1} のステップ {2} を実行中です。
errDeleteStepsButStepSelected=オーダーステップの削除に失敗しました。ロボット {0} はオーダー {1} のステップ {2} を実行予定です。
errDepthEndMustPosInt=終了深度番号は正の整数でなければなりません。
errDepthStartEQDepthEnd=深度が無効化されている場合、開始深度番号は終了深度番号と同じでなければなりません。
errDepthStartGtDepthEnd=開始深度は終了深度を超えてはなりません
errDepthStartMustPosInt=開始深度番号は正の整数でなければなりません。
errDingBadConfig=ディントークログイン設定が不完全です
errDingCallErr=ディントークリクエストが失敗しました、コード={0}
errDingNotEnabled=無効中ディントーク
errDingShuNoCode=コードなしのコールバック
errDirFilesNull=ディレクトリ内の文書の一覧表示に失敗しました
errDirNotExists=ディレクトリが存在しません:{0}
errDirectOutboundSubmitFail=直接出庫提出に失敗しました。理由:{0}
errDirectorOutboundBinNotOccupied=ライブラリビット{0}は空です。（直接出庫）
errDirectorOutboundEmptyLayouts=出庫可能な在庫の内訳は空です。（直接出庫）
errDirectorOutboundNoOrder=直接出庫し、受託伝票を設定していません。
errDistrictIdsEmpty=少なくとも1つのリポジトリ領域を指定する必要があります
errDuplicateBinName=マップ{0}には同じ名前のライブラリがあります:{1}。ライブラリ名を変更してください
errDuplicateBinNameInDifferentAreas=異なる領域には同じ名前のライブラリビットは許可されていません。領域{0}と領域{1}には同じ名前のライブラリビット{2}があります
errDuplicateManilaFilterCaseName=重複した統計スキーム名:{0}
errDuplicatePointName=異なる領域には同じ名前のポイントは許可されていません(ポイントを除く):領域{0}と領域{1}には同じ名前のポイント{2}があります
errDuplicateSerialNo=異なる領域には同じ番号のポイントが許可されていません。領域{0}にはポイント{1}、領域{2}にはポイント{3}、番号はすべて{4}です
errDuplicatedKeyError="{0}"の"{1}"フィールド値は重複できません。すべての重複値={2}です
errEmptyEntityValue=ビジネスオブジェクト"{0}"の値が空です
errEmptyPassword=パスワードは空にできません
errEntityNoField=ビジネスオブジェクト"{0}"にはフィールド"{1}"がありません。
errEntityRequestMissingIdOrQueryParam="id"または"query"パラメータを指定する必要があります
errEntityRequestMissingQueryParam=クエリは"query"パラメータを指定する必要があります
errExceedingMaxDecimalLength=ビジネスオブジェクト{0}のフィールド値{1}は、小数部で許可されている最大長{2}を超えています
errExceedingMaxIntegerLength=ビジネスオブジェクト{0}のフィールド値{1}は、整数部分で許可されている最大長{2}を超えています
errExceedingMaxValue=ビジネスオブジェクト{0}のフィールド値{1}が許可された最大点{2}を超えています
errExceedingMinValue=ビジネスオブジェクト{0}のフィールド値{1}が許可された最小点{2}を超えています
errExistBgPushTask=バックグラウンドプッシュタスクが既に存在しています。キャンセルしてから再試行してください。
errExistInconsistentElement=異なる地図間に不一致がある{0}:{1}
errFSNotEnabled=無効中Feishu
errFalconBlockInputParamNotList=コンポーネント"{0}"入力パラメータ"{1}"は配列である必要があります
errFalconBlockInputParamRangeError=コンポーネント"{0}"入力パラメータ"{1}"範囲エラー
errFalconBlockOptionParamError=コンポーネント"{0}"オプションパラメータ"{1}"エラー
errFalconCreateTaskNoDefId=タスクを作成できず、テンプレートが指定されていません
errFalconExpressionError=式の評価エラー。式"{0}"。詳細:{1}
errFalconMissingBlockInputParam=コンポーネント"{0}"には入力パラメーター"{1}"が欠けています。
errFalconRecordRemoveRunning=実行中のタスクがあり、削除できません。ファルコンタスク番号:{0}
errFalconThrowPrefix=エラー:{0}
errFeiShuBadConfig=Feishuログイン設定が不完全です
errFeiShuCallErr=Feishuリクエストが失敗しました、コード={0}
errFeiShuNoCode=コードなしのコールバック
errFieldTextTooLong=フィールド"{0}.{1}"のコンテンツ長{2}が最大制限{3}を超えています
errFieldTypeMismatchWithSQLDataType=ビジネスオブジェクト{0}フィールド値{1}のフィールドタイプ{2}がSQLデータ型{3}と一致しません
errFileNotDirectory=文書ディレクトリではありません:{0}
errFileNotExists=文書存在しません:{0}
errFileNotInDir=文書"{0}"はディレクトリ"{1}"にありません
errGwNoRobot=ゲートウェイにはロボット"{0}"が設定されていません。
errHttpFail=HTTPリクエストが失敗し、HTTPレスポンスコード={0}、本文={1}
errHttpResponseBodyEmpty=HTTPリクエスト応答が空です
errIllegalPeriodTime=不正な時間パラメータ、周期型{0}時間値{1}
errInconsistentQuantity=マージマップとグループマップの{0}の数が一致しません
errIndexFieldDuplicated=インデックスのフィールド"{0}"が繰り返されます
errIndexFieldLenTooLong=インデックス{0}のフィールドの合計長がデータベースの制限{1}を超えています
errIndexNameDuplicated=インデックスの名前"{0}"が繰り返されます
errInitializeScheduler=定時タスクの初期化に失敗しました!"{0}"
errInterruptedException=実行が中断されました
errInvLayoutNoContainer=在庫明細にコンテナ{0}がありません
errInvShort=在庫不足:{0}
errInvShort2=在庫不足、資材{0}、出庫待ち={1}、在庫={2}
errLayerEndMustPosInt=終了層番号は正の整数でなければなりません。
errLayerStartEQLayerEnd=層が無効化されている場合、開始層番号は終了層番号と同じでなければなりません。
errLayerStartGtLayerEnd=開始レイヤー番号は終了レイヤー番号より大きくできません
errLayerStartMustPosInt=開始層番号は正の整数でなければなりません。
errLoadContainerToSelfBinFail=貨物{0}をロボットの{1}に入れることに失敗しました
errLockBinFailed=「{0}」のロックに失敗しました。以前はロックされていました。
errManualCancelOrder=手動で運送状をキャンセルする{0}
errMissingHttpPathParam=パスパラメータがありません:{0}
errMissingHttpQueryParam=HTTPクエリパラメータがありません:{0}
errMissingIdField=ビジネスオブジェクト{1}({0})にはidフィールドがありません
errMissingParam=パラメータが不足しています:{0}
errModifyOffDuty=ロボット{0}が空いているときにのみ、注文を受け付けないように修正できます。
errModifyProhibition={0}
errModifyProhibitionDefaultMsg=現在選択されているデータの状態は変更できません。
errModifyRobotConfig=ロボット{0}が空いているときにのみ設定を変更できます
errMrNoCurrentOrder=マシン"{0}"には現在運送状がありません
errMrNoCurrentStep=ロボット"{0}"は現在実行中の運送手順を持っていません
errMrNoOrder=運送状が存在しないか、実行が完了しました"{0}"
errMrUpdateOrderBadStatus=運送状の更新は許可されていません。運送状="{0}"、現在の状態={1}
errMySqlQueryListNotSupport=My SQLは現在、多値フィールド{0}|{1}のクエリをサポートしていません。
errNameConflict=名前の競合:{0}
errNoBin=ライブラリの位置"{0}"が見つかりませんでした。
errNoBinById=ライブラリの位置"{0}"が見つかりません。
errNoBinForOrder=ロボット"{0}"で運送状"{1}"の関連付けている見つかりません
errNoBinRobotArgs=アクションパラメータが不足しています。ライブラリの位置は"{0}"、アクションは"{1}"です。
errNoBpType=コンポーネント"{0}"が見つかりません。
errNoConfig=指定されていない構成
errNoDeviceHost=デバイスアドレス/IPが設定されていません
errNoDeviceType=デバイスタイプが設定されていません
errNoDistrictById=庫区"{0}"が見つかりません。
errNoEmptyBinInDistrict=空きスペースはありません
errNoFalconGlobalVariable=グローバル制御変数{0}が見つかりません
errNoFalconTaskDefById=タスクテンプレートが見つかりません、テンプレートID={0}
errNoJsonNode=JsonNodeは存在しません
errNoLockById=このライブラリはロックされていません"{0}"
errNoMongoDB=利用可能なMongoDBがありません
errNoOrderLine=単行なし、単行番号={0}
errNoRoutePath={0}から{1}までの到達可能なパスが見つかりません
errNoScriptFunctionProvided=スクリプト関数が指定されていません
errNoSqlDb=SQLデータソースは使用できません
errNoSuchContainerById=コンテナ"{0}"が見つかりません。
errNoSuchContainerTypeById=コンテナタイプ"{0}"が見つかりません。
errNoSuchDistrictById=庫区"{0}"が見つかりません。
errNoSuchEntity=ビジネスオブジェクト"{0}"が見つかりません。
errNoSuchScriptFunction=スクリプト関数"{0}"が見つかりません。
errNoSuchUserById=ユーザーが見つかりません。id="{0}"
errNoTaskDefByLabel=タスクテンプレートが見つかりません、テンプレート名={0}
errNoUiDir=インターフェース文書ディレクトリが見つかりません:{0}
errNoUpOrderConfig=上流のドキュメント構成が見つかりません。上流のドキュメント={1}、下流のドキュメント={0}
errNoUploadedFile=アップロードした文書フォームフィールドに配置する必要があります:"{0}"
errNoUserUsername=ユーザーが見つかりません。ユーザー名="{0}"
errNoWidthTooSmall=番号の幅が小さすぎます
errNonDecimal=ビジネスオブジェクト{0}のフィールド値{1}は小数ではありません
errNonInteger=ビジネスオブジェクト{0}のフィールド値{1}は整数ではありません
errNumOverflow=値{0}が数値型の範囲{1}~{2}を超える
errOrderNoPushConfig=伝票が見つかりません。設定を推奨します:{0}
errOrderStepExecutingFail=運送状{0}ステップ{1}の実行に失敗しました
errParseJsonFile=JSONの解析文書失敗
errParseJsonString=JSON文字列の解析に失敗しました。値は{0}です。
errPasswordNotMatch=パスワードエラー
errPointOutOfDistance=ポイント{0}はロボット群{1}とロボット群{2}の地図では、距離が遠すぎる({3}メートルを超えてはならない)
errPutOnContainerMissingContainerBin=コンテナを出品するには、コンテナを指定する必要があります。
errPwdErrExceedLimit=パスワードエラーが制限を超えています。管理者に連絡してリセットするか、{0}秒後に再試行してください
errPwdExpired=パスワードは期限切れです。管理者に連絡してリセットしてください
errPwdLengthExceedLimit=パスワードの長さは要件を満たしておらず、{0}から{1}ビットが必要です
errPwdStrengthNotAllow=パスワード強度は要件を満たしていません。現在のパスワード強度要件は{0}、要件は{1}です
errRecoverBadExternalCallAsync=キャンセルまたは終了する非同期呼び出しのみを再試行できます
errRefFieldNoRefEntity=フィールド"{0}"は設定されていません"ビジネスオブジェクトを参照"
errRemoveProhibition={0}
errRemoveProhibitionDefaultMsg=現在選択されているデータは削除できません。
errRetryFailedRobotButOrderNotExecuting=故障したロボットを再起動しますが、現在の運送状{0}の状態は実行中ではなく、{1}です。
errRetryFaultOrderFail=障害再試行運送伝票{0}が失敗しました。原因:{1}
errRetryFaultOrderNoRobot=障害リトライが失敗しました、シナリオ {0} に運送伝票 {1} を実行するロボット {2} が存在しません
errRobotNotFailed=ロボット「{0}」は故障していない
errRowEndMustPosInt=終了行番号は正の整数でなければなりません。
errRowStartEQRowEnd=行が無効化されている場合、開始行番号は終了行番号と同じでなければなりません。
errRowStartGtRowEnd=開始番号は終了番号より大きくしてはいけません
errRowStartMustPosInt=開始行番号は正の整数でなければなりません。
errScriptBadReturnNull=スクリプト関数{0}の戻り値が異常で、空にすることはできません
errScriptBuildError=スクリプトソースのビルドに失敗しました
errScriptCloseContextError=コンテキストエラーを閉じる
errScriptDisposeError=スクリプトシステムの破棄エラー
errScriptEntityRw=スクリプトビジネスオブジェクトの読み書きエラー:{0}
errScriptExt=スクリプトエラー:{0}
errScriptReturnNotString=スクリプト関数"{0}"の戻り値は文字列ではありません:{1}
errScriptStartError=スクリプトの起動に失敗しました
errSetToStatementInvalidJavaClass=setToStatementがサポートしていないパラメーターの型"{0}"
errSignInNoPassword=パスワードが必要です
errSimpleScriptBp=簡易スクリプトの実行に失敗しました:{0}
errSingletonEntityBadOp=単一のビジネスオブジェクト"{0}"は"{1}"を呼び出すことができません。
errSqlEmptyWhereNotAllowed=すべての更新を禁止する
errSqlUniqueConstraintViolation=値が繰り返されます。値={2}、テーブル={0}、インデックス={1}。
errStartedOnLtFinishedOn=開始時間は終了時間未満でなければなりません
errStepCannotBeEmpty=ステップは空にできません
errStepIndexCannotBeEmpty=ステップインデックスを空にすることはできません
errSubmitEmptyStepWhenSealed=封印されたが手順が空の運送状を提出できません
errSyncKeyMultiple=多値フィールド"{0}"はビジネスのプライマリキーとして使用できません
errSyncKeyNotUniqInCurrent=元のビジネスオブジェクトリストでは、キーは一意ではありません:"{0}"
errSyncKeyNotUniqInNew=新しいビジネスオブジェクトのリストでは、キーは一意ではありません:"{0}"
errTakeOffContainerBadBin=コンテナ"{0}"をライブラリ"{1}"から削除する必要がありますが、システムレコードコンテナは現在ライブラリ"{2}"にあります。
errTakeOffContainerBadContainer=ライブラリビット"{0}"からコンテナ"{1}"を削除しますが、システムレコードライブラリビット上のコンテナは{2}です
errTakeOffContainerContainerBinAtLeastOne=リポジトリのコンテナを削除し、コンテナまたはリポジトリの少なくとも1つを指定します。
errTakeOffContainerNoBin=ライブラリビット"{1}"からコンテナ"{0}"を削除する必要がありますが、システムレコードコンテナは現在どのライブラリビットにもありません
errTooLongNum=フィールド{0}の値{1}が大きすぎて(桁数が多すぎる)、桁数は{2}ビットを超えてはならず、整数部分は{3}ビットを超えてはならない
errTypesNotEnoughForHeaders=タイプ数が不足しています
errUnbindBinContainerBadBind=ライブラリビット"{0}"とコンテナ"{1}"のバインドを解除しようとしますが、ライブラリビット上の現在のコンテナは"{2}"です
errUnsupportedDBType=サポートされていないデータベースタイプ:{0}
errUnsupportedDataType=データ型はサポートされていません:{0}
errUnsupportedHttpRequestMethod=HTTPメソッドエラー"{0}"
errUpdatePriorityButNotBzOrder=業務用オーダーの優先度のみ変更可能です。オーダー {0} は業務用オーダーではありません。
errUpdateStepButStepExecuting=オーダーステップの更新に失敗しました。ロボット {0} は現在オーダー {1} のステップ {2} を実行中です。
errUpdateStepsButNoRobot=オーダーステップの更新に失敗しました。シナリオ内にオーダー {0} を実行するロボット {1} が存在しません。
errUpdateStepsButOrderIdNotMatch=オーダーステップの更新に失敗しました。ステップ {0} はオーダー {1} に属していません。
errUpdateStepsButStepSelected=オーダーステップの更新に失敗しました。ロボット {0} はオーダー {1} のステップ {2} を実行予定です。
errUserDisabled=アカウントは無効です
errWsNotConnected=未接続[Websocket{0}、状態={1}]
errWsSendFail=送信失敗[Websocket{0}]
errWsSendTimeout=タイムアウトを送信[Websocket{0}]
errWwxBadConfig=企業WeChatログイン設定が不完全です
errWwxCallErr=エンタープライズWeChatリクエストが失敗しました、コード={0}
errWwxNoCode=コードなしのコールバック
errWwxNotEnabled=無効中企業WeChat
errXlsFormulaNotSupported=セル内の数式はサポートされていません

[base.falcon]
Falcon_BlockGroup_Basic=基本コンポーネント
Falcon_BlockGroup_Bin=ライブラリビット
Falcon_BlockGroup_BinContainer=コンテナーライブラリのビット
Falcon_BlockGroup_ConditionAndLoop=条件とループ
Falcon_BlockGroup_Container=コンテナ
Falcon_BlockGroup_ContainerTransport=コンテナハンドリングシート
Falcon_BlockGroup_Cq=コンプレックスQuery
Falcon_BlockGroup_CustomBlocks=カスタムコンポーネント
Falcon_BlockGroup_Entity=ビジネスオブジェクト
Falcon_BlockGroup_FalconRecord=ファルコンクエスト
Falcon_BlockGroup_Inv=在庫
Falcon_Bp_AbortTaskBp_Input_msg_description=ファルコンミッションを終了する理由
Falcon_Bp_AbortTaskBp_Input_msg_label=エラーメッセージ
Falcon_Bp_AbortTaskBp_description=ファルコンミッションの終了
Falcon_Bp_AbortTaskBp_label=タスクの終了
Falcon_Bp_BindBinContainerBp_Input_binId_description=ライブラリID
Falcon_Bp_BindBinContainerBp_Input_binId_label=ライブラリ番号
Falcon_Bp_BindBinContainerBp_Input_checkBinStatus_description=ライブラリビットに他のコンテナがバインドされている場合、エラーが発生します
Falcon_Bp_BindBinContainerBp_Input_checkBinStatus_label=ライブラリの状態を確認する
Falcon_Bp_BindBinContainerBp_Input_checkContainerStatus_description=コンテナが他のライブラリビットにバインドされている場合、エラーが発生します
Falcon_Bp_BindBinContainerBp_Input_checkContainerStatus_label=コンテナの状態を確認する
Falcon_Bp_BindBinContainerBp_Input_containerId_description=コンテナ番号
Falcon_Bp_BindBinContainerBp_Input_containerId_label=コンテナ番号
Falcon_Bp_BindBinContainerBp_Input_unlockBin_description=ライブラリのロックを同期する必要があります。
Falcon_Bp_BindBinContainerBp_Input_unlockBin_label=ライブラリのロックを解除する
Falcon_Bp_BindBinContainerBp_Input_unlockContainer_description=コンテナのロック解除を同期する必要があります。
Falcon_Bp_BindBinContainerBp_Input_unlockContainer_label=コンテナのロック解除
Falcon_Bp_BindBinContainerBp_description=ライブラリビットをコンテナにバインドします
Falcon_Bp_BindBinContainerBp_label=ビットコンテナーをバインドします
Falcon_Bp_BreakBp_Input_condition_description=条件が満たされるとループを終了します
Falcon_Bp_BreakBp_Input_condition_label=条件付き
Falcon_Bp_BreakBp_description=条件を満たすと例外がスローされ、ループが終了します
Falcon_Bp_BreakBp_label=ループ終了Break
Falcon_Bp_CreateInvFromOrderBp_Input_bin_description=ライブラリビット
Falcon_Bp_CreateInvFromOrderBp_Input_bin_label=ライブラリビット
Falcon_Bp_CreateInvFromOrderBp_Input_container_description=コンテナ
Falcon_Bp_CreateInvFromOrderBp_Input_container_label=コンテナ
Falcon_Bp_CreateInvFromOrderBp_Input_copyFields_description=複数のフィールドは","で区切られ、これらのフィールドと対応するデータがインベントリ情報にコピーされます。
Falcon_Bp_CreateInvFromOrderBp_Input_copyFields_label=コピーフィールド
Falcon_Bp_CreateInvFromOrderBp_Input_lotNoFormat_label=ライブラリ番号形式
Falcon_Bp_CreateInvFromOrderBp_Input_materialFieldName_description=資料を示すフィールド名を入力してください
Falcon_Bp_CreateInvFromOrderBp_Input_materialFieldName_label=マテリアルフィールド
Falcon_Bp_CreateInvFromOrderBp_Input_order_description=伝票オブジェクトを入力してください
Falcon_Bp_CreateInvFromOrderBp_Input_order_label=書類
Falcon_Bp_CreateInvFromOrderBp_Input_qtyFieldName_description=伝票に記載されている数量を示すフィールド名を入力してください
Falcon_Bp_CreateInvFromOrderBp_Input_qtyFieldName_label=数量フィールド
Falcon_Bp_CreateInvFromOrderBp_Input_state_description=デフォルトの3つのストレージ状態:受信:受信済み;保存:ストレージ中;割り当て:割り当て済み。
Falcon_Bp_CreateInvFromOrderBp_Input_state_label=在庫状況
Falcon_Bp_CreateInvFromOrderBp_description=伝票情報に基づいて在庫情報を作成する
Falcon_Bp_CreateInvFromOrderBp_label=伝票からインベントリを作成
Falcon_Bp_CreateInvFromOrderLinesBp_Input_bin_description=ライブラリビット
Falcon_Bp_CreateInvFromOrderLinesBp_Input_bin_label=ライブラリビット
Falcon_Bp_CreateInvFromOrderLinesBp_Input_container_description=コンテナ
Falcon_Bp_CreateInvFromOrderLinesBp_Input_container_label=コンテナ
Falcon_Bp_CreateInvFromOrderLinesBp_Input_copyFields_description=複数のフィールドは","で区切られ、これらのフィールドと対応するデータがインベントリ情報にコピーされます。
Falcon_Bp_CreateInvFromOrderLinesBp_Input_copyFields_label=次のフィールドを1行からコピーします
Falcon_Bp_CreateInvFromOrderLinesBp_Input_materialFieldName_description=マテリアルを表すフィールド名を1行に入力してください。
Falcon_Bp_CreateInvFromOrderLinesBp_Input_materialFieldName_label=1行のマテリアルフィールド名
Falcon_Bp_CreateInvFromOrderLinesBp_Input_order_description=伝票オブジェクトを入力してください
Falcon_Bp_CreateInvFromOrderLinesBp_Input_order_label=ドキュメントオブジェクト
Falcon_Bp_CreateInvFromOrderLinesBp_Input_qtyFieldName_description=1行に数量を表すフィールド名を入力してください。
Falcon_Bp_CreateInvFromOrderLinesBp_Input_qtyFieldName_label=1行の数量フィールド名
Falcon_Bp_CreateInvFromOrderLinesBp_Input_state_description=デフォルトの3つのストレージ状態:受信:受信済み;保存:ストレージ中;割り当て:割り当て済み。
Falcon_Bp_CreateInvFromOrderLinesBp_Input_state_label=ストレージの状態
Falcon_Bp_CreateInvFromOrderLinesBp_description=1行の情報からインベントリ情報を作成する
Falcon_Bp_CreateInvFromOrderLinesBp_label=伝票行からインベントリを作成
Falcon_Bp_CreateTraceContainerBp_Input_containerType_description=M 4レコードのコンテナタイプを入力してください。
Falcon_Bp_CreateTraceContainerBp_Input_containerType_label=コンテナーの種類
Falcon_Bp_CreateTraceContainerBp_Output_containerId_description=新しいコンテナー番号。
Falcon_Bp_CreateTraceContainerBp_Output_containerId_label=コンテナ番号
Falcon_Bp_CreateTraceContainerBp_description=トレースコンテナを作成します。
Falcon_Bp_CreateTraceContainerBp_label=トレースコンテナの作成
Falcon_Bp_DelayBp_Input_timeMillis_description=遅延時間(ミリ秒)
Falcon_Bp_DelayBp_Input_timeMillis_label=時間(ミリ秒)
Falcon_Bp_DelayBp_description=後のモジュールの実行を遅らせるために使用され、入力されたパラメータに応じてどのくらい遅らせるか
Falcon_Bp_DelayBp_label=遅延
Falcon_Bp_ExpressionBp_Input_expression_description=式
Falcon_Bp_ExpressionBp_Input_expression_label=式
Falcon_Bp_ExpressionBp_Output_expResult_description=式の結果
Falcon_Bp_ExpressionBp_Output_expResult_label=式の結果
Falcon_Bp_ExpressionBp_description=入力された式に基づいて結果を計算します
Falcon_Bp_ExpressionBp_label=式の評価
Falcon_Bp_FindEmptyContainerBp_Input_districtIds_description=エリア番号。必要に応じて、M 4レコードのエリア番号を入力してください。
Falcon_Bp_FindEmptyContainerBp_Input_districtIds_label=リポジトリId
Falcon_Bp_FindEmptyContainerBp_Input_keepTrying_description=無効:(デフォルト)一度だけ検索します。有効:空のコンテナが見つかるまで検索し続けます。
Falcon_Bp_FindEmptyContainerBp_Input_keepTrying_label=成功するまで再試行してください
Falcon_Bp_FindEmptyContainerBp_Input_sort_description=ターゲットライブラリ領域内のライブラリビットのソート方法。
Falcon_Bp_FindEmptyContainerBp_Input_sort_label=並べ替え
Falcon_Bp_FindEmptyContainerBp_Output_binId_description=ターゲットの空のコンテナを格納するリポジトリの番号。
Falcon_Bp_FindEmptyContainerBp_Output_binId_label=ライブラリ番号
Falcon_Bp_FindEmptyContainerBp_Output_containerId_description=見つかった空のコンテナの番号。
Falcon_Bp_FindEmptyContainerBp_Output_containerId_label=コンテナ番号
Falcon_Bp_FindEmptyContainerBp_Output_found_description=true:見つかりました; false:見つかりません。
Falcon_Bp_FindEmptyContainerBp_Output_found_label=空のコンテナを見つけるかどうか
Falcon_Bp_FindEmptyContainerBp_description=目的のルールに従って、ターゲット領域から空のコンテナを探し、このコンテナをロックします。
Falcon_Bp_FindEmptyContainerBp_label=空のコンテナを探す
Falcon_Bp_FindFalconRecordFieldBp_Input_fieldName_label=フィールドの読み取り
Falcon_Bp_FindFalconRecordFieldBp_Output_fieldValue_label=読み取り値
Falcon_Bp_FindFalconRecordFieldBp_label=タスクレコードフィールドの読み取り
Falcon_Bp_FindFieldValueByIdBp_Input_entityName_description=ビジネスオブジェクト名
Falcon_Bp_FindFieldValueByIdBp_Input_entityName_label=ビジネスオブジェクト名
Falcon_Bp_FindFieldValueByIdBp_Input_field_description=クエリのフィールドを指定します
Falcon_Bp_FindFieldValueByIdBp_Input_field_label=フィールド
Falcon_Bp_FindFieldValueByIdBp_Input_id_description=ビジネスオブジェクトエンティティID
Falcon_Bp_FindFieldValueByIdBp_Input_id_label=ビジネスオブジェクトID
Falcon_Bp_FindFieldValueByIdBp_Output_found_description=ビジネスオブジェクトエンティティが存在するかどうか
Falcon_Bp_FindFieldValueByIdBp_Output_found_label=ビジネスオブジェクトが存在します。
Falcon_Bp_FindFieldValueByIdBp_Output_value_description=クエリフィールドの値
Falcon_Bp_FindFieldValueByIdBp_Output_value_label=値
Falcon_Bp_FindFieldValueByIdBp_description=IDからビジネスオブジェクトエンティティの指定されたフィールドを検索します
Falcon_Bp_FindFieldValueByIdBp_label=IDに基づいてビジネスオブジェクトのフィールドを検索します
Falcon_Bp_FindNotOccupiedBinBp_Input_cq_description=空のライブラリを探すための条件
Falcon_Bp_FindNotOccupiedBinBp_Input_cq_label=条件付き
Falcon_Bp_FindNotOccupiedBinBp_Input_districtIds_description=リポジトリリスト
Falcon_Bp_FindNotOccupiedBinBp_Input_districtIds_label=リポジトリリスト
Falcon_Bp_FindNotOccupiedBinBp_Input_keepTrying_description=trueの場合は成功するまで空のライブラリビットを繰り返し探し、falseの場合は一度だけ探します
Falcon_Bp_FindNotOccupiedBinBp_Input_keepTrying_label=試行錯誤して成功する
Falcon_Bp_FindNotOccupiedBinBp_Input_sort_description=ソートルール
Falcon_Bp_FindNotOccupiedBinBp_Input_sort_label=並べ替え
Falcon_Bp_FindNotOccupiedBinBp_Output_binId_description=見つかったライブラリID
Falcon_Bp_FindNotOccupiedBinBp_Output_binId_label=ライブラリID
Falcon_Bp_FindNotOccupiedBinBp_Output_found_description=trueは見つかった、falseは見つからなかった
Falcon_Bp_FindNotOccupiedBinBp_Output_found_label=見つかるかどうか
Falcon_Bp_FindNotOccupiedBinBp_description=空きライブラリを探す
Falcon_Bp_FindNotOccupiedBinBp_label=空のライブラリを探す
Falcon_Bp_FindOneBp_Input_entityName_label=エンティティ
Falcon_Bp_FindOneBp_Input_field_label=フィールド
Falcon_Bp_FindOneBp_Input_value_label=価値
Falcon_Bp_FindOneBp_label=単一のレコードを検索する
Falcon_Bp_FindOneEntityByIdBp_Input_entityName_description=ビジネスオブジェクト名
Falcon_Bp_FindOneEntityByIdBp_Input_entityName_label=ビジネスオブジェクト名
Falcon_Bp_FindOneEntityByIdBp_Input_id_description=ビジネスオブジェクトエンティティID
Falcon_Bp_FindOneEntityByIdBp_Input_id_label=ビジネスオブジェクトID
Falcon_Bp_FindOneEntityByIdBp_Output_ev_description=ビジネスオブジェクトエンティティの詳細
Falcon_Bp_FindOneEntityByIdBp_Output_ev_label=ビジネスオブジェクト
Falcon_Bp_FindOneEntityByIdBp_Output_found_description=見つかるかどうか
Falcon_Bp_FindOneEntityByIdBp_Output_found_label=見つかるかどうか
Falcon_Bp_FindOneEntityByIdBp_description=IDに基づくビジネスオブジェクトエンティティの検索
Falcon_Bp_FindOneEntityByIdBp_label=IDに基づいてビジネスオブジェクトを検索する
Falcon_Bp_GetBinContainerBp_Input_binId_description=ライブラリID
Falcon_Bp_GetBinContainerBp_Input_binId_label=ライブラリ番号
Falcon_Bp_GetBinContainerBp_Output_binEmpty_description=コンテナが空かどうか、trueが空かどうか
Falcon_Bp_GetBinContainerBp_Output_binEmpty_label=ライブラリが空かどうか
Falcon_Bp_GetBinContainerBp_Output_containerId_description=コンテナ番号
Falcon_Bp_GetBinContainerBp_Output_containerId_label=コンテナ番号
Falcon_Bp_GetBinContainerBp_description=ライブラリビット上のコンテナを取得します。取得する前提は、以前にライブラリビットがコンテナにバインドされていたことです。取得できない場合は、コンテナ番号がnullを返します
Falcon_Bp_GetBinContainerBp_label=ライブラリ上のコンテナーを取得します
Falcon_Bp_GetContainerBinBp_Input_containerId_description=コンテナ番号
Falcon_Bp_GetContainerBinBp_Input_containerId_label=コンテナ番号
Falcon_Bp_GetContainerBinBp_Output_binId_description=ライブラリ番号
Falcon_Bp_GetContainerBinBp_Output_binId_label=ライブラリ番号
Falcon_Bp_GetContainerBinBp_Output_found_description=見つけたかどうか、trueは見つけたことを意味します。
Falcon_Bp_GetContainerBinBp_Output_found_label=見つかるかどうか
Falcon_Bp_GetContainerBinBp_description=コンテナがあるライブラリビットを取得します。取得する前提は、以前にライブラリビットがコンテナにバインドされていたことです。取得できない場合、ライブラリビットIDはnullを返します
Falcon_Bp_GetContainerBinBp_label=コンテナーの場所を取得します
Falcon_Bp_GetContainerInvBp_Input_containerId_description=M 4レコードのコンテナ番号を入力してください
Falcon_Bp_GetContainerInvBp_Input_containerId_label=コンテナ番号
Falcon_Bp_GetContainerInvBp_Output_found_description=true:見つかりました; false:見つかりません。
Falcon_Bp_GetContainerInvBp_Output_found_label=見つかるかどうか
Falcon_Bp_GetContainerInvBp_Output_inv_description=在庫明細書。
Falcon_Bp_GetContainerInvBp_Output_inv_label=在庫明細書
Falcon_Bp_GetContainerInvBp_description=コンテナ番号から対応する在庫明細を取得します。
Falcon_Bp_GetContainerInvBp_label=コンテナ内の在庫の詳細を取得する
Falcon_Bp_GetContainerTypeStoreDistrictsBp_Input_containerId_description=M 4に記録されたコンテナの番号を入力してください。
Falcon_Bp_GetContainerTypeStoreDistrictsBp_Input_containerId_label=コンテナ番号
Falcon_Bp_GetContainerTypeStoreDistrictsBp_Output_storeDistricts_description=領域名のコレクション、配列。
Falcon_Bp_GetContainerTypeStoreDistrictsBp_Output_storeDistricts_label=ストア領域
Falcon_Bp_GetContainerTypeStoreDistrictsBp_description=コンテナ番号に基づいて、そのようなコンテナを格納できるリポジトリ領域の名前をクエリします。
Falcon_Bp_GetContainerTypeStoreDistrictsBp_label=コンテナーの種類のストアをクエリします
Falcon_Bp_IfBp_Input_condition_description=判断条件
Falcon_Bp_IfBp_Input_condition_label=条件付き
Falcon_Bp_IfBp_description=条件判断は、条件が満たされた場合にのみサブモジュールを実行し続けることができる
Falcon_Bp_IfBp_label=IFの場合
Falcon_Bp_IfElseBp_Input_condition_description=判断条件
Falcon_Bp_IfElseBp_Input_condition_label=条件付き
Falcon_Bp_IfElseBp_description=条件判断は、条件を満たすときにtrueのサブモジュールを、条件を満たさないときにfalseのサブモジュールを移動する
Falcon_Bp_IfElseBp_label=もしそうでなければIF ELSE
Falcon_Bp_IterateListBp_Context_index_description=インデックス
Falcon_Bp_IterateListBp_Context_item_description=単一データ情報
Falcon_Bp_IterateListBp_Input_list_description=リストをトラバースします
Falcon_Bp_IterateListBp_Input_list_label=リスト
Falcon_Bp_IterateListBp_Input_parallel_description=並列走査するかどうかを判断する
Falcon_Bp_IterateListBp_Input_parallel_label=並列処理
Falcon_Bp_IterateListBp_description=配列を走査し、並列をサポートします
Falcon_Bp_IterateListBp_label=配列を走査する
Falcon_Bp_KeepTryingLockBinBp_Input_binId_description=ライブラリID
Falcon_Bp_KeepTryingLockBinBp_Input_binId_label=ライブラリビット
Falcon_Bp_KeepTryingLockBinBp_Input_reason_description=ロックの理由
Falcon_Bp_KeepTryingLockBinBp_Input_reason_label=理由
Falcon_Bp_KeepTryingLockBinBp_description=成功するまで、リポジトリのロックを繰り返します
Falcon_Bp_KeepTryingLockBinBp_label=成功するまで、リポジトリのロックを繰り返します
Falcon_Bp_KeepTryingLockNotOccupiedBinBp_Input_binId_description=ライブラリID
Falcon_Bp_KeepTryingLockNotOccupiedBinBp_Input_binId_label=ライブラリ番号
Falcon_Bp_KeepTryingLockNotOccupiedBinBp_description=ライブラリが空になるのを待ってロックします
Falcon_Bp_KeepTryingLockNotOccupiedBinBp_label=ライブラリが空になるのを待ってロックします
Falcon_Bp_LockBinOnceBp_Input_binId_label=ライブラリ番号
Falcon_Bp_LockBinOnceBp_Input_notFoundToAborted_description=失敗した場合、タスクを終了する必要がありますか? trueが必要です。
Falcon_Bp_LockBinOnceBp_Input_notFoundToAborted_label=終了タスクが見つかりません
Falcon_Bp_LockBinOnceBp_Input_notFoundToFault_description=タスクを失敗させる必要があるかどうか、trueが必要です
Falcon_Bp_LockBinOnceBp_Input_notFoundToFault_label=タスクを失敗させることができません
Falcon_Bp_LockBinOnceBp_Output_ok_description=ロックが成功したかどうか、成功がtrue、失敗がfalse
Falcon_Bp_LockBinOnceBp_Output_ok_label=ロックが成功しましたか?
Falcon_Bp_LockBinOnceBp_description=ロックされていないライブラリビットを一度ロックしようとすると、trueが正常に返され、それ以外の場合はfalseが返されます
Falcon_Bp_LockBinOnceBp_label=ロックされていないライブラリをロックする
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_newState_Option_Cancelled=キャンセル
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_newState_Option_Done=完了する
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_newState_Option_Failed=失敗する
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_newState_description=期待される更新の状態
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_newState_label=更新の状態
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_orderId_description=コンテナ運搬伝票の伝票番号、一意の識別
Falcon_Bp_MarkContainerTransportOrderDoneBp_Input_orderId_label=注文番号
Falcon_Bp_MarkContainerTransportOrderDoneBp_description=伝票番号に基づいてコンテナ運搬伝票の状態を更新する
Falcon_Bp_MarkContainerTransportOrderDoneBp_label=コンテナの取り扱い注文の完了をマークします
Falcon_Bp_MoveInvByContainerBp_Input_leafContainerId_description=一番内側のコンテナの番号を入力してください。
Falcon_Bp_MoveInvByContainerBp_Input_leafContainerId_label=最も内側のコンテナ
Falcon_Bp_MoveInvByContainerBp_Input_state_description=在庫状態を指定した在庫状態に変更します
Falcon_Bp_MoveInvByContainerBp_Input_state_label=在庫状態の指定
Falcon_Bp_MoveInvByContainerBp_Input_toBinId_description=最内側のコンテナを配置するライブラリビットの番号。
Falcon_Bp_MoveInvByContainerBp_Input_toBinId_label=エンドポイントライブラリのビット
Falcon_Bp_MoveInvByContainerBp_description=最内層のコンテナ情報を別のライブラリビットにバインドします。
Falcon_Bp_MoveInvByContainerBp_label=コンテナごとにインベントリを移動
Falcon_Bp_PadIntStrBp_Input_numLength_description=数字を文字列に変換した後、指定された長さが元の長さより小さい場合は、指定された長さを無視します。そうでなければ、0で補完します
Falcon_Bp_PadIntStrBp_Input_numLength_label=数字の長さ
Falcon_Bp_PadIntStrBp_Input_num_description=変換する必要がある数値
Falcon_Bp_PadIntStrBp_Input_num_label=数字
Falcon_Bp_PadIntStrBp_Input_prefix_description=変換後の文字列プレフィックス
Falcon_Bp_PadIntStrBp_Input_prefix_label=プレフィックス
Falcon_Bp_PadIntStrBp_Input_suffix_description=変換後の文字列サフィックス
Falcon_Bp_PadIntStrBp_Input_suffix_label=サフィックス
Falcon_Bp_PadIntStrBp_Output_numStr_description=数値変換された文字列
Falcon_Bp_PadIntStrBp_Output_numStr_label=数値変換された文字列
Falcon_Bp_PadIntStrBp_description=条件に応じて、数値を指定した長さの文字列に変換し、不足している場合は0を補完します
Falcon_Bp_PadIntStrBp_label=数値を指定した長さの文字列に変換します
Falcon_Bp_ParallelFlowBp_description=並列実行
Falcon_Bp_ParallelFlowBp_label=並列実行
Falcon_Bp_PrintBp_Input_message_description=印刷する情報
Falcon_Bp_PrintBp_Input_message_label=メッセージ
Falcon_Bp_PrintBp_description=印刷する
Falcon_Bp_PrintBp_label=印刷する
Falcon_Bp_ReduceBinInvFromOrderBp_Input_binField_description=1行にライブラリを表すフィールド名を入力してください。
Falcon_Bp_ReduceBinInvFromOrderBp_Input_binField_label=ライブラリフィールド
Falcon_Bp_ReduceBinInvFromOrderBp_Input_ev_description=伝票情報を入力してください
Falcon_Bp_ReduceBinInvFromOrderBp_Input_ev_label=書類
Falcon_Bp_ReduceBinInvFromOrderBp_Input_materialField_description=1行にマテリアルを表すフィールド名を入力してください
Falcon_Bp_ReduceBinInvFromOrderBp_Input_materialField_label=マテリアルフィールド
Falcon_Bp_ReduceBinInvFromOrderBp_Input_outboundOrderIdField_label=出荷番号
Falcon_Bp_ReduceBinInvFromOrderBp_Input_pickOrderIdField_label=仕分け番号
Falcon_Bp_ReduceBinInvFromOrderBp_Input_qtyField_description=1行に数量を表すフィールド名を入力してください。
Falcon_Bp_ReduceBinInvFromOrderBp_Input_qtyField_label=数量フィールド
Falcon_Bp_ReduceBinInvFromOrderBp_Input_subContainerIdField_label=グリッド
Falcon_Bp_ReduceBinInvFromOrderBp_description=ドキュメントごとに在庫を削除します
Falcon_Bp_ReduceBinInvFromOrderBp_label=ドキュメントごとに在庫を削除します
Falcon_Bp_RemoveInvByContainerBp_Input_containerId_description=M 4レコードのコンテナ番号を入力してください。
Falcon_Bp_RemoveInvByContainerBp_Input_containerId_label=コンテナ番号
Falcon_Bp_RemoveInvByContainerBp_Output_count_description=削除された在庫内訳の数。
Falcon_Bp_RemoveInvByContainerBp_Output_count_label=内訳の数
Falcon_Bp_RemoveInvByContainerBp_description=コンテナ番号に基づいて、対応する在庫明細を削除します。
Falcon_Bp_RemoveInvByContainerBp_label=コンテナごとの在庫内訳の削除
Falcon_Bp_RemoveTraceContainerBp_Input_containerId_description=M 4レコードの追跡コンテナの番号を入力してください。
Falcon_Bp_RemoveTraceContainerBp_Input_containerId_label=コンテナ番号
Falcon_Bp_RemoveTraceContainerBp_description=トレースコンテナを削除します。
Falcon_Bp_RemoveTraceContainerBp_label=トレースコンテナを削除
Falcon_Bp_RepeatNumBp_Child_default_label=コンポーネント
Falcon_Bp_RepeatNumBp_Context_index_description=シリアル番号
Falcon_Bp_RepeatNumBp_Context_index_label=シリアル番号
Falcon_Bp_RepeatNumBp_Input_num_description=子コンポーネントの繰り返し実行回数
Falcon_Bp_RepeatNumBp_Input_num_label=実行回数
Falcon_Bp_RepeatNumBp_description=子コンポーネントの繰り返し実行
Falcon_Bp_RepeatNumBp_label=繰り返し実行 Repeat
Falcon_Bp_SerialFlowBp_description=シリアル実行
Falcon_Bp_SerialFlowBp_label=シリアル実行
Falcon_Bp_SetBinEmptyBp_Input_binId_description=ライブラリID
Falcon_Bp_SetBinEmptyBp_Input_binId_label=ライブラリ番号
Falcon_Bp_SetBinEmptyBp_Input_noContainer_description=trueは、このライブラリビットをコンテナなしに設定します。
Falcon_Bp_SetBinEmptyBp_Input_noContainer_label=コンテナなし
Falcon_Bp_SetBinEmptyBp_Input_notOccupied_description=trueは、ライブラリの位置を未使用に設定するためのものです。
Falcon_Bp_SetBinEmptyBp_Input_notOccupied_label=未使用
Falcon_Bp_SetBinEmptyBp_Input_unlock_label=ロック解除
Falcon_Bp_SetBinEmptyBp_Input_unlock_label_description=trueは、そのライブラリのロックを解除するためのものです。
Falcon_Bp_SetBinEmptyBp_description=ライブラリスペースが占有されていない（廃止）
Falcon_Bp_SetBinEmptyBp_label=ライブラリスペースが占有されていない（廃止）
Falcon_Bp_SetBinNotOccupiedBp_Input_binId_description=ライブラリID
Falcon_Bp_SetBinNotOccupiedBp_Input_binId_label=ライブラリ番号
Falcon_Bp_SetBinNotOccupiedBp_Input_unlockAfter_description=trueは未使用を設定してライブラリのロックを解除し、falseは未使用を設定してライブラリのロックを解除しない
Falcon_Bp_SetBinNotOccupiedBp_Input_unlockAfter_label=ロック解除
Falcon_Bp_SetBinNotOccupiedBp_description=ライブラリスペースが占有されていないように設定します。
Falcon_Bp_SetBinNotOccupiedBp_label=ライブラリスペースが占有されていないように設定します。
Falcon_Bp_SetBinUnLockBp_Input_binId_label=ライブラリ番号
Falcon_Bp_SetBinUnLockBp_Input_requireLock_description=このライブラリがロックされている必要があるかどうかライブラリがロックされていない場合、例外が終了します
Falcon_Bp_SetBinUnLockBp_Input_requireLock_label=このライブラリビットをロックする必要があります
Falcon_Bp_SetBinUnLockBp_description=指定されたライブラリをロック解除する
Falcon_Bp_SetBinUnLockBp_label=ライブラリのロックを解除する
Falcon_Bp_SetGlobalVariableBp_Input_varName_description=変数名を入力してください。変数値ではありません。
Falcon_Bp_SetGlobalVariableBp_Input_varName_label=変数名
Falcon_Bp_SetGlobalVariableBp_Input_varValue_description=データ型はグローバル変数と一致する必要があります
Falcon_Bp_SetGlobalVariableBp_Input_varValue_label=変数の値
Falcon_Bp_SetGlobalVariableBp_description=グローバル変数の値のみを変更でき、グローバル変数を追加することはできません。グローバル変数にnameが指定されていない場合、例外がスローされます
Falcon_Bp_SetGlobalVariableBp_label=グローバル変数の設定
Falcon_Bp_SetTaskVariableBp_Input_varName_description=タスク変数の名前
Falcon_Bp_SetTaskVariableBp_Input_varName_label=変数名
Falcon_Bp_SetTaskVariableBp_Input_varValue_description=タスク変数の値
Falcon_Bp_SetTaskVariableBp_Input_varValue_label=変数の値
Falcon_Bp_SetTaskVariableBp_description=タスク変数の設定
Falcon_Bp_SetTaskVariableBp_label=タスク変数の設定
Falcon_Bp_SimpleScriptBp_Input_scriptStr_label=スクリプトのソースコード
Falcon_Bp_SimpleScriptBp_Output_scriptOut_description=スクリプトの戻り値を実行します
Falcon_Bp_SimpleScriptBp_Output_scriptOut_label=戻り値
Falcon_Bp_SimpleScriptBp_label=簡易スクリプト
Falcon_Bp_SuiBp_Input_config_description=ユーザーの構成操作
Falcon_Bp_SuiBp_Input_config_label=構成する
Falcon_Bp_SuiBp_Output_button_description=ユーザーがクリックしたボタン
Falcon_Bp_SuiBp_Output_button_label=ユーザーがクリックしたボタン
Falcon_Bp_SuiBp_Output_input_description=ユーザーの入力パラメーター
Falcon_Bp_SuiBp_Output_input_label=ユーザーの入力
Falcon_Bp_SuiBp_description=ユーザーの操作を待つ
Falcon_Bp_SuiBp_label=ユーザーの介入
Falcon_Bp_ThrowBp_Input_condition_description=例外をスローするかどうか
Falcon_Bp_ThrowBp_Input_condition_label=条件付き
Falcon_Bp_ThrowBp_Input_errMsg_description=例外メッセージをスロー
Falcon_Bp_ThrowBp_Input_errMsg_label=例外情報
Falcon_Bp_ThrowBp_description=例外をスロー
Falcon_Bp_ThrowBp_label=例外をスロー
Falcon_Bp_TimestampBp_Input_formatDate_description=日付形式を指定します
Falcon_Bp_TimestampBp_Input_formatDate_label=日付形式
Falcon_Bp_TimestampBp_Output_timestamp_description=指定した日付形式の文字列を返します
Falcon_Bp_TimestampBp_Output_timestamp_label=現在のタイムスタンプ
Falcon_Bp_TimestampBp_description=指定した書式の現在時刻文字列を返します
Falcon_Bp_TimestampBp_label=現在の時刻を返します
Falcon_Bp_TriggerTaskEventBp_Input_eventData_description=イベントデータ
Falcon_Bp_TriggerTaskEventBp_Input_eventData_label=イベントデータ
Falcon_Bp_TriggerTaskEventBp_Input_eventName_description=イベントの名前
Falcon_Bp_TriggerTaskEventBp_Input_eventName_label=イベント名
Falcon_Bp_TriggerTaskEventBp_description=タスクイベントのトリガー
Falcon_Bp_TriggerTaskEventBp_label=タスクイベントのトリガー
Falcon_Bp_TryCatchBp_Input_ignoreAbort_description=例外のキャンセルを無視する
Falcon_Bp_TryCatchBp_Input_ignoreAbort_label=例外のキャンセルを無視する
Falcon_Bp_TryCatchBp_Input_swallowError_description=例外を飲み込み、スローしない
Falcon_Bp_TryCatchBp_Input_swallowError_label=例外を飲み込んでスローしない
Falcon_Bp_TryCatchBp_description=例外情報のキャプチャ
Falcon_Bp_TryCatchBp_label=トライキャッチ
Falcon_Bp_UnbindBinContainerBp_Input_binId_description=ライブラリID
Falcon_Bp_UnbindBinContainerBp_Input_binId_label=ライブラリ番号
Falcon_Bp_UnbindBinContainerBp_Input_containerId_description=コンテナ番号
Falcon_Bp_UnbindBinContainerBp_Input_containerId_label=コンテナ番号
Falcon_Bp_UnbindBinContainerBp_Input_unlockBin_description=ライブラリのロックを解除するかどうか
Falcon_Bp_UnbindBinContainerBp_Input_unlockBin_label=ライブラリのロックを解除する
Falcon_Bp_UnbindBinContainerBp_Input_unlockContainer_description=コンテナのロックを解除するかどうか
Falcon_Bp_UnbindBinContainerBp_Input_unlockContainer_label=コンテナのロック解除
Falcon_Bp_UnbindBinContainerBp_description=ライブラリビットをコンテナから解放します
Falcon_Bp_UnbindBinContainerBp_label=リポジトリコンテナのバインド解除
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_Option_fromBin=出発点のライブラリビット
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_Option_loaded=受け取り済み
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_Option_robotName=ロボット
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_Option_status=状態
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_Option_toBin=エンドポイントライブラリのビット
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_Option_unloaded=出荷済み
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_description=更新が必要なフィールド名
Falcon_Bp_UpdateContainerTransportOrderBp_Input_field_label=フィールド
Falcon_Bp_UpdateContainerTransportOrderBp_Input_fv_description=更新するフィールド値
Falcon_Bp_UpdateContainerTransportOrderBp_Input_fv_label=フィールド値
Falcon_Bp_UpdateContainerTransportOrderBp_Input_orderId_description=コンテナ運搬伝票の伝票番号、一意の識別
Falcon_Bp_UpdateContainerTransportOrderBp_Input_orderId_label=注文番号
Falcon_Bp_UpdateContainerTransportOrderBp_description=コンテナハンドリングシートの指定フィールドの値を更新します
Falcon_Bp_UpdateContainerTransportOrderBp_label=コンテナ運搬伝票の更新
Falcon_Bp_UpdateEntityFieldBp_Input_entityName_description=ビジネスオブジェクト名
Falcon_Bp_UpdateEntityFieldBp_Input_entityName_label=ビジネスオブジェクト名
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_Any=任意の型(Any)
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_Boolean=ブール型(Boolean)
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_Double=倍精度浮動小数点（Double）
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_JSONArray=JSON配列(JSONArray)
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_JSONObject=JSONオブジェクト
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_Long=長い整形（Long）
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_Option_String=文字列型(String)
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_description=クエリするフィールドの種類
Falcon_Bp_UpdateEntityFieldBp_Input_queryFieldType_label=クエリフィールドタイプ
Falcon_Bp_UpdateEntityFieldBp_Input_queryField_description=クエリするフィールド名
Falcon_Bp_UpdateEntityFieldBp_Input_queryField_label=クエリフィールド
Falcon_Bp_UpdateEntityFieldBp_Input_queryValue_description=クエリするフィールドの値
Falcon_Bp_UpdateEntityFieldBp_Input_queryValue_label=クエリ値
Falcon_Bp_UpdateEntityFieldBp_Input_setToNull_label=nullに設定
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_Any=任意の型(Any)
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_Boolean=ブール型(Boolean)
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_Double=倍精度浮動小数点（Double）
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_JSONArray=JSON配列(JSONArray)
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_JSONObject=JSONオブジェクト
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_Long=長い整形（Long）
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_Option_String=文字列型(String)
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_description=更新フィールドの種類
Falcon_Bp_UpdateEntityFieldBp_Input_updateFieldType_label=更新フィールドの種類
Falcon_Bp_UpdateEntityFieldBp_Input_updateField_description=更新するフィールド
Falcon_Bp_UpdateEntityFieldBp_Input_updateField_label=更新されたフィールド
Falcon_Bp_UpdateEntityFieldBp_Input_updateMany_description=バッチ更新するかどうか
Falcon_Bp_UpdateEntityFieldBp_Input_updateMany_label=バッチ更新
Falcon_Bp_UpdateEntityFieldBp_Input_updateValue_description=フィールドの更新後の値
Falcon_Bp_UpdateEntityFieldBp_Input_updateValue_label=値の更新
Falcon_Bp_UpdateEntityFieldBp_description=条件に基づいてビジネスオブジェクトの単一フィールドを更新します
Falcon_Bp_UpdateEntityFieldBp_label=条件に基づいてビジネスオブジェクトの単一フィールドを更新します
Falcon_Bp_UpdateFalconRecordFieldBp_Input_fieldName_label=フィールドの設定
Falcon_Bp_UpdateFalconRecordFieldBp_Output_fieldValue_label=設定値
Falcon_Bp_UpdateFalconRecordFieldBp_label=タスクレコードフィールドの設定
Falcon_Bp_UpdateOneEntityByIdBp_Input_entityName_description=ビジネスオブジェクト名
Falcon_Bp_UpdateOneEntityByIdBp_Input_entityName_label=ビジネスオブジェクト名
Falcon_Bp_UpdateOneEntityByIdBp_Input_fieldName_description=フィールド名の更新
Falcon_Bp_UpdateOneEntityByIdBp_Input_fieldName_label=フィールド名
Falcon_Bp_UpdateOneEntityByIdBp_Input_fv_description=更新されたフィールドの値
Falcon_Bp_UpdateOneEntityByIdBp_Input_fv_label=フィールド値
Falcon_Bp_UpdateOneEntityByIdBp_Input_id_description=エンティティID
Falcon_Bp_UpdateOneEntityByIdBp_Input_id_label=Id
Falcon_Bp_UpdateOneEntityByIdBp_Input_setToNull_label=nullに設定
Falcon_Bp_UpdateOneEntityByIdBp_description=IDに基づいてビジネスオブジェクトエンティティの単一フィールドを更新します
Falcon_Bp_UpdateOneEntityByIdBp_label=IDに基づいてビジネスオブジェクトの単一フィールドを更新します
Falcon_Bp_WebSocketBp_Input_eventName_description=WebSocket通信識別子
Falcon_Bp_WebSocketBp_Input_eventName_label=イベントを送信
Falcon_Bp_WebSocketBp_Input_message_description=コンテンツを送信
Falcon_Bp_WebSocketBp_Input_message_label=コンテンツを送信
Falcon_Bp_WebSocketBp_description=WebSocketクライエントサイドメッセージを送信
Falcon_Bp_WebSocketBp_label=WebSocketメッセージの送信
Falcon_Bp_WhileBp_Input_condition_label=条件付き
Falcon_Bp_WhileBp_description=サブモジュールは、条件が満たされたときに継続して実行されます
Falcon_Bp_WhileBp_label=ループ実行中
Falcon_label=ファルコンクエスト
errSubBlockNotFound={0}{1}のサブブロック{2}が見つかりません
labelFalcon=ファルコンクエスト

[wcs.base]
ModbusDeviceNotInit=Modbusデバイス"{0}"は初期化されていません
wcs_err_TomNoUrl=スケジュール"{0}"未設定のURL
wcs_err_TomNotFound=未設定のスケジューリング"{0}"
wcs_err_tom_BlockError=スケジューリングによると、ブロックの実行に失敗し、シングル番号={0}、ブロック={1}、状態={2}
wcs_err_tom_BlockNotFound=スケジューリングによると、ブロックが見つからないとのことです。注文番号は{0}、ブロックは{1}です。
wcs_err_tom_Completed_No_Path=スケジューリングエラーが発生し、すでに封鎖されているため、復元できません。原因:サイトへのパスが見つかりません。
wcs_err_tom_ConnectError=スケジューリングサーバーへの接続に失敗しました:{0}
wcs_err_tom_HttpError=リクエストスケジューリングインターフェースの他のエラー:[{0}]{1}
wcs_err_tom_HttpError404=リクエストスケジューリングインターフェース404
wcs_err_tom_IOError=リクエストスケジューリング、IOエラー:{0}、リクエストアドレス:{1}
wcs_err_tom_OtherError=リクエストスケジューリング、その他のエラー:{0}、リクエストアドレス:{1}
wcs_err_tom_TomError=スケジューリングエラー、エラーコード={0}、メッセージ={1}
wcs_err_tom_TomResponseEmpty=リクエストスケジューリング応答本文が空です

[wcs.error]
clientDeviceExceptionConnectFail=リンク失敗{0}
clientDeviceExceptionDeviceDisabled=デバイスが無効になっています{0}
clientDeviceExceptionDeviceNotExist=デバイスは存在しません{0}
clientDeviceExceptionOpCancel=スレッドが中断されました{0}
clientDeviceExceptionOpTimeout=操作タイムアウト{0}
clientDeviceExceptionReadFail=読み取り失敗{0}
clientDeviceExceptionReqError=リクエストエラー{0}
clientDeviceExceptionWriteFail=書き込み失敗{0}
errDeviceDisabled=デバイス{0}は無効です
errNoDevice=このデバイスなし:{0}

[wcs.falcon]
Falcon_BlockGroup_PLC=PLC
Falcon_Bp_ModbusReadBp_Input_address_description=Modbusの住所
Falcon_Bp_ModbusReadBp_Input_address_label=アドレス
Falcon_Bp_ModbusReadBp_Input_code_Option_1=0 x 01コイル状態の読み取り
Falcon_Bp_ModbusReadBp_Input_code_Option_2=0 x 02入力状態の読み取り
Falcon_Bp_ModbusReadBp_Input_code_Option_3=0 x 03リードホールドレジスタ
Falcon_Bp_ModbusReadBp_Input_code_Option_4=0 x 04入力レジスタを読み込む
Falcon_Bp_ModbusReadBp_Input_code_description=Modbusの機能コード
Falcon_Bp_ModbusReadBp_Input_code_label=機能コード
Falcon_Bp_ModbusReadBp_Input_deviceName_description=デバイス名は、PLCデバイス構成メニューで構成する必要があります
Falcon_Bp_ModbusReadBp_Input_deviceName_label=デバイス名
Falcon_Bp_ModbusReadBp_Input_maxRetry_description=PLCデバイス構成メニューで設定された最大再試行回数がデフォルトです
Falcon_Bp_ModbusReadBp_Input_maxRetry_label=最大再試行回数
Falcon_Bp_ModbusReadBp_Input_retryDelay_description=PLCデバイス構成メニューで設定されたデフォルトの再試行間隔
Falcon_Bp_ModbusReadBp_Input_retryDelay_label=再試行待機(ミリ秒)
Falcon_Bp_ModbusReadBp_Input_slaveId_description=ModbusデバイスID、デフォルトは0です。
Falcon_Bp_ModbusReadBp_Input_slaveId_label=スレーブID
Falcon_Bp_ModbusReadBp_Output_value_description=読み出された対応するアドレスの値
Falcon_Bp_ModbusReadBp_Output_value_label=値
Falcon_Bp_ModbusReadBp_description=Modbusの値を読み取る
Falcon_Bp_ModbusReadBp_label=Modbus読み取り
Falcon_Bp_ModbusReadEqBp_Input_address_description=Modbusの住所
Falcon_Bp_ModbusReadEqBp_Input_address_label=アドレス
Falcon_Bp_ModbusReadEqBp_Input_code_Option_1=0 x 01コイル状態の読み取り
Falcon_Bp_ModbusReadEqBp_Input_code_Option_2=0 x 02入力状態の読み取り
Falcon_Bp_ModbusReadEqBp_Input_code_Option_3=0 x 03リードホールドレジスタ
Falcon_Bp_ModbusReadEqBp_Input_code_Option_4=0 x 04入力レジスタを読み込む
Falcon_Bp_ModbusReadEqBp_Input_code_description=Modbusの機能コード
Falcon_Bp_ModbusReadEqBp_Input_code_label=機能コード
Falcon_Bp_ModbusReadEqBp_Input_deviceName_description=デバイス名は、PLCデバイス構成メニューで構成する必要があります
Falcon_Bp_ModbusReadEqBp_Input_deviceName_label=デバイス名
Falcon_Bp_ModbusReadEqBp_Input_maxRetry_description=読み取りに失敗した場合、自動再試行の最大回数は、PLCデバイス構成メニューで設定された最大再試行回数にデフォルト設定されます
Falcon_Bp_ModbusReadEqBp_Input_maxRetry_label=失敗回数の制限
Falcon_Bp_ModbusReadEqBp_Input_readDelay_description=読み取り間隔はデフォルトで1秒です
Falcon_Bp_ModbusReadEqBp_Input_readDelay_label=読み取り間隔
Falcon_Bp_ModbusReadEqBp_Input_readLimit_description=読み取り値が予想と異なる回数制限、デフォルト値-1。記入しない、-1は制限しない
Falcon_Bp_ModbusReadEqBp_Input_readLimit_label=読み取り値が予想と異なる回数制限
Falcon_Bp_ModbusReadEqBp_Input_retryDelay_description=読み取りに失敗した場合、再試行間隔は、PLCデバイス構成メニューで設定された再試行間隔にデフォルトで設定されます
Falcon_Bp_ModbusReadEqBp_Input_retryDelay_label=失敗再試行待機(ミリ秒)
Falcon_Bp_ModbusReadEqBp_Input_slaveId_description=ModbusデバイスID、デフォルトは0です。
Falcon_Bp_ModbusReadEqBp_Input_slaveId_label=スレーブID
Falcon_Bp_ModbusReadEqBp_Input_targetValue_description=目標値
Falcon_Bp_ModbusReadEqBp_Input_targetValue_label=目標値
Falcon_Bp_ModbusReadEqBp_description=Modbusを期待値に等しくなるまで読み取り、ノットイコール場合は最大リトライ回数を超えるまで読み取り続けます
Falcon_Bp_ModbusReadEqBp_label=Modbusは等しいまで読み取ります
Falcon_Bp_ModbusWriteBp_Input_address_description=Modbusの住所
Falcon_Bp_ModbusWriteBp_Input_address_label=アドレス
Falcon_Bp_ModbusWriteBp_Input_code_Option_5=0 x 05単一のコイルを書く
Falcon_Bp_ModbusWriteBp_Input_code_Option_6=0 x 06は単一のレジスタを書き込みます
Falcon_Bp_ModbusWriteBp_Input_code_description=Modbusの機能コード
Falcon_Bp_ModbusWriteBp_Input_code_label=機能コード
Falcon_Bp_ModbusWriteBp_Input_deviceName_description=デバイス名は、PLCデバイス構成メニューで構成する必要があります
Falcon_Bp_ModbusWriteBp_Input_deviceName_label=デバイス名
Falcon_Bp_ModbusWriteBp_Input_maxRetry_description=PLCデバイス構成メニューで設定された最大再試行回数がデフォルトです
Falcon_Bp_ModbusWriteBp_Input_maxRetry_label=最大再試行回数
Falcon_Bp_ModbusWriteBp_Input_retryDelay_description=PLCデバイス構成メニューで設定されたデフォルトの再試行間隔
Falcon_Bp_ModbusWriteBp_Input_retryDelay_label=再試行待機(ミリ秒)
Falcon_Bp_ModbusWriteBp_Input_slaveId_description=ModbusデバイスID、デフォルトは0です。
Falcon_Bp_ModbusWriteBp_Input_slaveId_label=スレーブID
Falcon_Bp_ModbusWriteBp_Input_value_description=値を書き込む
Falcon_Bp_ModbusWriteBp_Input_value_label=値
Falcon_Bp_ModbusWriteBp_description=Modbusに書き込む
Falcon_Bp_ModbusWriteBp_label=Modbusを書く
Falcon_Bp_S7ReadBp_Input_bitOffset_description=ビットオフセット
Falcon_Bp_S7ReadBp_Input_bitOffset_label=ビットオフセット
Falcon_Bp_S7ReadBp_Input_blockType_description=プロトコルの内容に応じて、対応するブロックタイプを選択してください。
Falcon_Bp_S7ReadBp_Input_blockType_label=ブロックタイプ
Falcon_Bp_S7ReadBp_Input_byteOffset_description=バイトオフセットは、アドレス番号として理解できます。
Falcon_Bp_S7ReadBp_Input_byteOffset_label=バイトオフセット
Falcon_Bp_S7ReadBp_Input_dataType_description=契約内容に応じて、対応するデータ型を選択してください。
Falcon_Bp_S7ReadBp_Input_dataType_label=データ型
Falcon_Bp_S7ReadBp_Input_dbId_description=DB番号は、規約内容に応じた数値を入力してください。
Falcon_Bp_S7ReadBp_Input_dbId_label=dbId
Falcon_Bp_S7ReadBp_Input_deviceName_description=PLCデバイス管理で構成されたS 7デバイスの名前を入力します。
Falcon_Bp_S7ReadBp_Input_deviceName_label=デバイス名
Falcon_Bp_S7ReadBp_Input_maxRetry_description=最大再試行回数
Falcon_Bp_S7ReadBp_Input_maxRetry_label=最大再試行回数
Falcon_Bp_S7ReadBp_Input_retryDelay_description=再試行間隔、ミリ秒単位
Falcon_Bp_S7ReadBp_Input_retryDelay_label=再試行待機(ミリ秒)
Falcon_Bp_S7ReadBp_Output_value_description=値
Falcon_Bp_S7ReadBp_Output_value_label=値
Falcon_Bp_S7ReadBp_description=S 7プロトコルを介してPLCデバイスのデータを一度に読み取り、読み取った値をこのコンポーネントの出力パラメータとします。
Falcon_Bp_S7ReadBp_label=S 7読み取り
Falcon_Bp_S7ReadEqBp_Input_bitOffset_description=ビットオフセット
Falcon_Bp_S7ReadEqBp_Input_bitOffset_label=ビットオフセット
Falcon_Bp_S7ReadEqBp_Input_blockType_description=プロトコルの内容に応じて、対応するブロックタイプを選択してください。
Falcon_Bp_S7ReadEqBp_Input_blockType_label=ブロックタイプ
Falcon_Bp_S7ReadEqBp_Input_byteOffset_description=バイトオフセットは、アドレス番号として理解できます。
Falcon_Bp_S7ReadEqBp_Input_byteOffset_label=バイトオフセット
Falcon_Bp_S7ReadEqBp_Input_dataType_description=契約内容に応じて、対応するデータタイプを選択してください。
Falcon_Bp_S7ReadEqBp_Input_dataType_label=データ型
Falcon_Bp_S7ReadEqBp_Input_dbId_description=DB番号は、規約内容に応じた数値を入力してください。
Falcon_Bp_S7ReadEqBp_Input_dbId_label=dbId
Falcon_Bp_S7ReadEqBp_Input_deviceName_description=PLCデバイス管理で構成されたS 7デバイスの名前を入力します。
Falcon_Bp_S7ReadEqBp_Input_deviceName_label=デバイス名
Falcon_Bp_S7ReadEqBp_Input_maxRetry_description=最大再試行回数
Falcon_Bp_S7ReadEqBp_Input_maxRetry_label=最大再試行回数
Falcon_Bp_S7ReadEqBp_Input_readDelay_description=読み取り間隔はデフォルトで1秒です
Falcon_Bp_S7ReadEqBp_Input_readDelay_label=読み取り間隔
Falcon_Bp_S7ReadEqBp_Input_readLimit_description=読み取り値が予想と異なる回数制限、デフォルト値-1。記入しない、-1は制限しない
Falcon_Bp_S7ReadEqBp_Input_readLimit_label=読み取り値が予想と異なる回数制限
Falcon_Bp_S7ReadEqBp_Input_retryDelay_description=読み取りに失敗した場合、再試行間隔は、PLCデバイス構成メニューで設定された再試行間隔にデフォルトで設定されます
Falcon_Bp_S7ReadEqBp_Input_retryDelay_label=失敗再試行待機(ミリ秒)
Falcon_Bp_S7ReadEqBp_Input_value_description=期待値
Falcon_Bp_S7ReadEqBp_Input_value_label=値
Falcon_Bp_S7ReadEqBp_description=S 7プロトコルを介してPLCデバイスのデータを読み取り続け、読み取り値が期待値と同じになるまで停止します。
Falcon_Bp_S7ReadEqBp_label=S7に等しくなるまで読み取る
Falcon_Bp_S7WriteBp_Input_bitOffset_description=ビットオフセット
Falcon_Bp_S7WriteBp_Input_bitOffset_label=ビットオフセット
Falcon_Bp_S7WriteBp_Input_blockType_description=プロトコルの内容に応じて、対応するブロックタイプを選択してください。
Falcon_Bp_S7WriteBp_Input_blockType_label=ブロックタイプ
Falcon_Bp_S7WriteBp_Input_byteOffset_description=バイトオフセットは、アドレス番号として理解できます。
Falcon_Bp_S7WriteBp_Input_byteOffset_label=バイトオフセット
Falcon_Bp_S7WriteBp_Input_dataType_description=契約内容に応じて、対応するデータタイプを選択してください。
Falcon_Bp_S7WriteBp_Input_dataType_label=データ型
Falcon_Bp_S7WriteBp_Input_dbId_description=DB番号は、規約内容に応じた数値を入力してください。
Falcon_Bp_S7WriteBp_Input_dbId_label=dbId
Falcon_Bp_S7WriteBp_Input_deviceName_description=PLCデバイス管理で構成されたS 7デバイスの名前を入力します。
Falcon_Bp_S7WriteBp_Input_deviceName_label=デバイス名
Falcon_Bp_S7WriteBp_Input_maxRetry_description=最大再試行回数
Falcon_Bp_S7WriteBp_Input_maxRetry_label=最大再試行回数
Falcon_Bp_S7WriteBp_Input_retryDelay_description=再試行間隔、ミリ秒単位
Falcon_Bp_S7WriteBp_Input_retryDelay_label=再試行待機(ミリ秒)
Falcon_Bp_S7WriteBp_Input_value_description=目標値
Falcon_Bp_S7WriteBp_Input_value_label=値
Falcon_Bp_S7WriteBp_description=S 7プロトコルを介してPLCデバイスに目標値を書き込みます。
Falcon_Bp_S7WriteBp_label=S 7書き込み
errModbusReadEqNotMatch=ModbusReadEqBp読み取り値が期待と異なる回数が制限を超えた
errS7ReadEqNotMatch=S 7 ReadEqBp読み取り値が期待と異なる回数が制限を超えた

[fleet.base]
RbkApiNoNoSupported=API番号{0}はサポートされていません
RbkClientConnectFail=RBKの接続に失敗しました{0}
RbkClientRequestFail=RBKのリクエストに失敗しました{0}
RobotAlarm=ロボットアラーム
RobotNotExistedById=ロボット"{0}"は存在しません
btnPushMap=プッシュマップ
errSceneMapsPushing=前回のプッシュが完了していないので、前回のプッシュを待つかキャンセルしてください
errorFleetOrderFault=運送状"{0}"の故障。ロボット="{1}"。原因:{2}。
labelFleet=フリート管理
manualLoadTip=手動で完了するには、まず{0}の位置から貨物を取り出してロボットに置き、手動で完了をクリックします。
manualLoadTip1=手動で完了するには、まず{0}の位置から貨物を取り出し、ロボットフォーク(999番)のバックバスケットに置き、ロボットの他のバックバスケットに入れないで、手動で完了をクリックします。
manualLoadTip2=手動で完了するには、まず{0}の位置から貨物を取り出し、ロボットの第{1}層({2}番)のバックバスケットに置き、手動で完了をクリックします。
manualUnloadTip=手動で完了するには、ロボットから貨物を取り出し、{0}の位置に置き、手動で完了をクリックします。
manualUnloadTip1=手動で完了するには、ロボットフォーク（No.999）のバスケットから商品を取り出し、{0}の位置に置き、[手動で完了]をクリックします。
manualUnloadTip2=手動で完了するには、ロボットのレイヤー{0}（{1}）のバスケットから商品を取り出し、{2}の位置に置き、[手動で完了]をクリックします。

[fleet.falcon]
Falcon_BlockGroup_DirectOrder=直接運送伝票
Falcon_BlockGroup_Map=地図
Falcon_BlockGroup_Ndc=NDCスケジューリング伝票
Falcon_BlockGroup_SeerTom=第二世代のスケジューリング運送状
Falcon_BlockGroup_TransportOrder=第三世代のスケジューリング運送状
Falcon_Bp_AddStepAndWaitCompleteBp_Input_binTask_description=binTaskキーを使用して、ライブラリの位置を入力します。
Falcon_Bp_AddStepAndWaitCompleteBp_Input_binTask_label=binTask
Falcon_Bp_AddStepAndWaitCompleteBp_Input_forLoad_description=このステップでピックアップする場合は、正しく設定する必要があります!
Falcon_Bp_AddStepAndWaitCompleteBp_Input_forLoad_label=このステップで商品を受け取る
Falcon_Bp_AddStepAndWaitCompleteBp_Input_forUnload_description=このステップで商品を置く場合は、正しく設定する必要があります!
Falcon_Bp_AddStepAndWaitCompleteBp_Input_forUnload_label=このステップでアンロードします
Falcon_Bp_AddStepAndWaitCompleteBp_Input_location_description=ポイントまたはライブラリのビット
Falcon_Bp_AddStepAndWaitCompleteBp_Input_location_label=ポイントまたはライブラリのビット
Falcon_Bp_AddStepAndWaitCompleteBp_Input_nextStepSameOrder_description=マルチロードロボット、このパラメータがtrueの場合、このステップが完了し、次のステップは同じ運送状でなければならず、他のロボットの運送状を実行することは許可されていません
Falcon_Bp_AddStepAndWaitCompleteBp_Input_nextStepSameOrder_label=強制的に次のステップは同じ運送状でなければならない
Falcon_Bp_AddStepAndWaitCompleteBp_Input_operationArgs_description=アクションパラメータ、JSON形式、例:{"endheight": 0.1}は、フォークが0.1 mの高さまで上昇することを示します
Falcon_Bp_AddStepAndWaitCompleteBp_Input_operationArgs_label=rbkArgs
Falcon_Bp_AddStepAndWaitCompleteBp_Input_operation_description=機構アクション名。JackLoad、ForkLoadなど
Falcon_Bp_AddStepAndWaitCompleteBp_Input_operation_label=オペレーション
Falcon_Bp_AddStepAndWaitCompleteBp_Input_orderId_description=ステップが属する運送状番号
Falcon_Bp_AddStepAndWaitCompleteBp_Input_orderId_label=運送状番号
Falcon_Bp_AddStepAndWaitCompleteBp_Input_withdrawOrderAllowed_description=許可されていれば、ロボットはこのステップを実行する過程で(実際に商品を受け取る前に)、より適切な運送状を受け取ることができる
Falcon_Bp_AddStepAndWaitCompleteBp_Input_withdrawOrderAllowed_label=再割り当てを許可する
Falcon_Bp_AddStepAndWaitCompleteBp_Output_robotName_description=ロボット名
Falcon_Bp_AddStepAndWaitCompleteBp_Output_robotName_label=ロボット名
Falcon_Bp_AddStepAndWaitCompleteBp_Output_stepId_description=ステップID
Falcon_Bp_AddStepAndWaitCompleteBp_Output_stepId_label=ステップID
Falcon_Bp_AddStepAndWaitCompleteBp_description=3世代の運送状ステップを追加し、運送状ステップが完了するのを待つ
Falcon_Bp_AddStepAndWaitCompleteBp_label=3世代の運送状ステップを追加し、完了を待つ
Falcon_Bp_AddTomBlockBp_Input_binTask_description=binTask
Falcon_Bp_AddTomBlockBp_Input_binTask_label=binTask
Falcon_Bp_AddTomBlockBp_Input_goodsId_description=コンテナ番号
Falcon_Bp_AddTomBlockBp_Input_goodsId_label=コンテナ番号
Falcon_Bp_AddTomBlockBp_Input_location_description=宛先名、サイト名、またはライブラリ
Falcon_Bp_AddTomBlockBp_Input_location_label=サイト
Falcon_Bp_AddTomBlockBp_Input_nextLocation_description=現在のブロックが前置点の場合、実際に移動するライブラリの番号
Falcon_Bp_AddTomBlockBp_Input_nextLocation_label=次のターゲットポイント
Falcon_Bp_AddTomBlockBp_Input_operation_description=実行機構アクション
Falcon_Bp_AddTomBlockBp_Input_operation_label=アクション
Falcon_Bp_AddTomBlockBp_Input_orderId_description=【第二世代スケジューリング運送状を作成する】ブロック生成の「運送状番号」
Falcon_Bp_AddTomBlockBp_Input_orderId_label=運送状番号
Falcon_Bp_AddTomBlockBp_Input_tomId_description=「ロボットアプリケーション」ページの「第二世代スケジューリング」のシーン名
Falcon_Bp_AddTomBlockBp_Input_tomId_label=スケジューリングID
Falcon_Bp_AddTomBlockBp_Output_blockId_description=スケジューリング中のアクションのブロックID
Falcon_Bp_AddTomBlockBp_Output_blockId_label=ブロックID
Falcon_Bp_AddTomBlockBp_description=アクションブロックを追加
Falcon_Bp_AddTomBlockBp_label=第二世代の輸送ブロックを追加する
Falcon_Bp_AddTransportStepBp_Input_binTask_description=binTaskキーを使用して、ライブラリの位置を入力します。
Falcon_Bp_AddTransportStepBp_Input_binTask_label=binTask
Falcon_Bp_AddTransportStepBp_Input_forLoad_description=このステップでピックアップする場合は、正しく設定する必要があります!
Falcon_Bp_AddTransportStepBp_Input_forLoad_label=このステップで商品を受け取る
Falcon_Bp_AddTransportStepBp_Input_forUnload_description=このステップで商品を置く場合は、正しく設定する必要があります!
Falcon_Bp_AddTransportStepBp_Input_forUnload_label=このステップでアンロードします
Falcon_Bp_AddTransportStepBp_Input_location_description=ポイントまたはライブラリのビット
Falcon_Bp_AddTransportStepBp_Input_location_label=ポイントまたはライブラリのビット
Falcon_Bp_AddTransportStepBp_Input_nextStepSameOrder_description=マルチロードロボット、このパラメータがtrueの場合、このステップが完了し、次のステップは同じ運送状でなければならず、他のロボットの運送状を実行することは許可されていません
Falcon_Bp_AddTransportStepBp_Input_nextStepSameOrder_label=強制的に次のステップは同じ運送状でなければならない
Falcon_Bp_AddTransportStepBp_Input_operationArgs_description=アクションパラメータ、JSON形式、例:{"endheight": 0.1}は、フォークが0.1 mの高さまで上昇することを示します
Falcon_Bp_AddTransportStepBp_Input_operationArgs_label=rbkArgs
Falcon_Bp_AddTransportStepBp_Input_operation_description=機構アクション名。JackLoad、ForkLoadなど
Falcon_Bp_AddTransportStepBp_Input_operation_label=オペレーション
Falcon_Bp_AddTransportStepBp_Input_orderId_description=ステップが属する運送状番号
Falcon_Bp_AddTransportStepBp_Input_orderId_label=運送状番号
Falcon_Bp_AddTransportStepBp_Input_withdrawOrderAllowed_description=許可されていれば、ロボットはこのステップを実行する過程で(実際に商品を受け取る前に)、より適切な運送状を受け取ることができる
Falcon_Bp_AddTransportStepBp_Input_withdrawOrderAllowed_label=再割り当てを許可する
Falcon_Bp_AddTransportStepBp_Output_stepId_description=ステップID
Falcon_Bp_AddTransportStepBp_Output_stepId_label=ステップID
Falcon_Bp_AddTransportStepBp_description=3世代の運送伝票ステップだけを追加し、「3世代の運送伝票ステップが完了するのを待つ」と組み合わせて使用する
Falcon_Bp_AddTransportStepBp_label=第三世代の運送伝票を追加する手順
Falcon_Bp_AllowNdcLoadBp_Input_orderId_label=運送状番号
Falcon_Bp_AllowNdcLoadBp_description=無効中
Falcon_Bp_AllowNdcLoadBp_label=NDCのロードを許可する
Falcon_Bp_AllowNdcUnloadBp_Input_orderId_label=運送状番号
Falcon_Bp_AllowNdcUnloadBp_description=無効中
Falcon_Bp_AllowNdcUnloadBp_label=NDCのアンロードを許可する
Falcon_Bp_CompleteTomOrderBp_Input_orderId_description=【第二世代スケジューリング運送状を作成する】ブロック生成の「運送状番号」
Falcon_Bp_CompleteTomOrderBp_Input_orderId_label=運送状番号
Falcon_Bp_CompleteTomOrderBp_Input_tomId_description=「ロボットアプリケーション」ページの「第二世代スケジューリング」のシーン名
Falcon_Bp_CompleteTomOrderBp_Input_tomId_label=スケジューリングID
Falcon_Bp_CompleteTomOrderBp_description=封をする
Falcon_Bp_CompleteTomOrderBp_label=第二世代のスケジューリング運送状を終了する
Falcon_Bp_CompleteTransportOrderBp_Input_orderId_description=運送状番号
Falcon_Bp_CompleteTransportOrderBp_Input_orderId_label=運送状番号
Falcon_Bp_CompleteTransportOrderBp_description=第三世代の運送伝票を終了する
Falcon_Bp_CompleteTransportOrderBp_label=第三世代の運送伝票を終了する
Falcon_Bp_CreateNdcOrderBp_Input_endBin_description=NDCのライブラリビット、ショートタイプ
Falcon_Bp_CreateNdcOrderBp_Input_endBin_label=終点
Falcon_Bp_CreateNdcOrderBp_Input_priority_label=優先度
Falcon_Bp_CreateNdcOrderBp_Input_startBin_description=NDCのライブラリビット、ショートタイプ
Falcon_Bp_CreateNdcOrderBp_Input_startBin_label=スタート
Falcon_Bp_CreateNdcOrderBp_Output_orderId_description=NDC運送状の追跡番号
Falcon_Bp_CreateNdcOrderBp_Output_orderId_label=運送状番号
Falcon_Bp_CreateNdcOrderBp_description=M 4は始点、終点、優先度を一緒にNDCに送り、NDCは自動的に実行し、NDCは取得、解放、タスク完了後にM 4に報告します
Falcon_Bp_CreateNdcOrderBp_label=NDC運送状の作成
Falcon_Bp_CreateTomOrderBp_Input_group_description=ロボットグループを指定し、車両選択時にこのロボットグループに属するロボットを割り当てて実行する
Falcon_Bp_CreateTomOrderBp_Input_group_label=指定されたロボットグループ
Falcon_Bp_CreateTomOrderBp_Input_keyGoodsId_description=材料箱車n+1のシーンに対して、補助coreの最後の集荷を最初に置く
Falcon_Bp_CreateTomOrderBp_Input_keyGoodsId_label=keyGoodsId
Falcon_Bp_CreateTomOrderBp_Input_keyRouteStr_description=重要なポイントは、ディスパッチロボットの決定を支援するために使用されます記入しないと、システムは現在の運転状況に基づいて適切なロボットディスパッチを自動的に選択します
Falcon_Bp_CreateTomOrderBp_Input_keyRouteStr_label=重要な位置
Falcon_Bp_CreateTomOrderBp_Input_keyTask_description=「load」または「unload」の場合、他のフィールドを記入すると自動的に無視され、ディスパッチロボットの決定を支援します記入しない場合、システムは現在の実行状況に基づいて適切なロボットディスパッチを自動的に選択します
Falcon_Bp_CreateTomOrderBp_Input_keyTask_label=keyTask
Falcon_Bp_CreateTomOrderBp_Input_label_description=ロボットラベルを指定し、現在の運送状について特定のラベルのロボットを指定するようシステムに要求します
Falcon_Bp_CreateTomOrderBp_Input_label_label=ラベル
Falcon_Bp_CreateTomOrderBp_Input_loadBlockCount_description=存在し、0より大きい場合は、注文が実行するために少なくともX個の空のバスケットが必要であることを示します。つまり、注文にはX個のピックアップアクションブロックがあります
Falcon_Bp_CreateTomOrderBp_Input_loadBlockCount_label=ピックアップブロック数
Falcon_Bp_CreateTomOrderBp_Input_notMarkComplete_description=ファルコンのタスクをキャンセルし、出荷伝票を封印せずにスケジュールします。
Falcon_Bp_CreateTomOrderBp_Input_notMarkComplete_label=タスクをキャンセルし、運送状を封印しないでください。
Falcon_Bp_CreateTomOrderBp_Input_priority_description=運送状の優先度は、数字が大きいほど注文の優先度が高いことを示します。
Falcon_Bp_CreateTomOrderBp_Input_priority_label=優先度
Falcon_Bp_CreateTomOrderBp_Input_tomId_description=「ロボットアプリケーション管理」ページの「第二世代スケジューリング」のシーン名
Falcon_Bp_CreateTomOrderBp_Input_tomId_label=シーン名
Falcon_Bp_CreateTomOrderBp_Input_vehicle_description=指定されたロボットを選択するときに指定されたロボットを割り当てて実行します
Falcon_Bp_CreateTomOrderBp_Input_vehicle_label=指定ロボット
Falcon_Bp_CreateTomOrderBp_Output_actualVehicle_description=第二世代のスケジューリング割り当てロボット
Falcon_Bp_CreateTomOrderBp_Output_actualVehicle_label=割り当てられたロボット
Falcon_Bp_CreateTomOrderBp_Output_orderId_description=第二世代のスケジューリング運送番号
Falcon_Bp_CreateTomOrderBp_Output_orderId_label=運送状番号
Falcon_Bp_CreateTomOrderBp_description=第二世代のスケジューリング運送状、つまりコアスケジューリングを作成する
Falcon_Bp_CreateTomOrderBp_label=第2世代のスケジューリング運送状を作成する
Falcon_Bp_CreateTransportOrderBp_Input_containerDir_description=コンテナの方向、単位は角度です
Falcon_Bp_CreateTransportOrderBp_Input_containerDir_label=コンテナの方向
Falcon_Bp_CreateTransportOrderBp_Input_containerId_description=コンテナ番号
Falcon_Bp_CreateTransportOrderBp_Input_containerId_label=コンテナ番号
Falcon_Bp_CreateTransportOrderBp_Input_containerTypeName_description=コンテナーの種類の名前
Falcon_Bp_CreateTransportOrderBp_Input_containerTypeName_label=コンテナータイプ名
Falcon_Bp_CreateTransportOrderBp_Input_expectedRobotGroups_description=コンマで区切って複数指定できる実行対象のマシングループ
Falcon_Bp_CreateTransportOrderBp_Input_expectedRobotGroups_label=マシングループの実行を期待しています。
Falcon_Bp_CreateTransportOrderBp_Input_expectedRobotNames_description=コンマで区切られた複数のボットを指定できます
Falcon_Bp_CreateTransportOrderBp_Input_expectedRobotNames_label=ロボットの実行を楽しみにしています
Falcon_Bp_CreateTransportOrderBp_Input_keyLocations_description=ディスパッチボットの決定を支援するために、1つまたは複数のサイトまたはライブラリ名をカンマで区切って入力します
Falcon_Bp_CreateTransportOrderBp_Input_keyLocations_label=重要な位置
Falcon_Bp_CreateTransportOrderBp_Input_priority_description=整数、数字が大きいほど優先度が高い
Falcon_Bp_CreateTransportOrderBp_Input_priority_label=優先度
Falcon_Bp_CreateTransportOrderBp_Input_sceneName_description=三世代スケジューリングのシーン名は必須ではなく、デフォルトで最初のシーン名を取ります
Falcon_Bp_CreateTransportOrderBp_Input_sceneName_label=シーン名
Falcon_Bp_CreateTransportOrderBp_Output_orderId_description=ステップが属する運送状番号
Falcon_Bp_CreateTransportOrderBp_Output_orderId_label=運送状番号
Falcon_Bp_CreateTransportOrderBp_Output_robotName_description=ロボット名
Falcon_Bp_CreateTransportOrderBp_Output_robotName_label=ロボット名
Falcon_Bp_CreateTransportOrderBp_description=第三世代のスケジューリング運送状を作成する
Falcon_Bp_CreateTransportOrderBp_label=第三世代のスケジューリング運送状を作成する
Falcon_Bp_DirectOrderExecuteBp_Input_desc_label=説明する
Falcon_Bp_DirectOrderExecuteBp_Input_fillMiddleLandmarks_description=「ダイレクト伝票ステップ」の間に他のサイトがある場合は、中間サイトをダイレクト伝票に入力します
Falcon_Bp_DirectOrderExecuteBp_Input_fillMiddleLandmarks_label=中間点の塗りつぶし
Falcon_Bp_DirectOrderExecuteBp_Input_robotId_label=ロボット番号
Falcon_Bp_DirectOrderExecuteBp_Input_sceneName_description=「ロボットアプリケーション管理」(2.5世代スケジューリング)のシーン名、または「スケジューリングシーン」(3世代スケジューリング)のシーン名を入力します
Falcon_Bp_DirectOrderExecuteBp_Input_sceneName_label=シーン名
Falcon_Bp_DirectOrderExecuteBp_Input_seer3066_description=3066コマンドを使用して、パスナビゲーションを指定します。
Falcon_Bp_DirectOrderExecuteBp_Input_seer3066_label=3066命令を有効にする
Falcon_Bp_DirectOrderExecuteBp_Input_taskId_label=タスク番号
Falcon_Bp_DirectOrderExecuteBp_Input_unlockCurrentDirectSiteIds_description=デフォルトはfalseで、そのロボットがロックされているリソースをロック解除することを表し、trueはその直接運送伝票がロックされているリソースをロック解除することを表します
Falcon_Bp_DirectOrderExecuteBp_Input_unlockCurrentDirectSiteIds_label=リソースをロック解除する際、直接運送状でロックされたリソースだけをロック解除するかどうか
Falcon_Bp_DirectOrderExecuteBp_Output_orderId_description=「直接運送状」の伝票番号
Falcon_Bp_DirectOrderExecuteBp_Output_orderId_label=注文番号
Falcon_Bp_DirectOrderExecuteBp_description=「ダイレクト運送状」を作成し、実行が完了するのを待つ(重複して作成されない)
Falcon_Bp_DirectOrderExecuteBp_label=直接運送状を実行する
Falcon_Bp_DirectOrderMoveBp_Input_binTask_label=binTask
Falcon_Bp_DirectOrderMoveBp_Input_containerId_label=コンテナ番号
Falcon_Bp_DirectOrderMoveBp_Input_id_description=宛先名、サイト名、またはライブラリ
Falcon_Bp_DirectOrderMoveBp_Input_id_label=サイト
Falcon_Bp_DirectOrderMoveBp_Input_operation_description=実行機構アクション
Falcon_Bp_DirectOrderMoveBp_Input_operation_label=アクション
Falcon_Bp_DirectOrderMoveBp_description=直接運送手順
Falcon_Bp_DirectOrderMoveBp_label=直接運送手順
Falcon_Bp_MapPointBinBp_Input_pointId_label=サイト
Falcon_Bp_MapPointBinBp_Output_binId_label=ライブラリビット
Falcon_Bp_MapPointBinBp_Output_found_label=サイトにはライブラリが割り当てられています。
Falcon_Bp_MapPointBinBp_label=サイトのライブラリ
Falcon_Bp_WaitStepCompleteBp_Input_stepId_description=運送状のステップID
Falcon_Bp_WaitStepCompleteBp_Input_stepId_label=ステップID
Falcon_Bp_WaitStepCompleteBp_Output_robotName_description=ロボット名
Falcon_Bp_WaitStepCompleteBp_Output_robotName_label=ロボット名
Falcon_Bp_WaitStepCompleteBp_description=三世代運送伝票ステップの実行が完了するのを待って、「三世代運送伝票ステップを追加する」と組み合わせて使用する
Falcon_Bp_WaitStepCompleteBp_label=三世代の運送手順が完了するのを待つ
Falcon_Bp_WaitUntilNdcArriveEndBinBp_Input_orderId_label=運送状番号
Falcon_Bp_WaitUntilNdcArriveEndBinBp_description=無効中
Falcon_Bp_WaitUntilNdcArriveEndBinBp_label=NDCがフィニッシュラインに到達するのを待つ
Falcon_Bp_WaitUntilNdcArriveStartBinBp_Input_orderId_label=運送状番号
Falcon_Bp_WaitUntilNdcArriveStartBinBp_description=無効中
Falcon_Bp_WaitUntilNdcArriveStartBinBp_label=NDCが出発点に到着するのを待つ
Falcon_Bp_WaitUntilNdcFinishBp_Input_orderId_label=運送状番号
Falcon_Bp_WaitUntilNdcFinishBp_label=NDCタスクの完了を待つ
Falcon_Bp_WaitUntilNdcLoadedBp_Input_orderId_label=運送状番号
Falcon_Bp_WaitUntilNdcLoadedBp_label=NDCの積み込みが完了するのを待つ
Falcon_Bp_WaitUntilNdcUnloadedBp_Input_orderId_label=運送状番号
Falcon_Bp_WaitUntilNdcUnloadedBp_label=NDCのアンロードが完了するのを待つ

[fleet.single.base]
relocStatus_0=位置情報の失敗
relocStatus_1=位置が正しい
relocStatus_2=再配置中
relocStatus_3=位置決め完了
relocStatus_null=位置情報の状態が不明です

[fleet.single.falcon]
Falcon_BlockGroup_RobotSingleControl=サイクリングコントロール
Falcon_Bp_RobotReadDIBp_Input_id_description=DIの番号を指定します
Falcon_Bp_RobotReadDIBp_Input_id_label=DI番号
Falcon_Bp_RobotReadDIBp_Input_tomId_description=「ロボットアプリケーション」ページの「第二世代スケジューリング」のシーン名
Falcon_Bp_RobotReadDIBp_Input_tomId_label=スケジューリングID
Falcon_Bp_RobotReadDIBp_Input_vehicle_description=指定ロボット
Falcon_Bp_RobotReadDIBp_Input_vehicle_label=指定ロボット
Falcon_Bp_RobotReadDIBp_Output_result_label=読み取り結果
Falcon_Bp_RobotReadDIBp_description=指定したロボットのDIを読み取る
Falcon_Bp_RobotReadDIBp_label=DIの読み取り
Falcon_Bp_RobotSetDOBp_Input_id_description=DOの番号を指定します
Falcon_Bp_RobotSetDOBp_Input_id_label=DO番号
Falcon_Bp_RobotSetDOBp_Input_status_description=指定したDOの状態をその状態に変更します
Falcon_Bp_RobotSetDOBp_Input_status_label=ターゲットの状態
Falcon_Bp_RobotSetDOBp_Input_tomId_description=「ロボットアプリケーション」ページの「第二世代スケジューリング」のシーン名
Falcon_Bp_RobotSetDOBp_Input_tomId_label=スケジューリングID
Falcon_Bp_RobotSetDOBp_Input_vehicle_description=指定ロボット
Falcon_Bp_RobotSetDOBp_Input_vehicle_label=指定ロボット
Falcon_Bp_RobotSetDOBp_Output_result_description=実行後の応答結果
Falcon_Bp_RobotSetDOBp_Output_result_label=resultリクエストの結果
Falcon_Bp_RobotSetDOBp_description=指定されたDOの状態を変更するために使用されます
Falcon_Bp_RobotSetDOBp_label=DOの設定
Falcon_Bp_RobotSingleAngleRadianBp_Input_conversionType_Option_AngleToRadian=角度をラジアンに変換
Falcon_Bp_RobotSingleAngleRadianBp_Input_conversionType_Option_RadianToAngle=ラジアン回転角度
Falcon_Bp_RobotSingleAngleRadianBp_Input_conversionType_description=変換タイプを選択し、値を目的の度またはラジアンに変換します。
Falcon_Bp_RobotSingleAngleRadianBp_Input_conversionType_label=変換タイプ
Falcon_Bp_RobotSingleAngleRadianBp_Input_degrees_description=変換タイプを選択し、値を目的の度またはラジアンに変換します。
Falcon_Bp_RobotSingleAngleRadianBp_Input_degrees_label=数値
Falcon_Bp_RobotSingleAngleRadianBp_Output_degrees_description=変換された度、またはラジアンの数。
Falcon_Bp_RobotSingleAngleRadianBp_Output_degrees_label=結果
Falcon_Bp_RobotSingleAngleRadianBp_description=角度とラジアンの間の相互変換
Falcon_Bp_RobotSingleAngleRadianBp_label=ラジアンと角度の変換
Falcon_Bp_RobotSingleForkBp_Input_endHeight_description=フォーク調整の最終高さ、デフォルト値0.500（単位: m）
Falcon_Bp_RobotSingleForkBp_Input_endHeight_label=終わりの高さ
Falcon_Bp_RobotSingleForkBp_Input_forkDist_description=フォワードフォークの場合、フォークが前方に移動する距離は、デフォルト値0.00(単位: m)です
Falcon_Bp_RobotSingleForkBp_Input_forkDist_label=前方移動距離（前方フォーク）
Falcon_Bp_RobotSingleForkBp_Input_forkMidHeight_description=歩行時のフォークの高さ、デフォルト値0.100（単位: m）
Falcon_Bp_RobotSingleForkBp_Input_forkMidHeight_label=歩く高さ
Falcon_Bp_RobotSingleForkBp_Input_operation_Option_ForkForward=フォークフォワード
Falcon_Bp_RobotSingleForkBp_Input_operation_Option_ForkHeight=フォークトップの高さ（ForkHeight）
Falcon_Bp_RobotSingleForkBp_Input_operation_Option_ForkLoad=フォークピックアップ（ForkLoad）
Falcon_Bp_RobotSingleForkBp_Input_operation_Option_ForkUnload=フォークドロップ（ForkUnload）
Falcon_Bp_RobotSingleForkBp_Input_operation_description=フォーク機構の操作可能な動作
Falcon_Bp_RobotSingleForkBp_Input_operation_label=アクション
Falcon_Bp_RobotSingleForkBp_Input_recfile_description=ロボットが特定の動作を実行する場合、この文書を用いて認識比較などを行う必要がある
Falcon_Bp_RobotSingleForkBp_Input_recfile_label=認識文書
Falcon_Bp_RobotSingleForkBp_Input_reqId_description=コンポーネント実行時の識別子、オプション
Falcon_Bp_RobotSingleForkBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleForkBp_Input_startHeight_description=フォークの開始高さ、デフォルト値0.100（単位: m）
Falcon_Bp_RobotSingleForkBp_Input_startHeight_label=開始の高さ
Falcon_Bp_RobotSingleForkBp_Input_station_description=ターゲットサイトをナビゲートします
Falcon_Bp_RobotSingleForkBp_Input_station_label=ターゲットサイト
Falcon_Bp_RobotSingleForkBp_Output_rbkResult_description=実行後の応答結果
Falcon_Bp_RobotSingleForkBp_Output_rbkResult_label=RBKリクエストの結果
Falcon_Bp_RobotSingleForkBp_description=フォーク貨物機構を操作する
Falcon_Bp_RobotSingleForkBp_label=フォーク/リリース
Falcon_Bp_RobotSingleGetAllStatusBp_Output_status_description=ロボットのすべての状態、データ型はJsonObjectです
Falcon_Bp_RobotSingleGetAllStatusBp_Output_status_label=状態
Falcon_Bp_RobotSingleGetAllStatusBp_description=ロボット1100インターフェースの元のレポートを取得します
Falcon_Bp_RobotSingleGetAllStatusBp_label=ロボットのすべての状態を取得する
Falcon_Bp_RobotSingleGetOneStatusBp_Input_status_Option_batteryLevel=バッテリー残量
Falcon_Bp_RobotSingleGetOneStatusBp_Input_status_Option_charging=充電状態
Falcon_Bp_RobotSingleGetOneStatusBp_Input_status_Option_currentStation=現在のサイト
Falcon_Bp_RobotSingleGetOneStatusBp_Input_status_description=ロボットの状態、例えば、電力量、現在のポイント
Falcon_Bp_RobotSingleGetOneStatusBp_Input_status_label=状態
Falcon_Bp_RobotSingleGetOneStatusBp_Output_status_description=ロボットの指定状態
Falcon_Bp_RobotSingleGetOneStatusBp_Output_status_label=状態
Falcon_Bp_RobotSingleGetOneStatusBp_description=入力パラメータに基づいてロボットの指定状態を返す
Falcon_Bp_RobotSingleGetOneStatusBp_label=ロボットの指定状態を取得する
Falcon_Bp_RobotSingleJackingBp_Input_jackHeight_description=ジャッキ機構を持ち上げる高さ、デフォルト値0.010（単位: m）
Falcon_Bp_RobotSingleJackingBp_Input_jackHeight_label=ジャッキアップの高さ
Falcon_Bp_RobotSingleJackingBp_Input_operation_Option_JackLoad=ジャックロード（JackLoad）
Falcon_Bp_RobotSingleJackingBp_Input_operation_Option_JackUnload=置く（JackUnload）
Falcon_Bp_RobotSingleJackingBp_Input_operation_description=ジャッキ機構の操作可能な動作
Falcon_Bp_RobotSingleJackingBp_Input_operation_label=アクション
Falcon_Bp_RobotSingleJackingBp_Input_recfile_description=ロボットが特定の動作を実行する場合、この文書を用いて認識比較などを行う必要がある
Falcon_Bp_RobotSingleJackingBp_Input_recfile_label=認識文書
Falcon_Bp_RobotSingleJackingBp_Input_reqId_description=コンポーネント実行時の識別子、オプション
Falcon_Bp_RobotSingleJackingBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleJackingBp_Input_station_description=ターゲットサイトをナビゲートします
Falcon_Bp_RobotSingleJackingBp_Input_station_label=ターゲットサイト
Falcon_Bp_RobotSingleJackingBp_Input_useDownPgv_description=下視PGVを使用する
Falcon_Bp_RobotSingleJackingBp_Input_useDownPgv_label=下視PGVを使用する
Falcon_Bp_RobotSingleJackingBp_Input_usePgv_description=上視PGVを使用する
Falcon_Bp_RobotSingleJackingBp_Input_usePgv_label=上視PGVを使用する
Falcon_Bp_RobotSingleJackingBp_Output_rbkResult_description=実行後の応答結果
Falcon_Bp_RobotSingleJackingBp_Output_rbkResult_label=RBKリクエストの結果
Falcon_Bp_RobotSingleJackingBp_description=ジャッキ機構の操作
Falcon_Bp_RobotSingleJackingBp_label=昇進する
Falcon_Bp_RobotSingleNavigationBp_Input_reqId_description=コンポーネント実行時の識別子、オプション
Falcon_Bp_RobotSingleNavigationBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleNavigationBp_Input_station_description=ターゲットサイトをナビゲートします
Falcon_Bp_RobotSingleNavigationBp_Input_station_label=ターゲットサイト
Falcon_Bp_RobotSingleNavigationBp_Output_rbkResult_description=実行後の応答結果
Falcon_Bp_RobotSingleNavigationBp_Output_rbkResult_label=RBKリクエストの結果
Falcon_Bp_RobotSingleNavigationBp_description=ロボットをターゲットサイトに移動します
Falcon_Bp_RobotSingleNavigationBp_label=パスナビゲーション
Falcon_Bp_RobotSinglePlayAudioBp_Input_audioFilename_description=ボットの再生を指定するオーディオ文書
Falcon_Bp_RobotSinglePlayAudioBp_Input_audioFilename_label=オーディオ文書名
Falcon_Bp_RobotSinglePlayAudioBp_Input_loop_description=ループ再生にチェックを入れると、オーディオが繰り返し再生されます
Falcon_Bp_RobotSinglePlayAudioBp_Input_loop_label=ループ再生
Falcon_Bp_RobotSinglePlayAudioBp_Input_reqId_description=コンポーネント実行時の識別子、オプション
Falcon_Bp_RobotSinglePlayAudioBp_Input_reqId_label=ID
Falcon_Bp_RobotSinglePlayAudioBp_Input_station_description=ターゲットサイトをナビゲートします
Falcon_Bp_RobotSinglePlayAudioBp_Input_station_label=ターゲットサイト
Falcon_Bp_RobotSinglePlayAudioBp_Output_rbkResult_description=実行後の応答結果
Falcon_Bp_RobotSinglePlayAudioBp_Output_rbkResult_label=RBKリクエストの結果
Falcon_Bp_RobotSinglePlayAudioBp_description=ロボットを制御してオーディオを再生する
Falcon_Bp_RobotSinglePlayAudioBp_label=オーディオを再生
Falcon_Bp_RobotSingleRollerBp_Input_direction_Option_back=後で
Falcon_Bp_RobotSingleRollerBp_Input_direction_Option_front=前に
Falcon_Bp_RobotSingleRollerBp_Input_direction_Option_left=左
Falcon_Bp_RobotSingleRollerBp_Input_direction_Option_right=右
Falcon_Bp_RobotSingleRollerBp_Input_direction_description=ローラー機構の転がり方向
Falcon_Bp_RobotSingleRollerBp_Input_direction_label=方向
Falcon_Bp_RobotSingleRollerBp_Input_operation_Option_RollerLoad=ローラーアンロード（RollerUnload）
Falcon_Bp_RobotSingleRollerBp_Input_operation_Option_RollerUnload=ローラーロード（RollerLoad）
Falcon_Bp_RobotSingleRollerBp_Input_operation_description=ローラー機構の動作可能な動作
Falcon_Bp_RobotSingleRollerBp_Input_operation_label=アクション
Falcon_Bp_RobotSingleRollerBp_Input_reqId_description=コンポーネント実行時の識別子、オプション
Falcon_Bp_RobotSingleRollerBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleRollerBp_Input_station_description=ターゲットサイトをナビゲートします
Falcon_Bp_RobotSingleRollerBp_Input_station_label=ターゲットサイト
Falcon_Bp_RobotSingleRollerBp_Output_rbkResult_description=実行後の応答結果
Falcon_Bp_RobotSingleRollerBp_Output_rbkResult_label=RBKリクエストの結果
Falcon_Bp_RobotSingleRollerBp_description=操作ローラー機構
Falcon_Bp_RobotSingleRollerBp_label=ローラー
Falcon_Bp_RobotSingleRotateBp_Input_angle_description=ロボットの回転角度を制御する
Falcon_Bp_RobotSingleRotateBp_Input_angle_label=回転角度（°）
Falcon_Bp_RobotSingleRotateBp_Input_mode_description=マイレージモードはマイレージに基づいて移動し、測位モードは正確に測位する必要があり、デフォルトではマイレージモードにデフォルト設定されています
Falcon_Bp_RobotSingleRotateBp_Input_mode_label=モード
Falcon_Bp_RobotSingleRotateBp_Input_reqId_description=コンポーネント実行時の識別子、オプション
Falcon_Bp_RobotSingleRotateBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleRotateBp_Input_vw_description=ロボットの回転速度を制御します。デフォルト値は45.0(°/s)です
Falcon_Bp_RobotSingleRotateBp_Input_vw_label=回転角速度（°/s）
Falcon_Bp_RobotSingleRotateBp_Output_rbkResult_description=実行後の応答結果
Falcon_Bp_RobotSingleRotateBp_Output_rbkResult_label=RBKリクエストの結果
Falcon_Bp_RobotSingleRotateBp_description=ロボットをその場で回転させる
Falcon_Bp_RobotSingleRotateBp_label=回転する
Falcon_Bp_RobotSingleSetDOBp_Input_id_description=DOの番号を指定します
Falcon_Bp_RobotSingleSetDOBp_Input_id_label=DO番号
Falcon_Bp_RobotSingleSetDOBp_Input_status_Option_status_0=0（無効）
Falcon_Bp_RobotSingleSetDOBp_Input_status_Option_status_1=1（有効）
Falcon_Bp_RobotSingleSetDOBp_Input_status_description=指定したDOの状態をその状態に変更します
Falcon_Bp_RobotSingleSetDOBp_Input_status_label=ターゲットの状態
Falcon_Bp_RobotSingleSetDOBp_Output_rbkResult_description=実行後の応答結果
Falcon_Bp_RobotSingleSetDOBp_Output_rbkResult_label=RBKリクエストの結果
Falcon_Bp_RobotSingleSetDOBp_description=指定されたDOの状態を変更するために使用されます
Falcon_Bp_RobotSingleSetDOBp_label=DOの設定
Falcon_Bp_RobotSingleStopAudioBp_Input_reqId_description=コンポーネント実行時の識別子、オプション
Falcon_Bp_RobotSingleStopAudioBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleStopAudioBp_Input_station_description=ターゲットサイトをナビゲートします
Falcon_Bp_RobotSingleStopAudioBp_Input_station_label=ターゲットサイト
Falcon_Bp_RobotSingleStopAudioBp_Output_rbkResult_description=実行後の応答結果
Falcon_Bp_RobotSingleStopAudioBp_Output_rbkResult_label=RBKリクエストの結果
Falcon_Bp_RobotSingleStopAudioBp_description=制御ロボットは、オーディオの再生を停止します
Falcon_Bp_RobotSingleStopAudioBp_label=再生を停止
Falcon_Bp_RobotSingleTranslationBp_Input_dist_description=ロボットの移動距離を制限するために使用されます
Falcon_Bp_RobotSingleTranslationBp_Input_dist_label=直線移動距離（単位: m）
Falcon_Bp_RobotSingleTranslationBp_Input_mode_Option_0=マイレージモード
Falcon_Bp_RobotSingleTranslationBp_Input_mode_Option_1=位置モード
Falcon_Bp_RobotSingleTranslationBp_Input_mode_description=マイレージモードはマイレージに基づいて移動し、測位モードは正確に測位する必要があり、デフォルトではマイレージモードにデフォルト設定されています
Falcon_Bp_RobotSingleTranslationBp_Input_mode_label=モード
Falcon_Bp_RobotSingleTranslationBp_Input_reqId_description=コンポーネント実行時の識別子、オプション
Falcon_Bp_RobotSingleTranslationBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleTranslationBp_Input_vx_description=前後の移動速度を制御します。負の値は後退、正の値は前進、デフォルト値は0.5(単位: m/s)です
Falcon_Bp_RobotSingleTranslationBp_Input_vx_label=前進速度（vx、単位: m/s）
Falcon_Bp_RobotSingleTranslationBp_Input_vy_description=左右方向の速度を制御することは、全方向車にのみ有用で、他の車種はすべて0で、デフォルト値は0.0(単位: m/s)である
Falcon_Bp_RobotSingleTranslationBp_Input_vy_label=横移動速度（vy、単位: m/s）
Falcon_Bp_RobotSingleTranslationBp_Output_rbkResult_description=実行後の応答結果
Falcon_Bp_RobotSingleTranslationBp_Output_rbkResult_label=RBKリクエストの結果
Falcon_Bp_RobotSingleTranslationBp_description=ロボットの自由な動きを制御する
Falcon_Bp_RobotSingleTranslationBp_label=並進
Falcon_Bp_RobotSingleUserDefinedBp_Input_apiNo_description=指令番号は、デフォルトで3051です。
Falcon_Bp_RobotSingleUserDefinedBp_Input_apiNo_label=API番号
Falcon_Bp_RobotSingleUserDefinedBp_Input_reqId_description=コンポーネント実行時の識別子、オプション
Falcon_Bp_RobotSingleUserDefinedBp_Input_reqId_label=ID
Falcon_Bp_RobotSingleUserDefinedBp_Input_userDefinedAction_description=カスタムアクションディレクティブ（JSON文字列形式）
Falcon_Bp_RobotSingleUserDefinedBp_Input_userDefinedAction_label=アクション
Falcon_Bp_RobotSingleUserDefinedBp_Output_rbkResult_description=実行後の応答結果
Falcon_Bp_RobotSingleUserDefinedBp_Output_rbkResult_label=RBKリクエストの結果
Falcon_Bp_RobotSingleUserDefinedBp_description=ロボットにカスタムアクションを実行させる
Falcon_Bp_RobotSingleUserDefinedBp_label=カスタムアクション
Falcon_Bp_RobotSingleWaitDIBp_Input_id_description=DIの番号を指定します
Falcon_Bp_RobotSingleWaitDIBp_Input_id_label=DI番号
Falcon_Bp_RobotSingleWaitDIBp_Input_status_Option_status_0=0（無効）
Falcon_Bp_RobotSingleWaitDIBp_Input_status_Option_status_1=1（有効）
Falcon_Bp_RobotSingleWaitDIBp_Input_status_description=DIの状態が所望の状態である場合、DIがトリガされたことを示す。
Falcon_Bp_RobotSingleWaitDIBp_Input_status_label=期待の状態
Falcon_Bp_RobotSingleWaitDIBp_Input_timeout_description=DIがトリガーされるまでのタイムアウト時間を待ち、入力しない場合は待ち続けます
Falcon_Bp_RobotSingleWaitDIBp_Input_timeout_label=タイムアウト時間
Falcon_Bp_RobotSingleWaitDIBp_Output_rbkResult_description=実行後の応答結果
Falcon_Bp_RobotSingleWaitDIBp_Output_rbkResult_label=RBKリクエストの結果
Falcon_Bp_RobotSingleWaitDIBp_description=待ちDIの状態が所望の状態に変更される。
Falcon_Bp_RobotSingleWaitDIBp_label=DIのトリガーを待つ
Falcon_Bp_RobotWaitDIBp_Input_id_description=DIの番号を指定します
Falcon_Bp_RobotWaitDIBp_Input_id_label=DI番号
Falcon_Bp_RobotWaitDIBp_Input_status_description=DIの状態が所望の状態である場合、DIがトリガされたことを示す。
Falcon_Bp_RobotWaitDIBp_Input_status_label=期待の状態
Falcon_Bp_RobotWaitDIBp_Input_timeOut_description=DIがトリガーされるまでのタイムアウト時間を待ち、入力しない場合は待ち続けます
Falcon_Bp_RobotWaitDIBp_Input_timeOut_label=タイムアウト時間
Falcon_Bp_RobotWaitDIBp_Input_tomId_description=「ロボットアプリケーション」ページの「第二世代スケジューリング」のシーン名
Falcon_Bp_RobotWaitDIBp_Input_tomId_label=スケジューリングID
Falcon_Bp_RobotWaitDIBp_Input_vehicle_description=指定ロボット
Falcon_Bp_RobotWaitDIBp_Input_vehicle_label=指定ロボット
Falcon_Bp_RobotWaitDIBp_Output_result_description=実行後の応答結果
Falcon_Bp_RobotWaitDIBp_Output_result_label=結果を待つ
Falcon_Bp_RobotWaitDIBp_description=待ちDIの状態が所望の状態に変更される
Falcon_Bp_RobotWaitDIBp_label=DIのトリガーを待つ

[fleet.error]
OpErrCodeNavTaskFailed=ナビゲーションタスクが失敗しました。ロボット警告:{0}。経路ナビゲーションタスクID={1}。スケジューリングアクション={2}。交通管理タスク={3}。
OpErrCodeRobotDisposed=運送状のステップを実行する過程で、ロボットは破壊される。
OpErrCodeSceneDisposed=運送状ステップの実行中に、シーンが破棄されます。
T00010001=パスプランニングパラメータの検証に失敗しました
T00010002=ルート計画は、出発ルートコード{0}に基づいてルートが見つからないので、このルートコードが現在のロボットグループ{1}マップに存在するかどうかを確認してください
T00010003=経路計画、始点または終点が見つからない、始点コード{0}、終点コード{1}、所属ロボットグループ{2}
T00010004=経路計画、{0}経路が見つからず、始点{1}、終点{2}は、回転できない点{3}や、方向転換点が見つからず、材料棚を目標角度に回転させることと関係があるかもしれない
T00010005=パスプランニング、ボットロックタイムアウトを取得します。
T00010006=経路計画、ロボット{0}駐車タイムアウト、駐車ロックリソースに申請できるかどうかを確認してください
T00010007=経路計画、ロボット{0}は{1}コンテナを搭載しているが、そのコンテナ構成の寸法情報が見つからない
T00010008=経路計画、{0}に未知の異常があり、手動でトラブルシューティングする必要があります。
T00010010=パス計画、{0}計画が成功しました
T00020001=ロボット{0}の構築に失敗しました。ロボットグループ{1}がロボットサイズ情報を構成しているかどうかを確認してください
T00020002=ロボット{0}ジャッキ棚{1}角度は空にできない
T00020003=ボット{0}コンテナタイプ{1}がボットグループ{2}にバインドされていない可能性があります。コンテナタイプの設定を確認してください
T00020010={0}スペースリソースのオンライン申請に失敗しました
T00020011=ロボット{0}がオフラインで、占有空間資源情報が存在する場合は、そのロボットがシステムにいるかどうかをチェックし、システムにいない場合は、下のボタンをクリックしてそのロボットの空間資源を解放してください
T00030001=ロボットがデッドロックを起こし、車両をデッドロックする{0}
T00030002=ロボット{0}は空き車の運送状を受け取ることができません。理由は{1}です。
T00030003=ロボット{0}はまだ運送状{1}を持っていて、空いている車の運送状を押すことができない
btnReleaseSpaceLock=宇宙資源の解放
btnResetSpaceLock=スペースリソースをリセット
err3066Cancelled=要求ロボットのナビゲーション命令はキャンセルされ、命令id:{0}
err3066Fail=ロボットのナビゲーション命令を要求して失敗しました。命令id:{0}。ロボットの警告を見て、失敗の原因を確認してください
errAddStepLocationInvalid=binTaskを使用する「サイトまたは場所」の値は、ライブラリ名である必要があります。
errAddStepTooManyOp=複数のアクションの説明があります。
errAddStepWhenSealed=運送状番号{0}は封印されており、追加はできません。
errAwaitMove=ナビゲーションが完了するのを待っていますが、ナビゲーションが失敗しました。具体的な原因:{0}
errChangeTrafficButOrders=エラー:シーンに{0}個の未完了の運送状があり、交通管理ポリシーを変更できません。運送状の完了またはキャンセルをお待ちください。
errCollisionModelEmpty=衝突モデルは空にできません
errCreateRandomOrderNoArea=ランダムな注文、失敗:ビジネスライン"{0}"の期待されるロボットグループ"{1}"。
errCreateRandomOrderNoRobot=ランダム注文、失敗:ビジネスライン"{0}"で期待されるボットグループ"{1}"に利用可能なボットがありません。
errCreateStoOrderExists=運送状はすでに存在します={0}
errCrossAreaButNoLift=利用可能なエレベーターがありません:ロボット"{0}"は運送状ステップ"{1}"を実行して"{2}"から"{3}"に失敗しました
errCrossAreaLiftsAvailableNone=利用可能なエレベーターがなく、ロボット「{0}」は運送状ステップ「{1}」を実行し、「{2}」から「{3}」に行くことができない。
errCurrentMapNotMatched=ロボット{0}の現在の地図が地域の地図と一致しない
errDirectRobotOrderFailed=直接運送伝票が失敗しました、伝票番号={0}
errDirectRobotOrderNonexistent=直送伝票が存在しません。伝票番号={0}
errDoorNotFoundById=シーン"{0}"にID"{1}"のドアはありません
errDoorReadFaultMsgButNull=自動ドア"{0}"の障害情報の取得に失敗し、読み込まれた値はnullです
errDoorReadStatusButNull=自動ドア"{0}"の状態の取得に失敗し、読み込まれた値はnullです
errDuplicatedSceneName=シーン名{0}は既に存在します。
errEmptyKeyLocations=重要な位置は空にできません!
errEmptySceneName=シーン名を空にすることはできません。有効なシーン名を入力してください。
errFailedToFetchMapFromRobot=ボット{0}からマップを取得できませんでした
errFalconFailed=ファルコンミッション&lt;{0}&gt;失敗しました。ミッション={1}。理由:{2}
errFaultContainer=コンテナ番号は{0}です。
errFaultOptions=トラブルシューティング後に再試行するか、手動で完了するか、運送状をキャンセルすることができます（キャンセル後は復元できません）。
errFaultReason=故障の原因:{0}。
errFaultReasonUnknown=故障原因は不明。
errFetchChassisMode=シャーシドライブタイプの取得に失敗しました
errFetchCurrentMapFail=ロボットの現在の地図の取得に失敗しました。ロボット={0}、具体的な原因={1}です。
errFetchDoorReportFailed=ドア"{0}"の実行状態の取得に失敗しました。原因:{1}
errFetchLiftReportFailed=エレベーター"{0}"の動作状態の取得に失敗しました。原因:{1}
errFetchMapFail=ロボットマップの読み取りに失敗しました、ロボット={0}、具体的な原因={1}
errFileNotFound=文書{0}は存在しません
errFleetNoSuchSceneByName=このシーンはありません:{0}
errGroupMapNoPointOrBin=マシングループ<{0}>マップにはポイントやライブラリがありません{1}
errGwNoLocalRobot=ゲートウェイにはロボット{0}がありません。
errIllegalPath=不正な文書パス{0}
errInvalidPath=無効な文書パス{0}
errKeyLocationNotReachable=運送状の重要な位置が無効です
errLackPoint=マップポイントが不足しています。マシン数:{0}、マップポイント数:{1}
errLiftControl=エレベーター{0}の制御に失敗しました。原因:{1}
errLiftDisabled=エレベーター"{0}"は停止しています。
errLiftJinBoFloorCodeMustInt=このエレベーターの「エリアフロア{1}」の「フロアコード」は整数でなければなりません。
errLiftJinBoNoClient=このエレベーターはまだクライエントサイド。
errLiftJinBoReplyEmptyData=このエレベーターの情報は空です。
errLiftNotFoundById=シーン"{0}"にID"{1}"のエレベーターはありません
errLiftNotFoundByName=シーン「{0}」には「{1}」という名前のエレベーターはありません
errLiftNotMock=エレベータ「{0}」無効中シミュレーションモードです。
errManualFinishStepNoExecutingSelected=手動で運送状の手順を完了することに失敗し、ロボット{0}の運送状{1}は実行中の手順を実行していない
errManualFinishStepsButNoRobot=手動で運送状のステップを完了することができず、シーンに運送状{0}を実行するロボット{1}がいない
errMapNotFound=地図が見つかりません、{0}
errMissingJson=リクエストパラメータが欠落しています
errMockRobotNotFound=シミュレーションロボット{0}が見つかりません
errMockRobotPointNotChange=ロボット {0} の位置は変更できません。ロボットは実行中です。
errMotorNotFound=モデル文書からのモーター情報の読み取りに失敗しました。
errMotorParamTypeUnsupported=モデル文書の{0}の{1}は、arrayParamとcomboParamでサポートされていない引数型{2}を使用します。
errMotorPropNotFound=モデル文書の{0}の{1}には属性{2}がありません。
errMotorTypeNotFound=モデル文書の{0}には、{1}型の属性分類がありません。
errMove3051=3051パスナビゲーションの送信に失敗しました:{0}
errMove3066=3066パスナビゲーションの送信に失敗しました:{0}
errMoveRobotFromToStation=ロボット{0}は現在{1}にあります。パスの始点{2}または終点{3}に戻して再試行してください
errMustGetRobotGroupByRobot=シーン{0}でロボット{1}のグループが見つかりません
errNoAnyRobot=ロボットは存在しません
errNoAreaById=領域が見つかりません、id={0}
errNoBinOrLocation=ターゲットライブラリまたはポイントがありません
errNoBins=ライブラリ{0}が存在しないか、無効になっています
errNoCloseLiftFunc=スクリプトにエレベーター{0}:{1}のドアを閉める関数{2}が見つかりません
errNoDefaultMap=デフォルトマップ（最初のエリアの最初のモデル）が見つかりません
errNoEnabledAreasForRobotGroup=ロボットグループ「{0}」が属する地域「{2}」は無効になっており、ロボット「{1}」には利用可能な地図がない
errNoEnabledScene=無効中シーン
errNoFirstLightScene=光通信シーンはありません
errNoFromPointName=開始点{0}が見つかりません
errNoKeyLocations=重要な位置がありません。
errNoLocalRobot=ローカルボット{0}が見つかりません
errNoOpenLiftFunc=スクリプトでエレベーター{0}:{1}のドアを開ける関数{2}が見つかりません
errNoOrderById=運送状が見つかりません{0}
errNoOrderStepById=運送状の手順{0}が見つかりません
errNoOrders=条件を満たすビジネス注文がありません。
errNoPointOrDisabled=ポイント{0}が存在しないか、無効になっています
errNoPointOrPath=マップ{0}ポイントまたはパスなし
errNoProperty=この属性"{0}"はありません
errNoRbkClient=ロボットとの通信のクライエントサイド初期化されていない
errNoReportApplyUrl=報告先が指定されていません
errNoReportLiftFunc=スクリプトにエレベーター{0}:{1}の報告情報関数{2}が見つかりません
errNoRobot=このロボット"{0}"はありません。
errNoRobotGroupId=ロボットグループID{0}が見つかりません
errNoRobotGroupName=ロボットグループ名{0}が見つかりません
errNoRobots=ロボットが見つかりません:{0}
errNoSceneBasicFile=シーンの基礎が見つから文書
errNoSceneByOrderId=運送番号{0}に対応するシーンが見つかりません。
errNoSuchBinOrLocation={0}という名前のポイントまたはライブラリが見つかりません
errNoToPointName=終点が見つからない{0}
errNotExistContainerType=コンテナ型名{0}は存在しません
errNotRobotInTom=スケジューリング{0}にはロボット{1}がありません
errOnlyOneSingleAppCanBeEnabled=システムは自転車アプリケーションを1つしか有効にできない
errOrderNoSceneId=運送状{0}には対応するシーンがありません。
errOrderStepFailed={0}運送状{1}の{2}ステップが失敗しました。
errOrderStepLocationNotInGroupMap=現在の運送状の{0}ステップを確認してください。その終点位置/ライブラリ位置{1}が現在のロボットグループがある地図上にあるかどうかを確認してください
errPositionToNoSite=場所({0},{1})の近くにサイトはありません
errQuery3066BadTaskId=3066のクエリの結果、タスクIDが一致しません。期待値は{0}ですが、実際には{1}です。
errRbkNotOk=RBKエラー。エラーコード={0}。メッセージ={1}
errRbkResErr=rbkリクエストが失敗しました、番号:{0}、タイプ:{1}、理由:{2}
errRbkResultKindConnectFail=rbk接続に失敗しました
errRecAndForkLoadOverHeight=入力されたピックアップ前の高さ{0}は、フォークの最大リフト高さ{1}を超えています。
errRemoveAreaHasRobots=エリア{0}内にロボットがあり、削除や無効化はできません
errRemoveAreaHasRunningOrders=エリア{0}内に実行中の運送状があり、削除や無効化はできません
errRemoveRobotGroupButNotEmpty=ボットグループ"{0}"の削除に失敗しました。このグループにはボット"{1}"も存在します。
errReplaying=再生中です
errRequestControl=制御の要求に失敗しました、ロボット={0}
errRetryButOrderNotFailed=再試行しますが、注文ステータスは失敗ではありません。
errRobotAppNoControlPower=制御なし
errRobotAppNoScene=未設定のシーン
errRobotAppNoSceneById=このシーンはありません:{0}
errRobotAppNoSceneByName=このシーンはありません:{0}
errRobotAppNoSingleScene=シーン{0}は自転車アプリではありません。
errRobotAppSceneDisabled=シーン{0}は無効になりました
errRobotBadGroup=エラー:ロボット{0}はロボットグループ(またはロボットグループ削除済み)に属していなければならない
errRobotBinNum=ロボット「{0}」がシステムに配置した最大積載数は「{1}」ですが、実際にロボットに読み込まれたのは「{2}」のライブラリビットだけなので、配置をチェックしてください
errRobotBinNumBadOrder=ロボット"{0}"が有効にしているバックバスケット番号が異常です:"{1}";要求に応じてモデル文書の"tainer"を構成してください
errRobotBinPlusOne=ロボット「{0}」はシステムで「N+1」:「{1}」を構成していますが、実際にロボット「N+1」:「{2}」を読み込んで、構成を確認してください
errRobotBinPlusOneNotConfig=ロボット「{0}」は「N+1」をオンにする必要があります。構成を確認してください
errRobotBinPlusOneNotMatchSelfNum=ロボット「{0}」は「N+1」をオンにしていますが、最大積載数は「{1}」です。配置を確認してください
errRobotBinPlusOneShouldNotConfig=ロボット「{0}」が配置した最大積載数「{1}」が実際の数量「{2}」より小さい場合、「N+1」の配置は許可されていません。配置を確認してください
errRobotBinUpdate=設定の更新に失敗しました。ロボット"{0}"のライブラリインデックス"{1}"は空ではありません。現在、設定を確認してください
errRobotDisabled=ロボット{0}は無効になりました
errRobotExecutingTask=ロボットはナビゲーションタスクを実行しています。
errRobotGroupDisabled=ロボットグループ{0}は無効になりました
errRobotHasSimpleOrderCurrent=ロボット{0}はまだ運送状{1}を実行しており、新しい注文を受け取ることができません。
errRobotIpConflict=エラー:ロボット"{0}"のIPアドレス"{1}"は、同じシーンのロボット{2}によって使用されています。
errRobotIpMissing=エラー:ボット"{0}"のIPアドレスを入力してください。
errRobotMapInconsistentWithScene=ロボットマップは{0}シーンマップと一致しません
errRobotMoveNoStart=ロボット{0}がタスクを実行するための適切な開始点が見つかりません
errRobotNameDuplicated=エラー:ロボット名は重複できません:{0}
errRobotNavNoId=パスナビゲーションのリクエストが間違っています。終点パラメータ'id'が必要です。
errRobotNavNoSourceId=パスナビゲーションのリクエストが間違っています。開始パラメータ'source_id'が必要です。
errRobotNoAnyScene=現在、シーンはありません。
errRobotNoConnector=ロボット"{0}"とのコネクタはありません
errRobotNoCurrentStation=ボット{0}は現在サイトにありません
errRobotNoPosition=ロボット"{0}"の位置が取得できません。ロボットがポイントにいることを確認してください
errRobotNotIdleCmdOrders=ロボットの運送状は空ではありません、ロボット{0}
errRobotNotIdleCmdStatus=ロボットはアイドルではなく、ロボットは{0}、cmdStatus={1}です。
errRobotNotInitialized=ロボットは初期化されていません、ロボット{0}
errRobotOffline=ロボット「{0}」はオンラインではないか、連絡を失っている
errRobotRuntimeBinRemove=ロボット「{0}」はシステム構成の最大積載数と運行記録が一致せず、運行記録のライブラリインデックス「{1}」を削除しようとして失敗し、貨物や運送状が存在する
errRobotRuntimePlusOneRemove=ロボット「{0}」はシステムに「N+1」を構成していませんが、運行記録に「N+1」の倉庫に関連する貨物と運送状がありますので、構成を確認してください
errRobotUpdateBusy=ロボットが作業中で、更新できません
errSceneDisabled=シーン"{0}({1})"は無効になりました。
errSceneNameCannotBeBlank=シーン名は空にできません
errSceneNotIdle=シーンは空いていません。運送状があり、シーンIDは{0}です。
errSimpleTestBadContainerTypeName=発注失敗:現在のシーン"{0}"にコンテナタイプ"{1}"が存在しません。
errSimpleTestBadInitialPos=発注失敗:現在のシーン"{0}"に出生点"{1}"が存在しません。
errSimpleTestBadPriority=請求書の失敗:運送状の優先度の記述が異常で、原文は「{0}」で、要求通りに記入して再試行してください
errSimpleTestBadRobotName=発注失敗:現在のシーン"{0}"にはロボット"{1}"が存在しません。
errSimpleTestBadStepAction=請求書の失敗:「{0}」のステップにアクションの説明がないので、要求通りに記入して再試行してください
errSimpleTestBadStepContainerDir=請求書の失敗:「{0}」のステップの「容器の出荷角度」が異常で、原文は「{1}」で、要求通りに記入して再試行してください
errSimpleTestBadStepLocation=請求書の失敗:「{0}」のステップに目標ポイントがないので、要求通りに記入して再試行してください
errSimpleTestBadStepStruct=請求書の失敗:「{0}」のステップの説明が異常で、原文は「{1}」で、要求通りに記入して再試行してください
errSimpleTestInitialPosOnlyForRealRobot=発票失敗:ロボット"{0}"はシミュレーションロボットではなく、出生地"{1}"を指定できません。
errSimpleTestLoadUnloadStepsInvalid=発注失敗:発注書に「ロード」と「アンロード」アクションの説明が含まれているステップは、ペアで表示する必要があり、最大で1セットしか表示できません。
errStepBinTaskInvalid=ステップ{0}のターゲットライブラリビット{1}には、{2}という名前のbinTaskがありません。
errStepLocationInvalid={0}ステップではbinTaskを使用します。このステップの「サイトまたは場所」の値は、ライブラリ名である必要があります。
errStepRbkArgsInvalid=ステップ{0}の動作パラメータが異常{1}、原因:{2}
errStepTooManyOp=ステップ{0}には複数のアクションの説明があります。
errStoHikNotSupported=自転車の運送状はHikロボットをサポートしていません
errStoNoRobot=運送状{1}の作成に失敗し、ゲートウェイにロボット{0}がいません
errStoRetryFailedBadCurrentStatus=現在の運送状{0}の状態は失敗ではなく{1}です
errStoRetryFailedNoCurrent=現在、ロボットには運送状がありません。
errTargetMotorNotFound=モデル文書{0}という名前のモーターはありません。
errTomBlockStopped=スケジューリングブロック{0}:{1}が停止しました。手動で処理してください。エラーメッセージ{2}
errTomDisconnected=スケジューリングサーバーが存在しないか、接続に失敗しました:{0}
errTomOrderAwaitNotFound=送信後、スケジューリング運送状"{0}"が見つかりません。
errTomOrderKeyRouteMissing=少なくとも1つの重要な場所を指定する必要があります
errTomOrderNoVehicle=運送状"{0}"未割り当てロボット(運送状状態:{1})
errTomOrderNoVehicle2=運送状"{0}"未割り当てのロボット
errUnsupportedFetchMap=次の接続方法ではマップを取得できません:{0}
errUnsupportedFetchMap2=このシナリオでは、マップの取得はサポートされていません
errorLocationEmpty=場所は空にできません
errorTomOrderIdEmpty=スケジューリング伝票番号は空にできません
warningNotFindStartPoint=ロボットの現在位置は点にも線にもなく、点と線から許容範囲{0}(m)を超えている
warningRobotAreaMapNotFound=ロボットの現在の地図:{0}、ロボットグループ:{1}の地図ではこの地図が見つからないので、ロボットの地図とスケジュールが一致しているかどうかを確認してください
warningRobotFetchError=ボット「{0}」の状態の取得に失敗しました
warningStepSelectError=ロボット「{0}」は次のステップを実行できません。フォークに商品がありますので、すぐに処理してください

[stats]
StatsLabel_FalconTaskCreate=生成されたファルコンミッションレコードの数
StatsLabel_FalconTaskDone=完了したファルコンミッションの記録数
StatsLabel_FalconTaskError=異常なファルコンミッションの記録数
StatsLabel_QsInboundInvQty=在庫数
StatsLabel_QsInboundOrderCount=入庫単数
StatsLabel_QsOutboundInvQty=出庫数
StatsLabel_QsOutboundOrderCount=出庫単数
StatsLabel_QsStatsInvSummary=期末在庫の総数
StatsLabel_QsStatsMaterialCount=期末在庫品目数
StatsLabel_RobotChargeDuration=充電時間
StatsLabel_RobotChargeTimes=充電回数
StatsLabel_RobotEmptyDuration=無負荷時間
StatsLabel_RobotErrorDuration=障害時間
StatsLabel_RobotErrorTimes=障害の数
StatsLabel_RobotFailureRate=故障率
StatsLabel_RobotIdleDuration=アイドル時間
StatsLabel_RobotIdleRate=アイドル率
StatsLabel_RobotLoadDuration=積載時間
StatsLabel_RobotMileage=マイルを追加
StatsLabel_RobotNoLoadRate=無負荷率
StatsLabel_RobotOnlineDuration=オンライン時間
StatsLabel_RobotWorkDuration=勤務期間
StatsLabel_RobotWorkTimes=作業回数
StatsLabel_period=サイクル
StatsLabel_subject=科目
StatsLabel_target=オブジェクト

[entity]
entity.DemoEntity.fields.componentTableField.tip=コンポーネント表コンポーネント表コンポーネント表コンポーネント表コンポーネント表コンポーネント表コンポーネント表コンポーネント表コンポーネント表
entity.DemoEntity.fields.fileField.tip=アップロード文書アップロード文書アップロード文書アップロード文書アップロード文書アップロード文書アップロード文書アップロード文書アップロード文書アップロード文書アップロード文書
entity.DemoEntity.fields.floatField.tip=説明を入力説明を入力説明を入力説明を入力説明を入力説明を入力説明を入力
entity.DemoEntity.fields.id.tip=説明を入力
entity.DemoEntity.fields.imageField.tip=アップロード文書アップロード文書アップロード文書アップロード文書アップロード文書アップロード文書アップロード文書アップロード文書
entity.DemoEntity.fields.stringField.tip=説明を入力

[entity.i18n]

[qs]
Falcon_BlockGroup_QuickStoreBc=QSコンテナーライブラリのビット
Falcon_BlockGroup_QuickStoreBin=QSライブラリ
Falcon_BlockGroup_QuickStoreContainer=QSコンテナ
Falcon_BlockGroup_QuickStoreInv=QS在庫
Falcon_Bp_QsBindBinContainerBp_Input_binId_description=ライブラリID
Falcon_Bp_QsBindBinContainerBp_Input_binId_label=ライブラリ番号
Falcon_Bp_QsBindBinContainerBp_Input_containerId_description=コンテナ番号
Falcon_Bp_QsBindBinContainerBp_Input_containerId_label=コンテナ番号
Falcon_Bp_QsBindBinContainerBp_description=ライブラリビットをコンテナにバインドします
Falcon_Bp_QsBindBinContainerBp_label=QSバインディングライブラリビットコンテナ
Falcon_Bp_QsCreateInvFromOrderBp_Input_bin_description=ライブラリビット
Falcon_Bp_QsCreateInvFromOrderBp_Input_bin_label=ライブラリビット
Falcon_Bp_QsCreateInvFromOrderBp_Input_container_description=コンテナ
Falcon_Bp_QsCreateInvFromOrderBp_Input_container_label=コンテナ
Falcon_Bp_QsCreateInvFromOrderBp_Input_copyFields_description=複数のフィールドは","で区切られ、これらのフィールドと対応するデータがインベントリ情報にコピーされます。
Falcon_Bp_QsCreateInvFromOrderBp_Input_copyFields_label=コピーフィールド
Falcon_Bp_QsCreateInvFromOrderBp_Input_lotNoFormat_label=ライブラリ番号形式
Falcon_Bp_QsCreateInvFromOrderBp_Input_materialFieldName_description=資料を示すフィールド名を入力してください
Falcon_Bp_QsCreateInvFromOrderBp_Input_materialFieldName_label=マテリアルフィールド
Falcon_Bp_QsCreateInvFromOrderBp_Input_order_description=伝票オブジェクトを入力してください
Falcon_Bp_QsCreateInvFromOrderBp_Input_order_label=書類
Falcon_Bp_QsCreateInvFromOrderBp_Input_qtyFieldName_description=伝票に記載されている数量を示すフィールド名を入力してください
Falcon_Bp_QsCreateInvFromOrderBp_Input_qtyFieldName_label=数量フィールド
Falcon_Bp_QsCreateInvFromOrderBp_Input_state_description=デフォルトの3つのストレージ状態:受信:受信済み;保存:ストレージ中;割り当て:割り当て済み。
Falcon_Bp_QsCreateInvFromOrderBp_Input_state_label=在庫状況
Falcon_Bp_QsCreateInvFromOrderBp_description=伝票情報に基づいて在庫情報を作成する
Falcon_Bp_QsCreateInvFromOrderBp_label=伝票からインベントリを作成
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_bin_description=ライブラリビット
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_bin_label=ライブラリビット
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_container_description=コンテナ
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_container_label=コンテナ
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_copyFields_description=複数のフィールドは","で区切られ、これらのフィールドと対応するデータがインベントリ情報にコピーされます。
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_copyFields_label=次のフィールドを1行からコピーします
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_materialFieldName_description=マテリアルを表すフィールド名を1行に入力してください。
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_materialFieldName_label=1行のマテリアルフィールド名
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_order_description=伝票オブジェクトを入力してください
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_order_label=ドキュメントオブジェクト
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_qtyFieldName_description=1行に数量を表すフィールド名を入力してください。
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_qtyFieldName_label=1行の数量フィールド名
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_state_description=デフォルトの3つのストレージ状態:受信:受信済み;保存:ストレージ中;割り当て:割り当て済み。
Falcon_Bp_QsCreateInvFromOrderLinesBp_Input_state_label=ストレージの状態
Falcon_Bp_QsCreateInvFromOrderLinesBp_description=1行の情報からインベントリ情報を作成する
Falcon_Bp_QsCreateInvFromOrderLinesBp_label=伝票行からインベントリを作成
Falcon_Bp_QsCreateTraceContainerBp_Input_containerType_description=M 4レコードのコンテナタイプを入力してください。
Falcon_Bp_QsCreateTraceContainerBp_Input_containerType_label=コンテナーの種類
Falcon_Bp_QsCreateTraceContainerBp_Output_containerId_description=新しいコンテナー番号。
Falcon_Bp_QsCreateTraceContainerBp_Output_containerId_label=コンテナ番号
Falcon_Bp_QsCreateTraceContainerBp_description=トレースコンテナを作成します。
Falcon_Bp_QsCreateTraceContainerBp_label=トレースコンテナの作成
Falcon_Bp_QsFindEmptyBinBp_Input_cq_description=空のライブラリを探すための条件
Falcon_Bp_QsFindEmptyBinBp_Input_cq_label=条件付き
Falcon_Bp_QsFindEmptyBinBp_Input_districtIds_label=リポジトリリスト
Falcon_Bp_QsFindEmptyBinBp_Input_keepTrying_description=trueの場合は成功するまで空のライブラリビットを繰り返し探し、falseの場合は一度だけ探します
Falcon_Bp_QsFindEmptyBinBp_Input_keepTrying_label=試行錯誤して成功する
Falcon_Bp_QsFindEmptyBinBp_Input_sort_description=例えば["row","column","layer"]
Falcon_Bp_QsFindEmptyBinBp_Input_sort_label=ソートルール
Falcon_Bp_QsFindEmptyBinBp_Output_binId_description=見つかったライブラリID
Falcon_Bp_QsFindEmptyBinBp_Output_binId_label=ライブラリID
Falcon_Bp_QsFindEmptyBinBp_Output_found_description=trueは見つかった、falseは見つからなかった
Falcon_Bp_QsFindEmptyBinBp_Output_found_label=見つかるかどうか
Falcon_Bp_QsFindEmptyBinBp_description=空きライブラリを探す
Falcon_Bp_QsFindEmptyBinBp_label=QSは空のライブラリを探しています
Falcon_Bp_QsFindEmptyContainerBp_Input_bzDesc_description=ビジネスの説明。
Falcon_Bp_QsFindEmptyContainerBp_Input_bzDesc_label=ビジネスの説明
Falcon_Bp_QsFindEmptyContainerBp_Input_bzMark_description=ビジネストークンです。
Falcon_Bp_QsFindEmptyContainerBp_Input_bzMark_label=ビジネスタグ
Falcon_Bp_QsFindEmptyContainerBp_Input_districtIds_description=エリア番号。必要に応じて、M 4レコードのエリア番号を入力してください。
Falcon_Bp_QsFindEmptyContainerBp_Input_districtIds_label=リポジトリId
Falcon_Bp_QsFindEmptyContainerBp_Input_groupPriority_description=グループの優先順位。
Falcon_Bp_QsFindEmptyContainerBp_Input_groupPriority_label=グループの優先順位
Falcon_Bp_QsFindEmptyContainerBp_Input_priority_description=優先度。
Falcon_Bp_QsFindEmptyContainerBp_Input_priority_label=優先度
Falcon_Bp_QsFindEmptyContainerBp_Input_sort_description=ターゲットライブラリ領域内のライブラリビットのソート方法。
Falcon_Bp_QsFindEmptyContainerBp_Input_sort_label=並べ替え
Falcon_Bp_QsFindEmptyContainerBp_Input_timeout_description=タイムアウトを待つ。
Falcon_Bp_QsFindEmptyContainerBp_Input_timeout_label=タイムアウト時間
Falcon_Bp_QsFindEmptyContainerBp_Output_binId_description=ターゲットの空のコンテナを格納するリポジトリの番号。
Falcon_Bp_QsFindEmptyContainerBp_Output_binId_label=ライブラリ番号
Falcon_Bp_QsFindEmptyContainerBp_Output_containerId_description=見つかった空のコンテナの番号。
Falcon_Bp_QsFindEmptyContainerBp_Output_containerId_label=コンテナ番号
Falcon_Bp_QsFindEmptyContainerBp_Output_found_description=true:見つかりました; false:見つかりません。
Falcon_Bp_QsFindEmptyContainerBp_Output_found_label=空のコンテナを見つけるかどうか
Falcon_Bp_QsFindEmptyContainerBp_description=目的のルールに従って、ターゲット領域から空のコンテナを探し、このコンテナをロックします。
Falcon_Bp_QsFindEmptyContainerBp_label=空のコンテナを探す
Falcon_Bp_QsGetBinContainerBp_Input_binId_description=ライブラリID
Falcon_Bp_QsGetBinContainerBp_Input_binId_label=ライブラリ番号
Falcon_Bp_QsGetBinContainerBp_Output_binEmpty_description=コンテナが空かどうか、trueが空かどうか
Falcon_Bp_QsGetBinContainerBp_Output_binEmpty_label=ライブラリが空です
Falcon_Bp_QsGetBinContainerBp_Output_containerId_label=コンテナ番号
Falcon_Bp_QsGetBinContainerBp_description=ライブラリビット上のコンテナを取得します。取得する前提は、以前にライブラリビットがコンテナにバインドされていたことです。取得できない場合は、コンテナ番号がnullを返します
Falcon_Bp_QsGetBinContainerBp_label=QSは、ライブラリビット上のコンテナーを取得します
Falcon_Bp_QsGetContainerBinBp_Input_containerId_label=コンテナ番号
Falcon_Bp_QsGetContainerBinBp_Output_binId_label=ライブラリ番号
Falcon_Bp_QsGetContainerBinBp_Output_found_description=見つけたかどうか、trueは見つけたことを意味します。
Falcon_Bp_QsGetContainerBinBp_Output_found_label=ライブラリを見つける
Falcon_Bp_QsGetContainerBinBp_description=コンテナがあるライブラリビットを取得します。取得する前提は、以前にライブラリビットがコンテナにバインドされていたことです。取得できない場合、ライブラリビットIDはnullを返します
Falcon_Bp_QsGetContainerBinBp_label=QSは、コンテナが存在するライブラリを取得します
Falcon_Bp_QsGetContainerInvBp_Input_containerId_description=M 4レコードのコンテナ番号を入力してください
Falcon_Bp_QsGetContainerInvBp_Input_containerId_label=コンテナ番号
Falcon_Bp_QsGetContainerInvBp_Output_found_description=true:見つかりました; false:見つかりません。
Falcon_Bp_QsGetContainerInvBp_Output_found_label=見つかるかどうか
Falcon_Bp_QsGetContainerInvBp_Output_inv_description=在庫明細書。
Falcon_Bp_QsGetContainerInvBp_Output_inv_label=在庫明細書
Falcon_Bp_QsGetContainerInvBp_description=コンテナ番号から対応する在庫明細を取得します。
Falcon_Bp_QsGetContainerInvBp_label=コンテナ内の在庫の詳細を取得する
Falcon_Bp_QsGetContainerTypeStoreDistrictsBp_Input_containerId_description=M 4に記録されたコンテナの番号を入力してください。
Falcon_Bp_QsGetContainerTypeStoreDistrictsBp_Input_containerId_label=コンテナ番号
Falcon_Bp_QsGetContainerTypeStoreDistrictsBp_Output_storeDistricts_description=領域名のコレクション、配列。
Falcon_Bp_QsGetContainerTypeStoreDistrictsBp_Output_storeDistricts_label=ストア領域
Falcon_Bp_QsGetContainerTypeStoreDistrictsBp_description=コンテナ番号に基づいて、そのようなコンテナを格納できるリポジトリ領域の名前をクエリします。
Falcon_Bp_QsGetContainerTypeStoreDistrictsBp_label=コンテナーの種類のストアをクエリします
Falcon_Bp_QsKeepTryingLockBinBp_Input_binId_label=ライブラリID
Falcon_Bp_QsKeepTryingLockBinBp_Input_bzDesc_description=bzDesc
Falcon_Bp_QsKeepTryingLockBinBp_Input_bzDesc_label=ロックの理由
Falcon_Bp_QsKeepTryingLockBinBp_label=QSは成功するまで繰り返しライブラリをロックしようとします
Falcon_Bp_QsKeepTryingLockEmptyBinBp_Input_binId_label=ライブラリID
Falcon_Bp_QsKeepTryingLockEmptyBinBp_Input_bzDesc_description=bzDesc
Falcon_Bp_QsKeepTryingLockEmptyBinBp_Input_bzDesc_label=ロックの理由
Falcon_Bp_QsKeepTryingLockEmptyBinBp_label=QSはライブラリが空になるのを待ってからロックします。
Falcon_Bp_QsLockBinOnceBp_Input_binId_label=ライブラリID
Falcon_Bp_QsLockBinOnceBp_Input_bzDesc_description=bzDesc
Falcon_Bp_QsLockBinOnceBp_Input_bzDesc_label=ロックの理由
Falcon_Bp_QsLockBinOnceBp_Input_notFoundToAborted_description=失敗した場合、タスクを終了する必要がありますか? trueが必要です。
Falcon_Bp_QsLockBinOnceBp_Input_notFoundToAborted_label=終了タスクが見つかりません
Falcon_Bp_QsLockBinOnceBp_Input_notFoundToFault_description=タスクを失敗させる必要があるかどうか、trueが必要です
Falcon_Bp_QsLockBinOnceBp_Input_notFoundToFault_label=タスクを失敗させることができません
Falcon_Bp_QsLockBinOnceBp_Output_ok_description=成功はtrue、失敗はfalseです。
Falcon_Bp_QsLockBinOnceBp_Output_ok_label=ロックが成功しましたか?
Falcon_Bp_QsLockBinOnceBp_description=ロックされていないライブラリビットを一度ロックしようとすると、trueが正常に返され、それ以外の場合はfalseが返されます
Falcon_Bp_QsLockBinOnceBp_label=QSロックされていないライブラリビット
Falcon_Bp_QsMoveInvByContainerBp_Input_leafContainerId_description=一番内側のコンテナの番号を入力してください。
Falcon_Bp_QsMoveInvByContainerBp_Input_leafContainerId_label=最も内側のコンテナ
Falcon_Bp_QsMoveInvByContainerBp_Input_state_description=在庫状態を指定した在庫状態に変更します
Falcon_Bp_QsMoveInvByContainerBp_Input_state_label=在庫状態の指定
Falcon_Bp_QsMoveInvByContainerBp_Input_toBinId_description=最内側のコンテナを配置するライブラリビットの番号。
Falcon_Bp_QsMoveInvByContainerBp_Input_toBinId_label=エンドポイントライブラリのビット
Falcon_Bp_QsMoveInvByContainerBp_description=最内層のコンテナ情報を別のライブラリビットにバインドします。
Falcon_Bp_QsMoveInvByContainerBp_label=コンテナごとにインベントリを移動
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_binField_description=1行にライブラリを表すフィールド名を入力してください。
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_binField_label=ライブラリフィールド
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_ev_description=伝票情報を入力してください
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_ev_label=書類
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_materialField_description=1行にマテリアルを表すフィールド名を入力してください
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_materialField_label=マテリアルフィールド
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_outboundOrderIdField_label=出荷番号
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_pickOrderIdField_label=仕分け番号
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_qtyField_description=1行に数量を表すフィールド名を入力してください。
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_qtyField_label=数量フィールド
Falcon_Bp_QsReduceBinInvFromOrderBp_Input_subContainerIdField_label=グリッド
Falcon_Bp_QsReduceBinInvFromOrderBp_description=ドキュメントごとに在庫を削除します
Falcon_Bp_QsReduceBinInvFromOrderBp_label=ドキュメントごとに在庫を削除します
Falcon_Bp_QsRemoveBzMarkBp_Input_binId_label=ライブラリ番号
Falcon_Bp_QsRemoveBzMarkBp_Input_requireLock_description=このライブラリがロックされている必要があるかどうかライブラリがロックされていない場合、例外が終了します
Falcon_Bp_QsRemoveBzMarkBp_Input_requireLock_label=このライブラリビットをロックする必要があります
Falcon_Bp_QsRemoveBzMarkBp_description=指定されたライブラリをロック解除する
Falcon_Bp_QsRemoveBzMarkBp_label=QSロック解除ライブラリ
Falcon_Bp_QsRemoveInvByContainerBp_Input_containerId_description=M 4レコードのコンテナ番号を入力してください。
Falcon_Bp_QsRemoveInvByContainerBp_Input_containerId_label=コンテナ番号
Falcon_Bp_QsRemoveInvByContainerBp_Output_count_description=削除された在庫内訳の数。
Falcon_Bp_QsRemoveInvByContainerBp_Output_count_label=内訳の数
Falcon_Bp_QsRemoveInvByContainerBp_description=コンテナ番号に基づいて、対応する在庫明細を削除します。
Falcon_Bp_QsRemoveInvByContainerBp_label=コンテナごとの在庫内訳の削除
Falcon_Bp_QsRemoveTraceContainerBp_Input_containerId_description=M 4レコードの追跡コンテナの番号を入力してください。
Falcon_Bp_QsRemoveTraceContainerBp_Input_containerId_label=コンテナ番号
Falcon_Bp_QsRemoveTraceContainerBp_description=トレースコンテナを削除します。
Falcon_Bp_QsRemoveTraceContainerBp_label=トレースコンテナを削除
Falcon_Bp_QsTakeOffContainerBp_Input_binId_label=ライブラリID
Falcon_Bp_QsTakeOffContainerBp_Input_removingInv_label=コンテナ内のインベントリを削除
Falcon_Bp_QsTakeOffContainerBp_Input_unlockAfter_description=trueは未使用を設定してライブラリのロックを解除し、falseは未使用を設定してライブラリのロックを解除しない
Falcon_Bp_QsTakeOffContainerBp_Input_unlockBin_label=ロック解除
Falcon_Bp_QsTakeOffContainerBp_description=ライブラリ上のコンテナを削除し、ビジネスタグを付けることができます。
Falcon_Bp_QsTakeOffContainerBp_label=QSの空のリポジトリ
Falcon_Bp_QsUnbindBinContainerBp_Input_binId_description=ライブラリID
Falcon_Bp_QsUnbindBinContainerBp_Input_binId_label=ライブラリ番号
Falcon_Bp_QsUnbindBinContainerBp_Input_containerId_label=コンテナ番号
Falcon_Bp_QsUnbindBinContainerBp_Input_removingInv_description=trueは、コンテナ内のインベントリの詳細を削除します
Falcon_Bp_QsUnbindBinContainerBp_Input_removingInv_label=インベントリの削除
Falcon_Bp_QsUnbindBinContainerBp_Input_unlockBin_label=ライブラリのロックを解除する
Falcon_Bp_QsUnbindBinContainerBp_Input_unlockContainer_label=コンテナのロック解除
Falcon_Bp_QsUnbindBinContainerBp_description=ライブラリビットをコンテナから解放します
Falcon_Bp_QsUnbindBinContainerBp_label=QSリポジトリコンテナのバインド解除
errBinContainer1Container2=ライブラリビット{0}にコンテナ{1}があることを期待していますが、実際には{2}があります
errContainerNoOnBin=コンテナ{0}はライブラリ{1}にありません
errMoveContainerToBinOldContainer=コンテナ{0}をライブラリ{1}に移動しますが、ライブラリにはすでにコンテナ{2}があります
errNoBinInvByBin=在庫が見つかりません、在庫={0}
errNoBinInvByContainer=コンテナの在庫が見つかりません、コンテナ={0}
errNoContainerByBin=バインドされたコンテナが見つかりません、ライブラリの位置={0}

[fleet.diagnosis]
FleetCheckItem_BackgroundJob=バックエンドタスクが正常なロボットだけが注文/移動できます。
FleetCheckItem_ExpectedRobotExecutingOtherOrder=実行を期待しているロボットが他の運送状を実行しているかどうか
FleetCheckItem_FirstStepDone=最初の運送状ステップが完了したかどうかを検出します
FleetCheckItem_LiftAvailableWhenCrossArea=ロボットが階段を上り下りするとき、利用可能なエレベーターがあります
FleetCheckItem_NotReallocation=運送状が再割り当てを無効にしているかどうかを確認する
FleetCheckItem_OnDutyRobotsUnreachable=受注可能なロボットのパスは到達できません。
FleetCheckItem_OrderFault=故障リストかどうかを検出する
FleetCheckItem_OrderFindNextStepFail=運送状には実行可能な手順がありません
FleetCheckItem_OrderIsAllocated=運送状は、続行するために割り当てられています
FleetCheckItem_OrderLoaded=運送状を受け取った後、再配布は許可されません。
FleetCheckItem_OrderNoNextStep=運送状に次のステップがあるかどうか
FleetCheckItem_OrderNotBusiness=運送伝票が業務伝票かどうかを確認する
FleetCheckItem_OrderNotToBeAllocated=運送状の状態が割り当て待ちかどうか
FleetCheckItem_OrderNotWithdrawnOrCancelled=運送状の状態が撤回またはキャンセルであるかどうかを検出します
FleetCheckItem_OrderParametersValidate=運送状パラメータが正当かどうかを検出する
FleetCheckItem_OrderReachability=運送状のクリティカルパスが到達可能かどうかを検出します。
FleetCheckItem_OrderStepCannotAchievable=運送状の次の終点に到達できるかどうかを検出します
FleetCheckItem_RobotAlreadyParked=ロボットがすでにドッキングしているかどうかを確認します
FleetCheckItem_RobotAutoOrder=ロボットが自動運送伝票（充電、ドッキングなど）を実行しているかどうかを確認します
FleetCheckItem_RobotBatteryBelowChargeNeed=ロボットの電力がchargeNeedしきい値を下回っているかどうかを検出します。
FleetCheckItem_RobotBatteryBelowChargeOnly=ロボットの電力がchargeOnlyしきい値を下回っているかどうかを検出します。
FleetCheckItem_RobotBatteryExceedChargeOnly=ロボットの電力がchargeOnlyしきい値を超えているかどうかを検出します。
FleetCheckItem_RobotBlocked=ロボットがブロックされているかどうかを検出する
FleetCheckItem_RobotBlockedByTraffic=交通管理がリソースを取得できず、他のロボットにブロックされているかどうかを確認します。
FleetCheckItem_RobotBusy=ロボットがビジネス注文を実行しているかどうかを検出します。
FleetCheckItem_RobotCanReachOrder=この運送状がそのロボットに到達可能かどうかを検査する
FleetCheckItem_RobotCanReachPoint=ロボットが対応するポイントに到達できるかどうかを検出します
FleetCheckItem_RobotCharging=ロボットの充電状態を検出する
FleetCheckItem_RobotChargingNotEnoughTime=ロボットの充電状態と充電時間制限を検出します
FleetCheckItem_RobotChargingNotFull=ロボットがプリセットされたフル充電に充電されているかどうかを検出します
FleetCheckItem_RobotChargingPaused=ロボットが自動充電オフにしましたかどうか
FleetCheckItem_RobotChargingPointAvailable=ロボットが利用可能な充電ポイントを持っているかどうかを検出します
FleetCheckItem_RobotCmdInterrupted=ロボットの運送状の状態を検出する
FleetCheckItem_RobotCompositiveControlledFailed=ロボットはシステム制御状態ではありません。ロボットの無効化、制御権、受注、緊急停止状態、システム緊急停止状態をチェックしてください
FleetCheckItem_RobotCurrentPointNameNotBlank=ロボットの開始位置が空でなければタスクを実行できない
FleetCheckItem_RobotDisabled=ロボットが停止しているかどうかを検出する
FleetCheckItem_RobotFailed=ロボットは故障が発生していない場合にのみ受注できます。業務層の故障やロボットの故障報告を含みます
FleetCheckItem_RobotForceCharging=ロボットが強い衝撃状態にあるかどうかを検出する
FleetCheckItem_RobotIsOrderExpected=このロボットがこの運送状で期待されているかどうかを検出します。
FleetCheckItem_RobotNoMoreBin=ロボットがまだ利用可能なライブラリを持っているかどうかを検出します。
FleetCheckItem_RobotNoOrder=運送状のあるロボットだけが移動できます。
FleetCheckItem_RobotNotMaster=検出ロボットの制御
FleetCheckItem_RobotOffDuty=ロボットの受注状態を検出する
FleetCheckItem_RobotOffline=ロボットの接続状態を検出する
FleetCheckItem_RobotOrdersFault=ロボットが受け取った運送状は故障していなければ実行を続けることができない
FleetCheckItem_RobotParkPointAvailable=ロボットが利用可能なドッキングポイントを持っているかどうかを検出します
FleetCheckItem_RobotParkingPaused=ロボットが自動停止オフにしましたかどうか
FleetCheckItem_RobotPendingTrafficTaskCancelling=ロボットは交通管理任務を実行中にキャンセル中で、キャンセルが完了するのを待つ必要がある
FleetCheckItem_RobotToCharging=ロボットが充電タスクを実行しているかどうかを検出します
FleetCheckItem_RobotToPark=ロボットがドッキングタスクを実行しているかどうかを検出します
FleetCheckItem_RobotWaitIdleTimeoutToPark=ロボットのアイドルドッキング制限時間が満たされているかどうかを確認します
FleetCheckItem_SelfReportError=ロボットがエラーを報告しているかどうかを検出する
FleetCheckItem_TrafficNotReady=交通管理の初期化状態を確認する
FleetCheckItem_TrafficPlanPaused=交通管理ルートの計画が一時停止されたかどうかを検出します。
FleetCheckItem_validateRobotsStatus=ロボットの状態を検出する
FleetDiagnosis_AllRobotUnDispatchable=すべての実行を期待しているロボットは受注しない、受注しない理由:{0}
FleetDiagnosis_BackgroundJobFailed=次のスケジュールされたタスクの実行に失敗しました。
FleetDiagnosis_ErrKeyLocationInvalidate=運送状の重要な位置が無効です
FleetDiagnosis_ErrorCurrentStepIndex=現在のステップのインデックスエラー
FleetDiagnosis_ErrorLoad=積み下ろしが関係ない場合、積み下ろしが完了した
FleetDiagnosis_ErrorOrderStepIndex=運送状のステップインデックスエラー
FleetDiagnosis_ErrorStepIndex=運送状ステップのインデックスエラー
FleetDiagnosis_ErrorStepSize=ステップ数は実際のステップ数と一致しません
FleetDiagnosis_ExpectedRobotExecutingOtherOrder=実行を期待しているロボットは、他の運送状を実行しています。
FleetDiagnosis_FirstStepDone=最初の運送状ステップが完了しました
FleetDiagnosis_FromPointNullOrBlank=ロボットの初期位置が空で、タスクを実行できません
FleetDiagnosis_KeyLocationsNull=キーの場所が空です
FleetDiagnosis_LiftsAvailableNone=利用可能なエレベーターはありません
FleetDiagnosis_MissingGroups=運送状が実行を期待しているロボットグループ{0}は存在しません。
FleetDiagnosis_NoAvailableRobots=受注可能なロボットがありません。
FleetDiagnosis_NoExpectedRobot=運送状には実行を期待するロボットがいません。
FleetDiagnosis_NotReallocation=運送状の再配布は無効になっています
FleetDiagnosis_OnDutyRobotsUnreachable=受注可能なロボット{0}のパスは到達できません。
FleetDiagnosis_OrderFault=運送伝票は故障伝票です。
FleetDiagnosis_OrderIsNull=運送状が空です。
FleetDiagnosis_OrderLoaded=運送状を受け取りました
FleetDiagnosis_OrderNoNextStep=運送状には次のステップがありません。封印を期待しています。
FleetDiagnosis_OrderNotAllocated=運送状は割り当てられておらず、割り当て待ちの状態にある
FleetDiagnosis_OrderNotBusiness=運送状はビジネス伝票ではありません。運送状の種類:{0}
FleetDiagnosis_OrderNotToBeAllocated=運送状の状態は割り当て待ちの状態ではありません、状態:{0}
FleetDiagnosis_OrderNotWithdrawnOrCancelled=運送状の状態は撤回またはキャンセルで、状態は{0}です。
FleetDiagnosis_OrderStepCannotAchievable=ロボットの現在位置は{0}で、運送状の次のステップの終点{1}は到達できない
FleetDiagnosis_OrderStepPlusOneMustUnload=運送状の次のステップは「N+1」でなければならない
FleetDiagnosis_PointIsNull=ポイント名が空です
FleetDiagnosis_RobotAlreadyParked=ロボットはすでにドッキングポイントにいます
FleetDiagnosis_RobotAutoOrder=ロボットは自動運送伝票(充電、ドッキングなど)を実行している
FleetDiagnosis_RobotBatteryBelowChargeNeed=ロボットの電力は十分で、chargeNeed閾値={0}を超えています。
FleetDiagnosis_RobotBatteryBelowChargeOnly=ロボットの電力は十分で、chargeOnlyしきい値={0}を超えています。
FleetDiagnosis_RobotBatteryExceedChargeOnly=ロボットのバッテリーが低すぎて、充電するしかない={0}
FleetDiagnosis_RobotBatteryLowButBusy=ロボットは電力が低いが、業務運送伝票を実行しており、充電できない
FleetDiagnosis_RobotBlocked=ロボットがブロックされている
FleetDiagnosis_RobotBlockedByTraffic=ロボットがブロックされました:{0}
FleetDiagnosis_RobotBusy=ロボットは業務運送伝票を実行しています
FleetDiagnosis_RobotCanReachOrder=この運送状のクリティカルパスはロボットには届かないので、確認してください
FleetDiagnosis_RobotCanReachPoint=ロボットは対応するポイントに到達できません。地図を確認してください
FleetDiagnosis_RobotChargeOnly=ロボットのバッテリーが低すぎる
FleetDiagnosis_RobotCharging=ロボットは充電中です
FleetDiagnosis_RobotChargingNotEnoughTime=ロボットは充電を始めたが、少なくともしばらく充電して、すぐには終わらない
FleetDiagnosis_RobotChargingNotFull=ロボットは充電中ですが、設定されたフル充電に達していません。
FleetDiagnosis_RobotChargingPaused=ロボットオフにしました自動充電
FleetDiagnosis_RobotCmdInterrupted=ロボットは前の運送状を取り消しています。少々お待ちください。
FleetDiagnosis_RobotCurrentPointNameNullOrBlank=ロボットの初期位置が空で、タスクを実行できません
FleetDiagnosis_RobotDisabled=ロボットを無効にするには、まず有効にしてください
FleetDiagnosis_RobotFailed=ロボットが故障を実行し、故障原因={0}、まず故障したロボットを見つけて故障してから回復を試みてください
FleetDiagnosis_RobotForceCharging=ロボットは強制充電状態にあります。
FleetDiagnosis_RobotGroupUnreachable=運送状が実行を期待しているロボットグループ{0}は、重要な位置{1}に到達できません。
FleetDiagnosis_RobotIsNull=ロボットは空です
FleetDiagnosis_RobotNoAvailableChargingPoint=地図上に空き充電スポットはありませんので、ご確認ください
FleetDiagnosis_RobotNoAvailableChargingPointByCollision=地図上に空いている充電ポイントがありますが、衝突する可能性があります:{0}、確認してください
FleetDiagnosis_RobotNoAvailableParkPoint=地図上に空いている停留所はありませんので、ご確認ください
FleetDiagnosis_RobotNoAvailableParkPointByCollision=地図上に空いている停留所がありますが、衝突する可能性があります:{0}、確認してください
FleetDiagnosis_RobotNoMoreBin=ロボットにはもう在庫がありません（積載能力）
FleetDiagnosis_RobotNoOrder=ロボットは運送状を受け取っていません。
FleetDiagnosis_RobotNotChargeNeed=ロボットの現在の電力量は充電できる電力量よりも大きく、充電する必要はない
FleetDiagnosis_RobotNotChargeOnly=ロボットの現在の電力量は「充電しなければならない」電力量よりも大きく、強制充電は必要ない
FleetDiagnosis_RobotNotInterruptible=ロボットは中断できないタスクを実行しています
FleetDiagnosis_RobotNotMaster=コントロールがないので、まずコントロールを取得してください
FleetDiagnosis_RobotNotOrderExpectedRobotGroups=この運送状が期待しているロボットグループは={0}で、このロボットは満足していない
FleetDiagnosis_RobotNotOrderExpectedRobotNames=この運送状が期待しているロボットは={0}で、このロボットは満足していない
FleetDiagnosis_RobotOffDuty=ロボットは注文を受け付けないように設定されています。まず、ロボットが注文を受け付けるように設定してください。
FleetDiagnosis_RobotOffline=ロボットが切断されました
FleetDiagnosis_RobotParkingPaused=ロボットオフにしました自動ドッキング
FleetDiagnosis_RobotPendingTrafficTaskCancelling=交通管理任務はキャンセル中です。しばらくお待ちください
FleetDiagnosis_RobotSelfFailed=ロボット自身が故障を報告し、故障原因={0}、まずrbk故障を処理してください
FleetDiagnosis_RobotToCharging=ロボットは充電中です
FleetDiagnosis_RobotToPark=ロボットはドッキングを実行しています
FleetDiagnosis_RobotWaitIdleTimeoutToPark=ロボットはまだタイムアウトしていないため、しばらくの間停止する必要があります。
FleetDiagnosis_RobotsAllUnreachable=利用可能なロボットはキー位置{0}に到達できません
FleetDiagnosis_RobotsOrderFault=ロボットの運送状の故障
FleetDiagnosis_RobotsUnreachable=運送状が実行を期待しているロボットグループ{0}は、重要な位置{1}に到達できません。
FleetDiagnosis_SelfReportError=ロボットがエラーを報告しました={0}
FleetDiagnosis_TrafficNotReady=交管の初期化が完了するまでお待ちください。
FleetDiagnosis_TrafficPlanPaused=交通ルートの計画が一時停止されました
FleetDiagnosis_errNoFromPointName=開始点{0}が見つかりません
FleetDiagnosis_errNoRobots=運送状が実行を期待しているロボットは存在しません。実行を期待しているロボット:{0}
FleetDiagnosis_errNoSuchBinOrLocation={0}という名前のポイントまたはライブラリが見つかりません
FleetDiagnosis_errNoToPointName=終点が見つからない{0}
i18n_button_abort_label=諦める
i18n_button_manualFinished_label=手動で完了
i18n_button_retry_label=再試行
i18n_entity.AgentUser.fields.appId.label=appId
i18n_entity.AgentUser.fields.appKey.label=appKey
i18n_entity.AgentUser.fields.btDisabled.label=停止する
i18n_entity.AgentUser.fields.createdBy.label=作成者
i18n_entity.AgentUser.fields.createdOn.label=作成時間
i18n_entity.AgentUser.fields.id.label=ID
i18n_entity.AgentUser.fields.modifiedBy.label=最後に人を修正する
i18n_entity.AgentUser.fields.modifiedOn.label=最後の変更時間
i18n_entity.AgentUser.fields.remark.label=備考
i18n_entity.AgentUser.fields.version.label=改訂版
i18n_entity.AgentUser.group=コア
i18n_entity.AgentUser.label=プロキシアカウント
i18n_entity.ApiCallTrace.fields.apiType.inlineOptionBill.items.HTTP.label=HTTP
i18n_entity.ApiCallTrace.fields.apiType.label=インターフェースタイプ
i18n_entity.ApiCallTrace.fields.costTime.label=時間がかかる
i18n_entity.ApiCallTrace.fields.createdBy.label=作成者
i18n_entity.ApiCallTrace.fields.createdOn.label=作成時間
i18n_entity.ApiCallTrace.fields.httpMethod.inlineOptionBill.items.DELETE.label=削除する
i18n_entity.ApiCallTrace.fields.httpMethod.inlineOptionBill.items.GET.label=GET
i18n_entity.ApiCallTrace.fields.httpMethod.inlineOptionBill.items.POST.label=ポスト
i18n_entity.ApiCallTrace.fields.httpMethod.inlineOptionBill.items.PUT.label=PUT
i18n_entity.ApiCallTrace.fields.httpMethod.label=HTTPメソッド
i18n_entity.ApiCallTrace.fields.httpPath.label=HTTPパス
i18n_entity.ApiCallTrace.fields.httpUrl.label=リクエストURL
i18n_entity.ApiCallTrace.fields.id.label=ID
i18n_entity.ApiCallTrace.fields.modifiedBy.label=最後に人を修正する
i18n_entity.ApiCallTrace.fields.modifiedOn.label=時間を変更する
i18n_entity.ApiCallTrace.fields.reqBody.label=リクエスト本文
i18n_entity.ApiCallTrace.fields.reqBodyOn.label=レコードリクエスト本文
i18n_entity.ApiCallTrace.fields.reqEndOn.label=送信応答時間
i18n_entity.ApiCallTrace.fields.reqIp.label=リクエストIP
i18n_entity.ApiCallTrace.fields.reqStartOn.label=リクエストを受信した時間
i18n_entity.ApiCallTrace.fields.reqUser.label=リクエストユーザー
i18n_entity.ApiCallTrace.fields.resBody.label=応答本文
i18n_entity.ApiCallTrace.fields.resBodyOn.label=応答本文の記録を要求する
i18n_entity.ApiCallTrace.fields.resCode.label=応答コード
i18n_entity.ApiCallTrace.fields.version.label=改訂版
i18n_entity.ApiCallTrace.group=コア
i18n_entity.ApiCallTrace.label=インターフェイス呼び出しレコード
i18n_entity.ApiCallTrace.pagesButtons.ListMain.buttons[0].label=削除する
i18n_entity.ApiCallTrace.pagesButtons.ListMain.buttons[1].label=エクスポート
i18n_entity.ApiCallTrace.pagesButtons.ListMain.buttons[2].label=構成する
i18n_entity.BgTaskRecord.fields.args.label=入力パラメータ
i18n_entity.BgTaskRecord.fields.createdBy.label=作成者
i18n_entity.BgTaskRecord.fields.createdOn.label=作成時間
i18n_entity.BgTaskRecord.fields.fault.label=ミッション失敗
i18n_entity.BgTaskRecord.fields.faultMsg.label=失敗の原因
i18n_entity.BgTaskRecord.fields.id.label=ID
i18n_entity.BgTaskRecord.fields.modifiedBy.label=最後に人を修正する
i18n_entity.BgTaskRecord.fields.modifiedOn.label=最後の変更時間
i18n_entity.BgTaskRecord.fields.name.label=タスク名
i18n_entity.BgTaskRecord.fields.paused.label=一時停止しました
i18n_entity.BgTaskRecord.fields.version.label=改訂版
i18n_entity.BgTaskRecord.group=コア
i18n_entity.BgTaskRecord.label=バックグラウンドタスクの記録
i18n_entity.BgTaskRecord.pagesButtons.ListMain.buttons[0].label=エクスポート
i18n_entity.BgTaskRecord.pagesButtons.ListMain.buttons[1].label=続けて
i18n_entity.BgTaskRecord.pagesButtons.ListMain.buttons[2].label=一時停止する
i18n_entity.BgTaskRecord.pagesButtons.ListMain.buttons[3].label=障害の再試行
i18n_entity.BgTaskRecord.pagesButtons.ListMain.buttons[4].label=キャンセル
i18n_entity.BgTaskStepRecord.fields.createdBy.label=作成者
i18n_entity.BgTaskStepRecord.fields.createdOn.label=作成時間
i18n_entity.BgTaskStepRecord.fields.id.label=ID
i18n_entity.BgTaskStepRecord.fields.modifiedBy.label=最後に人を修正する
i18n_entity.BgTaskStepRecord.fields.modifiedOn.label=最後の変更時間
i18n_entity.BgTaskStepRecord.fields.output.label=実行結果
i18n_entity.BgTaskStepRecord.fields.taskId.label=バックグラウンドタスクID
i18n_entity.BgTaskStepRecord.fields.version.label=改訂版
i18n_entity.BgTaskStepRecord.group=コア
i18n_entity.BgTaskStepRecord.label=バックグラウンドタスクブロックレコード
i18n_entity.BgTaskStepRecord.pagesButtons.ListMain.buttons[0].label=エクスポート
i18n_entity.CallEmptyContainerOrder.fields.createdBy.label=作成者
i18n_entity.CallEmptyContainerOrder.fields.createdOn.label=作成時間
i18n_entity.CallEmptyContainerOrder.fields.district.label=倉庫エリア
i18n_entity.CallEmptyContainerOrder.fields.id.label=注文番号
i18n_entity.CallEmptyContainerOrder.fields.modifiedBy.label=最後に人を修正する
i18n_entity.CallEmptyContainerOrder.fields.modifiedOn.label=最後の変更時間
i18n_entity.CallEmptyContainerOrder.fields.num.label=数量
i18n_entity.CallEmptyContainerOrder.fields.version.label=改訂版
i18n_entity.CallEmptyContainerOrder.group=ウェアハウス
i18n_entity.CallEmptyContainerOrder.label=空のコンテナを呼び出す
i18n_entity.CallEmptyContainerOrder.listCard.district.prefix=倉庫エリア
i18n_entity.CallEmptyContainerOrder.listCard.num.prefix=数量
i18n_entity.CallEmptyContainerOrder.pagesButtons.Create.buttons[0].label=提出する
i18n_entity.ContainerTransportOrder.fields.atPort.label=在庫口
i18n_entity.ContainerTransportOrder.fields.container.label=コンテナ
i18n_entity.ContainerTransportOrder.fields.createdBy.label=作成者
i18n_entity.ContainerTransportOrder.fields.createdOn.label=作成時間
i18n_entity.ContainerTransportOrder.fields.doneOn.label=完了時間
i18n_entity.ContainerTransportOrder.fields.errMsg.label=エラーの原因
i18n_entity.ContainerTransportOrder.fields.expectedRobot.label=指定ロボット
i18n_entity.ContainerTransportOrder.fields.falconTaskDefId.label=ファルコンタスクテンプレートID
i18n_entity.ContainerTransportOrder.fields.falconTaskDefLabel.label=ファルコンタスクテンプレート名
i18n_entity.ContainerTransportOrder.fields.falconTaskId.label=ファルコンミッション番号
i18n_entity.ContainerTransportOrder.fields.fromBin.label=出発点のライブラリビット
i18n_entity.ContainerTransportOrder.fields.fromChannel.label=スタートレーン
i18n_entity.ContainerTransportOrder.fields.id.label=ID
i18n_entity.ContainerTransportOrder.fields.kind.label=タイプ
i18n_entity.ContainerTransportOrder.fields.loaded.label=受け取り済み
i18n_entity.ContainerTransportOrder.fields.modifiedBy.label=最後に人を修正する
i18n_entity.ContainerTransportOrder.fields.modifiedOn.label=最後の変更時間
i18n_entity.ContainerTransportOrder.fields.postProcessMark.label=タグの処理
i18n_entity.ContainerTransportOrder.fields.priority.label=優先度
i18n_entity.ContainerTransportOrder.fields.remark.label=備考
i18n_entity.ContainerTransportOrder.fields.robotName.label=実行ロボット
i18n_entity.ContainerTransportOrder.fields.sourceOrderId.label=関連文書
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Assigned.label=車を派遣しました
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Building.label=未提出
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Cancelled.label=キャンセル
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Created.label=提出済み
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Done.label=完了する
i18n_entity.ContainerTransportOrder.fields.status.inlineOptionBill.items.Failed.label=失敗する
i18n_entity.ContainerTransportOrder.fields.status.label=状態
i18n_entity.ContainerTransportOrder.fields.toBin.label=エンドポイントライブラリのビット
i18n_entity.ContainerTransportOrder.fields.toChannel.label=終点の路地
i18n_entity.ContainerTransportOrder.fields.unloaded.label=出荷済み
i18n_entity.ContainerTransportOrder.fields.version.label=改訂版
i18n_entity.ContainerTransportOrder.group=WCS
i18n_entity.ContainerTransportOrder.label=コンテナハンドリングシート
i18n_entity.ContainerTransportOrder.listCard.container.prefix=コンテナ
i18n_entity.ContainerTransportOrder.listCard.doneOn.prefix=完了する
i18n_entity.ContainerTransportOrder.listCard.falconTaskId.prefix=ファルコンクエスト
i18n_entity.ContainerTransportOrder.listCard.fromBin.suffix=--->
i18n_entity.ContainerTransportOrder.listCard.priority.prefix=優先度
i18n_entity.ContainerTransportOrder.listCard.robotName.prefix=ロボット
i18n_entity.ContainerTransportOrder.listCard.sourceOrderId.prefix=関連文書
i18n_entity.ContainerTransportOrder.listStats.items[0].label=失敗する
i18n_entity.DemoComponent.fields.btLineNo.label=行番号
i18n_entity.DemoComponent.fields.btParentId.label=所属注文
i18n_entity.DemoComponent.fields.createdBy.label=作成者
i18n_entity.DemoComponent.fields.createdOn.label=作成時間
i18n_entity.DemoComponent.fields.floatValue.label=floatValueの値
i18n_entity.DemoComponent.fields.id.label=ナンバリング
i18n_entity.DemoComponent.fields.modifiedBy.label=最後に人を修正する
i18n_entity.DemoComponent.fields.modifiedOn.label=最後の変更時間
i18n_entity.DemoComponent.fields.referenceField.label=引用する
i18n_entity.DemoComponent.fields.ro.label=ルート組織
i18n_entity.DemoComponent.fields.stringField.label=テキスト
i18n_entity.DemoComponent.fields.version.label=バージョン
i18n_entity.DemoComponent.group=テスト
i18n_entity.DemoComponent.label=テストコンポーネント
i18n_entity.DemoComponentTable.fields.btLineNo.label=行番号
i18n_entity.DemoComponentTable.fields.btParentId.label=所属注文
i18n_entity.DemoComponentTable.fields.createdBy.label=作成者
i18n_entity.DemoComponentTable.fields.createdOn.label=作成時間
i18n_entity.DemoComponentTable.fields.dateField.label=dateFieldのフィールド
i18n_entity.DemoComponentTable.fields.id.label=ナンバリング
i18n_entity.DemoComponentTable.fields.intField.label=intFieldの設定
i18n_entity.DemoComponentTable.fields.modifiedBy.label=最後に人を修正する
i18n_entity.DemoComponentTable.fields.modifiedOn.label=最後の変更時間
i18n_entity.DemoComponentTable.fields.referenceField.label=引用する
i18n_entity.DemoComponentTable.fields.ro.label=ルート組織
i18n_entity.DemoComponentTable.fields.stringField.label=テキスト
i18n_entity.DemoComponentTable.fields.version.label=バージョン
i18n_entity.DemoComponentTable.group=テスト
i18n_entity.DemoComponentTable.label=テストコンポーネント表
i18n_entity.DemoEntity.fields.booleanField.label=ブール
i18n_entity.DemoEntity.fields.booleanListField.label=ブール多値
i18n_entity.DemoEntity.fields.checkList2Field.inlineOptionBill.items.v1.label=オプション1
i18n_entity.DemoEntity.fields.checkList2Field.inlineOptionBill.items.v2.label=オプション2
i18n_entity.DemoEntity.fields.checkList2Field.inlineOptionBill.items.v3.label=オプション3
i18n_entity.DemoEntity.fields.checkList2Field.inlineOptionBill.items.v4.label=オプション4
i18n_entity.DemoEntity.fields.checkList2Field.label=オプションリスト（複数選択）
i18n_entity.DemoEntity.fields.checkListField.inlineOptionBill.items.v1.label=オプション1
i18n_entity.DemoEntity.fields.checkListField.inlineOptionBill.items.v2.label=オプション2
i18n_entity.DemoEntity.fields.checkListField.inlineOptionBill.items.v3.label=オプション3
i18n_entity.DemoEntity.fields.checkListField.inlineOptionBill.items.v4.label=オプション4
i18n_entity.DemoEntity.fields.checkListField.label=オプションリスト(ラジオ)
i18n_entity.DemoEntity.fields.componentField.label=コンポーネント
i18n_entity.DemoEntity.fields.componentListField.label=コンポーネントの多値
i18n_entity.DemoEntity.fields.componentTableField.label=コンポーネントテーブル
i18n_entity.DemoEntity.fields.createdBy.label=作成者
i18n_entity.DemoEntity.fields.createdOn.label=作成時間
i18n_entity.DemoEntity.fields.dateField.label=日付
i18n_entity.DemoEntity.fields.dateListField.label=日付の多値
i18n_entity.DemoEntity.fields.dateTimeField.label=日付と時刻
i18n_entity.DemoEntity.fields.dateTimeListField.label=日付と時刻の多値
i18n_entity.DemoEntity.fields.fileField.label=文書
i18n_entity.DemoEntity.fields.fileListField.label=文書多値
i18n_entity.DemoEntity.fields.floatField.label=浮動小数点数
i18n_entity.DemoEntity.fields.floatListField.label=浮動小数点数の多値
i18n_entity.DemoEntity.fields.id.label=ナンバリング
i18n_entity.DemoEntity.fields.imageField.label=の写真
i18n_entity.DemoEntity.fields.imageListField.label=画像は多値です。
i18n_entity.DemoEntity.fields.intField.label=整数
i18n_entity.DemoEntity.fields.intListField.label=整数の多値
i18n_entity.DemoEntity.fields.modifiedBy.label=最後に人を修正する
i18n_entity.DemoEntity.fields.modifiedOn.label=最後の変更時間
i18n_entity.DemoEntity.fields.passwordField.label=パスワード入力
i18n_entity.DemoEntity.fields.referenceField.label=引用する
i18n_entity.DemoEntity.fields.referenceListField.label=参照多値
i18n_entity.DemoEntity.fields.ro.label=ルート組織
i18n_entity.DemoEntity.fields.selectField.inlineOptionBill.items.v1.label=オプション1
i18n_entity.DemoEntity.fields.selectField.inlineOptionBill.items.v2.label=オプション2
i18n_entity.DemoEntity.fields.selectField.inlineOptionBill.items.v3.label=オプション3
i18n_entity.DemoEntity.fields.selectField.inlineOptionBill.items.v4.label=オプション4
i18n_entity.DemoEntity.fields.selectField.label=入力を選択
i18n_entity.DemoEntity.fields.selectListField.inlineOptionBill.items.v1.label=オプション1
i18n_entity.DemoEntity.fields.selectListField.inlineOptionBill.items.v2.label=オプション2
i18n_entity.DemoEntity.fields.selectListField.inlineOptionBill.items.v3.label=オプション3
i18n_entity.DemoEntity.fields.selectListField.inlineOptionBill.items.v4.label=オプション4
i18n_entity.DemoEntity.fields.selectListField.label=選択入力(複数値)
i18n_entity.DemoEntity.fields.stringField.label=テキスト
i18n_entity.DemoEntity.fields.stringListField.label=テキストの多値
i18n_entity.DemoEntity.fields.textAreaField.label=複数行のテキスト入力
i18n_entity.DemoEntity.fields.textAreaListField.label=複数行テキスト入力（複数値）
i18n_entity.DemoEntity.fields.version.label=バージョン
i18n_entity.DemoEntity.group=テスト
i18n_entity.DemoEntity.label=テストエンティティ
i18n_entity.DemoEntity2.fields.booleanField.label=ブール
i18n_entity.DemoEntity2.fields.booleanListField.label=ブール多値
i18n_entity.DemoEntity2.fields.checkList2Field.inlineOptionBill.items.v1.label=オプション1
i18n_entity.DemoEntity2.fields.checkList2Field.inlineOptionBill.items.v2.label=オプション2
i18n_entity.DemoEntity2.fields.checkList2Field.inlineOptionBill.items.v3.label=オプション3
i18n_entity.DemoEntity2.fields.checkList2Field.inlineOptionBill.items.v4.label=オプション4
i18n_entity.DemoEntity2.fields.checkList2Field.label=オプションリスト（複数選択）
i18n_entity.DemoEntity2.fields.checkListField.inlineOptionBill.items.v1.label=オプション1
i18n_entity.DemoEntity2.fields.checkListField.inlineOptionBill.items.v2.label=オプション2
i18n_entity.DemoEntity2.fields.checkListField.inlineOptionBill.items.v3.label=オプション3
i18n_entity.DemoEntity2.fields.checkListField.inlineOptionBill.items.v4.label=オプション4
i18n_entity.DemoEntity2.fields.checkListField.label=オプションリスト(ラジオ)
i18n_entity.DemoEntity2.fields.componentField.label=コンポーネント
i18n_entity.DemoEntity2.fields.componentListField.label=コンポーネントの多値
i18n_entity.DemoEntity2.fields.componentTableField.label=コンポーネントテーブル
i18n_entity.DemoEntity2.fields.createdBy.label=作成者
i18n_entity.DemoEntity2.fields.createdOn.label=作成時間
i18n_entity.DemoEntity2.fields.dateField.label=日付
i18n_entity.DemoEntity2.fields.dateListField.label=日付の多値
i18n_entity.DemoEntity2.fields.dateTimeField.label=日付と時刻
i18n_entity.DemoEntity2.fields.dateTimeListField.label=日付と時刻の多値
i18n_entity.DemoEntity2.fields.fileField.label=文書
i18n_entity.DemoEntity2.fields.fileListField.label=文書多値
i18n_entity.DemoEntity2.fields.floatField.label=浮動小数点数
i18n_entity.DemoEntity2.fields.floatListField.label=浮動小数点数の多値
i18n_entity.DemoEntity2.fields.id.label=ナンバリング
i18n_entity.DemoEntity2.fields.imageField.label=の写真
i18n_entity.DemoEntity2.fields.imageListField.label=画像は多値です。
i18n_entity.DemoEntity2.fields.intField.label=整数
i18n_entity.DemoEntity2.fields.intListField.label=整数の多値
i18n_entity.DemoEntity2.fields.modifiedBy.label=最後に人を修正する
i18n_entity.DemoEntity2.fields.modifiedOn.label=最後の変更時間
i18n_entity.DemoEntity2.fields.passwordField.label=パスワード入力
i18n_entity.DemoEntity2.fields.referenceField.label=引用する
i18n_entity.DemoEntity2.fields.referenceListField.label=参照多値
i18n_entity.DemoEntity2.fields.ro.label=ルート組織
i18n_entity.DemoEntity2.fields.selectField.inlineOptionBill.items.v1.label=オプション1
i18n_entity.DemoEntity2.fields.selectField.inlineOptionBill.items.v2.label=オプション2
i18n_entity.DemoEntity2.fields.selectField.inlineOptionBill.items.v3.label=オプション3
i18n_entity.DemoEntity2.fields.selectField.inlineOptionBill.items.v4.label=オプション4
i18n_entity.DemoEntity2.fields.selectField.label=入力を選択
i18n_entity.DemoEntity2.fields.selectListField.inlineOptionBill.items.v1.label=オプション1
i18n_entity.DemoEntity2.fields.selectListField.inlineOptionBill.items.v2.label=オプション2
i18n_entity.DemoEntity2.fields.selectListField.inlineOptionBill.items.v3.label=オプション3
i18n_entity.DemoEntity2.fields.selectListField.inlineOptionBill.items.v4.label=オプション4
i18n_entity.DemoEntity2.fields.selectListField.label=選択入力(複数値)
i18n_entity.DemoEntity2.fields.stringField.label=テキスト
i18n_entity.DemoEntity2.fields.stringListField.label=テキストの多値
i18n_entity.DemoEntity2.fields.textAreaField.label=複数行のテキスト入力
i18n_entity.DemoEntity2.fields.textAreaListField.label=複数行テキスト入力（複数値）
i18n_entity.DemoEntity2.fields.version.label=バージョン
i18n_entity.DemoEntity2.group=テスト
i18n_entity.DemoEntity2.label=テストエンティティ2
i18n_entity.Department.fields.createdBy.label=作成者
i18n_entity.Department.fields.createdOn.label=作成時間
i18n_entity.Department.fields.disabled.label=無効にする
i18n_entity.Department.fields.id.label=ナンバリング
i18n_entity.Department.fields.leafNode.label=葉部門
i18n_entity.Department.fields.level.label=階層
i18n_entity.Department.fields.modifiedBy.label=最後に人を修正する
i18n_entity.Department.fields.modifiedOn.label=最後の変更時間
i18n_entity.Department.fields.name.label=お名前
i18n_entity.Department.fields.owner.label=担当者
i18n_entity.Department.fields.parentNode.label=上級部門
i18n_entity.Department.fields.ro.label=ルート組織
i18n_entity.Department.fields.rootNode.label=トップ部門
i18n_entity.Department.fields.version.label=バージョン
i18n_entity.Department.group=ユーザ
i18n_entity.Department.label=セクター
i18n_entity.Department.listCard.owner.prefix=担当者
i18n_entity.DirectRobotOrder.fields.createdBy.label=作成者
i18n_entity.DirectRobotOrder.fields.createdOn.label=作成時間
i18n_entity.DirectRobotOrder.fields.description.label=説明する
i18n_entity.DirectRobotOrder.fields.doneOn.label=完了時間
i18n_entity.DirectRobotOrder.fields.errMsg.label=エラーの原因
i18n_entity.DirectRobotOrder.fields.id.label=ID
i18n_entity.DirectRobotOrder.fields.modifiedBy.label=最後に人を修正する
i18n_entity.DirectRobotOrder.fields.modifiedOn.label=時間を変更する
i18n_entity.DirectRobotOrder.fields.moves.label=アクション
i18n_entity.DirectRobotOrder.fields.robotName.label=ロボット名
i18n_entity.DirectRobotOrder.fields.seer3066.label=パスナビゲーションの指定
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Cancelled.label=キャンセル済み
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Created.label=新規作成
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Done.label=完了しました
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Failed.label=失敗する
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.ManualDone.label=手作業で完成
i18n_entity.DirectRobotOrder.fields.status.inlineOptionBill.items.Sent.label=送信済み
i18n_entity.DirectRobotOrder.fields.status.label=状態
i18n_entity.DirectRobotOrder.fields.status.listBatchButtons.buttons[0].label=キャンセル
i18n_entity.DirectRobotOrder.fields.status.listBatchButtons.buttons[1].label=手作業で完成
i18n_entity.DirectRobotOrder.fields.taskId.label=所属タスク
i18n_entity.DirectRobotOrder.fields.version.label=改訂版
i18n_entity.DirectRobotOrder.group=WCS
i18n_entity.DirectRobotOrder.label=直接運送伝票
i18n_entity.DirectRobotOrder.listCard.createdOn.prefix=作成する
i18n_entity.DirectRobotOrder.listCard.description.prefix=説明する
i18n_entity.DirectRobotOrder.listCard.robotName.prefix=ロボット
i18n_entity.DirectRobotOrder.listCard.taskId.prefix=所属タスク
i18n_entity.DirectRobotOrder.listStats.items[0].label=新規作成
i18n_entity.DirectRobotOrder.listStats.items[1].label=配布する
i18n_entity.DirectRobotOrder.listStats.items[2].label=失敗する
i18n_entity.EmptyContainerStoreOrder.fields.container.label=コンテナ
i18n_entity.EmptyContainerStoreOrder.fields.containerType.label=コンテナーの種類
i18n_entity.EmptyContainerStoreOrder.fields.createdBy.label=作成者
i18n_entity.EmptyContainerStoreOrder.fields.createdOn.label=作成時間
i18n_entity.EmptyContainerStoreOrder.fields.id.label=ID
i18n_entity.EmptyContainerStoreOrder.fields.modifiedBy.label=最後に人を修正する
i18n_entity.EmptyContainerStoreOrder.fields.modifiedOn.label=最後の変更時間
i18n_entity.EmptyContainerStoreOrder.fields.storeDistrict.label=ストア領域
i18n_entity.EmptyContainerStoreOrder.fields.version.label=改訂版
i18n_entity.EmptyContainerStoreOrder.group=ウェアハウス
i18n_entity.EmptyContainerStoreOrder.label=新しいコンテナのリスト
i18n_entity.EmptyContainerStoreOrder.listCard.container.prefix=コンテナ
i18n_entity.EmptyContainerStoreOrder.listCard.storeDistrict.prefix=ストア領域
i18n_entity.EmptyContainerStoreOrder.pagesButtons.ListMain.buttons[0].label=新規作成
i18n_entity.EmptyContainerStoreOrder.pagesButtons.ListMain.buttons[1].label=削除する
i18n_entity.EmptyContainerStoreOrder.pagesButtons.ListMain.buttons[2].label=空の箱
i18n_entity.EntityChangedRecord.fields.changeType.label=変更の種類
i18n_entity.EntityChangedRecord.fields.createdBy.label=作成者
i18n_entity.EntityChangedRecord.fields.createdOn.label=作成時間
i18n_entity.EntityChangedRecord.fields.entityFields.label=フィールドリスト
i18n_entity.EntityChangedRecord.fields.entityId.label=エンティティID
i18n_entity.EntityChangedRecord.fields.entityName.label=エンティティ名
i18n_entity.EntityChangedRecord.fields.id.label=ID
i18n_entity.EntityChangedRecord.fields.modifiedBy.label=最後に人を修正する
i18n_entity.EntityChangedRecord.fields.modifiedOn.label=最後の変更時間
i18n_entity.EntityChangedRecord.fields.version.label=改訂版
i18n_entity.EntityChangedRecord.group=コア
i18n_entity.EntityChangedRecord.label=エンティティ変更レコード
i18n_entity.EntityComment.fields.content.label=コンテンツ
i18n_entity.EntityComment.fields.createdBy.label=作成者
i18n_entity.EntityComment.fields.createdOn.label=作成時間
i18n_entity.EntityComment.fields.entityId.label=entityId
i18n_entity.EntityComment.fields.entityName.label=エンティティ
i18n_entity.EntityComment.fields.id.label=ナンバリング
i18n_entity.EntityComment.fields.modifiedBy.label=最後に人を修正する
i18n_entity.EntityComment.fields.modifiedOn.label=最後の変更時間
i18n_entity.EntityComment.fields.ro.label=企業向け
i18n_entity.EntityComment.fields.version.label=バージョン
i18n_entity.EntityComment.group=コア
i18n_entity.EntityComment.label=エンティティのコメント
i18n_entity.EntitySyncRecord.fields.bzType.label=ビジネスタイプ
i18n_entity.EntitySyncRecord.fields.cost.label=時間(ミリ秒)
i18n_entity.EntitySyncRecord.fields.createdBy.label=作成者
i18n_entity.EntitySyncRecord.fields.createdCount.label=追加されたエンティティの数
i18n_entity.EntitySyncRecord.fields.createdOn.label=作成時間
i18n_entity.EntitySyncRecord.fields.deletedCount.label=エンティティ数の削除
i18n_entity.EntitySyncRecord.fields.entityName.label=エンティティ名
i18n_entity.EntitySyncRecord.fields.faileReason.label=失敗の原因
i18n_entity.EntitySyncRecord.fields.id.label=ID
i18n_entity.EntitySyncRecord.fields.modifiedBy.label=最後に人を修正する
i18n_entity.EntitySyncRecord.fields.modifiedOn.label=最後の変更時間
i18n_entity.EntitySyncRecord.fields.oldCount.label=同期前のエンティティ数
i18n_entity.EntitySyncRecord.fields.ro.label=企業向け
i18n_entity.EntitySyncRecord.fields.success.label=成功する
i18n_entity.EntitySyncRecord.fields.syncCount.label=同期エンティティの数
i18n_entity.EntitySyncRecord.fields.syncOn.label=同期時間
i18n_entity.EntitySyncRecord.fields.txId.label=トランザクションID
i18n_entity.EntitySyncRecord.fields.updatedCount.label=エンティティ数の更新
i18n_entity.EntitySyncRecord.fields.version.label=バージョン
i18n_entity.EntitySyncRecord.group=コア
i18n_entity.EntitySyncRecord.label=エンティティ同期レコード
i18n_entity.ExternalCallRecord.fields.createdBy.label=作成者
i18n_entity.ExternalCallRecord.fields.createdOn.label=作成時間
i18n_entity.ExternalCallRecord.fields.doneOn.label=完了時間
i18n_entity.ExternalCallRecord.fields.failedNum.label=失敗の回数
i18n_entity.ExternalCallRecord.fields.failedReason.label=失敗の原因
i18n_entity.ExternalCallRecord.fields.id.label=ID
i18n_entity.ExternalCallRecord.fields.modifiedBy.label=最後に人を修正する
i18n_entity.ExternalCallRecord.fields.modifiedOn.label=最後の変更時間
i18n_entity.ExternalCallRecord.fields.okChecker.label=成功した検査方法
i18n_entity.ExternalCallRecord.fields.options.label=オプション
i18n_entity.ExternalCallRecord.fields.req.label=リクエストの詳細
i18n_entity.ExternalCallRecord.fields.resBody.label=応答本文
i18n_entity.ExternalCallRecord.fields.resCode.label=応答コード
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Aborted.label=終了しました
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Cancelled.label=キャンセル済み
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Done.label=成功する
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Failed.label=失敗する
i18n_entity.ExternalCallRecord.fields.status.inlineOptionBill.items.Init.label=始める
i18n_entity.ExternalCallRecord.fields.status.label=状態
i18n_entity.ExternalCallRecord.fields.url.label=URL
i18n_entity.ExternalCallRecord.fields.version.label=改訂版
i18n_entity.ExternalCallRecord.group=コア
i18n_entity.ExternalCallRecord.label=サードパーティの呼び出し履歴
i18n_entity.ExternalCallTrace.fields.createdBy.label=作成者
i18n_entity.ExternalCallTrace.fields.createdOn.label=作成時間
i18n_entity.ExternalCallTrace.fields.id.label=ID
i18n_entity.ExternalCallTrace.fields.ioError.label=通信エラー
i18n_entity.ExternalCallTrace.fields.ioError.view.trueText=IOエラー
i18n_entity.ExternalCallTrace.fields.ioErrorMsg.label=通信エラーの原因
i18n_entity.ExternalCallTrace.fields.method.label=HTTPメソッド
i18n_entity.ExternalCallTrace.fields.modifiedBy.label=最後に人を修正する
i18n_entity.ExternalCallTrace.fields.modifiedOn.label=最後の変更時間
i18n_entity.ExternalCallTrace.fields.reqBody.label=リクエスト本文
i18n_entity.ExternalCallTrace.fields.reqOn.label=リクエスト時間
i18n_entity.ExternalCallTrace.fields.resBody.label=応答本文
i18n_entity.ExternalCallTrace.fields.resCode.label=応答コード
i18n_entity.ExternalCallTrace.fields.resOn.label=応答時間
i18n_entity.ExternalCallTrace.fields.url.label=URL
i18n_entity.ExternalCallTrace.fields.version.label=改訂版
i18n_entity.ExternalCallTrace.group=コア
i18n_entity.ExternalCallTrace.label=HTTPクライエントサイドログ
i18n_entity.FailureRecord.fields.createdBy.label=作成者
i18n_entity.FailureRecord.fields.createdOn.label=作成時間
i18n_entity.FailureRecord.fields.desc.label=説明する
i18n_entity.FailureRecord.fields.firstOn.label=最初の発生時間
i18n_entity.FailureRecord.fields.id.label=ID
i18n_entity.FailureRecord.fields.kind.label=カテゴリー
i18n_entity.FailureRecord.fields.lastOn.label=最新の発生時刻
i18n_entity.FailureRecord.fields.level.inlineOptionBill.items.Error.label=故障
i18n_entity.FailureRecord.fields.level.inlineOptionBill.items.Fatal.label=深刻です
i18n_entity.FailureRecord.fields.level.inlineOptionBill.items.Warning.label=警告する
i18n_entity.FailureRecord.fields.level.label=レベル
i18n_entity.FailureRecord.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FailureRecord.fields.modifiedOn.label=最後の変更時間
i18n_entity.FailureRecord.fields.num.label=回数
i18n_entity.FailureRecord.fields.part.label=オブジェクト
i18n_entity.FailureRecord.fields.source.label=ソース
i18n_entity.FailureRecord.fields.subKind.label=サブカテゴリ
i18n_entity.FailureRecord.fields.version.label=改訂版
i18n_entity.FailureRecord.group=コア
i18n_entity.FailureRecord.label=障害ログ
i18n_entity.FalconBlockChildId.fields.blockId.label=blockId
i18n_entity.FalconBlockChildId.fields.childId.label=child ID
i18n_entity.FalconBlockChildId.fields.contextKey.label=contextキー
i18n_entity.FalconBlockChildId.fields.createdBy.label=作成者
i18n_entity.FalconBlockChildId.fields.createdOn.label=作成時間
i18n_entity.FalconBlockChildId.fields.id.label=ID
i18n_entity.FalconBlockChildId.fields.index.label=インデックス
i18n_entity.FalconBlockChildId.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FalconBlockChildId.fields.modifiedOn.label=時間を変更する
i18n_entity.FalconBlockChildId.fields.taskId.label=taskId
i18n_entity.FalconBlockChildId.fields.version.label=改訂版
i18n_entity.FalconBlockChildId.group=ファルコン
i18n_entity.FalconBlockChildId.label=ファルコンタスクブロックブロックID
i18n_entity.FalconBlockChildId.listCard.contextKey.prefix=contextキー
i18n_entity.FalconBlockChildId.listCard.createdOn.prefix=作成する
i18n_entity.FalconBlockChildId.listCard.taskId.prefix=taskId
i18n_entity.FalconBlockRecord.fields.blockConfigId.label=block ConfigId
i18n_entity.FalconBlockRecord.fields.createdBy.label=作成者
i18n_entity.FalconBlockRecord.fields.createdOn.label=作成時間
i18n_entity.FalconBlockRecord.fields.endedOn.label=endedOn
i18n_entity.FalconBlockRecord.fields.endedReason.label=endedReason
i18n_entity.FalconBlockRecord.fields.failureNum.label=失敗の回数
i18n_entity.FalconBlockRecord.fields.id.label=ID
i18n_entity.FalconBlockRecord.fields.inputParams.label=inputパラメータ
i18n_entity.FalconBlockRecord.fields.internalVariables.label=インターナル変数
i18n_entity.FalconBlockRecord.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FalconBlockRecord.fields.modifiedOn.label=時間を変更する
i18n_entity.FalconBlockRecord.fields.outputParams.label=outputパラメータ
i18n_entity.FalconBlockRecord.fields.startedOn.label=startedOn
i18n_entity.FalconBlockRecord.fields.status.label=ステータス
i18n_entity.FalconBlockRecord.fields.taskId.label=taskId
i18n_entity.FalconBlockRecord.fields.version.label=改訂版
i18n_entity.FalconBlockRecord.group=ファルコン
i18n_entity.FalconBlockRecord.label=ファルコンミッションブロックレコード
i18n_entity.FalconBlockRecord.listCard.blockConfigId.prefix=block ConfigId
i18n_entity.FalconBlockRecord.listCard.taskId.prefix=taskId
i18n_entity.FalconLog.fields.blockId.label=関連コンポーネント
i18n_entity.FalconLog.fields.createdBy.label=作成者
i18n_entity.FalconLog.fields.createdOn.label=作成時間
i18n_entity.FalconLog.fields.id.label=ID
i18n_entity.FalconLog.fields.level.inlineOptionBill.items.Debug.label=一般的に
i18n_entity.FalconLog.fields.level.inlineOptionBill.items.Error.label=エラー
i18n_entity.FalconLog.fields.level.inlineOptionBill.items.Info.label=重要
i18n_entity.FalconLog.fields.level.label=レベル
i18n_entity.FalconLog.fields.message.label=メッセージ
i18n_entity.FalconLog.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FalconLog.fields.modifiedOn.label=時間を変更する
i18n_entity.FalconLog.fields.taskId.label=タスクの関連付け
i18n_entity.FalconLog.fields.version.label=改訂版
i18n_entity.FalconLog.group=ファルコン
i18n_entity.FalconLog.label=ファルコンログ
i18n_entity.FalconLog.listCard.blockId.prefix=関連コンポーネント
i18n_entity.FalconLog.listCard.createdOn.prefix=作成する
i18n_entity.FalconRelatedObject.fields.createdBy.label=作成者
i18n_entity.FalconRelatedObject.fields.createdOn.label=作成時間
i18n_entity.FalconRelatedObject.fields.id.label=ID
i18n_entity.FalconRelatedObject.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FalconRelatedObject.fields.modifiedOn.label=最後の変更時間
i18n_entity.FalconRelatedObject.fields.objectArgs.label=objectArgs
i18n_entity.FalconRelatedObject.fields.objectId.label=objectId
i18n_entity.FalconRelatedObject.fields.objectType.label=objectType
i18n_entity.FalconRelatedObject.fields.taskId.label=taskId
i18n_entity.FalconRelatedObject.fields.version.label=改訂版
i18n_entity.FalconRelatedObject.group=ファルコン
i18n_entity.FalconRelatedObject.label=ファルコンミッション関連オブジェクト
i18n_entity.FalconRelatedObject.listCard.objectArgs.prefix=objectArgs
i18n_entity.FalconRelatedObject.listCard.objectId.prefix=objectId
i18n_entity.FalconTaskRecord.fields.actualRobots.label=実行ロボット
i18n_entity.FalconTaskRecord.fields.createdBy.label=作成者
i18n_entity.FalconTaskRecord.fields.createdOn.label=作成時間
i18n_entity.FalconTaskRecord.fields.defId.label=IDの定義
i18n_entity.FalconTaskRecord.fields.defLabel.label=タスクテンプレート
i18n_entity.FalconTaskRecord.fields.defVersion.label=テンプレートバージョン
i18n_entity.FalconTaskRecord.fields.endedOn.label=終了時間
i18n_entity.FalconTaskRecord.fields.endedReason.label=エラーの原因
i18n_entity.FalconTaskRecord.fields.failureNum.label=障害の数
i18n_entity.FalconTaskRecord.fields.id.label=ID
i18n_entity.FalconTaskRecord.fields.inputParams.label=入力パラメータ
i18n_entity.FalconTaskRecord.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FalconTaskRecord.fields.modifiedOn.label=時間を変更する
i18n_entity.FalconTaskRecord.fields.paused.label=一時停止しました
i18n_entity.FalconTaskRecord.fields.ro.label=RO
i18n_entity.FalconTaskRecord.fields.rootBlockStateId.label=ルートブロックID
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.100.label=作成済み
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.120.label=開始しました
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.140.label=故障
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.160.label=完了しました
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.180.label=キャンセル済み
i18n_entity.FalconTaskRecord.fields.status.inlineOptionBill.items.190.label=諦めました
i18n_entity.FalconTaskRecord.fields.status.label=状態
i18n_entity.FalconTaskRecord.fields.subTask.label=サブタスク
i18n_entity.FalconTaskRecord.fields.topTaskId.label=トップレベルのタスクID
i18n_entity.FalconTaskRecord.fields.variables.label=タスク変数
i18n_entity.FalconTaskRecord.fields.version.label=改訂版
i18n_entity.FalconTaskRecord.group=ファルコン
i18n_entity.FalconTaskRecord.label=ファルコンミッションレコード
i18n_entity.FalconTaskRecord.listCard.createdOn.prefix=作成する
i18n_entity.FalconTaskRecord.listCard.defLabel.prefix=テンプレート
i18n_entity.FalconTaskRecord.listCard.defVersion.prefix=(
i18n_entity.FalconTaskRecord.listCard.defVersion.suffix=)
i18n_entity.FalconTaskRecord.listCard.endedOn.prefix=終わり
i18n_entity.FalconTaskRecord.listStats.items[0].label=故障
i18n_entity.FalconTaskRecord.listStats.items[1].label=一時停止しました
i18n_entity.FalconTaskRecord.listStats.items[2].label=今日追加
i18n_entity.FalconTaskRecord.listStats.items[3].label=今日はキャンセルに失敗しました
i18n_entity.FalconTaskRecord.listStats.items[4].label=今週追加
i18n_entity.FalconTaskRecord.listStats.items[5].label=今週は失敗しました。キャンセルします。
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[0].label=削除する
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[1].label=エクスポート
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[2].label=一時停止
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[3].label=実行を続行
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[4].label=障害の再試行
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[5].label=実行をキャンセル
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[6].label=削除する
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[7].label=テンプレート
i18n_entity.FalconTaskRecord.pagesButtons.ListMain.buttons[8].label=グローバルコントロール
i18n_entity.FalconTaskResource.fields.args.label=パラメータ
i18n_entity.FalconTaskResource.fields.createdBy.label=作成者
i18n_entity.FalconTaskResource.fields.createdOn.label=作成時間
i18n_entity.FalconTaskResource.fields.id.label=ID
i18n_entity.FalconTaskResource.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FalconTaskResource.fields.modifiedOn.label=最後の変更時間
i18n_entity.FalconTaskResource.fields.resId.label=リソースID
i18n_entity.FalconTaskResource.fields.resType.label=リソースの種類
i18n_entity.FalconTaskResource.fields.taskId.label=タスク番号
i18n_entity.FalconTaskResource.fields.version.label=改訂版
i18n_entity.FalconTaskResource.group=ファルコン
i18n_entity.FalconTaskResource.label=ファルコンクエストリソース
i18n_entity.FalconTaskResource.listCard.resId.prefix=リソースID
i18n_entity.FbAssemblyLine.fields.building.label=ビル
i18n_entity.FbAssemblyLine.fields.buildingLayer.label=フロア
i18n_entity.FbAssemblyLine.fields.createdBy.label=作成者
i18n_entity.FbAssemblyLine.fields.createdOn.label=作成時間
i18n_entity.FbAssemblyLine.fields.disabled.label=無効にする
i18n_entity.FbAssemblyLine.fields.id.label=注文番号
i18n_entity.FbAssemblyLine.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbAssemblyLine.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbAssemblyLine.fields.name.label=お名前
i18n_entity.FbAssemblyLine.fields.remark.label=備考
i18n_entity.FbAssemblyLine.fields.ro.label=ルート組織
i18n_entity.FbAssemblyLine.fields.version.label=バージョン
i18n_entity.FbAssemblyLine.group=MainData
i18n_entity.FbAssemblyLine.label=生産ライン
i18n_entity.FbBin.fields.assemblyLine.label=所属生産ライン
i18n_entity.FbBin.fields.boxDirection.label=フォークの方向
i18n_entity.FbBin.fields.boxHeight.label=フォークリフトの高さ
i18n_entity.FbBin.fields.btDisabled.label=停止する
i18n_entity.FbBin.fields.channel.label=路地にいる
i18n_entity.FbBin.fields.column.label=列
i18n_entity.FbBin.fields.container.label=コンテナー上のライブラリ
i18n_entity.FbBin.fields.createdBy.label=作成者
i18n_entity.FbBin.fields.createdOn.label=作成時間
i18n_entity.FbBin.fields.depth.label=深い
i18n_entity.FbBin.fields.district.label=所属する倉庫エリア
i18n_entity.FbBin.fields.id.label=ライブラリ番号
i18n_entity.FbBin.fields.layer.label=レイヤー
i18n_entity.FbBin.fields.loadStatus.inlineOptionBill.items.Empty.label=未使用
i18n_entity.FbBin.fields.loadStatus.inlineOptionBill.items.Occupied.label=占有済み
i18n_entity.FbBin.fields.loadStatus.inlineOptionBill.items.ToLoad.label=もうすぐ届く
i18n_entity.FbBin.fields.loadStatus.inlineOptionBill.items.ToUnload.label=すぐに運び去る
i18n_entity.FbBin.fields.loadStatus.label=占有状態
i18n_entity.FbBin.fields.locked.label=ロックする
i18n_entity.FbBin.fields.locked.listBatchButtons.buttons[0].label=ロック解除
i18n_entity.FbBin.fields.lockedBy.label=ロックの理由
i18n_entity.FbBin.fields.materialCategoryLabel.label=ストレージマテリアルカテゴリ名称
i18n_entity.FbBin.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbBin.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbBin.fields.occupied.label=在庫あり
i18n_entity.FbBin.fields.occupied.view.trueText=在庫あり
i18n_entity.FbBin.fields.pendingContainer.label=出荷されるコンテナ
i18n_entity.FbBin.fields.purpose.label=お使いになる
i18n_entity.FbBin.fields.rack.label=所属する棚
i18n_entity.FbBin.fields.remark.label=備考
i18n_entity.FbBin.fields.ro.label=ルート組織
i18n_entity.FbBin.fields.robotDirection.label=ロボットの方向
i18n_entity.FbBin.fields.robotX.label=ロボットの位置X
i18n_entity.FbBin.fields.robotY.label=ロボットの位置Y
i18n_entity.FbBin.fields.row.label=並べる
i18n_entity.FbBin.fields.version.label=バージョン
i18n_entity.FbBin.fields.warehouse.label=所属倉庫
i18n_entity.FbBin.fields.workSite.label=所属するデスク
i18n_entity.FbBin.group=MainData
i18n_entity.FbBin.label=ライブラリビット
i18n_entity.FbBin.listCard.container.prefix=コンテナー上のライブラリ
i18n_entity.FbBin.listCard.district.prefix=所属する倉庫エリア
i18n_entity.FbBin.listStats.items[0].label=処理中（ロック）
i18n_entity.FbBin.listStats.items[1].label=在庫あり
i18n_entity.FbBin.listStats.items[2].label=もうすぐ届く
i18n_entity.FbBin.listStats.items[3].label=すぐに運び去る
i18n_entity.FbBin.pagesButtons.ListMain.buttons[0].label=追加する
i18n_entity.FbBin.pagesButtons.ListMain.buttons[1].label=一括編集
i18n_entity.FbBin.pagesButtons.ListMain.buttons[2].label=削除する
i18n_entity.FbBin.pagesButtons.ListMain.buttons[3].label=エクスポート
i18n_entity.FbBin.pagesButtons.ListMain.buttons[4].label=インポート
i18n_entity.FbBin.pagesButtons.ListMain.buttons[5].label=ライブラリビットの一括作成
i18n_entity.FbBinInv.fields.amount.label=金額
i18n_entity.FbBinInv.fields.assemblyLine.label=所属生産ライン
i18n_entity.FbBinInv.fields.bin.label=ライブラリビット
i18n_entity.FbBinInv.fields.binDisabled.label=ライブラリの停止
i18n_entity.FbBinInv.fields.binFilled.label=在庫はありますか?
i18n_entity.FbBinInv.fields.btBzDesc.label=ビジネスの説明
i18n_entity.FbBinInv.fields.btBzMark.label=ビジネスタグ
i18n_entity.FbBinInv.fields.btMaterial.label=マテリアル
i18n_entity.FbBinInv.fields.btMaterialCategory.label=材料の分類番号
i18n_entity.FbBinInv.fields.btMaterialCategoryName.label=マテリアルカテゴリ名称
i18n_entity.FbBinInv.fields.btMaterialId.label=マテリアル番号
i18n_entity.FbBinInv.fields.btMaterialModel.label=マテリアルモデル
i18n_entity.FbBinInv.fields.btMaterialName.label=マテリアル名
i18n_entity.FbBinInv.fields.btMaterialSpec.label=材料仕様
i18n_entity.FbBinInv.fields.btMaterialTopCategory.label=材料クラスIの分類番号
i18n_entity.FbBinInv.fields.btMaterialTopCategoryName.label=マテリアルレベルカテゴリ名称
i18n_entity.FbBinInv.fields.btPrepare.label=btPrepare
i18n_entity.FbBinInv.fields.channel.label=路地
i18n_entity.FbBinInv.fields.column.label=列
i18n_entity.FbBinInv.fields.containerDisabled.label=コンテナの停止
i18n_entity.FbBinInv.fields.containerFilled.label=コンテナは在庫がありますか?
i18n_entity.FbBinInv.fields.containers.label=コンテナーリスト
i18n_entity.FbBinInv.fields.createdBy.label=作成者
i18n_entity.FbBinInv.fields.createdOn.label=作成時間
i18n_entity.FbBinInv.fields.depth.label=深い
i18n_entity.FbBinInv.fields.district.label=倉庫エリア
i18n_entity.FbBinInv.fields.id.label=ID
i18n_entity.FbBinInv.fields.layer.label=レイヤー
i18n_entity.FbBinInv.fields.lotNo.label=バッチ番号
i18n_entity.FbBinInv.fields.materialCategoryLabel.label=ストレージマテリアルカテゴリ名称
i18n_entity.FbBinInv.fields.materialIds.label=ライブラリ上のマテリアル
i18n_entity.FbBinInv.fields.materialNames.label=ライブラリ上のマテリアル名のリスト
i18n_entity.FbBinInv.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbBinInv.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbBinInv.fields.onRobot.label=所在ロボット
i18n_entity.FbBinInv.fields.pendingContainer.label=出荷されるコンテナ
i18n_entity.FbBinInv.fields.qty.label=数量
i18n_entity.FbBinInv.fields.rack.label=棚
i18n_entity.FbBinInv.fields.robotBin.label=ロボットライブラリの場所
i18n_entity.FbBinInv.fields.row.label=並べる
i18n_entity.FbBinInv.fields.subNum.label=グリッド数
i18n_entity.FbBinInv.fields.topContainer.label=最も外側のコンテナ
i18n_entity.FbBinInv.fields.topContainerType.label=最も外側のコンテナタイプ
i18n_entity.FbBinInv.fields.version.label=改訂版
i18n_entity.FbBinInv.fields.warehouse.label=倉庫
i18n_entity.FbBinInv.fields.workSite.label=ワークスペース
i18n_entity.FbBinInv.group=メイン
i18n_entity.FbBinInv.label=在庫の場所
i18n_entity.FbContainer.fields.bin.label=ライブラリの場所
i18n_entity.FbContainer.fields.btDisabled.label=停止する
i18n_entity.FbContainer.fields.createdBy.label=作成者
i18n_entity.FbContainer.fields.createdOn.label=作成時間
i18n_entity.FbContainer.fields.district.label=現在のリポジトリ
i18n_entity.FbContainer.fields.filled.label=在庫あり
i18n_entity.FbContainer.fields.fixedStoreBin.label=指定されたライブラリストレージ
i18n_entity.FbContainer.fields.height.label=コンテナの高さ
i18n_entity.FbContainer.fields.id.label=コンテナ番号
i18n_entity.FbContainer.fields.length.label=コンテナの長さ
i18n_entity.FbContainer.fields.locked.label=ロックする
i18n_entity.FbContainer.fields.locked.listBatchButtons.buttons[0].label=ロック解除
i18n_entity.FbContainer.fields.maxWeight.label=コンテナの耐荷重性
i18n_entity.FbContainer.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbContainer.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbContainer.fields.onRobot.label=所在ロボット
i18n_entity.FbContainer.fields.pContainer.label=親コンテナー
i18n_entity.FbContainer.fields.preBin.label=リポジトリビットを予約します
i18n_entity.FbContainer.fields.qcResult.label=品質検査の結果
i18n_entity.FbContainer.fields.remark.label=備考
i18n_entity.FbContainer.fields.ro.label=ルート組織
i18n_entity.FbContainer.fields.state.label=状態
i18n_entity.FbContainer.fields.subNum.label=グリッド数
i18n_entity.FbContainer.fields.targetBin.label=出荷されるライブラリのビット
i18n_entity.FbContainer.fields.taskType.inlineOptionBill.items.Count.label=棚卸中
i18n_entity.FbContainer.fields.taskType.inlineOptionBill.items.Pick.label=仕分け待ち
i18n_entity.FbContainer.fields.taskType.inlineOptionBill.items.Put.label=箱詰め待ち
i18n_entity.FbContainer.fields.taskType.label=タスクの種類
i18n_entity.FbContainer.fields.type.label=コンテナーの種類
i18n_entity.FbContainer.fields.version.label=バージョン
i18n_entity.FbContainer.fields.volume.label=コンテナ容量
i18n_entity.FbContainer.fields.warehouse.label=現在のリポジトリ
i18n_entity.FbContainer.fields.width.label=コンテナ幅
i18n_entity.FbContainer.fields.workStatus.inlineOptionBill.items.ToPick.label=ピッキング待ち
i18n_entity.FbContainer.fields.workStatus.inlineOptionBill.items.ToPut.label=積み込み待ち
i18n_entity.FbContainer.fields.workStatus.label=仕事の状態
i18n_entity.FbContainer.group=MainData
i18n_entity.FbContainer.label=コンテナ
i18n_entity.FbContainer.listCard.bin.prefix=ライブラリの場所
i18n_entity.FbContainer.listCard.type.prefix=タイプ
i18n_entity.FbContainer.listStats.items[0].label=処理中（ロック）
i18n_entity.FbContainer.listStats.items[1].label=在庫あり
i18n_entity.FbContainer.listStats.items[2].label=ライブラリにはありません
i18n_entity.FbContainerType.fields.btDisabled.label=停止する
i18n_entity.FbContainerType.fields.createdBy.label=作成者
i18n_entity.FbContainerType.fields.createdOn.label=作成時間
i18n_entity.FbContainerType.fields.id.label=コンテナータイプのエンコーディング
i18n_entity.FbContainerType.fields.mixedMaterial.label=材料の混合
i18n_entity.FbContainerType.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbContainerType.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbContainerType.fields.name.label=コンテナータイプ名
i18n_entity.FbContainerType.fields.remark.label=説明する
i18n_entity.FbContainerType.fields.ro.label=ルート組織
i18n_entity.FbContainerType.fields.storeDistricts.label=リポジトリ領域
i18n_entity.FbContainerType.fields.subNum.label=セルの数
i18n_entity.FbContainerType.fields.version.label=バージョン
i18n_entity.FbContainerType.group=MainData
i18n_entity.FbContainerType.label=コンテナーの種類
i18n_entity.FbContainerType.listCard.storeDistricts.prefix=リポジトリ領域
i18n_entity.FbCountDiffLine.fields.actualQty.label=実際の数量
i18n_entity.FbCountDiffLine.fields.bin.label=元の保管場所
i18n_entity.FbCountDiffLine.fields.btLineNo.label=行番号
i18n_entity.FbCountDiffLine.fields.btMaterial.label=マテリアル
i18n_entity.FbCountDiffLine.fields.btMaterialCategory.label=材料の分類番号
i18n_entity.FbCountDiffLine.fields.btMaterialCategoryName.label=マテリアルカテゴリ名称
i18n_entity.FbCountDiffLine.fields.btMaterialId.label=マテリアル番号
i18n_entity.FbCountDiffLine.fields.btMaterialImage.label=マテリアル画像
i18n_entity.FbCountDiffLine.fields.btMaterialModel.label=マテリアルモデル
i18n_entity.FbCountDiffLine.fields.btMaterialName.label=マテリアル名
i18n_entity.FbCountDiffLine.fields.btMaterialSpec.label=材料仕様
i18n_entity.FbCountDiffLine.fields.btParentId.label=所属注文
i18n_entity.FbCountDiffLine.fields.container.label=コンテナ
i18n_entity.FbCountDiffLine.fields.createdBy.label=作成者
i18n_entity.FbCountDiffLine.fields.createdOn.label=作成時間
i18n_entity.FbCountDiffLine.fields.diffQty.label=差異の数
i18n_entity.FbCountDiffLine.fields.fix.label=実行する
i18n_entity.FbCountDiffLine.fields.id.label=ID
i18n_entity.FbCountDiffLine.fields.lotNo.label=バッチ番号
i18n_entity.FbCountDiffLine.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbCountDiffLine.fields.modifiedOn.label=時間を変更する
i18n_entity.FbCountDiffLine.fields.qty.label=元の数量
i18n_entity.FbCountDiffLine.fields.qualityLevel.label=品質レベル
i18n_entity.FbCountDiffLine.fields.remark.label=備考
i18n_entity.FbCountDiffLine.fields.sourceInvLayout.label=関連する在庫の詳細
i18n_entity.FbCountDiffLine.fields.subContainerId.label=グリッド番号
i18n_entity.FbCountDiffLine.fields.taskId.label=棚卸タスク
i18n_entity.FbCountDiffLine.fields.version.label=改訂版
i18n_entity.FbCountDiffLine.group=ウェアハウス
i18n_entity.FbCountDiffLine.label=ディスク差の記録行
i18n_entity.FbCountDiffLine.listCard.btMaterial.prefix=マテリアル
i18n_entity.FbCountDiffLine.listCard.subContainerId.prefix=グリッド番号
i18n_entity.FbCountFix.fields.btMaterial.label=マテリアル
i18n_entity.FbCountFix.fields.btMaterialCategory.label=材料の分類番号
i18n_entity.FbCountFix.fields.btMaterialCategoryName.label=マテリアルカテゴリ名称
i18n_entity.FbCountFix.fields.btMaterialId.label=マテリアル番号
i18n_entity.FbCountFix.fields.btMaterialImage.label=マテリアル画像
i18n_entity.FbCountFix.fields.btMaterialModel.label=マテリアルモデル
i18n_entity.FbCountFix.fields.btMaterialName.label=マテリアル名
i18n_entity.FbCountFix.fields.btMaterialSpec.label=材料仕様
i18n_entity.FbCountFix.fields.container.label=コンテナ
i18n_entity.FbCountFix.fields.createdBy.label=作成者
i18n_entity.FbCountFix.fields.createdOn.label=作成時間
i18n_entity.FbCountFix.fields.id.label=ID
i18n_entity.FbCountFix.fields.lotNo.label=バッチ番号
i18n_entity.FbCountFix.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbCountFix.fields.modifiedOn.label=時間を変更する
i18n_entity.FbCountFix.fields.qty.label=変更の数
i18n_entity.FbCountFix.fields.qualityLevel.label=品質レベル
i18n_entity.FbCountFix.fields.remark.label=備考
i18n_entity.FbCountFix.fields.subContainerId.label=グリッド番号
i18n_entity.FbCountFix.fields.version.label=改訂版
i18n_entity.FbCountFix.group=ウェアハウス
i18n_entity.FbCountFix.label=在庫修正記録
i18n_entity.FbCountFix.listCard.container.prefix=コンテナ
i18n_entity.FbCountFix.listCard.qty.prefix=変更の数
i18n_entity.FbCountFix.listCard.subContainerId.prefix=グリッド番号
i18n_entity.FbCountOrder.fields.bins.label=計画された在庫リスト
i18n_entity.FbCountOrder.fields.btOrderState.inlineOptionBill.items.Done.label=完了する
i18n_entity.FbCountOrder.fields.btOrderState.inlineOptionBill.items.Init.label=未提出
i18n_entity.FbCountOrder.fields.btOrderState.inlineOptionBill.items.Submitted.label=提出済み
i18n_entity.FbCountOrder.fields.btOrderState.label=ビジネスの状態
i18n_entity.FbCountOrder.fields.btOrderStateReason.label=ステータスの説明
i18n_entity.FbCountOrder.fields.containers.label=計画されたインベントリコンテナのリスト
i18n_entity.FbCountOrder.fields.createdBy.label=作成者
i18n_entity.FbCountOrder.fields.createdOn.label=作成時間
i18n_entity.FbCountOrder.fields.diffLines.label=ディスク差リスト
i18n_entity.FbCountOrder.fields.districts.label=計画された在庫エリアのリスト
i18n_entity.FbCountOrder.fields.doneProcessed.label=盤差は処理済みです。
i18n_entity.FbCountOrder.fields.id.label=注文番号
i18n_entity.FbCountOrder.fields.materials.label=在庫リストを計画する
i18n_entity.FbCountOrder.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbCountOrder.fields.modifiedOn.label=時間を変更する
i18n_entity.FbCountOrder.fields.remark.label=説明する
i18n_entity.FbCountOrder.fields.taskGenerated.label=棚卸タスクが生成されました
i18n_entity.FbCountOrder.fields.taskLines.label=タスクの統計行
i18n_entity.FbCountOrder.fields.version.label=改訂版
i18n_entity.FbCountOrder.group=ウェアハウス
i18n_entity.FbCountOrder.label=インベントリリスト
i18n_entity.FbCountOrder.states.states.Done.label=完了する
i18n_entity.FbCountOrder.states.states.Init.label=未提出
i18n_entity.FbCountOrder.states.states.Init.nextStates.Submitted.buttonLabel=提出する
i18n_entity.FbCountOrder.states.states.Submitted.label=提出済み
i18n_entity.FbCountOrder.states.states.Submitted.nextStates.Done.buttonLabel=ディスク差異の実行
i18n_entity.FbCountTask.fields.bin.label=ソースライブラリのビット
i18n_entity.FbCountTask.fields.btLines.label=単一行
i18n_entity.FbCountTask.fields.btOrderState.inlineOptionBill.items.Init.label=棚卸し待ち
i18n_entity.FbCountTask.fields.btOrderState.inlineOptionBill.items.Submitted.label=棚卸済み
i18n_entity.FbCountTask.fields.btOrderState.label=ビジネスの状態
i18n_entity.FbCountTask.fields.btOrderStateReason.label=ステータスの説明
i18n_entity.FbCountTask.fields.container.label=インベントリコンテナ
i18n_entity.FbCountTask.fields.containerInOrderId.label=コンテナ入庫運送番号
i18n_entity.FbCountTask.fields.containerOutOrderId.label=コンテナ出荷伝票番号
i18n_entity.FbCountTask.fields.countOrderId.label=所属インベントリリスト
i18n_entity.FbCountTask.fields.createdBy.label=作成者
i18n_entity.FbCountTask.fields.createdOn.label=作成時間
i18n_entity.FbCountTask.fields.id.label=ID
i18n_entity.FbCountTask.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbCountTask.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbCountTask.fields.remark.label=説明する
i18n_entity.FbCountTask.fields.submittedPostProcessed.label=提出後の処理完了
i18n_entity.FbCountTask.fields.version.label=改訂版
i18n_entity.FbCountTask.group=ウェアハウス
i18n_entity.FbCountTask.label=棚卸タスク
i18n_entity.FbCountTask.listCard.bin.prefix=ライブラリビット
i18n_entity.FbCountTask.listCard.container.prefix=コンテナ
i18n_entity.FbCountTask.listStats.items[0].label=棚卸し待ち
i18n_entity.FbCountTask.states.states.Init.label=棚卸し待ち
i18n_entity.FbCountTask.states.states.Init.nextStates.Submitted.buttonLabel=提出する
i18n_entity.FbCountTask.states.states.Submitted.label=棚卸済み
i18n_entity.FbCountTaskLine.fields.actualQty.label=実際の数量
i18n_entity.FbCountTaskLine.fields.amount.label=金額
i18n_entity.FbCountTaskLine.fields.bin.label=ライブラリの場所
i18n_entity.FbCountTaskLine.fields.btLineNo.label=行番号
i18n_entity.FbCountTaskLine.fields.btMaterial.label=マテリアル
i18n_entity.FbCountTaskLine.fields.btMaterialCategory.label=材料の分類番号
i18n_entity.FbCountTaskLine.fields.btMaterialCategoryName.label=マテリアルカテゴリ名称
i18n_entity.FbCountTaskLine.fields.btMaterialId.label=マテリアル番号
i18n_entity.FbCountTaskLine.fields.btMaterialImage.label=マテリアル画像
i18n_entity.FbCountTaskLine.fields.btMaterialModel.label=マテリアルモデル
i18n_entity.FbCountTaskLine.fields.btMaterialName.label=マテリアル名
i18n_entity.FbCountTaskLine.fields.btMaterialSpec.label=材料仕様
i18n_entity.FbCountTaskLine.fields.btParentId.label=所属タスク
i18n_entity.FbCountTaskLine.fields.createdBy.label=作成者
i18n_entity.FbCountTaskLine.fields.createdOn.label=作成時間
i18n_entity.FbCountTaskLine.fields.district.label=所在する倉庫エリア
i18n_entity.FbCountTaskLine.fields.expDate.label=有効期間
i18n_entity.FbCountTaskLine.fields.id.label=ID
i18n_entity.FbCountTaskLine.fields.leafContainer.label=コンテナ
i18n_entity.FbCountTaskLine.fields.lotNo.label=バッチ番号
i18n_entity.FbCountTaskLine.fields.mfgDate.label=製造日
i18n_entity.FbCountTaskLine.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbCountTaskLine.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbCountTaskLine.fields.owner.label=荷主
i18n_entity.FbCountTaskLine.fields.price.label=単価
i18n_entity.FbCountTaskLine.fields.qty.label=数量
i18n_entity.FbCountTaskLine.fields.qualityLevel.label=品質レベル
i18n_entity.FbCountTaskLine.fields.sourceInvLayout.label=在庫の内訳を引用する
i18n_entity.FbCountTaskLine.fields.subContainerId.label=グリッド
i18n_entity.FbCountTaskLine.fields.topContainer.label=最も外側のコンテナ
i18n_entity.FbCountTaskLine.fields.unitLabel.label=ユニット名
i18n_entity.FbCountTaskLine.fields.vendor.label=サプライヤー
i18n_entity.FbCountTaskLine.fields.version.label=改訂版
i18n_entity.FbCountTaskLine.fields.warehouse.label=所在する倉庫
i18n_entity.FbCountTaskLine.group=ウェアハウス
i18n_entity.FbCountTaskLine.label=タスクラインを点検する
i18n_entity.FbCountTaskLine.listCard.actualQty.prefix=実際の数量
i18n_entity.FbCountTaskLine.listCard.btMaterialId.prefix=マテリアル番号
i18n_entity.FbCountTaskLine.listCard.lotNo.prefix=バッチ番号
i18n_entity.FbCountTaskLine.listCard.qty.prefix=在庫数
i18n_entity.FbCountTaskStatLine.fields.bin.label=在庫の位置を確認する
i18n_entity.FbCountTaskStatLine.fields.btLineNo.label=行番号
i18n_entity.FbCountTaskStatLine.fields.btParentId.label=所属注文
i18n_entity.FbCountTaskStatLine.fields.container.label=インベントリコンテナ
i18n_entity.FbCountTaskStatLine.fields.createdBy.label=作成者
i18n_entity.FbCountTaskStatLine.fields.createdOn.label=作成時間
i18n_entity.FbCountTaskStatLine.fields.id.label=ID
i18n_entity.FbCountTaskStatLine.fields.materialsNum.label=記録された材料の数
i18n_entity.FbCountTaskStatLine.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbCountTaskStatLine.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbCountTaskStatLine.fields.qty.label=レコードの総数
i18n_entity.FbCountTaskStatLine.fields.taskId.label=棚卸タスク
i18n_entity.FbCountTaskStatLine.fields.version.label=改訂版
i18n_entity.FbCountTaskStatLine.group=ウェアハウス
i18n_entity.FbCountTaskStatLine.label=インベントリタスクの統計行
i18n_entity.FbCountTaskStatLine.listCard.materialsNum.prefix=マテリアル数
i18n_entity.FbCountTaskStatLine.listCard.qty.prefix=合計数量
i18n_entity.FbCustomer.fields.address.label=アドレス
i18n_entity.FbCustomer.fields.btDisabled.label=停止する
i18n_entity.FbCustomer.fields.contact.label=連絡先
i18n_entity.FbCustomer.fields.createdBy.label=作成者
i18n_entity.FbCustomer.fields.createdOn.label=作成時間
i18n_entity.FbCustomer.fields.id.label=顧客コード
i18n_entity.FbCustomer.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbCustomer.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbCustomer.fields.name.label=顧客名
i18n_entity.FbCustomer.fields.phone.label=連絡先電話番号
i18n_entity.FbCustomer.fields.remark.label=備考
i18n_entity.FbCustomer.fields.ro.label=ルート組織
i18n_entity.FbCustomer.fields.version.label=バージョン
i18n_entity.FbCustomer.group=MainData
i18n_entity.FbCustomer.label=クライアント
i18n_entity.FbCustomer.listCard.btDisabled.formatMapping[0].replaceText=使用停止済み
i18n_entity.FbCustomer.listCard.contact.prefix=連絡先
i18n_entity.FbCustomer.listCard.name.prefix=顧客名
i18n_entity.FbCustomer.listCard.phone.prefix=電話で
i18n_entity.FbDepartment.fields.btDisabled.label=停止する
i18n_entity.FbDepartment.fields.btHiLeafNode.label=葉ノード
i18n_entity.FbDepartment.fields.btHiNodeLevel.label=レイヤー
i18n_entity.FbDepartment.fields.btHiParentNode.label=上司
i18n_entity.FbDepartment.fields.btHiRootNode.label=トップレベルノード
i18n_entity.FbDepartment.fields.createdBy.label=作成者
i18n_entity.FbDepartment.fields.createdOn.label=作成時間
i18n_entity.FbDepartment.fields.id.label=ナンバリング
i18n_entity.FbDepartment.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbDepartment.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbDepartment.fields.name.label=お名前
i18n_entity.FbDepartment.fields.owner.label=担当者
i18n_entity.FbDepartment.fields.ro.label=ルート組織
i18n_entity.FbDepartment.fields.version.label=バージョン
i18n_entity.FbDepartment.group=ユーザ
i18n_entity.FbDepartment.label=組織する
i18n_entity.FbDepartment.listCard.owner.prefix=担当者
i18n_entity.FbDevTask.fields.comments.label=コメント
i18n_entity.FbDevTask.fields.createdBy.label=作成者
i18n_entity.FbDevTask.fields.createdOn.label=作成時間
i18n_entity.FbDevTask.fields.description.label=説明する
i18n_entity.FbDevTask.fields.devVersion.label=反復
i18n_entity.FbDevTask.fields.files.label=添付ファイル文書
i18n_entity.FbDevTask.fields.id.label=ナンバリング
i18n_entity.FbDevTask.fields.images.label=添付画像
i18n_entity.FbDevTask.fields.kind.inlineOptionBill.items.Bug.label=欠陥
i18n_entity.FbDevTask.fields.kind.inlineOptionBill.items.Feature.label=機能する
i18n_entity.FbDevTask.fields.kind.inlineOptionBill.items.Improvement.label=最適化
i18n_entity.FbDevTask.fields.kind.inlineOptionBill.items.Test.label=テスト
i18n_entity.FbDevTask.fields.kind.label=タイプ
i18n_entity.FbDevTask.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbDevTask.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbDevTask.fields.priority.inlineOptionBill.items.High.label=高い
i18n_entity.FbDevTask.fields.priority.inlineOptionBill.items.Low.label=低い
i18n_entity.FbDevTask.fields.priority.inlineOptionBill.items.Normal.label=中
i18n_entity.FbDevTask.fields.priority.label=優先度
i18n_entity.FbDevTask.fields.processedBy.label=人を扱う
i18n_entity.FbDevTask.fields.project.label=プロジェクト
i18n_entity.FbDevTask.fields.ro.label=企業向け
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Closed.label=オフにしました
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Open.label=新規作成
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Processing.label=未完了
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Rejected.label=拒否されました
i18n_entity.FbDevTask.fields.state.inlineOptionBill.items.Resolved.label=解決済み
i18n_entity.FbDevTask.fields.state.label=状態
i18n_entity.FbDevTask.fields.testImages.label=テスト画像
i18n_entity.FbDevTask.fields.testResult.label=テスト結果
i18n_entity.FbDevTask.fields.title.label=タイトル
i18n_entity.FbDevTask.fields.version.label=バージョン
i18n_entity.FbDevTask.group=Dev
i18n_entity.FbDevTask.label=共同タスク
i18n_entity.FbDevVersion.fields.createdBy.label=作成者
i18n_entity.FbDevVersion.fields.createdOn.label=作成時間
i18n_entity.FbDevVersion.fields.displayOrder.label=表示順序
i18n_entity.FbDevVersion.fields.done.label=完了しました
i18n_entity.FbDevVersion.fields.id.label=ID
i18n_entity.FbDevVersion.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbDevVersion.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbDevVersion.fields.name.label=お名前
i18n_entity.FbDevVersion.fields.planDoneOn.label=計画完了時間
i18n_entity.FbDevVersion.fields.ro.label=企業向け
i18n_entity.FbDevVersion.fields.version.label=バージョン
i18n_entity.FbDevVersion.group=Dev
i18n_entity.FbDevVersion.label=反復
i18n_entity.FbDirectPutawayOrder.fields.bin.label=リポジトリを上架する
i18n_entity.FbDirectPutawayOrder.fields.btInvProcessed.label=在庫処理済み
i18n_entity.FbDirectPutawayOrder.fields.btLines.label=単一行
i18n_entity.FbDirectPutawayOrder.fields.btOrderKind.inlineOptionBill.items.MfFinishedInbound.label=製品の入庫
i18n_entity.FbDirectPutawayOrder.fields.btOrderKind.inlineOptionBill.items.OtherInbound.label=その他の入庫
i18n_entity.FbDirectPutawayOrder.fields.btOrderKind.inlineOptionBill.items.PurchaseInbound.label=購入入庫
i18n_entity.FbDirectPutawayOrder.fields.btOrderKind.label=タイプ
i18n_entity.FbDirectPutawayOrder.fields.btOrderState.inlineOptionBill.items.Init.label=未提出
i18n_entity.FbDirectPutawayOrder.fields.btOrderState.inlineOptionBill.items.Submitted.label=提出済み
i18n_entity.FbDirectPutawayOrder.fields.btOrderState.label=ビジネスの状態
i18n_entity.FbDirectPutawayOrder.fields.btOrderStateReason.label=ステータスの説明
i18n_entity.FbDirectPutawayOrder.fields.container.label=コンテナを棚に上げる
i18n_entity.FbDirectPutawayOrder.fields.createdBy.label=作成者
i18n_entity.FbDirectPutawayOrder.fields.createdOn.label=作成時間
i18n_entity.FbDirectPutawayOrder.fields.id.label=注文番号
i18n_entity.FbDirectPutawayOrder.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbDirectPutawayOrder.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbDirectPutawayOrder.fields.remark.label=備考
i18n_entity.FbDirectPutawayOrder.fields.ro.label=ルート組織
i18n_entity.FbDirectPutawayOrder.fields.version.label=バージョン
i18n_entity.FbDirectPutawayOrder.group=ウェアハウス
i18n_entity.FbDirectPutawayOrder.label=手動で注文を出す
i18n_entity.FbDirectPutawayOrder.listCard.bin.prefix=リポジトリを上架する
i18n_entity.FbDirectPutawayOrder.listCard.container.prefix=コンテナを棚に上げる
i18n_entity.FbDirectPutawayOrder.states.states.Init.label=未提出
i18n_entity.FbDirectPutawayOrder.states.states.Init.nextStates.Submitted.buttonLabel=提出する
i18n_entity.FbDirectPutawayOrder.states.states.Submitted.label=提出済み
i18n_entity.FbDirectPutawayOrderLine.fields.btLineNo.label=行番号
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterial.label=マテリアル
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialCategory.label=材料の分類番号
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialCategoryName.label=マテリアルカテゴリ名称
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialId.label=マテリアル番号
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialImage.label=マテリアル画像
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialModel.label=マテリアルモデル
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialName.label=マテリアル名
i18n_entity.FbDirectPutawayOrderLine.fields.btMaterialSpec.label=材料仕様
i18n_entity.FbDirectPutawayOrderLine.fields.btParentId.label=所属リスト
i18n_entity.FbDirectPutawayOrderLine.fields.createdBy.label=作成者
i18n_entity.FbDirectPutawayOrderLine.fields.createdOn.label=作成時間
i18n_entity.FbDirectPutawayOrderLine.fields.id.label=ID
i18n_entity.FbDirectPutawayOrderLine.fields.lotNo.label=バッチ番号
i18n_entity.FbDirectPutawayOrderLine.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbDirectPutawayOrderLine.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbDirectPutawayOrderLine.fields.price.label=単価
i18n_entity.FbDirectPutawayOrderLine.fields.qty.label=上架数量
i18n_entity.FbDirectPutawayOrderLine.fields.qualityLevel.label=品質レベル
i18n_entity.FbDirectPutawayOrderLine.fields.ro.label=ルート組織
i18n_entity.FbDirectPutawayOrderLine.fields.subContainerId.label=グリッド番号
i18n_entity.FbDirectPutawayOrderLine.fields.unitLabel.label=ユニット名
i18n_entity.FbDirectPutawayOrderLine.fields.version.label=バージョン
i18n_entity.FbDirectPutawayOrderLine.group=ウェアハウス
i18n_entity.FbDirectPutawayOrderLine.label=手動で単一行を掲載する
i18n_entity.FbDirectPutawayOrderLine.listCard.btMaterialId.prefix=マテリアル番号
i18n_entity.FbDirectPutawayOrderLine.listCard.qty.prefix=数量
i18n_entity.FbDirectPutawayOrderLine.listCard.subContainerId.prefix=グリッド番号
i18n_entity.FbDistrict.fields.btDisabled.label=停止する
i18n_entity.FbDistrict.fields.createdBy.label=作成者
i18n_entity.FbDistrict.fields.createdOn.label=作成時間
i18n_entity.FbDistrict.fields.displayOrder.label=表示順序
i18n_entity.FbDistrict.fields.id.label=倉庫エリア番号
i18n_entity.FbDistrict.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbDistrict.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbDistrict.fields.name.label=エリア名
i18n_entity.FbDistrict.fields.remark.label=備考
i18n_entity.FbDistrict.fields.ro.label=ルート組織
i18n_entity.FbDistrict.fields.structure.label=倉庫エリアの構造
i18n_entity.FbDistrict.fields.version.label=バージョン
i18n_entity.FbDistrict.fields.warehouse.label=所属倉庫
i18n_entity.FbDistrict.group=MainData
i18n_entity.FbDistrict.label=倉庫エリア
i18n_entity.FbGoodsOwner.fields.address.label=アドレス
i18n_entity.FbGoodsOwner.fields.btDisabled.label=停止する
i18n_entity.FbGoodsOwner.fields.contact.label=連絡先
i18n_entity.FbGoodsOwner.fields.createdBy.label=作成者
i18n_entity.FbGoodsOwner.fields.createdOn.label=作成時間
i18n_entity.FbGoodsOwner.fields.id.label=荷主コード
i18n_entity.FbGoodsOwner.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbGoodsOwner.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbGoodsOwner.fields.name.label=荷主の名前
i18n_entity.FbGoodsOwner.fields.phone.label=連絡先電話番号
i18n_entity.FbGoodsOwner.fields.remark.label=備考
i18n_entity.FbGoodsOwner.fields.ro.label=ルート組織
i18n_entity.FbGoodsOwner.fields.version.label=バージョン
i18n_entity.FbGoodsOwner.group=MainData
i18n_entity.FbGoodsOwner.label=荷主
i18n_entity.FbGoodsOwner.listCard.contact.prefix=連絡先
i18n_entity.FbGoodsOwner.listCard.name.prefix=荷主の名前
i18n_entity.FbGoodsOwner.listCard.phone.prefix=電話で
i18n_entity.FbInboundOrder.fields.asnId.label=到着通知番号
i18n_entity.FbInboundOrder.fields.btInvProcessed.label=在庫処理済み
i18n_entity.FbInboundOrder.fields.btLines.label=単一行
i18n_entity.FbInboundOrder.fields.btOrderKind.inlineOptionBill.items.MfFinishedInbound.label=製品の入庫
i18n_entity.FbInboundOrder.fields.btOrderKind.inlineOptionBill.items.OtherInbound.label=その他の入庫
i18n_entity.FbInboundOrder.fields.btOrderKind.inlineOptionBill.items.PurchaseInbound.label=購入入庫
i18n_entity.FbInboundOrder.fields.btOrderKind.label=タイプ
i18n_entity.FbInboundOrder.fields.btOrderState.inlineOptionBill.items.Committed.label=提出済み
i18n_entity.FbInboundOrder.fields.btOrderState.inlineOptionBill.items.Init.label=未提出
i18n_entity.FbInboundOrder.fields.btOrderState.label=ビジネスの状態
i18n_entity.FbInboundOrder.fields.btOrderStateReason.label=ステータスの説明
i18n_entity.FbInboundOrder.fields.callContainerAll.label=空のコンテナーが呼び出されました
i18n_entity.FbInboundOrder.fields.createdBy.label=作成者
i18n_entity.FbInboundOrder.fields.createdOn.label=作成時間
i18n_entity.FbInboundOrder.fields.district.label=入庫エリア
i18n_entity.FbInboundOrder.fields.id.label=注文番号
i18n_entity.FbInboundOrder.fields.mfgFQCOrderId.label=完成品の品質検査番号
i18n_entity.FbInboundOrder.fields.mfgOrderId.label=生産工程番号
i18n_entity.FbInboundOrder.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbInboundOrder.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbInboundOrder.fields.planQty.label=受取数量
i18n_entity.FbInboundOrder.fields.purchaseOrderId.label=発注書番号
i18n_entity.FbInboundOrder.fields.qty.label=今回の入庫数
i18n_entity.FbInboundOrder.fields.recevingOrderId.label=受取伝票番号
i18n_entity.FbInboundOrder.fields.remark.label=備考
i18n_entity.FbInboundOrder.fields.ro.label=ルート組織
i18n_entity.FbInboundOrder.fields.vendor.label=サプライヤー
i18n_entity.FbInboundOrder.fields.version.label=バージョン
i18n_entity.FbInboundOrder.fields.warehouse.label=倉庫に入庫する
i18n_entity.FbInboundOrder.group=ウェアハウス
i18n_entity.FbInboundOrder.label=入庫伝票
i18n_entity.FbInboundOrder.states.states.Committed.label=提出済み
i18n_entity.FbInboundOrder.states.states.Init.label=未提出
i18n_entity.FbInboundOrder.states.states.Init.nextStates.Committed.buttonLabel=提出する
i18n_entity.FbInboundOrderLine.fields.bin.label=リポジトリビット
i18n_entity.FbInboundOrderLine.fields.btLineNo.label=行番号
i18n_entity.FbInboundOrderLine.fields.btMaterial.label=マテリアル
i18n_entity.FbInboundOrderLine.fields.btMaterialCategory.label=材料の分類番号
i18n_entity.FbInboundOrderLine.fields.btMaterialCategoryName.label=マテリアルカテゴリ名称
i18n_entity.FbInboundOrderLine.fields.btMaterialId.label=マテリアル番号
i18n_entity.FbInboundOrderLine.fields.btMaterialImage.label=マテリアル画像
i18n_entity.FbInboundOrderLine.fields.btMaterialModel.label=マテリアルモデル
i18n_entity.FbInboundOrderLine.fields.btMaterialName.label=マテリアル名
i18n_entity.FbInboundOrderLine.fields.btMaterialSpec.label=材料仕様
i18n_entity.FbInboundOrderLine.fields.btParentId.label=所属入庫伝票
i18n_entity.FbInboundOrderLine.fields.callContainerQty.label=割り当てられたコンテナーの数
i18n_entity.FbInboundOrderLine.fields.createdBy.label=作成者
i18n_entity.FbInboundOrderLine.fields.createdOn.label=作成時間
i18n_entity.FbInboundOrderLine.fields.finishedQty.label=以前の在庫数
i18n_entity.FbInboundOrderLine.fields.id.label=ID
i18n_entity.FbInboundOrderLine.fields.lotNo.label=バッチ番号
i18n_entity.FbInboundOrderLine.fields.materialName.label=マテリアル名
i18n_entity.FbInboundOrderLine.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbInboundOrderLine.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbInboundOrderLine.fields.planQty.label=受取数量
i18n_entity.FbInboundOrderLine.fields.price.label=単価
i18n_entity.FbInboundOrderLine.fields.qty.label=在庫数
i18n_entity.FbInboundOrderLine.fields.qualityLevel.label=品質レベル
i18n_entity.FbInboundOrderLine.fields.ro.label=ルート組織
i18n_entity.FbInboundOrderLine.fields.storeQty.label=上架数量
i18n_entity.FbInboundOrderLine.fields.unitLabel.label=ユニット名
i18n_entity.FbInboundOrderLine.fields.version.label=バージョン
i18n_entity.FbInboundOrderLine.group=ウェアハウス
i18n_entity.FbInboundOrderLine.label=単一行の在庫
i18n_entity.FbInboundOrderLine.listCard.btMaterialId.prefix=マテリアル番号
i18n_entity.FbInboundOrderLine.listCard.callContainerQty.prefix=コンテナを割り当てる
i18n_entity.FbInboundOrderLine.listCard.qty.prefix=入庫する
i18n_entity.FbInboundOrderLine.listCard.storeQty.prefix=上架する
i18n_entity.FbInvChange.fields.btMaterial.label=マテリアル
i18n_entity.FbInvChange.fields.btMaterialCategory.label=材料の分類番号
i18n_entity.FbInvChange.fields.btMaterialCategoryName.label=マテリアルカテゴリ名称
i18n_entity.FbInvChange.fields.btMaterialId.label=マテリアル番号
i18n_entity.FbInvChange.fields.btMaterialImage.label=マテリアル画像
i18n_entity.FbInvChange.fields.btMaterialModel.label=マテリアルモデル
i18n_entity.FbInvChange.fields.btMaterialName.label=マテリアル名
i18n_entity.FbInvChange.fields.btMaterialSpec.label=材料仕様
i18n_entity.FbInvChange.fields.container.label=コンテナ
i18n_entity.FbInvChange.fields.createdBy.label=作成者
i18n_entity.FbInvChange.fields.createdOn.label=作成時間
i18n_entity.FbInvChange.fields.id.label=ID
i18n_entity.FbInvChange.fields.lotNo.label=バッチ
i18n_entity.FbInvChange.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbInvChange.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbInvChange.fields.qty.label=数量
i18n_entity.FbInvChange.fields.subContainerId.label=グリッド番号
i18n_entity.FbInvChange.fields.version.label=改訂版
i18n_entity.FbInvChange.group=ウェアハウス
i18n_entity.FbInvChange.label=在庫の変更
i18n_entity.FbInvLayout.fields.amount.label=金額
i18n_entity.FbInvLayout.fields.assemblyLine.label=所属生産ライン
i18n_entity.FbInvLayout.fields.bin.label=ライブラリの場所
i18n_entity.FbInvLayout.fields.btBzDesc.label=ビジネスの説明
i18n_entity.FbInvLayout.fields.btBzMark.label=ビジネスタグ
i18n_entity.FbInvLayout.fields.btMaterial.label=マテリアル
i18n_entity.FbInvLayout.fields.btMaterialCategory.label=材料の分類番号
i18n_entity.FbInvLayout.fields.btMaterialCategoryName.label=マテリアルカテゴリ名称
i18n_entity.FbInvLayout.fields.btMaterialId.label=マテリアル番号
i18n_entity.FbInvLayout.fields.btMaterialImage.label=マテリアル画像
i18n_entity.FbInvLayout.fields.btMaterialModel.label=マテリアルモデル
i18n_entity.FbInvLayout.fields.btMaterialName.label=マテリアル名
i18n_entity.FbInvLayout.fields.btMaterialSpec.label=材料仕様
i18n_entity.FbInvLayout.fields.btMaterialTopCategory.label=材料クラスIの分類番号
i18n_entity.FbInvLayout.fields.btMaterialTopCategoryName.label=マテリアルレベルカテゴリ名称
i18n_entity.FbInvLayout.fields.btPrepare.label=btPrepare
i18n_entity.FbInvLayout.fields.channel.label=路地
i18n_entity.FbInvLayout.fields.column.label=列
i18n_entity.FbInvLayout.fields.createdBy.label=作成者
i18n_entity.FbInvLayout.fields.createdOn.label=作成時間
i18n_entity.FbInvLayout.fields.depth.label=深い
i18n_entity.FbInvLayout.fields.district.label=所在する倉庫エリア
i18n_entity.FbInvLayout.fields.expDate.label=有効期間
i18n_entity.FbInvLayout.fields.id.label=ID
i18n_entity.FbInvLayout.fields.inboundOn.label=入庫時間
i18n_entity.FbInvLayout.fields.inboundOrderId.label=入庫番号
i18n_entity.FbInvLayout.fields.inboundOrderLineNo.label=単一の行番号を入庫する
i18n_entity.FbInvLayout.fields.layer.label=レイヤー
i18n_entity.FbInvLayout.fields.leafContainer.label=最も内側のコンテナ
i18n_entity.FbInvLayout.fields.leafContainerType.label=最も内側のコンテナタイプ
i18n_entity.FbInvLayout.fields.locked.label=ロックする
i18n_entity.FbInvLayout.fields.locked.listBatchButtons.buttons[0].label=ロック解除
i18n_entity.FbInvLayout.fields.lotNo.label=バッチ番号
i18n_entity.FbInvLayout.fields.matLotNo.label=バッチ
i18n_entity.FbInvLayout.fields.matSerialNo.label=シリアル番号
i18n_entity.FbInvLayout.fields.mfgDate.label=製造日
i18n_entity.FbInvLayout.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbInvLayout.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbInvLayout.fields.onRobot.label=所在ロボット
i18n_entity.FbInvLayout.fields.outboundOrderId.label=出荷番号
i18n_entity.FbInvLayout.fields.outboundOrderLineNo.label=出庫単行番号
i18n_entity.FbInvLayout.fields.owner.label=荷主
i18n_entity.FbInvLayout.fields.price.label=単価
i18n_entity.FbInvLayout.fields.qty.label=数量
i18n_entity.FbInvLayout.fields.rack.label=棚
i18n_entity.FbInvLayout.fields.refInv.label=在庫の内訳を引用する
i18n_entity.FbInvLayout.fields.robotBin.label=ロボットのリポジトリ
i18n_entity.FbInvLayout.fields.row.label=並べる
i18n_entity.FbInvLayout.fields.state.inlineOptionBill.items.Assigned.label=割り当て済み
i18n_entity.FbInvLayout.fields.state.inlineOptionBill.items.Received.label=受け取り済み
i18n_entity.FbInvLayout.fields.state.inlineOptionBill.items.Storing.label=ストレージ
i18n_entity.FbInvLayout.fields.state.label=在庫状況
i18n_entity.FbInvLayout.fields.subContainerId.label=グリッド
i18n_entity.FbInvLayout.fields.topContainer.label=最も外側のコンテナ
i18n_entity.FbInvLayout.fields.topContainerType.label=最も外側のコンテナタイプ
i18n_entity.FbInvLayout.fields.usedQty.label=割り当てられた数量
i18n_entity.FbInvLayout.fields.validityDay.label=有効期間の日数
i18n_entity.FbInvLayout.fields.vendor.label=サプライヤー
i18n_entity.FbInvLayout.fields.version.label=改訂版
i18n_entity.FbInvLayout.fields.warehouse.label=所在する倉庫
i18n_entity.FbInvLayout.fields.workSite.label=ワークスペース
i18n_entity.FbInvLayout.group=ウェアハウス
i18n_entity.FbInvLayout.label=在庫の内訳
i18n_entity.FbInvLayout.listCard.bin.prefix=ライブラリビット
i18n_entity.FbInvLayout.listCard.inboundOn.prefix=入庫時間
i18n_entity.FbInvLayout.listCard.lotNo.prefix=バッチ番号
i18n_entity.FbInvLayout.listCard.qty.prefix=数量
i18n_entity.FbInvLayout.listCard.topContainer.prefix=コンテナ
i18n_entity.FbInvSnapShot.fields.btMaterial.label=マテリアル
i18n_entity.FbInvSnapShot.fields.btMaterialCategory.label=材料の分類番号
i18n_entity.FbInvSnapShot.fields.btMaterialCategoryName.label=マテリアルカテゴリ名称
i18n_entity.FbInvSnapShot.fields.btMaterialId.label=マテリアル番号
i18n_entity.FbInvSnapShot.fields.btMaterialImage.label=マテリアル画像
i18n_entity.FbInvSnapShot.fields.btMaterialModel.label=マテリアルモデル
i18n_entity.FbInvSnapShot.fields.btMaterialName.label=マテリアル名
i18n_entity.FbInvSnapShot.fields.btMaterialSpec.label=材料仕様
i18n_entity.FbInvSnapShot.fields.createdBy.label=作成者
i18n_entity.FbInvSnapShot.fields.createdOn.label=作成時間
i18n_entity.FbInvSnapShot.fields.id.label=ID
i18n_entity.FbInvSnapShot.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbInvSnapShot.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbInvSnapShot.fields.qty.label=数量
i18n_entity.FbInvSnapShot.fields.uuid.label=uuid
i18n_entity.FbInvSnapShot.fields.version.label=改訂版
i18n_entity.FbInvSnapShot.group=ウェアハウス
i18n_entity.FbInvSnapShot.label=在庫スナップショット
i18n_entity.FbMaterial.fields.abc.label=ABCの分類
i18n_entity.FbMaterial.fields.btDisabled.label=停止する
i18n_entity.FbMaterial.fields.categoriesDesc.label=材料分類の説明
i18n_entity.FbMaterial.fields.category1.label=レベル1の分類
i18n_entity.FbMaterial.fields.category2.label=二次分類
i18n_entity.FbMaterial.fields.category3.label=レベル3の分類
i18n_entity.FbMaterial.fields.createdBy.label=作成者
i18n_entity.FbMaterial.fields.createdOn.label=作成時間
i18n_entity.FbMaterial.fields.displayDecimals.label=小数点以下の桁数
i18n_entity.FbMaterial.fields.endOn.label=停止日
i18n_entity.FbMaterial.fields.height.label=高さ
i18n_entity.FbMaterial.fields.id.label=マテリアルコーディング
i18n_entity.FbMaterial.fields.image.label=の写真
i18n_entity.FbMaterial.fields.leafCategory.label=材料の分類
i18n_entity.FbMaterial.fields.length.label=長さ
i18n_entity.FbMaterial.fields.mainUnit.label=主な測定単位の名前
i18n_entity.FbMaterial.fields.mainVendor.label=メインサプライヤー
i18n_entity.FbMaterial.fields.mixLotNo.label=バッチ混合
i18n_entity.FbMaterial.fields.mixMaterial.label=材料の混合
i18n_entity.FbMaterial.fields.model.label=モデル
i18n_entity.FbMaterial.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbMaterial.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbMaterial.fields.name.label=マテリアル名
i18n_entity.FbMaterial.fields.owner.label=荷主
i18n_entity.FbMaterial.fields.price.label=単価
i18n_entity.FbMaterial.fields.remark.label=備考
i18n_entity.FbMaterial.fields.ro.label=ルート組織
i18n_entity.FbMaterial.fields.spec.label=材料仕様
i18n_entity.FbMaterial.fields.startOn.label=有効にする日付
i18n_entity.FbMaterial.fields.syncOut.label=外部インポート
i18n_entity.FbMaterial.fields.topCat.label=在庫カテゴリ名
i18n_entity.FbMaterial.fields.unit.label=マテリアルユニット
i18n_entity.FbMaterial.fields.unitLabel.label=ユニット名
i18n_entity.FbMaterial.fields.version.label=バージョン
i18n_entity.FbMaterial.fields.volume.label=ボリューム
i18n_entity.FbMaterial.fields.weight.label=重量
i18n_entity.FbMaterial.fields.width.label=幅
i18n_entity.FbMaterial.group=MainData
i18n_entity.FbMaterial.label=マテリアル
i18n_entity.FbMaterial.listCard.leafCategory.prefix=材料の分類
i18n_entity.FbMaterialCategory.fields.btDisabled.label=停止する
i18n_entity.FbMaterialCategory.fields.createdBy.label=作成者
i18n_entity.FbMaterialCategory.fields.createdOn.label=作成時間
i18n_entity.FbMaterialCategory.fields.id.label=分類番号
i18n_entity.FbMaterialCategory.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbMaterialCategory.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbMaterialCategory.fields.name.label=お名前
i18n_entity.FbMaterialCategory.fields.parent.label=上位の分類
i18n_entity.FbMaterialCategory.fields.remark.label=備考
i18n_entity.FbMaterialCategory.fields.storeDistricts.label=リポジトリ領域
i18n_entity.FbMaterialCategory.fields.version.label=改訂版
i18n_entity.FbMaterialCategory.group=MainData
i18n_entity.FbMaterialCategory.label=材料の分類
i18n_entity.FbMaterialCategory.listCard.storeDistricts.prefix=リポジトリ領域
i18n_entity.FbMaterialContainerMaxQty.fields.btDisabled.label=停止する
i18n_entity.FbMaterialContainerMaxQty.fields.btMaterial.label=マテリアル
i18n_entity.FbMaterialContainerMaxQty.fields.containerType.label=コンテナーの種類
i18n_entity.FbMaterialContainerMaxQty.fields.createdBy.label=作成者
i18n_entity.FbMaterialContainerMaxQty.fields.createdOn.label=作成時間
i18n_entity.FbMaterialContainerMaxQty.fields.id.label=ID
i18n_entity.FbMaterialContainerMaxQty.fields.maxQty.label=最大でもいくつか入れます。
i18n_entity.FbMaterialContainerMaxQty.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbMaterialContainerMaxQty.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbMaterialContainerMaxQty.fields.version.label=改訂版
i18n_entity.FbMaterialContainerMaxQty.group=MainData
i18n_entity.FbMaterialContainerMaxQty.label=マテリアルコンテナ容量
i18n_entity.FbMaterialContainerMaxQty.listCard.btMaterial.prefix=材料の保管
i18n_entity.FbMaterialContainerMaxQty.listCard.maxQty.prefix=最大でも置く
i18n_entity.FbMaterialContainerMaxQty.listCard.maxQty.suffix=一つ
i18n_entity.FbMaterialLot.fields.createdBy.label=作成者
i18n_entity.FbMaterialLot.fields.createdOn.label=作成時間
i18n_entity.FbMaterialLot.fields.disabled.label=無効にする
i18n_entity.FbMaterialLot.fields.id.label=生産ロット番号
i18n_entity.FbMaterialLot.fields.material.label=関連資料
i18n_entity.FbMaterialLot.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbMaterialLot.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbMaterialLot.fields.name.label=生産バッチ名
i18n_entity.FbMaterialLot.fields.remark.label=備考
i18n_entity.FbMaterialLot.fields.ro.label=ルート組織
i18n_entity.FbMaterialLot.fields.version.label=バージョン
i18n_entity.FbMaterialLot.group=MainData
i18n_entity.FbMaterialLot.label=材料バッチ
i18n_entity.FbMaterialUnit.fields.basic.label=基本単位
i18n_entity.FbMaterialUnit.fields.createdBy.label=作成者
i18n_entity.FbMaterialUnit.fields.createdOn.label=作成時間
i18n_entity.FbMaterialUnit.fields.disabled.label=無効にする
i18n_entity.FbMaterialUnit.fields.id.label=ユニットコード
i18n_entity.FbMaterialUnit.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbMaterialUnit.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbMaterialUnit.fields.name.label=ユニット名
i18n_entity.FbMaterialUnit.fields.parent.label=親ユニット
i18n_entity.FbMaterialUnit.fields.ratio.label=変換関係
i18n_entity.FbMaterialUnit.fields.remark.label=備考
i18n_entity.FbMaterialUnit.fields.ro.label=ルート組織
i18n_entity.FbMaterialUnit.fields.version.label=バージョン
i18n_entity.FbMaterialUnit.group=MainData
i18n_entity.FbMaterialUnit.label=マテリアルユニット
i18n_entity.FbOutboundOrder.fields.btInvProcessed.label=在庫処理済み
i18n_entity.FbOutboundOrder.fields.btLines.label=単一行
i18n_entity.FbOutboundOrder.fields.btOrderKind.inlineOptionBill.items.Product.label=商品の出庫
i18n_entity.FbOutboundOrder.fields.btOrderKind.label=タイプ
i18n_entity.FbOutboundOrder.fields.btOrderState.inlineOptionBill.items.Committed.label=提出済み
i18n_entity.FbOutboundOrder.fields.btOrderState.inlineOptionBill.items.Init.label=未提出
i18n_entity.FbOutboundOrder.fields.btOrderState.label=ビジネスの状態
i18n_entity.FbOutboundOrder.fields.btOrderStateReason.label=ステータスの説明
i18n_entity.FbOutboundOrder.fields.createdBy.label=作成者
i18n_entity.FbOutboundOrder.fields.createdOn.label=作成時間
i18n_entity.FbOutboundOrder.fields.customer.label=クライアント
i18n_entity.FbOutboundOrder.fields.direct.label=直接出庫
i18n_entity.FbOutboundOrder.fields.district.label=出庫エリア
i18n_entity.FbOutboundOrder.fields.id.label=注文番号
i18n_entity.FbOutboundOrder.fields.invAssignedAll.label=在庫配分完了
i18n_entity.FbOutboundOrder.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbOutboundOrder.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbOutboundOrder.fields.planQty.label=出庫予定数
i18n_entity.FbOutboundOrder.fields.priority.label=優先度
i18n_entity.FbOutboundOrder.fields.qty.label=今回の出庫数
i18n_entity.FbOutboundOrder.fields.receiver.label=受信ユニット
i18n_entity.FbOutboundOrder.fields.remark.label=備考
i18n_entity.FbOutboundOrder.fields.ro.label=ルート組織
i18n_entity.FbOutboundOrder.fields.saleOrderId.label=販売注文番号
i18n_entity.FbOutboundOrder.fields.saleShipOrderId.label=販売請求書
i18n_entity.FbOutboundOrder.fields.version.label=バージョン
i18n_entity.FbOutboundOrder.fields.warehouse.label=出庫倉庫
i18n_entity.FbOutboundOrder.group=ウェアハウス
i18n_entity.FbOutboundOrder.label=出庫伝票
i18n_entity.FbOutboundOrder.listCard.priority.prefix=優先度
i18n_entity.FbOutboundOrder.states.states.Committed.label=提出済み
i18n_entity.FbOutboundOrder.states.states.Init.label=未提出
i18n_entity.FbOutboundOrder.states.states.Init.nextStates.Committed.buttonLabel=提出する
i18n_entity.FbOutboundOrderLine.fields.btInvRefAll.label=総在庫
i18n_entity.FbOutboundOrderLine.fields.btInvRefMatch.label=利用可能な在庫
i18n_entity.FbOutboundOrderLine.fields.btLineNo.label=行番号
i18n_entity.FbOutboundOrderLine.fields.btMaterial.label=マテリアル
i18n_entity.FbOutboundOrderLine.fields.btMaterialCategory.label=材料の分類番号
i18n_entity.FbOutboundOrderLine.fields.btMaterialCategoryName.label=マテリアルカテゴリ名称
i18n_entity.FbOutboundOrderLine.fields.btMaterialId.label=マテリアル番号
i18n_entity.FbOutboundOrderLine.fields.btMaterialImage.label=マテリアル画像
i18n_entity.FbOutboundOrderLine.fields.btMaterialModel.label=マテリアルモデル
i18n_entity.FbOutboundOrderLine.fields.btMaterialName.label=マテリアル名
i18n_entity.FbOutboundOrderLine.fields.btMaterialSpec.label=材料仕様
i18n_entity.FbOutboundOrderLine.fields.btParentId.label=出荷リストに属する
i18n_entity.FbOutboundOrderLine.fields.createdBy.label=作成者
i18n_entity.FbOutboundOrderLine.fields.createdOn.label=作成時間
i18n_entity.FbOutboundOrderLine.fields.finishedQty.label=以前の出庫数
i18n_entity.FbOutboundOrderLine.fields.id.label=ID
i18n_entity.FbOutboundOrderLine.fields.invAssignedQty.label=割り当てられた在庫の量
i18n_entity.FbOutboundOrderLine.fields.layoutId.label=関連する在庫明細ID
i18n_entity.FbOutboundOrderLine.fields.lotNo.label=バッチ番号
i18n_entity.FbOutboundOrderLine.fields.materialName.label=マテリアル名
i18n_entity.FbOutboundOrderLine.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbOutboundOrderLine.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbOutboundOrderLine.fields.planQty.label=計画された出荷の総数
i18n_entity.FbOutboundOrderLine.fields.qty.label=出庫数
i18n_entity.FbOutboundOrderLine.fields.qualityLevel.label=品質レベル
i18n_entity.FbOutboundOrderLine.fields.ro.label=ルート組織
i18n_entity.FbOutboundOrderLine.fields.version.label=バージョン
i18n_entity.FbOutboundOrderLine.group=ウェアハウス
i18n_entity.FbOutboundOrderLine.label=出庫単行
i18n_entity.FbOutboundOrderLine.listCard.btMaterialId.prefix=マテリアル番号
i18n_entity.FbOutboundOrderLine.listCard.invAssignedQty.prefix=割り当てられた在庫
i18n_entity.FbOutboundOrderLine.listCard.lotNo.prefix=バッチ番号
i18n_entity.FbOutboundOrderLine.listCard.qty.prefix=数量
i18n_entity.FbPkg.fields.createdBy.label=作成者
i18n_entity.FbPkg.fields.createdOn.label=作成時間
i18n_entity.FbPkg.fields.disabled.label=無効にする
i18n_entity.FbPkg.fields.id.label=パッケージコード
i18n_entity.FbPkg.fields.material.label=関連資料
i18n_entity.FbPkg.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbPkg.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbPkg.fields.name.label=パッケージ名
i18n_entity.FbPkg.fields.purpose.label=パッケージの用途
i18n_entity.FbPkg.fields.qty.label=パッケージ内の数量
i18n_entity.FbPkg.fields.remark.label=備考
i18n_entity.FbPkg.fields.ro.label=ルート組織
i18n_entity.FbPkg.fields.version.label=バージョン
i18n_entity.FbPkg.group=MainData
i18n_entity.FbPkg.label=パッケージ仕様
i18n_entity.FbPkg.listCard.disabled.formatMapping[0].replaceText=使用停止済み
i18n_entity.FbPkg.listCard.material.prefix=関連資料
i18n_entity.FbPkg.listCard.name.prefix=パッケージ名
i18n_entity.FbPkg.listCard.qty.prefix=最大でも置く
i18n_entity.FbPkg.listCard.qty.suffix=一つ
i18n_entity.FbVendor.fields.address.label=アドレス
i18n_entity.FbVendor.fields.btDisabled.label=停止する
i18n_entity.FbVendor.fields.contact.label=連絡先
i18n_entity.FbVendor.fields.createdBy.label=作成者
i18n_entity.FbVendor.fields.createdOn.label=作成時間
i18n_entity.FbVendor.fields.email.label=メールアドレス
i18n_entity.FbVendor.fields.id.label=サプライヤー番号
i18n_entity.FbVendor.fields.level.label=レベル
i18n_entity.FbVendor.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbVendor.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbVendor.fields.name.label=サプライヤー名
i18n_entity.FbVendor.fields.phone.label=連絡先電話番号
i18n_entity.FbVendor.fields.remark.label=備考
i18n_entity.FbVendor.fields.ro.label=ルート組織
i18n_entity.FbVendor.fields.version.label=バージョン
i18n_entity.FbVendor.group=MainData
i18n_entity.FbVendor.label=サプライヤー
i18n_entity.FbVendor.listCard.contact.prefix=連絡先
i18n_entity.FbVendor.listCard.name.prefix=お名前
i18n_entity.FbVendor.listCard.phone.prefix=電話で
i18n_entity.FbWarehouse.fields.address.label=アドレス
i18n_entity.FbWarehouse.fields.btDisabled.label=停止する
i18n_entity.FbWarehouse.fields.contact.label=連絡先
i18n_entity.FbWarehouse.fields.createdBy.label=作成者
i18n_entity.FbWarehouse.fields.createdOn.label=作成時間
i18n_entity.FbWarehouse.fields.defaultBin.label=デフォルトのライブラリビット
i18n_entity.FbWarehouse.fields.displayOrder.label=表示順序
i18n_entity.FbWarehouse.fields.id.label=倉庫番号
i18n_entity.FbWarehouse.fields.latitude.label=場所の緯度
i18n_entity.FbWarehouse.fields.longitude.label=位置の経度
i18n_entity.FbWarehouse.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbWarehouse.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbWarehouse.fields.name.label=リポジトリ名
i18n_entity.FbWarehouse.fields.phone.label=連絡先電話番号
i18n_entity.FbWarehouse.fields.remark.label=備考
i18n_entity.FbWarehouse.fields.ro.label=ルート組織
i18n_entity.FbWarehouse.fields.version.label=バージョン
i18n_entity.FbWarehouse.fields.volume.label=ボリューム
i18n_entity.FbWarehouse.group=MainData
i18n_entity.FbWarehouse.label=倉庫
i18n_entity.FbWarehouse.listCard.btDisabled.formatMapping[0].replaceText=使用停止済み
i18n_entity.FbWorkPosition.fields.createdBy.label=作成者
i18n_entity.FbWorkPosition.fields.createdOn.label=作成時間
i18n_entity.FbWorkPosition.fields.disabled.label=無効にする
i18n_entity.FbWorkPosition.fields.id.label=注文番号
i18n_entity.FbWorkPosition.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbWorkPosition.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbWorkPosition.fields.name.label=お名前
i18n_entity.FbWorkPosition.fields.remark.label=備考
i18n_entity.FbWorkPosition.fields.ro.label=ルート組織
i18n_entity.FbWorkPosition.fields.version.label=バージョン
i18n_entity.FbWorkPosition.group=MainData
i18n_entity.FbWorkPosition.label=ポジション
i18n_entity.FbWorkPosition.listCard.disabled.formatMapping[0].replaceText=使用停止済み
i18n_entity.FbWorkSite.fields.bin.label=所属ライブラリ
i18n_entity.FbWorkSite.fields.createdBy.label=作成者
i18n_entity.FbWorkSite.fields.createdOn.label=作成時間
i18n_entity.FbWorkSite.fields.disabled.label=無効にする
i18n_entity.FbWorkSite.fields.id.label=デスクコード
i18n_entity.FbWorkSite.fields.kind.label=デスクタイプ
i18n_entity.FbWorkSite.fields.line.label=所属生産ライン
i18n_entity.FbWorkSite.fields.modifiedBy.label=最後に人を修正する
i18n_entity.FbWorkSite.fields.modifiedOn.label=最後の変更時間
i18n_entity.FbWorkSite.fields.name.label=デスク名
i18n_entity.FbWorkSite.fields.position.label=所属するポジション
i18n_entity.FbWorkSite.fields.remark.label=備考
i18n_entity.FbWorkSite.fields.ro.label=ルート組織
i18n_entity.FbWorkSite.fields.version.label=バージョン
i18n_entity.FbWorkSite.group=MainData
i18n_entity.FbWorkSite.label=ワークスペース
i18n_entity.FbWorkSite.listCard.bin.prefix=所属ライブラリ
i18n_entity.FbWorkSite.listCard.disabled.formatMapping[0].replaceText=使用停止済み
i18n_entity.FbWorkSite.listCard.kind.prefix=デスクタイプ
i18n_entity.FbWorkSite.listCard.name.prefix=工位名
i18n_entity.FbWorkSite.listCard.position.prefix=所属するポジション
i18n_entity.HaiMockRobot.fields.battery.label=バッテリー残量
i18n_entity.HaiMockRobot.fields.createdBy.label=作成者
i18n_entity.HaiMockRobot.fields.createdOn.label=作成時間
i18n_entity.HaiMockRobot.fields.id.label=ID
i18n_entity.HaiMockRobot.fields.modifiedBy.label=最後に人を修正する
i18n_entity.HaiMockRobot.fields.modifiedOn.label=最後の変更時間
i18n_entity.HaiMockRobot.fields.posX.label=位置X
i18n_entity.HaiMockRobot.fields.posY.label=位置Y
i18n_entity.HaiMockRobot.fields.version.label=改訂版
i18n_entity.HaiMockRobot.group=WCS
i18n_entity.HaiMockRobot.label=Haiシミュレーションロボット
i18n_entity.HaiMockRobot.listCard.battery.prefix=バッテリー残量
i18n_entity.HaiMockRobot.listCard.posX.prefix=位置X
i18n_entity.HaiMockRobot.listCard.posY.prefix=位置Y
i18n_entity.HikResourcePack.fields.active.label=メイン設定
i18n_entity.HikResourcePack.fields.createdBy.label=作成者
i18n_entity.HikResourcePack.fields.createdOn.label=作成時間
i18n_entity.HikResourcePack.fields.id.label=ID
i18n_entity.HikResourcePack.fields.lmap.label=レーザーポイントクラウド文書lmap
i18n_entity.HikResourcePack.fields.modifiedBy.label=最後に人を修正する
i18n_entity.HikResourcePack.fields.modifiedOn.label=最後の変更時間
i18n_entity.HikResourcePack.fields.podConfig.label=シェルフ構成XML
i18n_entity.HikResourcePack.fields.remark.label=備考
i18n_entity.HikResourcePack.fields.version.label=改訂版
i18n_entity.HikResourcePack.group=WCS
i18n_entity.HikResourcePack.label=Hikリソースパッケージ
i18n_entity.HikResourcePack.listCard.version.prefix=バージョン
i18n_entity.HumanUser.fields.btDisabled.label=停止する
i18n_entity.HumanUser.fields.company.label=会社
i18n_entity.HumanUser.fields.createdBy.label=作成者
i18n_entity.HumanUser.fields.createdOn.label=作成時間
i18n_entity.HumanUser.fields.directSignInDisabled.label=直接ログイン禁止
i18n_entity.HumanUser.fields.disabled.label=無効にする
i18n_entity.HumanUser.fields.email.label=メール
i18n_entity.HumanUser.fields.externalAdded.label=外部追加
i18n_entity.HumanUser.fields.externalSource.label=外部ソース
i18n_entity.HumanUser.fields.externalUserId.label=外部ユーザー ID
i18n_entity.HumanUser.fields.id.label=ナンバリング
i18n_entity.HumanUser.fields.modifiedBy.label=最後に人を修正する
i18n_entity.HumanUser.fields.modifiedOn.label=最後の変更時間
i18n_entity.HumanUser.fields.password.label=パスワード
i18n_entity.HumanUser.fields.phone.label=携帯電話
i18n_entity.HumanUser.fields.pwdErrCount.label=パスワードエラー数
i18n_entity.HumanUser.fields.pwdSetOn.label=パスワード設定時間
i18n_entity.HumanUser.fields.ro.label=ルート組織
i18n_entity.HumanUser.fields.roAdmin.label=エンタープライズ管理者
i18n_entity.HumanUser.fields.roleIds.label=キャラクター
i18n_entity.HumanUser.fields.truename.label=本名
i18n_entity.HumanUser.fields.username.label=ユーザー名
i18n_entity.HumanUser.fields.version.label=バージョン
i18n_entity.HumanUser.group=ユーザ
i18n_entity.HumanUser.label=ユーザー
i18n_entity.HumanUser.listCard.phone.prefix=携帯電話
i18n_entity.HumanUser.listCard.truename.prefix=本名
i18n_entity.HumanUserSession.fields.createdBy.label=作成者
i18n_entity.HumanUserSession.fields.createdOn.label=作成時間
i18n_entity.HumanUserSession.fields.expiredAt.label=有効期限
i18n_entity.HumanUserSession.fields.id.label=ID
i18n_entity.HumanUserSession.fields.modifiedBy.label=最後に人を修正する
i18n_entity.HumanUserSession.fields.modifiedOn.label=最後の変更時間
i18n_entity.HumanUserSession.fields.ro.label=RO
i18n_entity.HumanUserSession.fields.userId.label=ユーザー
i18n_entity.HumanUserSession.fields.userToken.label=トークン
i18n_entity.HumanUserSession.fields.version.label=改訂版
i18n_entity.HumanUserSession.group=コア
i18n_entity.HumanUserSession.label=ユーザーセッション
i18n_entity.IdGen.fields.createdBy.label=作成者
i18n_entity.IdGen.fields.createdOn.label=作成時間
i18n_entity.IdGen.fields.flowNo.label=流水番号
i18n_entity.IdGen.fields.id.label=ナンバリング
i18n_entity.IdGen.fields.key.label=グループ
i18n_entity.IdGen.fields.modifiedBy.label=最後に人を修正する
i18n_entity.IdGen.fields.modifiedOn.label=最後の変更時間
i18n_entity.IdGen.fields.ro.label=ルート組織
i18n_entity.IdGen.fields.timestamp.label=日付
i18n_entity.IdGen.fields.version.label=バージョン
i18n_entity.IdGen.group=コア
i18n_entity.IdGen.label=番号付けルール
i18n_entity.LePage.fields.content.label=コンテンツ
i18n_entity.LePage.fields.createdBy.label=作成者
i18n_entity.LePage.fields.createdOn.label=作成時間
i18n_entity.LePage.fields.id.label=ID
i18n_entity.LePage.fields.label.label=表示名
i18n_entity.LePage.fields.modifiedBy.label=最後に人を修正する
i18n_entity.LePage.fields.modifiedOn.label=最後の変更時間
i18n_entity.LePage.fields.name.label=ページ名
i18n_entity.LePage.fields.version.label=改訂版
i18n_entity.LePage.group=コア
i18n_entity.LePage.label=カスタムインターフェース
i18n_entity.ListFilterCase.fields.content.label=コンテンツ
i18n_entity.ListFilterCase.fields.createdBy.label=作成者
i18n_entity.ListFilterCase.fields.createdOn.label=作成時間
i18n_entity.ListFilterCase.fields.global.label=グローバル
i18n_entity.ListFilterCase.fields.id.label=ナンバリング
i18n_entity.ListFilterCase.fields.modifiedBy.label=最後に人を修正する
i18n_entity.ListFilterCase.fields.modifiedOn.label=最後の変更時間
i18n_entity.ListFilterCase.fields.owner.label=所有者
i18n_entity.ListFilterCase.fields.page.label=ページ
i18n_entity.ListFilterCase.fields.ro.label=ルート組織
i18n_entity.ListFilterCase.fields.version.label=バージョン
i18n_entity.ListFilterCase.group=コア
i18n_entity.ListFilterCase.label=リストクエリシナリオ
i18n_entity.MockSeerRobot.fields.createdBy.label=作成者
i18n_entity.MockSeerRobot.fields.createdOn.label=作成時間
i18n_entity.MockSeerRobot.fields.currentStation.label=現在のサイト
i18n_entity.MockSeerRobot.fields.id.label=ID
i18n_entity.MockSeerRobot.fields.modifiedBy.label=最後に人を修正する
i18n_entity.MockSeerRobot.fields.modifiedOn.label=最後の変更時間
i18n_entity.MockSeerRobot.fields.staringStation.label=出生サイト
i18n_entity.MockSeerRobot.fields.version.label=改訂版
i18n_entity.MockSeerRobot.group=WCS
i18n_entity.MockSeerRobot.label=シミュレーションSeerロボット
i18n_entity.MockSeerRobot.listCard.createdOn.prefix=作成する
i18n_entity.MockSeerRobot.listCard.staringStation.prefix=出生サイト
i18n_entity.MrRobotRuntimeRecord.fields.channel.label=路地にいる
i18n_entity.MrRobotRuntimeRecord.fields.createdBy.label=作成者
i18n_entity.MrRobotRuntimeRecord.fields.createdOn.label=作成時間
i18n_entity.MrRobotRuntimeRecord.fields.ctOrders.label=コンテナハンドリングシート
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Charging.label=充電中です。
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Idle.label=空いている
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Inbound.label=入庫中
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Outbound.label=出庫中です。
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Parking.label=停泊する
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.inlineOptionBill.items.Tasking.label=運送状の実行
i18n_entity.MrRobotRuntimeRecord.fields.extTaskStatus.label=拡張タスクの状態
i18n_entity.MrRobotRuntimeRecord.fields.faultMsg.label=障害情報
i18n_entity.MrRobotRuntimeRecord.fields.id.label=ID
i18n_entity.MrRobotRuntimeRecord.fields.modifiedBy.label=最後に人を修正する
i18n_entity.MrRobotRuntimeRecord.fields.modifiedOn.label=最後の変更時間
i18n_entity.MrRobotRuntimeRecord.fields.offDuty.label=注文を受け付けない
i18n_entity.MrRobotRuntimeRecord.fields.port.label=在庫口
i18n_entity.MrRobotRuntimeRecord.fields.rtBins.label=貨物状況のリアルタイムスケジューリング
i18n_entity.MrRobotRuntimeRecord.fields.rtCmdStatus.label=リアルタイムスケジューリングの実行状態
i18n_entity.MrRobotRuntimeRecord.fields.rtCurrentOrder.label=現在の運送状をリアルタイムでスケジュールする
i18n_entity.MrRobotRuntimeRecord.fields.rtCurrentStep.label=現在の運送状ステップをリアルタイムでスケジュールする
i18n_entity.MrRobotRuntimeRecord.fields.rtOrders.label=リアルタイムスケジューリング運送状リスト
i18n_entity.MrRobotRuntimeRecord.fields.selectSameOrderId.label=この運送状の手順を指定する必要があります
i18n_entity.MrRobotRuntimeRecord.fields.taskStatus.inlineOptionBill.items.Charging.label=充電中です。
i18n_entity.MrRobotRuntimeRecord.fields.taskStatus.inlineOptionBill.items.Idle.label=空いている
i18n_entity.MrRobotRuntimeRecord.fields.taskStatus.inlineOptionBill.items.Tasking.label=タスク中
i18n_entity.MrRobotRuntimeRecord.fields.taskStatus.label=タスクの状態
i18n_entity.MrRobotRuntimeRecord.fields.version.label=改訂版
i18n_entity.MrRobotRuntimeRecord.group=WCS
i18n_entity.MrRobotRuntimeRecord.label=移動ロボットの実行記録
i18n_entity.MrRobotRuntimeRecord.listCard.ctOrders.prefix=コンテナハンドリングシート
i18n_entity.MrRobotRuntimeRecord.listCard.rtBins.prefix=商品
i18n_entity.MrRobotRuntimeRecord.listCard.rtCurrentOrder.prefix=現在の運送状
i18n_entity.MrRobotSystemConfig.fields.category.label=カテゴリー
i18n_entity.MrRobotSystemConfig.fields.connectionType.inlineOptionBill.items.GwWs.label=ゲートウェイ接続サーバ（GWチャネル）
i18n_entity.MrRobotSystemConfig.fields.connectionType.inlineOptionBill.items.GwWsLight.label=ゲートウェイ接続サーバ（光通信）
i18n_entity.MrRobotSystemConfig.fields.connectionType.inlineOptionBill.items.Mock.label=シミュレーション
i18n_entity.MrRobotSystemConfig.fields.connectionType.inlineOptionBill.items.Rbk.label=サーバー接続ボット
i18n_entity.MrRobotSystemConfig.fields.connectionType.label=接続タイプ
i18n_entity.MrRobotSystemConfig.fields.createdBy.label=作成者
i18n_entity.MrRobotSystemConfig.fields.createdOn.label=作成時間
i18n_entity.MrRobotSystemConfig.fields.disabled.label=停止する
i18n_entity.MrRobotSystemConfig.fields.gwAuthId.label=ゲートウェイログインID
i18n_entity.MrRobotSystemConfig.fields.gwAuthSecret.label=ゲートウェイログインキー
i18n_entity.MrRobotSystemConfig.fields.id.label=ID
i18n_entity.MrRobotSystemConfig.fields.image.label=の写真
i18n_entity.MrRobotSystemConfig.fields.modifiedBy.label=最後に人を修正する
i18n_entity.MrRobotSystemConfig.fields.modifiedOn.label=最後の変更時間
i18n_entity.MrRobotSystemConfig.fields.offDuty.label=注文を受け付けない
i18n_entity.MrRobotSystemConfig.fields.robotHost.label=ロボットのアドレス
i18n_entity.MrRobotSystemConfig.fields.scene.label=シーン
i18n_entity.MrRobotSystemConfig.fields.selfBinNum.label=最大積載量
i18n_entity.MrRobotSystemConfig.fields.ssl.label=SSL/TSL暗号化
i18n_entity.MrRobotSystemConfig.fields.tags.label=タグ
i18n_entity.MrRobotSystemConfig.fields.taskMode.inlineOptionBill.items.Custom.label=カスタムスケジューリング
i18n_entity.MrRobotSystemConfig.fields.taskMode.inlineOptionBill.items.SingleRobot.label=M 4 A 1
i18n_entity.MrRobotSystemConfig.fields.taskMode.inlineOptionBill.items.Tom.label=コア
i18n_entity.MrRobotSystemConfig.fields.taskMode.label=タスクモード
i18n_entity.MrRobotSystemConfig.fields.tomId.label=SeerスケジューリングID
i18n_entity.MrRobotSystemConfig.fields.vendor.inlineOptionBill.items.Hai.label=Hai
i18n_entity.MrRobotSystemConfig.fields.vendor.inlineOptionBill.items.Hik.label=Hik
i18n_entity.MrRobotSystemConfig.fields.vendor.inlineOptionBill.items.Seer.label=Seer
i18n_entity.MrRobotSystemConfig.fields.vendor.label=メーカー
i18n_entity.MrRobotSystemConfig.fields.version.label=改訂版
i18n_entity.MrRobotSystemConfig.group=WCS
i18n_entity.MrRobotSystemConfig.label=移動ロボットシステム構成
i18n_entity.MrRobotSystemConfig.listCard.scene.prefix=シーン
i18n_entity.MrRobotSystemConfig.listCard.selfBinNum.prefix=最大積載量
i18n_entity.NdcOrder.fields.allowLoad.label=ピックアップを許可する
i18n_entity.NdcOrder.fields.allowUnload.label=出荷を許可する
i18n_entity.NdcOrder.fields.createdBy.label=作成者
i18n_entity.NdcOrder.fields.createdOn.label=作成時間
i18n_entity.NdcOrder.fields.endBin.label=終点
i18n_entity.NdcOrder.fields.falconId.label=ファルコンクエスト
i18n_entity.NdcOrder.fields.id.label=ID
i18n_entity.NdcOrder.fields.ikey.label=ikey
i18n_entity.NdcOrder.fields.index.label=インデックス
i18n_entity.NdcOrder.fields.modifiedBy.label=最後に人を修正する
i18n_entity.NdcOrder.fields.modifiedOn.label=最後の変更時間
i18n_entity.NdcOrder.fields.priority.label=優先度
i18n_entity.NdcOrder.fields.startBin.label=スタート
i18n_entity.NdcOrder.fields.status.label=状態
i18n_entity.NdcOrder.fields.version.label=バージョン
i18n_entity.NdcOrder.group=NDC
i18n_entity.NdcOrder.label=NDC運送状
i18n_entity.OrderFlowRecord.fields.createdBy.label=作成者
i18n_entity.OrderFlowRecord.fields.createdOn.label=作成時間
i18n_entity.OrderFlowRecord.fields.id.label=ID
i18n_entity.OrderFlowRecord.fields.modifiedBy.label=最後に人を修正する
i18n_entity.OrderFlowRecord.fields.modifiedOn.label=最後の変更時間
i18n_entity.OrderFlowRecord.fields.pushType.label=タイプ
i18n_entity.OrderFlowRecord.fields.sourceOrderId.label=ソース番号
i18n_entity.OrderFlowRecord.fields.sourceOrderName.label=ソース名
i18n_entity.OrderFlowRecord.fields.targetOrderId.label=新しい注文番号
i18n_entity.OrderFlowRecord.fields.targetOrderName.label=新しいシングル名
i18n_entity.OrderFlowRecord.fields.targetOrderType.label=新しいシングルタイプ
i18n_entity.OrderFlowRecord.fields.txId.label=トランザクションID
i18n_entity.OrderFlowRecord.fields.version.label=改訂版
i18n_entity.OrderFlowRecord.group=コア
i18n_entity.OrderFlowRecord.label=文書の流通記録
i18n_entity.PickOrder.fields.allUsed.label=全体の仕分け
i18n_entity.PickOrder.fields.btLines.label=単一行
i18n_entity.PickOrder.fields.btOrderKind.inlineOptionBill.items.NormalTask.label=ロードタスク
i18n_entity.PickOrder.fields.btOrderKind.label=タイプ
i18n_entity.PickOrder.fields.btOrderState.inlineOptionBill.items.Done.label=仕分け完了
i18n_entity.PickOrder.fields.btOrderState.inlineOptionBill.items.Todo.label=仕分けを待つ
i18n_entity.PickOrder.fields.btOrderState.label=ビジネスの状態
i18n_entity.PickOrder.fields.btOrderStateReason.label=ステータスの説明
i18n_entity.PickOrder.fields.container.label=コンテナ
i18n_entity.PickOrder.fields.containerBackOrderId.label=コンテナのリポジトリ配送番号
i18n_entity.PickOrder.fields.containerOutDone.label=コンテナの出庫と運搬が完了しました。
i18n_entity.PickOrder.fields.containerOutOrderId.label=コンテナ出荷伝票番号
i18n_entity.PickOrder.fields.createdBy.label=作成者
i18n_entity.PickOrder.fields.createdOn.label=作成時間
i18n_entity.PickOrder.fields.id.label=注文番号
i18n_entity.PickOrder.fields.modifiedBy.label=最後に人を修正する
i18n_entity.PickOrder.fields.modifiedOn.label=最後の変更時間
i18n_entity.PickOrder.fields.sourceOrderId.label=出荷番号
i18n_entity.PickOrder.fields.submittedPostProcessed.label=後処理完了
i18n_entity.PickOrder.fields.version.label=改訂版
i18n_entity.PickOrder.group=ウェアハウス
i18n_entity.PickOrder.label=仕分けシート
i18n_entity.PickOrder.listCard.container.prefix=コンテナ
i18n_entity.PickOrder.listCard.sourceOrderId.prefix=出庫伝票
i18n_entity.PickOrder.states.states.Done.label=仕分け完了
i18n_entity.PickOrder.states.states.Todo.label=仕分けを待つ
i18n_entity.PickOrder.states.states.Todo.nextStates.Done.buttonLabel=完了する
i18n_entity.PickOrderLine.fields.btLineNo.label=行番号
i18n_entity.PickOrderLine.fields.btMaterial.label=マテリアル
i18n_entity.PickOrderLine.fields.btMaterialCategory.label=材料の分類番号
i18n_entity.PickOrderLine.fields.btMaterialCategoryName.label=マテリアルカテゴリ名称
i18n_entity.PickOrderLine.fields.btMaterialId.label=マテリアル番号
i18n_entity.PickOrderLine.fields.btMaterialImage.label=マテリアル画像
i18n_entity.PickOrderLine.fields.btMaterialModel.label=マテリアルモデル
i18n_entity.PickOrderLine.fields.btMaterialName.label=マテリアル名
i18n_entity.PickOrderLine.fields.btMaterialSpec.label=材料仕様
i18n_entity.PickOrderLine.fields.btParentId.label=所属書類
i18n_entity.PickOrderLine.fields.container.label=コンテナ
i18n_entity.PickOrderLine.fields.createdBy.label=作成者
i18n_entity.PickOrderLine.fields.createdOn.label=作成時間
i18n_entity.PickOrderLine.fields.id.label=ID
i18n_entity.PickOrderLine.fields.lotNo.label=バッチ番号
i18n_entity.PickOrderLine.fields.modifiedBy.label=最後に人を修正する
i18n_entity.PickOrderLine.fields.modifiedOn.label=最後の変更時間
i18n_entity.PickOrderLine.fields.planQty.label=数を選び出すことを期待する
i18n_entity.PickOrderLine.fields.qty.label=実際のピッキング数
i18n_entity.PickOrderLine.fields.qualityLevel.label=品質レベル
i18n_entity.PickOrderLine.fields.sourceLineId.label=ソース単一行ID
i18n_entity.PickOrderLine.fields.sourceLineNo.label=ソース行番号
i18n_entity.PickOrderLine.fields.sourceOrderId.label=ソースドキュメント
i18n_entity.PickOrderLine.fields.subContainerId.label=グリッド番号
i18n_entity.PickOrderLine.fields.version.label=改訂版
i18n_entity.PickOrderLine.group=ウェアハウス
i18n_entity.PickOrderLine.label=単行のソート
i18n_entity.PickOrderLine.listCard.btMaterialId.prefix=マテリアル番号
i18n_entity.PickOrderLine.listCard.lotNo.prefix=バッチ
i18n_entity.PickOrderLine.listCard.planQty.prefix=期待を選び出す
i18n_entity.PickOrderLine.listCard.qty.prefix=実際にピッキング
i18n_entity.PickOrderLine.listCard.subContainerId.prefix=グリッド番号
i18n_entity.PlcDeviceConfig.fields.autoRetry.label=自動再接続
i18n_entity.PlcDeviceConfig.fields.createdBy.label=作成者
i18n_entity.PlcDeviceConfig.fields.createdOn.label=作成時間
i18n_entity.PlcDeviceConfig.fields.disabled.label=停止する
i18n_entity.PlcDeviceConfig.fields.endpoint.label=接続文字列
i18n_entity.PlcDeviceConfig.fields.host.label=アドレス/IP
i18n_entity.PlcDeviceConfig.fields.id.label=名前(ID)
i18n_entity.PlcDeviceConfig.fields.maxRetry.label=最大再試行回数
i18n_entity.PlcDeviceConfig.fields.modifiedBy.label=最後に人を修正する
i18n_entity.PlcDeviceConfig.fields.modifiedOn.label=最後の変更時間
i18n_entity.PlcDeviceConfig.fields.port.label=ポート
i18n_entity.PlcDeviceConfig.fields.rack.label=S 7ラック番号
i18n_entity.PlcDeviceConfig.fields.retryDelay.label=再試行待機(ミリ秒)
i18n_entity.PlcDeviceConfig.fields.slot.label=S 7スロット番号
i18n_entity.PlcDeviceConfig.fields.subType.label=サブタイプ
i18n_entity.PlcDeviceConfig.fields.timeout.label=接続タイムアウト時間(ミリ秒)
i18n_entity.PlcDeviceConfig.fields.type.inlineOptionBill.items.Modbus.label=Modbus
i18n_entity.PlcDeviceConfig.fields.type.inlineOptionBill.items.OPCUA.label=OPCUA
i18n_entity.PlcDeviceConfig.fields.type.inlineOptionBill.items.S7.label=S 7
i18n_entity.PlcDeviceConfig.fields.type.label=タイプ
i18n_entity.PlcDeviceConfig.fields.version.label=改訂版
i18n_entity.PlcDeviceConfig.group=WCS
i18n_entity.PlcDeviceConfig.label=PLCデバイス構成
i18n_entity.PlcDeviceConfig.listCard.host.prefix=アドレス/IP
i18n_entity.PlcDeviceConfig.listCard.port.prefix=ポート
i18n_entity.PlcRwLog.fields.action.label=操作する
i18n_entity.PlcRwLog.fields.createdBy.label=作成者
i18n_entity.PlcRwLog.fields.createdOn.label=作成時間
i18n_entity.PlcRwLog.fields.deviceName.label=デバイス名
i18n_entity.PlcRwLog.fields.deviceType.label=デバイスの種類
i18n_entity.PlcRwLog.fields.id.label=ID
i18n_entity.PlcRwLog.fields.ip.label=IP
i18n_entity.PlcRwLog.fields.modifiedBy.label=最後に人を修正する
i18n_entity.PlcRwLog.fields.modifiedOn.label=最後の変更時間
i18n_entity.PlcRwLog.fields.oldValueDesc.label=元の値
i18n_entity.PlcRwLog.fields.reason.label=理由
i18n_entity.PlcRwLog.fields.rw.inlineOptionBill.items.Read.label=読む
i18n_entity.PlcRwLog.fields.rw.inlineOptionBill.items.Write.label=書く
i18n_entity.PlcRwLog.fields.rw.label=読み書き
i18n_entity.PlcRwLog.fields.valueDesc.label=値
i18n_entity.PlcRwLog.fields.version.label=改訂版
i18n_entity.PlcRwLog.group=WCS
i18n_entity.PlcRwLog.label=PLCの読み書き記録
i18n_entity.PlcRwLog.listCard.oldValueDesc.prefix=元の値
i18n_entity.PlcRwLog.listCard.valueDesc.prefix=値
i18n_entity.PutinContainerOrder.fields.btLines.label=単一行
i18n_entity.PutinContainerOrder.fields.btOrderKind.inlineOptionBill.items.NormalTask.label=ロードタスク
i18n_entity.PutinContainerOrder.fields.btOrderKind.label=タイプ
i18n_entity.PutinContainerOrder.fields.btOrderState.inlineOptionBill.items.Done.label=上架完了
i18n_entity.PutinContainerOrder.fields.btOrderState.inlineOptionBill.items.Filled.label=読み込み完了
i18n_entity.PutinContainerOrder.fields.btOrderState.inlineOptionBill.items.Todo.label=読み込みを待つ
i18n_entity.PutinContainerOrder.fields.btOrderState.label=ビジネスの状態
i18n_entity.PutinContainerOrder.fields.btOrderStateReason.label=ステータスの説明
i18n_entity.PutinContainerOrder.fields.container.label=コンテナ
i18n_entity.PutinContainerOrder.fields.containerBackOrderId.label=コンテナのリポジトリ配送番号
i18n_entity.PutinContainerOrder.fields.containerOutDone.label=コンテナの出庫と運搬が完了しました。
i18n_entity.PutinContainerOrder.fields.containerOutOrderId.label=コンテナ出荷伝票番号
i18n_entity.PutinContainerOrder.fields.createdBy.label=作成者
i18n_entity.PutinContainerOrder.fields.createdOn.label=作成時間
i18n_entity.PutinContainerOrder.fields.id.label=注文番号
i18n_entity.PutinContainerOrder.fields.modifiedBy.label=最後に人を修正する
i18n_entity.PutinContainerOrder.fields.modifiedOn.label=最後の変更時間
i18n_entity.PutinContainerOrder.fields.sourceOrderId.label=入庫番号
i18n_entity.PutinContainerOrder.fields.version.label=改訂版
i18n_entity.PutinContainerOrder.group=ウェアハウス
i18n_entity.PutinContainerOrder.label=ロードリスト
i18n_entity.PutinContainerOrder.listCard.container.prefix=コンテナ
i18n_entity.PutinContainerOrder.states.states.Done.label=上架完了
i18n_entity.PutinContainerOrder.states.states.Filled.label=読み込み完了
i18n_entity.PutinContainerOrder.states.states.Todo.label=読み込みを待つ
i18n_entity.PutinContainerOrder.states.states.Todo.nextStates.Filled.buttonLabel=完了する
i18n_entity.PutinContainerOrderLine.fields.btLineNo.label=行番号
i18n_entity.PutinContainerOrderLine.fields.btMaterial.label=マテリアル
i18n_entity.PutinContainerOrderLine.fields.btMaterialCategory.label=材料の分類番号
i18n_entity.PutinContainerOrderLine.fields.btMaterialCategoryName.label=マテリアルカテゴリ名称
i18n_entity.PutinContainerOrderLine.fields.btMaterialId.label=マテリアル番号
i18n_entity.PutinContainerOrderLine.fields.btMaterialImage.label=マテリアル画像
i18n_entity.PutinContainerOrderLine.fields.btMaterialModel.label=マテリアルモデル
i18n_entity.PutinContainerOrderLine.fields.btMaterialName.label=マテリアル名
i18n_entity.PutinContainerOrderLine.fields.btMaterialSpec.label=材料仕様
i18n_entity.PutinContainerOrderLine.fields.btParentId.label=所属書類
i18n_entity.PutinContainerOrderLine.fields.container.label=コンテナ
i18n_entity.PutinContainerOrderLine.fields.createdBy.label=作成者
i18n_entity.PutinContainerOrderLine.fields.createdOn.label=作成時間
i18n_entity.PutinContainerOrderLine.fields.id.label=ID
i18n_entity.PutinContainerOrderLine.fields.lotNo.label=バッチ番号
i18n_entity.PutinContainerOrderLine.fields.modifiedBy.label=最後に人を修正する
i18n_entity.PutinContainerOrderLine.fields.modifiedOn.label=最後の変更時間
i18n_entity.PutinContainerOrderLine.fields.planQty.label=計画されたロード数
i18n_entity.PutinContainerOrderLine.fields.price.label=単価
i18n_entity.PutinContainerOrderLine.fields.qty.label=実際にロードする数
i18n_entity.PutinContainerOrderLine.fields.qualityLevel.label=品質レベル
i18n_entity.PutinContainerOrderLine.fields.sourceLineId.label=ソース単一行ID
i18n_entity.PutinContainerOrderLine.fields.sourceLineNo.label=ソース行番号
i18n_entity.PutinContainerOrderLine.fields.sourceOrderId.label=ソースドキュメント
i18n_entity.PutinContainerOrderLine.fields.subContainerId.label=グリッド番号
i18n_entity.PutinContainerOrderLine.fields.unitLabel.label=ユニット名
i18n_entity.PutinContainerOrderLine.fields.version.label=改訂版
i18n_entity.PutinContainerOrderLine.group=ウェアハウス
i18n_entity.PutinContainerOrderLine.label=積み込み単行
i18n_entity.PutinContainerOrderLine.listCard.btMaterialId.prefix=マテリアル番号
i18n_entity.PutinContainerOrderLine.listCard.btMaterialName.prefix=マテリアル名
i18n_entity.PutinContainerOrderLine.listCard.lotNo.prefix=バッチ番号
i18n_entity.PutinContainerOrderLine.listCard.planQty.prefix=計画の読み込み
i18n_entity.PutinContainerOrderLine.listCard.qty.prefix=実際に読み込む
i18n_entity.PutinContainerOrderLine.listCard.subContainerId.prefix=グリッド番号
i18n_entity.QsInboundOrder.fields.btBzKind.inlineOptionBill.items.Normal.label=一般入庫
i18n_entity.QsInboundOrder.fields.btBzKind.inlineOptionBill.items.Other.label=その他の入庫
i18n_entity.QsInboundOrder.fields.btBzKind.label=ビジネスタイプ
i18n_entity.QsInboundOrder.fields.btLines.label=単一行
i18n_entity.QsInboundOrder.fields.btOrderState.inlineOptionBill.items.Cancelled.label=キャンセル済み
i18n_entity.QsInboundOrder.fields.btOrderState.inlineOptionBill.items.Committed.label=提出済み
i18n_entity.QsInboundOrder.fields.btOrderState.inlineOptionBill.items.Init.label=未提出
i18n_entity.QsInboundOrder.fields.btOrderState.label=ビジネスの状態
i18n_entity.QsInboundOrder.fields.btOrderStateReason.label=ステータスの説明
i18n_entity.QsInboundOrder.fields.callContainerAll.label=空のコンテナの割り当てが完了しました
i18n_entity.QsInboundOrder.fields.createdBy.label=作成者
i18n_entity.QsInboundOrder.fields.createdOn.label=作成時間
i18n_entity.QsInboundOrder.fields.id.label=注文番号
i18n_entity.QsInboundOrder.fields.modifiedBy.label=最後に人を修正する
i18n_entity.QsInboundOrder.fields.modifiedOn.label=最後の変更時間
i18n_entity.QsInboundOrder.fields.otherType.label=その他の種類
i18n_entity.QsInboundOrder.fields.priority.label=優先度
i18n_entity.QsInboundOrder.fields.remark.label=備考
i18n_entity.QsInboundOrder.fields.ro.label=ルート組織
i18n_entity.QsInboundOrder.fields.version.label=バージョン
i18n_entity.QsInboundOrder.group=クイックストア
i18n_entity.QsInboundOrder.kinds.kinds.Normal.label=一般入庫
i18n_entity.QsInboundOrder.kinds.kinds.Other.label=その他の入庫
i18n_entity.QsInboundOrder.label=QS入庫書
i18n_entity.QsInboundOrder.states.states.Cancelled.label=キャンセル済み
i18n_entity.QsInboundOrder.states.states.Committed.label=提出済み
i18n_entity.QsInboundOrder.states.states.Init.label=未提出
i18n_entity.QsInboundOrder.states.states.Init.nextStates.Cancelled.buttonLabel=キャンセル
i18n_entity.QsInboundOrder.states.states.Init.nextStates.Committed.buttonLabel=提出する
i18n_entity.QsInboundOrderLine.fields.btLineNo.label=行番号
i18n_entity.QsInboundOrderLine.fields.btMaterial.label=マテリアル
i18n_entity.QsInboundOrderLine.fields.btMaterialCategory.label=材料の分類番号
i18n_entity.QsInboundOrderLine.fields.btMaterialCategoryName.label=マテリアルカテゴリ名称
i18n_entity.QsInboundOrderLine.fields.btMaterialId.label=マテリアル番号
i18n_entity.QsInboundOrderLine.fields.btMaterialImage.label=マテリアル画像
i18n_entity.QsInboundOrderLine.fields.btMaterialModel.label=マテリアルモデル
i18n_entity.QsInboundOrderLine.fields.btMaterialName.label=マテリアル名
i18n_entity.QsInboundOrderLine.fields.btMaterialSpec.label=材料仕様
i18n_entity.QsInboundOrderLine.fields.btParentId.label=出荷リストに属する
i18n_entity.QsInboundOrderLine.fields.ccQty.label=空のコンテナを呼び出して、数量を割り当てます。
i18n_entity.QsInboundOrderLine.fields.createdBy.label=作成者
i18n_entity.QsInboundOrderLine.fields.createdOn.label=作成時間
i18n_entity.QsInboundOrderLine.fields.id.label=ID
i18n_entity.QsInboundOrderLine.fields.lotNo.label=バッチ番号
i18n_entity.QsInboundOrderLine.fields.modifiedBy.label=最後に人を修正する
i18n_entity.QsInboundOrderLine.fields.modifiedOn.label=最後の変更時間
i18n_entity.QsInboundOrderLine.fields.priority.label=優先度
i18n_entity.QsInboundOrderLine.fields.qty.label=在庫数
i18n_entity.QsInboundOrderLine.fields.ro.label=ルート組織
i18n_entity.QsInboundOrderLine.fields.version.label=バージョン
i18n_entity.QsInboundOrderLine.group=クイックストア
i18n_entity.QsInboundOrderLine.label=QS入庫単行
i18n_entity.QsInboundOrderLine.listCard.qty.prefix=数量
i18n_entity.QsMoveBinOrder.fields.actualToBin.label=実際のエンドポイントライブラリのビット
i18n_entity.QsMoveBinOrder.fields.btBzKind.inlineOptionBill.items.Normal.label=デフォルト
i18n_entity.QsMoveBinOrder.fields.btBzKind.label=ビジネスタイプ
i18n_entity.QsMoveBinOrder.fields.container.label=コンテナ
i18n_entity.QsMoveBinOrder.fields.createdBy.label=作成者
i18n_entity.QsMoveBinOrder.fields.createdOn.label=作成時間
i18n_entity.QsMoveBinOrder.fields.expectToBin.label=エンドポイントライブラリの指定
i18n_entity.QsMoveBinOrder.fields.expectToDistrict.label=指定された終点領域
i18n_entity.QsMoveBinOrder.fields.fromBin.label=出発点のライブラリビット
i18n_entity.QsMoveBinOrder.fields.id.label=ID
i18n_entity.QsMoveBinOrder.fields.modifiedBy.label=最後に人を修正する
i18n_entity.QsMoveBinOrder.fields.modifiedOn.label=最後の変更時間
i18n_entity.QsMoveBinOrder.fields.oldInvLines.label=移動時のコンテナ内の在庫内訳
i18n_entity.QsMoveBinOrder.fields.remark.label=説明する
i18n_entity.QsMoveBinOrder.fields.version.label=改訂版
i18n_entity.QsMoveBinOrder.group=クイックストア
i18n_entity.QsMoveBinOrder.kinds.kinds.Normal.label=デフォルト
i18n_entity.QsMoveBinOrder.label=QS移行リスト
i18n_entity.QsMoveBinOrder.listCard.container.prefix=コンテナ
i18n_entity.QsMoveBinOrder.listCard.fromBin.suffix=->
i18n_entity.QsOldInvLine.fields.amount.label=金額
i18n_entity.QsOldInvLine.fields.btLineNo.label=行番号
i18n_entity.QsOldInvLine.fields.btMaterial.label=マテリアル
i18n_entity.QsOldInvLine.fields.btMaterialCategory.label=材料の分類番号
i18n_entity.QsOldInvLine.fields.btMaterialCategoryName.label=マテリアルカテゴリ名称
i18n_entity.QsOldInvLine.fields.btMaterialImage.label=マテリアル画像
i18n_entity.QsOldInvLine.fields.btMaterialModel.label=マテリアルモデル
i18n_entity.QsOldInvLine.fields.btMaterialName.label=マテリアル名
i18n_entity.QsOldInvLine.fields.btMaterialSpec.label=材料仕様
i18n_entity.QsOldInvLine.fields.btParentId.label=所属注文
i18n_entity.QsOldInvLine.fields.createdBy.label=作成者
i18n_entity.QsOldInvLine.fields.createdOn.label=作成時間
i18n_entity.QsOldInvLine.fields.expDate.label=有効期間
i18n_entity.QsOldInvLine.fields.id.label=ID
i18n_entity.QsOldInvLine.fields.inboundOn.label=入庫時間
i18n_entity.QsOldInvLine.fields.leafContainer.label=最も内側のコンテナ
i18n_entity.QsOldInvLine.fields.lotNo.label=バッチ番号
i18n_entity.QsOldInvLine.fields.matLotNo.label=バッチ
i18n_entity.QsOldInvLine.fields.matSerialNo.label=シリアル番号
i18n_entity.QsOldInvLine.fields.mfgDate.label=製造日
i18n_entity.QsOldInvLine.fields.modifiedBy.label=最後に人を修正する
i18n_entity.QsOldInvLine.fields.modifiedOn.label=最後の変更時間
i18n_entity.QsOldInvLine.fields.owner.label=荷主
i18n_entity.QsOldInvLine.fields.price.label=単価
i18n_entity.QsOldInvLine.fields.qty.label=数量
i18n_entity.QsOldInvLine.fields.refInvId.label=関連する在庫明細ID
i18n_entity.QsOldInvLine.fields.subContainerId.label=グリッド
i18n_entity.QsOldInvLine.fields.topContainer.label=最も外側のコンテナ
i18n_entity.QsOldInvLine.fields.vendor.label=サプライヤー
i18n_entity.QsOldInvLine.fields.version.label=改訂版
i18n_entity.QsOldInvLine.group=クイックストア
i18n_entity.QsOldInvLine.label=QS既存在庫の内訳
i18n_entity.QsOldInvLine.listStats.items[0].label=処理中（ロック）
i18n_entity.QsOutboundOrder.fields.btLines.label=単一行
i18n_entity.QsOutboundOrder.fields.btOrderKind.inlineOptionBill.items.Default.label=デフォルト
i18n_entity.QsOutboundOrder.fields.btOrderKind.label=タイプ
i18n_entity.QsOutboundOrder.fields.btOrderState.inlineOptionBill.items.Committed.label=提出済み
i18n_entity.QsOutboundOrder.fields.btOrderState.inlineOptionBill.items.Init.label=未提出
i18n_entity.QsOutboundOrder.fields.btOrderState.label=ビジネスの状態
i18n_entity.QsOutboundOrder.fields.btOrderStateReason.label=ステータスの説明
i18n_entity.QsOutboundOrder.fields.createdBy.label=作成者
i18n_entity.QsOutboundOrder.fields.createdOn.label=作成時間
i18n_entity.QsOutboundOrder.fields.id.label=注文番号
i18n_entity.QsOutboundOrder.fields.invAssignedAll.label=在庫配分完了
i18n_entity.QsOutboundOrder.fields.modifiedBy.label=最後に人を修正する
i18n_entity.QsOutboundOrder.fields.modifiedOn.label=最後の変更時間
i18n_entity.QsOutboundOrder.fields.priority.label=優先度
i18n_entity.QsOutboundOrder.fields.remark.label=備考
i18n_entity.QsOutboundOrder.fields.ro.label=ルート組織
i18n_entity.QsOutboundOrder.fields.typePriority.label=型の優先順位
i18n_entity.QsOutboundOrder.fields.version.label=バージョン
i18n_entity.QsOutboundOrder.group=クイックストア
i18n_entity.QsOutboundOrder.label=QS出荷リスト
i18n_entity.QsOutboundOrder.states.states.Committed.label=提出済み
i18n_entity.QsOutboundOrder.states.states.Init.label=未提出
i18n_entity.QsOutboundOrder.states.states.Init.nextStates.Committed.buttonLabel=提出する
i18n_entity.QsOutboundOrderLine.fields.btLineNo.label=行番号
i18n_entity.QsOutboundOrderLine.fields.btMaterial.label=マテリアル
i18n_entity.QsOutboundOrderLine.fields.btMaterialCategory.label=材料の分類番号
i18n_entity.QsOutboundOrderLine.fields.btMaterialCategoryName.label=マテリアルカテゴリ名称
i18n_entity.QsOutboundOrderLine.fields.btMaterialId.label=マテリアル番号
i18n_entity.QsOutboundOrderLine.fields.btMaterialImage.label=マテリアル画像
i18n_entity.QsOutboundOrderLine.fields.btMaterialModel.label=マテリアルモデル
i18n_entity.QsOutboundOrderLine.fields.btMaterialName.label=マテリアル名
i18n_entity.QsOutboundOrderLine.fields.btMaterialSpec.label=材料仕様
i18n_entity.QsOutboundOrderLine.fields.btParentId.label=出荷リストに属する
i18n_entity.QsOutboundOrderLine.fields.createdBy.label=作成者
i18n_entity.QsOutboundOrderLine.fields.createdOn.label=作成時間
i18n_entity.QsOutboundOrderLine.fields.id.label=ID
i18n_entity.QsOutboundOrderLine.fields.invAssignedQty.label=割り当てられた在庫の量
i18n_entity.QsOutboundOrderLine.fields.lotNo.label=バッチ番号
i18n_entity.QsOutboundOrderLine.fields.modifiedBy.label=最後に人を修正する
i18n_entity.QsOutboundOrderLine.fields.modifiedOn.label=最後の変更時間
i18n_entity.QsOutboundOrderLine.fields.priority.label=優先度
i18n_entity.QsOutboundOrderLine.fields.qty.label=出庫数
i18n_entity.QsOutboundOrderLine.fields.ro.label=ルート組織
i18n_entity.QsOutboundOrderLine.fields.version.label=バージョン
i18n_entity.QsOutboundOrderLine.group=クイックストア
i18n_entity.QsOutboundOrderLine.label=QS出庫単行
i18n_entity.QsOutboundOrderLine.listCard.qty.prefix=出庫数
i18n_entity.QsPickOrder.fields.btLines.label=単一行
i18n_entity.QsPickOrder.fields.btOrderKind.inlineOptionBill.items.NormalTask.label=ロードタスク
i18n_entity.QsPickOrder.fields.btOrderKind.label=タイプ
i18n_entity.QsPickOrder.fields.btOrderState.inlineOptionBill.items.Done.label=ピッキング完了
i18n_entity.QsPickOrder.fields.btOrderState.inlineOptionBill.items.Todo.label=ピッキング待ち
i18n_entity.QsPickOrder.fields.btOrderState.label=ビジネスの状態
i18n_entity.QsPickOrder.fields.btOrderStateReason.label=ステータスの説明
i18n_entity.QsPickOrder.fields.container.label=コンテナ
i18n_entity.QsPickOrder.fields.createdBy.label=作成者
i18n_entity.QsPickOrder.fields.createdOn.label=作成時間
i18n_entity.QsPickOrder.fields.id.label=注文番号
i18n_entity.QsPickOrder.fields.modifiedBy.label=最後に人を修正する
i18n_entity.QsPickOrder.fields.modifiedOn.label=最後の変更時間
i18n_entity.QsPickOrder.fields.targetBin.label=ターゲットライブラリのビット
i18n_entity.QsPickOrder.fields.version.label=改訂版
i18n_entity.QsPickOrder.group=クイックストア
i18n_entity.QsPickOrder.label=QSピッキングリスト
i18n_entity.QsPickOrder.listCard.container.prefix=コンテナ
i18n_entity.QsPickOrder.states.states.Done.label=ピッキング完了
i18n_entity.QsPickOrder.states.states.Todo.label=ピッキング待ち
i18n_entity.QsPickOrder.states.states.Todo.nextStates.Done.buttonLabel=完了する
i18n_entity.QsPickOrderLine.fields.btLineNo.label=行番号
i18n_entity.QsPickOrderLine.fields.btMaterial.label=マテリアル
i18n_entity.QsPickOrderLine.fields.btMaterialCategory.label=材料の分類番号
i18n_entity.QsPickOrderLine.fields.btMaterialCategoryName.label=マテリアルカテゴリ名称
i18n_entity.QsPickOrderLine.fields.btMaterialId.label=マテリアル番号
i18n_entity.QsPickOrderLine.fields.btMaterialImage.label=マテリアル画像
i18n_entity.QsPickOrderLine.fields.btMaterialModel.label=マテリアルモデル
i18n_entity.QsPickOrderLine.fields.btMaterialName.label=マテリアル名
i18n_entity.QsPickOrderLine.fields.btMaterialSpec.label=材料仕様
i18n_entity.QsPickOrderLine.fields.btParentId.label=所属書類
i18n_entity.QsPickOrderLine.fields.createdBy.label=作成者
i18n_entity.QsPickOrderLine.fields.createdOn.label=作成時間
i18n_entity.QsPickOrderLine.fields.id.label=ID
i18n_entity.QsPickOrderLine.fields.invLayoutId.label=在庫明細ID
i18n_entity.QsPickOrderLine.fields.lotNo.label=バッチ番号
i18n_entity.QsPickOrderLine.fields.modifiedBy.label=最後に人を修正する
i18n_entity.QsPickOrderLine.fields.modifiedOn.label=最後の変更時間
i18n_entity.QsPickOrderLine.fields.outboundLineId.label=出庫単行ID
i18n_entity.QsPickOrderLine.fields.outboundLineNo.label=出庫単行行番号
i18n_entity.QsPickOrderLine.fields.outboundOrderId.label=出荷番号
i18n_entity.QsPickOrderLine.fields.planQty.label=数を選び出すことを期待する
i18n_entity.QsPickOrderLine.fields.qty.label=実際のピッキング数
i18n_entity.QsPickOrderLine.fields.subContainerId.label=グリッド番号
i18n_entity.QsPickOrderLine.fields.topContainer.label=コンテナ
i18n_entity.QsPickOrderLine.fields.version.label=改訂版
i18n_entity.QsPickOrderLine.group=クイックストア
i18n_entity.QsPickOrderLine.label=QSピッキングライン
i18n_entity.QsPickOrderLine.listCard.planQty.prefix=期待される数量
i18n_entity.QsPickOrderLine.listCard.qty.prefix=実際の数量
i18n_entity.QsPutOnContainerOrder.fields.bin.label=リポジトリを上架する
i18n_entity.QsPutOnContainerOrder.fields.container.label=コンテナ
i18n_entity.QsPutOnContainerOrder.fields.createdBy.label=作成者
i18n_entity.QsPutOnContainerOrder.fields.createdOn.label=作成時間
i18n_entity.QsPutOnContainerOrder.fields.id.label=ID
i18n_entity.QsPutOnContainerOrder.fields.modifiedBy.label=最後に人を修正する
i18n_entity.QsPutOnContainerOrder.fields.modifiedOn.label=最後の変更時間
i18n_entity.QsPutOnContainerOrder.fields.oldInvLines.label=出品時のコンテナ内の在庫明細
i18n_entity.QsPutOnContainerOrder.fields.version.label=改訂版
i18n_entity.QsPutOnContainerOrder.group=クイックストア
i18n_entity.QsPutOnContainerOrder.label=QSの手動リスト
i18n_entity.QsPutOnContainerOrder.listCard.bin.prefix=リポジトリを上架する
i18n_entity.QsPutOrder.fields.bin.label=リポジトリを上架する
i18n_entity.QsPutOrder.fields.btLines.label=単一行
i18n_entity.QsPutOrder.fields.btOrderKind.inlineOptionBill.items.NormalTask.label=ロードタスク
i18n_entity.QsPutOrder.fields.btOrderKind.label=タイプ
i18n_entity.QsPutOrder.fields.btOrderState.inlineOptionBill.items.Done.label=積み込み完了
i18n_entity.QsPutOrder.fields.btOrderState.inlineOptionBill.items.Todo.label=積み込み待ち
i18n_entity.QsPutOrder.fields.btOrderState.label=ビジネスの状態
i18n_entity.QsPutOrder.fields.btOrderStateReason.label=ステータスの説明
i18n_entity.QsPutOrder.fields.container.label=コンテナ
i18n_entity.QsPutOrder.fields.createdBy.label=作成者
i18n_entity.QsPutOrder.fields.createdOn.label=作成時間
i18n_entity.QsPutOrder.fields.id.label=注文番号
i18n_entity.QsPutOrder.fields.modifiedBy.label=最後に人を修正する
i18n_entity.QsPutOrder.fields.modifiedOn.label=最後の変更時間
i18n_entity.QsPutOrder.fields.oldInvLines.label=コンテナ内の元の在庫の詳細
i18n_entity.QsPutOrder.fields.targetBin.label=ターゲットライブラリのビット
i18n_entity.QsPutOrder.fields.version.label=改訂版
i18n_entity.QsPutOrder.group=クイックストア
i18n_entity.QsPutOrder.label=QSロードリスト
i18n_entity.QsPutOrder.listCard.bin.prefix=リポジトリを上架する
i18n_entity.QsPutOrder.listCard.container.prefix=コンテナ
i18n_entity.QsPutOrder.states.states.Done.label=積み込み完了
i18n_entity.QsPutOrder.states.states.Todo.label=積み込み待ち
i18n_entity.QsPutOrder.states.states.Todo.nextStates.Done.buttonLabel=完了する
i18n_entity.QsPutOrderLine.fields.btLineNo.label=行番号
i18n_entity.QsPutOrderLine.fields.btMaterial.label=マテリアル
i18n_entity.QsPutOrderLine.fields.btMaterialCategory.label=材料の分類番号
i18n_entity.QsPutOrderLine.fields.btMaterialCategoryName.label=マテリアルカテゴリ名称
i18n_entity.QsPutOrderLine.fields.btMaterialId.label=マテリアル番号
i18n_entity.QsPutOrderLine.fields.btMaterialImage.label=マテリアル画像
i18n_entity.QsPutOrderLine.fields.btMaterialModel.label=マテリアルモデル
i18n_entity.QsPutOrderLine.fields.btMaterialName.label=マテリアル名
i18n_entity.QsPutOrderLine.fields.btMaterialSpec.label=材料仕様
i18n_entity.QsPutOrderLine.fields.btParentId.label=所属書類
i18n_entity.QsPutOrderLine.fields.createdBy.label=作成者
i18n_entity.QsPutOrderLine.fields.createdOn.label=作成時間
i18n_entity.QsPutOrderLine.fields.id.label=ID
i18n_entity.QsPutOrderLine.fields.lotNo.label=バッチ番号
i18n_entity.QsPutOrderLine.fields.modifiedBy.label=最後に人を修正する
i18n_entity.QsPutOrderLine.fields.modifiedOn.label=最後の変更時間
i18n_entity.QsPutOrderLine.fields.planQty.label=期待されるロード数
i18n_entity.QsPutOrderLine.fields.qty.label=実際のロード数
i18n_entity.QsPutOrderLine.fields.subContainerId.label=グリッド番号
i18n_entity.QsPutOrderLine.fields.version.label=改訂版
i18n_entity.QsPutOrderLine.group=クイックストア
i18n_entity.QsPutOrderLine.label=QSロード単行
i18n_entity.QsPutOrderLine.listCard.planQty.prefix=積み込みを期待する
i18n_entity.QsPutOrderLine.listCard.qty.prefix=実際の積み込み
i18n_entity.QsTakeOffContainerOrder.fields.bin.label=在庫を削除する
i18n_entity.QsTakeOffContainerOrder.fields.container.label=コンテナ
i18n_entity.QsTakeOffContainerOrder.fields.createdBy.label=作成者
i18n_entity.QsTakeOffContainerOrder.fields.createdOn.label=作成時間
i18n_entity.QsTakeOffContainerOrder.fields.id.label=ID
i18n_entity.QsTakeOffContainerOrder.fields.keepInv.label=在庫を保持する
i18n_entity.QsTakeOffContainerOrder.fields.modifiedBy.label=最後に人を修正する
i18n_entity.QsTakeOffContainerOrder.fields.modifiedOn.label=最後の変更時間
i18n_entity.QsTakeOffContainerOrder.fields.oldInvLines.label=下架時のコンテナ内の在庫明細
i18n_entity.QsTakeOffContainerOrder.fields.version.label=改訂版
i18n_entity.QsTakeOffContainerOrder.group=クイックストア
i18n_entity.QsTakeOffContainerOrder.label=QS人工下架注文
i18n_entity.QsTakeOffContainerOrder.listCard.bin.prefix=在庫を削除する
i18n_entity.RaSingleBatteryRecord.fields.batteryLevel.label=バッテリー残量
i18n_entity.RaSingleBatteryRecord.fields.batteryTemp.label=バッテリー温度
i18n_entity.RaSingleBatteryRecord.fields.charging.label=充電するかどうか
i18n_entity.RaSingleBatteryRecord.fields.createdBy.label=作成者
i18n_entity.RaSingleBatteryRecord.fields.createdOn.label=作成時間
i18n_entity.RaSingleBatteryRecord.fields.current.label=電流
i18n_entity.RaSingleBatteryRecord.fields.id.label=ナンバリング
i18n_entity.RaSingleBatteryRecord.fields.modifiedBy.label=最後に人を修正する
i18n_entity.RaSingleBatteryRecord.fields.modifiedOn.label=最後の変更時間
i18n_entity.RaSingleBatteryRecord.fields.robotName.label=ロボット名
i18n_entity.RaSingleBatteryRecord.fields.version.label=改訂版
i18n_entity.RaSingleBatteryRecord.fields.voltage.label=電圧
i18n_entity.RaSingleBatteryRecord.group=RaSingle
i18n_entity.RaSingleBatteryRecord.label=ロボットバッテリー記録
i18n_entity.RaSingleNavigateRecord.fields.cost.label=かかる時間
i18n_entity.RaSingleNavigateRecord.fields.createdBy.label=作成者
i18n_entity.RaSingleNavigateRecord.fields.createdOn.label=作成時間
i18n_entity.RaSingleNavigateRecord.fields.id.label=ナンバリング
i18n_entity.RaSingleNavigateRecord.fields.modifiedBy.label=最後に人を修正する
i18n_entity.RaSingleNavigateRecord.fields.modifiedOn.label=最後の変更時間
i18n_entity.RaSingleNavigateRecord.fields.robotName.label=ロボット名
i18n_entity.RaSingleNavigateRecord.fields.targetId.label=ターゲットサイト
i18n_entity.RaSingleNavigateRecord.fields.targetPoint.label=ターゲット座標ポイント
i18n_entity.RaSingleNavigateRecord.fields.taskId.label=ナビゲーションID
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.0.label=どれも
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.1.label=待つ
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.2.label=ランニング
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.3.label=中断されました
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.4.label=完了しました
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.5.label=失敗しました
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.inlineOptionBill.items.6.label=キャンセル
i18n_entity.RaSingleNavigateRecord.fields.taskStatus.label=ナビゲーションの状態
i18n_entity.RaSingleNavigateRecord.fields.taskType.inlineOptionBill.items.0.label=ナビゲーションなし
i18n_entity.RaSingleNavigateRecord.fields.taskType.inlineOptionBill.items.1.label=任意のポイントへの自由なナビゲーション
i18n_entity.RaSingleNavigateRecord.fields.taskType.inlineOptionBill.items.100.label=その他
i18n_entity.RaSingleNavigateRecord.fields.taskType.inlineOptionBill.items.2.label=サイトへの自由なナビゲーション
i18n_entity.RaSingleNavigateRecord.fields.taskType.inlineOptionBill.items.3.label=サイトへのパスナビゲーション
i18n_entity.RaSingleNavigateRecord.fields.taskType.inlineOptionBill.items.7.label=並進回転
i18n_entity.RaSingleNavigateRecord.fields.taskType.label=ナビゲーションタイプ
i18n_entity.RaSingleNavigateRecord.fields.version.label=改訂版
i18n_entity.RaSingleNavigateRecord.group=RaSingle
i18n_entity.RaSingleNavigateRecord.label=ロボットナビゲーション記録
i18n_entity.ResourceLock.fields.createdBy.label=作成者
i18n_entity.ResourceLock.fields.createdOn.label=作成時間
i18n_entity.ResourceLock.fields.id.label=ID
i18n_entity.ResourceLock.fields.lockOn.label=ロック時間
i18n_entity.ResourceLock.fields.locked.label=ロックする
i18n_entity.ResourceLock.fields.modifiedBy.label=最後に人を修正する
i18n_entity.ResourceLock.fields.modifiedOn.label=時間を変更する
i18n_entity.ResourceLock.fields.owner.label=ロッカー
i18n_entity.ResourceLock.fields.reason.label=ロックの理由
i18n_entity.ResourceLock.fields.resId.label=リソースID
i18n_entity.ResourceLock.fields.resType.label=リソースの種類
i18n_entity.ResourceLock.fields.version.label=改訂版
i18n_entity.ResourceLock.group=コア
i18n_entity.ResourceLock.label=リソースロック
i18n_entity.RobotConnectedPoint.fields.bin.label=関連するライブラリビット
i18n_entity.RobotConnectedPoint.fields.channel.label=路地にいる
i18n_entity.RobotConnectedPoint.fields.createdBy.label=作成者
i18n_entity.RobotConnectedPoint.fields.createdOn.label=作成時間
i18n_entity.RobotConnectedPoint.fields.id.label=ID
i18n_entity.RobotConnectedPoint.fields.modifiedBy.label=最後に人を修正する
i18n_entity.RobotConnectedPoint.fields.modifiedOn.label=最後の変更時間
i18n_entity.RobotConnectedPoint.fields.remark.label=備考
i18n_entity.RobotConnectedPoint.fields.version.label=改訂版
i18n_entity.RobotConnectedPoint.fields.x.label=位置x
i18n_entity.RobotConnectedPoint.fields.y.label=位置y
i18n_entity.RobotConnectedPoint.group=WCS
i18n_entity.RobotConnectedPoint.label=ロボット通信ポイント
i18n_entity.RobotConnectedPoint.listCard.bin.prefix=関連するライブラリビット
i18n_entity.RobotConnectedPoint.listCard.x.prefix=位置x
i18n_entity.RobotConnectedPoint.listCard.y.prefix=位置y
i18n_entity.RobotPropChangeTimeline.fields.createdBy.label=作成者
i18n_entity.RobotPropChangeTimeline.fields.createdOn.label=作成時間
i18n_entity.RobotPropChangeTimeline.fields.delta.label=変化の量
i18n_entity.RobotPropChangeTimeline.fields.duration.label=長さ(ミリ秒)
i18n_entity.RobotPropChangeTimeline.fields.finishedOn.label=終了時間
i18n_entity.RobotPropChangeTimeline.fields.id.label=ID
i18n_entity.RobotPropChangeTimeline.fields.modifiedBy.label=最後に人を修正する
i18n_entity.RobotPropChangeTimeline.fields.modifiedOn.label=最後の変更時間
i18n_entity.RobotPropChangeTimeline.fields.newValue.label=新しい値
i18n_entity.RobotPropChangeTimeline.fields.oldValue.label=古い値
i18n_entity.RobotPropChangeTimeline.fields.percentageBase.label=比率ベース
i18n_entity.RobotPropChangeTimeline.fields.robotName.label=ロボット
i18n_entity.RobotPropChangeTimeline.fields.startedOn.label=開始時間
i18n_entity.RobotPropChangeTimeline.fields.type.label=状態変化の種類
i18n_entity.RobotPropChangeTimeline.fields.version.label=改訂版
i18n_entity.RobotPropChangeTimeline.group=統計データ
i18n_entity.RobotPropChangeTimeline.label=ロボット状態変化表
i18n_entity.RobustScriptExecutor.fields.args.label=パラメータ
i18n_entity.RobustScriptExecutor.fields.createdBy.label=作成者
i18n_entity.RobustScriptExecutor.fields.createdOn.label=作成時間
i18n_entity.RobustScriptExecutor.fields.description.label=説明する
i18n_entity.RobustScriptExecutor.fields.fault.label=故障
i18n_entity.RobustScriptExecutor.fields.faultMsg.label=障害情報
i18n_entity.RobustScriptExecutor.fields.funcName.label=方法
i18n_entity.RobustScriptExecutor.fields.id.label=ID
i18n_entity.RobustScriptExecutor.fields.modifiedBy.label=最後に人を修正する
i18n_entity.RobustScriptExecutor.fields.modifiedOn.label=最後の変更時間
i18n_entity.RobustScriptExecutor.fields.version.label=改訂版
i18n_entity.RobustScriptExecutor.group=コア
i18n_entity.RobustScriptExecutor.label=バックエンドタスク
i18n_entity.RobustScriptExecutor.pagesButtons.ListMain.buttons[0].label=一括編集
i18n_entity.RobustScriptExecutor.pagesButtons.ListMain.buttons[1].label=削除する
i18n_entity.RobustScriptExecutor.pagesButtons.ListMain.buttons[2].label=復元する
i18n_entity.ScriptRunOnce.fields.createdBy.label=作成者
i18n_entity.ScriptRunOnce.fields.createdOn.label=作成時間
i18n_entity.ScriptRunOnce.fields.id.label=ID
i18n_entity.ScriptRunOnce.fields.modifiedBy.label=最後に人を修正する
i18n_entity.ScriptRunOnce.fields.modifiedOn.label=時間を変更する
i18n_entity.ScriptRunOnce.fields.output.label=出力する
i18n_entity.ScriptRunOnce.fields.version.label=改訂版
i18n_entity.ScriptRunOnce.group=コア
i18n_entity.ScriptRunOnce.label=スクリプトを1回実行します
i18n_entity.SimpleTransportOrder.fields.createdBy.label=作成者
i18n_entity.SimpleTransportOrder.fields.createdOn.label=作成時間
i18n_entity.SimpleTransportOrder.fields.currentMove.label=現在のステップ
i18n_entity.SimpleTransportOrder.fields.doneOn.label=終了時間
i18n_entity.SimpleTransportOrder.fields.errorMsg.label=エラーメッセージ
i18n_entity.SimpleTransportOrder.fields.id.label=ID
i18n_entity.SimpleTransportOrder.fields.modifiedBy.label=最後に人を修正する
i18n_entity.SimpleTransportOrder.fields.modifiedOn.label=最後の変更時間
i18n_entity.SimpleTransportOrder.fields.moves.label=アクションリスト
i18n_entity.SimpleTransportOrder.fields.robotName.label=ロボット
i18n_entity.SimpleTransportOrder.fields.seer3066.label=パスナビゲーションの指定
i18n_entity.SimpleTransportOrder.fields.status.inlineOptionBill.items.Cancelled.label=キャンセル済み
i18n_entity.SimpleTransportOrder.fields.status.inlineOptionBill.items.Created.label=作成済み
i18n_entity.SimpleTransportOrder.fields.status.inlineOptionBill.items.Done.label=完了しました
i18n_entity.SimpleTransportOrder.fields.status.inlineOptionBill.items.Failed.label=失敗する
i18n_entity.SimpleTransportOrder.fields.status.label=状態
i18n_entity.SimpleTransportOrder.fields.vendor.label=メーカー
i18n_entity.SimpleTransportOrder.fields.version.label=改訂版
i18n_entity.SimpleTransportOrder.group=GW
i18n_entity.SimpleTransportOrder.label=自転車の運送状
i18n_entity.SimpleTransportOrder.listCard.robotName.prefix=ロボット
i18n_entity.SimpleTransportOrder.listCard.vendor.prefix=メーカー
i18n_entity.SimpleTransportOrder.listStats.items[0].label=実行中
i18n_entity.SimpleTransportOrder.listStats.items[1].label=失敗する
i18n_entity.SocNode.fields.attention.label=注意する
i18n_entity.SocNode.fields.createdBy.label=作成者
i18n_entity.SocNode.fields.createdOn.label=作成時間
i18n_entity.SocNode.fields.description.label=説明する
i18n_entity.SocNode.fields.id.label=ID
i18n_entity.SocNode.fields.label.label=タグ
i18n_entity.SocNode.fields.modifiedBy.label=最後に人を修正する
i18n_entity.SocNode.fields.modifiedOn.label=最後の変更時間
i18n_entity.SocNode.fields.modifiedReason.label=更新の理由
i18n_entity.SocNode.fields.modifiedTimestamp.label=更新時間
i18n_entity.SocNode.fields.value.label=値
i18n_entity.SocNode.fields.version.label=改訂版
i18n_entity.SocNode.group=コア
i18n_entity.SocNode.label=監視ノード
i18n_entity.SocNode.listCard.modifiedTimestamp.prefix=更新時間:
i18n_entity.StatsTimelineValueReport.fields.createdBy.label=作成者
i18n_entity.StatsTimelineValueReport.fields.createdOn.label=作成時間
i18n_entity.StatsTimelineValueReport.fields.denominator.label=分母
i18n_entity.StatsTimelineValueReport.fields.finishedOn.label=終了時間
i18n_entity.StatsTimelineValueReport.fields.id.label=ID
i18n_entity.StatsTimelineValueReport.fields.modifiedBy.label=最後に人を修正する
i18n_entity.StatsTimelineValueReport.fields.modifiedOn.label=最後の変更時間
i18n_entity.StatsTimelineValueReport.fields.molecular.label=分子
i18n_entity.StatsTimelineValueReport.fields.period.label=サイクル
i18n_entity.StatsTimelineValueReport.fields.periodType.label=サイクルタイプ
i18n_entity.StatsTimelineValueReport.fields.startedOn.label=開始時間
i18n_entity.StatsTimelineValueReport.fields.subject.label=テーマ
i18n_entity.StatsTimelineValueReport.fields.target.label=キーワード
i18n_entity.StatsTimelineValueReport.fields.value.label=値
i18n_entity.StatsTimelineValueReport.fields.version.label=改訂版
i18n_entity.StatsTimelineValueReport.group=統計データ
i18n_entity.StatsTimelineValueReport.label=時系列数値レポート
i18n_entity.SystemKeyEvent.fields.content.label=コンテンツ
i18n_entity.SystemKeyEvent.fields.createdBy.label=作成者
i18n_entity.SystemKeyEvent.fields.createdOn.label=作成時間
i18n_entity.SystemKeyEvent.fields.group.label=モジュール
i18n_entity.SystemKeyEvent.fields.id.label=ID
i18n_entity.SystemKeyEvent.fields.level.inlineOptionBill.items.Error.label=エラー
i18n_entity.SystemKeyEvent.fields.level.inlineOptionBill.items.Info.label=普通
i18n_entity.SystemKeyEvent.fields.level.inlineOptionBill.items.Warning.label=警告する
i18n_entity.SystemKeyEvent.fields.level.label=レベル
i18n_entity.SystemKeyEvent.fields.modifiedBy.label=最後に人を修正する
i18n_entity.SystemKeyEvent.fields.modifiedOn.label=最後の変更時間
i18n_entity.SystemKeyEvent.fields.relatedUser.label=関連するユーザー
i18n_entity.SystemKeyEvent.fields.title.label=タイトル
i18n_entity.SystemKeyEvent.fields.version.label=改訂版
i18n_entity.SystemKeyEvent.group=コア
i18n_entity.SystemKeyEvent.label=システムクリティカルイベント
i18n_entity.TransportOrder.fields.actualRobotName.label=実行ロボット
i18n_entity.TransportOrder.fields.containerDir.label=コンテナの方向
i18n_entity.TransportOrder.fields.containerId.label=コンテナ番号
i18n_entity.TransportOrder.fields.containerTypeName.label=コンテナータイプ名
i18n_entity.TransportOrder.fields.createdBy.label=作成者
i18n_entity.TransportOrder.fields.createdOn.label=作成時間
i18n_entity.TransportOrder.fields.currentStepIndex.label=現在のステップ
i18n_entity.TransportOrder.fields.dispatchCost.label=割り当てコスト
i18n_entity.TransportOrder.fields.doneOn.label=完了の瞬間
i18n_entity.TransportOrder.fields.doneStepIndex.label=完了した手順
i18n_entity.TransportOrder.fields.executingTime.label=実行時間（秒）
i18n_entity.TransportOrder.fields.expectedRobotGroups.label=指定されたロボットグループ
i18n_entity.TransportOrder.fields.expectedRobotNames.label=指定ロボット
i18n_entity.TransportOrder.fields.externalId.label=外部注文番号
i18n_entity.TransportOrder.fields.failureNum.label=障害の数
i18n_entity.TransportOrder.fields.fault.label=故障
i18n_entity.TransportOrder.fields.fault.view.trueText=故障
i18n_entity.TransportOrder.fields.faultDuration.label=障害時間
i18n_entity.TransportOrder.fields.faultReason.label=障害の原因
i18n_entity.TransportOrder.fields.id.label=ID
i18n_entity.TransportOrder.fields.keyLocations.label=キーポイント
i18n_entity.TransportOrder.fields.kind.inlineOptionBill.items.Business.label=ビジネス
i18n_entity.TransportOrder.fields.kind.inlineOptionBill.items.Charging.label=充电
i18n_entity.TransportOrder.fields.kind.inlineOptionBill.items.IdleAvoid.label=推空闲车
i18n_entity.TransportOrder.fields.kind.inlineOptionBill.items.Parking.label=停泊する
i18n_entity.TransportOrder.fields.kind.label=タイプ
i18n_entity.TransportOrder.fields.loadDuration.label=ピックアップ時間（s）
i18n_entity.TransportOrder.fields.loadPoint.label=ピックアップポイント
i18n_entity.TransportOrder.fields.loaded.label=受け取り済み
i18n_entity.TransportOrder.fields.modifiedBy.label=最後に人を修正する
i18n_entity.TransportOrder.fields.modifiedOn.label=時間を変更する
i18n_entity.TransportOrder.fields.oldRobots.label=履歴割り当てボット
i18n_entity.TransportOrder.fields.priority.label=優先度
i18n_entity.TransportOrder.fields.processingTime.label=処理時間（秒）
i18n_entity.TransportOrder.fields.ro.label=RO
i18n_entity.TransportOrder.fields.robotAllocatedOn.label=ロボットの割り当て時刻
i18n_entity.TransportOrder.fields.sceneId.label=シーン
i18n_entity.TransportOrder.fields.sceneName.label=シーン名
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.Allocated.label=割り当て済み
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.Cancelled.label=キャンセル済み
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.Done.label=完了しました
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.Executing.label=実行中
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.Pending.label=実行待ち
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.ToBeAllocated.label=割り当て待ち
i18n_entity.TransportOrder.fields.status.inlineOptionBill.items.Withdrawn.label=撤回されました
i18n_entity.TransportOrder.fields.status.label=状態
i18n_entity.TransportOrder.fields.stepFixed.label=ステップ固定
i18n_entity.TransportOrder.fields.stepNum.label=タスクのステップ数
i18n_entity.TransportOrder.fields.taskBatch.label=タスクバッチ
i18n_entity.TransportOrder.fields.unloadDuration.label=出荷時間（s）
i18n_entity.TransportOrder.fields.unloadPoint.label=出荷ポイント
i18n_entity.TransportOrder.fields.unloaded.label=出荷済み
i18n_entity.TransportOrder.fields.version.label=改訂版
i18n_entity.TransportOrder.fields.waitExecuteDuration.label=実行待ち時間
i18n_entity.TransportOrder.group=フリート
i18n_entity.TransportOrder.label=新しい一般的な運送状
i18n_entity.TransportOrder.listCard.actualRobotName.prefix=実行ロボット
i18n_entity.TransportOrder.listCard.createdOn.prefix=作成する
i18n_entity.TransportOrder.listCard.kind.prefix=タイプ
i18n_entity.TransportOrder.listStats.items[0].label=故障
i18n_entity.TransportOrder.listStats.items[1].label=割り当て待ち
i18n_entity.TransportOrder.listStats.items[2].label=割り当て済み
i18n_entity.TransportOrder.listStats.items[3].label=今日作成
i18n_entity.TransportOrder.listStats.items[4].label=今日完了
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[0].label=追加する
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[1].label=一括編集
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[2].label=削除する
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[3].label=エクスポート
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[4].label=キャンセル
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[5].label=封をする
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[6].label=障害の再試行
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[7].label=優先度の変更
i18n_entity.TransportOrder.pagesButtons.ListMain.buttons[8].label=スナップショットをエクスポート
i18n_entity.TransportStep.fields.createdBy.label=作成者
i18n_entity.TransportStep.fields.createdOn.label=作成時間
i18n_entity.TransportStep.fields.endOn.label=実行時間の終了
i18n_entity.TransportStep.fields.executingTime.label=実行時間（秒）
i18n_entity.TransportStep.fields.forLoad.label=ピックアップポイント
i18n_entity.TransportStep.fields.forUnload.label=出荷ポイント
i18n_entity.TransportStep.fields.id.label=ID
i18n_entity.TransportStep.fields.location.label=ジョブの位置
i18n_entity.TransportStep.fields.modifiedBy.label=最後に人を修正する
i18n_entity.TransportStep.fields.modifiedOn.label=時間を変更する
i18n_entity.TransportStep.fields.nextStepSameOrder.label=次のステップは同じ運送状でなければならない
i18n_entity.TransportStep.fields.orderId.label=運送伝票番号
i18n_entity.TransportStep.fields.processingTime.label=処理時間（秒）
i18n_entity.TransportStep.fields.rbkArgs.label=アクションパラメータ
i18n_entity.TransportStep.fields.startOn.label=実行開始時刻
i18n_entity.TransportStep.fields.status.label=状態
i18n_entity.TransportStep.fields.stepIndex.label=何番目のステップですか?
i18n_entity.TransportStep.fields.version.label=改訂版
i18n_entity.TransportStep.fields.withdrawOrderAllowed.label=再割り当てを許可する
i18n_entity.TransportStep.group=フリート
i18n_entity.TransportStep.label=新しい一般的な運送状の手順
i18n_entity.TransportStep.listCard.locationSite.prefix=ポイント
i18n_entity.TransportStep.listCard.operation.prefix=アクション
i18n_entity.TransportStep.listCard.stepIndex.prefix=第一
i18n_entity.TransportStep.listCard.stepIndex.suffix=ステップ
i18n_entity.UserNotice.fields.actionType.label=アクションタイプ
i18n_entity.UserNotice.fields.content.label=本文
i18n_entity.UserNotice.fields.createdBy.label=作成者
i18n_entity.UserNotice.fields.createdOn.label=作成時間
i18n_entity.UserNotice.fields.entityId.label=エンティティID
i18n_entity.UserNotice.fields.entityName.label=エンティティ名
i18n_entity.UserNotice.fields.hasContent.label=本文あり
i18n_entity.UserNotice.fields.id.label=ID
i18n_entity.UserNotice.fields.modifiedBy.label=最後に人を修正する
i18n_entity.UserNotice.fields.modifiedOn.label=最後の変更時間
i18n_entity.UserNotice.fields.read.label=既読
i18n_entity.UserNotice.fields.readOn.label=既読時間
i18n_entity.UserNotice.fields.title.label=タイトル
i18n_entity.UserNotice.fields.userId.label=ユーザー
i18n_entity.UserNotice.fields.version.label=改訂版
i18n_entity.UserNotice.group=コア
i18n_entity.UserNotice.label=ユーザー通知
i18n_entity.UserOpLog.fields.content.label=操作内容
i18n_entity.UserOpLog.fields.createdBy.label=作成者
i18n_entity.UserOpLog.fields.createdOn.label=作成時間
i18n_entity.UserOpLog.fields.id.label=ID
i18n_entity.UserOpLog.fields.level.inlineOptionBill.items.Danger.label=危険
i18n_entity.UserOpLog.fields.level.inlineOptionBill.items.Normal.label=正常です
i18n_entity.UserOpLog.fields.level.label=レベル
i18n_entity.UserOpLog.fields.modifiedBy.label=最後に人を修正する
i18n_entity.UserOpLog.fields.modifiedOn.label=最後の変更時間
i18n_entity.UserOpLog.fields.operator.label=ユーザー
i18n_entity.UserOpLog.fields.page.label=ページ
i18n_entity.UserOpLog.fields.version.label=改訂版
i18n_entity.UserOpLog.group=コア
i18n_entity.UserOpLog.label=ユーザー操作ログ
i18n_entity.UserRole.fields.createdBy.label=作成者
i18n_entity.UserRole.fields.createdOn.label=作成時間
i18n_entity.UserRole.fields.defaultRole.label=デフォルトロール
i18n_entity.UserRole.fields.id.label=ナンバリング
i18n_entity.UserRole.fields.modifiedBy.label=最後に人を修正する
i18n_entity.UserRole.fields.modifiedOn.label=最後の変更時間
i18n_entity.UserRole.fields.name.label=キャラクター名
i18n_entity.UserRole.fields.pItems.label=権限リスト
i18n_entity.UserRole.fields.ro.label=ルート組織
i18n_entity.UserRole.fields.version.label=バージョン
i18n_entity.UserRole.group=ユーザ
i18n_entity.UserRole.label=ユーザーロール
i18n_entity.UserRole.listCard.createdBy.prefix=作成者
i18n_entity.UserRole.listCard.modifiedOn.prefix=時間を変更する
i18n_entity.WcsMrOrder.fields.actualRobotName.label=実行ロボット
i18n_entity.WcsMrOrder.fields.cancelling.label=キャンセル中
i18n_entity.WcsMrOrder.fields.containerId.label=コンテナ番号
i18n_entity.WcsMrOrder.fields.createdBy.label=作成者
i18n_entity.WcsMrOrder.fields.createdOn.label=作成時間
i18n_entity.WcsMrOrder.fields.currentStepIndex.label=現在のステップ
i18n_entity.WcsMrOrder.fields.dispatchCost.label=割り当てコスト
i18n_entity.WcsMrOrder.fields.doneOn.label=完了の瞬間
i18n_entity.WcsMrOrder.fields.doneStepIndex.label=完了した手順
i18n_entity.WcsMrOrder.fields.executing.label=実行中
i18n_entity.WcsMrOrder.fields.expectedRobotGroups.label=指定されたロボットグループ
i18n_entity.WcsMrOrder.fields.expectedRobotNames.label=指定ロボット
i18n_entity.WcsMrOrder.fields.fault.label=故障
i18n_entity.WcsMrOrder.fields.id.label=ID
i18n_entity.WcsMrOrder.fields.keySites.label=キーポイント
i18n_entity.WcsMrOrder.fields.kind.inlineOptionBill.items.Parking.label=停泊する
i18n_entity.WcsMrOrder.fields.kind.label=タイプ
i18n_entity.WcsMrOrder.fields.loadLocationSite.label=ピックアップポイント
i18n_entity.WcsMrOrder.fields.loaded.label=受け取り済み
i18n_entity.WcsMrOrder.fields.materialId.label=マテリアル番号
i18n_entity.WcsMrOrder.fields.materialKind.label=マテリアルタイプ
i18n_entity.WcsMrOrder.fields.modifiedBy.label=最後に人を修正する
i18n_entity.WcsMrOrder.fields.modifiedOn.label=時間を変更する
i18n_entity.WcsMrOrder.fields.priority.label=優先度
i18n_entity.WcsMrOrder.fields.reqId.label=リクエスト番号
i18n_entity.WcsMrOrder.fields.ro.label=RO
i18n_entity.WcsMrOrder.fields.robotAllocatedOn.label=ロボットの割り当て時刻
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Allocated.label=割り当て済み
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Building.label=ビルド中
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Cancelled.label=キャンセル済み
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Cancelling.label=キャンセル中
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Done.label=完了しました
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Executing.label=実行中
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Pending.label=実行待ち
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.ToBeAllocated.label=割り当て待ち
i18n_entity.WcsMrOrder.fields.status.inlineOptionBill.items.Withdrawn.label=撤回されました
i18n_entity.WcsMrOrder.fields.status.label=状態
i18n_entity.WcsMrOrder.fields.stepFixed.label=ステップ固定
i18n_entity.WcsMrOrder.fields.stepNum.label=タスクのステップ数
i18n_entity.WcsMrOrder.fields.taskBatch.label=タスクバッチ
i18n_entity.WcsMrOrder.fields.unloadLocationSite.label=出荷ポイント
i18n_entity.WcsMrOrder.fields.unloaded.label=出荷済み
i18n_entity.WcsMrOrder.fields.version.label=改訂版
i18n_entity.WcsMrOrder.fields.withdrawn.label=再割り当て
i18n_entity.WcsMrOrder.group=WCS
i18n_entity.WcsMrOrder.label=一般的な運送状
i18n_entity.WcsMrOrder.listCard.actualRobotName.prefix=実行ロボット
i18n_entity.WcsMrOrder.listCard.createdOn.prefix=作成する
i18n_entity.WcsMrOrder.listCard.kind.prefix=タイプ
i18n_entity.WcsMrOrder.listStats.items[0].label=故障
i18n_entity.WcsMrOrder.listStats.items[1].label=割り当て待ち
i18n_entity.WcsMrOrder.listStats.items[2].label=割り当て済み
i18n_entity.WcsMrOrderStep.fields.createdBy.label=作成者
i18n_entity.WcsMrOrderStep.fields.createdOn.label=作成時間
i18n_entity.WcsMrOrderStep.fields.endOn.label=実行時間の終了
i18n_entity.WcsMrOrderStep.fields.forLoad.label=ピックアップポイント
i18n_entity.WcsMrOrderStep.fields.forUnload.label=出荷ポイント
i18n_entity.WcsMrOrderStep.fields.id.label=ID
i18n_entity.WcsMrOrderStep.fields.locationSite.label=アクションポイント
i18n_entity.WcsMrOrderStep.fields.modifiedBy.label=最後に人を修正する
i18n_entity.WcsMrOrderStep.fields.modifiedOn.label=時間を変更する
i18n_entity.WcsMrOrderStep.fields.operation.label=アクション
i18n_entity.WcsMrOrderStep.fields.operationArgs.label=アクションパラメータ
i18n_entity.WcsMrOrderStep.fields.orderId.label=運送伝票番号
i18n_entity.WcsMrOrderStep.fields.startOn.label=実行開始時刻
i18n_entity.WcsMrOrderStep.fields.status.label=状態
i18n_entity.WcsMrOrderStep.fields.stepIndex.label=何番目のステップですか?
i18n_entity.WcsMrOrderStep.fields.version.label=改訂版
i18n_entity.WcsMrOrderStep.group=WCS
i18n_entity.WcsMrOrderStep.label=一般的な運送状の手順
i18n_entity.WcsMrOrderStep.listCard.locationSite.prefix=ポイント
i18n_entity.WcsMrOrderStep.listCard.operation.prefix=アクション
i18n_entity.WcsMrOrderStep.listCard.stepIndex.prefix=第一
i18n_entity.WcsMrOrderStep.listCard.stepIndex.suffix=ステップ
i18n_entity.WcsRobotTaskLog.fields.args.label=パラメータ
i18n_entity.WcsRobotTaskLog.fields.category.label=カテゴリー
i18n_entity.WcsRobotTaskLog.fields.code.label=コード
i18n_entity.WcsRobotTaskLog.fields.createdBy.label=作成者
i18n_entity.WcsRobotTaskLog.fields.createdOn.label=作成時間
i18n_entity.WcsRobotTaskLog.fields.id.label=ID
i18n_entity.WcsRobotTaskLog.fields.level.label=レベル
i18n_entity.WcsRobotTaskLog.fields.modifiedBy.label=最後に人を修正する
i18n_entity.WcsRobotTaskLog.fields.modifiedOn.label=時間を変更する
i18n_entity.WcsRobotTaskLog.fields.robotName.label=ロボット
i18n_entity.WcsRobotTaskLog.fields.taskId.label=タスク番号
i18n_entity.WcsRobotTaskLog.fields.tcId.label=追跡番号
i18n_entity.WcsRobotTaskLog.fields.version.label=改訂版
i18n_entity.WcsRobotTaskLog.group=WCS
i18n_entity.WcsRobotTaskLog.label=ロボットタスクログ
i18n_entity.WcsRobotTaskLog.listCard.category.prefix=カテゴリー
i18n_entity.WcsRobotTaskLog.listCard.code.prefix=コード
i18n_entity.WcsRobotTaskLog.listCard.level.prefix=レベル
i18n_entity.WcsRobotTaskLog.listCard.taskId.prefix=タスク番号