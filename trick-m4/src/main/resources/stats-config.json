[{"id": "fleet-falcon-task-create-hour", "reportSubject": "FalconTaskCreate", "resKeyTarget": "all", "resKeyPeriod": "createdOn", "resKeyValue": "value", "changeEntityName": "FalconTaskRecord", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "All", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "COUNT", "name": "id", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "createdOn", "alias": "createdOn", "statisticDateType": "Hour"}], "statsDateType": "Hour", "converters": {"all": "defaultAll"}, "disabled": false, "statsOrder": 0, "includeAll": false}, {"id": "fleet-falcon-task-create-day", "reportSubject": "FalconTaskCreate", "resKeyTarget": "all", "resKeyPeriod": "createdOn", "resKeyValue": "value", "changeEntityName": "FalconTaskRecord", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "All", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "COUNT", "name": "id", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "createdOn", "alias": "createdOn", "statisticDateType": "Day"}], "statsDateType": "Day", "converters": {"all": "defaultAll"}, "disabled": false, "statsOrder": 0, "includeAll": false}, {"id": "fleet-falcon-task-create-week", "reportSubject": "FalconTaskCreate", "resKeyTarget": "", "resKeyPeriod": "", "resKeyValue": "", "changeEntityName": "StatsTimelineValueReport", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "subject", "field2": null, "value": "FalconTaskCreate", "items": null}, {"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "periodType", "field2": null, "value": "Day", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": null, "name": "value", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "target", "alias": "target"}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Week", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": false}, {"id": "fleet-falcon-task-done-hour", "reportSubject": "FalconTaskDone", "resKeyTarget": "all", "resKeyPeriod": "createdOn", "resKeyValue": "value", "changeEntityName": "FalconTaskRecord", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "status", "field2": null, "value": 160, "items": null}]}, "changeAggFields": [{"function": "COUNT", "name": "id", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "createdOn", "alias": "createdOn", "statisticDateType": "Hour"}], "statsDateType": "Hour", "converters": {"all": "defaultAll"}, "disabled": false, "statsOrder": 0, "includeAll": false}, {"id": "fleet-falcon-task-done-day", "reportSubject": "FalconTaskDone", "resKeyTarget": "all", "resKeyPeriod": "createdOn", "resKeyValue": "value", "changeEntityName": "FalconTaskRecord", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "status", "field2": null, "value": 160, "items": null}]}, "changeAggFields": [{"function": "COUNT", "name": "id", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "createdOn", "alias": "createdOn", "statisticDateType": "Day"}], "statsDateType": "Day", "converters": {"all": "defaultAll"}, "disabled": false, "statsOrder": 0, "includeAll": false}, {"id": "fleet-falcon-task-done-week", "reportSubject": "FalconTaskDone", "resKeyTarget": "", "resKeyPeriod": "", "resKeyValue": "", "changeEntityName": "StatsTimelineValueReport", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "subject", "field2": null, "value": "FalconTaskDone", "items": null}, {"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "periodType", "field2": null, "value": "Day", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": null, "name": "value", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "target", "alias": "target"}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Week", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": false}, {"id": "fleet-falcon-task-error-hour", "reportSubject": "FalconTaskError", "resKeyTarget": "all", "resKeyPeriod": "createdOn", "resKeyValue": "value", "changeEntityName": "FalconTaskRecord", "changeFilter": {"type": "General", "or": false, "not": false, "operator": "In", "field1": "status", "field2": null, "value": [190, 180], "items": null}, "changeAggFields": [{"function": "COUNT", "name": "id", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "createdOn", "alias": "createdOn", "statisticDateType": "Hour"}], "statsDateType": "Hour", "converters": {"all": "defaultAll"}, "disabled": false, "statsOrder": 0, "includeAll": false}, {"id": "fleet-falcon-task-error-day", "reportSubject": "FalconTaskError", "resKeyTarget": "all", "resKeyPeriod": "createdOn", "resKeyValue": "value", "changeEntityName": "FalconTaskRecord", "changeFilter": {"type": "General", "or": false, "not": false, "operator": "In", "field1": "status", "field2": null, "value": [190, 180], "items": null}, "changeAggFields": [{"function": "COUNT", "name": "id", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "createdOn", "alias": "createdOn", "statisticDateType": "Day"}], "statsDateType": "Day", "converters": {"all": "defaultAll"}, "disabled": false, "statsOrder": 0, "includeAll": false}, {"id": "fleet-falcon-task-error-week", "reportSubject": "FalconTaskError", "resKeyTarget": "", "resKeyPeriod": "", "resKeyValue": "", "changeEntityName": "StatsTimelineValueReport", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "subject", "field2": null, "value": "FalconTaskError", "items": null}, {"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "periodType", "field2": null, "value": "Day", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": null, "name": "value", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "target", "alias": "target"}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Week", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": false}, {"id": "fleet-robot-mileage-hour", "reportSubject": "RobotMileage", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "value", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "Mileage", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "SUM", "name": "delta", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Hour"}], "statsDateType": "Hour", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-mileage-day", "reportSubject": "RobotMileage", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "value", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "Mileage", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "SUM", "name": "delta", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Day", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-mileage-week", "reportSubject": "RobotMileage", "resKeyTarget": "", "resKeyPeriod": "", "resKeyValue": "", "changeEntityName": "StatsTimelineValueReport", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "subject", "field2": null, "value": "RobotMileage", "items": null}, {"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "periodType", "field2": null, "value": "Day", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": null, "name": "value", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "target", "alias": "target", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Week", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": false}, {"id": "fleet-robot-charge-times-hour", "reportSubject": "RobotChargeTimes", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "value", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "Charging", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "COUNT", "name": "id", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Hour"}], "statsDateType": "Hour", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-charge-times-day", "reportSubject": "RobotChargeTimes", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "value", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "Charging", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "COUNT", "name": "id", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Day", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-charge-times-week", "reportSubject": "RobotChargeTimes", "resKeyTarget": "", "resKeyPeriod": "", "resKeyValue": "", "changeEntityName": "StatsTimelineValueReport", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "subject", "field2": null, "value": "RobotChargeTimes", "items": null}, {"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "periodType", "field2": null, "value": "Day", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": null, "name": "value", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "target", "alias": "target", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Week", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": false}, {"id": "fleet-robot-charge-duration-hour", "reportSubject": "RobotChargeDuration", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "value", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "Charging", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "SUM", "name": "duration", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Hour"}], "statsDateType": "Hour", "converters": {"value": "millSecondToHour"}, "disabled": false, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-charge-duration-day", "reportSubject": "RobotChargeDuration", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "value", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "Charging", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "SUM", "name": "duration", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Day", "converters": {"value": "millSecondToHour"}, "disabled": false, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-charge-duration-week", "reportSubject": "RobotChargeDuration", "resKeyTarget": "", "resKeyPeriod": "", "resKeyValue": "", "changeEntityName": "StatsTimelineValueReport", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "subject", "field2": null, "value": "RobotChargeDuration", "items": null}, {"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "periodType", "field2": null, "value": "Day", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": null, "name": "value", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "target", "alias": "target", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Week", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": false}, {"id": "fleet-robot-work-times-hour", "reportSubject": "RobotWorkTimes", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "value", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "Working", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "COUNT", "name": "id", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Hour"}], "statsDateType": "Hour", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-work-times-day", "reportSubject": "RobotWorkTimes", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "value", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "Working", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "COUNT", "name": "id", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Day", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-work-times-week", "reportSubject": "RobotWorkTimes", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "value", "changeEntityName": "StatsTimelineValueReport", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "subject", "field2": null, "value": "RobotWorkTimes", "items": null}, {"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "periodType", "field2": null, "value": "Day", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": null, "name": "value", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "target", "alias": "target"}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Week", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-work-duration-hour", "reportSubject": "RobotWorkDuration", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "value", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "Working", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "SUM", "name": "duration", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Hour"}], "statsDateType": "Hour", "converters": {"value": "millSecondToHour"}, "disabled": false, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-work-duration-day", "reportSubject": "RobotWorkDuration", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "value", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "Working", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "SUM", "name": "duration", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Day", "converters": {"value": "millSecondToHour"}, "disabled": false, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-work-duration-week", "reportSubject": "RobotWorkDuration", "resKeyTarget": "", "resKeyPeriod": "", "resKeyValue": "", "changeEntityName": "StatsTimelineValueReport", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "subject", "field2": null, "value": "RobotWorkDuration", "items": null}, {"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "periodType", "field2": null, "value": "Day", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": null, "name": "value", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "target", "alias": "target", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Week", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": false}, {"id": "fleet-robot-idle-duration-hour", "reportSubject": "RobotIdleDuration", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "value", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "Idle", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "SUM", "name": "duration", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Hour"}], "statsDateType": "Hour", "converters": {"value": "millSecondToHour"}, "disabled": false, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-idle-duration-day", "reportSubject": "RobotIdleDuration", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "value", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "Idle", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "SUM", "name": "duration", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Day", "converters": {"value": "millSecondToHour"}, "disabled": false, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-idle-duration-week", "reportSubject": "RobotIdleDuration", "resKeyTarget": "", "resKeyPeriod": "", "resKeyValue": "", "changeEntityName": "StatsTimelineValueReport", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "subject", "field2": null, "value": "RobotIdleDuration", "items": null}, {"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "periodType", "field2": null, "value": "Day", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": null, "name": "value", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "target", "alias": "target", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Week", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": false}, {"id": "fleet-robot-error-times-hour", "reportSubject": "RobotErrorTimes", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "value", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "InError", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "COUNT", "name": "id", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Hour"}], "statsDateType": "Hour", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-error-times-day", "reportSubject": "RobotErrorTimes", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "value", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "InError", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "COUNT", "name": "id", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Day", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-error-times-week", "reportSubject": "RobotErrorTimes", "resKeyTarget": "", "resKeyPeriod": "", "resKeyValue": "", "changeEntityName": "StatsTimelineValueReport", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "subject", "field2": null, "value": "RobotErrorTimes", "items": null}, {"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "periodType", "field2": null, "value": "Day", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": null, "name": "value", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "target", "alias": "target", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Week", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": false}, {"id": "fleet-robot-error-duration-hour", "reportSubject": "RobotErrorDuration", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "value", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "InError", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "SUM", "name": "duration", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Hour"}], "statsDateType": "Hour", "converters": {"value": "millSecondToHour"}, "disabled": false, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-error-duration-day", "reportSubject": "RobotErrorDuration", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "value", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "InError", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "SUM", "name": "duration", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Day", "converters": {"value": "millSecondToHour"}, "disabled": false, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-error-duration-week", "reportSubject": "RobotErrorDuration", "resKeyTarget": "", "resKeyPeriod": "", "resKeyValue": "", "changeEntityName": "StatsTimelineValueReport", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "subject", "field2": null, "value": "RobotErrorDuration", "items": null}, {"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "periodType", "field2": null, "value": "Day", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": null, "name": "value", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "target", "alias": "target", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Week", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": false}, {"id": "fleet-robot-empty-duration-hour", "reportSubject": "RobotEmptyDuration", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "value", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "Empty", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "SUM", "name": "duration", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Hour"}], "statsDateType": "Hour", "converters": {"value": "millSecondToHour"}, "disabled": false, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-empty-duration-day", "reportSubject": "RobotEmptyDuration", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "value", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "Empty", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "SUM", "name": "duration", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Day", "converters": {"value": "millSecondToHour"}, "disabled": false, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-empty-duration-week", "reportSubject": "RobotEmptyDuration", "resKeyTarget": "", "resKeyPeriod": "", "resKeyValue": "", "changeEntityName": "StatsTimelineValueReport", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "subject", "field2": null, "value": "RobotEmptyDuration", "items": null}, {"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "periodType", "field2": null, "value": "Day", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": null, "name": "value", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "target", "alias": "target", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Week", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": false}, {"id": "fleet-robot-load-duration-hour", "reportSubject": "RobotLoadDuration", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "value", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "Loaded", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "SUM", "name": "duration", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Hour"}], "statsDateType": "Hour", "converters": {"value": "millSecondToHour"}, "disabled": false, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-load-duration-day", "reportSubject": "RobotLoadDuration", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "value", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "Loaded", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "SUM", "name": "duration", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Day", "converters": {"value": "millSecondToHour"}, "disabled": false, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-load-duration-week", "reportSubject": "RobotLoadDuration", "resKeyTarget": "", "resKeyPeriod": "", "resKeyValue": "", "changeEntityName": "StatsTimelineValueReport", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "subject", "field2": null, "value": "RobotLoadDuration", "items": null}, {"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "periodType", "field2": null, "value": "Day", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": null, "name": "value", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "target", "alias": "target", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Week", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": false}, {"id": "fleet-robot-online-duration-hour", "reportSubject": "RobotOnlineDuration", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "value", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "Online", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "SUM", "name": "duration", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Hour"}], "statsDateType": "Hour", "converters": {"value": "millSecondToHour"}, "disabled": false, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-online-duration-day", "reportSubject": "RobotOnlineDuration", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "value", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "Online", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "SUM", "name": "duration", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Day", "converters": {"value": "millSecondToHour"}, "disabled": false, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-online-duration-week", "reportSubject": "RobotOnlineDuration", "resKeyTarget": "", "resKeyPeriod": "", "resKeyValue": "", "changeEntityName": "StatsTimelineValueReport", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "subject", "field2": null, "value": "RobotOnlineDuration", "items": null}, {"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "periodType", "field2": null, "value": "Day", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": null, "name": "value", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "target", "alias": "target", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Week", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": false}, {"id": "qs-inbound-order-count-day", "reportSubject": "QsInboundOrderCount", "resKeyTarget": "btBzKind", "resKeyPeriod": "createdOn", "resKeyValue": "value", "changeEntityName": "QsInboundOrder", "changeFilter": {"type": "All", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": null}, "changeAggFields": [], "changeGroupBy": [], "statsDateType": "Day", "converters": {}, "disabled": true, "statsOrder": 0, "includeAll": false}, {"id": "qs-inbound-order-qty-day", "reportSubject": "QsInboundInvQty", "resKeyTarget": "btBzKind", "resKeyPeriod": "createdOn", "resKeyValue": "value", "changeEntityName": "QsInboundOrder", "changeFilter": {"type": "All", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": null}, "changeAggFields": [], "changeGroupBy": [], "statsDateType": "Day", "converters": {}, "disabled": true, "statsOrder": 0, "includeAll": false}, {"id": "qs-outbound-order-count-day", "reportSubject": "QsOutboundOrderCount", "resKeyTarget": "btBzKind", "resKeyPeriod": "createdOn", "resKeyValue": "value", "changeEntityName": "QsOutboundOrder", "changeFilter": {"type": "All", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": null}, "changeAggFields": [], "changeGroupBy": [], "statsDateType": "Day", "converters": {}, "disabled": true, "statsOrder": 0, "includeAll": false}, {"id": "qs-outbound-order-qty-day", "reportSubject": "QsOutboundInvQty", "resKeyTarget": "btBzKind", "resKeyPeriod": "createdOn", "resKeyValue": "value", "changeEntityName": "QsOutboundOrder", "changeFilter": {"type": "All", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": null}, "changeAggFields": [], "changeGroupBy": [], "statsDateType": "Day", "converters": {}, "disabled": true, "statsOrder": 0, "includeAll": false}, {"id": "qs-inv-summary-day", "reportSubject": "QsStatsInvSummary", "resKeyTarget": "btBzKind", "resKeyPeriod": "createdOn", "resKeyValue": "value", "changeEntityName": "QsInboundOrder", "changeFilter": {"type": "All", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": null}, "changeAggFields": [], "changeGroupBy": [], "statsDateType": "Day", "converters": {}, "disabled": true, "statsOrder": 0, "includeAll": false}, {"id": "qs-material-day", "reportSubject": "QsStatsMaterialCount", "resKeyTarget": "btBzKind", "resKeyPeriod": "createdOn", "resKeyValue": "value", "changeEntityName": "QsInboundOrder", "changeFilter": {"type": "All", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": null}, "changeAggFields": [], "changeGroupBy": [], "statsDateType": "Day", "converters": {}, "disabled": true, "statsOrder": 0, "includeAll": false}, {"id": "qs-inv-summary-month", "reportSubject": "QsStatsInvSummary", "resKeyTarget": "btBzKind", "resKeyPeriod": "createdOn", "resKeyValue": "value", "changeEntityName": "QsInboundOrder", "changeFilter": {"type": "All", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": null}, "changeAggFields": [], "changeGroupBy": [], "statsDateType": "Month", "converters": {}, "disabled": true, "statsOrder": 0, "includeAll": false}, {"id": "qs-material-month", "reportSubject": "QsStatsMaterialCount", "resKeyTarget": "btBzKind", "resKeyPeriod": "createdOn", "resKeyValue": "value", "changeEntityName": "QsInboundOrder", "changeFilter": {"type": "All", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": null}, "changeAggFields": [], "changeGroupBy": [], "statsDateType": "Month", "converters": {}, "disabled": true, "statsOrder": 0, "includeAll": false}, {"id": "qs-inv-summary-year", "reportSubject": "QsStatsInvSummary", "resKeyTarget": "btBzKind", "resKeyPeriod": "createdOn", "resKeyValue": "value", "changeEntityName": "QsInboundOrder", "changeFilter": {"type": "All", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": null}, "changeAggFields": [], "changeGroupBy": [], "statsDateType": "Year", "converters": {}, "disabled": true, "statsOrder": 0, "includeAll": false}, {"id": "qs-material-year", "reportSubject": "QsStatsMaterialCount", "resKeyTarget": "btBzKind", "resKeyPeriod": "createdOn", "resKeyValue": "value", "changeEntityName": "QsInboundOrder", "changeFilter": {"type": "All", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": null}, "changeAggFields": [], "changeGroupBy": [], "statsDateType": "Year", "converters": {}, "disabled": true, "statsOrder": 0, "includeAll": false}, {"id": "qs-inbound-order-count-week", "reportSubject": "QsInboundOrderCount", "resKeyTarget": "", "resKeyPeriod": "", "resKeyValue": "", "changeEntityName": "StatsTimelineValueReport", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "subject", "field2": null, "value": "QsInboundOrderCount", "items": null}, {"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "periodType", "field2": null, "value": "Day", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": null, "name": "value", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "target", "alias": "target"}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Week", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": false}, {"id": "qs-inbound-order-qty-week", "reportSubject": "QsInboundInvQty", "resKeyTarget": "", "resKeyPeriod": "", "resKeyValue": "", "changeEntityName": "StatsTimelineValueReport", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "subject", "field2": null, "value": "QsInboundInvQty", "items": null}, {"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "periodType", "field2": null, "value": "Day", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": null, "name": "value", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "target", "alias": "target"}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Week", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": false}, {"id": "qs-outbound-order-count-week", "reportSubject": "QsOutboundOrderCount", "resKeyTarget": "", "resKeyPeriod": "", "resKeyValue": "", "changeEntityName": "StatsTimelineValueReport", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "subject", "field2": null, "value": "QsOutboundOrderCount", "items": null}, {"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "periodType", "field2": null, "value": "Day", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": null, "name": "value", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "target", "alias": "target"}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Week", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": false}, {"id": "qs-outbound-order-qty-week", "reportSubject": "QsOutboundInvQty", "resKeyTarget": "", "resKeyPeriod": "", "resKeyValue": "", "changeEntityName": "StatsTimelineValueReport", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "subject", "field2": null, "value": "QsOutboundInvQty", "items": null}, {"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "periodType", "field2": null, "value": "Day", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": null, "name": "value", "alias": "value", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "target", "alias": "target"}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Week", "converters": {}, "disabled": false, "statsOrder": 0, "includeAll": false}, {"id": "fleet-robot-failure-rate-hour", "reportSubject": "RobotFailureRate", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "rate", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "InError", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "SUM", "name": "duration", "alias": "rate", "rightFields": [{"function": "SUM", "name": "percentageBase", "alias": "percentageBase", "rightFields": [], "operators": []}], "operators": ["Divide"]}, {"function": "SUM", "name": "duration", "alias": "molecular", "rightFields": [], "operators": []}, {"function": "SUM", "name": "percentageBase", "alias": "denominator", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Hour"}], "statsDateType": "Hour", "converters": {}, "disabled": true, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-failure-rate-day", "reportSubject": "RobotFailureRate", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "rate", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "InError", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "SUM", "name": "duration", "alias": "rate", "rightFields": [{"function": "SUM", "name": "percentageBase", "alias": "percentageBase", "rightFields": [], "operators": []}], "operators": ["Divide"]}, {"function": "SUM", "name": "duration", "alias": "molecular", "rightFields": [], "operators": []}, {"function": "SUM", "name": "percentageBase", "alias": "denominator", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Day", "converters": {}, "disabled": true, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-failure-rate-week", "reportSubject": "RobotFailureRate", "resKeyTarget": "", "resKeyPeriod": "", "resKeyValue": "", "changeEntityName": "StatsTimelineValueReport", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "subject", "field2": null, "value": "RobotFailureRate", "items": null}, {"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "periodType", "field2": null, "value": "Day", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": null, "name": "molecular", "alias": "molecular", "rightFields": [], "operators": []}, {"function": null, "name": "denominator", "alias": "denominator", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "target", "alias": "target", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Week", "converters": {}, "disabled": true, "statsOrder": 0, "includeAll": false, "type": "RATIO"}, {"id": "fleet-robot-noLoad-rate-hour", "reportSubject": "RobotNoLoadRate", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "rate", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "Empty", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "SUM", "name": "duration", "alias": "rate", "rightFields": [{"function": "SUM", "name": "percentageBase", "alias": "percentageBase", "rightFields": [], "operators": []}], "operators": ["Divide"]}, {"function": "SUM", "name": "duration", "alias": "molecular", "rightFields": [], "operators": []}, {"function": "SUM", "name": "percentageBase", "alias": "denominator", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Hour"}], "statsDateType": "Hour", "converters": {}, "disabled": true, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-noLoad-rate-day", "reportSubject": "RobotNoLoadRate", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "rate", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "Empty", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "SUM", "name": "duration", "alias": "rate", "rightFields": [{"function": "SUM", "name": "percentageBase", "alias": "percentageBase", "rightFields": [], "operators": []}], "operators": ["Divide"]}, {"function": "SUM", "name": "duration", "alias": "molecular", "rightFields": [], "operators": []}, {"function": "SUM", "name": "percentageBase", "alias": "denominator", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Day", "converters": {}, "disabled": true, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-noLoad-rate-week", "reportSubject": "RobotNoLoadRate", "resKeyTarget": "", "resKeyPeriod": "", "resKeyValue": "", "changeEntityName": "StatsTimelineValueReport", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "subject", "field2": null, "value": "RobotNoLoadRate", "items": null}, {"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "periodType", "field2": null, "value": "Day", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": null, "name": "molecular", "alias": "molecular", "rightFields": [], "operators": []}, {"function": null, "name": "denominator", "alias": "denominator", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "target", "alias": "target", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Week", "converters": {}, "disabled": true, "statsOrder": 0, "includeAll": false, "type": "RATIO"}, {"id": "fleet-robot-idle-rate-hour", "reportSubject": "RobotIdleRate", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "rate", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "Idle", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "SUM", "name": "duration", "alias": "rate", "rightFields": [{"function": "SUM", "name": "percentageBase", "alias": "percentageBase", "rightFields": [], "operators": []}], "operators": ["Divide"]}, {"function": "SUM", "name": "duration", "alias": "molecular", "rightFields": [], "operators": []}, {"function": "SUM", "name": "percentageBase", "alias": "denominator", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Hour"}], "statsDateType": "Hour", "converters": {}, "disabled": true, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-idle-rate-day", "reportSubject": "RobotIdleRate", "resKeyTarget": "robotName", "resKeyPeriod": "finishedOn", "resKeyValue": "rate", "changeEntityName": "RobotPropChangeTimeline", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "type", "field2": null, "value": "Idle", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": "SUM", "name": "duration", "alias": "rate", "rightFields": [{"function": "SUM", "name": "percentageBase", "alias": "percentageBase", "rightFields": [], "operators": []}], "operators": ["Divide"]}, {"function": "SUM", "name": "duration", "alias": "molecular", "rightFields": [], "operators": []}, {"function": "SUM", "name": "percentageBase", "alias": "denominator", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "robotName", "alias": "robotName", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Day", "converters": {}, "disabled": true, "statsOrder": 0, "includeAll": true}, {"id": "fleet-robot-idle-rate-week", "reportSubject": "RobotIdleRate", "resKeyTarget": "", "resKeyPeriod": "", "resKeyValue": "", "changeEntityName": "StatsTimelineValueReport", "changeFilter": {"type": "Compound", "or": false, "not": false, "operator": null, "field1": null, "field2": null, "value": null, "items": [{"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "subject", "field2": null, "value": "RobotIdleRate", "items": null}, {"type": "General", "or": false, "not": false, "operator": "Eq", "field1": "periodType", "field2": null, "value": "Day", "items": null}, {"type": "General", "or": false, "not": false, "operator": "NotNull", "field1": "finishedOn", "field2": null, "value": null, "items": null}]}, "changeAggFields": [{"function": null, "name": "molecular", "alias": "molecular", "rightFields": [], "operators": []}, {"function": null, "name": "denominator", "alias": "denominator", "rightFields": [], "operators": []}], "changeGroupBy": [{"name": "target", "alias": "target", "statisticDateType": null}, {"name": "finishedOn", "alias": "finishedOn", "statisticDateType": "Day"}], "statsDateType": "Week", "converters": {}, "disabled": true, "statsOrder": 0, "includeAll": false, "type": "RATIO"}]