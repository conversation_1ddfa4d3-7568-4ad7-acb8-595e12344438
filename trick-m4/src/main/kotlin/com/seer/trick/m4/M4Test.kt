package com.seer.trick.m4


import com.seer.wcs.device.ClientDeviceException
import com.seer.wcs.device.plc.modbus.ModbusClient
import com.seer.wcs.device.plc.modbus.ModbusConfig
import com.seer.wcs.device.plc.modbus.ModbusWriteReq
import org.slf4j.LoggerFactory

object M4Test {
  fun start() {
    val logger = LoggerFactory.getLogger("test")
    
    
    val mc = ModbusClient(ModbusConfig("Test1", autoRetry = true, timeout = 15000, host = "*************", port = 502))
    try {
      mc.init()
      
      for (i in 0..100) {
        val r = mc.writeRetry(ModbusWriteReq(0x10, 1, 0), listOf(10, 20, 30))
        logger.info("write r; $r")
        Thread.sleep(3000)
      }
      
      // val value = mc.read(ModbusReadReq(0x03, 1, 0, 4))
      // logger.info("read value: $value")
    } catch (e: ClientDeviceException) {
      logger.error("test: ${e.kind}: ${e.message}")
    } catch (e: Exception) {
      logger.error("test", e)
    } finally {
      // mc.dispose()
    }
  }
}