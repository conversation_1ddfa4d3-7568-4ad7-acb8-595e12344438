package com.seer.trick.m4

import com.seer.trick.base.AppModule
import com.seer.trick.base.BaseModule
import com.seer.trick.base.BootHelper
import com.seer.trick.falcon.FalconModule
import com.seer.trick.fleet.FleetModule
import com.seer.trick.quick.store.QuickStoreModule
import com.seer.trick.robot.RobotModule
import com.seer.wcs.WcsModule
import java.util.*

object M4Application {

  fun getVersion(): String? {
    val properties = Properties()
    val stream = M4Application.javaClass.getResourceAsStream("/properties-from-pom.properties")
      ?: return null
    stream.use {
      properties.load(it)
    }
    return properties.getProperty("trick.version")
  }
}

fun main(args: Array<String>) {
  val baseConfig = BootHelper.parseJsonConfigFileFromArgs0(args)

  val modules = mutableListOf<AppModule>(BaseModule)
  if (!baseConfig.disabledModules.contains("Falcon")) modules.add(FalconModule)
  if (!baseConfig.disabledModules.contains("Wcs")) modules.add(WcsModule)
  if (!baseConfig.disabledModules.contains("Robot")) modules.add(RobotModule)
  if (!baseConfig.disabledModules.contains("Fleet")) modules.add(FleetModule)
  if (!baseConfig.disabledModules.contains("QuickStore")) modules.add(QuickStoreModule)

  BootHelper.boot(M4Application.getVersion(), modules)
}