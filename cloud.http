

### PING 接口
# 用来检查是否能与服务器通讯并且处于已登录状态
# 如果未登录（或过期）返回 401。
# 如果已登录，返回用户 ID、用户名等信息

GET http://localhost:5800/api/ping


### 登录接口
# 登录成功响应 200，登录失败响应 400，给出错误原因。
# 登录成功响应的正文中，给出用户 ID，例如
# {
#   "userId": "__admin__",
#   "userToken": "n6F0HgxprbgTvH1AJ95qmQ5H"
# }

POST http://localhost:5800/api/sign-in
Content-Type: application/json

{
  "username": "admin",
  "password": "admin"
}


###
POST http://localhost:5800/api/aac/gen
Content-Type: application/json

{
  "name": "Test",
  "fp": "1234567890",
  "acp": {
    "expiredOn": "2020-12-23 01:01:00"
  }
}