顶层任务和子任务都会在 unfinishedTasks 里，记录 Record。宕机重启后都会被重新加载。

TaskRecord 增加记录：topTaskId 和 subTask。

宕机后，顶层任务被标记为暂停。子任务不需要。

暂停、恢复、重试等，只能针对顶层任务！TODO VS 找到对应的顶层任务操作！！

故障了要停止原来的 future，作废，收回。

取消任务：

- 任务可能处于故障、暂停
- 有并发问题吗？标记取消了，又被 started 了

# TODO

- 表达式类型，增加
- 子任务
    - 也有 TaskRecord，
    - TaskRecord/TaskRuntime 特殊标识
    - process 的特殊处理，例如，如果遇到错误，最上层任务终止
    - 是否在列表中显示
    - 不能被独立暂停、恢复等

# Features

- 自定义流程
- 分支、循环等高级流程
- 变量与表达式
- 子任务
- 子任务带 body
- 清理
- 故障恢复
- 人工处理