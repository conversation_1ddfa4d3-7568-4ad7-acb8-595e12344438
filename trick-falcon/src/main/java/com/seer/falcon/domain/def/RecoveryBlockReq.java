package com.seer.falcon.domain.def;

import com.seer.entity.NsBin;
import com.seer.entity.NsContainer;
import com.seer.entity.NsInvLayout;

import java.util.List;

public class RecoveryBlockReq {
    public String taskId;

    // 要删除某条库存明细，给加上 "remove" : true 即可
    public List<NsInvLayout> layouts;
    public List<NsContainer> containers;
    public List<NsBin> bins;
    // 执行哪些 bp 就传哪些
    public List<ManualExecuteBlocks> blocks;
}
