package com.seer.falcon.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.seer.core.ext.ExtCenter;
import com.seer.core.ext.ExtOutgoingCenter;
import com.seer.core.helper.IdHelper;
import com.seer.core.helper.JsonHelper;
import com.seer.core.helper.ListHelper;
import com.seer.core.helper.MapHelper;
import com.seer.core.schedule.DefaultExecutor;
import com.seer.core.service.NsIdGenManager;
import com.seer.entity.*;
import com.seer.falcon.*;
import com.seer.falcon.domain.def.NsFalconTaskDefSnapshot;
import com.seer.falcon.domain.def.*;
import com.seer.falcon.error.*;
import com.seer.falcon.event.FalconTaskEvent;
import com.seer.falcon.event.FalconTaskEventBus;
import com.seer.meta.controller.MVELHelper;
import com.seer.meta.domain.BzError;
import com.seer.meta.domain.NsEntityMapValue;
import com.seer.meta.domain.NsFindOptions;
import com.seer.meta.service.NsDictManager;
import com.seer.meta.service.NsEntityReader;
import com.seer.meta.service.NsEntityWriter;
import com.seer.meta.service.NsMetaManager;
import com.seer.robot.sr.service.StandardDispatch;
import com.seer.web.MyWebSocketHandler;
import com.shadow.ns.service.core.NsBlockedMarkService;
import lombok.SneakyThrows;
import org.apache.commons.collections4.IteratorUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.mvel2.MVEL;
import org.mvel2.PropertyAccessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.lang.reflect.InvocationTargetException;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.seer.core.helper.NumHelper.anyToDouble;
import static com.seer.core.helper.NumHelper.anyToLong;
import static com.seer.meta.domain.NsComplexQuery.*;

public class FalconTaskService {

    public void manualProcessBlock(RecoveryBlockReq recoveryBlock, TaskRuntime taskRuntime) {
        // 库位
        Optional.ofNullable(recoveryBlock.bins).ifPresent(bins -> {
            for (var b : bins) {
                entityWriter.updateOneById(b.getId(), b);
            }
        });

        // 容器
        Optional.ofNullable(recoveryBlock.containers).ifPresent(containers -> {
            for (var c : containers) {
                entityWriter.updateOneById(c.getId(), c);
            }
        });

        // 库存
        Optional.ofNullable(recoveryBlock.layouts).ifPresent(invLayouts -> {
            for (var inv : invLayouts) {
                if (BooleanUtils.isTrue(Boolean.valueOf(inv.fetchMap().get("remove").toString())))
                    entityWriter.deleteOneById(inv.getId(), NsInvLayout.class.getSimpleName());
                else entityWriter.updateOneById(inv.getId(), inv);
            }
        });

        // 需要特殊处理的块
        Optional.ofNullable(recoveryBlock.blocks).ifPresent(blocks -> {
            for (var b : recoveryBlock.blocks) {
                var bCfg = findBlockByName(taskRuntime, b.blockName);

                // 填入参数
                Optional.ofNullable(b.inputParams).ifPresent(inputParams -> {
                    for (var ip : inputParams.entrySet())
                        bCfg.inputParams.put(ip.getKey(), ip.getValue());
                });

                var blockRuntime = new BlockRuntime(bCfg, getBlockDef(b.blockType), b.blockName, new BlockContext(null),
                    taskRuntime);

                processBlock(blockRuntime);
            }
        });
    }

    public BlockConfig findBlockByName(TaskRuntime taskRuntime, String blockName) {
        return findBlockByName(taskRuntime.def.rootBlock, blockName);
    }

    public BlockConfig findBlockByName(BlockConfig blockConfig, String blockName) {
        if (blockConfig.name.equals(blockName)) return blockConfig;

        // 搜索：深度优先，即自上向下
        for (var list : blockConfig.children.values()) {
            for (var bc : list) {
                var t = findBlockByName(bc, blockName);
                if (t != null) return t;
            }
        }

        return null;
    }

    public HashMap<String, BlockConfig> listManualRecoveryBlock(TaskRuntime taskRuntime) {
        return listManualRecoveryBlock(taskRuntime.def.rootBlock);
    }

    public HashMap<String, BlockConfig> listManualRecoveryBlock(BlockConfig bCfg) {
        var r = new HashMap<String, BlockConfig>();
        var def = getBlockDefOrNull(bCfg.blockType);

        if (def.manualRecovery) r.put(bCfg.name, bCfg);

        for (var list : bCfg.children.values()) {
            for (var bc : list) {
                r.putAll(listManualRecoveryBlock(bc));
            }
        }

        return r;
    }

    public void checkDefBlocksExist(TaskRuntime taskRuntime) {
        Map<String, String> notExistBlockMap = new HashMap<>();
        checkChildBlocksExist(notExistBlockMap, taskRuntime.def.rootBlock);

        if (!notExistBlockMap.isEmpty())
            throw BzError.unRegisteredBlock(taskRuntime.def.label, JsonHelper.writeValueAsString(notExistBlockMap));
    }

    private void checkChildBlocksExist(Map<String, String> notExistMap, BlockConfig bCfg) {
        var t = getBlockDefOrNull(bCfg.blockType);
        if (t == null) notExistMap.put(bCfg.name, bCfg.blockType);
        for (var list : bCfg.children.values()) {
            for (var bc : list) {
                checkChildBlocksExist(notExistMap, bc);
            }
        }
    }

    private BlockDef getBlockDefOrNull(String name) {
        var registers = applicationContext.getBean(BlockRegisters.class);
        return registers.getBlockDef(name);
    }

    public Object fire(TaskEvent event) {
        for (var listener : listeners) {
            DefaultExecutor.bgCacheExecutor.submit(() -> {
                try {
                    listener.callback(event);
                } catch (Exception e) {
                    logger.error("fire event", e);
                }
            });
        }
        DefaultExecutor.bgCacheExecutor.submit(
            () -> myWebSocketHandler.sendAll("FalconTask", JsonHelper.writeValueAsString(event)));
        return sendTaskEventToExt(event);
    }

    private Object sendTaskEventToExt(TaskEvent event) {
        try {
            if (extCenter.providedExtFunctions.contains("onFalconTaskEvent")) {
                Map<String, Object> params = new HashMap<>();
                params.put("taskId", event.getTaskId());
                params.put("taskDefLabel", event.getTaskDefLabel());
                params.put("eventName", event.getEventName());
                params.put("eventData", event.getEventData());
                return outgoingCenter.callFunction("onFalconTaskEvent", params, true);
            }
        } catch (Exception e) {
            logger.error("sendTaskEventToExt", e);
            throw e;
        }
        return null;
    }

    private void pauseVehicle(String topTaskId) {
        var records = resourceService.getResources(topTaskId);
        for (var r : records) {
            try {
                var type = TaskResourceType.valueOf(r.getType());
                String key = r.getKey();
                if (type == TaskResourceType.Vehicle)
                    applicationContext.getBean("standardDispatch", StandardDispatch.class).pause(key);
            } catch (Throwable e) {
                logger.error("Failed to pause vehicle {}", r, e);
            }
        }
    }

    private void resumeVehicle(String topTaskId) {
        var records = resourceService.getResources(topTaskId);
        for (var r : records) {
            try {
                var type = TaskResourceType.valueOf(r.getType());
                String key = r.getKey();
                if (type == TaskResourceType.Vehicle)
                    applicationContext.getBean("standardDispatch", StandardDispatch.class).resume(key);
            } catch (Throwable e) {
                logger.error("Failed to resume vehicle {}", r, e);
            }
        }
    }

    private String buildLogHead(TaskRuntime taskRuntime) {
        var def = taskRuntime.def;
        return String.format("%s[%s/%s][%s] ", dict.lo("FalconTask"), def.label, def._version, taskRuntime.taskId);
    }

}
