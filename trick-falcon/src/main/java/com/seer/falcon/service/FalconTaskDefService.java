package com.seer.falcon.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.seer.core.helper.*;
import com.seer.core.init.BootConfig;
import com.seer.entity.NsFalconTaskDefSnapshot;
import com.seer.falcon.domain.def.BlockConfig;
import com.seer.falcon.domain.def.BlockParamType;
import com.seer.falcon.domain.def.NsFalconTaskDef;
import com.seer.falcon.domain.def.TaskInputParamDef;
import com.seer.meta.domain.BzError;
import com.seer.meta.domain.NsFindOptions;
import com.seer.meta.service.NsDictManager;
import com.seer.meta.service.NsEntityReader;
import com.seer.meta.service.NsEntityWriter;
import com.shadow.ns.domain.NsInvChangeSource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.File;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;

import static com.seer.meta.domain.NsComplexQuery.and;
import static com.seer.meta.domain.NsComplexQuery.eq;

@Service
public class FalconTaskDefService {
    
    private final NsEntityReader readService;
    private final NsEntityWriter writeService;
    private final NsDictManager dict;
    public final List<NsFalconTaskDef> defs = new CopyOnWriteArrayList<>();
    
    private final String rootBlockConfigTemplate;
    
    public FalconTaskDefService(NsEntityReader readService, NsEntityWriter writeService, NsDictManager dict) {
        this.readService = readService;
        this.writeService = writeService;
        this.dict = dict;
        
        rootBlockConfigTemplate = FileHelper.loadClasspathResourceAsString("/root-block.json");
    }
    
    @PostConstruct
    public void init() {
        initOrderInputParams();
        
        var file = new File(BootConfig.dirs.configDir, "falcon.yaml");
        if (!file.exists()) return;
        List<NsFalconTaskDef> defs = YamlHelper.parseFileToType(file, new TypeReference<>() {});
        this.defs.clear();
        if (defs != null) this.defs.addAll(defs);
    }
    
    public synchronized NsFalconTaskDef fetchLatestTaskDefByLabel(String label) {
        var def = ListHelper.find(defs, it -> Objects.equals(it.label, label));
        if (def == null) throw BzError.taskDefNotFound(label);
        ensureSnapshot(def);
        return def;
    }
    
    public boolean checkDefExistedByLabel(String label) {
        var def = ListHelper.find(defs, it -> Objects.equals(it.label, label));
        return def != null;
    }
    
    public synchronized NsFalconTaskDef fetchLatestTaskDefById(String defId) {
        var def = ListHelper.find(defs, it -> Objects.equals(it._id, defId));
        if (def == null) throw BzError.taskDefNotFound(defId);
        ensureSnapshot(def);
        return def;
    }
    
    private void ensureSnapshot(NsFalconTaskDef def) {
        NsFalconTaskDefSnapshot snapshot = readService.findOne(NsFalconTaskDefSnapshot.class,
                and(eq("defId", def._id), eq("defVersion", def._version)),
                new NsFindOptions().setProjection(List.of("_id")));
        
        if (null == snapshot) {
            snapshot = new NsFalconTaskDefSnapshot();
            snapshot.setId(IdHelper.objectIdStr());
            snapshot.setDefId(def._id);
            snapshot.setDefVersion(def._version);
            snapshot.setDef(JsonHelper.writeValueAsString(def));
            writeService.createOne(snapshot);
        }
    }
    
    public synchronized void save(NsFalconTaskDef def) {
        if (StringUtils.isBlank(def._id)) def._id = IdHelper.objectIdStr();
        var index = ListHelper.indexOf(defs, def2 -> Objects.equals(def2._id, def._id));
        if (index >= 0) {
            defs.set(index, def);
        } else {
            defs.add(def);
        }
        var file = new File(BootConfig.dirs.configDir, "falcon.yaml");
        YamlHelper.writeYamlToFile(file, defs);
    }
    
    public synchronized void remove(List<String> ids) {
        List<NsFalconTaskDef> newList = new ArrayList<>();
        for (var def : defs) {
            if (ids.contains(def._id)) continue;
            newList.add(def);
        }
        defs.clear();
        defs.addAll(newList);
        var file = new File(BootConfig.dirs.configDir, "falcon.yaml");
        YamlHelper.writeYamlToFile(file, defs);
    }
    
    private static Map<String, List<TaskInputParamDef>> orderInputParams = new HashMap<>();
    
    private void initOrderInputParams() {
        orderInputParams.put(NsInvChangeSource.SourceInbound,
                List.of(new TaskInputParamDef("template", BlockParamType.String, dict.lo("Template")),
                        new TaskInputParamDef("containerId", BlockParamType.String, dict.lo("ContainerNo")),
                        new TaskInputParamDef("startBin", BlockParamType.String, dict.lo("StartBin")),
                        new TaskInputParamDef("endBin", BlockParamType.String, dict.lo("TargetBinNo")),
                        new TaskInputParamDef("endDistrict", BlockParamType.String, dict.lo("EndDistrictId")),
                        new TaskInputParamDef("endWarehouse", BlockParamType.String, dict.lo("EndWarehouseId")),
                        new TaskInputParamDef("sourceOrder", BlockParamType.String, dict.lo("BzOrderNo")),
                        new TaskInputParamDef("invState", BlockParamType.String, dict.lo("InvStatus"))));
        orderInputParams.put(NsInvChangeSource.SourceInboundNotice, List.of());
        orderInputParams.put(NsInvChangeSource.SourcePutAway,
                List.of(new TaskInputParamDef("template", BlockParamType.String, dict.lo("Template")),
                        new TaskInputParamDef("containerId", BlockParamType.String, dict.lo("ContainerNo")),
                        new TaskInputParamDef("startBin", BlockParamType.String, dict.lo("StartBin")),
                        new TaskInputParamDef("endBin", BlockParamType.String, dict.lo("TargetBinNo")),
                        new TaskInputParamDef("endDistrict", BlockParamType.String, dict.lo("EndDistrictId")),
                        new TaskInputParamDef("sourceOrder", BlockParamType.String, dict.lo("BzOrderNo")),
                        new TaskInputParamDef("qcResult", BlockParamType.String, dict.lo("QcResult")),
                        new TaskInputParamDef("partLines", BlockParamType.JSONArray, dict.lo("BOM"))));
        orderInputParams.put(NsInvChangeSource.SourceTakeAway,
                List.of(new TaskInputParamDef("template", BlockParamType.String, dict.lo("Template")),
                        new TaskInputParamDef("containerId", BlockParamType.String, dict.lo("ContainerNo")),
                        new TaskInputParamDef("startBin", BlockParamType.String, dict.lo("StartBin")),
                        new TaskInputParamDef("endBin", BlockParamType.String, dict.lo("TargetBinNo")),
                        new TaskInputParamDef("endDistrict", BlockParamType.String, dict.lo("EndDistrictId")),
                        new TaskInputParamDef("sourceOrder", BlockParamType.String, dict.lo("BzOrderNo"))));
        orderInputParams.put(NsInvChangeSource.SourceBindContainer,
                List.of(new TaskInputParamDef("template", BlockParamType.String, dict.lo("Template")),
                        new TaskInputParamDef("containerId", BlockParamType.String, dict.lo("ContainerNo")),
                        new TaskInputParamDef("startBin", BlockParamType.String, dict.lo("StartBin")),
                        new TaskInputParamDef("inboundOrderId", BlockParamType.String, dict.lo("InboundOrderNo2"))));
        orderInputParams.put(NsInvChangeSource.SourceCallEmptyContainer,
                List.of(new TaskInputParamDef("containerId", BlockParamType.String, dict.lo("ContainerNo")),
                        new TaskInputParamDef("startBinId", BlockParamType.String, dict.lo("StartBin")),
                        new TaskInputParamDef("endBinId", BlockParamType.String, dict.lo("TargetBinNo")),
                        new TaskInputParamDef("endDistrictIds", BlockParamType.String, dict.lo("EndDistrictIds")),
                        new TaskInputParamDef("inboundOrderAssign", BlockParamType.String, dict.lo(
                                "InboundOrderAssign")),
                        new TaskInputParamDef("inboundOrderId", BlockParamType.String, dict.lo("InboundOrderNo2"))));

        orderInputParams.put(NsInvChangeSource.SourceOutbound,
                List.of(new TaskInputParamDef("containerId", BlockParamType.String, dict.lo("ContainerNo")),
                        new TaskInputParamDef("startBinId", BlockParamType.String, dict.lo("StartBin")),
                        new TaskInputParamDef("targetBinId", BlockParamType.String, dict.lo("TargetBinNo")),
                        new TaskInputParamDef("targetDistrictId", BlockParamType.String, dict.lo("EndDistrictId")),
                        new TaskInputParamDef("transportOn", BlockParamType.String, dict.lo("ExpectTransportOn")),
                        new TaskInputParamDef("whole", BlockParamType.Boolean, dict.lo("Whole")),
                        new TaskInputParamDef("pickLines", BlockParamType.JSONArray, dict.lo("IpLines")),
                        new TaskInputParamDef("outLines", BlockParamType.JSONArray, dict.lo("NsOutboundOrderLine"))));
        orderInputParams.put(NsInvChangeSource.SourcePick,
                List.of(new TaskInputParamDef("containerId", BlockParamType.String, dict.lo("ContainerNo"))));
        orderInputParams.put(NsInvChangeSource.SourceCount,
                List.of(new TaskInputParamDef("containerId", BlockParamType.String, dict.lo("ContainerNo")),
                        new TaskInputParamDef("countDistrict", BlockParamType.String, dict.lo("CountingDistrict")),
                        new TaskInputParamDef("backBin", BlockParamType.String, dict.lo("ReturnBinAfterCount"))));
        orderInputParams.put(NsInvChangeSource.SourceTransfer, List.of());
        orderInputParams.put(NsInvChangeSource.SourcePickAndSort,
                List.of(new TaskInputParamDef("containerId", BlockParamType.String, dict.lo("ContainerNo")),
                        new TaskInputParamDef("startBin", BlockParamType.String, dict.lo("StartBin")),
                        new TaskInputParamDef("endBin", BlockParamType.String, dict.lo("TargetBinNo")),
                        new TaskInputParamDef("endDistrict", BlockParamType.String, dict.lo("EndDistrictId")),
                        new TaskInputParamDef("sourceOrder", BlockParamType.String, dict.lo("BzOrderNo")),
                        new TaskInputParamDef("invState", BlockParamType.String, dict.lo("InvStatus"))));
        orderInputParams.put(NsInvChangeSource.RsCountOut,
                List.of(new TaskInputParamDef("containerId", BlockParamType.String, dict.lo("ContainerNo")),
                        new TaskInputParamDef("startBin", BlockParamType.String, dict.lo("StartBin")),
                        new TaskInputParamDef("sourceOrder", BlockParamType.String, dict.lo("BzOrderNo")),
                        new TaskInputParamDef("countEndDistrict", BlockParamType.String, dict.lo("CountEndDistrict"))));
        orderInputParams.put(NsInvChangeSource.RsCountBack,
                List.of(new TaskInputParamDef("containerId", BlockParamType.String, dict.lo("ContainerNo")),
                        new TaskInputParamDef("startBin", BlockParamType.String, dict.lo("StartBin")),
                        new TaskInputParamDef("endBin", BlockParamType.String, dict.lo("TargetBinNo")),
                        new TaskInputParamDef("sourceOrder", BlockParamType.String, dict.lo("BzOrderNo"))));
    }
    
    public String createTaskDefForOrder(String orderType, String defLabel) {
        var oldDef = ListHelper.find(defs, it -> Objects.equals(it.label, defLabel));
        if (oldDef != null) return oldDef._id;
        
        List<TaskInputParamDef> inputParams = orderInputParams.get(orderType);
        if (inputParams == null) throw BzError.orderTypeNotSupport(orderType);
        
        BlockConfig blockConfig = JsonHelper.parseStringToClass(rootBlockConfigTemplate, BlockConfig.class);
        var def = new NsFalconTaskDef();
        def._id = IdHelper.objectIdStr();
        def._version = 1;
        def.label = defLabel;
        def.builtin = false;
        def.rootBlock = blockConfig;
        def.inputParams = inputParams;
        
        save(def);
        
        return def._id;
    }
    
    public List<BlockConfig> getTaskBlocks(NsFalconTaskDef def) {
        var r = new ArrayList<BlockConfig>();
        traverseBlockCfg(def.rootBlock, r);
        
        return r;
    }
    
    private void traverseBlockCfg(BlockConfig blockConfig, List<BlockConfig> list) {
        if (blockConfig != null) list.add(blockConfig);
        
        for (var l : blockConfig.children.values()) {
            for (var b : l) {
                traverseBlockCfg(b, list);
            }
        }
    }
}
