package com.seer.falcon.service;

import com.seer.entity.NsBin;
import com.seer.entity.NsContainer;
import com.seer.entity.NsFalconTaskResourceRecord;
import com.seer.falcon.TaskResourceType;
import com.seer.meta.service.NsDictManager;
import com.seer.meta.service.NsEntityReader;
import com.seer.meta.service.NsEntityWriter;
import com.seer.robot.hr.HrRobotService;
import com.seer.robot.sr.service.ExecutingTaskRecordService;
import com.shadow.ns.service.common.NsCommonBusinessService;
import com.shadow.ns.service.core.NsResourceLockService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.seer.meta.domain.NsComplexQuery.and;
import static com.seer.meta.domain.NsComplexQuery.eq;

/**
 * 猎鹰任务 “资源” Service
 * <p>
 * 猎鹰任务运行时会锁定一系列 “资源”，如库位、容器、容器内库存等。
 * 成功完成的猎鹰任务会通过配置的 BP 解锁这些 “资源”，但 “终止” 的猎鹰任务（因无法执行后续的 BP 导致）可能有未释放的 “资源”。
 * FalconTaskResourceService 的目的是在 “终止” 猎鹰任务时，自动释放 “资源”。
 * <p>
 * “资源” 类型见 {@link TaskResourceType}。
 * 添加 “资源” 的方法见 {@link FalconTaskResourceService#addTaskResource(String, TaskResourceType, String)}
 * 释放 “资源” 的方法见 {@link FalconTaskResourceService#releaseTaskResources(String)}
 */
@Service
public class FalconTaskResourceService {
    
    private static final Logger logger = LoggerFactory.getLogger(FalconTaskResourceService.class);
    
    private final String reason;
    
    private final NsCommonBusinessService commonBusinessService;
    private final NsResourceLockService resourceLockService;
    private final ExecutingTaskRecordService executingTaskRecordService;
    private final NsDictManager dict;
    private final NsEntityReader readService;
    private final NsEntityWriter writeService;
    private final HrRobotService hrRobotService;
    
    public FalconTaskResourceService(NsCommonBusinessService commonBusinessService,
                                     NsResourceLockService resourceLockService,
                                     ExecutingTaskRecordService executingTaskRecordService, NsDictManager dict,
                                     NsEntityReader readService, NsEntityWriter writeService,
                                     HrRobotService hrRobotService) {
        this.commonBusinessService = commonBusinessService;
        this.resourceLockService = resourceLockService;
        this.executingTaskRecordService = executingTaskRecordService;
        this.dict = dict;
        reason = dict.lo("ForceReleaseResources");
        this.readService = readService;
        this.writeService = writeService;
        this.hrRobotService = hrRobotService;
    }
    
    /**
     * 添加猎鹰任务 “资源”
     */
    public void addTaskResource(String taskId, TaskResourceType type, String key) {
        NsFalconTaskResourceRecord r = new NsFalconTaskResourceRecord();
        r.setCreatedOn(new Date());
        r.setTaskId(taskId);
        r.setType(type.name());
        r.setKey(key);
        writeService.createOne(r);
    }
    
    public void addTaskResources(String taskId, TaskResourceType type, List<String> keys) {
        List<NsFalconTaskResourceRecord> list = new ArrayList<>();
        for (String key : keys) {
            NsFalconTaskResourceRecord r = new NsFalconTaskResourceRecord();
            r.setCreatedOn(new Date());
            r.setTaskId(taskId);
            r.setType(type.name());
            r.setKey(key);
            list.add(r);
        }
        writeService.createMany(list);
    }
    
    public void removeTaskResource(String taskId, TaskResourceType type, String key) {
        writeService.deleteOne(and(eq("taskId", taskId), eq("type", type), eq("key", key)),
                "NsFalconTaskResourceRecord");
    }
    
    /**
     * 释放同组的任务资源
     * <p>
     * 用于不同的猎鹰任务公用相同 RDS-Core 订单的情况。
     * 此方法会释放同 RDS-Core 订单关联的所有的猎鹰任务 “资源”
     */
    public void releaseRelatedTaskResources(String taskId) {
        var records = readService.findMany(NsFalconTaskResourceRecord.class,
                and(eq("taskId", taskId), eq("type", "RdsCoreOrder")));
        
        for (var r : records) {
            var rdsCoreOrderId = r.getKey();
            var relatedTask = readService.findMany(NsFalconTaskResourceRecord.class,
                    and(eq("type", "RdsCoreOrder"), eq("key", rdsCoreOrderId)));
            for (var t : relatedTask) {
                releaseTaskResources(t.getTaskId());
            }
        }
    }
    
    /**
     * 释放猎鹰任务 “资源”
     */
    public void releaseTaskResources(String taskId) {
        var records = readService.findMany(NsFalconTaskResourceRecord.class, eq("taskId", taskId));
        logger.info("Terminate the task and release the task's [{}] unreleased resources {}", taskId, records);
        writeService.deleteMany(eq("taskId", taskId), "NsFalconTaskResourceRecord");
        for (var r : records) {
            try {
                var type = TaskResourceType.valueOf(r.getType());
                String key = r.getKey();
                switch (type) {
                    case Bin:
                        resourceLockService.unlockBin(key, taskId, reason);
                        break;
                    case Container:
                        resourceLockService.unlockContainer(key, taskId, reason);
                        break;
                    case TrackContainer:
                        // FIXME 生成的追踪容器，在任务成功的情况下也要删除？
                        // 追踪容器，直接删除
                        var bin = readService.findOne(NsBin.class, eq("container", r.getKey()));
                        if (bin == null) break;
                        commonBusinessService.unbindBinContainer(bin.getId(), r.getKey(), "",
                                "Falcon release resource");
                        commonBusinessService.removeContainerInv(bin.getId(), "", "", "", 0, "");
                        writeService.deleteOneById(r.getKey(), NsContainer.class.getSimpleName());
                        break;
                    case BinContainer:
                        var arr = key.split(":-:"); // binId:-:containerId
                        if (arr.length == 2) {
                            logger.info("猎鹰任务释放资源，类型={}，key={}", TaskResourceType.BinContainer.name(), key);
                            var reason = dict.lo("猎鹰任务释放资源");
                            commonBusinessService.unbindBinContainer( arr[0],  arr[1], taskId, reason);
                        } else {
                            logger.error("猎鹰任务释放[BinContainer]资源异常，key 按照 :-: 拆分失败，key={}", key);
                        }
                        break;
                    case ContainerInv:
                        resourceLockService.unlockAllInvLayoutsByTopContainer(key, taskId, reason);
                        break;
                    case Vehicle:
                    case SeerDispatchOrder:
                        executingTaskRecordService.terminateOrder(key);
                        break;
                    case HrDispatchOrder:
                        hrRobotService.cancelTask(key);
                        break;
                }
            } catch (Throwable e) {
                logger.error(dict.lo("ReleaseResourceException"), r, e);
            }
        }
    }
    
    public List<NsFalconTaskResourceRecord> getResources(String taskId) {
        var records = readService.findMany(NsFalconTaskResourceRecord.class, eq("taskId", taskId));
        logger.info(dict.lo("GetResources"), taskId, records);
        return records;
    }
    
    
}
