package com.seer.falcon.service;

import com.seer.core.helper.JsonHelper;
import com.seer.entity.NsFalconGlobalVariable;
import com.seer.falcon.domain.def.FalconGlobalVariable;
import com.seer.meta.domain.BzError;
import com.seer.meta.domain.NsComplexQuery;
import com.seer.meta.service.NsDictManager;
import com.seer.meta.service.NsEntityReader;
import com.seer.meta.service.NsEntityWriter;
import com.seer.meta.service.NsMetaManager;
import com.seer.wms.service.NsBusinessManager;
import kotlin.jvm.Synchronized;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.seer.meta.domain.NsComplexQuery.all;

/**
 * Falcon 全局变量
 * <p>
 * 实现方案：
 * 1. meta 配置字段，字段作为变量定义。过于依赖 meta，不便于后续提取
 * 2. 配置在 business 里。同上
 * 3. 实体记录，每个变量一条记录。代码量较大
 */
@Service
public class FalconGlobalVariablesService {
    private static final Logger logger = LoggerFactory.getLogger(FalconGlobalVariablesService.class);
    
    // Falcon 全局变量 （business 模式）
    public final Map<String, Object> globalVariables = new ConcurrentHashMap<>();
    
    private final NsDictManager dict;
    private final NsBusinessManager businessManager;
    private final NsEntityReader entityReader;
    private final NsEntityWriter entityWriter;
    private final NsMetaManager metaManager;
    
    public FalconGlobalVariablesService(NsDictManager dict, NsBusinessManager businessManager,
                                        NsEntityReader entityReader, NsEntityWriter entityWriter,
                                        NsMetaManager metaManager) {
        this.dict = dict;
        this.businessManager = businessManager;
        this.entityReader = entityReader;
        this.entityWriter = entityWriter;
        this.metaManager = metaManager;
    }
    
    @PostConstruct
    public void reloadGlobalVariables() {
        logger.info("reload Falcon global variable");
        globalVariables.clear();
        
        var list = entityReader.findMany(NsFalconGlobalVariable.class, NsComplexQuery.all(), null);
        if (CollectionUtils.isEmpty(list)) return;
        var list2 = list.stream().map(this::parseRecord).collect(Collectors.toList());
        for (var fv : list2) {
            globalVariables.put(fv.name, ObjectUtils.defaultIfNull(fv.value, ""));
        }
    }
    
    @PreDestroy
    public void dispose() {
        logger.info("clear Falcon global variable");
        globalVariables.clear();
    }
    
    @Synchronized
    public void save(Map<String, Object> ev) {
        logger.info("save Falcon global variable, ev= {}", ev);
        var existGvMap = getGlobalVariables().stream()
                .collect(Collectors.toMap(FalconGlobalVariable::getName, Function.identity()));
        for (var e : ev.entrySet()) {
            if (!existGvMap.containsKey(e.getKey()))
                throw new BzError(dict.lo("LackFalconGlobalVariableName", e.getKey()));
            existGvMap.get(e.getKey()).value = e.getValue();
        }
        var rList = existGvMap.values().stream().map(this::toRecord).collect(Collectors.toCollection(ArrayList::new));
        persistGlobalVariables(rList);
        
        reloadGlobalVariables();
    }
    
    
    @Synchronized
    public void config(List<FalconGlobalVariable> list) {
        logger.info("config Falcon global variable, list= {}", list);
        // name 不能重复
        var cm = list.stream().collect(Collectors.groupingBy(it -> it.name, Collectors.counting()));
        for (var t : cm.entrySet()) {
            if (t.getValue() > 1) throw new BzError(dict.lo("DuplicateFalconGlobalVariableName", t.getKey()));
        }
        
        var existGvMap = getGlobalVariables().stream()
                .collect(Collectors.toMap(FalconGlobalVariable::getName, Function.identity()));
        for (var fv : list) {
            if (existGvMap.containsKey(fv.name)) {
                fv.value = existGvMap.get(fv.name).value;
            }
        }
        var rList = list.stream().map(this::toRecord).collect(Collectors.toCollection(ArrayList::new));
        persistGlobalVariables(rList);
        
        reloadGlobalVariables();
    }
    
    public List<FalconGlobalVariable> getGlobalVariables() {
        var existGvList = entityReader.findMany(NsFalconGlobalVariable.class, all());
        return existGvList.stream().map(this::parseRecord).collect(Collectors.toCollection(ArrayList::new));
    }
    
    private void persistGlobalVariables(List<NsFalconGlobalVariable> list) {
        entityWriter.deleteMany(all(), NsFalconGlobalVariable.class.getSimpleName());
        entityWriter.createMany(list);
    }
    
    private FalconGlobalVariable parseRecord(NsFalconGlobalVariable record) {
        var r = new FalconGlobalVariable();
        r = JsonHelper.parseStringToClass(record.getDef(), FalconGlobalVariable.class);
        return r;
    }
    
    private NsFalconGlobalVariable toRecord(FalconGlobalVariable gv) {
        var r = new NsFalconGlobalVariable();
        r.setName(gv.name);
        r.setDef(JsonHelper.writeValueAsString(gv));
        return r;
    }
}
