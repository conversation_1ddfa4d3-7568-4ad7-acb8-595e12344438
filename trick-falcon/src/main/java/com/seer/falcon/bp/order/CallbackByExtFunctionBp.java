package com.seer.falcon.bp.order;

import com.seer.core.helper.IdHelper;
import com.seer.core.schedule.DefaultExecutor;
import com.seer.falcon.BlockProcessor;
import com.seer.falcon.BlockRuntime;
import com.seer.falcon.BlockStatus;
import com.seer.falcon.TaskLogLevel;
import com.seer.falcon.domain.def.BlockDef;
import com.seer.falcon.domain.def.BlockInputParamDef;
import com.seer.falcon.domain.def.BlockOutputParamDef;
import com.seer.falcon.domain.def.BlockParamType;
import com.seer.falcon.recovery.FalconRecoveryService;
import com.seer.falcon.recovery.RecoveryAction;
import com.seer.falcon.recovery.RecoveryConfig;
import com.seer.falcon.service.FalconTaskService;
import com.seer.meta.service.NsDictManager;
import com.seer.wms.callback.service.CallbackService;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class CallbackByExtFunctionBp implements BlockProcessor {
    
    private final NsDictManager dict;
    private final CallbackService callBackService;
    private final FalconTaskService falconTaskService;
    private final FalconRecoveryService recoveryService;
    
    private final BlockDef def;
    
    private final String ipFunctions = "ipFunctions";
    private final String ipContext = "ipContext";
    private final String ipBlockUntilFinish = "ipBlockUntilFinish";
    
    private final String opChainId = "opChainId";
    
    public CallbackByExtFunctionBp(NsDictManager dict, CallbackService callBackService,
                                   FalconTaskService falconTaskService, FalconRecoveryService recoveryService) {
        this.dict = dict;
        this.callBackService = callBackService;
        this.falconTaskService = falconTaskService;
        this.def = new BlockDef(CallbackByExtFunctionBp.class.getSimpleName(),
                dict.lo(CallbackByExtFunctionBp.class.getSimpleName()));
        this.recoveryService = recoveryService;
        this.def.inputParams = List.of(
                new BlockInputParamDef(ipFunctions, BlockParamType.String, dict.lo("ExtFunction"), true, "", ""),
                new BlockInputParamDef(ipContext, BlockParamType.Any, dict.lo("Context"), false, "", "{}"),
                new BlockInputParamDef(ipBlockUntilFinish, BlockParamType.Boolean, dict.lo("BlockUntilFinish"), false,
                        "", false));
        this.def.outputParams = Map.of(opChainId,
                new BlockOutputParamDef(BlockParamType.String, dict.lo("CallbackChainId")));
    }
    
    @Override
    public BlockDef getDef() {
        return def;
    }
    
    
    @Override
    public void process(BlockRuntime blockRuntime) {
        if (blockRuntime.status >= BlockStatus.Finished) return;
        
        var functions = falconTaskService.getBlockInputParam(blockRuntime, ipFunctions).toString();
        @SuppressWarnings("unchecked") Map<String, Object> context = (Map<String, Object>) falconTaskService.getBlockInputParam(
                blockRuntime, ipContext);
        var blockUntilFinish = (Boolean) falconTaskService.getBlockInputParam(blockRuntime, ipBlockUntilFinish);
        
        var chainId = IdHelper.objectIdStr();
        var funcList = List.of(functions.split(","));
        var errMsg = "";
        // TODO 监控回调链状态，如果被定时器重试成功了，需要消除错误信息
        recoveryService.wrap(blockRuntime,
                new RecoveryConfig(errMsg, List.of(new RecoveryAction(dict.lo("ManualRetry"), true, () -> {}) {})),
                () -> {
                    if (BooleanUtils.isTrue(blockUntilFinish)) {
                        extracted(blockRuntime, context, funcList, chainId);
                    } else {
                        DefaultExecutor.bgCacheExecutor.submit(
                                () -> extracted(blockRuntime, context, funcList, chainId));
                    }
                });
        
        falconTaskService.setBlockOutputParams(blockRuntime, Map.of(opChainId, chainId));
    }
    
    private void extracted(BlockRuntime blockRuntime, Map<String, Object> context, List<String> funcList,
                           String chainId) {
        falconTaskService.logTask(blockRuntime.getTopTaskRuntime(),
                falconTaskService.buildBlockLogHead(blockRuntime) + " callback chainId = [" + chainId + "]",
                TaskLogLevel.Important);
        callBackService.handleChain(chainId, funcList, context);
    }
    
}
