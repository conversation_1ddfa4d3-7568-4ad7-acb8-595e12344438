package com.seer.falcon.bp.basic;

import com.seer.core.cm.*;
import com.seer.entity.NsMessageConfirmRecord;
import com.seer.entity.NsWebInformMessage;
import com.seer.falcon.BlockProcessor;
import com.seer.falcon.BlockRuntime;
import com.seer.falcon.BlockStatus;
import com.seer.falcon.domain.def.BlockDef;
import com.seer.falcon.domain.def.BlockInputParamDef;
import com.seer.falcon.domain.def.BlockOutputParamDef;
import com.seer.falcon.domain.def.BlockParamType;
import com.seer.falcon.service.FalconTaskService;
import com.seer.meta.domain.NsComplexQuery;
import com.seer.meta.service.NsDictManager;
import com.seer.meta.service.NsEntityReader;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class ManualDecideBp implements BlockProcessor {
    
    private final String message = "message";
    //private final String type = "type";
    private final String btnOption = "btnOption";
    private final String agents = "agents";
    private final String manualDecide = "manualDecide";
    
    
    private final FalconTaskService taskService;
    private final BlockDef def;
    private final NsDictManager dict;
    private final NsAppMessageService appMessageService;
    private final NsEntityReader reader;
    
    public ManualDecideBp(FalconTaskService taskService, NsAppMessageService appMessageService, NsDictManager dict,
                          NsAppMessageService appMessageService1, NsEntityReader reader) {
        this.taskService = taskService;
        this.appMessageService = appMessageService1;
        this.reader = reader;
        this.def = new BlockDef(ManualDecideBp.class.getSimpleName(), "ManualDecideBp");
        this.dict = dict;
        this.def.inputParams = List.of(
                new BlockInputParamDef(message, BlockParamType.String, "Message", true).setDefaultValue(""),
                // new BlockInputParamDef(type, BlockParamType.String, "Type", true).setDefaultValue(""),
                new BlockInputParamDef(btnOption, BlockParamType.String, "ButtonType", true).setDefaultValue(""),
                new BlockInputParamDef(agents, BlockParamType.String, "AssignDevice", false).setDefaultValue(""));
        this.def.outputParams = Map.of(manualDecide, new BlockOutputParamDef(BlockParamType.String, "ManualDecideBp"));
    }
    
    @Override
    public BlockDef getDef() {
        return def;
    }
    
    @Override
    public void process(BlockRuntime blockRuntime) {
        if (blockRuntime.status >= BlockStatus.Finished) return;
        
        var appMsg = new NsAppMessageDef();
        var id = blockRuntime.getTopTaskId();
        var msg = taskService.getBlockInputParam(blockRuntime, message).toString();
        // 当前只支持一种，就是消息的按钮选项
        //var decideType = taskService.getBlockInputParam(blockRuntime, type).toString();
        var Option = taskService.getBlockInputParam(blockRuntime, btnOption).toString();
        var device = taskService.getBlockInputParam(blockRuntime, agents).toString();
        appMsg.title = id;
        appMsg.body = msg;
        appMsg.targetType = NsAppMessageTargetType.Agents;
        appMsg.buttonType = NsAppMessageButtonType.Buttons;
        appMsg.buttons = List.of(Option.split(","));
        appMsg.targetInfo = List.of(device.split(","));
        
        appMessageService.sendMessage(appMsg);
        var index = 0;
        while (true) {
            try {
                var webInfo = reader.findOne(NsWebInformMessage.class, NsComplexQuery.eq("title", id));
                if (webInfo.getState().equals(NsAPPMessageState.done.toString())) {
                    var confirm = reader.findOne(NsMessageConfirmRecord.class,
                            NsComplexQuery.eq("messageId", webInfo.getId()));
                    index = Integer.parseInt(confirm.getButtonIndex());
                    break;
                }
                Thread.sleep(1000);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        taskService.setBlockOutputParams(blockRuntime, Map.of(manualDecide, appMsg.buttons.get(index)));
    }
    
}

