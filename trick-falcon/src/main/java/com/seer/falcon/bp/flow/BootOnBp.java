package com.seer.falcon.bp.flow;

import com.seer.core.helper.DateHelper;
import com.seer.falcon.BlockProcessor;
import com.seer.falcon.BlockRuntime;
import com.seer.falcon.BlockStatus;
import com.seer.falcon.TaskLogLevel;
import com.seer.falcon.domain.def.BlockChildDef;
import com.seer.falcon.domain.def.BlockDef;
import com.seer.falcon.domain.def.BlockInputParamDef;
import com.seer.falcon.domain.def.BlockParamType;
import com.seer.falcon.service.FalconTaskService;
import com.seer.meta.domain.BzError;
import com.seer.meta.service.NsDictManager;
import lombok.SneakyThrows;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Service
public class BootOnBp implements BlockProcessor {

    private final Logger logger = LoggerFactory.getLogger(BootOnBp.class);

    private final FalconTaskService taskService;

    private final String ipTimestamp = "timestamp";
    private final String childDefault = "default";

    private final BlockDef def;

    private final NsDictManager dict;

    public BootOnBp(NsDictManager dict, FalconTaskService taskService) {
        this.taskService = taskService;
        this.dict = dict;
        this.def = new BlockDef(BootOnBp.class.getSimpleName(), "BootOnBp");
        this.def.inputParams = List.of(
                new BlockInputParamDef(ipTimestamp, BlockParamType.String, "IpTimestamp", true)
        );
        this.def.children = Map.of(
                childDefault, new BlockChildDef("Default")
        );
    }

    @Override
    public BlockDef getDef() {
        return def;
    }

    @SneakyThrows
    @Override
    public void process(BlockRuntime blockRuntime) {
        if (blockRuntime.status >= BlockStatus.Finished) return;

        Object timestampStr = taskService.getBlockInputParam(blockRuntime, ipTimestamp, null);
        taskService.logTask(blockRuntime.getTopTaskRuntime(), dict.lo("BootOn", timestampStr), TaskLogLevel.Normal);
        Date timestamp = null;
        if (!(timestampStr == null || (timestampStr instanceof String && ((String) timestampStr).isBlank()))) {
            timestamp = DateHelper.anyToDate(timestampStr);
            if (timestamp == null) throw BzError.invalidDate(timestampStr);
        }

        if (timestamp != null) {
            var t = timestamp;
            var scheduledFuture = taskService.scheduledExecutors.scheduleAtFixedRate(() -> {
                if (blockRuntime.getTopTaskRuntime().isTaskAborted()) throw new ScheduleDone();
                var now = new Date();
                if (now.compareTo(t) > 0) {
                    logger.info(dict.lo("BootOnLog"), timestampStr, t, now);
                    throw new ScheduleDone();
                }
            }, 0, 1, TimeUnit.SECONDS);

            try {
                scheduledFuture.get();
            } catch (ExecutionException e) {
                if (!(e.getCause() instanceof ScheduleDone)) throw e.getCause() != null ? e.getCause() : e;
            } catch (InterruptedException e) {
                return;
            }
        }

        var children = blockRuntime.blockConfig.children.get(childDefault);
        if (children == null) throw BzError.falconNoChildBlock(childDefault);
        // 注意，块上下文不变
        taskService.serialProcessChildren(blockRuntime, children, childDefault, blockRuntime.blockContext);
    }

}