package com.seer.falcon.bp;

import com.seer.core.ext.ExtOutgoingCenter;
import com.seer.falcon.BlockProcessor;
import com.seer.falcon.BlockRuntime;
import com.seer.falcon.domain.def.BlockDef;
import com.seer.falcon.service.FalconTaskService;
import com.seer.meta.domain.BzError;
import com.seer.meta.service.NsDictManager;
import com.seer.meta.service.NsEntityReader;
import com.seer.meta.service.NsEntityWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class WrappedExtBp implements BlockProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(WrappedExtBp.class);
    
    public static final String extBlockNamePrefix = "WrappedExt::";
    
    private final String childDefault = "default";
    
    private final BlockDef def;
    
    
    private final FalconTaskService taskService;
    private final ExtOutgoingCenter outgoingCenter;
    private final NsEntityReader entityReader;
    private final NsEntityWriter entityWriter;
    private final NsDictManager dictManager;
    
    public WrappedExtBp(FalconTaskService taskService, ExtOutgoingCenter outgoingCenter, NsEntityReader entityReader,
                        NsEntityWriter entityWriter, NsDictManager dictManager) {
        this.taskService = taskService;
        this.outgoingCenter = outgoingCenter;
        this.entityReader = entityReader;
        this.entityWriter = entityWriter;
        this.dictManager = dictManager;
        
        this.def = new BlockDef(WrappedExtBp.class.getSimpleName(), WrappedExtBp.class.getSimpleName());
    }
    
    
    @Override
    public BlockDef getDef() {
        return def;
    }
    
    @Override
    public void process(BlockRuntime blockRuntime) {
        
        var allInputParams = taskService.collectAllBlockInputParams(blockRuntime);
        var blockDef = taskService.getExtBlockDef(blockRuntime.blockConfig.blockType);
        
        // 前置流程
        {
            Map<String, Object> r;
            try {
                r = (Map<String, Object>) outgoingCenter.callFunction(blockDef.startExtFunction,
                        Map.of("inputParams", allInputParams, "internalVariables", blockRuntime.internalVariables,
                                "contextVariables", blockRuntime.blockContext.ctxVariables), true);
            } catch (ClassCastException e) {
                logger.error("ExtBp: Call Ext Function catch ClassCastException");
                logger.error(e.getMessage());
                throw new BzError("ClassCastException: " + e.getMessage(), e);
            }
            
            // 内部变量在扩展函数内自行持久化
            
            if (r != null) {
                Map<String, Object> outputParams = (Map<String, Object>) r.get("outputParams");
                Map<String, Object> ctxVariables = (Map<String, Object>) r.get("ctxVariables");
                if (outputParams != null) {
                    taskService.setBlockOutputParams(blockRuntime, outputParams);
                    blockRuntime.blockContext.addOutputParamsToExpressionCtx(blockRuntime);
                }
                if (ctxVariables != null) {
                    blockRuntime.blockContext.setContextVariables(blockRuntime, ctxVariables);
                }
            }
        }
        
        // 依次执行子块
        var children = blockRuntime.blockConfig.children.get(childDefault);
        if (children == null) return;
        taskService.serialProcessChildren(blockRuntime, children, "default", blockRuntime.blockContext);
        
        // 后置流程
        {
            Map<String, Object> r;
            try {
                r = (Map<String, Object>) outgoingCenter.callFunction(blockDef.endExtFunction,
                        Map.of("inputParams", allInputParams, "internalVariables", blockRuntime.internalVariables,
                                "contextVariables", blockRuntime.blockContext.ctxVariables), true);
            } catch (ClassCastException e) {
                logger.error("ExtBp: Call Ext Function catch ClassCastException");
                logger.error(e.getMessage());
                throw new BzError("ClassCastException: " + e.getMessage(), e);
            }
            
            // 内部变量在扩展函数内自行持久化
            
            if (r != null) {
                Map<String, Object> outputParams = (Map<String, Object>) r.get("outputParams");
                Map<String, Object> ctxVariables = (Map<String, Object>) r.get("ctxVariables");
                if (outputParams != null) {
                    taskService.setBlockOutputParams(blockRuntime, outputParams);
                    blockRuntime.blockContext.addOutputParamsToExpressionCtx(blockRuntime);
                }
                if (ctxVariables != null) {
                    blockRuntime.blockContext.setContextVariables(blockRuntime, ctxVariables);
                }
            }
        }
    }
}
