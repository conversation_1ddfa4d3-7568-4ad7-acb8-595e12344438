package com.seer.falcon.bp.basic;

import com.seer.core.cm.NsAppMessageButtonType;
import com.seer.core.cm.NsAppMessageDef;
import com.seer.core.cm.NsAppMessageService;
import com.seer.core.cm.NsAppMessageTargetType;
import com.seer.falcon.BlockProcessor;
import com.seer.falcon.BlockRuntime;
import com.seer.falcon.domain.def.BlockDef;
import com.seer.falcon.domain.def.BlockInputParamDef;
import com.seer.falcon.domain.def.BlockParamType;
import com.seer.falcon.service.FalconTaskService;
import com.seer.meta.domain.BzError;
import com.seer.meta.domain.OptionBillHelper;
import com.seer.meta.service.NsDictManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AppMessageBp implements BlockProcessor {

    private final String msgTitle = "msgTitle";
    private final String msgBody = "msgBody";
    private final String msgTarget = "msgTarget";
    private final String msgUser = "msgUser";
    //通知显示的按钮
    private final String msgButtons = "msgButtons";
    // 显示的按钮，不填默认为空
    private final String msgAcceptInfo = "msgAcceptInfo";


    private final FalconTaskService taskService;
    private final NsAppMessageService appMessageService;
    private final BlockDef def;
    private final NsDictManager dict;

    public AppMessageBp(FalconTaskService taskService, NsAppMessageService appMessageService, NsDictManager dict) {
        var list = OptionBillHelper.paramOptionList("NsAppMessageTargetType");
        this.taskService = taskService;
        this.appMessageService = appMessageService;
        this.def = new BlockDef(AppMessageBp.class.getSimpleName(),  "AppMessageBp");
        this.dict = dict;
        this.def.inputParams = List.of(
                new BlockInputParamDef(msgTitle, BlockParamType.String, "Title", true).setDefaultValue(""),
                new BlockInputParamDef(msgBody, BlockParamType.Any, "Content", true).setDefaultValue(""),
                new BlockInputParamDef(msgUser, BlockParamType.Any, "TargetType", true,"","AllUser",list),
                new BlockInputParamDef(msgTarget, BlockParamType.Any, "TargetUser", false).setDefaultValue(""),
                new BlockInputParamDef(msgButtons, BlockParamType.Any, "ExecuteButton", false).setDefaultValue(""),
                new BlockInputParamDef(msgAcceptInfo, BlockParamType.Any, "AcceptInfo", false).setDefaultValue("")
        );
    }

    @Override
    public BlockDef getDef() {
        return def;
    }

    @Override
    public void process(BlockRuntime blockRuntime) {
        // TODO
        var appMessageDef = new NsAppMessageDef();
        appMessageDef.title = taskService.getBlockInputParam(blockRuntime, msgTitle).toString();
        appMessageDef.body = taskService.getBlockInputParam(blockRuntime, msgBody).toString();

        appMessageDef.targetType = NsAppMessageTargetType.valueOf(
                taskService.getBlockInputParam(
                        blockRuntime,
                        msgUser
                ).toString()
        );
        // 如果目标不是全部用户，则传需要通知的String=“aaa,bbb”
        if (appMessageDef.targetType != NsAppMessageTargetType.AllUser) {
            var info = taskService.getBlockInputParam(blockRuntime, msgTarget).toString();
            appMessageDef.targetInfo = List.of(info.split(","));
        }
        var button = taskService.getBlockInputParam(blockRuntime, msgButtons);
        if (StringUtils.isNotBlank((CharSequence) button)) {
            appMessageDef.buttonType = NsAppMessageButtonType.Buttons;
            appMessageDef.buttons = List.of(button.toString().split(","));
        }
        var acceptInfo = taskService.getBlockInputParam(blockRuntime, msgAcceptInfo);
        if (StringUtils.isNotBlank((CharSequence) acceptInfo)) {
            appMessageDef.acceptInfo = List.of(acceptInfo.toString().split(","));
        }
        if (appMessageDef.buttons.size() != appMessageDef.acceptInfo.size()) {
            throw new BzError(dict.lo("ButtonNeqFun", appMessageDef.buttons.size(), appMessageDef.acceptInfo.size()));
        }
        appMessageService.sendMessage(appMessageDef);

    }
}
