package com.seer.falcon.bp.basic;

import com.seer.falcon.BlockProcessor;
import com.seer.falcon.BlockRuntime;
import com.seer.falcon.domain.def.BlockDef;

public class MailBp implements BlockProcessor {

    @Override
    public BlockDef getDef() {
        return null;
    }

    @Override
    public void process(BlockRuntime blockRuntime) {

    }

}


// private val ipFromAddress = "fromAddress"
//    private val ipToAddresses = "toAddresses"
//    private val ipSubject = "subject"
//    private val ipContent = "content"
//
//    override val def = BlockDef(
//        MailBp::class.simpleName!!, dict.lo("MailBp"), inputParams = listOf(
//            BlockInputParamDef(ipFromAddress, BlockParamType.String, dict.lo("FromAddress"), required = true),
//            BlockInputParamDef(
//                ipToAddresses, BlockParamType.String, dict.lo("ToAddress"), required = true, description = dict.lo("SplitByComma")
//            ),
//            BlockInputParamDef(ipSubject, BlockParamType.String, dict.lo("Subject"), required = true),
//            BlockInputParamDef(ipContent, BlockParamType.String, dict.lo("Content"), required = true),
//        )
//    )
//
//    override fun process(blockRuntime: BlockRuntime) {
//        val fromAddress =
//            taskService.getBlockInputParam(blockRuntime, ipFromAddress) as String? ?: throw ObservableError(dict.lo("LackFromAddress"))
//        val toAddressesStr =
//            taskService.getBlockInputParam(blockRuntime, ipToAddresses) as String? ?: throw ObservableError(dict.lo("LackToAddress"))
//        val toAddresses = splitTrim(toAddressesStr, ",")
//        val subject =
//            taskService.getBlockInputParam(blockRuntime, ipSubject) as String? ?: throw ObservableError(dict.lo("LackSubject"))
//        val content =
//            taskService.getBlockInputParam(blockRuntime, ipContent) as String? ?: throw ObservableError(dict.lo("LackContent"))
//
//        // MailHelper.sendSimple(fromAddress, toAddresses, subject, content)
//    }