package com.seer.falcon.bp;

import com.seer.falcon.BlockProcessor;
import com.seer.falcon.BlockRuntime;
import com.seer.falcon.BlockStatus;
import com.seer.falcon.domain.def.BlockDef;
import com.seer.falcon.recovery.FalconRecoveryService;
import com.seer.falcon.recovery.RecoveryAction;
import com.seer.falcon.recovery.RecoveryConfig;
import com.seer.falcon.service.FalconTaskService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

public class ExtBp implements BlockProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(ExtBp.class);
    
    private final FalconTaskService taskService;
    
    private final FalconRecoveryService recoveryService;
    
    private final ExtOutgoingCenter outgoingCenter;
    
    private final NsDictManager dict;
    
    private final String childDefault = "default";
    
    private final BlockDef def;
    
    public static final String extBlockNamePrefix = "Ext::";
    
    public ExtBp(FalconTaskService taskService, FalconRecoveryService recoveryService, ExtOutgoingCenter outgoingCenter,
                 NsDictManager dict) {
        this.taskService = taskService;
        this.recoveryService = recoveryService;
        this.outgoingCenter = outgoingCenter;
        this.dict = dict;
        
        this.def = new BlockDef(ExtBp.class.getSimpleName(), "ExtBp");
    }
    
    @Override
    public BlockDef getDef() {
        return def;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    public void process(BlockRuntime blockRuntime) {
        if (blockRuntime.status >= BlockStatus.Finished) return;
        
        String recoveryMsg = dict.lo("ExtException", blockRuntime.blockConfig.blockType);
        recoveryService.wrap(blockRuntime,
                new RecoveryConfig(recoveryMsg, List.of(new RecoveryAction(dict.lo("Retry"), () -> {
                }))), () -> {
                    var blockDef = taskService.getExtBlockDef(blockRuntime.blockConfig.blockType);
                    
                    if (StringUtils.isBlank(blockDef.extFunction)) throw BzError.noExtFunction();
                    
                    var allInputParams = taskService.collectAllBlockInputParams(blockRuntime);
                    
                    Map<String, Object> r;
                    try {
                        r = (Map<String, Object>) outgoingCenter.callFunction(blockDef.extFunction,
                                Map.of("inputParams", allInputParams, "internalVariables",
                                        blockRuntime.internalVariables, "contextVariables",
                                        blockRuntime.blockContext.ctxVariables), true);
                    } catch (ClassCastException e) {
                        logger.error("ExtBp: Call Ext Function catch ClassCastException");
                        logger.error(e.getMessage());
                        throw new BzError("ClassCastException: " + e.getMessage(), e);
                    }
                    
                    // 内部变量在扩展函数内自行持久化
                    
                    if (r != null) {
                        Map<String, Object> outputParams = (Map<String, Object>) r.get("outputParams");
                        Map<String, Object> ctxVariables = (Map<String, Object>) r.get("ctxVariables");
                        if (outputParams != null) {
                            taskService.setBlockOutputParams(blockRuntime, outputParams);
                        }
                        if (ctxVariables != null) {
                            blockRuntime.blockContext.setContextVariables(blockRuntime, ctxVariables);
                        }
                    }
                });
    }
    
}