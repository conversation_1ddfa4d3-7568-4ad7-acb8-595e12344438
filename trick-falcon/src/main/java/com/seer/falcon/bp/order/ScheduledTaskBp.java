package com.seer.falcon.bp.order;

import com.seer.core.service.NsIdGenManager;
import com.seer.entity.NsScheduledTask;
import com.seer.falcon.BlockProcessor;
import com.seer.falcon.BlockRuntime;
import com.seer.falcon.TaskLogLevel;
import com.seer.falcon.domain.def.BlockDef;
import com.seer.falcon.domain.def.BlockInputParamDef;
import com.seer.falcon.domain.def.BlockParamType;
import com.seer.falcon.service.FalconTaskService;
import com.seer.meta.domain.OptionBillHelper;
import com.seer.meta.service.NsDictManager;
import com.seer.meta.service.NsEntityWriter;
import com.seer.wms.scheduledtask.service.ScheduledTaskService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class ScheduledTaskBp implements BlockProcessor {
    
    private final NsDictManager dict;
    private final FalconTaskService falconTaskService;
    private final NsEntityWriter entityWriter;
    private final NsIdGenManager idGenManager;
    private final ScheduledTaskService scheduledTask;
    private final FalconTaskService taskService;
    private final BlockDef def;
    
    private final String ipCategory = "ipCategory";
    private final String ipSourceOrder = "ipSourceOrder";
    private final String ipParams = "ipParams";
    private final String ipScriptFunc = "ipScriptFunc";
    private final String ipScheduledMethod = "ipScheduledMethod";
    private final String IpRetryDelay = "IpRetryDelay";
    
    
    public ScheduledTaskBp(NsDictManager dict, FalconTaskService falconTaskService, NsEntityWriter entityWriter,
                           NsIdGenManager idGenManager, ScheduledTaskService scheduledTask,
                           FalconTaskService taskService) {
        this.entityWriter = entityWriter;
        this.idGenManager = idGenManager;
        this.scheduledTask = scheduledTask;
        this.taskService = taskService;
        var list = OptionBillHelper.paramOptionList("ScheduledType");
        this.dict = dict;
        this.falconTaskService = falconTaskService;
        this.def = new BlockDef(ScheduledTaskBp.class.getSimpleName(), dict.lo(ScheduledTaskBp.class.getSimpleName()));
        this.def.inputParams = List.of(
                new BlockInputParamDef(ipCategory, BlockParamType.String, dict.lo("Category"), false, "", ""),
                new BlockInputParamDef(ipSourceOrder, BlockParamType.String, dict.lo("sourceOrder"), true, "", ""),
                new BlockInputParamDef(ipParams, BlockParamType.JSONObject, dict.lo("InputParameters"), true, "", "{}"),
                new BlockInputParamDef(ipScriptFunc, BlockParamType.String, dict.lo("FunctionName"), true, "", ""),
                new BlockInputParamDef(ipScheduledMethod, BlockParamType.String, dict.lo("ScheduledMethod"), true, "",
                        "", list),
                new BlockInputParamDef(IpRetryDelay, BlockParamType.Long, dict.lo("RetryDelay"), true, "", 30L));
    }
    
    @Override
    public BlockDef getDef() {
        return def;
    }
    
    
    @Override
    public void process(BlockRuntime blockRuntime) {
        var task = new NsScheduledTask();
        task.setId(idGenManager.genId(NsScheduledTask.class.getSimpleName()));
        task.setCategory(falconTaskService.getBlockInputParam(blockRuntime, ipCategory).toString());
        task.setSourceOrder(falconTaskService.getBlockInputParam(blockRuntime, ipSourceOrder).toString());
        task.setParams(falconTaskService.getBlockInputParam(blockRuntime, ipParams).toString());
        task.setScriptFunc(falconTaskService.getBlockInputParam(blockRuntime, ipScriptFunc).toString());
        task.setScheduledMethod(falconTaskService.getBlockInputParam(blockRuntime, ipScheduledMethod).toString());
        task.setRetryDelay(
                Integer.valueOf(falconTaskService.getBlockInputParam(blockRuntime, IpRetryDelay).toString()));
        task.setState(50);
        task.setCreatedOn(new Date());
        task.getTryCount(0);
        entityWriter.createOne(task);
        taskService.logTask(blockRuntime.getTopTaskRuntime(), dict.lo("ScheduledTaskBp")+task.getId(),
                TaskLogLevel.Normal);
        scheduledTask.reStartTake(task);
    }
    
}

