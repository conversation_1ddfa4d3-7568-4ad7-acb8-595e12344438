package com.seer.falcon.controller;

import com.seer.core.helper.JsonHelper;
import com.seer.core.init.ShadowConfig;
import com.seer.core.service.NsIdGenManager;
import com.seer.entity.NsFalconBlockRecord;
import com.seer.entity.NsFalconTaskRecord;
import com.seer.entity.NsHumanUser;
import com.seer.entity.NsTaskAbortInitData;
import com.seer.falcon.BlockRegisters;
import com.seer.falcon.CreateTaskReq;
import com.seer.falcon.TaskStatus;
import com.seer.falcon.domain.def.*;
import com.seer.falcon.recovery.FailureView;
import com.seer.falcon.recovery.FalconRecoveryService;
import com.seer.falcon.service.FalconGlobalVariablesService;
import com.seer.falcon.service.FalconTaskDefService;
import com.seer.falcon.service.FalconTaskService;
import com.seer.meta.controller.domain.UpdateResult;
import com.seer.meta.domain.BzError;
import com.seer.meta.service.NsDictManager;
import com.seer.meta.service.NsEntityReader;
import com.seer.meta.service.NsEntityWriter;
import com.seer.web.SecurityFilter;
import com.seer.wms.service.NsBusinessManager;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.seer.meta.domain.NsComplexQuery.eq;

@RestController
@RequestMapping("api/falcon")
public class FalconController {
    
    private static final Logger logger = LoggerFactory.getLogger(FalconController.class);
    
    private final ShadowConfig shadowConfig;
    private final FalconTaskDefService defService;
    private final FalconTaskService taskService;
    private final FalconRecoveryService recoveryService;
    private final FalconGlobalVariablesService globalVariablesService;
    private final BlockRegisters blockRegisters;
    private final NsDictManager dict;
    private final NsEntityReader readService;
    private final NsEntityWriter writeService;
    private final NsIdGenManager idGenManager;
    
    public FalconController(ShadowConfig shadowConfig, FalconTaskDefService defService, FalconTaskService taskService,
                            FalconRecoveryService recoveryService, FalconGlobalVariablesService globalVariablesService,
                            BlockRegisters blockRegisters, NsDictManager dict, NsEntityReader readService,
                            NsEntityWriter writeService, NsIdGenManager idGenManager) {
        this.shadowConfig = shadowConfig;
        this.defService = defService;
        this.taskService = taskService;
        this.recoveryService = recoveryService;
        this.globalVariablesService = globalVariablesService;
        this.blockRegisters = blockRegisters;
        this.dict = dict;
        this.readService = readService;
        this.writeService = writeService;
        this.idGenManager = idGenManager;
    }
    
    @PostMapping("task-def/create-for-order")
    public CreateTaskDefForOrderResult createTaskDefForOrder(@RequestBody CreateTaskDefForOrder req) {
        var defId = defService.createTaskDefForOrder(req.orderType, req.defLabel);
        return new CreateTaskDefForOrderResult(defId, req.defLabel);
    }
    
    @GetMapping("blocks")
    public List<BlockDefGroup> listBlockGroups(HttpServletRequest request) {
        return blockRegisters.i18nGroup();
    }
    
    @PostMapping("ext-blocks")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void addExtBlockDefs(@RequestBody List<BlockDef> blockDefs) {
        blockRegisters.addExtBlockDefs(blockDefs);
    }
    
    @PostMapping("task/abort")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void abortTasks(@RequestParam("ids") String idsStr) {
        var ids = idsStr.split(",");
        for (var id : ids) {
            // 防呆
            var record = readService.findOneById(NsFalconTaskRecord.class, id);
            if (record.getStatusOrZero() == TaskStatus.Finished)
                throw new BzError(dict.lo("不能终止已完成的猎鹰任务，%s", id));
            else if (record.getStatusOrZero() == TaskStatus.Aborted)
                throw new BzError(dict.lo("不能终止已经终止的猎鹰任务，%s", id));
            
            try {
                taskService.abortTask(id, dict.lo("TaskAbort"));
            } catch (Exception e) {
                logger.error("Failed to abort task {}", id, e);
            }
        }
    }
    
    
    @PostMapping("task/pause")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void pauseTasks(@RequestParam("ids") String idsStr) {
        var ids = idsStr.split(",");
        StringBuilder errorMessage = new StringBuilder();
        for (var id : ids) {
            // 防呆
            var record = readService.findOneById(NsFalconTaskRecord.class, id);
            if (record.getPausedOrFalse()) throw new BzError(dict.lo("不能暂停已暂停的猎鹰任务，%s", id));
            if (record.getStandbyOrFalse()) throw new BzError(dict.lo("不能暂停已挂起的猎鹰任务，%s", id));
            if (record.getStatusOrZero() != TaskStatus.Started)
                throw new BzError(dict.lo("不能暂停非已启动(Started)的猎鹰任务, %s", id));
            
            try {
                taskService.pauseTask(id, dict.lo("UserOrExt"));
            } catch (Exception e) {
                logger.error(dict.lo("PauseTaskException"), id, e);
                errorMessage.append(dict.lo("PauseTaskException2", id)).append(e.getMessage());
            }
        }
        String errMsg = errorMessage.toString();
        if (!errMsg.isBlank()) throw new BzError(errMsg);
    }
    
    @PostMapping("task/resume")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void resumeTasks(@RequestParam("ids") String idsStr) {
        var ids = idsStr.split(",");
        for (var id : ids) {
            // 防呆
            var record = readService.findOneById(NsFalconTaskRecord.class, id);
            if (!record.getPausedOrFalse()) throw new BzError(dict.lo("不能继续执行中的猎鹰任务，%s", id));
            if (record.getStandbyOrFalse()) throw new BzError(dict.lo("不能继续已挂起的猎鹰任务，%s", id));
            if (record.getStatusOrZero() != TaskStatus.Started)
                throw new BzError(dict.lo("不能继续非已启动(Started)的猎鹰任务, %s", id));
    
            try {
                taskService.resumeTask(id);
            } catch (Exception e) {
                logger.error(dict.lo("ResumeTaskException"), id, e);
            }
        }
    }
    
    @GetMapping("task/{id}/blocks")
    public List<NsFalconBlockRecord> getAllBlockStatesOfTask(@PathVariable("id") String taskId) {
        return readService.findMany(NsFalconBlockRecord.class, eq("taskId", taskId));
    }
    
    @PostMapping("task/clear-all")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void clearAllTasks() {
        writeService.deleteMany(null, "NsFalconTaskLog");
        writeService.deleteMany(null, "NsFalconBlockRecord");
        writeService.deleteMany(null, "NsFalconTaskRecord");
    }
    
    @PostMapping("task/re-run-standby")
    public Map<String, Integer> reRunStandBy() {
        var taskNum = taskService.reRunStandBy();
        return Map.of("taskNum", taskNum);
    }
    
    @PostMapping("task/re-run-from-error")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void reRunFromError(@RequestBody TaskIds req) {
        for (var id : req.ids) taskService.reRunFromError(id);
    }
    
    @GetMapping("failures")
    public List<FailureView> listFailures() {
        return recoveryService.listFailures();
    }
    
    @PostMapping("recover-failure")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void setRecoverAction(@RequestBody RecoverAction req) {
        recoveryService.setRecoverAction(req.id, req.userAction);
    }
    
    @PostMapping("failures/clear")
    public void clearFailures() {
        recoveryService.clearFailures();
    }
    
    @Deprecated
    @PostMapping("task/abort/init-data")
    public Map<String, Object> abortInitData(@RequestBody NsTaskAbortInitData req, HttpServletRequest request) {
        return recoveryContainerInv(req, request);
    }
    
    @PostMapping("task/recovery/recovery-convtainer-inv")
    public Map<String, Object> recoveryContainerInv(@RequestBody NsTaskAbortInitData req, HttpServletRequest request) {
        logger.info("abortInitData req=[{}]", JsonHelper.writeValueAsString(req));
        if (StringUtils.isBlank(req.getOrderIdOrEmpty())) throw new BzError("未输入猎鹰任务号");
        
        NsHumanUser user = (NsHumanUser) request.getAttribute(SecurityFilter.SIGN_IN_USER_ATTR);
        // TODO 校验猎鹰任务状态
        var id = StringUtils.isNotBlank(req.getIdOrEmpty()) ? req.getIdOrEmpty() :
                idGenManager.genId(NsTaskAbortInitData.class.getSimpleName());
        var abort = new NsTaskAbortInitData();
        abort.setId(id);
        abort.setCreatedOn(new Date());
        abort.setOrderId(req.getOrderIdOrEmpty());
        abort.setAbortModel(req.getAbortModelOrEmpty());
        abort.setOrderId(req.getOrderIdOrEmpty());
        abort.setContainerId(req.getContainerId(""));
        abort.setDeleteInv(req.getDeleteInvOrFalse());
        abort.setIpBinId(req.getIpBinId(""));
        abort.setState(req.getStateOrEmpty());
        writeService.createOne(abort);
        
        return Map.of("state", recoveryService.recoveryContainerAndInv(abort, user));
    }
    
    @GetMapping("task/recovery/list-recovery-block")
    public HashMap<String, BlockConfig> recoveryList(@RequestParam String taskId) {
        // 校验 1
        var taskRuntime = taskService.unfinishedTasks.get(taskId);
        if (taskRuntime == null) throw BzError.falconTaskNotRunning(taskId);
        
        // 校验 2
        taskService.checkDefBlocksExist(taskRuntime);
        
        return taskService.listManualRecoveryBlock(taskRuntime);
    }
    
    @PostMapping("task/recovery/recovery-block")
    public void recoveryBlocks(@RequestBody RecoveryBlockReq req) {
        // 校验 1
        var record = readService.findOneById(NsFalconTaskRecord.class, req.taskId);
        if (record == null) throw new BzError(dict.lo("未找到猎鹰任务记录，taskId=[{}]", req.taskId));
        
        // 校验 2
        var taskRuntime = taskService.unfinishedTasks.get(req.taskId);
        if (taskRuntime == null) throw BzError.falconTaskNotRunning(req.taskId);
        
        // 处理资源
        taskService.manualProcessBlock(req, taskRuntime);
    }
    
    @GetMapping("global-variables")
    public Map<String, Object> getGlobalVariables() {
        return globalVariablesService.globalVariables;
    }
    
    @PostMapping("global-variables/save")
    public UpdateResult save(@RequestBody Map<String, Object> ev) {
        globalVariablesService.save(ev);
        return new UpdateResult(ev.size());
    }
    
    @GetMapping("global-variables/config")
    public List<FalconGlobalVariable> getConfig() {
        return globalVariablesService.getGlobalVariables();
    }
    
    @PostMapping("global-variables/config")
    public UpdateResult config(@RequestBody List<FalconGlobalVariable> list) {
        globalVariablesService.config(list);
        return new UpdateResult(list.size());
    }
    
}
