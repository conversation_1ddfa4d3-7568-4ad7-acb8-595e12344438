package com.seer.falcon;

import com.seer.falcon.bp.*;
import com.seer.falcon.bp.basic.*;
import com.seer.falcon.bp.flow.*;
import com.seer.falcon.domain.def.BlockDef;
import com.seer.falcon.domain.def.BlockDefGroup;
import com.seer.falcon.error.NoBlockType;
import com.seer.trick.BzError;
import org.apache.commons.collections4.MapUtils;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

public class BlockRegisters {

    public final List<BlockDefGroup> groups = new CopyOnWriteArrayList<>();

    private final Map<String, BlockDef> blockDefMap = new ConcurrentHashMap<>();
    private final Map<String, BlockProcessor> blockProcessorMap = new ConcurrentHashMap<>();

    private final Map<String, BlockDef> extBlockDefMap = new ConcurrentHashMap<>();

    private final BlockDefGroup extGroup = new BlockDefGroup("BpExt", 0, new ArrayList<>());

    @PostConstruct
    public void init() {
        register(RootBp.class);

        register(SubTaskBp.class);
        register(ExtBp.class);
        register(WrappedExtBp.class);

        groups.add(extGroup);

        groups.add(new BlockDefGroup("TaskBpGroup", 0,
            List.of(register(TaskSyncBp.class), register(TaskAsyncBp.class)
                //register(SetTaskVariableBp.class),
                //register(AddTaskResourceBp.class),
                //register(TriggerTaskEventBp.class),
                //register(MarkTaskStartBp.class),
                //register(ScheduledTaskBp.class)))
            )));

        groups.add(new BlockDefGroup("FlowBpGroup", 0,
            List.of(register(SerialFlowBp.class),
                //register(ParallelFlowBp.class), register(IfOnlyBp.class),
                //register(IfBp.class), register(DoWhileBp.class), register(DelayBp.class),
                register(BootOnBp.class),
                //register(IterateListBp.class), register(ContainsBp.class),
                register(RepeatNumBp.class), register(TryCatchBp.class), register(ThrowBp.class)
                //register(LoopUntilWithDelayBp.class)
            )));

        //groups.add(new BlockDefGroup("QueryBpGroup", 0, List.of(register(ComplexQueryGeneralBp.class))));

        groups.add(new BlockDefGroup("BasicBpGroup", 0, List.of(register(PrintBp.class), register(TimestampBp.class),
            // register(MailBp.class),
            register(WebSocketBp.class), register(PadIntStrBp.class), register(AppMessageBp.class),
            register(ManualDecideBp.class), register(ExpressionBp.class))));
    }

    public List<BlockDefGroup> i18nGroup() {
        // TODO 需要运行时的 i18n 的块定义，不这么搞
        return groups;
    }

    public BlockDef register(BlockProcessor processor) {
        var def = processor.getDef();
        blockDefMap.put(def.name, def);
        blockProcessorMap.put(def.name, processor);
        return def;
    }

    public BlockDef getBlockDef(String blockType) {
        return blockDefMap.get(blockType);
    }

    public BlockProcessor getBlockProcessor(String blockType) {
        if (blockType.startsWith(SubTaskBp.blockTypeNamePrefix)) {
            return blockProcessorMap.get("SubTaskBp");
        } else if (blockType.startsWith(ExtBp.extBlockNamePrefix)) {
            return blockProcessorMap.get("ExtBp");
        } else if (blockType.startsWith(WrappedExtBp.extBlockNamePrefix)) {
            return blockProcessorMap.get("WrappedExtBp");
        }
        // TODO
        else {
            var bp = blockProcessorMap.get(blockType);
            if (bp == null) throw new NoBlockType(blockType);
            return bp;
        }
    }

    public void addExtBlockDefs(List<BlockDef> blockDefs) {
        for (var def : blockDefs) {
            if (!def.name.startsWith(ExtBp.extBlockNamePrefix) &&
                !def.name.startsWith(WrappedExtBp.extBlockNamePrefix)) {
                if (MapUtils.isEmpty(def.children)) def.name = ExtBp.extBlockNamePrefix + def.name;
                else def.name = WrappedExtBp.extBlockNamePrefix + def.name;
            }
            // TODO
            extBlockDefMap.put(def.name, def);
        }
        blockDefMap.putAll(extBlockDefMap);
        extGroup.blocks = new ArrayList<>(extBlockDefMap.values());
    }

    public BlockDef getExtBlockDef(String name) {
        var def = extBlockDefMap.get(name);
        if (def == null) throw new BzError(null, "NoExtBlockDefByName", name); //"找不到扩展块 " +
        return def;
    }

}
