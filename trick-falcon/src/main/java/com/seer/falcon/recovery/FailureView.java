package com.seer.falcon.recovery;

import java.util.Date;
import java.util.List;

public class FailureView {

    public String _id; // 故障 ID;
    public String taskId; // 相关猎鹰任务;
    public Date timestamp; // 发生时间;
    public String errorMessage; // 错误消息;
    public String actualError; // 错误详情;
    public List<String> buttons;  // 按钮标签;

    public FailureView(String _id, String taskId, Date timestamp, String errorMessage, String actualError, List<String> buttons) {
        this._id = _id;
        this.taskId = taskId;
        this.timestamp = timestamp;
        this.errorMessage = errorMessage;
        this.actualError = actualError;
        this.buttons = buttons;
    }

}
