package com.seer.falcon.recovery;

public class RecoveryAction {

    public String label;
    public boolean noRecovery;
    public Runnable fixer;

    public RecoveryAction(String label, boolean noRecovery, Runnable fixer) {
        this.label = label;
        this.noRecovery = noRecovery;
        this.fixer = fixer;
    }

    public RecoveryAction(String label, Runnable fixer) {
        this.label = label;
        this.noRecovery = false;
        this.fixer = fixer;
    }

}
