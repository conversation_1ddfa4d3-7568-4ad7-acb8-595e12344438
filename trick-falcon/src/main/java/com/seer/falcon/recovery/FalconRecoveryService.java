package com.seer.falcon.recovery;

import com.seer.core.helper.IdHelper;
import com.seer.core.helper.JsonHelper;
import com.seer.core.schedule.DefaultExecutor;
import com.seer.core.service.NsIdGenManager;
import com.seer.entity.NsBin;
import com.seer.entity.NsHumanUser;
import com.seer.entity.NsTaskAbortInitData;
import com.seer.falcon.BlockRuntime;
import com.seer.falcon.TaskLogLevel;
import com.seer.falcon.service.FalconTaskDefService;
import com.seer.falcon.service.FalconTaskService;
import com.seer.meta.domain.NsFindOptions;
import com.seer.meta.service.NsDictManager;
import com.seer.meta.service.NsEntityReader;
import com.seer.meta.service.NsEntityWriter;
import com.seer.web.MyWebSocketHandler;
import com.shadow.ns.service.common.BindBinContainerOptions;
import com.shadow.ns.service.common.NsCommonBusinessService;
import com.shadow.ns.service.core.NsContainerService;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BooleanSupplier;
import java.util.stream.Collectors;

import static com.seer.meta.domain.NsComplexQuery.*;

/**
 * 猎鹰任务 “故障恢复” Service
 * <p>
 * {@link FalconRecoveryService} 用于捕获猎鹰任务 BP 执行时抛出的异常，并通过 “故障恢复” 机制快速恢复。
 * 常见的 “恢复操作” 有：重试、人工完成、终止任务。
 * <p>
 * “故障恢复” 的核心方法是 {@link FalconRecoveryService#wrap(BlockRuntime, RecoveryConfig, BooleanSupplier, Runnable)}，
 * 需要在猎鹰 BP 中调用。
 * 其捕获到 BP 抛出的异常后，会创建一个 {@link Failure} 存到 {@link FalconRecoveryService#failures} 。
 * 如果这个 BP 配置了 “监听器”，还会异步运行 monitorWorker，并将这个 Future 存到 {@link Failure}
 * <p>
 * MWMS 关闭后，{@link FalconRecoveryService#failures} 的线程一定会被停止，所以只在内存中记录失败的任务，不持久化。
 */
@Service
public class FalconRecoveryService {
    
    private static final Logger logger = LoggerFactory.getLogger(FalconRecoveryService.class);
    
    private final Map<String, Failure> failures = new ConcurrentHashMap<>();
    
    private final MyWebSocketHandler myWebSocketHandler;
    
    private final FalconTaskService taskService;
    
    
    private final NsDictManager dict;
    private final NsCommonBusinessService commonBusinessService;
    private final NsEntityWriter entityWriter;
    private final NsEntityReader entityReader;
    private final NsIdGenManager idGenManager;
    private final NsContainerService containerService;
    
    public FalconRecoveryService(MyWebSocketHandler myWebSocketHandler, FalconTaskService taskService,
                                 FalconTaskDefService defService, NsDictManager dict,
                                 NsCommonBusinessService commonBusinessService, NsEntityWriter entityWriter,
                                 NsEntityReader entityReader, NsIdGenManager idGenManager,
                                 NsContainerService containerService) {
        this.myWebSocketHandler = myWebSocketHandler;
        this.taskService = taskService;
        this.dict = dict;
        this.commonBusinessService = commonBusinessService;
        this.entityWriter = entityWriter;
        this.entityReader = entityReader;
        this.idGenManager = idGenManager;
        this.containerService = containerService;
    }
    
    @PostConstruct
    public void init() {
        myWebSocketHandler.sendAll("FalconRecover", "");
    }
    
    /**
     * 记录用户选的 “恢复操作”
     */
    public void setRecoverAction(String id, int userAction) {
        var f = failures.get(id);
        if (f != null) f.userAction = userAction;
    }
    
    public List<FailureView> listFailures() {
        List<FailureView> views = new ArrayList<>();
        for (var item : failures.entrySet()) {
            var value = item.getValue();
            views.add(new FailureView(item.getKey(), value.taskId, value.timestamp, value.config.errorMessage,
                    value.actualError, value.config.fixers.stream().map(it -> it.label).collect(Collectors.toList())));
        }
        return views;
    }
    
    public void clearFailures() {
        failures.clear();
    }
    
    /**
     * 捕获异常并处理。
     *
     * @param blockRuntime  猎鹰任务运行参数
     * @param config        恢复配置。可以配置 “重试”，“人工完成”，“终止任务” 等。默认会额外加一个 “放弃修复”。
     * @param monitorWorker 监听器：用于监听调度（RDS-Core） 订单状态，在 RoboShop 中处理订单状态之后，MWMS 需要能自动恢复。
     * @param worker        BP 的业务代码
     */
    public void wrap(BlockRuntime blockRuntime, RecoveryConfig config, BooleanSupplier monitorWorker, Runnable worker) {
        // 不要恢复，直接报错
        if (blockRuntime.blockContext.noRecovery) {
            worker.run();
            return;
        }
        
        while (true) {
            taskService.suspendIfTaskPaused(blockRuntime.getTopTaskRuntime());
            if (blockRuntime.getTopTaskRuntime().isTaskAborted()) return;
            try {
                worker.run();
                return;
            } catch (Exception e) {
                logException(e, blockRuntime);
                
                var id = IdHelper.objectIdStr();
                var failure = createFailure(config, blockRuntime, e, monitorWorker, id);
                failures.put(id, failure);
                DefaultExecutor.bgCacheExecutor.submit(() -> myWebSocketHandler.sendAll("FalconRecover", ""));
                
                if (processException(id, failure, blockRuntime, config, e)) return;
            }
        }
    }
    
    public void wrap(BlockRuntime blockRuntime, RecoveryConfig config, Runnable worker) {
        wrap(blockRuntime, config, null, worker);
    }
    
    // 返回 true 处理完成，跳出 wrap()；返回 false 需要重新执行 worker()
    
    /**
     * 处理异常
     */
    @SneakyThrows
    private boolean processException(String id, Failure failure, BlockRuntime blockRuntime, RecoveryConfig config,
                                     Exception e) {
        while (true) {
            if (failure.userAction == -1) {
                // 放弃恢复
                logActionAndRemoveFailures(id, blockRuntime, dict.lo("NoRecovery"), failure, true);
                throw e;
            } else if (failure.userAction >= 0) {
                // 恢复
                var msg = dict.lo("ChooseRecover2", failure.userAction, config.fixers.get(failure.userAction).label);
                logActionAndRemoveFailures(id, blockRuntime, msg, failure, true);
                
                config.fixers.get(failure.userAction).fixer.run();
                
                return config.fixers.get(failure.userAction).noRecovery;
            } else {
                // 未选择。检查 FalconTask 和 monitorWorker
                try {
                    // FIXME monitorDone = monitor?.isDone == true && monitorResult 是否欠妥？
                    if (failure.monitorDone()) {
                        var msg = dict.lo("RecoveryMonitorDone", failure.taskId, failure.actualError);
                        logActionAndRemoveFailures(id, blockRuntime, msg, failure, false);
                        
                        // return false;
                        return failure.monitorResult;
                    }
                    if (blockRuntime.getTopTaskRuntime().isTaskAborted()) {
                        logActionAndRemoveFailures(id, blockRuntime, dict.lo("TaskAbortedSkipRecovery"), failure, true);
                        
                        return true;
                    }
                    Thread.sleep(500);
                } catch (InterruptedException ie) {
                    if (blockRuntime.getTopTaskRuntime().isTaskAborted()) {
                        logActionAndRemoveFailures(id, blockRuntime, dict.lo("InterruptedByTaskAbort"), failure, false);
                        return true;
                    }
                    throw ie;
                }
            }
        }
    }
    
    private Failure createFailure(RecoveryConfig config, BlockRuntime blockRuntime, Exception e,
                                  BooleanSupplier monitorWorker, String id) {
        var failure = new Failure(config, blockRuntime.getTopTaskId(), e.getMessage(), false, null);
        
        failure.monitor = monitorWorker != null ? DefaultExecutor.bgCacheExecutor.submit(() -> {
            logger.debug("recover service monitor begin ------");
            failure.monitorResult = monitorWorker.getAsBoolean();
        }) : null;
        if (failure.monitor != null) blockRuntime.getTopTaskRuntime().monitorFutures.put(id, failure.monitor);
        
        return failure;
    }
    
    private void logException(Exception e, BlockRuntime blockRuntime) {
        var msg = taskService.buildBlockLogHead(blockRuntime) +
                dict.lo("RecoveryServiceExceptionLog", blockRuntime.blockConfig.id,
                        JsonHelper.writeValueAsString(blockRuntime.blockContext));
        taskService.logTask(blockRuntime.getTopTaskRuntime(), msg, TaskLogLevel.Normal);
        var msg2 = taskService.buildBlockLogHead(blockRuntime) + "error msg=" + e.getMessage();
        taskService.logTask(blockRuntime.getTopTaskRuntime(), msg2, TaskLogLevel.Important);
        logger.error("recovery", e);
    }
    
    private void logActionAndRemoveFailures(String id, BlockRuntime blockRuntime, String action, Failure failure,
                                            boolean cancelMonitor) {
        var msg = taskService.buildBlockLogHead(blockRuntime) + action;
        taskService.logTask(blockRuntime.getTopTaskRuntime(), msg, TaskLogLevel.Important);
        
        failures.remove(id);
        if (cancelMonitor) failure.cancelMonitor();
        
        DefaultExecutor.bgCacheExecutor.submit(() -> myWebSocketHandler.sendAll("FalconRecover", ""));
    }
    
    // TODO 预留 修改容器组合
    public boolean recoveryContainerAndInv(NsTaskAbortInitData data, NsHumanUser user) {
        var binId = data.getIpBinId("");
        var containerId = data.getContainerId("");
        var removeInv = data.getDeleteInvOrFalse();
        
        // 清理原本库位上的容器
        var oldBins = entityReader.findMany(NsBin.class, and(eq("container", containerId), ne("_id", binId)),
                new NsFindOptions().setProjection(List.of("_id")));
        if (CollectionUtils.isNotEmpty(oldBins)) oldBins.forEach(
                b -> commonBusinessService.setBinNotOccupiedNotLocked(b.getId(), user.getId(),
                        dict.lo("RecoveryContainerAndInv")));
        
        // 库位 + 容器
        if (StringUtils.isNotBlank(binId) && StringUtils.isNotBlank(containerId)) {
            BindBinContainerOptions options = new BindBinContainerOptions(false);
            commonBusinessService.bindBinContainer(binId, containerId, data.getOrderId(""), "", options);
        }
        
        // 库存
        if (removeInv) {
            commonBusinessService.removeContainerInv(containerId, "", "", "", 0, user.getId());
        } else {
            var state = data.getState("");
            commonBusinessService.fixInvByContainerMoved(containerId, state, "", binId, data.getOrderIdOrEmpty(), "", 0,
                    "", "", "");
        }
        logger.info("修正完成,修改猎鹰任务状态[{}]", data.getOrderId(""));
        
        try {
            taskService.abortTask(data.getOrderId(""), dict.lo("TaskAbort"));
        } catch (Exception e) {
            logger.error("Failed to abort task {}", data.getOrderId(""), e);
        }
        return true;
    }
    
}
