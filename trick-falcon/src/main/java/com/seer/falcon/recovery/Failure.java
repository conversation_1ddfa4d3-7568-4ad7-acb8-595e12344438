package com.seer.falcon.recovery;

import kotlin.jvm.Volatile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.concurrent.Future;

public class Failure {

    private static final Logger logger = LoggerFactory.getLogger(Failure.class);

    public RecoveryConfig config;
    public String taskId;
    public String actualError;
    @Volatile
    public boolean monitorResult;
    public Future<?> monitor;

    public final Date timestamp = new Date();

    public volatile int userAction = -2;

    public Failure(RecoveryConfig config, String taskId, String actualError, boolean monitorResult, Future<?> monitor) {
        this.config = config;
        this.taskId = taskId;
        this.actualError = actualError;
        this.monitorResult = monitorResult;
        this.monitor = monitor;
    }

    public boolean monitorDone() {
        return monitor != null && monitor.isDone() && monitorResult;
    }

    public void cancelMonitor() {
        logger.info("Falcon task [{}] recovery，cancel monitor!", taskId);
        if (monitor != null) monitor.cancel(true);
    }

}
