package com.seer.trick.falcon.bp.basic

import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockOutputParamDef
import com.seer.trick.falcon.domain.BlockParamType

class ExpressionBp : AbstractBp() {
  
  override fun process() {
    val r = getBlockInputParam(ipExpression.name)
    setBlockOutputParams(mapOf("expResult" to r))
  }
  
  companion object {
    
    private val ipExpression = BlockInputParamDef("expression", BlockParamType.String, true)
    
    val def = BlockDef(
      ExpressionBp::class.simpleName!!,
      color = "#ffd22b",
      inputParams = listOf(ipExpression),
      outputParams = listOf(BlockOutputParamDef("expResult", BlockParamType.Any))
    )
    
  }
  
}


