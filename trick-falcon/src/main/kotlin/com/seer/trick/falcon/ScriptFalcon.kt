package com.seer.trick.falcon

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.base.script.ScriptTraceContext
import com.seer.trick.falcon.task.FalconGlobalControlService
import com.seer.trick.falcon.task.FalconTaskDefService
import com.seer.trick.falcon.task.FalconTaskService
import com.seer.trick.helper.JsonHelper
import org.slf4j.LoggerFactory

object ScriptFalcon {

  private val logger = LoggerFactory.getLogger(javaClass)

  fun runTaskByLabelAsync(tc: ScriptTraceContext, defLabel: String, jsInputParams: Map<String, Any?>): String {
    val inputParams: Map<String, Any?> = JsonHelper.clone(jsInputParams, jacksonTypeRef())
    val taskDef = FalconTaskDefService.mustGetTaskDefByLabel(defLabel)
    val tr = FalconTaskService.createTask(taskDef, inputParams, null)
    FalconTaskService.runTaskAsync(tr)
    return tr.taskId
  }

  fun logTask(tc: ScriptTraceContext, taskId: String, level: String, message: String) {
    val l = FalconLogLevel.valueOf(level)
    LogManager.logTask(logger, taskId, l, message)
  }

  fun logBlock(tc: ScriptTraceContext, taskId: String, blockId: String, level: String, message: String) {
    val l = FalconLogLevel.valueOf(level)
    LogManager.logBlock(logger, taskId, blockId, l, message)
  }

  fun getGlobalVariable(tc: ScriptTraceContext, varName: String): Any? {
    FalconGlobalControlService.getConfig(varName) ?: throw BzError("errNoFalconGlobalVariable", varName)
    return FalconGlobalControlService.getValue(varName)
  }

  fun setGlobalVariable(tc: ScriptTraceContext, varName: String, varValue: Any) {
    FalconGlobalControlService.getConfig(varName) ?: throw BzError("errNoFalconGlobalVariable", varName)
    // TODO 暂时不校验值类型
    FalconGlobalControlService.setValue(varName, varValue)
    logger.info("set falcon global variable $varName = $varValue")
  }
}