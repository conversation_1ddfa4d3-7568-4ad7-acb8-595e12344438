package com.seer.trick.falcon.bp

import java.util.*

class BlockContext(
  parentContext: BlockContext?,
  @Volatile
  var noRecovery: Boolean = parentContext?.noRecovery ?: false
) {
  
  // 上下文变量不持久化，每次运行时赋值
  // 如果运行多次子块，上下文变量分每次运行不同的（如第几次循环）和每次运行相同的（如分配的资源）
  // 如果要持久化上下文参数，可以用同名的块内部变量
  val ctxVariables: MutableMap<String, Any?> = Collections.synchronizedMap(HashMap())
  
  val blocksByConfigId: MutableMap<Int, AbstractBp> = Collections.synchronizedMap(HashMap())
  
  // by block name
  val expressionBlocks: MutableMap<String, MutableMap<String, Any?>> = Collections.synchronizedMap(HashMap())
  
  init {
    if (parentContext != null) {
      blocksByConfigId.putAll(parentContext.blocksByConfigId)
      expressionBlocks.putAll(parentContext.expressionBlocks)
    }
  }
  
  fun addOutputParamsToExpressionCtx(bp: AbstractBp) {
    blocksByConfigId[bp.blockConfig.id] = bp
    var blockExpCtx = expressionBlocks[bp.blockConfig.name]
    if (blockExpCtx == null) {
      blockExpCtx = HashMap()
      expressionBlocks[bp.blockConfig.name] = blockExpCtx
    }
    blockExpCtx.putAll(bp.outputParams)
  }
  
  fun setContextVariables(bp: AbstractBp, ctxVariables: Map<String, Any?>) {
    blocksByConfigId[bp.blockConfig.id] = bp
    this.ctxVariables.putAll(ctxVariables)
    var blockExpCtx = expressionBlocks[bp.blockConfig.name]
    if (blockExpCtx == null) {
      blockExpCtx = HashMap()
      expressionBlocks[bp.blockConfig.name] = blockExpCtx
    }
    // TODO 这里应该是 this.ctxVariables 还是 ctxVariables（函数参数）
    blockExpCtx.putAll(this.ctxVariables)
  }
  
}
