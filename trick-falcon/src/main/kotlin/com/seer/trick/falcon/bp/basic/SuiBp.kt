package com.seer.trick.falcon.bp.basic

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef

import com.seer.trick.base.sui.SuiManager
import com.seer.trick.base.sui.SuiRequest
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockOutputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.helper.JsonHelper

class SuiBp : AbstractBp() {
  
  override fun process() {
    val rawConfig = mustGetBlockInputParam("config")
    val config: SuiRequest = JsonHelper.mapper.convertValue(rawConfig, jacksonTypeRef())
    val result = SuiManager.sui(config)
    setBlockOutputParams(mapOf("button" to result.button, "input" to result.input))
  }
  
  companion object {
    
    val def = BlockDef(
      SuiBp::class.simpleName!!,
      color = "#BEA7E1",
      inputParams = listOf(BlockInputParamDef("config", BlockParamType.JSONObject, true)),
      outputParams = listOf(
        BlockOutputParamDef("button", BlockParamType.Long),
        BlockOutputParamDef("input", BlockParamType.JSONObject)
      )
    )
    
  }
  
}