package com.seer.trick.falcon.handler

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.base.file.FileManager
import com.seer.trick.base.http.Handlers
import com.seer.trick.base.http.HttpServerManager.auth
import com.seer.trick.base.http.HttpServerManager.noAuth
import com.seer.trick.base.http.getReqBody
import com.seer.trick.falcon.FalconCenter
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.CreateTaskReq
import com.seer.trick.falcon.domain.TaskDef
import com.seer.trick.falcon.task.*
import com.seer.trick.helper.JsonHelper
import io.javalin.http.Context
import org.apache.commons.io.IOUtils
import java.nio.charset.StandardCharsets

object FalconHandler {

  fun registerHandlers() {
    val c = Handlers("api/falcon")
    c.get("blocks", ::listBlocks, noAuth())

    c.get("task-defs", ::listTaskDefs, noAuth())
    c.get("task-def/{id}", ::getTaskDef, noAuth())
    c.post("task-def/update", ::saveTaskDef, auth())
    c.post("task-def/remove", ::removeTaskDef, auth())
    c.post("task-def/list-history", ::listHistoryTaskDef, auth())
    c.post("task-def/get-one", ::getOneTaskDef, auth())
    c.post("task-def/restore-version", ::restoreTaskDef, auth())
    c.post("task-def/export", ::exportTaskDefs, auth())

    c.post("task-def/import", ::importTaskDefs, auth())
    c.post("task-def/check/import", ::checkImportTaskDefs, auth())

    c.get("ext-bp", ::listExtBp, noAuth())
    c.post("ext-bp/update", ::saveExtBp, auth())
    c.post("ext-bp/remove", ::removeExtBp, auth())
    c.post("ext-bp/list-history", ::listHistoryExtBp, auth())
    c.post("ext-bp/get-one", ::getOneExtBp, auth())
    c.post("ext-bp/restore-version", ::restoreExtBp, auth())

    c.post("task/run", ::runTask, auth())
    c.post("task/recover-error", ::recoverError, auth())
    c.post("task/pause-resume", ::pauseResume, auth())
    c.post("task/cancel", ::cancelTask, auth())

    c.get("global-control/values", ::listGlobalValues, auth())
    c.get("global-control/configs", ::listGlobalConfigs, auth())
    c.post("global-control/configs", ::setGlobalConfig, auth())
    c.post("global-control/values", ::setGlobalValue, auth())
  }

  private fun listGlobalValues(ctx: Context) {
    ctx.json(FalconGlobalControlService.valueMap())
  }

  private fun listGlobalConfigs(ctx: Context) {
    ctx.json(FalconGlobalControlService.configList())
  }

  private fun setGlobalConfig(ctx: Context) {
    val cfgList: List<FalconGlobalVariable> = ctx.getReqBody()
    FalconGlobalControlService.setConfigs(cfgList)
  }

  private fun setGlobalValue(ctx: Context) {
    val map: Map<String, Any?> = ctx.getReqBody()
    FalconGlobalControlService.setValues(map)
  }

  private fun listBlocks(ctx: Context) {
    val groups = FalconCenter.listGroups()
    ctx.json(groups)
  }

  private fun listTaskDefs(ctx: Context) {
    val list = FalconTaskDefService.list().map { def ->
      mapOf(
        "id" to def.id,
        "label" to def.label,
        "version" to def.version,
        "inputParams" to def.inputParams,
        "outputParams" to def.outputParams,
        "group" to def.group,
        "builtin" to def.builtin,
      )
    }
    ctx.json(list)
  }

  private fun getTaskDef(ctx: Context) {
    val taskDefId = ctx.pathParam("id")
    val def = FalconTaskDefService.mustFetchLatestTaskDefById(taskDefId)
    ctx.json(def)
  }

  private fun saveTaskDef(ctx: Context) {
    val req: TaskDef = ctx.getReqBody()
    if (req.label.isEmpty()) throw BzError("errFalconLabelEmpty")
    if (!FalconTaskDefService.checkRepeatFalcon(req)) throw BzError("errFalconLabelRepeat", req.label)
    val id = FalconTaskDefService.save(req)

    ctx.json(mapOf("id" to id))
  }

  private fun removeTaskDef(ctx: Context) {
    val req: RemoveTaskDefReq = ctx.getReqBody()
    FalconTaskDefService.remove(req.ids)

    ctx.status(200)
  }

  private fun listExtBp(ctx: Context) {
    val list = FalconCenter.extBlockDefMap.values
    ctx.json(list)
  }

  private fun saveExtBp(ctx: Context) {
    val def: BlockDef = ctx.getReqBody()
    FalconCenter.saveExtBp(def)
    ctx.status(200)
  }

  private fun removeExtBp(ctx: Context) {
    val req: RemoveBlockDefReq = ctx.getReqBody()
    FalconCenter.removeExtBp(req.ids)

    ctx.status(200)
  }

  private fun runTask(ctx: Context) {
    val req: RunTaskReq = ctx.getReqBody()

    val tr = FalconTaskService.createTopTask(CreateTaskReq(req.defId, req.input))

    if (req.sync) {
      FalconTaskService.runTaskSync(tr)
    } else {
      FalconTaskService.runTaskAsync(tr)
    }

    ctx.json(mapOf("taskId" to tr.taskId, "defId" to req.defId))
  }

  private fun recoverError(ctx: Context) {
    val req: RecoverErrorReq = ctx.getReqBody()

    for (taskId in req.taskIds) {
      FalconTaskService.recoveryErrorRun(taskId)
    }

    ctx.status(200)
  }

  private fun pauseResume(ctx: Context) {
    val req: PauseResumeReq = ctx.getReqBody()

    for (taskId in req.taskIds) {
      if (req.paused) {
        FalconTaskService.pauseTask(taskId, null)
      } else {
        FalconTaskService.resumeTask(taskId)
      }
    }
  }

  private fun cancelTask(ctx: Context) {
    val req: CancelTaskReq = ctx.getReqBody()

    for (taskId in req.taskIds) {
      FalconTaskService.cancelTask(taskId, null)
    }
  }

  private fun listHistoryTaskDef(ctx: Context) {
    val defId = ctx.queryParam("id")
    if (defId.isNullOrBlank()) throw BzError("errMissingHttpQueryParam", "id")
    ctx.json(FalconTaskDefService.listTaskDefRevisions(defId))
  }

  private fun getOneTaskDef(ctx: Context) {
    val req: DesignatedFalconReq = ctx.getReqBody()
    ctx.json(FalconTaskDefService.loadTaskDefRevision(req.id, req.version))
  }

  private fun restoreTaskDef(ctx: Context) {
    val req: DesignatedFalconReq = ctx.getReqBody()
    FalconTaskDefService.restoreTaskDefRevision(req.id, req.version)
    ctx.status(200)
  }

  private fun listHistoryExtBp(ctx: Context) {
    val name = ctx.queryParam("name")
    if (name.isNullOrBlank()) throw BzError("errMissingHttpQueryParam", "name")
    val taskDefList = FalconCenter.listExtBpRevisions(name)
    ctx.json(taskDefList)
  }

  private fun getOneExtBp(ctx: Context) {
    val req: DesignatedExtBpReq = ctx.getReqBody()
    ctx.json(FalconCenter.loadExtBpRevision(req.name, req.version))
  }

  private fun restoreExtBp(ctx: Context) {
    val req: DesignatedExtBpReq = ctx.getReqBody()
    FalconCenter.restoreExtBpRevision(req.name, req.version)
    ctx.status(200)
  }

  // 导出猎鹰任务
  private fun exportTaskDefs(ctx: Context) {
    val req: ExportDefReq = ctx.getReqBody()
    val file = FalconTaskDefService.exportTaskDef(req.ids, req.names, req.remark)
    ctx.json(mapOf("path" to FileManager.fileToPath(file)))
  }

  // 校验导入的猎鹰任务
  private fun checkImportTaskDefs(ctx: Context) {
    val file = ctx.uploadedFile("f0") ?: throw BzError("errNoUploadedFile", "f0")
    if (!file.filename().endsWith(".falcon.json")) throw BzError("errIllegalFile")

    file.content().use { inputStream ->
      val str = IOUtils.toString(inputStream, StandardCharsets.UTF_8)
      val data: ExportRecord = JsonHelper.mapper.readValue(str, jacksonTypeRef())
      ctx.json(FalconTaskDefService.checkImportTaskDefs(data))
    }
  }

  // 导入
  private fun importTaskDefs(ctx: Context) {
    val req: ImportFalconReq = ctx.getReqBody()
    FalconTaskDefService.importTaskDef(req.templates, req.components)
  }
}

data class RunTaskReq(val defId: String, val input: Map<String, Any?>, val sync: Boolean = false)

data class RecoverErrorReq(val taskIds: List<String>)

data class PauseResumeReq(val taskIds: List<String> = emptyList(), val paused: Boolean = false)

data class CancelTaskReq(val taskIds: List<String>)

data class RemoveTaskDefReq(val ids: List<String>)

data class RemoveBlockDefReq(val ids: List<String>)

/**
 * 导出猎鹰导出请求参数实体
 */
data class ExportDefReq(
  val ids: List<String> = emptyList(),
  val names: List<String> = emptyList(),
  val remark: String = "",
)

/**
 * 确认导入的猎鹰参数实体
 */
data class ImportFalconReq(
  val templates: List<TaskDef> = emptyList(), // 组件名称
  val components: List<BlockDef> = emptyList(), // 猎鹰任务模板名称
)

data class DesignatedFalconReq(val id: String, val version: Int)

data class DesignatedExtBpReq(val name: String, val version: Int)