package com.seer.trick.falcon.bp.basic

import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.falcon.domain.MissingBlockInputParamError

class DelayBp : AbstractBp() {

  override fun process() {
    val timeMillis = getBlockInputParamAsLong(ipTimeMillis.name)
      ?: throw MissingBlockInputParamError(blockConfig.name, ipTimeMillis.name)
//    val timeMillis = getBlockInputParam(ipTimeMillis.name) as Long?
//      ?: throw MissingBlockInputParamError(blockConfig.name, ipTimeMillis.name)
    Thread.sleep(timeMillis)
  }

  companion object {

    private val ipTimeMillis = BlockInputParamDef("timeMillis", BlockParamType.Long, true)

    val def = BlockDef(
      DelayBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(ipTimeMillis),
    )
  }
}