package com.seer.trick.falcon.bp.basic

import com.seer.trick.falcon.FalconLogLevel
import com.seer.trick.falcon.LogManager
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType

class PrintBp : AbstractBp() {
  
  override fun process() {
    val message = getBlockInputParam(ipMessage.name)
    log(FalconLogLevel.Debug, message?.toString() ?: "")
    LogManager.logTask(logger, taskRuntime.taskId, FalconLogLevel.Debug, message?.toString() ?: "")
  }
  
  companion object {
    
    private val ipMessage = BlockInputParamDef("message", BlockParamType.Any)
    
    val def = BlockDef(
      PrintBp::class.simpleName!!,
      color = "#f8fdcb",
      inputParams = listOf(ipMessage)
    )
    
  }
  
}

