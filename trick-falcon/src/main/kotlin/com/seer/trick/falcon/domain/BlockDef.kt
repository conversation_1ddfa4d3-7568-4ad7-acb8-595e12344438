package com.seer.trick.falcon.domain

import com.fasterxml.jackson.annotation.JsonInclude
import java.util.*

data class BlockDef(
  val name: String = "",
  val inputParams: List<BlockInputParamDef> = emptyList(),
  val outputParams: List<BlockOutputParamDef> = emptyList(),
  val contextVariables: List<BlockContextVariableDef> = emptyList(),
  val children: List<BlockChildDef> = emptyList(),
  val hidden: Boolean = false,
  val extFunction: String? = null, // 脚本函数名
  val sourceLang: String? = null, // python, js
  val source: String? = null, // 源代码
  var label: String = "", // 目前仅扩展组件用
  var description: String = "", // 目前仅扩展组件用
  var color: String = "",
  var version: Int = 1,
  @JsonInclude(JsonInclude.Include.NON_NULL)
  val modifiedOn: Date? = null,
  val modifiedBy: String = "",
  @JsonInclude(JsonInclude.Include.NON_NULL)
  val createdOn: Date? = null,
  val createdBy: String = "",
)

data class BlockInputParamDef(
  val name: String = "",
  val type: BlockParamType = BlockParamType.String,
  val required: Boolean = false,
  val defaultValue: Any? = null,
  val options: List<BlockInputParamOption>? = null,
  var label: String = "", // 目前仅扩展组件用
  var description: String = "", // 目前仅扩展组件用
  val objectTypes: List<String>? = null,
)

data class BlockOutputParamDef(
  val name: String = "",
  val type: BlockParamType = BlockParamType.String,
  var label: String = "", // 目前仅扩展组件用
  var description: String = "", // 目前仅扩展组件用
  val objectTypes: List<String>? = null,
)

data class BlockContextVariableDef(
  val name: String = "",
  val type: BlockParamType = BlockParamType.String,
  var label: String = "", // 目前仅扩展组件用
  var description: String = "", // 目前仅扩展组件用
  val objectTypes: List<String>? = null,
)

object ParamObjectType {

  const val EntityName = "EntityName"
  const val EntityId = "EntityId"
  const val EntityFieldName = "EntityFieldName"

  const val OrderId = "OrderId"
  const val CtoId = "CtoId"
  const val DirectOrderId = "DirectOrderId"

  const val BinId = "BinId"
  const val DistrictId = "DistrictId"
  const val SiteId = "SiteId"

  const val RobotScene = "RobotScene"

  const val TomOrderId = "TomOrderId"
  const val TomBlockId = "TomBlockId"

  const val RobotName = "RobotName"
  const val RobotGroup = "RobotGroup"

  const val ContainerId = "ContainerId"
  const val ContainerType = "ContainerType"

  const val PlcDevice = "PlcDevice"

  const val StepId = "StepId"
}

data class BlockChildDef(
  val name: String = "",
  val childrenMinNum: Int? = null, // 允许最少多少个孩子
  val childrenMaxNum: Int? = null, // 允许最多多少个孩子
  var label: String = "",
)

data class BlockInputParamOption(val value: String? = null, var label: String? = null)

enum class BlockParamType {
  String,
  Boolean,
  Long,
  Double,
  JSONObject,
  JSONArray,
  Any,
}

data class BlockDefGroup(
  val name: String,
  var label: String = "",
  val order: Int = 0,
  val hidden: Boolean = true,
  val blocks: List<BlockDef>,
)