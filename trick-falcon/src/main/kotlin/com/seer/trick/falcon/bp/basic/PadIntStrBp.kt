package com.seer.trick.falcon.bp.basic

import com.seer.trick.BzError
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockOutputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import org.apache.commons.lang3.StringUtils

class PadIntStrBp : AbstractBp() {

  override fun process() {
    val num = mustGetBlockInputParamAsLong("num")
    val numLength = mustGetBlockInputParamAsLong("numLength")
    if (numLength > Int.MAX_VALUE || numLength <= 0) {
      throw BzError("errFalconBlockInputParamRangeError", blockConfig.name, "numLength")
    }
    val prefix = getBlockInputParam("prefix") as String? ?: ""
    val suffix = getBlockInputParam("suffix") as String? ?: ""
    val str = prefix + StringUtils.leftPad(num.toString(), numLength.toInt(), '0') + suffix
    setBlockOutputParams(mapOf("numStr" to str))
  }

  companion object {
    val def = BlockDef(
      PadIntStrBp::class.simpleName!!,
      color = "#F8FDCD",
      inputParams = listOf(
        BlockInputParamDef("num", BlockParamType.Long, true),
        BlockInputParamDef("numLength", BlockParamType.Long, true),
        BlockInputParamDef("prefix", BlockParamType.String, false),
        BlockInputParamDef("suffix", BlockParamType.String, false)
      ),
      outputParams = listOf(BlockOutputParamDef("numStr", BlockParamType.String))
    )
  }
}