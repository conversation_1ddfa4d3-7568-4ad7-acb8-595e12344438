package com.seer.trick.falcon.bp.conditionloop


import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.bp.BlockContext
import com.seer.trick.falcon.domain.*

class WhileBp : AbstractBp() {

  override fun process() {
    var i = 0L

    while (getBlockInputParamAsBool(ipCondition.name)) {
      val children = blockConfig.children["true"] ?: return

      val context = BlockContext(blockContext)
      try {
        serialRunChildren(children, i++.toString(), context)
      } catch (e: BreakLoop) {
        break
      }

      Thread.sleep(100) // 防止 while 内部执行时间果断导致的刷屏
    }
    super.persistBlockInputParams()
  }

  companion object {
    private val ipCondition = BlockInputParamDef("condition", BlockParamType.Boolean, true)

    val def = BlockDef(
      WhileBp::class.simpleName!!,
      color = "#9EC8B9",
      inputParams = listOf(ipCondition),
      children = listOf(BlockChildDef("true")),
    )
  }
}