package com.seer.trick.falcon.bp.basic

import com.seer.trick.BzError

import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockInputParamOption
import com.seer.trick.falcon.domain.BlockOutputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.helper.DateHelper
import java.util.Date

class TimestampBp : AbstractBp() {

  override fun process() {
    val formatDate = mustGetBlockInputParam("formatDate") as String
    // 前后端一致性校验，格式不存在则抛异常
    if (!formatDateOptions.contains(formatDate)) {
      throw BzError("errFalconBlockOptionParamError", blockConfig.name, "formatDate")
    }
    setBlockOutputParams(mapOf("timestamp" to DateHelper.formatDate(Date(), formatDate)))
  }

  companion object {
    private val formatDateOptions = listOf(
      "yyyy-MM-dd'T'HH:mm:ss.SSSXXX",
      "yyyy-MM-dd'T'HH:mm:ss.SSSZ",
      "yyyy-MM-dd'T'HH:mm:ssX",
      "yyyy-MM-dd HH:mm:ss",
      "yyyy-MM-dd HH:mm:ss.SSS",
      "yyyy-MM-dd HH:mm",
      "yyyy-MM-dd",
      "yyyy/MM/dd",
      "yyyy'年'MM'月'dd'日'",
      "HH:mm:ss"
    )
    val def = BlockDef(
      TimestampBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(
        BlockInputParamDef(
          name = "formatDate",
          options = formatDateOptions.map { BlockInputParamOption(it) },
          required = true
        )
      ),
      outputParams = listOf(BlockOutputParamDef("timestamp", BlockParamType.String))
    )
  }
}