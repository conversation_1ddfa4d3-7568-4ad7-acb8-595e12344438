package com.seer.trick.falcon.bp.falcon


import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*

/**
 * 查找当前任务记录的字段值，不能查找父、子实例的字段
 */
class FindFalconRecordFieldBp : AbstractBp() {

  override fun process() {
    val fieldName = mustGetBlockInputParam("fieldName") as String
    EntityHelper.mustGetFm(BaseCenter.mustGetEntityMeta("FalconTaskRecord"), fieldName)
    val ev = EntityRwService.findOneById("FalconTaskRecord", taskRuntime.taskId)
    setBlockOutputParams(mapOf("fieldValue" to ev!![fieldName]))
  }

  companion object {
    val def = BlockDef(
      FindFalconRecordFieldBp::class.simpleName!!,
      color = "#C7DCA7",
      inputParams = listOf(
        BlockInputParamDef(
          "fieldName",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.EntityFieldName),
        ),
      ),
      outputParams = listOf(
        BlockOutputParamDef("fieldValue", BlockParamType.JSONObject),
      ),
    )
  }
}