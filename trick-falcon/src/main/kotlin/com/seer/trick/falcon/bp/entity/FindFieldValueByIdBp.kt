package com.seer.trick.falcon.bp.entity


import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*

class FindFieldValueByIdBp : AbstractBp() {
  override fun process() {
    val entityName = mustGetBlockInputParam("entityName") as String
    val id = mustGetBlockInputParam("id") as String
    val field = mustGetBlockInputParam("field") as String
    
    val ev = EntityRwService.findOneById(entityName, id)
    if (ev == null) {
      setBlockOutputParams(mapOf("found" to false, "value" to null))
    } else {
      setBlockOutputParams(mapOf("found" to true, "value" to ev[field]))
    }
  }
  
  companion object {
    val def = BlockDef(
      FindFieldValueByIdBp::class.simpleName!!,
      color = "#48c9b0",
      inputParams = listOf(
        BlockInputParamDef(
          "entityName", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.EntityName)
        ),
        BlockInputParamDef(
          "id", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.EntityId)
        ),
        BlockInputParamDef(
          "field", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.EntityFieldName)
        ),
      ),
      outputParams = listOf(
        BlockOutputParamDef("found", BlockParamType.Boolean),
        BlockOutputParamDef("value", BlockParamType.JSONObject),
      ),
    )
  }
  
}