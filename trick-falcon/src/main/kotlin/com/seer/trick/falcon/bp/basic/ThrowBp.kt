package com.seer.trick.falcon.bp.basic

import com.seer.trick.BzError

import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType

class ThrowBp : AbstractBp() {

  override fun process() {
    // 异常处理标识
    val condition = getBlockInputParamAsBool("condition")
    // 异常信息
    val errMsg = getBlockInputParam("errMsg")
    if (condition) {
      throw BzError("errFalconThrowPrefix", errMsg?.toString())
    }
  }

  companion object {
    val def = BlockDef(
      ThrowBp::class.simpleName!!,
      color = "#F8DFD4",
      inputParams = listOf(
        BlockInputParamDef("condition", BlockParamType.Boolean, false),
        BlockInputParamDef("errMsg", BlockParamType.String, false)
      )
    )
  }
}