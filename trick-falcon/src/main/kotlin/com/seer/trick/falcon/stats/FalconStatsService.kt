package com.seer.trick.falcon.stats

import com.fasterxml.jackson.annotation.JsonProperty
import com.seer.trick.Cq

import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.stats.ChartGroup
import com.seer.trick.base.stats.ChartItem
import com.seer.trick.base.stats.StatsService
import com.seer.trick.base.stats.StatsService.buildShowLabelOnTop
import com.seer.trick.falcon.task.TaskStatus
import com.seer.trick.helper.DateHelper
import com.seer.trick.helper.JsonHelper
import org.apache.commons.lang3.time.DateUtils
import java.util.*

object FalconStatsService {

  fun register() {
    StatsService.addGroup(
      ChartGroup(
        "FalconTask", "猎鹰任务", 0, mutableListOf(
          ChartItem("FalconTaskLast10Days", "最近十日猎鹰任务", "", 0),
          ChartItem("FalconTaskLast30Days", "最近三十日猎鹰任务", "", 0),
        )
      )
    )
    StatsService.internalCalcMap["FalconTaskLast10Days"] = ::calcFalconTaskLast10Days
    StatsService.internalCalcMap["FalconTaskLast30Days"] = ::calcFalconTaskLast30Days
  }

  private fun calcFalconTaskLast10Days(): String {
    return calcFalconTaskLastNDays(10, "最近十日猎鹰任务")
  }

  private fun calcFalconTaskLast30Days(): String {
    return calcFalconTaskLastNDays(30, "最近三十日猎鹰任务")
  }

  private fun calcFalconTaskLastNDays(days: Int, title: String): String {
    val todayStart = DateUtils.truncate(Date(), Calendar.DATE)

    val data = mutableMapOf<String, FalconTaskValues>()
    for (d in 0 until days) {
      val dt = DateHelper.formatDate(DateUtils.addDays(todayStart, -d), "yyyy-MM-dd")
      data[dt] = FalconTaskValues(dt)
    }

    val startInstant = DateUtils.addDays(todayStart, -(days - 1))
    val endInstant = Date(DateUtils.addDays(todayStart, 1).time - 1)

    val evList = EntityRwService.findMany(
      
      "FalconTaskRecord",
      Cq.and(listOf(Cq.gte("createdOn", startInstant), Cq.lt("createdOn", endInstant))),
      FindOptions(projection = listOf("createdOn", "status"))
    )
    for (ev in evList) {
      val dt = DateHelper.formatDate(ev["createdOn"] as Date, "yyyy-MM-dd")
      val sv = data[dt] ?: continue
      sv.created++

      val status = ev["status"]
      if (status == TaskStatus.Done) sv.done++
      else if (status == TaskStatus.Cancelled || status == TaskStatus.Aborted) sv.failed++
    }

    val option = mapOf(
      "title" to mapOf("text" to title, "x" to "left", "y" to "top"),
      "legend" to mapOf("x" to "right", "y" to "top"),
      "dataset" to mapOf(
        "dimensions" to listOf("datetime", "创建数", "完成数", "异常数"),
        "source" to data.values.sortedBy { it.datetime }
      ),
      "xAxis" to mapOf("type" to "category"),
      "yAxis" to mapOf("type" to "value"),
      "series" to listOf(
        mapOf("type" to "bar") + buildShowLabelOnTop(),
        mapOf("type" to "bar") + buildShowLabelOnTop(),
        mapOf("type" to "bar") + buildShowLabelOnTop(),
      )
    )
    return JsonHelper.mapper.writeValueAsString(option)
  }

}

data class FalconTaskValues(
  val datetime: String,
  @JsonProperty("创建数")
  var created: Int = 0,
  @JsonProperty("完成数")
  var done: Int = 0,
  @JsonProperty("异常数")
  var failed: Int = 0,
)