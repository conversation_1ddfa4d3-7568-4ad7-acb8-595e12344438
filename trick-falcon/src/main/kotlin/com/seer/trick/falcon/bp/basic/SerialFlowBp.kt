package com.seer.trick.falcon.bp.basic

import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockChildDef
import com.seer.trick.falcon.domain.BlockDef

class SerialFlowBp : AbstractBp() {
  
  override fun process() {
    val childrenConfigs = defaultChildrenConfig
    serialRunChildren(childrenConfigs, CHILD_DEFAULT, blockContext)
  }
  
  companion object {
    
    val def = BlockDef(
      SerialFlowBp::class.simpleName!!,
      color = "#F8DFD4",
      children = listOf(BlockChildDef(CHILD_DEFAULT))
    )
    
  }
  
}