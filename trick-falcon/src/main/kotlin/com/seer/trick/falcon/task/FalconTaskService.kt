package com.seer.trick.falcon.task

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.Cq
import com.seer.trick.I18N
import com.seer.trick.I18N.lo

import com.seer.trick.base.BaseCenter.singleEnabled
import com.seer.trick.base.SysEmc
import com.seer.trick.base.alarm.AlarmAction
import com.seer.trick.base.alarm.AlarmItem
import com.seer.trick.base.alarm.AlarmLevel
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.base.concurrent.BaseConcurrentCenter.highTimeSensitiveExecutor
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityMeta
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.entity.service.RemoveOptions
import com.seer.trick.base.entity.service.extension.EntityServiceExtension
import com.seer.trick.falcon.FalconCenter
import com.seer.trick.falcon.FalconConcurrentCenter.falconMainExecutor
import com.seer.trick.falcon.FalconConcurrentCenter.updateAlarmExecutor
import com.seer.trick.falcon.FalconEventBus
import com.seer.trick.falcon.FalconLogLevel
import com.seer.trick.falcon.LogManager
import com.seer.trick.falcon.bp.BlockContext
import com.seer.trick.falcon.bp.BlockStatus
import com.seer.trick.falcon.bp.RootBp
import com.seer.trick.falcon.domain.*
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.NumHelper
import com.seer.trick.helper.submitCatch
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.Future
import kotlin.collections.set

/**
 * 为方便，这个 Service 承载了太多内容。Service 本来是给外部调用的，但实际目前内部也在调用，比如 Bp 执行时。
 */
object FalconTaskService : EntityServiceExtension() {

  private val logger = LoggerFactory.getLogger(javaClass)

  // TODO 这个都起什么作用
  val unfinishedTasks: MutableMap<String, TaskRuntime> = ConcurrentHashMap<String, TaskRuntime>()

  @Volatile
  private var updateAlarmFuture: Future<*>? = null

  private val resCleaners: MutableMap<String, MutableList<FalconResourceCleaner>> = ConcurrentHashMap()

  fun init() {
    updateAlarmFuture = updateAlarmExecutor.submit(::updateAlarmLoop)
  }

  fun dispose() {
    updateAlarmFuture?.cancel(true)
  }

  /**
   * 仅用于创建顶层任务
   */
  fun createTopTask(req: CreateTaskReq): TaskRuntime {
    if (req.defId.isBlank()) throw BzError("errFalconCreateTaskNoDefId")
    val taskDef = FalconTaskDefService.mustFetchLatestTaskDefById(req.defId)

    return createTask(taskDef, req.inputParams)
  }

  /**
   * 也给子任务用
   */
  fun createTask(
    
    taskDef: TaskDef,
    inputParams: Map<String, Any?>,
    topTask: TaskRuntime? = null,
  ): TaskRuntime {
    val rootBlockStateId = IdHelper.oidStr()
    val taskId = createTaskRecord(taskDef, rootBlockStateId, inputParams, topTask?.taskId)

    LogManager.logTask(logger, taskId, FalconLogLevel.Info, "TaskCreate: ${taskDef.label}, $inputParams")

    val taskRuntime = TaskRuntime(taskId, rootBlockStateId, taskDef, topTask)
    taskRuntime.inputParams.putAll(taskDef.parseInputParams(inputParams))

    // 任务变量默认值
    val taskVariables = taskDef.taskVariables.associate { it.name to it.defaultValue }
    setTaskVariables(taskRuntime, taskVariables)

    unfinishedTasks[taskId] = taskRuntime

    return taskRuntime
  }

  /**
   * 任何错误终止任务
   */
  fun runTaskSync(taskRuntime: TaskRuntime) {
    runTask(taskRuntime, true)
  }

  fun runTaskAsync(taskRuntime: TaskRuntime) {
    taskRuntime.future = falconMainExecutor.submit {
      runTaskNoError(taskRuntime)
    }
  }

  /**
   * 仅仅是不抛异常，用于最上层调用
   */
  private fun runTaskNoError(taskRuntime: TaskRuntime) {
    try {
      runTask(taskRuntime, false)
    } catch (e: Throwable) {
      if (!(e is TaskCancelledError || e is FatalError || e is BzError || e is BlockError)) {
        logger.error("runTaskNoError", e)
      }
    }
  }

  /**
   * 同步阻塞执行，可能很耗时。
   * 此方法仅给内部调用
   */
  fun runTask(taskRuntime: TaskRuntime, forcedEnd: Boolean) {
    LogManager.logTask(logger, taskRuntime.taskId, FalconLogLevel.Info, "TaskStart")

    // 标记开始
    taskRuntime.status = TaskStatus.Started
    updateTaskRecord(taskRuntime.taskId, mutableMapOf("status" to TaskStatus.Started))
    FalconEventBus.fire(taskRuntime.taskId, "TaskStart")

    // 单车猎鹰任务特殊处理：等待获得控制（串行执行）
    if (singleEnabled() && taskRuntime.isTopTask()) {
      SingleRobotExecutionController.control(taskRuntime.taskId)
    }

    try {
      val rootBlockConfig = taskRuntime.def.rootBlock
      if (rootBlockConfig.blockType != RootBp.def.name) throw FatalError("FalconNoRootBp")

      // 不管顶层任务还是子任务，上下文在任务层截止不再向上追溯
      val rootContext = BlockContext(null)

      try {
        FalconCenter.runBp(rootBlockConfig, taskRuntime.rootBlockStateId, rootContext, taskRuntime)
      } catch (e: BreakLoop) {
        logger.info("Terminate the loop")
      }

      for (opd in taskRuntime.def.outputParams) {
        val bp = rootContext.blocksByConfigId[opd.refBlockConfigId] ?: continue
        taskRuntime.outputParams[opd.name] = bp.outputParams[opd.refOutputParamName]
      }

      // 标记任务成功
      onTaskDone(taskRuntime)
    } catch (e: Exception) {
      if (e is TaskCancelledError) {
        logger.error("任务被用户取消")
      } else if (e is FatalError) {
        logger.error("任务遇到不可恢复性错误：" + e.message)
      } else if (e is BzError) {
        logger.error("任务遇到报错：" + e.message)
      } else if (e is BlockError) {
        logger.error("任务遇到报错：" + e.parent.message)
      } else {
        logger.error("任务遇到报错", e)
      }

      // 同步执行，遇到任务错误，直接终止
      onTaskError(taskRuntime, e, forcedEnd)

      if (e is AbortTaskError) {
        throw BzError(e.message ?: "errFalconAbortTask")
      }
      throw e
    } finally {
      // 单车猎鹰任务特殊处理：无论是否是终态，只要任务结束就释放任务串行执行的控制
      if (singleEnabled() && taskRuntime.isTopTask()) {
        SingleRobotExecutionController.release(taskRuntime.taskId)
      }
    }
  }

  fun cancelTask(taskId: String, reason: String?) {
    val topTaskId = findTopTask(taskId)
    val subTaskIds = findUnfinishedSubTask(topTaskId)
    logger.info("用户取消任务 topTaskId=$topTaskId, subTaskIds=$subTaskIds taskId=$taskId")
    doCancel(topTaskId, reason)
    for (subTaskId in subTaskIds) {
      doCancel(subTaskId, reason)
    }
  }

  private fun doCancel(taskId: String, reason: String?) {
    val tr = unfinishedTasks[taskId]
    if (tr != null) {
      // if (!tr.isTopTask()) return
      logger.info("用户取消任务 '$taskId'，当前状态：${tr.status}")
      // if (tr.status >= TaskStatus.Done) return

      unfinishedTasks.remove(taskId)
      if (tr.status == TaskStatus.Failed) {
        // 如果故障，任务未在运行，直接清理
        cleanTask(taskId)
      }

      LogManager.logTask(logger, taskId, FalconLogLevel.Error, "TaskCancelled: $reason")

      // 故障中、暂停中
      tr.status = TaskStatus.Cancelled
      tr.paused = false
      tr.future?.cancel(true)
    } else {
      logger.info("用户取消任务 '$taskId'，内存里没有")
      cleanTask(taskId)
    }

    updateTaskRecord(
      
      taskId,
      mutableMapOf(
        "status" to TaskStatus.Cancelled,
        "endedOn" to Date(),
        "endedReason" to reason,
        "paused" to false, // 抹去标记！
      ),
    )
    FalconEventBus.fire(taskId, "TaskCancelled")
  }

  private fun findTopTask(taskId: String): String {
    val tr = unfinishedTasks[taskId]
    return if (tr != null) {
      if (tr.isTopTask()) {
        tr.taskId
      } else {
        tr.getTopTask().taskId
      }
    } else {
      val ev = EntityRwService.findOne("FalconTaskRecord", Cq.eq("id", taskId))
        ?: throw BzError("errFalconTaskRecordNotFound", taskId)
      if ((ev["topTaskId"] as String?).isNullOrBlank()) {
        EntityHelper.mustGetId(ev)
      } else {
        ev["topTaskId"] as String
      }
    }
  }

  private fun findUnfinishedSubTask(taskId: String): List<String> = EntityRwService.findMany(
    
    "FalconTaskRecord",
    Cq.and(
      Cq.eq("topTaskId", taskId),
      Cq.include("status", listOf(TaskStatus.Created, TaskStatus.Started, TaskStatus.Failed)),
    ),
  ).map { EntityHelper.mustGetId(it) }

  fun pauseTask(taskId: String, reason: String?) {
    val topTaskId = findTopTask(taskId)
    logger.info("用户暂停任务 topTaskId=$topTaskId, taskId=$taskId")
    doPause(topTaskId, reason)
  }

  private fun doPause(taskId: String, reason: String?) {
    val tr = unfinishedTasks[taskId] ?: return
    // if (!tr.isTopTask()) return
    if (tr.paused) return
    if (tr.status >= TaskStatus.Done) return // TODO 为什么会发生

    LogManager.logTask(logger, taskId, FalconLogLevel.Info, "FalconTaskPause: $reason")

    tr.paused = true
    updateTaskRecord(taskId, mutableMapOf("paused" to true))

    updateUnfinishedSubtask(taskId, mutableMapOf("paused" to true))

    // TODO pauseVehicle(taskRuntime.taskId)
  }

  fun resumeTask(taskId: String) {
    val topTask = findTopTask(taskId)
    logger.info("用户继续执行暂定的任务 topTaskId=$topTask, taskId=$taskId")
    doResume(topTask)
  }

  private fun doResume(taskId: String) {
    val tr = unfinishedTasks[taskId] ?: return
    // if (!tr.isTopTask()) return
    if (!tr.paused) return

    LogManager.logTask(logger, taskId, FalconLogLevel.Info, "FalconTaskResume")
    tr.paused = false
    updateTaskRecord(taskId, mutableMapOf("paused" to false))

    // TODO resumeVehicle(taskRuntime.taskId)
  }

  fun recoveryErrorRun(taskId: String) {
    val topTaskId = findTopTask(taskId)
    logger.info("用户重试故障任务 topTaskId=$topTaskId, taskId=$taskId")
    doRecovery(topTaskId)
  }

  private fun doRecovery(taskId: String) {
    val tr = unfinishedTasks[taskId] ?: return
    // if (!tr.isTopTask()) return
    if (tr.status != TaskStatus.Failed && !tr.paused) return // 兼容故障之后重启 M4 后猎鹰任务状态由 “故障”变为“已开始 且 暂停” 的场景

    LogManager.logTask(logger, tr.taskId, FalconLogLevel.Info, "RecoveryError")

    // 获取最新定义
    tr.def = FalconTaskDefService.mustFetchLatestTaskDefById(tr.def.id)

    tr.status = TaskStatus.Created
    tr.paused = false
    // 任务
    updateTaskRecord(
      
      taskId,
      mutableMapOf("status" to TaskStatus.Created, "endedReason" to "", "paused" to false),
    )

    // 失败的块记录，修改状态，再继续执行
    // 注意是块！！！
    EntityRwService.updateMany(
      
      "FalconBlockRecord",
      Cq.and(listOf(Cq.eq("taskId", taskId), Cq.gt("status", BlockStatus.Done))),
      mutableMapOf("status" to BlockStatus.Created),
    )

    runTaskAsync(tr)

    FalconEventBus.fire(taskId, "TaskRecover")
  }

  fun suspendIfTaskPaused(taskRuntime: TaskRuntime) {
    val top = taskRuntime.getTopTask() // TODO 改成监听自己任务的
    while (SysEmc.isSysEmc() || top.paused) {
      try {
        Thread.sleep(200)
      } catch (e: InterruptedException) {
        return
      }
    }
  }

  private fun onTaskDone(taskRuntime: TaskRuntime) {
    unfinishedTasks.remove(taskRuntime.taskId)

    if (taskRuntime.status >= TaskStatus.Done) return // 注意取消也在里面

    val status = TaskStatus.Done
    val event = "TaskSuccess"

    LogManager.logTask(logger, taskRuntime.taskId, FalconLogLevel.Info, event)

    taskRuntime.status = status
    updateTaskRecord(
      
      taskRuntime.taskId,
      mutableMapOf(
        "status" to status,
        "endedReason" to "",
        "endedOn" to Date(),
        "paused" to false,
      ),
    )
    FalconEventBus.fire(taskRuntime.taskId, event)
  }

  /**
   * forcedEnd 不管什么错，强制终止
   */
  private fun onTaskError(taskRuntime: TaskRuntime, e: Exception, forcedEnd: Boolean) {
    // 不再需要恢复的错误
    if (forcedEnd || e is FatalError || e is TaskCancelledError) {
      unfinishedTasks.remove(taskRuntime.taskId)
      cleanTask(taskRuntime.taskId)
    }

    // if (taskRuntime.status == TaskStatus.Done) return

    // 错误在最内层打日志，外面不重复打
    val status: Int
    val endedReason = e.message ?: ""
    val event: String
    if (forcedEnd || e is FatalError) {
      status = TaskStatus.Aborted
      event = "TaskAborted"
    } else if (e is TaskCancelledError) {
      status = TaskStatus.Cancelled
      event = "TaskCancelled"
    } else {
      status = TaskStatus.Failed
      event = "TaskFail"
    }

    LogManager.logTask(logger, taskRuntime.taskId, FalconLogLevel.Info, event)

    taskRuntime.status = status

    if (status == TaskStatus.Failed) ++taskRuntime.failureNum

    updateTaskRecord(
      
      taskRuntime.taskId,
      mutableMapOf(
        "status" to status,
        "endedReason" to endedReason,
        "endedOn" to Date(),
        "paused" to false,
        "failureNum" to taskRuntime.failureNum,
      ),
    )
    FalconEventBus.fire(taskRuntime.taskId, event)
  }

  fun addResCleaner(resType: String, c: FalconResourceCleaner) {
    resCleaners.getOrPut(resType) { CopyOnWriteArrayList() } += c
  }

  private fun cleanTask(taskId: String) {
    val resEvList = EntityRwService.findMany("FalconTaskResource", Cq.eq("taskId", taskId))
    if (resEvList.isEmpty()) return
    for (resEv in resEvList) {
      val resType = resEv["resType"] as String
      val resId = resEv["resId"] as String
      val argsStr = resEv["args"] as String?
      val args: Map<String, Any?>? = if (argsStr.isNullOrBlank()) {
        null
      } else {
        JsonHelper.mapper.readValue(argsStr, jacksonTypeRef())
      }
      try {
        logger.info("清理猎鹰任务资源: $taskId, $resType, $resId")
        val list = resCleaners[resType] ?: continue
        for (c in list) c.invoke(taskId, resType, resId, args)
      } catch (e: Exception) {
        logger.error("清理猎鹰任务资源: $taskId, $resType, $resId", e)
      }
    }
    EntityRwService.removeMany("FalconTaskResource", Cq.eq("taskId", taskId))
  }

  private fun createTaskRecord(
    
    def: TaskDef,
    rootBlockStateId: String,
    inputParams: Map<String, Any?>,
    topTaskId: String?,
  ): String {
    val record: EntityValue = mutableMapOf(
      "rootBlockStateId" to rootBlockStateId,
      "defId" to def.id,
      "defVersion" to def.version,
      "defLabel" to def.label,
      "createdOn" to Date(),
      "status" to TaskStatus.Created,
      "topTaskId" to topTaskId,
      "subTask" to (topTaskId != null),
      "inputParams" to JsonHelper.writeValueAsString(inputParams),
    )
    return EntityRwService.createOne("FalconTaskRecord", record)
  }

  private fun restoreTaskRecord(record: EntityValue, topTask: TaskRuntime? = null): TaskRuntime {
    val defId = record["defId"] as String
    val def = FalconTaskDefService.mustFetchLatestTaskDefById(defId) // TODO 应该用最新的版本吗？

    val taskId = EntityHelper.mustGetId(record)
    val rootBlockStateId = record["rootBlockStateId"] as String

    val taskRuntime = TaskRuntime(taskId, rootBlockStateId, def, topTask)

    taskRuntime.status = record["status"] as Int
    taskRuntime.paused = record["paused"] as Boolean? ?: false

    val inputParamsStr = record["inputParams"] as String?
    if (!inputParamsStr.isNullOrBlank()) {
      val inputParams: Map<String, Any?> = JsonHelper.mapper.readValue(inputParamsStr, jacksonTypeRef())
      taskRuntime.inputParams.putAll(def.parseInputParams(inputParams))
    }

    val variablesStr = record["variables"] as String?
    if (!variablesStr.isNullOrBlank()) {
      val variables: Map<String, Any?> = JsonHelper.mapper.readValue(variablesStr, jacksonTypeRef())
      taskRuntime.variables.putAll(variables)
    }

    taskRuntime.failureNum = NumHelper.anyToInt(record["failureNum"]) ?: 0

    return taskRuntime
  }

  private fun updateTaskRecord(taskId: String, update: EntityValue) {
    EntityRwService.updateOne("FalconTaskRecord", Cq.idEq(taskId), update)
  }

  private fun updateUnfinishedSubtask(taskId: String, update: EntityValue) {
    EntityRwService.updateMany(
      
      "FalconTaskRecord",
      Cq.and(
        Cq.eq("topTaskId", taskId),
        Cq.include("status", listOf(TaskStatus.Created, TaskStatus.Started, TaskStatus.Failed)),
      ),
      update,
    )
  }

  fun setTaskVariable(taskRuntime: TaskRuntime, name: String, value: Any?) {
    taskRuntime.variables[name] = value
    val varString = JsonHelper.writeValueAsString(taskRuntime.variables)
    updateTaskRecord(taskRuntime.taskId, mutableMapOf("variables" to varString))
  }

  fun setTaskVariables(taskRuntime: TaskRuntime, variables: Map<String, Any?>) {
    taskRuntime.variables.putAll(variables)
    val varString = JsonHelper.writeValueAsString(taskRuntime.variables)
    updateTaskRecord(taskRuntime.taskId, mutableMapOf("variables" to varString))
  }

  fun getTaskVariable(taskRuntime: TaskRuntime, name: String): Any? = taskRuntime.variables[name]

  fun loadUnfinishedTasks() {
    
    val records = EntityRwService.findMany(
      
      "FalconTaskRecord",
      Cq.include("status", listOf(TaskStatus.Created, TaskStatus.Started, TaskStatus.Failed)),
    )
    if (records.isNotEmpty()) {
      logger.error("启动时发现未完成的任务数量：${records.size}")
    } else {
      return
    }
    // 先加载顶层任务，因为恢复子任务时需要
    for (record in records) {
      try {
        val subTask = record["subTask"] as Boolean? ?: false
        if (subTask) continue
        val tr = restoreTaskRecord(record, null)
        unfinishedTasks[tr.taskId] = tr

        // 暂停掉
        tr.paused = true
        updateTaskRecord(tr.taskId, mutableMapOf("paused" to true))

        // 重新等待运行
        runTaskAsync(tr)
      } catch (e: BzError) {
        logger.error("恢复任务失败：" + e.message)
      } catch (e: Exception) {
        logger.error("恢复任务失败", e)
      }
    }
    // 再加载子任务
    for (record in records) {
      val subTask = record["subTask"] as Boolean? ?: false
      if (!subTask) continue
      val topTaskId = record["topTaskId"] as String?
      if (topTaskId.isNullOrBlank()) continue // TODO 清理掉这种，否则会越攒越多
      val topTask = unfinishedTasks[topTaskId] ?: continue
      val tr = restoreTaskRecord(record, topTask)
      unfinishedTasks[tr.taskId] = tr
    }
  }

  // TODO? 这个方法干嘛的
  // fun generateOrRestoreBlockInternalVariables(blockRuntime: BlockRuntime, name: String): String? {
  //   var value = blockRuntime.internalVariables[name] as String?
  //   if (StringUtils.isBlank(value)) {
  //     value = IdHelper.uuidStr()
  //     setBlockInternalVariables(blockRuntime, java.util.Map.of<String, Any?>(name, value))
  //   }
  //   return value
  // }

  private fun updateAlarmLoop() {
    while (true) {
      updateAlarm()
      Thread.sleep(500)
    }
  }

  private fun updateAlarm() {
    
    val evList = EntityRwService.findMany(
      
      "FalconTaskRecord",
      Cq.eq("status", TaskStatus.Failed),
      FindOptions(projection = listOf("id", "defLabel", "endedReason")),
    )

    // 先清之前的
    AlarmService.removeAllByCode("FalconFailed")

    // 重新添加告警
    for (it in evList) {
      val defLabel = it["defLabel"] as String? ?: ""
      val endedReason = it["endedReason"] as String? ?: ""
      val id = EntityHelper.mustGetId(it)
      val ai = AlarmItem(
        group = "Falcon",
        code = "FalconFailed",
        key = "FalconFailed-$id", // 要唯一
        level = AlarmLevel.Error,
        message = lo("errFalconFailed", listOf(defLabel, id, endedReason)),
        args = listOf(id),
        // 下面注册了三个处理操作（界面上的三个按钮）：查看详情、故障重试、取消任务
        actions = listOf(
          AlarmAction(lo("btnViewDetail"), uiAction = "FalconViewTask"),
          AlarmAction(lo("btnFaultRetry")),
          AlarmAction(lo("btnCancelTask")),
        ),
      )
      AlarmService.addItem(ai) { actionIndex: Int, args: List<Any?> ->
        // 用户点击按钮后，处理告警
        if (actionIndex == 1) {
          // 故障重试
          // 先移除告警，如果后续还有问题，下一轮再产生
          AlarmService.removeItem(ai.key)
          recoveryErrorRun(args[0] as String)
        } else if (actionIndex == 2) {
          // 取消任务
          // 先移除告警，如果后续还有问题，下一轮再产生
          AlarmService.removeItem(ai.key)
          cancelTask(args[0] as String, "ByAlarmAction")
        }
      }
    }
  }

  /**
   * 删除猎鹰任务记录
   *
   * - 只能删除未执行、完成和取消的猎鹰任务
   * - 无论选择的是父任务、子任务，都会删除同一顶级父任务下的所有任务，校验同理
   */
  private fun removeTaskRecords(taskIds: List<String>) {
    val topTaskIds = taskIds.map { findTopTask(it) }.distinct()
    val recordIds = EntityRwService.findMany(
      
      "FalconTaskRecord",
      Cq.and(
        Cq.or(Cq.include("topTaskId", topTaskIds), Cq.include("id", topTaskIds)),
        Cq.include("status", listOf(TaskStatus.Started, TaskStatus.Failed)), // Created 是刚创建未开始执行的，也可删除
      ),
    ).map { EntityHelper.mustGetId(it) }
    if (recordIds.isNotEmpty()) {
      val recordIdStr = recordIds.joinToString(", ")
      throw BzError("errFalconRecordRemoveRunning", lo("errFalconRecordRemoveRunning", listOf(recordIdStr)))
    }

    EntityRwService.removeMany(
      
      "FalconTaskRecord",
      Cq.or(Cq.include("topTaskId", topTaskIds), Cq.include("id", topTaskIds)),
      RemoveOptions(muteExt = true), // 防止重复触发 EntityServiceExtension
    )
  }

  // TODO before vs after
  override fun beforeRemoving(em: EntityMeta, ids: List<String>): Long? {
    if (em.name != "FalconTaskRecord") return null
    highTimeSensitiveExecutor.submitCatch("Remove falcon task", logger) {
      removeTaskRecords(ids)
    }

    return null
  }
}

typealias FalconResourceCleaner = (
  
  taskId: String,
  resType: String,
  resId: String,
  args: Map<String, Any?>?,
) -> Unit