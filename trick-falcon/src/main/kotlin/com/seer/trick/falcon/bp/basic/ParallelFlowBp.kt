package com.seer.trick.falcon.bp.basic

import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockChildDef
import com.seer.trick.falcon.domain.BlockDef

class ParallelFlowBp : AbstractBp() {

  override fun process() {
    val childrenConfigs = defaultChildrenConfig
    parallelProcessChildren(childrenConfigs, CHILD_DEFAULT)
  }

  companion object {

    val def = BlockDef(
      ParallelFlowBp::class.simpleName!!,
      color = "#E19898",
      children = listOf(BlockChildDef(CHILD_DEFAULT)),
    )
  }
}