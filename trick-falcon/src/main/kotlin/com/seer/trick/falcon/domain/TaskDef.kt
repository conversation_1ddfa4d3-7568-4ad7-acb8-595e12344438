package com.seer.trick.falcon.domain

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.helper.BoolHelper
import com.seer.trick.helper.JsonHelper
import com.seer.trick.helper.NumHelper
import java.util.*

data class TaskDef(
  val id: String = "",
  val version: Int = 0,
  val label: String = "",
  val builtin: Boolean = false,
  val inputParams: List<TaskInputParamDef> = emptyList(),
  val outputParams: List<TaskOutputParamDef> = emptyList(),
  val taskVariables: List<TaskVariablesDef> = emptyList(),
  val rootBlock: BlockConfig = BlockConfig(),
  @JsonInclude(JsonInclude.Include.NON_NULL)
  val modifiedOn: Date? = null,
  val modifiedBy: String = "",
  @JsonInclude(JsonInclude.Include.NON_NULL)
  val createdOn: Date? = null,
  val createdBy: String = "",
  val group: String = "",
) {

  fun parseInputParams(values: Map<String, Any?>): Map<String, Any?> {
    val params = mutableMapOf<String, Any?>()
    for (param in inputParams) {
      val inputValue = values[param.name]
      val paramValue = if (inputValue is String) {
        if ((values[param.name] as String?).isNullOrBlank()) {
          param.defaultValue
        } else {
          values[param.name]
        }
      } else {
        values[param.name] ?: param.defaultValue
      }
      val value = when (param.type) {
        BlockParamType.Boolean -> BoolHelper.anyToBool(paramValue)
        BlockParamType.Long -> NumHelper.anyToLong(paramValue)
        BlockParamType.Double -> NumHelper.anyToDouble(paramValue)
        BlockParamType.String -> paramValue
        BlockParamType.JSONObject -> {
          if (paramValue is String) {
            if (paramValue.isNotBlank()) {
              JsonHelper.mapper.readValue<Map<String, Any?>>(paramValue, jacksonTypeRef())
            } else {
              emptyMap()
            }
          } else {
            paramValue // TODO JSONNode
          }
        }
        BlockParamType.JSONArray -> {
          if (paramValue is String) {
            if (paramValue.isNotBlank()) {
              JsonHelper.mapper.readValue<List<Any?>>(paramValue, jacksonTypeRef())
            } else {
              emptyList()
            }
          } else {
            paramValue // TODO JSONNode
          }
        }
        BlockParamType.Any -> paramValue
      }
      params[param.name] = value
    }
    return params
  }
}

data class TaskInputParamDef(
  val name: String = "",
  val type: BlockParamType = BlockParamType.String,
  val label: String = "",
  val remark: String = "",
  val required: Boolean = false,
  val defaultValue: Any? = null,
  val objectTypes: List<String>? = null,
)

data class TaskOutputParamDef(
  val name: String = "",
  val type: BlockParamType = BlockParamType.String,
  val label: String = "",
  val remark: String = "",
  val refBlockConfigId: Int = 0,
  val refOutputParamName: String = "",
  val objectTypes: List<String>? = null,
)

data class BlockConfig(
  val id: Int = 0,
  val name: String = "",
  val blockType: String = "",
  val refTaskDefId: String = "",
  val remark: String? = null,
  val disabled: Boolean? = null,
  val inputParams: Map<String, BlockInputParamConfig> = emptyMap(),
  val children: Map<String, List<BlockConfig>> = emptyMap(),
  // 前端用
  val color: String? = null,
)

data class BlockInputParamConfig(
  val type: BlockInputParamItemType = BlockInputParamItemType.Simple,
  val value: Any? = null,
  val ref: BlockInputParamConfigRef? = null,
)

data class BlockInputParamConfigRef(
  val source: String = "",
  val name: String = "",
)

enum class BlockInputParamItemType {
  Simple,
  Expression,
  Ref,
}

data class TaskVariablesDef(
  val name: String = "",
  val type: BlockParamType = BlockParamType.String,
  val defaultValue: Any? = null,
  val objectTypes: List<String>? = null,
  val remark: String = "",
)