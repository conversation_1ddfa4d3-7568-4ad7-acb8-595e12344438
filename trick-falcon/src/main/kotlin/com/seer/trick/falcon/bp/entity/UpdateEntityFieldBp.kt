package com.seer.trick.falcon.bp.entity

import com.seer.trick.Cq

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.reslock.ResLockService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*
import kotlin.concurrent.withLock

class UpdateEntityFieldBp : AbstractBp() {

  override fun process() {
    val entityName = mustGetBlockInputParam("entityName") as String
    val qf = mustGetBlockInputParam("queryField") as String
    val qft = mustGetBlockInputParam("queryFieldType") as String
    val qv = mustGetBlockInputParam("queryValue", BlockParamType.valueOf(qft))
    val uf = mustGetBlockInputParam("updateField") as String
    val uft = mustGetBlockInputParam("updateFieldType") as String
    val uv = getBlockInputParam("updateValue", BlockParamType.valueOf(uft))
    val updateMany = getBlockInputParamAsBool("updateMany")
    val setToNull = getBlockInputParamAsBool("setToNull")

    val update: EntityValue = mutableMapOf(uf to if (setToNull) null else uv)

    // 防止并发修改库位库存、库存明细等资源
    ResLockService.resLock.withLock {
      val c = if (updateMany) {
        EntityRwService.updateMany(entityName, Cq.eq(qf, qv), update)
      } else {
        EntityRwService.updateOne(entityName, Cq.eq(qf, qv), update)
      }

      logger.info("UpdateEntityFieldBp update $c rows")
    }
  }

  companion object {

    val options = listOf(
      BlockInputParamOption(BlockParamType.String.name, BlockParamType.String.name),
      BlockInputParamOption(BlockParamType.Boolean.name, BlockParamType.Boolean.name),
      BlockInputParamOption(BlockParamType.Long.name, BlockParamType.Long.name),
      BlockInputParamOption(BlockParamType.Double.name, BlockParamType.Double.name),
      BlockInputParamOption(BlockParamType.JSONObject.name, BlockParamType.JSONObject.name),
      BlockInputParamOption(BlockParamType.JSONArray.name, BlockParamType.JSONArray.name),
      BlockInputParamOption(BlockParamType.Any.name, BlockParamType.Any.name),
    )
    val def = BlockDef(
      UpdateEntityFieldBp::class.simpleName!!,
      color = "#98fb98",
      inputParams = listOf(
        BlockInputParamDef(
          "entityName",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.EntityName),
        ),
        BlockInputParamDef(
          "queryField",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.EntityFieldName),
        ),
        BlockInputParamDef("queryFieldType", BlockParamType.String, true, options = options),
        BlockInputParamDef("queryValue", BlockParamType.Any, true),
        BlockInputParamDef(
          "updateField",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.EntityFieldName),
        ),
        BlockInputParamDef("updateFieldType", BlockParamType.String, true, options = options),
        BlockInputParamDef("updateValue", BlockParamType.Any, false),
        BlockInputParamDef("updateMany", BlockParamType.Boolean, false),
        BlockInputParamDef(
          "setToNull",
          BlockParamType.Boolean,
          false,
        ),
      ),
    )
  }
}