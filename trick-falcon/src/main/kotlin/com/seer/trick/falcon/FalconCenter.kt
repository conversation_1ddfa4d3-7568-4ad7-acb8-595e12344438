package com.seer.trick.falcon

import com.seer.trick.BzError
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.event.EventListener
import com.seer.trick.base.user.Operator
import com.seer.trick.falcon.bp.*
import com.seer.trick.falcon.bp.basic.*
import com.seer.trick.falcon.bp.conditionloop.*
import com.seer.trick.falcon.bp.entity.FindFieldValueByIdBp
import com.seer.trick.falcon.bp.entity.FindOneEntityByIdBp
import com.seer.trick.falcon.bp.entity.UpdateEntityFieldBp
import com.seer.trick.falcon.bp.entity.UpdateOneEntityByIdBp
import com.seer.trick.falcon.bp.falcon.FindFalconRecordFieldBp
import com.seer.trick.falcon.bp.falcon.UpdateFalconRecordFieldBp
import com.seer.trick.falcon.domain.BlockConfig
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockDefGroup
import com.seer.trick.falcon.task.TaskRuntime
import com.seer.trick.helper.JsonFileHelper
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.io.File
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import kotlin.reflect.KClass
import kotlin.reflect.full.primaryConstructor

object FalconCenter : EventListener<String> {
  
  private val logger: Logger = LoggerFactory.getLogger(javaClass)

  private val blockProcessorMap: MutableMap<String, KClass<out AbstractBp>> = ConcurrentHashMap()
  private val blockDefMap: MutableMap<String, BlockDef> = ConcurrentHashMap()

  val extBlockDefMap: MutableMap<String, BlockDef> = ConcurrentHashMap()

  const val EXT_BLOCK_NAME_PREFIX = "EXT::"

  const val SUB_TASK_BLOCK_NAME_PREFIX = "SubTask::"

  private val blockGroups: MutableList<BlockDefGroup> = Collections.synchronizedList(ArrayList())

  init {
    BzConfigManager.eventBus.listeners += this
    val hiddenBlocks = listOf(
      registerBp(RootBp::class, RootBp.def),
      registerBp(SubTaskBp::class, SubTaskBp.def),
    )
    hiddenBlocks.forEach { it.color = "rgba(0,102,255,0.5)" }
    registerBpGroup(BlockDefGroup("Hidden", "", 0, true, hiddenBlocks))

    val basicBlocks = listOf(
      registerBp(SimpleScriptBp::class, SimpleScriptBp.def),
      registerBp(ExpressionBp::class, ExpressionBp.def),
      registerBp(PrintBp::class, PrintBp.def),
      registerBp(DelayBp::class, DelayBp.def),
      registerBp(SerialFlowBp::class, SerialFlowBp.def),
      registerBp(ParallelFlowBp::class, ParallelFlowBp.def),
      registerBp(SetTaskVariableBp::class, SetTaskVariableBp.def),
      registerBp(SetGlobalVariableBp::class, SetGlobalVariableBp.def),
      registerBp(TriggerTaskEventBp::class, TriggerTaskEventBp.def),
      registerBp(TryCatchBp::class, TryCatchBp.def),
      registerBp(AbortTaskBp::class, AbortTaskBp.def),
      registerBp(PadIntStrBp::class, PadIntStrBp.def),
      registerBp(ThrowBp::class, ThrowBp.def),
      registerBp(TimestampBp::class, TimestampBp.def),
      registerBp(SuiBp::class, SuiBp.def),
      registerBp(WebSocketBp::class, WebSocketBp.def),
    )
    basicBlocks.forEach { it.color = "rgba(0,102,255, 0.3)" }
    registerBpGroup(BlockDefGroup("Basic", "", 1, false, basicBlocks))

    val entityBlocks = listOf(
      registerBp(FindOneEntityByIdBp::class, FindOneEntityByIdBp.def),
      registerBp(UpdateOneEntityByIdBp::class, UpdateOneEntityByIdBp.def),
      registerBp(UpdateEntityFieldBp::class, UpdateEntityFieldBp.def),
      registerBp(FindFieldValueByIdBp::class, FindFieldValueByIdBp.def),
    )
    entityBlocks.forEach { it.color = "rgba(0,102,255,0.2)" }
    registerBpGroup(BlockDefGroup("Entity", "", 10, false, entityBlocks))

    val conditionLoopBlocks = listOf(
      registerBp(IfBp::class, IfBp.def),
      registerBp(IfElseBp::class, IfElseBp.def),
      registerBp(IterateListBp::class, IterateListBp.def),
      registerBp(RepeatNumBp::class, RepeatNumBp.def),
      registerBp(WhileBp::class, WhileBp.def),
      registerBp(BreakBp::class, BreakBp.def),
    )
    conditionLoopBlocks.forEach { it.color = "rgba(0,102,255, 0.25)" }
    registerBpGroup(BlockDefGroup("ConditionAndLoop", "", 5, false, conditionLoopBlocks))

    val falconBlocks = listOf(
      registerBp(FindFalconRecordFieldBp::class, FindFalconRecordFieldBp.def),
      registerBp(UpdateFalconRecordFieldBp::class, UpdateFalconRecordFieldBp.def),
    )
    falconBlocks.forEach { it.color = "rgba(156,147,253, 0.70)" }
    registerBpGroup(BlockDefGroup("FalconRecord", "", 80, false, falconBlocks))
  }

  fun listGroups(): List<BlockDefGroup> = blockGroups.sortedBy { it.order }

  fun registerBpGroup(bg: BlockDefGroup) {
    FalconI18N.loBlockDefGroup(bg)
    blockGroups += bg
  }

  fun registerBp(bpCls: KClass<out AbstractBp>, def: BlockDef): BlockDef {
    FalconI18N.loBlockDef(def)
    blockProcessorMap[def.name] = bpCls
    blockDefMap[def.name] = def
    return def
  }

  /**
   * 抛出异常：AbstractBp 的 run 方法抛出的异常
   */
  fun runBp(
    
    blockConfig: BlockConfig,
    recordId: String,
    blockContext: BlockContext,
    taskRuntime: TaskRuntime,
  ): AbstractBp? {
    if (blockConfig.disabled == true) return null
    return if (blockConfig.blockType.startsWith(SUB_TASK_BLOCK_NAME_PREFIX)) {
      val refId = blockConfig.blockType.substringAfter(SUB_TASK_BLOCK_NAME_PREFIX)
      val bc2 = blockConfig.copy(blockType = SubTaskBp.def.name, refTaskDefId = refId)
      val bp = SubTaskBp()
      bp.run(bc2, SubTaskBp.def, recordId, blockContext, taskRuntime)
      bp
    } else if (blockConfig.blockType.startsWith(EXT_BLOCK_NAME_PREFIX)) {
      val def = extBlockDefMap[blockConfig.blockType] ?: throw BzError("errNoBpType", blockConfig.blockType)
      val bp = ExtBp()
      bp.run(blockConfig, def, recordId, blockContext, taskRuntime)
      bp
    } else {
      val def = blockDefMap[blockConfig.blockType] ?: throw BzError("errNoBpType", blockConfig.blockType)
      val bpCls = blockProcessorMap[blockConfig.blockType] ?: throw BzError("errNoBpType", blockConfig.blockType)
      val bp = bpCls.primaryConstructor!!.call()
      bp.run(blockConfig, def, recordId, blockContext, taskRuntime)
      bp
    }
  }

  /**
   * 加载所有定制组件
   */
  fun loadExtBp() {
    extBlockDefMap.clear()

    val defList: List<BlockDef>? = JsonFileHelper.readJsonFromFile(getExtBpFile())
    if (!defList.isNullOrEmpty()) {
      for (def in defList) extBlockDefMap[EXT_BLOCK_NAME_PREFIX + def.name] = def
    }
  }

  /**
   * 保存一个定制组件；可能是新增也可能是修改。
   */
  fun saveExtBp(def: BlockDef) {
    val oldBlockDef = extBlockDefMap[EXT_BLOCK_NAME_PREFIX + def.name]

    // 保存历史版本（如果存在旧版本）
    oldBlockDef?.let {
      JsonFileHelper.writeJsonToFile(getExtBpRevisionFile(it.name, it.version), it, true)
    }

    val op = Operator.current()
    val blockDef = def.copy(
      version = (oldBlockDef?.version ?: 0) + 1,
      createdBy = oldBlockDef?.createdBy ?: op?.username ?: "",
      createdOn = oldBlockDef?.createdOn ?: Date(),
      modifiedBy = op?.username ?: "",
      modifiedOn = Date(),
    )
    extBlockDefMap[EXT_BLOCK_NAME_PREFIX + blockDef.name] = blockDef

    saveExtBp()
  }

  fun removeExtBp(names: List<String>) {
    for (name in names) {
      extBlockDefMap.remove(EXT_BLOCK_NAME_PREFIX + name)
    }
    saveExtBp()
  }

  /**
   * 按名字列出一个组件的所有修订版本
   */
  fun listExtBpRevisions(name: String): List<ExtBpRevision> {
    val extBpDir = ensureExtBpRevisionDir()

    return extBpDir.listFiles { file -> file.name.startsWith("$name-") }?.mapNotNull {
      JsonFileHelper.readJsonFromFile<ExtBpRevision>(it)
    } ?: emptyList()
  }

  /**
   * 加载读取组件的历史修订版本
   *
   */
  fun loadExtBpRevision(name: String, version: Int): BlockDef {
    val targetFile = getExtBpRevisionFile(name, version)
    return JsonFileHelper.readJsonFromFile(targetFile)
      ?: throw BzError("errFileNotExists", targetFile.name)
  }

  /**
   * 恢复到指定组件的指定版本
   *
   */
  fun restoreExtBpRevision(name: String, version: Int) {
    logger.error("还原指定版本的组件 name = '$name' version = '$version'")

    val targetFile = getExtBpRevisionFile(name, version)

    val extBp: BlockDef = JsonFileHelper.readJsonFromFile(targetFile)
      ?: throw BzError("errFileNotExists", targetFile.name)
    saveExtBp(extBp)
  }

  private fun saveExtBp() {
    JsonFileHelper.writeJsonToFile(getExtBpFile(), extBlockDefMap.values, true)
  }

  // 所有自定义组件的存储文件
  private fun getExtBpFile(): File = File(BaseCenter.baseConfig.configDir, "falcon-ext-bp.json")

  // 根据组件名和版本获取历史版本文件
  private fun getExtBpRevisionFile(name: String, version: Int): File =
    File(ensureExtBpRevisionDir(), "$name-$version.json")

  private fun ensureExtBpRevisionDir(): File {
    val dir = File(BaseCenter.baseConfig.configDir, "falcon-bp-revision")
    dir.mkdirs()
    return dir
  }

  override fun onEvent(e: String) {
    blockGroups.forEach {
      FalconI18N.loBlockDefGroup(it)
      it.blocks.forEach { bp -> FalconI18N.loBlockDef(bp) }
    }
  }
}

data class ExtBpRevision(
  val name: String = "",
  val label: String = "",
  val version: Int? = null,
  val modifiedOn: Date? = null,
  val modifiedBy: String = "",
  val createdOn: Date? = null,
  val createdBy: String = "",
)