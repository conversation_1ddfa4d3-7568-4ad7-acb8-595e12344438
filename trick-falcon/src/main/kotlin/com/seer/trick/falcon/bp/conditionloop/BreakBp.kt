package com.seer.trick.falcon.bp.conditionloop


import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.falcon.domain.BreakLoop

class BreakBp : AbstractBp() {

  override fun process() {
    val condition = mustGetBlockInputParam("condition") as <PERSON><PERSON><PERSON>
    if (condition) {
      logger.info("Terminate the loop")
      throw BreakLoop()
    }
  }

  companion object {

    private val condition = BlockInputParamDef("condition", BlockParamType.Boolean, true)

    val def = BlockDef(
      BreakBp::class.simpleName!!,
      color = "#9EC8B9",
      inputParams = listOf(condition),
    )
  }
}