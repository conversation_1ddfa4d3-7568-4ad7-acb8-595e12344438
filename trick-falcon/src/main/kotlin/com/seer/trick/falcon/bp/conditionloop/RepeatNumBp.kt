package com.seer.trick.falcon.bp.conditionloop


import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.bp.BlockContext
import com.seer.trick.falcon.domain.BlockChildDef
import com.seer.trick.falcon.domain.BlockContextVariableDef
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.falcon.domain.BreakLoop

class RepeatNumBp : AbstractBp() {

  override fun process() {
    val num = mustGetBlockInputParamAsLong("num")
    if (num <= 0) return
    val children = blockConfig.children[CHILD_DEFAULT] ?: return
    for (index in 0 until num) {
      val context = BlockContext(blockContext)
      context.setContextVariables(this, mapOf("index" to index))
      try {
        serialRunChildren(children, index.toString(), context)
      } catch (e: BreakLoop) {
        break
      }
    }
  }

  companion object {
    val def = BlockDef(
      RepeatNumBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(BlockInputParamDef("num", BlockParamType.Long, true)),
      contextVariables = listOf(BlockContextVariableDef("index", BlockParamType.Long)),
      children = listOf(BlockChildDef(CHILD_DEFAULT)),
    )
  }
}