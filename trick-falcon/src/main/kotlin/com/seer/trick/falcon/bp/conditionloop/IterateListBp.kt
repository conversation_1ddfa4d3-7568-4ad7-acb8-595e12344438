package com.seer.trick.falcon.bp.conditionloop


import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.bp.BlockContext
import com.seer.trick.falcon.domain.*

class IterateListBp : AbstractBp() {

  override fun process() {
    val arr = getBlockInputParamAsList(ipList.name)
      ?: throw MissingBlockInputParamError(blockConfig.name, ipList.name) // TODO 封装到一起，传、检查
    val parallel = getBlockInputParam(ipParallel.name) as Boolean? ?: false

    val children = defaultChildrenConfig

    if (parallel) {
      // TODO 并行时无法终止后续遍历，只能终止遍历中的后续步骤
      parallelWorks(arr) { i, item ->
        val context = BlockContext(blockContext)
        context.setContextVariables(this, mapOf(ctxIndex to i, ctxItem to item))
        serialRunChildren(children, i.toString(), context)
      }
    } else {
      for (i in arr.indices) {
        // TODO 使用块日志，记录这次循环
        val context = BlockContext(blockContext)
        context.setContextVariables(this, mapOf(ctxIndex to i, ctxItem to arr[i]))
        try {
          serialRunChildren(children, i.toString(), context)
        } catch (e: BreakLoop) {
          break
        }
      }
    }
  }

  companion object {

    private val ipList = BlockInputParamDef("list", BlockParamType.JSONArray, true)
    private val ipParallel = BlockInputParamDef("parallel", BlockParamType.Boolean)

    private const val ctxIndex = "index"
    private const val ctxItem = "item"

    val def = BlockDef(
      IterateListBp::class.simpleName!!,
      color = "#A5B6DD",
      inputParams = listOf(ipList, ipParallel),
      contextVariables = listOf(
        BlockContextVariableDef(ctxIndex, BlockParamType.Long),
        BlockContextVariableDef(ctxItem, BlockParamType.Any),
      ),
      children = listOf(
        BlockChildDef(CHILD_DEFAULT),
      ),
    )
  }
}