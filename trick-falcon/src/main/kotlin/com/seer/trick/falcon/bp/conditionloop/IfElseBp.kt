package com.seer.trick.falcon.bp.conditionloop


import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockChildDef
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType

class IfElseBp : AbstractBp() {

  override fun process() {
    val condition = getOrSetBlockInternalVariable("conditionResult") {
      getBlockInputParam(ipCondition.name)
    } as Boolean

    if (condition) {
      val children = blockConfig.children["true"] ?: return
      // 注意，块上下文不变
      serialRunChildren(children, "true", blockContext)
    } else {
      val children = blockConfig.children["false"] ?: return
      // 注意，块上下文不变
      serialRunChildren(children, "false", blockContext)
    }
  }

  companion object {

    private val ipCondition = BlockInputParamDef("condition", BlockParamType.Boolean, true)

    val def = BlockDef(
      IfElseBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(ipCondition),
      children = listOf(BlockChildDef("true"), BlockChildDef("false")),
    )
  }
}