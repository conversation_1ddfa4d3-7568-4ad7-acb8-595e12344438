package com.seer.trick.falcon.bp

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.*
import com.seer.trick.base.config.BzConfigManager
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.http.WebSocketManager
import com.seer.trick.base.http.WsMsg
import com.seer.trick.falcon.*
import com.seer.trick.falcon.FalconConcurrentCenter.falconMainExecutor
import com.seer.trick.falcon.domain.*
import com.seer.trick.falcon.domain.FatalError
import com.seer.trick.falcon.task.*
import com.seer.trick.helper.*
import java.util.Collections
import java.util.Date
import java.util.concurrent.*
import org.apache.commons.collections4.IteratorUtils
import org.apache.commons.lang3.StringUtils
import org.mvel2.MVEL
import org.mvel2.PropertyAccessException
import org.slf4j.Logger
import org.slf4j.LoggerFactory

abstract class AbstractBp {

  protected val logger: Logger = LoggerFactory.getLogger(javaClass)

  lateinit var blockConfig: BlockConfig
  protected lateinit var def: BlockDef
  private lateinit var recordId: String
  lateinit var blockContext: BlockContext
  lateinit var taskRuntime: TaskRuntime

  private lateinit var logHead: String

  @Volatile
  var status: Int = BlockStatus.Created

  val outputParams: MutableMap<String, Any?> = Collections.synchronizedMap(HashMap())
  protected val internalVariables: MutableMap<String, Any?> = Collections.synchronizedMap(HashMap())

  abstract fun process()

  val defaultChildrenConfig: List<BlockConfig>
    get() {
      return blockConfig.children[CHILD_DEFAULT] ?: throw BzError("errBpNoChild", blockConfig.name)
    }

  /**
   * 异常：只抛出 TaskCancelledError / FatalError / BlockError
   */
  fun run(
    
    blockConfig: BlockConfig,
    def: BlockDef,
    recordId: String,
    blockContext: BlockContext,
    taskRuntime: TaskRuntime,
  ) {
    this.blockConfig = blockConfig
    this.def = def
    this.recordId = recordId
    this.blockContext = blockContext
    this.taskRuntime = taskRuntime

    logHead = "${blockConfig.blockType}|$recordId"

    val topTr = taskRuntime.getTopTask()

    val start = System.currentTimeMillis()

    try {
      // 持久化 block；恢复记录状态
      ensureRecord()

      topTr.throwIfCancelledOrAborted() // top?

      if (status < BlockStatus.Done) {
        FalconTaskService.suspendIfTaskPaused(topTr)

        logger.debug("BlockStart")
        markBlockState(BlockStatus.Started, null, start)
        persistBlockInputParams()
        process()

        topTr.throwIfCancelledOrAborted()

        logger.debug("BlockDone")
        markBlockState(BlockStatus.Done, "", start)
      } else if (status == BlockStatus.Done && blockConfig.children.isNotEmpty()) {
        reloadChildrenBlockRecord(recordId)
      }

      blockContext.addOutputParamsToExpressionCtx(this)
    } catch (e: InterruptedException) {
      if (topTr.status == TaskStatus.Cancelled) {
        log(FalconLogLevel.Error, "TaskCancelled")
        markBlockState(BlockStatus.Cancelled, null, start)
        throw TaskCancelledError()
      } else if (topTr.status == TaskStatus.Aborted) {
        log(FalconLogLevel.Error, "TaskAborted")
        markBlockState(BlockStatus.Aborted, null, start)
        throw FatalError("")
      } else {
        // 不应该出现
        log(FalconLogLevel.Error, "BlockInterruptedException")
        markBlockState(BlockStatus.Failed, "InterruptedException", start)
        throw e
      }
    } catch (e: TaskCancelledError) {
      log(FalconLogLevel.Error, "TaskCancelled")
      markBlockState(BlockStatus.Cancelled, null, start)
      throw e
    } catch (e: FatalError) {
      log(FalconLogLevel.Error, "FatalError")
      markBlockState(BlockStatus.Aborted, e.message ?: "", start)
      throw e
    } catch (e: BlockError) {
      markBlockState(BlockStatus.Failed, e.parent.message, start)
      throw e
    } catch (e: BzError) {
      if (e is BreakLoop) {
        markBlockState(BlockStatus.Done, "", start)
        throw e
      } else {
        log(FalconLogLevel.Error, e.getTypeMessage())
        markBlockState(BlockStatus.Failed, e.message, start)
        throw BlockError(e)
      }
    } catch (e: Throwable) {
      // 只打异常栈
      log(FalconLogLevel.Error, e.getTypeMessage())
      logger.error("[$logHead]", e)
      // 在这里额外判断取消的原因是，当响应中断时，抛出的异常不一定是 InterruptedException 或者 InterruptedIOException
      // 譬如在执行脚本时响应中断抛出的异常为 org.graalvm.polyglot.PolyglotException:sleep interrupted
      // 在执行 sql 时抛出的异常可能是 java.sql.SQLTransientConnectionException：Connection closed by unknown interrupt
      if (topTr.status == TaskStatus.Cancelled) {
        log(FalconLogLevel.Error, "TaskCancelled")
        markBlockState(BlockStatus.Cancelled, null, start)
        throw TaskCancelledError()
      }
      markBlockState(BlockStatus.Failed, e.getTypeMessage(), start)
      throw BlockError(e)
    }
  }

  private fun markBlockState(status: Int, endReason: String?, start: Long) {
    if (this.status >= BlockStatus.Done) return // 并发可能引起多次结束
    this.status = status

    val cost = System.currentTimeMillis() - start

    val update: EntityValue = mutableMapOf("status" to status)
    if (status == BlockStatus.Started) {
      update["startedOn"] = Date()
    } else if (status == BlockStatus.Done || status == BlockStatus.Aborted) {
      update["endedOn"] = Date()
      update["endedReason"] = endReason
      update["cost"] = cost
    }

    if (status == BlockStatus.Failed) {
      val record = EntityRwService.findOneById("FalconBlockRecord", recordId)
      update["failureNum"] = 1 + (NumHelper.anyToInt(record?.get("failureNum")) ?: 0)
    }

    persistBlockRecord(update)

    fireBlockLifetimeEvent(status)
  }

  /**
   * 由于可能停用块，children 与返回的 bp 列表可能不对应
   */
  protected fun serialRunChildren(
    
    children: List<BlockConfig>,
    contextKey: String,
    blockContext: BlockContext,
  ): List<AbstractBp> {
    val childrenStateIds = ensureChildrenStateIds(children, contextKey)

    return children.mapIndexed { i, childConfig ->
      val stateId = childrenStateIds[i]
      FalconCenter.runBp(childConfig, stateId, blockContext, taskRuntime)
    }.filterNotNull()
  }

  protected fun parallelProcessChildren(children: List<BlockConfig>, contextKey: String) {
    val childrenStateIds = ensureChildrenStateIds(children, contextKey)

    parallelWorks(children) { index, childConfig ->
      val stateId = childrenStateIds[index]
      FalconCenter.runBp(childConfig, stateId, blockContext, taskRuntime)
    }
  }

  protected fun <T> parallelWorks(items: List<T>, worker: (index: Int, item: T) -> Unit) {
    val completionService = ExecutorCompletionService<Unit>(falconMainExecutor)
    val futures = items.mapIndexed { index, item ->
      completionService.submit {
        worker.invoke(index, item)
      }
    }
    try {
      for (i in items.indices) { // Wait for all tasks to complete.
        try {
          val future = completionService.take() // Take the first completed task.
          future.get() // Will throw exception if the task failed.
        } catch (e: ExecutionException) {
          throw e.cause ?: e // Extract the original exception.
        } catch (e: CancellationException) {
          logger.error("$logHead parallelWorks, CancellationException, throw TaskCancelledAbortedError")
          throw TaskCancelledError() // Custom exception for task cancellation.
        } catch (e: InterruptedException) {
          logger.error("$logHead parallelWorks, InterruptedException, throw TaskCancelledAbortedError")
          throw TaskCancelledError() // Custom exception for interruption.
        }
      }
    } catch (e: Throwable) {
      // Handle any uncaught exceptions and ensure all other futures are cancelled.
      if (e is TaskCancelledError || e is FatalError) {
        // Cancel any remaining tasks (those that are not yet done or cancelled).
        for (future in futures) {
          if (!future.isDone && !future.isCancelled) {
            future.cancel(true)
          }
        }
      }
      throw e // Re-throw the exception after handling.
    }
  }

  private fun ensureChildrenStateIds(children: List<BlockConfig>, contextKey: String): List<String> {
    val idRows = EntityRwService.findMany(
      
      "FalconBlockChildId",
      Cq.and(listOf(Cq.eq("blockId", recordId), Cq.eq("contextKey", contextKey))),
      FindOptions(sort = listOf("index")),
    ).map(EntityHelper::mustGetId)
    // 由于可能会有故障重试，且故障重试时可能改变猎鹰任务的结构
    // 所以可能的状态如下
    // idRows 为空，idRows 不为空且与 children 一致，idRows 不为空且不与 children 一致
    if (idRows.size == children.size) {
      return idRows
    }

    if (idRows.isNotEmpty()) {
      // If there are existing IDs that don't match the size, clear them
      EntityRwService.removeMany(
        
        "FalconBlockChildId",
        Cq.and(listOf(Cq.eq("blockId", recordId), Cq.eq("contextKey", contextKey))),
      )
    }

    val idList = List(children.size) { IdHelper.oidStr() }
    val rows: List<EntityValue> = idList.mapIndexed { i, id ->
      mutableMapOf(
        "id" to id,
        "taskId" to taskRuntime.taskId,
        "blockId" to recordId,
        "contextKey" to contextKey,
        "index" to i,
        "childId" to id, // TODO 多余
      )
    }
    EntityRwService.createMany("FalconBlockChildId", rows)
    return idList
  }

  protected fun setBlockInternalVariables(variables: Map<String, Any?>) {
    internalVariables.putAll(variables)
    persistBlockInternalVariables()
  }

  protected fun removeBlockInternalVariables(key: String) {
    internalVariables.remove(key)
    persistBlockInternalVariables()
  }

  fun getOrSetBlockInternalVariable(name: String, generator: () -> Any?): Any? =
    if (internalVariables.containsKey(name)) {
      internalVariables[name]
    } else {
      val c = generator()
      internalVariables[name] = c
      persistBlockInternalVariables()
      c
    }

  fun getInternalVariable(name: String): Any? = internalVariables[name]

  private fun persistBlockInternalVariables() {
    persistBlockRecord(mutableMapOf("internalVariables" to JsonHelper.writeValueAsString(internalVariables)))
  }

  protected fun setBlockOutputParams(params: Map<String, Any?>) {
    // logTask(blockRuntime.getTopTaskRuntime(), dict.lo("BlockOutputParams", params), TaskLogLevel.Important)
    outputParams.putAll(params)
    persistBlockOutputParams()
  }

  private fun persistBlockOutputParams() {
    persistBlockRecord(mutableMapOf("outputParams" to JsonHelper.writeValueAsString(outputParams)))
  }

  private fun persistBlockRecord(update: EntityValue) {
    EntityRwService.updateOne("FalconBlockRecord", Cq.idEq(recordId), update)
  }

  /**
   * @param update 需要更新的字段 to 值
   */
  fun persistFalconTaskRecord(update: EntityValue) {
    EntityRwService.updateOne("FalconTaskRecord", Cq.idEq(taskRuntime.taskId), update)
  }

  /**
   * 不要抛异常
   */
  private fun fireBlockLifetimeEvent(status: Int) {
    val event = mapOf(
      "stage" to status,
      "blockConfigId" to blockConfig.id,
      "topTaskId" to taskRuntime.getTopTask().taskId,
    )
    WebSocketManager.sendAllAsync(WsMsg("FalconBlock::Lifetime", JsonHelper.writeValueAsString(event)))
  }

  /**
   * TODO 错误怎么处理
   */
  private fun ensureRecord() {
    val record = EntityRwService.findOneById("FalconBlockRecord", recordId)
    if (record != null) {
      status = record["status"] as Int

      val outputParamsStr = record["outputParams"] as String?
      if (!outputParamsStr.isNullOrBlank()) {
        val outputParams: Map<String, Any?> = JsonHelper.mapper.readValue(outputParamsStr, jacksonTypeRef())
        this.outputParams.putAll(outputParams)
      }

      val internalVariablesStr = record["internalVariables"] as String?
      if (!internalVariablesStr.isNullOrBlank()) {
        val internalVariables: Map<String, Any?> = JsonHelper.mapper.readValue(internalVariablesStr, jacksonTypeRef())
        this.internalVariables.putAll(internalVariables)
      }
    } else {
      val nr: EntityValue = mutableMapOf(
        "id" to recordId,
        "blockConfigId" to blockConfig.id,
        "taskId" to taskRuntime.taskId,
        "status" to status,
      )
      EntityRwService.createOne("FalconBlockRecord", nr)
    }
  }

  protected fun mustGetBlockInputParam(name: String, typeHint: BlockParamType? = null): Any {
    val v = getBlockInputParam(name, typeHint)
      ?: throw BzError("errFalconMissingBlockInputParam", blockConfig.name, name)
    if (v is String && v.isBlank()) throw BzError("errFalconMissingBlockInputParam", blockConfig.name, name)
    return v
  }

  protected fun getBlockInputParamAsBool(
    name: String,
    typeHint: BlockParamType? = null, // TODO 明确类型了，还需要这个吗
  ): Boolean = BoolHelper.anyToBool(getBlockInputParam(name, typeHint))

  protected fun getBlockInputParamAsLong(
    name: String,
    typeHint: BlockParamType? = null, // TODO 明确类型了，还需要这个吗
  ): Long? = NumHelper.anyToLong(getBlockInputParam(name, typeHint))

  protected fun mustGetBlockInputParamAsLong(
    name: String,
    typeHint: BlockParamType? = null, // TODO 明确类型了，还需要这个吗
  ): Long = NumHelper.anyToLong(getBlockInputParam(name, typeHint))
    ?: throw BzError("errFalconMissingBlockInputParam", blockConfig.name, name)

  protected fun getBlockInputParamAsList(name: String): List<*>? {
    val rawValue = getBlockInputParam(name) ?: return null
    return if (rawValue.javaClass.isArray) {
      listOf(*rawValue as Array<*>)
    } else {
      when (rawValue) {
        is String -> JsonHelper.mapper.readValue(rawValue, jacksonTypeRef())
        is List<*> -> rawValue
        is JsonNode -> IteratorUtils.toList(rawValue.iterator())
        else -> throw BzError("errFalconBlockInputParamNotList", blockConfig.name, name)
      }
    }
  }

  protected fun collectAllBlockInputParams(): Map<String, Any?> {
    val all: MutableMap<String, Any?> = HashMap()
    for (ip in def.inputParams) {
      all[ip.name] = getBlockInputParam(ip.name)
    }
    all["topTaskId"] = taskRuntime.getTopTask().taskId
    all["taskId"] = taskRuntime.taskId
    all["blockId"] = recordId
    return all
  }

  protected fun getBlockInputParam(name: String, typeHint: BlockParamType? = null): Any? {
    val paramDef = def.inputParams.find { it.name == name }
    val paramConfig = blockConfig.inputParams[name]
    // 没配置
    if (paramConfig == null) {
      if (paramDef != null && paramDef.required) {
        throw BzError("errFalconMissingBlockInputParam", blockConfig.name, name)
      }
      return null
    }
    return getBlockGeneralInputParam(name, paramConfig, paramDef, typeHint)
  }

  protected fun getBlockGeneralInputParam(
    name: String,
    paramConfig: BlockInputParamConfig,
    paramDef: BlockInputParamDef?,
    typeHint: BlockParamType? = null,
  ): Any? {
    val paramValue = parseBlockInputParam(paramConfig, paramDef, typeHint)
    if (paramDef != null && paramDef.required && paramValue == null) {
      throw BzError("errFalconMissingBlockInputParam", blockConfig.name, name)
    }
    return paramValue
  }

  private fun parseBlockInputParam(
    paramConfig: BlockInputParamConfig,
    paramDef: BlockInputParamDef?,
    typeHint: BlockParamType? = null,
  ): Any? {
    val paramValue = paramConfig.value ?: return null
    return when (paramConfig.type) {
      BlockInputParamItemType.Simple -> parseSimpleParam(typeHint ?: paramDef?.type, paramValue)
      BlockInputParamItemType.Expression -> parseExpressionParam(paramConfig)
      BlockInputParamItemType.Ref -> parseRefParam(paramConfig)
    }
  }

  private fun parseSimpleParam(paramType: BlockParamType?, paramValue: Any?): Any? {
    return if (paramType == null || paramValue == null) {
      paramValue
    } else {
      return when (paramType) {
        BlockParamType.String -> paramValue.toString().trim() // !trim
        BlockParamType.Boolean -> BoolHelper.anyToBool(paramValue)
        BlockParamType.Long -> NumHelper.anyToLong(paramValue)
        BlockParamType.Double -> NumHelper.anyToDouble(paramValue)
        BlockParamType.JSONObject -> if (paramValue is String) {
          if (paramValue.isNotBlank()) {
            JsonHelper.mapper.readValue<Map<String, Any?>>(paramValue, jacksonTypeRef())
          } else {
            emptyMap()
          }
        } else {
          paramValue // TODO JSONNode
        }

        BlockParamType.JSONArray -> if (paramValue is String) {
          if (paramValue.isNotBlank()) {
            JsonHelper.mapper.readValue<List<Any?>>(paramValue, jacksonTypeRef())
          } else {
            emptyList()
          }
        } else {
          paramValue // TODO JSONNode
        }

        BlockParamType.Any -> return paramValue // TODO
      }
    }
  }

  private fun getExpressionContext(): Map<String, Any?> = mapOf(
    "task" to mapOf(
      "id" to taskRuntime.taskId,
      "defLabel" to taskRuntime.def.label,
      // "createdOn" to taskRuntime.createdOn,
      "status" to taskRuntime.status,
    ),
    "taskInputs" to flatFetchMap(taskRuntime.inputParams),
    "taskVariables" to flatFetchMap(taskRuntime.variables),
    "globals" to flatFetchMap(FalconGlobalControlService.valueMap()),
    "blocks" to blockContext.expressionBlocks,
    // ctx["helper"] = MVELHelper(NsMetaManager.optionBills)
  )

  private fun flatFetchMap(map: Map<String, Any?>): Map<String, Any?> = HashMap(map)

  /**
   * TODO 最终类型转换
   */
  private fun parseExpressionParam(paramConfig: BlockInputParamConfig): Any? {
    val exp = paramConfig.value as String
    return if (StringUtils.isBlank(exp)) {
      null
    } else {
      try {
        MVEL.eval(exp, getExpressionContext())
      } catch (e: PropertyAccessException) {
        logger.error("$exp : " + e.message)
        throw BzError("errFalconExpressionError", exp, e.message)
      }
    }
  }

  /**
   * TODO 最终类型转换
   */
  private fun parseRefParam(paramConfig: BlockInputParamConfig): Any? {
    if (paramConfig.ref == null) return null

    val source = paramConfig.ref.source
    val name = paramConfig.ref.name

    if (source == "taskInputs") {
      return taskRuntime.inputParams[name]
    } else if (source.startsWith("b")) {
      if (blockContext.ctxVariables.contains(name)) return blockContext.ctxVariables[name]
      val id = source.substring(1).toInt()
      val block = blockContext.blocksByConfigId[id] ?: return null
      return block.outputParams[name]
    } else if (source == "taskVariables") {
      return taskRuntime.variables[name]
    } else if (source == "globals") {
      return FalconGlobalControlService.valueMap()[name]
    } else {
      return null
    }
  }

  protected fun addResource(resType: String, resId: String, args: Map<String, Any?>?) {
    val topId = taskRuntime.getTopTask().taskId
    val old = EntityRwService.findOne(
      
      "FalconTaskResource",
      Cq.and(listOf(Cq.eq("taskId", topId), Cq.eq("resType", resType), Cq.eq("resId", resId))),
    )
    if (old != null) return
    val argsStr = if (args == null) null else JsonHelper.mapper.writeValueAsString(args)
    EntityRwService.createOne(
      
      "FalconTaskResource",
      mutableMapOf("taskId" to topId, "resType" to resType, "resId" to resId, "args" to argsStr),
    )
  }

  protected fun removeResource(resType: String, resId: String): Long {
    val topId = taskRuntime.getTopTask().taskId
    return EntityRwService.removeOne(
      
      "FalconTaskResource",
      Cq.and(listOf(Cq.eq("taskId", topId), Cq.eq("resType", resType), Cq.eq("resId", resId))),
    )
  }

  // 不重复添加
  protected fun addRelatedObject(objectType: String, objectId: String, objectArgs: String?) {
    val old = EntityRwService.findOne(
      
      "FalconRelatedObject",
      Cq.and(
        listOf(
          Cq.eq("taskId", taskRuntime.getTopTask().taskId),
          Cq.eq("objectType", objectType),
          Cq.eq("objectId", objectId),
        ),
      ),
    )
    if (old != null) return
    val ev: EntityValue = mutableMapOf(
      "taskId" to taskRuntime.getTopTask().taskId,
      "objectType" to objectType,
      "objectId" to objectId,
      "objectArgs" to objectArgs,
    )
    EntityRwService.createOne("FalconRelatedObject", ev)
  }

  /**
   * 更新猎鹰任务记录的执行机器人
   */
  protected fun addActualVehicle(
    
    vehicleName: String? = null,
    previousRobotName: String? = null
  ) {
    synchronized(taskRuntime) {
      val cq = Cq.idEq(taskRuntime.taskId)

      val oldRecord = EntityRwService.findOne(
        
        "FalconTaskRecord",
        cq,
        FindOptions(projection = listOf("actualRobots")),
      )

      val oldRobots = oldRecord?.get("actualRobots")?.toString().orEmpty()
      val robotsSet = oldRobots.split(',').filterTo(mutableSetOf()) { it.isNotBlank() }

      // 处理机器人名称变更
      var modified = false

      // 移除旧机器人名称
      if (!previousRobotName.isNullOrBlank()) {
        modified = robotsSet.remove(previousRobotName) || modified
      }

      // 添加新机器人名称（非空时）
      if (!vehicleName.isNullOrBlank()) {
        modified = robotsSet.add(vehicleName) || modified
      }

      // 仅在发生变更时更新数据库
      if (modified) {
        val newRobots = robotsSet.joinToString(",")
        EntityRwService.updateOne(
          
          "FalconTaskRecord",
          cq,
          mutableMapOf("actualRobots" to newRobots)
        )
      }
    }
  }

  protected fun log(level: FalconLogLevel, msg: String) {
    LogManager.logBlock(logger, taskRuntime.taskId, recordId, level, msg)
  }

  /**
   * 记录块输入参数
   */
  fun persistBlockInputParams() {
    val enabled = BzConfigManager.getByPath("Falcon", "enableRecordInput") as Boolean? ?: return
    if (!enabled) return
    val inputParams = def.inputParams
    val inputMap = HashMap<String, Any>()
    var jsonSize = 0

    for (inputParam in inputParams) {
      val paramName = inputParam.name
      if (paramName.isBlank()) continue
      try {
        getBlockInputParam(paramName)?.let { value ->
          val entryJsonSize = JsonHelper.writeValueAsString(mapOf(paramName to value)).length

          if (jsonSize + entryJsonSize <= 4000) { // 超过字符限制就跳过
            inputMap[paramName] = value
            jsonSize += entryJsonSize
          }
        }
      } catch (_: Exception) { // 不需要处理
      }
    }
    if (inputMap.isNotEmpty()) {
      persistBlockRecord(
        
        mutableMapOf(
          "inputParams" to JsonHelper.writeValueAsString(inputMap),
        ),
      )
    }
  }

  /**
   * 故障重试时，加载“父 BP 已完成” 的子 BP 的 context
   */
  private fun reloadChildrenBlockRecord(blockId: String) {
    // 在 FalconBlockChildId 查所有子 block
    // 根据 contextKey 加载加载对应 block 的数据
    val subBlockEvs = EntityRwService.findMany(
      
      "FalconBlockChildId",
      Cq.and(
        Cq.eq("taskId", taskRuntime.taskId),
        Cq.eq("blockId", recordId),
      ),
      FindOptions(sort = listOf("index")),
    )
    val subBlockId = subBlockEvs.map { EntityHelper.mustGetId(it) }
    val subBlockRecords = EntityRwService.findMany(
      
      "FalconBlockRecord",
      Cq.and(
        Cq.include("id", subBlockId),
      ),
    )

    for (subBlockRecord in subBlockRecords) {
      val brId = EntityHelper.mustGetId(subBlockRecord)
      val subBlockConfigId = NumHelper.anyToInt(subBlockRecord["blockConfigId"])
      val subBlockConfig = this.blockConfig.children.values.flatten().firstOrNull { it.id == subBlockConfigId }
        ?: throw BzError("errSubBlockNotFound", taskRuntime.taskId, recordId, subBlockConfigId)
      FalconCenter.runBp(subBlockConfig, brId, blockContext, taskRuntime)
    }
  }

  companion object {
    const val CHILD_DEFAULT: String = "default"
  }
}

object BlockStatus {
  const val Created = 200
  const val Started = 220
  const val Failed = 250 // 故障
  const val Done = 260
  const val Cancelled = 280 // 用户取消
  const val Aborted = 290 // 任务彻底失败
}