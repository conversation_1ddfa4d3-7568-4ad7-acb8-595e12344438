package com.seer.trick.falcon.bp.falcon

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef

import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.EntityHelper
import com.seer.trick.base.entity.FieldScale
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*
import com.seer.trick.helper.JsonHelper
import org.apache.commons.lang3.StringUtils

/**
 * 更新当前运行任务记录的字段值
 */
class UpdateFalconRecordFieldBp : AbstractBp() {

  override fun process() {
    val fieldName = mustGetBlockInputParam("fieldName") as String
    val fieldMeta = EntityHelper.mustGetFm(BaseCenter.mustGetEntityMeta("FalconTaskRecord"), fieldName)
    var fieldValue = getBlockInputParam("fieldValue")
    // 判断是多值并且是简单值，字符不为空时转成数组
    if (fieldMeta.scale == FieldScale.List &&
      blockConfig.inputParams["fieldValue"]?.type == BlockInputParamItemType.Simple &&
      (fieldValue is String && StringUtils.isNotBlank(fieldValue))
    ) {
      fieldValue = JsonHelper.mapper.readValue<List<Any?>>(fieldValue, jacksonTypeRef())
    }
    persistFalconTaskRecord(
      
      mutableMapOf(
        fieldName to fieldValue,
      ),
    )
    logger.info("Update falcon record col = $fieldName, newValue = $fieldValue")
  }

  companion object {
    val def = BlockDef(
      UpdateFalconRecordFieldBp::class.simpleName!!,
      color = "#C7DCA7",
      inputParams = listOf(
        BlockInputParamDef(
          "fieldName",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.EntityFieldName),
        ),
        BlockInputParamDef("fieldValue", BlockParamType.Any, false),
      ),
    )
  }
}