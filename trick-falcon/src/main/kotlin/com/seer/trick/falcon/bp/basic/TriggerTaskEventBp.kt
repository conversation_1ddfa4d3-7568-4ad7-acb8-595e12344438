package com.seer.trick.falcon.bp.basic


import com.seer.trick.falcon.FalconEventBus
import com.seer.trick.falcon.task.FalconTaskService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType

class TriggerTaskEventBp : AbstractBp() {

  override fun process() {
    val eventName = getBlockInputParam(ipEventName.name) as String
    // val eventData = getBlockInputParam(ipEventData.name)

    FalconEventBus.fire(
      
      taskRuntime.getTopTask().taskId, // TODO top?
      eventName,
      // TODO eventData
    )
  }

  companion object {

    private val ipEventName = BlockInputParamDef("eventName", BlockParamType.String, true)
    private val ipEventData = BlockInputParamDef("eventData", BlockParamType.Any)

    val def = BlockDef(
      TriggerTaskEventBp::class.simpleName!!,
      color = "#bea7e1",
      inputParams = listOf(ipEventName, ipEventData),
    )

  }
}