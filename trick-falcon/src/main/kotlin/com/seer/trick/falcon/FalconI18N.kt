package com.seer.trick.falcon

import com.seer.trick.I18N.lo
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockDefGroup

object FalconI18N {

  fun loBlockDefGroup(bg: BlockDefGroup) {
    bg.label = lo("Falcon_BlockGroup_" + bg.name, defaultValue = bg.name)
  }

  fun loBlockDef(bd: BlockDef) {
    bd.label = lo("Falcon_Bp_${bd.name}_label", defaultValue = bd.name)
    bd.description = lo("Falcon_Bp_${bd.name}_description", defaultValue = "")

    for (ip in bd.inputParams) {
      ip.label = lo("Falcon_Bp_${bd.name}_Input_${ip.name}_label", defaultValue = ip.name)
      ip.description = lo("Falcon_Bp_${bd.name}_Input_${ip.name}_description", defaultValue = "")
      if (!ip.options.isNullOrEmpty()) {
        for (o in ip.options) {
          // o.label = lo("Falcon_Option_${o.value}", defaultValue = o.value)
          o.label = lo("Falcon_Bp_${bd.name}_Input_${ip.name}_Option_${o.value}", defaultValue = o.value)
        }
      }
    }

    for (op in bd.outputParams) {
      op.label = lo("Falcon_Bp_${bd.name}_Output_${op.name}_label", defaultValue = op.name)
      op.description = lo("Falcon_Bp_${bd.name}_Output_${op.name}_description", defaultValue = "")
    }

    for (cv in bd.contextVariables) {
      cv.label = lo("Falcon_Bp_${bd.name}_Context_${cv.name}_label", defaultValue = cv.name)
      cv.description = lo("Falcon_Bp_${bd.name}_Context_${cv.name}_description", defaultValue = "")
    }

    for (ch in bd.children) {
      ch.label = lo("Falcon_Bp_${bd.name}_Child_${ch.name}_label", defaultValue = ch.name)
    }
  }

}