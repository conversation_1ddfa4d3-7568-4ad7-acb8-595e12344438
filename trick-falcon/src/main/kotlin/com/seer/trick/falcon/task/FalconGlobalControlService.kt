package com.seer.trick.falcon.task

import com.seer.trick.ComplexQuery

import com.seer.trick.base.BaseCenter
import com.seer.trick.base.entity.FieldType
import com.seer.trick.base.entity.InlineOptionBill
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.helper.JsonFileHelper
import org.slf4j.LoggerFactory
import java.io.File
import java.util.concurrent.ConcurrentHashMap

/**
 * 猎鹰任务全局控制
 *
 * 全局控制的配置、值存在项目包 config/falcon-global-value.json
 * 结构为：
 * {
 *     "变量 A": {
 *         "name": "变量 A",
 *         "type": "同 FieldType",
 *         "inlineOptionBill": "",
 *         "refEntity": "",
 *         "refFilter": "",
 *         "value": "Any"
 *     },
 *     "变量 B": {
 *         "name": "变量 B",
 *         "type": "同 FieldType",
 *         "inlineOptionBill": "",
 *         "refEntity": "",
 *         "refFilter": "",
 *         "value": "Any"
 *     }
 * }
 *
 *
 * 注意：
 * 控制并发，可能同时 reload、setValue、setConfig、persist
 *
 * TODO 国际化：99% 的全局变量都是脚本中的，所以要在脚本里国际化，暂时不做。
 *
 * value 只更新修改的，防止多个用户同时更新时，没刷新页面导致的数据被覆盖
 * config 全量更新，不用写单独的增删改查
 */
object FalconGlobalControlService {

  private val logger = LoggerFactory.getLogger(javaClass)

  // { 变量名 -> 变量配置 { name, type, inlineOptionBill, refEntity, refFilter, value, displayOrder, defaultValue } }
  private val map: MutableMap<String, FalconGlobalVariable> = ConcurrentHashMap()

  @Synchronized
  fun init() {
    
    // 读文件
    map.clear()

    // 兼容老版本，将 falcon-global-value.json 转换为 falcon-global-variable.json
    val oldFile = File(BaseCenter.baseConfig.configDir, "falcon-global-value.json")
    if (oldFile.exists()) {
      logger.info("Found old falcon-global-value.json, will convert to falcon-global-variable.json")
      val jsonMap = JsonFileHelper.readJsonFromFile<MutableMap<String, FalconGlobalVariable>>(oldFile)
      if (jsonMap != null) map.putAll(jsonMap)
      persist()
      oldFile.delete() // 删掉老的全局变量文件，防止覆盖
    }

    val file = File(BaseCenter.baseConfig.configDir, "falcon-global-variable.json")
    if (file.exists()) {
      val jsonMap = JsonFileHelper.readJsonFromFile<MutableMap<String, FalconGlobalVariable>>(file)
      // 将 json 合并入 map
      if (jsonMap != null) map.putAll(jsonMap)
      logger.info("FalconGlobalControlService loaded field ${file.path}")
    }
    logger.info("FalconGlobalControlService init")
  }

  fun dispose() {
    // 持久化一次
    map.clear()
  }

  fun getConfig(varName: String) = map[varName]

  fun configList() = map.values

  @Synchronized
  fun setConfigs(cfgList: List<FalconGlobalVariable>) {
    for (cfg in cfgList) {
      val old = map.getOrDefault(cfg.name, FalconGlobalVariable()) // 这个 cfg 只取 value，所以 name 无需赋值
      map[cfg.name] = cfg.copy(value = old.value)
    }
    // 删除的全局变量
    val removedKeys = map.keys.toSet() - cfgList.map { it.name }.toSet()
    map.keys.removeAll(removedKeys) // 在 Map 的 .keys 或 .values 中调用 remove() 并提供键或值来删除条目
    persist()
  }

  fun valueMap(): Map<String, Any?> = map.map { it.key to it.value.value }.toMap()

  fun getValue(varName: String) = map[varName]?.value ?: map[varName]?.defaultValue

  @Synchronized
  fun setValues(map: Map<String, Any?>) {
    for ((name, value) in map) {
      setValue(name, value)
    }
  }

  /**
   * 无此 name 的变量时，会建一个
   */
  @Synchronized
  fun setValue(name: String, value: Any?) {
    val cfg = map.getOrDefault(name, FalconGlobalVariable(name = name)) // 这里 cfg 要被放会 map，所以 name 要赋值
    map[name] = cfg.copy(value = value)
    persist()
  }

  fun fieldTypeToFalconBlockParamType(type: FieldType): BlockParamType = when (type) {
    FieldType.String -> BlockParamType.String
    FieldType.Int, FieldType.Long -> BlockParamType.Long
    FieldType.Float -> BlockParamType.Double
    FieldType.Boolean -> BlockParamType.Boolean
    FieldType.Object -> BlockParamType.JSONObject
    FieldType.Date -> BlockParamType.Any
    FieldType.Time -> BlockParamType.Any
    FieldType.DateTime -> BlockParamType.Any
    FieldType.Reference -> BlockParamType.Any
    FieldType.Component -> BlockParamType.Any
    FieldType.File -> BlockParamType.Any
    FieldType.Image -> BlockParamType.Any
  }

  // 理论上很快，不用放 FalconConcurrentCenter 中异步执行
  // 可以去掉 @Synchronized。为了后续代码无脑 persist，这里也加锁
  @Synchronized
  private fun persist() =
    JsonFileHelper.writeJsonToFile(File(BaseCenter.baseConfig.configDir, "falcon-global-variable.json"), map, true)
}

data class FalconGlobalVariable(
  val name: String = "",
  val type: FieldType = FieldType.String,
  val inlineOptionBill: InlineOptionBill? = null,
  val refEntity: String? = null,
  val refFilter: ComplexQuery? = null,
  val value: Any? = null, // 每次 copy 一个对象，然后放入 map
  val displayOrder: Int = 0, // 显示顺序。比较重要，用于控制在《全局控制变量配置页面》、《猎鹰任务配置页面》的显示顺序
  val defaultValue: Any? = null, // 仅前端用，后端无法区分 value 为 null 时，是未设值还是设的 null。
)