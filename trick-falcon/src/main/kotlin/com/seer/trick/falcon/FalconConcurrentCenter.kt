package com.seer.trick.falcon

import com.seer.trick.helper.ThreadHelper.newBoundedThreadPool
import java.util.concurrent.Executors

/**
 * 猎鹰任务：因为线程是最昂贵的资源，因此集中管理
 */
object FalconConcurrentCenter {
  
  /**
   * 用于执行任务、子任务、并行块
   */
  val falconMainExecutor = newBoundedThreadPool(3, 250)
  
  /**
   * 专用于更新告警
   */
  val updateAlarmExecutor = Executors.newSingleThreadExecutor()
  
  /**
   * 专用于记猎鹰任务日志
   */
  val logExecutor = Executors.newSingleThreadExecutor()
  
  /**
   * 用于异步发送猎鹰任务事件
   */
  val eventExecutor = newBoundedThreadPool(3, 50)
  
}