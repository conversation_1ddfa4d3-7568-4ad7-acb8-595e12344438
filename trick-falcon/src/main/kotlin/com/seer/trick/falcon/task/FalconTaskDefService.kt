package com.seer.trick.falcon.task

import com.seer.trick.BzError
import com.seer.trick.base.BaseCenter
import com.seer.trick.base.file.FileManager
import com.seer.trick.base.user.Operator
import com.seer.trick.falcon.FalconCenter
import com.seer.trick.falcon.FalconCenter.EXT_BLOCK_NAME_PREFIX
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.bp.RootBp
import com.seer.trick.falcon.bp.SubTaskBp
import com.seer.trick.falcon.bp.basic.DelayBp
import com.seer.trick.falcon.bp.basic.ExpressionBp
import com.seer.trick.falcon.bp.basic.PrintBp
import com.seer.trick.falcon.bp.conditionloop.IterateListBp
import com.seer.trick.falcon.domain.*
import com.seer.trick.helper.FileHelper
import com.seer.trick.helper.FileHelper.unzipStreamToDir
import com.seer.trick.helper.IdHelper
import com.seer.trick.helper.JsonFileHelper
import com.seer.trick.helper.JsonHelper
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.io.File
import java.util.*
import java.util.concurrent.ConcurrentHashMap

object FalconTaskDefService {
  
  private val logger: Logger = LoggerFactory.getLogger(javaClass)
  
  private val taskDefMap: MutableMap<String, TaskDef> = ConcurrentHashMap()
  
  private val defaultTaskDef: Map<String, TaskDef> = mutableMapOf(
    "TestSubTask" to TaskDef(
      "TestSubTask",
      builtin = true,
      version = 1,
      label = "TestSubTask",
      inputParams = listOf(TaskInputParamDef("message", BlockParamType.String, "Message")),
      rootBlock = BlockConfig(
        id = 0,
        blockType = RootBp.def.name,
        children = mapOf(
          AbstractBp.CHILD_DEFAULT to listOf(
            BlockConfig(
              id = 11002,
              blockType = PrintBp.def.name,
              inputParams = mapOf(
                "message" to BlockInputParamConfig(
                  BlockInputParamItemType.Expression,
                  "'Sub: ' + taskInputs.message",
                ),
              ),
            ),
          ),
        ),
      ),
    ),
    "TestOne" to TaskDef(
      "TestOne",
      builtin = true,
      version = 1,
      label = "TestTask",
      inputParams = listOf(TaskInputParamDef("username", BlockParamType.String, "Username")),
      rootBlock = BlockConfig(
        id = 0,
        blockType = RootBp.def.name,
        children = mapOf(
          AbstractBp.CHILD_DEFAULT to listOf(
            BlockConfig(
              id = 11001,
              name = "exp1",
              blockType = ExpressionBp.def.name,
              inputParams = mapOf(
                "expression" to BlockInputParamConfig(
                  BlockInputParamItemType.Expression,
                  """ "Hello " + taskInputs.username """,
                ),
              ),
            ),
            BlockConfig(
              id = 11002,
              blockType = PrintBp.def.name,
              inputParams = mapOf(
                "message" to BlockInputParamConfig(BlockInputParamItemType.Expression, "blocks.exp1.expResult"),
              ),
            ),
            BlockConfig(
              id = 21012,
              blockType = DelayBp.def.name,
              inputParams = mapOf(
                "timeMillis" to BlockInputParamConfig(BlockInputParamItemType.Simple, 2000),
              ),
            ),
            BlockConfig(
              id = 21013,
              blockType = PrintBp.def.name,
              inputParams = mapOf(
                "message" to BlockInputParamConfig(BlockInputParamItemType.Simple, "After delay"),
              ),
            ),
            BlockConfig(
              id = 21014,
              name = "exp2",
              blockType = "EXT::YouGood",
              inputParams = mapOf(
                "msg" to BlockInputParamConfig(BlockInputParamItemType.Simple, "XXX XXX XXX"),
              ),
            ),
            BlockConfig(
              id = 22001,
              name = "loop",
              blockType = IterateListBp.def.name,
              inputParams = mapOf(
                "list" to BlockInputParamConfig(BlockInputParamItemType.Simple, """["AAA", "BBB", "CCC"]"""),
              ),
              children = mapOf(
                AbstractBp.CHILD_DEFAULT to listOf(
                  BlockConfig(
                    id = 21011,
                    blockType = PrintBp.def.name,
                    inputParams = mapOf(
                      "message" to BlockInputParamConfig(
                        BlockInputParamItemType.Expression,
                        """ "index:" + blocks.loop.index + ", item:" + blocks.loop.item""",
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    ),
    "Test2" to TaskDef(
      "Test2",
      builtin = true,
      version = 1,
      label = "TestTask",
      inputParams = listOf(TaskInputParamDef("username", BlockParamType.String, "Username")),
      rootBlock = BlockConfig(
        id = 0,
        blockType = RootBp.def.name,
        children = mapOf(
          AbstractBp.CHILD_DEFAULT to listOf(
            BlockConfig(
              id = 11001,
              name = "exp1",
              blockType = ExpressionBp.def.name,
              inputParams = mapOf(
                "expression" to BlockInputParamConfig(
                  BlockInputParamItemType.Expression,
                  """ "Hello " + taskInputs.username """,
                ),
              ),
            ),
            BlockConfig(
              id = 11002,
              blockType = PrintBp.def.name,
              inputParams = mapOf(
                "message" to BlockInputParamConfig(BlockInputParamItemType.Expression, "blocks.exp1.expResult"),
              ),
            ),
            BlockConfig(
              id = 22001,
              name = "loop",
              blockType = IterateListBp.def.name,
              inputParams = mapOf(
                "list" to BlockInputParamConfig(BlockInputParamItemType.Simple, """["AAA", "BBB", "CCC"]"""),
              ),
              children = mapOf(
                AbstractBp.CHILD_DEFAULT to listOf(
                  BlockConfig(
                    id = 21001,
                    blockType = SubTaskBp.def.name,
                    refTaskDefId = "TestSubTask",
                    inputParams = mapOf(
                      "message" to BlockInputParamConfig(
                        BlockInputParamItemType.Expression,
                        """ "index:" + blocks.loop.index + ", item:" + blocks.loop.item""",
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    ),
  )
  
  fun load() {
    taskDefMap.clear()
    val defList: List<TaskDef>? = JsonFileHelper.readJsonFromFile(getFile())
    if (!defList.isNullOrEmpty()) {
      for (def in defList) taskDefMap[def.id] = def
    }
    
    if (taskDefMap.isEmpty()) taskDefMap.putAll(defaultTaskDef)
    
    // 加载内建猎鹰任务
    loadBuiltinTaskDefZip()
  }
  
  /**
   * 加载内建猎鹰任务
   */
  private fun loadBuiltinTaskDefZip() {
    // 从 resources 中加载内建猎鹰任务的 zip 文件。
    val builtinZipStream = FileHelper.loadClasspathResourceAsStream("/falcon-task-builtin.zip")
    if (builtinZipStream == null) {
      logger.info("no builtin falcon task found....")
      return
    }
    
    // 删除项目包原有的内建猎鹰任务。
    logger.info("remove old builtin falcon tasks .")
    val builtinTaskDefDir = File(BaseCenter.baseConfig.configDir, "falcon-task-builtin")
    builtinTaskDefDir.list()
    if (builtinTaskDefDir.exists()) {
      if (!builtinTaskDefDir.isDirectory) {
        builtinTaskDefDir.delete() // 删除同名的文件
      } else {
        builtinTaskDefDir.listFiles()?.forEach { it.delete() } // 删除历史的内建猎鹰任务，但是保留文件夹
      }
    }
    
    // 将此 zip 文件解压到 "{项目包}/config/falcon-task-builtin/" 目录下。
    logger.info("unzip builtin falcon tasks to /config/falcon-task-builtin/ .")
    unzipStreamToDir(builtinZipStream, builtinTaskDefDir)
    
    // 加载内建猎鹰任务到内存中。
    logger.info("load builtin falcon tasks to cache .")
    builtinTaskDefDir.listFiles()?.forEach { it ->
      // 加载原文件
      val def: TaskDef = JsonFileHelper.readJsonFromFile(it) ?: return@forEach
      
      // 加载最近的历史版本
      // TODO: 不能修改猎鹰任务原文件，那么内建猎鹰任务如何切换版本？
      val latestVer = listTaskDefRevisions(def.id).sortedByDescending { it.version }.firstOrNull()?.version
      // 优先加载修改过的内建猎鹰任务。
      taskDefMap[def.id] = if (latestVer == null) def else loadTaskDefRevision(def.id, latestVer)
    }
  }
  
  fun list(): List<TaskDef> = taskDefMap.values.toList().sortedBy { it.label }
  
  fun mustGetTaskDefByLabel(label: String): TaskDef = taskDefMap.values.find {
    it.label == label
  } ?: throw BzError("errNoTaskDefByLabel", label)
  
  fun getTaskDefByLabel(label: String): TaskDef? = taskDefMap.values.find { it.label == label }
  
  fun mustFetchLatestTaskDefById(defId: String): TaskDef =
    taskDefMap[defId] ?: throw BzError("errNoFalconTaskDefById", defId)
  
  fun fetchLatestTaskDefById(defId: String): TaskDef? = taskDefMap[defId]
  
  /**
   * 保存猎鹰任务模版定义。可能是新增页可能是修改。
   */
  fun save(req: TaskDef): String {
    val id = StringUtils.firstNonBlank(req.id, IdHelper.oidStr())
    val oldTaskDef = taskDefMap[id]
    
    // 只能修改已有的内建猎鹰任务，不能新建内建猎鹰任务。
    if (req.builtin) {
      if (oldTaskDef == null) throw BzError("errCannotCreateNewBuiltinTaskDef")
      if (!oldTaskDef.builtin) throw BzError("errCannotModifyBuiltinFromFalseToTrue", req.label)
      // TODO: 待讨论：是否允许修改内建猎鹰任务的 label ? 暂时先不允许修改 label，便于管理，否则用户改 label, 会增加沟通成本，排查问题困难。
      if (oldTaskDef.label != req.label) throw BzError("errCannotModifyBuiltinLabel", oldTaskDef.label, req.label)
    }
    
    // 如果保存内建猎鹰任务时，将修改后的 def 也放到 "./config/falcon-task-revision/" 目录下，相关规则也和普通猎鹰任务一样。
    // TODO：待讨论，如果用内建猎鹰任务的文件名，作为 revision 的文件名，会更直观，更便于管理。
    oldTaskDef?.let { def ->
      // 保存历史版本
      JsonFileHelper.writeJsonToFile(getTaskRevision(def.id, def.version), def, true)
    }
    
    val op = Operator.current()
    val taskDef = req.copy(
      id = id,
      version = (oldTaskDef?.version ?: 0) + 1,
      createdBy = oldTaskDef?.createdBy ?: op?.username ?: "",
      createdOn = oldTaskDef?.createdOn ?: Date(),
      modifiedBy = op?.username ?: "",
      modifiedOn = Date(),
    )
    
    taskDefMap[id] = taskDef
    save()
    
    return id
  }
  
  fun remove(ids: List<String>) {
    // 不能删除内建任务模板，并提出完整的提示
    val builtinDefNames = taskDefMap.values.filter { ids.contains(it.id) && it.builtin }.map { it.label }
    if (builtinDefNames.isNotEmpty()) throw BzError("errCannotRemoveBuiltinTaskDef", builtinDefNames.joinToString(", "))
    
    for (id in ids) {
      taskDefMap.remove(id)
    }
    save()
  }
  
  /**
   * 列出指定猎鹰任务（ID）的所有历史版本
   */
  fun listTaskDefRevisions(id: String): List<TaskDefRevision> {
    val taskDefDir = ensureTaskRevisionDir()
    val taskDefFiles = taskDefDir.listFiles { file -> file.name.startsWith("$id-") }
    
    return taskDefFiles?.mapNotNull { JsonFileHelper.readJsonFromFile<TaskDefRevision>(it) } ?: emptyList()
  }
  
  /**
   * 加载指定 id 任务的指定版本
   *
   */
  fun loadTaskDefRevision(id: String, version: Int): TaskDef {
    val targetFile = getTaskRevision(id, version)
    return JsonFileHelper.readJsonFromFile(targetFile)
      ?: throw BzError("errFileNotExists", targetFile.name)
  }
  
  /**
   * 恢复指定 id 任务的指定版本
   */
  fun restoreTaskDefRevision(id: String, version: Int) {
    logger.error("还原指定版本的模板 id = '$id' version = '$version'")
    
    val targetFile = getTaskRevision(id, version)
    
    val taskDef: TaskDef = JsonFileHelper.readJsonFromFile(targetFile)
      ?: throw BzError("errFileNotExists", targetFile.name)
    
    save(taskDef)
  }
  
  /**
   * 导出猎鹰任务。自动包含子任务和用到的定制组件。
   */
  fun exportTaskDef(ids: List<String>, names: List<String>, remark: String): File {
    val taskDefList = taskDefMap.values.filter { !it.builtin && it.id in ids }
    
    // 构建 DefExtBpIndex
    val def = DefExtBpIndex().apply {
      taskDefList.forEach { taskDef ->
        taskDefIds += taskDef.id
        findSubTaskAndExtBp(taskDef.rootBlock, this)
      }
      names.forEach { name ->
        this.extBpNames += EXT_BLOCK_NAME_PREFIX + name
      }
    }
    
    // 构建 components 列表
    val components = def.extBpNames.mapNotNull { bpName ->
      FalconCenter.extBlockDefMap[bpName]
    }
    
    // 构建 templates 列表
    val templates = def.taskDefIds.mapNotNull { defId ->
      taskDefMap[defId]
    }
    
    // 构建导出数据并写入 JSON 文件
    val record = ExportRecord(remark = remark, components = components, templates = templates)
    return FileManager.nextTmpFile(suffix = ".falcon.json").apply {
      JsonFileHelper.writeJsonToFile(this, record, true)
    }
  }
  
  /**
   * 对比现有猎鹰任务比较是否有改动、新增
   */
  fun checkImportTaskDefs(eid: ExportRecord): CheckImportRes {
    val map = taskDefMap.values.associateBy { it.label }
    // 处理模板的逻辑
    val temList = eid.templates.map { template ->
      val existingTemplate = map[template.label]
      val isAdd = existingTemplate == null
      val id = existingTemplate?.id ?: template.id
      val isChange = existingTemplate?.let {
        JsonHelper.writeValueAsString(
          TaskDef(
            label = it.label,
            group = it.group,
            inputParams = it.inputParams,
            outputParams = it.outputParams,
            rootBlock = it.rootBlock,
          ),
        ) != JsonHelper.writeValueAsString(
          TaskDef(
            label = template.label,
            group = template.group,
            inputParams = template.inputParams,
            outputParams = template.outputParams,
            rootBlock = template.rootBlock,
          ),
        )
      } ?: false
      
      TemplateDefRes(
        id = id,
        label = template.label,
        modifiedOn = template.modifiedOn,
        isChange = isChange,
        isAdd = isAdd,
        version = template.version,
        importFalconJson = template,
        sourceFalconJson = existingTemplate,
      )
    }
    
    // 处理组件的逻辑
    val comList = eid.components.map { component ->
      val existingComponent = FalconCenter.extBlockDefMap[EXT_BLOCK_NAME_PREFIX + component.name]
      val isAdd = existingComponent == null
      val label = existingComponent?.label ?: component.label
      val isChange = existingComponent?.let {
        JsonHelper.writeValueAsString(
          BlockDef(
            name = it.name,
            inputParams = it.inputParams,
            outputParams = it.outputParams,
            contextVariables = it.contextVariables,
            children = it.children,
            hidden = it.hidden,
            extFunction = it.extFunction,
            label = it.label,
            description = it.description,
            color = it.color,
            source = it.source,
          ),
        ) != JsonHelper.writeValueAsString(
          BlockDef(
            name = component.name,
            inputParams = component.inputParams,
            outputParams = component.outputParams,
            contextVariables = component.contextVariables,
            children = component.children,
            hidden = component.hidden,
            extFunction = component.extFunction,
            label = component.label,
            description = component.description,
            color = component.color,
            source = component.source,
          ),
        )
      } ?: false
      
      ComponentRes(
        name = component.name,
        label = label,
        isAdd = isAdd,
        modifiedOn = component.modifiedOn,
        isChange = isChange,
        version = component.version,
        importComponentJson = component,
        sourceComponentJson = existingComponent,
      )
    }
    
    return CheckImportRes(
      remark = eid.remark,
      templates = temList,
      components = comList,
    )
  }
  
  // 查找子任务和引用的自定义组件
  private fun findSubTaskAndExtBp(blockConfig: BlockConfig, defExtBpIndex: DefExtBpIndex) {
    val blockType = blockConfig.blockType
    
    when {
      blockType.startsWith(FalconCenter.SUB_TASK_BLOCK_NAME_PREFIX) -> {
        defExtBpIndex.taskDefIds += blockConfig.refTaskDefId
      }
      
      blockType.startsWith(EXT_BLOCK_NAME_PREFIX) -> {
        defExtBpIndex.extBpNames += blockType
      }
    }
    
    blockConfig.children.forEach { (_, bcs) ->
      bcs.forEach { bc ->
        findSubTaskAndExtBp(bc, defExtBpIndex) // 递归处理子任务
      }
    }
  }
  
  private fun save() {
    val list = taskDefMap.values.filter { !it.builtin }.sortedBy { it.label }
    JsonFileHelper.writeJsonToFile(getFile(), list, true)
  }
  
  // 所有猎鹰任务的存储文件
  private fun getFile(): File = File(BaseCenter.baseConfig.configDir, "falcon.json")
  
  /**
   * 确定导入猎鹰任务
   */
  fun importTaskDef(templates: List<TaskDef>, components: List<BlockDef>) {
    val map = taskDefMap.values.associateBy { it.label }
    templates.forEach { template ->
      val updatedTemplate = map[template.label]?.let { existingTemplate ->
        template.copy(
          id = existingTemplate.id,
          createdBy = existingTemplate.createdBy,
          createdOn = existingTemplate.createdOn,
        )
      } ?: template
      save(updatedTemplate)
    }
    components.forEach { component ->
      val updatedComponent =
        FalconCenter.extBlockDefMap[EXT_BLOCK_NAME_PREFIX + component.name]?.let { existingComponent ->
          component.copy(
            createdOn = existingComponent.createdOn,
            createdBy = existingComponent.createdBy,
          )
        } ?: component
      FalconCenter.saveExtBp(updatedComponent)
    }
  }
  
  /**
   * 校验任务名是否重复
   */
  fun checkRepeatFalcon(def: TaskDef): Boolean = if (def.version == 1) {
    taskDefMap.values.none { it.label == def.label }
  } else {
    true
  }
  
  private fun ensureTaskRevisionDir(): File {
    val file = File(BaseCenter.baseConfig.configDir, "falcon-task-revision")
    file.mkdirs()
    return file
  }
  
  // 获取指定 ID 的猎鹰任务的指定版本
  private fun getTaskRevision(id: String, version: Int): File = File(ensureTaskRevisionDir(), "$id-$version.json")
}

data class TaskDefRevision(
  val id: String = "",
  val version: Int? = null,
  val label: String = "",
  val modifiedOn: Date? = null,
  val modifiedBy: String = "",
  val createdOn: Date? = null,
  val createdBy: String = "",
)

/*
* 猎鹰任务，自定义块索引对象
*/
data class DefExtBpIndex(
  val taskDefIds: MutableSet<String> = mutableSetOf(),
  val extBpNames: MutableSet<String> = mutableSetOf(),
)

/*
* 猎鹰任务导入导出的数据对象
*/
data class ExportRecord(
  val remark: String = "", // 备注
  val templates: List<TaskDef> = emptyList(), // 任务模板
  val components: List<BlockDef> = emptyList(), // 组件
)

/**
 * 猎鹰任务回显弹框实体
 */
data class CheckImportRes(
  val templates: List<TemplateDefRes> = emptyList(),
  val components: List<ComponentRes> = emptyList(),
  val remark: String = "",
)

// 猎鹰任务校验回显实体
data class TemplateDefRes(
  val id: String = "", // 任务id
  val label: String = "", // 显示名称
  val modifiedOn: Date?, // 修改时间
  val isChange: Boolean = false, // 是否修改
  val isAdd: Boolean = true, // 是否新增
  val version: Int = 0,
  val importFalconJson: Any, // 导入的任务 json
  val sourceFalconJson: Any? = null, // 系统同名的任务 json
)

/**
 * 导入自定义组件回显实体
 */
data class ComponentRes(
  val name: String = "", //
  val label: String = "", // 显示名称
  val modifiedOn: Date?, // 修改时间
  val isChange: Boolean = false, // 是否修改
  val isAdd: Boolean = true, // 是否新增
  val version: Int = 0,
  val importComponentJson: Any, // 导入的任务 json
  val sourceComponentJson: Any? = null, // 系统同名的任务 json
)