package com.seer.trick.falcon

import com.seer.trick.I18N.lo
import com.seer.trick.base.AppModule
import com.seer.trick.base.alarm.AlarmGroup
import com.seer.trick.base.alarm.AlarmService
import com.seer.trick.base.entity.service.extension.EntityServiceExtensions
import com.seer.trick.falcon.handler.FalconHandler
import com.seer.trick.falcon.handler.FalconWsManager
import com.seer.trick.falcon.stats.FalconStatsService
import com.seer.trick.falcon.task.FalconGlobalControlService
import com.seer.trick.falcon.task.FalconTaskDefService
import com.seer.trick.falcon.task.FalconTaskService
import org.graalvm.polyglot.Value

object FalconModule : AppModule() {

  init {
    EntityServiceExtensions.addExtension("FalconTaskRecord", FalconTaskService)
  }

  override fun init() {
    AlarmService.registerGroup(AlarmGroup("Falcon", lo("labelFalcon"), 10))
  }

  override fun registerHttpHandlers() {
    FalconHandler.registerHandlers()
    FalconWsManager.registerHandlers()
  }

  override fun registerStatsChart() {
    FalconStatsService.register()
  }

  override fun afterScript() {
    FalconTaskDefService.load()
    FalconCenter.loadExtBp()

    FalconTaskService.loadUnfinishedTasks()

    FalconTaskService.init()
    FalconGlobalControlService.init()
  }

  override fun putMoreScriptBindings(bindings: Value) {
    bindings.putMember("falcon", ScriptFalcon)
  }

  override fun dispose() {
    FalconGlobalControlService.dispose()
  }
}