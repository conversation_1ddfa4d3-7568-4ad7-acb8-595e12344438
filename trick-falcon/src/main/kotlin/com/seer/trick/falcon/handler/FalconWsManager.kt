package com.seer.trick.falcon.handler

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.Cq

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.entity.service.FindOptions
import com.seer.trick.base.http.WebSocketManager
import com.seer.trick.base.http.WebSocketSubscriber
import com.seer.trick.base.http.WsEnv
import com.seer.trick.base.http.WsMsg
import com.seer.trick.falcon.domain.TaskDef
import com.seer.trick.falcon.task.FalconTaskDefService
import com.seer.trick.helper.JsonHelper
import io.javalin.websocket.WsMessageContext

object FalconWsManager : WebSocketSubscriber() {
  
  fun registerHandlers() {
    WebSocketManager.subscribers += this
  }
  
  override fun onMessage(ctx: WsMessageContext, msg: WsMsg, env: WsEnv) {
    when (msg.action) {
      "Falcon::TaskView" -> onTaskView(ctx, msg)
    }
  }
  
  private fun onTaskView(ctx: WsMessageContext, msg: WsMsg) {
    
    val req: FalconTaskViewReq = JsonHelper.mapper.readValue(msg.content, jacksonTypeRef())
    
    val taskRecord = EntityRwService.findOneById("FalconTaskRecord", req.taskId)
    val res: FalconTaskViewRes = if (taskRecord != null) {
      val defId = taskRecord["defId"] as String
      val taskDef = FalconTaskDefService.fetchLatestTaskDefById(defId)
      if (taskDef != null) {
        val blocks = EntityRwService.findMany("FalconBlockRecord", Cq.eq("taskId", req.taskId))
        val childIds = EntityRwService.findMany("FalconBlockChildId", Cq.eq("taskId", req.taskId))
        
        // 日志
        val logs = if (req.showingLog) {
          val logs = EntityRwService.findMany(
            
            "FalconLog",
            Cq.eq("taskId", req.taskId),
            FindOptions(sort = listOf("+createdOn")),
          )
          if (req.logsCursor != null && req.logsCursor > 0 && req.logsCursor <= logs.size) {
            logs.subList(req.logsCursor, logs.size)
          } else {
            logs
          }
        } else {
          null
        }
        
        val relatedObjects = EntityRwService.findMany("FalconRelatedObject", Cq.eq("taskId", req.taskId))
        
        FalconTaskViewRes(req.taskId, true, null, taskRecord, taskDef, blocks, childIds, logs, relatedObjects)
      } else {
        FalconTaskViewRes(req.taskId, false, "NoTaskDef")
      }
    } else {
      FalconTaskViewRes(req.taskId, false, "NoTaskRecord")
    }
    
    ctx.send(WsMsg.json("Falcon::TaskView::Reply", res, replyToId = msg.id))
  }
}

data class FalconTaskViewReq(val taskId: String, val showingLog: Boolean = false, val logsCursor: Int? = null)

data class FalconTaskViewRes(
  val taskId: String,
  val ok: Boolean,
  val errMsg: String? = null,
  val taskRecord: EntityValue? = null,
  val taskDef: TaskDef? = null,
  val blocks: List<EntityValue>? = null,
  val childIds: List<EntityValue>? = null,
  var logs: List<EntityValue>? = null,
  val relatedObjects: List<EntityValue>? = null,
)