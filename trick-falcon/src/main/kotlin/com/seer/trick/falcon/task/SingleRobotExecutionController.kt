package com.seer.trick.falcon.task

import com.seer.trick.BzError

import org.slf4j.LoggerFactory
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

/**
 * 单车猎鹰任务特殊处理：强制串行控制
 */
object SingleRobotExecutionController {

  private val logger = LoggerFactory.getLogger(javaClass)

  /**
   * 正在执行的猎鹰任务 ID（顶层）
   */
  @Volatile
  private var executingTaskId: String? = null

  private val lock = ReentrantLock()
  private val condition = lock.newCondition()

  /**
   * 申请控制单车执行猎鹰任务
   */
  fun control(taskId: String) {
    logger.info("申请控制单车猎鹰任务，taskId: $taskId")
    while (true) {
      if (executingTaskId == null || executingTaskId == taskId) {
        executingTaskId = taskId
        logger.info("获得对单车的控制，执行猎鹰任务：$taskId")
        return
      }
      lock.withLock {
        try {
          condition.await()
        } catch (e: InterruptedException) {
          throw e
        }
      }
    }
  }

  fun release(taskId: String) {
    lock.withLock {
      if (taskId !=
        executingTaskId
      ) {
        throw BzError("errCodeErr", "Single robot falcon, release task $taskId, but executing task is $executingTaskId")
      }
      executingTaskId = null
      logger.info("释放控制单车猎鹰任务，taskId=$taskId")
      condition.signalAll()
    }
  }
}