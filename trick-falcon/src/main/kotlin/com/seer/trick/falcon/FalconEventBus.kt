package com.seer.trick.falcon

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.falcon.FalconConcurrentCenter.eventExecutor
import com.seer.trick.helper.submitCatch
import org.slf4j.LoggerFactory
import java.util.concurrent.CopyOnWriteArrayList

/**
 * TODO 同步事件+异步事件的支持
 */
object FalconEventBus {
  
  private val logger = LoggerFactory.getLogger(javaClass)
  
  val listeners: MutableList<FalconEventListener> = CopyOnWriteArrayList()
  
  /**
   * 事件可能被触发多次
   * 标准事件：TaskStart, TaskSuccess, TaskAborted, TaskCancelled, TaskFail
   * 还有自定义事件，通过 TriggerTaskEventBp
   * TODO TaskCancelled 可能被触发多次
   */
  fun fire(taskId: String, event: String) {
    eventExecutor.submitCatch("Falcon event", logger) {
      val taskEv = EntityRwService.findOneById("FalconTaskRecord", taskId) ?: return@submitCatch
      val eo = TaskEvent(taskId, event, taskEv)
      for (listener in listeners) {
        listener.onFalconEvent(eo)
      }
    }
  }
  
}

interface FalconEventListener {
  
  fun onFalconEvent(event: TaskEvent)
  
}

class TaskEvent(
  val taskId: String,
  val event: String,
  val taskEv: EntityValue
)