package com.seer.trick.falcon

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.falcon.FalconConcurrentCenter.logExecutor
import org.slf4j.Logger

object LogManager {

  /**
   * taskId 是当前任务不是顶级任务
   */
  fun logTask(logger: Logger, taskId: String, level: FalconLogLevel, message: String) {
    val msg = "${taskId}|$message"
    when (level) {
      FalconLogLevel.Debug -> logger.debug(msg)
      FalconLogLevel.Info -> logger.info(msg)
      FalconLogLevel.Error -> logger.error(msg)
    }
    persistAsync(taskId, null, level, message)
  }

  /**
   * taskId 是当前任务不是顶级任务
   */
  fun logBlock(
    logger: Logger, taskId: String, blockId: String, level: FalconLogLevel, message: String
  ) {
    val msg = "${taskId}|${blockId}|$message"
    when (level) {
      FalconLogLevel.Debug -> logger.debug(msg)
      FalconLogLevel.Info -> logger.info(msg)
      FalconLogLevel.Error -> logger.error(msg)
    }
    persistAsync(taskId, blockId, level, message)
  }

  private fun persistAsync(
    taskId: String, blockId: String?, level: FalconLogLevel, message: String
  ) = logExecutor.submit {
    val ev: EntityValue = mutableMapOf(
      "taskId" to taskId,
      "blockId" to blockId,
      "level" to level.name,
      "message" to message
    )
    EntityRwService.createOne("FalconLog", ev)
  }

}

enum class FalconLogLevel {
  Debug, Info, Error
}