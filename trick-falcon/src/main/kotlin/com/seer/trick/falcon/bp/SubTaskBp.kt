package com.seer.trick.falcon.bp

import com.seer.trick.BzError
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.TaskDef
import com.seer.trick.falcon.domain.TaskInputParamDef
import com.seer.trick.falcon.task.FalconTaskDefService
import com.seer.trick.falcon.task.FalconTaskService

/**
 * 子任务有自己的 TaskRecord
 * 任务量应该记在子任务里而不是顶层任务里，否则就破坏了封装。
 */
class SubTaskBp : AbstractBp() {

  override fun process() {
    val taskDef = FalconTaskDefService.mustFetchLatestTaskDefById(blockConfig.refTaskDefId)

    val inputParams = getInputParams(taskDef)

    val taskId = internalVariables["subTaskId"] as String?
    val subTaskRuntime = if (taskId.isNullOrBlank()) {
      val subTr = FalconTaskService.createTask(taskDef, inputParams, taskRuntime.getTopTask())
      logger.info("创建子任务: ${subTr.def.label} ${subTr.taskId}，顶级任务=${taskRuntime.getTopTask().taskId}")
      setBlockInternalVariables(mapOf("subTaskId" to subTr.taskId))
      subTr
    } else {
      // 到这里说明之前宕机过。但前面恢复任务时包括子任务也会被加载出来，因此可以直接查找
      FalconTaskService.unfinishedTasks[taskId] ?: throw BzError("errCodeErr", "SubTask not in unfinished: $taskId")
    }
    // 获取最新的子任务定义
    subTaskRuntime.def = FalconTaskDefService.mustFetchLatestTaskDefById(subTaskRuntime.def.id)
    FalconTaskService.runTask(subTaskRuntime, false)

    setBlockOutputParams(subTaskRuntime.outputParams)
  }

  private fun getInputParams(taskDef: TaskDef): Map<String, Any> {
    val taskInputParams: MutableMap<String, Any> = HashMap()
    for (param in taskDef.inputParams) {
      val paramDef = taskInputParamDefToBlockInputParamDef(param)
      val paramConfig = blockConfig.inputParams[param.name]
      var paramValue: Any? = null
      if (paramConfig != null) {
        paramValue = getBlockGeneralInputParam(param.name, paramConfig, paramDef, null)
      }
      val ipValue = paramValue ?: param.defaultValue
      if (ipValue == null && param.required)
        throw BzError("errFalconMissingBlockInputParam", blockConfig.name, param.name)
      if (ipValue != null) taskInputParams[param.name] = ipValue
    }
    return taskInputParams
  }

  private fun taskInputParamDefToBlockInputParamDef(taskInputParamDef: TaskInputParamDef): BlockInputParamDef {
    return BlockInputParamDef(
      taskInputParamDef.name,
      taskInputParamDef.type,
      taskInputParamDef.required,
      defaultValue = taskInputParamDef.defaultValue
    )
  }

  companion object {

    val def = BlockDef(
      SubTaskBp::class.simpleName!!,
    )

  }
}