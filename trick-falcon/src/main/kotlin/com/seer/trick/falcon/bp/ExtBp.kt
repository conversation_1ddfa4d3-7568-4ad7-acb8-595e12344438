package com.seer.trick.falcon.bp

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError
import com.seer.trick.FatalError
import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.script.ScriptCenter
import com.seer.trick.base.script.ScriptExeRequest
import com.seer.trick.base.script.ScriptTraceContext
import com.seer.trick.helper.JsonHelper

class ExtBp : AbstractBp() {
  
  override fun process() {
    val funcName = def.extFunction
    if (funcName.isNullOrBlank()) throw BzError("errNoScriptFunctionProvided")
    
    val allInputParams = collectAllBlockInputParams()
    
    val source = def.source
    val r = if (!source.isNullOrBlank()) {
      val ctx = ScriptCenter.createContext(def.sourceLang ?: "python", source)
      ScriptCenter.execute(
        ctx,
        ScriptExeRequest(
          funcName,
          arrayOf(ScriptTraceContext(), allInputParams, taskRuntime.inputParams),
          def.sourceLang
        ),
      )
    } else {
      ScriptCenter.execute(
        ScriptExeRequest(
          funcName,
          arrayOf(ScriptTraceContext(), allInputParams, taskRuntime.inputParams),
          def.sourceLang
        ),
      )
    }
    
    // logger.debug("Ext result, $r")
    if (!r.isNullOrBlank()) {
      val ro: ExtScriptResult = JsonHelper.mapper.readValue(r, jacksonTypeRef())
      if (ro.fatal) {
        throw FatalError(ro.errorMsg ?: "")
      } else if (ro.error) {
        throw BzError("errScriptExt", ro.errorMsg)
      }
      if (!ro.outputParams.isNullOrEmpty()) {
        setBlockOutputParams(ro.outputParams)
      }
    }
  }
}

class ExtScriptResult(
  val fatal: Boolean = false, // 致命错误，任务结束
  val error: Boolean = false, // 引发故障
  val errorMsg: String? = null, // 错误原因
  val outputParams: EntityValue? = null,
)