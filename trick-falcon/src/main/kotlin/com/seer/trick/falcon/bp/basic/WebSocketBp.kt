package com.seer.trick.falcon.bp.basic


import com.seer.trick.base.http.WebSocketManager
import com.seer.trick.base.http.WsMsg
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*
import com.seer.trick.helper.JsonHelper

class WebSocketBp : AbstractBp() {

  override fun process() {
    val eventName = mustGetBlockInputParam("eventName") as String
    val message = getBlockInputParam("message")

    WebSocketManager.sendAllAsync(
      WsMsg(
        eventName,
        JsonHelper.mapper.writeValueAsString(message)
      )
    )
  }

  companion object {
    val def = BlockDef(
      WebSocketBp::class.simpleName!!,
      color = "#BEA7E1",
      inputParams = listOf(
        BlockInputParamDef("eventName", BlockParamType.String, true),
        BlockInputParamDef("message", BlockParamType.Any, false, defaultValue = "")
      )
    )
  }
}