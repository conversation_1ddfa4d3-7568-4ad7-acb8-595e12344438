package com.seer.trick.falcon.bp.entity

import com.seer.trick.Cq

import com.seer.trick.base.entity.EntityValue
import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.base.reslock.ResLockService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.falcon.domain.ParamObjectType
import kotlin.concurrent.withLock

class UpdateOneEntityByIdBp : AbstractBp() {

  override fun process() {
    val entityName = mustGetBlockInputParam("entityName") as String
    val id = mustGetBlockInputParam("id") as String
    val fieldName = mustGetBlockInputParam("fieldName") as String
    val fv = getBlockInputParam("fv")
    val setToNull = getBlockInputParamAsBool("setToNull")

    val update: EntityValue = mutableMapOf(fieldName to if (setToNull) null else fv)
    // 防止并发修改库位库存、库存明细等资源
    ResLockService.resLock.withLock {
      EntityRwService.updateOne(entityName, Cq.idEq(id), update)
    }
    // 添加猎鹰任务相关业务对象
    addRelatedObject(entityName, id, null)
  }

  companion object {

    val def = BlockDef(
      UpdateOneEntityByIdBp::class.simpleName!!,
      color = "#18b0a1",
      inputParams = listOf(
        BlockInputParamDef(
          "entityName",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.EntityName),
        ),
        BlockInputParamDef(
          "id",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.EntityId),
        ),
        BlockInputParamDef(
          "fieldName",
          BlockParamType.String,
          true,
          objectTypes = listOf(ParamObjectType.EntityFieldName),
        ),
        BlockInputParamDef("fv", BlockParamType.Any, false),
        BlockInputParamDef(
          "setToNull",
          BlockParamType.Boolean,
          false,
        ),
      ),
    )
  }
}