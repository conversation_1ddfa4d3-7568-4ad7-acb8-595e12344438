package com.seer.trick.falcon.bp.basic

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.seer.trick.BzError

import com.seer.trick.base.script.ScriptCenter
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockOutputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.helper.JsonHelper

class SimpleScriptBp : AbstractBp() {

  override fun process() {
    val scriptStr = mustGetBlockInputParam("scriptStr") as String

    ScriptCenter.createMainContext().use { ctx ->
      ctx.eval(ScriptCenter.mainScriptType, scriptStr)
      val func = ctx.getBindings(ScriptCenter.mainScriptType).getMember("simpleScript")
      val proxy = BlockScriptProxy(this)
      val rv = func.execute(proxy)
      val rvStr = rv.asString()
      val rvO: SimpleScriptResult = JsonHelper.mapper.readValue(rvStr, jacksonTypeRef())
      if (rvO.error) throw BzError("errSimpleScriptBp", rvO.errorMsg)

      setBlockOutputParams(mapOf("scriptOut" to rvO.result))
    }
  }

  companion object {

    val def = BlockDef(
      SimpleScriptBp::class.simpleName!!,
      color = "#ffd22b",
      inputParams = listOf(BlockInputParamDef("scriptStr", BlockParamType.String, true)),
      outputParams = listOf(BlockOutputParamDef("scriptOut", BlockParamType.Any)),
    )
  }
}

class BlockScriptProxy(private val bp: AbstractBp) {

  fun getTopTaskId(): String = bp.taskRuntime.getTopTask().taskId

  fun getTaskInputParam(name: String): Any? = bp.taskRuntime.inputParams[name]

  fun getBlockOutputParam(blockName: String, paramName: String): Any? {
    val blockId = blockName.substring(1).toInt()
    return bp.blockContext.blocksByConfigId[blockId]?.outputParams?.get(paramName)
  }

  fun getContextVariable(name: String): Any? = bp.blockContext.ctxVariables[name]
}

data class SimpleScriptResult(val error: Boolean = false, val errorMsg: String? = null, val result: Any? = null)