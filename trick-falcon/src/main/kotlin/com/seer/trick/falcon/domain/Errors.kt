package com.seer.trick.falcon.domain

import com.seer.trick.BzError

/**
 * 用户取消
 */
class TaskCancelledError : RuntimeException("TaskCancelledError")

/**
 * 程序不可恢复性错误
 */
open class FatalError(message: String) : RuntimeException("FatalError: $message")

/**
 * 块终止猎鹰任务
 */
class AbortTaskError(message: String) : FatalError(message)

class MissingBlockInputParamError(bpConfigName: String, name: String) :
  BzError("errFalconMissingBlockInputParam", bpConfigName, name)

class BlockError(val parent: Throwable) : RuntimeException(parent.message, parent)

class BreakLoop : BzError("errBreakLoop")