package com.seer.trick.falcon.bp.basic

import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.AbortTaskError
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType

class AbortTaskBp : AbstractBp() {
  
  override fun process() {
    val msg = getBlockInputParam("msg") as String? ?: ""
    throw AbortTaskError(msg)
  }
  
  companion object {
    
    val def = BlockDef(
      AbortTaskBp::class.simpleName!!,
      color = "#DC143C",
      inputParams = listOf(BlockInputParamDef("msg", BlockParamType.String, true)),
    )
    
  }
}