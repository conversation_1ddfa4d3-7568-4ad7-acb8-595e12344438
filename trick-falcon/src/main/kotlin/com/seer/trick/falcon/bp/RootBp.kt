package com.seer.trick.falcon.bp

import com.seer.trick.falcon.domain.BlockChildDef
import com.seer.trick.falcon.domain.BlockDef

class RootBp : AbstractBp() {

  override fun process() {
    val rootChildren = defaultChildrenConfig
    serialRunChildren(rootChildren, CHILD_DEFAULT, blockContext)
  }

  companion object {
    val def = BlockDef(
      RootBp::class.simpleName!!,
      children = listOf(BlockChildDef(CHILD_DEFAULT, 1, -1))
    )
  }

}

