package com.seer.trick.falcon.bp.entity


import com.seer.trick.base.entity.service.EntityRwService
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*

class FindOneEntityByIdBp : AbstractBp() {
  
  override fun process() {
    
    val entityName = mustGetBlockInputParam("entityName") as String
    val id = mustGetBlockInputParam("id") as String
    
    val ev = EntityRwService.findOneById(entityName, id)
    val found = ev != null
    
    setBlockOutputParams(mapOf("ev" to ev, "found" to found))
  }
  
  companion object {
    
    val def = BlockDef(
      FindOneEntityByIdBp::class.simpleName!!,
      color = "#A8D297",
      inputParams = listOf(
        BlockInputParamDef(
          "entityName", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.EntityName)
        ),
        BlockInputParamDef(
          "id", BlockParamType.String, true, objectTypes = listOf(ParamObjectType.EntityId)
        )
      ),
      outputParams = listOf(
        BlockOutputParamDef("found", BlockParamType.Boolean),
        BlockOutputParamDef("ev", BlockParamType.JSONObject),
      )
    )
  }
  
}