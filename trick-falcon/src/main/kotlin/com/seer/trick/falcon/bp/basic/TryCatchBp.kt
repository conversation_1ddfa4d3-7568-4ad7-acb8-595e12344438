package com.seer.trick.falcon.bp.basic


import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.*
import java.util.concurrent.CancellationException

class TryCatchBp : AbstractBp() {

  override fun process() {
    val swallowError = getBlockInputParamAsBool("swallowError")
    val ignoreAbort = getBlockInputParamAsBool("ignoreAbort")

    // 注意，块上下文不变
    try {
      val children = blockConfig.children["try"] ?: return
      serialRunChildren(children, "try", blockContext)
    } catch (e: Throwable) {
      logger.error("TryCatchBp 捕获到异常", e)

      val children = blockConfig.children["catch"] ?: return

      if ((e is CancellationException || e is TaskCancelledError || e is FatalError) && ignoreAbort) {
        // 不处理
        throw e
      }

      try {
        serialRunChildren(children, "catch", blockContext)
      } catch (e: Exception) {
        logger.error("TryCatchBp 的 catch 部分捕获到异常", e)
      }

      if (!swallowError) throw e
    }
  }

  companion object {

    val def = BlockDef(
      TryCatchBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(
        BlockInputParamDef("swallowError", BlockParamType.Boolean, false),
        BlockInputParamDef("ignoreAbort", BlockParamType.Boolean, false)
      ),
      children = listOf(BlockChildDef("try"), BlockChildDef("catch"))
    )

  }

}