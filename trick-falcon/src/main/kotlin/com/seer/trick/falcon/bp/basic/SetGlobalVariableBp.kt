package com.seer.trick.falcon.bp.basic

import com.seer.trick.BzError

import com.seer.trick.falcon.FalconLogLevel
import com.seer.trick.falcon.LogManager
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.falcon.task.FalconGlobalControlService

class SetGlobalVariableBp : AbstractBp() {

  override fun process() {
    val varName = getBlockInputParam(ipVariableName.name) as String

    val globalVariable = FalconGlobalControlService.getConfig(varName)
      ?: throw BzError("errNoFalconGlobalVariable", varName)
    val varValue = getBlockInputParam(
      ipVariableValue.name,
      FalconGlobalControlService.fieldTypeToFalconBlockParamType(globalVariable.type),
    )

    LogManager.logTask(logger, taskRuntime.taskId, FalconLogLevel.Info, "Set global variable $varName=")
    FalconGlobalControlService.setValue(varName, varValue)
  }

  companion object {
    private val ipVariableName = BlockInputParamDef("varName", BlockParamType.String, true)
    private val ipVariableValue = BlockInputParamDef("varValue", BlockParamType.Any, true)

    val def = BlockDef(
      SetGlobalVariableBp::class.simpleName!!,
      color = "#FFA6FF",
      inputParams = listOf(ipVariableName, ipVariableValue),
    )
  }
}