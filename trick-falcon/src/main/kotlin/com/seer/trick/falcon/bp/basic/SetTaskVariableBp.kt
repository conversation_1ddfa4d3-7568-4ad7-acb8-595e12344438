package com.seer.trick.falcon.bp.basic


import com.seer.trick.falcon.FalconLogLevel
import com.seer.trick.falcon.LogManager
import com.seer.trick.falcon.bp.AbstractBp
import com.seer.trick.falcon.domain.BlockDef
import com.seer.trick.falcon.domain.BlockInputParamDef
import com.seer.trick.falcon.domain.BlockParamType
import com.seer.trick.falcon.task.FalconTaskService

class SetTaskVariableBp : AbstractBp() {

  override fun process() {
    val name = getBlockInputParam(ipVariableName.name) as String
    val variable = taskRuntime.def.taskVariables.find { it.name == name }
    val value = if (variable != null) {
      getBlockInputParam(ipVariableValue.name, variable.type)
    } else {
      getBlockInputParam(ipVariableValue.name)
    }
    LogManager.logTask(logger, taskRuntime.taskId, FalconLogLevel.Info, "Set task variable $name=$value")
    FalconTaskService.setTaskVariable(taskRuntime, name, value)
  }

  companion object {

    private val ipVariableName = BlockInputParamDef("varName", BlockParamType.String, true)
    private val ipVariableValue =
      BlockInputParamDef("varValue", BlockParamType.Any, false) // TODO 如何区别有无配置和是否是 null

    val def = BlockDef(
      SetTaskVariableBp::class.simpleName!!,
      color = "#C7DCA7",
      inputParams = listOf(ipVariableName, ipVariableValue),
    )
  }
}