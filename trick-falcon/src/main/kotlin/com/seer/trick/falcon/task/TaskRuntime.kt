package com.seer.trick.falcon.task

import com.seer.trick.FatalError
import com.seer.trick.falcon.domain.TaskCancelledError
import com.seer.trick.falcon.domain.TaskDef
import java.util.*
import java.util.concurrent.Future

/**
 * 这里面主要放数据，不要放业务
 */
class TaskRuntime(
  val taskId: String,
  val rootBlockStateId: String,
  @Volatile
  var def: TaskDef,
  private val topTask: TaskRuntime? = null,
) {

  // 例如，任务终止时，代码需要读取这个状态
  @Volatile
  var status: Int = TaskStatus.Created

  @Volatile
  var paused = false

  val inputParams: MutableMap<String, Any?> = Collections.synchronizedMap(HashMap())
  val variables: MutableMap<String, Any?> = Collections.synchronizedMap(HashMap()) // 任务变量
  val outputParams: MutableMap<String, Any?> = Collections.synchronizedMap(HashMap())

  @Volatile
  var future: Future<*>? = null

  fun getTopTask() = topTask ?: this

  fun isTopTask() = topTask == null

  @Volatile
  var failureNum = 0

  fun throwIfCancelledOrAborted() {
    if (getTopTask().status == TaskStatus.Cancelled) throw TaskCancelledError()
    if (getTopTask().status == TaskStatus.Aborted) throw FatalError("")
  }
}

object TaskStatus {
  const val Created = 100
  const val Started = 120
  const val Failed = 140 // 故障
  const val Done = 160
  const val Cancelled = 180 // 用户取消
  const val Aborted = 190 // 任务彻底失败
}