# 业务对象与数据库表

## MongoDB

为简单，下文中，表即集合。

1、Component 存储在单独的表（称为组件表）中。不管是不是多值的。

表名是组件引用的业务对象的名字。如果有多个业务对象（如单据）使用相同组件（如单行），则它们存储于相同的表中。

在组件表中通过 `btParentId` 和 `btLineNo` 关联到父业务对象。

2、File 和 Image，以嵌入文档（单值）或数组和嵌入文档（多值）存储在主表中。

3、其他字段，如果是多值的，以数组存储在主表中；如果是单值的，直接存储在主表中。

4、Object 类型，序列化为 JSON 字符串存储。

## SQL 数据库

1、对于 File 和 Image

如果是单值的，展平为多列存储于主表。

- 路径列：取字段名
- 文件大小列：取字段名加 `Size`
- 文件名列：取字段名加 `Name`
- 文件名列：取字段名加 `MD5`

如果是多值的，存储于关联表。

其他列：`_filePath` `_fileSize` `_fileName` `_fileMD5`

2、对于 Reference

2.1 如果是单值的，直接存储于主表。

2.2 如果是多值的，存储于关联表。

其他列：`__refId`

3、对于 Component

存储于关联表。

4、对于其他字段。

如果是单值，直接存储于主表，否则存储于关联表。

5、关联表。

表名是主表名加字段名（大写字段名首字母）。

通过 `btParentId` 和 `btLineNo` 关联到父表。