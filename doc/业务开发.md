[toc]

# 概述

## 约定

- 单据不一定有单行。
- 一个装货单只对应一个容器。

## 子容器

系统中用数字标识子容器，从 1 开始。

系统中子容器的字段一般是 `subContainerId`。

# 最佳实践 & 注意事项

- 一般在最后一步（如上架）再创建库存，前面（收货、装箱）尽量不要创建库存，否则还得转换库存状态。
- 暂时，出库消耗库存，对库存明细的修改还是采用：减少数量，如果数量为 0，删除这条明细。
- 入库单与装货单、出库单与分拣单的单行，物料特征一般对应。例如，如果入库单行上有物料编号、批次、质量等级，装货单单行上也要有这三个字段。
- 由于实现方式，单行的 ID 是不稳定的。比如，修改了单据，已有单行的 ID 可能会变。因为默认采用了，单行全删再重新插入的方式。因此在记录关联单行时，一般不要记 ID，而要记单号+行号。

# 通用

## 补充物料信息

几乎所有的单行，都有物料单号之外的物料信息，如物料名称、规格、图片等。
用户、程序一般只填编号，其他字段可以通过调用 `MaterialManager.fillMaterialFieldsIntoLine` 补充。

# 库存

## 库存说明

库存实体是 `FbInvLayout`。核心是：

- qty: 数量
- btMaterial：物料
- 物料属性
- 位置类。除了标准的库位、库区、仓库。可以为了业务方便，添加排列层、深等位置信息。
- 容器类。subContainerId 是子容器号。至少填 leafContainer，即最内层容器。
- state 状态
- 锁
- 关联单据
- 附加字段，如批次，即之前的特征

## 创建库存

创建库存分为三步：

1. 从单据（行）转换为库存明细。
2. 填充、矫正。
3. 写数据库。

第 2、3 步，由 `CreateInvService` 实现。里面有两个 `fixCreateInvLayout` 实现。
可以用 `CreateInvLine` 帮你把值填对、告诉你哪些必填。或者直接生成 `FbInvLayout` 实体值。

主要是第一步。目前暂时不知道怎么标准化这一步，差异在于：

- 现在有两种单据，带单行和不带单行的。对于后者，物料、数量直接在单头上。
- 容器、库位等有的业务在单行上，有的在单头上，有的从配置中取，有的是写死的。
- 关联单据的取法。
- 附加字段从哪里取值。

因此，目前从单据转库存明细，还是要每个“入库类”单据自己实现。

# 出库和分拣

## 多种出库和分拣

MWMS 时代，一个出库单，配模版。现在是提供标准出库单 `FbOutboundOrder` 。 
如果有更多出库类型，如成品出库、原料出库，复制标准出库单，分别创建业务对象、菜单。

## 同步、异步出库

出库有两种触发方式：人工（同步）、自动（异步）。

- 人工就是通过人工提交入库单的方式，同步触发分配库存、生成分拣单搬运单等。
- 自动，即人工提交，或同步其他系统自动提交。系统在后台处理提交的出库单。

分拣亦然。

## 实物分拣不足

比如当系统分配从容器 A 里拣出 10 个，但容器 A 里只有 8 个。实际分拣数量填 8。当实际分拣数量不等于系统预计时，

- 系统仍按 10 个扣库存。其中 2 个做盘亏处理。
- 将此单行已找到库存数量减 2，重新分配 2 个库存。

## 出库、分拣相关业务对象和关键属性

出库单默认业务对象 `FbOutboundOrder`。关键属性：

- btOrderState：状态。Committed 状态时触发找库存、生产分拣单。
- invAssignedAll：为 true 表示此出库单的库存都以找到。开始为 false。变为 true 后可以再变成 false —— 当实际分拣时发现不够，可以补充出库。

分拣单行默认业务对象：`FbOutboundOrderLine`。关键属性：

- btMaterial：物料
- qty：出库数量
- invAssignedQty：已找到库存数量

分拣单默认业务对象 `PickOrder`。关键属性：

- container：容器
- btOrderState：状态。Todo 待分拣。Done 表示分拣完成。Done 触发后处理。
- submittedPostProcessed：true 表示后处理完成。
- containerOutOrderId：容器出库运单号，隐藏字段，用于判定是否已分配运单。
- containerBackOrderId：容器回库运单号，隐藏字段，用于判定是否已分配运单。
- containerOutDone：容器出库搬运完成，隐藏字段。
- allUsed：表示此分拣单关联的容器中所有物料都要被出库

分拣单行默认业务对象 `PickOrderLine`。关键属性：

- btMaterial：物料
- subContainerId：格子，如果启用了格子。
- planQty：系统规划的分拣数量
- qty：实际分拣数量

## 容器的搬出和搬回

目前系统标准功能不处理容器的搬出和搬回。也暂时没有标准化的办法。有两种思路：

1. 监控分拣单的状态和字段，触发猎鹰任务。
2. 监控分拣单的状态和字段，创建“容器搬运单”，再用猎鹰任务处理“容器搬运单”。这种适合需要人工放行、人工在库存分配、分拣单创建后再决定最终目标位置（分拣库位）的情况。

# 库位、容器与库存

以库位为核心

- 修改库位占用状态
- 修改库位锁定状态
- 反复尝试锁定库位
- 反复尝试锁定容器
- 等待库位为空并锁定
- 将容器上架到库位
- 将容器从库位下架：允许容器不传
- 获取库位上的容器
- 获取容器所在库位
- 批量操作
- 选空库位
- 选非空库位
